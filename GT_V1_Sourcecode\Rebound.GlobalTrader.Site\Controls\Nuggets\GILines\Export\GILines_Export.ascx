<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="GILines_Export.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
 <style>
        .stockImage {
    float: left;
    margin: 0px 30px 20px 0px;
    padding: 2px;
    border: 1px solid #cccccc;
    position: relative;
    cursor: default;
    height: 261px;
    width: 200px;
    background-color: #c4e9c0;
}

.stockImageCaption {
    color: #090909;
    text-align: center;
    font-size: 10px;
    padding: 5px 2px;
    margin-top: 2px;
    border-top: 1px dotted #cccccc;
    font-size: 12px!important;
}
.stockImageCaption input{width:100%;}
    </style>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
   
    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Export" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>

    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "GILines_Export")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server">
            <ReboundUI_Form:FormField ID="ctlGoodsIn" runat="server" FieldID="lblGoodsIn" ResourceTitle="GoodsInNo">
                <Field>
                    <asp:Label ID="lblGoodsIn" runat="server" /></Field>
            </ReboundUI_Form:FormField>
               <ReboundUI_Form:FormField ID="ctlIsGeneralNotesAdd" runat="server" FieldID="chkIsGeneralNotesAdd" ResourceTitle="IsGeneralNotesAdd">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkIsGeneralNotesAdd" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>
             <ReboundUI_Form:FormField id="ctlExternalNotes" runat="server" FieldID="txtExternalNotes" ResourceTitle="ExternalNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtExternalNotes" runat="server" Width="200" Maxlength="100" TextMode="multiLine"   />
          
				</Field>
			</ReboundUI_Form:FormField>          	
          <%--  <ReboundUI_Form:FormField ID="ctlIsPDF" runat="server" FieldID="chkIsPDF" ResourceTitle="IsExportPDF">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkIsPDF" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>--%>
            <%--<ReboundUI_Form:FormField ID="ctlIsWord" runat="server" FieldID="chkIsWord" ResourceTitle="IsExportWord">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkIsWord" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>--%>
             <ReboundUI_Form:FormField ID="ctlSelectAllImage" runat="server" FieldID="chkSelectAllImage" ResourceTitle="SelectAllImage">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkSelectAllImage" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlImageList" runat="server" FieldID="pnlImages" ResourceTitle="StockImagesList">
                <Field>
                    <asp:Panel ID="pnlImages" runat="server" Visible="true"></asp:Panel>
                </Field>
            </ReboundUI_Form:FormField>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>
