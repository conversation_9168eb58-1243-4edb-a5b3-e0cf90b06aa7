﻿/*
 Marker     ChangedBy       ChangedDate     Remarks
 [001]      <PERSON><PERSON><PERSON>     25-Jan-2019     Sales Dashboard Changes/ Req Dashboard Headings
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class PartDetails {
		
		#region Constructors
		
		public PartDetails() { }
		
		#endregion
		
		#region Properties
	
		/// <summary>
		/// PartId (from Table)
		/// </summary>
		public System.Int32 PartId { get; set; }
		/// <summary>
		/// FullPart (from Table)
		/// </summary>
		public System.String FullPart { get; set; }
		/// <summary>
		/// ManufacturerNo (from usp_selectAll_Allocation)
		/// </summary>
		public System.Int32? ManufacturerNo { get; set; }
		/// <summary>
		/// PackageNo (from usp_selectAll_Allocation)
		/// </summary>
		public System.Int32? PackageNo { get; set; }
        /// PackageId
        /// </summary>
        public System.Int32? PackageId { get; set; }
		/// <summary>
		/// ProductNo (from usp_selectAll_Allocation)
		/// </summary>
		public System.Int32? ProductNo { get; set; }
		/// <summary>
		/// MinimumQuantity (from Table)
		/// </summary>
		public System.Int32? MinimumQuantity { get; set; }
		/// <summary>
		/// ReOrderQuantity (from Table)
		/// </summary>
		public System.Int32? ReOrderQuantity { get; set; }
		/// <summary>
		/// LeadTime (from Table)
		/// </summary>
		public System.Int32? LeadTime { get; set; }
		/// <summary>
		/// ClientNo (from Table)
		/// </summary>
		public System.Int32 ClientNo { get; set; }
		/// <summary>
		/// ResalePrice (from Table)
		/// </summary>
		public System.Double? ResalePrice { get; set; }
		/// <summary>
		/// ROHSCompliant (from Table)
		/// </summary>
		public System.Boolean ROHSCompliant { get; set; }
		/// <summary>
		/// MasterPart (from Table)
		/// </summary>
		public System.Boolean? MasterPart { get; set; }
		/// <summary>
		/// GoldenPart (from Table)
		/// </summary>
		public System.Boolean? GoldenPart { get; set; }
		/// <summary>
		/// UpdatedBy (from Table)
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP (from Table)
		/// </summary>
		public System.DateTime DLUP { get; set; }
		/// <summary>
		/// PartTitle (from Table)
		/// </summary>
		public System.String PartTitle { get; set; }
		/// <summary>
		/// PartName (from usp_list_Activity_by_Client_with_filter)
		/// </summary>
		public System.String PartName { get; set; }

        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }	
        public System.String PartNameWithManufacture { get; set; }
        public System.String Productname { get; set; }
        public System.String DateCode { get; set; }
        public System.String Packagename { get; set; }
        public System.Int32 ROHSNo { get; set; }
        public System.String ManufacturerName { get; set; }
        public System.String DateCodeOriginal { get; set; }
        public System.String ProductDescription { get; set; }
        public System.Boolean? ProductInactive { get; set; }
        public System.String IHSProductDescription { get; set; }
        public System.String ManufacturerFullName { get; set; }
        public System.String IHSProduct { get; set; }
        public System.String ECCNCode { get; set; }
        public System.Int32 ECCNNo { get; set; }
        //[001] start
        public int? StockId { get; set; }
        public string ResultType { get; set; }
        public System.Int32 PartEccnMappedId { get; set; }
        /// <summary>
        /// CountryOfOrigin
        /// </summary>
        public System.String CountryOfOrigin { get; set; }
        /// <summary>
        /// CountryOfOriginNo
        /// </summary>
        public System.Int32? CountryOfOriginNo { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double? AveragePrice { get; set; }
        /// <summary>
        /// ManufacturerNo
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }
        //[001] end
        public System.String ROHSName { get; set; }
        public System.String Descriptions { get; set; }
        public int? IHSPartsId { get; set; }
        public System.String PartStatus { get; set; }
        public System.String IHSCurrencyCode { get; set; }
        public System.String IHSDutyCode { get; set; }

        public System.Double? LastPricePaidByCust { get; set; }
        public System.String PaidByCustCurrencyCode { get; set; }
        public System.String LastSoldtoCustomer { get; set; }
        public System.Double? LastAverageReboundPriceSold { get; set; }
        public System.DateTime LastSoldOn { get; set; }


        public System.Int32? LastQuantity { get; set; }
        public System.String LastSupplierType { get; set; }
        public System.String LastDatecode { get; set; }
        public System.DateTime LastDatePurchased { get; set; }
        public System.String LastCustomerRegion { get; set; }


        public System.Double? CustLastPricePaidByCust { get; set; }
        public System.String CustPaidByCustCurrencyCode { get; set; }
        public System.Double? CustLastAvgReboundPriceSold { get; set; }
        public System.String CustLastSoldtoCustomer { get; set; }
        public System.DateTime CustLastSoldOn { get; set; }


        public System.Int32? CustQuantity { get; set; }
        public System.String CustSupplierType { get; set; }
        public System.String CustDatecode { get; set; }
        public System.DateTime CustDatePurchased { get; set; }
        public System.String CustomerRegion { get; set; }
        public System.Double? CleintBestPricePaid12 { get; set; }
        public System.Double? BestLastPricePaid12 { get; set; }
        public System.String PackageDescription { get; set; }
        public System.Boolean? ECCNStatus { get; set; }
        public System.String ECCNWarning { get; set; }
        public System.String ItemFound { get; set; }
        public int TotalRecords { get; set; }
        #endregion

    }
}