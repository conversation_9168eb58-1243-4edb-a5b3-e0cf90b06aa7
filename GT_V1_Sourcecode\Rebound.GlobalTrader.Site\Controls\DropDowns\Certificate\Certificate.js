Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.initializeBase(this,[n]);this._intCategoryID=0};Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intCategoryID=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Certificate");this._objData.set_DataObject("Certificate");this._objData.set_DataAction("GetData");this._objData.addParameter("CategoryID",this._intCategoryID)},dataCallOK:function(){var t=this._objData._result,n;if(t.Certificates)for(n=0;n<t.Certificates.length;n++)this.addOption(t.Certificates[n].Name,t.Certificates[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Certificate",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);