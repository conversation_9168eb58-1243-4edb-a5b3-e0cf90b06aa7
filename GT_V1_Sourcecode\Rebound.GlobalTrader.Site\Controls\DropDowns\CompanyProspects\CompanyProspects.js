Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyProspects=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyProspects.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyProspects.prototype={get_CompanyProspectsDDLType:function(){return this._CompanyProspectsDDLType},set_CompanyProspectsDDLType:function(n){this._CompanyProspectsDDLType!==n&&(this._CompanyProspectsDDLType=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyProspects.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._IsPartialCompanyProspects=null,Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyProspects.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/CompanyProspects");this._objData.set_DataObject("CompanyProspects");this._objData.set_DataAction("GetData");this._objData.addParameter("CompanyProspectsDDLType",this._CompanyProspectsDDLType)},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyProspects.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyProspects",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);