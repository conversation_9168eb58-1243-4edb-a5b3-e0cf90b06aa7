///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 17.05.2010:
// - [168]: Add new default setting for rating
//
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/08/2012   Customer No required if approved checked
//[002]      Suhail          02/05/2018   Added Credit Limit2
//[003]      Aashu Singh     14-Sep-2018  [REB-12820]:Provision to add Global Security on Contact Section
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.initializeBase(this, [element]);
    this._intCompanyID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intCompanyID = null;
        //[002] code start
        this._lblCurrency_ActualCreditLimit = null;
        //[002] code end
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        this._ctlSelectCompanies = $find(this.getField("ctlSelectCompany").ID);
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }
        var _that = this;
        this.loadLinkedCompanies();
        $('#btnSearchCompanybyVatNo').click(function () {
            _that.loadLinkedCompanies();
        });
        
    },
    loadLinkedCompanies: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyFinanceInfo");
        obj.set_DataObject("CompanyFinanceInfo");
        obj.set_DataAction("loadLinkedCompanies");
        obj.addParameter("id", this._intCompanyID);
        obj.addParameter("VatNo", $('#ctl00_cphMain_ctlFinanceInfo_ctlDB_ctl14_ctlLink_ctlDB_ctlVatNumber_ctl04_txtVatNumber').val());
        obj.addDataOK(Function.createDelegate(this, this.loadLinkedCompaniesComplete));
        obj.addError(Function.createDelegate(this, this.loadLinkedCompaniesError));
        obj.addTimeout(Function.createDelegate(this, this.loadLinkedCompaniesError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();        
    },
    loadLinkedCompaniesError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    loadLinkedCompaniesComplete: function (args) {
        console.log(args);
        this.showLoading(false);
        this._ctlSelectCompanies.clearData();
        var result = args._result;
        var row;

        if (result != null) {
            //selected
            if (result.Selected != null) {
                for (var i = 0; i < result.Selected.length; i++) {
                    row = result.Selected[i];
                    this._ctlSelectCompanies.addRow(true, [$R_FN.setCleanTextValue(row.Name)], row.ID);
                    $('#ctl00_cphMain_ctlFinanceInfo_ctlDB_ctl14_ctlLink_ctlDB_ctlVatNumber_ctl04_txtVatNumber').val(row.VatNo);
                    row = null;
                }
            }

            //unselected
            if (result.Unselected != null) {
                for (i = 0; i < result.Unselected.length; i++) {
                    row = result.Unselected[i];
                    this._ctlSelectCompanies.addRow(false, [$R_FN.setCleanTextValue(row.Name)], row.ID);
                    row = null;
                }
            }
            this._ctlSelectCompanies.populateTables();
            this.showInnerContent(true);
        }
    },

    saveClicked: function () {
        //if (!this.validateForm() || this._ctlSelectCompanies.getValuesAsString(true) == "") {
        if (!this.validateForm()) {
            //alert("please select Company From The List!");
            this.showError(true, "Please Enter Vat Number");
            return false;
        }
        else{
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/CompanyFinanceInfo");
            obj.set_DataObject("CompanyFinanceInfo");
            obj.set_DataAction("SaveLinked");
            obj.addParameter("id", this._intCompanyID);
            obj.addParameter("VatNo", $('#ctl00_cphMain_ctlFinanceInfo_ctlDB_ctl14_ctlLink_ctlDB_ctlVatNumber_ctl04_txtVatNumber').val());
            obj.addParameter("Selected", this._ctlSelectCompanies.getValuesAsString(true));
            obj.addParameter("Unselected", this._ctlSelectCompanies.getValuesAsString(false));
            obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
            obj.addError(Function.createDelegate(this, this.saveEditError));
            obj.addTimeout(Function.createDelegate(this, this.saveEditError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
            this.showFormWithoutError(true, "");
            return false;
            }    
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            //if (!this._blnApproved && this._chkApproved._blnChecked)
            //    this.getMessageText();
            this.showSavedOK(true);
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    },

    cancelClicked: function() {
        this.onCancel();
    },

    //setCurrencyLabel: function(strCurrency) {
    //    $R_FN.setInnerHTML(this._lblCurrency_CreditLimit, strCurrency);
    //    //[002] code start
    //    $R_FN.setInnerHTML(this._lblCurrency_ActualCreditLimit, strCurrency);
    //    //[002] code end
    //},

    //approveClicked: function() {
    //    //only set the default rating the first time
    //    if (this._blnFirstTimeApprovedClicked) {
    //        if (this._chkApproved._blnChecked) this.setFieldValue("ctlRating", this._intDefaultRating);
    //    }
    //    this._blnFirstTimeApprovedClicked = false;
    //}
    //,
    //[001] code start
    //validateCustomerNo: function() {
    //    if (this.getFieldValue("ctlIsApproved") == true && this.checkFieldEntered("ctlCustomerNo") == false) {
    //        this.showError(true, $R_RES.CustomerNoMessage);
    //        return false;
    //    }
    //    else
    //        return true;
    //},
    //InsuredCurrencyAmount: function () {
    //    if (this.getFieldValue("ctlInsuredAmount") > 0 && this.checkFieldEntered("ctlInsuredAmountCurrency") == false) {
    //        this.showError(true, $R_RES.InsuredCurrencyAmount);
    //        return false;
    //    }
    //    else
    //        //$("#ctl00_cphMain_ctlSalesInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctlInsuredAmountCurrency_ctl03_ddlInsuredAmountCurrency_ddl").val(0);
    //        return true;
    //},
    
    //getMessageText: function() {
    //    Rebound.GlobalTrader.Site.WebServices.GetMailMessage_CompanySO(this._intCompanyID, Function.createDelegate(this, this.getMessageTextComplete));
    //},
    //getMessageTextComplete: function(strMsg) {
    //    this.sendMail(strMsg);
    //},
    //sendMail: function(strMsg) {
    //    Rebound.GlobalTrader.Site.WebServices.NotifyMessage("", this._intMailGroupNo, $R_RES.CompanyApproveSubject, strMsg, this._intCompanyID, Function.createDelegate(this, this.sendMailComplete));
    //},
    //sendMailComplete: function() {
    //    // this.finishedForm();
    //}
    //[001] code end

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyFinanceInfo_Link", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
