﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EXCELDocumentsDragDrop.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.EXCELDocumentsDragDrop" %>

<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<%--<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />--%>
		<button id="UpdateBomCsv" style="display:none;" >BOM Import</button>
		<div class="documentsize" ><%=Functions.GetGlobalResource("FormFields", "MaxDocumentSize")%><span id="DocumentFileSizeUploadExcel"></span></div>
		<div id="singleupload3">Upload</div>	
	</Links>
	
	<Content>
		<style>
			.documentsize{
				display: block;
				position: absolute;
				left: 205px;
				top: 16px;
				color: #000;
				font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
			}

			#UpdateBomCsv{
				/*background-color: #3535ff;*/
				background-color: #2f8ab9;
				box-shadow:0 2px 0 0 #13648d;
				color: white;
				border:none;
				border-radius: 3px;
				font-size: 11px;
				font-weight:bold;
				margin-top: -7px;
				padding: 5px;
				cursor: pointer;
				width: 100px;
			}
			#UpdateBomCsv:hover{
				background-color: #3396c9;
				box-shadow:0 2px 0 0 #15719f;
			}
		</style>
		<asp:Panel ID="pnlEXCELDocuments" runat="server"></asp:Panel>
	
		<asp:HiddenField ID="hidSection" runat="server" />
		
		
		<link href="css/uploadfile.css" rel="stylesheet">
<%--<script src="js/jquery.min.js"></script>--%>
<script src="js/jquery.uploadfile.js"></script>
		<script type="text/javascript">
			$(document).ready(function () {
                var PDFFileSize;
                var SectionMod;
                $.ajax({
                    processData: false,
                    contentType: 'application/json',
                    type: 'POST',
                    async: false,
                    url: "DocImage.ashx?type=DOCUMENTFILESIZE&DocumentType=2",
                    success: function (data) {
                        var obj = JSON.parse(data);
                        PDFFileSize = obj.PDFFileSize;
                        PDFFileSizeMB = obj.PDFFileSizeMB;
                        $("#DocumentFileSizeUploadExcel").text('(' + PDFFileSizeMB + ' MB)');
                        PDFDragDrop(PDFFileSize);
                    },
                    error: function () {
                        alert("The system has encountered a network connectivity issue. Please check your connectivity and try again.");

                    },
                });

				function PDFDragDrop(PDFFileSize) {
					var dragdropObj = $("#singleupload3").uploadFile({
						url: "DocImage.ashx?mxs=1&type=EXCELUPLOAD&docId=0&IsDragDrop=true",
						allowedTypes: "xlsx,xls,csv",
						fileName: "myfile",
						autoSubmit: false,
						multiple: false,
                        maxFileSize: PDFFileSize,
						showStatusAfterSuccess: false,
						showCancel: true,
						showDone: true,
						uploadDiv: "excelipload",
						dynamicFormData: function () {
							var data = { section: $find("<%=this.ClientID%>")._strSectionName }
							return data;
						},

						onSuccess: function (files, data, xhr) {

							var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
							if (json.Result) {
								$find("<%=this.ClientID%>")._frmAdd._strDBFileName = json.FileName;
								$find("<%=this.ClientID%>")._frmAdd.saveEdit();
							}
							else {
								$find("<%=this.ClientID%>")._frmAdd.showError(true, "Failed to upload. Please try again.");
							}
						},
						onSelect: function (fup) {

							if ($find("<%=this.ClientID%>")._intCountPDF < $find("<%=this.ClientID%>")._intMaxPDFDocuments) {
                                if (fup != null && ((fup[0].name.split('.').pop().toLowerCase() == "xlsx") || (fup[0].name.split('.').pop().toLowerCase() == "xls") || (fup[0].name.split('.').pop().toLowerCase() == "csv")) && fup[0].size < PDFFileSize)
									$find("<%=this.ClientID%>").showAddFormFromDrag(fup[0].name, dragdropObj);
								return true;
							}
							else {
								alert("Can't upload more than " + $find("<%=this.ClientID%>")._intMaxPDFDocuments + " xlsx,xls,csv.");
								return false;
							}

						}
					});
				}
               
		    });

		    function getSectionName() {
		        var strOut = "";
		        if ($find("<%=this.ClientID%>"))
		            strOut = $find("<%=this.ClientID%>")._strSectionName;
		        return strOut;
		    }

		    $(document).ready(function() {
		        setTimeout("PermissionAdd();", 500);
		    });

		    function PermissionAdd() {
		        if (!$find("<%=this.ClientID%>")._blnCanAdd) {
		            // $(".ajax-upload-dragdrop").hide();
		            
		            $("#excelipload").hide();
		        }
		    }
        </script>
		
		<div class="clearing"></div>
		
	
	</Content>
	
	<Forms>
		<ReboundForm:EXCELDocuments_AddDragDrop id="ctlAdd" runat="server" />
		<ReboundForm:EXCELDocuments_DeleteDragDrop id="ctlDelete" runat="server" />		
	</Forms>
</ReboundUI_Nugget:DesignBase>
