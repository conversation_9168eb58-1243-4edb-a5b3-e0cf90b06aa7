///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 15.02.2010:
// - add tooltip to search type button
//
// RP 06.01.2010:
// - add full disposing event
//  Marker     changed by      date               Remarks
//  [001]      Devendar      16 /06/2023      RP-423: search field needs to deal with language accents"

//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.prototype = {

	get_txt: function() { return this._txt; }, set_txt: function(v) { if (this._txt !== v)  this._txt = v; }, 
	get_hypSearchType: function() { return this._hypSearchType; }, set_hypSearchType: function(v) { if (this._hypSearchType !== v)  this._hypSearchType = v; }, 
	get_enmSearchType: function() { return this._enmSearchType; }, set_enmSearchType: function(v) { if (this._enmSearchType !== v)  this._enmSearchType = v; }, 
	get_strSearchType_StartsWith: function() { return this._strSearchType_StartsWith; }, set_strSearchType_StartsWith: function(v) { if (this._strSearchType_StartsWith !== v)  this._strSearchType_StartsWith = v; }, 
	get_strSearchType_EndsWith: function() { return this._strSearchType_EndsWith; }, set_strSearchType_EndsWith: function(v) { if (this._strSearchType_EndsWith !== v)  this._strSearchType_EndsWith = v; }, 
	get_strSearchType_Contains: function() { return this._strSearchType_Contains; }, set_strSearchType_Contains: function(v) { if (this._strSearchType_Contains !== v)  this._strSearchType_Contains = v; }, 

	addEnterPressed: function(fn) {
		$R_TXTBOX.addEnterPressedEvent(this._txt, fn);
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.callBaseMethod(this, "initialize");
		$addHandler(this._txt, "focus", Function.createDelegate(this, this.textBoxFocus));
		$addHandler(this._txt, "blur", Function.createDelegate(this, this.textBoxBlur));
		$addHandler(this._txt, "keyup", Function.createDelegate(this, this.textBoxKeyup));
		$addHandler(this._hypSearchType, "click", Function.createDelegate(this, this.changeSearchType));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._txt) $clearHandlers(this._txt);
		if (this._hypSearchType) $clearHandlers(this._hypSearchType);
		this._txt = null;
		this._hypSearchType = null;
		this._enmSearchType = null;
		this._strSearchType_StartsWith = null;
		this._strSearchType_EndsWith = null;
		this._strSearchType_Contains = null;
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.callBaseMethod(this, "dispose");
	},
	
	textBoxFocus: function() {
		this.enableField(true);
	},
	
	textBoxBlur: function() {
		this.enableField($R_FN.isEntered(this._txt.value));
	},
	
	textBoxKeyup: function() {
		this.enableField($R_FN.isEntered(this._txt.value));
	},
	
	getValue: function () {
		/*[001] Start*/
		/*var str = escape(this._txt.value.trim());*/
		var str = encodeURIComponent(this._txt.value.trim());
		switch (this._enmSearchType) {
			case $R_ENUM$TextFilterSearchType.StartsWith: str = str + "%"; break;
			case $R_ENUM$TextFilterSearchType.Contains: str = "%" + str + "%"; break;
			case $R_ENUM$TextFilterSearchType.EndsWith: str = "%" + str; break;
		}
		/*return escape(str);*/
		return encodeURIComponent(str);
		/*[001] END*/
	},
	
	setValue: function(v) {
		if (typeof(v) == "undefined" || v == null) v = "";
		v = v.toString().trim();
		this._txt.value = v;
		this.enableField(v.length > 0);
	},
		
	reset: function() {
		this._txt.value = "";
		this.enableField(false);
	},
	
	changeSearchType: function() {
		this.setSearchType(this._enmSearchType + 1);
	},
	
	setSearchType: function(i) {
		this._enmSearchType = i;
		if (this._enmSearchType > 2) this._enmSearchType = 0;
		this.showSearchType();
	},
	
	showSearchType: function() {
		switch (this._enmSearchType) {
			case $R_ENUM$TextFilterSearchType.StartsWith: 
				this._hypSearchType.className = "searchType_StartsWith"; 
				this._hypSearchType.setAttribute("title", this._strSearchType_StartsWith);
				this._hypSearchType.setAttribute("alt", this._strSearchType_StartsWith);
				break;
			case $R_ENUM$TextFilterSearchType.Contains: 
				this._hypSearchType.className = "searchType_Contains";
				this._hypSearchType.setAttribute("title", this._strSearchType_Contains);
				this._hypSearchType.setAttribute("alt", this._strSearchType_Contains);
				break;
			case $R_ENUM$TextFilterSearchType.EndsWith: 
				this._hypSearchType.className = "searchType_EndsWith";
				this._hypSearchType.setAttribute("title", this._strSearchType_EndsWith);
				this._hypSearchType.setAttribute("alt", this._strSearchType_EndsWith);
				break;
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox", Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base, Sys.IDisposable);
