<%@ Control Language="C#" CodeBehind="GITempLotNumber.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempLotNumber" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server"  InitialSortDirection="DESC">
	<FieldsLeft>
		<%--<ReboundUI_FilterDataItemRow:DropDown id="ctlGroup"  runat="server" ResourceTitle="SubGroup" DropDownType="Group"  DropDownAssembly="Rebound.GlobalTrader.Site" />--%>
        <ReboundUI_FilterDataItemRow:TextBox id="ctlLotNo" runat="server"  ResourceTitle="LotNo" />
       <%--  <ReboundUI_FilterDataItemRow:Numerical id="ctlLotNo" runat="server" ResourceTitle="LotNo" />--%>
	</FieldsLeft>
	<FieldsRight>
      
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
