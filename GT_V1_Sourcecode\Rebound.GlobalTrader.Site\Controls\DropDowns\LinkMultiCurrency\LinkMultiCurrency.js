Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.prototype={get_intCustomerClientNo:function(){return this._intCustomerClientNo},set_intCustomerClientNo:function(n){this._intCustomerClientNo!==n&&(this._intCustomerClientNo=n)},get_intBuyCurrencyNo:function(){return this._intBuyCurrencyNo},set_intBuyCurrencyNo:function(n){this._intBuyCurrencyNo!==n&&(this._intBuyCurrencyNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGlobalCurrencyNo=null,this._intBuyCurrencyNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/LinkMultiCurrency");this._objData.set_DataObject("LinkMultiCurrency");this._objData.set_DataAction("GetData");this._objData.addParameter("CustClientNo",this._intCustomerClientNo);this._objData.addParameter("BuyCurrencyNo",this._intBuyCurrencyNo)},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Currencies)for(t=0;t<n.Currencies.length;t++)this.addOption(n.Currencies[t].Name,n.Currencies[t].ID,n.Currencies[t].LinkID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.LinkMultiCurrency",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);