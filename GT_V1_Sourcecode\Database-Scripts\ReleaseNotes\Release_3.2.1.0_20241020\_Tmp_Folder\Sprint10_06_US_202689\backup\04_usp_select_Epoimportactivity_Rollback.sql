﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_select_Epoimportactivity]                                  
 @DisplayLength int=0                                
,@DisplayStart int=0                                
,@SortCol int=0                              
,@SortDir nvarchar(10)                                  
,@Search nvarchar(255) = NULL             
,@ClientType int=0               
,@selectedclientid int=0             
,@CreateBy int=0               
                                
as                                  
begin                   
                             
    Declare @FirstRec int, @LastRec int                                  
    Set @FirstRec = @DisplayStart;                                  
    Set @LastRec = @DisplayStart + @DisplayLength;                                  
                                     
    With CTE_Stock as                                  
    (                                  
         Select ROW_NUMBER() over (order by                                  
                                          
         importdate  desc                             
   )                                  
         as RowNum,                                  
         COUNT(*) over() as TotalCount,                                  
                              
  ImportDate ,ImportName  ,RowsAffected ,Target ,(case when ClientType=1 then 'HUB' when ClientType=2 then 'UK' when ClientType=3 then 'HK' else '' end) ClientTypeText ,ClientType                 
                                
        from BorisGlobalTraderimports.dbo.tbImportActivity_Epo               
      where isnull(ClientType,2)=@ClientType          
                   
    )                                  
    Select RowNum,TotalCount,                              
                       
 CONVERT(varchar,ImportDate,9) as Date                
 ,ImportName as Source ,RowsAffected as Rows ,Target  as Status ,ClientTypeText as Client                        
                               
    from CTE_Stock                                  
    where  RowNum > @FirstRec and RowNum <= @LastRec                                 
    ORDER BY                 
                 
ImportDate desc                  
    
end 


