﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class ContactGroupProvider : DataAccess
    {
        private static ContactGroupProvider _instance;
        public static ContactGroupProvider Instance
        {
            get
            {
                if (_instance == null)
                {
                    var providerType = Type.GetType(Globals.Settings.ContactGroup.ProviderType);
                    _instance = (ContactGroupProvider)Activator.CreateInstance(providerType);
                }
                return _instance;
            }
        }

        protected ContactGroupProvider()
        {
            ConnectionString = Globals.Settings.ContactGroup.ConnectionString;
        }

        public abstract List<ContactGroup> GetDataByNameOrCode(string nameSearch, string codeSearch, string codeType);
        public abstract int SaveGroupData(string groupName, string groupCode, string groupType, int updatedBy);
        public abstract int EditGroupData(int groupId, string groupName, string groupCode, string groupType, int updatedBy);
        public abstract List<ContactGroup> GetAllGroupCodeForCompany(string groupType, bool isDropDown);
        public abstract int DeleteCustomerGroupCodeByID(int groupId);
        public abstract int InactiveCustomerGroupCodeByID(int groupId, int inactive, int updatedBy);
        public abstract List<ContactGroup> AutoSearchGroupCode(string strSearch, string groupType, bool isDropDown);

        public abstract DataTable GetManufacturerGroupByMfr(System.Int32? MfrId);
        public abstract DataTable GetSupplierTypeByMfrGroup(System.Int32? ContactGroupId, System.Int32? ClientNo);

        public abstract List<ContactGroup> GetManufacturerGroupBySupplier(System.Int32? supplierId, System.Int32? clientNo, System.Boolean? inactive);
    }
}