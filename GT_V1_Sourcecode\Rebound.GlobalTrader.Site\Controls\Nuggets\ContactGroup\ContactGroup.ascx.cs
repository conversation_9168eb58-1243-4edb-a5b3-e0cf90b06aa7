﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class ContactGroup : Base
    {
        #region Locals

        //protected IconButton _ibtnEdit;
        //protected IconButton _ibtnAdd;
        //protected IconButton _ibtnMap;
        //protected FlexiDataTable _tbl;
        //protected HyperLink _hypPrev;
        //protected HyperLink _hypNext;
        //protected Label _lblItemNumber;
        //protected Panel _pnlItemDetail;
        //protected Panel _pnlLoadingItemDetail;
        //protected Panel _pnlItemDetailError;
        //protected IconButton _ibtnSearch;
        //protected IconButton _ibtnReset;
        #endregion

        #region Properties

        private bool _blnCanEdit = true;
        public bool CanEdit
        {
            get { return _blnCanEdit; }
            set { _blnCanEdit = value; }
        }

        private bool _blnCanAdd = true;
        public bool CanAdd
        {
            get { return _blnCanAdd; }
            set { _blnCanAdd = value; }
        }

        private string _strContactType = "";
        public string ContactType
        {
            get { return _strContactType; }
            set { _strContactType = value; }
        }

        #endregion


        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            //WireUpControls();
            AddScriptReference("Controls.Nuggets.ContactGroup.ContactGroup.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "ContactGroup");
            //SetupTables();
        }
        protected override void OnLoad(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnLoad(e);
        }
        protected override void OnPreRender(EventArgs e)
        {
            //_ibtnAdd.Visible = _blnCanAdd;
            //_ibtnEdit.Visible = _blnCanEdit;
            //_ibtnMap.Visible = _blnCanAdd;
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactGroup", ctlDesignBase.ClientID);
            //if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
            //if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            //if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnMap", _ibtnMap.ClientID);
            //_scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("ibtnSearch", _ibtnSearch.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("ibtnReset", _ibtnReset.ClientID);
            _scScriptControlDescriptor.AddProperty("strContactType", _strContactType);
            //_scScriptControlDescriptor.AddProperty("strName", _strContactType);
            //_scScriptControlDescriptor.AddProperty("strCode", _strContactType);
        }

        private void SetupTables()
        {
            //_tbl.Columns.Add(new FlexiDataColumn("ContactGroup"));
        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
            //_ibtnAdd = FindIconButton("ibtnAdd");
            //_ibtnEdit = FindIconButton("ibtnEdit");
            //_ibtnMap = FindIconButton("ibtnMap");
            //_ibtnSearch = FindIconButton("ibtnSearch");
            //_ibtnReset = FindIconButton("ibtnReset");
            //_tbl = (FlexiDataTable)FindContentControl("tbl");

        }
    }
}