﻿//Marker     Changed by         Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>     10/09/2021    Added class for purchase methods.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class PurchaseMethod : BizObject
    {
        #region Properties

        protected static DAL.RohsStatusElement Settings
        {
            get { return Globals.Settings.RohsStatuss; }
        }

        /// <summary>
        /// ROHSStatusId
        /// </summary>
        public System.Int32 PurchaseMethodId { get; set; }
        /// <summary>
        /// Name
        /// </summary>
        public System.String Name { get; set; }

        #endregion

        #region Methods

        /// <summary>
        /// Delete
        /// Calls [usp_delete_ROHSStatus]
        /// </summary>
        public static bool Delete(System.Int32? rohsStatusId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.RohsStatus.Delete(rohsStatusId);
        }
        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_ROHSStatus]
        /// </summary>
        public static List<PurchaseMethod> DropDown()
        {
            List<PurchaseMethodDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseMethod.DropDown();
            if (lstDetails == null)
            {
                return new List<PurchaseMethod>();
            }
            else
            {
                List<PurchaseMethod> lst = new List<PurchaseMethod>();
                foreach (PurchaseMethodDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PurchaseMethod obj = new Rebound.GlobalTrader.BLL.PurchaseMethod();
                    obj.PurchaseMethodId = objDetails.PurchaseMethodId;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
 
       
        

       
        #endregion
    }
}
