<%--
--%>
<%@ Control Language="C#" CodeBehind="EndUserUndertakingForm.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<style>
    .dropbtn {
    background-color: #d2ffd2;
    color: white;
    padding: 1px;
    font-size: 16px;
    border: none;
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    /*padding-right: 28px;*/
    font-size: 12px;
    white-space: nowrap;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
       display: none;
    position: absolute;
    background-color: #f1f1f1;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgb(0 0 0 / 20%);
    z-index: 5;
    /*top: -1px;
    margin-left: 143px;*/
    padding-left:10px;   
}

    .dropdown-content a {
        /*color: black;*/
        padding: 8px 7px;
        text-decoration: none;
        display: block;
    }

        .dropdown-content a:hover {
            background-color: #ddd;
        }

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown:hover .dropbtn {
    /*background-color: #3e8e41;*/
}

.downloadEEUFormTemplate .iconButton  {
    position: absolute; 
    right: 30px; 
    top: 0px; 
    color: blue !important;
    width:170px !important;
}

.downloadEUUFormSA992C .iconButton  {
    position: absolute; 
    right: 222px; 
    top: 0px; 
    color: blue !important;
    width:170px !important;
}

</style>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Lines" BoxType="Standard">
    <Links>
        <div class="downloadEEUFormTemplate">
            <ReboundUI:IconButton ID="ibtnEUUForm" runat="server" IconGroup="Nugget" IconTitleResource="DownloadEEUFormTemplate"/>         
        </div>
<%--        <div class=".downloadEUUFormSA992C">
            <ReboundUI:IconButton ID="ibtnEUUFormSA992C" runat="server" IconGroup="Nugget" IconTitleResource="ibtnEUUFormSA992C"/>
        </div>--%>
    </Links>
    <Content>
        <ReboundUI:FlexiDataTable ID="tblAll" runat="server" AllowSelection="true" PanelHeight="160" />
    </Content>
</ReboundUI_Nugget:DesignBase>


