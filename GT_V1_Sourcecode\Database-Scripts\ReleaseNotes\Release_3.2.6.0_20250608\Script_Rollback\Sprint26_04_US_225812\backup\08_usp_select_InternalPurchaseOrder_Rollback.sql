﻿---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  
  
-- =============================================        
-- Author:  Surendra        
-- Create date: February 23, 2016        
-- Description: To get IPO header Information        
-- =============================================        
CREATE OR ALTER PROCEDURE usp_select_InternalPurchaseOrder         
@InternalPurchaseOrderId int           
AS        
BEGIN        
 SET NOCOUNT ON;        
 Declare @vSupplierRMAIds Varchar(1000)              
 Declare @vSupplierRMANumbers Varchar(1000)               
 Declare @vDebitIds Varchar(1000)              
 Declare @vDebitNumbers Varchar(1000)      
 DECLARE @PurchaseOrderNo INT    
 SET @PurchaseOrderNo=(Select TOP 1 ipo.PurchaseOrderNo from dbo.tbInternalPurchaseOrder ipo WHERE ipo.InternalPurchaseOrderId=@InternalPurchaseOrderId)    
 Execute usp_select_SupplierRMA_By_PurchaseOrder @PurchaseOrderNo ,@vSupplierRMAIds Out,@vSupplierRMANumbers Out              
 Execute usp_select_Debit_By_PurchaseOrder @PurchaseOrderNo, @vDebitIds Out,@vDebitNumbers Out              
          
    Declare @vEPRIds Varchar(1000)               
    Select @vEPRIds = COALESCE(@vEPRIds + ',','') + COALESCE(Cast(EPRId As Varchar),'')                    
    From tbEPR                
    Where PurchaseOrderId = @PurchaseOrderNo AND isnull(Inactive,0) = 0 order by DLUP desc            
           
               
SELECT *,              
    @vSupplierRMAIds As 'SupplierRMAIds',              
    @vSupplierRMANumbers As 'SupplierRMANumbers',              
    @vDebitIds As 'DebitIds',              
    @vDebitNumbers As 'DebitNumbers' ,            
    @vEPRIds as 'EPRIds'     
    ,(select RegionName from tbRegion where RegionId=ipo.RegionNo) as RegionName          
from dbo.vwInternalPurchaseOrder ipo                
WHERE ipo.InternalPurchaseOrderId = @InternalPurchaseOrderId        
END   
  
  