﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class CommunicationLogType : Base {

        public bool IncludeNewSystemDocuments { get; set; }

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("CommunicationLogType");
            AddScriptReference("Controls.DropDowns.CommunicationLogType.CommunicationLogType");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.CommunicationLogType", ClientID);
            base.OnLoad(e);
		}

        protected override void Render(HtmlTextWriter writer) {
            _scScriptControlDescriptor.AddProperty("blnIncludeNewSystemDocuments", IncludeNewSystemDocuments);
            base.Render(writer);
        }

	}
}