  
  
CREATE PROCEDURE [dbo].[usp_update_Stock_Quarantined]  
--**********************************************************************************************  
--* RP 30.09.2009:  
--* - Ensure Location change from null is recorded on Stock Log   
--*  
--* RP 06.08.2009:  
--* - Send Location change to Stock Log if necessary  
--**********************************************************************************************  
    @StockId int  
  , @Quarantine bit  
  , @Location nvarchar(10) = NULL  
  , @UpdatedBy int  
  , @RowsAffected int = NULL OUTPUT  
AS   
    BEGIN  
    
        BEGIN TRANSACTION  
  
        DECLARE @StockLogDetail nvarchar(50)  
        SET @StockLogDetail = ''  
        IF @Location IS NULL SET @Location = ''  
        IF NOT @Location = (SELECT  isnull(Location,'')  
                            FROM    tbStock  
                            WHERE   StockId = @StockId  
                           )  
            SET @StockLogDetail = 'Location'  
      
        UPDATE  dbo.tbStock  
        SET     Unavailable = @Quarantine  
              , Location = @Location  
              , UpdatedBy = @UpdatedBy  
              , DLUP = CURRENT_TIMESTAMP  
        WHERE   StockId = @StockId  
          
        DECLARE @QuantityInStock int  
          , @QuantityOnOrder int  
          , @StockLogId int  
    , @GoodsInLineNo INT  
          
        SELECT  @QuantityInStock = QuantityInStock  
              , @QuantityOnOrder = QuantityOnOrder  
     , @GoodsInLineNo = GoodsInLineNo  
        FROM    tbStock  
        WHERE   StockId = @StockId  
  
  --Espire:20 Feb 20 Update related GI Line in case stock change the quarnatine status  
  UPDATE tbGoodsInline set Unavailable = @Quarantine WHERE GoodsInLineId = @GoodsInLineNo  
          
          
        IF @Quarantine = 1   
            BEGIN  
                EXEC usp_insert_StockLog @StockLogTypeNo = 6 --Quarantined  
                    , @StockNo = @StockId --  
                    , @QuantityInStock = @QuantityInStock --  
                    , @QuantityOnOrder = @QuantityOnOrder --  
                    , @UpdatedBy = @UpdatedBy --  
                    , @Detail = @StockLogDetail --  
                    , @StockLogId = @StockLogId OUTPUT  --  
            END  
        ELSE   
            BEGIN  
                EXEC usp_insert_StockLog @StockLogTypeNo = 17 --Remove from quarantine  
                    , @StockNo = @StockId --  
                    , @QuantityInStock = @QuantityInStock --  
                    , @QuantityOnOrder = @QuantityOnOrder --  
                    , @Detail = @StockLogDetail --  
                    , @UpdatedBy = @UpdatedBy --  
                    , @StockLogId = @StockLogId OUTPUT  
            END  
          
          
        COMMIT TRANSACTION  
          
          
    END  
--  
    SELECT  @RowsAffected = @@ROWCOUNT  
  
  
  
  
  