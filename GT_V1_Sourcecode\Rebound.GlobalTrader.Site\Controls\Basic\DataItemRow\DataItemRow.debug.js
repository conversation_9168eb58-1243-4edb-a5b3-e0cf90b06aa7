///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - complete dispose event
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.DataItemRow = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataItemRow.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataItemRow.prototype = {

	get_tdTitle: function() { return this._tdTitle; }, set_tdTitle: function(value) { if (this._tdTitle !== value)  this._tdTitle = value; }, 
	get_lbl: function() { return this._lbl; }, set_lbl: function(value) { if (this._lbl !== value)  this._lbl = value; }, 
	get_chk: function() { return this._chk; }, set_chk: function(value) { if (this._chk !== value)  this._chk = value; }, 
	get_hid: function() { return this._hid; }, set_hid: function(value) { if (this._hid !== value)  this._hid = value; }, 
	get_hyp: function() { return this._hyp; }, set_hyp: function(value) { if (this._hyp !== value)  this._hyp = value; }, 
	get_stars: function() { return this._stars; }, set_stars: function(value) { if (this._stars !== value)  this._stars = value; }, 
	get_value: function() { return this._value; }, 	set_value: function(value) { if (this._value !== value)  this._value = value; }, 
	get_ctlEllipses: function() { return this._ctlEllipses; }, 	set_ctlEllipses: function(ctlEllipses) { if (this._ctlEllipses !== ctlEllipses)  this._ctlEllipses = ctlEllipses; }, 

	addEllipsesSetupData: function(handler) { this.get_events().addHandler("EllipsesSetupData", handler); },
	removeEllipsesSetupData: function(handler) { this.get_events().removeHandler("EllipsesSetupData", handler); },
	onEllipsesSetupData: function() {
		var handler = this.get_events().getHandler("EllipsesSetupData");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DataItemRow.callBaseMethod(this, "initialize");
		if (this._ctlEllipses) this._ctlEllipses.addSetupData(Function.createDelegate(this, this.onEllipsesSetupData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._chk) this._chk.dispose();
		if (this._stars) this._stars.dispose();
		if (this._ctlEllipses) this._ctlEllipses.dispose();
		this._tdTitle = null;
		this._lbl = null;
		this._chk = null;
		this._hid = null;
		this._hyp = null;
		this._stars = null;
		this._value = null;
		this._ctlEllipses = null;
		Rebound.GlobalTrader.Site.Controls.DataItemRow.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},

	setValue: function(varValue, varValue2) {
		if (this._lbl) {
			if (typeof(varValue) == "undefined" || !varValue) varValue = "";
			$R_FN.setInnerHTML(this._lbl, $R_FN.setCleanTextValue(varValue));
			this._value = varValue;
			return;
		}		
		if (this._chk) {
			if (typeof(varValue) == "undefined" || !varValue) varValue = false;
			this._chk.setChecked(varValue);
			this._value = this._chk._blnChecked;
			return;
		}
		if (this._stars) {
			if (typeof(varValue) == "undefined" || !varValue) varValue = 0;
			this._stars.setRating(varValue);
			this._value = this._stars._intCurrentRating;
			return;
		}
		if (this._hid) {
			if (typeof(varValue) == "undefined" || !varValue) varValue = "";
			this._hid.value = $R_FN.setCleanTextValue(varValue);
			this._value = varValue;
			return;
		}
		if (this._hyp) {
			if (typeof(varValue) == "undefined" || !varValue) varValue = "javascript:void(0);";
			if (typeof(varValue2) == "undefined" || !varValue2) varValue2 = "";
			$R_FN.showElement(this._hyp, (varValue2.trim().length > 0));
			$R_FN.setInnerHTML(this._hyp, $R_FN.setCleanTextValue(varValue2));
			this._hyp.href = $R_FN.setCleanTextValue(varValue);
			this._value = $R_FN.setCleanTextValue(varValue2);
			return;
		}
		this._value = "";
	},

	setEmailValue: function(strEmail, strText) {
		if (strText == "") strText = strEmail;
		if (this._hyp) this._hyp.target = "";
		this.setValue($R_FN.formatEmail(strEmail), strEmail);
	},

	setURLValue: function(strURL, strText, blnExternal) {
		if (blnExternal) {
			if (this._hyp) this._hyp.target = "_blank";
			strURL = $R_FN.formatURL(strURL);
		}
		if (strText == "") strText = strURL;
		this.setValue(strURL, strText);
	},
	
	showHide: function(blnShow) {
		$R_FN.showElement(this.get_element(), blnShow);
	},
	
	getValue: function() {
		if (this._lbl) return this._lbl.innerHTML;
		if (this._chk) return this._chk._blnChecked;
		if (this._stars) return this._stars._intCurrentRating;
		if (this._hid) return this._hid.value;
		if (this._hyp) return this._hyp.text;
	},
	
	setTitle: function(strTitle) {
		$R_FN.setInnerHTML(this._tdTitle, strTitle);
	},

	hideEllipses: function() {
		if (this._ctlEllipses) this._ctlEllipses.hide();
	},

	showEllipses: function() {
		if (this._ctlEllipses) this._ctlEllipses.show();
	},
	
	resetEllipses: function() {
		if (this._ctlEllipses) this._ctlEllipses.reset();
	},

	showEllipsesLoading: function() {
		if (this._ctlEllipses) this._ctlEllipses.showLoading();
	},
	
	showEllipsesError: function() {
		if (this._ctlEllipses) this._ctlEllipses.showError();
	},
	
	showEllipsesContent: function() {
		if (this._ctlEllipses) this._ctlEllipses.showContent();
	}

};

Rebound.GlobalTrader.Site.Controls.DataItemRow.registerClass("Rebound.GlobalTrader.Site.Controls.DataItemRow", Sys.UI.Control, Sys.IDisposable);