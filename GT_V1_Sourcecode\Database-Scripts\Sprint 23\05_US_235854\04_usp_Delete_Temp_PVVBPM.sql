﻿/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-235854]     CuongDox		 12-Mar-2025		CREATE		IPO- Simplified HUBRFQ Creation - Addition of PPV/ Bom Qualification at creation stage (Client Side)
============================================================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Delete_Temp_PVVBPM]    
      @GeneratedId NVARCHAR(100)    
    , @UpdatedBy int    
    , @RowsAffected int OUTPUT    
AS     
      BEGIN     
     Delete from tbHUBRFQPVVAnswerTemp where BomNoGenerated=@GeneratedId  
           
      SET @RowsAffected = @@rowcount    
    
 END    
    