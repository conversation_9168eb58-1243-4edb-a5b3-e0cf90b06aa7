//Changed by      Date Remarks
//[0001]     <PERSON>     21/06/2022   /// code for converting dictionary to concurrent dictionary
//[002]      Soorya          03/03/2023   RP-1048 Remove AI code
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
//using Microsoft.ApplicationInsights; //[002]
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site.Pages
{
    public class Content : Base
    {

        //private Dictionary<int, bool> _dctPagePermissions;
        private ConcurrentDictionary<int, bool> _dctPagePermissions;
        protected Breadcrumb _ctlBreadcrumb;
        protected SiteSection _objSiteSection;
        protected Masters.Base _mstMaster;
        //[001] code start
        protected List<string> _lstTabs = null;
        //[001] code end

        #region Properties

        private int _intCurrentTab = -1;
        public int CurrentTab
        {
            get { return _intCurrentTab; }
            set { _intCurrentTab = value; }
        }

        protected SecurityManager _objSecurityManager;
        public SecurityManager SecurityManager
        {
            get { return _objSecurityManager; }
            set { _objSecurityManager = value; }
        }

        /// <summary>
        /// Should the page check for messages?
        /// </summary>
        private bool _blnShouldCheckForMessages = true;
        public bool ShouldCheckForMailMessages
        {
            get { return _blnShouldCheckForMessages; }
            set { _blnShouldCheckForMessages = value; }
        }

        /// <summary>
        /// It check data is logged-in client or other
        /// </summary>
        private bool _blnIsDataHasOtherClient = false;
        public bool IsDataHasOtherClient
        {
            get { return _blnIsDataHasOtherClient; }
            set { _blnIsDataHasOtherClient = value; }
        }

        #endregion

        #region Overrides

        protected override void OnPreInit(EventArgs e)
        {
            _mstMaster = ((Masters.Base)Master);
            _blnConfigurationIsDebug = false;
#if (DEBUG)
            _blnConfigurationIsDebug = true;
#endif
            SetupQueryStringManager();
            this.CheckUserLoggedIn();

            if (SessionManager.LoginID == null)
            {
                _objSecurityManager = new SecurityManager(-1);
            }

            _intCurrentTab = _objQSManager.Tab;
            base.OnPreInit(e);
        }

        protected override void OnInit(EventArgs e)
        {
            if (_blnLoggedIn)
            {
                int? intMasterLoginNo = SessionManager.MasterLoginNo;
                string strServerIP = (Request != null) ? Request.ServerVariables["LOCAL_ADDR"] : "";
                var reqUrlFromV2 = (Request != null) ? Request.QueryString["redirectUri"] : "";
                if (intMasterLoginNo != null && intMasterLoginNo > 0)
                {
                    BLL.Package pk = BLL.Package.GetMasterLogin(intMasterLoginNo);
                    if (pk.ClientId != SessionManager.ClientID && SessionManager.ClientID > 0)
                    {
                        BLL.Login login = LoginManager.LogMasterNoIntoSystem(intMasterLoginNo, pk.ClientId, Request.UserHostAddress, Session.SessionID, strServerIP);
                        if (!string.IsNullOrEmpty(reqUrlFromV2))
                        {
                            Response.Redirect(reqUrlFromV2);
                        }
                        else
                        {
                            Response.Redirect(this.FullURLWithQueryString);
                        }
                    }
                }
                else
                {
                    NotADLoggedIn();
                }

                if (!string.IsNullOrEmpty(reqUrlFromV2))
                {
                    Response.Redirect(reqUrlFromV2);
                }
            }

            CheckSectionPermissions();
            _ctlBreadcrumb = (Breadcrumb)Functions.FindControlRecursive(this, "ctlBreadcrumb");
            CurrentTab = _objQSManager.Tab;
            TitleText = Functions.GetGlobalResource("PageTitles", _objSitePage.Name);
            // Null check because error occur after login out- 27-Jan-2021

            if (_mstMaster != null)
            {
                _mstMaster.SelectedSiteSectionID = _objSiteSection.ID;
                _mstMaster.CallingSitePage = _objSitePage;
                _mstMaster.ShouldCheckForMailMessages = _blnShouldCheckForMessages;
                base.OnInit(e);
            }
        }

        protected override void OnLoad(EventArgs e)
        {
            // Null check because error occur after login out- 27-Jan-2021
            if (_mstMaster != null)
            {
                _mstMaster.SetupPageScript("Rebound.GlobalTrader.Site.Pages.Content");
                AddCSSFile("ReboundUI.css");

                bool enableADAuth = false;
                if (!string.IsNullOrEmpty(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["EnableADAuth"])))
                    enableADAuth = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["EnableADAuth"]);

                if (!enableADAuth)
                {
                    if (!_blnLoggedIn) NotLoggedInNew(this.FullURLWithQueryString);
                }
                else
                {
                    if (!_blnLoggedIn)
                    {
                        NotADLoggedIn();
                    }
                }
                base.OnLoad(e);
            }
        }

        protected override void OnPreRender(EventArgs e)
        {
            // Null check because error occur after login out- 27-Jan-2021
            if (_mstMaster != null)
            {
                SetPageTitlebar(TitleText);
                base.OnPreRender(e);
            }
        }

        protected override void OnError(EventArgs e)
        {
            Exception Ex = Server.GetLastError();
            //Server.ClearError();
            //[002]
            // var ai = new TelemetryClient(); // or re-use an existing instance
            //ai.TrackTrace("Exception on Home Page");
            // ai.TrackException(Ex);
            //try { Server.Transfer(_objSite.GetPage("Error").Url); }
            //         catch(Exception ex)
            //         {
            //             Response.Write(ex.Message);
            //         }
        }

        #endregion

        #region Methods

        protected void SetSection(string strSection)
        {
            _objSiteSection = _objSite.GetSection(strSection);
        }

        protected void SetSection(Enum enmSection)
        {
            _objSiteSection = _objSite.GetSection(enmSection);
        }

        public void AddLocalImageForPreload(string strImagePath)
        {
            _mstMaster.AddImageForPreload(Functions.GetLocalThemeImage(strImagePath, Theme));
        }

        protected void CheckUserLoggedIn()
        {
            _blnLoggedIn = SessionManager.CheckLoggedInOnDatabase();
        }

        protected bool CheckPagePermission(BLL.SecurityFunction.List enmFunction)
        {
            if (_dctPagePermissions == null) GetPagePermissions();
            bool blnOK = true;
            if (_dctPagePermissions.ContainsKey((int)enmFunction)) _dctPagePermissions.TryGetValue((int)enmFunction, out blnOK);
            return blnOK;
        }

        protected bool CheckPagePermissionNew(BLL.SecurityFunction.List enmFunction, int PageId)
        {
            if (_dctPagePermissions == null || _dctPagePermissions.Count == 0) GetPagePermissions(PageId);
            bool blnOK = true;
            if (_dctPagePermissions.ContainsKey((int)enmFunction)) _dctPagePermissions.TryGetValue((int)enmFunction, out blnOK);
            return blnOK;
        }

        private void GetPagePermissions()
        {
            //_dctPagePermissions = new Dictionary<int, bool>();
            _dctPagePermissions = new ConcurrentDictionary<int, bool>();
            foreach (SecurityFunction sf in SecurityFunction.GetListPagePermissionsByPageAndLogin(_objSitePage.ID, SessionManager.LoginID, IsDataHasOtherClient))
            {
                //if (!_dctPagePermissions.ContainsKey(sf.SecurityFunctionId)) _dctPagePermissions.Add(sf.SecurityFunctionId, (bool)sf.IsAllowed);
                if (!_dctPagePermissions.ContainsKey(sf.SecurityFunctionId)) _dctPagePermissions.TryAdd(sf.SecurityFunctionId, (bool)sf.IsAllowed);
            }
        }

        private void GetPagePermissions(int byPageId)
        {
            //_dctPagePermissions = new Dictionary<int, bool>();
            _dctPagePermissions = new ConcurrentDictionary<int, bool>();
            foreach (SecurityFunction sf in SecurityFunction.GetListPagePermissionsByPageAndLogin(byPageId, SessionManager.LoginID, IsDataHasOtherClient))
            {
                //if (!_dctPagePermissions.ContainsKey(sf.SecurityFunctionId)) _dctPagePermissions.Add(sf.SecurityFunctionId, (bool)sf.IsAllowed);
                if (!_dctPagePermissions.ContainsKey(sf.SecurityFunctionId)) _dctPagePermissions.TryAdd(sf.SecurityFunctionId, (bool)sf.IsAllowed);
            }
        }




        private void CheckSectionPermissions()
        {
            //if (SessionManager.LoginID == null)
            //    Response.Redirect("ADLogin.aspx");

            if (_objSiteSection.ID < 1 || SessionManager.LoginID == null) return;
            if (!SecurityManager.CheckCanViewSection((BLL.SiteSection.List)(_objSiteSection.ID))) RedirectToNotFound();
            _objSecurityManager = new SecurityManager((BLL.SiteSection.List)(_objSiteSection.ID), (BLL.SitePage.List)(_objSitePage.ID), (int)SessionManager.LoginID, IsDataHasOtherClient);
        }

        protected void RedirectToNotFound()
        {
            HttpContext.Current.Response.Redirect(_objSite.GetPage("NotFound").Url, true);
        }
        protected void GetInvisibleTabSecurityList(Rebound.GlobalTrader.BLL.SecurityFunction.List enmPageId)
        {
            if (SessionManager.LoginID != null)
            {
                TabSecurityFuncion obj = TabSecurityFuncion.GetInvisibleTabSecurityList((int)enmPageId, (int)SessionManager.LoginID);
                if (obj == null)
                    _lstTabs = new List<string>();
                else
                {
                    _lstTabs = new List<string>();
                    if (obj.MyTab.Value == false)
                        _lstTabs.Add("0");
                    if (obj.TeamTab.Value == false)
                        _lstTabs.Add("1");
                    if (obj.DivisionTab.Value == false)
                        _lstTabs.Add("2");
                    if (obj.CompanyTab.Value == false)
                        _lstTabs.Add("3");

                }
            }
            else
            {
                _lstTabs = new List<string>();
            }

        }
        protected void GetVisibleTabSecurityList(Rebound.GlobalTrader.BLL.SecurityFunction.List enmPageId)
        {
            if (SessionManager.LoginID != null)
            {
                TabSecurityFuncion obj = TabSecurityFuncion.GetInvisibleTabSecurityList((int)enmPageId, (int)SessionManager.LoginID);
                if (obj == null)
                    _lstTabs = new List<string>();
                else
                {
                    _lstTabs = new List<string>();
                    if (obj.MyTab.Value == true)
                        _lstTabs.Add("0");
                    if (obj.TeamTab.Value == true)
                        _lstTabs.Add("1");
                    if (obj.DivisionTab.Value == true)
                        _lstTabs.Add("2");
                    if (obj.CompanyTab.Value == true)
                        _lstTabs.Add("3");
                }
            }
            else
            {
                _lstTabs = new List<string>();
            }
        }
        protected void GetVisibleCompanyTabSecurityList(Rebound.GlobalTrader.BLL.SecurityFunction.List enmPageId1, Rebound.GlobalTrader.BLL.SecurityFunction.List enmPageId2)
        {
            if (SessionManager.LoginID != null)
            {
                TabSecurityFuncion obj = TabSecurityFuncion.GetVisibleCompanyTabSecurityList((int)enmPageId1, (int)enmPageId2, (int)SessionManager.LoginID);
                if (obj == null)
                    _lstTabs = new List<string>();
                else
                {
                    _lstTabs = new List<string>();
                    if (obj.MyTab.Value == true)
                        _lstTabs.Add("0");
                    if (obj.TeamTab.Value == true)
                        _lstTabs.Add("1");
                    if (obj.DivisionTab.Value == true)
                        _lstTabs.Add("2");
                    if (obj.CompanyTab.Value == true)
                        _lstTabs.Add("3");
                }
            }
            else
            {
                _lstTabs = new List<string>();
            }
        }
        protected bool CheckTabSecurityInDetailPage(List<string> lst, Int32 teamId, Int32 loginTeamID, Int32 divisionId, Int32 loginDivisionId, Int32 clientId, Int32 loginClientId, Int32 loginId, Int32 loginUserId)
        {
            bool IsAllow = false;
            if (lst.Contains(Convert.ToString(3)))
            {
                IsAllow = ((clientId == loginClientId) ? true : false);
            }
            if (!IsAllow && lst.Contains(Convert.ToString(2)))
            {
                IsAllow = ((divisionId == loginDivisionId) ? true : false);
            }
            if (!IsAllow && lst.Contains(Convert.ToString(1)))
            {
                IsAllow = ((teamId == loginTeamID) ? true : false);
            }
            if (!IsAllow && lst.Contains(Convert.ToString(0)))
            {
                IsAllow = ((loginId == loginUserId) ? true : false);
            }
            return IsAllow;
        }

        #endregion

    }
}
