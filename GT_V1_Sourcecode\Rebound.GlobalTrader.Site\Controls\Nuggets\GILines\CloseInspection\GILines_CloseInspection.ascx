<%@ Control Language="C#" CodeBehind="GILines_CloseInspection.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

	<Explanation>
		<asp:Label ID="lblExplainStartInspection" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("FormExplanations", "GILines_CloseInspection")%></asp:Label>
		
	</Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
            <asp:TableRow>
                <asp:TableCell RowSpan="2" Style="width: 100%">
<style>
    .labelheader{
    text-align: left;
    font-weight: bold;
    font-size: 11px;
    color: #d3fFcC;
    }
</style>
                    <table id="CloseInsTable" style="width: 100%;">
                        <tr>
                            <td style="width: 12%;"><label class="labelheader"><%=Functions.GetGlobalResource("FormFields", "Cl_PartTicked")%></label></td>
                            <td style="width: 32%;"><label id="lblPartTicked"></label></td>
                           <%-- <td><ReboundUI:ImageCheckBox ID="chkPartTicked" runat="server" Enabled="true" /></td>--%>
                        </tr>
                        <tr>
                            <td style="width: 12%;"><label class="labelheader"><%=Functions.GetGlobalResource("FormFields", "Cl_PBComplete")%></label></td>
                            <td style="width: 32%;"><label id="lblPBComplete"></label></td>
                            <%--<td><ReboundUI:ImageCheckBox ID="chkPBComplete" runat="server" Enabled="true" /></td>--%>
                        </tr>
                        <tr>
                            <td style="width: 12%;"><label class="labelheader"><%=Functions.GetGlobalResource("FormFields", "Cl_PhotosAttached")%></label></td>
                            <td style="width: 32%;"><label id="lblPhotosAttached"></label></td>
                            <%--<td><ReboundUI:ImageCheckBox ID="chkPhotosAttached" runat="server" Enabled="true" /></td>--%>
                        </tr>
                        <tr>
                            <td style="width: 12%;"><label class="labelheader"><%=Functions.GetGlobalResource("FormFields", "Cl_BarCodeScan")%></label></td>
                            <td style="width: 32%;"><label id="lblBarCodeScan"></label></td>
                            <%--<td><ReboundUI:ImageCheckBox ID="chkBarCodeScan" runat="server" Enabled="true" /></td>--%>
                        </tr>
                         <tr>
                            <td style="width: 12%;"><label class="labelheader"><%=Functions.GetGlobalResource("FormFields", "Cl_QueryFormat")%></label></td>
                            <td style="width: 32%;"><label id="lblQueryFormat"></label></td>
                            <%--<td><ReboundUI:ImageCheckBox ID="chkQueryFormat" runat="server" Enabled="true" /></td>--%>
                        </tr>
                        <tr>
                            <td colspan="3">
                              <ReboundUI_Form:FormField id="ctlComment" runat="server" FieldID="txtNote" ResourceTitle="Comment">
				                <Field><ReboundUI:ReboundTextBox ID="txtNote" runat="server" Style="height:49px; width:310px" Width="310" TextMode="MultiLine" Rows="2" CountChar="true" /></Field>          
			                  </ReboundUI_Form:FormField>
                            </td>
                        </tr>
                        <tr>
                            <td colspan="3">
                              <ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				                <Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			                  </ReboundUI_Form:FormField>
                            </td>
                        </tr>
                    </table>
                </asp:TableCell>
            </asp:TableRow>

			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
