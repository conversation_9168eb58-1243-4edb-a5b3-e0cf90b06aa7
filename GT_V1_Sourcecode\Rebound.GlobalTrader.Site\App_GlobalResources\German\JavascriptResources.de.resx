﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AddressTypeAlreadyEntered" xml:space="preserve">
    <value>Eine Adresse mit dieser Art ist bereits eingegeben worden</value>
  </data>
  <data name="ApplicationError" xml:space="preserve">
    <value>Traurig, gab es ein Problem.&lt;br /&gt;Die folgenden Details sind in Ihrem Computer' gemerkt worden; s-Ereignismaschinenbordbuch und gemailt dem Rebound-Personal für Aufmerksamkeit.</value>
    <comment>Copied from Messages.resx</comment>
  </data>
  <data name="ChangesSavedSuccessfully" xml:space="preserve">
    <value>Ihre Änderungen wurden erfolgreich gespart</value>
  </data>
  <data name="ContactsForCompany" xml:space="preserve">
    <value>Contacts for {0}</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>Es gab ein Problem mit der Datenbank</value>
  </data>
  <data name="DatabaseTimeout" xml:space="preserve">
    <value>Traurig, hat der Datenbankaufruf heraus Zeit festgesetzt</value>
  </data>
  <data name="DataNotFound" xml:space="preserve">
    <value>Störung: Keine Daten fanden</value>
  </data>
  <data name="DateTimeMustBeInFuture" xml:space="preserve">
    <value>Erreichen Sie bitte ein Datum und eine Zeit zukünftig</value>
  </data>
  <data name="DuplicateCurrencyCode" xml:space="preserve">
    <value>Eine Währung mit diesem Code existiert bereits von der Datenbank.</value>
  </data>
  <data name="DuplicateLoginName" xml:space="preserve">
    <value>Der LOGON-Name, den Sie eintrugen, ist bereits genommen worden</value>
  </data>
  <data name="EmailInvalidMessage" xml:space="preserve">
    <value>Tragen Sie bitte ein gültiges email address ein</value>
  </data>
  <data name="EnterFieldGeneric" xml:space="preserve">
    <value>Tragen Sie bitte einen Wert ein</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Störung</value>
  </data>
  <data name="FeedbackSent" xml:space="preserve">
    <value>Rebound Ihr Rückgespräch ist geschickt worden, danke während Ihrer Zeit.&lt;br/&gt;Presse &lt;b&gt;Fortfahren&lt;/b&gt;zu zu Ihrer vorigen Seite zurückgehen.</value>
  </data>
  <data name="FolderAdded" xml:space="preserve">
    <value>Ihr neues Faltblatt ist addiert worden</value>
  </data>
  <data name="FolderDeleted" xml:space="preserve">
    <value>Ihr Faltblatt ist gelöscht worden</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Unaktiviert</value>
  </data>
  <data name="Inbox" xml:space="preserve">
    <value>Inbox</value>
  </data>
  <data name="ItemXOfY" xml:space="preserve">
    <value>Einzelteil {0} von {1}</value>
  </data>
  <data name="LineXOfY" xml:space="preserve">
    <value>Linie {0} von {1}</value>
  </data>
  <data name="Loading" xml:space="preserve">
    <value>Laden</value>
  </data>
  <data name="MessageDeleted" xml:space="preserve">
    <value>Ihre Mitteilungen sind gelöscht worden</value>
  </data>
  <data name="MessageMoved" xml:space="preserve">
    <value>Ihre Mitteilungen sind verschoben worden</value>
  </data>
  <data name="MessageSent" xml:space="preserve">
    <value>Your message has been sent</value>
  </data>
   <data name="NewCustomerRequirementAdded" xml:space="preserve">
    <value>Neue Kunden-Anforderung fügte hinzu</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewCustomerRMAAdded" xml:space="preserve">
    <value>Neuer Kunde RMA fügte hinzu</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewCreditNoteAdded" xml:space="preserve">
    <value>Neue Kreditnote addiert</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewDebitNoteAdded" xml:space="preserve">
    <value>Neue Belastungsanzeige addiert</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewGoodsInAdded" xml:space="preserve">
    <value>Neue Waren in der Anmerkung fügten hinzu</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewInvoiceAdded" xml:space="preserve">
    <value>Neue Rechnung addiert</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewPurchaseOrderAdded" xml:space="preserve">
    <value>Neuer Kaufauftrag fügte hinzu</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewQuoteAdded" xml:space="preserve">
    <value>Neuer Preisangabe fügte hinzu</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewSalesOrderAdded" xml:space="preserve">
    <value>Neuer Verkaufs-Auftrag fügte hinzu</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="NewSupplierRMAAdded" xml:space="preserve">
    <value>Neuer Lieferant RMA fügte hinzu</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="No" xml:space="preserve">
    <value>Nein</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>Kein</value>
  </data>
  <data name="NumericFieldError" xml:space="preserve">
    <value>Tragen Sie bitte einen gültigen numerischen Wert ein</value>
  </data>
  <data name="OldPasswordIncorrect" xml:space="preserve">
    <value>Das Kennwort, das Sie eintrugen, ist falsch.</value>
  </data>
  <data name="RequiredFieldMissingMessage" xml:space="preserve">
    <value>Tragen Sie bitte einen Wert ein</value>
  </data>
  <data name="RetainOneAdmin" xml:space="preserve">
    <value>Die Verwalter-Gruppe muss mindestens ein Mitglied behalten.</value>
  </data>
  <data name="ROHSCompliant" xml:space="preserve">
    <value>RoHS gefällig</value>
  </data>
  <data name="ROHSExempt" xml:space="preserve">
    <value>RoHS ausgenommen</value>
  </data>
  <data name="ROHSNonCompliant" xml:space="preserve">
    <value>RoHS Non-compliant</value>
  </data>
  <data name="ROHSUnknown" xml:space="preserve">
    <value>RoHS Unbekanntes</value>
  </data>
  <data name="SentMessages" xml:space="preserve">
    <value>Gesendet</value>
  </data>
  <data name="SourcingNuggetTitle" xml:space="preserve">
    <value>Auftreten</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>Heute</value>
  </data>
  <data name="Unfiltered" xml:space="preserve">
    <value>Ungefiltert</value>
  </data>
  <data name="URLInvalidMessage" xml:space="preserve">
    <value>Geben Sie bitte eine gültige Netzadresse ein</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Ja</value>
  </data>
  <data name="EmailTo" xml:space="preserve">
    <value>Zu</value>
  </data>
  <data name="TooMuchStockAllocated" xml:space="preserve">
    <value>Sie haben mehr Vorrat zugeteilt, als vorhanden ist</value>
  </data>
  <data name="ROHSNotApplicable" xml:space="preserve">
    <value>RoHS nicht anwendbar</value>
  </data>
  <data name="UploadFileTooBig" xml:space="preserve">
    <value>Die Akte, die Sie vorgewählt haben, ist zu groß. Wählen Sie bitte eine Akte vor, die kleiner als 4mb ist.</value>
  </data>
  <data name="FileUploadFailed" xml:space="preserve">
    <value>Die Aktenantriebskraft verlassen</value>
  </data>
  <data name="FileUploadNotAllowedType" xml:space="preserve">
    <value>Die Akte, die Sie vorgewählt haben, ist nicht eine erlaubte Art</value>
  </data>
  <data name="AppTitle" xml:space="preserve">
    <value>Rebound Global:Trader</value>
  </data>
  <data name="NumericFieldGreaterThanError" xml:space="preserve">
    <value>Tragen Sie bitte einen numerischen Wert ein, der als größer ist {0}</value>
  </data>
  <data name="NumericFieldLessThanError" xml:space="preserve">
    <value>Tragen Sie bitte einen numerischen Wert weniger als ein {0}</value>
  </data>
  <data name="NumericFieldGreaterThanOrEqualToError" xml:space="preserve">
    <value>Tragen Sie bitte einen numerischen Wert ein, der als größer sind oder Gleichgestelltes {0}</value>
  </data>
  <data name="NumericFieldLessThanOrEqualToError" xml:space="preserve">
    <value>Tragen Sie bitte einen numerischen Wert weniger als oder Gleichgestelltes ein {0}</value>
  </data>
  <data name="NotifySalesOrder" xml:space="preserve">
    <value>Verkaufs-Auftrags- {0} Mitteilung</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="Sending" xml:space="preserve">
    <value>Senden</value>
  </data>
  <data name="Saving" xml:space="preserve">
    <value>Einsparung</value>
  </data>
  <data name="ReceivedPurchaseOrder" xml:space="preserve">
    <value>Empfangener Kaufauftrag</value>
  </data>
  <data name="PopupLoggedOut" xml:space="preserve">
    <value>Sie werden nicht mehr, dieses Fenster schließen angemeldet</value>
  </data>
  <data name="NotifyGoodsIn" xml:space="preserve">
    <value>Waren in Anmerkungs- {0} Mitteilung</value>
    <comment>Used in Mail Message Subjects</comment>
  </data>
  <data name="OnStop" xml:space="preserve">
    <value>Auf Anschlag</value>
  </data>
  <data name="Units_Kg" xml:space="preserve">
    <value>kg</value>
  </data>
  <data name="Units_Pounds" xml:space="preserve">
    <value>lb</value>
  </data>
  <data name="ClosePO" xml:space="preserve">
    <value>Dieses schließt die Überschrift und alle Linien. 
Alle Verteilungen werden gelöscht.
Alle bestellte Linie Quantitäten wird auf die empfangene Quantität eingestellt.
Der in Verbindung stehende Verkaufs-Auftrag wird nicht gelöscht.
Stellen Sie bitte sicher, mit dass alle in Verbindung stehenden Unterlagen beschäftigt werden.</value>
  </data>
  <data name="CloseSO" xml:space="preserve">
    <value>Dieses schließt die Überschrift und alle Linien. 
Alle Verteilungen werden gelöscht.
Der in Verbindung stehende Kaufauftrag wird nicht gelöscht.
Stellen Sie bitte sicher, mit dass alle in Verbindung stehenden Unterlagen beschäftigt werden.</value>
  </data>
  <data name="ConfirmQuantityOrderedGreaterThanRequired" xml:space="preserve">
    <value>Die Quantität, die bestellt wird, ist größer als die erforderte Quantität.
Möchten Sie diese übermäßige Quantität behalten?</value>
  </data>
  <data name="FreightChargeAndShippingChanged" xml:space="preserve">
    <value>Frachtgebühren und Verschiffenkosten geändert.</value>
  </data>
  <data name="FreightChargeLeft" xml:space="preserve">
    <value>Frachtgebühren werden gelassen, wie ist.</value>
  </data>
  <data name="FreightChargeWillBeChanged" xml:space="preserve">
    <value>Frachtgebühren werden geändert.</value>
  </data>
  <data name="QuantityOrderedAsEntered" xml:space="preserve">
    <value>Die Quantität, die bestellt wird, ist, wie eingeführt.</value>
  </data>
  <data name="QuantityOrderedResetToOriginal" xml:space="preserve">
    <value>Die bestellte Quantität ist zum ursprünglichen Wert zurückgestellt worden.</value>
  </data>
  <data name="ShipInCostChanged" xml:space="preserve">
    <value>Versenden-in den Kosten geändert.</value>
  </data>
  <data name="ShippingWaived" xml:space="preserve">
    <value>Verschiffen aufgegeben</value>
  </data>
  <data name="SOFreightChargeOption" xml:space="preserve">
    <value>Dieser Kunde wird nicht normalerweise für Verschiffen aufgeladen. 
Presse O.K., zum der Rückstellungskosten einzustellen, damit diese Verschiffen Methode oder Löschen die Frachtgebühren lässt, wie ist.
Den Rückstellungsverschiffenkosten werden jede Weise eingestellt.</value>
  </data>
  <data name="ClosePOLine" xml:space="preserve">
    <value>Dieses schließt die Linie.
Jede mögliche Verteilung wird gelöscht.
Die bestellte Linie Quantität wird auf die empfangene Quantität eingestellt.
Die in Verbindung stehende Verkaufs-Auftragslinie wird nicht gelöscht.
Stellen Sie bitte sicher, mit dass alle in Verbindung stehenden Unterlagen beschäftigt werden.</value>
  </data>
  <data name="CloseSOLine" xml:space="preserve">
    <value>Dieses schließt die Überschrift und alle Linien. 
Alle Verteilungen werden gelöscht.
Die bestellte Linie Quantität kann auf die versendete Quantität eingestellt werden, wenn sie angezeigt wird.
Die in Verbindung stehende Kaufauftraglinie wird nicht gelöscht.
Stellen Sie bitte sicher, mit dass alle in Verbindung stehenden Unterlagen beschäftigt werden.</value>
  </data>

  <data name="NewCustomerRequirement" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neue Kunden-Anforderung&lt;/a&gt; addiert:

Nummer: [#CUSREQ_NUMBER#]
Kunde: [#CUSTOMER#]
Quantität: [#QUANTITY#]
Teilenummer: [#PART#]
Kunde Teilenummer Part No: [#CUSTOMERPART#]
Hersteller: [#MANUFACTURER#]
DateCode: [#DATECODE#]
Produkt: [#PRODUCT#]
Paket: [#PACKAGE#]
Richtpreis: [#PRICE#]
Datum erforderte: [#DATEREQUIRED#]</value>
  </data>
  </root>