﻿using System;

namespace Rebound.GlobalTrader.DAL
{
    public class ProspectiveOfferDetails
    {
        public int ProspectiveOfferId { get; set; }
        public int SupplierId { get; set; }
        public string SupplierName { get; set; }
        public string SourceFileName { get; set; }
        public int ImportRowCount { get; set; }
        public string ImportStatus { get; set; }
        public string ImportedBy { get; set; }
        public DateTime ImportDate { get; set; }
        public int RowCnt { get; set; }
    }
}
