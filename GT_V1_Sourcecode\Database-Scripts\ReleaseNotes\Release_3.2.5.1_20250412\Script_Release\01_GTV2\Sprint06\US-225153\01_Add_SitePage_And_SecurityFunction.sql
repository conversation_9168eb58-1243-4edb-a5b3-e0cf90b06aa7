﻿/*   
===========================================================================================  
TASK	            UPDATED BY			      DATE			ACTION		DESCRIPTION  
[US-225153]		Trang.PhamNguyenThuy		18-Dec-2024		Create		Add new permissions to the Warnings setup in the database
===========================================================================================  
*/

DECLARE @SitePageId INT = 6011801
DECLARE @SiteSectionNo INT = 6

IF NOT EXISTS (
      SELECT 1
      FROM [dbo].[tbSitePage]
      WHERE [SitePageId] = @SitePageId
)
INSERT INTO [dbo].[tbSitePage]
           ([SitePageId]
           ,[ShortName]
           ,[Description]
           ,[URL]
           ,[SiteSectionNo]
           ,[UpdatedBy]
           ,[DLUP])
     VALUES
           (@SitePageId
           ,'Setup_CompanyDetails_Warnings'
           ,null
           ,'Set_CD_Warnings.aspx'
           ,@SiteSectionNo
           ,null
           ,CURRENT_TIMESTAMP)

IF NOT EXISTS (
      SELECT 1
      FROM [dbo].[tbSecurityFunction]
      WHERE [SecurityFunctionId] = @SitePageId
)
INSERT INTO [dbo].[tbSecurityFunction]
           ([SecurityFunctionId]
           ,[FunctionName]
           ,[Description]
           ,[SitePageNo]
           ,[SiteSectionNo]
           ,[ReportNo]
           ,[UpdatedBy]
           ,[DLUP]
           ,[InitiallyProhibitedForNewLogins]
           ,[DisplaySortOrder])
     VALUES
           ( @SitePageId
           ,'Setup_CompanySettings_Warnings'
           , null
           , null
           ,@SiteSectionNo
           ,null
           ,null
           ,CURRENT_TIMESTAMP
           ,1
           ,12)

IF NOT EXISTS (
      SELECT 1
      FROM [dbo].[tbSecurityFunction]
      WHERE [SecurityFunctionId] = 6011802
)
INSERT INTO [dbo].[tbSecurityFunction]
           ([SecurityFunctionId]
           ,[FunctionName]
           ,[Description]
           ,[SitePageNo]
           ,[SiteSectionNo]
           ,[ReportNo]
           ,[UpdatedBy]
           ,[DLUP]
           ,[InitiallyProhibitedForNewLogins]
           ,[DisplaySortOrder])
     VALUES
           ( 6011802
           ,'Setup_CompanySettings_Warnings_Add'
           , null
           , @SitePageId
           ,@SiteSectionNo
           ,null
           ,null
           ,CURRENT_TIMESTAMP
           ,1
           ,1)

IF NOT EXISTS (
      SELECT 1
      FROM [dbo].[tbSecurityFunction]
      WHERE [SecurityFunctionId] = 6011803
)
INSERT INTO [dbo].[tbSecurityFunction]
           ([SecurityFunctionId]
           ,[FunctionName]
           ,[Description]
           ,[SitePageNo]
           ,[SiteSectionNo]
           ,[ReportNo]
           ,[UpdatedBy]
           ,[DLUP]
           ,[InitiallyProhibitedForNewLogins]
           ,[DisplaySortOrder])
     VALUES
           ( 6011803
           ,'Setup_CompanySettings_Warnings_Edit'
           , null
           , @SitePageId
           ,@SiteSectionNo
           ,null
           ,null
           ,CURRENT_TIMESTAMP
           ,1
           ,2)

--delete from [tbSitePage] where SitePageId = @SitePageId
--delete from [tbSecurityFunction] where SecurityFunctionId in (@SitePageId, 6011802, 6011803)