Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.ReboundTextBox=function(){this.message=null};var $R_TXTBOX=Rebound.GlobalTrader.Site.Controls.ReboundTextBox;$R_TXTBOX._objEnterEvents={};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.dispose=function(){$R_TXTBOX.isDisposed||($R_TXTBOX._objEnterEvents=null,$R_TXTBOX.isDisposed=!0,this.message=null)};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.initTextBox=function(n,t,i,r,u,f,e,o,s,h){n&&(n.removeAttribute("onfocus"),typeof h=="undefined"?($R_TXTBOX.setCountChar(n,s,null),$R_TXTBOX.setMode(n,t,i,null)):($R_TXTBOX.setCountChar(n,s,h.id),$R_TXTBOX.setMode(n,t,i,h.id)),$R_TXTBOX.setUpperCaseOnly(n,r),$R_TXTBOX.setFixedWidth(n,u),f&&n.setAttribute("onblur",String.format("{0} $R_TXTBOX.setDecimalPlacesOnBlur(this, {1});",n.getAttribute("onblur"),e)),$R_TXTBOX.checkNumberOnly(n,t,o))};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setMode=function(n,t,i,r){if(n){i==null&&(i=!1);var u="",e="48, 49, 50, 51, 52, 53, 54, 55, 56, 57",f="Sys.UI.Key.enter, Sys.UI.Key.backspace, Sys.UI.Key.tab, Sys.UI.Key.esc, Sys.UI.Key.space, Sys.UI.Key.pageUp, Sys.UI.Key.pageDown, Sys.UI.Key.end, Sys.UI.Key.home, Sys.UI.Key.left, Sys.UI.Key.up, Sys.UI.Key.right, Sys.UI.Key.down, Sys.UI.Key.del",o=Sys.CultureInfo.CurrentCulture.numberFormat.CurrencyDecimalSeparator.charCodeAt(0),s=Sys.CultureInfo.CurrentCulture.dateTimeFormat.TimeSeparator.charCodeAt(0);switch(t){case $R_ENUM$TextBoxMode.Normal:u="[]";break;case $R_ENUM$TextBoxMode.Numeric:u=String.format("[{0}, {1}]",f,e);break;case $R_ENUM$TextBoxMode.NumericDash:u=String.format("[{0}, {1}]",f,"45, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57");break;case $R_ENUM$TextBoxMode.Currency:u=String.format("[{0}, {1}, {2}]",f,e,o);break;case $R_ENUM$TextBoxMode.Time:u=String.format("[{0}, {1}, {2}, {3}]",f,e,s,o);break;case $R_ENUM$TextBoxMode.AlphaNumeric:u=String.format("[{0}, {1}, {2}, {3}]",f,e,"65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90","97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110 , 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122")}u==""&&(u="[]");n.setAttribute("onkeypress",String.format("return $R_TXTBOX.checkKeyPress(event, {0}, {1}, []);",i,u));n.setAttribute("B_TXTBOX_Mode",t);r!=null&&n.setAttribute("maxlength","2000");u=null;e=null;f=null;o=null;s=null}};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setUpperCaseOnly=function(n,t){if(n)if(t)n.setAttribute("onblur","$R_TXTBOX.setToUpperCaseOnBlur(this);");else{var i=n.getAttribute("onblur");i!=null&&n.setAttribute("onblur",i.replace("$R_TXTBOX.setToUpperCaseOnBlur(this);","").trim());i=null}};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.checkNumberOnly=function(n,t,i){if(n)if(t==1||i==!0)n.setAttribute("onblur","$R_TXTBOX.setNumberOnly(this);");else{var r=n.getAttribute("onblur");r!=null&&n.setAttribute("onblur",r.replace("$R_TXTBOX.setNumberOnly(this);","").trim());r=null}};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setFixedWidth=function(n,t){n&&(t?Sys.UI.DomElement.addCssClass(n,"fixedWidth"):Sys.UI.DomElement.removeCssClass(n,"fixedWidth"))};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.getTextBoxMode=function(n){if(n)return n.getAttribute("B_TXTBOX_Mode")};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.checkKeyPress=function(n,t,i,r){var u,f,e,o,s,h;try{if(n.keyCode&&(u=n.keyCode),n.which&&(u=n.which),u==Sys.UI.Key.enter){if($R_TXTBOX._objEnterEvents[n.target.id]){try{$R_TXTBOX._objEnterEvents[n.target.id](n.target)}catch(c){alert(c)}return!1}if(!t)return!1}if(n.ctrlKey&&(u==99||u==118||u==120))return!0;if(i&&i.length>0){for(f=!1,e=0,o=i.length;e<o;e++)if(u==i[e]){f=!0;break}return f}if(r&&r.length>0){for(f=!0,s=0,h=r.length;e<h;e++)if(u==r[s]){f=!1;break}return f}}finally{u=null}return!0};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setToUpperCaseOnBlur=function(n){n&&(n.value=n.value.toUpperCase())};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setDecimalPlacesOnBlur=function(n,t){n&&(n.value=$R_FN.formatCurrency(n.value,"",t))};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setNumberOnly=function(n){if(n){var t=!1;t=$R_FN.isValidNumber(n.value);t||(n.value=0)}};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.addEnterPressedEvent=function(n,t){n&&n.id&&($R_TXTBOX._objEnterEvents||($R_TXTBOX._objEnterEvents={}),$R_TXTBOX._objEnterEvents[n.id]=t)};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.removeInitialSettings=function(n){n&&n.removeAttribute("onfocus")};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.clearEvents=function(n){n&&n.id&&$R_TXTBOX._objEnterEvents&&($R_TXTBOX._objEnterEvents[n.id]=null)};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setCountChar=function(n,t,i){if(n)if(t)n.setAttribute("onkeyup","$R_TXTBOX.setCountCharOnkeyUp(this,"+i+");");else{var r=n.getAttribute("onkeyup");r!=null&&n.setAttribute("onkeyup",r.replace("$R_TXTBOX.setCountCharOnkeyUp(this,"+i+");","").trim());r=null}};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setCountCharOnkeyUp=function(n,t){if(n){var r=0,i=0;r=n.value.length;i=2e3-r;i<0;i>0;$R_FN.setInnerHTML(document.getElementById(t.id),i)}};Rebound.GlobalTrader.Site.Controls.ReboundTextBox.registerClass("Rebound.GlobalTrader.Site.Controls.ReboundTextBox");