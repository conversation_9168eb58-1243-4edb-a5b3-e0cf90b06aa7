///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Profile");

Rebound.GlobalTrader.Site.Pages.Profile.Profile = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Profile.Profile.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Profile.Profile.prototype = {

	get_ctlLeft_RecentlyViewed: function() { return this._ctlLeft_RecentlyViewed; }, 	set_ctlLeft_RecentlyViewed: function(v) { if (this._ctlLeft_RecentlyViewed !== v)  this._ctlLeft_RecentlyViewed = v; }, 
	get_ctlUserPreferences: function() { return this._ctlUserPreferences; }, 	set_ctlUserPreferences: function(v) { if (this._ctlUserPreferences !== v)  this._ctlUserPreferences = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Profile.Profile.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlUserPreferences) this._ctlUserPreferences.addSaveEditComplete(Function.createDelegate(this, this.ctlUserPreferences_SaveEditComplete));
		Rebound.GlobalTrader.Site.Pages.Profile.Profile.callBaseMethod(this, "goInit");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlLeft_RecentlyViewed) this._ctlLeft_RecentlyViewed.dispose();
		if (this._ctlUserPreferences) this._ctlUserPreferences.dispose();
		this._ctlLeft_RecentlyViewed = null;
		this._ctlUserPreferences = null;
		Rebound.GlobalTrader.Site.Pages.Profile.Profile.callBaseMethod(this, "dispose");
	},
	
	ctlUserPreferences_SaveEditComplete: function() {
		this._ctlLeft_RecentlyViewed.refresh();
	}
	
};

Rebound.GlobalTrader.Site.Pages.Profile.Profile.registerClass("Rebound.GlobalTrader.Site.Pages.Profile.Profile", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
