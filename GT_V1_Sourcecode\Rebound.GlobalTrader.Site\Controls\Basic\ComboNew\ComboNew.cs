using System;
using System.Data;
using System.Web;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.ComponentModel;
using System.Web.UI.WebControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls {

	[DefaultProperty("")]
	[ToolboxData("<{0}:DesignBase runat=server></{0}:DesignBase>")]
	public class ComboNew : Panel, IScriptControl {

		#region Locals

		protected ReboundTextBox _txt;
		protected ScriptManager _sm;

		#endregion

		#region Properties

		public AutoSearch.Base AutoSearchControl { get; set; }

		private string _strAutoSearchControlType = "";
		public string AutoSearchControlType {
			get { return _strAutoSearchControlType; }
			set { _strAutoSearchControlType = value; }
		}

		private string _strAutoSearchControlAssembly = "";
		public string AutoSearchControlAssembly {
			get { return _strAutoSearchControlAssembly; }
			set { _strAutoSearchControlAssembly = value; }
		}

		private Unit _untPanelHeight = Unit.Pixel(100);
		public Unit PanelHeight {
			get { return _untPanelHeight; }
			set { _untPanelHeight = value; }
		}

		private Unit _untPanelWidth = Unit.Pixel(250);
		public Unit PanelWidth {
			get { return _untPanelWidth; }
			set { _untPanelWidth = value; }
		}

        private string _strParentID = "";
        public string ParentControlID
        {
            get { return _strParentID; }
            set { _strParentID = value; }
        }
		#endregion

		#region Overrides

		protected override void CreateChildControls() {
			base.CreateChildControls();

			//textbox
			_txt = new ReboundTextBox();
            _txt.ID = _strParentID + "txt";
			_txt.Width = _untPanelWidth;
			Controls.Add(_txt);

			//autosearch
			if (AutoSearchControl == null) {
				if (_strAutoSearchControlType != "") {
					AutoSearchControl = AutoSearchManager.GetAutoSearch(_strAutoSearchControlAssembly, _strAutoSearchControlType);
				} else {
					throw new HttpException(string.Format("Combo '{0}' has neither AutoSearchControlType set in declarative code or AutoSearchControl set in C# code", ID));
				}
			}
            AutoSearchControl.ID = _strParentID + "aut";
			AutoSearchControl.Width = _untPanelWidth;
			AutoSearchControl.ResultsHeight = _untPanelHeight;
			AutoSearchControl.RelatedTextBoxID = _txt.ID;
			AutoSearchControl.ResultsActionType = Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.AutoSearchResultsActionType.RaiseEvent;
			Controls.Add(AutoSearchControl);

		}

		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		/// <summary>
		/// external call to ensure child controls
		/// </summary>
		public void MakeChildControls() {
			EnsureChildControls();
		}

		#region IScriptControl Members

		public IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor sd = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ComboNew", this.ClientID);
			sd.AddElementProperty("txt", _txt.ClientID);
			sd.AddComponentProperty("aut", AutoSearchControl.ClientID);
			return new ScriptDescriptor[] { sd };
		}

		public IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			ScriptReference sr = Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.ComboNew.ComboNew.js", true);
			return new ScriptReference[] { sr };
		}

		#endregion


	}

}