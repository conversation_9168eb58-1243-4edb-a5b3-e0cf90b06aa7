///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Salesperson filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
/*
 Marker     ChangedBy       Date            Remarks
 [001]      A<PERSON><PERSON>     17-Aug-2018     Provision to add Global Security in Sales Order
 */
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.prototype = {
    get_intSalesPersonID: function () { return this._intSalesPersonID; }, set_intSalesPersonID: function (value) { if (this._intSalesPersonID !== value) this._intSalesPersonID = value; },
    //[001] start
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    //[001] end
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (v) { if (this._IsGSA !== v) this._IsGSA = v; },
    get_ibtnExportCSV: function () { return this._ibtnExportCSV; }, set_ibtnExportCSV: function (v) { if (this._ibtnExportCSV !== v) this._ibtnExportCSV = v; },
    get_ibtnViewTask: function () { return this._ibtnViewTask; }, set_ibtnViewTask: function (v) { if (this._ibtnViewTask !== v) this._ibtnViewTask = v; },

    initialize: function () {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/SalesOrders";
        this._strDataObject = "SalesOrders";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.callBaseMethod(this, "initialize");
        if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.exportCSV));
        if (this._ibtnViewTask) $R_IBTN.addClick(this._ibtnViewTask, Function.createDelegate(this, this.viewTask));
        this._frmAdd = $find(this._aryFormIDs[0]);
        this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
        this._frmAdd.addSaveComplete(Function.createDelegate(this, this.addComplete));
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.applySalesPersonFilter();
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intSalesPersonID = null;
        //[001] start
        this._IsGlobalLogin = null;
        this._IsGSA = null;
        //[001] end
        if (this._frmAdd) this._frmAdd.dispose();
        this._frmAdd = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        //[001] start
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
        //[001] end
    },

    getDataOK: function () {
        var chkdatestatus = '';
        this._sortIndex = this._objResult.SortIndex;
        this._sortDir = this._objResult.SortDir;
        this._pageIndex = this._objResult.PageIndex;
        this._pageSize = this._objResult.PageSize;
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            chkdatestatus = '';
            var row = this._objResult.Results[i];
            if (row.DatePromisedStatus == 'Green')
                chkdatestatus = 'green';
            else if (row.DatePromisedStatus == 'Amber')
                chkdatestatus = '#FFBF00';
            else if (row.DatePromisedStatus == 'Red')
                chkdatestatus = 'Red';
            else if (row.DatePromisedStatus == 'White') 
                chkdatestatus = 'White';
            else
                chkdatestatus = 'White';

            var addTaskHtml = String.format("<a href=\"javascript:void(0);\" title='Add task' onclick=\"$find('{0}').showAddForm({1},'{2}','{3}');\">" + "Add Task" + "</a>",
                this._element.id, row.ID, row.No, $R_FN.setCleanTextValue(row.CM)
            ) + "&nbsp;&nbsp;&nbsp;";
            var viewTaskHtml = String.format("<a href=\"javascript:void(0);\" title='View task' onclick=\"$find('{0}').redirectToDetails('{1}');\" style=\"color: {2};\">" + (row.TaskCount + " Task") + "</a>",
                this._element.id, row.No, row.HasUnFinishedTask ? 'red' : '');

            var aryData = [
				$RGT_nubButton_SalesOrder(row.ID, row.No)
				, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustPONO))
				, $R_FN.writeTriCellValue(row.Quantity, row.QuantityShipped, row.QuantityInStock)
                , $R_FN.writeDoubleCellValue(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM) + '</span>':$RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
				, $R_FN.setCleanTextValue(row.DateOrdered)
				//, $R_FN.setCleanTextValue(row.DatePromised)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.DatePromised), chkdatestatus == 'White' ? "<span style='background-color:" + chkdatestatus + "!important;float: right;margin-top: -17px;height: 20px;width: 20px;visibility: hidden;'></span>" : "<span style='background-color:" + chkdatestatus + "!important;float: right;margin-top: -17px;height: 20px;width: 20px;'></span>")
				, $R_FN.setCleanTextValue(row.Status)
                , $R_FN.setCleanTextValue(row.ContractNo)
                , (addTaskHtml + viewTaskHtml)
		    ];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function() {
        this.getFilterField("ctlSalesman").show(this._enmViewLevel != 0);
        //[001] start
        this.getFilterField("ctlClientName").show((this._IsGlobalLogin || this._IsGSA));
        //[001] end
    },
    applySalesPersonFilter: function() {
        if ((this._intSalesPersonID) && this._intSalesPersonID > 0)
            this.getFilterField("ctlSalesman").setValue(this._intSalesPersonID);
    },

    exportCSV: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("ExportToCSV");
        obj._intTimeoutMilliseconds = 500 * 1000;
        obj.addParameter("ViewLevel", this._enmViewLevel);
        obj.addParameter("SortIndex", this._sortIndex);
        obj.addParameter("SortDir", this._sortDir);
        obj.addParameter("PageIndex", this._pageIndex);
        obj.addParameter("PageSize", this._pageSize);

        obj.addParameter("SONoLo", this.getFilterFieldValue_Min("ctlSONo"));
        obj.addParameter("SONoHi", this.getFilterFieldValue_Max("ctlSONo"));
        obj.addParameter("Part", this.getFilterFieldValue("ctlPart"));
        obj.addParameter("RecentOnly", this.getFilterFieldValue("ctlRecentOnly"));
        obj.addParameter("IncludeClosed", this.getFilterFieldValue("ctlIncludeClosed"));
        obj.addParameter("CMName", this.getFilterFieldValue("ctlCompanyName"));
        obj.addParameter("Contact", this.getFilterFieldValue("ctlContactName"));
        obj.addParameter("Country", this.getFilterFieldValue("ctlCountry"));
        obj.addParameter("SOCheckedStatus", this.getFilterFieldValue("ctlCheckedStatus"));
        obj.addParameter("SalesOrderStatus", this.getFilterFieldValue("ctlStatus"));
        obj.addParameter("Salesman", this.getFilterFieldValue("ctlSalesman"));
        obj.addParameter("CustPO", this.getFilterFieldValue("ctlCustomerPO"));
        obj.addParameter("DateOrderedFrom", this.getFilterFieldValue("ctlDateOrderedFrom"));
        obj.addParameter("DateOrderedTo", this.getFilterFieldValue("ctlDateOrderedTo"));
        obj.addParameter("DatePromisedFrom", this.getFilterFieldValue("ctlDatePromisedFrom"));
        obj.addParameter("DatePromisedTo", this.getFilterFieldValue("ctlDatePromisedTo"));
        obj.addParameter("ContractNo", this.getFilterFieldValue("ctlContractNo"));
        obj.addParameter("IncludeOrderSent", this.getFilterFieldValue("ctlIncludeSentOrder"));
        obj.addParameter("IsGlobalLogin", this._IsGlobalLogin);
        obj.addParameter("IsGSA", this._IsGSA);
        obj.addParameter("Client", this.getFilterFieldValue("ctlClientName"));
        obj.addParameter("AS6081", this.getFilterFieldValue("ctlAS6081"));

        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    viewTask: function () {
        location.href = ('Prf_ToDo.aspx?Category=4'), '_blank';
    },
    showAddForm: function (Id, salesOrderNumber, CustomerName) {
        this._frmAdd.setFormFieldsToDefaults();
        this._frmAdd.setFieldValue("ctlDueTime", "09:00");
        this._frmAdd.setFieldValue("ctlReminderTime", "09:00");

        this._frmAdd.setFieldValue("ctlSalesOrder", Id, null, salesOrderNumber);
        this._frmAdd._intCategoryID = 4 //SalesOrder
        this._frmAdd._customerName = CustomerName;
        this.showForm(this._frmAdd, true);
    },
    hideAddForm: function () {
        this._frmAdd.resetFormData();
        this.showForm(this._frmAdd, false);
    },

    addComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    redirectToDetails: function (number) {
        location.href = ('Prf_ToDo.aspx?so=' + number + '&Category=4'), '_blank';
    }
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
