﻿using Rebound.GlobalTrader;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Rebound.GlobalTrader.DAL;


namespace Rebound.GlobalTrader.BLL
{
	public partial class RevenueTarget : BizObject
	{

        #region Properties

        protected static RevenueTargetElement Settings
        {
            get { return Globals.Settings.RevenueTargets; }
        }
		/// <summary>
		/// SectionId means division team sales or customer id
		/// </summary>
		public System.Int32 SectionId { get; set; }
		/// <summary>
		/// SectionName means name of section like Division Team Sales or Customer
		/// </summary>
        public string SectionName { get; set; }
		/// <summary>
		/// Division Team Sales Customer
		/// </summary>
		public string SectionType { get; set; }
		public System.Double? JanTarget { get; set; }
		public System.Double? FebTarget { get; set; }
		public System.Double? MarchTarget { get; set; }
		public System.Double? AprTarget { get; set; }
		public System.Double? MayTarget { get; set; }
		public System.Double? JuneTarget { get; set; }
		public System.Double? JulyTarget { get; set; }
		public System.Double? AugTarget { get; set; }
		public System.Double? SepTarget { get; set; }
		public System.Double? OctTarget { get; set; }
		public System.Double? NovTarget { get; set; }
		public System.Double? DecTarget { get; set; }
		public System.Double? JanRevenue { get; set; }
		public System.Double? FebRevenue { get; set; }
		public System.Double? MarchRevenue { get; set; }
		public System.Double? AprRevenue { get; set; }
		public System.Double? MayRevenue { get; set; }
		public System.Double? JuneRevenue { get; set; }
		public System.Double? JulyRevenue { get; set; }
		public System.Double? AugRevenue { get; set; }
		public System.Double? SepRevenue { get; set; }
		public System.Double? OctRevenue { get; set; }
		public System.Double? NovRevenue { get; set; }
		public System.Double? DecRevenue { get; set; }
		public System.Double? AllocatedPer { get; set; }
		public System.Double? RevenuePer { get; set; }

		public System.Double? JanGrossProfit { get; set; }
		public System.Double? FebGrossProfit { get; set; }
		public System.Double? MarchGrossProfit { get; set; }
		public System.Double? AprGrossProfit { get; set; }
		public System.Double? MayGrossProfit { get; set; }
		public System.Double? JuneGrossProfit { get; set; }
		public System.Double? JulyGrossProfit { get; set; }
		public System.Double? AugGrossProfit { get; set; }
		public System.Double? SepGrossProfit { get; set; }
		public System.Double? OctGrossProfit { get; set; }
		public System.Double? NovGrossProfit { get; set; }
		public System.Double? DecGrossProfit { get; set; }

		public System.Double? TotalTarget { get; set; }
		public System.Double? TotalRevenue { get; set; }
		public System.Double? TotalGrossProfit { get; set; }


		public System.Int32 RowId { get; set; }
        public System.Int32 RecordCount { get; set; }



        #endregion

        #region Methods

        /// <summary>
        /// KPI_GetDivisionRevenueTargetByID
        /// </summary>
        /// <param name="divisionNo"></param>
        /// <param name="yearNo"></param>
        /// <returns></returns>
        public static List<RevenueTarget> GetDivisionRevenueByID(System.Int32? divisionNo,System.Int32 yearNo)
		{
			List<RevenueTargetDetails> lstDetails = SiteProvider.RevenueTarget.GetDivisionRevenueByID(divisionNo, yearNo);
			if (lstDetails == null)
			{
				return new List<RevenueTarget>();
			}
			else
			{
				List<RevenueTarget> lst = new List<RevenueTarget>();
				foreach (RevenueTargetDetails objDetails in lstDetails)
				{
					BLL.RevenueTarget obj = new BLL.RevenueTarget();
					obj.SectionId = objDetails.SectionId;
					obj.SectionName = objDetails.SectionName;
					obj.SectionType = objDetails.SectionType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.JanRevenue = objDetails.JanRevenue;
					obj.FebRevenue = objDetails.FebRevenue;
					obj.MarchRevenue = objDetails.MarchRevenue;
					obj.AprRevenue = objDetails.AprRevenue;
					obj.MayRevenue = objDetails.MayRevenue;
					obj.JuneRevenue = objDetails.JuneRevenue;
					obj.JulyRevenue = objDetails.JulyRevenue;
					obj.AugRevenue = objDetails.AugRevenue;
					obj.SepRevenue = objDetails.SepRevenue;
					obj.OctRevenue = objDetails.OctRevenue;
					obj.NovRevenue = objDetails.NovRevenue;
					obj.DecRevenue = objDetails.DecRevenue;

					obj.JanGrossProfit = objDetails.JanGrossProfit;
					obj.FebGrossProfit = objDetails.FebGrossProfit;
					obj.MarchGrossProfit = objDetails.MarchGrossProfit;
					obj.AprGrossProfit = objDetails.AprGrossProfit;
					obj.MayGrossProfit = objDetails.MayGrossProfit;
					obj.JuneGrossProfit = objDetails.JuneGrossProfit;
					obj.JulyGrossProfit = objDetails.JulyGrossProfit;
					obj.AugGrossProfit = objDetails.AugGrossProfit;
					obj.SepGrossProfit = objDetails.SepGrossProfit;
					obj.OctGrossProfit = objDetails.OctGrossProfit;
					obj.NovGrossProfit = objDetails.NovGrossProfit;
					obj.DecGrossProfit = objDetails.DecGrossProfit;

					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.TotalRevenue = objDetails.TotalRevenue;
					obj.TotalGrossProfit = objDetails.TotalGrossProfit;

					obj.RevenuePer = objDetails.RevenuePer;
					obj.RowId = objDetails.RowId;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}


		/// <summary>
		/// KPI_GetSalesRevenueTargetByID
		/// </summary>
		/// <param name="divisionNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public static List<RevenueTarget> GetSalesRevenueByID(System.Int32? teamTargetNo, System.Int32 yearNo)
		{
			List<RevenueTargetDetails> lstDetails = SiteProvider.RevenueTarget.GetSalesRevenueByID(teamTargetNo, yearNo);
			if (lstDetails == null)
			{
				return new List<RevenueTarget>();
			}
			else
			{
				List<RevenueTarget> lst = new List<RevenueTarget>();
				foreach (RevenueTargetDetails objDetails in lstDetails)
				{
					BLL.RevenueTarget obj = new BLL.RevenueTarget();
					obj.SectionId = objDetails.SectionId;
					obj.SectionName = objDetails.SectionName;
					obj.SectionType = objDetails.SectionType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.JanRevenue = objDetails.JanRevenue;
					obj.FebRevenue = objDetails.FebRevenue;
					obj.MarchRevenue = objDetails.MarchRevenue;
					obj.AprRevenue = objDetails.AprRevenue;
					obj.MayRevenue = objDetails.MayRevenue;
					obj.JuneRevenue = objDetails.JuneRevenue;
					obj.JulyRevenue = objDetails.JulyRevenue;
					obj.AugRevenue = objDetails.AugRevenue;
					obj.SepRevenue = objDetails.SepRevenue;
					obj.OctRevenue = objDetails.OctRevenue;
					obj.NovRevenue = objDetails.NovRevenue;
					obj.DecRevenue = objDetails.DecRevenue;
					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.TotalRevenue = objDetails.TotalRevenue;

					obj.JanGrossProfit = objDetails.JanGrossProfit;
					obj.FebGrossProfit = objDetails.FebGrossProfit;
					obj.MarchGrossProfit = objDetails.MarchGrossProfit;
					obj.AprGrossProfit = objDetails.AprGrossProfit;
					obj.MayGrossProfit = objDetails.MayGrossProfit;
					obj.JuneGrossProfit = objDetails.JuneGrossProfit;
					obj.JulyGrossProfit = objDetails.JulyGrossProfit;
					obj.AugGrossProfit = objDetails.AugGrossProfit;
					obj.SepGrossProfit = objDetails.SepGrossProfit;
					obj.OctGrossProfit = objDetails.OctGrossProfit;
					obj.NovGrossProfit = objDetails.NovGrossProfit;
					obj.DecGrossProfit = objDetails.DecGrossProfit;
					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.TotalGrossProfit = objDetails.TotalGrossProfit;

					obj.RevenuePer = objDetails.RevenuePer;
					obj.RowId = objDetails.RowId;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// KPI_GetCustomerRevenueTargetByID
		/// </summary>
		/// <param name="divisionNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public static List<RevenueTarget> GetCustomerRevenueByID(System.Int32? salesTargetNo, System.Int32 yearNo)
		{
			List<RevenueTargetDetails> lstDetails = SiteProvider.RevenueTarget.GetCustomerRevenueByID(salesTargetNo, yearNo);
			if (lstDetails == null)
			{
				return new List<RevenueTarget>();
			}
			else
			{
				List<RevenueTarget> lst = new List<RevenueTarget>();
				foreach (RevenueTargetDetails objDetails in lstDetails)
				{
					BLL.RevenueTarget obj = new BLL.RevenueTarget();
					obj.SectionId = objDetails.SectionId;
					obj.SectionName = objDetails.SectionName;
					obj.SectionType = objDetails.SectionType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.JanRevenue = objDetails.JanRevenue;
					obj.FebRevenue = objDetails.FebRevenue;
					obj.MarchRevenue = objDetails.MarchRevenue;
					obj.AprRevenue = objDetails.AprRevenue;
					obj.MayRevenue = objDetails.MayRevenue;
					obj.JuneRevenue = objDetails.JuneRevenue;
					obj.JulyRevenue = objDetails.JulyRevenue;
					obj.AugRevenue = objDetails.AugRevenue;
					obj.SepRevenue = objDetails.SepRevenue;
					obj.OctRevenue = objDetails.OctRevenue;
					obj.NovRevenue = objDetails.NovRevenue;
					obj.DecRevenue = objDetails.DecRevenue;

					obj.JanGrossProfit = objDetails.JanGrossProfit;
					obj.FebGrossProfit = objDetails.FebGrossProfit;
					obj.MarchGrossProfit = objDetails.MarchGrossProfit;
					obj.AprGrossProfit = objDetails.AprGrossProfit;
					obj.MayGrossProfit = objDetails.MayGrossProfit;
					obj.JuneGrossProfit = objDetails.JuneGrossProfit;
					obj.JulyGrossProfit = objDetails.JulyGrossProfit;
					obj.AugGrossProfit = objDetails.AugGrossProfit;
					obj.SepGrossProfit = objDetails.SepGrossProfit;
					obj.OctGrossProfit = objDetails.OctGrossProfit;
					obj.NovGrossProfit = objDetails.NovGrossProfit;
					obj.DecGrossProfit = objDetails.DecGrossProfit;

					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.TotalRevenue = objDetails.TotalRevenue;
					obj.RevenuePer = objDetails.RevenuePer;
					obj.RowId = objDetails.RowId;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// [KPI_GetSalesRevenueTargetByTeamID]
		/// </summary>
		/// <param name="teamNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public static List<RevenueTarget> GetSalesRevenueTargetByTeamID(System.Int32? teamNo, System.Int32 yearNo)
		{
			List<RevenueTargetDetails> lstDetails = SiteProvider.RevenueTarget.GetSalesRevenueTargetByTeamID(teamNo, yearNo);
			if (lstDetails == null)
			{
				return new List<RevenueTarget>();
			}
			else
			{
				List<RevenueTarget> lst = new List<RevenueTarget>();
				foreach (RevenueTargetDetails objDetails in lstDetails)
				{
					BLL.RevenueTarget obj = new BLL.RevenueTarget();
					obj.SectionId = objDetails.SectionId;
					obj.SectionName = objDetails.SectionName;
					obj.SectionType = objDetails.SectionType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.JanRevenue = objDetails.JanRevenue;
					obj.FebRevenue = objDetails.FebRevenue;
					obj.MarchRevenue = objDetails.MarchRevenue;
					obj.AprRevenue = objDetails.AprRevenue;
					obj.MayRevenue = objDetails.MayRevenue;
					obj.JuneRevenue = objDetails.JuneRevenue;
					obj.JulyRevenue = objDetails.JulyRevenue;
					obj.AugRevenue = objDetails.AugRevenue;
					obj.SepRevenue = objDetails.SepRevenue;
					obj.OctRevenue = objDetails.OctRevenue;
					obj.NovRevenue = objDetails.NovRevenue;
					obj.DecRevenue = objDetails.DecRevenue;
					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.TotalRevenue = objDetails.TotalRevenue;
					obj.RevenuePer = objDetails.RevenuePer;
					obj.RowId = objDetails.RowId;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// [KPI_GetCustomerRevenueTargetBySalesManNo]
		/// </summary>
		/// <param name="salemanNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public static List<RevenueTarget> GetSalesRevenueTargetBySalesID(System.Int32? salemanNo, System.Int32 yearNo)
		{
			List<RevenueTargetDetails> lstDetails = SiteProvider.RevenueTarget.GetSalesRevenueTargetBySalesID(salemanNo, yearNo);
			if (lstDetails == null)
			{
				return new List<RevenueTarget>();
			}
			else
			{
				List<RevenueTarget> lst = new List<RevenueTarget>();
				foreach (RevenueTargetDetails objDetails in lstDetails)
				{
					BLL.RevenueTarget obj = new BLL.RevenueTarget();
					obj.SectionId = objDetails.SectionId;
					obj.SectionName = objDetails.SectionName;
					obj.SectionType = objDetails.SectionType;
					obj.JanTarget = objDetails.JanTarget;
					obj.FebTarget = objDetails.FebTarget;
					obj.MarchTarget = objDetails.MarchTarget;
					obj.AprTarget = objDetails.AprTarget;
					obj.MayTarget = objDetails.MayTarget;
					obj.JuneTarget = objDetails.JuneTarget;
					obj.JulyTarget = objDetails.JulyTarget;
					obj.AugTarget = objDetails.AugTarget;
					obj.SepTarget = objDetails.SepTarget;
					obj.OctTarget = objDetails.OctTarget;
					obj.NovTarget = objDetails.NovTarget;
					obj.DecTarget = objDetails.DecTarget;
					obj.JanRevenue = objDetails.JanRevenue;
					obj.FebRevenue = objDetails.FebRevenue;
					obj.MarchRevenue = objDetails.MarchRevenue;
					obj.AprRevenue = objDetails.AprRevenue;
					obj.MayRevenue = objDetails.MayRevenue;
					obj.JuneRevenue = objDetails.JuneRevenue;
					obj.JulyRevenue = objDetails.JulyRevenue;
					obj.AugRevenue = objDetails.AugRevenue;
					obj.SepRevenue = objDetails.SepRevenue;
					obj.OctRevenue = objDetails.OctRevenue;
					obj.NovRevenue = objDetails.NovRevenue;
					obj.DecRevenue = objDetails.DecRevenue;
					obj.TotalTarget = objDetails.TotalTarget;
					obj.AllocatedPer = objDetails.AllocatedPer;
					obj.TotalRevenue = objDetails.TotalRevenue;
					obj.RevenuePer = objDetails.RevenuePer;
					obj.RowId = objDetails.RowId;
                    obj.RecordCount = objDetails.RecordCount;
                    lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// Update
		/// Calls [usp_update_DivisionTeamTarget]
		/// </summary>
		public static bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue,System.Int32? updatedBy,System.Int32? Year, System.Int32? divisionNo)
		{
			return SiteProvider.DivisionTarget.Update(rowId, rowType, columnName, targetValue, updatedBy, Year, divisionNo);
		}

		/// <summary>
		/// Update
		/// Calls [usp_saveAllDivisionTeamTarget]
		/// </summary>
		public static bool SaveAllDivisionTeamTarget(System.Int32? Year, System.Int32? divisionNo, System.Int32? updatedBy)
		{
			return SiteProvider.DivisionTarget.SaveAllDivisionTeamTarget(Year, divisionNo, updatedBy);
		}

		#endregion

	}
}