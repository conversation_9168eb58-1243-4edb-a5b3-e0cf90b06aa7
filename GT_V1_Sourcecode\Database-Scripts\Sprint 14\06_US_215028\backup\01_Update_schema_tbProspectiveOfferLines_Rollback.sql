﻿/*
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-215028]		Trung Pham			29-Oct-2024		CREATE			UPDATE SCHEMA
===========================================================================================
*/ 
IF (EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbProspectiveOfferLines'
	AND COLUMN_NAME IN ('Notes')))
	BEGIN
		ALTER TABLE tbProspectiveOfferLines
		DROP COLUMN Notes
	END
IF (EXISTS (SELECT 1 FROM [BorisGlobalTraderimports].INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbProspectiveOffer_ToBeImported'
	AND COLUMN_NAME IN ('Notes')))
	BEGIN
		ALTER TABLE [BorisGlobalTraderimports].dbo.tbProspectiveOffer_ToBeImported
		DROP COLUMN Notes
	END

IF EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbProspectiveOfferLines' 
    AND COLUMN_NAME = 'FullPart'
)
BEGIN
    ALTER TABLE tbProspectiveOfferLines
    DROP COLUMN FullPart; 
END