﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210625]			Phuc Hoang			09-Aug-2024		CREATE          Sanctioned manufacturers need to be highlighted in red on the SOR PDF and SO screens.
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_SalesOrderLine]               
@SalesOrderLineId int                                                                        
AS                                                                       
  --[001]                          
Declare @SOLineNo int=0                          
Declare @WareHouseNo int=0                
--for Stock detials display               
Declare @StockNo int=0                
--for Stock detials display                        
select top 1 @SOLineNo = salesorderlineid from tbsalesorderLine where SalesOrderLineId = @SalesOrderLineId and Posted=1                      
--for Stock detials display                  
select top 1 @StockNo=sk.StockId, @WareHouseNo = sk.warehouseno from  dbo.tbAllocation al JOIN  dbo.tbStock sk ON sk.StockId = al.StockNo                           
--for Stock detials display              
where al.SalesOrderLineNo= @SOLineNo                                    
declare @LocalCurrencyNo INT=0                          
select @LocalCurrencyNo = LocalCurrencyNo from tbwarehouse where WarehouseId = @WareHouseNo                              
--[001]                             
declare @IsIPOCreated int, @IsIPOOpen int, @SourcingResultNo INT, @FirstIPOSalesOrderNo INT, @CurrSONo int, @SourcingResultUsedByOther bit, @ClonedID int,@SOSerialNo int,                            
@CustomerRequirementId INT = NULL,@CustomerRequirementNumber INT = NULL                            
SELECT  @CustomerRequirementId=cust.CustomerRequirementId,@CustomerRequirementNumber=cust.CustomerRequirementNumber                             
FROM  dbo.tbSalesOrderLine AS sol LEFT JOIN                             
   dbo.tbCustomerRequirement cust  ON cust.CustomerRequirementId = sol.DocNo                            
   WHERE SOL.SalesOrderLineId = @SalesOrderLineId AND DocType = 'REQ'                            
                              
--select @IsIPOAttached = count(*) from vwAllocation_Inward where SalesOrderLineNo = @SalesOrderLineId and InternalPurchaseOrderId is not null                              
                            
SET @SourcingResultUsedByOther = 0                            
SET @IsIPOOpen = 0                            
                            
SELECT @SourcingResultNo =  SourcingResultNo,@CurrSONo = SalesOrderNo, @ClonedID= ISNULL(ClonedID,0) FROM tbSalesOrderLine where SalesOrderLineId = @SalesOrderLineId                            
SELECT @SOSerialNo= SOSerialNo FROM tbSalesOrderLine where SalesOrderLineId = @ClonedID                            
IF @SourcingResultNo IS NOT NULL                            
BEGIN                            
 SELECT TOP 1 @FirstIPOSalesOrderNo = ipo.SalesOrderNo FROM tbInternalPurchaseOrderLine ipol join tbInternalPurchaseOrder ipo on                             
 ipol.InternalPurchaseOrderNo = ipo.InternalPurchaseOrderId where SourcingResultNo = @SourcingResultNo                            
                            
 IF @FirstIPOSalesOrderNo < >  @CurrSONo                            
 BEGIN                            
   SET @SourcingResultUsedByOther = 1                            
 END                            
                            
    SELECT @IsIPOOpen = COUNT(1)                              
 FROM    tbAllocation al                                      
 LEFT JOIN tbStock sk ON al.StockNo = sk.StockId                                      
 LEFT JOIN tbPurchaseOrderLine pol ON sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId                                      
 LEFT JOIN tbPurchaseOrder po ON pol.PurchaseOrderNo = po.PurchaseOrderId                                      
 LEFT JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo                                     
 LEFT JOIN tbInternalPurchaseOrder ipo ON po.PurchaseOrderId = ipo.PurchaseOrderNo                                    
 WHERE   al.SupplierRMALineNo IS NULL  and ipol.SourcingResultNo = isnull(@SourcingResultNo,0)   and po.ApprovedBy is null and isnull(pol.Closed,0) = 0                             
                            
 SELECT @IsIPOCreated = COUNT(1)                              
 from tbInternalPurchaseOrderLine where SourcingResultNo = isnull(@SourcingResultNo,0)                               
 --and po.ApprovedBy is null and isnull(pol.Closed,0) = 0                            
                            
  -- set  @IsIPOAttached = @IsIPOPOExist                
END                            
                              
                            
                              
SELECT so.* ,                
--(                                
--select isnull(po.ApprovedBy,0)  from tbPurchaseOrder po join  tbInternalPurchaseOrder ipo on po.InternalPurchaseOrderNo=ipo.InternalPurchaseOrderId                                
--  join tbSalesOrder so  on ipo.SalesOrderNo=so.SalesOrderId join tbSalesOrderLine sol on so.SalesOrderId=sol.SalesOrderNo where sol.SalesOrderLineId= @SalesOrderLineId) as IPOApprovedBy                           
                                
0 as IPOApprovedBy                              
--,ipo.InternalPurchaseOrderLineId                              
, case when @IsIPOCreated > 0 then 1 else 0 end as IsIPOCreated                            
, case when @IsIPOOpen > 0 then 1 else 0 end as IsIPOAndPOOpen                            
, @SourcingResultUsedByOther AS SourcingResultUsedByOther                            
, @SOSerialNo AS SOSerialNumber        , dbo.ufn_get_productdutyrate(ProductNo,getdate()) as ProductDutyRate  --- to get Rate %  on form Sales Order                            
, @CustomerRequirementId AS CustomerRequirementId                             
, @CustomerRequirementNumber AS CustomerRequirementNumber                            
, ps.Name as ProductSourceName                            
--, c.WarehouseNo                           
, g.GlobalCurrencyName                          
,iSNULL(@LocalCurrencyNo,0) as LocalCurrencyid                          
,cast(dbo.ufn_GetECCNMessage(so.ECCNCode,so.ClientNo)as nvarchar(900))as IHSECCNCodeDefination                        
--,so.EI_Required As EI_RequiredId                      
--,so.EvidenceNotes                      
--,so.TestingType  as TestingTypeId                      
--,so.EI_RequiredName                      
--,so.TypeName                     
--,so.ISEIToken                    
--,ISNULL(bdi.ISBooked,0) AS IsEIBooked                
--,ISNULL(bdi.BookingStatusNo,0) AS EIStatus              
--for Stock detials display                 
,cast(dbo.ufn_GetStockAvailableDetail(so.Part,so.ClientNo,null)as nvarchar(900))as StockAvailableDetail                  
 --for Stock detials display              
,so.BlankECCNCode          
FROM  dbo.vwSalesOrderLine so                             
left join tbProductSource ps on isnull(so.ProductSource,0) = ps.ProductSourceId                            
left join tbGlobalCurrencyList g on g.GlobalCurrencyId=so.GlobalCurrencyNo                        
--left join tbEI_BookingDetailsInfo bdi on so.SalesOrderLineId=bdi.SOLineNo                    
--left join dbo.tbcompany c on c.CompanyId = so.CompanyNo                           
                            
WHERE SalesOrderLineId = @SalesOrderLineId 
---------------------RP-31 ends----

---------------------RP-57---Start------------
GO
