using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using System.Text;
using System.Xml;

namespace Rebound.GlobalTrader.Site {
	public class AddressManager {

		internal static string ToLongString(string strName, string strLine1, string strLine2, string strLine3, string strCity, string strCounty, string strState, string strZIP, string strCountry, string strSeparator) {
			Address ad = new Address();
			ad.AddressName = strName;
			ad.Line1 = strLine1;
			ad.Line2 = strLine2;
			ad.Line3 = strLine3;
			ad.County = strCounty;
			ad.City = strCity;
			ad.State = strState;
			ad.CountryName = strCountry;
			ad.ZIP = strZIP;
			string str = ToLongString(ad, strSeparator);
			ad = null;
			return str;
		}

		internal static string ToLongString(int? intAddressNo) {
			return ToLongString(intAddressNo, ", ");
		}

		internal static string ToLongString(int? intAddressNo, string strSeparator) {
			Address ad = Address.Get(intAddressNo);
			string str = "";
			if (ad != null) str = ToLongString(ad, strSeparator, false);
			ad = null;
			return str;
		}

		internal static string ToLongString(string strName, string strLine1, string strLine2, string strLine3, string strCity, string strCounty, string strState, string strZIP, string strCountry) {
			return ToLongString(strName, strLine1, strLine2, strLine3, strCity, strCounty, strState, strZIP, strCountry, ", ");
		}

		internal static string ToLongString(Address ad) {
			return ToLongString(ad, ", ", false);
		}

		internal static string ToLongString(Address ad, string strSeparator) {
			return ToLongString(ad, strSeparator, false);
		}

		internal static string ToLongString(Address ad, bool blnExcludeName) {
			return ToLongString(ad, ", ", blnExcludeName);
		}

		internal static string ToLongString(Address ad, string strSeparator, bool blnExcludeName) {
			if (ad == null) return "";
			StringBuilder sb = new StringBuilder();
			if (!blnExcludeName && !String.IsNullOrEmpty(ad.AddressName)) {
				sb.Append(ad.AddressName.Trim());
			}
			if (!String.IsNullOrEmpty(ad.Line1)) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(ad.Line1.Trim());
			}
			if (!String.IsNullOrEmpty(ad.Line2)) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(ad.Line2.Trim());
			}
			if (!String.IsNullOrEmpty(ad.Line3)) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(ad.Line3.Trim());
			}
			if (!String.IsNullOrEmpty(ad.City)) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(ad.City.Trim());
			}
			if (!String.IsNullOrEmpty(ad.County)) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(ad.County.Trim());
			}
			if (!String.IsNullOrEmpty(ad.State)) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(ad.State.Trim());
			}
			if (!String.IsNullOrEmpty(ad.ZIP)) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(ad.ZIP.Trim());
			}
			if (!String.IsNullOrEmpty(ad.CountryName)) {
				if (sb.Length > 0) sb.Append(strSeparator);
				sb.Append(ad.CountryName.Trim());
			}
            if (!String.IsNullOrEmpty(ad.WHShipToVATNo))
            {
                if (sb.Length > 0) sb.Append(strSeparator);
                sb.Append(ad.WHShipToVATNo.Trim());
            }
            return sb.ToString();
		}

		internal static string ShippingInstructions(int? intAddressNo) {
			string strOut = "";
			if (intAddressNo != null) {
				CompanyAddress cad = CompanyAddress.GetByAddress(intAddressNo);
				if (cad != null) strOut = cad.ShippingNotes;
				cad = null;
			}
			return strOut;
		}

		internal static void ToXMLVariablesForEmail(string strXMLElementName, Address ad, ref XmlWriter writer) {
			if (ad == null) return;
			if (!String.IsNullOrEmpty(ad.AddressName)) writer.WriteElementString(strXMLElementName, ad.AddressName.Trim());
			if (!String.IsNullOrEmpty(ad.Line1)) writer.WriteElementString(strXMLElementName, ad.Line1.Trim());
			if (!String.IsNullOrEmpty(ad.Line2)) writer.WriteElementString(strXMLElementName, ad.Line2.Trim());
			if (!String.IsNullOrEmpty(ad.Line3)) writer.WriteElementString(strXMLElementName, ad.Line3.Trim());
			if (!String.IsNullOrEmpty(ad.City)) writer.WriteElementString(strXMLElementName, ad.City.Trim());
			if (!String.IsNullOrEmpty(ad.County)) writer.WriteElementString(strXMLElementName, ad.County.Trim());
			if (!String.IsNullOrEmpty(ad.State)) writer.WriteElementString(strXMLElementName, ad.State.Trim());
			if (!String.IsNullOrEmpty(ad.ZIP)) writer.WriteElementString(strXMLElementName, ad.ZIP.Trim());
			if (!String.IsNullOrEmpty(ad.CountryName)) writer.WriteElementString(strXMLElementName, ad.CountryName.Trim());
		}

	}
}
