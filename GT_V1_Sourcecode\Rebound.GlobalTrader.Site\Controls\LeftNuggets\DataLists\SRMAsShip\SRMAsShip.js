Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.SRMAsShip=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.SRMAsShip.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.SRMAsShip.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this._strPathToData="controls/DataListNuggets/SRMAsShip";this._strDataObject="SRMAsShip";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.SRMAsShip.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.SRMAsShip.callBaseMethod(this,"dispose")},setupDataCall:function(){var n="GetData";this.getFilterValue("ViewLevel")==1&&(n+="_All");this._objData.set_DataAction(n)},getDataOK:function(){for(var n,t,i=0,r=this._objResult.Results.length;i<r;i++)n=this._objResult.Results[i],t=String.format('<a href="{0}"><b>{1}<\/b> - {2}',$RGT_gotoURL_ShipSRMA(n.ID),n.No,$R_FN.setCleanTextValue(n.CM)),n.Part.length>0&&(t+=String.format("<br />{0} x {1}<\/a>",n.Quantity,$R_FN.writePartNo(n.Part,n.ROHS))),t+="<\/a>",this._tbl.addRow([t],n.ID,!1),t=null,n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.SRMAsShip.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.SRMAsShip",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);