using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class EPRNotify_Notify : Base
    {

		#region Locals
        protected IconButton _ibtnSend;
        protected IconButton _ibtnSend_Footer;
		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "EPRNotify_Notify");
            AddScriptReference("Controls.Nuggets.EPRNotify.Notify.EPRNotify_Notify.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpButtons();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void WireUpButtons() {
            _ibtnSend = FindIconButton("ibtnSend");
            _ibtnSend_Footer = (IconButton)FindFooterIconButton("ibtnSend");
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intEPRID", _objQSManager.EPRId);
            _scScriptControlDescriptor.AddElementProperty("ibtnSend", _ibtnSend.ClientID);
            if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", _ibtnSend_Footer.ClientID);

            string strOut = string.Empty; 
            BLL.EPR epr = BLL.EPR.Get(_objQSManager.EPRId);
            if (epr != null)
            {
                strOut = MailTemplateManager.GetMessage_NotifyEPR(epr);
                _scScriptControlDescriptor.AddProperty("strMessageText", strOut);
                _scScriptControlDescriptor.AddProperty("strEPRNo", epr.PurchaseOrderNumber + "-" + epr.EPRId);
                _scScriptControlDescriptor.AddProperty("intPO", epr.PurchaseOrderId);
                _scScriptControlDescriptor.AddProperty("intPONumber", epr.PurchaseOrderNumber);
                //_scScriptControlDescriptor.AddProperty("intBuyerId", npr.BuyerId);
                //_scScriptControlDescriptor.AddProperty("strBuyerName", npr.BuyerName);
                //_scScriptControlDescriptor.AddProperty("intGoodsInLineId", npr.GoodsInLineId);
            }
            else
            {
                HttpContext.Current.Response.Redirect(_objSite.GetPage("NotFound").Url, true);
            }
           
		}

	}
}
