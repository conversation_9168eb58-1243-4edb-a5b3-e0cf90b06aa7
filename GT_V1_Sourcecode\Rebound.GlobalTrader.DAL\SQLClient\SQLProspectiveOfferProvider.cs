﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Linq;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlProspectiveOfferProvider : ProspectiveOfferProvider
    {
        public override List<ProspectiveOfferDetails> DataListNugget(int? teamId,
                                                                     int? divisionId,
                                                                     int? loginId,
                                                                     int? orderBy,
                                                                     int? sortDir,
                                                                     int? pageIndex,
                                                                     int? pageSize,
                                                                     string fileNameSearch,
                                                                     string partSearch,
                                                                     DateTime? dateUploadForm,
                                                                     DateTime? dateUploadTo,
                                                                     int? importedBy,
                                                                     string supplierSearch)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_ProspectiveOffer", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@FileNameSearch", SqlDbType.NVarChar).Value = fileNameSearch;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@DateUploadFrom", SqlDbType.DateTime).Value = dateUploadForm;
                cmd.Parameters.Add("@DateUploadTo", SqlDbType.DateTime).Value = dateUploadTo;
                cmd.Parameters.Add("@ImportedBy", SqlDbType.Int).Value = importedBy;
                cmd.Parameters.Add("@SupplierSearch", SqlDbType.NVarChar).Value = supplierSearch;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProspectiveOfferDetails> lst = new List<ProspectiveOfferDetails>();
                while (reader.Read())
                {
                    ProspectiveOfferDetails obj = new ProspectiveOfferDetails()
                    {
                        ProspectiveOfferId = GetReaderValue_Int32(reader, "ProspectiveOfferId", 0),
                        SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0),
                        SupplierName = GetReaderValue_String(reader, "SupplierName", ""),
                        SourceFileName = GetReaderValue_String(reader, "SourceFileName", ""),
                        ImportRowCount = GetReaderValue_Int32(reader, "ImportRowCount", 0),
                        ImportStatus = GetReaderValue_String(reader, "ImportStatus", ""),
                        ImportedBy = GetReaderValue_String(reader, "ImportedBy", ""),
                        ImportDate = GetReaderValue_DateTime(reader, "ImportDate", DateTime.MinValue),
                        RowCnt = GetReaderValue_Int32(reader, "RowCnt", 0)
                    };

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ProspectiveOffer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override ProspectiveOfferDetails GetStatus(int proId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_ProspectiveOffer_Status", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@ProId", SqlDbType.Int).Value = proId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                ProspectiveOfferDetails offer = new ProspectiveOfferDetails();
                while (reader.Read())
                {
                    offer = new ProspectiveOfferDetails()
                    {
                        ProspectiveOfferId = GetReaderValue_Int32(reader, "ProspectiveOfferId", 0),
                        SupplierId = GetReaderValue_Int32(reader, "SupplierId", 0),
                        SupplierName = GetReaderValue_String(reader, "SupplierName", ""),
                        SourceFileName = GetReaderValue_String(reader, "SourceFileName", ""),
                        ImportRowCount = GetReaderValue_Int32(reader, "ImportRowCount", 0),
                        ImportStatus = GetReaderValue_String(reader, "ImportStatus", ""),
                        ImportedBy = GetReaderValue_String(reader, "ImportedBy", ""),
                        ImportDate = GetReaderValue_DateTime(reader, "ImportDate", DateTime.MinValue),
                        RowCnt = GetReaderValue_Int32(reader, "RowCnt", 0)
                    };
                }
                return offer;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ProspectiveOffer Status", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// execute usp_Get_ProspectiveOffer_Lines_ByID
        /// </summary>
        /// <param name="prospectiveOfferLineId"></param>
        /// <returns></returns>
        public override ProspectiveOfferLinesOffer GetProspectiveOfferLineByID(int prospectiveOfferLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_ProspectiveOffer_Lines_ByID", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 120
                };
                cmd.Parameters.Add("@ProspectiveOfferLineId", SqlDbType.Int).Value = prospectiveOfferLineId;


                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);

                if (reader.Read())
                {
                    ProspectiveOfferLinesOffer obj = new ProspectiveOfferLinesOffer()
                    {
                        Id = GetReaderValue_Int32(reader, "ProspectiveOfferLineId", 0),
                        PartNo = GetReaderValue_String(reader, "Part", ""),
                        SourceFileName = GetReaderValue_String(reader, "SourceFileName", ""),
                        ImportRowCount = GetReaderValue_String(reader, "ImportRowCount", "")
                    };
                    return obj;
                }
                return null;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ProspectiveOffer Lines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// execute usp_Get_ProspectiveOffer_Logs
        /// </summary>
        /// <param name="prospectiveOfferLineId"></param>
        /// <param name="customerRequirementId"></param>
        /// <returns></returns>
        public override List<ProspectiveOffersLogs> GetProspectiveOffersLogs(int prospectiveOfferLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_ProspectiveOffer_Logs", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 120
                };
                cmd.Parameters.Add("@ProspectiveOfferId", SqlDbType.Int).Value = prospectiveOfferLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProspectiveOffersLogs> lstOfferLogs = new List<ProspectiveOffersLogs>();
                while (reader.Read())
                {
                    ProspectiveOffersLogs offerLogs = new ProspectiveOffersLogs()
                    {
                        Id = GetReaderValue_Int32(reader, "ProspectiveOfferLogsId", 0),
                        ProspectiveOfferLineId = GetReaderValue_Int32(reader, "ProspectiveOfferLineId", 0),
                        PartNo = GetReaderValue_String(reader, "PartNo", ""),
                        HUBRFQCustomer = GetReaderValue_String(reader, "HUBRFQCustomer", ""),
                        Quantity = GetReaderValue_Int32(reader, "Quantity", 0),
                        Price = GetReaderValue_Double(reader, "Price", 0),
                        Currency = GetReaderValue_String(reader, "Currency", ""),
                        IHSAvgPrice = GetReaderValue_Double(reader, "IHSAvgPrice", 0),
                        LyticaAvgPrice = GetReaderValue_Double(reader, "LyticaAvgPrice", 0),
                        ReceivedDate = GetReaderValue_DateTime(reader, "ReceivedDate", DateTime.MinValue),
                        Manufacturer = GetReaderValue_String(reader, "Manufacturer", ""),
                        SalesPerson = GetReaderValue_String(reader, "SalesPerson", ""),
                        CustomerReqId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0),
                        BOMNo = GetReaderValue_String(reader, "BOMCode", ""),
                        BOMId = GetReaderValue_Int32(reader, "BOMId", 0),
                        CustomerPartNo = GetReaderValue_String(reader, "CustomerPartNo", ""),
                        DateCode = GetReaderValue_String(reader, "DateCode", ""),
                        ProductName = GetReaderValue_String(reader, "ProductName", ""),
                        PackageName = GetReaderValue_String(reader, "PackageName", ""),
                        HUBRFQCreatedId = GetReaderValue_Int32(reader, "HUBRFQCreatedId", 0),
                        HUBRFQCreatedNo = GetReaderValue_String(reader, "HUBRFQCreatedNo", ""),
                        NewOfferPriceFromProspective = GetReaderValue_NullableDouble(reader, "NewOfferPriceFromProspective", null)
                    };
                    lstOfferLogs.Add(offerLogs);
                }
                return lstOfferLogs;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ProspectiveOffer logs", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// execute usp_Get_ProspectiveOffer_Logs_SentDate
        /// </summary>
        /// <param name="prospectiveOfferLineId"></param>
        /// <param name="customerRequirementId"></param>
        /// <returns></returns>
        public override List<ProspectiveOffersLogs> GetProspectiveOffersLogsSentDate(int prospectiveOfferLineId, List<int> customerRequirementIds)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_ProspectiveOffer_Logs_SentDate", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 120
                };
                cmd.Parameters.Add("@ProspectiveOfferId", SqlDbType.Int).Value = prospectiveOfferLineId;
                cmd.Parameters.Add("@CustomerRequirementIds", SqlDbType.NVarChar).Value = string.Join(",", customerRequirementIds);
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProspectiveOffersLogs> lstOfferLogs = new List<ProspectiveOffersLogs>();
                while (reader.Read())
                {
                    ProspectiveOffersLogs offerLogs = new ProspectiveOffersLogs()
                    {
                        PartNo = GetReaderValue_String(reader, "PartNo", ""),
                        SentDate = GetReaderValue_DateTime(reader, "SentDate", DateTime.MinValue),
                        Manufacturer = GetReaderValue_String(reader, "Manufacturer", ""),
                    };
                    lstOfferLogs.Add(offerLogs);
                }
                return lstOfferLogs;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ProspectiveOffer logs", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        
        public override List<ProspectiveOfferLines> GetProspectiveOfferLines(int proId, int curPage, int rpp)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_ProspectiveOffer_Lines", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@ProspectiveOfferId", SqlDbType.Int).Value = proId;
                cmd.Parameters.Add("@CurPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = rpp;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProspectiveOfferLines> lst = new List<ProspectiveOfferLines>();
                int i = 1;
                while (reader.Read())
                {
                    ProspectiveOfferLines obj = new ProspectiveOfferLines()
                    {
                        Id = i,
                        ProspectiveOfferLineId = GetReaderValue_Int32(reader, "ProspectiveOfferLineId", 0),
                        PartNo = GetReaderValue_String(reader, "PartNo", ""),
                        HUBRFQCustomer = GetReaderValue_String(reader, "HUBRFQCustomer", ""),
                        QuantityOffered = GetReaderValue_Int32(reader, "QuantityOffered", 0),
                        UploadedOfferPrice = GetReaderValue_Double(reader, "UploadedOfferPrice", 0),
                        LowestOffer = GetReaderValue_Double(reader, "GTLowestPrice", 0),
                        HighestOffer = GetReaderValue_Double(reader, "GTHighestPrice", 0),
                        Currency = GetReaderValue_String(reader, "Currency", ""),
                        ReqGlobalCurrencyNo = GetReaderValue_Int32(reader, "ReqGlobalCurrencyNo", 0),
                        CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0),
                        IHSAvgPrice = GetReaderValue_Double(reader, "IHSAvgPrice", 0),
                        LyticaAvgPrice = GetReaderValue_Double(reader, "LyticaAvgPrice", 0),
                        GTReqCount = GetReaderValue_Int32(reader, "NumOfReq", 0),
                        QuotePrice = GetReaderValue_Double(reader, "QuotePrice", 0),
                        QuoteQTY = GetReaderValue_Int32(reader, "QuoteQTY", 0),
                        SOPrice = GetReaderValue_Double(reader, "QuotePrice", 0),
                        SOQTY = GetReaderValue_Int32(reader, "QuoteQTY", 0),
                        SOTaxable = GetReaderValue_String(reader, "SOTaxable", ""),
                        SODate = GetReaderValue_DateTime(reader, "SODate", DateTime.MinValue),
                        SOTaxRate = GetReaderValue_Double(reader, "SOTaxRate", 0),
                        Manufacturer = GetReaderValue_String(reader, "Manufacturer", ""),
                        FileShared = GetReaderValue_String(reader, "FileShared", ""),
                        IsFromGT = GetReaderValue_Int32(reader, "IsFromGT", 0),
                        Notes = GetReaderValue_String(reader, "Notes", ""),
                        ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerNo", 0)
                    };
                    i++;
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ProspectiveOffer Lines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<ProspectiveOfferLines> GetGTOffersDetail(int proId, List<int> proLineIds, int monthRange, int minOfferQty, int curPage, int rpp)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_GTOffer_Details", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@ProspectiveOfferId", SqlDbType.Int).Value = proId;
                cmd.Parameters.Add("@LineIds", SqlDbType.VarChar).Value = String.Join(",", proLineIds);
                cmd.Parameters.Add("@MonthRange", SqlDbType.Int).Value = monthRange;
                cmd.Parameters.Add("@MinOfferQty", SqlDbType.Int).Value = minOfferQty;
                cmd.Parameters.Add("@CurPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = rpp;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProspectiveOfferLines> lst = new List<ProspectiveOfferLines>();
                while (reader.Read())
                {
                    ProspectiveOfferLines obj = new ProspectiveOfferLines()
                    {
                        ProspectiveOfferLineId = GetReaderValue_Int32(reader, "ProspectiveOfferLineId", 0),
                        CustomerReqId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0),
                        BOMNo = GetReaderValue_Int32(reader, "BOMNo", 0),
                        PartNo = GetReaderValue_String(reader, "PartNo", ""),
                        HUBRFQCustomer = GetReaderValue_String(reader, "HUBRFQCustomer", ""),
                        QuantityOffered = GetReaderValue_Int32(reader, "QuantityOffered", 0),
                        UploadedOfferPrice = GetReaderValue_Double(reader, "UploadedOfferPrice", 0),
                        LowestOffer = GetReaderValue_Double(reader, "GTLowestOffer", 0),
                        HighestOffer = GetReaderValue_Double(reader, "GTHighestOffer", 0),
                        Currency = GetReaderValue_String(reader, "Currency", ""),
                        CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0),
                        ReqGlobalCurrencyNo = GetReaderValue_Int32(reader, "ReqGlobalCurrencyNo", 0),
                        IHSAvgPrice = GetReaderValue_Double(reader, "IHSAvgPrice", 0),
                        LyticaAvgPrice = GetReaderValue_Double(reader, "LyticaAvgPrice", 0),
                        QuotePrice = GetReaderValue_Double(reader, "QuotePrice", 0),
                        QuoteQTY = GetReaderValue_Int32(reader, "QuoteQTY", 0),
                        SOPrice = GetReaderValue_Double(reader, "SOPrice", 0),
                        SOQTY = GetReaderValue_Int32(reader, "SOQTY", 0),
                        SOTaxable = GetReaderValue_String(reader, "SOTaxable", ""),
                        SODate = GetReaderValue_NullableDateTime(reader, "SODate", null),
                        SOTaxRate = GetReaderValue_Double(reader, "SOTaxRate", 0),
                        Manufacturer = GetReaderValue_String(reader, "Manufacturer", ""),
                        FileShared = GetReaderValue_String(reader, "FileShared", ""),
                        IsFromGT = GetReaderValue_Int32(reader, "IsFromGT", 0),
                        TotalCount = GetReaderValue_Int32(reader, "TotalCount", 0),
                        SentProspectiveOfferAt = GetReaderValue_String(reader, "SentProspectiveOfferAt", ""),
                        NewOfferPriceFromProspective = GetReaderValue_NullableDouble(reader, "NewOfferPriceFromProspective", null),
                        DateRelease = GetReaderValue_String(reader, "DateRelease", ""),
                        CusReqCurrencyNo = GetReaderValue_Int32(reader, "CusReqCurrencyNo", 0),
                        ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0),
                        ProspectiveOfferPrice = GetReaderValue_Double(reader, "ProspectiveOfferPrice", 0),
                        IsProspectiveSent = GetReaderValue_Boolean(reader, "IsProspectiveSent", false)
                    };

                    lst.Add(obj);
                    obj = null;
                }
                return lst.OrderBy(x => x.ProspectiveOfferLineId).ThenBy(x => x.PartNo).ToList();
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ProspectiveOffer Lines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void CloneHUBRFQOffer(int proId, int bomId, int gtOfferId, int newCusReqId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_CloneHUBRFQ_Prospective_Offer", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = gtOfferId;
                cmd.Parameters.Add("@NewCusReqId", SqlDbType.Int).Value = newCusReqId;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@ProId", SqlDbType.Int).Value = proId;

                cn.Open();
                cmd.ExecuteNonQuery();
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Clone HUBRFQ for Prospective Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int CloneCustomerRequirement(int existingCusReqId, int currencyNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Clone_Customer_Requirement", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = existingCusReqId;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@InsertedId", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();
                cmd.ExecuteNonQuery();

                return Convert.ToInt32(cmd.Parameters["@InsertedId"].Value);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to Clone Customer Requirement for Prospective Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override ProspectiveOfferCustomerRequirement GetExistingHUBRFQ(int gtOfferId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_CustomerRequirements_Specific_Data", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = gtOfferId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                if (reader.Read())
                {
                    ProspectiveOfferCustomerRequirement obj = new ProspectiveOfferCustomerRequirement()
                    {
                        CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0),
                        ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0),
                        CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0),
                        CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0),
                        Salesman = GetReaderValue_Int32(reader, "Salesman", 0),
                        ProspectiveOfferPrice = GetReaderValue_Double(reader, "NewOfferPriceFromProspective", 0)
                    };
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ProspectiveOffer Lines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int GetNewHUBRFQ(int proId, int proLineId, int gtId, int bomId, int? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_New_Prospective_Offer_HUBRFQ_by_Clone", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@ProspectiveOfferId", SqlDbType.Int).Value = proId;
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = gtId;
                cmd.Parameters.Add("@ExistingBomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);

                return cmd.Parameters["@BOMId"].Value != DBNull.Value ? Convert.ToInt32(cmd.Parameters["@BOMId"].Value) : 0;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get new HUBRFQ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void SaveImportColumnHeader(string columnList, string insertColumnList, int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_saveProsOffer_tempHeading", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@SelectColumns", SqlDbType.NVarChar, 3000).Value = columnList;
                cmd.Parameters.Add("@InsertColumnList", SqlDbType.NVarChar, 3000).Value = insertColumnList;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert prospective offer header info", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void SaveBulkImportData(DataTable dtData, string originalFilename, string generatedFilename, int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmdcur = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmdcur = new SqlCommand("usp_saveProsOffer_tempData", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmdcur.Parameters.AddWithValue("@UploadedData", dtData);
                cmdcur.Parameters.AddWithValue("@OriginalFilename", originalFilename);
                cmdcur.Parameters.AddWithValue("@GeneratedFilename", generatedFilename);
                cmdcur.Parameters.AddWithValue("@UserId", loginId);
                cn.Open();
                cmdcur.ExecuteNonQuery();
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert prospective offer data", sqlex);
            }
            finally
            {
                cmdcur.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetRawDataHeader(int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_get_ProsOffer_tempHeading", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get prospective offer raw data headers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetProspectiveOfferRawData(int displayLength, int displayStart, int sortCol, string sortDir, int userId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_get_ProsOffer_rawData", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmd.Parameters.Add("@DisplayLength", SqlDbType.Int).Value = displayLength;
                cmd.Parameters.Add("@DisplayStart", SqlDbType.Int).Value = displayStart;
                cmd.Parameters.Add("@SortCol", SqlDbType.Int).Value = sortCol;
                cmd.Parameters.Add("@SortDir", SqlDbType.NVarChar, 10).Value = sortDir;//[004]  
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;//[003]

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get GetProspectiveOfferRawData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetSupplierMappedColumn(int supplierId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_select_ProspectiveOfferMappedColumn", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Supplier Column Mapping for prospective offer import", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void SaveSupplierColumnMapping(int supplierId,
                                                       string manufacturer,
                                                       string part,
                                                       string quantity,
                                                       string price,
                                                       string description,
                                                       string alterPart,
                                                       string datecode,
                                                       string product,
                                                       string package,
                                                       string rohs,
                                                       string supplierPart,
                                                       int currencyNo,
                                                       int loginNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insertOrUpdate_ProsOfferColumnMapping", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };
                cmd.Parameters.Add("@supplierNo", SqlDbType.Int).Value = supplierId;
                cmd.Parameters.Add("@manufacturer", SqlDbType.NVarChar).Value = manufacturer;
                cmd.Parameters.Add("@part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@quantity", SqlDbType.NVarChar).Value = quantity;
                cmd.Parameters.Add("@price", SqlDbType.NVarChar).Value = price;
                cmd.Parameters.Add("@description", SqlDbType.NVarChar).Value = description;
                cmd.Parameters.Add("@alterPart", SqlDbType.NVarChar).Value = alterPart;
                cmd.Parameters.Add("@datecode", SqlDbType.NVarChar).Value = datecode;
                cmd.Parameters.Add("@product", SqlDbType.NVarChar).Value = product;
                cmd.Parameters.Add("@package", SqlDbType.NVarChar).Value = package;
                cmd.Parameters.Add("@rohs", SqlDbType.NVarChar).Value = rohs;
                cmd.Parameters.Add("@supplierPart", SqlDbType.NVarChar).Value = supplierPart;
                cmd.Parameters.Add("@currencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@loginNo", SqlDbType.Int).Value = loginNo;

                cn.Open();
                ExecuteNonQuery(cmd);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed in usp_insertOrUpdate_ProsOfferColumnMapping", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable ValidateImportData(int supplierId, int loginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_validate_ProspectiveOfferImport", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 600
                };

                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = supplierId;

                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed in usp_validate_ProspectiveOfferImport", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override int ImportProspectiveOffers(int supplierId, int loginId, out string outputMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_insert_ProspectiveOfferImport", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@SupplierId", SqlDbType.Int).Value = supplierId;
                cmd.Parameters.Add("@RecordCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@OutputMessage", SqlDbType.VarChar, 2000).Direction = ParameterDirection.Output;

                cn.Open();
                ExecuteNonQuery(cmd);

                outputMessage = Convert.ToString(cmd.Parameters["@OutputMessage"].Value);

                return Convert.ToInt32(cmd.Parameters["@RecordCount"].Value);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to import prospective offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override ProspectiveOfferLines GetProspectiveOfferLineById(int lineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_Prospective_Offer_Line_By_Id", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 900
                };
                cmd.Parameters.Add("@LineId", SqlDbType.Int).Value = lineId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                if (reader.Read())
                {
                    ProspectiveOfferLines obj = new ProspectiveOfferLines()
                    {
                        PartNo = GetReaderValue_String(reader, "Part", ""),
                        ManufacturerNo = GetReaderValue_Int32(reader, "ManufacturerNo", 0),
                        DateCode = GetReaderValue_String(reader, "DateCode", ""),
                        ProductNo = GetReaderValue_Int32(reader, "ProductNo", 0),
                        PackageNo = GetReaderValue_Int32(reader, "PackageId", 0),
                        QuantityOffered = GetReaderValue_Int32(reader, "Quantity", 0),
                        UploadedOfferPrice = GetReaderValue_Double(reader, "Price", 0),
                        CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0),
                        SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0),
                        RoHS = GetReaderValue_Int32(reader, "ROHS", 0)
                    };
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get ProspectiveOffer Line By Id", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void UpdateProspectiveOfferLine(int proId, List<int> proLineIds, List<ProspectiveOfferLines> gtOffers)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                var listObjects = new List<object>();
                foreach (var item in gtOffers)
                {
                    if (item.NewOfferPriceFromProspective != null)
                    {
                        listObjects.Add(new { Id = item.CustomerReqId, Price = item.NewOfferPriceFromProspective });
                    }
                }
                List<int> cusReqIds = gtOffers.Select(x => x.CustomerReqId).ToList();
                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Update_SharedFile", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@ProspectiveOfferId", SqlDbType.Int).Value = proId;
                cmd.Parameters.Add("@ProspectiveOfferLineIds", SqlDbType.VarChar).Value = String.Join(",", proLineIds);
                cmd.Parameters.Add("@CustomerRequirementIds", SqlDbType.VarChar).Value = String.Join(",", cusReqIds);
                cmd.Parameters.Add("@JsonData", SqlDbType.NVarChar).Value = JsonConvert.SerializeObject(listObjects);

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to update prospective offer SentDate", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// execute usp_Insert_ProspectiveOffer_Logs
        /// </summary>
        /// <param name="proId"></param>
        /// <param name="proLineIds"></param>
        public override void InsertProspectiveOfferLogs(int proLineId, int customerRequirementId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Insert_ProspectiveOffer_Logs", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@ProspectiveOfferLineId", SqlDbType.Int).Value = proLineId;

                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to update prospective offer SentDate", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override void UpdateProspectiveOfferManufacturer(int proId, int proLineId, int manufacturerNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(ConfigurationManager.ConnectionStrings["LocalSqlServer"].ToString());
                cmd = new SqlCommand("usp_Update_ProspectiveOffer_Manufacturer", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 60
                };
                cmd.Parameters.Add("@ProspectiveOfferId", SqlDbType.Int).Value = proId;
                cmd.Parameters.Add("@ProspectiveOfferLineId", SqlDbType.Int).Value = proLineId;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cn.Open();
                cmd.ExecuteNonQuery();

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to update prospective offer Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
