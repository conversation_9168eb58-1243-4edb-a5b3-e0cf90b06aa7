///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber = function(element) { 
    Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.initializeBase(this, [element]);
    this._intGoodsInLineNo = -1;
    this._intGoodsInNo = -1;
    this._elementId = "";
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.prototype = {
    addPotentialStatusChange: function (handler) { this.get_events().addHandler("PotentialStatusChange", handler); },
    removePotentialStatusChange: function (handler) { this.get_events().removeHandler("PotentialStatusChange", handler); },
    onPotentialStatusChange: function () {
        var handler = this.get_events().getHandler("PotentialStatusChange");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
		this.addSearched(Function.createDelegate(this, this.doSearched));
	    //
	    //this.getField("ctlGroup").get_ddl._intGoodsInLineNo = 10;
		
		//alert($find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGITempSerialNumber_ctlDB_ctlGroup_ddl"));
	    //this.getField("ctlGroup").ControlID
		//alert(this._intGoodsInLineNo);
		//$find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGITempSerialNumber_ctlDB_ctlGroup_ddl")._intGoodsInLineNo = this._intGoodsInLineNo;
	},



	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intGoodsInLineNo = null;
	    this._intGoodsInNo = null;
	    this._elementId = null;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.callBaseMethod(this, "dispose");
	},
	refereshGroup: function () {
	    $find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl")._intGoodsInLineNo = this._intGoodsInLineNo;
	    $find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl").getData();
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/GITempSerialNumber");
		this._objData.set_DataObject("GITempSerialNumber");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("GoodsInId", this._intGoodsInNo);
		this._objData.addParameter("GoodsInLineId", this._intGoodsInLineNo);
		
	},
	getGroupValue: function () {
	    return this.getFieldValue("ctlGroup");
	},
	
	doGetDataComplete: function () {
	    this._invoiceExist = false;
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
		    var row = this._objResult.Results[i];
		    this._invoiceExist = row.InvoiceLineNo > 0 ? true : false;
		    var aryData = "";

		    if (this._invoiceExist == false) {
		        aryData = [
                 row.SubGroup,
                  row.SerialNo,
                row.GoodsNumber,
               // "<a id='" + row.SerialNoId + '' + row.Status + "' Style='color:red'  href='javascript:void(0)' onclick='$find.deleteRow();'>" + row.Delete + "</a>"
                  String.format("<a href=\"javascript:void(0);\" Style='color:#006600' class='Delete'  onclick=\"$find('{0}').removeItem({1},'{2}');\" class=\"quickSearchReselect\">Delete</a>", this._elementId, row.ID, row.Status)
		        ];
		    }
		    else {
		        aryData = [
              row.SubGroup,
               row.SerialNo,
             row.GoodsNumber,
            // "<a id='" + row.SerialNoId + '' + row.Status + "' Style='color:red'  href='javascript:void(0)' onclick='$find.deleteRow();'>" + row.Delete + "</a>"
               String.format("<a href=\"javascript:void(0);\" Style='color: #006600' Hidden='true'  onclick=\"return false;\" class=\"quickSearchReselect\">Delete</a>", this._elementId, row.ID)
		        ];
		    }


		    var objExtraData = {
		        SerialNoId: row.ID,
		        SubGroup: row.SubGroup,
		        SerialNo: row.SerialNo,
		        GoodsInNo: row.GoodsInNo,
		        Status: row.Status,
		        InvoiceLineNo: row.InvoiceLineNo

		    };
		    var strCSS = (row.Inactive) ? "ceased" : "";
		    this._tblResults.addRow(aryData, row.ID, false, objExtraData, strCSS);
		    aryData = null;
		    row = null;

		   
		}
		//this.onPotentialStatusChange();
	},
	doSearched: function () {
	    this.onPotentialStatusChange();
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempSerialNumber", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
