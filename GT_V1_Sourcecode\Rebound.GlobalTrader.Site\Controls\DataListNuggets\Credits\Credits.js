Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.prototype={get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_blnHubCredit:function(){return this._blnHubCredit},set_blnHubCredit:function(n){this._blnHubCredit!==n&&(this._blnHubCredit=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.callBaseMethod(this,"initialize");this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/Credits";this._strDataObject="Credits";this.getData()},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.hubCreditFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._blnHubCredit=null,this._blnPOHub=null,this._IsGSA=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.hubCreditFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("blnHubCredit",this._blnHubCredit)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_CreditNote(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Date),$R_FN.writeDoubleCellValue($RGT_nubButton_Invoice(n.InvNo,n.Invoice),$RGT_nubButton_ClientInvoice(n.ClientInvoiceNo,n.ClientInvoiceNumber)),$R_FN.setCleanTextValue(n.CustPO),$R_FN.setCleanTextValue(n.Total)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlSalesman").show(this._enmViewLevel!=0);this.getFilterField("ctlPohubOnly").show(this._blnPOHub);this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);this.getFilterField("ctlClientInvNo").show(!1);this.getFilterField("ctlClientInvNo").enableField(!1);this.getFilterField("ctlClientName").show(this._IsGSA)},hubCreditFilterVisibility:function(){this.getFilterField("ctlSalesman").show(this._enmViewLevel!=0&&!this._blnHubCredit);this.getFilterField("ctlCreditNotes").show(!this._blnHubCredit);this.getFilterField("ctlCompanyName").show(!this._blnHubCredit);this.getFilterField("ctlContactName").show(!this._blnHubCredit);this.getFilterField("ctlCRMANo").show(!this._blnHubCredit);this.getFilterField("ctlClientInvNo").show(!0)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Credits",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);