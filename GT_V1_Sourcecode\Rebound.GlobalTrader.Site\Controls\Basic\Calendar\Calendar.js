Rebound.GlobalTrader.Site.Controls.CalendarManager=function(n){Rebound.GlobalTrader.Site.Controls.CalendarManager.initializeBase(this,[n]);this._aryCalendarIDs=[]};Rebound.GlobalTrader.Site.Controls.CalendarManager.prototype={dispose:function(){var n,i,t;if(!this.isDisposed){if(this._aryCalendarIDs)for(n=0,i=this._aryCalendarIDs.length;n<i;n++)t=$find(this._aryCalendarIDs[n]),t&&t.dispose(),t=null;this._aryCalendarIDs=null;this.isDisposed=!0}},registerCalendar:function(n){Array.add(this._aryCalendarIDs,n._element.id)},closeOtherCalendars:function(n){for(var t=0,i=this._aryCalendarIDs.length;t<i;t++)this._aryCalendarIDs[t]!=n._element.id&&$find(this._aryCalendarIDs[t]).explicitHideCalendar()}};Rebound.GlobalTrader.Site.Controls.CalendarManager.registerClass("Rebound.GlobalTrader.Site.Controls.CalendarManager");$R_CAL_MANAGER=new Rebound.GlobalTrader.Site.Controls.CalendarManager;Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.Calendar=function(n){Rebound.GlobalTrader.Site.Controls.Calendar.initializeBase(this,[n]);this._dtmSelectedDate=new Date;this._blnFormatAsBirthday=!1;this._intMonthSelectTimeout=-1;this._intYearSelectTimeout=-1;this._intYearMonthTimeout={Show:250,Hide:50};this._blnShowWeekNumber=!0};Rebound.GlobalTrader.Site.Controls.Calendar.prototype={get_txt:function(){return this._txt},set_txt:function(n){this._txt!==n&&(this._txt=n)},get_pnlCalendar:function(){return this._pnlCalendar},set_pnlCalendar:function(n){this._pnlCalendar!==n&&(this._pnlCalendar=n)},get_imgCal:function(){return this._imgCal},set_imgCal:function(n){this._imgCal!==n&&(this._imgCal=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_hypOff:function(){return this._hypOff},set_hypOff:function(n){this._hypOff!==n&&(this._hypOff=n)},get_blnVisible:function(){return this._blnVisible},set_blnVisible:function(n){this._blnVisible!==n&&(this._blnVisible=n)},get_blnFormatAsBirthday:function(){return this._blnFormatAsBirthday},set_blnFormatAsBirthday:function(n){this._blnFormatAsBirthday!==n&&(this._blnFormatAsBirthday=n)},addShowCalendarEvent:function(n){this.get_events().addHandler("ShowCalendar",n)},removeShowCalendarEvent:function(n){this.get_events().removeHandler("ShowCalendar",n)},onShowCalendar:function(){var n=this.get_events().getHandler("ShowCalendar");n&&n(this,Sys.EventArgs.Empty)},addCloseCalendarEvent:function(n){this.get_events().addHandler("CloseCalendar",n)},removeCloseCalendarEvent:function(n){this.get_events().removeHandler("CloseCalendar",n)},onCloseCalendar:function(){var n=this.get_events().getHandler("CloseCalendar");n&&n(this,Sys.EventArgs.Empty)},addDateSelectEvent:function(n){this.get_events().addHandler("DateSelect",n)},removeDateSelectEvent:function(n){this.get_events().removeHandler("DateSelect",n)},onDateSelect:function(){var n=this.get_events().getHandler("DateSelect");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Calendar.callBaseMethod(this,"initialize");$addHandler(this._imgCal,"click",Function.createDelegate(this,this.imgCal_Click));$addHandler(this._txt,"click",Function.createDelegate(this,this.txt_Click));$addHandler(this._txt,"focus",Function.createDelegate(this,this.txt_Click));$addHandler(this._hypOff,"click",Function.createDelegate(this,this.hypOff_Click));$R_CAL_MANAGER.registerCalendar(this)},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._imgCal&&$clearHandlers(this._imgCal),this._txt&&$clearHandlers(this._txt),this._hypOff&&$clearHandlers(this._hypOff),this._txt=null,this._pnlCalendar=null,this._imgCal=null,this._tbl=null,this._hypOff=null,this._dtmSelectedDate=null,this._blnFormatAsBirthday=null,this._blnVisible=null,this._intMonthSelectTimeout=null,this._intYearSelectTimeout=null,this._intYearMonthTimeout=null,Rebound.GlobalTrader.Site.Controls.Calendar.callBaseMethod(this,"dispose"),this.isDisposed=!0)},imgCal_Click:function(){this.toggleShowCalendar()},txt_Click:function(){this._blnVisible||this.showCalendar(!0)},hypOff_Click:function(){this.showCalendar(!1)},setInitialMonthAndYear:function(){this._txt.value.length>0&&(this._dtmSelectedDate=Date.parseLocale(this._txt.value));this._dtmSelectedDate||(this._dtmSelectedDate=new Date);this._intCurrentYear=this._dtmSelectedDate.getFullYear();this._intCurrentMonth=this._dtmSelectedDate.getMonth()+1},toggleShowCalendar:function(){this._blnVisible=!this._blnVisible;this.showCalendar(this._blnVisible)},showCalendar:function(n,t){this._blnVisible=n;$R_FN.showElement(this._pnlCalendar,n);n?(this.setInitialMonthAndYear(),this.populateCalendar(),t||this.onShowCalendar(),$R_CAL_MANAGER.closeOtherCalendars(this),Sys.Browser.agent==Sys.Browser.Safari&&(this._pnlCalendar.style.left=String.format("-{0}px",Sys.UI.DomElement.getBounds(this._txt).width))):t||this.onCloseCalendar()},explicitHideCalendar:function(){this._blnVisible=!1;$R_FN.showElement(this._pnlCalendar,!1);this.onCloseCalendar()},changeMonth:function(n){this._intCurrentMonth=this._intCurrentMonth+n;this._intCurrentMonth>12&&(this._intCurrentMonth=1,this._intCurrentYear+=1);this._intCurrentMonth<1&&(this._intCurrentMonth=12,this._intCurrentYear-=1);this.selectMonth(this._intCurrentMonth)},selectMonth:function(n){this._intCurrentMonth=n;this.populateCalendar()},changeYear:function(n){this.selectYear(this._intCurrentYear+n)},selectYear:function(n){this._intCurrentYear=n;this.populateCalendar()},selectDate:function(n){this._dtmSelectedDate=new Date(n);this._txt.value=this._dtmSelectedDate.localeFormat(this._blnFormatAsBirthday?"m":"d");this.showCalendar(!1);this.onDateSelect()},selectToday:function(){this.selectDate(new Date)},selectNone:function(){this._txt.value="";this.showCalendar(!1);this.onDateSelect()},populateCalendar:function(){var s=new Date,f,t,c,a,e,v,y,o,u;for(s.setFullYear(this._intCurrentYear,this._intCurrentMonth-1,1),f=new Date(s),f.setDate(27);f.getMonth()==s.getMonth();)f.setDate(f.getDate()+1);f.setDate(f.getDate()-1);var r=new Date(s),n=[],i,p=this,h=this.get_element().id;for(t=this._tbl.rows.length-1;t>=0;t--)this._tbl.deleteRow(t);for(Array.clear(n),n[0]=document.createElement("td"),n[0].align="center",n[0].colSpan=this._blnShowWeekNumber?8:7,n[0].className="calendarNextPrevStyle",c=document.createElement("div"),c.className="calendarPrev",i=document.createElement("a"),i.href="javascript:void(0);",i.setAttribute("onclick",String.format("$find('{0}').changeYear(-1);",h)),i.innerHTML="&lt;&lt;",c.appendChild(i),anc2=document.createElement("a"),anc2.href="javascript:void(0);",anc2.setAttribute("onclick",String.format("$find('{0}').changeMonth(-1);",h)),anc2.innerHTML="&lt;",c.appendChild(anc2),n[0].appendChild(c),a=document.createElement("div"),a.className="calendarNext",anc2=document.createElement("a"),anc2.href="javascript:void(0);",anc2.setAttribute("onclick",String.format("$find('{0}').changeYear(1);",h)),anc2.innerHTML="&gt;&gt;",a.appendChild(anc2),i=document.createElement("a"),i.href="javascript:void(0);",i.setAttribute("onclick",String.format("$find('{0}').changeMonth(1);",h)),i.innerHTML="&gt;",a.appendChild(i),n[0].appendChild(a),n[0].innerHTML+=String.format("{0}&nbsp;{1}",this.formatMonthString(this._intCurrentMonth),this.formatYearString(this._intCurrentYear)),this.addRowToTable(n,"calendarTitle"),Array.clear(n),e=new Date(s);e.getDay()!=0;)e.setDate(e.getDate()+1);for(tr=document.createElement("tr"),t=0;t<7;t++)v=t==0||t==6?"calendarWeekendHeader":"calendarHeader",y=e.localeFormat("ddd"),n[t]=document.createElement("td"),n[t].align="center",n[t].className=v,n[t].innerHTML=y,e.setDate(e.getDate()+1);for(this._blnShowWeekNumber&&(n[7]=document.createElement("td"),n[7].align="center",n[7].className="calendarHeader",n[7].innerHTML="Week"),e=null,this.addRowToTable(n),t=0;t<6;t++)if(r.valueOf()<=f.valueOf()){if(Array.clear(n),o=0,t==0)for(o,l=s.getDay();o<l;o++)n[o]=document.createElement("td"),n[o].align="center",n[o].innerHTML="&nbsp;";for(u=o;u<7;u++)r.valueOf()<=f.valueOf()?(v=r.getDay()==0||r.getDay()==6?"calendarWeekendDay":"calendarDay",r.getDate()==this._dtmSelectedDate.getDate()&&r.getMonth()==this._dtmSelectedDate.getMonth()&&r.getYear()==this._dtmSelectedDate.getYear()&&(v="calendarSelectedDay"),n[u]=document.createElement("td"),n[u].align="center",n[u].className=v,n[u].innerHTML=String.format('<a href="javascript:void(0);" onclick="$find(\'{0}\').selectDate({1});">{2}<\/a>',this.get_element().id,r.valueOf(),r.getDate())):(n[u]=document.createElement("td"),n[u].align="center",n[u].innerHTML="&nbsp;"),this._blnShowWeekNumber&&(n[7]=document.createElement("td"),n[7].align="center",n[7].className="calendarHeader",n[7].innerHTML=this.getWeek(r)),r.setDate(r.getDate()+1);this.addRowToTable(n)}Array.clear(n);n[0]=document.createElement("td");n[0].align="center";n[0].colSpan=7;n[0].className="calendarNextPrevStyle";i=document.createElement("a");i.href="javascript:void(0);";i.onclick=new Function("evtSelectToday",String.format("$find('{0}').selectToday();",h));i.innerHTML=$R_RES.Today;n[0].appendChild(i);anc2=document.createElement("a");anc2.href="javascript:void(0);";anc2.onclick=new Function("evtSelectNone",String.format("$find('{0}').selectNone();",h));anc2.innerHTML=$R_RES.None;n[0].appendChild(anc2);this.addRowToTable(n);n=null;i=null;anc2=null},addRowToTable:function(n,t){var i,r,u;for(t||(t=""),i=this._tbl.insertRow(-1),i.className=t,r=0,u=n.length;r<u;r++)i.appendChild(n[r]);i=null},formatMonthString:function(n){for(var i="",r=this._element.id,i=String.format('<div class="monthSelect" onmouseover="$find(\'{0}\').showMonthSelect(true);" onmouseout="$find(\'{0}\').showMonthSelect(false);">{1}<div id="{0}_monthSelect" class="monthSelectDrop invisible">',r,Sys.CultureInfo.CurrentCulture.dateTimeFormat.AbbreviatedMonthNames[n-1]),t=0;t<6;t++)i+="<div>",i+=String.format('<div class="item" onclick="$find(\'{1}\').selectMonth({2})">{0}<\/div>',Sys.CultureInfo.CurrentCulture.dateTimeFormat.AbbreviatedMonthNames[t],r,t+1),i+=String.format('<div class="item" onclick="$find(\'{1}\').selectMonth({2})">{0}<\/div>',Sys.CultureInfo.CurrentCulture.dateTimeFormat.AbbreviatedMonthNames[t+6],r,t+6+1),i+="<\/div>";return i+"<\/div><\/div>"},formatYearString:function(n){var t="",r=this._element.id,i;for(t=String.format('<div class="yearSelect" onmouseover="$find(\'{0}\').showYearSelect(true);" onmouseout="$find(\'{0}\').showYearSelect(false);">{1}<div id="{0}_yearSelect" class="yearSelectDrop invisible">',r,n),n-=5,i=0;i<7;i++)n+=1,t+=String.format("<div onclick=\"$find('{1}').selectYear({0})\">{0}<\/div>",n,r);return t+"<\/div><\/div>"},showMonthSelect:function(n){var t=$get(String.format("{0}_monthSelect",this._element.id));this.clearShowMonthTimeout();n?$R_FN.isElementVisible(t)||(this._intMonthSelectTimeout=setTimeout(Function.createDelegate(this,this.finishShowMonthSelect),this._intYearMonthTimeout.Show)):$R_FN.isElementVisible(t)&&(this._intMonthSelectTimeout=setTimeout(Function.createDelegate(this,this.finishHideMonthSelect),this._intYearMonthTimeout.Hide));t=null},clearShowMonthTimeout:function(){this._intMonthSelectTimeout!=-1&&clearTimeout(this._intMonthSelectTimeout)},finishShowMonthSelect:function(){this.clearShowMonthTimeout();$R_FN.showElement($get(String.format("{0}_monthSelect",this._element.id)),!0)},finishHideMonthSelect:function(){this.clearShowMonthTimeout();$R_FN.showElement($get(String.format("{0}_monthSelect",this._element.id)),!1)},showYearSelect:function(n){var t=$get(String.format("{0}_yearSelect",this._element.id));this.clearShowYearTimeout();n?$R_FN.isElementVisible(t)||(this._intYearSelectTimeout=setTimeout(Function.createDelegate(this,this.finishShowYearSelect),this._intYearMonthTimeout.Show)):$R_FN.isElementVisible(t)&&(this._intYearSelectTimeout=setTimeout(Function.createDelegate(this,this.finishHideYearSelect),this._intYearMonthTimeout.Hide));t=null},clearShowYearTimeout:function(){this._intYearSelectTimeout!=-1&&clearTimeout(this._intYearSelectTimeout)},finishShowYearSelect:function(){this.clearShowYearTimeout();$R_FN.showElement($get(String.format("{0}_yearSelect",this._element.id)),!0)},finishHideYearSelect:function(){this.clearShowYearTimeout();$R_FN.showElement($get(String.format("{0}_yearSelect",this._element.id)),!1)},getWeek:function(n){var r=function(n){n.dtmin.getDay()!=1&&(n.dtmin.getDay()<=4&&n.dtmin.getDay()!=0&&(n.w+=1),n.dtmin.setDate(n.dtmin.getDay()==0?2:9-n.dtmin.getDay()));n.w+=Math.ceil(((n.dtmax.getTime()-n.dtmin.getTime())/864e5+1)/7)},u=function(n,t){for(var i=31,r=0;r<=3;r++)if(i=i-r,(dtInst=new Date(n,t-1,i))&&dtInst.getDate()==i&&dtInst.getMonth()+1==t&&dtInst.getFullYear()==n)break;return i},i,t,f;return n.getMonth()+1==1&&n.getDate()>=1&&n.getDate()<=3&&(n.getDay()>=5||n.getDay()==0)?(i={dtmin:new Date(n.getFullYear()-1,0,1,0,0,0,0),dtmax:new Date(n.getFullYear()-1,11,u(n.getFullYear()-1,12),0,0,0,0),w:0},r(i),i.w):(t={dtmin:new Date(n.getFullYear(),0,1,0,0,0,0),dtmax:new Date(n.getFullYear(),n.getMonth(),n.getDate(),0,0,0,0),w:0},f=u(n.getFullYear(),12),n.getMonth()==12&&n.getDay()!=0&&n.getDay()<=3&&f-n.getDate()<=3-n.getDay()?t.w=1:r(t),t.w)}};Rebound.GlobalTrader.Site.Controls.Calendar.registerClass("Rebound.GlobalTrader.Site.Controls.Calendar",Sys.UI.Control,Sys.IDisposable);