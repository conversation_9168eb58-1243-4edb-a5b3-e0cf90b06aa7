﻿<browsers>
    <!-- Assume this is a desktop browser -->
    <!-- sample UA "Go.Web/1.1 (UP.Browser/3.1-UPG1 UP.Link/3.2; Mozilla/1.0; RIM957; Elaine/1.0 )" -->
    <!-- sample UA "Mozilla/1.0[en]; Go.Web/1.1 (compatible; MSIE 2.0; HandHttp 1.1; Palm)" -->
    <browser id="Mozilla" parentID="Default">
        <identification>
            <userAgent match="Mozilla" />
            <userAgent nonMatch="MME|Opera" />
        </identification>

        <capture>
            <userAgent match="Mozilla/(?'version'(?'major'\d+)(?'minor'\.\d+)\w*)" />
            <userAgent match=" (?'screenWidth'\d*)x(?'screenHeight'\d*)" />
        </capture>

        <capabilities>
            <capability name="browser"                         value="Mozilla" />
            <capability name="cookies"                         value="false" />
            <capability name="defaultScreenCharactersHeight"   value="40" />
            <capability name="defaultScreenCharactersWidth"    value="80" />
            <capability name="defaultScreenPixelsHeight"       value="480" />
            <capability name="defaultScreenPixelsWidth"        value="640" />
            <capability name="inputType"                       value="keyboard" />
            <capability name="isColor"                         value="true" />
            <capability name="isMobileDevice"                  value="false" />
            <capability name="majorversion"                    value="${major}" />
            <capability name="maximumRenderedPageSize"         value="300000" />
            <capability name="minorversion"                    value="${minor}" />
            <capability name="screenBitDepth"                  value="8" />
            <capability name="screenPixelsHeight"              value="${screenHeight}" />
            <capability name="screenPixelsWidth"               value="${screenWidth}" />
            <capability name="supportsBold"                    value="true" />
            <capability name="supportsCss"                     value="true" />
            <capability name="supportsDivNoWrap"               value="true" />
            <capability name="supportsFontName"                value="true" />
            <capability name="supportsFontSize"                value="true" />
            <capability name="supportsImageSubmit"             value="true" />
            <capability name="supportsItalic"                  value="true" />
            <capability name="type"                            value="Mozilla" />
            <capability name="version"                         value="${version}" />
        </capabilities>
    </browser>

    <gateway id="MozillaBeta" parentID="Mozilla">
        <identification>
            <userAgent match="Mozilla/\d+\.\d+b" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="beta"    value="true" />
        </capabilities>
    </gateway>

    <gateway id="MozillaGold" parentID="Mozilla">
        <identification>
            <userAgent match="Mozilla/\d+\.\d+\w*Gold" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="gold"    value="true" />
        </capabilities>
    </gateway>

    <browser id="PowerBrowser" parentID="Mozilla">
        <identification>
            <userAgent match="^Mozilla/2\.01 \(Compatible\) Oracle\(tm\) PowerBrowser\(tm\)/1\.0a" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="browser"             value="PowerBrowser" />
            <capability name="cookies"             value="true" />
            <capability name="ecmascriptversion"   value="1.0" />
            <capability name="frames"              value="true" />
            <capability name="javaapplets"         value="true" />
            <capability name="javascript"          value="true" />
            <capability name="majorversion"        value="1" />
            <capability name="minorversion"        value=".5" />
            <capability name="platform"            value="Win95" />
            <capability name="tables"              value="true" />
            <capability name="vbscript"            value="true" />
            <capability name="version"             value="1.5" />
            <capability name="type"                value="PowerBrowser1" />
        </capabilities>

        <controlAdapters>
            <adapter controlType="System.Web.UI.WebControls.Menu"
                     adapterType="System.Web.UI.WebControls.Adapters.MenuAdapter" />
        </controlAdapters>
    </browser>

    <browser id="Gecko" parentID="Mozilla">
        <identification>
            <userAgent match="Gecko" />
        </identification>

        <capabilities>
            <capability name="browser"                 value="Mozilla" />
            <capability name="cookies"                 value="true" />
            <capability name="ecmascriptversion"       value="0.0" />
            <capability name="frames"                  value="true" />
            <capability name="isColor"                 value="true" />
            <capability name="javaapplets"             value="true" />
            <capability name="javascript"              value="true" />
            <capability name="maximumRenderedPageSize" value="20000" />
            <capability name="preferredImageMime"      value="image/jpeg" />
            <capability name="screenBitDepth"          value="32" />
            <capability name="supportsDivNoWrap"       value="false" />
            <capability name="supportsFileUpload"      value="true" />
            <capability name="tables"                  value="true" />
            <capability name="type"                    value="desktop" />
            <capability name="version"                 value="${version}" />
        </capabilities>
    </browser>

    <browser id="MozillaRV" parentID="Gecko">
        <identification>
            <userAgent match="rv\:(?'version'(?'major'\d+)(?'minor'\.[.\d]*))" />
            <userAgent nonMatch="Netscape" />
        </identification>

        <capabilities>
            <capability name="ecmascriptversion"       value="1.4" />
            <capability name="majorversion"            value="${major}" />
            <capability name="maximumRenderedPageSize" value="300000" />
            <capability name="minorversion"            value="${minor}" />
            <capability name="preferredImageMime"      value="image/gif" />
            <capability name="supportsCallback"        value="true" />
            <capability name="supportsDivNoWrap"       value="false" />
            <capability name="supportsMaintainScrollPositionOnPostback" value="true" />
            <capability name="tagwriter"               value="System.Web.UI.HtmlTextWriter" />
            <capability name="type"                    value="Mozilla${major}" />
            <capability name="version"                 value="${version}" />
            <capability name="w3cdomversion"           value="1.0" />
        </capabilities>
    </browser>

    <!-- sample UA "Mozilla/5.0 (Windows; U; Windows NT 5.0; en-US; rv:1.4.1) Gecko/20031008" -->
    <gateway id="MozillaV14Plus" parentID="MozillaRV">
        <identification>
            <capability name="majorversion"            match="^1$" />
            <capability name="minorversion"            match="^\.[4-9]" />
        </identification>

        <capabilities>
            <capability name="backgroundsounds"                value="true" />
            <capability name="css1"                            value="true" />
            <capability name="css2"                            value="true" />
            <capability name="javaapplets"                     value="true" />
            <capability name="maximumRenderedPageSize"         value="2000000" />
            <capability name="preferredImageMime"              value="image/jpeg" />
            <capability name="screenBitDepth"                  value="32" />
            <capability name="supportsMultilineTextBoxDisplay" value="true" />
            <capability name="type"                            value="Mozilla${version}" />
            <capability name="xml"                             value="true" />
        </capabilities>
    </gateway>

    <!-- sample UA "Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.5) Gecko/20031007 Firebird/0.7" -->
    <browser id="MozillaFirebird" parentID="MozillaRV">
        <identification>
            <userAgent match="Gecko\/\d+ Firebird\/(?'version'(?'major'\d+)(?'minor'\.[.\d]*))" />
        </identification>

        <capabilities>
            <capability name="browser"                         value="Firebird" />
            <capability name="majorversion"                    value="${major}" />
            <capability name="minorversion"                    value="${minor}" />
            <capability name="version"                         value="${version}" />
            <capability name="type"                            value="Firebird${version}" />
         </capabilities>
    </browser>

    <!-- sample UA "Mozilla/5.0 (Windows; U; Windows NT 5.0; de-DE; rv:1.7) Gecko/20040707 Firefox/0.9.2" -->
    <browser id="MozillaFirefox" parentID="MozillaRV">
        <identification>
            <userAgent match="Gecko\/\d+ Firefox\/(?'version'(?'major'\d+)(?'minor'\.[.\d]*))" />
        </identification>

        <capabilities>
            <capability name="browser"                         value="Firefox" />
            <capability name="majorversion"                    value="${major}" />
            <capability name="minorversion"                    value="${minor}" />
            <capability name="version"                         value="${version}" />
            <capability name="type"                            value="Firefox${version}" />
        </capabilities>
    </browser>

    <!-- sample UA "Mozilla/5.0 (Macintosh; U; PPC Mac OS X; en-us) AppleWebKit/85 (KHTML, like Gecko) Safari/85" -->
    <!-- sample UA "Mozilla/5.0 (Macintosh; U; PPC Mac OS X; en) AppleWebKit/125.5.6 (KHTML, like Gecko) Safari/125.12" -->
    <browser id="Safari" parentID="Gecko">
        <identification>
            <userAgent match="AppleWebKit/(?'webversion'\d+)" />
        </identification>

        <capture>
        </capture>
        
        <capabilities>
            <capability name="appleWebTechnologyVersion"   value="${webversion}" />
            <capability name="backgroundsounds"            value="true" />
            <capability name="browser"                     value="AppleMAC-Safari" />
            <capability name="css1"                        value="true" />
            <capability name="css2"                        value="true" />
            <capability name="ecmascriptversion"           value="0.0" />
            <capability name="futureBrowser"               value="Apple Safari" />
            <capability name="screenBitDepth"              value="24" />
            <capability name="tables"                      value="true" />
            <capability name="tagwriter"                   value="System.Web.UI.HtmlTextWriter" />
            <capability name="type"                        value="Desktop" />
        </capabilities>

        <controlAdapters>
            <adapter controlType="System.Web.UI.WebControls.Menu"
                     adapterType="System.Web.UI.WebControls.Adapters.MenuAdapter" />
        </controlAdapters>
    </browser>

    <browser id="Safari60" parentID="Safari">
        <identification>
            <capability name="appleWebTechnologyVersion" match="60" />
        </identification>

        <capture>
        </capture>

        <capabilities>
            <capability name="ecmascriptversion"       value="1.0" />
        </capabilities>
    </browser>

    <browser id="Safari85" parentID="Safari">
        <identification>
            <capability name="appleWebTechnologyVersion" match="85" />
        </identification>

        <capture>
        </capture>
        
        <capabilities>
            <capability name="ecmascriptversion"       value="1.4" />
        </capabilities>
    </browser>

    <browser id="Safari1Plus" parentID="Safari">
        <identification>
            <capability name="appleWebTechnologyVersion" match="\d\d\d" />
        </identification>

        <capture>
        </capture>
        
        <capabilities>
            <capability name="ecmascriptversion"       value="1.4" />
            <capability name="w3cdomversion"           value="1.0" />
            <capability name="supportsCallback"        value="true" />
        </capabilities>
    </browser>
</browsers>
