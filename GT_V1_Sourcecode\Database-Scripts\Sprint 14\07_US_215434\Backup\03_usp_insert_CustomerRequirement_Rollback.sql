﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_insert_CustomerRequirement]    Script Date: 11/7/2024 7:34:35 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_CustomerRequirement]                                                      
--******************************************************************************************                                                      
--* SG 27.10.2017:                                                      
--* - Add logging from tbCustomerRequirement insert trigger so that trigger can be deleted                                                      
--*                                                      
--* SK 20.01.2010:                                                      
--* - allow for new columns - PartWatch, BOM, BOMName                                                      
--*                                                      
--* SK 29.10.2009:                                                      
--* - allow for new column - FullCustomerPart - used for searching             
--* [004] <PERSON>    29-08-2023    RP-2227   AS6081 (Counterfeit Electronic Part check)        
--* [005] <PERSON>    29-08-2023    RP-2301   recieve data is updated from getdate()    
--******************************************************************************************                                                      
 @ClientNo      INT,                                                      
 @Part       NVARCHAR(40),                                                      
 @ManufacturerNo     INT = NULL,                                                      
 @DateCode      NVARCHAR(5) = NULL,                                                      
 @PackageNo      INT = NULL,                                                      
 @Quantity      INT,                                                      
 @Price       FLOAT,                                                      
 @CurrencyNo      INT,                                                      
 @ReceivedDate     DATETIME,                                                      
 @Salesman      INT,                                                      
 @DatePromised     DATETIME,                                                      
 @Notes       NVARCHAR(MAX) = NULL,                                                      
 @Instructions     NVARCHAR(MAX) = NULL,                                                      
 @Shortage      BIT,                                                      
 @CompanyNo      INT,                                                      
 @ContactNo      INT,                                                      
 @UsageNo      INT = NULL,                                                      
 @Alternate      BIT,                                                      
 @OriginalCustomerRequirementNo INT = NULL,                                                      
 @ReasonNo      INT,                                                      
 @ProductNo      INT = NULL,                                                      
 @CustomerPart     NVARCHAR(30) = NULL,                                                      
 @Closed       BIT,                                                      
 @ROHS       TINYINT = NULL,                                                      
 @UpdatedBy      INT = NULL,                                                      
 @PartWatch      BIT = NULL,                                                      
 @BOM       BIT = NULL,                                                      
 @BOMName      NVARCHAR(128) = NULL,                                                      
 @BOMNo       INT = NULL,                                                      
 @FactorySealed     BIT = NULL,                                                      
 @MSL       NVARCHAR(50) = NULL,                                                      
 @PartialQuantityAcceptable  BIT = NULL,                                                      
 @Obsolete      BIT = NULL,                
 @LastTimeBuy     BIT = NULL,      
 @RefirbsAcceptable    BIT = NULL,                         
 @TestingRequired    BIT  = NULL,                                                      
 @TargetSellPrice    FLOAT = NULL,          
 @CompetitorBestOffer   FLOAT = NULL,                                                      
 @CustomerDecisionDate   DATETIME = NULL,                                                      
 @RFQClosingDate     DATETIME = NULL,                                    
 @QuoteValidityRequired   INT = NULL,               
 @Type       INT = NULL,                                                      
 @OrderToPlace     INT = NULL,                                                 
 @RequirementForTraceability  INT = NULL,                      
 @EAU       NVARCHAR(50) = NULL,                                                      
 @AlternativesAccepted   BIT = NULL,                                                      
 @RepeatBusiness     BIT = NULL,                                                     
 @SupportTeamMemberNo INT  =null  ,                                                 
 --add parameter for ihs code start                                                
--@CountryOfOrigin  NVARCHAR(100) = NULL,                                                 
@CountryOfOriginNo int=0 ,                                                
@LifeCycleStage NVARCHAR(100) = NULL,                                                 
@HTSCode VARCHAR(20) = NULL,                                                
@AveragePrice FLOAT=0,                                                
@Packing  VARCHAR(60) = NULL,                                                
@PackagingSize NVARCHAR(100) = NULL,                                  
@Descriptions NVARCHAR(max) = NULL,                                            
@IHSPartsNo INT  =null  ,                                          
@IHSCurrencyCode NVARCHAR(100) = NULL,                                   
@IHSProduct NVARCHAR(100) = NULL,                                 
@ECCNCode NVARCHAR(30) = NULL,                        
@ECCNNo int = null,                              
@AS6081 BIT = NULL, -- [004]parameter for 'AS6081'                                           
 --add paramerter for ihs code end                                                   
 @CustomerRequirementId   INT OUTPUT                                                      
AS                                                      
BEGIN                                                      
DECLARE @CustomerRequirementNumber INT                                                   
                                                      
EXEC usp_select_CustomerRequirement_NextNumber @ClientNo, @UpdatedBy, @CustomerRequirementNumber OUTPUT                                                      
-- SET @CustomerRequirementNumber = 1705218                                                      
                                                      
IF EXISTS ( SELECT (1)                                                      
   FROM tbCustomerRequirement                                                      
   WHERE CustomerRequirementNumber = @CustomerRequirementNumber                                                      
   AND  ClientNo = @ClientNo)                                                      
 BEGIN                                                      
 SET @CustomerRequirementId = -1                                         
 RETURN                                                      
 END                                                      
   if(@ECCNCode = '[Blank]')          
  begin          
 set @ECCNCode = null          
 set @ECCNNo = null          
  end                                                     
INSERT INTO dbo.tbCustomerRequirement                                                  
 ( CustomerRequirementNumber                
 , ClientNo                                                      
 , FullPart                              
 , Part                                      
 , ManufacturerNo                                                      
 , DateCode                                            
 , PackageNo                                                      
 , Quantity                                                      
 , Price                                                      
 , CurrencyNo                                        
 , ReceivedDate                                                      
 , Salesman                                                      
 , DatePromised                                
 , Notes                                                      
 , Instructions                                                      
 , Shortage                                                      
 , CompanyNo                                                      
 , ContactNo               
 , UsageNo                                                      
 , Alternate                                                      
 , OriginalCustomerRequirementNo                                                      
 , ReasonNo                                                      
 , ProductNo                                          
 , CustomerPart                                                      
 , Closed                                                      
 , ROHS                                                      
 , UpdatedBy                          
 , FullCustomerPart                                                      
 , PartWatch                                                      
 , BOM                                                      
 , BOMName                         
 , BOMNo                                                      
 , FactorySealed                                                      
 , MSL                                                  
 , PartialQuantityAcceptable                                                      
 , Obsolete                                
 , LastTimeBuy                                                      
 , RefirbsAcceptable                                                      
 , TestingRequired                                                      
 , TargetSellPrice                                                      
 , CompetitorBestOffer                                              
 , CustomerDecisionDate                                                      
 , RFQClosingDate                                                      
 , QuoteValidityRequired                                                      
 , ReqType                                                      
 , OrderToPlace                                     
 , ReqForTraceability                                                      
 , EAU                                                      
 , AlternativesAccepted                                                      
 , RepeatBusiness                                                     
 ,SupportTeamMemberNo                                                  
 --ihs code start                                             
--,CountryOfOrigin                                                
,CountryOfOriginNo                                                
,LifeCycleStage                                                
,HTSCode                                                
,AveragePrice                                                
,Packing                                                
,PackagingSize                                               
,Descriptions                              
,IHSPartsNo                                         
,ihsCurrencyCode                                  
,IHSProduct   
,ECCNCode                                        
 --ihs code end                                                    
 ,REQStatus        
 , AS6081--[004]        
)                                                      
VALUES                                               
 ( @CustomerRequirementNumber                                                      
 , @ClientNo                                                      
 , dbo.ufn_get_fullpart(@Part)                                                      
 , @Part                                     
 , @ManufacturerNo                                                      
 , @DateCode                                                      
 , @PackageNo                                                      
 , @Quantity                                                      
 , @Price                               
 , @CurrencyNo    
   /*[005] Code START*/  
-- , @ReceivedDate         
  ,getdate()   
  /*[005] Code END*/  
 , @Salesman                                                      
 , @DatePromised                                                      
 , @Notes                                                      
 , @Instructions                                              
 , @Shortage                                                      
 , @CompanyNo                                                      
 , @ContactNo                                                      
 , @UsageNo                                                      
 , @Alternate                                                      
 , @OriginalCustomerRequirementNo                                                      
 , @ReasonNo                                                 
 , @ProductNo                                                      
 , @CustomerPart                                                      
 , @Closed                                                      
 , @ROHS                                                      
 , @UpdatedBy                                                      
 , dbo.ufn_get_fullpart(@CustomerPart)                                                      
 , @PartWatch                                                      
 , @BOM                                                      
 , @BOMName                                                      
 , @BOMNo                                                      
 , @FactorySealed                                                      
 , @MSL                                                      
 , @PartialQuantityAcceptable                                                   
 , @Obsolete                                                      
 , @LastTimeBuy                                                      
 , @RefirbsAcceptable                                                      
 , @TestingRequired                                                      
 , @TargetSellPrice                                                      
 , @CompetitorBestOffer                                                      
 , @CustomerDecisionDate                                                      
 , @RFQClosingDate                                                      
 , @QuoteValidityRequired                                                      
 , @Type                                                      
 , @OrderToPlace                                                      
 , @RequirementForTraceability                                    
 , @EAU                                                      
 , @AlternativesAccepted                                                      
 , @RepeatBusiness                                                      
 , @SupportTeamMemberNo                                                 
 --, @CountryOfOrigin                                                
 , @CountryOfOriginNo  
 , @LifeCycleStage                                                
 , @HTSCode         
 , @AveragePrice                                                
 , @Packing                                                
 , @PackagingSize                                              
 ,@Descriptions        
 ,@IHSPartsNo                                            
 ,@IHSCurrencyCode                      
 ,@IHSProduct                                    
 ,@ECCNCode                             
 ,1             
 , @AS6081--[004]        
)                                                      
END                                                      
                           
SET @CustomerRequirementId = SCOPE_IDENTITY()                                     
                                                  
-- SG 27.10.2017 Add logging from trigger                                                      
INSERT INTO dbo.tbActivity                                                      
 ( TableName                                                      
 , KeyNo                                                      
 , ClientNo                                                      
 , UpdatedBy                                                      
)                                                      
VALUES                                                      
 ( 'CustomerRequirement'                                                      
 , @CustomerRequirementId                                                      
 , @ClientNo                                                      
 , @UpdatedBy                                        
)                                                      
                                                      
INSERT INTO dbo.tbCommunicationLog                                                      
 ( SystemDocumentNo                                                      
 , ContactNo                                                      
 , CompanyNo                            
 , KeyNo                                                
 , DocumentNumber                                                      
 , UpdatedBy                                                      
 )                  
VALUES                                                      
 ( 1 -- Cus Req                                                      
 , @ContactNo                                                      
 , @CompanyNo                                                      
 , @CustomerRequirementId                                                      
 , @CustomerRequirementNumber                                   
 , @UpdatedBy                                                       
)                           
if(@ECCNNo is not null)                        
begin                        
declare @checkdublicate int=0                             
set @checkdublicate=(select count(*) from tbPartEccnMapped where  Part=@Part )                                         
if(@checkdublicate=0)                                
begin                                  
INSERT INTO dbo.tbPartEccnMapped                                                      
 ( Part                                                      
 , ECCNNo                                                      
 , ECCNCode                            
 , ClientNo                                                
 , UpdatedBy                                                      
                              
 )                                                      
VALUES                                                      
 ( @Part                                                      
 , @ECCNNo                                                      
 , @ECCNCode                                                      
 , @ClientNo                                                      
 , @UpdatedBy                                                       
)                                      
end                
 --------------------------------for add log entry into Division header when update the record-----------------------------                    
    declare @REQ_Number varchar(100)=@CustomerRequirementNumber                
  declare @SectionName varchar(50) = 'CustomerRequirementECCN'                      
    declare @SubSectionName varchar(50) = 'ECCN'                      
    declare @ActionName   varchar(10) = 'Print'                      
    declare @DocumentNo     int     = @CustomerRequirementId                      
    --declare @Detail     nvarchar(max) = 'Action¦¦' +' Requirement Added  with this ECCN Code  ( ' + @ECCNCode + ' ) and ( REQ Number : ' +@REQ_Number + ' , ' + ' PartNo : ' + @part + ' ) '                     
 declare @Detail     nvarchar(max) = 'Action¦¦' +' CUSTOMER REQUIREMENT ADDED  WITH THIS ECCN CODE  ( ' + @ECCNCode + ' )'         
    declare @PrintDocumentLogId  int =NULL                     
   -----------------------------------------------------------------------                      
   EXEC [dbo].[usp_insert_PrintEmailLog]                         
   @SectionName                       
       , @SubSectionName                      
       , @ActionName                         
       , @DocumentNo                         
       , @Detail                           
       , @UpdatedBy                        
       , @PrintDocumentLogId                                     
  -----------------------------------------                       
end; 
GO


