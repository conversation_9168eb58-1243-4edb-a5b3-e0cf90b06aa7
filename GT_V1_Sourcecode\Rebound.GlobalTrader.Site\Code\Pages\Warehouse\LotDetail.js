Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlLotItems:function(){return this._ctlLotItems},set_ctlLotItems:function(n){this._ctlLotItems!==n&&(this._ctlLotItems=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addGotData(Function.createDelegate(this,this.ctlMainInfo_GotData));Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._element&&$clearHandlers(this._element),this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlLotItems&&this._ctlLotItems.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlPageTitle=null,this._ctlLotItems=null,this._ctlMainInfo=null,Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.callBaseMethod(this,"dispose"))},ctlMainInfo_GotData:function(){this._ctlPageTitle.updateTitle(this._ctlMainInfo.getFieldValue("ctlName"))}};Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.LotDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);