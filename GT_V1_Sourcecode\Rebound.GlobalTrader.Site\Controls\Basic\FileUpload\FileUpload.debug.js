///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - add full dispose
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for different section
[002]      Vinay           28/2/2014     CR:- Image Utility 
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FileUpload");

Rebound.GlobalTrader.Site.Controls.FileUpload = function(el) {
    Rebound.GlobalTrader.Site.Controls.FileUpload.initializeBase(this, [el]);
    this._win = null;
    this._doc = null;
    this._strErrorMessage = "";
    this._strUploadedFilename = "";
    this._intTimeoutSeconds = 10;
    this._aryAllowedExtensions = [];
    this._intTimeoutID = -1;
    // [001] code start
    this._strSectionName = "";
    // [001] code end
    //[002] code start
    this._intDocumentId = 0;
    //[002] code end
    this._strExcel = "";
};

Rebound.GlobalTrader.Site.Controls.FileUpload.prototype = {
    get_ifmUpload: function() { return this._ifmUpload; }, set_ifmUpload: function(value) { if (this._ifmUpload !== value) this._ifmUpload = value; },
    get_strAllowedExtensions: function() { return this._strAllowedExtensions; }, set_strAllowedExtensions: function(value) { if (this._strAllowedExtensions !== value) this._strAllowedExtensions = value; },
    get_dblMaxFileSizeInMb: function() { return this._dblMaxFileSizeInMb; }, set_dblMaxFileSizeInMb: function(value) { if (this._dblMaxFileSizeInMb !== value) this._dblMaxFileSizeInMb = value; },
    // [001] code start
    get_strSectionName: function() { return this._strSectionName; }, set_strSectionName: function(value) { if (this._strSectionName !== value) this._strSectionName = value; },
    // [001] code end
    get_strExcel: function() { return this._strExcel; }, set_strExcel: function(value) { if (this._strExcel !== value) this._strExcel = value; },

    addFileNotSelected: function(handler) { this.get_events().addHandler("FileNotSelected", handler); },
    removeFileNotSelected: function(handler) { this.get_events().removeHandler("FileNotSelected", handler); },
    onFileNotSelected: function() {
        var handler = this.get_events().getHandler("FileNotSelected");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addFileTooBig: function(handler) { this.get_events().addHandler("FileTooBig", handler); },
    removeFileTooBig: function(handler) { this.get_events().removeHandler("FileTooBig", handler); },
    onFileTooBig: function() {
        var handler = this.get_events().getHandler("FileTooBig");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addUploadComplete: function(handler) { this.get_events().addHandler("UploadComplete", handler); },
    removeUploadComplete: function(handler) { this.get_events().removeHandler("UploadComplete", handler); },
    onUploadComplete: function() {
        var handler = this.get_events().getHandler("UploadComplete");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addUploadFailed: function(handler) { this.get_events().addHandler("UploadFailed", handler); },
    removeUploadFailed: function(handler) { this.get_events().removeHandler("UploadFailed", handler); },
    onUploadFailed: function() {
        var handler = this.get_events().getHandler("UploadFailed");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addTimeout: function(handler) { this.get_events().addHandler("Timeout", handler); },
    removeTimeout: function(handler) { this.get_events().removeHandler("Timeout", handler); },
    onTimeout: function() {
        var handler = this.get_events().getHandler("Timeout");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addFileNotAllowedType: function(handler) { this.get_events().addHandler("FileNotAllowedType", handler); },
    removeFileNotAllowedType: function(handler) { this.get_events().removeHandler("FileNotAllowedType", handler); },
    onFileNotAllowedType: function() {
        var handler = this.get_events().getHandler("FileNotAllowedType");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.FileUpload.callBaseMethod(this, "initialize");
        this._win = this._ifmUpload.contentWindow;
        this._doc = this._ifmUpload.contentDocument;
        this._aryAllowedExtensions = $R_FN.singleStringToArray(this._strAllowedExtensions);
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this.get_element()) $clearHandlers(this.get_element());
        this.clearTimeout();
        this._win = null;
        this._doc = null;
        this._aryAllowedExtensions = null;
        this._ifmUpload = null;
        this._strErrorMessage = null;
        this._strUploadedFilename = null;
        this._intTimeoutSeconds = null;
        this._intTimeoutID = null;
        // [001] code start
        this._strSectionName = null;
        //[001] code end
        this._strExcel = null;
        //[002] code start
        this._intDocumentId = null;
        //[002] code end
        Rebound.GlobalTrader.Site.Controls.FileUpload.callBaseMethod(this, "dispose");
        this.isDisposed = true;
    },


    setupFrame: function() {
        this._win = this._ifmUpload.contentWindow;
        this._doc = this._ifmUpload.contentDocument;
        this._win.setFileUploadID(this._element.id);
    },

    doUpload: function() {
        this.setupFrame();
        if (this.checkAllowedType()) {
            this._win.doSubmit();
            this._intTimeoutID = setTimeout(Function.createDelegate(this, this.checkPageLoaded), this._intTimeoutSeconds * 1000);
        } else {
            this._strErrorMessage = $R_RES.FileUploadNotAllowedType;
            this.onFileNotAllowedType();
        }
    },

    checkPageLoaded: function() {
        this.clearTimeout();
        var blnOK = true;
        try {
            blnOK = this._win.blnPageLoaded;
        } catch (e) {
            blnOK = false;
        }
        if (!blnOK) {
            this._strErrorMessage = $R_RES.FileUploadFailed;
            this.onTimeout();
        }
    },

    getValue: function() {
        var file = this.getFileControl();
        var varReturn = (file) ? file.value : "";
        file = null;
        return varReturn;
    },

    setValue: function(v) {
        var file = this.getFileControl();
        file.value = v;
        file = null;
    },

    // [001] code start
    reset: function() {
       
        this._win.location = String.format("FileUpload.aspx?mxs={0}&section={1}&docId={2}&excel={3}", this._dblMaxFileSizeInMb, this._strSectionName, this._intDocumentId, this._strExcel);
    },
    // [001] code end

    checkEntered: function() {
        this.setupFrame();
        var file = this.getFileControl();
        var blnReturn = (file) ? (file.value.length > 0) : false;
        file = null;
        return blnReturn;
    },

    getFileControl: function() {
        this.setupFrame();
        return this._doc.getElementById("filFile");
    },

    showFieldError: function(bln) {
        try { this._win.setError(bln); } catch (ex) { }
    },

    checkAllowedType: function() {
        var blnAllowed = false;
        var file = this.getFileControl();
        for (var i = 0, l = this._aryAllowedExtensions.length; i < l; i++) {
            if (file.value.toString().toUpperCase().endsWith("." + this._aryAllowedExtensions[i].toString().toUpperCase())) {
                blnAllowed = true;
                break;
            }
        }
        file = null;
        return blnAllowed;
    },

    clearTimeout: function() {
        if (this._intTimeoutID != -1) clearTimeout(this._intTimeoutID);
    }

};

Rebound.GlobalTrader.Site.Controls.FileUpload.registerClass("Rebound.GlobalTrader.Site.Controls.FileUpload", Sys.UI.Control, Sys.IDisposable);
