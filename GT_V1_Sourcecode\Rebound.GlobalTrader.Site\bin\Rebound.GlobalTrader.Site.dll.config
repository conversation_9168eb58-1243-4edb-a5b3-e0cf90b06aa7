﻿<?xml version="1.0"?>
<configuration>
  <configSections>
    <section name="ReboundUIConfiguration" type="Rebound.GlobalTrader.Site.Settings.ReboundUIConfigSection, Rebound.GlobalTrader.Site"/>
    <section name="globalTraderDAL" type="Rebound.GlobalTrader.DAL.GlobalTraderDALSection"/>
  </configSections>
  <!--GT connection string dependancy code start-->
  <!--<globalTraderDAL defaultConnectionStringName="LocalSqlServer" mediaConnectionStringName="MediaSqlServer" GTConnectionStringName="GTSqlServer">-->
  <globalTraderDAL defaultConnectionStringName="LocalSqlServer" mediaConnectionStringName="LocalSqlServer" GTConnectionStringName="LocalSqlServer">
    <!--GT connection string dependancy code end-->
    <databaseSettings databaseType="Sql"/>
  </globalTraderDAL>
  <connectionStrings configSource="User\Settings\Connection.config"/>
  <appSettings configSource="User\Settings\AppSettings.config"/>
  <ReboundUIConfiguration configSource="Settings\UI.config"/>
  <system.net>
    <mailSettings>
      <smtp configSource="User\Settings\SMTP.config"/>
    </mailSettings>
  </system.net>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.6" />
      </system.Web>
  -->
  <system.web>
    <httpRuntime fcnMode="Disabled" requestValidationMode="2.0" enableVersionHeader="false"/>
    <!--<httpRuntime executionTimeout="240" maxRequestLength="20480" />-->
    <!--<sessionState mode="StateServer" >
    </sessionState>-->
    <machineKey validationKey="17F6A5A4A4344B23DB3363FB24E0C81388110D742002B02F707658F2F38817920F744419096C3D2397350A63D8FDD3638A1A21FB14825F28D0A7BED7953350BA" decryptionKey="DE446F8682EAEB9F42C1083A91F221C42ADEC338D97FC189" validation="SHA1"/>
    <globalization uiCulture="en-GB" culture="en-GB" responseEncoding="utf-8"/>
    <pages theme="Original" validateRequest="false" enableViewState="false" enableEventValidation="false" enableSessionState="true" autoEventWireup="false" controlRenderingCompatibilityVersion="3.5" clientIDMode="AutoID">
      <namespaces>
        <!--Added by Rahil-->
        <add namespace="System.Web.Helpers"/>
        <add namespace="System.Web.Mvc"/>
        <add namespace="System.Web.Mvc.Ajax"/>
        <add namespace="System.Web.Mvc.Html"/>
        <add namespace="System.Web.Routing"/>
        <add namespace="System.Web.WebPages"/>
        <!--Added by Rahil-->
        <!--<add namespace="System.Web.Optimization" />-->
      </namespaces>
      <controls>
        <!-- AJAX Framework -->
        <add tagPrefix="ajax" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add tagPrefix="ajax" namespace="System.Web.UI.WebControls" assembly="System.Web.Extensions, Version=3.5.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add tagPrefix="ajax" namespace="AjaxControlToolkit" assembly="AjaxControlToolkit"/>
        <!-- Main Controls -->
        <add tagPrefix="Rebound" namespace="Rebound.GlobalTrader.Site.Controls" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundDropDown" namespace="Rebound.GlobalTrader.Site.Controls.DropDowns" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundPages" namespace="Rebound.GlobalTrader.Site.Pages" assembly="Rebound.GlobalTrader.Site"/>
        <!-- Rebound UI -->
        <add tagPrefix="ReboundUI" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls"/>
        <add tagPrefix="ReboundUI_Nugget" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.Nuggets"/>
        <add tagPrefix="ReboundUI_LeftNugget" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.LeftNuggets"/>
        <add tagPrefix="ReboundUI_LeftNugget_DataList" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists"/>
        <add tagPrefix="ReboundUI_Form" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.Forms"/>
        <add tagPrefix="ReboundUI_Table" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.Tables"/>
        <add tagPrefix="ReboundUI_DropDown" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.DropDowns"/>
        <add tagPrefix="ReboundUI_DataListNugget" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.DataListNuggets"/>
        <add tagPrefix="ReboundUI_FilterDataItemRow" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows"/>
        <add tagPrefix="ReboundUI_FormFieldCollection" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.FormFieldCollections"/>
        <add tagPrefix="ReboundUI_ItemSearch" assembly="Rebound.GlobalTrader.Site" namespace="Rebound.GlobalTrader.Site.Controls.ItemSearch"/>
        <!-- Tables -->
        <add tagPrefix="ReboundTable" namespace="Rebound.GlobalTrader.Site.Controls.Tables" assembly="Rebound.GlobalTrader.Site"/>
        <!-- Home Nuggets -->
        <add tagPrefix="ReboundHomeNugget" namespace="Rebound.GlobalTrader.Site.Controls.HomeNuggets" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundHomeNugget" tagName="ApprovedCustomersOnStop" src="~/controls/HomeNuggets/ApprovedCustomersOnStop/ApprovedCustomersOnStop.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyOpenSalesOrders" src="~/controls/HomeNuggets/MyOpenSalesOrders/MyOpenSalesOrders.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyStatistics" src="~/controls/HomeNuggets/MyStatistics/MyStatistics.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="ReceivedOrders" src="~/controls/HomeNuggets/ReceivedOrders/ReceivedOrders.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="TodaysShippedOrders" src="~/controls/HomeNuggets/TodaysShippedOrders/TodaysShippedOrders.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyRecentlyShippedOrders" src="~/controls/HomeNuggets/MyRecentlyShippedOrders/MyRecentlyShippedOrders.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyOpenRequirements" src="~/controls/HomeNuggets/MyOpenRequirements/MyOpenRequirements.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyApprovedCustomersOnStop" src="~/controls/HomeNuggets/MyApprovedCustomersOnStop/MyApprovedCustomersOnStop.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="StockItemsBelowTheMinimumQuantity" src="~/controls/HomeNuggets/StockItemsBelowTheMinimumQuantity/StockItemsBelowTheMinimumQuantity.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyOpenPurchaseOrders" src="~/controls/HomeNuggets/MyOpenPurchaseOrders/MyOpenPurchaseOrders.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="SalesOrdersReadyToShip" src="~/controls/HomeNuggets/SalesOrdersReadyToShip/SalesOrdersReadyToShip.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="AllPurchaseOrdersDueIn" src="~/controls/HomeNuggets/AllPurchaseOrdersDueIn/AllPurchaseOrdersDueIn.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="InternalPurchaseOrdersDueIn" src="~/controls/HomeNuggets/InternalPurchaseOrders/InternalPurchaseOrders.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="Bom" src="~/controls/HomeNuggets/Bom/Bom.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="BomManager" src="~/controls/HomeNuggets/BomManager/BomManager.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="TodayOpenPurchaseOrders" src="~/controls/HomeNuggets/TodayOpenPurchaseOrders/TodayOpenPurchaseOrders.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="UnProcessSalesOrders" src="~/controls/HomeNuggets/UnProcessSalesOrders/UnProcessSalesOrders.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyOpenQuotes" src="~/controls/HomeNuggets/MyOpenQuotes/MyOpenQuotes.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="AllStatistics" src="~/controls/HomeNuggets/AllStatistics/AllStatistics.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="TableActivity" src="~/controls/HomeNuggets/TableActivity/TableActivity.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyRecentActivity" src="~/controls/HomeNuggets/MyRecentActivity/MyRecentActivity.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyMessages" src="~/controls/HomeNuggets/MyMessages/MyMessages.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyToDoList" src="~/controls/HomeNuggets/MyToDoList/MyToDoList.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="PartsBeingOrderedToday" src="~/controls/HomeNuggets/PartsBeingOrderedToday/PartsBeingOrderedToday.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="TopSalespersons" src="~/controls/HomeNuggets/TopSalespersons/TopSalespersons.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="UncheckedIPO" src="~/controls/HomeNuggets/UncheckedIPO/UncheckedIPO.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="CustomerOrderValue" src="~/controls/HomeNuggets/CustomerOrderValue/CustomerOrderValue.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="PODeliveryStatus" src="~/controls/HomeNuggets/PODeliveryStatus/PODeliveryStatus.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="BOMImport" src="~/controls/Nuggets/BOMImport/BOMImport.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="OpenSupplierPOApproval" src="~/controls/HomeNuggets/OpenSupplierPOApproval/OpenSupplierPOApproval.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyGIQueries" src="~/controls/HomeNuggets/MyGIQueries/MyGIQueries.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="MyQualityGIQueries" src="~/controls/HomeNuggets/MyQualityGIQueries/MyQualityGIQueries.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="PowerBIActivity" src="~/controls/HomeNuggets/PowerBIActivity/PowerBIActivity.ascx"/>
        <add tagPrefix="ReboundHomeNugget" tagName="PowerBiSalesDashboard" src="~/controls/HomeNuggets/PowerBiSalesDashboard/PowerBiSalesDashboard.ascx"/>
        <!-- Data List Nuggets -->
        <add tagPrefix="ReboundDataListNugget" namespace="Rebound.GlobalTrader.Site.Controls.DataListNuggets" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Companies" src="~/controls/DataListNuggets/Companies/Companies.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Contacts" src="~/controls/DataListNuggets/Contacts/Contacts.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Manufacturers" src="~/controls/DataListNuggets/Manufacturers/Manufacturers.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="PurchaseOrders" src="~/controls/DataListNuggets/PurchaseOrders/PurchaseOrders.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="PurchaseOrdersReceive" src="~/controls/DataListNuggets/PurchaseOrdersReceive/PurchaseOrdersReceive.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="SalesOrders" src="~/controls/DataListNuggets/SalesOrders/SalesOrders.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="SalesOrdersShip" src="~/controls/DataListNuggets/SalesOrdersShip/SalesOrdersShip.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="CRMAs" src="~/controls/DataListNuggets/CRMAs/CRMAs.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="CRMAsReceive" src="~/controls/DataListNuggets/CRMAsReceive/CRMAsReceive.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="SRMAs" src="~/controls/DataListNuggets/SRMAs/SRMAs.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="SRMAsShip" src="~/controls/DataListNuggets/SRMAsShip/SRMAsShip.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Quotes" src="~/controls/DataListNuggets/Quotes/Quotes.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Invoices" src="~/controls/DataListNuggets/Invoices/Invoices.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="InvoiceLines" src="~/controls/DataListNuggets/InvoiceLines/InvoiceLines.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="ClientInvoice" src="~/controls/DataListNuggets/ClientInvoice/ClientInvoice.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="CustomerRequirements" src="~/controls/DataListNuggets/CustomerRequirements/CustomerRequirements.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="AllStock" src="~/controls/DataListNuggets/AllStock/AllStock.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Services" src="~/controls/DataListNuggets/Services/Services.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Lots" src="~/controls/DataListNuggets/Lots/Lots.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Divisions" src="~/controls/DataListNuggets/Divisions/Divisions.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="PurchaseRequisitions" src="~/controls/DataListNuggets/PurchaseRequisitions/PurchaseRequisitions.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="TableActivity" src="~/controls/DataListNuggets/TableActivity/TableActivity.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="GoodsIn" src="~/controls/DataListNuggets/GoodsIn/GoodsIn.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="IHSCatalogue" src="~/controls/DataListNuggets/IHSCatalogue/IHSCatalogue.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Credits" src="~/controls/DataListNuggets/Credits/Credits.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="CreditBulk" src="~/controls/DataListNuggets/CreditBulk/CreditBulk.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="Debits" src="~/controls/DataListNuggets/Debits/Debits.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="DabitBulk" src="~/controls/DataListNuggets/DabitBulk/DabitBulk.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="ReceivedPurchaseOrders" src="~/controls/DataListNuggets/ReceivedPurchaseOrders/ReceivedPurchaseOrders.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="CommunicationLog" src="~/controls/DataListNuggets/CommunicationLog/CommunicationLog.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="SupplierInvoice" src="~/controls/DataListNuggets/SupplierInvoice/SupplierInvoice.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="PurchaseQuotes" src="~/controls/DataListNuggets/PurchaseQuotes/PurchaseQuotes.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CommunicationLog_AddEdit" src="~/controls/DataListNuggets/CommunicationLog/AddEdit/CommunicationLog_AddEdit.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="NPR" src="~/controls/DataListNuggets/NPR/NPR.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="CustomerRequirementsPrint" src="~/controls/DataListNuggets/CustomerRequirementsPrint/CustomerRequirementsPrint.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="BOM" src="~/controls/DataListNuggets/BOM/BOM.ascx"/>
		<add tagPrefix="ReboundDataListNugget" tagName="ProspectiveOffers" src="~/controls/DataListNuggets/ProspectiveOffers/ProspectiveOffers.ascx"/>  
        <add tagPrefix="ReboundNugget" tagName="BOMMainInfo" src="~/controls/Nuggets/BOMMainInfo/BOMMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMMainInfo_Edit" src="~/controls/Nuggets/BOMMainInfo/Edit/BOMMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMMainInfo_Delete" src="~/controls/Nuggets/BOMMainInfo/Delete/BOMMainInfo_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMMainInfo_Notify" src="~/controls/Nuggets/BOMMainInfo/Notify/BOMMainInfo_Notify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMMainInfo_Release" src="~/controls/Nuggets/BOMMainInfo/Release/BOMMainInfo_Release.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Confirm" src="~/controls/Nuggets/BOMMainInfo/Confirm/Confirm.ascx"/>
        <!--BOMPVV code start-->
        <add tagPrefix="ReboundNugget" tagName="BOMPVV" src="~/controls/Nuggets/BOMPVV/BOMPVV.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMPVV_Edit" src="~/controls/Nuggets/BOMPVV/Edit/BOMPVV_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMPVV_Delete" src="~/controls/Nuggets/BOMPVV/Delete/BOMPVV_Delete.ascx"/>
        <!--BOMPVV code end-->
        <add tagPrefix="ReboundNugget" tagName="BOMItems" src="~/controls/Nuggets/BOMItems/BOMItems.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMItems_Confirm" src="~/controls/Nuggets/BOMItems/Confirm/BOMItems_Confirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMItems_Add" src="~/controls/Nuggets/BOMItems/Add/BOMItems_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMItems_Delete" src="~/controls/Nuggets/BOMItems/Delete/BOMItems_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMItems_UnRelease" src="~/controls/Nuggets/BOMItems/UnRelease/BOMItems_UnRelease.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMItems_AddExpedite" src="~/controls/Nuggets/BOMItems/AddExpedite/BOMItems_AddExpedite.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="BOMLines" src="~/controls/DataListNuggets/BOMLines/BOMLines.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="BOMManagerLines" src="~/controls/DataListNuggets/BOMManagerLines/BOMManagerLines.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="BOMManagerLinesAssign" src="~/controls/DataListNuggets/BOMManagerLinesAssign/BOMManagerLinesAssign.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ConfirmClose" src="~/controls/Nuggets/BOMMainInfo/ConfirmClose/ConfirmClose.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMItems_NoBidConfirm" src="~/controls/Nuggets/BOMItems/NoBidConfirm/BOMItems_NoBidConfirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMImportSourcingResult" src="~/controls/Nuggets/BOMItems/BOMImportSourcingResult/BOMImportSourcingResult.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMMainInfo_NoBid" src="~/controls/Nuggets/BOMMainInfo/NoBid/BOMMainInfo_NoBid.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMMainInfo_AddExpedite" src="~/controls/Nuggets/BOMMainInfo/AddExpedite/BOMMainInfo_AddExpedite.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMMainInfo_CrossMatch" src="~/controls/Nuggets/BOMMainInfo/CrossMatch/BOMMainInfo_CrossMatch.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientBomConfirm" src="~/controls/Nuggets/ClientImportBOMMainInfo/Confirm/ClientImportConfirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SaveAsHUBRFQ" src="~/controls/Nuggets/ClientImportBOMMainInfo/SaveAsHUBRFQ/SaveAsHUBRFQ.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="ShortShipment" src="~/controls/DataListNuggets/ShortShipment/ShortShipment.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="ToDoList" src="~/controls/DataListNuggets/ToDoList/ToDoList.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="ToDoList" src="~/controls/DataListNuggets/ToDoList/ToDoList.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ToDoList_Confirm" src="~/controls/DataListNuggets/ToDoList/Confirm/ToDoList_Confirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ToDoList_Edit" src="~/controls/DataListNuggets/ToDoList/Edit/ToDoList_Edit.ascx"/>
        <add tagPrefix="ReboundDataListNugget" tagName="SupplierMfrSearch" src="~/controls/DataListNuggets/SupplierMfrSearch/SupplierMfrSearch.ascx"/>
        <!-- start code by umendra -->
        <add tagPrefix="ReboundDataListNugget" tagName="CustomerRequirementsBOMImport" src="~/controls/DataListNuggets/CustomerRequirementsBOMImport/CustomerRequirementsBOMImport.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ClientImportBOMMainInfo" src="~/controls/Nuggets/ClientImportBOMMainInfo/ClientImportBOMMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientImportBOMMainInfo_Edit" src="~/controls/Nuggets/ClientImportBOMMainInfo/Edit/ClientImportBOMMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ClientBOMItems" src="~/controls/Nuggets/ClientBOMItems/ClientBOMItems.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientBOMItems_Add" src="~/controls/Nuggets/ClientBOMItems/Add/ClientBOMItems_Add.ascx"/>
        <!-- end code by umendra -->
        <add tagPrefix="ReboundDataListNugget" tagName="OGELLinesExport" src="~/controls/DataListNuggets/OGELLinesExport/OGELLinesExport.ascx"/>
        <!-- Data List Nugget Filters -->
        <add tagPrefix="ReboundDataListNuggetFilter" namespace="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Filters" assembly="Rebound.GlobalTrader.Site"/>
        <!-- Filter Data Item Rows -->
        <add tagPrefix="ReboundFilterDataItemRow" namespace="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows" assembly="Rebound.GlobalTrader.Site"/>
        <!-- Nuggets and Forms: General Stuff -->
        <add tagPrefix="ReboundNugget" namespace="Rebound.GlobalTrader.Site.Controls.Nuggets" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundForm" namespace="Rebound.GlobalTrader.Site.Controls.Forms" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundFormFieldCollection" namespace="Rebound.GlobalTrader.Site.Controls.FormFieldCollections" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundFormFieldSet" tagName="SendMailMessage" src="~/controls/Forms/SendMailMessage/SendMailMessage.ascx"/>
        <add tagPrefix="ReboundFormFieldSet" tagName="SendMailMessageSupplier" src="~/controls/Forms/SendMailMessage/SendMailMessageSupplier.ascx"/>
        <add tagPrefix="ReboundFormFieldSet" tagName="SendApprovalRequest" src="~/controls/Forms/SendApprovalRequest/SendApprovalRequest.ascx"/>
        <add tagPrefix="ReboundFormFieldSet" tagName="NotifyMailMessageReqToHUB" src="~/controls/Forms/NotifyMailMessageReqToHUB/NotifyMailMessageReqToHUB.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="EditNotes" src="~/controls/Nuggets/EditNotes.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ContactExtendedInfo_Edit" src="~/controls/Nuggets/ContactExtendedInfo/Edit/ContactExtendedInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="Feedback" src="~/controls/Nuggets/Feedback/Feedback.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Feedback_Add" src="~/controls/Nuggets/Feedback/Add/Feedback_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="Report" src="~/controls/Nuggets/Report/Report.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EmailDocument" src="~/controls/Forms/EmailDocument/EmailDocument.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ControlStrip" src="~/controls/Nuggets/ControlStrip/ControlStrip.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Invoices_Confirm" src="~/controls/DataListNuggets/Invoices/Confirm/Invoices_Confirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CreditBulk_Confirm" src="~/controls/DataListNuggets/CreditBulk/Confirm/CreditBulk_Confirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DebitBulk_Confirm" src="~/controls/DataListNuggets/DabitBulk/Confirm/DabitBulk_Confirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOM_Confirm" src="~/controls/DataListNuggets/BOM/Confirm/BOM_Confirm.ascx"/>
        <add tagPrefix="ReboundFormFieldSet" tagName="IndustryTypeList" src="~/controls/Forms/IndustryTypeList/IndustryTypeList.ascx"/>
        <!-- Nuggets and Forms: Contact Section -->
        <add tagPrefix="ReboundNugget" tagName="CompanyMainInfo" src="~/controls/Nuggets/CompanyMainInfo/CompanyMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyMainInfo_Edit" src="~/controls/Nuggets/CompanyMainInfo/Edit/CompanyMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CompanyAddresses" src="~/controls/Nuggets/CompanyAddresses/CompanyAddresses.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyAddresses_AddEdit" src="~/controls/Nuggets/CompanyAddresses/AddEdit/CompanyAddresses_AddEdit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyAddresses_Confirm" src="~/controls/Nuggets/CompanyAddresses/Confirm/CompanyAddresses_Confirm.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CompanyTransactions" src="~/controls/Nuggets/CompanyTransactions/CompanyTransactions.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ContactMainInfo" src="~/controls/Nuggets/ContactMainInfo/ContactMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ContactMainInfo_Edit" src="~/controls/Nuggets/ContactMainInfo/Edit/ContactMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ContactExtendedInfo" src="~/controls/Nuggets/ContactExtendedInfo/ContactExtendedInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ContactExtendedInfo_Edit" src="~/controls/Nuggets/ContactExtendedInfo/Edit/ContactExtendedInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ContactsForCompany" src="~/controls/Nuggets/ContactsForCompany/ContactsForCompany.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ContactsForCompany_Add" src="~/controls/Nuggets/ContactsForCompany/Add/ContactsForCompany_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ContactsForCompany_Delete" src="~/controls/Nuggets/ContactsForCompany/Delete/ContactsForCompany_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ContactsForCompany_MakeDefault" src="~/controls/Nuggets/ContactsForCompany/MakeDefault/ContactsForCompany_MakeDefault.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CompanyPurchasingInfo" src="~/controls/Nuggets/CompanyPurchasingInfo/CompanyPurchasingInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyPurchasingInfo_Edit" src="~/controls/Nuggets/CompanyPurchasingInfo/Edit/CompanyPurchasingInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CompanySalesInfo" src="~/controls/Nuggets/CompanySalesInfo/CompanySalesInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanySalesInfo_Edit" src="~/controls/Nuggets/CompanySalesInfo/Edit/CompanySalesInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CompanyManufacturers" src="~/controls/Nuggets/CompanyManufacturers/CompanyManufacturers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyManufacturers_AddEdit" src="~/controls/Nuggets/CompanyManufacturers/AddEdit/CompanyManufacturers_AddEdit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyManufacturers_Delete" src="~/controls/Nuggets/CompanyManufacturers/Delete/CompanyManufacturers_Delete.ascx"/>
		<add tagPrefix="ReboundForm" tagName="CompanyManufacturers_View" src="~/controls/Nuggets/CompanyManufacturers/View/CompanyManufacturers_View.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ManufacturerSuppliers" src="~/controls/Nuggets/ManufacturerSuppliers/ManufacturerSuppliers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ManufacturerSuppliers_AddEdit" src="~/controls/Nuggets/ManufacturerSuppliers/AddEdit/ManufacturerSuppliers_AddEdit.ascx"/>
		<add tagPrefix="ReboundForm" tagName="ManufacturerSuppliers_View" src="~/controls/Nuggets/ManufacturerSuppliers/View/ManufacturerSuppliers_View.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ManufacturerSuppliers_Delete" src="~/controls/Nuggets/ManufacturerSuppliers/Delete/ManufacturerSuppliers_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ManufacturerMainInfo" src="~/controls/Nuggets/ManufacturerMainInfo/ManufacturerMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ManufacturerMainInfo_Edit" src="~/controls/Nuggets/ManufacturerMainInfo/Edit/ManufacturerMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ManufacturerCompanies" src="~/controls/Nuggets/ManufacturerCompanies/ManufacturerCompanies.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ManufacturerCompanies_Add" src="~/controls/Nuggets/ManufacturerCompanies/Add/ManufacturerCompanies_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ManufacturerCompanies_Delete" src="~/controls/Nuggets/ManufacturerCompanies/Delete/ManufacturerCompanies_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CompanyAdd" src="~/controls/Nuggets/CompanyAdd/CompanyAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyAdd_Add" src="~/controls/Nuggets/CompanyAdd/Add/CompanyAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="GroupCodeCompanyAdd" src="~/controls/Nuggets/GroupCodeCompanyAdd/GroupCodeCompanyAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GroupCodeCompanyAdd_Add" src="~/controls/Nuggets/GroupCodeCompanyAdd/Add/GroupCodeCompanyAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ManufacturerAdd" src="~/controls/Nuggets/ManufacturerAdd/ManufacturerAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ManufacturerAdd_Add" src="~/controls/Nuggets/ManufacturerAdd/Add/ManufacturerAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CompanyProspects" src="~/controls/Nuggets/CompanyProspects/CompanyProspects.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyProspects_Edit" src="~/controls/Nuggets/CompanyProspects/Edit/CompanyProspects_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CompanyFinanceInfo" src="~/controls/Nuggets/CompanyFinanceInfo/CompanyFinanceInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyFinanceInfo_Link" src="~/controls/Nuggets/CompanyFinanceInfo/Link/CompanyFinanceInfo_Link.ascx"/>
        <!-- Nuggets and Forms: Warehouse Section -->
        <add tagPrefix="ReboundNugget" tagName="CRMAReceivingInfo" src="~/controls/Nuggets/CRMAReceivingInfo/CRMAReceivingInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CRMAReceivingLines" src="~/controls/Nuggets/CRMAReceivingLines/CRMAReceivingLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMALines_Receive" src="~/controls/Nuggets/CRMAReceivingLines/Receive/CRMAReceivingLines_Receive.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="GIAdd" src="~/controls/Nuggets/GIAdd/GIAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GIAdd_Add" src="~/controls/Nuggets/GIAdd/Add/GIAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="GIMainInfo" src="~/controls/Nuggets/GIMainInfo/GIMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GIMainInfo_Edit" src="~/controls/Nuggets/GIMainInfo/Edit/GIMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GIMainInfo_Notify" src="~/controls/Nuggets/GIMainInfo/Notify/GIMainInfo_Notify.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="GILines" src="~/controls/Nuggets/GILines/GILines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_Edit" src="~/controls/Nuggets/GILines/Edit/GILines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_SerailNo" src="~/controls/Nuggets/GILines/SerialNo/GILines_SerialNo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_Delete" src="~/controls/Nuggets/GILines/Delete/GILines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_Inspect" src="~/controls/Nuggets/GILines/Inspect/GILines_Inspect.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMALines_SerailNo" src="~/controls/Nuggets/CRMAReceivingLines/CRMAReceiveLines_SerialNo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMALines_Confirm" src="~/controls/Nuggets/CRMAReceivingLines/Confirm/CRMAReceiveLines_Confirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_SplitGI" src="~/controls/Nuggets/GILines/SplitGI/GILines_SplitGI.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_AddShortShipment" src="~/controls/Nuggets/GILines/AddShortShipment/GILines_AddShortShipment.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_Quarantine" src="~/controls/Nuggets/GILines/Quarantine/GILines_Quarantine.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_StartInspection" src="~/controls/Nuggets/GILines/StartInspection/GILines_StartInspection.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_CloseInspection" src="~/controls/Nuggets/GILines/CloseInspection/GILines_CloseInspection.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_ImageUpload" src="~/controls/Nuggets/GILines/ImageUpload/GILines_ImageUpload.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILInes_UploadPDF" src="~/controls/Nuggets/GILines/UploadPDF/GILInes_UploadPDF.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_LotNo" src="~/controls/Nuggets/GILines/LotNo/GILines_LotNo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_Export" src="~/controls/Nuggets/GILines/Export/GILines_Export.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="GSA" src="~/controls/Nuggets/GSA/GSA.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GSA_EditMembers" src="~/controls/Nuggets/GSA/EditMembers/GSA_EditMembers.ascx"/>
        <!--Add physical inspect control-->
        <add tagPrefix="ReboundForm" tagName="GILines_PhysicalInspect" src="~/controls/Nuggets/GILines/PhysicalInspect/GILines_PhysicalInspect.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_PrintLabel" src="~/controls/Nuggets/GILines/PrintLabel/GILines_PrintLabel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_NPRPrinted" src="~/controls/Nuggets/GILines/NPRPrinted/GILines_NPRPrinted.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="LotMainInfo" src="~/controls/Nuggets/LotMainInfo/LotMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LotMainInfo_Edit" src="~/controls/Nuggets/LotMainInfo/Edit/LotMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LotMainInfo_Delete" src="~/controls/Nuggets/LotMainInfo/Delete/LotMainInfo_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="LotItems" src="~/controls/Nuggets/LotItems/LotItems.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LotItems_Delete" src="~/controls/Nuggets/LotItems/Delete/LotItems_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LotItems_Transfer" src="~/controls/Nuggets/LotItems/Transfer/LotItems_Transfer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_PrintNiceLabel" src="~/controls/Nuggets/GILines/PrintNiceLabel/GILines_PrintNiceLabel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_PrintNiceLabelCRX" src="~/controls/Nuggets/GILines/PrintNiceLabelCRX/GILines_PrintNiceLabelCRX.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_PrintStockLabel" src="~/controls/Nuggets/GILines/PrintStockLabel/GILines_PrintStockLabel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILines_PrintRejectedLabel" src="~/controls/Nuggets/GILines/PrintRejectedLabel/GILines_PrintRejectedLabel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LotItems_StockProvision" src="~/controls/Nuggets/LotItems/StockProvision/LotItems_StockProvision.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LotItems_Confirm" src="~/controls/Nuggets/LotItems/Confirm/LotItems_Confirm.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="POReceivingInfo" src="~/controls/Nuggets/POReceivingInfo/POReceivingInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="POReceivingLines" src="~/controls/Nuggets/POReceivingLines/POReceivingLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLines_Receive" src="~/controls/Nuggets/POReceivingLines/Receive/POReceivingLines_Receive.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ServiceAllocations" src="~/controls/Nuggets/ServiceAllocations/ServiceAllocations.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ServiceAllocations_Deallocate" src="~/controls/Nuggets/ServiceAllocations/Deallocate/ServiceAllocations_Deallocate.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ServiceMainInfo" src="~/controls/Nuggets/ServiceMainInfo/ServiceMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ServiceMainInfo_Edit" src="~/controls/Nuggets/ServiceMainInfo/Edit/ServiceMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ServiceMainInfo_Delete" src="~/controls/Nuggets/ServiceMainInfo/Delete/ServiceMainInfo_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ServiceAdd" src="~/controls/Nuggets/ServiceAdd/ServiceAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ServiceAdd_Add" src="~/controls/Nuggets/ServiceAdd/Add/ServiceAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOShippingInfo" src="~/controls/Nuggets/SOShippingInfo/SOShippingInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOShippingLines" src="~/controls/Nuggets/SOShippingLines/SOShippingLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Ship" src="~/controls/Nuggets/SOShippingLines/Ship/SOShippingLines_Ship.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_SerailNo" src="~/controls/Nuggets/SOShippingLines/SerialNo/SOShippingLines_SerialNo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Confirm" src="~/controls/Nuggets/SOShippingLines/Confirm/SOShippingLines_Confirm.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="Shipping" src="~/controls/Nuggets/Shipping/Shipping.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="StockMainInfo" src="~/controls/Nuggets/StockMainInfo/StockMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMainInfo_Edit" src="~/controls/Nuggets/StockMainInfo/Edit/StockMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMainInfo_Quarantine" src="~/controls/Nuggets/StockMainInfo/Quarantine/StockMainInfo_Quarantine.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMainInfo_Split" src="~/controls/Nuggets/StockMainInfo/Split/StockMainInfo_Split.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMainInfo_StockProvision" src="~/controls/Nuggets/StockMainInfo/StockProvision/StockMainInfo_StockProvision.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMainInfo_PrintNiceLabel" src="~/controls/Nuggets/StockMainInfo/PrintNiceLabel/StockMainInfo_PrintNiceLabel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMainInfo_PrintNiceLabelCRX" src="~/controls/Nuggets/StockMainInfo/PrintNiceLabelCRX/StockMainInfo_PrintNiceLabelCRX.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMainInfo_PrintExcessLabel" src="~/controls/Nuggets/StockMainInfo/PrintExcessLabel/StockMainInfo_PrintExcessLabel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMainInfo_PrintRejectedLabel" src="~/controls/Nuggets/StockMainInfo/PrintRejectedLabel/StockMainInfo_PrintRejectedLabel.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="StockImages" src="~/controls/Nuggets/StockImages/StockImages.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockImages_Add" src="~/controls/Nuggets/StockImages/Add/StockImages_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockImages_Delete" src="~/controls/Nuggets/StockImages/Delete/StockImages_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="StockAllocations" src="~/controls/Nuggets/StockAllocations/StockAllocations.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockAllocations_Deallocate" src="~/controls/Nuggets/StockAllocations/Deallocate/StockAllocations_Deallocate.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="StockLog" src="~/controls/Nuggets/StockLog/StockLog.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="StockRelatedStock" src="~/controls/Nuggets/StockRelatedStock/StockRelatedStock.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="StockTransactions" src="~/controls/Nuggets/StockTransactions/StockTransactions.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="StockAdd" src="~/controls/Nuggets/StockAdd/StockAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockAdd_Add" src="~/controls/Nuggets/StockAdd/Add/StockAdd_Add.ascx"/>
		<add tagPrefix="ReboundNugget" tagName="IHSAdd" src="~/controls/Nuggets/IHSAdd/IHSAdd.ascx"/>
		<add tagPrefix="ReboundForm" tagName="IHSAdd_Add" src="~/controls/Nuggets/IHSAdd/Add/IHSAdd_Add.ascx"/>  
		  
        <add tagPrefix="ReboundNugget" tagName="LotAdd" src="~/controls/Nuggets/LotAdd/LotAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LotAdd_Add" src="~/controls/Nuggets/LotAdd/Add/LotAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SRMAShippingInfo" src="~/controls/Nuggets/SRMAShippingInfo/SRMAShippingInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SRMAShippingLines" src="~/controls/Nuggets/SRMAShippingLines/SRMAShippingLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMALines_Ship" src="~/controls/Nuggets/SRMAShippingLines/Ship/SRMAShippingLines_Ship.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SupplierInvoiceMainInfo" src="~/controls/Nuggets/SupplierInvoiceMainInfo/SupplierInvoiceMainInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SupplierInvoiceLines" src="~/controls/Nuggets/SupplierInvoiceLines/SupplierInvoiceLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SupplierInvoiceMainInfo_Edit" src="~/controls/Nuggets/SupplierInvoiceMainInfo/Edit/SupplierInvoiceMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SupplierInvoiceMainInfo_Notify" src="~/controls/Nuggets/SupplierInvoiceMainInfo/Notify/SupplierInvoiceMainInfo_Notify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SupplierInvoiceLines_Delete" src="~/controls/Nuggets/SupplierInvoiceLines/Delete/SupplierInvoiceLines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SupplierInvoiceLines_Add" src="~/controls/Nuggets/SupplierInvoiceLines/Add/SupplierInvoiceLines_Add.ascx"/>
        <!-- Nuggets and Forms: Orders Section -->
        <add tagPrefix="ReboundNugget" tagName="SelectPart" src="~/controls/Nuggets/SelectPart/SelectPart.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="Sourcing" src="~/controls/Nuggets/Sourcing/Sourcing.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_EditOffer" src="~/controls/Nuggets/Sourcing/EditOffer/Sourcing_EditOffer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_AddToRequirement" src="~/controls/Nuggets/Sourcing/AddToReq/Sourcing_AddToReq.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_RFQ" src="~/controls/Nuggets/Sourcing/RFQ/Sourcing_RFQ.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_AddStockInfo" src="~/controls/Nuggets/Sourcing/AddStockInfo/Sourcing_AddStockInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_EditStockInfo" src="~/controls/Nuggets/Sourcing/EditStockInfo/Sourcing_EditStockInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_AddOffer" src="~/controls/Nuggets/Sourcing/AddOffer/Sourcing_AddOffer.ascx"/>
        <!--<add tagPrefix="ReboundForm" tagName="Sourcing_SearchOffer" src="~/controls/Nuggets/Sourcing/SearchOffer/Sourcing_SearchOffer.ascx"/>-->
        <add tagPrefix="ReboundForm" tagName="Sourcing_AddTrusted" src="~/controls/Nuggets/Sourcing/AddTrusted/Sourcing_AddTrusted.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CsvExportHistory" src="~/controls/Nuggets/CsvExportHistory/CsvExportHistory.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="HubRFQAssignmentHistory" src="~/controls/Nuggets/HubRFQAssignmentHistory/HubRFQAssignmentHistory.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ClientInvoiceMainInfo" src="~/controls/Nuggets/ClientInvoiceMainInfo/ClientInvoiceMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceMainInfo_Edit" src="~/controls/Nuggets/ClientInvoiceMainInfo/Edit/ClientInvoiceMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceMainInfo_Notify" src="~/controls/Nuggets/ClientInvoiceMainInfo/Notify/ClientInvoiceMainInfo_Notify.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ClientInvoiceLines" src="~/controls/Nuggets/ClientInvoiceLines/ClientInvoiceLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceLines_Delete" src="~/controls/Nuggets/ClientInvoiceLines/Delete/ClientInvoiceLines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceLines_Add" src="~/controls/Nuggets/ClientInvoiceLines/Add/ClientInvoiceLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLineRelease" src="~/controls/Nuggets/POLines/Release/POLine_Release.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLineUnRelease" src="~/controls/Nuggets/POLines/UnRelease/POLine_UnRelease.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockImportTool" src="~/controls/Nuggets/Sourcing/StockImportTool/StockImportTool.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockImportEpoTool" src="~/controls/Nuggets/Sourcing/StockImportEpoTool/StockImportEpoTool.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockImportReverseLogisticTool" src="~/controls/Nuggets/Sourcing/StockImportReverseLogisticTool/StockImportReverseLogisticTool.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SupplierMfrSearchNugget" src="~/controls/Nuggets/SupplierMfrSearch/SupplierMfrSearch.ascx"/>
        <!--PO Hub Sourcing Control-->
        <add tagPrefix="ReboundNugget" tagName="POHubSourcing" src="~/controls/Nuggets/POHubSourcing/POHubSourcing.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_EditOffer" src="~/controls/Nuggets/POHubSourcing/EditOffer/POHubSourcing_EditOffer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_AddToRequirement" src="~/controls/Nuggets/POHubSourcing/AddToReq/POHubSourcing_AddToReq.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_RFQ" src="~/controls/Nuggets/POHubSourcing/RFQ/POHubSourcing_RFQ.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_AddStockInfo" src="~/controls/Nuggets/POHubSourcing/AddStockInfo/POHubSourcing_AddStockInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_EditStockInfo" src="~/controls/Nuggets/POHubSourcing/EditStockInfo/POHubSourcing_EditStockInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_AddOffer" src="~/controls/Nuggets/POHubSourcing/AddOffer/POHubSourcing_AddOffer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_AddTrusted" src="~/controls/Nuggets/POHubSourcing/AddTrusted/POHubSourcing_AddTrusted.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_EditAltPartInfo" src="~/controls/Nuggets/POHubSourcing/EditAltPartInfo/POHubSourcing_EditAltPartInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CsvExportHistory" src="~/controls/Nuggets/CsvExportHistory/CsvExportHistory.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="BomUploadedExcel" src="~/controls/Nuggets/BomUploadedExcel/BomUploadedExcel.ascx"/>
        <!--<add tagPrefix="ReboundForm" tagName="POHubSourcing_EditTrust" src="~/controls/Nuggets/POHubSourcing/EditTrust/POHubSourcing_EditTrust.ascx"/>-->
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_EditEpo" src="~/controls/Nuggets/POHubSourcing/EditEpo/POHubSourcing_EditEpo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_EditEpo" src="~/controls/Nuggets/Sourcing/EditEpo/Sourcing_EditEpo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_EditAltPart" src="~/controls/Nuggets/Sourcing/EditAltPart/Sourcing_EditAltPart.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_EditReverseLogistics" src="~/controls/Nuggets/Sourcing/EditReverseLogistics/Sourcing_EditReverseLogistics.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sourcing_EditReverseLogisticsBulk" src="~/controls/Nuggets/Sourcing/EditReverseLogisticsBulk/Sourcing_EditReverseLogisticsBulk.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_EditReverseLogisticsBulk" src="~/controls/Nuggets/POHubSourcing/EditReverseLogisticsBulk/POHubSourcing_EditReverseLogisticsBulk.ascx"/>
        <!--Internal Purchase Order-->
        <!-- POSourcingHub ReverseLogistic RP-1421-->
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_EditReverseLogistic" src="~/controls/Nuggets/POHubSourcing/EditReverseLogistic/POHubSourcing_EditReverseLogistic.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_ReverseLogisticBulkEditHistory" src="~/controls/Nuggets/POHubSourcing/ReverseLogisticBulkEditHistory/POHubSourcing_ReverseLogisticBulkEditHistory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_AddReverseLogistic" src="~/controls/Nuggets/POHubSourcing/AddReverseLogistic/POHubSourcing_AddReverseLogistic.ascx"/>
        <!-- POSourcingHub ReverseLogistic-->
        <!-- POSourcingHub ReverseLogistic RP-2346-->
        <add tagPrefix="ReboundForm" tagName="POHubSourcing_EditStock" src="~/controls/Nuggets/POHubSourcing/EditStock/POHubSourcing_EditStock.ascx"/>
        <!-- POSourcingHub ReverseLogistic-->
        <add tagPrefix="ReboundDataListNugget" tagName="InternalPurchaseOrders" src="~/controls/DataListNuggets/InternalPurchaseOrders/InternalPurchaseOrders.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="InternalPOAdd" src="~/controls/Nuggets/InternalPOAdd/InternalPOAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOAdd_Add" src="~/controls/Nuggets/InternalPOAdd/Add/InternalPOAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="InternalPOMainInfo" src="~/controls/Nuggets/InternalPOMainInfo/InternalPOMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOMainInfo_AddExpedite" src="~/controls/Nuggets/InternalPOMainInfo/AddExpedite/InternalPOMainInfo_AddExpedite.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOMainInfo_Approve" src="~/controls/Nuggets/InternalPOMainInfo/Approve/InternalPOMainInfo_Approve.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOMainInfo_Close" src="~/controls/Nuggets/InternalPOMainInfo/Close/InternalPOMainInfo_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPurchaseOrderMainInfo_Edit" src="~/controls/Nuggets/InternalPOMainInfo/Edit/InternalPOMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOMainInfo_Notify" src="~/controls/Nuggets/InternalPOMainInfo/Notify/InternalPOMainInfo_Notify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOMainInfo_Warehouse" src="~/controls/Nuggets/SOMainInfo/Warehouse/SOMainInfo_Warehouse.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Warehouse" src="~/controls/Nuggets/SOLines/Warehouse/SOLines_Warehouse.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOLines_Post" src="~/controls/Nuggets/InternalPOLines/Post/InternalPOLines_Post.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOLines_Edit" src="~/controls/Nuggets/InternalPOLines/Edit/InternalPOLines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOLines_Add" src="~/controls/Nuggets/InternalPOLines/Add/InternalPOLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOLines_Allocate" src="~/controls/Nuggets/InternalPOLines/Allocate/InternalPOLines_Allocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOLines_Deallocate" src="~/controls/Nuggets/InternalPOLines/Deallocate/InternalPOLines_Deallocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOLines_Delete" src="~/controls/Nuggets/InternalPOLines/Delete/InternalPOLines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InternalPOLines_Close" src="~/controls/Nuggets/InternalPOLines/Close/InternalPOLines_Close.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="InternalPOLines" src="~/controls/Nuggets/InternalPOLines/InternalPOLines.ascx"/>
        <!--Purchase Order-->
        <add tagPrefix="ReboundNugget" tagName="POAdd" src="~/controls/Nuggets/POAdd/POAdd.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="POMainInfo" src="~/controls/Nuggets/POMainInfo/POMainInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="POLines" src="~/controls/Nuggets/POLines/POLines.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="POApprovals" src="~/controls/Nuggets/POApprovals/POApprovals.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ExportApprovalStatus" src="~/controls/Nuggets/ExportApprovalStatus/ExportApprovalStatus.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ExportApprovalStatus_edit" src="~/controls/Nuggets/ExportApprovalStatus/Edit/ExportApprovalStatus_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ExportApprovalStatus_EditAll" src="~/controls/Nuggets/ExportApprovalStatus/EditAll/ExportApprovalStatus_EditAll.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ExportApprovalStatus_RequestApproval" src="~/controls/Nuggets/ExportApprovalStatus/RequestApproval/ExportApprovalStatus_RequestApproval.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ExportApprovalStatus_Approvals" src="~/controls/Nuggets/ExportApprovalStatus/Approvals/ExportApprovalStatus_Approvals.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POAdd_Add" src="~/controls/Nuggets/POAdd/Add/POAdd_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PurchaseOrderMainInfo_Edit" src="~/controls/Nuggets/POMainInfo/Edit/POMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POMainInfo_Close" src="~/controls/Nuggets/POMainInfo/Close/POMainInfo_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POMainInfo_Approve" src="~/controls/Nuggets/POMainInfo/Approve/POMainInfo_Approve.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POMainInfo_Notify" src="~/controls/Nuggets/POMainInfo/Notify/POMainInfo_Notify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLines_Post" src="~/controls/Nuggets/POLines/Post/POLines_Post.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POApprovals_Approvals" src="~/controls/Nuggets/POApprovals/Approvals/Supplier_Approvals.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLines_Edit" src="~/controls/Nuggets/POLines/Edit/POLines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="supplierApprovals_edit" src="~/controls/Nuggets/POApprovals/Edit/Supplier_Approval_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLines_Add" src="~/controls/Nuggets/POLines/Add/POLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLines_Allocate" src="~/controls/Nuggets/POLines/Allocate/POLines_Allocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLines_Deallocate" src="~/controls/Nuggets/POLines/Deallocate/POLines_Deallocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLines_Delete" src="~/controls/Nuggets/POLines/Delete/POLines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POLines_Close" src="~/controls/Nuggets/POLines/Close/POLines_Close.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOMainInfo" src="~/controls/Nuggets/SOMainInfo/SOMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOMainInfo_Edit" src="~/controls/Nuggets/SOMainInfo/Edit/SOMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOMainInfo_Notify" src="~/controls/Nuggets/SOMainInfo/Notify/SOMainInfo_Notify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOMainInfo_Close" src="~/controls/Nuggets/SOMainInfo/Close/SOMainInfo_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOMainInfo_PayByCreditCard" src="~/controls/Nuggets/SOMainInfo/PayByCreditCard/SOMainInfo_PayByCreditCard.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOShowPaymentFiles" src="~/controls/Nuggets/SOShowPaymentFiles/SOShowPaymentFiles.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOMainInfo_Confirm" src="~/controls/Nuggets/SOMainInfo/Confirm/SOMainInfo_Confirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOMainInfo_Consolidate" src="~/controls/Nuggets/SOMainInfo/Consolidate/SOMainInfo_Consolidate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOMainInfo_SentOrder" src="~/controls/Nuggets/SOMainInfo/SentOrder/SOMainInfo_SentOrder.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Confirm1" src="~/controls/Nuggets/SOLines/SOLines_Confirm/SOLines_Confirm.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOAuthorisation" src="~/controls/Nuggets/SOAuthorisation/SOAuthorisation.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOLines" src="~/controls/Nuggets/SOLines/SOLines.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="EndUserUndertakingForm" src="~/controls/Nuggets/EndUserUndertakingForm/EndUserUndertakingForm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Post" src="~/controls/Nuggets/SOLines/Post/SOLines_Post.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Edit" src="~/controls/Nuggets/SOLines/Edit/SOLines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Add" src="~/controls/Nuggets/SOLines/Add/SOLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Allocate" src="~/controls/Nuggets/SOLines/Allocate/SOLines_Allocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_IpoAllocate" src="~/controls/Nuggets/SOLines/IpoAllocate/SOLines_IpoAllocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Deallocate" src="~/controls/Nuggets/SOLines/Deallocate/SOLines_Deallocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Delete" src="~/controls/Nuggets/SOLines/Delete/SOLines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_Close" src="~/controls/Nuggets/SOLines/Close/SOLines_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_EditAll" src="~/controls/Nuggets/SOLines/EditAll/SOLines_EditAll.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOAdd" src="~/controls/Nuggets/SOAdd/SOAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOAdd_Add" src="~/controls/Nuggets/SOAdd/Add/SOAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOShipLines" src="~/controls/Nuggets/SOShipLines/SOShipLines.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOAuthorisation" src="~/controls/Nuggets/SOAuthorisation/SOAuthorisation.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOAuthorisation_Authorise" src="~/controls/Nuggets/SOAuthorisation/Authorise/SOAuthorisation_Authorise.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOAuthorisation_ReadyToShip" src="~/controls/Nuggets/SOAuthorisation/ReadyToShip/SOAuthorisation_ReadyToShip.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SORequestApproval" src="~/controls/Nuggets/SOAuthorisation/RequestApproval/RequestApproval.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOLines_CreateClone" src="~/controls/Nuggets/SOLines/CreateClone/SOLines_CreateClone.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="InvoiceMainInfo" src="~/controls/Nuggets/InvoiceMainInfo/InvoiceMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceMainInfo_Edit" src="~/controls/Nuggets/InvoiceMainInfo/Edit/InvoiceMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceMainInfo_EditShippingInfo" src="~/controls/Nuggets/InvoiceMainInfo/EditShippingInfo/InvoiceMainInfo_EditShippingInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceMainInfo_Export" src="~/controls/Nuggets/InvoiceMainInfo/Export/InvoiceMainInfo_Export.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="InvoiceAdd" src="~/controls/Nuggets/InvoiceAdd/InvoiceAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceAdd_Add" src="~/controls/Nuggets/InvoiceAdd/Add/InvoiceAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="InvoiceLines" src="~/controls/Nuggets/InvoiceLines/InvoiceLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceLines_Edit" src="~/controls/Nuggets/InvoiceLines/Edit/InvoiceLines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceLines_Add" src="~/controls/Nuggets/InvoiceLines/Add/InvoiceLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceLines_Delete" src="~/controls/Nuggets/InvoiceLines/Delete/InvoiceLines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceLines_EditAllocation" src="~/controls/Nuggets/InvoiceLines/EditAllocation/InvoiceLines_EditAllocation.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="InvoiceLinesDeleted" src="~/controls/Nuggets/InvoiceLinesDeleted/InvoiceLinesDeleted.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="QuoteAdd" src="~/controls/Nuggets/QuoteAdd/QuoteAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteAdd_Add" src="~/controls/Nuggets/QuoteAdd/Add/QuoteAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="QuoteMainInfo" src="~/controls/Nuggets/QuoteMainInfo/QuoteMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteMainInfo_Edit" src="~/controls/Nuggets/QuoteMainInfo/Edit/QuoteMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteMainInfo_Close" src="~/controls/Nuggets/QuoteMainInfo/Close/QuoteMainInfo_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteMainInfo_MarkAsToDo" src="~/controls/Nuggets/QuoteMainInfo/AddTask/QuoteMainInfo_MarkAsToDo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="QuoteLines" src="~/controls/Nuggets/QuoteLines/QuoteLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteLines_Add" src="~/controls/Nuggets/QuoteLines/Add/QuoteLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteLines_Edit" src="~/controls/Nuggets/QuoteLines/Edit/QuoteLines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteLines_Close" src="~/controls/Nuggets/QuoteLines/Close/QuoteLines_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteLines_Delete" src="~/controls/Nuggets/QuoteLines/Delete/QuoteLines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="QuoteLines_EditOriginalOffer" src="~/controls/Nuggets/QuoteLines/EditOriginalOffer/QuoteLines_EditOriginalOffer.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CRMAAdd" src="~/controls/Nuggets/CRMAAdd/CRMAAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMAAdd_Add" src="~/controls/Nuggets/CRMAAdd/Add/CRMAAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CRMAMainInfo" src="~/controls/Nuggets/CRMAMainInfo/CRMAMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMAMainInfo_Edit" src="~/controls/Nuggets/CRMAMainInfo/Edit/CRMAMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CRMALines" src="~/controls/Nuggets/CRMALines/CRMALines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMALines_Edit" src="~/controls/Nuggets/CRMALines/Edit/CRMALines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMALines_Add" src="~/controls/Nuggets/CRMALines/Add/CRMALines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMALines_Delete" src="~/controls/Nuggets/CRMALines/Delete/CRMALines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMALines_Deallocate" src="~/controls/Nuggets/CRMALines/Deallocate/CRMALines_Deallocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CRMAMainInfo_AddExpedite" src="~/controls/Nuggets/CRMAMainInfo/AddExpedite/CRMAMainInfo_AddExpedite.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMAMainInfo_AddExpedite" src="~/controls/Nuggets/SRMAMainInfo/AddExpedite/SRMAMainInfo_AddExpedite.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceLines_EditBankFee" src="~/controls/Nuggets/InvoiceLines/EditBankFee/InvoiceLines_EditBankFee.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POMainInfo_AddExpedite" src="~/controls/Nuggets/POMainInfo/AddExpedite/POMainInfo_AddExpedite.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="BOMAdd" src="~/controls/Nuggets/BOMAdd/BOMAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMAdd_Add" src="~/controls/Nuggets/BOMAdd/Add/BOMAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ImportExcel" src="~/controls/Nuggets/ImportExcel/ImportExcel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ImportExcel_Add" src="~/controls/Nuggets/ImportExcel/Add/ImportExcel_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CsvUploadHistory" src="~/controls/Nuggets/CsvUploadHistory/CsvUploadHistory.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="LogHistory" src="~/controls/Nuggets/LogHistory/LogHistory.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ClientInvoiceAdd" src="~/controls/Nuggets/ClientInvoiceAdd/ClientInvoiceAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceAdd_Add" src="~/controls/Nuggets/ClientInvoiceAdd/Add/ClientInvoiceAdd_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMImport_Form" src="~/controls/Nuggets/BOMImport/BOMImport_Form/BOMImport_Form.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CrossMatch_Form" src="~/controls/Nuggets/CrossMatch/CrossMatch_Form/CrossMatch_Form.ascx"/>
        <!--start code by umendra-->
        <add tagPrefix="ReboundNugget" tagName="ClientBOMAdd" src="~/controls/Nuggets/ClientBOMAdd/ClientBOMAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientBOMAdd_Add" src="~/controls/Nuggets/ClientBOMAdd/Add/ClientBOMAdd_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientBOMItems_AddToHUBRFQ" src="~/controls/Nuggets/ClientBOMItems/AddToHUBRFQ/ClientBOMItems_AddToHUBRFQ.ascx"/>
        <!--end code by umendra-->
        <!--Company Insurance certificate-->
        <add tagPrefix="ReboundNugget" tagName="CompanyCertificate" src="~/controls/Nuggets/CompanyCertificate/CompanyCertificate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyCertificate_Add" src="~/controls/Nuggets/CompanyCertificate/Add/CompanyCertificate_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyCertificate_Edit" src="~/controls/Nuggets/CompanyCertificate/Edit/CompanyCertificate_Edit.ascx"/>
        <!--Company certificate-->
        <add tagPrefix="ReboundNugget" tagName="CompanyInsuranceCertificate" src="~/controls/Nuggets/CompanyInsuranceCertificate/CompanyInsuranceCertificate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyInsuranceCertificate_Add" src="~/controls/Nuggets/CompanyInsuranceCertificate/Add/CompanyInsuranceCertificate_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyInsuranceCertificate_Edit" src="~/controls/Nuggets/CompanyInsuranceCertificate/Edit/CompanyInsuranceCertificate_Edit.ascx"/>
        <!--[001] code start-->
        <!--Company API Customer-->
        <add tagPrefix="ReboundNugget" tagName="CompanyApiCustomer" src="~/controls/Nuggets/CompanyApiCustomer/CompanyApiCustomer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyApiCustomer_Add" src="~/controls/Nuggets/CompanyApiCustomer/Add/CompanyApiCustomer_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyApiCustomer_MappingAdd" src="~/controls/Nuggets/CompanyApiCustomer/ApiMapping/CompanyApiCustomer_MappingAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyApiCustomer_Edit" src="~/controls/Nuggets/CompanyApiCustomer/Edit/CompanyApiCustomer_Edit.ascx"/>
        <!--[001] code start-->
        <add tagPrefix="ReboundForm" tagName="CRMALines_Close" src="~/controls/Nuggets/CRMALines/Close/CRMALines_Close.ascx"/>
        <!--[001] code end-->
        <add tagPrefix="ReboundNugget" tagName="SRMAAdd" src="~/controls/Nuggets/SRMAAdd/SRMAAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMAAdd_Add" src="~/controls/Nuggets/SRMAAdd/Add/SRMAAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SRMAMainInfo" src="~/controls/Nuggets/SRMAMainInfo/SRMAMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMAMainInfo_Edit" src="~/controls/Nuggets/SRMAMainInfo/Edit/SRMAMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SRMALines" src="~/controls/Nuggets/SRMALines/SRMALines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMALines_Edit" src="~/controls/Nuggets/SRMALines/Edit/SRMALines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMALines_Add" src="~/controls/Nuggets/SRMALines/Add/SRMALines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMALines_Delete" src="~/controls/Nuggets/SRMALines/Delete/SRMALines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMALines_Allocate" src="~/controls/Nuggets/SRMALines/Allocate/SRMALines_Allocate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SRMALines_Deallocate" src="~/controls/Nuggets/SRMALines/Deallocate/SRMALines_Deallocate.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CustomerRequirementAdd" src="~/controls/Nuggets/CusReqAdd/CusReqAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementAdd_Add" src="~/controls/Nuggets/CusReqAdd/Add/CusReqAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CustomerRequirementMainInfo" src="~/controls/Nuggets/CusReqMainInfo/CusReqMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_AddAlternate" src="~/controls/Nuggets/CusReqMainInfo/AddAlternate/CusReqMainInfo_AddAlternate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_Edit" src="~/controls/Nuggets/CusReqMainInfo/Edit/CusReqMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_Close" src="~/controls/Nuggets/CusReqMainInfo/Close/CusReqMainInfo_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_Delete" src="~/controls/Nuggets/CusReqMainInfo/Delete/CusReqMainInfo_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_Confirm" src="~/controls/Nuggets/CusReqMainInfo/Confirm/CustomerRequirementMainInfo_Confirm.ascx"/>
        <!--<add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_CloneHUBRFQ" src="~/controls/Nuggets/CusReqMainInfo/CloneHUBRFQ/CustomerRequirementMainInfo_CloneHUBRFQ.ascx" />-->
        <!--<add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_CloneHUB" src="~/controls/Nuggets/CusReqMainInfo/CloneHUB/CustomerRequirementMainInfo_CloneHUB.ascx" />-->
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_CloneHUBRFQ" src="~/controls/Nuggets/CusReqMainInfo/EditCloneHUBRFQ/CusReqMainInfo_EditCloneHUBRFQ.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementMainInfo_CloneHUB" src="~/controls/Nuggets/CusReqMainInfo/EditCloneHUB/CusReqMainInfo_EditCloneHUB.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CustReqAllInfo_AllInfo" src="~/controls/Nuggets/CusReqMainInfo/CustReqAllInfo/CustReqAllInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CustomerRequirementSourcingResults" src="~/controls/Nuggets/CusReqSourcingResults/CusReqSourcingResults.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementSourcingResults_Add" src="~/controls/Nuggets/CusReqSourcingResults/Add/CusReqSourcingResults_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementSourcingResults_Edit" src="~/controls/Nuggets/CusReqSourcingResults/Edit/CusReqSourcingResults_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="PurReqMainInfo" src="~/controls/Nuggets/PurReqMainInfo/PurReqMainInfo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CreditAdd" src="~/controls/Nuggets/CreditAdd/CreditAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CreditAdd_Add" src="~/controls/Nuggets/CreditAdd/Add/CreditAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CreditMainInfo" src="~/controls/Nuggets/CreditMainInfo/CreditMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CreditMainInfo_Edit" src="~/controls/Nuggets/CreditMainInfo/Edit/CreditMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CreditMainInfo_Export" src="~/controls/Nuggets/CreditMainInfo/Export/CreditMainInfo_Export.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="CreditLines" src="~/controls/Nuggets/CreditLines/CreditLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CreditLines_Edit" src="~/controls/Nuggets/CreditLines/Edit/CreditLines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CreditLines_Add" src="~/controls/Nuggets/CreditLines/Add/CreditLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CreditLines_Delete" src="~/controls/Nuggets/CreditLines/Delete/CreditLines_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CreditLines_Confirm" src="~/controls/Nuggets/CreditLines/Confirm/CreditLines_Confirm.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="DebitAdd" src="~/controls/Nuggets/DebitAdd/DebitAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DebitAdd_Add" src="~/controls/Nuggets/DebitAdd/Add/DebitAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="DebitMainInfo" src="~/controls/Nuggets/DebitMainInfo/DebitMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DebitMainInfo_Edit" src="~/controls/Nuggets/DebitMainInfo/Edit/DebitMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DebitMainInfo_Export" src="~/controls/Nuggets/DebitMainInfo/Export/DebitMainInfo_Export.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="DebitLines" src="~/controls/Nuggets/DebitLines/DebitLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DebitLines_Edit" src="~/controls/Nuggets/DebitLines/Edit/DebitLines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DebitLines_Add" src="~/controls/Nuggets/DebitLines/Add/DebitLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DebitLines_Delete" src="~/controls/Nuggets/DebitLines/Delete/DebitLines_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SupplierInvoiceAdd" src="~/controls/Nuggets/SupplierInvoiceAdd/SupplierInvoiceAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SupplierInvoiceAdd_Add" src="~/controls/Nuggets/SupplierInvoiceAdd/Add/SupplierInvoiceAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="NPRNotify" src="~/controls/Nuggets/NPRNotify/NPRNotify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="NPRNotify_Notify" src="~/controls/Nuggets/NPRNotify/Notify/NPRNotify_Notify.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="BOMCustomerRequirementSourcingResults" src="~/controls/Nuggets/BOMCusReqSourcingResults/BOMCusReqSourcingResults.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMCustomerRequirementSourcingResults_Add" src="~/controls/Nuggets/BOMCusReqSourcingResults/Add/BOMCusReqSourcingResults_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMCustomerRequirementSourcingResults_Edit" src="~/controls/Nuggets/BOMCusReqSourcingResults/Edit/BOMCusReqSourcingResults_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMCustomerRequirementSourcingResults_Delete" src="~/controls/Nuggets/BOMCusReqSourcingResults/Delete/BOMCusReqSourcingResults_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMCustomerRequirementSourcingResults_Confirm" src="~/controls/Nuggets/BOMCusReqSourcingResults/Confirm/BOMCusReqSourcingResults_Confirm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMCustomerRequirementSourcingResults_Approval" src="~/controls/Nuggets/BOMCusReqSourcingResults/Approval/BOMCusReqSourcingResults_Approval.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMCusReqSourcingResults_DeletePartWatch" src="~/controls/Nuggets/BOMCusReqSourcingResults/DeletePartWatch/BOMCusReqSourcingResults_DeletePartWatch.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="EPRNotify" src="~/controls/Nuggets/EPRNotify/EPRNotify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EPRNotify_Notify" src="~/controls/Nuggets/EPRNotify/Notify/EPRNotify_Notify.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ShortShipmentNotify" src="~/controls/Nuggets/ShortShipmentNotify/ShortShipmentNotify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ShortShipmentNotify_Notify" src="~/controls/Nuggets/ShortShipmentNotify/Notify/ShortShipmentNotify_Notify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CustomerRequirementSourcingResults_Delete" src="~/controls/Nuggets/CusReqSourcingResults/Delete/CusReqSourcingResults_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="GILineNotify" src="~/controls/Nuggets/GILineNotify/GILineNotify.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GILineNotify_Notify" src="~/controls/Nuggets/GILineNotify/Notify/GILineNotify_Notify.ascx"/>
        <!-- Nuggets and Forms: Profile Section -->
        <add tagPrefix="ReboundNugget" tagName="MailMessages" src="~/controls/Nuggets/MailMessages/MailMessages.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessages_NewFolder" src="~/controls/Nuggets/MailMessages/NewFolder/MailMessages_NewFolder.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessages_DeleteFolder" src="~/controls/Nuggets/MailMessages/DeleteFolder/MailMessages_DeleteFolder.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessages_EditFolder" src="~/controls/Nuggets/MailMessages/EditFolder/MailMessages_EditFolder.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessages_DeleteMessage" src="~/controls/Nuggets/MailMessages/DeleteMessage/MailMessages_DeleteMessage.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessages_MoveMessage" src="~/controls/Nuggets/MailMessages/MoveMessage/MailMessages_MoveMessage.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessages_NewMessage" src="~/controls/Nuggets/MailMessages/NewMessage/MailMessages_NewMessage.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessages_MarkAsToDo" src="~/controls/Nuggets/MailMessages/MarkAsToDo/MailMessages_MarkAsToDo.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="ToDo" src="~/controls/Nuggets/ToDo/ToDo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ToDo_Edit" src="~/controls/Nuggets/ToDo/Edit/ToDo_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ToDo_Confirm" src="~/controls/Nuggets/ToDo/Confirm/ToDo_Confirm.ascx"/>
        <!--Purchase Quote-->
        <add tagPrefix="ReboundNugget" tagName="POQuoteAdd" src="~/controls/Nuggets/POQuoteAdd/POQuoteAdd.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POQuoteAdd_Add" src="~/controls/Nuggets/POQuoteAdd/Add/POQuoteAdd_Add.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="POQuoteMainInfo" src="~/controls/Nuggets/POQuoteMainInfo/POQuoteMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POQuoteMainInfo_Edit" src="~/controls/Nuggets/POQuoteMainInfo/Edit/POQuoteMainInfo_Edit.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="POQuoteLines" src="~/controls/Nuggets/POQuoteLines/POQuoteLines.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POQuoteLines_Add" src="~/controls/Nuggets/POQuoteLines/Add/POQuoteLines_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POQuoteLines_Edit" src="~/controls/Nuggets/POQuoteLines/Edit/POQuoteLines_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POQuoteLines_Close" src="~/controls/Nuggets/POQuoteLines/Close/POQuoteLines_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="POQuoteMainInfo_Notify" src="~/controls/Nuggets/POQuoteMainInfo/Notify/POQuoteMainInfo_Notify.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="PurchaseRequestLineDetail" src="~/controls/Nuggets/PurchaseRequestLineDetail/PurchaseRequestLineDetail.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PurchaseRequestLineDetail_Add" src="~/controls/Nuggets/PurchaseRequestLineDetail/Add/PurchaseRequestLineDetail_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PurchaseRequestLineDetail_Edit" src="~/controls/Nuggets/PurchaseRequestLineDetail/Edit/PurchaseRequestLineDetail_Edit.ascx"/>
        <!--Purchase Quote-->
        <!-- AutoSearch -->
        <add tagPrefix="ReboundAutoSearch" namespace="Rebound.GlobalTrader.Site.Controls.AutoSearch" assembly="Rebound.GlobalTrader.Site"/>
        <!-- Left Nuggets -->
        <add tagPrefix="ReboundLeftNugget" namespace="Rebound.GlobalTrader.Site.Controls.LeftNuggets" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ContactSelection" src="~/controls/LeftNuggets/ContactSelection/ContactSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="OrdersSelection" src="~/controls/LeftNuggets/OrdersSelection/OrdersSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="WarehouseSelection" src="~/controls/LeftNuggets/WarehouseSelection/WarehouseSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ProfileSelection" src="~/controls/LeftNuggets/ProfileSelection/ProfileSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="RecentlyViewed" src="~/controls/LeftNuggets/RecentlyViewed/RecentlyViewed.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="CompanyList" src="~/controls/LeftNuggets/CompanyList/CompanyList.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ViewOptions" src="~/controls/LeftNuggets/ViewOptions/ViewOptions.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ViewOptions_Home" src="~/controls/LeftNuggets/ViewOptions_Home/ViewOptions_Home.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="SourcingLinks" src="~/controls/LeftNuggets/SourcingLinks/SourcingLinks.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="QuickJump" src="~/controls/LeftNuggets/QuickJump/QuickJump.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="StockSelection" src="~/controls/LeftNuggets/StockSelection/StockSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="GlobalSettingsSelection" src="~/controls/LeftNuggets/GlobalSettingsSelection/GlobalSettingsSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="SecuritySettingsSelection" src="~/controls/LeftNuggets/SecuritySettingsSelection/SecuritySettingsSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="CompanySettingsSelection" src="~/controls/LeftNuggets/CompanySettingsSelection/CompanySettingsSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ReportsSelection" src="~/controls/LeftNuggets/ReportsSelection/ReportsSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Credits" src="~/controls/LeftNuggets/DataLists/Credits/Credits.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_CRMAs" src="~/controls/LeftNuggets/DataLists/CRMAs/CRMAs.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_CustomerRequirements" src="~/controls/LeftNuggets/DataLists/CustomerRequirements/CustomerRequirements.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Debits" src="~/controls/LeftNuggets/DataLists/Debits/Debits.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Invoices" src="~/controls/LeftNuggets/DataLists/Invoices/Invoices.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_PurchaseOrders" src="~/controls/LeftNuggets/DataLists/PurchaseOrders/PurchaseOrders.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_PurchaseRequisitions" src="~/controls/LeftNuggets/DataLists/PurchaseRequisitions/PurchaseRequisitions.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Quotes" src="~/controls/LeftNuggets/DataLists/Quotes/Quotes.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_SalesOrders" src="~/controls/LeftNuggets/DataLists/SalesOrders/SalesOrders.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_SRMAs" src="~/controls/LeftNuggets/DataLists/SRMAs/SRMAs.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Manufacturers" src="~/controls/LeftNuggets/DataLists/Manufacturers/Manufacturers.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Lots" src="~/controls/LeftNuggets/DataLists/Lots/Lots.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_CRMAsReceive" src="~/controls/LeftNuggets/DataLists/CRMAsReceive/CRMAsReceive.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_GoodsIn" src="~/controls/LeftNuggets/DataLists/GoodsIn/GoodsIn.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Companies" src="~/controls/LeftNuggets/DataLists/Companies/Companies.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Contacts" src="~/controls/LeftNuggets/DataLists/Contacts/Contacts.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_PurchaseOrdersReceive" src="~/controls/LeftNuggets/DataLists/PurchaseOrdersReceive/PurchaseOrdersReceive.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_SalesOrdersShip" src="~/controls/LeftNuggets/DataLists/SalesOrdersShip/SalesOrdersShip.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Services" src="~/controls/LeftNuggets/DataLists/Services/Services.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_SRMAsShip" src="~/controls/LeftNuggets/DataLists/SRMAsShip/SRMAsShip.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Stock" src="~/controls/LeftNuggets/DataLists/Stock/Stock.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="UtilityBOMImport" src="~/controls/Nuggets/UtilityBOMImport/UtilityBOMImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityBOM_Import" src="~/controls/Nuggets/UtilityBOMImport/UtilityBOM_Import/UtilityBOM_Import.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="UtilityOfferImport" src="~/controls/Nuggets/UtilityOfferImport/UtilityOfferImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityOffer_Import" src="~/controls/Nuggets/UtilityOfferImport/UtilityOffer_Import/UtilityOffer_Import.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SupplierImport_Import" src="~/controls/Nuggets/CompanyApiCustomer/SupplierImport_Import/SupplierImport_Import.ascx"/>
        <!-- Alternative Import Tool Start -->
        <add tagPrefix="ReboundNugget" tagName="UtilityAlternativeImport" src="~/controls/Nuggets/UtilityAlternativeImport/UtilityAlternativeImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityAlternative_Import" src="~/controls/Nuggets/UtilityAlternativeImport/UtilityAlternative_Import/UtilityAlternative_Import.ascx"/>
        <!-- Alternative Import Tool End -->
        <add tagPrefix="ReboundNugget" tagName="UtilityHUBOfferImport" src="~/controls/Nuggets/UtilityHUBOfferImport/UtilityHUBOfferImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityHUBOffer_Import" src="~/controls/Nuggets/UtilityHUBOfferImport/UtilityHUBOffer_Import/UtilityHUBOffer_Import.ascx"/>

		  <add tagPrefix="ReboundNugget" tagName="UtilityHUBOfferImportLarge" src="~/controls/Nuggets/UtilityHUBOfferImportLarge/UtilityHUBOfferImportLarge.ascx"/>
		  <add tagPrefix="ReboundForm" tagName="UtilityHUBOffer_ImportLarge" src="~/controls/Nuggets/UtilityHUBOfferImportLarge/UtilityHUBOffer_ImportLarge/UtilityHUBOffer_ImportLarge.ascx"/>
        <!-- Reverse Logistics Import Tool Start -->
        <add tagPrefix="ReboundNugget" tagName="UtilityReverseLogisticsImport" src="~/controls/Nuggets/UtilityReverseLogisticsImport/UtilityReverseLogisticsImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityReverseLogistics_Import" src="~/controls/Nuggets/UtilityReverseLogisticsImport/UtilityReverseLogistics_Import/UtilityReverseLogistics_Import.ascx"/>
        <!-- Reverse Logistics Import Tool End -->
        <add tagPrefix="ReboundNugget" tagName="UtilityXMatchImport" src="~/controls/Nuggets/UtilityXMatchImport/UtilityXMatchImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityXMatch_Import" src="~/controls/Nuggets/UtilityXMatchImport/UtilityXMatch_Import/UtilityXMatch_Import.ascx"/>
        <add tagPrefix="ReboundLeftNugget" namespace="Rebound.GlobalTrader.Site.Controls.LeftNuggets" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ContactSelection" src="~/controls/LeftNuggets/ContactSelection/ContactSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="OrdersSelection" src="~/controls/LeftNuggets/OrdersSelection/OrdersSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="WarehouseSelection" src="~/controls/LeftNuggets/WarehouseSelection/WarehouseSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ProfileSelection" src="~/controls/LeftNuggets/ProfileSelection/ProfileSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="RecentlyViewed" src="~/controls/LeftNuggets/RecentlyViewed/RecentlyViewed.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="CompanyList" src="~/controls/LeftNuggets/CompanyList/CompanyList.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ViewOptions" src="~/controls/LeftNuggets/ViewOptions/ViewOptions.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ViewOptions_Home" src="~/controls/LeftNuggets/ViewOptions_Home/ViewOptions_Home.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="SourcingLinks" src="~/controls/LeftNuggets/SourcingLinks/SourcingLinks.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="QuickJump" src="~/controls/LeftNuggets/QuickJump/QuickJump.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="StockSelection" src="~/controls/LeftNuggets/StockSelection/StockSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="GlobalSettingsSelection" src="~/controls/LeftNuggets/GlobalSettingsSelection/GlobalSettingsSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="SecuritySettingsSelection" src="~/controls/LeftNuggets/SecuritySettingsSelection/SecuritySettingsSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="CompanySettingsSelection" src="~/controls/LeftNuggets/CompanySettingsSelection/CompanySettingsSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="ReportsSelection" src="~/controls/LeftNuggets/ReportsSelection/ReportsSelection.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Credits" src="~/controls/LeftNuggets/DataLists/Credits/Credits.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_CRMAs" src="~/controls/LeftNuggets/DataLists/CRMAs/CRMAs.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_CustomerRequirements" src="~/controls/LeftNuggets/DataLists/CustomerRequirements/CustomerRequirements.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Debits" src="~/controls/LeftNuggets/DataLists/Debits/Debits.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Invoices" src="~/controls/LeftNuggets/DataLists/Invoices/Invoices.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_PurchaseOrders" src="~/controls/LeftNuggets/DataLists/PurchaseOrders/PurchaseOrders.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_PurchaseRequisitions" src="~/controls/LeftNuggets/DataLists/PurchaseRequisitions/PurchaseRequisitions.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Quotes" src="~/controls/LeftNuggets/DataLists/Quotes/Quotes.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_SalesOrders" src="~/controls/LeftNuggets/DataLists/SalesOrders/SalesOrders.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_SRMAs" src="~/controls/LeftNuggets/DataLists/SRMAs/SRMAs.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Manufacturers" src="~/controls/LeftNuggets/DataLists/Manufacturers/Manufacturers.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Lots" src="~/controls/LeftNuggets/DataLists/Lots/Lots.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_CRMAsReceive" src="~/controls/LeftNuggets/DataLists/CRMAsReceive/CRMAsReceive.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_GoodsIn" src="~/controls/LeftNuggets/DataLists/GoodsIn/GoodsIn.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Companies" src="~/controls/LeftNuggets/DataLists/Companies/Companies.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Contacts" src="~/controls/LeftNuggets/DataLists/Contacts/Contacts.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_PurchaseOrdersReceive" src="~/controls/LeftNuggets/DataLists/PurchaseOrdersReceive/PurchaseOrdersReceive.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_SalesOrdersShip" src="~/controls/LeftNuggets/DataLists/SalesOrdersShip/SalesOrdersShip.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Services" src="~/controls/LeftNuggets/DataLists/Services/Services.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_SRMAsShip" src="~/controls/LeftNuggets/DataLists/SRMAsShip/SRMAsShip.ascx"/>
        <add tagPrefix="ReboundLeftNugget" tagName="DataList_Stock" src="~/controls/LeftNuggets/DataLists/Stock/Stock.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="UtilityBOMImport" src="~/controls/Nuggets/UtilityBOMImport/UtilityBOMImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityBOM_Import" src="~/controls/Nuggets/UtilityBOMImport/UtilityBOM_Import/UtilityBOM_Import.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="UtilityOfferImport" src="~/controls/Nuggets/UtilityOfferImport/UtilityOfferImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityOffer_Import" src="~/controls/Nuggets/UtilityOfferImport/UtilityOffer_Import/UtilityOffer_Import.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="UtilityHUBOfferImport" src="~/controls/Nuggets/UtilityHUBOfferImport/UtilityHUBOfferImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityHUBOffer_Import" src="~/controls/Nuggets/UtilityHUBOfferImport/UtilityHUBOffer_Import/UtilityHUBOffer_Import.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="UtilityXMatchImport" src="~/controls/Nuggets/UtilityXMatchImport/UtilityXMatchImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityXMatch_Import" src="~/controls/Nuggets/UtilityXMatchImport/UtilityXMatch_Import/UtilityXMatch_Import.ascx"/>
        <!-- Stock Import Tool Start -->
        <add tagPrefix="ReboundNugget" tagName="UtilityStockImport" src="~/controls/Nuggets/UtilityStockImport/UtilityStockImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityStock_Import" src="~/controls/Nuggets/UtilityStockImport/UtilityStock_Import/UtilityStock_Import.ascx"/>
        <!-- Stock Import Tool End -->
        <!-- Bom Manager Tool Start -->
        <add tagPrefix="ReboundNugget" tagName="UtilityBOMManagerImport" src="~/controls/Nuggets/UtilityBOMManagerImport/UtilityBOMManagerImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityBOMManager_Import" src="~/controls/Nuggets/UtilityBOMManagerImport/UtilityBOMManager_Import/UtilityBOMManager_Import.ascx"/>
        <!-- Bom Manager Import Tool end -->
        <!-- Stock Import Tool Start -->
        <add tagPrefix="ReboundNugget" tagName="UtilityLog" src="~/controls/Nuggets/UtilityLog/UtilityLog.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityLog_Import" src="~/controls/Nuggets/UtilityLog/UtilityLog_Import/UtilityLog_Import.ascx"/>
        <!-- Stock Import Tool End -->
        <!-- SubTitleBar Buttons -->
        <add tagPrefix="ReboundSubTitleBarButton" namespace="Rebound.GlobalTrader.Site.Controls.SubTitleBarButtons" assembly="Rebound.GlobalTrader.Site"/>
        <!-- Item Searches -->
        <add tagPrefix="ReboundItemSearch" namespace="Rebound.GlobalTrader.Site.Controls.ItemSearch" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundItemSearch" tagName="Company" src="~/controls/ItemSearch/Company/Company.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="CustomerRequirements" src="~/controls/ItemSearch/CusReqs/CusReqs.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="CustomerRequirementSourcingResults" src="~/controls/ItemSearch/CusReqSourcingResults/CusReqSourcingResults.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="CRMAs" src="~/controls/ItemSearch/CRMAs/CRMAs.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="CRMALines" src="~/controls/ItemSearch/CRMALines/CRMALines.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="GoodsIn" src="~/controls/ItemSearch/GoodsIn/GoodsIn.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="Invoices" src="~/controls/ItemSearch/Invoices/Invoices.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="ClientInvoices" src="~/controls/ItemSearch/ClientInvoices/ClientInvoices.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="InvoiceLines" src="~/controls/ItemSearch/InvoiceLines/InvoiceLines.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="PReqs" src="~/controls/ItemSearch/PReqs/PReqs.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="PurchaseOrderLines" src="~/controls/ItemSearch/PurchaseOrderLines/PurchaseOrderLines.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="PurchaseOrders" src="~/controls/ItemSearch/PurchaseOrders/PurchaseOrders.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="ReceivedPurchaseOrders" src="~/controls/ItemSearch/ReceivedPurchaseOrders/ReceivedPurchaseOrders.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="QuoteLines" src="~/controls/ItemSearch/QuoteLines/QuoteLines.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="SalesOrderLines" src="~/controls/ItemSearch/SalesOrderLines/SalesOrderLines.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="SalesOrderLinesClose" src="~/controls/ItemSearch/SalesOrderLinesClose/SalesOrderLinesClose.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="SalesOrderLinesAuthorised" src="~/controls/ItemSearch/SalesOrderLinesAuthorised/SalesOrderLinesAuthorised.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="SalesOrders" src="~/controls/ItemSearch/SalesOrders/SalesOrders.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="SRMAs" src="~/controls/ItemSearch/SRMAs/SRMAs.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="SRMALines" src="~/controls/ItemSearch/SRMALines/SRMALines.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="Stock" src="~/controls/ItemSearch/Stock/Stock.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="Service" src="~/controls/ItemSearch/Service/Service.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="Debits" src="~/controls/ItemSearch/Debits/Debits.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="SIGILines" src="~/controls/ItemSearch/SIGILines/SIGILines.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="Supplier" src="~/controls/ItemSearch/Supplier/Supplier.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="RequirementsWithBOM" src="~/controls/ItemSearch/ReqsWithBOM/ReqsWithBOM.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="RequiredBomItem" src="~/controls/ItemSearch/RequiredBomItem/RequiredBomItem.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="IpoStock" src="~/controls/ItemSearch/IpoStock/IpoStock.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="GISerialNumber" src="~/controls/ItemSearch/GISerialNumber/GISerialNumber.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="GITempSerialNumber" src="~/controls/ItemSearch/GITempSerialNumber/GITempSerialNumber.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="IhsSearch" src="~/controls/ItemSearch/IhsSearch/IhsSearch.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="RequiredClientBomItem" src="~/controls/ItemSearch/RequiredClientBomItem/RequiredClientBomItem.ascx"/>
        <add tagPrefix="ReboundItemSearch" tagName="GITempLotNumber" src="~/controls/ItemSearch/GITempLotNumber/GITempLotNumber.ascx"/>
		<add tagPrefix="ReboundItemSearch" tagName="SIDebitNotes" src="~/controls/ItemSearch/SIDebitNotes/SIDebitNotes.ascx"/>
        <!-- Setup Nuggets -->
        <add tagPrefix="ReboundSetupNugget" tagName="CommunicationLogType" src="~/controls/SetupNuggets/CommunicationLogType/CommunicationLogType.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CommunicationLogType_Add" src="~/controls/SetupNuggets/CommunicationLogType/Add/CommunicationLogType_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CommunicationLogType_Edit" src="~/controls/SetupNuggets/CommunicationLogType/Edit/CommunicationLogType_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="CompanyType" src="~/controls/SetupNuggets/CompanyType/CompanyType.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyType_Add" src="~/controls/SetupNuggets/CompanyType/Add/CompanyType_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CompanyType_Edit" src="~/controls/SetupNuggets/CompanyType/Edit/CompanyType_Edit.ascx"/>
        <!-- Setup Nuggets PDF Document file size RP-2379 -->
        <add tagPrefix="ReboundSetupNugget" tagName="PDFDocumentFileSize" src="~/controls/SetupNuggets/PDFDocumentFileSize/PDFDocumentFileSize.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PDFDocumentFileSize_Add" src="~/controls/SetupNuggets/PDFDocumentFileSize/Add/PDFDocumentFileSize_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PDFDocumentFileSize_Edit" src="~/controls/SetupNuggets/PDFDocumentFileSize/Edit/PDFDocumentFileSize_Edit.ascx"/>
        <!-- Setup Nuggets PDF Document file size RP-2379-->
        <add tagPrefix="ReboundSetupNugget" tagName="CountingMethod" src="~/controls/SetupNuggets/CountingMethod/CountingMethod.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CountingMethod_Add" src="~/controls/SetupNuggets/CountingMethod/Add/CountingMethod_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CountingMethod_Edit" src="~/controls/SetupNuggets/CountingMethod/Edit/CountingMethod_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Country" src="~/controls/SetupNuggets/Country/Country.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Country_Add" src="~/controls/SetupNuggets/Country/Add/Country_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Country_Edit" src="~/controls/SetupNuggets/Country/Edit/Country_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Country_ManageHeader" src="~/controls/SetupNuggets/Country/ManageHeader/Country_ManageHeader.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Country_DeleteHeader" src="~/controls/SetupNuggets/Country/DeleteHeader/Country_DeleteHeader.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Currency" src="~/controls/SetupNuggets/Currency/Currency.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Currency_Add" src="~/controls/SetupNuggets/Currency/Add/Currency_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Currency_Edit" src="~/controls/SetupNuggets/Currency/Edit/Currency_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Currency_EditRates" src="~/controls/SetupNuggets/Currency/EditRates/Currency_EditRates.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="CurrencyRateHistory" src="~/controls/SetupNuggets/CurrencyRateHistory/CurrencyRateHistory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CurrencyRateHistory_Edit" src="~/controls/SetupNuggets/CurrencyRateHistory/Edit/CurrencyRateHistory_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CurrencyRateHistory_Delete" src="~/controls/SetupNuggets/CurrencyRateHistory/Delete/CurrencyRateHistory_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Division" src="~/controls/SetupNuggets/Division/Division.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="LocalCurrency" src="~/controls/SetupNuggets/LocalCurrency/LocalCurrency.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LocalCurrency_Add" src="~/controls/SetupNuggets/LocalCurrency/Add/LocalCurrency_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LocalCurrency_Edit" src="~/controls/SetupNuggets/LocalCurrency/Edit/LocalCurrency_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="ExchangeRateHistory" src="~/controls/SetupNuggets/ExchangeRateHistory/ExchangeRateHistory.ascx"/>
        <!--For Certificate Category-->
        <add tagPrefix="ReboundSetupNugget" tagName="CertificateCategory" src="~/controls/SetupNuggets/CertificateCategory/CertificateCategory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CertificateCategory_Add" src="~/controls/SetupNuggets/CertificateCategory/Add/CertificateCategory_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="CertificateCategory_Edit" src="~/controls/SetupNuggets/CertificateCategory/Edit/CertificateCategory_Edit.ascx"/>
        <!--For GT Update-->
        <add tagPrefix="ReboundSetupNugget" tagName="GTUpdate" src="~/controls/SetupNuggets/GTUpdate/GTUpdate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GTUpdate_Add" src="~/controls/SetupNuggets/GTUpdate/Add/GTUpdate_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GTUpdate_Edit" src="~/controls/SetupNuggets/GTUpdate/Edit/GTUpdate_Edit.ascx"/>
        <!--For Certificate-->
        <add tagPrefix="ReboundSetupNugget" tagName="Certificate" src="~/controls/SetupNuggets/Certificate/Certificate.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Certificate_Add" src="~/controls/SetupNuggets/Certificate/Add/Certificate_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Certificate_Edit" src="~/controls/SetupNuggets/Certificate/Edit/Certificate_Edit.ascx"/>
        <!-- For 8 D Code -->
        <add tagPrefix="ReboundSetupNugget" tagName="EightDCode" src="~/controls/SetupNuggets/EightDCode/EightDCode.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EightDCode_Add" src="~/controls/SetupNuggets/EightDCode/Add/EightDCode_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EightDCode_Edit" src="~/controls/SetupNuggets/EightDCode/Edit/EightDCode_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="EightDSubCategory" src="~/controls/SetupNuggets/EightDSubCategory/EightDSubCategory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EightDSubCategory_Add" src="~/controls/SetupNuggets/EightDSubCategory/Add/EightDSubCategory_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EightDSubCategory_Edit" src="~/controls/SetupNuggets/EightDSubCategory/Edit/EightDSubCategory_Edit.ascx"/>
        <!--Code Start-->
        <add tagPrefix="ReboundSetupNugget" tagName="InvoiceSetting" src="~/controls/SetupNuggets/InvoiceSetting/InvoiceSetting.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceSetting_Add" src="~/controls/SetupNuggets/InvoiceSetting/Add/InvoiceSetting_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="InvoiceSetting_Edit" src="~/controls/SetupNuggets/InvoiceSetting/Edit/InvoiceSetting_Edit.ascx"/>
        <!--Code End-->
        <!--Start:- Add a new Control in SetupNuggets-->
        <add tagPrefix="ReboundSetupNugget" tagName="EmailComposer" src="~/controls/SetupNuggets/EmailComposer/EmailComposer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EmailComposer_Edit" src="~/controls/SetupNuggets/EmailComposer/Edit/EmailComposer_Edit.ascx"/>
        <!--Start:- Add a new Control in SetupNuggets-->
        <add tagPrefix="ReboundForm" tagName="Division_Add" src="~/controls/SetupNuggets/Division/Add/Division_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Division_Edit" src="~/controls/SetupNuggets/Division/Edit/Division_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="DivisionMembers" src="~/controls/SetupNuggets/DivisionMembers/DivisionMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="DocHeaderImage" src="~/controls/SetupNuggets/DocHeaderImage/DocHeaderImage.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DocHeaderImage_Add" src="~/controls/SetupNuggets/DocHeaderImage/Add/DocHeaderImage_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DocHeaderImage_Delete" src="~/controls/SetupNuggets/DocHeaderImage/Delete/DocHeaderImage_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DocHeaderImage_Edit" src="~/controls/SetupNuggets/DocHeaderImage/Edit/DocHeaderImage_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="DocFooters" src="~/controls/SetupNuggets/DocFooters/DocFooters.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="WarningMessage" src="~/controls/SetupNuggets/WarningMessage/WarningMessage.ascx"/>
        <add tagPrefix="ReboundForm" tagName="WarningMessage_Edit" src="~/controls/SetupNuggets/WarningMessage/Edit/WarningMessage_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="WarningMessage_Add" src="~/controls/SetupNuggets/WarningMessage/Add/WarningMessage_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DocFooters_Edit" src="~/controls/SetupNuggets/DocFooters/Edit/DocFooters_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalCountryList" src="~/controls/SetupNuggets/GlobalCountryList/GlobalCountryList.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalCountryList_Add" src="~/controls/SetupNuggets/GlobalCountryList/Add/GlobalCountryList_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalCountryList_Edit" src="~/controls/SetupNuggets/GlobalCountryList/Edit/GlobalCountryList_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalCurrencyList" src="~/controls/SetupNuggets/GlobalCurrencyList/GlobalCurrencyList.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalCurrencyList_Add" src="~/controls/SetupNuggets/GlobalCurrencyList/Add/GlobalCurrencyList_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalCurrencyList_Edit" src="~/controls/SetupNuggets/GlobalCurrencyList/Edit/GlobalCurrencyList_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="IndustryType" src="~/controls/SetupNuggets/IndustryType/IndustryType.ascx"/>
        <add tagPrefix="ReboundForm" tagName="IndustryType_Add" src="~/controls/SetupNuggets/IndustryType/Add/IndustryType_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="IndustryType_Edit" src="~/controls/SetupNuggets/IndustryType/Edit/IndustryType_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="EntertainmentType" src="~/controls/SetupNuggets/EntertainmentType/EntertainmentType.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EntertainmentType_Add" src="~/controls/SetupNuggets/EntertainmentType/Add/EntertainmentType_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EntertainmentType_Edit" src="~/controls/SetupNuggets/EntertainmentType/Edit/EntertainmentType_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Incoterm" src="~/controls/SetupNuggets/Incoterm/Incoterm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Incoterm_Add" src="~/controls/SetupNuggets/Incoterm/Add/Incoterm_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Incoterm_Edit" src="~/controls/SetupNuggets/Incoterm/Edit/Incoterm_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Incoterm_Disable" src="~/controls/SetupNuggets/Incoterm/Disable/Incoterm_Disable.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Package" src="~/controls/SetupNuggets/Package/Package.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Package_Add" src="~/controls/SetupNuggets/Package/Add/Package_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Package_Edit" src="~/controls/SetupNuggets/Package/Edit/Package_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Client" src="~/controls/SetupNuggets/Client/Client.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Client_Edit" src="~/controls/SetupNuggets/Client/Edit/Client_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="MasterLogin" src="~/controls/SetupNuggets/MasterLogin/MasterLogin.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MasterLogin_Confirm" src="~/controls/SetupNuggets/MasterLogin/Confirm/MasterLogin_Confirm.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Product" src="~/controls/SetupNuggets/Product/Product.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Product_Add" src="~/controls/SetupNuggets/Product/Add/Product_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Product_Edit" src="~/controls/SetupNuggets/Product/Edit/Product_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="ProductDutyRateHistory" src="~/controls/SetupNuggets/ProductDutyRateHistory/ProductDutyRateHistory.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalProductDutyRateHistory" src="~/controls/SetupNuggets/GlobalProductDutyRateHistory/GlobalProductDutyRateHistory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProductDutyRateHistory_Edit" src="~/controls/SetupNuggets/GlobalProductDutyRateHistory/Edit/GlobalProductDutyRateHistory_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Reason" src="~/controls/SetupNuggets/Reason/Reason.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Reason_Add" src="~/controls/SetupNuggets/Reason/Add/Reason_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Reason_Edit" src="~/controls/SetupNuggets/Reason/Edit/Reason_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityGroups" src="~/controls/SetupNuggets/SecurityGroups/SecurityGroups.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroups_Add" src="~/controls/SetupNuggets/SecurityGroups/Add/SecurityGroups_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroups_Edit" src="~/controls/SetupNuggets/SecurityGroups/Edit/SecurityGroups_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroups_Delete" src="~/controls/SetupNuggets/SecurityGroups/Delete/SecurityGroups_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroups_Clone" src="~/controls/SetupNuggets/SecurityGroups/Clone/SecurityGroups_Clone.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityUsers" src="~/controls/SetupNuggets/SecurityUsers/SecurityUsers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_Add" src="~/controls/SetupNuggets/SecurityUsers/Add/SecurityUsers_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_AddUsers" src="~/controls/SetupNuggets/SecurityUsers/AddUsers/SecurityUsers_AddUsers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_Edit" src="~/controls/SetupNuggets/SecurityUsers/Edit/SecurityUsers_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_Disable" src="~/controls/SetupNuggets/SecurityUsers/Disable/SecurityUsers_Disable.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_Transfer" src="~/controls/SetupNuggets/SecurityUsers/Transfer/SecurityUsers_Transfer.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityUserGroups" src="~/controls/SetupNuggets/SecurityUserGroups/SecurityUserGroups.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUserGroups_EditMembers" src="~/controls/SetupNuggets/SecurityUserGroups/EditMembers/SecurityUserGroups_EditMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityGroupMembers" src="~/controls/SetupNuggets/SecurityGroupMembers/SecurityGroupMembers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroupMembers_EditMembers" src="~/controls/SetupNuggets/SecurityGroupMembers/EditMembers/SecurityGroupMembers_EditMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalSecurityGroupMembers" src="~/controls/SetupNuggets/GlobalSecurityGroupMembers/GlobalSecurityGroupMembers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalSecurityGroupMembers_EditMembers" src="~/controls/SetupNuggets/GlobalSecurityGroupMembers/EditMembers/GlobalSecurityGroupMembers_EditMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityGroupPermissionsGeneral" src="~/controls/SetupNuggets/SecurityGroupPermissionsGeneral/SecurityGroupPermissionsGeneral.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroupPermissionsGeneral_Edit" src="~/controls/SetupNuggets/SecurityGroupPermissionsGeneral/Edit/SecurityGroupPermissionsGeneral_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalSecGroupPermissionsGeneral" src="~/controls/SetupNuggets/GlobalSecGroupPermissionsGeneral/GlobalSecGroupPermissionsGeneral.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalSecGroupPermissionsGeneral_Edit" src="~/controls/SetupNuggets/GlobalSecGroupPermissionsGeneral/Edit/GlobalSecGroupPermissionsGeneral_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="TabSecurity" src="~/controls/SetupNuggets/TabSecurity/TabSecurity.ascx"/>
        <add tagPrefix="ReboundForm" tagName="TabSecurity_Edit" src="~/controls/SetupNuggets/TabSecurity/Edit/TabSecurity_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityGroupPermissionsReports" src="~/controls/SetupNuggets/SecurityGroupPermissionsReports/SecurityGroupPermissionsReports.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroupPermissionsReports_Edit" src="~/controls/SetupNuggets/SecurityGroupPermissionsReports/Edit/SecurityGroupPermissionsReports_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Sequencer" src="~/controls/SetupNuggets/Sequencer/Sequencer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sequencer_Edit" src="~/controls/SetupNuggets/Sequencer/Edit/Sequencer_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="ShipVia" src="~/controls/SetupNuggets/ShipVia/ShipVia.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ShipVia_Add" src="~/controls/SetupNuggets/ShipVia/Add/ShipVia_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ShipVia_Edit" src="~/controls/SetupNuggets/ShipVia/Edit/ShipVia_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="StockLogReason" src="~/controls/SetupNuggets/StockLogReason/StockLogReason.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockLogReason_Add" src="~/controls/SetupNuggets/StockLogReason/Add/StockLogReason_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockLogReason_Edit" src="~/controls/SetupNuggets/StockLogReason/Edit/StockLogReason_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Tax" src="~/controls/SetupNuggets/Tax/Tax.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Tax_Add" src="~/controls/SetupNuggets/Tax/Add/Tax_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Tax_Edit" src="~/controls/SetupNuggets/Tax/Edit/Tax_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Tax_EditRates" src="~/controls/SetupNuggets/Tax/EditRates/Tax_EditRates.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="TaxRateHistory" src="~/controls/SetupNuggets/TaxRateHistory/TaxRateHistory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="TaxRateHistory_Delete" src="~/controls/SetupNuggets/TaxRateHistory/Delete/TaxRateHistory_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalTax" src="~/controls/SetupNuggets/GlobalTax/GlobalTax.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalTax_Add" src="~/controls/SetupNuggets/GlobalTax/Add/GlobalTax_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalTax_Edit" src="~/controls/SetupNuggets/GlobalTax/Edit/GlobalTax_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalTax_EditRates" src="~/controls/SetupNuggets/GlobalTax/EditRates/GlobalTax_EditRates.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalTaxRateHistory" src="~/controls/SetupNuggets/GlobalTaxRateHistory/GlobalTaxRateHistory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalTaxRateHistory_Delete" src="~/controls/SetupNuggets/GlobalTaxRateHistory/Delete/GlobalTaxRateHistory_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Team" src="~/controls/SetupNuggets/Team/Team.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Team_Add" src="~/controls/SetupNuggets/Team/Add/Team_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Team_Edit" src="~/controls/SetupNuggets/Team/Edit/Team_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="TeamMembers" src="~/controls/SetupNuggets/TeamMembers/TeamMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Terms" src="~/controls/SetupNuggets/Terms/Terms.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Terms_Add" src="~/controls/SetupNuggets/Terms/Add/Terms_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Terms_Edit" src="~/controls/SetupNuggets/Terms/Edit/Terms_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SourcingLinks" src="~/controls/SetupNuggets/SourcingLinks/SourcingLinks.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SourcingLinks_Add" src="~/controls/SetupNuggets/SourcingLinks/Add/SourcingLinks_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SourcingLinks_Edit" src="~/controls/SetupNuggets/SourcingLinks/Edit/SourcingLinks_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SourcingLinks_Delete" src="~/controls/SetupNuggets/SourcingLinks/Delete/SourcingLinks_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="User" src="~/controls/SetupNuggets/User/User.ascx"/>
        <add tagPrefix="ReboundForm" tagName="User_Add" src="~/controls/SetupNuggets/User/Add/User_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="User_Edit" src="~/controls/SetupNuggets/User/Edit/User_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="User_Address" src="~/controls/SetupNuggets/User/Address/User_Address.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Warehouse" src="~/controls/SetupNuggets/Warehouse/Warehouse.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Warehouse_Add" src="~/controls/SetupNuggets/Warehouse/Add/Warehouse_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Warehouse_Edit" src="~/controls/SetupNuggets/Warehouse/Edit/Warehouse_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Warehouse_Default" src="~/controls/SetupNuggets/Warehouse/Default/Warehouse_Default.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="UserProfile" src="~/controls/SetupNuggets/UserProfile/UserProfile.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UserProfile_Edit" src="~/controls/SetupNuggets/UserProfile/Edit/UserProfile_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UserProfile_ChangePassword" src="~/controls/SetupNuggets/UserProfile/ChangePassword/UserProfile_ChangePassword.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UserProfile_ResetPassword" src="~/controls/SetupNuggets/UserProfile/ResetPassword/UserProfile_ResetPassword.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="UserPreferences" src="~/controls/SetupNuggets/UserPreferences/UserPreferences.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UserPreferences_Edit" src="~/controls/SetupNuggets/UserPreferences/Edit/UserPreferences_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="MailMessageGroups" src="~/controls/SetupNuggets/MailMessageGroups/MailMessageGroups.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessageGroups_Add" src="~/controls/SetupNuggets/MailMessageGroups/Add/MailMessageGroups_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessageGroups_Edit" src="~/controls/SetupNuggets/MailMessageGroups/Edit/MailMessageGroups_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessageGroups_Delete" src="~/controls/SetupNuggets/MailMessageGroups/Delete/MailMessageGroups_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="MailMessageGroupMembers" src="~/controls/SetupNuggets/MailMessageGroupMembers/MailMessageGroupMembers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessageGroupMembers_EditMembers" src="~/controls/SetupNuggets/MailMessageGroupMembers/EditMembers/MailMessageGroupMembers_EditMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="AppSettings" src="~/controls/SetupNuggets/AppSettings/AppSettings.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AppSettings_Edit" src="~/controls/SetupNuggets/AppSettings/Edit/AppSettings_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="ToDoListType" src="~/controls/SetupNuggets/ToDoListType/ToDoListType.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ToDoListType_Add" src="~/controls/SetupNuggets/ToDoListType/Add/ToDoListType_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ToDoListType_Edit" src="~/controls/SetupNuggets/ToDoListType/Edit/ToDoListType_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Division_Add" src="~/controls/SetupNuggets/Division/Add/Division_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Division_Edit" src="~/controls/SetupNuggets/Division/Edit/Division_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="DivisionMembers" src="~/controls/SetupNuggets/DivisionMembers/DivisionMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="DocHeaderImage" src="~/controls/SetupNuggets/DocHeaderImage/DocHeaderImage.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DocHeaderImage_Add" src="~/controls/SetupNuggets/DocHeaderImage/Add/DocHeaderImage_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DocHeaderImage_Delete" src="~/controls/SetupNuggets/DocHeaderImage/Delete/DocHeaderImage_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DocHeaderImage_Edit" src="~/controls/SetupNuggets/DocHeaderImage/Edit/DocHeaderImage_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="DocFooters" src="~/controls/SetupNuggets/DocFooters/DocFooters.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="WarningMessage" src="~/controls/SetupNuggets/WarningMessage/WarningMessage.ascx"/>
        <add tagPrefix="ReboundForm" tagName="WarningMessage_Edit" src="~/controls/SetupNuggets/WarningMessage/Edit/WarningMessage_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="WarningMessage_Add" src="~/controls/SetupNuggets/WarningMessage/Add/WarningMessage_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="DocFooters_Edit" src="~/controls/SetupNuggets/DocFooters/Edit/DocFooters_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalCountryList" src="~/controls/SetupNuggets/GlobalCountryList/GlobalCountryList.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalCountryList_Add" src="~/controls/SetupNuggets/GlobalCountryList/Add/GlobalCountryList_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalCountryList_Edit" src="~/controls/SetupNuggets/GlobalCountryList/Edit/GlobalCountryList_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalCurrencyList" src="~/controls/SetupNuggets/GlobalCurrencyList/GlobalCurrencyList.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalCurrencyList_Add" src="~/controls/SetupNuggets/GlobalCurrencyList/Add/GlobalCurrencyList_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalCurrencyList_Edit" src="~/controls/SetupNuggets/GlobalCurrencyList/Edit/GlobalCurrencyList_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="IndustryType" src="~/controls/SetupNuggets/IndustryType/IndustryType.ascx"/>
        <add tagPrefix="ReboundForm" tagName="IndustryType_Add" src="~/controls/SetupNuggets/IndustryType/Add/IndustryType_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="IndustryType_Edit" src="~/controls/SetupNuggets/IndustryType/Edit/IndustryType_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Incoterm" src="~/controls/SetupNuggets/Incoterm/Incoterm.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Incoterm_Add" src="~/controls/SetupNuggets/Incoterm/Add/Incoterm_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Incoterm_Edit" src="~/controls/SetupNuggets/Incoterm/Edit/Incoterm_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Incoterm_Disable" src="~/controls/SetupNuggets/Incoterm/Disable/Incoterm_Disable.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="AS6081" src="~/controls/SetupNuggets/AS6081/AS6081.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_Add" src="~/controls/SetupNuggets/AS6081/Add/AS6081_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_Edit" src="~/controls/SetupNuggets/AS6081/Edit/AS6081_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_Delete" src="~/controls/SetupNuggets/AS6081/Delete/AS6081_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="AS6081_ROS" src="~/controls/SetupNuggets/AS6081_ROS/AS6081_ROS.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_ROS_Add" src="~/controls/SetupNuggets/AS6081_ROS/Add/AS6081_ROS_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_ROS_Edit" src="~/controls/SetupNuggets/AS6081_ROS/Edit/AS6081_ROS_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_ROS_Delete" src="~/controls/SetupNuggets/AS6081_ROS/Delete/AS6081_ROS_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="AS6081_RCS" src="~/controls/SetupNuggets/AS6081_RCS/AS6081_RCS.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_RCS_Add" src="~/controls/SetupNuggets/AS6081_RCS/Add/AS6081_RCS_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_RCS_Edit" src="~/controls/SetupNuggets/AS6081_RCS/Edit/AS6081_RCS_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AS6081_RCS_Delete" src="~/controls/SetupNuggets/AS6081_RCS/Delete/AS6081_RCS_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Package" src="~/controls/SetupNuggets/Package/Package.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Package_Add" src="~/controls/SetupNuggets/Package/Add/Package_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Package_Edit" src="~/controls/SetupNuggets/Package/Edit/Package_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Client" src="~/controls/SetupNuggets/Client/Client.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Client_Edit" src="~/controls/SetupNuggets/Client/Edit/Client_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="MasterLogin" src="~/controls/SetupNuggets/MasterLogin/MasterLogin.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MasterLogin_Confirm" src="~/controls/SetupNuggets/MasterLogin/Confirm/MasterLogin_Confirm.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Product" src="~/controls/SetupNuggets/Product/Product.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Product_Add" src="~/controls/SetupNuggets/Product/Add/Product_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Product_Edit" src="~/controls/SetupNuggets/Product/Edit/Product_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="ProductDutyRateHistory" src="~/controls/SetupNuggets/ProductDutyRateHistory/ProductDutyRateHistory.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalProductDutyRateHistory" src="~/controls/SetupNuggets/GlobalProductDutyRateHistory/GlobalProductDutyRateHistory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProductDutyRateHistory_Edit" src="~/controls/SetupNuggets/GlobalProductDutyRateHistory/Edit/GlobalProductDutyRateHistory_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Reason" src="~/controls/SetupNuggets/Reason/Reason.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Reason_Add" src="~/controls/SetupNuggets/Reason/Add/Reason_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Reason_Edit" src="~/controls/SetupNuggets/Reason/Edit/Reason_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityGroups" src="~/controls/SetupNuggets/SecurityGroups/SecurityGroups.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroups_Add" src="~/controls/SetupNuggets/SecurityGroups/Add/SecurityGroups_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroups_Edit" src="~/controls/SetupNuggets/SecurityGroups/Edit/SecurityGroups_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroups_Delete" src="~/controls/SetupNuggets/SecurityGroups/Delete/SecurityGroups_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroups_Clone" src="~/controls/SetupNuggets/SecurityGroups/Clone/SecurityGroups_Clone.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityUsers" src="~/controls/SetupNuggets/SecurityUsers/SecurityUsers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_Add" src="~/controls/SetupNuggets/SecurityUsers/Add/SecurityUsers_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_AddUsers" src="~/controls/SetupNuggets/SecurityUsers/AddUsers/SecurityUsers_AddUsers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_Edit" src="~/controls/SetupNuggets/SecurityUsers/Edit/SecurityUsers_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_Disable" src="~/controls/SetupNuggets/SecurityUsers/Disable/SecurityUsers_Disable.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUsers_Transfer" src="~/controls/SetupNuggets/SecurityUsers/Transfer/SecurityUsers_Transfer.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityUserGroups" src="~/controls/SetupNuggets/SecurityUserGroups/SecurityUserGroups.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityUserGroups_EditMembers" src="~/controls/SetupNuggets/SecurityUserGroups/EditMembers/SecurityUserGroups_EditMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityGroupMembers" src="~/controls/SetupNuggets/SecurityGroupMembers/SecurityGroupMembers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroupMembers_EditMembers" src="~/controls/SetupNuggets/SecurityGroupMembers/EditMembers/SecurityGroupMembers_EditMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalSecurityGroupMembers" src="~/controls/SetupNuggets/GlobalSecurityGroupMembers/GlobalSecurityGroupMembers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalSecurityGroupMembers_EditMembers" src="~/controls/SetupNuggets/GlobalSecurityGroupMembers/EditMembers/GlobalSecurityGroupMembers_EditMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityGroupPermissionsGeneral" src="~/controls/SetupNuggets/SecurityGroupPermissionsGeneral/SecurityGroupPermissionsGeneral.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroupPermissionsGeneral_Edit" src="~/controls/SetupNuggets/SecurityGroupPermissionsGeneral/Edit/SecurityGroupPermissionsGeneral_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalSecGroupPermissionsGeneral" src="~/controls/SetupNuggets/GlobalSecGroupPermissionsGeneral/GlobalSecGroupPermissionsGeneral.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalSecGroupPermissionsGeneral_Edit" src="~/controls/SetupNuggets/GlobalSecGroupPermissionsGeneral/Edit/GlobalSecGroupPermissionsGeneral_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="TabSecurity" src="~/controls/SetupNuggets/TabSecurity/TabSecurity.ascx"/>
        <add tagPrefix="ReboundForm" tagName="TabSecurity_Edit" src="~/controls/SetupNuggets/TabSecurity/Edit/TabSecurity_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SecurityGroupPermissionsReports" src="~/controls/SetupNuggets/SecurityGroupPermissionsReports/SecurityGroupPermissionsReports.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SecurityGroupPermissionsReports_Edit" src="~/controls/SetupNuggets/SecurityGroupPermissionsReports/Edit/SecurityGroupPermissionsReports_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Sequencer" src="~/controls/SetupNuggets/Sequencer/Sequencer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Sequencer_Edit" src="~/controls/SetupNuggets/Sequencer/Edit/Sequencer_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="ShipVia" src="~/controls/SetupNuggets/ShipVia/ShipVia.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ShipVia_Add" src="~/controls/SetupNuggets/ShipVia/Add/ShipVia_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ShipVia_Edit" src="~/controls/SetupNuggets/ShipVia/Edit/ShipVia_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="StockLogReason" src="~/controls/SetupNuggets/StockLogReason/StockLogReason.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockLogReason_Add" src="~/controls/SetupNuggets/StockLogReason/Add/StockLogReason_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockLogReason_Edit" src="~/controls/SetupNuggets/StockLogReason/Edit/StockLogReason_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Tax" src="~/controls/SetupNuggets/Tax/Tax.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Tax_Add" src="~/controls/SetupNuggets/Tax/Add/Tax_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Tax_Edit" src="~/controls/SetupNuggets/Tax/Edit/Tax_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Tax_EditRates" src="~/controls/SetupNuggets/Tax/EditRates/Tax_EditRates.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="TaxRateHistory" src="~/controls/SetupNuggets/TaxRateHistory/TaxRateHistory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="TaxRateHistory_Delete" src="~/controls/SetupNuggets/TaxRateHistory/Delete/TaxRateHistory_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalTax" src="~/controls/SetupNuggets/GlobalTax/GlobalTax.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalTax_Add" src="~/controls/SetupNuggets/GlobalTax/Add/GlobalTax_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalTax_Edit" src="~/controls/SetupNuggets/GlobalTax/Edit/GlobalTax_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalTax_EditRates" src="~/controls/SetupNuggets/GlobalTax/EditRates/GlobalTax_EditRates.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalTaxRateHistory" src="~/controls/SetupNuggets/GlobalTaxRateHistory/GlobalTaxRateHistory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalTaxRateHistory_Delete" src="~/controls/SetupNuggets/GlobalTaxRateHistory/Delete/GlobalTaxRateHistory_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Team" src="~/controls/SetupNuggets/Team/Team.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Team_Add" src="~/controls/SetupNuggets/Team/Add/Team_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Team_Edit" src="~/controls/SetupNuggets/Team/Edit/Team_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="TeamMembers" src="~/controls/SetupNuggets/TeamMembers/TeamMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Terms" src="~/controls/SetupNuggets/Terms/Terms.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Terms_Add" src="~/controls/SetupNuggets/Terms/Add/Terms_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Terms_Edit" src="~/controls/SetupNuggets/Terms/Edit/Terms_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="SourcingLinks" src="~/controls/SetupNuggets/SourcingLinks/SourcingLinks.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SourcingLinks_Add" src="~/controls/SetupNuggets/SourcingLinks/Add/SourcingLinks_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SourcingLinks_Edit" src="~/controls/SetupNuggets/SourcingLinks/Edit/SourcingLinks_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SourcingLinks_Delete" src="~/controls/SetupNuggets/SourcingLinks/Delete/SourcingLinks_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="User" src="~/controls/SetupNuggets/User/User.ascx"/>
        <add tagPrefix="ReboundForm" tagName="User_Add" src="~/controls/SetupNuggets/User/Add/User_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="User_Edit" src="~/controls/SetupNuggets/User/Edit/User_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="User_Address" src="~/controls/SetupNuggets/User/Address/User_Address.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="Warehouse" src="~/controls/SetupNuggets/Warehouse/Warehouse.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Warehouse_Add" src="~/controls/SetupNuggets/Warehouse/Add/Warehouse_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Warehouse_Edit" src="~/controls/SetupNuggets/Warehouse/Edit/Warehouse_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Warehouse_Default" src="~/controls/SetupNuggets/Warehouse/Default/Warehouse_Default.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="UserProfile" src="~/controls/SetupNuggets/UserProfile/UserProfile.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UserProfile_Edit" src="~/controls/SetupNuggets/UserProfile/Edit/UserProfile_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UserProfile_ChangePassword" src="~/controls/SetupNuggets/UserProfile/ChangePassword/UserProfile_ChangePassword.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UserProfile_ResetPassword" src="~/controls/SetupNuggets/UserProfile/ResetPassword/UserProfile_ResetPassword.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="UserPreferences" src="~/controls/SetupNuggets/UserPreferences/UserPreferences.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UserPreferences_Edit" src="~/controls/SetupNuggets/UserPreferences/Edit/UserPreferences_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="MailMessageGroups" src="~/controls/SetupNuggets/MailMessageGroups/MailMessageGroups.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessageGroups_Add" src="~/controls/SetupNuggets/MailMessageGroups/Add/MailMessageGroups_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessageGroups_Edit" src="~/controls/SetupNuggets/MailMessageGroups/Edit/MailMessageGroups_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessageGroups_Delete" src="~/controls/SetupNuggets/MailMessageGroups/Delete/MailMessageGroups_Delete.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="MailMessageGroupMembers" src="~/controls/SetupNuggets/MailMessageGroupMembers/MailMessageGroupMembers.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MailMessageGroupMembers_EditMembers" src="~/controls/SetupNuggets/MailMessageGroupMembers/EditMembers/MailMessageGroupMembers_EditMembers.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="AppSettings" src="~/controls/SetupNuggets/AppSettings/AppSettings.ascx"/>
        <add tagPrefix="ReboundForm" tagName="AppSettings_Edit" src="~/controls/SetupNuggets/AppSettings/Edit/AppSettings_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="ToDoListType" src="~/controls/SetupNuggets/ToDoListType/ToDoListType.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ToDoListType_Add" src="~/controls/SetupNuggets/ToDoListType/Add/ToDoListType_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ToDoListType_Edit" src="~/controls/SetupNuggets/ToDoListType/Edit/ToDoListType_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="ClientInvoiceHeaderImage" src="~/controls/SetupNuggets/ClientInvoiceHeaderImage/ClientInvoiceHeaderImage.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceHeaderImage_Add" src="~/controls/SetupNuggets/ClientInvoiceHeaderImage/Add/ClientInvoiceHeaderImage_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceHeaderImage_Delete" src="~/controls/SetupNuggets/ClientInvoiceHeaderImage/Delete/ClientInvoiceHeaderImage_Delete.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceHeaderImage_Edit" src="~/controls/SetupNuggets/ClientInvoiceHeaderImage/Edit/ClientInvoiceHeaderImage_Edit.ascx"/>
        <!--Add Printer Ref-->
        <add tagPrefix="ReboundSetupNugget" tagName="Printer" src="~/controls/SetupNuggets/Printer/Printer.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Printer_Add" src="~/controls/SetupNuggets/Printer/Add/Printer_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="Printer_Edit" src="~/controls/SetupNuggets/Printer/Edit/Printer_Edit.ascx"/>
        <!--Add PPVBOM Ref-->
        <add tagPrefix="ReboundSetupNugget" tagName="PPVBOM" src="~/controls/SetupNuggets/PPVBOM/PPVBOM.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PPVBOM_Add" src="~/controls/SetupNuggets/PPVBOM/Add/PPVBOM_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PPVBOM_Edit" src="~/controls/SetupNuggets/PPVBOM/Edit/PPVBOM_Edit.ascx"/>
        <!--End-->
        
        <!--Add OGELLicenses Ref-->
        <add tagPrefix="ReboundSetupNugget" tagName="OGELLicenses" src="~/controls/SetupNuggets/OGELLicenses/OGELLicenses.ascx"/>
        <add tagPrefix="ReboundForm" tagName="OGELLicenses_Add" src="~/controls/SetupNuggets/OGELLicenses/Add/OGELLicenses_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="OGELLicenses_Edit" src="~/controls/SetupNuggets/OGELLicenses/Edit/OGELLicenses_Edit.ascx"/>
        <!--End-->
        
        <!--Add LabelFullPath Ref-->
        <add tagPrefix="ReboundSetupNugget" tagName="LabelFullPath" src="~/controls/SetupNuggets/LabelFullPath/LabelFullPath.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LabelFullPath_Add" src="~/controls/SetupNuggets/LabelFullPath/Add/LabelFullPath_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LabelFullPath_Edit" src="~/controls/SetupNuggets/LabelFullPath/Edit/LabelFullPath_Edit.ascx"/>
        <!--End-->
        <!-- Setup Controls -->
        <add tagPrefix="ReboundSetup" namespace="Rebound.GlobalTrader.Site.Setup.Controls" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundSetupBox" namespace="Rebound.GlobalTrader.Site.Setup.Controls.Boxes" assembly="Rebound.GlobalTrader.Site"/>
        <add tagPrefix="ReboundSetupBox" tagName="Database" src="~/Setup/Controls/Boxes/Database/Database.ascx"/>
        <add tagPrefix="ReboundSetupBox" tagName="AddCompany" src="~/Setup/Controls/Boxes/AddCompany/AddCompany.ascx"/>
        <add tagPrefix="ReboundSetupBox" tagName="Users" src="~/Setup/Controls/Boxes/Users/<USER>"/>
        <add tagPrefix="ReboundSetupBox" tagName="Email" src="~/Setup/Controls/Boxes/Email/Email.ascx"/>
        <add tagPrefix="ReboundSetupBox" tagName="Sessions" src="~/Setup/Controls/Boxes/Sessions/Sessions.ascx"/>
        <add tagPrefix="ReboundSetupBox" tagName="DisableCompany" src="~/Setup/Controls/Boxes/DisableCompany/DisableCompany.ascx"/>
        <add tagPrefix="ReboundSetupBox" tagName="AppSettings" src="~/Setup/Controls/Boxes/AppSettings/AppSettings.ascx"/>
        <!--Start PDFDocuments-->
        <add tagPrefix="ReboundForm" tagName="PDFDocuments_Add" src="~/controls/Nuggets/PDFDocuments/Add/PDFDocuments_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PDFDocuments_Delete" src="~/controls/Nuggets/PDFDocuments/Delete/PDFDocuments_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="PDFDocuments" src="~/controls/Nuggets/PDFDocuments/PDFDocuments.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SORPDFDocuments_Add" src="~/controls/Nuggets/SORPDFDocuments/Add/SORPDFDocuments_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SORPDFDocuments_Delete" src="~/controls/Nuggets/SORPDFDocuments/Delete/SORPDFDocuments_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SORPDFDocuments" src="~/controls/Nuggets/SORPDFDocuments/SORPDFDocuments.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="PDFDocumentsDragDrop" src="~/controls/Nuggets/PDFDocumentsDragDrop/PDFDocumentsDragDrop.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="EndUserUndertakingPDFDragDrop" src="~/controls/Nuggets/EndUserUndertakingPDFDragDrop/EndUserUndertakingPDFDragDrop.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SAPDFDocumentsDragDrop" src="~/controls/Nuggets/SAPDFDocumentsDragDrop/SAPDFDocumentsDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PDFDocuments_AddDragDrop" src="~/controls/Nuggets/PDFDocumentsDragDrop/Add/PDFDocuments_AddDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EndUserUndertakingPDF_AddDragDrop" src="~/controls/Nuggets/EndUserUndertakingPDFDragDrop/Add/EndUserUndertakingPDF_AddDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EndUserUndertakingPDF_DeleteDragDrop" src="~/controls/Nuggets/EndUserUndertakingPDFDragDrop/Delete/EndUserUndertakingPDF_DeleteDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PDFDocuments_DeleteDragDrop" src="~/controls/Nuggets/PDFDocumentsDragDrop/Delete/PDFDocuments_DeleteDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SAPDFDocuments_AddDragDrop" src="~/controls/Nuggets/SAPDFDocumentsDragDrop/Add/SAPDFDocuments_AddDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SAPDFDocuments_DeleteDragDrop" src="~/controls/Nuggets/SAPDFDocumentsDragDrop/Delete/SAPDFDocuments_DeleteDragDrop.ascx"/>
        <!--POD Drag and Drop Date: 02 July 2021-->
        <add tagPrefix="ReboundNugget" tagName="PODPDFDocumentsDragDrop" src="~/controls/Nuggets/PODPDFDocumentsDragDrop/PODPDFDocumentsDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PODPDFDocuments_AddDragDrop" src="~/controls/Nuggets/PODPDFDocumentsDragDrop/Add/PODPDFDocuments_AddDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PODPDFDocuments_DeleteDragDrop" src="~/controls/Nuggets/PODPDFDocumentsDragDrop/Delete/PODPDFDocuments_DeleteDragDrop.ascx"/>
        <!--POD Drag and Drop Date: 02 July 2021-->
        <add tagPrefix="ReboundNugget" tagName="INSPDFDocumentsDragDrop" src="~/controls/Nuggets/INSPDFDocumentsDragDrop/INSPDFDocumentsDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="INSPDFDocuments_AddDragDrop" src="~/controls/Nuggets/INSPDFDocumentsDragDrop/Add/INSPDFDocuments_AddDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="INSPDFDocuments_DeleteDragDrop" src="~/controls/Nuggets/INSPDFDocumentsDragDrop/Delete/INSPDFDocuments_DeleteDragDrop.ascx"/>
        <!--POR Report Documents Date: 05 jan 2022-->
        <add tagPrefix="ReboundForm" tagName="PORPDFDocuments_Add" src="~/controls/Nuggets/PORPDFDocuments/Add/PORPDFDocuments_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="PORPDFDocuments_Delete" src="~/controls/Nuggets/PORPDFDocuments/Delete/PORPDFDocuments_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="PORPDFDocuments" src="~/controls/Nuggets/PORPDFDocuments/PORPDFDocuments.ascx"/>
        <!--SOR Drag and Drop Date: 06 July 2015-->
        <add tagPrefix="ReboundForm" tagName="SORPDFDocsDragDrop_Add" src="~/controls/Nuggets/SORPDFDocsDragDrop/Add/SORPDFDocsDragDrop_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SORPDFDocsDragDrop_Delete" src="~/controls/Nuggets/SORPDFDocsDragDrop/Delete/SORPDFDocsDragDrop_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SORPDFDocsDragDrop" src="~/controls/Nuggets/SORPDFDocsDragDrop/SORPDFDocsDragDrop.ascx"/>
        <!--SOR Drag and Drop Date: 09 Aug 2018-->
        <add tagPrefix="ReboundUI" tagName="SOMain_UploadPDFAdd" src="~/controls/Nuggets/SOMainInfo/SOMain_UploadPDF/SOMain_UploadPDFAdd.ascx"/>
        <add tagPrefix="ReboundUI" tagName="SOMain_UploadPDF" src="~/controls/Nuggets/SOMainInfo/SOMain_UploadPDF/SOMain_UploadPDF.ascx"/>
        <!--SOR Drag and Drop Date: 06 July 2015-->
        <add tagPrefix="ReboundNugget" tagName="StockImagesDragDrop" src="~/controls/Nuggets/StockImagesDragDrop/StockImagesDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockImagesDragDrop_Add" src="~/controls/Nuggets/StockImagesDragDrop/Add/StockImagesDragDrop_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockImagesDragDrop_Delete" src="~/controls/Nuggets/StockImagesDragDrop/Delete/StockImagesDragDrop_Delete.ascx"/>
        <!--SA Drag and Drop Date: 17 September 2021-->
        <add tagPrefix="ReboundNugget" tagName="SAImagesDragDrop" src="~/controls/Nuggets/SAImageDragDrop/SAImageDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SAImagesDragDrop_Add" src="~/controls/Nuggets/SAImageDragDrop/Add/SAImageDragDrop_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SAImagesDragDrop_Delete" src="~/controls/Nuggets/SAImageDragDrop/Delete/SAImageDragDrop_Delete.ascx"/>
        <!--Image Drag and Drop Date: 09 Aug 2018-->
        <add tagPrefix="ReboundNugget" tagName="ImagesDragDrop" src="~/controls/Nuggets/UploadImagesDragDrop/UploadImagesDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ImagesDragDrop_Add" src="~/controls/Nuggets/UploadImagesDragDrop/Add/UploadImagesDragDrop_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ImagesDragDrop_Delete" src="~/controls/Nuggets/UploadImagesDragDrop/Delete/UploadImagesDragDrop_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="StockMultipleImageDragDrop" src="~/controls/Nuggets/StockMultipleImageDragDrop/StockMultipleImageDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMultipleImageDragDrop_Add" src="~/controls/Nuggets/StockMultipleImageDragDrop/Add/StockMultipleImageDragDrop_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="StockMultipleImageDragDrop_Delete" src="~/controls/Nuggets/StockMultipleImageDragDrop/Delete/StockMultipleImageDragDrop_Delete.ascx"/>
        <!--<add tagPrefix="ReboundNugget" tagName="GILineImagesDragDrop" src="~/controls/Nuggets/GILineUploadImagesDragDrop/GILineUploadImagesDragDrop.ascx" />
        <add tagPrefix="ReboundForm" tagName="GILineImagesDragDrop_Add" src="~/controls/Nuggets/GILineUploadImagesDragDrop/Add/GILineUploadImagesDragDrop_Add.ascx" />
        <add tagPrefix="ReboundForm" tagName="GILineImagesDragDrop_Delete" src="~/controls/Nuggets/GILineUploadImagesDragDrop/Delete/GILineUploadImagesDragDrop_Delete.ascx" />-->
        <add tagPrefix="ReboundNugget" tagName="MultipleImageDragDrop" src="~/controls/Nuggets/MultipleImageDragDrop/MultipleImageDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MultipleImageDragDrop_Add" src="~/controls/Nuggets/MultipleImageDragDrop/Add/MultipleImageDragDrop_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="MultipleImageDragDrop_Delete" src="~/controls/Nuggets/MultipleImageDragDrop/Delete/MultipleImageDragDrop_Delete.ascx"/>
        <!--End PDFDocuments -->
        <add tagPrefix="ReboundNugget" tagName="EXCELDocuments" src="~/controls/Nuggets/EXCELDocuments/EXCELDocuments.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EXCELDocuments_Add" src="~/controls/Nuggets/EXCELDocuments/Add/EXCELDocuments_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EXCELDocuments_Delete" src="~/controls/Nuggets/EXCELDocuments/Delete/EXCELDocuments_Delete.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="EXCELDocumentsDragDrop" src="~/controls/Nuggets/EXCELDocumentsDragDrop/EXCELDocumentsDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EXCELDocuments_AddDragDrop" src="~/controls/Nuggets/EXCELDocumentsDragDrop/Add/EXCELDocuments_AddDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="EXCELDocuments_DeleteDragDrop" src="~/controls/Nuggets/EXCELDocumentsDragDrop/Delete/EXCELDocuments_DeleteDragDrop.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="UploadExcelDragDrop" src="~/controls/Nuggets/UploadExcelDragDrop/UploadExcelDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UploadExcel_AddDragDrop" src="~/controls/Nuggets/UploadExcelDragDrop/Add/UploadExcel_AddDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UploadExcel_DeleteDragDrop" src="~/controls/Nuggets/UploadExcelDragDrop/Delete/UploadExcel_DeleteDragDrop.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SOEXCELDocFileDragDrop" src="~/controls/Nuggets/SOEXCELDocFileDragDrop/SOEXCELDocFileDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOEXCELDocFileDocuments_AddDragDrop" src="~/controls/Nuggets/SOEXCELDocFileDragDrop/Add/SOEXCELDocFileDocuments_AddDragDrop.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SOEXCELDocFileDocuments_DeleteDragDrop" src="~/controls/Nuggets/SOEXCELDocFileDragDrop/Delete/SOEXCELDocFileDocuments_DeleteDragDrop.ascx"/>
        <!--Expedite History in HUBRFQ  -->
        <add tagPrefix="ReboundNugget" tagName="ExpediteHistory" src="~/controls/Nuggets/ExpediteHistory/ExpediteHistory.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalProduct" src="~/controls/SetupNuggets/GlobalProduct/GlobalProduct.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProduct_Add" src="~/controls/SetupNuggets/GlobalProduct/Add/GlobalProduct_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProduct_Edit" src="~/controls/SetupNuggets/GlobalProduct/Edit/GlobalProduct_Edit.ascx"/>
        <!--Global Product Name Added By Suhail  -->
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalProductName" src="~/controls/SetupNuggets/GlobalProductName/GlobalProductName.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProductName_Add" src="~/controls/SetupNuggets/GlobalProductName/Add/GlobalProductName_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProductName_Edit" src="~/controls/SetupNuggets/GlobalProductName/Edit/GlobalProductName_Edit.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="LabelSetup" src="~/controls/SetupNuggets/LabelSetup/LabelSetup.ascx"/>
        <add tagPrefix="ReboundSetupNugget" tagName="LabelSetupItem" src="~/controls/SetupNuggets/LabelSetupItem/LabelSetupItem.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LabelSetupItem_Add" src="~/controls/SetupNuggets/LabelSetupItem/Add/LabelSetupItem_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="LabelSetupItem_Edit" src="~/controls/SetupNuggets/LabelSetupItem/Edit/LabelSetupItem_Edit.ascx"/>
        <!--Add Restricted Manufacture -->
        <add tagPrefix="ReboundSetupNugget" tagName="RestrictedManufacture" src="~/controls/SetupNuggets/RestrictedManufacture/RestrictedManufacture.ascx"/>
        <add tagPrefix="ReboundForm" tagName="RestrictedManufacture_Add" src="~/controls/SetupNuggets/RestrictedManufacture/Add/RestrictedManufacture_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="RestrictedManufacture_Edit" src="~/controls/SetupNuggets/RestrictedManufacture/Edit/RestrictedManufacture_Edit.ascx"/>
        <!--Add ECCN Code -->
        <add tagPrefix="ReboundSetupNugget" tagName="ECCN" src="~/controls/SetupNuggets/ECCN/ECCN.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ECCN_Add" src="~/controls/SetupNuggets/ECCN/Add/ECCN_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ECCN_Edit" src="~/controls/SetupNuggets/ECCN/Edit/ECCN_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ECCN_EditMap" src="~/controls/SetupNuggets/ECCN/EditMap/ECCN_EditMap.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ECCN_EditClone" src="~/controls/SetupNuggets/ECCN/Clone/ECCN_EditClone.ascx"/>
        <add tagPrefix="ReboundNugget" tagName="SSMainInfo" src="~/controls/Nuggets/SSMainInfo/SSMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SSMainInfo_Cancel" src="~/controls/Nuggets/SSMainInfo/Cancel/SSMainInfo_Cancel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SSMainInfo_Close" src="~/controls/Nuggets/SSMainInfo/Close/SSMainInfo_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SSMainInfo_Confirm" src="~/controls/Nuggets/SSMainInfo/Confirm/SSMainInfo_Confirm.ascx"/>
        <!--End-->
        <add tagPrefix="ReboundNugget" tagName="SSMainInfo" src="~/controls/Nuggets/SSMainInfo/SSMainInfo.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SSMainInfo_Cancel" src="~/controls/Nuggets/SSMainInfo/Cancel/SSMainInfo_Cancel.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SSMainInfo_Close" src="~/controls/Nuggets/SSMainInfo/Close/SSMainInfo_Close.ascx"/>
        <add tagPrefix="ReboundForm" tagName="SSMainInfo_Confirm" src="~/controls/Nuggets/SSMainInfo/Confirm/SSMainInfo_Confirm.ascx"/>
        <!--End-->
        <!--Global Product Main Category Added By Arpit  -->
        <add tagPrefix="ReboundSetupNugget" tagName="GlobalProductMainCategory" src="~/controls/SetupNuggets/GlobalProductMainCategory/GlobalProductMainCategory.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProductMainCategory_Add" src="~/controls/SetupNuggets/GlobalProductMainCategory/Add/GlobalProductMainCategory_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProductMainCategory_Edit" src="~/controls/SetupNuggets/GlobalProductMainCategory/Edit/GlobalProductMainCategory_Edit.ascx"/>
        <add tagPrefix="ReboundForm" tagName="GlobalProductMainCategory_Map" src="~/controls/SetupNuggets/GlobalProductMainCategory/Map/GlobalProductMainCategory_Map.ascx"/>
        <!-- ContactGroup control  -->
        <add tagPrefix="ReboundNugget" tagName="ContactGroup" src="~/controls/Nuggets/ContactGroup/ContactGroup.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ContactGroup_Add" src="~/controls/Nuggets/ContactGroup/ContactGroup_Add/ContactGroup_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMItems_ConfirmPartwatch" src="~/controls/Nuggets/BOMItems/ConfirmPartwatch/BOMItems_ConfirmPartwatch.ascx"/>
        <add tagPrefix="ReboundForm" tagName="BOMItems_ConfirmRemovePartwatch" src="~/controls/Nuggets/BOMItems/ConfirmRemovePartwatch/BOMItems_ConfirmRemovePartwatch.ascx"/>
        <!--Start Added By Devendra-->
        <add tagPrefix="ReboundNugget" tagName="UtilityPriceQuoteImport" src="~/controls/Nuggets/UtilityPriceQuoteImport/UtilityPriceQuoteImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityPriceQuote_Import" src="~/controls/Nuggets/UtilityPriceQuoteImport/UtilityPriceQuote_Import/UtilityPriceQuote_Import.ascx"/>
        <!--End-->
        <add tagPrefix="ReboundSetupNugget" tagName="ClientInvoiceHeader" src="~/controls/SetupNuggets/ClientInvoiceHeader/ClientInvoiceHeader.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceHeader_Add" src="~/controls/SetupNuggets/ClientInvoiceHeader/Add/ClientInvoiceHeader_Add.ascx"/>
        <add tagPrefix="ReboundForm" tagName="ClientInvoiceHeader_Edit" src="~/controls/SetupNuggets/ClientInvoiceHeader/Edit/ClientInvoiceHeader_Edit.ascx"/>
        <!--Start Added By Devendra-->
        <add tagPrefix="ReboundNugget" tagName="UtilityPriceQuoteImport" src="~/controls/Nuggets/UtilityPriceQuoteImport/UtilityPriceQuoteImport.ascx"/>
        <add tagPrefix="ReboundForm" tagName="UtilityPriceQuote_Import" src="~/controls/Nuggets/UtilityPriceQuoteImport/UtilityPriceQuote_Import/UtilityPriceQuote_Import.ascx"/>
        <!--End-->
	    <add tagPrefix="ReboundNugget" tagName="UtilityProspectiveOfferImport" src="~/controls/Nuggets/UtilityProspectiveOfferImport/UtilityProspectiveOfferImport.ascx" />
	    <add tagPrefix="ReboundForm" tagName="UtilityProspectiveOffer_Import" src="~/controls/Nuggets/UtilityProspectiveOfferImport/UtilityProspectiveOffer_Import/UtilityProspectiveOffer_Import.ascx" />
        <!--start add POHub Auto Sourcing-->
		<add tagPrefix="ReboundNugget" tagName="POHubAutoSourcing" src="~/controls/Nuggets/POHubAutoSourcing/POHubAutoSourcing.ascx"/>
		<!--End-->
		<add tagPrefix="ReboundForm" tagName="POHubSourcing_EditEpoBulk" src="~/controls/Nuggets/POHubSourcing/EditEpoBulk/EditEpoBulk.ascx"/>
	    <add tagPrefix="ReboundItemSearch" tagName="SourcingBulkEditLog" src="~/controls/ItemSearch/SourcingBulkEditLog/SourcingBulkEditLog.ascx"/>
		<add tagPrefix="ReboundForm" tagName="POHubSourcing_BulkEditLog" src="~/controls/Nuggets/POHubSourcing/BulkEditLog/POHubSourcing_BulkEditLog.ascx"/>


		  <!--Add StarRating Ref-->
		  <add tagPrefix="ReboundSetupNugget" tagName="StarRating" src="~/controls/SetupNuggets/StarRating/StarRating.ascx"/>
		  <add tagPrefix="ReboundForm" tagName="StarRating_Add" src="~/controls/SetupNuggets/StarRating/Add/StarRating_Add.ascx"/>
		  <add tagPrefix="ReboundForm" tagName="HubImportSourcingResult" src="~/controls/Nuggets/BOMItems/HubImportSourcingResult/HubImportSourcingResult.ascx"/>
		  <!--End-->
				<add tagPrefix="ReboundNugget" tagName="InvoiceExportHistory" src="~/controls/Nuggets/InvoiceExportHistory/InvoiceExportHistory.ascx"/>
	  </controls>
    </pages>
    <!--<customErrors mode="Off"  >-->
    <!--<error statusCode="404" redirect="~/CustomError.aspx"/>-->
    <!--</customErrors>-->
    <!--<customErrors mode="On" defaultRedirect="~/ErrorPage/DefaultError.aspx" redirectMode="ResponseRewrite">
      <error statusCode="400" redirect="~/ErrorPage/400Error.aspx" />
      <error statusCode="403" redirect="~/ErrorPage/403Error.aspx" />
      <error statusCode="404" redirect="~/ErrorPage/404Error.aspx" />
      <error statusCode="405" redirect="~/ErrorPage/405Error.aspx" />
      <error statusCode="408" redirect="~/ErrorPage/408Error.aspx" />
      <error statusCode="500" redirect="~/ErrorPage/DefaultError.aspx" />
      <error statusCode="503" redirect="~/ErrorPage/DefaultError.aspx" />
    </customErrors>-->
    <compilation debug="false" batch="false" targetFramework="4.8">
      <assemblies>
        <add assembly="System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Security, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="System.Xml, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B77A5C561934E089"/>
        <add assembly="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <add assembly="Microsoft.Build.Framework, Version=4.0.0.0, Culture=neutral, PublicKeyToken=B03F5F7F11D50A3A"/>
        <!--Added by Rahil-->
        <add assembly="System.Web.Abstractions, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Helpers, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Routing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.Mvc, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Web.WebPages, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31BF3856AD364E35"/>
        <add assembly="System.Runtime, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"/>
        <!--Added by Rahil-->
      </assemblies>
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"/>
      </buildProviders>
    </compilation>
    <httpHandlers>
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91" validate="false"/>
    </httpHandlers>
    <sessionState mode="Custom" customProvider="DefaultSessionProvider" timeout="120">
      <providers>
        <clear/>
        <add name="DefaultSessionProvider" type="System.Web.Providers.DefaultSessionStateProvider, System.Web.Providers, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35" connectionStringName="SQLAzureSession"/>
      </providers>
    </sessionState>
    <!-- Commented this block to disable the AI code //RP-845 -->
    <!--<httpModules>
      <add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web"/>
    </httpModules>-->
  </system.web>
  <system.webServer>
    <!--<httpErrors errorMode="Custom" existingResponse="Auto" defaultResponseMode="ExecuteURL" >
      <remove statusCode="404" />
      <error statusCode="404" path="/ErrorPage/404Error.aspx" responseMode="ExecuteURL" prefixLanguageFilePath="" />
      <remove statusCode="500" />
      <error statusCode="500" path="/ErrorPage/DefaultError.aspx" responseMode="ExecuteURL" prefixLanguageFilePath="" />
      <remove statusCode="503" />
      <error statusCode="503" path="/ErrorPage/DefaultError.aspx" responseMode="ExecuteURL" prefixLanguageFilePath="" />
    </httpErrors>-->
    <httpProtocol>
      <customHeaders>
        <remove name="X-Powered-By"/>
      </customHeaders>
    </httpProtocol>
    <security>
      <requestFiltering removeServerHeader="true"/>
    </security>
    <!--<modules runAllManagedModulesForAllRequests="true" />-->
    <validation validateIntegratedModeConfiguration="false"/>
    <handlers>
      <add name="ReportViewerWebControlHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91"/>
    </handlers>
    <!--<security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="52428800" />
      </requestFiltering>
    </security>-->
    <!--<applicationInitialization remapManagedRequestsTo="/default.aspx">
      <add initializationPage="/default.aspx" />
    </applicationInitialization>-->
    <applicationInitialization>
      <!--<add initializationPage="/" hostName="https://gt-uat.azurefd.net/"/>-->
      <!--<add initializationPage="/" hostName="https://gt.reboundeu.com/"/>-->
    </applicationInitialization>
    <!-- Disable the Code of AI //RP-845 -->
    <!--<modules>
      <remove name="TelemetryCorrelationHttpModule"/>
      <add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation" preCondition="managedHandler"/>
      <remove name="ApplicationInsightsWebTracking"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler"/>
    </modules>-->
  </system.webServer>
  <location path="PrintReport.aspx">
    <system.web>
      <httpRuntime executionTimeout="6000"/>
    </system.web>
  </location>
  <location path="FileUpload.aspx">
    <system.web>
      <!-- maxRequestLength needs to be set quite high because it errors appallingly. Better that we control it in the app and we can error gracefully -->
      <httpRuntime maxRequestLength="5000000" executionTimeout="6000"/>
    </system.web>
  </location>
  <location path="DocImage.ashx">
    <system.web>
      <!--<httpRuntime maxRequestLength="20000" executionTimeout="6000" />-->
      <httpRuntime maxRequestLength="102400" executionTimeout="60000"/>
    </system.web>
  </location>
  <location path="UtilityBOMManagerImport.ashx">
    <system.web>
      <httpRuntime executionTimeout="6000"/>
    </system.web>
  </location>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.IdentityModel.Tokens.Jwt" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Logging" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Tokens" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="itextsharp" publicKeyToken="8354ae6d2174ddca" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.3.0" newVersion="3.0.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.4.1" newVersion="4.0.4.1"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-3.0.0.0" newVersion="3.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-5.2.9.0" newVersion="5.2.9.0"/>
      </dependentAssembly>
      <!--Added by Rahil-->
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Razor" culture="neutral" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.0.0" newVersion="3.0.0.0"/>
      </dependentAssembly>
      <!--Added by Rahil-->
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.3" newVersion="7.0.0.3"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Web.Infrastructure" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Azure.Storage.Common" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-11.2.3.0" newVersion="11.2.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Azure.KeyVault.Core" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.5.0" newVersion="3.0.5.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Protocols" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Protocols.OpenIdConnect" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-7.0.0.0" newVersion="7.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
    <!--Added by Rahil-->
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages.Razor" culture="neutral" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-3.0.0.0" newVersion="3.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
    <!--Added by Rahil-->
  </runtime>
</configuration>