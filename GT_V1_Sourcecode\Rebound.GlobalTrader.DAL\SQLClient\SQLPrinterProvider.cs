﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlPrinterProvider : PrinterProvider {
				
		
        /// <summary>
        /// DropDownForPrinter 
        /// Calls [usp_dropdown_PrinterAll]
        /// </summary>
        public override List<PrinterDetails> DropDownForPrinter(System.Int32? clientId)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_PrinterAll", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
				while (reader.Read()) {
                    PrinterDetails obj = new PrinterDetails();
                    obj.PrinterId = GetReaderValue_Int32(reader, "PrinterId", 0);
                    obj.PrinterName = GetReaderValue_String(reader, "PrinterName", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Printers", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
        /// <summary>
        /// Calls [usp_insert_Printer]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override int Insert(System.Int32? clientNo, System.String printerName, System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_Printer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@PrinterName", SqlDbType.NVarChar).Value = printerName;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = description;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@PrinterId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@PrinterId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Printer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Printer
        /// Calls [usp_update_Printer]
        /// </summary>
        public override bool Update(System.Int32? printerId, System.Int32? clientNo, System.String printerName, System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Printer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@PrinterId", SqlDbType.Int).Value = printerId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@PrinterName", SqlDbType.NVarChar).Value = printerName;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = description;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Printer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Call [usp_selectAll_Printer_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<PrinterDetails> GetListForClient(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Printer_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
                while (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.PrinterId = GetReaderValue_Int32(reader, "PrinterId", 0);
                    obj.PrinterName = GetReaderValue_String(reader, "PrinterName", "");
                    obj.PrinterDescription = GetReaderValue_String(reader, "PrinterDescription", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Printers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// GetListLabelSetup
        /// Calls [usp_selectAll_LabelSetup]
        /// </summary>
        public override List<PrinterDetails> GetListLabelSetup()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_LabelSetup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
                while (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.ListSetpId = GetReaderValue_Int32(reader, "ListSetpId", 0);
                    obj.ListSetupName = GetReaderValue_String(reader, "ListSetupName", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Label setup", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Pass the primary key of the table tbListSetup
        /// Calls [usp_selectAll_LabelSetupItem]
        /// </summary>
        public override List<PrinterDetails> GetListLabelSetupItem(System.Int32? labelSetupId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_LabelSetupItem", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ListSetpId", SqlDbType.Int).Value = labelSetupId;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
                while (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.LabelSetupItemId = GetReaderValue_Int32(reader, "ID", 0);
                    obj.LabelSetupItemName = GetReaderValue_String(reader, "Value", "");
                    obj.LabelSetupOther = GetReaderValue_String(reader, "Other", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Label setup", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Insert
        /// Calls [usp_insert_LabelSetupItem]
        /// </summary>
        public override int InsertLabelSetupItem(System.Int32? labelSetupId, System.String name, System.String email, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_LabelSetupItem", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@labelSetupId", SqlDbType.Int).Value = labelSetupId;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
                cmd.Parameters.Add("@email", SqlDbType.NVarChar).Value = email;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@LabelSetupItemId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@LabelSetupItemId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Label setup item", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update
        /// Calls [usp_update_LabelSetupItem]
        /// </summary>
        public override bool UpdateLabelSetupItem(System.Int32? labelsetupItemId, System.Int32? labelSetupId, System.String name, System.String email, System.Int32? updatedBy, System.Boolean? inactive)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_LabelSetupItem", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@labelsetupItemId", SqlDbType.Int).Value = labelsetupItemId;
                cmd.Parameters.Add("@labelSetupId", SqlDbType.Int).Value = labelSetupId;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
                cmd.Parameters.Add("@Email", SqlDbType.NVarChar).Value = email;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Label setup", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //Restricted MFR
        /// <summary>
        /// Insert Restricted Manufacturer
        /// Calls [usp_insert_RestrictedManufacture]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="manufactureNo"></param>
        /// <param name="notes"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override int InsertMfr(System.Int32? clientNo, System.Int32? manufactureNo, System.String notes, System.Boolean? inActive, System.Int32? updatedBy, System.Int32? ManufacturerCount, System.String ManufactureName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_RestrictedManufacture", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufactureNo;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ManufacturerCount", SqlDbType.Int).Value = ManufacturerCount;
                cmd.Parameters.Add("@ManufactureName", SqlDbType.NVarChar).Value = ManufactureName;
                cmd.Parameters.Add("@RestrictedManufacturerId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RestrictedManufacturerId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Restricted Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override System.String InsertMultipleManufacturers(System.Int32? clientNo, System.String manufactureNos, System.String mfrNameSuffix, System.String notes, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_MultipleRestrictedManufacturers", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ManufacturerNos", SqlDbType.NVarChar).Value = manufactureNos;
                cmd.Parameters.Add("@MFRNameSuffix", SqlDbType.NVarChar).Value = mfrNameSuffix;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RestrictedManufacturerNames", SqlDbType.NVarChar, 4000).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (String)cmd.Parameters["@RestrictedManufacturerNames"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Restricted Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Restricted Manufacturer
        /// Calls [usp_update_RestrictedManufacturer]
        /// </summary>
        /// <param name="RestrictedManufacturerId"></param>
        /// <param name="clientNo"></param>
        /// <param name="manufactureNo"></param>
        /// <param name="notes"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override bool UpdateMfr(System.Int32? RestrictedManufacturerId, System.Int32? clientNo, System.Int32? manufactureNo, System.String notes, System.Boolean? inActive, System.Int32? updatedBy, System.String manufactureName, System.Int32 manufactureCount)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_RestrictedManufacturer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@RestrictedManufacturerId", SqlDbType.Int).Value = RestrictedManufacturerId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufactureNo;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ManufactureName", SqlDbType.NVarChar).Value = manufactureName;
                cmd.Parameters.Add("@ManufactureCount", SqlDbType.Int).Value = manufactureCount;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Restricted Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool UpdateMultipleMfr(System.Int32? clientNo, System.String manufacturerNos, System.String mfrNameSuffix, System.String notes, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_MultipleRestrictedManufacturer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ManufacturerNos", SqlDbType.NVarChar).Value = manufacturerNos;
                cmd.Parameters.Add("@MFRNameSuffix", SqlDbType.NVarChar).Value = mfrNameSuffix;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Restricted Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool ActivateMultipleMfr(System.Int32? clientNo, System.String manufacturerNos, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ActivateMultipleRestrictedManufacturer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ManufacturerNos", SqlDbType.NVarChar).Value = manufacturerNos;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Restricted Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Call [usp_selectAll_Printer_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<PrinterDetails> GetListForRestrictedManufacturer(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_RestrictedManufacturer_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
                while (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.RestrictedManufacturerId = GetReaderValue_Int32(reader, "RestrictedManufacturerId", 0);
                    obj.ManufactureNo = GetReaderValue_Int32(reader, "ManufacturerNo", 0);
                    obj.ManufactureName = GetReaderValue_String(reader, "ManufactureName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ManufacturerCount = GetReaderValue_Int32(reader, "ManufacturerCount", 0);
                    obj.MFRNameSuffix = GetReaderValue_String(reader, "ManufacturerNameSuffix", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Restricted Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<PrinterDetails> GetItemsByMfrIds(System.Int32? clientId, System.String manufacturerNos)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_RestrictedManufacturerByMfrIds", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ManufacturerNos", SqlDbType.NVarChar).Value = manufacturerNos;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
                while (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.ManufactureName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ManufacturerCount = GetReaderValue_Int32(reader, "ManufacturerCount", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Restricted Manufacturer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
		/// Calls [usp_Select_RestrictedManufacture]
        /// </summary>
		public override PrinterDetails Get(System.Int32? clientNo, System.Int32? manufactureNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Select_RestrictedManufacture", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufactureNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.ManufacturerCount = GetReaderValue_Int32(reader, "ManufacturerCount", 0);
                    obj.ManufactureName = GetReaderValue_String(reader, "ManufactureName", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Restricted Manufacturer Count", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// GetRestrictedManufacture 
        /// Calls [usp_Select_Search_for_RestrictedManufacture]
        /// </summary>
        public override PrinterDetails GetRestrictedManufacture(System.Int32? clientNo, System.Int32? manufactureNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Select_Search_for_RestrictedManufacture", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufactureNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.isRestrictedManufacturer = GetReaderValue_Int32(reader, "isRestrictedManufacturer", 0);
                    obj.RestrictedMFRMessage = GetReaderValue_String(reader, "RestrictedMFRMessage", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Restricted Manufacturer Count", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Call [usp_selectAll_ECCN_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<PrinterDetails> GetListForECCNCode(System.Int32? clientId,System.Boolean NotifyStatus,System.String SearchType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_ECCN_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ClientNotify", SqlDbType.Bit).Value = NotifyStatus;
                cmd.Parameters.Add("@SearchType", SqlDbType.NVarChar).Value = SearchType;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
                while (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.ECCNId = GetReaderValue_Int32(reader, "ECCNId", 0);
                    obj.ECCNCode = GetReaderValue_String(reader, "eccncode", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ECCNStatus = GetReaderValue_Boolean(reader, "eccnstatus", false);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ECCNNotify = GetReaderValue_Boolean(reader, "ECCNNotify", false);
                    obj.EccnSubject = GetReaderValue_String(reader, "EccnSubject", "");
                    obj.EccnMessage = GetReaderValue_String(reader, "EccnMessage", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Eccn Record", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// Call [usp_selectAll_ECCN_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<PrinterDetails> GetListForECCNCodeMap(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_ECCN_for_EditMap", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
                while (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.ECCNId = GetReaderValue_Int32(reader, "ECCNId", 0);
                    obj.ECCNCode = GetReaderValue_String(reader, "eccncode", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ECCNStatus = GetReaderValue_Boolean(reader, "eccnstatus", false);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ECCNNotify = GetReaderValue_Boolean(reader, "ECCNNotify", false);
                    obj.EccnSubject = GetReaderValue_String(reader, "EccnSubject", "");
                    obj.EccnMessage = GetReaderValue_String(reader, "EccnMessage", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Eccn Record", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// [usp_insert_ECCN]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="EccnCode"></param>
        /// <param name="Notes"></param>
        /// <param name="EccnStatus"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override int InsertEccn(System.Int32? clientNo, System.String ECCNCode, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ECCN", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@ECCNStatus", SqlDbType.Bit).Value = ECCNStatus;
                cmd.Parameters.Add("@ECCNNotify", SqlDbType.Bit).Value = ECCNNotify;
                cmd.Parameters.Add("@EccnSubject", SqlDbType.NVarChar).Value = EccnSubject;
                cmd.Parameters.Add("@EccnMessage", SqlDbType.NVarChar).Value = EccnMessage;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ECCNId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ECCNId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert ECCN ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// [usp_insert_ECCN]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="EccnCode"></param>
        /// <param name="Notes"></param>
        /// <param name="EccnStatus"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override int InsertEccnClone(System.Int32? ECCNCloneId,System.Int32? clientNo, System.String ECCNCode, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ECCNClone", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ECCNParentID", SqlDbType.Int).Value = ECCNCloneId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@ECCNStatus", SqlDbType.Bit).Value = ECCNStatus;
                cmd.Parameters.Add("@ECCNNotify", SqlDbType.Bit).Value = ECCNNotify;
                cmd.Parameters.Add("@EccnSubject", SqlDbType.NVarChar).Value = EccnSubject;
                cmd.Parameters.Add("@EccnMessage", SqlDbType.NVarChar).Value = EccnMessage;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ECCNId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ECCNId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert ECCN Clone ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Calls [usp_update_ECCN]
        /// </summary>
        /// <param name="ECCNId"></param>
        /// <param name="clientNo"></param>
        /// <param name="Notes"></param>
        /// <param name="EccnStatus"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override bool UpdateEccn(System.Int32? ECCNId, System.Int32? clientNo, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
        
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ECCN", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ECCNId", SqlDbType.Int).Value = ECCNId;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@ECCNStatus", SqlDbType.Bit).Value = ECCNStatus;
                cmd.Parameters.Add("@ECCNNotify", SqlDbType.Bit).Value = ECCNNotify;
                cmd.Parameters.Add("@EccnSubject", SqlDbType.NVarChar).Value = EccnSubject;
                cmd.Parameters.Add("@EccnMessage", SqlDbType.NVarChar).Value = EccnMessage;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update ECCN", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool UpdateMapEccn(System.Int32? clientNo, System.String SelectedEccnIds, System.String EccnWarningMessage, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_MapECCNWarning", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SelectedEccnIds", SqlDbType.NVarChar).Value = SelectedEccnIds;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = EccnWarningMessage;
                cmd.Parameters.Add("@ECCNStatus", SqlDbType.Bit).Value = ECCNStatus;
                cmd.Parameters.Add("@ECCNNotify", SqlDbType.Bit).Value = ECCNNotify;
                cmd.Parameters.Add("@EccnSubject", SqlDbType.NVarChar).Value = EccnSubject;
                cmd.Parameters.Add("@EccnMessage", SqlDbType.NVarChar).Value = EccnMessage;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Mapped  ECCN worning", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Calls [usp_insert_Printer]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override int InsertPVV(System.Int32? clientNo,  System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_PVVQestion", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = description;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@PVVQuestionId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@PVVQuestionId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert PVV Question", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Printer
        /// Calls [usp_update_Printer]
        /// </summary>
        public override bool UpdatePVV(System.Int32? printerId, System.Int32? clientNo,  System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_PVVQestion", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@PVVQuestionId", SqlDbType.Int).Value = printerId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = description;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = inActive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update PVV Question", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Call [usp_selectAll_PVVQuestion_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<PrinterDetails> GetListForPVV(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_PVVQuestion_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PrinterDetails> lst = new List<PrinterDetails>();
                while (reader.Read())
                {
                    PrinterDetails obj = new PrinterDetails();
                    obj.PrinterId = GetReaderValue_Int32(reader, "PVVQuestionId", 0);
                    obj.PrinterDescription = GetReaderValue_String(reader, "PVVQuestionName", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.EmployeeName= GetReaderValue_String(reader, "EmployeeName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Printers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

    }
}