﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_update_CustomerRequirement]    Script Date: 11/7/2024 8:14:22 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_update_CustomerRequirement]-- @CustomerRequirementId=2171635,                                            
--@part=234, @BOMNo=325,@Quantity=22,@Price=234,@CurrencyNo=254, @ReceivedDate='2016-04-25 15:31:20.507',@Salesman=2439,                                             
--@DatePromised='2016-04-25 00:00:00.000',@Shortage=0,@CompanyNo=206689,@ContactNo=293005,@Alternate=0,@Closed=0                                            
--************************************************************************************                                              
--* SK 20.01.2010:                                              
--* - allow for new columns - PartWatch, BOM, BOMName                                              
--*                                               
--* SK 29.10.2009:                                              
--* - allow for new column - FullCustomerPart - used for searching      
    
-- Marker   Owner   Date   Remarks    
--[003]       Ravi   31-08-2023  RP-2227 AS6081    
--************************************************************************************                                              
      @CustomerRequirementId int                                              
    , @Part nvarchar(40)                                              
    , @ManufacturerNo int = NULL                                              
    , @DateCode nvarchar(5) = NULL                                              
    , @PackageNo int = NULL                                              
    , @Quantity int                                              
    , @Price float                                              
    , @CurrencyNo int                                              
    , @ReceivedDate datetime                                              
    , @Salesman int                                              
    , @DatePromised datetime                                              
    , @Notes nvarchar(max) = NULL                                              
    , @Instructions nvarchar(max) = NULL                                              
    , @Shortage bit                                              
    , @CompanyNo int                                              
    , @ContactNo int                                              
    , @UsageNo int = NULL                                              
    , @Alternate bit                                              
    , @OriginalCustomerRequirementNo int = NULL                                              
    , @ReasonNo int = NULL                                              
    , @ProductNo int = NULL                                              
    , @CustomerPart nvarchar(30) = NULL                                              
    , @Closed bit                                              
    , @ROHS tinyint  =NULL                                            
    , @UpdatedBy int = NULL                                              
 , @PartWatch bit = Null                                                
 , @BOM bit = Null                                                
 , @BOMName nvarchar(128) = Null                                                
    ,@BOMNo int =Null                                           
    ,@FactorySealed bit=Null                                          
    ,@MSL nvarchar(50) = Null                                     
                                        
    ,@PartialQuantityAcceptable bit=Null,                                        
@Obsolete bit =Null,                                        
@LastTimeBuy bit=Null,                                        
@RefirbsAcceptable bit=Null,                                        
@TestingRequired bit =Null,                                        
@TargetSellPrice float=Null,                                        
@CompetitorBestOffer float=Null,                
@CustomerDecisionDate datetime=Null,                                        
@RFQClosingDate datetime=Null,                                        
@QuoteValidityRequired int=Null,                        
@Type int=Null,                                        
@OrderToPlace int =Null,                                        
@RequirementForTraceability int=Null ,                                  
@EAU   nvarchar(50) = Null,                                     
                               
@AlternativesAccepted bit =NULL,                                        
@RepeatBusiness bit=NULL,                                    
@SupportTeamMemberNo   int =null,                              
 --add parameter for ihs code start                                             
@CountryOfOrigin   NVARCHAR(100) = NULL,                                               
@CountryOfOriginNo int=0 ,                                              
@LifeCycleStage    NVARCHAR(100) = NULL,                                               
@HTSCode           VARCHAR(20) = NULL,                                              
@AveragePrice      FLOAT=0,                                              
@Packing           VARCHAR(60) = NULL,                                              
@PackagingSize     NVARCHAR(100) = NULL,                                
@Descriptions      NVARCHAR(max) = NULL,                                          
@IHSPartsNo        INT  =null  ,                                        
@IHSCurrencyCode   NVARCHAR(100) = NULL,                               
@IHSProduct        NVARCHAR(100) = NULL,                               
@ECCNCode          NVARCHAR(30) = NULL,                  
@ECCNNo int = null,                        
@AS6081 bit = NULL, --[003]    
 --add paramerter for ihs code end                            
@RowsAffected int = NULL OUTPUT                                              
                                              
AS                                               
                                              
  --IF NOT EXISTS(select BOMNo from dbo.tbcustomerrequirement where CustomerRequirementId = @CustomerRequirementId )                                            
  BEGIN                                            
                                              
  DECLARE @CustomerRequirementNumber int =NULL;                                         
  DECLARE @CurPrice float                                      
  DECLARE @ClientNo  int                      
  DECLARE @PartEditStatus  int=0              
  declare @CHKEccnCode nvarchar(50)=null                
              
   declare @SectionName varchar(50) = 'CustomerRequirementECCN'                  
    declare @SubSectionName varchar(50) = 'ECCN'                  
    declare @ActionName   varchar(10) = 'Print'                  
    declare @DocumentNo     int     = @CustomerRequirementId                  
    declare @Detail     nvarchar(max) =null                 
    declare @PrintDocumentLogId  int =NULL                                    
            
  select @CHKEccnCode=ECCNCode from tbCustomerRequirement                       
  where CustomerRequirementId=@CustomerRequirementId and ECCNCode is not null            
            
                                      
  SELECT  @CustomerRequirementNumber=CustomerRequirementNumber,@CurPrice = Price , @ClientNo = ClientNo                                      
  FROM tbCustomerRequirement  where CustomerRequirementId=@CustomerRequirementId                       
  --PartEditStatus check                      
  select @PartEditStatus=count(CustomerRequirementId) from tbCustomerRequirement                       
  where CustomerRequirementId=@CustomerRequirementId and REQStatus=1 and BOMNo is null and REQStatus!=5                         
  --when part status allow edit      
      
  if(@ECCNCode = '[Blank]')      
  begin     
 set @ECCNCode = null      
 set @ECCNNo = null      
  end      
      
  -------------------------------------ECCN Log Entery End----------------------------------------------------              
   if(@ECCNNo is not null and @ECCNCode is not null)                
   begin              
  if(@CHKEccnCode!= @ECCNCode)              
  begin              
 set @Detail   = 'Action¦¦' +' ECCN CODE CHANGED FROM  ( ' + @CHKEccnCode + ' )'               
 EXEC [dbo].[usp_insert_PrintEmailLog]                       
   @SectionName                     
       , @SubSectionName                    
       , @ActionName                       
       , @DocumentNo                       
       , @Detail                
       , @UpdatedBy                      
       , @PrintDocumentLogId                   
  end              
  --else              
  --if(@CHKEccnCode is not null)              
  --begin              
  --set @Detail  = 'Action¦¦' +' ECCN Code changed from  ( ' + @ECCNCode + ' )'               
  --EXEC [dbo].[usp_insert_PrintEmailLog]                       
-- @SectionName                     
  --     , @SubSectionName                    
  --     , @ActionName                       
  --     , @DocumentNo                       
  --     , @Detail                         
  --     , @UpdatedBy                      
  --     , @PrintDocumentLogId                   
  -- -----------------------------------------------------------------------                    
  --end              
  else              
  if(@CHKEccnCode is  null)              
  begin              
  set @Detail  = 'Action¦¦' +' ECCN CODE ADDED WITH  ( ' + @ECCNCode + ' )'               
  EXEC [dbo].[usp_insert_PrintEmailLog]                       
   @SectionName                     
       , @SubSectionName                    
       , @ActionName                       
       , @DocumentNo                  , @Detail                         
       , @UpdatedBy                      
       , @PrintDocumentLogId                   
   -----------------------------------------------------------------------                    
  end              
               
  end            
    if(@ECCNCode is  null)             
 begin               
  if(@CHKEccnCode is not null)                
  begin                
  set @Detail  = 'Action¦¦' +' ECCN CODE CHANGED FROM ( ' + @CHKEccnCode + ' )'                 
  EXEC [dbo].[usp_insert_PrintEmailLog]                         
   @SectionName                       
       , @SubSectionName                      
       , @ActionName                         
       , @DocumentNo                         
       , @Detail                           
       , @UpdatedBy                        
       , @PrintDocumentLogId                     
   -----------------------------------------------------------------------                      
  end              
  end            
                
-------------------------------------ECCN Log Entery End----------------------------------------------------            
            
                      
  if(@PartEditStatus=1 and @IHSPartsNo is not null)                      
  begin                      
  UPDATE  dbo.tbCustomerRequirement                                              
 SET     FullPart = dbo.ufn_get_fullpart(@Part)                                              
  , Part = @Part                                              
  , ManufacturerNo = @ManufacturerNo                                              
  , DateCode = @DateCode                                              
  , PackageNo = @PackageNo                                              
  , Quantity = @Quantity                                              
  , Price = @Price                                              
  , CurrencyNo = @CurrencyNo                                              
  , ReceivedDate = @ReceivedDate                                
  , Salesman = @Salesman                                              
  , DatePromised = @DatePromised                                              
  , Notes = @Notes                                              
  , Instructions = @Instructions                                              
  , Shortage = @Shortage                        
  , CompanyNo = @CompanyNo                                              
  , ContactNo = @ContactNo                                              
  , UsageNo = @UsageNo                                              
  , Alternate = @Alternate                                              
  , OriginalCustomerRequirementNo = @OriginalCustomerRequirementNo                                             
  , ReasonNo = @ReasonNo                                              
  , ProductNo = @ProductNo                                              
  , CustomerPart = @CustomerPart                                              
  , Closed = @Closed                                              
  , ROHS = @ROHS                                              
  , UpdatedBy = @UpdatedBy              , DLUP = CURRENT_TIMESTAMP                                              
  , FullCustomerPart = dbo.ufn_get_fullpart(@CustomerPart)                                              
  , PartWatch = @PartWatch                                              
  , BOM = @BOM                                              
  , BOMName = @BOMName                                             
  ,BOMNo=@BOMNo                                           
  ,FactorySealed=@FactorySealed                        
   ,MSL=@MSL                                     
   ,PartialQuantityAcceptable=@PartialQuantityAcceptable                                        
 ,Obsolete  =@Obsolete                                      
 ,LastTimeBuy  =@LastTimeBuy                   
 ,RefirbsAcceptable =@RefirbsAcceptable                                       
 ,TestingRequired  =@TestingRequired                                      
 ,TargetSellPrice   =@TargetSellPrice                                     
 ,CompetitorBestOffer  =@CompetitorBestOffer                                      
 ,CustomerDecisionDate  =@CustomerDecisionDate                                      
 ,RFQClosingDate    =@RFQClosingDate                                    
 ,QuoteValidityRequired  =@QuoteValidityRequired                                      
 ,ReqType    =@Type                                    
 ,OrderToPlace    =@OrderToPlace                                    
 ,ReqForTraceability  =@RequirementForTraceability                                       
 ,EAU =@EAU                                   
 ,AlternativesAccepted=@AlternativesAccepted                                  
 ,RepeatBusiness=@RepeatBusiness                                  
 ,SupportTeamMemberNo=@SupportTeamMemberNo                                
  --ihs code start   when part status allow edit                                           
,CountryOfOriginNo =@CountryOfOriginNo                                      
,LifeCycleStage    =@LifeCycleStage                                     
,HTSCode           =@HTSCode                                     
,AveragePrice      =@AveragePrice                                     
,Packing           =@Packing                                     
,PackagingSize     =@PackagingSize                                
,Descriptions     =@Descriptions                            
,IHSPartsNo        =@IHSPartsNo                              
,ihsCurrencyCode  =@IHSCurrencyCode                         
,IHSProduct       =@IHSProduct                              
,ECCNCode         =@ECCNCode                                
 --ihs code end      
, AS6081= @AS6081 --[003]    
  WHERE   CustomerRequirementId = @CustomerRequirementId                      
  end                 
  else                 
  begin                                        
 UPDATE  dbo.tbCustomerRequirement                                              
 SET     FullPart = dbo.ufn_get_fullpart(@Part)                                              
  , Part = @Part                                              
  , ManufacturerNo = @ManufacturerNo                                              
  , DateCode = @DateCode       
  , PackageNo = @PackageNo                             
  , Quantity = @Quantity                                              
  , Price = @Price                                              
  , CurrencyNo = @CurrencyNo                                              
  , ReceivedDate = @ReceivedDate                                              
  , Salesman = @Salesman                                              
  , DatePromised = @DatePromised                              
  , Notes = @Notes                                              
  , Instructions = @Instructions                                              
  , Shortage = @Shortage                                              
  , CompanyNo = @CompanyNo                                              
  , ContactNo = @ContactNo                                              
  , UsageNo = @UsageNo                                              
  , Alternate = @Alternate                                              
  , OriginalCustomerRequirementNo = @OriginalCustomerRequirementNo                                              
  , ReasonNo = @ReasonNo                                              
  , ProductNo = @ProductNo                                              
  , CustomerPart = @CustomerPart                                              
  , Closed = @Closed                                              
  , ROHS = @ROHS                                              
  , UpdatedBy = @UpdatedBy              , DLUP = CURRENT_TIMESTAMP                                              
  , FullCustomerPart = dbo.ufn_get_fullpart(@CustomerPart)                   
  , PartWatch = @PartWatch                                              
  , BOM = @BOM                                              
  , BOMName = @BOMName                                             
  ,BOMNo=@BOMNo                                           
  ,FactorySealed=@FactorySealed                                           
   ,MSL=@MSL                                     
   ,PartialQuantityAcceptable=@PartialQuantityAcceptable                                        
 ,Obsolete  =@Obsolete                                      
 ,LastTimeBuy  =@LastTimeBuy                                      
 ,RefirbsAcceptable =@RefirbsAcceptable                                       
 ,TestingRequired  =@TestingRequired            
 ,TargetSellPrice   =@TargetSellPrice                                     
 ,CompetitorBestOffer  =@CompetitorBestOffer                                      
 ,CustomerDecisionDate  =@CustomerDecisionDate                                      
 ,RFQClosingDate    =@RFQClosingDate                                    
 ,QuoteValidityRequired  =@QuoteValidityRequired                                      
 ,ReqType    =@Type                                    
 ,OrderToPlace    =@OrderToPlace                                    
 ,ReqForTraceability  =@RequirementForTraceability                                       
 ,EAU =@EAU                                  
 ,AlternativesAccepted=@AlternativesAccepted                                  
 ,RepeatBusiness=@RepeatBusiness                                  
 ,SupportTeamMemberNo=@SupportTeamMemberNo                                
 ,ECCNCode         =@ECCNCode      
 , AS6081= @AS6081 --[003]    
  WHERE   CustomerRequirementId = @CustomerRequirementId                                             
  end                      
   
  if(@BOMNo>0)                                            
  BEGIN                                      
  IF NOT EXISTS(select count(1) from dbo.tbBOM where BOMId=@BOMNo and status=3)                                    
 begin                                            
  UPDATE  dbo.tbBOM                                            
  SET [Status] =2 where BOMId=@BOMNo                                            
   end                                     
                                           
  Update dbo.tbCustomerRequirement                                            
set BOMNo=@BOMNo                                             
  WHERE ClientNo = @ClientNo and   CustomerRequirementNumber = @CustomerRequirementNumber                                            
  AND Alternate = 1                                       
                                      
  --Update price, if price updated                                      
   IF @CurPrice <> @Price                                      
   BEGIN                                      
                                      
  DECLARE @PHCurrencyNo INT                                              
  DECLARE  @TotalValue float                                            
   SELECT TOP 1 @PHCurrencyNo = POCurrencyNo                                                
   FROM tbCompany where ClientNo = @ClientNo and isnull(IsPOHub,0) = 1                                        
                                          
       UPDATE tbCustomerRequirement SET PHPrice = isnull(isnull(price,0)/ dbo.ufn_get_exchange_rate(CurrencyNo, getdate()),0)*dbo.ufn_get_exchange_rate(@PHCurrencyNo, getdate())                                              
   WHERE BOMNo = @BOMNo                                              
          
  SELECT @TotalValue=SUM(Quantity * Isnull(PHPrice,0))  from tbCustomerRequirement where BOMNo = @BOMNo                                        
  UPDATE tbBOM set TotalBomLinePrice=@TotalValue where BOMId=  @BOMNo                                        
   END                                      
                                      
  END                                            
                        
--for Eccn code insert cammand                  
if(@ECCNNo is not null)                      
begin                      
declare @checkdublicate int=0                           
set @checkdublicate=(select count(*) from tbPartEccnMapped where  Part=@Part )                                       
if(@checkdublicate=0)                   
begin                                
INSERT INTO dbo.tbPartEccnMapped                                                    
 ( Part                                                    
 , ECCNNo                                                    
 , ECCNCode                          
 , ClientNo                                              
 , UpdatedBy                                                    
                                                  
 )                                                    
VALUES                                                    
 ( @Part                                                    
 , @ECCNNo                                                    
 , @ECCNCode                                                    
 , @ClientNo                                                    
 , @UpdatedBy                             
)                                    
end             
                
end                  
--code end                                   
                                      
 SELECT  @RowsAffected = @@ROWCOUNT                                            
                                             
END 
GO
