﻿using System.Collections.Generic;

namespace Rebound.GlobalTrader.DAL
{
    public class HubSourcingResultImportTempDetails
    {
        public List<SourcingResultImportTempDetails> RawDataList { get; set; } = new List<SourcingResultImportTempDetails>();
        public int TotalRecords { get; set; }
        public int TotalRecordsError { get; set; }
        public int CurrentPage { get; set; }
        public List<SRImportTempIncorrectDetails> IncorrectDataList { get; set; } = new List<SRImportTempIncorrectDetails>();
    }
    public partial class SourcingResultImportTempDetails
    {
        public int BomImportSourcingId { get; set; }
        public string Requirement { get; set; }
        public string RequirementMessage { get; set; }
        public string CustomerRefNo { get; set; }
        public string CustomerRefNoMessage { get; set; }
        public string Supplier { get; set; }
        public string SupplierMessage { get; set; }
        public string SupplierPart { get; set; }
        public string SupplierPartMessage { get; set; }
        public string SupplierCost { get; set; }
        public string SupplierCostMessage { get; set; }
        public string ROHS { get; set; }
        public string ROHSMessage { get; set; }
        public string MFR { get; set; }
        public string MFRMessage { get; set; }
        public string DateCode { get; set; }
        public string DateCodeMessage { get; set; }
        public string Package { get; set; }
        public string PackageMessage { get; set; }
        public string OfferedQuantity { get; set; }
        public string OfferedQuantityMessage { get; set; }
        public string OfferStatus { get; set; }
        public string OfferStatusMessage { get; set; }
        public string SPQ { get; set; }
        public string SPQMessage { get; set; }
        public string FactorySealed { get; set; }
        public string FactorySealedMessage { get; set; }
        public string QtyInStock { get; set; }
        public string QtyInStockMessage { get; set; }
        public string MOQ { get; set; }
        public string MOQMessage { get; set; }
        public string LastTimeBuy { get; set; }
        public string LastTimeBuyMessage { get; set; }
        public string Currency { get; set; }
        public string CurrencyMessage { get; set; }
        public string BuyPrice { get; set; }
        public string BuyPriceMessage { get; set; }
        public string SellPrice { get; set; }
        public string SellPriceMessage { get; set; }
        public string ShippingCost { get; set; }
        public string ShippingCostMessage { get; set; }
        public string LeadTime { get; set; }
        public string LeadTimeMessage { get; set; }
        public string Region { get; set; }
        public string RegionMessage { get; set; }
        public string DeliveryDate { get; set; }
        public string DeliveryDateMessage { get; set; }
        public string Notes { get; set; }
        public string NotesMessage { get; set; }
        public string MSL { get; set; }
        public string MSLMessage { get; set; }
    }
    public class SRImportTempIncorrectDetails
    {
        public int ID { get; set; }
        public string Value { get; set; }
        public string Type { get; set; }
        public int Count { get; set; }
    }
}
