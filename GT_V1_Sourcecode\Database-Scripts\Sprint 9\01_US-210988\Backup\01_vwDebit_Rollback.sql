﻿
GO

CREATE OR ALTER VIEW [dbo].[vwDebit]                          
AS                            
SELECT                     
a.<PERSON>                    
,a.<PERSON>                    
,a.<PERSON>                    
,a.<PERSON>                
,a.<PERSON>                    
,a.<PERSON>                    
,a.<PERSON><PERSON>                    
,a.<PERSON>                    
,a.<PERSON>                    
,a.<PERSON>                    
,a.Instruction<PERSON>                    
,a.<PERSON>                    
,a.<PERSON>                    
,a.<PERSON>                    
,a.<PERSON>ur<PERSON>rderNo                    
,a.<PERSON>lierR<PERSON>                    
,a.Reference<PERSON>ate                    
,a.SupplierInvoice                    
,a.SupplierRMA                    
,a.SupplierCredit                    
,a.Updated<PERSON>y                    
,a.DLUP                    
,a.<PERSON>                    
,a.<PERSON>it<PERSON>o                          
,b.<PERSON>ame                  
,  c.<PERSON>                          
,  j.<PERSON>                          
,  j.<PERSON>                          
,  d.Employee<PERSON>ame           AS RaiserName                          
,  e.EmployeeName           AS BuyerName                          
,  e.TeamNo                          
,  f.Division<PERSON>ame                          
,  k.Tax<PERSON>ame                          
,  g.PurchaseOrderNumber                          
,  g.DateOrdered           AS PurchaseOrderDate                          
,  h.SupplierRMANumber                          
,  ( SELECT SUM(ISNULL((z.Price * z.Quantity), 0))                          
   FROM   dbo.tbDebitLine z                          
   WHERE  z.DebitNo = a.DebitId                          
  )              AS DebitValue                          
,  dbo.ufn_get_taxrate(a.TaxNo, a.ClientNo, a.DebitDate) AS TaxRate                 
, ipo.InternalPurchaseOrderId AS InternalPurchaseOrderNo                  
, ipo.InternalPurchaseOrderNumber                
, ipo.CompanyNo AS IPOCompanyNo              
, cop.CompanyName AS IPOCompanyName             
,a.ishublocked               
, ipo.LinkMultiCurrencyNo            
,a.HeaderImageName                
-----------        
--,cr.FooterText        
, FHstry.FooterText AS FooterText        
-----------------        
,a.SysDocHazardousHistoryNo    
,a.DateExported    
,a.Exported      
,a.URNNumber  
,a.CanBeExported  
      
FROM    dbo.tbDebit a                
JOIN    dbo.tbCompany b   ON a.CompanyNo   = b.CompanyId                          
JOIN    dbo.tbContact c  ON a.ContactNo   = c.ContactId                          
JOIN    dbo.tbLogin d    ON a.RaisedBy   = d.LoginId                          
JOIN    dbo.tbLogin e    ON a.Buyer    = e.LoginId                          
JOIN    dbo.tbDivision f    ON a.DivisionNo  = f.DivisionId                          
JOIN    dbo.tbPurchaseOrder g   ON a.PurchaseOrderNo = g.PurchaseOrderId                
LEFT JOIN    dbo.tbInternalPurchaseOrder ipo   ON ipo.PurchaseOrderNo = g.PurchaseOrderId                          
LEFT JOIN dbo.tbSupplierRMA h  ON a.SupplierRMANo  = h.SupplierRMAId                          
JOIN    dbo.tbCurrency j  ON a.CurrencyNo  = j.CurrencyId                          
LEFT JOIN    dbo.tbTax k  ON a.TaxNo    = k.TaxId               
LEFT JOIN tbCompany cop ON ipo.CompanyNo = cop.CompanyId           
LEFT JOIN tbSystemDocumentFooterHistory FHstry ON FHstry.SystemDocumentFooterHistoryId=a.[SystemDocumentFooterHistoryNo] 
GO


