using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Breadcrumb runat=server></{0}:Breadcrumb>")]
	public class Breadcrumb : WebControl {

		#region Locals
		private PlaceHolder _plhItems;
		private Panel _pnlItems;
		#endregion


		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			_pnlItems = ControlBuilders.CreatePanelInsideParent(this, "breadcrumb");
			_plhItems = ControlBuilders.CreatePlaceHolderInsideParent(_pnlItems);
			base.CreateChildControls();
		}

		/// <summary>
		/// on pre render
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (_plhItems.Controls.Count > 0) {
				_pnlItems.Visible = true;
			} else {
				_pnlItems.Visible = false;
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Adds an item to the breadcrumb trail
		/// </summary>
		public void AddItem(string strPageTitle, string strHref, string strDetail) {
			EnsureChildControls();
			if (_plhItems.Controls.Count > 0) {
				Literal lit = new Literal();
				lit.Text = "&nbsp;&gt;&nbsp;";
				_plhItems.Controls.Add(lit);
			}
			if (strDetail != "") strPageTitle += string.Format(" ({0})", strDetail);
			if (strHref == "") {
				Literal lit = new Literal();
				lit.Text = strPageTitle;
				_plhItems.Controls.Add(lit);
			} else {
				HtmlAnchor anc = new HtmlAnchor();
				anc.InnerText = strPageTitle;
				anc.HRef = strHref;
				_plhItems.Controls.Add(anc);
			}
		}
		public void AddItem(string strPageTitle) {
			AddItem(strPageTitle, "", "");
		}
		public void AddItem(string strPageTitle, string strHref) {
			AddItem(strPageTitle, strHref, "");
		}
		public void AddItem(SitePage objSitePage) {
			AddItem(Functions.GetGlobalResource("PageTitles", objSitePage.Name), objSitePage.Url, "");
		}
		public void AddItem(SitePage objSitePage, string strHref) {
			AddItem(Functions.GetGlobalResource("PageTitles", objSitePage.Name), strHref, "");
		}
		public void AddItem(SitePage objSitePage, string strHref, string strDetail) {
			AddItem(Functions.GetGlobalResource("PageTitles", objSitePage.Name), strHref, strDetail);
		}
	}
}