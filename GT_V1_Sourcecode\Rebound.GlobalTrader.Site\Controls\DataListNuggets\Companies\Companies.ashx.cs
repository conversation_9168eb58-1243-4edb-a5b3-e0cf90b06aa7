/* Marker     changed by      date         Remarks  
  [001]      <PERSON><PERSON><PERSON>      13-Sep2018   [REB-12820]:Provision to add Global Security on Contact Section
  [002]      <PERSON>      16-june2021   P8015 - Add search filter to contacts, customers and supplier to search for email address
  
 */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Text;
using System.Web.UI;
using System.Collections.Generic;
//using Rebound.GlobalTrader.Site.Controls.DropDowns.Data;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class Companies : Base {

		public override void ProcessRequest(HttpContext context) {
			base.ProcessRequest(context);
			switch (Action) {
				case "GetData_Companies": GetData_Companies(); break;
				case "GetData_Suppliers": GetData_Suppliers(); break;
				case "GetData_Customers": GetData_Customers(); break;
				case "GetData_Prospects": GetData_Prospects(); break;
                case "AddNew": AddNew(); break;
            }
		}
        public void AddNew()
        {
            bool? IsPohub = SessionManager.IsPOHub;
            try
            {
                int intNewID = BLL.Company.InsertToDoList(
                      SessionManager.ClientID
                    , GetFormValue_String("Name")
                    , GetFormValue_NullableInt("Contact")
                    , GetFormValue_NullableInt("Type")
                    , GetFormValue_String("Task")
                    , GetFormValue_NullableDateTime("TaskDate")
                    , GetFormValue_NullableDateTime("ReminderDate")
                    , DateTime.Now
                    , LoginID
                    , GetFormValue_NullableInt("CompanyId")

                );
                
                if (intNewID > 0) { 
              
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("NewID", intNewID);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception e)
            {

                WriteError(e);
            }
        }
        private void GetData_Companies() {

			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
            //[001] start
            int ClientId  = 0;
            if((GetFormValue_Boolean("IsGlobalLogin") || SessionManager.IsGSA.Value==true) && (GetFormValue_NullableInt("Client")??0)!=0)
            {
                ClientId = GetFormValue_NullableInt("Client")??0;
            }
            else
                ClientId = SessionManager.ClientID ?? 0;
			//[001] end
			//get data

			var CompanyStatus = GetFormValue_NullableInt("CompanyStatus") == 2 ? 0 : GetFormValue_NullableInt("CompanyStatus");

			List<Company> lst = Company.DataListNugget(
                ClientId
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex", 1)
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				 //, GetFormValue_StringForNameSearchDecode("Name")
				 /* For RP-2060 --start code*/
				 //, GetFormValue_StringForLikeSearchCompany("Name")
				 , GetFormValue_StringForSearch("Name")
				/********** end  code*/
				, GetFormValue_StringForSearch("Type")
				, GetFormValue_StringForSearch("City")
				//, GetFormValue_StringForSearch("Country")
				, null
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_NullableInt("CustomerRatingLo")
				, GetFormValue_NullableInt("CustomerRatingHi")
				, GetFormValue_NullableInt("SupplierRatingLo")
				, GetFormValue_NullableInt("SupplierRatingHi")
				, GetFormValue_StringForSearch("CustomerNo")
				, GetFormValue_StringForSearch("TelNo")
                , GetFormValue_StringForSearch("Zip")
                //[001] start
                , GetFormValue_Boolean("IsGlobalLogin")
                //[001] end
                , GetFormValue_StringForSearch("State")
                , GetFormValue_StringForSearch("County")
                , GetFormValue_NullableInt("Region")
                 //, GetFormValue_StringForSearch("Email")
                 , GetFormValue_String("Email")
				 , GetFormValue_NullableInt("CountryNo")
				 , GetFormValue_StringForSearch("MFR")
                 , GetFormValue_StringForSearch("GroupCodeName")
				 , GetFormValue_NullableInt("CompanyType")
                 , GetFormValue_StringForSearch("VATIDs")
                 , GetFormValue_StringForLikeSearchCompany("IndustryType")
				 , GetFormValue_StringForSearch("InsuranceCertificateNo")
				 , GetFormValue_NullableInt("CertificateCategoryNo")
				 , CompanyStatus
				 , SessionManager.IsGSA.Value
				 , SessionManager.ClientID
				 , SessionManager.LoginID
		   );
			ProcessData(lst);
			lst = null;
			SaveState();
		}

		private void GetData_Customers() {
			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
            //[001] start
            int ClientId = 0;
            if ((GetFormValue_Boolean("IsGlobalLogin") || SessionManager.IsGSA.Value == true) && (GetFormValue_NullableInt("Client") ?? 0) != 0)
            {
                ClientId = GetFormValue_NullableInt("Client") ?? 0;
            }
            else
                ClientId = SessionManager.ClientID ?? 0;
			//[001] end
			//get data	
			var CompanyStatus = GetFormValue_NullableInt("CompanyStatus") == 2 ? 0 : GetFormValue_NullableInt("CompanyStatus");

			List<Company> lst = Company.DataListNuggetAsCustomers(
                ClientId
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex", 1)
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
                //, GetFormValue_StringForNameSearch("Name")
                , GetFormValue_StringForNameSearchDecode("Name")
				, GetFormValue_StringForSearch("Type")
				, GetFormValue_StringForSearch("City")
				//, GetFormValue_StringForSearch("Country")
				, null
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_NullableInt("CustomerRatingLo")
				, GetFormValue_NullableInt("CustomerRatingHi")
				, GetFormValue_NullableInt("SupplierRatingLo")
				, GetFormValue_NullableInt("SupplierRatingHi")
				, GetFormValue_StringForSearch("CustomerNo")
				, GetFormValue_StringForSearch("TelNo")
                , GetFormValue_StringForSearch("Zip")
                //[001] start
                , GetFormValue_Boolean("IsGlobalLogin")
                //[001] end
                //[001] end
                , GetFormValue_StringForSearch("State")
                , GetFormValue_StringForSearch("County")
                , GetFormValue_NullableInt("Region")
                 //, GetFormValue_StringForSearch("Email")
                 , GetFormValue_String("Email")
				 , GetFormValue_NullableInt("CountryNo")
				  , GetFormValue_StringForSearch("MFR")
                  , GetFormValue_StringForSearch("GroupCodeName")
                  , GetFormValue_NullableInt("CompanyType")
                  , GetFormValue_StringForSearch("IndustryType")
				  , GetFormValue_StringForSearch("InsuranceCertificateNo")
				  , GetFormValue_NullableInt("CertificateCategoryNo")
				  , CompanyStatus
				   , SessionManager.IsGSA.Value
				 , SessionManager.ClientID
				 , SessionManager.LoginID
				 
			);
			ProcessData(lst);
			lst = null;
			SaveState();
		}

		private void GetData_Suppliers() {
			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
            //[001] start
            int ClientId = 0;
            if (GetFormValue_Boolean("IsGlobalLogin") && (GetFormValue_NullableInt("Client") ?? 0) != 0)
            {
                ClientId = GetFormValue_NullableInt("Client") ?? 0;
            }
            else
                ClientId = SessionManager.ClientID ?? 0;
			//[001] end
			//get data	
			var CompanyStatus = GetFormValue_NullableInt("CompanyStatus") == 2 ? 0 : GetFormValue_NullableInt("CompanyStatus");
			List<Company> lst = Company.DataListNuggetAsSuppliers(
                ClientId
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex", 1)
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
                , GetFormValue_StringForNameSearch("Name")
				, GetFormValue_StringForSearch("Type")
				, GetFormValue_StringForSearch("City")
				//, GetFormValue_StringForSearch("Country")
				, null
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_NullableInt("CustomerRatingLo")
				, GetFormValue_NullableInt("CustomerRatingHi")
				, GetFormValue_NullableInt("SupplierRatingLo")
				, GetFormValue_NullableInt("SupplierRatingHi")
				, GetFormValue_StringForSearch("CustomerNo")
				, GetFormValue_StringForSearch("TelNo")
                , GetFormValue_StringForSearch("Zip")
                //[001] start
                , GetFormValue_Boolean("IsGlobalLogin")
                 //[001] end
                 , GetFormValue_StringForSearch("State")
                , GetFormValue_StringForSearch("County")
                , GetFormValue_NullableInt("Region")
                 //, GetFormValue_StringForSearch("Email")
                 , GetFormValue_String("Email")
				 , GetFormValue_NullableInt("CountryNo")
				  , GetFormValue_StringForSearch("MFR")
                  , GetFormValue_StringForSearch("IndustryType")
				  , GetFormValue_StringForSearch("InsuranceCertificateNo")
				  , GetFormValue_NullableInt("CertificateCategoryNo")
				  , CompanyStatus
			);
			ProcessData(lst);
			lst = null;
			SaveState();
		}

		private void GetData_Prospects() {
			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
            //[001] start
            int ClientId = 0;
            if (GetFormValue_Boolean("IsGlobalLogin") && (GetFormValue_NullableInt("Client") ?? 0) != 0)
            {
                ClientId = GetFormValue_NullableInt("Client") ?? 0;
            }
            else
                ClientId = SessionManager.ClientID ?? 0;
			//[001] end
			//get data	
			var CompanyStatus = GetFormValue_NullableInt("CompanyStatus") == 2 ? 0 : GetFormValue_NullableInt("CompanyStatus");
			List<Company> lst = Company.DataListNuggetAsProspects(
                ClientId
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex", 1)
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
                , GetFormValue_StringForNameSearch("Name")
				, GetFormValue_StringForSearch("Type")
				, GetFormValue_StringForSearch("City")
				//, GetFormValue_StringForSearch("Country")
				, null
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_NullableInt("CustomerRatingLo")
				, GetFormValue_NullableInt("CustomerRatingHi")
				, GetFormValue_NullableInt("SupplierRatingLo")
				, GetFormValue_NullableInt("SupplierRatingHi")
				, GetFormValue_StringForSearch("CustomerNo")
				, GetFormValue_StringForSearch("TelNo")
                , GetFormValue_StringForSearch("Zip")
                //[001] start
                , GetFormValue_Boolean("IsGlobalLogin")
                  //[001] end
                  , GetFormValue_StringForSearch("State")
                , GetFormValue_StringForSearch("County")
                , GetFormValue_NullableInt("Region")
                //, GetFormValue_StringForSearch("Email")
                //[002] start
                , GetFormValue_String("Email")
				//[002] end
				, GetFormValue_NullableInt("CountryNo")
				 , GetFormValue_StringForSearch("MFR")
                 , GetFormValue_StringForSearch("IndustryType")
				 , GetFormValue_StringForSearch("InsuranceCertificateNo")
				 , GetFormValue_NullableInt("CertificateCategoryNo")
				 , CompanyStatus
		   );
			ProcessData(lst);
			lst = null;
			SaveState();
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void ProcessData(List<Company> lst) {
			JsonObject jsn = new JsonObject();
			bool? blnMakeYellow = false;
			int? SelectedclientNo = null;
			int? SessionClientNo = SessionManager.ClientID;
			if (SessionManager.IsGSA == true && SessionManager.IsGlobalUser == false)
			{
				SelectedclientNo = GetFormValue_NullableInt("Client");
				if (SelectedclientNo != null)
				{
					blnMakeYellow = true;
				}
				else
				{
					blnMakeYellow = false;
				}

			}
			else
			{
				blnMakeYellow = false;
			}
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			JsonObject jsnRowsArray = new JsonObject(true);
			foreach (Company it in lst) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", it.CompanyId);
				jsnRow.AddVariable("Name", it.CompanyName);
				jsnRow.AddVariable("Tel", it.Telephone);
				jsnRow.AddVariable("Type", it.CompanyType);
				jsnRow.AddVariable("City", it.City);
				jsnRow.AddVariable("Country", it.Country);
				jsnRow.AddVariable("SalespersonName", it.SalesmanName);
				jsnRow.AddVariable("OnStop", it.OnStop);
				jsnRow.AddVariable("LastContact", Functions.FormatDaysAgo(it.DaysSinceContact));
                jsnRow.AddVariable("Terms", it.TermsName);
                jsnRow.AddVariable("CustomerCode", it.CustomerCode);
                jsnRow.AddVariable("SupplierCode", it.SupplierCode);
                jsnRow.AddVariable("SalesTurnOver",string.Format("{0}/ {1}", Functions.FormatCurrency(it.SalesTurnover, 0),Functions.FormatCurrency(it.SalesGrossProfit, 0)));
                //[001] start
                jsnRow.AddVariable("ClientName", it.ClientName);
                //[001] end
                jsnRow.AddVariable("Email", it.EMail);
                jsnRow.AddVariable("TaskCount", it.TaskCount);
                jsnRow.AddVariable("CRMCompleteCount", it.CRMCompleteCount);
				jsnRow.AddVariable("blnMakeYellow", blnMakeYellow);
				jsnRow.AddVariable("InsuranceCertificateNo", it.InsuranceCertificateNo);
				jsnRow.AddVariable("CertificateCategoryName", it.CertificateCategoryName);
				string companyNotes = Company.GetAdvisoryNotes(it.CompanyId);
				jsnRow.AddVariable("AdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
				jsnRow.AddVariable("Inactive", it.Inactive);
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose(); jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose(); jsnRowsArray = null;
			jsn.Dispose(); jsn = null;
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Name");
			AddFilterState("Type");
			AddFilterState("City");
			AddFilterState("Country");
			AddFilterState("Salesman");
			AddFilterState("CustomerRating");
			AddFilterState("SupplierRating");
			AddFilterState("CustomerNo");
			AddFilterState("TelNo");
            AddFilterState("Zip");
            AddFilterState("Region");
            AddFilterStateWithHttpEncode("Email");
            AddFilterState("IndustryType");
			AddFilterState("InsuranceCertificateNo");
			AddFilterState("CertificateCategoryNo");
			AddFilterState("CompanyStatus");
			base.AddFilterStates();
		}

	}
}