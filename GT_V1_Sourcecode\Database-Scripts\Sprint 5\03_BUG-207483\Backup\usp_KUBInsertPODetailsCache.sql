
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KUBInsertPODetailsCache]    
@PartNo NVARCHAR(100)=null,      
@ClientID int=null     
AS    
/*    
 * Action: Created  By: <PERSON><PERSON><PERSON><PERSON>  Date: 01/08/2023  Comment: Add proc to cache the PO details.    
 */    
BEGIN    
SET NOCOUNT ON    
  
Declare @DefaultCurrency int = 0;  
Declare @DefaultCurrencyCode nvarchar(max); 
CREATE TABLE #tb_TempTopBuyPrice
(
POID		INT,
PO			INT,
[Date]		NVARCHAR(100),
Price		FLOAT,
ClientNo	INT,
Part		NVARCHAR(100),
DLUP		DATETIME,
IsClientPrice	BIT,
CurrencyCode	NVARCHAR(100)
) 
    
select @DefaultCurrency= CurrencyNo, @DefaultCurrencyCode= cr.CurrencyCode from tbClient c   
left join tbCurrency cr on c.CurrencyNo = cr.CurrencyId  
where ClientId = @ClientID;  
  
  
IF((SELECT COUNT(1) FROM tbKUBPoDetailsCache WHERE ClientNo=@ClientID AND Part=@PartNo     
 AND DLUP>DATEAdd(DAY,-3,GETDATE()))=0)    
BEGIN    
DELETE FROM tbKUBPoDetailsCache WHERE ClientNo=@ClientID AND Part=@PartNo    
INSERT INTO #tb_TempTopBuyPrice     
SELECT  po.PurchaseOrderId as POID, po.PurchaseOrderNumber as PO,       
CAST(FORMAT(po.DateOrdered,'dd-MM-yyyy') AS NVARCHAR(40)) as [Date],       
FORMAT(  
  CONVERT(  
    DECIMAL(16,5),dbo.ufn_convert_currency_value(pol.Price,po.CurrencyNo,@DefaultCurrency,GETDATE())  
     )  
 ,'N')  as Price ,    
@ClientID,    
@PartNo,    
GETDATE(),
0,
@DefaultCurrencyCode       
FROM tbPurchaseOrder  as po      
left join tbPurchaseOrderLine pol on po.PurchaseOrderId = pol.PurchaseOrderNo        
WHERE po.ClientNo=@ClientID       
AND pol.FullPart=@PartNo          
AND (pol.DeliveryDate)>CAST(DATEADD(MONTH,-6,GETDATE()) AS DATE)      

 
UNION

SELECT  ipo.InternalPurchaseOrderId as POID, ipo.InternalPurchaseOrderNumber as PO,       
CAST(FORMAT(ipo.DateOrdered,'dd-MM-yyyy') AS NVARCHAR(40)) as [Date],       
FORMAT(  
  CONVERT(  
    DECIMAL(16,5),dbo.ufn_convert_currency_value(ipol.Price,ipo.CurrencyNo,@DefaultCurrency,GETDATE())  
     )  
 ,'N') as Price ,    
@ClientID,    
@PartNo,    
GETDATE() ,
1,
@DefaultCurrencyCode      
FROM tbInternalPurchaseOrderLine ipol                    
LEFT OUTER JOIN tbInternalPurchaseOrder ipo ON ipol.InternalPurchaseOrderNo=ipo.InternalPurchaseOrderId  
LEFT OUTER JOIN tbPurchaseOrderLine pol ON ipol.PurchaseOrderLineNo=pol.PurchaseOrderLineId       
WHERE ipo.ClientNo=@ClientID       
AND pol.FullPart=@PartNo          
AND (pol.DeliveryDate)>CAST(DATEADD(MONTH,-6,GETDATE()) AS DATE)   

INSERT INTO tbKUBPoDetailsCache
SELECT  TOP 3 
POID,
PO,
[Date],
CAST(CONVERT(DECIMAL(16,5),Price) AS NVARCHAR(100))+' '+CurrencyCode,
ClientNo,
Part,
DLUP,
IsClientPrice 
FROM #tb_TempTopBuyPrice
ORDER BY price ASC
END  
DROP TABLE #tb_TempTopBuyPrice  
SET NOCOUNT OFF    
END 
GO


