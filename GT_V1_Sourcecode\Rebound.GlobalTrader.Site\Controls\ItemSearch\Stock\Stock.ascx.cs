//----------------------------------------------------------------------------------------------------------------
// RP 27.01.2010:
// - pass through SupplierRMANo to javascript
//
// RP 08.12.2009:
// - add more columns
//----------------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
    public partial class Stock : Base {

        private bool _blnForRMAs = false;
        public bool ForRMAs {
            get { return _blnForRMAs; }
            set { _blnForRMAs = value; }
        }

        private bool _blnIncludeQuarantined = false;
        public bool IncludeQuarantined {
            get { return _blnIncludeQuarantined; }
            set { _blnIncludeQuarantined = value; }
        }

        private bool _blnIncludeLotsOnHold = false;
        public bool IncludeLotsOnHold {
            get { return _blnIncludeLotsOnHold; }
            set { _blnIncludeLotsOnHold = value; }
        }

        public int SupplierRMANo { get; set; }

        protected override void OnInit(EventArgs e) {
            base.OnInit(e);
			SetItemSearchType("Stock");
			AddScriptReference("Controls.ItemSearch.Stock.Stock.js");
        }

        protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.Stock", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnForRMAs", _blnForRMAs);
            _scScriptControlDescriptor.AddProperty("blnIncludeQuarantined", _blnIncludeQuarantined);
            _scScriptControlDescriptor.AddProperty("blnIncludeLotsOnHold", _blnIncludeLotsOnHold);
            if (_blnForRMAs) _scScriptControlDescriptor.AddProperty("intSupplierRMANo", SupplierRMANo);
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e) {
            ctlDesignBase.MakeChildControls();
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", "SupplierPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("QuantityInStock", "QuantityOnOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("QuantityAllocated", "QuantityAvailable", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Warehouse", "Location", WidthManager.GetWidth(WidthManager.ColumnWidth.Warehouse), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Supplier", "LandedCost", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            if (_blnForRMAs) {
                ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("CRMA", "CustomerRMADate", Unit.Empty, true));
            } else {
                ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PurchaseOrderSerialNo", "DateDelivered", Unit.Empty, true));
            }
            if (_blnIncludeQuarantined) ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("QuarantinedAbbreviation", WidthManager.GetWidth(WidthManager.ColumnWidth.BooleanValue), true));
            base.OnPreRender(e);
        }

    }
}