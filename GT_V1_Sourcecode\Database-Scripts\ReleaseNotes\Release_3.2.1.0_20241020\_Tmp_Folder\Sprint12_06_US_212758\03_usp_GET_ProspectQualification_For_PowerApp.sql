﻿

GO

/*
--===========================================================================================  
TASK            UPDATED BY       DATE                ACTION        DESCRIPTION  
[US-212758]     Phuc Hoang         03-Oct-2024        CREATE        Prospect Qualification - Power Automate Function for Reminder
=============================================================================================  
*/


CREATE OR ALTER PROCEDURE [dbo].[usp_GET_ProspectQualification_For_PowerApp]
    
AS
BEGIN
SET NOCOUNT ON

Declare @PowerProspectUrl NVARCHAR(MAX)=''

-----------Power App url---------
SELECT
@PowerProspectUrl=FlowUrl
FROM tbPowerApp_urls WHERE FlowName='Prospect_Qualification_Notification'
--------------------------------------------------
SELECT
    @PowerProspectUrl AS PowerProspectUrl
    ,td.ToDoId
    ,td.LoginNo
    ,td.Subject
    ,td.DateAdded
    ,td.DueDate
    ,td.ToDoText
    ,td.Priority
    ,td.IsComplete
    ,td.ReminderDate
    ,td.ReminderText
    ,td.CompanyNo
    ,co.CompanyName
    ,td.ReminderHasBeenNotified
    ,td.RelatedMailMessageNo
    ,td.UpdatedBy
    ,td.DLUP
    ,td.HasReview
    ,td.Name
    ,td.ContactNo
    ,td.TypeNo
    ,lg.EmployeeName
    ,lg.EMail As ToEmail
  FROM  dbo.tbToDo td 
  LEFT JOIN dbo.tbCompany co ON td.CompanyNo = co.CompanyId
  LEFT JOIN dbo.tbLogin lg ON td.UpdatedBy = lg.LoginId
  WHERE ISNULL(td.ReminderText, '') <> ''  
  AND CAST(td.ReminderDate AS DATE) = CAST(GETDATE() AS DATE)  
  AND DATEPART(HOUR, td.ReminderDate) = DATEPART(HOUR, GETDATE())
  AND DATEPART(MINUTE, td.ReminderDate) >= DATEPART(MINUTE, GETDATE()) AND DATEPART(MINUTE, td.ReminderDate) <= DATEPART(MINUTE, GETDATE()) + 14
  
SET NOCOUNT OFF

END
GO


