Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.initializeBase(this,[n]);this._aryComponents=[]};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.prototype={get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._aryComponents=null,this._tbl=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.callBaseMethod(this,"dispose"))},addRow:function(n,t,i){var f=this._tbl.insertRow(-1),r,e,u,o;for(f.id=this.getControlID("tr",n),r=0,e=i.length;r<e;r++)u=document.createElement("td"),o=this.getControlID("txt",n,r),u.innerHTML=String.format('<input id="{0}" type="text" style="width: 100%;" value="{1}" />',o,i[r]),f.appendChild(u),u=null;f=null;u=null},getControlID:function(n,t,i){return String.format("{0}_{1}{2}_{3}",this._element.id,n,t,i)},clearRows:function(){for(var n=this._tbl.rows.length-1;n>=1;n--)this._tbl.deleteRow(n)}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);