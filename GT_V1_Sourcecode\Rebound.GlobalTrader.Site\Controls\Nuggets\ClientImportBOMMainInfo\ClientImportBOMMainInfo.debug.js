///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      Suhail       25/04/2018   Added contact and company name while sending mail via Add New Communication Note
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");
Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.initializeBase(this, [element]);
    this._intBOMID = -1;
    this._blnHasRequirement = false;
    this._blnRequestedToPoHub = false;
    this._blnRelease = false;
    this._isAddButtonEnable = true;
    this._isPurchaseHub = false;
    this._intCurrencyNo = -1;
    this._BomCode = "";
    this._BomName = "";
    this._BomCompanyName = "";
    this._BomCompanyNo = 0;
    this._intContact2No = -1;
    this._stringCurrency = null;
    this._inActive = false;
    this._BomContactname = "";
    this._BomContactNo = 0;
    // this._blnPOHub = false;
    this._CurrentSupplier = "";
    this._QuoteRequired = "";
    this._blnAllHasDelDate = false;
    this._blnAllHasProduct = false;
    this._blnCanReleaseAll=false;
    this._blnAllItemHasSourcing= false;
    this.BOMStatus=""; 
    this._isClosed = false;

    this._UpdatedBy = null;
    this._blnCanNoBidAll = true;
    this._isNoBidCount = false;
    this._RequestToPOHubBy = -1;
    this._UpdateByPH = -1;
    this._blnReqInValid = false;
    this._ValidMessage = "";
    this._ClientBOMStatus = -1;
    this._ClientCompanyId = -1;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.prototype = {
    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_ibtnDelete: function() { return this._ibtnDelete; }, set_ibtnDelete: function(value) { if (this._ibtnDelete !== value) this._ibtnDelete = value; },
    get_ibtnExportCSV: function() { return this._ibtnExportCSV; }, set_ibtnExportCSV: function(value) { if (this._ibtnExportCSV !== value) this._ibtnExportCSV = value; },
    get_ibtnExportPurchaseHUB: function() { return this._ibtnExportPurchaseHUB; }, set_ibtnExportPurchaseHUB: function(value) { if (this._ibtnExportPurchaseHUB !== value) this._ibtnExportPurchaseHUB = value; },
    get_ibtnNotify: function() { return this._ibtnNotify; }, set_ibtnNotify: function(value) { if (this._ibtnNotify !== value) this._ibtnNotify = value; },
    get_ibtnRelease: function() { return this._ibtnRelease; }, set_ibtnRelease: function(value) { if (this._ibtnRelease !== value) this._ibtnRelease = value; },
    get_blnPOHub: function() { return this._blnPOHub; }, set_blnPOHub: function(value) { if (this._blnPOHub !== value) this._blnPOHub = value; },
    get_ibtnClose: function() { return this._ibtnClose; }, set_ibtnClose: function(value) { if (this._ibtnClose !== value) this._ibtnClose = value; },
    get_ibtnNoBid: function () { return this._ibtnNoBid; }, set_ibtnNoBid: function (value) { if (this._ibtnNoBid !== value) this._ibtnNoBid = value; },
    get_ibtnNote: function () { return this._ibtnNote; }, set_ibtnNote: function (value) { if (this._ibtnNote !== value) this._ibtnNote = value; },
    get_ibtnComplete: function () { return this._ibtnComplete; }, set_ibtnComplete: function (value) { if (this._ibtnComplete !== value) this._ibtnComplete = value; },

    addGotData: function(handler) { this.get_events().addHandler("GotData", handler); },
    removeGotData: function(handler) { this.get_events().removeHandler("GotData", handler); },
    onGotData: function() {
        var handler = this.get_events().getHandler("GotData");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
    
     addCallBeforeRelease: function(handler) { this.get_events().addHandler("CallBeforeRelease", handler); },
    removeCallBeforeRelease: function(handler) { this.get_events().removeHandler("CallBeforeRelease", handler); },
    onCallBeforeRelease: function() {
        var handler = this.get_events().getHandler("CallBeforeRelease");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.showExportCSV));
        //   if (this._ibtnExportPurchaseHUB) $R_IBTN.addClick(this._ibtnExportPurchaseHUB, Function.createDelegate(this, this.savePurchaseHUBData));
        //edit form
      
        if (this._ibtnEdit) {

            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit._intBOMID = this._intBOMID;
            this._frmEdit._BomCode = this._BomCode;
            this._frmEdit._BomName = this._BomName;
            this._frmEdit._BomCompanyName = this._BomCompanyName;
            this._frmEdit._BomCompanyNo = this._BomCompanyNo;
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }
        //Delete
        if (this._ibtnComplete) {
            $R_IBTN.addClick(this._ibtnComplete, Function.createDelegate(this, this.showMarkasComplete));
            this._frmMarkasComplete = $find(this._aryFormIDs[1]);
            this._frmMarkasComplete._intBOMID = this._intBOMID;
            this._frmMarkasComplete.addNotConfirmed(Function.createDelegate(this, this.cancelComplete));
            this._frmMarkasComplete.addSaveComplete(Function.createDelegate(this, this.saveCompFormComplete));
        }
        
       //SaveAsHUBRFQ
        if (this._ibtnExportPurchaseHUB) {
            $R_IBTN.addClick(this._ibtnExportPurchaseHUB, Function.createDelegate(this, this.showSaveAsHUBRFQComplete));
            this._frmSaveAsHUBRFQ = $find(this._aryFormIDs[2]);
            this._frmSaveAsHUBRFQ._intBOMID = this._intBOMID;
            this._frmSaveAsHUBRFQ.addNotConfirmed(Function.createDelegate(this, this.cancelSaveAsHUBRFQComplete));
            this._frmSaveAsHUBRFQ.addSaveComplete(Function.createDelegate(this, this.saveAsHUBRFQComplete));
        }
        this.getData();

    },


    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnNotify) $R_IBTN.clearHandlers(this._ibtnNotify);
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
        if (this._ibtnClose) $R_IBTN.clearHandlers(this._ibtnClose);
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmDelete) this._frmDelete.dispose();
        this._ibtnNotify = null;
        this._intBOMID = null;
        this._ibtnEdit = null;
        this._ibtnDelete = null;
        this._frmEdit = null;
        this._frmDelete = null;
        this._ibtnExportCSV = null;
        this._blnHasRequirement = null;
        this._blnPOHub = null;
        this._blnRequestedToPoHub = null;
        this._blnRelease = null;
        this._intCurrencyNo = null;
        this._blnAllHasDelDate = null;
        this._blnAllHasProduct = null;
        this._blnCanReleaseAll=null;
        this._blnAllItemHasSourcing = null;
        this._ibtnClose = null;
        this._ibtnNoBid = null;
        this._ibtnNote = null;
        this._ValidMessage = null;
        this._SalesmanId = null;
        this._SalesmanName = null;
        this._ibtnComplete = null;
        this.__ibtnExportPurchaseHUB = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.callBaseMethod(this, "dispose");
    },

    getData: function() {
        this._blnRequestedToPoHub = false;
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientImportBOMMainInfo");
        obj.set_DataObject("ClientImportBOMMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },

    getDataOK: function(args) {
    
        var res = args._result;
        this.BOMStatus=res.BOMStatus;
        if (res.IsPoHub) {
            this._isPurchaseHub = res.IsPoHub;
            this.disableNotifyAndExportButton(!res.IsPoHub);
        }
        this._BomCode = res.Code;
        this._BomName = res.Name;
        this._BomCompanyName = res.Company;
        this._BomCompanyNo = res.CompanyNo;
        this.setFieldValue("ctlCode", $R_FN.setCleanTextValue(res.Code));
        this.setFieldValue("ctlName", $R_FN.setCleanTextValue(res.Name));
        this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes, true));
        this.setFieldValue("ctlInActive", res.InActive);
       // this.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(res.Company));
       // this.setFieldValue("ctlContact", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("hidCompanyNo", res.CompanyNo);
        this.setFieldValue("hidContactNo", res.ContactNo);
        this.setFieldValue("ctlCompanyName", $R_FN.setCleanTextValue(res.Company));
        this.setFieldValue("ctlContactName", $R_FN.setCleanTextValue(res.Contact));
        this.setFieldValue("ctlCurrencyCode", $R_FN.setCleanTextValue(res.CurrencyName));
        this.setFieldValue("ctlSalespersion", $R_FN.setCleanTextValue(res.Salesman));
        this.setFieldValue("hidSalesmanId", $R_FN.setCleanTextValue(res.SalesmanId));
        this._blnRequestedToPoHub = res.blnReqToPoHub;
        this._blnRelease = res.blnRelease;
        this.setFieldValue("hidDisplayStatus", $R_FN.setCleanTextValue(res.BOMStatus));
        //this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.CurrencyCode));
        //this.setFieldValue("ctlAS9120", res.AS9120);
        this._intCurrencyNo = res.CurrencyNo;
        //this.setFieldValue("ctl_Currency", $R_FN.setCleanTextValue(res.Currency_Code));
        this._stringCurrency = res.Currency_Code;
        this._intCurrencyNo = res.CurrencyNo;
        this.setDLUP(res.DLUP);
        this._inActive = res.InActive;
        this._CurrentSupplier = res.CurrentSupplier;
        this._QuoteRequired = res.QuoteRequired;
        this._blnAllItemHasSourcing = res.AllItemHasSourcing;

        this._UpdatedBy = res.UpdatedBy;
        this._isNoBidCount = res.isNoBidCount;
        this._isClosed = res.IsClosed;
        this._RequestToPOHubBy = res.RequestToPOHubBy;
        this._UpdateByPH = res.UpdateByPH;
        this._blnReqInValid = res.IsReqInValid;
        this._ValidMessage = res.ValidMessage;
        this.setFieldValue("hidSalesmanId", res.SalesmanId);
        this._SalesmanId = res.SalesmanId;
        this._SalesmanName = res.Salesman;
        this._isAddButtonEnable = !res.blnReqToPoHub && !res.InActive;
        //this.setFieldValue("ctlRecordRemaining", res.RecordRemaining);
        
        this._ClientBOMStatus = res.ClientBmStatus;
        this._ClientCompanyId = res.CompanyNo;
        this.setFieldValue("ctlMarkComplete", (res.ClientBmStatus === 4));
        this.setFieldValue("ctlRecordsProc", $R_FN.setCleanTextValue(res.RecordsProcessed));
        this.setFieldValue("ctlRecordsRemain", $R_FN.setCleanTextValue(res.RecordsRemaining));
        this.setFieldValue("ctlHUBRFQName", $R_FN.writeDoubleCellValue($RGT_nubButton_BOM(res.BomId, res.BomName), ""));
        this.getDataOK_End();
        this.onGotData();
        //alert(!res.blnReqToPoHub && !res.InActive);
    
        this.enableButtons(!res.InActive);
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    enableButtons: function (bln) {
      
        if (bln) {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !(this._ClientBOMStatus === 4 || this._ClientBOMStatus===3));
            if (this._ibtnComplete) $R_IBTN.enableButton(this._ibtnComplete, !(this._ClientBOMStatus === 4));
            if (this._ibtnExportPurchaseHUB) $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, !(this._ClientBOMStatus === 4 || this._ClientBOMStatus === 3));
         } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnComplete) $R_IBTN.enableButton(this._ibtnComplete, false);
            if (this._ibtnExportPurchaseHUB) $R_IBTN.enableButton(this._ibtnExportPurchaseHUB, false);
        }
    },

    showEditForm: function () {
        this._frmEdit._intBOMID = this._intBOMID;
        this._frmEdit._BomCode = this._BomCode;
        this._frmEdit._BomName = this._BomName;
        this._frmEdit._BomCompanyName = this._BomCompanyName;
        this._frmEdit._BomCompanyNo = this._BomCompanyNo;
        this._frmEdit.setFieldValue("ctlCode", this.getFieldValue("ctlCode"));
        this._frmEdit.setFieldValue("ctlName", this.getFieldValue("ctlName"));
        this._frmEdit.setFieldValue("ctlNotes", this.getFieldValue("ctlNotes"));
        this._frmEdit.setFieldValue("ctlInActive", this.getFieldValue("ctlInActive"));
        //this._frmEdit.getFieldDropDownData("ctlCurrency");
        this._frmEdit.setFieldValue("ctlCurrency", this._intCurrencyNo);
        this._frmEdit.setFieldValue("ctlCompany", this.getFieldValue("ctlCompanyName"));
        this._frmEdit.getFieldControl("ctlContact")._intCompanyID = this.getFieldValue("hidCompanyNo");
        this._frmEdit.getFieldDropDownData("ctlContact");
        this._frmEdit.setFieldValue("ctlContact", this.getFieldValue("hidContactNo"));
       // this._frmEdit.getFieldDropDownData("ctlSalesman");
        //this._frmEdit.setFieldValue("ctlSalespersion", this._SalesmanId);
        this._frmEdit.setFieldValue("ctlSalespersion", this.getFieldValue("hidSalesmanId"), null, this.getFieldValue("ctlSalespersion"));

        
        this._frmEdit._blnRequestedToPoHub = this._blnRequestedToPoHub;
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function() {
        this.showForm(this._frmEdit, false);
    },

    cancelEdit: function() {
        this.hideEditForm();
    },

    saveEditComplete: function() {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    showMarkasComplete: function() {
        this._frmMarkasComplete._intBOMID = this._intBOMID;
        this.showForm(this._frmMarkasComplete, true);
    },
    showSaveAsHUBRFQComplete: function () {
        this._frmSaveAsHUBRFQ._intBOMID = this._intBOMID;
        this._frmSaveAsHUBRFQ._BomName = this._BomName;
        this.showForm(this._frmSaveAsHUBRFQ, true);
    },
    showConfirmForm: function() {
        this._frmConfirm._intBOMID = this._intBOMID;
        this._frmConfirm._BomCode = this._BomCode;
        this._frmConfirm._BomName = this._BomName;
        this._frmConfirm._BomCompanyName = this._BomCompanyName;
        this._frmConfirm._BomCompanyNo = this._BomCompanyNo;
        this._frmConfirm._blnReqInValid = this._blnReqInValid;
        this._frmConfirm._ValidMessage = this._ValidMessage;
        this._frmConfirm._intContact2No = this._intContact2No;
        this.showForm(this._frmConfirm, true);
    },
    hideConfirmForm: function() {
        this.showForm(this._frmConfirm, false);
    },

    hideCompleteForm: function() {
        this.showForm(this._frmMarkasComplete, false);
    },
    hideSaveAsHUBRFQForm: function () {
        this.showForm(this._frmSaveAsHUBRFQ, false);
    },

    
   hideNoBidForm: function () {
        this.showForm(this._frmNoBid, false);
    },

    
    
    hideConfirmCloseForm: function() {
        this.showForm(this._frmConfirmClose, false);
    },

       
   
    hideReleaseForm: function() {
        this.showForm(this._frmRelease, false);
    },
    cancelComplete: function() {
        this.hideCompleteForm();
    },
    cancelSaveAsHUBRFQComplete: function () {
        this.hideSaveAsHUBRFQForm();
    },
    showExportCSV: function() {
        ////        alert(this._stringCurrency);
        this.getData_Start();
        //this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("ExportToCSV");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("currency_Code", this._stringCurrency);
        //alert(this.getFieldValue("ctlCurrency"));
        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },
    exportCSV_OK: function(args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    saveCompFormComplete: function () {
        this.hideCompleteForm();
        this.getData();
    },
    saveAsHUBRFQComplete: function () {
        this.hideSaveAsHUBRFQForm();
        this.getData();
    },


    savePurchaseHUBData: function() {
        this.getData_Start();
        //this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientImportBOMMainInfo");
        obj.set_DataObject("ClientImportBOMMainInfo");
        obj.set_DataAction("savePurchaseHUBData");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("BomCode", this._BomCode);
        obj.addParameter("BomName", this._BomName);
        obj.addParameter("BomCompanyName", this._BomCompanyName);
        obj.addParameter("BomCompanyNo", this._BomCompanyNo);

        //        obj.addParameter("BomContactname", this._BomContactname);
        //        obj.addParameter("BomContactNo", this._BomContactNo);

        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        // this.onSave();
    },

    disableNotifyAndExportButton: function(isDisabled) {
        if (this._ibtnNotify) $R_IBTN.showButton(this._ibtnNotify, isDisabled);
    },
    enableDisableReleaseButton: function(isDisabled) {
       // alert(this._blnHasRequirement && this._blnPOHub && !this._blnRelease && isDisabled);
       // if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, this._blnHasRequirement && this._blnPOHub && !this._blnRelease && isDisabled);
    },
   getValidationOK: function (args) {
        var res = args._result;
        this._blnReqInValid = res.IsReqInValid;
        this._ValidMessage = res.ValidMessage;
        this.getDataOK_End();
        this.onGotData();
        this.showConfirmForm();
    },

    getValidationError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    }
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientImportBOMMainInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
