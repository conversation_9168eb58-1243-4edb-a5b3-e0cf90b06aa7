using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
    public partial class Supplier : Base
    {

		private bool _blnForPOs = false;
		public bool ForPOs {
			get { return _blnForPOs; }
			set { _blnForPOs = value; }
		}

		private bool _blnForSOs = false;
		public bool ForSOs {
			get { return _blnForSOs; }
			set { _blnForSOs = value; }
		}
        private bool _blnShowPO = false;
        public bool ShowPOFilter
        {
            get { return _blnShowPO; }
            set { _blnShowPO = value; }
        }

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            SetItemSearchType("Supplier");
            AddScriptReference("Controls.ItemSearch.Supplier.Supplier.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("blnForPOs", _blnForPOs);
			_scScriptControlDescriptor.AddProperty("blnForSOs", _blnForSOs);
            _scScriptControlDescriptor.AddProperty("blnShowPOFilter", _blnShowPO);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("CompanyName", Unit.Empty, true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SupplierCode", Unit.Percentage(12), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("CompanyType", Unit.Percentage(14), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("City", Unit.Percentage(15), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Country", Unit.Percentage(15), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Tel", Unit.Percentage(10)));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Salesperson", Unit.Percentage(12), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("LastContacted", Unit.Percentage(12), true));
			base.OnPreRender(e);
		}

	}
}