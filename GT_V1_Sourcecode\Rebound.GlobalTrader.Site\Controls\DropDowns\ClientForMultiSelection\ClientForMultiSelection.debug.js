///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.ClientForMultiSelection = function(element) {
	Rebound.GlobalTrader.Site.Controls.DropDowns.ClientForMultiSelection.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ClientForMultiSelection.prototype = {

//	get_blnLimitToCurrentUsersDivision: function() { return this._blnLimitToCurrentUsersDivision; }, set_blnLimitToCurrentUsersDivision: function(v) { if (this._blnLimitToCurrentUsersDivision !== v) this._blnLimitToCurrentUsersDivision = v; },
//	get_blnLimitToCurrentUsersTeam: function() { return this._blnLimitToCurrentUsersTeam; }, set_blnLimitToCurrentUsersTeam: function(v) { if (this._blnLimitToCurrentUsersTeam !== v) this._blnLimitToCurrentUsersTeam = v; },
//	get_blnExcludeCurrentUser: function() { return this._blnExcludeCurrentUser; }, set_blnExcludeCurrentUser: function(v) { if (this._blnExcludeCurrentUser !== v) this._blnExcludeCurrentUser = v; },

	initialize: function() {
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
		Rebound.GlobalTrader.Site.Controls.DropDowns.ClientForMultiSelection.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
//		this._blnLimitToCurrentUsersDivision = null;
//		this._blnLimitToCurrentUsersTeam = null;
//		this._blnExcludeCurrentUser = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.ClientForMultiSelection.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/ClientForMultiSelection");
		this._objData.set_DataObject("ClientForMultiSelection");
		this._objData.set_DataAction("GetData");
//		this._objData.addParameter("LimitToCurrentUsersTeam", this._blnLimitToCurrentUsersTeam);
//		this._objData.addParameter("LimitToCurrentUsersDivision", this._blnLimitToCurrentUsersDivision);
//		this._objData.addParameter("ExcludeCurrentUser", this._blnExcludeCurrentUser);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Clients) {
			for (var i = 0; i < result.Clients.length; i++) {
				this.addOption(result.Clients[i].Name, result.Clients[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ClientForMultiSelection.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ClientForMultiSelection", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
