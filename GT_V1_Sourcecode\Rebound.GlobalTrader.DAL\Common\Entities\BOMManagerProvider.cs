﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
	public abstract class BOMManagerProvider : DataAccess
	{
		static private BOMManagerProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public BOMManagerProvider Instance
		{
			get
			{
				if (_instance == null) _instance = (BOMManagerProvider)Activator.CreateInstance(Type.GetType("Rebound.GlobalTrader.DAL.SqlClient.SQLBOMManagerProvider"));
				return _instance;
			}
		}
		public BOMManagerProvider()
		{
			this.ConnectionString = Globals.Settings.BOMManager.ConnectionString;
		}
		/// <summary>
		/// Get uploaded BOM file Data
		/// Calls [USP_BOMSEARCH]
		/// </summary>
		public abstract List<BOMManager> BOMSearch(System.Int32 BOMId, System.Int32? clientId, DateTime? FromDate, DateTime? ToDate, System.Int32? stageId, bool? isLock, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId);
		public abstract BOMManager Get(System.Int32? bomManagerId);
		/// <summary>
		/// GetBOMListForCustomerRequirement 
		/// Calls [usp_selectAll_CustomerRequirement_for_BOM]
		/// </summary>
		public abstract List<BOMManager> GetBOMListForCustomerRequirement(System.Int32? BOMNo, System.Int32? clientID, int curPage, int Rpp, System.String Part, System.Int32? ReqType = 0);
		/// <summary>
		/// Update
		/// Calls [usp_update_BOM]
		/// </summary>
		public abstract bool UpdateBOMManager(System.Int32? bomId, System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? CurrencyNo, System.String CurrentSupplier, System.DateTime? QuoteRequired, System.Boolean? AS9120, System.Int32? contact2Id, System.Int32? salespersonId);
		/// <summary>
		/// Update
		/// Calls [usp_update_BOM_POHubQuote]
		/// </summary>
		public abstract bool UpdatePurchaseQuote(System.Int32? bomId, System.Int32? updatedBy, System.Int32? bomStatus, System.Int32 AssignUserNo, out System.String ValidateMessage, string AssignedUserType);
		/// <summary>
		/// Calls [usp_update_BOMStatusToClosed]
		/// </summary>
		public abstract DataTable UpdateBOMStatusToClosed(System.Int32? bomId, System.Int32? updatedBy, System.Int32? bomStatus);
		public abstract List<CustomerRequirementDetails> DataListNuggetHUBRFQBOMManager(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String BOMCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.String Manufacturer, System.String Part, System.Int32? intDivision, System.DateTime? startdate, System.DateTime? enddate, System.Int32? salesPerson);
		public abstract List<BOMManager> DataListNuggetOld(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String bomManagerCode, System.String bomManagerName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomManagerStatus, int? isAssignToMe, int? assignedUser, System.Int32? intDivisionNo, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, string CompanyNo,System.Int32? MailGroupId);
		public abstract List<BOMManager> DataListNuggetOld_ForAssign(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String bomManagerCode, System.String bomManagerName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomManagerStatus, int? isAssignToMe, int? assignedUser, System.Int32? intDivisionNo, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, string CompanyNo);
		/// <summary>
		/// DataListNugget
		/// Calls [usp_datalistnugget_BOM]
		/// </summary>
		public abstract List<BOMManager> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String bomCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? isAssignToMe, int? assignedUser, System.Int32? intDivisionNo, System.Int32? salesPerson, System.DateTime? startdate, System.DateTime? enddate, string CompanyNo);
		//public abstract List<BOMManager> BOMSearch(System.Int32 BOMId, System.Int32? clientId, DateTime? FromDate, DateTime? ToDate, System.Int32? stageId, bool? isLock, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId);
		public abstract DataTable GetBOMManagerGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId);
		public abstract void SaveBOMManagerExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);
		public abstract void saveBOMManagerExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency);
		public abstract DataTable GetBOMManagerDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);
		public abstract DataTable GenerateCustomTemplateData(int QuoteId, string ColumnString);
		public abstract DataTable SaveCustomTemplateMapping(int QuoteId, string MappingDetails, int? clientNo, int CompanyNo, int? updatedBy);
		public abstract DataTable GetBOMManagerUploadMapping(int QuoteId, int? clientNo);
		public abstract DataTable GetCustomTemplateMapping(int QuoteId, int? clientNo);
		public abstract string SaveBOMManagerImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out System.String errorMessage, string BomName, string CompanyName, string ContactName, int SalesmanId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, out System.String NewBomCode, out System.Int32 NewBomid, System.Int32? ReqforTraceabilityId, System.Int32? TypeId, System.DateTime DateRequired);
		/// <summary>
		/// Source
		/// Calls [[usp_ipobom_source_Offer]]
		/// </summary>
		public abstract List<OfferDetails> IPOBOMAutoSource(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, int? BOM, int? CallType, int? customerRequirementId, int curPage, int Rpp);

		public abstract List<AutoSourcing> GetAutoSourcingResult(System.Int32? BOM, int? CustomerReqID, int curPage, int Rpp);
		public abstract List<AutoSourcing> GetBOMManagerAutoSourcing(System.Int32 BOMManagerId, bool IsPOHUB, int? CustomerReqID, int curPage, int Rpp);
		public abstract QuoteDetails LoadQuoteGenerationDataBOMManager(int CustomerReqID, System.Int32 BOMManagerNo);
		public abstract DataTable UpdateAutoSourcing(int BOM,
											   int SourceId,
											   int? SPQ,
											   double ReSell,
											   double Cost,
											   int EditMfrId,
											   int EditProdId,
											   string Reason,
											   string part,
											   int? rohs,
											   int? countryOfOrigin,
											   string dateCode,
											   int? packageNo,
											   int quantity,
											   int? offerStatusNo,
											   string factorySealed,
											   int? mslNo,
											   string totalQSA,
											   string moq,
											   string ltb,
											   int currencyNo,
											   double? shippingCost,
											   string leadTime,
											   int? regionNo,
											   DateTime? deliveryDate,
											   int? supplierWarranty,
											   string rohsStatus,
											   string notes,
											   bool? isTestingRecommened,
											   int? UpdatedBy);
		public abstract DataTable UpdateReasonForAutoSourcing(System.Int32? BOM, System.Int32? SourceId, string Reason, int? UpdatedBy);
		public abstract DataTable ReplaceSourcingBOMManager(System.Int32 BOM, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy, System.Int32 ReplaceSourceType);
		public abstract DataTable RemoveOfferBOMManager(System.Int32 BOM, System.Int32 SourceId, System.Int32 UpdatedBy);
		public abstract DataTable AddNewOffer(int BOM,
											int sourceId,
											int CustomerRequirementId,
											int UpdatedBy,
											int ReplaceSourceType,
											string OfferSource);
		public abstract DataTable Save_PrimarySourcing(System.Int32 ClientId, System.Int32 SourcingResultId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy);
		public abstract DataTable Reset_PrimarySourcing(System.Int32 ClientId, System.Int32 SourcingResultId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy);
		public abstract DataTable AssignUser(string BOMManagerids, System.Int32 AssignUserId);
		public abstract List<SourcingResultDetails> GetListForBOMManagerReleaseAll(System.Int32? BOMManagerID, System.Boolean isPOHub, int reqStatus);
		public abstract List<String> GetUnsourcedParts(System.Int32? BOMManagerID);
		public abstract DataTable BOMManagerReleaseRequirement(System.Int32? BOMManagerID, System.Int32? updatedBy, System.String CIds, System.Int32? ReqType, System.String NoBidNotes, System.String ASIds);
		public abstract DataTable BOMManagerUpdateCustomerRequirementReleaseNotes(System.Int32 CustomerRequirementID, System.String ReleaseNote);
		public abstract DataTable GetBOMManagerStatus(System.Int32 BOM);
		public abstract bool DeleteBOMManagerItem(int BOMManagerID, string CustReqID, int? UpdatedBy);
		public abstract bool SaveEditBOMItemData(int customerRequirementId, int RequirementforTraceability, int salesman, int quantity, System.Int32? Usage, int Type, System.String EAU, int manufacturerNo, string customerPart, Double TargetSellPrice, int currencyNo, System.Byte? rohs, string dateCode, int productNo, System.Int32? PackageNo, string MSL, DateTime DatePromised, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, string BOMManagerName, System.String notes, System.String instructions, System.Int32? SupportTeamMemberNo, int BOMManagerID, System.Int32? UpdatedBy);

		// [002] Code Start Here
		/// <summary>
		/// GetListReadyForClient
		/// Calls [usp_selectAll_BOM]
		/// </summary>
		public abstract List<BOMManager> GetBomManagerList(System.Int32? clientId, System.Boolean? isPoHUB, System.Int32? topToSelect, System.Int32? bomStatus, System.Int32? updatedBy);
		public abstract bool EditSourcingResultsSalesman(System.Int32 CRID, Double UnitPrice, string Notes, System.Int32? updatedBy);
		public abstract Int32 InsertFromSourcingResultBOMManager(System.Int32? sourcingResultId, System.Int32? quoteNo, System.DateTime? dateQuoted, int BOMManagerID);
		public abstract DataTable GetCustomTemplateData(int QouteID);
		public abstract bool SaveUpliftAllPrice(int BOMManagerID, float UpliftPercentage, System.Int32? UpdatedBy);
		public abstract DataTable GetUpliftPercentageAll(System.Int32 BOM);
		public abstract bool RemoveUpliftPriceAll(int BOMManagerID, System.Int32? UpdatedBy);
		public abstract List<AutoSourcing> GetBOMManagerSourcingForUplift(System.Int32 BOMManagerId, System.String CustomerRequirementIds = "");
		public abstract bool SaveBOMItemNoBid(int BOMManagerID, int CustomerRequirementId, int? UpdatedBy, string NoBidReason);
		public abstract bool RecallNoBidRequirement(System.Int32? customerRequirementId, System.Int32? updatedBy);
		public abstract bool ResetUpliftPriceAll(int BOMManagerID, int? ClientID);
		public abstract bool PHSaveEditBOMItemData(int customerRequirementId, int manufacturerNo, int productNo, int BOMManagerID, System.Int32? UpdatedBy);

		/// <summary>
		/// GetListForCompany
		/// Calls [usp_selectAll_BOMManager_for_Company]
		/// </summary>
		public abstract List<BOMManager> GetListForCompany(System.Int32? clientId, System.Int32? companyId, System.Boolean? includeClosed);

		/// <summary>
		/// GetListForCompany
		/// Calls [usp_count_BOMManager_for_Company]
		/// </summary>
		public abstract Int32 CountForCompany(System.Int32? companyId, System.Boolean? includeClosed);
	}
}
