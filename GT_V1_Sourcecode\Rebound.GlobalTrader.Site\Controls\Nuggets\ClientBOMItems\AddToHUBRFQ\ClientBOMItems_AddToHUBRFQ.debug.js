///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.initializeBase(this, [element]);
    this._intLineID = 0;
    this._intRequirementLineID = 0;
    this._strCompanyName = "";
    this._intBOMID = 0;
    this._intClientCompanyId = -1;
    this._CustReqNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.prototype = {
    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._trSourceFromRequirement = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {

            //form actions
            this.addSave(Function.createDelegate(this, this.saveAdd));
         
            this._strPathToData = "controls/Nuggets/ClientBOMItems";
            this._strDataObject = "ClientBOMItems";
        }
        this.getFieldControl("ctlBOMHeader")._intCompanyID = this._intClientCompanyId;
        this.getFieldDropDownData("ctlBOMHeader");

    },


    loadDropDowns: function() {
        //this.getFieldDropDownData("ctlPackage");
        this.getFieldDropDownData("ctlROHS");

    },

    selectRequirementItem: function() {
        this.continueClicked();
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = true;
        if (this.getFieldValue("ctlBOMHeader") == null)
        {
            blnOK = false;
            this.showError(true, "Please select HUBRFQ for this Requirement.");
        }
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    saveAdd: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("UpdateCustomerRequirementWithHUBRFQ");
        //obj.addParameter("id", this._intPOQuoteID);
        obj.addParameter("BomId", this.getFieldValue("ctlBOMHeader"));
        obj.addParameter("CompanyNo",  this._intClientCompanyId);
        obj.addParameter("CustomerRequirementNumber", this._CustReqNo);
       // obj.addParameter("ReqIds", $R_FN.arrayToSingleString(this._ctlReqsWithBOM._tblResults._aryCurrentValues, ","));
        obj.addDataOK(Function.createDelegate(this, this.saveAddOK));
        obj.addError(Function.createDelegate(this, this.saveAddError));
        obj.addTimeout(Function.createDelegate(this, this.saveAddError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveAddError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveAddOK: function(args) {
        if (args._result.Result == true) {
            this._intLineID = args._result.NewID;
            this.onSaveComplete();
        } else {
            this.saveEditError(args);
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMItems_AddToHUBRFQ", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
