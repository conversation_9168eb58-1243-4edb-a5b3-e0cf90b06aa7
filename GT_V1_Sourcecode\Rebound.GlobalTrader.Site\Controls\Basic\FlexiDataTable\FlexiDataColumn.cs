//-----------------------------------------------------------------------------------------
// RP 06.11.2009:
// - begin adding ability to sort data in table without returning to the database
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.ComponentModel;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Text;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site.Enumerations;


namespace Rebound.GlobalTrader.Site.Controls {

	/// <summary>
	/// A Column object
	/// </summary>
	[Serializable]
	public class FlexiDataColumn : TableHeaderCell, INamingContainer {


		#region Properties

		/// <summary>
		/// Is it sortable?
		/// </summary>
		private bool _blnIsSortable = false;
		public bool IsSortable {
			get { return _blnIsSortable; }
			set { _blnIsSortable = value; }
		}

		/// <summary>
		/// Sort expression for this column
		/// </summary>
		private List<int> _intSortFields;
		public List<int> SortFields {
			get { return _intSortFields; }
			set { _intSortFields = value; }
		}

		/// <summary>
		/// Parent design base control
		/// </summary>
		private FlexiDataTable _ctlParentFlexiDataTable;
		public FlexiDataTable ParentFlexiDataTable {
			get { return _ctlParentFlexiDataTable; }
			set { _ctlParentFlexiDataTable = value; }
		}

		/// <summary>
		/// Column Index
		/// </summary>
		private int _intColumnIndex;
		public int ColumnIndex {
			get { return _intColumnIndex; }
			set { _intColumnIndex = value; }
		}

		/// <summary>
		/// Is this the currently sorted field (initially)?
		/// </summary>
		private bool _blnIsCurrentSort;
		public bool IsCurrentSort {
			get { return _blnIsCurrentSort; }
			set { _blnIsCurrentSort = value; }
		}

		/// <summary>
		/// sort direction
		/// </summary>
		private SortColumnDirection _enmSortDirection;
		public SortColumnDirection SortDirection {
			get { return _enmSortDirection; }
			set { _enmSortDirection = value; }
		}

		private string _strText1;
		public string Text1 {
			get { return _strText1; }
			set { _strText1 = value; }
		}

		private string _strText2;
		public string Text2 {
			get { return _strText2; }
			set { _strText2 = value; }
		}

        private string _strText3;
        public string Text3
        {
            get { return _strText3; }
            set { _strText3 = value; }
        }

		/// <summary>
		/// What format is this field for a client sort?
		/// </summary>
		private ClientSortFormat _enmFormatForClientSort = ClientSortFormat.Text;
		public ClientSortFormat FormatForClientSort {
			get { return _enmFormatForClientSort; }
			set { _enmFormatForClientSort = value; }
		}

		#endregion

		#region Constructors

		/// <summary>
		/// A column object
		/// </summary>
		public FlexiDataColumn() { }
		public FlexiDataColumn(string strText) {
			SetText(strText);
			Width = Unit.Empty;
			_blnIsSortable = false;
			HorizontalAlign = HorizontalAlign.Left;
		}
		public FlexiDataColumn(string strText1, string strText2) {
			SetText(strText1, strText2);
			Width = Unit.Empty;
			_blnIsSortable = false;
			HorizontalAlign = HorizontalAlign.Left;
		}        
		public FlexiDataColumn(string strText, Unit untWidth) {
			SetText(strText);
			Width = untWidth;
			_blnIsSortable = false;
			HorizontalAlign = HorizontalAlign.Left;
		}
		public FlexiDataColumn(string strText1, string strText2, Unit untWidth) {
			SetText(strText1, strText2);
			Width = untWidth;
			_blnIsSortable = false;
			HorizontalAlign = HorizontalAlign.Left;
		}
		public FlexiDataColumn(string strText, Unit untWidth, bool blnIsSortable) {
			SetText(strText);
			Width = untWidth;
			_blnIsSortable = blnIsSortable;
			HorizontalAlign = HorizontalAlign.Left;
		}
		public FlexiDataColumn(string strText, Unit untWidth, bool blnIsSortable, ClientSortFormat enmSortFormat) {
			SetText(strText);
			Width = untWidth;
			_blnIsSortable = blnIsSortable;
			HorizontalAlign = HorizontalAlign.Left;
			_enmFormatForClientSort = enmSortFormat;
		}
		public FlexiDataColumn(string strText1, string strText2, Unit untWidth, bool blnIsSortable) {
			SetText(strText1, strText2);
			Width = untWidth;
			_blnIsSortable = blnIsSortable;
			HorizontalAlign = HorizontalAlign.Left;
		}

        public FlexiDataColumn(string strText1, string strText2, string strText3, Unit untWidth, bool blnIsSortable)
        {
            SetText(strText1, strText2, strText3);
            Width = untWidth;
            _blnIsSortable = blnIsSortable;
            HorizontalAlign = HorizontalAlign.Left;
        }

		public FlexiDataColumn(string strText1, string strText2, Unit untWidth, bool blnIsSortable, ClientSortFormat enmSortFormat) {
			SetText(strText1, strText2);
			Width = untWidth;
			_blnIsSortable = blnIsSortable;
			HorizontalAlign = HorizontalAlign.Left;
			_enmFormatForClientSort = enmSortFormat;
		}
		public FlexiDataColumn(string strText, Unit untWidth, bool blnIsSortable, HorizontalAlign enmAlign) {
			SetText(strText);
			Width = untWidth;
			_blnIsSortable = blnIsSortable;
			HorizontalAlign = enmAlign;
		}
		public FlexiDataColumn(string strText, Unit untWidth, bool blnIsSortable, HorizontalAlign enmAlign, ClientSortFormat enmSortFormat) {
			SetText(strText);
			Width = untWidth;
			_blnIsSortable = blnIsSortable;
			HorizontalAlign = enmAlign;
			_enmFormatForClientSort = enmSortFormat;
		}
		public FlexiDataColumn(string strText, HorizontalAlign enmAlign) {
			SetText(strText);
			Width = Unit.Empty;
			_blnIsSortable = false;
			HorizontalAlign = enmAlign;
		}
		public FlexiDataColumn(string strText, Unit untWidth, HorizontalAlign enmAlign) {
			SetText(strText);
			Width = untWidth;
			HorizontalAlign = enmAlign;
			_blnIsSortable = false;
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			switch (HorizontalAlign) {
				case HorizontalAlign.Right: CssClass += " alignRight"; break;
				case HorizontalAlign.Center: CssClass += " alignCenter"; break;
				case HorizontalAlign.Justify: CssClass += " alignJustify"; break;
			}
			HorizontalAlign = HorizontalAlign.NotSet; //reset to allow css to handle the display
			Attributes.Add("col_index", _intColumnIndex.ToString());
			base.Render(writer);
		}

		#endregion

		#region Methods

		private void SetText(string strText) {
			_strText1 = strText;
			Text = Functions.GetGlobalResource("TableColumnHeadings", strText);
		}
		private void SetText(string strText1, string strText2) {
			_strText1 = strText1;
			_strText2 = strText2;
			Text = (String.Format("<div class=\"doubleHeaderTop\">{0}</div>{1}", Functions.GetGlobalResource("TableColumnHeadings", strText1), Functions.GetGlobalResource("TableColumnHeadings", strText2)));
		}

        private void SetText(string strText1, string strText2, string strText3)
        {
            _strText1 = strText1;
            _strText2 = strText2;
            _strText3 = strText3;
            Text = (String.Format("<div class=\"doubleHeaderTop\">{0}</div><div class=\"doubleHeaderTop\">{1}</div>{2}", Functions.GetGlobalResource("TableColumnHeadings", strText1), Functions.GetGlobalResource("TableColumnHeadings", strText2), Functions.GetGlobalResource("TableColumnHeadings", strText3)));
        }

		#endregion

		#region Enumerations

		public enum ClientSortFormat {
			Text,
			Numeric,
			Currency,
			Date,
			DateAndTime
		}

		#endregion

	}

}