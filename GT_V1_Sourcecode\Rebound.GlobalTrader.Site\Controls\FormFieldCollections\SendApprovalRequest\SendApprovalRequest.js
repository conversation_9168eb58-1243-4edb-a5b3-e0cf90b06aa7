Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendApprovalRequest=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendApprovalRequest.initializeBase(this,[n]);this._aryRecipientLoginIDs=[];this._aryRecipientLoginNames=[];this._aryRecipientGroupIDs=[];this._aryRecipientGroupNames=[];this._intNumberRecipients=0};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendApprovalRequest.prototype={get_pnlSelected:function(){return this._pnlSelected},set_pnlSelected:function(n){this._pnlSelected!==n&&(this._pnlSelected=n)},get_lblSelected:function(){return this._lblSelected},set_lblSelected:function(n){this._lblSelected!==n&&(this._lblSelected=n)},get_autLoginOrGroup:function(){return this._autLoginOrGroup},set_autLoginOrGroup:function(n){this._autLoginOrGroup!==n&&(this._autLoginOrGroup=n)},initialize:function(){this._autLoginOrGroup._intGlobalLoginClientNo=this._intGlobalClientNo;this._autLoginOrGroup.addSelectionMadeEvent(Function.createDelegate(this,this.newRecipientSelected));this.showSelected();Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendApprovalRequest.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._autLoginOrGroup&&this._autLoginOrGroup.dispose(),this._pnlSelected=null,this._lblSelected=null,this._autLoginOrGroup=null,this._aryRecipientLoginIDs=null,this._aryRecipientLoginNames=null,this._aryRecipientGroupIDs=null,this._aryRecipientGroupNames=null,this._intNumberRecipients=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendApprovalRequest.callBaseMethod(this,"dispose"))},newRecipientSelected:function(){this._autLoginOrGroup._varSelectedExtraData.toUpperCase()=="GROUP"?this.addNewGroupRecipient(this._autLoginOrGroup._varSelectedID,this._autLoginOrGroup._varSelectedValue):this.addNewLoginRecipient(this._autLoginOrGroup._varSelectedID,this._autLoginOrGroup._varSelectedValue)},SetApproverType:function(n){this._autLoginOrGroup._strApprovalType=n},SetDocID:function(n){this._autLoginOrGroup._intDocmentId=n},addNewLoginRecipient:function(n,t){Array.contains(this._aryRecipientLoginIDs,n)||(Array.add(this._aryRecipientLoginIDs,n),Array.add(this._aryRecipientLoginNames,t),this._intNumberRecipients+=1);this.showSelected();this._autLoginOrGroup.reselect()},addNewGroupRecipient:function(n,t){Array.contains(this._aryRecipientGroupIDs,n)||(Array.add(this._aryRecipientGroupIDs,n),Array.add(this._aryRecipientGroupNames,t),this._intNumberRecipients+=1);this.showSelected();this._autLoginOrGroup.reselect()},showSelected:function(){var i,n,t,r;for($R_FN.showElement(this._pnlSelected,this._intNumberRecipients>0),i="",n="",t=0,r=this._aryRecipientGroupIDs.length;t<r;t++)n+='<div class="mailRecipient mailRecipientGroup">',n+="<b>"+this._aryRecipientGroupNames[t]+"<\/b>",i=String.format("$find('{0}').removeGroupRecipient({1});",this._element.id,t),n+=String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]<\/a>',i),n+="<\/div>";for(t=0,r=this._aryRecipientLoginIDs.length;t<r;t++)n+='<div class="mailRecipient">',n+=this._aryRecipientLoginNames[t],i=String.format("$find('{0}').removeLoginRecipient({1});",this._element.id,t),n+=String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]<\/a>',i),n+="<\/div>";$R_FN.setInnerHTML(this._lblSelected,n)},removeLoginRecipient:function(n){Array.removeAt(this._aryRecipientLoginIDs,n);Array.removeAt(this._aryRecipientLoginNames,n);this._intNumberRecipients-=1;this.showSelected()},removeGroupRecipient:function(n){Array.removeAt(this._aryRecipientGroupIDs,n);Array.removeAt(this._aryRecipientGroupNames,n);this._intNumberRecipients-=1;this.showSelected()},setValue_Subject:function(n){this._ctlRelatedForm.setFieldValue("ctlSubject",n)},getValue_Subject:function(){return this._ctlRelatedForm.getFieldValue("ctlSubject")},setValue_Body:function(n){this._ctlRelatedForm.setFieldValue("ctlBody",n)},getValue_Body:function(){return this._ctlRelatedForm.getFieldValue("ctlBody")},setValue_To:function(n){this._ctlRelatedForm.setFieldValue("ctlTo",n)},getValue_To:function(){return this._ctlRelatedForm.getFieldValue("ctlTo")},validateFields:function(){var n=!0;return this._ctlRelatedForm.resetFormFields(),this._ctlRelatedForm.checkFieldEntered("ctlBody")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlSubject")||(n=!1),this._intNumberRecipients==0&&(this._ctlRelatedForm.setFieldInError("ctlTo",!0,$R_RES.RequiredFieldMissingMessage),n=!1),n},resetFields:function(){this.setValue_Subject("");this.setValue_Body("");Array.clear(this._aryRecipientLoginIDs);Array.clear(this._aryRecipientLoginNames);Array.clear(this._aryRecipientGroupIDs);Array.clear(this._aryRecipientGroupNames);this._intNumberRecipients=0;this.showSelected()}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendApprovalRequest.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendApprovalRequest",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);