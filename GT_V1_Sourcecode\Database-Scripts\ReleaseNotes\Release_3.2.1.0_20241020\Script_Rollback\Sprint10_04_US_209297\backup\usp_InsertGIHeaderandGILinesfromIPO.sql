﻿Alter PROCEDURE usp_InsertGIHeaderandGILinesfromIPO                                                     
@ClientNo INT = 0,                                                      
@PurchaseOrderNo INT,                                        
@Location   NVARCHAR(100)='',                                      
@Qnty_Stock INT=0,                                      
@Qnty_SalesOrder INT=0 ,                                  
@StockID INT,  
@IPOPrice FLOAT=0                                  
AS                                                    
BEGIN                                           
declare @HubGoodsInNo int  
declare @HubGoodsInLineNo int  
declare @HubGIStockNO int  
DECLARE @BuyCurrency INT =0;  
set @HubGIStockNO=@StockID   
select @HubGoodsInLineNo=GoodsInLineNo from tbStock where Stockid=@HubGIStockNO and GoodsInLineNo is not null    
 ------------------------------------   
  
 ------------------------------------  
 set @HubGIStockNO=@StockID    
/**First Part for GI Header -- Start CODE **/                                                     
DECLARE  @ShipViaNo INT=0, @AirWayBill NVARCHAR(50) = NULL ,@Reference NVARCHAR(50) = NULL, @CompanyNo INT, @ReceivingNotes NVARCHAR(MAX) = NULL                                                    
 , @DateReceived DATETIME=GETDATE(),@PurchaseOrderLineNo INT , @CustomerRMANo INT=Null, @WarehouseNo INT=0, @CurrencyNo INT=0                                                    
 , @UpdatedBy INT=0 , @PurchaseCountryNo INT=0,                                                    
   @GoodsInId INT=0,@ClientLandedCost FLOAT=0;                                                     
SET    @Location= IIF(ISNULL(@Location,'')='','UK',@Location);                                     
 SELECT                                                    
    @ShipViaNo=shipviaNo, @AirWayBill=AirWayBillPO,@WarehouseNo=WarehouseNo,@CurrencyNo=CurrencyNo ,@CompanyNo=CompanyNo                                                   
 ,@PurchaseCountryNo=ImportCountryNo,@UpdatedBy=vw.UpdatedBy                                                    
 ,@PurchaseOrderLineNo=pol.PurchaseOrderLineId                                                    
  from dbo.vwPurchaseOrder vw                                                       
  Inner Join tbPurchaseOrderLine Pol on vw.PurchaseOrderId=pol.PurchaseOrderNo                                                    
        LEFT JOIN tbCompanyType ct                                                      
        ON vw.TypeNo  = ct.CompanyTypeId                                                                                 
    WHERE vw.PurchaseOrderId = @PurchaseOrderNo                                                     
                                                    
  /** PROC For Adding GI Header**/                                                    
 EXEC usp_insert_GoodsIn                                                     
    @ClientNo                                                     
  , @ShipViaNo                                                       
  , @AirWayBill                                                       
  , @Reference      --(Advise Note)                                                      
  , @CompanyNo                                                      
  , @ReceivingNotes   ---Notes To Warehouse                                                      
  , @DateReceived       --currentdate                                                      
  , @UpdatedBy                                                     
  , @PurchaseOrderNo                                                      
  , @CustomerRMANo                                                       
  , @WarehouseNo                                                         
  , @CurrencyNo                                                         
  , @UpdatedBy                                                        
  , @PurchaseCountryNo                                                     
  , @GoodsInId =@GoodsInId OUTPUT                                              
                                                    
  /*************** END CODE********************/                                                     
     DECLARE @IPOLandedCost FLOAT =0;  
  DECLARE @CUrrencyNoData INT=0;  
  SELECT @BuyCurrency=CurrencyNo FROM tbInternalPurchaseOrder WHERE PurchaseOrderNo=@PurchaseOrderNo  
  SELECT @CUrrencyNoData=CurrencyNo FROm tbClient WHERE ClientId=@ClientNo  
 SET @IPOLandedCost= dbo.ufn_convert_currency_value(@IpoPrice,@BuyCurrency,@CUrrencyNoData,GETDATE())                                               
  /**Second Part for GI Lines -- Start CODE **/                                                     
 Declare @Part NVARCHAR(30),@ManufacturerNo INT,@DateCode NVARCHAR(5),@PackageNo INT,@Quantity INT,@Price FLOAT,@ShipInCost FLOAT                                                    
         ,@QualityControlNotes NVARCHAR(MAX)                                                 
   ,@ProductNo INT                                      
   ,@ROHS TINYINT,@CountryOfManufacture INT = NULL,@ClientPrice FLOAT                                                     
   /************** Parameters which are filled on UI Screen here passed as NULL************************/                                                    
   ,@LotNo INT=NULL,@CustomerRMALineNo INT=NULL,@SupplierPart NVARCHAR(30) = NULL                                                    
   ,@Unavailable BIT =NULL,@Notes NVARCHAR(128) = NULL, @ChangedFields NVARCHAR(MAX) = NULL                                                     
   ,@CountingMethodNo INT=NULL  ,@SerialNosRecorded BIT=NULL ,@PartMarkings NVARCHAR(50) = NULL                                          
   ,@ReqSerialNo BIT  = 0                                                     
    /**************************************/                                                    
   ,@GoodsInLineId  INT                                                    
   ,@strMessage nvarchar(300),                                      
   @PreviousDLUP varchar(100)=getutcdate();                                      
                                    
        /******** LOOP START***************/                                             
                                
   DECLARE @Count INT=0,@TotalCount INT=0;                       
   SET @Count=1;                      
    SELECT @TotalCount= COUNT(PurchaseOrderLineid) from dbo.tbPurchaseOrderLine pol                                                                                                   
    WHERE pol.PurchaseOrderNo = @PurchaseOrderNo ;                       
  SELECT  ROW_NUMBER() OVER (ORDER BY PurchaseOrderlineId) RowNo, PurchaseOrderlineId                      
       INTO #Temp    FROM  tbPurchaseOrderLine   WHERE PurchaseOrderNo = @PurchaseOrderNo                         
                       
WHILE ( @Count <= @TotalCount)                      
BEGIN                      
                        
    SELECT @PurchaseOrderLineNo= PurchaseOrderlineId FROM  #Temp  where RowNo=@Count;                      
                                                    
  select @Part=Part,@ManufacturerNo=ManufacturerNo,@DateCode=DateCode,@PackageNo=PackageNo, @Quantity=Quantity,@Price=Price,                                                    
          @ShipInCost=ShipInCost,@QualityControlNotes=ReceivingNotes,@ProductNo=ProductNo,@CurrencyNo=CurrencyNo,                                                    
          @Rohs=ROHS,@CountryOfManufacture=Null,@ClientPrice=ClientPrice                                                    
  from vwPurchaseOrderLine   where PurchaseOrderLineId =@PurchaseOrderLineNo                                                    
                                       
                                                    
  /** PROC For Adding GI Lines**/                                                    
   EXEC usp_insert_GoodsInLine_HUBStock                                                 
   @GoodsInId                                                                                              
  ,@PurchaseOrderLineNo                                                                                             
  ,@Part      -- Part No (Not Full Part)                                                                                        
  ,@ManufacturerNo                                                                                            
  ,@DateCode                                                                                          
  ,@PackageNo                                                                 
  ,@Quantity                                                                                      
  ,@Price                                                                                          
  ,@ShipInCost                                                                                              
  ,@QualityControlNotes --  ReceivingNotes                                                                               
  ,@Location -- screen filled                                                                
  ,@LotNo  -- screen filled                                                                                               
  ,@ProductNo                                                                                               
  ,@CurrencyNo                                                                             
  ,@CustomerRMALineNo  --   screen filled                                                                                            
  ,@SupplierPart  ---screen filled                                                                                        
  ,@ROHS                                                                                
  ,@CountryOfManufacture                                                                                               
  ,@Unavailable   ---- screen filled                                                
  ,@Notes            ---- screen filled                        
  ,@ChangedFields  --Location||Lot||CountryOfManufacture||QuarantineThisItem||CountingMethod||SerialNosRecorded||PartMarkings||LandedCost||Division                                                                              
  ,@CountingMethodNo                                                                                            
  ,@SerialNosRecorded                                                  
  ,@PartMarkings   -- ---- screen filled                                                                                            
  ,@UpdatedBy                                                                     
  ,@ClientPrice                                                        
  ,@ReqSerialNo                                                       
  ,@GoodsInLineId  =@GoodsInLineId output                                                                   
  ,@strMessage   =@strMessage output                                         
                                        
    
    /*********Condition to Enable Release Button GI line *************/                              
    UPDATE dbo.tbGoodsInLine SET  PhysicalInspectedBy=@UpdatedBy,DatePhysicalInspected=GETDATE() WHERE GoodsInLineId=@GoodsInLineId;                               
    /***********************/               
             
 SELECT @ClientLandedCost= ClientLandedCost FROM dbo.tbGoodsInLine WHERE  GoodsInLineId=@GoodsInLineId;             
           
   UPDATE dbo.tbstock SET LandedCost=@ClientLandedCost,            
     ResalePrice=IIF(ISNULL(ResalePrice,0)=0,@ClientLandedCost,ResalePrice)            
    WHERE PurchaseOrderNo=@PurchaseOrderNo AND PurchaseOrderLineNo=@PurchaseOrderLineNo            
          
           
     DECLARE  @ChildStockId INT=0          
  SELECT @ChildStockId =StockId from dbo.tbstock WHERE PurchaseOrderNo=@PurchaseOrderNo AND PurchaseOrderLineNo=@PurchaseOrderLineNo                 
   EXEC usp_insert_StockLog  --                                  
                      @StockLogTypeNo = 11 --add from po                                  
                    , @StockNo = @ChildStockId --                                  
                    , @QuantityInStock = 0 --                                  
                    , @QuantityOnOrder = @Qnty_SalesOrder --                                  
                    , @ActionQuantity = @Qnty_SalesOrder --                                  
                    , @PurchaseOrderNo = @PurchaseOrderNo --                                  
                    , @UpdatedBy = @UpdatedBy --                                  
                    , @Detail = 'LandedCost||ResalePrice||PurchasePrice'                                
                    , @StockLogId = 0              
            
  
  
----------------------  
---HUbStock code start     
UPDATE tbGoodsInLine SET ShipInCost=0 WHERE GoodsInLineId=@GoodsInLineId  
IF(@HubGoodsInLineNo is not null)   
begin UPDATE gilNew SET gilNew.FullPart=gilOld.FullPart, gilNew.Part=gilOld.Part, gilNew.ManufacturerNo=gilOld.ManufacturerNo, gilNew.DateCode=gilOld.DateCode, gilNew.PackageNo=gilOld.PackageNo, gilNew.QualityControlNotes=gilOld.QualityControlNotes, gilN
ew.[Location]=gilOld.[Location], gilNew.ProductNo=gilOld.ProductNo, gilNew.LandedCost=gilOld.LandedCost, gilNew.CustomerRMALineNo=gilOld.CustomerRMALineNo, gilNew.SupplierPart=gilOld.SupplierPart, gilNew.ROHS=gilOld.ROHS, gilNew.CountryOfManufacture=gilOl
d.CountryOfManufacture, gilNew.SerialNosRecorded=gilOld.SerialNosRecorded, gilNew.Unavailable=gilOld.Unavailable, gilNew.LotNo=gilOld.LotNo, gilNew.CountingMethodNo=gilOld.CountingMethodNo, gilNew.PartMarkings=gilOld.PartMarkings, gilNew.Notes=gilOld.Note
s, gilNew.FullSupplierPart=gilOld.FullSupplierPart, gilNew.NPRPrinted=gilOld.NPRPrinted, gilNew.PODeliveryDate=gilOld.PODeliveryDate, gilNew.POPromiseDate=gilOld.POPromiseDate, gilNew.ClientLandedCost=ISNULL(gilOld.ClientLandedCost,@IPOLandedCost), gilNew
.IsInvoiceCreated=gilOld.IsInvoiceCreated, gilNew.ClientPrice=ISNULL(gilOld.ClientPrice,@IPOPrice), gilNew.POBankFee=gilOld.POBankFee, gilNew.LinkMultiCurrencyNo=gilOld.LinkMultiCurrencyNo, gilNew.ReqSerialNo=gilOld.ReqSerialNo, gilNew.SerialNoCount=gilOl
d.SerialNoCount, gilNew.MSLLevel=gilOld.MSLLevel, gilNew.PrintHazardous=gilOld.PrintHazardous, gilNew.ParentGILineNo=gilOld.ParentGILineNo, gilNew.CountryOfOriginNo=gilOld.CountryOfOriginNo, gilNew.LifeCycleStage=gilOld.LifeCycleStage, gilNew.HTSCode=gilO
ld.HTSCode, gilNew.AveragePrice=gilOld.AveragePrice, gilNew.Packing=gilOld.Packing, gilNew.PackagingSize=gilOld.PackagingSize, gilNew.Descriptions=gilOld.Descriptions, gilNew.IHSProduct=gilOld.IHSProduct, gilNew.RefIdHK=gilOld.RefIdHK, gilNew.NewRecord=gi
lOld.NewRecord, gilNew.ECCNCode=gilOld.ECCNCode, gilNew.IsPartNoCorrect=gilOld.IsPartNoCorrect, gilNew.CorrectPartNo=gilOld.CorrectPartNo, gilNew.IsManufacturerCorrect=gilOld.IsManufacturerCorrect, gilNew.CorrectManufacturer=gilOld.CorrectManufacturer, gi
lNew.IsDateCodeCorrect=gilOld.IsDateCodeCorrect, gilNew.CorrectDateCode=gilOld.CorrectDateCode, gilNew.IsDateCodeRequired=gilOld.IsDateCodeRequired, gilNew.IsPackageTypeCorrect=gilOld.IsPackageTypeCorrect, gilNew.CorrectPackageType=gilOld.CorrectPackageTy
pe, gilNew.IsMSLLevelCorrect=gilOld.IsMSLLevelCorrect, gilNew.CorrectMSLLevel=gilOld.CorrectMSLLevel, gilNew.HICStatus=gilOld.HICStatus, gilNew.IsHICStatusCorrect=gilOld.IsHICStatusCorrect, gilNew.CorrectHICStatus=gilOld.CorrectHICStatus, gilNew.PKGBreakd
ownMismatch=gilOld.PKGBreakdownMismatch, gilNew.IsROHSStatusCorrect=gilOld.IsROHSStatusCorrect, gilNew.CorrectROHSStatus=gilOld.CorrectROHSStatus, gilNew.IsLotCodesReq=gilOld.IsLotCodesReq, gilNew.BakingLevelAdded=gilOld.BakingLevelAdded, gilNew.EnhancedI
nspectionReq=gilOld.EnhancedInspectionReq, gilNew.GeneralInspectionNotes=gilOld.GeneralInspectionNotes, gilNew.IsInspectionConducted=gilOld.IsInspectionConducted, gilNew.IsPDFReportRequired=gilOld.IsPDFReportRequired, gilNew.IsPDFAvailable=gilOld.IsPDFAva
ilable, gilNew.LotNoCount=gilOld.LotNoCount, gilNew.ActeoneTestStatus=gilOld.ActeoneTestStatus, gilNew.IsopropryleStatus=gilOld.IsopropryleStatus, gilNew.ActeoneTest=gilOld.ActeoneTest, gilNew.Isopropryle=gilOld.Isopropryle, gilNew.QueryBakeLevel=gilOld.Q
ueryBakeLevel, gilNew.EnhInpectionReqId=gilOld.EnhInpectionReqId, gilNew.PrintableDC=gilOld.PrintableDC, gilNew.HasBarCodeScan=gilOld.HasBarCodeScan, gilNew.BarCodeScanRemarks=gilOld.BarCodeScanRemarks, gilNew.PartNoQuery=gilOld.PartNoQuery, gilNew.Manufa
cturerQuery=gilOld.ManufacturerQuery, gilNew.PackagingTypeQuery=gilOld.PackagingTypeQuery, gilNew.MslQuery=gilOld.MslQuery, gilNew.RohsQuery=gilOld.RohsQuery, gilNew.ISNewGI=gilOld.ISNewGI, gilNew.AS6081=gilOld.AS6081 FROM tbGoodsInLine gilNew INNER JOIN 
tbGoodsInLine gilOld   ON  gilNew.GoodsInLineId=@GoodsInLineId AND gilOld.GoodsInLineId=@HubGoodsInLineNo  
  
-----------------------------------------                    
--PDF copy  
  INSERT  INTO dbo.tbStockPDF (                  
            StockNo              
          , Caption        
          , [FileName]               
          , UpdatedBy                
          , DLUP     
          )                  
        select                    
            @ChildStockId              
          , Caption        
          , [FileName]               
          , UpdatedBy                
          , CURRENT_TIMESTAMP                  
            FROM    dbo.tbStockPDF  WHERE   StockNo = @HubGIStockNO    
  
--image copy  
declare @GIStockNo int  
  SELECT  @GIStockNo = st.StockId       
            FROM    dbo.tbStock st                                        
            JOIN    tbGoodsInLine gil ON gil.PurchaseOrderLineNo = st.PurchaseOrderLineNo                                        
                                         AND gil.GoodsInLineId = st.GoodsInLineNo                                        
            WHERE   GoodsInLineId = @HubGoodsInLineNo      
    IF(@GIStockNo>0)    
 BEGIN    
    INSERT  INTO dbo.tbStockImage (                
            StockNo                
          , Caption                
          , ImageName                
          , UpdatedBy                
          , DLUP )                
   select                
              @ChildStockId                
            , Caption                
            , ImageName                
            , @UpdatedBy                
            , CURRENT_TIMESTAMP                
             from    tbStockImage where stockno= @GIStockNo             
      
 END           
   
end  
--------------------------  
  
  
                       
 SET @Count  = @Count  + 1                      
  
  
    
END     
  
  
EXEC [usp_SendMailNotificationToCustomer_IPO] @PurchaseOrderNo                
   /*                              
   SELECT @GoodsInLineId as '@GoodsInLineId';                              
  select top 1 GoodsInLineId from dbo.tbGoodsInLine where GoodsInLineId=@GoodsInLineId;                              
   UPDATE dbo.tbStock SET                                   
   GoodsInLineNo=(select top 1 GoodsInLineId from dbo.tbGoodsInLine where GoodsInNo= @GoodsInId) ,                                  
   -- GoodsInLineNo=@GoodsInLineId ,                                  
   PurchaseOrderNo=@PurchaseOrderNo,                                  
   PurchaseOrderLineNo=@PurchaseOrderLineNo                                  
   WHERE StockId=@StockID                               
 */                              
     /*************************/                               
  /*                              
  IF(@Qnty_Stock>@Qnty_SalesOrder and @Qnty_Stock>0 and @Qnty_SalesOrder>0 and 1=2 )                                      
  BEGIN                                      
  SET @Quantity =@Qnty_Stock-@Qnty_SalesOrder                    
    SELECT                                     
 @PreviousDLUP= CONVERT(VARCHAR(23),gil.DLUP,121)                                     
          FROM tbGoodsIn gi                                          
        JOIN tbGoodsInLine gil ON gi.GoodsInId = gil.GoodsInNo                                          
    WHERE  gil.GoodsInLineId = @GoodsInLineId                                      
                                      
  /* Proc for Split Stock**/                                      
 EXEC usp_split_GoodsInLine_DEMO                                    
     @GoodsInLineId    =  @GoodsInLineId--768782                                                                    
    ,@Part   = @Part                                                                     
    ,@ManufacturerNo  =  @ManufacturerNo--6019                                                                       
    ,@DateCode =  @DateCode--'20+'                                                                       
    ,@PackageNo  = @PackageNo--3                                                                       
    ,@Quantity   =@Quantity                                                                     
    ,@Price  =0                                                                      
    ,@ShipInCost  = 0                                        
    ,@QualityControlNotes  =  @QualityControlNotes--'Instruction to WH & Quality control'                      
    ,@Location  = @Location                                                                       
    ,@LotNo  = @LotNo                                                                       
    ,@ProductNo  = @ProductNo                                                                       
    ,@SupplierPart  = @SupplierPart--'SUPPLIER PART NO'                                                                  
    ,@ROHS    = @ROHS                                                         
    ,@CountryOfManufacture  = @CountryOfManufacture--190                                                                       
    ,@CurrencyNo   = @CurrencyNo----1                                                                     
    ,@Unavailable     =0                                                                   
    ,@ChangedFields  = NULL                                                                       
    ,@Notes  = @Notes-- 'SPLITT BY DEVNEDRA gi=616991'                                                            
    ,@CountingMethodNo     =2                                                                   
    ,@SerialNosRecorded      =0                                                                  
    ,@PartMarkings  = 'Part Markings'                            
    ,@UpdateStock  = 0                           
    ,@UpdateShipments  = 0                                                                       
    ,@UpdatedBy  = @UpdatedBy                                                             
    ,@ClientPrice   = 0                                                        
    ,@ReqSerialNo  = 0                                                                              
    ,@RowsAffected  = 0                                           
    ,@MSLLevel    = 0                                                            
    ,@PrintHazardous  = 0                                      
    ,@PreviousDLUP = @PreviousDLUP  --'2023-09-06 16:18:07.360'                                            
    ,@ErrorMessage =@strMessage                                        END                                
  */                              
  /*************** END CODE********************/                                                   
END 