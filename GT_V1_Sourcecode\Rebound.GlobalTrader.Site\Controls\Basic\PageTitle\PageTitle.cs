//----------------------------------------------------------------------------------------------------
// RP 06.11.2009:
// - added AboveSubTitle template 
// - remove very old and pointless UpdatePanel!
//----------------------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:PageTitle runat=server></{0}:PageTitle>")]
	public class PageTitle : WebControlWithScript, IScriptControl, INamingContainer {

		private Literal _litTitle;
		private Panel _pnlTabs;
		private Panel _pnlBoxInner;
		private Panel _pnlBoxContent;
		private Panel _pnlPageTitle;
		private PlaceHolder _plhTabs;
		private Panel _pnlTabsInner;
		private Panel _pnlCompanyType;
		private Literal _litCompanyType;
		private PlaceHolder _plhSubTitle;
		private Panel _pnlTopRight;
		private PlaceHolder _plhTopRight;
		private Panel _pnlBox;
		private HtmlControl _ctlH3;
		private List<string> _lstTabClientIDs = new List<string>();
		private Panel _pnlAboveTitle;

		#region Properties
		/// <summary>
		/// Title text
		/// </summary>
		private String _strTitleText;
		public String TitleText {
			get { return _strTitleText; }
			set { _strTitleText = value; }
		}

		/// <summary>
		/// Abovetitle container
		/// </summary>
		private ITemplate _tmpAbovetitle = null;
		[TemplateContainer(typeof(Container))]
		[PersistenceMode(PersistenceMode.InnerProperty)]
		public ITemplate AboveTitle {
			get { return _tmpAbovetitle; }
			set { _tmpAbovetitle = value; }
		}

		/// <summary>
		/// Subtitle container
		/// </summary>
		private ITemplate _tmpSubtitle = null;
		[TemplateContainer(typeof(Container))]
		[PersistenceMode(PersistenceMode.InnerProperty)]
		public ITemplate SubTitle {
			get { return _tmpSubtitle; }
			set { _tmpSubtitle = value; }
		}

		/// <summary>
		/// Top right content container
		/// </summary>
		private ITemplate _tmpTopRightContent = null;
		[TemplateContainer(typeof(Container))]
		[PersistenceMode(PersistenceMode.InnerProperty)]
		public ITemplate TopRightContent {
			get { return _tmpTopRightContent; }
			set { _tmpTopRightContent = value; }
		}

		/// <summary>
		/// Index of the selected tab within the tab array (if this has tabs)
		/// </summary>
		private int _intSelectedTabIndex = -1;
		public int SelectedTabIndex {
			get { return _intSelectedTabIndex; }
			set { _intSelectedTabIndex = value; }
		}

		/// <summary>
		/// Array of tabs
		/// </summary>
		private List<Controls.PageTitleTab> _lstTabs = new List<Controls.PageTitleTab>();
		public List<Controls.PageTitleTab> Tabs {
			get { return _lstTabs; }
			set { _lstTabs = value; }
		}

		/// <summary>
		/// Company type
		/// </summary>
		private String _strCompanyType = "";
		public String CompanyType {
			get { return _strCompanyType; }
			set { _strCompanyType = value; }
		}
		#endregion

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("PageTitle.css");
			((Pages.Base)Page).AddCSSFile("Nuggets.css");
			EnsureChildControls();
			_litCompanyType.Text = _strCompanyType;
			_pnlCompanyType.Visible = (_strCompanyType != "");
			_litTitle.Text = _strTitleText;
			RenderTabs();
			base.OnLoad(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		/// <param name="e"></param>
		protected override void CreateChildControls() {
			//outer stuff
			_pnlBox = ControlBuilders.CreatePanelInsideParent(this, "box boxTitleBar");
			_pnlBoxInner = ControlBuilders.CreatePanelInsideParent(_pnlBox);

			//curve stuff
			_pnlBoxInner.Controls.Add(ControlBuilders.CreatePanel("boxTL"));
			_pnlBoxInner.Controls.Add(ControlBuilders.CreatePanel("boxTR"));

			//content box
			_pnlBoxContent = ControlBuilders.CreatePanelInsideParent(_pnlBoxInner, "boxContent");

			//above title
			_pnlAboveTitle = ControlBuilders.CreatePanelInsideParent(_pnlBoxContent, "abovePageTitle");

			//page title div
			_pnlPageTitle = ControlBuilders.CreatePanelInsideParent(_pnlBoxContent, "pageTitle");
			_ctlH3 = ControlBuilders.CreateHtmlGenericControlInsideParent(_pnlPageTitle, "h3");
			_ctlH3.ID = "h3";
			_litTitle = ControlBuilders.CreateLiteralInsideParent(_ctlH3);
			_pnlCompanyType = ControlBuilders.CreatePanelInsideParent(_pnlPageTitle, "companyType");
			_litCompanyType = ControlBuilders.CreateLiteralInsideParent(_pnlPageTitle);

			//subtitle
			_plhSubTitle = ControlBuilders.CreatePlaceHolderInsideParent(_pnlBoxContent);

			//top right
			_pnlTopRight = ControlBuilders.CreatePanelInsideParent(_pnlBoxContent, "pageTitleTopRight");
			_plhTopRight = ControlBuilders.CreatePlaceHolderInsideParent(_pnlTopRight);
			ControlBuilders.CreatePanelInsideParent(_pnlTopRight, "clearing");

			//tabs
			_pnlTabs = ControlBuilders.CreatePanelInsideParent(_pnlBoxContent, "pageTitleTabs");
			_pnlTabsInner = ControlBuilders.CreatePanelInsideParent(_pnlTabs);
			_plhTabs = ControlBuilders.CreatePlaceHolderInsideParent(_pnlTabsInner);

			//curve stuff
			_pnlBoxInner.Controls.Add(ControlBuilders.CreatePanel("boxBL"));
			_pnlBoxInner.Controls.Add(ControlBuilders.CreatePanel("boxBR"));

			PopulateContainers();
			base.CreateChildControls();
		}

		/// <summary>
		/// Populate containers
		/// </summary>
		private void PopulateContainers() {
			if (_tmpSubtitle != null) Functions.AddControlsFromTemplate(_plhSubTitle, _tmpSubtitle);
			if (_tmpTopRightContent != null) Functions.AddControlsFromTemplate(_plhTopRight, _tmpTopRightContent);
			if (_tmpAbovetitle != null) Functions.AddControlsFromTemplate(_pnlAboveTitle, _tmpAbovetitle);
			_pnlTopRight.Visible = (_tmpTopRightContent != null);
			_pnlAboveTitle.Visible = (_tmpAbovetitle != null);
		}

		/// <summary>
		/// Find a control in the sub title container
		/// </summary>
		/// <param name="strControlName"></param>
		public Control FindSubTitleControl(string strControlName) {
			EnsureChildControls();
			return (Control)Functions.FindControlRecursive(_plhSubTitle.Controls[0], strControlName);
		}

		/// <summary>
		/// Find a control in the Top Right Content container
		/// </summary>
		/// <param name="strControlName"></param>
		public Control FindTopRightControl(string strControlName) {
			EnsureChildControls();
			return (Control)Functions.FindControlRecursive(_plhTopRight.Controls[0], strControlName);
		}

		/// <summary>
		/// Add a tab to the tabs array
		/// </summary>
		/// <param name="strTitle">Title</param>
		/// <param name="strHref">Link href</param>
		public void AddTab(string strTitle, string strHref, bool blnIsInitiallyInvisible, string strOnClick) {
			Rebound.GlobalTrader.Site.Controls.PageTitleTab tab = new Rebound.GlobalTrader.Site.Controls.PageTitleTab();
			tab.Title = strTitle;
			tab.Href = strHref;
			tab.IsInitiallyInvisible = blnIsInitiallyInvisible;
			tab.OnClientClick = strOnClick;
			_lstTabs.Add(tab);
			tab.ID = string.Format("tab{0}", _lstTabs.Count);
		}
		/// <summary>
		/// Add a tab to the tabs array
		/// </summary>
		/// <param name="strTitle">Title</param>
		/// <param name="strHref">Link href</param>
		public void AddTab(string strTitle, string strHref) {
			AddTab(strTitle, strHref, false, "");
		}
		/// <summary>
		/// Add a tab to the tabs array
		/// </summary>
		/// <param name="strTitle">Title</param>
		/// <param name="strHref">Link href</param>
		public void AddTab(string strTitle, string strHref, string strOnClick) {
			AddTab(strTitle, strHref, false, strOnClick);
		}

		/// <summary>
		/// Display the tabs
		/// </summary>
		private void RenderTabs() {
			if (_lstTabs.Count > 0) {
				_pnlTabs.Visible = true;
				for (int i = 0; i < _lstTabs.Count; i++) {
					PageTitleTab tab = _lstTabs[i];
					//tab.Title = ((Rebound.GlobalTrader.Site.Controls.PageTitleTab)_lstTabs[i]).Title;
					//tab.Href = ((Rebound.GlobalTrader.Site.Controls.PageTitleTab)_lstTabs[i]).Href;
					tab.IsSelected = (i == _intSelectedTabIndex);
					tab.PositionOffset = i;
					_plhTabs.Controls.Add(tab);
					_lstTabClientIDs.Add(tab.ClientID);
				}
				_pnlTabsInner.Attributes.Add("style", string.Format("position:relative; left:{0}px;", _lstTabs.Count - 1));
			} else {
				_pnlTabs.Visible = false;
			}
		}

		public void SetSelectedTab(int intTab) {
			_intSelectedTabIndex = intTab;
			for (int i = 0; i < _lstTabs.Count; i++) {
				_lstTabs[i].IsSelected = (i == _intSelectedTabIndex);
			}
		}

		/// <summary>
		/// Sets a client ID from within the Page Title Sub Title template
		/// </summary>
		/// <param name="strControlName"></param>
		public void SetClientIDForControlInPageSubTitle(Control ctlCalling, string strControlName) {
			Literal lit = (Literal)Functions.FindControlRecursive(ctlCalling, string.Format("litClientID_{0}", strControlName));
			Control ctl = FindSubTitleControl(strControlName);
			if (lit != null && ctl != null) lit.Text = ctl.ClientID;
		}

		#region IScriptControl Members

		protected new virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.PageTitle.PageTitle", true) };
		}

		protected new virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.PageTitle", this.ClientID);
			descriptor.AddElementProperty("ctlH3", _ctlH3.ClientID);
			descriptor.AddProperty("aryTabIDs", _lstTabClientIDs);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}