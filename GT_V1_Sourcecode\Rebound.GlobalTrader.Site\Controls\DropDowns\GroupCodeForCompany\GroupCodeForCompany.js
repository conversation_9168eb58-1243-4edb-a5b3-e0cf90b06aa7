Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.GroupCodeForCompany=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.GroupCodeForCompany.initializeBase(this,[n]);this._intCompanyID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.GroupCodeForCompany.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.GroupCodeForCompany.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.DropDowns.GroupCodeForCompany.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/GroupCodeForCompany");this._objData.set_DataObject("GroupCodeForCompany");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.Items)for(n=0;n<t.Items.length;n++)this.addOption(t.Items[n].Name,t.Items[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.GroupCodeForCompany.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GroupCodeForCompany",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);