//-----------------------------------------------------------------------------------------
// RP 21.12.2009:
// - new control
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.ComponentModel;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Text;
using System.Collections.Generic;


namespace Rebound.GlobalTrader.Site.Controls {

	/// <summary>
	/// A Column object
	/// </summary>
	[Serializable]
	public class SimpleDataColumn : TableHeaderCell, INamingContainer {


		#region Properties

        public string Text1 { get; set; }
        public string Text2 { get; set; }

        #endregion

		#region Constructors

		/// <summary>
		/// A column object
		/// </summary>
		public SimpleDataColumn() { }
		public SimpleDataColumn(string strText) {
			SetText(strText);
			Width = Unit.Empty;
			HorizontalAlign = HorizontalAlign.Left;
		}
		public SimpleDataColumn(string strText1, string strText2) {
			SetText(strText1, strText2);
			Width = Unit.Empty;
			HorizontalAlign = HorizontalAlign.Left;
		}
		public SimpleDataColumn(string strText, Unit untWidth) {
			SetText(strText);
			Width = untWidth;
			HorizontalAlign = HorizontalAlign.Left;
		}
		public SimpleDataColumn(string strText1, string strText2, Unit untWidth) {
			SetText(strText1, strText2);
			Width = untWidth;
			HorizontalAlign = HorizontalAlign.Left;
		}
		public SimpleDataColumn(string strText, HorizontalAlign enmAlign) {
			SetText(strText);
			Width = Unit.Empty;
			HorizontalAlign = enmAlign;
		}
		public SimpleDataColumn(string strText, Unit untWidth, HorizontalAlign enmAlign) {
			SetText(strText);
			Width = untWidth;
			HorizontalAlign = enmAlign;
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			switch (HorizontalAlign) {
				case HorizontalAlign.Right: CssClass += " alignRight"; break;
				case HorizontalAlign.Center: CssClass += " alignCenter"; break;
				case HorizontalAlign.Justify: CssClass += " alignJustify"; break;
			}
			HorizontalAlign = HorizontalAlign.NotSet; //reset to allow css to handle the display
			base.Render(writer);
		}

		#endregion

		#region Methods

		private void SetText(string strText) {
			Text1 = strText;
			Text = Functions.GetGlobalResource("TableColumnHeadings", strText);
		}
		private void SetText(string strText1, string strText2) {
			Text1 = strText1;
			Text2 = strText2;
			Text = (String.Format("<div class=\"doubleHeaderTop\">{0}</div>{1}", Functions.GetGlobalResource("TableColumnHeadings", strText1), Functions.GetGlobalResource("TableColumnHeadings", strText2)));
		}

		#endregion

	}

}