﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="c:\program files (x86)\microsoft visual studio\2019\professional\team tools\static analysis tools\fxcop\Xml\CodeAnalysisReport.xsl"?>
<FxCopReport Version="16.0">
 <Targets>
  <Target Name="C:\WORKSPACE\Software\ajaxmin-master\AjaxMinTask\bin\Debug\AjaxMinTask.dll">
   <Modules>
    <Module Name="ajaxmintask.dll">
     <Messages>
      <Message TypeName="AssembliesShouldHaveValidStrongNames" Category="Microsoft.Design" CheckId="CA2210" Status="Active" Created="2024-01-17 02:18:33Z" FixCategory="NonBreaking">
       <Issue Name="NoStrongName" Certainty="95" Level="CriticalError">Sign 'AjaxMinTask.dll' with a strong name key.</Issue>
      </Message>
     </Messages>
    </Module>
   </Modules>
  </Target>
 </Targets>
 <Rules>
  <Rule TypeName="AssembliesShouldHaveValidStrongNames" Category="Microsoft.Design" CheckId="CA2210">
   <Name>Assemblies should have valid strong names</Name>
   <Description>Either the assembly has no strong name, an invalid one, or the strong name is valid only because of the computer configuration. The assembly should not be deployed in this state. The most common causes of this are: 1) The assembly's contents were modified after it was signed. 2) The signing process failed. 3) The assembly was delay-signed. 4) A registry key existed that allowed the check to pass (where it would not have otherwise).</Description>
   <Resolution Name="NoStrongName">Sign {0} with a strong name key.</Resolution>
   <Owner />
   <Url>http://msdn.microsoft.com/library/ms182127.aspx</Url>
   <Email>[none]</Email>
   <MessageLevel Certainty="95">CriticalError</MessageLevel>
   <File Name="designrules.dll" Version="********" />
  </Rule>
 </Rules>
 <Localized>
  <String Key="Category">Category</String>
  <String Key="Certainty">Certainty</String>
  <String Key="CollapseAll">Collapse All</String>
  <String Key="CheckId">Check Id</String>
  <String Key="Error">Error</String>
  <String Key="Errors">error(s)</String>
  <String Key="ExpandAll">Expand All</String>
  <String Key="Help">Help</String>
  <String Key="Line">Line</String>
  <String Key="Messages">message(s)</String>
  <String Key="LocationNotStoredInPdb">[Location not stored in Pdb]</String>
  <String Key="Project">Project</String>
  <String Key="Resolution">Resolution</String>
  <String Key="Rule">Rule</String>
  <String Key="RuleFile">Rule File</String>
  <String Key="RuleDescription">Rule Description</String>
  <String Key="Source">Source</String>
  <String Key="Status">Status</String>
  <String Key="Target">Target</String>
  <String Key="Warning">Warning</String>
  <String Key="Warnings">warning(s)</String>
  <String Key="ReportTitle">Code Analysis Report</String>
 </Localized>
</FxCopReport>
