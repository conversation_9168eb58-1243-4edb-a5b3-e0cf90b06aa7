﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{

    public abstract class ShortShipmentProvider : DataAccess
    {
        static private ShortShipmentProvider _instance = null;
        /// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public ShortShipmentProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (ShortShipmentProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.ShortShipments.ProviderType));
                return _instance;
            }
        }
        public ShortShipmentProvider()
        {
            this.ConnectionString = Globals.Settings.ShortShipments.ConnectionString;
        }
        #region Method Registrations
        /// <summary>
        /// ShortShipment
        /// Calls [usp_report_NPR]
        /// </summary>
        public abstract ShortShipmentDetails GetShortShipmentById(System.Int32 ShortShipmentId);

        /// <summary>
        /// Insert
        /// Calls [usp_insert_ShortShipment]
        /// </summary>
        //[0001] start
        public abstract Int32 Insert(System.Int32? Supplier, System.Int32? PurchaseOrderNo, System.Int32? Salesman, System.String Reference, System.Int32? GoodsInNo, System.DateTime? DateReceived, System.Int32? Raisedby, System.Int32? Buyer, System.String PartNo, System.Int32? ManufacturerNo, System.Int32? QuantityOrdered, System.Int32? QuantityAdvised, System.Int32? QuantityReceived, System.Int32? ShortageQuantity, System.Int32? ShortValue, System.Int32? Status);
        /// <summary>
        ////[0001] end
        /// Insert
        /// Calls [usp_update_ShortShipment]
        /// </summary>
        /// //[0001] start
        public abstract bool UpdateShortShipment(System.Int32 ShortShipmentId, System.Boolean IsShortageRefundIssue, System.String ShortageRefundIssue, System.Int32 loginId, System.Int32? clientId, ref string message, ref int result, System.Int32? ShortShipmentStatusId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail);
        /// <summary>
        /// //[0001] end
        /// Get NPR by Id
        /// calls [usp_select_NPRbyId]
        /// </summary>
        /// <param name="nprId"></param>
        /// <returns></returns>
        //public abstract ShortShipmentDetails Get(System.Int32 ShortShipmentId);

        /// <summary>
        /// Delete NPR by Id
        /// Calls [usp_delete_nprReport]
        /// </summary>
        /// <param name="nprId"></param>
        /// <returns></returns>
        public abstract bool Delete(System.Int32? ShortShipmentId);

        /// <summary>
        /// usp_select_NPRLog
        /// </summary>
        /// <param name="nprId"></param>
        /// <returns></returns>
        //public abstract List<ShortShipmentDetails> GetNPRLog(System.Int32? nprId);
        /// <summary>
        /// usp_select_NPR_CompanyID
        /// </summary>
        /// <param name="nprId"></param>
        /// <returns></returns>
        //public abstract int? getNPRCompanyID(System.Int32? SalesorderNo, System.Int32? ClientNo);
        /// <summary>
        /// Insert NPR Email Log
        /// Call [usp_insert_Email_NPR_Log]
        /// </summary>
        /// <param name="nprId"></param>
        /// <param name="details"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        //public abstract Int32 InsertEmailNPRLog(System.Int32? nprId, System.String details, System.Int32? updatedBy);

        // <summary>
        /// calls[usp_datalistnugget_ShortShipment]
        /// </summary>
        /// <returns></returns>
        public abstract List<ShortShipmentDetails> DataListNugget(System.Int32? ClientID, System.Int32? OrderBy, System.Int32? SortDir, System.Int32? PageIndex, System.Int32? PageSize, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrderNoHi, System.DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? Status, System.Int32? GINumberLo, System.Int32? GINumberHi);

        public abstract List<ShortShipmentDetails> DropDown(System.Int32? IsPartialShortShipmentStatus, System.String strquery, System.Int32? LoginId,  System.Int32? ClientId);
        // <summary>
        /// calls[usp_insert_Email_ShortShipment_Log]
        /// </summary>
        /// <returns></returns>
        public abstract Int32 InsertEmailShortShipmentLog(System.Int32? shortShipmentId, System.String strShortShipmentEmailLog, System.Int32? loginID);
        /// <summary>
        /// Calls [usp_datalistnugget_ShortShipment]
        /// </summary>
        /// <param name="ClientID"></param>
        /// <param name="OrderBy"></param>
        /// <param name="SortDir"></param>
        /// <param name="PageIndex"></param>
        /// <param name="PageSize"></param>
        /// <param name="Supplier"></param>
        /// <param name="PurchaseOrderNoLo"></param>
        /// <param name="PurchaseOrderNoHi"></param>
        /// <param name="DateReceived"></param>
        /// <param name="QuantityOrderedLo"></param>
        /// <param name="QuantityOrderedHi"></param>
        /// <param name="QuantityReceivedLo"></param>
        /// <param name="QuantityReceivedHi"></param>
        /// <param name="ShortageQuantityLo"></param>
        /// <param name="ShortageQuantityHi"></param>
        /// <param name="ShortageValueLo"></param>
        /// <param name="ShortageValueHi"></param>
        /// <param name="IsShortageRefundIssue"></param>
        /// <param name="Status"></param>
        /// <param name="GINumberLo"></param>
        /// <param name="GINumberHi"></param>
        /// <param name="Buyer"></param>
        /// <returns></returns>
        public abstract List<ShortShipmentDetails> DataListNugget(System.Int32? ClientID, System.Int32? OrderBy, System.Int32? SortDir, System.Int32? PageIndex, System.Int32? PageSize, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrderNoHi, System.DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? Status, System.Int32? GINumberLo, System.Int32? GINumberHi, System.Int32? Buyer, System.Boolean IsRecentOnly);
        /// <summary>
        /// Calls [usp_datalistnugget_ShortShipment_Export]
        /// </summary>
        /// <param name="clientID"></param>
        /// <param name="SortIndex"></param>
        /// <param name="SortDir"></param>
        /// <param name="Supplier"></param>
        /// <param name="PurchaseOrderNoLo"></param>
        /// <param name="PurchaseOrderNoHi"></param>
        /// <param name="DateReceived"></param>
        /// <param name="QuantityOrderedLo"></param>
        /// <param name="QuantityOrderedHi"></param>
        /// <param name="QuantityReceivedLo"></param>
        /// <param name="QuantityReceivedHi"></param>
        /// <param name="ShortageQuantityLo"></param>
        /// <param name="ShortageQuantityHi"></param>
        /// <param name="ShortageValueLo"></param>
        /// <param name="ShortageValueHi"></param>
        /// <param name="IsShortageRefundIssue"></param>
        /// <param name="ShortShipmentStatus"></param>
        /// <param name="GINumberLo"></param>
        /// <param name="GINumberHi"></param>
        /// <param name="IsPOHub"></param>
        /// <param name="Buyer"></param>
        /// <returns></returns>
        public abstract DataTable DataListNugget_Export(System.Int32? clientID, System.Int32? SortIndex, System.Int32? SortDir, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrderNoHi, DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? ShortShipmentStatus, System.Int32? GINumberLo, System.Int32? GINumberHi, System.Boolean? IsPOHub, System.Int32? Buyer, System.Boolean IsRecentOnly);
        public abstract bool CancelShortShipment(System.Int32? shortShipmentId, System.Int32? loginId, System.Int32? clientId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail);
        public abstract bool CloseShortShipment(System.Int32? shortShipmentId, System.Int32? loginId, System.Int32? clientId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail);
        #endregion
    }
}