using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Threading;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:ToolTip runat=server></{0}:ToolTip>")]
	public class ToolTip : Panel, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected Panel _pnlContent;
		protected Panel _pnlShadow;

		#endregion

		#region Properties

		private int _intMillisecondsWaitBeforeShow = 1000;
		public int MillisecondsWaitBeforeShow {
			get { return _intMillisecondsWaitBeforeShow; }
			set { _intMillisecondsWaitBeforeShow = value; }
		}

		#endregion

		#region Constructors

		public ToolTip() { }

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("ToolTip.css");
			CssClass = "ToolTip invisible";
			base.OnInit(e);
		}

		protected override void CreateChildControls() {
			ControlBuilders.CreateLiteralInsideParent(ControlBuilders.CreatePanelInsideParent(this, "tooltipCallout"), "&nbsp;");
			_pnlShadow = ControlBuilders.CreatePanelInsideParent(this, "tooltipShadow");
			ControlBuilders.CreateLiteralInsideParent(_pnlShadow, "&nbsp;");
			_pnlContent = ControlBuilders.CreatePanelInsideParent(this, "tooltipContent");
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {

			//add script reference
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.ToolTip.ToolTip", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ToolTip", this.ClientID);
			descriptor.AddElementProperty("pnlContent", _pnlContent.ClientID);
			descriptor.AddElementProperty("pnlShadow", _pnlShadow.ClientID);
			descriptor.AddProperty("strLoading", "<div class=\"loading\">&nbsp;</div>");
			descriptor.AddProperty("intMillisecondsWaitBeforeShow", MillisecondsWaitBeforeShow);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}