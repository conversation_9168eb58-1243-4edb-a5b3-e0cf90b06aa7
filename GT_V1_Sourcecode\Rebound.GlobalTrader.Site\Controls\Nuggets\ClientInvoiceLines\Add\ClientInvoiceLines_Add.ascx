<%--
Marker     Changed by      Date               Remarks
[001]      Vinay           13/06/2013         CR:- Client Invoice
--%>
<%@ Control Language="C#" CodeBehind="ClientInvoiceLines_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceLines_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ClientInvoiceLines_Add")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			
			<ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="lblSupplier" IsRequiredField="true" ResourceTitle="Customer">
				<Field><asp:Label ID="lblSupplier" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlSupplierCode" runat="server" FieldID="lblSupplierCode" IsRequiredField="true" ResourceTitle="SupplierCode">
				<Field><asp:Label ID="lblSupplierCode" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlClientInvoice" runat="server" FieldID="lblClientInvoice" IsRequiredField="true" ResourceTitle="ClientInvoice">
				<Field><asp:Label ID="lblClientInvoice" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		     <ReboundUI_Form:FormField id="ctlInvoiceDate" runat="server" FieldID="lblInvoiceDate" ResourceTitle="InvoiceDate" IsRequiredField="true">
	           <Field><asp:Label ID="lblInvoiceDate" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="lblCurrency" ResourceTitle="Currency" IsRequiredField="true">
				<Field><asp:Label ID="lblCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlInvoiceAmount" runat="server" FieldID="lblInvoiceAmount" ResourceTitle="InvoiceAmount">
				<Field><asp:Label ID="lblInvoiceAmount" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlGoodsValue" runat="server" FieldID="lblGoodsValue" ResourceTitle="GoodsValue">
				<Field><asp:Label ID="lblGoodsValue" runat="server" />  &nbsp; &nbsp;<asp:Label ID="lbl_SelectedGI" runat="server"><%=Functions.GetGlobalResource("FormFields", "SelectedGI")%></asp:Label>&nbsp;<ReboundUI:ReboundTextBox ID="txtSelectedGI" runat="server" TextBoxMode="currency" BackColor="Yellow"   DecimalPlaces="5"  Width="150" ReadOnly="true"/></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="lblTax" ResourceTitle="Tax">
				<Field><asp:Label ID="lblTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlDeliveryCharge" runat="server" FieldID="lblDeliveryCharge" ResourceTitle="DeliveryCharge">
				<Field><asp:Label ID="lblDeliveryCharge" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlBankFee" runat="server" FieldID="lblBankFee" ResourceTitle="BankFee">
				<Field><asp:Label ID="lblBankFee" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlCreditCardFee" runat="server" FieldID="lblCreditCardFee" ResourceTitle="CreditCardFee">
				<Field><asp:Label ID="lblCreditCardFee" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlCanExported" runat="server" FieldID="chkCanExported" ResourceTitle="CanBeExported">
				<Field><ReboundUI:ImageCheckBox ID="chkCanExported" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="lblNotes" ResourceTitle="Notes">
				<Field><asp:Label ID="lblNotes" runat="server" /></Field>
			</ReboundUI_Form:FormField>
				
			
			
			<ReboundUI_Form:FormField id="ctlSecondRef" runat="server" FieldID="txtSecondRef" ResourceTitle="SecondRef">
				<Field><ReboundUI:ReboundTextBox ID="txtSecondRef" runat="server"  MaxLength="16"  Width="150" />  <asp:Label ID="lbl_SecondRef" runat="server"><%=Functions.GetGlobalResource("Misc", "SecondRef")%></asp:Label> </Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlNarrative" runat="server" FieldID="txtNarrative" ResourceTitle="Narrative">
				<Field><ReboundUI:ReboundTextBox ID="txtNarrative" runat="server"  MaxLength="41"   Width="150" />   <asp:Label ID="lbl_Narrative" runat="server"><%=Functions.GetGlobalResource("Misc", "Narrative")%></asp:Label> </Field>
			</ReboundUI_Form:FormField>
			
			<%--<ReboundUI_Form:FormField id="ctlSelectedGI" runat="server" FieldID="txtSelectedGI" ResourceTitle="SelectedGI">
				<Field><ReboundUI:ReboundTextBox ID="txtSelectedGI" runat="server" TextBoxMode="currency" DecimalPlaces="2"  Width="150" ReadOnly="true" /> </Field>
			</ReboundUI_Form:FormField>--%>
			
		
			
		 <asp:TableRow >
			<asp:TableCell id="TableCell2"  ColumnSpan="2" ></asp:TableCell>
		</asp:TableRow> 
		
		 <asp:TableRow   style="border-top:1px dotted #CCCCCC;margin-top:10px;">
			<asp:TableCell id="TableCell1"  ColumnSpan="2" runat="server"><ReboundItemSearch:SIGILines id="ctlSelectSIGILines" runat="server" ShowIncludeInvoice="false" /></asp:TableCell>
		</asp:TableRow> 
		

		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
