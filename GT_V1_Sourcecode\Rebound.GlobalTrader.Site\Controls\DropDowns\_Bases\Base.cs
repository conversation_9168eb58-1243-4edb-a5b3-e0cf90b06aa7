//Marker     changed by     Date         Remarks
//[001]      Vinay         16/11/2016    Logout, if user change login from other browser tab
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {

	public class Base : WebControl, IScriptControl, INamingContainer {


		#region Locals

		protected QueryStringManager _objQSManager = new QueryStringManager(HttpContext.Current.Request.QueryString);
		protected DropDown _objDropDown;
		public DropDownList ddl;
		public Panel pnlAddForm;
		public HyperLink hypAdd;
		public IconButton ibtnAdd;
		public IconButton ibtnCancel;
		public Panel pnlFormInner;
		public Panel pnlAddError;
		public Panel pnlAddSaving;
		public HyperLink hypRefresh;
		protected ScriptManager _sm;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		private int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;

		#endregion

		#region Properties

		private bool _blnCanAddTo = false;
		public bool CanAddTo {
			get { return _blnCanAddTo; }
			set { _blnCanAddTo = value; }
		}

		private bool _blnCanRefresh = true;
		public bool CanRefresh {
			get { return _blnCanRefresh; }
			set { _blnCanRefresh = value; }
		}

		private bool _blnServerSideOnly = false;
		public bool ServerSideOnly {
			get { return _blnServerSideOnly; }
			set { _blnServerSideOnly = value; }
		}

		private bool _blnGetDataStraightAway = false;
		public bool GetDataStraightAway {
			get { return _blnGetDataStraightAway; }
			set { _blnGetDataStraightAway = value; }
		}

		/// <summary>
		/// Add form container
		/// </summary>
		private ITemplate _tmpAddForm = null;
		[PersistenceMode(PersistenceMode.InnerDefaultProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate AddForm {
			get { return _tmpAddForm; }
			set { _tmpAddForm = value; }
		}

		/// <summary>
		/// Add form title
		/// </summary>
		private string _strAddFormTitle;
		public string AddFormTitle {
			get { return _strAddFormTitle; }
			set { _strAddFormTitle = value; }
		}

		private Unit _untWidth;
		public new Unit Width {
			get { return _untWidth; }
			set { _untWidth = value; }
		}

		private bool _blnIncludeNoValue = true;
		public bool IncludeNoValue {
			get { return _blnIncludeNoValue; }
			set { _blnIncludeNoValue = value; }
		}

		private string _strNoValue_Value = "0";
		public string NoValue_Value {
			get { return _strNoValue_Value; }
			set { _strNoValue_Value = value; }
		}

		private string _strNoValue_Text = string.Format("< {0} >", Functions.GetGlobalResource("misc", "select"));
		public string NoValue_Text {
			get { return _strNoValue_Text; }
			set { _strNoValue_Text = value; }
		}

		private string _strInitialValue = "0";
		public string InitialValue {
			get { return _strInitialValue; }
			set { _strInitialValue = value; }
		}

		private string _strDataPathModification = "";
		public string DataPathModification {
			get { return _strDataPathModification; }
			set { _strDataPathModification = value; }
		}

        public bool OnlyTeam { get; set; }
        public bool OnlyDivision { get; set; }

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			AddScriptReference("Controls.DropDowns._Bases.Base");
			((Pages.Base)Page).AddCSSFile("DropDowns.css");
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			if (_blnIncludeNoValue) ddl.Items.Add(new ListItem(_strNoValue_Text, _strNoValue_Value));
			base.OnLoad(e);
		}

		protected override void CreateChildControls() {

			//outer
			Panel pnlOuter = ControlBuilders.CreatePanel("dropDown");
			Panel pnlInner = ControlBuilders.CreatePanelInsideParent(pnlOuter, "dropDownInner");

			//drop down list
			ddl = new DropDownList();
			ddl.ID = "ddl";
			pnlInner.Controls.Add(ddl);

			//refresh button
			if (_blnCanRefresh) {
				hypRefresh = ControlBuilders.CreateHyperLink("dropDownRefresh", "javascript:void(0);");
				Image imgRefresh = ControlBuilders.CreateImageInsideParent(hypRefresh, "", "~/images/x.gif", 20, 17);
				pnlInner.Controls.Add(hypRefresh);
			}

			if (_blnCanAddTo) {
				//Add to list box
				hypAdd = ControlBuilders.CreateHyperLink("dropDownAdd", "javascript:void(0);");
				Image imgAdd = ControlBuilders.CreateImageInsideParent(hypAdd, "", "~/images/x.gif", 20, 17);
				pnlInner.Controls.Add(hypAdd);

				//add form
				pnlAddForm = ControlBuilders.CreatePanelInsideParent(pnlOuter, "dropDownAddFormOuter invisible");
				Panel pnlAddFormInner = ControlBuilders.CreatePanelInsideParent(pnlAddForm, "dropDownAddForm");
				HtmlGenericControl h4 = ControlBuilders.CreateHtmlGenericControlInsideParent(pnlAddFormInner, "h4");
				pnlFormInner = ControlBuilders.CreatePanelInsideParent(pnlAddFormInner);
				pnlFormInner.ID = "pnlFormInner";
				ControlBuilders.CreateLiteralInsideParent(h4, _strAddFormTitle);

				//error and saving
				pnlAddError = ControlBuilders.CreatePanelInsideParent(pnlFormInner, "error invisible");
				pnlAddSaving = ControlBuilders.CreatePanelInsideParent(pnlFormInner, "loading invisible");
				ControlBuilders.CreateLiteralInsideParent(pnlAddSaving, Functions.GetGlobalResource("misc", "Saving"));

				////form controls
				//if (_tmpAddForm != null) {
				//    DropDownContainer cnt = new DropDownContainer();
				//    _tmpAddForm.InstantiateIn(cnt);
				//    pnlFormInner.Controls.Add(cnt);
				//    cnt.Dispose(); cnt = null;
				//}

				//Buttons panel
				Panel pnlAddFormButtons = ControlBuilders.CreatePanelInsideParent(pnlFormInner, "buttons");

				//add button
				ibtnAdd = new IconButton();
				ibtnAdd.IconGroup = IconButton.IconGroupList.DropDown;
				ibtnAdd.IconTitleResource = "Add";
				ibtnAdd.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
				ibtnAdd.Alignment = "right";
				ibtnAdd.Href = "javascript:void(0);";
				pnlAddFormButtons.Controls.Add(ibtnAdd);

				//cancel button
				ibtnCancel = new IconButton();
				ibtnCancel.IconGroup = IconButton.IconGroupList.DropDown;
				ibtnCancel.IconTitleResource = "Cancel";
				ibtnCancel.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
				ibtnCancel.Alignment = "right";
				ibtnCancel.Href = "javascript:void(0);";
				pnlAddFormButtons.Controls.Add(ibtnCancel);

			}
			Controls.Add(pnlOuter);
			base.CreateChildControls();
		}

		protected override void OnPreRender(EventArgs e) {
			if (!_blnServerSideOnly && !this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		protected override void Render(HtmlTextWriter writer) {
			ddl.Width = _untWidth;
			ddl.SelectedValue = _strInitialValue;
			if (!_blnServerSideOnly && !this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region Methods

		internal virtual void GetData() { }

		protected void AddToDropDown(string strText, string strValue) {
			EnsureChildControls();
			ddl.Items.Add(new ListItem(strText, strValue));
		}

		protected void AddToDropDown(string strResourceObject, Enum enm) {
			AddToDropDown(Functions.GetGlobalResource(strResourceObject, enm.ToString()), Convert.ToInt32(enm).ToString());
		}

		protected void AddNoValueToDropDown() {
			AddToDropDown(_strNoValue_Text, _strNoValue_Value);
		}

		protected void ClearDropDown() {
			EnsureChildControls();
			ddl.Items.Clear();
		}
		/// <summary>
		/// Find control in form template
		/// </summary>
		/// <param name="strControlName">Control ID to find</param>
		/// <returns>The found Control or null</returns>
		public object FindAddFormControl(string strControlName) {
			EnsureChildControls();
			return (Functions.FindControlRecursive(pnlAddForm, strControlName));
		}

		/// <summary>
		/// Add AJAX script reference
		/// </summary>
		/// <param name="sr"></param>
		protected void AddScriptReference(bool blnDebug, string strAssembly, string strRef) {
			ScriptReference sr = Functions.GetScriptReference(blnDebug, strAssembly, strRef, true);
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
		}
		protected void AddScriptReference(string strAssembly, string strRef) {
			AddScriptReference(false, strAssembly, strRef);
		}
		protected void AddScriptReference(string strRef) {
			bool blnDebug = false;
#if DEBUG
			blnDebug = true;
#endif
			AddScriptReference(blnDebug, "Rebound.GlobalTrader.Site", strRef);
		}

		protected void SetDropDownType(string str) {
			_objDropDown = _objSite.GetDropDown(str);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			if (_scScriptControlDescriptor == null) _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.Base", ClientID);
			_scScriptControlDescriptor.AddElementProperty("lbx", ddl.ClientID);
			_scScriptControlDescriptor.AddProperty("blnCanAdd", _blnCanAddTo);
			_scScriptControlDescriptor.AddProperty("blnCanRefresh", _blnCanRefresh);
			_scScriptControlDescriptor.AddProperty("initialValue", _strInitialValue);
			_scScriptControlDescriptor.AddProperty("varCurrentValue", _strInitialValue);
			_scScriptControlDescriptor.AddProperty("blnIncludeNoValue", _blnIncludeNoValue);
			_scScriptControlDescriptor.AddProperty("blnGetDataStraightAway", _blnGetDataStraightAway);
			_scScriptControlDescriptor.AddProperty("strNoValue_Text", _strNoValue_Text);
			_scScriptControlDescriptor.AddProperty("strNoValue_Value", _strNoValue_Value);
			_scScriptControlDescriptor.AddProperty("strDataPathModification", _strDataPathModification);
			_scScriptControlDescriptor.AddProperty("intDropDownID", _objDropDown.ID);
            //[001] code start
            _scScriptControlDescriptor.AddProperty("intCurrentLogin", (int)SessionManager.LoginID);
            //[001] code end

			if (_blnCanRefresh) _scScriptControlDescriptor.AddElementProperty("hypRefresh", hypRefresh.ClientID);
			if (_blnCanAddTo) {
				_scScriptControlDescriptor.AddElementProperty("hypAdd", hypAdd.ClientID);
				_scScriptControlDescriptor.AddElementProperty("pnlAddForm", pnlAddForm.ClientID);
				_scScriptControlDescriptor.AddElementProperty("pnlFormInner", pnlFormInner.ClientID);
				_scScriptControlDescriptor.AddElementProperty("ibtnAdd", ibtnAdd.ClientID);
				_scScriptControlDescriptor.AddElementProperty("ibtnCancel", ibtnCancel.ClientID);
				_scScriptControlDescriptor.AddElementProperty("pnlAddError", pnlAddError.ClientID);
				_scScriptControlDescriptor.AddElementProperty("pnlAddSaving", pnlAddSaving.ClientID);
				_scScriptControlDescriptor.AddElementProperty("pnlAddSaving", pnlAddSaving.ClientID);
			}
			return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		System.Collections.Generic.IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }
		System.Collections.Generic.IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		#endregion


	}
}