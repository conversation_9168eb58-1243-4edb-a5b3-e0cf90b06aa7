Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.initializeBase(this,[n]);this._intContactID=-1};Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.prototype={get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveClicked))},dispose:function(){this.isDisposed||(this._intContactID=null,Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.callBaseMethod(this,"dispose"))},formShown:function(){this.getFieldDropDownData("ctlMaritalStatus");this.getFieldDropDownData("ctlGender");this.getFieldDropDownData("ctlChild1Sex");this.getFieldDropDownData("ctlChild2Sex");this.getFieldDropDownData("ctlChild3Sex")},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ContactExtendedInfo");n.set_DataObject("ContactExtendedInfo");n.set_DataAction("SaveEdit");n.addParameter("id",this._intContactID);n.addParameter("Gender",this.getFieldValue("ctlGender"));n.addParameter("Birthday",this.getFieldValue("ctlBirthday"));n.addParameter("MaritalStatus",this.getFieldValue("ctlMaritalStatus"));n.addParameter("Partner",this.getFieldValue("ctlPartner"));n.addParameter("PartnerBirthday",this.getFieldValue("ctlPartnerBirthday"));n.addParameter("Anniversary",this.getFieldValue("ctlAnniversary"));n.addParameter("NumberOfChildren",this.getFieldValue("ctlNumberChildren"));n.addParameter("ChildName1",this.getFieldValue("ctlChild1Name"));n.addParameter("ChildGender1",this.getFieldValue("ctlChild1Sex"));n.addParameter("ChildBirthday1",this.getFieldValue("ctlChild1Birthday"));n.addParameter("ChildName2",this.getFieldValue("ctlChild2Name"));n.addParameter("ChildGender2",this.getFieldValue("ctlChild2Sex"));n.addParameter("ChildBirthday2",this.getFieldValue("ctlChild2Birthday"));n.addParameter("ChildName3",this.getFieldValue("ctlChild3Name"));n.addParameter("ChildGender3",this.getFieldValue("ctlChild3Sex"));n.addParameter("ChildBirthday3",this.getFieldValue("ctlChild3Birthday"));n.addParameter("PersonalCellphone",this.getFieldValue("ctlMobileTel"));n.addParameter("FavouriteSport",this.getFieldValue("ctlFavouriteSport"));n.addParameter("FavouriteTeam",this.getFieldValue("ctlFavouriteTeam"));n.addParameter("Hobbies",this.getFieldValue("ctlHobbies"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()}};Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);