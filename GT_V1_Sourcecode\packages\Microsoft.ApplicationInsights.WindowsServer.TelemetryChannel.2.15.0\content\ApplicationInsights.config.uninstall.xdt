<ApplicationInsights xmlns="http://schemas.microsoft.com/ApplicationInsights/2013/Settings" xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <TelemetrySinks>
    <Add Name="default">
      <TelemetryChannel xdt:Transform="Remove" xdt:Locator="Match(Type)" Type="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.ServerTelemetryChannel, Microsoft.AI.ServerTelemetryChannel" />
      <TelemetryProcessors>
        <Add Type="Microsoft.ApplicationInsights.Extensibility.AutocollectedMetricsExtractor, Microsoft.ApplicationInsights"
           xdt:Transform="RemoveAll"
           xdt:Locator="Match(Type)">
        </Add>
        <Add Type="Microsoft.ApplicationInsights.WindowsServer.TelemetryChannel.AdaptiveSamplingTelemetryProcessor, Microsoft.AI.ServerTelemetryChannel"
           xdt:Transform="RemoveAll"
           xdt:Locator="Match(Type)">
        </Add>
      </TelemetryProcessors>
      <TelemetryProcessors xdt:Transform="Remove" xdt:Locator="Condition(count(*)=0)"/>
    </Add>
  </TelemetrySinks>

  <TelemetrySinks>
    <Add Name="default" xdt:Transform="Remove" xdt:Locator="Condition(count(*)=0)">
    </Add>
  </TelemetrySinks>

  <TelemetrySinks xdt:Transform="Remove" xdt:Locator="Condition(count(*)=0)"/>
</ApplicationInsights>