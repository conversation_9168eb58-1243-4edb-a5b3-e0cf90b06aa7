﻿/*$('#btnUploadExcelFile').click(function () {*/
var AbsoluteURL = window.location.origin;
var companyName = '';
var companyId = -1;
var currencyId = -1;
var defaultSelectProto = '<option value="0">&lt; Select &gt;</option>';
var defaultSelectdrop = '<option value="0">&lt; Not Imported &gt;</option>';
var listItems = "";
var optionStart = "<option value='";
var optionEnd = "</option>";
var defaultSelect = 0;
var SelectedClientId = 0;
var handlerUrl = '';
var selectColumnList = '';
var options = new Array();
var excelColumns = '';
var excelColumnsim = '';
var clientid = 0;
var i = 0;
var Columnlist = '';
var DestColumnlist = '';
var Callfrom = '';
var Column_Name = '';
var clickfrombutton = '';
var excelCheckBox = '';
var columnLable = '';
var dataCheckBox = '';
var salespersonId = 0;
var uploadExcelflag = false;
var generateImportflag = false;
var saveRFQflag = false;

$('.inputDisabled').prop("disabled", true);


function DisplayRawBOMData() {
     if (validation() == true) {
        var formData = new FormData();
        var file = document.getElementById("myfile").files[0];
        formData.append("myfile", file);
        formData.append("clientId", $("#ClientId").val());
        formData.append("digitalCurrency", $("#DigitalCurrency option:selected").text());
        formData.append("chkcolumnheader", $('#Filecolumnheader').val());
        $.ajax({
            type: "POST",
            url: AbsoluteURL + "/BOM/SalesBomSheet/UploadExcelFileOnCloud",
            data: formData,
            dataType: 'json',
            contentType: false,
            processData: false,
            cache: false,
            success: function (data) {
                $("#Messageid").fadeIn(200).delay(1000).fadeOut(200);
                var exceldata = data.Result;
                var obj = {
                    maxHeight: 300,
                    minWidth: 500,
                    resizable: true,
                    showHeader: false,
                    numberCell: { show: false },
                    scrollModel: { autoFit: true },
                    dataModel: { data: exceldata }
                };
                $("#grid_json").pqGrid(obj).pqGrid('refreshDataAndView');
                var excelColumndata = data.ExcelFileColumn;
                $('.dropdown-control').empty();
                $.each(excelColumndata, function (i, ExcelHeaderColumn) {
                    $(".dropdown-control").append('<option value=" ' + ExcelHeaderColumn.Id + ' ">' + ExcelHeaderColumn.ColumnsName + '</option>');
                });

                mapcheckDropdown(excelColumndata);
                $("#GenerateImportData").enable = true;
                uploadExcelflag = true;

                //$.each(excelColumndata, function (i, ExcelHeaderColumn) {
                //    $(".dropdown-control").find('<option value=" ' + ExcelHeaderColumn.Id + ' ">' + ExcelHeaderColumn.ColumnsName + '</option>');
                //});

                //var exists = false;
                //$('#StockCode option').each(function () {
                //    if (this.value == 'StockCode') {
                //        chbStockCode.check = true;
                //    }
                //});

                //let text = 'StockCode';
                //$("#StockCode option").filter(function () {
                //    //may want to use $.trim in here
                //    return $(this).text() == text;
                //}).prop('selected', true);

                //var dID = $(this).find(":StockCode").val();
                //$('#chbStockCode[value="' + dID + '"]').prop('checked', true)
                //    .siblings().prop('checked', false);
            }
        });
    }
};

function validation() {
    if ($("#ClientId").val() == '') {
        alert('Please select Client');
        return false;
    }
    else if ($("#DigitalCurrency").val() == '') {
        alert('Please select Digital Currency');
        return false;
    }
    else if ($("#Company").val() == '') {
        alert('Please Enter Company');
        return false;
    }
    else if ($("#Salesperson").val() == '') {
        alert('Please Enter Sales Person');
        return false;
    }
    else if ($("#Contact").val() == 0) {
        alert('Please select Contact');
        return false;
    }
    else if ($("#BOMName").val() == '') {
        alert('Please Enter BOM Name');
        return false;
    }
    else if ($("#myfile").val() == '') {
        alert('Please select a file.');
        return false;
    }
    else
        return true;
}


function UploadFile() {
    if (ValidateExcelFile()) {
        var excelFile = document.getElementById('ExcelFile');
        formData = new FormData();
        if (excelFile.files.length > 0) {
            for (var i = 0; i < excelFile.files.length; i++) {
                formData.append('file-' + i, excelFile.files[i]);
            }
        }
        $.ajax({
            url: "/BOM/SalesBomSheet/MyController",
            type: "POST",
            dataType: 'json',
            processData: false,
            contentType: false,
            data: formData,
            success: function (data) {
                // Further Processing
            },
            error: function (err) {
                //Error
            }
        });
    }
};


function getColumnHeaderData() {
    var formData = new FormData();
    var file = document.getElementById("myfile").files[0];
    formData.append("myfile", file);
    //var AbsoluteURL = window.location.origin;

    $.ajax({
        type: "POST",
        url: AbsoluteURL + "/BOM/SalesBomSheet/GetExcelFileColumn",
        data: formData,
        dataType: 'json',
        contentType: false,
        processData: false,
        cache: false,
        success: function (response) {
            var myhtml = '';
            if (response.success) {
                myhtml = '<div class="alert alert-success">' + response.responseMessage + '</div>';
            }
            else {
                myhtml = '<div class="alert alert-danger">' + response + '</div>';
            }
            $("#Messageid").fadeIn(200).delay(1000).fadeOut(200);
            var data = response;
        }
    });
}


function getExcelColumn() {
    filters = new Object();
    filters.username = $('#createuser-usernamesearch #user_name').val();
    return filters;
}

function ResetBOMData() {
    uploadExcelflag = false;
    generateImportflag = false;
    $("#ClientId").val() == 0;
    $("#DigitalCurrency").val() == 0;
    $("#Company").val();
    $("#Salesperson").val();
    $("#Contact").val() == 0;
    $("#BOMName").val() == '';
    $("#myfile").val() == '';
    $("#chbStockCode").prop('checked', false);
    $("#chbPart").prop('checked', false);
    $("#chbUnitPrice").prop('checked', false);
    $("#chbDescription").prop('checked', false);
    $("#chbRFQ").prop('checked', false);
    $("#LineTotal").prop('checked', false);
};


    
$(function () {
    $('#ClientId').change(function () {
        var ClientId = $.trim($("#ClientId").val());
        //var AbsoluteURL = window.location.origin;
        $.ajax({
            type: "POST",
            url: "/BOM/SalesBomSheet/SearchCurreny",
            data: "{'clientId':'" + ClientId + "'}",
            dataType: 'json',
            contentType: 'application/json; charset=utf-8',
            processData: false,
            cache: false,
            async: true,
            success: function (data) {
               // response(data.d);
                $("#DigitalCurrency").empty("");
                //$("#DigitalCurrency").find("option").remove();
                $.each(data, function (i, DigitalCurrency) {
                    $("#DigitalCurrency").append('<option value="' + DigitalCurrency.CurrencyId + '">' + DigitalCurrency.CurrencyDescription + '</option>');
                });
            },
            error: function (result) {
                //alert("Error");
            }
        });
    });
});


$(function () {
    $("#Company").autocomplete({
        source: function (request, response) {
            $.ajax({
                type: "POST",
                contentType: "application/json; charset=utf-8",
                url: "/BOM/SalesBomSheet/AutoSearchSales",
                data: "{'ClientId':'" + document.getElementById('ClientId').value + "','Searchvalue':'" + document.getElementById('Company').value + "'}",
                dataType: "json",
                minLength: 3,
                success: function (data) {
                    response($.map(data, function (item) {
                        return {
                            value: item.CompanyName,
                            id: item.CompanyId
                        };
                    }));
                },
                error: function (result) {
                    alert("No Match");
                }
            });
        },
        select: function (event, ui) {
            companyId = ui.item.id;
            $("#Company").val(ui.item.CompanyName);            
            $.ajax({
                cache: false,
                async: false,
                type: "POST",
                url: "/BOM/SalesBomSheet/GetCompanyAndOtherMasterData", 
                data: { 'ClientId': companyId },
                success: function (data) {
                    var jsonData = JSON.parse(data);
                    var objContact = $.grep(jsonData, function (v) { return v.ResultType.toLowerCase().trim() == "contact"; });
                    bindddlContact(objContact);
                    $("#Salesperson").val(objContact[1].SalesPersonName);
                    salespersonId = (objContact[1].SalesPersonId);
                    var exists = false;
                    $('#Contact option').each(function () {
                        if (this.value == jsonData[0].ContactId) {
                            exists = true;
                            return false;
                        }
                    });
                    if (exists) {
                        $("#Contact").val(objContact[0].ContactId);
                    }
                },
                error: function (xhr, ajaxOptions, thrownError) {
                    alert('Failed to retrieve states.');
                }
            });
        }
    });
});


function bindddlContact(data) {
    listItems = '';
    listItems = defaultSelectProto;
    for (var i = 0; i < data.length; i++) {
        listItems += optionStart + data[i].Id + "'>" + data[i].Text + optionEnd;
    }
    $("#Contact").html(listItems);
    var exists = false;
    $('#DigitalCurrency option').each(function () {
        if (this.value == data[0].CurrencyId) {
            exists = true;
            return false;
        }
    });
    if (exists) {
        $("#DigitalCurrency").val(data[0].CurrencyId);
    }
    //$("#Company").html(listItems)    
};


function mapcheckDropdown(data) {
    var d = data;
    $("#chbStockCode").prop('checked', false);
    $("#chbPart").prop('checked', false);
    $("#chbUnitPrice").prop('checked', false);
    $("#chbDescription").prop('checked', false);
    $("#chbRFQ").prop('checked', false);
    $("#chbLineTotal").prop('checked', false);    
    for (var i = 1; i <= data.length - 1; i++) {
        var result1 = '';
        var result = data[i].ColumnsName;
        if (result == 'Stock Code') {
            result1 = 'StockCode'
        }
        if (result == 'Unit Price') {
            result1 = 'UnitPrice'
        }
        if (result == 'Line Total') {
            result1 = 'LineTotal'
        }
        if ($("#chbStockCode").val() == result1) {
            $("#chbStockCode").prop('checked', true);
            $("#StockCode option").filter(function () {
                return $(this).text() == result;
            }).prop('selected', true);
        }
        if ($("#chbPart").val() == result) {
            $("#chbPart").prop('checked', true);
            $("#Part option").filter(function () {
                return $(this).text() == result;
            }).prop('selected', true);
        }
        if ($("#chbUnitPrice").val() == result1) {
            $("#chbUnitPrice").prop('checked', true);
            $("#UnitPrice option").filter(function () {
                return $(this).text() == result;
            }).prop('selected', true);
        }
        if ($("#chbDescription").val() == result) {
            $("#chbDescription").prop('checked', true);
            $("#Description option").filter(function () {
                return $(this).text() == result;
            }).prop('selected', true);
        }
        if ($("#chbRFQ").val() == result) {
            $("#chbRFQ").prop('checked', true);
            $("#RFQ option").filter(function () {
                return $(this).text() == result;
            }).prop('selected', true);
        }
        if ($("#chbLineTotal").val() == result1) {
            $("#chbLineTotal").prop('checked', true);
            $("#LineTotal option").filter(function () {
                return $(this).text() == result;
            }).prop('selected', true);
        }
    }
};


function GenerateImportData() {
    if (validation() == true) {
        if ($("#RequiremenTraceability").val() == '') {
            alert('Please Select Requirement Traceability');
            return false;
        }
        if ($("#Type").val() == '') {
            alert('Please Select Type');
            return false;
        }
        if ($("#CurrentDateTime").val() == '') {
            alert('Please Enter Current Date Time');
            return false;
        }
        if (uploadExcelflag == false) {
            alert('Please upload file first');
            return false;
        }

        var ClientId = $.trim($("#ClientId").val());
        var selectedclient_Id = $.trim($("#ClientId").val());
        //company  //var Company = $.trim($("#Company").val());
        var ddlClient = $.trim($("#ddlClient").val());
        var company_Id = companyId;
        var companyName_Text = $.trim($("#Company").val());
        //contact
        var contact_Id = $.trim($("#Contact").val());
        var contactName = $("#Contact option:selected").text();
        //Difital currency //var chkOverRide = $.trim($("#chkOverRide").val());
        var digitalCurrency_id = $.trim($("#DigitalCurrency").val());
        var digitalCurrency_Name = $("#DigitalCurrency option:selected").text();
        //default check box
        var defaultcurrency = $('#Defaultcurrency').val();
        //var Salesperson = $.trim($("#Salesperson").val());
        //var BOMName = $.trim($("#BOMName").val());
        var ddlCurrency_short = $.trim($("#DigitalCurrency").val());

        //var insertDataList = "Column3 as Part,Column1 as CustomerPart,Column2 as PackageName,Column4 as ManufacturerName,Column5 as Price,Column6 as Quantity,";
        checkSelectedExcelColumns();
        var insertDataList = excelCheckBox;
        //var Column_Lable = "Part,CustomerPart,PackageName,ManufacturerName,Price,Quantity,";
        columnLebal();
        var column_Lable = columnLebals;
        //var Column_Name = "Column3,Column1,Column2,Column4,Column5,Column6,";
        checkSelectedDataColumns();
        var column_Name = dataCheckBox;

        //var CurrencyColumn_Name = $.trim($("#chkOverRide").val());
        var currencyColumn_Name = "Column4";

        //string company_Id, string companyName_Text, string selectedclient_Id, string ddlCurrency_short, string insertDataList_Colmn,
        //string ColumnLable_text, string Column_Name, string ContactName, string Contact_Id, string chkOverRide, string digitalCurrency_Name, 
        //string DefaultCurrency_Id, string CurrencyColumn_Name

        //var formData = new FormData();

        //formData.append("clientId", $.trim($("#ClientId").val()));
        //formData.append("company_Id", companyId);
        //formData.append("companyName_Text", $.trim($("#Company").val()));
        //formData.append("contact_Id", $.trim($("#Contact").val()));
        //formData.append("contactName", $("#Contact option:selected").text());
        //formData.append("digitalCurrency_id", $.trim($("#DigitalCurrency").val()));
        //formData.append("digitalCurrency", $("#DigitalCurrency option:selected").text());
        //formData.append("defaultcurrency", $('#Defaultcurrency').val());
        //formData.append("chkcolumnheader", $('#Filecolumnheader').val());
        //formData.append("insertDataList", excelCheckBox);
        //formData.append("column_Lable", columnLebals);
        //formData.append("column_Name", dataCheckBox);
        //formData.append("RequiremenTraceability", $("#RequiremenTraceability").val());
        //formData.append("Type", $("#Type").val());
        //formData.append("CurrentDateTime", $("#CurrentDateTime").val());

        var requiremenTraceability = $.trim($("#RequiremenTraceability").val());
        var type = $.trim($("#Type").val());
        var currentDateTime = $.trim($("#CurrentDateTime").val());

        $.ajax({
            type: "POST",
            url: "/BOM/SalesBomSheet/GenrateBOMManagerData",
            data: "{'company_Id': '" + company_Id + "','companyName_Text': '" + companyName_Text + "','selectedclient_Id': '" + selectedclient_Id + "', 'ddlCurrency_short': '" + ddlCurrency_short + "', 'insertDataList_Colmn': '" + insertDataList + "', 'columnLable_text': '" + column_Lable + "', 'column_Name': '" + column_Name + "', 'contactName': '" + contactName + "', 'chkOverRide': '" + defaultcurrency + "', 'digitalCurrency_Name': '" + digitalCurrency_Name + "', 'currencyColumn_Name': '" + currencyColumn_Name + "', 'requiremenTraceability': '" + requiremenTraceability + "', 'type': '" + type + "', 'currentDateTime': '" + currentDateTime + "'}",
            //data: formData,
            dataType: 'json',
            contentType: 'application/json; charset=utf-8',
            processData: false,
            cache: false,
            async: true,
            success: function (response) {
                var exceldata = response;
                //console.log(exceldata);
                var obj = {
                    maxHeight: 300,
                    width: "auto",
                    resizable: true,
                    showHeader: false,
                    numberCell: { show: false },
                    scrollModel: { autoFit: true },
                    dataModel: { data: exceldata }
                };

                $("#gridDataImport_json").pqGrid(obj).pqGrid('refreshDataAndView');

                generateImportflag = true;
                //var excelColumndata = response.ExcelFileColumn;
                //$('.dropdown-control').empty();

                //$.each(excelColumndata, function (i, ExcelHeaderColumn) {
                //    $(".dropdown-control").append('<option value=" ' + ExcelHeaderColumn.Id + ' ">' + ExcelHeaderColumn.ColumnsName + '</option>');
                //});

                //var exists = false;
                //$('#StockCode option').each(function () {
                //    if (this.value == 'StockCode') {
                //        chbStockCode.check = true;
                //    }
                //});

                //let text = 'StockCode';
                //$("#StockCode option").filter(function () {
                //    //may want to use $.trim in here
                //    return $(this).text() == text;
                //}).prop('selected', true);

                //var dID = $(this).find(":StockCode").val();
                //$('#chbStockCode[value="' + dID + '"]').prop('checked', true)
                //    .siblings().prop('checked', false);
            }
        });
    }
};

function checkSelectedExcelColumns() {
    excelCheckBox = '';
    if ($("#chbStockCode").prop("checked") == true) {
        excelCheckBox = excelCheckBox + 'Column1 as ' + $("#chbStockCode").val()+','
    }
    if ($("#chbDescription").prop("checked") == true) {
        excelCheckBox = excelCheckBox + 'Column2 as ' + $("#chbDescription").val() + ','
    }
    if ($("#chbPart").prop("checked") == true) {
        excelCheckBox = excelCheckBox + 'Column3 as ' + $("#chbPart").val() + ','
    }
    if ($("#chbRFQ").prop("checked") == true) {
        excelCheckBox = excelCheckBox + 'Column4 as ' + $("#chbRFQ").val() + ','
    }
    if ($("#chbUnitPrice").prop("checked") == true) {
        excelCheckBox = excelCheckBox + 'Column5 as ' + $("#chbUnitPrice").val() + ','
    }
    if ($("#chbLineTotal").prop("checked") == true) {
        excelCheckBox = excelCheckBox + 'Column6 as ' + $("#chbLineTotal").val() + ','
    }
};

function checkSelectedDataColumns() {
    dataCheckBox = '';
    if ($("#chbStockCode").prop("checked") == true) {
        dataCheckBox = dataCheckBox + 'Column1'+','
    }
    if ($("#chbDescription").prop("checked") == true) {
        dataCheckBox = dataCheckBox + 'Column2' + ','
    }
    if ($("#chbPart").prop("checked") == true) {
        dataCheckBox = dataCheckBox + 'Column3' + ','
    }
    if ($("#chbRFQ").prop("checked") == true) {
        dataCheckBox = dataCheckBox + 'Column4'+ ','
    }
    if ($("#chbUnitPrice").prop("checked") == true) {
        dataCheckBox = dataCheckBox + 'Column5' + ','
    }
    if ($("#chbLineTotal").prop("checked") == true) {
        dataCheckBox = dataCheckBox + 'Column6'+ ','
    }
};

function columnLebal() {
    columnLebals = '';
    columnLebals = 'StockCode,Description,Part,RFQ,UnitPrice,LineTotal';
};


function SaveSupplierMappingsSave() {
    if (validation() == true) {
        if ($("#RequiremenTraceability").val() == '') {
            alert('Please Select Requirement Traceability');
            return false;
        }
        if ($("#Type").val() == '') {
            alert('Please Select Type');
            return false;
        }
        if ($("#CurrentDateTime").val() == '') {
            alert('Please Enter Current Date Time');
            return false;
        }

        if (uploadExcelflag == false) {
            alert('Please Upload File First');
            return false;
        }

        if (saveRFQflag == true) {
            alert('File already saved');
            return false;
        }

        if (generateImportflag== false) {
            alert('Please Generate Importe Data First');
            return false;
        }

        var ClientId = $.trim($("#ClientId").val());
        var selectedclient_Id = $.trim($("#ClientId").val());
        var ddlClient = $.trim($("#ddlClient").val());
        var company_Id = companyId;
        var companyName_Text = $.trim($("#Company").val());
        var contact_Id = $.trim($("#Contact").val());
        var contactName = $("#Contact option:selected").text();
        var digitalCurrency_id = $.trim($("#DigitalCurrency").val());
        var digitalCurrency_Name = $("#DigitalCurrency option:selected").text();
        var defaultcurrency = $('#Defaultcurrency').val();
        var ddlCurrency_short = $.trim($("#DigitalCurrency").val());
        var filecolumnheader = $.trim($("#Filecolumnheader").val());

        //var insertDataList = "Column3 as Part,Column1 as CustomerPart,Column2 as PackageName,Column4 as ManufacturerName,Column5 as Price,Column6 as Quantity,";
        checkSelectedExcelColumns();
        var insertDataList = excelCheckBox;
        //var Column_Lable = "Part,CustomerPart,PackageName,ManufacturerName,Price,Quantity,";
        columnLebal();
        var column_Lable = columnLebals;
        //var Column_Name = "Column3,Column1,Column2,Column4,Column5,Column6,";
        checkSelectedDataColumns();
        var column_Name = dataCheckBox;

        //var CurrencyColumn_Name = $.trim($("#chkOverRide").val());
        var currencyColumn_Name = "Column4";

        var requiremenTraceability = $.trim($("#RequiremenTraceability").val());
        var type = $.trim($("#Type").val());
        var currentDateTime = $.trim($("#CurrentDateTime").val());

        var formData = new FormData();
        formData.append("clientId", $("#ClientId").val());
        formData.append("clientIdName", $("#ClientId option:selected").text());
        formData.append("digitalcurrencyId", $("#DigitalCurrency").val());
        formData.append("digitalcurrencyName", $("#DigitalCurrency option:selected").text());
        formData.append("chkDefaultCurrent", $("#Defaultcurrency").val());

        formData.append("companyId", companyId);
        formData.append("companyName", $("#Company").val());
        formData.append("salespersonName", $("#Salesperson").val());
        formData.append("salespersonId", salespersonId);
        formData.append("applyPartwatch", $("#ApplyPartwatch").val());

        formData.append("contactId", $("#Contact").val());
        formData.append("contactName", $("#Contact option:selected").text());
        formData.append("bOMName", $("#BOMName").val());

        formData.append("insertDataList", insertDataList);
        formData.append("column_Lable", column_Lable);
        formData.append("column_Name", column_Name);

        formData.append("requiremenTraceabilityId", $("#RequiremenTraceability").val());
        formData.append("requiremenTraceabilityName", $("#RequiremenTraceability option:selected").text());
        formData.append("type", $("#Type").val());
        formData.append("currentDateTime", $("#CurrentDateTime").val());
        formData.append("ActionPerform", "ImportData");
        formData.append("SaveImportOrHubRFQ", "SaveAsBomManagerHUBRFQ");

        $.ajax({
            type: "POST",
            url: AbsoluteURL + "/BOM/SalesBomSheet/ImportBOMManagerdata",
            data: formData,
            dataType: 'json',
            contentType: false,
            processData: false,
            cache: false,
            success: function (response) {
                var exceldata = response;
                saveRFQflag = true;
                alert(response);       

                //var obj = {
                //    maxHeight: 300,
                //    width: "auto",
                //    resizable: true,
                //    showHeader: false,
                //    numberCell: { show: false },
                //    scrollModel: { autoFit: false },
                //    dataModel: { data: exceldata }
                //};

                //$("#gridDataImport_json").pqGrid(obj).pqGrid('refreshDataAndView');

                //var excelColumndata = response.ExcelFileColumn;
                //$('.dropdown-control').empty();

                //$.each(excelColumndata, function (i, ExcelHeaderColumn) {
                //    $(".dropdown-control").append('<option value=" ' + ExcelHeaderColumn.Id + ' ">' + ExcelHeaderColumn.ColumnsName + '</option>');
                //});

                //var exists = false;
                //$('#StockCode option').each(function () {
                //    if (this.value == 'StockCode') {
                //        chbStockCode.check = true;
                //    }
                //});

                //let text = 'StockCode';
                //$("#StockCode option").filter(function () {
                //    //may want to use $.trim in here
                //    return $(this).text() == text;
                //}).prop('selected', true);

                //var dID = $(this).find(":StockCode").val();
                //$('#chbStockCode[value="' + dID + '"]').prop('checked', true)
                //    .siblings().prop('checked', false);
            }
        });
    }
};


function RetrieveSupplierMappingsSave() {
    if (validation() == true) {
        if ($("#RequiremenTraceability").val() == '') {
            alert('Please Select Requirement Traceability');
            return false;
        }
        if ($("#Type").val() == '') {
            alert('Please Select Type');
            return false;
        }
        if ($("#CurrentDateTime").val() == '') {
            alert('Please Enter Current Date Time');
            return false;
        }
        if (uploadExcelflag == false) {
            alert('Please Upload File First');
            return false;
        }
        if (generateImportflag == false) {
            alert('Please Generate Importe Data First');
            return false;
        }
        if (saveRFQflag == true) {
            alert('File already saved');
            return false;
        }

        var ClientId = $.trim($("#ClientId").val());
        var selectedclient_Id = $.trim($("#ClientId").val());
        var ddlClient = $.trim($("#ddlClient").val());
        var company_Id = companyId;
        var companyName_Text = $.trim($("#Company").val());
        var contact_Id = $.trim($("#Contact").val());
        var contactName = $("#Contact option:selected").text();
        var digitalCurrency_id = $.trim($("#DigitalCurrency").val());
        var digitalCurrency_Name = $("#DigitalCurrency option:selected").text();
        var defaultcurrency = $('#Defaultcurrency').val();
        var ddlCurrency_short = $.trim($("#DigitalCurrency").val());
        var filecolumnheader = $.trim($("#Filecolumnheader").val());

        //var insertDataList = "Column3 as Part,Column1 as CustomerPart,Column2 as PackageName,Column4 as ManufacturerName,Column5 as Price,Column6 as Quantity,";
        checkSelectedExcelColumns();
        var insertDataList = excelCheckBox;
        //var Column_Lable = "Part,CustomerPart,PackageName,ManufacturerName,Price,Quantity,";
        columnLebal();
        var column_Lable = columnLebals;
        //var Column_Name = "Column3,Column1,Column2,Column4,Column5,Column6,";
        checkSelectedDataColumns();
        var column_Name = dataCheckBox;

        //var CurrencyColumn_Name = $.trim($("#chkOverRide").val());
        var currencyColumn_Name = "Column4";

        var requiremenTraceability = $.trim($("#RequiremenTraceability").val());
        var type = $.trim($("#Type").val());
        var currentDateTime = $.trim($("#CurrentDateTime").val());

        var formData = new FormData();
        formData.append("clientId", $("#ClientId").val());
        formData.append("clientIdName", $("#ClientId option:selected").text());
        formData.append("digitalcurrencyId", $("#DigitalCurrency").val());
        formData.append("digitalcurrencyName", $("#DigitalCurrency option:selected").text());
        formData.append("chkDefaultCurrent", $("#Defaultcurrency").val());

        formData.append("companyId", companyId);
        formData.append("companyName", $("#Company").val());
        formData.append("salespersonName", $("#Salesperson").val());
        formData.append("salespersonId", salespersonId);
        formData.append("applyPartwatch", $("#ApplyPartwatch").val());

        formData.append("contactId", $("#Contact").val());
        formData.append("contactName", $("#Contact option:selected").text());
        formData.append("bOMName", $("#BOMName").val());

        formData.append("insertDataList", insertDataList);
        formData.append("column_Lable", column_Lable);
        formData.append("column_Name", column_Name);

        formData.append("requiremenTraceabilityId", $("#RequiremenTraceability").val());
        formData.append("requiremenTraceabilityName", $("#RequiremenTraceability option:selected").text());
        formData.append("type", $("#Type").val());
        formData.append("currentDateTime", $("#CurrentDateTime").val());
        formData.append("ActionPerform", "ImportData");
        formData.append("SaveImportOrHubRFQ", "SaveAsBomManagerHUBRFQ");

        $.ajax({
            type: "POST",
            url: AbsoluteURL + "/BOM/SalesBomSheet/ImportBOMManagerdata",
            data: formData,
            dataType: 'json',
            contentType: false,
            processData: false,
            cache: false,
            success: function (response) {
                var exceldata = response;
                saveRFQflag == true
                alert(response);                
                //var obj = {
                //    maxHeight: 300,
                //    width: "auto",
                //    resizable: true,
                //    showHeader: false,
                //    numberCell: { show: false },
                //    scrollModel: { autoFit: false },
                //    dataModel: { data: exceldata }
                //};

                //$("#gridDataImport_json").pqGrid(obj).pqGrid('refreshDataAndView');             
            }
        });
    }
};

//$("#btnUploadExcelFile")
//    .addEventListener("click", function () {
//        document.body.style.cursor = "wait";
//        document.getElementById("btn")
//            .style.backgroundColor = "gray";
//        document.getElementById("btn")
//            .style.cursor = "wait";
//    });


$(function () {
    $("#StockCode").change(function () {
        var CustomerPart = ($("#StockCode:selected").index()) ? $("#StockCode option:selected").index() : 0;
        if (CustomerPart > 0) {
            $("#chbStockCode").prop('checked', true);
            //$('#btnSaveMapping').prop('disabled', false).css('opacity', 5.5);
        }
        else {
            $("#StockCode").prop('checked', false);
        }
    });
});

$(function () {
    $("#Part").change(function () {
        var CustomerPart = ($("#Part:selected").index()) ? $("#Part option:selected").index() : 0;
        if (CustomerPart > 0) {
            $("#chbPart").prop('checked', true);
            //$('#btnSaveMapping').prop('disabled', false).css('opacity', 5.5);
        }
        else {
            $("#Part").prop('checked', false);
        }
    });
});
$(function () {
    $("#UnitPrice").change(function () {
        var CustomerPart = ($("#UnitPrice:selected").index()) ? $("#UnitPrice option:selected").index() : 0;
        if (CustomerPart > 0) {
            $("#chbUnitPrice").prop('checked', true);
            //$('#btnSaveMapping').prop('disabled', false).css('opacity', 5.5);
        }
        else {
            $("#UnitPrice").prop('checked', false);
        }
    });
});
$(function () {
    $("#Description").change(function () {
        var CustomerPart = ($("#Description:selected").index()) ? $("#Description option:selected").index() : 0;
        if (CustomerPart > 0) {
            $("#chbDescription").prop('checked', true);
            //$('#btnSaveMapping').prop('disabled', false).css('opacity', 5.5);
        }
        else {
            $("#Description").prop('checked', false);
        }
    });
});
$(function () {
    $("#RFQ").change(function () {
        var CustomerPart = ($("#RFQ:selected").index()) ? $("#RFQ option:selected").index() : 0;
        if (CustomerPart > 0) {
            $("#chbRFQ").prop('checked', true);
            //$('#btnSaveMapping').prop('disabled', false).css('opacity', 5.5);
        }
        else {
            $("#RFQ").prop('checked', false);
        }
    });
});
$(function () {
    $("#LineTotal").change(function () {
        var CustomerPart = ($("#LineTotal:selected").index()) ? $("#LineTotal option:selected").index() : 0;
        if (CustomerPart > 0) {
            $("#chbLineTotal").prop('checked', true);
            //$('#LineTotal').prop('disabled', false).css('opacity', 5.5);
        }
        else {
            $("#LineTotal").prop('checked', false);
        }
    });
});



$("#CurrentDateTime").datepicker({
    showOn: "button",
    buttonImage: "~/App_Themes/Original/images/calendar/cal.gif",
    buttonImageOnly: true,
    buttonText: "Select date"
});

function disable() {
    $("#chbStockCode").disabled = true;
};






