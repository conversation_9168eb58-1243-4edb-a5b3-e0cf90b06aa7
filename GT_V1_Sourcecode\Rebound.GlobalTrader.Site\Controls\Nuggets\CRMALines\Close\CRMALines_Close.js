Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Close=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Close.initializeBase(this,[n]);this._intCRMAID=0;this._intLineID=0;this._ctlConfirm=null};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Close.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Close.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},setFieldsFromHeader:function(n,t){this.setFieldValue("ctlCustomerRMA",n);this.setFieldValue("ctlCustomer",t)},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._intLineID=null,this._intCRMAID=null,this._ctlConfirm=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Close.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},yesClicked:function(){if(confirm($R_RES.CloseCRMALine)){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("CloseLine");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Close.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Close",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);