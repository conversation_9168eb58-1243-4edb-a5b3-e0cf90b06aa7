﻿IF EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbSO_ExportApprovalDetails' AND COLUMN_NAME = 'PartApplication'
)
BEGIN
    ALTER TABLE tbSO_ExportApprovalDetails
    DROP COLUMN PartApplication;
END

IF EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbSO_ExportApprovalDetails' AND COLUMN_NAME = 'ExportControl'
)
BEGIN
    ALTER TABLE tbSO_ExportApprovalDetails
    DROP COLUMN ExportControl;
END

IF EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbSO_ExportApprovalDetails' AND COLUMN_NAME = 'AerospaceUse'
)
BEGIN
    ALTER TABLE tbSO_ExportApprovalDetails
    DROP COLUMN AerospaceUse;
END

IF EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbSO_ExportApprovalDetails' AND COLUMN_NAME = 'PartTested'
)
BEGIN
    ALTER TABLE tbSO_ExportApprovalDetails
    DROP COLUMN PartTested;
END