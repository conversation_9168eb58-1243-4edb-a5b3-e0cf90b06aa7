//[001]  A<PERSON><PERSON><PERSON>                     Date: 27-10-2021                        Add draft new draft button.
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class ExportApprovalStatus_Edit : Base
    {

        #region Properties
        private int _intPurchaseOrderID = -1;
        public int PurchaseOrderID
        {
            get { return _intPurchaseOrderID; }
            set { _intPurchaseOrderID = value; }
        }
        private string _strTitleMessage;
        public string TitleMessage
        {
            get { return _strTitleMessage; }
            set { _strTitleMessage = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "ExportApproval_Edit");
            AddScriptReference("Controls.Nuggets.ExportApprovalStatus.Edit.ExportApprovalStatus_Edit.js");
            if (_objQSManager.PurchaseOrderID > 0) _intPurchaseOrderID = _objQSManager.PurchaseOrderID;
            _strTitleMessage = Functions.GetGlobalResource("FormTitles", "ExportApproval_Edit");
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            WireUpControls();
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("strTitleMessage", _strTitleMessage);
            _scScriptControlDescriptor.AddProperty("intPurchaseOrderID", _intPurchaseOrderID);
        }
        private void WireUpControls()
        {

        }
    }
}
