Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.initializeBase(this,[n]);this._intCurrencyID=-1};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.prototype={get_lblCurrency:function(){return this._lblCurrency},set_lblCurrency:function(n){this._lblCurrency!==n&&(this._lblCurrency=n)},get_lblCurrency2:function(){return this._lblCurrency2},set_lblCurrency2:function(n){this._lblCurrency2!==n&&(this._lblCurrency2=n)},get_ibtnApply:function(){return this._ibtnApply},set_ibtnApply:function(n){this._ibtnApply!==n&&(this._ibtnApply=n)},get_ibtnCalculate:function(){return this._ibtnCalculate},set_ibtnCalculate:function(n){this._ibtnCalculate!==n&&(this._ibtnCalculate=n)},get_ibtnCancel:function(){return this._ibtnCancel},set_ibtnCancel:function(n){this._ibtnCancel!==n&&(this._ibtnCancel=n)},get_trLoading:function(){return this._trLoading},set_trLoading:function(n){this._trLoading!==n&&(this._trLoading=n)},get_trError:function(){return this._trError},set_trError:function(n){this._trError!==n&&(this._trError=n)},get_trApply:function(){return this._trApply},set_trApply:function(n){this._trApply!==n&&(this._trApply=n)},get_trCalculate:function(){return this._trCalculate},set_trCalculate:function(n){this._trCalculate!==n&&(this._trCalculate=n)},addApply:function(n){this.get_events().addHandler("Apply",n)},removeApply:function(n){this.get_events().removeHandler("Apply",n)},onApply:function(){var n=this.get_events().getHandler("Apply");n&&n(this,Sys.EventArgs.Empty)},addCancel:function(n){this.get_events().addHandler("Cancel",n)},removeCancel:function(n){this.get_events().removeHandler("Cancel",n)},onCancel:function(){var n=this.get_events().getHandler("Cancel");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){$R_IBTN.addClick(this._ibtnCalculate,Function.createDelegate(this,this.doCalculate));$R_IBTN.addClick(this._ibtnCancel,Function.createDelegate(this,this.doCancel));$R_IBTN.addClick(this._ibtnApply,Function.createDelegate(this,this.doApply));Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||($R_IBTN.clearHandlers(this._ibtnCalculate),$R_IBTN.clearHandlers(this._ibtnCancel),$R_IBTN.clearHandlers(this._ibtnApply),this._lblCurrency=null,this._lblCurrency2=null,this._ibtnApply=null,this._ibtnCalculate=null,this._ibtnCancel=null,this._trLoading=null,this._trError=null,this._trApply=null,this._trCalculate=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.callBaseMethod(this,"dispose"))},show:function(n){$R_FN.showElement(this._element,n);n&&(this._ctlRelatedForm.getFieldDropDownData("ctlLCC_Funds"),this._ctlRelatedForm.getFieldDropDownData("ctlLCC_Product"),this.resetFields(),this.showResult(!1),this._ctlRelatedForm.showField("ctlLCC_LandedCost",!1),$R_FN.showElement(this._trApply,!1))},setCurrency:function(n,t){this._intCurrencyID=n;$R_FN.setInnerHTML(this._lblCurrency,t);$R_FN.setInnerHTML(this._lblCurrency2,t)},showLoading:function(n){$R_FN.showElement(this._trLoading,n);n&&(this._ctlRelatedForm.showField("ctlLCC_LandedCost",!1),$R_FN.showElement(this._trError,!1),$R_FN.showElement(this._trApply,!1),$R_FN.showElement(this._trCalculate,!1))},showError:function(n){$R_FN.showElement(this._trError,n);n&&(this._ctlRelatedForm.showField("ctlLCC_LandedCost",!1),$R_FN.showElement(this._trLoading,!1),$R_FN.showElement(this._trApply,!1),$R_FN.showElement(this._trCalculate,!0))},showResult:function(n){this._ctlRelatedForm.showField("ctlLCC_LandedCost",n);$R_FN.showElement(this._trApply,n);n&&($R_FN.showElement(this._trError,!1),$R_FN.showElement(this._trLoading,!1),$R_FN.showElement(this._trCalculate,!0))},validateForm:function(){var n=!0;return this.resetFields(),this._ctlRelatedForm.checkFieldEntered("ctlLCC_Quantity")||(n=!1),this._ctlRelatedForm.checkFieldNumeric("ctlLCC_Quantity")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlLCC_Cost")||(n=!1),this._ctlRelatedForm.checkFieldNumeric("ctlLCC_Cost")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlLCC_Funds")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlLCC_Date")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlLCC_Product")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlLCC_ShippingCost")||(n=!1),this._ctlRelatedForm.checkFieldNumeric("ctlLCC_ShippingCost")||(n=!1),n},resetFields:function(){this._ctlRelatedForm.resetFieldError("ctlLCC_Quantity");this._ctlRelatedForm.resetFieldError("ctlLCC_Cost");this._ctlRelatedForm.resetFieldError("ctlLCC_Funds");this._ctlRelatedForm.resetFieldError("ctlLCC_Date");this._ctlRelatedForm.resetFieldError("ctlLCC_Product");this._ctlRelatedForm.resetFieldError("ctlLCC_ShippingCost")},doCancel:function(){this.show(!1);this.onCancel()},doCalculate:function(){this.validateForm()&&(this.showLoading(!0),Rebound.GlobalTrader.Site.WebServices.CalculateLandedcost(this._ctlRelatedForm.getFieldValue("ctlLCC_Quantity"),this._ctlRelatedForm.getFieldValue("ctlLCC_Cost"),this._ctlRelatedForm.getFieldValue("ctlLCC_Funds"),this._ctlRelatedForm.getFieldValue("ctlLCC_Date"),this._ctlRelatedForm.getFieldValue("ctlLCC_ApplyDuty"),this._ctlRelatedForm.getFieldValue("ctlLCC_Product"),this._ctlRelatedForm.getFieldValue("ctlLCC_ShippingCost"),Function.createDelegate(this,this.doCalculateComplete),Function.createDelegate(this,this.doCalculateError)))},doCalculateComplete:function(n){this.showResult(!0);this._ctlRelatedForm.setFieldValue("ctlLCC_LandedCost",n);this._varValue=n},doCalculateError:function(){this.showError(!0)},doApply:function(){this.onApply()}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.LandedCostCalculator",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);