<%@ Control Language="C#" CodeBehind="Supplier.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" />
	</FieldsLeft>
    <FieldsRight>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNo"  />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
