//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class SalesOrdersReadyToShip : Base {

		protected Panel _pnlReady;
		protected SimpleDataTable _tblReady;
		protected PageHyperLink _lnkMore;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "SalesOrdersReadyToShip";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.SalesOrdersReadyToShip.SalesOrdersReadyToShip.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.SalesOrdersReadyToShip", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlReady", _pnlReady.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblReady", _tblReady.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlMore", FindContentControl("pnlMore").ClientID);
			_lnkMore.AddQueryStringVariable(QueryStringManager.QueryStringVariables.BypassSavedState, true);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblReady.Columns.Add(new SimpleDataColumn("SalesOrder", Unit.Pixel(65)));
			_tblReady.Columns.Add(new SimpleDataColumn("Supplier"));
			_tblReady.Columns.Add(new SimpleDataColumn("DatePromised", Unit.Pixel(75)));
			_lnkMore = (PageHyperLink)FindContentControl("lnkMore");
		}

		private void WireUpControls() {
			_pnlReady = (Panel)FindContentControl("pnlReady");
			_tblReady = (SimpleDataTable)FindContentControl("tblReady");
		}
	}
}