///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.MultiStep = function(element) { 
	Rebound.GlobalTrader.Site.Controls.MultiStep.initializeBase(this, [element]);
	this._intTotalSteps = 0;
};

Rebound.GlobalTrader.Site.Controls.MultiStep.prototype = {

	get_aryItemIDs: function() { return this._aryItemIDs; }, 	set_aryItemIDs: function(value) { if (this._aryItemIDs !== value)  this._aryItemIDs = value; }, 
	get_aryContentFormIDs: function() { return this._aryContentFormIDs; }, 	set_aryContentFormIDs: function(value) { if (this._aryContentFormIDs !== value)  this._aryContentFormIDs = value; }, 
	get_aryItemLinkIDs: function() { return this._aryItemLinkIDs; }, 	set_aryItemLinkIDs: function(value) { if (this._aryItemLinkIDs !== value)  this._aryItemLinkIDs = value; }, 
	get_arySeparatorIDs: function() { return this._arySeparatorIDs; }, 	set_arySeparatorIDs: function(value) { if (this._arySeparatorIDs !== value)  this._arySeparatorIDs = value; }, 
	get_aryExplainLabelIDs: function() { return this._aryExplainLabelIDs; }, 	set_aryExplainLabelIDs: function(value) { if (this._aryExplainLabelIDs !== value)  this._aryExplainLabelIDs = value; }, 
	get_pnlMultiStep: function() { return this._pnlMultiStep; }, 	set_pnlMultiStep: function(value) { if (this._pnlMultiStep !== value)  this._pnlMultiStep = value; }, 
	get_intCurrentStep: function() { return this._intCurrentStep; }, 	set_intCurrentStep: function(value) { if (this._intCurrentStep !== value)  this._intCurrentStep = value; }, 

	addStepChanged: function(handler) { this.get_events().addHandler("StepChanged", handler); },
	removeStepChanged: function(handler) { this.get_events().removeHandler("StepChanged", handler); },
	onStepChanged: function() {
		var handler = this.get_events().getHandler("StepChanged");
		if (handler) handler(this, new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode, this._errorMessage, this._result, this._url));
	},	

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.MultiStep.callBaseMethod(this, "initialize");
		this._intTotalSteps = this._aryContentFormIDs.length;
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._aryItemIDs = null;
		this._aryContentFormIDs = null;
		this._aryItemLinkIDs = null;
		this._arySeparatorIDs = null;
		this._aryExplainLabelIDs = null;
		this._pnlMultiStep = null;
		this._intTotalSteps = null;
		Rebound.GlobalTrader.Site.Controls.MultiStep.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	gotoStep: function(intStep) {
		this._intCurrentStep = intStep;
		for (var i = 1, l = this._intTotalSteps; i <= l; i++) {
			$R_FN.showElement($get(this._aryContentFormIDs[i-1]), i == this._intCurrentStep);
			$R_FN.showElement($get(this._aryExplainLabelIDs[i-1]), i == this._intCurrentStep);
			var strCurrentTime = this.currentStepTimeString(i);
			var tdItem = $get(this._aryItemIDs[i-1]);
			if (tdItem) tdItem.className = String.format("step step{0}", strCurrentTime);
			var tdSep = $get(this._arySeparatorIDs[i-1]);
			if (tdSep) tdSep.className = String.format("sep sep_{0}_{1}", strCurrentTime, this.nextStepTimeString(i));
			var anc = $get(this._aryItemLinkIDs[i-1]);
			anc.setAttribute("onclick", "void(0);");
			if (strCurrentTime == "Past") anc.setAttribute("onclick", String.format("$find('{0}').gotoStep({1});" ,this.get_element().id , i));
			tdItem = null;
			tdSep = null;
			anc = null;
		}
		if ($R_FN.isElementVisible(this._pnlMultiStep)) $R_FN.scrollPageToElement(this._pnlMultiStep, -95);
		this.onStepChanged();
	},
	
	nextStep: function() {
		var intStep = this._intCurrentStep + 1;
		if (intStep > this._intTotalSteps) intStep = this._intTotalSteps;
		this.gotoStep(intStep);
	},
	
	prevStep: function() {
		var intStep = this._intCurrentStep - 1;
		if (intStep < 1) intStep = 1;
		this.gotoStep(intStep);
	},
	
	currentStepTimeString: function(i) {
		var strOut = "Future";
		if (i == this._intCurrentStep) strOut = "Current";
		if (i < this._intCurrentStep) strOut = "Past";
		return strOut;
	},

	nextStepTimeString: function(i) {
		i += 1;
		return (i > this._intTotalSteps) ? "End" : this.currentStepTimeString(i);
	},
	
	disableStep: function(i) {
		var anc = $get(this._aryItemLinkIDs[i-1]);
		if (anc) anc.setAttribute("onclick", "void(0);");
		var tdSep = $get(this._aryItemIDs[i-1]);
		if (tdSep) tdSep.className += " stepDisabled";
		anc = null;
		tdSep = null;
	},
	
	showSteps: function(bln) {
		$R_FN.showElement(this._pnlMultiStep, bln);
	},
	
	showExplainLabel: function(bln){
		$R_FN.showElement($get(this._aryExplainLabelIDs[this._intCurrentStep - 1]), bln);
	}
		
};

Rebound.GlobalTrader.Site.Controls.MultiStep.registerClass("Rebound.GlobalTrader.Site.Controls.MultiStep", Sys.UI.Control, Sys.IDisposable);