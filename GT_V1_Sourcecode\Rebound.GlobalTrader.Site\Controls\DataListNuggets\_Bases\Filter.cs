using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.ComponentModel;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Text;
using System.Collections.Generic;
using System.Collections.Specialized;
using Rebound.GlobalTrader.Site.Controls.Nuggets;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {

	/// <summary>
	/// Design Template for Filters
	/// </summary>
	[DefaultProperty("")]
	[ToolboxData("<{0}:Filter runat=server></{0}:Filter>")]
	public class Filter : WebControl, INamingContainer {

		#region Locals

		internal Panel pnlOuter;
		internal Panel pnlContent;
		internal Panel pnlHeader;
		public Table tblColLeft;
		public Table tblColRight;
		internal IconButton _ibtnReset;
		internal IconButton _ibtnApply;
		internal IconButton _ibtnOff;

		#endregion

		#region Properties

		/// <summary>
		/// Title
		/// </summary>
		private string _strTitleText = Functions.GetGlobalResource("misc", "FilterResults");
		public string TitleText {
			get { return _strTitleText; }
			set { _strTitleText = value; }
		}

		/// <summary>
		/// Left Fields container
		/// </summary>
		private ITemplate _tmpFieldsLeft = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate FieldsLeft {
			get { return _tmpFieldsLeft; }
			set { _tmpFieldsLeft = value; }
		}

		/// <summary>
		/// Right Fields container
		/// </summary>
		private ITemplate _tmpFieldsRight = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate FieldsRight {
			get { return _tmpFieldsRight; }
			set { _tmpFieldsRight = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
		}

		/// <summary>
		/// CreateChildControls
		/// </summary>
		protected override void CreateChildControls() {

			//containers
			pnlOuter = ControlBuilders.CreatePanelInsideParent(this, "dataFilter");
			ControlBuilders.CreatePanelInsideParent(pnlOuter, "dataFilterTL");
			ControlBuilders.CreatePanelInsideParent(pnlOuter, "dataFilterTR");
			pnlContent = ControlBuilders.CreatePanelInsideParent(pnlOuter, "dataFilterInner");

			//header
			pnlHeader = ControlBuilders.CreatePanelInsideParent(pnlContent, "dataFilterHeader");
			HtmlContainerControl h5 = ControlBuilders.CreateHtmlGenericControlInsideParent(pnlHeader, "h5");
			ControlBuilders.CreateLiteralInsideParent(h5, _strTitleText);
			Panel pnlButtons = ControlBuilders.CreatePanelInsideParent(pnlHeader, "buttons");

			//Off button
			_ibtnOff = new IconButton();
			_ibtnOff.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			_ibtnOff.IconGroup = IconButton.IconGroupList.Filter;
			_ibtnOff.IconTitleResource = "Off";
			_ibtnOff.ID = "ibtnOff";
			pnlButtons.Controls.Add(_ibtnOff);

			//Reset button
			_ibtnReset = new IconButton();
			_ibtnReset.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			_ibtnReset.IconGroup = IconButton.IconGroupList.Filter;
			_ibtnReset.IconTitleResource = "Reset";
			_ibtnReset.ID = "ibtnReset";
			pnlButtons.Controls.Add(_ibtnReset);

			//Apply button
			_ibtnApply = new IconButton();
			_ibtnApply.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			_ibtnApply.IconGroup = IconButton.IconGroupList.Filter;
			_ibtnApply.IconTitleResource = "Apply";
			_ibtnApply.ID = "ibtnApply";
			pnlButtons.Controls.Add(_ibtnApply);

			//fields
			Table tblTwoCols = ControlBuilders.CreateTableInsideParent(pnlContent, "twoCols");
			tblTwoCols.Rows.Add(new TableRow());
			tblTwoCols.Rows[0].Cells.Add(new TableCell());
			tblTwoCols.Rows[0].Cells[0].CssClass = "col1";
			tblColLeft = ControlBuilders.CreateTableInsideParent(tblTwoCols.Rows[0].Cells[0], "dataItems");
			tblTwoCols.Rows[0].Cells.Add(new TableCell());
			tblTwoCols.Rows[0].Cells[1].CssClass = "col2";
			tblColRight = ControlBuilders.CreateTableInsideParent(tblTwoCols.Rows[0].Cells[1], "dataItems");

			//populate left fields container
			if (_tmpFieldsLeft != null) {
				Container cbc = new Container();
				_tmpFieldsLeft.InstantiateIn(cbc);
				for (int i = 0; i < cbc.Controls.Count; i++) {
					if (cbc.Controls[i] is TableRow) tblColLeft.Controls.Add(cbc.Controls[i]);
				}
				cbc.Dispose();
			}

			//populate right fields container
			if (_tmpFieldsRight != null) {
				Container cbc = new Container();
				_tmpFieldsRight.InstantiateIn(cbc);
				for (int i = 0; i < cbc.Controls.Count; i++) {
					if (cbc.Controls[i] is TableRow) tblColRight.Controls.Add(cbc.Controls[i]);
				}
				cbc.Dispose();
			}

			//footer
			ControlBuilders.CreatePanelInsideParent(pnlOuter, "dataFilterBL");
			ControlBuilders.CreatePanelInsideParent(pnlOuter, "dataFilterBR");
			ControlBuilders.CreatePanelInsideParent(pnlOuter, "dataFilterFooter");

			base.CreateChildControls();
		}

		#endregion

		/// <summary>
		/// Explicit call to EnsureChildControls
		/// </summary>
		internal void MakeChildControls() {
			EnsureChildControls();
		}

		/// <summary>
		/// Finds a control in the left or right fiels collection
		/// </summary>
		public object FindFieldControl(string strControlID) {
			EnsureChildControls();
			Control ctl = Functions.FindControlRecursive(tblColLeft, strControlID);
			if (ctl == null) ctl = Functions.FindControlRecursive(tblColRight, strControlID);
			return ctl;
		}
        public FilterDataItemRows.DropDown FindFieldControl_DropDown(string strControlID) {
            Object obj = FindFieldControl(strControlID);
            if (obj == null) return null;
            return (FilterDataItemRows.DropDown)obj;
        }


	}
}