﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL {
		public partial class EntertainmentType : BizObject {
		
		#region Properties

		protected static DAL.EntertainmentTypeElement Settings {
			get { return Globals.Settings.EntertainmentType; }
		}

		/// <summary>
		/// EntertainmentTypeId
		/// </summary>
		public System.Int32 EntertainmentTypeId { get; set; }		
		/// <summary>
		/// Name
		/// </summary>
		public System.String Name { get; set; }		
		/// <summary>
		/// Inactive
		/// </summary>
		public System.Boolean Inactive { get; set; }		
		/// <summary>
		/// UpdatedBy
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }		
		/// <summary>
		/// DLUP
		/// </summary>
		public System.DateTime? DLUP { get; set; }		

		#endregion
		
		#region Methods
		
		/// <summary>
		/// Delete
		/// Calls [usp_delete_EntertainmentType]
		/// </summary>
		public static bool Delete(System.Int32? entertainmentTypeId) {
			return Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.Delete(entertainmentTypeId);
		}
        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_EntertainmentType]
        /// </summary>
        public static List<EntertainmentType> DropDown()
        {
            List<EntertainmentTypeDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.DropDown();
            if (lstDetails == null)
            {
                return new List<EntertainmentType>();
            }
            else
            {
                List<EntertainmentType> lst = new List<EntertainmentType>();
                foreach (EntertainmentTypeDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.EntertainmentType obj = new Rebound.GlobalTrader.BLL.EntertainmentType();
                    obj.EntertainmentTypeId = objDetails.EntertainmentTypeId;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_EntertainmentType]
        /// </summary>
        public static Int32 Insert(System.String name) {
			Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.Insert(name);
			return objReturn;
		}

		public static Int32 Insert2(System.String name)
		{
			Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.Insert(name);
			return objReturn;
		}
		/// <summary>
		/// Insert (without parameters)
		/// Calls [usp_insert_EntertainmentType]
		/// </summary>
		public Int32 Insert() {
			return Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.Insert(Name);
		}
		/// <summary>
		/// Get
		/// Calls [usp_select_EntertainmentType]
		/// </summary>
		public static EntertainmentType Get(System.Int32? entertainmentTypeId) {
			Rebound.GlobalTrader.DAL.EntertainmentTypeDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.Get(entertainmentTypeId);
			if (objDetails == null) {
				return null;
			} else {
				EntertainmentType obj = new EntertainmentType();
				obj.EntertainmentTypeId = objDetails.EntertainmentTypeId;
				obj.Name = objDetails.Name;
				obj.Inactive = objDetails.Inactive;
				obj.UpdatedBy = objDetails.UpdatedBy;
				obj.DLUP = objDetails.DLUP;
				objDetails = null;
				return obj;
			}
		}
		/// <summary>
		/// GetList
		/// Calls [usp_selectAll_EntertainmentType]
		/// </summary>
		public static List<EntertainmentType> GetList() {
			List<EntertainmentTypeDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.GetList();
			if (lstDetails == null) {
				return new List<EntertainmentType>();
			} else {
				List<EntertainmentType> lst = new List<EntertainmentType>();
				foreach (EntertainmentTypeDetails objDetails in lstDetails) {
					Rebound.GlobalTrader.BLL.EntertainmentType obj = new Rebound.GlobalTrader.BLL.EntertainmentType();
					obj.EntertainmentTypeId = objDetails.EntertainmentTypeId;
					obj.Name = objDetails.Name;
					obj.Inactive = objDetails.Inactive;
					obj.UpdatedBy = objDetails.UpdatedBy;
					obj.DLUP = objDetails.DLUP;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}
		/// <summary>
		/// Update
		/// Calls [usp_update_EntertainmentType]
		/// </summary>
		public static bool Update(System.String name, System.Int32? entertainmentTypeId, System.Boolean? inactive, System.Int32? updatedBy) {
			return Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.Update(name, entertainmentTypeId, inactive, updatedBy);
		}
		/// <summary>
		/// Update (without parameters)
		/// Calls [usp_update_EntertainmentType]
		/// </summary>
		public bool Update() {
			return Rebound.GlobalTrader.DAL.SiteProvider.EntertainmentType.Update(Name, EntertainmentTypeId, Inactive, UpdatedBy);
		}

        private static EntertainmentType PopulateFromDBDetailsObject(EntertainmentTypeDetails obj) {
            EntertainmentType objNew = new EntertainmentType();
			objNew.EntertainmentTypeId = obj.EntertainmentTypeId;
			objNew.Name = obj.Name;
			objNew.Inactive = obj.Inactive;
			objNew.UpdatedBy = obj.UpdatedBy;
			objNew.DLUP = obj.DLUP;
            return objNew;
        }
		
		#endregion
		
	}
}