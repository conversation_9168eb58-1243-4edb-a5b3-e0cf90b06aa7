﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER   FUNCTION [dbo].[ufn_convert_to_HUB_currency] (  
      @Value FLOAT
    , @CurrencyId int
	, @ExchangeRateDate datetime
)
/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-208827]		An.TranTan		01-Oct-2024		Create		Convert KUB assistant PO/IPO buy price to DMCC currency
[US-208827]		An.TranTan		20-Oct-2024		Update		Set default exchange rate = 1
===========================================================================================
*/
RETURNS FLOAT

AS BEGIN  
	DECLARE @ExchangeRate FLOAT = 1;
	DECLARE @TargetCurrencyCode NVARCHAR(5);
	--target currency code
	SELECT @TargetCurrencyCode = CurrencyCode FROM tbCurrency WITH(NOLOCK) WHERE CurrencyId = @CurrencyId;

	--get exchange rate for Client Currency in HUB side
	SELECT TOP 1 @ExchangeRate = dbo.ufn_get_exchange_rate(cr.CurrencyId, @ExchangeRateDate)
	FROM dbo.tbCurrency cr WITH (NOLOCK)
	WHERE cr.ClientNo = 114 AND cr.CurrencyCode = @TargetCurrencyCode AND cr.Inactive = 0

	--convert to HUB price
	RETURN  @Value / @ExchangeRate;
END 

/*
	declare @result nvarchar(200);
	SET @result = dbo.ufn_convert_to_HUB_currency(10, 2, GETDATE())
	SELECT @result as 'Converted value'
*/
  
GO


