Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.prototype={get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.updateFilterVisibility();this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/AllStock";this._strDataObject="AllStock";Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.getData()},dispose:function(){this.isDisposed||(this._blnPOHub=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this.updateFilterVisibility();this.getData()},setupDataCall:function(){var n="GetData_";switch(this._intCurrentTab){case 0:n+="All";break;case 1:n+="Available";break;case 2:n+="Quarantined"}this._objData.set_DataAction(n);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=n.IsPoHub==!0||this._IsGlobalLogin==!0?[$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(n.ID,n.Part,n.ROHS),$R_FN.setCleanTextValue(n.SupplierPart)),$R_FN.writeDoubleCellValue(n.InStock,n.OnOrder),$R_FN.writeDoubleCellValue(n.Allocated,n.Available),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Warehouse),n.Location),$R_FN.writeDoubleCellValue($RGT_nubButton_Lot(n.LotNo,n.Lot),$RGT_nubButton_PurchaseOrder(n.PurchaseOrderNo,n.PurchaseOrder)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Status),$RGT_nubButton_CRMA(n.CRMANo,n.CRMA)),n.ClientName]:[$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(n.ID,n.Part,n.ROHS),$R_FN.setCleanTextValue(n.SupplierPart)),$R_FN.writeDoubleCellValue(n.InStock,n.OnOrder),$R_FN.writeDoubleCellValue(n.Allocated,n.Available),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Warehouse),n.Location),$R_FN.writeDoubleCellValue($RGT_nubButton_Lot(n.LotNo,n.Lot),$RGT_nubButton_PurchaseOrder(n.PurchaseOrderNo,n.PurchaseOrder)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Status),$RGT_nubButton_CRMA(n.CRMANo,n.CRMA)),n.ClientName],this._table.addRow(i,n.ID),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlClientName").show(this._blnPOHub||this._IsGlobalLogin)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.AllStock",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);