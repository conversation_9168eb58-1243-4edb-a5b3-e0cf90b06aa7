﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-204678]		An.TranTan			14-Aug-2024		UPDATE			Get DebitAmount, DebitNoteRef
[US-204678]		An.TranTan			14-Aug-2024		UPDATE			Temporary change TaxNo to fix bug Tax dropdown not selected value in Supplier Invoice Edit screen
===========================================================================================
*/
CREATE OR ALTER View vwSupplierInvoice
AS                          
SELECT                           
   si.SupplierInvoiceId                         
  ,si.SupplierInvoiceNumber                         
, si.CompanyNo                          
, si.ClientNo                          
, si.SupplierInvoiceDate                          
, si.SupplierCode                          
, si.SupplierName                          
, CASE        
    WHEN si.ISOCRGEN = 1         
 THEN cr.GlobalCurrencyNo        
    ELSE si.CurrencyNo        
 END as CurrencyNo        
, si.InvoiceAmount                          
, si.GoodsValue                          
, si.Tax                          
, si.DeliveryCharge                          
, isnull(si.BankFee,0)  BankFee                         
, si.CreditCardFee                          
, si.Notes                          
, si.SecondRef                          
, si.Narrative                          
, si.CanbeExported                          
, si.Exported                          
, si.URNNumber                          
, si.DLUP                          
, si.UpdatedBy                      
, isnull(gi.PurchaseOrderNo, po.PurchaseOrderId) AS PurchaseOrderNo                            
, po.PurchaseOrderNumber                        
, si.CurrencyCode            
--, case when si.Tax=0 then 68 when si.Tax>0 then 66 else si.TaxNo end as TaxNo --TODO: confirm with Abhinav on this behavior           
, si.TaxNo              
,  si.TaxCode           
, tax.TaxName          
, si.StatusReasonNo           
, sr.Name as StatusReason
, si.DebitAmount
, si.DebitNoteRef
FROM tbSupplierInvoice si                      
LEFT JOIN tbSupplierInvoiceLine sil on  si.SupplierInvoiceID=sil.SupplierInvoiceNo                          
LEFT JOIN  tbGoodsIn gi ON sil.GoodsInNo = gi.GoodsInId                            
LEFT JOIN tbPurchaseOrder po ON gi.PurchaseOrderNo = po.PurchaseOrderId                     
LEFT JOIN tbTax tax on si.TaxNo=tax.TaxId          
LEFT JOIN tbStatusReason sr on si.StatusReasonNo = sr.StatusReasonId        
LEFT JOIN tbcurrency cr on si.currencyNo = cr.CurrencyId  