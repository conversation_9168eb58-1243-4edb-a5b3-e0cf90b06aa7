﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

using Rebound.GlobalTrader;

namespace Rebound.GlobalTrader.DAL
{
	
	public abstract class RevenueTargetProvider : DataAccess {
		static private RevenueTargetProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public RevenueTargetProvider Instance {
			get {
				if (_instance == null) _instance = (RevenueTargetProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.RevenueTargets.ProviderType));
				return _instance;
			}
		}
		public RevenueTargetProvider() {
			this.ConnectionString = Globals.Settings.DivisionTargets.ConnectionString;
		}

		#region Method Registrations

		/// <summary>
		/// KPI_GetDivisionRevenueTargetByID
		/// </summary>
		/// <param name="divisionNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public abstract List<RevenueTargetDetails> GetDivisionRevenueByID(System.Int32? divisionNo, System.Int32 yearNo);

		/// <summary>
		/// [KPI_GetSalesRevenueTargetByTeamID]
		/// </summary>
		/// <param name="salemanNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public abstract List<RevenueTargetDetails> GetSalesRevenueTargetByTeamID(System.Int32? teamNo, System.Int32 yearNo);
		/// <summary>
		/// [KPI_GetCustomerRevenueTargetBySalesManNo]
		/// </summary>
		/// <param name="teamNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public abstract List<RevenueTargetDetails> GetSalesRevenueTargetBySalesID(System.Int32? salemanNo, System.Int32 yearNo);

		/// <summary>
		/// KPI_GetSalesRevenueTargetByID
		/// </summary>
		/// <param name="teamTargetNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public abstract List<RevenueTargetDetails> GetSalesRevenueByID(System.Int32? teamTargetNo, System.Int32 yearNo);

		/// <summary>
		/// KPI_GetCustomerRevenueTargetByID
		/// </summary>
		/// <param name="salesTargetNo"></param>
		/// <param name="yearNo"></param>
		/// <returns></returns>
		public abstract List<RevenueTargetDetails> GetCustomerRevenueByID(System.Int32? salesTargetNo, System.Int32 yearNo);
		/// <summary>
		/// Update
		/// Calls [usp_update_DivisionTeamTarget]
		/// </summary>
		public abstract bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue, System.Int32? updatedBy, System.Int32? Year, System.Int32? divisionNo);

		/// <summary>
		/// Update
		/// Calls [usp_saveAllDivisionTeamTarget]
		/// </summary>
		public abstract bool SaveAllDivisionTeamTarget(System.Int32? Year, System.Int32? divisionNo, System.Int32? updatedBy);

		#endregion

		/// <summary>
		/// Returns a new DivisionDetails instance filled with the DataReader's current record data
		/// </summary>        
		protected virtual DivisionDetails GetDivisionFromReader(DbDataReader reader) {
			DivisionDetails division = new DivisionDetails();
			if (reader.HasRows) {
				division.DivisionId = GetReaderValue_Int32(reader, "DivisionId", 0); //From: [Table]
				division.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0); //From: [Table]
				division.DivisionName = GetReaderValue_String(reader, "DivisionName", ""); //From: [usp_select_Credit]
				division.AddressNo = GetReaderValue_NullableInt32(reader, "AddressNo", null); //From: [Table]
				division.Manager = GetReaderValue_NullableInt32(reader, "Manager", null); //From: [Table]
				division.Budget = GetReaderValue_NullableDouble(reader, "Budget", null); //From: [Table]
				division.Telephone = GetReaderValue_String(reader, "Telephone", ""); //From: [Table]
				division.Fax = GetReaderValue_String(reader, "Fax", ""); //From: [Table]
				division.EMail = GetReaderValue_String(reader, "EMail", ""); //From: [Table]
				division.Notes = GetReaderValue_String(reader, "Notes", ""); //From: [usp_select_Address_DefaultBilling_for_Company]
				division.URL = GetReaderValue_String(reader, "URL", ""); //From: [Table]
				division.Inactive = GetReaderValue_Boolean(reader, "Inactive", false); //From: [Table]
				division.HasDocumentHeaderImage = GetReaderValue_Boolean(reader, "HasDocumentHeaderImage", false); //From: [Table]
				division.UseCompanyHeaderForInvoice = GetReaderValue_Boolean(reader, "UseCompanyHeaderForInvoice", false); //From: [Table]
				division.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null); //From: [Table]
				division.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue); //From: [Table]
				division.ManagerName = GetReaderValue_String(reader, "ManagerName", ""); //From: [usp_select_Division]
				division.NumberOfMembers = GetReaderValue_NullableInt32(reader, "NumberOfMembers", null); //From: [usp_selectAll_Division_for_Client]
			}
			return division;
		}
	
		/// <summary>
		/// Returns a collection of DivisionDetails objects with the data read from the input DataReader
		/// </summary>                
		protected virtual List<DivisionDetails> GetDivisionCollectionFromReader(DbDataReader reader) {
			List<DivisionDetails> divisions = new List<DivisionDetails>();
			while (reader.Read()) divisions.Add(GetDivisionFromReader(reader));
			return divisions;
		}
		
		
	}
}