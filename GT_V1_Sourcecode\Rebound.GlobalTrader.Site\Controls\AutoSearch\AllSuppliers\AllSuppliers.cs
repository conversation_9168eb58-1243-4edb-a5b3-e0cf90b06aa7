using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:AllSuppliers runat=server></{0}:AllSuppliers>")]
	public class AllSuppliers : Base {

		#region Properties

		/// <summary>
		/// Should supplier no be used on actions rather than the CompanyNo? 
		/// Only relevant when ResultsActionType = RaiseEvent
		/// </summary>
		private bool _blnUseSupplierNo = false;
		public bool UseSupplierNo {
			get { return _blnUseSupplierNo; }
			set { _blnUseSupplierNo = value; }
		}

		#endregion

		#region Overrides
		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.AutoSearch.AllSuppliers.AllSuppliers.js");
			SetAutoSearchType("AllSuppliers");
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			CharactersToEnterBeforeSearch = 2;
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}
		#endregion

		/// <summary>
		/// SetupScriptDescriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.AutoSearch.AllSuppliers", ClientID);
			_scScriptControlDescriptor.AddProperty("blnUseSupplierNo", _blnUseSupplierNo);
		}
	}
}