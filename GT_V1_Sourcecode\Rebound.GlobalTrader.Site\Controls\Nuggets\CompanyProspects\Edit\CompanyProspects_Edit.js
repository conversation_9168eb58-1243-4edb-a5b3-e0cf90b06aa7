Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.initializeBase(this,[n]);this._intCustomerID=-1;this._intCRMProspectID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intCustomerID=null,this._ctlSelectIndustryType&&this._ctlSelectIndustryType.dispose(),this._ctlSelectIndustryType=null,this._ctlSelectEntertainmentType&&this._ctlSelectEntertainmentType.dispose(),this._ctlSelectEntertainmentType=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._ctlSelectIndustryType=$find(this.getField("ctlIndustryType").ControlID));this.getFieldControl("ctlProspectType")._CompanyProspectsDDLType="ProspectType";this.getFieldDropDownData("ctlProspectType");this.getFieldControl("ctlElectronicSpend")._CompanyProspectsDDLType="ElectronicSpend";this.getFieldDropDownData("ctlElectronicSpend");this.getFieldControl("ctlFrequencyOfPurchase")._CompanyProspectsDDLType="FrequencyOfPurchase";this.getFieldDropDownData("ctlFrequencyOfPurchase");this.getFieldControl("ctlTurnover")._CompanyProspectsDDLType="Turnover";this.getFieldDropDownData("ctlTurnover")},saveClicked:function(){var n,t;this.validateForm()&&(n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/CompanyProspects"),n.set_DataObject("CompanyProspects"),n.set_DataAction("SaveEdit"),n.addParameter("id",this._intCustomerID),n.addParameter("CRMProspectId",this._intCRMProspectID),n.addParameter("ProspectTypeId",this.getFieldValue("ctlProspectType")),t=$get("ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel_0").checked==!0?!0:$get("ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel_1").checked==!0?!1:null,n.addParameter("IscrmProspectBoardLevel",t!=null?t.toString():null),t=null,t=$get("ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly_0").checked==!0?!0:$get("ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly_1").checked==!0?!1:null,n.addParameter("IsFinalAssembly",t!=null?t.toString():null),n.addParameter("EndCustomer",this.getFieldValue("ctlEndCustomer")),n.addParameter("IsIndustry",0),n.addParameter("LimitedEstimate",this.getFieldValue("ctlLimitedEstimate")),n.addParameter("HealthRating",this.getFieldValue("ctlHealthRating")),n.addParameter("ElectronicSpendId",this.getFieldValue("ctlElectronicSpend")),n.addParameter("FrequencyOfPurchaseId",this.getFieldValue("ctlFrequencyOfPurchase")),n.addParameter("Commoditiestext",this.getFieldValue("ctlCommodities")),n.addParameter("TurnoverId",this.getFieldValue("ctlTurnover")),n.addParameter("IndustryTypes",this._ctlSelectIndustryType.getValuesAsString(!0)),n.addDataOK(Function.createDelegate(this,this.saveEditComplete)),n.addError(Function.createDelegate(this,this.saveEditError)),n.addTimeout(Function.createDelegate(this,this.saveEditError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null)},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(this.onSaveComplete(),$("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_imgRefresh").trigger("click")):(n._result.Message&&(this._strErrorMessage=n._result.Message),this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);