<%@ Control Language="C#" CodeBehind="StockItemsBelowTheMinimumQuantity.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.StockItemsBelowTheMinimumQuantity" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<content>
		<asp:Panel ID="pnlBelow" runat="server">
			<h5><%=Functions.GetGlobalResource("misc", "Below")%></h5>
			<ReboundUI:FlexiDataTable ID="tblBelow" runat="server" AllowSelection="false" />
		</asp:Panel>
		<br />...
	</content>
</ReboundUI_Nugget:DesignBase>
