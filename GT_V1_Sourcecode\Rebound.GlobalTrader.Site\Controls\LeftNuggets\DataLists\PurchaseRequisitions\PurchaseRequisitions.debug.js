///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseRequisitions = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseRequisitions.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseRequisitions.prototype = {

	initialize: function() {
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this._strPathToData = "controls/DataListNuggets/PurchaseRequisitions";
		this._strDataObject = "PurchaseRequisitions";
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseRequisitions.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseRequisitions.callBaseMethod(this, "dispose");
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var strData = String.format("<a href=\"{0}\"><b>{1}</b> - {2}", $RGT_gotoURL_PurchaseRequisition(row.ID), row.No, $R_FN.setCleanTextValue(row.CM));
			if (row.Part.length > 0) strData += String.format("<br />{0} x {1}</a>", row.Quantity, $R_FN.writePartNo(row.Part, row.ROHS));
			strData += "</a>";
			this._tbl.addRow([ strData ], row.ID, false);
			strData = null; row = null;
		}
	}

};
Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseRequisitions.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseRequisitions", Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base, Sys.IDisposable);
