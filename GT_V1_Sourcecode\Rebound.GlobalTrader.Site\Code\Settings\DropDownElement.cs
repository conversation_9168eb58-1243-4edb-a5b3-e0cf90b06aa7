using System;
using System.Data;
using System.Configuration;
using System.Web.Configuration;
using System.Web;
using System.Web.UI;

namespace Rebound.GlobalTrader.Site.Settings {
	public class DropDownElement : ConfigurationElement {

		[ConfigurationProperty("id", IsRequired = true)]
		public int ID {
			get { return Convert.ToInt32(this["id"]); }
		}

		[ConfigurationProperty("name", IsRequired = true)]
		public string Name {
			get { return this["name"] as string; }
		}

	}
}
