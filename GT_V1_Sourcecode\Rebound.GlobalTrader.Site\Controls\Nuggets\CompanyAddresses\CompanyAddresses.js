Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.initializeBase(this,[n]);this._intCompanyID=-1;this._intCompanyAddressID=-1;this._strCompanyName="";this._blnLineLoaded=!1;this._blnEditHubSupplier=!0;this._intCompanyAddressIDNew=-1;this._inactive=!1;this._CompanyAddressVatNo="";this._companyVatNo=""};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intCompanyAddressID:function(){return this._intCompanyAddressID},set_intCompanyAddressID:function(n){this._intCompanyAddressID!==n&&(this._intCompanyAddressID=n)},get_strAddressType:function(){return this._strAddressType},set_strAddressType:function(n){this._strAddressType!==n&&(this._strAddressType=n)},get_tblAddresses:function(){return this._tblAddresses},set_tblAddresses:function(n){this._tblAddresses!==n&&(this._tblAddresses=n)},get_lblType:function(){return this._lblType},set_lblType:function(n){this._lblType!==n&&(this._lblType=n)},get_ibtnDefaultBill:function(){return this._ibtnDefaultBill},set_ibtnDefaultBill:function(n){this._ibtnDefaultBill!==n&&(this._ibtnDefaultBill=n)},get_ibtnDefaultShip:function(){return this._ibtnDefaultShip},set_ibtnDefaultShip:function(n){this._ibtnDefaultShip!==n&&(this._ibtnDefaultShip=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnCease:function(){return this._ibtnCease},set_ibtnCease:function(n){this._ibtnCease!==n&&(this._ibtnCease=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_pnlLoadingAddressInfo:function(){return this._pnlLoadingAddressInfo},set_pnlLoadingAddressInfo:function(n){this._pnlLoadingAddressInfo!==n&&(this._pnlLoadingAddressInfo=n)},get_pnlAddressError:function(){return this._pnlAddressError},set_pnlAddressError:function(n){this._pnlAddressError!==n&&(this._pnlAddressError=n)},get_pnlAddressInfo:function(){return this._pnlAddressInfo},set_pnlAddressInfo:function(n){this._pnlAddressInfo!==n&&(this._pnlAddressInfo=n)},get_lblAddressName:function(){return this._lblAddressName},set_lblAddressName:function(n){this._lblAddressName!==n&&(this._lblAddressName=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_blnCanEditTax:function(){return this._blnCanEditTax},set_blnCanEditTax:function(n){this._blnCanEditTax!==n&&(this._blnCanEditTax=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/CompanyAddresses";this._strDataObject="CompanyAddresses";this.addRefreshEvent(Function.createDelegate(this,this.getData));this.addGetDataClicked(Function.createDelegate(this,this.initialGetData));this._tblAddresses.addSelectedIndexChanged(Function.createDelegate(this,this.tblAddresses_SelectedIndexChanged));(this._ibtnAdd||this._ibtnEdit)&&(this._ibtnAdd&&$R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._ibtnEdit&&$R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmAddEdit=$find(this._aryFormIDs[0]),this._frmAddEdit.addCancel(Function.createDelegate(this,this.cancelAddEdit)),this._frmAddEdit.addSaveComplete(Function.createDelegate(this,this.saveAddEditComplete)),this._frmAddEdit._intCompanyID=this._intCompanyID,this._frmAddEdit._blnCanEditTax=this._blnCanEditTax);(this._ibtnCease||this._ibtnDefaultBill)&&(this._ibtnCease&&$R_IBTN.addClick(this._ibtnCease,Function.createDelegate(this,this.showCeaseForm)),this._ibtnDefaultBill&&$R_IBTN.addClick(this._ibtnDefaultBill,Function.createDelegate(this,this.showDefaultBillForm)),this._ibtnDefaultShip&&$R_IBTN.addClick(this._ibtnDefaultShip,Function.createDelegate(this,this.showDefaultShipForm)),this._frmConfirm=$find(this._aryFormIDs[1]),this._frmConfirm.addCancel(Function.createDelegate(this,this.hideConfirmForm)),this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveCeaseComplete)),this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm)),this._frmConfirm._intCompanyID=this._intCompanyID,this._frmConfirm._strAddressType=this._strAddressType)},dispose:function(){this.isDisposed||(this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnCease&&$R_IBTN.clearHandlers(this._ibtnCease),this._ibtnDefaultBill&&$R_IBTN.clearHandlers(this._ibtnDefaultBill),this._ibtnDefaultShip&&$R_IBTN.clearHandlers(this._ibtnDefaultShip),this._frmAddEdit&&this._frmAddEdit.dispose(),this._frmConfirm&&this._frmConfirm.dispose(),this._tblAddresses&&this._tblAddresses.dispose(),this._frmAddEdit=null,this._frmConfirm=null,this._tblAddresses=null,this._lblType=null,this._ibtnDefaultBill=null,this._ibtnDefaultShip=null,this._ibtnAdd=null,this._ibtnCease=null,this._ibtnEdit=null,this._pnlLoadingAddressInfo=null,this._pnlAddressError=null,this._pnlAddressInfo=null,this._lblAddressName=null,this._blnCanEditTax=null,this._blnEditHubSupplier=null,this._inactive=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.callBaseMethod(this,"dispose"))},initialGetData:function(){this.showContent(!0);this.getData();this.getCompanyInactive()},getData:function(){this.getData_Start();this.enableEditButtons(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetAddressList");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var i,r;if(this.getDataOK_Start(),i=n._result,this._tblAddresses.clearTable(),i.Addresses){for(r=0;r<i.Addresses.length;r++){var t=i.Addresses[r],u=[$R_FN.setCleanTextValue(t.Name),$R_FN.setCleanTextValue(t.Long),t.DefaultBill?$R_RES.Yes:"-",t.DefaultShip?$R_RES.Yes:"-"],f=t.DefaultBill||t.DefaultShip?"defaultAddress":"";this._tblAddresses.addRow(u,t.ID,t.ID==this._intCompanyAddressID,null,f);t=null}this._strDefaultAddress=i.DefaultAddress}this._tblAddresses.resizeColumns();this._intCompanyAddressID>0&&this.getAddressData();this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCompanyInactive:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCompanyDetailInactive");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getCompanyInactiveOK));n.addError(Function.createDelegate(this,this.getCompanyInactiveError));n.addTimeout(Function.createDelegate(this,this.getCompanyInactiveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCompanyInactiveOK:function(n){var t=n._result;t&&(this._inactive=t.Inactive,this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._inactive),this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive),this._ibtnCease&&$R_IBTN.enableButton(this._ibtnCease,!this._inactive),this._ibtnDefaultBill&&$R_IBTN.enableButton(this._ibtnDefaultBill,!this._inactive),this._ibtnDefaultShip&&$R_IBTN.enableButton(this._ibtnDefaultShip,!this._inactive))},getCompanyInactiveError:function(n){this.showError(!0,n.get_ErrorMessage())},enableEditButtons:function(n){if(n){var t=this._tblAddresses.getSelectedCellValue(2)!="-",i=this._tblAddresses.getSelectedCellValue(3)!="-";this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._inactive&&this._blnEditHubSupplier);this._ibtnDefaultBill&&$R_IBTN.enableButton(this._ibtnDefaultBill,!this._inactive&&!t&&this._blnLineLoaded&&this._blnEditHubSupplier);this._ibtnDefaultShip&&$R_IBTN.enableButton(this._ibtnDefaultShip,!this._inactive&&!i&&this._blnLineLoaded&&this._blnEditHubSupplier);this._ibtnCease&&$R_IBTN.enableButton(this._ibtnCease,!this._inactive&&!t&&!i&&this._blnLineLoaded&&this._blnEditHubSupplier);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive&&this._blnLineLoaded&&this._blnEditHubSupplier)}else this._ibtnDefaultBill&&$R_IBTN.enableButton(this._ibtnDefaultBill,!1),this._ibtnDefaultShip&&$R_IBTN.enableButton(this._ibtnDefaultShip,!1),this._ibtnCease&&$R_IBTN.enableButton(this._ibtnCease,!1),this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1)},showAddForm:function(){this._frmAddEdit._globalLoginClientNo=this._globalLoginClientNo;this._frmAddEdit.changeMode("ADD");this.showForm(this._frmAddEdit,!0);this._frmAddEdit._blnCanEditTax=this._blnCanEditTax;this._frmAddEdit.setFieldValue("ctlName",this._strCompanyName);this._frmAddEdit.setFieldValue("ctlAddressName",this._strCompanyName);this._frmAddEdit.setFieldValue("ctlTax_Label","")},showEditForm:function(){this._frmAddEdit.changeMode("EDIT");this._frmAddEdit._globalLoginClientNo=this._globalLoginClientNo;this.showForm(this._frmAddEdit,!0);this._frmAddEdit._intCompanyAddressID=this._intCompanyAddressIDNew;this._frmAddEdit._intCompanyID=this._intCompanyID;this._frmAddEdit.setFieldValue("ctlName",this._strCompanyName);this._frmAddEdit.setFieldValue("ctlAddressName",this.getFieldValue("hidName"));this._frmAddEdit.setFieldValue("ctlLine1",this.getFieldValue("hidLine1"));this._frmAddEdit.setFieldValue("ctlLine2",this.getFieldValue("hidLine2"));this._frmAddEdit.setFieldValue("ctlLine3",this.getFieldValue("hidLine3"));this._frmAddEdit.setFieldValue("ctlTown",this.getFieldValue("hidTown"));this._frmAddEdit.setFieldValue("ctlCounty",this.getFieldValue("hidCounty"));this._frmAddEdit.setFieldValue("ctlState",this.getFieldValue("hidState"));this._frmAddEdit.setFieldValue("ctlCountry",this.getFieldValue("hidCountryID"));this._frmAddEdit.setFieldValue("ctlPostcode",this.getFieldValue("hidPostcode"));this._frmAddEdit.setFieldValue("ctlNotes",this.getFieldValue("ctlNotes"));this._frmAddEdit.setFieldValue("ctlShipVia",this.getFieldValue("hidShipViaNo"));this._frmAddEdit.setFieldValue("ctlShipViaAccount",this.getFieldValue("ctlShipViaAccount"));this._frmAddEdit.setFieldValue("ctlShippingNotes",this.getFieldValue("ctlShippingNotes"));this._frmAddEdit.setFieldValue("ctlTax",this.getFieldValue("hidTaxbyAddress"));this._frmAddEdit.setFieldValue("ctlTax_Label",this.getFieldValue("ctlTaxbyAddress"));this._frmAddEdit.setFieldValue("ctlVatNo",this.getFieldValue("ctlVatNo"));this._frmAddEdit.setFieldValue("ctlVatNo_Label",this.getFieldValue("ctlVatNo"));this._frmAddEdit._blnCanEditTax=this._blnCanEditTax;this._frmAddEdit.setFieldValue("ctlIncoterm",this.getFieldValue("hidIncotermNo"));this._frmAddEdit.setFieldValue("ctlRecieveNotes",this.getFieldValue("ctlRecievingNotes"));this._frmAddEdit.setFieldValue("ctlRegion",this.getFieldValue("hidRegion"));this._frmAddEdit.setFieldValue("ctlDivisionHeader",this.getFieldValue("hidDivisionHeader"));this._frmAddEdit.setFieldValue("ctlLabelType",this.getFieldValue("hidLabelType"));this.CheckCompanyAddressVatUpdatable()},tblAddresses_SelectedIndexChanged:function(){this._intCompanyAddressID=this._tblAddresses._varSelectedValue;this._intCompanyAddressIDNew=this._tblAddresses._varSelectedValue;this.getAddressData();this.enableEditButtons(!this._inactive)},getAddressData:function(){this.showLoading(!0);this._blnLineLoaded=!1;this.enableEditButtons(!1);$R_FN.showElement(this._pnlAddressInfo,!1);$R_FN.showElement(this._pnlLoadingAddressInfo,!0);$R_FN.showElement(this._pnlAddressError,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetAddress");n.addParameter("ID",this._intCompanyAddressID);n.addDataOK(Function.createDelegate(this,this.getAddressDataOK));n.addError(Function.createDelegate(this,this.getAddressDataError));n.addTimeout(Function.createDelegate(this,this.getAddressDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getAddressDataOK:function(n){$R_FN.showElement(this._pnlAddressError,!1);var t=n._result;$R_FN.setInnerHTML(this._lblAddressName,$R_FN.setCleanTextValue(t.Name));this.setFieldValue("ctlAddress",t.Long);this.setFieldValue("ctlNotes",t.Notes);this.setFieldValue("ctlShipVia",t.ShipVia);this.setFieldValue("ctlShipViaAccount",t.ShipViaAccount);this.setFieldValue("hidName",t.Name);this.setFieldValue("hidLine1",t.Address1);this.setFieldValue("hidLine2",t.Address2);this.setFieldValue("hidLine3",t.Address3);this.setFieldValue("hidTown",t.City);this.setFieldValue("hidCounty",t.County);this.setFieldValue("hidState",t.State);this.setFieldValue("hidCountryID",t.CountryID);this.setFieldValue("hidShipViaNo",t.ShipViaNo);this.setFieldValue("hidPostcode",t.ZIP);this.setFieldValue("ctlDefaultBill",t.DefaultBill);this.setFieldValue("ctlDefaultShip",t.DefaultShip);this.setFieldValue("ctlShippingNotes",t.ShippingNotes);this.setFieldValue("hidTaxbyAddress",t.TaxbyAddress);this.setFieldValue("ctlTaxbyAddress",t.TaxValue);this.setFieldValue("hidIncotermNo",t.IncotermNo);this.setFieldValue("ctlIncoterm",t.Incoterm);this.setFieldValue("ctlVatNo",t.VatNo);this.setFieldValue("ctlRecievingNotes",t.RNotes);this.setFieldValue("hidRegion",t.RegionNo);this.setFieldValue("ctlRegion",t.Region);this.setFieldValue("hidDivisionHeader",t.DivisionHeaderNo);this.setFieldValue("ctlDivisionHeader",$R_FN.setCleanTextValue(t.DivisionHeaderName));this.setFieldValue("hidLabelType",t.LabelTypeNo);this.setFieldValue("ctlLabelType",$R_FN.setCleanTextValue(t.LabelTypeName));this.setFieldValue("ctlVatNo",t.VatNo);this.setFieldValue("ctlEORINo",$("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlEORINumber_lbl").text());this.setFieldValue("CtlTelephoneNo",$("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlTel_lbl").text());this.setFieldValue("ctlCmpRegNo",t.CompanyRegNo);$R_FN.showElement(this._pnlAddressInfo,!0);$R_FN.showElement(this._pnlLoadingAddressInfo,!1);this.showLoading(!1);this._blnLineLoaded=!0;this.enableEditButtons(!this._inactive)},getAddressDataError:function(n){this.showLoading(!1);$R_FN.showElement(this._pnlLoadingAddressInfo,!1);$R_FN.showElement(this._pnlAddressError,!0);$R_FN.setInnerHTML(this._pnlAddressError,n.get_ErrorMessage())},cancelAddEdit:function(){this.showForm(this._frmAddEdit,!1)},saveAddEditComplete:function(){this.showForm(this._frmAddEdit,!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()},showCeaseForm:function(){this._frmConfirm.changeMode("CEASE");this.showConfirmForm()},showConfirmForm:function(){this._frmConfirm._intCompanyAddressID=this._intCompanyAddressID;this._frmConfirm.setFieldValue("ctlAddress",this._tblAddresses.getSelectedCellValue(1));this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},saveCeaseComplete:function(){this.hideConfirmForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intCompanyAddressID=-1;this.getData()},showDefaultBillForm:function(){this._frmConfirm.changeMode("DEFAULT_BILL");this.showConfirmForm()},showDefaultShipForm:function(){this._frmConfirm.changeMode("DEFAULT_SHIP");this.showConfirmForm()},CheckCompanyAddressVatUpdatable:function(){}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);