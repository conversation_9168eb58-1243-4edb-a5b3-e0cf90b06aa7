<%@ Control Language="C#" CodeBehind="Debits.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlDebitNo" runat="server" ResourceTitle="DebitNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlContact" runat="server" ResourceTitle="Contact" />
		<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlSupplierRMANo" runat="server" ResourceTitle="SupplierRMANo" />
		<ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNo" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDebitDateFrom" runat="server" ResourceTitle="DebitDateFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDebitDateTo" runat="server" ResourceTitle="DebitDateTo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
