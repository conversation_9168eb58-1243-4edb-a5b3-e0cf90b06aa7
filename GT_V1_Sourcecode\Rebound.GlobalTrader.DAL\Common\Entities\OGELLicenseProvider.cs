﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL {
	
	public abstract class OGELLicenseProvider : DataAccess {
		static private OGELLicenseProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public OGELLicenseProvider Instance {
			get {
				if (_instance == null) _instance = (OGELLicenseProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.OGELLicense.ProviderType));
                return _instance;
			}
		}
        public OGELLicenseProvider()
        {
			this.ConnectionString = Globals.Settings.OGELLicense.ConnectionString;
		}

        #region Method Registrations

        /// <summary>
        /// Insert
        /// Calls [usp_insert_OGELLicense]
        /// </summary>
        /// <param name="ogelNumber"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract Int32 Insert(System.String ogelNumber, System.String description, System.Boolean? inActive, System.Int32? updatedBy);
        /// <summary>
        /// Update
        /// Call [usp_update_OGELLicense]
        /// </summary>
        /// <param name="ogelId"></param>
        /// <param name="ogelNumber"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract int Update(System.Int32? ogelId, System.String ogelNumber, System.String description, System.Boolean? inActive, System.Int32? updatedBy);
        /// <summary>
        /// Call [usp_selectAll_OGELLicenses]
        /// </summary>
        /// <returns></returns>
        public abstract List<OGELLicenseDetails> GetList();
        #endregion

    }
}