///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date               Remarks
//[001]      Vinay           06/06/2013         CR:- Supplier Invoice
//[002]      Abhinav <PERSON>   22/01/2014         Make NPR value "Yes" to red and bold in supplier invoice
//[003]      <PERSON>    28-03-2023         [RP-1326]
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines = function (element) {
    Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.initializeBase(this, [element]);
    this._CompanyNo = 0;
    this._aryPONumber = [];
    this._aryGINumber = [];
    this._aryGoodsInLineIds = [];
    this._floatTotalSelectedValue = 0;
    this._intGoodsInID = 0;
    this._aryAddedGoodsInLineIds = [];
    this._aryTotalLineValue = [];
    this._intCount = 0;
    this._isClientInvoice = false;
    this._intInvoiceClientNo = -1;
    this._aryTotalShipLiveValue = [];
    this._floatTotalSelShipValue = 0;
    this._intGILineNo = 0;
    this._intGINo = 0;
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.prototype = {
    get_ShowIncludeInvoice: function () { return this._ShowIncludeInvoice; }, set_ShowIncludeInvoice: function (v) { if (this._ShowIncludeInvoice !== v) this._ShowIncludeInvoice = v; },

    addPotentialStatusChange: function (handler) { this.get_events().addHandler("PotentialStatusChange", handler); },
    removePotentialStatusChange: function (handler) { this.get_events().removeHandler("PotentialStatusChange", handler); },
    onPotentialStatusChange: function () {
        var handler = this.get_events().getHandler("PotentialStatusChange");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.callBaseMethod(this, "initialize");
        this.addSetupData(Function.createDelegate(this, this.doSetupData));
        this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
        // alert(this._ShowIncludeInvoice);
        $R_FN.showElement(this.getField("ctlIncludeInvoiced")._element, this._ShowIncludeInvoice);
        
        if (document.getElementById("ibtnSaveLog") != null)
            document.getElementById("ibtnSaveLog").addEventListener("click", Function.createDelegate(this, this.SaveUnReleasedGILog));

    },

    dispose: function () {
        if (this.isDisposed) return;
        this._CompanyNo = null;
        this._aryPONumber = null;
        this._aryGINumber = null;
        this._aryGoodsInLineIds = null;
        this._floatTotalSelectedValue = null;
        this._intGoodsInID = null;
        this._intGILineNo = null;
        this._intGINo = null;
        this._aryAddedGoodsInLineIds = null;
        this._aryTotalLineValue = null;
        this._intCount = null;
        this._isClientInvoice = null;
        this._aryTotalShipLiveValue = null;
        this._floatTotalSelShipValue = null;
        Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.callBaseMethod(this, "dispose");
    },

    doSetupData: function () {
        // alert(this._isClientInvoice);
        this._objData.set_PathToData("controls/ItemSearch/SIGILines");
        this._objData.set_DataObject("SIGILines");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("CompanyNo", this._CompanyNo);
        this._objData.addParameter("IncludeInvoiced", (this._intCount == 0 && this._intGoodsInID > 0) ? "" : this.getFieldValue("ctlIncludeInvoiced"));
        this._objData.addParameter("GIDateFrom", (this._intCount == 0 && this._intGoodsInID > 0) ? "" : this.getFieldValue("ctlGIDateFrom"));
        this._objData.addParameter("GIDateTo", (this._intCount == 0 && this._intGoodsInID > 0) ? "" : this.getFieldValue("ctlGIDateTo"));
        this._objData.addParameter("GoodsInNo", (this._intCount == 0 && this._intGoodsInID > 0) ? this._intGoodsInID : "");
        this._objData.addParameter("IsClientInvoice", this._isClientInvoice);
        this._objData.addParameter("InvoiceClientNo", this._intInvoiceClientNo);
        this._objData.addParameter("PONoLo", this.getFieldValue("ctlPurchaseOrderNo"));
        this._objData.addParameter("PONoHi", this.getFieldValue("ctlPurchaseOrderNo"));
        /*this._objData.addParameter("IncludeUnreleaseGI", this.getFieldValue("ctlUnReleasedGI"));*/
        this._objData.addParameter("ShowReleaseGI", this.getFieldValue("ctlReleasedGI")); /*[003]*/
        this._intCount += 1;
    },



    doGetDataComplete: function () {
        Array.clear(this._aryPONumber);
        Array.clear(this._aryGINumber);
        Array.clear(this._aryGoodsInLineIds);
        Array.clear(this._aryTotalLineValue);
        Array.clear(this._aryTotalShipLiveValue);
        this._floatTotalSelectedValue = 0;
        this._floatTotalSelShipValue = 0;
        this._tblResults.clearTable();

        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var blnGILineExist = Array.contains(this._aryAddedGoodsInLineIds, row.ID);
            var aryData = [
                this.writeCheckbox(row.ID, i, (blnGILineExist || row.blnFromGoodsIn))
                , row.GINumber
                , $R_FN.setCleanTextValue(row.GIDate)
                , row.PONumber
                , $R_FN.setCleanTextValue(row.Part)
                , row.DebitId > 0 ? $RGT_nubButton_DebitNote(row.DebitId, row.DebitNumber) : ''//$RGT_nubButton_DebitNote(row.DebitId, row.DebitNumber)
                , row.QtyReceived
                , row.Price
                , row.LineTotal
                , row.ShipInCost
                , (row.NPRPrinted) ? $R_FN.showRedBoldText($R_RES.Yes) : "-" //[002]
            ];
            this._tblResults.addRow(aryData, row.ID, false, { TotalLineValue: row.LineTotalValue, ShipInCostVal: row.ShipInCostVal });

            this.registerCheckBox(row.ID, i, (blnGILineExist || row.blnFromGoodsIn), !blnGILineExist);
            var chk = this.getCheckBox(i);
            chk._element.setAttribute("onClick", ((!blnGILineExist) ? String.format("$find(\"{0}\").getCheckedCellValue({1},{2},{3},{4});", this._element.id, i, row.IsUnReleasedLine, row.ID, row.GoodsInNo) : "javascript:void(0);"));
            chk = null;
            if (row.blnFromGoodsIn && !blnGILineExist) {
                this.getCheckedCellValue(i);
            }
            aryData = null; row = null;

        }
        this.onPotentialStatusChange();
    },

    getSelectedPOLines: function (poNumber) {
        Array.add(this._aryPONumber, poNumber);
    },

    getSelectedGILines: function (giNumber, gilineid, totalPrice, totalShipValue) {
        Array.add(this._aryGINumber, giNumber);
        Array.add(this._aryGoodsInLineIds, gilineid);
        Array.add(this._aryTotalLineValue, totalPrice);
        Array.add(this._aryTotalShipLiveValue, totalShipValue);
    },

    removeSelectedPOLines: function (poNumber) {
        Array.remove(this._aryPONumber, poNumber);
    },

    removeSelectedGILines: function (giNumber, gilineid, totalPrice, totalShipValue) {
        Array.remove(this._aryGINumber, giNumber);
        Array.remove(this._aryGoodsInLineIds, gilineid);
        Array.remove(this._aryTotalLineValue, totalPrice);
        Array.remove(this._aryTotalShipLiveValue, totalShipValue);
    },

    getSelectedTotal: function () {
        this._floatTotalSelectedValue = 0;
        for (var i = 0, l = this._aryTotalLineValue.length; i < l; i++) {
            this._floatTotalSelectedValue += parseFloat(this._aryTotalLineValue[i]);
        }
    },

    getSelectedTotalShip: function () {
        this._floatTotalSelShipValue = 0;
        for (var i = 0, l = this._aryTotalShipLiveValue.length; i < l; i++) {
            this._floatTotalSelShipValue += parseFloat(this._aryTotalShipLiveValue[i]);
        }
    },

    //    writeCheckbox: function(varID, intIndex, blnChecked) {
    //        var strChkID = this.getControlID("chk", intIndex);
    //        var strChkImageID = this.getControlID("chkImg", intIndex);
    //        var strSpanID = this.getControlID("chkSpan", intIndex);
    //        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /> </div>&nbsp;<span id=\"{3}\">{4}</span>", strChkID, strChkImageID, (blnChecked) ? "on" : "off", strSpanID, (blnChecked) ? "TRUE" : "FALSE");
    //        return str;
    //    },

    writeCheckbox: function (varID, intIndex, blnChecked) {
        var strChkID = this.getControlID("chk", intIndex);
        var strChkImageID = this.getControlID("chkImg", intIndex);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /> </div>", strChkID, strChkImageID, (blnChecked) ? "on" : "off");
        return str;
    },

    getControlID: function (str, i) {
        return String.format("{0}_{1}{2}", this._tblResults._element.id, str, i);
    },

    registerCheckBox: function (varID, intIndex, blnChecked, blnEnabled) {
        var strChkID = this.getControlID("chk", intIndex);
        var strChkImageID = this.getControlID("chkImg", intIndex);
        var chk = this.getCheckBox(intIndex);
        if (chk) {
            chk.dispose();
            chk = null;
        }
        eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", [["blnChecked", blnChecked], ["blnEnabled", blnEnabled], ["img", String.format("$get(\"{0}\")", strChkImageID)]], strChkID));
    },

    getCheckBox: function (intCheckBox) {
        return $find(this.getControlID("chk", intCheckBox));
    },


    getCheckedCellValue: function (intIndex, IsUnreleasedGI, GILineNo, GoodsInNo) {
        if (!(this._tblResults) || (this._tblResults == "undefined")) return;
        if (!(this._tblResults._tbl) || (this._tblResults._tbl == "undefined")) return;

        var chk = this.getCheckBox(intIndex);
        var tr = this._tblResults._tbl.rows[intIndex];
        if (!tr) return;

        var goodsInNum = tr.cells[1].innerHTML;
        var poNum = tr.cells[3].innerHTML;
        var goodsInLineId = this._tblResults._aryValues[intIndex];
        //var span = this.getControlID("chkSpan", intIndex);

        if (chk._blnChecked) {
            this.getSelectedGILines(goodsInNum, goodsInLineId, this._tblResults.getSelectedExtraData(intIndex).TotalLineValue, this._tblResults.getSelectedExtraData(intIndex).ShipInCostVal);
            if (poNum != "&nbsp;")
                this.getSelectedPOLines(poNum);
            //document.getElementById(span).innerHTML = "TRUE";
        }
        else {
            this.removeSelectedGILines(goodsInNum, goodsInLineId, this._tblResults.getSelectedExtraData(intIndex).TotalLineValue, this._tblResults.getSelectedExtraData(intIndex).ShipInCostVal);
            if (poNum != "&nbsp;")
                this.removeSelectedPOLines(poNum);
            //document.getElementById(span).innerHTML = "FALSE";
        }
        this.getSelectedTotal();
        this.getSelectedTotalShip();

        // span = null;
        goodsInLineId = null;

        this.onPotentialStatusChange();
        
        if (IsUnreleasedGI) {

            if (chk._blnChecked) {
                this.AddCommentOnGI(GILineNo, GoodsInNo);
            }

        }
        goodsInNum = null;
        poNum = null;
        chk = null;
    },
    AddCommentOnGI: function (GIlineNo, GoodsInNo) {
        ShowCommentForUnreleasedGI();
        this._intGILineNo = GIlineNo;
        this._intGINo = GoodsInNo;
    },
    SaveUnReleasedGILog: function () {
        
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/SupplierInvoiceAdd");
        obj.set_DataObject("SupplierInvoiceAdd");
        obj.set_DataAction("SaveUnReleasedGILog");
        obj.addParameter("GILIneNo", this._intGILineNo);
        obj.addParameter("GINo", this._intGINo);
        if ($("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_txtWarehoueseNotes").val() != undefined) {
            obj.addParameter("ReasontoAdd", $("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_txtWarehoueseNotes").val());
        }
        else {
            obj.addParameter("ReasontoAdd", $("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_txtWarehoueseNotes").val());
        }

        obj.addDataOK(Function.createDelegate(this, this.SaveUnReleasedGILogComplete));
        obj.addError(Function.createDelegate(this, this.SaveUnReleasedGILogError));
        obj.addTimeout(Function.createDelegate(this, this.SaveUnReleasedGILogError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    SaveUnReleasedGILogError: function (args) {
        this._strErrorMessage = args._errorMessage;
        //this.onSaveError();
    },

    SaveUnReleasedGILogComplete: function (args) {
        if (args._result.Result == true) {
            HideCommentForUnreleasedGI();
            $("#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_txtWarehoueseNotes").val('')
        } else {
            this._strErrorMessage = args._errorMessage;
        }
    }

};

Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
