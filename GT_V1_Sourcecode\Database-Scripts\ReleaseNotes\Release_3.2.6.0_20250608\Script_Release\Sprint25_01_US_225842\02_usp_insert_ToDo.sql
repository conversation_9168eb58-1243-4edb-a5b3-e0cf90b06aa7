﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--===================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-212758]     Phuc Hoang		 31-Oct-2024		UPDATE		Prospect Qualification - Power Automate Function for Reminder
[US-229094]     Phuc Hoang		 21-Jan-2025		UPDATE		Quote - Add automatic Task Reminder based on Quote Status
[US-237650]		Phuc Hoang		 20-Mar-2025		UPDATE      IPO - Addition of Task Management based on each Requirement on HUBRFQ
[US-225842]		An.TranTan		 02-Apr-2025		UPDATE		Insert TODO task type SalesOrder
===================================================================================================================================== 
*/

CREATE OR ALTER   PROCEDURE [dbo].[usp_insert_ToDo]      
@LoginNo int      
,@Subject nvarchar(256)      
,@DateAdded datetime      
,@DueDate datetime = null      
,@ToDoText nvarchar(max)      
,@Priority int = null      
,@IsComplete bit = false      
,@ReminderDate datetime = null      
,@ReminderText nvarchar(128) = null      
,@ReminderHasBeenNotified bit = false      
,@CompanyNo int = null      
,@RelatedMailMessageNo int = null      
,@UpdatedBy int      
,@ToDoId int output     
,@HasReview bit=NULL  
,@ToDoListTypeId INT=NULL
,@ContactNo int=NULL 
,@QuoteNo int=NULL 
,@ToDoCategoryNo int=NULL 
,@DailyReminder bit=NULL 
,@SalesOrderNo INT = NULL
AS      
BEGIN      
INSERT INTO dbo.tbToDo      
           (      
			LoginNo      
			,[Subject]      
			,DateAdded      
			,DueDate      
			,ToDoText      
			,Priority      
			,IsComplete      
			,ReminderDate      
			,ReminderText      
			,ReminderHasBeenNotified      
			,CompanyNo      
			,RelatedMailMessageNo      
			,UpdatedBy      
			,DLUP      
			,HasReview    
			,TypeNo  
			,ContactNo
			,QuoteNo
			,ToDoCategoryNo
			,DailyReminder
			,SalesOrderNo
   )      
     VALUES      
           (      
			@LoginNo      
           ,@Subject      
           ,getdate()    
           ,@DueDate      
           ,@ToDoText      
           ,@Priority      
           ,@IsComplete      
           ,@ReminderDate      
           ,@ReminderText      
           ,@ReminderHasBeenNotified      
           ,@CompanyNo      
           ,@RelatedMailMessageNo      
           ,@UpdatedBy      
           ,getdate()     
		   ,@HasReview   
		   ,@ToDoListTypeId  
		   ,@ContactNo 
		   ,@QuoteNo
		   ,@ToDoCategoryNo
		   ,@DailyReminder
		   ,@SalesOrderNo
   )      
      
 SET @ToDoId = SCOPE_IDENTITY() 
      
END 


GO


