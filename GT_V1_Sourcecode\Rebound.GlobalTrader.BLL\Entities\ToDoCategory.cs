﻿using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;

namespace Rebound.GlobalTrader.BLL
{
	public partial class ToDoCategory : BizObject
	{

		#region Properties

		protected static DAL.ToDoCategoryElement Settings
		{
			get { return Globals.Settings.ToDoCategories; }
		}

		public int ToDoCategoryId { get; set; }
		public string ToDoCategoryName { get; set; }

		#endregion

		#region Methods

		public static List<ToDoCategory> DropDown()
		{
			List<ToDoCategoryDetails> lstDetails = SiteProvider.ToDoCategory.DropDown();
			if (lstDetails == null)
			{
				return new List<ToDoCategory>();
			}
			else
			{
				List<ToDoCategory> lst = new List<ToDoCategory>();
				foreach (ToDoCategoryDetails objDetails in lstDetails)
				{
					ToDoCategory obj = new ToDoCategory()
					{
						ToDoCategoryId = objDetails.ToDoCategoryId,
						ToDoCategoryName = objDetails.ToDoCategoryName
					};
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}
		#endregion

	}
}