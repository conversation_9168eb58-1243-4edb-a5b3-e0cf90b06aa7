//-----------------------------------------------------------------------------------------
// RP 14.10.2009:
// - prevent pressing enter on checkbox from posting the page
//-----------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Nuggets;

namespace Rebound.GlobalTrader.Site.Controls.FilterDataItemRows {

	public class Base : TableRow, IScriptControl, INamingContainer {

		protected ScriptManager _sm;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		private int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;
		protected System.Web.UI.WebControls.CheckBox _chkOn;
		protected Label _lblField;
		protected Site _objSite = Rebound.GlobalTrader.Site.Site.GetInstance();
		protected bool _blnConfigurationIsDebug = false;

		#region Properties

		/// <summary>
		/// Title of resource object in FormFields global resource
		/// </summary>
		private string _strResourceTitle = "";
		public string ResourceTitle {
			get { return _strResourceTitle; }
			set { _strResourceTitle = value; }
		}

		private bool _blnForLeftNugget = false;
		public bool ForLeftNugget {
			get { return _blnForLeftNugget; }
			set { _blnForLeftNugget = value; }
		}

		private string _strFilterField;
		public string FilterField {
			get { return _strFilterField; }
			set { _strFilterField = value; }
		}

		public Type FieldType { get; set; }
		public string DefaultValue { get; set; }

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
#if (DEBUG)
			_blnConfigurationIsDebug = true;
#endif
			AddScriptReference(Functions.GetScriptReference(_blnConfigurationIsDebug, "Rebound.GlobalTrader.Site", "Controls.FilterDataItemRows._Bases.Base", true));
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {

			string strTitle = "&nbsp;";
			if (ResourceTitle != "") strTitle = Functions.GetGlobalResource("FormFields", ResourceTitle);

			if (_blnForLeftNugget) {
				//only one cell
				TableCell td = new TableCell();

				//checkbox and title
				Panel pnlTitle = ControlBuilders.CreatePanelInsideParent(td);
				_chkOn = new System.Web.UI.WebControls.CheckBox();
				if (FieldType != Type.CheckBox) _chkOn.CssClass = "invisible";
				pnlTitle.Controls.Add(_chkOn);
				ControlBuilders.CreateLiteralInsideParent(pnlTitle, strTitle);

				//field span control
				Panel pnlContent = ControlBuilders.CreatePanelInsideParent(td);
				_lblField = ControlBuilders.CreateLabelInsideParent(pnlContent, "filterDisabled");

				Cells.Add(td);
			} else {
				//title cell
				TableCell td1 = new TableCell();
				td1.CssClass = "desc";
				ControlBuilders.CreateLiteralInsideParent(td1, strTitle);
				Cells.Add(td1);

				//Field cell
				TableCell td2 = new TableCell();
				td2.ID = "tdField";
				td2.CssClass = "item";
				Cells.Add(td2);

				//checkbox
				_chkOn = new System.Web.UI.WebControls.CheckBox();
				td2.Controls.Add(_chkOn);

				//field span control
				_lblField = ControlBuilders.CreateLabelInsideParent(td2, "filterDisabled");
			}
			_chkOn.ID = "chkOn";
			_chkOn.Attributes["onkeydown"] = "return $R_FN.preventEnterKeyForControl(this, event);";
			_lblField.ID = "lblField";
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		public virtual void SetDefaultValue() { }
		public virtual void Reset() { }

		/// <summary>
		/// Add AJAX script reference
		/// </summary>
		/// <param name="sr"></param>
		protected void AddScriptReference(ScriptReference sr) {
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
		}

		public void Enable(bool blnEnable) {
			EnsureChildControls();
			_lblField.CssClass = (blnEnable) ? "" : "filterDisabled";
			_chkOn.Checked = blnEnable;
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			_scScriptControlDescriptor.AddElementProperty("lblField", _lblField.ClientID);
			_scScriptControlDescriptor.AddElementProperty("chkOn", _chkOn.ClientID);
			_scScriptControlDescriptor.AddProperty("strFilterField", _strFilterField);
			_scScriptControlDescriptor.AddProperty("enmFieldType", FieldType);
			_scScriptControlDescriptor.AddProperty("strDefaultValue", DefaultValue);
			return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }

		#endregion

	}

	public enum Type {
		None,
		CheckBox,
		DropDown,
		DateSelect,
		Numerical,
		StarRating,
		TextBox
	}

}