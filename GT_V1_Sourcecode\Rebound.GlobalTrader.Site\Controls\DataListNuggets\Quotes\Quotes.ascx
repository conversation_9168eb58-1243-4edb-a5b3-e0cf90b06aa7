<%--
	MARKER				Changed By				Date				Remarks
	[001]				<PERSON>			19-09-2023			RP-2338  AS6081 Search/Filter functionality on different pages
--%>

<%@ Control Language="C#" CodeBehind="Quotes.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Links>
        <ReboundUI:IconButton ID="ibtnViewTask" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ViewTask" IconCSSType="Add"  IsInitiallyEnabled="true" />
	</Links>
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlQuoteNo" runat="server" ResourceTitle="QuoteNo" FilterField="QuoteNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
                <ReboundUI_FilterDataItemRow:Numerical id="ctlTotalValue" runat="server" ResourceTitle="TotalValue" FilterField="Total" CanShowDecimal="true" />
                <ReboundUI_FilterDataItemRow:Numerical id="ctlTotalProfit" runat="server" ResourceTitle="Profit" FilterField="TotalProfit" CanShowDecimal="true" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesmanName" runat="server" ResourceTitle="SalesmanName" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateQuotedFrom" runat="server" ResourceTitle="DateQuotedFrom" FilterField="DateQuotedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateQuotedTo" runat="server" ResourceTitle="DateQuotedTo" FilterField="DateQuotedTo" />
                <ReboundUI_FilterDataItemRow:CheckBox id="ctlImportant" runat="server" ResourceTitle="Important" FilterField="Important" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlStatus" runat="server" ResourceTitle="Status" DropDownType="QuoteStatus" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="QStatus" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlAS6081" runat="server" ResourceTitle="AS6081Filter" DropDownType="CounterfeitElectronicParts" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="AS6081" /> <%--[001]--%>
			    <ReboundUI_FilterDataItemRow:DropDown ID="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
	<Forms>
		<ReboundForm:MailMessages_MarkAsToDo id="ctlAdd" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
<style>
	.readonly-field{
		pointer-events: none;
		opacity: 0.5;
	}
</style>