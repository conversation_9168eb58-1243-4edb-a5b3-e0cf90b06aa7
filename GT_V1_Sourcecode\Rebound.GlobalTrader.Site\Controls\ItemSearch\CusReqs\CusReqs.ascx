<%@ Control Language="C#" CodeBehind="CusReqs.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirements" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">

	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlReqNo" runat="server" ResourceTitle="RequirementNo"/>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedFrom" runat="server" ResourceTitle="DateReceivedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedTo" runat="server" ResourceTitle="DateReceivedTo" />
	</FieldsRight>
	
</ReboundUI_ItemSearch:DesignBase>
