//------------------------------------------------------------------------------------------
// Contains the old control.js code now in a new control so it can be attached to something
// RP 24.11.2009:
// - New control
//------------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Pages {

	public class ContentControl : Panel, IScriptControl {

		protected ScriptManager _sm;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		private int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;

		#region Properties

		public bool ConfigurationIsDebug { get; set; }
		internal string bdyBody_ClientID { get; set; }
		internal string CloseLeft_ClientID { get; set; }
		internal bool CheckMessages { get; set; }
		internal string tblAlerts_ClientID { get; set; }
		internal string ancCloseLeft_ClientID { get; set; }
		internal string ibtnOpenItem_ClientID { get; set; }
		internal string ibtnDismiss_ClientID { get; set; }
		internal string ibtnSnooze_ClientID { get; set; }
		internal string hypCloseNewMessages_ClientID { get; set; }
		internal string pnlShadow_ClientID { get; set; }
		internal string pnlLeftContent_ClientID { get; set; }
		internal string pnlLeftButton_ClientID { get; set; }
		internal string pnlNewMessages_ClientID { get; set; }
		internal string lblNewMessagesTitle_ClientID { get; set; }
		internal string tblNewMessages_ClientID { get; set; }
		internal string pnlToDoAlert_ClientID { get; set; }
		internal string pnlMainArea_ClientID { get; set; }
		internal string pnlTitleBar_ClientID { get; set; }
		internal string ddlSnoozeTime_ClientID { get; set; }
		internal string ctlToolTip_ClientID { get; set; }
        internal string ddlClientByMaster_ClientID { get; set; }

        #endregion

        protected override void OnInit(EventArgs e) {
			AddScriptReference("Code.Pages.Common.Content");
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
			CssClass = "invisible";
			ControlBuilders.CreateLiteralInsideParent(this, "&nbsp;");
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		public void AddScriptReference(string strAssembly, string strRef) {
			ConfigurationIsDebug = false;
#if DEBUG
			ConfigurationIsDebug = true;
#endif
			ScriptReference sr = Functions.GetScriptReference(ConfigurationIsDebug, strAssembly, strRef, true);
			ScriptManager.GetCurrent(Page).Scripts.Add(sr);
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
			sr = null;
		}

		public virtual void AddScriptReference(string strRef) {
			AddScriptReference("Rebound.GlobalTrader.Site", strRef);
		}

		public void AddScriptProperty(string strElement, object objValue) {
			_scScriptControlDescriptor.AddProperty(strElement, objValue);
		}

		public void AddScriptElementProperty(string strElement, string strID) {
			_scScriptControlDescriptor.AddElementProperty(strElement, strID);
		}

		public void AddScriptComponentProperty(string strElement, string strID) {
			_scScriptControlDescriptor.AddComponentProperty(strElement, strID);
		}

		public void SetupScriptDescriptor(string strScriptClass) {
			if (_scScriptControlDescriptor == null) _scScriptControlDescriptor = new ScriptControlDescriptor(strScriptClass, ClientID);
		}

		private string GetBackgroundImages() {
			StringBuilder sbOut = new StringBuilder("");
			object objCachedBackgrounds = CacheManager.GetItem(CacheKeys.BackgroundImages);
			if (objCachedBackgrounds != null) {
				sbOut.Append(objCachedBackgrounds.ToString());
			} else {
				sbOut.Append("{");
				sbOut.Append(GetBackgroundImageForJavascript("brick"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("fern"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("flowers"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("fruit"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("metal"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("office"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("palm"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("plants"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("silk"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("sky"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("sunflowers"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("wood"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("sun"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("map"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("spark"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("xmaslights"));
				sbOut.AppendFormat(",{0}", GetBackgroundImageForJavascript("snowflake"));
				sbOut.Append("}");
				CacheManager.StoreItem(CacheKeys.BackgroundImages, sbOut.ToString(), CacheManager.CacheExpiryType.OneWorkingDay);
			}
			objCachedBackgrounds = null;
			return sbOut.ToString();
		}

		private string GetBackgroundImageForJavascript(string strImage) {
			return string.Format(@"{0}: ""{1}""", strImage.ToLower(), Functions.GetLocalThemeImage(string.Format("backgrounds/{0}.jpg", strImage), Page.Theme, true));
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			SetupScriptDescriptor("Rebound.GlobalTrader.Site.Pages.Content");
			_scScriptControlDescriptor.AddElementProperty("bdyBody", bdyBody_ClientID);
			_scScriptControlDescriptor.AddElementProperty("strPageTheme", Page.Theme);
			_scScriptControlDescriptor.AddElementProperty("ancCloseLeft", ancCloseLeft_ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlShadow", pnlShadow_ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLeftContent", pnlLeftContent_ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLeftButton", pnlLeftButton_ClientID);
			_scScriptControlDescriptor.AddProperty("blnLeftPanelVisible", SessionManager.LeftPanelVisible);
			_scScriptControlDescriptor.AddElementProperty("pnlNewMessages", pnlNewMessages_ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypCloseNewMessages", hypCloseNewMessages_ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblNewMessagesTitle", lblNewMessagesTitle_ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblNewMessages", tblNewMessages_ClientID);
			_scScriptControlDescriptor.AddProperty("blnPageShouldCheckMessages", CheckMessages);
			_scScriptControlDescriptor.AddElementProperty("pnlToDoAlert", pnlToDoAlert_ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblAlerts", tblAlerts_ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnOpenItem", ibtnOpenItem_ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnDismiss", ibtnDismiss_ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnSnooze", ibtnSnooze_ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlMainArea", pnlMainArea_ClientID);
			_scScriptControlDescriptor.AddElementProperty("divMainArea", "divMainArea");
			_scScriptControlDescriptor.AddElementProperty("pnlTitleBar", pnlTitleBar_ClientID);
			_scScriptControlDescriptor.AddProperty("objBackgroundImages", GetBackgroundImages());
			_scScriptControlDescriptor.AddComponentProperty("ddlSnoozeTime", ddlSnoozeTime_ClientID);
			_scScriptControlDescriptor.AddComponentProperty("ctlToolTip", ctlToolTip_ClientID);
            //Espire : 14 Nov 2019: Implement SSO
            _scScriptControlDescriptor.AddComponentProperty("ddlClientByMaster", ddlClientByMaster_ClientID);


            _scScriptControlDescriptor.AddProperty("intMasterLoginNo", SessionManager.MasterLoginNo);

            int intDropdownClient = 0;
            //if (HttpContext.Current.Session["ClientNoForDropdown"] != null)
            //    intDropdownClient = Convert.ToInt32(HttpContext.Current.Session["ClientNoForDropdown"]);
            //else
                intDropdownClient = (int)SessionManager.ClientID;

            _scScriptControlDescriptor.AddProperty("intDefaultClientNo", intDropdownClient);

            return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		System.Collections.Generic.IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }
		System.Collections.Generic.IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		#endregion

	}
}
