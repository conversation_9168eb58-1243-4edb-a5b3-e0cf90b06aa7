Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close=function(n){Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.initializeBase(this,[n]);this._intQuoteID=-1};Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.prototype={get_intQuoteID:function(){return this._intQuoteID},set_intQuoteID:function(n){this._intQuoteID!==n&&(this._intQuoteID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._intQuoteID=null,Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)));this.getFieldDropDownData("ctlReason")},yesClicked:function(){if(this.resetFormFields(),this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/QuoteMainInfo");n.set_DataObject("QuoteMainInfo");n.set_DataAction("CloseAllLines");n.addParameter("id",this._intQuoteID);n.addParameter("Reason",this.getFieldValue("ctlReason"));n.addParameter("txtReason",this.getFieldValue("ctlTxtReasons"));n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){return this.autoValidateFields()}};Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_Close",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);