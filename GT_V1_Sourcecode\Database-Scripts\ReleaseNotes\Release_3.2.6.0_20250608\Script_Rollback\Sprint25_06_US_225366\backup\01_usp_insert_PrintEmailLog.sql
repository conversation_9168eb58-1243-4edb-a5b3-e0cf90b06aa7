﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[usp_insert_PrintEmailLog] 
--
      @SectionName	varchar(50) = NULL
	, @SubSectionName varchar(50) = null
    , @ActionName			varchar(10) = NULL
    , @DocumentNo					int
    , @Detail					nvarchar(max) = NULL
    , @UpdatedBy				int = NULL
    , @PrintDocumentLogId		int OUTPUT
AS 
BEGIN

      IF @SubSectionName = 'ReceivingDocket' OR @SubSectionName = 'ProFormaReceivingDocket'
	  BEGIN
	     SELECT @DocumentNo = GoodsInNo FROM tbGoodsInLine WHERE GoodsInLineId = @DocumentNo
	  END

	   
      
      
      INSERT    INTO dbo.tbPrintDocumentLog (
                  SectionName
				, SubSectionName
                , ActionName
                , DocumentNo
                , Detail
                , UpdatedBy
                , DLUP

	          )
      VALUES    (
                 @SectionName
			   , @SubSectionName
               , @ActionName
               , @DocumentNo
               , @Detail
               , @UpdatedBy
               , GETDATE()
	          )	
      SET @PrintDocumentLogId = scope_identity()
END



GO


