Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect=function(n){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.prototype={get_txt:function(){return this._txt},set_txt:function(n){this._txt!==n&&(this._txt=n)},get_cal:function(){return this._cal},set_cal:function(n){this._cal!==n&&(this._cal=n)},get_enmComparison:function(){return this._enmComparison},set_enmComparison:function(n){this._enmComparison!==n&&(this._enmComparison=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.callBaseMethod(this,"initialize");$addHandler(this._txt,"focus",Function.createDelegate(this,this.dateSelectFocus));$addHandler(this._txt,"blur",Function.createDelegate(this,this.dateSelectBlur));this._cal.addShowCalendarEvent(Function.createDelegate(this,this.dateSelectFocus));this._cal.addCloseCalendarEvent(Function.createDelegate(this,this.dateSelectBlur));this._cal.addDateSelectEvent(Function.createDelegate(this,this.dateSelectBlur));this.addFieldDisabledEvent(Function.createDelegate(this,this.hideCalendarAfterDeselect))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._txt&&$clearHandlers(this._txt),this._txt=null,this._cal&&this._cal.dispose(),this._cal=null,Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.callBaseMethod(this,"dispose"))},dateSelectFocus:function(){this.getValue()==""?this.setValue($R_FN.shortDate()):this.enableField(!0)},dateSelectBlur:function(){this.enableField($R_FN.isEntered(this._txt.value))},hideCalendarAfterDeselect:function(){this._cal.showCalendar(!1,!0)},getValue:function(){return this._txt.value},setValue:function(n){if((typeof n=="undefined"||n==null)&&(n=""),n=="")this.enableField(!1);else{switch(n.toUpperCase()){case"TODAY":n=$R_FN.shortDate();break;case"FIRSTDAYOFMONTH":n=$R_FN.firstDayOfMonth();break;case"LASTDAYOFMONTH":n=$R_FN.lastDayOfMonth();break;case"ONEWEEKAGO":n=$R_FN.oneWeekAgo()}this._txt.value=n;this.enableField(!0)}},reset:function(){this._txt.value="";this.enableField(!1)}};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DateSelect",Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base,Sys.IDisposable);