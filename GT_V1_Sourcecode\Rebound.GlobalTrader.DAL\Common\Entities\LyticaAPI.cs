﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public class LyticaAPI
    {
        #region Constructors
        public LyticaAPI() { }
        #endregion

        #region Properties
        public System.Int32 LyticaAPIId { get; set; }
        [JsonProperty("commodity")]
        public System.String Commodity { get; set; }
        [JsonProperty("mpn")]
        public System.String OriginalPartSearched { get; set; }
        [JsonProperty("manufacturer")]
        public System.String Manufacturer { get; set; }
        [JsonProperty("avgPrice", DefaultValueHandling = 0)]
        public double? AveragePrice { get; set; }
        [JsonProperty("targetPrice", DefaultValueHandling = 0)]
        public double? TargetPrice { get; set; }
        [JsonProperty("marketLeading", DefaultValueHandling = 0)]
        public double? MarketLeading { get; set; }
        [JsonProperty("lifecycle")]
        public System.String LifeCycle { get; set; }
        [JsonProperty("lifecycleStatus")]
        public System.String lifeCycleStatus { get; set; }
        [JsonProperty("overallRisk")]
        public System.String OverAllRisk { get; set; }
        [JsonProperty("mpnBreadth")]
        public System.String PartBreadth { get; set; }
        [JsonProperty("mfrBreadth")]
        public System.String ManufacturerBreadth { get; set; }
        [JsonProperty("dueDiligence")]
        public System.String DueDiligence { get; set; }
        [JsonProperty("mpnConcentration")]
        public System.String PartConcentration { get; set; }
        [JsonProperty("alternateParts")]
        public List<LyticaAlternateParts> AlternateParts { get; set; }
        public DateTime? OriginalEntryDate { get; set; }
        public System.Int32? TotalCount { get; set; }

        public System.Int32? LyticaAPILogCount { get; set; }
        #endregion Properties

    }
    public class LyticaAPIAlternatePart
    {
        #region Constructors
        public LyticaAPIAlternatePart() { }
        #endregion

        #region Properties
        public System.String Manufacturer { get; set; }
        public System.String Part { get; set; }
        public System.String lifeCycleStatus { get; set; }

        #endregion
    }
    public class LyticalAlternatepartlist
    {
        public LyticaAPI LyticaApiData { get; set; }
        [JsonProperty("alternateParts")]
        public List<LyticaAPIAlternatePart> LyticaAPIAlternatePartData { get; set; }
    }

    public class LyticaAlternateParts
    {
        public LyticaAlternateParts() { }

        [JsonProperty("manufacturer")]
        public System.String Manufacturer { get; set; }
        [JsonProperty("mpn")]
        public System.String Part { get; set; }
        [JsonProperty("lifecycleStatus")]
        public System.String lifeCycleStatus { get; set; }

    }
}
