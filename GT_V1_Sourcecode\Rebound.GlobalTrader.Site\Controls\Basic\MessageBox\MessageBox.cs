using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:MessageBox runat=server></{0}:MessageBox>")]
	public class MessageBox : WebControl {

		#region Locals

		private Literal _litTitle;
		private Literal _litMessage;
		private Panel _pnlBoxContentInner;
		private HtmlControl _H4;

		#endregion

		#region Properties

		private String _strMessageText;
		public String MessageText {
			get { return _strMessageText; }
			set { _strMessageText = value; }
		}

		private MessageTypeList _enmMessageType;
		public MessageTypeList MessageType {
			get { return _enmMessageType; }
			set { _enmMessageType = value; }
		}


		#endregion

		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("MessageBox.css");
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		/// <param name="e"></param>
		protected override void CreateChildControls() {
			//outer stuff
			Panel pnlBox = ControlBuilders.CreatePanelInsideParent(this, "box messageBox");
			Panel pnlBoxInner = ControlBuilders.CreatePanelInsideParent(pnlBox);

			//curve stuff
			pnlBoxInner.Controls.Add(ControlBuilders.CreatePanel("boxTL"));
			pnlBoxInner.Controls.Add(ControlBuilders.CreatePanel("boxTR"));

			//content box
			Panel pnlBoxContent = ControlBuilders.CreatePanelInsideParent(pnlBoxInner, "boxContent");
			_pnlBoxContentInner = ControlBuilders.CreatePanelInsideParent(pnlBoxContent);

			//page title div
			_H4 = ControlBuilders.CreateHtmlGenericControlInsideParent(_pnlBoxContentInner, "h4");
			_litTitle = ControlBuilders.CreateLiteralInsideParent(_H4);

			//message
			Panel pnlMessage = ControlBuilders.CreatePanelInsideParent(_pnlBoxContentInner);
			_litMessage = ControlBuilders.CreateLiteralInsideParent(pnlMessage);

			//curve stuff
			pnlBoxInner.Controls.Add(ControlBuilders.CreatePanel("boxBL"));
			pnlBoxInner.Controls.Add(ControlBuilders.CreatePanel("boxBR"));

			base.CreateChildControls();
		}

		/// <summary>
		/// pre-render
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			_litMessage.Text = MessageText;
			switch (_enmMessageType) {
				case MessageTypeList.None:
					Functions.SetCSSVisibility(_H4, false);
					break;
				case MessageTypeList.Warning:
					_pnlBoxContentInner.CssClass = " messageBoxWarning";
					_litTitle.Text = Functions.GetGlobalResource("Misc", "Warning");
					break;
				case MessageTypeList.Error:
					_pnlBoxContentInner.CssClass = " messageBoxError";
					_litTitle.Text = Functions.GetGlobalResource("Misc", "Error");
					break;
				case MessageTypeList.Information:
					_pnlBoxContentInner.CssClass = " messageBoxInformation";
					_litTitle.Text = Functions.GetGlobalResource("Misc", "Information");
					break;
			}
			base.OnPreRender(e);
		}

		#region Enumerations

		/// <summary>
		/// Generic message type
		/// </summary>
		public enum MessageTypeList {
			None,
			Information,
			Warning,
			Error
		}

		#endregion

	}
}