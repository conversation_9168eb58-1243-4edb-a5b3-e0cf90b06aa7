///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Reports");

Rebound.GlobalTrader.Site.Pages.Reports = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Reports.initializeBase(this, [el]);
	this._aryReportPanelIDs = [];	
	this._aryReportSearchText = [];	
	this._aryGroupNuggetIDs = [];
	this._aryCategoryHeaderIDs = [];
	this._isChecked=false;
};

Rebound.GlobalTrader.Site.Pages.Reports.prototype = {

	get_ctlControlStrip: function() { return this._ctlControlStrip; }, 	set_ctlControlStrip: function(v) { if (this._ctlControlStrip !== v)  this._ctlControlStrip = v; }, 
	get_txtSearch: function() { return this._txtSearch; }, 	set_txtSearch: function(v) { if (this._txtSearch !== v)  this._txtSearch = v; }, 
	get_hypClear: function() { return this._hypClear; }, 	set_hypClear: function(v) { if (this._hypClear !== v)  this._hypClear = v; }, 
	get_ibtnCollapseAll: function() { return this._ibtnCollapseAll; }, 	set_ibtnCollapseAll: function(v) { if (this._ibtnCollapseAll !== v)  this._ibtnCollapseAll = v; }, 
	get_ibtnExpandAll: function() { return this._ibtnExpandAll; }, 	set_ibtnExpandAll: function(v) { if (this._ibtnExpandAll !== v)  this._ibtnExpandAll = v; }, 
	get_aryGroupNuggetIDs: function() { return this._aryGroupNuggetIDs; }, 	set_aryGroupNuggetIDs: function(v) { if (this._aryGroupNuggetIDs !== v)  this._aryGroupNuggetIDs = v; }, 
	get_aryCategoryHeaderIDs: function() { return this._aryCategoryHeaderIDs; }, 	set_aryCategoryHeaderIDs: function(v) { if (this._aryCategoryHeaderIDs !== v)  this._aryCategoryHeaderIDs = v; }, 
	get_aryReportPanelIDs: function() { return this._aryReportPanelIDs; }, 	set_aryReportPanelIDs: function(v) { if (this._aryReportPanelIDs !== v)  this._aryReportPanelIDs = v; }, 
	get_aryReportSearchText: function() { return this._aryReportSearchText; }, 	set_aryReportSearchText: function(v) { if (this._aryReportSearchText !== v)  this._aryReportSearchText = v; }, 
    get_chkIsForPOHub: function() { return this._chkIsForPOHub; }, 	set_chkIsForPOHub: function(v) { if (this._chkIsForPOHub !== v)  this._chkIsForPOHub = v; }, 
   // get_pnlChkBox: function() { return this._pnlChkBox; }, 	set_pnlChkBox: function(v) { if (this._pnlChkBox !== v)  this._pnlChkBox = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Reports.callBaseMethod(this, "initialize");
		  this._strCKExp = 1;
	},
	
	goInit: function() {
	
		//if (this._txtSearch) $R_TXTBOX.addEnterPressedEvent(this._txtSearch, Function.createDelegate(this, this.searchReports));
		if (this._txtSearch) $addHandler(this._txtSearch, "keyup", Function.createDelegate(this, this.searchReports));
		if (this._hypClear) $addHandler(this._hypClear, "click", Function.createDelegate(this, this.clearSearch));
		if (this._ibtnExpandAll) $R_IBTN.addClick(this._ibtnExpandAll, Function.createDelegate(this, this.expandNuggets));
		if (this._ibtnCollapseAll) $R_IBTN.addClick(this._ibtnCollapseAll, Function.createDelegate(this, this.collapseNuggets));
		if (this._chkIsForPOHub) this._chkIsForPOHub.addClick(Function.createDelegate(this, this.CheckBox));
		if (this._chkIsForPOHub) this.showOnlyHubReport(this._chkIsForPOHub._blnChecked);
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		if (this._txtSearch) $clearHandlers(this._txtSearch);
		if (this._hypClear) $clearHandlers(this._hypClear);
		if (this._ibtnExpandAll) $R_IBTN.clearHandlers(this._ibtnExpandAll);
		if (this._ibtnCollapseAll) $R_IBTN.clearHandlers(this._ibtnCollapseAll);
		if (this._ctlControlStrip) this._ctlControlStrip.dispose();
		this._ctlControlStrip = null;
		this._txtSearch = null;
		this._hypClear = null;
		this._ibtnCollapseAll = null;
		this._ibtnExpandAll = null;
		this._aryGroupNuggetIDs = null;
		this._aryCategoryHeaderIDs = null;
		this._aryReportPanelIDs = null;
		this._aryReportSearchText = null;
		if(_chkIsForPOHub)this._chkIsForPOHub=null;
	//	if(this._pnlChkBox)this._pnlChkBox=null;
		Rebound.GlobalTrader.Site.Pages.Reports.callBaseMethod(this, "dispose");
	},

	CheckBox: function () {
	    if (this._chkIsForPOHub._blnChecked) {
	        this._isChecked = true;
	    }
	    else {
	        this._isChecked = false;
	    }

	    $R_FN.setCookie("isChecked", this._isChecked, this._strCKExp);
	    this.showOnlyHubReport(this._isChecked);
	    //this.IsPOReportChecked();

	},

	//IsPOReportChecked: function() { 
	
	//	//this.getData_Start();
	//	var obj = new Rebound.GlobalTrader.Site.Data();
	//	obj.set_PathToData("controls/Nuggets/Report");
	//	obj.set_DataObject("Report");
	//	obj.set_DataAction("IsPOReportChecked");
	//	obj._intTimeoutMilliseconds = 90 * 1000;
	//	obj.addParameter("isChecked", this._isChecked);
	//	obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
	//	obj.addError(Function.createDelegate(this, this.exportCSV_Error));
	//	obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
	//	$R_DQ.addToQueue(obj);
	//	$R_DQ.processQueue();
	//	obj = null;
	//},
	//exportCSV_OK: function(args) { 
	//    var res = args._result;
	//    alert("dfs");
		
	//	//this.getDataOK_End();
	//},

	exportCSV_Error: function(args) { 
		this.showError(true, args.get_ErrorMessage());
	},
	
	searchReports: function() {
		var str = this._txtSearch.value.trim().toUpperCase();
		this.showClear();
		this.setNuggetsRolledUpState(false);
		if (str.length == 0) {
			this.resetReports();
		} else {
			var aryCountForGroup = [];
			var aryCountForCategory = [];
			for (var i = 0, l = this._aryReportPanelIDs.length; i < l; i++) {
				var rpt = $get(this._aryReportPanelIDs[i]);
				if (rpt) {
					var blnShow = this._aryReportSearchText[i].toUpperCase().indexOf(str) >= 0;
					$R_FN.showElement(rpt, blnShow);
					if (blnShow) {
						this.addToCount(aryCountForGroup, rpt.getAttribute("GroupID"));
						this.addToCount(aryCountForCategory, rpt.getAttribute("CategoryID"));
					}
				}
				rpt = null;
			}
			//set headers visible or not
			for (i = 0, l = this._aryCategoryHeaderIDs.length; i < l; i++) {
				var hd = $get(this._aryCategoryHeaderIDs[i]);
				if (hd) {
					var intCategoryNo = Number.parseInvariant(hd.getAttribute("CategoryID"));
					if (!aryCountForCategory[intCategoryNo]) aryCountForCategory[intCategoryNo] = 0;
					$R_FN.showElement(hd, aryCountForCategory[intCategoryNo] > 0);
				}
				hd = null;
			}
			//set nuggets visible or not
			for (i = 0, l = this._aryGroupNuggetIDs.length; i < l; i++) {
				var ctl = $find(this._aryGroupNuggetIDs[i]);
				if (ctl) {
					var intGroupNo = Number.parseInvariant(ctl._element.getAttribute("GroupID"));
					if (!aryCountForGroup[intGroupNo]) aryCountForGroup[intGroupNo] = 0;
					$R_FN.showElement(ctl._element, aryCountForGroup[intGroupNo] > 0);
				}
				ctl = null;
			}
		}
	},
	
	expandNuggets: function() {
		this.setNuggetsRolledUpState(false);
	},
	
	collapseNuggets: function() {
		this.setNuggetsRolledUpState(true);
	},
	
	setNuggetsRolledUpState: function(bln) {
		for (var i = 0, l = this._aryGroupNuggetIDs.length; i < l; i++) {
			var ctl = $find(this._aryGroupNuggetIDs[i]);
			if (ctl) {
				if (ctl._blnIsRolledUp != bln) ctl.toggleRollUp();
			}
			ctl = null;
		}
		$R_IBTN.showButton(this._ibtnExpandAll, bln);
		$R_IBTN.showButton(this._ibtnCollapseAll, !bln);
	},
	
	resetReports: function() {
		for (var i = 0, l = this._aryReportPanelIDs.length; i < l; i++) {
			var rpt = $get(this._aryReportPanelIDs[i]);
			if (rpt) $R_FN.showElement(rpt, true);
			rpt = null;
		}
		for (i = 0, l = this._aryCategoryHeaderIDs.length; i < l; i++) {
			var hd = $get(this._aryCategoryHeaderIDs[i]);
			if (hd) $R_FN.showElement(hd, true);
			hd = null;
		}
		//set nuggets visible or not
		for (i = 0, l = this._aryGroupNuggetIDs.length; i < l; i++) {
			var ctl = $find(this._aryGroupNuggetIDs[i]);
			if (ctl) $R_FN.showElement(ctl._element, true);
			ctl = null;
		}
	},
	
	addToCount: function(ary, strID) {
		var intID = Number.parseInvariant(strID);
		if (!ary[intID]) ary[intID] = 0;
		ary[intID] += 1; 
	},
	
	clearSearch: function() {
		this._txtSearch.value = "";
		this.resetReports();
		this.showClear();
	},
	
	showClear: function() {
		$R_FN.showElement(this._hypClear, this._txtSearch.value.trim().length > 0);
	},
	showOnlyHubReport: function (bln) {
	    for (var i = 0, l = this._aryReportPanelIDs.length; i < l; i++) {
	        var rpt = $get(this._aryReportPanelIDs[i]);
            
	        if (rpt) {
	            if (rpt.getAttribute("class") != "reportListItemHUB") {
	                $R_FN.showElement(rpt, !bln);
	            }
	        }
	        rpt = null;
	    }
	}
	
};

Rebound.GlobalTrader.Site.Pages.Reports.registerClass("Rebound.GlobalTrader.Site.Pages.Reports", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
