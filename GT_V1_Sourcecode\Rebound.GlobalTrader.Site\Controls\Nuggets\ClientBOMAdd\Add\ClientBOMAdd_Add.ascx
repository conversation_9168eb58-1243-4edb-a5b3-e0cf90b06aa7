<%--
Marker     changed by      date         Remarks
--%>
<%@ Control Language="C#" CodeBehind="ClientBOMAdd_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientBOMAdd_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ClientBOMAdd_Add")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">			

			<ReboundUI_Form:FormField id="ctlName" runat="server" FieldID="txtName" ResourceTitle="BOMName" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtName" runat="server" Width="250"  MaxLength="50"/></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCompany" runat="server" FieldID="cmbCustomer" ResourceTitle="Company" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbCustomer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="AllSaleCompanies" /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="ddlContact" ResourceTitle="Contact" IsRequiredField="true">
				<Field><ReboundDropDown:ContactsForCompany ID="ddlContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField ID="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency"  IsRequiredField="true">                           
            <Field> <%--<ReboundDropDown:SellCurrency ID="ddlCurrency" runat="server" Visible="true" />--%>
                <ReboundDropDown:BuyCurrencyByGlobalNo ID="ddlCurrency" Visible="true" runat="server" />
            </Field>
             </ReboundUI_Form:FormField>
            <%--<ReboundUI_Form:FormField id="ctlSalesman" runat="server" FieldID="ddlSalesman" ResourceTitle="Salesperson" IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlSalesman" runat="server" /></Field>
			</ReboundUI_Form:FormField>	--%>
            <ReboundUI_Form:FormField id="ctlSalespersion" runat="server" FieldID="cmbSalespersion" ResourceTitle="Salesperson" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbSalespersion" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="SalesPersion" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlInActive" runat="server" FieldID="chkInActive" ResourceTitle="IsInactive">
				<Field><ReboundUI:ImageCheckBox ID="chkInActive" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" TextMode="MultiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>	
             <asp:TableRow >               
			<asp:TableCell id="TableCell2" ColumnSpan="2" style="border-bottom:1px dotted #CCCCCC;margin-top:10px;" >
            </asp:TableCell>
    </asp:TableRow> 
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
