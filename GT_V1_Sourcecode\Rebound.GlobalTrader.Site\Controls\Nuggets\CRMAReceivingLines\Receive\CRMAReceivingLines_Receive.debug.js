///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.initializeBase(this, [element]);
    this._intCRMAID = 0;
    this._intCRMALineID = 0;
    this._intInvoiceLineID = 0;
    this._intInvoiceLineAllocationID = 0;
    this._intLoginID = 0;
    this._intCustomerID = 0;
    this._intGoodsInID = 0;
    this._intGoodsInLineID = 0;
    this._intManufacturerNo = -1;
    this._intProductNo = -1;
    this._intPackageNo = -1;
    this._intROHS = -1;
    this._strCustomerName = "";
    this._intQuantityOutstanding = 0;
    this._intGlobalClientNo = -1;
    this._partNo = "";
    this._countSerialNo = -1;
    this._countSerialRecords = 0;
    this._reqSerialNo = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.prototype = {

    get_strSupplierName: function () { return this._strSupplierName; }, set_strSupplierName: function (v) { if (this._strSupplierName !== v) this._strSupplierName = v; },
    get_intCRMAID: function () { return this._intCRMAID; }, set_intCRMAID: function (v) { if (this._intCRMAID !== v) this._intCRMAID = v; },
    get_intCRMALineID: function () { return this._intCRMALineID; }, set_intCRMALineID: function (v) { if (this._intCRMALineID !== v) this._intCRMALineID = v; },
    get_intGoodsInID: function () { return this._intGoodsInID; }, set_intGoodsInID: function (v) { if (this._intGoodsInID !== v) this._intGoodsInID = v; },
    get_intLoginID: function () { return this._intLoginID; }, set_intLoginID: function (v) { if (this._intLoginID !== v) this._intLoginID = v; },
    get_ibtnContinue: function () { return this._ibtnContinue; }, set_ibtnContinue: function (v) { if (this._ibtnContinue !== v) this._ibtnContinue = v; },
    get_ibtnContinue_Footer: function () { return this._ibtnContinue_Footer; }, set_ibtnContinue_Footer: function (v) { if (this._ibtnContinue_Footer !== v) this._ibtnContinue_Footer = v; },
    get_ibtnSend: function () { return this._ibtnSend; }, set_ibtnSend: function (v) { if (this._ibtnSend !== v) this._ibtnSend = v; },
    get_ibtnSend_Footer: function () { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function (v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    get_radNewOrExisting: function () { return this._radNewOrExisting; }, set_radNewOrExisting: function (v) { if (this._radNewOrExisting !== v) this._radNewOrExisting = v; },
    get_trNewGI: function () { return this._trNewGI; }, set_trNewGI: function (v) { if (this._trNewGI !== v) this._trNewGI = v; },
    get_trGoodsIn: function () { return this._trGoodsIn; }, set_trGoodsIn: function (v) { if (this._trGoodsIn !== v) this._trGoodsIn = v; },
    get_ctlGoodsIn: function () { return this._ctlGoodsIn; }, set_ctlGoodsIn: function (v) { if (this._ctlGoodsIn !== v) this._ctlGoodsIn = v; },
    get_lblCurrency: function () { return this._lblCurrency; }, set_lblCurrency: function (v) { if (this._lblCurrency !== v) this._lblCurrency = v; },
    get_ctlSelectInvoiceLine: function () { return this._ctlSelectInvoiceLine; }, set_ctlSelectInvoiceLine: function (v) { if (this._ctlSelectInvoiceLine !== v) this._ctlSelectPOLine = v; },
    get_lblSelectInvoiceLine: function () { return this._lblSelectInvoiceLine; }, set_lblSelectInvoiceLine: function (v) { if (this._lblSelectInvoiceLine !== v) this._lblSelectPOLine = v; },
    get_strLoginFullName: function () { return this._strLoginFullName; }, set_strLoginFullName: function (v) { if (this._strLoginFullName !== v) this._strLoginFullName = v; },

    get_btnAll: function () { return this._btnAll; }, set_btnAll: function (v) { if (this._btnAll !== v) this._btnAll = v; },
    get_lblComplete: function () { return this._lblComplete; }, set_lblComplete: function (v) { if (this._lblComplete !== v) this._lblComplete = v; },
    get_trSerialNo: function () { return this._trSerialNo; }, set_trSerialNo: function (v) { if (this._trSerialNo !== v) this._trSerialNo = v; },
    get_btnAdd: function () { return this._btnAdd; }, set_btnAdd: function (v) { if (this._btnAdd !== v) this._btnAdd = v; },
    get_btnRefresh: function () { return this._btnRefresh; }, set_btnRefresh: function (v) { if (this._btnRefresh !== v) this._btnRefresh = v; },
    get_ctlGiSerialNumber: function () { return this._ctlGiSerialNumber; }, set_ctlGiSerialNumber: function (v) { if (this._ctlGiSerialNumber !== v) this._ctlGiSerialNumber = v; },
    get_ctlMultiSelectionCount: function () { return this._ctlMultiSelectionCount; }, set_ctlMultiSelectionCount: function (value) { if (this._ctlMultiSelectionCount !== value) this._ctlMultiSelectionCount = value; },
    get_tblSerialNoFinal: function () { return this._tblSerialNoFinal; }, set_tblSerialNoFinal: function (v) { if (this._tblSerialNoFinal !== v) this._tblSerialNoFinal = v; },
    get_reqSerialNo: function () { return this._reqSerialNo; }, set_reqSerialNo: function (v) { if (this._reqSerialNo !== v) this._reqSerialNo = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.callBaseMethod(this, "initialize");
        this._ctlGiSerialNumber.addPotentialStatusChange(Function.createDelegate(this, this.ctlGiSerialNumber_PotentialStatusChange));
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function () {
        
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            //alert("clicked me");
            //buttons
            var fnContinue = Function.createDelegate(this, this.continueClicked);
            $R_IBTN.addClick(this._ibtnContinue, fnContinue);
            $R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);
            var fnSend = Function.createDelegate(this, this.sendMail);
            $R_IBTN.addClick(this._ibtnSend, fnSend);
            $R_IBTN.addClick(this._ibtnSend_Footer, fnSend);

            //other controls
            this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
            this._ctlMail._ctlRelatedForm = this;
            this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));
            this._ctlGoodsIn.addItemSelected(Function.createDelegate(this, this.selectGoodsInItem));
            this.addFieldCheckBoxClickEvent("ctlSendMail", Function.createDelegate(this, this.chooseIfSendMail));

            if (this._btnAdd) $addHandler(this._btnAdd, "click", Function.createDelegate(this, this.serialSaveClicked));
            if (this._btnAll) $addHandler(this._btnAll, "click", Function.createDelegate(this, this.allClicked));
            //if (this._btnRefresh) $addHandler(this._btnRefresh, "click", Function.createDelegate(this, this.refreshClicked));
            if (this._btnRefresh) $addHandler(this._btnRefresh, "click", Function.createDelegate(this, this.refreshClicked));
            this._ctlMultiSelectionCount.registerTable(this._ctlGiSerialNumber._tblResults);
        }
        $R_FN.showElement(this._lblComplete, false);

        this._tblSerialNoFinal.show(false);
        this.showField("ctlAddUpdate", false);
        this.showHideSerialNo(false);

        this._countSerialRecords = 0;
        this.setFormFieldsToDefaults();
        this.resetSteps();
        this.gotoStep(1);
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
        if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlMail) this._ctlMail.dispose();
        if (this._ctlGoodsIn) this._ctlGoodsIn.dispose();
        if (this._ctlSelectInvoiceLine) this._ctlSelectInvoiceLine.dispose();
        if (this._ctlGiSerialNumber) this._ctlGiSerialNumber.dispose();

        this._strSupplierName = null;
        this._intCRMAID = null;
        this._intCRMALineID = null;
        this._intGoodsInID = null;
        this._intLoginID = null;
        this._ibtnContinue = null;
        this._ibtnContinue_Footer = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._radNewOrExisting = null;
        this._trNewGI = null;
        this._trGoodsIn = null;
        this._ctlGoodsIn = null;
        this._lblCurrency = null;
        this._ctlSelectInvoiceLine = null;
        this._lblSelectInvoiceLine = null;
        this._intInvoiceLineID = null;
        this._intInvoiceLineAllocationID = null;
        this._intCustomerID = null;
        this._intGoodsInLineID = null;
        this._intManufacturerNo = null;
        this._intProductNo = null;
        this._intPackageNo = null;
        this._intROHS = null;
        this._strCustomerName = null;
        this._intQuantityOutstanding = null;
        this._intGlobalClientNo = null;
        this._strLoginFullName = null;
        this._ctlGiSerialNumber = null;
        this._btnAll = null;
        this._lblComplete = null;
        this._trSerialNo = null;
        this._btnAdd = null;
        this._btnRefresh = null;
        this._ctlGiSerialNumber = null;
        this._ctlMultiSelectionCount = null;
        this._tblSerialNoFinal = null;
        this._countSerialRecords = null
        this._countSerialNo = null;
        this._reqSerialNo = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.callBaseMethod(this, "dispose");
    },

    setFieldsFromHeader: function (strCRMANumber, strCustomerName, intCustomerNo, intWarehouseNo, intShipViaNo, intCurrencyNo, strCurrency) {
        this._strCustomerName = strCustomerName;
        this._intCustomerID = intCustomerNo;
        this.setFieldValue("ctlCustomerRMA", strCRMANumber);
        this.setFieldValue("ctlCustomer", strCustomerName);
        this._intWarehouseNo = intWarehouseNo;
        this._intShipViaNo = intShipViaNo;
        this._intCurrencyNo = intCurrencyNo;
        this._strCurrency = strCurrency;

    },

    doInitialGoodsInSearch: function () {
        if (!this._ctlGoodsIn._initialized) setTimeout(Function.createDelegate(this, this.doInitialGoodsInSearch), 100);
        this._ctlGoodsIn.setFieldValue("ctlCRMANo", this.getFieldValue("ctlCustomerRMA"));
        this._ctlGoodsIn._intGlobalClientNo = this._intGlobalClientNo;
        this._ctlGoodsIn.getData();
    },

    selectGoodsInItem: function () {
        this._intGoodsInID = this._ctlGoodsIn.getSelectedID();
        this.continueClicked();
    },

    continueClicked: function () {
       
        this._strHeaderSelected = this.findWhichHeaderSelected();
        switch (this._ctlMultiStep._intCurrentStep) {
            case 1:
                this.nextStep();
                break;
            case 2:
                if (this._strHeaderSelected == "NEW") {
                    if (this.validateHeaderForm()) this.nextStep();
                } else {
                    this.nextStep();
                }
                break;
            case 3:
                this.nextStep();
                break;
            case 4:
                this.finishedForm();
                break;
        }
    },

    findWhichHeaderSelected: function () {
        for (var i = 0; i < 2; i++) {
            var rad = $get(String.format("{0}_{1}", this._radNewOrExisting.id, i));
            if (rad.checked) {
                $R_FN.showElement(this._trNewGI, i == 0);
                $R_FN.showElement(this._trGoodsIn, i == 1);
                switch (i) {
                    case 0: return "NEW"; break;
                    case 1: return "EXISTING"; break;
                }
            }
        }
    },

    stepChanged: function () {

        this._strHeaderSelected = this.findWhichHeaderSelected();
        var intStep = this._ctlMultiStep._intCurrentStep;
        $R_IBTN.enableButton(this._ibtnSave, intStep == 3);
        $R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 3);
        $R_IBTN.showButton(this._ibtnSend, intStep == 4);
        $R_IBTN.showButton(this._ibtnSend_Footer, intStep == 4);
        $R_IBTN.showButton(this._ibtnSave, intStep != 4);
        $R_IBTN.showButton(this._ibtnSave_Footer, intStep != 4);
        $R_IBTN.showButton(this._ibtnCancel, intStep != 4);
        $R_IBTN.showButton(this._ibtnCancel_Footer, intStep != 4);
        $R_IBTN.showButton(this._ibtnContinue, intStep == 1 || intStep == 4 || (this._strHeaderSelected == "NEW" && intStep == 2));
        $R_IBTN.showButton(this._ibtnContinue_Footer, intStep == 1 || intStep == 4 || (this._strHeaderSelected == "NEW" && intStep == 2));
        //alert(this._strLoginFullName);
        this._ctlMultiStep.showSteps(intStep != 4);
        if (intStep == 2) {
            $R_FN.showElement(this._trNewGI, this._strHeaderSelected == "NEW");
            $R_FN.showElement(this._trGoodsIn, this._strHeaderSelected != "NEW");
            if (this._strHeaderSelected == "NEW") {
                this.setFieldValue("ctlDateReceived", $R_FN.shortDate());
                this.setFieldValue("ctlReceivedBy", this._intLoginID);
                this.setFieldValue("ctlWarehouse", this._intWarehouseNo);
                this.setFieldValue("ctlShipVia", this._intShipViaNo);
                this.setFieldValue("ctlCurrency", this._intCurrencyNo);
                this.setFieldValue("ctlReceivedByLbl", this._strLoginFullName);
            } else {
                this.doInitialGoodsInSearch();
            }
            this.getFieldControl("ctlWarehouse")._intGlobalLoginClientNo = this._intGlobalClientNo;
            this.getFieldDropDownData("ctlWarehouse");
            this.getFieldControl("ctlShipVia")._intGlobalLoginClientNo = this._intGlobalClientNo;
            this.getFieldDropDownData("ctlShipVia");
            this.getFieldControl("ctlCurrency")._intGlobalLoginClientNo = this._intGlobalClientNo;
            this.getFieldDropDownData("ctlCurrency");
            this.getFieldControl("ctlReceivedBy")._intGlobalLoginClientNo = this._intGlobalClientNo;
            this.getFieldDropDownData("ctlReceivedBy");

            this.showField("ctlCurrency", this._intGlobalClientNo < 1);
            this.showField("ctlLblCurrency", this._intGlobalClientNo > 0);
            this.setFieldValue("ctlLblCurrency", this._strCurrency);
            this.showField("ctlReceivedBy", this._intGlobalClientNo < 1);
            this.showField("ctlReceivedByLbl", this._intGlobalClientNo > 0);
        }
        if (intStep == 3) {
            if (this._reqSerialNo)
                $("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlQuantity_pnlFieldControls").find('a').show();
            else
                $("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlQuantity_pnlFieldControls").find('a').hide();

            if (this._strHeaderSelected == "NEW") {
                this.setFieldValue("ctlCustomerDetail", this.getFieldValue("ctlCustomer"));
                this.setFieldValue("ctlAirWayBillDetail", this.getFieldValue("ctlAirWayBill"));
                this.setFieldValue("ctlReferenceDetail", this.getFieldValue("ctlReference"));
                this.setFieldValue("ctlReceivingNotesDetail", this.getFieldValue("ctlReceivingNotes"));
            } else {
                this.getGoodsIn();
            }
            this.showField("ctlGoodsInNumber", this._strHeaderSelected != "NEW");
            this.getCRMALine();
            this.getFieldDropDownData("ctlCountingMethod");
        }
        if (intStep == 4) {
            if (this._strHeaderSelected == "NEW") {
                this.getMessageText();
                this.setFieldValue("ctlSendMail", false);
                this.showMailButtons();
            } else {
                //this._ctlMultiStep.disableStep(3);
                this.finishedForm();
            }
            this._countSerialRecords = 0;
        }
    },

    saveHeaderThenLine: function (args) {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GIAdd");
        obj.set_DataObject("GIAdd");
        obj.set_DataAction("AddNewCRMA");
        obj.addParameter("ShipViaNo", this.getFieldValue("ctlShipVia"));
        obj.addParameter("AirWayBill", this.getFieldValue("ctlAirWayBill"));
        obj.addParameter("Reference", this.getFieldValue("ctlReference"));
        obj.addParameter("CMNo", this._intCustomerID);
        obj.addParameter("Notes", this.getFieldValue("ctlReceivingNotes"));
        obj.addParameter("DateReceived", this.getFieldValue("ctlDateReceived"));
        obj.addParameter("ReceivedBy", this._intGlobalClientNo > 0 ? this._intLoginID : this.getFieldValue("ctlReceivedBy"));
        obj.addParameter("CRMANo", this._intCRMAID);
        obj.addParameter("WarehouseNo", this.getFieldValue("ctlWarehouse"));
        obj.addParameter("CurrencyNo", this._intGlobalClientNo > 0 ? this._intCurrencyNo : this.getFieldValue("ctlCurrency"));
        obj.addParameter("Unavailable", false);
        obj.addParameter("GlobalClientNo", this._intGlobalClientNo);
        obj.addDataOK(Function.createDelegate(this, this.saveHeaderThenLineOK));
        obj.addError(Function.createDelegate(this, this.saveHeaderThenLineError));
        obj.addTimeout(Function.createDelegate(this, this.saveHeaderThenLineError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveHeaderThenLineOK: function (args) {
        if (args._result.NewID > 0) {
            this._intGoodsInID = args._result.NewID;
            this.saveLine();
        } else {
            this.saveHeaderThenLineError(args);
        }
    },

    saveHeaderThenLineError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    validateHeaderForm: function () {
        this.onValidate();
        var blnOK = true;
        if (!this.checkFieldEntered("ctlWarehouse")) blnOK = false;
        if (!this.checkFieldEntered("ctlShipVia")) blnOK = false;
        //Un commented: 22 May 2018,need to make required
        if (this._intGlobalClientNo <= 0)
            if (!this.checkFieldEntered("ctlCurrency")) blnOK = false;
        if (!this.checkFieldEntered("ctlReference")) blnOK = false;
        //if (!this.checkFieldEntered("ctlReceivedBy")) blnOK = false;
        if (!this.checkFieldEntered("ctlDateReceived")) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    getCRMALine: function () {
        this.showCRMALineDataFieldsLoading(true);
        $R_FN.showElement(this._pnlLines, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMAReceivingLines");
        obj.set_DataObject("CRMAReceivingLines");
        obj.set_DataAction("GetLine");
        obj.addParameter("ID", this._intCRMALineID);
        obj.addParameter("GICurrencyNo", this._intGlobalClientNo > 0 ? this._intCurrencyNo : this.getFieldValue("ctlCurrency"));
        obj.addParameter("GICurrencyCode", this.getFieldDropDownExtraText("ctlCurrency"));
        obj.addParameter("GIDate", this.getFieldValue("ctlDateReceived"));
        obj.addDataOK(Function.createDelegate(this, this.getCRMALineOK));
        obj.addError(Function.createDelegate(this, this.getCRMALineError));
        obj.addTimeout(Function.createDelegate(this, this.getCRMALineError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCRMALineOK: function (args) {
        var res = args._result;
        this.setFieldValue("ctlPartNo", $R_FN.setCleanTextValue(res.Part));
        this.setFieldValue("ctlDateCode", $R_FN.setCleanTextValue(res.DC));
        this.setFieldValue("ctlAuthorised", res.Quantity);
        this.setFieldValue("ctlQuantity", this._intQuantityOutstanding);
        this.setFieldValue("ctlPrice", res.Price);
        this.setFieldValue("ctlROHSStatus", $R_FN.writeROHS(res.ROHS));
        this.setFieldValue("ctlSupplierPart", $R_FN.setCleanTextValue(res.CustomerPart));
        this.setFieldValue("ctlManufacturer", $R_FN.setCleanTextValue(res.Manufacturer));
        this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(res.Product));
        this.setFieldValue("ctlPackage", $R_FN.setCleanTextValue(res.Package));
        this._intManufacturerNo = res.ManufacturerNo;
        this._intProductNo = res.ProductNo;
        this._intPackageNo = res.PackageNo;
        this._intROHS = res.ROHS;
        this._dblPrice = res.PriceRaw;
        this._intInvoiceLineID = res.InvoiceLineNo;
        this._partNo = $R_FN.setCleanTextValue(res.Part);
        this.showCRMALineDataFieldsLoading(false);
    },

    getCRMALineError: function (args) {
        this.showCRMALineDataFieldsLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    showCRMALineDataFieldsLoading: function (bln) {
        this.showFieldLoading("ctlPartNo", bln);
        this.showFieldLoading("ctlDateCode", bln);
        this.showFieldLoading("ctlAuthorised", bln);
        this.showFieldLoading("ctlQuantity", bln);
        this.showFieldLoading("ctlDateCode", bln);
        this.showFieldLoading("ctlPrice", bln);
        this.showFieldLoading("ctlROHSStatus", bln);
        this.showFieldLoading("ctlProduct", bln);
        this.showFieldLoading("ctlPackage", bln);
    },

    getGoodsIn: function () {
        this.showGoodsInFieldsLoading(true);
        $R_FN.showElement(this._pnlLines, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GIMainInfo");
        obj.set_DataObject("GIMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("ID", this._intGoodsInID);
        obj.addDataOK(Function.createDelegate(this, this.getGoodsInOK));
        obj.addError(Function.createDelegate(this, this.getGoodsInError));
        obj.addTimeout(Function.createDelegate(this, this.getGoodsInError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getGoodsInOK: function (args) {
        var res = args._result;
        this.setFieldValue("ctlCustomerDetail", res.SupplierName);
        this.setFieldValue("ctlGoodsInNumber", res.GoodsInNumber);
        this.setFieldValue("ctlAirWayBillDetail", res.AirWayBill);
        this.setFieldValue("ctlReferenceDetail", res.Reference);
        this.setFieldValue("ctlReceivingNotesDetail", res.ReceivingNotes);
        this.showGoodsInFieldsLoading(false);
    },

    getGoodsInError: function (args) {
        this.showGoodsInFieldsLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    showGoodsInFieldsLoading: function (bln) {
        this.showFieldLoading("ctlCustomerDetail", bln);
        this.showFieldLoading("ctlGoodsInNumber", bln);
        this.showFieldLoading("ctlAirWayBillDetail", bln);
        this.showFieldLoading("ctlReferenceDetail", bln);
        this.showFieldLoading("ctlReceivingNotesDetail", bln);
    },

    saveClicked: function () {
        this.resetFormFields();
        if (this.validateForm()) {
            if (this._strHeaderSelected == "NEW") {
                this.saveHeaderThenLine();
            } else {
                this.saveLine();
            }
        }
    },

    saveLine: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMAReceivingLines");
        obj.set_DataObject("CRMAReceivingLines");
        obj.set_DataAction("ReceiveLine");
        obj.addParameter("id", this._intGoodsInID);
        obj.addParameter("CRMALineNo", this._intCRMALineID);
        obj.addParameter("InvoiceLineAllocationID", this._intInvoiceLineAllocationID);
        obj.addParameter("ManufacturerNo", this._intManufacturerNo);
        obj.addParameter("ROHS", this._intROHS);
        obj.addParameter("ProductNo", this._intProductNo);
        obj.addParameter("PackageNo", this._intPackageNo);
        obj.addParameter("Part", this.getFieldValue("ctlPartNo"));
        obj.addParameter("DC", this.getFieldValue("ctlDateCode"));
        obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
        obj.addParameter("Price", this._dblPrice);
        obj.addParameter("ShipInCost", this.getFieldValue("ctlShipInCost"));
        obj.addParameter("QCNotes", this.getFieldValue("ctlQualityControlNotes"));
        obj.addParameter("Location", this.getFieldValue("ctlLocation"));
        obj.addParameter("SupplierPart", this.getFieldValue("ctlSupplierPart"));
        obj.addParameter("CurrencyNo", this._intCurrencyNo);
        obj.addParameter("CountingMethodNo", this.getFieldValue("ctlCountingMethod"));
        obj.addParameter("SerialNosRecorded", this.getFieldValue("ctlSerialNosRecorded"));
        obj.addParameter("PartMarkings", this.getFieldValue("ctlPartMarkings"));
        obj.addDataOK(Function.createDelegate(this, this.saveLineOK));
        obj.addError(Function.createDelegate(this, this.saveLineError));
        obj.addTimeout(Function.createDelegate(this, this.saveLineError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveLineError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveLineOK: function (args) {
        if (args._result.Result == true) {
            this._intGoodsInLineID = args._result.NewID;
            this.showSaving(false);
            this.showInnerContent(true);
            this.nextStep();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = true;
        if (!this.checkFieldEntered("ctlQuantity")) blnOK = false;
        if (!this.checkNumericFieldLessThanOrEqualTo("ctlQuantity", this.getFieldValue("ctlAuthorised"))) blnOK = false;
        if (!this.checkNumericFieldGreaterThan("ctlQuantity", 0)) blnOK = false;
        if (!this.checkFieldEntered("ctlLocation")) blnOK = false;
        if (!this.checkFieldEntered("ctlDateCode")) blnOK = false;
        if (!this.checkFieldEntered("ctlShipInCost")) blnOK = false;
        if (!blnOK) this.showError(true);
        if (this._reqSerialNo)
        {
            if (this._countSerialNo < 1) {
                this.showError(true, "Please attach serial no.");
                blnOK = false;
            }
            else if (this._countSerialNo > 0 && this._countSerialNo > this.getFieldValue("ctlQuantity"))
            {
                this.showError(true, "No. of Serial No(s) does not match with the Qty.");
                blnOK = false;
            }
        }
        

        return blnOK;
    },

    showMailButtons: function () {
        var bln = this.getFieldValue("ctlSendMail");
        this.showField("ctlSendMailMessage", bln);
        $R_IBTN.showButton(this._ibtnSend, bln);
        $R_IBTN.showButton(this._ibtnSend_Footer, bln);
        $R_IBTN.showButton(this._ibtnContinue, !bln);
        $R_IBTN.showButton(this._ibtnContinue_Footer, !bln);
    },

    chooseIfSendMail: function () {
        this.showMailButtons();
    },

    getMessageText: function () {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewGoodsIn(this._intGoodsInID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function (strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject($R_RES.NewGoodsInAdded);
    },

    validateMailForm: function () {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMail: function () {
        if (!this.validateMailForm()) return;
        Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), this._intCustomerID, Function.createDelegate(this, this.sendMailComplete));
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, false);
        $R_IBTN.showButton(this._ibtnSend_Footer, false);
    },

    sendMailComplete: function () {
        this.finishedForm();
    },

    finishedForm: function () {
        this._ctlMultiStep.showExplainLabel(false);
        this._ctlMultiStep.showSteps(false);
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, false);
        $R_IBTN.showButton(this._ibtnSend_Footer, false);
        this.showSavedOK(true);
        this.onSaveComplete();
    },
    OpenSerialForm: function () {
        
        this.setFieldValue("ctlQtyToShpped", this._intQuantityOutstanding);
        this.setFieldValue("ctlPart", this._partNo);
        this._ctlGiSerialNumber._intInvoiceLineNo = this._intInvoiceLineID;
        this._ctlGiSerialNumber.refereshCRMAGroup();
        this.showHideSerialNo(true);
        this.getAttachedSerial();
        this._ctlGiSerialNumber.getData();
        this._ctlMultiSelectionCount.show(this._ctlGiSerialNumber._tblResults._intTotalRecords > 0);

    },
    getAttachedSerial: function () {
        
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMAReceivingLines");
        obj.set_DataObject("CRMAReceivingLines");
        obj.set_DataAction("GetAttachedSerial");
        obj.addParameter("CustomerRMANo", this._intCRMAID);
        obj.addParameter("CustomerRMALineNo", this._intCRMALineID);
        obj.addDataOK(Function.createDelegate(this, this.getAttachedSerialOK));
        obj.addError(Function.createDelegate(this, this.getAttachedSerialError));
        obj.addTimeout(Function.createDelegate(this, this.getAttachedSerialError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getAttachedSerialOK: function (args) {
        res = args._result;

        this._countSerialNo = res.Count;
        this._tblSerialNoFinal.show(res.Results.length > 0);
        this._tblSerialNoFinal.clearTable();
        for (var i = 0; i < res.Results.length; i++) {
            var row = res.Results[i];
            
                var aryData = [
                  row.Group,
                   row.SerialNo,
                   String.format("<a href=\"javascript:void(0);\" Style='color:#006600' class='Delete'  onclick=\"$find('{0}').removeItem({1});\" class=\"quickSearchReselect\">Delete</a>", this._element.id, row.ID)

                ];
            
            var objExtraData = {
                SerialNoId: row.ID,
                SubGroup: row.Group,
                SerialNo: row.SerialNo
            };
            this._tblSerialNoFinal.addRow(aryData, row.ID, false, objExtraData);
            aryData = null;
            row = null;
            objExtraData = null;
        }
        this._countSerialRecords = (this._countSerialRecords + this._countSerialNo);
        this._tblSerialNoFinal.resizeColumns();
    },
    getAttachedSerialError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    searchClicked: function () {
        $R_FN.showElement(this._lblComplete, false);
        this._ctlGiSerialNumber.getData();
    },
    ctlGiSerialNumber_PotentialStatusChange: function () {
        this._ctlMultiSelectionCount.clearAll();
        this._ctlMultiSelectionCount.show(this._ctlGiSerialNumber._objResult.Count > 0);
        this.showField("ctlAddUpdate", this._ctlGiSerialNumber._objResult.Count > 0);
    },
    serialSaveClicked: function () {
        $R_FN.showElement(this._lblComplete, false);
        this.attachSerialNo();
    },
    attachSerialNo: function () {
        // alert("sdasda");
        if (!this.validateAddSerial()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMAReceivingLines");
        obj.set_DataObject("CRMAReceivingLines");
        obj.set_DataAction("AttachSerialNo");
        obj.addParameter("strSerialNo", this._ctlGiSerialNumber._tblResults._aryCurrentValues);
        obj.addParameter("CustomerRMANo", this._intCRMAID);
        obj.addParameter("CustomerRMALineNo", this._intCRMALineID);
        obj.addDataOK(Function.createDelegate(this, this.attachSerialComplete));
        obj.addError(Function.createDelegate(this, this.attachSerialError));
        obj.addTimeout(Function.createDelegate(this, this.attachSerialError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

        //this.LoadSerailNoGrid();
    },
    attachSerialError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    attachSerialComplete: function (args) {
        // alert(args._result.Result);
        if (args._result.Result == true) {
            // alert("updated");
            this._ctlGiSerialNumber.getData();
            //this.ctlGiSerialNumber_PotentialStatusChange();
            this.getAttachedSerial();
            $R_FN.showElement(this._lblComplete, false);
            // alert(this._ctlGiSerialNumber._tblResults.countRows());
        }

        this._ctlMultiSelectionCount.show(this._ctlGiSerialNumber._tblResults.countRows() > 0);
        this.showField("ctlAddUpdate", this._ctlGiSerialNumber._tblResults.countRows() > 0);
    },
    allClicked: function () {
        this._ctlGroup = this._ctlGiSerialNumber.getGroupValue();
        if (this._ctlGroup == undefined) {
            this._strErrorMessage = 'Please select BOX';
            this.showError(true, this._strErrorMessage);
            return;

        }
        this._strErrorMessage = '';
        this.showError(false, this._strErrorMessage);
        this.AttachSerialByCRMA();
        //Code Start
        this._ctlGiSerialNumber.refereshCRMAGroup();
        //Code End
    },

    AttachSerialByCRMA: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMAReceivingLines");
        obj.set_DataObject("CRMAReceivingLines");
        obj.set_DataAction("AttachSerialByCRMA");
        obj.addParameter("SubGroup", this._ctlGroup);
        obj.addParameter("InvoiceLineNo", this._intInvoiceLineID);
        obj.addParameter("CustomerRMANo", this._intCRMAID);
        obj.addParameter("CustomerRMALineNo", this._intCRMALineID);
        obj.addDataOK(Function.createDelegate(this, this.AttachSerialByCRMAOK));
        obj.addError(Function.createDelegate(this, this.AttachSerialByCRMAError));
        obj.addTimeout(Function.createDelegate(this, this.AttachSerialByCRMAError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    AttachSerialByCRMAOK: function (args) {
        this._strErrorMessage = '';
        $R_FN.showElement(this._lblComplete, false);
        if (args._result.Result == true) {
            this.getAttachedSerial();
            this._ctlGiSerialNumber.getData();
            return;
        }

        if (args._result.Result == false && args._result.ValidateMessage != null && args._result.NewID == -1) {
            this._strErrorMessage = args._result.ValidateMessage;
            this.showError(true, this._strErrorMessage);
            return;
        }

        if (args._result.Result == false && args._result.ValidateMessage != null && args._result.NewID == 0) {
            //  this._lblComplete = args._result.ValidateMessage;
            $R_FN.showElement(this._lblComplete, true);
            return;
        }
        else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },


    AttachSerialByCRMAError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    refreshClicked: function () {

        this._tblSerialNoFinal.clearTable();
        this._tblSerialNoFinal.show(false);
        this.showField("ctlAddUpdate", false);
        this.showHideSerialNo(false);
        //this.setFieldValue("ctlGroup", "");
        //this.setFieldValue("ctlSerialNo", "");       
        this._strErrorMessage = '';
        this.showError(false, this._strErrorMessage);
        this.getAttachedSerial();

    },
    showHideSerialNo: function (bln) {
        $R_FN.showElement(this._trSerialNo, bln);
        $R_FN.showElement(document.getElementById("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_TableCell4"), bln);
        $R_FN.showElement(document.getElementById("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_lblSerialCount1"), bln);

        //this.showField("FormField1", bln);
        this.showField("ctlQtyToShpped", bln);
        this.showField("ctlPart", bln);

    },
    validateAddSerial: function () {
        var blnOK = true;

        if (this._ctlGiSerialNumber._tblResults._aryCurrentValues.length <= 0) blnOK = false;
        if (!blnOK) this.showError(true, "Please select the any records");
        if ((this._countSerialNo) > parseInt(this.getFieldValue("ctlQtyToShpped"))) {
            blnOK = false;
            this.showError(true, "Cannot insert Serial Numbers beyond Quantity limit");
        }
        if (blnOK) this.showError(false);
        return blnOK;
    },
    removeItem: function (id) {
        var isDelete = confirm("Are you sure you want to delete");
        if (isDelete == true) {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/CRMAReceivingLines");
            obj.set_DataObject("CRMAReceivingLines");
            obj.set_DataAction("DeleteAttachedSerial");
            obj.addParameter("SerialId", id);
            obj.addParameter("CustomerRMANo", this._intCRMAID);
            obj.addParameter("CustomerRMALineNo", this._intCRMALineID);
            obj.addDataOK(Function.createDelegate(this, this.deleteComplete));
            obj.addError(Function.createDelegate(this, this.deleteError));
            obj.addTimeout(Function.createDelegate(this, this.deleteError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        }
        else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    deleteError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    deleteComplete: function (args) {
        if (args._result.Result == true) {
            this.getAttachedSerial();
            this._ctlGiSerialNumber.getData();
            this._ctlGiSerialNumber.refereshCRMAGroup();
            return;
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },
    refreshSerialForm: function () {

        this.setFieldValue("ctlQtyToShpped", this.getFieldValue("ctlQuantity"));
        this.setFieldValue("ctlPart", this._partNo);
        this._ctlGiSerialNumber._intInvoiceLineNo = this._intInvoiceLineID;
        this._ctlGiSerialNumber.refereshCRMAGroup();
        this.showHideSerialNo(true);
        this.getAttachedSerial();
        this._ctlGiSerialNumber.getData();
        this._ctlMultiSelectionCount.show(this._ctlGiSerialNumber._tblResults._intTotalRecords > 0);

    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceivingLines_Receive", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
