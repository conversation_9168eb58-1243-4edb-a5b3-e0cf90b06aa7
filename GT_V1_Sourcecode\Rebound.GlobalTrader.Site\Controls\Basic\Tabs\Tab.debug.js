///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Tab = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Tab.initializeBase(this, [element]);
	this._tabIndex = 0;
	this._titleText = "";
	this._isSelected = false;
};

Rebound.GlobalTrader.Site.Controls.Tab.prototype = {

	get_TabIndex: function() { return this._tabIndex; }, 	set_TabIndex: function(value) { if (this._tabIndex !== value)  this._tabIndex = value; }, 
	get_TitleText: function() { return this._titleText; }, 	set_TitleText: function(value) { if (this._titleText !== value)  this._titleText = value; }, 
	get_pnlTab: function() { return this._pnlTab; }, 	set_pnlTab: function(value) { if (this._pnlTab !== value)  this._pnlTab = value; }, 
	get_IsSelected: function() { return this._isSelected; }, 	set_IsSelected: function(value) { if (this._isSelected !== value)  this._isSelected = value; }, 
	get_ParentTabStrip: function() { return this._parentTabStrip; }, 	set_ParentTabStrip: function(value) { if (this._parentTabStrip !== value)  this._parentTabStrip = value; }, 
	get_pnlRelatedContent: function() { return this._pnlRelatedContent; }, 	set_pnlRelatedContent: function(value) { if (this._pnlRelatedContent !== value)  this._pnlRelatedContent = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Tab.callBaseMethod(this, "initialize");
		$addHandler(this.get_element(), "click", Function.createDelegate(this, this.onClick));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._pnlTab = null;
		this._ParentTabStrip = null;
		this._pnlRelatedContent = null;
		Rebound.GlobalTrader.Site.Controls.Tab.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},

	onClick: function() {
		this._parentTabStrip.selectTab(this._tabIndex);
	},
	
	selectTab: function(blnSelect) {
		if (blnSelect) {
			Sys.UI.DomElement.addCssClass(this._pnlTab, "tabItemSelected");
			Sys.UI.DomElement.removeCssClass(this._pnlTab, "tabItem");
		} else {
			Sys.UI.DomElement.addCssClass(this._pnlTab, "tabItem");
			Sys.UI.DomElement.removeCssClass(this._pnlTab, "tabItemSelected");
		}
		this._isSelected = blnSelect;
	},
	
	updateTitle: function(strNewText) {
		$R_FN.setInnerHTML(this._pnlTab, strNewText);
	},
	
	addToTitle: function(strNewText) {
		strNewText = String.format("{0} {1}", this._titleText, strNewText);
		this.updateTitle(strNewText);
	},
	
	addCountToTitle: function(intCount) {
		strNewText = String.format("{0} ({1})", this._titleText, intCount);
		this.updateTitle(strNewText);
	},
	
	resetTitle: function() {
		this.updateTitle(this._titleText);
	}

};

Rebound.GlobalTrader.Site.Controls.Tab.registerClass("Rebound.GlobalTrader.Site.Controls.Tab", Sys.UI.Control, Sys.IDisposable);