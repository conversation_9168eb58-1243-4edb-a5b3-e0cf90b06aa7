ALTER PROCEDURE [dbo].[usp_update_GILine_Quarantined]
    @GoodsInLineId int,
    @Quarantine bit,
    @UpdatedBy int,
    @RowsAffected int = NULL OUTPUT
AS
BEGIN
    /*       
    Marker            Owner             Date                Remarks       
    [001]             <PERSON>      28-04-2023          [RP-967] Deallocate any sales order from that line        
*/
    BEGIN TRANSACTION

    --declare variables                                          
    DECLARE @Location nvarchar(10) = NULL,
            @PurchaseOrderLineNo int,
            @PurchaseOrderNo int,
            @StockId int,
            @GoodsInNo int,
            @QuantityInStock int,
            @QuantityOnOrder int,
            @StockLogId int

    --get the stockId to use on the stockLog and the new values (CRMA)                                          
    SELECT @StockId = st.StockId,
           @GoodsInNo = gil.GoodsInNo,
           @Location = st.Location,
           @QuantityInStock = st.QuantityInStock,
           @QuantityOnOrder = st.QuantityOnOrder
    FROM dbo.tbStock st
        JOIN tbGoodsInLine gil
            ON gil.PurchaseOrderLineNo = st.PurchaseOrderLineNo
               AND gil.GoodsInLineId = st.GoodsInLineNo
    WHERE GoodsInLineId = @GoodsInLineId

    --update @Quarantine status In tbGoodsInLine table        
    UPDATE tbGoodsInline
    set Unavailable = @Quarantine
    WHERE GoodsInLineId = @GoodsInLineId

    DECLARE @StockLogDetail nvarchar(50)
    SET @StockLogDetail = ''
    IF @Location IS NULL
        SET @Location = ''
    IF NOT @Location =
       (
           SELECT isnull(Location, '') FROM tbStock WHERE StockId = @StockId
       )
        SET @StockLogDetail = 'Location'

    UPDATE dbo.tbStock
    SET Unavailable = @Quarantine,
        UpdatedBy = @UpdatedBy,
        DLUP = CURRENT_TIMESTAMP
    WHERE StockId = @StockId

    IF @Quarantine = 1
    BEGIN
        EXEC usp_insert_StockLog @StockLogTypeNo = 6,                 --Quarantined          
                                 @StockNo = @StockId,                 --          
                                 @QuantityInStock = @QuantityInStock, --          
                                 @QuantityOnOrder = @QuantityOnOrder, --          
                                 @UpdatedBy = @UpdatedBy,             --          
                                 @Detail = @StockLogDetail,           --          
                                 @StockLogId = @StockLogId OUTPUT
    --          
    END
    ELSE
    BEGIN
        EXEC usp_insert_StockLog @StockLogTypeNo = 17,                --Remove from quarantine          
                                 @StockNo = @StockId,                 --          
                                 @QuantityInStock = @QuantityInStock, --          
                                 @QuantityOnOrder = @QuantityOnOrder, --          
                                 @Detail = @StockLogDetail,           --          
                                 @UpdatedBy = @UpdatedBy,             --          
                                 @StockLogId = @StockLogId OUTPUT
    END

    /* ----   [001] start  ----- */
    /*       
            1. @GoodsInLineId is passed as argument, based on this @GoodsInNumber is fetched.  -- already done above       
            2. Based on @GoodsInNumber, search for @StockID in tbStock  -- already done above       
            3. Based on @StockId search for @stockNo in 'tbAllocation'  -- already done above       
            4. Using this @StockId, delete the record from 'tbAllocation' -- already done above       
        */
    --delete from tbAllocation where StockNo = @StockId  --(Rollback RP-967 for 18-06-2023 release)      

    /* ----   [001] End  ----- */



    COMMIT TRANSACTION

END
--          
SELECT @RowsAffected = @@ROWCOUNT