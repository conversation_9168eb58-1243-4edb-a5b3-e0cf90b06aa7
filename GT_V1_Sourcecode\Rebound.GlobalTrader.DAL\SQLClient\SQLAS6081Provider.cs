﻿//Marker     Changed by               Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>           31/08/2023   Add new module AS6081 for RP-2226
//[002]      <PERSON>             08/09/2023   RP-2226 GET Alert message at the time of creating SO, PO, stocks etc.
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;
using Rebound.GlobalTrader.DAL.Common.Entities;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlAS6081Provider: AS6081Provider
    {
        #region Type Of Supplier Setup Section
        public override Int32 InsertTypeOfSupplier(System.String name,System.Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_InsertTypeOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@TypeOfSupplierId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@TypeOfSupplierId"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert As6081 type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<AS6081Details> GetListTypeOfSupplier()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetTypeOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<AS6081Details> lst = new List<AS6081Details>();

                while (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.TypeOfSupplierId = GetReaderValue_Int32(reader, "TypeOfSupplierId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Incoterms", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool UpdateTypeOfSupplier(System.Int32?TypeOfSupplierId,System.String Name, System.Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_updateTypeOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@TypeOfSupplierId", SqlDbType.Int).Value = TypeOfSupplierId;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = Name;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Incoterm", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool DeleteTypeOfSupplier(System.Int32? TypeOfSupplierId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_DeleteTypeOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@TypeOfSupplierId", SqlDbType.Int).Value = TypeOfSupplierId;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 ValidateTypeOfSupplier(System.Int32? ID, System.String Name,System.Int32? Screen)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int ret = -1;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_ValidateTypeOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = Name;
                cmd.Parameters.Add("@Screen", SqlDbType.Int).Value = Screen;
                cmd.Parameters.Add("@ItemId", SqlDbType.Int).Value = ID;
                cmd.Parameters.Add("@ISValid", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                ret = (Int32)cmd.Parameters["@ISValid"].Value;

            }
            catch (SqlException sqlex)
            {
                ret = -2;
                throw new Exception("Failed to validate As6081 type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return ret;
        }
        #endregion

        #region Reason For Chosen Supplier Setup Section
        public override Int32 InsertReasonForChosenSupplier(System.String name, System.Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_InsertReasonForChosenSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@ReasonForChosenSupplierId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ReasonForChosenSupplierId"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert As6081 type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<AS6081Details> GetListReasonForChosenSupplier()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetReasonForChosenSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<AS6081Details> lst = new List<AS6081Details>();

                while (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.TypeOfSupplierId = GetReaderValue_Int32(reader, "TypeOfSupplierId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Incoterms", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool UpdateReasonForChosenSupplier(System.Int32? ReasonForChosenSupplierId, System.String Name, System.Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_updateReasonForChosenSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ReasonForChosenSupplierId", SqlDbType.Int).Value = ReasonForChosenSupplierId;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = Name;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Incoterm", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool DeleteReasonForChosenSupplier(System.Int32? ReasonForChosenSupplierId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_DeleteReasonForChosenSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ReasonForChosenSupplierId", SqlDbType.Int).Value = ReasonForChosenSupplierId;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 ValidateReasonForChosenSupplier(System.Int32? ID, System.String Name, System.Int32? Screen)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int ret = -1;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_ValidateReasonForChosenSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = Name;
                cmd.Parameters.Add("@Screen", SqlDbType.Int).Value = Screen;
                cmd.Parameters.Add("@ItemId", SqlDbType.Int).Value = ID;
                cmd.Parameters.Add("@ISValid", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                ret = (Int32)cmd.Parameters["@ISValid"].Value;

            }
            catch (SqlException sqlex)
            {
                ret = -2;
                throw new Exception("Failed to validate As6081 type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return ret;
        }
        #endregion

        #region Risk Of Supplier Setup Section
        public override Int32 InsertRiskOfSupplier(System.String name, System.Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_InsertRiskOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@RiskOfSupplierId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RiskOfSupplierId"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to insert As6081 type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<AS6081Details> GetListRiskOfSupplier()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetRiskOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<AS6081Details> lst = new List<AS6081Details>();

                while (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.TypeOfSupplierId = GetReaderValue_Int32(reader, "TypeOfSupplierId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Incoterms", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool UpdateRiskOfSupplier(System.Int32? RiskOfSupplierId, System.String Name, System.Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_updateRiskOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@RiskOfSupplierId", SqlDbType.Int).Value = RiskOfSupplierId;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = Name;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Incoterm", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool DeleteRiskOfSupplier(System.Int32? RiskOfSupplierId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_DeleteRiskOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@RiskOfSupplierId", SqlDbType.Int).Value = RiskOfSupplierId;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 ValidateRiskOfSupplier(System.Int32? ID, System.String Name, System.Int32? Screen)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            int ret = -1;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_ValidateRiskOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = Name;
                cmd.Parameters.Add("@Screen", SqlDbType.Int).Value = Screen;
                cmd.Parameters.Add("@ItemId", SqlDbType.Int).Value = ID;
                cmd.Parameters.Add("@ISValid", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                ret = (Int32)cmd.Parameters["@ISValid"].Value;

            }
            catch (SqlException sqlex)
            {
                ret = -2;
                throw new Exception("Failed to validate As6081 type of supplier.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return ret;
        }
        #endregion


        #region Master Dropdowns for AS6081
        public override List<AS6081Details> DropDownTypeOfSupplier()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_dropdownTypeOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<AS6081Details> lst = new List<AS6081Details>();
                while (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.Id = GetReaderValue_Int32(reader, "TypeOfSupplierId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Type Of Supplier", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<AS6081Details> DropDownReasonForSupplier()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_dropdownReasonForSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<AS6081Details> lst = new List<AS6081Details>();
                while (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.Id = GetReaderValue_Int32(reader, "ReasonForChosenSupplierId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Reason For Supplier", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<AS6081Details> DropDownRiskOfSupplier()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_dropdownRiskOfSupplier", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<AS6081Details> lst = new List<AS6081Details>();
                while (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.Id = GetReaderValue_Int32(reader, "RiskOfSupplierId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Risk Of Supplier", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<AS6081Details> DropDownAssignSecurityGroup(System.Int32? ClientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_dropdownAssignSecurityGroup", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<AS6081Details> lst = new List<AS6081Details>();
                while (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.Id = GetReaderValue_Int32(reader, "SecurityGroupId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Assign Security Group", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        #endregion

        #region Approver change history
        public override List<AS6081Details> GetApproverChnageLog(System.Int32? ID)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetApproverAssignmentLog", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@BomNo", SqlDbType.Int).Value = ID;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<AS6081Details> lst = new List<AS6081Details>();
                while (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.AssignmentHistoryId = GetReaderValue_Int32(reader, "AssignmentHistoryId",0);
                    obj.DocumentId = GetReaderValue_Int32(reader, "DocumentId",0);
                    obj.DocumentNumber = GetReaderValue_String(reader, "DocumentNumber", "");
                    obj.AssignedTo = GetReaderValue_String(reader, "AssignedTo", "");
                    obj.AssignedBy = GetReaderValue_String(reader, "AssignedBy", "");
                    obj.AssignmentType = GetReaderValue_String(reader, "AssignmentType", "");
                    obj.LogDate = GetReaderValue_DateTime(reader, "LogDate", DateTime.MinValue);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get log details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        #endregion


        public override AS6081Details GetCountry(System.Int32? SupplierNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetCountryDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = SupplierNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    AS6081Details obj = new AS6081Details();
                    obj.IsCountryFound = GetReaderValue_NullableBoolean(reader, "IsCountryFound", false);
                    obj.CountryName = GetReaderValue_String(reader, "CountryName", "");
                    obj.CountryNo = GetReaderValue_Int32(reader, "CountryNo", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SourcingResult", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[002] start

        /// <summary>
        /// get the message by AlertMessageId
        /// use [usp_AS6081_SelectAlertMessageByID]
        /// </summary>
        /// <param name="messageId"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public override AS6081AlertMessageDetails GetAlertMessageById(int alertMessageId)
        {
            using(SqlConnection cn = new SqlConnection(this.ConnectionString))
            {
                using(SqlCommand cmd = new SqlCommand("usp_AS6081_SelectAlertMessageByID",cn))
                {
                    
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 30;
                    cmd.Parameters.AddWithValue("@messageId", alertMessageId);
                   
                    
                    SqlParameter outputParam = new SqlParameter
                    {
                        ParameterName = "@statusMessage",
                        SqlDbType = SqlDbType.NVarChar,
                        Size = 500,
                        Direction = ParameterDirection.Output
                    };

                    cmd.Parameters.Add(outputParam);

                    try
                    {
                        cn.Open();
                        
                        using (DbDataReader reader = cmd.ExecuteReader())
                        {
                            AS6081AlertMessageDetails msgDtl = new AS6081AlertMessageDetails();
                            if (reader.Read())
                            {
                                msgDtl.AlertMessageId = GetReaderValue_Int32(reader, "AlertMessageId", 0);
                                msgDtl.ShortName = GetReaderValue_String(reader, "ShortName", "");
                                msgDtl.Message = GetReaderValue_String(reader, "Message", "");
                            }
                            return msgDtl;
                        }
                    }
                    catch(Exception ex)
                    {
                        throw new Exception("Failed to get 'Alert message'", ex.InnerException);
                    }
                    
                }
            }

        }
        /// <summary>
        /// uses [usp_AS6081_SelectAlertMessageByOperationType] stored procedure
        /// Pass the @operationType parameter with values like 'PO', 'SO', 'Quote', 'Stock', etc.     
        ///This allows developers to retrieve alert messages from the tbAS6081AlertMessage table without needing to modify
        ///the C# code and deploy it to production when they want to change an alert message     
        ///or add a new one for a specific operation.
        /// </summary>
        /// <param name="operationType"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public override AS6081AlertMessageDetails GetAlertMessageByOperationType(System.String operationType )
        {
            AS6081AlertMessageDetails msgDtl = new AS6081AlertMessageDetails();
            string statusMessage = string.Empty;// initialize the out parameter
            using (SqlConnection cn = new SqlConnection(this.ConnectionString))
            {
                using (SqlCommand cmd = new SqlCommand("usp_AS6081_SelectAlertMessageByOperationType",cn))
                {
                    
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 30;
                    cmd.Parameters.AddWithValue("@operationType", operationType);
                    cmd.Parameters.Add("@statusMessage", SqlDbType.NVarChar,1000).Direction = ParameterDirection.Output;
                    
                    try
                    {
                        cn.Open();
                        using (DbDataReader reader = ExecuteReader(cmd,CommandBehavior.SingleRow))
                        {
                            if (reader.Read())
                            {
                                msgDtl.AlertMessageId = GetReaderValue_Int32(reader, "AlertMessageId", 0);
                                msgDtl.ShortName = GetReaderValue_String(reader, "ShortName", "");
                                msgDtl.Message = GetReaderValue_String(reader, "Message", "");
                            }
                        }
                        statusMessage = cmd.Parameters["@statusMessage"].Value as string;
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Failed to get 'Alert message'", ex);
                    }
                }
            }
            return msgDtl;
        }

        public override AS6081AlertMessageDetails GetAlertMessageByOperationTypeForSoPrint(System.String operationType, System.Int32 soID)
        {
            AS6081AlertMessageDetails msgDtl = new AS6081AlertMessageDetails();
            string statusMessage = string.Empty;// initialize the out parameter
            using (SqlConnection cn = new SqlConnection(this.ConnectionString))
            {
                using (SqlCommand cmd = new SqlCommand("usp_AS6081_SelectAlertMessageByOperationTypeForSoPrint", cn))
                {

                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.CommandTimeout = 30;
                    cmd.Parameters.AddWithValue("@operationType", operationType);
                    cmd.Parameters.AddWithValue("@soId", soID);
                    cmd.Parameters.Add("@statusMessage", SqlDbType.NVarChar, 1000).Direction = ParameterDirection.Output;

                    try
                    {
                        cn.Open();
                        using (DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow))
                        {
                            if (reader.Read())
                            {
                                msgDtl.AlertMessageId = GetReaderValue_Int32(reader, "AlertMessageId", 0);
                                msgDtl.ShortName = GetReaderValue_String(reader, "ShortName", "");
                                msgDtl.Message = GetReaderValue_String(reader, "Message", "");
                                msgDtl.AS6081 = GetReaderValue_Boolean(reader, "AS6081", false);
                            }
                        }
                        statusMessage = cmd.Parameters["@statusMessage"].Value as string;
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("Failed to get 'Alert message'", ex);
                    }
                }
            }
            return msgDtl;
        }
        //[002] end

    }
}
