﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('dbo.usp_SavePriceQuoteImport_ColumnHeading', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_SavePriceQuoteImport_ColumnHeading
END
GO

CREATE PROCEDURE [dbo].[usp_SavePriceQuoteImport_ColumnHeading]                          
 @InserColumnList NVARCHAR(3000),                          
 @SelectColumns NVARCHAR(3000),                          
 @ClientId INT,                     
 @SelectedClientId int,                       
 @UserId INT                          
AS                          
BEGIN                           
 declare @chkexit int=0;                
set @chkexit=(SELECT count(*) FROM BorisGlobalTraderImports.dbo.tbPriceQuoteImportColumnHeading WHERE ClientId=@ClientId and SelectedClientId=@SelectedClientId AND CreatedBy=@UserId)                
if(@chkexit =1)                
begin                
--print @chkexit                
delete from BorisGlobalTraderImports.dbo.tbPriceQuoteImportColumnHeading where  ClientId=@ClientId AND CreatedBy=@UserId and SelectedClientId=@SelectedClientID                  
 delete from BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData where  ClientId=@ClientId AND CreatedBy=@UserId and SelectedClientId=@SelectedClientID                  
end                
                
IF NOT EXISTS(SELECT TOP 1 * FROM BorisGlobalTraderImports.dbo.tbPriceQuoteImportColumnHeading WHERE ClientId=@ClientId and SelectedClientId=@SelectedClientId AND CreatedBy=@UserId)                          
BEGIN                          
 DECLARE @DynamicQuery NVARCHAR(4000)=''                           
  SET @InserColumnList ='INSERT INTO BorisGlobalTraderImports.dbo.tbPriceQuoteImportColumnHeading('+@InserColumnList                          
 SET @InserColumnList = @InserColumnList +', ClientId,SelectedClientId,CreatedBy' ;                          
 SET @DynamicQuery =  @DynamicQuery+@InserColumnList  +') Select '+@SelectColumns+','+CONVERT(NVARCHAR(10), @ClientId)+','+CONVERT(NVARCHAR(10), @SelectedClientId)+                                
      +','+CONVERT(NVARCHAR(10), @UserId);                          
  EXEC(@DynamicQuery)                          
END                
END 

GO


