///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           05/02/2014    CR:- Add AS9120 Requirement in GT application
[002]      <PERSON><PERSON><PERSON>     12-Sep-2018   [REB-11024]:Option to export quote report 
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail = function (el) {
    Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.prototype = {

    get_intQuoteID: function() { return this._intQuoteID; }, set_intQuoteID: function(v) { if (this._intQuoteID !== v) this._intQuoteID = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlQuoteLines: function() { return this._ctlQuoteLines; }, set_ctlQuoteLines: function(v) { if (this._ctlQuoteLines !== v) this._ctlQuoteLines = v; },
    get_btnPrint: function () { return this._btnPrint; }, set_btnPrint: function (v) { if (this._btnPrint !== v) this._btnPrint = v; },
    get_lblStatus: function () { return this._lblStatus; }, set_lblStatus: function (v) { if (this._lblStatus !== v) this._lblStatus = v; },
    get_pnlStatus: function () { return this._pnlStatus; }, set_pnlStatus: function (v) { if (this._pnlStatus !== v) this._pnlStatus = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.callBaseMethod(this, "initialize");
    },

    goInit: function () {
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        if (this._ctlMainInfo) this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_SaveEditComplete));
        if (this._ctlMainInfo) this._ctlMainInfo.addSaveCloseComplete(Function.createDelegate(this, this.ctlMainInfo_SaveCloseComplete));
        if (this._ctlQuoteLines) this._ctlQuoteLines.addPotentialStatusChange(Function.createDelegate(this, this.ctlLines_PotentialStatusChange));
        if (this._ctlQuoteLines) this._ctlQuoteLines.addRefereshSOButtonChange(Function.createDelegate(this, this.ctlLines_RefereshSOButtonChange));
        if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printQuote));
        if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailQuote));
        if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.callBaseMethod(this, "goInit");
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._btnPrint) this._btnPrint.dispose();
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlQuoteLines) this._ctlQuoteLines.dispose();
        this._btnPrint = null;
        this._ctlMainInfo = null;
        this._ctlQuoteLines = null;
        this._intQuoteID = null;
        this._lblStatus = null;
        this._pnlStatus = null;
        Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.callBaseMethod(this, "dispose");
    },

    ctlMainInfo_GetDataComplete: function () {
        this._ctlQuoteLines.setFieldsFromHeader(
			this._ctlMainInfo.getFieldValue("hidCustomerName")
			, this._ctlMainInfo.getFieldValue("hidCustomerID")
			, this._ctlMainInfo.getFieldValue("hidContactNo")
			, this._ctlMainInfo.getFieldValue("hidCurrencyNo")
			, this._ctlMainInfo.getFieldValue("hidCurrencyCode")
			, this._ctlMainInfo.getFieldValue("ctlDateQuoted")
			, this._ctlMainInfo._blnCanCreateSO
			);
        //[001] code start
        this._ctlQuoteLines._blnAS9120 = this._ctlMainInfo.getFieldValue("ctlAS9120");
        this.updateQuoteMainInfoControl();
        //[001] code end
        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidHStatus"));
    },

    ctlMainInfo_SaveEditComplete: function () {
        //[001] code start
        this._ctlQuoteLines._blnAS9120 = this._ctlMainInfo.getFieldValue("ctlAS9120");
        //[001] code end
        this._ctlQuoteLines.getTabData();
    },

    ctlMainInfo_SaveCloseComplete: function () {
        //[001] code start
        this._ctlQuoteLines._blnAS9120 = this._ctlMainInfo.getFieldValue("ctlAS9120");
        //[001] code end
        this._ctlQuoteLines.getTabData();
    },

    ctlLines_PotentialStatusChange: function () {
        this.updateQuoteMainInfoControl();

       // 
      //  this._ctlQuoteLines.enableButton(true);
       // alert("Called");
    },
    ctlLines_RefereshSOButtonChange: function () {
        this._ctlMainInfo.getData();
    },

    printQuote: function () {
        var windowPrint = $R_FN.openPrintQuoteWindow($R_ENUM$PrintObject.Quote, this._intQuoteID);
        var quoteStatus = $('#ctl00_cphMain_ctlPageTitle_ctl20_lblStatus').text();
        if (windowPrint.opener && (quoteStatus == "Pending" || quoteStatus == "New" || quoteStatus == "Partially Offered")) {
            setTimeout(() => {
                this.updateQuoteStatus();
            }, 3000);
        }
    },

    emailQuote: function () {
        var windowPrint = $R_FN.openPrintQuoteWindow($R_ENUM$PrintObject.Quote, this._intQuoteID, true);
        var quoteStatusText = $('#ctl00_cphMain_ctlPageTitle_ctl20_lblStatus').text();

        if (windowPrint.opener != null && (quoteStatusText == "Pending" || quoteStatusText == "New" || quoteStatusText == "Partially Offered")) {
            window.addEventListener("message", function (event) {
                if (event.origin === location.origin && event.data) {
                    this._ctlMainInfo.getData();
                }
            }.bind(this), false);
        }
    },
    customTemplate: function () {
        $R_FN.openCustomTemplateWindow($R_ENUM$PrintObject.Quote, this._intQuoteID);
    },
    printOtherDocs: function () {
        if (this._btnPrint._strExtraButtonClickCommand == "EmailQOHTML") $R_FN.openPrintWindow($R_ENUM$PrintObject.QuoteEmail, this._intQuoteID, true);
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intQuoteID, false, "Quote");
        //[001] start
        if (this._btnPrint._strExtraButtonClickCommand == "ExportToExcel") this.exportClicked();
        //[001] end
        if (this._btnPrint._strExtraButtonClickCommand == "CustomTemplate") this.customTemplate();
      
    },
    updateQuoteMainInfoControl: function () {
     
        this._ctlMainInfo.clearMessages();
        this._ctlMainInfo._blnAllLineContainSource = this._ctlQuoteLines._blnAllLineContainSource;
        this._ctlMainInfo._blnAllLineContainMSL = this._ctlQuoteLines._blnAllLineContainMSL;
        this._ctlMainInfo._blnAllLineContainProduct = this._ctlQuoteLines._blnAllLineContainProduct;
        if (this._ctlMainInfo.getFieldValue("ctlAS9120") == true) {
            if (this._btnPrint) $R_FN.showElement(this._btnPrint._element, this._ctlQuoteLines._blnAllLineContainSource);
            if (this._ctlMainInfo._ibtnCreateSalesOrder) $R_IBTN.enableButton(this._ctlMainInfo._ibtnCreateSalesOrder, this._ctlMainInfo._blnCanCreateSO && this._ctlMainInfo._TotalQuantityLine && this._ctlQuoteLines._blnAllLineContainSource);
        }
        else {
            if (this._btnPrint) $R_FN.showElement(this._btnPrint._element, true);
        }
        if (this._ctlMainInfo.getFieldValue("ctlAS9120") == true && this._ctlQuoteLines._blnAllLineContainSource == false) {
            this._ctlMainInfo.showProductSourceMessage();
        }
        else {
            this._ctlMainInfo.clearMessages();
        }
       
    },
    //[001] start
    exportClicked: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/QuoteMainInfo");
        obj.set_DataObject("QuoteMainInfo");
        obj.set_DataAction("ExportQuoteReport");
        obj.addParameter("id", this._intQuoteID);
        obj._intTimeoutMilliseconds = 90 * 1000;
        obj.addDataOK(Function.createDelegate(this, this.exportComplete));
        obj.addError(Function.createDelegate(this, this.exportError));
        obj.addTimeout(Function.createDelegate(this, this.exportError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    exportError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    exportComplete: function (args) {
        if (args._result.Result == 1) {
            var dt = new Date();
            //location.href = String.format("{0}?t={1}", args._result.FileURL, dt.getTime());;
            var IsFileExists = false;
            IsFileExists = this.UrlExists((window.location.origin + '/' + args._result.FileURL));
            if (IsFileExists == true) {
                location.href = String.format("{0}?t={1}", args._result.FileURL, dt.getTime());
            }
            dt = null;
        } else {
            //alert("Error");
        }
        //this.getDataOK_End();
    },
    updateQuoteStatus: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/QuoteMainInfo");
        obj.set_DataObject("QuoteMainInfo");
        obj.set_DataAction("UpdatePrintQuoteStatus");
        obj.addParameter("id", this._intQuoteID);
        obj._intTimeoutMilliseconds = 90 * 1000;
        obj.addDataOK(Function.createDelegate(this, this.updateQuoteStatusComplete));
        obj.addError(Function.createDelegate(this, this.updateQuoteStatusError));
        obj.addTimeout(Function.createDelegate(this, this.updateQuoteStatusError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    updateQuoteStatusError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    updateQuoteStatusComplete: function (args) {
        if (args._result.Result) {
            this._ctlMainInfo.getData();
        } else {
            //alert("Error");
        }
    },
    UrlExists: function (url) {
        var http = new XMLHttpRequest();
        http.open('HEAD', url, false);
        try {
            http.send();
        }
        catch (err) {
        }
        return http.status != 404;
    }
    //[001] end

};
Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.QuoteDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
