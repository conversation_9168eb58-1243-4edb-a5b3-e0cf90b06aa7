﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO




ALTER  PROCEDURE  [dbo].[usp_source_Offer]                       
--********************************************************************************************                            
--* RP 09.03.2011:                            
--* - add recompile option                            
--*                            
--* RP 25.05.2010:                            
--* - remove UNIONS, process Clients in code                            
--*                            
--* SK 17.02.2010:                            
--* - adjust display of external client data                             
--*                            
--* SK 20.01.2010:                            
--* - add ClientId to parameters and predicate: if equal display data as now, if not show                              
--*   ClientName as customer  - with no hyperlink - and do not show any price                              
--*                            
--* RP 18.01.2010:                            
--* - coalesce SupplierName and ManufacturerName                            
--*                            
--* RP 05.06.2009:                            
--* - search with <PERSON>I<PERSON>                            
--*                            
--* SK 01.06.2009:                            
--* - add order by clause                            
--*Marker     Changed by      Date         Remarks                              
--*[001]      Vinay           16/10/2012   Display supplier type in stock grid                            
--********************************************************************************************                                
    @ClientId INT                            
  , @PartSearch NVARCHAR(50)                            
  , @Index int =1                      
  , @StartDate datetime = NULL                        
  , @FinishDate datetime = NULL   
  , @OrderBy int = 1
  , @SortDir int = 1                        
WITH RECOMPILE AS                             
 BEGIN                        
     --DECLARE VARIABLE                    
     DECLARE @Month int                      
     DECLARE @FROMDATE DATETIME                      
     DECLARE @ENDDATE DATETIME                      
     DECLARE @OutPutDate DATETIME       
           
      DECLARE @FinishDateVW DATETIME                     
     DECLARE @FROMDATEVW DATETIME                            
     DECLARE @ENDDATEVW DATETIME                      
     SET @Month=6                      
     /*                    
        When we get index 1 then we find the maximum date from matching record                    
        and decsrease no of month for the start date.                    
     */   
	 
	
	   declare @HUBName nvarchar(300)     
	  select top 1 @HUBName = CompanyName from tbCompany where ClientNo = @ClientId and IsPOHub=1                
     IF @Index=1                      
     BEGIN          
           
       SELECT @FinishDateVW=MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) FROM tbSourcingResult tsr              
      WHERE            
       tsr.SourcingTable in('PQ','EXPH','OFPH')  AND                          
       tsr.FullPart LIKE @PartSearch                              
      SET @FROMDATEVW=dbo.ufn_get_date_from_datetime(DATEADD(month,-@Month,@FinishDateVW))                            
      SET @ENDDATEVW =dbo.ufn_get_date_from_datetime(@FinishDateVW)       
            
                       
      SELECT @FinishDate=MAX(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) FROM [BorisGlobalTraderImports].dbo.tbOffer o        
        JOIN    tbClient cl ON o.ClientNo = cl.ClientId                
      WHERE   ((o.ClientNo = @ClientId)                        
             OR (o.ClientNo <> @ClientId                        
               --  AND cl.OwnDataVisibleToOthers = 1))                   
			   AND (case when o.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
     AND FullPart LIKE @PartSearch                      
      SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-@Month,@FinishDate))                      
      SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)         
        
END        
    ELSE                     
     BEGIN          
                       
       SET @FROMDATE=dbo.ufn_get_date_from_datetime(@StartDate)                      
       SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)      
               
       SET @FROMDATEVW=dbo.ufn_get_date_from_datetime(@StartDate)                      
       SET @ENDDATEVW =dbo.ufn_get_date_from_datetime(@FinishDate)         
     END                    
      --SET THE OUTPUT DATE                    
      SET @OutPutDate=DATEADD(month,-@Month,@FinishDate)    


	if @FinishDate IS NULL
	BEGIN
	 SET @Index=3
	END
-- If Index value equal to 3 then more than one year data will be pick from archive database.	  
 IF @Index = 3
     BEGIN                    
       ;                                              
    WITH    cteSearch                                              
              AS(                    
    SELECT  o.OfferId                            
          , o.FullPart  COLLATE DATABASE_DEFAULT as FullPart                               
          , o.Part   COLLATE DATABASE_DEFAULT as Part                           
          , o.ManufacturerNo                            
          , o.DateCode COLLATE DATABASE_DEFAULT as DateCode                            
          , o.ProductNo                            
          , o.PackageNo                            
          , o.Quantity                            
          ,case when o.ClientNo=114 then 0 else o.Price end AS Price --o.Price                            
          , o.OriginalEntryDate                            
          , o.Salesman                            
          , o.SupplierNo                            
          , o.CurrencyNo                            
          , o.ROHS                            
          , o.UpdatedBy                            
          , o.DLUP                            
          , o.OfferStatusNo                            
		  , ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate                            
          , o.OfferStatusChangeLoginNo                            
          , m.ManufacturerCode                            
          , p.ProductName                            
          , c.CurrencyCode                            
          , c.CurrencyDescription                            
          , case when o.ClientNo=114 then @HUBName else  ISNULL(s.CompanyName, o.SupplierName) end AS SupplierName                            
          , ISNULL(m.ManufacturerName, o.ManufacturerName) AS ManufacturerName                            
          , s.EMail AS SupplierEmail                            
          , l.EmployeeName AS SalesmanName                            
          , l2.EmployeeName AS OfferStatusChangeEmployeeName                            
          , g.PackageName                            
		  , o.Notes  COLLATE DATABASE_DEFAULT  as Notes                                   
          , o.ClientNo                            
          , cl.ClientId                          
          , cl.ClientName                            
          , cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                            
            --[001] code start                            
          , isnull(cotype.Name,'') as SupplierType                            
          --[001] code end          
          , cl.ClientCode     
          ,ishub=0                           
    FROM    [BorisGlobalTraderArchive].dbo.tbOffer_arc o                            
    LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId 
 LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId                            
 LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId                            
    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId                            
    LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId               
    LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId                            
    LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId                            
    JOIN    tbClient cl ON o.ClientNo = cl.ClientId                           
      --[001] code start                            
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId                            
    --[001] code end                             
    WHERE   ((o.ClientNo = @ClientId)                        
             OR (o.ClientNo <> @ClientId                        
                -- AND cl.OwnDataVisibleToOthers = 1))        
				AND (case when o.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
     AND o.FullPart LIKE @PartSearch                
     --AND (dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)                              
  -- ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                            
              union all      
                    
           select OfferId                            
          , FullPart                            
          , Part                            
          , ManufacturerNo                            
          , DateCode                            
          , ProductNo                            
          , PackageNo                            
          , Quantity                            
			,case when ClientNo=114 then 0 else Price end AS Price --o.Price  --Price                            
          , OriginalEntryDate                            
          , Salesman                            
          , SupplierNo                            
          , CurrencyNo                            
          , ROHS                            
          , UpdatedBy                            
          , DLUP                            
          , OfferStatusNo                            
		  , OfferStatusChangeDate                            
          , OfferStatusChangeLoginNo                            
          , ManufacturerCode                            
          , ProductName                            
          , CurrencyCode                            
          , CurrencyDescription                            
          , case when ClientNo=114 then @HUBName else  SupplierName end  SupplierName                            
          , ManufacturerName                            
          , SupplierEmail                            
          , SalesmanName                            
          , OfferStatusChangeEmployeeName                            
          , PackageName                            
          , Replace(Notes,CHAR(10),' ') as Notes                            
          , ClientNo                            
          , ClientId                          
          , ClientName                            
          ,  ClientDataVisibleToOthers                            
            --[001] code start                            
          ,  SupplierType                            
          --[001] code end          
          , ClientCode     
          ,ishub from [vwSourcingData]         
                    
               where          
  ((ClientNo = @ClientId)                          
             OR (ClientNo <> @ClientId                          
       --   AND ClientDataVisibleToOthers = 1))          
	   AND (case when ClientNo=114 then cast(1 as bit) else  ClientDataVisibleToOthers end) = 1 )) 
               
     AND FullPart LIKE @PartSearch                      
    -- AND (dbo.ufn_get_date_from_datetime(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) between @FROMDATEVW   AND  @ENDDATEVW)                    
  -- ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                   
      --SELECT THE OUT DATE       
        )      
      --select * from cteSearch  ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC   
	   SELECT *, dbo.ufn_GetSupplierMessage(SupplierNo) as  SupplierMessage 
	   FROM (SELECT 
	       OfferId                            
          , FullPart                            
          , Part        
          , ManufacturerNo                            
          , DateCode                            
          , ProductNo                            
          , PackageNo                            
          , Quantity                            
          , case when ClientNo=114 then 0 else Price end AS Price --o.Price  --Price                            
          , OriginalEntryDate                            
          , Salesman                            
          , SupplierNo                            
          , CurrencyNo                            
          , ROHS                            
          , UpdatedBy                            
          , DLUP                            
          , OfferStatusNo                            
		  , OfferStatusChangeDate                            
          , OfferStatusChangeLoginNo                            
          , ManufacturerCode                            
          , ProductName                            
          , CurrencyCode                            
          , CurrencyDescription                            
          , case when ClientNo=114 then @HUBName else  SupplierName end  SupplierName                            
          , ManufacturerName                            
          , SupplierEmail                            
          , SalesmanName                            
          , OfferStatusChangeEmployeeName                            
          , PackageName                            
          , Replace(Notes,CHAR(10),' ') as Notes                            
          , ClientNo                            
          , ClientId                          
          , ClientName                            
          ,  ClientDataVisibleToOthers                            
            --[001] code start                            
          ,  SupplierType                            
          --[001] code end          
          , ClientCode     
          ,ishub 
		  ,ROW_NUMBER() OVER (ORDER BY
                              case WHEN @OrderBy = 1
                              AND @SortDir = 2 THEN Quantity
                              END DESC  
							  ,case WHEN @OrderBy = 1 THEN Quantity
                              END
							  ,case WHEN @OrderBy = 6
                              AND @SortDir = 2 THEN ISNULL(OfferStatusChangeDate, OriginalEntryDate)
                              END DESC  
							  ,case WHEN @OrderBy = 6 THEN ISNULL(OfferStatusChangeDate,OriginalEntryDate)
                              END  
							   ,case WHEN @OrderBy = 7
                              AND @SortDir = 2 THEN Price
                              END DESC  
							  ,case WHEN @OrderBy = 7 THEN Price
                              END              
							 ) As RowNum          
		  FROM   cteSearch ) AS B
	  ORDER BY RowNum  
END
ELSE
BEGIN
    ;                                              
    WITH    cteSearch                                              
              AS(                    
    SELECT  o.OfferId   
          , o.FullPart  COLLATE DATABASE_DEFAULT as FullPart                               
          , o.Part   COLLATE DATABASE_DEFAULT as Part                           
 , o.ManufacturerNo         
          , o.DateCode COLLATE DATABASE_DEFAULT as DateCode                            
          , o.ProductNo                            
          , o.PackageNo                            
          , o.Quantity                            
            ,case when o.ClientNo=114 then 0 else o.Price end AS Price --o.Price                             
          , o.OriginalEntryDate                            
          , o.Salesman                            
          , o.SupplierNo                            
          , o.CurrencyNo                            
          , o.ROHS                            
          , o.UpdatedBy                            
          , o.DLUP                            
          , o.OfferStatusNo                            
		  , ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) AS OfferStatusChangeDate                            
          , o.OfferStatusChangeLoginNo                            
          , m.ManufacturerCode                            
          , p.ProductName                            
          , c.CurrencyCode                            
          , c.CurrencyDescription                            
          , case when o.ClientNo=114 then @HUBName else  ISNULL(s.CompanyName, o.SupplierName) end  AS SupplierName                            
          , ISNULL(m.ManufacturerName, o.ManufacturerName) AS ManufacturerName                            
          , s.EMail AS SupplierEmail                            
          , l.EmployeeName AS SalesmanName                            
          , l2.EmployeeName AS OfferStatusChangeEmployeeName                            
          , g.PackageName                            
		  , o.Notes  COLLATE DATABASE_DEFAULT  as Notes                            
          , o.ClientNo                            
          , cl.ClientId                          
          , cl.ClientName                            
          , cl.OwnDataVisibleToOthers AS ClientDataVisibleToOthers                            
            --[001] code start                            
          , isnull(cotype.Name,'') as SupplierType                            
          --[001] code end          
          , cl.ClientCode     
          ,ishub=0                           
    FROM    [BorisGlobalTraderImports].dbo.tbOffer o                       
    LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId                            
    LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId                            
    LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId                            
    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId                            
    LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId                            
    LEFT JOIN dbo.tbLogin l2 ON o.OfferStatusChangeLoginNo = l2.LoginId                            
    LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId                            
    JOIN    tbClient cl ON o.ClientNo = cl.ClientId                           
      --[001] code start                            
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId                            
    --[001] code end                             
    WHERE   ((o.ClientNo = @ClientId)                        
             OR (o.ClientNo <> @ClientId                        
                 --AND cl.OwnDataVisibleToOthers = 1))        
				 	AND (case when o.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 )) 
     AND o.FullPart LIKE @PartSearch                
     AND (dbo.ufn_get_date_from_datetime(ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)                              
  -- ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                            
              union all   
             
   select OfferId   
          , FullPart                            
          , Part                            
          , ManufacturerNo                            
          , DateCode                            
          , ProductNo                            
          , PackageNo                            
          , Quantity                            
,  case when ClientNo=114 then 0 else Price end AS Price --o.Price  Price                            
          , OriginalEntryDate                            
          , Salesman                            
          , SupplierNo                            
          , CurrencyNo                            
          , ROHS                            
          , UpdatedBy                            
          , DLUP                            
          , OfferStatusNo                            
		  , OfferStatusChangeDate                            
          , OfferStatusChangeLoginNo                            
          , ManufacturerCode        
          , ProductName                            
          , CurrencyCode                            
          , CurrencyDescription                            
          , case when ClientNo=114 then @HUBName else  SupplierName end  as SupplierName                            
          , ManufacturerName                            
          , SupplierEmail                            
          , SalesmanName                            
          , OfferStatusChangeEmployeeName                            
          , PackageName                            
          , Replace(Notes,CHAR(10),' ') as Notes                            
          , ClientNo                            
          , ClientId                          
          , ClientName                            
          ,  ClientDataVisibleToOthers                            
            --[001] code start                            
          ,  SupplierType                            
          --[001] code end          
          , ClientCode     
          ,ishub from [vwSourcingData]         
                    
               where          
  ((ClientNo = @ClientId)                          
             OR (ClientNo <> @ClientId                          
         -- AND ClientDataVisibleToOthers = 1))          
		 	AND (case when ClientNo=114 then cast(1 as bit) else  ClientDataVisibleToOthers end) = 1 )) 
               
     AND FullPart LIKE @PartSearch                      
     AND (dbo.ufn_get_date_from_datetime(ISNULL(OfferStatusChangeDate, OriginalEntryDate)) between @FROMDATEVW   AND  @ENDDATEVW)                    
  -- ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC                   
      --SELECT THE OUT DATE       
        )      
      --select * from cteSearch  ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC   
	   SELECT *,dbo.ufn_GetSupplierMessage(SupplierNo) as  SupplierMessage FROM (SELECT 
	       OfferId                            
          , FullPart                            
          , Part                            
          , ManufacturerNo                            
          , DateCode                            
          , ProductNo                            
          , PackageNo                            
          , Quantity                            
           ,case when ClientNo=114 then 0 else Price end AS Price --o.Price                              
          , OriginalEntryDate                            
          , Salesman                            
          , SupplierNo                            
          , CurrencyNo                            
          , ROHS                            
          , UpdatedBy                            
          , DLUP                            
          , OfferStatusNo                            
		  , OfferStatusChangeDate                           
          , OfferStatusChangeLoginNo                 
 , ManufacturerCode                            
          , ProductName                            
          , CurrencyCode                            
          , CurrencyDescription                            
          , case when ClientNo=114 then @HUBName else  SupplierName end as SupplierName                            
          , ManufacturerName                            
          , SupplierEmail                            
          , SalesmanName                            
          , OfferStatusChangeEmployeeName                            
          , PackageName                            
          , Replace(Notes,CHAR(10),' ') as Notes                            
          , ClientNo                            
          , ClientId                          
          , ClientName                            
          ,  ClientDataVisibleToOthers                            
            --[001] code start                            
          ,  SupplierType                            
          --[001] code end          
          , ClientCode     
          ,ishub 
		  ,ROW_NUMBER() OVER (ORDER BY
                              case WHEN @OrderBy = 1
                              AND @SortDir = 2 THEN Quantity
                END DESC  
							  ,case WHEN @OrderBy = 1 THEN Quantity
                              END
							  ,case WHEN @OrderBy = 6
                              AND @SortDir = 2 THEN ISNULL(OfferStatusChangeDate, OriginalEntryDate)
                              END DESC  
							  ,case WHEN @OrderBy = 6 THEN ISNULL(OfferStatusChangeDate,OriginalEntryDate)
                              END  
							   ,case WHEN @OrderBy = 7
                              AND @SortDir = 2 THEN Price
                              END DESC  
							  ,case WHEN @OrderBy = 7 THEN Price
                              END              
							 ) As RowNum          
		  FROM   cteSearch ) AS B
	  ORDER BY RowNum 
END               
    SELECT @OutPutDate AS OutPutDate 
END



