using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class SellCurrency : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

		public override void ProcessRequest(HttpContext context) {
			SetDropDownType("SalesOrderStatus");
			base.ProcessRequest(context);
		}

		protected override void GetData() {
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");

            string strOptions = CacheManager.SerializeOptions(new object[] { intGlobalLoginClientNo.HasValue ? intGlobalLoginClientNo.Value : SessionManager.ClientID });
			string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strOptions);
			if (string.IsNullOrEmpty(strCachedData)) {
				JsonObject jsn = new JsonObject();
				JsonObject jsnList = new JsonObject(true);
                List<BLL.Currency> lst = BLL.Currency.DropDownSellForClient((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].CurrencyId);
					jsnItem.AddVariable("Name", String.Format("{1} - {0}", lst[i].CurrencyDescription, lst[i].CurrencyCode));
					jsnItem.AddVariable("Code", lst[i].CurrencyCode);
					jsnList.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				lst.Clear(); lst = null;
				jsn.AddVariable("Currencies", jsnList);
				jsnList.Dispose(); jsnList = null;
				CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.ThreeHours);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} else {
				_context.Response.Write(strCachedData);
			}
			strCachedData = null;
		}
	}
}
