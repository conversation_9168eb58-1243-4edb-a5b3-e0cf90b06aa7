﻿
GO

/****** Object:  StoredProcedure [dbo].[USP_GetSourcingData]    Script Date: 2/5/2025 5:25:40 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[USP_GetSourcingData]        
    @MfrId INT = null                         
,        
    @ProductTypeId INT = Null                        
,        
    @Franchised int =null                          
,        
    @IndustryType nvarchar(max) = null                           
,        
    @SupplierType nvarchar(max) = null                                 
,        
    @Orderdate int = null                        
,        
    @ProductId int=Null                        
,        
   @GlobalProductId int = null        
,        
    @ClientId int = null        
        
AS        
Begin        
        
    declare @ConvertedOrderDate datetime;        
        
    if(@ProductTypeId =0)                                     
        set @ProductTypeId = null        
    if(@ProductId =0)                                     
        set @ProductId = null        
    if(@MfrId =0)                                     
        set @MfrId = null        
    if @Franchised is null                                 
        set @Franchised = 0        
    if(@IndustryType = '0')                                 
        set @IndustryType = null;        
    if (@SupplierType = '0')                                 
        set @SupplierType = null        
    if  @Orderdate = 0 or @Orderdate is null                        
        set @ConvertedOrderDate = DATEADD(MONTH,-24,getdate())        
    if @GlobalProductId = 0        
        BEGIN        
            set @GlobalProductId = null;        
        END        
 if @Orderdate is null or @Orderdate = 0      
  begin      
  set @orderdate = 24;      
  end      
    else if @Orderdate  is not null or @Orderdate != 0                           
  begin      
        select @Orderdate =                            
        case when @orderdate = 1 then 1                            
        when @Orderdate = 2 then 3                            
        when @Orderdate = 3 then  6                             
        when @Orderdate = 4 then 12                           
        else 24                           
        end      
 end      
    set @ConvertedOrderDate = DATEADD(MONTH,-@Orderdate,GETDATE())        
        
    /* ================================================================                     
  [Start] select statement using tbPurchaeOrderLine and tbPurchaseOrder                      
 ====================================================================*/        
    SELECT DISTINCT so.DateOrdered           
   , FORMAT (max(So.DateOrdered), 'dd-MM-yyyy') AS LastOrderDate                   
   , so.buyer AS SalesmanId, lo.EmployeeName AS SalesmanName                     
   , replace(co.CompanyName,'"','') AS supplier, co.CompanyId as supplierId           
   , replace(ct.Name,'"','') as [Type]        
   , mf.ManufacturerId AS ManufacturerId        
   , replace(mf.ManufacturerName,'"','') AS ManufacturerName                     
   , pr.ProductId, pr.ProductName AS Product                 
   , so.PurchaseOrderNumber as 'PurchaseOrder'               
   , so.PurchaseOrderId as 'PurchaseOrderId'           
   --, it.IndustryTypeId AS IndustryTypeId, it.Name AS IndustryType,                 
   , 0 AS IndustryTypeId        
   , dbo.ufn_get_Company_IndustryType_List(co.CompanyId) AS IndustryType           
   , co.URL AS WebSite        
   , co.EMail AS Email            
   , CU.CurrencyCode             
   , (           
    select count(1)        
        from vwSupplierRMA     
  where CompanyNo= so.CompanyNo     
  and SupplierRMADate>=DaTeadd(Month,-12,GETDATE())    
  ) as Last12MonthRMA     
     , dbo.ufn_calculate_po_amount_by_po_number(so.PurchaseOrderId) as LastOrderValue             
    , isnull(mfl.ManufacturerRating,0) as SupplierRating                   
    , isnull(mfl.SupplierRating,0) as ManufacturerRating                 
   --, (select mfl.ManufacturerRating        
   --     from tbManufacturerLink mfl        
   --     where mfl.ManufacturerNo = mf.ManufacturerId and mfl.SupplierCompanyNo = co.CompanyId     
   --  ) as SupplierRating                 
   --, (select mfl.SupplierRating        
   --     from tbManufacturerLink mfl        
   --     where mfl.ManufacturerNo = mf.ManufacturerId and mfl.SupplierCompanyNo = co.CompanyId           
   --  ) as ManufacturerRating             
   , (           
     select count(distinct(PurchaseOrderNo))        
        from tbPurchaseOrderLine  pol        
            left join tbPurchaseOrder po        
            on pol.PurchaseOrderNo = po.PurchaseOrderId        
        where po.CompanyNo = so.CompanyNo        
            AND pol.ManufacturerNo = mf.ManufacturerId        
            AND pol.ProductNo = pr.ProductId        
            and po.ClientNo = @ClientId        
            AND po.DateOrdered >= DATEADD(YEAR, -1, convert(date,getdate()))    
   --AND pol.Closed in (0,1)  
    ) as OrderCountInLastOneYear        
        
        
    FROM dbo.tbPurchaseOrderLine sol with (nolock)        
        JOIN dbo.tbPurchaseOrder so with (nolock) ON sol.PurchaseOrderNo = so.PurchaseOrderId        
        JOIN dbo.tbManufacturer mf with (nolock) ON sol.ManufacturerNo = mf.ManufacturerId        
        LEFT JOIN dbo.tbCurrency cu with (nolock) ON so.CurrencyNo = cu.CurrencyId        
        JOIN dbo.tbCompany co with (nolock) ON so.CompanyNo = co.CompanyId        
        JOIN dbo.tbProduct pr with (nolock) ON ProductId = sol.ProductNo        
        JOIN dbo.tbLogin lo with (nolock) ON lo.LoginId = so.Buyer        
        LEFT JOIN dbo.tbCompanyType cotype with (nolock) ON co.TypeNo=cotype.CompanyTypeId        
        JOIN tbGlobalProduct gp  with (nolock) on gp.GlobalProductId = pr.GlobalProductNo        
        LEFT JOIN tbGlobalProductName gpn  with (nolock) on gpn.GlobalProductNameId=gp.GlobalProductNameNo        
        LEFT JOIN tbProductCategory pc  with (nolock) on pc.ProductCategoryId = gpn.ProductCategoryNo        
        JOIN tbCompanyType ct on ct.CompanyTypeId = co.TypeNo        
        left join tbCompanyIndustryType cit on cit.CompanyNo=co.CompanyId        
        LEFT join tbIndustryType it on it.IndustryTypeId=cit.IndustryTypeNo        
        JOIN dbo.tbClient cl with (nolock) ON cl.ClientId = so.ClientNo        
  LEFT JOIN tbManufacturerLink mfl on mfl.SupplierCompanyNo = co.companyId      
  and mfl.ManufacturerNo = mf.ManufacturerId      
        
    WHERE                                    
   ((@ClientId IS NULL) OR(so.ClientNo = @ClientId))        
        --OR (so.ClientNo <> @ClientId AND cl.OwnDataVisibleToOthers = 1))                 
        and ((@ProductTypeId IS NULL) OR (NOT @ProductTypeId IS NULL AND pc.productCategoryId = @ProductTypeId)) /*Added for [001] */        
        --AND ((@ProductId IS NULL) OR (NOT @ProductId IS NULL AND pc.productCategoryId = @ProductId)) /commented this line as it seems invalid condition/         
        AND ((@ProductId IS NULL) OR (NOT @ProductId IS NULL AND pr.ProductId = @ProductId)) /*Added for [001] */        
        and((@GlobalProductId is null) or (Not @GlobalProductId is null and gp.GlobalProductNameNo = @GlobalProductId))        
        AND ((@MfrId IS NULL) OR (NOT @MfrId IS NULL AND mf.ManufacturerId = @MfrId)) /*Added for [001] */        
        AND ((@Franchised = 0 and cotype.name =cotype.name) or        
        ( @Franchised = 1 and cotype.name like '%Franchise%') OR (@Franchised = 2 and cotype.name not like '%Franchise%'))        
        AND ((@IndustryType IS NULL) OR (NOT @IndustryType IS NULL AND it.IndustryTypeId  in (select *        
        from SplitString(@IndustryType,','))))        
        AND ((@SupplierType IS NULL) OR (NOT @SupplierType IS NULL AND co.CompanyId  in (select *        
        from SplitString(@SupplierType,','))))        
        and dbo.ufn_get_date_from_datetime(so.DateOrdered) between                      
  dbo.ufn_get_date_from_datetime(@ConvertedOrderDate) and dbo.ufn_get_date_from_datetime(getdate())        
        
    group by             
 so.DateOrdered           
  , so.buyer , lo.EmployeeName                   
 , co.CompanyName , co.CompanyId ,                     
        ct.Name , mf.ManufacturerId , mf.ManufacturerName                    
  , pr.ProductId, pr.ProductName                  
  --, it.IndustryTypeId , it.Name                  
  ,dbo.ufn_get_Company_IndustryType_List(co.CompanyId)                   , co.URL , co.EMail                    
 ,  CU.CurrencyCode                      
 , mfl.SupplierRating                      
 , mfl.ManufacturerRating                     
 -- ,  so.PurchaseOrderId                 
    , so.CompanyNo                 
    ,mf.ManufacturerCode             
 , so.PurchaseOrderId        
  , so.PurchaseOrderNumber        
    order by 1 desc        
/* ================================================================                     
  [end] select statement using tbPurchaeOrderLine and tbPurchaseOrder                      
 ====================================================================*/        
end 
GO


