<%@ Control Language="C#" CodeBehind="CompanyTransactions.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyTransactions" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Content>
		<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
			<tr>
				<td class="desc"><%=Functions.GetGlobalResource("FormFields", "IncludeClosed")%></td>
				<td class="item"><ReboundUI:ImageCheckBox id="chkIncludeClosed" runat="server" Enabled="true" /></td>
			</tr>
		</table>
		<ReboundUI:TabStrip id="ctlTabs" runat="server">
			<TabsTemplate>
				<ReboundUI:Tab ID="ctlTabRequirements" runat="server" IsSelected="true" RelatedContentPanelID="pnlTabRequirements" />
				<ReboundUI:Tab ID="ctlTabBOMs" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabBOMs" />
				<ReboundUI:Tab ID="ctlTabQuotes" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabQuotations" />
				<ReboundUI:Tab ID="ctlTabSalesOrders" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabSalesOrders" />
				<ReboundUI:Tab ID="ctlTabInvoices" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabInvoices" />
				<ReboundUI:Tab ID="ctlTabPurchaseOrders" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabPurchaseOrders" />
				<ReboundUI:Tab ID="ctlTabCustomerRMAs" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabCustomerRMAs" />
				<ReboundUI:Tab ID="ctlTabSupplierRMAs" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabSupplierRMAs" />
				<ReboundUI:Tab ID="ctlTabCreditNotes" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabCreditNotes" />
				<ReboundUI:Tab ID="ctlTabDebitNotes" runat="server" IsSelected="false" RelatedContentPanelID="pnlTabDebitNotes" />
			</TabsTemplate>
			<TabsContent>
				<asp:Panel ID="pnlTabRequirements" runat="server"><ReboundUI:FlexiDataTable ID="tblRequirements" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabBOMs" runat="server"><ReboundUI:FlexiDataTable ID="tblBOMs" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabQuotations" runat="server"><ReboundUI:FlexiDataTable ID="tblQuotes" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabSalesOrders" runat="server"><ReboundUI:FlexiDataTable ID="tblSOs" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabInvoices" runat="server"><ReboundUI:FlexiDataTable ID="tblInvoices" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabPurchaseOrders" runat="server"><ReboundUI:FlexiDataTable ID="tblPOs" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabCustomerRMAs" runat="server"><ReboundUI:FlexiDataTable ID="tblCRMAs" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabSupplierRMAs" runat="server"><ReboundUI:FlexiDataTable ID="tblSRMAs" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabCreditNotes" runat="server"><ReboundUI:FlexiDataTable ID="tblCreditNotes" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
				<asp:Panel ID="pnlTabDebitNotes" runat="server"><ReboundUI:FlexiDataTable ID="tblDebitNotes" runat="server" HasMoreInfoBox="false" PanelHeight="150" /></asp:Panel>
			</TabsContent>
		</ReboundUI:TabStrip>
	</Content>
</ReboundUI_Nugget:DesignBase>
