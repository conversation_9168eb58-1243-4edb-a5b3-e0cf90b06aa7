///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           26/07/2012   Set the application to only accept qtys greater than 0:- ESMS Task-103
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.initializeBase(this, [element]);

    this._intSOLineID = -1;
    this._intGIID = -1;
    this._intSerialID = -1;
    this._serialDetail = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.prototype = {
    get_intSerialID: function () { return this._intSerialID; }, set_intSerialID: function (v) { if (this._intSerialID !== v) this._intSerialID = v; },
    get_intGIID: function () { return this._intGIID; }, set_intGIID: function (v) { if (this._intGIID !== v) this._intGIID = v; },
    get_intSOLineID: function () { return this._intSOLineID; }, set_intSOLineID: function (v) { if (this._intSOLineID !== v) this._intSOLineID = v; },
    get_btnAdd: function () { return this._btnAdd; }, set_btnAdd: function (v) { if (this._btnAdd !== v) this._btnAdd = v; },
    get_btnRefresh: function () { return this._btnRefresh; }, set_btnRefresh: function (v) { if (this._btnRefresh !== v) this._btnRefresh = v; },
    //get_btnUpdate: function () { return this._btnUpdate; }, set_btnUpdate: function (v) { if (this._btnUpdate !== v) this._btnUpdate = v; },
    get_lblDuplicateError: function () { return this._lblDuplicateError; }, set_lblDuplicateError: function (v) { if (this._lblDuplicateError !== v) this._lblDuplicateError = v; },
    get_tblSerialNodetails: function () { return this._tblSerialNodetails; }, set_tblSerialNodetails: function (v) { if (this._tblSerialNodetails !== v) this._tblSerialNodetails = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addSave(Function.createDelegate(this, this.saveSerialClicked));

        //this._tblSerialNodetails.addSelectedIndexChanged(Function.createDelegate(this, this.getSerialDetail));
        //$R_FN.showElement(this._btnUpdate, false);
        $R_FN.showElement(this._btnAdd, true);
        $R_FN.showElement(this._btnRefresh, true);
        $R_FN.showElement(this._lblDuplicateError, false);       
        this.showField("ctlSerialNoDetail", false);
        this.getFieldDropDownData("ctlSubGroup");
        this.setFieldValue("ctlSerailNo", "");
        if (this._btnAdd) $addHandler(this._btnAdd, "click", Function.createDelegate(this, this.saveClicked));
        //if (this._btnUpdate) $addHandler(this._btnUpdate, "click", Function.createDelegate(this, this.updateClicked));
        if (this._btnRefresh) $addHandler(this._btnRefresh, "click", Function.createDelegate(this, this.refreshClicked));
        this.LoadSerailNoGrid();
    },

    formShown: function () {
        this.showField("ctlSerialNoDetail", false);        
        this.setFieldValue("ctlSerailNo", 0, null, "");
        $R_FN.showElement(this._lblDuplicateError, false);       
        $R_FN.showElement(this._btnUpdate, false);
        $R_FN.showElement(this._btnAdd, true);
        $R_FN.showElement(this._btnRefresh, true);
        this._intSOLineID = this._intSOLineID;
        this.LoadSerailNoGrid();
        $find(this.getField("ctlSubGroup").ControlID).addChanged(Function.createDelegate(this, this.setGroup));
        this.getFieldDropDownData("ctlSubGroup");
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intSerialID = -1;
        this._btnAdd = null;
        this._btnRefresh = null;
        this._btnUpdate = null;
        this._intGIID = -1;      
        this._lblDuplicateError = null;       
        this._tblSerialNodetails = null;
        this._intSOLineID = null;
        this._serialDetail = -1;
        Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.callBaseMethod(this, "dispose");
       
    },
  
    setGroup: function () {
        $find(this.getField("ctlSerailNo").ControlID)._aut._txtGroup = this.getFieldValue("ctlSubGroup");
        this.setFieldValue("ctlSerailNo", 0, null, "");
    },

    saveSerialClicked: function () {
       
        if (this._serialDetail <= 0) {
            this._strErrorMessage = 'No Record Exist';
            this.showError(true, this._strErrorMessage);
            return;

        }
        this.setFieldValue("ctlSubGroup", "");       
        this.setFieldValue("ctlSerailNo", 0, null, "");
        this.onSaveComplete();      
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/SOShippingLines");
        obj.set_DataObject("SOShippingLines");
        obj.set_DataAction("AddAllSerialNo");
        obj.addDataOK(Function.createDelegate(this, this.saveSerialComplete));
        obj.addError(Function.createDelegate(this, this.saveSerialError));
        obj.addTimeout(Function.createDelegate(this, this.saveSerialError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },
        
    saveSerialError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveSerialComplete: function (args) {
        if (args._result.Result == true) {
            this.setFieldValue("ctlSubGroup", "");
            this.setFieldValue("ctlSerailNo", 0, null, "");
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/SOShippingLines");
        obj.set_DataObject("SOShippingLines");
        obj.set_DataAction("AddSerialNo");
        obj.addParameter("SubGroup", this.getFieldValue("ctlSubGroup"));
        obj.addParameter("SerialNo", this.getFieldComboText("ctlSerailNo"));
        obj.addParameter("SOLineNo", this._intSOLineID);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

        //this.LoadSerailNoGrid();
    },
    saveError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },



    saveComplete: function (args) {
        if (args._result.Result == true) {          
            this.showField("ctlSerialNoDetail", true);         
            this.setFieldValue("ctlSerailNo", 0, null, "");
            this._strErrorMessage = '';
            this.showError(false, this._strErrorMessage);
            $R_FN.showElement(this._lblDuplicateError, false);
            this.LoadSerailNoGrid();
            return;
        }
        if (args._result.Result == false && args._result.ValidateMessage != null) {            
            $R_FN.showElement(this._lblDuplicateError, true);
            return;
        }
        else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    //updateClicked: function () {
    //    if (!this.validateForm()) return;       
    //    var obj = new Rebound.GlobalTrader.Site.Data();
    //    obj.set_PathToData("controls/Nuggets/GILines");
    //    obj.set_DataObject("GILines");
    //    obj.set_DataAction("UpdateSerialNo");
    //    obj.addParameter("SerialID", this._intSerialID);
    //    obj.addParameter("SubGroup", this.getFieldValue("ctlSubGroup"));
    //    obj.addParameter("SerialNo", this.getFieldValue("ctlSerailNo"));
    //    obj.addParameter("GoodsInId", this._intGIID);
    //    obj.addDataOK(Function.createDelegate(this, this.updateComplete));
    //    obj.addError(Function.createDelegate(this, this.updateError));
    //    obj.addTimeout(Function.createDelegate(this, this.updateError));
    //    $R_DQ.addToQueue(obj);
    //    $R_DQ.processQueue();
    //    obj = null;

    //    this.LoadSerailNoGrid();
    //},
    //updateError: function (args) {
    //    this._strErrorMessage = args._errorMessage;
    //    this.onSaveError();
    //},

    //updateComplete: function (args) {
    //    if (args._result.Result == true) {        
    //        this.showField("ctlSerialNoDetail", true);
    //        this.setFieldValue("ctlSubGroup", "");
    //        this.setFieldValue("ctlSerailNo", "");
    //        $R_FN.showElement(this._lblDuplicateError, false);
    //        return;
    //    }
    //    if (args._result.Result == false && args._result.ValidateMessage != null) {       
    //        $R_FN.showElement(this._lblDuplicateError, true);
    //        return;
    //    }
    //    else {
    //        this._strErrorMessage = args._errorMessage;
    //        this.onSaveError();
    //    }
    //},

    LoadSerailNoGrid: function () {

        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/SOShippingLines");
        obj.set_DataObject("SOShippingLines");
        obj.set_DataAction("GetDataGrid");
        obj.addParameter("SOLineNo", this._intSOLineID);
        obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
        obj.addError(Function.createDelegate(this, this.getDataGridError));
        obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },

    getDataGridError: function (args) {

    },
    getDataGrid: function (args) {

        res = args._result;
        this._serialDetail = res.SerialNoDetails.length;

        this.showField("ctlSerialNoDetail", res.SerialNoDetails.length > 0);
        this._tblSerialNodetails.clearTable();
        for (var i = 0; i < res.SerialNoDetails.length; i++) {
            var row = res.SerialNoDetails[i];
            var aryData = [              
              row.SubGroup,
               row.SerialNo 

            ];
            var objExtraData = {
                SerialNoId: row.SerialNoId,
                SubGroup: row.SubGroup,
                SerialNo: row.SerialNo 
            };
            this._tblSerialNodetails.addRow(aryData, row.SerialNoId, false, objExtraData);
            aryData = null;
            row = null;
        }

        this._tblSerialNodetails.resizeColumns();
    },

    getSerialDetail: function () {
        $R_FN.showElement(this._lblDuplicateError, false);
        this._strErrorMessage = '';
        this.showError(false, this._strErrorMessage);
        //$R_FN.showElement(this._btnUpdate, true);
        $R_FN.showElement(this._btnAdd, true);
        this.setFieldValue("ctlSubGroup", "");
        this.setFieldValue("ctlSerailNo", 0, null, "");
        this.getValuesBySerial();
     
    },

    getValuesBySerial: function () {
        this._intSerialID = -1;
        var obj = this._tblSerialNodetails.getSelectedExtraData(); if (!obj) return;
        this.setFieldValue("ctlSubGroup", obj.SubGroup);
        this.setFieldValue("ctlSerailNo", obj.SerialNo);
        this._intSerialID = obj.SerialNoId;
      
    },
    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    },

    refreshClicked: function () {
        this.LoadSerailNoGrid();
        this.getFieldDropDownData("ctlSubGroup");
        this.setFieldValue("ctlSubGroup", "");
        this.setFieldValue("ctlSerailNo", 0, null, "");
        this._strErrorMessage = '';
        this.showError(false, this._strErrorMessage);
        $R_FN.showElement(this._lblGroupError, false);

    }
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAReceiveLines_SerialNo", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
