<%@ Control Language="C#" CodeBehind="MyRecentActivity.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyRecentActivity" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="allwhite">
	<Content>
		<div class="homepageNugget">
		    <asp:Panel ID="pnlCR" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "CustomerRequirements")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblCR" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlGI" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "GoodsIn")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblGI" runat="server" AllowSelection="false" />
		    </asp:Panel>
		    <asp:Panel ID="pnlQU" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "Quotes")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblQU" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlPO" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "PurchaseOrders")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblPO" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlSO" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "SalesOrders")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblSO" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlCRMA" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "CustomerRMAs")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblCRMA" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlSRMA" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "SupplierRMAs")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblSRMA" runat="server" AllowSelection="false" />
		    </asp:Panel>		
 		    <asp:Panel ID="pnlCredit" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "Credits")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblCredit" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlDebit" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "Debits")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblDebit" runat="server" AllowSelection="false" />
		    </asp:Panel>		
		    <asp:Panel ID="pnlInv" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "Invoices")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblInv" runat="server" AllowSelection="false" />
		    </asp:Panel>		
             <asp:Panel ID="pnlOsPoApproval" runat="server" CssClass="homepageNugget">
	            <h5><%=Functions.GetGlobalResource("misc", "MySupplierApprovals")%></h5>
			    <ReboundUI:SimpleDataTable ID="tblOsPoApproval" runat="server" AllowSelection="false" />
		    </asp:Panel>
       </div>
	</Content>
</ReboundUI_Nugget:DesignBase>
