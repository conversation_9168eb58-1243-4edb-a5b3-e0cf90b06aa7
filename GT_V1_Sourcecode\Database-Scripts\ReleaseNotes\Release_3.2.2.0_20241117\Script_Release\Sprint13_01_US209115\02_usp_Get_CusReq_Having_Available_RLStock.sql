﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-209115]			Trung Pham			18-Oct-2024		CREATE          Get Customer Requirement using RL stock
===========================================================================================
*/
CREATE OR ALTER PROCEDURE usp_Get_CusReq_Having_Available_RLStock
	@BOMId INT
AS
BEGIN
	SELECT DISTINCT cr.BOMNo,
		c.Company<PERSON>,
		cr.Part, 
		mfr.ManufacturerName, 
		p.ProductName, 
		pack.PackageName, 
		cr.DateCode, 
		cr.Quantity, 
		cr.Price, 
		w.ClientNo, 
		bom.BOMCode, 
		cr.DLUP,
		cr.CurrencyNo, 
		curs.CurrencyCode, 
		cur.GlobalCurrencyNo AS ClientGlobalCurrencyNo,
		cur.CurrencyCode AS ClientCurrencyCode, 
		curs.GlobalCurrencyNo AS ReqGlobalCurrencyNo, 
		bom.ClientCurrencyNo
	FROM tbCustomerRequirement cr
	JOIN tbBOM bom ON bom.BOMId = cr.BOMNo
	JOIN tbCompany c ON c.CompanyId = bom.CompanyNo
	JOIN tbManufacturer mfr ON mfr.ManufacturerId = cr.ManufacturerNo
	JOIN tbCurrency cur ON cur.CurrencyId = bom.ClientCurrencyNo
	JOIN tbCurrency curs ON curs.CurrencyId = cr.CurrencyNo
	LEFT JOIN vwStock s ON s.Part = cr.Part AND s.QuantityAvailable > 0
	LEFT JOIN tbwarehouse w WITH (NOLOCK) ON w.WarehouseId = s.WarehouseNo
	LEFT JOIN tbProduct p ON p.ProductId = cr.ProductNo
	LEFT JOIN tbPackage pack ON pack.PackageId = cr.PackageNo
	JOIN tbLot l ON l.LotId = s.LotNo AND SUBSTRING(l.LotName, 1, 2) = 'ex'
	WHERE cr.BOMNo = @BOMId 
	AND (bom.Status = 3 OR bom.Status = 2)
	AND DATEDIFF(MONTH, cr.ReceivedDate, GETDATE()) <= 3
	ORDER BY cr.DLUP
END
GO
