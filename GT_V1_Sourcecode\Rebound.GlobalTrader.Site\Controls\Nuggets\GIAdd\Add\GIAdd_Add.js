Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.initializeBase(this,[n]);this._intNewID=0;this._intCompanyID=0;this._intLoginID=0;this._strCompanyName=""};Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.prototype={get_ctlSelectPO:function(){return this._ctlSelectPO},set_ctlSelectPO:function(n){this._ctlSelectPO!==n&&(this._ctlSelectPO=n)},get_intLoginID:function(){return this._intLoginID},set_intLoginID:function(n){this._intLoginID!==n&&(this._intLoginID=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},initialize:function(){var n,t;Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.callBaseMethod(this,"initialize");this._ctlMail=$find(this.getField("ctlSendMailMessage").ID);this._ctlMail._ctlRelatedForm=this;this.addCancel(Function.createDelegate(this,this.cancelClicked));this.addSave(Function.createDelegate(this,this.saveClicked));this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged));this._ctlSelectPO.addItemSelected(Function.createDelegate(this,this.selectPurchaseOrder));this.addFieldCheckBoxClickEvent("ctlSendMail",Function.createDelegate(this,this.chooseIfSendMail));n=Function.createDelegate(this,this.finishedForm);$R_IBTN.addClick(this._ibtnContinue,n);$R_IBTN.addClick(this._ibtnContinue_Footer,n);t=Function.createDelegate(this,this.sendMail);$R_IBTN.addClick(this._ibtnSend,t);$R_IBTN.addClick(this._ibtnSend_Footer,t);this.formShown()},dispose:function(){this.isDisposed||(this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlSelectPO&&this._ctlSelectPO.dispose(),this._ctlMail&&this._ctlMail.dispose(),this._ctlMail=null,this._ctlSelectPO=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._intNewID=null,this._intCompanyID=null,this._intLoginID=null,this._strCompanyName=null,Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.callBaseMethod(this,"dispose"))},formShown:function(){this.resetSteps();this._intCompanyID>0&&this.doInitialCompanySearch();this.gotoStep(1)},doInitialCompanySearch:function(){this._ctlSelectPO._initialized||setTimeout(Function.createDelegate(this,this.doInitialCompanySearch),100);this._ctlSelectPO.setFieldValue("ctlCompany",this._strCompanyName);this._ctlSelectPO.getData()},selectPurchaseOrder:function(){this._intPurchaseOrderID=this._ctlSelectPO.getSelectedID();this.getPurchaseOrder();this.nextStep()},getPurchaseOrder:function(){$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/POMainInfo");n.set_DataObject("POMainInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intPurchaseOrderID);n.addDataOK(Function.createDelegate(this,this.getPurchaseOrderOK));n.addError(Function.createDelegate(this,this.getPurchaseOrderError));n.addTimeout(Function.createDelegate(this,this.getPurchaseOrderError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getPurchaseOrderOK:function(n){var t=n._result;this.setFieldsFromPurchaseOrder(t)},setFieldsFromPurchaseOrder:function(n){n&&(this.setFieldValue("ctlPurchaseOrder",n.PONumber),this.setFieldValue("ctlSupplier",n.SupplierName),this._intSupplierID=n.SupplierNo,this.getFieldDropDownData("ctlWarehouse"),this.getFieldDropDownData("ctlShipVia"),this.getFieldDropDownData("ctlCurrency"),this.getFieldDropDownData("ctlReceivedBy"),this.setFieldValue("ctlWarehouse",n.WarehouseNo),this.setFieldValue("ctlShipVia",n.ShipViaNo),this.setFieldValue("ctlCurrency",n.CurrencyNo),this.setFieldValue("ctlReceivedBy",this._intLoginID),this.setFieldValue("ctlDateReceived",$R_FN.shortDate()),this.setFieldValue("ctlReceivingNotes",n.Instructions))},getPurchaseOrderError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},cancelClicked:function(){$R_FN.navigateBack()},stepChanged:function(){var n=this._ctlMultiStep._intCurrentStep;$R_IBTN.showButton(this._ibtnSend,n==3);$R_IBTN.showButton(this._ibtnSend_Footer,n==3);$R_IBTN.enableButton(this._ibtnSave,n==2);$R_IBTN.enableButton(this._ibtnSave_Footer,n==2);$R_IBTN.showButton(this._ibtnSave,n!=3);$R_IBTN.showButton(this._ibtnSave_Footer,n!=3);$R_IBTN.showButton(this._ibtnCancel,n!=3);$R_IBTN.showButton(this._ibtnCancel_Footer,n!=3);$R_IBTN.showButton(this._ibtnContinue,n==3);$R_IBTN.showButton(this._ibtnContinue_Footer,n==3);this._ctlMultiStep.showSteps(n!=3);n==3&&(this.getMessageText(),this.setFieldValue("ctlSendMail",!1),this.showMailButtons())},saveClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GIAdd");n.set_DataObject("GIAdd");n.set_DataAction("AddNew");n.addParameter("ShipViaNo",this.getFieldValue("ctlShipVia"));n.addParameter("AirWayBill",this.getFieldValue("ctlAirWayBill"));n.addParameter("Reference",this.getFieldValue("ctlReference"));n.addParameter("CMNo",this._intSupplierID);n.addParameter("Notes",this.getFieldValue("ctlReceivingNotes"));n.addParameter("DateReceived",this.getFieldValue("ctlDateReceived"));n.addParameter("ReceivedBy",this.getFieldValue("ctlReceivedBy"));n.addParameter("PONo",this._intPurchaseOrderID);n.addParameter("WarehouseNo",this.getFieldValue("ctlWarehouse"));n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.NewID>0?(this._intNewID=n._result.NewID,this.showSaving(!1),this.showInnerContent(!0),this.nextStep()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this._ctlMultiStep._intCurrentStep==2&&(this.checkFieldEntered("ctlWarehouse")||(n=!1),this.checkFieldEntered("ctlReference")||(n=!1),this.checkFieldEntered("ctlReceivedBy")||(n=!1),this.checkFieldEntered("ctlDateReceived")||(n=!1)),this._ctlMultiStep._intCurrentStep==3&&(this._ctlMail.validateFields()||(n=!1)),n||this.showError(!0),n},showMailButtons:function(){var n=this.getFieldValue("ctlSendMail");this.showField("ctlSendMailMessage",n);$R_IBTN.showButton(this._ibtnSend,n);$R_IBTN.showButton(this._ibtnSend_Footer,n);$R_IBTN.showButton(this._ibtnContinue,!n);$R_IBTN.showButton(this._ibtnContinue_Footer,!n)},chooseIfSendMail:function(){this.showMailButtons()},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewGoodsIn(this._intNewID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject($R_RES.NewGoodsInAdded)},validateMailForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMail:function(){this.validateMailForm()&&(Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),this._intNewID,Function.createDelegate(this,this.sendMailComplete)),$R_IBTN.showButton(this._ibtnSave,!1),$R_IBTN.showButton(this._ibtnSave_Footer,!1),$R_IBTN.showButton(this._ibtnSend,!1),$R_IBTN.showButton(this._ibtnSend_Footer,!1))},sendMailComplete:function(){this.finishedForm()},finishedForm:function(){this._ctlMultiStep.showExplainLabel(!1);this._ctlMultiStep.showSteps(!1);$R_IBTN.showButton(this._ibtnSave,!1);$R_IBTN.showButton(this._ibtnSave_Footer,!1);$R_IBTN.showButton(this._ibtnSend,!1);$R_IBTN.showButton(this._ibtnSend_Footer,!1);this.showSavedOK(!0);this.onSaveComplete()}};Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);