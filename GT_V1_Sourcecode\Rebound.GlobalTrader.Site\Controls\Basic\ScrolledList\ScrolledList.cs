﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Web.UI.WebControls;
using System.ComponentModel;
using System.Web.UI.HtmlControls;
using System.Web.UI;
using System.Web;

namespace Rebound.GlobalTrader.Site.Controls {
    
    public class ScrolledList : Panel, IScriptControl, INamingContainer {

        private TableCell _td1;
        private TableCell _td2;
        private TableCell _td3;
        private TableCell _td4;
        private ScriptManager _sm;

        [DefaultValue(2)]
        public int NumberOfColumns { get; set; }
        public Unit PanelHeight { get; set; }

        protected override void OnInit(EventArgs e) {
            ((Pages.Base)Page).AddCSSFile("FlexiDataTable.css");
            ((Pages.Base)Page).AddCSSFile("ScrolledList.css");
            EnableViewState = false;
            base.OnInit(e);
        }

        protected override void CreateChildControls() {
            base.CreateChildControls();
            Tables.Base tbl = new Tables.Base();
            TableRow tr = new TableRow();
            tbl.Rows.Add(tr);
            switch (NumberOfColumns) {
                case 2: tbl.CssClass = "twoCols"; break;
                case 3: tbl.CssClass = "threeCols"; break;
                case 4: tbl.CssClass = "fourCols"; break;
            }
            _td1 = new TableCell();
            _td1.CssClass = "col1";
            _td1.ID = "td1";
            tr.Cells.Add(_td1);
            if (NumberOfColumns >= 2) {
                _td2 = new TableCell();
                _td2.CssClass = "col2";
                _td2.ID = "td2";
                tr.Cells.Add(_td2);
            }
            if (NumberOfColumns >= 3) {
                _td3 = new TableCell();
                _td3.CssClass = "col3";
                _td3.ID = "td3";
                tr.Cells.Add(_td3);
            }
            if (NumberOfColumns >= 4) {
                _td4 = new TableCell();
                _td4.ID = "td4";
                _td4.CssClass = "col4";
                tr.Cells.Add(_td4);
            }
            Controls.Add(tbl);

            if (PanelHeight != Unit.Empty) {
                CssClass = "scrolledListScroll";
                Height = PanelHeight;
            }
        }

        protected override void OnPreRender(EventArgs e) {
            if (!this.DesignMode) {
                _sm = ScriptManager.GetCurrent(Page);
                if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
                _sm.RegisterScriptControl(this);
            }
            base.OnPreRender(e);
        }

        protected override void Render(HtmlTextWriter writer) {
            if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
            base.Render(writer);
        }

        #region IScriptControl Members

        protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.ScrolledList.ScrolledList", true) };
        }

        protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
            ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ScrolledList", this.ClientID);
            descriptor.AddProperty("intNumberOfColumns", NumberOfColumns);
            descriptor.AddElementProperty("td1", _td1.ClientID);
            if (_td2 != null) descriptor.AddElementProperty("td2", _td2.ClientID);
            if (_td3 != null) descriptor.AddElementProperty("td3", _td3.ClientID);
            if (_td4 != null) descriptor.AddElementProperty("td4", _td4.ClientID);
            return new ScriptDescriptor[] { descriptor };
        }

        IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
            return GetScriptReferences();
        }

        IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
            return GetScriptDescriptors();
        }

        #endregion
    }
}
