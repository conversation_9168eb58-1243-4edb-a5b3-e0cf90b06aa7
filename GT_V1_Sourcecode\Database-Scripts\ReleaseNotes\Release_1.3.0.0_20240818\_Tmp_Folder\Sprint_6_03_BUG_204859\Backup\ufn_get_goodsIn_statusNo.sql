ALTER FUNCTION ufn_get_goodsIn_statusNo (@GoodsInId int)  
--  
RETURNS int  
--  
AS --  
   BEGIN  
  
    DECLARE @StatusNo int  
      , @QuarantinedLines int  
      , @InspectedLines int  
      , @Lines int  
      , @InvoiceDate datetime  
      , @SupplierInvoice nvarchar(50)  
        
    SELECT  @StatusNo = 1  
          , @InvoiceDate = InvoiceDate  
          , @SupplierInvoice = SupplierInvoice  
    FROM    dbo.tbGoodsIn  
    WHERE   GoodsInId = @GoodsInId  
  
    SELECT  @Lines = isnull(count(*), 0)  
    FROM    dbo.tbGoodsInLine  
    WHERE   GoodsInNo = @GoodsInId  
  
-- New: no lines  
--------------------------------------------------------------------------------------------------  
    IF @Lines = 0   
        BEGIN  
            SET @StatusNo = 1  
            RETURN @StatusNo    
        END  
  
-- Quarantined: all lines unavailable  
--------------------------------------------------------------------------------------------------  
    SELECT  @QuarantinedLines = isnull(count(*), 0)  
    FROM    dbo.tbGoodsInLine gil  
    JOIN    dbo.tbStock sk ON gil.GoodsInLineId = sk.GoodsInLineNo  
    WHERE   GoodsInNo = @GoodsInId  
            AND sk.Unavailable = 1  
  
    IF @Lines > 0  
        AND @QuarantinedLines = @Lines   
        BEGIN  
            SET @StatusNo = 5  
            RETURN @StatusNo    
        END  
          
-- InvoicePaid: invoiceDate set on header  
--------------------------------------------------------------------------------------------------  
--    IF @SupplierInvoice > ' '  
--        AND @InvoiceDate IS NOT NULL   
--        BEGIN  
--            SET @StatusNo = 6  
--            RETURN @StatusNo    
--        END  
  
-- Received: all received lines not inspected  
--------------------------------------------------------------------------------------------------  
    SELECT  @InspectedLines = isnull(count(*), 0)  
    FROM    dbo.tbGoodsInLine  
    WHERE   GoodsInNo = @GoodsInId  
            AND DateInspected IS NOT NULL  
  
    IF @Lines > 0  
        AND @InspectedLines = 0   
        BEGIN  
            SET @StatusNo = 2  
            RETURN @StatusNo    
        END  
  
  
-- Inspected: all received lines inspected  
--------------------------------------------------------------------------------------------------  
    IF @Lines > 0  
        AND @InspectedLines = @Lines   
        BEGIN  
            SET @StatusNo = 3  
            RETURN @StatusNo    
        END  
  
  
-- PartInspected: not all received lines inspected  
--------------------------------------------------------------------------------------------------  
    IF @Lines > 0  
        AND @InspectedLines > 0  
        AND @InspectedLines < @Lines   
        BEGIN  
            SET @StatusNo = 4  
        END  
  
    RETURN @StatusNo  
--  
   END  