﻿
GO

/*
--===========================================================================================  
TASK			UPDATED BY      DATE				ACTION		DESCRIPTION  
[US-216692]     NgaiTo			04-Nov-2024			CREATE		216692: OGEL - Script to insert new OGEL and correct the existing SO/ Invoice
=============================================================================================  
*/
-- https://gt.reboundeu.com/Ord_SODetail.aspx?so=575667

DECLARE @SalesOrderNo INT = 575667;
DECLARE @OgelNumber NVARCHAR(255) = 'GBOGE2024/00756';

IF NOT EXISTS (SELECT 1 FROM dbo.tbOGELLicense WITH (NOLOCK) WHERE OgelNumber = @OgelNumber)
BEGIN
	INSERT INTO [dbo].[tbOGELLicense] (
		[OgelNumber],
		[Description],
		[Inactive],
		[DLUP],
		[UpdatedBy]
		)
	VALUES (
		@OgelNumber,
		'',
		0,
		GETDATE(),
		1
		)
END

-- Update OgelNumber:
UPDATE [dbo].[tbSO_ExportApprovalStatusOGEL]
SET [OGELNumber] = (SELECT [OgelId] FROM tbOGELLicense WHERE OgelNumber = @OgelNumber)
WHERE [SalesOrderNo] = @SalesOrderNo

-- Verify the result:
-- https://gt.reboundeu.com/Ord_InvoiceDetail.aspx?inv=984763
SELECT *
FROM [dbo].[tbSO_ExportApprovalStatusOGEL]
WHERE [SalesOrderNo] = @SalesOrderNo
