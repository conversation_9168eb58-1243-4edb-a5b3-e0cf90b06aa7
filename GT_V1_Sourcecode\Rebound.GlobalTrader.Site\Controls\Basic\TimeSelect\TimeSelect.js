Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.TimeSelect=function(n){Rebound.GlobalTrader.Site.Controls.TimeSelect.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.TimeSelect.prototype={get_ddlMinutes:function(){return this._ddlMinutes},set_ddlMinutes:function(n){this._ddlMinutes!==n&&(this._ddlMinutes=n)},get_ddlHour:function(){return this._ddlHour},set_ddlHour:function(n){this._ddlHour!==n&&(this._ddlHour=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.TimeSelect.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._ddlMinutes=null,this._ddlHour=null,Rebound.GlobalTrader.Site.Controls.TimeSelect.callBaseMethod(this,"dispose"),this.isDisposed=!0)},setValue:function(n){for(var i,r=n.substr(0,2),t=0;t<this._ddlHour.options.length;t++)this._ddlHour.options[t].selected=this._ddlHour.options[t].value==r;for(i=n.substring(3),t=0;t<this._ddlMinutes.options.length;t++)this._ddlMinutes.options[t].selected=this._ddlMinutes.options[t].value==i},getValue:function(){return String.format("{0}:{1}",this.getHours(),this.getMinutes())},getHours:function(){return this._ddlHour.options[this._ddlHour.selectedIndex].value},getMinutes:function(){return this._ddlMinutes.options[this._ddlMinutes.selectedIndex].value}};Rebound.GlobalTrader.Site.Controls.TimeSelect.registerClass("Rebound.GlobalTrader.Site.Controls.TimeSelect",Sys.UI.Control,Sys.IDisposable);