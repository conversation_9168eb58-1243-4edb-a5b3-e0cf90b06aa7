Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.initializeBase(this,[n]);this._intCompanyID=-1;this._blnFirstTimeApprovedClicked=!0;this._blnApproved=!1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intDefaultRating:function(){return this._intDefaultRating},set_intDefaultRating:function(n){this._intDefaultRating!==n&&(this._intDefaultRating=n)},get_intMailGroupNo:function(){return this._intMailGroupNo},set_intMailGroupNo:function(n){this._intMailGroupNo!==n&&(this._intMailGroupNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ibtnSave&&$R_IBTN.clearHandlers(this._ibtnSave),this._ibtnSave_Footer&&$R_IBTN.clearHandlers(this._ibtnSave_Footer),this._ibtnCancel&&$R_IBTN.clearHandlers(this._ibtnCancel),this._ibtnCancel_Footer&&$R_IBTN.clearHandlers(this._ibtnCancel_Footer),this._chkApproved&&this._chkApproved.dispose(),this._intCompanyID=null,this._ibtnSave=null,this._ibtnSave_Footer=null,this._ibtnCancel=null,this._ibtnCancel_Footer=null,this._chkApproved=null,this._intMailGroupNo=null,this._blnApproved=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyPurchasingInfo");n.set_DataObject("CompanyPurchasingInfo");n.set_DataAction("SaveEdit");n.addParameter("id",this._intCompanyID);n.addParameter("IsApproved",this.getFieldValue("ctlApproved"));n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency"));n.addParameter("TermsNo",this.getFieldValue("ctlTerms"));n.addParameter("Rating",this.getFieldValue("ctlRating"));n.addParameter("ShipViaNo",null);n.addParameter("ShippingAccountNo",null);n.addParameter("ContactNo",this.getFieldValue("ctlContact"));n.addParameter("SupplierNo",this.getFieldValue("ctlSupplierNo"));n.addParameter("CountryNo",this.getFieldValue("ctlCountry"));n.addParameter("OnStop",this.getFieldValue("ctlOnStop"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},formShown:function(){this.showField("ctlShipVia",!1);this.showField("ctlShippingAccountNo",!1);this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._chkApproved=$find(this.getField("ctlApproved").ControlID),this._chkApproved.addClick(Function.createDelegate(this,this.approveClicked)));this.getFieldControl("ctlCurrency")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlTerms")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlShipVia")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlContact")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlCountry")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldDropDownData("ctlCurrency");this.getFieldDropDownData("ctlTerms");this.getFieldDropDownData("ctlShipVia");this.getFieldDropDownData("ctlContact");this.getFieldDropDownData("ctlCountry");this._blnApproved=this._chkApproved._blnChecked},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(!this._blnApproved&&this._chkApproved._blnChecked&&this.getMessageText(),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()},cancelClicked:function(){this.onCancel()},approveClicked:function(){this._blnFirstTimeApprovedClicked&&this._chkApproved._blnChecked&&this.setFieldValue("ctlRating",this._intDefaultRating);this._blnFirstTimeApprovedClicked=!1},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_CompanyPO(this._intCompanyID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this.sendMail(n)},sendMail:function(n){Rebound.GlobalTrader.Site.WebServices.NotifyMessage("",this._intMailGroupNo,$R_RES.CompanyApproveSubject,n,this._intCompanyID,Function.createDelegate(this,this.sendMailComplete))},sendMailComplete:function(){}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);