﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_HUBOfferImportLargeFile_Temp_Invalid]
    @FileID int, -- Search keyword for filtering
	@RecordCount INT OUTPUT
AS
BEGIN
	SELECT 
			@RecordCount = COUNT(*)
		FROM 
			BorisGlobalTraderimports.dbo.tbOfferImportByExcelTemp temp
		OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbCompany cmp 
			WHERE cmp.CompanyName = temp.Vendor AND cmp.ClientNo = 114 AND cmp.IsSupplier = 1
		) cmp
		INNER JOIN BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile lf 
			ON lf.ID = temp.HUBOfferImportLargeFileID AND lf.[status] = 'Uploaded'
		WHERE 
			HUBOfferImportLargeFileID = @FileID
			AND
			(
				(MPN IS NULL 
				OR LEN(MPN) > 256
				OR LEN([LeadTime]) > 256
				OR TRY_CAST(REPLACE([SPQ], ',', '') AS INT) IS NULL
				OR TRY_CAST([SPQ] AS INT) < 0
				OR ISNUMERIC([SPQ]) = 0
				OR LEN([SPQ]) > 256
				OR TRY_CAST(REPLACE([MOQ], ',', '') AS INT) IS NULL
				OR TRY_CAST([MOQ] AS INT) < 0
				OR ISNUMERIC([MOQ]) = 0
				OR MOQ IS NULL 
				OR (OfferedDate NOT LIKE '[0-3][0-9]/[0-1][0-9]/[1-9][0-9][0-9][0-9]' 
					AND OfferedDate NOT LIKE '[1-9]/[1-9]/[1-9][0-9][0-9][0-9]')
				OR TRY_CAST(REPLACE(COST, ',', '') AS FLOAT) IS NULL
				OR TRY_CAST(COST AS FLOAT) < 0
				OR ISNUMERIC(COST) = 0
				OR COST IS NULL 
				OR cmp.CompanyName IS NULL
				)
			)
END;
GO
