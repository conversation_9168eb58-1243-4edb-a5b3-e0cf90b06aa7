///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.initializeBase(this, [element]);
	this._blnIsExpanded = true;
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.prototype = {

	get_ancShowHideClick: function() { return this._ancShowHideClick; }, 	set_ancShowHideClick: function(value) { if (this._ancShowHideClick !== value)  this._ancShowHideClick = value; }, 
	get_pnlOuter: function() { return this._pnlOuter; }, 	set_pnlOuter: function(value) { if (this._pnlOuter !== value)  this._pnlOuter = value; }, 
	get_blnIsExpanded: function() { return this._blnIsExpanded; }, 	set_blnIsExpanded: function(value) { if (this._blnIsExpanded !== value)  this._blnIsExpanded = value; }, 
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.callBaseMethod(this, "initialize");
		$addHandler(this._ancShowHideClick, "click", Function.createDelegate(this, this.toggleRollup));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		$clearHandlers(this.get_element());
		if (this._ancShowHideClick) $clearHandlers(this._ancShowHideClick);
		this._blnIsExpanded = null;
		this._ancShowHideClick = null;
		this._pnlOuter = null;
		this._blnIsExpanded = null;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},

	toggleRollup: function() {
		this._blnIsExpanded = !this._blnIsExpanded;
		this.rollup(!this._blnIsExpanded);
	},
	
	rollup: function(bln) {
		this._blnIsExpanded = !bln;
		if (this._blnIsExpanded) {
			Sys.UI.DomElement.removeCssClass(this._pnlOuter, "leftNuggetCollapsed");
		} else {
			Sys.UI.DomElement.addCssClass(this._pnlOuter, "leftNuggetCollapsed");
		}
	}
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", Sys.UI.Control, Sys.IDisposable);
