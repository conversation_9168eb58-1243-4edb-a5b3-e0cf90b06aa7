//Marker     Changed by      Date               Remarks
//[001]      Vinay           27/05/2013         CR:- Client Invoice
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class ClientInvoiceMainInfo_Edit : Base
    {

		#region Locals

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "ClientInvoiceMainInfo_Edit");
            AddScriptReference("Controls.Nuggets.ClientInvoiceMainInfo.Edit.ClientInvoiceMainInfo_Edit.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpControls();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intClientInvoiceID", _objQSManager.ClientInvoiceID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_InvoiceAmount", FindFieldControl("ctlInvoiceAmount", "lblCurrency_InvoiceAmount").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_GoodsInValue", FindFieldControl("ctlGoodsValue", "lblCurrency_GoodsInValue").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_Tax", FindFieldControl("ctlTax", "lblCurrency_Tax").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_DeliveryCharge", FindFieldControl("ctlDeliveryCharge", "lblCurrency_DeliveryCharge").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_BankFee", FindFieldControl("ctlBankFee", "lblCurrency_BankFee").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblCurrency_CreditCardFee", FindFieldControl("ctlCreditCardFee", "lblCurrency_CreditCardFee").ClientID);
        }

	}
}