﻿using Newtonsoft.Json;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Areas.BOM.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Mvc;

namespace Rebound.GlobalTrader.Site.Areas.BOM.Controllers
{
    public class SupplierInvoiceDetailController : Controller
    {
        // GET: BOM/SupplierInvoice
        public ActionResult SupplierInvoiceDetail(int id)
        {
            if (SessionManager.IsLoggedIn != true)
            {
                Response.Redirect("NotFound.aspx");
            }
            else
            {
                if (!string.IsNullOrWhiteSpace(Request.QueryString["id"]))
                {
                    int SId = Convert.ToInt32(Request.QueryString["id"].ToString());
                    if (SId > 0)
                    {
                    }
                }


            }
            return View();
        }

        [HttpPost]
        public ActionResult GetSupplierInvoiceDetails(int SID)
        {
            try
            {
                SupplierInvoice supplierInvoice = SupplierInvoice.Get(SID);
                return Json(supplierInvoice, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside SupplierInvoiceDetailController class, Method name : GetSupplierInvoiceDetails. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult GetMatchingGILines(int SID)
        {
            try
            {
                SupplierInvoiceMatchingData MatchingData= new SupplierInvoiceMatchingData();
                DataTable PurchaseOrderNumberList;
                List<BLL.SupplierInvoiceLine> lst = BLL.SupplierInvoiceLine.GetMatchingGILines(SessionManager.ClientID, SID, SessionManager.LoginID, out PurchaseOrderNumberList);
                MatchingData.PurchaseOrderNumberList = PurchaseOrderNumberList;
                MatchingData.supplierInvoiceLineList = lst;

                var MatchedData = JsonConvert.SerializeObject(MatchingData,
                                                        Formatting.None,
                                                        new JsonSerializerSettings()
                                                        {
                                                            ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore
                                                        });

                return Json(MatchedData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside SupplierInvoiceDetailController class, Method name : GetMatchingGILines. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }

        [HttpPost]
        public ActionResult InsertSupplierInvoiceLines(string GIIds, int SID)
        {
            bool blnOK = true;
            try
            {
                List<string> aryGoodsInLineIDs = GIIds.Substring(0,GIIds.Length-1).Split('|').ToList();
                aryGoodsInLineIDs = aryGoodsInLineIDs.Distinct().ToList();

                foreach (var item in aryGoodsInLineIDs)
                {
                    int intNewSupplierInvoiceLineID = SupplierInvoiceLine.InsertSupplierInvoiceLine(
                    Convert.ToInt32(item)
                    , SID
                    , Convert.ToInt32(SessionManager.LoginID)
                    , SessionManager.IsPOHub
                    , "Authorised From New Detail Matching Screen"
);
                    if (intNewSupplierInvoiceLineID < 1)
                    {
                        blnOK = false;
                        continue;
                    }
                }

                return Json(blnOK, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside SupplierInvoiceDetailController class, Method name : GetMatchingGILines. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }

        [HttpPost]
        public ActionResult SaveForExport(int SID)
        {
            try
            {
                bool blnResult = SupplierInvoice.SaveForExport((SID.ToString() + ",|,"), SessionManager.LoginID);
                return Json(blnResult, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside SupplierInvoiceDetailController class, Method name : SaveForExport. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
    }
}
