
<%--
Marker     changed by      date         Remarks

--%>
<%@ Control Language="C#" CodeBehind="ClientImportBOMMainInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMMainInfo_Edit")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			
			<ReboundUI_Form:FormField id="ctlCode" runat="server" FieldID="txtCode" ResourceTitle="Code" style="display:none">
				<Field><ReboundUI:ReboundTextBox ID="txtCode" runat="server" Width="100" MaxLength="10" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlName" runat="server" FieldID="txtName" ResourceTitle="Name" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtName" runat="server" Width="250" /></Field>
			</ReboundUI_Form:FormField>		
				
			<ReboundUI_Form:FormField id="ctlCompany" runat="server" FieldID="lblCompany" ResourceTitle="Company" DisplayRequiredFieldMarkerOnly="true">
				<Field><asp:Label ID="lblCompany" runat="server" ></asp:Label> </Field>
			</ReboundUI_Form:FormField>
			
            <ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="ddlContact" ResourceTitle="Contact" IsRequiredField="true">
				<Field><ReboundDropDown:ContactsForCompany ID="ddlContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField ID="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency"  IsRequiredField="true">                          
             <Field> <%--<ReboundDropDown:SellCurrency ID="ddlCurrency" runat="server" Visible="true" /> --%>
                 <ReboundDropDown:BuyCurrencyByGlobalNo ID="ddlCurrency" Visible="true" runat="server" />
             </Field>
             </ReboundUI_Form:FormField>
           
<%--            	  <ReboundUI_Form:FormField id="ctlSalesman" runat="server" FieldID="ddlSalesman" ResourceTitle="Salesman">
				<Field><ReboundDropDown:Employee ID="ddlSalesman" runat="server" /></Field>
			</ReboundUI_Form:FormField>	--%>

            <ReboundUI_Form:FormField id="ctlSalespersion" runat="server" FieldID="cmbSalespersion" ResourceTitle="Salesperson" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbSalespersion" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="SalesPersion" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlInActive" runat="server" FieldID="chkInActive" ResourceTitle="IsInactive">
				<Field><ReboundUI:ImageCheckBox ID="chkInActive" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" TextMode="MultiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>

             

          
		</ReboundUI_Table:Form>
	
	</Content>
</ReboundUI_Form:DesignBase>
