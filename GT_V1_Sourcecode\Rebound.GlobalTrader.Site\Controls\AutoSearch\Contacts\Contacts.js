Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.Contacts=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.Contacts.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.Contacts.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.Contacts.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("Contacts")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.AutoSearch.Contacts.callBaseMethod(this,"dispose")},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",i=this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.Navigate?$RGT_nubButton_Contact(n.ID,n.Name):$R_FN.setCleanTextValue(n.Name),this.addResultItem(i,$R_FN.setCleanTextValue(n.Name),n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.Contacts.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Contacts",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);