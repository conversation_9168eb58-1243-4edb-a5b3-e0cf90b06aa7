using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
    [DefaultProperty("")]
    [ToolboxData("<{0}:TextBoxArray runat=server></{0}:TextBoxArray>")]
    public class TextBoxArray : LabelFormField, INamingContainer {

        #region Locals

        HtmlTable _tbl;

        #endregion

        #region Properties

        private int _intTextBoxWidth = 0;
        public int TextBoxWidth {
            get { return _intTextBoxWidth; }
            set { _intTextBoxWidth = value; }
        }

        private ReboundTextBox.TextBoxModeList _enmTextBoxMode = ReboundTextBox.TextBoxModeList.Normal;
        public ReboundTextBox.TextBoxModeList TextBoxMode {
            get { return _enmTextBoxMode; }
            set { _enmTextBoxMode = value; }
        }

        private string _strTitleText = "";
        public string TitleText {
            get { return _strTitleText; }
            set { _strTitleText = value; }
        }

        private bool _blnUppercaseOnly = false;
        public bool UppercaseOnly {
            get { return _blnUppercaseOnly; }
            set { _blnUppercaseOnly = value; }
        }

        private bool _blnFormatDecimalPlaces = false;
        public bool FormatDecimalPlaces {
            get { return _blnFormatDecimalPlaces; }
            set { _blnFormatDecimalPlaces = value; }
        }

        private int _intDecimalPlaces = 2;
        public int DecimalPlaces {
            get { return _intDecimalPlaces; }
            set { _intDecimalPlaces = value; }
        }

        private bool _blnRequiredField = false;
        public bool RequiredField {
            get { return _blnRequiredField; }
            set { _blnRequiredField = value; }
        }

        private bool _blnAllowAddAndDelete = false;
        public bool AllowAddAndDelete {
            get { return _blnAllowAddAndDelete; }
            set { _blnAllowAddAndDelete = value; }
        }

        private bool _blnValidatePasteChar = false;
        public bool ValidatePasteChar
        {
            get { return _blnValidatePasteChar; }
            set { _blnValidatePasteChar = value; }
        }

        #endregion

        protected override void OnInit(EventArgs e) {
            base.OnInit(e);
            AddScriptReference("Controls.Basic.ReboundTextBox.ReboundTextBox");
			AddScriptReference("Controls.FormFieldCollections.TextBoxArray.TextBoxArray");
            RemoveCSSClass = true;
        }

        protected override void OnLoad(EventArgs e) {
            if (_strTitleText != "") {
                HtmlGenericControl h5 = ControlBuilders.CreateHtmlGenericControl("h5", "textBoxArray");
                ControlBuilders.CreateLiteralInsideParent(h5, _strTitleText);
                AddControl(h5);
            }
            _tbl = new HtmlTable();
            _tbl.CellPadding = 0;
            _tbl.CellSpacing = 0;
            _tbl.Border = 0;
            _tbl.Attributes["class"] = "formRows formRowsNoMargin";
            AddControl(_tbl);
            SetupScriptDescriptors();
            base.OnLoad(e);
        }

        private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray", this.ClientID);
            _scScriptControlDescriptor.AddElementProperty("tbl", _tbl.ClientID);
            _scScriptControlDescriptor.AddProperty("intTextBoxWidth", _intTextBoxWidth);
            _scScriptControlDescriptor.AddProperty("enmTextBoxMode", _enmTextBoxMode);
            _scScriptControlDescriptor.AddProperty("blnUppercaseOnly", _blnUppercaseOnly);
            _scScriptControlDescriptor.AddProperty("blnFormatDecimalPlaces", _blnFormatDecimalPlaces);
            _scScriptControlDescriptor.AddProperty("intDecimalPlaces", _intDecimalPlaces);
            _scScriptControlDescriptor.AddProperty("blnAllowAddAndDelete", _blnAllowAddAndDelete);
            _scScriptControlDescriptor.AddProperty("blnRequiredField", _blnRequiredField);
            _scScriptControlDescriptor.AddProperty("blnValidatePasteChar", _blnValidatePasteChar);
        }

    }
}