///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 15.12.2009:
// - allow ordering by PO Delivery Date (task 171)
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
/* Mark<PERSON>     changed by      date         Remarks
/* [0001]     Suhail         30/05/2018    Avoiding visibility of grid on page load */
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.prototype = {

    get_blnShowUninspectedOnly: function() { return this._blnShowUninspectedOnly; }, set_blnShowUninspectedOnly: function(v) { if (this._blnShowUninspectedOnly !== v) this._blnShowUninspectedOnly = v; },
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (v) { if (this._blnPOHub !== v) this._blnPOHub = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },

    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.updateFilterVisibility();
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/SupplierMfrSearch";
        this._strDataObject = "SupplierMfrSearch";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
       // [0001] Code Start
       // this.getData();
        this.showContentLoading(false);
        // [0001] Code End
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnShowUninspectedOnly = null;
        this._blnPOHub = null;
        this._IsGlobalLogin = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._blnShowUninspectedOnly = (this._intCurrentTab == 1);
        this.updateFilterVisibility();
        // [0001] Code Start
       // this.getData();
        this.showContentLoading(false);
        // [0001] Code End
    },

    setupDataCall: function() {
        this._objData.addParameter("UninspectedOnly", this._blnShowUninspectedOnly);
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
    },

    getDataOK: function () {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData;
            // if (row.IsPoHub == true) {
            aryData = [
                $R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(row.ID, row.No), row.QueryRaised == true ? "<a target='_blank' href='Whs_GIDetail.aspx?gi=" + row.ID + "&landedgilineid=" + row.GoodsInLineId + "&islandedquery=true'><img src='../App_Themes/Original/images/buttons/nuggets/quote.gif' title='Query Raised For GI Line.' id='imgQuery'/></a>" : "")
                , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                , row.Quantity
                //, $R_FN.writeDoubleCellValue($RGT_nubButton_Company((row.PONo > 0 && row.IsPoHub == false) ? row.IPOSupplier : row.CMNo, (row.PONo > 0 && row.IsPoHub == false) ? row.IPOSupplierName : row.CM), $R_FN.setCleanTextValue(row.AWB))
                , $R_FN.writeDoubleCellValue($R_FN.showSupplierMessage($RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.SuppMessage)), $R_FN.setCleanTextValue(row.AWB))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Date), $R_FN.setCleanTextValue(row.Receiver))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.DelivDate), $RGT_nubButton_PurchaseOrder(row.PONo, row.PO))
                , $R_FN.setCleanTextValue(row.GoodInLineMessage)
                , $R_FN.setCleanTextValue(row.ClientName)




            ];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },
    updateFilterVisibility: function() {
        this.getFilterField("ctlClientName").show(this._blnPOHub || this._IsGlobalLogin);
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
