Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.initializeBase(this,[n]);this._intCompanyID=-1;this._blnLineLoaded=!1;this._clientId=0;this._inactive=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnBomMapping:function(){return this._ibtnBomMapping},set_ibtnBomMapping:function(n){this._ibtnBomMapping!==n&&(this._ibtnBomMapping=n)},get_ibtnSupplierImport:function(){return this._ibtnSupplierImport},set_ibtnSupplierImport:function(n){this._ibtnSupplierImport!==n&&(this._ibtnSupplierImport=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.callBaseMethod(this,"initialize");this._strDataPath="controls/Nuggets/CompanyApiCustomer";this._strDataObject="CompanyApiCustomer";this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tbl.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[1]),this._frmEdit.addCancel(Function.createDelegate(this,this.hideEditForm)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnBomMapping&&($R_IBTN.addClick(this._ibtnBomMapping,Function.createDelegate(this,this.showAddMappingForm)),this._frmAddMapping=$find(this._aryFormIDs[2]),this._frmAddMapping.addCancel(Function.createDelegate(this,this.hideAddMappingForm)),this._frmAddMapping.addSaveComplete(Function.createDelegate(this,this.saveAddMappingComplete)));this._ibtnSupplierImport&&($R_IBTN.addClick(this._ibtnSupplierImport,Function.createDelegate(this,this.showOfferForm)),this.frmSupplierImport=$find("ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmSupplierImport_ctlDB"),this.frmSupplierImport.addCancel(Function.createDelegate(this,this.hideOfferForm)),this.frmSupplierImport.addSaveComplete(Function.createDelegate(this,this.saveOfferComplete)));this.getData();this.getCompanyInactive()},dispose:function(){this.isDisposed||(this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnBomMapping&&$R_IBTN.clearHandlers(this._ibtnBomMapping),this._ibtnSupplierImport&&$R_IBTN.clearHandlers(this._ibtnSupplierImport),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._frmAdd&&this._frmAdd.dispose(),this._frmEdit&&this._frmEdit.dispose(),this._ibtnBomMapping&&this._frmAddMapping.dispose(),this._ibtnSupplierImport&&this.frmSupplierImport.dispose(),this._tbl&&this._tbl.dispose(),this._frmAdd=null,this._ibtnBomMapping=null,this._ibtnSupplierImport=null,this._intCompanyID=null,this._blnLineLoaded=null,this._inactive=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.callBaseMethod(this,"dispose"))},enableEditButtons:function(n){if(n){var t=this._tbl.countRows();this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive&&this._blnLineLoaded)}else this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1)},disableAddButtons:function(n){if(n){var t=this._tbl.countRows();t==0?this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._inactive):this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!1)}},tbl_SelectedIndexChanged:function(){this._intLineID=this._tbl._varSelectedValue;this._intLineID&&(this._blnLineLoaded=!0);this.enableEditButtons(!0)},getData:function(){this._blnLineLoaded=!1;$R_FN.showElement(this._pnlLineDetail,!1);this.enableEditButtons(!0);this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCertificates");n.addParameter("CompanyID",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var i,r;if(i=n._result,this.showLoading(!1),this._tbl.clearTable(),i.Lines)for(r=0;r<i.Lines.length;r++){var t=i.Lines[r],f=[$R_FN.setCleanTextValue(t.Email),$R_FN.setCleanTextValue(t.Mobile),$R_FN.setCleanTextValue(t.ContactName),$R_FN.setCleanTextValue(t.InActive==!0?"Yes":"No"),$R_FN.setCleanTextValue(t.IsBomUser==!0?"Yes":"No"),$R_FN.setCleanTextValue(t.InSupUser==!0?"Yes":"No")],e={Password:t.Password,InActive:t.InActive,IsBomUser:t.IsBomUser,InSupUser:t.InSupUser,InCountryCode:t.CountryCode},u=t.InActive?"ceased":"";this._tbl.addRow(f,t.ID,t.ID==this._intLineID,e,u);t=null;u=null}this.disableAddButtons(!0);this._intLineCount=this._tbl.countRows();this.showContent(!0);this.showContentLoading(!1);this._tbl.resizeColumns();this._intLineID>0&&this.tbl_SelectedIndexChanged();this.enableEditButtons(!0)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCompanyInactive:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCompanyDetailInactive");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getCompanyInactiveOK));n.addError(Function.createDelegate(this,this.getCompanyInactiveError));n.addTimeout(Function.createDelegate(this,this.getCompanyInactiveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCompanyInactiveOK:function(n){var t=n._result;this._inactive=t.Inactive;this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnAdd,!this._inactive);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnSupplierImport,!this._inactive);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnBomMapping,!this._inactive)},getCompanyInactiveError:function(n){this.showError(!0,n.get_ErrorMessage())},showEditForm:function(){var n=this._tbl.getSelectedExtraData().InCountryCode;this._frmEdit._intCompanyID=this._intCompanyID;this._frmEdit._intLineID=this._intLineID;this._frmEdit.setFieldValue("ctlEmail",this._tbl.getSelectedCellValue(0));this._frmEdit.setFieldValue("ctlMobile",this._tbl.getSelectedCellValue(1));this._frmEdit.setFieldValue("ctlContactPerson",this._tbl.getSelectedCellValue(2));$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlMobile_ctl03_txtCountryCode").val(this._tbl.getSelectedExtraData().InCountryCode);this._tbl.getSelectedExtraData().InActive&&this._frmEdit.setFieldValue("ctlInActive",this._tbl.getSelectedExtraData().InActive);this._tbl.getSelectedExtraData().IsBomUser&&this._frmEdit.setFieldValue("ctlBomUser",this._tbl.getSelectedExtraData().IsBomUser);this._tbl.getSelectedExtraData().InSupUser&&this._frmEdit.setFieldValue("ctlSupplierUser",this._tbl.getSelectedExtraData().InSupUser);this._frmEdit.setFieldValue("ctlPassword",this._tbl.getSelectedExtraData().Password);this.showForm(this._frmEdit,!0)},hideEditForm:function(){$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword").css("background-color","#56954E");$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword").css("background-color","#56954E");$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword_ctl03_txtPassword").val("");$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").val("");$("#checkbox2").prop("checked",!1);this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showAddMappingForm:function(){this._clientId=$("#ctl00_ddlClientByMaster_ddl").val();this._frmAddMapping._intCompanyID=this._intCompanyID;this.showForm(this._frmAddMapping,!0)},hideAddMappingForm:function(){resetField();this.showForm(this._frmAddMapping,!1)},saveAddMappingComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intLineID=this._frmAddMapping._intNewID;this.getData()},showOfferForm:function(){this._clientId=$("#ctl00_ddlClientByMaster_ddl").val();this.showForm(this.frmSupplierImport,!0)},hideOfferForm:function(){resetFieldSupp();this.showForm(this._frmAddMapping,!1)},saveOfferComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intLineID=this.frmSupplierImport._intNewID;this.getData()},showAddForm:function(){this._frmAdd._intCompanyID=this._intCompanyID;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1)},saveAddComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intLineID=this._frmAdd._intNewID;this.getData()}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyApiCustomer",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);