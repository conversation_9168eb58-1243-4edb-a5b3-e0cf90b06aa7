using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.Site {
	public class TaxManager {

        internal static double GetTaxRate(int intTaxNo, DateTime dtmTaxPoint, bool? isGlobalTax = false)
        {
            double dbl = 0;
            BLL.TaxRate rt = BLL.TaxRate.GetForTaxAndDate(intTaxNo, SessionManager.ClientID, dtmTaxPoint, isGlobalTax);
            if (rt != null) dbl = (double)rt.CurrentTaxRate / 100;
            rt = null;
            return dbl;
        }

        internal static double GetTaxRate2(int intTaxNo, DateTime dtmTaxPoint, bool? isGlobalTax = false)
        {
            double dbl = 0;
            BLL.TaxRate rt = BLL.TaxRate.Get2ForTaxAndDate(intTaxNo, SessionManager.ClientID, dtmTaxPoint, isGlobalTax);
            if (rt != null) dbl = (double)rt.CurrentTaxRate / 100;
            rt = null;
            return dbl;
        }
    }
}
