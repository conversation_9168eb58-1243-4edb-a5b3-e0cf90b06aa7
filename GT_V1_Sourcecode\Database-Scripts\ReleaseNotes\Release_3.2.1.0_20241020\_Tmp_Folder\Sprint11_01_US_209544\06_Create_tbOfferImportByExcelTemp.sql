﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209544]     CuongDox		 17-Sep-2024		CREATE		Add temptable for storing excel data
===========================================================================================  
*/

use BorisGlobalTraderImports
DROP TABLE IF EXISTS [tbOfferImportByExcelTemp];

CREATE TABLE [dbo].[tbOfferImportByExcelTemp](
	[OfferTempId] [int] IDENTITY(1,1) NOT NULL,
	[MPN] [nvarchar](MAX) NULL,
	[MFR] [nvarchar](MAX) NULL,
	[COST] [nvarchar](MAX) NULL,
	[LeadTime] [nvarchar](MAX) NULL,
	[SPQ] [nvarchar](MAX) NULL,
	[MOQ] [nvarchar](MAX) NULL,
	[Remarks] [nvarchar](MAX) NULL,
	[OfferedDate] [nvarchar](MAX) NULL,
	[Vendor] [nvarchar](MAX) NULL,
	[ClientNo] [int] NULL,
	[DLUP] [datetime] NOT NULL,
	GeneratedFileName [nvarchar](255)  NULL
 CONSTRAINT [PK_tbOfferImportByExcelTemp] PRIMARY KEY CLUSTERED 
(
	[OfferTempId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

ALTER TABLE [dbo].[tbOfferImportByExcelTemp] ADD  CONSTRAINT [DF_tbOfferImportByExcelTemp_DLUP]  DEFAULT (getdate()) FOR [DLUP]
GO