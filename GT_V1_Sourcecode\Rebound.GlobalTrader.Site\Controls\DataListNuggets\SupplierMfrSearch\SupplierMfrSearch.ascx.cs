//----------------------------------------------------------------------------------------------------
// RP 23.12.2009:
// - render state on server
//
// RP 15.12.2009:
// - allow ordering by PO Delivery Date (task 171)
//----------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
    public partial class SupplierMfrSearch : Base {

        #region Properties

        private bool _blnShowUninspectedOnly = false;
        public bool ShowUninspectedOnly {
            get { return _blnShowUninspectedOnly; }
            set { _blnShowUninspectedOnly = value; }
        }

        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            //IsGlobalLogin = true;//Call here Global Security.
            SetDataListNuggetType("GoodsIn");
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("Nuggets", "GoodsIn");
            AddScriptReference("Controls.DataListNuggets.GoodsIn.GoodsIn");
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
			SetupTable();
        }

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.GoodsIn", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
			base.OnLoad(e);
		}
		protected override void RenderAdditionalState() {
			int intTab = 0;
			if (string.IsNullOrEmpty(_objQSManager.SearchPartNo)) {
				var strCallType = this.GetSavedStateValue("CallType").ToUpper();
				if (string.IsNullOrEmpty(strCallType)) strCallType = "ALL";
				if (strCallType == "ALL") intTab = 0;
				if (strCallType == "UNINSPECTED") intTab = 1;
			}
			this._blnShowUninspectedOnly = (intTab == 1);
			((Pages.Content)Page).CurrentTab = intTab;
			this.OnAskPageToChangeTab();
			base.RenderAdditionalState();
		}

		protected override void OnPreRender(EventArgs e) {
			base.OnPreRender(e);
			_scScriptControlDescriptor.AddProperty("blnShowUninspectedOnly", _blnShowUninspectedOnly);
		}

        #endregion

        private void SetupTable() {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("GoodsIn", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", "AirWayBill", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("DateReceived", "ReceivedBy", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            _tbl.Columns.Add(new FlexiDataColumn("DeliveryDate", "PurchaseOrder", Unit.Empty, true));
            _tbl.Columns.Add(new FlexiDataColumn("Status", Unit.Empty, true));
            if (SessionManager.IsPOHub == true || _IsGlobalLogin==true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("Client", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            }
           
        }
    }
}