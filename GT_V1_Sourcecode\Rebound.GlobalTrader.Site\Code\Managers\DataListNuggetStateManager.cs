﻿using System;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls.DataListNuggets;

namespace Rebound.GlobalTrader.Site {

	public class DataListNuggetStateManager {

		public static DataListNuggetState GetDataListNuggetState(int intDataListNuggetID, string strDataListNuggetSubType) {
			DataListNuggetState obj;
			BLL.DataListNuggetState dlns = BLL.DataListNuggetState.GetForDLNAndLogin(intDataListNuggetID, strDataListNuggetSubType ?? "", SessionManager.LoginID);
			if (dlns == null) {
				obj = new DataListNuggetState();
				obj.ClearState();
			} else {
				obj = DataListNuggetState.Deserialize(dlns.StateText);
			}
			dlns = null;
			return obj;
		}

		public static void SaveState(int intDataListNuggetID, string strDataListNuggetSubType, string strState) {
			BLL.DataListNuggetState.UpdateForDLNAndLogin(
				intDataListNuggetID
				, strDataListNuggetSubType
				, SessionManager.LoginID
				, strState
			);
		}

	}
}
