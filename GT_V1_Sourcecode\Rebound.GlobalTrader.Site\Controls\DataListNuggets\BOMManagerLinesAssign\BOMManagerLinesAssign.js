Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign.initializeBase(this,[n]);this._frmConfirm=null;this._intSalesPersonId=null;this._ibtnAssign=null};Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign.prototype={get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_ibtnAssign:function(){return this._ibtnAssign},set_ibtnAssign:function(n){this._ibtnAssign!==n&&(this._ibtnAssign=n)},get_sortIndex:function(){return this._sortIndex},set_sortIndex:function(n){this._sortIndex!==n&&(this._sortIndex=n)},get_sortDir:function(){return this._sortDir},set_sortDir:function(n){this._sortDir!==n&&(this._sortDir=n)},get_pageIndex:function(){return this._pageIndex},set_pageIndex:function(n){this._pageIndex!==n&&(this._pageIndex=n)},get_pageSize:function(){return this._pageSize},set_pageSize:function(n){this._pageSize!==n&&(this._pageSize=n)},get_code:function(){return this._code},set_code:function(n){this._code!==n&&(this._code=n)},get_name:function(){return this._name},set_name:function(n){this._name!==n&&(this._name=n)},get_ctlMultiSelectionCount:function(){return this._ctlMultiSelectionCount},set_ctlMultiSelectionCount:function(n){this._ctlMultiSelectionCount!==n&&(this._ctlMultiSelectionCount=n)},get_intSalesPersonId:function(){return this._intSalesPersonId},set_intSalesPersonId:function(n){this._intSalesPersonId!==n&&(this._intSalesPersonId=n)},initialize:function(){this._ibtnPrint=$get("ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ibtnAssign_hyp");this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/BOMManagerLinesAssign";this._strDataObject="BOMManagerLinesAssign";Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign.callBaseMethod(this,"initialize");this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.exportCSV));this._table.addMultipleSelectionChanged(Function.createDelegate(this,this.selectionMade));this._ctlMultiSelectionCount.registerTable(this._table);document.getElementById("ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ibtnAssign_hyp").addEventListener("click",Function.createDelegate(this,this.AssignUser));$("#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ibtnAssign_hyp").hide()},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},saveCeaseComplete:function(){this.hideConfirmForm();this.getData()},selectionMade:function(){this._table._arySelectedIndexes.length>0?($("#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ibtnAssign_hyp").show(),$("#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ibtnAssign_hyp").css("font-weight","bold").css("font-size","12px")):$("#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ibtnAssign_hyp").hide()},AssignUser:function(){var t=this._table._aryCurrentValues,n=$("#ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ddlSalesperson_ddl").val();n!="0"&&$.ajax({processData:!0,contentType:"application/json",type:"POST",url:"BOM/BOMManager/AssignUserId?Bommanagerids="+t+"&AssignUserId="+n,dataType:"json",success:function(){alert("User Assigned")},error:function(){alert("Something went wrong.")}})},dispose:function(){this.isDisposed||(this._blnPOHub=null,this._ibtnExportCSV&&$R_IBTN.clearHandlers(this._ibtnExportCSV),this._ibtnExportCSV=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){},getRadioButtonValue:function(){for(var i="",u=document.getElementById("ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl16_ctlFilter_ctl11_ctl02_radHeaderDetail"),t=u.getElementsByTagName("input"),r,n=0;n<t.length;n++)if(t[n].checked){r=t[n];i=r.value;break}return i},getDataOK:function(){var u,t,r,n,i;if(this._ctlMultiSelectionCount.clearAll(),u=this._objResult.isSearchFromRequirements,this._sortIndex=this._objResult.SortIndex,this._sortDir=this._objResult.SortDir,this._pageIndex=this._objResult.PageIndex,this._pageSize=this._objResult.PageSize,this._code=this._objResult.Code,this._name=this._objResult.Name,this._intSalesPersonId=this._objResult.SalesPerson,!u)for(t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.writeDoubleCellValue(n.isPoHUB==!0?$RGT_nubButton_BMMPO(n.ID,n.Code):$RGT_nubButton_BMM(n.ID,n.Code)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Name)),$R_FN.writeDoubleCellValue("",""),$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CompanyNo,n.Company),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Salesman),$R_FN.setCleanTextValue(n.AssignedUser)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.ReceivedDate),$R_FN.setCleanTextValue(n.PromiseDate)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.BOMStatus)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.TotalValue)),n.TotalValue],this._table.addRow(i,n.ID,!1),i=null,n=null;if(u)for(t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.writeDoubleCellValue(n.isPoHUB==!0?$RGT_nubButton_BMMPO(n.ID,n.BOMCode):$RGT_nubButton_BMM(n.ID,n.BOMCode)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.BOMName)),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Salesman)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Received),$R_FN.setCleanTextValue(n.Promised)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.BOMStatus)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.TotalValue)),n.TotalValue],this._table.addRow(i,n.ID,!1),i=null,n=null;setTimeout(function(){document.getElementById("ctl00_cphMain_ctlBOMManagerResults_ctlDB_ctl12_ddlSalesperson_ctl02").click()},1e3)},updateFilterVisibility:function(){this.getFilterField("ctlDivision")._ddl._intSelClientNo=9999;this.getFilterField("ctlDivision")._ddl.getData();this.getFilterField("ctlClient").show(this._blnPOHub);this.getFilterField("ctlDivision").show(this._blnPOHub)},exportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/DataListNuggets/BOMManagerLinesAssign");n.set_DataObject("BOMManagerLinesAssign");n.addParameter("ViewLevel",this._enmViewLevel);n.set_DataAction("ExportToCSV");n._intTimeoutMilliseconds=5e5;n.addParameter("SortIndex",this._sortIndex);n.addParameter("SortDir",this._sortDir);n.addParameter("PageIndex",this._pageIndex);n.addParameter("PageSize",this._pageSize);n.addParameter("Code",this._code);n.addParameter("Name",this._name);n.addParameter("BomStatus",this.getFilterFieldValue("ctlStatus"));n.addParameter("Manufacturer",this.getFilterFieldValue("ctlManufacturer"));n.addParameter("PoHubBuyer",this.getFilterFieldValue("ctlSalesperson"));n.addParameter("StartDate",this.getFilterFieldValue("ctlStartDate"));n.addParameter("EndDate",this.getFilterFieldValue("ctlEndDate"));n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},onClientChange:function(){this.getDivision();this.getSalesPersonByClient()}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);