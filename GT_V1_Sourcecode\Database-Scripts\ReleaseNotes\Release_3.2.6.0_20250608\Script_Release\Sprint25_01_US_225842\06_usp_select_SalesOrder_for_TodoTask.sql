﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--============================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-225842]     An.TranTan		 03-Apr-2025		CREATE		Get addition data for Sales order when create TODO task
==============================================================================================================================  
*/    
CREATE OR ALTER PROCEDURE [dbo].[usp_select_SalesOrder_for_TodoTask]            
    @SalesOrderId int            
AS             
    SELECT  so.SalesOrderId             
			, so.SalesOrderNumber               
			, so.CompanyNo            
			, co.CompanyName  
    FROM    tbSalesOrder so WITH(NOLOCK)           
    JOIN    tbCompany co WITH(NOLOCK) ON so.CompanyNo = co.CompanyId            
    WHERE   so.SalesOrderId = @SalesOrderId     
GO


