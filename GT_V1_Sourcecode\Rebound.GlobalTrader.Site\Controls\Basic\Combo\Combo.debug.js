///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - complete dispose event
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.Combo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Combo.initializeBase(this, [element]);
	this._intSelectedID = null;
	this._strSelectedText = "";
	this._strSelectedExtraText = "";
};

Rebound.GlobalTrader.Site.Controls.Combo.prototype = {

    get_txt: function() { return this._txt; }, set_txt: function(v) { if (this._txt !== v) this._txt = v; },
    get_aut: function() { return this._aut; }, set_aut: function(v) { if (this._aut !== v) this._aut = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Combo.callBaseMethod(this, "initialize");
        this._aut.addSelectionMadeEvent(Function.createDelegate(this, this.selectionMade));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this.get_element()) $clearHandlers(this.get_element());
        if (this._aut) this._aut.dispose();
        this._txt = null;
        this._aut = null;
        this._intSelectedID = null;
        this._strSelectedText = null;
        this._strSelectedExtraText = null;
        Rebound.GlobalTrader.Site.Controls.Combo.callBaseMethod(this, "dispose");
        this.isDisposed = true;
    },

    reset: function() {
        this._intSelectedID = null;
        this._strSelectedText = "";
        if (this._aut) this._aut.reselect();
    },

    selectionMade: function() {
        this._intSelectedID = this._aut._varSelectedID;
        this._strSelectedText = this._aut._varSelectedValue;
        this._strSelectedExtraText = this._aut._varComboExtraText;
    },

    checkEntered: function() {
        this.selectionMade();
        return (this._intSelectedID > 0);
    },

    setValue: function(varID, varValue) {
        if (varID == undefined || varValue == undefined) return;
        if (!varID) varID = "";
        if (!varValue) varValue = "";
        if (varID == "" || varValue == "") {
            this._aut.reselect();
        } else {
            this._aut.doItemClick(varValue, varID);
        }
    },

    getValue: function() {
        this.selectionMade();
        return (this._intSelectedID);
    }

};

Rebound.GlobalTrader.Site.Controls.Combo.registerClass("Rebound.GlobalTrader.Site.Controls.Combo", Sys.UI.Control, Sys.IDisposable);