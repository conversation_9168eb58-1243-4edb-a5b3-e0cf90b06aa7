///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch = function (element) {
    Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.initializeBase(this, [element]);
    this._strpart = "";
    this._searchType = "";
    
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.prototype = {
    addPotentialStatusChange: function (handler) { this.get_events().addHandler("PotentialStatusChange", handler); },
    removePotentialStatusChange: function (handler) { this.get_events().removeHandler("PotentialStatusChange", handler); },
    onPotentialStatusChange: function () {
        var handler = this.get_events().getHandler("PotentialStatusChange");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
		this.addSearched(Function.createDelegate(this, this.doSearched));
	   
	},

	dispose: function() { 
	    if (this.isDisposed) return;
	    this._strpart = null;
        this._searchType = null;
	    Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.callBaseMethod(this, "dispose");
	},
	
	
	doSetupData: function () {
	    this._objData.set_PathToData("controls/ItemSearch/IhsSearch");
	    this._objData.set_DataObject("IhsSearch");
		this._objData.set_DataAction("GetData");
        this._objData.addParameter("partsearch", this._strpart);
        this._objData.addParameter("searchType", this._searchType);
		
	},
	getGroupValue: function () {
	   // return this.getFieldValue("ctlGroup");
	},
	
	doGetDataComplete: function () {
	    this._invoiceExist = false;
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
		    var row = this._objResult.Results[i];
		    var aryData = [
               
                $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ID), $R_FN.showBoldText($R_FN.setCleanTextValue(row.PartStatus))),
                $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Manufacturer), $R_FN.setCleanTextValue(row.ROHSName)),
                $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.CountryOfOrigin), $R_FN.setCleanTextValue(row.HTSCode)),
                $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Packaging), $R_FN.setCleanTextValue(row.PackagingSize)),
                $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Descriptions), $R_FN.setCleanTextValue(row.ECCNCode)),
                //$R_FN.setCleanTextValue(row.Descriptions)
		    ];
		    var objExtraData = {
		        PartName: row.ID,
		        ManufacturerNo: row.ManufacturerNo,
		        Manufacturer: row.Manufacturer,
		        Descriptions: row.Descriptions,
		        ROHSNo: row.ROHSNo,
		        ROHSName: row.ROHSName,
		        CountryOfOrigin: row.CountryOfOrigin,
		        CountryOfOriginNo: row.CountryOfOriginNo,
		        LifeCycleStage: row.PartStatus,
		        HTSCode: row.HTSCode,
		        AveragePrice: row.AveragePrice,
		        Packaging: row.Packaging,
		        PackagingSize: row.PackagingSize,
		        IHSPartsId: row.IHSPartsId,
		        ResultType: row.ResultType,
		        ihsCurrencyCode: row.ihsCurrencyCode,
		        ProdDesc: row.ProdDesc,
		        ProdNo: row.ProdNo,
		        IHSProdDesc: row.IHSProdDesc,
		        IHSDutyCode: row.IHSDutyCode,
		        ManufacturerFullName: row.ManufacturerFullName,
                IHSProduct: row.IHSProduct,
                ECCNCode: row.ECCNCode

		    };
		    this._tblResults.addRow(aryData, row.Ids, false, objExtraData);
		    aryData = null;
		    row = null;
		  
		   
		}
	},
	doSearched: function () {
	    this.onPotentialStatusChange();
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
