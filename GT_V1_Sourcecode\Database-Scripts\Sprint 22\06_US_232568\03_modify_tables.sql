﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-232568]     An.TranTan		 17-Feb-2025		UPDATE		Add column HUBRFQ ID to tempData table
===========================================================================================  
*/
IF COL_LENGTH('BorisGlobalTraderimports.dbo.tbBOMSourcing_tempData', 'BOMNo') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderimports.dbo.tbBOMSourcing_tempData ADD BOMNo INT NULL
END
GO
IF COL_LENGTH('BorisGlobalTraderimports.dbo.tbBOMSourcing_tempData', 'CustomerRequirementNo') IS NOT NULL
BEGIN
   ALTER TABLE BorisGlobalTraderimports.dbo.tbBOMSourcing_tempData DROP COLUMN CustomerRequirementNo
END
GO
IF COL_LENGTH('BorisGlobalTraderImports.dbo.tboffer', 'ReferenceRequirementNo') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderimports.dbo.tboffer ADD ReferenceRequirementNo INT NULL
END
GO
--create user-defined table types to store temp data
IF OBJECT_ID('dbo.usp_saveBOMSourcing_tempData', 'P') IS NOT NULL
  DROP PROCEDURE dbo.usp_saveBOMSourcing_tempData
GO

IF EXISTS (SELECT 1 FROM sys.types WHERE name = 'UploadedBOMSourcingResult' AND is_table_type = 1)
BEGIN
    DROP TYPE [dbo].[UploadedBOMSourcingResult]
END
GO

CREATE TYPE [dbo].[UploadedBOMSourcingResult] AS TABLE(
	[Column1] [nvarchar](max) NULL DEFAULT (NULL),
	[Column2] [nvarchar](max) NULL DEFAULT (NULL),
	[Column3] [nvarchar](max) NULL DEFAULT (NULL),
	[Column4] [nvarchar](max) NULL DEFAULT (NULL),
	[Column5] [nvarchar](max) NULL DEFAULT (NULL),
	[Column6] [nvarchar](max) NULL DEFAULT (NULL),
	[Column7] [nvarchar](max) NULL DEFAULT (NULL),
	[Column8] [nvarchar](max) NULL DEFAULT (NULL),
	[Column9] [nvarchar](max) NULL DEFAULT (NULL),
	[Column10] [nvarchar](max) NULL DEFAULT (NULL),
	[Column11] [nvarchar](max) NULL DEFAULT (NULL),
	[Column12] [nvarchar](max) NULL DEFAULT (NULL),
	[Column13] [nvarchar](max) NULL DEFAULT (NULL),
	[Column14] [nvarchar](max) NULL DEFAULT (NULL),
	[Column15] [nvarchar](max) NULL DEFAULT (NULL),
	[Column16] [nvarchar](max) NULL DEFAULT (NULL),
	[Column17] [nvarchar](max) NULL DEFAULT (NULL),
	[Column18] [nvarchar](max) NULL DEFAULT (NULL),
	[Column19] [nvarchar](max) NULL DEFAULT (NULL),
	[Column20] [nvarchar](max) NULL DEFAULT (NULL),
	[Column21] [nvarchar](max) NULL DEFAULT (NULL),
	[Column22] [nvarchar](max) NULL DEFAULT (NULL),
	[Column23] [nvarchar](max) NULL DEFAULT (NULL),
	[Column24] [nvarchar](max) NULL DEFAULT (NULL),
	[Column25] [nvarchar](max) NULL DEFAULT (NULL),
	[Column26] [nvarchar](max) NULL DEFAULT (NULL),
	[Column27] [nvarchar](max) NULL DEFAULT (NULL),
	[Column28] [nvarchar](max) NULL DEFAULT (NULL),
	[Column29] [nvarchar](max) NULL DEFAULT (NULL),
	[Column30] [nvarchar](max) NULL DEFAULT (NULL),
	[Column31] [nvarchar](max) NULL DEFAULT (NULL),
	[LineNumber] [int] NULL
)
GO
IF COL_LENGTH('dbo.tbCustomerRequirement', 'CustomerRefNo') IS NULL
BEGIN
   ALTER TABLE dbo.tbCustomerRequirement ADD CustomerRefNo NVARCHAR(200) NULL
END
GO