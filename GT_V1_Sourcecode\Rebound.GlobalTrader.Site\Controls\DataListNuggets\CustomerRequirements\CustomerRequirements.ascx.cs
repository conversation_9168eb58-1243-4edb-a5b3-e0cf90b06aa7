using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
    public partial class CustomerRequirements : Base {
        private Boolean _IsIPOHUB = false;
        public Boolean IsIPOHUB
        {
            get { return _IsIPOHUB; }
            set { _IsIPOHUB = value; }
        }
        private bool _IsGSA = false;
        public bool IsGSA
        {
            get { return _IsGSA; }
            set { _IsGSA = value; }
        }
        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            SetDataListNuggetType("CustomerRequirements");
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("Nuggets", "CustomerRequirements");
            AddScriptReference("Controls.DataListNuggets.CustomerRequirements.CustomerRequirements.js");
            _IsIPOHUB = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["IsIPOHUB"]);
            SetupTable();
        }

        protected override void OnLoad(EventArgs e) {
            IsGSA = SessionManager.IsGSA.Value;
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirements", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intSalesPersonID", _objQSManager.SalesPersonID > 0 ? _objQSManager.SalesPersonID : 0);
            _scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
            base.OnLoad(e);
        }

        protected override void RenderAdditionalState() {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            if (!string.IsNullOrEmpty(strViewLevel)) {
                ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
                _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
                this.OnAskPageToChangeTab();
            }
            base.RenderAdditionalState();
        }

        #endregion

        private void SetupTable() {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", "Contact" ));
            //<% --Code start[001]-- %>
            _tbl.Columns.Add(new FlexiDataColumn("Salesman", "Industrytype", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
            //<% --Code End[001]-- %>
            _tbl.Columns.Add(new FlexiDataColumn("ReceivedDate", "DatePromised", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            //_tbl.Columns.Add(new FlexiDataColumn("IPOBOM", "REQStatus", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("IPOBOM", "HUBSTATUS", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            _tbl.Columns.Add(new FlexiDataColumn("TotalValue","TotalInBase", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
           // if (_IsIPOHUB) 
        }

    }
}