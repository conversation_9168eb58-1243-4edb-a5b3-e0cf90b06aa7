Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.initializeBase(this,[n]);this._intPOQuoteID=-1;this._intBOMID=-1};Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_tblCreditHistory:function(){return this._tblCreditHistory},set_tblCreditHistory:function(n){this._tblCreditHistory!==n&&(this._tblCreditHistory=n)},get_intPOQuoteID:function(){return this._intPOQuoteID},set_intPOQuoteID:function(n){this._intPOQuoteID!==n&&(this._intPOQuoteID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/ExpediteHistory";this._strDataObject="ExpediteHistory";this.addRefreshEvent(Function.createDelegate(this,this.GetExpediteHistory));this.showLoading(!1);this.showContent(!0);this.showContentLoading(!1);this.GetExpediteHistory()},dispose:function(){this.isDisposed||(this._tblCreditHistory&&this._tblCreditHistory.dispose(),this._tblCreditHistory=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.callBaseMethod(this,"dispose"))},GetExpediteHistory:function(){this.showContent(!0);this.showCreditHistoryError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetExpediteHistory");n.addParameter("ID",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getCreditHistoryOK));n.addError(Function.createDelegate(this,this.getCreditHistoryError));n.addTimeout(Function.createDelegate(this,this.getCreditHistoryError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCreditHistoryOK:function(n){res=n._result;this.showLoadingCreditHistory(!1);this.showCreditHistoryError(!1);this._tblCreditHistory.clearTable();this.processExpediteHistory(this._tblCreditHistory);this._tblCreditHistory.resizeColumns()},getCreditHistoryError:function(n){this.showLoadingCreditHistory(!1);this.showCreditHistoryError(!0,n.get_ErrorMessage())},showLoadingCreditHistory:function(n){$R_FN.showElement(this._pnlLoadingCreditHistory,n);$R_FN.showElement(this._pnlCreditHistory,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetCreditHistory,!1)},showCreditHistoryError:function(n,t){$R_FN.showElement(this._pnlCreditHistoryError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlCreditHistory,!1),$R_FN.showElement(this._pnlGetCreditHistory,!1),$R_FN.setInnerHTML(this._pnlCreditHistoryError,t))},showCreditHistoryGetData:function(n){$R_FN.showElement(this._pnlGetCreditHistory,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingCreditHistory,!1),$R_FN.showElement(this._pnlCreditHistory,!1),$R_FN.setInnerHTML(this._pnlCreditHistoryError,!1))},processExpediteHistory:function(n){var i,t,r;if(res.ExpHist)for(i=0;i<res.ExpHist.length;i++)t=res.ExpHist[i],r=[$R_FN.setCleanTextValue(t.Note),t.ReqNos,t.Date,t.EmployeeName,t.To,t.CCUserID,t.SendToGroup],n.addRow(r,t.ID,!1),t=null,r=null}};Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ExpediteHistory",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);