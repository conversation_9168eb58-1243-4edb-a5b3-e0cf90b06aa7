﻿using System;

namespace Rebound.GlobalTrader.DAL
{
    public class ProspectiveOffersLogs
    {
        public int Id { get; set; }
        public int ProspectiveOfferLineId { get; set; }
        public string PartNo { get; set; }
        public string HUBRFQCustomer { get ; set; }
        public int Quantity { get; set; }
        public double Price { get; set; }
        public string Currency { get; set; }
        public double? IHSAvgPrice { get; set; }
        public double? LyticaAvgPrice { get; set; }
        public DateTime ReceivedDate { get; set; }
        public string Manufacturer { get; set; }
        public string SalesPerson { get; set; }

        public int CustomerReqId {  get; set; }
        public string BOMNo { get; set; }
        public int BOMId { get; set; }
        public string CustomerPartNo { get; set; }
        public string DateCode { get; set; }
        public string ProductName { get; set; }
        public string PackageName { get; set; }
        public DateTime SentDate { get; set; }
        public int HUBRFQCreatedId { get; set; }
        public string HUBRFQCreatedNo { get; set; }
        public double? NewOfferPriceFromProspective { get; set; }
    }
}
