Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplier=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplier.initializeBase(this,[n]);this._aryRecipientLoginIDs=[];this._aryRecipientLoginNames=[];this._aryRecipientGroupIDs=[];this._aryRecipientGroupNames=[];this._intNumberRecipients=0;this._aryRecipientEmail=[];this._aryCompanyIDs=[];this._suppliersIdEmail=[]};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplier.prototype={get_pnlSelected:function(){return this._pnlSelected},set_pnlSelected:function(n){this._pnlSelected!==n&&(this._pnlSelected=n)},get_lblSelected:function(){return this._lblSelected},set_lblSelected:function(n){this._lblSelected!==n&&(this._lblSelected=n)},get_autLoginOrGroup:function(){return this._autLoginOrGroup},set_autLoginOrGroup:function(n){this._autLoginOrGroup!==n&&(this._autLoginOrGroup=n)},initialize:function(){this._autLoginOrGroup.addSelectionMadeEvent(Function.createDelegate(this,this.newRecipientSelected));Array.clear(this._aryRecipientLoginIDs);Array.clear(this._aryRecipientLoginNames);Array.clear(this._aryRecipientGroupIDs);Array.clear(this._aryRecipientGroupNames);Array.clear(this._aryCompanyIDs);Array.clear(this._aryRecipientEmail);this.showSelected();this.newRecipient();Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplier.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._autLoginOrGroup&&this._autLoginOrGroup.dispose(),this._pnlSelected=null,this._lblSelected=null,this._autLoginOrGroup=null,this._aryRecipientLoginIDs=null,this._aryRecipientLoginNames=null,this._intNumberRecipients=null,this._aryRecipientEmail=null,this._aryCompanyIDs=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplier.callBaseMethod(this,"dispose"))},newRecipientSelected:function(){this.addNewLoginRecipient(this._autLoginOrGroup._varSelectedID,this._autLoginOrGroup._varSelectedValue,this._autLoginOrGroup._varSelectedExtraData)},newRecipient:function(){this.addNewLoginSupplier(this._autLoginOrGroup._varSelectedID,this._autLoginOrGroup._varSelectedValue,this._autLoginOrGroup._varSelectedExtraData)},addNewLoginSupplier:function(){},addNewLoginRecipient:function(n,t,i){this.addKeyValueArray(n,i,t);this._intNumberRecipients+=1;this.showSelected();this._autLoginOrGroup.reselect()},showSelected:function(){$R_FN.showElement(this._pnlSelected,this._intNumberRecipients>0);var t="",n="";for(i=0,l=this._suppliersIdEmail.length;i<l;i++)n+='<div class="mailRecipient">',n+=this._suppliersIdEmail[i].supname+"&nbsp;&nbsp;&nbsp&nbsp;&nbsp;&nbsp",n+=String.format(" <input id=Email"+i+" type=\"text\"  style=\"width: 250px;\" onchange=\"$find('{0}').addLoginRecipient({1},{2},'{3}','{4}');\"  value="+this._suppliersIdEmail[i].email+" >",this._element.id,i,this._suppliersIdEmail[i].cmpId,this._suppliersIdEmail[i].email,this._suppliersIdEmail[i].supname),t=String.format("$find('{0}').removeLoginRecipient({1});",this._element.id,this._suppliersIdEmail[i].cmpId),n+=String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]<\/a>',t),n+="<\/div>";$R_FN.setInnerHTML(this._lblSelected,n)},addLoginRecipient:function(n,t,i,r){this.removeKeyValueArray(t);this.addKeyValueArray(t,document.getElementById("Email"+n).value,r);this._intNumberRecipients+=1},removeLoginRecipient:function(n){this._intNumberRecipients-=1;this.removeKeyValueArray(n);this.showSelected()},removeKeyValueArray:function(n){for(var t=0;t<this._suppliersIdEmail.length;++t)if(this._suppliersIdEmail[t].cmpId==n){delete this._suppliersIdEmail.splice(t,1);return}},addKeyValueArray:function(n,t,i){for(var u=!0,r=0;r<this._suppliersIdEmail.length;++r)if(this._suppliersIdEmail[r].cmpId==n){u=!1;break}u&&this._suppliersIdEmail.push({cmpId:n,email:t,supname:i})},setKeyValueArray:function(){Array.clear(this._aryRecipientEmail);Array.clear(this._aryCompanyIDs);Array.clear(this._aryRecipientLoginNames);for(var n=0;n<this._suppliersIdEmail.length;++n)Array.add(this._aryRecipientEmail,this._suppliersIdEmail[n].email),Array.add(this._aryCompanyIDs,this._suppliersIdEmail[n].cmpId),Array.add(this._aryRecipientLoginNames,this._suppliersIdEmail[n].supname)},setValue_Subject:function(n){this._ctlRelatedForm.setFieldValue("ctlSubject",n)},getValue_Subject:function(){return this._ctlRelatedForm.getFieldValue("ctlSubject")},setValue_Body:function(n){this._ctlRelatedForm.setFieldValue("ctlBody",n)},getValue_Body:function(){return this._ctlRelatedForm.getFieldValue("ctlBody")},validateFields:function(){var n=!0;return this._ctlRelatedForm.resetFormFields(),this._ctlRelatedForm.checkFieldEntered("ctlBody")||(n=!1),this._ctlRelatedForm.checkFieldEntered("ctlSubject")||(n=!1),this._intNumberRecipients==0&&(this._ctlRelatedForm.setFieldInError("ctlTo",!0,$R_RES.RequiredFieldMissingMessage),n=!1),n},resetFields:function(){this.setValue_Subject("");this.setValue_Body("");Array.clear(this._aryRecipientLoginIDs);Array.clear(this._aryRecipientLoginNames);Array.clear(this._aryRecipientEmail);this._intNumberRecipients=0;this.showSelected()}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplier.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplier",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);