using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class AllPurchaseOrdersDueIn : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                List<PurchaseOrder> lstDueIn = PurchaseOrder.GetListDueForClient(SessionManager.ClientID, RowCount, Convert.ToBoolean(SessionManager.IsPOHub));
                if (lstDueIn == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    //due in
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Count", lstDueIn.Count);
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstDueIn.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstDueIn[i].PurchaseOrderId);
                        jsnItem.AddVariable("No", lstDueIn[i].PurchaseOrderNumber);
                        jsnItem.AddVariable("Due", Functions.FormatDate(lstDueIn[i].DeliveryDate));
                        jsnItem.AddVariable("CM", lstDueIn[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstDueIn[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("DueInPO", jsnItems);
                    jsnItems.Dispose(); jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                lstDueIn = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


	}
}
