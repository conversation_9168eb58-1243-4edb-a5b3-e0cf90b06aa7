///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - add full disposing and raise events on enable / disable field
//
// RP 30.12.2009:
// - parse boolean for enabling field
//
// RP 16.10.2009:
// - allow setting default for field
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.prototype = {

	get_lblField: function() { return this._lblField; }, set_lblField: function(value) { if (this._lblField !== value)  this._lblField = value; }, 
	get_chkOn: function() { return this._chkOn; }, set_chkOn: function(value) { if (this._chkOn !== value)  this._chkOn = value; }, 
	get_strFilterField: function() { return this._strFilterField; }, set_strFilterField: function(value) { if (this._strFilterField !== value)  this._strFilterField = value; }, 
	get_enmFieldType: function() { return this._enmFieldType; }, set_enmFieldType: function(value) { if (this._enmFieldType !== value)  this._enmFieldType = value; }, 
	get_strDefaultValue: function() { return this._strDefaultValue; }, set_strDefaultValue: function(value) { if (this._strDefaultValue !== value)  this._strDefaultValue = value; }, 

	addFieldEnabledEvent: function(handler) { this.get_events().addHandler("FieldEnabled", handler); },
	removeFieldEnabledEvent: function(handler) { this.get_events().removeHandler("FieldEnabled", handler); },
	onFieldEnabled: function() {
		var handler = this.get_events().getHandler("FieldEnabled");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addFieldDisabledEvent: function(handler) { this.get_events().addHandler("FieldDisabled", handler); },
	removeFieldDisabledEvent: function(handler) { this.get_events().removeHandler("FieldDisabled", handler); },
	onFieldDisabled: function() {
		var handler = this.get_events().getHandler("FieldDisabled");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	initialize: function() {
		$addHandler(this._chkOn, "click", Function.createDelegate(this, this.toggleEnableField));
		this._blnOn = this._chkOn.checked;
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._chkOn) $clearHandlers(this._chkOn);
		this._chkOn = null;
		this._lblField = null;
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	toggleEnableField: function() {
		this._blnOn = !this._blnOn;
		this.enableField(this._blnOn);
	},
	
	explicitEnableField: function() {
		enableField(true);
	},
	
	enableField: function(blnEnable) {
		this._blnOn = Boolean.parse(blnEnable.toString());
		if (this._blnOn) {
			if (!this._chkOn.checked) this._chkOn.checked = true;
			Sys.UI.DomElement.removeCssClass(this._lblField, "filterDisabled");
			this.onFieldEnabled();
		} else {
			if (this._chkOn.checked) this._chkOn.checked = false;
			Sys.UI.DomElement.addCssClass(this._lblField, "filterDisabled");
			this.onFieldDisabled();
		}
	},
	
	show: function(bln) {
		$R_FN.showElement(this._element, bln);
	},
	
	resetToDefault: function() {
		this.setValue(this._strDefaultValue);
	}
	
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base", Sys.UI.Control, Sys.IDisposable);
