﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
==============================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-246240]     Phuc Hoang		 15-May-2025		CREATE		Invoice - Line from 'Authorised SO Service Line' source function on Client/ DMCC side (Part 2)
==============================================================================================================================================================  
*/

CREATE OR ALTER PROC [dbo].[usp_Get_SalesOrderByInvoice] (
	@InvoiceId INT = NULL,
	@ClientNo INT = NULL
)
AS

BEGIN
	SELECT DISTINCT so.SalesOrderId, so.SalesOrderNumber
	FROM [dbo].[tbInvoice] inv
		LEFT JOIN [dbo].[tbInvoiceLine] inl ON inl.InvoiceNo = inv.InvoiceId
		LEFT JOIN [dbo].[tbSalesOrderLine] sol ON sol.SalesOrderLineId = inl.SalesOrderLineNo
		LEFT JOIN [dbo].[tbSalesOrder] so ON so.SalesOrderId = sol.SalesOrderNo
		LEFT JOIN [dbo].[tbSalesOrderLine] solService ON so.SalesOrderId = solService.SalesOrderNo 
	WHERE inv.InvoiceId = @InvoiceId AND inv.ClientNo = @ClientNo
		--AND sol.Closed IN (0, @IncludeClosed) 
		AND ISNULL(so.AuthorisedBy, 0) > 0 AND so.DateAuthorised IS NOT NULL 
		AND ISNULL(solService.ServiceNo, 0) > 0
END

GO
