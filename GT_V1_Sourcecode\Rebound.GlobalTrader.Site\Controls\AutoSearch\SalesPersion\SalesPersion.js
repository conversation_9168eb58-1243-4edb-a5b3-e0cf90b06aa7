Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.SalesPersion=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.SalesPersion.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.SalesPersion.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.SalesPersion.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("SalesPersion")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.AutoSearch.SalesPersion.callBaseMethod(this,"dispose")},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",i=this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.Navigate?$RGT_nubButton_Company(n.ID,n.Name):$R_FN.setCleanTextValue(n.Name),this.addResultItem(i,$R_FN.setCleanTextValue(n.Name),n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.SalesPersion.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.SalesPersion",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);