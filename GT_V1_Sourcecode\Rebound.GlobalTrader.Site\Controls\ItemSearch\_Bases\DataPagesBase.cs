using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Text;
using System.Web.Script.Serialization;
using System.Web.SessionState;
using System.Globalization;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls.DataListNuggets;
using Rebound.GlobalTrader.Site.Enumerations;


namespace Rebound.GlobalTrader.Site.Data.ItemSearch {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Base : Rebound.GlobalTrader.Site.Data.Base {

		protected Rebound.GlobalTrader.Site.ItemSearch _objItemSearch;

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				_objItemSearch = _objSite.GetItemSearch(GetFormValue_Int("ISID"));
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		protected virtual void GetData() { }
	}
}