﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202689]		Trung Pham			11-SEP-2024		CREATE			Inactivate EpoImportactivity and tbEpo record
===========================================================================================
*/
CREATE OR ALTER PROCEDURE usp_Inactivate_EpoImportActivity
	@ClientId INT =0,
	@SelectedImportId INT=0,
	@InactiveBy INT,
	@IsSuccess BIT OUTPUT
AS
BEGIN
	IF (NOT EXISTS (SELECT 1 FROM tbSourcingResult a INNER JOIN BorisGlobalTraderImports..tbEpo b ON a.SourcingTableItemNo = b.EpoId
		WHERE SourcingTable = 'EPPH' AND b.ImportId = @SelectedImportId))
	BEGIN
		UPDATE BorisGlobalTraderimports.dbo.tbImportActivity_Epo SET Inactive = 1, InactiveBy = @InactiveBy, InactiveDate = GETDATE() WHERE ImportId = @SelectedImportId
		UPDATE BorisGlobalTraderimports.dbo.tbEpo SET Inactive = 1, InactiveBy = @InactiveBy, InactiveDate = GETDATE() WHERE ImportId = @SelectedImportId
		SET @IsSuccess = @@ROWCOUNT;
	END
	ELSE
	BEGIN
		SET @IsSuccess = 0;
	END
	SELECT @IsSuccess
END
GO