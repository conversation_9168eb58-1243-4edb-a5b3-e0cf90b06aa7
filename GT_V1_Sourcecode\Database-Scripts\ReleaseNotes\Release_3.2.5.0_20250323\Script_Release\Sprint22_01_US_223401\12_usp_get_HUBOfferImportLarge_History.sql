﻿
GO
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*
==============================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     An.TranTan		 19-Feb-2025		CREATE		Get history, except deleted record status
[US-223401]     An.TranTan		 19-Feb-2025		UPDATE		Get generated fime name in blob storage
==============================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_get_HUBOfferImportLarge_History] 
	@DisplayLength INT = 0
	,@DisplayStart INT = 0
	,@SortCol INT = 0
	,@SortDir NVARCHAR(10) = null
AS
BEGIN
	DECLARE @FirstRec INT, @LastRec INT;

	SET @FirstRec = @DisplayStart;
	SET @LastRec = @DisplayStart + @DisplayLength;

	WITH cteHUBLargeImportHistory
	AS (
		SELECT
			i.ID
			,ROW_NUMBER() OVER(ORDER BY i.DLUP DESC) AS RowNum
			,COUNT(*) OVER () AS TotalCount
			,i.DLUP AS ImportedDate
			,i.OriginalFileName
			,CONCAT(l.FirstName, ' ', l.LastName) AS ImportedBy
			,ISNULL(i.UploadedRowCount,0) AS RowsCount
			,'HUB' AS ClientType
			,i.[status] AS Status
			,i.GeneratedFileName
		FROM BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile i WITH(NOLOCK)
		JOIN tbLogin l WITH(NOLOCK) ON l.LoginId = i.UpdatedBy
		WHERE i.status <> 'Deleted'
	)
	SELECT
		ID AS HUBOfferImportLargeID
		,RowNum
		,TotalCount
		,CONVERT(VARCHAR, ImportedDate, 9) AS ImportedDate
		,OriginalFileName
		,ImportedBy
		,RowsCount
		,ClientType
		,Status
		,GeneratedFileName
	FROM cteHUBLargeImportHistory
	WHERE RowNum > @FirstRec
		AND RowNum <= @LastRec
	ORDER BY CAST(ImportedDate as DATETIME) DESC
END
GO


