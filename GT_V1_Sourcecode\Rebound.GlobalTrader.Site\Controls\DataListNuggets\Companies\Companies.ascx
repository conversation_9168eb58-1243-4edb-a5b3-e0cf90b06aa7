<%-- Marker     changed by      date         Remarks  
     [001]      <PERSON><PERSON><PERSON>     13-Sep-2018  [REB-12820]:Provision to add Global Security on Contact Section --%>
<%@ Control Language="C#" CodeBehind="Companies.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<script src="js/ToDoList.js"></script>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlName" runat="server" ResourceTitle="Name" FilterField="Name" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlType" runat="server" ResourceTitle="Type" FilterField="Type" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCity" runat="server" ResourceTitle="City" FilterField="City" />
				<%--<ReboundUI_FilterDataItemRow:TextBox id="ctlCountry" runat="server" ResourceTitle="Country" FilterField="Country" />--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctllstCountry" runat="server"  ResourceTitle="Country" DropDownType="Country" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="CountryNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlTel" runat="server" ResourceTitle="Tel" FilterField="TelNo" />
                <ReboundUI_FilterDataItemRow:TextBox id="ctlState" runat="server" ResourceTitle="State" FilterField="State" />
                <ReboundUI_FilterDataItemRow:TextBox id="ctlCounty" runat="server" ResourceTitle="County" FilterField="County" />
				
				<ReboundUI_FilterDataItemRow:TextBox id="ctlRelatedManufac" runat="server" ResourceTitle="ManufacturerSupplied" FilterField="MFR" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlGroupCodeName" runat="server" ResourceTitle="GroupCodeName" FilterField="GroupCodeName" />
				<ReboundUI_FilterDataItemRow:DropDown ID="ctlCompSupType" runat="server" DropDownAssembly="Rebound.GlobalTrader.Site" DropDownType="CompanySupType" ResourceTitle="CompanyType" FilterField="CompanyType" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlVATIDs" runat="server" ResourceTitle="VATFilter" FilterField="VATIDs" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlInsuranceCertificateNo" runat="server" ResourceTitle="InsuranceCertificateNo" FilterField="InsuranceCertificateNo" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlCertificateCategoryNo" runat="server" DropDownType="CertificateCategory" ResourceTitle="CertificateCategory" FilterField="CertificateCategoryNo" DropDownAssembly="Rebound.GlobalTrader.Site" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesperson" runat="server" DropDownType="Employee" ResourceTitle="Salesperson" FilterField="Salesman" DropDownAssembly="Rebound.GlobalTrader.Site" />
				<ReboundUI_FilterDataItemRow:StarRating id="ctlSupplierRating" runat="server" ResourceTitle="SupplierRating" FilterField="SupplierRating" />
				<ReboundUI_FilterDataItemRow:StarRating id="ctlCustomerRating" runat="server" ResourceTitle="CustomerRating" FilterField="CustomerRating" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerNo" runat="server" ResourceTitle="CustomerNo" FilterField="CustomerNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlZipcode" runat="server" ResourceTitle="Zipcode" FilterField="Zip" />
                <%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
				<%--[001]Code End--%>
                <ReboundUI_FilterDataItemRow:DropDown id="ctlRegion" runat="server" DropDownType="Region" ResourceTitle="Region" FilterField="Region" DropDownAssembly="Rebound.GlobalTrader.Site" />
                <ReboundUI_FilterDataItemRow:TextBox id="ctlEmail" runat="server" ResourceTitle="Email" FilterField="Email" />
                <ReboundUI_FilterDataItemRow:TextBox id="ctlIndustryType" runat="server" ResourceTitle="IndustryType" FilterField="IndustryType" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlCompanyStatus" runat="server" DropDownType="CompanyStatus" ResourceTitle="Status" FilterField="CompanyStatus" DropDownAssembly="Rebound.GlobalTrader.Site" DefaultValue="2"/>
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
     
<style>
    #ctl00_cphMain_ctlCompanies_ctlDB_tbl_tbl tr td:nth-child(2){width:88px!important; text-align:center;}
    #ctl00_cphMain_ctlCompanies_ctlDB_pnlFormLinks0{
        margin-top: 10px;
    }
    
</style>

	</Filters>
      <Forms>
		<ReboundForm:MailMessages_MarkAsToDo id="ctlAdd" runat="server" />
	</Forms>
     
      
</ReboundUI_Nugget:DesignBase>
