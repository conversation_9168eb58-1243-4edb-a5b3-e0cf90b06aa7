<%@ Control Language="C#" CodeBehind="DebitLines_Delete.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Delete" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "DebitLines_Delete")%></Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
		
			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="lblPartNo" ResourceTitle="PartNo">
				<Field><asp:Label ID="lblPartNo" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlService" runat="server" FieldID="lblService" ResourceTitle="Service">
				<Field><asp:Label ID="lblService" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlServiceDescription" runat="server" FieldID="lblServiceDescription" ResourceTitle="Description">
				<Field><asp:Label ID="lblServiceDescription" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlManufacturer" runat="server" FieldID="lblManufacturer" ResourceTitle="Manufacturer">
				<Field><asp:Label ID="lblManufacturer" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlDateCode" runat="server" FieldID="lblDateCode" ResourceTitle="DateCode">
				<Field><asp:Label ID="lblDateCode" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="lblProduct" ResourceTitle="Product">
				<Field><asp:Label ID="lblProduct" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="lblPackage" ResourceTitle="Package">
				<Field><asp:Label ID="lblPackage" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
