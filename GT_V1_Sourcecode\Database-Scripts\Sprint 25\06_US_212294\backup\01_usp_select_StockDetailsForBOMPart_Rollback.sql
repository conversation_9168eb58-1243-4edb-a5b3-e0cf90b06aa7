﻿  
/*   
===========================================================================================  
TASK        UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-201581]  An.TranTan   29-May-2024  CREATE   Get stock details for KUB Assistant in BOM Manager Details (Client Side)  
[BUG-205015] An.TranTan   04-Jun-2024  UPDATE   Set ClientID = 0 if client is POHub  
[US-201581]  An.TranTan   07-Jun-2024  UPDATE   Make sure each result set return at least 1 record in order to Sql<PERSON><PERSON><PERSON><PERSON><PERSON> read data correctly  
[US-201581]  An.TranTan   11-Jun-2024  UPDATE   Correct convert price value logic:  
                 - Convert original stock price => client base currency => BOM currency  
[US-201581]  An.TranTan   13-Jun-2024  UPDATE   Prefer use uplift price of RL and strategic stock  
[US-205444]  An.TranTan   14-Jun-2024  UPDATE   Using local currency to convert stock value  
[US-207699]  An.TranTan   28-Jun-2024  UPDATE   Get stock for HUBRFQ Part  
===========================================================================================  
*/  
  
CREATE OR ALTER PROCEDURE [dbo].[usp_select_StockDetailsForBOMPart]  
    @PartNumber NVARCHAR(50) = NULL,  
    @ClientID INT = 0,  
    @BOMID INT = 0,  
 @IsHUBRFQ BIT = 0  
AS  
BEGIN  
    DECLARE @BOMCurrencyNo INT = NULL;  
    DECLARE @BOMCurrencyCode NVARCHAR(5) = NULL;  
 DECLARE @NotfoundMessage NVARCHAR(40) = N'NOT FOUND';  
  
 --Final result tables  
 IF OBJECT_ID('tempdb..#finalRLStock') IS NOT NULL  
        DROP TABLE #finalRLStock  
    CREATE TABLE #finalRLStock  
    (  
        DateAdded NVARCHAR(40),  
  Quantity INT,  
        Price NVARCHAR(MAX),  
        Manufacturer NVARCHAR(200)  
    )  
  
 IF OBJECT_ID('tempdb..#finalStrategicStock') IS NOT NULL  
        DROP TABLE #finalStrategicStock  
    CREATE TABLE #finalStrategicStock  
    (  
        DateAdded NVARCHAR(40),  
  Quantity INT,  
        Price NVARCHAR(MAX),  
        Manufacturer NVARCHAR(200)  
    )  
  
 IF OBJECT_ID('tempdb..#finalStock') IS NOT NULL  
        DROP TABLE #finalStock  
    CREATE TABLE #finalStock  
    (  
        StockId NVARCHAR(MAX),  
  Quantity INT,  
        Price NVARCHAR(MAX),  
        Client NVARCHAR(200)  
    )  
 -----------END-----------  
  
 --Get all data if client is POHub  
 SET @ClientID = CASE WHEN @ClientID = 114 THEN 0   
      ELSE ISNULL(@ClientID, 0)   
     END;  
  
 --Get local currency for client  
 IF OBJECT_ID('tempdb..#tempLocalCurrency') IS NOT NULL  
        DROP TABLE #tempLocalCurrency  
    CREATE TABLE #tempLocalCurrency  
    (  
        LocalCurrencyId INT,  
  LocalCurrencyCode NVARCHAR(5)  
    )  
  
 ;WITH cteLocalCurrency AS (  
  SELECT lc.LocalCurrencyId     
        ,gcu.GlobalCurrencyName As LocalCurrencyCode  
  FROM tbLocalCurrency lc WITH(NOLOCK)          
  INNER JOIN tbGlobalCurrencyList gcu WITH(NOLOCK) On lc.GlobalCurrencyNo = gcu.GlobalCurrencyId        
  WHERE lc.ClientID = @ClientID  
  UNION ALL  
  SELECT cl.LocalCurrencyNo As LocalCurrencyId,  
      gcu.GlobalCurrencyName AS LocalCurrencyCode    
  FROM tbClient cl WITH(NOLOCK)    
  JOIN tbGlobalCurrencyList gcu WITH(NOLOCK) on cl.LocalCurrencyNo = gcu.GlobalCurrencyId        
  WHERE cl.ClientId = @ClientID   
 )  
 INSERT INTO #tempLocalCurrency (LocalCurrencyId, LocalCurrencyCode)  
 SELECT LocalCurrencyId, LocalCurrencyCode FROM cteLocalCurrency  
 --------------------END--------------------  
   
 --Get local currency for BOM Manager/HUBRFQ currency  
 IF(@IsHUBRFQ = 1)  
 BEGIN  
  SELECT @BOMCurrencyNo = tlc.LocalCurrencyId,  
         @BOMCurrencyCode = c.CurrencyCode  
  FROM dbo.tbBOM b WITH (NOLOCK)  
      JOIN dbo.tbCurrency c WITH (NOLOCK)  
          ON c.CurrencyId = b.CurrencyNo  
   LEFT JOIN #tempLocalCurrency tlc ON c.CurrencyCode = tlc.LocalCurrencyCode  
  WHERE b.BOMId = @BOMID;  
 END  
 ELSE BEGIN  
  SELECT @BOMCurrencyNo = tlc.LocalCurrencyId,  
         @BOMCurrencyCode = c.CurrencyCode  
  FROM dbo.tbBOMManager b WITH (NOLOCK)  
      JOIN dbo.tbCurrency c WITH (NOLOCK)  
          ON c.CurrencyId = b.CurrencyNo  
   LEFT JOIN #tempLocalCurrency tlc ON c.CurrencyCode = tlc.LocalCurrencyCode  
  WHERE b.BOMManagerId = @BOMID;  
 END  
 --------------------END--------------------  
  
    --Get Reverse Logistics 10 most recent offers for selected part, refer usp_ipobom_source_ReverseLogistic  
    ;With cteTop10RLOffers  
    AS (SELECT ROW_NUMBER() OVER (ORDER BY o.DLUP DESC) as rn,  
               o.DLUP,  
               o.Quantity,  
               ISNULL(o.UpliftPrice, o.Price) AS Price,  
               c.CurrencyCode AS OfferCurrencyCode,  
      CASE WHEN @BOMCurrencyNo IS NULL THEN NULL --BOM currency not exist in local currency  
     WHEN tlc.LocalCurrencyId IS NULL THEN NULL --Offer currency not exist in local currency  
     ELSE dbo.ufn_convert_to_local_currency(ISNULL(o.UpliftPrice, o.Price), tlc.LocalCurrencyId, @BOMCurrencyNo, o.DLUP, @ClientID)  
      END AS BomPrice,  
      @BOMCurrencyCode AS BomCurrencyCode,  
               m.ManufacturerName  
        FROM [BorisGlobalTraderImports].dbo.tbReverseLogistic o WITH (NOLOCK)  
            JOIN tbClient cl WITH (NOLOCK)  
                ON o.ClientNo = cl.ClientId  
            JOIN dbo.tbCurrency c WITH (NOLOCK)  
                ON o.CurrencyNo = c.CurrencyId  
            LEFT JOIN dbo.tbManufacturer m WITH (NOLOCK)  
                ON o.ManufacturerNo = m.ManufacturerId  
   LEFT JOIN #tempLocalCurrency tlc ON tlc.LocalCurrencyCode = c.CurrencyCode  
        WHERE (  
                  (o.ClientNo = @ClientId)  
                  OR (  
                         o.ClientNo <> @ClientId  
                         AND (case  
                                  when o.ClientNo = 114 then  
                                      cast(1 as bit)  
                                  else  
                                      cl.OwnDataVisibleToOthers  
                              end  
                             ) = 1  
                     )  
              )  
              AND o.FullPart = @PartNumber  
              AND ISNULL(o.InActive, 0) = 0  
              AND o.Quantity > 0  
       )  
 INSERT INTO #finalRLStock  
 (  
  DateAdded,  
  Quantity,  
  Price,  
  Manufacturer  
 )  
    SELECT CAST(FORMAT(DLUP, 'dd-MM-yyyy') AS NVARCHAR(40)) as DateAdded,  
           Quantity,  
     CASE WHEN BomPrice IS NOT NULL THEN  
     CAST(CONVERT(DECIMAL(16, 5), BomPrice) AS NVARCHAR(100)) + ' ' + BomCurrencyCode + ' ('  
     + CAST(CONVERT(DECIMAL(16, 5), Price) AS NVARCHAR(100)) + ' ' + OfferCurrencyCode + ')'  
    ELSE   
     CAST(CONVERT(DECIMAL(16, 5), Price) AS NVARCHAR(100)) + ' ' + OfferCurrencyCode  
     END AS Price,  
           ManufacturerName AS Manufacturer  
    FROM cteTop10RLOffers  
    WHERE rn <= 10;  
  
 --Make sure #finalRLStock has at least 1 record  
 IF(NOT EXISTS(SELECT 1 FROM #finalRLStock))  
 BEGIN  
  INSERT INTO #finalRLStock  
  (  
   DateAdded,  
   Quantity,  
   Price,  
   Manufacturer  
  )VALUES (@NotfoundMessage, 0, '', '')  
 END  
  
    --Get strategic Stock 10 most recent offers for selected part, refer [usp_ipobom_source_Epo]  
    ;WITH cteTop10StrategicStockOffers   
 AS (SELECT ROW_NUMBER() OVER (ORDER BY o.DLUP DESC) as rn,  
               o.DLUP,  
               o.Quantity,  
               ISNULL(o.UpliftPrice, o.Price) AS Price,  
               c.CurrencyCode AS OfferCurrencyCode,  
      CASE WHEN @BOMCurrencyNo IS NULL THEN NULL --BOM currency not exist in local currency  
     WHEN tlc.LocalCurrencyId IS NULL THEN NULL --Offer currency not exist in local currency  
     ELSE dbo.ufn_convert_to_local_currency(ISNULL(o.UpliftPrice, o.Price), tlc.LocalCurrencyId, @BOMCurrencyNo, o.DLUP, @ClientID)  
      END AS BomPrice,  
      @BOMCurrencyCode AS BomCurrencyCode,  
               m.ManufacturerName  
        FROM [BorisGlobalTraderImports].dbo.tbEpo o WITH (NOLOCK)  
            JOIN tbClient cl WITH (NOLOCK)  
                ON o.ClientNo = cl.ClientId  
            JOIN tbCurrency c WITH (NOLOCK)  
                ON o.CurrencyNo = c.CurrencyId  
            LEFT JOIN dbo.tbManufacturer m WITH (NOLOCK)  
        ON o.ManufacturerNo = m.ManufacturerId  
   LEFT JOIN #tempLocalCurrency tlc ON tlc.LocalCurrencyCode = c.CurrencyCode  
        WHERE (  
                  (o.ClientNo = @ClientId)  
                  OR (  
                         o.ClientNo <> @ClientId  
                         AND (case  
                                  when o.ClientNo = 114 then  
                                      cast(1 as bit)  
                                  else  
                                      cl.OwnDataVisibleToOthers  
                              end  
                             ) = 1  
                     )  
              )  
              AND o.FullPart = @PartNumber  
       )  
 INSERT INTO #finalStrategicStock  
 (  
  DateAdded,  
  Quantity,  
  Price,  
  Manufacturer  
 )  
    SELECT CAST(FORMAT(DLUP, 'dd-MM-yyyy') AS NVARCHAR(40)) as DateAdded,  
           Quantity,  
     CASE WHEN BomPrice IS NOT NULL THEN  
     CAST(CONVERT(DECIMAL(16, 5), BomPrice) AS NVARCHAR(100)) + ' ' + BomCurrencyCode + ' ('  
     + CAST(CONVERT(DECIMAL(16, 5), Price) AS NVARCHAR(100)) + ' ' + OfferCurrencyCode + ')'  
    ELSE   
     CAST(CONVERT(DECIMAL(16, 5), Price) AS NVARCHAR(100)) + ' ' + OfferCurrencyCode  
     END AS Price,  
           ManufacturerName AS Manufacturer  
    FROM cteTop10StrategicStockOffers  
    WHERE rn <= 10;  
  
 --Make sure #finalStrategicStock has at least 1 record  
 IF(NOT EXISTS(SELECT 1 FROM #finalStrategicStock))  
 BEGIN  
  INSERT INTO #finalStrategicStock  
  (  
   DateAdded,  
   Quantity,  
   Price,  
   Manufacturer  
  )VALUES (@NotfoundMessage, 0, '', '')  
 END  
  
    --Get 20 most recent stock for selected part but not limit to any client, refer usp_source_Stock  
    IF OBJECT_ID('tempdb..#tempStock') IS NOT NULL  
        DROP TABLE #tempStock  
    CREATE TABLE #tempStock  
    (  
        StockId INT NOT NULL,  
        QuantityAvailable INT NOT NULL,  
        Price FLOAT NULL  
    )  
    INSERT INTO #tempStock  
    (  
        StockId,  
        QuantityAvailable,  
        Price  
    )  
    SELECT s.StockId,  
           CASE  
               WHEN s.Unavailable = 1 THEN  
                   0  
               ELSE  
                   s.QuantityInStock + s.QuantityOnOrder - ISNULL(SUM(al.QuantityAllocated), 0)  
           END,  
     CASE   
    -- Case stock on DMCC, priority: ClientUpLiftPrice =>  ResalePrice  
    -- Case stock on client,  priority: only ResalePrice  
    WHEN s.ClientNo = 114 AND ISNULL(s.ClientUpLiftPrice, 0) > 0 THEN s.ClientUpLiftPrice  
    ELSE ISNULL(s.ResalePrice, 0)  
     END  
    FROM dbo.tbStock s WITH (NOLOCK)  
        LEFT JOIN tbAllocation al WITH (NOLOCK)  
            ON al.StockNo = s.StockId  
    WHERE (  
              s.FullPart = @PartNumber  
              OR s.FullSupplierPart = @PartNumber  
          )  
          AND (  
                  s.QuantityInStock > 0  
                  OR s.QuantityOnOrder > 0  
              )  
    GROUP BY s.StockId,  
    s.ClientNo,  
             s.QuantityInStock,  
             s.QuantityOnOrder,  
             s.Unavailable,  
             s.ResalePrice,  
             s.ClientUpLiftPrice  
  
    ;WITH cteStock  
    AS (SELECT ROW_NUMBER() OVER (ORDER BY s.DLUP DESC) as rn,  
               s.StockId,  
               temp.QuantityAvailable AS Quantity,  
               CASE  
       WHEN temp.Price = 0 THEN CAST(0 AS FLOAT)  
       WHEN @BOMCurrencyNo IS NULL THEN NULL --BOM currency not exist in local currency  
       WHEN tlc.LocalCurrencyId IS NULL THEN NULL --Offer currency not exist in local currency  
       ELSE dbo.ufn_convert_to_local_currency(temp.Price, tlc.LocalCurrencyId, @BOMCurrencyNo, s.DLUP, @ClientID)  
               END AS BomPrice,  
               temp.Price AS OriginalPrice,  
               cu.CurrencyCode AS OfferCurrencyCode,  
               @BOMCurrencyCode AS BomCurrencyCode,  
      cl.ClientId,  
      cl.ClientName  
        FROM dbo.tbStock s WITH (NOLOCK)  
            JOIN #tempStock temp WITH (NOLOCK)  
                ON temp.StockId = s.StockId  
            JOIN dbo.tbClient cl WITH (NOLOCK)  
                ON cl.ClientId = s.ClientNo  
            JOIN dbo.tbCurrency cu WITH (NOLOCK)  
                ON cu.CurrencyId = cl.CurrencyNo  
   LEFT JOIN #tempLocalCurrency tlc ON tlc.LocalCurrencyCode = cu.CurrencyCode  
       )  
 INSERT INTO #finalStock  
 (  
  StockId,  
  Quantity,  
  Price,  
  Client  
 )  
    SELECT   
  '<a class="documentachor no-left-spacing" href="../../Whs_StockDetail.aspx?stk=' + CAST(StockId AS NVARCHAR(20))  
  + '" target="_blank">' + CAST(StockId AS NVARCHAR(20)) + '</a>' AS StockId,  
  Quantity,  
  CASE   
   WHEN OriginalPrice = 0 THEN 'Contact Client'  
   WHEN BomPrice IS NULL THEN CAST(CONVERT(DECIMAL(16, 5), OriginalPrice) AS NVARCHAR(100)) + ' ' + OfferCurrencyCode  
   ELSE   
    CAST(CONVERT(DECIMAL(16, 5), BomPrice) AS NVARCHAR(100)) + ' ' + BomCurrencyCode + ' ('  
    + CAST(CONVERT(DECIMAL(16, 5), OriginalPrice) AS NVARCHAR(100)) + ' ' + OfferCurrencyCode + ')'  
  END AS Price,  
  ClientName AS Client  
    FROM cteStock  
    WHERE rn <= 20;  
  
 --Make sure #finalStock has at least 1 record  
 IF(NOT EXISTS(SELECT 1 FROM #finalStock))  
 BEGIN  
  INSERT INTO #finalStock  
  (  
   StockId,  
   Quantity,  
   Price,  
   Client  
  ) VALUES (@NotfoundMessage, 0, '', '')  
 END  
  
 /*==========Final results==========*/  
 SELECT * FROM #finalRLStock;  
 SELECT * FROM #finalStrategicStock;  
 SELECT * FROM #finalStock;  
END  