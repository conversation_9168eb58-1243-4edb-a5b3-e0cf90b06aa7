﻿/*  
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-242815]		Trung Pham		05-May-2025		UPDATE		Get more fields  
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE usp_GetExportApprovalDataById        
@ExportApprovalId  INT        
AS        
/*        
 *[001]  Created  Abhinav Saxena  30-03-2023  Add new procedure to get the data of export approval by ID.    
 *[002]  Altered  Abhinav Saxena  10-04-2023  Add new block of code for Export Approval.          
 */        
BEGIN        
SET NOCOUNT ON        
DECLARE @ExportPowerUrl NVARCHAR(MAX)='';    
DECLARE @EUUFormUploadName NVARCHAR(MAX)=NULL;    
    
SELECT @ExportPowerUrl=FlowUrl FROM tbPowerApp_urls WHERE FlowName='Export Approval'    
SELECT @EUUFormUploadName=[FileName] FROM tbSOLineEUUPDF WHERE ExportApprovalNo=@ExportApprovalId    
    
CREATE TABLE #tempExportApprovalById        
(        
ExportApprovalId   INT,        
SalesPersonName    NVARCHAR(MAX),        
SalesOrderNumber   INT,        
LineNumber     INT,        
CustomerName    NVARCHAR(MAX),        
PART      NVARCHAR(MAX),        
DestinationCountry   NVARCHAR(MAX),      
ISPdfAttached  BIT,      
MilitaryUseName  NVARCHAR(MAX),      
EndUser    NVARCHAR(MAX),    
PowerExportUrl  NVARCHAR(MAX),    
EUUUPDFploadName NVARCHAR(MAX),    
SalesOrderNo  INT,    
SalesOrderLineId INT,    
EUUFornName   NVARCHAR(MAX),
ECCN NVARCHAR(MAX),
PartApplication NVARCHAR(100),
ExportControl BIT,
AerospaceUse BIT,
PartTested NVARCHAR(200),
CommodityCode NVARCHAR(MAX),
ClientNo INT,
ShipToCustomerCountry NVARCHAR(MAX),
ShipToCustomerName NVARCHAR(MAX),
ShipFromCountry NVARCHAR(MAX),
ShipFromWarehouse NVARCHAR(MAX)
)        
        
INSERT INTO #tempExportApprovalById        
SELECT          
easg.ExportApprovalId,        
lgnsp.EmployeeName,        
so.SalesOrderNumber,        
sol.SOSerialNo,        
co.CompanyName,        
sol.FullPart,        
cn.CountryName,      
CASE WHEN(SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE SOLineNo=easg.SalesOrderLineNo)>0 THEN 1 ELSE 0 END,      
CASE WHEN ead.MilitaryUseNo=1 THEN 'Yes' WHEN ead.MilitaryUseNo=2 THEN 'No'       
WHEN ead.MilitaryUseNo=3 THEN 'Dual'  
WHEN ead.MilitaryUseNo=4 THEN 'TBC' ELSE '' END,      
ISNULL(ead.ENDUserText,''),    
@ExportPowerUrl,    
@EUUFormUploadName,    
so.SalesOrderId,    
easg.SalesOrderLineNo,    
CASE WHEN @EUUFormUploadName IS NOT  NULL THEN 'EUU Form.pdf' ELSE 'File Not Uploaded'  END,
sol.ECCNCode,
ead.PartApplication,
ead.ExportControl,
ead.AerospaceUse,
ead.PartTested,
pr.DutyCode,
so.ClientNo,
cy.CountryName,
co.CompanyName,
ct.CountryName,
wh.WarehouseName
FROM tbSO_ExportApprovalStatusOGEL easg        
LEFT OUTER JOIN tbSalesOrder so ON easg.SalesOrderNo=so.SalesOrderId        
LEFT OUTER JOIN tbLogin lgnsp ON so.Salesman=lgnsp.LoginId        
LEFT OUTER JOIN tbSalesOrderLine sol ON easg.SalesOrderLineNo=sol.SalesOrderLineId        
LEFT OUTER JOIN tbCompany co ON so.CompanyNo=co.CompanyId        
LEFT OUTER JOIN tbSO_ExportApprovalDetails ead ON ead.ExportApprovalNo=easg.ExportApprovalId    
LEFT OUTER JOIN tbCountry cn ON ead.EndDestinationCountryNo=cn.CountryId   
LEFT OUTER JOIN tbProduct pr ON pr.ProductId=sol.ProductNo
LEFT OUTER JOIN tbAddress ad ON ad.AddressId=so.ShipToAddressNo
LEFT OUTER JOIN tbCountry cy ON cy.CountryId=ad.CountryNo
LEFT OUTER JOIN vwAllocation al ON al.CustomerPO=so.CustomerPO AND sol.Part = al.Part
LEFT OUTER JOIN tbPurchaseOrder po ON al.PurchaseOrderNo=po.PurchaseOrderId
LEFT OUTER JOIN tbCountry ct ON ct.CountryId=po.ImportCountryNo
LEFT OUTER JOIN tbWareHouse wh ON wh.WarehouseId=po.WarehouseNo
WHERE easg.ExportApprovalId=@ExportApprovalId        
        
SELECT        
 *        
  From #tempExportApprovalById        
        
DROP TABLE #tempExportApprovalById        
SET NOCOUNT OFF        
END  