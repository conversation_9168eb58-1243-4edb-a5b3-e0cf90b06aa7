///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
/*
Marker     Changed by      Date         Remarks
[001]       Bhooma Nand   28/01/2021       Added New Nugget for CRM prospects
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.initializeBase(this, [element]);
    this._intCRMProspectID = -1;
    this._inactive = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.prototype = {

    get_intCustomerID: function () { return this._intCustomerID; }, set_intCustomerID: function (v) { if (this._intCustomerID !== v) this._intCustomerID = v; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },
    get_CanEditActive: function () { return this._CanEditActive; }, set_CanEditActive: function (v) { if (this._CanEditActive !== v) this._CanEditActive = v; },
    get_ibtnViewTask: function () { return this._ibtnViewTask; }, set_ibtnViewTask: function (v) { if (this._ibtnViewTask !== v) this._ibtnViewTask = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        //edit form
        if (this._ibtnEdit) {

            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
            this._frmEdit.addSaveError(Function.createDelegate(this, this.saveEditError));
        }

        //Add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[1]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.cancelAdd));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
            this._frmAdd.addSaveError(Function.createDelegate(this, this.saveAddError));
        }
        if (!this._blnIsNoDataFound) {
            if (!this._blnHasInitialData) {
                this.getData();        
            }
        }
        if (this._ibtnViewTask) {
            $R_IBTN.addClick(this._ibtnViewTask, Function.createDelegate(this, this.redirectToDetails));
        }
        this.getCompanyInactive();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmAdd) this._frmAdd.dispose();
        this._intCustomerID = null;
        this._ibtnAdd = null;
        this._frmEdit = null;
        this._frmAdd = null;
        this._inactive = null;
        if (this._ibtnViewTask) $R_IBTN.clearHandlers(this._ibtnViewTask);
        this._ibtnViewTask = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.callBaseMethod(this, "dispose");
    },


    getData: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyProspects");
        obj.set_DataObject("CompanyProspects");
        obj.set_DataAction("GetData");
        //obj.addParameter("id", this._intCustomerID);
        obj.addParameter("id", this._intCustomerID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function (args) {
        var res = args._result;
        this.setFieldValue("ctlProspectType", $R_FN.setCleanTextValue(res.ProspectTypeName));
        this.setFieldValue("ctlMFRBoardLevel", res.IscrmProspectBoardLevel == true ? "Yes" : res.IscrmProspectBoardLevel == false ? "No" : "");
        this.setFieldValue("ctlFinalAssembly", res.IsFinalAssembly == true ? "Yes" : res.IsFinalAssembly == false ? "No" : "");
        this.setFieldValue("ctlEndCustomer", $R_FN.setCleanTextValue(res.EndCustomer));
        //this.setFieldValue("ctlIndustry", res.IsIndustryAlreadyAssign == true ? true : (res.IsIndustry));
        //this.setFieldValue("ctlIndustry", res.IsIndustry);
        this.setFieldValue("ctlCreditInfo", 'Credit Limit Potential: ' + $R_FN.setCleanTextValue(res.LimitedEstimate) + '<br/> Health Rating %: ' + $R_FN.setCleanTextValue(res.HealthRating));
        //this.setFieldValue("ctlHealthRating", $R_FN.setCleanTextValue(res.HealthRating));
        this.setFieldValue("ctlElectronicSpend", $R_FN.setCleanTextValue(res.ElectronicSpendName));
        this.setFieldValue("ctlFrequencyOfPurchase", $R_FN.setCleanTextValue(res.FrequencyOfPurchaseName));
        this.setFieldValue("ctlCommodities", $R_FN.setCleanTextValue(res.CommoditiesName));
        this.setFieldValue("ctlTurnover", $R_FN.setCleanTextValue(res.TurnoverName));

        this.setFieldValue("hidProspectTypeId", res.ProspectTypeId);
        this.setFieldValue("hidIsIndustry", res.IsIndustry);
        this.setFieldValue("hidElectronicSpendId", res.ElectronicSpendId);
        this.setFieldValue("hidFrequencyOfPurchaseId", res.FrequencyOfPurchaseId);
        this.setFieldValue("hidCommoditiesId", res.CommoditiesId);
        this.setFieldValue("hidTurnoverId", res.TurnoverId);
        this.setFieldValue("hidLimitedEstimate", res.LimitedEstimate);
        this.setFieldValue("hidHealthRating", res.HealthRating);

        this.setFieldValue("hidMFRBoardLevel", res.IscrmProspectBoardLevel);
        this.setFieldValue("hidFinalAssembly", res.IsFinalAssembly);
        //this._blnIsIndustryAlreadyAssign = res.IsIndustryAlreadyAssign;
        this._intCRMProspectID = res.ID;
        //$('#dvCompletePecent').html("<img src='../../../../images/crmicons/0-disable.png'> <img src='../../../../images/crmicons/10-disable.png'> <img src='../../../../images/crmicons/20-disable.png'>");

        //
        //parse industry types
       
        var IsIndustrySelected = false;
        var strIndustryTypes = ""; var strIndustryTypeIDs = ""; var strAllIndustryTypes = ""; var strAllIndustryTypeIDs = "";
        if (res.IndustryTypes) {
            for (var i = 0; i < res.IndustryTypes.length; i++) {
                if (res.IndustryTypes[i].Selected) {
                    if (strIndustryTypes.length > 0) strIndustryTypes += ", ";
                    strIndustryTypes += res.IndustryTypes[i].Name;
                    if (strIndustryTypeIDs.length > 0) strIndustryTypeIDs += "||";
                    strIndustryTypeIDs += res.IndustryTypes[i].ID;
                    IsIndustrySelected = true;
                }
                if (strAllIndustryTypes.length > 0) strAllIndustryTypes += "||";
                strAllIndustryTypes += res.IndustryTypes[i].Name;
                if (strAllIndustryTypeIDs.length > 0) strAllIndustryTypeIDs += "||";
                strAllIndustryTypeIDs += res.IndustryTypes[i].ID;
            }
        }
        //var IsEntertainmentSelected = false;
        //var strEntertainmentTypes = ""; var strEntertainmentTypeIDs = ""; var strAllEntertainmentTypes = ""; var strAllEntertainmentTypeIDs = "";
        //if (res.EntertainmentTypes) {
        //    for (var i = 0; i < res.EntertainmentTypes.length; i++) {
        //        if (res.EntertainmentTypes[i].Selected) {
        //            if (strEntertainmentTypes.length > 0) strEntertainmentTypes += ", ";
        //            strEntertainmentTypes += res.EntertainmentTypes[i].Name;
        //            if (strEntertainmentTypeIDs.length > 0) strEntertainmentTypeIDs += "||";
        //            strEntertainmentTypeIDs += res.EntertainmentTypes[i].ID;
        //            IsEntertainmentSelected  = true;
        //        }
        //        if (strAllEntertainmentTypes.length > 0) strAllEntertainmentTypes += "||";
        //        strAllEntertainmentTypes += res.EntertainmentTypes[i].Name;
        //        if (strAllEntertainmentTypeIDs.length > 0) strAllEntertainmentTypeIDs += "||";
        //        strAllEntertainmentTypeIDs += res.EntertainmentTypes[i].ID;
        //    }
        //}

        /********** Area Type********/
        //var IsIndustryAreaSelected = false;
        //var strIndustryAreaTypes = ""; var strIndustryAreaTypeIDs = ""; var strAllIndustryAreaTypes = ""; var strAllIndustryAreaTypeIDs = "";
        //if (res.IndustryAreaTypes) {
        //    for (var i = 0; i < res.IndustryAreaTypes.length; i++) {
        //        if (res.IndustryAreaTypes[i].Selected) {
        //            if (strIndustryAreaTypes.length > 0) strIndustryAreaTypes += ", ";
        //            strIndustryAreaTypes += '<span class="areaClass" onClick="getContactLog(' + res.IndustryAreaTypes[i].ID +')"  id=AreaID_' + res.IndustryAreaTypes[i].ID + ' style="color:#0000ff; cursor: pointer; text-decoration:underline;" >' + res.IndustryAreaTypes[i].Name + '</span>';
                    
        //            if (strIndustryAreaTypeIDs.length > 0) strIndustryAreaTypeIDs += "||";
        //            strIndustryAreaTypeIDs += res.IndustryAreaTypes[i].ID;
        //            IsIndustryAreaSelected = true;
        //        }
        //        if (strAllIndustryAreaTypes.length > 0) strAllIndustryAreaTypes += "||";
        //        strAllIndustryAreaTypes += res.IndustryAreaTypes[i].Name;
        //        if (strAllIndustryAreaTypeIDs.length > 0) strAllIndustryAreaTypeIDs += "||";
        //        strAllIndustryAreaTypeIDs += res.IndustryAreaTypes[i].ID;
        //    }
            
        //}
        
        /********** -**- ********/
        this.setFieldValue("ctlIndustryType", strIndustryTypes);
        this.setFieldValue("hidIndustryTypeIDs", strIndustryTypeIDs);
        this.setFieldValue("hidAllIndustryTypeNames", strAllIndustryTypes);
        this.setFieldValue("hidAllIndustryTypeIDs", strAllIndustryTypeIDs);

        /********** Entertainment Type********/
        //this.setFieldValue("ctlEntertainmentType", strEntertainmentTypes);
        //this.setFieldValue("hidEntertainmentTypeIDs", strEntertainmentTypeIDs);
        //this.setFieldValue("hidAllEntertainmentTypeNames", strAllEntertainmentTypes);
        //this.setFieldValue("hidAllEntertainmentTypeIDs", strAllEntertainmentTypeIDs);
        /********** -**- ********/

        /********** Area Type********/
        //this.setFieldValue("ctlIndustryAreaType", strIndustryAreaTypes);
        //this.setFieldValue("hidIndustryAreaTypeIDs", strIndustryAreaTypeIDs);
        //this.setFieldValue("hidAllIndustryAreaTypeNames", strAllIndustryAreaTypes);
        //this.setFieldValue("hidAllIndustryAreaTypeIDs", strAllIndustryAreaTypeIDs);
        /********** -**- ********/
        //

        var count = 0
        var point = 0;
        if (res.ProspectTypeId > 0) {
            count = count + 1;
        }
        if (res.IscrmProspectBoardLevel != null) {
            count = count + 1;
        }
        if (res.IsFinalAssembly != null) {
            count = count + 1;
        }
        if (res.EndCustomer != '') {
            count = count + 1;
        }
        if (IsIndustrySelected == true) {
            count = count + 1;
        }
        if (res.ElectronicSpendId > 0) {
            count = count + 1;
        }
        if (res.FrequencyOfPurchaseId > 0) {
            count = count + 1;
        } if (res.CommoditiesName.length > 0) {
            count = count + 1;
        } if (res.TurnoverId > 0) {
            count = count + 1;
        }
        if ((res.LimitedEstimate != '' && res.LimitedEstimate != undefined) && (res.HealthRating != '' && res.HealthRating != undefined)) {
            count = count + 1;
        }
        else {
            if ((res.LimitedEstimate != '' && res.LimitedEstimate != undefined)) {
                point = point + 0.5;
            }
            if ((res.HealthRating != '' && res.HealthRating != undefined)) {
                point = point + 0.5;
            }
        }
        var imgLink = "";
        var imgDisableLink = "";
        for (var i = 0; i <= 10; i++) {
            if (i <= count) {
                imgLink = imgLink + " <img src='../../../../images/crmicons/" + i + ".png'>";
            }
            else {
                imgDisableLink = imgDisableLink + " <img src='../../../../images/crmicons/" + (i) + "-disable.png'>";

            }
        }
        if (point > 0) {
            imgLink = imgLink + " <img src='../../../../images/crmicons/" + (count + point) + ".png'>";
        }
        imgLink = imgLink + imgDisableLink;
        imgLink = imgLink + '<br/>' + '<span style=" margin - left: 235px;color: green;"><b>Percentage complete</b></span> <br/><br/> <span style=" margin - left: 235px;color: red;"><b>"Credit Limit Potential" and "Health Rating" is calculated as 5% each and the rest is 10%</b></span>' + '<br/><br/>';
        $('#dvCompletePecent').html(imgLink);

        this.getDataOK_End();

       
    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getCompanyInactive: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyProspects");
        obj.set_DataObject("CompanyProspects");
        obj.set_DataAction("GetCompanyDetailInactive");
        obj.addParameter("id", this._intCustomerID);
        obj.addDataOK(Function.createDelegate(this, this.getCompanyInactiveOK));
        obj.addError(Function.createDelegate(this, this.getCompanyInactiveError));
        obj.addTimeout(Function.createDelegate(this, this.getCompanyInactiveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCompanyInactiveOK: function (args) {
        var result = args._result;
        this._inactive = result.Inactive;
        if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._inactive);
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive);
        if (this._ibtnViewTask) $R_IBTN.enableButton(this._ibtnViewTask, !this._inactive);
    },

    getCompanyInactiveError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    showEditForm: function () {

        this.showForm(this._frmEdit, true);
        this._frmEdit._intCRMProspectID = this._intCRMProspectID;
        this._frmEdit._intCustomerID = this._intCustomerID;
        this._frmEdit.setFieldValue("ctlProspectType", this.getFieldValue("hidProspectTypeId"));
        //this._frmEdit.setFieldValue("ctlMFRBoardLevel", this.getFieldValue("ctlMFRBoardLevel"));
        //this._frmEdit.setFieldValue("ctlFinalAssembly", this.getFieldValue("ctlFinalAssembly"));
        this._frmEdit.setFieldValue("ctlEndCustomer", this.getFieldValue("ctlEndCustomer"));
        //this._frmEdit.setFieldValue("ctlIndustry", this.getFieldValue("hidIsIndustry"));
        this._frmEdit.setFieldValue("ctlLimitedEstimate", this.getFieldValue("hidLimitedEstimate"));
        this._frmEdit.setFieldValue("ctlHealthRating", this.getFieldValue("hidHealthRating"));
        this._frmEdit.setFieldValue("ctlElectronicSpend", this.getFieldValue("hidElectronicSpendId"));
        this._frmEdit.setFieldValue("ctlFrequencyOfPurchase", this.getFieldValue("hidFrequencyOfPurchaseId"));
        //this._frmEdit.setFieldValue("ctlCommodities", this.getFieldValue("hidCommoditiesId"));
        this._frmEdit.setFieldValue("ctlCommodities", this.getFieldValue("ctlCommodities"));
        this._frmEdit.setFieldValue("ctlTurnover", this.getFieldValue("hidTurnoverId"));
        if (this.getFieldValue("hidMFRBoardLevel") == 'true') {
            ($get('ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel_0')).checked = true;
        }
        else if (this.getFieldValue("hidMFRBoardLevel") == 'false') {
            ($get('ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel_1')).checked = true;
        }

        if (this.getFieldValue("hidFinalAssembly") == 'true') {
            ($get('ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly_0')).checked = true;
        }
        else if (this.getFieldValue("hidFinalAssembly") == 'false') {
            ($get('ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly_1')).checked = true;
        }
        //this._frmEdit._blnIsIndustryAlreadyAssign = this._blnIsIndustryAlreadyAssign;
        //if (this._blnIsIndustryAlreadyAssign == true) {
        //    this._frmEdit.setFieldValue("ctlIndustry", this._blnIsIndustryAlreadyAssign);
        //    this._frmEdit.enableFieldCheckBox("ctlIndustry", false);
        //}
        //else {
        //    this._frmEdit.setFieldValue("ctlIndustry", this.getFieldValue("hidIsIndustry"));
        //}

        this._frmEdit._ctlSelectIndustryType.clearData();
        var aryCurrentIndustryTypeIDs = $R_FN.singleStringToArray(this.getFieldValue("hidIndustryTypeIDs"));
        var aryAllIndustryTypes = $R_FN.singleStringToArray(this.getFieldValue("hidAllIndustryTypeNames"));
        var aryAllIndustryTypeIDs = $R_FN.singleStringToArray(this.getFieldValue("hidAllIndustryTypeIDs"));
        for (var i = 0; i < aryAllIndustryTypes.length; i++) {
            this._frmEdit._ctlSelectIndustryType.addRow(Array.contains(aryCurrentIndustryTypeIDs, aryAllIndustryTypeIDs[i]), [$R_FN.setCleanTextValue(aryAllIndustryTypes[i])], aryAllIndustryTypeIDs[i]);
        }
        this._frmEdit._ctlSelectIndustryType.populateTables();

        //this._frmEdit._ctlSelectEntertainmentType.clearData();
        //var aryCurrentEntertainmentTypeIDs = $R_FN.singleStringToArray(this.getFieldValue("hidEntertainmentTypeIDs"));
        //var aryAllEntertainmentTypes = $R_FN.singleStringToArray(this.getFieldValue("hidAllEntertainmentTypeNames"));
        //var aryAllEntertainmentTypeIDs = $R_FN.singleStringToArray(this.getFieldValue("hidAllEntertainmentTypeIDs"));
        //for (var i = 0; i < aryAllEntertainmentTypes.length; i++) {
        //    this._frmEdit._ctlSelectEntertainmentType.addRow(Array.contains(aryCurrentEntertainmentTypeIDs, aryAllEntertainmentTypeIDs[i]), [$R_FN.setCleanTextValue(aryAllEntertainmentTypes[i])], aryAllEntertainmentTypeIDs[i]);
        //}
        //this._frmEdit._ctlSelectEntertainmentType.populateTables();

        //this._frmEdit._ctlSelectIndustryAreaType.clearData();
        //var aryCurrentIndustryAreaTypeIDs = $R_FN.singleStringToArray(this.getFieldValue("hidIndustryAreaTypeIDs"));
        //var aryAllIndustryAreaTypes = $R_FN.singleStringToArray(this.getFieldValue("hidAllIndustryAreaTypeNames"));
        //var aryAllIndustryAreaTypeIDs = $R_FN.singleStringToArray(this.getFieldValue("hidAllIndustryAreaTypeIDs"));
        //for (var i = 0; i < aryAllIndustryAreaTypes.length; i++) {
        //    this._frmEdit._ctlSelectIndustryAreaType.addRow(Array.contains(aryCurrentIndustryAreaTypeIDs, aryAllIndustryAreaTypeIDs[i]), [$R_FN.setCleanTextValue(aryAllIndustryAreaTypes[i])], aryAllIndustryAreaTypeIDs[i]);
        //}
        //this._frmEdit._ctlSelectIndustryAreaType.populateTables();

        this._frmEdit.showInnerContent(true);
    },

    cancelEdit: function () {
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function () {
        this.showForm(this._frmEdit, false);
        this.showContentLoading(false);
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },

    saveEditError: function () {
        this.showError(true, this._frmEdit._strErrorMessage);
    },

    showAddForm: function () {
        this._frmAdd.setFormFieldsToDefaults();
        this._frmAdd.setFieldValue("ctlDueTime", "09:00");
        this._frmAdd.setFieldValue("ctlReminderTime", "09:00");
        this.showForm(this._frmAdd, true);
        this._frmAdd.setFieldValue("ctlCompanyNew", this._intCustomerID, null, $R_FN.setCleanTextValue($('#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCompanyName_lbl').text()));
        //this._frmAdd.setFieldValue("ctlCompany", $RGT_nubButton_Company(this._intCustomerID, $R_FN.setCleanTextValue($('#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCompanyName_lbl').text())));
        this._frmAdd.setFieldValue("ctlCompany", '<a style="color:white;" href="Con_CompanyDetail.aspx?cm=' + this._intCustomerID + '">' + $R_FN.setCleanTextValue($('#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCompanyName_lbl').text()) + '</a>');
        this._frmAdd.showField("ctlCompanyNew", false);
    },

    cancelAdd: function () {
        this.showForm(this._frmAdd, false);
    },

    saveAddComplete: function () {
        this.showForm(this._frmAdd, false);
        this.showContentLoading(false);
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },

    saveAddError: function () {
        this.showError(true, this._frmAdd._strErrorMessage);
    },
    redirectToDetails: function () {
        location.href = ('Prf_ToDo.aspx?cm=' + this._intCustomerID + '&cmn=' + $R_FN.setCleanTextValue($('#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCompanyName_lbl').text())), '_blank';
    },
    

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
