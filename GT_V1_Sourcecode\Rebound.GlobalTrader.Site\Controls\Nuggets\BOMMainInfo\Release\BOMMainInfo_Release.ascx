<%@ Control Language="C#" CodeBehind="BOMMainInfo_Release.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Release" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMItem_Release")%></Explanation>
	<Content>
        
       
        
		<ReboundUI_Table:Form id="frm" runat="server">
            
            
			<ReboundUI_Form:FormField id="ctlRelease" runat="server" FieldID="ctlConfirmRelease" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmRelease" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="FormField1" runat="server"  ResourceTitle="">
				<Field>
				</Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlSerialNoDetail" runat="server" ResourceTitle="AllSourcingResults">
				<Field>	
                    
                        <asp:Panel ID="pnlSerialNodetails" runat="server" CssClass="GridSerialdetails" Width="70%">
                      <Reboundui:FlexiDataTable id="tblAllReleasedetails"  runat="server"  PanelHeight="200"  AllowSelection="true"/>
                       </asp:Panel>            
				</Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>

        
      </Content>
	                
						
	
</ReboundUI_Form:DesignBase>
<style>
    #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlRelease_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_0,
    #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlRelease_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_1,
    #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlRelease_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_2,
    #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlRelease_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_3,
    #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlRelease_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_4 {
        border-color: #bdbdbd;    background-color: #bdbdbd !important;    color: #333 !important;
    }
</style>
