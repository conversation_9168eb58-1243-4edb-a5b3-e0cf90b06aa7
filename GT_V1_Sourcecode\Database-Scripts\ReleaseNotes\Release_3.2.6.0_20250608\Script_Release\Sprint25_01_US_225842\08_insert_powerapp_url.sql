﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-225842]		An.TranTan			08-Apr-2025		CREATE			Insert/update powerapp URL for Sales Order Task Reminder
===========================================================================================
*/
DECLARE @HttpURL NVARCHAR(MAX) = N'https://gt-uat-webapp-001.azurewebsites.net'

IF EXISTS(SELECT TOP 1 1 FROM tbPowerApp_urls WITH(NOLOCK) WHERE FlowName = 'SalesOrder_TaskReminder_Notification')
BEGIN
	UPDATE tbPowerApp_urls SET FlowUrl = @HttpURL WHERE FlowName = 'SalesOrder_TaskReminder_Notification'
END
ELSE BEGIN
	INSERT INTO tbPowerApp_urls
	(
		FlowName,
		FlowUrl
	)
	VALUES
	(
		'SalesOrder_TaskReminder_Notification'
		,@HttpURL
	)
END
GO
