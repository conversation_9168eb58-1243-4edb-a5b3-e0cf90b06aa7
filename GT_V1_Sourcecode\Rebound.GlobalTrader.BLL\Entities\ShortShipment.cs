﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;
using System.Data;

namespace Rebound.GlobalTrader.BLL
{
    public class ShortShipment : BizObject
    {
        #region Properties

        protected static DAL.ShortShipmentElement Settings
        {
            get { return Globals.Settings.ShortShipments; }
        }
        /// <summary>
        /// ShortShipmentID
        /// </summary>
        public System.Int32? ShortShipmentId { get; set; }
        /// <summary>
        /// Supplier
        /// </summary>
        public System.String Supplier { get; set; }
        /// <summary>
        /// PurchaseOrderNo
        /// </summary>
        public System.Int32? PurchaseOrderNo { get; set; }
        /// <summary>
        /// IPONo
        /// </summary>
        public System.Int32? IPONo { get; set; }
        /// <summary>
        /// Salesman
        /// </summary>
        public System.String Salesman { get; set; }
        /// <summary>
        /// SalesmanId
        /// </summary>
        public System.Int32? SalesmanId { get; set; }
        /// <summary>
        /// DeliveryNoteNo
        /// </summary>
        public System.String Reference { get; set; }
        /// <summary>
        /// GINoteNo
        /// </summary>
        public System.Int32? GoodsInNo { get; set; }
        /// <summary>
        /// DateReceived
        /// </summary>
        public System.DateTime? DateReceived { get; set; }
        /// <summary>
        /// Raisedby
        /// </summary>
        public System.String Raisedby { get; set; }
        /// <summary>
        /// BuyerId
        /// </summary>
        public System.Int32? BuyerId { get; set; }
        /// <summary>
        /// Buyer
        /// </summary>
        public System.String Buyer { get; set; }
        /// <summary>
        /// PartNo
        /// </summary>
        public System.String PartNo { get; set; }
        /// <summary>
        /// ManufacturerName
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// QuantityOrdered
        /// </summary>
        public System.Int32? QuantityOrdered { get; set; }
        /// <summary>
        /// QuantityAdvised
        /// </summary>
        public System.Int32? QuantityAdvised { get; set; }
        /// <summary>
        /// QuantityReceived
        /// </summary>
        public System.Int32? QuantityReceived { get; set; }
        /// <summary>
        /// ShortageQuantity
        /// </summary>
        public System.Int32? ShortageQuantity { get; set; }
        /// <summary>
        /// ShortValue
        /// </summary>
        public System.Double? ShortageValue { get; set; }
        /// <summary>
        /// IsShortageRefundIssue
        /// </summary>
        public System.Boolean? IsShortageRefundIssue { get; set; }
        /// <summary>
        /// ShortageRefundIssue
        /// </summary>
        public System.String ShortageRefundIssue { get; set; }
        /// <summary>
        /// Completedby
        /// </summary>
        public System.String Completedby { get; set; }
        /// <summary>
        /// CompletedDate
        /// </summary>
        public System.DateTime? CompletedDate { get; set; }
        /// <summary>
        /// Status
        /// </summary>
        public System.String Status { get; set; }
        /// <summary>
        /// RowCnt
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// RowNum
        /// </summary>
        public System.Int32? RowNum { get; set; }
        /// <summary>
        /// StatusId
        /// </summary>
        public System.Int32? StatusId { get; set; }
        /// <summary>
        /// IsStageTwoUpdated
        /// </summary>
        public System.Boolean IsStageTwoUpdated { get; set; }
        /// <summary>
        /// RaisedbyId
        /// </summary>
        public System.Int32? RaisedbyId { get; set; }
        /// <summary>
        /// GoodsInId
        /// </summary>
        public System.Int32? GoodsInId { get; set; }
        /// <summary>
        /// ClientNo
        /// </summary>
        public System.Int32? ClientNo { get; set; }
        /// <summary>
        /// PurchaseOrderId
        /// </summary>
        public System.Int32? PurchaseOrderId { get; set; }
        /// <summary>
        /// DebitNoteNo
        /// </summary>
        public System.Int32? DebitNoteNo { get; set; }
        /// <summary>
        /// DebitNumber
        /// </summary>
        public System.Int32? DebitNumber { get; set; }
        /// <summary>
        /// IsCancel
        /// </summary>
        public System.Boolean IsCancel { get; set; }
        /// <summary>
        /// IsClosed
        /// </summary>
        public System.Boolean IsClosed { get; set; }
        /// <summary>
        /// IsDebitNoteExists
        /// </summary>
        public System.Boolean IsDebitNoteExists { get; set; }
        /// <summary>
        /// IsPOHub
        /// </summary>
        public System.Boolean? IsPOHub { get; set; }
        /// <summary>
        /// ManufacturerId
        /// </summary>
        public System.Int32? ManufacturerId { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        public System.Int32? CurrencyNo { get; set; }
        public System.String DebitIds { get; set; }
        public System.String DebitNumbers { get; set; }
        public int? InternalPurchaseOrderId { get; set; }
        public int? InternalPurchaseOrderNo { get; set; }
        public System.String CurrencyCode { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.String CompanyAdvisoryNotes { get; set; }
        public System.String MfrAdvisoryNotes { get; set; }
        #endregion

        #region Method

        /*public static ShortShipment GetShortShipmentById(System.Int32 ShortShipmentId)
        {
            Rebound.GlobalTrader.DAL.ShortShipmentDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.GetShortShipmentById(ShortShipmentId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Rebound.GlobalTrader.BLL.ShortShipment obj = new Rebound.GlobalTrader.BLL.ShortShipment();
                obj.ShortShipmentId = objDetails.ShortShipmentId;
                obj.Supplier = objDetails.Supplier;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.Salesman = objDetails.Salesman;
                obj.DeliveryNoteNo = objDetails.DeliveryNoteNo;
                obj.GINoteNo = objDetails.GINoteNo;
                obj.PartNo = objDetails.PartNo;
                obj.DateReceived = objDetails.DateReceived;
                obj.Raisedby = objDetails.Raisedby;
                obj.Supplier = objDetails.Supplier;
                obj.Buyer = objDetails.Buyer;
                obj.PartNo = objDetails.PartNo;
                obj.ManufacturerNo = objDetails.ManufacturerNo;
                obj.QuantityOrdered = objDetails.QuantityOrdered;
                obj.QuantityAdvised = objDetails.QuantityAdvised;
                obj.QuantityReceived = objDetails.QuantityReceived;
                obj.ShortageQuantity = objDetails.ShortageQuantity;
                //[0001] start
                obj.ShortValue = objDetails.ShortValue; 
                //[0001] end
                // obj.CustomerRMANo = objDetails.CustomerRMANo;
               // obj.SupplierRMANo = objDetails.SupplierRMANo;
                //[0002] start
                obj.IsShortageRefundIssue = objDetails.IsShortageRefundIssue;
                obj.ShortageRefundIssue = objDetails.ShortageRefundIssue;
                obj.DebitNoteNo = objDetails.DebitNoteNo;
                obj.Comments = objDetails.Comments;
                obj.Completedby = objDetails.Completedby;
                obj.CompletedDate = objDetails.CompletedDate;
                obj.Status = objDetails.Status;

                //[0002] end

                objDetails = null;
                return obj;
            }

        }*/

        /// <summary>
        /// Insert
        /// Calls [usp_insert_ShortShipment]
        /// </summary>
        public static Int32 Insert(System.Int32? Supplier, System.Int32? PurchaseOrderNo, System.Int32? Salesman, System.String Reference, System.Int32? GoodsInNo, System.DateTime? DateReceived, System.Int32? Raisedby, System.Int32? Buyer, System.String PartNo, System.Int32? ManufacturerNo, System.Int32? QuantityOrdered, System.Int32? QuantityAdvised, System.Int32? QuantityReceived, System.Int32? ShortageQuantity, System.Int32? ShortValue, System.Int32? Status)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.Insert(Supplier, PurchaseOrderNo, Salesman, Reference, GoodsInNo, DateReceived, Raisedby, Buyer, PartNo, ManufacturerNo, QuantityOrdered, QuantityAdvised, QuantityReceived, ShortageQuantity, ShortValue, Status);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_update_ShortShipment]
        /// </summary>
        public static bool UpdateShortShipment(System.Int32 ShortShipmentId, System.Boolean IsShortageRefundIssue, System.String ShortageRefundIssue, System.Int32 loginId, System.Int32? clientId, ref string message, ref int result, System.Int32? ShortShipmentStatusId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail)
        {
            bool objReturn = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.UpdateShortShipment(ShortShipmentId, IsShortageRefundIssue, ShortageRefundIssue, loginId, clientId, ref message, ref result, ShortShipmentStatusId, ref NoReplyId, ref NoReplyEmail);
            return objReturn;
        }
        /// <summary>
        /// Get NPR by NPRId
        /// calls [usp_select_NPRbyId]
        /// </summary>
        /// <param name="nprId"></param>
        /// <returns></returns>
        public static ShortShipment GetShortShipmentById(System.Int32 ShortShipmentId)
        {
            Rebound.GlobalTrader.DAL.ShortShipmentDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.GetShortShipmentById(ShortShipmentId);
            if (objDetails == null)
            {
                return new ShortShipment();
            }
            else
            {
                Rebound.GlobalTrader.BLL.ShortShipment obj = new Rebound.GlobalTrader.BLL.ShortShipment();
                obj.ShortShipmentId = objDetails.ShortShipmentId;
                obj.Supplier = objDetails.Supplier;
                obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                obj.Salesman = objDetails.Salesman;
                obj.SalesmanId = objDetails.SalesmanId;
                obj.Reference = objDetails.Reference;
                obj.GoodsInNo = objDetails.GoodsInNo;
                obj.PartNo = objDetails.PartNo;
                obj.DateReceived = objDetails.DateReceived;
                obj.Raisedby = objDetails.Raisedby;
                obj.RaisedbyId = objDetails.RaisedbyId;
                obj.Supplier = objDetails.Supplier;
                obj.BuyerId = objDetails.BuyerId;
                obj.Buyer = objDetails.Buyer;
                obj.PartNo = objDetails.PartNo;
                obj.ManufacturerName = objDetails.ManufacturerName;
                obj.QuantityOrdered = objDetails.QuantityOrdered;
                obj.QuantityAdvised = objDetails.QuantityAdvised;
                obj.QuantityReceived = objDetails.QuantityReceived;
                obj.ShortageQuantity = objDetails.ShortageQuantity;
                obj.ShortageValue = objDetails.ShortageValue;
                obj.IsShortageRefundIssue = objDetails.IsShortageRefundIssue;
                obj.ShortageRefundIssue = objDetails.ShortageRefundIssue;
                obj.Completedby = objDetails.Completedby;
                obj.CompletedDate = objDetails.CompletedDate;
                obj.Status = objDetails.Status;
                obj.IsStageTwoUpdated = objDetails.IsStageTwoUpdated;
                obj.StatusId = objDetails.StatusId;
                obj.IsCancel = objDetails.IsCancel;
                obj.IsDebitNoteExists = objDetails.IsDebitNoteExists;
                obj.IsPOHub = objDetails.IsPOHub;
                obj.IsClosed = objDetails.IsClosed;

                obj.ManufacturerId = objDetails.ManufacturerId;
                obj.GoodsInId = objDetails.GoodsInId;
                obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                obj.DebitNoteNo = objDetails.DebitNoteNo; // For Single Debit Note
                obj.DebitNumber = objDetails.DebitNumber;// For Single Debit Note
                obj.ClientNo = objDetails.ClientNo;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                obj.CurrencyNo = objDetails.CurrencyNo;
                obj.DebitIds = objDetails.DebitIds;// For Multiple Debit Note
                obj.DebitNumbers = objDetails.DebitNumbers;// For Multiple Debit Note

                obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                obj.InternalPurchaseOrderNo = objDetails.InternalPurchaseOrderNo;
                obj.CurrencyCode = objDetails.CurrencyCode;
                obj.ClientCurrencyCode = objDetails.ClientCurrencyCode;
                obj.CompanyAdvisoryNotes = objDetails.CompanyAdvisoryNotes;
                obj.MfrAdvisoryNotes = objDetails.MfrAdvisoryNotes;
                objDetails = null;
                return obj;
            }

        }

        /// <summary>
        /// Delete NPR by Id
        /// Calls [usp_delete_nprReport]
        /// </summary>
        /// <param name="nprId"></param>
        /// <returns></returns>
        public static bool Delete(System.Int32? ShortShipmentID)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.Delete(ShortShipmentID);
        }

        /*public static List<ShortShipment> GetNPRLog(System.Int32? ShortShipmentID)
        {
           List<ShortShipmentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.GetNPRLog(ShortShipmentID);
           if (lstDetails == null)
           {
               return null;
           }
           else
           {
               List<ShortShipment> lst = new List<ShortShipment>();
               foreach (ShortShipmentDetails objDetails in lstDetails)
               {
                   ShortShipment obj = new ShortShipment();
                   obj.NPRLogId = objDetails.NPRLogId;
                   obj.NPRId = objDetails.NPRId;
                   obj.NPRNo = objDetails.NPRNo;
                   obj.Details = objDetails.Details;
                   obj.UpdatedByName = objDetails.UpdatedByName;
                   obj.DLUP = objDetails.DLUP;
                   lst.Add(obj);
                   obj = null;
               }
               lstDetails = null;
               return lst;
           }
        }*/

        /*public static Int32? getNPRCompanyID(System.Int32? SalesorderNo, System.Int32? ClientID)
        {
            Int32? NPRCompnayID = 0;
            NPRCompnayID = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.getNPRCompanyID(SalesorderNo, ClientID);
            if (NPRCompnayID == null)
            {
                return null;
            }
            return NPRCompnayID;

        }*/
        /// <summary>
        /// Insert NPR Email Log
        /// Call [usp_insert_Email_NPR_Log]
        /// </summary>
        /// <param name="nprId"></param>
        /// <param name="details"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        /*public static Int32 InsertEmailNPRLog(System.Int32? nprId, System.String details,  System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.InsertEmailNPRLog(nprId, details, updatedBy);
            return objReturn;
        }*/

        // <summary>
        /// calls[]
        /// </summary>
        /// <returns></returns>
        public static List<ShortShipment> DataListNugget(System.Int32? ClientID, System.Int32? OrderBy, System.Int32? SortDir, System.Int32? PageIndex, System.Int32? PageSize, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrderNoHi, System.DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? Status, System.Int32? GINumberLo, System.Int32? GINumberHi)
        {
            List<ShortShipmentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.DataListNugget(ClientID, OrderBy, SortDir, PageIndex, PageSize, Supplier, PurchaseOrderNoLo, PurchaseOrderNoHi, DateReceived, QuantityOrderedLo, QuantityOrderedHi, QuantityReceivedLo, QuantityReceivedHi, ShortageQuantityLo, ShortageQuantityHi, ShortageValueLo, ShortageValueHi, IsShortageRefundIssue, Status, GINumberLo, GINumberHi);
            if (lstDetails == null)
            {
                return new List<ShortShipment>();
            }
            else
            {
                List<ShortShipment> lst = new List<ShortShipment>();
                foreach (ShortShipmentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ShortShipment obj = new Rebound.GlobalTrader.BLL.ShortShipment();
                    obj.ShortShipmentId = objDetails.ShortShipmentId;
                    obj.Supplier = objDetails.Supplier;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.IPONo = objDetails.IPONo;
                    obj.Salesman = objDetails.Salesman;
                    obj.Reference = objDetails.Reference;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.PartNo = objDetails.PartNo;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.Raisedby = objDetails.Raisedby;
                    obj.Supplier = objDetails.Supplier;
                    obj.Buyer = objDetails.Buyer;
                    obj.PartNo = objDetails.PartNo;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.QuantityAdvised = objDetails.QuantityAdvised;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.ShortageQuantity = objDetails.ShortageQuantity;
                    obj.ShortageValue = objDetails.ShortageValue;
                    obj.IsShortageRefundIssue = objDetails.IsShortageRefundIssue;
                    obj.ShortageRefundIssue = objDetails.ShortageRefundIssue;
                    obj.Completedby = objDetails.Completedby;
                    obj.CompletedDate = objDetails.CompletedDate;
                    obj.Status = objDetails.Status;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.GoodsInId = objDetails.GoodsInId;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<ShortShipment> DropDown(System.Int32? IsPartialShortShipmentStatus,System.String strquery, System.Int32? LoginId, System.Int32? ClientId)
        {
            List<ShortShipmentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.DropDown(IsPartialShortShipmentStatus, strquery, LoginId, ClientId);
            if (lstDetails == null)
            {
                return new List<ShortShipment>();
            }
            else
            {
                List<ShortShipment> lst = new List<ShortShipment>();
                foreach (ShortShipmentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ShortShipment obj = new Rebound.GlobalTrader.BLL.ShortShipment();
                    obj.StatusId = objDetails.StatusId;
                    obj.Status = objDetails.Status;

                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        // <summary>
        /// calls[]
        /// </summary>
        /// <returns></returns>
        public static Int32 InsertEmailShortShipmentLog(System.Int32? shortShipmentId, System.String strShortShipmentEmailLog, System.Int32? loginID)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.InsertEmailShortShipmentLog(shortShipmentId, strShortShipmentEmailLog, loginID);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_datalistnugget_ShortShipment]
        /// </summary>
        /// <param name="ClientID"></param>
        /// <param name="OrderBy"></param>
        /// <param name="SortDir"></param>
        /// <param name="PageIndex"></param>
        /// <param name="PageSize"></param>
        /// <param name="Supplier"></param>
        /// <param name="PurchaseOrderNoLo"></param>
        /// <param name="PurchaseOrderNoHi"></param>
        /// <param name="DateReceived"></param>
        /// <param name="QuantityOrderedLo"></param>
        /// <param name="QuantityOrderedHi"></param>
        /// <param name="QuantityReceivedLo"></param>
        /// <param name="QuantityReceivedHi"></param>
        /// <param name="ShortageQuantityLo"></param>
        /// <param name="ShortageQuantityHi"></param>
        /// <param name="ShortageValueLo"></param>
        /// <param name="ShortageValueHi"></param>
        /// <param name="IsShortageRefundIssue"></param>
        /// <param name="Status"></param>
        /// <param name="GINumberLo"></param>
        /// <param name="GINumberHi"></param>
        /// <param name="Buyer"></param>
        /// <returns></returns>
        public static List<ShortShipment> DataListNugget(System.Int32? ClientID, System.Int32? OrderBy, System.Int32? SortDir, System.Int32? PageIndex, System.Int32? PageSize, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrderNoHi, System.DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? Status, System.Int32? GINumberLo, System.Int32? GINumberHi, System.Int32? Buyer, System.Boolean IsRecentOnly)
        {
            List<ShortShipmentDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.DataListNugget(ClientID, OrderBy, SortDir, PageIndex, PageSize, Supplier, PurchaseOrderNoLo, PurchaseOrderNoHi, DateReceived, QuantityOrderedLo, QuantityOrderedHi, QuantityReceivedLo, QuantityReceivedHi, ShortageQuantityLo, ShortageQuantityHi, ShortageValueLo, ShortageValueHi, IsShortageRefundIssue, Status, GINumberLo, GINumberHi, Buyer, IsRecentOnly);
            if (lstDetails == null)
            {
                return new List<ShortShipment>();
            }
            else
            {
                List<ShortShipment> lst = new List<ShortShipment>();
                foreach (ShortShipmentDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ShortShipment obj = new Rebound.GlobalTrader.BLL.ShortShipment();
                    obj.ShortShipmentId = objDetails.ShortShipmentId;
                    obj.Supplier = objDetails.Supplier;
                    obj.PurchaseOrderNo = objDetails.PurchaseOrderNo;
                    obj.IPONo = objDetails.IPONo;
                    obj.Salesman = objDetails.Salesman;
                    obj.Reference = objDetails.Reference;
                    obj.GoodsInNo = objDetails.GoodsInNo;
                    obj.PartNo = objDetails.PartNo;
                    obj.DateReceived = objDetails.DateReceived;
                    obj.Raisedby = objDetails.Raisedby;
                    obj.Supplier = objDetails.Supplier;
                    obj.Buyer = objDetails.Buyer;
                    obj.PartNo = objDetails.PartNo;
                    obj.ManufacturerName = objDetails.ManufacturerName;
                    obj.QuantityOrdered = objDetails.QuantityOrdered;
                    obj.QuantityAdvised = objDetails.QuantityAdvised;
                    obj.QuantityReceived = objDetails.QuantityReceived;
                    obj.ShortageQuantity = objDetails.ShortageQuantity;
                    obj.ShortageValue = objDetails.ShortageValue;
                    obj.IsShortageRefundIssue = objDetails.IsShortageRefundIssue;
                    obj.ShortageRefundIssue = objDetails.ShortageRefundIssue;
                    obj.Completedby = objDetails.Completedby;
                    obj.CompletedDate = objDetails.CompletedDate;
                    obj.Status = objDetails.Status;
                    obj.RowNum = objDetails.RowNum;
                    obj.RowCnt = objDetails.RowCnt;
                    obj.GoodsInId = objDetails.GoodsInId;
                    obj.DebitNoteNo = objDetails.DebitNoteNo;
                    obj.DebitNumber = objDetails.DebitNumber;
                    obj.IsClosed = objDetails.IsClosed;
                    obj.IsCancel = objDetails.IsCancel;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Calls [usp_datalistnugget_ShortShipment_Export]
        /// </summary>
        /// <param name="clientID"></param>
        /// <param name="SortIndex"></param>
        /// <param name="SortDir"></param>
        /// <param name="Supplier"></param>
        /// <param name="PurchaseOrderNoLo"></param>
        /// <param name="PurchaseOrderNoHi"></param>
        /// <param name="DateReceived"></param>
        /// <param name="QuantityOrderedLo"></param>
        /// <param name="QuantityOrderedHi"></param>
        /// <param name="QuantityReceivedLo"></param>
        /// <param name="QuantityReceivedHi"></param>
        /// <param name="ShortageQuantityLo"></param>
        /// <param name="ShortageQuantityHi"></param>
        /// <param name="ShortageValueLo"></param>
        /// <param name="ShortageValueHi"></param>
        /// <param name="IsShortageRefundIssue"></param>
        /// <param name="ShortShipmentStatus"></param>
        /// <param name="GINumberLo"></param>
        /// <param name="GINumberHi"></param>
        /// <param name="IsPOHub"></param>
        /// <param name="Buyer"></param>
        /// <returns></returns>
        public static DataTable DataListNugget_Export(System.Int32? clientID, System.Int32? SortIndex, System.Int32? SortDir, System.String Supplier, System.Int32? PurchaseOrderNoLo, System.Int32? PurchaseOrderNoHi, DateTime? DateReceived, System.Int32? QuantityOrderedLo, System.Int32? QuantityOrderedHi, System.Int32? QuantityReceivedLo, System.Int32? QuantityReceivedHi, System.Int32? ShortageQuantityLo, System.Int32? ShortageQuantityHi, System.Double? ShortageValueLo, System.Double? ShortageValueHi, System.Boolean? IsShortageRefundIssue, System.Int32? ShortShipmentStatus, System.Int32? GINumberLo, System.Int32? GINumberHi, System.Boolean? IsPOHub, System.Int32? Buyer, System.Boolean IsRecentOnly)
        {
            DataTable dt = Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.DataListNugget_Export(clientID, SortIndex, SortDir, Supplier, PurchaseOrderNoLo, PurchaseOrderNoHi, DateReceived, QuantityOrderedLo, QuantityOrderedHi, QuantityReceivedLo, QuantityReceivedHi, ShortageQuantityLo, ShortageQuantityHi, ShortageValueLo, ShortageValueHi, IsShortageRefundIssue, ShortShipmentStatus, GINumberLo, GINumberHi, IsPOHub, Buyer, IsRecentOnly);
            return dt;
        }
        public static bool CancelShortShipment(System.Int32? shortShipmentId, System.Int32? loginId, System.Int32? clientId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.CancelShortShipment(shortShipmentId, loginId, clientId, ref NoReplyId, ref NoReplyEmail);
        }
        public static bool CloseShortShipment(System.Int32? shortShipmentId, System.Int32? loginId, System.Int32? clientId, ref System.Int32 NoReplyId, ref System.String NoReplyEmail)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.ShortShipment.CloseShortShipment(shortShipmentId, loginId, clientId, ref NoReplyId, ref NoReplyEmail);
        }
        #endregion
    }
}
