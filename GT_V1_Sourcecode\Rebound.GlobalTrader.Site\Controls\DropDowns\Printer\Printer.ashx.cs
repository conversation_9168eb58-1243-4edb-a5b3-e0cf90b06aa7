//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Printer : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {
        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("Printer");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.ClientID });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData))
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.Printer> lst = BLL.Printer.DropDownForPrinter(SessionManager.ClientID);
                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].PrinterId);
                    jsnItem.AddVariable("Name", lst[i].PrinterName);
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                lst.Clear(); lst = null;
                jsn.AddVariable("Printers", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
