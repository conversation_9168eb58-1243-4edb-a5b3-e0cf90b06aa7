<%@ Control Language="C#" CodeBehind="Invoices_Confirm.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.Invoices_Confirm" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "Invoice_Email")%></Explanation>
	<Content>
        <style type="text/css">
            #ctl00_cphMain_ctlInvoices_ctlDB_ctl13_ctlConfirm_ctlDB_ctlFormat_tdTitle {
                padding-top: 7px;
            }

            #ctl00_cphMain_ctlInvoices_ctlDB_ctl13_ctlConfirm_ctlDB_ctlFormat_tdItem{
                 padding-left: 3px;
             }
        </style>
		<ReboundUI_Form:LabelFormField id="ctlLine" runat="server" />
		<ReboundUI_Table:Form ID="frm1" runat="server">
            <ReboundUI_Form:FormField runat="server" id="ctlFormat" FieldID="radFormat" ResourceTitle="InvoiceFormat" CssClass="vertical-align-middle">
                    <Field>
                        <style type="text/css">
							.invoice-radio-format{
								margin-left: 3px;
							}
						</style>
                        <asp:RadioButtonList ID="radFormat" CssClass="invoice-radio-format" name="radioList" runat="server" RepeatDirection="Horizontal">
                            <asp:ListItem Text="PDF Format" Value="PDF" Class="optionPDF" Selected="True"/>
                            <asp:ListItem Text="XML Format" Value="XML" Class="optionXML invisible" />
                        </asp:RadioButtonList>
                    </Field>
            </ReboundUI_Form:FormField>
        </ReboundUI_Table:Form>

		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
