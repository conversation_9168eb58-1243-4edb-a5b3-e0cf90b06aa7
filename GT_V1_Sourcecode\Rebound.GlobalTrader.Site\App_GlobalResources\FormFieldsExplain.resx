<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AppSettingsEdit_IncludeShippingOnHomepageStats" xml:space="preserve">
    <value>Choose whether to include shipping in the calculations for 'All Statistics' on the homepage</value>
  </data>
  <data name="AppSettingsEdit_MaxStockImages" xml:space="preserve">
    <value>Limit for the number of images that can be added to a Stock bin on the Stock detail page</value>
  </data>
  <data name="Setup_Warehouse_Virtual" xml:space="preserve">
    <value>Virtual warehouses are hidden from the Orders screens, only showing in Warehouse functions</value>
  </data>
  <data name="SOClose_ResetQuantities" xml:space="preserve">
    <value>Choose whether all lines have their quantities set back to the amount shipped (or zero if none has been shipped)</value>
  </data>
  <data name="AppSettingsEdit_OwnDataVisibleToOthers" xml:space="preserve">
    <value>Choose whether to allow other client companies to view Excess, Offers  and History data </value>
  </data>
  <data name="StockSplit_ShouldPhotosBeCopied" xml:space="preserve">
    <value>Choose whether any photos should be copied over to the split stock</value>
  </data>
  <data name="AppSettingsEdit_AutoApprovePO" xml:space="preserve">
    <value>Choose whether the PO will be approved on creation or will need to be manually approved</value>
  </data>
  <data name="AppSettingsEdit_DefaultPORating" xml:space="preserve">
    <value>Select a default rating for when you approve a vendor</value>
  </data>
  <data name="AppSettingsEdit_DefaultSORating" xml:space="preserve">
    <value>Select a default rating for when you approve a customer</value>
  </data>
  <data name="AppSettingsEdit_HomepageTopSalespeople" xml:space="preserve">
    <value>How many top salespeople should be shown on the homepage?</value>
  </data>
  <data name="AppSettingsEdit_AutoApproveSO" xml:space="preserve">
    <value>Choose whether the SO will be approved on creation or will need to be manually approved</value>
  </data>
  <data name="AppSettingsEdit_MaxPDFDocument" xml:space="preserve">
    <value>Limit for the number of PDF document that can be added to existing pages</value>
  </data>
  <data name="AppSettingsEdit_AutoExportSI" xml:space="preserve">
    <value>Supplier invoice will be automatically exported, if it is checked</value>
  </data>
  <data name="AppSettingsEdit_IncludeInvoiceEmbedImage" xml:space="preserve">
    <value>Invoice and Packing Slip will be print\email, if it is checked</value>
  </data>
  <data name="AppSettingsEdit_EditPromiseDate" xml:space="preserve">
    <value>Edit Date Promised period between current month and Date Promise month, if it is checked</value>
  </data>
  <data name="AppSettingsEdit_InvAutoExport" xml:space="preserve">
    <value>Activate auto invoice export</value>
  </data>
  <data name="AppSettingsEdit_IPOPurchasing" xml:space="preserve">
    <value>IPO Purchasing email group</value>
  </data>
  <data name="AppSettingsEdit_EnablePowerApp" xml:space="preserve">
    <value>You can enable and disable Power app workflow notifications.</value>
  </data>
  <data name="AppSettingsEdit_EICharges" xml:space="preserve">
    <value>This is the Enhanced Inspection changes configuration.</value>
  </data>
  <data name="AppSettingsEdit_SendEmail" xml:space="preserve">
    <value>To allow email sending on UAT</value>
  </data>
</root>