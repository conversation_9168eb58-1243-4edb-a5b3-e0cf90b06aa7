using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class Services : Base {

		protected override void GetData() {
			JsonObject jsn = new JsonObject();
			JsonObject jsnRowsArray = new JsonObject(true);
			List<Service> lst = BLL.Service.DataListNugget(
				SessionManager.ClientID
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
                //, GetFormValue_StringForNameSearch("Name")
                 , GetFormValue_StringForNameSearchDecode("Name")
				, GetFormValue_StringForSearch("Description")
				, GetFormValue_NullableInt("LotNo")
			);
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			foreach (BLL.Service sv in lst) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", sv.ServiceId);
				jsnRow.AddVariable("Desc", sv.ServiceDescription);
				jsnRow.AddVariable("Name", sv.ServiceName);
				jsnRow.AddVariable("Lot", sv.LotName);
				jsnRow.AddVariable("LotNo", sv.LotNo);
				jsnRow.AddVariable("Cost", Functions.FormatCurrency(sv.Cost, SessionManager.ClientCurrencyCode));
				jsnRow.AddVariable("Price", Functions.FormatCurrency(sv.Price, SessionManager.ClientCurrencyCode));
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			lst = null;
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
			AddFilterState("Name");
			AddFilterState("Description");
			AddFilterState("LotNo");
			base.AddFilterStates();
		}
	}
}
