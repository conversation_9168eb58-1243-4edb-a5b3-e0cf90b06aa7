Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.prototype={get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._ibtnPrint=$get(this._aryButtonIDs[0]);this._ibtnEmail=$get(this._aryButtonIDs[1]);this._frmConfirm=$find(this._aryFormIDs[0]);this._frmConfirm.addCancel(Function.createDelegate(this,this.hideConfirmForm));this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveCeaseComplete));this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm));this._ibtnEmail&&$R_IBTN.addClick(this._ibtnEmail,Function.createDelegate(this,this.showConfirmForm));this._ibtnPrint&&$R_IBTN.addClick(this._ibtnPrint,Function.createDelegate(this,this.printDebit));this.enableBulkButtons(!1);this._table.addMultipleSelectionChanged(Function.createDelegate(this,this.selectionMade));this._strPathToData="controls/DataListNuggets/DabitBulk";this._strDataObject="DabitBulk";Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._IsGlobalLogin=null,this._IsGSA=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.callBaseMethod(this,"dispose"))},enableBulkButtons:function(n){this._ibtnPrint&&$R_IBTN.enableButton(this._ibtnPrint,n);this._ibtnEmail&&$R_IBTN.enableButton(this._ibtnEmail,n)},selectionMade:function(){this.enableBulkButtons(this._table._arySelectedIndexes.length>0)},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin);this._objData.addParameter("PageSizeLimit",$("#ctl00_cphMain_ctlDebits_ctlDB_txtLimitResults").val())},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=this._IsGlobalLogin?[$RGT_nubButton_DebitNote(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue($R_FN.showSupplierMessage(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.SuppMessage)),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Date),$RGT_nubButton_PurchaseOrder(n.PONo,n.PurchaseOrder),$R_FN.setCleanTextValue(n.SupplierInvoice),$R_FN.setCleanTextValue(n.ClientName)]:[$RGT_nubButton_DebitNote(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue($R_FN.showSupplierMessage(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.SuppMessage)),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.Date),$RGT_nubButton_PurchaseOrder(n.PONo,n.PurchaseOrder),$R_FN.setCleanTextValue(n.SupplierInvoice)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlBuyerName").show(this._enmViewLevel!=0);this.getFilterField("ctlPohubOnly").show(this._blnPOHub);this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);this.getFilterField("ctlClientName").show(this._IsGlobalLogin||this._IsGSA)},showConfirmForm:function(){this._frmConfirm._strDebits=this._table._aryCurrentValues;this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},saveCeaseComplete:function(){this.hideConfirmForm()},printDebit:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/DebitMainInfo");n.set_DataObject("DebitMainInfo");n.set_DataAction("SaveDebitPrint");n.addParameter("DebitsPrintId",this._table._aryCurrentValues);n.addDataOK(Function.createDelegate(this,this.printDebitsaveComplete));n.addError(Function.createDelegate(this,this.printDebitError));n.addTimeout(Function.createDelegate(this,this.printDebitError));$R_DQ.addToQueue(n);$R_DQ.processQueue()},printDebitError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},printDebitsaveComplete:function(n){var t=n._result.Result,i=n._result.Url;window.open("Print.aspx?pro=61&id="+t,"winPrintBulkDebit","left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")},test:function(){}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);