﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class SendToMemberListProvider : DataAccess 
    {
        static private SendToMemberListProvider _instance = null;

        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>   
        
        static public SendToMemberListProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (SendToMemberListProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.Activitys.ProviderType));
                return _instance;
            }
        }
        public SendToMemberListProvider()
        {
            this.ConnectionString = Globals.Settings.SendToMemberList.ConnectionString;
        }

        public abstract List<SendToMemberListDetails> GetMembersLoginNoList(System.Int32 SecurityGroupNo);
        public abstract List<SendToMemberListDetails> GetRequiredSecurityGroupList();

    }
}
