//Marker     Changed by      Date         Remarks
//[0001]     <PERSON>     21/06/2022   /// code for converting dictionary to concurrent dictionary
// [0002] Soorya Vyas RP-2326/RP-1709   both strat offers and reverse logistics import tools are not showing on DMCC on live (added LoginId parameter)
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site {
	public class SecurityManager {

        #region Properties

        //[0001] code start
        //private Dictionary<BLL.SecurityFunction.List, bool> _dctSectionLevelPermissions = new Dictionary<Rebound.GlobalTrader.BLL.SecurityFunction.List, bool>();
        private ConcurrentDictionary<BLL.SecurityFunction.List, bool> _dctSectionLevelPermissions = new ConcurrentDictionary<Rebound.GlobalTrader.BLL.SecurityFunction.List, bool>();
        //public Dictionary<BLL.SecurityFunction.List, bool> SectionLevelPermissions {
        public ConcurrentDictionary<BLL.SecurityFunction.List, bool> SectionLevelPermissions
        {
            get { return _dctSectionLevelPermissions; }
			set { _dctSectionLevelPermissions = value; }
		}
        //[0001] code end

        private BLL.SiteSection.List _enmSiteSection;
		public BLL.SiteSection.List SiteSection {
			get { return _enmSiteSection; }
			set { _enmSiteSection = value; }
		}

		private BLL.SitePage.List _enmSitePage;
		public BLL.SitePage.List SitePage {
			get { return _enmSitePage; }
			set { _enmSitePage = value; }
		}

		private int _intLoginID;
		public int LoginID {
			get { return _intLoginID; }
			set { _intLoginID = value; }
		}
        /// <summary>
        /// It check data is logged-in client or other
        /// </summary>
        private bool _blnIsDataHasOtherClient = false;
        public bool IsDataHasOtherClient
        {
            get { return _blnIsDataHasOtherClient; }
            set { _blnIsDataHasOtherClient = value; }
        }

		#endregion

		#region Constructors

      
		public SecurityManager(BLL.SiteSection.List enmSection, int intLoginID) {
			_enmSiteSection = enmSection;
			_intLoginID = intLoginID;
			GetSectionLevelPermissions();
		}

		public SecurityManager(BLL.SiteSection.List enmSection, BLL.SitePage.List enmPage, int intLoginID,bool isDataOtherClient) {
			_enmSitePage = enmPage;
			_enmSiteSection = enmSection;
			_intLoginID = intLoginID;
            IsDataHasOtherClient = isDataOtherClient;
			GetSectionLevelPermissions();
		}

        public SecurityManager(int intLoginID)
        {
            //_enmSiteSection = enmSection;
            _intLoginID = intLoginID;
           // GetSectionLevelPermissions();
        }

        #endregion

        #region Methods
        //[0001] code start
        internal void GetSectionLevelPermissions() {
            List<BLL.SecurityFunction> lst = BLL.SecurityFunction.GetListPermissionsByLoginAndSiteSection(_intLoginID, (int)_enmSiteSection, IsDataHasOtherClient);
			foreach (BLL.SecurityFunction sec in lst) {
				if (!_dctSectionLevelPermissions.ContainsKey((BLL.SecurityFunction.List)sec.SecurityFunctionId)) {
                    //_dctSectionLevelPermissions.Add((BLL.SecurityFunction.List)sec.SecurityFunctionId, (bool)sec.IsAllowed);
                    _dctSectionLevelPermissions.TryAdd((BLL.SecurityFunction.List)sec.SecurityFunctionId, (bool)sec.IsAllowed);
                }
			}
			lst = null;
		}
        //[0001] code end
        internal bool CheckSectionLevelPermission(BLL.SecurityFunction.List enmFunction) {
			bool blnReturn = true; //default to true (so we don't have to add loads of data to the tables all the time)
			if (_dctSectionLevelPermissions != null) {
				if (_dctSectionLevelPermissions.ContainsKey(enmFunction)) blnReturn = _dctSectionLevelPermissions[enmFunction];
			}
			return blnReturn;
		}

		#endregion

		#region Static Methods

        //[0001] code start
		public static void StoreGeneralLevelPermissions(int intLoginID) {
			List<BLL.SecurityFunction> lst = BLL.SecurityFunction.GetListGeneralPermissionsByLogin(intLoginID);
            //Dictionary<BLL.SecurityFunction.List, bool> dct = new Dictionary<Rebound.GlobalTrader.BLL.SecurityFunction.List, bool>();
            ConcurrentDictionary<BLL.SecurityFunction.List, bool> dct = new ConcurrentDictionary<Rebound.GlobalTrader.BLL.SecurityFunction.List, bool>();
            foreach (BLL.SecurityFunction sec in lst) {
                //dct.Add((BLL.SecurityFunction.List)sec.SecurityFunctionId, (bool)sec.IsAllowed);
             dct.TryAdd((BLL.SecurityFunction.List)sec.SecurityFunctionId, (bool)sec.IsAllowed);
                

            }
			SessionManager.GeneralPermissions = dct;
			lst = null;
		}
        //[0001] code end

            //[0001] code start
        public static void StoreSectionLevelPermissions(int intLoginID) {
			List<BLL.SecurityFunction> lst = BLL.SecurityFunction.GetListSectionPermissionsByLogin(intLoginID);
            //Dictionary<BLL.SecurityFunction.List, bool> dct = new Dictionary<Rebound.GlobalTrader.BLL.SecurityFunction.List, bool>();
            ConcurrentDictionary<BLL.SecurityFunction.List, bool> dct = new ConcurrentDictionary<Rebound.GlobalTrader.BLL.SecurityFunction.List, bool>();
            foreach (BLL.SecurityFunction sec in lst) {
                //dct.Add((BLL.SecurityFunction.List)sec.SecurityFunctionId, (bool)sec.IsAllowed);
                dct.TryAdd((BLL.SecurityFunction.List)sec.SecurityFunctionId, (bool)sec.IsAllowed);
            }
			SessionManager.SectionPermissions = dct;
			lst = null;
		}
        
        public static void StoreWarehousePermissions()
        {
            //Dictionary<int, bool> dct = new Dictionary<int, bool>();
            ConcurrentDictionary<int, bool> dct = new ConcurrentDictionary<int, bool>();
            dct.TryAdd((int)BLL.SecurityFunction.List.Warehouse_ReceivePO, SecurityManager.CheckPermission1(BLL.SecurityFunction.List.Warehouse_ReceivePO));
            dct.TryAdd((int)BLL.SecurityFunction.List.Warehouse_ShipSO, SecurityManager.CheckPermission1(BLL.SecurityFunction.List.Warehouse_ShipSO));
            dct.TryAdd((int)BLL.SecurityFunction.List.Warehouse_ReceiveCRMA, SecurityManager.CheckPermission1(BLL.SecurityFunction.List.Warehouse_ReceiveCRMA));
            dct.TryAdd((int)BLL.SecurityFunction.List.Warehouse_ShipSRMA, SecurityManager.CheckPermission1(BLL.SecurityFunction.List.Warehouse_ShipSRMA));
            SessionManager.WarehousePermission = dct;
            dct = null;
        }

        //[0002] Start
        public static void StoreUtilityPermissions(int LoginId)
        {
            //Dictionary<int, bool> dct = new Dictionary<int, bool>();
            ConcurrentDictionary<int, bool> dct = new ConcurrentDictionary<int, bool>();
            dct.TryAdd((int)BLL.SecurityFunction.List.Utility_Strategic_Offers_Import_Tool, SecurityManager.CheckPermissionWithLoginId(BLL.SecurityFunction.List.Utility_Strategic_Offers_Import_Tool, LoginId));
            dct.TryAdd((int)BLL.SecurityFunction.List.Utility_Reverse_Logistics_Import_Tool, SecurityManager.CheckPermissionWithLoginId(BLL.SecurityFunction.List.Utility_Reverse_Logistics_Import_Tool, LoginId));
            dct.TryAdd((int)BLL.SecurityFunction.List.Utility_Offer_Import_Large, SecurityManager.CheckPermissionWithLoginId(BLL.SecurityFunction.List.Utility_Offer_Import_Large, LoginId));
            SessionManager.UtilityPermission = dct;
            dct = null;
        }
        //[0002] Start

        public static void StoreOrdersPermission(int loginId)
        {
            ConcurrentDictionary<int, bool> dct = new ConcurrentDictionary<int, bool>();
            dct.TryAdd((int)BLL.SecurityFunction.List.Orders_ProspectiveCrossSelling, SecurityManager.CheckPermissionWithLoginId(BLL.SecurityFunction.List.Orders_ProspectiveCrossSelling, loginId));
            dct.TryAdd((int)BLL.SecurityFunction.List.Orders_ProsCrossSellingDetail, SecurityManager.CheckPermissionWithLoginId(BLL.SecurityFunction.List.Orders_ProsCrossSellingDetail, loginId));
            SessionManager.OrdersPermission = dct;
        }

        //[0001] code end
        /// <summary>
        /// Checks a particular function to see if the user has permissions
        /// </summary>
        /// <param name="enmFunction"></param>
        /// <returns></returns>
        public static bool CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List enmFunction) {
			bool blnReturn = true; //default to true (so we don't have to add loads of data to the tables all the time)
			if (SessionManager.GeneralPermissions != null) {
				if (SessionManager.GeneralPermissions.ContainsKey(enmFunction)) {
					blnReturn = SessionManager.GeneralPermissions[enmFunction];
				}
			}
			return blnReturn;
		}

		/// <summary>
		/// Checks a particular function to see if the user has permissions (checked from session variables)
		/// </summary>
		/// <param name="enmFunction"></param>
		/// <returns></returns>
		public static bool CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List enmFunction) {
			bool blnReturn = true; //default to true (so we don't have to add loads of data to the tables all the time)
			if (SessionManager.SectionPermissions != null) {
				if (SessionManager.SectionPermissions.ContainsKey(enmFunction)) {
					blnReturn = SessionManager.SectionPermissions[enmFunction];
				}
			}
			return blnReturn;
		}

		/// <summary>
		/// Check if a user can view a section (checked from session variables)
		/// </summary>
		/// <param name="enmSection"></param>
		/// <returns></returns>
		public static bool CheckCanViewSection(BLL.SiteSection.List enmSection) {
			bool blnReturn = false;
			switch (enmSection) {
				case Rebound.GlobalTrader.BLL.SiteSection.List.Contact:
					blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.ContactSection_View);
					break;
				case Rebound.GlobalTrader.BLL.SiteSection.List.Orders:
					blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.OrdersSection_View);
					break;
				case Rebound.GlobalTrader.BLL.SiteSection.List.Warehouse:
					blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.WarehouseSection_View);
					break;
				case Rebound.GlobalTrader.BLL.SiteSection.List.Reports:
					blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.ReportsSection_View);
					break;
				case Rebound.GlobalTrader.BLL.SiteSection.List.Setup:
					blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.SetupSection_View);
					break;
				case Rebound.GlobalTrader.BLL.SiteSection.List.Profile:
					blnReturn = true;
					break;
                case Rebound.GlobalTrader.BLL.SiteSection.List.Dashboards:
                    blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.DashboardSection_View);
                    break;
                case Rebound.GlobalTrader.BLL.SiteSection.List.Utility:
                    blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Utility_View);
                    break;
                case Rebound.GlobalTrader.BLL.SiteSection.List.PowerBI:
                    blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.PowerBI_View);
                    break;
                case Rebound.GlobalTrader.BLL.SiteSection.List.BOMManager:
                    blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.BOMManager);
                    break;
                default:
					blnReturn = false;
					break;
			}
			return blnReturn;
		}

        /// <summary>
        /// Checks for a permission from the database, storing it in Session (cookie) on the first call
        /// </summary>
        /// <param name="enmFunction"></param>
        public static bool CheckPermission1(Rebound.GlobalTrader.BLL.SecurityFunction.List enmFunction)
        {
            bool blnReturn = true; //default to true (so we don't have to add loads of data to the tables all the time)
            try
            {
              //  if (SessionManager.GetPermission(enmFunction) == null)
               // {
                    SecurityFunction sf = SecurityFunction.GetPermissionByLogin((int)enmFunction, SessionManager.LoginID);
                    //SessionManager.StorePermission(enmFunction, (bool)sf.IsAllowed);
                    if (sf == null)
                        return true;
                    else
                    {
                        return (sf.IsAllowed.HasValue) ? sf.IsAllowed.Value : true;
                    }

                   // sf = null;
               // }
               // blnReturn = (bool)SessionManager.GetPermission(enmFunction);
            }
            catch
            {
                blnReturn = true;
            }
            return blnReturn;
        }

        //[0002] Start
        public static bool CheckPermissionWithLoginId(Rebound.GlobalTrader.BLL.SecurityFunction.List enmFunction, int LoginId)
        {
            bool blnReturn = true; //default to true (so we don't have to add loads of data to the tables all the time)
            try
            {
                //  if (SessionManager.GetPermission(enmFunction) == null)
                // {
                SecurityFunction sf = SecurityFunction.GetPermissionByLogin((int)enmFunction, LoginId);
                //SessionManager.StorePermission(enmFunction, (bool)sf.IsAllowed);
                if (sf == null)
                    return true;
                else
                {
                    return (sf.IsAllowed.HasValue) ? sf.IsAllowed.Value : true;
                }

                // sf = null;
                // }
                // blnReturn = (bool)SessionManager.GetPermission(enmFunction);
            }
            catch
            {
                blnReturn = true;
            }
            return blnReturn;
        }
        //[0002] End


        /// <summary>
        /// Checks for a permission from the database, storing it in Session (cookie) on the first call
        /// </summary>
        /// <param name="enmFunction"></param>
        public static bool CheckPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List enmFunction) {
			bool blnReturn = true; //default to true (so we don't have to add loads of data to the tables all the time)
			try {
				if (SessionManager.GetPermission(enmFunction) == null) {
					SecurityFunction sf = SecurityFunction.GetPermissionByLogin((int)enmFunction, SessionManager.LoginID);
					SessionManager.StorePermission(enmFunction, (bool)sf.IsAllowed);
					sf = null;
				}
				blnReturn = (bool)SessionManager.GetPermission(enmFunction);
			} catch {
				blnReturn = true;
			}
			return blnReturn;
		}

		/// <summary>
		/// Checks a particular report to see if the user has permissions to view it
		/// </summary>
		/// <param name="enmFunction"></param>
		/// <returns></returns>
		public static bool CheckReportPermission(int intReportID) {
			bool blnReturn = true; //default to true (so we don't have to add loads of data to the tables all the time)
			BLL.SecurityFunction sf = BLL.SecurityFunction.GetReportPermissionForLogin(SessionManager.LoginID, intReportID);
			if (sf != null) blnReturn = (bool)sf.IsAllowed;
			return blnReturn;
		}

        /// <summary>
        /// Checks report to view by user 
        /// </summary>
        /// <param name="enmFunction"></param>
        /// <returns></returns>
        public static bool IsIpoPriceEdit(int intLoginId)
        {
            bool blnCanEdit = true;
            blnCanEdit = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.PriceEditPoHub);

            return blnCanEdit;
        }

        /// <summary>
        /// Checks report to view by user 
        /// </summary>
        /// <param name="enmFunction"></param>
        /// <returns></returns>
        public static bool IsCompanyStatusStopEdit(int intLoginId)
        {
            bool blnCanEdit = true;
            blnCanEdit = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.PriceEditPoHub);

            return blnCanEdit;
        }

        /// <summary>
        /// Checks report to view by user 
        /// </summary>
        /// <param name="enmFunction"></param>
        /// <returns></returns>
        public static bool IsViewOnlyMyReport(int intLoginId)
        {
            bool blnReturn = false;
            blnReturn = CheckGeneralPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.ViewOnlyMyReports);

            return blnReturn;
        }

        public static Int32 CheckSecurityLevel()
        {
            Int32 blnReturn = 1;
            blnReturn = CheckReportLevelPermission((int)Rebound.GlobalTrader.BLL.SecurityFunction.List.ViewOnlyMyReports);

            return blnReturn;
        }

        /// <summary>
        /// Checks a particular report to see if the user has permissions to view it
        /// </summary>
        /// <param name="enmFunction"></param>
        /// <returns></returns>
        public static Int32 CheckReportLevelPermission(int intReportID)
        {
            Int32 blnReturn = 1; //default to true (so we don't have to add loads of data to the tables all the time)
            BLL.SecurityFunction sf = BLL.SecurityFunction.GetSecurityLevelPermissionByLogin(intReportID,SessionManager.LoginID);
            if (sf != null) blnReturn = (Int32)sf.SecurityLevel;
            return blnReturn;
        }
		#endregion
	}
}