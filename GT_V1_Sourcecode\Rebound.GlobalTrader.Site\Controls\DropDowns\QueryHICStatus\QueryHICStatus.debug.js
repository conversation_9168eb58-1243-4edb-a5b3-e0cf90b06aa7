///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// add comment here on any change
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus = function (element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function () {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.callBaseMethod(this, "dispose");
    },

    setupDataCall: function () {
        this._objData.set_PathToData("controls/DropDowns/QueryHICStatus");
        this._objData.set_DataObject("QueryHICStatus");
        this._objData.set_DataAction("GetData");
    },

    dataCallOK: function () {
        var result = this._objData._result;
        if (result != null) {
            if (result.Types) {
                for (var i = 0; i < result.Types.length; i++) {
                    this.addOption(result.Types[i].Name, result.Types[i].ID);
                }
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.QueryHICStatus", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
