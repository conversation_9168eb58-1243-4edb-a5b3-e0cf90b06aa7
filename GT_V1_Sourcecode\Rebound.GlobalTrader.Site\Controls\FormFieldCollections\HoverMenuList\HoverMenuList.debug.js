///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList = function(element) {
    Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.initializeBase(this, [element]);
    this._aryComponents = [];
    this._intCheckBoxNo = 0;
    this._intHeadingNo = -1;
    this._intMyTabHeading = -1;
    this._str="<div class=\"sidebarmenu\"><ul id=\"sidebarmenu1\">";
    this._isCall=false;
    this._prefix="";
    this._isULcreated = false;
    this._txtReason="";
    this._txtReasonHidden="";
    
    
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.prototype = {

    get_pnlSubMenu: function() { return this._pnlSubMenu; }, set_pnlSubMenu: function(v) { if (this._pnlSubMenu !== v) this._pnlSubMenu = v; },
    get_txtReason: function() { return this._txtReason; }, set_txtReason: function(v) { if (this._txtReason !== v) this._txtReason = v; },
    get_txtReasonHidden: function() { return this._txtReasonHidden; }, set_txtReasonHidden: function(v) { if (this._txtReasonHidden !== v) this._txtReasonHidden = v; },
    get_ibtnIcon: function() { return this._ibtnIcon; }, set_ibtnIcon: function(v) { if (this._ibtnIcon !== v) this._ibtnIcon = v; },
    initialize: function() {

        this.getCategory();
        $R_FN.showElement(this._pnlSubMenu, false);
        if (this._ibtnIcon) {
            $R_IBTN.addClick(this._ibtnIcon, Function.createDelegate(this, this.showMenuList));
            if (this._pnlSubMenu) $addHandler(this._pnlSubMenu, "click", Function.createDelegate(this, this.hideMenuList));
        }
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.callBaseMethod(this, "initialize");

    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this.get_element()) $clearHandlers(this.get_element());
        this._tbl = null;
        this._aryComponents = null;
        this._intCheckBoxNo = null;
        this._intHeadingNo = null;
        this._intMyTabHeading = null;
        this._txtReason = null;
        this._txtReasonHidden = null;
        this._ibtnIcon = null;
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.callBaseMethod(this, "dispose");
    },

    initmenu: function() {
        this._isCall = true;
        var menuids = ["sidebarmenu1"] //Enter id(s) of each Side Bar Menu's main UL, separated by commas
        for (var x = 0; x < menuids.length; x++) {
            //var ultags=document.getElementById(menuids[x]).getElementsByTagName("ul")
            var ultags = [];
            var ultags1 = document.getElementsByClassName('sidebarmenu');
            for (var i = 0; i < ultags1.length - 1; i++) {
                var tmp1 = ultags1[i].getElementsByTagName("ul")[0].getElementsByTagName("ul");
                for (var j = 0; j < tmp1.length; j++)
                    ultags.push(tmp1[j]);
            }

            for (var t = 0; t < ultags.length; t++) {
                ultags[t].parentNode.getElementsByTagName("a")[0].className += " subfolderstyle"
                if (ultags[t].parentNode.parentNode.id == menuids[x]) { //if this is a first level submenu
                    ultags[t].style.left = ultags[t].parentNode.offsetWidth + "120px" //dynamically position first level submenus to be width of main menu item
                    ultags[t].parentNode.onclick = function() {
                        this.getElementsByTagName("ul")[0].style.display = "none"

                    }
                }
                else //else if this is a sub level submenu (ul)
                    ultags[t].style.left = ultags[t - 1].getElementsByTagName("a")[0].offsetWidth + "px" //position menu to the right of menu item that activated it
                ultags[t].parentNode.onmouseover = function() {
                    this.getElementsByTagName("ul")[0].style.display = "block"
                }
                ultags[t].parentNode.onmouseout = function() {
                    this.getElementsByTagName("ul")[0].style.display = "none"
                }

            }
            for (var t = ultags.length - 1; t > -1; t--) { //loop through all sub menus again, and use "display:none" to hide menus (to prevent possible page scrollbars
                ultags[t].style.visibility = "visible"
                ultags[t].style.display = "none"
            }
        }
    },
    getCategory: function() {

        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMALines");
        obj.set_DataObject("CRMALines");
        obj.set_DataAction("GetCategory");
        obj.addDataOK(Function.createDelegate(this, this.getCategoryOK));
        obj.addError(Function.createDelegate(this, this.getCategoryError));
        obj.addTimeout(Function.createDelegate(this, this.getCategoryError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;


    },

    getCategoryOK: function(args) {
        var res = args._result;
        var aryData, row;
        if (res != null)
            this.addItem(res.EightDCode, 0);
    },

    addItem: function(res, i) {
        if (res != null) {
            for (var i = 0; i < res.length; i++) {
                var isEOUL = false;
                row = res[i];
                if (i == 0 || (i > 0 && res[i].Prefix != res[i - 1].Prefix)) {
                    if (i != 0)
                        this._str += "</ul></li>";

                    this._str += "<li><a href=\"javascript:void(0);\">" + row.CategoryName + "</a><ul>";

                    this._str += String.format("<li><a href=\"javascript:void(0);\" onclick=\"$find('{0}').setSubCategory('{1}',{2});return false;\">{3}</a></li>", this._element.id, row.CatSubCode, row.SubCatId, row.SubCategory);
                }
                else {
                    this._str += String.format("<li><a href=\"javascript:void(0);\" onclick=\"$find('{0}').setSubCategory('{1}',{2});return false;\">{3}</a></li>", this._element.id, row.CatSubCode, row.SubCatId, row.SubCategory);
                }

                if (i == res.length - 1) {
                    this._str += "</ul></li></ul></div>";
                    this._pnlSubMenu.innerHTML = this._str;
                    this.initmenu();
                }
               
            }
        }
    },
    getCategoryError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },
    setSubCategory: function(value, val1) {
        this._txtReason.value = value;
        this._txtReasonHidden.value = val1;

    },
    getSubCategory: function() {
        return this._txtReasonHidden.value;
        //this._txtReason.value;
    },
    showMenuList: function() {
        $R_FN.showElement(this._pnlSubMenu, true);
        //alert("test");
    },
    hideMenuList: function() {
        $R_FN.showElement(this._pnlSubMenu, false);
    },
    blankReason: function() {
        this._txtReason.value = "";
        this._txtReasonHidden.value = "";
        $R_FN.showElement(this._pnlSubMenu, false);
    },
    hidMenuPanel:function()
    {
          $R_FN.showElement(this._pnlSubMenu, false);
    }

};
Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.HoverMenuList", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);

