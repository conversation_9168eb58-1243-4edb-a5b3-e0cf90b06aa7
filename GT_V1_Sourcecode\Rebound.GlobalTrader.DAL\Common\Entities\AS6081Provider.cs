﻿//Marker     Changed by               Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>           31/08/2023   Add new module AS6081 for RP-2226
//[002]      <PERSON>             08/09/2023   RP-2226 GET Alert message at the time of creating SO, PO, stocks etc.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;
using Rebound.GlobalTrader.DAL.Common.Entities;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class AS6081Provider : DataAccess
    {
        static private AS6081Provider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public AS6081Provider Instance
        {
            get
            {
                if (_instance == null) _instance = (AS6081Provider)Activator.CreateInstance(Type.GetType(Globals.Settings.AS6081s.ProviderType));
                return _instance;
            }
        }
        public AS6081Provider()
        {
            this.ConnectionString = Globals.Settings.AS6081s.ConnectionString;
        }

        #region Method Registrations

        #region Type Of Supplier Setup Section
        public abstract Int32 InsertTypeOfSupplier(System.String name,System.Int32? LoginId);
        public abstract List<AS6081Details> GetListTypeOfSupplier();
        public abstract bool UpdateTypeOfSupplier(System.Int32? TypeOfSupplierId, System.String Name, System.Int32? LoginId);
        public abstract bool DeleteTypeOfSupplier(System.Int32? TypeOfSupplierId);
        public abstract Int32 ValidateTypeOfSupplier(System.Int32? ID,System.String Name, System.Int32? Screen);
        #endregion

        #region Reason For Chosen Supplier Setup Section
        public abstract Int32 InsertReasonForChosenSupplier(System.String name, System.Int32? LoginId);
        public abstract List<AS6081Details> GetListReasonForChosenSupplier();
        public abstract bool UpdateReasonForChosenSupplier(System.Int32? RiskOfSupplierId, System.String Name, System.Int32? LoginId);
        public abstract bool DeleteReasonForChosenSupplier(System.Int32? RiskOfSupplierId);
        public abstract Int32 ValidateReasonForChosenSupplier(System.Int32? ID, System.String Name, System.Int32? Screen);
        #endregion

        #region Risk Of Supplier Setup Section
        public abstract Int32 InsertRiskOfSupplier(System.String name, System.Int32? LoginId);
        public abstract List<AS6081Details> GetListRiskOfSupplier();
        public abstract bool UpdateRiskOfSupplier(System.Int32? RiskOfSupplierId, System.String Name, System.Int32? LoginId);
        public abstract bool DeleteRiskOfSupplier(System.Int32? RiskOfSupplierId);
        public abstract Int32 ValidateRiskOfSupplier(System.Int32? ID, System.String Name, System.Int32? Screen);
        #endregion

        #region Master Dropdowns for AS6081
        public abstract List<AS6081Details> DropDownTypeOfSupplier();
        public abstract List<AS6081Details> DropDownReasonForSupplier();
        public abstract List<AS6081Details> DropDownRiskOfSupplier();

        //[002] start
        public abstract AS6081AlertMessageDetails GetAlertMessageById(System.Int32 alertMessageId); //[002]
        public abstract AS6081AlertMessageDetails GetAlertMessageByOperationType(System.String operationType);//[002]

        public abstract AS6081AlertMessageDetails GetAlertMessageByOperationTypeForSoPrint(System.String operationType, System.Int32 soId);

        //[002] end

        public abstract List<AS6081Details> DropDownAssignSecurityGroup(System.Int32? ClientNo);

        #endregion

        #region Approver change history
        public abstract List<AS6081Details> GetApproverChnageLog(System.Int32? ID);
        public abstract AS6081Details GetCountry(System.Int32? SupplierNos);
        #endregion

        #endregion

    }
}
