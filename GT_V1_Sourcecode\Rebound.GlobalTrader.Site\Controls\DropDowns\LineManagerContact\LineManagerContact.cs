﻿//Marker     Changed by               Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>           28/01/2022   Add class for Line Manager Contact.
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class LineManagerContact : Base {
		
		protected override void OnInit(EventArgs e) {
			CanAddTo = false;
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("LineManagerContact");
            AddScriptReference("Controls.DropDowns.LineManagerContact.LineManagerContact");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.LineManagerContact", ClientID);
			base.OnLoad(e);
		}

	}
}