﻿//Marker     Changed by      Date         Remarks    
//[001]      Vinay           16/10/2012   Display supplier type in stock grid  
//[002]      Soorya Vyas     20/03/2023    RP-1019 Win32Exception excecution TimeOut issue
//[004]      Soorya Vyas    14-04-2023   [RP-1421] add revers logistic similar to strategic offer on HUBRFQ page
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlOfferProvider : OfferProvider {
		/// <summary>
		/// Delete Offer
		/// Calls [usp_delete_Offer]
		/// </summary>
		public override bool Delete(System.Int32? offerId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_delete_Offer", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@OfferId", SqlDbType.Int).Value = offerId;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to delete Offer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
		
		/// <summary>
		/// Create a new row
        /// Calls [usp_insert_OfferNew]
		/// </summary>
        public override Int32 Insert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? cleintNo, bool? isPoHub)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
            string proc = "usp_insert_OfferNew";
			try {
                proc = isPoHub == true ? "usp_insert_OfferNewPH" : proc;
				cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
				cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
				cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
				cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
				cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
				cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
				cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
				cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
				cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = originalEntryDate;
				cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
				cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
				cmd.Parameters.Add("@SupplierName", SqlDbType.NVarChar).Value = supplierName;
				cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
				cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
				cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = cleintNo;                
				cmd.Parameters.Add("@OfferId", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (Int32)cmd.Parameters["@OfferId"].Value;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to insert Offer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_OfferNew]
        /// </summary>
        public override Int32 IPOBOMInsert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? cleintNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_insert_IPOOffer";
            try
            {
                //proc = isPoHub == true ? "usp_ipobom_insert_OfferNewPH" : proc;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = originalEntryDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@SupplierName", SqlDbType.NVarChar).Value = supplierName;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = cleintNo;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = msl;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = spq;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = isPoHub;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@OfferId", SqlDbType.Int).Direction = ParameterDirection.Output;
                
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@OfferId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override Int32 BOMManagerAPIOfferInsert(int Supplier,
                                                       string SupplierName,
                                                       string PartNo,
                                                       int ROHS,
                                                       int? ManufacturerNo,
                                                       string ManufacturerName,
                                                       string DateCode,
                                                       int? ProductNo,
                                                       string ProductName,
                                                       int? PackageNo,
                                                       string PackageName,
                                                       int? Quantity,
                                                       double? Price,
                                                       int? Currency,
                                                       int OfferStatus,
                                                       string SupplierTotalQSA,
                                                       string SupplierMOQ,
                                                       string SupplierLTB,
                                                       int MSLNo,
                                                       string SPQ,
                                                       string LeadTime,
                                                       string FactorySealed,
                                                       string ROHSStatus,
                                                       string Notes,
                                                       int BOMManagerID,
                                                       int ClientID,
                                                       int alterCRNumber,
                                                       int? supplierWarranty,
                                                       int? countryOfOriginNo,
                                                       double? sellPrice,
                                                       double? shippingCost,
                                                       string reason,
                                                       int regionNo,
                                                       DateTime? deliveryDate,
                                                       bool? isTestingRecommended)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_insert_Offer_APIBOMManager";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn)
                {
                    CommandType = CommandType.StoredProcedure
                };
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = PartNo;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = ManufacturerNo;
                cmd.Parameters.Add("@ManufacturerName", SqlDbType.NVarChar).Value = ManufacturerName;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = DateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = ProductNo;
                cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = ProductName;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = PackageNo;
                cmd.Parameters.Add("@PackageName", SqlDbType.NVarChar).Value = PackageName;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = Quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = Price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = Currency;
                cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = DateTime.Now;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = Supplier;
                cmd.Parameters.Add("@SupplierName", SqlDbType.NVarChar).Value = SupplierName;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = ROHS;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = OfferStatus;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = Notes;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = SupplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = SupplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = SupplierMOQ;
                //cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSLNo;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = SPQ;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = LeadTime;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = FactorySealed;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = ROHSStatus;
                cmd.Parameters.Add("@MSLNo", SqlDbType.Int).Value = MSLNo;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@ClientID", SqlDbType.Int).Value = ClientID;
                cmd.Parameters.Add("@AlterCRNumber", SqlDbType.Int).Value = alterCRNumber;

                cmd.Parameters.Add("@SupplierWarranty", SqlDbType.Int).Value = supplierWarranty;
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = countryOfOriginNo;
                cmd.Parameters.Add("@SellPrice", SqlDbType.Float).Value = sellPrice;
                cmd.Parameters.Add("@ShippingCost", SqlDbType.Float).Value = shippingCost;
                cmd.Parameters.Add("@SellPriceLessReason", SqlDbType.NVarChar).Value = reason;
                cmd.Parameters.Add("@RegionNo", SqlDbType.Int).Value = regionNo;
                cmd.Parameters.Add("@DeliveryDate", SqlDbType.DateTime).Value = deliveryDate;
                cmd.Parameters.Add("@TestingRecommended", SqlDbType.Bit).Value = isTestingRecommended;

                cmd.Parameters.Add("@APIOfferId", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@APIOfferId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Create a new row
        /// Calls [usp_offer_clone_AddToRequirement]
        /// </summary>
        public override Int32 CloneOfferAddToReq(System.Int32 offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? cleintNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            //string proc = "usp_insert_IPOOffer";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_offer_clone_AddToRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = originalEntryDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@SupplierName", SqlDbType.NVarChar).Value = supplierName;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = cleintNo;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = msl;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = spq;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = isPoHub;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@OfferId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar,150).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@OfferId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Create a new row
        /// Calls [usp_AltPartInfo_clone_AddToRequirement]
        /// </summary>
        public override Int32 CloneAltPartInfoAddToReq(System.Int32 AltpartInfoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? cleintNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            //string proc = "usp_insert_IPOOffer";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AltPartInfo_clone_AddToRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@AltpartInfoId", SqlDbType.Int).Value = AltpartInfoId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = originalEntryDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@SupplierName", SqlDbType.NVarChar).Value = supplierName;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = cleintNo;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = msl;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = spq;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = isPoHub;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@AltpartInfoIdnew", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar,150).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@AltpartInfoIdnew"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
		

        //codee add for CrossMatch Clone
        /// <summary>
        /// Insert
        /// Calls [usp_offer_clone_CrossMatchRequirement]
        /// </summary>
        public override Int32 CloneOfferCrossMatchReq(System.Int32 offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? cleintNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo, out System.String strLinkMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strLinkMessage = "";
            //string proc = "usp_insert_IPOOffer";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_offer_clone_CrossMatchRequirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = originalEntryDate;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@SupplierName", SqlDbType.NVarChar).Value = supplierName;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = cleintNo;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = msl;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = spq;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = isPoHub;
                cmd.Parameters.Add("@CustomerRequirementNo", SqlDbType.Int).Value = customerRequirementNo;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@OfferId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@LinkCurrencyMsg", SqlDbType.VarChar,150).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strLinkMessage = (string)cmd.Parameters["@LinkCurrencyMsg"].Value;
                return (Int32)cmd.Parameters["@OfferId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
            //end

       

        /// <summary>
        /// Get 
		/// Calls [usp_select_Offer]
        /// </summary>
		public override OfferDetails Get(System.Int32? offerId,bool? isPoHub) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
            string proc = "usp_select_Offer";
			try {
                //proc = isPoHub == true ? "usp_select_OfferPH" : proc;
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand(proc, cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@OfferId", SqlDbType.Int).Value = offerId;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
				if (reader.Read()) {
					//return GetOfferFromReader(reader);
					OfferDetails obj = new OfferDetails();
					obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
					obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
					obj.Part = GetReaderValue_String(reader, "Part", "");
					obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
					obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
					obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
					obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
					obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
					obj.Price = GetReaderValue_Double(reader, "Price", 0);
					obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
					obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
					obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
					obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
					obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
					obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
					obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
					obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
					obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
					obj.Notes = GetReaderValue_String(reader, "Notes", "");
					obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
					obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
					obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.RoHSStatus= GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.productNameDescrip = GetReaderValue_String(reader, "productNameDescrip", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    return obj;
				} else {
					return null;
				}
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Offer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


        /// <summary>
        /// Get 
        /// Calls [usp_select_AltPart]
        /// </summary>
        public override OfferDetails GetAltPart(System.Int32? AlternativePartId, bool? isPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_select_AltPart";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@AlternativePartId", SqlDbType.Int).Value = AlternativePartId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetOfferFromReader(reader);
                    OfferDetails obj = new OfferDetails();
                    obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.productNameDescrip = GetReaderValue_String(reader, "productNameDescrip", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Get 
        /// Calls [usp_select_EditEpo]
        /// </summary>
        public override OfferDetails GetEpo(System.Int32? EpoId, bool? isPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_select_EditEpo";
            try
            {
                //proc = isPoHub == true ? "usp_select_OfferPH" : proc;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@EpoId", SqlDbType.Int).Value = EpoId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetOfferFromReader(reader);
                    OfferDetails obj = new OfferDetails();
                    obj.EpoId = GetReaderValue_Int32(reader, "EpoId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.EpoStatusNo = GetReaderValue_NullableInt32(reader, "EpoStatusNo", null);
                    obj.EpoStatusChangeDate = GetReaderValue_NullableDateTime(reader, "EpoStatusChangeDate", null);
                    obj.EpoStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "EpoStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.productNameDescrip = GetReaderValue_String(reader, "productNameDescrip", "");
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.Description = GetReaderValue_String(reader, "Description", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Get 
        /// Calls [usp_select_EditGetReverseLogistic]
        /// </summary>
        public override OfferDetails GetReverseLogistic(System.Int32? ReverseLogisticId, bool? isPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_select_EditReverseLogistic";
            try
            {
                //proc = isPoHub == true ? "usp_select_OfferPH" : proc;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ReverseLogisticId", SqlDbType.Int).Value = ReverseLogisticId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetOfferFromReader(reader);
                    OfferDetails obj = new OfferDetails();
                    obj.ReverseLogisticId = GetReaderValue_Int32(reader, "ReverseLogisticId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ReverseLogisticStatusNo = GetReaderValue_NullableInt32(reader, "ReverseLogisticStatusNo", null);
                    obj.ReverseLogisticStatusChangeDate = GetReaderValue_NullableDateTime(reader, "ReverseLogisticStatusChangeDate", null);
                    obj.ReverseLogisticStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "ReverseLogisticStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.productNameDescrip = GetReaderValue_String(reader, "productNameDescrip", "");
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.Description = GetReaderValue_String(reader, "Description", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get CrossMatch Auto Search Details
        /// Calls [usp_CrossMatch_SearchLog_select]
        /// </summary>
        public override OfferDetails GetCrossMatchAutoSearch(System.Int32 BomId, System.Int32 userId, int ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_CrossMatch_SearchLog_select";
            try
            {
            
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = BomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = userId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.LogDetails = GetReaderValue_String(reader, "LogDetails", "");
                    
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
		
		
        /// <summary>
        /// Source 
		/// Calls [usp_source_Offer]
        /// </summary>
        public override List<OfferDetails> Source(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
            outDate = null;
			try {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_source_Offer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //[002] Start
                //cmd.CommandTimeout = 30; 
				cmd.CommandTimeout = 120;
                //[002] End
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
				cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = sortIndex;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDirection;
                
                
                
                cn.Open();
				//DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();

				List<OfferDetails> lst = new List<OfferDetails>();
				while (reader.Read()) {
					OfferDetails obj = new OfferDetails();
					obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
					obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
					obj.Part = GetReaderValue_String(reader, "Part", "");
					obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
					obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
					obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
					obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
					obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
					obj.Price = GetReaderValue_Double(reader, "Price", 0);
					obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
					obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
					obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
					obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
					obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
					obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
					obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
					obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
					obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
					obj.Notes = GetReaderValue_String(reader, "Notes", "");
					obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
					obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
					obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
					obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
					obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
					obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
					obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
					obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
					obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
					obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
					obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
					obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
					obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", "");
                    lst.Add(obj);
					obj = null;
				}
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Offers", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

        /// <summary>
        /// Source 
		/// Calls [usp_source_OfferPH]
        /// </summary>
        public override List<OfferDetails> SourceArchive(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_source_OfferPH", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //[002] Start
                //cmd.CommandTimeout = 30; 
                cmd.CommandTimeout = 120;
                //[002] End
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = sortIndex;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDirection;



                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();

                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", "");
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Source
        /// Calls [usp_OfferAPIBOMManager]
        /// </summary>
        public override List<APIExternalLinksDetails> OfferAPIBOMManager(int BOMManagerID, out DataTable Parts, string Part, int? CustomerReqID, int curPage, int Rpp)
        {
            Parts = new DataTable();
            Parts.Columns.Add("Part");
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_OfferAPIBOMManager", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@CustomerRequirementID", SqlDbType.Int).Value = CustomerReqID;
                //cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = Part;
                //cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = StartDate;
                //cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = EndDate;
                cmd.Parameters.Add("@curPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = Rpp;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<APIExternalLinksDetails> lst = new List<APIExternalLinksDetails>();
                while (reader.Read())
                {
                    APIExternalLinksDetails obj = new APIExternalLinksDetails();
                    obj.FElectronicsId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    //obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //obj.EpoStatusNo = GetReaderValue_NullableInt32(reader, "EpoStatusNo", null);
                    //obj.EpoStatusChangeDate = GetReaderValue_NullableDateTime(reader, "EpoStatusChangeDate", null);
                    //obj.EpoStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "EpoStatusChangeLoginNo", null);                   
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.EpoStatusChangeEmployeeName = GetReaderValue_String(reader, "EpoStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    //obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);                   
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    //obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    //obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    //obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierEpo = GetReaderValue_String(reader, "SupplierEpo", "");
                    obj.VirtualCostPrice = GetReaderValue_String(reader, "VirtualCostPrice", "");

                    obj.Quantity = GetReaderValue_Int32(reader, "QuantityAvailable", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "QuantityInSupplier", "");

                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.GTDate = GetReaderValue_NullableDateTime(reader, "GTDate", null);
                    obj.Description = GetReaderValue_String(reader, "Description", "");

                    //obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");

                    //[001] code start
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");

                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "PackageType", "");
                    obj.ECCN = GetReaderValue_String(reader, "ECCN", "");

                    //obj.Suppliertype = GetReaderValue_String(reader, "Suppliertype", "");
                    obj.PublishDate = GetReaderValue_NullableDateTime(reader, "PublishDate", null);
                    obj.UnitCostPrice = GetReaderValue_String(reader, "UnitCostPrice", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "MOQ", "");
                    obj.ApiSourceName = GetReaderValue_String(reader, "ApiSourceName", "");
                    obj.TotalCount = GetReaderValue_Int32(reader, "TotalRecords", 0);
                    obj.OfferAddFlag = GetReaderValue_Boolean(reader, "OfferAddFlag", false);
                    lst.Add(obj);
                    obj = null;
                }
                if(reader.NextResult())
                {
                while (reader.Read())
                    {
                        DataRow dr = Parts.NewRow();
                        dr["Part"] = GetReaderValue_String(reader, "Part", "");
                        Parts.Rows.Add(dr);
                    }

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMManager API Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
		/// Create a new row
        /// Calls [usp_insert_OfferNew]
		/// </summary>
        public override int LyticaApiLog(int BOMManagerID, int? Client)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "LyticaApiLog";
            try
            {
                proc = "LyticaApiLog";
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@bommanagerno", SqlDbType.NVarChar).Value = BOMManagerID;
                cmd.Parameters.Add("@updatedby", SqlDbType.Int).Value = Client;
      
               cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();

                int val = 0;
                while (reader.Read())
                {
                    
                    val = GetReaderValue_Int32(reader, "lyCounter", 0);
                 
                }
               
                return val;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Source
        /// Calls [usp_GetLyticaAPIData]
        /// </summary>
        public override List<LyticaAPI> GetLyticaAPIData(int BOMManagerID, int? CustomerReqID, string Parts, out DataTable dtPart, int curPage, int Rpp )
        {

            SqlConnection cn = null;
            SqlCommand cmd = null;
            DataTable dt = new DataTable();
            dt.Clear();
            dt.Columns.Add("Part");
          
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_GetLyticaAPIData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 600;
                cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                cmd.Parameters.Add("@CustomerRequirementID", SqlDbType.Int).Value = CustomerReqID;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = Parts;
                //cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = StartDate;
                //cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = EndDate;
                cmd.Parameters.Add("@curPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = Rpp;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<LyticaAPI> lst = new List<LyticaAPI>();
                while (reader.Read())
                {
                    LyticaAPI obj = new LyticaAPI();
                    obj.LyticaAPIId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
                    obj.Commodity = GetReaderValue_String(reader, "Commodity", "");
                    obj.OriginalPartSearched = GetReaderValue_String(reader, "OriginalPartSearched", "");
                    obj.Manufacturer = GetReaderValue_String(reader, "Manufacturer", "");
                    obj.AveragePrice = GetReaderValue_NullableDouble(reader, "AveragePrice", 0);
                    obj.TargetPrice = GetReaderValue_NullableDouble(reader, "TargetPrice", 0);
                    obj.MarketLeading = GetReaderValue_NullableDouble(reader, "MarketLeading", 0);
                    obj.LifeCycle = GetReaderValue_String(reader, "LifeCycle", "");
                    obj.lifeCycleStatus = GetReaderValue_String(reader, "lifeCycleStatus", "");
                    obj.OverAllRisk = GetReaderValue_String(reader, "OverAllRisk", "");
                    obj.PartBreadth = GetReaderValue_String(reader, "PartBreadth", "");
                    obj.ManufacturerBreadth = GetReaderValue_String(reader, "ManufacturerBreadth", "");
                    obj.DueDiligence = GetReaderValue_String(reader, "DueDiligence", "");
                    obj.PartConcentration = GetReaderValue_String(reader, "PartConcentration", "");
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.TotalCount = GetReaderValue_Int32(reader, "TotalRecords", 0);
                    //obj.lyCounter = GetReaderValue_Int32(reader, "lyCounter", 0);
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    DataRow dr = dt.NewRow();
                    dr["Part"] = reader[0].ToString();
                    dt.Rows.Add(dr);
                }
               
                dtPart = dt;
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Lytica API Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Source
        /// Calls [usp_GetLyticaAPIAlternateData]
        /// </summary>
        public override DataSet GetLyticaAPIAlternateData(string Parts, int curPage, int Rpp)
        {

            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_GetLyticaAPIAlternateData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                //cmd.Parameters.Add("@BOMManagerID", SqlDbType.Int).Value = BOMManagerID;
                //cmd.Parameters.Add("@CustomerRequirementID", SqlDbType.Int).Value = CustomerReqID;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = Parts;
                //cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = StartDate;
                //cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = EndDate;
                cmd.Parameters.Add("@curPage", SqlDbType.Int).Value = curPage;
                cmd.Parameters.Add("@Rpp", SqlDbType.Int).Value = Rpp;
                cn.Open();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                DataSet ds = new DataSet();
                da.Fill(ds);

                return ds;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Lytica API Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //Epo
        /// <summary>
        /// Source Epo
        /// Calls [usp_ipobom_source_Epo]
        /// </summary>
        public override List<OfferDetails> SourceEpo(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, System.Boolean? IsPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_ipobom_source_Epo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                
                
                
                //cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = IsPOHub;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.EpoId = GetReaderValue_Int32(reader, "EpoId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.EpoStatusNo = GetReaderValue_NullableInt32(reader, "EpoStatusNo", null);
                    obj.EpoStatusChangeDate = GetReaderValue_NullableDateTime(reader, "EpoStatusChangeDate", null);
                    obj.EpoStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "EpoStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.EpoStatusChangeEmployeeName = GetReaderValue_String(reader, "EpoStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");

                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierEpo = GetReaderValue_String(reader, "SupplierEpo", "");
                    obj.Description = GetReaderValue_String(reader, "Description", "");
                    obj.OfferAddFlag = GetReaderValue_Boolean(reader, "OfferAddFlag", false);
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Epo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //ReverseLogistics
        /// <summary>
        /// Source ReverseLogistics
        /// Calls [usp_ipobom_source_ReverseLogistics]
        /// </summary>
        public override List<OfferDetails> SourceReverseLogistics(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, System.Boolean? IsPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_ipobom_source_ReverseLogistic", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                
                
                
                //cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = IsPOHub;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.ReverseLogisticId = GetReaderValue_Int32(reader, "ReverseLogisticId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ReverseLogisticStatusNo = GetReaderValue_NullableInt32(reader, "ReverseLogisticStatusNo", null);
                    obj.ReverseLogisticStatusChangeDate = GetReaderValue_NullableDateTime(reader, "ReverseLogisticStatusChangeDate", null);
                    obj.ReverseLogisticStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "ReverseLogisticStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ReverseLogisticStatusChangeEmployeeName = GetReaderValue_String(reader, "ReverseLogisticStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");

                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierReverseLogistic = GetReaderValue_String(reader, "SupplierReverseLogistic", "");
                    obj.Description = GetReaderValue_String(reader, "Description", "");
                    obj.OfferAddFlag = GetReaderValue_Boolean(reader, "OfferAddFlag", false);

                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ReverseLogistic", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        //ReverseLogistics
        /// <summary>
        /// Source ReverseLogistics
        /// Calls [usp_source_GetReverseLogisticBulkEditHistory]
        /// </summary>
        public override List<OfferDetails> SourceReverseLogisticBulkEditHistory(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, System.Boolean? IsPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_source_GetReverseLogisticBulkEditHistory", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;



                //cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = IsPOHub;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.ReverseLogisticId = GetReaderValue_Int32(reader, "ReverseLogisticId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ReverseLogisticStatusNo = GetReaderValue_NullableInt32(reader, "ReverseLogisticStatusNo", null);
                    obj.ReverseLogisticStatusChangeDate = GetReaderValue_NullableDateTime(reader, "ReverseLogisticStatusChangeDate", null);
                    obj.ReverseLogisticStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "ReverseLogisticStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ReverseLogisticStatusChangeEmployeeName = GetReaderValue_String(reader, "ReverseLogisticStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");

                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierReverseLogistic = GetReaderValue_String(reader, "SupplierReverseLogistic", "");
                    obj.Description = GetReaderValue_String(reader, "Description", ""); 
                    obj.BulkEditBy = GetReaderValue_String(reader, "updatedbyname", "");
                    obj.BulkEditDate = GetReaderValue_NullableDateTime(reader, "bulkeditdate", null);
                    obj.BulkEditAction = GetReaderValue_String(reader, "ActionName", "");
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ReverseLogistic", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        //EPO

        //Ipo
        /// <summary>
        /// Source Ipo
        /// Calls [usp_Select_SourcingResult_Epo]
        /// </summary>
        public override List<OfferDetails> SourceIpo(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, System.Boolean? IsPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_Select_SourcingResult_Epo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 80;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = IsPOHub;
                
                
                
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNo", 0);
                    obj.SourcingTable = GetReaderValue_String(reader, "SourcingTable", "");
                    obj.SourcingTableItemNo = GetReaderValue_NullableInt32(reader, "SourcingTableItemNo", null);
                    obj.TypeName = GetReaderValue_String(reader, "TypeName", "");
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.EPOCurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_Int32(reader, "Salesman", 0);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.SupplierPrice = GetReaderValue_Double(reader, "SupplierPrice", 0);
                    obj.POHubCompanyNo = GetReaderValue_NullableInt32(reader, "POHubCompanyNo", null);
                    obj.POHubSupplierName = GetReaderValue_String(reader, "POHubSupplierName", "");
                    obj.POHubReleaseBy = GetReaderValue_NullableInt32(reader, "POHubReleaseBy", 0);
                    obj.ClientCompanyNo = GetReaderValue_NullableInt32(reader, "ClientCompanyNo", null);
                    obj.ClientSupplierName = GetReaderValue_String(reader, "ClientSupplierName", "");
                    obj.ClientCurrencyNo = GetReaderValue_Int32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.ConvertedSourcingPrice = GetReaderValue_Double(reader, "ConvertedSourcingPrice", 0);
                    obj.MslSpqFactorySealed = GetReaderValue_String(reader, "MslSpqFactorySealed", "");
                    obj.EstimatedShippingCost = GetReaderValue_NullableDouble(reader, "EstimatedShippingCost", 0);

                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);

                    obj.ActualPrice = GetReaderValue_NullableDouble(reader, "SupplierPrice", 0);
                    obj.SupplierPercentage = GetReaderValue_NullableDouble(reader, "UPLiftPrice", 0);

                    obj.SupplierManufacturerName = GetReaderValue_String(reader, "SupplierManufacturerName", "");
                    obj.SupplierDateCode = GetReaderValue_String(reader, "SupplierDateCode", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "SupplierPackageType", "");
                    obj.SupplierProductType = GetReaderValue_String(reader, "SupplierProductType", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierNotes = GetReaderValue_String(reader, "SupplierNotes", "");
                    obj.SourcingRelease = GetReaderValue_NullableBoolean(reader, "SourcingReleased", false);
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.RegionName = GetReaderValue_String(reader, "RegionName", "");
                    obj.IsClosed = GetReaderValue_Boolean(reader, "Closed", false);
                    obj.IsSoCreated = GetReaderValue_Boolean(reader, "IsSoCreated", false);
                    // obj.IsApplyPOBankFee = GetReaderValue_Boolean(reader, "IsApplyPOBankFee", false);
                    // obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.SourceRef = GetReaderValue_String(reader, "SourceRef", "");

                    obj.OriginalPrice = GetReaderValue_NullableDouble(reader, "BuyPrice", 0);
                    obj.ActualCurrencyNo = GetReaderValue_Int32(reader, "ActualCurrencyNo", 0);
                    obj.ActualCurrencyCode = GetReaderValue_String(reader, "BuyCurrencyCode", "");
                    // obj.SourcingReleasedCount = GetReaderValue_Int32(reader, "SourcingReleasedCount", 0);
                    obj.MSLLevelNo = GetReaderValue_Int32(reader, "MSLLevelNo", 0);
                    obj.MSLLevelText = GetReaderValue_String(reader, "MSLLevelText", "");

                    //[001] start
                    obj.SupplierWarranty = GetReaderValue_Int32(reader, "SupplierWarranty", 0);
                    //[001] end
                    //[003] start
                    obj.IsTestingRecommended = GetReaderValue_Boolean(reader, "IsTestingRecommended", false);
                    //[003] end
                    obj.IsImageAvailable = GetReaderValue_NullableBoolean(reader, "IsImageAvailable", false);
                    obj.PriorityNo = GetReaderValue_Int32(reader, "PriorityNo", 0);
                    obj.IHSCountryOfOriginNo = GetReaderValue_Int32(reader, "IHSCountryOfOriginNo", 0);
                    obj.IHSCountryOfOriginName = GetReaderValue_String(reader, "IHSCountryOfOriginName", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.CountryOfOriginName = GetReaderValue_String(reader, "CountryOfOriginName", "");
                    obj.ReReleased = GetReaderValue_Int32(reader, "ReReleased", 0);
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Epo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //IPO



        /// <summary>
        /// Source 
        /// Calls [usp_source_Offer_PQ_Trusted]
        /// </summary>
        public override List<OfferDetails> SourceOfferAll(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_source_Offer_PQ_Trusted", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.OfferId = GetReaderValue_Int32(reader, "SourcingResultId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Source 
        /// Calls [[usp_ipobom_source_Offer]]
        /// 
        /// </summary>
        public override List<OfferDetails> IPOBOMSource(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal,System.Boolean? isPOHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                if (isPOHub.Value)
                    cmd = new SqlCommand("usp_IPOBOM_Source_OfferPH", cn);
                else
                    cmd = new SqlCommand("usp_ipobom_source_Offer", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                //[002] Start
                //cmd.CommandTimeout = 30;
                cmd.CommandTimeout = 120; 
                //[002] End
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = isPOHub;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");

                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.IsSourcingHub=  GetReaderValue_Boolean(reader, "ishub", false);
                    obj.SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", "");
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
		
	
		
        /// <summary>
        /// Update Offer
		/// Calls [usp_update_Offer]
        /// </summary>
        public override bool Update(System.Int32? offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
            string proc = "usp_update_Offer";
			try {
                proc = isPoHub == true ? "usp_update_OfferPH" : proc;
				cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@OfferId", SqlDbType.Int).Value = offerId;
				cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
				cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
				cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
				cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
				cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
				cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
				cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
				cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
				cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
				cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
				cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
				cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
				cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                ////cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                ////cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                ////cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;

                ////cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                ////cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = SPQ;
                ////cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                ////cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                ////cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;

				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to update Offer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


        /// <summary>
        /// Update Offer
        /// Calls [usp_update_AltPart]
        /// </summary>
        public override bool UpdateAltPart(System.Int32? AlternativePartId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_update_AltPart";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@AlternativePartId", SqlDbType.Int).Value = AlternativePartId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;


                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update Epo Sourcing
        /// Calls [usp_update_Epo]
        /// </summary>
        public override bool UpdateEpo(System.Int32? EpoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? EpoStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_update_Epo";
            try
            {
                proc = isPoHub == true ? "usp_update_Epo" : proc;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@EpoId", SqlDbType.Int).Value = EpoId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@EpoStatusNo", SqlDbType.Int).Value = EpoStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                ////cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                ////cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                ////cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;

                ////cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                ////cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = SPQ;
                ////cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                ////cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                ////cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Epo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update Offer
        /// Calls [usp_ipobom_update_Offer]
        /// </summary>
        public override bool IPOBOMUpdate(System.Int32? offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_ipobom_update_Offer";
            try
            {
                //proc = isPoHub == true ? "usp_ipobom_update_OfferPH" : proc;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OfferId", SqlDbType.Int).Value = offerId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;

                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = SPQ;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Update Offer
        /// Calls [usp_update_AltPart]
        /// </summary>
        public override bool UpdateAltPartInfo(System.Int32? AltPartId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_update_HUBRFQAltPart";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@AlternativePartId", SqlDbType.Int).Value = AltPartId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;


                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Epo
        /// Calls [usp_ipobom_update_Epo]
        /// </summary>
        public override bool IPOBOMUpdateEpo(System.Int32? EpoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? EpoStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo, System.String Description, System.Double? VirtualCostPrice)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_ipobom_update_Epo";
            try
            {
                //proc = isPoHub == true ? "usp_ipobom_update_OfferPH" : proc;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@EpoId", SqlDbType.Int).Value = EpoId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@EpoStatusNo", SqlDbType.Int).Value = EpoStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;

                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = SPQ;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = Description;
                cmd.Parameters.Add("@VirtualCostPrice", SqlDbType.NVarChar).Value = VirtualCostPrice;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update ReverseLogistic //[004]
        /// Calls [usp_ipobom_update_ReverseLogistic]
        /// </summary>
        public override bool IPOBOMUpdateReverseLogistic(System.Int32? ReverseLogisticId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? ReverseLogisticStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo, System.String Description)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_ipobom_update_ReverseLogistic";
            try
            {
                //proc = isPoHub == true ? "usp_ipobom_update_OfferPH" : proc;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ReverseLogisticId", SqlDbType.Int).Value = ReverseLogisticId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.Int).Value = salesman;
                cmd.Parameters.Add("@ReverseLogisticStatusNo", SqlDbType.Int).Value = ReverseLogisticStatusNo;
                cmd.Parameters.Add("@SupplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SupplierTotalQSA", SqlDbType.NVarChar).Value = supplierTotalQSA;
                cmd.Parameters.Add("@SupplierLTB", SqlDbType.NVarChar).Value = supplierLTB;
                cmd.Parameters.Add("@SupplierMOQ", SqlDbType.NVarChar).Value = supplierMOQ;

                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                cmd.Parameters.Add("@SPQ", SqlDbType.NVarChar).Value = SPQ;
                cmd.Parameters.Add("@LeadTime", SqlDbType.NVarChar).Value = leadTime;
                cmd.Parameters.Add("@FactorySealed", SqlDbType.NVarChar).Value = factorySealed;
                cmd.Parameters.Add("@ROHSStatus", SqlDbType.NVarChar).Value = rohsStatus;
                cmd.Parameters.Add("@MSLLevelNo", SqlDbType.Int).Value = mslLevelNo;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = Description;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update Offer
        /// Calls [usp_update_Offer_for_sourcing]
        /// </summary>
        public override bool UpdateForSourcing(System.Int32? offerId, System.Int32? quantity, System.Double? price, System.String notes, System.Int32? updatedBy)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_Offer_for_sourcing", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@OfferId", SqlDbType.Int).Value = offerId;
				cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
				cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
				cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to update Offer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// Update Offer
		/// Calls [usp_update_Offer_OfferStatus]
        /// </summary>
		public override bool UpdateOfferStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_Offer_OfferStatus", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerNo;
				cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to update Offer", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

        /// <summary>
        /// Update Offer
        /// Calls [usp_update_AltPartStatus]
        /// </summary>
        public override bool UpdateAltPartsStatus(System.Int32? AlternativePartNo, System.Int32? offerStatusNo, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_AltPartStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@AlternativePartNo", SqlDbType.Int).Value = AlternativePartNo;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //EPO
        /// <summary>
        /// Update Strategic Offer
        /// Calls [usp_update_Strategic_StrategicStatus]
        /// </summary>
        public override bool UpdateEPOStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Strategic_StrategicStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerNo;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Strategic Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end
        //Reverse Logistics
        /// <summary>
        /// Update Reverse Logistics
        /// Calls [usp_update_ReverseLT_ReverseLTStatus]
        /// </summary>
        public override bool UpdateReverseLogisticsStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ReverseLT_ReverseLTStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@OfferNo", SqlDbType.Int).Value = offerNo;
                cmd.Parameters.Add("@OfferStatusNo", SqlDbType.Int).Value = offerStatusNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Reverse Logistics", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //end

        /// <summary>
        /// Source 
        /// Calls [[usp_CrossMatch_Offer]]
        /// 
        /// </summary>
        public override List<OfferDetails> CrossMatch(System.Int32? clientId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.String sortDir, System.String partSearch, System.String PartMatch, System.String months, System.Int32? monthTime, System.Int32? vendorNo, System.Int32? currencyNo, System.Boolean? isManufaurer, System.Int32? NoOfTopRecord, bool IsServerLocal, System.Boolean? isPohub, System.Int32? BomID, System.Boolean? IncludeAltPart, System.Int32? ReqId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                    cn = new SqlConnection(this.ConnectionString);
                    cmd = new SqlCommand("usp_CrossMatch_Offer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.NVarChar).Value = sortDir;
                cmd.Parameters.Add("@Parts", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@PartMatch", SqlDbType.NVarChar).Value = PartMatch;
                cmd.Parameters.Add("@Months", SqlDbType.NVarChar).Value = months;
                cmd.Parameters.Add("@MonthTime", SqlDbType.Int).Value = monthTime;
                cmd.Parameters.Add("@VenderTYpe", SqlDbType.Int).Value = vendorNo;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Manufacturer", SqlDbType.Bit).Value = isManufaurer;
                cmd.Parameters.Add("@NoOfTopRecord", SqlDbType.Bit).Value = NoOfTopRecord;
                cmd.Parameters.Add("@BomID", SqlDbType.Int).Value = BomID;
                cmd.Parameters.Add("@IncludeAltPart", SqlDbType.Bit).Value = IncludeAltPart;
                cmd.Parameters.Add("@CustomerReqID", SqlDbType.Int).Value = ReqId;
                
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();
                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");

                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "SupplierMOQ", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.isIncludeAltPart = GetReaderValue_Boolean(reader, "isIncludeAltPart", false);
                    obj.RowNum = GetReaderValue_Int32(reader, "RowNum", 0);
                    obj.TotalCount = GetReaderValue_Int32(reader, "TotalCount", 0);
                    lst.Add(obj);
                    obj = null;
                }
                //reader.NextResult();
                //while (reader.Read())
                //{
                //    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                //}

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable GetPartMatchRequirementDetails(System.Int32? clientId, System.Int32? OfferId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_partwatch_matching_Requirement", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OfferId", SqlDbType.Int).Value = OfferId;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update Stock info list
        /// Calls [usp_HUBRFQ_update_Stock]
        /// </summary>
        public override bool UpdateStockHUBRFQ(System.Int32? StockId, System.Double? price, System.Double? ClientUpliftPrice, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.Int32? supplierNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_HUBRFQ_update_Stock";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = StockId;
                //cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                //cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                //cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                //cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                //cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                //cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@ClientUpliftPrice", SqlDbType.Float).Value = ClientUpliftPrice;
                //cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@supplierNo", SqlDbType.Int).Value = supplierNo;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Stock in HubRFQ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [usp_select_StockHUBRFQ]
        /// </summary>
        public override OfferDetails GetStockHUBRFQ(System.Int32? StockId, bool? isPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_select_StockHUBRFQ";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Value = StockId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.EpoId = GetReaderValue_Int32(reader, "StockId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "QuantityInStock", 0);
                    obj.Price = GetReaderValue_Double(reader, "ResalePrice", 0);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "ISupplierNo", 0);
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.SupplierName = GetReaderValue_String(reader, "ISupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "ROHSStatus", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    //obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "ClientUPLiftPrice", 0);
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.SupplierValidateMessage = GetReaderValue_String(reader, "SupplierValidateMessage", "");
                    obj.SupplierCountValid = GetReaderValue_Int32(reader, "SupplierCountValid", 0) > 0 ? true : false;
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Stock HUBRFQ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update ReverseLogistic //[004]
        /// Calls [usp_ipobom_update_ReverseLogisticBulk]
        /// </summary>
        public override bool IPOBOMUpdateReverseLogisticBulk(System.String ReverseLogisticIds, bool? isBulk, System.Int32? updatedBy/*,bool? isPoHub*/)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            string proc = "usp_ipobom_update_ReverseLogisticBulk";
            try
            {
               
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand(proc, cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ReverseLogisticId", SqlDbType.NVarChar).Value = ReverseLogisticIds;
                cmd.Parameters.Add("@isBulk", SqlDbType.Bit).Value = isBulk;
                cmd.Parameters.Add("@updatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Offer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //public override void AddSourcingResultsforMatchedRequirements(System.Int32? OfferId, System.Int32? ClientId, DataTable dtRequirement)
        //{
        //    SqlConnection cn = null;
        //    SqlCommand cmd = null;
        //    try
        //    {
        //        cn = new SqlConnection(this.ConnectionString);
        //        cmd = new SqlCommand("usp_partwatch_match_Requirement_Add_SourcingResult", cn);
        //        cmd.CommandType = CommandType.StoredProcedure;
        //        cmd.CommandTimeout = 120;
        //        cmd.Parameters.Add("@OfferId", SqlDbType.Int).Value = OfferId;
        //        cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
        //        cmd.Parameters.Add("@MatchedRequirement", SqlDbType.Structured).Value = dtRequirement;
        //        cn.Open();
        //        ExecuteNonQuery(cmd);
        //    }
        //    catch (SqlException sqlex)
        //    {
        //        //LogException(sqlex);
        //        throw new Exception("Failed to update data", sqlex);
        //    }
        //    finally
        //    {
        //        cmd.Dispose();
        //        cn.Close();
        //        cn.Dispose();
        //    }
        //}

        /// <summary>
        /// Source 
        /// Calls [usp_source_AltParts]
        /// </summary>
        public override List<OfferDetails> SourceAltParts(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_source_AltParts", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = sortIndex;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDirection;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();


                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", "");
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Source 
        /// Calls [usp_source_AltPartsPH]
        /// </summary>
        public override List<OfferDetails> SourceAltPartsArchive(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool hasServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            SqlDataReader reader = null;
            outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_source_AltPartsPH", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Index", SqlDbType.Int).Value = index;
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startDate;
                cmd.Parameters.Add("@FinishDate", SqlDbType.DateTime).Value = endDate;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = sortIndex;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDirection;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                reader = cmd.ExecuteReader();


                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails();
                    obj.OfferId = GetReaderValue_Int32(reader, "OfferId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null);
                    obj.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null);
                    obj.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null);
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);
                    //[001] code start
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", "");
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    outDate = GetReaderValue_NullableDateTime(reader, "OutPutDate", null);
                }

                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Offers", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<OfferDetails> POHubAutoSourcing(int customerRequirementId, int sortIndex, int sortDir, int tableLength, int clientNo, int loginNo, out string ihsResult, out string lyticaResult)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            ihsResult = string.Empty;
            lyticaResult = string.Empty;
            try
            {
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_get_POHub_AutoSource", cn)
                {
                    CommandType = CommandType.StoredProcedure,
                    CommandTimeout = 30
                };
                cmd.Parameters.Add("@CustomerRequirementId", SqlDbType.Int).Value = customerRequirementId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@TableLength", SqlDbType.Int).Value = tableLength;
                cmd.Parameters.Add("@SortIndex", SqlDbType.Int).Value = sortIndex;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@LoginID", SqlDbType.Int).Value = loginNo;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                List<OfferDetails> lst = new List<OfferDetails>();
                while (reader.Read())
                {
                    OfferDetails obj = new OfferDetails
                    {
                        OfferId = GetReaderValue_Int32(reader, "ID", 0),
                        ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0),
                        Part = GetReaderValue_String(reader, "PartNo", ""),
                        ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null),
                        ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", ""),
                        ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", ""),
                        ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null),
                        ProductName = GetReaderValue_String(reader, "ProductName", ""),
                        ProductDescription = GetReaderValue_String(reader, "ProductDescription", ""),
                        SupplierName = GetReaderValue_String(reader, "SupplierName", ""),
                        OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "DateOrdered", null),
                        Quantity = GetReaderValue_Int32(reader, "Quantity", 0),
                        Price = GetReaderValue_Double(reader, "BuyPrice", 0),
                        UpliftPrice = GetReaderValue_Double(reader, "SellPrice", 0),
                        CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", ""),
                        DivisionStatus = GetReaderValue_Int32(reader, "DivisionStatus", 0),
                        IsRestrictMfr = GetReaderValue_Boolean(reader, "IsRestrictMfr", false),
                        IsSourcingHub = GetReaderValue_Boolean(reader, "IsSourcingHub", false),
                        ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0),
                        SourcingType = GetReaderValue_String(reader, "SourcingType", ""),
                        SourcingTypeDescription = GetReaderValue_String(reader, "SourcingTypeDescription", ""),
                        SourcingResultId = GetReaderValue_Int32(reader, "SourcingResultId", 0),
                        AllowRemoveOffer = GetReaderValue_Boolean(reader, "AllowRemoveOffer", false)
                    };
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                while (reader.Read())
                {
                    ihsResult = GetReaderValue_String(reader, "IHSResult", "");
                }
                reader.NextResult();
                while (reader.Read())
                {
                    lyticaResult = GetReaderValue_String(reader, "LyticaResult", "");
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get POHubAutoSourcing", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool POHubBulkUpdateEpo(string epoIds, string action, int? loginNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_POHub_StrategicLogisticBulk", cn)
                {
                    CommandType = CommandType.StoredProcedure
                };
                cmd.Parameters.Add("@EpoIDs", SqlDbType.NVarChar).Value = epoIds;
                cmd.Parameters.Add("@Action", SqlDbType.VarChar).Value = action;
                cmd.Parameters.Add("@LoginID", SqlDbType.Int).Value = loginNo;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to bulk update Strategic Logistic", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
