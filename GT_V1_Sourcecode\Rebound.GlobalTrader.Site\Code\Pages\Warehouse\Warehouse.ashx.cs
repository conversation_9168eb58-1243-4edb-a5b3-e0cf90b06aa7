using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site.Controls.DropDowns.Data;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Pages.Data.Warehouse {
	/// <summary>
	/// Summary description for $codebehindclassname$
	/// </summary>
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Warehouse : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "CountReceivePOs": CountReceivePOs(); break;
					case "CountShipSOs": CountShipSOs(); break;
					case "CountReceiveCRMAs": CountReceiveCRMAs(); break;
					case "CountShipSRMAs": CountShipSRMAs(); break;
					case "CountStock": CountStock(); break;
					case "CountServices": CountServices(); break;
					case "CountLots": CountLots(); break;
					case "CountGIs": CountGIs(); break;
                   
				}
			}
		}

		private void CountReceivePOs() {
			OutputCount(PurchaseOrder.CountReceivingForClient(SessionManager.ClientID));
		}

		private void CountShipSOs() {
			OutputCount(SalesOrder.CountShippingForClient(SessionManager.ClientID));
		}

		private void CountReceiveCRMAs() {
			OutputCount(CustomerRma.CountReceivingForClient(SessionManager.ClientID));
		}

		private void CountShipSRMAs() {
			OutputCount(SupplierRma.CountShippingForClient(SessionManager.ClientID));
		}

		private void CountStock() {
			OutputCount(Stock.CountForClient(SessionManager.ClientID));
		}

		private void CountServices() {
			OutputCount(Service.CountForClient(SessionManager.ClientID));
		}

		private void CountLots() {
			OutputCount(BLL.Lot.CountForClient(SessionManager.ClientID));
		}

		private void CountGIs() {
			OutputCount(GoodsIn.CountForClient(SessionManager.ClientID));
		}

       
	}
}