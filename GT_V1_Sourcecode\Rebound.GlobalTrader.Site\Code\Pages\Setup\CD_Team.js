Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.CD_Team=function(n){Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.prototype={get_ctlTeam:function(){return this._ctlTeam},set_ctlTeam:function(n){this._ctlTeam!==n&&(this._ctlTeam=n)},get_ctlTeamMembers:function(){return this._ctlTeamMembers},set_ctlTeamMembers:function(n){this._ctlTeamMembers!==n&&(this._ctlTeamMembers=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.callBaseMethod(this,"initialize")},goInit:function(){this._ctlTeam.addSelectTeam(Function.createDelegate(this,this.ctlTeam_SelectTeam));Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlTeam&&this._ctlTeam.dispose(),this._ctlTeamMembers&&this._ctlTeamMembers.dispose(),this._ctlTeam=null,this._ctlTeamMembers=null,Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.callBaseMethod(this,"dispose"))},ctlTeam_SelectTeam:function(){this._ctlTeamMembers._intTeamID=this._ctlTeam._intTeamID;this._ctlTeamMembers.refresh();this._ctlTeam._tbl.resizeColumns();this._ctlTeamMembers.show(!0)},showNuggets:function(n){this._ctlSecurityUserGroups.show(n)}};Rebound.GlobalTrader.Site.Pages.Setup.CD_Team.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Team",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);