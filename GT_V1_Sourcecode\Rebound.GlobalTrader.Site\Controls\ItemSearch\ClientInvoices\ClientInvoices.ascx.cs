using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
    public partial class ClientInvoices : Base
    {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            SetItemSearchType("ClientInvoices");
            AddScriptReference("Controls.ItemSearch.ClientInvoices.ClientInvoices.js");
		}

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.ClientInvoices", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
            ctlDesignBase.MakeChildControls();
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("ClientInvoice", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Company", Unit.Empty, true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("GoodsIn", WidthManager.GetWidth(WidthManager.ColumnWidth.ContactName), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PurchaseOrderIPO", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), true));

            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DateInvoiced", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            
            //ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("GoodsIn", WidthManager.GetWidth(WidthManager.ColumnWidth.ExternalCompanyDocument), true));
            //	ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SalesOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            base.OnPreRender(e);
		}
	}
}