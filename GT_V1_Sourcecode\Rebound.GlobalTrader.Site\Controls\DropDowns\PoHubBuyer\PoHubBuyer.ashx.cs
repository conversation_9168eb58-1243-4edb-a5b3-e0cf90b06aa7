using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class PoHubBuyer : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {
		private string _pageName = string.Empty;
		public override void ProcessRequest(HttpContext context) {
			SetDropDownType("PoHubBuyer");
			_pageName = context.Request.UrlReferrer.AbsolutePath;
			base.ProcessRequest(context);
			
		}

		protected override void GetData() {
			int intTeamNo = 0;
			int intDivisionNo = 0;
			int intExcludeLoginNo = 0;
			//if (GetFormValue_Boolean("LimitToCurrentUsersTeam")) intTeamNo = (int)SessionManager.LoginTeamID;
			//if (GetFormValue_Boolean("LimitToCurrentUsersDivision")) intDivisionNo = (int)SessionManager.LoginDivisionID;
			//if (GetFormValue_Boolean("ExcludeCurrentUser")) intExcludeLoginNo = (int)SessionManager.LoginID;
            //string strOptions = CacheManager.SerializeOptions(new object[] { 114, "POHub" });
			//string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strOptions);
			//if (string.IsNullOrEmpty(strCachedData)) {
				JsonObject jsn = new JsonObject();
				JsonObject jsnList = new JsonObject(true);
				//List<Login> lst = Login.DropDownForPurchaseHub(SessionManager.ClientID, intTeamNo, intDivisionNo, intExcludeLoginNo);
                //Temporary set client id=114;
                List<Login> lst = Login.DropDownForPurchaseHub(114, intTeamNo, intDivisionNo, intExcludeLoginNo);
			if (_pageName == "/BOMSearchAssign.aspx")
			{
				Login unassingLogin = new Login() { LoginId = -1, EmployeeName = "UnAssigned" };
				lst.Insert(0, unassingLogin);
			}
			
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].LoginId);
					jsnItem.AddVariable("Name", lst[i].EmployeeName);
					jsnList.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				lst.Clear(); lst = null;
				jsn.AddVariable("Employees", jsnList);
				jsnList.Dispose(); jsnList = null;
				//CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			//} else {
			//	_context.Response.Write(strCachedData);
			//}
			//strCachedData = null;
		}
	}
}
