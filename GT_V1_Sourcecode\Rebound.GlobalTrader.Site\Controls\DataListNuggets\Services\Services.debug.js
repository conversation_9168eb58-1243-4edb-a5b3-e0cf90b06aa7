///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.prototype = {

	get_intLotID: function() { return this._intLotID; }, 	set_intLotID: function(value) { if (this._intLotID !== value)  this._intLotID = value; }, 

	initialize: function() {
		this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
		this._strPathToData = "controls/DataListNuggets/Services";
		this._strDataObject = "Services";
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.callBaseMethod(this, "initialize");
	},
	
	initAfterBaseIsReady: function() {
		if (this._intLotID > 0) this.getFilterField("ctlLot").show(false);
		this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intLotID = null;
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() {
		//if (this._intLotID > 0) this._objData.addParameter("LotNo", this._intLotID);
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				$RGT_nubButton_Service(row.ID, row.Name)
				, $R_FN.setCleanTextValue(row.Desc)
				, $RGT_nubButton_Lot(row.LotNo, row.Lot)
				, row.Cost
				, row.Price
			];
			this._table.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
	
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
