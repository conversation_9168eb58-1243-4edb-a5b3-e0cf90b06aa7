Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.initializeBase(this,[n]);this._intDebitID=0;this._intLineID=0;this._intSupplierRMALineID=null;this._intPurchaseOrderID=null;this._intPurchaseOrderLineID=null;this._intServiceID=null;this._strSupplierName="";this._intCurrencyNo=null;this._intGlobalClientNo=-1;this._blnGlobalUser=!1};Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.prototype={get_strSupplierName:function(){return this._strSupplierName},set_strSupplierName:function(n){this._strSupplierName!==n&&(this._strSupplierName=n)},get_intDebitID:function(){return this._intDebitID},set_intDebitID:function(n){this._intDebitID!==n&&(this._intDebitID=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},get_radSelectSource:function(){return this._radSelectSource},set_radSelectSource:function(n){this._radSelectSource!==n&&(this._radSelectSource=n)},get_trSelectPOLine:function(){return this._trSelectPOLine},set_trSelectPOLine:function(n){this._trSelectPOLine!==n&&(this._trSelectPOLine=n)},get_trSelectService:function(){return this._trSelectService},set_trSelectService:function(n){this._trSelectService!==n&&(this._trSelectService=n)},get_ctlSelectService:function(){return this._ctlSelectService},set_ctlSelectService:function(n){this._ctlSelectService!==n&&(this._ctlSelectService=n)},get_pnlLines:function(){return this._pnlLines},set_pnlLines:function(n){this._pnlLines!==n&&(this._pnlLines=n)},get_tblLines:function(){return this._tblLines},set_tblLines:function(n){this._tblLines!==n&&(this._tblLines=n)},get_pnlLinesError:function(){return this._pnlLinesError},set_pnlLinesError:function(n){this._pnlLinesError!==n&&(this._pnlLinesError=n)},get_lblLinesError:function(){return this._lblLinesError},set_lblLinesError:function(n){this._lblLinesError!==n&&(this._lblLinesError=n)},get_pnlLinesLoading:function(){return this._pnlLinesLoading},set_pnlLinesLoading:function(n){this._pnlLinesLoading!==n&&(this._pnlLinesLoading=n)},get_pnlLinesNoneFound:function(){return this._pnlLinesNoneFound},set_pnlLinesNoneFound:function(n){this._pnlLinesNoneFound!==n&&(this._pnlLinesNoneFound=n)},get_pnlLinesNotAvailable:function(){return this._pnlLinesNotAvailable},set_pnlLinesNotAvailable:function(n){this._pnlLinesNotAvailable!==n&&(this._pnlLinesNotAvailable=n)},get_lblCurrency_Price:function(){return this._lblCurrency_Price},set_lblCurrency_Price:function(n){this._lblCurrency_Price!==n&&(this._lblCurrency_Price=n)},get_lblExplain_POLine:function(){return this._lblExplain_POLine},set_lblExplain_POLine:function(n){this._lblExplain_POLine!==n&&(this._lblExplain_POLine=n)},get_lblExplain_Service:function(){return this._lblExplain_Service},set_lblExplain_Service:function(n){this._lblExplain_Service!==n&&(this._lblExplain_Service=n)},get_arySources:function(){return this._arySources},set_arySources:function(n){this._arySources!==n&&(this._arySources=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this._ctlSelectService&&this._ctlSelectService.dispose(),this._tblLines&&this._tblLines.dispose(),this._strSupplierName=null,this._intDebitID=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._radSelectSource=null,this._trSelectPOLine=null,this._trSelectService=null,this._ctlSelectService=null,this._pnlLines=null,this._tblLines=null,this._pnlLinesError=null,this._lblLinesError=null,this._pnlLinesLoading=null,this._pnlLinesNoneFound=null,this._pnlLinesNotAvailable=null,this._lblCurrency_Price=null,this._lblExplain_POLine=null,this._lblExplain_Service=null,this._arySources=null,this._intLineID=null,this._intSupplierRMALineID=null,this._intPurchaseOrderID=null,this._intPurchaseOrderLineID=null,this._intServiceID=null,this._strSupplierName=null,this._intCurrencyNo=null,this._intGlobalClientNo=null,this._blnGlobalUser=null,Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.callBaseMethod(this,"dispose"))},formShown:function(){if(this._blnFirstTimeShown){this.addSave(Function.createDelegate(this,this.saveClicked));var n=Function.createDelegate(this,this.continueClicked);$R_IBTN.addClick(this._ibtnContinue,n);$R_IBTN.addClick(this._ibtnContinue_Footer,n);this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged));this._tblLines.addSelectedIndexChanged(Function.createDelegate(this,this.selectPOLine));this._ctlSelectService.addItemSelected(Function.createDelegate(this,this.selectService));this._strPathToData="controls/Nuggets/DebitLines";this._strDataObject="DebitLines"}this.resetSteps();this.setFormFieldsToDefaults();$find(this.getField("ctlProduct").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.productChange));this.setFieldValue("ctlPrintHazWar",!1);this.enableFieldCheckBox("ctlPrintHazWar",!1)},setFieldsFromHeader:function(n,t,i,r,u,f){this.setFieldValue("ctlDebit",i);this.setFieldValue("ctlSupplier",r);this._intPurchaseOrderID=u;this._strSupplierName=r;this._intCurrencyNo=n;$R_FN.setInnerHTML(this._lblCurrency_Price,t);this._dtmDebitDate=f},continueClicked:function(){this.nextStep()},findWhichTypeSelected:function(){for(var t,n=0;n<this._arySources.length;n++)if(t=$get(String.format("{0}_{1}",this._radSelectSource.id,n)),t.checked)return this._arySources[n]},stepChanged:function(){this._strSourceSelected=this.findWhichTypeSelected();var n=this._ctlMultiStep._intCurrentStep,t=n==1||n==1&&this._strSourceSelected!="NEW";$R_IBTN.showButton(this._ibtnContinue,t);$R_IBTN.showButton(this._ibtnContinue_Footer,t);$R_IBTN.enableButton(this._ibtnSave,n==3);$R_IBTN.enableButton(this._ibtnSave_Footer,n==3);this._ctlMultiStep.showExplainLabel(n!=2);$R_FN.showElement(this._lblExplain_POLine,n==2&&this._strSourceSelected=="PO");$R_FN.showElement(this._lblExplain_Service,n==2&&this._strSourceSelected=="SERVICE");n==2&&(this._strSourceSelected=="PO"&&(this._tblLines.resizeColumns(),this.getPOLines()),this._strSourceSelected=="SERVICE"&&(this._ctlSelectService.resizeColumns(),this._ctlSelectService.setFieldValue("ctlName","%"),this._intGlobalClientNo=this._blnGlobalUser==!0?this._intGlobalClientNo:null,this._ctlSelectService._GlobalClientNo=this._intGlobalClientNo,this._ctlSelectService.getData()),$R_FN.showElement(this._trSelectPOLine,this._strSourceSelected=="PO"),$R_FN.showElement(this._trSelectService,this._strSourceSelected=="SERVICE"),this.showField("ctlService",this._strSourceSelected=="SERVICE"),this.showField("ctlServiceDescription",this._strSourceSelected=="SERVICE"),this.showField("ctlPartNo",this._strSourceSelected=="PO"),this.showField("ctlProduct",this._strSourceSelected=="PO"),this.showField("ctlPackage",this._strSourceSelected=="PO"),this.showField("ctlROHSStatus",this._strSourceSelected=="PO"),this.showField("ctlManufacturer",this._strSourceSelected=="PO"),this.showField("ctlSupplierPart",this._strSourceSelected=="PO"),this.showField("ctlDateCode",this._strSourceSelected=="PO"))},getPOLines:function(){this._tblLines.clearTable();$R_FN.showElement(this._pnlLines,!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetPOLines");n.addParameter("ID",this._intPurchaseOrderID);n.addDataOK(Function.createDelegate(this,this.getPOLinesOK));n.addError(Function.createDelegate(this,this.getPOLinesError));n.addTimeout(Function.createDelegate(this,this.getPOLinesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getPOLinesOK:function(n){var r=n._result,i,f,t,u;if(r.Lines.length==0)$R_FN.showElement(this._pnlLines,!1),$R_FN.showElement(this._pnlLinesNotAvailable,!0);else for(i=0,f=r.Lines.length;i<f;i++)t=r.Lines[i],u=[t.Part,t.Mfr,t.Product,t.QuantityOrdered,t.Price,t.ShipInCost],this._tblLines.addRow(u,t.ID,!1),u=null,t=null},getPOLinesError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},selectPOLine:function(){this._intPurchaseOrderLineID=this._tblLines._varSelectedValue;this.fillData_POLine();this.nextStep()},selectService:function(){this._intServiceID=this._ctlSelectService.getSelectedID();this.fillData_Service();this.nextStep()},clearNewItemValues:function(){this.setFormFieldsToDefaults()},resetNewItemValues:function(){this.setFormFieldsToDefaults()},fillData:function(){switch(this._strSourceSelected){case"PO":this.fillData_POLine();break;case"SERVICE":this.fillData_Service()}},fillDataError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},fillData_POLine:function(){this.resetNewItemValues();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetPOLineForNew");n.addParameter("id",this._intPurchaseOrderLineID);n.addParameter("DebitCurrencyNo",this._intCurrencyNo);n.addParameter("DebitDate",this._dtmDebitDate);n.addDataOK(Function.createDelegate(this,this.fillData_POLineOK));n.addError(Function.createDelegate(this,this.fillDataError));n.addTimeout(Function.createDelegate(this,this.fillDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},fillData_POLineOK:function(n){var t=n._result;this.setFieldValue("ctlPartNo",t.Part);this.setFieldValue("ctlDateCode",t.DateCode);this.setFieldValue("ctlSupplierPart",t.SupplierPart);this.setFieldValue("ctlPrice",t.Price);this.setFieldValue("ctlQuantity",t.Quantity);this.setFieldValue("ctlManufacturer",t.MfrNo,null,t.Mfr);this.setFieldValue("ctlProduct",t.ProductNo,null,t.ProductDescription);this.setFieldValue("ctlPackage",t.PackageNo,null,t.PackageDescription);this.setFieldValue("ctlROHSStatus",t.ROHS);this.getDropDownsData()},fillData_Service:function(){this.resetNewItemValues();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetServiceForNew");n.addParameter("id",this._intServiceID);n.addParameter("DebitCurrencyNo",this._intCurrencyNo);n.addParameter("DebitDate",this._dtmDebitDate);n.addDataOK(Function.createDelegate(this,this.fillData_ServiceOK));n.addError(Function.createDelegate(this,this.fillDataError));n.addTimeout(Function.createDelegate(this,this.fillDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},fillData_ServiceOK:function(n){var t=n._result;this.setFieldValue("ctlService",t.ServiceName);this.setFieldValue("ctlServiceDescription",t.ServiceDescription);this.setFieldValue("ctlPrice",t.Price);this.setFieldValue("ctlQuantity",1);this.getDropDownsData()},getDropDownsData:function(){this.getFieldDropDownData("ctlROHSStatus")},saveClicked:function(){this.resetFormFields();this.validateForm()&&this.saveEdit()},validateForm:function(){var n=!0;return this._strSourceSelected=="SERVICE"?this.checkFieldEntered("ctlService")||(n=!1):this.checkFieldEntered("ctlPartNo")||(n=!1),this.checkFieldEntered("ctlQuantity")||(n=!1),this.checkFieldNumeric("ctlQuantity")||(n=!1),this.checkFieldEntered("ctlPrice")||(n=!1),this.checkFieldNumeric("ctlPrice")||(n=!1),n||this.showError(!0),n},saveEdit:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("AddNew");n.addParameter("id",this._intDebitID);n.addParameter("LineIsService",this._strSourceSelected=="SERVICE");this._strSourceSelected=="SERVICE"?(n.addParameter("ServiceNo",this._intServiceID),n.addParameter("Service",this.getFieldValue("ctlService")),n.addParameter("ServiceDescription",this.getFieldValue("ctlServiceDescription"))):(n.addParameter("PurchaseOrderLineNo",this._intPurchaseOrderLineID),n.addParameter("Part",this.getFieldValue("ctlPartNo")),n.addParameter("MfrNo",this.getFieldValue("ctlManufacturer")),n.addParameter("DateCd",this.getFieldValue("ctlDateCode")),n.addParameter("PackageNo",this.getFieldValue("ctlPackage")),n.addParameter("ProductNo",this.getFieldValue("ctlProduct")),n.addParameter("SupplierPart",this.getFieldValue("ctlSupplierPart")),n.addParameter("ROHS",this.getFieldValue("ctlROHSStatus")));n.addParameter("SupplierRMALineNo",this._intSupplierRMALineID);n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("Price",this.getFieldValue("ctlPrice"));n.addParameter("Taxable",this.getFieldValue("ctlTaxable"));n.addParameter("LineNotes",this.getFieldValue("ctlLineNotes"));n.addParameter("PrintHazWar",this.getFieldValue("ctlPrintHazWar"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditOK:function(n){n._result.Result==!0?(this._intLineID=n._result.NewID,this.onSaveComplete()):this.saveEditError(n)},productChange:function(){var n="",t,i;this.setFieldValue("ctlPrintHazWar",!1);n=$find(this.getField("ctlProduct").ControlID)._aut._varSelectedExtraData;typeof n=="undefined"||(t=n.split(":"),i=t[0],this.enableFieldCheckBox("ctlPrintHazWar",Boolean.parse(i)))}};Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);