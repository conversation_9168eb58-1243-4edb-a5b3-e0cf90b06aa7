///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_UnRelease = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_UnRelease.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intBOMID = -1
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_UnRelease.prototype = {

//get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(value) { if (this._intBOMID !== value) this._intBOMID = value; },

//get_intRequirementLineID: function() { return this._intRequirementLineID; }, set_intRequirementLineID: function(value) { if (this._intRequirementLineID !== value) this._intRequirementLineID = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_UnRelease.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlUnRelease) this._ctlUnRelease.dispose();
        this._ctlUnRelease = null;
        this._intRequirementLineID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_UnRelease.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        //$("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMerginesourcing").hide();
        if (this._blnFirstTimeShown) {
            this._ctlUnRelease = this.getFieldComponent("ctlUnRelease");
            this._ctlUnRelease.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlUnRelease.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function() {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("UnReleaseBomItem");
        obj.addParameter("RequirementID", this._intCustomerRequirementID);
        obj.addParameter("BomID", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },


    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }



};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_UnRelease.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_UnRelease", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
