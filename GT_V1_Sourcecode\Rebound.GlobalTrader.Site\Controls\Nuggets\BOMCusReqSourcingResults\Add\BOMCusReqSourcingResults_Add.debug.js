///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     changed by      date         Remarks
//[001]      Aashu          06/06/2018     Added supplier warranty field
//[002]      Aashu <PERSON>    16-Aug-2018    REB-12322 : A tick box to recomond test the parts from HUB side.
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intSupplierId = -1;
    this._upliftPer = 0;
    this._ClientNo = -1;
    this._intESTShippingCost = 0;
    this._Quantity = 0;
    this._NonPreferredCompany = true;
    this._intGlobalClientNo = -1;
    this._blnISAS6081Required = false;
    this._CountryNo = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add.prototype = {

    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (value) { if (this._intCustomerRequirementID !== value) this._intCustomerRequirementID = value; },
    get_lblUpliftPrice: function () { return this._lblUpliftPrice; }, set_lblUpliftPrice: function (value) { if (this._lblUpliftPrice !== value) this._lblUpliftPrice = value; },
    get_lblQty: function () { return this._lblQty; }, set_lblQty: function (value) { if (this._lblQty !== value) this._lblQty = value; },
    get_lblSupplierPrice: function () { return this._lblSupplierPrice; }, set_lblSupplierPrice: function (value) { if (this._lblSupplierPrice !== value) this._lblSupplierPrice = value; },

    //[001] start
    get_NonPreferredCompany: function () { return this._NonPreferredCompany; }, set_NonPreferredCompany: function (value) { if (this._NonPreferredCompany !== value) this._NonPreferredCompany = value; },
    //[001] end
    //ihs code
    get_btn1: function () { return this._btn1; }, set_btn1: function (v) { if (this._btn1 !== v) this._btn1 = v; },
    get_btn2: function () { return this._btn2; }, set_btn2: function (v) { if (this._btn2 !== v) this._btn2 = v; },
    get_lblError: function () { return this._lblError; }, set_lblError: function (v) { if (this._lblError !== v) this._lblError = v; },
    get_pnlPartDetail: function () { return this._pnlPartDetail; }, set_pnlPartDetail: function (value) { if (this._pnlPartDetail !== value) this._pnlPartDetail = value; },
    get_ctltblPartdetails: function () { return this._ctltblPartdetails; }, set_ctltblPartdetails: function (v) { if (this._ctltblPartdetails !== v) this._ctltblPartdetails = v; },

    //ihs code end

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this._ctltblPartdetails.addItemSelected(Function.createDelegate(this, this.getIHSDataSelected));
        // alert($find(this.getField("ctlSupplierPrice")));
        // if($find(this.getField("ctlSupplierPrice").ControlID)) $addHandler($find(this.getField("ctlSupplierPrice").ControlID), "onblur", Function.createDelegate(this, this.focusOnPrice));
    },
    getFormControlID: function (ParentId, controlID) {
        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    formShown: function () {

        this.showField("ctlCompanyNamelbl", false);
        this.showField("ctlCompanyNameddl", false);
        this.setFieldValue("ctlSupplierWarranty","0");
        window.scroll(0, 1);
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").hide();
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNamelbl_pnlFieldControls").append("<a id='linkReselect' class='quickSearchReselect' href='javascript:void(0);'>[ Reselect ]</a>");

            document.getElementById("linkReselect").addEventListener("click", Function.createDelegate(this, this.GetLocationDropDown));
        }
        this.getDropDownsData();
        //   alert('test'+this._Quantity);
        this.setFieldValue("ctlSupplier", "0", null, "");
        this.setFieldValue("ctlPartNo", this._strPartNo);
        this.setFieldValue("ctlROHS", this._ROHS);
        this.setFieldValue("ctlDateCode", this._DateCode);
        this.setFieldValue("ctlManufacturer", this._ManufacturerNo, null, $R_FN.setCleanTextValue(this._Manufacturer) + $R_FN.createAdvisoryNotesIcon(this._MfrAdvisoryNotes,'margin-left-10'));
        //  this.setFieldValue("ctlQuantity", this._Quantity);
        this.setFieldValue("ctlPackage", this._PackageID, null, $R_FN.setCleanTextValue(this._Package));
        this.setFieldValue("ctlProduct", this._ProductID, null, $R_FN.setCleanTextValue(this._Product));
        if (this._IHSCountryOfOriginName != null && this._IHSCountryOfOriginName != '')

            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryOfOrigin").text('(  ' + this._IHSCountryOfOriginName + ' )');
        else
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryOfOrigin").text("");

        //IHS Gride implement hear
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn7").addEventListener("click", Function.createDelegate(this, this.Toggle2));
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn3").addEventListener("click", Function.createDelegate(this, this.Toggle1));
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn5").addEventListener("click", Function.createDelegate(this, this.Canceltop));
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn6").addEventListener("click", Function.createDelegate(this, this.Cancelbottom));
        //document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn4").addEventListener("click", Function.createDelegate(this, this.getValuesByPartsOnClick));
        $addHandler(document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btnOK_hyp"), "click", Function.createDelegate(this, this.getValuesByPartsOnClick));
        if (this._btn1) $addHandler(this._btn1, "click", Function.createDelegate(this, this.SearchTypePopup));
        //if (this._btn2) $addHandler(this._btn2, "click", Function.createDelegate(this, this.Toggle2));
        $R_FN.showElement(this._lblError, false);
        $R_FN.showElement(this._btn2, false);
        $R_FN.showElement(this._pnlPartDetail, false);

        //code end


        //alert(this._Quantity);

        // document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlQuantity_ctl04_lblQty").innerText = "( " +  this._Quantity + ")";
        $R_FN.setInnerHTML(this._lblQty, '[ Qty Req: ' + this._Quantity + ' ]');
        if ($find(this.getField("ctlSupplier").ControlID)) $find(this.getField("ctlSupplier").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.supplierChangeEvent));
        if ($find(this.getField("ctlManufacturer").ControlID)) $find(this.getField("ctlManufacturer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getMfrNotes));
        $find(this.getField("ctlGlobalCurrency").ControlID).addChanged(Function.createDelegate(this, this.updateCurrency));
        $find(this.getField("ctlProduct").ControlID)._aut._intPOHubClientNo = this._ClientNo;
        //alert(document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSupplierPrice_ctl04_lblSupplierPrice"));

        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSupplierPrice_ctl04_lblSupplierPrice").setAttribute("onkeyup", String.format("$find(\"{0}\").focusOnPrice();", this._element.id));
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPrice_ctl04_txtPrice").setAttribute("onkeyup", String.format("$find(\"{0}\").focusOnSellPrice();", this._element.id));

        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPartNo_tdTitle").style.color = ''
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlMSL_tdTitle").style.color = ''
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlManufacturer_tdTitle").style.color = ''
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPackage_tdTitle").style.color = ''
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_tdTitle").style.color = ''
        document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlProduct_tdTitle").style.color = ''
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlMSL_ctl04_ddlMsl_ddl option:selected").text('');
        //document.getElementById("lblihsproduct").style.color = '#d3fFcC'
        //document.getElementById("lblihsHTSCode").style.color = '#d3fFcC'
        //document.getElementById("lblduty").style.color = '#d3fFcC'
        //document.getElementById("lblECCNCode").style.color = '#d3fFcC'
        $("#spnROHSName").hide();
        $("#spnManufacturer").hide();
        //$("#spnIHSProduct").hide();
        $("#spnPackaging").hide();
        //$("#spnHTSCode").hide();
        $("#spnIHSCountryOfOrigin").hide();
        //$("#spnHTSCode").hide();
        //$("#spnIHSDutyCode").hide();
        //$("#spnIHSECCNCode").hide();

        $find(this.getField("ctlCountryOfManufacture").ControlID).addChanged(Function.createDelegate(this, this.findCountryRiskData));

        //default: hide the reason why sell price < buy price
        document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessLabel').style.display = "none";
        this.showField("ctlSellPriceLessReason", false);
    },

    focusOnPrice: function () {
        var price = ((parseFloat(this.getFieldValue("ctlSupplierPrice")) * this._upliftPer) / 100) + parseFloat(this.getFieldValue("ctlSupplierPrice"));
        if (parseFloat(this.getFieldValue("ctlSupplierPrice")) > 0) {
            this.setFieldValue("ctlPrice", $R_FN.formatCurrency(price, "", 5));
            //hide reason
            document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessLabel').style.display = "none";
            $('span[id^="spnRequiredReason"]').remove();
            this.showField("ctlSellPriceLessReason", false);
        }
    },
    focusOnSellPrice: function () {
        var buyPrice = parseFloat(this.getFieldValue("ctlSupplierPrice"));
        var sellPrice = parseFloat(this.getFieldValue("ctlPrice"));
        if (sellPrice < buyPrice) {
            document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessLabel').style.display = "";
            this.showField("ctlSellPriceLessReason", true);
            if ($('#spnRequiredReason').length == 0) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessReason_tdTitle").append("<span id='spnRequiredReason' class='requiredField'>*</span>");
            }
        } else {
            document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessLabel').style.display = "none";
            $('span[id^="spnRequiredReason"]').remove();
            this.showField("ctlSellPriceLessReason", false);
        }
    },
    dispose: function () {
        if (this.isDisposed) return;
        this._intCustomerRequirementID = null;
        this._upliftPer = null;
        this._Quantity = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add.callBaseMethod(this, "dispose");
    },

    //ihs code start
    SearchTypePopup: function () {


        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctlSearchtxtPartNo_txt").hide();
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctlSearchtxtPartNo_ctl03").hide();
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctlSearchtxtPartNo").hide();
        $('input:radio[name="searchType"][value="contains"]').prop('checked', true);
        $R_FN.showElement(this._pnlPartDetail, false);
        this._ctltblPartdetails._tblResults.clearTable();
        $("#myModal").show();
        var strPart = this.getFieldValue("ctlPartNo");
        $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value = strPart;
        //this.loadIHSGrid();
        this.Toggle1();
        $R_FN.showElement(document.getElementById("okbtn"), false);
        $('.okbtn').hide();

        document.getElementsByClassName("dataFilter")[1].style.display = "none";
        if (document.getElementsByClassName("itemSearchGo")[1])
            document.getElementsByClassName("itemSearchGo")[1].style.display = "none";


    },
    loadIHSGrid: function () {

        $("#lblihserror").text("");
        this._ctltblPartdetails._tblResults.clearTable();
        this._ctltblPartdetails._tblResults.resizeColumns();
        this._ctltblPartdetails.showNoneFound(true);
        this._ctltblPartdetails.resetToFirstPage();


        this._ctltblPartdetails.setFieldValue("ctlSearchtxtPartNo", $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value));
        this._ctltblPartdetails._strpart = $R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
        this._ctltblPartdetails._searchType = $("input[name='searchType']:checked").val();
        this._ctltblPartdetails.getData();
    },
    Canceltop: function () {
        $("#myModal").hide();
        //this.Toggle3();
    },
    Cancelbottom: function () {
        $("#myModal").hide();
        // this.Toggle3();

    },
    Toggle1: function () {
        $("#lblIhsServiceMessage").text("");
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl08").hide();
        if ($("input[name='searchType']:checked").val() == "exact".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 2) {
                $("#lblihserror").text("");
                $('#divLoader').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror").text("The search term must contain at least 3 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }
        if ($("input[name='searchType']:checked").val() == "startswith".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 2) {
                $("#lblihserror").text("");
                $('#divLoader').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror").text("The search term must contain at least 3 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }
        if ($("input[name='searchType']:checked").val() == "contains".trim()) {
            var strexact = $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value;
            if (strexact.length > 3) {
                $("#lblihserror").text("");
                $('#divLoader').show();
                $R_FN.showElement(this._pnlPartDetail, true);
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/CusReqAdd");
                obj.set_DataObject("CusReqAdd");
                obj.set_DataAction("GetDataGrid");
                obj.addParameter("partsearch", $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value);
                obj.addParameter("searchType", $("input[name='searchType']:checked").val());
                obj.addDataOK(Function.createDelegate(this, this.getDataGrid));
                obj.addError(Function.createDelegate(this, this.getDataGridError));
                obj.addTimeout(Function.createDelegate(this, this.getDataGridError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;

            }
            else {
                $("#lblihserror").text("The search term must contain at least 4 alphanumeric characters. Examples are '2n222', 'F245', etc.");
                $R_FN.showElement(this._lblError, true);
                $R_FN.showElement(this._pnlPartDetail, false);
                this._ctltblPartdetails._tblResults.clearTable();
                this._ctltblPartdetails._tblResults.resizeColumns();
                this._ctltblPartdetails.showNoneFound(true);
                return;

            }
        }

    },
    getIHSValuesByParts: function (IHSPartsId, MSLName, MSLNo, Manufacturer, IHSProdDesc, Packaging, HTSCode, IHSDutyCode, CountryOfOrigin, CountryOfOriginNo, PackagingSize) {
        $('#divLoader').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqAdd");
        obj.set_DataObject("CusReqAdd");
        obj.set_DataAction("GetIHSPartDetails");
        obj.addParameter("IHSPartsId", IHSPartsId);
        obj.addParameter("MSLName", MSLName);
        obj.addParameter("MSLNo", MSLNo);
        obj.addParameter("Manufacturer", Manufacturer);
        obj.addParameter("IHSProdDesc", IHSProdDesc);
        obj.addParameter("Packaging", Packaging);
        obj.addParameter("HTSCode", HTSCode);
        obj.addParameter("IHSDutyCode", IHSDutyCode);
        obj.addParameter("CountryOfOrigin", CountryOfOrigin);
        obj.addParameter("CountryOfOriginNo", CountryOfOriginNo);
        obj.addParameter("PackagingSize", PackagingSize);
        obj.addDataOK(Function.createDelegate(this, this.getIHSDataGrid));
        obj.addError(Function.createDelegate(this, this.getIHSDataError));
        obj.addTimeout(Function.createDelegate(this, this.getIHSDataGridError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getIHSDataError: function (args) {
        $('#divLoader').hide();
    },
    getIHSDataGrid: function (args) {
        var obj = args._result.Result
        if (obj[0].IHSPartsId > 0) {

            var PartName = obj[0].ID;

            if (PartName != 'undefined ' && PartName.length > 0) {
                this.setFieldValue("ctlPartNo", $R_FN.setCleanTextValue(PartName));
                document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPartNo_tdTitle").style.color = 'yellow'
            }
            else {
                document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPartNo_tdTitle").style.color = ''
            }

            if (obj[0].ROHSName != 'undefined ' && obj[0].ROHSName != '' && obj[0].ROHSName.length > 0) {
                $("#spnROHSName").show();
                //$("#spnROHSName").text(" ( " + obj[0].ROHSNo + " ) ");
                this.setFieldValue("ctlMSL", obj[0].ROHSNo);
                document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlMSL_tdTitle").style.color = 'yellow'
                $("#spnROHSName").text(" ( " + $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlMSL_ctl04_ddlMsl_ddl option:selected").text() + " ) ");
                if (obj[0].ROHSName == 'N/A')
                    document.getElementById("spnROHSName").style.color = 'yellow';
                else
                    document.getElementById("spnROHSName").style.color = '';

            }
            else {
                $("#spnROHSName").show();
                $("#spnROHSName").text(" ( " + "N/A" + " ) ");
                document.getElementById("spnROHSName").style.color = 'yellow'
                this.setFieldValue("ctlMSL", 1);
                document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlMSL_tdTitle").style.color = 'yellow'

            }


            if (obj[0].Manufacturer != 'undefined ' && obj[0].Manufacturer != '' && obj[0].Manufacturer.length > 0) {
                if (obj[0].ManufacturerNo != 0) {
                    this.setFieldValue("ctlManufacturer", obj[0].ManufacturerNo, null, $R_FN.setCleanTextValue(obj[0].Manufacturer));
                    $("#spnManufacturer").hide();
                    document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlManufacturer_tdTitle").style.color = 'yellow'



                }
            }
            else
                if (this._ManufacturerNo > 0 && this._Manufacturer != '' && this._Manufacturer != 'undefined') {
                    this.setFieldValue("ctlManufacturer", this._ManufacturerNo, null, $R_FN.setCleanTextValue(this._Manufacturer));
                    //$("#spnManufacturer").show();
                    //$("#spnManufacturer").text(" (" + obj[0].ManufacturerFullName + " ) ");
                    document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlManufacturer_tdTitle").style.color = ''

                }
                else {
                    $("#spnManufacturer").show();
                    $("#spnManufacturer").text(" (" + obj[0].ManufacturerFullName + " ) ");
                    document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlManufacturer_tdTitle").style.color = ''
                }




            if (obj[0].ProdDesc != 'undefined ' && obj[0].IHSProdDesc.length > 0) {

                if (obj[0].ProdNo != 0) {
                    this.setFieldValue("ctlProduct", obj[0].ProdNo, null, $R_FN.setCleanTextValue(obj[0].ProdDesc));
                    $("#spnIHSProduct").show();
                    document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlProduct_tdTitle").style.color = 'yellow'
                    $("#spnIHSProduct").text(obj[0].IHSProdDesc);
                    document.getElementById("lblihsproduct").style.color = 'yellow'
                }
                else if (obj[0].IHSProdDesc != '' && obj[0].ProdDesc != 'undefined ') {
                    //$find(this.getFormControlID(this._element.id, 'cmbProducts')).setValue("", 0);
                    $("#spnIHSProduct").show();
                    $("#spnIHSProduct").text(obj[0].IHSProdDesc);
                    document.getElementById("lblihsproduct").style.color = 'yellow'
                    document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlProduct_tdTitle").style.color = ''
                }

            }
            else {


                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnIHSProduct").show();
                    $("#spnIHSProduct").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsproduct").style.color = 'yellow'
                    document.getElementById("spnIHSProduct").style.color = 'yellow'
                } else {
                    document.getElementById("lblihsproduct").style.color = ''
                    document.getElementById("spnIHSProduct").style.color = ''
                }
            }


            if (obj[0].IHSDutyCode != 'undefined ' && obj[0].IHSDutyCode != '' && obj[0].IHSDutyCode.length > 0) {
                $("#spnIHSDutyCode").show();
                $("#spnIHSDutyCode").text(" ( " + obj[0].IHSDutyCode + " ) ");
                document.getElementById("lblduty").style.color = 'yellow'
                document.getElementById("spnIHSDutyCode").style.color = ''

            }
            else {
                $("#spnIHSDutyCode").show();
                $("#spnIHSDutyCode").text("( N/A )");
                document.getElementById("lblduty").style.color = '#d3fFcC'
                document.getElementById("spnIHSDutyCode").style.color = 'yellow'
            }


            if (obj[0].PackageId > 0) {
               // this.setFieldValue("ctlPackage", obj[0].PackageId);
                $("#spnPackaging").show();
                //$("#spnPackaging").text(" ( " + $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPackage_ctl04_ddlPackage_ddl option:selected").text() + " ) ");
                this.setFieldValue("ctlPackage", obj[0].PackageId, null, $R_FN.setCleanTextValue(obj[0].PackageDescription));
                $("#spnPackaging").text(" ( " + obj[0].Packaging + " ) ");
                document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPackage_tdTitle").style.color = 'yellow'
                document.getElementById("spnPackaging").style.color = ""

            }
            else {
                $("#spnPackaging").show();
                $("#spnPackaging").text(" ( " + "N/A" + " ) ");
                document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPackage_tdTitle").style.color = ''
                document.getElementById("spnPackaging").style.color = 'yellow'
                //this.setFieldValue("ctlPackage", 0);
                this.setFieldValue("ctlPackage", 0, null, "");

            }

            if (obj[0].HTSCode != 'undefined ' && obj[0].HTSCode != '' && obj[0].HTSCode.length > 0) {
                $("#spnHTSCode").show();
                $("#spnHTSCode").text(obj[0].HTSCode);
                document.getElementById("spnHTSCode").style.color = ''
                document.getElementById("lblihsHTSCode").style.color = 'yellow'

            }
            else {
                if (obj[0].HTSCode != '' || obj[0].IHSProdDes != '') {
                    $("#spnHTSCode").show();
                    $("#spnHTSCode").text(" ( " + "N/A" + " ) ");
                    document.getElementById("lblihsHTSCode").style.color = '#d3fFcC'
                    document.getElementById("spnHTSCode").style.color = 'yellow'

                } else {
                    document.getElementById("lblihsHTSCode").style.color = ''
                    document.getElementById("spnHTSCode").style.color = ''
                }
            }



            if (obj[0].CountryOfOrigin != 'undefined ' && obj[0].CountryOfOrigin != '' && obj[0].CountryOfOrigin.length > 0) {
                $("#spnIHSCountryOfOrigin").hide();
                this.setFieldValue("ctlCountryOfManufacture", obj[0].CountryOfOriginNo);
                document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_tdTitle").style.color = 'yellow'
                document.getElementById("spnIHSCountryOfOrigin").style.color = ''
            }
            else {
                $("#spnIHSCountryOfOrigin").show();
                $("#spnIHSCountryOfOrigin").text(" ( " + "N/A" + " ) ");
                this.setFieldValue("ctlCountryOfManufacture", 0);
                document.getElementById("spnIHSCountryOfOrigin").style.color = 'yellow'
                document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_tdTitle").style.color = ''
            }
            //code for ECCN Code start
            if (obj[0].ECCNCode != 'undefined ' && obj[0].ECCNCode != '' && obj[0].ECCNCode.length > 0) {
                $("#spnIHSECCNCode").show();
                $("#spnIHSECCNCode").text(" ( " + obj[0].ECCNCode + " ) ");
                document.getElementById("lblECCNCode").style.color = 'yellow'
                document.getElementById("spnIHSECCNCode").style.color = ''

            }
            else {
                $("#spnIHSECCNCode").show();
                $("#spnIHSECCNCode").text("( N/A )");
                document.getElementById("lblECCNCode").style.color = '#d3fFcC'
                document.getElementById("spnIHSECCNCode").style.color = 'yellow'
            }
            //code for ECCN Code end

            if (obj[0].Packaging != 'undefined ' && obj[0].Packaging != '') {
                this._hidPackaging = obj[0].Packaging;
            }
            else {
                this._hidPackaging = "";
            }
            if (obj[0].PackagingSize != 'undefined ' && obj[0].PackagingSize != '') {
                this._hidPackagingSize = obj[0].PackagingSize;
            }
            else {
                this._hidPackagingSize = "";
            }


        }
        $('#divLoader').hide();
        $("#myModal").hide();
    },
    getIHSDataGridError: function (args) {
        $('#divLoader').hide();
    },

    getDataGridError: function (args) {

    },
    getIHSDataSelected: function () {
        $('.okbtn').show();
    },
    getValuesByPartsOnClick: function () {

        var PartNo = this._ctltblPartdetails._tblResults._varSelectedValue;
        if (PartNo.length > 0) {
            this.getValuesByParts();
        }
        // this.Canceltop();
    },
    getValuesByParts: function () {
        var obj = this._ctltblPartdetails._tblResults.getSelectedExtraData(); if (!obj) return;
        this.getIHSValuesByParts(obj.IHSPartsId, obj.ROHSName, obj.ROHSNo, obj.Manufacturer, obj.IHSProdDesc, obj.Packaging, obj.HTSCode, obj.IHSDutyCode, obj.CountryOfOrigin, obj.CountryOfOriginNo, obj.PackagingSize);

    },
    getDataGrid: function (args) {

        //alert(args._result.Result);
        //$('#divLoader').hide();
        //if (args._result.Result) {
        //    this.loadIHSGrid();
        //    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl08").show();
        //}
        //else {
        //    this.loadIHSGrid();
        //}
        $('#divLoader').hide();

        var ihsmsg = args._result.ServiceStatus;

        if (ihsmsg == false) {
            $("#lblIhsServiceMessage").text("Sorry, the IHS part lookup service is not currently available.");
            $R_FN.showElement(this._lblError, true);
            $R_FN.showElement(this._pnlPartDetail, false);
            this._ctltblPartdetails._tblResults.clearTable();
            this._ctltblPartdetails._tblResults.resizeColumns();
        }
        else {
            if (args._result.Result) {
                this.loadIHSGrid();
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl08").show();
            }
            else {
                this.loadIHSGrid();
            }
        }
    },
    Toggle2: function () {
        $("#lblIhsServiceMessage").text("");
        $('input:radio[name="searchType"][value="contains"]').prop('checked', true);
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl08").hide();
        $get(this.getFormControlID(this._element.id, 'SearchtxtPartNo')).value = "";
        $("#lblihserror").text("");
        $R_FN.showElement(this._pnlPartDetail, false);
        this._ctltblPartdetails._tblResults.clearTable();
        this._ctltblPartdetails._tblResults.resizeColumns();
        this._ctltblPartdetails.showNoneFound(true);

    },
    getParSearch: function () {
        //  alert(this._tblPartdetails._varSelectedValue);
        //  this._intCustomerRequirementID = this._tblStock._varSelectedValue;
        this.setFieldValue("ctlDateCode", "");
        this.setFieldValue("ctlProduct", 0, null, "");
        this.setFieldValue("ctlPackage", -1);
        this.setFieldValue("ctlROHS", -1);
        this.setFieldValue("ctlManufacturer", 0, null, "");

        var PartNo = this._tblPartdetails._varSelectedValue;
        if (PartNo.length > 0) {
            this.getValuesByParts();

        }
    },

    //getValuesByParts: function () {

    //    var obj = this._tblPartdetails.getSelectedExtraData(); if (!obj) return;
    //    var PartName = obj.PartName;
    //    $R_FN.showElement(this._btn2, true);
    //    //$R_FN.showElement(this._btn1, false);
    //    this.setFieldValue("ctlDateCode", obj.DateCodeOriginal);
    //    //this.setFieldValue("ctlProduct", obj.ProductNo, null, obj.ProductDescription);
    //    //this.setFieldValue("ctlProduct", 0, null, "");
    //    if (!obj.ProductInactive)
    //        this.setFieldValue("ctlProduct", obj.ProductNo, null, obj.ProductDescription);
    //    else
    //        this.setFieldValue("ctlProduct", 0, null, ""); //Disable due to inactive product
    //    this.setFieldValue("ctlPackage", obj.PackageNo);
    //    this.setFieldValue("ctlROHS", obj.ROHSNo);
    //    this.setFieldValue("ctlManufacturer", obj.ManufacturerNo, null, obj.Manufacturer);
    //    this.setFieldValue("ctlPartNo", PartName);

    //    this.getPartDetail(PartName, obj.ResultType == "S" ? obj.StockId : 0);
    //    //alert(obj.StockId);
    //    //alert(obj.ResultType);
    //    this.showDetailDiv(true);
    //    //return;
    //},
    //ihs code end

    updateCurrency: function () {
        // alert($find(this.getField("ctlSupplier")).getExtraText());
        //$R_FN.setInnerHTML(this._lblcurrecny, this.getFieldDropDownExtraText("ctlGlobalCurrency"));
        $R_FN.setInnerHTML(this._lblSupplierPrice, this.getFieldDropDownExtraText("ctlGlobalCurrency"));
        $R_FN.setInnerHTML(this._lblUpliftPrice, String.format("{0} ( {1} % )", this.getFieldDropDownExtraText("ctlGlobalCurrency"), this._upliftPer));
    },

    getDropDownsData: function () {

        this.getFieldDropDownData("ctlROHS");
        if (this._blnISAS6081Required == true) {
            this.showField("ctlTypeOfSupplier", true);
            this.showField("ctlReasonForSupplier", true);
            this.showField("ctlRiskOfSupplier", true);
            this.getFieldDropDownData("ctlTypeOfSupplier");
            this.getFieldDropDownData("ctlReasonForSupplier");
            this.getFieldDropDownData("ctlRiskOfSupplier");

            if (!$("#spnTypeOfSupplier").hasClass("requiredField")) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlTypeOfSupplier_tdTitle").append("<span id='spnTypeOfSupplier' class='requiredField'>*</span>");
            }
            if (!$("#spnReasonForSupplier").hasClass("requiredField")) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlReasonForSupplier_tdTitle").append("<span id='spnReasonForSupplier' class='requiredField'>*</span>");
            }
            if (!$("#spnRiskOfSupplier").hasClass("requiredField")) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlRiskOfSupplier_tdTitle").append("<span id='spnRiskOfSupplier' class='requiredField'>*</span>");
            }
            if (!$("#spnLocation").hasClass("requiredField")) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl_tdTitle").append("<span id='spnLocation' class='requiredField'>*</span>");
            }
        } else {
            this.showField("ctlTypeOfSupplier", false);
            this.showField("ctlReasonForSupplier", false);
            this.showField("ctlRiskOfSupplier", false);
        }

        //this.getFieldControl("ctlLinkCurrency")._intCustomerClientNo = 101;// this._ClientNo;
        //this.getFieldControl("ctlLinkCurrency")._intBuyCurrencyNo = 244;// this._ActBuyCurrencyNo;
        //this.getFieldDropDownData("ctlCurrencyNew");
        //this.getFieldDropDownData("ctlPackage");
        //this.getFieldDropDownData("ctlCurrencyNew");
        this.getFieldDropDownData("ctlOfferStatus");
        this.getFieldDropDownData("ctlRegion");
        this.getFieldDropDownData("ctlMSL");
        this.getFieldControl("ctlCountryOfManufacture")._intGlobalLoginClientNo = this._intGlobalClientNo;
        this.getFieldDropDownData("ctlCountryOfManufacture");
    },

    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("AddNew");
        obj.addParameter("CustomerRequirementID", this._intCustomerRequirementID);
        obj.addParameter("Part", this.getFieldValue("ctlPartNo"));
        obj.addParameter("Manufacturer", this.getFieldValue("ctlManufacturer"));
        obj.addParameter("DateCode", this.getFieldValue("ctlDateCode"));
        obj.addParameter("Package", this.getFieldValue("ctlPackage"));
        obj.addParameter("Product", this.getFieldValue("ctlProduct"));
        obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
        obj.addParameter("Price", this.getFieldValue("ctlPrice"));

        //if (this._blnPOHub)
        //    obj.addParameter("Price", this.getFieldValue("ctlPrice"));
        //else
        //    obj.addParameter("Price", this.getFieldValue("ctlPriceClient"));


        //obj.addParameter("Currency", this.getFieldValue("ctlLinkCurrency"));
        obj.addParameter("OfferStatus", this.getFieldValue("ctlOfferStatus"));
        obj.addParameter("ROHS", this.getFieldValue("ctlROHS"));
        obj.addParameter("SupplierNo", this.getFieldValue("ctlSupplier"));
        obj.addParameter("SupplierName", this.getFieldComboText("ctlSupplier"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        //Added New Fields
        obj.addParameter("SUPPrice", this.getFieldValue("ctlSupplierPrice"));
        obj.addParameter("EstimatedShippingCost", this.getFieldValue("ctlEstimatedShippingCost"));
        obj.addParameter("DeliveryDate", this.getFieldValue("ctlDeliveryDate"));

        obj.addParameter("SPQ", this.getFieldValue("ctlSPQ"));
        obj.addParameter("LeadTime", this.getFieldValue("ctlLeadTime"));
        obj.addParameter("ROHSStatus", this.getFieldValue("ctlROHSStatus"));
        obj.addParameter("FactorySealed", this.getFieldValue("ctlFactorySealed"));
        obj.addParameter("MSL", this.getFieldValue("ctlMSL"));

        obj.addParameter("SupplierTotalQSA", this.getFieldValue("ctlTQSA"));
        obj.addParameter("SupplierMOQ", this.getFieldValue("ctlMOQ"));
        obj.addParameter("SupplierLTB", this.getFieldValue("ctlLTB"));
        obj.addParameter("Region", this.getFieldValue("ctlRegion"));
        obj.addParameter("Currency", this.getFieldValue("ctlGlobalCurrency"));
        obj.addParameter("SupplierWarranty", this.getFieldValue("ctlSupplierWarranty"));
        //obj.addParameter("LinkCurrencyNo", this.getFieldValue("ctlCurrencyNew")); //this.getFieldDropDownExtraText("ctlLinkCurrency")
        //[002] start
        obj.addParameter("TestingRecommended", this.getFieldValue("ctlTestingRecommended"));
        //[002] end
        obj.addParameter("CountryOfOriginNo", this.getFieldValue("ctlCountryOfManufacture"));
        obj.addParameter("TypeOfSupplier", this.getFieldValue("ctlTypeOfSupplier"));
        obj.addParameter("ReasonForSupplier", this.getFieldValue("ctlReasonForSupplier"));
        obj.addParameter("RiskOfSupplier", this.getFieldValue("ctlRiskOfSupplier"));



        if (this._CountryNo > 0)
        {
            obj.addParameter("CountryNo", this._CountryNo);
        }
        else
        {
            obj.addParameter("CountryNo", this.getFieldValue("ctlCompanyNameddl"));
        }
        var buyPrice = parseFloat(this.getFieldValue("ctlSupplierPrice"));
        var sellPrice = parseFloat(this.getFieldValue("ctlPrice"));
        var reason = (buyPrice > sellPrice) ? this.getFieldValue("ctlSellPriceLessReason") : "";
        obj.addParameter("SellPriceLessReason", reason);

        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null; buyPrice = null; sellPrice = null; reason = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.Result) {
            this.onSaveComplete();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide();
        } else {
            this._strErrorMessage = args._errorMessage;
            if (args._result.Msg != "undefined" && args._result.Msg.length > 0) {
                this.showError(true, args._result.Msg);
            }
            else {
                this.onSaveError();
            }
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        if (this.get_NonPreferredCompany() == true && this.getFieldValue("ctlSupplierWarranty").length < 1) {
            blnOK = false;
            this.showError(true, $R_RES.SupplierWarranty);
        }
        if (this._blnISAS6081Required == true) {
            if (!this.checkFieldEntered("ctlTypeOfSupplier")) {
                blnOK = false;
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlTypeOfSupplier").addClass("formRowError");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlTypeOfSupplier_pnlLoading").removeClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlTypeOfSupplier_pnlLoading").removeClass("formFieldLoading");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlTypeOfSupplier_pnlLoading").html('<span style="margin-left: 25px;">Please enter a value</span>');
            }
            else {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlTypeOfSupplier_pnlLoading").addClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlTypeOfSupplier_pnlLoading").addClass("formFieldLoading");
            }
            if (!this.checkFieldEntered("ctlReasonForSupplier")) {
                blnOK = false;
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlReasonForSupplier").addClass("formRowError");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlReasonForSupplier_pnlLoading").removeClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlReasonForSupplier_pnlLoading").removeClass("formFieldLoading");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlReasonForSupplier_pnlLoading").html('<span style="margin-left: 25px;">Please enter a value</span>');
            }
            else {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlReasonForSupplier_pnlLoading").addClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlReasonForSupplier_pnlLoading").addClass("formFieldLoading");
            }
            if (!this.checkFieldEntered("ctlRiskOfSupplier")) {
                blnOK = false;
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlRiskOfSupplier").addClass("formRowError");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlRiskOfSupplier_pnlLoading").removeClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlRiskOfSupplier_pnlLoading").removeClass("formFieldLoading");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlRiskOfSupplier_pnlLoading").html('<span style="margin-left: 25px;">Please enter a value</span>');
            }
            else {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlRiskOfSupplier_pnlLoading").addClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlRiskOfSupplier_pnlLoading").addClass("formFieldLoading");
            }
            if (this._CountryNo == 0) {
                if (!this.checkFieldEntered("ctlCompanyNameddl")) {
                    blnOK = false;
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl").addClass("formRowError");
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl_pnlLoading").removeClass("invisible");
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl_pnlLoading").removeClass("formFieldLoading");
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl_pnlLoading").html('<span style="margin-left: 25px;">Please enter a value</span>');
                }
                else {
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl_pnlLoading").addClass("invisible");
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl_pnlLoading").addClass("formFieldLoading");
                }
            }
            if (blnOK == false) {
                this.showError(true, "");
            }
        }

        //validate reason for sell price < buy price
        var buyPrice = parseFloat(this.getFieldValue("ctlSupplierPrice"));
        var sellPrice = parseFloat(this.getFieldValue("ctlPrice"));
        var sellPriceLessReason = this.getFieldValue("ctlSellPriceLessReason");
        if (buyPrice > sellPrice && sellPriceLessReason == "") {
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessReason").addClass("formRowError");
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessReason_pnlLoading").removeClass("invisible");
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessReason_pnlLoading").removeClass("formFieldLoading");
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessReason_pnlLoading").html('<span style="margin-left: 10px;">Please enter a value</span>');
            blnOK = false;
            this.showError(true, "");
        } else {
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessReason").removeClass("formRowError");
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessReason_pnlLoading").addClass("invisible");
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSellPriceLessReason_pnlLoading").addClass("formFieldLoading");
        }
        
        return blnOK;
    },

    supplierChangeEvent: function () {

        if (this._intSupplierId != this.getFieldValue("ctlSupplier")) {
            this._intSupplierId = this.getFieldValue("ctlSupplier");
            this.getPurchaseData();
        }
        if (this._blnISAS6081Required == true) {
            this.GetCountry();
        }
        
    },
    getPurchaseData: function () {
        // this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        this._strPath = "controls/Nuggets/PurchaseRequestLineDetail";
        this._strData = "PurchaseRequestLineDetail";
        obj.set_PathToData(this._strPath);
        obj.set_DataObject(this._strData);
        obj.set_DataAction("GetDefaultPurchasingInfo");
        obj.addParameter("id", this.getFieldValue("ctlSupplier"));
        obj.addDataOK(Function.createDelegate(this, this.getPurchaseDataOK));
        obj.addError(Function.createDelegate(this, this.getPurchaseDataError));
        obj.addTimeout(Function.createDelegate(this, this.getPurchaseDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getPurchaseDataOK: function (args) {
        var res = args._result;
        this._intGlobalCurrencyNo = res.GlobalCurrencyNo;

        this._intPOCurrencyNo = res.CurrencyNo;
        // this._PocurrencyCode = res.Currency;

        $R_FN.setInnerHTML(this._lblSupplierPrice, res.Currency);
        this._upliftPer = res.UpliftPer;
        $R_FN.setInnerHTML(this._lblUpliftPrice, String.format("{0} ( {1} % )", res.Currency, res.UpliftPer));
        this._intESTShippingCost = res.ESTShippingCost;
        this.setFieldValue("ctlEstimatedShippingCost", $R_FN.formatCurrency(this._intESTShippingCost, "", 5));
        //[001] Start Here
        this.set_NonPreferredCompany(res.NonPreferredCompany);
        this.setFieldValue("ctlSupplierWarranty", res.SupplierWarranty);
        //[001] End Here

        var selectedCompanyLabel = $find(this.getField("ctlSupplier").ControlID)._aut._lblSelectedValue;
        $(selectedCompanyLabel).parent().find('.advisory-notes').remove();
        $(selectedCompanyLabel).append($R_FN.createAdvisoryNotesIcon(res.CompanyAdvisoryNotes, 'margin-left-10'));

        this.bindCurrency();
        //this.getDataOK_End();
        this.focusOnPrice();
    },
    getPurchaseDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    bindCurrency: function () {
        // alert(this._intPOCurrencyNo);
        this.getFieldControl("ctlGlobalCurrency")._intGlobalCurrencyNo = this._intGlobalCurrencyNo;
        this.getFieldDropDownData("ctlGlobalCurrency");
        //[001] Start Here
        this.setFieldValue("ctlGlobalCurrency", this._intPOCurrencyNo);
        // this.updateCurrency();
        // $R_FN.setInnerHTML(this._lblcurrecny, this._PocurrencyCode);
        //[001] End Here
        //this.updateCurrency();


    },
    //findCountryRiskData: function () {
    //    var result = (this.getFieldDropDownExtraText("ctlCountryOfManufacture"));
    //    if (result != "") {
    //        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text(result);
    //    }
    //    else {
    //        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text('');
    //    }
    //},
    findCountryRiskData: function () {
        //alert(this.getFieldValue("ctlCountryOfManufacture"));
        this._intManufacturerNo = this.getFieldValue("ctlCountryOfManufacture");
        this.getRsMfr(this._intManufacturerNo);

        //var result = (this.getFieldDropDownExtraText("ctlCountryOfManufacture"));
        //if (result != "") {

        //    $("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text(result);
        //}
        //else {
        //    $("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text('');
        //}
    },
    getRsMfr: function (RsManufacturerNo) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/POReceivingLines");
        obj.set_DataObject("POReceivingLines");
        obj.set_DataAction("GetWarningMessage");
        obj.addParameter("RsManufacturerNo", RsManufacturerNo);
        obj.addDataOK(Function.createDelegate(this, this.getRsMfrOK));
        obj.addError(Function.createDelegate(this, this.getRsMfrError));
        obj.addTimeout(Function.createDelegate(this, this.getRsMfrError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getRsMfrOK: function (args) {
        var res = args._result;
        this._RestrictedMFRMessage = res.WorningMessage;
        if (this._RestrictedMFRMessage != "") {
            //this.setFieldValue("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus", $R_FN.showIHSstatusDefi("", this._RestrictedMFRMessage ));
            //$("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text(this._RestrictedMFRMessage);
            //$("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text($R_FN.showIHSstatusDefi(res.LifeCycleStage, res.IHSStatusDefination);
            //$find(this.getFormControlID(this._element.id, 'lblCountryOfOrigin')).setValue("Test");
            //this.setFieldValue("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryOfOrigin", $R_FN.showRestCountry("Active", this._RestrictedMFRMessage));
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").show();
            document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus').setAttribute('title', this._RestrictedMFRMessage);
        }
        else {
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").hide();
        }


    },
    getRsMfrError: function () {

    },
    GetCountry: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("GetCountry");
        obj.addParameter("SupplierNo", this.getFieldValue("ctlSupplier"));
        obj.addDataOK(Function.createDelegate(this, this.GetCountryOK));
        obj.addError(Function.createDelegate(this, this.GetCountryError));
        obj.addTimeout(Function.createDelegate(this, this.GetCountryError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    GetCountryOK: function (args) {
        var res = args._result;
        if (res.IsCountryFound == true) {
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNamelbl").show();
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl").hide();
            this.setFieldValue("ctlCompanyNamelbl", res.CountryName);
            this.showField("ctlCompanyNamelbl", true);
            this.showField("ctlCompanyNameddl", false);
            this._CountryNo = res.CountryNo;
           
        }
        else {
            this.showField("ctlCompanyNamelbl", false);
            this.showField("ctlCompanyNameddl", true);
            this.getFieldDropDownData("ctlCompanyNameddl");
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNamelbl").hide();
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl").show();
            this._CountryNo = 0;
        }
        

        
    },
    GetCountryError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    GetLocationDropDown: function () {
        this.showField("ctlCompanyNamelbl", false);
        this.showField("ctlCompanyNameddl", true);
        this.getFieldDropDownData("ctlCompanyNameddl");
        this._CountryNo = 0;
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNamelbl").hide();
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl").show();
    },
    getMfrNotes: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        this._strPath = "controls/Nuggets/ManufacturerMainInfo";
        this._strData = "ManufacturerMainInfo";
        obj.set_PathToData(this._strPath);
        obj.set_DataObject(this._strData);
        obj.set_DataAction("GetAdvisoryNotes");
        obj.addParameter("ID", this.getFieldValue("ctlManufacturer"));
        obj.addDataOK(Function.createDelegate(this, this.getMfrNotesOK));
        obj.addError(Function.createDelegate(this, this.getMfrNotesError));
        obj.addTimeout(Function.createDelegate(this, this.getMfrNotesError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getMfrNotesOK: function (args) {
        var res = args._result;
        var selectedMfrLabel = $find(this.getField("ctlManufacturer").ControlID)._aut._lblSelectedValue;
        $(selectedMfrLabel).parent().find('.advisory-notes').remove();
        $(selectedMfrLabel).append($R_FN.createAdvisoryNotesIcon(res.MfrAdvisoryNotes, 'margin-left-10'));
    },
    getMfrNotesError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
