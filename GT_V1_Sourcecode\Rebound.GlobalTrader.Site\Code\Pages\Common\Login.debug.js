///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Login");

Rebound.GlobalTrader.Site.Pages.Login = function() { 
	this._intPauseTime = 250; //milliseconds
};

Rebound.GlobalTrader.Site.Pages.Login.prototype = {

	initialize: function() {
		if (this._blnCheckForDatabase) this.checkDatabaseConnection();
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._pnlDBCheck = null;
		this._pnlCheckingDatabase = null;
		this._pnlNoDatabase = null;
		this._pnlDatabaseOK = null;
		this._pnlLogin = null;
		this.isDisposed = true;
	},
	
	checkDatabaseConnection: function() {
		$R_FN.showElement(this._pnlDBCheck, true);
		$R_FN.showElement(this._pnlCheckingDatabase, true);
		$R_FN.showElement(this._pnlNoDatabase, false);
		$R_FN.showElement(this._pnlDatabaseOK, false);
		$R_FN.showElement(this._pnlLogin, false);
		setTimeout(Function.createDelegate(this, this.doDatabaseCheck), this._intPauseTime);
	},
	
	doDatabaseCheck: function() {
		Rebound.GlobalTrader.Site.WebServices.CheckDatabaseConnection(Function.createDelegate(this, this.databaseOK), Function.createDelegate(this, this.databaseError));
	},
	
	databaseOK: function(strError) {
		$R_FN.showElement(this._pnlCheckingDatabase, false);
		if (strError.length == 0) {
			setTimeout(Function.createDelegate(this, this.showLogin), this._intPauseTime);
			$R_FN.showElement(this._pnlNoDatabase, false);
			$R_FN.showElement(this._pnlDatabaseOK, true);
		} else {
			this.databaseError();
		}
	},
	
	databaseError: function() {
		$R_FN.showElement(this._pnlCheckingDatabase, false);
		$R_FN.showElement(this._pnlNoDatabase, true);
		$R_FN.showElement(this._pnlLogin, false);
	},
	
	showLogin: function() {
		$R_FN.showElement(this._pnlLogin, true);
		$R_FN.showElement(this._pnlDBCheck, false);
	},
	
	doReboundClientChoice: function(intClientID, strRedirectPath) {
		Rebound.GlobalTrader.Site.WebServices.DoReboundClientChoice(intClientID, strRedirectPath, Function.createDelegate(this, this.doReboundClientChoiceOK));
	},
	
	doReboundClientChoiceOK: function(str) {
		if (str.length > 0) location.href = str;
	}
	
};

Rebound.GlobalTrader.Site.Pages.Login.registerClass("Rebound.GlobalTrader.Site.Pages.Login", null, Sys.IDisposable);
