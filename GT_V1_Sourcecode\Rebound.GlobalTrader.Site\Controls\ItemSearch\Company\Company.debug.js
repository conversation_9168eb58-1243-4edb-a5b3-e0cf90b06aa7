///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//test ---- 
//
// dfhdshfhsd
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.Company = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.prototype = {

    get_blnForPOs: function() { return this._blnForPOs; }, set_blnForPOs: function(v) { if (this._blnForPOs !== v) this._blnForPOs = v; },
    get_blnForSOs: function() { return this._blnForSOs; }, set_blnForSOs: function(v) { if (this._blnForSOs !== v) this._blnForSOs = v; },
    get_SupplierOnStop: function() { return this._SupplierOnStop; }, set_SupplierOnStop: function(v) { if (this._SupplierOnStop !== v) this._SupplierOnStop = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.callBaseMethod(this, "initialize");
        this.addSetupData(Function.createDelegate(this, this.doSetupData));
        this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnForPOs = null;
        this._blnForSOs = null;
        this._SupplierOnStop = null;
        Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.callBaseMethod(this, "dispose");
    },

    doSetupData: function() {
        this._objData.set_PathToData("controls/ItemSearch/Company");
        this._objData.set_DataObject("Company");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("Name", this.getFieldValue("ctlCompanyName"));
        if (this._blnForPOs) this._objData.addParameter("POApproved", true);
        if (this._blnForSOs) this._objData.addParameter("SOApproved", true);
        if (this._SupplierOnStop) this._objData.addParameter("SupplierStop", true);
    },

    doGetDataComplete: function() {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
                $R_FN.setCleanTextValue(row.Name) + $R_FN.createAdvisoryNotesIcon(row.AdvisoryNotes, 'margin-left-10'),
				$R_FN.setCleanTextValue(row.Type),
				$R_FN.setCleanTextValue(row.City),
				$R_FN.setCleanTextValue(row.Country),
				$R_FN.setCleanTextValue(row.Tel),
				$R_FN.setCleanTextValue(row.Salesperson),
				$R_FN.setCleanTextValue(row.LastContact)
			];
            this._tblResults.addRow(aryData, row.ID, false, {CompanyName: row.Name, Traceability: row.Traceability, SalemanNo: row.SalespersonNo, AdvisoryNotes: row.AdvisoryNotes });
            aryData = null; row = null;
        }
    }

};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Company.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Company", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
