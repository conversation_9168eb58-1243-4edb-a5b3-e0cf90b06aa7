﻿/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for Purchage Order section
[002]      Vinay           01/11/2012   Add comma(,) seprated Debit and SRMA in purchase order section  
[003]      Abhinav         06/11/2013   Add VATNO, CuostmerCode in Print document
[004]      Vinay           17/01/2014   ESMS Ticket No : 95
[005]      Aashu           01/06/2018   Quick Jump in Global Warehouse
[006]      Aashu           05/06/2018   Show Billed to address on PO
[007]      <PERSON><PERSON><PERSON>     22-Aug-2018  REB-12084:Lock PO lines when EPR is authorised
[008]      <PERSON><PERSON><PERSON>     28-Sep-2018  REB-13083 Change request PO - delivery status
[009]      <PERSON>     17-08-2020   Change request Print Po Invoice add header
[010]      Abhinav <PERSON>a  03-08-2021   Add properties for the blackbox
[011]      Abhinav <PERSON>  08-09-2021   Add new properties for supplier Approval nugget.
[012]      Abhinav <PERSON>xena  08-09-2021   Add new properties for supplier Approval nugget.
[RP-2882]  <PERSON>		23-01-2024	Sanctioned warning messages problem
 * 
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class PurchaseOrderSaleSupportDetails
    {
		
		#region Constructors
		
		public PurchaseOrderSaleSupportDetails() { }

        #endregion

        /// <summary>
        /// PurchaseOrderId
        /// </summary>
        public System.Int32 PurchaseOrderId { get; set; }
        public System.Int32 InternalPurchaseOrderId { get; set; }

        /// <summary>
        /// PurchaseOrderNumber
        /// </summary>
        public System.Int32 PurchaseOrderNumber { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }

        /// <summary>
        /// Buyer
        /// </summary>
        public System.Int32 Buyer { get; set; }

        /// <summary>
        /// BuyerName
        /// </summary>
        public System.String BuyerName { get; set; }
        public System.Int32? IPOSupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String IPOSupportTeamMemberName { get; set; }
        /// <summary>
        /// Buyer
        /// </summary>
        public System.Int32 IPOBuyer { get; set; }
        /// <summary>
        /// BuyerName
        /// </summary>
        public System.String IPOBuyerName { get; set; }

    }
}