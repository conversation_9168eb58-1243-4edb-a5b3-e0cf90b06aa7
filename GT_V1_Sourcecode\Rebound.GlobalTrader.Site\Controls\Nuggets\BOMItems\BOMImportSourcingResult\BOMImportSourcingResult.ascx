﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="BOMImportSourcingResult.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMImportSourcingResult" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<%--<script src="js/jquery.dataTables.min.js" defer></script>
<%--<script src="js/dataTables.cellEditSourcing.js" defer></script>
<script src="js/dataTables.cellEditOffers.js" defer></script>--%>
<%--<link href="css/uploadfile.css" rel="stylesheet">--%>
<script src="js/jquery.uploadfile.js"></script>
<script src="js/jquery.dataTables.min.js"  ></script>
<link href="css/jquery-ui.css.css" rel="stylesheet" />
<script src="js/jquery-ui.js"></script>
<%--<script src="css/jquery-ui.js" integrity="sha256-xI/qyl9vpwWFOXz7+x/9WkG5j/SVnSw21viy8fWwbeE=" crossorigin="anonymous"></script>--%>
<style>
    .hide_column {
        display: none;
    }

    .table-edit {
        text-align: left;
        margin-right: 8px;
        background-image: url(../App_Themes/Original/images/IconButton/nuggets/edit.gif);
        padding: 2px 0px 5px 21px;
        position: relative;
        top: 1px;
    }

    .table-edit1 {
        text-align: left;
        margin-right: 8px;
        background-image: url(../App_Themes/Original/images/IconButton/nuggets/edit.gif);
        padding: 2px 0px 5px 21px;
        position: relative;
        top: 1px;
    }

    .table-delete {
        text-align: left;
        margin-right: 8px;
        background-image: url(../App_Themes/Original/images/IconButton/nuggets/delete.gif);
        padding: 2px 0px 5px 21px;
        position: relative;
        top: 1px;
    }

    table.dataTable td.dataTables_empty {
        padding-left: 500px;
    }
    /*Popup CSS*/
    /* Popup box BEGIN */
    .hover_bkgr_fricc {
        background: rgba(0,0,0,.4);
        cursor: pointer;
        display: none;
        /*height: auto;*/
        height: 100%;
        position: absolute;
        text-align: center;
        top: -49px;
        width: 100%;
        z-index: 10000;
    }

        .hover_bkgr_fricc .helper {
            display: inline-block;
            height: 100%;
            vertical-align: middle;
        }

        .hover_bkgr_fricc > div {
            background-color: #00994D;
            box-shadow: 10px 10px 60px #555;
            display: inline-block;
            height: auto;
            /*max-width: auto;*/
            min-height: 100px;
            vertical-align: middle;
            width: 65%;
            position: relative;
            border-radius: 8px;
            padding: 15px 5%;
        }

    .popupCloseButton {
        background-color: #F98;
        border: 3px solid #999;
        border-radius: 50px;
        cursor: pointer;
        display: inline-block;
        font-family: arial;
        font-weight: bold;
        position: absolute;
        top: -20px;
        right: -20px;
        font-size: 25px;
        line-height: 30px;
        width: 30px;
        height: 30px;
        text-align: center;
    }

        .popupCloseButton:hover {
            background-color: #ccc;
        }

    .btnclass {
        width: 25% !Important;
        background-color: #008CBA !Important;
        color: white !Important;
        padding: 12px 3px 28px 4px !Important;
        margin: 8px 0 !Important;
        border: none !Important;
        border-radius: 4px !Important;
        cursor: pointer !Important;
        height: 30px;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        width: 88%;
        text-align: right;
        height: 60px;
        border-radius: 10%;
        background-color: #C0C0C0;
        vertical-align: central;
        color: black;
        margin: 0 5px;
        border: 0 !important;
        line-height: 17px;
        box-shadow: none !important;
    }


    fieldset {
        font-size: 12px;
        padding: 10px;
        width: 95%;
        line-height: 1.8;
    }


    .btnclass {
        width: 25% !Important;
        background-color: #008CBA !Important;
        color: white !Important;
        padding: 12px 3px 28px 4px !Important;
        margin: 8px 0 !Important;
        border: none !Important;
        border-radius: 4px !Important;
        cursor: pointer !Important;
        height: 30px;
    }

    .dataTables_wrapper .dataTables_paginate .paginate_button {
        width: 88%;
        text-align: right;
        height: 60px;
        border-radius: 10%;
        background-color: #C0C0C0;
        vertical-align: central;
        color: black;
        margin: 0 5px;
        border: 0 !important;
        line-height: 17px;
        box-shadow: none !important;
    }

    * {
        box-sizing: border-box;
    }

    .boxEpotool {
        background-repeat: repeat-x;
        background-position: bottom;
        padding: 8px 5px 4px;
        font-size: 11px;
    }

    .buttonEpo {
        background-color: #555555; /* Green */
        border: none;
        color: white;
        text-align: center;
        text-decoration: none;
        display: inline-block;
        cursor: pointer;
        height: 17px;
        width: 16px;
    }

    #divEpohistory {
        border: 1px solid #3a6c34;
        overflow: scroll;
        width: 96%;
        height: 300px;
        display: block;
        margin-top: 36px;
        padding: 2px;
    }
    .notification{background:#10101026;
    color: #fff!important;
    text-align: center;
    display: inline-block;
    margin-left: 6px;
    align-items: center;
    padding: 1px 7px 3px 7px!important;}
    .folowupline {
        font-weight: normal;border: none;font-size: 12px;color: #2f442c;
    }
</style>
<style>
    .alltabblock ul {
        margin: 0px;
        padding: 0px;
    }

        .alltabblock ul li {
            display: inline-block;
            background-color: #3a6c34;
            padding: 15px 18px;
            position: relative;
            min-width: 154px;
            border-radius: 6px;
            text-align: center;
            font-size: 12px;
            margin: 0px;
        }

            .alltabblock ul li:hover {
                background-color: #c0c0c0;
                cursor: pointer;
            }

                .alltabblock ul li:hover:after {
                    border-top: #222e39;
                }

            .alltabblock ul li.currentepo {
                background-color: #98de90;
                color: #3a6c34;
            }

                .alltabblock ul li.currentepo:after {
                    width: 0;
                    height: 0;
                    border-left: 15px solid transparent;
                    border-right: 15px solid transparent;
                    border-top: 15px solid #98de90;
                    content: " ";
                    position: absolute;
                    bottom: -15px;
                    left: 35%;
                }

    .alltabblock a, .alltabblock #showall {
        display: inline-block;
        margin: 0 1%;
        padding: 2%;
        border-radius: 10%;
        -webkit-box-shadow: 0px 3px 11px 2px rgba(0,0,0,0.2);
        -moz-box-shadow: 0px 3px 11px 2px rgba(0,0,0,0.2);
        box-shadow: 0px 3px 11px 2px rgba(0,0,0,0.2);
    }

        .alltabblock a:nth-child(1) {
            background: #E63946;
            color: #eee;
        }

        .alltabblock a:nth-child(2) {
            background: #F1FAEE;
            color: #222;
        }

        .alltabblock a:nth-child(3) {
            background: #A8DADC;
            color: #222;
        }

        .alltabblock a:nth-child(4) {
            background: #457B9D;
            color: #eee;
        }

        .alltabblock a:nth-child(5) {
            background: #1D3557;
            color: #eee;
        }

    .alltabblock .cnt {
        margin-top: 25px;
        padding: 15px;
        float: left;
        min-width: 600px;
        border-radius: 5px;
        border: 1px #d3dfe9 solid;
        border-top: 2px #98de90 solid;
    }

        .alltabblock .cnt label {
            display: inline-block;
            width: 100%;
            padding-bottom: 10px;
        }

        .alltabblock .cnt input {
            padding: 10px;
            border-radius: 3px;
            border: 1px #ccc solid;
        }

        .alltabblock .cnt .title, .alltabblock .cnt .item {
            display: block;
            float: left;
            width: 100%;
        }

        .alltabblock .cnt .item {
            width: auto;
        }

    .targetDivEpo1 {
        background-color: #98de90;
        padding: 15px;
        margin-top: 15px;
        border-radius: 5px;
        color: #3a6c34;
    }

        .targetDivEpo1 p {
            width: 100%;
            float: left;
        }

    .targetDivEpo2 {
        background-color: #98de90;
        padding: 15px;
        margin-top: 15px;
        border-radius: 5px;
        color: #3a6c34;
    }

        .targetDivEpo2 p {
            width: 100%;
            float: left;
        }

    .targetDivEpo3 {
        background-color: #98de90;
        padding: 15px;
        margin-top: -9px;
        border-radius: 5px;
        color: #3a6c34;
    }

        .targetDivEpo3 p {
            width: 100%;
            float: left;
        }

    .lablehead {
        float: left;
        width: 16%;
        padding-top: 5px;
    }

    .lableopt {
        float: left;
        background-color: #78bd6d;
        padding: 5px 15px;
        border-radius: 4px;
    }

        .lableopt select {
            width: 60%;
            padding: 5px;
        }

    .btnHistory1, .ajax-file-upload {
        padding: 7px 25px !important;
        width: auto !important;
        background-color: #158684 !important;
        color: #fff !important;
        margin: 0 !important;
    }

    .ajax-file-upload {
        height: 24px !important;
    }

    .btnReset1 {
        background-color: transparent !important;
        border: 1px #cee7c1 solid !important;
        width: 16% !important;
        padding: 5px 25px !important;
    }

    #exceliploadEpo {
        padding: 10px 0px;
		width:210px!important;
    }

    .spanBorder {
        font-size: 10px !important;
        border: 2px dotted #fff;
        border-radius: 3px;
        font-size: 10px !important;
        margin: 0 0 0 -14px;
    }

    .block {
        float: left;
        width: 100%;
        margin-bottom: 15px;
    }

    #chkFileCCHEpo {
        vertical-align: middle;
    }

    #tableEpoUploadTemp_length, #tableEpoUploadTemp_info {
        padding: 10px;
        background: #3a6c34;
        color: #fff;
    }

    #exceliploadEpo input {
        width: 100% !important;
    }

    #tableEpoUploadTemp_paginate {
        margin-top: 5px;
    }

        #tableEpoUploadTemp_paginate a {
            padding: 5px;
            margin: 5px;
            background: #3a6c34;
            color: #fff;
        }

    .targetDivEpo .nestedItem, .targetDivEpo tr td {
        color: #3a6c34 !important;
    }

    .targetDivEpo fieldset {
        border: 1px #3a6c34 solid;
    }

    .spanBorder {
        color: rgb(0, 0, 0);
        margin-left: 20px;
    }
	ul.steps{color: #3c6a37;font-weight: 600;font-size: 11px;}
	ul.steps li{padding-bottom: 3px;}
	ul.steps b{font-size: 14px;padding-bottom: 6px;display: block;}
	.titlerow{float: left;
background: #56954e;
margin-bottom: 10px;width:100%;color: #fff;}
.titlerow p{padding:0px; margin:0px;}
.titlerow b{display: inline-block;
padding: 8px 8px;
border-right: 1px #ddd solid;
margin-right: 0px;}
.titlerow b:first-child{background-color:#444;}
.titlerow b:last-child{border-right:0px #ddd solid!important;}
.spanBorder {
    border: 2px dotted #56954e!important;
    border-radius: 3px;
    margin: 1px 0 0 5px!important;
    padding: 3px 5px 5px 2px!important;
    position: absolute;
    
    text-align: center!important;
    background: #98de90!important;
}
.lableopt{margin-right:15px;}
.btnHistory{height:27px!important;}
</style>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
    <Links>
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server">
            <asp:TableRow>
                <asp:TableCell class="title" RowSpan="2" Style="width: 100%">
        <div style="position: absolute;right: 17px;top: -5px;">
            <span><p  title="Download BOM Import Sourcing Result Template"><a href="User/EpoTemplate/SourcingResultHubTemplate.xlsx"  style="font-size:11px;color:white; " >Download BOM Import Sourcing Result Template</a></p></span>
        </div>
    
   <div class="boxEpotool " style="background-color: #98de90;">
   <input type="button" id="hypShowHideepo1"  class="buttonEpo" value="-">
  
<div id="divepo1" class="targetDivEpo1" style="display:block">
    <div style="float:left; margin-top:-20px;">
	
	 <b>Steps to follow:</b>
	<div class="toggle-btn" style="font-weight: normal;border: none;font-size: 12px;color: #2f442c; cursor: pointer;display: inline-block;">Show/hide</div>
	<div class="steps">
	
     <ul class="steps" style="padding-left: 10px;">
    
	 
	 
	 
	 
	 
   <li class="folowupline" >Upload the sourcing result file </li>
	<li class="folowupline">System will process the uploaded excel file & match with customer requirements on basis of Part & Qty & price.</li> 
	<li class="folowupline">Matched data with will be added with Sourcing result.</li> 
	<li class="folowupline">System show unmatched data in 2 grids one for Customer Requirement & other for Offers</li> 
	<li class="folowupline"> User can match the Manufacture & Vendor as per GT System</li>
	<li class="folowupline">Once unmatched Manufacture & Vendor matched, click on ‘Re-Process’ button to process</li> 
	<li class="folowupline"> System will keep on showing unmatched data in the grid.</li>
	<li class="folowupline">User can click on ‘Reset’ button to delete data from grids to discard those unmatched records. </li> 
       </ul>
	   
	   <script>
$(document).ready(function(){
    // Toggles paragraphs with different speeds
    $(".toggle-btn").click(function(){
        $(".steps").toggle();
        
    });
});
</script>
</div>

        </div>
     <div class="LoaderPopup" id="divLoaderEpo">
      <div>
      <div class="cssload-loader">Uploading..</div> 
      </div>
      </div>
   <div class="block">
   <div class="lablehead">
   <input type="checkbox" name="FileCCH" value="FileCCH"  checked="checked"  style="display:none"   id="chkFileCCHEpo" />
   </div>
   <div class="lableopt">
    <div id="ImagesingleuploadOffer" >Upload</div>
    <div class="clearing"></div>
 </div>
   <div class="uploadblock">   
   <input type="button" id="btnReset" value="Reset"  title="Reset" class="btnHistory">
  </div>
   <script type="text/javascript">
       $(document).ready(function () {
           var chkhead = "YES"
           var Client_type = "";
           formControlId = "<%=this.ClientID%>";
           loginId = "<%=SessionManager.LoginID%>";
           var dragdropObj = $("#ImagesingleuploadOffer").uploadFile({
               url: "DocImage.ashx?mxs=1&type=EXCELUPLOADSOURCINGRESULT&IsDragDrop=true",
               allowedTypes: "csv,xlsx,xls",
               fileName: "myfile",
               section: "BOM_BomImport_Stock",
               autoSubmit: true,
               multiple: false,
               maxFileSize: 79000000,
               showStatusAfterSuccess: false,
               showCancel: true,
               showDone: true,
               async: false,
               uploadDiv: "exceliploadEpo",
               timeout: 6000000,
               dynamicFormData: function () {
                   var data = { section: "BOM_BomImport_Stock" }
                   return data;
               },
               onSuccess: function (files, data, xhr) {
                   var originalFilename = '';
                   var generatedFilename = '';
                   originalFilename = files[0];
                   var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
                   generatedFilename = json.FileName;
                   chkhead = "YES";
                   Client_type = 1;
                   $find("<%=this.ClientID%>").OfferimpotExcelData(originalFilename, generatedFilename, chkhead, Client_type);

               },
               onSelect: function (fup) {
                   var result = true;
                   $find("<%=this.ClientID%>")._dragobj = dragdropObj;

                   return result;
               }
           });
       });
         </script>
</div>
   
<div class="titlerow">
<p><b>Customer Requirement Not Matched</b>
Total No of Records Imported  <label class="notification" for=""   id="lblTotalRecordCount" ></label>
Total No of Records Matched  <label class="notification" for="" id="lblTotalRecordMatchCount" ></label>
Total No of Records Un-Matched  <label class="notification" for="" id="lblTotalRecordNotMatchCount" ></label>
</p>
</div>
<div style="overflow: scroll;  border: solid 1px; overflow-x: scroll; overflow-y: scroll; width: 100%; height: 235px; border-style: ridge;" class="display .dataTables_wrapper .dataTables_paginate .paginate_button overflow: auto">
<table  id="tableBomNotMacthDataLeft" class="table table-striped table-bordered nowrap " cellspacing="0" cellpadding="0" border="1" style="width: 100%; background-color:white; table-layout: auto; border-style: None; border-collapse: collapse;">
 <thead  class="th">
</thead>
</table>
</div>
</div>
</div><br />
<div class="boxEpotool " style="background-color: #98de90;">
<input type="button" id="hypShowHideepo3"  class="buttonEpo" value="-">
<div id="divepo3" class="targetDivEpo3" style="display:block;">
<div class="titlerow">
<p><b>Sourcing Result Not Matched</b>
Total No of Records Imported  <label class="notification" for=""   id="lblTotalRecordCount2" ></label>
Total No of Records Matched  <label class="notification" for="" id="lblTotalRecordMatchCount2" ></label>
Total No of Records Un-Matched  <label class="notification" for="" id="lblTotalRecordNotMatchCount2" ></label>
</p></div>
<div style="overflow: scroll;  border: solid 1px; overflow-x: scroll; overflow-y: scroll; width: 100%; height: 235px; border-style: ridge;" class="display .dataTables_wrapper .dataTables_paginate .paginate_button overflow: auto">
<table  id="tableBomNotMacthDataRight" class="table table-striped table-bordered nowrap " cellspacing="0" cellpadding="0" border="1" style="width: 100%; background-color:white; table-layout: auto; border-style: None; border-collapse: collapse;">
<thead>
</thead>
</table>
</div><br />
<table class="formRows" cellspacing="0" cellpadding="0" border="0" style="border-style:None;border-collapse:collapse;">
<tr>
<td align="center">
<input type="button" id="btnProcess" value="Re-Process" title="Re-Process"  class="stkButton">
</td>
</tr>
</table>
</div>
</div>
<script>
    $(document).ready(function () {
        $("#hypShowHideepo1").click(function () {
            if ($(this).val() == "+") {
                $("#divepo1").show();
                $(this).val("-");

            } else {
                $("#divepo1").hide();
                $(this).val("+");
            }
        });


        $("#hypShowHideepo3").click(function () {
            if ($(this).val() == "+") {
                $("#divepo3").show();
                $(this).val("-");

            } else {
                $("#divepo3").hide();
                $(this).val("+");
            }

        });


    });


</script>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    <style>
                        .nestedItem {
                            width: 40%;
                            padding-right: 10px;
                            padding-bottom: 7px;
                            text-align: left;
                            font-weight: bold;
                            font-size: 11px;
                            color: #d3fFcC;
                        }

                        .nestedSelect {
                            float: right;
                            margin-right: 60px;
                            width: 42%;
                        }

                        .stkSelect {
                            width: 20%;
                        }

                        .stkButton {
                            width: 10% !Important;
                            background-color: #008CBA !Important;
                            color: white !Important;
                            
                            margin: 8px 0 !Important;
                            border: none !Important;
                            border-radius: 4px !Important;
                            cursor: pointer !Important;
                            height: 27px;
                        }

                        .spanBorder {
                            color: rgb(0, 0, 0);
                        }

                        table.stockImportTool tr td {
                            font-size: 11px;
                        }

                        .btnHistory {
                            width: 10% !Important;
                            background-color: #008CBA !Important;
                            color: white !Important;
                            padding: 6px 3px 20px 4px !Important;
                            margin: 8px 0 !Important;
                            border: none !Important;
                            border-radius: 4px !Important;
                            cursor: pointer !Important;
                            height: 21px;
                        }

                        .btnReset {
                            width: 10% !Important;
                            background-color: #008CBA !Important;
                            color: white !Important;
                            padding: 6px 3px 20px 4px !Important;
                            margin: 8px 0 !Important;
                            border: none !Important;
                            border-radius: 4px !Important;
                            cursor: pointer !Important;
                            height: 21px;
                        }

                        .commandExButtonDisabled {
                            background-color: #2d529f;
                            color: gray;
                            font-weight: bold;
                            font-size: 9pt;
                        }

                        .LoaderPopup {
                            background: rgba(0,0,0,.4);
                            cursor: pointer;
                            display: none;
                            /* height: auto; */
                            height: 100%;
                            position: absolute;
                            text-align: center;
                            top: -49px;
                            width: 100%;
                            z-index: 10000;
                        }

                        .cssload-loader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206,66,51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }
                    </style>
                     
                </asp:TableCell>
            </asp:TableRow>


            <%--Add/Edit section --%>
            <asp:TableRow>
                <asp:TableCell ColumnSpan="4">

                    <div class="hover_bkgr_fricc" id="dvAddEdit">
                        <span class="helper"></span>
                        <div>
                            <div id="divclosebutton" class="popupCloseButton">X</div>

                            <table cellspacing="0" cellpadding="0" border="0" id="tbAddEdit" class="tbAddEdit">

                                <tr>
                                    <td colspan="2">
                                        <div class="formHeader">
                                            <h4>Customer Requirement Not Matched Data
                                            </h4>
                                        </div>
                                    </td>
                                </tr>


                                <input type="hidden" id="hdnTempid" />
                                <input type="hidden" id="hdnClientId" />
                                <input type="hidden" id="hdnBomId" />
                                <tr>
                                    <td class="title">Part
                                    </td>
                                    <td style="float: left">
                                        <label for="" id="lblpart"></label>
                                    </td>

                                </tr>
                                <tr>
                                    <td class="title">Quantity
                                    </td>

                                    <td style="float: left">
                                        <label for="" id="lblQuantity"></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Price
                                    </td>
                                    <td style="float: left">
                                        <label for="" id="lbPrice"></label>
                                    </td>
                                </tr>


                                <ReboundUI_Form:FormField ID="ctlManufacturer" runat="server" FieldID="cmbManufacturer" ResourceTitle="Manufacturer" IsRequiredField="true">
                                    <Field>
                                        <table>
                                            <tr>
                                                <td style="width: 200px;">
                                                    <ReboundUI:Combo ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" />
                                                </td>
                                                <td style="width: auto;">
                                                    <label for="" id="lblManufacturer"></label>
                                                </td>
                                            </tr>
                                        </table>
                                    </Field>

                                </ReboundUI_Form:FormField>



                                <tr>
                                    <td colspan="2" class="alignCenter">
                                        <input type="button" id="btnSubmit" value="Save" class="stkButton" />&nbsp
                                        <input type="button" id="btnCancel" value="Cancel" class="stkButton" />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>

                </asp:TableCell>
            </asp:TableRow>

            <asp:TableRow>
                <asp:TableCell ColumnSpan="4">

                    <div class="hover_bkgr_fricc" id="dvAddEditright">
                        <span class="helper"></span>
                        <div>
                            <div id="divclosebuttonright" class="popupCloseButton">X</div>

                            <table cellspacing="0" cellpadding="0" border="0" id="tbAddEditright" class="tbAddEdit">

                                <tr>
                                    <td colspan="2">
                                        <div class="formHeader">
                                            <h4>Sourcing Result Not Matched Data
                                            </h4>
                                        </div>
                                    </td>
                                </tr>

                                <input type="hidden" id="hdnSourcingResultId" />
                                <input type="hidden" id="hdnSourcingClientId" />
                                <tr>
                                    <td class="title">Quoted MPN
                                    </td>
                                    <td style="float: left">
                                        <label for="" id="lblquotedmpn"></label>
                                    </td>

                                </tr>
                                <tr>
                                    <td class="title">Cost $
                                    </td>
                                    <td style="float: left">
                                        <label for="" id="lbCost"></label>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="title">Lead Time
                                    </td>

                                    <td style="float: left">
                                        <label for="" id="lblLeadTime"></label>
                                    </td>
                                </tr>


                                <ReboundUI_Form:FormField ID="FormField1" runat="server" FieldID="cmbManufacturer" ResourceTitle="Manufacturer" IsRequiredField="true">
                                    <Field>
                                        <table>
                                            <tr>
                                                <td style="width: 200px;">
                                                    <ReboundUI:Combo ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" />
                                                </td>
                                                <td style="width: auto;">
                                                    <label for="" id="lblquotedManufacturer"></label>
                                                </td>
                                            </tr>
                                        </table>
                                    </Field>

                                </ReboundUI_Form:FormField>


                                <ReboundUI_Form:FormField ID="ctlSupplier" runat="server" FieldID="cmbSupplier" ResourceTitle="Supplier" IsRequiredField="true">
                                    <Field>
                                        <table>
                                            <tr>
                                                <td style="width: 200px;">
                                                    <ReboundUI:Combo ID="cmbSupplier" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="AllCompanies" />
                                                </td>
                                                <td style="width: auto;">
                                                    <label for="" id="lblSupplier"></label>
                                                </td>
                                            </tr>
                                        </table>
                                    </Field>

                                </ReboundUI_Form:FormField>
                                <tr>
                                    <td colspan="2" class="alignCenter">
                                        <input type="button" id="btnSubmitright" value="Save" class="stkButton" />&nbsp
                                        <input type="button" id="btnCancelright" value="Cancel" class="stkButton" />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>

                </asp:TableCell>
            </asp:TableRow>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>
