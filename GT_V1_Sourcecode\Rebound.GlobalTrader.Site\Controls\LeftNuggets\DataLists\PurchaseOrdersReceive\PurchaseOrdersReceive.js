Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrdersReceive=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrdersReceive.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrdersReceive.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseComplete));this._strPathToData="controls/DataListNuggets/PurchaseOrdersReceive";this._strDataObject="PurchaseOrdersReceive";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrdersReceive.callBaseMethod(this,"initialize")},initAfterBaseComplete:function(){this.getFilterFieldByFilterName("ViewLevel")._ddl.addChanged(Function.createDelegate(this,this.updateFilterVisibility));this.updateFilterVisibility()},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrdersReceive.callBaseMethod(this,"dispose")},setupDataCall:function(){var n="GetData";this.getFilterValue("ViewLevel")==1&&(n+="_All");this._objData.set_DataAction(n)},getDataOK:function(){for(var n,t,i=0,r=this._objResult.Results.length;i<r;i++)n=this._objResult.Results[i],t=String.format('<a href="{0}"><b>{1}<\/b> - {2}',$RGT_gotoURL_ReceivePurchaseOrder(n.ID),n.No,$R_FN.setCleanTextValue(n.CM)),n.Part.length>0&&(t+=String.format("<br />{0} x {1}<\/a>",n.Quantity,$R_FN.writePartNo(n.Part,n.ROHS))),t+="<\/a>",this._tbl.addRow([t],n.ID,!1),t=null,n=null},updateFilterVisibility:function(){this.completelyHideFilterItem("RecentOnly",this.getFilterValue("ViewLevel")==0);this.completelyHideFilterItem("IncludeClosed",this.getFilterValue("ViewLevel")==0)}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrdersReceive.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrdersReceive",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);