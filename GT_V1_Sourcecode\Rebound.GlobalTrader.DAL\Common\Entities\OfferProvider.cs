﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;
//[001]      Soorya Vyas     11/04/2023   RP-223 - Reverse Logistics Dubai Setup
namespace Rebound.GlobalTrader.DAL {
	
	public abstract class OfferProvider : DataAccess {
		static private OfferProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public OfferProvider Instance {
			get {
				if (_instance == null) _instance = (OfferProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.Offers.ProviderType));
				return _instance;
			}
		}
		public OfferProvider() {
			this.ConnectionString = Globals.Settings.Offers.ConnectionString;
            this.GTConnectionString = Globals.Settings.Offers.GTConnectionString;
		}

		#region Method Registrations
		
		/// <summary>
		/// Delete
		/// Calls [usp_delete_Offer]
		/// </summary>
		public abstract bool Delete(System.Int32? offerId);
		/// <summary>
		/// 
		/// Calls [usp_Import_Offer]
		/// </summary>
		/// <summary>
		/// Insert
        /// Calls [usp_insert_OfferNew]
		/// </summary>
        public abstract Int32 Insert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub);

        /// <summary>
        /// 
        /// Calls [usp_Import_Offer]
        /// </summary>
        /// <summary>
        /// Insert
        /// Calls [usp_insert_OfferNew]
        /// </summary>
        public abstract Int32 IPOBOMInsert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo);
        /// <summary>
        /// usp_offer_clone_AddToRequirement
        /// </summary>
        /// <returns></returns>
        /// 
        public abstract Int32 BOMManagerAPIOfferInsert(int Supplier,
                                                       string SupplierName,
                                                       string PartNo,
                                                       int ROHS,
                                                       System.Int32? ManufacturerNo,
                                                       string ManufacturerName,
                                                       System.String DateCode,
                                                       System.Int32? ProductNo,
                                                       string ProductName,
                                                       System.Int32? PackageNo,
                                                       string PackageName,
                                                       System.Int32? Quantity,
                                                       System.Double? Price,
                                                       System.Int32? Currency,
                                                       int OfferStatus,
                                                       System.String SupplierTotalQSA,
                                                       System.String SupplierMOQ,
                                                       System.String SupplierLTB,
                                                       int MSLNo,
                                                       System.String SPQ,
                                                       System.String LeadTime,
                                                       System.String FactorySealed,
                                                       System.String ROHSStatus,
                                                       string Notes,
                                                       int BOMManagerID,
                                                       int ClientID,
                                                       int alterCRNumber,
                                                       int? supplierWarranty,
                                                       int? countryOfOriginNo,
                                                       double? sellPrice,
                                                       double? shippingCost,
                                                       string reason,
                                                       int regionNo,
                                                       DateTime? deliveryDate,
                                                       bool? isTestingRecommended);
        public abstract Int32 CloneOfferAddToReq(System.Int32 offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo, out System.String strLinkMessage);
        public abstract Int32 CloneAltPartInfoAddToReq(System.Int32 AltpartInfoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo, out System.String strLinkMessage);

        //codee add for CrossMatch Clone
        /// <summary>
        /// Insert
        /// Calls [usp_offer_clone_CrossMatchRequirement]
        /// </summary>
        public abstract Int32 CloneOfferCrossMatchReq(System.Int32 offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.DateTime? originalEntryDate, System.Int32? salesman, System.Int32? supplierNo, System.String supplierName, System.Byte? rohs, System.Int32? offerStatusNo, System.String notes, System.Int32? updatedBy, System.Int32? clientNo, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String msl, System.String spq, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? customerRequirementNo, System.Int32? mslLevelNo, out System.String strLinkMessage);
        //end

        public abstract DataTable GetPartMatchRequirementDetails(System.Int32? clientId, System.Int32? OfferId);
       // public abstract void AddSourcingResultsforMatchedRequirements(System.Int32? OfferId, System.Int32? ClientId, DataTable dtRequirement);




        /// <summary>
        /// Get
        /// Calls [usp_select_Offer]
        /// </summary>
        public abstract OfferDetails Get(System.Int32? offerId,bool? isPoHub);


        /// <summary>
        /// Get
        /// Calls [usp_select_AltPart]
        /// </summary>
        public abstract OfferDetails GetAltPart(System.Int32? AlternativePartId, bool? isPoHub);

        /// <summary>
        /// GetEpo
        /// Calls [usp_select_EditEpo]
        /// </summary>
        public abstract OfferDetails GetEpo(System.Int32? EpoId, bool? isPoHub);

        /// <summary>
        /// Get StockHUBRFQ
        /// Calls [usp_select_StockHUBRFQ]
        /// </summary>
        public abstract OfferDetails GetStockHUBRFQ(System.Int32? StockId, bool? isPoHub);


        /// <summary>
        /// [001]
        /// GetReverseLogistic
        /// Calls [usp_select_EditReverseLogistic]
        /// </summary>
        public abstract OfferDetails GetReverseLogistic(System.Int32? EpoId, bool? isPoHub);

        /// <summary>
        /// Get CrossMatch Auto Search Details
        /// Calls [usp_CrossMatch_SearchLog_select]
        /// </summary>
        public abstract OfferDetails GetCrossMatchAutoSearch(System.Int32 BomId, System.Int32 userId, int ClientId);

        /// <summary>
        /// Source
        /// Calls [usp_source_Offer]
        /// </summary>
        public abstract List<OfferDetails> Source(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection);
        /// <summary>
        /// Source
        /// Calls [usp_source_OfferPH]
        /// </summary>
        public abstract List<OfferDetails> SourceArchive(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection);
        /// <summary>
        /// BOM Manager
        /// Calls [usp_OfferAPIBOMManager]
        /// </summary>
        public abstract List<APIExternalLinksDetails> OfferAPIBOMManager(int BOMManagerID, out DataTable Parts, string Part, int? CustomerReqID, int curPage, int Rpp);
        /// <summary>
        /// Source
        /// Calls [usp_GetLyticaAPIData]
        /// </summary>
        public abstract List<LyticaAPI> GetLyticaAPIData(int BOMManagerID, int? CustomerReqID, string Parts, out DataTable dtPart, int curPage, int Rpp);

        public abstract int LyticaApiLog(int BOMManagerID, int? Client);
        /// <summary>
        /// Source
        /// Calls [usp_GetLyticaAPIAlternateData]
        /// </summary>
        public abstract DataSet GetLyticaAPIAlternateData(string Parts, int curPage, int Rpp);
        //Epo
        /// <summary>
        /// Source Epo
        /// Calls [usp_ipobom_source_Epo]
        /// </summary>
        public abstract List<OfferDetails> SourceEpo(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, System.Boolean? IsPOHub);
        //Epo

        //ReverseLogistics
        /// <summary>
        ///[001]
        /// Source ReverseLogistics
        /// Calls [usp_ipobom_source_ReverseLogistics]
        /// </summary>
        public abstract List<OfferDetails> SourceReverseLogistics(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, System.Boolean? IsPOHub);

        /// <summary>
        ///[001]
        /// Source ReverseLogistics
        /// Calls [usp_source_GetReverseLogisticBulkEditHistory]
        /// </summary>
        public abstract List<OfferDetails> SourceReverseLogisticBulkEditHistory(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, System.Boolean? IsPOHub);


        //Ipo
        /// <summary>
        /// Source Epo
        /// Calls [usp_ipobom_source_Epo]
        /// </summary>
        public abstract List<OfferDetails> SourceIpo(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, System.Boolean? IsPOHub);
        //Ipo

        /// <summary>
        /// Source
        /// Calls [usp_source_Offer_PQ_Trusted]
        /// </summary>
        public abstract List<OfferDetails> SourceOfferAll(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal);

        /// <summary>
        /// Source
        /// Calls [[usp_ipobom_source_Offer]]
        /// </summary>
        public abstract List<OfferDetails> IPOBOMSource(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal,System.Boolean? isPohub);
		
        /// <summary>
		/// Update
		/// Calls [usp_update_Offer]
		/// </summary>
        public abstract bool Update(System.Int32? offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub);


        /// <summary>
        /// Update
        /// Calls [usp_update_AltPart]
        /// </summary>
        public abstract bool UpdateAltPart(System.Int32? AlternativePartId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub);


        /// <summary>
        /// Update Epo
        /// Calls [usp_update_Epo]
        /// </summary>
        public abstract bool UpdateEpo(System.Int32? EpoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? EpoStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub);

        /// <summary>
        /// Update
        /// Calls [usp_ipobom_update_Offer]
        /// </summary>
        public abstract bool IPOBOMUpdate(System.Int32? offerId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo);


        /// <summary>
        /// Update
        /// Calls [usp_update_AltPart]
        /// </summary>
        public abstract bool UpdateAltPartInfo(System.Int32? AltPartId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? offerStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo);

        /// <summary>
        /// Update Epo
        /// Calls [usp_ipobom_update_Epo]
        /// </summary>
        public abstract bool IPOBOMUpdateEpo(System.Int32? EpoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? EpoStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo, System.String Description, System.Double? VirtualCostPrice);

        /// <summary>
        /// Update Stock info list
        /// Calls [usp_HUBRFQ_update_Stock]
        /// </summary>
       // public abstract bool UpdateStockHUBRFQ(System.Int32? StockId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? ClientUpliftPrice, System.Int32? salesman, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub);
        public abstract bool UpdateStockHUBRFQ(System.Int32? StockId, System.Double? price, System.Double? ClientUpliftPrice,  System.String notes, System.Int32? updatedBy, bool? isPoHub,System.Int32? supplierNo);



        /// <summary>
        /// Update ReverseLogistic //[001]
        /// Calls [usp_ipobom_update_ReverseLogistic]
        /// </summary>
        public abstract bool IPOBOMUpdateReverseLogistic(System.Int32? EpoId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? productNo, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Int32? currencyNo, System.Int32? salesman, System.Int32? EpoStatusNo, System.Int32? supplierNo, System.Byte? rohs, System.String notes, System.Int32? updatedBy, bool? isPoHub, System.String supplierTotalQSA, System.String supplierMOQ, System.String supplierLTB, System.String MSL, System.String SPQ, System.String leadTime, System.String factorySealed, System.String rohsStatus, System.Int32? mslLevelNo, System.String Description);

        /// <summary>
        /// UpdateForSourcing
        /// Calls [usp_update_Offer_for_sourcing]
        /// </summary>
        public abstract bool UpdateForSourcing(System.Int32? offerId, System.Int32? quantity, System.Double? price, System.String notes, System.Int32? updatedBy);
        /// <summary>
        /// UpdateOfferStatus
        /// Calls [usp_update_Offer_OfferStatus]
        /// </summary>
        public abstract bool UpdateOfferStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy);

        /// <summary>
		/// UpdateOfferStatus
		/// Calls [usp_update_AltPartStatus]
		/// </summary>
		public abstract bool UpdateAltPartsStatus(System.Int32? AlternativePartNo, System.Int32? offerStatusNo, System.Int32? updatedBy);


        /// <summary>
		/// UpdateOfferStatus
		/// Calls [usp_update_Strategic_StrategicStatus]
		/// </summary>
		public abstract bool UpdateEPOStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy);

        /// <summary>
		/// UpdateOfferStatus
		/// Calls [usp_update_ReverseLT_ReverseLTStatus]
		/// </summary>
		public abstract bool UpdateReverseLogisticsStatus(System.Int32? offerNo, System.Int32? offerStatusNo, System.Int32? updatedBy);


        /// <summary>
        /// Update ReverseLogistic Bulk record//[001]
        /// Calls [usp_ipobom_update_ReverseLogisticBulk]
        /// </summary>
        public abstract bool IPOBOMUpdateReverseLogisticBulk(System.String ReverseLogisticIds, bool? isBulk, System.Int32? updatedBy/*,bool?isPoHub*/);

        #endregion

        /// <summary>
        /// Returns a new OfferDetails instance filled with the DataReader's current record data
        /// </summary>        
        protected virtual OfferDetails GetOfferFromReader(DbDataReader reader) {
			OfferDetails offer = new OfferDetails();
			if (reader.HasRows) {
				offer.OfferId = GetReaderValue_Int32(reader, "OfferId", 0); //From: [Table]
				offer.FullPart = GetReaderValue_String(reader, "FullPart", ""); //From: [Table]
				offer.Part = GetReaderValue_String(reader, "Part", ""); //From: [Table]
				offer.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null); //From: [Table]
				offer.DateCode = GetReaderValue_String(reader, "DateCode", ""); //From: [Table]
				offer.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null); //From: [Table]
				offer.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null); //From: [Table]
				offer.Quantity = GetReaderValue_Int32(reader, "Quantity", 0); //From: [Table]
				offer.Price = GetReaderValue_Double(reader, "Price", 0); //From: [Table]
				offer.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null); //From: [Table]
				offer.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null); //From: [Table]
				offer.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0); //From: [Table]
				offer.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null); //From: [Table]
				offer.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0); //From: [Table]
				offer.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null); //From: [Table]
				offer.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue); //From: [Table]
				offer.OfferStatusNo = GetReaderValue_NullableInt32(reader, "OfferStatusNo", null); //From: [Table]
				offer.OfferStatusChangeDate = GetReaderValue_NullableDateTime(reader, "OfferStatusChangeDate", null); //From: [Table]
				offer.OfferStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "OfferStatusChangeLoginNo", null); //From: [Table]
				offer.SupplierName = GetReaderValue_String(reader, "SupplierName", ""); //From: [Table]
				offer.Notes = GetReaderValue_String(reader, "Notes", ""); //From: [Table]
				offer.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", ""); //From: [Table]
				offer.ProductName = GetReaderValue_String(reader, "ProductName", ""); //From: [Table]
				offer.PackageName = GetReaderValue_String(reader, "PackageName", ""); //From: [Table]
				offer.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null); //From: [Table]
				offer.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", ""); //From: [usp_source_Offer]
				offer.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", ""); //From: [usp_source_Offer]
				offer.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", ""); //From: [usp_source_Offer]
				offer.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", ""); //From: [usp_source_Offer]
				offer.SalesmanName = GetReaderValue_String(reader, "SalesmanName", ""); //From: [usp_source_Offer]
				offer.OfferStatusChangeEmployeeName = GetReaderValue_String(reader, "OfferStatusChangeEmployeeName", ""); //From: [usp_source_Offer]
				offer.ClientId = GetReaderValue_Int32(reader, "ClientId", 0); //From: [usp_source_Offer]
				offer.ClientName = GetReaderValue_String(reader, "ClientName", ""); //From: [usp_source_Offer]
				offer.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null); //From: [usp_source_Offer]
                offer.LogDetails = GetReaderValue_String(reader, "LogDetails", ""); //From: [usp_CrossMatch_SearchLog_select]
			}
			return offer;
		}

        /// <summary>
        /// Source
        /// Calls [[usp_CrossMatch_Offer]]
        /// </summary>
        public abstract List<OfferDetails> CrossMatch(System.Int32? clientId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.String sortDir, System.String partSearch, System.String PartMatch, System.String months, System.Int32? monthTime, System.Int32? vendorNo, System.Int32? currencyNo, System.Boolean? isManufaurer, System.Int32? NoOfTopRecord, bool IsServerLocal, System.Boolean? isPohub, System.Int32? BomID, System.Boolean? IncludeAltPart, System.Int32? ReqId);

        /// <summary>
        /// Returns a collection of OfferDetails objects with the data read from the input DataReader
        /// </summary>                
        protected virtual List<OfferDetails> GetOfferCollectionFromReader(DbDataReader reader) {
			List<OfferDetails> offers = new List<OfferDetails>();
			while (reader.Read()) offers.Add(GetOfferFromReader(reader));
			return offers;
		}


        /// <summary>
        /// Source
        /// Calls [usp_source_AltParts]
        /// </summary>
        public abstract List<OfferDetails> SourceAltParts(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection);

        /// <summary>
        /// Source
        /// Calls [usp_source_AltPartsPH]
        /// </summary>
        public abstract List<OfferDetails> SourceAltPartsArchive(System.Int32? clientId, System.String partSearch, System.Int32? index, DateTime? startDate, DateTime? endDate, out DateTime? outDate, bool IsServerLocal, System.Boolean? IsPOHub, System.Int32? sortIndex, System.Int32? sortDirection);

        public abstract List<OfferDetails> POHubAutoSourcing(int customerRequirementId, int sortIndex, int sortDir, int tableLength, int clientNo, int loginNo, out string ihsResult, out string lyticaResult);
        public abstract bool POHubBulkUpdateEpo(string epoIds, string action, int? loginNo);
    }
}
