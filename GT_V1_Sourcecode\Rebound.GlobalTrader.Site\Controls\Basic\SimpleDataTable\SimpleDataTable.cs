//-----------------------------------------------------------------------------------------
// RP 21.12.2009:
// - new control
//-----------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Threading;
using AjaxControlToolkit;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
    [ToolboxData("<{0}:SimpleDataTable runat=server></{0}:SimpleDataTable>")]
	public class SimpleDataTable : Panel, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected Table _tbl;

		#endregion

		#region Properties

		/// <summary>
		/// Columns
		/// </summary>
		private List<SimpleDataColumn> _lstColumns = new List<SimpleDataColumn>();
		public List<SimpleDataColumn> Columns {
			get { return _lstColumns; }
			set { _lstColumns = value; }
		}

		/// <summary>
		/// Is this table inside a Data List Nugget
		/// </summary>
		private bool _blnInsideDataListNugget = false;
		public bool InsideDataListNugget {
			get { return _blnInsideDataListNugget; }
			set { _blnInsideDataListNugget = value; }
		}

		/// <summary>
		/// Should header be shown
		/// </summary>
		private bool _blnShowHeader = true;
		public bool ShowHeader {
			get { return _blnShowHeader; }
			set { _blnShowHeader = value; }
		}

		/// <summary>
		/// Is the table visible on first load of the screen
		/// </summary>
		private bool _blnIsInitiallyVisible = true;
		public bool IsInitiallyVisible {
			get { return _blnIsInitiallyVisible; }
			set { _blnIsInitiallyVisible = value; }
		}

		#endregion

		#region Constructors

		public SimpleDataTable() { }

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("FlexiDataTable.css");
			this.EnableViewState = false;
			base.OnInit(e);
		}

		protected override void CreateChildControls() {
			CssClass = "dataTableOuter";

            //table
            _tbl = ControlBuilders.CreateTable("dataTable");
            _tbl.ID = "tbl";
            _tbl.Width = Unit.Percentage(100);
            Controls.Add(_tbl);

			//col headings
			TableHeaderRow trHeading = new TableHeaderRow();
			trHeading.VerticalAlign = VerticalAlign.Top;
			for (int i = 0; i < _lstColumns.Count; i++) {
				//set id and css class
				_lstColumns[i].ID = string.Format("th_{0}", i);
				_lstColumns[i].CssClass = (i == _lstColumns.Count - 1) ? "last" : "";
				trHeading.Cells.Add(_lstColumns[i]);
			}
			_tbl.Rows.Add(trHeading);
			trHeading.Dispose(); trHeading = null;

			base.CreateChildControls();
		}


		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			CssClass = (_blnIsInitiallyVisible) ? "dataTableOuter" : "dataTableOuter invisible";

			//add script reference
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.SimpleDataTable.SimpleDataTable", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
            ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.SimpleDataTable", this.ClientID);
			descriptor.AddElementProperty("tbl", _tbl.ClientID);
			descriptor.AddProperty("blnInsideDataListNugget", _blnInsideDataListNugget);
			descriptor.AddProperty("blnShowHeader", _blnShowHeader);

			//columns
			List<int> lstColumnAlignment = new List<int>();
			List<string> lstColumnWidth = new List<string>();
			for (int i = 0; i < _lstColumns.Count; i++) {
				lstColumnAlignment.Add((int)_lstColumns[i].HorizontalAlign);
				lstColumnWidth.Add(_lstColumns[i].Width.ToString());
			}
			descriptor.AddProperty("aryColumnAlignment", lstColumnAlignment);
			descriptor.AddProperty("aryColumnWidth", lstColumnWidth);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}