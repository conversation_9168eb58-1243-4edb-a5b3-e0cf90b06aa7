-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_ExportToFolder_UAT1', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_ExportToFolder_UAT1;
END

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_ExportToFolder_UAT1]                                                                                                         
/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[Bug 207888]		Phuc Hoang			11-Jul-2024		Create			[PROD Bug] Dubai Invoice Export
===========================================================================================
*/


@ObjectName varchar(50) , 
@FileName varchar(150) 

AS

SET NOCOUNT ON

declare @SQL		varchar(2000)
	,	@FileDate	varchar(7)

set @FileDate =  cast(year (getdate()) as varchar(4)) + cast(datepart(week, getdate()) as varchar(2))+ cast( datepart(weekday, getdate()) as varchar(1))

select @SQL = 'bcp BorisGlobalTraderLive.dbo.' + @ObjectName + ' out ' + @FileName + @FileDate +'.csv' + ' -c -t, -T' 

exec master..xp_cmdshell @SQL



GO