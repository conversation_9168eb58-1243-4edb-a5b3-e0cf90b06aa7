///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmPartwatch = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmPartwatch.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intBOMID = -1;
    this._ReqIds = "";
    this._intBOMNo = -1;
    this._RequestToPOHubBy = -1;
    this._UpdateByPH = -1;
    this._BomName = null;
    this._BomCompanyNo = -1;
    this._intContact2No = -1;
    this._reqSalespeson = "";
   // this._ReqIds = [];
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmPartwatch.prototype = {

    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function() { return this._BomCompanyNo; }, set_BomCompanyNo: function(value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
    get_SalesManNo: function() { return this._SalesManNo; }, set_SalesManNo: function(value) { if (this._SalesManNo !== value) this._SalesManNo = value; },
    get_SalesManName: function() { return this._SalesManName; }, set_SalesManName: function(value) { if (this._SalesManName !== value) this._SalesManName = value; },
    get_tblAllReleasedetails: function () { return this._tblAllReleasedetails; }, set_tblAllReleasedetails: function (v) { if (this._tblAllReleasedetails !== v) this._tblAllReleasedetails = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmPartwatch.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intRequirementLineID = null;
        this._ReqSalesman = null;
        this._SupportTeamMemberNo = null;
        this._tblAllReleasedetails = null;
        this._UnitBuyPrice = null;
        this._UnitSellPrice = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmPartwatch.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        $("#partwatchLoad").hide();
        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_BOMItems_ConfirmPartwatch_ctlDB_ctlConfirm_ctl03_ctlConfirmation_ibtnYes_hyp").prop('disabled', false).css('opacity', 5.5);
        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_BOMItems_ConfirmPartwatch_ctlDB_ctlConfirm_ctl03_ctlConfirmation_ibtnYes_hyp").removeClass("disable-click");
        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_BOMItems_ConfirmPartwatch_ctlDB_ctlConfirm_ctl03_ctlConfirmation_ibtnNo_hyp").prop('disabled', false).css('opacity', 5.5);
        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_BOMItems_ConfirmPartwatch_ctlDB_ctlConfirm_ctl03_ctlConfirmation_ibtnNo_hyp").removeClass("disable-click");

        
        if (this._blnFirstTimeShown) {
         
          
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
        else {
            //this._tblAllReleasedetails.clearTable();
            //this.getSerialDetail(this._intRequirementLineID)
            //this._tblAllReleasedetails.resizeColumns();
        }
    },
    


    //code end
    yesClicked: function () {

        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_BOMItems_ConfirmPartwatch_ctlDB_ctlConfirm_ctl03_ctlConfirmation_ibtnYes_hyp").prop('disabled', true).css('opacity', 0.5);
        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_BOMItems_ConfirmPartwatch_ctlDB_ctlConfirm_ctl03_ctlConfirmation_ibtnYes_hyp").addClass("disable-click");
        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_BOMItems_ConfirmPartwatch_ctlDB_ctlConfirm_ctl03_ctlConfirmation_ibtnNo_hyp").prop('disabled', true).css('opacity', 0.5);
        $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_BOMItems_ConfirmPartwatch_ctlDB_ctlConfirm_ctl03_ctlConfirmation_ibtnNo_hyp").addClass("disable-click");
        $("#partwatchLoad").show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("PartWatchHUBIPOMatch");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("AddNotes", "test");
        obj.addParameter("ReqIds", $R_FN.arrayToSingleString(this._ReqIds, ","));
        obj.addParameter("UpdateByPH", this._UpdateByPH);
        obj.addParameter("RequestToPOHubBy", this._RequestToPOHubBy);
        obj.addParameter("HUBRFQName", this._BomName);
        obj.addParameter("HUBRFQCompanyNo", this._BomCompanyNo);
        obj.addParameter("Contact2No", this._intContact2No);
        obj.addParameter("ReqsalesPerson", this._reqSalespeson);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },


    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
        //Array.clear(this._ReqIds);
    },

    saveComplete: function (args) {
        this.showSaving(false);
        if (args._result.Result) {
            this.onSaveComplete();
            $("#partwatchLoad").hide();
           
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
            $("#partwatchLoad").hide();
        }
    }




};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmPartwatch.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_ConfirmPartwatch", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
