﻿GO

IF OBJECT_ID('dbo.tbDigiKeyAPIPartAttributes', 'U') IS NOT NULL 
  DROP TABLE dbo.tbDigiKeyAPIPartAttributes; 
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-204486]			Phuc Hoang			26-Jul-2024		Create			Sourcing - the Digikey API to be added to the supplier data feeds section
===========================================================================================
*/

CREATE TABLE [dbo].[tbDigiKeyAPIPartAttributes](
	[PartAttributesId] [int] IDENTITY(1,1) NOT NULL,
	[SupplierAPINo] [int] NULL,
	[DigiKeyProductNumber] [nvarchar](256) NULL,
	[PackageType] [nvarchar](256) NULL,
	[ManufacturerName] [nvarchar](256) NULL,
	[Rohs] [nvarchar](256) NULL,
	[ProductName] [nvarchar](MAX) NULL,
	[ProductDescription] [nvarchar](MAX) NULL,
	[ECCN] [nvarchar](256) NULL,
	[MSL] [nvarchar](256) NULL,
	[DateCode] [nvarchar](256) NULL,
	[LeadFree] [nvarchar](256) NULL,
	[UnitPrice] [decimal](15, 5) NULL,
	[UpdatedBy] [int] NULL,
	[DLUP] [datetime] NULL,
PRIMARY KEY CLUSTERED 

(
	[PartAttributesId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[tbDigiKeyAPIPartAttributes] ADD  DEFAULT (getdate()) FOR [DLUP]
GO


