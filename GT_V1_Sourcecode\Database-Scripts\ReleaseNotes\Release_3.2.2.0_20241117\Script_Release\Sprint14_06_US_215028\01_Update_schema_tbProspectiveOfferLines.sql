﻿/*
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-215028]		Trung Pham			29-Oct-2024		CREATE			UPDATE SCHEMA
[US-215028]		Trung Pham			12-Nov-2024		UPDATE			UPDATE SCHEMA
===========================================================================================
*/ 
IF (NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbProspectiveOfferLines'
	AND COLUMN_NAME IN ('Notes')))
	BEGIN
		ALTER TABLE tbProspectiveOfferLines
		ADD Notes NVARCHAR(255)
	END
IF (NOT EXISTS (SELECT 1 FROM [BorisGlobalTraderimports].INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbProspectiveOffer_ToBeImported'
	AND COLUMN_NAME IN ('Notes')))
	BEGIN
		ALTER TABLE [BorisGlobalTraderimports].dbo.tbProspectiveOffer_ToBeImported
		ADD Notes NVARCHAR(255)
	END

IF NOT EXISTS (
    SELECT * 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbProspectiveOfferLines' 
    AND COLUMN_NAME = 'FullPart'
)
BEGIN
    ALTER TABLE tbProspectiveOfferLines
    ADD FullPart NVARCHAR(MAX); 
END