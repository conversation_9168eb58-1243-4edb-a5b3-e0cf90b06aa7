///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - complete dispose event
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.ConfirmationExport = function(element) { 
    Rebound.GlobalTrader.Site.Controls.ConfirmationExport.initializeBase(this, [element]);
	this._dummy = null;
};

Rebound.GlobalTrader.Site.Controls.ConfirmationExport.prototype = {

	get_ibtnYes: function() { return this._ibtnYes; }, 	set_ibtnYes: function(value) { if (this._ibtnYes !== value)  this._ibtnYes = value; }, 
    get_ibtnNo: function () { return this._ibtnNo; }, set_ibtnNo: function (value) { if (this._ibtnNo !== value) this._ibtnNo = value; }, 
    get_ibtnCancel: function () { return this._ibtnCancel; }, set_ibtnCancel: function (value) { if (this._ibtnCancel !== value) this._ibtnCancel = value; }, 
	get_dummy: function() { return this._dummy; }, 	set_dummy: function(value) { if (this._dummy !== value)  this._dummy = value; }, 

	addClickYesEvent: function(handler) { this.get_events().addHandler("ClickYes", handler); },
	removeClickYesEvent: function(handler) { this.get_events().removeHandler("ClickYes", handler); },
	onClickYes: function() { 
		var handler = this.get_events().getHandler("ClickYes");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	addClickNoEvent: function(handler) { this.get_events().addHandler("ClickNo", handler); },
	removeClickNoEvent: function(handler) { this.get_events().removeHandler("ClickNo", handler); },
	onClickNo: function() { 
		var handler = this.get_events().getHandler("ClickNo");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
    addClickRejectEvent: function (handler) { this.get_events().addHandler("ClickReject", handler); },
    removeClickRejectEvent: function (handler) { this.get_events().removeHandler("ClickReject", handler); },
    onClickReject: function () {
        var handler = this.get_events().getHandler("ClickReject");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
	initialize: function() {
        Rebound.GlobalTrader.Site.Controls.ConfirmationExport.callBaseMethod(this, "initialize");
		$R_IBTN.addClick(this._ibtnYes, Function.createDelegate(this, this.onClickYes));
        $R_IBTN.addClick(this._ibtnNo, Function.createDelegate(this, this.onClickReject));
        $R_IBTN.addClick(this._ibtnCancel, Function.createDelegate(this, this.onClickNo));

	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._ibtnYes) $R_IBTN.clearHandlers(this._ibtnYes);
        if (this._ibtnNo) $R_IBTN.clearHandlers(this._ibtnNo);
        if (this._ibtnCancel) $R_IBTN.clearHandlers(this._ibtnCancel);
		this._ibtnYes = null;
        this._ibtnNo = null;
        this._ibtnCancel = null;
        Rebound.GlobalTrader.Site.Controls.ConfirmationExport.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	}
	
};

Rebound.GlobalTrader.Site.Controls.ConfirmationExport.registerClass("Rebound.GlobalTrader.Site.Controls.ConfirmationExport", Sys.UI.Control, Sys.IDisposable);