﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		09-Jan-2025		Create		Insert temp data when import prospective offers
[US-232568]		An.TranTan		17-Feb-2025		Update		Change insert HUBRFQ ID instead of Req ID
===========================================================================================  
*/   
CREATE OR ALTER Procedure [dbo].[usp_saveBOMSourcing_tempData]    
    @UploadedData UploadedBOMSourcingResult READONLY,
	@BOMNo INT,
    @originalFilename NVARCHAR(max),    
    @generatedFilename NVARCHAR(max),    
    @UserId INT = 1
AS    
BEGIN    
    SET NOCOUNT ON;
	--Clear previous temp data
	DELETE BorisGlobalTraderimports.dbo.tbBOMSourcing_tempData WHERE CreatedBy = @UserId;
	
    INSERT INTO BorisGlobalTraderimports.dbo.tbBOMSourcing_tempData    
    (    
        [BOMNo]
		,[Column1]
		,[Column2]
		,[Column3]
		,[Column4]
		,[Column5]
		,[Column6]
		,[Column7]
		,[Column8]
		,[Column9]
		,[Column10]
		,[Column11]
		,[Column12]
		,[Column13]
		,[Column14]
		,[Column15]
		,[Column16]
		,[Column17]
		,[Column18]
		,[Column19]
		,[Column20]
		,[Column21]
		,[Column22]
		,[Column23]
		,[Column24]
		,[Column25]
		,[Column26]
		,[Column27]
		,[Column28]
		,[Column29]
		,[Column30]
		,[Column31]
		,[LineNumber]
		,[OriginalFilename]
		,[GeneratedFilename]
		,[CreatedBy]
		,[CreatedDate]
    )    
    SELECT
		@BOMNo
		,[Column1]
		,[Column2]
		,[Column3]
		,[Column4]
		,[Column5]
		,[Column6]
		,[Column7]
		,[Column8]
		,[Column9]
		,[Column10]
		,[Column11]
		,[Column12]
		,[Column13]
		,[Column14]
		,[Column15]
		,[Column16]
		,[Column17]
		,[Column18]
		,[Column19]
		,[Column20]
		,[Column21]
		,[Column22]
		,[Column23]
		,[Column24]
		,[Column25]
		,[Column26]
		,[Column27]
		,[Column28]
		,[Column29]
		,[Column30]
		,[Column31]
		,[LineNumber]
        ,@originalFilename    
        ,@generatedFilename    
        ,@userId
		,GETDATE()
    FROM @UploadedData    
END  
GO


