//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class FormFields {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FormFields() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.FormFields", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Abbreviation.
        /// </summary>
        internal static string Abbreviation {
            get {
                return ResourceManager.GetString("Abbreviation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account.
        /// </summary>
        internal static string Account {
            get {
                return ResourceManager.GetString("Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string AccountNotes {
            get {
                return ResourceManager.GetString("AccountNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acetone Test.
        /// </summary>
        internal static string ActeoneTest {
            get {
                return ResourceManager.GetString("ActeoneTest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action.
        /// </summary>
        internal static string Action {
            get {
                return ResourceManager.GetString("Action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Added by.
        /// </summary>
        internal static string AddedBy {
            get {
                return ResourceManager.GetString("AddedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add More.
        /// </summary>
        internal static string AddMore {
            get {
                return ResourceManager.GetString("AddMore", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Notes.
        /// </summary>
        internal static string AddNotes {
            get {
                return ResourceManager.GetString("AddNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        internal static string Address {
            get {
                return ResourceManager.GetString("Address", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address Name.
        /// </summary>
        internal static string AddressName {
            get {
                return ResourceManager.GetString("AddressName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General info.
        /// </summary>
        internal static string AddressNotes {
            get {
                return ResourceManager.GetString("AddressNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes for packing slip.
        /// </summary>
        internal static string AddressShippingNotes {
            get {
                return ResourceManager.GetString("AddressShippingNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Email.
        /// </summary>
        internal static string ADEmail {
            get {
                return ResourceManager.GetString("ADEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Login Name.
        /// </summary>
        internal static string ADLoginName {
            get {
                return ResourceManager.GetString("ADLoginName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Administrators Security Group Name.
        /// </summary>
        internal static string AdminSecurityGroupName {
            get {
                return ResourceManager.GetString("AdminSecurityGroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Administrator User Name.
        /// </summary>
        internal static string AdminUserName {
            get {
                return ResourceManager.GetString("AdminUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AD Username.
        /// </summary>
        internal static string ADUserName {
            get {
                return ResourceManager.GetString("ADUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advice Note.
        /// </summary>
        internal static string AdviceNotes {
            get {
                return ResourceManager.GetString("AdviceNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advisory Notes.
        /// </summary>
        internal static string AdvisoryNotes {
            get {
                return ResourceManager.GetString("AdvisoryNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Aerospace Use.
        /// </summary>
        internal static string AerospaceUse {
            get {
                return ResourceManager.GetString("AerospaceUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agency.
        /// </summary>
        internal static string Agency {
            get {
                return ResourceManager.GetString("Agency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Agency SO Authoriser.
        /// </summary>
        internal static string AgencySOAuthoriser {
            get {
                return ResourceManager.GetString("AgencySOAuthoriser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Air Way Bill.
        /// </summary>
        internal static string AirWayBill {
            get {
                return ResourceManager.GetString("AirWayBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated.
        /// </summary>
        internal static string Allocated {
            get {
                return ResourceManager.GetString("Allocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Ready To Ship?.
        /// </summary>
        internal static string AllowReadyToShip {
            get {
                return ResourceManager.GetString("AllowReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow viewing Team, Division or Company reports.
        /// </summary>
        internal static string AllowViewTeamReport {
            get {
                return ResourceManager.GetString("AllowViewTeamReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Results.
        /// </summary>
        internal static string AllSourcingResults {
            get {
                return ResourceManager.GetString("AllSourcingResults", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternate Part No.
        /// </summary>
        internal static string AlternatePart {
            get {
                return ResourceManager.GetString("AlternatePart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add all suggested Rebound alternatives.
        /// </summary>
        internal static string Alternatives {
            get {
                return ResourceManager.GetString("Alternatives", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alternatives Accepted.
        /// </summary>
        internal static string AlternativesAccepted {
            get {
                return ResourceManager.GetString("AlternativesAccepted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount Owed.
        /// </summary>
        internal static string AmountOwed {
            get {
                return ResourceManager.GetString("AmountOwed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Anniversary.
        /// </summary>
        internal static string Anniversary {
            get {
                return ResourceManager.GetString("Anniversary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply Shipping Surcharge ?.
        /// </summary>
        internal static string AppliedShippingSurcharge {
            get {
                return ResourceManager.GetString("AppliedShippingSurcharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Surcharge:.
        /// </summary>
        internal static string AppliedSurcharge {
            get {
                return ResourceManager.GetString("AppliedSurcharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply.
        /// </summary>
        internal static string Apply {
            get {
                return ResourceManager.GetString("Apply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply Bank Fee.
        /// </summary>
        internal static string ApplyBankFee {
            get {
                return ResourceManager.GetString("ApplyBankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply Division Header.
        /// </summary>
        internal static string ApplyDivisionHeader {
            get {
                return ResourceManager.GetString("ApplyDivisionHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply Duty?.
        /// </summary>
        internal static string ApplyDuty {
            get {
                return ResourceManager.GetString("ApplyDuty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply On Client Invoice.
        /// </summary>
        internal static string ApplyOnCI {
            get {
                return ResourceManager.GetString("ApplyOnCI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply On PO.
        /// </summary>
        internal static string ApplyOnPo {
            get {
                return ResourceManager.GetString("ApplyOnPo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply PO Bank Fee.
        /// </summary>
        internal static string ApplyPOBankFee {
            get {
                return ResourceManager.GetString("ApplyPOBankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply To.
        /// </summary>
        internal static string ApplyTo {
            get {
                return ResourceManager.GetString("ApplyTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply To Catagory.
        /// </summary>
        internal static string ApplyToCatagory {
            get {
                return ResourceManager.GetString("ApplyToCatagory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Approval History.
        /// </summary>
        internal static string ApprovalHistory {
            get {
                return ResourceManager.GetString("ApprovalHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved By.
        /// </summary>
        internal static string ApprovedBy {
            get {
                return ResourceManager.GetString("ApprovedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Customer.
        /// </summary>
        internal static string ApprovedCustomer {
            get {
                return ResourceManager.GetString("ApprovedCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved for Export.
        /// </summary>
        internal static string ApprovedforExport {
            get {
                return ResourceManager.GetString("ApprovedforExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Supplier.
        /// </summary>
        internal static string ApprovedSupplier {
            get {
                return ResourceManager.GetString("ApprovedSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inhouse AS6081 testing required?.
        /// </summary>
        internal static string AS6081 {
            get {
                return ResourceManager.GetString("AS6081", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inhouse AS6081 testing required?.
        /// </summary>
        internal static string AS6081Filter {
            get {
                return ResourceManager.GetString("AS6081Filter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS6081 testing required?.
        /// </summary>
        internal static string AS6081Filternew {
            get {
                return ResourceManager.GetString("AS6081Filternew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS6081 Part Included?.
        /// </summary>
        internal static string AS6081Included {
            get {
                return ResourceManager.GetString("AS6081Included", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inhouse  AS6081  testing &lt;br&gt;required?*.
        /// </summary>
        internal static string AS6081Label {
            get {
                return ResourceManager.GetString("AS6081Label", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inhouse AS6081 Part Included?.
        /// </summary>
        internal static string AS6081MainInfoLabel {
            get {
                return ResourceManager.GetString("AS6081MainInfoLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS6081 testing required?.
        /// </summary>
        internal static string AS6081new {
            get {
                return ResourceManager.GetString("AS6081new", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason For Chosen Supplier.
        /// </summary>
        internal static string AS6081RCS {
            get {
                return ResourceManager.GetString("AS6081RCS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Risk Of Supplier.
        /// </summary>
        internal static string AS6081ROS {
            get {
                return ResourceManager.GetString("AS6081ROS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type Of Supplier.
        /// </summary>
        internal static string AS6081TOS {
            get {
                return ResourceManager.GetString("AS6081TOS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source of Supply Required.
        /// </summary>
        internal static string AS9120 {
            get {
                return ResourceManager.GetString("AS9120", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned User.
        /// </summary>
        internal static string AssignedUser {
            get {
                return ResourceManager.GetString("AssignedUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assigned Lead.
        /// </summary>
        internal static string AssignedUserLead {
            get {
                return ResourceManager.GetString("AssignedUserLead", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign To.
        /// </summary>
        internal static string AssignTo {
            get {
                return ResourceManager.GetString("AssignTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorisation History.
        /// </summary>
        internal static string AuthorisationHistory {
            get {
                return ResourceManager.GetString("AuthorisationHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorised By.
        /// </summary>
        internal static string AuthorisedBy {
            get {
                return ResourceManager.GetString("AuthorisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorised Date.
        /// </summary>
        internal static string AuthorisedDate {
            get {
                return ResourceManager.GetString("AuthorisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorised Note.
        /// </summary>
        internal static string AuthorisedNote {
            get {
                return ResourceManager.GetString("AuthorisedNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authoriser.
        /// </summary>
        internal static string Authoriser {
            get {
                return ResourceManager.GetString("Authoriser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatically approve PO?.
        /// </summary>
        internal static string AutoApprovePO {
            get {
                return ResourceManager.GetString("AutoApprovePO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatically approve SO?.
        /// </summary>
        internal static string AutoApproveSO {
            get {
                return ResourceManager.GetString("AutoApproveSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Automatically export supplier invoice.
        /// </summary>
        internal static string AutoExportSI {
            get {
                return ResourceManager.GetString("AutoExportSI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available.
        /// </summary>
        internal static string Available {
            get {
                return ResourceManager.GetString("Available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avg. Price.
        /// </summary>
        internal static string AveragePrice {
            get {
                return ResourceManager.GetString("AveragePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avg. Price (50th Percentile).
        /// </summary>
        internal static string AveragePriceLytica {
            get {
                return ResourceManager.GetString("AveragePriceLytica", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Background Image.
        /// </summary>
        internal static string BackgroundImage {
            get {
                return ResourceManager.GetString("BackgroundImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Baking Label added.
        /// </summary>
        internal static string BakingLevelAdded {
            get {
                return ResourceManager.GetString("BakingLevelAdded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance.
        /// </summary>
        internal static string Balance {
            get {
                return ResourceManager.GetString("Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance with this Order &amp;&amp; Posted Lines.
        /// </summary>
        internal static string BalanceWithBoth {
            get {
                return ResourceManager.GetString("BalanceWithBoth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance with all Posted Lines.
        /// </summary>
        internal static string BalanceWithOpenOrders {
            get {
                return ResourceManager.GetString("BalanceWithOpenOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance with this Order.
        /// </summary>
        internal static string BalanceWithThisOrder {
            get {
                return ResourceManager.GetString("BalanceWithThisOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bank Charge Fee.
        /// </summary>
        internal static string BankChargeFee {
            get {
                return ResourceManager.GetString("BankChargeFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bank Fee.
        /// </summary>
        internal static string BankFee {
            get {
                return ResourceManager.GetString("BankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batch Code.
        /// </summary>
        internal static string BatchCode {
            get {
                return ResourceManager.GetString("BatchCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batch Reference.
        /// </summary>
        internal static string BatchReference {
            get {
                return ResourceManager.GetString("BatchReference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Billing Address.
        /// </summary>
        internal static string BillingAddress {
            get {
                return ResourceManager.GetString("BillingAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Billing Address Name.
        /// </summary>
        internal static string BillingAddressName {
            get {
                return ResourceManager.GetString("BillingAddressName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bill to Address.
        /// </summary>
        internal static string BillToAddress {
            get {
                return ResourceManager.GetString("BillToAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Billing Address Name.
        /// </summary>
        internal static string BillToAddressName {
            get {
                return ResourceManager.GetString("BillToAddressName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bill To Country.
        /// </summary>
        internal static string BillToCountry {
            get {
                return ResourceManager.GetString("BillToCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Birthday.
        /// </summary>
        internal static string Birthday {
            get {
                return ResourceManager.GetString("Birthday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM.
        /// </summary>
        internal static string BOM {
            get {
                return ResourceManager.GetString("BOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM.
        /// </summary>
        internal static string BOMChk {
            get {
                return ResourceManager.GetString("BOMChk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Code.
        /// </summary>
        internal static string BOMCode {
            get {
                return ResourceManager.GetString("BOMCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign Group.
        /// </summary>
        internal static string BomMailGroups {
            get {
                return ResourceManager.GetString("BomMailGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager No-Bid.
        /// </summary>
        internal static string BOMManagerNoBid {
            get {
                return ResourceManager.GetString("BOMManagerNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Partial Release.
        /// </summary>
        internal static string BOMManagerPartialRelease {
            get {
                return ResourceManager.GetString("BOMManagerPartialRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Recall.
        /// </summary>
        internal static string BOMManagerRecall {
            get {
                return ResourceManager.GetString("BOMManagerRecall", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Recall No-Bid.
        /// </summary>
        internal static string BOMManagerRecallNoBid {
            get {
                return ResourceManager.GetString("BOMManagerRecallNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Release.
        /// </summary>
        internal static string BOMManagerRelease {
            get {
                return ResourceManager.GetString("BOMManagerRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Name.
        /// </summary>
        internal static string BOMName {
            get {
                return ResourceManager.GetString("BOMName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM.
        /// </summary>
        internal static string BomUse {
            get {
                return ResourceManager.GetString("BomUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boxes.
        /// </summary>
        internal static string Boxes {
            get {
                return ResourceManager.GetString("Boxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Budget.
        /// </summary>
        internal static string Budget {
            get {
                return ResourceManager.GetString("Budget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy.
        /// </summary>
        internal static string Buy {
            get {
                return ResourceManager.GetString("Buy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buyer.
        /// </summary>
        internal static string Buyer {
            get {
                return ResourceManager.GetString("Buyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buyer.
        /// </summary>
        internal static string BuyerName {
            get {
                return ResourceManager.GetString("BuyerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buying Price.
        /// </summary>
        internal static string BuyPrice {
            get {
                return ResourceManager.GetString("BuyPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Price.
        /// </summary>
        internal static string BuyPrice1 {
            get {
                return ResourceManager.GetString("BuyPrice1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Ship Via.
        /// </summary>
        internal static string BuyShipVia {
            get {
                return ResourceManager.GetString("BuyShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Ship Via Acct.
        /// </summary>
        internal static string BuyShipViaAccount {
            get {
                return ResourceManager.GetString("BuyShipViaAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Ship Via No.
        /// </summary>
        internal static string BuyShipViaNo {
            get {
                return ResourceManager.GetString("BuyShipViaNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved for Export.
        /// </summary>
        internal static string CanBeExported {
            get {
                return ResourceManager.GetString("CanBeExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Caption.
        /// </summary>
        internal static string Caption {
            get {
                return ResourceManager.GetString("Caption", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CC.
        /// </summary>
        internal static string CC {
            get {
                return ResourceManager.GetString("CC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cease Date.
        /// </summary>
        internal static string CeaseDate {
            get {
                return ResourceManager.GetString("CeaseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Category.
        /// </summary>
        internal static string CertificateCategory {
            get {
                return ResourceManager.GetString("CertificateCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Category Name.
        /// </summary>
        internal static string CertificateCategoryName {
            get {
                return ResourceManager.GetString("CertificateCategoryName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Name.
        /// </summary>
        internal static string CertificateName {
            get {
                return ResourceManager.GetString("CertificateName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Number.
        /// </summary>
        internal static string CertificateNumber {
            get {
                return ResourceManager.GetString("CertificateNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review Status.
        /// </summary>
        internal static string CertificationNotes {
            get {
                return ResourceManager.GetString("CertificationNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &lt;b&gt;Change Status&lt;/b&gt;.
        /// </summary>
        internal static string ChangeStatus {
            get {
                return ResourceManager.GetString("ChangeStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Charge.
        /// </summary>
        internal static string Charge {
            get {
                return ResourceManager.GetString("Charge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspected by.
        /// </summary>
        internal static string CheckedBy {
            get {
                return ResourceManager.GetString("CheckedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked ?.
        /// </summary>
        internal static string CheckedStatus {
            get {
                return ResourceManager.GetString("CheckedStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 1.
        /// </summary>
        internal static string Child1 {
            get {
                return ResourceManager.GetString("Child1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 1 Birthday.
        /// </summary>
        internal static string Child1Birthday {
            get {
                return ResourceManager.GetString("Child1Birthday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 1 Name.
        /// </summary>
        internal static string Child1Name {
            get {
                return ResourceManager.GetString("Child1Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 1 Gender.
        /// </summary>
        internal static string Child1Sex {
            get {
                return ResourceManager.GetString("Child1Sex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 2.
        /// </summary>
        internal static string Child2 {
            get {
                return ResourceManager.GetString("Child2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 2 Birthday.
        /// </summary>
        internal static string Child2Birthday {
            get {
                return ResourceManager.GetString("Child2Birthday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 2 Name.
        /// </summary>
        internal static string Child2Name {
            get {
                return ResourceManager.GetString("Child2Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 2 Gender.
        /// </summary>
        internal static string Child2Sex {
            get {
                return ResourceManager.GetString("Child2Sex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 3.
        /// </summary>
        internal static string Child3 {
            get {
                return ResourceManager.GetString("Child3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 3 Birthday.
        /// </summary>
        internal static string Child3Birthday {
            get {
                return ResourceManager.GetString("Child3Birthday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 3 Name.
        /// </summary>
        internal static string Child3Name {
            get {
                return ResourceManager.GetString("Child3Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Child 3 Gender.
        /// </summary>
        internal static string Child3Sex {
            get {
                return ResourceManager.GetString("Child3Sex", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sanctions.
        /// </summary>
        internal static string ChkSanctions {
            get {
                return ResourceManager.GetString("ChkSanctions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        internal static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Town/City.
        /// </summary>
        internal static string CityTown {
            get {
                return ResourceManager.GetString("CityTown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client.
        /// </summary>
        internal static string Client {
            get {
                return ResourceManager.GetString("Client", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Bill To.
        /// </summary>
        internal static string ClientBillTo {
            get {
                return ResourceManager.GetString("ClientBillTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clients Debit No.
        /// </summary>
        internal static string ClientDebitNo {
            get {
                return ResourceManager.GetString("ClientDebitNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice .
        /// </summary>
        internal static string ClientInvoice {
            get {
                return ResourceManager.GetString("ClientInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice Date From.
        /// </summary>
        internal static string ClientInvoiceDateFrom {
            get {
                return ResourceManager.GetString("ClientInvoiceDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice Date To.
        /// </summary>
        internal static string ClientInvoiceDateTo {
            get {
                return ResourceManager.GetString("ClientInvoiceDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice Header.
        /// </summary>
        internal static string ClientInvoiceHeader {
            get {
                return ResourceManager.GetString("ClientInvoiceHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice No.
        /// </summary>
        internal static string ClientInvoiceNo {
            get {
                return ResourceManager.GetString("ClientInvoiceNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Name.
        /// </summary>
        internal static string ClientName {
            get {
                return ResourceManager.GetString("ClientName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uplift Price.
        /// </summary>
        internal static string ClientUPLiftPrice {
            get {
                return ResourceManager.GetString("ClientUPLiftPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send this to Purchase Hub.
        /// </summary>
        internal static string CloneSendHub {
            get {
                return ResourceManager.GetString("CloneSendHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close all alternates?.
        /// </summary>
        internal static string CloseAllAlternates {
            get {
                return ResourceManager.GetString("CloseAllAlternates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed.
        /// </summary>
        internal static string Closed {
            get {
                return ResourceManager.GetString("Closed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Reason.
        /// </summary>
        internal static string CloseReason {
            get {
                return ResourceManager.GetString("CloseReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcodes scanned ticked?.
        /// </summary>
        internal static string Cl_BarCodeScan {
            get {
                return ResourceManager.GetString("Cl_BarCodeScan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part number filled/ticked.
        /// </summary>
        internal static string Cl_PartTicked {
            get {
                return ResourceManager.GetString("Cl_PartTicked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package breakdown completed.
        /// </summary>
        internal static string Cl_PBComplete {
            get {
                return ResourceManager.GetString("Cl_PBComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Photos attached?.
        /// </summary>
        internal static string Cl_PhotosAttached {
            get {
                return ResourceManager.GetString("Cl_PhotosAttached", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All open queries answered and approved?.
        /// </summary>
        internal static string Cl_QueryFormat {
            get {
                return ResourceManager.GetString("Cl_QueryFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include CofC.
        /// </summary>
        internal static string COC {
            get {
                return ResourceManager.GetString("COC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code.
        /// </summary>
        internal static string Code {
            get {
                return ResourceManager.GetString("Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code 1.
        /// </summary>
        internal static string Code1 {
            get {
                return ResourceManager.GetString("Code1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code 2.
        /// </summary>
        internal static string Code2 {
            get {
                return ResourceManager.GetString("Code2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code N.
        /// </summary>
        internal static string CodeN {
            get {
                return ResourceManager.GetString("CodeN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code Y.
        /// </summary>
        internal static string CodeY {
            get {
                return ResourceManager.GetString("CodeY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate of Conformity Notes.
        /// </summary>
        internal static string CofCNotes {
            get {
                return ResourceManager.GetString("CofCNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        internal static string Comment {
            get {
                return ResourceManager.GetString("Comment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commodities.
        /// </summary>
        internal static string Commodities {
            get {
                return ResourceManager.GetString("Commodities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commodity Code:.
        /// </summary>
        internal static string CommodityCode {
            get {
                return ResourceManager.GetString("CommodityCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Address.
        /// </summary>
        internal static string CompanyAddress {
            get {
                return ResourceManager.GetString("CompanyAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Code.
        /// </summary>
        internal static string CompanyCode {
            get {
                return ResourceManager.GetString("CompanyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accounts notes.
        /// </summary>
        internal static string CompanyImportantNotes {
            get {
                return ResourceManager.GetString("CompanyImportantNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string CompanyName {
            get {
                return ResourceManager.GetString("CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General customer info.
        /// </summary>
        internal static string CompanyNotes {
            get {
                return ResourceManager.GetString("CompanyNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchasing Notes.
        /// </summary>
        internal static string CompanyPurchasingNotes {
            get {
                return ResourceManager.GetString("CompanyPurchasingNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Reg. No.
        /// </summary>
        internal static string CompanyRegNo {
            get {
                return ResourceManager.GetString("CompanyRegNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Type.
        /// </summary>
        internal static string CompanyType {
            get {
                return ResourceManager.GetString("CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Competitor Best offer.
        /// </summary>
        internal static string CompetitorBestoffer {
            get {
                return ResourceManager.GetString("CompetitorBestoffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please confirm.
        /// </summary>
        internal static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed.
        /// </summary>
        internal static string Confirmed {
            get {
                return ResourceManager.GetString("Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Conflict Resource.
        /// </summary>
        internal static string ConflictResource {
            get {
                return ResourceManager.GetString("ConflictResource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consolidate.
        /// </summary>
        internal static string Consildate {
            get {
                return ResourceManager.GetString("Consildate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CC communication notes to.
        /// </summary>
        internal static string Contact2 {
            get {
                return ResourceManager.GetString("Contact2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Optional: Select an additional salesperson to receive HUBRFQ communication notes from Purchase Hub.
        /// </summary>
        internal static string Contact2Details {
            get {
                return ResourceManager.GetString("Contact2Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact First Name.
        /// </summary>
        internal static string ContactFirstName {
            get {
                return ResourceManager.GetString("ContactFirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Last Name.
        /// </summary>
        internal static string ContactLastName {
            get {
                return ResourceManager.GetString("ContactLastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Log Item.
        /// </summary>
        internal static string ContactLogItem {
            get {
                return ResourceManager.GetString("ContactLogItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Name.
        /// </summary>
        internal static string ContactName {
            get {
                return ResourceManager.GetString("ContactName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Name*.
        /// </summary>
        internal static string ContactName_ {
            get {
                return ResourceManager.GetString("ContactName*", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contract No.
        /// </summary>
        internal static string ContractNo {
            get {
                return ResourceManager.GetString("ContractNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Code.
        /// </summary>
        internal static string CorrectDateCode {
            get {
                return ResourceManager.GetString("CorrectDateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query - HIC Status.
        /// </summary>
        internal static string CorrectHIC {
            get {
                return ResourceManager.GetString("CorrectHIC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query - Manufacturer.
        /// </summary>
        internal static string CorrectManufacturer {
            get {
                return ResourceManager.GetString("CorrectManufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query - MSL.
        /// </summary>
        internal static string CorrectMSL {
            get {
                return ResourceManager.GetString("CorrectMSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query - Packaging Type.
        /// </summary>
        internal static string CorrectPackage {
            get {
                return ResourceManager.GetString("CorrectPackage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query - Part Number.
        /// </summary>
        internal static string CorrectPartNo {
            get {
                return ResourceManager.GetString("CorrectPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query - ROHS Status.
        /// </summary>
        internal static string CorrectRohsStatus {
            get {
                return ResourceManager.GetString("CorrectRohsStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost.
        /// </summary>
        internal static string Cost {
            get {
                return ResourceManager.GetString("Cost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Counting Method.
        /// </summary>
        internal static string CountingMethod {
            get {
                return ResourceManager.GetString("CountingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        internal static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country of Manufacture/Origin.
        /// </summary>
        internal static string CountryOfManufacture {
            get {
                return ResourceManager.GetString("CountryOfManufacture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Of Origin.
        /// </summary>
        internal static string countryOforigin {
            get {
                return ResourceManager.GetString("countryOforigin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to County.
        /// </summary>
        internal static string County {
            get {
                return ResourceManager.GetString("County", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to County/State.
        /// </summary>
        internal static string CountyState {
            get {
                return ResourceManager.GetString("CountyState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created Date From.
        /// </summary>
        internal static string CreatedDateFrom {
            get {
                return ResourceManager.GetString("CreatedDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Created Date To.
        /// </summary>
        internal static string CreatedDateTo {
            get {
                return ResourceManager.GetString("CreatedDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note.
        /// </summary>
        internal static string Credit {
            get {
                return ResourceManager.GetString("Credit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Card Fee.
        /// </summary>
        internal static string CreditCardFee {
            get {
                return ResourceManager.GetString("CreditCardFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Date.
        /// </summary>
        internal static string CreditDate {
            get {
                return ResourceManager.GetString("CreditDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Date From.
        /// </summary>
        internal static string CreditDateFrom {
            get {
                return ResourceManager.GetString("CreditDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Date To.
        /// </summary>
        internal static string CreditDateTo {
            get {
                return ResourceManager.GetString("CreditDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note Format.
        /// </summary>
        internal static string CreditFormat {
            get {
                return ResourceManager.GetString("CreditFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit History.
        /// </summary>
        internal static string CreditHistory {
            get {
                return ResourceManager.GetString("CreditHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Information.
        /// </summary>
        internal static string CreditInfo {
            get {
                return ResourceManager.GetString("CreditInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Limit.
        /// </summary>
        internal static string CreditLimit {
            get {
                return ResourceManager.GetString("CreditLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit No.
        /// </summary>
        internal static string CreditNo {
            get {
                return ResourceManager.GetString("CreditNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note No.
        /// </summary>
        internal static string CreditNoteNo {
            get {
                return ResourceManager.GetString("CreditNoteNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CRMA {
            get {
                return ResourceManager.GetString("CRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA Date.
        /// </summary>
        internal static string CRMADate {
            get {
                return ResourceManager.GetString("CRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA .
        /// </summary>
        internal static string CRMANo {
            get {
                return ResourceManager.GetString("CRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URNnumber.
        /// </summary>
        internal static string ctlURNnumber {
            get {
                return ResourceManager.GetString("ctlURNnumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency.
        /// </summary>
        internal static string Currency {
            get {
                return ResourceManager.GetString("Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Overdue.
        /// </summary>
        internal static string Current {
            get {
                return ResourceManager.GetString("Current", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Approver.
        /// </summary>
        internal static string CurrentApprover {
            get {
                return ResourceManager.GetString("CurrentApprover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Landed Cost.
        /// </summary>
        internal static string CurrentLandedCost {
            get {
                return ResourceManager.GetString("CurrentLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Rate.
        /// </summary>
        internal static string CurrentRate {
            get {
                return ResourceManager.GetString("CurrentRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Supplier.
        /// </summary>
        internal static string CurrentSupplier {
            get {
                return ResourceManager.GetString("CurrentSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Req No.
        /// </summary>
        internal static string CusReqNo {
            get {
                return ResourceManager.GetString("CusReqNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Date Required.
        /// </summary>
        internal static string CustDateRequired {
            get {
                return ResourceManager.GetString("CustDateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        internal static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Code.
        /// </summary>
        internal static string CustomerCode {
            get {
                return ResourceManager.GetString("CustomerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Debit No.
        /// </summary>
        internal static string CustomerDebit {
            get {
                return ResourceManager.GetString("CustomerDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Decision Date.
        /// </summary>
        internal static string CustomerDecisionDate {
            get {
                return ResourceManager.GetString("CustomerDecisionDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Group Code.
        /// </summary>
        internal static string CustomerGroupCode {
            get {
                return ResourceManager.GetString("CustomerGroupCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Name.
        /// </summary>
        internal static string CustomerName {
            get {
                return ResourceManager.GetString("CustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer No.
        /// </summary>
        internal static string CustomerNo {
            get {
                return ResourceManager.GetString("CustomerNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes to customer.
        /// </summary>
        internal static string CustomerNotes {
            get {
                return ResourceManager.GetString("CustomerNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Part.
        /// </summary>
        internal static string CustomerPart {
            get {
                return ResourceManager.GetString("CustomerPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cust Part No.
        /// </summary>
        internal static string CustomerPartNo {
            get {
                return ResourceManager.GetString("CustomerPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO.
        /// </summary>
        internal static string CustomerPO {
            get {
                return ResourceManager.GetString("CustomerPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO No.
        /// </summary>
        internal static string CustomerPONo {
            get {
                return ResourceManager.GetString("CustomerPONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO.
        /// </summary>
        internal static string CustomerPurchaseOrderNo {
            get {
                return ResourceManager.GetString("CustomerPurchaseOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Rating.
        /// </summary>
        internal static string CustomerRating {
            get {
                return ResourceManager.GetString("CustomerRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Ref No..
        /// </summary>
        internal static string CustomerRefNo {
            get {
                return ResourceManager.GetString("CustomerRefNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Rejection No.
        /// </summary>
        internal static string CustomerRejectionNo {
            get {
                return ResourceManager.GetString("CustomerRejectionNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirement.
        /// </summary>
        internal static string CustomerReq {
            get {
                return ResourceManager.GetString("CustomerReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Return No.
        /// </summary>
        internal static string CustomerReturn {
            get {
                return ResourceManager.GetString("CustomerReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA Date.
        /// </summary>
        internal static string CustomerRMADate {
            get {
                return ResourceManager.GetString("CustomerRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA Date From.
        /// </summary>
        internal static string CustomerRMADateFrom {
            get {
                return ResourceManager.GetString("CustomerRMADateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA Date To.
        /// </summary>
        internal static string CustomerRMADateTo {
            get {
                return ResourceManager.GetString("CustomerRMADateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CustomerRMANo {
            get {
                return ResourceManager.GetString("CustomerRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Target Price.
        /// </summary>
        internal static string CustomerTargetPrice {
            get {
                return ResourceManager.GetString("CustomerTargetPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO.
        /// </summary>
        internal static string CustPO {
            get {
                return ResourceManager.GetString("CustPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO number.
        /// </summary>
        internal static string CustPONo {
            get {
                return ResourceManager.GetString("CustPONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Reminder.
        /// </summary>
        internal static string DailyReminder {
            get {
                return ResourceManager.GetString("DailyReminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Database Server.
        /// </summary>
        internal static string DatabaseServer {
            get {
                return ResourceManager.GetString("DatabaseServer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Database Username.
        /// </summary>
        internal static string DatabaseUsername {
            get {
                return ResourceManager.GetString("DatabaseUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Database Password.
        /// </summary>
        internal static string DatabaseUserPassword {
            get {
                return ResourceManager.GetString("DatabaseUserPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Authorised.
        /// </summary>
        internal static string DateAuthorised {
            get {
                return ResourceManager.GetString("DateAuthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Code.
        /// </summary>
        internal static string DateCode {
            get {
                return ResourceManager.GetString("DateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Code Received.
        /// </summary>
        internal static string DateCodeReceived {
            get {
                return ResourceManager.GetString("DateCodeReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Confirmed.
        /// </summary>
        internal static string DateConfirmed {
            get {
                return ResourceManager.GetString("DateConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Delivered From.
        /// </summary>
        internal static string DateDeliveredFrom {
            get {
                return ResourceManager.GetString("DateDeliveredFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Delivered To.
        /// </summary>
        internal static string DateDeliveredTo {
            get {
                return ResourceManager.GetString("DateDeliveredTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date From.
        /// </summary>
        internal static string DateFrom {
            get {
                return ResourceManager.GetString("DateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Import From.
        /// </summary>
        internal static string DateImportFrom {
            get {
                return ResourceManager.GetString("DateImportFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Import To.
        /// </summary>
        internal static string DateImportTo {
            get {
                return ResourceManager.GetString("DateImportTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Released.
        /// </summary>
        internal static string DateInspected {
            get {
                return ResourceManager.GetString("DateInspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Invoiced.
        /// </summary>
        internal static string DateInvoiced {
            get {
                return ResourceManager.GetString("DateInvoiced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Invoiced From.
        /// </summary>
        internal static string DateInvoicedFrom {
            get {
                return ResourceManager.GetString("DateInvoicedFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Invoiced To.
        /// </summary>
        internal static string DateInvoicedTo {
            get {
                return ResourceManager.GetString("DateInvoicedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Ordered.
        /// </summary>
        internal static string DateOrdered {
            get {
                return ResourceManager.GetString("DateOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Ordered From.
        /// </summary>
        internal static string DateOrderedFrom {
            get {
                return ResourceManager.GetString("DateOrderedFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Ordered To.
        /// </summary>
        internal static string DateOrderedTo {
            get {
                return ResourceManager.GetString("DateOrderedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Picked.
        /// </summary>
        internal static string DatePicked {
            get {
                return ResourceManager.GetString("DatePicked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Promised.
        /// </summary>
        internal static string DatePromised {
            get {
                return ResourceManager.GetString("DatePromised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Promised From.
        /// </summary>
        internal static string DatePromisedFrom {
            get {
                return ResourceManager.GetString("DatePromisedFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Promised To.
        /// </summary>
        internal static string DatePromisedTo {
            get {
                return ResourceManager.GetString("DatePromisedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Quoted.
        /// </summary>
        internal static string DateQuoted {
            get {
                return ResourceManager.GetString("DateQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Quoted From.
        /// </summary>
        internal static string DateQuotedFrom {
            get {
                return ResourceManager.GetString("DateQuotedFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Quoted To.
        /// </summary>
        internal static string DateQuotedTo {
            get {
                return ResourceManager.GetString("DateQuotedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Date.
        /// </summary>
        internal static string DateReceived {
            get {
                return ResourceManager.GetString("DateReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Received From.
        /// </summary>
        internal static string DateReceivedFrom {
            get {
                return ResourceManager.GetString("DateReceivedFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Received To.
        /// </summary>
        internal static string DateReceivedTo {
            get {
                return ResourceManager.GetString("DateReceivedTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Required.
        /// </summary>
        internal static string DateRequired {
            get {
                return ResourceManager.GetString("DateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date To.
        /// </summary>
        internal static string DateTo {
            get {
                return ResourceManager.GetString("DateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days.
        /// </summary>
        internal static string Days {
            get {
                return ResourceManager.GetString("Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 01- 29 Days.
        /// </summary>
        internal static string Days1 {
            get {
                return ResourceManager.GetString("Days1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 120+ days.
        /// </summary>
        internal static string Days120 {
            get {
                return ResourceManager.GetString("Days120", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 30 - 59 days.
        /// </summary>
        internal static string Days30 {
            get {
                return ResourceManager.GetString("Days30", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 60 - 89 days.
        /// </summary>
        internal static string Days60 {
            get {
                return ResourceManager.GetString("Days60", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 90 - 119 days.
        /// </summary>
        internal static string Days90 {
            get {
                return ResourceManager.GetString("Days90", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit.
        /// </summary>
        internal static string Debit {
            get {
                return ResourceManager.GetString("Debit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Amount.
        /// </summary>
        internal static string DebitAmount {
            get {
                return ResourceManager.GetString("DebitAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Date.
        /// </summary>
        internal static string DebitDate {
            get {
                return ResourceManager.GetString("DebitDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Date From.
        /// </summary>
        internal static string DebitDateFrom {
            get {
                return ResourceManager.GetString("DebitDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Date To.
        /// </summary>
        internal static string DebitDateTo {
            get {
                return ResourceManager.GetString("DebitDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit No.
        /// </summary>
        internal static string DebitNo {
            get {
                return ResourceManager.GetString("DebitNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note Number.
        /// </summary>
        internal static string DebitNoteNo {
            get {
                return ResourceManager.GetString("DebitNoteNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note Ref.
        /// </summary>
        internal static string DebitNoteRef {
            get {
                return ResourceManager.GetString("DebitNoteRef", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Contact.
        /// </summary>
        internal static string DefaultContactNo {
            get {
                return ResourceManager.GetString("DefaultContactNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Currency.
        /// </summary>
        internal static string DefaultCurrency {
            get {
                return ResourceManager.GetString("DefaultCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Division Header.
        /// </summary>
        internal static string DefaultDivisionHeader {
            get {
                return ResourceManager.GetString("DefaultDivisionHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Division Name.
        /// </summary>
        internal static string DefaultDivisionName {
            get {
                return ResourceManager.GetString("DefaultDivisionName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Homepage Tab.
        /// </summary>
        internal static string DefaultHomepageTab {
            get {
                return ResourceManager.GetString("DefaultHomepageTab", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Language.
        /// </summary>
        internal static string DefaultLanguage {
            get {
                return ResourceManager.GetString("DefaultLanguage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Browse Page Size.
        /// </summary>
        internal static string DefaultListPageSize {
            get {
                return ResourceManager.GetString("DefaultListPageSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Browse Page View.
        /// </summary>
        internal static string DefaultListPageView {
            get {
                return ResourceManager.GetString("DefaultListPageView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Vendor Rating.
        /// </summary>
        internal static string DefaultPORating {
            get {
                return ResourceManager.GetString("DefaultPORating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Ship from Country.
        /// </summary>
        internal static string DefaultShipCountry {
            get {
                return ResourceManager.GetString("DefaultShipCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Shipping Account No.
        /// </summary>
        internal static string DefaultShippingAccountNo {
            get {
                return ResourceManager.GetString("DefaultShippingAccountNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Ship Via.
        /// </summary>
        internal static string DefaultShipVia {
            get {
                return ResourceManager.GetString("DefaultShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Customer Rating.
        /// </summary>
        internal static string DefaultSORating {
            get {
                return ResourceManager.GetString("DefaultSORating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Team Name.
        /// </summary>
        internal static string DefaultTeamName {
            get {
                return ResourceManager.GetString("DefaultTeamName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deliver By Date.
        /// </summary>
        internal static string DeliverByDate {
            get {
                return ResourceManager.GetString("DeliverByDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Charge.
        /// </summary>
        internal static string DeliveryCharge {
            get {
                return ResourceManager.GetString("DeliveryCharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date.
        /// </summary>
        internal static string DeliveryDate {
            get {
                return ResourceManager.GetString("DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date To Customer.
        /// </summary>
        internal static string DeliveryDateCustomer {
            get {
                return ResourceManager.GetString("DeliveryDateCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date From.
        /// </summary>
        internal static string DeliveryDateFrom {
            get {
                return ResourceManager.GetString("DeliveryDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date To Rebound.
        /// </summary>
        internal static string DeliveryDateRebound {
            get {
                return ResourceManager.GetString("DeliveryDateRebound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date Required.
        /// </summary>
        internal static string DeliveryDateRequired {
            get {
                return ResourceManager.GetString("DeliveryDateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date To.
        /// </summary>
        internal static string DeliveryDateTo {
            get {
                return ResourceManager.GetString("DeliveryDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Notes.
        /// </summary>
        internal static string DeliveryNotes {
            get {
                return ResourceManager.GetString("DeliveryNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Descriptions.
        /// </summary>
        internal static string Descriptions {
            get {
                return ResourceManager.GetString("Descriptions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination Country.
        /// </summary>
        internal static string DestinationCountry {
            get {
                return ResourceManager.GetString("DestinationCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dimensional Weight.
        /// </summary>
        internal static string DimensionalWeight {
            get {
                return ResourceManager.GetString("DimensionalWeight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display Advisory throughout all screens.
        /// </summary>
        internal static string DisplayAdvisory {
            get {
                return ResourceManager.GetString("DisplayAdvisory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division.
        /// </summary>
        internal static string Division {
            get {
                return ResourceManager.GetString("Division", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Header.
        /// </summary>
        internal static string DivisionHeader {
            get {
                return ResourceManager.GetString("DivisionHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document.
        /// </summary>
        internal static string Document {
            get {
                return ResourceManager.GetString("Document", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document File Size In Byte.
        /// </summary>
        internal static string DocumentSizeByte {
            get {
                return ResourceManager.GetString("DocumentSizeByte", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document File Size In MB.
        /// </summary>
        internal static string DocumentSizeMB {
            get {
                return ResourceManager.GetString("DocumentSizeMB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document Type.
        /// </summary>
        internal static string DocumentType {
            get {
                return ResourceManager.GetString("DocumentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date.
        /// </summary>
        internal static string DueDate {
            get {
                return ResourceManager.GetString("DueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Time.
        /// </summary>
        internal static string DueTime {
            get {
                return ResourceManager.GetString("DueTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duration.
        /// </summary>
        internal static string Duration {
            get {
                return ResourceManager.GetString("Duration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty % .
        /// </summary>
        internal static string Duty {
            get {
                return ResourceManager.GetString("Duty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Code.
        /// </summary>
        internal static string DutyCode {
            get {
                return ResourceManager.GetString("DutyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Code&lt;br/&gt;(Rate %).
        /// </summary>
        internal static string DutyCodeRate {
            get {
                return ResourceManager.GetString("DutyCodeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty-Free Harmonised Code.
        /// </summary>
        internal static string DutyFreeCode {
            get {
                return ResourceManager.GetString("DutyFreeCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Harmonised Code.
        /// </summary>
        internal static string DutyHarmonisedCode {
            get {
                return ResourceManager.GetString("DutyHarmonisedCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimated Annual Usage.
        /// </summary>
        internal static string EAU {
            get {
                return ResourceManager.GetString("EAU", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN:.
        /// </summary>
        internal static string ECCN {
            get {
                return ResourceManager.GetString("ECCN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Code.
        /// </summary>
        internal static string ECCNCode {
            get {
                return ResourceManager.GetString("ECCNCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Message.
        /// </summary>
        internal static string EccnMessage {
            get {
                return ResourceManager.GetString("EccnMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subject.
        /// </summary>
        internal static string EccnSubject {
            get {
                return ResourceManager.GetString("EccnSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Code.
        /// </summary>
        internal static string ECCN_Code {
            get {
                return ResourceManager.GetString("ECCN_Code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Details.
        /// </summary>
        internal static string EditDetails {
            get {
                return ResourceManager.GetString("EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edited By.
        /// </summary>
        internal static string EditedBy {
            get {
                return ResourceManager.GetString("EditedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edited Date From.
        /// </summary>
        internal static string EditedDateFrom {
            get {
                return ResourceManager.GetString("EditedDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edited Date To.
        /// </summary>
        internal static string EditedDateTo {
            get {
                return ResourceManager.GetString("EditedDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow edit Date Promised period between current month and Date Promise month.
        /// </summary>
        internal static string EditPromiseDate {
            get {
                return ResourceManager.GetString("EditPromiseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EEC Member.
        /// </summary>
        internal static string EECMember {
            get {
                return ResourceManager.GetString("EECMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sort Order.
        /// </summary>
        internal static string EightDCodeOrder {
            get {
                return ResourceManager.GetString("EightDCodeOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prefix.
        /// </summary>
        internal static string EightDCodePrifix {
            get {
                return ResourceManager.GetString("EightDCodePrifix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prefix Description.
        /// </summary>
        internal static string EightDCodePrifixDescription {
            get {
                return ResourceManager.GetString("EightDCodePrifixDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code.
        /// </summary>
        internal static string EightDSubCatCode {
            get {
                return ResourceManager.GetString("EightDSubCatCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code Description.
        /// </summary>
        internal static string EightDSubCatCodeDescription {
            get {
                return ResourceManager.GetString("EightDSubCatCodeDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        internal static string EIRequestComment {
            get {
                return ResourceManager.GetString("EIRequestComment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enhanced Inspection Charges.
        /// </summary>
        internal static string EI_Charges {
            get {
                return ResourceManager.GetString("EI_Charges", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Electronic Spend.
        /// </summary>
        internal static string ElectronicSpend {
            get {
                return ResourceManager.GetString("ElectronicSpend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Body.
        /// </summary>
        internal static string EmailBody {
            get {
                return ResourceManager.GetString("EmailBody", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email To.
        /// </summary>
        internal static string EmailTo {
            get {
                return ResourceManager.GetString("EmailTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchasing.
        /// </summary>
        internal static string EmailToPurchasing {
            get {
                return ResourceManager.GetString("EmailToPurchasing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Approval.
        /// </summary>
        internal static string EmailToQualityApproval {
            get {
                return ResourceManager.GetString("EmailToQualityApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales.
        /// </summary>
        internal static string EmailToSales {
            get {
                return ResourceManager.GetString("EmailToSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee.
        /// </summary>
        internal static string EmployeeName {
            get {
                return ResourceManager.GetString("EmployeeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Name.
        /// </summary>
        internal static string EmployeeNames {
            get {
                return ResourceManager.GetString("EmployeeNames", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable Power App Notification.
        /// </summary>
        internal static string EnablePowerApp {
            get {
                return ResourceManager.GetString("EnablePowerApp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Customer or CEM.
        /// </summary>
        internal static string EndCustomer {
            get {
                return ResourceManager.GetString("EndCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Date.
        /// </summary>
        internal static string EndDate {
            get {
                return ResourceManager.GetString("EndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Destination Country.
        /// </summary>
        internal static string EndDestinationCountry {
            get {
                return ResourceManager.GetString("EndDestinationCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End User.
        /// </summary>
        internal static string EndUser {
            get {
                return ResourceManager.GetString("EndUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enhanced Inspection Required.
        /// </summary>
        internal static string EnhancedInpectionRequired {
            get {
                return ResourceManager.GetString("EnhancedInpectionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enhanced Inspection.
        /// </summary>
        internal static string EnhancedInspection {
            get {
                return ResourceManager.GetString("EnhancedInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter the email.
        /// </summary>
        internal static string EnterEmail {
            get {
                return ResourceManager.GetString("EnterEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entertainment Date.
        /// </summary>
        internal static string EntertainDate {
            get {
                return ResourceManager.GetString("EntertainDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entertainment Type.
        /// </summary>
        internal static string EntertainmentType {
            get {
                return ResourceManager.GetString("EntertainmentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter the user name.
        /// </summary>
        internal static string EnterUserName {
            get {
                return ResourceManager.GetString("EnterUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Value.
        /// </summary>
        internal static string EnterValue {
            get {
                return ResourceManager.GetString("EnterValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EORI No.
        /// </summary>
        internal static string EORINumber {
            get {
                return ResourceManager.GetString("EORINumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EPR.
        /// </summary>
        internal static string EPR {
            get {
                return ResourceManager.GetString("EPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ERAI Member.
        /// </summary>
        internal static string ERAIMember {
            get {
                return ResourceManager.GetString("ERAIMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ERAI Reported.
        /// </summary>
        internal static string ERAIReported {
            get {
                return ResourceManager.GetString("ERAIReported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimated Freight.
        /// </summary>
        internal static string EstimatedFreight {
            get {
                return ResourceManager.GetString("EstimatedFreight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimated Shipping Cost.
        /// </summary>
        internal static string EstimatedShippingCost {
            get {
                return ResourceManager.GetString("EstimatedShippingCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ETA.
        /// </summary>
        internal static string ETA {
            get {
                return ResourceManager.GetString("ETA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Rate.
        /// </summary>
        internal static string ExchangeRate {
            get {
                return ResourceManager.GetString("ExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite Date.
        /// </summary>
        internal static string ExpediteDate {
            get {
                return ResourceManager.GetString("ExpediteDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite Date From.
        /// </summary>
        internal static string ExpediteDateFrom {
            get {
                return ResourceManager.GetString("ExpediteDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite Date To.
        /// </summary>
        internal static string ExpediteDateTo {
            get {
                return ResourceManager.GetString("ExpediteDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite History.
        /// </summary>
        internal static string ExpediteHistory {
            get {
                return ResourceManager.GetString("ExpediteHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite Notes.
        /// </summary>
        internal static string ExpediteNotes {
            get {
                return ResourceManager.GetString("ExpediteNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expiry Date.
        /// </summary>
        internal static string ExpiryDate {
            get {
                return ResourceManager.GetString("ExpiryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If Export Control is required, has the supplier verified they have the appropriate license.
        /// </summary>
        internal static string ExportControl {
            get {
                return ResourceManager.GetString("ExportControl", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export?.
        /// </summary>
        internal static string ExportData {
            get {
                return ResourceManager.GetString("ExportData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exported?.
        /// </summary>
        internal static string Exported {
            get {
                return ResourceManager.GetString("Exported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exported Date.
        /// </summary>
        internal static string ExportedDate {
            get {
                return ResourceManager.GetString("ExportedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exported Only.
        /// </summary>
        internal static string ExportedOnly {
            get {
                return ResourceManager.GetString("ExportedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exported?.
        /// </summary>
        internal static string Exported_ {
            get {
                return ResourceManager.GetString("Exported?", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Extension.
        /// </summary>
        internal static string Extension {
            get {
                return ResourceManager.GetString("Extension", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string ExternalNotes {
            get {
                return ResourceManager.GetString("ExternalNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Factory.
        /// </summary>
        internal static string Factory {
            get {
                return ResourceManager.GetString("Factory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Factory Sealed (Y/N).
        /// </summary>
        internal static string FactorySealed {
            get {
                return ResourceManager.GetString("FactorySealed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Factory Sealed.
        /// </summary>
        internal static string FactorySealed2 {
            get {
                return ResourceManager.GetString("FactorySealed2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fail.
        /// </summary>
        internal static string Fail {
            get {
                return ResourceManager.GetString("Fail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed Only.
        /// </summary>
        internal static string FailedOnly {
            get {
                return ResourceManager.GetString("FailedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Favourite Sport.
        /// </summary>
        internal static string FavouriteSport {
            get {
                return ResourceManager.GetString("FavouriteSport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Favourite Team.
        /// </summary>
        internal static string FavouriteTeam {
            get {
                return ResourceManager.GetString("FavouriteTeam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fax.
        /// </summary>
        internal static string Fax {
            get {
                return ResourceManager.GetString("Fax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Feedback.
        /// </summary>
        internal static string Feedback {
            get {
                return ResourceManager.GetString("Feedback", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File.
        /// </summary>
        internal static string File {
            get {
                return ResourceManager.GetString("File", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Final Assembly.
        /// </summary>
        internal static string FinalAssembly {
            get {
                return ResourceManager.GetString("FinalAssembly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finance Contact?.
        /// </summary>
        internal static string FinanceContact {
            get {
                return ResourceManager.GetString("FinanceContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        internal static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to FOB.
        /// </summary>
        internal static string FOB {
            get {
                return ResourceManager.GetString("FOB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Folder.
        /// </summary>
        internal static string Folder {
            get {
                return ResourceManager.GetString("Folder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Footer Text.
        /// </summary>
        internal static string FooterText {
            get {
                return ResourceManager.GetString("FooterText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Franchised?.
        /// </summary>
        internal static string Franchise {
            get {
                return ResourceManager.GetString("Franchise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Free On Board.
        /// </summary>
        internal static string FreeOnBoard {
            get {
                return ResourceManager.GetString("FreeOnBoard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight.
        /// </summary>
        internal static string Freight {
            get {
                return ResourceManager.GetString("Freight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Match Freight to the Shipping cost on invoice.
        /// </summary>
        internal static string FreightCost {
            get {
                return ResourceManager.GetString("FreightCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uplift by percentage.
        /// </summary>
        internal static string FreightPercentage {
            get {
                return ResourceManager.GetString("FreightPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Frequency Of Purchase.
        /// </summary>
        internal static string FrequencyOfPurchase {
            get {
                return ResourceManager.GetString("FrequencyOfPurchase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From.
        /// </summary>
        internal static string From {
            get {
                return ResourceManager.GetString("From", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Funds.
        /// </summary>
        internal static string Funds {
            get {
                return ResourceManager.GetString("Funds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gender.
        /// </summary>
        internal static string Gender {
            get {
                return ResourceManager.GetString("Gender", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Inspection Notes.
        /// </summary>
        internal static string GeneralInspectionNotes {
            get {
                return ResourceManager.GetString("GeneralInspectionNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Notes.
        /// </summary>
        internal static string GeneralNotes {
            get {
                return ResourceManager.GetString("GeneralNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Users Security Group Name.
        /// </summary>
        internal static string GeneralUsersSecurityGroupName {
            get {
                return ResourceManager.GetString("GeneralUsersSecurityGroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generic database timeout.
        /// </summary>
        internal static string GenericDatabaseTimeout {
            get {
                return ResourceManager.GetString("GenericDatabaseTimeout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All GI Query.
        /// </summary>
        internal static string GIAllQueries {
            get {
                return ResourceManager.GetString("GIAllQueries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Recorded.
        /// </summary>
        internal static string GiBarcodeScan {
            get {
                return ResourceManager.GetString("GiBarcodeScan", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Barcode Scan Remark.
        /// </summary>
        internal static string GiBarcodeScanRemark {
            get {
                return ResourceManager.GetString("GiBarcodeScanRemark", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Date From.
        /// </summary>
        internal static string GIDateFrom {
            get {
                return ResourceManager.GetString("GIDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Date To.
        /// </summary>
        internal static string GIDateTo {
            get {
                return ResourceManager.GetString("GIDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete Inspection.
        /// </summary>
        internal static string GILines_CloseInspection {
            get {
                return ResourceManager.GetString("GILines_CloseInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Inspection.
        /// </summary>
        internal static string GILines_StartInspection {
            get {
                return ResourceManager.GetString("GILines_StartInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GIN.
        /// </summary>
        internal static string GIN {
            get {
                return ResourceManager.GetString("GIN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Note.
        /// </summary>
        internal static string GINo {
            get {
                return ResourceManager.GetString("GINo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Number.
        /// </summary>
        internal static string GINoNpr {
            get {
                return ResourceManager.GetString("GINoNpr", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CC.
        /// </summary>
        internal static string GIQCC {
            get {
                return ResourceManager.GetString("GIQCC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved.
        /// </summary>
        internal static string GIQueryApproved {
            get {
                return ResourceManager.GetString("GIQueryApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query Reply.
        /// </summary>
        internal static string GIQueryReply {
            get {
                return ResourceManager.GetString("GIQueryReply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query Status.
        /// </summary>
        internal static string GIQueryStatus {
            get {
                return ResourceManager.GetString("GIQueryStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RENAME ATTACHMENT.
        /// </summary>
        internal static string GIRename {
            get {
                return ResourceManager.GetString("GIRename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In.
        /// </summary>
        internal static string GoodsIn {
            get {
                return ResourceManager.GetString("GoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Note.
        /// </summary>
        internal static string GoodsInNo {
            get {
                return ResourceManager.GetString("GoodsInNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In No.
        /// </summary>
        internal static string GoodsInNoteNo {
            get {
                return ResourceManager.GetString("GoodsInNoteNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Number.
        /// </summary>
        internal static string GoodsInNumber {
            get {
                return ResourceManager.GetString("GoodsInNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        internal static string GoodsInUpdateType {
            get {
                return ResourceManager.GetString("GoodsInUpdateType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Value.
        /// </summary>
        internal static string GoodsInValue {
            get {
                return ResourceManager.GetString("GoodsInValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods Value.
        /// </summary>
        internal static string GoodsValue {
            get {
                return ResourceManager.GetString("GoodsValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP.
        /// </summary>
        internal static string GP {
            get {
                return ResourceManager.GetString("GP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group.
        /// </summary>
        internal static string Group {
            get {
                return ResourceManager.GetString("Group", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Access.
        /// </summary>
        internal static string GroupAccess {
            get {
                return ResourceManager.GetString("GroupAccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Code.
        /// </summary>
        internal static string GroupCode {
            get {
                return ResourceManager.GetString("GroupCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Code Name.
        /// </summary>
        internal static string GroupCodeName {
            get {
                return ResourceManager.GetString("GroupCodeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Description.
        /// </summary>
        internal static string GroupDiscription {
            get {
                return ResourceManager.GetString("GroupDiscription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Group Name.
        /// </summary>
        internal static string GroupName {
            get {
                return ResourceManager.GetString("GroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT Updated Version.
        /// </summary>
        internal static string GTAppUpdateVersion {
            get {
                return ResourceManager.GetString("GTAppUpdateVersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT Update Name.
        /// </summary>
        internal static string GTUpdateName {
            get {
                return ResourceManager.GetString("GTUpdateName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        internal static string GTUpdateTitle {
            get {
                return ResourceManager.GetString("GTUpdateTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Health Rating %.
        /// </summary>
        internal static string HealthRating {
            get {
                return ResourceManager.GetString("HealthRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HIC Status.
        /// </summary>
        internal static string HICStatus {
            get {
                return ResourceManager.GetString("HICStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hobbies.
        /// </summary>
        internal static string Hobbies {
            get {
                return ResourceManager.GetString("Hobbies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home Email.
        /// </summary>
        internal static string HomeEmail {
            get {
                return ResourceManager.GetString("HomeEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home Fax.
        /// </summary>
        internal static string HomeFax {
            get {
                return ResourceManager.GetString("HomeFax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Homepage top salespeople.
        /// </summary>
        internal static string HomepageTopSalespeople {
            get {
                return ResourceManager.GetString("HomepageTopSalespeople", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tel 2.
        /// </summary>
        internal static string HomeTel {
            get {
                return ResourceManager.GetString("HomeTel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home Telephone.
        /// </summary>
        internal static string HomeTelephone {
            get {
                return ResourceManager.GetString("HomeTelephone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTC Code.
        /// </summary>
        internal static string HtcCode {
            get {
                return ResourceManager.GetString("HtcCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTS Code.
        /// </summary>
        internal static string HTSCode {
            get {
                return ResourceManager.GetString("HTSCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB CC.
        /// </summary>
        internal static string HUBCC {
            get {
                return ResourceManager.GetString("HUBCC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign HUBRFQ.
        /// </summary>
        internal static string HUBRFQ {
            get {
                return ResourceManager.GetString("HUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ No-Bid.
        /// </summary>
        internal static string HUBRFQIsNoBid {
            get {
                return ResourceManager.GetString("HUBRFQIsNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Released.
        /// </summary>
        internal static string HUBRFQIsReleased {
            get {
                return ResourceManager.GetString("HUBRFQIsReleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string HUBRFQName {
            get {
                return ResourceManager.GetString("HUBRFQName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ignore the selected ‘Apply Division header’ only on invoices.
        /// </summary>
        internal static string IgnoreDivHeader {
            get {
                return ResourceManager.GetString("IgnoreDivHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ignore Row Limit.
        /// </summary>
        internal static string IgnoreRowLimit {
            get {
                return ResourceManager.GetString("IgnoreRowLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Information.
        /// </summary>
        internal static string ihsInformation {
            get {
                return ResourceManager.GetString("ihsInformation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Product.
        /// </summary>
        internal static string IHSProduct {
            get {
                return ResourceManager.GetString("IHSProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IHS Product.
        /// </summary>
        internal static string IHSProductName {
            get {
                return ResourceManager.GetString("IHSProductName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only show marked as important.
        /// </summary>
        internal static string Important {
            get {
                return ResourceManager.GetString("Important", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Important Notes.
        /// </summary>
        internal static string ImportantNotes {
            get {
                return ResourceManager.GetString("ImportantNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Imported By.
        /// </summary>
        internal static string ImportedBy {
            get {
                return ResourceManager.GetString("ImportedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Status.
        /// </summary>
        internal static string ImportStatus {
            get {
                return ResourceManager.GetString("ImportStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactivate Restricted MFR.
        /// </summary>
        internal static string InactivateRestrictedMFR {
            get {
                return ResourceManager.GetString("InactivateRestrictedMFR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Advance.
        /// </summary>
        internal static string InAdvance {
            get {
                return ResourceManager.GetString("InAdvance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Closed?.
        /// </summary>
        internal static string IncludeClosed {
            get {
                return ResourceManager.GetString("IncludeClosed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attach Customer Template.
        /// </summary>
        internal static string IncludeCustomTemplate {
            get {
                return ResourceManager.GetString("IncludeCustomTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Exported ?.
        /// </summary>
        internal static string IncludeExported {
            get {
                return ResourceManager.GetString("IncludeExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include History.
        /// </summary>
        internal static string IncludeHistory {
            get {
                return ResourceManager.GetString("IncludeHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Invoiced?.
        /// </summary>
        internal static string IncludeInvoiced {
            get {
                return ResourceManager.GetString("IncludeInvoiced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print/Email inv/pck slip as PDF.
        /// </summary>
        internal static string IncludeInvoiceEmbedImage {
            get {
                return ResourceManager.GetString("IncludeInvoiceEmbedImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include on-Hold.
        /// </summary>
        internal static string IncludeOnHold {
            get {
                return ResourceManager.GetString("IncludeOnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO sent to customer.
        /// </summary>
        internal static string IncludeOrderSent {
            get {
                return ResourceManager.GetString("IncludeOrderSent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Paid.
        /// </summary>
        internal static string IncludePaid {
            get {
                return ResourceManager.GetString("IncludePaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Received?.
        /// </summary>
        internal static string IncludeReceived {
            get {
                return ResourceManager.GetString("IncludeReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Shipped?.
        /// </summary>
        internal static string IncludeShipped {
            get {
                return ResourceManager.GetString("IncludeShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include shipping on Homepage Statistics?.
        /// </summary>
        internal static string IncludeShippingOnHomepageStats {
            get {
                return ResourceManager.GetString("IncludeShippingOnHomepageStats", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Zero Stock?.
        /// </summary>
        internal static string IncludeZeroStock {
            get {
                return ResourceManager.GetString("IncludeZeroStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incoterms.
        /// </summary>
        internal static string Incoterm {
            get {
                return ResourceManager.GetString("Incoterm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incoterms.
        /// </summary>
        internal static string Incoterms {
            get {
                return ResourceManager.GetString("Incoterms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Match freight to the Shipping Cost on Invoice.
        /// </summary>
        internal static string IncreaseFreight {
            get {
                return ResourceManager.GetString("IncreaseFreight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry.
        /// </summary>
        internal static string Industry {
            get {
                return ResourceManager.GetString("Industry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Area discussed.
        /// </summary>
        internal static string IndustryAreaType {
            get {
                return ResourceManager.GetString("IndustryAreaType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Industry Type.
        /// </summary>
        internal static string IndustryType {
            get {
                return ResourceManager.GetString("IndustryType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Initial.
        /// </summary>
        internal static string Initial {
            get {
                return ResourceManager.GetString("Initial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Released By.
        /// </summary>
        internal static string InspectedBy {
            get {
                return ResourceManager.GetString("InspectedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspection completed for the Goods In Line.
        /// </summary>
        internal static string InspectionCompletedDetails {
            get {
                return ResourceManager.GetString("InspectionCompletedDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspection History.
        /// </summary>
        internal static string InspectionHis {
            get {
                return ResourceManager.GetString("InspectionHis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspection re-open for the Goods In Line.
        /// </summary>
        internal static string InspectionReopenDetails {
            get {
                return ResourceManager.GetString("InspectionReopenDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspection started for the Goods In Line.
        /// </summary>
        internal static string InspectionstartedDetails {
            get {
                return ResourceManager.GetString("InspectionstartedDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Stock.
        /// </summary>
        internal static string InStock {
            get {
                return ResourceManager.GetString("InStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instructions to Warehouse.
        /// </summary>
        internal static string InsToQualityControlNotes {
            get {
                return ResourceManager.GetString("InsToQualityControlNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instructions.
        /// </summary>
        internal static string Instructions {
            get {
                return ResourceManager.GetString("Instructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance Ceriticate Name.
        /// </summary>
        internal static string InsuranceCeriticateName {
            get {
                return ResourceManager.GetString("InsuranceCeriticateName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance Ceriticate Number.
        /// </summary>
        internal static string InsuranceCeriticateNumber {
            get {
                return ResourceManager.GetString("InsuranceCeriticateNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate No.
        /// </summary>
        internal static string InsuranceCertificateNo {
            get {
                return ResourceManager.GetString("InsuranceCertificateNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance File No.
        /// </summary>
        internal static string InsuranceFileNo {
            get {
                return ResourceManager.GetString("InsuranceFileNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insurance History.
        /// </summary>
        internal static string InsuranceHistory {
            get {
                return ResourceManager.GetString("InsuranceHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insured Amount.
        /// </summary>
        internal static string InsuredAmount {
            get {
                return ResourceManager.GetString("InsuredAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Insured Amount Currency.
        /// </summary>
        internal static string InsuredAmountCurrency {
            get {
                return ResourceManager.GetString("InsuredAmountCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Instructions.
        /// </summary>
        internal static string InternalInstructions {
            get {
                return ResourceManager.GetString("InternalInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Log.
        /// </summary>
        internal static string InternalLog {
            get {
                return ResourceManager.GetString("InternalLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal notes.
        /// </summary>
        internal static string InternalNotes {
            get {
                return ResourceManager.GetString("InternalNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string InternalPO {
            get {
                return ResourceManager.GetString("InternalPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Internal Purchase Order Line.
        /// </summary>
        internal static string InternalPOLines_Edit {
            get {
                return ResourceManager.GetString("InternalPOLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Purchase Order.
        /// </summary>
        internal static string InternalPurchaseOrder {
            get {
                return ResourceManager.GetString("InternalPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO No.
        /// </summary>
        internal static string InternalPurchaseOrderNo {
            get {
                return ResourceManager.GetString("InternalPurchaseOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Amount.
        /// </summary>
        internal static string InvoiceAmount {
            get {
                return ResourceManager.GetString("InvoiceAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activate Invoice Auto Export.
        /// </summary>
        internal static string InvoiceAutoExport {
            get {
                return ResourceManager.GetString("InvoiceAutoExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Date.
        /// </summary>
        internal static string InvoiceDate {
            get {
                return ResourceManager.GetString("InvoiceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Format.
        /// </summary>
        internal static string InvoiceFormat {
            get {
                return ResourceManager.GetString("InvoiceFormat", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hold Export.
        /// </summary>
        internal static string InvoiceHold {
            get {
                return ResourceManager.GetString("InvoiceHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice No.
        /// </summary>
        internal static string InvoiceNo {
            get {
                return ResourceManager.GetString("InvoiceNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Not Exported.
        /// </summary>
        internal static string InvoiceNotExport {
            get {
                return ResourceManager.GetString("InvoiceNotExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid Date.
        /// </summary>
        internal static string InvoicePaidDate {
            get {
                return ResourceManager.GetString("InvoicePaidDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string IPO {
            get {
                return ResourceManager.GetString("IPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string IPOBOM {
            get {
                return ResourceManager.GetString("IPOBOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Name.
        /// </summary>
        internal static string IPOBOMName {
            get {
                return ResourceManager.GetString("IPOBOMName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO line notes.
        /// </summary>
        internal static string IPOLineNotes {
            get {
                return ResourceManager.GetString("IPOLineNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO No.
        /// </summary>
        internal static string IPONo {
            get {
                return ResourceManager.GetString("IPONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO Purchasing email group.
        /// </summary>
        internal static string IPOPurchasing {
            get {
                return ResourceManager.GetString("IPOPurchasing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string IPurchaseOrderNo {
            get {
                return ResourceManager.GetString("IPurchaseOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IsActive?.
        /// </summary>
        internal static string IsActive {
            get {
                return ResourceManager.GetString("IsActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved?.
        /// </summary>
        internal static string IsApproved {
            get {
                return ResourceManager.GetString("IsApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Customer?.
        /// </summary>
        internal static string IsApprovedCustomer {
            get {
                return ResourceManager.GetString("IsApprovedCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Supplier?.
        /// </summary>
        internal static string IsApprovedSupplier {
            get {
                return ResourceManager.GetString("IsApprovedSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to AS6081.
        /// </summary>
        internal static string IsAS6081Required {
            get {
                return ResourceManager.GetString("IsAS6081Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Authorised.
        /// </summary>
        internal static string IsAuthorised {
            get {
                return ResourceManager.GetString("IsAuthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avoidable.
        /// </summary>
        internal static string IsAvoidable {
            get {
                return ResourceManager.GetString("IsAvoidable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel?.
        /// </summary>
        internal static string IsCancel {
            get {
                return ResourceManager.GetString("IsCancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed?.
        /// </summary>
        internal static string IsClosed {
            get {
                return ResourceManager.GetString("IsClosed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Completed.
        /// </summary>
        internal static string IsCompleted {
            get {
                return ResourceManager.GetString("IsCompleted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed?.
        /// </summary>
        internal static string IsConfirmed {
            get {
                return ResourceManager.GetString("IsConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Consignment?.
        /// </summary>
        internal static string IsConsignment {
            get {
                return ResourceManager.GetString("IsConsignment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct Date Code?.
        /// </summary>
        internal static string IsDateCodeCorrect {
            get {
                return ResourceManager.GetString("IsDateCodeCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Date Code Required?.
        /// </summary>
        internal static string IsDateCodeRequired {
            get {
                return ResourceManager.GetString("IsDateCodeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Bill?.
        /// </summary>
        internal static string IsDefaultBill {
            get {
                return ResourceManager.GetString("IsDefaultBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Ship?.
        /// </summary>
        internal static string IsDefaultShip {
            get {
                return ResourceManager.GetString("IsDefaultShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty?.
        /// </summary>
        internal static string IsDutyPayable {
            get {
                return ResourceManager.GetString("IsDutyPayable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Notification.
        /// </summary>
        internal static string IsECCNClientNotification {
            get {
                return ResourceManager.GetString("IsECCNClientNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Warning.
        /// </summary>
        internal static string IsECCNStatus {
            get {
                return ResourceManager.GetString("IsECCNStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text Only Email?.
        /// </summary>
        internal static string IsEmailTextOnly {
            get {
                return ResourceManager.GetString("IsEmailTextOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exported?.
        /// </summary>
        internal static string IsExported {
            get {
                return ResourceManager.GetString("IsExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Export To PDF?.
        /// </summary>
        internal static string IsExportPDF {
            get {
                return ResourceManager.GetString("IsExportPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Export To Word?.
        /// </summary>
        internal static string IsExportWord {
            get {
                return ResourceManager.GetString("IsExportWord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generated from Prospective Offer.
        /// </summary>
        internal static string IsFromProspectiveOffer {
            get {
                return ResourceManager.GetString("IsFromProspectiveOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Full Quantity Received?.
        /// </summary>
        internal static string IsFullQuantityReceived {
            get {
                return ResourceManager.GetString("IsFullQuantityReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is General Notes Add?.
        /// </summary>
        internal static string IsGeneralNotesAdd {
            get {
                return ResourceManager.GetString("IsGeneralNotesAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Grouped?.
        /// </summary>
        internal static string IsGrouped {
            get {
                return ResourceManager.GetString("IsGrouped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send mail to users.
        /// </summary>
        internal static string IsGTUpdateSendMail {
            get {
                return ResourceManager.GetString("IsGTUpdateSendMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Popup show on Home.
        /// </summary>
        internal static string IsGTUpdateShowHome {
            get {
                return ResourceManager.GetString("IsGTUpdateShowHome", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hazarders?.
        /// </summary>
        internal static string IsHazarders {
            get {
                return ResourceManager.GetString("IsHazarders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raise HIC Status Query?.
        /// </summary>
        internal static string IsHICCorrect {
            get {
                return ResourceManager.GetString("IsHICCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to High Priority?.
        /// </summary>
        internal static string IsHighPriority {
            get {
                return ResourceManager.GetString("IsHighPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sanctions?.
        /// </summary>
        internal static string IsHighRisk {
            get {
                return ResourceManager.GetString("IsHighRisk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark as Important.
        /// </summary>
        internal static string IsImportant {
            get {
                return ResourceManager.GetString("IsImportant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactive?.
        /// </summary>
        internal static string IsInactive {
            get {
                return ResourceManager.GetString("IsInactive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Included.
        /// </summary>
        internal static string IsIncluded {
            get {
                return ResourceManager.GetString("IsIncluded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspection conducted in accordance with Supplier Type, Batch Sample Table RGFM09 and Material Inspection Guidelines RGFM42.
        /// </summary>
        internal static string IsInspectionConducted {
            get {
                return ResourceManager.GetString("IsInspectionConducted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List Priority?.
        /// </summary>
        internal static string IsListPriority {
            get {
                return ResourceManager.GetString("IsListPriority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct Manufacturer?.
        /// </summary>
        internal static string IsManufacturerCorrect {
            get {
                return ResourceManager.GetString("IsManufacturerCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MSL Correct?.
        /// </summary>
        internal static string IsMSLCorrect {
            get {
                return ResourceManager.GetString("IsMSLCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No-Bid.
        /// </summary>
        internal static string IsNoBid {
            get {
                return ResourceManager.GetString("IsNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Hold?.
        /// </summary>
        internal static string IsOnHold {
            get {
                return ResourceManager.GetString("IsOnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Stop?.
        /// </summary>
        internal static string IsOnStop {
            get {
                return ResourceManager.GetString("IsOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Isopropyl Alcohol Test.
        /// </summary>
        internal static string Isopropryle {
            get {
                return ResourceManager.GetString("Isopropryle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order via IPO only?.
        /// </summary>
        internal static string IsOrderViaIPOonly {
            get {
                return ResourceManager.GetString("IsOrderViaIPOonly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Type Correct?.
        /// </summary>
        internal static string IsPackageCorrect {
            get {
                return ResourceManager.GetString("IsPackageCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid?.
        /// </summary>
        internal static string IsPaid {
            get {
                return ResourceManager.GetString("IsPaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct Part Number?.
        /// </summary>
        internal static string IsPartNoCorrect {
            get {
                return ResourceManager.GetString("IsPartNoCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PoHub?.
        /// </summary>
        internal static string IsPoHub {
            get {
                return ResourceManager.GetString("IsPoHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Posted.
        /// </summary>
        internal static string IsPosted {
            get {
                return ResourceManager.GetString("IsPosted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospect?.
        /// </summary>
        internal static string IsProspect {
            get {
                return ResourceManager.GetString("IsProspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantined?.
        /// </summary>
        internal static string IsQuarantined {
            get {
                return ResourceManager.GetString("IsQuarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restricted Product.
        /// </summary>
        internal static string IsRestrictedProduct {
            get {
                return ResourceManager.GetString("IsRestrictedProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Compliant?.
        /// </summary>
        internal static string IsROHSCompliant {
            get {
                return ResourceManager.GetString("IsROHSCompliant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Status Correct?.
        /// </summary>
        internal static string IsRohsStatusCorrect {
            get {
                return ResourceManager.GetString("IsRohsStatusCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Shipment Notification?.
        /// </summary>
        internal static string IsSendShipmentNotification {
            get {
                return ResourceManager.GetString("IsSendShipmentNotification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship ASAP.
        /// </summary>
        internal static string IsShipASAP {
            get {
                return ResourceManager.GetString("IsShipASAP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Waive Shipping &amp; Shipping Surcharge?.
        /// </summary>
        internal static string IsShippingWaived {
            get {
                return ResourceManager.GetString("IsShippingWaived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit refund given?.
        /// </summary>
        internal static string IsShortageRefundIssue {
            get {
                return ResourceManager.GetString("IsShortageRefundIssue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will a credit / refund be issued to cover the value of the shortage?.
        /// </summary>
        internal static string IsShortageRefundIssued {
            get {
                return ResourceManager.GetString("IsShortageRefundIssued", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Images attached.
        /// </summary>
        internal static string IsSorcingImage {
            get {
                return ResourceManager.GetString("IsSorcingImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable?.
        /// </summary>
        internal static string IsTaxable {
            get {
                return ResourceManager.GetString("IsTaxable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Traceability required.
        /// </summary>
        internal static string IsTraceability {
            get {
                return ResourceManager.GetString("IsTraceability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Virtual?.
        /// </summary>
        internal static string IsVirtual {
            get {
                return ResourceManager.GetString("IsVirtual", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Job Title.
        /// </summary>
        internal static string JobTitle {
            get {
                return ResourceManager.GetString("JobTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable Kub.
        /// </summary>
        internal static string KubConfig {
            get {
                return ResourceManager.GetString("KubConfig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Label Path.
        /// </summary>
        internal static string LabelFullPath {
            get {
                return ResourceManager.GetString("LabelFullPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nice Label Path.
        /// </summary>
        internal static string LabelPath {
            get {
                return ResourceManager.GetString("LabelPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Vat Label.
        /// </summary>
        internal static string LabelType {
            get {
                return ResourceManager.GetString("LabelType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost.
        /// </summary>
        internal static string LandedCost {
            get {
                return ResourceManager.GetString("LandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost From.
        /// </summary>
        internal static string LandedCostFrom {
            get {
                return ResourceManager.GetString("LandedCostFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost To.
        /// </summary>
        internal static string LandedCostTo {
            get {
                return ResourceManager.GetString("LandedCostTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Contacted.
        /// </summary>
        internal static string LastContacted {
            get {
                return ResourceManager.GetString("LastContacted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        internal static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Order Date.
        /// </summary>
        internal static string LastOrderDate {
            get {
                return ResourceManager.GetString("LastOrderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last time buy.
        /// </summary>
        internal static string LastTimeBuy {
            get {
                return ResourceManager.GetString("LastTimeBuy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Time Buy Date.
        /// </summary>
        internal static string LastTimeBuyDate {
            get {
                return ResourceManager.GetString("LastTimeBuyDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Time Ship Date.
        /// </summary>
        internal static string LastTimeShipDate {
            get {
                return ResourceManager.GetString("LastTimeShipDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Year.
        /// </summary>
        internal static string LastYear {
            get {
                return ResourceManager.GetString("LastYear", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last year [spend | profit (%)]:.
        /// </summary>
        internal static string LastYearNew {
            get {
                return ResourceManager.GetString("LastYearNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lead Time (Weeks).
        /// </summary>
        internal static string LeadTime {
            get {
                return ResourceManager.GetString("LeadTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lead Time (Weeks).
        /// </summary>
        internal static string LeadTimeWKS {
            get {
                return ResourceManager.GetString("LeadTimeWKS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Status.
        /// </summary>
        internal static string LifeCycleStage {
            get {
                return ResourceManager.GetString("LifeCycleStage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Life Status.
        /// </summary>
        internal static string LifeStatus {
            get {
                return ResourceManager.GetString("LifeStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Limit Potential.
        /// </summary>
        internal static string LimitedEstimate {
            get {
                return ResourceManager.GetString("LimitedEstimate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Detail.
        /// </summary>
        internal static string LineDetail {
            get {
                return ResourceManager.GetString("LineDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Details.
        /// </summary>
        internal static string LineDetails {
            get {
                return ResourceManager.GetString("LineDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List Name.
        /// </summary>
        internal static string ListName {
            get {
                return ResourceManager.GetString("ListName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List No.
        /// </summary>
        internal static string ListNo {
            get {
                return ResourceManager.GetString("ListNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List Page Results Limit.
        /// </summary>
        internal static string ListViewResultsLimit {
            get {
                return ResourceManager.GetString("ListViewResultsLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Month GP.
        /// </summary>
        internal static string LMGp {
            get {
                return ResourceManager.GetString("LMGp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Month GP (%).
        /// </summary>
        internal static string LMGpPct {
            get {
                return ResourceManager.GetString("LMGpPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Month %.
        /// </summary>
        internal static string LMPct {
            get {
                return ResourceManager.GetString("LMPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Local Currency.
        /// </summary>
        internal static string LocalCurrency {
            get {
                return ResourceManager.GetString("LocalCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lock update from Client.
        /// </summary>
        internal static string LockUpdateClient {
            get {
                return ResourceManager.GetString("LockUpdateClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Date From.
        /// </summary>
        internal static string LogDateFrom {
            get {
                return ResourceManager.GetString("LogDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Date To.
        /// </summary>
        internal static string LogDateTo {
            get {
                return ResourceManager.GetString("LogDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login timeout.
        /// </summary>
        internal static string LoginTimeout {
            get {
                return ResourceManager.GetString("LoginTimeout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Notes.
        /// </summary>
        internal static string LogNotes {
            get {
                return ResourceManager.GetString("LogNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Type.
        /// </summary>
        internal static string LogType {
            get {
                return ResourceManager.GetString("LogType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Details.
        /// </summary>
        internal static string Log_Details {
            get {
                return ResourceManager.GetString("Log_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot.
        /// </summary>
        internal static string Lot {
            get {
                return ResourceManager.GetString("Lot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LOT Code Required.
        /// </summary>
        internal static string LotCodeReq {
            get {
                return ResourceManager.GetString("LotCodeReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lock for customer.
        /// </summary>
        internal static string LotLockForCust {
            get {
                return ResourceManager.GetString("LotLockForCust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot No.
        /// </summary>
        internal static string LotNo {
            get {
                return ResourceManager.GetString("LotNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot Number.
        /// </summary>
        internal static string LotNumber {
            get {
                return ResourceManager.GetString("LotNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Lead Time - Air.
        /// </summary>
        internal static string LTAir {
            get {
                return ResourceManager.GetString("LTAir", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last time buy (LTB) - (Y/N).
        /// </summary>
        internal static string LTB {
            get {
                return ResourceManager.GetString("LTB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Lead Time - Surface.
        /// </summary>
        internal static string LTSurface {
            get {
                return ResourceManager.GetString("LTSurface", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Year GP.
        /// </summary>
        internal static string LYGp {
            get {
                return ResourceManager.GetString("LYGp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Year GP (%).
        /// </summary>
        internal static string LYGpPct {
            get {
                return ResourceManager.GetString("LYGpPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Year %.
        /// </summary>
        internal static string LYPct {
            get {
                return ResourceManager.GetString("LYPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail to be send to.
        /// </summary>
        internal static string MailGroup {
            get {
                return ResourceManager.GetString("MailGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manager.
        /// </summary>
        internal static string Manager {
            get {
                return ResourceManager.GetString("Manager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer Name.
        /// </summary>
        internal static string ManufacturerName {
            get {
                return ResourceManager.GetString("ManufacturerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturers.
        /// </summary>
        internal static string Manufacturers {
            get {
                return ResourceManager.GetString("Manufacturers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer Supplied.
        /// </summary>
        internal static string ManufacturerSupplied {
            get {
                return ResourceManager.GetString("ManufacturerSupplied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marital Status.
        /// </summary>
        internal static string MaritalStatus {
            get {
                return ResourceManager.GetString("MaritalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark Complete.
        /// </summary>
        internal static string MarkComplete {
            get {
                return ResourceManager.GetString("MarkComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Market Leading.
        /// </summary>
        internal static string MarketLeadingLytica {
            get {
                return ResourceManager.GetString("MarketLeadingLytica", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Country Name.
        /// </summary>
        internal static string MasterCountryName {
            get {
                return ResourceManager.GetString("MasterCountryName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Currency.
        /// </summary>
        internal static string MasterCurrency {
            get {
                return ResourceManager.GetString("MasterCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max Document Size .
        /// </summary>
        internal static string MaxDocumentSize {
            get {
                return ResourceManager.GetString("MaxDocumentSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Max Exposure Credit Limit.
        /// </summary>
        internal static string MaxExpoCreditLimit {
            get {
                return ResourceManager.GetString("MaxExpoCreditLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Character Count (2000 chrs max) : .
        /// </summary>
        internal static string MaxLengthCountingVIew {
            get {
                return ResourceManager.GetString("MaxLengthCountingVIew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum PDF Documents.
        /// </summary>
        internal static string MaxPDFDocuments {
            get {
                return ResourceManager.GetString("MaxPDFDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Maximum images per Stock.
        /// </summary>
        internal static string MaxStockImages {
            get {
                return ResourceManager.GetString("MaxStockImages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Message.
        /// </summary>
        internal static string Message {
            get {
                return ResourceManager.GetString("Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Message Alert?.
        /// </summary>
        internal static string MessageAlertShown {
            get {
                return ResourceManager.GetString("MessageAlertShown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Message check interval.
        /// </summary>
        internal static string MessageCheckTimeout {
            get {
                return ResourceManager.GetString("MessageCheckTimeout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MFR Board Level.
        /// </summary>
        internal static string MFRBoardLevel {
            get {
                return ResourceManager.GetString("MFRBoardLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MFR Label.
        /// </summary>
        internal static string MFRLabel {
            get {
                return ResourceManager.GetString("MFRLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MFR Name Suffix.
        /// </summary>
        internal static string MFRNameSuffix {
            get {
                return ResourceManager.GetString("MFRNameSuffix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Military Use?.
        /// </summary>
        internal static string MilitaryUse {
            get {
                return ResourceManager.GetString("MilitaryUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile.
        /// </summary>
        internal static string Mobile {
            get {
                return ResourceManager.GetString("Mobile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mobile Tel.
        /// </summary>
        internal static string MobileTel {
            get {
                return ResourceManager.GetString("MobileTel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact(Mobile)*.
        /// </summary>
        internal static string Mobile_ {
            get {
                return ResourceManager.GetString("Mobile*", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Minimum Order Quantity (MOQ).
        /// </summary>
        internal static string MOQ {
            get {
                return ResourceManager.GetString("MOQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MSL.
        /// </summary>
        internal static string MSL {
            get {
                return ResourceManager.GetString("MSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MSL.
        /// </summary>
        internal static string MSLLevel {
            get {
                return ResourceManager.GetString("MSLLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MTD GP.
        /// </summary>
        internal static string MTDGp {
            get {
                return ResourceManager.GetString("MTDGp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MTD %.
        /// </summary>
        internal static string MTDPct {
            get {
                return ResourceManager.GetString("MTDPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to N/A.
        /// </summary>
        internal static string NA {
            get {
                return ResourceManager.GetString("NA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name 1.
        /// </summary>
        internal static string Name1 {
            get {
                return ResourceManager.GetString("Name1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name 2.
        /// </summary>
        internal static string Name2 {
            get {
                return ResourceManager.GetString("Name2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Narrative.
        /// </summary>
        internal static string Narrative {
            get {
                return ResourceManager.GetString("Narrative", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Landed Cost.
        /// </summary>
        internal static string NewLandedCost {
            get {
                return ResourceManager.GetString("NewLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New location.
        /// </summary>
        internal static string NewLocation {
            get {
                return ResourceManager.GetString("NewLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Lot.
        /// </summary>
        internal static string NewLot {
            get {
                return ResourceManager.GetString("NewLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Password.
        /// </summary>
        internal static string NewPassword {
            get {
                return ResourceManager.GetString("NewPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Stock Provision.
        /// </summary>
        internal static string NewStockProvision {
            get {
                return ResourceManager.GetString("NewStockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New User.
        /// </summary>
        internal static string NewUser {
            get {
                return ResourceManager.GetString("NewUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Label Path.
        /// </summary>
        internal static string NiceLabelPath {
            get {
                return ResourceManager.GetString("NiceLabelPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nickname.
        /// </summary>
        internal static string Nickname {
            get {
                return ResourceManager.GetString("Nickname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No-Bid Notes.
        /// </summary>
        internal static string NoBidNotes {
            get {
                return ResourceManager.GetString("NoBidNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Non Preferred Company.
        /// </summary>
        internal static string NonPreferredCompany {
            get {
                return ResourceManager.GetString("NonPreferredCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes to Print on Invoice.
        /// </summary>
        internal static string NotesForInv {
            get {
                return ResourceManager.GetString("NotesForInv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes to Invoice.
        /// </summary>
        internal static string NotesToInvoice {
            get {
                return ResourceManager.GetString("NotesToInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes To Warehouse.
        /// </summary>
        internal static string NotesToWarehouse {
            get {
                return ResourceManager.GetString("NotesToWarehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Exported.
        /// </summary>
        internal static string NotExported {
            get {
                return ResourceManager.GetString("NotExported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Exported Invoices.
        /// </summary>
        internal static string notexportedinvoices {
            get {
                return ResourceManager.GetString("notexportedinvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the New Requirement.
        /// </summary>
        internal static string NotifyothersoftheNewReq {
            get {
                return ResourceManager.GetString("NotifyothersoftheNewReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify Salesperson?.
        /// </summary>
        internal static string NotifySales {
            get {
                return ResourceManager.GetString("NotifySales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Number.
        /// </summary>
        internal static string NprNo {
            get {
                return ResourceManager.GetString("NprNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Printed.
        /// </summary>
        internal static string NPRPrinted {
            get {
                return ResourceManager.GetString("NPRPrinted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Raised Date From.
        /// </summary>
        internal static string NprRaisedDateFrom {
            get {
                return ResourceManager.GetString("NprRaisedDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Raised Date To.
        /// </summary>
        internal static string NprRaisedDateTo {
            get {
                return ResourceManager.GetString("NprRaisedDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  NPR Report.
        /// </summary>
        internal static string NPRReport {
            get {
                return ResourceManager.GetString("NPRReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Children.
        /// </summary>
        internal static string NumberChildren {
            get {
                return ResourceManager.GetString("NumberChildren", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Packs.
        /// </summary>
        internal static string NumberOfPacks {
            get {
                return ResourceManager.GetString("NumberOfPacks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recently viewed pages.
        /// </summary>
        internal static string NumberRecentlyViewedPages {
            get {
                return ResourceManager.GetString("NumberRecentlyViewedPages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Purchase Order.
        /// </summary>
        internal static string NumOfPO {
            get {
                return ResourceManager.GetString("NumOfPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Obsolete.
        /// </summary>
        internal static string Obsolete {
            get {
                return ResourceManager.GetString("Obsolete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto-Imported OCR Invoices.
        /// </summary>
        internal static string OCRGEN {
            get {
                return ResourceManager.GetString("OCRGEN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offer Date.
        /// </summary>
        internal static string OfferDate {
            get {
                return ResourceManager.GetString("OfferDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offer Price.
        /// </summary>
        internal static string OfferPrice {
            get {
                return ResourceManager.GetString("OfferPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offer Status.
        /// </summary>
        internal static string OfferStatus {
            get {
                return ResourceManager.GetString("OfferStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL.
        /// </summary>
        internal static string OGEL {
            get {
                return ResourceManager.GetString("OGEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination Country.
        /// </summary>
        internal static string OGELDestinationCountry {
            get {
                return ResourceManager.GetString("OGELDestinationCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End User.
        /// </summary>
        internal static string OgelEndUser {
            get {
                return ResourceManager.GetString("OgelEndUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EUU Form.
        /// </summary>
        internal static string OGELEUUForm {
            get {
                return ResourceManager.GetString("OGELEUUForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Lines.
        /// </summary>
        internal static string OGELLines {
            get {
                return ResourceManager.GetString("OGELLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Military Use?.
        /// </summary>
        internal static string OgelMilitaryUse {
            get {
                return ResourceManager.GetString("OgelMilitaryUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL No.
        /// </summary>
        internal static string OGELNo {
            get {
                return ResourceManager.GetString("OGELNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Number.
        /// </summary>
        internal static string OgelNumber {
            get {
                return ResourceManager.GetString("OgelNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Person.
        /// </summary>
        internal static string OGELSalesPerson {
            get {
                return ResourceManager.GetString("OGELSalesPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Line Number.
        /// </summary>
        internal static string OGELSOLine {
            get {
                return ResourceManager.GetString("OGELSOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old Password.
        /// </summary>
        internal static string OldPassword {
            get {
                return ResourceManager.GetString("OldPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Old User.
        /// </summary>
        internal static string OldUser {
            get {
                return ResourceManager.GetString("OldUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Only From IPO.
        /// </summary>
        internal static string OnlyFromIPO {
            get {
                return ResourceManager.GetString("OnlyFromIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Order.
        /// </summary>
        internal static string OnOrder {
            get {
                return ResourceManager.GetString("OnOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Stop?.
        /// </summary>
        internal static string OnStop {
            get {
                return ResourceManager.GetString("OnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Landed Cost.
        /// </summary>
        internal static string OpenCost {
            get {
                return ResourceManager.GetString("OpenCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Freight Charges.
        /// </summary>
        internal static string OpenFreight {
            get {
                return ResourceManager.GetString("OpenFreight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open GP (%).
        /// </summary>
        internal static string OpenGpPct {
            get {
                return ResourceManager.GetString("OpenGpPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Purchase Orders.
        /// </summary>
        internal static string OpenPOs {
            get {
                return ResourceManager.GetString("OpenPOs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Sales Value.
        /// </summary>
        internal static string OpenSales {
            get {
                return ResourceManager.GetString("OpenSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Sales Orders.
        /// </summary>
        internal static string OpenSOs {
            get {
                return ResourceManager.GetString("OpenSOs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Posted Sales Order Total.
        /// </summary>
        internal static string OpenSOTotal {
            get {
                return ResourceManager.GetString("OpenSOTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOs sent to customer only.
        /// </summary>
        internal static string OrderSenttoCust {
            get {
                return ResourceManager.GetString("OrderSenttoCust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order To Place.
        /// </summary>
        internal static string OrderToPlace {
            get {
                return ResourceManager.GetString("OrderToPlace", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Order Value.
        /// </summary>
        internal static string OrderValue {
            get {
                return ResourceManager.GetString("OrderValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Original Offer Date.
        /// </summary>
        internal static string OriginalOfferDate {
            get {
                return ResourceManager.GetString("OriginalOfferDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Original Offer Price.
        /// </summary>
        internal static string OriginalOfferPrice {
            get {
                return ResourceManager.GetString("OriginalOfferPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Original Offer Supplier.
        /// </summary>
        internal static string OriginalOfferSupplier {
            get {
                return ResourceManager.GetString("OriginalOfferSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outstanding Invoices.
        /// </summary>
        internal static string OutstandingInvoices {
            get {
                return ResourceManager.GetString("OutstandingInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overdue Purchase Orders.
        /// </summary>
        internal static string OverduePOs {
            get {
                return ResourceManager.GetString("OverduePOs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overdue Sales Orders.
        /// </summary>
        internal static string OverdueSOs {
            get {
                return ResourceManager.GetString("OverdueSOs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revert to company header on invoice?.
        /// </summary>
        internal static string OverrideInvoiceHeader {
            get {
                return ResourceManager.GetString("OverrideInvoiceHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data is visible to other companies?.
        /// </summary>
        internal static string OwnDataVisibleToOthers {
            get {
                return ResourceManager.GetString("OwnDataVisibleToOthers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package.
        /// </summary>
        internal static string Package {
            get {
                return ResourceManager.GetString("Package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pack..
        /// </summary>
        internal static string PackageAbbreviation {
            get {
                return ResourceManager.GetString("PackageAbbreviation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query - Packaging.
        /// </summary>
        internal static string PackageBreakdownInfo {
            get {
                return ResourceManager.GetString("PackageBreakdownInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Code.
        /// </summary>
        internal static string PackageCode {
            get {
                return ResourceManager.GetString("PackageCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Type.
        /// </summary>
        internal static string PackageType {
            get {
                return ResourceManager.GetString("PackageType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Unit.
        /// </summary>
        internal static string PackageUnit {
            get {
                return ResourceManager.GetString("PackageUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Breakdown.
        /// </summary>
        internal static string PackagingBreakdown {
            get {
                return ResourceManager.GetString("PackagingBreakdown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Size.
        /// </summary>
        internal static string PackagingSize {
            get {
                return ResourceManager.GetString("PackagingSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Slip.
        /// </summary>
        internal static string PackagingSlip {
            get {
                return ResourceManager.GetString("PackagingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Total:.
        /// </summary>
        internal static string PackagingTotal {
            get {
                return ResourceManager.GetString("PackagingTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging Type.
        /// </summary>
        internal static string PackagingType {
            get {
                return ResourceManager.GetString("PackagingType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging.
        /// </summary>
        internal static string Packing {
            get {
                return ResourceManager.GetString("Packing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pack Size.
        /// </summary>
        internal static string PackSize {
            get {
                return ResourceManager.GetString("PackSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid.
        /// </summary>
        internal static string Paid {
            get {
                return ResourceManager.GetString("Paid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid Only.
        /// </summary>
        internal static string PaidOnly {
            get {
                return ResourceManager.GetString("PaidOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent Company.
        /// </summary>
        internal static string ParentCompany {
            get {
                return ResourceManager.GetString("ParentCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parent Requirement No.
        /// </summary>
        internal static string ParentRequirementNo {
            get {
                return ResourceManager.GetString("ParentRequirementNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part.
        /// </summary>
        internal static string Part {
            get {
                return ResourceManager.GetString("Part", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Application:.
        /// </summary>
        internal static string PartApplication {
            get {
                return ResourceManager.GetString("PartApplication", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Details.
        /// </summary>
        internal static string PartDetails {
            get {
                return ResourceManager.GetString("PartDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Markings.
        /// </summary>
        internal static string PartMarkings {
            get {
                return ResourceManager.GetString("PartMarkings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MPN Quoted.
        /// </summary>
        internal static string PartMPNQuoted {
            get {
                return ResourceManager.GetString("PartMPNQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partner.
        /// </summary>
        internal static string Partner {
            get {
                return ResourceManager.GetString("Partner", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partner Birthday.
        /// </summary>
        internal static string PartnerBirthday {
            get {
                return ResourceManager.GetString("PartnerBirthday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No.
        /// </summary>
        internal static string PartNo {
            get {
                return ResourceManager.GetString("PartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Number.
        /// </summary>
        internal static string PartNumber {
            get {
                return ResourceManager.GetString("PartNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Of.
        /// </summary>
        internal static string PartOf {
            get {
                return ResourceManager.GetString("PartOf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Status.
        /// </summary>
        internal static string PartStatus {
            get {
                return ResourceManager.GetString("PartStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Will the parts be tested and where (Please also advise Company Test House name).
        /// </summary>
        internal static string PartTested {
            get {
                return ResourceManager.GetString("PartTested", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PartWatch.
        /// </summary>
        internal static string PartWatch {
            get {
                return ResourceManager.GetString("PartWatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PartWatch Match.
        /// </summary>
        internal static string PartWatchMatch {
            get {
                return ResourceManager.GetString("PartWatchMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pass.
        /// </summary>
        internal static string Pass {
            get {
                return ResourceManager.GetString("Pass", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password.
        /// </summary>
        internal static string Password {
            get {
                return ResourceManager.GetString("Password", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Password*.
        /// </summary>
        internal static string Password_ {
            get {
                return ResourceManager.GetString("Password*", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment Terms.
        /// </summary>
        internal static string PaymentTerms {
            get {
                return ResourceManager.GetString("PaymentTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to %.
        /// </summary>
        internal static string Pct {
            get {
                return ResourceManager.GetString("Pct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDF Report Required.
        /// </summary>
        internal static string PDFReportRequired {
            get {
                return ResourceManager.GetString("PDFReportRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permission Value.
        /// </summary>
        internal static string PermissionValue {
            get {
                return ResourceManager.GetString("PermissionValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal Address.
        /// </summary>
        internal static string PersonalAddress {
            get {
                return ResourceManager.GetString("PersonalAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Personal Address Name.
        /// </summary>
        internal static string PersonalAddressName {
            get {
                return ResourceManager.GetString("PersonalAddressName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick Up.
        /// </summary>
        internal static string PickUp {
            get {
                return ResourceManager.GetString("PickUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Delivery Date.
        /// </summary>
        internal static string PoDeliveryDate {
            get {
                return ResourceManager.GetString("PoDeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PoHub Only.
        /// </summary>
        internal static string PohubOnly {
            get {
                return ResourceManager.GetString("PohubOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes to warehouse.
        /// </summary>
        internal static string POInternalNotes {
            get {
                return ResourceManager.GetString("POInternalNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Line Count.
        /// </summary>
        internal static string POLineCount {
            get {
                return ResourceManager.GetString("POLineCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO line notes.
        /// </summary>
        internal static string POLineNotes {
            get {
                return ResourceManager.GetString("POLineNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Number.
        /// </summary>
        internal static string PONumber {
            get {
                return ResourceManager.GetString("PONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request No.
        /// </summary>
        internal static string POQuoteNo {
            get {
                return ResourceManager.GetString("POQuoteNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instruction to Warehouse.
        /// </summary>
        internal static string POReceivingNotes {
            get {
                return ResourceManager.GetString("POReceivingNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Postcode*.
        /// </summary>
        internal static string Postcode {
            get {
                return ResourceManager.GetString("Postcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Posted.
        /// </summary>
        internal static string Posted {
            get {
                return ResourceManager.GetString("Posted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes to supplier.
        /// </summary>
        internal static string POSupplierNotes {
            get {
                return ResourceManager.GetString("POSupplierNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Power BI Password.
        /// </summary>
        internal static string PowerBIPassword {
            get {
                return ResourceManager.GetString("PowerBIPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Power BI Username.
        /// </summary>
        internal static string PowerBIUserName {
            get {
                return ResourceManager.GetString("PowerBIUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Partial Quantity Acceptable.
        /// </summary>
        internal static string PQA {
            get {
                return ResourceManager.GetString("PQA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Preferred Warehouse.
        /// </summary>
        internal static string PreferredWarehouse {
            get {
                return ResourceManager.GetString("PreferredWarehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Premier Customer.
        /// </summary>
        internal static string PremierCustomer {
            get {
                return ResourceManager.GetString("PremierCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prevent SOR sign off.
        /// </summary>
        internal static string PreventSOR {
            get {
                return ResourceManager.GetString("PreventSOR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Previous Review Date.
        /// </summary>
        internal static string PreviousReviewDate {
            get {
                return ResourceManager.GetString("PreviousReviewDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price.
        /// </summary>
        internal static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string PriceRequest {
            get {
                return ResourceManager.GetString("PriceRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Primary Landed Cost.
        /// </summary>
        internal static string PrimaryLandedCost {
            get {
                return ResourceManager.GetString("PrimaryLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Of Print.
        /// </summary>
        internal static string PrintCount {
            get {
                return ResourceManager.GetString("PrintCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printable Date Code.
        /// </summary>
        internal static string PrintDateCode {
            get {
                return ResourceManager.GetString("PrintDateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printed line notes to invoice.
        /// </summary>
        internal static string PrintedLineNotes {
            get {
                return ResourceManager.GetString("PrintedLineNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer.
        /// </summary>
        internal static string Printer {
            get {
                return ResourceManager.GetString("Printer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer.
        /// </summary>
        internal static string PrinterName {
            get {
                return ResourceManager.GetString("PrinterName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print hazardous warning.
        /// </summary>
        internal static string PrintHazWarning {
            get {
                return ResourceManager.GetString("PrintHazWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Notes.
        /// </summary>
        internal static string PrintNotes {
            get {
                return ResourceManager.GetString("PrintNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Priority.
        /// </summary>
        internal static string Priority {
            get {
                return ResourceManager.GetString("Priority", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        internal static string Product {
            get {
                return ResourceManager.GetString("Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prod..
        /// </summary>
        internal static string ProductAbbreviation {
            get {
                return ResourceManager.GetString("ProductAbbreviation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier product may need testing.
        /// </summary>
        internal static string ProductReqTesting {
            get {
                return ResourceManager.GetString("ProductReqTesting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Source.
        /// </summary>
        internal static string ProductSource {
            get {
                return ResourceManager.GetString("ProductSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Type.
        /// </summary>
        internal static string ProductType {
            get {
                return ResourceManager.GetString("ProductType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit.
        /// </summary>
        internal static string Profit {
            get {
                return ResourceManager.GetString("Profit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Progress.
        /// </summary>
        internal static string Progress {
            get {
                return ResourceManager.GetString("Progress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Promise Date.
        /// </summary>
        internal static string PromiseDate {
            get {
                return ResourceManager.GetString("PromiseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Promised.
        /// </summary>
        internal static string PromisedDate {
            get {
                return ResourceManager.GetString("PromisedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Promise Reason.
        /// </summary>
        internal static string PromiseReason {
            get {
                return ResourceManager.GetString("PromiseReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Promise Reason Log.
        /// </summary>
        internal static string PromiseReasonLog {
            get {
                return ResourceManager.GetString("PromiseReasonLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospect Type.
        /// </summary>
        internal static string ProspectType {
            get {
                return ResourceManager.GetString("ProspectType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Offer Uploaded By.
        /// </summary>
        internal static string PrOUploadedBy {
            get {
                return ResourceManager.GetString("PrOUploadedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country of Purchase.
        /// </summary>
        internal static string PurchaseCountry {
            get {
                return ResourceManager.GetString("PurchaseCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order.
        /// </summary>
        internal static string PurchaseOrder {
            get {
                return ResourceManager.GetString("PurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order.
        /// </summary>
        internal static string PurchaseOrderNo {
            get {
                return ResourceManager.GetString("PurchaseOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Number.
        /// </summary>
        internal static string PurchaseOrderNoNPR {
            get {
                return ResourceManager.GetString("PurchaseOrderNoNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Price.
        /// </summary>
        internal static string PurchasePrice {
            get {
                return ResourceManager.GetString("PurchasePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request.
        /// </summary>
        internal static string PurchaseQuote {
            get {
                return ResourceManager.GetString("PurchaseQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request Date.
        /// </summary>
        internal static string PurchaseQuoteDate {
            get {
                return ResourceManager.GetString("PurchaseQuoteDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Tax Code.
        /// </summary>
        internal static string PurchaseTaxCode {
            get {
                return ResourceManager.GetString("PurchaseTaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PVV Question.
        /// </summary>
        internal static string PVVQuestion {
            get {
                return ResourceManager.GetString("PVVQuestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Control Notes.
        /// </summary>
        internal static string QCNotes {
            get {
                return ResourceManager.GetString("QCNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sent To.
        /// </summary>
        internal static string QMApprovalName {
            get {
                return ResourceManager.GetString("QMApprovalName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date and Time.
        /// </summary>
        internal static string QMApprovedDate {
            get {
                return ResourceManager.GetString("QMApprovedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Department.
        /// </summary>
        internal static string QMDepartment {
            get {
                return ResourceManager.GetString("QMDepartment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchasing Approval.
        /// </summary>
        internal static string QMIsPurchassingApproved {
            get {
                return ResourceManager.GetString("QMIsPurchassingApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Approval.
        /// </summary>
        internal static string QMIsQualityApproved {
            get {
                return ResourceManager.GetString("QMIsQualityApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Approval.
        /// </summary>
        internal static string QMIsSalesApproved {
            get {
                return ResourceManager.GetString("QMIsSalesApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raised By.
        /// </summary>
        internal static string QMRaisedBy {
            get {
                return ResourceManager.GetString("QMRaisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Approvers:.
        /// </summary>
        internal static string QMSConfigApprover {
            get {
                return ResourceManager.GetString("QMSConfigApprover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Purchasing Approver:.
        /// </summary>
        internal static string QMSCurrentPurchasingApprover {
            get {
                return ResourceManager.GetString("QMSCurrentPurchasingApprover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Sales Approver:.
        /// </summary>
        internal static string QMSCurrentSalesApprover {
            get {
                return ResourceManager.GetString("QMSCurrentSalesApprover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send To:.
        /// </summary>
        internal static string QMSendTO {
            get {
                return ResourceManager.GetString("QMSendTO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invite new Purchasing Approver:.
        /// </summary>
        internal static string QMSInvitePurchasingApprover {
            get {
                return ResourceManager.GetString("QMSInvitePurchasingApprover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invite new Sales Approver:.
        /// </summary>
        internal static string QMSInviteSalesApprover {
            get {
                return ResourceManager.GetString("QMSInviteSalesApprover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        internal static string QMStatus {
            get {
                return ResourceManager.GetString("QMStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify To:.
        /// </summary>
        internal static string QryMsgNotifyTo {
            get {
                return ResourceManager.GetString("QryMsgNotifyTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Received.
        /// </summary>
        internal static string QtyReceived {
            get {
                return ResourceManager.GetString("QtyReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Quantity.
        /// </summary>
        internal static string QtyTobeShpped {
            get {
                return ResourceManager.GetString("QtyTobeShpped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality inbox.
        /// </summary>
        internal static string QualityApprover {
            get {
                return ResourceManager.GetString("QualityApprover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Control Notes.
        /// </summary>
        internal static string QualityControlNotes {
            get {
                return ResourceManager.GetString("QualityControlNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality/scope of supply.
        /// </summary>
        internal static string QualityNotes {
            get {
                return ResourceManager.GetString("QualityNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantities.
        /// </summary>
        internal static string Quantities {
            get {
                return ResourceManager.GetString("Quantities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Advised.
        /// </summary>
        internal static string QuantityAdvised {
            get {
                return ResourceManager.GetString("QuantityAdvised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Allocated.
        /// </summary>
        internal static string QuantityAllocated {
            get {
                return ResourceManager.GetString("QuantityAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Authorised.
        /// </summary>
        internal static string QuantityAuthorised {
            get {
                return ResourceManager.GetString("QuantityAuthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Available.
        /// </summary>
        internal static string QuantityAvailable {
            get {
                return ResourceManager.GetString("QuantityAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Available for Split.
        /// </summary>
        internal static string QuantityAvailableForSplit {
            get {
                return ResourceManager.GetString("QuantityAvailableForSplit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty BackOrder.
        /// </summary>
        internal static string QuantityBackOrder {
            get {
                return ResourceManager.GetString("QuantityBackOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty In Stock.
        /// </summary>
        internal static string QuantityInStock {
            get {
                return ResourceManager.GetString("QuantityInStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty On Order.
        /// </summary>
        internal static string QuantityOnOrder {
            get {
                return ResourceManager.GetString("QuantityOnOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Order.
        /// </summary>
        internal static string QuantityOrder {
            get {
                return ResourceManager.GetString("QuantityOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Ordered.
        /// </summary>
        internal static string QuantityOrdered {
            get {
                return ResourceManager.GetString("QuantityOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Outstanding.
        /// </summary>
        internal static string QuantityOutstanding {
            get {
                return ResourceManager.GetString("QuantityOutstanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Received.
        /// </summary>
        internal static string QuantityReceived {
            get {
                return ResourceManager.GetString("QuantityReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Required.
        /// </summary>
        internal static string QuantityRequired {
            get {
                return ResourceManager.GetString("QuantityRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Shipped.
        /// </summary>
        internal static string QuantityShipped {
            get {
                return ResourceManager.GetString("QuantityShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity to Split.
        /// </summary>
        internal static string QuantityToSplit {
            get {
                return ResourceManager.GetString("QuantityToSplit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Needed.
        /// </summary>
        internal static string Quantity_Needed {
            get {
                return ResourceManager.GetString("Quantity Needed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantine.
        /// </summary>
        internal static string Quarantine {
            get {
                return ResourceManager.GetString("Quarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantined.
        /// </summary>
        internal static string Quarantined {
            get {
                return ResourceManager.GetString("Quarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantine Product.
        /// </summary>
        internal static string QuarantineProduct {
            get {
                return ResourceManager.GetString("QuarantineProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantine this Item?.
        /// </summary>
        internal static string QuarantineThisItem {
            get {
                return ResourceManager.GetString("QuarantineThisItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query - Baking.
        /// </summary>
        internal static string QueryBakingLevel {
            get {
                return ResourceManager.GetString("QueryBakingLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query-Date Code.
        /// </summary>
        internal static string QueryDateCode {
            get {
                return ResourceManager.GetString("QueryDateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approval Status.
        /// </summary>
        internal static string QueryMsgApprovalStatus {
            get {
                return ResourceManager.GetString("QueryMsgApprovalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query- Quantity.
        /// </summary>
        internal static string QueryQuantity {
            get {
                return ResourceManager.GetString("QueryQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote.
        /// </summary>
        internal static string Quote {
            get {
                return ResourceManager.GetString("Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instruction to warehouse.
        /// </summary>
        internal static string QuoteInstructions {
            get {
                return ResourceManager.GetString("QuoteInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote No.
        /// </summary>
        internal static string QuoteNo {
            get {
                return ResourceManager.GetString("QuoteNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printed line notes.
        /// </summary>
        internal static string Quotenotes {
            get {
                return ResourceManager.GetString("Quotenotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Number.
        /// </summary>
        internal static string QuoteNumber {
            get {
                return ResourceManager.GetString("QuoteNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Required .
        /// </summary>
        internal static string QuoteRequired {
            get {
                return ResourceManager.GetString("QuoteRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Status.
        /// </summary>
        internal static string QuoteStatus {
            get {
                return ResourceManager.GetString("QuoteStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Validity Required.
        /// </summary>
        internal static string QuoteValidityRequired {
            get {
                return ResourceManager.GetString("QuoteValidityRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raised By.
        /// </summary>
        internal static string RaisedBy {
            get {
                return ResourceManager.GetString("RaisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate.
        /// </summary>
        internal static string Rate {
            get {
                return ResourceManager.GetString("Rate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rate 2.
        /// </summary>
        internal static string Rate2 {
            get {
                return ResourceManager.GetString("Rate2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rating.
        /// </summary>
        internal static string Rating {
            get {
                return ResourceManager.GetString("Rating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason 1.
        /// </summary>
        internal static string Reason {
            get {
                return ResourceManager.GetString("Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason 2.
        /// </summary>
        internal static string Reason2 {
            get {
                return ResourceManager.GetString("Reason2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rebate Account.
        /// </summary>
        internal static string RebateAccount {
            get {
                return ResourceManager.GetString("RebateAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receipt No.
        /// </summary>
        internal static string ReceiptNo {
            get {
                return ResourceManager.GetString("ReceiptNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received By.
        /// </summary>
        internal static string ReceivedBy {
            get {
                return ResourceManager.GetString("ReceivedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Date.
        /// </summary>
        internal static string ReceivedDate {
            get {
                return ResourceManager.GetString("ReceivedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Date From.
        /// </summary>
        internal static string ReceivedDateFrom {
            get {
                return ResourceManager.GetString("ReceivedDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Date To.
        /// </summary>
        internal static string ReceivedDateTo {
            get {
                return ResourceManager.GetString("ReceivedDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received End Date.
        /// </summary>
        internal static string ReceivedEndDate {
            get {
                return ResourceManager.GetString("ReceivedEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Start Date.
        /// </summary>
        internal static string ReceivedStartDate {
            get {
                return ResourceManager.GetString("ReceivedStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Email?.
        /// </summary>
        internal static string ReceiveEmail {
            get {
                return ResourceManager.GetString("ReceiveEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receiving Instructions.
        /// </summary>
        internal static string ReceivingInstructions {
            get {
                return ResourceManager.GetString("ReceivingInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receiving Notes.
        /// </summary>
        internal static string ReceivingNotes {
            get {
                return ResourceManager.GetString("ReceivingNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal notes.
        /// </summary>
        internal static string ReceivingPOInternalInstructions {
            get {
                return ResourceManager.GetString("ReceivingPOInternalInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes to supplier.
        /// </summary>
        internal static string ReceivingPONotes {
            get {
                return ResourceManager.GetString("ReceivingPONotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instruction to WH &amp; Quality control notes.
        /// </summary>
        internal static string ReceivingPOQualityControlNotes {
            get {
                return ResourceManager.GetString("ReceivingPOQualityControlNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recent Only?.
        /// </summary>
        internal static string RecentOnly {
            get {
                return ResourceManager.GetString("RecentOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping and receiving instructions to wh only.
        /// </summary>
        internal static string RecievingNotes {
            get {
                return ResourceManager.GetString("RecievingNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Record Remaining.
        /// </summary>
        internal static string RecordRemaining {
            get {
                return ResourceManager.GetString("RecordRemaining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Records Processed.
        /// </summary>
        internal static string RecordsProcessed {
            get {
                return ResourceManager.GetString("RecordsProcessed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Records Remaining.
        /// </summary>
        internal static string RecordsRemaining {
            get {
                return ResourceManager.GetString("RecordsRemaining", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received - Manufacturer.
        /// </summary>
        internal static string RecvCorrectManufacturer {
            get {
                return ResourceManager.GetString("RecvCorrectManufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received - MSL.
        /// </summary>
        internal static string RecvCorrectMSL {
            get {
                return ResourceManager.GetString("RecvCorrectMSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received - Packaging Type.
        /// </summary>
        internal static string RecvCorrectPackage {
            get {
                return ResourceManager.GetString("RecvCorrectPackage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received - Part Number.
        /// </summary>
        internal static string RecvCorrectPartNo {
            get {
                return ResourceManager.GetString("RecvCorrectPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received - ROHS Status.
        /// </summary>
        internal static string RecvCorrectRohsStatus {
            get {
                return ResourceManager.GetString("RecvCorrectRohsStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference.
        /// </summary>
        internal static string Reference {
            get {
                return ResourceManager.GetString("Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference Date.
        /// </summary>
        internal static string ReferenceDate {
            get {
                return ResourceManager.GetString("ReferenceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refurbs Acceptable.
        /// </summary>
        internal static string RefirbsAcceptable {
            get {
                return ResourceManager.GetString("RefirbsAcceptable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference No.
        /// </summary>
        internal static string RefNo {
            get {
                return ResourceManager.GetString("RefNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region.
        /// </summary>
        internal static string Region {
            get {
                return ResourceManager.GetString("Region", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Region Name.
        /// </summary>
        internal static string RegionName {
            get {
                return ResourceManager.GetString("RegionName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Related To Dos.
        /// </summary>
        internal static string RelatedToDos {
            get {
                return ResourceManager.GetString("RelatedToDos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Released by.
        /// </summary>
        internal static string Releasedby {
            get {
                return ResourceManager.GetString("Releasedby", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reminder.
        /// </summary>
        internal static string Reminder {
            get {
                return ResourceManager.GetString("Reminder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reminder Date.
        /// </summary>
        internal static string ReminderDate {
            get {
                return ResourceManager.GetString("ReminderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reminder Text.
        /// </summary>
        internal static string ReminderText {
            get {
                return ResourceManager.GetString("ReminderText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reminder Time.
        /// </summary>
        internal static string ReminderTime {
            get {
                return ResourceManager.GetString("ReminderTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Regular/Repeat business.
        /// </summary>
        internal static string RepeatBusiness {
            get {
                return ResourceManager.GetString("RepeatBusiness", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Repeat Order.
        /// </summary>
        internal static string RepeatOrder {
            get {
                return ResourceManager.GetString("RepeatOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reply To.
        /// </summary>
        internal static string ReplyTo {
            get {
                return ResourceManager.GetString("ReplyTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reprice Open Orders?.
        /// </summary>
        internal static string RepriceOpenOrders {
            get {
                return ResourceManager.GetString("RepriceOpenOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Name.
        /// </summary>
        internal static string ReqBOMName {
            get {
                return ResourceManager.GetString("ReqBOMName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot Code required.
        /// </summary>
        internal static string ReqLotNo {
            get {
                return ResourceManager.GetString("ReqLotNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial No. required.
        /// </summary>
        internal static string ReqSerailNo {
            get {
                return ResourceManager.GetString("ReqSerailNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to REQ Line Status.
        /// </summary>
        internal static string REQStatus {
            get {
                return ResourceManager.GetString("REQStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requested by.
        /// </summary>
        internal static string Requestedby {
            get {
                return ResourceManager.GetString("Requestedby", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requestor Notes.
        /// </summary>
        internal static string RequesterNote {
            get {
                return ResourceManager.GetString("RequesterNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Required.
        /// </summary>
        internal static string RequiredDate {
            get {
                return ResourceManager.GetString("RequiredDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Required End Date.
        /// </summary>
        internal static string RequiredEndDate {
            get {
                return ResourceManager.GetString("RequiredEndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Required Start Date.
        /// </summary>
        internal static string RequiredStartDate {
            get {
                return ResourceManager.GetString("RequiredStartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirement for Traceability.
        /// </summary>
        internal static string RequirementforTraceability {
            get {
                return ResourceManager.GetString("RequirementforTraceability", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirement No.
        /// </summary>
        internal static string RequirementNo {
            get {
                return ResourceManager.GetString("RequirementNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resale Price.
        /// </summary>
        internal static string ResalePrice {
            get {
                return ResourceManager.GetString("ResalePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Quantity?.
        /// </summary>
        internal static string ResetQuantity {
            get {
                return ResourceManager.GetString("ResetQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return Date.
        /// </summary>
        internal static string ReturnDate {
            get {
                return ResourceManager.GetString("ReturnDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task for Review.
        /// </summary>
        internal static string Review {
            get {
                return ResourceManager.GetString("Review", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review Date.
        /// </summary>
        internal static string ReviewDate {
            get {
                return ResourceManager.GetString("ReviewDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task for Review.
        /// </summary>
        internal static string ReviewOnly {
            get {
                return ResourceManager.GetString("ReviewOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RFQ Closing Date.
        /// </summary>
        internal static string RFQClosingDate {
            get {
                return ResourceManager.GetString("RFQClosingDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RMA Date.
        /// </summary>
        internal static string RMADate {
            get {
                return ResourceManager.GetString("RMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RoHS.
        /// </summary>
        internal static string RoHS {
            get {
                return ResourceManager.GetString("RoHS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Status (Y/N).
        /// </summary>
        internal static string ROHSStatus {
            get {
                return ResourceManager.GetString("ROHSStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Status.
        /// </summary>
        internal static string RohsStatusGI {
            get {
                return ResourceManager.GetString("RohsStatusGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rohs (Y/N).
        /// </summary>
        internal static string ROHSYN {
            get {
                return ResourceManager.GetString("ROHSYN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Cause.
        /// </summary>
        internal static string RootCause {
            get {
                return ResourceManager.GetString("RootCause", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Orders in Last 12 months.
        /// </summary>
        internal static string SAApprovedOrder {
            get {
                return ResourceManager.GetString("SAApprovedOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Line Manager.
        /// </summary>
        internal static string SAChooseLineManger {
            get {
                return ResourceManager.GetString("SAChooseLineManger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Device Pictures.
        /// </summary>
        internal static string SADevicePicture {
            get {
                return ResourceManager.GetString("SADevicePicture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT Client For PO.
        /// </summary>
        internal static string SAGTClintPo {
            get {
                return ResourceManager.GetString("SAGTClintPo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ/Quote No.
        /// </summary>
        internal static string SaHubRFQQuote {
            get {
                return ResourceManager.GetString("SaHubRFQQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales CC.
        /// </summary>
        internal static string SalesCC {
            get {
                return ResourceManager.GetString("SalesCC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Contact.
        /// </summary>
        internal static string SalesContact {
            get {
                return ResourceManager.GetString("SalesContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Sales.
        /// </summary>
        internal static string SalesDivision {
            get {
                return ResourceManager.GetString("SalesDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson.
        /// </summary>
        internal static string Salesman {
            get {
                return ResourceManager.GetString("Salesman", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional salesperson %.
        /// </summary>
        internal static string Salesman2Percent {
            get {
                return ResourceManager.GetString("Salesman2Percent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson.
        /// </summary>
        internal static string SalesmanName {
            get {
                return ResourceManager.GetString("SalesmanName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order.
        /// </summary>
        internal static string SalesOrder {
            get {
                return ResourceManager.GetString("SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order.
        /// </summary>
        internal static string SalesOrderNo {
            get {
                return ResourceManager.GetString("SalesOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Number.
        /// </summary>
        internal static string SalesOrderNumber {
            get {
                return ResourceManager.GetString("SalesOrderNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Support Team Member.
        /// </summary>
        internal static string SalesPersion {
            get {
                return ResourceManager.GetString("SalesPersion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson.
        /// </summary>
        internal static string Salesperson {
            get {
                return ResourceManager.GetString("Salesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional Salesperson.
        /// </summary>
        internal static string Salesperson2 {
            get {
                return ResourceManager.GetString("Salesperson2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Manager Notes.
        /// </summary>
        internal static string SALineManagerNote {
            get {
                return ResourceManager.GetString("SALineManagerNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturers Label Pictures.
        /// </summary>
        internal static string SAManufacturersPictures {
            get {
                return ResourceManager.GetString("SAManufacturersPictures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Margin.
        /// </summary>
        internal static string SAMargin {
            get {
                return ResourceManager.GetString("SAMargin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Has the Part been reported on ERAI.
        /// </summary>
        internal static string SAPartERAI {
            get {
                return ResourceManager.GetString("SAPartERAI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to POs in the last 12 months.
        /// </summary>
        internal static string SAPOCount {
            get {
                return ResourceManager.GetString("SAPOCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Defined Vendor.
        /// </summary>
        internal static string SAPrecogsSupplier {
            get {
                return ResourceManager.GetString("SAPrecogsSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchasing Method.
        /// </summary>
        internal static string SAPurchasingMethod {
            get {
                return ResourceManager.GetString("SAPurchasingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Approval Notes.
        /// </summary>
        internal static string SAQualityNote {
            get {
                return ResourceManager.GetString("SAQualityNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rebound Purchaser (Division).
        /// </summary>
        internal static string SAreboundPurchaser {
            get {
                return ResourceManager.GetString("SAreboundPurchaser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RMAs in the last 12 months.
        /// </summary>
        internal static string SASupplierRMACount {
            get {
                return ResourceManager.GetString("SASupplierRMACount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Terms and Condition of purchase.pdf.
        /// </summary>
        internal static string SATermAndCondetion {
            get {
                return ResourceManager.GetString("SATermAndCondetion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Traceability Pictures.
        /// </summary>
        internal static string SATraceabilityPictures {
            get {
                return ResourceManager.GetString("SATraceabilityPictures", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Reference 1.
        /// </summary>
        internal static string SATradeRefOne {
            get {
                return ResourceManager.GetString("SATradeRefOne", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Reference 3.
        /// </summary>
        internal static string SATradeRefThree {
            get {
                return ResourceManager.GetString("SATradeRefThree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trade Reference 2.
        /// </summary>
        internal static string SATradeRefTwo {
            get {
                return ResourceManager.GetString("SATradeRefTwo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Term &amp; Condition Attachment .
        /// </summary>
        internal static string SATrmAndCndtnAttchmnt {
            get {
                return ResourceManager.GetString("SATrmAndCndtnAttchmnt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Line Manager.
        /// </summary>
        internal static string SAUpdateLineManager {
            get {
                return ResourceManager.GetString("SAUpdateLineManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Confirm.
        /// </summary>
        internal static string SaveConfirm {
            get {
                return ResourceManager.GetString("SaveConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save searches by default?.
        /// </summary>
        internal static string SaveDLNState {
            get {
                return ResourceManager.GetString("SaveDLNState", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warranty Period.
        /// </summary>
        internal static string SAWarrantyperiod {
            get {
                return ResourceManager.GetString("SAWarrantyperiod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Franchise Weblink or evidence.
        /// </summary>
        internal static string SAWeblinkEvidence {
            get {
                return ResourceManager.GetString("SAWeblinkEvidence", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scheduled Call.
        /// </summary>
        internal static string ScheduledCall {
            get {
                return ResourceManager.GetString("ScheduledCall", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to For.
        /// </summary>
        internal static string ScheduledCallFor {
            get {
                return ResourceManager.GetString("ScheduledCallFor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sealed.
        /// </summary>
        internal static string Sealed {
            get {
                return ResourceManager.GetString("Sealed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No.
        /// </summary>
        internal static string SearchtxtPartNo {
            get {
                return ResourceManager.GetString("SearchtxtPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search Type.
        /// </summary>
        internal static string SearchType {
            get {
                return ResourceManager.GetString("SearchType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Second Ref.
        /// </summary>
        internal static string SecondRef {
            get {
                return ResourceManager.GetString("SecondRef", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Group Name.
        /// </summary>
        internal static string SecurityGroupName {
            get {
                return ResourceManager.GetString("SecurityGroupName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Group.
        /// </summary>
        internal static string SecurityGroupNo {
            get {
                return ResourceManager.GetString("SecurityGroupNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Page Code.
        /// </summary>
        internal static string SecurityPageCode {
            get {
                return ResourceManager.GetString("SecurityPageCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Security Page Function .
        /// </summary>
        internal static string SecurityPageFunctionNo {
            get {
                return ResourceManager.GetString("SecurityPageFunctionNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select All Image.
        /// </summary>
        internal static string SelectAllImage {
            get {
                return ResourceManager.GetString("SelectAllImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select the client.
        /// </summary>
        internal static string SelectClient {
            get {
                return ResourceManager.GetString("SelectClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected.
        /// </summary>
        internal static string Selected {
            get {
                return ResourceManager.GetString("Selected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Companies.
        /// </summary>
        internal static string SelectedCompanies {
            get {
                return ResourceManager.GetString("SelectedCompanies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Code List.
        /// </summary>
        internal static string SelectedECCNCode {
            get {
                return ResourceManager.GetString("SelectedECCNCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected GI Value.
        /// </summary>
        internal static string SelectedGI {
            get {
                return ResourceManager.GetString("SelectedGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Groups.
        /// </summary>
        internal static string SelectedGroups {
            get {
                return ResourceManager.GetString("SelectedGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected Logins.
        /// </summary>
        internal static string SelectedLogins {
            get {
                return ResourceManager.GetString("SelectedLogins", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unmapped product list.
        /// </summary>
        internal static string SelectedProduct {
            get {
                return ResourceManager.GetString("SelectedProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Ship Value.
        /// </summary>
        internal static string SelectedShip {
            get {
                return ResourceManager.GetString("SelectedShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select IndustryType.
        /// </summary>
        internal static string SelectIndustryType {
            get {
                return ResourceManager.GetString("SelectIndustryType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Item.
        /// </summary>
        internal static string SelectItem {
            get {
                return ResourceManager.GetString("SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Master Country.
        /// </summary>
        internal static string SelectMasterCountry {
            get {
                return ResourceManager.GetString("SelectMasterCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Master Currency.
        /// </summary>
        internal static string SelectMasterCurrency {
            get {
                return ResourceManager.GetString("SelectMasterCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a New or an Existing Header.
        /// </summary>
        internal static string SelectNewOrExistingHeader {
            get {
                return ResourceManager.GetString("SelectNewOrExistingHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Source.
        /// </summary>
        internal static string SelectSource {
            get {
                return ResourceManager.GetString("SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Target.
        /// </summary>
        internal static string SelectTarget {
            get {
                return ResourceManager.GetString("SelectTarget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell.
        /// </summary>
        internal static string Sell {
            get {
                return ResourceManager.GetString("Sell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Currency.
        /// </summary>
        internal static string SellCurrency {
            get {
                return ResourceManager.GetString("SellCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selling Price.
        /// </summary>
        internal static string SellPrice {
            get {
                return ResourceManager.GetString("SellPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Ship Via.
        /// </summary>
        internal static string SellShipVia {
            get {
                return ResourceManager.GetString("SellShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Ship Via Account.
        /// </summary>
        internal static string SellShipViaAccount {
            get {
                return ResourceManager.GetString("SellShipViaAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Ship Via No.
        /// </summary>
        internal static string SellShipViaNo {
            get {
                return ResourceManager.GetString("SellShipViaNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allow Email Sending on UAT.
        /// </summary>
        internal static string SendEmail {
            get {
                return ResourceManager.GetString("SendEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send to IPO Buyer.
        /// </summary>
        internal static string SendMailToClientBuyer {
            get {
                return ResourceManager.GetString("SendMailToClientBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send to IPO Support.
        /// </summary>
        internal static string SendMailToClientSupport {
            get {
                return ResourceManager.GetString("SendMailToClientSupport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send to PO Buyer.
        /// </summary>
        internal static string SendMailToHubBuyer {
            get {
                return ResourceManager.GetString("SendMailToHubBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send to PO Support.
        /// </summary>
        internal static string SendMailToHubSupport {
            get {
                return ResourceManager.GetString("SendMailToHubSupport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send email to IPO Purchasing.
        /// </summary>
        internal static string SendMailToIPO {
            get {
                return ResourceManager.GetString("SendMailToIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send email to sales and Support Team Member.
        /// </summary>
        internal static string SendMailToSales {
            get {
                return ResourceManager.GetString("SendMailToSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send To Group.
        /// </summary>
        internal static string SendToGroup {
            get {
                return ResourceManager.GetString("SendToGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial Number.
        /// </summary>
        internal static string SerialNo {
            get {
                return ResourceManager.GetString("SerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial Nos Recorded?.
        /// </summary>
        internal static string SerialNosRecorded {
            get {
                return ResourceManager.GetString("SerialNosRecorded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        internal static string Service {
            get {
                return ResourceManager.GetString("Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship ASAP.
        /// </summary>
        internal static string ShipASAP {
            get {
                return ResourceManager.GetString("ShipASAP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Ship In Cost.
        /// </summary>
        internal static string ShipCost {
            get {
                return ResourceManager.GetString("ShipCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Freight Charges.
        /// </summary>
        internal static string ShipFreight {
            get {
                return ResourceManager.GetString("ShipFreight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship From.
        /// </summary>
        internal static string ShipFrom {
            get {
                return ResourceManager.GetString("ShipFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship From Country.
        /// </summary>
        internal static string ShipFromCountry {
            get {
                return ResourceManager.GetString("ShipFromCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship From Warehouse:.
        /// </summary>
        internal static string ShipFromWarehouse {
            get {
                return ResourceManager.GetString("ShipFromWarehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped GP (%).
        /// </summary>
        internal static string ShipGpPct {
            get {
                return ResourceManager.GetString("ShipGpPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship In Cost.
        /// </summary>
        internal static string ShipInCost {
            get {
                return ResourceManager.GetString("ShipInCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship In Cost History.
        /// </summary>
        internal static string ShipInCostHis {
            get {
                return ResourceManager.GetString("ShipInCostHis", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts to be shipped:.
        /// </summary>
        internal static string ShipmentsListTitle {
            get {
                return ResourceManager.GetString("ShipmentsListTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped.
        /// </summary>
        internal static string Shipped {
            get {
                return ResourceManager.GetString("Shipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped By.
        /// </summary>
        internal static string ShippedBy {
            get {
                return ResourceManager.GetString("ShippedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped From.
        /// </summary>
        internal static string ShippedFrom {
            get {
                return ResourceManager.GetString("ShippedFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipper.
        /// </summary>
        internal static string Shipper {
            get {
                return ResourceManager.GetString("Shipper", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping.
        /// </summary>
        internal static string Shipping {
            get {
                return ResourceManager.GetString("Shipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping A/C.
        /// </summary>
        internal static string ShippingAccountNo {
            get {
                return ResourceManager.GetString("ShippingAccountNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Cost.
        /// </summary>
        internal static string ShippingCost {
            get {
                return ResourceManager.GetString("ShippingCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Instruction.
        /// </summary>
        internal static string ShippingInstruction {
            get {
                return ResourceManager.GetString("ShippingInstruction", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Instr.
        /// </summary>
        internal static string ShippingInstructions {
            get {
                return ResourceManager.GetString("ShippingInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Notes.
        /// </summary>
        internal static string ShippingNotes {
            get {
                return ResourceManager.GetString("ShippingNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes for packing slip and acknowledgement.
        /// </summary>
        internal static string ShippingNotesAck {
            get {
                return ResourceManager.GetString("ShippingNotesAck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Note to WH only.
        /// </summary>
        internal static string ShippingNoteToWHOnly {
            get {
                return ResourceManager.GetString("ShippingNoteToWHOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Soon?.
        /// </summary>
        internal static string ShippingSoon {
            get {
                return ResourceManager.GetString("ShippingSoon", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Status.
        /// </summary>
        internal static string ShippingStatus {
            get {
                return ResourceManager.GetString("ShippingStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Surcharge.
        /// </summary>
        internal static string ShippingSurcharge {
            get {
                return ResourceManager.GetString("ShippingSurcharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Sales Value.
        /// </summary>
        internal static string ShipSales {
            get {
                return ResourceManager.GetString("ShipSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping instruction.
        /// </summary>
        internal static string ShipSOInstructions {
            get {
                return ResourceManager.GetString("ShipSOInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Surcharge (%).
        /// </summary>
        internal static string ShipSurchargePer {
            get {
                return ResourceManager.GetString("ShipSurchargePer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship To.
        /// </summary>
        internal static string ShipTo {
            get {
                return ResourceManager.GetString("ShipTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship To Address.
        /// </summary>
        internal static string ShipToAddress {
            get {
                return ResourceManager.GetString("ShipToAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship To Address Name.
        /// </summary>
        internal static string ShipToAddressName {
            get {
                return ResourceManager.GetString("ShipToAddressName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship to Country.
        /// </summary>
        internal static string ShipToCountry {
            get {
                return ResourceManager.GetString("ShipToCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship to Customer Country:.
        /// </summary>
        internal static string ShipToCustomerCountry {
            get {
                return ResourceManager.GetString("ShipToCustomerCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship to Customer Name:.
        /// </summary>
        internal static string ShipToCustomerName {
            get {
                return ResourceManager.GetString("ShipToCustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShipTo VAT No.
        /// </summary>
        internal static string ShipToVATNo {
            get {
                return ResourceManager.GetString("ShipToVATNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Via.
        /// </summary>
        internal static string ShipVia {
            get {
                return ResourceManager.GetString("ShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Via Account.
        /// </summary>
        internal static string ShipViaAccount {
            get {
                return ResourceManager.GetString("ShipViaAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Via.
        /// </summary>
        internal static string ShipViaNo {
            get {
                return ResourceManager.GetString("ShipViaNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Should the shortage order be fulfilled?.
        /// </summary>
        internal static string ShortageFulfilled {
            get {
                return ResourceManager.GetString("ShortageFulfilled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortage Quantity.
        /// </summary>
        internal static string ShortageQuantity {
            get {
                return ResourceManager.GetString("ShortageQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Additional comments.
        /// </summary>
        internal static string ShortageRefundIssue {
            get {
                return ResourceManager.GetString("ShortageRefundIssue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shortage Value.
        /// </summary>
        internal static string ShortageValue {
            get {
                return ResourceManager.GetString("ShortageValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment.
        /// </summary>
        internal static string ShortShipment {
            get {
                return ResourceManager.GetString("ShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment History.
        /// </summary>
        internal static string ShortShipmentHistory {
            get {
                return ResourceManager.GetString("ShortShipmentHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Should the shortage order be fulfilled?.
        /// </summary>
        internal static string ShortShipOrderFulFill {
            get {
                return ResourceManager.GetString("ShortShipOrderFulFill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Mail?.
        /// </summary>
        internal static string ShouldMailBeSent {
            get {
                return ResourceManager.GetString("ShouldMailBeSent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Manager Approval.
        /// </summary>
        internal static string ShouldMailBeSenttoLineManager {
            get {
                return ResourceManager.GetString("ShouldMailBeSenttoLineManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Approval.
        /// </summary>
        internal static string ShouldMailBeSenttoQuality {
            get {
                return ResourceManager.GetString("ShouldMailBeSenttoQuality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Supplier T&amp;C.
        /// </summary>
        internal static string ShouldMailBeSenttoSupplier {
            get {
                return ResourceManager.GetString("ShouldMailBeSenttoSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Copy images?.
        /// </summary>
        internal static string ShouldPhotosBeCopied {
            get {
                return ResourceManager.GetString("ShouldPhotosBeCopied", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Exchange Rate.
        /// </summary>
        internal static string ShowExchangeRate {
            get {
                return ResourceManager.GetString("ShowExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Released GI Only.
        /// </summary>
        internal static string ShowReleased {
            get {
                return ResourceManager.GetString("ShowReleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Only Unreleased GI.
        /// </summary>
        internal static string ShowUnReleased {
            get {
                return ResourceManager.GetString("ShowUnReleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices with a Total Discrepancy.
        /// </summary>
        internal static string SIDescrepancy {
            get {
                return ResourceManager.GetString("SIDescrepancy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please Provide Comment For Unreleased GI Line.
        /// </summary>
        internal static string SIGIUnreleased {
            get {
                return ResourceManager.GetString("SIGIUnreleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTP Host.
        /// </summary>
        internal static string SMTPHost {
            get {
                return ResourceManager.GetString("SMTPHost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTP Password.
        /// </summary>
        internal static string SMTPPassword {
            get {
                return ResourceManager.GetString("SMTPPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTP Port.
        /// </summary>
        internal static string SMTPPort {
            get {
                return ResourceManager.GetString("SMTPPort", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SMTP Username.
        /// </summary>
        internal static string SMTPUsername {
            get {
                return ResourceManager.GetString("SMTPUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked By.
        /// </summary>
        internal static string SOAuthorisedBy {
            get {
                return ResourceManager.GetString("SOAuthorisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Checked.
        /// </summary>
        internal static string SODateAuthorised {
            get {
                return ResourceManager.GetString("SODateAuthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone SO Line No.
        /// </summary>
        internal static string SOLineNo {
            get {
                return ResourceManager.GetString("SOLineNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Name.
        /// </summary>
        internal static string SOName {
            get {
                return ResourceManager.GetString("SOName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printed line notes to invoice.
        /// </summary>
        internal static string SONotes {
            get {
                return ResourceManager.GetString("SONotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Number.
        /// </summary>
        internal static string SONumber {
            get {
                return ResourceManager.GetString("SONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Instructions to WH only.
        /// </summary>
        internal static string SOShippingInstructions {
            get {
                return ResourceManager.GetString("SOShippingInstructions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unchecked Only?.
        /// </summary>
        internal static string SOUnauthorisedOnly {
            get {
                return ResourceManager.GetString("SOUnauthorisedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source File Name.
        /// </summary>
        internal static string SourceFileName {
            get {
                return ResourceManager.GetString("SourceFileName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Split.
        /// </summary>
        internal static string SplitQty {
            get {
                return ResourceManager.GetString("SplitQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split Quantities.
        /// </summary>
        internal static string SplitQuantities {
            get {
                return ResourceManager.GetString("SplitQuantities", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard Pack Quantity (SPQ).
        /// </summary>
        internal static string SPQ {
            get {
                return ResourceManager.GetString("SPQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMA.
        /// </summary>
        internal static string SRMA {
            get {
                return ResourceManager.GetString("SRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Date.
        /// </summary>
        internal static string SRMADate {
            get {
                return ResourceManager.GetString("SRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SRMANo {
            get {
                return ResourceManager.GetString("SRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipment No.
        /// </summary>
        internal static string SSNumber {
            get {
                return ResourceManager.GetString("SSNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Standard Shipping.
        /// </summary>
        internal static string StandardShipping {
            get {
                return ResourceManager.GetString("StandardShipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date.
        /// </summary>
        internal static string StartDate {
            get {
                return ResourceManager.GetString("StartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        internal static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        internal static string Status {
            get {
                return ResourceManager.GetString("Status", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hold Reason.
        /// </summary>
        internal static string StatusReason {
            get {
                return ResourceManager.GetString("StatusReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Transfer Order.
        /// </summary>
        internal static string STO {
            get {
                return ResourceManager.GetString("STO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to   .
        /// </summary>
        internal static string StockChoice {
            get {
                return ResourceManager.GetString("StockChoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Date.
        /// </summary>
        internal static string StockDate {
            get {
                return ResourceManager.GetString("StockDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Image List.
        /// </summary>
        internal static string StockImagesList {
            get {
                return ResourceManager.GetString("StockImagesList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Keeping Unit.
        /// </summary>
        internal static string StockKeepingUnit {
            get {
                return ResourceManager.GetString("StockKeepingUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Log Reason.
        /// </summary>
        internal static string StockLogReason {
            get {
                return ResourceManager.GetString("StockLogReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock No.
        /// </summary>
        internal static string StockNo {
            get {
                return ResourceManager.GetString("StockNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost.
        /// </summary>
        internal static string StockProvisionLandedCost {
            get {
                return ResourceManager.GetString("StockProvisionLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instruction to WH &amp; Quality control notes.
        /// </summary>
        internal static string StockQualityControlNotes {
            get {
                return ResourceManager.GetString("StockQualityControlNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop Status.
        /// </summary>
        internal static string StopStatus {
            get {
                return ResourceManager.GetString("StopStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string String {
            get {
                return ResourceManager.GetString("String", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOX.
        /// </summary>
        internal static string SubGroup {
            get {
                return ResourceManager.GetString("SubGroup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subject.
        /// </summary>
        internal static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SubTotal.
        /// </summary>
        internal static string SubTotal {
            get {
                return ResourceManager.GetString("SubTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Successful save message time.
        /// </summary>
        internal static string SuccessfulSaveMessageTime {
            get {
                return ResourceManager.GetString("SuccessfulSaveMessageTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sum.
        /// </summary>
        internal static string Sum {
            get {
                return ResourceManager.GetString("Sum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Type.
        /// </summary>
        internal static string SuplierType {
            get {
                return ResourceManager.GetString("SuplierType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        internal static string Supplier {
            get {
                return ResourceManager.GetString("Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval.
        /// </summary>
        internal static string SupplierApproval {
            get {
                return ResourceManager.GetString("SupplierApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Code.
        /// </summary>
        internal static string SupplierCode {
            get {
                return ResourceManager.GetString("SupplierCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Credit.
        /// </summary>
        internal static string SupplierCredit {
            get {
                return ResourceManager.GetString("SupplierCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Change History.
        /// </summary>
        internal static string SupplierHistory {
            get {
                return ResourceManager.GetString("SupplierHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoice {
            get {
                return ResourceManager.GetString("SupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice Date From.
        /// </summary>
        internal static string SupplierInvoiceDateFrom {
            get {
                return ResourceManager.GetString("SupplierInvoiceDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice Date To.
        /// </summary>
        internal static string SupplierInvoiceDateTo {
            get {
                return ResourceManager.GetString("SupplierInvoiceDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoices.
        /// </summary>
        internal static string SupplierInvoices {
            get {
                return ResourceManager.GetString("SupplierInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Name.
        /// </summary>
        internal static string SupplierName {
            get {
                return ResourceManager.GetString("SupplierName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier No.
        /// </summary>
        internal static string SupplierNo {
            get {
                return ResourceManager.GetString("SupplierNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Notes.
        /// </summary>
        internal static string SupplierNotes {
            get {
                return ResourceManager.GetString("SupplierNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Part.
        /// </summary>
        internal static string SupplierPart {
            get {
                return ResourceManager.GetString("SupplierPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Part No.
        /// </summary>
        internal static string SupplierPartNo {
            get {
                return ResourceManager.GetString("SupplierPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Price.
        /// </summary>
        internal static string SupplierPrice {
            get {
                return ResourceManager.GetString("SupplierPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Rating.
        /// </summary>
        internal static string SupplierRating {
            get {
                return ResourceManager.GetString("SupplierRating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Return.
        /// </summary>
        internal static string SupplierReturn {
            get {
                return ResourceManager.GetString("SupplierReturn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SupplierRMA {
            get {
                return ResourceManager.GetString("SupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Date.
        /// </summary>
        internal static string SupplierRMADate {
            get {
                return ResourceManager.GetString("SupplierRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Date From.
        /// </summary>
        internal static string SupplierRMADateFrom {
            get {
                return ResourceManager.GetString("SupplierRMADateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Date To.
        /// </summary>
        internal static string SupplierRMADateTo {
            get {
                return ResourceManager.GetString("SupplierRMADateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SupplierRMANo {
            get {
                return ResourceManager.GetString("SupplierRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Type.
        /// </summary>
        internal static string SupplierType {
            get {
                return ResourceManager.GetString("SupplierType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier VAT Number.
        /// </summary>
        internal static string SupplierVatNumber {
            get {
                return ResourceManager.GetString("SupplierVatNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Warranty.
        /// </summary>
        internal static string SupplierWarranty {
            get {
                return ResourceManager.GetString("SupplierWarranty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Support Team Member to update.
        /// </summary>
        internal static string SupportTeamMember {
            get {
                return ResourceManager.GetString("SupportTeamMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        internal static string SupUse {
            get {
                return ResourceManager.GetString("SupUse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Surname.
        /// </summary>
        internal static string Surname {
            get {
                return ResourceManager.GetString("Surname", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Symbol.
        /// </summary>
        internal static string Symbol {
            get {
                return ResourceManager.GetString("Symbol", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activity.
        /// </summary>
        internal static string TableName {
            get {
                return ResourceManager.GetString("TableName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Price.
        /// </summary>
        internal static string TargetPrice {
            get {
                return ResourceManager.GetString("TargetPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Price (70th Percentile).
        /// </summary>
        internal static string TargetPriceLytica {
            get {
                return ResourceManager.GetString("TargetPriceLytica", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Sell Price.
        /// </summary>
        internal static string TargetSellPrice {
            get {
                return ResourceManager.GetString("TargetSellPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Category.
        /// </summary>
        internal static string TaskCategory {
            get {
                return ResourceManager.GetString("TaskCategory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Date.
        /// </summary>
        internal static string TaskDate {
            get {
                return ResourceManager.GetString("TaskDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Date From.
        /// </summary>
        internal static string TaskDateFrom {
            get {
                return ResourceManager.GetString("TaskDateFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Date To.
        /// </summary>
        internal static string TaskDateTo {
            get {
                return ResourceManager.GetString("TaskDateTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Reminder Date.
        /// </summary>
        internal static string TaskReminderDate {
            get {
                return ResourceManager.GetString("TaskReminderDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Status.
        /// </summary>
        internal static string TaskStatus {
            get {
                return ResourceManager.GetString("TaskStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Task Type.
        /// </summary>
        internal static string TaskType {
            get {
                return ResourceManager.GetString("TaskType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        internal static string Tax {
            get {
                return ResourceManager.GetString("Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax 1 On 2.
        /// </summary>
        internal static string Tax1On2 {
            get {
                return ResourceManager.GetString("Tax1On2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable?.
        /// </summary>
        internal static string Taxable {
            get {
                return ResourceManager.GetString("Taxable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Code.
        /// </summary>
        internal static string TaxCode {
            get {
                return ResourceManager.GetString("TaxCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Name.
        /// </summary>
        internal static string TaxName {
            get {
                return ResourceManager.GetString("TaxName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team.
        /// </summary>
        internal static string Team {
            get {
                return ResourceManager.GetString("Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tel.
        /// </summary>
        internal static string Tel {
            get {
                return ResourceManager.GetString("Tel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telephone.
        /// </summary>
        internal static string Telephone {
            get {
                return ResourceManager.GetString("Telephone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 0800 Number.
        /// </summary>
        internal static string Telephone800 {
            get {
                return ResourceManager.GetString("Telephone800", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ext.
        /// </summary>
        internal static string TelExt {
            get {
                return ResourceManager.GetString("TelExt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dialling Code.
        /// </summary>
        internal static string TelPrefix {
            get {
                return ResourceManager.GetString("TelPrefix", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms.
        /// </summary>
        internal static string Terms {
            get {
                return ResourceManager.GetString("Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Term.
        /// </summary>
        internal static string TermsName {
            get {
                return ResourceManager.GetString("TermsName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TermsWarning.
        /// </summary>
        internal static string TermsWarning {
            get {
                return ResourceManager.GetString("TermsWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Testing Recommended.
        /// </summary>
        internal static string TestingRecommended {
            get {
                return ResourceManager.GetString("TestingRecommended", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Testing Required.
        /// </summary>
        internal static string TestingRequired {
            get {
                return ResourceManager.GetString("TestingRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Text.
        /// </summary>
        internal static string Text {
            get {
                return ResourceManager.GetString("Text", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tier 2 Premier Customer.
        /// </summary>
        internal static string Tier2PremierCustomer {
            get {
                return ResourceManager.GetString("Tier2PremierCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Time.
        /// </summary>
        internal static string Time {
            get {
                return ResourceManager.GetString("Time", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        internal static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Month GP (%).
        /// </summary>
        internal static string TMGpPct {
            get {
                return ResourceManager.GetString("TMGpPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To.
        /// </summary>
        internal static string To {
            get {
                return ResourceManager.GetString("To", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Landed Cost.
        /// </summary>
        internal static string TotalCost {
            get {
                return ResourceManager.GetString("TotalCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Current Landed Cost.
        /// </summary>
        internal static string TotalCurrentLandedCost {
            get {
                return ResourceManager.GetString("TotalCurrentLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Freight Charges.
        /// </summary>
        internal static string TotalFreight {
            get {
                return ResourceManager.GetString("TotalFreight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total GP (%).
        /// </summary>
        internal static string TotalGpPct {
            get {
                return ResourceManager.GetString("TotalGpPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Landed Cost.
        /// </summary>
        internal static string TotalLandedCost {
            get {
                return ResourceManager.GetString("TotalLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total New Landed Cost .
        /// </summary>
        internal static string TotalNewLandedCost {
            get {
                return ResourceManager.GetString("TotalNewLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Primary Landed Cost.
        /// </summary>
        internal static string TotalPrimaryLandedCost {
            get {
                return ResourceManager.GetString("TotalPrimaryLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total quantity of stock available.
        /// </summary>
        internal static string TotalQSA {
            get {
                return ResourceManager.GetString("TotalQSA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Sales Value.
        /// </summary>
        internal static string TotalSales {
            get {
                return ResourceManager.GetString("TotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recommended Ship In Cost.
        /// </summary>
        internal static string TotalShipInCost {
            get {
                return ResourceManager.GetString("TotalShipInCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Value.
        /// </summary>
        internal static string TotalValue {
            get {
                return ResourceManager.GetString("TotalValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Value Of PO and Currency.
        /// </summary>
        internal static string TotalValueOfPo {
            get {
                return ResourceManager.GetString("TotalValueOfPo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Work.
        /// </summary>
        internal static string TotalWork {
            get {
                return ResourceManager.GetString("TotalWork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Quantity of Stock Available.
        /// </summary>
        internal static string TQSA {
            get {
                return ResourceManager.GetString("TQSA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Turnover.
        /// </summary>
        internal static string Turnover {
            get {
                return ResourceManager.GetString("Turnover", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason.
        /// </summary>
        internal static string txtReason {
            get {
                return ResourceManager.GetString("txtReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Year GP (%).
        /// </summary>
        internal static string TYGpPct {
            get {
                return ResourceManager.GetString("TYGpPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        internal static string Type {
            get {
                return ResourceManager.GetString("Type", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unauthorised.
        /// </summary>
        internal static string Unauthorised {
            get {
                return ResourceManager.GetString("Unauthorised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unauthorised Only?.
        /// </summary>
        internal static string UnauthorisedOnly {
            get {
                return ResourceManager.GetString("UnauthorisedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Price.
        /// </summary>
        internal static string UnitPriceUSD {
            get {
                return ResourceManager.GetString("UnitPriceUSD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unselected.
        /// </summary>
        internal static string Unselected {
            get {
                return ResourceManager.GetString("Unselected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unselected Companies.
        /// </summary>
        internal static string UnselectedCompanies {
            get {
                return ResourceManager.GetString("UnselectedCompanies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Code List.
        /// </summary>
        internal static string UnselectedECCNCode {
            get {
                return ResourceManager.GetString("UnselectedECCNCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unselected Groups.
        /// </summary>
        internal static string UnselectedGroups {
            get {
                return ResourceManager.GetString("UnselectedGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unselected IndustryType.
        /// </summary>
        internal static string UnselectedIndustryType {
            get {
                return ResourceManager.GetString("UnselectedIndustryType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unselected Logins.
        /// </summary>
        internal static string UnselectedLogins {
            get {
                return ResourceManager.GetString("UnselectedLogins", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mapped product list.
        /// </summary>
        internal static string UnselectedProduct {
            get {
                return ResourceManager.GetString("UnselectedProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Bill To Address?.
        /// </summary>
        internal static string UpdateBillToAddress {
            get {
                return ResourceManager.GetString("UpdateBillToAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Ship To Address?.
        /// </summary>
        internal static string UpdateShipToAddress {
            get {
                return ResourceManager.GetString("UpdateShipToAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uplift Price (%).
        /// </summary>
        internal static string UPLiftPercent {
            get {
                return ResourceManager.GetString("UPLiftPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uplift Sell Price.
        /// </summary>
        internal static string UpliftPrice {
            get {
                return ResourceManager.GetString("UpliftPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload From Date.
        /// </summary>
        internal static string UploadFromDate {
            get {
                return ResourceManager.GetString("UploadFromDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload To Date.
        /// </summary>
        internal static string UploadToDate {
            get {
                return ResourceManager.GetString("UploadToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Website.
        /// </summary>
        internal static string URL {
            get {
                return ResourceManager.GetString("URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URN No.
        /// </summary>
        internal static string URNNo {
            get {
                return ResourceManager.GetString("URNNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URN Number.
        /// </summary>
        internal static string URNNumber {
            get {
                return ResourceManager.GetString("URNNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Usage.
        /// </summary>
        internal static string Usage {
            get {
                return ResourceManager.GetString("Usage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User.
        /// </summary>
        internal static string User {
            get {
                return ResourceManager.GetString("User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 1.
        /// </summary>
        internal static string UserField1 {
            get {
                return ResourceManager.GetString("UserField1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 10.
        /// </summary>
        internal static string UserField10 {
            get {
                return ResourceManager.GetString("UserField10", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 2.
        /// </summary>
        internal static string UserField2 {
            get {
                return ResourceManager.GetString("UserField2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 3.
        /// </summary>
        internal static string UserField3 {
            get {
                return ResourceManager.GetString("UserField3", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 4.
        /// </summary>
        internal static string UserField4 {
            get {
                return ResourceManager.GetString("UserField4", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 5.
        /// </summary>
        internal static string UserField5 {
            get {
                return ResourceManager.GetString("UserField5", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 6.
        /// </summary>
        internal static string UserField6 {
            get {
                return ResourceManager.GetString("UserField6", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 7.
        /// </summary>
        internal static string UserField7 {
            get {
                return ResourceManager.GetString("UserField7", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 8.
        /// </summary>
        internal static string UserField8 {
            get {
                return ResourceManager.GetString("UserField8", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Field 9.
        /// </summary>
        internal static string UserField9 {
            get {
                return ResourceManager.GetString("UserField9", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User Message.
        /// </summary>
        internal static string UserMessaage {
            get {
                return ResourceManager.GetString("UserMessaage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Username.
        /// </summary>
        internal static string Username {
            get {
                return ResourceManager.GetString("Username", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VAT ID.
        /// </summary>
        internal static string VATFilter {
            get {
                return ResourceManager.GetString("VATFilter", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VAT No.
        /// </summary>
        internal static string VATNo {
            get {
                return ResourceManager.GetString("VATNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VAT No.
        /// </summary>
        internal static string VATNumber {
            get {
                return ResourceManager.GetString("VATNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Level.
        /// </summary>
        internal static string ViewLevel {
            get {
                return ResourceManager.GetString("ViewLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Virtual Cost Price.
        /// </summary>
        internal static string VirtualCostPrice {
            get {
                return ResourceManager.GetString("VirtualCostPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning Message.
        /// </summary>
        internal static string WarningMessage {
            get {
                return ResourceManager.GetString("WarningMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning Name.
        /// </summary>
        internal static string WarningName {
            get {
                return ResourceManager.GetString("WarningName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warning Message.
        /// </summary>
        internal static string WarningText {
            get {
                return ResourceManager.GetString("WarningText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Website.
        /// </summary>
        internal static string WebSiteAddress {
            get {
                return ResourceManager.GetString("WebSiteAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weight.
        /// </summary>
        internal static string Weight {
            get {
                return ResourceManager.GetString("Weight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weight (lbs)?.
        /// </summary>
        internal static string WeightInPounds {
            get {
                return ResourceManager.GetString("WeightInPounds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year To Date.
        /// </summary>
        internal static string YearToDate {
            get {
                return ResourceManager.GetString("YearToDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year to date [spend | profit (%)]:.
        /// </summary>
        internal static string YearToDateNew {
            get {
                return ResourceManager.GetString("YearToDateNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to YTD GP.
        /// </summary>
        internal static string YTDGp {
            get {
                return ResourceManager.GetString("YTDGp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to YTD %.
        /// </summary>
        internal static string YTDPct {
            get {
                return ResourceManager.GetString("YTDPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zip Code.
        /// </summary>
        internal static string Zipcode {
            get {
                return ResourceManager.GetString("Zipcode", resourceCulture);
            }
        }
    }
}
