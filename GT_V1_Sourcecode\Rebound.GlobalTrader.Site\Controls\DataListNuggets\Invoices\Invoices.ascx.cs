using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class Invoices : Base {

		#region Overrides
        private bool _blnPageLoad = true;
        public bool CanPageLoad
        {
            get { return _blnPageLoad; }
            set { _blnPageLoad = value; }
        }
        private bool _blnEmail = true;
        public bool CanEmail
        {
            get { return _blnEmail; }
            set { _blnEmail = value; }
        }
		public bool AllowGenerateXml { get; set; } = false;
		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("Invoices");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "Invoices");
			AddScriptReference("Controls.DataListNuggets.Invoices.Invoices.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = true;
            _tbl.AllowMultipleSelection = true;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "CustomerPurchaseOrderNo", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			_tbl.Columns.Add(new FlexiDataColumn("SalesOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("InvoiceDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			_tbl.Columns.Add(new FlexiDataColumn("Value", Unit.Empty, false));
		}
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPageLoad", _blnPageLoad);
			_scScriptControlDescriptor.AddProperty("ClientNo", SessionManager.ClientID);
			_scScriptControlDescriptor.AddProperty("AllowGenerateXml", AllowGenerateXml);
			//_scScriptControlDescriptor.AddProperty("blnEmail", _blnEmail);
		}

	}
}