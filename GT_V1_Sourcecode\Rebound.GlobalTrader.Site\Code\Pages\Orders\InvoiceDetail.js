Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.prototype={get_intInvoiceID:function(){return this._intInvoiceID},set_intInvoiceID:function(n){this._intInvoiceID!==n&&(this._intInvoiceID=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_ctlLinesDeleted:function(){return this._ctlLinesDeleted},set_ctlLinesDeleted:function(n){this._ctlLinesDeleted!==n&&(this._ctlLinesDeleted=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_pnlStatus:function(){return this._pnlStatus},set_pnlStatus:function(n){this._pnlStatus!==n&&(this._pnlStatus=n)},get_ctlInvoicePDF:function(){return this._ctlInvoicePDF},set_ctlInvoicePDF:function(n){this._ctlInvoicePDF!==n&&(this._ctlInvoicePDF=n)},get_ctlInvoicePDFDragDrop:function(){return this._ctlInvoicePDFDragDrop},set_ctlInvoicePDFDragDrop:function(n){this._ctlInvoicePDFDragDrop!==n&&(this._ctlInvoicePDFDragDrop=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_ctlInvoicePODPDFDragDrop:function(){return this._ctlInvoicePODPDFDragDrop},set_ctlInvoicePODPDFDragDrop:function(n){this._ctlInvoicePODPDFDragDrop!==n&&(this._ctlInvoicePODPDFDragDrop=n)},get_ctlInvoiceExportHistory:function(){return this._ctlInvoiceExportHistory},set_ctlInvoiceExportHistory:function(n){this._ctlInvoiceExportHistory!==n&&(this._ctlInvoiceExportHistory=n)},get_AllowGenerateXml:function(){return this._AllowGenerateXml},set_AllowGenerateXml:function(n){this._AllowGenerateXml!==n&&(this._AllowGenerateXml=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.callBaseMethod(this,"initialize");document.getElementById("btnFormatAction").addEventListener("click",Function.createDelegate(this,this.selectPrintFormat));document.getElementById("btnFormatCancel").addEventListener("click",Function.createDelegate(this,this.closePopup))},goInit:function(){this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printInvoice));this._btnPrint&&this._btnPrint.addEmail(Function.createDelegate(this,this.emailInvoice));this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlLines&&this._ctlLines.addGetDeletedData(Function.createDelegate(this,this.ctlLines_GetDeletedData));this._ctlInvoicePDF&&this._ctlInvoicePDF.getData();this._ctlInvoicePDFDragDrop&&this._ctlInvoicePDFDragDrop.getData();this._ctlInvoicePODPDFDragDrop&&this._ctlInvoicePODPDFDragDrop.getData();this._ctlInvoiceExportHistory&&this._ctlInvoiceExportHistory.getData();Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._ctlLinesDeleted&&this._ctlLinesDeleted.dispose(),this._btnPrint&&this._btnPrint.dispose(),this._btnPrint=null,this._ctlMainInfo=null,this._ctlLines=null,this._ctlLinesDeleted=null,this._pnlStatus=null,this._lblStatus=null,this._intInvoiceID=null,this._ctlInvoicePDF=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.callBaseMethod(this,"dispose"))},printInvoice:function(){this.openFormatModal()},emailInvoice:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.Invoice,this._intInvoiceID,!0)},selectPrintFormat:function(){var n=$('input[name="rdInvoiceFormat"]:checked').val();n=="XML"?$R_FN.openPrintWindow($R_ENUM$PrintObject.XmlInvoice,this._intInvoiceID):$R_FN.openPrintWindow($R_ENUM$PrintObject.Invoice,this._intInvoiceID);this.closePopup()},openFormatModal:function(){let n=this._AllowGenerateXml&&this._ctlMainInfo.getFieldValue("hidGlobalClientNo")=="108"?"XML":"PDF";$("#overlay").css("display","block");$("input:radio[name=rdInvoiceFormat]").val([n]);$("#optionXML").css("display",this._AllowGenerateXml?"block":"none");$("#formatModal").dialog("open")},closePopup:function(){$("#overlay").css("display","none");$("#formatModal").dialog("close")},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintPackingSlip"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlip,this._intInvoiceID);this._btnPrint._strExtraButtonClickCommand=="EmailPackingSlip"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlip,this._intInvoiceID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintCertificateOfConformance"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.CertificateOfConformance,this._intInvoiceID);this._btnPrint._strExtraButtonClickCommand=="EmailCertificateOfConformance"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.CertificateOfConformance,this._intInvoiceID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintCommercialInvoice"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.CommercialInvoice,this._intInvoiceID);this._btnPrint._strExtraButtonClickCommand=="EmailCommercialInvoice"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.CommercialInvoice,this._intInvoiceID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintInvoiceWithCoC"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.InvoiceWithCocf,this._intInvoiceID);this._btnPrint._strExtraButtonClickCommand=="EmailInvoiceWithCoC"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.InvoiceWithCocf,this._intInvoiceID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintPackingSlipWithCoc"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlipWithCocf,this._intInvoiceID);this._btnPrint._strExtraButtonClickCommand=="EmailPackingSlipWithCoc"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlipWithCocf,this._intInvoiceID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intInvoiceID,!1,"Invoice");this._btnPrint._strExtraButtonClickCommand=="PrintDivisionHeaaderLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintDivisionHeaderLog,this._intInvoiceID,!1,"InvoiceDivisionHeader")},ctlMainInfo_GetDataComplete:function(){$R_FN.setInnerHTML(this._lblStatus,this._ctlMainInfo.getFieldValue("hidStatus"));this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null;this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin;this.setLineFieldsFromHeader();this._ctlLines.getData();this._ctlLinesDeleted.getData()},ctlLines_GetDeletedData:function(){this._ctlLinesDeleted.getData()},setLineFieldsFromHeader:function(){var n=this._ctlMainInfo.getFieldValue("hidNo"),t=this._ctlMainInfo.getFieldValue("hidCompany"),i=this._ctlMainInfo.getFieldValue("hidCurrencyCode"),r=this._ctlMainInfo.getFieldValue("hidCurrencyNo"),u=this._ctlMainInfo.getFieldValue("ctlDateShipped");this._ctlLines&&(this._ctlLines._blnExported=this._ctlMainInfo._blnExported,this._ctlLines._frmAdd&&this._ctlLines._frmAdd.setFieldsFromHeader(n,t,i,r,u))}};Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.InvoiceDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);