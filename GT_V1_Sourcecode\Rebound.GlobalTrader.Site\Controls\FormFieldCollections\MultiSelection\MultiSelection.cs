using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
	[ToolboxData("<{0}:MultiSelection runat=server></{0}:MultiSelection>")]
	public class MultiSelection : Base, INamingContainer {

		#region Locals

		private FlexiDataTable _tblSelected;
		private FlexiDataTable _tblUnselected;
		private HtmlControl _ctlHeadingSelected;
		private HtmlControl _ctlHeadingUnselected;
		private IconButton _ibtnSelect;
		private IconButton _ibtnDeselect;

		#endregion

		#region Properties

		private string _strSelectedTitleResource = "";
		public string SelectedTitleResource {
			get { return _strSelectedTitleResource; }
			set { _strSelectedTitleResource = value; }
		}

		private Unit _untPanelHeight = Unit.Pixel(250);
		public Unit PanelHeight {
			get { return _untPanelHeight; }
			set { _untPanelHeight = value; }
		}

		private string _strUnselectedTitleResource = "";
		public string UnselectedTitleResource {
			get { return _strUnselectedTitleResource; }
			set { _strUnselectedTitleResource = value; }
		}

		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.MultiSelection.MultiSelection");
			RemoveCSSClass = true;
		}

		protected override void OnLoad(EventArgs e) {

			//start table
			HtmlTable tbl = new HtmlTable();
			tbl.Border = 0;
			tbl.CellPadding = 0;
			tbl.CellSpacing = 0;
			HtmlTableRow tr = new HtmlTableRow();
			tr.VAlign = "middle";
			tbl.Controls.Add(tr);

			//left cell
			HtmlTableCell tdLeft = new HtmlTableCell();
			tdLeft.Style.Add("width", "175px");
			_ctlHeadingUnselected = new HtmlGenericControl("h5");
			tdLeft.Controls.Add(_ctlHeadingUnselected);
			_tblUnselected = new FlexiDataTable();
			_tblUnselected.ShowHeader = false;
			_tblUnselected.AllowMultipleSelection = true;
			_tblUnselected.Columns.Add(new FlexiDataColumn());
			tdLeft.Controls.Add(_tblUnselected);
			tr.Controls.Add(tdLeft);

			//center cell
			HtmlTableCell tdCentre = new HtmlTableCell();
			tdCentre.Style.Add("vertical-align", "middle");
			tdCentre.Style.Add("padding", "0px 0px 0px 12px");
			_ibtnSelect = new IconButton();
			_ibtnSelect.IconGroup = IconButton.IconGroupList.FormBody;
			_ibtnSelect.IconTitleResource = "Select";
			_ibtnSelect.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			_ibtnSelect.Href = "javascript:void(0);";
			_ibtnSelect.IsInitiallyEnabled = false;
			tdCentre.Controls.Add(_ibtnSelect);
			tdCentre.Controls.Add(ControlBuilders.CreateLiteral("<br /><br />"));
			_ibtnDeselect = new IconButton();
			_ibtnDeselect.IconGroup = IconButton.IconGroupList.FormBody;
			_ibtnDeselect.IconTitleResource = "Deselect";
			_ibtnDeselect.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			_ibtnDeselect.Href = "javascript:void(0);";
			_ibtnDeselect.IsInitiallyEnabled = false;
			tdCentre.Controls.Add(_ibtnDeselect);
			tr.Controls.Add(tdCentre);

			//right cell
			HtmlTableCell tdRight = new HtmlTableCell();
			tdRight.Style.Add("width", "175px");
			_ctlHeadingSelected = new HtmlGenericControl("h5");
			tdRight.Controls.Add(_ctlHeadingSelected);
			_tblSelected = new FlexiDataTable();
			_tblSelected.ShowHeader = false;
			_tblSelected.AllowMultipleSelection = true;
			_tblSelected.Columns.Add(new FlexiDataColumn());
			tdRight.Controls.Add(_tblSelected);
			tr.Controls.Add(tdRight);

			AddControl(tbl);
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_ctlHeadingSelected.Controls.Add(ControlBuilders.CreateLiteral(Functions.GetGlobalResource("FormFields", _strSelectedTitleResource)));
			_ctlHeadingUnselected.Controls.Add(ControlBuilders.CreateLiteral(Functions.GetGlobalResource("FormFields", _strUnselectedTitleResource)));
			_tblSelected.PanelHeight = _untPanelHeight;
			_tblUnselected.PanelHeight = _untPanelHeight;
			base.OnPreRender(e);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection", this.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnSelect", _ibtnSelect.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnDeselect", _ibtnDeselect.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblSelected", _tblSelected.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblUnselected", _tblUnselected.ClientID);
		}

	}
}
