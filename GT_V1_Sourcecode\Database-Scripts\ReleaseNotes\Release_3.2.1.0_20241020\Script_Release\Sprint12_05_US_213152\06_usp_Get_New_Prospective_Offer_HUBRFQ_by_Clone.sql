﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-213152]		Trung Pham Van		10-Oct-2024		CREATE		Get New Prospective Offer HUBRFQ by Clone
[US-213152]		Trung Pham Van		14-Oct-2024		UPDATE		Refactor code to check new HUBRFQ is cloned or not
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE usp_Get_New_Prospective_Offer_HUBRFQ_by_Clone
	@ProspectiveOfferId INT,
	@ProspectiveOfferLineId INT,
	@CustomerRequirementId INT,
	@ExistingBomId INT,
	@ClientId INT,
	@BOMId INT OUTPUT
AS
BEGIN
	SELECT cr.Part, b.BOMId, c.CompanyId,  cr.PackageNo, cr.ProductNo,  cr.CustomerPart
	INTO #oldData
	FROM tbBOM b
	JOIN tbCompany c ON b.CompanyNo = c.CompanyId
	JOIN tbCustomerRequirement cr ON b.BOMId = cr.BOMNo
	WHERE b.BomId = @ExistingBomId AND cr.CustomerRequirementId = @CustomerRequirementId

	SELECT @BOMId = bom.BOMId
	FROM tbBOM bom
	JOIN tbCompany c ON bom.CompanyNo = c.CompanyId
	JOIN tbCustomerRequirement cr ON cr.BOMNo = bom.BOMId
	JOIN tbProspectiveOfferLines prol ON prol.Part = cr.Part
	JOIN #oldData o ON o.Part = cr.Part
	WHERE bom.ProspectiveOfferNo = @ProspectiveOfferId AND bom.IsFromProspectiveOffer = 1
	AND prol.ProspectiveOfferNo = @ProspectiveOfferId AND bom.CompanyNo = o.CompanyId
	AND ISNULL(cr.PackageNo, 0) = ISNULL(o.PackageNo, 0)
	AND ISNULL(cr.ProductNo, 0) = ISNULL(o.ProductNo, 0)
	AND ISNULL(cr.CustomerPart, '') = ISNULL(o.CustomerPart, '')
	AND cr.REQStatus = 3

	DROP TABLE #oldData
END
GO