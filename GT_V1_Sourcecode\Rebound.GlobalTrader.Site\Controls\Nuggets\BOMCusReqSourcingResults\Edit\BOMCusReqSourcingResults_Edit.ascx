<%--
********************************************************************************************
Marker     changed by      date         Remarks
[001]      Aashu          06/06/2018     Added supplier warranty field
**********************************************************************************************
--%>
<%@ Control Language="C#" CodeBehind="BOMCusReqSourcingResults_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CusReqSourcingResults_Edit")%></Explanation>
	
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
		
			<%--<ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="cmbSupplier" ResourceTitle="Supplier" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbSupplier" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Suppliers" /></Field>
			</ReboundUI_Form:FormField>--%>
			
			<ReboundUI_Form:FormField id="ctlSupplier_Label" runat="server" FieldID="lblSupplier" ResourceTitle="Supplier" DisplayRequiredFieldMarkerOnly="true">
				<Field><asp:Label ID="lblSupplier" runat="server"></asp:Label>  </Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCompanyNamelbl" runat="server" FieldID="lblCompanyName" ResourceTitle="Location" >
	            <Field><asp:Label ID="lblCompanyName" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlCompanyNameddl" runat="server" FieldID="ddlCompanyName" ResourceTitle="Location" IsRequiredField="false" >
				<Field><ReboundDropDown:Country ID="ddlCompanyName" runat="server" /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlTypeOfSupplier" runat="server" FieldID="ddlTypeOfSupplier" ResourceTitle="AS6081TOS"  IsRequiredField="false">
				<Field><ReboundDropDown:TypeOfSupplier ID="ddlTypeOfSupplier" runat="server"  NoValue_Value="" InitialValue=""  /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlReasonForSupplier" runat="server" FieldID="ddlReasonForSupplier" ResourceTitle="AS6081RCS"  IsRequiredField="false">
				<Field><ReboundDropDown:ReasonForSupplier ID="ddlReasonForSupplier" runat="server"  NoValue_Value="" InitialValue=""  /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlRiskOfSupplier" runat="server" FieldID="ddlRiskOfSupplier" ResourceTitle="AS6081ROS"  IsRequiredField="false">
				<Field><ReboundDropDown:RiskOfSupplier ID="ddlRiskOfSupplier" runat="server"  NoValue_Value="" InitialValue=""  /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="txtPartNo" ResourceTitle="PartNo" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtPartNo" runat="server" Width="250" UppercaseOnly="true" />
					<ReboundAutoSearch:PartSearch ID="autPartNo" runat="server" RelatedTextBoxID="txtPartNo" ResultsHeight="150" Width="250" ResultsActionType="RaiseEvent" TriggerByButton="true" />
				</Field>
			</ReboundUI_Form:FormField>
           <%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlROHS" runat="server" FieldID="ddlROHS" ResourceTitle="ROHS" IsRequiredField="false"  >
				<Field><ReboundDropDown:ROHSStatus ID="ddlROHS" runat="server"   NoValue_Value="" InitialValue="" /></Field>
			</ReboundUI_Form:FormField>
           <%--[001] code end--%>
			<ReboundUI_Form:FormField id="ctlManufacturer" runat="server" FieldID="cmbManufacturer" ResourceTitle="Manufacturer" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" />
		&nbsp;<asp:Label ID="lblManufacturer" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>

             <ReboundUI_Form:FormField ID="ctlCountryOfManufacture" runat="server" FieldID="ddlCountryOfManufacture"
                ResourceTitle="CountryOfOrigin">
                <Field>
                    <ReboundDropDown:GlobalCountryList ID="ddlCountryOfManufacture" runat="server" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:Label ID="lblCountryOfOrigin"    runat="server" />
                    &nbsp;&nbsp;<asp:Label ID="lblCountryRiskStatus" runat="server" class="ihspartstatusdoc"/>
                    </Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlDateCode" runat="server" FieldID="txtDateCode" ResourceTitle="DateCode" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtDateCode" runat="server" Width="60" MaxLength="5" UppercaseOnly="true" /></Field>			
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="ddlProduct" ResourceTitle="Product" IsRequiredField="true">
				<Field><ReboundDropDown:Product ID="ddlProduct" runat="server" />
			&nbsp;&nbsp;&nbsp;	&nbsp;<asp:Label ID="lblProduct" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>--%>
            <ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="cmbProducts" ResourceTitle="Product"  IsRequiredField="true" >
				<Field><ReboundUI:Combo ID="cmbProducts" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="450" PanelHeight="250"  AutoSearchControlType="Products" />
                    &nbsp;&nbsp;&nbsp;	&nbsp;<asp:Label ID="lblProduct" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>
			<%--<ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="ddlPackage" ResourceTitle="Package" IsRequiredField="true">
				<Field><ReboundDropDown:Package ID="ddlPackage" runat="server" />				
			&nbsp;&nbsp;&nbsp;&nbsp;	&nbsp;<asp:Label ID="lblPackage" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>--%>
            <ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="cmbPackage" ResourceTitle="Package" IsRequiredField="false">
				<Field>
                    <ReboundUI:Combo ID="cmbPackage" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site"  AutoSearchControlType="Packages" />				
			&nbsp;&nbsp;&nbsp;&nbsp;	&nbsp;<asp:Label ID="lblPackage" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="txtQuantity" ResourceTitle="Quantity" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" TextBoxMode="Numeric" />&nbsp;&nbsp;<asp:Label ID="lblQty" runat="server" /></Field>
			</ReboundUI_Form:FormField>


			<%--<ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency" IsRequiredField="true">
				<Field><ReboundDropDown:Currency ID="ddlCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>

			<ReboundUI_Form:FormField id="ctlOfferStatus" runat="server" FieldID="ddlOfferStatus" ResourceTitle="OfferStatus" IsRequiredField="false" >
				<Field><ReboundDropDown:OfferStatus ID="ddlOfferStatus" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			            
            <ReboundUI_Form:FormField id="ctlSPQ" runat="server" FieldID="txtSPQ" ResourceTitle="SPQ" IsRequiredField="false" >
				<Field><ReboundUI:ReboundTextBox ID="txtSPQ" runat="server" /></Field>
			</ReboundUI_Form:FormField>		
			
			<ReboundUI_Form:FormField id="ctlFactorySealed" runat="server" FieldID="txtFactorySealed" ResourceTitle="FactorySealed" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtFactorySealed" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlROHSStatus" runat="server" FieldID="txtROHSStatus" ResourceTitle="ROHSStatus" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtROHSStatus" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>

			<%--<ReboundUI_Form:FormField id="ctlMSL" runat="server" FieldID="txtMSL" ResourceTitle="MSL"   >
				<Field><ReboundUI:ReboundTextBox ID="txtMSL" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
             <ReboundUI_Form:FormField id="ctlMSL" runat="server" FieldID="ddlMsl" ResourceTitle="MSL" IsRequiredField="false">
				<Field><ReboundDropDown:MSLLevelNo ID="ddlMsl" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlTQSA" runat="server" FieldID="txtTQSA" ResourceTitle="TQSA"  IsRequiredField="false" >
				<Field><ReboundUI:ReboundTextBox ID="txtTQSA" runat="server"  TextBoxMode="Numeric"/></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlMOQ" runat="server" FieldID="txtMOQ" ResourceTitle="MOQ" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtMOQ" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlLTB" runat="server" FieldID="txtLTB" ResourceTitle="LTB" >
				<Field><ReboundUI:ReboundTextBox ID="txtLTB" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            
            <ReboundUI_Form:FormField id="ctlLinkCurrency" runat="server" FieldID="ddlLinkCurrency" ResourceTitle="SellCurrency" IsRequiredField="true">
				<Field><ReboundDropDown:LinkMultiCurrency ID="ddlLinkCurrency" runat="server"  /></Field>
			</ReboundUI_Form:FormField>
            	
			<ReboundUI_Form:FormField id="ctlPrice" runat="server" FieldID="txtPrice" ResourceTitle="UpliftPrice" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />&nbsp;&nbsp;<asp:Label ID="lblUpliftPrice" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlPriceClient" runat="server" FieldID="txtcPrice" ResourceTitle="Price" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtcPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> &nbsp;<asp:Label ID="txtcPrice_Currency" runat="server" />  </Field>
			</ReboundUI_Form:FormField>
			
		     <ReboundUI_Form:FormField id="ctlSupplierPrice" runat="server" FieldID="lblSupplierPrice" ResourceTitle="BuyPrice1" IsRequiredField="true" >
				<Field><ReboundUI:ReboundTextBox ID="lblSupplierPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> &nbsp;<asp:Label ID="lblSupplierPrice_Currency" runat="server" />  </Field>
			</ReboundUI_Form:FormField>
			<asp:TableRow ID="ctlSellPriceLessLabel">
                <asp:TableCell ColumnSpan="2" Style="padding-top:5px">
                    <label id="lblSellPriceLess"  style="color:#FFFF00; padding-bottom: 5px;font-weight: bold;font-size: 11px;">Uplift Sell Price is Less than Buy Price</label>
                </asp:TableCell>
            </asp:TableRow>
			<ReboundUI_Form:FormField id="ctlSellPriceLessReason" runat="server" FieldID="txtSellPriceLessReason" ResourceTitle="txtReason" IsRequiredField="false">
                <Field><ReboundUI:ReboundTextBox ID="txtSellPriceLessReason" runat="server" Width="400" TextMode="multiLine" MaxLength="128" /></Field>			
			</ReboundUI_Form:FormField>

             <ReboundUI_Form:FormField id="ctlEstimatedShippingCost" runat="server" FieldID="lblEstimatedShippingCost" ResourceTitle="EstimatedShippingCost" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="lblEstimatedShippingCost" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />  </Field>
			 </ReboundUI_Form:FormField>
			 
			 <ReboundUI_Form:FormField id="ctlLeadTime" runat="server" FieldID="txtLeadTime" ResourceTitle="LeadTime" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtLeadTime" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlRegion" runat="server" FieldID="ddlRegion" ResourceTitle="Region" IsRequiredField="false">
				<Field><ReboundDropDown:Region ID="ddlRegion" runat="server" NoValue_Value="" InitialValue=""/></Field>
			</ReboundUI_Form:FormField>

			 <ReboundUI_Form:FormField ID="ctlDeliveryDate" runat="server" FieldID="txtDeliveryDate"
                ResourceTitle="DeliveryDate" IsRequiredField="false">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtDeliveryDate" runat="server" Width="150" />
                    <ReboundUI:Calendar ID="calDeliveryDate" runat="server" RelatedTextBoxID="txtDeliveryDate" />
                </Field>
            </ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlROHSStatus" runat="server" FieldID="txtROHSStatus" ResourceTitle="ROHSStatus" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtROHSStatus" Width="50" runat="server" /></Field>
			</ReboundUI_Form:FormField>
            <%-- [001] code start--%>
            <ReboundUI_Form:FormField id="ctlSupplierWarranty" runat="server" FieldID="txtSupplierWarranty" ResourceTitle="SupplierWarranty">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierWarranty" runat="server" Width="50" MaxLength="10" TextBoxMode="Numeric"/> days</Field>
			</ReboundUI_Form:FormField>
            <%-- [001] code end--%>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="400" TextMode="multiLine" MaxLength="128" /></Field>			
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlTestingRecommended" runat="server" FieldID="chkTestingRecommended" ResourceTitle="TestingRecommended">
				<Field><ReboundUI:ImageCheckBox ID="chkTestingRecommended" runat="server"  Enabled="true"/></Field>			
			</ReboundUI_Form:FormField>

             
               <ReboundUI_Form:FormField id="ctlPriority" runat="server" FieldID="ddlPriority" ResourceTitle="Priority" >
				<Field><ReboundDropDown:Priority ID="ddlPriority" runat="server" NoValue_Value="" InitialValue=""/></Field>
			</ReboundUI_Form:FormField>                  
                                   
             <ReboundUI_Form:FormField id="ctlPartWatchMatch" runat="server" FieldID="chkPartWatchMatch" ResourceTitle="PartWatchMatch">
				<Field><ReboundUI:ImageCheckBox ID="chkPartWatchMatch" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	
	</Content>
</ReboundUI_Form:DesignBase>
