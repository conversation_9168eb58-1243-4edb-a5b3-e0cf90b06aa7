///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/07/2012   Rebound- Invoice bulk Emailer
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.initializeBase(this, [element]);
	this._intContactID = -1;
	this._ctlAddress = null;
	this._intCompanyNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.prototype = {

    get_intContactID: function() { return this._intContactID; }, set_intContactID: function(value) { if (this._intContactID !== value) this._intContactID = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addSave(Function.createDelegate(this, this.saveClicked));
    },

    dispose: function() {
        if (this.isDisposed) return;
        //if (this._ctlAddress) this._ctlAddress.dispose();
        //this._ctlAddress = null;
        this._intContactID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            //this._ctlAddress = $find(this.getField("ctlAddress").ID);
            //this._ctlAddress._ctlRelatedForm = this;
        }
        // this.getFieldDropDownData("ctlCountry");
       // alert(this._intCompanyNo);
        this.getFieldControl("ctlCompanyAddress")._intCompanyID = this._intCompanyNo;
        this.getFieldDropDownData("ctlCompanyAddress");
    },

    saveClicked: function() {
        if (!this.validateForm()) return;
        //[001] code start
        if (!this.validateEmail()) return;
        //[001] code end
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ContactMainInfo");
        obj.set_DataObject("ContactMainInfo");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intContactID);
        obj.addParameter("FirstName", this.getFieldValue("ctlFirstName"));
        obj.addParameter("Surname", this.getFieldValue("ctlSurname"));
        obj.addParameter("JobTitle", this.getFieldValue("ctlJobTitle"));
        obj.addParameter("Tel", this.getFieldValue("ctlTel"));
        obj.addParameter("Fax", this.getFieldValue("ctlFax"));
        obj.addParameter("Extension", this.getFieldValue("ctlExtension"));
        obj.addParameter("HomeTel", this.getFieldValue("ctlHomeTel"));
        obj.addParameter("MobileTel", this.getFieldValue("ctlMobileTel"));
        obj.addParameter("Email", this.getFieldValue("ctlEmail"));
        obj.addParameter("TextOnlyEmail", this.getFieldValue("ctlTextOnlyEmail"));
        obj.addParameter("Nickname", this.getFieldValue("ctlNickname"));
        //obj.addParameter("HasAddress", this._ctlAddress.addressHasBeenEntered());
        //obj.addParameter("AddressID", this._ctlAddress._intAddressID);
        //obj.addParameter("AddressName", this.getFieldValue("ctlAddressName"));
        //obj.addParameter("Address1", this.getFieldValue("ctlLine1"));
        //obj.addParameter("Address2", this.getFieldValue("ctlLine2"));
        //obj.addParameter("Address3", this.getFieldValue("ctlLine3"));
        //obj.addParameter("Town", this.getFieldValue("ctlTown"));
        //obj.addParameter("County", this.getFieldValue("ctlCounty"));
        //obj.addParameter("Country", this.getFieldValue("ctlCountry"));
        //obj.addParameter("Postcode", this.getFieldValue("ctlPostcode"));
        obj.addParameter("AddressNo", this.getFieldValue("ctlCompanyAddress"));
        //[001] code start
        obj.addParameter("FinanceContact", this.getFieldValue("ctlFinanceContacts"));
        obj.addParameter("Inactive", this.getFieldValue("ctlInactive"));
        obj.addParameter("IsSendShipmentNotification", this.getFieldValue("ctlSendShipmentNotification"));
        //[001] code end
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        //if (!this._ctlAddress.validateFields()) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },
    //[001] code start
    validateEmail: function() {
    if (this.getFieldValue("ctlFinanceContacts") == true && this.checkFieldEntered("ctlEmail") == false) {
        this.showError(true, $R_RES.ContactEmailMessage);
            return false;
        }
        else
            return true;
    }
    //[001] code end

};

Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
