using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class BOMItems : Base
    {

        #region Locals
        protected IconButton _ibtnAdd;
        protected IconButton _ibtnDelete;
        protected IconButton _ibtnUnRelease;  
        protected IconButton _ibtnRelease;
        //start code by umendra 26/10/2020
        protected IconButton _ibtnImportSrcReslt;
        //end code by umendra 26/10/2020
        //End
       
        private TabStrip _ctlTabs;
        protected Tab _ctlTabStock;
        protected FlexiDataTable _tblStock;
        protected Panel _pnlLineDetail;
        protected Panel _pnlLoadingLineDetail;
        protected Panel _pnlLineDetailError;
        //  protected Controls.Forms.BOMItems_Add _frmAdd;

        protected IconButton _ibtnNoBid;
        protected IconButton _ibtnRecallNoBid;
        protected IconButton _ibtnNote;
        //
        protected IconButton _ibtnSourcingImportTool;
        //

        protected IconButton _ibtnApplyPartwatch;
        protected IconButton _ibtnRemovePartwatch;
        protected IconButton _ibtnExportToExcel;
        protected IconButton _ibtnHubImportSR;
        #endregion

        #region Properties

        private int _intBOMID = -1;
        public int BOMID
        {
            get { return _intBOMID; }
            set { _intBOMID = value; }
        }
        private bool _blnCanRelease = true;
        public bool CanRelease
        {
            get { return _blnCanRelease; }
            set { _blnCanRelease = value; }
        }

        private bool _blnAdd = true;
        public bool CanAdd
        {
            get { return _blnAdd; }
            set { _blnAdd = value; }
        }

        private bool _blnDelete = true;
        public bool CanDelete
        {
            get { return _blnDelete; }
            set { _blnDelete = value; }
        }
        private bool _blnUnRelease = true;
        public bool CanUnRelease
        {
            get { return _blnUnRelease; }
            set { _blnUnRelease = value; }
        }

        private bool _blnCanNobid = true;
        public bool CanNoBid
        {
            get { return _blnCanNobid; }
            set { _blnCanNobid = value; }
        }
        private bool _blnCanRecallNobid = true;
        public bool CanRecallNoBid
        {
            get { return _blnCanRecallNobid; }
            set { _blnCanRecallNobid = value; }
        }
        private bool _blnCanNote = true;
        public bool CanNote
        {
            get { return _blnCanNote; }
            set { _blnCanNote = value; }
        }

        //start code by umendra
        private bool _blnImportResult = true;
        public bool CanImportResult
        {
            get { return _blnImportResult; }
            set { _blnImportResult = value; }
        }
        //end code by umendra
        public bool CanImportExportSourcing { get; set; }
        #endregion

        #region Overrides
        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            SetupTables();
            AddScriptReference("Controls.Nuggets.BOMItems.BOMItems.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "BOMItems");
            if (_objQSManager.BOMID > 0) _intBOMID = _objQSManager.BOMID;
        }

        protected override void OnPreRender(EventArgs e)
        {
            //_ibtnRelease.Visible = _blnCanRelease;
            _ibtnRelease.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnAdd.Visible = !Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnNoBid.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnRecallNoBid.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            //start code by umendra
            //_ibtnImportSrcReslt.Visible = true;
            //end code by umendra
            _ibtnImportSrcReslt.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnApplyPartwatch.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnRemovePartwatch.Visible = Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnExportToExcel.Visible = CanImportExportSourcing && Convert.ToBoolean(SessionManager.IsPOHub);
            _ibtnHubImportSR.Visible = CanImportExportSourcing && Convert.ToBoolean(SessionManager.IsPOHub);

            base.OnPreRender(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnLoad(e);
        }


        #endregion

        private void SetupTables()
        {


            _tblStock.PanelHeight = Unit.Pixel(160);
            _tblStock.AllowSelection = true;
            _tblStock.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(18), false, HorizontalAlign.Center));
            _tblStock.Columns.Add(new FlexiDataColumn("Requirement", Unit.Pixel(34)));
            if (SessionManager.IsPOHub == true)
            {
                _tblStock.Columns.Add(new FlexiDataColumn("Quantity", "HUBPartwatch", Unit.Pixel(51)));
            }
            else
            {
                _tblStock.Columns.Add(new FlexiDataColumn("Quantity",  Unit.Pixel(51)));//WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsQuantity)));
            }

            _tblStock.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", Unit.Pixel(120)));//WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsPartNumber)));
            _tblStock.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsManufacturerCode)));
            _tblStock.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsPackage)));
            _tblStock.Columns.Add(new FlexiDataColumn("Customer", "DateRequired", Unit.Pixel(126)));//WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsCompanyName)));
            _tblStock.Columns.Add(new FlexiDataColumn("TargetPrice", "Salesman", Unit.Pixel(112)));//WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsSalesman)));
            _tblStock.Columns.Add(new FlexiDataColumn("MSL", "FactorySealed", WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsFactorySealed)));
            _tblStock.Columns.Add(new FlexiDataColumn("AS6081Required", Unit.Pixel(58)));
            _tblStock.Columns.Add(new FlexiDataColumn("AssignedTo", Unit.Pixel(58)));
            if (SessionManager.IsPOHub == true)
                _tblStock.Columns.Add(new FlexiDataColumn("PurchaseRequestNo", "Notes", WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsNotes)));
            else
                _tblStock.Columns.Add(new FlexiDataColumn("Notes"));


            ////_tblStock.PanelHeight = Unit.Pixel(160);
            ////_tblStock.AllowSelection = true;
            ////_tblStock.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(13), false, HorizontalAlign.Center));
            ////_tblStock.Columns.Add(new FlexiDataColumn("Requirement", Unit.Pixel(34))); //WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)
            ////_tblStock.Columns.Add(new FlexiDataColumn("Quantity", Unit.Pixel(51)));
            ////_tblStock.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", Unit.Pixel(120))); //WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)
            ////_tblStock.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", Unit.Pixel(63)));//WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)
            ////_tblStock.Columns.Add(new FlexiDataColumn("Product", "Package", Unit.Pixel(50)));
            ////_tblStock.Columns.Add(new FlexiDataColumn("Customer", "DateRequired", Unit.Pixel(126)));//WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName)
            ////_tblStock.Columns.Add(new FlexiDataColumn("TargetPrice", "Salesman", Unit.Pixel(112)));// WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName)
            ////if (SessionManager.IsPOHub == true)
            ////{
            ////    _tblStock.Columns.Add(new FlexiDataColumn("PurchaseRequestNo", "Notes", Unit.Pixel(100))); //WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName)
            ////}
            ////else
            ////{
            ////    _tblStock.Columns.Add(new FlexiDataColumn("Notes", Unit.Pixel(100)));//WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName)

            ////}

            ////_tblStock.Columns.Add(new FlexiDataColumn("MSL", "FactorySealed", Unit.Pixel(262)));//WidthManager.GetWidth(WidthManager.ColumnWidth.Product)


          //  _tblStock.PanelHeight = Unit.Pixel(160);
           // _tblStock.AllowSelection = true;
            //_tblStock.Columns.Add(new FlexiDataColumn("Select", WidthManager.GetWidth(WidthManager.ColumnWidth.LineNo),false,HorizontalAlign.Center));
            //_tblStock.Columns.Add(new FlexiDataColumn("Requirement", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
            //_tblStock.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
            //_tblStock.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo))); //
            //_tblStock.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            //_tblStock.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
            //_tblStock.Columns.Add(new FlexiDataColumn("Customer", "DateRequired", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName)));
            //_tblStock.Columns.Add(new FlexiDataColumn("TargetPrice", "Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName)));
            //_tblStock.Columns.Add(new FlexiDataColumn("MSL", "FactorySealed", WidthManager.GetWidth(WidthManager.ColumnWidth.BooleanValue)));
            //if (SessionManager.IsPOHub == true)
            //{
            //    _tblStock.Columns.Add(new FlexiDataColumn("PurchaseRequestNo", "Notes"));
            //}
            //else
            //{
            //    _tblStock.Columns.Add(new FlexiDataColumn("Notes"));

            //}
        }

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
            _scScriptControlDescriptor.AddComponentProperty("ctlTabStrip", _ctlTabs.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblStock", _tblStock.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlTabStock", _ctlTabStock.ClientID);
            if (_blnAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            if (_blnDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
            if (_blnUnRelease) _scScriptControlDescriptor.AddElementProperty("ibtnUnRelease", _ibtnUnRelease.ClientID);
            if (_blnCanRelease) _scScriptControlDescriptor.AddElementProperty("ibtnRelease", _ibtnRelease.ClientID);
            if (_blnCanNobid) _scScriptControlDescriptor.AddElementProperty("ibtnNoBid", _ibtnNoBid.ClientID);
            if (_blnCanRecallNobid) _scScriptControlDescriptor.AddElementProperty("ibtnRecallNoBid", _ibtnRecallNoBid.ClientID);
            if (_blnCanNote) _scScriptControlDescriptor.AddElementProperty("ibtnNote", _ibtnNote.ClientID);
            //if (_blnImportResult) _scScriptControlDescriptor.AddElementProperty("ibtnImportSrcReslt", _ibtnImportSrcReslt.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetail", _pnlLineDetail.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", _pnlLoadingLineDetail.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetailError", _pnlLineDetailError.ClientID);
            //
            _scScriptControlDescriptor.AddElementProperty("ibtnImportSrcReslt", _ibtnImportSrcReslt.ClientID);//[003]
                                                                                                              //
            _scScriptControlDescriptor.AddElementProperty("ibtnApplyPartwatch", _ibtnApplyPartwatch.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnRemovePartwatch", _ibtnRemovePartwatch.ClientID);
            if (CanImportExportSourcing)
            {
                _scScriptControlDescriptor.AddElementProperty("ibtnExportToExcel", _ibtnExportToExcel.ClientID);
                _scScriptControlDescriptor.AddElementProperty("ibtnHubImportSR", _ibtnHubImportSR.ClientID);//[003]
            }
        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
            _ctlTabs = (TabStrip)ctlDesignBase.FindContentControl("ctlTabs");
            _ctlTabs.CreateControls();
            _ctlTabStock = (Tab)ctlDesignBase.FindContentControl("ctlTabStock");
            _tblStock = (FlexiDataTable)_ctlTabStock.FindContentControl("tblStock");
            _ibtnAdd = FindIconButton("ibtnAdd");
            _ibtnDelete = FindIconButton("ibtnDelete");
            _ibtnUnRelease = FindIconButton("ibtnUnRelease");
            _ibtnRelease = (IconButton)FindIconButton("ibtnRelease");
            _pnlLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLineDetail");
            _pnlLoadingLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLoadingLineDetail");
            _pnlLineDetailError = (Panel)Functions.FindControlRecursive(this, "pnlLineDetailError");
            _ibtnNoBid = FindIconButton("ibtnNoBid");
            _ibtnRecallNoBid = FindIconButton("ibtnRecallNoBid");
            _ibtnNote = FindIconButton("ibtnNote");
            //start code by umendra 26/10/2020
            // _ibtnImportSrcReslt = (IconButton)FindIconButton("ibtnImportSrcReslt");
            //end code by umendra
            //  _frmAdd = (Forms.BOMItems_Add)FindFormControl("frmAdd");
            // 
            //
            _ibtnImportSrcReslt = (IconButton)Functions.FindControlRecursive(this, "ibtnImportSrcReslt");
            //
            _ibtnApplyPartwatch = FindIconButton("ibtnApplyPartwatch");
            _ibtnRemovePartwatch = FindIconButton("ibtnRemovePartwatch");
            _ibtnExportToExcel = FindIconButton("ibtnExportToExcel");
            _ibtnHubImportSR = (IconButton)Functions.FindControlRecursive(this, "ibtnHubImportSR");
        }

    }
}
