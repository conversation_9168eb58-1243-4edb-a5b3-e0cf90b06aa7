﻿/*===========================================================================================  
TASK				UPDATED BY			DATE			ACTION		DESCRIPTION  
[BUG-223083]		Trung Pham Van		29-Nov-2024		CREATE		Inactive SO from 26th June 2024 backwards excepts the SO are using in Sourcing Result
===========================================================================================  
*/
UPDATE epo 
SET epo.Inactive=1, epo.UpdatedBy = 0, epo.InactiveDate=GETDATE() 
FROM BorisGlobalTraderimports.dbo.tbEpo epo
LEFT JOIN dbo.tbSourcingResult sr ON sr.SourcingTableItemNo = epo.EpoId
WHERE sr.SourcingTableItemNo IS NULL AND epo.DLUP < '2024-06-26'