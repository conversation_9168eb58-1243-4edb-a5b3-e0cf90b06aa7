using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class Lots : Base {

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("Lots");
			base.OnInit(e);
			AddScriptReference("Controls.DataListNuggets.Lots.Lots.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "Lots");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Name", Unit.Empty, true));
			_tbl.Columns.Add(new FlexiDataColumn("Code", WidthManager.GetWidth(WidthManager.ColumnWidth.Lot), true));
			_tbl.Columns.Add(new FlexiDataColumn("StockCount", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			_tbl.Columns.Add(new FlexiDataColumn("IsConsignment", WidthManager.GetWidth(WidthManager.ColumnWidth.BooleanValue), true));
			_tbl.Columns.Add(new FlexiDataColumn("IsOnHold", WidthManager.GetWidth(WidthManager.ColumnWidth.BooleanValue), true));
		}

	}
}