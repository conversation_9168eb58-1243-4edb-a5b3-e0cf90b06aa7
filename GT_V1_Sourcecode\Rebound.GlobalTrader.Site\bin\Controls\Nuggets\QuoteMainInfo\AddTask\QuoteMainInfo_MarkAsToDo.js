Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo=function(n){Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.initializeBase(this,[n]);this._intQuoteID=null;this._intMessageID=null;this._ctlToDoID=null;this._quoteMinReminderDate=null;this._quoteStatus=null};Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this._ctlToDo=this.getField("ctlToDo")},dispose:function(){this.isDisposed||(this._ctlToDo=null,this._ctlToDoID=null,this._intQuoteID=null,this._intMessageID=null,this._quoteMinReminderDate=null,this._quoteStatus=null,Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._ctlToDo=$find(this.getField("ctlToDo").ID),this._ctlToDoID=this._ctlToDo._element.id,this._ctlToDo._ctlRelatedForm=this,this._ctlToDo.setupReminderClick());this._ctlToDo._intQuoteID=this._intQuoteID;this._ctlToDo._intMessageID=this._intMessageID;this._ctlToDo._intCategoryID=2;this._ctlToDo._quoteMinReminderDate=this._quoteMinReminderDate;this._ctlToDo._quoteStatus=this._quoteStatus;this.setFieldValue("ctlReminder",!0);this._ctlToDo.selectReminder();this.getFieldDropDownData("ctlToDoListType")},saveClicked:function(){if(!this.validateForm())return!1;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/QuoteMainInfo");n.set_DataObject("QuoteMainInfo");n.set_DataAction("AddToDoTask");n.addParameter("QuoteNo",this._intQuoteID);this._ctlToDo.addFieldsToDataObject(n);n.addDataOK(Function.createDelegate(this,this.saveOK));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},validateForm:function(){this.onValidate();var n=this._ctlToDo.validateFields();return n||this.showError(!0),n},saveError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveOK:function(n){n._result.Result?this.onSaveComplete():this.saveError(n)},getFormControlID:function(n,t){return String.format("#{0}_{1}",n,t)}};Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);