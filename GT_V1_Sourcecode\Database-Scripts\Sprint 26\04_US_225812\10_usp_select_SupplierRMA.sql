/*     
===========================================================================================    
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION    
[US-225812]     Trung Pham		 20-Apr-2025		UPDATE		Get country and related warning message 
===========================================================================================    
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_SupplierRMA]        
--        
@SupplierRMAId int        
--        
AS        
--        
 declare @RefNumber INT      
 declare @ParentId int      
      
 SELECT @ParentId = ClientSupplierRMANo from tbSupplierRMA where SupplierRMAId = @SupplierRMAId      
 IF ISNULL(@ParentId,0) <> 0      
 BEGIN      
   SELECT @RefNumber = SupplierRMANumber from tbSupplierRMA where SupplierRMAId = @ParentId      
 END      
 ELSE      
 BEGIN      
   SELECT @RefNumber = SupplierRMANumber from tbSupplierRMA where ClientSupplierRMANo = @SupplierRMAId      
 END      
      
SELECT  vw.SupplierRMAId            
      , vw.ClientNo            
      , vw.SupplierRMANumber            
      , vw.PurchaseOrderNo            
      , vw.PurchaseOrderNumber            
      , vw.AuthorisedBy            
      , vw.AuthoriserName            
      , vw.SupplierRMADate            
      , vw.Notes            
      , vw.Instructions            
      , vw.ShipViaNo            
      , vw.ShipViaName            
      , vw.Account            
      , vw.ShipToAddressNo            
      , vw.Reference            
      , vw.CompanyNo            
      --,CASE WHEN vw.ClientSupplierRMANo IS NOT NULL THEN vw.CompanyNo  ELSE           
      --(SELECT CASE WHEN ipo.CompanyNo IS NULL THEN vw.CompanyNo ELSE ipo.CompanyNo END FROM tbInternalPurchaseOrder ipo WHERE ipo.PurchaseOrderNo = vw.PurchaseOrderNo) END AS CompanyNo              
      , vw.CompanyName           
      --,CASE WHEN vw.ClientSupplierRMANo IS NOT NULL THEN vw.CompanyName  ELSE           
      --(SELECT CASE WHEN c.CompanyName IS NULL THEN vw.CompanyName ELSE c.CompanyName END FROM tbCompany c JOIN tbInternalPurchaseOrder ipo ON c.CompanyId = ipo.CompanyNo WHERE ipo.PurchaseOrderNo = vw.PurchaseOrderNo) END AS CompanyName              
      , vw.FullCompanyName            
      , vw.ContactNo            
      , vw.ContactName            
      , vw.DivisionNo            
      , vw.DivisionName            
      , vw.UpdatedBy            
      , vw.DLUP            
      , vw.CurrencyNo            
      , vw.CurrencyCode            
      , vw.CurrencyDescription            
      , lg.TeamNo            
      , vw.CustomerCode            
      , vw.TaxNo            
      , vw.TaxName            
      , vw.Buyer            
      , vw.Quantity            
      , vw.QuantityShipped            
    ,  vw.IncotermNo            
   ,   vw.IncotermName             
    , vw.ClientSupplierRMANo           
          , vw.InternalPurchaseOrderNo            
      , vw.InternalPurchaseOrderNumber       
,lg.EmployeeName as POBuyerName        
, vw.IPOCompanyNo       
, vw.IPOCompanyName       
, @RefNumber as RefNumber      
,vw.ishublocked   
, vw.AS6081 as AS6081  
, ct.CountryName
, swm.WarningText
, CAST(CASE 
		WHEN EXISTS (SELECT 1 FROM tbSystemWarningMessage WHERE SystemWarningMessageId = swm.SystemWarningMessageId )
		THEN 1 ELSE 0 END AS BIT
	) AS IsHasCountryMessage
FROM  dbo.vwSupplierRMA vw      
LEFT JOIN tbLogin lg on vw.Buyer = lg.LoginId      
LEFT JOIN dbo.tbCompany c ON c.CompanyId = vw.CompanyNo
LEFT JOIN dbo.tbCompanyAddress ca ON c.CompanyId = ca.CompanyNo AND ca.DefaultBilling = 1
 LEFT JOIN dbo.tbAddress bad ON ca.AddressNo = bad.AddressId
 LEFT JOIN dbo.tbCountry ct ON bad.CountryNo = ct.CountryId AND ISNULL(ct.InActive, 0) = 0
 LEFT JOIN dbo.tbSystemWarningMessage swm ON ct.CountryId = swm.ApplyTo AND swm.ClientNo = vw.ClientNo AND ISNULL(swm.InActive,0) = 0
WHERE SupplierRMAId = @SupplierRMAId      