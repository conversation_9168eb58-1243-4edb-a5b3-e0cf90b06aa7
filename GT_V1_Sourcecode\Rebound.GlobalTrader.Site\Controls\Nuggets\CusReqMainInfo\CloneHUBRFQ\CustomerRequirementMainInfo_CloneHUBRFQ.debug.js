///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intCurrencyID = -1;
    this._intCompanyID = -1;
    this._blnReqValidated = true;
    this._ctlCompany = "";
    this._strCustomerRequirementNumber = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.prototype = {
    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (value) { if (this._intCustomerRequirementID !== value) this._intCustomerRequirementID = value; },
    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));

    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intCustomerRequirementID = null;
        this._intCurrencyID = -null;
        this._intCompanyID = null;
        this._ctlCompany = "";
        this._strCustomerRequirementNumber = "";
        Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.callBaseMethod(this, "dispose");
    },



    formShown: function () {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
        this.getFieldControl("ctlBOMHeader")._intCompanyID = this._intCompanyID;
        this.getFieldDropDownData("ctlBOMHeader");
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_lblCustomer").text(this._ctlCompany);
        $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_lblCustReqNo").text(this._strCustomerRequirementNumber);
        $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl23_ibtnBack_hyp"), "click", Function.createDelegate(this, this.noClicked1));
        $addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl24_ibtnBack_hyp"), "click", Function.createDelegate(this, this.noClicked1));
    },

    noClicked1: function () {
        this.onNotConfirmed();
    },

    yesClicked: function () {
        if (!this.validateForm()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("CloneRequirementDataHUBRFQ");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addParameter("HUBRFQId", this.getFieldValue("ctlBOMHeader"));
        obj.addDataOK(Function.createDelegate(this, this.saveCloneHUBRFQComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function () {
        this.showSaving(false);
        this.onNotConfirmed();
    },
    validateForm: function () {
        this.onValidate();
        var blnOK = true;
        if (!this.checkFieldEntered("ctlBOMHeader")) blnOK = false;
        if (this._blnReqValidated == false) {
            blnOK = false;
            this.showError(true, "Some mandatory data is missing from this requirement. Please go back and fill in the missing data.");
        }

        return blnOK;
    },

    saveError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveCloneHUBRFQComplete: function (args) {
        var res = args._result;
        // alert(args._result.Result);
        if (args._result.Result > 0) {
            //this.showSavedOK(true);
            //this.onSaveComplete();
            //[001] Start Here
            //window.location.href = ("Ord_CusReqDetail.aspx?req=" + args._result.Result)
            this.showSavedOK(true);
            location.href = $RGT_gotoURL_CustomerRequirement(args._result.Result);

        } else {
            this.showError(true, "Error!!");
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
