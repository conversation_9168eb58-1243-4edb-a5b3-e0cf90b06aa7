using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

/*
Marker     Changed by      Date         Remarks
[001]       Bhooma Nand   28/01/2021       Added New Nugget for CRM prospects
*/
namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CompanyProspects : Base {

		#region Locals

		protected IconButton _ibtnEdit;
        protected IconButton _ibtnAdd;
        protected IconButton _ibtnViewTask;

        #endregion

        #region Properties
        private int _intCustomerID = -1;
		public int CustomerID {
			get { return _intCustomerID; }
			set { _intCustomerID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

      
		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CompanyProspects.CompanyProspects.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CompanyProspects");
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnEdit.Visible = _blnCanEdit;
            _ibtnAdd.Visible = true;
            _ibtnViewTask.Visible = true;
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyProspects", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", FindIconButton("ibtnEdit").ClientID);
			 _scScriptControlDescriptor.AddElementProperty("ibtnAdd", FindIconButton("ibtnAdd").ClientID);
			_scScriptControlDescriptor.AddProperty("intCustomerID", _intCustomerID);
            _scScriptControlDescriptor.AddElementProperty("ibtnViewTask", FindIconButton("ibtnViewTask").ClientID);
        }

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
            _ibtnEdit = FindIconButton("ibtnEdit");
            _ibtnAdd = FindIconButton("ibtnAdd");
            _ibtnViewTask = FindIconButton("ibtnViewTask");
		}


	}
}
