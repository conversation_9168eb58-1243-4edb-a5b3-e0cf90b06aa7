using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class ClientImportBOMMainInfo_Delete : Base
    {
		#region Locals

		#endregion

		#region Properties

		private int _intBOMID;
		public int LineID {
			get {
				return _intBOMID;
			}
			set {
				_intBOMID = value;
			}
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "BOMMainInfo_Delete");
			AddScriptReference("Controls.Nuggets.BOMMainInfo.Delete.BOMMainInfo_Delete");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Delete", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
		}
	}
}