﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		25-Sep-2024		Create		Get column mapping for supplier
[US-204867]		An.TranTan		03-Oct-2024		Update		Update get supplier default currency
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE usp_select_ProspectiveOfferMappedColumn                
	@SupplierNo INT          
AS                
BEGIN
	DECLARE @DefaultCurrency INT;
	SELECT @DefaultCurrency = ISNULL(POCurrencyNo, 0) FROM tbCompany WITH(NOLOCK) WHERE CompanyId = @SupplierNo;

	IF EXISTS (
		SELECT TOP 1 1 FROM BorisGlobalTraderimports.dbo.tbProspectiveOffer_ColumnMapping WITH(NOLOCK)
		WHERE SupplierNo = @SupplierNo
	)
	BEGIN
		SELECT    
			Manufacturer,
			Part,
			Quantity,
			Price,
			[Description],
			AlterPart,
			DateCode,
			Product,
			Package,
			ROHS,
			SupplierPart,
			@DefaultCurrency AS FixedCurrencyNo   
		FROM BorisGlobalTraderimports.dbo.tbProspectiveOffer_ColumnMapping WITH(NOLOCK)
		WHERE SupplierNo = @SupplierNo
	END
	ELSE BEGIN
		SELECT    
			NULL AS Manufacturer,
			NULL AS Part,
			NULL AS Quantity,
			NULL AS Price,
			NULL AS [Description],
			NULL AS AlterPart,
			NULL AS DateCode,
			NULL AS Product,
			NULL AS Package,
			NULL AS ROHS,
			NULL AS SupplierPart,
			@DefaultCurrency AS FixedCurrencyNo 
	END
END