/*
 Marker     ChangedBy       Date            Remarks
 [001]      <PERSON><PERSON><PERSON>     17-Aug-2018     Provision to add Global Security in Sales Order
 */
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class OGELLinesExport : Base {
		//[001] start
		#region Properties
		protected IconButton _ibtnExportCSV;
		private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }
        #endregion
        //[001] end

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
            //[001] start
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
            //[001] end
			SetDataListNuggetType("OGELLinesExport");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "OGELLinesExport");
			AddScriptReference("Controls.DataListNuggets.OGELLinesExport.OGELLinesExport");
			WireUpControls();
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intSalesPersonID", _objQSManager.SalesPersonID > 0 ? _objQSManager.SalesPersonID : 0);
            //[001] start
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
			_scScriptControlDescriptor.AddElementProperty("ibtnExportCSV", _ibtnExportCSV.ClientID);
			//[001] end
			base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            if (!string.IsNullOrEmpty(strViewLevel)) {
                ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
                _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
                this.OnAskPageToChangeTab();
            }
            base.RenderAdditionalState();
		}

		#endregion
		private void WireUpControls()
		{
			_ibtnExportCSV = (IconButton)FindIconButton("ibtnExportCSV");

		}
		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("SONumber", Unit.Pixel(90), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "CustomerPurchaseOrderNo", Unit.Pixel(150), true));
			_tbl.Columns.Add(new FlexiDataColumn("ShipStatus", "DatePromised", Unit.Pixel(90), true));
			_tbl.Columns.Add(new FlexiDataColumn("SOLineNo", Unit.Pixel(50), true));
			_tbl.Columns.Add(new FlexiDataColumn("AirWayBill", Unit.Pixel(100), true));
			_tbl.Columns.Add(new FlexiDataColumn("ECCNCode", Unit.Pixel(90), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", Unit.Pixel(150), true));
			_tbl.Columns.Add(new FlexiDataColumn("OGELNumber", Unit.Pixel(100), true));
			_tbl.Columns.Add(new FlexiDataColumn("OGEL_MilitaryUse", Unit.Pixel(50), true));
			_tbl.Columns.Add(new FlexiDataColumn("OGEL_EndDestinationCountry", Unit.Empty, true));
		}

	}
}
