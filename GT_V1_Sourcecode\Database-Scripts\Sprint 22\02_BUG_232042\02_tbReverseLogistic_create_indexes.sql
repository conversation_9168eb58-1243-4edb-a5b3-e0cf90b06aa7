﻿USE [BorisGlobalTraderImports]
GO

SET ANSI_PADDING ON
GO

/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[Bug-232042]	Ngai To				20-Feb-2025		Update			Bug 232042: [PROD Bug] RL Import History not working Prod BUG
==========================================================================================================
*/

IF NOT EXISTS (SELECT * FROM sys.indexes
		WHERE name = 'IX_tbReverseLogistic_ImportId_Inactive'
			AND object_id = OBJECT_ID('tbReverseLogistic')
		)
BEGIN
	CREATE NONCLUSTERED INDEX [IX_tbReverseLogistic_ImportId_Inactive] ON [dbo].[tbReverseLogistic] ([ImportId] ASC) INCLUDE (
		[Inactive],
		[UpdatedBy]
		)
		WITH (
				PAD_INDEX = OFF,
				STATISTICS_NORECOMPUTE = OFF,
				SORT_IN_TEMPDB = OFF,
				DROP_EXISTING = OFF,
				ONLINE = OFF,
				ALLOW_ROW_LOCKS = ON,
				ALLOW_PAGE_LOCKS = ON
				) ON [PRIMARY]
END