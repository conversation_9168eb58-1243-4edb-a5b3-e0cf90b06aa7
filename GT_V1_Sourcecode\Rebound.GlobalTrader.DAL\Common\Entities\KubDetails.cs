﻿using System.Collections.Generic;

//Anuj
namespace Rebound.GlobalTrader.DAL
{
    public class KubAssistanceDetails
    {
        public System.String PartNo { get; set; }
        public System.String TotalPartsTreaded { get; set; }
        public System.String TotalGP { get; set; }
        public System.String AverageBestPrice { get; set; }
        public System.String LastUpdatedDate { get; set; }
        public System.Boolean? IsClientPrice { get; set; }
        public System.String TotalGPbasedLastActualBuyPrice { get; set; }
        public System.String LowestSalesPriceLast12Months { get; set; }
        public System.String NumberOfPartsSoldLast12months { get; set; }
        public System.String LastEnquiredDateOfPart { get; set; }
        public System.String LastQuotedPrice { get; set; }
        public System.String LastSoldPrice { get; set; }
        public System.String LastHubprice { get; set; }

        public System.String NumberOfRequirement  { get; set; }
        public System.String LastestRequirementDate { get; set; }
        public System.String LastestRequirementNumber { get; set; }
        public System.String LastHighestSoldPrice { get; set; }
        public System.String LastLowestSoldPrice { get; set; }
        public System.String NumberOfInvoice { get; set; }
        public System.String LastestHubRFQName { get; set; }
        public System.DateTime LastestHubNumberDate { get; set; }
        public System.String LastestHubRFQId { get; set; }
        public System.String NumberOfQuote { get; set; }
        public System.String NumberQuoteToSalesOrder { get; set; }
        public System.String LastDatePartSoldToBomCustomer { get; set; }
        public System.String IHSResultForPartNo { get; set; }
        public System.String LyticaResultForPartNo { get; set; }
    }

    public class KubCountryWiseSaleDetails
    {
        public System.String Countries { get; set; }
        public System.String NoOfSales { get; set; }
        public System.String ReSale { get; set; }
        public System.String UnShippedReSale { get; set; }
        public System.String UnShippedNoOfSales { get; set; }
        public System.String InvoiceId { get; set; }
        public System.String InvoiceNumber { get; set; }
        public System.String Quantity { get; set; }
        public System.String ShippingCost { get; set; }
        public System.String Freight { get; set; }
        public System.String LandedCost { get; set; }
        public System.String InvoiceValue { get; set; }
        public System.String TOTAL { get; set; }
    }

    public class KubTop3BuyPriceDetails
    {
        public System.String POID { get; set; }
        public System.String PONo { get; set; }
        public System.String Date { get; set; }
        public System.String Price { get; set; }
        public System.Boolean? IsClientPrice { get; set; }
    }

    public class KubLast10RecentQuoteDetails
    {
        public System.String QuoteID { get; set; }
        public System.String QuoteNumber { get; set; }
        public System.Boolean? IsAllowedEnable { get; set; }
        public System.String QuoteDate { get; set; }
        public System.String Quantity { get; set; }
        public System.String UnitPrice { get; set; }
        public System.String BuyPrice { get; set; }
        public System.String Profit { get; set; }

    }

    public class SQLAllowedEnabledForHUB
    {
        public System.Boolean? IsAllowedEnable { get; set; }
    }

    public class KubAvgPriceDetails
    {
        public System.String AveragePriceOfPartLast12Months { get; set; }
        public System.String LastUpdatedDate
        {
            get; set;
        }
    }

    public class KubTotalLineInvoiceDetails
    {
        public System.String TotalNoOfLinesInvoicedIn12Months { get; set; }
    }

    public class KubMainProductGroupDetails
    {
        public System.String MainPoductGroup { get; set; }
    }

    public class KubTop20CustomeRequirementForBOM
    {
        public System.String BOMId { get; set; }
        public System.String BOMName { get; set; }
        public System.String Price { get; set; }
        public System.String Quantity { get; set; }
        public System.String CurrencyCode { get; set; }
        public System.String QuoteId { get; set; }
        public System.String QuoteNumber { get; set; }
        public System.String DLUP { get; set; }
        public System.String Quoted { get; set; }
    }

    public class SqlKubTop10QuoteForBOM
    {
        public System.String QuoteId { get; set; }
        public System.String QuoteNumber { get; set; }
        public System.String Quantity { get; set; }
        public System.String Price { get; set; }
        public System.String QuoteLineId { get; set; }
        public System.String SalesOrderNo { get; set; }
        public System.String SalesOrderNumber { get; set; }
        public System.String CurrencyCode { get; set; }
        public System.String DLUP { get; set; }
        public System.String ConvertedToSO { get; set; }
    }

    public class KubStockDetailsForBOM
    {
        public List<StockOffer> Top10ReverseLogisticsOffer { get; set; } = new List<StockOffer>();

        public List<StockOffer> Top10StrategicStockOffer { get; set; } = new List<StockOffer>();
        public List<StockDetailsForPart> Top20RecentStockForPart { get; set; } = new List<StockDetailsForPart>();
    }

    public class StockOffer
    {
        public string DateAdded { get; set; }
        public int Quantity { get; set; }
        public string Price { get; set; }
        public string Manufacturer { get; set; }
    }


    public class StockDetailsForPart
    {
        public string StockId { get; set; }
        public int Quantity { get; set; }
        public string Price { get; set; }
        public string Client { get; set; }
    }

}
