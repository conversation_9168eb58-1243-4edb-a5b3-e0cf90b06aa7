///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail = function(el) {
    Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.initializeBase(this, [el]);
    //this.getClosedStatus=true;
};

Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.prototype = {
    get_intBOMID: function() { return this._intBOMID; }, set_intBOMID: function(v) { if (this._intBOMID !== v) this._intBOMID = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlBOMItems: function() { return this._ctlBOMItems; }, set_ctlBOMItems: function(v) { if (this._ctlBOMItems !== v) this._ctlBOMItems = v; },
   // get_ctlClientBOMSourcingResults: function() { return this._ctlClientBOMSourcingResults; }, set_ctlClientBOMSourcingResults: function(v) { if (this._ctlClientBOMSourcingResults !== v) this._ctlClientBOMSourcingResults = v; },
    get_ctlPOHubSourcing: function() { return this._ctlPOHubSourcing; }, set_ctlPOHubSourcing: function(v) { if (this._ctlPOHubSourcing !== v) this._ctlPOHubSourcing = v; },
    get_ctlClientBomCSV: function() { return this._ctlClientBomCSV; }, set_ctlClientBomCSV: function(v) { if (this._ctlClientBomCSV !== v) this._ctlClientBomCSV = v; },
    //get_pnlStatus: function() { return this._pnlStatus; }, set_pnlStatus: function(v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    get_lblStatus: function() { return this._lblStatus; }, set_lblStatus: function(v) { if (this._lblStatus !== v) this._lblStatus = v; },
    get_ctlClientBOMCsvExportHistory: function() { return this._ctlClientBOMCsvExportHistory; }, set_ctlClientBOMCsvExportHistory: function(v) { if (this._ctlClientBOMCsvExportHistory !== v) this._ctlClientBOMCsvExportHistory = v; },
   // get_getClosedStatus: function() { return this._getClosedStatus; }, set_getClosedStatus: function(v) { if (this._getClosedStatus !== v) this._getClosedStatus = v; },
    get_btnPrint: function () { return this._btnPrint; }, set_btnPrint: function (v) { if (this._btnPrint !== v) this._btnPrint = v; },
    //    get_ibtnAssignToMe: function() { return this._ibtnAssignToMe; }, set_ibtnAssignToMe: function(v) { if (this._ibtnAssignToMe !== v) this._ibtnAssignToMe = v; },
    get_ctlExpediteHistory: function () { return this._ctlExpediteHistory; }, set_ctlExpediteHistory: function (v) { if (this._ctlExpediteHistory !== v) this._ctlExpediteHistory = v; },
    get_ctlClientBOMHubImagesDragDrop: function () { return this._ctlClientBOMHubImagesDragDrop; }, set_ctlClientBOMHubImagesDragDrop: function (v) { if (this._ctlClientBOMHubImagesDragDrop !== v) this._ctlClientBOMHubImagesDragDrop = v; },

    get_ctlBomCSV: function () { return this._ctlBomCSV; }, set_ctlBomCSV: function (v) { if (this._ctlBomCSV !== v) this._ctlBomCSV = v; },

    initialize: function() {
        //    if (this._ibtnAssignToMe) {
        //        $R_IBTN.addClick(this._ibtnAssignToMe, Function.createDelegate(this, this.assignToMe));
        //        }
        Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.callBaseMethod(this, "initialize");
    },
    

    goInit: function () {
        
        if (this._btnPrint) {
            this._btnPrint.addPrint(Function.createDelegate(this, this.printHUBRFQ));
            this._btnPrint.addEmail(Function.createDelegate(this, this.emailHUBRFQ));
            this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        }
        if (this._ctlMainInfo) this._ctlMainInfo.addGotData(Function.createDelegate(this, this.mainBOMDetailGotDataOK));
        //alert(this.getClosedStatus);
        if (this._ctlBOMItems) this._ctlBOMItems.addPartSelected(Function.createDelegate(this, this.selectPart));
        if (this._ctlBOMItems) this._ctlBOMItems.addStartGetData(Function.createDelegate(this, this.mainInfoStartGetData));
        if (this._ctlBOMItems) this._ctlBOMItems.addGotDataOK(Function.createDelegate(this, this.mainInfoGotDataOK));
        if (this._ctlBOMItems) this._ctlBOMItems.addGetDataComplete(Function.createDelegate(this, this.ctlBOMItems_GetDataComplete));
       // if (this._ctlClientBOMSourcingResults) this._ctlClientBOMSourcingResults.addAddFormShown(Function.createDelegate(this, this.ctlClientBOMSourcingResults_AddFormShown));
       // if (this._ctlClientBOMSourcingResults) this._ctlClientBOMSourcingResults.addGotDataOK(Function.createDelegate(this, this.ctlClientBOMSourcingResults_GetDataComplete));
        if (this._ctlBOMItems) this._ctlBOMItems.addCallBeforeRelease(Function.createDelegate(this, this.ctlBOMItems_addCallBeforeRelease));
        if (this._ctlMainInfo) this._ctlMainInfo.addCallBeforeRelease(Function.createDelegate(this, this.ctlMainInfo_addCallBeforeRelease));

        if (this._ctlBOMItems) this._ctlBOMItems.addRefereshAfterRelease(Function.createDelegate(this, this.ctlBOMItems_RefereshAfterRelease));


        //if (this._ctlMainInfo) { this._ctlBOMItems.disableItemAddButton(this._ctlMainInfo._isAddButtonEnable) };
        if (this._ctlPOHubSourcing) { this._ctlPOHubSourcing.addSourcingResultAdded(Function.createDelegate(this, this.ctlSourcing_SourcingResultAdded)) };
       // if (this._ctlClientBOMSourcingResults) { this._ctlClientBOMSourcingResults.addSaveEditComplete(Function.createDelegate(this, this.ctlClientBOMSourcingResults_SaveEditComplete)) };
       // if (this._ctlClientBOMSourcingResults) { this._ctlClientBOMSourcingResults.addSourcingResultDeleted(Function.createDelegate(this, this.ctlClientBOMSourcingResults_SourcingResultDeleted)) };
       // if (this._ctlClientBOMSourcingResults) this._ctlClientBOMSourcingResults.addCallBeforeRelease(Function.createDelegate(this, this.ctlClientBOMSourcingResults_addCallBeforeRelease));
       // if (this._ctlClientBOMSourcingResults) { this._ctlClientBOMSourcingResults.addSourcingResultSelect(Function.createDelegate(this, this.ctlClientBOMSourcingResults_SourcingResultSelect)) };
        // this.setFieldsFromMainInfo();
        if (this._ctlBomCSV) { this._ctlBomCSV.getData() };
        Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        //if (this._ctlClientBOMSourcingResults) this._ctlClientBOMSourcingResults.dispose();
        if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.dispose();
        // if (this._ctlLines) this._ctlLines.dispose();
        this._ctlMainInfo = null;
       // this._ctlClientBOMSourcingResults = null;
        this._ctlBOMItems = null;
        this._ctlPOHubSourcing = null;
        this._ctlClientBomCSV = null;
        this._pnlStatus = null;
        this._lblStatus = null;
        this._ctlClientBOMCsvExportHistory = null;
        this._ctlExpediteHistory = null;
        this._ctlClientBOMHubImagesDragDrop = null;
        this._ctlBomCSV = null;
        Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.callBaseMethod(this, "dispose");
    },

    selectPart: function () {
        if (!this._ctlBOMItems._isClosed) {
            // alert("lo");
            // alert(this._ctlBOMItems._isNoBid);
            this._intCustomerRequirementID = this._ctlBOMItems._intCustomerRequirementID;
            //this._ctlClientBOMSourcingResults._blnRequirementClosed = this._ctlBOMItems._blnRequirementClosed;
            //this._ctlClientBOMSourcingResults._intCustomerRequirementID = this._intCustomerRequirementID;
            //this._ctlClientBOMSourcingResults._strPartNo = this._ctlBOMItems.getSelectedPartNo();
            //this._ctlClientBOMSourcingResults._ClientNo = this._ctlBOMItems.getIPOClientNo();
            //this._ctlClientBOMSourcingResults.getData();
            if (this._ctlPOHubSourcing) this._ctlPOHubSourcing._intCustomerRequirementID = this._intCustomerRequirementID;
            if (this._ctlPOHubSourcing) this._ctlPOHubSourcing._blnRequirementClosed = this._ctlBOMItems._blnRequirementClosed;
            if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.selectPart(this._ctlBOMItems.getSelectedPartNo());
            if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.updateIPOClientId(this._ctlBOMItems.getIPOClientNo());
            if (this._ctlPOHubSourcing) this._ctlPOHubSourcing._blnRequirementReleased = this._ctlBOMItems._blnRequirementReleased || (this._ctlBOMItems._isNoBid && this._ctlBOMItems._blnPOHub);
            //  if (this._ctlPOHubSourcing) this._ctlPOHubSourcing._blnRequirementReleased = this._ctlBOMItems._isNoBid;


        }

    },

    ctlSourcing_SourcingResultAdded: function() {
        this._ctlMainInfo.getData();
        //this._ctlClientBOMSourcingResults.getData();
        if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.hideAddForm();
    },

    ctlClientBOMSourcingResults_SourcingResultDeleted: function() {
        this._ctlMainInfo.getData();
        this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult);
    },

    mainInfoStartGetData: function() {
        if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.show(false);
        //this._ctlClientBOMSourcingResults.show(false);
        if(this._ctlClientBOMHubImagesDragDrop) this._ctlClientBOMHubImagesDragDrop.show(false);
    },
    mainInfoGotDataOK: function () {
     
    //  if (this._ctlClientBOMSourcingResults) this._ctlClientBOMSourcingResults._intCompanyID = this._ctlBOMItems._intCompanyID;
    //    // if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.show(!this._ctlBOMItems._blnRequirementReleased);
    //    if (this._ctlPOHubSourcing) this._ctlPOHubSourcing.show(!this._ctlMainInfo._inActive && !this._ctlBOMItems._isClosedOnly);
    //    this._ctlClientBOMSourcingResults.show(!this._ctlMainInfo._inActive );

    //    this._ctlClientBOMSourcingResults._hasReleasedItem = !this._ctlBOMItems._blnRequirementReleased;
    // $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidDisplayStatus"));
    //    //this._ctlClientBOMSourcingResults._hasReleasedItem =!this._ctlBOMItems._isNoBid;

    //    if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._Quantity = this._ctlBOMItems.getFieldValue("ctlQuantity");
    //    if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._PackageID = this._ctlBOMItems.getFieldValue("hidPackageID");
    //    if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._ProductID = this._ctlBOMItems.getFieldValue("hidProductID");
    //    if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._ROHS = this._ctlBOMItems.getFieldValue("hidROHS");
    //    if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._Manufacturer = this._ctlBOMItems.getFieldValue("hidManufacturer");
    //    if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._ManufacturerNo = this._ctlBOMItems.getFieldValue("hidManufacturerNo");
    //    if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._DateCode = this._ctlBOMItems.getFieldValue("ctlDateCode");
    //    if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._Product = this._ctlBOMItems.getFieldValue("ctlProduct");
     },
        

    ctlBOMItems_RefereshAfterRelease: function() {
        this._ctlMainInfo.getData();
    },

    mainBOMDetailGotDataOK: function () {
        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidDisplayStatus"));
        //alert(this._ctlMainInfo.getFieldValue("hidDisplayStatus"));
        if(this._ctlMainInfo.getFieldValue("hidDisplayStatus"))
        this._ctlBOMItems._inActive = this._ctlMainInfo._inActive;
        this._ctlBOMItems._blnRequestedToPoHub = this._ctlMainInfo._blnRequestedToPoHub;
        //alert(this._ctlMainInfo._isClosed);
        this._ctlBOMItems._isClosed =   this._ctlMainInfo._isClosed; 
        this._ctlBOMItems._isClosedOnly = this._ctlMainInfo.getFieldValue("hidDisplayStatus") == 'CLOSED' ? true : false;
        this._ctlBOMItems._intContact2No = this._ctlMainInfo._intContact2No;
        this._ctlBOMItems._intClientStatus = this._ctlMainInfo._ClientBOMStatus;
        this._ctlBOMItems._intClientCompanyId = this._ctlMainInfo._ClientCompanyId;
       
        if (this._ctlBOMItems) this._ctlBOMItems.getData();
        
    },

    ctlBOMItems_GetDataComplete: function() {
        //this._ctlBOMItems.disableItemAddButton(!this._ctlMainInfo._inActive);
        this._ctlMainInfo._blnHasRequirement = (this._ctlBOMItems._intCountStock > 0);
        this._ctlMainInfo.enableButtons(true);
        if (this._ctlMainInfo._isPurchaseHub) {
            this._ctlMainInfo.disableNotifyAndExportButton(!this._ctlMainInfo._isPurchaseHub);
        }
        this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult);
        if (this._ctlExpediteHistory) this._ctlExpediteHistory.GetExpediteHistory();
       // alert("Rferes");
    },

    ctlClientBOMSourcingResults_GetDataComplete: function() {
        //this._ctlBOMItems._blnAllHasDelDate = this._ctlClientBOMSourcingResults._blnAllHasDelDate;
        //this._ctlMainInfo._blnAllHasDelDate = this._ctlClientBOMSourcingResults._blnAllHasDelDate;
        //this._ctlBOMItems._blnAllHasProduct = this._ctlClientBOMSourcingResults._blnAllHasProduct;
        //this._ctlMainInfo._blnAllHasProduct = this._ctlClientBOMSourcingResults._blnAllHasProduct;
        //to bind data on Add Quote to CLient load
        //Comment on 17 may : seems default selection not working
        //if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._Quantity = this._ctlBOMItems.getFieldValue("ctlQuantity");
        //if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._PackageID = this._ctlBOMItems.getFieldValue("hidPackageID");
        //if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._ProductID = this._ctlBOMItems.getFieldValue("hidProductID");
        //if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._ROHS = this._ctlBOMItems.getFieldValue("hidROHS");
        //if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._Manufacturer = this._ctlBOMItems.getFieldValue("hidManufacturer");
        //if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._ManufacturerNo = this._ctlBOMItems.getFieldValue("hidManufacturerNo");
        //if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._DateCode = this._ctlBOMItems.getFieldValue("ctlDateCode");
        //if (this._ctlPOHubSourcing) this._ctlClientBOMSourcingResults._Product = this._ctlBOMItems.getFieldValue("ctlProduct");
    },
    ctlClientBOMSourcingResults_addCallBeforeRelease: function () {
       // this._ctlClientBOMSourcingResults.clearMessages();
       // var isValidSourcing = this.validateDeliveryDate(this._ctlClientBOMSourcingResults._blnAllHasDelDate && this._ctlClientBOMSourcingResults._blnAllHasProduct);
       // this._ctlClientBOMSourcingResults._blnCanRelease = isValidSourcing;
    },
    ctlBOMItems_addCallBeforeRelease: function() {
       // this._ctlClientBOMSourcingResults.clearMessages();
        var isValidSourcing = this.validateDeliveryDate(this._ctlBOMItems._blnAllHasDelDate && this._ctlBOMItems._blnAllHasProduct);
       // var isProduct = this.validateProduct(this._ctlBOMItems._blnAllHasProduct);
        //alert(isDeliveryDate + " "+ isProduct);
         this._ctlBOMItems._blnCanRelease = isValidSourcing;
       // this._ctlBOMItems._blnCanRelease = (isDeliveryDate == true && isProduct == true);
        //this._ctlBOMItems._blnCanRelease = this.validateDeliveryDate(this._ctlBOMItems._blnAllHasDelDate);
    },
    ctlMainInfo_addCallBeforeRelease: function() {
        this._ctlBOMItems.clearMessages();
        var isValidSourcing = this.validateReqItemDeliveryDate(this._ctlBOMItems._blnAllItemHasDelDate && this._ctlBOMItems._blnAllItemHasProduct);
        //var isProduct = this.validateReqItemProduct(this._ctlBOMItems._blnAllItemHasProduct);
        this._ctlMainInfo._blnCanReleaseAll = isValidSourcing;
        //this._ctlMainInfo._blnCanReleaseAll = this.validateReqItemDeliveryDate(this._ctlBOMItems._blnAllItemHasDelDate);
    },

    validateDeliveryDate: function(bln) {
       // this._ctlClientBOMSourcingResults.clearMessages();
        if (bln) {
            return true;
        }
        else {

          //  this._ctlClientBOMSourcingResults.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },

    validateReqItemDeliveryDate: function(bln) {
        this._ctlBOMItems.clearMessages();
        if (bln) {
            return true;
        }
        else {

            this._ctlBOMItems.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },
    validateProduct: function(bln) {
      //  this._ctlClientBOMSourcingResults.clearMessages();
        if (bln) {
            return true;
        }
        else {

         //   this._ctlClientBOMSourcingResults.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },

    validateReqItemProduct: function(bln) {
        this._ctlBOMItems.clearMessages();
        if (bln) {
            return true;
        }
        else {

            this._ctlBOMItems.addMessage("Delivery date and product is required for all attached sourcing results", $R_ENUM$MessageTypeList.Error)
            return false;
        }

    },
    ctlClientBOMSourcingResults_SaveEditComplete: function() {
        //this._ctlBOMItems.getData();
        this._ctlMainInfo.getData();
        //this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult);
       
    },

    ctlMainInfo_SaveEditComplete: function() {
        this._ctlLines.getData();

    },
    printHUBRFQ: function () {
      //  alert(this._intBOMID);
        this._BomId = this._ctlMainInfo._intBOMID;
       // alert(this._BomId);
        $R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBRFQ, this._BomId);
    },
    emailHUBRFQ: function () {
        this._BomId = this._ctlMainInfo._intBOMID;
        $R_FN.openPrintWindow($R_ENUM$PrintObject.EmailHUBRFQ, this._BomId,true);
    },
    printOtherDocs: function () {
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._BomId, false, "PrintHUBRFQ");
    },
    ctlClientBOMSourcingResults_AddFormShown: function () {
       // this._ctlClientBOMSourcingResults._frmAdd.setFieldValue("ctlMSL", this._ctlBOMItems.getFieldValue("hidMSL"));
        //        this._ctlClientBOMSourcingResults._frmAdd.setFieldValue("ctlManufacturer", this._ctlMainInfo.getFieldValue("hidManufacturerNo"), null, this._ctlMainInfo.getFieldValue("hidManufacturer"));
        //        this._ctlClientBOMSourcingResults._frmAdd.setFieldValue("ctlDateCode", this._ctlMainInfo.getFieldValue("ctlDateCode"));
        //        this._ctlClientBOMSourcingResults._frmAdd.setFieldValue("ctlProduct", this._ctlMainInfo.getFieldValue("hidProductID"));
        //        this._ctlClientBOMSourcingResults._frmAdd.setFieldValue("ctlPackage", this._ctlMainInfo.getFieldValue("hidPackageID"));
        //        this._ctlClientBOMSourcingResults._frmAdd.setFieldValue("ctlQuantity", this._ctlMainInfo.getFieldValue("ctlQuantity"));
        //        this._ctlClientBOMSourcingResults._frmAdd.setFieldValue("ctlCurrency", this._ctlMainInfo.getFieldValue("hidCurrencyID"));
        //        this._ctlClientBOMSourcingResults._frmAdd.setFieldValue("ctlROHS", this._ctlMainInfo.getFieldValue("hidROHS"));
    },
    ctlClientBOMSourcingResults_SourcingResultSelect: function ()
    {
        //if (this._ctlClientBOMHubImagesDragDrop && this._ctlClientBOMSourcingResults._selectedRow == 1) {
        //    this._ctlClientBOMHubImagesDragDrop.show(true);
        //    this._ctlClientBOMHubImagesDragDrop._intRefDocID = this._ctlClientBOMSourcingResults._SourcingResultNo;
        //    this._ctlClientBOMHubImagesDragDrop.viewPanel(this._ctlClientBOMSourcingResults._blnImageAvail);
        //}
        //else {
        //    this._ctlHubImagesDragDrop.show(false);
        //}
        //alert(this._ctlClientBOMSourcingResults._SourcingResultNo);
    }
    
};

Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.ClientBOMDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
