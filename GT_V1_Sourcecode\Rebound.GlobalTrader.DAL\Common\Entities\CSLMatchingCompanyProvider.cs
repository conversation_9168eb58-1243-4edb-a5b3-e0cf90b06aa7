﻿//Marker     changed by      date         Remarks
//[001]      Arpit           03/03/2023   RP-257

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class CSLMatchingCompanyProvider : DataAccess
    {
        static private CSLMatchingCompanyProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public CSLMatchingCompanyProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (CSLMatchingCompanyProvider)Activator.CreateInstance(Type.GetType("Rebound.GlobalTrader.DAL.SqlClient.SQLCSLMatchingCompanyProvider"));
                return _instance;
            }
        }

        public CSLMatchingCompanyProvider()
        {
            this.ConnectionString = Globals.Settings.CSLMatchingCompanys.ConnectionString;
        }
        /// <summary>
        /// usp_Select_CSVMatchingCompanyDataByDate
        /// </summary>
        /// <param name="dateInput"></param>
        /// <returns></returns>
        public abstract List<CSLMatchingCompanyDetails> GetListCSLMatchingCompany(string dateInput, string type);
        public abstract List<CSLEUSearchDetails> GetListCSLEU(string name, string type, string address, string country, bool isFuzzyName);
        /// <summary>
        /// usp_CSL_CSVImportedDate
        /// </summary>
        /// <returns></returns>
        public abstract List<CSLMatchingCompanyDetails> GetCSVImportedDate(string type);

    }
}
