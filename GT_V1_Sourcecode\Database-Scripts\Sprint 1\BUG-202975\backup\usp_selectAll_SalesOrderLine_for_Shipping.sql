/****** Object:  StoredProcedure [dbo].[usp_selectAll_SalesOrderLine_for_Shipping]    Script Date: 5/10/2024 11:48:27 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE  PROCEDURE [dbo].[usp_selectAll_SalesOrderLine_for_Shipping]                          
--******************************************************************************************                          
--* Returns all SalesOrderLines for the Sales Order Shipping Screen (that means all rows                          
--* now, not just the ready to ship ones)                          
--*                           
--* RP 19.07.2009:                          
--* - use Quantity ordered for Service QuantityInStock                          
--*                           
--* RP 14.06.2009:                          
--* - include services                          
--*                           
--* RP 17.05.2009:                          
--* - order by DatePromised                          
--*                           
--* RP 09.06.2009:                          
--* - fix check of ready to ship to be at allocation level                          
--*                           
--* RP 02.06.2009:                          
--* - new proc                          
--******************************************************************************************                          
    @SalesOrderNo int                          
AS         
  
Declare @ApprovalStatusIdcount int,@ShowOGELWarning bit    
select  @ApprovalStatusIdcount= count(sm.ApprovalStatusId)   from  dbo.tbSalesOrderLine sol    
LEFT JOIN tbSO_ExportApprovalStatusOGEL es on es.SalesOrderLineNo = sol.SalesOrderLineId              
LEFT JOIN tbSOExportApprovalStatusMaster sm on sm.ApprovalStatusId = es.ApprovalStatusId  WHERE  sol.salesorderno  = @SalesOrderNo   and sm.ApprovalStatusId !=7  
SET @ShowOGELWarning = CASE WHEN  @ApprovalStatusIdcount>0 THEN 1  ELSE 0 END   
  
  
    SELECT  *                          
    FROM    (SELECT al.AllocationId                          
                  , sol.SalesOrderLineId                          
                  , sol.SalesOrderNo                          
                  , sol.Part                          
                  , stk.ROHS                          
                  , sol.DateCode                          
                  , sol.Quantity                          
                  , sol.ManufacturerNo                          
                  , mf.ManufacturerName                          
                  , al.StockNo                          
                  , stk.GoodsInLineNo                          
                  , mf.ManufacturerCode                          
                  , case WHEN sol.ServiceNo IS NULL THEN al.QuantityAllocated                          
                         ELSE sol.Quantity                          
                    END AS QuantityAllocated                          
                  , sol.Quantity AS QuantityOrdered                          
                  , co.CompanyName                          
                  , so.CompanyNo                          
                  , so.CustomerPO                          
                  , so.DateOrdered                          
                  , case WHEN sol.ServiceNo IS NULL THEN stk.QuantityInStock                          
                         ELSE sol.Quantity                          
                    END AS QuantityInStock                          
                  , so.SalesOrderNumber                          
                  , sol.ProductNo                          
                  , pr.ProductName                          
                  , pr.ProductDescription                          
                  , pa.PackageName                          
                  , pa.PackageDescription                          
                  , stk.Location                          
                  , isnull((SELECT  sum(ila.Quantity)                          
                            FROM    dbo.tbInvoicelineAllocation ila                          
                            WHERE   sol.SalesOrderLineId = ila.SalesOrderLineNo                        
                           ), 0) AS QuantityShipped                          
                  , sol.CustomerPart                          
                  , sol.RequiredDate                          
                  , sol.DatePromised                          
        --          , case WHEN EXISTS ( SELECT   1                          
        --                               FROM     vwSalesOrderLineShipList                          
        --                               WHERE    (sol.ServiceNo IS NULL                          
        --                                         AND SalesOrderLineId = sol.SalesOrderLineId                          
        --                                         AND AllocationId = al.AllocationId)                          
        --                                        OR (NOT sol.ServiceNo IS NULL                  
        --                                            AND NOT sol.ServiceShipped = 1                          
        --AND SalesOrderLineId = sol.SalesOrderLineId) )               
        --                                            AND ( (ISNULL(co.CreditStatus,'' )!='M')          OR                  
        --              (ISNULL(co.CreditStatus,'')='M' and Month(sol.DatePromised) =Month(GETDATE()) AND YEAR(sol.DatePromised) = YEAR(GETDATE())))               
        --                                            THEN cast(1 AS bit)                          
        --                 ELSE cast(0 AS bit)                          
        --            END AS ReadyToShip               
                      
    , CASE WHEN EXISTS ( SELECT   1                          
                                       FROM     vwSalesOrderLineShipList                     
                                       WHERE    (sol.ServiceNo IS NULL                          
                                                 AND SalesOrderLineId = sol.SalesOrderLineId                          
                                                 AND AllocationId = al.AllocationId              
                                                 AND CreditSatusReadytoShip  = 1)        
             AND((ISNULL(so.OGEL_Required,0) = 1 AND es.ApprovalStatusId != 8) OR (ISNULL(so.OGEL_Required,0)  = 0))        
                                                                           
                                                OR (NOT sol.ServiceNo IS NULL                          
                                                    AND NOT sol.ServiceShipped = 1                          
                                                    AND SalesOrderLineId = sol.SalesOrderLineId)               
                                                   AND CreditSatusReadytoShip  = 1)        
               AND((ISNULL(so.OGEL_Required,0)  = 1 AND es.ApprovalStatusId != 8) OR (ISNULL(so.OGEL_Required,0)  = 0))        
          THEN cast(1 AS bit)                         
          ELSE cast(0 AS bit)                          
      END AS ReadyToShip              
                       
      --, cast(1 AS bit) as  ReadyToShip                       
                  , stk.WarehouseNo                          
                  , wh.WarehouseName                          
                  , sol.ServiceNo                          
                  ,sol.Instructions                       
                  , CASE WHEN co.CreditStatus='M' AND (NOT Month(sol.DatePromised) =Month(GETDATE()) AND YEAR(sol.DatePromised) = YEAR(GETDATE())) then '[Stop Status : M]' else ''end as CreditStatus                     
      , isnull(gil.ReqSerialNo,0) as ReqSerialNo              
      , sol.MSLLevel              
   ,sol.SOSerialNo            
   ,sm.ApprovalName as OGELApprovalStatus        
   ,so.OGEL_Required as OGELREquired    
   ,@ShowOGELWarning as ShowOGELWarning  
             FROM   dbo.tbSalesOrderLine sol                          
             JOIN   dbo.tbSalesOrder so ON sol.SalesOrderNo = so.SalesOrderId                     
             LEFT JOIN dbo.tbCompany co ON so.CompanyNo = co.CompanyId                          
             LEFT JOIN dbo.tbContact cn ON so.ContactNo = cn.ContactId                          
             LEFT JOIN dbo.tbLogin lg ON lg.LoginId = so.Salesman                          
             LEFT JOIN dbo.tbProduct pr ON sol.ProductNo = pr.ProductId                          
             LEFT JOIN dbo.tbPackage pa ON sol.PackageNo = pa.PackageId                          
             LEFT JOIN dbo.tbManufacturer mf ON sol.ManufacturerNo = mf.ManufacturerId                          
             LEFT JOIN dbo.tbAllocation al ON sol.SalesOrderLineId = al.SalesOrderLineNo                          
             LEFT JOIN dbo.tbStock stk ON al.StockNo = stk.StockId                          
             LEFT JOIN dbo.tbWarehouse wh ON wh.WarehouseId = stk.WarehouseNo                 
    --Espire, 08 March 2018              
    LEFT JOIN tbGoodsInLine gil on gil.GoodsInLineId = stk.GoodsInLineNo                   LEFT JOIN tbSO_ExportApprovalStatusOGEL es on es.SalesOrderLineNo = sol.SalesOrderLineId            
      LEFT JOIN tbSOExportApprovalStatusMaster sm on sm.ApprovalStatusId = es.ApprovalStatusId          
             WHERE  so.SalesOrderId = @SalesOrderNo           
          
            ) AS v                          
    WHERE   QuantityShipped < Quantity                          
    ORDER BY DatePromised
GO


