<%@ Control Language="C#" CodeBehind="SalesOrdersShip.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlSONo" runat="server" ResourceTitle="SalesOrderNo" FilterField="SONo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
			<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />	
            </FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerPO" runat="server" ResourceTitle="CustomerPurchaseOrderNo" FilterField="CustPO" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" FilterField="DateOrderedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" FilterField="DateOrderedTo" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedFrom" runat="server" ResourceTitle="DatePromisedFrom" FilterField="DatePromisedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedTo" runat="server" ResourceTitle="DatePromisedTo" FilterField="DatePromisedTo" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>

