Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.prototype={get_intDebitID:function(){return this._intDebitID},set_intDebitID:function(n){this._intDebitID!==n&&(this._intDebitID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printDebitNote));this._btnPrint&&this._btnPrint.addEmail(Function.createDelegate(this,this.emailDebitNote));this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlMainInfo&&this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlMainInfo_SaveEditComplete));this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));this.setFieldsFromMainInfo();Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._btnPrint&&this._btnPrint.dispose(),this._btnPrint=null,this._ctlMainInfo=null,this._ctlLines=null,Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.callBaseMethod(this,"dispose"))},printDebitNote:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.DebitNote,this._intDebitID)},emailDebitNote:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.DebitNote,this._intDebitID,!0)},ctlMainInfo_GetDataComplete:function(){this.setFieldsFromMainInfo()},ctlMainInfo_SaveEditComplete:function(){this._ctlLines&&(this._ctlLines._intGlobalClientNo=this._ctlMainInfo.getFieldValue("hidGlobalClientNo"));this._ctlLines&&(this._ctlLines._blnGlobalUser=this._ctlMainInfo._blnGlobalLogin);this._ctlLines.getData()},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintHUBDebit"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBDebit,this._intDebitID);this._btnPrint._strExtraButtonClickCommand=="EmailHUBDebit"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBDebit,this._intDebitID,!0);this._btnPrint._strExtraButtonClickCommand=="EmailDBHTML"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.DBEmail,this._intDebitID,!0);this._btnPrint._strExtraButtonClickCommand=="EmailHUBDebitHtml"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.EmailHUBDebitHtml,this._intDebitID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intDebitID,!1,"DebitNote")},setFieldsFromMainInfo:function(){this._ctlLines._frmEdit&&this._ctlLines._frmEdit.setCurrency(this._ctlMainInfo.getFieldValue("hidCurrencyCode"));this._ctlLines._frmAdd&&this._ctlLines._frmAdd.setFieldsFromHeader(this._ctlMainInfo.getFieldValue("hidCurrencyNo"),this._ctlMainInfo.getFieldValue("hidCurrencyCode"),this._ctlMainInfo.getFieldValue("hidDebitNumber"),this._ctlMainInfo.getFieldValue("hidSupplierName"),this._ctlMainInfo.getFieldValue("hidPONo"),this._ctlMainInfo.getFieldValue("ctlDebitDate"));this._ctlLines&&(this._ctlLines._intGlobalClientNo=this._ctlMainInfo.getFieldValue("hidGlobalClientNo"));this._ctlLines&&(this._ctlLines._blnGlobalUser=this._ctlMainInfo._blnGlobalLogin)}};Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);