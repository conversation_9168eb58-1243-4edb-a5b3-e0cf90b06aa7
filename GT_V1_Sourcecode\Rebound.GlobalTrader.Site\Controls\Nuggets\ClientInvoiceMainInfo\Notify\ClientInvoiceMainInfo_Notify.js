Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.initializeBase(this,[n]);this._strClientInvoiceNumber=""};Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.prototype={get_strClientInvoiceNumber:function(){return this._strClientInvoiceNumber},set_strClientInvoiceNumber:function(n){this._strClientInvoiceNumber!==n&&(this._strClientInvoiceNumber=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intClientInvoiceID:function(){return this._intClientInvoiceID},set_intClientInvoiceID:function(n){this._intClientInvoiceID!==n&&(this._intClientInvoiceID=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&($R_IBTN.addClick(this._ibtnSend,Function.createDelegate(this,this.sendMail)),$R_IBTN.addClick(this._ibtnSend_Footer,Function.createDelegate(this,this.sendMail)),this._ctlMail=$find(this.getField("ctlSendMailMessage").ID),this._ctlMail._ctlRelatedForm=this,this.getMessageText())},dispose:function(){this.isDisposed||(this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlMail&&this._ctlMail.dispose(),this._ctlMail=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._strClientInvoiceNumber=null,this._intCompanyID=null,this._intClientInvoiceID=null,Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.callBaseMethod(this,"dispose"))},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyClientInvoice(this._intClientInvoiceID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject(String.format($R_RES.NotifyClientInvoice,this._strClientInvoiceNumber))},sendMail:function(){this.validateForm()&&Rebound.GlobalTrader.Site.WebServices.NotifyMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),this._intCompanyID,Function.createDelegate(this,this.sendMailComplete))},validateForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMailComplete:function(){this.showSavedOK(!0);this.onSaveComplete()}};Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Notify",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);