<%--
     Marker     Changed by      Date         Remarks
    [001]      Shashi Keshar   20/01/2012   Added Insurance History in Detail Section--%>
<%--[002]      Suhail          02/05/2018   Added Credit Limit2  --%>
<%@ Control Language="C#" CodeBehind="CompanyFinanceInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Link Account" IconCSSType="Edit" IconButtonMode="Hyperlink" Href="javascript:void(0);" />
	</Links>
	
	<Content>
		<div>
            <table class="dataItems">
                <tr>
                    <td class="desc">Currency Selector</td>
					<td class="item">
						<select id="CompanyFinanceInfoCurrencyDropdown">
							<option value="0">Select</option>
						</select><a style="cursor:pointer;" id="RefreshCompanyFinanceInfoCurrencyDropdown" class="dropDownRefresh"></a>
					</td>
<%--					<td class="desc">
                       Exchange Rate
					</td>
					<td class="item" id="ExchangeRatetd">

					</td>--%>
                </tr>
            </table>
			
            <div id="LinkedCompanyCreditInfo"></div>
		</div>
		<br>
        <div id="LinkedCompanyInfo">
        </div>
	</Content>
	
	<Forms>
		<ReboundForm:CompanyFinanceInfo_Link id="ctlLink" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
<style>
	#LinkedCompanyInfo {
        margin: 4px, 4px;
        padding: 4px;
        width: 100%;
        overflow-x: auto;
        overflow-y: hidden;
        white-space: nowrap;
	}
	img:-moz-loading {
    visibility: hidden;
	}
</style>
