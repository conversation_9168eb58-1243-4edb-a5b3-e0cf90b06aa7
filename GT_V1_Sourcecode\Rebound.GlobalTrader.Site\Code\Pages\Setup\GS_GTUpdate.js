Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate=function(n){Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.prototype={get_ctlGTUpdate:function(){return this._ctlGTUpdate},set_ctlGTUpdate:function(n){this._ctlGTUpdate!==n&&(this._ctlGTUpdate=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.callBaseMethod(this,"initialize")},goInit:function(){this._ctlGTUpdate&&this._ctlGTUpdate.addSelectCategory(Function.createDelegate(this,this.ctlCertificatecategory_SelectCategory));Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlGTUpdate&&this._ctlGTUpdate.dispose(),this._ctlGTUpdate=null,Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.callBaseMethod(this,"dispose"))},ctlCertificatecategory_SelectCategory:function(){}};Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);