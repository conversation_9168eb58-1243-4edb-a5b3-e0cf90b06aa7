/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-203943]		Cuong.DoX			14-JUNE-2024		Update			Change stock to remove ALLOCATE
===========================================================================================
*/
/****** Object:  StoredProcedure [dbo].[usp_insert_InternalPurchaseOrder]    Script Date: 6/4/2024 11:50:24 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('usp_insert_InternalPurchaseOrder','P') IS NOT NULL
    DROP PROC [dbo].[usp_insert_InternalPurchaseOrder]
GO
CREATE PROCEDURE [dbo].[usp_insert_InternalPurchaseOrder]
    @SalesOrderNo INT = 0,
    @SalesOrderLines VARCHAR(max) = NULL,
    @POHubSupplierNo INT,
    @ClientNo INT = 0,
    @WarehouseNo INT = 0,
    --@PurchaseQuoteLineNo INT = 0,                                                                                                                                                  
    @SourceingResultAttachedBy INT,
    @SourceingResultNo INT = 0,
    --@EstimatedShippingCost FLOAT=0,                                                                                                                                         
    --@DeliveryDate DATETIME=NULL,                                                                                                                                         
    --@ConvertedEstimatedShippingCost FLOAT=0,                                                                                                                                                 
    @IPOClientCurrencyNo INT = 0,
    @POBuyCurrencyNo INT = 0,
    @SrCurrencyNo INT,
    @LinkMultiCurrencyNo INT,
    @InternalPurchaseOrderNo INT OUTPUT,
    @PurchaseOrderNo INT OUTPUT,
    @Result INT OUTPUT,
    @SOL nvarchar(max) OUTPUT
AS
BEGIN
    SET nocount ON;
    DECLARE @InternalPurchaseOrderId INT
    DECLARE @InternalPurchaseOrderLineId INT
    DECLARE @CompanyNo INT
    DECLARE @POHubContactNo INT
    DECLARE @DateOrdered Date
    DECLARE @CurrencyNo INT
    DECLARE @BuyerId INT
    DECLARE @ShipViaNo INT = NULL
    DECLARE @Account NVARCHAR(50) = NULL
    DECLARE @TermsNo INT
    DECLARE @ExpediteNotes NVARCHAR(max) = NULL
    DECLARE @ExpediteDate DATETIME = NULL
    DECLARE @TotalShipInCost FLOAT = NULL
    DECLARE @DivisionNo INT
    DECLARE @TaxNo INT = 0
    DECLARE @Notes NVARCHAR(max) = NULL
    DECLARE @Notes_SOL NVARCHAR(max) = NULL
    DECLARE @Instructions NVARCHAR(max) = NULL
    declare @SOInstructions NVARCHAR(max) = NULL
    DECLARE @Paid BIT
    DECLARE @ImportCountryNo INT = NULL
    DECLARE @FreeOnBoard NVARCHAR(25) = NULL
    DECLARE @StatusNo INT = NULL
    DECLARE @Closed BIT
    DECLARE @IncotermNo INT = NULL
    DECLARE @UpdatedBy INT = NULL
    DECLARE @AirWayBillPO NVARCHAR(50) = NULL
    DECLARE @PurchaseOrderId INT
    DECLARE @InternalPurchaseOrderNumber INT
    DECLARE @SoLineId INT
    DECLARE @PurchaseOrderNumber INT
    DECLARE @Part NVARCHAR(30)
    DECLARE @ManufacturerNo INT = NULL
    DECLARE @DateCode NVARCHAR(5) = NULL
    DECLARE @PackageNo INT = NULL
    DECLARE @Quantity INT
    DECLARE @Price FLOAT
    --DECLARE @DeliveryDate DATETIME                                                                                                                                    
    DECLARE @ReceivingNotes NVARCHAR(max) = NULL
    DECLARE @Taxable BIT
    DECLARE @ProductNo INT = NULL
    DECLARE @Posted BIT
    DECLARE @ShipInCost FLOAT = NULL
    DECLARE @SupplierPart NVARCHAR(30) = NULL
    DECLARE @ROHS TINYINT = NULL
    DECLARE @Notes_POL NVARCHAR(128) = NULL
    DECLARE @PromiseDate DATETIME
    DECLARE @UpdatedBy_POL INT = NULL
    DECLARE @PurchaseOrderLineId INT
    DECLARE @InternalPurchaseOrderLineNo INT
    DECLARE @RowID INT
    DECLARE @SalesOrderLineNo INT
    DECLARE @PurchaseHubClientNo INT
    DECLARE @PurchaseHubWarehouseNo INT
    --DECLARE @PurchaseCurrencyNo INT                                                                      
    DECLARE @Buyer INT
    DECLARE @IPOPrice FLOAT
    DECLARE @ContactNo INT
    DECLARE @OriginalOfferCurrencyNo INT
    DECLARE @OriginalOfferDate DATETIME
    DECLARE @intErrorCode INT
    DECLARE @BasePrice FLOAT
    DECLARE @IPOCurrencyNo INT
    DECLARE @ClientCurrencyNo INT
    DECLARE @OriginalEntryDate DATETIME
    declare @SalesPerson int
    DECLARE @SOLIDs nvarchar(max) = NULL
    DECLARE @PurchaseQuoteLineNo INT
    DECLARE @EstimatedShippingCost FLOAT
    DECLARE @DeliveryDate DATETIME
    DECLARE @ConvertedEstimatedShippingCost FLOAT
    DECLARE @SupplierWarranty int
    DECLARE @MSLLevel NVARCHAR(50) = NULL
    --[001] code start                           
    --ihs data passing start                              
    declare
        --@CountryOfOrigin nvarchar(50)=null,                                                         
        @CountryOfOriginNo int = null,
        @LifeCycleStage nvarchar(50) = null,
        @HTSCode varchar(20) = null,
        @AveragePrice float = null,
        @Packing varchar(60) = null,
        @PackagingSize varchar(100) = null,
        @Descriptions nvarchar(max) = null,
        @IHSProduct varchar(100) = null,
        @ECCNCode varchar(100) = null,
        @DivisionHeaderNo int = null,
        @HUBStock nvarchar(50) = null,
        @HUBStockId int = null,
        @Location VARCHAR(50),
        @Qnty_Stock INT = 0,
        @Qnty_SalesOrder INT = 0,
        @PurchaseOrderHUBSTKId int = null


    --[001] code end                                                                                                            
    SET @SOL = NULL
    SET @Result = -1


    BEGIN




        select @SOL = coalesce(@SOL + ', ', '') + CONVERT(varchar, sol.SOSerialNo)
        FROM dbo.tbSalesOrderLine sol
            JOIN tbSourcingResult sr
                ON sr.SourcingResultId = sol.SourcingResultNo
        WHERE SalesOrderLineId IN (
                                      SELECT * FROM dbo.Ufn_split_salesorderline(@SalesOrderLines)
                                  )
              and sr.IsReleased = 0

        SELECT @SOLIDs = coalesce(@SOLIDs + ', ', '') + CONVERT(varchar, sr.SourcingResultId)
        --,@HUBStock=sr.SourcingTable                               
        --,@HUBStockId=sr.SourcingTableItemNo                                                                                   
        --,@UpdatedBy=   IIF(@HUBStock='HUBSTK',sr.UpdatedBy,@UpdatedBy)                                                                        
        --,@Qnty_SalesOrder= sol.Quantity                                                                                         
        FROM dbo.tbSalesOrderLine sol
            JOIN tbSourcingResult sr
                ON sr.SourcingResultId = sol.SourcingResultNo
        WHERE SalesOrderLineId IN (
                                      SELECT * FROM dbo.Ufn_split_salesorderline(@SalesOrderLines)
                                  )
              and sr.IsReleased = 1
        SELECT @Location = Location,
               @Qnty_Stock = QuantityInStock
        FROM tbstock
        where stockid = @HUBStockId

        /* Code for controlling  Creation of  Stock (Condition used to create new stock in trigger [utrg_insert_PurchaseOrderLine_Stock]) */
        SET @Posted = 1 --IIF(@HUBStock='HUBSTK',0,1)                                                                          
        /*********************************************************************************/

        IF (@SOLIDs IS NULL)
        BEGIN
            SET @Result = 3
            RETURN
        END

        BEGIN TRANSACTION [Tran1]

        BEGIN try

            SET @DeliveryDate = ISNULL(@DeliveryDate, GETDATE())
            SELECT @DivisionNo = DivisionNo
            FROM tbLogin
            WHERE LoginId = @SourceingResultAttachedBy

            SELECT TOP 1
                @PurchaseHubClientNo = ClientId
            --@PurchaseCurrencyNo = CurrencyNo                                                  
            FROM tbClient
            WHERE IsPOHub = 1


            SELECT @ImportCountryNo = DefaultPOShipCountryNo,
                   @POHubContactNo = DefaultPOContactNo
            FROM tbCompany
            WHERE CompanyId = @POHubSupplierNo

            SELECT TOP 1
                @CompanyNo = CompanyId,
                @ContactNo = DefaultPOContactNo
            --, @IPOCurrencyNo = POCurrencyNo                                                                                                         
            FROM tbCompany
            WHERE ClientNo = @ClientNo
                  AND IsPOHub = 1

            IF (@ContactNo IS NULL)
            BEGIN
                SELECT TOP 1
                    @ContactNo = contactid
                FROM tbcontact
                WHERE companyno = @CompanyNo
            END

            ----******************************** Insert PurchaseOrder table data Start******************************--------------------                                                                                                                               

            SELECT @DateOrdered = CURRENT_TIMESTAMP, -- DateOrdered                 
                   @CurrencyNo = CurrencyNo,
                                                     ----@ShipViaNo = ShipViaNo,                                                                                       
                   @Account = Account,
                                                     ---- @TermsNo = 0,                                              
                   @ExpediteNotes = NULL,
                   @ExpediteDate = NULL,
                   @TotalShipInCost = ShippingCost,
                                                     ----@TaxNo = 0,                                                                              
                   @Notes = Notes,
                   @Instructions = Instructions,
                   @Paid = Paid,
                   @FreeOnBoard = NULL,
                   @StatusNo = 0,
                   @Closed = 0,
                                                     ---- @IncotermNo = IncotermNo,                                                                                                             
                   @Buyer = Salesman,
                   @SalesPerson = Salesman
            FROM dbo.tbSalesOrder
            WHERE SalesOrderId = @SalesOrderNo

            SET @POHubContactNo = Isnull(@POHubContactNo, 0)
            --SET @PurchaseCurrencyNo= Isnull(@PurchaseCurrencyNo, 0)                                                              
            SET @DivisionNo = Isnull(@DivisionNo, 0)

            SELECT @TermsNo = ISNULL(tm.TermsId, 0)
            --------,@TaxNo = ISNULL(tx.TaxId,0),@ShipViaNo = ISNULL(sv.ShipViaId,0)                                          
            FROM tbCompany co --LEFT JOIN dbo.tbShipVia sv ON co.DefaultPurchaseShipViaNo = sv.ShipViaId                             

                --LEFT JOIN dbo.tbTax tx ON co.POTaxNo = tx.TaxId  AND NOT tx.Inactive = 1                                                                                                                
                LEFT JOIN dbo.tbTerms tm
                    ON co.POTermsNo = tm.TermsId
            WHERE CompanyId = @POHubSupplierNo

            SELECT @IncotermNo = ISNULL(IncotermNo, 0),
                   @TaxNo = ISNULL(TaxbyAddress, 0)
            FROM tbCompanyAddress
            WHERE DefaultBilling = 1
                  AND CompanyNo = @POHubSupplierNo

            --Get ShipVia No from client table                                                              
            DECLARE @RegionNo int,
                    @IPODocNumber int
            SELECT @ShipViaNo = ISNULL(ShipViaNo, 0)
            FROM tbClient
            WHERE ClientId = @ClientNo
            select @RegionNo = RegionNo,
                   @IPOCurrencyNo = ClientCurrencyNo
            from tbSourcingResult
            where SourcingResultId = @SourceingResultNo

            declare @DocHeaderImageName varchar(200)

            select @DocHeaderImageName = DocumentHeaderImageName
            from tbDivision
            where DivisionId = @DivisionNo

            IF @DocHeaderImageName IS NULL
               OR LEN(@DocHeaderImageName) = 0
            BEGIN
                SELECT @DocHeaderImageName = DocumentHeaderImageName
                FROM tbClient
                where ClientId = @PurchaseHubClientNo
            END

            DECLARE @AS6081PO BIT = 0;
            SELECT @AS6081PO = ISNULL(AS6081, 0)
            FROM tbSalesOrder
            WHERE SalesOrderId = @SalesOrderNo


            EXEC [dbo].[Usp_insert_purchaseorder] @ClientNo = @PurchaseHubClientNo,
                                                  @CompanyNo = @POHubSupplierNo,
                                                  @ContactNo = @POHubContactNo,
                                                  @DateOrdered = @DateOrdered,
                                                  @WarehouseNo = @WarehouseNo,
                                                  @CurrencyNo = @POBuyCurrencyNo,
                                                  @Buyer = @SourceingResultAttachedBy,
                                                  @ShipViaNo = @ShipViaNo,
                                                  @Account = account,
                                                  @TermsNo = @TermsNo,
                                                  @ExpediteNotes = @ExpediteNotes,
                                                  @ExpediteDate = @ExpediteDate,
                                                  @TotalShipInCost = @TotalShipInCost,
                                                  @DivisionNo = @DivisionNo,
                                                  @TaxNo = @TaxNo,
                                                  --@Notes = notes,                                                                                                                                                  
                                                  @Notes = '',
                                                  -- @Instructions = @Instructions,                                                                                                                                             
                                                  @Instructions = '',
                                                  @Paid = @Paid,
                                                  @Confirmed = 0,
                                                  @ImportCountryNo = @ImportCountryNo,
                                                  @FreeOnBoard = @FreeOnBoard,
                                                  @StatusNo = @StatusNo,
                                                  @Closed = @Closed,
                                                  @IncotermNo = @IncotermNo,
                                                  @UpdatedBy = 0,
                                                  @AirWayBillPO = '',
                                                  @DivisionHeaderNo = @DivisionNo,
                                                  @DocHeaderImageName = @DocHeaderImageName,
                                                  @AS6081 = @AS6081PO,
                                                  @PurchaseOrderId = @PurchaseOrderId OUTPUT

            ------******************************** Insert PurchaseOrder table data End******************************-------------------                                                                                                                                


            ------Header Insert Start-----------                                                                                        
            EXEC Usp_select_internalpurchaseorder_nextnumber @ClientNo,
                                                             @UpdatedBy,
                                                             @InternalPurchaseOrderNumber output

            DECLARE @GlobalCountryNo int
            DECLARE @IPOImportCountryNo int
            SELECT @GlobalCountryNo = cn.GlobalCountryNo
            FROM dbo.tbPurchaseOrder po
                LEFT JOIN dbo.tbCountry cn
                    ON cn.CountryId = po.ImportCountryNo
            WHERE po.PurchaseOrderId = @PurchaseOrderId

            SELECT top 1
                @IPOImportCountryNo = CountryId
            FROM tbCountry
            WHERE GlobalCountryNo = @GlobalCountryNo
                  and ClientNo = @ClientNo
                  and Inactive = 0

            DECLARE @TaxbyAddress int
            select @TaxbyAddress = TaxbyAddress
            from tbCompanyAddress
            where CompanyNo = @CompanyNo
                  AND isnull(DefaultBilling, 0) = 1
            DECLARE @POTermsNo int
            select @POTermsNo = POTermsNo
            from tbCompany
            where CompanyId = @CompanyNo
            select @IPODocNumber
                = CAST((CAST(@ClientNo as varchar(50)) + CAST(@InternalPurchaseOrderNumber as varchar(50))) as int)

            INSERT INTO tbInternalPurchaseOrder
            (
                InternalPurchaseOrderNumber,
                PurchaseOrderNo,
                ClientNo,
                CompanyNo,
                ContactNo,
                DateOrdered,
                WarehouseNo,
                CurrencyNo,
                Buyer,
                TotalShipInCost,
                DivisionNo,
                TaxNo,
                Notes,
                Instructions,
                Paid,
                Confirmed,
                ImportCountryNo,
                FreeOnBoard,
                StatusNo,
                Closed,
                UpdatedBy,
                DLUP,
                ApprovedBy,
                DateApproved,
                CreatedBy,
                CreateDate,
                SalesOrderNo,
                TermsNo,
                RegionNo,
                HubCurrencyNo,
                LinkMultiCurrencyNo,
                DivisionHeaderNo,
                AS6081
            )
            SELECT @IPODocNumber,
                   @PurchaseOrderId,
                   @ClientNo,
                   @CompanyNo,
                   @ContactNo,
                   dateordered,
                   @WarehouseNo,
                   @IPOClientCurrencyNo,
                   @SalesPerson,
                   TotalShipInCost,
                   (
                       select DivisionNo from tbLogin where LoginId = @SalesPerson
                   ),
                   @TaxbyAddress,
                   Notes,
                   Instructions,
                   Paid,
                   0,
                   @IPOImportCountryNo,
                   FreeOnBoard,
                   0,
                   0,
                   0,
                   CURRENT_TIMESTAMP,
                   0,
                   NULL,
                   0,
                   CURRENT_TIMESTAMP,
                   @SalesOrderNo,
                   @POTermsNo,
                   @RegionNo,
                   @SrCurrencyNo,
                   @LinkMultiCurrencyNo,
                   (
                       select DivisionNo from tbLogin where LoginId = @SalesPerson
                   ),
                   @AS6081PO
            FROM dbo.tbPurchaseOrder
            WHERE PurchaseOrderId = @PurchaseOrderId

            SET @InternalPurchaseOrderId = Scope_identity()

            DECLARE @SourcingResultId int
            DECLARE @BomId int

            SET @RowID = 0

            ------******************************** Insert PurchaseOrderLine and Stock table data Start ******************************--------------------                         

            declare @SODeliveryDate datetime
            declare @PODeliveryDate datetime

            DECLARE db_cursorpolineinsert CURSOR FOR
            SELECT sol.Part,
                   sol.ManufacturerNo,
                   sol.DateCode,
                   sol.PackageNo,
                   sol.Quantity,
                   ISNULL(sr.ActualPrice, 0) AS ActualPrice,
                   CASE
                       WHEN sol.Taxable = 'Y' THEN
                           1
                       ELSE
                           0
                   END,
                   sol.ProductNo,
                                                                               --sol.CustomerPart,                                                                                         
                   null as CustomerPart,
                   sr.ROHS,                                                    --sol.ROHS,   // ROHS not updated from SO                                                     
                   sol.SalesOrderLineId,
                   sol.notes,
                   sr.SourcingResultId,
                   sr.POHubCompanyNo,
                   sr.SourcingTableItemNo,
                   sr.Buyer AS SourceingResultAttachedBy,
                   sr.SourcingResultId,
                   IIF(sr.SourcingTable = 'HUBSTK', 0, sr.EstimatedShippingCost),
                   sr.DeliveryDate,
                   sr.ClientCurrencyNo,
                   ISNULL(sr.OriginalEntryDate, sr.DLUP),
                   sol.PODeliveryDate,
                   sol.Instructions,
                   sol.MSLLevel,
                   isnull(SupplierWarranty, 0) as SupplierWarranty,
                                                                               --[001] code start                             
                                                                               --ihs data passing start                                                                    
                                                                               --,sol.CountryOfOrigin                                                                                                                
                   sol.CountryOfOriginNo,
                   sol.LifeCycleStage,
                   sol.HTSCode,
                   sol.AveragePrice,
                   sol.Packing,
                   sol.PackagingSize,
                   sol.Descriptions,
                   sol.IHSProduct,
                   sol.ECCNCode,
                                                                               --[001] code end                                                                                                           
                                                                               --[002] CODE START---                                                                                                      
                   sr.SourcingTable,                                           --- @HUBStock=                                                                    
                   sr.SourcingTableItemNo,                                     ---@HUBStockId=                                                                                  
                   IIF(sr.SourcingTable = 'HUBSTK', sr.UpdatedBy, @UpdatedBy), --@UpdatedBy=                                       
                   sol.Quantity                                                -- @Qnty_SalesOrder=                                                              
            --[002] CODE END---                                                             
            FROM dbo.tbSalesOrderLine sol
                JOIN tbSourcingResult sr
                    ON sr.SourcingResultId = sol.SourcingResultNo
            WHERE SalesOrderLineId IN (
                                          SELECT * FROM dbo.Ufn_split_salesorderline(@SalesOrderLines)
                                      )
                  and sr.IsReleased = 1

            OPEN db_cursorpolineinsert

            FETCH next FROM db_cursorpolineinsert
            INTO @Part,
                 @ManufacturerNo,
                 @DateCode,
                 @PackageNo,
                 @Quantity,
                 @Price,
                 @Taxable,
                 @ProductNo,
                 @SupplierPart,
                 @ROHS,
                 @SalesOrderLineNo,
                 @Notes_SOL,
                 @SourcingResultId,
                 @POHubSupplierNo,
                 @PurchaseQuoteLineNo,
                 @SourceingResultAttachedBy,
                 @SourceingResultNo,
                 @EstimatedShippingCost,
                 @DeliveryDate,
                 @ClientCurrencyNo,
                 @OriginalEntryDate,
                 @SODeliveryDate,
                 @SOInstructions,
                 @MSLLevel,
                 @SupplierWarranty,
                 --[001] code start                                                                                                          
                 --ihs data passing start                                                                                                          
                 -- ,@CountryOfOrigin                                                            
                 @CountryOfOriginNo,
                 @LifeCycleStage,
                 @HTSCode,
                 @AveragePrice,
                 @Packing,
                 @PackagingSize,
                 @Descriptions,
                 @IHSProduct,
                 @ECCNCode,
                 --[001] code end                                                              
                 --[002] CODE START ----                                                            
                 @HUBStock,
                 @HUBStockId,
                 @UpdatedBy,
                 @Qnty_SalesOrder
            --[002] CODE END ----                             
            WHILE @@FETCH_STATUS = 0
            BEGIN
                SET @ConvertedEstimatedShippingCost
                    = IIF(@HUBStock = 'HUBSTK',
                          0,
                          @EstimatedShippingCost / dbo.ufn_get_exchange_rate(@ClientCurrencyNo, @OriginalEntryDate))
                SET @PromiseDate = @DeliveryDate
                SET @ShipInCost = NULL
                SET @PODeliveryDate = ISNULL(@SODeliveryDate, @DeliveryDate)

                DECLARE @AS6081ForSOLine BIT = 0;
                SELECT @AS6081ForSOLine = AS6081
                FROM tbSalesOrderLine
                WHERE SalesOrderLineId = @SalesOrderLineNo

                EXEC [dbo].[Usp_insert_purchaseorderline] @PurchaseOrderNo = @PurchaseOrderId,
                                                          @Part = @Part,
                                                          @ManufacturerNo = @ManufacturerNo,
                                                          @DateCode = @DateCode,
                                                          @PackageNo = @PackageNo,
                                                          @Quantity = @Quantity,
                                                          @Price = @Price,
                                                          @DeliveryDate = @PODeliveryDate,
                                                          @ReceivingNotes = @SOInstructions,
                                                          @Taxable = @Taxable,
                                                          @ProductNo = @ProductNo,
                                                          @Posted = @Posted,
                                                          @ShipInCost = @ConvertedEstimatedShippingCost,
                                                          --@ShipInCost = @ShipInCost,                                                                                   
                                                          @SupplierPart = @SupplierPart,
                                                          @ROHS = @ROHS,
                                                          @Notes = @Notes_SOL,
                                                          -- @Notes = '',                                            
                                                          -- @PromiseDate = @PromiseDate,                                                                                                                       
                                                          @PromiseDate = @PODeliveryDate,
                                                          @UpdatedBy = 0,
                                                          @MSLLevel = @MSLLevel,
                                                          @SupplierWarranty = @SupplierWarranty,
                                                          --[001] code start                                                                
                                                          --ihs data passing start                                                                                                
                                                          --,@CountryOfOrigin = @CountryOfOrigin                                                                                                              
                                                          @CountryOfOriginNo = @CountryOfOriginNo,
                                                          @LifeCycleStage = @LifeCycleStage,
                                                          @HTSCode = @HTSCode,
                                                          @AveragePrice = @AveragePrice,
                                                          @Packing = @Packing,
                                                          @PackagingSize = @PackagingSize,
                                                          @Descriptions = @Descriptions,
                                                          @IHSProduct = @IHSProduct,
                                                          @ECCNCode = @ECCNCode,
                                                          --[001] code end                                               
                                                          @AS6081 = @AS6081ForSOLine,
                                                          @PurchaseOrderLineId = @PurchaseOrderLineId out

                ----******************************** Insert Allocation table data Start******************************--------------------                                                                               
                INSERT INTO dbo.tbAllocation
                (
                    StockNo,
                    SalesOrderLineNo,
                    QuantityAllocated,
                    UpdatedBy,
                    DLUP
                )
                SELECT Stockid,
                       @SalesOrderLineNo,
                       QuantityOnOrder,
                       UpdatedBy,
                       GETDATE()
                FROM dbo.tbStock
                WHERE PurchaseOrderNo = @PurchaseOrderId
                      AND PurchaseOrderLineNo = @PurchaseOrderLineId

                IF (@HUBStock = 'HUBSTK')
                BEGIN
                    --this Insert allrady done when Add Requment from Souricng Screen on Stock Nugget  
                    --  INSERT INTO dbo.tbAllocation                                                                                                                                            
                    -- (StockNo,                                               
                    -- SalesOrderLineNo,                                                                                
                    -- QuantityAllocated,                                                                                                        
                    -- UpdatedBy,                                                                                                                                            
                    -- DLUP)                                                                        
                    -- SELECT                                                                                
                    -- Stockid,                                                                           
                    --0,-- @SalesOrderLineNo,                              
                    --/********* Updating Quantity as zero as discuused with james on 22 nov 2023 so Allocation on parent stock should be zero**********/                              
                    --0,-- QuantityOnOrder,                                                                            
                    -- UpdatedBy,                                                                                                                                            
                    -- GETDATE()                                                                                                   
                    -- FROM   dbo.tbStock  WHERE StockId=@HUBStockId                                             

					/***remove allocation because it is not used anymore - cuongdox 14-6-24 bug 203943***/

                    --DECLARE @AllocationNo INT;
                    --SELECT TOP 1
                    --    @AllocationNo = AllocationId
                    --FROM tbAllocation
                    --WHERE StockNo = @HUBStockId
                    --order by AllocationId desc
					/*end removing for cuongdox 14-6-24 bug 203943*/

                    -- tbStock - subtract shipments for inserts                                                                                     
                    UPDATE tbStock
                    SET QuantityInStock = QuantityInStock - @Qnty_SalesOrder
                    FROM tbStock s
                        --JOIN tbAllocation a
                        --    ON a.StockNo = s.StockId
					--WHERE a.AllocationId = @AllocationNo
                    WHERE s.StockId = @HUBStockId                                                                    


                    /*************** Update ParentStock id in New stock **********************/

                    DECLARE @SODivision INT = 0;
                    DECLARE @SOShipToAddressNo INT = 0;
                    SELECT @SODivision = ISNULL(DivisionNo, 0),
                           @SOShipToAddressNo = ISNULL(ShipToAddressNo, 0)
                    FROM tbSalesOrder
                    WHERE SalesOrderId = @SalesOrderNo

                    UPDATE tbStock
                    SET ParentStockNo = @HUBStockId,
                        QuantityOnOrder = 0,
                        QuantityInstock = @Qnty_SalesOrder,
                        DivisionNo = @SODivision,
                        StockDate = GETDATE(),
                        ROHS = @ROHS
                    WHERE PurchaseOrderNo = @PurchaseOrderId
                          AND PurchaseOrderLineNo = @PurchaseOrderLineId



                END
                ----******************************** Insert Allocation table data End ******************************--------------------                                                                                                                                   


                ----******************************** Insert InternalPurchaseOrderLine  table data Start ******************************--------------------                                                                                                                  

                SELECT @IPOPrice = ISNULL(Price, 0),
                       @OriginalOfferCurrencyNo = ClientCurrencyNo,
                       @OriginalOfferDate = @DateOrdered
                FROM tbSourcingResult
                WHERE SourcingResultId = @SourcingResultId

                SELECT top 1
                    @BomId = cr.BOMNo
                FROM tbCustomerRequirement cr
                    JOIN tbSourcingResult sr
                        ON cr.CustomerRequirementId = sr.CustomerRequirementNo
                WHERE sr.SourcingResultId = @SourcingResultId

                INSERT INTO dbo.tbInternalPurchaseOrderLine
                (
                    InternalPurchaseOrderNo,
                    PurchaseOrderLineNo,
                    Price,
                    UpdatedBy,
                    DLUP,
                    IsSRMA,
                    SalesOrderLineNo,
                    PurchaseQuoteLineNo,
                    EstimatedShippingCost,
                    BOMNo,
                    SourcingResultNo,
                    ProductNo,
                    SupplierWarranty,
                    AS6081
                )
                SELECT @InternalPurchaseOrderId,
                       PurchaseOrderLineId,
                       @IPOPrice,
                       UpdatedBy,
                       Getdate(),
                       IsSRMA,
                       @SalesOrderLineNo,
                       --null,                                                                                                                          
                       @PurchaseQuoteLineNo,
                       @ConvertedEstimatedShippingCost,
                       @BomId,
                       @SourceingResultNo,
                       ProductNo,
                       @SupplierWarranty,
                       AS6081
                FROM dbo.tbpurchaseorderline
                WHERE purchaseorderlineid = @PurchaseOrderLineId

                update tbSourcingResult
                set IsSoCreated = 1
                where SourcingResultId = @SourceingResultNo

                SET @RowID = @RowID + 1

                ----******************************** Insert InternalPurchaseOrderLine  table data End ******************************--------------------                                                                                                                   

                FETCH next FROM db_cursorpolineinsert
                INTO @Part,
                     @ManufacturerNo,
                     @DateCode,
                     @PackageNo,
                     @Quantity,
                     @Price,
                     @Taxable,
                     @ProductNo,
                     @SupplierPart,
                     @ROHS,
                     @SalesOrderLineNo,
                     @Notes_SOL,
                     @SourcingResultId,
                     @POHubSupplierNo,
                     @PurchaseQuoteLineNo,
                     @SourceingResultAttachedBy,
                     @SourceingResultNo,
                     @EstimatedShippingCost,
                     @DeliveryDate,
                     @ClientCurrencyNo,
                     @OriginalEntryDate,
                     @SODeliveryDate,
                     @SOInstructions,
                     @MSLLevel,
                     @SupplierWarranty,
                     --[001] code start                                                                                                 
                     --ihs data passing start                                                                                                          
                     --,@CountryOfOrigin                                                                                                                
                     @CountryOfOriginNo,
                     @LifeCycleStage,
                     @HTSCode,
                     @AveragePrice,
                     @Packing,
                     @PackagingSize,
                     @Descriptions,
                     @IHSProduct,
                     @ECCNCode,
                     --[001] code end                                                              
                     --[002] CODE START ----                                                            
                     @HUBStock,
                     @HUBStockId,
                     @UpdatedBy,
                     @Qnty_SalesOrder
            --[002] CODE END ----                                                                     
            END

            CLOSE db_cursorpolineinsert
            DEALLOCATE db_cursorpolineinsert
            ----******************************** Insert InternalPurchaseOrderLine and Stock table data End ******************************--------------------       
            SET @InternalPurchaseOrderNo =
            (
                SELECT internalpurchaseordernumber
                FROM tbInternalPurchaseOrder
                WHERE InternalPurchaseOrderId = @InternalPurchaseOrderId
            )
            SET @PurchaseOrderNo = @PurchaseOrderId
            SET @Result = 1
            COMMIT TRANSACTION [Tran1]
        END try
        BEGIN catch
            ROLLBACK TRANSACTION [Tran1]
            DECLARE @ErrorMessage NVARCHAR(4000);
            DECLARE @ErrorSeverity INT;
            DECLARE @ErrorState INT;
            SELECT @ErrorMessage = ERROR_MESSAGE(),
                   @ErrorSeverity = ERROR_SEVERITY(),
                   @ErrorState = ERROR_STATE();
            RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
        END catch
    END
    SELECT TOP 1
        @InternalPurchaseOrderId = InternalPurchaseOrderId
    FROM tbInternalPurchaseOrder
    WHERE InternalPurchaseOrderNumber = @InternalPurchaseOrderNo
    UPDATE tbPurchaseOrder
    SET InternalPurchaseOrderNo = @InternalPurchaseOrderId
    WHERE PurchaseOrderId = @PurchaseOrderId


    IF (@HUBStock = 'HUBSTK')
    BEGIN
        EXEC [dbo].[usp_update_PurchaseOrder_Approve] @PurchaseOrderId = @PurchaseOrderId,
                                                      @ApprovedBy = @UpdatedBy,
                                                      @Approve = 1,
                                                      @RowsAffected = 0
        EXEC usp_InsertGIHeaderandGILinesfromIPO @ClientNo = @ClientNo,
                                                 @PurchaseOrderNo = @PurchaseOrderId,
                                                 @Location = @Location,
                                                 @Qnty_Stock = @Qnty_Stock,
                                                 @Qnty_SalesOrder = @Qnty_SalesOrder,
                                                 @StockID = @HUBStockId,
                                                 @IPOPrice = @IPOPrice


        --added by rahil to maintain STO                                                    
        Declare @clientStockNo int
        Declare @STOId bigint
        Declare @ClientAllocationId int
        select @PurchaseHubWarehouseNo = WarehouseNo,
               @Location = [Location]
        from tbStock
        where StockId = @HUBStockId
        select @clientStockNo = Stockid
        from tbStock
        where ParentStockNo = @HUBStockId

        -- Added By Devendra to maintain STO details on ParentStock             
        DECLARE @QuantityInStock INT = 0;
        SELECT @QuantityInStock = QuantityInStock
        FROM tbStock
        WHERE StockID = @HUBStockId

        insert into tbStockTransferOrder
        (
            DateOfTransfer,
            HubUser,
            HubStockNo,
            Part,
            RoHs,
            ManufacturerNo,
            Datecode,
            ProductNo,
            PackageNo,
            HubWareHouse,
            HubLocation,
            TransferQuantity,
            ReasonForTransfer,
            SalePrice,
            BuyPrice,
            ClientStockNo,
            TransferToClient,
            ShipToAddress,
            DesignatedLocation,
            ReceivedBy,
            DLUP,
            UpdatedBy,
            HubCurrencyNo,
            ClientCurrencyNo
        )
        select getdate(),
               @UpdatedBy,
               @HUBStockId,
               @Part,
               @ROHS,
               @ManufacturerNo,
               @DateCode,
               @ProductNo,
               @PackageNo,
               @PurchaseHubWarehouseNo,
               @Location,
               @Quantity,
               @SalesOrderNo,
               @IPOPrice,
               @Price,
               @clientStockNo,
               @ClientNo,
               @SOShipToAddressNo,
               @Location,
               @UpdatedBy,
               getdate(),
               @UpdatedBy,
               @SrCurrencyNo,
               @IPOCurrencyNo
        --added by rahil to maintain STO             


        --soorya swaped @IPOPrice,@Price 24/11/23 8:12        
        select @STOId = stoid
        from tbStockTransferOrder
        where ClientStockNo = @clientStockNo

        SELECT TOP 1
            @ClientAllocationId = AllocationId
        FROM tbAllocation
        WHERE StockNo = @clientStockNo
        order by AllocationId desc

        update tbAllocation
        set STOId = @STOId
        where AllocationId = @ClientAllocationId
        
        DECLARE @StockLogId int
        EXEC usp_insert_StockLog --                                        
            @StockLogTypeNo = 11,                --add from po     
            @StockNo = @clientStockNo,           --                                        
            @QuantityInStock = 0,                --                                        
            @QuantityOnOrder = @Quantity,        --                                        
            @ActionQuantity = @Quantity,         --                                        
            @PurchaseOrderNo = @PurchaseOrderId, --                                        
            @UpdatedBy = @UpdatedBy,             --                                        
            @Detail = 'STO',
            @StockLogId = @StockLogId OUTPUT

        /*********For STO Log Entry in Parent Stock******************/
        EXEC usp_insert_StockLog @StockLogTypeNo = 11,                --add from po                                
                                 @StockNo = @HUBStockId,              --                                        
                                 @QuantityInStock = @QuantityInStock, --                                                        , @QuantityOnOrder = 0 --                                        
                                 @ActionQuantity = @Quantity,         --                                        
                                 @PurchaseOrderNo = @PurchaseOrderId, --                                        
                                 @UpdatedBy = @UpdatedBy,             --                                        
                                 @Detail = 'STO',
                                 @StockLogId = 0;

    END
END