///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrders.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrders.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrders.callBaseMethod(this, "dispose");
	},

	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/PurchaseOrders");
		this._objData.set_DataObject("PurchaseOrders");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Contact", this.getFieldValue("ctlContact"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("IncludeClosed", this.getFieldValue("ctlIncludeClosed"));
		this._objData.addParameter("Buyer", this.getFieldValue("ctlBuyer"));
		this._objData.addParameter("PurchaseOrderNoLo", this.getFieldValue_Min("ctlPurchaseOrderNo"));
		this._objData.addParameter("PurchaseOrderNoHi", this.getFieldValue_Max("ctlPurchaseOrderNo"));
		this._objData.addParameter("DateOrderedFrom", this.getFieldValue("ctlDateOrderedFrom"));
		this._objData.addParameter("DateOrderedTo", this.getFieldValue("ctlDateOrderedTo"));
		this._objData.addParameter("ExpediteDateFrom", this.getFieldValue("ctlExpediteDateFrom"));
		this._objData.addParameter("ExpediteDateTo", this.getFieldValue("ctlExpediteDateTo"));
		this._objData.addParameter("IPONoLo", this.getFieldValue_Min("ctlInternalPurchaseOrderNo"));
		this._objData.addParameter("IPONoHi", this.getFieldValue_Max("ctlInternalPurchaseOrderNo"));
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
				$R_FN.setCleanTextValue(row.Contact),
				$R_FN.setCleanTextValue(row.Date),
				$R_FN.setCleanTextValue(row.Buyer),
				row.Expedite
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrders.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrders", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
