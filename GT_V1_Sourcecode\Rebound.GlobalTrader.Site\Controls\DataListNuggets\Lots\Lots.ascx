<%--Marker    changed by      date           Remarks
   [001]      Umendra         25/02/2019     Add IsInactive parameter--%>
<%@ Control Language="C#" CodeBehind="Lots.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Lots" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCode" runat="server" ResourceTitle="Code" FilterField="Code" />
                <ReboundUI_FilterDataItemRow:CheckBox id="ctlInActive" runat="server" ResourceTitle="IsInactive" FilterField="IsInactive"  /><%--[001]--%> 
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlName" runat="server" ResourceTitle="Name" FilterField="Name" />
			</FieldsRight>
           
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
