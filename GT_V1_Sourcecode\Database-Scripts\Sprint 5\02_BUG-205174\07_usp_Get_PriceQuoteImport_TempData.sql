﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_Get_PriceQuoteImport_TempData', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_Get_PriceQuoteImport_TempData
END
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-205174]		An.TranTan			28-Jun-2024		Update			Get 1 more column and remove useless ALIAS
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_Get_PriceQuoteImport_TempData]
    @DisplayLength int,
    @DisplayStart int,
    @SortCol int,
    @SortDir nvarchar(10),
    @Search nvarchar(255) = NULL,
    @UserId INT,
    @ClientId INT,
    @SelectedClientId INT
AS
BEGIN
    SET nocount off;
    Declare @FirstRec int,
            @LastRec int
    Set @FirstRec = @DisplayStart;
    Set @LastRec = @DisplayStart + @DisplayLength;

    With CTE_Stock
    as (Select ROW_NUMBER() over (order by case
                                               when
                                               (
                                                   @SortCol = 0
                                                   and @SortDir = 'asc'
                                               ) then
                                                   SelectedClientId
                                           end asc
                                 ) as RowNum,
               COUNT(*) over () as TotalCount,
               REPLACE(Column1, '\', '\\') as Column1,
               REPLACE(Column2, '\', '\\') as Column2,
               REPLACE(Column3, '\', '\\') as Column3,
               REPLACE(Column4, '\', '\\') as Column4,
               REPLACE(Column5, '\', '\\') as Column5,
               REPLACE(Column6, '\', '\\') as Column6,
               REPLACE(Column7, '\', '\\') as Column7,
               REPLACE(Column8, '\', '\\') as Column8,
               REPLACE(Column9, '\', '\\') as Column9,
               REPLACE(Column10, '\', '\\') as Column10,
               REPLACE(Column11, '\', '\\') as Column11,
               REPLACE(Column12, '\', '\\') as Column12,
               REPLACE(Column13, '\', '\\') as Column13,
               REPLACE(Column14, '\', '\\') as Column14,
               REPLACE(Column15, '\', '\\') as Column15,
               REPLACE(Column16, '\', '\\') as Column16,
               REPLACE(Column17, '\', '\\') as Column17,
               REPLACE(Column18, '\', '\\') as Column18,
               REPLACE(Column19, '\', '\\') as Column19,
               REPLACE(Column20, '\', '\\') as Column20,
               REPLACE(Column21, '\', '\\') as Column21,
               REPLACE(Column22, '\', '\\') as Column22,
               REPLACE(Column23, '\', '\\') as Column23,
               REPLACE(Column24, '\', '\\') as Column24,
               REPLACE(Column25, '\', '\\') as Column25,
               PriceQuoteImportId,
               ImportDate,
               ClientId,
               SelectedClientId,
               OriginalFilename
        FROM BorisGlobalTraderimports.dbo.tbPriceQuoteImport_tempData
        WHERE (
                  ClientId = @ClientId
                  and SelectedClientId = @SelectedClientId
                  and CreatedBy = @UserId
              )
       )
    SELECT RowNum,
           TotalCount,
           Column1,
           Column2,
           Column3,
           Column4,
           Column5,
           Column6,
           Column7,
           Column8,
           Column9,
           Column10,
           Column11,
           Column12,
           Column13,
           Column14,
           Column15,
           Column16,
           Column17,
           Column18,
           Column19,
           Column20,
           Column21,
           Column22,
           Column23,
           Column24,
           Column25,
           PriceQuoteImportId,
           ImportDate,
           ClientId,
           SelectedClientId,
           OriginalFilename
    from CTE_Stock
    where RowNum > @FirstRec
          and RowNum <= @LastRec
    ORDER BY ImportDate desc
END
GO