Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.initializeBase(this,[n]);this._intLineID="";this._files=[];this._dragobj=null};Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.prototype={get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked))},dispose:function(){this.isDisposed||(this._intLineID=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.callBaseMethod(this,"dispose"))},saveClicked:function(){this.validateForm()&&(this._dragobj!=undefined?this._dragobj&&this._dragobj.startUpload():this.saveGILineImage("",""))},saveGILineImage:function(n,t){var i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData("controls/Nuggets/GILines");i.set_DataObject("GILines");i.set_DataAction("SaveGILineImage");i.addParameter("ID",this._intLineID);i.addParameter("Caption",this.getFieldValue("ctlCaption"));i.addParameter("TempFile",t);i.addDataOK(Function.createDelegate(this,this.saveAddComplete));i.addError(Function.createDelegate(this,this.saveAddError));i.addTimeout(Function.createDelegate(this,this.saveAddError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null},hideForm:function(){$(".ajax-file-upload-red").each(function(){$(this).click()})},saveAddComplete:function(n){this.hideForm();n._result.Result>0?(this.setFieldValue("ctlCaption",""),this.onSaveComplete()):(n._result.Message&&(this._strErrorMessage=n._result.Message),this.onSaveError())},saveAddError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},validateForm:function(){this.onValidate();return this.autoValidateFields()}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);