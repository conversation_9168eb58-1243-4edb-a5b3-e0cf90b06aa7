Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevel=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevel.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevel.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevel.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevel.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/MSLLevel");this._objData.set_DataObject("MSLLevel");this._objData.set_DataAction("GetData")},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Types)for(t=0;t<n.Types.length;t++)this.addOption(n.Types[t].MSLLevels,n.Types[t].MSLLevels)}};Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevel.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.MSLLevel",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);