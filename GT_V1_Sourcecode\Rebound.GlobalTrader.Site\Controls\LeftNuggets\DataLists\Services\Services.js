Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/Services";this._strDataObject="Services";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.callBaseMethod(this,"dispose")},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=String.format('<a href="{0}">{1}<\/a>',$RGT_gotoURL_Service(n.ID),$R_FN.setCleanTextValue(n.Name)),this._tbl.addRow([i],n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Services",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);