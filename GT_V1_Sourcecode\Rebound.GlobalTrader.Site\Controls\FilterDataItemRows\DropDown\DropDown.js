Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown=function(n){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.prototype={get_ddl:function(){return this._ddl},set_ddl:function(n){this._ddl!==n&&(this._ddl=n)},addDropDownChanged:function(n){this.get_events().addHandler("DropDownChanged",n)},removeDropDownChanged:function(n){this.get_events().removeHandler("DropDownChanged",n)},onDropDownChanged:function(){var n=this.get_events().getHandler("DropDownChanged");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.callBaseMethod(this,"initialize");this._ddl&&(this._ddl.addChanged(Function.createDelegate(this,this.onDropDownChanged)),this._ddl._lbx&&($addHandler(this._ddl._lbx,"focus",Function.createDelegate(this,this.onFocus)),$addHandler(this._ddl._lbx,"blur",Function.createDelegate(this,this.onBlur))))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._ddl&&(this._ddl._lbx&&$clearHandlers(this._ddl._lbx),this._ddl.dispose()),this._ddl=null,Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.callBaseMethod(this,"dispose"))},onFocus:function(){this.enableField(!0)},onBlur:function(){this.enableField(this.isEntered())},getValue:function(){return this._ddl?this._ddl.getValue():null},getText:function(){return this._ddl?this._ddl.getText().trim():""},getExtraText:function(){return this._ddl?this._ddl.getExtraText():""},setValue:function(n){this._ddl&&((typeof n=="undefined"||n==null)&&(n=""),this._ddl.setInitialValue(n),this._ddl.setValue(n),this.enableField(n!=""&&n!=this._ddl._strNoValue_Value))},reset:function(){this._ddl&&(this._ddl.reset(),this.enableField(!1))},getDropDownData:function(){this._ddl&&(this._blnGotDropDownData||this._ddl.onRefresh(),this._blnGotDropDownData=!0)},isEntered:function(){return!this._ddl.isSetAsNoValue()}};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown",Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base,Sys.IDisposable);