Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.CounterfeitElectronicParts=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.CounterfeitElectronicParts.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.CounterfeitElectronicParts.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.CounterfeitElectronicParts.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.CounterfeitElectronicParts.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/CounterfeitElectronicParts");this._objData.set_DataObject("CounterfeitElectronicParts");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.CounterfeitElectronicParts.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.CounterfeitElectronicParts",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);