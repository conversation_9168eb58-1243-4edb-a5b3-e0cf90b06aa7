///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.prototype = {

	get_blnShowAllOrders: function() { return this._blnShowAllOrders; }, set_blnShowAllOrders: function(v) { if (this._blnShowAllOrders !== v)  this._blnShowAllOrders = v; }, 
	get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
	initialize: function() {
		this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this.updateFilterVisibility();
		this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
		this._strPathToData = "controls/DataListNuggets/SRMAsShip";
		this._strDataObject = "SRMAsShip";
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.callBaseMethod(this, "initialize");
	},
	
	initAfterBaseIsReady: function() {
		this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
		this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._blnShowAllOrders = null;
		this._IsGlobalLogin = null;
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.callBaseMethod(this, "dispose");
	},
						
	pageTabChanged: function() {
		this._table._intCurrentPage = 1;
		this._blnShowAllOrders = (this._intCurrentTab == 1);
		this.getData();
	},

	setupDataCall: function() {
	    var strAction = "GetData";
	   // alert(this._blnShowAllOrders);
	    if (this._blnShowAllOrders) strAction += "_All";
	    this._objData.set_DataAction(strAction);
	    this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
		    var row = this._objResult.Results[i];
		    var aryData;
		    if (this._IsGlobalLogin) {
		        aryData = [
                   $RGT_nubButton_ShipSRMA(row.ID, row.No)
                   , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                   , $R_FN.writeDoubleCellValue(row.Quantity, row.QuantityAllocated)
                   , $RGT_nubButton_Company(row.CMNo, row.CM)
                   , $R_FN.setCleanTextValue(row.Date)
                   , $RGT_nubButton_PurchaseOrder(row.PONo, row.PO)
                   , $R_FN.setCleanTextValue(row.ClientName)
		        ];
		    } else {
		        aryData = [
                $RGT_nubButton_ShipSRMA(row.ID, row.No)
              , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
              , $R_FN.writeDoubleCellValue(row.Quantity, row.QuantityAllocated)
              , $RGT_nubButton_Company(row.CMNo, row.CM)
              , $R_FN.setCleanTextValue(row.Date)
              , $RGT_nubButton_PurchaseOrder(row.PONo, row.PO)
		        ];
		    }
			this._table.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	},
	updateFilterVisibility: function () {
	    this.getFilterField("ctlClientName").show(this._IsGlobalLogin);
	}
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
