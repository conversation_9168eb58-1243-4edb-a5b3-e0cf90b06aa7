///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - add full disposing event
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.prototype = {

	get_ctlRating: function() { return this._ctlRating; }, set_ctlRating: function(value) { if (this._ctlRating !== value)  this._ctlRating = value; }, 
	get_ddl: function() { return this._ddl; }, set_ddl: function(value) { if (this._ddl !== value)  this._ddl = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.callBaseMethod(this, "initialize");
		this._ctlRating.addChanged(Function.createDelegate(this, this.ctlRating_StarsChanged));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._ctlRating) this._ctlRating.dispose();
		this._ctlRating = null;
		this._ddl = null;
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.callBaseMethod(this, "dispose");
	},
	
	ctlRating_StarsChanged: function() {
		this.enableField(this._ctlRating._intCurrentRating > 0);
	},
	
	getMinValue: function() {
		if (this._ctlRating._intCurrentRating == null) this._ctlRating._intCurrentRating = 0;
		var obj = $R_FN.parseComparisonToMinMax(this._ddl.value, this._ctlRating._intCurrentRating);
		return obj.Min;
	},
	
	getMaxValue: function() {
		if (this._ctlRating._intCurrentRating == null) this._ctlRating._intCurrentRating = 0;
		var obj = $R_FN.parseComparisonToMinMax(this._ddl.value, this._ctlRating._intCurrentRating);
		return obj.Max;
	},
	
	setValue: function(v) {
		if (typeof(v) == "undefined" || v == null) v = 0;
		v = Number.parseInvariant(v.toString());
		this._ctlRating.setRating(v);
		this.enableField(false);
		if (v > 0) this.enableField(true);
	},
	
	getValue: function(v) {
		if (this._ctlRating._intCurrentRating == null) this._ctlRating._intCurrentRating = 0;
		return this._ctlRating._intCurrentRating;
	},

	deriveFilterExpression: function() {
		this._strFilterExpression = (this._blnOn) ? String.format("[{0}] {1} {2}", this._strFilterField, $R_FN.getNumericalComparator(this._ddl.value), this._ctlRating._intCurrentRating) : "";
		return this._strFilterExpression;
	},
	
	reset: function() {
		this._ctlRating.resetRating();
		this.enableField(false);
	},
	
	setComparisonType: function(enmComparisonType) {
		this._ddl.value = enmComparisonType;
	}
	
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating", Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base, Sys.IDisposable);
