## Pre-requisite for Batch File:
********************************
- The batch script deploys all the .sql files present in a directory or sub-directories in an order.
- NO SPACE in directory or sub-directories.
- NO SPACE in .sql file name.
- To execute .sql in order, add a numeric sequence as follows:
	+ 01_createtable.sql
	+ 02_insertdata.sql
	+ 03_execstoredproc....etc.
- All .sql files must be saved with the encoding UTF-8-BOM
- Create a "logs" folder inside the "Script_Release" folder

## Check script confict:
************************
- For Each Sprint, create folder with name Compare_Scripts
- Create sub-directories:
	+ Tables
	+ Views
	+ SP
	+ Ufn
- Before changing the script, the DEV team backs up the script from the production environment and saves it in the correct folder for each type of script to compare and resolve conflicts, if any.

