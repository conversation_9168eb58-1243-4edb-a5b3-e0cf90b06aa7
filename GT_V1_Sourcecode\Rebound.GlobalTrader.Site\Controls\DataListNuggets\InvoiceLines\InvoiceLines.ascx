<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           14/09/2012   Add airwaybill search
[002]     <PERSON>     07/02/2020    Add WareHouse Search filter
[003]     A<PERSON><PERSON><PERSON>    02/12/2021  Add new filter for "not exported invoices".
[004]     Ravi            07/02/2020    RP-2338  AS6081 Search/Filter functionality on different pages 
--%>
<%@ Control Language="C#" CodeBehind="InvoiceLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.InvoiceLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlInvoiceNo" runat="server" ResourceTitle="InvoiceNo" FilterField="InvoiceNo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludePaid" runat="server" ResourceTitle="PaidOnly" FilterField="IncludePaid" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
                	<%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
				<%--[001]Code End--%>
                <ReboundUI_FilterDataItemRow:CheckBox id="chkHold" runat="server" ResourceTitle="InvoiceHold" FilterField="InvoiceHold" />
			    <ReboundUI_FilterDataItemRow:DropDown id="ctllstCountry" runat="server"  ResourceTitle="ShipToCountry" DropDownType="Country" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="CountryNo" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesmanName" runat="server" ResourceTitle="SalesmanName" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerPO" runat="server" ResourceTitle="CustomerPurchaseOrderNo" FilterField="CustPO" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlSONo" runat="server" ResourceTitle="SalesOrderNo" FilterField="SONo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlInvoiceDateFrom" runat="server" ResourceTitle="DateInvoicedFrom" FilterField="InvoiceDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlInvoiceDateTo" runat="server" ResourceTitle="DateInvoicedTo" FilterField="InvoiceDateTo" />
				<%--[001] code start--%>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlAirWayBill" runat="server" ResourceTitle="AirWayBill" FilterField="AirWayBill" />
               	<%--[001] code end--%>
                <%--[002] code start--%>
                  <ReboundUI_FilterDataItemRow:DropDown id="ctlWarehouse" runat="server" DropDownType="Warehouse" ResourceTitle="Warehouse" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Warehouse" />
                <%--[002] code end--%>
                <%--[003] code start--%>
                <ReboundUI_FilterDataItemRow:CheckBox id="chknotexportedinvoices" runat="server" ResourceTitle="notexportedinvoices" FilterField="notexportedinvoices" />
                <%--[003] code end--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlAS6081" runat="server" ResourceTitle="AS6081Filternew" DropDownType="CounterfeitElectronicParts" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="AS6081" /> <%--[004]--%>
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
