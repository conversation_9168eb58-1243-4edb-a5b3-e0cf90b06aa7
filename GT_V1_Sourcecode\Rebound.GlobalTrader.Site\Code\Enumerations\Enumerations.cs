//Marker     Changed by      Date               Remarks
//[001]      Vinay           25/06/2013         CR:- Supplier Invoice
using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.ComponentModel;
using System.Reflection;

/// <summary>
/// Enumerations
/// </summary>
namespace Rebound.GlobalTrader.Site {

	/// <summary>
	/// View level
	/// *** MAKE SURE THIS ENUM IS COPIED TO JAVASCRIPT (as ViewLevel) ***
	/// </summary>
	public enum ViewLevelList {
		My = 0,
		Team = 1,
		Division = 2,
		Company = 3
	}

    //[001] code start
    public enum SupplierInvoiceStatus
    {
        All=1,
        ExportedOnly=2,
        ApproveandUnExported=3
    }
    //[001] code end

    public enum CustReqAlternative
    {
        Alternative = 1,
        PossibleAlternative = 2,
        FirmAlternative = 3
    }

    public enum DataListNuggetExport
    {
        HUBRFQ = 0,
        SalesOrder = 1
    }
}
