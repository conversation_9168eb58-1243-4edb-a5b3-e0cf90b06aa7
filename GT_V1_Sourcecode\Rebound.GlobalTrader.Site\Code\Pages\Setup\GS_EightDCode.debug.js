///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.prototype = {

    get_ctlEightDCode: function() { return this._ctlEightDCode; }, set_ctlEightDCode: function(v) { if (this._ctlEightDCode !== v) this._ctlEightDCode = v; },
    get_ctlEightDSubCategory: function() { return this._ctlEightDSubCategory; }, set_ctlEightDSubCategory: function(v) { if (this._ctlEightDSubCategory !== v) this._ctlEightDSubCategory = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.callBaseMethod(this, "initialize");
    },

    goInit: function() {
    
         this._ctlEightDCode.addSelectCategory(Function.createDelegate(this, this.ctlEightDCode_SelectCategory));
        // if (this._ctlCertificate) this._ctlCertificate.addChangedData(Function.createDelegate(this, this.ctlCurrencyRates_ChangedData));
        Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlEightDCode) this._ctlEightDCode.dispose();
        if (this._ctlEightDSubCategory) this._ctlEightDSubCategory.dispose();
        this._ctlEightDCode = null;
        this._ctlEightDSubCategory = null;
        Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.callBaseMethod(this, "dispose");
    },

    ctlEightDCode_SelectCategory: function() {
   
        this._ctlEightDSubCategory._intcategoryID = this._ctlEightDCode._intCategoryID;
        this._ctlEightDSubCategory._tbl.resizeColumns();
        this._ctlEightDSubCategory.show(true);
        this._ctlEightDSubCategory.refresh();
    }

};

Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_EightDCode", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
