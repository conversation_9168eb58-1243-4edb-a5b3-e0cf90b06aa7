Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.IconButton=function(){};var $R_IBTN=Rebound.GlobalTrader.Site.Controls.IconButton;Rebound.GlobalTrader.Site.Controls.IconButton.addClick=function(n,t){$addHandler($get(n.id+"_hyp"),"click",t);n=null};Rebound.GlobalTrader.Site.Controls.IconButton.clearHandlers=function(n){var t=$get(n.id+"_hyp");t&&$clearHandlers(t);t=null;n=null};Rebound.GlobalTrader.Site.Controls.IconButton.enableButton=function(n,t){$R_FN.showElement($get(n.id+"_hyp"),t);$R_FN.showElement($get(n.id+"_lblDisabled"),!t);n=null};Rebound.GlobalTrader.Site.Controls.IconButton.showButton=function(n,t){$R_FN.showElement(n,t);n=null};Rebound.GlobalTrader.Site.Controls.IconButton.isButtonEnabled=function(n){var t=$R_FN.isElementVisible($get(n.id+"_hyp"));return n=null,t};Rebound.GlobalTrader.Site.Controls.IconButton.setHref=function(n,t){var i=$get(n.id+"_hyp");i&&(i.href=t,n=null)};Rebound.GlobalTrader.Site.Controls.IconButton.updateText=function(n,t){$R_FN.setInnerHTML($get(n.id+"_hyp"),t);$R_FN.setInnerHTML($get(n.id+"_lblDisabled"),t);n=null};Rebound.GlobalTrader.Site.Controls.IconButton.registerClass("Rebound.GlobalTrader.Site.Controls.IconButton");