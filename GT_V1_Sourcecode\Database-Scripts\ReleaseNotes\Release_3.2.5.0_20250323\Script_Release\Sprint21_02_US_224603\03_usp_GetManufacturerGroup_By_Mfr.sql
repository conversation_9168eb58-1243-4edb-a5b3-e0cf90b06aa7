﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
========================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-224603]		Phuc Hoang			05-Feb-2025		CREATE			IPO- Sourcing - Check Supplier/ MFR Data- Addition of MFR Group Name
========================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_GetManufacturerGroup_By_Mfr](
	@ManufacturerId  NVARCHAR (100) = NULL
)
AS

BEGIN
	SELECT mfr.ContactGroupID, cgr.ContactName, cgr.Code FROM [dbo].[tbManufacturer] mfr
	JOIN [dbo].[tbContactGroup] cgr ON cgr.ItemId = mfr.ContactGroupID
	WHERE mfr.ManufacturerId = @ManufacturerId 
END
GO


