//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class FormErrors {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FormErrors() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.FormErrors", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter your email address.
        /// </summary>
        internal static string EnterEmailAddress {
            get {
                return ResourceManager.GetString("EnterEmailAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter your password.
        /// </summary>
        internal static string EnterPassword {
            get {
                return ResourceManager.GetString("EnterPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter your username.
        /// </summary>
        internal static string EnterUsername {
            get {
                return ResourceManager.GetString("EnterUsername", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There were some problems with your form&lt;br /&gt;Please check below and try again..
        /// </summary>
        internal static string FormProblems {
            get {
                return ResourceManager.GetString("FormProblems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select your client.
        /// </summary>
        internal static string SelectClient {
            get {
                return ResourceManager.GetString("SelectClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a date between current date and end of the current calendar month.
        /// </summary>
        internal static string SOLineEdit_DatePromisedNotInRange {
            get {
                return ResourceManager.GetString("SOLineEdit_DatePromisedNotInRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter a date before the end of the current calendar month.
        /// </summary>
        internal static string SOLineEdit_DatePromisedNotInRange1 {
            get {
                return ResourceManager.GetString("SOLineEdit_DatePromisedNotInRange1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The file you have selected is too large. Please select a file smaller than {0}mb..
        /// </summary>
        internal static string UploadFileTooBig {
            get {
                return ResourceManager.GetString("UploadFileTooBig", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If Anything Not Selected Then All Manufacturers Selected By Default..
        /// </summary>
        internal static string WarningManufacturer {
            get {
                return ResourceManager.GetString("WarningManufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If Anything Not Selected Then All Products Selected By Default..
        /// </summary>
        internal static string WarningProduct {
            get {
                return ResourceManager.GetString("WarningProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to If Anything Not Selected Then All Vendors Selected By Default..
        /// </summary>
        internal static string WarningVendor {
            get {
                return ResourceManager.GetString("WarningVendor", resourceCulture);
            }
        }
    }
}
