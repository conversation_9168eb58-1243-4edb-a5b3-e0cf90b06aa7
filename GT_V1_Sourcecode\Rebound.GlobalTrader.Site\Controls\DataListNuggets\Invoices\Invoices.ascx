<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           21/09/2012   Add expoted only filter
[002]      Vinay           22/11/2012   Add Failed only  filter
[003]     <PERSON>     07/02/2020    Add WareHouse Search filter
--%>
<%@ Control Language="C#" CodeBehind="Invoices.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Invoices" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <Filters>
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
            <FieldsLeft>
                <ReboundUI_FilterDataItemRow:Numerical ID="ctlInvoiceNo" runat="server" ResourceTitle="InvoiceNo" FilterField="InvoiceNo" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlIncludePaid" runat="server" ResourceTitle="IncludePaid" FilterField="IncludePaid" DefaultValue="true" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlSalesmanName" runat="server" ResourceTitle="SalesmanName" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlCOC" runat="server" ResourceTitle="COC" FilterField="COC" DefaultValue="false" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlPackaginSlip" runat="server" ResourceTitle="PackagingSlip" FilterField="PackagingSlip" DefaultValue="false" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="chkHold" runat="server" ResourceTitle="IncludeOnHold" FilterField="InvoiceHold" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctllstCountry" runat="server" ResourceTitle="ShipToCountry" DropDownType="Country" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="CountryNo" />
            </FieldsLeft>
            <FieldsRight>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCustomerPO" runat="server" ResourceTitle="CustomerPurchaseOrderNo" FilterField="CustPO" />
                <ReboundUI_FilterDataItemRow:Numerical ID="ctlSONo" runat="server" ResourceTitle="SalesOrderNo" FilterField="SONo" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlInvoiceDateFrom" runat="server" ResourceTitle="DateInvoicedFrom" FilterField="InvoiceDateFrom" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlInvoiceDateTo" runat="server" ResourceTitle="DateInvoicedTo" FilterField="InvoiceDateTo" />
                <%--[001] code start--%>
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlExportedOnly" runat="server" ResourceTitle="ExportedOnly" FilterField="ExportedOnly" DefaultValue="true" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlNotExported" runat="server" ResourceTitle="NotExported" FilterField="NotExported" DefaultValue="false" />
                <%--[001] code start--%>
                <%--[002] code start--%>
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlFailed" runat="server" ResourceTitle="FailedOnly" FilterField="FailedOnly" DefaultValue="false" />
                <%--[002] code end--%>
                <%--[003] code start--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlWarehouse" runat="server" DropDownType="Warehouse" ResourceTitle="Warehouse" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Warehouse" />
                <%--[003] code start--%>
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>
        <div id="overlay" style="display: none"></div>
    </Filters>
    <Forms>
        <ReboundForm:Invoices_Confirm ID="ctlConfirm" runat="server" />
    </Forms>
    <Content>
        <script type="text/javascript" src="js/jquery-ui.js"></script>

        <style type="text/css">
            .custom-format-dialog {
                position: absolute;
                top: 40%;
                left: 40%;
                border-radius: 5px;
                background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
                background-repeat: repeat-x;
                z-index: 2;
            }

            .ui-dialog-titlebar {
                font-weight: normal;
                padding: 5px;
                font-family: Lucida Sans Unicode, Arial !important;
                font-size: 12px;
                color: black;
            }

            .ui-dialog-titlebar-close {
                display: none;
            }

            .custom-modal {
                background-color: #56954E;
                color: white;
                padding-left: 5px;
                width: auto;
                height: 110px;
            }

            .modal-content-title {
                border-bottom: dotted 1px #cccccc;
                padding: 5px 0px;
                margin-bottom: 5px;
            }

            .modal-footer {
                background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
                background-repeat: repeat-x;
                border-bottom-left-radius: 5px;
                border-bottom-right-radius: 5px;
                padding: 5px;
            }

            #overlay {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0,0,0,0.5);
                z-index: 1;
                cursor: pointer;
            }

            .radio-container {
                display: flex;
                align-items: center;
            }

                .radio-container input[type=radio] {
                    margin-bottom: 1px;
                }
        </style>
        <script type="text/javascript">
            $(function () {
                // Initialize the modal dialog
                $("#formatModal").dialog({
                    autoOpen: false, // Start closed
                    modal: true,
                    resizable: false,
                    draggable: false,
                    width: 400,
                    dialogClass: 'custom-format-dialog'
                });
            });
        </script>
        <div id="formatModal" class="modal" title="Print Invoice" style="display: none">
            <div class="modal-content custom-modal">
                <div>
                    <h4 id="formatActionTitle" class="modal-content-title">PRINT INVOICE</h4>
                    <div style="padding-bottom: 5px">Select the generate format of invoice</div>
                    <div class="radio-container">
                        <div id="optionPDF" style="padding-right: 15px">
                            <input type="radio" name="rdInvoiceFormat" id="rdPDF" value="PDF" />
                            <label for="rdPDF">PDF Format</label>
                        </div>
                        <div id="optionXML" style="display: none;">
                            <input type="radio" name="rdInvoiceFormat" id="rdXml" value="XML" />
                            <label for="rdXml">XML Format</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <span>
                    <a id="btnFormatAction" class="iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft" href="javascript:void(0);">Print</a>
                </span>
                <span>
                    <a id="btnFormatCancel" class="iconButton iconButton_Nugget iconButton_Nugget_Cancel iconButton_alignLeft" href="javascript:void(0);">Cancel</a>
                </span>
            </div>
        </div>
    </Content>
</ReboundUI_Nugget:DesignBase>
