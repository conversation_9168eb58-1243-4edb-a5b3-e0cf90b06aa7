using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;


namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class BOMCusReqSourcingResults_Add : Base {

        #region Locals
       
        protected FlexiDataTable _tblPartdetails;
        protected Panel _pnlPartDetail;
        #endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CusReqSourcingResults_Add");
			AddScriptReference("Controls.Nuggets.BOMCusReqSourcingResults.Add.BOMCusReqSourcingResults_Add.js");
            WireUpControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
               _pnlPartDetail = (Panel)ctlDesignBase.FindContentControl("pnlPartDetail");
        }
		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _objQSManager.CustomerRequirementID);
            _scScriptControlDescriptor.AddElementProperty("lblUpliftPrice", FindFieldControl("ctlPrice", "lblUpliftPrice").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblQty", FindFieldControl("ctlQuantity", "lblQty").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblSupplierPrice", FindFieldControl("ctlSupplierPrice", "lblSupplierPrice_Currency").ClientID);
            //ihs code
            _scScriptControlDescriptor.AddElementProperty("btn1", FindFieldControl("ctlPartNo", "btn1").ClientID);
            _scScriptControlDescriptor.AddElementProperty("btn2", FindFieldControl("ctlPartNo", "btn2").ClientID);
             _scScriptControlDescriptor.AddElementProperty("lblError", FindFieldControl("ctlPartNo", "lblError").ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlPartDetail", _pnlPartDetail.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctltblPartdetails", ((ItemSearch.Base)FindContentControl("ctltblPartdetails")).ctlDesignBase.ClientID);
            //ihs code end
		}

	}
}