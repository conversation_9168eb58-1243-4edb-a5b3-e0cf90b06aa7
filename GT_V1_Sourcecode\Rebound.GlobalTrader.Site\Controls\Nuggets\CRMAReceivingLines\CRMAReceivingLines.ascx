<%@ Control Language="C#" CodeBehind="CRMAReceivingLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Lines" BoxType="Standard">

	<Links>
		<ReboundUI:IconButton ID="ibtnReceive" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Receive" />
	</Links>
	
	<Content>
		<ReboundUI:FlexiDataTable ID="tblAll" runat="server" AllowSelection="true" PanelHeight="150" />
		
		<asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
		<asp:Panel ID="pnlLineDetailError" runat="server" CssClass="error invisible" />
		<asp:Panel id="pnlLineDetail" runat="server" CssClass="invisible">
			<h4 class="extraTopMargin"><asp:HyperLink ID="hypPrev" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingPrev">&laquo;</asp:HyperLink><asp:Label ID="lblLineNumber" runat="server" /><asp:HyperLink ID="hypNext" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingNext">&raquo;</asp:HyperLink></h4>
			<table class="twoCols">
				<tr>
					<td class="col1">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlQuantityOrdered" runat="server" ResourceTitle="QuantityOrdered" />
							<ReboundUI:DataItemRow id="ctlQuantityReceived" runat="server" ResourceTitle="QuantityReceived" />
							<ReboundUI:DataItemRow id="ctlQuantityOutstanding" runat="server" ResourceTitle="QuantityOutstanding" />
							<ReboundUI:DataItemRow id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
							<ReboundUI:DataItemRow id="ctlROHS" runat="server" ResourceTitle="ROHS" />
							<ReboundUI:DataItemRow id="hidROHS" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow id="ctlDateCode" runat="server" ResourceTitle="DateCode" />
							<ReboundUI:DataItemRow id="ctlCustomerPart" runat="server" ResourceTitle="CustomerPart" />
						</table>
					</td>
					<td class="col2">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlReturnDate" runat="server" ResourceTitle="ReturnDate" />
							<ReboundUI:DataItemRow id="ctlManufacturer" runat="server" ResourceTitle="Manufacturer" />
							<ReboundUI:DataItemRow id="hidManufacturer" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidManufacturerNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlPackage" runat="server" ResourceTitle="Package" />
							<ReboundUI:DataItemRow id="hidPackageNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlProduct" runat="server" ResourceTitle="Product" />
							<ReboundUI:DataItemRow id="hidProductNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlReason" runat="server" ResourceTitle="Reason" />
							<ReboundUI:DataItemRow id="ctlLineNotes" runat="server" ResourceTitle="Notes" />
						</table>
					</td>
				</tr>
			</table>
		</asp:Panel>
	</Content>
	
	<Forms>
		<ReboundForm:CRMALines_Receive id="frmReceive" runat="server" />
        <ReboundForm:CRMALines_Confirm id="frmSerialNo" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
