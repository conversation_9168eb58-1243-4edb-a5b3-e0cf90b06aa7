///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.Manufacturers = function(element){
	Rebound.GlobalTrader.Site.Controls.AutoSearch.Manufacturers.initializeBase(this, [element]);
	//this._blnIsSelectionAllowed = true;
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Manufacturers.prototype = {

	get_ShowInactive: function () { return this._ShowInactive; }, set_ShowInactive: function (value) { if (this._ShowInactive !== value) this._ShowInactive = value; },

	initialize: function(){
		Rebound.GlobalTrader.Site.Controls.AutoSearch.Manufacturers.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
    	//this.addSetupParametersEvent(Function.createDelegate(this, this.setupParameters));
        //this.addSelectionMadeEvent(Function.createDelegate(this, this.selectionMade));
	
		this.setupDataObject("Manufacturers");
	},
	
	dispose: function(){
		if (this.isDisposed) return;
		this._ShowInactive = null;
		//this._blnIsSelectionAllowed = null;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.Manufacturers.callBaseMethod(this, "dispose");
	},
	
//	setupParameters: function() {
//	alert("hi");
//	//this.addParameter("blnShowInactive", this._ShowInactive);
//		this.addDataParameter("blnShowInactive", this._ShowInactive);
//	},

    //[001] code start
    //selectionMade: function () {
    //    var extar = this._varSelectedExtraData;
    //    setTimeout(function () { if (extar == true) { alert($R_RES.HazardousMessage); } }, 300);

    //},
    //[001] code end
	
	dataReturned: function(){
	
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = "";
				if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
					strHTML = $RGT_nubButton_Manufacturer(res.ID, res.Name);
				} else {
					strHTML = $R_FN.setCleanTextValue(res.Name);
				}
				this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID, this._blnIsSelectionAllowed);
                //this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID,1);
				strHTML = null;
				res = null;
			}
		}
	}
};
Rebound.GlobalTrader.Site.Controls.AutoSearch.Manufacturers.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Manufacturers", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
