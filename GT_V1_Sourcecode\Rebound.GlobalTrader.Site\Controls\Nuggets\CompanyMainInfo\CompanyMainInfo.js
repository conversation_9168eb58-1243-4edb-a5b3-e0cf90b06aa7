Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyMainInfo.initializeBase(this,[n]);this._intCompanyID=-1;this._intContactID=-1;this._PremierCustomer=!1;this._T2PremierCustomer=!1;this._rebateAccount=!1;this._blnCanEditPremierCustomer=!1;this._blnCanEditTier2PremierCustomer=!1;this._blnCanEditisPurchasingNotes=!1;this._displayAdvisory=!1;this._inactive=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyMainInfo.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnContacts:function(){return this._ibtnContacts},set_ibtnContacts:function(n){this._ibtnContacts!==n&&(this._ibtnContacts=n)},get_pnlContactInfo:function(){return this._pnlContactInfo},set_pnlContactInfo:function(n){this._pnlContactInfo!==n&&(this._pnlContactInfo=n)},get_tblContacts:function(){return this._tblContacts},set_tblContacts:function(n){this._tblContacts!==n&&(this._tblContacts=n)},get_enmCompanyListType:function(){return this._enmCompanyListType},set_enmCompanyListType:function(n){this._enmCompanyListType!==n&&(this._enmCompanyListType=n)},get_blnCanEditCompanyType:function(){return this._blnCanEditCompanyType},set_blnCanEditCompanyType:function(n){this._blnCanEditCompanyType!==n&&(this._blnCanEditCompanyType=n)},get_blnCanEditPremierCustomer:function(){return this._blnCanEditPremierCustomer},set_blnCanEditPremierCustomer:function(n){this._blnCanEditPremierCustomer!==n&&(this._blnCanEditPremierCustomer=n)},get_blnCanEditAccountNotes:function(){return this._blnCanEditAccountNotes},set_blnCanEditAccountNotes:function(n){this._blnCanEditAccountNotes!==n&&(this._blnCanEditAccountNotes=n)},get_blnCanEditTier2PremierCustomer:function(){return this._blnCanEditTier2PremierCustomer},set_blnCanEditTier2PremierCustomer:function(n){this._blnCanEditTier2PremierCustomer!==n&&(this._blnCanEditTier2PremierCustomer=n)},get_blnCanEditisPurchasingNotes:function(){return this._blnCanEditisPurchasingNotes},set_blnCanEditisPurchasingNotes:function(n){this._blnCanEditisPurchasingNotes!==n&&(this._blnCanEditisPurchasingNotes=n)},get_IsDiffrentClient:function(){return this._IsDiffrentClient},set_IsDiffrentClient:function(n){this._IsDiffrentClient!==n&&(this._IsDiffrentClient=n)},get_AllowActiveCompany:function(){return this._AllowActiveCompany},set_AllowActiveCompany:function(n){this._AllowActiveCompany!==n&&(this._AllowActiveCompany=n)},get_IsGSAEditPermission:function(){return this._IsGSAEditPermission},set_IsGSAEditPermission:function(n){this._IsGSAEditPermission!==n&&(this._IsGSAEditPermission=n)},get_AllowActiveCompany:function(){return this._AllowActiveCompany},set_AllowActiveCompany:function(n){this._AllowActiveCompany!==n&&(this._AllowActiveCompany=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},get_ibtnCreditLimit:function(){return this._ibtnCreditLimit},set_ibtnCreditLimit:function(n){this._ibtnCreditLimit!==n&&(this._ibtnCreditLimit=n)},get_ibtnActive:function(){return this._ibtnActive},set_ibtnActive:function(n){this._ibtnActive!==n&&(this._ibtnActive=n)},get_ibtnInactive:function(){return this._ibtnInactive},set_ibtnInactive:function(n){this._ibtnInactive!==n&&(this._ibtnInactive=n)},get_pnlCreditLimitToolTip:function(){return this._pnlCreditLimitToolTip},set_pnlCreditLimitToolTip:function(n){this._pnlCreditLimitToolTip!==n&&(this._pnlCreditLimitToolTip=n)},get_hypNewCreditLimit:function(){return this._hypNewCreditLimit},set_hypNewCreditLimit:function(n){this._hypNewCreditLimit!==n&&(this._hypNewCreditLimit=n)},get_pnlCreditLimit:function(){return this._pnlCreditLimit},set_pnlCreditLimit:function(n){this._pnlCreditLimit!==n&&(this._pnlCreditLimit=n)},addInactiveCompanyComplete:function(n){this.get_events().addHandler("InactiveCompanyComplete",n)},removeInactiveCompanyComplete:function(n){this.get_events().removeHandler("InactiveCompanyComplete",n)},onInactiveCompanyComplete:function(){var n=this.get_events().getHandler("InactiveCompanyComplete");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._IsDiffrentClient==!0?this._IsGSA==!0?this._IsGSAEditPermission==!0?($("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlAddresses_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCRMProspects_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlContactLog_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCMPDocumentsNew_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlGSA_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSalesInfo_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPurchasingInfo_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCompanyCertificate_ctlDB_pnlLinks").show()):($("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlAddresses_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlCRMProspects_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlContactLog_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlCMPDocumentsNew_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlGSA_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlSalesInfo_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlPurchasingInfo_ctlDB_pnlLinks").hide(),$("#ctl00_cphMain_ctlCompanyCertificate_ctlDB_pnlLinks").hide()):($("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlAddresses_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCRMProspects_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlContactLog_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCMPDocumentsNew_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlGSA_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSalesInfo_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPurchasingInfo_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCompanyCertificate_ctlDB_pnlLinks").show()):($("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlAddresses_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCRMProspects_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlContactLog_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCMPDocumentsNew_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlGSA_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlSalesInfo_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlPurchasingInfo_ctlDB_pnlLinks").show(),$("#ctl00_cphMain_ctlCompanyCertificate_ctlDB_pnlLinks").show());this.showGroupCodeName();this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)),this._frmEdit.addSaveError(Function.createDelegate(this,this.saveEditError)),this._frmEdit._blnCanEditCompanyType=this._blnCanEditCompanyType,this._frmEdit._blnCanEditAccountNotes=this._blnCanEditAccountNotes,this._frmEdit._blnCanEditPremierCustomer=this._blnCanEditPremierCustomer,this._frmEdit._blnCanEditTier2PremierCustomer=this._blnCanEditTier2PremierCustomer,this._frmEdit._blnCanEditisPurchasingNotes=this._blnCanEditisPurchasingNotes);this._ibtnCreditLimit&&(this._hypNewCreditLimit&&$addHandler(this._hypNewCreditLimit,"click",Function.createDelegate(this,this.OpenCreditLimit)),$addHandler(this._ibtnCreditLimit,"mouseover",Function.createDelegate(this,this.showTopIcons)),$addHandler(this._pnlCreditLimitToolTip,"mouseover",Function.createDelegate(this,this.showTopIcons)),$addHandler(this._ibtnCreditLimit,"mouseout",Function.createDelegate(this,this.hideTopIcons)),$addHandler(this._pnlCreditLimitToolTip,"mouseout",Function.createDelegate(this,this.hideTopIcons)));this._ibtnActive&&$R_IBTN.addClick(this._ibtnActive,Function.createDelegate(this,this.showActivePopup));this._ibtnInactive&&$R_IBTN.addClick(this._ibtnInactive,Function.createDelegate(this,this.showInactivePopup))},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._frmEdit&&this._frmEdit.dispose(),this._tblContacts&&this._tblContacts.dispose(),this._frmEdit=null,this._ibtnEdit=null,this._ibtnContacts=null,this._pnlContactInfo=null,this._tblContacts=null,this._frmEdit=null,this._intCompanyID=null,this._intContactID=null,this._blnCanEditCompanyType=null,this._blnCanEditAccountNotes=null,this._blnCanEditPremierCustomer=null,this._blnCanEditTier2PremierCustomer=null,this._blnCanEditisPurchasingNotes=null,this._PremierCustomer=null,this._T2PremierCustomer=null,this._rebateAccount=null,this._ibtnCreditLimit=null,this._ibtnActive=null,this.__AllowActiveCompany=null,this._pnlCreditLimitToolTip=null,this._hypNewCreditLimit=null,this._pnlCreditLimit=null,this._displayAdvisory=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyMainInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyMainInfo");n.set_DataObject("CompanyMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t,c,l,u,r,h,i;t=n._result;this.setFieldValue("ctlCompanyName",$R_FN.getCleanTextValue(t.Name));this.setFieldValue("ctlGroup","");this.setFieldValue("ctlGroup",$RGT_nubButton_Company(t.ParentCompanyNo,t.ParentCompanyName));this.setFieldValue("hidParentCompanyID",t.ParentCompanyNo);this.setFieldValue("hidParentCompany",$R_FN.getCleanTextValue(t.ParentCompanyName));this.setFieldValue("ctlCompanyType",$R_FN.getCleanTextValue(t.CompanyType));this.setFieldValue("hidCustomerGroupCode",t.GroupCodeNo);this.setFieldValue("ctlCustomerGroupCode",$R_FN.getCleanTextValue(t.CustomerGroupName));this.setFieldValue("hidCompanyTypeID",t.CompanyTypeNo);this.setFieldValue("ctlTel",t.Tel);this.setFieldValue("ctlTel800",t.Tel800);this.setFieldValue("ctlFax",t.Fax);this.setFieldValueEmail("ctlEmail",$R_FN.getCleanTextValue(t.Email));this.setFieldValueURL("ctlURL",t.URL,t.URL,"",!0);this.setFieldValue("ctlVATNumber",t.VATNumber);this.setFieldValue("ctlCmpRegNo",t.CompanyRegNo);this.setFieldValue("ctlCertificationNotes",$R_FN.getCleanTextValue(t.certificateNotes));MainInfoCertificationNotes=$R_FN.getCleanTextValue(t.certificateNotes);this.setFieldValue("ctlQualityNotes",$R_FN.getCleanTextValue(t.qualityNotes));MainInfoCompanyQualityNotes=$R_FN.getCleanTextValue(t.qualityNotes);this.setFieldValue("ctlIsTraceability",t.IsTraceability);this.showField("ctlIsTraceability",!1);this.setFieldValue("ctlEARIMember",t.EARIMember);this.setFieldValue("ctlEARIReported",t.EARIReported);this.setFieldValue("ctlSanction",t.IsSanctioned);this.setFieldValue("ctlNotes",$R_FN.getCleanTextValue(t.Notes));this.setFieldValue("ctlAdvisoryNotes",$R_FN.getCleanTextValue(t.AdvisoryNotes));this.setFieldValue("hidIsDisplayAdvisory",t.IsDisplayAdvisory);this._displayAdvisory=t.IsDisplayAdvisory;MainInfoCompanyNotes=$R_FN.getCleanTextValue(t.Notes);this.setFieldValue("ctlImportantNotes",$R_FN.getCleanTextValue(t.ImportantNotes));MainInfoCompanyImportantNotes=$R_FN.getCleanTextValue(t.ImportantNotes);this.setFieldValue("ctlSalesperson",this.showSalesMan(t.Salesman,t.SalesAccInDay));this.setFieldValue("hidSalespersonNo",t.SalesmanNo);this.setFieldValue("hidLastReviewDate",t.LastReviewDate);this.setDLUP(t.DLUP);this.populateContactList(t);this.setFieldValue("hidUPLiftPrice",t.UPLiftPrice);var f="",e="",o="",s="";if(t.IndustryTypes)for(i=0;i<t.IndustryTypes.length;i++)t.IndustryTypes[i].Selected&&(f.length>0&&(f+=", "),f+=t.IndustryTypes[i].Name,e.length>0&&(e+="||"),e+=t.IndustryTypes[i].ID),o.length>0&&(o+="||"),o+=t.IndustryTypes[i].Name,s.length>0&&(s+="||"),s+=t.IndustryTypes[i].ID;if(this.setFieldValue("ctlIndustryType",f),this.setFieldValue("hidIndustryTypeIDs",e),this.setFieldValue("hidAllIndustryTypeNames",o),this.setFieldValue("hidAllIndustryTypeIDs",s),this.setFieldValue("hidReviewDate",$R_FN.getCleanTextValue(t.ReviewDate)),this.setFieldValue("hidPreviousReviewDate",$R_FN.getCleanTextValue(t.PreviousReviewDate)),this.setFieldValue("hidSupplierWarranty",t.SupplierWarranty),this.setFieldValue("hidGlobalClientNo",t.ClientNo),this.setFieldValue("ctlEORINumber",t.EORINumber),this.setFieldValue("ctlProductReqTesting",t.ProductReqTesting),this.setFieldValue("ctlISCustomer",$R_FN.getApprovedStatus(t.ISCustomer,t.ISSOApproved)),this.setFieldValue("ctlISSupplier",$R_FN.getApprovedStatus(t.ISSupplier,t.ISPOApproved)),this.setFieldValue("ctlhidCustomer",t.ISCustomer),this.setFieldValue("ctlhidSupplier",t.ISSupplier),this.setFieldValue("hidSupplierOnStop",t.OnSupStop),this.setFieldValue("ctlPremierCustomer",t.ISPremierCustomer),this.setFieldValue("ctlhidPremierCustomer",t.ISPremierCustomer),this.setFieldValue("ctlTier2PremierCustomer",t.Tier2PremierCustomer),this.setFieldValue("ctlhidTier2PremierCustomer",t.Tier2PremierCustomer),this.setFieldValue("ctlRebateAccount",t.IsRebateAccount),this.setFieldValue("ctlhidRebateAccount",t.IsRebateAccount),this._PremierCustomer=t.ISPremierCustomer,this._T2PremierCustomer=t.Tier2PremierCustomer,this._rebateAccount=t.IsRebateAccount,this.setFieldValue("ctlPurchasingNotes",$R_FN.getCleanTextValue(t.PurchasingNotes)),this.setFieldValue("ctlSanction",t.IsSanctioned),MainInfoCompanyPurchasingNotes=$R_FN.getCleanTextValue(t.PurchasingNotes),this._inactive=t.Inactive,$R_IBTN.showButton(this._ibtnActive,this._inactive),$R_IBTN.showButton(this._ibtnInactive,!this._inactive),$R_IBTN.enableButton(this._ibtnEdit,!this._inactive),$R_IBTN.enableButton(this._ibtnContacts,!this._inactive),$R_IBTN.enableButton(this._ibtnCreditLimit,!this._inactive),c=document.getElementById("ctl00_cphMain_ctlPageTitle_h3"),c.style.color=this._inactive?"grey":"black",l=document.getElementById("ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl12_pnlCreditLimitToolTip"),l.style.display=this._inactive?"none":"",u=document.getElementById("ctl00_cphMain_ctlCMPDocumentsNew_ctlDB_ctl13_pnlPDFDocuments").getElementsByClassName("pdfDocumentDelete"),u&&u.length>0)for(i=0;i<u.length;i++)u[i].style.display=this._inactive?"none":"";if(r=t.CreditLimitIds.split("|"),h="",!0)for(i=0;i<r.length;i++)r[i]!=""&&(h+="<a style='color: #cccccc;text-decoration: none;' href='javascript:void(0)'; onclick='$RGT_openCreditLimitWindow("+this._intCompanyID+","+r[i]+");'>"+this._intCompanyID+": CAF-"+r[i]+"<\/a>&nbsp;&nbsp;<a style='color: #cccccc;text-decoration: none;' href='javascript:void(0)'; onclick='$RGT_openCreditLimitLogWindow("+this._intCompanyID+","+r[i]+");'> Log<\/a><br>");console.log(h);$R_FN.setInnerHTML(this._pnlCreditLimit,"");$R_FN.setInnerHTML(this._pnlCreditLimit,h);$R_FN.showElement(this._pnlCreditLimit,r.length>0);this.getDataOK_End();setTimeout(function(){$("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_imgRefresh").trigger("click")},1e3);setTimeout(function(){$("#ctl00_cphMain_ctlGSA_ctlDB_imgRefresh").trigger("click")},1e3);t.OnStop==!0?$("#ctl00_cphMain_ctlPageTitle_ctl20_lblCompanyOnStop").removeClass("invisible"):$("#ctl00_cphMain_ctlPageTitle_ctl20_lblCompanyOnStop").addClass("invisible");t.IsSanctioned==!0?$("#ctl00_cphMain_ctlPageTitle_ctl20_lblCompanyIsSanctioned").removeClass("invisible"):$("#ctl00_cphMain_ctlPageTitle_ctl20_lblCompanyIsSanctioned").addClass("invisible");document.getElementById("ctl00_cphMain_ctlCMPDocumentsNew_ctlDB_pnlLinks").style.pointerEvents=this._inactive?"none":"auto";document.getElementById("ctl00_cphMain_ctlCMPDocumentsNew_ctlDB_pnlLinks").style.opacity=this._inactive?.5:1},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},showSalesMan:function(n,t){var i="&nbsp;";return i+=n,i+String.format(" ({0} days)",t)},showActivePopup:function(){confirm("Are you sure you are going to activate the selected company.")&&(this._inactive=!1,this.activeCompany(!1))},showInactivePopup:function(){confirm("Are you sure you are going to inactivate the selected company. You will not be able to generate any Sales Order or Purchase Order for that company in the future.")&&(this._inactive=!0,this.activeCompany(!0))},activeCompany:function(n){this.getData_Start();var t=new Rebound.GlobalTrader.Site.Data;t.set_PathToData("controls/Nuggets/CompanyMainInfo");t.set_DataObject("CompanyMainInfo");t.set_DataAction("ActiveCompany");t.addParameter("id",this._intCompanyID);t.addParameter("Inactive",n);t.addDataOK(Function.createDelegate(this,this.activeCompanyComplete));t.addError(Function.createDelegate(this,this.activeCompanyError));t.addTimeout(Function.createDelegate(this,this.activeCompanyError));$R_DQ.addToQueue(t);$R_DQ.processQueue();t=null},activeCompanyError:function(n){this._strErrorMessage=n._errorMessage},activeCompanyComplete:function(n){if(n._result.Result==!0)this._inactive?alert("Inactive Successfully."):alert("Active Successfully."),this.onInactiveCompanyComplete();else{var t=this._inactive?"Inactive":"Active";alert(t+" Failed.")}},populateContactList:function(n){var r,i,t,u,f;if(this._tblContacts.clearTable(),n.Contacts)for(r="",i=0;i<n.Contacts.length;i++)t=n.Contacts[i],u=[t.Inactive==!0?t.Name:$RGT_nubButton_Contact(t.ID,t.Name,this._enmCompanyListType),$R_FN.setCleanTextValue(t.Tel),$R_FN.setCleanTextValue(t.EMail)],r=t.Inactive==!0?"RowColorInactive":"",f={Inactive:t.Inactive,Name:$R_FN.setCleanTextValue(t.Name),Tel:t.Tel},this._tblContacts.addRowRowColor(u,t.ID,t.ID==this._intContactID,f,null,null,t.Inactive,r),this._ibtnContacts&&i==0&&$R_IBTN.setHref(this._ibtnContacts,$RGT_gotoURL_Contact(t.ID,this._enmCompanyListType)),t=null,u=null;this._ibtnContacts&&$R_FN.showElement(this._ibtnContacts,n.Contacts.length>0)},showEditForm:function(){var n;this._frmEdit.setFieldValue("ctlName",this.getFieldValue("ctlCompanyName"));this._frmEdit.setFieldValue("ctlGroup",this.getFieldValue("hidParentCompanyID"),null,this.getFieldValue("hidParentCompany"));this._frmEdit.setFieldValue("ctlSalesperson",this.getFieldValue("hidSalespersonNo"));this._frmEdit.setFieldValue("ctlCompanyType",this.getFieldValue("hidCompanyTypeID"));this._frmEdit.setFieldValue("ctlCustomerGroupCode",this.getFieldValue("hidCustomerGroupCode"));this._frmEdit.setFieldValue("ctlTel",this.getFieldValue("ctlTel"));this._frmEdit.setFieldValue("ctlTel800",this.getFieldValue("ctlTel800"));this._frmEdit.setFieldValue("ctlFax",this.getFieldValue("ctlFax"));this._frmEdit.setFieldValue("ctlEmail",this.getFieldValue("ctlEmail"));this._frmEdit.setFieldValue("ctlURL",this.getFieldValue("ctlURL"));this._frmEdit.setFieldValue("ctlVATNumber",this.getFieldValue("ctlVATNumber"));this._frmEdit.setFieldValue("ctlCmpRegNo",this.getFieldValue("ctlCmpRegNo"));this._frmEdit.setFieldValue("ctlNotes",MainInfoCompanyNotes);this._frmEdit.setFieldValue("ctlImportantNotes",MainInfoCompanyImportantNotes);this._frmEdit.setFieldValue("ctlImportantNotes_Label",MainInfoCompanyImportantNotes);this._frmEdit.setFieldValue("ctlCompanyType_Label",this.getFieldValue("ctlCompanyType"));this._frmEdit.setFieldValue("ctlCertificateNotes",MainInfoCertificationNotes);this._frmEdit.setFieldValue("ctlqualityNotes",MainInfoCompanyQualityNotes);this._frmEdit.setFieldValue("ctlCertificateNotes_Label",MainInfoCertificationNotes);this._frmEdit.setFieldValue("ctlQualityNotes_Label",MainInfoCompanyQualityNotes);this._frmEdit.setFieldValue("ctlIsTraceability",this.getFieldValue("ctlIsTraceability"));this._frmEdit.setFieldValue("ctlEARIMember",this.getFieldValue("ctlEARIMember"));this._frmEdit.setFieldValue("ctlEARIReported",this.getFieldValue("ctlEARIReported"));this._frmEdit.setFieldValue("ctlISCustomer",Boolean.parse(this.getFieldValue("ctlhidCustomer")));this._frmEdit.setFieldValue("ctlISSupplier",Boolean.parse(this.getFieldValue("ctlhidSupplier")));this._frmEdit._blnCanEditAccountNotes=this._blnCanEditAccountNotes;this._frmEdit.setFieldValue("ctlReviewDate",this.getFieldValue("hidReviewDate"));this._frmEdit.setFieldValue("ctlReviewDate_Label",this.getFieldValue("hidReviewDate"));this._frmEdit.setFieldValue("ctlUPLiftPrice",this.getFieldValue("hidUPLiftPrice"));this._frmEdit.setFieldValue("ctlPreviousReviewDate",this.getFieldValue("hidPreviousReviewDate"));this._frmEdit.setLastReviewDate(this.getFieldValue("hidLastReviewDate"));this._frmEdit.setFieldValue("ctlSupplierWarranty",this.getFieldValue("hidSupplierWarranty"));this._frmEdit._intGlobalClientNo=this.getFieldValue("hidGlobalClientNo");this._frmEdit.setFieldValue("ctlEORINumber",this.getFieldValue("ctlEORINumber"));this._frmEdit.setFieldValue("ctlProductReqTesting",this.getFieldValue("ctlProductReqTesting"));this._frmEdit._PremierCustomer=this._PremierCustomer;this._frmEdit._T2PremierCustomer=this._T2PremierCustomer;this._frmEdit._rebateAccount=this._rebateAccount;this._frmEdit.setFieldValue("ctlSanctions",this.getFieldValue("ctlSanction"));this._frmEdit.setFieldValue("ctlAdvisoryNotes",this.getFieldValue("ctlAdvisoryNotes"));this._frmEdit._displayAdvisory=this._displayAdvisory;this.showForm(this._frmEdit,!0);this._frmEdit._ctlSelectIndustryType.clearData();var r=$R_FN.singleStringToArray(this.getFieldValue("hidIndustryTypeIDs")),t=$R_FN.singleStringToArray(this.getFieldValue("hidAllIndustryTypeNames")),i=$R_FN.singleStringToArray(this.getFieldValue("hidAllIndustryTypeIDs"));for(n=0;n<t.length;n++)this._frmEdit._ctlSelectIndustryType.addRow(Array.contains(r,i[n]),[$R_FN.setCleanTextValue(t[n])],i[n]);this._frmEdit._ctlSelectIndustryType.populateTables();this._frmEdit.showInnerContent(!0);this._frmEdit._blnCanEditCompanyType=this._blnCanEditCompanyType;this._frmEdit._blnCanEditPremierCustomer=this._blnCanEditPremierCustomer;this._frmEdit._blnCanEditTier2PremierCustomer=this._blnCanEditTier2PremierCustomer;this._frmEdit._blnCanEditisPurchasingNotes=this._blnCanEditisPurchasingNotes;this._frmEdit.setFieldValue("ctlPurchasingNotes",this.getFieldValue("ctlPurchasingNotes"));this._frmEdit.setFieldValue("ctlPurchasingNotes_Label",this.getFieldValue("ctlPurchasingNotes"));this._frmEdit.setFieldValue("ctlSanctions",this.getFieldValue("ctlSanction"));this._frmEdit.setFieldValue("ctlPurchasingNotes",MainInfoCompanyPurchasingNotes);this._frmEdit.setFieldValue("ctlPurchasingNotes_Label",MainInfoCompanyPurchasingNotes)},cancelEdit:function(){this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.showForm(this._frmEdit,!1);this.showContentLoading(!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData();this.onSaveEditComplete()},saveEditError:function(){this.showError(!0,this._frmEdit._strErrorMessage)},enableButtons:function(n){this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive&&n);this._ibtnContacts&&$R_IBTN.enableButton(this._ibtnContacts,!this._inactive&&n)},showTopIcons:function(){clearTimeout(this._intTimeout);$R_FN.showElement(this._pnlCreditLimitToolTip,!0);this._pnlCreditLimitToolTip.style.top="0px";this._pnlCreditLimitToolTip.style.left="0px";this.setToolTipLocation()},hideTopIcons:function(){clearTimeout(this._intTimeout);this._intTimeout=setTimeout(Function.createDelegate(this,this.finishHideTopIcons),100)},setToolTipLocation:function(){this._pnlCreditLimitToolTip&&(this._ibtnCreditLimit&&(this._pnlCreditLimitToolTip.style.top=String.format("{0}px",Sys.UI.DomElement.getBounds(this._ibtnCreditLimit).y-Sys.UI.DomElement.getBounds(this._pnlCreditLimitToolTip).y+15)),this._ibtnCreditLimit&&(this._pnlCreditLimitToolTip.style.left=String.format("{0}px",Sys.UI.DomElement.getBounds(this._ibtnCreditLimit).x-Sys.UI.DomElement.getBounds(this._pnlCreditLimitToolTip).x)))},OpenCreditLimit:function(){$R_FN.openCreditLimitWindow(this._intCompanyID,0)},finishHideTopIcons:function(){$R_FN.showElement(this._pnlCreditLimitToolTip,!1)},showGroupCodeName:function(){this._enmCompanyListType!==$R_ENUM$CompanyListType.Customers?$("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCustomerGroupCode").hide():$("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlCustomerGroupCode").show()}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);