﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208827]		An.TranTan			01-Oct-2024		CREATE			In DMCC: Convert buy price to DMCC currency
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_get_Top3BuyPrice_BOM_KUB]      
	@PartNo NVARCHAR(100) = NULL,
	@ClientID int = 0          
AS     
      
BEGIN   
SET NOCOUNT ON

DECLARE @ClientCurrencyCode NVARCHAR(5) = NULL
		,@Last6Months DATE = CAST(DATEADD(MONTH, - 6, GETDATE()) AS DATE)

CREATE TABLE #tempTop3BuyPrice
(
	POID INT,
	PONo INT,
	[Date] NVARCHAR(20),
	Price NVARCHAR(200),
	IsClientPrice BIT
);

SET @ClientID = CASE WHEN ISNULL(@ClientID, 0) = 0 THEN 114 ELSE @ClientID END;

IF(@ClientID = 114) --HUB side
BEGIN
	--get HUB base currency code
	SELECT 
		@ClientCurrencyCode = cr.CurrencyCode
	FROM tbClient c WITH (NOLOCK)
		JOIN tbCurrency cr WITH (NOLOCK) on c.CurrencyNo = cr.CurrencyId    
	WHERE ClientId = @ClientID;

	--get PO/IPO for PartNo within 6 months and convert price to HUB currency
	;with ctePurchaseOrders as(
		select 
			po.PurchaseOrderId AS POID,
			po.PurchaseOrderNumber AS PONo,
			CAST(FORMAT(pol.DeliveryDate, 'dd-MM-yyyy') AS NVARCHAR(40)) AS [Date],
			dbo.ufn_convert_to_HUB_currency(pol.Price, cr.CurrencyId, getdate()) AS Price,
			CAST(0 AS BIT) AS IsClientPrice 
		from tbPurchaseOrder po with(nolock)
		join tbPurchaseOrderLine pol with(nolock) on pol.PurchaseOrderNo = po.PurchaseOrderId
		join tbCurrency cr with(nolock) on cr.CurrencyId = po.CurrencyNo
		where pol.FullPart = @PartNo
			and pol.DeliveryDate >= @Last6Months
		union all
		select 
			ipo.InternalPurchaseOrderId AS POID,
			ipo.InternalPurchaseOrderNumber AS PONo,
			CAST(FORMAT(pol.DeliveryDate, 'dd-MM-yyyy') AS NVARCHAR(40)) AS [Date],
			dbo.ufn_convert_to_HUB_currency(ipol.Price, cr.CurrencyId, getdate()) AS Price,
			CAST(1 AS BIT) AS IsClientPrice
		from tbInternalPurchaseOrderLine ipol with(nolock)
		join tbInternalPurchaseOrder ipo with(nolock) ON ipol.InternalPurchaseOrderNo = ipo.InternalPurchaseOrderId
		join tbPurchaseOrderLine pol with(nolock) ON ipol.PurchaseOrderLineNo = pol.PurchaseOrderLineId
		join tbCurrency cr with(nolock) on cr.CurrencyId = ipo.CurrencyNo
		where pol.FullPart = @PartNo
			and pol.DeliveryDate >= @Last6Months
	)
	--select top 3
	INSERT INTO #tempTop3BuyPrice
	(
		POID,
		PONo,
		[Date],
		Price,
		IsClientPrice
	)
	select top 3 
		POID,
		PONo,
		[Date],
		(CAST(CONVERT(DECIMAL(16,5), Price) AS NVARCHAR(100)) + ' ' + @ClientCurrencyCode) AS Price,
		IsClientPrice
	from ctePurchaseOrders
	order by price asc
END
ELSE BEGIN	--client side, re-use existing cache data
	INSERT INTO #tempTop3BuyPrice
	(
		POID,
		PONo,
		[Date],
		Price,
		IsClientPrice
	)
	SELECT TOP 3 
		POID,
		PO AS PONo, 
		[Date],       
		Price,
		IsClientPrice        
	FROM tbKUBPoDetailsCache WITH (NOLOCK)         
	WHERE Part = @PartNo
		AND ClientNo = @ClientID
	ORDER BY POCacheId ASC	
END

--Final result
IF NOT EXISTS(SELECT TOP 1 1 FROM #tempTop3BuyPrice)
BEGIN
	SELECT 'No purchase found' AS POID,  
	'' AS PONo,  
	'' AS [Date],  
	'' AS Price ,
	0 AS IsClientPrice 
END
ELSE
BEGIN
	SELECT * FROM #tempTop3BuyPrice
END
DROP TABLE #tempTop3BuyPrice;

SET NOCOUNT OFF     
END 
GO
/*
exec usp_get_Top3BuyPrice_BOM_KUB
	@PartNo = '0503103', @ClientID = 0
*/

