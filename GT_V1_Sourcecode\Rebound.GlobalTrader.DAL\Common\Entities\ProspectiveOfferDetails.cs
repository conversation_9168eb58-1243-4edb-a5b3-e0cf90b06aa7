﻿using System;

namespace Rebound.GlobalTrader.DAL
{
    public class ProspectiveOfferLines
    {
        public int Id { get; set; }
        public int ProspectiveOfferLineId { get; set; }
        public string PartNo { get; set; }
        public string HUBRFQCustomer { get ; set; }
        public int QuantityOffered { get; set; }
        public double UploadedOfferPrice { get; set; }
        public double LowestOffer { get; set; }
        public double HighestOffer { get; set; }
        public string Currency { get; set; }
        public int CurrencyNo { get; set; }
        public double? IHSAvgPrice { get; set; }
        public double? LyticaAvgPrice { get; set; }
        public double? QuotePrice { get; set; }
        public int? QuoteQTY { get; set; }
        public double? SOPrice { get; set; }
        public int? SOQTY { get; set; }
        public string SOTaxable { get; set; }
        public DateTime? SODate { get; set; }
        public double? SOTaxRate { get; set; }
        public string Manufacturer { get; set; }
        public string FileShared { get; set; }
        public int IsFromGT { get; set; }
        public int TotalCount { get; set; }
        public int curpage { get; set; }
        public int GTReqCount{ get; set; }
        public bool IsLineTaxable
        {
            get
            {
                return (SOTaxable == "Y" || SOTaxable == "1");
            }
        }
        public double SOLineValue {  get; set; }
        public int CustomerReqId {  get; set; }
        public int BOMNo { get; set; }
        public string ReceivedDate {  get; set; }
        public string CompanyName { get; set; }
        public string CustomerPartNo { get; set; }
        public string CustomerPartyNo { get; set;}
        public string DateCode { get; set; }
        public int ProductNo { get; set; }
        public int PackageNo { get; set; }
        public int SupplierNo { get; set; }
        public int ManufacturerNo { get; set; }
        public int? RoHS { get; set; }
        public int ReqGlobalCurrencyNo { get; set; }
        public string Notes { get; set; }
        public string SentProspectiveOfferAt { get; set; }
        public double? NewOfferPriceFromProspective { get; set; }
        public string DateRelease { get; set; }
        public int ClientCurrencyNo { get; set; }
        public int CusReqCurrencyNo { get; set; }
        public double ProspectiveOfferPrice { get; set; }
        public bool IsProspectiveSent {  get; set; }
    }

    public class ProspectiveOfferLinesOffer: ProspectiveOfferLines
    {
        public string SourceFileName { get; set; }
        public string ImportRowCount { get; set; }
    }
}
