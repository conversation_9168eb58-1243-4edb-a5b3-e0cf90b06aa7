using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;
//Marker     Changed by      Date         Remarks
//[001]      Shashi Keshar  20/01/2016    added Insurance History in Detail Section
namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CompanyFinanceInfo : Base {

		#region Locals

		protected IconButton _ibtnLink;

        //[001] End Here
		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

        private Boolean _Status = false;
        public Boolean Status
        {
            get { return _Status; }
            set { _Status = value; }
        }

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CompanyFinanceInfo.CompanyFinanceInfo.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CompanyFinanceInfo");
			if (_intCompanyID == -1) _intCompanyID = _objQSManager.CompanyID;
			//SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
            EnableControl();
			_ibtnLink.Visible = _blnCanEdit;
			base.OnPreRender(e);
		}

		#endregion
        private void EnableControl()
        {
            _Status = SecurityManager.IsCompanyStatusStopEdit(Convert.ToInt32(SessionManager.LoginID));
           
        }

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyFinanceInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnLink", _ibtnLink.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
        }

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnLink = FindIconButton("ibtnEdit");
        }

	}
}
