/*   
===========================================================================================  
TASK        UPDATED BY       DATE          ACTION    DESCRIPTION  
[BUG-203241]    Phuc Hoang   14-Jun-2024  Create   [PROD Bug] KPI Sales/ Team and Division are not showing any GT Revenue figures  
===========================================================================================  
*/  
-- Create the new procedure  
CREATE OR ALTER PROCEDURE [dbo].[usp_InsertKPIRevenueDeatils_For_All_Client]   
AS  
BEGIN  
  
 DECLARE @currentYear INT, @clientId INT;  
 SET @currentYear = YEAR(GETDATE());  
  
 DECLARE curClientId CURSOR FOR  
 SELECT ClientId FROM tbClient WHERE inactive = 0;  
  
 OPEN curClientId;  
 FETCH NEXT FROM curClientId INTO @clientId;  
  
 WHILE @@FETCH_STATUS = 0  
 BEGIN  
  EXEC usp_InsertKPIRevenueDeatils @clientId, @currentYear;  
  FETCH NEXT FROM curClientId INTO @clientId;  
 END;  
  
 CLOSE curClientId;  
 DEALLOCATE curClientId;  
  
END  