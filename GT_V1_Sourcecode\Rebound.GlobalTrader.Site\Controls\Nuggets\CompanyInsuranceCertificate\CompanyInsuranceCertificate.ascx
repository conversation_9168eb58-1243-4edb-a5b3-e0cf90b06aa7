<%@ Control Language="C#" CodeBehind="CompanyInsuranceCertificate.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyInsuranceCertificate" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Lines" BoxType="Standard" >

	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" />
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IsInitiallyEnabled="false" />
	</Links>
	
	<Content>
	
		<ReboundUI:FlexiDataTable ID="tbl" runat="server" AllowSelection="true" PanelHeight="150" />
		
	</Content>
	
	<Forms>
		<ReboundForm:CompanyInsuranceCertificate_Add id="frmAdd" runat="server" />
		<ReboundForm:CompanyInsuranceCertificate_Edit id="frmEdit" runat="server" />
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
