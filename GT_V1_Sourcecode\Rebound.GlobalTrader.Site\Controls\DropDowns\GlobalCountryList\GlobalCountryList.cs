﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class GlobalCountryList : Base {

		protected bool _blnIncludeSelected = true;
		public bool IncludeSelected {
			get { return _blnIncludeSelected; }
			set { _blnIncludeSelected = value; }
		}

		protected override void OnLoad(EventArgs e) {
			SetDropDownType("GlobalCountryList");
			AddScriptReference("Controls.DropDowns.GlobalCountryList.GlobalCountryList");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCountryList", ClientID);
			_scScriptControlDescriptor.AddProperty("blnIncludeSelected", _blnIncludeSelected);
			base.OnLoad(e);
		}

	}
}