  
  
  
CREATE OR ALTER PROCEDURE [dbo].[usp_update_InternalPurchaseOrder]                     
--*****************************************************************************************                    
                
--*****************************************************************************************                    
@InternalPurchaseOrderId  int ,                    
@CurrencyNo    int ,                    
@ContactNo    int ,                    
@Buyer     int ,                    
@DivisionNo    int ,                    
@UpdatedBy    int    = Null ,    
@DivisionHeaderNo int =null,                   
@RowsAffected   int = NULL Output                    
                    
AS                    
BEGIN      
                    
 UPDATE dbo.tbInternalPurchaseOrder                    
 SET           
   CurrencyNo   = @CurrencyNo                     
 --, ContactNo   = @ContactNo                     
 , Buyer    = @Buyer                     
 , DivisionNo   = @DivisionNo                    
 , UpdatedBy   = @UpdatedBy                    
 , DLUP    = current_timestamp        
 , DivisionHeaderNo=@DivisionHeaderNo              
 WHERE InternalPurchaseOrderId  = @InternalPurchaseOrderId            
          
 SELECT @RowsAffected  = @@ROWCOUNT       
      
END      
      
    
  
  