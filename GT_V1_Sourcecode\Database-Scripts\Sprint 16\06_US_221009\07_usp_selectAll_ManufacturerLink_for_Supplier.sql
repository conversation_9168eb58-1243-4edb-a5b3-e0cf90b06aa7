﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-210037]  An.TranTan   23-Oct-2024	create    Get Manufacturer advisory notes  
[US-221009]  An.TranTan   29-Nov-2024	update    update logic get manufacturer notes 
===========================================================================================  
*/ 
CREATE OR ALTER   PROCEDURE [dbo].[usp_selectAll_ManufacturerLink_for_Supplier]  
@SupplierCompanyNo int
,@ClientID INT = 0
AS  
SELECT a.ManufacturerLinkId  
,  a.ManufacturerNo  
,  b.ManufacturerName  
,  a.SupplierCompanyNo   
,  c.CompanyName  As SupplierName   
,  a.ManufacturerRating  
,  a.SupplierRating  
,  a.UpdatedBy  
,  a.DLUP
, dbo.ufn_get_MfrNotes(b.ManufacturerId,@ClientID) AS AdvisoryNotes
FROM  dbo.tbManufacturerLink a  
JOIN dbo.tbManufacturer b  
 ON a.ManufacturerNo = b.ManufacturerId  
JOIN dbo.tbCompany c  
 ON a.SupplierCompanyNo = c.CompanyId  
WHERE a.SupplierCompanyNo = @SupplierCompanyNo  
ORDER BY SupplierName  
  
  
  
  
GO


