Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Military=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Military.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Military.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Military.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Military.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Military");this._objData.set_DataObject("Military");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Military)for(n=0;n<t.Military.length;n++)this.addOption(t.Military[n].Name,t.Military[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Military.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Military",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);