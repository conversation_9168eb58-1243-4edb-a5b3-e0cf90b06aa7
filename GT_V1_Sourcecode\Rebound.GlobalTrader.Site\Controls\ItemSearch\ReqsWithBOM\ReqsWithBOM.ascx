<%@ Control Language="C#" CodeBehind="ReqsWithBOM.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.ReqsWithBOM" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">

	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlReqNo" runat="server" ResourceTitle="RequirementNo" FilterField="ReqNoLo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" FilterField="Part"/>	
		<ReboundUI_FilterDataItemRow:TextBox id="ctlBOM" runat="server" ResourceTitle="IPOBOMName" FilterField="BOM"/>
		<ReboundUI_FilterDataItemRow:DropDown id="ctlViewLevel" runat="server" ResourceTitle="HUBRFQ" DropDownType="ViewLevel" DropDownAssembly="Rebound.GlobalTrader.Site"  FilterField="ViewLevel"/>
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedFrom" runat="server" ResourceTitle="DateReceivedFrom" FilterField="DateReceivedFrom"/>
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedTo" runat="server" ResourceTitle="DateReceivedTo" FilterField="DateReceivedTo"/>
		<ReboundUI_FilterDataItemRow:DropDown id="ctlClient" runat="server" ResourceTitle="Client" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site"  FilterField="Client"/>
	</FieldsRight>
	
</ReboundUI_ItemSearch:DesignBase>
