<%@ Control Language="C#" CodeBehind="PReqs.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlSalesOrderNo" runat="server" ResourceTitle="SalesOrderNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" />
	</FieldsRight>	
</ReboundUI_ItemSearch:DesignBase>
