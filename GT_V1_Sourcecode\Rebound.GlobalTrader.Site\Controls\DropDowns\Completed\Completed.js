Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Completed=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Completed");this._objData.set_DataObject("Completed");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Items)for(n=0;n<t.Items.length;n++)this.addOption(t.Items[n].Name,t.Items[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Completed.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Completed",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);