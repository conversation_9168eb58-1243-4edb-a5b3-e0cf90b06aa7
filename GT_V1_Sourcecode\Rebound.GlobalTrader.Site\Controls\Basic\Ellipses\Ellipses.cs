using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Ellipses runat=server></{0}:Ellipses>")]
	public class Ellipses : Label, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected HyperLink _hypValue;
		protected HyperLink _hyp;
		protected Label _lblLoading;
		protected Label _lblError;

		#endregion

		#region Properties

		private bool _blnShowParentheses = true;
		public bool ShowParentheses {
			get { return _blnShowParentheses; }
			set { _blnShowParentheses = value; }
		}

		private bool _blnShowParenthesesAfterResult = false;
		public bool ShowParenthesesAfterResult {
			get { return _blnShowParenthesesAfterResult; }
			set { _blnShowParenthesesAfterResult = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			_hypValue = ControlBuilders.CreateHyperLinkInsideParent(this, "ellipses", "javascript:void(0);");
			_hyp = ControlBuilders.CreateHyperLinkInsideParent(this, "ellipses", "javascript:void(0);");
			if (_blnShowParentheses) _hyp.Text += "(";
			_hyp.Text += Functions.GetGlobalResource("misc", "Ellipses");
			if (_blnShowParentheses) _hyp.Text += ")";
			_lblLoading = ControlBuilders.CreateLabelInsideParent(this, "ellipsesLoading");
			ControlBuilders.CreateLiteralInsideParent(_lblLoading, "&nbsp;&nbsp;&nbsp;");
			_lblError = ControlBuilders.CreateLabelInsideParent(this, "ellipsesError");
			ControlBuilders.CreateLiteralInsideParent(_lblError, Functions.GetGlobalResource("misc", "Error"));
			Functions.SetCSSVisibility(_hypValue, false);
			Functions.SetCSSVisibility(_lblLoading, false);
			Functions.SetCSSVisibility(_lblError, false);
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.Ellipses.Ellipses", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Ellipses", this.ClientID);
			descriptor.AddElementProperty("hypValue", _hypValue.ClientID);
			descriptor.AddElementProperty("hyp", _hyp.ClientID);
			descriptor.AddElementProperty("lblLoading", _lblLoading.ClientID);
			descriptor.AddElementProperty("lblError", _lblError.ClientID);
			descriptor.AddProperty("blnShowParentheses", _blnShowParentheses);
			descriptor.AddProperty("blnShowParenthesesAfterResult", _blnShowParenthesesAfterResult);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion

	}

}