<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Accounts" xml:space="preserve">
    <value>Konten</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Alle</value>
  </data>
  <data name="AllOrders" xml:space="preserve">
    <value>Alle Aufträge</value>
  </data>
  <data name="AllUnshipped" xml:space="preserve">
    <value>Alles Unshipped</value>
  </data>
  <data name="Anforderungen" xml:space="preserve">
    <value>Anforderungen</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Vorhanden</value>
  </data>
  <data name="AwaitingReceipt" xml:space="preserve">
    <value>Erwarten des Empfangs</value>
  </data>
  <data name="Broker" xml:space="preserve">
    <value>Vermittler</value>
  </data>
  <data name="Calls" xml:space="preserve">
    <value>Anrufe</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Geschlossen</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Gutschriften</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Gutschriften</value>
  </data>
  <data name="CRMAs" xml:space="preserve">
    <value>Kunde RMAs</value>
  </data>
  <data name="CustomerRMAs" xml:space="preserve">
    <value>Kunde RMAs</value>
  </data>
  <data name="DebitNotes" xml:space="preserve">
    <value>Schuldposten</value>
  </data>
  <data name="Debits" xml:space="preserve">
    <value>Schuldposten</value>
  </data>
  <data name="Detail" xml:space="preserve">
    <value>Detail</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Waren innen</value>
  </data>
  <data name="Gutschriften" xml:space="preserve">
    <value>Gutschriften</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Rechnungen</value>
  </data>
  <data name="Log" xml:space="preserve">
    <value>Maschinenbordbuch</value>
  </data>
  <data name="Main" xml:space="preserve">
    <value>Hauptsächlich</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>Kein</value>
  </data>
  <data name="Old" xml:space="preserve">
    <value>Alt</value>
  </data>
  <data name="Open" xml:space="preserve">
    <value>Geöffnet</value>
  </data>
  <data name="POs" xml:space="preserve">
    <value>POs</value>
  </data>
  <data name="Preisangaben" xml:space="preserve">
    <value>Preisangaben</value>
  </data>
  <data name="PurchaseOrders" xml:space="preserve">
    <value>POs</value>
  </data>
  <data name="Purchasing" xml:space="preserve">
    <value>Kauf</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Unter Quarantäne gestellt</value>
  </data>
  <data name="Quotations" xml:space="preserve">
    <value>Preisangaben</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Preisangaben</value>
  </data>
  <data name="ReadyToReceive" xml:space="preserve">
    <value>Bereiten Sie vor, um zu empfangen</value>
  </data>
  <data name="ReadyToShip" xml:space="preserve">
    <value>Ready to Ship</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Empfangen</value>
  </data>
  <data name="Rechnungen" xml:space="preserve">
    <value>Rechnungen</value>
  </data>
  <data name="RelatedByCRMALine" xml:space="preserve">
    <value>Durch CRMA Linie</value>
  </data>
  <data name="RelatedByPart" xml:space="preserve">
    <value>Durch Part</value>
  </data>
  <data name="RelatedByPOLine" xml:space="preserve">
    <value>Durch PO-Linie</value>
  </data>
  <data name="Reqs" xml:space="preserve">
    <value>Anforderungen</value>
  </data>
  <data name="Requirements" xml:space="preserve">
    <value>Anforderungen</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Verkäufe</value>
  </data>
  <data name="SalesOrders" xml:space="preserve">
    <value>SOs</value>
  </data>
  <data name="Schuldposten" xml:space="preserve">
    <value>Schuldposten</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="Shipped" xml:space="preserve">
    <value>Versendet</value>
  </data>
  <data name="SOs" xml:space="preserve">
    <value>SOs</value>
  </data>
  <data name="SRMAs" xml:space="preserve">
    <value>Lieferant RMAs</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Vorrat</value>
  </data>
  <data name="SupplierRMAs" xml:space="preserve">
    <value>Lieferant RMAs</value>
  </data>
  <data name="Today" xml:space="preserve">
    <value>Heute</value>
  </data>
  <data name="Uninspected" xml:space="preserve">
    <value>Nicht geprüft</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Lager</value>
  </data>
</root>