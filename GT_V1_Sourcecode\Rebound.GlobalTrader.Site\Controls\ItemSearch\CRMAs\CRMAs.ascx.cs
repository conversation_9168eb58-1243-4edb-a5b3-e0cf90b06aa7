using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
	public partial class CRMAs : Base {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			SetItemSearchType("CRMAs");
			AddScriptReference("Controls.ItemSearch.CRMAs.CRMAs.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.CRMAs", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("CRMA", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Company", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("CustomerRMADate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Authoriser", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Invoice", Unit.Empty, true));
			base.OnPreRender(e);
		}
	}
}