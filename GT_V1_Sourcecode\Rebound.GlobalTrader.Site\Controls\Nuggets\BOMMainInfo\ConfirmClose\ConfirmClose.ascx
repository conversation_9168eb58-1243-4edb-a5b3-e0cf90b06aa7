<%@ Control Language="C#" CodeBehind="ConfirmClose.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ConfirmClose" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ConfirmClose")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlConfirmClose" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
