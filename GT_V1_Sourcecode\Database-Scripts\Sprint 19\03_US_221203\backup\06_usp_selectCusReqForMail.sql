  
  
/*   
===============================================================================================================================================  
TASK        UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-208862]  Trung Pham   16-Jul-2024  Create   Manual Trigger: Refreshing Lytica API  
[US-215434]  Phuc Hoang   06-Nov-2024  Update   Lytica Price should apply fuzzy logic for inserting & displaying  
===============================================================================================================================================  
*/  
  
CREATE  OR ALTER PROCEDURE [dbo].[usp_selectCusReqForMail]          
@BOMNo int ,                                        
@ClientID int                                                  
AS              
BEGIN           
SELECT           
 cr.CustomerRequirementNumber,          
 cr.CustomerRequirementId,          
 cr.Quantity,          
 cr.CustomerPart,          
 cr.Part,          
 cr.DateCode,          
 cr.ManufacturerCode,          
 cr.PackageName,          
 cr.ProductName,          
 cr.CompanyName,          
 cr.ClientName,          
 cr.DatePromised,          
 dbo.ufn_convert_currency_value(cr.Price, cr.CurrencyNo, cr.BOMCurrencyNo, cr.BOMDate) AS ConvertedTargetValue,          
 cr.CurrencyNo,          
 cu.CurrencyCode as BOMCurrencyCode ,          
 cr.SalesmanName,          
 case when cr.Obsolete=1 then 'Obsolete : Yes <br/>'  +ISNULL(cr.Instructions,'') else  'Obsolete : No <br/>' + ISNULL(cr.Instructions,'') end as Instructions,          
 cr.MSL,          
 cr.FactorySealed,          
 TRD.ServiceName AS ReqTypeText,      
 TRDT.ServiceName AS ReqForTraceability,  
 cr.AlternativesAccepted,  
 cr.RepeatBusiness  ,       
 cr.AlternateStatus  ,  
 cr.Alternate,  
 ISNULL(cr.LyticaAveragePrice, ly.AveragePrice) AS LyticaAveragePrice,  
 ISNULL(cr.LyticaTargetPrice, ly.TargetPrice) AS LyticaTargetPrice,  
 ISNULL(cr.LyticaMarketLeading, ly.MarketLeading) AS LyticaMarketLeading  
  
 FROM  dbo.vwCustomerRequirement cr           
 LEFT JOIN DBO.[tbRequirementDropDownData] TRD ON cr.ReqType=TRD.Id         
 LEFT JOIN DBO.[tbRequirementDropDownData] TRDT ON cr.ReqForTraceability=TRDT.Id        
 LEFT JOIN dbo.tbCurrency cu  ON cr.CurrencyNo = cu.CurrencyId  
 --LEFT JOIN tbLyticaAPI ly ON (ly.Manufacturer = cr.ManufacturerName AND ly.OriginalPartSearched = cr.Part)  
 OUTER APPLY (  
  SELECT  TOP 1 *  
   FROM tbLyticaAPI  
   WHERE OriginalPartSearched = cr.Part   
   AND ISNULL(Inactive, 0) = 0  
   AND (ISNULL(AveragePrice, 0) + ISNULL(TargetPrice, 0) + ISNULL(MarketLeading, 0)) > 0  
   AND (  
    Manufacturer = ISNULL(cr.ManufacturerName, '')   
    OR Manufacturer LIKE ISNULL(cr.ManufacturerName, '')  + '%'  
    OR cr.ManufacturerName LIKE ISNULL(Manufacturer, '') + '%'   
    OR Manufacturer LIKE ISNULL([dbo].[ufn_GetFirstWord](cr.ManufacturerName), '') + '%'  
   )  
 ) ly  
   
 WHERE BOMNo = @BOMNo   
--and cr.ClientNo= @ClientID     
-- AND (@ClientID IS NULL          
--                                 OR (NOT @ClientID IS NULL          
--                                     AND ClientNo = @ClientID))     
end    
  