using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class ToDoSnoozeTime : Base {

		protected override void OnInit(EventArgs e) {
			CanAddTo = false;
			CanRefresh = false;
			base.OnInit(e);
		}

		protected override void Render(HtmlTextWriter writer) {
			GetData();
			base.Render(writer);
		}

		internal override void GetData() {
			ClearDropDown();
			if (IncludeNoValue) AddNoValueToDropDown();
			AddToDropDown("misc", ToDoSnoozeTimeList.Minutes_5);
			AddToDropDown("misc", ToDoSnoozeTimeList.Minutes_10);
			AddToDropDown("misc", ToDoSnoozeTimeList.Minutes_15);
			AddToDropDown("misc", ToDoSnoozeTimeList.Minutes_30);
			AddToDropDown("misc", ToDoSnoozeTimeList.Hours_1);
			AddToDropDown("misc", ToDoSnoozeTimeList.Hours_2);
			AddToDropDown("misc", ToDoSnoozeTimeList.Hours_4);
			AddToDropDown("misc", ToDoSnoozeTimeList.Hours_8);
			AddToDropDown("misc", ToDoSnoozeTimeList.Days_1);
			AddToDropDown("misc", ToDoSnoozeTimeList.Days_2);
			AddToDropDown("misc", ToDoSnoozeTimeList.Days_3);
			AddToDropDown("misc", ToDoSnoozeTimeList.Days_4);
			AddToDropDown("misc", ToDoSnoozeTimeList.Weeks_1);
			AddToDropDown("misc", ToDoSnoozeTimeList.Weeks_2);
			base.GetData();
		}

		enum ToDoSnoozeTimeList {
			Minutes_5 = 5,
			Minutes_10 = 10,
			Minutes_15 = 15,
			Minutes_30 = 30,
			Hours_1 = 60,
			Hours_2 = 120,
			Hours_4 = 240,
			Hours_8 = 480,
			Days_1 = 1440,
			Days_2 = 2880,
			Days_3 = 4320,
			Days_4 = 5760,
			Weeks_1 = 10080,
			Weeks_2 = 20160
		}

	}
}