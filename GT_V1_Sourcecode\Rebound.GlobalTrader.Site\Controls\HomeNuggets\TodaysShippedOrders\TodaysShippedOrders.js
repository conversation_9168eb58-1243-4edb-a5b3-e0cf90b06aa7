Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.prototype={get_pnlShipped:function(){return this._pnlShipped},set_pnlShipped:function(n){this._pnlShipped!==n&&(this._pnlShipped=n)},get_tblShipped:function(){return this._tblShipped},set_tblShipped:function(n){this._tblShipped!==n&&(this._tblShipped=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblShipped&&this._tblShipped.dispose(),this._pnlShipped=null,this._tblShipped=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlShipped,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/TodaysShippedOrders");n.set_DataObject("TodaysShippedOrders");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,r,t,u;for(this.showNoneFoundOrContent(i.Count),this._tblShipped.clearTable(),r=0;r<i.Shipped.length;r++)t=i.Shipped[r],u=[$RGT_nubButton_SalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblShipped.addRow(u,null);$R_FN.showElement(this._pnlShipped,i.Shipped.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);