Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.initializeBase(this,[n]);this._intSupplierID=-1;this._intManufacturerLinkID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.prototype={get_intSupplierID:function(){return this._intSupplierID},set_intSupplierID:function(n){this._intSupplierID!==n&&(this._intSupplierID=n)},get_intManufacturerLinkID:function(){return this._intManufacturerLinkID},set_intManufacturerLinkID:function(n){this._intManufacturerLinkID!==n&&(this._intManufacturerLinkID=n)},get_autManufacturers:function(){return this._autManufacturers},set_autManufacturers:function(n){this._autManufacturers!==n&&(this._autManufacturers=n)},get_strTitleEdit:function(){return this._strTitleEdit},set_strTitleEdit:function(n){this._strTitleEdit!==n&&(this._strTitleEdit=n)},get_strTitleAdd:function(){return this._strTitleAdd},set_strTitleAdd:function(n){this._strTitleAdd!==n&&(this._strTitleAdd=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addModeChanged(Function.createDelegate(this,this.changeTitleOnMode))},dispose:function(){this.isDisposed||(this._autManufacturers&&this._autManufacturers.dispose(),this._autManufacturers=null,this._intSupplierID=null,this._intManufacturerLinkID=null,this._strTitleEdit=null,this._strTitleAdd=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.callBaseMethod(this,"dispose"))},formShown:function(){var n,t;this._blnFirstTimeShown&&(n=Function.createDelegate(this,this.validateDuplicate),$R_IBTN.addClick(this._ibtnSave,n),$R_IBTN.addClick(this._ibtnSave_Footer,n),t=Function.createDelegate(this,this.cancelClicked),$R_IBTN.addClick(this._ibtnCancel,t),$R_IBTN.addClick(this._ibtnCancel_Footer,t));this.showField("ctlManufacturer",this._mode=="ADD");this.showField("ctlManufacturerSelected",this._mode=="EDIT")},saveClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyManufacturers");n.set_DataObject("CompanyManufacturers");this._mode=="ADD"?(n.set_DataAction("SaveAddNew"),n.addParameter("SupplierNo",this._intSupplierID),n.addParameter("ManufacturerNo",this._autManufacturers._varSelectedID),n.addParameter("SupplierRating",this.getFieldValue("ctlRating"))):(n.set_DataAction("SaveEdit"),n.addParameter("ID",this._intManufacturerLinkID),n.addParameter("SupplierRating",this.getFieldValue("ctlRating")));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){this._mode=="EDIT"&&n._result.Result==!0||this._mode=="ADD"&&n._result.NewID>0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){var n=!0;return this._mode=="ADD"&&!this._autManufacturers._varSelectedID>0&&(this.setFieldInError("ctlManufacturer",!0,$R_RES.RequiredFieldMissingMessage),$("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl14_frmAddEdit_ctlDB_ctlManufacturer_pnlMessages").show(),n=!1),n||this.showError(!0),n},cancelClicked:function(){this.onCancel()},changeTitleOnMode:function(){switch(this._mode){case"ADD":this.changeTitle(this._strTitleAdd);break;case"EDIT":this.changeTitle(this._strTitleEdit)}},validateDuplicate:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyManufacturers");n.set_DataObject("CompanyManufacturers");n.set_DataAction("validateDuplicate");n.addParameter("ManufacturerNo",this._autManufacturers._varSelectedID);n.addParameter("ID",this._intSupplierID);n.addDataOK(Function.createDelegate(this,this.validateDuplicateComplete));n.addError(Function.createDelegate(this,this.validateDuplicateError));n.addTimeout(Function.createDelegate(this,this.validateDuplicateError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},validateDuplicateError:function(){return!1},validateDuplicateComplete:function(n){return this._mode=="ADD"?n._result.Result?this.saveClicked():(this._strErrorMessage="Manufacturers already exists for the company.",this.showError(!0,this._strErrorMessage),$("#ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl14_frmAddEdit_ctlDB_ctlManufacturer_pnlMessages").hide()):this.saveClicked(),n._result.Result}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_AddEdit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);