<%@ Control Language="C#" CodeBehind="IHSCatalogue.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
      <Links>
        <ReboundUI:IconButton ID="ibtnExportCSV" runat="server" IconButtonMode="hyperlink" Style="margin-left:6px;" IconGroup="Nugget" IconTitleResource="ExportToExcel" />
    </Links>
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
                 <ReboundUI_FilterDataItemRow:TextBox ID="ctlManufacturer" runat="server" ResourceTitle="Manufacturer" FilterField="Manufacturer" />
                 <ReboundUI_FilterDataItemRow:DropDown id="ctlcountryOforigin" runat="server" DropDownType="GlobalCountryList" ResourceTitle="countryOforigin" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="countryOforigin" />
                 <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
		        
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:TextBox ID="ctlMSL" runat="server" ResourceTitle="MSL" FilterField="MSL" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlHtcCode" runat="server" ResourceTitle="HtcCode" FilterField="HtcCode" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlDescription" runat="server" ResourceTitle="Description" FilterField="Description" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
