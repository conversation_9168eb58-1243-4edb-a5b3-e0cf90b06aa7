﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Communications" xml:space="preserve">
    <value>Communications Home</value>
  </data>
  <data name="Communications_MailMessageGroups" xml:space="preserve">
    <value>Mail Message Groups</value>
  </data>
  <data name="Profile_MailMessages" xml:space="preserve">
    <value>Mail Messages</value>
  </data>
  <data name="Profile_ToDo" xml:space="preserve">
    <value>To Do List</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact Home</value>
  </data>
  <data name="Contact_CompanyAdd" xml:space="preserve">
    <value>Add New Company</value>
  </data>
  <data name="Contact_GroupCodeCompanyAdd" xml:space="preserve">
    <value>Add New Group Code</value>
  </data>
  <data name="Contact_CompanyBrowse" xml:space="preserve">
    <value>Browse Companies</value>
  </data>
  <data name="Contact_CompanyDetail" xml:space="preserve">
    <value>Company Detail</value>
  </data>
  <data name="Contact_ContactDetail" xml:space="preserve">
    <value>Contact Detail</value>
  </data>
  <data name="Contact_ManufacturerAdd" xml:space="preserve">
    <value>Add New Manufacturer</value>
  </data>
  <data name="Contact_ManufacturerBrowse" xml:space="preserve">
    <value>Browse Manufacturers</value>
  </data>
  <data name="Contact_ManufacturerDetail" xml:space="preserve">
    <value>Manufacturer Detail</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Orders Home</value>
  </data>
  <data name="Orders_CreditNoteAdd" xml:space="preserve">
    <value>Add New Credit Note</value>
  </data>
  <data name="Orders_CreditNoteBrowse" xml:space="preserve">
    <value>Browse Credit Notes</value>
  </data>
  <data name="Orders_CustomerRequirementAdd" xml:space="preserve">
    <value>Add New Requirement</value>
  </data>
  <data name="Orders_CustomerRequirementBrowse" xml:space="preserve">
    <value>Browse Customer Requirements</value>
  </data>
  <data name="Orders_CustomerRMAAdd" xml:space="preserve">
    <value>Add New Customer RMA</value>
  </data>
  <data name="Orders_CustomerRMABrowse" xml:space="preserve">
    <value>Browse Customer RMAs</value>
  </data>
  <data name="Orders_DebitNoteAdd" xml:space="preserve">
    <value>Add New Debit Note</value>
  </data>
  <data name="Orders_DebitNoteBrowse" xml:space="preserve">
    <value>Browse Debit Notes</value>
  </data>
  <data name="Orders_InvoiceAdd" xml:space="preserve">
    <value>Add New Invoice</value>
  </data>
  <data name="Orders_InvoiceBrowse" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="Orders_PurchaseOrderAdd" xml:space="preserve">
    <value>Add New Purchase Order</value>
  </data>
  <data name="Orders_PurchaseOrderBrowse" xml:space="preserve">
    <value>Browse Purchase Orders</value>
  </data>
  <data name="Orders_PurchaseRequisitionBrowse" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="Orders_QuoteAdd" xml:space="preserve">
    <value>Add New Quote</value>
  </data>
  <data name="Orders_QuoteBrowse" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="Orders_SalesOrderAdd" xml:space="preserve">
    <value>Add New Sales Order</value>
  </data>
  <data name="Orders_SalesOrderBrowse" xml:space="preserve">
    <value>Browse Sales Orders</value>
  </data>
  <data name="Orders_Sourcing" xml:space="preserve">
    <value>Sourcing</value>
  </data>
  <data name="Orders_SupplierRMAAdd" xml:space="preserve">
    <value>Add New Supplier RMA</value>
  </data>
  <data name="Orders_SupplierRMABrowse" xml:space="preserve">
    <value>Browse Supplier RMAs</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Reports Home</value>
  </data>
  <data name="Reports_ReportDetail" xml:space="preserve">
    <value>Report Detail</value>
  </data>
  <data name="Setup" xml:space="preserve">
    <value>Setup Home</value>
  </data>
  <data name="Setup_CompanyDetails_Country" xml:space="preserve">
    <value>Setup Company Settings - Countries</value>
  </data>
  <data name="Setup_CompanyDetails_Currency" xml:space="preserve">
    <value>Setup Company Settings - Currencies</value>
  </data>
  <data name="Setup_CompanyDetails_Division" xml:space="preserve">
    <value>Setup Company Settings - Divisions</value>
  </data>
  <data name="Setup_CompanyDetails_ClientInvoiceHeader" xml:space="preserve">
    <value>Setup Company Settings - Client Invoice Header</value>
  </data>
  <data name="Setup_CompanyDetails_Product" xml:space="preserve">
    <value>Setup Company Settings - Products</value>
  </data>
  <data name="Setup_CompanyDetails_SequenceNumber" xml:space="preserve">
    <value>Setup Company Settings - Sequence Numbers</value>
  </data>
  <data name="Setup_CompanyDetails_ShippingMethod" xml:space="preserve">
    <value>Setup Company Settings - Shipping Methods</value>
  </data>
  <data name="Setup_CompanyDetails_SourcingLinks" xml:space="preserve">
    <value>Setup Company Settings - Sourcing Links</value>
  </data>
  <data name="Setup_CompanyDetails_StockLogReason" xml:space="preserve">
    <value>Setup Company Settings - Stock Log Reasons</value>
  </data>
  <data name="Setup_CompanyDetails_Tax" xml:space="preserve">
    <value>Setup Company Settings - Taxes</value>
  </data>
  <data name="Setup_CompanyDetails_Team" xml:space="preserve">
    <value>Setup Company Settings - Teams</value>
  </data>
  <data name="Setup_CompanyDetails_Terms" xml:space="preserve">
    <value>Setup Company Settings - Terms</value>
  </data>
  <data name="Setup_CompanyDetails_User" xml:space="preserve">
    <value>Setup Company Settings - Users</value>
  </data>
  <data name="Setup_CompanyDetails_Warehouse" xml:space="preserve">
    <value>Setup Company Settings - Warehouses</value>
  </data>
  <data name="Setup_GlobalSettings_CommunicationLogType" xml:space="preserve">
    <value>Setup Global Settings - Communication Log Types</value>
  </data>
  <data name="Setup_GlobalSettings_CompanyType" xml:space="preserve">
    <value>Setup Global Settings - Company Types</value>
  </data>
  <data name="Setup_GlobalSettings_IndustryType" xml:space="preserve">
    <value>Setup Global Settings - Industry Types</value>
  </data>
  <data name="Setup_GlobalSettings_EntertainmentType" xml:space="preserve">
    <value>Setup Global Settings - Entertainment Type</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCountryList" xml:space="preserve">
    <value>Setup Global Settings - Master Country List</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCurrencyList" xml:space="preserve">
    <value>Setup Global Settings - Master Currency List</value>
  </data>
  <data name="Setup_GlobalSettings_Package" xml:space="preserve">
    <value>Setup Global Settings - Packages</value>
  </data>
  <data name="Setup_GlobalSettings_ProductType" xml:space="preserve">
    <value>Setup Global Settings - Product Types</value>
  </data>
  <data name="Setup_GlobalSettings_Reason" xml:space="preserve">
    <value>Setup Global Settings - Quote Close Reasons</value>
  </data>
  <data name="Setup_Personal_UserProfile" xml:space="preserve">
    <value>Setup Personal Settings - My Profile</value>
  </data>
  <data name="Setup_Security_Groups" xml:space="preserve">
    <value>Setup Security Settings - Security Groups</value>
  </data>
  <data name="Setup_Security_Users" xml:space="preserve">
    <value>Setup Security Settings - Security Users</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse Home</value>
  </data>
  <data name="Warehouse_GoodsInBrowse" xml:space="preserve">
    <value>Browse Goods In</value>
  </data>
  <data name="Warehouse_LotsAdd" xml:space="preserve">
    <value>Add New Lot</value>
  </data>
  <data name="Warehouse_LotsBrowse" xml:space="preserve">
    <value>Browse Lots</value>
  </data>
  <data name="Warehouse_LotsDetail" xml:space="preserve">
    <value>Lot</value>
  </data>
  <data name="Warehouse_ReceiveCustomerRMABrowse" xml:space="preserve">
    <value>Browse Receive Customer RMAs</value>
  </data>
  <data name="Warehouse_ReceiveCustomerRMADetail" xml:space="preserve">
    <value>Receive Customer RMA</value>
  </data>
  <data name="Warehouse_ReceivePurchaseOrderBrowse" xml:space="preserve">
    <value>Browse Receive Purchase Orders</value>
  </data>
  <data name="Warehouse_ReceivePurchaseOrderDetail" xml:space="preserve">
    <value>Receive Purchase Order</value>
  </data>
  <data name="Warehouse_ServicesAdd" xml:space="preserve">
    <value>Add New Service</value>
  </data>
  <data name="Warehouse_ServicesBrowse" xml:space="preserve">
    <value>Browse Services</value>
  </data>
  <data name="Warehouse_ServicesDetail" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="Warehouse_ShipSalesOrderBrowse" xml:space="preserve">
    <value>Browse Ship Sales Orders</value>
  </data>
  <data name="Warehouse_ShipSalesOrderDetail" xml:space="preserve">
    <value>Ship Sales Order</value>
  </data>
  <data name="Warehouse_ShipSupplierRMABrowse" xml:space="preserve">
    <value>Browse Ship Supplier RMAs</value>
  </data>
  <data name="Warehouse_ShipSupplierRMADetail" xml:space="preserve">
    <value>Ship Supplier RMA</value>
  </data>
  <data name="Warehouse_StockAdd" xml:space="preserve">
    <value>Add New Stock Item</value>
  </data>
  <data name="Warehouse_StockBrowse" xml:space="preserve">
    <value>Browse Stock</value>
  </data>
  <data name="Warehouse_StockDetail" xml:space="preserve">
    <value>Stock Item</value>
  </data>
  <data name="Orders_CreditNoteDetail" xml:space="preserve">
    <value>Credit Note Detail</value>
  </data>
  <data name="Orders_CustomerRequirementDetail" xml:space="preserve">
    <value>Customer Requirement Detail</value>
  </data>
  <data name="Orders_CustomerRMADetail" xml:space="preserve">
    <value>Customer RMA Detail</value>
  </data>
  <data name="Orders_DebitNoteDetail" xml:space="preserve">
    <value>Debit Note Detail</value>
  </data>
  <data name="Orders_InvoiceDetail" xml:space="preserve">
    <value>Invoice Detail</value>
  </data>
  <data name="Orders_PurchaseOrderDetail" xml:space="preserve">
    <value>Purchase Order Detail</value>
  </data>
  <data name="Orders_QuoteDetail" xml:space="preserve">
    <value>Quote Detail</value>
  </data>
  <data name="Orders_SalesOrderDetail" xml:space="preserve">
    <value>Sales Order Detail</value>
  </data>
  <data name="Orders_SupplierRMADetail" xml:space="preserve">
    <value>Supplier RMA Detail</value>
  </data>
  <data name="Warehouse_GoodsInAdd" xml:space="preserve">
    <value>Add New Goods In Note</value>
  </data>
  <data name="Warehouse_GoodsInDetail" xml:space="preserve">
    <value>Goods In Detail</value>
  </data>
  <data name="Setup_CompanyDetails_MailGroups" xml:space="preserve">
    <value>Setup Company Settings - Mail Groups</value>
  </data>
  <data name="Setup_CompanyDetails_PrintedDocuments" xml:space="preserve">
    <value>Setup Company Settings - Printed Documents</value>
  </data>
  <data name="Setup_GlobalSettings_CountingMethod" xml:space="preserve">
    <value>Setup Global Settings - Counting Methods</value>
  </data>
  <data name="Setup_CompanyDetails_ApplicationSettings" xml:space="preserve">
    <value>Setup Company Settings - Application Settings</value>
  </data>
  <data name="Setup_GlobalSettings_ApplicationSettings" xml:space="preserve">
    <value>Setup Global Settings - Application Settings</value>
  </data>
  <data name="Setup_GlobalSettings_Incoterm" xml:space="preserve">
    <value>Setup Global Settings - Incoterms</value>
  </data>
  <data name="Setup_GlobalSettings_AS6081" xml:space="preserve">
    <value>Setup Global Settings - AS6081</value>
  </data>
  <data name="Warehouse_SupplierInvoiceDetail" xml:space="preserve">
    <value>Supplier Invoice</value>
  </data>
  <data name="Setup_CompanySettings_Printer" xml:space="preserve">
    <value>Setup Company Settings - Printer</value>
  </data>
  <data name="Setup_CompanyDetails_Printer" xml:space="preserve">
    <value>Setup Company Settings - Printer</value>
  </data>
  <data name="Setup_CompanySettings_LocalCurrencies" xml:space="preserve">
    <value>Setup Company Settings - Local Currencies</value>
  </data>
  <data name="Setup_GlobalSettings_Certificate" xml:space="preserve">
    <value>Setup Global Settings - Certificate &amp; Certificate Category</value>
  </data>
  <data name="Setup_CompanyDetails_LabelPath" xml:space="preserve">
    <value>Setup Company Settings - Nice Label Path</value>
  </data>
  <data name="Orders_BOMAdd" xml:space="preserve">
    <value>Add New HUBRFQ</value>
  </data>
  <data name="Orders_BOMImport" xml:space="preserve">
    <value>BOM Import</value>
  </data>
  <data name="Orders_BOMBrowse" xml:space="preserve">
    <value>Browse HUBRFQ</value>
  </data>
  <data name="vxcv" xml:space="preserve">
    <value />
  </data>
  <data name="Orders_InternalPurchaseOrderBrowse" xml:space="preserve">
    <value>Internal Purchase Order</value>
  </data>
  <data name="Orders_InternalPurchaseOrderAdd" xml:space="preserve">
    <value>Add New Internal Purchase</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value>Add New Internal Purchase Order</value>
  </data>
  <data name="Orders_InternalPurchaseOrderDetail" xml:space="preserve">
    <value>Internal Purchase Order</value>
  </data>
  <data name="Orders_ClientInvoiceDetail" xml:space="preserve">
    <value>Client Invoice</value>
  </data>
  <data name="Setup_CompanySettings_RestrictedManufacture" xml:space="preserve">
    <value>Setup Company Settings - Restricted Manufacturer</value>
  </data>
  <data name="WHS_SHORTSHIPMENTDETAILS" xml:space="preserve">
    <value>Short Shipment</value>
  </data>
  <data name="Orders_OGELLinesExportBrowse" xml:space="preserve">
    <value>OGEL Lines</value>
  </data>
  <data name="Setup_GlobalSettings_PDFDocumentFileSize" xml:space="preserve">
    <value>Setup Global Settings - Document File Size</value>
  </data>
  <data name="Setup_GlobalSettings_PPVBOMQualification" xml:space="preserve">
    <value>Setup Global Settings - PPV/ BOM Qualification</value>
  </data>
  <data name="Utility_ProsOfferImport" xml:space="preserve">
    <value>Prospective Offers Import</value>
  </data>
  <data name="Setup_CompanySettings_OGELLicenses" xml:space="preserve">
    <value>Setup Company Settings - OGEL Licenses</value>
  </data>
  <data name="Orders_BOMDetail" xml:space="preserve">
    <value>HUBRFQ Detail</value>
  </data>
</root>