Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.initializeBase(this,[n]);this._intCustomerRequirementID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intCustomerRequirementID=null,Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)));this.getFieldDropDownData("ctlReason")},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("Close");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("Reason",this.getFieldValue("ctlReason"));n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_Close",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);