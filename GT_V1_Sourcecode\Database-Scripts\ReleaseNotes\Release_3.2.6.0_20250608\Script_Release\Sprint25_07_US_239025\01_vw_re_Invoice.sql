﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
=============================================================================================================================================
[US-239025]		    Phuc Hoang			09-Apr-2025		UPDATE			Utility - Xmatch to display Salesperson for Requirements and Invoices
=============================================================================================================================================
*/

CREATE OR ALTER VIEW [dbo].[vw_re_Invoice]    
AS    
SELECT lg.EmployeeName AS Sls,    
  ' ' AS SlsCode,    
  co.CompanyName AS CustName,    
  po.PurchaseOrderNumber AS PONumber,    
  lg2.EmployeeName AS Buyer,    
  inv.InvoiceNumber AS InvoiceNo,    
  inv.SalesOrderNumber AS SONumber,    
  inv.InvoiceDate,    
  ISNULL(ila.Quantity, 0) AS Quantity,    
  il.Part AS PartNumber,    
  ISNULL(ila.LandedCost, 0) AS [Unit Landed Cost],    
  ISNULL(ila.Quantity * ila.LandedCost, 0) AS TotalCost,    
  il.Price / ISNULL(    
   ( SELECT TOP (1) ExchangeRate    
    FROM dbo.tbCurrencyRate WITH (nolock)    
    WHERE (CurrencyNo = inv.CurrencyNo)    
    AND  (CONVERT(datetime, CONVERT(varchar, CurrencyDate, 112)) <= CONVERT(datetime, CONVERT(varchar, ISNULL(inv.InvoiceDate, CURRENT_TIMESTAMP), 112)))    
    ORDER BY CurrencyDate DESC), 1) AS [Unit Sale],    
  ila.Quantity * (il.Price / ISNULL(    
   ( SELECT TOP (1) ExchangeRate    
    FROM dbo.tbCurrencyRate AS tbCurrencyRate_2 WITH (nolock)    
    WHERE (CurrencyNo = inv.CurrencyNo)    
    AND  (CONVERT(datetime, CONVERT(varchar, CurrencyDate, 112)) <= CONVERT(datetime, CONVERT(varchar, ISNULL(inv.InvoiceDate, CURRENT_TIMESTAMP), 112)))    
    ORDER BY CurrencyDate DESC), 1)) AS SellTotal,    
  ila.Quantity * (il.Price / ISNULL(    
   ( SELECT TOP (1) ExchangeRate    
    FROM dbo.tbCurrencyRate AS tbCurrencyRate_1 WITH (nolock)    
    WHERE (CurrencyNo = inv.CurrencyNo)    
    AND  (CONVERT(datetime, CONVERT(varchar, CurrencyDate, 112)) <= CONVERT(datetime, CONVERT(varchar, ISNULL(inv.InvoiceDate, CURRENT_TIMESTAMP), 112)))    
    ORDER BY CurrencyDate DESC), 1)) - ISNULL(ila.Quantity * ila.LandedCost, 0) AS Profit,    
  CASE (ila.Quantity * il.Price)    
   WHEN 0 THEN 0    
   ELSE ((ila.Quantity * (il.Price / ISNULL(    
     ( SELECT TOP (1) ExchangeRate    
      FROM dbo.tbCurrencyRate WITH (nolock)    
      WHERE (CurrencyNo = inv.CurrencyNo)    
      AND  (CONVERT(datetime, CONVERT(varchar, CurrencyDate, 112)) <= CONVERT(datetime, CONVERT(varchar, isnull(inv.InvoiceDate, CURRENT_TIMESTAMP), 112)))    
      ORDER BY CurrencyDate DESC), 1)))    
     - (IsNull((ila.Quantity * ila.LandedCost), 0)    
      + IsNull(inv.ShippingCost, 0))) / (ila.Quantity * (il.Price / ISNULL(    
      ( SELECT TOP (1) ExchangeRate    
       FROM dbo.tbCurrencyRate WITH (nolock)    
       WHERE (CurrencyNo = inv.CurrencyNo)    
       AND  (CONVERT(datetime, CONVERT(varchar, CurrencyDate, 112)) <= CONVERT(datetime, CONVERT(varchar, isnull(inv.InvoiceDate, CURRENT_TIMESTAMP), 112)))    
       ORDER BY CurrencyDate DESC), 1))) * 100 END AS Gross,    
  inv.CompanyNo AS Customer,    
  po.CompanyNo AS Vendor,    
  il.InvoiceLineId AS InvoiceLine,    
  sup.CompanyName AS VendName,    
  ' ' AS Category,    
  mf.ManufacturerName AS MFG,    
  NULL AS ImportDate,    
  il.FullPart AS FullPN,    
  dbo.ufn_get_basepart(il.Part) AS BasePN,    
  inv.ClientNo,    
  pol.PurchaseOrderLineId,    
  ila.GoodsInLineNo,    
  dbo.tbClient.ClientName    
  ,mf.ManufacturerId 
  ,(select CurrencyCode+' - ' +CurrencyDescription from tbcurrency tbcurr where tbcurr.currencyid = inv.currencyno)  as InvoiceCurrencyNo
  , lgcom.EmployeeName AS CompanySalesperson
FROM dbo.tbClient INNER JOIN    
  dbo.tbInvoice AS inv WITH (nolock) ON dbo.tbClient.ClientId = inv.ClientNo RIGHT OUTER JOIN    
  dbo.tbInvoiceLineAllocation AS ila WITH (nolock) INNER JOIN    
  dbo.tbInvoiceLine AS il WITH (nolock) ON ila.InvoiceLineNo = il.InvoiceLineId ON inv.InvoiceId = il.InvoiceNo LEFT OUTER JOIN    
  dbo.tbCompany AS co WITH (nolock) ON inv.CompanyNo = co.CompanyId LEFT OUTER JOIN    
  dbo.tbPurchaseOrderLine AS pol WITH (nolock) ON ila.PurchaseOrderLineNo = pol.PurchaseOrderLineId LEFT OUTER JOIN    
  dbo.tbPurchaseOrder AS po WITH (nolock) ON pol.PurchaseOrderNo = po.PurchaseOrderId LEFT OUTER JOIN    
  dbo.tbCompany AS sup WITH (nolock) ON po.CompanyNo = sup.CompanyId LEFT OUTER JOIN    
  dbo.tbManufacturer AS mf WITH (nolock) ON il.ManufacturerNo = mf.ManufacturerId LEFT OUTER JOIN    
  dbo.tbLogin AS lg WITH (nolock) ON inv.Salesman = lg.LoginId LEFT OUTER JOIN    
  dbo.tbLogin AS lg2 WITH (nolock) ON po.Buyer = lg2.LoginId LEFT OUTER JOIN   
  dbo.tbLogin AS lgcom WITH (nolock) ON co.Salesman = lgcom.LoginId
WHERE (inv.SupplierRMANo IS NULL)    
GO

IF NOT EXISTS (SELECT * FROM sys.fn_listextendedproperty(N'MS_DiagramPane1' , N'SCHEMA',N'dbo', N'VIEW',N'vw_re_Invoice', NULL,NULL))
	EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[34] 4[27] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "ila"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 114
               Right = 258
            End
            DisplayFlags = 280
            TopColumn = 8
         End
         Begin Table = "il"
            Begin Extent = 
               Top = 6
               Left = 296
               Bottom = 114
               Right = 461
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "inv"
            Begin Extent = 
               Top = 114
               Left = 38
               Bottom = 222
               Right = 208
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "co"
            Begin Extent = 
               Top = 114
               Left = 246
               Bottom = 222
               Right = 478
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "pol"
            Begin Extent = 
               Top = 222
               Left = 38
               Bottom = 330
               Right = 219
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "po"
            Begin Extent = 
               Top = 222
               Left = 257
               Bottom = 330
               Right = 446
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "sup"
            Begin Extent = 
               Top = 330
               Left = 38
               Bottom = 438
               Right = 270
            End
            DisplayFlags = 280
            TopColumn = 0
    ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Invoice'
ELSE
BEGIN
	EXEC sys.sp_updateextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[34] 4[27] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "ila"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 114
               Right = 258
            End
            DisplayFlags = 280
            TopColumn = 8
         End
         Begin Table = "il"
            Begin Extent = 
               Top = 6
               Left = 296
               Bottom = 114
               Right = 461
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "inv"
            Begin Extent = 
               Top = 114
               Left = 38
               Bottom = 222
               Right = 208
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "co"
            Begin Extent = 
               Top = 114
               Left = 246
               Bottom = 222
               Right = 478
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "pol"
            Begin Extent = 
               Top = 222
               Left = 38
               Bottom = 330
               Right = 219
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "po"
            Begin Extent = 
               Top = 222
               Left = 257
               Bottom = 330
               Right = 446
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "sup"
            Begin Extent = 
               Top = 330
               Left = 38
               Bottom = 438
               Right = 270
            End
            DisplayFlags = 280
            TopColumn = 0
    ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Invoice'
END
GO

IF NOT EXISTS (SELECT * FROM sys.fn_listextendedproperty(N'MS_DiagramPane2' , N'SCHEMA',N'dbo', N'VIEW',N'vw_re_Invoice', NULL,NULL))
	EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane2', @value=N'     End
         Begin Table = "mf"
            Begin Extent = 
               Top = 438
               Left = 38
               Bottom = 546
               Right = 210
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "lg"
            Begin Extent = 
               Top = 438
               Left = 248
               Bottom = 546
               Right = 420
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "lg2"
            Begin Extent = 
               Top = 546
               Left = 38
               Bottom = 654
               Right = 210
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "tbClient"
            Begin Extent = 
               Top = 6
               Left = 499
               Bottom = 114
               Right = 710
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 27
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 4965
         Alias = 1335
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Invoice'
ELSE
BEGIN
	EXEC sys.sp_updateextendedproperty @name=N'MS_DiagramPane2', @value=N'     End
         Begin Table = "mf"
            Begin Extent = 
               Top = 438
               Left = 38
               Bottom = 546
               Right = 210
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "lg"
            Begin Extent = 
               Top = 438
               Left = 248
               Bottom = 546
               Right = 420
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "lg2"
            Begin Extent = 
               Top = 546
               Left = 38
               Bottom = 654
               Right = 210
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "tbClient"
            Begin Extent = 
               Top = 6
               Left = 499
               Bottom = 114
               Right = 710
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 27
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 4965
         Alias = 1335
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 1350
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Invoice'
END
GO

IF NOT EXISTS (SELECT * FROM sys.fn_listextendedproperty(N'MS_DiagramPaneCount' , N'SCHEMA',N'dbo', N'VIEW',N'vw_re_Invoice', NULL,NULL))
	EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=2 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Invoice'
ELSE
BEGIN
	EXEC sys.sp_updateextendedproperty @name=N'MS_DiagramPaneCount', @value=2 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Invoice'
END
GO


