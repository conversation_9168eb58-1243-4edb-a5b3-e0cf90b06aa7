SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('usp_selectAllBOMManager_SourcingResult_for_Items','P') IS NOT NULL
    DROP PROC [dbo].usp_selectAllBOMManager_SourcingResult_for_Items
GO


/*
-- ==========================================================================================
-- TASK      		UPDATED BY     		DATE         ACTION 			DESCRIPTION                                    
-- US-201580 		Phuc.HoangDinh     	08-05-2024   UPDATE				[RP-2998] Quote to customer showing buy price from offer
-- US-201579 		An.TranTan     		02-06-2024   UPDATE				Prefer delivery date from tbAutoSource than tbCustomerRequirement
-- ==========================================================================================
*/


CREATE Procedure [dbo].[usp_selectAllBOMManager_SourcingResult_for_Items]                                                                           
@BOMManagerID int                       
,@IsPoHub bit  = 0                       
,@ItemsFor int                      
,@ReqStatus int = null                      
AS                                                                                              
begin                                   
                      
/*                      
                      
To Get items for Release                      
@ItemsFor =1 ==> reqstatus=3, isnobid=0,                      
To get items for Recall Releas                      
@itemsfor = 2 ==> reqstatus =4 , isnobid=0                       
To Get Items for Nobid                      
@itemsfor = 3 ==> reqstatus = 3 , isnobid=0                      
To Get items for Recall NoBid                       
@itemsfor = 4 ==> reqstatus =4 , isnobid=1                      
                      
                      
*/                      
                                         
                      
        
create table #tempData         
(LineNumber int ,sourceid int, CustomerRequirementNo int ,CustomerRequirementNumber int,ReleaseNote varchar(500), Fullpart varchar(100),        
Part varchar(100) , OriginalPart varchar(100), Price float, CurrencyCode int, DeliveryDate datetime,         
BuyPrice float, BuyCurrencyCode varchar(100), BuyCurrencyNo int, Status1 bit, status2 bit,        
VendorName varchar(200),HeaderFlag bit   
,ManufacturerNo int, ProductNo int, Reason varchar(500)
)        
                                                                   
   declare @DynamicQuery nvarchar(max)                         
   set @DynamicQuery=                       
'Insert into #tempData  SELECT distinct  cr.Sequence ,tbas.SourceId,                                                                                           
cr.CustomerRequirementId as CustomerRequirementNo                    
,cr.CustomerRequirementNumber as CustomerRequirementNumber  
,cr.ReleaseNote as ReleaseNote
, tbas.FullPart                                                                                                        
, tbas.Part     
,cr.part as originalpart    
, tbas.Resale as Price                                  
, (select CurrencyCode from tbCurrency where CurrencyId = tbas.ClientCurrencyNo) as CurrencyCode                                  
, ISNULL(tbas.DeliveryDate, cr.DatePromised) as DeliveryDate                                        
, tbas.Cost as BuyPrice                                        
, crncy.CurrencyCode as BuyCurrencyCode                  
, tbas.currencyNo as BuyCurrencyNo                  
, convert(bit,1) as Status1                       
, convert(bit,1) as Status2            
, tbas.VendorName            
,convert(bit,0)         
,tbas.ManufacturerNo
,tbas.ProductNo
,(select Reason from tbAudit_BOMManager where BOMManagerNo = '+ convert(varchar(100),@BOMManagerID)+ ' AND AutoSourceNo = tbas.SourceId) as Reason
FROM tbCustomerRequirement cr                        
 JOIN   dbo.tbAutoSource tbas on tbas.BOMManagerNo = cr.BOMManagerNo  and tbas.CustomerRequirementId = cr.CustomerRequirementId                          
left join tbCurrency crncy on tbas.CurrencyNo = crncy.CurrencyId                      
where cr.BOMManagerNo = '+ convert(varchar(100),@BOMManagerID) +''+                      
case when @ItemsFor = 1  then +' and cr.reqstatus in (3,4) and isnull(cr.isnobid,0)=0 and isnull(tbas.isdeleted,0)=0 and isnull(tbas.IsReleased,0)=0 '                      
when @ItemsFor = 2 then +' and  cr.reqstatus = 4 and isnull(cr.isnobid,0)=0 and isnull(tbas.isdeleted,0)=0 and tbas.IsReleased = 1 '                      
when @ItemsFor = 3 then +'  and cr.reqstatus < 3 and isnull(cr.isnobid,0)=0 '                      
when @ItemsFor = 4 then +'  and cr.reqstatus < 4 and cr.isnobid=1 '                      
else '' end                       
--+'and cr.REQStatus ='+  convert(varchar(100),@ReqStatus)                      
+' ORDER BY cr.CustomerRequirementId  '                      
                      
print  @DynamicQuery                      
                      
exec (@DynamicQuery)                      
        
        
 --select distinct LineNumber, originalpart,convert(bit,1),deliveryDate,CustomerRequirementNo from #tempData     
 
 declare @MissingRequiredFeilds bit
 
 if exists(select * from #tempData where isnull(ManufacturerNo,'')='' or isnull(ProductNo,'')='')
 begin
 set @MissingRequiredFeilds = 1
 end

insert into #tempData (LineNumber,part,Fullpart,HeaderFlag, deliveryDate,CustomerRequirementNo,ReleaseNote)        
select distinct LineNumber,originalpart,originalpart,convert(bit,1),deliveryDate,CustomerRequirementNo,ReleaseNote from #tempData        
     
 --select * from #tempData order by LineNumber    
select *,@MissingRequiredFeilds as MissingRequiredFeilds
from #tempData order by LineNumber, originalpart,CustomerRequirementNo,HeaderFlag desc        
end          
      
  
   
GO


