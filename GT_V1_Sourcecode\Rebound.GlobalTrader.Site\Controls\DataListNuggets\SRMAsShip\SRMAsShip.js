Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.prototype={get_blnShowAllOrders:function(){return this._blnShowAllOrders},set_blnShowAllOrders:function(n){this._blnShowAllOrders!==n&&(this._blnShowAllOrders=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.updateFilterVisibility();this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/SRMAsShip";this._strDataObject="SRMAsShip";Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.getData()},dispose:function(){this.isDisposed||(this._blnShowAllOrders=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._blnShowAllOrders=this._intCurrentTab==1;this.getData()},setupDataCall:function(){var n="GetData";this._blnShowAllOrders&&(n+="_All");this._objData.set_DataAction(n);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=this._IsGlobalLogin?[$RGT_nubButton_ShipSRMA(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue(n.Quantity,n.QuantityAllocated),$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.Date),$RGT_nubButton_PurchaseOrder(n.PONo,n.PO),$R_FN.setCleanTextValue(n.ClientName)]:[$RGT_nubButton_ShipSRMA(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue(n.Quantity,n.QuantityAllocated),$RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.Date),$RGT_nubButton_PurchaseOrder(n.PONo,n.PO)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlClientName").show(this._IsGlobalLogin)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);