﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using Rebound.GlobalTrader.BLL;
using System.Web.Script.Serialization;
using System.Text;
using System.Data;
using System.Globalization;
using Rebound.GlobalTrader.Site.Areas.BOM.Models;

namespace Rebound.GlobalTrader.Site.Areas.BOM.Controllers
{
    public class BOMManagerController : Controller
    {
        public class StatusMessage
        {
            public string status { get; set; }
            public string Message { get; set; }
        }
        // GET: BOM/BOMManager
        public ActionResult Index()
        {


            #region Getting clients
            Int32? intMastLoginNo = SessionManager.MasterLoginNo;
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.Client> lst = BLL.Client.GeClientByMaster(intMastLoginNo);
            //DataTable dt = new DataTable();
            //dt.Columns.Add("ClientId", typeof(System.Int32));
            //dt.Columns.Add("ClientName", typeof(System.String));
            //dt.Columns.Add("ClientCode", typeof(System.String));
            //for (int i = 0; i < lst.Count; i++)
            //{
            //    DataRow row = dt.NewRow();
            //    row["ClientId"] = lst[i].ClientId;
            //    row["ClientName"] = lst[i].ClientName;
            //    row["ClientCode"] = lst[i].ClientCode;
            //    dt.Rows.Add(row);
            //}
            //return dt;
            #endregion

            ViewData["CustomerName"] = lst;

            return View();
        }

        //public ActionResult SearchByLogin(string BOMId, int clientId, string BOMFromDate, string BOMToDate, int SearchType, string CompanyNo)
        //{
        //    var JsonSearch = GetBOMSearch(BOMId, SessionManager.ClientID, BOMFromDate, BOMToDate, SearchType, CompanyNo);
        //    return JsonSearch;
        //}
        //[NonAction]
        public ContentResult GetBOMSearch(int pq_curPage, int pq_rPP)
        {

            string BOMId = Request.Params["BOMId"];
            int clientId = Convert.ToInt32(Request.Params["clientId"]);
            string BOMDate = Request.Params["BOMDate"];
            string SearchType = Request.Params["SearchType"];
            string BOMFromDate = Request.Params["FromDate"];
            string BOMToDate = Request.Params["ToDate"];
            string CompanyNo = Request.Params["CompanyNo"];
            //int bomid;
            StringBuilder sb = null;
            try
            {

                if (System.String.IsNullOrWhiteSpace(BOMId))
                    BOMId = null;


                DateTime? bomfromdate = null;
                if (System.String.IsNullOrWhiteSpace(BOMFromDate))
                    bomfromdate = null;
                else bomfromdate = Convert.ToDateTime(BOMFromDate);

                DateTime? bomtodate = null;
                if (System.String.IsNullOrWhiteSpace(BOMToDate))
                    bomtodate = null;
                else bomtodate = Convert.ToDateTime(BOMToDate);

                if (System.String.IsNullOrWhiteSpace(SearchType))
                    SearchType = null;
                if (System.String.IsNullOrWhiteSpace(CompanyNo))
                    CompanyNo = null;


                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                ViewLevelList enmViewLevel = (ViewLevelList)Convert.ToInt32(SearchType);

                int? TeamId = (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null;
                int? DivisionId = (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null;
                int? MyId = (enmViewLevel == ViewLevelList.My) ? (int?)SessionManager.LoginID : null;

                //List<BOMManagerContract> BOMList = BOMManagerContract.BOMSearch(bomid, clientId, bomfromdate, bomtodate, 0, true,SessionManager.ClientCurrencyCode,SessionManager.Culture, TeamId, DivisionId, MyId);
                List<BOMManagerContract> BOMList = BOMManagerContract.DataListNugget(SessionManager.ClientID, TeamId, DivisionId, MyId, null, null, pq_curPage - 1, pq_rPP, BOMId, null, null, null, null, null, null, null, null, bomfromdate, bomtodate, CompanyNo);

                foreach (BOMManagerContract bomcntrct in BOMList)
                {
                    if (SessionManager.IsPOHub == true)
                    {

                        bomcntrct.TotalValueStr = Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(bomcntrct.TotalBomManagerLinePrice, (int)SessionManager.ClientCurrencyID, (bomcntrct.DateRequestToPOHub.HasValue) ? bomcntrct.DateRequestToPOHub.Value : bomcntrct.DLUP), SessionManager.ClientCurrencyCode, 2);
                    }
                    else
                    {
                        bomcntrct.TotalValueStr = Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(bomcntrct.TotalBomManagerLinePrice, (int)bomcntrct.POCurrencyNo, (bomcntrct.DateRequestToPOHub.HasValue) ? bomcntrct.DateRequestToPOHub.Value : bomcntrct.DLUP), SessionManager.ClientCurrencyCode, 2);
                    }
                }


                int total_Records = BOMList.Count;
                int skip = (pq_rPP * (pq_curPage - 1));
                if (skip >= total_Records)
                {
                    pq_curPage = (int)Math.Ceiling(((double)total_Records) / pq_rPP);
                    skip = (pq_rPP * (pq_curPage - 1));
                }
                var orders = (from bom in BOMList
                              orderby bom.ClientBOMManagerId
                              select bom).Skip(skip).Take(pq_rPP);
                sb = new StringBuilder(@"{""totalRecords"":" + total_Records + @",""curPage"":" + pq_curPage + @",""data"":");
                JavaScriptSerializer js = new JavaScriptSerializer();

                String json = js.Serialize(orders);
                sb.Append(json);
                sb.Append("}");
                return this.Content(sb.ToString(), "text/text");
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetBOMSearch. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return this.Content(sb.ToString(), "text/text");
            }

        }

        public ActionResult Paging(string sidx, string sord, int page, int rows)
        {
            string BOMId = Request.Params["BOMId"];
            int clientId = Convert.ToInt32(Request.Params["clientId"]);
            string BOMDate = Request.Params["BOMDate"];
            string SearchType = Request.Params["SearchType"];
            string BOMFromDate = Request.Params["BOMFromDate"];
            string BOMToDate = Request.Params["BOMFromDate"];
            int bomid;
            if (System.String.IsNullOrWhiteSpace(BOMId))
                bomid = 0;
            else
                bomid = Convert.ToInt32(BOMId);

            DateTime? bomfromdate = null;
            if (System.String.IsNullOrWhiteSpace(BOMFromDate))
                bomfromdate = null;
            else bomfromdate = Convert.ToDateTime(BOMFromDate);
            DateTime? bomtodate = null;
            if (System.String.IsNullOrWhiteSpace(BOMToDate))
                bomtodate = null;
            else bomtodate = Convert.ToDateTime(BOMToDate);


            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            ViewLevelList enmViewLevel = (ViewLevelList)Convert.ToInt32(SearchType);

            int? TeamId = (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null;
            int? DivisionId = (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null;
            int? MyId = (enmViewLevel == ViewLevelList.My) ? (int?)SessionManager.LoginID : null;
            try
            {
                List<BOMManagerContract> Results = BOMManagerContract.BOMSearch(bomid, SessionManager.ClientID, bomfromdate, bomtodate, 0, true, SessionManager.ClientCurrencyCode, SessionManager.Culture, TeamId, DivisionId, MyId);

                int pageIndex = Convert.ToInt32(page) - 1;
                int pageSize = rows;
                //return this.Content("{total:"+ 0,"page:"+1,"records:"+0,"rows:"+ [] +""}", "text/json")
                int totalRecords = Results.Count();
                var totalPages = (int)Math.Ceiling((float)totalRecords / (float)rows);
                if (sord.ToUpper() == "DESC")
                {
                    Results = Results.OrderByDescending(s => s.BOMManagerId).ToList();
                    Results = Results.Skip(pageIndex * pageSize).Take(pageSize).ToList();
                }
                else
                {
                    Results = Results.OrderBy(s => s.BOMManagerId).ToList();
                    Results = Results.Skip(pageIndex * pageSize).Take(pageSize).ToList();
                }
                var jsonData = new
                {
                    total = totalPages,
                    page,
                    records = totalRecords,
                    rows = Results
                };
                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : Paging. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                var jsonData = new
                {
                    total = 0,
                    page,
                    records = 0,
                    rows = 0
                };
                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult BOMManagerOrder(int BOM)
        {
            ViewBag.IsPoHub = SessionManager.IsPOHub;
            return View();
        }
        [HttpPost]
        public ActionResult GetCurrencyList()
        {
            List<BLL.Currency> crlst = new List<Currency>();
            try
            {
                crlst = BLL.Currency.DropDownSellForClient(SessionManager.ClientID);
                return Json(crlst, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetCurrencyList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(crlst, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult GetCompanyNameList()
        {
            List<Contact> Cmplst = new List<Contact>();
            try
            {
                int? companyno = Convert.ToInt32(TempData["CompanyNo"].ToString());
                Cmplst = Contact.DropDownForCompany(companyno);
                return Json(Cmplst, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetCompanyNameList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(Cmplst, JsonRequestBehavior.AllowGet);
            }
        }
        public ActionResult GetEmployeeList()
        {
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");
            int intTeamNo = 0;
            int intDivisionNo = 0;
            int intExcludeLoginNo = 0;
            List<Login> Employeelst = new List<Login>();
            try
            {
                if (GetFormValue_Boolean("LimitToCurrentUsersTeam")) intTeamNo = (int)SessionManager.LoginTeamID;
                if (GetFormValue_Boolean("LimitToCurrentUsersDivision")) intDivisionNo = (int)SessionManager.LoginDivisionID;
                if (GetFormValue_Boolean("ExcludeCurrentUser")) intExcludeLoginNo = (int)SessionManager.LoginID;
                string strOptions = CacheManager.SerializeOptions(new object[] { (intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID, intTeamNo, intDivisionNo, intExcludeLoginNo });
                Employeelst = Login.DropDownForClient((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID, intTeamNo, intDivisionNo, intExcludeLoginNo);
                return Json(Employeelst, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetEmployeeList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(Employeelst, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetBomManagerById(int? BOMManagerId)
        {
            BOMManagerContract BOMList = new BOMManagerContract();
            try
            {
                BOMList = BOMManagerContract.Get(BOMManagerId);
                BOMList.CompanyAdvisoryNotes = Company.GetAdvisoryNotes(BOMList.CompanyNo ?? 0);
                TempData["CompanyNo"] = BOMList.CompanyNo;
                return Json(BOMList, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetBomManagerById. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(BOMList, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult SaveCommunicationNotesData(int BOMManagerID, string cusReqIds, string sendToGroup, string notes, string cCUserIds, int sendTo, string BOMManagerName, int companyNo, int contact2No, int requestToPOHubBy, int updateByPH)
        {
            try
            {
                bool success = SaveBOMExpediteNotes(BOMManagerID, cusReqIds, sendToGroup, notes, cCUserIds, sendTo, BOMManagerName, companyNo, contact2No, requestToPOHubBy, updateByPH);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : SaveCommunicationNotesData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetComunicationNotes(int BOMManagerID, int CustomerReqNo)
        {
            try
            {
                List<Audit> audits = Audit.GetListExpediteForBOM(BOMManagerID, SessionManager.ClientID ?? 0, CustomerReqNo);
                List<CommunicationNotesModel> nodeLst = new List<CommunicationNotesModel>();
                int line = 1;
                foreach (var audit in audits)
                {
                    nodeLst.Add(new CommunicationNotesModel
                    {
                        ID = audit.AuditId,
                        Note = audit.Note,
                        CCUserID = audit.CCUserID,
                        DateTimeNote = Functions.FormatDate(audit.DLUP, false, true),
                        EmployeeName = audit.EmployeeName,
                        NoteTo = audit.To,
                        ReqNos = audit.ReqNos,
                        SendToGroup = audit.SendToGroup,
                        Line = line
                    });
                    line++;
                }

                return Json(nodeLst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetExpediteHistory. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetGroupForCommunicationNotes()
        {
            List<CommunicationNoteGroupModel> list = new List<CommunicationNoteGroupModel>();
            try
            {
                if (SessionManager.ClientID == 101)
                {
                    list = new List<CommunicationNoteGroupModel>
                    {
                        new CommunicationNoteGroupModel()
                        {
                            Id = "Hub Sales",
                            Name = Functions.GetGlobalResource("HUBRFQSendToGrouping", "Hub Sales")
                        }
                    };
                }
                else
                {
                    list = new List<CommunicationNoteGroupModel>
                    {
                        new CommunicationNoteGroupModel()
                        {
                            Id = "Hub Only",
                            Name = Functions.GetGlobalResource("HUBRFQSendToGrouping", "Hub Only")
                        },
                        new CommunicationNoteGroupModel()
                        {
                            Id = "Hub Sales",
                            Name = Functions.GetGlobalResource("HUBRFQSendToGrouping", "Hub Sales")
                        }
                    };
                }

                return Json(list, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetCurrencyList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(list, JsonRequestBehavior.AllowGet);
            }
        }

        private bool SaveBOMExpediteNotes(int BOMManagerID, string cusReqIds, string sendToGroup, string notes, string cCUserIds, int sendTo, string BOMManagerName, int companyNo, int contact2No, int requestToPOHubBy, int updateByPH)
        {
            WebServices servic = new WebServices();
            string addrCC = string.Empty;
            string Subject = Functions.GetGlobalResource("Printing", "CommunicationNoteSubject");
            string SendToGroup = sendToGroup == "0" ? string.Empty : sendToGroup;
            //int CompanyNo = companyNo;
            //System.Int32 Contact2No = contact2No;
            servic.GetNameOfCCLoginIDHUBRFQ(cCUserIds, out addrCC);

            int expediteNoteId = CustomerRequirement.InsertBOMExpediteNote(
                 BOMManagerID,
                 notes,
                 SessionManager.LoginID,
                 cusReqIds,
                 sendTo,
                 addrCC,
                 SendToGroup
            );

            if (expediteNoteId > 0)
            {
                string FullPart = "";
                string ReqSalesman = "";
                string SendToMembersList = "";
                string SendToArrayList = "";

                List<CustomerRequirement> lst = CustomerRequirement.GetHUBRFQReqNos(cusReqIds, SessionManager.ClientID);
                foreach (CustomerRequirement objDetails in lst)
                {
                    FullPart = FullPart + "," + objDetails.FullPart;
                    if (SendToGroup == "Hub Sales")
                    {
                        ReqSalesman = ReqSalesman + "||" + Convert.ToString(objDetails.Salesman);
                    }
                }

                if (sendTo > 0)
                {
                    SendToArrayList = Convert.ToString(sendTo);
                }
                else
                {
                    List<SendToMemberList> list = new List<SendToMemberList>();
                    List<SendToMemberList> sglist = SendToMemberList.GetSecurityGroupList();

                    foreach (SendToMemberList objsg in sglist)
                    {
                        list = SendToMemberList.GetMemberList(Convert.ToInt32(objsg.SecurityGroupNo));
                    }

                    foreach (SendToMemberList objDetails in list)
                    {
                        SendToMembersList = SendToMembersList + "||" + Convert.ToString(objDetails.LoginNo);
                    }

                    SendToArrayList = SendToMembersList.StartsWith("||") ? SendToMembersList.Remove(0, 2) : SendToMembersList;

                    if (SendToGroup == "Hub Sales")
                    {
                        if (contact2No != 0)
                        {
                            ReqSalesman = Convert.ToString(contact2No) + ReqSalesman;
                        }
                        else
                        {
                            ReqSalesman = requestToPOHubBy > 0 ? Convert.ToString(requestToPOHubBy) + ReqSalesman : ReqSalesman.Remove(0, 2);
                        }
                    }
                }
                StringBuilder message = new StringBuilder();
                string hyperLink = string.Format("Ord_BOMManagerDetail.aspx?BOM={0}&Note={1}", BOMManagerID, expediteNoteId);
                string poref = string.Format("Reference BOM Manager Detail   : <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">{2}</a>", "javascript:void(0);", hyperLink, BOMManagerName);
                message.Append("Message By  : " + SessionManager.LoginFullName + "<br />");
                message.Append("Date & Time  : " + Functions.FormatDate(Functions.GetUKLocalTime()) + " " + Functions.FormatTime(Functions.GetUKLocalTime()) + "<br />");
                message.Append("Part Nos  : " + FullPart.Remove(0, 1) + "<br />");
                message.Append(poref + "<br /><br />");
                message.Append("Communication Note  : " + notes + "<br />");
                message.Append("<br /><br />Regards,<br />" + SessionManager.LoginFullName + "<br />");


                if (!string.IsNullOrEmpty(ReqSalesman))
                {
                    string ccIds = SessionManager.IsPOHub == true ? string.Empty : cCUserIds;

                    servic.NotifyMessageExpediteNoteHUBRFQ(ReqSalesman, Subject, Convert.ToString(message), false, ccIds, SendToGroup);
                }

                if (!string.IsNullOrEmpty(SendToArrayList))
                {
                    string ccIds = SendToGroup.Equals("Hub Sales") && SessionManager.IsPOHub == false ? string.Empty : cCUserIds;
                    servic.NotifyMessageExpediteNoteHUBRFQ(SendToArrayList, Subject, Convert.ToString(message), false, ccIds, SendToGroup);
                }

                message = null;
                servic = null;

                return true;
            }
            else
            {
                return false;
            }
        }


        private bool GetFormValue_Boolean(string strIndex)
        {
            CultureInfo ci = new CultureInfo(SessionManager.Culture);
            return Convert.ToBoolean(GetFormValue(strIndex, false), ci);
        }
        private int? GetFormValue_NullableInt(string strIndex)
        {
            return GetFormValue_NullableInt(strIndex, null);
        }
        private int? GetFormValue_NullableInt(string strIndex, object objValueIfNull)
        {
            object obj = GetFormValue(strIndex, objValueIfNull);
            if (obj == null)
            {
                return null;
            }
            else
            {
                CultureInfo ci = new CultureInfo(SessionManager.Culture);
                return (int?)Convert.ToInt32(obj, ci);
            }
        }
        private object GetFormValue(string strIndex, object objIfNull)
        {
            System.Web.HttpContext _context = System.Web.HttpContext.Current;


            object obj = objIfNull;
            if (_context.Request.Form[strIndex] != null) obj = Functions.FormatStringForDatabase(_context.Request.Form[strIndex]);
            return obj;
        }

        protected override JsonResult Json(object data, string contentType,Encoding contentEncoding, JsonRequestBehavior behavior)
        {
            return new JsonResult()
            {
                Data = data,
                ContentType = contentType,
                ContentEncoding = contentEncoding,
                JsonRequestBehavior = behavior,
                MaxJsonLength = Int32.MaxValue
            };
        }

        [HttpPost]
        public ActionResult GetBOMItem(int? BOMManagerId, int? ClientId)
        {
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            //StringBuilder sb = null;
            try
            {
                int? cid = SessionManager.ClientID;
                List<BOMManagerContract> BOMList = BOMManagerContract.GetBOMListForCustomerRequirement(BOMManagerId, ClientId,curPage,Rpp);
                //sb = new StringBuilder(@"{""totalRecords"":" + BOMList.Count + @",""curPage"":" + 1 + @",""data"":");
                //JavaScriptSerializer js = new JavaScriptSerializer();
                //String json = js.Serialize(BOMList);
                //sb.Append(json);
                //sb.Append("}");
                //return this.Content(sb.ToString(), "text/text");
                foreach(var item in BOMList)
                {
                    item.MfrAdvisoryNotes = Functions.ReplaceLineBreaks(item.MfrAdvisoryNotes, "&#10;");
                }
                BOMList[0].curpage = curPage;
                return Json(BOMList, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetBOMItem. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return this.Content(null, "text/text");
            }
        }
        [HttpPost]
        public ActionResult UpdateBOMManager(System.Int32? bomId, System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? currencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32? contact2Id, System.Int32? salespersonId)
        {
            Boolean resultstatus = false;
            try
            {
                updatedBy = SessionManager.LoginID;
                resultstatus = BOMManagerContract.UpdateBOMManager(bomId, clientNo, bomName, notes, bomCode, inactive, updatedBy, companyId, contactId, currencyNo, currentSupplier, quoteRequired, AS9120, contact2Id, salespersonId);
                return Json(resultstatus, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : UpdateBOMManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(resultstatus, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetBuyersList()
        {
            int intTeamNo = 0;
            int intDivisionNo = 0;
            int intExcludeLoginNo = 0;
            List<Login> lst = new List<Login>();
            try
            {
                string strOptions = CacheManager.SerializeOptions(new object[] { 114, "POHub" });

                lst = Login.DropDownForPurchaseHub(114, intTeamNo, intDivisionNo, intExcludeLoginNo);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetBuyersList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetLoginListForClient()
        {
            int intTeamNo = 0;
            int intDivisionNo = 0;
            int intExcludeLoginNo = 0;
            List<Login> lst = new List<Login>();
            try
            {
                lst = Login.DropDownForClient(SessionManager.ClientID, intTeamNo, intDivisionNo, intExcludeLoginNo);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetLoginListForClient. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetMailToList(string searchString)
        {
            List<BLL.Login> lst = null;
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo"); //SessionManager.GlobalClientNo;

            try
            {
                lst = BLL.Login.AutoSearchForMail((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID, SessionManager.LoginID, searchString);
                //lst = BLL.Login.AutoSearchForMail(114, SessionManager.LoginID, searchString);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetMailToList. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                lst = null;
            }

        }
        [HttpPost]
        public ActionResult SendtoPurchaseHub(int BOMId, string BOMName, string BOMCode, string BomCompanyName, int BomCompanyNo, int AssignUserNo, string aryRecipientLoginIDsCC, int Contact2No, string AssignedUserType)
        {
            JsonObject jsn = new JsonObject();
            String ValidateMessage = null;
            bool blnOK = false;
            try
            {

                //System.Int32 AssignUserNo = GetFormValue_Int("AssignUserNo");
                //if (AssignUserNo > 0)
                //{
                    blnOK = BOMManagerContract.UpdatePurchaseQuote(BOMId, SessionManager.LoginID, (int)BomManagerStatus.List.SendToHub, AssignUserNo, out ValidateMessage, AssignedUserType);
                    if (blnOK)
                    {
                        WebServices servic = new WebServices();
                        servic.NotifyPurchaseRequestBomManager(AssignUserNo.ToString(), (SessionManager.POHubMailGroupId ?? 0).ToString(), string.Format(Functions.GetGlobalResource("Messages", "PurchaseRequestBOMManager"), BOMName), BOMCode, BOMName, BOMId, BomCompanyName, BomCompanyNo, aryRecipientLoginIDsCC, AssignedUserType);
                    }
                //}
                return Json(blnOK, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : SendtoPurchaseHub. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(blnOK, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult UpdateBOMStatusToClosed(System.Int32 BOMId, System.Int32? updatedBy)
        {
            JsonObject jsn = new JsonObject();
            bool blnOK = false;
            int Salesman = 0;
            int UpdateByPH = 0;
            int Status = 0;
            int companyNo = 0;
            string BOMManagerCode = string.Empty;
            string BOMManagerName = string.Empty;
            string companyName = string.Empty;

            DataTable dt;
            try
            {
                dt = BOMManagerContract.UpdateBOMStatusToClosed(BOMId, SessionManager.LoginID, (int)BomManagerStatus.List.Closed);
                foreach (DataRow dr in dt.Rows)
                {
                    Salesman = dr["Salesman"].ToString() != "" && dr["Salesman"].ToString() != null ? Convert.ToInt32(dr["Salesman"].ToString()) : 0;
                    UpdateByPH = dr["UpdateByPH"].ToString() != "" && dr["UpdateByPH"].ToString() != null ? Convert.ToInt32(dr["UpdateByPH"].ToString()) : 0;
                    Status = dr["Status"].ToString() != "" && dr["Status"].ToString() != null ? Convert.ToInt32(dr["Status"].ToString()) : 0;
                    BOMManagerCode = dr["BOMManagerCode"].ToString();
                    BOMManagerName = dr["BOMManagerName"].ToString();
                    companyNo = dr["companyNo"].ToString() != "" && dr["companyNo"].ToString() != null ? Convert.ToInt32(dr["companyNo"].ToString()) : 0;
                    companyName = dr["companyName"].ToString();
                }

                if (Status == 7)
                //if (blnOK)
                {
                    WebServices servic = new WebServices();
                    servic.NotifyBomManagerCloseToSaleperson(Salesman.ToString(),
                        (SessionManager.POHubMailGroupId ?? 0).ToString(),
                        string.Format(Functions.GetGlobalResource("Messages", "CloseBOMManager"), BOMManagerName),
                        BOMId, BOMManagerCode, BOMManagerName, companyNo, companyName, false, 0);
                    servic.NotifyBomManagerCloseToHub(UpdateByPH.ToString(),
                        (SessionManager.POHubMailGroupId ?? 0).ToString(),
                        string.Format(Functions.GetGlobalResource("Messages", "CloseBOMManager"), BOMManagerName),
                        BOMManagerCode, BOMManagerName, BOMId, companyName, companyNo, "");
                }
                return Json(blnOK, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : UpdateBOMStatusToClosed. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(blnOK, JsonRequestBehavior.AllowGet);
            }
        }

        [HttpPost]
        public ActionResult GetBOMManagerItemDetails(int CustomerRequirementId)
        {
            CustomerRequirement cReq = new CustomerRequirement();
            try
            {
                cReq = CustomerRequirement.GetReqBOM(CustomerRequirementId, SessionManager.ClientID);
                return Json(cReq, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetBOMManagerItemDetails. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(cReq, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetCompanyNames(string searchString)
        {
            List<Company> compList = new List<Company>();
            try
            {
                compList = Company.AutoSearchSale(SessionManager.ClientID, searchString);
                return Json(compList, JsonRequestBehavior.AllowGet);
            }

            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetCompanyNames. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(compList, JsonRequestBehavior.AllowGet);
            }
        }

        public ActionResult Test()
        {
            return View();
        }

        public ActionResult GridPagingTest(int pq_curPage, int pq_rPP)
        {
            StringBuilder sb = null;
            try
            {
                List<BOMManagerContract> BOMList = BOMManagerContract.DataListNugget(SessionManager.ClientID, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null);
                int total_Records = BOMList.Count;
                int skip = (pq_rPP * (pq_curPage - 1));
                if (skip >= total_Records)
                {
                    pq_curPage = (int)Math.Ceiling(((double)total_Records) / pq_rPP);
                    skip = (pq_rPP * (pq_curPage - 1));
                }
                var orders = (from bom in BOMList
                              orderby bom.ClientBOMManagerId
                              select bom).Skip(skip).Take(pq_rPP);
                sb = new StringBuilder(@"{""totalRecords"":" + total_Records + @",""curPage"":" + pq_curPage + @",""data"":");
                JavaScriptSerializer js = new JavaScriptSerializer();
                String json = js.Serialize(orders);
                sb.Append(json);
                sb.Append("}");
                return this.Content(sb.ToString(), "text/text");
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetCompanyNames. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(sb.ToString(), JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetBOMManagerAutoSourcing(int BOMManagerId, int? CustomerReqID)
        {
            int curPage = 1;
            if (Request["pq_curpage"] != null)
            {
                curPage = Convert.ToInt32(Request["pq_curpage"].ToString());
            }
            int Rpp = 5;
            if (Request["pq_rpp"] != null)
            {
                Rpp = Convert.ToInt32(Request["pq_rpp"].ToString());
            }
            List<Rebound.GlobalTrader.DAL.AutoSourcing> sourcinglist = new List<Rebound.GlobalTrader.DAL.AutoSourcing>();
            try
            {

                sourcinglist = BOMManagerContract.GetBOMManagerAutoSourcing(BOMManagerId, Convert.ToBoolean(SessionManager.IsPOHub), CustomerReqID, curPage, Rpp);
                //foreach (var item in sourcinglist)
                //{
                //    item.BasePrice = Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(item.UnitPrice, Convert.ToInt32(item.ClientCurrencyNo), DateTime.Now), SessionManager.ClientCurrencyCode);
                //    item.BasePriceFigure = BLL.Currency.ConvertValueToBaseCurrency(item.UnitPrice, Convert.ToInt32(item.ClientCurrencyNo), DateTime.Now);
                //    item.UpliftPrice = (item.UnitPrice + (item.UnitPrice * item.UpliftPercentage / 100));
                //}
                sourcinglist[0].curpage = curPage;
                return Json(sourcinglist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetBOMManagerAutoSourcing. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(sourcinglist, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                sourcinglist = null;
                //return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult EditSourcingResultsSalesman(Double UnitPrice, string Notes, System.Int32 CRID)
        {
            JsonObject jsn = new JsonObject();
            bool blnOK = false;
            try
            {
                blnOK = BOMManagerContract.EditSourcingResultsSalesman(CRID, UnitPrice, Notes, SessionManager.LoginID);
                return Json(blnOK, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : EditSourcingResultsSalesman. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(blnOK, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult LoadQuoteGenerationDataBOMManager(int CustomerReqID, int BOMManagerNo)
        {
            Rebound.GlobalTrader.DAL.QuoteDetails quoteDetails = new Rebound.GlobalTrader.DAL.QuoteDetails();
            try
            {
                quoteDetails.DateQuoted = DateTime.Now;
                quoteDetails = BOMManagerContract.LoadQuoteGenerationDataBOMManager(CustomerReqID,BOMManagerNo);
                return Json(quoteDetails, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : LoadQuoteGenerationDataBOMManager. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(quoteDetails, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                quoteDetails = null;
                //return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult LoadContactsForCompany(int CompanyID)
        {
            List<Contact> lst = new List<Contact>();
            try
            {
                lst = Contact.DropDownForCompany(CompanyID);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : LoadContactsForCompany. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                lst = null;
            }
        }
        [HttpPost]
        public ActionResult GetCompanySalesInfo(int CompanyID)
        {
            Company company = new Company();
            try
            {
                company = Company.GetDefaultSalesInfo(CompanyID);
                return Json(company, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetCompanySalesInfo. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(company, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                company = null;
            }
        }
        [HttpPost]
        public ActionResult LoadDivisionHeader()
        {
            List<Division> divisions = new List<Division>();
            try
            {
                divisions = Division.DropDownForClient(SessionManager.ClientID);
                return Json(divisions, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : LoadDivisionHeader. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(divisions, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                divisions = null;
            }
        }
        [HttpPost]
        public ActionResult LoadQuoteTerms()
        {
            List<Terms> terms = new List<Terms>();
            try
            {
                terms = Terms.DropDownSellForClient(SessionManager.ClientID);
                return Json(terms, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : LoadQuoteTerms. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(terms, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                terms = null;
            }
        }
        [HttpPost]
        public ActionResult LoadQuoteIncoterm()
        {
            List<Incoterm> incoterms = new List<Incoterm>();
            try
            {
                incoterms = Incoterm.DropDown();
                return Json(incoterms, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : LoadQuoteIncoterm. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(incoterms, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                incoterms = null;
            }
        }
        [HttpPost]
        public ActionResult AddNewQuote(System.String CustomerNotes, System.String InternalNotes, System.Int32? companyNo, System.Int32? contactNo, System.DateTime? dateQuoted, System.Int32? currencyNo, System.Int32? salesman, System.Int32? termsNo, System.Int32? divisionNo, System.Double? freight, System.Int32? incotermNo, System.Boolean? SourceSupplyRequired, System.Boolean? isImportant, System.Int32? SupportTeamMemberNo, System.Int32? DivisionHeaderNo, System.Int32? CustomerRequirementID, System.String DocumentHeaderImageName, int sourceId, int BOMManagerID, System.Boolean MultiQuote = false, System.String CustomerRequirementIds = "")
        {
            int QuoteID = 0;
            try
            {
                QuoteID = Quote.Insert(SessionManager.ClientID, CustomerNotes, InternalNotes, companyNo, contactNo, dateQuoted, currencyNo, salesman, termsNo, divisionNo, freight, false, incotermNo, SessionManager.LoginID, SourceSupplyRequired, isImportant, SupportTeamMemberNo, DivisionHeaderNo, CustomerRequirementID, DocumentHeaderImageName,false);
                if (QuoteID > 0)
                {
                    var QuoteLineID = QuoteLine.InsertFromSourcingResult(sourceId, QuoteID, dateQuoted, MultiQuote,CustomerRequirementIds);
                }
                return Json(QuoteID, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : AddNewQuote. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(QuoteID, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                QuoteID = 0;
            }
        }
        [HttpPost]
        public ActionResult GetBOMManagerStatus(System.Int32 BOM)
        {
            string GetBOMManagerStatus = string.Empty;
            string BOMManagerName = string.Empty;
            try
            {

                DataTable dt = BOMManagerContract.GetBOMManagerStatus(BOM);

                BOMManagerName = dt.Rows[0][0].ToString();
                GetBOMManagerStatus = dt.Rows[0][1].ToString();
                var jsonData = new { BOMName = BOMManagerName
                                    ,BOMStatus = GetBOMManagerStatus
                                    ,Status = dt.Rows[0][2]
                                    ,BOMCode = dt.Rows[0][3].ToString()
                                    ,TotalItemsCount = dt.Rows[0][9]
                                    ,PriceUplifted = dt.Rows[0][10]
                                    ,ItemsQuotedCount = dt.Rows[0][12]
                                    ,UpliftExist = dt.Rows[0][12]
                                    ,SalesPerson = dt.Rows[0][13]
                };
                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetBOMManagerStatus. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(GetBOMManagerStatus, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                GetBOMManagerStatus = null;
                //return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult DeleteBOMManagerItem(int BOMManagerID, string CustReqIDs)
        {
            try
            {

                bool success = BOMManagerContract.DeleteBOMManagerItem(BOMManagerID, CustReqIDs, SessionManager.LoginID);

                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : DeleteBOMManagerItem. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(false, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }

        [HttpPost]
        public ActionResult LoadEditBOMData(int CustReqID)
        {
            try
            {

                CustomerRequirement cReq = CustomerRequirement.Get(CustReqID);
                if (cReq.RFQClosingDate == DateTime.MinValue)
                {
                    cReq.RFQClosingDate = null;
                }
                if (cReq.CustomerDecisionDate == DateTime.MinValue)
                {
                    cReq.CustomerDecisionDate = null;
                }
                return Json(cReq, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : LoadEditBOMData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult GetReqDropDownData(string ReqType)
        {
            try
            {

                List<BLL.Usage> lst = BLL.Usage.ReqDropDown(ReqType);

                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetReqDropDownData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult GetUsageDropDownData()
        {
            try
            {

                List<BLL.Usage> lst = BLL.Usage.DropDown();
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetUsageDropDownData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpGet]
        public ActionResult GetPackages(string search)
        {
            List<Package> lst = new List<Package>();

            try
            {
                lst = BLL.Package.AutoSearch(search, false);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetPackages. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("Something went Wrong", JsonRequestBehavior.AllowGet);
            }
            finally
            {
                lst = null;
            }

        }
        [HttpPost]
        public ActionResult SaveEditBOMItemData(int customerRequirementId, int RequirementforTraceability, int salesman, int quantity, System.Int32? Usage, int Type, System.String EAU, int manufacturerNo, string customerPart, Double TargetSellPrice, int currencyNo, System.Byte? rohs, string dateCode, int productNo, System.Int32? PackageNo, string MSL, DateTime DatePromised, System.DateTime? CustomerDecisionDate, System.DateTime? RFQClosingDate, System.Int32? QuoteValidityRequired, string BOMManagerName, System.String notes, System.String instructions, System.Int32? SupportTeamMemberNo, int BOMManagerID)
        {
            try
            {
                bool success = BOMManagerContract.SaveEditBOMItemData(customerRequirementId, RequirementforTraceability, salesman, quantity, Usage, Type, EAU, manufacturerNo, customerPart, TargetSellPrice, currencyNo, rohs, dateCode, productNo, PackageNo, MSL, DatePromised, CustomerDecisionDate, RFQClosingDate, QuoteValidityRequired, BOMManagerName, notes, instructions, SupportTeamMemberNo, BOMManagerID, SessionManager.LoginID);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : SaveEditbOMItemData. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult SaveUpliftAllPrice(int BOMManagerID, float UpliftPercentage)
        {
            try
            {
                bool success = BOMManagerContract.SaveUpliftAllPrice(BOMManagerID, UpliftPercentage, SessionManager.LoginID);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : SaveUpliftAllPrice. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult GetUpliftPercentageAll(System.Int32 BOM)
        {
            Double UpliftPercentage;
            try
            {

                DataTable dt = BOMManagerContract.GetUpliftPercentageAll(BOM);

                UpliftPercentage = Convert.ToDouble(dt.Rows[0][0]);
                var jsonData = new { UpliftPercentage = UpliftPercentage };
                return Json(jsonData, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetUpliftPercentageAll. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json("", JsonRequestBehavior.AllowGet);
            }
            finally
            {
                //UpliftPercentage = null;
                //return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult RemoveUpliftPriceAll(int BOMManagerID)
        {
            try
            {
                bool success = BOMManagerContract.RemoveUpliftPriceAll(BOMManagerID, SessionManager.LoginID);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : RemoveUpliftPriceAll. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(null, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult GetBOMManagerSourcingForUplift(int BOMManagerId, String CustomerRequirementIds = "")
        {
            List<Rebound.GlobalTrader.DAL.AutoSourcing> sourcinglist = new List<Rebound.GlobalTrader.DAL.AutoSourcing>();
            try
            {

                sourcinglist = BOMManagerContract.GetBOMManagerSourcingForUplift(BOMManagerId, CustomerRequirementIds);
                foreach (var item in sourcinglist)
                {
                    item.BasePrice = Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(item.UnitPrice, Convert.ToInt32(item.ClientCurrencyNo), DateTime.Now), SessionManager.ClientCurrencyCode);
                    item.BasePriceFigure =BLL.Currency.ConvertValueToBaseCurrency(item.UnitPrice, Convert.ToInt32(item.ClientCurrencyNo), DateTime.Now);
                    item.UpliftPrice = (item.UnitPrice + (item.UnitPrice * item.UpliftPercentage / 100));
                }
                return Json(sourcinglist, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetBOMManagerAutoSourcing. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(sourcinglist, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                sourcinglist = null;
                //return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult Save_PrimarySourcing(System.Int32 ClientId, System.Int32 SourcingResultId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy)
        {
            
            StatusMessage stts = new StatusMessage();
            try
            {

                DataTable dt = BOMManagerContract.Save_PrimarySourcing((int)SessionManager.ClientID, SourcingResultId, CustomerRequirementId, (int)SessionManager.LoginID);

                foreach (DataRow dr in dt.Rows)
                {

                    stts.status = dr["Status"].ToString();
                    stts.Message = dr["Message"].ToString();
                }
                if (stts.status != "Success")
                {
                    new Errorlog().LogMessage("Inside BOMManagerController class, Method name : Save_PrimarySourcing. Exception details:" + stts.Message);
                }
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                stts.status = "Fail";
                stts.Message = "Something went wrong.";
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : Save_PrimarySourcing. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                stts = null;
                //return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult ResetUpliftPriceAll(int BOMManagerID)
        {
            try
            {

                bool success = BOMManagerContract.ResetUpliftPriceAll(BOMManagerID, SessionManager.ClientID);
                return Json(success, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {

                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : ResetUpliftPriceAll. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(false, JsonRequestBehavior.AllowGet);
            }
            finally
            {
            }
        }
        [HttpPost]
        public ActionResult Reset_PrimarySourcing(System.Int32 ClientId, System.Int32 SourcingResultId, System.Int32 CustomerRequirementId, System.Int32 UpdatedBy)
        {

            StatusMessage stts = new StatusMessage();
            try
            {

                DataTable dt = BOMManagerContract.Reset_PrimarySourcing((int)SessionManager.ClientID, SourcingResultId, CustomerRequirementId, (int)SessionManager.LoginID);

                foreach (DataRow dr in dt.Rows)
                {

                    stts.status = dr["Status"].ToString();
                    stts.Message = dr["Message"].ToString();
                }
                if (stts.status != "Success")
                {
                    new Errorlog().LogMessage("Inside BOMManagerController class, Method name : Save_PrimarySourcing. Exception details:" + stts.Message);
                }
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                stts.status = "Fail";
                stts.Message = "Something went wrong.";
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : Save_PrimarySourcing. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                stts = null;
                //return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult AssignUserId(String Bommanagerids, System.Int32 AssignUserId)
        {

            StatusMessage stts = new StatusMessage();
            try
            {

                DataTable dt = BOMManagerContract.AssignUser(Bommanagerids,AssignUserId);

                foreach (DataRow dr in dt.Rows)
                {

                    stts.status = dr["Status"].ToString();
                    stts.Message = dr["Message"].ToString();
                }
                if (stts.status != "Success")
                {
                    new Errorlog().LogMessage("Inside BOMManagerController class, Method name : AssignUserId. Exception details:" + stts.Message);
                }
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                stts.status = "Fail";
                stts.Message = "Something went wrong.";
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : Save_PrimarySourcing. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(stts, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                stts = null;
                //return Json(offerlist, JsonRequestBehavior.AllowGet);
            }
        }
        [HttpPost]
        public ActionResult GetMailGroup(string searchString)
        {
            List<BLL.MailGroup> lst = null;
            try
            {
                lst = BLL.MailGroup.GetListForClient(114);
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMManagerController class, Method name : GetMailGroup. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return Json(lst, JsonRequestBehavior.AllowGet);
            }
            finally
            {
                lst = null;
            }

        }
    }

}