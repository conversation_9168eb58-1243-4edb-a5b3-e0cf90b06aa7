﻿/*
Marker     Changed by      Date         Remarks
[001]      anand gupta     28/11/2019   For Get Global Product Search
[002]      Anand <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
*/
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlProductProvider : ProductProvider
    {


        /// <summary>
        /// AutoSearch 
        /// Calls [usp_autosearch_Product]
        /// </summary>
        public override List<ProductDetails> AutoSearch(System.String nameSearch, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Product", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductId = GetReaderValue_Int32(reader, "ProductId", 0);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.Hazarders = GetReaderValue_NullableBoolean(reader, "IsHazarders", false);
                    obj.OriginalProductDescription = GetReaderValue_String(reader, "OriginalProductDescription", "");
                    //[002] code start add parameter OrderViaIPOonly
                    obj.OrderViaIPOonly = GetReaderValue_NullableBoolean(reader, "IsOrderViaIPOonly", false);
                    //[002] code end
                    obj.ProductMessage = GetReaderValue_String(reader, "ProductMessage", "");
                    obj.IsRestrictedProduct = GetReaderValue_NullableBoolean(reader, "IsRestrictedProduct", false);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
		/// Delete Product
		/// Calls [usp_delete_Product]
		/// </summary>
		public override bool Delete(System.Int32? productId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_Product", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = productId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// DropDownForClient 
        /// Calls [usp_dropdown_Product_for_Client]
        /// </summary>
        public override List<ProductDetails> DropDownForClient(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_Product_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductId = GetReaderValue_Int32(reader, "ProductId", 0);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Products", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_Product]
        /// </summary>
        public override Int32 Insert(System.String productName, System.String productDescription, System.Int32? clientNo, System.String dutyCode, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_Product", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = productName;
                cmd.Parameters.Add("@ProductDescription", SqlDbType.NVarChar).Value = productDescription;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@DutyCode", SqlDbType.NVarChar).Value = dutyCode;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ProductId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_GlobalProduct]
        /// </summary>
        ///  // Adding parameter GlobalProductNameNo to Add record with respective ProductNameId selected (Suhail )
        public override Int32 GlobalInsert(System.String productName, System.String productDescription, System.Int32? clientNo, System.String dutyCode, System.Int32? updatedBy, System.Int32? GlobalProductNameNo, System.Double? dutyRateValue, System.Boolean? hazarders, System.Boolean? orderviaipoonly, System.Boolean? IsRestrictedProduct)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_GlobalProduct", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                //cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = productName; //To hide display Product Name on Global Product Group tab
                cmd.Parameters.Add("@ProductDescription", SqlDbType.NVarChar).Value = productDescription;

                cmd.Parameters.Add("@DutyCode", SqlDbType.NVarChar).Value = dutyCode;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@GlobalProductNameNo", SqlDbType.Int).Value = GlobalProductNameNo;
                cmd.Parameters.Add("@DutyRateValue", SqlDbType.Int).Value = dutyRateValue;
                cmd.Parameters.Add("@IsHazarders", SqlDbType.Bit).Value = hazarders;
                //[002] code start add parameter OrderViaIPOonly
                cmd.Parameters.Add("@IsOrderViaIPOonly", SqlDbType.Bit).Value = orderviaipoonly;
                //[002] code end
                cmd.Parameters.Add("@IsRestrictedProduct", SqlDbType.Bit).Value = IsRestrictedProduct;
                cmd.Parameters.Add("@GlobalProductId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@GlobalProductId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Global Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// Get 
        /// Calls [usp_select_Product]
        /// </summary>
        public override ProductDetails Get(System.Int32? productId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_Product", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = productId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetProductFromReader(reader);
                    ProductDetails obj = new ProductDetails();
                    obj.ProductId = GetReaderValue_Int32(reader, "ProductId", 0);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.DutyCode = GetReaderValue_String(reader, "DutyCode", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetLandedCostCalculation 
        /// Calls [usp_select_Product_LandedCostCalculation]
        /// </summary>
        public override ProductDetails GetLandedCostCalculation(System.Int32? quantity, System.DateTime? datePoint, System.Int32? currencyNo, System.Double? cost, System.Double? shippingCost, System.Boolean? applyDuty, System.Int32? productNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_Product_LandedCostCalculation", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@DatePoint", SqlDbType.DateTime).Value = datePoint;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Cost", SqlDbType.Float).Value = cost;
                cmd.Parameters.Add("@ShippingCost", SqlDbType.Float).Value = shippingCost;
                cmd.Parameters.Add("@ApplyDuty", SqlDbType.Bit).Value = applyDuty;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetProductFromReader(reader);
                    ProductDetails obj = new ProductDetails();
                    obj.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForClient 
        /// Calls [usp_selectAll_Product_for_Client]
        /// </summary>
        public override List<ProductDetails> GetListForClient(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Product_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductId = GetReaderValue_Int32(reader, "ProductId", 0);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.DutyCode = GetReaderValue_String(reader, "DutyCode", "");
                    obj.CurrentDutyRate = GetReaderValue_NullableDouble(reader, "CurrentDutyRate", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Products", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// GetListForClient 
        /// Calls [usp_selectAll_Product_for_Client]
        /// </summary>
        public override List<ProductDetails> GetProductList(System.Int32? GlobalProductId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Product_for_Global", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GlobalProductId", SqlDbType.Int).Value = GlobalProductId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductId = GetReaderValue_Int32(reader, "ProductId", 0);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.DutyCode = GetReaderValue_String(reader, "DutyCode", "");
                    obj.CurrentDutyRate = GetReaderValue_NullableDouble(reader, "CurrentDutyRate", null);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.LastUpdatedDutyRateDate = GetReaderValue_DateTime(reader, "LastUpdatedDutyRateDate", DateTime.MinValue);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Products", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        /// <summary>
        /// GetListForClient 
        /// Calls [usp_selectAll_GlobalProduct]
        /// </summary>
        /// // Adding parameterProductNameId to get record on basis of selected ProductNameId from Global product name (Suhail )
        public override List<ProductDetails> GetListGlobalProduct(System.Int32? GlobalProductNameId, System.String GlobalProductName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GlobalProduct", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GlobalProductNameId", SqlDbType.Int).Value = GlobalProductNameId;
                cmd.Parameters.Add("@GlobalProductName", SqlDbType.NVarChar, 100).Value = GlobalProductName;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductId = GetReaderValue_Int32(reader, "GlobalProductId", 0);
                    //  obj.ProductName = GetReaderValue_String(reader, "GlobalProductName", ""); // To hide display Product Name on Global Product Group tab
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");

                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.DutyCode = GetReaderValue_String(reader, "DutyCode", "");
                    obj.CurrentDutyRate = GetReaderValue_NullableDouble(reader, "CurrentDutyRate", null);
                    obj.Hazarders = GetReaderValue_NullableBoolean(reader, "IsHazardous", false);
                    //[002] code start add parameter OrderViaIPOonly
                    obj.OrderViaIPOonly = GetReaderValue_NullableBoolean(reader, "IsOrderViaIPOonly", false);
                    //[002] code end
                    obj.IsRestrictedProduct = GetReaderValue_NullableBoolean(reader, "IsRestrictedProduct", false);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Products", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update Product
        /// Calls [usp_update_Product]
        /// </summary>
        public override bool Update(System.Int32? productId, System.String productName, System.String productDescription, System.Int32? clientNo, System.String dutyCode, System.Boolean? inactive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Product", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = productId;
                cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = productName;
                cmd.Parameters.Add("@ProductDescription", SqlDbType.NVarChar).Value = productDescription;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@DutyCode", SqlDbType.NVarChar).Value = dutyCode;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }




        /// <summary>
        /// Update Product
        /// Calls [usp_update_GlobalProduct]
        /// </summary>
        public override bool GlobalUpdate(System.Int32? productId, System.String productName, System.String productDescription, System.Int32? clientNo, System.String dutyCode, System.Boolean? inactive, System.Int32? updatedBy, System.Boolean? hazarders, System.Boolean? OrderViaIPOonly, System.Boolean? IsRestrictedProduct)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GlobalProduct", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GlobalProductId", SqlDbType.Int).Value = productId;
                //cmd.Parameters.Add("@GlobalProductName", SqlDbType.NVarChar).Value = productName; // To hide display Product Name on Global Product Group tab
                cmd.Parameters.Add("@ProductDescription", SqlDbType.NVarChar).Value = productDescription;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@DutyCode", SqlDbType.NVarChar).Value = dutyCode;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@IsHazarders", SqlDbType.Bit).Value = hazarders;
                //[002] code start add parameter OrderViaIPOonly
                cmd.Parameters.Add("@IsOrderViaIPOonly", SqlDbType.Bit).Value = OrderViaIPOonly;
                //[002] code end
                cmd.Parameters.Add("@IsRestrictedProduct", SqlDbType.Bit).Value = IsRestrictedProduct;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Global Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update Product
        /// Calls [usp_update_Product_Globally]
        /// </summary>
        public override bool UpdateProductGlobally(System.Int32? productId, System.Boolean? inactive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_Product_Globally", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = productId;

                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// AutoSearch by Client and Global Product
        /// Calls [usp_autosearch_Product_By_GlobalProduct_Client]
        /// </summary>
        public override List<ProductDetails> ProductByGlobalAndClient(System.String nameSearch, System.Int32? clientId, System.Int32? globalProductNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Product_By_GlobalProduct_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@GlobalProductNo", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductId = GetReaderValue_Int32(reader, "ProductId", 0);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForClient 
        /// Calls [usp_selectAll_GlobalProductName]
        /// </summary>
        //public override List<ProductDetails> GetListGlobalProductName()
        public override List<ProductDetails> GetListGlobalProductName(System.String productName, System.Int32? ProductNameId, System.String ProductType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GlobalProductName", cn);
                if (productName == "" || productName == null)
                    cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = DBNull.Value;
                else
                    cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = productName;

                cmd.Parameters.Add("@ProductNameId", SqlDbType.Int).Value = ProductNameId;
                if (ProductType == "" || ProductType == null)
                    cmd.Parameters.Add("@ProductType", SqlDbType.NVarChar).Value =  DBNull.Value;
                else
                    cmd.Parameters.Add("@ProductType", SqlDbType.NVarChar).Value = ProductType; ;
                
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.GlobalProductNameId = GetReaderValue_Int32(reader, "GlobalProductNameId", 0);
                    obj.GlobalProductName = GetReaderValue_String(reader, "GlobalProductName", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Products", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// calls usp_selectAll_GlobalProductCategoryName
        /// </summary>
        /// <param name="productName"></param>
        /// <param name="ProductNameId"></param>
        /// <param name="ProductType"></param>
        /// <returns></returns>
        public override List<ProductDetails> GetListGlobalProductCategoryName(System.String productName, System.Int32? ProductNameId, System.String ProductType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GlobalProductCategoryName", cn);
                cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = productName;
                cmd.Parameters.Add("@ProductNameId", SqlDbType.Int).Value = ProductNameId;
                cmd.Parameters.Add("@ProductType", SqlDbType.NVarChar).Value = ProductType;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductCategoryId = GetReaderValue_Int32(reader, "GlobalProductNameId", 0);
                    obj.CategoryName = GetReaderValue_String(reader, "GlobalProductName", "");
                    //obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Categories", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// To get all product with category not set
        /// </summary>
        /// <param name="productName"></param>
        /// <param name="ProductNameId"></param>
        /// <param name="ProductType"></param>
        /// <returns></returns>
        public override List<ProductDetails> GetAllProductWithNoCategoryName()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GetProductWithoutCategoryId", cn);
                //cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = productName;
                //cmd.Parameters.Add("@ProductNameId", SqlDbType.Int).Value = ProductNameId;
                //cmd.Parameters.Add("@ProductType", SqlDbType.NVarChar).Value = ProductType;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductCategoryId = GetReaderValue_Int32(reader, "GlobalProductNameId", 0);
                    obj.CategoryName = GetReaderValue_String(reader, "GlobalProductName", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Categories", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_GlobalProductCatgeoryName] 
        /// </summary>
        /// <param name="categoryName"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public override Int32 GlobalCategoryNameInsert(System.String categoryName, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_GlobalProductCatgeoryName", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@CategoryName", SqlDbType.NVarChar).Value = categoryName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@DLUP", SqlDbType.DateTime).Value = DateTime.Now;
                cmd.Parameters.Add("@ProductCategoryId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ProductCategoryId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Global Product Category", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_GlobalProductName]
        /// </summary>
        public override bool GlobalNameInsert(System.String productName, System.Int32? updatedBy, System.Int32 categoryId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_GlobalProductName", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = productName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@DLUP", SqlDbType.DateTime).Value = DateTime.Now;
                cmd.Parameters.Add("@CategoryId", SqlDbType.Int).Value = categoryId;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Global Product", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Product
        /// Calls [usp_update_GlobalProductName]
        /// </summary>
        public override bool GlobalUpdateName(System.Int32? productId, System.String productName, System.Boolean? inactive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GlobalProductName", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GlobalProductNameId", SqlDbType.Int).Value = productId;
                cmd.Parameters.Add("@GlobalProductName", SqlDbType.NVarChar).Value = productName;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Global Product Name", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Update Product
        /// Calls usp_update_GlobalCategoryName
        /// </summary>
        public override Int32 GlobalUpdateCategoryName(System.Int32? productId, System.String productName, System.Boolean? inactive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GlobalCategoryName", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GlobalProductNameId", SqlDbType.Int).Value = productId;
                cmd.Parameters.Add("@GlobalProductName", SqlDbType.NVarChar).Value = productName;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@RowsAffected"].Value;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Global Product Category Name", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[001]
        /// <summary>
        /// Auto Search Global Product  into Top menu
        /// Calls [usp_autosearch_Product_Search]
        /// </summary>
        public override DataTable GetListGlobalProductNameSearch(string productName, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Product_Search", cn);
                cmd.Parameters.Add("@ProductName", SqlDbType.NVarChar).Value = productName;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Products", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[001]


        //[002]
        /// <summary>
        /// Auto Search Global Product  into CrossMatch Screen
        /// Calls [usp_autosearch_Product_CrossMatch]
        /// </summary>
        public override DataTable GetProductListCrossMatch(string productName, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Product_CrossMatch", cn);
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = productName;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Products", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[002]

        /// Calls [usp_GetProductMessage]
		/// </summary>
		public override ProductDetails GetProductStatusMessage(System.Int32? ProductNo, System.Boolean IsProdHaz, System.Boolean IsOrderViaIPOonly, System.Int32? ClientNo, System.Boolean? IsRestrictedProduct)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetProductMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = ProductNo;
                cmd.Parameters.Add("@IsHazardous", SqlDbType.Bit).Value = IsProdHaz;
                cmd.Parameters.Add("@IsOrderViaIPOonly", SqlDbType.Bit).Value = IsOrderViaIPOonly;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cmd.Parameters.Add("@IsRestrictedProduct", SqlDbType.Bit).Value = IsRestrictedProduct;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductMessage = GetReaderValue_String(reader, "ProductMessage", "");
                    obj.hazardousMessage = GetReaderValue_String(reader, "hazardousMsg", "");
                    obj.IPOMessage = GetReaderValue_String(reader, "IOPMsg", "");
                    obj.RestrictedMessage = GetReaderValue_String(reader, "RestrictedMsg", "");

                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Product Message", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// Calls [[usp_GetHazardousProductMessage]]
		/// </summary>
		public override ProductDetails GetHazardousProductStatusMessage(System.Int32? ProductNo, System.Boolean IsProdHaz, System.Boolean IsOrderViaIPOonly, System.Int32? ClientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetHazardousProductMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ProductId", SqlDbType.Int).Value = ProductNo;
                cmd.Parameters.Add("@IsHazardous", SqlDbType.Int).Value = IsProdHaz;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductMessage = GetReaderValue_String(reader, "ProductMessage", "");

                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Hazardous Product Message", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Product AutoSearch Category
        /// Calls [usp_autosearch_ProductCategary]
        /// </summary>
        public override List<ProductDetails> AutoSearchCategory(System.String nameSearch, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_ProductCategary", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ProductDetails> lst = new List<ProductDetails>();
                while (reader.Read())
                {
                    ProductDetails obj = new ProductDetails();
                    obj.ProductId = GetReaderValue_Int32(reader, "GlobalProductNameId", 0);
                    obj.ProductName = GetReaderValue_String(reader, "GlobalProductName", "");
                    //obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    // obj.Hazarders = GetReaderValue_NullableBoolean(reader, "IsHazarders", false);
                    // obj.OriginalProductDescription = GetReaderValue_String(reader, "OriginalProductDescription", "");
                    //[002] code start add parameter OrderViaIPOonly
                    //obj.OrderViaIPOonly = GetReaderValue_NullableBoolean(reader, "IsOrderViaIPOonly", false);
                    //[002] code end
                    // obj.ProductMessage = GetReaderValue_String(reader, "ProductMessage", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Product Category", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool UpdateMappingProductCategory(System.Int32? categoryId, System.String unselectedProductName, 
            System.String selectedProductName, System.Boolean? inactive, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ProductCategoryMapping", cn);
                cmd.Parameters.Add("@CategoryID", SqlDbType.Int).Value = categoryId;
                cmd.Parameters.Add("@UnselectedProductName", SqlDbType.NVarChar).Value = unselectedProductName;
                cmd.Parameters.Add("@SelectedProductName", SqlDbType.NVarChar).Value = selectedProductName;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Categories", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}