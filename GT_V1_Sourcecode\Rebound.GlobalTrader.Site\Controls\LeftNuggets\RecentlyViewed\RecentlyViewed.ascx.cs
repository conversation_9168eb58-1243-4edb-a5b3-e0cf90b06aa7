using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site;
using System.Text;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class RecentlyViewed : Base {

		#region Locals

		private Panel _pnlItems;
		private List<string> _lstItemIDs = new List<string>();

		#endregion

		#region Properties

		private static int _intNumberToShow = 10;
		public static int NumberToShow {
			get { return _intNumberToShow; }
			set { _intNumberToShow = value; }
		}

		#endregion

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("RecentlyViewed");
			base.OnInit(e);
			AddScriptReference("Controls.LeftNuggets.RecentlyViewed.RecentlyViewed");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			_pnlItems = (Panel)ctlDesignBase.FindContentControl("pnlItems");
			SetupScriptDescriptors();
		}

		protected override void Render(HtmlTextWriter writer) {
			AddToList(((Pages.Base)Page).FullURLWithQueryString, ((Pages.Base)Page).TitleText);
			ControlBuilders.CreateLiteralInsideParent(_pnlItems, RenderPageHistory(GetPageHistory(), ctlDesignBase.ClientID));
			base.Render(writer);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.RecentlyViewed", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlItems", _pnlItems.ClientID);
		}

		private static List<RecentlyViewedObject> GetPageHistory() {
			List<RecentlyViewedObject> lst = new List<RecentlyViewedObject>();
			foreach (BLL.RecentlyViewed rv in BLL.RecentlyViewed.GetListForUser(SessionManager.LoginID)) {
				lst.Add(new RecentlyViewedObject(rv.RecentlyViewedId, rv.PageTitle, rv.PageURL, rv.Locked));
			}
			return lst;
		}

		/// <summary>
		/// Returns the list of pages recently viewed as an HTML string
		/// </summary>
		public static string RenderPageHistoryForAjax(string strClientID) {
			return RenderPageHistory(GetPageHistory(), strClientID);
		}

		public static string RenderPageHistory(List<RecentlyViewedObject> lstPageHistory, string strClientID) {
			StringBuilder sbOut = new StringBuilder("");
			int i = 0;
			if (lstPageHistory.Count > 0) {
				sbOut.Append("<ul class=\"recentlyViewed\">");
				foreach (RecentlyViewedObject rv in lstPageHistory) {
					string strItemID = string.Format("{0}_item{1}", strClientID, i);
					sbOut.AppendFormat(@"<li><a href=""{0}"">{1}</a><div id=""{3}"" class=""recentlyViewedLock{2}"" bgt_locked=""{4}"", onclick=""$find('{5}').toggleLock({6}, {7});"">&nbsp;</div></li>", rv.URL, rv.Title, (rv.Locked) ? "On" : "Off", strItemID, rv.Locked.ToString().ToLower(), strClientID, i, rv.ID);
					i += 1;
				}
				sbOut.Append("</ul>");
			}
			return sbOut.ToString();
		}

		/// <summary>
		/// Add to Page History list
		/// </summary>
		/// <param name="strPageURL"></param>
		/// <param name="strTitleText"></param>
		public static void AddToList(string strPageURL, string strTitleText) {
			if (ShouldPageBeAddedToList(strPageURL, strTitleText)) {
				BLL.RecentlyViewed.Insert(SessionManager.LoginID, strTitleText, strPageURL);
			}
		}

		private static bool ShouldPageBeAddedToList(string strPageURL, string strTitleText) {
			bool bln = true;
			if (strPageURL.Contains(Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("NotFound").RelativeUrl)) bln = false;
			if (strTitleText.Contains(Functions.GetGlobalResource("PageTitles", BLL.SitePage.List.Error))) bln = false;
			return bln;
		}

	}

	public struct RecentlyViewedObject {
		public int ID;
		public string Title;
		public string URL;
		public bool Locked;

		public RecentlyViewedObject(int intID, string strTitle, string strURL, bool? blnLocked) {
			ID = intID;
			Title = strTitle;
			URL = strURL;
			Locked = Convert.ToBoolean(blnLocked);
		}
	}
}