﻿//Marker     Changed by         Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>     25/10/2021    Added class for Warning Type.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract  class WarningTypeProvider : DataAccess
    {
        static private WarningTypeProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public WarningTypeProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (WarningTypeProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.WarningTypes.ProviderType));
                return _instance;
            }
        }
        public WarningTypeProvider()
        {
            this.ConnectionString = Globals.Settings.RohsStatuss.ConnectionString;
        }

        #region Method Registrations

        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_ROHSStatus]
        /// </summary>
        public abstract List<WarningTypeDetails> DropDown();

        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_LabelType]
        /// </summary>
        public abstract List<WarningTypeDetails> LableTypeDropDown();

        



        #endregion
    }
}
