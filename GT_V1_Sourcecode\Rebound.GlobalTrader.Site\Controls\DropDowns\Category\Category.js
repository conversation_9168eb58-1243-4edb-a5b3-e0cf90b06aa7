Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Category=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Category.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Category.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Category.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Category.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Category");this._objData.set_DataObject("Category");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Categories)for(n=0;n<t.Categories.length;n++)this.addOption(t.Categories[n].Name,t.Categories[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Category.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Category",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);