//-------------------------------------------------------------------------------------------
// RP 14.03.2011:
// - add specific queries for YTD and Last Year values to prevent timeouts
//
// RP 17.11.2009:
// - use specific queries to cut down data going through pipes
// - get the YTD / last year values in one hit
// - add SupplierNo field
//Marker     Changed by      Date         Remarks
//[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//[002]      Vinay          25/03/2014     ESMS Ref:107 -  Add provision to Default Shipping from Country 
//-------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanyPurchasingInfo : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "GetOpenPOs": GetOpenPOs(); break;
                    case "GetOverduePOs": GetOverduePOs(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "GetYearToDate": GetYearToDate(); break;
                    case "GetLastYear": GetLastYear(); break;                          
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Get main data plus YTD, Last year
        /// </summary>
        private void GetData()
        {
            Company cm = Company.GetPurchaseInfo(ID);
            JsonObject jsn = null;
            if (cm != null)
            {
                jsn = new JsonObject();
                jsn.AddVariable("IsApproved", cm.POApproved);
                jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(cm.POCurrencyDescription, cm.POCurrencyCode));
                jsn.AddVariable("CurrencyNo", cm.POCurrencyNo);
                jsn.AddVariable("ContactName", cm.DefaultPOContactName);
                jsn.AddVariable("ContactNo", cm.DefaultPOContactNo);
                jsn.AddVariable("Terms", cm.POTermsName);
                jsn.AddVariable("TermsNo", cm.POTermsNo);
                jsn.AddVariable("Tax", cm.POTaxName);
                jsn.AddVariable("TaxNo", cm.POTaxNo);
                jsn.AddVariable("ShipVia", cm.DefaultPurchaseShipViaName);
                jsn.AddVariable("ShipViaNo", cm.DefaultPurchaseShipViaNo);
                jsn.AddVariable("FreightVal", Functions.FormatCurrency(cm.DefaultPurchaseFreightCharge, cm.POCurrencyCode, 2));
                jsn.AddVariable("ShippingCostVal", Functions.FormatCurrency(cm.DefaultPurchaseShippingCost, cm.POCurrencyCode, 2));
                jsn.AddVariable("ShippingAccountNo", cm.DefaultPurchaseShipViaAccount);
                jsn.AddVariable("BillToAddress", Functions.ReplaceLineBreaks(AddressManager.ToLongString(cm.DefaultBillingAddress)));
                jsn.AddVariable("ShipFromCountry", cm.DefaultBillingAddress.CountryNo);
                //ESMS #14
                jsn.AddVariable("TaxbyAddress", cm.DefaultBillingAddress.TaxbyAddress);
                jsn.AddVariable("TaxbyValue", cm.DefaultBillingAddress.TaxValue);
                // End
                //[001] code start
                jsn.AddVariable("Incoterm", cm.DefaultBillingAddress.IncotermName);
                jsn.AddVariable("IncotermNo", cm.DefaultBillingAddress.IncotermNo);
                //[001] code end
                jsn.AddVariable("Rating", cm.PORating);
                jsn.AddVariable("SupplierCode", cm.SupplierCode);
                jsn.AddVariable("DLUP", Functions.FormatDLUP(cm.DLUP, cm.UpdatedBy));
                jsn.AddVariable("POCurrencyCode", cm.POCurrencyCode);
                jsn.AddVariable("ExchangeRate", Functions.FormatCurrency(cm.ExchangeRate, 5));
                jsn.AddVariable("GlobalCurrencyNo", cm.GlobalCurrencyNo);
                jsn.AddVariable("GlobalCurrencyCode", cm.GlobalCurrencyCode);
                jsn.AddVariable("EARIReported", cm.ERAIReported);
                jsn.AddVariable("DefaultPOCountryNo", cm.DefaultPOShipCountryNo);
                jsn.AddVariable("DefaultPOCountry", cm.DefaultPOShipCountry);
                jsn.AddVariable("DivisionNo", SessionManager.LoginDivisionID);
                jsn.AddVariable("BuyerNo", SessionManager.LoginID);
                //[002] code start
                jsn.AddVariable("ApprovedByAndDate", Functions.FormatUPbyOn(cm.POApprovedDate, cm.POApprovedBy));
                //[002] Code End
                jsn.AddVariable("OnStop", cm.SupplierOnStop);
                jsn.AddVariable("TaxByAddressNo", cm.TaxByAddrssNo);
                jsn.AddVariable("WarehouseNo", cm.WarehouseNo);
                jsn.AddVariable("Inactive", cm.Inactive);
            }
            OutputResult(jsn);
            cm = null;
            jsn.Dispose(); jsn = null;
        }
        
        /// <summary>
        /// Gets the open POs
        /// </summary>
        private void GetOpenPOs()
        {
            //Invoices
            JsonObject jsn = new JsonObject();
            JsonObject jsnInvoices = new JsonObject(true);
            List<PurchaseOrder> lst = PurchaseOrder.GetListOpenForCompany(ID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].PurchaseOrderId);
                jsnItem.AddVariable("No", lst[i].PurchaseOrderNumber);
                jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DateOrdered));
                jsnItem.AddVariable("Amount", Functions.FormatCurrency(lst[i].PurchaseOrderValue, lst[i].CurrencyCode, 2,true));
                jsnInvoices.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst = null;
            jsn.AddVariable("Items", jsnInvoices);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }

        /// <summary>
        /// Gets the overdue POs
        /// </summary>
        private void GetOverduePOs()
        {
            //Invoices
            JsonObject jsn = new JsonObject();
            JsonObject jsnInvoices = new JsonObject(true);
            List<PurchaseOrder> lst = PurchaseOrder.GetListOverdueForCompany(ID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].PurchaseOrderId);
                jsnItem.AddVariable("No", lst[i].PurchaseOrderNumber);
                jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DateOrdered));
                jsnItem.AddVariable("Amount", Functions.FormatCurrency(lst[i].PurchaseOrderValue, lst[i].CurrencyCode, 2));
                jsnInvoices.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst = null;
            jsn.AddVariable("Items", jsnInvoices);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }

        /// <summary>
        /// Save Edit
        /// </summary>
        public void SaveEdit()
        {
            //try {
            bool blnResult = BLL.Company.UpdatePurchaseInfo(
                ID
                , GetFormValue_String("SupplierNo")
                , GetFormValue_Boolean("IsApproved")
                , GetFormValue_NullableInt("Rating")
                , GetFormValue_NullableInt("TermsNo")
                , GetFormValue_NullableInt("CurrencyNo")
                , GetFormValue_NullableInt("TaxNo")
                , GetFormValue_NullableInt("ContactNo")
                , GetFormValue_String("ShippingAccountNo")
                , GetFormValue_NullableInt("ShipViaNo")
                , SessionManager.LoginID
                , GetFormValue_NullableInt("CountryNo")
                , GetFormValue_Boolean("OnStop")
                );
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Result", blnResult);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }


        private void GetDefaultPurchasingInfo()
        {
            Company cm = Company.GetDefaultPurchasingInfo(ID);
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("ShippingAccountNo", cm.DefaultPurchaseShipViaAccount);
            jsn.AddVariable("ShipViaNo", cm.DefaultPurchaseShipViaNo);
            jsn.AddVariable("CurrencyNo", cm.DefaultPurchaseShipViaNo);
            jsn.AddVariable("TermsNo", cm.POTermsNo);
            jsn.AddVariable("Terms", cm.POTermsName);
            jsn.AddVariable("TaxNo", cm.POTaxNo);
            jsn.AddVariable("Tax", cm.POTaxName);
            jsn.AddVariable("CurrencyNo", cm.POCurrencyNo);
            jsn.AddVariable("Currency", cm.POCurrencyCode);
            jsn.AddVariable("ContactNo", cm.DefaultPOContactNo);
            jsn.AddVariable("Contact", cm.DefaultPOContactName);
            jsn.AddVariable("BillToAddress", Functions.ReplaceLineBreaks(AddressManager.ToLongString(cm.DefaultBillingAddress)));
            jsn.AddVariable("ShipFromCountry", cm.DefaultBillingAddress.CountryNo);
            cm = null;
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
        }

        private void GetYearToDate()
        {
            BLL.Company cm = BLL.Company.SummariseThisYearPurchaseOrderValue(ID);
            JsonObject jsn = new JsonObject();
            if (cm == null)
            {
                jsn.AddVariable("Value", Functions.FormatCurrency(0, 2));
            }
            else
            {
                jsn.AddVariable("Value", Functions.FormatCurrency(cm.PurchaseOrderValueYTDInBase, cm.POCurrencyCode, 2));
            }
            OutputResult(jsn);
            jsn.Dispose();
            cm = null;
        }

        private void GetLastYear()
        {
            BLL.Company cm = BLL.Company.SummariseLastYearPurchaseOrderValue(ID);
            JsonObject jsn = new JsonObject();
            if (cm == null)
            {
                jsn.AddVariable("Value", Functions.FormatCurrency(0, 2));
            }
            else
            {
                jsn.AddVariable("Value", Functions.FormatCurrency(cm.PurchaseOrderValueLastYearInBase, cm.POCurrencyCode, 2));
            }
            OutputResult(jsn);
            jsn.Dispose();
            cm = null;
        }


    }
}