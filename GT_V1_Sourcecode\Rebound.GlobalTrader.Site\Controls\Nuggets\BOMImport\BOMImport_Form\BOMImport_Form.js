Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.initializeBase(this,[n]);this._intBOMID=0};Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_tblPartdetails:function(){return this._tblPartdetails},set_tblPartdetails:function(n){this._tblPartdetails!==n&&(this._tblPartdetails=n)},get_btn1:function(){return this._btn1},set_btn1:function(n){this._btn1!==n&&(this._btn1=n)},get_btn2:function(){return this._btn2},set_btn2:function(n){this._btn2!==n&&(this._btn2=n)},get_lblError:function(){return this._lblError},set_lblError:function(n){this._lblError!==n&&(this._lblError=n)},get_intLoginID:function(){return this._intLoginID},set_intLoginID:function(n){this._intLoginID!==n&&(this._intLoginID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.callBaseMethod(this,"initialize");this._tblPartdetails.addSelectedIndexChanged(Function.createDelegate(this,this.getParSearch));this._btn1&&$addHandler(this._btn1,"click",Function.createDelegate(this,this.Toggle1));this._btn2&&$addHandler(this._btn2,"click",Function.createDelegate(this,this.Toggle2));$R_FN.showElement(this._lblError,!1);$R_FN.showElement(this._btn2,!1);this.showField("ctlPartDetail",!1)},dispose:function(){this.isDisposed||(this._tblPartdetails=null,this._btn1=null,this._btn2=null,this._lblError=null,this._intLoginID=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.callBaseMethod(this,"dispose"))},impotExcelData:function(n,t,i,r){$("#divLoader").show();var u=new Rebound.GlobalTrader.Site.Data;u.set_PathToData("controls/Nuggets/BOMImport");u.set_DataObject("BOMImport");u.set_DataAction("ImportExcelData");u.addParameter("originalFilename",n);u.addParameter("generatedFilename",t);u.addParameter("BOMId",i);u.addParameter("ExcelColumns",r);u.addDataOK(Function.createDelegate(this,this.importExcelDataOK));u.addError(Function.createDelegate(this,this.importExcelDataError));u.addTimeout(Function.createDelegate(this,this.importExcelDataError));$R_DQ.addToQueue(u);$R_DQ.processQueue();u=null},importExcelDataOK:function(n){flogId=n._result.FileLogId;bindGridData.call(this);$("#divLoader").hide()},importExcelDataError:function(n){alert(n._errorMessage.split("<br/>")[0]);$("#divLoader").hide()},getParSearch:function(){$("#ddlPackage").val(0);$("#ddlRoHS").val(0);$("#txtManufacturer").val("");$("#txtDateCode").val("");$("#txtProduct").val("");var n=this._tblPartdetails._varSelectedValue;n.length>0&&this.getValuesByParts()},getValuesByParts:function(){var n=this._tblPartdetails.getSelectedExtraData(),t;n&&(t=n.PartName,$R_FN.showElement(this._btn2,!0),n.ProductInactive?this.setFieldValue("ctlProduct",0,null,""):this.setFieldValue("ctlProduct",n.ProductNo,null,n.ProductDescription),$("#ddlPackage").val(n.PackageNo),$("#ddlRoHS").val(n.ROHSNo),$("#txtDateCode").val(n.DateCodeOriginal),this.setFieldValue("ctlManufacturer",n.ManufacturerNo,null,n.Manufacturer),this.setFieldValue("ctlPartNo",t))},getControlValue:function(n){return this.getFieldValue(n)},setComboValue:function(n,t,i){this.setFieldValue(n,t,null,i)},setPartNo:function(n){this.setFieldValue("ctlPartNo",n)},clearAutoSearchField:function(){this.setFieldValue("ctlProduct",0,null,"");this.setFieldValue("ctlManufacturer",0,null,"");this.setFieldValue("ctlPartNo","")},Toggle2:function(){this.clearAutoSearchField();$("#ddlPackage").val(0);$("#ddlRoHS").val(0);$("#txtDateCode").val("");this._tblPartdetails.clearTable();this.showField("ctlPartDetail",!1);$R_FN.showElement(this._btn1,!0);$R_FN.showElement(this._btn2,!1)},Toggle1:function(){var t,n;if(this.getFieldValue("ctlPartNo").length<3){$R_FN.showElement(this._lblError,!0);return}$R_FN.showElement(this._lblError,!1);t=this.getFieldValue("ctlPartNo");t.length>0&&(this.showProductLoading(!0),n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/CusReqAdd"),n.set_DataObject("CusReqAdd"),n.set_DataAction("GetDataGrid"),n.addParameter("searchType",this.getFieldValue("ctlPartNo")),n.addDataOK(Function.createDelegate(this,this.getDataGrid)),n.addError(Function.createDelegate(this,this.getDataGridError)),n.addTimeout(Function.createDelegate(this,this.getDataGridError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null)},getDataGridError:function(){},getDataGrid:function(n){var i;for(res=n._result,this.showField("ctlPartDetail",res.PartDetails.length>0),this._tblPartdetails.clearTable(),i=0;i<res.PartDetails.length;i++){var t=res.PartDetails[i],r=[t.ID,t.Manufacturer,t.Product,t.Package,t.DateCode,],u={PartName:t.ID,DateCodeOriginal:t.DateCodeOriginal,ManufacturerNo:t.ManufacturerNo,ProductNo:t.ProductNo,PackageNo:t.PackageNo,Manufacturer:t.Manufacturer,ROHSNo:t.ROHSNo,ProductDescription:t.ProductDescription,ProductInactive:t.PrdInactive};this._tblPartdetails.addRow(r,t.Ids,!1,u);r=null;t=null;this.showProductLoading(!1)}this._tblPartdetails.resizeColumns()},showProductLoading:function(n){this.showFieldLoading("ctlPartNo",n)}};Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form",Rebound.GlobalTrader.Site.Controls.Forms.Base);