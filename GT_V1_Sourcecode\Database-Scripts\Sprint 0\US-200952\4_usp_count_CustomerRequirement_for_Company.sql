GO

ALTER PROCEDURE [dbo].[usp_count_CustomerRequirement_for_Company]  

/*
-- ==========================================================================================
-- TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION                                    
--  				     				23-07-2009   	Create			new proc
-- US-200952		Phuc.HoangDinh		24-04-2024		Update			Update for US-200952
-- ==========================================================================================
*/
    @CompanyId int  
  , @IncludeClosed bit = 0  
AS   
    SELECT  count(*)  
    FROM    dbo.vwCustomerRequirement  
    WHERE   CompanyNo = @CompanyId
			AND BOM <> 1
            AND Closed IN (0, @IncludeClosed)  
            AND (OriginalCustomerRequirementNo IS NULL  
                 OR OriginalCustomerRequirementNo = 0)
			AND ReceivedDate>=DATEADD(MONTH,-24,GETDATE())	   
            --AND convert(varchar, DatePromised, 112) >= convert(varchar, getdate(), 112)  
GO