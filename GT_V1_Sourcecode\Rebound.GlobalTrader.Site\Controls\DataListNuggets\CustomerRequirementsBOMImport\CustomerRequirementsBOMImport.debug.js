///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.initializeBase(this, [element]);
    this._blnGet = true;
    this._intCompanyNo = -1;
    this._intContactNo = -1;

};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_strCompanyName: function() { return this._strCompanyName; }, set_strCompanyName: function(v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    //get_cmbCustomer: function() { return this._cmbCustomer; }, set_cmbCustomer: function(v) { if (this._cmbCustomer !== v) this._cmbCustomer = v; },
    //get_ddlContact: function() { return this._ddlContact; }, set_ddlContact: function(v) { if (this._ddlContact !== v) this._ddlContact = v; },
    get_intContactID: function() { return this._intContactID; }, set_intContactID: function(v) { if (this._intContactID !== v) this._intContactID = v; },



    initialize: function() {

        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));

        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this.addSortDataEvent(Function.createDelegate(this, this.getSorting));
       // this._ibtnPrint = $get(this._aryButtonIDs[0]);
        this._strPathToData = "controls/DataListNuggets/CustomerRequirementsBOMImport";
        this._strDataObject = "CustomerRequirementsBOMImport";
        this.enableBulkButtons(false);
        this._table.addMultipleSelectionChanged(Function.createDelegate(this, this.selectionMade));
        this._strCK = "Req";
        this._strCKExp = 1;
        this._lnsSeperator = "|";
        //alert(this._intCompanyID);
        //

        if (this._ibtnPrint) $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.printCustReq));
        //this.getData();
        //this.showLoading(false);
        this._table._intSortColumnIndex = 4;
        this._table._enmSortDirection = 2;

        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function () {
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intCompanyID = null;
        this._strCompanyName = null;
        this._intContactID = null;
        this._blnGet = null;
        this._intCompanyNo = null;
        this._intContactNo = null;
        if (this._ibtnPrint) this._ibtnPrint = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.callBaseMethod(this, "dispose");
    },

    enableBulkButtons: function(bln) {
        if (this._ibtnPrint) $R_IBTN.enableButton(this._ibtnPrint, bln);
    },
    setupDataCall: function() {
        this._objData.addParameter("CmpID", this._intCompanyNo);
        this._objData.addParameter("ConID", this._intContactNo);
        this._objData.addParameter("IsGet", this._blnGet);
        this._blnGet = false;
        this._objData.addParameter("PageLimit", this._txtLimitResults.value);
        //alert(this._txtLimitResults.value);
    },

    printCustReq: function() {
   
        if (this._cmbCustomer)
            this._intCompanyID = this._cmbCustomer.getValue();
        
        var strIDs = $R_FN.arrayToSingleString(this._table._aryCurrentValues, this._lnsSeperator);
        $R_FN.setCookie(this._strCK, strIDs, this._strCKExp); strIDs = "";
        $R_FN.openPrintWindowCustReqWithMultiples($R_ENUM$PrintObject.CustomerRequirement, this._intCompanyID);
        strIDs = null;
    },
    selectionMade: function() {
        this.enableBulkButtons(this._table._arySelectedIndexes.length > 0);
    },

    getDataOK: function(args) {          
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
                $R_FN.writeDoubleCellValue($RGT_nubButton_ClientBOMImport(row.ID, row.ClientBOMNo), row.ClientBOMName)
                , $R_FN.writeDoubleCellValue(row.Company, row.ContactName)
                , $R_FN.writeDoubleCellValue($RGT_nubButton_BOM(row.BomId, row.BOMName), "")
                , $R_FN.setCleanTextValue(row.Salesman)
				, $R_FN.setCleanTextValue(row.ImportDate)
                , $R_FN.writeDoubleCellValue(row.RecordsProcessed, row.RecordsRemaining)
                //, $R_FN.setCleanTextValue(row.NoOfRequirements)
			];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    enableApplyFilter: function(bln) {
        if (this._ibtnApply) $R_IBTN.enableButton(this._ibtnApply, bln);
    },
    applyFilter: function() {
        //if (this._cmbCustomer)
        //    this._intCompanyNo = this._cmbCustomer.getValue();
        //if (!this._intCompanyNo) {
        //    alert("Please select customer.");
        //    return;
        //}
        //if (this._ddlContact)
        //    this._intContactNo = this._ddlContact.getValue();
        //if (!this._intContactNo) {
        //    alert("Please select contact.");
        //    return;
        //}
        this._blnGet = true;
        if (this._blnGettingData) return;
        this.onFilterData();
    },
    selectedCustomer: function() {
        this._table.clearTable();
        this._table.resizeColumns();
        this._table.clearSelection(true);
        //this.getData();

        //if (this._cmbCustomer)
        //    this._intCompanyNo = this._cmbCustomer.getValue();
        //if (!this._intCompanyNo) {
        //    this._intCompanyNo = 0;
        //}
        //this._ddlContact._intCompanyID = this._intCompanyNo;
        //this._ddlContact.getData();
        // alert(this._ddlContact._intCompanyID);
        this.selectionMade();
    },
    resetFilter: function() {
        for (var i = 0, l = this._aryFilterFieldIDs.length; i < l; i++) {
            var fld = $find(this._aryFilterFieldIDs[i]);
            fld.reset();
            fld.resetToDefault();
            fld = null;
        }
        if (this._blnAllowSelection) {
            this._txtLimitResults.value = 50;
            this._intResultsLimit = 50;
        }
        this._cmbCustomer.setValue("", "");
    },
    reselectData: function() {
     
        this._table.clearTable();
        this._table.resizeColumns();
        this._table.clearSelection(true)
        this._blnGet = false;
        //this._ddlContact._intCompanyID = -1;
        //this._ddlContact.getData();
        this.getData();
    },
    getSorting: function() {
        this._blnGet = true;
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
