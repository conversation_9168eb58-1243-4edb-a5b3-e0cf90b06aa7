﻿
body {
    font-family: Tahoma;
    background-color: #56954e;
}

.container-greenbase {
    background-color: #56954e;
    color: #fff;
    font-size: 11px;
    font-family: tahoma;
    padding: 10px;
}

table {
    width: 100%;
    border-collapse: collapse;
    background-color: #56954E;
}

thead {
    background-color: #fee64b;
}

.botttom_tbl tr th {
    background-color: #fdf5b7;
}


.form_container table tr td {
    border: 1px solid #cccccc;
    text-align: left;
    padding: 5px 5px;
    font-size: 11px;
    color: #006600;
    background-color: #fff;
}

table tr th {
    border: 1px solid #cdbe63;
}

.tittle {
    width: 18%;
    padding-right: 10px;
    padding-bottom: 7px;
    text-align: left;
    font-weight: bold;
    font-size: 11px;
    color: #d3fFcC;
    vertical-align: top;
}

h5 {
    font-size: 10px;
    margin: 0px 0px 2px;
    text-transform: uppercase;
    color: #fff;
    font-weight: bold;
}


span {
    float: left;
    width: 100%;
    padding-left: 24px;
    color: #89b583;
    font-size: 16px;
}

.select_btn, .deselect_btn {
    background-image: url(./images/select_x.gif);
    background-position: left center;
    background-repeat: no-repeat;
}

.deselect_btn {
    background-image: url(./images/deselect_x.gif) !important;
}

.form_container {
    width: 175px;
    height: 139px;
    overflow-x: hidden;
}

td img {
    margin-left: 15px;
    margin-right: 20px;
    width: 12%;
}


.radion_content {
    float: left;
    text-align: left;
    width: 100%;
    padding: 10px;
    margin-bottom: 10px;
}


.file_container {
    width: 30%;
    border: 1px solid #AAE2A0;
    border-radius: 4px;
    position: absolute;
    left: 35%;
    top: 35%;
}



.sec_one {
    width: 175px;
    height: 139px;
}

    .sec_one option {
        padding: 4px !important;
    }

.radion_content img {
    margin-left: 15px;
    margin-right: 20px;
    width: 15%;
}


.btn_sel {
    float: left;
    width: 100%;
    padding-left: 24px;
    color: #89b583;
    font-size: 16px;
    background-image: url(/Areas/BOM/Images/select_x.gif);
    background-repeat: no-repeat;
    text-align: left;
    background-color: #56954e;
    border: none;
    background-position: left center;
}

.btn_desel {
    background-image: url(/Areas/BOM/Images/deselect_x.gif) !important;
}

.file_tittle {
    width: 100%;
    text-align: left;
    margin-bottom: 10px;
    font-size: 12px;
    font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
    color: #000;
    display: block;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
    background-repeat: repeat-x;
    padding: 8px 3px;
    font-weight: 700;
}

.print_btn {
    text-align: left;
    background-image: url(../../../App_Themes/Original/images/Nuggets/standard/bg_top.gif);
    background-repeat: repeat-x;
    padding: 5px 10px;
}

