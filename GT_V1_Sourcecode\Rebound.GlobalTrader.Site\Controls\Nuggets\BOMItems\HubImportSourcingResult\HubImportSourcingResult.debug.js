﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.initializeBase(this, [element]);
    this._intBOMID = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.prototype = {
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.callBaseMethod(this, "initialize");
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intBOMID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.callBaseMethod(this, "dispose");
    },

    importExcelData: function (originalFilename, generatedFilename, iscolumnheaderchk) {
        $('#divLoader').show();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj._intTimeoutMilliseconds = 600000;
        obj.set_PathToData("controls/Nuggets/BOMItems/HubImportSourcingResult");
        obj.set_DataObject("HubImportSourcingResult");
        obj.set_DataAction("ImportExcelData");
        obj.addParameter("originalFilename", originalFilename);
        obj.addParameter("generatedFilename", generatedFilename);
        obj.addParameter("ColumnHeader", iscolumnheaderchk);
        obj.addParameter("BOMNo", this._intBOMID);

        obj.addDataOK(Function.createDelegate(this, this.importExcelDataOK));
        obj.addError(Function.createDelegate(this, this.importExcelDataError));
        obj.addTimeout(Function.createDelegate(this, this.importExcelDataError));

        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

        return true;
    },

    importExcelDataOK: function (args) {
        flogId = args._result.FileLogId;
        $('#divLoader').hide();
        $("#btnDisplayCsvData").prop('disabled', false);
        $("#sourcingExcelUpload").prop('disabled', true).css('opacity', 0.5);
        $("#fileUploadWraper").addClass('prevent-upload');
        $('input:checkbox[id="chkFileCCH"]').prop('disabled', true);
        $('input:file').filter(function () {
            return this.files.length == 0
        }).prop('disabled', true);
        var IsLimitExceeded = args._result.IsLimitExceeded;
        if (IsLimitExceeded) {
            alert(args._result.LimitErrorMessage);
        }
    },

    importExcelDataError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        $('#divLoader').hide();
    },

    checkUnsaveData: function () {
        return isGridModified.call(this);
    },

    resetForm: function () {
        //call reset function in HubSourcingResultImport.js
        reset.call(this);
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.HubImportSourcingResult", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
