Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.initializeBase(this,[n]);this._intLineID=-1;this._intCompanyID=0};Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intLineID=null,this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&($find(this.getField("ctlCategory").ControlID).addChanged(Function.createDelegate(this,this.getCertificateByCategory)),this.addSave(Function.createDelegate(this,this.saveClicked)))},getCertificateByCategory:function(){this.showCertificateFieldsLoading(!0);this.getFieldComponent("ctlCertificate")._intCategoryID=this.getFieldValue("ctlCategory");this.getFieldDropDownData("ctlCertificate");this.showCertificateFieldsLoading(!1)},showCertificateFieldsLoading:function(n){this.showFieldLoading("ctlCertificate",n)},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanyCertificate");n.set_DataObject("CompanyCertificate");n.set_DataAction("SaveEdit");n.addParameter("id",this._intLineID);n.addParameter("Category",this.getFieldValue("ctlCategory"));n.addParameter("Certificate",this.getFieldValue("ctlCertificate"));n.addParameter("Number",this.getFieldValue("ctlCertificateNumbre"));n.addParameter("StartDate",this.getFieldValue("ctlStartDate"));n.addParameter("ExpiryDate",this.getFieldValue("ctlExpiryDate"));n.addParameter("InActive",this.getFieldValue("ctlInActive"));n.addParameter("Desc",this.getFieldValue("ctlDescription"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return n||this.showError(!0),n}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);