///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for sales order section
[002]      Vinay           05/02/2014    CR:- Add AS9120 Requirement in GT application
[003]      S<PERSON> Keshar   05/02/2014    CR:- Import CSV Log Referess
[004]      A<PERSON><PERSON>     20-Aug-2018   Provision to add Global Security in Sales Order
[005]      Aashu Singh     28-Aug-2018   Show so payment attachment
[006]      Abhinav Saxena  01-12-2021    Add Export to excel Button
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.SODetail = function (el) {
    Rebound.GlobalTrader.Site.Pages.Orders.SODetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.SODetail.prototype = {

    get_intSOID: function () { return this._intSOID; }, set_intSOID: function (v) { if (this._intSOID !== v) this._intSOID = v; },
    get_ctlMainInfo: function () { return this._ctlMainInfo; }, set_ctlMainInfo: function (v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlAuthorisation: function () { return this._ctlAuthorisation; }, set_ctlAuthorisation: function (v) { if (this._ctlAuthorisation !== v) this._ctlAuthorisation = v; },
    get_ctlLines: function () { return this._ctlLines; }, set_ctlLines: function (v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_btnPrint: function () { return this._btnPrint; }, set_btnPrint: function (v) { if (this._btnPrint !== v) this._btnPrint = v; },
    get_pnlStatus: function () { return this._pnlStatus; }, set_pnlStatus: function (v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    get_lblStatus: function () { return this._lblStatus; }, set_lblStatus: function (v) { if (this._lblStatus !== v) this._lblStatus = v; },
    // [001] code start
    get_ctlSODDocuments: function () { return this._ctlSODDocuments }, set_ctlSODDocuments: function (a) { if (this._ctlSODDocuments !== a) { this._ctlSODDocuments = a } },
    // [001] code end
    get_ctlSORPDFDocuments: function () { return this._ctlSORPDFDocuments }, set_ctlSORPDFDocuments: function (a) { if (this._ctlSORPDFDocuments !== a) { this._ctlSORPDFDocuments = a } },

    get_ctlSOPDFDragDrop: function () { return this._ctlSOPDFDragDrop }, set_ctlSOPDFDragDrop: function (a) { if (this._ctlSOPDFDragDrop !== a) { this._ctlSOPDFDragDrop = a } },
    get_ctlSORPDFDocsDragDrop: function () { return this._ctlSORPDFDocsDragDrop }, set_ctlSORPDFDocsDragDrop: function (a) { if (this._ctlSORPDFDocsDragDrop !== a) { this._ctlSORPDFDocsDragDrop = a } },
    get_ctlDragDropForSOR: function () { return this._ctlDragDropForSOR }, set_ctlDragDropForSOR: function (a) { if (this._ctlDragDropForSOR !== a) { this._ctlDragDropForSOR = a } },
    get_lblSoStatus: function () { return this._lblSoStatus; }, set_lblSoStatus: function (v) { if (this._lblSoStatus !== v) this._lblSoStatus = v; },
    get_pnlConsolidateStatus: function () { return this._pnlConsolidateStatus; }, set_pnlConsolidateStatus: function (v) { if (this._pnlConsolidateStatus !== v) this._pnlConsolidateStatus = v; },
    //[004] start
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    //[004] end
    //[005] start
    get_ctlSOShowPaymentFiles: function () { return this._ctlSOShowPaymentFiles; }, set_ctlSOShowPaymentFiles: function (v) { if (this._ctlSOShowPaymentFiles !== v) this._ctlSOShowPaymentFiles = v; },
    //[005] end

    get_ctlSOEXCELDocFileDragDrop: function () { return this._ctlSOEXCELDocFileDragDrop }, set_ctlSOEXCELDocFileDragDrop: function (a) { if (this._ctlSOEXCELDocFileDragDrop !== a) { this._ctlSOEXCELDocFileDragDrop = a } },

    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Orders.SODetail.callBaseMethod(this, "initialize");
    },

    goInit: function () {
        if (this._btnPrint) {
            this._btnPrint.addPrint(Function.createDelegate(this, this.printSO));
            this._btnPrint.addEmail(Function.createDelegate(this, this.emailSO));
            this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        }
        if (this._ctlAuthorisation) this._ctlAuthorisation.addPotentialStatusChange(Function.createDelegate(this, this.ctlAuthorisation_PotentialStatusChange));
        if (this._ctlLines) {
            this._ctlLines.addPotentialStatusChange(Function.createDelegate(this, this.ctlLines_PotentialStatusChange));
            this._ctlLines.addRefreshChange(Function.createDelegate(this, this.ctlLines_RefreshChange));
        }
        if (this._ctlMainInfo) {
            this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
            this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_SaveEditComplete));
            this._ctlMainInfo.addPotentialStatusChange(Function.createDelegate(this, this.ctlMainInfo_PotentialStatusChange));
            this._ctlMainInfo.addPotentialStatus(Function.createDelegate(this, this.ctlMainInfo_ValidateChange));
            this._ctlMainInfo.addPotentialStatusCon(Function.createDelegate(this, this.ctlMainInfo_UpdateConsolidate));
            this.setLineDetailsFromMainInfo();
        }
        // [001] code start
        if (this._ctlSODDocuments) { this._ctlSODDocuments.getData() }
        // [001] code end

        if (this._ctlSORPDFDocuments) { this._ctlSORPDFDocuments.getData() }



        if (this._ctlSOPDFDragDrop) { this._ctlSOPDFDragDrop.getData() }

        if (this._ctlDragDropForSOR) { this._ctlDragDropForSOR.getData() }

        if (this._ctlMainInfo._frmEdit && this._ctlAuthorisation) {
            this._ctlMainInfo._frmEdit.addSave(Function.createDelegate(this, this.frmEditSaveClicked));
        }
        if (this._ctlAuthorisation) {
            this._ctlAuthorisation.addGetDataComplete(Function.createDelegate(this, this.ctlAuthorisation_GetDataComplete));
        }
        if (this._ctlSORPDFDocsDragDrop) {
            this._ctlSORPDFDocsDragDrop.addGetDataComplete(Function.createDelegate(this, this.ctlSORPDFDocsDragDrop_GetDataComplete));
        }
        if (this._ctlSORPDFDocsDragDrop) { this._ctlSORPDFDocsDragDrop.getData() }
        //[005] start
        if (this._ctlSOShowPaymentFiles) { this._ctlSOShowPaymentFiles.getData() }
        //[005] end
        if (this._ctlSOEXCELDocFileDragDrop) { this._ctlSOEXCELDocFileDragDrop.getData() }

        Rebound.GlobalTrader.Site.Pages.Orders.SODetail.callBaseMethod(this, "goInit");
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._btnPrint) this._btnPrint.dispose();
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlAuthorisation) this._ctlAuthorisation.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        this._ctlMainInfo = null;
        this._ctlAuthorisation = null;
        this._ctlLines = null;
        this._lblStatus = null;
        this._pnlStatus = null;
        this._intSOID = null;
        // [001] code start
        if (this._ctlSODDocuments) { this._ctlSODDocuments.dispose() }
        this._ctlSODDocuments = null;
        // [001] code end
        if (this._ctlSORPDFDocuments) { this._ctlSORPDFDocuments.dispose() }
        this._ctlSORPDFDocuments = null;
        if (this._ctlSORPDFDocsDragDrop) { this._ctlSORPDFDocsDragDrop.dispose() }
        this._ctlSORPDFDocsDragDrop = null;
        if (this._ctlDragDropForSOR) { this._ctlDragDropForSOR.dispose() }
        this._ctlDragDropForSOR = null;
        this._pnlConsolidateStatus = null;
        this._lblSoStatus = null;
        //[004] start
        this._IsGlobalLogin = null;
        //[004] end
        //[005] start
        this._ctlSOShowPaymentFiles = null;
        //[005] end
        if (this._ctlSOEXCELDocFileDragDrop) { this._ctlSOEXCELDocFileDragDrop.dispose() }
        this._ctlSOEXCELDocFileDragDrop = null;
        Rebound.GlobalTrader.Site.Pages.Orders.SODetail.callBaseMethod(this, "dispose");
    },

    ConsolidatePrintSO: function () {
        this._ctlMainInfo.showConfirmForm(0);
        //if (window.confirm($R_RES.ConsolidateSOConfirmMSG)) {
        //  //  alert('1');
        //        $R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidatePrintSO, this._intSOID);
        //    }
        //else {
        // //   alert('2');
        //        $R_FN.openPrintWindow($R_ENUM$PrintObject.SalesOrder, this._intSOID);
        //    }

    },

    printSO: function () {
        //alert(this._ctlMainInfo._Consolidated);
        if (this._ctlMainInfo._Consolidated == "0") {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.SalesOrder, this._intSOID);
        }
        else if (this._ctlMainInfo._Consolidated == "1") {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidatePrintSO, this._intSOID);
        }
        else {
            this.ConsolidatePrintSO();
        }   
        if (this._ctlMainInfo._ibtnSentOrder) $R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder, false);
        this._ctlMainInfo.set_ctlIncludeSentOrder(true);
        //  return;
        //   $R_FN.openPrintWindow($R_ENUM$PrintObject.SalesOrder, this._intSOID);
    },

    ConsolidateEmailSO: function () {
        this._ctlMainInfo.showConfirmForm(1);

        //if (window.confirm($R_RES.ConsolidateMailSOConfirmMSG)) {
        //    //  alert('1');
        //    $R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidateEmailSO, this._intSOID, true);
        //}
        //else {
        //    //   alert('2');
        //    $R_FN.openPrintWindow($R_ENUM$PrintObject.SalesOrder, this._intSOID, true);
        //}
    },

    emailSO: function () {
        if (this._ctlMainInfo._Consolidated == "0") {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.SalesOrder, this._intSOID, true);
        }
        else if (this._ctlMainInfo._Consolidated == "1") {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidateEmailSO, this._intSOID, true);
        }
        else {
            this.ConsolidateEmailSO();
        }

        //  $R_FN.openPrintWindow($R_ENUM$PrintObject.SalesOrder, this._intSOID, true);
    },

    printOtherDocs: function () {
        if (this._btnPrint._strExtraButtonClickCommand == "PrintProFormaInvoice") {
            if (this._ctlMainInfo._Consolidated == "0")
                $R_FN.openPrintWindow($R_ENUM$PrintObject.ProFormaInvoice, this._intSOID);
            else if (this._ctlMainInfo._Consolidated == "1")
                $R_FN.openPrintWindow($R_ENUM$PrintObject.ProFormaInvoiceCon, this._intSOID);
            else
                this._ctlMainInfo.showConfirmForm(2);
            if (this._ctlMainInfo._ibtnSentOrder) $R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder, false);
            this._ctlMainInfo.set_ctlIncludeSentOrder(true);
        }
        if (this._btnPrint._strExtraButtonClickCommand == "EmailProFormaInvoice") {
            if (this._ctlMainInfo._Consolidated == "0")
                $R_FN.openPrintWindow($R_ENUM$PrintObject.ProFormaInvoice, this._intSOID, true);
            else if (this._ctlMainInfo._Consolidated == "1")
                $R_FN.openPrintWindow($R_ENUM$PrintObject.ProFormaInvoiceCon, this._intSOID, true);
            else
                this._ctlMainInfo.showConfirmForm(3);
        }


        if (this._btnPrint._strExtraButtonClickCommand == "PrintSOReport") {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.SOReport, this._intSOID);
            if (this._ctlMainInfo._ibtnSentOrder) $R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder, false);
            this._ctlMainInfo.set_ctlIncludeSentOrder(true);
        }
        if (this._btnPrint._strExtraButtonClickCommand == "ConsolidatePrintSO") {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidatePrintSO, this._intSOID);
            if (this._ctlMainInfo._ibtnSentOrder) $R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder, false);
            this._ctlMainInfo.set_ctlIncludeSentOrder(true);
        }
        if (this._btnPrint._strExtraButtonClickCommand == "ConsolidateEmailSO") {
            $R_FN.openPrintWindow($R_ENUM$PrintObject.ConsolidateEmailSO, this._intSOID, true);
            if (this._ctlMainInfo._ibtnSentOrder) $R_IBTN.enableButton(this._ctlMainInfo._ibtnSentOrder, false);
            this._ctlMainInfo.set_ctlIncludeSentOrder(true);
        }
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intSOID, false, "SalesOrder");
        if (this._btnPrint._strExtraButtonClickCommand == "ExportToExcel") this.exportClicked();
        if (this._btnPrint._strExtraButtonClickCommand == "CustomTemplate") this.customTemplate();
    },
    customTemplate: function () {
        var _intQuoteID = this._ctlMainInfo.getFieldValue("hidFirstQuoteId");
        $R_FN.openCustomTemplateWindow($R_ENUM$PrintObject.Quote, _intQuoteID);
    },
    ctlMainInfo_GetDataComplete: function () {
        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
        //[004] start
        this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
        this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;
        //[004] end
        this._ctlSOShowPaymentFiles.getData();
        this.setLineDetailsFromMainInfo();
        //this.ctlSOEXCELDocFileDragDrop.getData();
    },

    ctlMainInfo_SaveEditComplete: function () {
        //[002] code start
        this._ctlLines._blnAS9120 = this._ctlMainInfo.getFieldValue("ctlAS9120");
        //[002] code end
        $R_IBTN.enableButton(this._ctlLines._ibtnLineWarehouse, false);
        this._ctlLines.getTabData();
    },

    setLineDetailsFromMainInfo: function () {
        var strSO = this._ctlMainInfo.getFieldValue("hidNo");
        var intCustomer = this._ctlMainInfo.getFieldValue("hidCustomerNo");
        var strCustomer = this._ctlMainInfo.getFieldValue("hidCustomer");
        var strCurrency = this._ctlMainInfo.getFieldValue("hidCurrencyCode");
        var intCurrency = this._ctlMainInfo.getFieldValue("hidCurrencyNo");
        var strDate = this._ctlMainInfo.getFieldValue("ctlDateOrdered");

        if (this._ctlAuthorisation) {
            this._ctlAuthorisation._blnCompanyOnStop = this._ctlMainInfo._blnCompanyOnStop;
            this._ctlAuthorisation._blnSOComplete = this._ctlMainInfo._blnSOComplete;
            this._ctlAuthorisation.enableAuthoriseButtons(true);
        }
        var intStatus = Number.parseInvariant(this._ctlMainInfo.getFieldValue("hidStatusNo").toString());
        if (this._ctlLines) {
            this._ctlLines._blnSOComplete = (intStatus == $R_ENUM$SalesOrderStatus.Complete);
            this._ctlLines._blnSOAuthorised = this._ctlMainInfo._blnIsAuthorised;
            this._ctlLines.enableEditButtons(true);
            if (this._ctlLines._frmEdit) this._ctlLines._frmEdit.setFieldsFromSalesOrder(strSO, strCustomer, strCurrency);
            if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromSalesOrder(strSO, strCustomer, strCurrency, intCurrency, strDate);
            if (this._ctlLines._frmPost) this._ctlLines._frmPost.setFieldsFromSalesOrder(strSO, strCustomer);
            if (this._ctlLines._frmDelete) this._ctlLines._frmDelete.setFieldsFromSalesOrder(strSO, strCustomer);
            if (this._ctlLines._frmDeallocate) this._ctlLines._frmDeallocate.setFieldsFromSalesOrder(strSO, strCustomer);
            if (this._ctlLines._frmAllocate) this._ctlLines._frmAllocate.setFieldsFromSalesOrder(strCurrency, intCurrency, strDate);
            if (this._ctlLines._frmClose) this._ctlLines._frmClose.setFieldsFromSalesOrder(strSO, strCustomer);
            if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setIncludeLockLotCustNo(intCustomer);
            if (this._ctlLines._frmEditAll) this._ctlLines._frmEditAll.setFieldsFromSalesOrder(strSO, strCustomer, strCurrency);

        }
        //[002] code start
        this._ctlLines._blnAS9120 = this._ctlMainInfo.getFieldValue("ctlAS9120");

        //[002] code end
    },

    ctlMainInfo_PotentialStatusChange: function () {
        //[002] code start

        this._ctlLines._blnAS9120 = this._ctlMainInfo.getFieldValue("ctlAS9120");
        //[002] code end
        this._ctlLines.getTabData();
        this._ctlAuthorisation.getData();
        this.getCreditBalance();


    },
    ctlMainInfo_ValidateChange: function () {
        if (this._ctlLines.validateTotalPrice()) {
            this._ctlMainInfo._intValid = true;
        } else {
            this._ctlMainInfo._intValid = false;
        }

    },
    ctlAuthorisation_PotentialStatusChange: function () {
        this._ctlMainInfo.getData();
        this._ctlLines.getTabData();
        this.getCreditBalance();
        if (this._ctlSORPDFDocsDragDrop) {
            this._ctlSORPDFDocsDragDrop._blnIsAuthorise = this._ctlAuthorisation._blnIsAuthorised;
        }

    },

    ctlLines_PotentialStatusChange: function () {
        this._ctlMainInfo.getData();
        this._ctlAuthorisation.getData();
        this.getCreditBalance();
        this.setMainInfoFromLine();


    },
    frmEditSaveClicked: function () {
        this._ctlMainInfo._frmEdit._IsFrieght = true;
        this.validateFrieght();
    },
    validateFrieght: function () {
        var newFreight = this.calculateFreight();
        if (newFreight >= (parseFloat(this._ctlAuthorisation.getFieldValue("hidCreditBalance")) + ((this._ctlLines._anyLinePosted) ? parseFloat(this._ctlLines._totalFreight) : 0))) {
            this._ctlMainInfo._frmEdit._IsFrieght = false;
        }
    },
    ctlAuthorisation_GetDataComplete: function () {
        this.getCreditBalance();
        if (this._ctlDragDropForSOR && this._ctlAuthorisation._blnIsAuthorised)
            this._ctlDragDropForSOR.getData();
        if (this._ctlSORPDFDocsDragDrop) {
            this._ctlSORPDFDocsDragDrop._blnIsAuthorise = this._ctlAuthorisation._blnIsAuthorised;
        }
        this._ctlLines.showHideAdd();
    },
    getCreditBalance: function () {
        if (this._ctlLines && this._ctlAuthorisation) {
            this._ctlLines.setCreditBalance((this._ctlAuthorisation.getFieldValue("hidCreditBalance") <= 0) ? 0 : this._ctlAuthorisation.getFieldValue("hidCreditBalance"));
            this._ctlMainInfo.setCreditBalance((this._ctlAuthorisation.getFieldValue("hidCreditBalance") <= 0) ? 0 : this._ctlAuthorisation.getFieldValue("hidCreditBalance"));

        }
    },

    calculateFreight: function () {
        return parseFloat(this._ctlMainInfo._frmEdit.getFieldValue("ctlFreight")) + (parseFloat(this._ctlMainInfo._frmEdit.getFieldValue("ctlFreight")) * parseFloat(this._ctlLines._taxRate));
    },
    setMainInfoFromLine: function () {

        if (this._ctlMainInfo)
            this._ctlMainInfo._blnLineContainData = this._ctlLines._blnContainLine;
        //  this._ctlMainInfo._intQuantityOrdered =this._ctlLines.getFieldValue("ctlQuantityOrdered");
        // this._ctlMainInfo._inthidPrice =this._ctlLines.getFieldValue("hidPrice");
    },
    ctlLines_RefreshChange: function () {
        this.setMainInfoFromLine();
        this._ctlMainInfo.disableCreateIPO(this._ctlLines._blnAllIPOCreated);

    },
    ctlSORPDFDocsDragDrop_GetDataComplete: function () {
        // alert("Hi =" + this._ctlSORPDFDocsDragDrop._intCountPDF);
        // alert("au");
        //alert(this._ctlSORPDFDocsDragDrop._intCountPDF);
        //        if (this._ctlAuthorisation) {
        //            if (this._ctlSORPDFDocsDragDrop._intCountPDF == 0) {
        //                $R_IBTN.enableButton(this._ctlAuthorisation._ibtnAuthorise, false);
        //            }
        //            else {
        //                $R_IBTN.enableButton(this._ctlAuthorisation._ibtnAuthorise, this._ctlAuthorisation._blnAllowAuthorise && !this._ctlAuthorisation._blnIsAuthorised && !this._ctlAuthorisation._blnCompanyOnStop && !this._ctlAuthorisation._blnSOComplete);
        //            }

        //        }
        if (this._ctlAuthorisation)
            this._ctlAuthorisation.getData();
    },
    ctlMainInfo_UpdateConsolidate: function () {

        if (this._ctlMainInfo._Consolidated == "1")
            $R_FN.setInnerHTML(this._lblSoStatus, "Consolidate lines");
        else if (this._ctlMainInfo._Consolidated == "0")
            $R_FN.setInnerHTML(this._lblSoStatus, "Actual lines");
        else
            $R_FN.setInnerHTML(this._lblSoStatus, "None");
    },
    //[006] start
    exportClicked: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/SOMainInfo");
        obj.set_DataObject("SOMainInfo");
        obj.set_DataAction("ExportSalesOrderReport");
        obj.addParameter("id", this._intSOID);
        obj._intTimeoutMilliseconds = 90 * 1000;
        obj.addDataOK(Function.createDelegate(this, this.exportComplete));
        obj.addError(Function.createDelegate(this, this.exportError));
        obj.addTimeout(Function.createDelegate(this, this.exportError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    exportError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    exportComplete: function (args) {
        if (args._result.Result == 1) {
            var dt = new Date();
            //location.href = String.format("{0}?t={1}", args._result.FileURL, dt.getTime());
            var IsFileExists = false;
            IsFileExists = this.UrlExists((window.location.origin + '/' + args._result.FileURL));
            if (IsFileExists == true) {
                location.href = String.format("{0}?t={1}", args._result.FileURL, dt.getTime());
            }
            dt = null;
            this._ctlMainInfo.getData();
        } else {
            //alert("Error");
        }
        //this.getDataOK_End();
    },
    //[006] end
    UrlExists: function (url) {
        var http = new XMLHttpRequest();
        http.open('HEAD', url, false);
        try {
            http.send();
        }
        catch (err) {
        }
        return http.status != 404;
    }

};
Rebound.GlobalTrader.Site.Pages.Orders.SODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.SODetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
