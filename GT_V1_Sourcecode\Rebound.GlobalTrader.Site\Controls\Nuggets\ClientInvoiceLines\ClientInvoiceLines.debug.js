///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[005]      Prakash           11/04/2014         Add Client Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines = function(element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.initializeBase(this, [element]);
    this._intLineID = -1;
    this._intLineCount = 0;
    this._blnLineLoaded = false;
    this._floatTotal = 0;
    this._aryAddedGoodsInLineIds = [];
    this._blnExported = false;
    this._intInvoiceClientNo = -1;
    this._isPOHUb = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.prototype = {

    get_intSIID: function() { return this._intSIID; }, set_intSIID: function(v) { if (this._intSIID !== v) this._intSIID = v; },
    get_intLineID: function() { return this._intLineID; }, set_intLineID: function(v) { if (this._intLineID !== v) this._intLineID = v; },
    get_intCompanyNo: function() { return this._intCompanyNo; }, set_intCompanyNo: function(v) { if (this._intCompanyNo !== v) this._intCompanyNo = v; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(v) { if (this._ibtnEdit !== v) this._ibtnAdd = v; },
    get_ibtnDelete: function() { return this._ibtnDelete; }, set_ibtnDelete: function(v) { if (this._ibtnDelete !== v) this._ibtnDelete = v; },
    get_tblAll: function() { return this._tblAll; }, set_tblAll: function(v) { if (this._tblAll !== v) this._tblAll = v; },
    get_pnlSummary: function() { return this._pnlSummary; }, set_pnlSummary: function(v) { if (this._pnlSummary !== v) this._pnlSummary = v; },
    get_lblTotal: function() { return this._lblTotal; }, set_lblTotal: function(v) { if (this._lblTotal !== v) this._lblTotal = v; },
    get_ibtnPrint: function() { return this._ibtnPrint; }, set_ibtnPrint: function(v) { if (this._ibtnPrint !== v) this._ibtnPrint = v; },
    //    addPotentialStatusChange: function(handler) { this.get_events().addHandler("PotentialStatusChange", handler); },
    //    removePotentialStatusChange: function(handler) { this.get_events().removeHandler("PotentialStatusChange", handler); },
    //    onPotentialStatusChange: function() {
    //        var handler = this.get_events().getHandler("PotentialStatusChange");
    //        if (handler) handler(this, Sys.EventArgs.Empty);
    //    },
get_blnPOHub: function() { return this._blnPOHub; }, set_blnPOHub: function(value) { if (this._blnPOHub !== value) this._blnPOHub = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.callBaseMethod(this, "initialize");

        //data
        this._strDataPath = "controls/Nuggets/ClientInvoiceLines";
        this._strDataObject = "ClientInvoiceLines";

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));

        //other controls
        this._tblAll.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));

        //add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        //delete form
        if (this._ibtnDelete) {
            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[1]);
            this._frmDelete.addCancel(Function.createDelegate(this, this.hideDeleteForm));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.deleteComplete));
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
        }
        //print packingSlip
        if (this._ibtnPrint) {
            $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.printPackingSlip));
        }
        // this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._hypPrev) $clearHandlers(this._hypPrev);
        if (this._hypNext) $clearHandlers(this._hypNext);
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
        if (this._frmAdd) this._frmAdd.dispose();
        this._frmAdd = null;
        this._ibtnAdd = null;
        this._ibtnDelete = null;
        this._tblAll = null;
        this._pnlSummary = null;
        this._lblTotal = null;
        this._intSIID = null;
        this._intLineID = null;
        this._blnLineLoaded = null;
        this._floatTotal = null;
        this._aryAddedGoodsInLineIds = null;
        this._intCompanyNo = null;
        this._blnExported = null;
        if (this._ibtnPrint) $R_IBTN.clearHandlers(this._ibtnPrint);
        this._ibtnPrint = null;
        this._blnPOHub = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.callBaseMethod(this, "dispose");
    },

    getData: function() {
        //alert(this._intSIID);
        this.enableButtons(false);
        this._blnLineLoaded = false;
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines");
        obj.addParameter("id", this._intSIID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function(args) {
       this.showLoading(false);
        this._tblAll.clearTable();
        var result = args._result;
        this._floatTotal = 0;
        Array.clear(this._aryAddedGoodsInLineIds);
        if (result.Lines) {
           
            for (var i = 0; i < result.Lines.length; i++) {
            
                var row = result.Lines[i];
                var aryData;
               if (this._blnPOHub == true) {
                 aryData = [
                      $RGT_nubButton_GoodsIn(row.GoodsInNo, row.GoodsInNumber)
                     ,$R_FN.writeDoubleCellValue($RGT_nubButton_PurchaseOrder(row.PONo, row.PO),$RGT_nubButton_InternalPurchaseOrder(row.IPONo, row.IPO))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.StockNo, row.Part, row.ROHS),
                      $R_FN.setCleanTextValue(row.SupplierPart))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Manufacturer), $R_FN.setCleanTextValue(row.DateCode))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.PackageName))
					//, $R_FN.writeDoubleCellValue(row.Notes, row.Pack)
					, $R_FN.setCleanTextValue(row.DateReceived)
					, $R_FN.writeDoubleCellValue(row.QtyReceived, row.UnitPrice)
					, row.LineTotal
				];
				}
				else
				{
				aryData = [
                      $RGT_nubButton_GoodsIn(row.GoodsInNo, row.GoodsInNumber)
                     ,$R_FN.writeDoubleCellValue(row.PO,$RGT_nubButton_InternalPurchaseOrder(row.IPONo, row.IPO))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.StockNo, row.Part, row.ROHS),
                     $R_FN.setCleanTextValue(row.SupplierPart))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Manufacturer), $R_FN.setCleanTextValue(row.DateCode))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.PackageName))
					//, $R_FN.writeDoubleCellValue(row.Notes, row.Pack)
					, $R_FN.setCleanTextValue(row.DateReceived)
					, $R_FN.writeDoubleCellValue(row.QtyReceived, row.UnitPrice)
					, row.LineTotal
				];
				}
                this._tblAll.addRow(aryData, row.ClientInvoiceLineId, row.ClientInvoiceLineId == this._intLineID);
                Array.add(this._aryAddedGoodsInLineIds, row.GoodsInLineNo);
                row = null;
            }
        }
        $R_FN.setInnerHTML(this._lblTotal, result.Total);
        this._floatTotal = result.TotalValue;
        this._isPOHUb = result.IsPOHub;
        this.showContent(true);
        this.showContentLoading(false);
        this._blnLineLoaded = true;
        this._tblAll.resizeColumns();
        this.enableButtons(true);
        if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, (result.Lines) && (result.Lines.length > 0) && (this._intLineID > 0) && (!this._blnExported));
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    enableButtons: function(bln) {
        if (bln) {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, this._blnLineLoaded && (!this._blnExported) && this._isPOHUb);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, this._blnLineLoaded && (this._intLineCount > 0) && (!this._blnExported) && this._isPOHUb);
        } else {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, false);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
        }
    },

    tbl_SelectedIndexChanged: function() {
        this._intLineID = this._tblAll._varSelectedValue;
        this._intLineCount = this._tblAll.countRows();
        //this._blnInspected = this._tblAll.getSelectedExtraData().Inspected;
        this.enableButtons(true);
    },

    showAddForm: function () {
       // alert(this._intInvoiceClientNo);
        this._frmAdd._intClientInvoiceID = this._intSIID;
        this._frmAdd._intCompanyID = this._intCompanyNo;
        this._frmAdd._floatLineTotal = this._floatTotal;
        // alert("siline=" + this._aryAddedGoodsInLineIds);
        this._frmAdd._aryAddedGoodsInLineIds = this._aryAddedGoodsInLineIds;
        this._frmAdd._intInvoiceClientNo = this._intInvoiceClientNo;
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function() {
        this.showForm(this._frmAdd, false);
        this._tblAll.resizeColumns();
    },

    saveAddComplete: function() {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
        //o this.onPotentialStatusChange();
    },

    showDeleteForm: function() {
        this._frmDelete._intLineID = this._intLineID;
        this.showForm(this._frmDelete, true);
    },

    hideDeleteForm: function() {
        this.showForm(this._frmDelete, false);
        this._tblAll.resizeColumns();
    },

    deleteComplete: function() {
        this.hideDeleteForm();
        this._intLineID = -1;
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    printPackingSlip: function() {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlip, this._intInvoiceID);
    }

};

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
