///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.prototype = {

	get_pnlItems: function() { return this._pnlItems; }, 	set_pnlItems: function(v) { if (this._pnlItems !== v)  this._pnlItems = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._pnlItems = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.callBaseMethod(this, "dispose");
	},
			
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlItems, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.callBaseMethod(this, "setupLoadingState");
	},

	getData: function() {
		this.setupLoadingState();
		$R_FN.setInnerHTML(this._pnlItems, "");
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/MyApprovedCustomersOnStop");
		obj.set_DataObject("MyApprovedCustomersOnStop");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addParameter("OtherLoginID", this._intLoginID_Other);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		var result = args._result;
		this.showNoneFoundOrContent(result.Count);
		for (var i = 0; i < result.Items.length; i++) {
			this.addHTMLContentItem($RGT_nubButton_Company(result.Items[i].CompanyID, result.Items[i].CompanyName), this._pnlItems);
		}
		$R_FN.showElement(this._pnlItems, result.Count > 0);
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyApprovedCustomersOnStop", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
