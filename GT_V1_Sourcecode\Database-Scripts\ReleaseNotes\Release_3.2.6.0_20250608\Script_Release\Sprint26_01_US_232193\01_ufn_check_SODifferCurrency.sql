﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-232193]		An.TranTan			15-Apr-2024		Create			Check SO currency differ with customer currency
===========================================================================================
*/
CREATE OR ALTER FUNCTION [dbo].[ufn_check_SODifferCurrency] (@SalesOrderNo INT)                                        
Returns BIT                                        
AS        
BEGIN    
	DECLARE @IsDiffer BIT = 0,
			@SOCurrencyNo INT = 0,
			@CompanyCurrencyNo INT = 0;

	SELECT @SOCurrencyNo = so.CurrencyNo
		,@CompanyCurrencyNo = ISNULL(c.SOCurrencyNo,0)
	FROM tbSalesOrder so WITH(NOLOCK)
	JOIN tbCompany c WITH(NOLOCK) on c.CompanyId = so.CompanyNo
	WHERE so.SalesOrderId = @SalesOrderNo;

	IF @SOCurrencyNo <> @CompanyCurrencyNo
		SET @IsDiffer = CAST(1 AS BIT)

	RETURN @IsDiffer;
END 
GO
