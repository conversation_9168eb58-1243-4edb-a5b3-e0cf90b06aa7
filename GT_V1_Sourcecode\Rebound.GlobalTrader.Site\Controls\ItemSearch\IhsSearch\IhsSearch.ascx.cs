using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
    public partial class IhsSearch : Base
    {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            SetItemSearchType("IhsSearch");
            AddScriptReference("Controls.ItemSearch.IhsSearch.IhsSearch.js");
		}

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.IhsSearch", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
            ctlDesignBase.MakeChildControls();
            
            ctlDesignBase.tblResults.AllowSelection = true;
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", "PartStatus"));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Manufacturer", "MSL", Unit.Pixel(40)));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("CountryOfOrigin", "HTCCode", Unit.Pixel(40)));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PackageMethod", "PackingCode", Unit.Pixel(40)));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Description", "ECCNCode",Unit.Pixel(200)));
            base.OnPreRender(e);
		}

       
	}
}