﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlAPIExternalLinksProvider : APIExternalLinksProvider
    {
        ////private const string APIExternalLinksURL = "https://api.futureelectronics.com/api/v1/pim-future/batch/lookup";
        //private string FutureElectronicsURL = Convert.ToString(ConfigurationManager.AppSettings["FutureElectronicsURL"]);
        //private string Xorbweaverlicensekey = Convert.ToString(ConfigurationManager.AppSettings["x-orbweaver-licensekey"]);
        //private string HostName = Convert.ToString(ConfigurationManager.AppSettings["HostName"]);

        public override List<ApiKeyValueDetails> APIExternalLinksDetails(string sourcingName, string moduleName, int clientId, bool hasServerLocal)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            List<ApiKeyValueDetails> lst = new List<ApiKeyValueDetails>();
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetAPIDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@sourcingName", SqlDbType.VarChar).Value = sourcingName;
                cmd.Parameters.Add("@moduleName", SqlDbType.VarChar).Value = moduleName;
                cmd.Parameters.Add("@clientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                SqlDataReader reader = cmd.ExecuteReader();
                while (reader.Read())
                {
                    ApiKeyValueDetails obj = new ApiKeyValueDetails();
                    obj.ApiURLKeyId = GetReaderValue_Int32(reader, "ApiURLKeyId", 0);
                    obj.URL = GetReaderValue_String(reader, "URL", null);
                    obj.Licensekey = GetReaderValue_String(reader, "Licensekey", null);
                    obj.HostName = GetReaderValue_String(reader, "HostName", null);
                    obj.ApiShortName = GetReaderValue_String(reader, "ApiShortName", null);
                    obj.ApiName = GetReaderValue_String(reader, "ApiName", null);
                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Epo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
            return lst;
        }

        public override string APIExternalLinksOffers(string ApiURL, string HostName, string Xorbweaverlicensekey, string partSearch)
        {
            string DATA = "{" + '"' + "parts" + '"' + ':' + '[' + '"' + partSearch.Remove(partSearch.Length - 1, 1) + '"' + ']' + "}";

            System.Net.HttpWebRequest request = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(ApiURL);
            request.Method = "POST";
            request.ContentType = "application/json";
            request.Accept = "application/json,text/javascript";
            request.Host = HostName;        //"api.orbweaver.com"
            request.Headers.Add("x-orbweaver-licensekey", Xorbweaverlicensekey);        //"IA7VB-DAUOL-2QAWQ-FGVHF-53SQK"
            request.ContentLength = DATA.Length;

            using (System.IO.Stream webStream = request.GetRequestStream())
            using (System.IO.StreamWriter requestWriter = new System.IO.StreamWriter(webStream, System.Text.Encoding.ASCII))
            {
                requestWriter.Write(DATA);
            }

            try
            {
                System.Net.WebResponse webResponse = request.GetResponse();
                using (System.IO.Stream webStream = webResponse.GetResponseStream() ?? System.IO.Stream.Null)
                using (System.IO.StreamReader responseReader = new System.IO.StreamReader(webStream))
                {
                    string response = responseReader.ReadToEnd();
                    //Console.Out.WriteLine(response);
                    return response;
                }
               // return "";
            }
            catch (Exception e)
            {
                Console.Out.WriteLine("-----------------");
                Console.Out.WriteLine(e.Message);
            }
            return "";
        }

        public override List<APIExternalLinksDetails> ApiNotRespond(int? clientId, string partSearch, bool hasServerLocal, string SourcingName, string ApiName)
        {
            //SqlConnection cn = null;
            //SqlCommand cmd = null;
            try
            {
                //if (!hasServerLocal)
                //    cn = new SqlConnection(this.GTConnectionString);
                //else
                //    cn = new SqlConnection(this.ConnectionString);

                //cmd = new SqlCommand("usp_source_FutureElectronics", cn);
                //cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 120;
                //cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                //cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                //cn.Open();
                //SqlDataReader reader = cmd.ExecuteReader();                

                List<APIExternalLinksDetails> lst = new List<APIExternalLinksDetails>();
                //while (reader.Read())
                //{
                APIExternalLinksDetails obj = new APIExternalLinksDetails();
                obj.FElectronicsId = 0;// GetReaderValue_Int32(reader, "FElectronicsId", 0);
                obj.FullPart = ApiName; //GetReaderValue_String(ApiName, "FullPart", "");
                obj.ManufacturerNo = null;// GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                obj.ProductNo = null;// GetReaderValue_NullableInt32(reader, "ProductNo", null);
                obj.PackageNo = null;// GetReaderValue_NullableInt32(reader, "PackageNo", null);
                obj.Price = 0;//GetReaderValue_Double(reader, "Price", 0);
                obj.OriginalEntryDate = null;// GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                obj.Salesman = null;// GetReaderValue_NullableInt32(reader, "Salesman", null);
                obj.SupplierNo = 0;// GetReaderValue_Int32(reader, "SupplierNo", 0);
                obj.CurrencyNo = null;// GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                obj.ROHS = 0;// GetReaderValue_Byte(reader, "ROHS", (byte)0);
                obj.UpdatedBy = null;// GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                obj.DLUP = DateTime.MinValue;// GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                obj.Notes = null;// GetReaderValue_String(reader, "Notes", "");
                obj.PackageName = null;// GetReaderValue_String(reader, "PackageName", "");
                obj.ClientNo = null;// GetReaderValue_NullableInt32(reader, "ClientNo", null);
                obj.ManufacturerCode = null;// GetReaderValue_String(reader, "ManufacturerCode", "");
                obj.CurrencyCode = null;// GetReaderValue_String(reader, "CurrencyCode", "");
                obj.CurrencyDescription = null;// GetReaderValue_String(reader, "CurrencyDescription", "");
                obj.SupplierEmail = null;// GetReaderValue_String(reader, "SupplierEmail", "");
                obj.SalesmanName = null;// GetReaderValue_String(reader, "SalesmanName", "");
                obj.EpoStatusChangeEmployeeName = null;// GetReaderValue_String(reader, "EpoStatusChangeEmployeeName", "");
                obj.ClientId = 0;// GetReaderValue_Int32(reader, "ClientId", 0);
                obj.ClientName = null;// GetReaderValue_String(reader, "ClientName", "");
                obj.ClientCode = null;// GetReaderValue_String(reader, "ClientCode", "");
                obj.MSL = null;// GetReaderValue_String(reader, "MSL", "");
                obj.SPQ = null;// GetReaderValue_String(reader, "SPQ", "");
                obj.RoHSStatus = null;// GetReaderValue_String(reader, "RoHSStatus", "");
                obj.SupplierTotalQSA = null;// GetReaderValue_String(reader, "SupplierTotalQSA", "");
                obj.SupplierLTB = null;// GetReaderValue_String(reader, "SupplierLTB", "");
                obj.IsSourcingHub = false;// GetReaderValue_Boolean(reader, "ishub", false);
                obj.UpliftPrice = 0;// GetReaderValue_Double(reader, "UpliftPrice", 0);
                obj.SupplierEpo = null;// GetReaderValue_String(reader, "SupplierEpo", "");
                obj.VirtualCostPrice = null;// GetReaderValue_String(reader, "VirtualCostPrice", "");
                obj.Quantity = 0;// GetReaderValue_Int32(reader, "Quantity", 0);
                obj.Part = null;// GetReaderValue_String(reader, "Part", "");
                obj.GTDate = null;// GetReaderValue_NullableDateTime(reader, "GTDate", null);
                obj.Description = null;// GetReaderValue_String(reader, "Description", "");
                obj.ManufacturerName = null;// GetReaderValue_String(reader, "ManufacturerName", "");
                obj.DateCode = null;// GetReaderValue_String(reader, "DateCode", "");
                                    //[001] code start
                obj.SupplierName = null;//GetReaderValue_String(reader, "SupplierName", "");
                obj.SupplierType = null;// GetReaderValue_String(reader, "SupplierType", "");
                                        //[001] code end
                obj.Reference = null;// GetReaderValue_String(reader, "Reference", "");
                obj.ProductName = null;// GetReaderValue_String(reader, "ProductName", "");
                obj.SupplierPackageType = null;// GetReaderValue_String(reader, "PackageType", "");
                obj.ECCN = null;// GetReaderValue_String(reader, "ECCN", "");
                obj.PublishDate = null;// GetReaderValue_NullableDateTime(reader, "PublishDate", null);
                obj.UnitCostPrice = null;// GetReaderValue_String(reader, "UnitCostPrice", "");
                obj.SupplierMOQ = null;// GetReaderValue_String(reader, "MOQ", "");
                lst.Add(obj);
                obj = null;
                //}
                //reader.NextResult();
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Epo", sqlex);
            }
            finally
            {
                //cmd.Dispose();
                //cn.Close();
                //cn.Dispose();
            }
        }

        public override int InsertMultipleAPIJson(string result, string ApiName, int clientId, bool hasServerLocal)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            if (!hasServerLocal)
                cn = new SqlConnection(this.GTConnectionString);
            else
                cn = new SqlConnection(this.ConnectionString);

            cn = new SqlConnection(this.ConnectionString);
            //cmd = new SqlCommand("usp_source_Stock", cn);
            //cmd = new SqlCommand("usp_InsertFELookupParts_ApiJson", cn);
            cmd = new SqlCommand("usp_HandleMultileApiJson", cn);
            cmd.CommandType = CommandType.StoredProcedure;
            cmd.Parameters.Add("@ResultJson", SqlDbType.NVarChar).Value = result;
            //cmd.Parameters.Add("@ApiName", SqlDbType.VarChar).Value = ApiName;
            cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
            cn.Open();
            int ret = ExecuteNonQuery(cmd);
            if (ret > 0)
                return ret;
            return 0;
        }

        public override int InsertMultipleAPIJson(string result, string ApiName, int clientId, bool hasServerLocal, string ApiShortName)
        {
            throw new NotImplementedException();
        }

        public override List<APIExternalLinksDetails> Source1(System.Int32? clientId, System.String partSearch, bool hasServerLocal, string SourcingName, int ApiURLKeyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            //outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                //cmd = new SqlCommand("usp_source_FutureElectronics", cn);
                cmd = new SqlCommand("Usp_GetSupplierAPIData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@ApiURLKeyId", SqlDbType.Int).Value = ApiURLKeyId;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();

                List<APIExternalLinksDetails> lst = new List<APIExternalLinksDetails>();
                while (reader.Read())
                {
                    APIExternalLinksDetails obj = new APIExternalLinksDetails();
                    obj.FElectronicsId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //obj.EpoStatusNo = GetReaderValue_NullableInt32(reader, "EpoStatusNo", null);
                    //obj.EpoStatusChangeDate = GetReaderValue_NullableDateTime(reader, "EpoStatusChangeDate", null);
                    //obj.EpoStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "EpoStatusChangeLoginNo", null);                   
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.EpoStatusChangeEmployeeName = GetReaderValue_String(reader, "EpoStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    //obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);                   
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    //obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    //obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    //obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierEpo = GetReaderValue_String(reader, "SupplierEpo", "");
                    obj.VirtualCostPrice = GetReaderValue_String(reader, "VirtualCostPrice", "");

                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);

                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.GTDate = GetReaderValue_NullableDateTime(reader, "GTDate", null);
                    obj.Description = GetReaderValue_String(reader, "Description", "");

                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");

                    //[001] code start
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");

                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "PackageType", "");
                    obj.ECCN = GetReaderValue_String(reader, "ECCN", "");

                    //obj.Suppliertype = GetReaderValue_String(reader, "Suppliertype", "");
                    obj.PublishDate = GetReaderValue_NullableDateTime(reader, "PublishDate", null);
                    obj.UnitCostPrice = GetReaderValue_String(reader, "UnitCostPrice", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "MOQ", "");
                    obj.ApiSourceName = GetReaderValue_String(reader, "ApiSourceName", "");

                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Epo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<APIExternalLinksDetails> Source(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, string ApiName, int ApiURLKeyId)
        {
            throw new NotImplementedException();
        }

        public override List<APIExternalLinksDetails> Source(string result, int? clientId, string partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId, System.Int32? UserId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                if (!IsServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                //cmd = new SqlCommand("usp_source_FutureElectronics", cn);
                //cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 120;
                //cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                //cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                //cn.Open();
                ////DbDataReader reader = ExecuteReader(cmd);
                //SqlDataReader reader = cmd.ExecuteReader();

                cn = new SqlConnection(this.ConnectionString);
                //cmd = new SqlCommand("usp_source_Stock", cn);
                //cmd = new SqlCommand("usp_InsertFELookupParts_ApiJson", cn);
                cmd = new SqlCommand("usp_HandleMultileApiJson", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ResultJson", SqlDbType.NVarChar).Value = result;
                //cmd.Parameters.Add("@ApiName", SqlDbType.VarChar).Value = ApiName;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@ApiURLKeyId", SqlDbType.Int).Value = ApiURLKeyId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = UserId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);

                List<APIExternalLinksDetails> lst = new List<APIExternalLinksDetails>();
                while (reader.Read())
                {
                    APIExternalLinksDetails obj = new APIExternalLinksDetails();
                    obj.FElectronicsId = GetReaderValue_Int32(reader, "SupplierAPIId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //obj.EpoStatusNo = GetReaderValue_NullableInt32(reader, "EpoStatusNo", null);
                    //obj.EpoStatusChangeDate = GetReaderValue_NullableDateTime(reader, "EpoStatusChangeDate", null);
                    //obj.EpoStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "EpoStatusChangeLoginNo", null);                   
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.EpoStatusChangeEmployeeName = GetReaderValue_String(reader, "EpoStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    //obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);                   
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    //obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    //obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    //obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierEpo = GetReaderValue_String(reader, "SupplierEpo", "");
                    obj.VirtualCostPrice = GetReaderValue_String(reader, "VirtualCostPrice", "");

                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);

                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.GTDate = GetReaderValue_NullableDateTime(reader, "GTDate", null);
                    obj.Description = GetReaderValue_String(reader, "Description", "");

                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");

                    //[001] code start
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");

                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "PackageType", "");
                    obj.ECCN = GetReaderValue_String(reader, "ECCN", "");

                    //obj.Suppliertype = GetReaderValue_String(reader, "Suppliertype", "");
                    obj.PublishDate = GetReaderValue_NullableDateTime(reader, "PublishDate", null);
                    obj.UnitCostPrice = GetReaderValue_String(reader, "UnitCostPrice", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "MOQ", "");

                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get FEApi", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<APIExternalLinksDetails> SourceMultipleParts(string result, int? clientId, string partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                if (!IsServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                //cmd = new SqlCommand("usp_source_FutureElectronics", cn);
                //cmd.CommandType = CommandType.StoredProcedure;
                //cmd.CommandTimeout = 120;
                //cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                //cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                //cn.Open();
                ////DbDataReader reader = ExecuteReader(cmd);
                //SqlDataReader reader = cmd.ExecuteReader();

                cn = new SqlConnection(this.ConnectionString);
                //cmd = new SqlCommand("usp_source_Stock", cn);
                //cmd = new SqlCommand("usp_InsertFELookupParts_ApiJson", cn);
                cmd = new SqlCommand("usp_HandleMultileApiJsonMultipleParts", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ResultJson", SqlDbType.NVarChar).Value = result;
                //cmd.Parameters.Add("@ApiName", SqlDbType.VarChar).Value = ApiName;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch.Substring(1, partSearch.Length - 2);
                cmd.Parameters.Add("@ApiURLKeyId", SqlDbType.Int).Value = ApiURLKeyId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);

                List<APIExternalLinksDetails> lst = new List<APIExternalLinksDetails>();
                while (reader.Read())
                {
                    APIExternalLinksDetails obj = new APIExternalLinksDetails();
                    obj.FElectronicsId = GetReaderValue_Int32(reader, "FElectronicsId", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //obj.EpoStatusNo = GetReaderValue_NullableInt32(reader, "EpoStatusNo", null);
                    //obj.EpoStatusChangeDate = GetReaderValue_NullableDateTime(reader, "EpoStatusChangeDate", null);
                    //obj.EpoStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "EpoStatusChangeLoginNo", null);                   
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.EpoStatusChangeEmployeeName = GetReaderValue_String(reader, "EpoStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    //obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);                   
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    //obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    //obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    //obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierEpo = GetReaderValue_String(reader, "SupplierEpo", "");
                    obj.VirtualCostPrice = GetReaderValue_String(reader, "VirtualCostPrice", "");

                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);

                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.GTDate = GetReaderValue_NullableDateTime(reader, "GTDate", null);
                    obj.Description = GetReaderValue_String(reader, "Description", "");

                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");

                    //[001] code start
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");

                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "PackageType", "");
                    obj.ECCN = GetReaderValue_String(reader, "ECCN", "");

                    //obj.Suppliertype = GetReaderValue_String(reader, "Suppliertype", "");
                    obj.PublishDate = GetReaderValue_NullableDateTime(reader, "PublishDate", null);
                    obj.UnitCostPrice = GetReaderValue_String(reader, "UnitCostPrice", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "MOQ", "");

                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get FEApi", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<LyticaAPI> InsretLyticaAPI(string APIResponseJson, int? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                
                cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("usp_InsertLyticaAPI", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@JSON", SqlDbType.NVarChar).Value = APIResponseJson;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);

                List<LyticaAPI> lst = new List<LyticaAPI>();
                while (reader.Read())
                {
                    LyticaAPI obj = new LyticaAPI();
                    obj.LyticaAPIId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
                    obj.Commodity = GetReaderValue_String(reader, "Commodity", "");
                    obj.OriginalPartSearched = GetReaderValue_String(reader, "OriginalPartSearched", "");
                    obj.Manufacturer = GetReaderValue_String(reader, "Manufacturer", "");
                    obj.AveragePrice = GetReaderValue_NullableDouble(reader, "AveragePrice", 0);
                    obj.TargetPrice = GetReaderValue_NullableDouble(reader, "TargetPrice", 0);
                    obj.MarketLeading = GetReaderValue_NullableDouble(reader, "MarketLeading", 0);
                    obj.LifeCycle = GetReaderValue_String(reader, "LifeCycle", "");
                    obj.lifeCycleStatus = GetReaderValue_String(reader, "lifeCycleStatus", "");
                    obj.OverAllRisk = GetReaderValue_String(reader, "OverAllRisk", "");
                    obj.PartBreadth = GetReaderValue_String(reader, "PartBreadth", "");
                    obj.ManufacturerBreadth = GetReaderValue_String(reader, "ManufacturerBreadth", "");
                    obj.DueDiligence = GetReaderValue_String(reader, "DueDiligence", "");
                    obj.PartConcentration = GetReaderValue_String(reader, "PartConcentration", "");
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Insert Lytica API Data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<APIExternalLinksDetails> SourceFromDBMultipleParts(System.Int32? clientId, System.String partSearch, bool hasServerLocal, string SourcingName, int ApiURLKeyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            //outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                cmd = new SqlCommand("Usp_GetSupplierAPIData_MultipleParts", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch.Substring(1, partSearch.Length - 2);
                cmd.Parameters.Add("@ApiURLKeyId", SqlDbType.Int).Value = ApiURLKeyId;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();

                List<APIExternalLinksDetails> lst = new List<APIExternalLinksDetails>();
                while (reader.Read())
                {
                    APIExternalLinksDetails obj = new APIExternalLinksDetails();
                    obj.FElectronicsId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //obj.EpoStatusNo = GetReaderValue_NullableInt32(reader, "EpoStatusNo", null);
                    //obj.EpoStatusChangeDate = GetReaderValue_NullableDateTime(reader, "EpoStatusChangeDate", null);
                    //obj.EpoStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "EpoStatusChangeLoginNo", null);                   
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.EpoStatusChangeEmployeeName = GetReaderValue_String(reader, "EpoStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    //obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);                   
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    //obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    //obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    //obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierEpo = GetReaderValue_String(reader, "SupplierEpo", "");
                    obj.VirtualCostPrice = GetReaderValue_String(reader, "VirtualCostPrice", "");

                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);

                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.GTDate = GetReaderValue_NullableDateTime(reader, "GTDate", null);
                    obj.Description = GetReaderValue_String(reader, "Description", "");

                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");

                    //[001] code start
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");

                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "PackageType", "");
                    obj.ECCN = GetReaderValue_String(reader, "ECCN", "");

                    //obj.Suppliertype = GetReaderValue_String(reader, "Suppliertype", "");
                    obj.PublishDate = GetReaderValue_NullableDateTime(reader, "PublishDate", null);
                    obj.UnitCostPrice = GetReaderValue_String(reader, "UnitCostPrice", "");
                    obj.CurrencyCode = GetReaderValue_String(reader,"CurrencyCode","");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "MOQ", "");
                    obj.ApiSourceName = GetReaderValue_String(reader, "ApiSourceName", "");

                    lst.Add(obj);
                    obj = null;
                }
                //reader.NextResult();
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Epo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<APIExternalLinksDetails> GetDigiKeyApi(System.Int32? clientId, System.String partSearch, bool hasServerLocal, string SourcingName, int ApiURLKeyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            //outDate = null;
            try
            {
                if (!hasServerLocal)
                    cn = new SqlConnection(this.GTConnectionString);
                else
                    cn = new SqlConnection(this.ConnectionString);

                //cmd = new SqlCommand("usp_source_FutureElectronics", cn);
                cmd = new SqlCommand("Usp_GetDigiKeyAPIData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@ApiURLKeyId", SqlDbType.Int).Value = ApiURLKeyId;
                cn.Open();
                //DbDataReader reader = ExecuteReader(cmd);
                SqlDataReader reader = cmd.ExecuteReader();

                List<APIExternalLinksDetails> lst = new List<APIExternalLinksDetails>();
                while (reader.Read())
                {
                    APIExternalLinksDetails obj = new APIExternalLinksDetails();
                    obj.FElectronicsId = GetReaderValue_Int32(reader, "SupplierAPIID", 0);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.Salesman = GetReaderValue_NullableInt32(reader, "Salesman", null);
                    obj.SupplierNo = GetReaderValue_Int32(reader, "SupplierNo", 0);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //obj.EpoStatusNo = GetReaderValue_NullableInt32(reader, "EpoStatusNo", null);
                    //obj.EpoStatusChangeDate = GetReaderValue_NullableDateTime(reader, "EpoStatusChangeDate", null);
                    //obj.EpoStatusChangeLoginNo = GetReaderValue_NullableInt32(reader, "EpoStatusChangeLoginNo", null);                   
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.SupplierEmail = GetReaderValue_String(reader, "SupplierEmail", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.EpoStatusChangeEmployeeName = GetReaderValue_String(reader, "EpoStatusChangeEmployeeName", "");
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    //obj.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null);                   
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.SPQ = GetReaderValue_String(reader, "SPQ", "");
                    //obj.LeadTime = GetReaderValue_String(reader, "LeadTime", "");
                    obj.RoHSStatus = GetReaderValue_String(reader, "RoHSStatus", "");
                    //obj.FactorySealed = GetReaderValue_String(reader, "FactorySealed", "");
                    //obj.IPOBOMNo = GetReaderValue_Int32(reader, "IPOBOMNo", 0);
                    obj.SupplierTotalQSA = GetReaderValue_String(reader, "SupplierTotalQSA", "");
                    obj.SupplierLTB = GetReaderValue_String(reader, "SupplierLTB", "");
                    obj.IsSourcingHub = GetReaderValue_Boolean(reader, "ishub", false);
                    obj.UpliftPrice = GetReaderValue_Double(reader, "UpliftPrice", 0);
                    obj.SupplierEpo = GetReaderValue_String(reader, "SupplierEpo", "");
                    obj.VirtualCostPrice = GetReaderValue_String(reader, "VirtualCostPrice", "");

                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);

                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.GTDate = GetReaderValue_NullableDateTime(reader, "GTDate", null);
                    obj.Description = GetReaderValue_String(reader, "Description", "");

                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");

                    //[001] code start
                    obj.SupplierName = GetReaderValue_String(reader, "SupplierName", "");
                    obj.SupplierType = GetReaderValue_String(reader, "SupplierType", "");
                    //[001] code end
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");

                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.SupplierPackageType = GetReaderValue_String(reader, "PackageType", "");
                    obj.ECCN = GetReaderValue_String(reader, "ECCN", "");

                    //obj.Suppliertype = GetReaderValue_String(reader, "Suppliertype", "");
                    obj.PublishDate = GetReaderValue_NullableDateTime(reader, "PublishDate", null);
                    obj.UnitCostPrice = GetReaderValue_String(reader, "UnitCostPrice", "");
                    obj.SupplierMOQ = GetReaderValue_String(reader, "MOQ", "");
                    obj.ApiSourceName = GetReaderValue_String(reader, "ApiSourceName", "");

                    lst.Add(obj);
                    obj = null;
                }
                reader.NextResult();
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Epo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //public static string APIfutureelectronicsOffers(System.String sourcingName, System.String moduleName, int clientId, bool hasServerLocal)
        //{
        //    List<ApiKeyValueDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.FutureElectronics.APIExternalLinksDetails(sourcingName, moduleName, clientId, hasServerLocal);
        //    if (lstDetails == null)
        //    {
        //        return new List<ApiKeyDetails>();
        //    }
        //    else
        //    {
        //        List<ApiKeyDetails> lst = new List<ApiKeyDetails>();
        //        foreach (ApiKeyValueDetails objDetails in lstDetails)
        //        {
        //            ApiKeyDetails obj = new ApiKeyDetails();
        //            obj.URL = objDetails.URL;
        //            obj.HostName = objDetails.HostName;
        //            obj.Licensekey = objDetails.Licensekey;
        //            obj.ApiShortName = objDetails.ApiShortName;
        //            lst.Add(obj);
        //            obj = null;
        //        }
        //        lstDetails = null;
        //        return lst;
        //        //return lstDetails;
        //    }
        //    //return null;
        //}

        //public override string APIfutureelectronicsOffers(string partSearch)
        //{
        //    string DATA = "{" + '"' + "parts" + '"' + ':' + '[' + '"' + partSearch.Remove(partSearch.Length - 1, 1) + '"' + ']' + "}";

        //    System.Net.HttpWebRequest request = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(FutureElectronicsURL);
        //    request.Method = "POST";
        //    request.ContentType = "application/json";
        //    request.Accept = "application/json,text/javascript";
        //    request.Host = HostName;        //"api.orbweaver.com"
        //    request.Headers.Add("x-orbweaver-licensekey", Xorbweaverlicensekey);        //"IA7VB-DAUOL-2QAWQ-FGVHF-53SQK"
        //    request.ContentLength = DATA.Length;

        //    using (System.IO.Stream webStream = request.GetRequestStream())
        //    using (System.IO.StreamWriter requestWriter = new System.IO.StreamWriter(webStream, System.Text.Encoding.ASCII))
        //    {
        //        requestWriter.Write(DATA);
        //    }

        //    try
        //    {
        //        System.Net.WebResponse webResponse = request.GetResponse();
        //        using (System.IO.Stream webStream = webResponse.GetResponseStream() ?? System.IO.Stream.Null)
        //        using (System.IO.StreamReader responseReader = new System.IO.StreamReader(webStream))
        //        {
        //            string response = responseReader.ReadToEnd();
        //            //Console.Out.WriteLine(response);
        //            return response;
        //        }
        //        return "";
        //    }
        //    catch (Exception e)
        //    {
        //        Console.Out.WriteLine("-----------------");
        //        Console.Out.WriteLine(e.Message);
        //    }
        //    return "";
        //}
    }
}
