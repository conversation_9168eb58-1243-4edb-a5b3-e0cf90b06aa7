﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-223343]	NgaiTo		 		13-Dec-2024		UPDATE			Bug 223343: Setting - Update Selection query and @UpdatedBy in SP Login/ Setting
===========================================================================================
*/
   
CREATE OR ALTER PROCEDURE [dbo].[usp_select_Login]             
@LoginId int          
AS        
     
SELECT a.[LoginId],
	a.[ClientNo],
	a.[LoginName],
	a.[EmployeeName],
	a.[EmployeePassword],
	a.[FirstName],
	a.[LastName],
	a.[AddressNo],
	a.[Title],
	a.[Extension],
	a.[Telephone],
	a.[Fax],
	a.[EMail],
	a.[Notes],
	a.[HomeTelephone],
	a.[HomeFax],
	a.[HomeEmail],
	a.[Mobile],
	a.[DivisionNo],
	a.[TeamNo],
	a.[Salutation],
	a.[Inactive],
	a.[UpdatedBy],
	a.[DLUP],
	a.[KeyLogin],
	a.[ADLogin],
	a.[YearlyTarget],
	a.[GroupAccess],
	a.[PowerBIUser],
	a.[PowerBIPass],
	a.[MYpassword],
	a.[RefID109],
	a.[RefID109AddressNo],
	a.[RefID109DivisionNo],
	a.[RefID109TeamNo],
	a.[PassHash],
	a.[MasterLoginNo],
	a.[RefIdHK],
	a.[RefIdHKAddressNo],
	a.[RefIdHKDivisionNo],
	a.[RefIdHKTeamNo],
	a.[ADEmail],
	a.[IsKPISuperAdmin],
	c.DivisionName,
	b.TeamName,
	prf.PrinterNo,
	pr.PrinterName,
	prf.LabelPathNo,
	lp.LabelFullPath
FROM  dbo.tbLogin a        
Left Join dbo.tbTeam b On b.TeamId = a.TeamNo        
Left Join dbo.tbDivision c On c.DivisionId = a.DivisionNo        
Left Join dbo.tbLoginPreference prf On prf.LoginNo = a.LoginId      
Left Join dbo.tbPrinter pr ON pr.PrinterId = prf.PrinterNo      
Left Join dbo.tbLabelPath lp ON lp.LabelPathId = prf.LabelPathNo      
WHERE LoginId = @LoginId 


GO


