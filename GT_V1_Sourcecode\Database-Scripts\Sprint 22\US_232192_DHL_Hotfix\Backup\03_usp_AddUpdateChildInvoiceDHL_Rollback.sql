﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_AddUpdateChildInvoiceDHL]                                           
(                                              
  @StatementId Int = 0,                                            
  @AWBNo bigint,                  
  @ParentID varchar(150),                  
  @ChildID bigint=0,                  
  @ChildLabel varchar(500),                  
  @InvoiceNo bigint=0,                 
  @ParentInvoiceAWBDHLId int=0,                  
  @PInvoiceNo varchar(500)=null,                  
  @InvoiceFile varchar(500)=null,                 
  @InvoiceLabel varchar(500)=null,                
  @BBXJson nvarchar(max)=null,                 
  @CurrencyCode varchar(10),                  
  @ClientId int=0,               
  @LoginId int=0,       
  @ShipperNo varchar(50)=null,      
  @Flag bit=0,              
  @Message varchar(500) OUTPUT,                      
  @IsValid bit OUTPUT,                    
  @ReturnId int OUTPUT,
  @Weight Decimal = 0
)                                   
As    
--Created By: <PERSON><PERSON><PERSON> <PERSON>                                           
--Created On: 07 Oct 2022                         
--Purpose: To add/update child invoice on DHL                                            
BEGIN                          
   IF @StatementId > 0                          
     BEGIN                             
       IF EXISTS(SELECT 'X' FROM ChildInvoiceAWBDHL WHERE ParentID = @ParentID)                        
        BEGIN                      
           UPDATE ChildInvoiceAWBDHL SET                                   
           PAWBNo=@AWBNo,                
           ParentInvoiceAWBDHLId=@ParentInvoiceAWBDHLId,                
           InvoiceFile=@InvoiceFile,                
           InvoiceLabel=@InvoiceLabel,             
           WPXJson=@BBXJson,            
           IsActive = 1,
		   Weight = @Weight,
           Updated = CURRENT_TIMESTAMP                                         
           WHERE ParentID = @ParentID                           
                                    
           SET @Message = 'Declare statement has been added successfully.'                          
           SET @IsValid = 1                      
           SET @ReturnId =  @ParentInvoiceAWBDHLId                  
       END                               
     END                                    
    ELSE                                  
     BEGIN                   
      IF Not EXISTS(SELECT 'X' FROM ChildInvoiceAWBDHL WHERE AWBNo = @AWBNo and IsActive=1)                        
       BEGIN                
        if EXISTS(SELECT 'X' FROM ChildInvoiceAWBDHL WHERE InvoiceNo=@InvoiceNo and IsActive=0 and isnull(SendStatus,0)=1)              
          Begin              
            update ChildInvoiceAWBDHL set SendStatus=0 where InvoiceNo = @InvoiceNo and SendStatus=1            
                      
            update t set t.AirWayBill=Null, t.IsUpsInvoiceExported=0                  
            from tbInvoice t                  
            join ChildInvoiceAWBDHL ci on ci.InvoiceNo=t.InvoiceNumber and t.ClientNo=ci.ClientId and ci.LoginId=@LoginId               
            where ci.SendStatus=1           
          End           
     if ((SELECT top 1 isnull(IsUpsInvoiceExported,0) FROM tbInvoice WHERE InvoiceNumber=@InvoiceNo)=0)          
      Begin     
   if EXISTS(SELECT 'X' FROM ChildInvoiceAWBDHL WHERE InvoiceNo=@InvoiceNo and isnull(SendStatus,0)=1)              
           Begin     
    update ChildInvoiceAWBDHL set SendStatus=0, IsActive=0  where InvoiceNo = @InvoiceNo and SendStatus=1    
     End    
    
          INSERT INTO ChildInvoiceAWBDHL(AWBNo,ParentID,ChildID,ChildLabel,InvoiceNo,BBXJson,ClientId,CurrencyCode,LoginId,shipperno,Weight)                                            
          VALUES(@AWBNo,@ParentID,@ChildID,@ChildLabel,@InvoiceNo,@BBXJson,@ClientId,@CurrencyCode,@LoginId,@ShipperNo,@Weight)        
            
          update t set t.AirWayBill=ci.AWBNo, t.IsUpsInvoiceExported=1                  
          from tbInvoice t                  
          join ChildInvoiceAWBDHL ci on ci.InvoiceNo=t.InvoiceNumber and ci.ParentID=@ParentID and t.ClientNo=ci.ClientId                
          where ci.SendStatus=1           
                           
          SET @Message = 'Declare statement has been added successfully.'                          
          SET @IsValid = 1                      
          SET @ReturnId = SCOPE_IDENTITY()           
      End          
   End           
  else       
   Begin          
   SET @Message = 'Already processed.'                          
   SET @IsValid = 0                      
   SET @ReturnId = 0          
   End                                          
    END                
END        
GO


