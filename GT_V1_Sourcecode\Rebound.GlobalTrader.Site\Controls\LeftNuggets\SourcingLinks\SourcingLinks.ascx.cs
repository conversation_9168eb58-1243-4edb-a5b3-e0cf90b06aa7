using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.IO;
using System.Text;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class SourcingLinks : Base {

		protected HtmlControl _ulLinks;
		protected Panel _pnlNoneFound;

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("SourcingLinks");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			_ulLinks = (HtmlControl)FindContentControl("ulLinks");
			_pnlNoneFound = (Panel)FindContentControl("pnlNoneFound");
		}

		protected override void OnLoad(EventArgs e) {
			List<BLL.SourcingLink> lst = BLL.SourcingLink.GetListForClient(SessionManager.ClientID);
			foreach (BLL.SourcingLink sl in lst) {
				HtmlControl li = new HtmlGenericControl("li");
				HyperLink hyp = new HyperLink();
				hyp.NavigateUrl = (sl.URL.StartsWith("http")) ? sl.URL : string.Format("http://{0}", sl.URL.Trim());
				hyp.Text = sl.SourcingLinkName;
				hyp.Target = "_blank";
				li.Controls.Add(hyp);
				_ulLinks.Controls.Add(li);
				hyp = null;
			}
			_pnlNoneFound.Visible = (lst.Count == 0);
			_ulLinks.Visible = (lst.Count > 0);
			lst = null;
			base.OnLoad(e);
		}
	}
}