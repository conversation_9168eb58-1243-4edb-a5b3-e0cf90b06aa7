﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202689]		Trung Pham			13-SEP-2024		UPDATE			Get record by inactive param
===========================================================================================
*/

CREATE OR ALTER  PROCEDURE  [dbo].[usp_ipobom_source_Epo]                               
                                                            
    @ClientId INT                                                        
  , @PartSearch NVARCHAR(50)                                                        
  , @Index int =1                                                  
  , @StartDate datetime = NULL                                                    
  , @FinishDate datetime = NULL                                   
  --, @IsPoHUB bit=NULL                                                 
WITH RECOMPILE AS                                                         
BEGIN                                                     
     --DECLARE VARIABLE                                                
     DECLARE @Month int                                                  
     DECLARE @FROMDATE DATETIME                                                  
     DECLARE @ENDDATE DATETIME                                                  
     DECLARE @OutPutDate DATETIME                  
  declare @EPO NVARCHAR(50)='Strategic Vendor'                               
                                                    
       DECLARE @FinishDateVW DATETIME                                                 
     DECLARE @FROMDATEVW DATETIME                                                        
     DECLARE @ENDDATEVW DATETIME                                               
     SET @Month=6                                                  
     /*                                                
        When we get index 1 then we find the maximum date from matching record                                                
        and decsrease no of month for the start date.                                                
     */                              
   declare @HUBName nvarchar(300)                               
   select top 1 @HUBName = CompanyName from tbCompany where ClientNo = @ClientId and IsPOHub=1                                 
                                               
     IF @Index=1                                                  
     BEGIN                                  
                                   
                                        
                                                   
      SELECT @FinishDate=MAX(ISNULL(EpoStatusChangeDate, OriginalEntryDate)) FROM [BorisGlobalTraderImports].dbo.tbEpo o                                    
        JOIN    tbClient cl ON o.ClientNo = cl.ClientId                                               
      WHERE   ((o.ClientNo = @ClientId)                                                    
             OR (o.ClientNo <> @ClientId                                      
                -- AND cl.OwnDataVisibleToOthers = 1))                                               
     AND (case when o.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 ))                           
      AND FullPart LIKE @PartSearch
	  AND ISNULL(o.Inactive,0) = 0
      SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-@Month,@FinishDate))                                                  
      SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                                                  
     END                                                  
    ELSE                                                 
     BEGIN                                                  
       SET @FROMDATE=dbo.ufn_get_date_from_datetime(@StartDate)                                                  
       SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                                 
                                     
         SET @FROMDATEVW=dbo.ufn_get_date_from_datetime(@StartDate)                                                  
       SET @ENDDATEVW =dbo.ufn_get_date_from_datetime(@FinishDate)                                 
                                                      
  END                                                  
      --SET THE OUTPUT DATE                           
      SET @OutPutDate=DATEADD(month,-@Month,@FinishDate)               
               
--    if @FinishDate IS NULL                          
-- BEGIN                    
--  SET @Index=3                          
-- END                          
---- If Index value equal to 3 then more than one year data will be pick from archive database.                             
-- IF @Index = 3                          
--     BEGIN                     
             
                
                
     ;WITH    cteSearch                          
              AS(                                             
    SELECT  o.EpoId                                                        
, o.FullPart COLLATE DATABASE_DEFAULT  as FullPart                                                     
          , o.Part                                                        
          , o.ManufacturerNo                                                        
          , o.DateCode                                                        
          , o.ProductNo                                            
      , o.PackageNo                                                        
          , o.Quantity                                                        
    , o.Price                           
    -- ,case when o.ClientNo=114 then 0 else o.Price end AS Price                                                         
          --, o.OriginalEntryDate
		   ,o.DLUP as OriginalEntryDate  
          , o.Salesman                                                   
          , o.SupplierNo                                                        
          , o.CurrencyNo                                                        
          , o.ROHS                                                        
          , o.UpdatedBy                                                        
          , o.DLUP                                                        
          , o.EpoStatusNo                                                        
          , ISNULL(o.EpoStatusChangeDate, o.OriginalEntryDate) AS EpoStatusChangeDate                                                        
          , o.EpoStatusChangeLoginNo                                                        
          , m.ManufacturerCode COLLATE DATABASE_DEFAULT as ManufacturerCode                                                      
          , p.ProductName   COLLATE DATABASE_DEFAULT as ProductName                                                    
          , c.CurrencyCode  COLLATE DATABASE_DEFAULT  as CurrencyCode                                                    
          , c.CurrencyDescription  COLLATE DATABASE_DEFAULT as CurrencyDescription                                                     
         -- , ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT AS SupplierName                                
   --, case when o.ClientNo=114 then @HUBName else  ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT end AS SupplierName                                                   
          --, case when o.ClientNo=114 then ISNULL(s.CompanyName, o.SupplierName) else  ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT end AS SupplierName                                           
    , case when o.SupplierNo = 0 then o.SupplierName else ISNULL(s.CompanyName, o.SupplierName) COLLATE DATABASE_DEFAULT end AS SupplierName                                           
          , ISNULL(m.ManufacturerName, o.ManufacturerName) COLLATE DATABASE_DEFAULT AS ManufacturerName                                                        
          , s.EMail COLLATE DATABASE_DEFAULT AS SupplierEmail                           
          , l.EmployeeName COLLATE DATABASE_DEFAULT AS SalesmanName                                                        
          , l2.EmployeeName COLLATE DATABASE_DEFAULT AS EpoStatusChangeEmployeeName                                                        
          , g.PackageName COLLATE DATABASE_DEFAULT as PackageName                                            
          , o.Notes   COLLATE DATABASE_DEFAULT as Notes                       
    --,(o.LeadTime +' ,'+ convert(nvarchar(25),o. OriginalEntryDate, 121) + CONVERT(varchar(12), 9))  COLLATE DATABASE_DEFAULT AS Notes                                                 
          , o.ClientNo                           
          , cl.ClientId                                                      
          , cl.ClientName  COLLATE DATABASE_DEFAULT  as ClientName                                                    
          , isnull(cl.OwnDataVisibleToOthers,0) AS ClientDataVisibleToOthers                                                        
            --[001] code start                                                        
          , isnull(cotype.Name,'') COLLATE DATABASE_DEFAULT as SupplierType                                                        
          --[001] code end                    
      , cl.ClientCode COLLATE DATABASE_DEFAULT  as ClientCode                                 
           , o.SPQ   COLLATE DATABASE_DEFAULT  as SPQ                              
    , o.LeadTime COLLATE DATABASE_DEFAULT  as LeadTime                                
          , o.ROHSStatus  COLLATE DATABASE_DEFAULT as ROHSStatus                               
          , o.FactorySealed  COLLATE DATABASE_DEFAULT  as FactorySealed                               
         -- , o.MSL                                     
          , ml.MSLLevel COLLATE DATABASE_DEFAULT  as MSL                          
    , o.IPOBOMNo                                   
    ,o.SupplierTotalQSA  COLLATE DATABASE_DEFAULT   as SupplierTotalQSA                              
    ,o.SupplierLTB  COLLATE DATABASE_DEFAULT  as SupplierLTB                                
    ,o.SupplierMOQ  COLLATE DATABASE_DEFAULT  as SupplierMOQ                       
  ,o.UpliftPercentage                      
  ,o.UpliftPrice                          
    ,ishub=0                  
 ,@EPO as SupplierEpo          
 ,o.Description                                                        
    FROM    [BorisGlobalTraderImports].dbo.tbEpo o                                                        
    LEFT JOIN dbo.tbManufacturer m ON o.ManufacturerNo = m.ManufacturerId                                                
    LEFT JOIN dbo.tbProduct p ON o.ProductNo = p.ProductId                                                        
    LEFT JOIN dbo.tbCurrency c ON o.CurrencyNo = c.CurrencyId                                                        
    LEFT JOIN dbo.tbCompany s ON o.SupplierNo = s.CompanyId                                                        
  LEFT JOIN dbo.tbLogin l ON o.Salesman = l.LoginId                                                    
    LEFT JOIN dbo.tbLogin l2 ON o.EpoStatusChangeLoginNo = l2.LoginId                                                        
    LEFT JOIN dbo.tbPackage g ON o.PackageNo = g.PackageId                                                        
    JOIN    tbClient cl ON o.ClientNo = cl.ClientId                                             
      --[001] code start                                                        
    LEFT JOIN dbo.tbCompanyType cotype ON s.TypeNo=cotype.CompanyTypeId                               
    left join tbMSLLevel ml on o.MSLLevelNo = ml.MSLLevelId                                                                            
    --[001] code end                                                         
    WHERE   ((o.ClientNo = @ClientId)                   
             OR (o.ClientNo <> @ClientId                                                    
                 --AND cl.OwnDataVisibleToOthers = 1))                                   
     AND (case when o.ClientNo=114 then cast(1 as bit) else  cl.OwnDataVisibleToOthers end) = 1 ))                           
               --   AND ((@IsPoHUB is NULL)                                 
               -- OR (not @IsPoHUB is NULL AND isnull(o.IsPoHub,0)= @IsPoHUB ))                                    
     AND o.FullPart LIKE @PartSearch                                            
     AND (dbo.ufn_get_date_from_datetime(ISNULL(o.EpoStatusChangeDate, o.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)
	 AND ISNULL(o.Inactive,0) = 0
   -- ORDER BY ISNULL(o.OfferStatusChangeDate, o.OriginalEntryDate) DESC                                                        
                                   
        )                                  
      select * from cteSearch  ORDER BY ISNULL(EpoStatusChangeDate, OriginalEntryDate) DESC                                          
      --SELECT THE OUT DATE                                                       
      --SELECT THE OUT DATE                              
                                               
    SELECT @OutPutDate AS OutPutDate                                  
END 