Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Reason=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Reason.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Reason.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Reason.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Reason.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Reason");this._objData.set_DataObject("Reason");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Reason.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Reason",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);