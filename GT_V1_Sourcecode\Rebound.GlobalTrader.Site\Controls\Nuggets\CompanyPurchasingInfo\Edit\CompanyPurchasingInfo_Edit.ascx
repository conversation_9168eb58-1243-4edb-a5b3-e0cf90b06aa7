<%--
Marker     changed by      date          Remarks
[001]      Vinay          25/03/2014     ESMS Ref:107 -  Add provision to Default Shipping from Country 
[002]      Vinay          18/05/2015     ESMS Ref:233
--%>
<%@ Control Language="C#" CodeBehind="CompanyPurchasingInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyPurchasingInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyPurchasingInfo_Edit")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">

			<ReboundUI_Form:FormField id="ctlApproved" runat="server" FieldID="chkApproved" ResourceTitle="IsApproved">
				<Field><ReboundUI:ImageCheckBox ID="chkApproved" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<%--[002] code start--%>
			<ReboundUI_Form:FormField id="ctlOnStop" runat="server" FieldID="chkOnStop" ResourceTitle="OnStop">
				<Field><ReboundUI:ImageCheckBox ID="chkOnStop" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			<%--[002] code end--%>
			
			<ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency" IsRequiredField="true">
				<Field><ReboundDropDown:BuyCurrency ID="ddlCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlSupplierNo" runat="server" FieldID="txtSupplierNo" ResourceTitle="SupplierNo">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierNo" runat="server" Width="100" /></Field>
			</ReboundUI_Form:FormField>		
			
			<%--<ReboundUI_Form:FormField id="ctlTerms" runat="server" FieldID="ddlTerms" ResourceTitle="Terms" IsRequiredField="true">
				<Field><ReboundDropDown:Terms ID="ddlTerms" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
			<ReboundUI_Form:FormField id="ctlTerms" runat="server" FieldID="ddlTerms" ResourceTitle="Terms" IsRequiredField="true">
				<Field><ReboundDropDown:BuyTerms ID="ddlTerms" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<%--
			<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="ddlTax" ResourceTitle="Tax" IsRequiredField="true">
				<Field><ReboundDropDown:Tax ID="ddlTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
			
			<ReboundUI_Form:FormField id="ctlRating" runat="server" FieldID="starsRating" ResourceTitle="Rating">
				<Field><ReboundUI:StarRating ID="starsRating" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlShipVia" runat="server" FieldID="ddlShipVia" ResourceTitle="DefaultShipVia">
				<Field><ReboundDropDown:BuyShipMethod ID="ddlShipVia" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlShippingAccountNo" runat="server" FieldID="txtShippingAccountNo" ResourceTitle="DefaultShippingAccountNo">
				<Field><ReboundUI:ReboundTextBox ID="txtShippingAccountNo" runat="server" Width="150" /></Field>
			</ReboundUI_Form:FormField>		

			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="ddlContact" ResourceTitle="DefaultContactNo">
				<Field><ReboundDropDown:ContactsForCompany ID="ddlContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlCountry" runat="server" FieldID="ddlCountry" IsRequiredField="true"  ResourceTitle="DefaultShipCountry">
				<Field><ReboundDropDown:Country ID="ddlCountry" runat="server"  /></Field>
			</ReboundUI_Form:FormField>
			<%--[001] code end--%>
							
		</ReboundUI_Table:Form>
	</Content>
	
</ReboundUI_Form:DesignBase>
