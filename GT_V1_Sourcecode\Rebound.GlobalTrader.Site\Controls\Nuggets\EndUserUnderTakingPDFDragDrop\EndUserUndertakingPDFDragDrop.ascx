﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="EndUserUndertakingPDFDragDrop.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingPDFDragDrop" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<%--<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />--%>
			<div class="documentsize documentsize1" ><%=Functions.GetGlobalResource("FormFields", "MaxDocumentSize")%><span id="EndDocumentFileSizeUploadPDF"></span></div>
		<div id="singleupload2">Upload</div>	
	</Links>
	
	<Content>
		
		<asp:Panel ID="pnlPDFDocuments" runat="server"></asp:Panel>
	
		<asp:HiddenField ID="hidSection" runat="server" />
		
		
		<link href="css/uploadfile.css" rel="stylesheet">
<script src="js/jquery.min.js"></script>
<script src="js/jquery.uploadfile.js"></script>
		<script type="text/javascript">
			$(document).ready(function () {



                var PDFFileSize;
                var SectionMod;
                $.ajax({
                    processData: false,
                    contentType: 'application/json',
                    type: 'POST',
                    async: false,
                    url: "DocImage.ashx?type=DOCUMENTFILESIZE&DocumentType=1",
                    success: function (data) {
                        var obj = JSON.parse(data);
                        PDFFileSize = obj.PDFFileSize;
                        PDFFileSizeMB = obj.PDFFileSizeMB;
                        $("#EndDocumentFileSizeUploadPDF").text('(' + PDFFileSizeMB + ' MB)');
                        PDFDragDrop(PDFFileSize);
                    },
                    error: function () {
                        alert("The system has encountered a network connectivity issue. Please check your connectivity and try again.");

                    },
                });

				function PDFDragDrop(PDFFileSize) {
					// var strSection = $find("<%=this.ClientID%>")._strSectionName;
					var dragdropObj = $("#singleupload2").uploadFile({
						// url: "DocImage.ashx?mxs=1&type=PDFUPLOAD&section=" + document.getElementById("ctl00_cphMain_ctlInvoicePDFDragDrop_ctlDB_ctl13_hidSection").value + "&docId=0&IsDragDrop=true",
						url: "DocImage.ashx?mxs=1&type=PDFUPLOAD&docId=0&IsDragDrop=true",
						allowedTypes: "pdf",
						fileName: "myfile",
						autoSubmit: false,
						multiple: false,
						//maxFileSize: 7900000,
                        maxFileSize: PDFFileSize,
						showStatusAfterSuccess: false,
						showCancel: true,
						showDone: true,
						dynamicFormData: function () {
							var data = { section: $find("<%=this.ClientID%>")._strSectionName }
							return data;
						},
						//maxFileCount: 1,

						onSuccess: function (files, data, xhr) {
							var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
							if (json.Result) {
								$find("<%=this.ClientID%>")._frmAdd._strDBFileName = json.FileName;
								$find("<%=this.ClientID%>")._frmAdd.saveEdit();
							}
							else {
								$find("<%=this.ClientID%>")._frmAdd.showError(true, "Failed to upload. Please try again.");
							}
						},
						onSelect: function (fup) {
							var result = true;
							if ($find("<%=this.ClientID%>")._intCountPDF < $find("<%=this.ClientID%>")._intMaxPDFDocuments) {
                                if (fup != null && (fup[0].name.split('.').pop().toLowerCase() == "pdf" && fup[0].size < PDFFileSize) && ($find("<%=this.ClientID%>")._blnCanAdd))
									$find("<%=this.ClientID%>").showAddFormFromDrag(fup[0].name, dragdropObj);
								// return true;
							}
							else {
								alert("A PDF file already exists for the line you have selected. Please remove existing PDF file for selected line and perform upload again");
								$('.ajax-file-upload-statusbar').remove();
								//result = false;
							}
							//return result;

						}
					});
				}
		    });

		    function getSectionName() {
		        var strOut = "";
		        if ($find("<%=this.ClientID%>"))
		            strOut = $find("<%=this.ClientID%>")._strSectionName;
		        return strOut;
		    }

		    $(document).ready(function() {
		        setTimeout("PermissionAdd();", 500);
		    });

		    function PermissionAdd() {
		        var parentId = $find("<%=this.ClientID%>")._element.id;
		        var strSec = $find("<%=this.ClientID%>")._strSectionName;
		        
		        $("#"+parentId).find("[class=ajax-upload-dragdrop]").attr("id", strSec);
		        //alert($find("<%=this.ClientID%>")._element.id);
		        if (!$find("<%=this.ClientID%>")._blnCanAdd) {
                    $("#" + strSec).hide();
                }
            }
        </script>
		
		<div class="clearing"></div>
		
	
	</Content>
	
	<Forms>
		<ReboundForm:EndUserUndertakingPDF_AddDragDrop id="ctlAdd" runat="server" />
		<ReboundForm:EndUserUndertakingPDF_DeleteDragDrop id="ctlDelete" runat="server" />		
	</Forms>
</ReboundUI_Nugget:DesignBase>
