﻿//Marker     Changed by               Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>           18/05/2022   Generate Power App token.
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
   public class SqlPowerAppTokenProvider : PowerAppTokenProvider
    {
        public override PowerAppTokenDetails GetTokenForPowerApp(System.Int32? loginId, System.String WorkFlowType,System.Boolean? IsNotifySO)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PowerAppGenerateToken", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@LoginID", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@WorkFlowType", SqlDbType.NVarChar,500).Value = WorkFlowType;
                cmd.Parameters.Add("@IsNotifySO",SqlDbType.Bit).Value = IsNotifySO;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetLoginPreferenceFromReader(reader);
                    PowerAppTokenDetails obj = new PowerAppTokenDetails();
                    obj.RequestId = GetReaderValue_Int32(reader, "RequestId", 0);
                    obj.TokenValue = GetReaderValue_String(reader, "TokenValue", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Power App Token", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
   }
}
