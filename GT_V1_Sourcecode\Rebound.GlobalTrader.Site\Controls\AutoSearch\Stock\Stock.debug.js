///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock = function(element) { 
	Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.initializeBase(this, [element]);
	this._searchType = null;
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.prototype = {

	get_searchType: function() { return this._searchType; }, 	set_searchType: function(value) { if (this._searchType !== value)  this._searchType = value; }, 
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
		this.addSetupParametersEvent(Function.createDelegate(this, this.setupParameters));
		this.setupDataObject("Stock");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this.__searchType = null;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.callBaseMethod(this, "dispose");
	},
	
	setupParameters: function() {
		this.addDataParameter("searchType", this._searchType);
	},
	
	dataReturned: function() {
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = "";
				if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
					strHTML = $RGT_nubButton_Stock(res.ID, res.Name);
				} else {
					strHTML = $R_FN.setCleanTextValue(res.Name);
				}
				this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID);
				strHTML = null; res = null;
			}
		}
	}
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Stock", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
