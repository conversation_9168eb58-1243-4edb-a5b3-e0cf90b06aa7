﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-211911]		An.TranTan		17-Oct-2024		CREATE		Get top 20 data to be imported after validated and converted
===========================================================================================
*/
CREATE OR ALTER PROCEDURE dbo.usp_get_PriceQuote_DataToBeImported
	@DisplayLength INT = 20
	,@UserId INT = 0
AS
BEGIN
	SET NOCOUNT ON;
	SELECT 
		RequirementNo AS 'REQUIREMENT'
		,[SelectedClientId] AS 'CLIENT NO'
		,[SupplierName] AS 'SUPPLIER NAME'
		,[SupplierPart] AS 'SUPPLIER PART'
		,[SupplierCost] AS 'Supplier Cost'
		,[ROHS]
		,[ManufacturerName] AS 'MANUFACTURER'
		,[DateCode] AS 'DATE CODE'
		,[PackageName] AS 'PACKAGE'
		,[Quantity] AS 'OFFERED QTY'
		,[OfferStatus] AS 'OFFER STATUS'
		,[SPQ]
		,[FactorySealed] AS 'FACTORY SEALED'
		,[QtyInStock] AS 'QTY IN STOCK'
		,[MOQ]
		,[LastTimeBuy] AS 'LAST TIME BUY'
		,[CurrencyCode] AS 'CURRENCY'
		,[BuyPrice] AS 'BUY PRICE'
		,[SellPrice] AS 'SELL PRICE'
		,[ShippingCost] AS 'SHIPPING COST'
		,[LeadTime] AS 'LEAD TIME'
		,[Region] AS 'REGION'
		,[DeliveryDate] AS 'DELIVERY DATE'
		,[Description] AS 'NOTES'
		,[MSL]
		,COUNT(*) over() as TotalCount
	FROM BorisGlobalTraderImports.dbo.tbPriceQuoteToBeImported WITH(NOLOCK)
		WHERE CreatedBy = @UserId
			AND LineNumber <= @DisplayLength

	SET NOCOUNT OFF;
END
GO
/*
exec usp_get_PriceQuote_DataToBeImported
	@DisplayLength  = 20
	,@UserId  = 6670
*/



