using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Group : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("Group");
            base.ProcessRequest(context);
        }

        protected override void GetData() {
            int? intGoodsInLineNo; int? intInvoiceLineNo; 
            intGoodsInLineNo = GetFormValue_NullableInt("GoodsInLineNo");
            intInvoiceLineNo = GetFormValue_NullableInt("InvoiceLineNo");

           // string strCacheOptions = CacheManager.SerializeOptions(new object[] { intGoodsInLineNo });
           // string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
           // if (string.IsNullOrEmpty(strCachedData)) {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.GoodsInLine> lst = BLL.GoodsInLine.DropDown(intGoodsInLineNo, intInvoiceLineNo);

                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("Group", lst[i].SubGroup);
                    jsnItem.AddVariable("RemainSerialNo", lst[i].SerialNo);

                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;                   
                }              
                jsn.AddVariable("Results", jsnList);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            //} else {
            //    _context.Response.Write(strCachedData);
            //}
            //strCachedData = null;
        }
    }
}
