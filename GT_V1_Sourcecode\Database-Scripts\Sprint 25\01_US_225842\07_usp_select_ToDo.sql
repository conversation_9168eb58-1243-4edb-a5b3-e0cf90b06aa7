﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--============================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-229094]     An.TranTan		 21-Jan-2025		UPDATE		Get quote number
[US-229094]     An.TranTan		 24-Jan-2025		UPDATE		Get quote customer name
[US-229094]     An.TranTan		 04-Apr-2025		UPDATE		Get sales order customer name
==============================================================================================================================  
*/
CREATE OR ALTER   PROCEDURE [dbo].[usp_select_ToDo]
@ToDoId int
AS
BEGIN
	select vw.*
		, q.QuoteNumber
		, so.SalesOrderNumber
		, CASE WHEN vw.ToDoCategoryNo = 2 THEN qc.CompanyName 
			WHEN vw.ToDoCategoryNo = 4 THEN soc.CompanyName
			ELSE NULL
		END AS CustomerName
	from vwToDo vw
	left join tbQuote q WITH(NOLOCK) on q.QuoteId = vw.QuoteNo
	left join tbCompany qc WITH(NOLOCK) on qc.CompanyId = q.CompanyNo
	left join tbSalesOrder so WITH(NOLOCK) on so.SalesOrderId = vw.SalesOrderNo
	left join tbCompany soc WITH(NOLOCK) on soc.CompanyId = so.CompanyNo
	where vw.ToDoId = @ToDoId
END
GO


