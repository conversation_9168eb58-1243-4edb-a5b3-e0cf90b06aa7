Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.Base=function(n){Rebound.GlobalTrader.Site.Controls.Forms.Base.initializeBase(this,[n]);this._intIndex=-1;this._mode="";this._strErrorMessage="";this._blnShowQuickHelp=!1;this._aryFields=[];this._blnShowRequiredFieldsExplanation=!1;this._blnFirstTimeShown=!0;this._blnIsShown=!1;this._intPreviousScrollPosition=0;this._aryChangedFieldIDs=[]};Rebound.GlobalTrader.Site.Controls.Forms.Base.prototype={get_pnlContent:function(){return this._pnlContent},set_pnlContent:function(n){this._pnlContent!==n&&(this._pnlContent=n)},get_pnlContentInner:function(){return this._pnlContentInner},set_pnlContentInner:function(n){this._pnlContentInner!==n&&(this._pnlContentInner=n)},get_pnlNotes:function(){return this._pnlNotes},set_pnlNotes:function(n){this._pnlNotes!==n&&(this._pnlNotes=n)},get_pnlLoading:function(){return this._pnlLoading},set_pnlLoading:function(n){this._pnlLoading!==n&&(this._pnlLoading=n)},get_pnlHeader:function(){return this._pnlHeader},set_pnlHeader:function(n){this._pnlHeader!==n&&(this._pnlHeader=n)},get_pnlSaving:function(){return this._pnlSaving},set_pnlSaving:function(n){this._pnlSaving!==n&&(this._pnlSaving=n)},get_pnlSavedOK:function(){return this._pnlSavedOK},set_pnlSavedOK:function(n){this._pnlSavedOK!==n&&(this._pnlSavedOK=n)},get_pnlValidateError:function(){return this._pnlValidateError},set_pnlValidateError:function(n){this._pnlValidateError!==n&&(this._pnlValidateError=n)},get_pnlValidateErrorText:function(){return this._pnlValidateErrorText},set_pnlValidateErrorText:function(n){this._pnlValidateErrorText!==n&&(this._pnlValidateErrorText=n)},get_pnlExplain:function(){return this._pnlExplain},set_pnlExplain:function(n){this._pnlExplain!==n&&(this._pnlExplain=n)},get_ctlRelatedNugget:function(){return this._ctlRelatedNugget},set_ctlRelatedNugget:function(n){this._ctlRelatedNugget!==n&&(this._ctlRelatedNugget=n)},get_blnShowQuickHelp:function(){return this._blnShowQuickHelp},set_blnShowQuickHelp:function(n){this._blnShowQuickHelp!==n&&(this._blnShowQuickHelp=n)},get_aryFields:function(){return this._aryFields},set_aryFields:function(n){this._aryFields!==n&&(this._aryFields=n)},get_objFieldOrdinals:function(){return this._objFieldOrdinals},set_objFieldOrdinals:function(n){this._objFieldOrdinals!==n&&(this._objFieldOrdinals=n)},get_pnlLinksHolder:function(){return this._pnlLinksHolder},set_pnlLinksHolder:function(n){this._pnlLinksHolder!==n&&(this._pnlLinksHolder=n)},get_pnlFooterLinksHolder:function(){return this._pnlFooterLinksHolder},set_pnlFooterLinksHolder:function(n){this._pnlFooterLinksHolder!==n&&(this._pnlFooterLinksHolder=n)},get_intIndex:function(){return this._intIndex},set_intIndex:function(n){this._intIndex!==n&&(this._intIndex=n)},get_h4:function(){return this._h4},set_h4:function(n){this._h4!==n&&(this._h4=n)},get_hypQuickHelp:function(){return this._hypQuickHelp},set_hypQuickHelp:function(n){this._hypQuickHelp!==n&&(this._hypQuickHelp=n)},get_lblQuickHelpText:function(){return this._lblQuickHelpText},set_lblQuickHelpText:function(n){this._lblQuickHelpText!==n&&(this._lblQuickHelpText=n)},get_mode:function(){return this._mode},set_mode:function(n){this._mode!==n&&(this._mode=n)},get_ibtnSave:function(){return this._ibtnSave},set_ibtnSave:function(n){this._ibtnSave!==n&&(this._ibtnSave=n)},get_ibtnCancel:function(){return this._ibtnCancel},set_ibtnCancel:function(n){this._ibtnCancel!==n&&(this._ibtnCancel=n)},get_ibtnSave_Footer:function(){return this._ibtnSave_Footer},set_ibtnSave_Footer:function(n){this._ibtnSave_Footer!==n&&(this._ibtnSave_Footer=n)},get_ibtnCancel_Footer:function(){return this._ibtnCancel_Footer},set_ibtnCancel_Footer:function(n){this._ibtnCancel_Footer!==n&&(this._ibtnCancel_Footer=n)},get_blnShowRequiredFieldsExplanation:function(){return this._blnShowRequiredFieldsExplanation},set_blnShowRequiredFieldsExplanation:function(n){this._blnShowRequiredFieldsExplanation!==n&&(this._blnShowRequiredFieldsExplanation=n)},get_ctlMultiStep:function(){return this._ctlMultiStep},set_ctlMultiStep:function(n){this._ctlMultiStep!==n&&(this._ctlMultiStep=n)},addValidate:function(n){this.get_events().addHandler("Validate",n)},removeValidate:function(n){this.get_events().removeHandler("Validate",n)},onValidate:function(){this.showError(!1);this.resetFormFields();var n=this.get_events().getHandler("Validate");n&&n(this,Sys.EventArgs.Empty)},addSave:function(n){this.get_events().addHandler("Save",n)},removeSave:function(n){this.get_events().removeHandler("Save",n)},onSave:function(){this.showSaving(!0);this.showError(!1);var n=this.get_events().getHandler("Save");n&&n(this,Sys.EventArgs.Empty)},addSaveComplete:function(n){this.get_events().addHandler("SaveComplete",n)},removeSaveComplete:function(n){this.get_events().removeHandler("SaveComplete",n)},onSaveComplete:function(){this.showSaving(!1);this.showError(!1);this.showLoading(!1);var n=this.get_events().getHandler("SaveComplete");n&&n(this,Sys.EventArgs.Empty)},addSaveError:function(n){this.get_events().addHandler("SaveError",n)},removeSaveError:function(n){this.get_events().removeHandler("SaveError",n)},onSaveError:function(){this.showSaving(!1);this.showLoading(!1);this.showError(!1);this.showNuggetError(!0,this._strErrorMessage);var n=this.get_events().getHandler("SaveError");n&&n(this,Sys.EventArgs.Empty)},addCancel:function(n){this.get_events().addHandler("Cancel",n)},removeCancel:function(n){this.get_events().removeHandler("Cancel",n)},onCancel:function(){var n=this.get_events().getHandler("Cancel");n&&n(this,Sys.EventArgs.Empty)},addModeChanged:function(n){this.get_events().addHandler("ModeChanged",n)},removeModeChanged:function(n){this.get_events().removeHandler("ModeChanged",n)},onModeChanged:function(){var n=this.get_events().getHandler("ModeChanged");n&&n(this,Sys.EventArgs.Empty)},addNotConfirmed:function(n){this.get_events().addHandler("NotConfirmed",n)},removeNotConfirmed:function(n){this.get_events().removeHandler("NotConfirmed",n)},onNotConfirmed:function(){var n=this.get_events().getHandler("NotConfirmed");n&&n(this,Sys.EventArgs.Empty)},addShown:function(n){this.get_events().addHandler("Shown",n)},removeShown:function(n){this.get_events().removeHandler("Shown",n)},onShown:function(){this.showError(!1);this.showSaving(!1);this.showLoading(!1);this.showInnerContent(!0);this._ctlRelatedNugget&&(this._ctlRelatedNugget.control.showFooterContent(!0),this._ctlRelatedNugget.control.showError(!1));var n=this.get_events().getHandler("Shown");n&&n(this,Sys.EventArgs.Empty);this._blnFirstTimeShown=!1;this.scrollPageToForm()},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.Base.callBaseMethod(this,"initialize");this._ibtnSave&&this.addSaveClick(Function.createDelegate(this,this.doFormSave));this._ibtnCancel&&this.addCancelClick(Function.createDelegate(this,this.onCancel))},dispose:function(){this.get_element()&&$clearHandlers(this.get_element());this._ibtnSave&&$R_IBTN.clearHandlers(this._ibtnSave);this._ibtnSave_Footer&&$R_IBTN.clearHandlers(this._ibtnSave_Footer);this._ibtnCancel&&$R_IBTN.clearHandlers(this._ibtnCancel);this._ibtnCancel_Footer&&$R_IBTN.clearHandlers(this._ibtnCancel_Footer);this._pnlContent=null;this._pnlContentInner=null;this._pnlNotes=null;this._pnlLoading=null;this._pnlHeader=null;this._pnlSaving=null;this._pnlSavedOK=null;this._pnlValidateError=null;this._pnlValidateErrorText=null;this._pnlExplain=null;this._ctlRelatedNugget=null;this._aryFields=null;this._objFieldOrdinals=null;this._pnlLinksHolder=null;this._pnlFooterLinksHolder=null;this._intIndex=null;this._h4=null;this._hypQuickHelp=null;this._lblQuickHelpText=null;this._ibtnSave=null;this._ibtnCancel=null;this._ibtnSave_Footer=null;this._ibtnCancel_Footer=null;this._blnShowRequiredFieldsExplanation=null;this._ctlMultiStep=null;Rebound.GlobalTrader.Site.Controls.Forms.Base.callBaseMethod(this,"dispose")},show:function(n){$R_FN.showElement(this.get_element(),n);$R_FN.showElement(this._pnlContent,n);$R_FN.showElement(this._pnlLinksHolder,n);$R_FN.showElement(this._pnlFooterLinksHolder,n);n?(this.resetFormFields(),this.showError(!1),this._blnIsShown=!0,this.onShown()):this._blnIsShown&&(this._blnIsShown=!1,this.scrollPageBackToPositionBeforeForm())},getField:function(n){var t=eval("this._objFieldOrdinals."+n);return t==undefined&&eval(String.format("FormFieldNotFound_{0}()",n)),this._aryFields[t]},getFieldControl:function(n,t){t||(t=this.getField(n));switch(Number.parseInvariant(t.Type.toString())){case $R_ENUM$FormFieldControlType.TextBox:return $get(t.ControlID);case $R_ENUM$FormFieldControlType.CheckBox:return $find(t.ControlID);case $R_ENUM$FormFieldControlType.DropDown:return $find(t.ControlID);case $R_ENUM$FormFieldControlType.StarRating:return $find(t.ControlID);case $R_ENUM$FormFieldControlType.TimeSelect:return $find(t.ControlID);case $R_ENUM$FormFieldControlType.Literal:return $get(t.ControlID);case $R_ENUM$FormFieldControlType.FileUpload:return $find(t.ControlID);case $R_ENUM$FormFieldControlType.Combo:return $find(t.ControlID)}},getFieldComponent:function(n){return $find(this.getField(n).ControlID)},getFieldElement:function(n,t){return $get(String.format("{0}_{1}",n.ID,t))},showFormField:function(n,t){var i=$get(this.getField(n).ID);i&&$R_FN.showElement(i,t)},getFieldValue:function(n,t){t||(t=this.getField(n));var i;switch(Number.parseInvariant(t.Type.toString())){case $R_ENUM$FormFieldControlType.TextBox:i=$get(t.ControlID).value;break;case $R_ENUM$FormFieldControlType.CheckBox:i=$find(t.ControlID)._blnChecked;break;case $R_ENUM$FormFieldControlType.DropDown:i=$find(t.ControlID).getValue();break;case $R_ENUM$FormFieldControlType.StarRating:i=$find(t.ControlID)._intCurrentRating;break;case $R_ENUM$FormFieldControlType.TimeSelect:i=$find(t.ControlID).getValue();break;case $R_ENUM$FormFieldControlType.Literal:i=$get(t.ControlID).innerHTML;break;case $R_ENUM$FormFieldControlType.FileUpload:i=$find(t.ControlID).getValue();break;case $R_ENUM$FormFieldControlType.Combo:i=$find(t.ControlID).getValue()}return i},getFieldComboText:function(n,t){return t||(t=this.getField(n)),$find(t.ControlID)._strSelectedText},setFieldValue:function(n,t,i,r){i||(i=this.getField(n));typeof t=="string"&&(t=t.replace(/(<br \/>)|(<br\/>)|(<br>)/g,"\n"));t==null&&(t="");t=="&nbsp;"&&(t="");switch(Number.parseInvariant(i.Type.toString())){case $R_ENUM$FormFieldControlType.TextBox:$get(i.ControlID).value=$R_FN.setCleanTextValue(t.toString());break;case $R_ENUM$FormFieldControlType.CheckBox:$find(i.ControlID).setChecked(t);break;case $R_ENUM$FormFieldControlType.DropDown:$find(i.ControlID).setValue(t,r);break;case $R_ENUM$FormFieldControlType.StarRating:$find(i.ControlID).setRating(t);break;case $R_ENUM$FormFieldControlType.TimeSelect:$find(i.ControlID).setValue(t);break;case $R_ENUM$FormFieldControlType.Literal:$get(i.ControlID).innerHTML=t;break;case $R_ENUM$FormFieldControlType.Combo:$find(i.ControlID).setValue(t,r)}},setFieldInError:function(n,t,i,r){r||(r=this.getField(n));t?Sys.UI.DomElement.addCssClass($get(r.ID),"formRowError"):Sys.UI.DomElement.removeCssClass($get(r.ID),"formRowError");Number.parseInvariant(r.Type.toString())==$R_ENUM$FormFieldControlType.FileUpload&&$find(r.ControlID).showFieldError(t);var u=this.getFieldElement(r,"pnlMessages");u&&$R_FN.showElement(u,t);i&&$R_FN.setInnerHTML(u,i);u=null},showFieldLoading:function(n,t){var i=this.getField(n),r=this.getFieldElement(i,"pnlFieldControls"),u=this.getFieldElement(i,"pnlLoading");u&&$R_FN.showElement(u,t);r&&$R_FN.showElement(r,!t);u=null;r=null;i=null},resetFieldError:function(n,t){this.setFieldInError(n,!1,"",t)},getFieldDropDownData:function(n){var t=this.getField(n),i;if(t){if(t.Type==$R_ENUM$FormFieldControlType.DropDown){if(i=$find(t.ControlID),!i)return;i.getData()}t=null}},getFieldDropDownText:function(n){var t=this.getField(n),i="";return t.Type==$R_ENUM$FormFieldControlType.DropDown&&(i=$find(t.ControlID).getText()),t=null,i},getFieldDropDownExtraText:function(n){var t=this.getField(n),i="";return t.Type==$R_ENUM$FormFieldControlType.DropDown&&(i=$find(t.ControlID).getExtraText()),t=null,i},checkFieldEntered:function(n,t){t||(t=this.getField(n));var i=!0;switch(Number.parseInvariant(t.Type.toString())){case $R_ENUM$FormFieldControlType.TextBox:i=$R_FN.isEntered($get(t.ControlID).value);break;case $R_ENUM$FormFieldControlType.DropDown:i=!$find(t.ControlID).isSetAsNoValue();break;case $R_ENUM$FormFieldControlType.FileUpload:i=$find(t.ControlID).checkEntered();break;case $R_ENUM$FormFieldControlType.Combo:i=$find(t.ControlID).checkEntered()}return Boolean.parse(t.Required)&&(i||(this.setFieldInError(n,!0,$R_RES.RequiredFieldMissingMessage,t),Number.parseInvariant(t.Type.toString())==$R_ENUM$FormFieldControlType.FileUpload&&$find(t.ControlID).showFieldError(!0))),i},checkFieldNumeric:function(n,t){t||(t=this.getField(n));var i=!0;return Boolean.parse(t.IsNumeric)&&(t.Type==$R_ENUM$FormFieldControlType.TextBox&&(i=$R_FN.checkNumeric($get(t.ControlID))),i||this.setFieldInError(null,!0,$R_RES.NumericFieldError,t)),i},checkNumericFieldLessThan:function(n,t,i){var r=this.getField(n),u=!0;return Boolean.parse(r.IsNumeric)&&(r.Type==$R_ENUM$FormFieldControlType.TextBox&&(u=$R_FN.checkNumeric($get(r.ControlID))?Number.parseLocale(this.getFieldValue(null,r).toString())<(i?t+1:t):!1),u||this.setFieldInError(null,!0,String.format(i?$R_RES.NumericFieldLessThanOrEqualToError:$R_RES.NumericFieldLessThanError,t),r)),u},checkNumericFieldLessThanOrEqualTo:function(n,t){return this.checkNumericFieldLessThan(n,t,!0)},checkNumericFieldGreaterThan:function(n,t,i){var r=this.getField(n),u=!0;return Boolean.parse(r.IsNumeric)&&(r.Type==$R_ENUM$FormFieldControlType.TextBox&&(u=$R_FN.checkNumeric($get(r.ControlID))?Number.parseLocale(this.getFieldValue(null,r).toString())>(i?t-1:t):!1),u||this.setFieldInError(null,!0,String.format(i?$R_RES.NumericFieldGreaterThanOrEqualToError:$R_RES.NumericFieldGreaterThanError,t),r)),u},checkNumericFieldGreaterThanOrEqualTo:function(n,t){return this.checkNumericFieldGreaterThan(n,t,!0)},checkFieldValidEmail:function(n,t){return(t||(t=this.getField(n)),Boolean.parse(t.CheckForValidEmail)&&!$R_FN.isValidEmail(this.getFieldValue(null,t)))?(this.setFieldInError(null,!0,$R_RES.EmailInvalidMessage,t),!1):!0},checkFieldValidURL:function(n,t){return(t||(t=this.getField(n)),Boolean.parse(t.CheckForValidURL)&&!$R_FN.isValidURL(this.getFieldValue(null,t).toLowerCase()))?(this.setFieldInError(null,!0,$R_RES.URLInvalidMessage,t),!1):!0},fieldFocus:function(){var n=this.getFieldControl(strField);n.focus();n=null},showField:function(n,t){var i=this.getField(n);i.Visible=t;$R_FN.showElement($get(i.ID),t);i=null},disableDropdown:function(n,t){var r=this.getField(n),i;r.Type==$R_ENUM$FormFieldControlType.DropDown&&(i=this.getFieldControl(null,r),i&&i._lbx)&&(i._lbx.disabled=t,r=null,i=null)},addFieldCheckBoxClickEvent:function(n,t){var i=this.getFieldControl(n);i&&(i.addClick(t),i=null)},enableFieldCheckBox:function(n,t){var i=this.getField(n);i&&Number.parseInvariant(i.Type.toString())==$R_ENUM$FormFieldControlType.CheckBox&&($find(i.ControlID).enableButton(t),i=null)},clearField:function(n,t){t||(t=this.getField(n));switch(Number.parseInvariant(t.Type.toString())){case $R_ENUM$FormFieldControlType.TextBox:$get(t.ControlID).value="";break;case $R_ENUM$FormFieldControlType.CheckBox:$find(t.ControlID).setChecked(!1);break;case $R_ENUM$FormFieldControlType.DropDown:$find(t.ControlID).clearDropDown();break;case $R_ENUM$FormFieldControlType.StarRating:$find(t.ControlID).setRating(0);break;case $R_ENUM$FormFieldControlType.TimeSelect:$find(t.ControlID).setValue("09:00");break;case $R_ENUM$FormFieldControlType.Literal:$get(t.ControlID).innerHTML="";break;case $R_ENUM$FormFieldControlType.FileUpload:$find(t.ControlID).setValue("");break;case $R_ENUM$FormFieldControlType.Combo:$find(t.ControlID).reset()}},setFieldToDefault:function(n,t){t||(t=this.getField(n));switch(Number.parseInvariant(t.Type.toString())){case $R_ENUM$FormFieldControlType.TextBox:t.DefaultValue==undefined&&(t.DefaultValue="");break;case $R_ENUM$FormFieldControlType.CheckBox:t.DefaultValue==undefined&&(t.DefaultValue=!1);break;case $R_ENUM$FormFieldControlType.DropDown:t.DefaultValue==undefined&&(t.DefaultValue=$find(t.ControlID)._strNoValue_Value);break;case $R_ENUM$FormFieldControlType.StarRating:t.DefaultValue==undefined&&(t.DefaultValue=0);break;case $R_ENUM$FormFieldControlType.TimeSelect:t.DefaultValue==undefined&&(t.DefaultValue="09:00");break;case $R_ENUM$FormFieldControlType.FileUpload:$find(t.ControlID).reset();return;case $R_ENUM$FormFieldControlType.Combo:$find(t.ControlID).reset();return}Number.parseInvariant(t.Type.toString())!=$R_ENUM$FormFieldControlType.Literal&&this.setFieldValue(null,t.DefaultValue,t)},autoValidateFields:function(){for(var n,t=!0,i=0,r=this._aryFields.length;i<r;i++)n=this._aryFields[i],n&&(Boolean.parse(n.Required)&&(this.checkFieldEntered(null,n)||(t=!1)),Boolean.parse(n.CheckForValidEmail)&&(this.checkFieldValidEmail(null,n)||(t=!1)),Boolean.parse(n.CheckForValidURL)&&(this.checkFieldValidURL(null,n)||(t=!1)),Boolean.parse(n.IsNumeric)&&(this.checkFieldNumeric(null,n)||(t=!1))),n=null;return t||this.showError(!0),t},resetFormFields:function(){for(var t,n=0,i=this._aryFields.length;n<i;n++)t=this._aryFields[n],t&&this.resetFieldError(null,t),t=null},setFormFieldsToDefaults:function(){for(var t,n=0,i=this._aryFields.length;n<i;n++)t=this._aryFields[n],this.setFieldToDefault(null,t),t=null},storeOriginalFieldValues:function(){for(var n,t=0,i=this._aryFields.length;t<i;t++)n=this._aryFields[t],n&&(n.OriginalValue=Number.parseInvariant(n.Type.toString())==$R_ENUM$FormFieldControlType.DropDown?$find(n.ControlID)._initialValue:this.getFieldValue(null,n)),n=null},hasFieldChanged:function(n,t){var i,r;if(t||(t=this.getField(n)),!t)return!1;if(t.OriginalValue==undefined)return!0;if(i=this.getFieldValue(null,t),t.Type==$R_ENUM$FormFieldControlType.DropDown){if(r=this.getFieldControl(null,t),r&&r.countOptions()==0)return!1;(i===""||i==null)&&(i=r._strNoValue_Value)}return i==null&&(i=""),i.toString()!=t.OriginalValue.toString()},getChangedFields:function(n){var r="",u,t,e,i,f;for(Array.clear(this._aryChangedFieldIDs),u=!0,n&&(u=!1),t=0,e=u?this._aryFields.length:n.length;t<e;t++)i=u?this._aryFields[t]:this.getField(n[t]),i&&this.hasFieldChanged(null,i)&&(r!=""&&(r+="||"),f=this.getFieldTitleResource("",i),r+=f,Array.add(this._aryChangedFieldIDs,f)),i=null;return r},changeTitle:function(n){$R_FN.setInnerHTML(this._h4,n)},changeExplanation:function(n){$R_FN.setInnerHTML(this._pnlExplain,n)},changeMode:function(n){this._mode=n;this.onModeChanged()},showError:function(n,t){$R_FN.showElement(this._pnlValidateError,n);n&&($R_FN.showElement(this._pnlSaving,!1),$R_FN.showElement(this._pnlSavedOK,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlContentInner,!0),$R_FN.showElement(this._pnlLinksHolder,!0),$R_FN.showElement(this._pnlFooterLinksHolder,!0),this._ctlRelatedNugget&&(this._ctlRelatedNugget.control.showLoading(!1),this._ctlRelatedNugget.control.showRefresh(!0)),t||(t=""),this._pnlValidateErrorText.innerHTML=t)},showLoading:function(n){$R_FN.showElement(this._pnlLoading,n);this._ctlRelatedNugget&&this._ctlRelatedNugget.control.showLoading(n);n&&($R_FN.showElement(this._pnlSaving,!1),$R_FN.showElement(this._pnlSavedOK,!1),$R_FN.showElement(this._pnlContentInner,!1),$R_FN.showElement(this._pnlNotes,!1))},showSaving:function(n){$R_FN.showElement(this._pnlSaving,n);this._ctlRelatedNugget&&this._ctlRelatedNugget.control.showLoading(n);n&&(this._ctlRelatedNugget&&this._ctlRelatedNugget.control.showRefresh(!0),$R_FN.showElement(this._pnlLinksHolder,!1),$R_FN.showElement(this._pnlFooterLinksHolder,!1),$R_FN.showElement(this._pnlSavedOK,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlNotes,!1),$R_FN.showElement(this._pnlContentInner,!1))},showSavedOK:function(n){$R_FN.showElement(this._pnlSavedOK,n);n&&($R_FN.showElement(this._pnlSaving,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlNotes,!1),$R_FN.showElement(this._pnlContentInner,!1))},showContent:function(n){$R_FN.showElement(this._pnlContent,n);n?(this._ctlRelatedNugget&&this._ctlRelatedNugget.control.showLoading(!1),$R_FN.showElement(this._pnlNotes,this._blnShowRequiredFieldsExplanation)):$R_FN.showElement(this._pnlNotes,!1)},showInnerContent:function(n){$R_FN.showElement(this._pnlContentInner,n);n&&($R_FN.showElement(this._pnlLinksHolder,!0),$R_FN.showElement(this._pnlFooterLinksHolder,!0),$R_FN.showElement(this._pnlContent,!0),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlSaving,!1),$R_FN.showElement(this._pnlSavedOK,!1))},showNuggetError:function(n,t){this._ctlRelatedNugget&&this._ctlRelatedNugget.control.showError(n,t)},addSaveClick:function(n){$R_IBTN.addClick(this._ibtnSave,n);this._ibtnSave_Footer&&$R_IBTN.addClick(this._ibtnSave_Footer,n)},doFormSave:function(){if(this.areAnyDropDownsLoading()){this.flickerSaveButtons();return}this.showError(!0);this.showSaving(!0);this.showError(!1);this.onSave()},flickerSaveButtons:function(){this.enableSaveButtons(!1);setTimeout(Function.createDelegate(this,this.finishFlickerSaveButtons),400)},finishFlickerSaveButtons:function(){this.enableSaveButtons(!0)},enableSaveButtons:function(n){this._ibtnSave&&$R_IBTN.enableButton(this._ibtnSave,n);this._ibtnSave_Footer&&$R_IBTN.enableButton(this._ibtnSave_Footer,n)},areAnyDropDownsLoading:function(){for(var n,i=!1,t=0,r=this._aryFields.length;t<r;t++){if(n=this._aryFields[t],n&&Number.parseInvariant(n.Type.toString())==$R_ENUM$FormFieldControlType.DropDown&&$find(n.ControlID).isLoading()){i=!0;break}n=null}return i},addCancelClick:function(n){$R_IBTN.addClick(this._ibtnCancel,n);this._ibtnCancel_Footer&&$R_IBTN.addClick(this._ibtnCancel_Footer,n)},gotoStep:function(n){this._ctlMultiStep&&(this._ctlMultiStep.gotoStep(n),this.showError(!1))},nextStep:function(){this._ctlMultiStep&&(this._ctlMultiStep.nextStep(),this.showError(!1))},prevStep:function(){this._ctlMultiStep&&(this._ctlMultiStep.prevStep(),this.showError(!1))},resetSteps:function(){this.gotoStep(1)},disableStep:function(n){this._ctlMultiStep&&this._ctlMultiStep.disableStep(n)},scrollPageToForm:function(){this._intPreviousScrollPosition=$R_FN.windowScrollPosition();this._ctlRelatedNugget&&this._ctlRelatedNugget.control.scrollPageToForm()},scrollPageBackToPositionBeforeForm:function(){$R_FN.finishScrollPageToElement(this._intPreviousScrollPosition)},setSavingMessage:function(n){$R_FN.setInnerHTML(this._pnlSaving,n)},setSavedOKMessage:function(n){$R_FN.setInnerHTML(this._pnlSavedOK,n)},getFieldTitleResource:function(n,t){t||(t=this.getField(n));var i="";return t&&(i=$get(t.ID).getAttribute("titleResource")),t=null,i},showFormWithoutError:function(n){$R_FN.showElement(this._pnlValidateError,!1);n&&($R_FN.showElement(this._pnlSaving,!1),$R_FN.showElement(this._pnlSavedOK,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlContentInner,!0),$R_FN.showElement(this._pnlLinksHolder,!0),$R_FN.showElement(this._pnlFooterLinksHolder,!0),this._ctlRelatedNugget&&(this._ctlRelatedNugget.control.showLoading(!1),this._ctlRelatedNugget.control.showRefresh(!0)))},checkControlEntered:function(n,t){var i=!0;switch(t){case"TextBox":i=$R_FN.isEntered($get(n).value);break;case"DropDown":i=!$find(n).isSetAsNoValue();break;case"FileUpload":i=$find(n).checkEntered();break;case"Combo":i=$find(n).checkEntered()}return i?document.getElementById(n).style.border="":this.setControlInError(n,!0,$R_RES.RequiredFieldMissingMessage),i},setControlInError:function(n,t){document.getElementById(n).style.border=t?"1px solid red":""},getControlValue:function(n,t){var i;switch(t){case"TextBox":i=$get(n).value;break;case"CheckBox":i=$find(n)._blnChecked;break;case"DropDown":i=$find(n).getValue();break;case"StarRating":i=$find(n)._intCurrentRating;break;case"TimeSelect":i=$find(n).getValue();break;case"Literal":i=$get(n).innerHTML;break;case"FileUpload":i=$find(n).getValue();break;case"Combo":i=$find(n).getValue()}return i},setControlValue:function(n,t,i,r){typeof i=="string"&&(i=i.replace(/(<br \/>)|(<br\/>)|(<br>)/g,"\n"));i==null&&(i="");i=="&nbsp;"&&(i="");switch(n){case"TextBox":$get(t).value=$R_FN.setCleanTextValue(i.toString());break;case"CheckBox":$find(t).setChecked(i);break;case"DropDown":$find(t).setValue(r);break;case"StarRating":$find(t).setRating(i);break;case"TimeSelect":$find(t).setValue(i);break;case"Literal":$get(t).innerHTML=i;break;case"Combo":$find(t).setValue(i,r)}}};Rebound.GlobalTrader.Site.Controls.Forms.Base.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.Base",Sys.UI.Control,Sys.IDisposable);