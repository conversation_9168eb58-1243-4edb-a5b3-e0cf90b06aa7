﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public class APIExternalLinksDetails
    {
        #region Constructors
        public APIExternalLinksDetails() { }
        #endregion

        #region Properties
        /// <summary>
        /// OfferId (from Table)
        /// </summary>
        public System.Int32 FElectronicsId { get; set; }
        /// <summary>
        /// FullPart (from Table)
        /// </summary>
        public System.String FullPart { get; set; }
        /// <summary>
        /// SupplierEpo  hard code data
        /// </summary>
        public System.String SupplierEpo { get; set; }
        /// <summary>
        /// Part (from Table)
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo (from Table)
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode (from Table)
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// ProductNo (from Table)
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// PackageNo (from Table)
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity (from Table)
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price (from Table)
        /// </summary>
        public System.Double Price { get; set; }
        public System.Int32 EPOCurrencyNo { get; set; }
        /// <summary>
        /// OriginalEntryDate (from Table)
        /// </summary>
        public System.DateTime? OriginalEntryDate { get; set; }
        /// <summary>
        /// Salesman (from Table)
        /// </summary>
        public System.Int32? Salesman { get; set; }
        /// <summary>
        /// SupplierNo (from Table)
        /// </summary>
        public System.Int32 SupplierNo { get; set; }
        /// <summary>
        /// CurrencyNo (from Table)
        /// </summary>
        public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// ROHS (from Table)
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// UpdatedBy (from Table)
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// DLUP (from Table)
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// OfferStatusNo (from Table)
        /// </summary>
        //public System.Int32? OfferStatusNo { get; set; }
        ///// <summary>
        ///// OfferStatusChangeDate (from Table)
        ///// </summary>
        //public System.DateTime? OfferStatusChangeDate { get; set; }
        ///// <summary>
        ///// OfferStatusChangeLoginNo (from Table)
        ///// </summary>
        //public System.Int32? OfferStatusChangeLoginNo { get; set; }
        ///// <summary>
        ///// EpoStatusNo (from Table)
        ///// usp_ipobom_source_Epo
        /// </summary>
        //public System.Int32? EpoStatusNo { get; set; }
        ///// <summary>
        ///// EpoStatusChangeDate (from Table)
        ///// usp_ipobom_source_Epo
        ///// </summary>
        //public System.DateTime? EpoStatusChangeDate { get; set; }
        ///// <summary>
        ///// EpoStatusChangeLoginNo (from Table)
        ///// usp_ipobom_source_Epo
        ///// </summary>
        //public System.Int32? EpoStatusChangeLoginNo { get; set; }
        ///// <summary>
        ///// SupplierName (from Table)
        ///// </summary>
        public System.String SupplierName { get; set; }
        /// <summary>
        /// Notes (from Table)
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// ManufacturerName (from Table)
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// ProductName (from Table)
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// PackageName (from Table)
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// ClientNo (from Table)
        /// </summary>
        public System.Int32? ClientNo { get; set; }
        /// <summary>
        /// ManufacturerCode (from usp_source_Offer)
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// CurrencyCode (from usp_source_Offer)
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// CurrencyDescription (from usp_source_Offer)
        /// </summary>
        public System.String CurrencyDescription { get; set; }
        /// <summary>
        /// SupplierEmail (from usp_source_Offer)
        /// </summary>
        public System.String SupplierEmail { get; set; }
        /// <summary>
        /// SalesmanName (from usp_source_Offer)
        /// </summary>
        public System.String SalesmanName { get; set; }
        /// <summary>
        /// OfferStatusChangeEmployeeName (from usp_source_Offer)
        /// </summary>
        public System.String OfferStatusChangeEmployeeName { get; set; }
        /// <summary>
        /// OfferStatusChangeEmployeeName (from usp_ipobom_source_Epo)
        /// </summary>
        public System.String EpoStatusChangeEmployeeName { get; set; }
        /// <summary>
        /// UpliftPrice (from usp_ipobom_source_Epo)
        /// </summary>
        public System.Double UpliftPrice { get; set; }

        /// <summary>
        /// ClientId (from usp_source_Offer)
        /// </summary>
        public System.Int32 ClientId { get; set; }
        /// <summary>
        /// ClientName (from usp_source_Offer)
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// ClientDataVisibleToOthers (from usp_source_Offer)
        /// </summary>
        public System.Boolean? ClientDataVisibleToOthers { get; set; }
        public System.Int32? CustomerRequirementId { get; set; }

        //[001] code start
        /// <summary>
        /// SupplierType
        /// </summary>
        public System.String SupplierType { get; set; }
        //[001] code end
        public System.String ClientCode { get; set; }
        public System.String MSL { get; set; }
        public System.String SPQ { get; set; }
        public System.String MOQ { get; set; }
        public System.String LeadTime { get; set; }
        public System.String RoHSStatus { get; set; }
        //public System.String FactorySealed { get; set; }
        //public System.Int32 IPOBOMNo { get; set; }
        public System.String SupplierTotalQSA { get; set; }
        public System.String SupplierMOQ { get; set; }
        public System.String SupplierLTB { get; set; }
        public System.Boolean IsSourcingHub { get; set; }
        public System.String ProductDescription { get; set; }
        public System.Boolean? ProductInactive { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.String productNameDescrip { get; set; }
        public System.Boolean isIncludeAltPart { get; set; }
        public System.Int32? RowNum { get; set; }
        public System.Int32? TotalCount { get; set; }
        public System.String PackageDescription { get; set; }

        public System.String ApiSourceName { get; set; }
        #endregion

        //public System.Int32? BomID { get; set; }
        public System.String CustomerReqCHK { get; set; }
        public System.String partSearch { get; set; }
        public System.String isMonths { get; set; }
        public System.String isTime { get; set; }
        public System.Int32? VednerTypeID { get; set; }
        public System.String ddlCurrency { get; set; }
        public System.String PartMatchRd { get; set; }
        public System.String ExactPartRd { get; set; }
        public System.String chkIncludeAltPart { get; set; }
        public System.String chkManufacture { get; set; }
        public System.Int32? NoOfTopRecord { get; set; }
        public System.String Description { get; set; }

        //[001] code start
        /// <summary>
        /// Log Details CrossMatch
        /// </summary>
        public System.String LogDetails { get; set; }
        public System.String SupplierMessage { get; set; }
        //[001] code end
        //add from hear
        /// <summary>
        /// SourcingResultId
        /// </summary>
        public System.Int32 SourcingResultId { get; set; }
        /// <summary>
        /// CustomerRequirementNo
        /// </summary>
        public System.Int32 CustomerRequirementNo { get; set; }
        /// <summary>
        /// SourcingTable
        /// </summary>
        public System.String SourcingTable { get; set; }
        /// <summary>
        /// SourcingTableItemNo
        /// </summary>
        public System.Int32? SourcingTableItemNo { get; set; }
        /// <summary>
        /// TypeName
        /// </summary>
        public System.String TypeName { get; set; }
        public System.Double? SupplierPrice { get; set; }
        public System.String POHubSupplierName { get; set; }
        public System.Int32? POHubCompanyNo { get; set; }
        public System.String IsPoHub { get; set; }
        public System.Int32? POHubReleaseBy { get; set; }

        public System.String ClientSupplierName { get; set; }
        public System.Int32? ClientCompanyNo { get; set; }
        /// <summary>
        /// UPLiftPrice
        /// </summary>
        public System.Double? UPLiftPrice { get; set; }
        public System.Int32 ClientCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }
        public System.Double? ConvertedSourcingPrice { get; set; }
        public System.String MslSpqFactorySealed { get; set; }
        public double? EstimatedShippingCost { get; set; }
        public string SupplierManufacturerName { get; set; }
        public string SupplierDateCode { get; set; }
        public string SupplierPackageType { get; set; }
        public string SupplierProductType { get; set; }
        public string SupplierNotes { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public System.Boolean? SourcingRelease { get; set; }
        public bool IsClosed { get; set; }
        public string ROHSStatus { get; set; }
        public string RegionName { get; set; }
        public System.Int32? RegionNo { get; set; }
        public string HubRFQName { get; set; }
        public System.Int32? HubRFQNo { get; set; }
        public bool IsSoCreated { get; set; }
        public string TermsName { get; set; }
        public bool IsApplyPOBankFee { get; set; }
        public string SourceRef { get; set; }
        public bool IsReleased { get; set; }
        public bool Recalled { get; set; }
        public string SourcingNotes { get; set; }
        public double? OriginalPrice { get; set; }
        public System.Int32? ActualCurrencyNo { get; set; }
        public System.String ActualCurrencyCode { get; set; }
        public System.Int32 SourcingReleasedCount { get; set; }
        public System.String MSLLevelText { get; set; }
        public double? ActualPrice { get; set; }
        public double? SupplierPercentage { get; set; }
        //[001] start
        /// <summary>
        /// SupplierWarranty
        /// </summary>
        public System.Int32? SupplierWarranty { get; set; }
        /// <summary>
        /// NonPreferredCompany
        /// </summary>
        public System.Boolean? NonPreferredCompany { get; set; }
        //[001] end
        //[003] start
        public System.Boolean IsTestingRecommended { get; set; }
        //[003] end
        public System.Boolean? IsImageAvailable { get; set; }
        /// <summary>
        /// PriorityId (from Table)
        /// </summary>
        public System.Int32 PriorityId { get; set; }
        /// <summary>
        /// PriorityNo 
        /// </summary>
        public System.Int32 PriorityNo { get; set; }
        public System.Int32 IHSCountryOfOriginNo { get; set; }
        public System.String IHSCountryOfOriginName { get; set; }
        public System.Int32 CountryOfOriginNo { get; set; }
        public System.String CountryOfOriginName { get; set; }
        public System.Int32? ReReleased { get; set; }
        public System.Boolean? PartWatchMatch { get; set; }
        public System.Boolean? DiffrentClientOffer { get; set; }
        public System.String VirtualCostPrice { get; set; }
        public System.DateTime? PublishDate { get; set; }
        public System.DateTime? GTDate { get; set; }
        public System.String ECCN { get; set; }
        public System.String UnitCostPrice { get; set; }
        public System.String Reference { get; set; }
        public System.String PackageType { get; set; }
        public System.String ApiShortName { get; set; }
        //public System.String Licensekey { get; set; }
        //public System.String HostName { get; set; }
        public System.String ApiName { get; set; }
        public System.Int32 ApiURLKeyId { get; set; }
        public int curpage { get; set; }
        public bool OfferAddFlag { get; set; }

    }
}
