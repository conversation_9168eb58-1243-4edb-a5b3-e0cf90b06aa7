///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//Marker     Date           Changed By      Remarks
//[001]      Umendra Gupta   21-Jan-2019  Add View Tree Button.
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.initializeBase(this, [element]);
	this._intCreditID = -1;
	this._IsPOHub = false;
	this._InvoiceNo = -1;
	this._blnExported = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.prototype = {

	get_intCreditID: function() { return this._intCreditID; }, set_intCreditID: function(value) { if (this._intCreditID !== value) this._intCreditID = value; },
	get_ibtnEdit: function() { return this._ibtnEdit; }, 	set_ibtnEdit: function(value) { if (this._ibtnEdit !== value)  this._ibtnEdit = value; }, 
	get_ibtnExport: function () { return this._ibtnExport; }, set_ibtnExport: function (value) { if (this._ibtnExport !== value) this._ibtnExport = value; },
	get_ibtnRelease: function () { return this._ibtnRelease; }, set_ibtnRelease: function (value) { if (this._ibtnRelease !== value) this._ibtnRelease = value; },
    //[002] start
	get_ibtnViewTree: function () { return this._ibtnViewTree; }, set_ibtnViewTree: function (value) { if (this._ibtnViewTree !== value) this._ibtnViewTree = value; },
    //[002] end
	get_IsDiffrentClient: function () { return this._IsDiffrentClient; }, set_IsDiffrentClient: function (value) { if (this._IsDiffrentClient !== value) this._IsDiffrentClient = value; },
	get_IsGSAEditPermission: function () { return this._IsGSAEditPermission; }, set_IsGSAEditPermission: function (value) { if (this._IsGSAEditPermission !== value) this._IsGSAEditPermission = value; },
	get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (value) { if (this._IsGSA !== value) this._IsGSA = value; },

	initialize: function () {
		Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.callBaseMethod(this, "initialize");	
		
		//nugget events
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
		if (this._IsDiffrentClient == true) {
			if (this._IsGSA == true) {
				if (this._IsGSAEditPermission == true) {
					$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
				}
				else {
					$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").hide();
				}
			}
			else {
				$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
			}

		}
		else {
			$("#ctl00_cphMain_ctlLines_ctlDB_pnlLinks").show();
		}
		//edit form
		if (this._ibtnEdit) {
			$R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
			this._frmEdit = $find(this._aryFormIDs[0]);
			this._frmEdit._intCreditID = this._intCreditID;
			this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
			this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
		}
		
		if (this._ibtnExport || this._ibtnRelease) {
		    if (this._ibtnExport) $R_IBTN.addClick(this._ibtnExport, Function.createDelegate(this, this.showExportForm));
		    if (this._ibtnRelease) $R_IBTN.addClick(this._ibtnRelease, Function.createDelegate(this, this.showReleaseForm));
		    this._frmExport = $find(this._aryFormIDs[1]);
		    this._frmExport.addCancel(Function.createDelegate(this, this.hideExportForm));
		    this._frmExport.addSaveComplete(Function.createDelegate(this, this.saveExportComplete));
		    this._frmExport.addNotConfirmed(Function.createDelegate(this, this.hideExportForm));
		}
	    //[001] end
		if (this._ibtnViewTree)
		    $R_IBTN.addClick(this._ibtnViewTree, Function.createDelegate(this, this.OpenDocTree));
	    //[002] code end
		//initial action
		if (!this._blnIsNoDataFound && !this._blnHasInitialData) this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
		if (this._frmEdit) this._frmEdit.dispose();
		this._IsDiffrentClient = null;
		this._IsGSAEditPermission = null;
		this._IsGSA = null;
		this._intCreditID = null;
		this._ibtnEdit = null;
		this._frmEdit = null;
		this._ibtnExport = null;
		this._ibtnRelease = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.callBaseMethod(this, "dispose");
	},

	getData: function() { 
		this.getData_Start();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CreditMainInfo");
		obj.set_DataObject("CreditMainInfo");
		obj.set_DataAction("GetData");
		obj.addParameter("id", this._intCreditID);
		obj.addDataOK(Function.createDelegate(this, this.getDataOK));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getDataOK: function(args) { 
	    var res = args._result;
	   // alert(res.InvoiceNo);
		this.setFieldValue("ctlCustomerName", $RGT_nubButton_Company(res.CustomerNo, res.isClientInvoice == true ? res.RefClientName : res.CustomerName, null, null, null, res.CustomerAdvisoryNotes));
		this.setFieldValue("ctlContact", $RGT_nubButton_Contact(res.ContactNo,res.isClientInvoice == true ?" .... ": res.Contact));
		this.setFieldValue("ctlSalesperson", $R_FN.setCleanTextValue(res.Salesman));
		this.setFieldValue("hidSalespersonNo", res.SalesmanNo);
		this.setFieldValue("ctlSalesperson2", $R_FN.setCleanTextValue(res.Salesman2));
		this.setFieldValue("hidSalesperson2No", res.Salesman2No);
		this.setFieldValue("hidSalesman2Percent", res.Salesman2Percent);
		this.setFieldValue("ctlRaiser", res.Raiser);
		this.setFieldValue("ctlDivision", res.Division);
		this.setFieldValue("ctlCreditDate", res.CreditDate);
		this.setFieldValue("ctlReferenceDate", res.ReferenceDate);
		this.setFieldValue("ctlInvoice", $RGT_nubButton_Invoice(res.InvoiceNo, res.Invoice));
		this.setFieldValue("ctlSalesOrder", $RGT_nubButton_SalesOrder(res.SalesOrderNo, res.SalesOrder));
	    this.setFieldValue("ctlCustomerRMA", $RGT_nubButton_CRMA(res.CustomerRMANo, res.CustomerRMA));
		this.setFieldValue("ctlTax", res.Tax);
		this.setFieldValue("ctlFreight", res.Freight);
		this.setFieldValue("ctlShippingCost", res.ShippingCost);
		this.setFieldValue("ctlShipVia", res.ShipVia);
		this.setFieldValue("ctlIncoterm", $R_FN.setCleanTextValue(res.Incoterm));
		this.setFieldValue("ctlShippingAccountNo", res.ShippingAccountNo);
		this.setFieldValue("ctlCurrency", res.Currency);
		this.setFieldValue("ctlCustomerPO", res.CustomerPO);
		this.setFieldValue("ctlCustomerDebit", $R_FN.setCleanTextValue(res.CustomerDebit));
		this.setFieldValue("ctlCustomerReturn", $R_FN.setCleanTextValue(res.CustomerReturn));
		this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes));
		this.setFieldValue("ctlInstructions", $R_FN.setCleanTextValue(res.Instructions));

		this.setFieldValue("hidInvoiceNumber", res.Invoice);
		this.setFieldValue("hidCRMANumber", res.CustomerRMA);
		this.setFieldValue("hidCurrencyNo", res.CurrencyNo);
		this.setFieldValue("hidCurrencyCode", res.CurrencyCode);
		this.setFieldValue("hidCreditNumber", res.CreditNumber);
		this.setFieldValue("hidCustomerName", $R_FN.setCleanTextValue(res.CustomerName));
		this.setFieldValue("hidContactName", res.Contact);
		this.setFieldValue("hidDivisionNo", res.DivisionNo);
		this.setFieldValue("hidSalesmanNo", res.SalesmanNo);
		this.setFieldValue("hidRaiserNo", res.RaisedBy);
		this.setFieldValue("hidSalesOrderNumber", res.SalesOrder);

		this.setFieldValue("ctlClientInvoice", $RGT_nubButton_ClientInvoice(res.InvoiceNo, res.Invoice))
		this.showField("ctlClientInvoice", res.isClientInvoice);

		this.showField("ctlInvoice", !res.isClientInvoice);

		this.setFieldValue("hidTaxNo", res.TaxNo);
		this.setFieldValue("hidShipViaNo", res.ShipViaNo);
		this.setFieldValue("hidFreightRaw", res.FreightVal);
		this.setFieldValue("hidShippingCostRaw", res.ShippingCostVal);
		this.setFieldValue("hidIncotermNo", res.IncotermNo);
		this.setFieldValue("ctlCreditNoteBankFee", res.CreditNoteBankFee);
		this.setFieldValue("ctlRefNo", res.RefNumber);
		this.showField("ctlRefNo", res.RefNumber > 0);
		this.setFieldValue("hidIsClientInvoice", res.isClientInvoice);
		this.setFieldValue("hidClientInvoiceLineNo", res.ClientInvoiceLineNo);
		this.setFieldValue("hidHubLogin", res.HubLogin);
		this.setFieldValue("ctlExchangeRate", res.ExchangeRate);
		//RP-2136 code start
		this.setFieldValue("ctlSalesDivision", res.invSalesDivisionName);
		this.setFieldValue("ctlDivisionHeader", res.invDivisionHeaderName);
		this.setFieldValue("hidSalesDivision", res.invSalesDivisionNo);
		this.setFieldValue("hidDivisionHeaderNo", res.InvDivisionHeaderNo);
		//RP-2136 code end
		this._IsPOHub = res.IsPoHub;
		this._InvoiceNo = res.InvoiceNo;
		this._blnExported = res.isExport;
		this.enableEditButtons(true);
		if (res.isClientInvoice && res.HubLogin == false) {
		    if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
		}
        
		this.setDLUP(res.DLUP);
		this.getDataOK_End();
	},

	getDataError: function(args) {
		this.showError(true, args.get_ErrorMessage());
	},

	enableEditButtons: function (bln) {
	    if (bln) {
	        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._blnExported);
	        if (this._ibtnExport) $R_IBTN.enableButton(this._ibtnExport, !this._blnExported);
	        if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, this._blnExported);
	    } //else {
	    //    if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
	    //    if (this._ibtnExport) $R_IBTN.enableButton(this._ibtnExport, false);
	    //    if (this._ibtnRelease) $R_IBTN.enableButton(this._ibtnRelease, false);
	    //}
	},
	showExportForm: function () {
	    this.doShowExportForm("EXPORT");
	},

	showReleaseForm: function () {
	    this.doShowExportForm("RELEASE");
	},

	doShowExportForm: function (strMode) {
	    this._frmExport.changeMode(strMode);
	    this._frmExport._intCreditID = this._intCreditID; 
	    this._frmExport.setFieldValue("ctlCreditNote", this.getFieldValue("hidCreditNumber"));
	    this._frmExport.setFieldValue("ctlCustomer", this.getFieldValue("hidCustomerName"));
	    this.showForm(this._frmExport, true);
	},

	hideExportForm: function () {
	    this.showForm(this._frmExport, false);
	},

	saveExportComplete: function () {
	    this.showForm(this._frmExport, false);
	    this.showContentLoading(false);
	    this.getData();
	    this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
	   // this.onPotentialStatusChange();
	},

	showEditForm: function() {
		this._frmEdit._intLineID = this._intLineID;
		this._frmEdit.setFieldValue("ctlCustomer", this.getFieldValue("hidCustomerName"));
		this._frmEdit.setFieldValue("ctlContact", this.getFieldValue("hidContactName"));
		this._frmEdit.setFieldValue("ctlDivision", this.getFieldValue("hidDivisionNo"));
		this._frmEdit.setFieldValue("ctlSalesman", this.getFieldValue("hidSalespersonNo"));
		this._frmEdit.setFieldValue("ctlSalesman2", this.getFieldValue("hidSalesperson2No"));
		this._frmEdit.setFieldValue("ctlRaisedBy", this.getFieldValue("hidRaiserNo"));
		this._frmEdit.setFieldValue("ctlShipVia", this.getFieldValue("hidShipViaNo"));
		this._frmEdit.setFieldValue("ctlSalesman2Percent", this.getFieldValue("hidSalesman2Percent"));
		this._frmEdit.setFieldValue("ctlRaisedBy", this.getFieldValue("hidRaiserNo"));
		this._frmEdit.setFieldValue("ctlCreditDate", this.getFieldValue("ctlCreditDate"));
		this._frmEdit.setFieldValue("ctlReferenceDate", this.getFieldValue("ctlReferenceDate"));
		this._frmEdit.setFieldValue("ctlCustomerPO", this.getFieldValue("ctlCustomerPO"));
		this._frmEdit.setFieldValue("ctlCustomerReturn", this.getFieldValue("ctlCustomerReturn"));
		this._frmEdit.setFieldValue("ctlCustomerDebit", this.getFieldValue("ctlCustomerDebit"));
		this._frmEdit.setFieldValue("ctlInvoice", this.getFieldValue("hidInvoiceNumber"));
		this._frmEdit.setFieldValue("ctlSalesOrder", this.getFieldValue("hidSalesOrderNumber"));
		this._frmEdit.setFieldValue("ctlCustomerRMA", this.getFieldValue("hidCRMANumber"));
		this._frmEdit.setFieldValue("ctlTax", this.getFieldValue("hidTaxNo"));
		this._frmEdit.setFieldValue("ctlShipVia", this.getFieldValue("hidShipViaNo"));
		this._frmEdit.setFieldValue("ctlCurrency", this.getFieldValue("hidCurrencyNo"));
		this._frmEdit.setFieldValue("ctlFreight", this.getFieldValue("hidFreightRaw"));
		this._frmEdit.setFieldValue("ctlShippingCost", this.getFieldValue("hidShippingCostRaw"));
		this._frmEdit.setFieldValue("ctlNotes", this.getFieldValue("ctlNotes"));
		this._frmEdit.setFieldValue("ctlInstructions", this.getFieldValue("ctlInstructions"));
		this._frmEdit.setFieldValue("ctlIncoterm", this.getFieldValue("hidIncotermNo"));
		this._frmEdit.setFieldValue("ctlCreditNoteBankFee", this.getFieldValue("ctlCreditNoteBankFee"));
		$R_FN.setInnerHTML(this._frmEdit._lblFreight_Currency, this.getFieldValue("hidCurrencyCode"));
		this._frmEdit.showField("ctlRaisedByLbl",!this._IsPOHub);
        this._frmEdit.showField("ctlRaisedBy",this._IsPOHub);
        this._frmEdit._IsPOHub=this._IsPOHub;
        this._frmEdit._hidRaisedByNo=this.getFieldValue("hidRaiserNo")
        this._frmEdit.setFieldValue("ctlRaisedBy",this.getFieldValue("hidRaiserNo"));
        this._frmEdit.setFieldValue("ctlRaisedByLbl", this.getFieldValue("ctlRaiser"));
        this._frmEdit.showField("ctlShipViaLbl",!this._IsPOHub);
        this._frmEdit.showField("ctlShipVia",this._IsPOHub);
        this._frmEdit._hidShipViaNo=this.getFieldValue("hidShipViaNo")
        this._frmEdit.setFieldValue("ctlShipViaLbl", this.getFieldValue("ctlShipVia"));
        this._frmEdit.showField("ctlSalesman2",this._IsPOHub);
        this._frmEdit.showField("ctlSalesman2Percent", this._IsPOHub);
		this._frmEdit.setFieldValue("ctlExchangeRate", this.getFieldValue("ctlExchangeRate"));
		this._frmEdit.setFieldValue("ctlDivisionHeader", this.getFieldValue("hidDivisionHeaderNo"));
		
		this.showForm(this._frmEdit, true);
	},
	
	hideEditForm: function() {
		this.showForm(this._frmEdit, false);
	},
	
	cancelEdit: function() {
		this.hideEditForm();
	},
	
	saveEditComplete: function () {
	    this.hideEditForm();
	    this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
	    this.getData();
	},
    //[002] code start
	OpenDocTree: function () {
	    //$R_FN.openDocumentTree(this.getFieldValue("hidCreditNumber"), "CRD");
        $R_FN.openDocumentTree(this._intCreditID, "CRD", this.getFieldValue("hidCreditNumber"));
	}
    //[002] code end 
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
