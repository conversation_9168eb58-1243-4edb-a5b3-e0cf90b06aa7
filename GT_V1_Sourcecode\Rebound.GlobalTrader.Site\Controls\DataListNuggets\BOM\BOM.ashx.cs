using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class BOM : Base {

        /// <summary>
        /// Gets the main data
        /// </summary>
        /// 
        /// //Marker     changed by      date         Remarks
        //   [001]       Umendra Gupta    30/08/2018    Add ClientCode,ReceivedBy and SalesPerson field
        protected override void GetData()
        {
            Boolean isSearchFromRequirements = false;
            JsonObject jsn = new JsonObject();
            JsonObject jsnRowsArray = new JsonObject(true);
            int? bomStatus = GetFormValue_Int("BomStatus");
            int? assignedUser = GetFormValue_Int("PoHubBuyer");//BuyerId
            int? MyPageSize = GetFormValue_Int("MyPageSize");
            string selectedRadioValue = GetFormValue_String("SelectedRadio");
            //check view level
            ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");



            if ((!string.IsNullOrEmpty(selectedRadioValue) && selectedRadioValue == "Detail"))
            {
                isSearchFromRequirements = true;
            }
            if (isSearchFromRequirements == false)
            {
                List<Rebound.GlobalTrader.BLL.BOM> lst = BLL.BOM.DataListNugget(
                SessionManager.ClientID
                , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                , GetFormValue_NullableInt("SortIndex")
                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("PageSize", 10000)
                 , GetFormValue_StringForLikeSearch("Code", true)
                , GetFormValue_StringForLikeSearch("Name", true)
                , Convert.ToBoolean(SessionManager.IsPOHub)
                , Convert.ToBoolean(SessionManager.IsPOHub) ? GetFormValue_NullableInt("Client", null) : null
                , bomStatus
                , 1
                , assignedUser
                , null
                , GetFormValue_NullableInt("SalesPerson", null)//[001] 
                , GetFormValue_NullableInt("Companytypeid", null)
                , null
                , null
                ,null
                ,null
                , null
                , GetFormValue_NullableBoolean("IsAS6081Tab")
                , MyPageSize
                , false
            );
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
                foreach (BLL.BOM lt in lst)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lt.BOMId);
                    jsnRow.AddVariable("Code", lt.BOMCode);
                    jsnRow.AddVariable("Name", lt.BOMName);
                    jsnRow.AddVariable("Company", lt.CompanyName);
                    jsnRow.AddVariable("Inactive", lt.Inactive);
                    jsnRow.AddVariable("BOMStatus", lt.BOMStatus);
                    jsnRow.AddVariable("Date", Functions.FormatDate(lt.DLUP));
                    jsnRow.AddVariable("AssignedUser", lt.AssignedUser);
                    jsnRow.AddVariable("ClientCode", lt.ClientCode); //[001] 
                    jsnRow.AddVariable("RequestedBy", lt.Requestedby); //[001] 
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose();
                jsnRowsArray = null;
                jsn.Dispose();
                jsn = null;
                base.GetData();
            }
            else
            {
                List<Rebound.GlobalTrader.BLL.BOM> lst = BLL.BOM.DataListNugget(
               SessionManager.ClientID
               , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
               , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
               , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
               , GetFormValue_NullableInt("SortIndex")
               , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
               , GetFormValue_NullableInt("PageIndex", 0)
               , GetFormValue_NullableInt("PageSize", 10000)
                , GetFormValue_StringForLikeSearch("Code", true)
               , GetFormValue_StringForLikeSearch("Name", true)
               , Convert.ToBoolean(SessionManager.IsPOHub)
               , Convert.ToBoolean(SessionManager.IsPOHub) ? GetFormValue_NullableInt("Client", null) : null
               , bomStatus
               , 1
               , assignedUser
               , null
               , GetFormValue_NullableInt("SalesPerson", null)//[001] 
               , GetFormValue_NullableInt("Companytypeid", null)
               , null
               , null
               , null
               , null
               ,null
               , GetFormValue_NullableBoolean("IsAS6081Tab")
               , MyPageSize
               , true
           );
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
                foreach (BLL.BOM lt in lst)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lt.BOMId);
                    jsnRow.AddVariable("Code", lt.BOMCode);
                    jsnRow.AddVariable("Name", lt.BOMName);
                    jsnRow.AddVariable("Company", lt.CompanyName);
                    jsnRow.AddVariable("Inactive", lt.Inactive);
                    jsnRow.AddVariable("BOMStatus", lt.BOMStatus);
                    jsnRow.AddVariable("Date", Functions.FormatDate(lt.DLUP));
                    jsnRow.AddVariable("AssignedUser", lt.AssignedUser);
                    jsnRow.AddVariable("ClientCode", lt.ClientCode); //[001] 
                    jsnRow.AddVariable("RequestedBy", lt.Requestedby); //[001] 
                    jsnRow.AddVariable("Quantity", lt.Quantity);
                    jsnRow.AddVariable("Part", lt.Part);
                    jsnRow.AddVariable("CustomerRequirementId", lt.CustomerRequirementId);
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose();
                jsnRowsArray = null;
                jsn.Dispose();
                jsn = null;
                base.GetData();



            }
        }

        protected override void AddFilterStates() {
			AddFilterState("Code");
			AddFilterState("Name");
			base.AddFilterStates();
		}
	}
}
