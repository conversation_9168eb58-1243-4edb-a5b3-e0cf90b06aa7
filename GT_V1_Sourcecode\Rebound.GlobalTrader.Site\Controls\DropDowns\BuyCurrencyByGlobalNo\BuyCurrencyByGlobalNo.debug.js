///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//[001]      A<PERSON><PERSON> Singh     20-Aug-2018  Provision to add Global Security in Sales Order 
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo = function(element) { 
    Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.initializeBase(this, [element]);
    this._blnIsBuy = true;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.prototype = {
    get_intGlobalCurrencyNo: function () { return this._intGlobalCurrencyNo; }, set_intGlobalCurrencyNo: function (v) { if (this._intGlobalCurrencyNo !== v) this._intGlobalCurrencyNo = v; },
    get_blnIsBuy: function () { return this._blnIsBuy; }, set_blnIsBuy: function (v) { if (this._blnIsBuy !== v) this._blnIsBuy = v; },
    //[001] start
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },
    //[001] end
	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.callBaseMethod(this, "initialize");

		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intGlobalCurrencyNo = null;
	    this._blnIsBuy = null;
	    //[001] start
	    this._intGlobalLoginClientNo = null;
        //[001] end
		Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/BuyCurrencyByGlobalNo");
		this._objData.set_DataObject("BuyCurrencyByGlobalNo");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("GlobalNo", this._intGlobalCurrencyNo);
		this._objData.addParameter("blnBuy", this._blnIsBuy);
	    //[001] start
		this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);
        //[001] end
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Currencies) {
			for (var i = 0; i < result.Currencies.length; i++) {
				this.addOption(result.Currencies[i].Name, result.Currencies[i].ID, result.Currencies[i].Code);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BuyCurrencyByGlobalNo", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
