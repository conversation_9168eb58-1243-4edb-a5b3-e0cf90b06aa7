﻿
GO



CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_PurchaseOrderLine_candidates_for_SupplierRMA]    
@SupplierRMAId int  
  
AS 
begin   
--********************************************************************************    
--* SK 15.10.2009:    
--* - ensure that quantity has been received before it can be returned    
--* - also not all quantity has to be returned in at the same time       
--******************************************************************************** 

Select a.*,  a.QuantityReceived As QuantityToReturn    
 ,'' as Location  
 ,(SELECT CASE WHEN COUNT(npr.NPRId)>0 THEN 1 ELSE 0 END   
        FROM tbGoodsInLine gil JOIN tbNPR npr on gil.GoodsInLineId=npr.GoodsInLineNo   
        WHERE gil.PurchaseOrderLineNo=a.PurchaseOrderLineId) AS ISNPREXIST 
        
  ,(
  
  SELECT CASE WHEN COUNT(CustomerRMALineId) > 0 THEN 1 ELSE 0 END from tbCustomerRMALine where InvoiceLineNo in 
          (select InvoiceLineNo from tbInvoiceLineAllocation where PurchaseOrderLineNo=a.PurchaseOrderLineId)) AS ISCRMA 
into #POtemp
FROM dbo.vwPurchaseOrderLine a    
JOIN dbo.tbSupplierRMA b    
ON a.PurchaseOrderNo  = b.PurchaseOrderNo     
WHERE b.SupplierRMAId   = @SupplierRMAId    
AND  a.QuantityReceived  > 0  
    
SELECT a.*    
 , a.QuantityReceived As QuantityToReturn    
 ,'' as Location  
 ,ISNPREXIST 
 ,ISCRMA 
         
FROM #POtemp a    
JOIN dbo.tbSupplierRMA b    
 ON a.PurchaseOrderNo  = b.PurchaseOrderNo    
--LEFT JOIN  tbGoodsInLine gl on a.PurchaseOrderLineId=gl.PurchaseOrderLineNo  
WHERE b.SupplierRMAId   = @SupplierRMAId    
AND  a.QuantityReceived  > 0    
AND  a.PurchaseOrderLineId NOT IN ( SELECT z.PurchaseOrderLineNo    
           FROM dbo.tbSupplierRMALIne z    
           WHERE z.SupplierRMANo = @SupplierRMAId )     
UNION    
SELECT a.*    
 , a.QuantityReceived - ( SELECT SUM(y.Quantity)    
        FROM dbo.tbSupplierRMALIne y    
        WHERE y.SupplierRMANo = @SupplierRMAId ) As QuantityToReturn    
, '' as Location  
,ISNPREXIST
        
 ,ISCRMA    
          
FROM #POtemp a    
JOIN dbo.tbSupplierRMA b    
 ON a.PurchaseOrderNo  = b.PurchaseOrderNo    
 --LEFT JOIN  tbGoodsInLine gl on a.PurchaseOrderLineId=gl.PurchaseOrderLineNo  
WHERE b.SupplierRMAId   = @SupplierRMAId    
AND  a.QuantityReceived  > 0    
AND  a.PurchaseOrderLineId IN ( SELECT x.PurchaseOrderLineNo    
          FROM dbo.tbSupplierRMALIne x    
          WHERE x.SupplierRMANo = @SupplierRMAId    
          AND  x.Quantity  < a.QuantityReceived )     
ORDER BY PurchaseOrderLineId 

drop table #POtemp

end

GO


