﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216601]	    Phuc Hoang			12-Dec-2024		Update			216601: Quote - New status matrix
[Bug-229247 ]	Trung Pham			21-Jan-2025		Update			HUBRFQ screen status filters (8,9)
==========================================================================================================
*/

CREATE OR ALTER   PROCEDURE[dbo].[usp_insert_SalesOrderLine]                                                                                                           
--******************************************************************************************                                                                                                                                                                   

/*                                                                                                        
Marker     changed by      date         Remarks                                                                                                        
[001]      Vinay           21/08/2012   ESMS Ref:54 - If SO line created from Quote line then create hyperlink from sales order to quote                                                                                                      
[002]      Vinay           21/01/2014   CR:- Add AS9120 Requirement in GT application                                                            
[003]      Anand           25/08/2020   CR:- Add IHS Column Passing                                               
[004]      Abhinav Saxena  24/05/2022   Add new fields for EI p-1                   
[005]    Ravi Bhushan    30-03-2023   OGEL Priority task (insert row in tbSO_ExportApprovalStatusOGEL table if OGEL is = 1 and SalesOrderLineId does not already exists in the table)                
[007]  Ravi Bhushan  13-09-2023    RP-2340 'AS6081' value to tbSalesOrderLine    
--[RP-2464] Ravi      12-10-2023 Change AS6081 in header based on Quote lines   
*/                                                                                                                
--******************************************************************************************                                                                                                          
  @SalesOrderNo int                                                                                                          
, @Part nvarchar(30)                                                                                                          
, @ManufacturerNO int = NULL                                                                                                          
, @DateCode nvarchar(5) = NULL                                                                                                          
, @PackageNo int = NULL                                                                                                          
, @Quantity int                                                                                                          
, @Price float                                                                                                          
, @DatePromised datetime                                                                                                          
, @RequiredDate datetime                                                                                                          
, @Instructions nvarchar(max) = NULL                                                                                                          
, @ProductNo int = NULL                                                                                                          
, @Taxable nvarchar(1)                                                                                                          
, @CustomerPart nvarchar(30) = NULL                                                                                                          
, @Posted bit                                                                                                          
, @ShipASAP bit                                                                                                          
, @ServiceNo int = NULL                                     
, @StockNo int = NULL                                                                                                          
, @ROHS tinyint  = NULL                                    
, @Notes nvarchar(2000) = Null                                     
, @UpdatedBy int = NULL                                
--[001] code start                          
, @QuoteLineNo int = NULL                                                                                                          
--[001] code end                                
--[002] code start                                                                      
, @ProductSource tinyint = NULL                                            
, @SourcingResultNo int =NULL                                                                
, @IsCreateSOClone bit = 0                                                                
, @PODeliveryDate datetime = null                                                                    
, @PrintHazardous bit = 0                                                            
, @MSLLevel   nvarchar(100) = Null                                                                                                  
--[002] code end                                                                      
, @ContractNo nvarchar(100) = NULL                                                                                          
, @SalesOrderLineId int OUTPUT                                                                 
, @DocId INT = NULL                                                                  
, @DocType CHAR(10) = NULL                                             
--[004]  code start                                                                                                        
, @EI_Required    INT=0                                              
, @EvidenceNotes  NVARCHAR(MAX)=NULL                                              
, @TestingType    INT    =NULL                                            
--[004]  code end                                  
 ,@ECCNCode varchar(100)=null                               
 ,@ECCNNo int = null       
 , @AS6081 bit = 0      
AS                                                       
BEGIN                                                                           
--[003] ihs code start                                                          
declare                                                                 
 --@CountryOfOrigin nvarchar(50)=null,                                                                
@CountryOfOriginNo int=null,                                                                
@LifeCycleStage nvarchar(50)=null,                                                                
@HTSCode varchar(20)=null,                                                                
@AveragePrice  float=null,                                                                
@Packing varchar(60)=null,                                                                
@PackagingSize varchar(100)=null,                                                            
@Descriptions nvarchar(max)=null,                                                      
@IHSProduct varchar(100)=null,
@QuoteNo INT = null 
--@ECCNCode varchar(100)=null                                                         
                                                   
if(@QuoteLineNo is not null)                                                                
begin                                                                
select                                                                   
--@CountryOfOrigin =  CountryOfOrigin,                                                                
@CountryOfOriginNo = CountryOfOriginNo,                                                                
@LifeCycleStage = LifeCycleStage,                                                                
@HTSCode = HTSCode,                        
@AveragePrice  = AveragePrice,                                                                
@Packing = Packing,                                                                
@PackagingSize = PackagingSize,                                                             
@Descriptions =Descriptions   ,                                                
@IHSProduct=IHSProduct,
@QuoteNo = QuoteNo
--@ECCNCode  = ECCNCode                                                         
from tbQuoteLine where QuoteLineId = @QuoteLineNo 
end                                                                
--[003] ihs code end                                                          
if(@ECCNCode = '[Blank]' or @ECCNCode = '')            
begin            
 set @ECCNCode = null            
 set @ECCNNo = null            
            
end            
INSERT  INTO dbo.tbSalesOrderLine (                                                                          
          SalesOrderNo                                                                                                          
        , FullPart                                           
        , Part                                                                                                          
        , ManufacturerNo                                                                                                          
        , DateCode                                                                                           
		, PackageNo                                                                                                
        , Quantity                                                                                          
        , Price                                                                          
        , DatePromised                                                                                              
        , RequiredDate                                                                             
        , Instructions                                                                                      
        , ProductNo                                                                                
        , Taxable                                                               
        , CustomerPart                                                                                         
        , Posted                                                                                                          
		, ShipASAP                                                                                                          
		, ServiceNo                                                                                                          
        , StockNo                                                                                        
        , ROHS                                                                                                          
        , Notes                                                           
        , UpdatedBy                                                                                                           
        , FullCustomerPart                                                                               
        --[001] code start                                                                                                        
        , QuoteLineNo                                                                                                        
        --[001] code end                                                                      
        --[002] code start                                                 
        , ProductSource                                                                                                    
        , SourcingResultNo                                
  , PODeliveryDate                                                                      
  , PrintHazardous                                                                      
  , MSLLevel                 
        --[002] code start                                                                       
  ,ContractNo           
  ,DateCreated                                                                
  ,DocNo                                                             
  ,DocType                                                             
  --[003] ihs code start                                                             
  --, CountryOfOrigin                                                                
,CountryOfOriginNo                                                                
,LifeCycleStage     ,HTSCode                                                                
,AveragePrice                                                       
,Packing                                                                
,PackagingSize                                                            
,Descriptions                                                       
,IHSProduct                    
,ECCNCode                                                                   
--[003] ihs code end                                              
--[004]  code start                             
--, EI_Required                                          
--, EvidenceNotes                                          
--, TestingType                                          
--[004]  code end       
, AS6081 --[007]      
   )                                                
VALUES  (                                                                                                          
         @SalesOrderNo                                                                                                          
 , dbo.ufn_get_fullpart(@Part)                                                                                                          
       , @Part                                                                                                          
       , @ManufacturerNo                                                                   
       , @DateCode                                                                                             
    , @PackageNo                                       
       , @Quantity                                                                                                          
       , @Price                                                                                                          
       , @DatePromised                                                                
       , @RequiredDate                                                                                                          
       , @Instructions                                                                                                          
       , @ProductNo                                                                                                
       , @Taxable                                                                                                          
       , @CustomerPart                                   
       , @Posted                                                                                                  
       , @ShipASAP                                                                                                          
       , @ServiceNo                                                                                                          
       , @StockNo                                                                                      
       , @ROHS                                                                                                          
       , @Notes            
       , @UpdatedBy                                                        
       , dbo.ufn_get_fullpart(@CustomerPart)                                                                                                         
       --[001] code start                                                                              
      ,  @QuoteLineNo                                                                                         
       --[001] code end                                                                                      
       , @ProductSource                                                                                                      
       , @SourcingResultNo                                                      
    , @PODeliveryDate                                                                      
    , @PrintHazardous                                                                    
    , @MSLLevel                                                                    
    ,@ContractNo                                                              
    ,GETDATE()                                                                   
    ,@DocId              
    ,@DocType                                                          
 --[003] ihs code start                                                                 
  --, @CountryOfOrigin                                                                
 , @CountryOfOriginNo                                                                
 , @LifeCycleStage                                          
 , @HTSCode                                                                
 , @AveragePrice                                                                
 , @Packing                                                                
 , @PackagingSize                                                            
 ,@Descriptions                                                      
 ,@IHSProduct                                                     
 ,@ECCNCode                                                           
 --[003] ihs code end                                                          
--[004]  code start                                          
--, @EI_Required                                         
--, @EvidenceNotes                                          
--, @TestingType                                          
--[004]  code end         
, ISNULL(@AS6081,0) --[007]      
   )                                                                                                           
                                                      
                                                                                                  
SET @SalesOrderLineId = scope_identity() 

	
---------------------------------Set QuoteStatus---------------------------------
	IF(ISNULL(@QuoteLineNo, 0) > 0) 
	BEGIN
		DECLARE @QuoteLineTotal INT = 0, @QuoteLineClosedCount INT = 0, @QuoteStatus INT = 0, @QuoteLineNotClosed INT = 0;

		UPDATE  dbo.tbQuoteLine  
		SET  Closed = 1, ReasonNo = 1 --sold 
		WHERE   QuoteLineId = @QuoteLineNo;

		SELECT @QuoteStatus = QuoteStatus FROM [dbo].[tbQuote] WHERE QuoteId = @QuoteNo;

		IF (@QuoteStatus = 8) --Partially Declined
		BEGIN
			UPDATE  [dbo].[tbQuote]
			SET     QuoteStatus = 7 --Partially Accepted
			WHERE   QuoteId = @QuoteNo;
		END
		ELSE
		BEGIN
			SELECT  @QuoteLineTotal=COUNT(*) FROM  [dbo].[tbQuoteLine] WHERE QuoteNo=@QuoteNo;

			SELECT @QuoteLineNotClosed = COUNT(*) 
			FROM [dbo].[tbQuoteLine] ql
				INNER JOIN [dbo].[tbSalesOrderLine] sl ON sl.QuoteLineNo = ql.QuoteLineId
			WHERE QuoteNo=@QuoteNo;

			IF (@QuoteLineNotClosed = @QuoteLineTotal)
			BEGIN
				UPDATE  [dbo].[tbQuote]                                              
				SET     QuoteStatus = 2 --Accepted
				WHERE   QuoteId = @QuoteNo;
			END
			ELSE
			BEGIN
				UPDATE  [dbo].[tbQuote]
				SET     QuoteStatus = 7 --Partially Accepted
				WHERE   QuoteId = @QuoteNo;
			END
		END
		
	END
---------------------------------END Set QuoteStatus---------------------------------
                    
--[RP-2464]  start     
  IF (select count(1) from tbSalesOrderLine where SalesOrderNo = @SalesOrderNo and ISNULL(AS6081,0) = 1) > 0      
  BEGIN     
	UPDATE tbSalesOrder SET AS6081 = 1 WHERE SalesOrderId = @SalesOrderNo     
  END
  IF (select count(1) from tbSalesOrderLine where SalesOrderNo = @SalesOrderNo and ISNULL(AS6081,0) = 1) = 0      
  BEGIN     
	UPDATE tbSalesOrder SET AS6081 = 0 WHERE SalesOrderId = @SalesOrderNo     
  END
--[RP-2464] END   
  
--Reset the tbSalesOrderLine serial no                                                            
EXEC usp_Update_SalesOrderLine_SerialNo @SalesOrderNo                                                                               
                
----------------------------------------------------INSERT INTO QUOTE LINE START                    
IF(@SourcingResultNo IS NOT NULL AND @SourcingResultNo>0)                                                                                            
BEGIN                 
Declare @QuoteLineId int = null 
--Declare @QuoteNo Int                                                                                       
DECLARE @IsChecked BIT=0                                         
DECLARE @IPOLineOrderNo INT=0                                                         
                                                                      
--SET @QuoteNo=(Select TOP 1 qo.QuoteId from tbsalesorder as so                                                                              
--join tbsalesorderLine sol on sol.salesorderno=so.salesorderid                                                                                          
--join tbQuoteLine ql on sol.QuoteLineNo=ql.QuoteLineId                                                                                          
--join tbQuote qo on ql.QuoteNo=Qo.QuoteId                                                                                          
--where so.SalesOrderId=@SalesOrderNo)                      
                                                                           
--Update tbSourcingResult Set Closed=1 where SourcingResultId=@SourcingResultNo                                                                                        
                                                                                            
DECLARE @SourcingTable NVARCHAR(20)                                     
SELECT @SourcingTable=SourcingTable FROM tbSourcingResult WHERE SourcingResultId=@SourcingResultNo                                                                                        
IF(@SourcingTable='PQ' OR @SourcingTable='OFPH' OR @SourcingTable='EXPH')                                                                                        
BEGIN                                                                          
--- Start Here            This is update                                                              
--if exists(select 1 from tbQuoteLine where SourcingResultNo=@SourcingResultNo)                                                                            
--BEGIN                                                                            
                                                                            
--select @QuoteLineId=QuoteLineId from tbQuoteLine where SourcingResultNo=@SourcingResultNo                                                                            
--Update tbSalesOrderLine Set QuoteLineNo = @QuoteLineId  where SourcingResultNo=@SourcingResultNo                                                                             
--END                                                                             
 update tbSourcingResult set IsSoCreated=1,SourceRef='S' WHERE SourcingResultId=@SourcingResultNo                                                                                   
----EXEC usp_insert_QuoteLine @QuoteNo,@Part,@ManufacturerNo,@DateCode,@PackageNo,@Quantity,@Price,/*@ETA*/NULL,@Instructions,@ProductNo,/*@ReasonNo*/NULL,@CustomerPart,@ServiceNo,@StockNo ,@ROHS,@Notes,                                                   
                               
--/*@OriginalOfferCurrencyNo*/NULL,/*@OriginalOfferDate*/NULL,/*@OriginalOfferPrice*/NULL,/*@OriginalOfferSupplierNo*/NULL,@SourcingResultNo,@UpdatedBy,@ProductSource ,  @QuoteLineId   OUTPUT                                                                
                                                   
IF EXISTS(SELECT * FROM tbSalesOrderLine WHERE SalesOrderNo=@SalesOrderNo AND SourcingResultNo=@SourcingResultNo  AND IsChecked=1)                      
 BEGIN                             
   SET @IsChecked=0                                                                                    
 END                                          
 ELSE               
 BEGIN                                                                                    
  SET @IsChecked=1                                                     
 END                                                                            
                                                                
 --IF @IsCreateSOClone = 1                                                                                 
 --BEGIN                     
  --SET @IsChecked=1                                                 
 --END                                                                                
--Update tbsalesOrderLine Set ISIPO=1, QuoteLineNo= @QuoteLineId,IsChecked=@IsChecked Where SalesOrderLineId=@SalesOrderLineId                                                                                      
Update tbsalesOrderLine Set ISIPO=1, IsChecked=@IsChecked Where SalesOrderLineId=@SalesOrderLineId                                                                                      
end                          
END                            
----------------------------------------------------INSERT INTO QUOTELINE END HERE                         
                          
                          
--for Eccn code insert cammand                                              
if(@ECCNNo is not null and @ECCNCode is not null)                                                  
begin                    
if(@ECCNCode!='')                               
begin                  
declare @checkdublicate int=0                                                       
set @checkdublicate=(select count(*) from tbPartEccnMapped where  Part=@Part )                                                                   
if(@checkdublicate=0)                                                          
begin                                      
INSERT INTO dbo.tbPartEccnMapped                                                                                
 ( Part                                                                                
 , ECCNNo                                                                                
 , ECCNCode                                                      
 , ClientNo                                                                          
 , UpdatedBy                                                                                
                                                                              
 )                                                                                
VALUES                                                            
 ( @Part                                                                                
 , @ECCNNo                                                                                
 , @ECCNCode                                                                                
 , 0                                                                                
 , @UpdatedBy                                                
)                              
end                      
end                  
-------------------------------------Log entery for ECCN Log----------------------------16-09-2022-----RP-30------------------------------                            
                            
  declare @SectionName varchar(50) = 'SOLineECCN'                                      
    declare @SubSectionName varchar(50) = 'ECCN'                                      
    declare @ActionName   varchar(10) = 'Print'                  
    declare @DocumentNo     int     = @SalesOrderLineId                                      
    declare @Detail     nvarchar(max) = 'Action��' +' SO LINE ADDED WITH THIS ECCN CODE  ( ' + @ECCNCode + ' )'                      
    declare @PrintDocumentLogId  int =NULL                                     
   -----------------------------------------------------------------------                       
   EXEC [dbo].[usp_insert_PrintEmailLog]                                         
   @SectionName                                       
       , @SubSectionName    
       , @ActionName                                         
       , @DocumentNo                                         
       , @Detail                                           
       , @UpdatedBy                                        
       , @PrintDocumentLogId                                        
                    
-------Code end for ECCN Log Entery------------------------------------------------------------------------                             
                
-------------------------------------[005] Starts here ---------------------------------------------------------------------                
              
                
                
-----------------------------------[005] ends here --------------------------------                
end           
DECLARE @CheckOgelRequiredFlag bit = 0                
DECLARE @DefaultApprovalStatusId int = 3;                
                
IF((select count(1) from tbSO_ExportApprovalStatusOGEL WHERE SalesOrderLineNo = @SalesOrderLineId)=0)  -- check if SalesOrderLineNo already exists in table or not                
BEGIN              
SELECT @CheckOgelRequiredFlag  = isnull(OGEL_Required,0)                     
FROM tbSalesOrder t WHERE t.SalesOrderId = @SalesOrderNo                 
                
IF (@CheckOgelRequiredFlag = 1)                   
 BEGIN                
  INSERT INTO tbSO_ExportApprovalStatusOGEL (                
  SalesOrderNo                
  , SalesOrderLineNo                
  , ApprovalStatusId                
  --, MilitaryUses                
  , UpdatedBy                
  , DLUP)                
  VALUES (                
  @SalesOrderNo                
  , @SalesOrderLineId                
  , @DefaultApprovalStatusId                
  --, @Ogel_MilitaryUse                
  , @UpdatedBy                
  , getdate())                
 END                
END          
-- Update HUBRFQ status when generating SO
IF EXISTS (SELECT b.*
	FROM tbQuoteLine ql
	JOIN tbQuote q ON ql.QuoteNo = q.QuoteId
	JOIN tbSourcingResult sr ON sr.SourcingResultId = ql.SourcingResultNo
	JOIN tbCustomerRequirement cr ON cr.CustomerRequirementId = sr.CustomerRequirementNo
	JOIN tbBOM b ON b.BOMId = cr.BOMNo
	WHERE ql.QuoteLineId = @QuoteLineNo AND b.[Status] = 8 AND cr.ClientNo = b.ClientNo 
		AND b.DateRelease IS NOT NULL)
		BEGIN    
			UPDATE b
			SET [Status] = 9
			FROM tbQuoteLine ql
			JOIN tbQuote q ON ql.QuoteNo = q.QuoteId
			JOIN tbSourcingResult sr ON sr.SourcingResultId = ql.SourcingResultNo
			JOIN tbCustomerRequirement cr ON cr.CustomerRequirementId = sr.CustomerRequirementNo
			JOIN tbBOM b ON b.BOMId = cr.BOMNo
			WHERE cr.ClientNo = b.ClientNo AND b.DateRelease IS NOT NULL
			AND ql.QuoteLineId = @QuoteLineId AND b.[Status] = 8
		END  
END 
GO


