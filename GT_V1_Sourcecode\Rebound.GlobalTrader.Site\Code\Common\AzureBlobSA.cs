﻿using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.WindowsAzure;
//using Microsoft.WindowsAzure.StorageClient;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Web;
using Microsoft.Azure.Storage.Auth;


namespace Rebound.GlobalTrader.Site
{
    public class AzureBlobSA
    {
        public static IEnumerable<IListBlobItem> GetContainerFiles(string containerName)
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(
                Microsoft.Azure.CloudConfigurationManager.GetSetting("StorageAccountConnectionString"));
            CloudBlobClient storageClient = storageAccount.CreateCloudBlobClient();
            CloudBlobContainer storageContainer = storageClient.GetContainerReference(containerName);
            return storageContainer.ListBlobs();

        }
        public static string GetSasUrl(string containerName)
        {
            CloudStorageAccount storageAccount = CloudStorageAccount.Parse(ConfigurationManager.AppSettings["StorageAccountConnectionString"]);

            CloudBlobClient blobClient = storageAccount.CreateCloudBlobClient();
            CloudBlobContainer container = blobClient.GetContainerReference(containerName);
            //container.CreateIfNotExists();

            BlobContainerPermissions containerPermissions = new BlobContainerPermissions();
            containerPermissions.SharedAccessPolicies.Add("twominutepolicy", new SharedAccessBlobPolicy()
            {
                SharedAccessStartTime = DateTime.UtcNow.AddMinutes(-1),
                SharedAccessExpiryTime = DateTime.UtcNow.AddMinutes(2),
                Permissions = SharedAccessBlobPermissions.Write | SharedAccessBlobPermissions.Read
            });

            //containerPermissions.SharedAccessPolicies.Add("twominutepolicy", new SharedAccessPolicy()
            //{
            //    SharedAccessStartTime = DateTime.UtcNow.AddMinutes(-1),
            //    SharedAccessExpiryTime = DateTime.UtcNow.AddMinutes(2),
            //    Permissions = SharedAccessPermissions.Write | SharedAccessPermissions.Read
            //});

            containerPermissions.PublicAccess = BlobContainerPublicAccessType.Off;
            container.SetPermissions(containerPermissions);
            string sas = container.GetSharedAccessSignature(new SharedAccessBlobPolicy(), "twominutepolicy");
            return sas;
        }

        public static string GetSasBlobUrl(string containerName, string fileName, string sas,string foldername)
        {
            try
            {
                CloudStorageAccount storageAccount = CloudStorageAccount.Parse(ConfigurationManager.AppSettings["StorageAccountConnectionString"]);

                // StorageCredentialsSharedAccessSignature ss= new StorageCredentialsSharedAccessSignature

                // SharedAccessBlobPermissions.

                // StorageCredentialsSharedAccessSignature sasCreds = new StorageCredentialsSharedAccessSignature(sas);

                // CloudBlobClient sasBlobClient = new CloudBlobClient();// storageAccount.BlobEndpoint,
                CloudBlobClient cbclientDF = storageAccount.CreateCloudBlobClient();
                //new shares StorageCredentialsSharedAccessSignature(sas));

                CloudBlobContainer contDF = cbclientDF.GetContainerReference(containerName);
                CloudBlobDirectory directoryDF = contDF.GetDirectoryReference(foldername);

                //CloudBlob blob = sasBlobClient.GetBlobReference(containerName + @"/" + fileName);
                CloudBlockBlob blob = directoryDF.GetBlockBlobReference(fileName);
                // blob.Properties.CacheControl = "max";
                string strURL = string.Empty;
                if (blob.Exists())
                {
                    blob.Properties.CacheControl = "no-store, max-age=0";
                   // Update the blob's properties in the cloud
                    blob.SetProperties();
                    
                }
                strURL = HttpUtility.UrlDecode(blob.Uri.AbsoluteUri + sas).Replace("+", "%2B");
                return strURL;

            }
            catch
            {
                return "";
            }
        }
       
        static public string EncodeTo64(string toEncode)
        {
            byte[] toEncodeAsBytes = System.Text.ASCIIEncoding.ASCII.GetBytes(toEncode);
            string returnValue = System.Convert.ToBase64String(toEncodeAsBytes);
            return returnValue;
        }

        static public string DecodeFrom64(string encodedData)
        {
            byte[] encodedDataAsBytes = System.Convert.FromBase64String(encodedData);
            string returnValue = System.Text.ASCIIEncoding.ASCII.GetString(encodedDataAsBytes);
            return returnValue;
        }
    }
}