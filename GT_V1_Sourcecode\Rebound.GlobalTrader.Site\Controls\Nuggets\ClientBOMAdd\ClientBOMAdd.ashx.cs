//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - clear related dropdown cache
//Marker     changed by      date         Remarks

//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class ClientBOMAdd : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "AddNew": AddNew(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Add new BOM
		/// </summary>
		public void AddNew() {
			try {
                //CacheManager.ClearStoredDropDown("BOM", new object[] { SessionManager.ClientID });
                BOM bom = new BOM();
                bom.ClientNo = (int)SessionManager.ClientID;
                bom.BOMName = GetFormValue_String("Name");      
                bom.CompanyNo = GetFormValue_Int("Company");
                bom.ContactNo = GetFormValue_Int("Contact");
                bom.Inactive = GetFormValue_Boolean("Inactive");
                bom.Notes = GetFormValue_String("Notes");
                //bom.UpdateRequirement = 0;
                bom.Status = (int)Rebound.GlobalTrader.BLL.BOMStatus.List.New;
                bom.CurrencyNo = GetFormValue_NullableInt("CurrencyNo");
                //bom.Salesman = GetFormValue_String("Salesman");
                bom.Salesman = GetFormValue_String("SalesPersion");
                //bom.QuoteRequired = GetFormValue_NullableDateTime("QuoteRequired");
                bom.UpdatedBy = LoginID;
                //bom.AS9120 = GetFormValue_Boolean("AS9120");
                //bom.Contact2Id = GetFormValue_NullableInt("Contact2");
                bom.BOMId = bom.InsertClientBOM();   
                
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", bom.BOMId > 0);
                jsn.AddVariable("NewID", bom.BOMId);
                jsn.AddVariable("ValidationMessage", bom.ValidationMessage);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}
	}
}
