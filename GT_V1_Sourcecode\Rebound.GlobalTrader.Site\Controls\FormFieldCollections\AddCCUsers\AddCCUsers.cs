using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
	[ToolboxData("<{0}:SendMailMessage runat=server></{0}:SendMailMessage>")]
	public class AddCCUsers : Base, INamingContainer {

		#region Locals

		private Table _tbl;
		private FormField _ctlTo;
		private Panel _pnlSelected;
		private Label _lblSelected;
		private AutoSearch.CCUsersTo _aut;
		private FormField _ctlSubject;
		private FormField _ctlBody;

		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.AddCCUsers.AddCCUsers.js");
			RemoveCSSClass = true;
		}

		protected override void OnLoad(EventArgs e) {
			_tbl = ControlBuilders.CreateTable();
			_tbl.Width = Unit.Percentage(100);

			//To
			_ctlTo = new FormField();
			_ctlTo.ID = "ctlTo";
			_ctlTo.ResourceTitle = "CC";
			_ctlTo.FieldID = "lblSelected";
			_ctlTo.IsRequiredField = false;
			_pnlSelected = new Panel();
			_pnlSelected.ID = "pnlSelected";
			_lblSelected = ControlBuilders.CreateLabelInsideParent(_pnlSelected);
			_lblSelected.ID = "lblSelected";
			_ctlTo.AddFieldControl(_pnlSelected);
			ReboundTextBox txtTo = new ReboundTextBox();
			txtTo.ID = "txtTo";
			txtTo.Width = 250;
			_ctlTo.AddFieldControl(txtTo);
			_aut = new AutoSearch.CCUsersTo();
			_aut.ID = "autMailMessageTo";
			_aut.RelatedTextBoxID = "txtTo";
			_aut.CharactersToEnterBeforeSearch = 1;
			_aut.ResultsActionType = Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.AutoSearchResultsActionType.RaiseEvent;
			_aut.Width = 250;
			_aut.ResultsHeight = 200;
			_ctlTo.AddFieldControl(_aut);
			_tbl.Rows.Add(_ctlTo);

			AddControl(_tbl);
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.AddCCUsers", this.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlSelected", _pnlSelected.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblSelected", _lblSelected.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("autLoginOrGroup", _aut.ClientID);
		}

	}
}
