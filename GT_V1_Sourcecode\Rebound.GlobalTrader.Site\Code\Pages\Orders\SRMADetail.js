Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.prototype={get_intSRMAID:function(){return this._intSRMAID},set_intSRMAID:function(n){this._intSRMAID!==n&&(this._intSRMAID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_ctlSRMADocuments:function(){return this._ctlSRMADocuments},set_ctlSRMADocuments:function(n){this._ctlSRMADocuments!==n&&(this._ctlSRMADocuments=n)},get_ctlSRMAPDFDragDrop:function(){return this._ctlSRMAPDFDragDrop},set_ctlSRMAPDFDragDrop:function(n){this._ctlSRMAPDFDragDrop!==n&&(this._ctlSRMAPDFDragDrop=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&this._btnPrint.addPrint(Function.createDelegate(this,this.printSRMA));this._btnPrint&&this._btnPrint.addEmail(Function.createDelegate(this,this.emailSRMA));this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlMainInfo&&this.setLineFieldsFromHeader();this._ctlSRMADocuments&&this._ctlSRMADocuments.getData();this._ctlSRMAPDFDragDrop&&this._ctlSRMAPDFDragDrop.getData();this._btnPrint&&this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs));Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._btnPrint&&this._btnPrint.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._btnPrint=null,this._ctlMainInfo=null,this._ctlLines=null,this._intSRMAID=null,this._ctlSRMADocuments&&this._ctlSRMADocuments.dispose(),this._ctlSRMADocuments=null,Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.callBaseMethod(this,"dispose"))},printSRMA:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.SupplierRMA,this._intSRMAID)},emailSRMA:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.SupplierRMA,this._intSRMAID,!0)},ctlMainInfo_GetDataComplete:function(){this.setLineFieldsFromHeader()},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintHUBSRMA"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBSRMA,this._intSRMAID);this._btnPrint._strExtraButtonClickCommand=="EmailHUBSRMA"&&$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBSRMA,this._intSRMAID,!0);this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._intSRMAID,!1,"SupplierRMA")},setLineFieldsFromHeader:function(){var n=this._ctlMainInfo.getFieldValue("hidSupplier"),t=this._ctlMainInfo.getFieldValue("hidNo");this._ctlLines._frmAdd&&this._ctlLines._frmAdd.setFieldsFromHeader(t,n);this._ctlLines._frmEdit&&this._ctlLines._frmEdit.setFieldsFromHeader(t,n);this._ctlLines._frmDelete&&this._ctlLines._frmDelete.setFieldsFromHeader(t,n);this._ctlLines._frmAllocate&&this._ctlLines._frmAllocate.setFieldsFromHeader(t,n);this._ctlLines._frmDeallocate&&this._ctlLines._frmDeallocate.setFieldsFromHeader(t,n);this._ctlLines&&(this._ctlLines._blnSRMAAutoGenerated=this._ctlMainInfo.getFieldValue("hidIsSRMAAutoGen"))}};Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.SRMADetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);