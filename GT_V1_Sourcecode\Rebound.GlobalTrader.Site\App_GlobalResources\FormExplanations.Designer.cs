//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class FormExplanations {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FormExplanations() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.FormExplanations", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add/Edit Lot Code.
        /// </summary>
        internal static string AddEditLotNo {
            get {
                return ResourceManager.GetString("AddEditLotNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add/Edit Serial No.
        /// </summary>
        internal static string AddEditSerialNo {
            get {
                return ResourceManager.GetString("AddEditSerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activate auto invoice export.
        /// </summary>
        internal static string AppSettingsEdit_InvAutoExport {
            get {
                return ResourceManager.GetString("AppSettingsEdit_InvAutoExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Settings at the Company Level will override any Global settings, otherwise the defaults will be used. Edit the details and press &lt;b&gt;Save&lt;/b&gt;. Clearing a field will use the default value..
        /// </summary>
        internal static string AppSettings_Edit {
            get {
                return ResourceManager.GetString("AppSettings_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this reason for chosen supplier?.
        /// </summary>
        internal static string AS6081_RCS_Delete {
            get {
                return ResourceManager.GetString("AS6081_RCS_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this risk of supplier?.
        /// </summary>
        internal static string AS6081_ROS_Delete {
            get {
                return ResourceManager.GetString("AS6081_ROS_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this type of suppliers?.
        /// </summary>
        internal static string AS6081_TOS_Delete {
            get {
                return ResourceManager.GetString("AS6081_TOS_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the reason for chosen supplier and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string AS6_6081_RCS_Add {
            get {
                return ResourceManager.GetString("AS6_6081_RCS_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the reason for chosen supplier and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string AS6_6081_RCS_Edit {
            get {
                return ResourceManager.GetString("AS6_6081_RCS_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new reason for chosen supplier and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string AS6_6081_ROS_Add {
            get {
                return ResourceManager.GetString("AS6_6081_ROS_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the risk of supplier and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string AS6_6081_ROS_Edit {
            get {
                return ResourceManager.GetString("AS6_6081_ROS_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new type of supplier and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string AS6_6081_TOS_Add {
            get {
                return ResourceManager.GetString("AS6_6081_TOS_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the type of supplier and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string AS6_6081_TOS_Edit {
            get {
                return ResourceManager.GetString("AS6_6081_TOS_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new HUBRFQ and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string BOMAdd_Add {
            get {
                return ResourceManager.GetString("BOMAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete selected item?.
        /// </summary>
        internal static string BOMCusReqSourcingResults_Delete {
            get {
                return ResourceManager.GetString("BOMCusReqSourcingResults_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Communication Note.
        /// </summary>
        internal static string BOMItems_AddExpedite {
            get {
                return ResourceManager.GetString("BOMItems_AddExpedite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the selected  item?.
        /// </summary>
        internal static string BomItems_Delete {
            get {
                return ResourceManager.GetString("BomItems_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to Revoke the selected  item?.
        /// </summary>
        internal static string BomItems_UnRelease {
            get {
                return ResourceManager.GetString("BomItems_UnRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to Apply Partwatch?.
        /// </summary>
        internal static string BOMItem_ApplyPartwatch {
            get {
                return ResourceManager.GetString("BOMItem_ApplyPartwatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to Remove Partwatch?.
        /// </summary>
        internal static string BOMItem_ApplyRemovePartwatch {
            get {
                return ResourceManager.GetString("BOMItem_ApplyRemovePartwatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to No-Bid this HUBRFQ?.
        /// </summary>
        internal static string BOMItem_NoBid {
            get {
                return ResourceManager.GetString("BOMItem_NoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to No-Bid this HUBRFQ Line?.
        /// </summary>
        internal static string BOMItem_PartialNoBid {
            get {
                return ResourceManager.GetString("BOMItem_PartialNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to partially release this HUBRFQ?.
        /// </summary>
        internal static string BOMItem_PartialRelease {
            get {
                return ResourceManager.GetString("BOMItem_PartialRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to release this HUBRFQ?.
        /// </summary>
        internal static string BOMItem_Release {
            get {
                return ResourceManager.GetString("BOMItem_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this HUBRFQ?.
        /// </summary>
        internal static string BOMMainInfo_Delete {
            get {
                return ResourceManager.GetString("BOMMainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string BOMMainInfo_Edit {
            get {
                return ResourceManager.GetString("BOMMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of this HUBRFQ.
        /// </summary>
        internal static string BOMMainInfo_Notify {
            get {
                return ResourceManager.GetString("BOMMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to Assign these HUBRFQ?.
        /// </summary>
        internal static string BOM_AssignToMe {
            get {
                return ResourceManager.GetString("BOM_AssignToMe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new certificate category and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CertificateCategory_Add {
            get {
                return ResourceManager.GetString("CertificateCategory_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CertificateCategory_Edit {
            get {
                return ResourceManager.GetString("CertificateCategory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Certificate and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Certificate_Add {
            get {
                return ResourceManager.GetString("Certificate_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Certificate_Edit {
            get {
                return ResourceManager.GetString("Certificate_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new BOM and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ClientBOMAdd_Add {
            get {
                return ResourceManager.GetString("ClientBOMAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a new image for the Client Invoice Headers, 710 pixels wide and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ClientInvoiceHeaderImage_Add {
            get {
                return ResourceManager.GetString("ClientInvoiceHeaderImage_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the Client Invoice Header Image?.
        /// </summary>
        internal static string ClientInvoiceHeaderImage_Delete {
            get {
                return ResourceManager.GetString("ClientInvoiceHeaderImage_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Client Invoice Header Image and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ClientInvoiceHeaderImage_Edit {
            get {
                return ResourceManager.GetString("ClientInvoiceHeaderImage_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Division and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ClientInvoiceHeader_Add {
            get {
                return ResourceManager.GetString("ClientInvoiceHeader_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the text of the Document Footer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ClientInvoiceHeader_Edit {
            get {
                return ResourceManager.GetString("ClientInvoiceHeader_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ClientInvoiceLines_Add {
            get {
                return ResourceManager.GetString("ClientInvoiceLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the selected Client Invoice Line?.
        /// </summary>
        internal static string ClientInvoiceLines_Delete {
            get {
                return ResourceManager.GetString("ClientInvoiceLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ClientInvoiceMainInfo_Edit {
            get {
                return ResourceManager.GetString("ClientInvoiceMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the client and press Save.
        /// </summary>
        internal static string Client_Add {
            get {
                return ResourceManager.GetString("Client_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone requirement and send to HUB.
        /// </summary>
        internal static string CloneHUB {
            get {
                return ResourceManager.GetString("CloneHUB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone requirement and add HUBRFQ.
        /// </summary>
        internal static string CloneHUBRFQ {
            get {
                return ResourceManager.GetString("CloneHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the CommunicationLog Type and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CommunicationLogType_Add {
            get {
                return ResourceManager.GetString("CommunicationLogType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new CommunicationLog Type and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CommunicationLogType_Edit {
            get {
                return ResourceManager.GetString("CommunicationLogType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the Contact Log item and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CommunicationLog_AddEdit {
            get {
                return ResourceManager.GetString("CommunicationLog_AddEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Address and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyAddresses_Add {
            get {
                return ResourceManager.GetString("CompanyAddresses_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to cease this address?.
        /// </summary>
        internal static string CompanyAddresses_Cease {
            get {
                return ResourceManager.GetString("CompanyAddresses_Cease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to make this address the default Billing Address for this Company?.
        /// </summary>
        internal static string CompanyAddresses_DefaultBill {
            get {
                return ResourceManager.GetString("CompanyAddresses_DefaultBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to make this address the default Shipping Address for this Company?.
        /// </summary>
        internal static string CompanyAddresses_DefaultShip {
            get {
                return ResourceManager.GetString("CompanyAddresses_DefaultShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyAddresses_Edit {
            get {
                return ResourceManager.GetString("CompanyAddresses_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Company and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyAdd_Add {
            get {
                return ResourceManager.GetString("CompanyAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of customer/supplier.
        /// </summary>
        internal static string CompanyApiCustomer_Add {
            get {
                return ResourceManager.GetString("CompanyApiCustomer_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of customer/supplier.
        /// </summary>
        internal static string CompanyApiCustomer_Edit {
            get {
                return ResourceManager.GetString("CompanyApiCustomer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Company Certificate and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyCertificate_Add {
            get {
                return ResourceManager.GetString("CompanyCertificate_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyCertificate_Edit {
            get {
                return ResourceManager.GetString("CompanyCertificate_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Accounts with matching VAT Number which you wish to link.
        /// </summary>
        internal static string CompanyFinanceInfo_Link {
            get {
                return ResourceManager.GetString("CompanyFinanceInfo_Link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the Company Insurance Certificate and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyInsuranceCertificate_Add {
            get {
                return ResourceManager.GetString("CompanyInsuranceCertificate_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the Edit details of the Company Insurance Certificate and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyInsuranceCertificate_Edit {
            get {
                return ResourceManager.GetString("CompanyInsuranceCertificate_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details of the Company and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyMainInfo_Edit {
            get {
                return ResourceManager.GetString("CompanyMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specify the details of the Manufacturer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyManufacturers_AddEdit {
            get {
                return ResourceManager.GetString("CompanyManufacturers_AddEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to remove this Manufacturer?.
        /// </summary>
        internal static string CompanyManufacturers_Delete {
            get {
                return ResourceManager.GetString("CompanyManufacturers_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string CompanyProspects_Edit {
            get {
                return ResourceManager.GetString("CompanyProspects_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the purchasing details of the Company and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyPurchaseInfo_Edit {
            get {
                return ResourceManager.GetString("CompanyPurchaseInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the Purchasing Information and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyPurchasingInfo_Edit {
            get {
                return ResourceManager.GetString("CompanyPurchasingInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the sales details of the Company and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanySalesInfo_Edit {
            get {
                return ResourceManager.GetString("CompanySalesInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Company Type and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyType_Add {
            get {
                return ResourceManager.GetString("CompanyType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Company Type and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CompanyType_Edit {
            get {
                return ResourceManager.GetString("CompanyType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to send this to Purchase Hub?.
        /// </summary>
        internal static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close this HUBRFQ?.
        /// </summary>
        internal static string ConfirmClose {
            get {
                return ResourceManager.GetString("ConfirmClose", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to post this line?.
        /// </summary>
        internal static string ConfirmPost {
            get {
                return ResourceManager.GetString("ConfirmPost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to post all the unposted lines?.
        /// </summary>
        internal static string ConfirmPostAll {
            get {
                return ResourceManager.GetString("ConfirmPostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to approve supplier?.
        /// </summary>
        internal static string ConfirmSAApprove {
            get {
                return ResourceManager.GetString("ConfirmSAApprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to decline supplier?.
        /// </summary>
        internal static string ConfirmSADecline {
            get {
                return ResourceManager.GetString("ConfirmSADecline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to escalate supplier approval?.
        /// </summary>
        internal static string ConfirmSAEscalate {
            get {
                return ResourceManager.GetString("ConfirmSAEscalate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to unpost this line?.
        /// </summary>
        internal static string ConfirmUnpost {
            get {
                return ResourceManager.GetString("ConfirmUnpost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to unpost all the posted and unallocated lines?.
        /// </summary>
        internal static string ConfirmUnpostAll {
            get {
                return ResourceManager.GetString("ConfirmUnpostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the extended information for the Contact and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ContactExtendedInfo_Edit {
            get {
                return ResourceManager.GetString("ContactExtendedInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ContactMainInfo_Edit {
            get {
                return ResourceManager.GetString("ContactMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Contact and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ContactsForCompany_Add {
            get {
                return ResourceManager.GetString("ContactsForCompany_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Contact?.
        /// </summary>
        internal static string ContactsForCompany_Delete {
            get {
                return ResourceManager.GetString("ContactsForCompany_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set as Company&apos;s Default Contact for Purchase Orders.
        /// </summary>
        internal static string ContactsForCompany_MakeDefaultPO {
            get {
                return ResourceManager.GetString("ContactsForCompany_MakeDefaultPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set as Company&apos;s Default Contact for Purchase Orders Ledger.
        /// </summary>
        internal static string ContactsForCompany_MakeDefaultPOLedger {
            get {
                return ResourceManager.GetString("ContactsForCompany_MakeDefaultPOLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set as Company&apos;s Default Contact for Sales Orders.
        /// </summary>
        internal static string ContactsForCompany_MakeDefaultSO {
            get {
                return ResourceManager.GetString("ContactsForCompany_MakeDefaultSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set as Company&apos;s Default Contact for Sales Orders Ledger.
        /// </summary>
        internal static string ContactsForCompany_MakeDefaultSOLedger {
            get {
                return ResourceManager.GetString("ContactsForCompany_MakeDefaultSOLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Counting Method and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CountingMethod_Add {
            get {
                return ResourceManager.GetString("CountingMethod_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Counting Method and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CountingMethod_Edit {
            get {
                return ResourceManager.GetString("CountingMethod_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Country and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CountryAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CountryAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Country and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CountryAdd_SelectSource {
            get {
                return ResourceManager.GetString("CountryAdd_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to remove the country Header Image?.
        /// </summary>
        internal static string Country_DeleteHeader {
            get {
                return ResourceManager.GetString("Country_DeleteHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Country and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Country_Edit {
            get {
                return ResourceManager.GetString("Country_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details for the &lt;span style=&quot;
        ///color: red;
        /// background-color: yellow;
        /// font-weight: bold;
        ///&quot;&gt;Header&lt;/span&gt; and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Country_ManageHeader {
            get {
                return ResourceManager.GetString("Country_ManageHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Credit and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CreditAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CreditAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Credit .
        /// </summary>
        internal static string CreditAdd_Notify {
            get {
                return ResourceManager.GetString("CreditAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Customer RMA on which you would like to base this new Credit Note and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CreditAdd_SelectCRMA {
            get {
                return ResourceManager.GetString("CreditAdd_SelectCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Invoice on which you would like to base this new Credit Note and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CreditAdd_SelectInvoice {
            get {
                return ResourceManager.GetString("CreditAdd_SelectInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the item on which you would like to base this new Credit Note and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CreditAdd_SelectItem {
            get {
                return ResourceManager.GetString("CreditAdd_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source of this new Credit and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CreditAdd_SelectSource {
            get {
                return ResourceManager.GetString("CreditAdd_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Credit Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CreditLines_Add {
            get {
                return ResourceManager.GetString("CreditLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Credit Line?.
        /// </summary>
        internal static string CreditLines_Delete {
            get {
                return ResourceManager.GetString("CreditLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Credit Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CreditLines_Edit {
            get {
                return ResourceManager.GetString("CreditLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CreditLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("CreditLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search for and select the item you would like to use as the source for the new line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CreditLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("CreditLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Line.
        /// </summary>
        internal static string CreditLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("CreditLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CreditMainInfo_Edit {
            get {
                return ResourceManager.GetString("CreditMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Credit and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Credit_Add {
            get {
                return ResourceManager.GetString("Credit_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to send credit email?.
        /// </summary>
        internal static string Credit_Email {
            get {
                return ResourceManager.GetString("Credit_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Customer RMA and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CRMAAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CRMAAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Customer RMA.
        /// </summary>
        internal static string CRMAAdd_Notify {
            get {
                return ResourceManager.GetString("CRMAAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Invoice on which you would like to base this new Customer RMA and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CRMAAdd_SelectInvoice {
            get {
                return ResourceManager.GetString("CRMAAdd_SelectInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Customer RMA Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CRMALines_Add {
            get {
                return ResourceManager.GetString("CRMALines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CRMALines_Add_EnterDetail {
            get {
                return ResourceManager.GetString("CRMALines_Add_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search for and select the invoice line you would like to use as the source for the new Line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CRMALines_Add_SelectItem {
            get {
                return ResourceManager.GetString("CRMALines_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close this Customer RMA Line?.
        /// </summary>
        internal static string CRMALines_Close {
            get {
                return ResourceManager.GetString("CRMALines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to remove the selected Allocation(s)?.
        /// </summary>
        internal static string CRMALines_Deallocate {
            get {
                return ResourceManager.GetString("CRMALines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Customer RMA Line?.
        /// </summary>
        internal static string CRMALines_Delete {
            get {
                return ResourceManager.GetString("CRMALines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Customer RMA Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CRMALines_Edit {
            get {
                return ResourceManager.GetString("CRMALines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Internal Log.
        /// </summary>
        internal static string CRMAMainInfo_AddExpedite {
            get {
                return ResourceManager.GetString("CRMAMainInfo_AddExpedite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CRMAMainInfo_Edit {
            get {
                return ResourceManager.GetString("CRMAMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Would you also like to de-allocate selected serial Nos.
        /// </summary>
        internal static string CRMAReceiveLines_Confirm {
            get {
                return ResourceManager.GetString("CRMAReceiveLines_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm the receipt of the Customer RMA Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CRMAReceivingLines_Receive {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Goods In Note line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_Detail {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Goods In Note and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_Header {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Goods In Note detail.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_Notify {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Invoice Line Allocation you would like to use for the Goods In Line.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_SelectItem {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Customer RMA Line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CRMAReceivingLines_Receive_Source {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive_Source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Customer RMA and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CRMA_Add {
            get {
                return ResourceManager.GetString("CRMA_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CSV File.
        /// </summary>
        internal static string CSV_Add {
            get {
                return ResourceManager.GetString("CSV_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the Currency and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CurrencyAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CurrencyAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source of the Currency from the Master Currency List and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string CurrencyAdd_SelectSource {
            get {
                return ResourceManager.GetString("CurrencyAdd_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Currency Rate?.
        /// </summary>
        internal static string CurrencyRateHistory_Delete {
            get {
                return ResourceManager.GetString("CurrencyRateHistory_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details of the Currency Rate and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CurrencyRateHistory_Edit {
            get {
                return ResourceManager.GetString("CurrencyRateHistory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Currency and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Currency_Add {
            get {
                return ResourceManager.GetString("Currency_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Currency and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Currency_Edit {
            get {
                return ResourceManager.GetString("Currency_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the current Currency Rates and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Currency_EditRates {
            get {
                return ResourceManager.GetString("Currency_EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the detail of the new Requirement.
        /// </summary>
        internal static string CusReqAdd_EnterDetail {
            get {
                return ResourceManager.GetString("CusReqAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the detail of the new Requirement Step 1.
        /// </summary>
        internal static string CusReqAdd_EnterDetail1 {
            get {
                return ResourceManager.GetString("CusReqAdd_EnterDetail1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the detail of the new Requirement Step 2.
        /// </summary>
        internal static string CusReqAdd_EnterDetail2 {
            get {
                return ResourceManager.GetString("CusReqAdd_EnterDetail2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Requirement.
        /// </summary>
        internal static string CusReqAdd_Notify {
            get {
                return ResourceManager.GetString("CusReqAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Company for the new Requirement.
        /// </summary>
        internal static string CusReqAdd_SelectCompany {
            get {
                return ResourceManager.GetString("CusReqAdd_SelectCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the Alternate Part Requirement and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CusReqMainInfo_AddAlternate {
            get {
                return ResourceManager.GetString("CusReqMainInfo_AddAlternate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close this Customer Requirement?.
        /// </summary>
        internal static string CusReqMainInfo_Close {
            get {
                return ResourceManager.GetString("CusReqMainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete these Alternate Part(s)?.
        /// </summary>
        internal static string CusReqMainInfo_Delete {
            get {
                return ResourceManager.GetString("CusReqMainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details of the Requirement and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CusReqMainInfo_Edit {
            get {
                return ResourceManager.GetString("CusReqMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the Sourcing Result and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CusReqSourcingResults_Add {
            get {
                return ResourceManager.GetString("CusReqSourcingResults_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete selected item?.
        /// </summary>
        internal static string CusReqSourcingResults_Delete {
            get {
                return ResourceManager.GetString("CusReqSourcingResults_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details of the Sourcing Result and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string CusReqSourcingResults_Edit {
            get {
                return ResourceManager.GetString("CusReqSourcingResults_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer requirement all information.
        /// </summary>
        internal static string CustReqAllInfo {
            get {
                return ResourceManager.GetString("CustReqAllInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Debit and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string DebitAdd_EnterDetail {
            get {
                return ResourceManager.GetString("DebitAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Debit .
        /// </summary>
        internal static string DebitAdd_Notify {
            get {
                return ResourceManager.GetString("DebitAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Purchase Order on which you would like to base this new Debit and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string DebitAdd_SelectPO {
            get {
                return ResourceManager.GetString("DebitAdd_SelectPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Debit Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string DebitLines_Add {
            get {
                return ResourceManager.GetString("DebitLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Debit Line?.
        /// </summary>
        internal static string DebitLines_Delete {
            get {
                return ResourceManager.GetString("DebitLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Debit Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string DebitLines_Edit {
            get {
                return ResourceManager.GetString("DebitLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string DebitLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("DebitLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the PO Line you would like to use as the source for the new line.
        /// </summary>
        internal static string DebitLine_Add_SelectPOLine {
            get {
                return ResourceManager.GetString("DebitLine_Add_SelectPOLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search for and select the Service you would like to use as the source for the new line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string DebitLine_Add_SelectService {
            get {
                return ResourceManager.GetString("DebitLine_Add_SelectService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Line.
        /// </summary>
        internal static string DebitLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("DebitLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string DebitMainInfo_Edit {
            get {
                return ResourceManager.GetString("DebitMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Debit and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Debit_Add {
            get {
                return ResourceManager.GetString("Debit_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to send debit email?.
        /// </summary>
        internal static string Debit_Email {
            get {
                return ResourceManager.GetString("Debit_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete selected item?.
        /// </summary>
        internal static string DeletePartWatch {
            get {
                return ResourceManager.GetString("DeletePartWatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Division and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Division_Add {
            get {
                return ResourceManager.GetString("Division_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Division and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Division_Edit {
            get {
                return ResourceManager.GetString("Division_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the text of the Document Footer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string DocFooters_Edit {
            get {
                return ResourceManager.GetString("DocFooters_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a new image for the Document Headers, 710 pixels wide and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string DocHeaderImage_Add {
            get {
                return ResourceManager.GetString("DocHeaderImage_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the Document Header Image?.
        /// </summary>
        internal static string DocHeaderImage_Delete {
            get {
                return ResourceManager.GetString("DocHeaderImage_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Document Header Image and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string DocHeaderImage_Edit {
            get {
                return ResourceManager.GetString("DocHeaderImage_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new ECCN and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ECCN_Add {
            get {
                return ResourceManager.GetString("ECCN_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the ECCN and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ECCN_Edit {
            get {
                return ResourceManager.GetString("ECCN_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the ECCN Cdoe  of the Warning Message and press Save.
        /// </summary>
        internal static string ECCN_EditMap {
            get {
                return ResourceManager.GetString("ECCN_EditMap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT INVOICE INFORMATION AFTER RELEASE.
        /// </summary>
        internal static string editinvoiceinfoafterrelease {
            get {
                return ResourceManager.GetString("editinvoiceinfoafterrelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Root Cause category and press Save.
        /// </summary>
        internal static string EightDCode_Add {
            get {
                return ResourceManager.GetString("EightDCode_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter All of the details and press &lt;b&gt; Save &lt;/b&gt;.
        /// </summary>
        internal static string EightDCode_Edit {
            get {
                return ResourceManager.GetString("EightDCode_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Root Cause sub category and press &lt;b&gt; Save&lt;/b&gt;.
        /// </summary>
        internal static string EightDSubCategory_Add {
            get {
                return ResourceManager.GetString("EightDSubCategory_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string EigthDSubCategory_Edit {
            get {
                return ResourceManager.GetString("EigthDSubCategory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter message for invoice bulk email.
        /// </summary>
        internal static string EmailComposer_Edit {
            get {
                return ResourceManager.GetString("EmailComposer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the email and press &lt;b&gt;Send&lt;/b&gt;.
        /// </summary>
        internal static string EmailDocument {
            get {
                return ResourceManager.GetString("EmailDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Enhanced Inspection Test.
        /// </summary>
        internal static string EnhanceInspectionTest_Delete {
            get {
                return ResourceManager.GetString("EnhanceInspectionTest_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Test and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string EnhanceInspection_Add {
            get {
                return ResourceManager.GetString("EnhanceInspection_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string EnterDetailsAndPressSave {
            get {
                return ResourceManager.GetString("EnterDetailsAndPressSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Entertainment Type and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string EntertainmentType_Add {
            get {
                return ResourceManager.GetString("EntertainmentType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Entertainment Type and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string EntertainmentType_Edit {
            get {
                return ResourceManager.GetString("EntertainmentType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others about this EPR.
        /// </summary>
        internal static string EPRNotify_Notify {
            get {
                return ResourceManager.GetString("EPRNotify_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string EXCELDragDrop_Add {
            get {
                return ResourceManager.GetString("EXCELDragDrop_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a new Excel (maximum size {0}mb) and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string EXCEL_Add {
            get {
                return ResourceManager.GetString("EXCEL_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Excel?.
        /// </summary>
        internal static string EXCEL_Delete {
            get {
                return ResourceManager.GetString("EXCEL_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Excel or Doc?.
        /// </summary>
        internal static string EXCEL_DOC_Delete {
            get {
                return ResourceManager.GetString("EXCEL_DOC_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ExportApproval_Edit {
            get {
                return ResourceManager.GetString("ExportApproval_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please send us your thoughts and comments about &lt;b&gt;Rebound Global:Trader&lt;/b&gt;
        ///&lt;br /&gt;We are committed to its continual improvement and would love to hear any suggestions you have. 
        ///&lt;br /&gt;Thank you..
        /// </summary>
        internal static string Feedback_Add {
            get {
                return ResourceManager.GetString("Feedback_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the new Goods In Note and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GIAdd_EnterDetail {
            get {
                return ResourceManager.GetString("GIAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Goods In Note.
        /// </summary>
        internal static string GIAdd_Notify {
            get {
                return ResourceManager.GetString("GIAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Purchase Order on which you would like to base this new Goods In Note and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string GIAdd_SelectPO {
            get {
                return ResourceManager.GetString("GIAdd_SelectPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GILineNotify_Notify {
            get {
                return ResourceManager.GetString("GILineNotify_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add the details and press Save.
        /// </summary>
        internal static string GILines_AddShortShipment {
            get {
                return ResourceManager.GetString("GILines_AddShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to complete inspection of this GI Line?.
        /// </summary>
        internal static string GILines_CloseInspection {
            get {
                return ResourceManager.GetString("GILines_CloseInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Goods In Line?.
        /// </summary>
        internal static string GILines_Delete {
            get {
                return ResourceManager.GetString("GILines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GILines_Edit {
            get {
                return ResourceManager.GetString("GILines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select images and press &lt;b&gt;export&lt;/b&gt; .
        /// </summary>
        internal static string GILines_Export {
            get {
                return ResourceManager.GetString("GILines_Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload images and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GILines_ImageUpload {
            get {
                return ResourceManager.GetString("GILines_ImageUpload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm the inspection details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GILines_Inspect {
            get {
                return ResourceManager.GetString("GILines_Inspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GILines Notify.
        /// </summary>
        internal static string GILines_Notify {
            get {
                return ResourceManager.GetString("GILines_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GILines_NPRPrinted {
            get {
                return ResourceManager.GetString("GILines_NPRPrinted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm the inspection details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GILines_PhysicalInspect {
            get {
                return ResourceManager.GetString("GILines_PhysicalInspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Goods In Label.
        /// </summary>
        internal static string GILines_PrintLabel {
            get {
                return ResourceManager.GetString("GILines_PrintLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Goods In Label CRX B2B.
        /// </summary>
        internal static string GILines_PrintLabelCRX {
            get {
                return ResourceManager.GetString("GILines_PrintLabelCRX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Goods In Label Rejected.
        /// </summary>
        internal static string GILines_PrintLabelRejected {
            get {
                return ResourceManager.GetString("GILines_PrintLabelRejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Goods In Label Stock.
        /// </summary>
        internal static string GILines_PrintLabelStock {
            get {
                return ResourceManager.GetString("GILines_PrintLabelStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to quarantine this GI line? Once you quarantine, the system will deallocate the associated Sales Order lines, close all open queries related to this GI line, and set this GI line status as Released..
        /// </summary>
        internal static string GILines_Quarantine {
            get {
                return ResourceManager.GetString("GILines_Quarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GILines_SplitGI {
            get {
                return ResourceManager.GetString("GILines_SplitGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to start inspection of this GI Line?.
        /// </summary>
        internal static string GILines_StartInspection {
            get {
                return ResourceManager.GetString("GILines_StartInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a new PDF and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GILInes_UploadPDF {
            get {
                return ResourceManager.GetString("GILInes_UploadPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GIMainInfo_Edit {
            get {
                return ResourceManager.GetString("GIMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others about this Goods In Note.
        /// </summary>
        internal static string GIMainInfo_Notify {
            get {
                return ResourceManager.GetString("GIMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the changed details of the new Global Product Main Category and press Save.
        /// </summary>
        internal static string GlobalCategoryName_Edit {
            get {
                return ResourceManager.GetString("GlobalCategoryName_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Master Country and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GlobalCountryList_Add {
            get {
                return ResourceManager.GetString("GlobalCountryList_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details of the Master Country and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GlobalCountryList_Edit {
            get {
                return ResourceManager.GetString("GlobalCountryList_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Currency and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GlobalCurrencyList_Add {
            get {
                return ResourceManager.GetString("GlobalCurrencyList_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details of the Master Currency and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GlobalCurrencyList_Edit {
            get {
                return ResourceManager.GetString("GlobalCurrencyList_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Client Product and press Save.
        /// </summary>
        internal static string GlobalProductDutyRateHistory_Edit {
            get {
                return ResourceManager.GetString("GlobalProductDutyRateHistory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Global Product Category Name and press Save.
        /// </summary>
        internal static string GlobalProductMainCategory_Add {
            get {
                return ResourceManager.GetString("GlobalProductMainCategory_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Map product to category.
        /// </summary>
        internal static string GlobalProductMainCategory_Map {
            get {
                return ResourceManager.GetString("GlobalProductMainCategory_Map", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Global Product Name and press Save.
        /// </summary>
        internal static string GlobalProductName_Add {
            get {
                return ResourceManager.GetString("GlobalProductName_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the changed details of the new Global Product Name and press Save.
        /// </summary>
        internal static string GlobalProductName_Edit {
            get {
                return ResourceManager.GetString("GlobalProductName_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Global Product and press Save.
        /// </summary>
        internal static string GlobalProduct_Add {
            get {
                return ResourceManager.GetString("GlobalProduct_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Global Product and press Save.
        /// </summary>
        internal static string GlobalProduct_Edit {
            get {
                return ResourceManager.GetString("GlobalProduct_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Members for this Security Group and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GlobalSecurityGroupMembers_EditMembers {
            get {
                return ResourceManager.GetString("GlobalSecurityGroupMembers_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the permissions and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GlobalSeGroupPermissionsGeneral_Edit {
            get {
                return ResourceManager.GetString("GlobalSeGroupPermissionsGeneral_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Master Tax and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GlobalTax_Edit {
            get {
                return ResourceManager.GetString("GlobalTax_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend all the Master Tax Rates and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string GlobalTax_EditRates {
            get {
                return ResourceManager.GetString("GlobalTax_EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Members for this GSA Group and press Save.
        /// </summary>
        internal static string GSA_EditMembers {
            get {
                return ResourceManager.GetString("GSA_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add GT Application Update Information.
        /// </summary>
        internal static string GTUpdate_Add {
            get {
                return ResourceManager.GetString("GTUpdate_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the GT Notifications and press Save.
        /// </summary>
        internal static string GTUpdate_Edit {
            get {
                return ResourceManager.GetString("GTUpdate_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Part and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string IHSAdd_Add {
            get {
                return ResourceManager.GetString("IHSAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Incoterm and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Incoterm_Add {
            get {
                return ResourceManager.GetString("Incoterm_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to disable this Incoterm?.
        /// </summary>
        internal static string Incoterm_Disable {
            get {
                return ResourceManager.GetString("Incoterm_Disable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Incoterm and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Incoterm_Edit {
            get {
                return ResourceManager.GetString("Incoterm_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to enable this Incoterm?.
        /// </summary>
        internal static string Incoterm_Enable {
            get {
                return ResourceManager.GetString("Incoterm_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Industry Type and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string IndustryType_Add {
            get {
                return ResourceManager.GetString("IndustryType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Industry Type and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string IndustryType_Edit {
            get {
                return ResourceManager.GetString("IndustryType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Internal Purchase Order Line.
        /// </summary>
        internal static string InternalPOLines_Edit {
            get {
                return ResourceManager.GetString("InternalPOLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please add Expedite Note.
        /// </summary>
        internal static string InternalPOMainInfo_AddExpedite {
            get {
                return ResourceManager.GetString("InternalPOMainInfo_AddExpedite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string InternalPOMainInfo_Edit {
            get {
                return ResourceManager.GetString("InternalPOMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the new Invoice and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceAdd_EnterDetail {
            get {
                return ResourceManager.GetString("InvoiceAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Invoice.
        /// </summary>
        internal static string InvoiceAdd_Notify {
            get {
                return ResourceManager.GetString("InvoiceAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Sales Order on which you would like to base this new Invoice and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceAdd_SelectSO {
            get {
                return ResourceManager.GetString("InvoiceAdd_SelectSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Invoice Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceLines_Add {
            get {
                return ResourceManager.GetString("InvoiceLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm the details of the new line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceLines_Add_EnterDetail {
            get {
                return ResourceManager.GetString("InvoiceLines_Add_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search for and select the service line you would like to use as the source for the new Line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceLines_Add_SelectItem {
            get {
                return ResourceManager.GetString("InvoiceLines_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Line.
        /// </summary>
        internal static string InvoiceLines_Add_SelectSource {
            get {
                return ResourceManager.GetString("InvoiceLines_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Invoice Line?.
        /// </summary>
        internal static string InvoiceLines_Delete {
            get {
                return ResourceManager.GetString("InvoiceLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Invoice Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceLines_Edit {
            get {
                return ResourceManager.GetString("InvoiceLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Invoice Line Allocation and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceLines_EditAllocation {
            get {
                return ResourceManager.GetString("InvoiceLines_EditAllocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the Bank Charge Fee and press Save.
        /// </summary>
        internal static string InvoiceLines_EditBankFee {
            get {
                return ResourceManager.GetString("InvoiceLines_EditBankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Invoice and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceMainInfo_Edit {
            get {
                return ResourceManager.GetString("InvoiceMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string InvoiceMainInfo_EditShippingInfo {
            get {
                return ResourceManager.GetString("InvoiceMainInfo_EditShippingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Setting.
        /// </summary>
        internal static string InvoiceSetting_Add {
            get {
                return ResourceManager.GetString("InvoiceSetting_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule invoice.
        /// </summary>
        internal static string InvoiceSetting_Edit {
            get {
                return ResourceManager.GetString("InvoiceSetting_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to send invoice email?.
        /// </summary>
        internal static string Invoice_Email {
            get {
                return ResourceManager.GetString("Invoice_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LabelFullPath_Add {
            get {
                return ResourceManager.GetString("LabelFullPath_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LabelFullPath_Edit {
            get {
                return ResourceManager.GetString("LabelFullPath_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Master Status item and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LabelSetupItem_Add {
            get {
                return ResourceManager.GetString("LabelSetupItem_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Master Status item and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LabelSetupItem_Edit {
            get {
                return ResourceManager.GetString("LabelSetupItem_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This functionality allows a stopped company&apos;s sales order to ship, Are you sure?.
        /// </summary>
        internal static string lblExplainReadyToShip {
            get {
                return ResourceManager.GetString("lblExplainReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the local currency and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LocalCurrency_Add {
            get {
                return ResourceManager.GetString("LocalCurrency_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of  local currency and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LocalCurrency_Edit {
            get {
                return ResourceManager.GetString("LocalCurrency_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Lot and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LotAdd_Add {
            get {
                return ResourceManager.GetString("LotAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the unallocated Services for this Lot?.
        /// </summary>
        internal static string LotItems_Delete_Service {
            get {
                return ResourceManager.GetString("LotItems_Delete_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the unallocated Stock for this Lot?.
        /// </summary>
        internal static string LotItems_Delete_Stock {
            get {
                return ResourceManager.GetString("LotItems_Delete_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to save stock provision.
        /// </summary>
        internal static string LotItems_LotSave {
            get {
                return ResourceManager.GetString("LotItems_LotSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the stock provision and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LotItems_LotStockProvision {
            get {
                return ResourceManager.GetString("LotItems_LotStockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the stock provision and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LotItems_StockProvision {
            get {
                return ResourceManager.GetString("LotItems_StockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the new Lot to transfer the Services to and confirm you are sure.
        /// </summary>
        internal static string LotItems_Transfer_Service {
            get {
                return ResourceManager.GetString("LotItems_Transfer_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the new Lot to transfer the Stock to and confirm you are sure.
        /// </summary>
        internal static string LotItems_Transfer_Stock {
            get {
                return ResourceManager.GetString("LotItems_Transfer_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Lot?.
        /// </summary>
        internal static string LotMainInfo_Delete {
            get {
                return ResourceManager.GetString("LotMainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string LotMainInfo_Edit {
            get {
                return ResourceManager.GetString("LotMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the members of the Mail Group and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string MailMessageGroupMembers_EditMembers {
            get {
                return ResourceManager.GetString("MailMessageGroupMembers_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Mail Group and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string MailMessageGroups_Add {
            get {
                return ResourceManager.GetString("MailMessageGroups_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the Mail Group?.
        /// </summary>
        internal static string MailMessageGroups_Delete {
            get {
                return ResourceManager.GetString("MailMessageGroups_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details of the Mail Group and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string MailMessageGroups_Edit {
            get {
                return ResourceManager.GetString("MailMessageGroups_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this folder?.
        /// </summary>
        internal static string MailMessages_DeleteFolder {
            get {
                return ResourceManager.GetString("MailMessages_DeleteFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this/these message(s)?.
        /// </summary>
        internal static string MailMessages_DeleteMessage {
            get {
                return ResourceManager.GetString("MailMessages_DeleteMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter a new name for this folder and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string MailMessages_EditFolder {
            get {
                return ResourceManager.GetString("MailMessages_EditFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new To Do item and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string MailMessages_MarkAsToDo {
            get {
                return ResourceManager.GetString("MailMessages_MarkAsToDo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a destination folder for this/these message(s) and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string MailMessages_MoveMessage {
            get {
                return ResourceManager.GetString("MailMessages_MoveMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter a name for the new folder and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string MailMessages_NewFolder {
            get {
                return ResourceManager.GetString("MailMessages_NewFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new message and press &lt;b&gt;Send&lt;/b&gt;.
        /// </summary>
        internal static string MailMessages_NewMessage {
            get {
                return ResourceManager.GetString("MailMessages_NewMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Manufacturer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ManufacturerAdd_Add {
            get {
                return ResourceManager.GetString("ManufacturerAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a new company and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ManufacturerCompanies_Add {
            get {
                return ResourceManager.GetString("ManufacturerCompanies_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to remove this related Company?.
        /// </summary>
        internal static string ManufacturerCompanies_Delete {
            get {
                return ResourceManager.GetString("ManufacturerCompanies_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details for the Manufacturer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ManufacturerMainInfo_Edit {
            get {
                return ResourceManager.GetString("ManufacturerMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ManufacturerSuppliers_AddEdit {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers_AddEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to remove this Supplier?.
        /// </summary>
        internal static string ManufacturerSuppliers_Delete {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View the details below.
        /// </summary>
        internal static string ManufacturerSuppliers_View {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to mark client bom as complete?.
        /// </summary>
        internal static string MarkasCompleteConfirm {
            get {
                return ResourceManager.GetString("MarkasCompleteConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the master login and press Save.
        /// </summary>
        internal static string MasterLogin_Add {
            get {
                return ResourceManager.GetString("MasterLogin_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others about this NPR.
        /// </summary>
        internal static string NPRNotify_Notify {
            get {
                return ResourceManager.GetString("NPRNotify_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure would like to authorise this export?.
        /// </summary>
        internal static string OGELApproveExplntn {
            get {
                return ResourceManager.GetString("OGELApproveExplntn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new OGEL License and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string OGELLicenses_Add {
            get {
                return ResourceManager.GetString("OGELLicenses_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the OGEL License and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string OGELLicenses_Edit {
            get {
                return ResourceManager.GetString("OGELLicenses_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Package and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Package_Add {
            get {
                return ResourceManager.GetString("Package_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Package and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Package_Edit {
            get {
                return ResourceManager.GetString("Package_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Document File Size and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string PDFDocumentFileSize_Add {
            get {
                return ResourceManager.GetString("PDFDocumentFileSize_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Document File Size and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string PDFDocumentFileSize_Edit {
            get {
                return ResourceManager.GetString("PDFDocumentFileSize_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string PDFDragDrop_Add {
            get {
                return ResourceManager.GetString("PDFDragDrop_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a new PDF (maximum size {0}mb) and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string PDF_Add {
            get {
                return ResourceManager.GetString("PDF_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this PDF?.
        /// </summary>
        internal static string PDF_Delete {
            get {
                return ResourceManager.GetString("PDF_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Purchase Order and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string POAdd_EnterDetail {
            get {
                return ResourceManager.GetString("POAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Purchase Order.
        /// </summary>
        internal static string POAdd_Notify {
            get {
                return ResourceManager.GetString("POAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Company from which you are buying.
        /// </summary>
        internal static string POAdd_SelectCompany {
            get {
                return ResourceManager.GetString("POAdd_SelectCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close this Purchase Order Line?.
        /// </summary>
        internal static string POLines_Close {
            get {
                return ResourceManager.GetString("POLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to deallocate this Purchase Order Line?.
        /// </summary>
        internal static string POLines_Deallocate {
            get {
                return ResourceManager.GetString("POLines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Purchase Order Line?.
        /// </summary>
        internal static string POLines_Delete {
            get {
                return ResourceManager.GetString("POLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Purchase Order line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string POLines_Edit {
            get {
                return ResourceManager.GetString("POLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string POLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("POLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search for and select the item you would like to use as the source for the new Line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string POLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("POLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Line.
        /// </summary>
        internal static string POLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("POLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please add Expedite Note.
        /// </summary>
        internal static string POMainInfo_AddExpedite {
            get {
                return ResourceManager.GetString("POMainInfo_AddExpedite", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close this Purchase Order?.
        /// </summary>
        internal static string POMainInfo_Close {
            get {
                return ResourceManager.GetString("POMainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string POMainInfo_Edit {
            get {
                return ResourceManager.GetString("POMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of this Purchase Order.
        /// </summary>
        internal static string POMainInfo_Notify {
            get {
                return ResourceManager.GetString("POMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the selected Price Request ?.
        /// </summary>
        internal static string POQuoteLines_Close {
            get {
                return ResourceManager.GetString("POQuoteLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string POQuoteMainInfo_Edit {
            get {
                return ResourceManager.GetString("POQuoteMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of this Price Request.
        /// </summary>
        internal static string POQuoteMainInfo_Notify {
            get {
                return ResourceManager.GetString("POQuoteMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm the receipt of the Purchase Order Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string POReceivingLines_Receive {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Goods In Note line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string POReceivingLines_Receive_Detail {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Goods In Note.
        /// </summary>
        internal static string POReceivingLines_Receive_Header {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select an existing Goods In Note.
        /// </summary>
        internal static string POReceivingLines_Receive_Header_ExistingGI {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Header_ExistingGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Goods In Note and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string POReceivingLines_Receive_Header_NewGI {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Header_NewGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the received Purchase Order Line against a new Goods In Note or an existing one and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string POReceivingLines_Receive_NewOrExisting {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_NewOrExisting", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Goods In Note detail.
        /// </summary>
        internal static string POReceivingLines_Receive_Notify {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the target for the new receipt and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string POReceivingLines_Receive_Target {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive_Target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new PPV/ BOM Qualification and press Save.
        /// </summary>
        internal static string PPVBOM_Add {
            get {
                return ResourceManager.GetString("PPVBOM_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the PPV/ BOM Qualification and press Save.
        /// </summary>
        internal static string PPVBOM_Edit {
            get {
                return ResourceManager.GetString("PPVBOM_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Printer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Printer_Add {
            get {
                return ResourceManager.GetString("Printer_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Printer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Printer_Edit {
            get {
                return ResourceManager.GetString("Printer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Product and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Product_Add {
            get {
                return ResourceManager.GetString("Product_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Product and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Product_Edit {
            get {
                return ResourceManager.GetString("Product_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this PPV/ BOM Qualification Question &amp; Answer?.
        /// </summary>
        internal static string PVVBOM_Delete {
            get {
                return ResourceManager.GetString("PVVBOM_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Quote and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string QuoteAdd_EnterDetail {
            get {
                return ResourceManager.GetString("QuoteAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Quote.
        /// </summary>
        internal static string QuoteAdd_Notify {
            get {
                return ResourceManager.GetString("QuoteAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Company for which you would like to add this Quote.
        /// </summary>
        internal static string QuoteAdd_SelectCompany {
            get {
                return ResourceManager.GetString("QuoteAdd_SelectCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the lines from the Requirement you would like to carry into the new quote and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string QuoteAdd_SelectLines {
            get {
                return ResourceManager.GetString("QuoteAdd_SelectLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Requirement and Sourcing Results on which you would like to base this new Quote and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string QuoteAdd_SelectRequirement {
            get {
                return ResourceManager.GetString("QuoteAdd_SelectRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Quote and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string QuoteAdd_SelectSource {
            get {
                return ResourceManager.GetString("QuoteAdd_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close this Quote Line?.
        /// </summary>
        internal static string QuoteLines_Close {
            get {
                return ResourceManager.GetString("QuoteLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Quote Line?.
        /// </summary>
        internal static string QuoteLines_Delete {
            get {
                return ResourceManager.GetString("QuoteLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Quote Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string QuoteLines_Edit {
            get {
                return ResourceManager.GetString("QuoteLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Quote Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string QuoteLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("QuoteLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search  the Lot details for the new Quote Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string QuoteLine_Add_SearchLotDetails {
            get {
                return ResourceManager.GetString("QuoteLine_Add_SearchLotDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search for and select the item you would like to use as the source for the new Quote Line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string QuoteLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("QuoteLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Line.
        /// </summary>
        internal static string QuoteLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("QuoteLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string QuoteMainInfo_Edit {
            get {
                return ResourceManager.GetString("QuoteMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Reason and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Reason_Add {
            get {
                return ResourceManager.GetString("Reason_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Reason and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Reason_Edit {
            get {
                return ResourceManager.GetString("Reason_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Restricted Manufacturer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string RestrictedManufacture_Add {
            get {
                return ResourceManager.GetString("RestrictedManufacture_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Restricted Manufacturer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string RestrictedManufacture_Edit {
            get {
                return ResourceManager.GetString("RestrictedManufacture_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales BOM Import.
        /// </summary>
        internal static string SalesBOM_Import {
            get {
                return ResourceManager.GetString("SalesBOM_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save and Send&lt;/b&gt;.
        /// </summary>
        internal static string SATrade_EnterDetail {
            get {
                return ResourceManager.GetString("SATrade_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify for the supplier Approval.
        /// </summary>
        internal static string SATrade_Notify {
            get {
                return ResourceManager.GetString("SATrade_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Once converted to HUBRFQ you cannot make any changes to current Bom&lt;br/&gt;
        ///Are you sure you would like to save this as HUBRFQ?.
        /// </summary>
        internal static string SaveAsHUBRFQ {
            get {
                return ResourceManager.GetString("SaveAsHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Members for this Security Group and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityGroupMembers_EditMembers {
            get {
                return ResourceManager.GetString("SecurityGroupMembers_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the permissions and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityGroupPermissionsGeneral_Edit {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsGeneral_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the permissions and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityGroupPermissionsReports_Edit {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsReports_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Security Group and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityGroups_Add {
            get {
                return ResourceManager.GetString("SecurityGroups_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to make a copy of this Security Group and its permissions?.
        /// </summary>
        internal static string SecurityGroups_Clone {
            get {
                return ResourceManager.GetString("SecurityGroups_Clone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Security Group?.
        /// </summary>
        internal static string SecurityGroups_Delete {
            get {
                return ResourceManager.GetString("SecurityGroups_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details of the Security Group and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityGroups_Edit {
            get {
                return ResourceManager.GetString("SecurityGroups_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the members of this Group and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityUserGroups_EditMembers {
            get {
                return ResourceManager.GetString("SecurityUserGroups_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Security User and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityUsers_Add {
            get {
                return ResourceManager.GetString("SecurityUsers_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to disable this Security Group?.
        /// </summary>
        internal static string SecurityUsers_Disable {
            get {
                return ResourceManager.GetString("SecurityUsers_Disable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Security User and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityUsers_Edit {
            get {
                return ResourceManager.GetString("SecurityUsers_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to enable this Security Group?.
        /// </summary>
        internal static string SecurityUsers_Enable {
            get {
                return ResourceManager.GetString("SecurityUsers_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the user you would like to transfer sales accounts to and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SecurityUsers_Transfer {
            get {
                return ResourceManager.GetString("SecurityUsers_Transfer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Approval to this Sales Order Line.
        /// </summary>
        internal static string SendExportApprovalRequest {
            get {
                return ResourceManager.GetString("SendExportApprovalRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the Sequence Numbers and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sequencer_Edit {
            get {
                return ResourceManager.GetString("Sequencer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Service and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ServiceAdd_Add {
            get {
                return ResourceManager.GetString("ServiceAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to remove this allocation?.
        /// </summary>
        internal static string ServiceAllocations_Deallocate {
            get {
                return ResourceManager.GetString("ServiceAllocations_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Service?.
        /// </summary>
        internal static string ServiceMainInfo_Delete {
            get {
                return ResourceManager.GetString("ServiceMainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Service and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ServiceMainInfo_Edit {
            get {
                return ResourceManager.GetString("ServiceMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Shipping Method and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ShipVia_Add {
            get {
                return ResourceManager.GetString("ShipVia_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Shipping Method and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ShipVia_Edit {
            get {
                return ResourceManager.GetString("ShipVia_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ShortShipmentNotify_Notify {
            get {
                return ResourceManager.GetString("ShortShipmentNotify_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Sales Order and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOAdd_EnterDetail {
            get {
                return ResourceManager.GetString("SOAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Sales Order.
        /// </summary>
        internal static string SOAdd_Notify {
            get {
                return ResourceManager.GetString("SOAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Company for which you would like to add this Sales Order.
        /// </summary>
        internal static string SOAdd_SelectCompany {
            get {
                return ResourceManager.GetString("SOAdd_SelectCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search  the Lot details for the new SO Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOeLine_Add_SearchLotDetails {
            get {
                return ResourceManager.GetString("SOeLine_Add_SearchLotDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close this Sales Order Line?.
        /// </summary>
        internal static string SOLines_Close {
            get {
                return ResourceManager.GetString("SOLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to confirm?.
        /// </summary>
        internal static string SOLines_Confirm {
            get {
                return ResourceManager.GetString("SOLines_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to remove the selected allocation(s)?.
        /// </summary>
        internal static string SOLines_Deallocate {
            get {
                return ResourceManager.GetString("SOLines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Sales Order Line?.
        /// </summary>
        internal static string SOLines_Delete {
            get {
                return ResourceManager.GetString("SOLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOLines_Edit {
            get {
                return ResourceManager.GetString("SOLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to post all unposted lines?.
        /// </summary>
        internal static string SOLines_PostAll {
            get {
                return ResourceManager.GetString("SOLines_PostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to unpost all posted lines?.
        /// </summary>
        internal static string SOLines_UnpostAll {
            get {
                return ResourceManager.GetString("SOLines_UnpostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOLine_Add_EditDetails {
            get {
                return ResourceManager.GetString("SOLine_Add_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search for and select the item you would like to use as the source for the new Line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string SOLine_Add_SelectItem {
            get {
                return ResourceManager.GetString("SOLine_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Line.
        /// </summary>
        internal static string SOLine_Add_SelectSource {
            get {
                return ResourceManager.GetString("SOLine_Add_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the stock item to allocate to this Line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string SOLine_Allocate_SelectStock {
            get {
                return ResourceManager.GetString("SOLine_Allocate_SelectStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the allocation and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOLine_Alocate_EditDetails {
            get {
                return ResourceManager.GetString("SOLine_Alocate_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOLine_EditDetails {
            get {
                return ResourceManager.GetString("SOLine_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search for and select the item you would like to use as the source for the new line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string SOLine_SelectItem {
            get {
                return ResourceManager.GetString("SOLine_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source of the new line and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string SOLine_SelectSource {
            get {
                return ResourceManager.GetString("SOLine_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close this Sales Order?.
        /// </summary>
        internal static string SOMainInfo_Close {
            get {
                return ResourceManager.GetString("SOMainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Would you like to consolidate similar sales order lines ? &lt;br/&gt; (Consolidation is based on same Part, Customer Part, MFR, Product, Package, DC, RoHS, Product Source, Date Promised &amp; Unit Price).
        /// </summary>
        internal static string SoMainInfo_Confirm {
            get {
                return ResourceManager.GetString("SoMainInfo_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to reset print option?.
        /// </summary>
        internal static string SOMainInfo_Consolidate {
            get {
                return ResourceManager.GetString("SOMainInfo_Consolidate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOMainInfo_Edit {
            get {
                return ResourceManager.GetString("SOMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of this Sales Order.
        /// </summary>
        internal static string SOMainInfo_Notify {
            get {
                return ResourceManager.GetString("SOMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay By Credit Card.
        /// </summary>
        internal static string SOMainInfo_PayByCreditCard {
            get {
                return ResourceManager.GetString("SOMainInfo_PayByCreditCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please confirm sales order acknowledgement has been sent to the customer.
        /// </summary>
        internal static string SOMainInfo_SentOrder {
            get {
                return ResourceManager.GetString("SOMainInfo_SentOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string SOMainInfo_Warehouse {
            get {
                return ResourceManager.GetString("SOMainInfo_Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Approval to this Sales Order.
        /// </summary>
        internal static string SORequestApproval {
            get {
                return ResourceManager.GetString("SORequestApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Would you also like to de-allocate selected serial Nos.
        /// </summary>
        internal static string SoSHippingLines_Confirm {
            get {
                return ResourceManager.GetString("SoSHippingLines_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm the shipment of the Sales Order Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOShippingLines_Ship {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Invoice line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SOShippingLines_Ship_Detail {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Invoice.
        /// </summary>
        internal static string SOShippingLines_Ship_NewHeader {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_NewHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Invoice detail.
        /// </summary>
        internal static string SOShippingLines_Ship_Notify {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Invoice against which to add the shipped Sales Order Line(s).
        /// </summary>
        internal static string SOShippingLines_Ship_SelectInvoice {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_SelectInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select whether the shipped Sales Order Line(s) should be entered against a new Invoice or an existing one and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string SOShippingLines_Ship_Target {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship_Target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Sourcing Link and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SourcingLinks_Add {
            get {
                return ResourceManager.GetString("SourcingLinks_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Sourcing Link?.
        /// </summary>
        internal static string SourcingLinks_Delete {
            get {
                return ResourceManager.GetString("SourcingLinks_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Sourcing Link and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SourcingLinks_Edit {
            get {
                return ResourceManager.GetString("SourcingLinks_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the offer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_AddOffer {
            get {
                return ResourceManager.GetString("Sourcing_AddOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new sourcing info and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_AddStockInfo {
            get {
                return ResourceManager.GetString("Sourcing_AddStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to add the selected item(s) to the requirement?.
        /// </summary>
        internal static string Sourcing_AddToReq {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to add this/these items to the Customer Requirement?.
        /// </summary>
        internal static string Sourcing_AddToReq_Confirm {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to add this/these items as sourcing result to the HUBRFQ?.
        /// </summary>
        internal static string Sourcing_AddToReq_IPOBOMConfirm {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq_IPOBOMConfirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a Customer Requirement.
        /// </summary>
        internal static string Sourcing_AddToReq_SelectCusReq {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq_SelectCusReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the trusted and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_AddTrusted {
            get {
                return ResourceManager.GetString("Sourcing_AddTrusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Alternative Part and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_EditAltPart {
            get {
                return ResourceManager.GetString("Sourcing_EditAltPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Strategic Offers and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_EditEpo {
            get {
                return ResourceManager.GetString("Sourcing_EditEpo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the offer and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_EditOffer {
            get {
                return ResourceManager.GetString("Sourcing_EditOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Reverse Logistic and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_EditReverseLogistic {
            get {
                return ResourceManager.GetString("Sourcing_EditReverseLogistic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Reverse Logistic Offers and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_EditReverseLogistics {
            get {
                return ResourceManager.GetString("Sourcing_EditReverseLogistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Stock info and press Save.
        /// </summary>
        internal static string Sourcing_EditStock {
            get {
                return ResourceManager.GetString("Sourcing_EditStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the sourcing info and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_EditStockInfo {
            get {
                return ResourceManager.GetString("Sourcing_EditStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the trust and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_EditTrust {
            get {
                return ResourceManager.GetString("Sourcing_EditTrust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Office Import EPO Tool.
        /// </summary>
        internal static string Sourcing_EpoStockImportTool {
            get {
                return ResourceManager.GetString("Sourcing_EpoStockImportTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload Stock Epo FIle.
        /// </summary>
        internal static string Sourcing_EpoTool {
            get {
                return ResourceManager.GetString("Sourcing_EpoTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the message(s) and press &lt;b&gt;Send&lt;/b&gt;.
        /// </summary>
        internal static string Sourcing_RFQ {
            get {
                return ResourceManager.GetString("Sourcing_RFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the required Sourcing fields and click SEARCH.
        /// </summary>
        internal static string SOURCING_SEARCHOFFER {
            get {
                return ResourceManager.GetString("SOURCING_SEARCHOFFER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload Offers Import HUB Tool.
        /// </summary>
        internal static string Sourcing_StockImportEpoTool {
            get {
                return ResourceManager.GetString("Sourcing_StockImportEpoTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string Sourcing_StockImportTool {
            get {
                return ResourceManager.GetString("Sourcing_StockImportTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Supplier RMA and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMAAdd_EnterDetail {
            get {
                return ResourceManager.GetString("SRMAAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Supplier RMA.
        /// </summary>
        internal static string SRMAAdd_Notify {
            get {
                return ResourceManager.GetString("SRMAAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Purchase Order on which you would like to base this new Supplier RMA and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string SRMAAdd_SelectPO {
            get {
                return ResourceManager.GetString("SRMAAdd_SelectPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Supplier RMA Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMALines_Add {
            get {
                return ResourceManager.GetString("SRMALines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the new line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMALines_Add_EnterDetail {
            get {
                return ResourceManager.GetString("SRMALines_Add_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Purchase Order line you would like to use as the source for the new Line.
        /// </summary>
        internal static string SRMALines_Add_SelectItem {
            get {
                return ResourceManager.GetString("SRMALines_Add_SelectItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to remove the selected Allocation(s)?.
        /// </summary>
        internal static string SRMALines_Deallocate {
            get {
                return ResourceManager.GetString("SRMALines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Supplier RMA Line?.
        /// </summary>
        internal static string SRMALines_Delete {
            get {
                return ResourceManager.GetString("SRMALines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Supplier RMA Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMALines_Edit {
            get {
                return ResourceManager.GetString("SRMALines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the allocation and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMALine_Allocate_EditDetails {
            get {
                return ResourceManager.GetString("SRMALine_Allocate_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose the Stock Item to allocate to this SRMA.
        /// </summary>
        internal static string SRMALine_Allocate_SelectStock {
            get {
                return ResourceManager.GetString("SRMALine_Allocate_SelectStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the SRMA Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMALine_Alocate_EditDetails {
            get {
                return ResourceManager.GetString("SRMALine_Alocate_EditDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMAMainInfo_Edit {
            get {
                return ResourceManager.GetString("SRMAMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Debit Line and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMAShippingLines_Ship_Detail {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship_Detail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter details of the new Debit Note press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMAShippingLines_Ship_Header {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship_Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Debit Note?.
        /// </summary>
        internal static string SRMAShippingLines_Ship_Notify {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the source for the new Debit Note and press &lt;b&gt;Continue&lt;/b&gt;.
        /// </summary>
        internal static string SRMAShippingLines_Ship_SelectSource {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship_SelectSource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Supplier RMA and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SRMA_Add {
            get {
                return ResourceManager.GetString("SRMA_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to cancel the Short Shipment?.
        /// </summary>
        internal static string SSMainInfo_Cancel {
            get {
                return ResourceManager.GetString("SSMainInfo_Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to close the Short Shipment?.
        /// </summary>
        internal static string SSMainInfo_Close {
            get {
                return ResourceManager.GetString("SSMainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to update the Short Shipment?.
        /// </summary>
        internal static string SSMainInfo_Confirm {
            get {
                return ResourceManager.GetString("SSMainInfo_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Star Rating and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StarRating_Add {
            get {
                return ResourceManager.GetString("StarRating_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Stock and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StockAdd_Add {
            get {
                return ResourceManager.GetString("StockAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to Deallocate this Stock?.
        /// </summary>
        internal static string StockAllocations_Deallocate {
            get {
                return ResourceManager.GetString("StockAllocations_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StockImagesDragDrop_Add {
            get {
                return ResourceManager.GetString("StockImagesDragDrop_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a new Image (maximum size {0}mb) and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StockImages_Add {
            get {
                return ResourceManager.GetString("StockImages_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this Image?.
        /// </summary>
        internal static string StockImages_Delete {
            get {
                return ResourceManager.GetString("StockImages_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new StockLog Reason and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StockLogReason_Add {
            get {
                return ResourceManager.GetString("StockLogReason_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the StockLog Reason and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StockLogReason_Edit {
            get {
                return ResourceManager.GetString("StockLogReason_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the Stock and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StockMainInfo_Edit {
            get {
                return ResourceManager.GetString("StockMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to release this Stock?.
        /// </summary>
        internal static string StockMainInfo_MakeAvailable {
            get {
                return ResourceManager.GetString("StockMainInfo_MakeAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Excess Label.
        /// </summary>
        internal static string StockMainInfo_PrintExcessLabelStock {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintExcessLabelStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Stock Label.
        /// </summary>
        internal static string StockMainInfo_PrintLabel {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Stock Label CRX B2B.
        /// </summary>
        internal static string StockMainInfo_PrintLabelCRX {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintLabelCRX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Stock Label Rejected.
        /// </summary>
        internal static string StockMainInfo_PrintLabelRejected {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintLabelRejected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Label Stock.
        /// </summary>
        internal static string StockMainInfo_PrintLabelStock {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintLabelStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to quarantine this Stock?.
        /// </summary>
        internal static string StockMainInfo_Quarantine {
            get {
                return ResourceManager.GetString("StockMainInfo_Quarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the split press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StockMainInfo_Split {
            get {
                return ResourceManager.GetString("StockMainInfo_Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details of the stock provision and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string StockMainInfo_StockProvision {
            get {
                return ResourceManager.GetString("StockMainInfo_StockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string String1 {
            get {
                return ResourceManager.GetString("String1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the details of the new Supplier Invoice and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SupplierInvoiceAdd_EnterDetail {
            get {
                return ResourceManager.GetString("SupplierInvoiceAdd_EnterDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others of the new Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoiceAdd_Notify {
            get {
                return ResourceManager.GetString("SupplierInvoiceAdd_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select the Supplier Company.
        /// </summary>
        internal static string SupplierInvoiceAdd_SelectCompany {
            get {
                return ResourceManager.GetString("SupplierInvoiceAdd_SelectCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SupplierInvoiceLines_Add {
            get {
                return ResourceManager.GetString("SupplierInvoiceLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the selected Supplier Invoice Line?.
        /// </summary>
        internal static string SupplierInvoiceLines_Delete {
            get {
                return ResourceManager.GetString("SupplierInvoiceLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string SupplierInvoiceMainInfo_Edit {
            get {
                return ResourceManager.GetString("SupplierInvoiceMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify others about this Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoiceMainInfo_Notify {
            get {
                return ResourceManager.GetString("SupplierInvoiceMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Supplier_Approval_Edit {
            get {
                return ResourceManager.GetString("Supplier_Approval_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the permissions and press Save.
        /// </summary>
        internal static string TabSecurity_Edit {
            get {
                return ResourceManager.GetString("TabSecurity_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this future tax rate?.
        /// </summary>
        internal static string TaxRateHistory_Delete {
            get {
                return ResourceManager.GetString("TaxRateHistory_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Tax and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Tax_Add {
            get {
                return ResourceManager.GetString("Tax_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Tax and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Tax_Edit {
            get {
                return ResourceManager.GetString("Tax_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amend all the Tax Rates and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Tax_EditRates {
            get {
                return ResourceManager.GetString("Tax_EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Team and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Team_Add {
            get {
                return ResourceManager.GetString("Team_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Team and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Team_Edit {
            get {
                return ResourceManager.GetString("Team_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Terms and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Terms_Add {
            get {
                return ResourceManager.GetString("Terms_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Terms and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Terms_Edit {
            get {
                return ResourceManager.GetString("Terms_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Package and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ToDoListType_Add {
            get {
                return ResourceManager.GetString("ToDoListType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete the selected To Do item(s)?.
        /// </summary>
        internal static string ToDo_Delete {
            get {
                return ResourceManager.GetString("ToDo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all of the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string ToDo_Edit {
            get {
                return ResourceManager.GetString("ToDo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to mark the selected To Do item(s) as complete?.
        /// </summary>
        internal static string ToDo_MarkComplete {
            get {
                return ResourceManager.GetString("ToDo_MarkComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to mark the selected To Do item(s) as incomplete?.
        /// </summary>
        internal static string ToDo_MarkIncomplete {
            get {
                return ResourceManager.GetString("ToDo_MarkIncomplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload images.
        /// </summary>
        internal static string UploadImagesDragDrop_Add {
            get {
                return ResourceManager.GetString("UploadImagesDragDrop_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string UserPreferences_Edit {
            get {
                return ResourceManager.GetString("UserPreferences_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter your new and old passwords and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string UserProfile_ChangePassword {
            get {
                return ResourceManager.GetString("UserProfile_ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the details and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string UserProfile_Edit {
            get {
                return ResourceManager.GetString("UserProfile_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to reset the password? It will be set to the username..
        /// </summary>
        internal static string UserProfile_ResetPassword {
            get {
                return ResourceManager.GetString("UserProfile_ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT SSRS Report Access.
        /// </summary>
        internal static string UserProfile_SSRS {
            get {
                return ResourceManager.GetString("UserProfile_SSRS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new User and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string User_Add {
            get {
                return ResourceManager.GetString("User_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the User and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string User_Edit {
            get {
                return ResourceManager.GetString("User_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Import.
        /// </summary>
        internal static string UtilityBOMManager_Import {
            get {
                return ResourceManager.GetString("UtilityBOMManager_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility BOM Import.
        /// </summary>
        internal static string UtilityBOM_Import {
            get {
                return ResourceManager.GetString("UtilityBOM_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Log.
        /// </summary>
        internal static string UtilityLog {
            get {
                return ResourceManager.GetString("UtilityLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string UtilityOffer_Import {
            get {
                return ResourceManager.GetString("UtilityOffer_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Price Quote Import.
        /// </summary>
        internal static string UtilityPriceQuote_Import {
            get {
                return ResourceManager.GetString("UtilityPriceQuote_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Stock Import.
        /// </summary>
        internal static string UtilityStock_Import {
            get {
                return ResourceManager.GetString("UtilityStock_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the new Warehouse and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Warehouse_Add {
            get {
                return ResourceManager.GetString("Warehouse_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to clear the default Warehouse?.
        /// </summary>
        internal static string Warehouse_ClearDefault {
            get {
                return ResourceManager.GetString("Warehouse_ClearDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter the changed details for the Warehouse and press &lt;b&gt;Save&lt;/b&gt;.
        /// </summary>
        internal static string Warehouse_Edit {
            get {
                return ResourceManager.GetString("Warehouse_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to set this Warehouse as default for new POs and CRMAs?.
        /// </summary>
        internal static string Warehouse_MakeDefault {
            get {
                return ResourceManager.GetString("Warehouse_MakeDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter all the details of the Warning Message and press Save.
        /// </summary>
        internal static string WarningMessage_Add {
            get {
                return ResourceManager.GetString("WarningMessage_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit the text of the Warning Message and press Save.
        /// </summary>
        internal static string WarningMessage_Edit {
            get {
                return ResourceManager.GetString("WarningMessage_Edit", resourceCulture);
            }
        }
    }
}
