﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
	public partial class ClientInvoiceHeader : BizObject
	{
		#region Properties

		protected static DAL.DivisionElement Settings
		{
			get { return Globals.Settings.Divisions; }
		}

		/// <summary>
		/// ClientInvoiceHeaderId
		/// </summary>
		public System.Int32 ClientInvoiceHeaderId { get; set; }


		/// <summary>
		/// ClientInvoiceHeaderName
		/// </summary>
		public System.String ClientInvoiceHeaderName { get; set; }

		/// <summary>
		/// ClientNo
		/// </summary>
		public System.Int32 ClientNo { get; set; }

		/// <summary>
		/// ClientName
		/// </summary>
		public System.String ClientName { get; set; }

		/// <summary>
		/// Notes
		/// </summary>
		public System.String Notes { get; set; }

		/// <summary>
		/// DocumentHeaderImageName
		/// </summary>
		public System.String DocumentHeaderImageName { get; set; }

		/// <summary>
		/// HasDocumentHeaderImage
		/// </summary>
		public System.Boolean HasDocumentHeaderImage { get; set; }

		public System.Boolean Inactive { get; set; }

		/// <summary>
		/// UpdatedBy
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP
		/// </summary>
		public System.DateTime DLUP { get; set; }


		#endregion

		/// <summary>
		/// GetListForClient
		/// Calls [usp_selectAll_Division_for_Client]
		/// </summary>
		public static List<ClientInvoiceHeader> GetListForClient(System.Int32? clientId)
		{
			List<ClientInvoiceHeaderDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Division.GetListForClientClientInvoiceHeader(clientId);
			if (lstDetails == null)
			{
				return new List<ClientInvoiceHeader>();
			}
			else
			{
				List<ClientInvoiceHeader> lst = new List<ClientInvoiceHeader>();
				foreach (ClientInvoiceHeaderDetails objDetails in lstDetails)
				{
					Rebound.GlobalTrader.BLL.ClientInvoiceHeader obj = new Rebound.GlobalTrader.BLL.ClientInvoiceHeader();
					obj.ClientInvoiceHeaderId = objDetails.ClientInvoiceHeaderId;
					obj.ClientNo = objDetails.ClientNo;
					obj.ClientInvoiceHeaderName = objDetails.ClientInvoiceHeaderName;
					//obj.Manager = objDetails.Manager;
					//obj.ManagerName = objDetails.ManagerName;
					//obj.Budget = objDetails.Budget;
					//obj.Telephone = objDetails.Telephone;
					//obj.Fax = objDetails.Fax;
					//obj.EMail = objDetails.EMail;
					obj.Notes = objDetails.Notes;
					//obj.URL = objDetails.URL;
					obj.Inactive = objDetails.Inactive;
					obj.UpdatedBy = objDetails.UpdatedBy;
					obj.DLUP = objDetails.DLUP;
					//obj.NumberOfMembers = objDetails.NumberOfMembers;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// Get
		/// Calls [usp_select_Division]
		/// </summary>
		public static ClientInvoiceHeader Get(System.Int32? ClientInvoiceHeaderId)
		{
			Rebound.GlobalTrader.DAL.ClientInvoiceHeaderDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Division.GetClientInvoiceHeader(ClientInvoiceHeaderId);
			if (objDetails == null)
			{
				return null;
			}
			else
			{
				ClientInvoiceHeader obj = new ClientInvoiceHeader();
				obj.ClientInvoiceHeaderId = objDetails.ClientInvoiceHeaderId;
				obj.ClientNo = objDetails.ClientNo;
				obj.ClientInvoiceHeaderName = objDetails.ClientInvoiceHeaderName;
				obj.HasDocumentHeaderImage = objDetails.HasDocumentHeaderImage;
				obj.Notes = objDetails.Notes;
				obj.Inactive = objDetails.Inactive;
				obj.UpdatedBy = objDetails.UpdatedBy;
				obj.DLUP = objDetails.DLUP;
				objDetails = null;
				return obj;
			}
		}


		public bool Update()
		{
			return Rebound.GlobalTrader.DAL.SiteProvider.Division.UpdateClientInvoiceHeader(ClientInvoiceHeaderId, ClientNo, ClientInvoiceHeaderName, Notes,Inactive, UpdatedBy);
		}
	}
}
