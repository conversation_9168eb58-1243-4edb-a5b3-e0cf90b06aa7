﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-211911]		An.TranTan		21-Oct-2024		CREATE		Add more column required for dynamic mapping in price quote import tool
===========================================================================================
*/
IF COL_LENGTH('BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote', 'ClientNoColumn') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote ADD ClientNoColumn INT NULL
END
GO
IF COL_LENGTH('BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote', 'CreatedBy') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote ADD CreatedBy INT NULL
END
GO
IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbPriceQuoteToBeImported', 'LineNumber') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbPriceQuoteToBeImported ADD LineNumber INT NULL
END
GO
IF NOT EXISTS (SELECT 1 FROM dbo.tbUtilityType WHERE UtilityTypeName = 'Price Quote Import Tool')
BEGIN
	INSERT INTO dbo.tbUtilityType
	(
		UtilityTypeName,
		Inactive,
		DLUP
	) VALUES ('Price Quote Import Tool', 0, GETDATE())
END