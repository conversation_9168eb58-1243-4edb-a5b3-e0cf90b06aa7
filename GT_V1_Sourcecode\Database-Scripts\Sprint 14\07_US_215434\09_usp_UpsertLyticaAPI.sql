﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_UpsertLyticaAPI]    Script Date: 11/5/2024 2:10:10 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
=============================================================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208862]		Trung Pham			15-Jul-2024		CREATE			Refresh lytica data when create requirements/Upload BOM
[US-215434]		Phuc Hoang			05-Nov-2024		UPDATE			Lytica Price should apply fuzzy logic for displaying & Rebranding Lytica
=============================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_UpsertLyticaAPI]
(
@JSON VARCHAR(MAX),
@UpdatedBy INT
)    
AS    
BEGIN
	SELECT ROW_NUMBER() OVER (ORDER BY (SELECT 1)) AS RowNum, commodity, mpn, manufacturer, avgPrice, targetPrice, marketLeading,
	alternateParts, lifecycle, lifecycleStatus, overallRisk, mpnBreadth, mfrBreadth, dueDiligence, mpnConcentration , 'N' as flag 
	INTO #tempLyticaAPI
	FROM OPENJSON(@JSON)
	WITH (
	commodity VARCHAR(200) '$.commodity',
	mpn VARCHAR(200) '$.mpn',
	manufacturer VARCHAR(200) '$.manufacturer',
	avgPrice FLOAT '$.avgPrice',
	targetPrice FLOAT '$.targetPrice',
	marketLeading FLOAT '$.marketLeading',
	alternateParts NVARCHAR(max) '$.alternateParts' AS JSON,
	lifecycle VARCHAR(200) '$.lifecycle',
	lifecycleStatus VARCHAR(200) '$.lifecycleStatus',
	overallRisk VARCHAR(200) '$.overallRisk',
	mpnBreadth VARCHAR(200) '$.mpnBreadth',
	mfrBreadth VARCHAR(200) '$.mfrBreadth',
	dueDiligence VARCHAR(200) '$.dueDiligence',
	mpnConcentration VARCHAR(200) '$.mpnConcentration')

	DECLARE @rowNum INT

	--WHILE ((SELECT count(1) FROM #tempLyticaAPI WHERE flag ='N') > 0) 
	--BEGIN
		--SELECT top 1 @rowNum = rowNum FROM #tempLyticaAPI WHERE flag = 'N' 
		-- Refresh LyticaData
		UPDATE tbLyticaAPI
			SET Commodity = ISNULL(t.commodity,'N/A'),
				AveragePrice = t.avgPrice,
				TargetPrice = t.targetPrice,
				MarketLeading = t.marketLeading,
				LifeCycle = ISNULL(t.lifecycle,'N/A'),
				lifeCycleStatus = ISNULL(t.lifecycleStatus,'N/A'),
				OverAllRisk = ISNULL(t.overallRisk,'N/A'),
				PartBreadth = ISNULL(t.mpnBreadth,'N/A'),
				ManufacturerBreadth = ISNULL(t.mfrBreadth,'N/A'),
				DueDiligence = ISNULL(t.dueDiligence,'N/A'),
				PartConcentration = ISNULL(t.mpnConcentration,'N/A'),
				UpdatedBy = @UpdatedBy,
				DLUP = GETDATE(),
				InActive = 0
			FROM tbLyticaAPI l
			INNER JOIN #tempLyticaAPI t ON l.OriginalPartSearched = t.mpn AND l.Manufacturer = t.manufacturer;

		-- Insert ##tempAlternatePartListTemp
		SELECT ROW_NUMBER() OVER (ORDER BY alt.AlternateParts) AS RowId, alt.AlternateParts, 'N' flag, alt.mpn, alt.manufacturer
		INTO #tempAlternatePartListTemp 
		FROM #tempLyticaAPI alt
		LEFT JOIN tbLyticaAPI lytica ON lytica.OriginalPartSearched = alt.mpn AND lytica.Manufacturer = alt.manufacturer
		WHERE alt.AlternateParts <> '[]' AND lytica.LyticaAPIId IS NULL

		-- Insert to tbLyticaAPI
		INSERT INTO tbLyticaAPI
			SELECT DISTINCT ISNULL(tmp.commodity,'N/A'), ISNULL(tmp.mpn,'N/A'), ISNULL(tmp.manufacturer,'N/A'), ISNULL(tmp.avgPrice,0), ISNULL(tmp.targetPrice,0), ISNULL(tmp.marketLeading,0), 
				ISNULL(tmp.lifecycle,'N/A'), ISNULL(tmp.lifecycleStatus,'N/A'), ISNULL(tmp.overallRisk,'N/A'), ISNULL(tmp.mpnBreadth,'N/A'),
				ISNULL(tmp.mfrBreadth,'N/A'), ISNULL(tmp.dueDiligence,'N/A'), ISNULL(tmp.mpnConcentration,'N/A'), @UpdatedBy, GETDATE(), GETDATE(), 0
				FROM #tempLyticaAPI tmp
				LEFT JOIN tbLyticaAPI lyt ON (tmp.mpn = lyt.OriginalPartSearched AND tmp.manufacturer = lyt.Manufacturer)
				WHERE lyt.LyticaAPIId IS NULL;
		
		-- Insert #tempAlternatePartList
		SELECT ROW_NUMBER() OVER (ORDER BY alt.AlternateParts) AS RowId, alt.AlternateParts, 'N' flag, lytica.LyticaAPIId
		INTO #tempAlternatePartList 
		FROM #tempAlternatePartListTemp alt
		INNER JOIN tbLyticaAPI lytica ON lytica.OriginalPartSearched = alt.mpn AND lytica.Manufacturer = alt.manufacturer
		WHERE alt.AlternateParts <> '[]'

		DECLARE @rowidAlt INT, @LyticaAPIId INT;

		WHILE ((SELECT COUNT(1) FROM #tempAlternatePartList WHERE flag ='N') > 0)
		BEGIN
			DECLARE @json2 VARCHAR(MAX)
			SELECT TOP 1 @rowidAlt = rowid, @json2 = AlternateParts, @LyticaAPIId = LyticaAPIId FROM #tempAlternatePartList WHERE flag = 'N'
			SELECT ROW_NUMBER() OVER (ORDER BY [key]) AS RowId, *, 'N' flag INTO #tempAlternatePart FROM OPENJSON(@json2)
			UPDATE #tempAlternatePart SET flag = 'N'

			DECLARE @rowid INT
			DECLARE @json3 VARCHAR(MAX)
			WHILE ((SELECT COUNT(1) FROM #tempAlternatePart WHERE flag ='N') > 0)
			BEGIN
				SELECT TOP 1 @rowid = rowid, @json3 = [value] FROM #tempAlternatePart WHERE flag = 'N'
				INSERT INTO tbLyticaAlternatePart
				SELECT @LyticaAPIId, mpn, manufacturer, lifecycleStatus, @UpdatedBy, getdate(), getdate(), 0 FROM OPENJSON(@json3)
				WITH (mpn VARCHAR(200) '$.mpn', manufacturer VARCHAR(200) '$.manufacturer', lifecycleStatus VARCHAR(200) '$.lifecycleStatus')

				UPDATE #tempAlternatePart SET flag = 'Y' WHERE RowId = @rowid
			END

			DROP TABLE #tempAlternatePart
			UPDATE #tempAlternatePartList SET flag = 'Y' WHERE RowId = @rowidAlt
		END

		DROP TABLE #tempLyticaAPI, #tempAlternatePartListTemp, #tempAlternatePartList
		--UPDATE #tempLyticaAPI SET flag='Y' WHERE RowNum = @rowNum
	--END
	
END
GO

