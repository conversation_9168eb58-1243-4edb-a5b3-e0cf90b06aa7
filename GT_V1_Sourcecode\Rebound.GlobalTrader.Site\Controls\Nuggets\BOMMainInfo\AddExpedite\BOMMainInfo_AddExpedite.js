Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_AddExpedite=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_AddExpedite.initializeBase(this,[n]);this._intBOMID=-1;this._intBOMNo=-1;this._intRequestedby=-1;this._intUpdateByPH=-1;this._HUBRFQName=null;this._HubrfqCode=null;this._CompanyNo=-1;this._intContact2No=-1;this._companyname=null;this._contactname=null;this._reqSalespeson=""};Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_AddExpedite.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_AddExpedite.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked))},dispose:function(){this.isDisposed||(this._reqSalespeson=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_AddExpedite.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("SaveExpedite");n.addParameter("id",this._intBOMID);n.addParameter("AddNotes",this.getFieldValue("ctlExpediteNotes"));n.addParameter("HUBRFQName",this._HUBRFQName);n.addParameter("intRequestedby",this._intRequestedby);n.addParameter("intUpdateByPH",this._intUpdateByPH);n.addParameter("HubrfqCode",this._HubrfqCode);n.addParameter("CompanyNo",this._CompanyNo);n.addParameter("Contact2No",this._intContact2No);n.addParameter("CompanyName",this._companyname);n.addParameter("ContactName",this._contactname);n.addParameter("ReqsalesPerson",this._reqSalespeson);n.addDataOK(Function.createDelegate(this,this.saveAddComplete));n.addError(Function.createDelegate(this,this.saveAddError));n.addTimeout(Function.createDelegate(this,this.saveAddError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveAddError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveAddComplete:function(n){n._result.Result>0?this.onSaveComplete():(n._result.Message&&(this._strErrorMessage=n._result.Message),this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()}};Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_AddExpedite.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_AddExpedite",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);