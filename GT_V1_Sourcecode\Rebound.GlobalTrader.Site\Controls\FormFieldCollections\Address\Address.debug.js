///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.initializeBase(this, [element]);
	this._intAddressID = -1;
	this._blnAllowNonEntry = false;
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.prototype = {

	get_blnAllowNonEntry: function() { return this._blnAllowNonEntry; }, 	set_blnAllowNonEntry: function(v) { if (this._blnAllowNonEntry !== v)  this._blnAllowNonEntry = v; }, 
	get_blnRequireFields: function() { return this._blnRequireFields; }, 	set_blnRequireFields: function(v) { if (this._blnRequireFields !== v)  this._blnRequireFields = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intAddressID = null;
		this._blnAllowNonEntry = null;
		this._blnRequireFields = null;
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.callBaseMethod(this, "dispose");
	},
	
	validateFields: function() {
		var blnOK = true;
		this.resetFields();
		if (this._blnRequireFields) {
			if (this.addressHasBeenEntered() || !this._blnAllowNonEntry) {
				if (!this.checkEnteredAddressField("ctlAddressName")) blnOK = false;
				if (!this.checkEnteredAddressField("ctlLine1")) blnOK = false;
				if (!this.checkEnteredAddressField("ctlTown")) blnOK = false;
				if (!this.checkEnteredAddressField("ctlCountry")) blnOK = false;
			}
		}
		return blnOK;
	},
	
	checkEnteredAddressField: function(strField) {
		var blnOK = true;
		if (!this._ctlRelatedForm.checkFieldEntered(strField)) {
			this._ctlRelatedForm.setFieldInError(strField, true, $R_RES.RequiredFieldMissingMessage);
			blnOK = false; 
		}
		return blnOK;
	},
	
	resetFields: function() {
		this._ctlRelatedForm.resetFieldError("ctlAddressName");
		this._ctlRelatedForm.resetFieldError("ctlLine1");
		this._ctlRelatedForm.resetFieldError("ctlLine2");
		this._ctlRelatedForm.resetFieldError("ctlLine3");
		this._ctlRelatedForm.resetFieldError("ctlTown");
		this._ctlRelatedForm.resetFieldError("ctlCounty");
		this._ctlRelatedForm.resetFieldError("ctlState");
		this._ctlRelatedForm.resetFieldError("ctlCountry");
		this._ctlRelatedForm.resetFieldError("ctlPostcode");
	},
	
	setFieldsToDefaults: function() {
		this._ctlRelatedForm.setFieldToDefault("ctlAddressName");
		this._ctlRelatedForm.setFieldToDefault("ctlLine1");
		this._ctlRelatedForm.setFieldToDefault("ctlLine2");
		this._ctlRelatedForm.setFieldToDefault("ctlLine3");
		this._ctlRelatedForm.setFieldToDefault("ctlTown");
		this._ctlRelatedForm.setFieldToDefault("ctlCounty");
		this._ctlRelatedForm.setFieldToDefault("ctlState");
		this._ctlRelatedForm.setFieldToDefault("ctlCountry");
		this._ctlRelatedForm.setFieldToDefault("ctlPostcode");
	},
	
	addressHasBeenEntered: function() {
		var bln = false;
		if (this._ctlRelatedForm.getFieldValue("ctlAddressName").length > 0) bln = true;
		if (this._ctlRelatedForm.getFieldValue("ctlLine1").length > 0) bln = true;
		if (this._ctlRelatedForm.getFieldValue("ctlLine2").length > 0) bln = true;
		if (this._ctlRelatedForm.getFieldValue("ctlLine3").length > 0) bln = true;
		if (this._ctlRelatedForm.getFieldValue("ctlTown").length > 0) bln = true;
		if (this._ctlRelatedForm.getFieldValue("ctlCounty").length > 0) bln = true;
		if (this._ctlRelatedForm.getFieldValue("ctlState").length > 0) bln = true;
		if (!this._ctlRelatedForm.getFieldControl("ctlCountry").isSetAsNoValue()) bln = true;
		if (this._ctlRelatedForm.getFieldValue("ctlPostcode").length > 0) bln = true;
		return bln;
	}
	
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);
