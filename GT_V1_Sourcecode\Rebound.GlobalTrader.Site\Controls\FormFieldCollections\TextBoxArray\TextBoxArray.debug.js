///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 19.07.2010:
// - Add getValue, showFieldError, clearFieldError functions
//
// RP 16.06.2010:
// - Add updateValues and updateValue functions
//
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.initializeBase(this, [element]);
	this._aryComponents = [];
	this._aryExtraData = [];
	this._blnAllowAddAndDelete = true;
	this._blnAllowRecordSerialNo = true;
	this._intNextIndex = 0;
	this._intValidateMinValue = null;
	this._blnValidateGreaterThanOrEqualTo = false;
	this._intValidateMaxValue = null;
	this._blnValidateLessThanOrEqualTo = false;	
	this._frmSOShip = null;
	this._intLineID = -1;
	this._intAllocationID = -1;
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.prototype = {

	get_tbl: function() { return this._tbl; }, 	set_tbl: function(v) { if (this._tbl !== v)  this._tbl = v; }, 
	get_intTextBoxWidth: function() { return this._intTextBoxWidth; }, 	set_intTextBoxWidth: function(v) { if (this._intTextBoxWidth !== v)  this._intTextBoxWidth = v; }, 
	get_enmTextBoxMode: function() { return this._enmTextBoxMode; }, 	set_enmTextBoxMode: function(v) { if (this._enmTextBoxMode !== v)  this._enmTextBoxMode = v; }, 
	get_blnUppercaseOnly: function() { return this._blnUppercaseOnly; }, 	set_blnUppercaseOnly: function(v) { if (this._blnUppercaseOnly !== v)  this._blnUppercaseOnly = v; }, 
	get_blnFormatDecimalPlaces: function() { return this._blnFormatDecimalPlaces; }, 	set_blnFormatDecimalPlaces: function(v) { if (this._blnFormatDecimalPlaces !== v)  this._blnFormatDecimalPlaces = v; }, 
	get_intDecimalPlaces: function() { return this._intDecimalPlaces; }, 	set_intDecimalPlaces: function(v) { if (this._intDecimalPlaces !== v)  this._intDecimalPlaces = v; }, 
	get_aryExtraData: function() { return this._aryExtraData; }, 	set_aryExtraData: function(value) { if (this._aryExtraData !== value)  this._aryExtraData = value; }, 
	get_blnAllowAddAndDelete: function () { return this._blnAllowAddAndDelete; }, set_blnAllowAddAndDelete: function (v) { if (this._blnAllowAddAndDelete !== v) this._blnAllowAddAndDelete = v; },
	get_blnAllowRecordSerialNo: function () { return this._blnAllowRecordSerialNo; }, set_blnAllowRecordSerialNo: function (v) { if (this._blnAllowRecordSerialNo !== v) this._blnAllowRecordSerialNo = v; },
	get_blnRequiredField: function() { return this._blnRequiredField; }, 	set_blnRequiredField: function(v) { if (this._blnRequiredField !== v)  this._blnRequiredField = v; }, 
	get_blnValidatePasteChar: function () { return this._blnValidatePasteChar; }, set_blnValidatePasteChar: function (v) { if (this._blnValidatePasteChar !== v) this._blnValidatePasteChar = v; },

		initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._tbl = null;
		this._aryComponents = null;
		this._aryExtraData = null;
		this._enmTextBoxMode = null;
		this._blnUppercaseOnly = null;
		this._blnFormatDecimalPlaces = null;
		this._intDecimalPlaces = null;
		this._intTextBoxWidth = null;
		this._blnAllowAddAndDelete = null;
		this._blnAllowRecordSerialNo = null;
		this._intValidateMinValue = null;
		this._blnValidateGreaterThanOrEqualTo = null;
		this._intValidateMaxValue = null;
		this._blnValidateLessThanOrEqualTo = null;
		this._blnRequiredField = null;
		this._blnValidatePasteChar = null;
		this._intLineID = null;
		this._intAllocationID = null;

		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.callBaseMethod(this, "dispose");
	},
	
	show: function(bln) {
		$R_FN.showElement(this._element, bln);
	},

	addItem: function (intIndex, varID, strTitle, strValue, strTextBefore, strTextAfter, objExtraData) {
	    if (!intIndex) intIndex = this._intNextIndex;
	    if (!varID) varID = this._intNextIndex;
	    if (!strValue) strValue = "";
	    if (!strTextBefore) strTextBefore = "";
	    if (!strTextAfter) strTextAfter = "";
	    if (!objExtraData) objExtraData = null;	    
	    var tr = this._tbl.insertRow(-1);
	    tr.id = this.getControlID("tr", intIndex);
	    var td = [];
	    td[0] = document.createElement("td");
	    td[0].className = "title";
	    td[0].innerHTML = String.format("{0}{1}", $R_FN.setCleanTextValue(strTitle), (this._blnRequiredField) ? "<span class=\"requiredField\">*</span>" : "");
	    tr.appendChild(td[0]);
	    td[1] = document.createElement("td");
	    td[1].className = "item";
	    var strTextBoxID = this.getControlID("txt", intIndex);
	    var btnID = this.getControlID("btn", intIndex);
	    var strHTML = strTextBefore;
	    strHTML += String.format(" <input id=\"{0}\" type=\"text\" style=\"width: {1}px;\" value=\"{2}\" onfocus=\"$R_TXTBOX.initTextBox(this, {3}, false, {4}, false, {5}, {6},{7});\" />", strTextBoxID, (this._intTextBoxWidth == 0) ? "200" : this._intTextBoxWidth, strValue, this._enmTextBoxMode, this._blnUppercaseOnly, this._blnFormatDecimalPlaces, this._intDecimalPlaces, this._blnValidatePasteChar);
	    strHTML += String.format("&nbsp; {0}", strTextAfter);
	   
	    if (this._blnAllowAddAndDelete) strHTML += String.format("<span id=\"{0}\"></span>", this.getControlID("spnButtons", intIndex));
	    strHTML += String.format("<div id=\"{0}\" class=\"invisible\"></div>", this.getControlID("divError", intIndex));
	    td[1].innerHTML = strHTML;
	    strHTML = null;
	    tr.appendChild(td[1]);
	    Array.add(this._aryComponents, { ID: varID, TextBoxID: strTextBoxID, RowID: tr.id, ButtonsID: this.getControlID("spnButtons", intIndex), ErrorID: this.getControlID("divError", intIndex) });
	    Array.add(this._aryExtraData, objExtraData);
	    tr = null; td = null;	    
	    if (this._blnAllowAddAndDelete) this.displayButtons();
	    this._intNextIndex += 1;	    
	},
	
	addSerialItem: function (intIndex, varID, strTitle, strValue, strTextBefore, strTextAfter, objExtraData, frmSOShip, GoodsInLineNo, blnReqSerialNo, allocationID) {
		if (!intIndex) intIndex = this._intNextIndex;
		if (!varID) varID = this._intNextIndex;
		if (!strValue) strValue = "";
		if (!strTextBefore) strTextBefore = "";
		if (!strTextAfter) strTextAfter = "";
		if (!objExtraData) objExtraData = null;
		this._intLineID = objExtraData;
		var tr = this._tbl.insertRow(-1);
		tr.id = this.getControlID("tr", intIndex);
		var td = [];
		td[0] = document.createElement("td");
		td[0].className = "title";
		td[0].innerHTML = String.format("{0}{1}", $R_FN.setCleanTextValue(strTitle), (this._blnRequiredField) ? "<span class=\"requiredField\">*</span>" : "");
		tr.appendChild(td[0]);
		td[1] = document.createElement("td");
		td[1].className = "item";
		var strTextBoxID = this.getControlID("txt", intIndex);
		var btnID = this.getControlID("btn", intIndex);
		var strHTML = strTextBefore;
		if (blnReqSerialNo) {
		    strHTML += String.format(" <input id=\"{0}\" type=\"text\" style=\"width: {1}px;\" value=\"{2}\" onfocus=\"$R_TXTBOX.initTextBox(this, {3}, false, {4}, false, {5}, {6},{7});\" onkeyup=\"$find('{8}').refereshSerial({9});\" />", strTextBoxID, (this._intTextBoxWidth == 0) ? "200" : this._intTextBoxWidth, strValue, this._enmTextBoxMode, this._blnUppercaseOnly, this._blnFormatDecimalPlaces, this._intDecimalPlaces, this._blnValidatePasteChar, this._element.id, varID);
		}
		else {
		    strHTML += String.format(" <input id=\"{0}\" type=\"text\" style=\"width: {1}px;\" value=\"{2}\" onfocus=\"$R_TXTBOX.initTextBox(this, {3}, false, {4}, false, {5}, {6},{7});\"  />", strTextBoxID, (this._intTextBoxWidth == 0) ? "200" : this._intTextBoxWidth, strValue, this._enmTextBoxMode, this._blnUppercaseOnly, this._blnFormatDecimalPlaces, this._intDecimalPlaces, this._blnValidatePasteChar);
		}
		strHTML += String.format("&nbsp; {0}", strTextAfter);
	    ///Suhail
		if (blnReqSerialNo) strHTML += String.format("<span id=\"{0}\"></span>", this.getControlID("spanButtons", intIndex));
		strHTML += String.format("<div id=\"{0}\" class=\"invisible\"></div>", this.getControlID("divError", intIndex));
        ///
		if (this._blnAllowAddAndDelete) strHTML += String.format("<span id=\"{0}\"></span>", this.getControlID("spnButtons", intIndex));
		strHTML += String.format("<div id=\"{0}\" class=\"invisible\"></div>", this.getControlID("divError", intIndex));
		td[1].innerHTML = strHTML;
		strHTML = null;
		tr.appendChild(td[1]);
		Array.add(this._aryComponents, { ID: varID, TextBoxID: strTextBoxID, RowID: tr.id, ButtonID: this.getControlID("spanButtons", intIndex), ButtonsID: this.getControlID("spnButtons", intIndex), ErrorID: this.getControlID("divError", intIndex), GoodsInLineNo: GoodsInLineNo, SOLineNo: objExtraData, PartNo: strTitle, QtyAllocated: strValue, AllocationNo: allocationID });
		Array.add(this._aryExtraData, objExtraData);
		tr = null; td = null;
		if (blnReqSerialNo) this.displaySerialButtons();
		if (this._blnAllowAddAndDelete) this.displayButtons();
		this._intNextIndex += 1;
		this._frmSOShip = frmSOShip;
		//this._intAllocationID = allocationID;
	},

   	
	removeItem: function(i) {
		var intIndex = this.findIndexFromID(i);
		var tr = $get(this._aryComponents[intIndex].RowID);
		if (tr) {
			tr.parentNode.removeChild(tr);
			Array.removeAt(this._aryComponents, intIndex);
			Array.removeAt(this._aryExtraData, intIndex);
			this.displayButtons();
		}
		tr = null;
	},

	recordSerailNo: function (i) {
	    var intIndex = this.findIndexFromID(i);
	    var tr = $get(this._aryComponents[intIndex].RowID);
	    if (tr) {	
	        var txt = $get(this._aryComponents[intIndex].TextBoxID);
	        this._frmSOShip.OpenSerialForm(this._aryComponents[intIndex].SOLineNo, this._aryComponents[intIndex].GoodsInLineNo, txt.value, this._aryComponents[intIndex].PartNo, this._aryComponents[intIndex].AllocationNo);
	    }
	    tr = null;
	},

	refereshSerial:function(index)
	{
	    //alert(index + "index");
	    this.recordSerailNo(index);
	},
	
	addHeading: function(intLevel, str) {
	    var tr = this._tbl.insertRow(-1);
	    var td = document.createElement("td");
	    td.colSpan = 2;
	    td.innerHTML = String.format("<h{0}{2}>{1}</h{0}>", intLevel, str, (this._tbl.rows.length == 1) ? " class=\"first\"" : "");
	    tr.appendChild(td);
	    tr = null; td = null;
	},
	
	displayButtons: function() {
		if (!this._blnAllowAddAndDelete) return;
		for (var i = 0, l = this._aryComponents.length; i < l; i++) {
			var strHTML = "";
			if (i > 0 && l > 1) strHTML += String.format("<a href=\"javascript:void(0);\" onclick=\"$find('{0}').removeItem({1});\" class=\"quickSearchReselect\">[x]</a>", this._element.id, this._aryComponents[i].ID);
			if (i == (l - 1)) strHTML += String.format("<a href=\"javascript:void(0);\" onclick=\"$find('{0}').addItem();\" class=\"quickSearchReselect\">[+]</a>", this._element.id);
			var spn = $get(this._aryComponents[i].ButtonsID);
			if (spn) spn.innerHTML = strHTML;
			spn = null; strHTML = null;
		}
	},

	displaySerialButtons: function () {	   
	    for (var i = 0, l = this._aryComponents.length; i < l; i++) {
	        var strHTML = "";
	        strHTML += String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$find('{0}').recordSerailNo({1},{2});\" style=\"color:yellow;font-size: 13px\">Select Serial No. to ship</a>", this._element.id, this._aryComponents[i].ID, this._aryComponents[i].GoodsInLineNo);
	        var spn = $get(this._aryComponents[i].ButtonID);
	        if (spn) spn.innerHTML = strHTML;
	        spn = null; strHTML = null;
	    }

	},
	findIndexFromID: function(intID) {
		var intIndex = -1;
		for (var i = 0, l = this._aryComponents.length; i < l; i++) {
			if (this._aryComponents[i].ID == intID) {
				intIndex = i;
				break;
			}
		}
		return intIndex;
	},
	
	getControlID: function(str, i) {
		return String.format("{0}_{1}{2}", this._element.id, str, i);
	},
	
	clearItems: function() {
		for (var i = this._tbl.rows.length - 1; i >= 0; i--) { this._tbl.deleteRow(i); }
		Array.clear(this._aryComponents);
		Array.clear(this._aryExtraData);
		this._intNextIndex = 0;
	},
	
	getIDs: function() {
		var ary = [];
		for (var i = 0, l = this._aryComponents.length; i < l; i++) {
			Array.add(ary, this._aryComponents[i].ID);
		}
		return ary;
	},
	
	getIDsAsString: function() {	
		return $R_FN.arrayToSingleString(this.getIDs());	
	},
	
	getValues: function() {
		var ary = [];
		for (var i = 0, l = this._aryComponents.length; i < l; i++) {
			var txt = $get(this._aryComponents[i].TextBoxID);
			Array.add(ary, txt.value.trim());
			txt = null;
		}
		return ary;
	},

	updateValues: function(aryValues) {
		for (var i = 0, l = this._aryComponents.length; i < l; i++) {
			var txt = $get(this._aryComponents[i].TextBoxID);
			if (txt) txt.value = aryValues[i];
			txt = null;
		}
	},

	updateValue: function(i, varValue) {
		var txt = $get(this._aryComponents[i].TextBoxID);
		if (txt) txt.value = varValue;
		txt = null;
	},
	
	getValuesAsString: function() {	
		return $R_FN.arrayToSingleString(this.getValues());	
	},
	
	getSelectedExtraData: function(intIndex) {
		return (this._aryExtraData[intIndex]);
	},
	
	getValue: function(i) {	
		var txt = $get(this._aryComponents[i].TextBoxID);
		if (txt) return txt.value;
		txt = null;
	},
	
	setMinAndMaxForNumericValidation: function(intMin, intMax, blnGreaterThanOrEqualTo, blnLessThanOrEqualTo) {
		if (typeof(intMin) == "undefined" || intMin == null) {
			this._intValidateMinValue = null;
		} else {
			this._intValidateMinValue = intMin;
		}
		if (typeof(intMax) == "undefined" || intMax == null) {
			this._intValidateMaxValue = null;
		} else {
			this._intValidateMaxValue = intMax;
		}
		this._blnValidateLessThanOrEqualTo = false;
		this._blnValidateGreaterThanOrEqualTo = false;
		if (typeof(blnLessThanOrEqualTo) != "undefined" && blnLessThanOrEqualTo != null) this._blnValidateLessThanOrEqualTo = blnLessThanOrEqualTo;
		if (typeof(blnGreaterThanOrEqualTo) != "undefined" && blnGreaterThanOrEqualTo != null) this._blnValidateGreaterThanOrEqualTo = blnGreaterThanOrEqualTo;
	},
	
	validateAllFields: function() {
		var bln = true;
		for (var i = 0, l = this._aryComponents.length; i < l; i++) {
			var txt = $get(this._aryComponents[i].TextBoxID);
			this.clearFieldError(i);
			if (this._enmTextBoxMode == $R_ENUM$TextBoxMode.Numeric || this._enmTextBoxMode == $R_ENUM$TextBoxMode.Currency) {
				if (!$R_FN.checkNumeric(txt)) {
					this.showFieldError(i, $R_RES.NumericFieldError);
					bln = false;
				} else if (this._blnRequiredField && !$R_FN.isEntered(txt.value)) {
					this.showFieldError(i, $R_RES.EnterFieldGeneric);
					bln = false;
				} else if (this._intValidateMinValue != null) {
					if (this._blnValidateGreaterThanOrEqualTo) {
						if (Number.parseLocale(txt.value.toString()) < this._intValidateMinValue) {
							this.showFieldError(i, String.format($R_RES.NumericFieldGreaterThanOrEqualToError, this._intValidateMinValue));
							bln = false;
						}
					} else {
						if (Number.parseLocale(txt.value.toString()) <= this._intValidateMinValue) {
							this.showFieldError(i, String.format($R_RES.NumericFieldGreaterThanError, this._intValidateMinValue));
							bln = false;
						}
					}
				} else if (this._intValidateMaxValue != null) {
					if (this._blnValidateLessThanOrEqualTo) {
						if (Number.parseLocale(txt.value.toString()) > this._intValidateMaxValue) {
							this.showFieldError(i, String.format($R_RES.NumericFieldLessThanOrEqualToError, this._intValidateMaxValue));
							bln = false;
						}
					} else {
						if (Number.parseLocale(txt.value.toString()) >= this._intValidateMaxValue) {
							this.showFieldError(i, String.format($R_RES.NumericFieldLessThanError, this._intValidateMaxValue));
							bln = false;
						}
					}
				}
			} else {
				if (this._blnRequiredField  && !$R_FN.isEntered(txt.value)) {
					this.showFieldError(i, $R_RES.EnterFieldGeneric);
					bln = false;
				}
			}
			txt = null;
		}
		return bln;
	},
	
	showFieldError: function(i, strErrorMessage) {
		var div = $get(this._aryComponents[i].ErrorID);
		var tr = $get(this._aryComponents[i].RowID);
		if (!div) return;
		if (!tr) return;
		div.innerHTML = strErrorMessage;
		tr.className = "formRowError";
		$R_FN.showElement(div, true);
		div = null;
		tr = null;
	}, 
	
	clearFieldError: function(i) {
	    var div = $get(this._aryComponents[i].ErrorID);
	    var tr = $get(this._aryComponents[i].RowID);
	    if (!div) return;
	    if (!tr) return;
	    div.innerHTML = "";
	    tr.className = "formRow";
	    $R_FN.showElement(div, false);
	    div = null;
	    tr = null;
	}

};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TextBoxArray", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);