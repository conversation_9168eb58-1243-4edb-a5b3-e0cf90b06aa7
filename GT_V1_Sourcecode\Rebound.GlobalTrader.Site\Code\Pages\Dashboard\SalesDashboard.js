Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Dashboard");Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard=function(n){Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.prototype={get_strSaleskUrl:function(){return this._strSaleskUrl},set_strSaleskUrl:function(n){this._strSaleskUrl!==n&&(this._strSaleskUrl=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.callBaseMethod(this,"initialize")},goInit:function(){document.getElementById("ifrmQlikSalesOrder").src=this._strSaleskUrl;Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._strSaleskUrl=null,Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.callBaseMethod(this,"dispose"))}};Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.registerClass("Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);