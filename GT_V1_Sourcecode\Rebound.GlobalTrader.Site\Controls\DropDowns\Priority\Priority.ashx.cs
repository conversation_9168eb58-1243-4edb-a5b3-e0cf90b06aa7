//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Priority : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("Priority");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            int? intPOHubClientNo;
            intPOHubClientNo = GetFormValue_NullableInt("POHubClientNo");
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");
            //string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            string strOptions = CacheManager.SerializeOptions(new object[] { intGlobalLoginClientNo.HasValue ? intGlobalLoginClientNo.Value : SessionManager.ClientID, (intPOHubClientNo.HasValue) ? intPOHubClientNo.Value : 0 });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strOptions);
           if (string.IsNullOrEmpty(strCachedData))
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnPrioritys = new JsonObject(true);
                foreach (BLL.Country priority in BLL.Country.DropDownForPriority())
                {
                    JsonObject jsnPriority = new JsonObject();
                    jsnPriority.AddVariable("PriorityId", priority.PriorityId);
                    jsnPriority.AddVariable("PriorityNo", priority.PriorityNo);
                    jsnPrioritys.AddVariable(jsnPriority);
                    jsnPriority.Dispose(); jsnPriority = null;
                }
                jsn.AddVariable("Priority", jsnPrioritys);
                jsnPrioritys.Dispose();
                jsnPrioritys = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
