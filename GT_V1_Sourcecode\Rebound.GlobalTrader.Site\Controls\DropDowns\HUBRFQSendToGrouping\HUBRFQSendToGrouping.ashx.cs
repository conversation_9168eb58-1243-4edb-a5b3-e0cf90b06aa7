using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class HUBRFQSendToGrouping : Rebound.GlobalTrader.Site.Data.DropDowns.Base
	{

		public override void ProcessRequest(HttpContext context)
		{
			SetDropDownType("HUBRFQSendToGrouping");
			base.ProcessRequest(context);
		}

		protected override void GetData()
		{
			JsonObject jsn = new JsonObject();
			JsonObject jsnList = new JsonObject(true);
			int? var = SessionManager.ClientID;
			if (var == 101)
            {
			jsnList.AddVariable(GetHUBRFQSendToGroupingJsonItem("Hub Sales"));
            }
            else
			{ 
			jsnList.AddVariable(GetHUBRFQSendToGroupingJsonItem("Hub Only"));
			jsnList.AddVariable(GetHUBRFQSendToGroupingJsonItem("Hub Sales"));
            }
		
			
			jsn.AddVariable("Types", jsnList);
			jsnList.Dispose();
			jsnList = null;
			OutputResult(jsn);
			jsn.Dispose();
			jsn = null;
		}

		private JsonObject GetHUBRFQSendToGroupingJsonItem(string strValue)
		{
			JsonObject jsnItem = new JsonObject();
			jsnItem.AddVariable("ID", strValue);
			jsnItem.AddVariable("Name", Functions.GetGlobalResource("HUBRFQSendToGrouping", strValue));
			return jsnItem;
		}
	}
}
