///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Buyer filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.prototype = {
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (v) { if (this._blnPOHub !== v) this._blnPOHub = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (v) { if (this._IsGSA !== v) this._IsGSA = v; },

    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/Debits";
        this._strDataObject = "Debits";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._IsGlobalLogin = null;
        this._IsGSA = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
    },

    getDataOK: function(args) {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData;
            if (this._IsGlobalLogin) {
                aryData = [
                    $RGT_nubButton_DebitNote(row.ID, row.No)
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                    , $R_FN.writeDoubleCellValue($R_FN.showSupplierMessage(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM)+ '</span>' :$RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.SuppMessage)), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    , $R_FN.setCleanTextValue(row.Date)
                    , $RGT_nubButton_PurchaseOrder(row.PONo, row.PurchaseOrder)
                    , $R_FN.setCleanTextValue(row.SupplierInvoice)
                    , $R_FN.setCleanTextValue(row.ClientName)
                ];
            }
            else {
                aryData = [
                    $RGT_nubButton_DebitNote(row.ID, row.No)
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                    , $R_FN.writeDoubleCellValue($R_FN.showSupplierMessage(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM)+ '</span>':$RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.SuppMessage)), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    , $R_FN.setCleanTextValue(row.Date)
                    , $RGT_nubButton_PurchaseOrder(row.PONo, row.PurchaseOrder)
                    , $R_FN.setCleanTextValue(row.SupplierInvoice)
                ];
            }
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function() {
        this.getFilterField("ctlBuyerName").show(this._enmViewLevel != 0);
         this.getFilterField("ctlPohubOnly").show(this._blnPOHub);
         this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);
        this.getFilterField("ctlClientName").show(this._IsGlobalLogin || this._IsGSA);
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
