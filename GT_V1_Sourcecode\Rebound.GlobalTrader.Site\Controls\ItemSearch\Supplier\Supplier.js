Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.prototype={get_blnForPOs:function(){return this._blnForPOs},set_blnForPOs:function(n){this._blnForPOs!==n&&(this._blnForPOs=n)},get_blnForSOs:function(){return this._blnForSOs},set_blnForSOs:function(n){this._blnForSOs!==n&&(this._blnForSOs=n)},get_blnShowPOFilter:function(){return this._blnShowPOFilter},set_blnShowPOFilter:function(n){this._blnShowPOFilter!==n&&(this._blnShowPOFilter=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete));$R_FN.showElement(this.getField("ctlPurchaseOrderNo")._element,this._blnShowPOFilter)},dispose:function(){this.isDisposed||(this._blnForPOs=null,this._blnForSOs=null,this._blnShowPOFilter=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/Supplier");this._objData.set_DataObject("Supplier");this._objData.set_DataAction("GetData");this._objData.addParameter("Name",this.getFieldValue("ctlCompanyName"));this._blnForPOs&&this._objData.addParameter("POApproved",!0);this._blnForSOs&&this._objData.addParameter("SOApproved",!0);this._objData.addParameter("PONoLo",this.getFieldValue_Min("ctlPurchaseOrderNo"));this._objData.addParameter("PONoHi",this.getFieldValue_Max("ctlPurchaseOrderNo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.setCleanTextValue(n.Name),$R_FN.setCleanTextValue(n.SupplierCode),$R_FN.setCleanTextValue(n.Type),$R_FN.setCleanTextValue(n.City),$R_FN.setCleanTextValue(n.Country),$R_FN.setCleanTextValue(n.Tel),$R_FN.setCleanTextValue(n.Salesperson),$R_FN.setCleanTextValue(n.LastContact)],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);