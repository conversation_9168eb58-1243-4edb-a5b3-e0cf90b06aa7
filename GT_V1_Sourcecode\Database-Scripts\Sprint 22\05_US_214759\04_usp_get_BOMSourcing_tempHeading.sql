﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-214760]		An.TranTan		09-Jan-2025		Create		Get BOM Sourcing Result raw data headers
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE [dbo].[usp_get_BOMSourcing_tempHeading] 
	@UserId INT
AS
BEGIN
	SELECT ColumnId, ColumnHeading
	FROM (
		SELECT TOP 1 
			[Column1]
			,[Column2]
			,[Column3]
			,[Column4]
			,[Column5]
			,[Column6]
			,[Column7]
			,[Column8]
			,[Column9]
			,[Column10]
			,[Column11]
			,[Column12]
			,[Column13]
			,[Column14]
			,[Column15]
			,[Column16]
			,[Column17]
			,[Column18]
			,[Column19]
			,[Column20]
			,[Column21]
			,[Column22]
			,[Column23]
			,[Column24]
			,[Column25]
			,[Column26]
			,[Column27]
			,[Column28]
			,[Column29]
			,[Column30]
			,[Column31]
		FROM BorisGlobalTraderimports.dbo.tbBOMSourcing_tempHeading
		WHERE CreatedBy = @UserId
		ORDER BY CreatedDate DESC
	) AS SourceTable
	UNPIVOT
	(
		ColumnHeading FOR ColumnId IN ([Column1]
										,[Column2]
										,[Column3]
										,[Column4]
										,[Column5]
										,[Column6]
										,[Column7]
										,[Column8]
										,[Column9]
										,[Column10]
										,[Column11]
										,[Column12]
										,[Column13]
										,[Column14]
										,[Column15]
										,[Column16]
										,[Column17]
										,[Column18]
										,[Column19]
										,[Column20]
										,[Column21]
										,[Column22]
										,[Column23]
										,[Column24]
										,[Column25]
										,[Column26]
										,[Column27]
										,[Column28]
										,[Column29]
										,[Column30]
										,[Column31]
										)
	) AS UnpivotTable;
END
GO
/* --Test script
	exec usp_get_BOMSourcing_tempHeading @UserId = 6670
*/


