/*
Marker     changed by      date         Remarks
[001]      Abhinav       17/11/20011   ESMS Ref:25 & 34  - Virtual Stock Update & Closeing of line CRMA
[002]      Vinay           07/05/2012   This need to upload pdf document for customerRMA section
*/
///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.prototype = {

    get_intCRMAID: function() { return this._intCRMAID; }, set_intCRMAID: function(v) { if (this._intCRMAID !== v) this._intCRMAID = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlLines: function() { return this._ctlLines; }, set_ctlLines: function(v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_btnPrint: function() { return this._btnPrint; }, set_btnPrint: function(v) { if (this._btnPrint !== v) this._btnPrint = v; },
    // [002] code start
    get_ctlCRMADocuments: function() { return this._ctlCRMADocuments; }, set_ctlCRMADocuments: function(v) { if (this._ctlCRMADocuments !== v) this._ctlCRMADocuments = v; },
    // [002] code end
    get_ctlCRMAPDFDragDrop: function() { return this._ctlCRMAPDFDragDrop; }, set_ctlCRMAPDFDragDrop: function(v) { if (this._ctlCRMAPDFDragDrop !== v) this._ctlCRMAPDFDragDrop = v; },
    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printCRMA));
        if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailCRMA));
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        if (this._ctlMainInfo) this.setLineFieldsFromHeader();
        // [002] code start
        if (this._ctlCRMADocuments) this._ctlCRMADocuments.getData();
        // [002] code end
        if (this._ctlCRMAPDFDragDrop) this._ctlCRMAPDFDragDrop.getData();
        if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        if (this._btnPrint) this._btnPrint.dispose();
        // [002] code start
        if (this._ctlCRMADocuments) this._ctlCRMADocuments.dispose();
        this._ctlCRMADocuments = null;
        // [002] code end
        this._btnPrint = null;
        this._ctlMainInfo = null;
        this._ctlLines = null;
        this._intCRMAID = null;
        Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.callBaseMethod(this, "dispose");
    },

    printCRMA: function() {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.CustomerRMA, this._intCRMAID);
    },

    emailCRMA: function() {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.CustomerRMA, this._intCRMAID, true);
    },
    printOtherDocs: function () {
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intCRMAID, false, "CustomerRMA");

    },

    ctlMainInfo_GetDataComplete: function() {
        this.setLineFieldsFromHeader();
    },

    setLineFieldsFromHeader: function() {
      //  alert('main');
        var strCustomer = this._ctlMainInfo.getFieldValue("hidCustomer");
        var strCRMANumber = this._ctlMainInfo.getFieldValue("hidNo");
        if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromHeader(strCRMANumber, strCustomer);
        if (this._ctlLines._frmEdit) this._ctlLines._frmEdit.setFieldsFromHeader(strCRMANumber, strCustomer);
        if (this._ctlLines._frmDelete) this._ctlLines._frmDelete.setFieldsFromHeader(strCRMANumber, strCustomer);
        //[001] code start
        if (this._ctlLines._frmClose) this._ctlLines._frmClose.setFieldsFromHeader(strCRMANumber, strCustomer);
        //[001] code end 
        if (this._ctlLines._frmDeallocate) this._ctlLines._frmDeallocate.setFieldsFromHeader(strCRMANumber, strCustomer);
        strCustomer = null;
        strCRMANumber = null;
    }

};
Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.CRMADetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
