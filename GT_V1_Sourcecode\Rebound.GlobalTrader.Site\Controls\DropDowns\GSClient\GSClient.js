Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.GSClient=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.GSClient.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.GSClient.prototype={initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.GSClient.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.GSClient.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/GSClient");this._objData.set_DataObject("GSClient");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Clients)for(n=0;n<t.Clients.length;n++)this.addOption(t.Clients[n].Name,t.Clients[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.GSClient.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GSClient",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);