﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for different section
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");
Rebound.GlobalTrader.Site.Controls.Forms.EndUserUndertakingPDF_DeleteDragDrop = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.EndUserUndertakingPDF_DeleteDragDrop.initializeBase(this, [element]);
    this._intPDFDocumentID = -1;
    this._ctlConfirm = null;
    this._pdfFileName = "";
    this._strSectionName = "";
};
Rebound.GlobalTrader.Site.Controls.Forms.EndUserUndertakingPDF_DeleteDragDrop.prototype = {

    get_intPDFDocumentID: function() { return this._intPDFDocumentID; }, set_intPDFDocumentID: function(value) { if (this._intPDFDocumentID !== value) this._intPDFDocumentID = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.EndUserUndertakingPDF_DeleteDragDrop.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intPDFDocumentID = null;
        this._pdfFileName = null;
        Rebound.GlobalTrader.Site.Controls.Forms.EndUserUndertakingPDF_DeleteDragDrop.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function() {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/EndUserUndertakingPDFDragDrop");
        obj.set_DataObject("EndUserUndertakingPDFDragDrop");
        obj.set_DataAction("Delete");
        obj.addParameter("id", this._intPDFDocumentID);
        obj.addParameter("pdffilename", this._pdfFileName);
        obj.addParameter("Section", this._strSectionName);
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },

    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.EndUserUndertakingPDF_DeleteDragDrop.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EndUserUndertakingPDF_DeleteDragDrop", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
