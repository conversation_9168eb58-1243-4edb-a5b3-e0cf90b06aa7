﻿using System;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.BLL
{
    public class HUBOfferImportLargeFileTempList
    {
        public List<HUBOfferImportLargeFileTemp> lstHUBOfferImportLargeFileTemp { get; set; }
        public int TotalRecords { get; set; }
        public int TotalRecordsError { get; set; }
        public int CurrentPage { get; set; }
    }

    public partial class HUBOfferImportLargeFileTemp : BizObject
    {
        public int OfferTempId { get; set; }

        public string MPN { get; set; }
        public string MFR { get; set; }
        public string COST { get; set; }
        public string LeadTime { get; set; }
        public string SPQ { get; set; }
        public string MOQ { get; set; }
        public string Remarks { get; set; }
        public string OfferedDate { get; set; }
        public string Vendor { get; set; }
        public int? ClientNo { get; set; }
        public DateTime DLUP { get; set; }
        public string GeneratedFileName { get; set; }
        public int HUBOfferImportLargeFileID { get; set; }
        public string GTMFR { get; set; }
        public string GTVendor { get; set; }
        public string MPNMessage { get; set; }
        public string MFRMessage { get; set; }
        public string COSTMessage { get; set; }
        public string LeadTimeMessage { get; set; }
        public string SPQMessage { get; set; }
        public string MOQMessage { get; set; }
        public string RemarksMessage { get; set; }
        public string OfferedDateMessage { get; set; }
        public string VendorMessage { get; set; }
    }
}
