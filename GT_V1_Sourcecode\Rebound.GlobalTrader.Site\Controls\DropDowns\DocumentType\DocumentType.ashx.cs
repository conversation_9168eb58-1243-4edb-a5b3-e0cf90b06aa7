//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//
// RP 12.10.2009:
// - retrofitted changes from v3.0.34 to get Name from resource file
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class DocumentType : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("DocumentType");
            base.ProcessRequest(context);
        }
        
        protected override void GetData()
        {
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData))
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Select"));
                jsnList.AddVariable(jsnItem);
                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "1");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "PDFDocument"));
                jsnList.AddVariable(jsnItem);
                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "2");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "SOImages"));
                jsnList.AddVariable(jsnItem);
                jsn.AddVariable("Types", jsnList);
                jsnItem.Dispose(); jsnItem = null;
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
        
    }


}
