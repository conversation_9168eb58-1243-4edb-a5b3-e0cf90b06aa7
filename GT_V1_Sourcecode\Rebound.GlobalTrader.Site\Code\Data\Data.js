Type.registerNamespace("Rebound.GlobalTrader.Site");Rebound.GlobalTrader.Site.DataQueueManager=function(){this._aryDataCalls=[];this._aryLowPriorityDataCalls=[];this._intAllowedSimultaneousCalls=3;this._intCurrentlyActiveCalls=0;this._intTimeBetweenCalls=25;this._intTimeToWaitForLowPriorityCalls=25;this._intTimeoutID=null};Rebound.GlobalTrader.Site.DataQueueManager.prototype={get_aryDataCalls:function(){return this._aryDataCalls},set_aryDataCalls:function(n){this._aryDataCalls!==n&&(this._aryDataCalls=n)},get_intAllowedSimultaneousCalls:function(){return this._intAllowedSimultaneousCalls},set_intAllowedSimultaneousCalls:function(n){this._intAllowedSimultaneousCalls!==n&&(this._intAllowedSimultaneousCalls=n)},get_intCurrentlyActiveCalls:function(){return this._intCurrentlyActiveCalls},set_intCurrentlyActiveCalls:function(n){this._intCurrentlyActiveCalls!==n&&(this._intCurrentlyActiveCalls=n)},initialize:function(){Rebound.GlobalTrader.Site.DataQueueManager.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._aryDataCalls&&this.clearAllDataCalls(),this._aryDataCalls=null,this._aryLowPriorityDataCalls=null,this._intAllowedSimultaneousCalls=null,this._intCurrentlyActiveCalls=null,this._intTimeBetweenCalls=null,this._intTimeToWaitForLowPriorityCalls=null,this._intTimeoutID=null,Rebound.GlobalTrader.Site.DataQueueManager.callBaseMethod(this,"dispose"),this.isDisposed=!0)},addToQueue:function(n,t){n.addDataOK(Function.createDelegate(this,this.callFinished));n.addTimeout(Function.createDelegate(this,this.callFinished));n.addError(Function.createDelegate(this,this.callFinished));n.addCancelled(Function.createDelegate(this,this.callFinished));n._intQueueIndex=this._aryLowPriorityDataCalls.length+this._aryDataCalls.length;t?Array.add(this._aryLowPriorityDataCalls,n):Array.add(this._aryDataCalls,n)},processQueue:function(){this._aryLowPriorityDataCalls.length>0&&this._aryDataCalls.length==0?this.waitBeforeProcessingQueue():this.doNextCall()},doNextCall:function(){var n;if((clearTimeout(this._intTimeoutID),this._aryDataCalls.length!=0||this._aryLowPriorityDataCalls.length!=0)&&!(this._intCurrentlyActiveCalls>=this._intAllowedSimultaneousCalls)){if(this._aryDataCalls.length>0){n=this._aryDataCalls[0];Array.removeAt(this._aryDataCalls,0);n.getData();this._intCurrentlyActiveCalls+=1;return}if(this._aryLowPriorityDataCalls.length>0){this._aryDataCalls.length==0&&(n=this._aryLowPriorityDataCalls[0],Array.removeAt(this._aryLowPriorityDataCalls,0),n.getData(),this._intCurrentlyActiveCalls+=1);return}}},callFinished:function(n){n&&(this._intCurrentlyActiveCalls-=1);(this._aryDataCalls.length>0||this._aryLowPriorityDataCalls.length>0)&&this.doNextCall()},waitBeforeProcessingQueue:function(){clearTimeout(this._intTimeoutID);this._intTimeoutID=setTimeout(Function.createDelegate(this,this.doNextCall),this._intTimeToWaitForLowPriorityCalls)},clearAllDataCalls:function(){var n,i,t;if(this._aryDataCalls){for(n=0,i=this._aryDataCalls.length;n<i;n++)t=this._aryDataCalls[n],t&&t.dispose(),t=null;Array.clear(this._aryDataCalls)}if(this._aryLowPriorityDataCalls){for(n=0,i=this._aryLowPriorityDataCalls.length;n<i;n++)t=this._aryLowPriorityDataCalls[n],t&&t.dispose(),t=null;Array.clear(this._aryLowPriorityDataCalls)}}};Rebound.GlobalTrader.Site.DataQueueManager.registerClass("Rebound.GlobalTrader.Site.DataQueueManager",Sys.Component,Sys.IDisposable);Rebound.GlobalTrader.Site.DataEventArgs=function(n,t,i,r){Rebound.GlobalTrader.Site.DataEventArgs.initializeBase(this);this._statusCode=n;this._errorMessage=t;this._result=i;this._url=r};Rebound.GlobalTrader.Site.DataEventArgs.prototype={get_ErrorMessage:function(){return this._errorMessage},set_ErrorMessage:function(n){this._errorMessage!==n&&(this._errorMessage=n)},get_StatusCode:function(){return this._statusCode},set_StatusCode:function(n){this._statusCode!==n&&(this._statusCode=n)},get_Result:function(){return this._result},set_Result:function(n){this._result!==n&&(this._result=n)},initialize:function(){Rebound.GlobalTrader.Site.Data.callBaseMethod(this,"initialize")}};Rebound.GlobalTrader.Site.DataEventArgs.registerClass("Rebound.GlobalTrader.Site.DataEventArgs",Sys.EventArgs);Rebound.GlobalTrader.Site.Data=function(){Rebound.GlobalTrader.Site.Data.initializeBase(this);this._dataObject=null;this._dataAction=null;this._dataParams=[];this._result=null;this._pathToData="";this._errorMessage="";this._statusCode=0;this._result=null;this._url="";this._blnReturnSerialized=!1;this._intTimeoutMilliseconds=-1;this._wReq};Rebound.GlobalTrader.Site.Data.prototype={get_DataObject:function(){return this._dataObject},set_DataObject:function(n){this._dataObject!==n&&(this._dataObject=n)},get_DataAction:function(){return this._dataAction},set_DataAction:function(n){this._dataAction!==n&&(this._dataAction=n)},get_DataParams:function(){return this._dataParams},set_DataParams:function(n){this._dataParams!==n&&(this._dataParams=n)},get_Result:function(){return this._result},set_Result:function(n){this._result!==n&&(this._result=n)},get_PathToData:function(){return this._pathToData},set_PathToData:function(n){this._pathToData!==n&&(this._pathToData=n)},get_StatusCode:function(){return this._statusCode},set_StatusCode:function(n){this._statusCode!==n&&(this._statusCode=n)},get_Result:function(){return this._result},set_Result:function(n){this._result!==n&&(this._result=n)},get_ErrorMessage:function(){return this._errorMessage},set_ErrorMessage:function(n){this._errorMessage!==n&&(this._errorMessage=n)},get_blnReturnSerialized:function(){return this._blnReturnSerialized},set_blnReturnSerialized:function(n){this._blnReturnSerialized!==n&&(this._blnReturnSerialized=n)},get_intTimeoutMilliseconds:function(){return this._intTimeoutMilliseconds},set_intTimeoutMilliseconds:function(n){this._intTimeoutMilliseconds!==n&&(this._intTimeoutMilliseconds=n)},addDataOK:function(n){this.get_events().addHandler("DataOK",n)},removeDataOK:function(n){this.get_events().removeHandler("DataOK",n)},onDataOK:function(){var n=this.get_events().getHandler("DataOK");n&&n(this,new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode,this._errorMessage,this._result,this._url))},addError:function(n){this.get_events().addHandler("Error",n)},removeError:function(n){this.get_events().removeHandler("Error",n)},onError:function(){var n=this.get_events().getHandler("Error");n&&n(this,new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode,this._errorMessage,this._result,this._url))},addTimeout:function(n){this.get_events().addHandler("Timeout",n)},removeTimeout:function(n){this.get_events().removeHandler("Timeout",n)},onTimeout:function(){var n=this.get_events().getHandler("Timeout");n||(n=this.get_events().getHandler("Error"));n&&n(this,new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode,this._errorMessage,this._result,this._url))},addCancelled:function(n){this.get_events().addHandler("Cancelled",n)},removeCancelled:function(n){this.get_events().removeHandler("Cancelled",n)},onCancelled:function(){var n=this.get_events().getHandler("Cancelled");n&&n(this,new Rebound.GlobalTrader.Site.DataEventArgs(this._statusCode,this._errorMessage,this._result,this._url))},initialize:function(){Rebound.GlobalTrader.Site.Data.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.cancel(),this._dataObject=null,this._dataAction=null,this._dataParams=null,this._result=null,this._pathToData=null,this._errorMessage=null,this._result=null,this._url=null,this._wReq=null,Rebound.GlobalTrader.Site.Data.callBaseMethod(this,"dispose"),this.isDisposed=!0)},addParameter:function(n,t){n&&t&&Array.add(this._dataParams,{Name:n,Value:$R_FN.getCleanTextValue(t)})},clearParameters:function(){Array.clear(this._dataParams)},getData:function(){var t,i,n;if(this._url=String.format("{0}/{1}.ashx",this._pathToData,this._dataObject),this._wReq||(this._wReq=new Sys.Net.WebRequest),this._wReq.set_url(this._url),this._wReq.set_httpVerb("POST"),this._intTimeoutMilliseconds==-1&&(this._intTimeoutMilliseconds=$R_GENERIC_DATABASE_TIMEOUT*1e3),this._wReq.set_timeout(this._intTimeoutMilliseconds),n="",n=String.format("action={0}",this._dataAction),this._dataParams!=null)for(i=0;i<this._dataParams.length;i++)t=this._dataParams[i],n+=String.format("&{0}={1}",t.Name,t.Value),t=null;n+=String.format("&{0}={1}","cLoginUser",$R_LOGGIN_USER);this._wReq.set_body(n);this._wReq.add_completed(Function.createDelegate(this,this.getDataComplete));this._wReq.invoke()},getDataComplete:function(n){if(n.get_timedOut()){this._errorMessage=$R_RES.DatabaseTimeout;this.onTimeout();return}if(n.get_aborted()){this.onCancelled();return}if(n.get_responseAvailable()){if(n.get_statusCode()!=200){this._statusCode=n.get_statusCode();this._errorMessage=n.get_statusText();this._result=n.get_responseData();this.onError();return}if(this._result=n.get_responseData(),this._result.startsWith('{"Error":')||this._result.startsWith("{Error:"))this._result=Sys.Serialization.JavaScriptSerializer.deserialize(n.get_responseData()),this._errorMessage=this._result.Message,this._errorMessage=="LOGGED_OUT"?location.href=$R_URL_Logout:this.onError();else{if(!this._blnReturnSerialized)if(this._result.length==0)this._result=null;else try{this._result=Sys.Serialization.JavaScriptSerializer.deserialize(n.get_responseData())}catch(t){this._errorMessage="Javascript deserialization error:<br /><br />"+n.get_responseData();this._result={Error:!0};this.onError();return}this.onDataOK()}return}},cancel:function(){if(this._wReq){this._wReq.remove_completed(Function.createDelegate(this,this.getDataComplete));var n=this._wReq.get_executor();n&&(n.abort(),n=null)}},reset:function(){this.cancel();this._events=new Sys.EventHandlerList;Array.clear(this._dataParams)}};Rebound.GlobalTrader.Site.Data.registerClass("Rebound.GlobalTrader.Site.Data",Sys.Component,Sys.IDisposable);