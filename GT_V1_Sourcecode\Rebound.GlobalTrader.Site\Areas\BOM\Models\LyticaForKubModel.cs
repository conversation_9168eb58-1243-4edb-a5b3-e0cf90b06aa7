﻿namespace Rebound.GlobalTrader.Site.Areas.BOM.Models
{
    public class LyticaForKubModel
    {
        public string AveragePrice { get; set; } = "0.00";
        public string MarketLeading { get; set; } = "0.00";
        public string TargetPrice { get; set; } = "0.00";
        public string Status { get; set; } = "N/A";
    }

    public class IHSForKubModel
    {
        public string AveragePrice { get; set; } = "0.00";
        public string MarketLeading { get; set; } = "0.00";
        public string TargetPrice { get; set; } = "0.00";
        public string Status { get; set; } = "N/A";
    }
}