<%@ Control Language="C#" CodeBehind="GILines_LotNo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILines_LotNo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="CompleteSave" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Close" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "AddEditLotNo")%></Explanation>
	
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			        
            <ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="lblQuantity" ResourceTitle="Quantity">
				<Field>
                <asp:Label ID="lblQuantity" runat="server" />
                    </Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlSubGroup" runat="server" FieldID="txtSubGroup" ResourceTitle="SubGroup" IsRequiredField="true">
            <Field>
            <ReboundUI:ReboundTextBox ID="txtSubGroup" runat="server" Width="200"/>
            <ReboundAutoSearch:SubGroupSearch ID="SubGroup" runat="server" RelatedTextBoxID="txtSubGroup" ResultsHeight="150" Width="200" ResultsActionType="RaiseEvent" TriggerByButton="true" />
            </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlLotNo" runat="server" FieldID="txtLotNo" ResourceTitle="LotNo" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtLotNo" runat="server" Width="200" Maxlength="50"    />
          
				</Field>
			</ReboundUI_Form:FormField>          			
            <ReboundUI_Form:FormField id="ctlAddUpdate" runat="server" FieldID="btnAdd" >
				<Field>
                 <ReboundUI:IconButton ID="btnAdd" runat="server" IconGroup="Nugget" IconButtonMode="HyperLink" IconCSSType="Add" ForeColor="#ffff99" BackColor="White" CssClass="btnAddReset" IconTitleResource="Add" />
                <ReboundUI:IconButton ID="btnUpdate" runat="server" IconGroup="Nugget" IconButtonMode="HyperLink" IconCSSType="Edit" ForeColor="#ffff99" BackColor="White" CssClass="btnAddReset" IconTitleResource="Update" />
                <ReboundUI:IconButton ID="btnRefresh" runat="server" IconButtonMode="HyperLink" IconCSSType="AddUpdate" ForeColor="#ffff99" BackColor="White" CssClass="btnAddReset" IconTitleResource="Cancelupdate" />
                <asp:Label  ID="lblDuplicateError" Text="Duplicate Lot Numbers not allowed"  runat="server" CssClass="PartDetailsGridGoError" />
                    <br />
                    <br />
              
                   
                <asp:Label  ID="lblLotCount" runat="server" Font-Bold="true" color="White" /><br /><br />
                      <ReboundUI:IconButton ID="btnRefGrid" runat="server" IconButtonMode="HyperLink" IconCSSType="refreshButton" ForeColor="#ffff99" BackColor="Yellow" CssClass="btnAddReset" IconTitleResource="RefGrid" />
                     <asp:Panel ID="pnlLotNodetails" runat="server" CssClass="GridLotdetails" Width="85%">
                    <%--<Reboundui:FlexiDataTable id="tblLotNodetails"  runat="server"  PanelHeight="200"  />--%>
                            <ReboundItemSearch:GITempLotNumber id="ctlGiTempLotNo" runat="server"   />
                            
                        </asp:Panel>    
				</Field>
                   
			</ReboundUI_Form:FormField>
                <ReboundUI_Form:FormField id="ctlLotNoDetail" runat="server" >
				<Field>	
                                    
				</Field>
			</ReboundUI_Form:FormField>
        
		
    
			
		</ReboundUI_Table:Form>       
	</Content>
</ReboundUI_Form:DesignBase>






