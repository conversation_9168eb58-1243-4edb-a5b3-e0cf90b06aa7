﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL {
		public partial class ProductCategory : BizObject {
		
		#region Properties

            protected static DAL.ProductCategoryElement Settings
            {
                get { return Globals.Settings.ProductCategory; }
		}

		/// <summary>
            /// ProductCategoryId
		/// </summary>
        public System.Int32 ProductCategoryId { get; set; }		
		/// <summary>
		/// Name
		/// </summary>
		public System.String Name { get; set; }		

		#endregion
		
		#region Methods
		
		
		/// <summary>
		/// DropDown
		/// Calls [usp_dropdown_ProductCategory]
		/// </summary>
        public static List<ProductCategory> DropDown()
        {
            List<ProductCategoryDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.ProductCategory.DropDown();
			if (lstDetails == null) {
                return new List<ProductCategory>();
			} else {
                List<ProductCategory> lst = new List<ProductCategory>();
                foreach (ProductCategoryDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.ProductCategory obj = new Rebound.GlobalTrader.BLL.ProductCategory();
                    obj.ProductCategoryId = objDetails.ProductCategoryId;
					obj.Name = objDetails.CategoryName;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}
        /// <summary>
        /// usp_select_ProductCategory
        /// </summary>
        /// <returns></returns>
        //public static ProductCategory Get(System.Int32? ProductCategoryId)
        //{
        //    ProductCategoryDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.ProductCategory.Get(ProductCategoryId);
        //    if (objDetails == null)
        //    {
        //        return new ProductCategory();
        //    }
        //    else
        //    {
        //        ProductCategory obj = new ProductCategory();
        //        obj.ProductCategoryId = objDetails.ProductCategoryId;
        //        obj.Name = objDetails.Name;
        //        objDetails = null;
        //        return obj;
        //    }
        //}
		
		
		#endregion
		
	}
}