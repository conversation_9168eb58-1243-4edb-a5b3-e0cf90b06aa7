///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLinesSoc = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLinesSoc.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLinesSoc.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLinesSoc.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLinesSoc.callBaseMethod(this, "dispose");
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/InvoiceLinesSoc");
		this._objData.set_DataObject("InvoiceLinesSoc");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Part", this.getFieldValue("ctlPartNo"));
		this._objData.addParameter("Contact", this.getFieldValue("ctlContact"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("IncludePaid", this.getFieldValue("ctlIncludePaid"));
		this._objData.addParameter("Salesman", this.getFieldValue("ctlSalesman"));
		this._objData.addParameter("CustomerPO", this.getFieldValue("ctlCustomerPO"));
		this._objData.addParameter("InvoiceNoLo", this.getFieldValue_Min("ctlInvoiceNo"));
		this._objData.addParameter("InvoiceNoHi", this.getFieldValue_Max("ctlInvoiceNo"));
		this._objData.addParameter("SONoLo", this.getFieldValue_Min("ctlSalesOrderNo"));
		this._objData.addParameter("SONoHi", this.getFieldValue_Max("ctlSalesOrderNo"));
		this._objData.addParameter("DateInvoicedFrom", this.getFieldValue("ctlDateInvoicedFrom"));
		this._objData.addParameter("DateInvoicedTo", this.getFieldValue("ctlDateInvoicedTo"));
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
				$R_FN.writePartNo(row.Part, row.ROHS),
				$R_FN.setCleanTextValue(row.Date),
				row.Price,
				row.Quantity,
				$R_FN.setCleanTextValue(row.CustomerPO),
				$R_FN.setCleanTextValue(row.SalesOrderNo),
			    $R_FN.setCleanTextValue(row.Cost)
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLinesSoc.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLinesSoc", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
