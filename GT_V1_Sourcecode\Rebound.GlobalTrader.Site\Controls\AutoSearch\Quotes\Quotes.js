Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("Quotes")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.callBaseMethod(this,"dispose")},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i=$R_FN.setCleanTextValue(n.QuoteNumber),this.addResultItem(i,$R_FN.setCleanTextValue(n.QuoteNumber),n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);