﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*=======================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209544]     CuongDox		 17-Sep-2024		CREATE		Upload offers to offers table
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================  
*/

CREATE OR ALTER PROC [dbo].[usp_insert_tbOffer_fromLargeOffersTemp](
@generated_file_name VARCHAR(255),
@UtilityLogId INT
)
AS

BEGIN
	INSERT INTO BorisGlobalTraderImports.dbo.tbOffer              
	 (  
		ClientNo 
	 ,  SupplierNo             
	 ,  Part
	 ,  FullPart                        
	 ,  Quantity
	 ,  Price
	 ,  OriginalEntryDate 
	 ,  ManufacturerNo  
	 ,  ManufacturerName
	 ,  LeadTime
	 ,  SPQ
	 ,  SupplierName
	 ,  Notes
	 ,  DLUP
	 )  
	SELECT
	 temp.ClientNo AS ClientNo
	 ,ISNULL(cmp.CompanyId, 0) AS SupplierNo
	 ,RTRIM(LTRIM(temp.MPN)) AS Part
	 ,dbo.ufn_get_fullpart(temp.MPN) AS FullPart
	 ,ISNULL(TRY_CONVERT(INT, REPLACE(temp.MOQ, ',', '')), 0) AS Quantity
	 ,ROUND(ISNULL(TRY_CONVERT(FLOAT, REPLACE(temp.COST, ',', '')), 0), 5) AS Price
	 ,CASE 
        WHEN temp.OfferedDate IS NULL OR  temp.OfferedDate = '' THEN NULL 
        ELSE TRY_CONVERT(DATE,temp.OfferedDate,103) 
		END AS OriginalEntryDate
	 ,(select top 1 ManufacturerId FROM tbManufacturer WHERE ManufacturerName = temp.MFR AND Inactive = 0) AS ManufacturerNo
	 ,RTRIM(LTRIM(temp.MFR)) AS ManufacturerName
	 ,RTRIM(LTRIM(temp.LeadTime)) AS LeadTime
	 ,RTRIM(LTRIM(REPLACE(temp.SPQ, ',', ''))) AS SPQ
	 ,RTRIM(LTRIM(temp.Vendor)) AS SupplierName
	 ,CASE 
		WHEN (temp.MFR IS NULL OR temp.MFR = '') AND (temp.Remarks IS NOT NULL AND temp.Remarks <>  '')
		THEN '| MFR: Blank Import | ' + RTRIM(LTRIM(temp.Remarks))

		WHEN (temp.MFR IS NULL OR temp.MFR = '') AND (temp.Remarks IS NULL OR temp.Remarks =  '')
		THEN '| MFR: Blank Import'

		WHEN (temp.Remarks IS NULL OR temp.Remarks =  '')
			AND (temp.MFR IS NOT NULL AND temp.MFR <> '') 
			AND (SELECT TOP 1 ManufacturerId FROM tbManufacturer WHERE ManufacturerName = RTRIM(LTRIM(temp.MFR)) AND Inactive = 0) IS NULL
		THEN '| MFR: ' + RTRIM(LTRIM(temp.MFR))

		WHEN (temp.Remarks IS NOT NULL AND temp.Remarks <>  '')
			AND (temp.MFR IS NOT NULL AND temp.MFR <> '') 
			AND (SELECT TOP 1 ManufacturerId FROM tbManufacturer WHERE ManufacturerName = RTRIM(LTRIM(temp.MFR)) AND Inactive = 0) IS NULL
		THEN '| MFR: ' + RTRIM(LTRIM(temp.MFR)) + ' | ' + RTRIM(LTRIM(temp.Remarks))

		ELSE
			CASE 
				WHEN RTRIM(LTRIM(temp.Remarks)) = '' 
				THEN NULL
            ELSE '| ' + RTRIM(LTRIM(temp.Remarks))
        END
		END AS Notes
	 ,GETDATE()

	FROM BorisGlobalTraderImports.dbo.[tbOfferImportByExcelTemp] temp 
	JOIN dbo.tbCompany cmp ON (cmp.CompanyName = temp.Vendor AND cmp.ClientNo = 114 AND cmp.IsSupplier = 1)
	WHERE temp.GeneratedFileName = @generated_file_name
	AND temp.MPN is not Null 
	and temp.MPN <> ''
	--update utility log
	update BorisGlobalTraderImports.dbo.tbUtilityLog set iRowCount = @@ROWCOUNT
	where UtilityLogId = @UtilityLogId

END

GO


