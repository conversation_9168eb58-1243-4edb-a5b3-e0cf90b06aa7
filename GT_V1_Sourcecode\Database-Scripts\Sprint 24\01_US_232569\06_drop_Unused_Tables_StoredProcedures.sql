﻿/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-232569]		An.TranTan		24-Mar-2025		CREATE		Drop SPs, tables that no longer used
===========================================================================================
*/
IF OBJECT_ID('dbo.usp_saveBOMSourcing_tempHeading', 'P') IS NOT NULL
	DROP PROC dbo.usp_saveBOMSourcing_tempHeading
GO
IF OBJECT_ID('dbo.usp_get_BOMSourcing_tempHeading', 'P') IS NOT NULL
	DROP PROC dbo.usp_get_BOMSourcing_tempHeading
GO
IF OBJECT_ID('dbo.usp_saveBOMSourcing_tempData', 'P') IS NOT NULL
	DROP PROC dbo.usp_saveBOMSourcing_tempData
GO
IF OBJECT_ID('dbo.usp_get_BOMSourcing_rawData', 'P') IS NOT NULL
	DROP PROC dbo.usp_get_BOMSourcing_rawData
GO
IF OBJECT_ID('dbo.usp_select_BOMSourcing_ColumnMapping', 'P') IS NOT NULL
	DROP PROC dbo.usp_select_BOMSourcing_ColumnMapping
GO
IF OBJECT_ID('dbo.usp_save_BOMSourcing_ColumnMapping', 'P') IS NOT NULL
	DROP PROC dbo.usp_save_BOMSourcing_ColumnMapping
GO
IF OBJECT_ID('dbo.usp_validate_BOMSourcingResults', 'P') IS NOT NULL
	DROP PROC dbo.usp_validate_BOMSourcingResults
GO