/* Marker     changed by      date         Remarks
 [001]        <PERSON><PERSON><PERSON>     12-Dec-2018  implement bom import 
 [002]        Arpit           21-June-2022 Dictionary to ConcurrentDictionary
 */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Text;
using System.IO;
using System.Data.SqlClient;
using System.Configuration;
using System.Data.OleDb;
using System.Web.Script.Serialization;
using System.Text.RegularExpressions;
using Rebound.GlobalTrader.Site.Code.Common;
using OfficeOpenXml;
using System.Reflection;
//[001] add namespace for Azure Blob storage
using Microsoft.Azure;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using Microsoft.Azure.KeyVault;
using Microsoft.Azure.Storage.Shared;
using Microsoft.Azure.Storage.Auth;
using Microsoft.Azure.Storage.Auth.Protocol;
using Microsoft.Azure.Storage.RetryPolicies;
using Microsoft.Azure.KeyVault.Core;
using System.Net;
using System.Collections.Concurrent;
//[001]



namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BOMImport : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                if (string.IsNullOrEmpty(Action))
                {
                    Action = context.Request.QueryString["action"];
                }
                switch (Action)
                {
                    case "GetData": GetData(context); break;
                    case "GetDataHeader": GetDataHeader(context); break;
                    case "ImportExcelData": ImportExcelData(context); break;
                    case "SaveColumnMapping": SaveColumnMapping(context); break;
                    case "DeleteRecord": DeleteRecord(context); break;
                    case "GetBOMInfo": GetBOMInfo(context); break;
                    case "GetCompanyAndOtherMasterData": GetCompanyAndOtherMasterData(context); break;
                    case "GetSalespersonList": GetSalespersonList(context); break;
                    case "GetROHSList": GetROHSList(context); break;
                    case "GetCurrency": GetCurrency(context); break;
                    case "GetAllColumn": GetAllColumn(context); break;
                    case "GetMappedColumn": GetMappedColumn(context); break;
                    case "SaveUpdateRecord": SaveUpdateRecord(context); break;
                    case "ProcessRecord": ProcessRecord(context); break;
                    case "ValidateBOMData": ValidateBOMData(context); break;
                    case "GetRecordDetail": GetRecordDetail(context); break;
                    case "GetValidationError": GetValidationError(context); break;
                    case "DeleteTempMapping": DeleteTempMapping(context); break;
                    case "GetBomUploadedFiles": GetBomUploadedFiles(); break;
                    case "ResetBomData": ResetBomData(context); break;
                    case "RearrangeColumnData": RearrangeColumnData(context); break;
                    case "ReArrangeCompanyMapping": ReArrangeCompanyMapping(context); break;
                    case "IsCompanyMappingExists": IsCompanyMappingExists(context); break;
                    case "GetProduct": GetProduct(context); break;
                    case "GetManufacturer": GetManufacturer(context); break;
                    case "UpdateCell": UpdateCell(context); break; 
                    case "GetExcelHeader": GetExcelHeader(context); break;
                    case "SaveUpdateColumnMapping": SaveUpdateColumnMapping(context); break;
                    case "GetPDFAccessURL": GetPDFAccessURL(); break;
                    default: WriteErrorActionNotFound(); break;
                }

            }
        }
        //public void ImportExcelData(HttpContext context)
        //{
        //    try
        //    {
                
        //        string originalFilename = GetFormValue_String("originalFilename");
        //        string generatedFilename = GetFormValue_String("generatedFilename");

        //        string filepath = ConfigurationManager.AppSettings["ExcelDocumentPhysicalURL"].ToString() + @"Bom\" + generatedFilename;


        //        if (File.Exists(filepath))
        //        {
        //            string fileExtension = Path.GetExtension(filepath);
        //            int bomId = int.Parse(string.IsNullOrEmpty(GetFormValue_String("BOMId")) ? "0" : GetFormValue_String("BOMId"));
        //            DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepath) : ConvertExcelToDataTable(filepath);//ReadExcel(filepath);

        //            string json = GetFormValue_String("ExcelColumns").Replace('"', ' ').Trim(); 
        //            if (!string.IsNullOrEmpty(json))
        //            {
        //                DataTable dtprevious = new ConvertJsonStringToDataTable().JsonStringToDataTable(json);
        //                if(!CompareExcelHeader(dt,dtprevious))
        //                    throw new Exception("Sorry file could not uploaded because of file header not matched.", new Exception("Format invalid"));
        //            }
        //            int filelogid = 0;
        //            this.saveExcelData(dt, originalFilename, generatedFilename, bomId, out filelogid);
        //            this.SaveExcelHeader(dt,bomId);
        //            JsonObject jsn = new JsonObject();
        //            jsn.AddVariable("FileLogId", filelogid);
        //            OutputResult(jsn);
        //            jsn.Dispose(); jsn = null;
        //        }
        //        else
        //        {
        //            throw new Exception("File not uploaded successfully.", new Exception("FileNotFound"));
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        WriteError(ex);
        //    }
        //}
        public void ImportExcelData(HttpContext context)
        {
            try
            {

                string originalFilename = GetFormValue_String("originalFilename");
                string generatedFilename = GetFormValue_String("generatedFilename");
                string filepathtempfolder = FileUploadManager.GetTemporaryUploadFilePath() + generatedFilename;
                string filepath = ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"].ToString() + @"BOM/" + generatedFilename;



                //String strorageconn = ConfigurationManager.AppSettings.Get("StorageConnectionString");
                //CloudStorageAccount storageacc = CloudStorageAccount.Parse(strorageconn);
                //CloudBlobClient client = storageacc.CreateCloudBlobClient();
                string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                CloudBlobClient client = acc.CreateCloudBlobClient();
                CloudBlobContainer cont = client.GetContainerReference("gtdocmgmt");
                if (cont.Exists())
                {
                    CloudBlobDirectory directory = cont.GetDirectoryReference("BOM");
                    CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("generatedFilename"));
                    if (cblob.Exists())
                    {
                        string fileExtension = Path.GetExtension(filepath);
                        int bomId = int.Parse(string.IsNullOrEmpty(GetFormValue_String("BOMId")) ? "0" : GetFormValue_String("BOMId"));
                        //DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepath) : ConvertExcelToDataTable(filepathtempfolder, generatedFilename);//ReadExcel(filepath);
                        DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepathtempfolder,  generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, generatedFilename);//ReadExcel(filepath);
                        

                        string json = GetFormValue_String("ExcelColumns").Replace('"', ' ').Trim();
                        if (!string.IsNullOrEmpty(json))
                        {
                            DataTable dtprevious = new ConvertJsonStringToDataTable().JsonStringToDataTable(json);
                            if (!CompareExcelHeader(dt, dtprevious))
                                throw new Exception("Sorry file could not uploaded because of file header not matched.", new Exception("Format invalid"));
                        }
                        int filelogid = 0;
                        this.saveExcelData(dt, originalFilename, generatedFilename, bomId, out filelogid);
                        this.SaveExcelHeader(dt, bomId);
                        JsonObject jsn = new JsonObject();
                        jsn.AddVariable("FileLogId", filelogid);
                        OutputResult(jsn);
                        jsn.Dispose(); jsn = null;
                    }
                    else
                    {
                        throw new Exception("File not uploaded successfully.", new Exception("FileNotFound"));
                    }
                }
                // if (File.Exists(filepath))
                // {
                //    string fileExtension = Path.GetExtension(filepath);
                //    int bomId = int.Parse(string.IsNullOrEmpty(GetFormValue_String("BOMId")) ? "0" : GetFormValue_String("BOMId"));
                //    DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepath) : ConvertExcelToDataTable(filepath);//ReadExcel(filepath);

                //    string json = GetFormValue_String("ExcelColumns").Replace('"', ' ').Trim(); 
                //    if (!string.IsNullOrEmpty(json))
                //    {
                //        DataTable dtprevious = new ConvertJsonStringToDataTable().JsonStringToDataTable(json);
                //        if(!CompareExcelHeader(dt,dtprevious))
                //            throw new Exception("Sorry file could not uploaded because of file header not matched.", new Exception("Format invalid"));
                //    }
                //    int filelogid = 0;
                //    this.saveExcelData(dt, originalFilename, generatedFilename, bomId, out filelogid);
                //    this.SaveExcelHeader(dt,bomId);
                //    JsonObject jsn = new JsonObject();
                //    jsn.AddVariable("FileLogId", filelogid);
                //    OutputResult(jsn);
                //    jsn.Dispose(); jsn = null;
                //}
                //else
                //{
                //    throw new Exception("File not uploaded successfully.", new Exception("FileNotFound"));
                //}
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void SaveColumnMapping(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                string json = new StreamReader(context.Request.InputStream).ReadToEnd();
                if (json != "{\"MappingData\":[]}")
                {
                    DataTable dt = (new ConvertJsonStringToDataTable()).JsonStringToDataTable(json);
                    foreach (DataRow dr in dt.Rows)
                    {
                        CustomerRequirement.SaveMappingDetail(int.Parse(dr["ColumnIndex"].ToString()),
                            int.Parse(dr["ColumnId"].ToString()),
                            bomId,
                            SessionManager.LoginID ?? 0);
                    }
                }
                //auto map remaining column
                CustomerRequirement.AutoMapRemainingColumn(bomId, SessionManager.LoginID ?? 0);
                //save column mapping detail for company
                CustomerRequirement.SaveCompanyColumnMapping(bomId);
            }
            catch (Exception)
            {

            }
        }
        private void ValidateBOMData(HttpContext context)
        {
            try
            {
                string recordIds = context.Request.QueryString["RecordIds"];
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BomId"]) ? 0 : int.Parse(context.Request.QueryString["BomId"]);

                if (!string.IsNullOrEmpty(recordIds))
                    context.Response.Write(CustomerRequirement.ValidateBOMData(recordIds, bomId, SessionManager.LoginID ?? 0));


            }
            catch (Exception)
            {

            }
        }
        private void SaveUpdateRecord(HttpContext context)
        {
            try
            {
                string json = new StreamReader(context.Request.InputStream).ReadToEnd();
                json = HttpUtility.HtmlDecode(json);
                DataTable dt = (new ConvertJsonStringToDataTable()).JsonStringToDataTable(json);
                CustomerRequirement.SaveUpdateRecord(dt, SessionManager.LoginID ?? 0);
            }
            catch (Exception)
            {

            }
        }
        private void DeleteTempMapping(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BomId"]) ? 0 : int.Parse(context.Request.QueryString["BomId"]);
                CustomerRequirement.DeleteTempMapping(bomId, SessionManager.LoginID ?? 0);
            }
            catch (Exception)
            {

            }
        }
        private void ProcessRecord(HttpContext context)
        {
            string response = "{\"RecordCount\":0}";
            try
            {
                int BomId = 0;
                int PageSize = 0;
                int CurrentIndex = 0;
                string recordIds = string.Empty;
                string json = new StreamReader(context.Request.InputStream).ReadToEnd();
                json = HttpUtility.HtmlDecode(json);
                DataTable dt = (new ConvertJsonStringToDataTable()).JsonStringToDataTable(json);
                if (dt != null && dt.Rows.Count > 0)
                {
                    BomId = int.Parse(dt.Rows[0]["BomId"].ToString());
                    PageSize = int.Parse(dt.Rows[0]["PageSize"].ToString());
                    CurrentIndex = int.Parse(dt.Rows[0]["CurrentIndex"].ToString());
                    recordIds = dt.Rows[0]["RecordIds"].ToString();
                    int recordCount = CustomerRequirement.ProcessRecord(BomId, SessionManager.LoginID ?? 0, CurrentIndex, PageSize, recordIds);
                    response = response.Replace("0", recordCount.ToString());
                }

            }
            catch (Exception)
            {

            }
            context.Response.Write(response);
        }
        private void DeleteRecord(HttpContext context)
        {
            try
            {
                int recordId = int.Parse(context.Request.QueryString["Id"].ToString());
                CustomerRequirement.DeleteRecord(recordId);
            }
            catch (Exception)
            {

            }
        }
        private void saveExcelData(DataTable dtData, string originalFilename, string generatedFilename, int bomId, out int filelogid)
        {

            DataTable testBOM = new DataTable("tbTempBomData");
            DataTable dtColumnList = CustomerRequirement.GetCustTableAllColumn();
            // Create Column 1: SaleDate
            DataColumn newColumn = null;
            int colCount = 1;
            foreach (DataRow dr in dtColumnList.Rows)
            {
                newColumn = new DataColumn();
                newColumn.ColumnName = "Column" + colCount.ToString();
                testBOM.Columns.Add(newColumn);
                ++colCount;
            }

            DataColumn ClientBOMId = new DataColumn();
            ClientBOMId.DataType = typeof(int);
            ClientBOMId.ColumnName = "ClientBOMId";

            DataColumn CreatedBy = new DataColumn();
            CreatedBy.DataType = typeof(int);
            CreatedBy.ColumnName = "CreatedBy";

            DataColumn orgFilename = new DataColumn();
            orgFilename.DataType = typeof(string);
            orgFilename.ColumnName = "OriginalFilename";

            DataColumn genFilename = new DataColumn();
            genFilename.DataType = typeof(string);
            genFilename.ColumnName = "GeneratedFilename";


            // Add the columns to the ProductSalesData DataTable
            testBOM.Columns.Add(ClientBOMId);
            testBOM.Columns.Add(CreatedBy);
            testBOM.Columns.Add(orgFilename);
            testBOM.Columns.Add(genFilename);


            // Create a new row
            int maxColumnNo = dtData.Columns.Count;
            if (dtColumnList.Rows.Count < dtData.Columns.Count)
                maxColumnNo = dtColumnList.Rows.Count;
            foreach (DataRow dr in dtData.Rows)
            {
                DataRow newBOM = testBOM.NewRow();
                colCount = 1;
                for (int i = 0; i < maxColumnNo; i++)
                {
                    newBOM["Column" + colCount.ToString()] = Functions.CleanJunkCharInCSV(dr[i].ToString()).Replace("00:00:00:00", "").Replace("00:00:00", "").Replace("00:00", "");//remove time from datetime field
                    ++colCount;
                }
                newBOM["ClientBOMId"] = bomId;
                newBOM["CreatedBy"] = SessionManager.LoginID;
                newBOM["OriginalFilename"] = originalFilename;
                newBOM["GeneratedFilename"] = generatedFilename;
                testBOM.Rows.Add(newBOM);
            }


            // Copy the DataTable to SQL Server using SqlBulkCopy
            CustomerRequirement.saveExcelData(testBOM, bomId);
            //Save uploaded file info
            filelogid = CustomerRequirement.saveBomFileInfo(bomId, originalFilename, generatedFilename, SessionManager.LoginID ?? 0);

        }
        //public DataTable ReadCsvFile(string filepath)
        //{
        //    DataTable dtCsv = new DataTable();
        //    try
        //    {

        //        string Fulltext;
        //        using (StreamReader sr = new StreamReader(filepath,Encoding.Default))
        //        {
        //            while (!sr.EndOfStream)
        //            {
        //                Fulltext = sr.ReadToEnd().ToString(); //read full file text  
        //                string[] rows = Fulltext.Split('\n'); //split full file text into rows  
        //                for (int i = 0; i < rows.Length - 1; i++)
        //                {
        //                    string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
        //                    {
        //                        if (i == 0)
        //                        {
        //                            for (int j = 0; j < rowValues.Length; j++)
        //                            {
        //                                dtCsv.Columns.Add(rowValues[j]); //add headers  
        //                            }
        //                        }
        //                        else
        //                        {
        //                            DataRow dr = dtCsv.NewRow();
        //                            for (int k = 0; k < rowValues.Length; k++)
        //                            {
        //                                dr[k] = Functions.CleanJunkCharInCSV(rowValues[k].ToString()).Replace("00:00:00:00", "").Replace("00:00:00", "").Replace("00:00", "");
        //                            }
        //                            dtCsv.Rows.Add(dr); //add other rows  
        //                        }
        //                    }
        //                }
        //            }
        //        }
        //    }
        //    catch (Exception ex)
        //    {
        //        throw new Exception("Error Occured while processig the csv file.Kindly review the data in the csv file.", new Exception("CSVDataError"));
        //    }
        //    return dtCsv;
        //}
        public DataTable ReadCsvFile(string filepath, string FileName)
        {
            DataTable dtCsv = new DataTable();
            try
            {

                string Fulltext;
                WebClient web = new WebClient();
                System.IO.Stream stream = web.OpenRead(filepath);
                using (StreamReader sr = new StreamReader(stream, Encoding.Default))
                {
                    while (!sr.EndOfStream)
                    {
                        Fulltext = sr.ReadToEnd().ToString(); //read full file text  
                        string[] rows = Fulltext.Split('\n'); //split full file text into rows  
                        for (int i = 0; i < rows.Length - 1; i++)
                        {
                            string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
                            {
                                if (i == 0)
                                {
                                    for (int j = 0; j < rowValues.Length; j++)
                                    {
                                        dtCsv.Columns.Add(rowValues[j]); //add headers  
                                    }
                                }
                                else
                                {
                                    DataRow dr = dtCsv.NewRow();
                                    for (int k = 0; k < rowValues.Length; k++)
                                    {
                                        dr[k] = Functions.CleanJunkCharInCSV(rowValues[k].ToString()).Replace("00:00:00:00", "").Replace("00:00:00", "").Replace("00:00", "");
                                    }
                                    dtCsv.Rows.Add(dr); //add other rows  
                                }
                            }
                        }
                    }
                }
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                web.Dispose();
            }
            catch
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processig the csv file.Kindly review the data in the csv file.", new Exception("CSVDataError"));
            }
            return dtCsv;
        }
        public DataTable ConvertExcelToDataTable(string FilePath,string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            // Check if the file exists
            //if (!fi.Exists)
            //    throw new Exception("File " + FilePath + " Does Not Exists");
            try
            {
                if (fi.Exists)
                {
                    string Extension = Path.GetExtension(FilePath);
                    var connectionString = "";
                    //connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 12.0 Xml;HDR=YES;IMEX=1;MAXSCANROWS=0'";

                    switch (Extension)
                    {
                        case ".xls": //Excel 97-03

                            connectionString = "Provider=Microsoft.Jet.OLEDB.4.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 8.0;HDR=YES;'";
                            break;
                        case ".xlsx": //Excel 07
                            connectionString = "Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + FilePath + ";" + "Extended Properties='Excel 12.0 Xml;HDR=YES;IMEX=1;MAXSCANROWS=0'";
                            break;
                    }
                    using (var conn = new OleDbConnection(connectionString))
                    {
                        conn.Open();

                        var sheets = conn.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, new object[] { null, null, null, "TABLE" });
                        using (var cmd = conn.CreateCommand())
                        {
                            cmd.CommandText = "SELECT * FROM [" + sheets.Rows[0]["TABLE_NAME"].ToString() + "] ";

                            var adapter = new OleDbDataAdapter(cmd);

                            adapter.Fill(dt);
                        }
                        
                    }
                    string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                    System.IO.File.Delete(Deletetempfolderfile);
                }

            }
            catch (Exception)
            {
                throw new Exception("Error Occured while processig the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
            }
            return dt;
        }
        public DataTable ConvertExcelToDataTableNew(string FilePath,  string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            try
            {
                if (fi.Exists)
                {
                    List<string> sheets = ExcelAdapter.GetSheet(FilePath);
                    if (sheets.Count > 0)
                        dt = ExcelAdapter.ReadExcel(FilePath, sheets[0]);

                }
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);

            }
            catch (Exception)
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
            }
            return dt;
        }
        public DataTable ReadExcel(string FilePath)
        {

            DataTable dtResult = new DataTable();
            FileInfo fi = new FileInfo(FilePath);


            // Check if the file exists
            if (!fi.Exists)
                throw new Exception("File " + FilePath + " Does Not Exists");

            using (ExcelPackage xlPackage = new ExcelPackage(fi))
            {
                // get the first worksheet in the workbook
                ExcelWorksheet worksheet = xlPackage.Workbook.Worksheets["Sheet1"];

                // Fetch the WorkSheet size
                ExcelCellAddress startCell = worksheet.Dimension.Start;
                ExcelCellAddress endCell = worksheet.Dimension.End;

                // create all the needed DataColumn
                for (int col = startCell.Column; col <= endCell.Column; col++)
                    dtResult.Columns.Add(col.ToString());

                // place all the data into DataTable
                for (int row = startCell.Row; row <= endCell.Row; row++)
                {
                    DataRow dr = dtResult.NewRow();
                    int x = 0;
                    for (int col = startCell.Column; col <= endCell.Column; col++)
                    {
                        dr[x++] = worksheet.Cells[row, col].Value;
                    }
                    dtResult.Rows.Add(dr);
                }
            }
            //int totalSheet = 0; //No of sheets on excel file  
            //string conStr = Path.GetExtension(filepath) == ".xls" ? @"Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filepath + ";Extended Properties='Excel 8.0;HDR=YES'" :
            //                @"Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + filepath + ";Extended Properties='Excel 12.0 Xml;HDR=YES'";
            //using (OleDbConnection objConn = new OleDbConnection(conStr))
            //{
            //    objConn.Open();
            //    OleDbCommand cmd = new OleDbCommand();
            //    OleDbDataAdapter oleda = new OleDbDataAdapter();
            //    DataSet ds = new DataSet();
            //    DataTable dt = objConn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, null);
            //    string sheetName = string.Empty;
            //    if (dt != null)
            //    {
            //        var tempDataTable = (from dataRow in dt.AsEnumerable()
            //                             where !dataRow["TABLE_NAME"].ToString().Contains("FilterDatabase")
            //                             select dataRow).CopyToDataTable();
            //        dt = tempDataTable;
            //        totalSheet = dt.Rows.Count;
            //        sheetName = dt.Rows[0]["TABLE_NAME"].ToString();
            //    }
            //    cmd.Connection = objConn;
            //    cmd.CommandType = CommandType.Text;
            //    cmd.CommandText = "SELECT * FROM [" + sheetName + "]";
            //    oleda = new OleDbDataAdapter(cmd);
            //    oleda.Fill(ds, "excelData");
            //    dtResult = ds.Tables["excelData"];
            //    objConn.Close();
            return dtResult; //Returning Dattable  
            //}
        }
        private void GetDataHeader(HttpContext context)
        {
            try
            {
                DataTable dtColumnList = CustomerRequirement.GetCustTableAllColumn();
                JavaScriptSerializer js = new JavaScriptSerializer();
                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetData(HttpContext context)
        {
            try
            {
                int displayLength = int.Parse(context.Request.Params["Length"]);
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "asc";// context.Request.Params["order[0][dir]"];
                string search = context.Request.Params["search[value]"];
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);

                DataTable dtResult = CustomerRequirement.GetBOMDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, bomId);

                int total = Convert.ToInt32(dtResult.Rows.Count == 0 ? "0" : dtResult.Rows[0]["totalcount"].ToString());
                context.Response.Write(DataTableToJsonObj(dtResult, total, total));
                //jsn.Dispose();
                //jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetRecordDetail(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);

                DataTable dtResult = CustomerRequirement.GetRecordDetail(bomId);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetValidationError(HttpContext context)
        {
            try
            {
                string recordIds = context.Request.QueryString["RecordIds"];
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                DataTable dtResult = CustomerRequirement.GetValidationError(bomId, SessionManager.LoginID ?? 0, recordIds);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetBOMInfo(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                DataTable dtResult = CustomerRequirement.GetBOMInfo(bomId);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetAllColumn(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                DataTable dtResult = CustomerRequirement.GetAllColumn(bomId, SessionManager.LoginID ?? 0);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetMappedColumn(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                DataTable dtResult = CustomerRequirement.GetMappedColumn(bomId, SessionManager.LoginID ?? 0);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetCompanyAndOtherMasterData(HttpContext context)
        {
            try
            {
                int companyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
                DataTable dtResult = CustomerRequirement.GetCompanyAndOtherMasterData(companyId);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetSalespersonList(HttpContext context)
        {
            try
            {
                DataTable dtResult = CustomerRequirement.GetSalespersonList(SessionManager.ClientID ?? 0);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetROHSList(HttpContext context)
        {
            try
            {
                DataTable dtResult = new DataTable();
                dtResult.Columns.Add("Id", typeof(int));
                dtResult.Columns.Add("Text", typeof(string));
                List<BLL.RohsStatus> lst = BLL.RohsStatus.DropDown();
                for (int i = 0; i < lst.Count; i++)
                {
                    dtResult.Rows.Add(lst[i].ROHSStatusId, Functions.GetROHSStatusName(lst[i].ROHSStatusId));
                }
                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public void GetCurrency(HttpContext context)
        {
            try
            {
                int globalCurrencyNo = string.IsNullOrEmpty(context.Request.QueryString["GlobalCurrencyNo"]) ? 0 : int.Parse(context.Request.QueryString["GlobalCurrencyNo"]);
                DataTable dtResult = new DataTable();
                dtResult.Columns.Add("Id", typeof(int));
                dtResult.Columns.Add("Text", typeof(string));
                List<BLL.Currency> lst = BLL.Currency.DropDownBuyForClientAndGlobal(SessionManager.ClientID, globalCurrencyNo, false);
                for (int i = 0; i < lst.Count; i++)
                {
                    dtResult.Rows.Add(lst[i].CurrencyId, String.Format("{1} - {0}", lst[i].CurrencyDescription, lst[i].CurrencyCode));
                }
                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private string ConvertHeaderToJsonObj(DataTable dt)
        {
            StringBuilder JsonString = new StringBuilder();
            if (dt != null && dt.Rows.Count > 0)
            {
                //JsonString.Append("[");
                JsonString.Append("{");
                JsonString.Append("\"data\":");
                JsonString.Append("[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    JsonString.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        if (j < dt.Columns.Count - 1)
                        {
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\",");
                        }
                        else if (j == dt.Columns.Count - 1)
                        {
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\"");
                        }
                    }
                    if (i == dt.Rows.Count - 1)
                    {
                        JsonString.Append("}");
                    }
                    else
                    {
                        JsonString.Append("},");
                    }
                }
                JsonString.Append("]");
                JsonString.Append("}");
                //JsonString.Append("]");

                return Regex.Replace(JsonString.ToString(), @"\r\n?|\n", "");
            }
            else
            {
                return null;
            }
        }
        private string DataTableToJsonObj(DataTable dt, int iTotalRecords, int iTotalDisplayRecords)
        {
            StringBuilder JsonString = new StringBuilder();
            if (dt != null && dt.Rows.Count > 0)
            {
                //JsonString.Append("[");
                JsonString.Append("{\"iTotalRecords\":" + iTotalRecords + ",");
                JsonString.Append("\"iTotalDisplayRecords\":" + iTotalDisplayRecords + ",");
                JsonString.Append("\"data\":");
                JsonString.Append("[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    JsonString.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        if (j < dt.Columns.Count - 1)
                        {
                            //JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\",");
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString() + "\",");
                        }
                        else if (j == dt.Columns.Count - 1)
                        {
                            //JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\"");
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString() + "\"");
                        }
                    }
                    if (i == dt.Rows.Count - 1)
                    {
                        JsonString.Append("}");
                    }
                    else
                    {
                        JsonString.Append("},");
                    }
                }
                JsonString.Append("]");
                JsonString.Append("}");
                //JsonString.Append("]");


            }
            else
            {
                JsonString.Append("{\"iTotalRecords\":" + iTotalRecords + ",");
                JsonString.Append("\"iTotalDisplayRecords\":" + iTotalDisplayRecords + ",");
                JsonString.Append("\"data\":");
                JsonString.Append("[]}");
            }
            return Regex.Replace(JsonString.ToString(), @"\r\n?|\n", "");
        }
        private string ConvertDataTableToJSON(DataTable dt)
        {
            System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            //[002]
            //List<Dictionary<string, object>> rows = new List<Dictionary<string, object>>();
            List<ConcurrentDictionary<string, object>> rows = new List<ConcurrentDictionary<string, object>>();
            
            //Dictionary<string, object> row;
            ConcurrentDictionary<string, object> row;
            
            foreach (DataRow dr in dt.Rows)
            {
                //row = new Dictionary<string, object>()
                row = new ConcurrentDictionary<string, object>();
                foreach (DataColumn col in dt.Columns)
                {
                    //row.Add(col.ColumnName, dr[col]);
                    row.TryAdd(col.ColumnName, dr[col]);
                }
                rows.Add(row);
            }
            return serializer.Serialize(rows);
        }
        public void GetBomUploadedFiles()
        {
            try
            {

                int BomId = GetFormValue_Int("BomId");
                //string PDFPath = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExcelDocumentPhysicalURL"])) + @"Bom\";
                string PDFPath = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"])) + @"BOM/";
                List<PDFDocument> lstPDFDocument = CustomerRequirement.GetBomUploadedFiles(BomId);

                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                if (lstPDFDocument != null)
                {
                    foreach (BLL.PDFDocument pdfDoc in lstPDFDocument)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", pdfDoc.PDFDocumentId);
                        jsnItem.AddVariable("FilePath", !string.IsNullOrEmpty(pdfDoc.GeneratedFileName) ? PDFPath + pdfDoc.GeneratedFileName : "#");
                        jsnItem.AddVariable("FileName", pdfDoc.FileName);
                        jsnItem.AddVariable("Date", Functions.FormatDate(pdfDoc.DLUP, false, true));
                        jsnItem.AddVariable("By", pdfDoc.UpdatedByName);
                        jsnItem.AddVariable("Caption", pdfDoc.Caption);
                        jsnItem.AddVariable("GeneratedFileName", pdfDoc.GeneratedFileName);
                        jsnItem.AddVariable("Section", "BOM");
                        //jsnItem.AddVariable("IconPath", !string.IsNullOrEmpty(pdfDoc.GeneratedFileName) ? "app_themes/original/images/IconButton/pdficon.jpg" :
                        //                                "app_themes/original/images/IconButton/NoPDF.PNG");
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                }

                jsn.AddVariable("Items", jsnItems);
                jsn.AddVariable("IconPath", "app_themes/original/images/IconButton/ExcelIcon.jpg");
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;

            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void GetPDFAccessURL()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
                string bothirl = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", GetFormValue_String("filename"), sasURL, GetFormValue_String("section").ToUpper());
                jsn.AddVariable("bothirl", bothirl);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void ResetBomData(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BomId"]) ? 0 : int.Parse(context.Request.QueryString["BomId"]);
                int fileLogId = string.IsNullOrEmpty(context.Request.QueryString["FileLogId"]) ? 0 : int.Parse(context.Request.QueryString["FileLogId"]);
                CustomerRequirement.ResetBomData(bomId, SessionManager.LoginID ?? 0, fileLogId);
            }
            catch (Exception)
            {

            }
        }
        private void RearrangeColumnData(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BomId"]) ? 0 : int.Parse(context.Request.QueryString["BomId"]);
                string insertColumnList = context.Request.QueryString["InsertColumnList"];
                string selectColumnList = context.Request.QueryString["SelectColumnList"];
                CustomerRequirement.RearrangeColumnData(bomId, SessionManager.LoginID ?? 0, insertColumnList, selectColumnList);
            }
            catch (Exception)
            {

            }
        }
        private void ReArrangeCompanyMapping(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                string json = new StreamReader(context.Request.InputStream).ReadToEnd();
                if (json != "{\"MappingData\":[]}")
                {
                    DataTable dt = (new ConvertJsonStringToDataTable()).JsonStringToDataTable(json);
                    foreach (DataRow dr in dt.Rows)
                    {
                        CustomerRequirement.ReArrangeCompanyMapping(int.Parse(dr["ColumnIndex"].ToString()),
                            int.Parse(dr["ColumnId"].ToString()),
                            bomId,
                            SessionManager.LoginID ?? 0);
                    }
                }
                //update company mapping detail
                CustomerRequirement.UpdateCompanyMapping(bomId, SessionManager.LoginID ?? 0);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private void IsCompanyMappingExists(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                context.Response.Write(CustomerRequirement.IsCompanyMappingExists(bomId));
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
        private void GetProduct(HttpContext context)
        {

            int? intPOHubClientNo;
            intPOHubClientNo = GetFormValue_NullableInt("intPOHubClientNo");

            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");


            List<Product> lst = null;
            try
            {
                //lst = Product.AutoSearch(GetFormValue_StringForNameSearch("search"), intPOHubClientNo.HasValue && intPOHubClientNo.Value > 0?intPOHubClientNo.Value: SessionManager.ClientID);//GetFormValue_NullableBoolean("blnShowInactive")  GetFormValue_Boolean("ShowInactive")
                lst = Product.AutoSearch(GetFormValue_StringForNameSearch("search"), (intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : ((intPOHubClientNo.HasValue && intPOHubClientNo.Value > 0) ? intPOHubClientNo : SessionManager.ClientID));
                DataTable dt = createTable();
                for (int i = 0; i < lst.Count; i++)
                {
                    //dt.Rows.Add(lst[i].ProductId.ToString(), lst[i].ProductName.ToString());
                    //start code by umendra
                     dt.Rows.Add(lst[i].ProductId.ToString(), lst[i].ProductDescription.ToString());
                    //end code by umendra
                }
                context.Response.Write(ConvertDataTableToJSON(dt));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            finally
            {
                lst = null;
            }

        }

        protected void GetManufacturer(HttpContext context)
        {
            List<Manufacturer> lst = null;
            try
            {
                lst = Manufacturer.AutoSearch(GetFormValue_StringForNameSearch("search"), false);//GetFormValue_NullableBoolean("blnShowInactive")  GetFormValue_Boolean("ShowInactive")
                DataTable dt = createTable();
                for (int i = 0; i < lst.Count; i++)
                {

                    dt.Rows.Add(lst[i].ManufacturerId.ToString(), lst[i].ManufacturerName.ToString());
                    
                }
                context.Response.Write(ConvertDataTableToJSON(dt));

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        private DataTable createTable()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("Id", typeof(string));
            dt.Columns.Add("Text", typeof(string));
            return dt;
        }
        private void UpdateCell(HttpContext context)
        {
            try
            {
                string json = new StreamReader(context.Request.InputStream).ReadToEnd();
                json = HttpUtility.HtmlDecode(json);
                DataTable dt = (new ConvertJsonStringToDataTable()).JsonStringToDataTable(json);
                CustomerRequirement.UpdateCell(dt, SessionManager.LoginID ?? 0);
            }
            catch (Exception)
            {

            }
        }
        private void SaveExcelHeader(DataTable dtData,int bomId)
        {
            string columnList = string.Empty;
            string insertColumnList = string.Empty;
            int i = 1;
            foreach (DataColumn column in dtData.Columns)
            {
                columnList = columnList +"'"+ column.ColumnName + "',";
                insertColumnList = insertColumnList + "Column" + i.ToString()+ ",";
                ++i;
            }
            if (!string.IsNullOrEmpty(columnList))
                columnList = columnList.Substring(0,columnList.Length - 1);
            if (!string.IsNullOrEmpty(insertColumnList))
                insertColumnList = insertColumnList.Substring(0, insertColumnList.Length - 1);
            CustomerRequirement.SaveExcelHeader(columnList, insertColumnList,bomId, SessionManager.LoginID ?? 0);
        }
        private void GetExcelHeader(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                DataTable dtColumnList = CustomerRequirement.GetExcelHeader(bomId,SessionManager.LoginID ?? 0);
                JavaScriptSerializer js = new JavaScriptSerializer();
                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private bool CompareExcelHeader(DataTable dtCurrent,DataTable dtPrevious)
        {
            return getAllColumnCurrent(dtCurrent) == getAllColumnPrevious(dtPrevious);
        }
        private string getAllColumnCurrent(DataTable dt)
        {
            string columns = string.Empty;
            foreach (DataColumn  dc in dt.Columns)
            {
                columns = columns + dc.ColumnName.Trim()+",";
            }
            if (columns.Length > 0)
                columns = columns.Substring(0, columns.Length - 1);
            return columns;
        }
        private string getAllColumnPrevious(DataTable dt)
        {
            string columns = string.Empty;
            for(int i=0;i<dt.Columns.Count;i++)
            {
                string columnName = "Column" + i.ToString();
                columns = columns + dt.Rows[0][i].ToString().Trim() + ",";
            }
            if (columns.Length > 0)
                columns = columns.Substring(0, columns.Length - 1);
            return columns;
        }
        private void SaveUpdateColumnMapping(HttpContext context)
        {
            try
            {
                int bomId = string.IsNullOrEmpty(context.Request.QueryString["BOMId"]) ? 0 : int.Parse(context.Request.QueryString["BOMId"]);
                int columnIndex = string.IsNullOrEmpty(context.Request.QueryString["ColumnIndex"]) ? 0 : int.Parse(context.Request.QueryString["ColumnIndex"]);
                int columnNo = string.IsNullOrEmpty(context.Request.QueryString["columnNo"]) ? 0 : int.Parse(context.Request.QueryString["columnNo"]);
                int userid = SessionManager.LoginID ?? 0;
                if (bomId>0 && columnIndex>0&& columnNo>0&& userid>0)
                {
                   CustomerRequirement.SaveMappingDetail(columnIndex,columnNo,bomId,userid);
               }
               
            }
            catch (Exception)
            {

            }
        }


    }

}
