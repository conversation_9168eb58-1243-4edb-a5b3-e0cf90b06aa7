﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class APIExternalLinksProvider : DataAccess
    {
        static private APIExternalLinksProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public APIExternalLinksProvider Instance
        {
            get
            {
                if (_instance == null)
                    _instance = (APIExternalLinksProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.APIExternalLinks.ProviderType));
                // _instance = (APIExternalLinksProvider)Activator.CreateInstance(Type.GetType("Rebound.GlobalTrader.DAL.SqlClient.SQLAPIExternalLinksProvider"));
                return _instance;
            }
        }

        //public APIExternalLinksProvider()
        //{
        //    this.ConnectionString = Globals.Settings.APIExternalLinks.ConnectionString;
        //}
        public APIExternalLinksProvider()
        {
            this.ConnectionString = Globals.Settings.APIExternalLinks.ConnectionString;
            this.GTConnectionString = Globals.Settings.APIExternalLinks.GTConnectionString;
        }
        /// <summary>
        /// usp_Select_APIExternalLinks
        /// </summary>
        /// <param name="dateInput"></param>
        /// <returns></returns>
        public abstract List<APIExternalLinksDetails> Source1(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId);
        public abstract List<APIExternalLinksDetails> Source(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, string ApiName, int ApiURLKeyId);
        public abstract string APIExternalLinksOffers(string ApiURL, string HostName, string Xorbweaverlicensekey, string partSearch);
        public abstract List<ApiKeyValueDetails> APIExternalLinksDetails(System.String sourcingName, System.String moduleName, int clientId, bool hasServerLocal);
        public abstract int InsertMultipleAPIJson(System.String result, string ApiName, int clientId, bool hasServerLocal);
        public abstract int InsertMultipleAPIJson(System.String result, string ApiName, int clientId, bool hasServerLocal, string ApiShortName);
        public abstract List<APIExternalLinksDetails> ApiNotRespond(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, string ApiName);

        //public abstract List<APIExternalLinksDetails> Source(System.String result, System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId);
        public abstract List<APIExternalLinksDetails> SourceMultipleParts(System.String result, System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId);
        public abstract List<APIExternalLinksDetails> SourceFromDBMultipleParts(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId);
        public abstract List<APIExternalLinksDetails> Source(System.String result, System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId, System.Int32? UserId);
        public abstract List<LyticaAPI> InsretLyticaAPI(string APIResponseJson, int? UpdatedBy);

        public abstract List<APIExternalLinksDetails> GetDigiKeyApi(System.Int32? clientId, System.String partSearch, bool IsServerLocal, string SourcingName, int ApiURLKeyId);
    }
}
