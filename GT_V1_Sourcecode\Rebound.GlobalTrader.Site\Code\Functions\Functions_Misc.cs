using System;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Reflection;
using System.ComponentModel;
using System.Resources;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Text;
using System.IO;
using System.Web.UI.WebControls;
using System.Collections;
using System.Threading;
using System.Globalization;
using System.Diagnostics;
using System.Net;


/// <summary>
/// Functions, static class
/// </summary>
namespace Rebound.GlobalTrader.Site {
	public static partial class Functions {

		/// <summary>
		/// Recursively finds a control
		/// </summary>
		/// <param name="ctlRoot">Parent control to search in</param>
		/// <param name="strID">ID of the control to find</param>
		/// <returns>The found control or null</returns>
		public static Control FindControlRecursive(Control ctlRoot, string strID) {
			if (ctlRoot.ID == strID) {
				return ctlRoot;
			}
			foreach (Control ctl in ctlRoot.Controls) {
				Control ctl2 = FindControlRecursive(ctl, strID);
				if (ctl2 != null) {
					return ctl2;
				}
			}
			return null;
		}

		///// <summary>
		///// Gets a page URL from the PageURLS.resx file
		///// </summary>
		///// <param name="enmPage"></param>
		///// <returns></returns>
		//public static string GetPageURL(BLL.SitePage.List enmPage, bool blnIncludeTildePath) {
		//    return string.Format("{0}{1}", (blnIncludeTildePath) ? "~/" : "", BLL.SitePage.GetPageURL(enmPage));
		//}
		//public static string GetPageURL(BLL.SitePage.List enmPage) {
		//    return GetPageURL(enmPage, true);
		//}

		/// <summary>
		/// Creates a new string list
		/// </summary>
		/// <param name="str"></param>
		/// <returns></returns>
		public static List<string> CreateList(params string[] str) {
			List<string> lst = new List<string>();
			for (int i = 0; i < str.Length; i++) { lst.Add(str[i]); }
			return lst;
		}

		/// <summary>
		/// creates a new int list
		/// </summary>
		/// <param name="intIn"></param>
		/// <returns></returns>
		public static List<int> CreateList(params int[] intIn) {
			List<int> lst = new List<int>();
			for (int i = 0; i < intIn.Length; i++) { lst.Add(intIn[i]); }
			return lst;
		}

		/// <summary>
		/// creates a new list of Units
		/// </summary>
		/// <param name="intIn"></param>
		/// <returns></returns>
		public static List<Unit> CreateList(params Unit[] unitIn) {
			List<Unit> lst = new List<Unit>();
			for (int i = 0; i < unitIn.Length; i++) { lst.Add(unitIn[i]); }
			return lst;
		}

		/// <summary>
		/// Checks whether a given email address is valid
		/// </summary>
		/// <param name="strInputEmail"></param>
		/// <returns></returns>
		public static bool ValidEmail(string strInputEmail) {
			string strRegex = @"^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}" +
						@"\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\" +
						@".)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$";
			Regex re = new Regex(strRegex);
			return (re.IsMatch(strInputEmail));
		}

		/// <summary>
		/// 
		/// </summary>
		/// <param name="strRef">Reference</param>
		/// <param name="strAssembly">Assembly</param>
		/// <param name="blnUseFileForDebug">Should the Debug script be put in as a file?</param>
		/// <returns></returns>
		public static ScriptReference GetScriptReference(bool blnDebug, string strAssembly, string strRef, bool blnUseFileForDebug) {
			strRef = strRef.Replace(".js", "");
			strRef = strRef.Replace(strAssembly + ".", "");
			ScriptReference sr;
			if (blnDebug && blnUseFileForDebug) {
				sr = new ScriptReference(String.Format("~/{0}.js", strRef.Replace(".", "/")));
			} else {
				//put script reference in from assembly
				sr = new ScriptReference(String.Format("{0}.{1}.js", strAssembly, strRef.Replace("/", ".")), strAssembly);
			}
			if (sr != null) sr.ScriptMode = ScriptMode.Inherit;
			return sr;
		}
		public static ScriptReference GetScriptReference(bool blnDebug, string strAssembly, string strRef) {
			return GetScriptReference(blnDebug, strAssembly, strRef, true);
		}

		internal static string FormatDLUP(DateTime? dtm, int? intLoginNo) {
			string str = Functions.FormatDate(dtm, true, true);
			if (intLoginNo > 0) {
				string strLoginName = LoginManager.GetName((int)intLoginNo);
				if (!String.IsNullOrEmpty(strLoginName)) str = String.Format(Functions.GetGlobalResource("misc", "UpdatedByDateAndUser"), str, strLoginName);
			}
			return str;
		}

        internal static string FormatUPbyOn(DateTime? dtm, int? intLoginNo)
        {
            string str = Functions.FormatDate(dtm, true, true);
            if (intLoginNo > 0)
            {
                string strLoginName = LoginManager.GetName((int)intLoginNo);
                if (!String.IsNullOrEmpty(strLoginName)) str = String.Format(Functions.GetGlobalResource("misc", "ApprovedByAndDate"), strLoginName, str);
            }
            return str;
        }


		internal static string GetROHSStatusWithLogo(int intROHS) {
			string strROHS = GetROHSStatusName(intROHS);
			return string.Format(@"<div title=""{0}"" class=""rohs rohs{1}"">{0}</div>", strROHS, ((BLL.RohsStatus.List)intROHS).ToString());
		}

		internal static string GetROHSStatusName(int intROHSStatusID) {
			return Functions.GetGlobalResource("Misc", String.Format("ROHS{0}", ((BLL.RohsStatus.List)intROHSStatusID).ToString()));
		}

		public static string FooterString() {
			StringBuilder sb = new StringBuilder("");
			if (CacheManager.GetItem(CacheKeys.FooterText) == null) {
				FileVersionInfo fvi = FileVersionInfo.GetVersionInfo(Assembly.GetExecutingAssembly().Location);
				sb.AppendFormat(@"<p>{0} v{1}.{2}.{3}.{4}</p>", Functions.GetGlobalResource("Misc", "AppTitle"), fvi.FileMajorPart, fvi.FileMinorPart, fvi.FileBuildPart, fvi.FilePrivatePart);
				CacheManager.StoreItem(CacheKeys.FooterText, sb.ToString());
			} else {
				sb.Append(CacheManager.GetItem(CacheKeys.FooterText).ToString());
			}
			return sb.ToString();
		}

        public static void ClearFormCache(string id)
        {
            try
            {
                string[] strArray = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["WebFormURL"]).Split(',');
                foreach (string str in strArray)
                {
                    WebRequest request = HttpWebRequest.Create("https://" + str + "/DocImage.ashx?type=CLEARCACHE&ddlID=" + id);
                    WebResponse response = request.GetResponse();
                    request = null;
                    response = null;
                }
            }
            catch(Exception)
            {
            }
            //StreamReader reader = new StreamReader(response.GetResponseStream());
            //string urlText = reader.ReadToEnd(); // it takes the response from your url. now you can use as your need 
        }
        public static void ClearFormCacheByParam(string strDrpdownId,string strDropDownName,int? clientNo)
        {
            try
            {
                string[] strArray = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["WebFormURL"]).Split(',');
                foreach (string str in strArray)
                {
                    WebRequest request = HttpWebRequest.Create("https://" + str + "/DocImage.ashx?type=CLEARCACHE&ddlID=" + strDrpdownId + "&dropdown=" + strDropDownName + "&clientNo=" + clientNo);
                    WebResponse response = request.GetResponse();
                    request = null;
                    response = null;
                }
            }
            catch
            {
            }
            //StreamReader reader = new StreamReader(response.GetResponseStream());
            //string urlText = reader.ReadToEnd(); // it takes the response from your url. now you can use as your need 
        }
        public static void ClearAllCacheByDropDown(string ddlId)
        {
            List<string> cacheItemsToRemove = new List<string>();
            string key = null;
            //loop through all cache items
            if (!string.IsNullOrEmpty(ddlId))
            {
                foreach (DictionaryEntry c in System.Web.HttpContext.Current.Cache)
                {
                    key = (string)c.Key;
                    if (key.StartsWith(("ddl" + ddlId)))
                    {
                        cacheItemsToRemove.Add(key);
                    }
                }
            }
            //remove the selected cache items
            foreach (var k in cacheItemsToRemove)
            {
                System.Web.HttpContext.Current.Cache.Remove(k);
            }
        }

		public static bool HasNumbericValue(int? number)
		{
			if (number == null) return false;
			return number.Value > 0;
		}

		public static string StripAlphabet(string input)
		{
			return Regex.Replace(input, "[^a-zA-Z]", "");
        }
	}

}