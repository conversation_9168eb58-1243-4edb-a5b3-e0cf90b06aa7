using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CompanyGlobalSalesPDetails : Base {

		#region Locals

		private FlexiDataTable _tbl;
		private IconButton _ibtnAdd;
		private IconButton _ibtnEdit;
		private IconButton _ibtnDelete;

		#endregion

		#region Properties

		private int? _intSupplierID = -1;
		public int? SupplierID {
			get { return _intSupplierID; }
			set { _intSupplierID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CompanyGlobalSalesPDetails.CompanyGlobalSalesPDetails.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CompanyGlobalSalesPDetails");
			_intSupplierID = _objQSManager.CompanyID;
			SetupTable();
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnEdit.Visible = _blnCanEdit;
			_ibtnDelete.Visible = _blnCanDelete;
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// sets up the list of contacts table
		/// </summary>
		private void SetupTable() {
			_tbl.AllowSelection = true;
			_tbl.AllowMultipleSelection = false;
			_tbl.Columns.Add(new FlexiDataColumn("Name", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName)));
			_tbl.Columns.Add(new FlexiDataColumn("Client"));
		}

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyGlobalSalesPDetails", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intSupplierID", _intSupplierID);
			_scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_tbl = (FlexiDataTable)ctlDesignBase.FindContentControl("tbl");
			_ibtnAdd = (IconButton)FindIconButton("ibtnAdd");
			_ibtnEdit = (IconButton)FindIconButton("ibtnEdit");
			_ibtnDelete = (IconButton)FindIconButton("ibtnDelete");
		}


	}
}
