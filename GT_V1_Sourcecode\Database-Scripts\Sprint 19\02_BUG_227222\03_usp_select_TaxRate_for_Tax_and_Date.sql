﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-227222]	Ngai To				10-Jan-2025		Update			227222: [PROD Bug] Master Taxes - Display incorrect breadcrumb navigation and incorrect data in the "Current Rate" and "Current Rate 2" columns
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_TaxRate_for_Tax_and_Date]
	--
	@TaxNo INT,
	@ClientNo INT,
	@TaxPoint DATETIME = NULL,
	@IsGlobalTax BIT = 0
	--
AS
--
IF @TaxPoint IS NULL
BEGIN
	SET @TaxPoint = current_timestamp
END

IF @IsGlobalTax = 0
BEGIN
	SELECT dbo.ufn_get_taxrate(@TaxNo, @ClientNo, @TaxPoint) AS CurrentTaxRate
END
ELSE
BEGIN
	SELECT dbo.ufn_get_master_taxrate(@TaxNo, @ClientNo, @TaxPoint, @IsGlobalTax) AS CurrentTaxRate
END
GO


