﻿//-----------------------------------------------------------------------------------------
// RP 28.10.2009:
// - Remove "no value"
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class Year : Base {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("Year");
            AddScriptReference("Controls.DropDowns.Year.Year");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.Year", ClientID);
			base.OnLoad(e);
		}

	}
}