﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210625]			Phuc Hoang			09-Aug-2024		CREATE          Sanctioned manufacturers need to be highlighted in red on the SOR PDF and SO screens.
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_SalesOrderLine_ReportPO]                      
--************************************************************************************                      
--* GA 25.10.2011                      
--* - Landed cost calculation edited                      
--* RP 15.02.2010:                      
--* - add ROHS                      
--*                        
--* SK 04.11.2009:                      
--* - include SupplierPart                      
--*                        
--* SK 01.10.2009:                      
--* - include SupplierType                      
--*                       
--* SK 30.06.2009:                      
--* - adjusted to use current date to calculate landed cost                      
--************************************************************************************                      
    @SalesOrderLineId int                      
AS -- DECLARE @dt datetime                      
-- SET @dt = CURRENT_TIMESTAMP                      
                      
SELECT  sol.SalesOrderLineId                      
          , sol.SalesOrderNo                      
          , al.StockNo                      
          , al.QuantityAllocated                      
          , pol.Part                      
          , pol.ManufacturerNo                      
          , mf.ManufacturerName                      
          , mf.ManufacturerCode                      
          , pol.DateCode                      
          , isnull(ipo.CurrencyNo, po.CurrencyNo) as CurrencyNo        
          , isnull(cup.CurrencyCode, cu.CurrencyCode) as CurrencyCode        
          , isnull(cup.CurrencyDescription, cu.CurrencyDescription ) as CurrencyDescription        
          , pol.PurchaseOrderLineId AS PurchaseOrderLineNo                      
          , isnull(ipol.Price, pol.Price) AS PurchasePrice                      
          , pol.ProductNo                      
          , pr.ProductName     
    , pr.ProductDescription                     
          , pol.PurchaseOrderNo                      
         -- ,  po.PurchaseOrderNumber                      
   , isnull(ipo.InternalPurchaseOrderNumber, po.PurchaseOrderNumber ) as PurchaseOrderNumber        
          , po.CompanyNo AS SupplierNo                      
          , cm.CompanyName AS SupplierName                      
          --, cn.CountryName          
           -- , ISNULL(rg.RegionName , cn.CountryName ) as CountryName                  
             , case when ipo.InternalPurchaseOrderId  is null then cn.CountryName else '' end as CountryName        
          , isnull(cnipo.Duty, cn.Duty) as Duty                      
          , case isnull(cnipo.Duty, cn.Duty)                     
              WHEN 1 THEN dbo.ufn_get_productdutyrate(pol.ProductNo, pol.DeliveryDate)                      
              ELSE 0                      
            END AS DutyRate                      
          , Case when sk.QuantityInStock = 0 then (isnull(dbo.ufn_calculateLandedCost(al.QuantityAllocated, getdate(),                    
           po.CurrencyNo, pol.Price, isnull((pol.ShipInCost / pol.Quantity * al.QuantityAllocated), 0),                    
            cn.Duty, pol.ProductNo), pol.Price)) else sk.LandedCost end AS LandedCost            
                              
          , Case when sk.QuantityInStock = 0 then (isnull(dbo.ufn_calculateLandedCost(al.QuantityAllocated, getdate(),                    
           ipo.CurrencyNo, ipol.Price, isnull((pol.ShipInCost / pol.Quantity * al.QuantityAllocated), 0),                    
            cnipo.Duty, ipol.ProductNo), ipol.Price)) else sk.ClientLandedCost end AS  ClientLandedCost         
                    
           --  , Case when sk.QuantityInStock = 0 then (isnull(dbo.ufn_calculateLandedCost(al.QuantityAllocated, getdate(),                    
           --ipo.CurrencyNo, ipol.Price, isnull((pol.ShipInCost / pol.Quantity * al.QuantityAllocated), 0),                    
           -- cn.Duty, pol.ProductNo), ipol.Price)) else sk.ClientLandedCost end AS  ClientLandedCost         
                     
         -- , pol.DeliveryDate                      
          , isnull(pol.PromiseDate,pol.DeliveryDate) as  DeliveryDate                     
, po.TermsNo                      
          , tm.TermsName                      
          , cast(pol.Taxable AS char(1)) AS Taxable                      
          , pol.Quantity AS QuantitySupplied                      
          , isnull((pol.ShipInCost / pol.Quantity * al.QuantityAllocated), 0) AS ShipInCost                      
          , po.DateOrdered             
          , ct.Name AS SupplierType                      
          , pol.SupplierPart                      
          , pol.ROHS                      
          , pol.POSerialNo                   
          ,isnull(po.InternalPurchaseOrderNo,0) as InternalPurchaseOrderNo          
    -- ,cm.CompanyName as IPOSupplierName        
         -- ,ct.Name  as IPOSupplierType            
          --,(        
                  
--          select                       
--   co.CompanyName                   
--       from tbInternalPurchaseOrderLine ipol                   
--      Left Join tbSalesOrderLine sol on sol.SalesOrderLineId=ipol.SalesOrderLineNo                  
--left join tbInternalPurchaseOrder ipo on ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo                    
--left join tbCompany   co ON co.CompanyId = ipo.CompanyNo where sol.SalesOrderLineId=@SalesOrderLineId ) as IPOSupplierName              
 , cop.CompanyName as   IPOSupplierName          
  , ISNULL( po.DateOrdered,GETDATE()) as PODateOrdered        
--,(select                     
--        ct.Name                
--       from tbInternalPurchaseOrderLine ipol                 
--      Left Join tbSalesOrderLine sol on sol.SalesOrderLineId=ipol.SalesOrderLineNo                
--left join tbInternalPurchaseOrder ipo on ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo                  
--left join tbCompany   co ON co.CompanyId = ipo.CompanyNo                
-- LEFT JOIN dbo.tbCompanyType ct ON co.TypeNo = ct.CompanyTypeId                 
-- where sol.SalesOrderLineId=@SalesOrderLineId ) as IPOSupplierType           
  --, ctp.Name as  IPOSupplierType           
  , ct.Name as  IPOSupplierType          
   , isnull(pr.IsHazardous,0) as IsProdHazardous          
 ,isnull(pr.IsOrderViaIPOonly,0) as IsOrderViaIPOonly   
 ,sol.ECCNCode 
 ,isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0)  as IsECCNWarning
   , retr.ManufacturerNo AS RestrictedMfrNo
   , retr.Inactive AS RestrictedMfrInactive

    FROM    dbo.tbAllocation al                      
    JOIN    dbo.tbSalesOrderLine sol ON sol.SalesOrderLineId = al.SalesOrderLineNo                      
    JOIN    dbo.tbStock sk ON sk.StockId = al.StockNo                      
    LEFT JOIN dbo.tbPurchaseOrderLine pol ON pol.PurchaseOrderLineId = sk.PurchaseOrderLineNo                      
    LEFT JOIN dbo.tbPurchaseOrder po ON po.PurchaseOrderId = pol.PurchaseOrderNo                      
    LEFT JOIN dbo.tbProduct pr ON pr.ProductId = pol.ProductNo                      
    LEFT JOIN dbo.tbCurrency cu ON cu.CurrencyId = po.CurrencyNo                      
    LEFT JOIN dbo.tbCompany cm ON cm.CompanyId = po.CompanyNo                      
    LEFT JOIN dbo.tbCountry cn ON cn.CountryId = po.ImportCountryNo                      
    LEFT JOIN dbo.tbTerms tm ON tm.TermsId = po.TermsNo                      
    LEFT JOIN dbo.tbManufacturer mf ON mf.ManufacturerId = pol.ManufacturerNo              
    LEFT JOIN dbo.tbCompanyType ct ON cm.TypeNo = ct.CompanyTypeId          
           
    --LEFT JOIN dbo.tbInternalPurchaseOrderLine ipol on ipol.SalesOrderLineNo=sol.SalesOrderLineId        
    LEFT JOIN dbo.tbInternalPurchaseOrderLine ipol on ipol.PurchaseOrderLineNo=pol.PurchaseOrderLineId        
    LEFT JOIN dbo.tbInternalPurchaseOrder ipo on ipo.InternalPurchaseOrderId=ipol.InternalPurchaseOrderNo          
    LEFT JOIN dbo.tbCountry cnipo ON cnipo.CountryId = ipo.ImportCountryNo            
    LEFT JOIN tbRegion rg on ipo.RegionNo = rg.RegionId          
	LEFT JOIN dbo.tbCurrency cup ON cup.CurrencyId = ipo.CurrencyNo         
	LEFT JOIN tbCompany   cop ON cop.CompanyId = ipo.CompanyNo 
	LEFT JOIN tbSalesOrder so on so.SalesOrderId  = sol.SalesOrderNo    
	LEFT JOIN dbo.tbRestrictedManufacturer retr ON retr.ManufacturerNo = mf.ManufacturerId AND retr.ClientNo = so.ClientNo
 --LEFT JOIN dbo.tbCompanyType ctp ON cop.TypeNo = ctp.CompanyTypeId            
          
    WHERE   sol.SalesOrderLineId = @SalesOrderLineId                      
            AND sk.PurchaseOrderNo > 0                      
            AND sk.GoodsInLineNo IS NULL 


GO
