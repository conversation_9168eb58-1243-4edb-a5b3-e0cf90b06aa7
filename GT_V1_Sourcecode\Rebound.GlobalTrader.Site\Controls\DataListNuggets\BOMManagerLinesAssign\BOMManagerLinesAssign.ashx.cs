using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Text;
using System.IO;
using System.Xml;
using System.Text.RegularExpressions;
using System.Data.Common;
using System.Reflection;
using Rebound.GlobalTrader.Site.Code.Common;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class BOMManagerLinesAssign : Base
    {

        /// <summary>
        /// Gets the main data
        /// </summary>
        /// /// //Marker     changed by      date         Remarks
        //   [001]       <PERSON><PERSON>    04/09/2018    pass parameter as null because <PERSON><PERSON>erson parameter is passed in Assign HUBRFQ and same function is used and Export to csv button.
        //   [004]  Devendra <PERSON>  19-Dec-2023     RP-2629 (Line 759 added 3 parameters in Method ExportToCSV and  DataListNugget_Export)

        // start [001]    
        public override void ProcessRequest(HttpContext context)
        {
            //if (base.init(context))
            //{
            //    switch (Action)
            //    {
            //        case "ExportToCSV": ExportToCSV(); break;
            //        default: WriteErrorActionNotFound(); break;
            //    }
            //}
            base.ProcessRequest(context);
            if (Action == "ExportToCSV") ExportToCSV();
        }

        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {

                Boolean isSearchFromRequirements = false;
                int? bomStatus = GetFormValue_Int("BomStatus");
                int? assignedUser = GetFormValue_Int("PoHubBuyer");//BuyerId
                string Manufacture = GetFormValue_String("Manufacturer");
                string PartNumber = GetFormValue_String("Part");
                string selectedRadioValue = GetFormValue_String("SelectedRadio");//   [001]  
                DateTime? Startdate = GetFormValue_NullableDateTime("StartDate");//   [001]  
                DateTime? EndDate = GetFormValue_NullableDateTime("EndDate"); //   [001]  
                Boolean? isPoHUB = SessionManager.IsPOHub;
                int? salesPersonId = GetFormValue_NullableInt("SalesPerson", null);
                //  start [001]  
                if ((!string.IsNullOrEmpty(selectedRadioValue) && selectedRadioValue == "Detail"))
                {
                    isSearchFromRequirements = true;
                }
                //  end [001]
             
                // [004] CODE START -- DEVENDRA SINGH
                int? CompanyTypeid = GetFormValue_NullableInt("CompanyType", null);
                int? AS6081Required = GetFormValue_Int("AS6081Required");
                //  [004] CODE END -- DEVENDRA SINGH

                ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
                string filePath = string.Empty;
                string strFilename = String.Format("report_u{0}r{1}.xlsx", LoginID, 0);
                if (isSearchFromRequirements == false)
                {

                    DataTable dtResult = BLL.BOM.DataListNugget_Export(
                        SessionManager.ClientID
                        , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                        , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                        , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                        , GetFormValue_NullableInt("SortIndex")
                        , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                        , GetFormValue_StringForLikeSearch("Code", true)
                        , GetFormValue_StringForLikeSearch("Name", true)
                        , Convert.ToBoolean(SessionManager.IsPOHub)
                        , Convert.ToBoolean(SessionManager.IsPOHub) ? GetFormValue_NullableInt("Client", null) : null
                        , bomStatus
                        , 0//Check for assign to me(0->No,1->Yes)
                        , assignedUser
                        , GetFormValue_NullableInt("Division", null)
                        , salesPersonId//[001]
                        , Startdate
                        , EndDate
                    // [004] CODE START -- DEVENDRA SINGH 
                       , CompanyTypeid
                        , AS6081Required
                        , SessionManager.LoginID
                    //  [004] CODE END -- DEVENDRA SINGH
                    );
                    filePath = (new EPPlusExportUtility()).ExportDataTableToCSV(dtResult, strFilename, "HUBRFQResult");
                }
                else
                {
                    //get data
                    DataTable dtResult = CustomerRequirement.DataListNuggetHUBRFQ_Export(
                        isPoHUB == true ? null : SessionManager.ClientID
                        , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                        , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                        , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                        , GetFormValue_NullableInt("SortIndex", 0)
                        , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                        , GetFormValue_StringForLikeSearch("Code", true)
                        , GetFormValue_StringForLikeSearch("Name", true)
                         , Convert.ToBoolean(SessionManager.IsPOHub)
                        , Convert.ToBoolean(SessionManager.IsPOHub) ? GetFormValue_NullableInt("Client", null) : null
                        , bomStatus
                        , 0
                        , assignedUser
                        , GetFormValue_String("Manufacturer")
                        , GetFormValue_PartForLikeSearch("Part")
                        , GetFormValue_NullableInt("Division", null)
                        , Startdate//   [001]
                        , EndDate
                        , salesPersonId
                    );
                    filePath = (new EPPlusExportUtility()).ExportDataTableToCSV(dtResult, strFilename, "HUBRFQResult");


                }
                //string strFilename = String.Format("report_u{0}r{1}.csv", LoginID, 0);
                //File.WriteAllText(String.Format("{0}/{1}", FileUploadManager.GetCSVFilePath_Absolute(), strFilename), sbCSV.ToString());

                //return saved filename to the page
                jsn.AddVariable("Filename", filePath);
                OutputResult(jsn);

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }



        private string FormatTextForCSV(string strIn)
        {
            if (!string.IsNullOrEmpty(strIn) && strIn.Contains(","))
                strIn = strIn.Replace(",", "");
            return strIn;
        }

        private string BlankCSVLine()
        {
            return string.Format(@"{0}", System.Environment.NewLine);
        }

        // end [001]   

        protected override void GetData()
        {
            Boolean isSearchFromRequirements = false;
            JsonObject jsn = new JsonObject();
            JsonObject jsnRowsArray = new JsonObject(true);
            string Code = GetFormValue_String("Code");
            int? bomStatus = GetFormValue_Int("BomManagerStatus");
            int? assignedUser = GetFormValue_Int("PoHubBuyer");//BuyerId
            string Name = GetFormValue_String("Name");
            string Manufacture = GetFormValue_String("Manufacturer");
            string Part = GetFormValue_String("Part");
            string selectedRadioValue = GetFormValue_String("SelectedRadio");//   [001]  
            DateTime? Startdate = GetFormValue_NullableDateTime("StartDate");//   [001]  
            DateTime? EndDate = GetFormValue_NullableDateTime("EndDate"); //   [001]  
            Boolean? isPoHUB = SessionManager.IsPOHub;
            int? salesPersonId = GetFormValue_NullableInt("SalesPerson", null);
            //  start [001]  
            //if ((!string.IsNullOrEmpty(selectedRadioValue) && selectedRadioValue == "Detail") || (!string.IsNullOrEmpty(Manufacture)) || (!string.IsNullOrEmpty(PartNumber)) || (Startdate != null) || (EndDate != null))
            //{
            //    isSearchFromRequirements = true;
            //}
            if ((!string.IsNullOrEmpty(selectedRadioValue) && selectedRadioValue == "Detail"))
            {
                isSearchFromRequirements = true;
            }
            //  end [001]  


            //if (!string.IsNullOrEmpty(Manufacture))
            //{
            //    isSearchFromRequirements = true;
            //}

            //if (!string.IsNullOrEmpty(PartNumber))
            //{
            //    isSearchFromRequirements = true;
            //}


            //check view level

            ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");
            if (isSearchFromRequirements == false)
            {

                List<Rebound.GlobalTrader.BLL.BOMManagerContract> lst = Rebound.GlobalTrader.BLL.BOMManagerContract.DataListNuggetOLD_ForAssign(
                    SessionManager.ClientID
                    , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                    , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                    , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                    , GetFormValue_NullableInt("SortIndex")
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                    //, GetFormValue_NullableInt("CodeLo")
                    //, GetFormValue_NullableInt("CodeHi")
                    ////, GetFormValue_NullableInt("Code", null)
                    , GetFormValue_StringForLikeSearch("Code", true)
                    //, GetFormValue_StringForLikeSearch("Name", true)
                    , GetFormValue_StringNewForSearch("Name")
                    , Convert.ToBoolean(SessionManager.IsPOHub)
                    , Convert.ToBoolean(SessionManager.IsPOHub) ? GetFormValue_NullableInt("Client", null) : null
                    , bomStatus
                    , 0//Check for assign to me(0->No,1->Yes)
                    , assignedUser
                    , GetFormValue_NullableInt("Division", null)
                    , salesPersonId//[001]
                    , GetFormValue_NullableDateTime("StartDate")//   [001]
                    , GetFormValue_NullableDateTime("EndDate")//
                    , GetFormValue_String("Manufacturer")
                    //, GetFormValue_PartForLikeSearch("Part")

                );
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
                foreach (BLL.BOMManagerContract lt in lst)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lt.BOMManagerId);
                    jsnRow.AddVariable("Code", lt.BOMManagerCode);
                    jsnRow.AddVariable("Name", lt.BOMManagerName);
                    jsnRow.AddVariable("Company", lt.CompanyName);
                    jsnRow.AddVariable("CompanyNo", lt.CompanyNo);
                    jsnRow.AddVariable("Inactive", lt.Inactive);
                    jsnRow.AddVariable("BOMStatus", lt.BOMManagerStatus);
                    jsnRow.AddVariable("PromiseDate", Functions.FormatDate(lt.DatePromised));
                    jsnRow.AddVariable("ReceivedDate", Functions.FormatDate(lt.ReceivedDate));
                    jsnRow.AddVariable("Salesman", lt.SalesmanName);
                    jsnRow.AddVariable("Contact", lt.ContactName);
                    jsnRow.AddVariable("ContactNo", lt.ContactNo);
                    if (SessionManager.IsPOHub == true)
                    {


                        jsnRow.AddVariable("TotalValue", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(lt.TotalBomManagerLinePrice, (int)SessionManager.ClientCurrencyID, (lt.DateRequestToPOHub.HasValue) ? lt.DateRequestToPOHub.Value : lt.DLUP), SessionManager.ClientCurrencyCode, 2));
                    }
                    else
                    {
                        jsnRow.AddVariable("TotalValue", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(lt.TotalBomManagerLinePrice, (int)lt.POCurrencyNo, (lt.DateRequestToPOHub.HasValue) ? lt.DateRequestToPOHub.Value : lt.DLUP), SessionManager.ClientCurrencyCode, 2));
                    }
                    jsnRow.AddVariable("AssignedUser", lt.AssignedUser);
                    jsnRow.AddVariable("isPoHUB", SessionManager.IsPOHub);
                    jsnRow.AddVariable("DivName", lt.DivisionName);
                    jsnRowsArray.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            else
            {
                //get data
                List<CustomerRequirement> lst = CustomerRequirement.DataListNuggetHUBRFQBOMManager(
                    isPoHUB == true ? null : SessionManager.ClientID
                    , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                    , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                    , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                    , GetFormValue_NullableInt("SortIndex", 0)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)

                    , GetFormValue_StringForLikeSearch("Code", true)
                    //, GetFormValue_StringForLikeSearch("Name", true)
                    , GetFormValue_StringNewForSearch("Name")

                     , Convert.ToBoolean(SessionManager.IsPOHub)
                    , Convert.ToBoolean(SessionManager.IsPOHub) ? GetFormValue_NullableInt("Client", null) : null
                    , bomStatus
                    , 0
                    , assignedUser
                    , GetFormValue_String("Manufacturer")
                    , GetFormValue_PartForLikeSearch("Part")
                    , GetFormValue_NullableInt("Division", null)
                    , GetFormValue_NullableDateTime("StartDate")//   [001]
                    , GetFormValue_NullableDateTime("EndDate")//   [001]
                    , salesPersonId
                    ,null
                );

                //check counts
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

                //format data
                //  JsonObject jsnRowsArray = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ID", lst[i].BOMNo);
                        jsnRow.AddVariable("Code", lst[i].BOMCode);
                        //jsnRow.AddVariable("ID", lst[i].CustomerRequirementId);
                        //jsnRow.AddVariable("No", lst[i].CustomerRequirementNumber);
                        jsnRow.AddVariable("Part", Functions.GetPartWithAlternate(lst[i].Part, lst[i].AlternateStatus, lst[i].Alternate));
                        jsnRow.AddVariable("ROHS", lst[i].ROHS);
                        jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
                        jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
                        jsnRow.AddVariable("Quantity", lst[i].Quantity);
                        jsnRow.AddVariable("CM", lst[i].CompanyName);
                        jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                        jsnRow.AddVariable("Contact", lst[i].ContactName);
                        jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
                        jsnRow.AddVariable("Salesman", lst[i].SalesmanName);
                        jsnRow.AddVariable("Received", Functions.FormatDate(lst[i].ReceivedDate));
                        jsnRow.AddVariable("Promised", Functions.FormatDate(lst[i].DatePromised));
                        jsnRow.AddVariable("BOMCode", lst[i].BOMCode);
                        jsnRow.AddVariable("BOMNo", lst[i].BOMNo);
                        jsnRow.AddVariable("BOMName", lst[i].BOMName);
                        jsnRow.AddVariable("isPoHUB", isPoHUB);
                        jsnRow.AddVariable("Price", lst[i].BOMName);
                        jsnRow.AddVariable("BOMStatus", lst[i].BOMStatus);
                        jsnRow.AddVariable("DivName", lst[i].DivisionName);
                        jsnRow.AddVariable("SupportTeamMemberNo", lst[i].SupportTeamMemberNo);
                        jsnRow.AddVariable("SupportTeamMember", lst[i].SupportTeamMemberName);

                        if (SessionManager.IsPOHub == true)
                        {

                            jsnRow.AddVariable("TotalValue", Functions.FormatCurrency(lst[i].PHPrice * lst[i].Quantity, SessionManager.ClientCurrencyCode, 2));// Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(lst[i].Price * lst[i].Quantity, (int)SessionManager.ClientCurrencyID, (lst[i].DateRequestToPOHub.HasValue) ? lst[i].DateRequestToPOHub.Value : lst[i].DLUP), SessionManager.ClientCurrencyCode));
                        }
                        else
                        {
                            jsnRow.AddVariable("TotalValue", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(lst[i].Price * lst[i].Quantity, (int)lst[i].POCurrencyNo, (lst[i].DateRequestToPOHub.HasValue) ? lst[i].DateRequestToPOHub.Value : lst[i].DLUP), SessionManager.ClientCurrencyCode, 2));
                        }
                        jsnRowsArray.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
            }
            jsn.AddVariable("Results", jsnRowsArray);
            jsn.AddVariable("isSearchFromRequirements", isSearchFromRequirements);
            jsn.AddVariable("SortIndex", GetFormValue_NullableInt("SortIndex", 0));
            jsn.AddVariable("SortDir", GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC));
            jsn.AddVariable("PageIndex", GetFormValue_NullableInt("PageIndex", 0));
            jsn.AddVariable("PageSize", GetFormValue_NullableInt("PageSize", 10));
            jsn.AddVariable("Code", GetFormValue_StringForLikeSearch("Code", true));
            jsn.AddVariable("Name", GetFormValue_StringForLikeSearch("Name", true));
            jsn.AddVariable("SalesPerson", salesPersonId);
            OutputResult(jsn);
            jsnRowsArray.Dispose();
            jsnRowsArray = null;
            jsn.Dispose();
            jsn = null;
            base.GetData();
        }

        protected override void AddFilterStates()
        {
            AddFilterState("Code");
            AddFilterState("Name");
            AddFilterState("Manufacturer");
            AddFilterState("Part");
            base.AddFilterStates();
        }
    }
}
