﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-216162]    cuongdx			31-OCT-2024			Create		Create table for loggings
===========================================================================================  
*/  
IF OBJECT_ID('dbo.tbProspectiveOfferLogs', 'U') IS NOT NULL
BEGIN
    DROP TABLE dbo.tbProspectiveOfferLogs;
END

CREATE TABLE dbo.tbProspectiveOfferLogs (
	ProspectiveOfferLogsId INT IDENTITY(1,1) PRIMARY KEY,
    ProspectiveOfferLineNo INT,
	CustomerRequirementNo Int,
	SentDate DateTime,
    DLUP DATETIME
    -- Add other columns as needed
);

ALTER TABLE [dbo].tbProspectiveOfferLogs ADD  DEFAULT (getdate()) FOR [DLUP]
GO