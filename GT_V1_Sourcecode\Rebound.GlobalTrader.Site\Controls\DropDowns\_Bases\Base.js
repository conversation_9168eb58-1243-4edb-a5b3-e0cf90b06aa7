Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Base=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Base.initializeBase(this,[n]);this._strNoValue_Value="";this._strNoValue_Text="";this._varCurrentValue=null;this._intRefreshTimeout=0;this._blnFirstTimeIn=!0;this._strDataPathModification="";this._aryExtraText=[];this._blnInDataCall=!1;this._varOriginalInitialValue;this._objData};Rebound.GlobalTrader.Site.Controls.DropDowns.Base.prototype={get_lbx:function(){return this._lbx},set_lbx:function(n){this._lbx!==n&&(this._lbx=n)},get_strDataPathModification:function(){return this._strDataPathModification},set_strDataPathModification:function(n){this._strDataPathModification!==n&&(this._strDataPathModification=n)},get_hypAdd:function(){return this._hypAdd},set_hypAdd:function(n){this._hypAdd!==n&&(this._hypAdd=n)},get_pnlAddForm:function(){return this._pnlAddForm},set_pnlAddForm:function(n){this._pnlAddForm!==n&&(this._pnlAddForm=n)},get_txtAdd:function(){return this._txtAdd},set_txtAdd:function(n){this._txtAdd!==n&&(this._txtAdd=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnCancel:function(){return this._ibtnCancel},set_ibtnCancel:function(n){this._ibtnCancel!==n&&(this._ibtnCancel=n)},get_blnCanAdd:function(){return this._blnCanAdd},set_blnCanAdd:function(n){this._blnCanAdd!==n&&(this._blnCanAdd=n)},get_blnCanRefresh:function(){return this._blnCanRefresh},set_blnCanRefresh:function(n){this._blnCanRefresh!==n&&(this._blnCanRefresh=n)},get_initialValue:function(){return this._initialValue},set_initialValue:function(n){this._initialValue!==n&&(this._initialValue=n)},get_pnlFormInner:function(){return this._pnlFormInner},set_pnlFormInner:function(n){this._pnlFormInner!==n&&(this._pnlFormInner=n)},get_pnlAddError:function(){return this._pnlAddError},set_pnlAddError:function(n){this._pnlAddError!==n&&(this._pnlAddError=n)},get_pnlAddSaving:function(){return this._pnlAddSaving},set_pnlAddSaving:function(n){this._pnlAddSaving!==n&&(this._pnlAddSaving=n)},get_hypRefresh:function(){return this._hypRefresh},set_hypRefresh:function(n){this._hypRefresh!==n&&(this._hypRefresh=n)},get_blnIncludeNoValue:function(){return this._blnIncludeNoValue},set_blnIncludeNoValue:function(n){this._blnIncludeNoValue!==n&&(this._blnIncludeNoValue=n)},get_strNoValue_Value:function(){return this._strNoValue_Value},set_strNoValue_Value:function(n){this._strNoValue_Value!==n&&(this._strNoValue_Value=n)},get_strNoValue_Text:function(){return this._strNoValue_Text},set_strNoValue_Text:function(n){this._strNoValue_Text!==n&&(this._strNoValue_Text=n)},get_blnGetDataStraightAway:function(){return this._blnGetDataStraightAway},set_blnGetDataStraightAway:function(n){this._blnGetDataStraightAway!==n&&(this._blnGetDataStraightAway=n)},get_intDropDownID:function(){return this._intDropDownID},set_intDropDownID:function(n){this._intDropDownID!==n&&(this._intDropDownID=n)},get_varCurrentValue:function(){return this._varCurrentValue},set_varCurrentValue:function(n){this._varCurrentValue!==n&&(this._varCurrentValue=n)},get_intCurrentLogin:function(){return this._intCurrentLogin},set_intCurrentLogin:function(n){this._intCurrentLogin!==n&&(this._intCurrentLogin=n)},addSetupDataCall:function(n){this.get_events().addHandler("SetupDataCall",n)},removeSetupDataCall:function(n){this.get_events().removeHandler("SetupDataCall",n)},onSetupDataCall:function(){var n=this.get_events().getHandler("SetupDataCall");n&&n(this,Sys.EventArgs.Empty)},addGetDataOK:function(n){this.get_events().addHandler("GetDataOK",n)},removeGetDataOK:function(n){this.get_events().removeHandler("GetDataOK",n)},onGetDataOK:function(){var n=this.get_events().getHandler("GetDataOK");n&&n(this,Sys.EventArgs.Empty)},addChanged:function(n){this.get_events().addHandler("Changed",n)},removeChanged:function(n){this.get_events().removeHandler("Changed",n)},onChanged:function(){var n=this.get_events().getHandler("Changed");n&&n(this,Sys.EventArgs.Empty)},addShowAddForm:function(n){this.get_events().addHandler("ShowAddForm",n)},removeShowAddForm:function(n){this.get_events().removeHandler("ShowAddForm",n)},onShowAddForm:function(){var n=this.get_events().getHandler("ShowAddForm");n&&n(this,Sys.EventArgs.Empty)},addAdd:function(n){this.get_events().addHandler("Add",n)},removeAdd:function(n){this.get_events().removeHandler("Add",n)},onAdd:function(){var n=this.get_events().getHandler("Add");n&&n(this,Sys.EventArgs.Empty)},addRefresh:function(n){this.get_events().addHandler("Refresh",n)},removeRefresh:function(n){this.get_events().removeHandler("Refresh",n)},onRefresh:function(){var n=this.get_events().getHandler("Refresh");n&&n(this,Sys.EventArgs.Empty)},addGotDataComplete:function(n){this.get_events().addHandler("GotDataComplete",n)},removeGotDataComplete:function(n){this.get_events().removeHandler("GotDataComplete",n)},onGotDataComplete:function(){var n=this.get_events().getHandler("GotDataComplete");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){this._blnCanRefresh&&$addHandler(this._hypRefresh,"click",Function.createDelegate(this,this.refresh));this._blnCanAdd&&($addHandler(this._hypAdd,"click",Function.createDelegate(this,this.showAddForm)),$R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.onAdd)),$R_IBTN.addClick(this._ibtnCancel,Function.createDelegate(this,this.cancelAdd)));$addHandler(this._lbx,"change",Function.createDelegate(this,this.changeDropDown));this._blnFirstTimeIn=!0;this._varOriginalInitialValue=this._initialValue;this.addRefresh(Function.createDelegate(this,this.getData));this._blnGetDataStraightAway&&this.getData();Rebound.GlobalTrader.Site.Controls.DropDowns.Base.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._lbx&&$clearHandlers(this._lbx),this._hypAdd&&$clearHandlers(this._hypAdd),this._hypRefresh&&$clearHandlers(this._hypRefresh),this._objData&&this._objData.dispose(),this._strNoValue_Value=null,this._strNoValue_Text=null,this._intRefreshTimeout=null,this._blnFirstTimeIn=null,this._strDataPathModification=null,this._aryExtraText=null,this._blnInDataCall=null,this._varOriginalInitialValue=null,this._objData=null,this._lbx=null,this._hypAdd=null,this._pnlAddForm=null,this._txtAdd=null,this._ibtnAdd=null,this._ibtnCancel=null,this._pnlFormInner=null,this._pnlAddError=null,this._pnlAddSaving=null,this._hypRefresh=null,this._objData=null,this._varCurrentValue=null,this._intCurrentLogin=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Base.callBaseMethod(this,"dispose"),this.isDisposed=!0)},getData:function(){this.showLoading();this.cancelDataCall();this._blnInDataCall=!0;this._objData=new Rebound.GlobalTrader.Site.Data;this._objData.addDataOK(Function.createDelegate(this,this.getDataOK));this._objData.addError(Function.createDelegate(this,this.getDataError));this._objData.addTimeout(Function.createDelegate(this,this.getDataError));this._objData.addParameter("DDID",this._intDropDownID);this._objData.addParameter("cLogIn",this._intCurrentLogin);var n=new Date;this._objData.addParameter("DateTime",n.toISOString());this.onSetupDataCall();$R_DQ.addToQueue(this._objData,!0);$R_DQ.processQueue()},getDataOK:function(){$R_FN.clearListBox(this._lbx);this._blnIncludeNoValue&&this.addNoValueOption();this.onGetDataOK();this._blnFirstTimeIn?(this.finishSetValue(this._initialValue),this._blnFirstTimeIn=!1):this.finishSetValue(this._varCurrentValue);this._blnRefreshEvent&&(this._blnRefreshEvent=!1,this._lbx.selectedIndex=this._intIndexToSet,this.setValue(this._lbx.options[this._intIndexToSet].value));this._blnInDataCall=!1;this.onGotDataComplete()},getDataError:function(n){this.showError(n)},setValue:function(n,t){this.setInitialValue(n,t);this._blnFirstTimeIn=!1;this._varCurrentValue=n;this._lbx.options.length>0&&!this._blnInDataCall&&this.finishSetValue(n)},finishSetValue:function(n){this._varCurrentValue=n;for(var t=0,i=this._lbx.options.length;t<i;t++)this._lbx.options[t].selected=this._lbx.options[t].value==n},changeDropDown:function(){this._varCurrentValue=this._lbx.value;this.onChanged()},setValueFromText:function(n){for(var t=0,i=this._lbx.options.length;t<i;t++)this._lbx.options[t].selected=this._lbx.options[t].text.toString().trim().toUpperCase()==n.toString().trim().toUpperCase()},setInitialValue:function(n,t){this._blnFirstTimeIn=!0;(n===""||n==null)&&(n=this._blnIncludeNoValue?this._strNoValue_Value:this._varOriginalInitialValue);this._initialValue=n;this._strInitialText=t},getValue:function(){return this.isSetAsNoValue()?null:typeof this._lbx.value=="undefined"?null:this.isLoading()?null:this._varCurrentValue},getText:function(){return this.isSetAsNoValue()?"":this._lbx.options[this._lbx.selectedIndex].text},getExtraText:function(){var n=this._aryExtraText[this._lbx.selectedIndex];return typeof n=="undefined"&&(n=""),n.trim()},addNoValueOption:function(){this._blnIncludeNoValue&&($R_FN.addOptionToListBox(this._lbx,this._strNoValue_Text,this._strNoValue_Value,!0),Array.add(this._aryExtraText,""))},addOption:function(n,t,i){var r=!1;this._strInitialText&&(r=this._strInitialText.toString().trim().toUpperCase()==n.toString().trim().toUpperCase());this._initialValue&&(r=this._initialValue==t);$R_FN.addOptionToListBox(this._lbx,$R_FN.setCleanTextValue(n),t,r);(typeof i=="undefined"||i==null)&&(i="");Array.add(this._aryExtraText,i.toString())},showLoading:function(){$R_FN.clearListBox(this._lbx);Array.clear(this._aryExtraText);$R_FN.addOptionToListBox(this._lbx,String.format("< {0} >",$R_RES.Loading.toLowerCase()),"::loading::",!0)},showAddForm:function(){this._blnFormShown||($R_FN.showElement(this._pnlAddForm,!0),this.onShowAddForm(),this._blnFormShown=!0)},hideAddForm:function(){this._blnFormShown&&($R_FN.showElement(this._pnlAddForm,!1),this._blnFormShown=!1)},showError:function(n){var t=$R_RES.Error;n!=null&&typeof n!="undefined"&&(t=n._errorMessage);$R_FN.clearListBox(this._lbx);$R_FN.addOptionToListBox(this._lbx,String.format("< {0} >",$R_RES.Error.toLowerCase()),t,!0);this._lbx.options[0].className="error"},cancelAdd:function(){this.hideAddForm()},clearDropDown:function(){this._strInitialText=null;this._initialValue=null;Array.clear(this._aryExtraText);this.setToNoValue()},setToNoValue:function(){this.setValue(this._strNoValue_Value)},isSetAsNoValue:function(){return this._lbx.value==this._strNoValue_Value},isDataAlreadyGiven:function(n){for(var i=!1,t=0,r=this._lbx.options.length;t<r;t++)if(n.toString().trim()==this._lbx.options[t].text.toString().trim()){i=!0;break}return i},isIDAlreadyGiven:function(n){for(var i=!1,t=0,r=this._lbx.options.length;t<r;t++)if(n.toString().trim()==this._lbx.options[t].value.toString().trim()){i=!0;break}return i},showAddDataError:function(n){$R_FN.showElement(this._pnlAddSaving,!1);$R_FN.showElement(this._pnlAddError,!0);$R_FN.showElement(this._pnlFormInner,!1);$R_FN.setInnerHTML(this._pnlAddError,n._errorMessage)},refresh:function(){this._blnInDataCall||(this._blnRefreshEvent=!0,this._intIndexToSet=this._lbx.selectedIndex,this.showLoading(),this.onRefresh())},reset:function(){this._lbx.value=this._initialValue},countOptions:function(){var n=this._lbx.options.length;return this._blnIncludeNoValue&&(n-=1),n},cancelDataCall:function(){this._blnInDataCall=!1;this._objData&&this._objData.cancel()},isLoading:function(){return this._blnInDataCall}};Rebound.GlobalTrader.Site.Controls.DropDowns.Base.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Base",Sys.UI.Control,Sys.IDisposable);