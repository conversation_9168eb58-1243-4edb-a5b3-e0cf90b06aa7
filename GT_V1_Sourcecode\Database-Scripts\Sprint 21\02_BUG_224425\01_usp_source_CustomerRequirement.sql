/*   
===========================================================================================  
[US-224425]  CuongDoX   4-Feb-2025  UPDATE   [PROD Bug] Sourcing - Historic Data is shown incorrectly for multiple Part No. searching  
===========================================================================================  
*/  
CREATE OR ALTER PROCEDURE [dbo].[usp_source_CustomerRequirement]                    
--********************************************************************************************                      
--* RP 09.03.2011:                      
--* - add recompile option                      
--*                      
--* RP 25.05.2010:                      
--* - remove UNIONS, process Clients in code                      
--* - add Package, Product, CustomerPart, DateCode                      
--*                      
--* SK 20.01.2010:                      
--* - add ClientId to parameters and predicate: if equal display data as now, if not show                        
--*   ClientName as customer  - with no hyperlink - and do not show any price                        
--*                      
--* RP 01.06.2009:                      
--* - add search on CustomerPart                      
--*                      
--* SK 01.06.2009:                      
--* - add order by clause                      
--********************************************************************************************                          
    @ClientId INT                      
  , @PartSearch NVARCHAR(50) = '%%'                      
  , @Index int=1                
  , @StartDate datetime = NULL                      
  , @FinishDate datetime = NULL                               
--WITH RECOMPILE  
AS                      
 --DECLARE VARIABLE                  
     DECLARE @Month int                    
     DECLARE @FROMDATE DATETIME                    
     DECLARE @ENDDATE DATETIME                    
     DECLARE @OutPutDate DATETIME                  
       SET @Month=6                    
     /*                  
        When we get index 1 then we find the maximum date from matching record                  
        and decsrease no of month for the start date.                  
     */   
	 SET @FinishDate= GETDATE()     
     IF @Index=1                    
	  BEGIN   
	                          
                       
	  SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-@Month,@FinishDate))                    
	  SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                    
     END                    
    ELSE IF @Index = 2                   
     BEGIN                    
       SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-12,@FinishDate))                    
	  SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                    
     END                
     ELSE IF @Index = 3                   
     BEGIN                    
       SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-36,@FinishDate))                    
	  SET @ENDDATE =dbo.ufn_get_date_from_datetime(@FinishDate)                    
     END         
      --SET THE OUTPUT DATE                  
      SET @OutPutDate=DATEADD(month,-@Month,@FinishDate)                 
                       
    DECLARE @sql NVARCHAR(MAX) = 'SELECT  cr.ClientNo                      
          , cl.ClientName                      
          , cr.CustomerRequirementId                      
          , cr.CustomerRequirementNumber                      
          , cr.Part                      
          , cr.Quantity                      
          , cr.ROHS                      
          , cr.CompanyNo                      
          , co.CompanyName                      
          , cr.ReceivedDate                      
          , cr.Price                      
          , cr.CurrencyNo                      
          , cu.CurrencyCode                      
          , cr.ManufacturerNo                      
          , mf.ManufacturerCode                      
       , pr.ProductName                      
          , pk.PackageName                      
          , cr.CustomerPart                      
          , cr.DateCode          
          , cr.Salesman          
          , lo.EmployeeName     
          , VM.BOMID as  BOMID    
          , VM.BOMName as BOMName       
    ,cl.ClientCode         
    FROM    dbo.tbCustomerRequirement cr WITH (NoLock)  
    JOIN    dbo.tbCompany co WITH (NoLock) ON cr.CompanyNo = co.CompanyId                      
    JOIN    dbo.tbClient cl WITH (NoLock) ON cr.ClientNo = cl.ClientId                      
    LEFT JOIN dbo.tbManufacturer mf WITH (NoLock) ON cr.ManufacturerNo = mf.ManufacturerId                      
    LEFT JOIN dbo.tbCurrency cu WITH (NoLock) ON cu.CurrencyId = cr.CurrencyNo                      
    LEFT JOIN dbo.tbProduct pr WITH (NoLock) ON pr.ProductId = cr.ProductNo                      
    LEFT JOIN dbo.tbPackage pk WITH (NoLock) ON pk.PackageId = cr.PackageNo          
	LEFT JOIN dbo.tbLogin lo WITH (NoLock) ON lo.LoginId = cr.Salesman    
	LEFT JOIN VWBOM VM WITH (NoLock) ON VM.BOMID =  cr.BOMNO           
    WHERE '
	+ '((cr.ClientNo = '+CAST(@ClientId AS NVARCHAR)+') OR (cr.ClientNo <> '+CAST(@ClientId AS NVARCHAR)+'))'
	+ 'AND (dbo.ufn_get_date_from_datetime(cr.ReceivedDate) between  ''' + CONVERT(NVARCHAR, @FROMDATE, 23)+'''  AND  ''' +CONVERT(NVARCHAR, @ENDDATE, 23)+''' ) '
	+ 'AND( 1=0 '

	DECLARE @value NVARCHAR(MAX);
	DECLARE item_cursor CURSOR FOR 
	SELECT LTRIM(RTRIM(value))  -- Trim spaces
	FROM STRING_SPLIT(@PartSearch, '_');

	OPEN item_cursor;
	FETCH NEXT FROM item_cursor INTO @value;

	WHILE @@FETCH_STATUS = 0
	BEGIN
		IF RIGHT(LTRIM(RTRIM(@value)), 1) != '%'
    BEGIN
        SET @value = LTRIM(RTRIM(@value)) + '%';
    END
		SET @sql = @sql 	
		+ 'OR (cr.FullPart LIKE ''' +@value  +'''                    
                 OR cr.FullCustomerPart LIKE ''' +@value+''' )'
		FETCH NEXT FROM item_cursor INTO @value;
	END

	CLOSE item_cursor;
	DEALLOCATE item_cursor;

	SET @sql = @sql +')' 
	+ 'ORDER BY cr.ReceivedDate DESC                      
          , cr.CustomerRequirementNumber ASC '
	PRINT @sql;  -- Debugging: Check the generated SQL before execution
	EXEC sp_executesql @sql;
    
      --SELECT THE OUT DATE                   
    SELECT @OutPutDate AS OutPutDate       