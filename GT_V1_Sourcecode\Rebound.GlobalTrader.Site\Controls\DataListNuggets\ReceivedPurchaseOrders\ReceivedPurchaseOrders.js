Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.prototype={initialize:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/ReceivedPurchaseOrders";this._strDataObject="ReceivedPurchaseOrders";Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.getData()},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.callBaseMethod(this,"dispose")},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_ReceivedPurchaseOrder(n.ID,n.No),$R_FN.writeDoubleCellValue(n.Qty,n.QtyR),$R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(n.GINo,n.GI),$R_FN.setCleanTextValue(n.DateR)),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.SuppPart),$R_FN.setCleanTextValue(n.SuppInv)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.AWB),$R_FN.setCleanTextValue(n.InvTotal))],this._table.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);