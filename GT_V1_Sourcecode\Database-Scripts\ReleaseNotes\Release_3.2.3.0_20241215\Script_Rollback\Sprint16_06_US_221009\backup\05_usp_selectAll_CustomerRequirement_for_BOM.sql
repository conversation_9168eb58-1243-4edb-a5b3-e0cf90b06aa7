﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK         UPDATED BY   DATE			ACTION		DESCRIPTION  
[US-210037]  An.TranTan   23-Oct-2024	UPDATE		Get Manufacturer/Company advisory notes for HUBRFQ items 
===========================================================================================  
*/ 
CREATE OR ALTER   PROCEDURE [dbo].[usp_selectAll_CustomerRequirement_for_BOM] 
	@BOMNo INT
	,@ClientID INT
	,@IsPoHub BIT = 0
AS
--*===========================================================================================              
--* Action: Altered  By: Abhinav Saxena  Date:01-09-2023  Comment: For RP-2228              
--*===========================================================================================                                   
BEGIN
	DECLARE @AllSorcingHasDelDate INT
	DECLARE @AllSorcingHasProduct INT
	DECLARE @SourcingResult INT
	DECLARE @SourcingResultId INT
	DECLARE @AssigneeIds NVARCHAR(MAX);

	SELECT @AssigneeIds = STUFF((
				SELECT ',' + CAST(LoginId AS NVARCHAR(100))
				FROM tbLogin l WITH (NOLOCK)
				LEFT JOIN tbSession s WITH (NOLOCK) ON s.LoginNo = l.LoginId
				WHERE ClientNo = @ClientID
					AND s.sessionTimestamp >= CONVERT(DATE, GETDATE())
					AND l.Inactive = 0
				FOR XML PATH('')
					,TYPE
				).value('.', 'NVARCHAR(MAX)'), 1, 1, ' ')

	SELECT @AllSorcingHasDelDate = count(*)
	FROM tbCustomerRequirement cr
	JOIN tbBOM bm ON bm.BOMId = cr.BOMNo
	JOIN tbSourcingResult sr ON cr.CustomerRequirementId = sr.CustomerRequirementNo
	WHERE bm.BOMId = @BOMNo
		AND sr.SourcingTable IN (
			'PQ'
			,'OFPH'
			,'EXPH'
			)
		AND sr.DeliveryDate IS NULL

	SELECT @AllSorcingHasProduct = count(*)
	FROM tbCustomerRequirement cr
	JOIN tbBOM bm ON bm.BOMId = cr.BOMNo
	JOIN tbSourcingResult sr ON cr.CustomerRequirementId = sr.CustomerRequirementNo
	WHERE bm.BOMId = @BOMNo
		AND sr.SourcingTable IN (
			'PQ'
			,'OFPH'
			,'EXPH'
			)
		AND sr.ProductNo IS NULL

	SELECT cr.CustomerRequirementId
		,cr.CustomerRequirementNumber
		,cr.ClientNo
		,REPLACE(cr.FullPart, '"', '') AS FullPart
		,REPLACE(cr.Part, '"', '') AS Part
		,cr.ManufacturerNo
		,cr.DateCode
		,cr.PackageNo
		,cr.Quantity
		,cr.Price
		,cr.CurrencyNo
		,cr.Salesman
		,cr.DatePromised
		,cr.Instructions
		,cr.CompanyNo
		,cr.Alternate
		,cr.CustomerPart
		,cr.Closed
		,cr.ROHS
		,cr.UpdatedBy
		,cr.DLUP
		,cr.FactorySealed
		,cr.MSL
		,cr.PartialQuantityAcceptable
		,cr.Obsolete
		,cr.LastTimeBuy
		,cr.RefirbsAcceptable
		,cr.TestingRequired
		,cr.TargetSellPrice
		,cr.CompetitorBestOffer
		,cr.CustomerDecisionDate
		,cr.RFQClosingDate
		,cr.QuoteValidityRequired
		,cr.ReqType
		,cr.OrderToPlace
		,cr.ReqForTraceability
		,lg.EmployeeName AS SalesmanName
		,co.CompanyName
		,cu.CurrencyCode
		,pr.ProductName
		,mf.ManufacturerCode
		,pk.PackageName
		,ct.IsTraceability
		,bom.BOMName AS BOMHeader
		,cr.BOMNo
		,cr.POHubReleaseBy
		,bom.RequestToPOHubBy
		,bom.BOMCode
		,bom.BOMName AS BOMFullName
		,bom.CurrencyNo AS BOMCurrencyNo
		,bom.DLUP AS BOMDate
		,bom.UpdateByPH
		,CASE bom.[Status]
			WHEN 1
				THEN 'NEW'
			WHEN 2
				THEN 'OPEN'
			WHEN 3
				THEN 'RPQ'
			WHEN 4
				THEN 'PARTIAL RELEASED'
			WHEN 5
				THEN 'RELEASED'
			WHEN 6
				THEN 'CLOSED'
			END AS BOMStatus
		,c.ClientName
		--,  (SELECT TOP 1 SourcingResultId FROM tbSourcingResult sr WHERE sr.CustomerRequirementNo=cr.CustomerRequirementId ) AS SourcingResultId                                                  
		--, (SELECT TOP 1 POHubCompanyNo FROM tbSourcingResult WHERE CustomerRequirementNo=cr.CustomerRequirementId and POHubCompanyNo is not null ) AS POHubCompany                                                     
		,cob.CurrencyCode AS BOMCurrencyCode
		,dbo.ufn_convert_currency_value(cr.Price, cr.CurrencyNo, bom.CurrencyNo, bom.DLUP) AS ConvertedTargetValue
		,@AllSorcingHasDelDate AS AllSorcingHasDelDate
		,@AllSorcingHasProduct AS AllSorcingHasProduct
		,0 AS SourcingResult
		,cr.HasClientSourcingResult
		,cr.HasHubSourcingResult
		--,@SourcingResultId AS SourcingResult                                 
		,cr.IsNoBid
		,cr.ExpediteDate
		,CASE 
			WHEN cr.Alternate = 1
				AND cr.AlternateStatus IS NULL
				THEN cast(1 AS TINYINT)
			ELSE cr.AlternateStatus
			END AS AlternateStatus
		-- 1-Alternate,2-Possible Alternate,3-Firm Alternate                               
		,cr.SupportTeamMemberNo
		,stm.EmployeeName AS SupportTeamMemberName
		,cr.PartWatch_HUBIPO AS PartWatchHUBIPO
		,(
			--- query added by arpit 15.03.2022                    
			--- to get the row if any discripancy is found                          
			SELECT TOP 1 PriceIssueBuyAndSell
			FROM (
				SELECT CASE 
						WHEN A.ActualPrice >= A.Price
							THEN 1
						ELSE 0
						END PriceIssueBuyAndSell
				FROM (
					SELECT CASE 
							WHEN sr.PartWatchMatch = 1
								AND sr.POHubCompanyNo IS NOT NULL
								AND cr.ClientNo != 114
								AND @IsPoHub = 0
								THEN 0
							ELSE sr.Price
							END Price
						,sr.ActualPrice
					FROM dbo.tbSourcingResult sr
					WHERE sr.CustomerRequirementNo = cr.CustomerRequirementId
						AND (
							sr.PartWatchMatch = 1
							OR (
								(
									@IsPoHub = 0
									AND (
										sr.IsReleased = 1
										OR sr.POHubCompanyNo IS NULL
										)
									)
								OR (
									@IsPoHub = 1
									AND NOT sr.POHubCompanyNo IS NULL
									)
								)
							)
						AND ISNULL(sr.PartWatchMatch, 0) = 0
					) A
				) B
			WHERE B.PriceIssueBuyAndSell = 1
			) PriceIssueBuyAndSell
		,CASE 
			WHEN ISNULL(cr.AS6081, 0) = 1
				THEN 'Yes'
			ELSE 'No'
			END AS IsAs6081Required
		,CASE 
			WHEN ISNULL(cr.IsGroupAssignment, 0) = 0
				AND ISNULL(cr.UpdateByPH, 0) != 0
				THEN lcr.EmployeeName
			WHEN ISNULL(cr.IsGroupAssignment, 0) = 1
				AND ISNULL(cr.UpdateByPH, 0) != 0
				THEN sgcr.SecurityGroupName + ' (Group)'
			WHEN ISNULL(bom.IsGroupAssignment, 0) = 0
				AND ISNULL(bom.UpdateByPH, 0) != 0
				THEN (lat.EmployeeName)
			WHEN ISNULL(bom.IsGroupAssignment, 0) = 1
				AND ISNULL(bom.UpdateByPH, 0) != 0
				THEN sg.SecurityGroupName + ' (Group)'
			ELSE ''
			END AS AssignedTo
		--, dbo.ufn_AS6081_GetAssigneeDetails(cr.CustomerRequirementId) AS AssigneeId          
		,@AssigneeIds AS AssigneeId
		,CASE WHEN ISNULL(co.IsDisplayAdvisory, 0) = 1 THEN co.AdvisoryNotes ELSE '' END AS CompanyAdvisoryNotes
		,CASE WHEN ISNULL(mf.IsDisplayAdvisory, 0) = 1 THEN mf.AdvisoryNotes ELSE '' END AS MfrAdvisoryNotes
	FROM dbo.tbCustomerRequirement cr
	JOIN dbo.tbCompany co ON cr.CompanyNo = co.CompanyId
	LEFT JOIN dbo.tbCurrency cu ON cr.CurrencyNo = cu.CurrencyId
	LEFT JOIN dbo.tbLogin lg ON cr.Salesman = lg.LoginId
	--LEFT JOIN dbo.tbContact cn  ON cr.ContactNo = cn.ContactId                                            
	LEFT JOIN dbo.tbProduct pr ON cr.ProductNo = pr.ProductId
	LEFT JOIN dbo.tbPackage pk ON cr.PackageNo = pk.PackageId
	LEFT JOIN dbo.tbManufacturer mf ON cr.ManufacturerNo = mf.ManufacturerId
	--LEFT JOIN dbo.tbUsage us   ON cr.UsageNo = us.UsageId                                            
	--LEFT JOIN dbo.tbReason re   ON cr.ReasonNo = re.ReasonId                                            
	--LEFT JOIN dbo.tbDivision dv ON lg.DivisionNo = dv.DivisionId                                            
	LEFT JOIN tbCompanyType ct ON ct.CompanyTypeId = co.TypeNo
	LEFT JOIN tbBOM bom ON bom.BOMId = cr.BOMNo
	LEFT JOIN tbCurrency cob ON bom.CurrencyNo = cob.CurrencyId
	LEFT JOIN tbClient c ON cr.ClientNo = c.ClientId
	LEFT JOIN dbo.tbLogin stm ON cr.SupportTeamMemberNo = stm.LoginId
	LEFT JOIN tbLogin lcr ON cr.UpdateByPH = lcr.LoginId
	LEFT JOIN tbLogin lat ON bom.UpdateByPH = lat.LoginId
	LEFT JOIN tbSecurityGroup sg ON bom.UpdateByPH = sg.SecurityGroupId
	LEFT JOIN tbSecurityGroup sgcr ON cr.UpdateByPH = sgcr.SecurityGroupId
	WHERE cr.BOMNo = @BOMNo
	ORDER BY cr.sequence ASC
END
GO


