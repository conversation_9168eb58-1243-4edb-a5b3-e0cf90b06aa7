﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllowEmailing" xml:space="preserve">
    <value>Allow Emailing</value>
  </data>
  <data name="AllowPrinting" xml:space="preserve">
    <value>Allow Printing</value>
  </data>
  <data name="CommunicationsSection_View" xml:space="preserve">
    <value>Allow viewing Communications section</value>
  </data>
  <data name="Communications_NewMessage_Add" xml:space="preserve">
    <value>Allow adding a new Message</value>
  </data>
  <data name="Communications_NewToDo_Add" xml:space="preserve">
    <value>Allow adding a new ToDo item</value>
  </data>
  <data name="ContactSection_View" xml:space="preserve">
    <value>Allow viewing Contact section</value>
  </data>
  <data name="Contact_CompanyDetail_Addresses_Add" xml:space="preserve">
    <value>Allow adding a new Address</value>
  </data>
  <data name="Contact_CompanyDetail_Addresses_Cease" xml:space="preserve">
    <value>Allow ceasing an Address</value>
  </data>
  <data name="Contact_CompanyDetail_Addresses_Edit" xml:space="preserve">
    <value>Allow editing an Address</value>
  </data>
  <data name="Contact_CompanyDetail_Addresses_MakeDefault" xml:space="preserve">
    <value>Allow making an Address default billing or shipping for a company</value>
  </data>
  <data name="Contact_CompanyDetail_ContactLog_Add" xml:space="preserve">
    <value>Allow adding to Contact Log</value>
  </data>
  <data name="Contact_CompanyDetail_ContactLog_Edit" xml:space="preserve">
    <value>Allow editing Contact Log items</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Contact_CompanyDetail_ManufacturersSupplied_Add" xml:space="preserve">
    <value>Allow adding a Manufacturer Supplied</value>
  </data>
  <data name="Contact_CompanyDetail_ManufacturersSupplied_Delete" xml:space="preserve">
    <value>Allow deleting a Manufacturer Supplied</value>
  </data>
  <data name="Contact_CompanyDetail_ManufacturersSupplied_Edit" xml:space="preserve">
    <value>Allow editing a Manufacturer Supplied</value>
  </data>
  <data name="Contact_CompanyDetail_PurchasingInfo_Edit" xml:space="preserve">
    <value>Allow editing Purchasing Information</value>
  </data>
  <data name="Contact_CompanyDetail_SalesInfo_Edit" xml:space="preserve">
    <value>Allow editing Sales Information</value>
  </data>
  <data name="Contact_Company_Add" xml:space="preserve">
    <value>Allow adding a new Company</value>
  </data>
  <data name="Contact_ContactDetail_ContactList_Add" xml:space="preserve">
    <value>Allow adding a new Contact</value>
  </data>
  <data name="Contact_ContactDetail_ContactList_Delete" xml:space="preserve">
    <value>Allow deleting a Contact</value>
  </data>
  <data name="Contact_ContactDetail_ContactList_MakePODefault" xml:space="preserve">
    <value>Allow making a Company's default Contact for Purchase Orders</value>
  </data>
  <data name="Contact_ContactDetail_ContactList_MakeSODefault" xml:space="preserve">
    <value>Allow making a Company's default Contact for Sales Orders</value>
  </data>
  <data name="Contact_ContactDetail_ContactLog_Add" xml:space="preserve">
    <value>Allow adding to Contact Log</value>
  </data>
  <data name="Contact_ContactDetail_ContactLog_Edit" xml:space="preserve">
    <value>Allow editing Contact Log items</value>
  </data>
  <data name="Contact_ContactDetail_ExtendedInfo_Edit" xml:space="preserve">
    <value>Allow editing Extended Information</value>
  </data>
  <data name="Contact_ContactDetail_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Contact_ManufacturerDetail_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Contact_ManufacturerDetail_RelatedCompanies_Add" xml:space="preserve">
    <value>Allow adding a new Related Company</value>
  </data>
  <data name="Contact_ManufacturerDetail_RelatedCompanies_Delete" xml:space="preserve">
    <value>Allow deleting a Related Company</value>
  </data>
  <data name="Contact_ManufacturerDetail_Suppliers_Add" xml:space="preserve">
    <value>Allow adding a new Supplier</value>
  </data>
  <data name="Contact_ManufacturerDetail_Suppliers_Delete" xml:space="preserve">
    <value>Allow deleting a Supplier</value>
  </data>
  <data name="Contact_ManufacturerDetail_Suppliers_Edit" xml:space="preserve">
    <value>Allow editing a Supplier</value>
  </data>
  <data name="Contact_Manufacturer_Add" xml:space="preserve">
    <value>Allow adding a new Manufacturer</value>
  </data>
  <data name="GeneralPermissions" xml:space="preserve">
    <value>General Permissions</value>
  </data>
  <data name="OrdersSection_View" xml:space="preserve">
    <value>Allow viewing Orders section</value>
  </data>
  <data name="Orders_CreditNote_Add" xml:space="preserve">
    <value>Allow adding a new Credit Note</value>
  </data>
  <data name="Orders_CreditNote_Email" xml:space="preserve">
    <value>Allow emailing</value>
  </data>
  <data name="Orders_CreditNote_Lines_Add" xml:space="preserve">
    <value>Allow adding a new Line</value>
  </data>
  <data name="Orders_CreditNote_Lines_CanAdd_CRMA" xml:space="preserve">
    <value>Allow adding a new Line based on a CRMA line</value>
  </data>
  <data name="Orders_CreditNote_Lines_CanAdd_Invoice" xml:space="preserve">
    <value>Allow adding a new Line based on an Invoice line</value>
  </data>
  <data name="Orders_CreditNote_Lines_CanAdd_Service" xml:space="preserve">
    <value>Allow adding a new Line based on a Service</value>
  </data>
  <data name="Orders_CreditNote_Lines_Delete" xml:space="preserve">
    <value>Allow deleting a Line</value>
  </data>
  <data name="Orders_CreditNote_Lines_Edit" xml:space="preserve">
    <value>Allow editing a Line</value>
  </data>
  <data name="Orders_CreditNote_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_CreditNote_Print" xml:space="preserve">
    <value>Allow printing</value>
  </data>
  <data name="Orders_Credit_Add_FromCRMA" xml:space="preserve">
    <value>Allow adding a new Credit based on a CRMA</value>
  </data>
  <data name="Orders_Credit_Add_FromInvoice" xml:space="preserve">
    <value>Allow adding a new Credit based on an Invoice</value>
  </data>
  <data name="Orders_CRMA_Add" xml:space="preserve">
    <value>Allow adding a new Customer RMA</value>
  </data>
  <data name="Orders_CRMA_Email" xml:space="preserve">
    <value>Allow emailing</value>
  </data>
  <data name="Orders_CRMA_Lines_Add" xml:space="preserve">
    <value>Allow adding a new Line</value>
  </data>
  <data name="Orders_CRMA_Lines_Deallocate" xml:space="preserve">
    <value>Allow deallocating a Customer RMA Line</value>
  </data>
  <data name="Orders_CRMA_Lines_Delete" xml:space="preserve">
    <value>Allow deleting a Line</value>
  </data>
  <data name="Orders_CRMA_Lines_Edit" xml:space="preserve">
    <value>Allow editing a Line</value>
  </data>
  <data name="Orders_CRMA_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_CRMA_Print" xml:space="preserve">
    <value>Allow printing</value>
  </data>
  <data name="Orders_CustomerRequirement_Add" xml:space="preserve">
    <value>Allow adding a new Customer Requirement</value>
  </data>
  <data name="Orders_CustomerRequirement_MainInformation_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_CustomerRequirement_PartsRequired_AddAlternate" xml:space="preserve">
    <value>Allow adding an Alternate Part</value>
  </data>
  <data name="Orders_CustomerRequirement_PartsRequired_Close" xml:space="preserve">
    <value>Allow closing a Customer Requirement</value>
  </data>
  <data name="Orders_CustomerRequirement_SourcingResults_Add" xml:space="preserve">
    <value>Allow adding a new Sourcing Result</value>
  </data>
  <data name="Orders_CustomerRequirement_SourcingResults_Edit" xml:space="preserve">
    <value>Allow editing a Sourcing Result to a Customer Requirement</value>
  </data>
  <data name="Orders_CustomerRequirement_Sourcing_AddToRequirement" xml:space="preserve">
    <value>Allow adding a Sourcing Result to a Customer Requirement</value>
  </data>
  <data name="Orders_CustomerRequirement_Sourcing_EditOffer" xml:space="preserve">
    <value>Allow editing a Sourcing Result</value>
  </data>
  <data name="Orders_CustomerRequirement_Sourcing_RFQ" xml:space="preserve">
    <value>Allow sending a Request for Quote</value>
  </data>
  <data name="Orders_DebitNote_Add" xml:space="preserve">
    <value>Allow adding a new Debit Note</value>
  </data>
  <data name="Orders_DebitNote_Email" xml:space="preserve">
    <value>Allow emailing</value>
  </data>
  <data name="Orders_DebitNote_Lines_Add" xml:space="preserve">
    <value>Allow adding a new Line</value>
  </data>
  <data name="Orders_DebitNote_Lines_CanAdd_PO" xml:space="preserve">
    <value>Allow adding a new Line based on a PO line</value>
  </data>
  <data name="Orders_DebitNote_Lines_CanAdd_Service" xml:space="preserve">
    <value>Allow adding a new Line based on a Service</value>
  </data>
  <data name="Orders_DebitNote_Lines_Delete" xml:space="preserve">
    <value>Allow deleting a Line</value>
  </data>
  <data name="Orders_DebitNote_Lines_Edit" xml:space="preserve">
    <value>Allow editing a Line</value>
  </data>
  <data name="Orders_DebitNote_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_DebitNote_Print" xml:space="preserve">
    <value>Allow printing</value>
  </data>
  <data name="Orders_Invoice_Add" xml:space="preserve">
    <value>Allow adding a new Invoice</value>
  </data>
  <data name="Orders_Invoice_BulkPrint" xml:space="preserve">
    <value>Allow bulk printing Invoices</value>
  </data>
  <data name="Orders_Invoice_DeletedLines" xml:space="preserve">
    <value>Allow viewing deleted Invoice Lines</value>
  </data>
  <data name="Orders_Invoice_Email" xml:space="preserve">
    <value>Allow emailing</value>
  </data>
  <data name="Orders_Invoice_EmailCertificateOfConformance" xml:space="preserve">
    <value>Allow emailing of Certificate Of Conformance</value>
  </data>
  <data name="Orders_Invoice_EmailPackingSlip" xml:space="preserve">
    <value>Allow emailing of Packing Slip</value>
  </data>
  <data name="Orders_Invoice_Lines_Add" xml:space="preserve">
    <value>Allow adding a new Line</value>
  </data>
  <data name="Orders_Invoice_Lines_CanAdd_NewLine" xml:space="preserve">
    <value>Allow adding a new manual Line</value>
  </data>
  <data name="Orders_Invoice_Lines_CanAdd_Service" xml:space="preserve">
    <value>Allow adding a new Line based on a Service</value>
  </data>
  <data name="Orders_Invoice_Lines_Delete" xml:space="preserve">
    <value>Allow deleting an Invoice Line</value>
  </data>
  <data name="Orders_Invoice_Lines_Edit" xml:space="preserve">
    <value>Allow editing an Invoice Line</value>
  </data>
  <data name="Orders_Invoice_Lines_EditAllocation" xml:space="preserve">
    <value>Allow editing an Invoice Line Allocation</value>
  </data>
  <data name="Orders_Invoice_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_Invoice_MainInfo_Export" xml:space="preserve">
    <value>Allow marking for export</value>
  </data>
  <data name="Orders_Invoice_MainInfo_Release" xml:space="preserve">
    <value>Allow releasing for edit</value>
  </data>
  <data name="Orders_Invoice_Print" xml:space="preserve">
    <value>Allow printing</value>
  </data>
  <data name="Orders_Invoice_PrintCertificateOfConformance" xml:space="preserve">
    <value>Allow printing of Certificate Of Conformance</value>
  </data>
  <data name="Orders_Invoice_PrintlPackingSlip" xml:space="preserve">
    <value>Allow printing of Packing Slip</value>
  </data>
  <data name="Orders_Invoice_PrintPackingSlip" xml:space="preserve">
    <value>Allow printing Packing Slip</value>
  </data>
  <data name="Orders_PurchaseOrder_Add" xml:space="preserve">
    <value>Allow adding a new Purchase Order</value>
  </data>
  <data name="Orders_PurchaseOrder_EditTax" xml:space="preserve">
    <value>Allow modification of default tax</value>
  </data>
  <data name="Orders_PurchaseOrder_EditCurrencyAndTerms" xml:space="preserve">
    <value>Allow modification of default currency and terms</value>
  </data>
  <data name="Orders_PurchaseOrder_Email" xml:space="preserve">
    <value>Allow emailing</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_Add" xml:space="preserve">
    <value>Allow adding a new Line</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_CanAdd_NewLine" xml:space="preserve">
    <value>Allow adding a new manual Line</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_CanAdd_PO" xml:space="preserve">
    <value>Allow adding a new Line based on another Purchase Order</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_CanAdd_POReq" xml:space="preserve">
    <value>Allow adding a new Line based on a Purchase Requisition</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_CanAdd_Req" xml:space="preserve">
    <value>Allow adding a new Line based on a Requirement</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_CanAdd_Stock" xml:space="preserve">
    <value>Allow adding a new Line for Stock</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_Close" xml:space="preserve">
    <value>Allow closing of Purchase Order Lines</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_Deallocate" xml:space="preserve">
    <value>Allow deallocating a Line</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_Delete" xml:space="preserve">
    <value>Allow deleting a Line</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_Edit" xml:space="preserve">
    <value>Allow editing a Line</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_Post" xml:space="preserve">
    <value>Allow posting a Line</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_Unpost" xml:space="preserve">
    <value>Allow unposting a Line</value>
  </data>
  <data name="Orders_PurchaseOrder_MainInfo_Approve" xml:space="preserve">
    <value>Allow approving</value>
  </data>
  <data name="Orders_PurchaseOrder_MainInfo_Close" xml:space="preserve">
    <value>Allow closing a Purchase Order</value>
  </data>
  <data name="Orders_PurchaseOrder_MainInfo_Disapprove" xml:space="preserve">
    <value>Allow disapproving</value>
  </data>
  <data name="Orders_PurchaseOrder_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_PurchaseOrder_PDF" xml:space="preserve">
    <value>Allow creating a PDF for Purchase Order</value>
  </data>
  <data name="Orders_PurchaseOrder_Print" xml:space="preserve">
    <value>Allow printing</value>
  </data>
  <data name="Orders_QuoteLines_Add" xml:space="preserve">
    <value>Allow adding a new Line</value>
  </data>
  <data name="Orders_QuoteLines_CanAdd_NewLine" xml:space="preserve">
    <value>Allow adding a new manual Line</value>
  </data>
  <data name="Orders_QuoteLines_CanAdd_Req" xml:space="preserve">
    <value>Allow adding a new Line based on a Requirement</value>
  </data>
  <data name="Orders_QuoteLines_CanAdd_Service" xml:space="preserve">
    <value>Allow adding a new Line based on a Service</value>
  </data>
  <data name="Orders_QuoteLines_CanAdd_SourcingResult" xml:space="preserve">
    <value>Allow adding a new Line based on a Requirement Sourcing Result</value>
  </data>
  <data name="Orders_QuoteLines_CanAdd_Stock" xml:space="preserve">
    <value>Allow adding a new Line from Stock</value>
  </data>
  <data name="Orders_QuoteLines_Close" xml:space="preserve">
    <value>Allow closing a Line</value>
  </data>
  <data name="Orders_QuoteLines_Edit" xml:space="preserve">
    <value>Allow editing a Line</value>
  </data>
  <data name="Orders_Quote_Add" xml:space="preserve">
    <value>Allow adding a new Quote</value>
  </data>
  <data name="Orders_Quote_Add_FromNew" xml:space="preserve">
    <value>Allow adding a new blank Quote</value>
  </data>
  <data name="Orders_Quote_Add_FromReq" xml:space="preserve">
    <value>Allow adding a new Quote based on a Requirement</value>
  </data>
  <data name="Orders_Quote_Email" xml:space="preserve">
    <value>Allow emailing</value>
  </data>
  <data name="Orders_Quote_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_Quote_PDF" xml:space="preserve">
    <value>Allow creating a PDF for Quote</value>
  </data>
  <data name="Orders_Quote_Print" xml:space="preserve">
    <value>Allow printing</value>
  </data>
  <data name="Orders_SalesOrder_Add" xml:space="preserve">
    <value>Allow adding a new Sales Order</value>
  </data>
  <data name="Orders_SalesOrder_EditTax" xml:space="preserve">
    <value>Allow modification of default tax</value>
  </data>
  <data name="Orders_SalesOrder_EditCurrencyAndTerms" xml:space="preserve">
    <value>Allow modification of default currency and terms</value>
  </data>
  <data name="Orders_SalesOrder_Email" xml:space="preserve">
    <value>Allow emailing</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Add" xml:space="preserve">
    <value>Allow adding a new Line</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Allocate" xml:space="preserve">
    <value>Allow allocating a Line</value>
  </data>
  <data name="Orders_SalesOrder_Lines_CanAdd_NewLine" xml:space="preserve">
    <value>Allow adding a new manual Line</value>
  </data>
  <data name="Orders_SalesOrder_Lines_CanAdd_Quote" xml:space="preserve">
    <value>Allow adding a new Line based on a Quote</value>
  </data>
  <data name="Orders_SalesOrder_Lines_CanAdd_Req" xml:space="preserve">
    <value>Allow adding a new Line based on a Requirement</value>
  </data>
  <data name="Orders_SalesOrder_Lines_CanAdd_Service" xml:space="preserve">
    <value>Allow adding a new Line for Service</value>
  </data>
  <data name="Orders_SalesOrder_Lines_CanAdd_SO" xml:space="preserve">
    <value>Allow adding a new Line based on another Sales Order</value>
  </data>
  <data name="Orders_SalesOrder_Lines_CanAdd_Stock" xml:space="preserve">
    <value>Allow adding a new Line for Stock</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Close" xml:space="preserve">
    <value>Allow closing of Sales Order Lines</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Deallocate" xml:space="preserve">
    <value>Allow deallocating a Line</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Delete" xml:space="preserve">
    <value>Allow deleting a Line</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Edit" xml:space="preserve">
    <value>Allow editing a Line</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Post" xml:space="preserve">
    <value>Allow posting a Line</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Unpost" xml:space="preserve">
    <value>Allow unposting a Line</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_Authorise" xml:space="preserve">
    <value>Allow authorising</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_Close" xml:space="preserve">
    <value>Allow closing a Sales Order</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_Deauthorise" xml:space="preserve">
    <value>Allow deauthorising</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_EditShippingCosts" xml:space="preserve">
    <value>Allow editing shipping costs and freight</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_Notify" xml:space="preserve">
    <value>Allow notification</value>
  </data>
  <data name="Orders_SalesOrder_Print" xml:space="preserve">
    <value>Allow printing</value>
  </data>
  <data name="Orders_SalesOrder_ProForma_Email" xml:space="preserve">
    <value>Allow emailing Pro Forma Invoice</value>
  </data>
  <data name="Orders_SalesOrder_ProForma_Print" xml:space="preserve">
    <value>Allow printing Pro Forma Invoice</value>
  </data>
  <data name="Orders_SalesOrder_Report" xml:space="preserve">
    <value>Allow production of Sales Order Report</value>
  </data>
  <data name="Orders_SalesOrder_SOReport_Print" xml:space="preserve">
    <value>Allow printing Sales Order Report</value>
  </data>
  <data name="Orders_Sourcing_AddToRequirement" xml:space="preserve">
    <value>Allow add to requirement button to be visible</value>
  </data>
  <data name="Orders_Sourcing_EditOffer" xml:space="preserve">
    <value>Allow editing a Sourcing Result</value>
  </data>
  <data name="Orders_Sourcing_RFQ" xml:space="preserve">
    <value>Allow sending a Request for Quote</value>
  </data>
  <data name="Orders_SRMA_Add" xml:space="preserve">
    <value>Allow adding a new Supplier RMA</value>
  </data>
  <data name="Orders_SRMA_Email" xml:space="preserve">
    <value>Allow emailing</value>
  </data>
  <data name="Orders_SRMA_Lines_Add" xml:space="preserve">
    <value>Allow adding a new Line</value>
  </data>
  <data name="Orders_SRMA_Lines_Delete" xml:space="preserve">
    <value>Allow deleting a Line</value>
  </data>
  <data name="Orders_SRMA_Lines_Edit" xml:space="preserve">
    <value>Allow editing a Line</value>
  </data>
  <data name="Orders_SRMA_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Orders_SRMA_Print" xml:space="preserve">
    <value>Allow printing</value>
  </data>
  <data name="OverallSetup" xml:space="preserve">
    <value>Allow entrance to Setup Screens</value>
  </data>
  <data name="PermissionsForSection" xml:space="preserve">
    <value>Permissions for {0} Section</value>
  </data>
  <data name="ReportsSection_View" xml:space="preserve">
    <value>Allow viewing Reports section</value>
  </data>
  <data name="SetupSection_View" xml:space="preserve">
    <value>Allow viewing Setup section</value>
  </data>
  <data name="Setup_CompanySettings" xml:space="preserve">
    <value>Allow viewing Company Settings</value>
  </data>
  <data name="Setup_CompanySettings_AppSettings" xml:space="preserve">
    <value>Allow viewing company Application Settings setup</value>
  </data>
  <data name="Setup_CompanySettings_AppSettings_Edit" xml:space="preserve">
    <value>Allow editing company Application Settings</value>
  </data>
  <data name="Setup_CompanySettings_Countries" xml:space="preserve">
    <value>Allow viewing Countries setup</value>
  </data>
  <data name="Setup_CompanySettings_Countries_Add" xml:space="preserve">
    <value>Allow adding a new Country</value>
  </data>
  <data name="Setup_CompanySettings_Countries_Edit" xml:space="preserve">
    <value>Allow editing a Country</value>
  </data>
  <data name="Setup_CompanySettings_Currencies" xml:space="preserve">
    <value>Allow viewing Currencies setup</value>
  </data>
  <data name="Setup_CompanySettings_Currencies_Add" xml:space="preserve">
    <value>Allow adding a new Currency</value>
  </data>
  <data name="Setup_CompanySettings_Currencies_Edit" xml:space="preserve">
    <value>Allow editing a Currency</value>
  </data>
  <data name="Setup_CompanySettings_Currencies_EditRates" xml:space="preserve">
    <value>Allow editing current Currency rates</value>
  </data>
  <data name="Setup_CompanySettings_CurrencyRateHistory_Delete" xml:space="preserve">
    <value>Allow deleting a Currency Rate</value>
  </data>
  <data name="Setup_CompanySettings_CurrencyRateHistory_Edit" xml:space="preserve">
    <value>Allow editing a Currency Rate</value>
  </data>
  <data name="Setup_CompanySettings_Divisions" xml:space="preserve">
    <value>Allow viewing Divisions setup</value>
  </data>
  <data name="Setup_CompanySettings_Divisions_Add" xml:space="preserve">
    <value>Allow adding a new Division</value>
  </data>
  <data name="Setup_CompanySettings_Divisions_DocumentHeader_AddDelete" xml:space="preserve">
    <value>Allow adding and deleting a Division's Document Header Image</value>
  </data>
  <data name="Setup_CompanySettings_Divisions_Edit" xml:space="preserve">
    <value>Allow editing a Division</value>
  </data>
  <data name="Setup_CompanySettings_MailGroups" xml:space="preserve">
    <value>Allow edting Mail Groups</value>
  </data>
  <data name="Setup_CompanySettings_MailGroups_Add" xml:space="preserve">
    <value>Allow adding a new Mail Group</value>
  </data>
  <data name="Setup_CompanySettings_MailGroups_Delete" xml:space="preserve">
    <value>Allow deleting a Mail Group</value>
  </data>
  <data name="Setup_CompanySettings_MailGroups_Edit" xml:space="preserve">
    <value>Allow editing a Mail Group</value>
  </data>
  <data name="Setup_CompanySettings_PrintedDocuments" xml:space="preserve">
    <value>Allow viewing Printed Documents setup</value>
  </data>
  <data name="Setup_CompanySettings_PrintedDocuments_DocumentFooters_Edit" xml:space="preserve">
    <value>Allow editing Document Footer text</value>
  </data>
  <data name="Setup_CompanySettings_PrintedDocuments_HeaderImage_AddDelete" xml:space="preserve">
    <value>Allow adding and deleting the Document Header Image</value>
  </data>
  <data name="Setup_CompanySettings_Products" xml:space="preserve">
    <value>Allow viewing Products setup</value>
  </data>
  <data name="Setup_CompanySettings_Products_Add" xml:space="preserve">
    <value>Allow adding a new Product</value>
  </data>
  <data name="Setup_CompanySettings_Products_Edit" xml:space="preserve">
    <value>Allow editing a Product</value>
  </data>
  <data name="Setup_CompanySettings_SequenceNumbers" xml:space="preserve">
    <value>Allow viewing Sequence Numbers setup</value>
  </data>
  <data name="Setup_CompanySettings_ShippingMethods" xml:space="preserve">
    <value>Allow viewing Shipping Methods setup</value>
  </data>
  <data name="Setup_CompanySettings_ShippingMethods_Add" xml:space="preserve">
    <value>Allow adding a new Shipping Method</value>
  </data>
  <data name="Setup_CompanySettings_ShippingMethods_Edit" xml:space="preserve">
    <value>Allow editing a Shipping Method</value>
  </data>
  <data name="Setup_CompanySettings_SourcingLinks" xml:space="preserve">
    <value>Allow viewing Sourcing Links setup</value>
  </data>
  <data name="Setup_CompanySettings_SourcingLinks_Add" xml:space="preserve">
    <value>Allow adding a new Sourcing Link</value>
  </data>
  <data name="Setup_CompanySettings_SourcingLinks_Delete" xml:space="preserve">
    <value>Allow deleting a Sourcing Link</value>
  </data>
  <data name="Setup_CompanySettings_SourcingLinks_Edit" xml:space="preserve">
    <value>Allow editing a Sourcing Link</value>
  </data>
  <data name="Setup_CompanySettings_StockLogReasons" xml:space="preserve">
    <value>Allow viewing Stock Log Reasons setup</value>
  </data>
  <data name="Setup_CompanySettings_StockLogReasons_Add" xml:space="preserve">
    <value>Allow adding a new Stock Log Reason</value>
  </data>
  <data name="Setup_CompanySettings_StockLogReasons_Edit" xml:space="preserve">
    <value>Allow editing a Stock Log Reason</value>
  </data>
  <data name="Setup_CompanySettings_Taxes" xml:space="preserve">
    <value>Allow viewing Taxes setup</value>
  </data>
  <data name="Setup_CompanySettings_Taxes_Add" xml:space="preserve">
    <value>Allow adding a new Tax</value>
  </data>
  <data name="Setup_CompanySettings_Taxes_DeleteFutureRate" xml:space="preserve">
    <value>Allow deleting a future Tax Rate</value>
  </data>
  <data name="Setup_CompanySettings_Taxes_Edit" xml:space="preserve">
    <value>Allow editing a Tax</value>
  </data>
  <data name="Setup_CompanySettings_Taxes_EditRates" xml:space="preserve">
    <value>Allow editing Tax Rates</value>
  </data>
  <data name="Setup_CompanySettings_Teams" xml:space="preserve">
    <value>Allow viewing Teams setup</value>
  </data>
  <data name="Setup_CompanySettings_Teams_Add" xml:space="preserve">
    <value>Allow adding a new Team</value>
  </data>
  <data name="Setup_CompanySettings_Teams_Edit" xml:space="preserve">
    <value>Allow editing a Team</value>
  </data>
  <data name="Setup_CompanySettings_Terms" xml:space="preserve">
    <value>Allow viewing Terms setup</value>
  </data>
  <data name="Setup_CompanySettings_Terms_Add" xml:space="preserve">
    <value>Allow adding a new set of Terms</value>
  </data>
  <data name="Setup_CompanySettings_Terms_Edit" xml:space="preserve">
    <value>Allow editing a set of Terms</value>
  </data>
  <data name="Setup_CompanySettings_Warehouses" xml:space="preserve">
    <value>Allow viewing Warehouses setup</value>
  </data>
  <data name="Setup_CompanySettings_Warehouses_Add" xml:space="preserve">
    <value>Allow adding a new Warehouse</value>
  </data>
  <data name="Setup_CompanySettings_Warehouses_Edit" xml:space="preserve">
    <value>Allow editing a Warehouse</value>
  </data>
  <data name="Setup_CompanySettings_Warehouses_SetDefault" xml:space="preserve">
    <value>Allow setting / clearing a default Warehouse for POs and CRMAs</value>
  </data>
  <data name="Setup_GlobalSettings" xml:space="preserve">
    <value>Allow viewing Global Settings</value>
  </data>
  <data name="Setup_GlobalSettings_AppSettings" xml:space="preserve">
    <value>Allow viewing global Application Settings setup</value>
  </data>
  <data name="Setup_GlobalSettings_AppSettings_Edit" xml:space="preserve">
    <value>Allow editing global Application Settings</value>
  </data>
  <data name="Setup_GlobalSettings_CommunicationLogTypes" xml:space="preserve">
    <value>Allow viewing Communication Log Types setup</value>
  </data>
  <data name="Setup_GlobalSettings_CommunicationLogTypes_Add" xml:space="preserve">
    <value>Allow adding a new Communication Log Type</value>
  </data>
  <data name="Setup_GlobalSettings_CommunicationLogTypes_Edit" xml:space="preserve">
    <value>Allow editing a Communication Log Type</value>
  </data>
  <data name="Setup_GlobalSettings_CompanyTypes" xml:space="preserve">
    <value>Allow viewing Company Types setup</value>
  </data>
  <data name="Setup_GlobalSettings_CompanyType_Add" xml:space="preserve">
    <value>Allow adding a new Company Type</value>
  </data>
  <data name="Setup_GlobalSettings_CompanyType_Edit" xml:space="preserve">
    <value>Allow editing a Company Type</value>
  </data>
  <data name="Setup_GlobalSettings_CountingMethods" xml:space="preserve">
    <value>Allow viewing Counting Methods</value>
  </data>
  <data name="Setup_GlobalSettings_CountingMethod_Add" xml:space="preserve">
    <value>Allow adding a new Counting Method</value>
  </data>
  <data name="Setup_GlobalSettings_CountingMethod_Edit" xml:space="preserve">
    <value>Allow editing a Counting Method</value>
  </data>
  <data name="Setup_GlobalSettings_Incoterms" xml:space="preserve">
    <value>Allow viewing Incoterms setup</value>
  </data>
  <data name="Setup_GlobalSettings_AS6081" xml:space="preserve">
    <value>Allow viewing AS6081 setup</value>
  </data>
  <data name="Setup_GlobalSettings_Incoterm_Add" xml:space="preserve">
    <value>Allow adding a new Incoterm</value>
  </data>
  <data name="Setup_GlobalSettings_Incoterm_Edit" xml:space="preserve">
    <value>Allow editing an Incoterm</value>
  </data>
  <data name="Setup_GlobalSettings_IndustryTypes" xml:space="preserve">
    <value>Allow viewing Industry Types setup</value>
  </data>
  <data name="Setup_GlobalSettings_IndustryType_Add" xml:space="preserve">
    <value>Allow adding a new Industry Type</value>
  </data>
  <data name="Setup_GlobalSettings_IndustryType_Edit" xml:space="preserve">
    <value>Allow editing a Industry Type</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCountryList" xml:space="preserve">
    <value>Allow viewing Master Country List setup</value>
  </data>
  <data name="Setup_GlobalSettings_EntertainmentType_Add" xml:space="preserve">
    <value>Allow adding a new Entertainment Type</value>
  </data>
  <data name="Setup_GlobalSettings_EntertainmentType_Edit" xml:space="preserve">
    <value>Allow editing a Entertainment Type</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCountryList_Add" xml:space="preserve">
    <value>Allow adding a new Master Country</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCountryList_Edit" xml:space="preserve">
    <value>Allow editing a Master Country</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCurrencyList" xml:space="preserve">
    <value>Allow viewing Master Currency List setup</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCurrencyList_Add" xml:space="preserve">
    <value>Allow adding a new Master Currency</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCurrencyList_Edit" xml:space="preserve">
    <value>Allow editing a Master Currency</value>
  </data>
  <data name="Setup_GlobalSettings_Packages" xml:space="preserve">
    <value>Allow viewing Packages setup</value>
  </data>
  <data name="Setup_GlobalSettings_Package_Add" xml:space="preserve">
    <value>Allow adding a new Package</value>
  </data>
  <data name="Setup_GlobalSettings_Package_Edit" xml:space="preserve">
    <value>Allow editing a Package</value>
  </data>
  <data name="Setup_GlobalSettings_ProductTypes" xml:space="preserve">
    <value>Allow viewing Product Types setup</value>
  </data>
  <data name="Setup_GlobalSettings_ProductType_Add" xml:space="preserve">
    <value>Allow adding a new Product Type</value>
  </data>
  <data name="Setup_GlobalSettings_ProductType_Edit" xml:space="preserve">
    <value>Allow editing a Product Type</value>
  </data>
  <data name="Setup_GlobalSettings_Reasons" xml:space="preserve">
    <value>Allow viewing Reasons setup</value>
  </data>
  <data name="Setup_GlobalSettings_Reason_Add" xml:space="preserve">
    <value>Allow adding a new Reason</value>
  </data>
  <data name="Setup_GlobalSettings_Reason_Edit" xml:space="preserve">
    <value>Allow editing a Reason</value>
  </data>
  <data name="Setup_SecuritySettings" xml:space="preserve">
    <value>Allow viewing Security Settings</value>
  </data>
  <data name="Setup_SecuritySettings_GeneralPermissions_Edit" xml:space="preserve">
    <value>Allow editing General Permissions</value>
  </data>
  <data name="Setup_SecuritySettings_GroupMembers_Edit" xml:space="preserve">
    <value>Allow editing Security Group Members</value>
  </data>
  <data name="Setup_SecuritySettings_Groups" xml:space="preserve">
    <value>Allow viewing Security Groups setup</value>
  </data>
  <data name="Setup_SecuritySettings_Groups_Add" xml:space="preserve">
    <value>Allow adding a new Security Group</value>
  </data>
  <data name="Setup_SecuritySettings_Groups_Clone" xml:space="preserve">
    <value>Allow cloning a Security Group</value>
  </data>
  <data name="Setup_SecuritySettings_Groups_Delete" xml:space="preserve">
    <value>Allow deleting a Security Group</value>
  </data>
  <data name="Setup_SecuritySettings_Groups_Edit" xml:space="preserve">
    <value>Allow editing a Security Group</value>
  </data>
  <data name="Setup_SecuritySettings_ReportPermissions_Edit" xml:space="preserve">
    <value>Allow editing Report Permissions</value>
  </data>
  <data name="Setup_SecuritySettings_Users" xml:space="preserve">
    <value>Allow viewing Security Users setup</value>
  </data>
  <data name="Setup_SecuritySettings_Users_Add" xml:space="preserve">
    <value>Allow adding a new User</value>
  </data>
  <data name="Setup_SecuritySettings_Users_Disable" xml:space="preserve">
    <value>Allow disabling a User</value>
  </data>
  <data name="Setup_SecuritySettings_Users_Edit" xml:space="preserve">
    <value>Allow editing a User</value>
  </data>
  <data name="Setup_SecuritySettings_Users_Enable" xml:space="preserve">
    <value>Allow enabling a User</value>
  </data>
  <data name="Setup_SecuritySettings_Users_Groups_Edit" xml:space="preserve">
    <value>Allow editing a user's Group Membership</value>
  </data>
  <data name="Setup_SecuritySettings_Users_Profile_ChangePassword" xml:space="preserve">
    <value>Allow changing a User's Password</value>
  </data>
  <data name="Setup_SecuritySettings_Users_Profile_Edit" xml:space="preserve">
    <value>Allow editing a User Profile</value>
  </data>
  <data name="Setup_SecuritySettings_Users_Profile_ResetPassword" xml:space="preserve">
    <value>Allow resetting a User's Password</value>
  </data>
  <data name="Setup_SecuritySettings_Users_TransferAccounts" xml:space="preserve">
    <value>Allow transferring a user's accounts to another user</value>
  </data>
  <data name="ViewAllStatistics" xml:space="preserve">
    <value>Allow viewing Company Statistics</value>
  </data>
  <data name="ViewDifferentUsersHomepage" xml:space="preserve">
    <value>Allow viewing a different User's homepage</value>
  </data>
  <data name="ViewReport" xml:space="preserve">
    <value>Allow viewing "{0}"</value>
  </data>
  <data name="ViewTopSalesman" xml:space="preserve">
    <value>Allow viewing top Salesperson figures</value>
  </data>
  <data name="WarehouseSection_View" xml:space="preserve">
    <value>Allow viewing Warehouse section</value>
  </data>
  <data name="Warehouse_GoodsIn_Add" xml:space="preserve">
    <value>Allow adding a new Goods In Note</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Delete" xml:space="preserve">
    <value>Allow deleting a Goods In line</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Edit" xml:space="preserve">
    <value>Allow editing a Goods In line</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_EditAfterInspection" xml:space="preserve">
    <value>Allow Warehouse to Edit data within GI Screen after Release</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Inspect" xml:space="preserve">
    <value>Allow release a Goods In line</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Print" xml:space="preserve">
    <value>Allow printing a Goods In line</value>
  </data>
  <data name="Warehouse_GoodsIn_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Warehouse_GoodsIn_MainInfo_EditAccountsInfo" xml:space="preserve">
    <value>Allow editing Goods In Accounting Info</value>
  </data>
  <data name="Warehouse_GoodsIn_MainInfo_EditExportedFlag" xml:space="preserve">
    <value>Allow editing Goods In Exported flag</value>
  </data>
  <data name="Warehouse_Lot_Add" xml:space="preserve">
    <value>Allow adding a new Lot</value>
  </data>
  <data name="Warehouse_Lot_Items_Services_Delete" xml:space="preserve">
    <value>Allow deleting unallocated Services</value>
  </data>
  <data name="Warehouse_Lot_Items_Services_Transfer" xml:space="preserve">
    <value>Allow transferring Services</value>
  </data>
  <data name="Warehouse_Lot_Items_Stock_Delete" xml:space="preserve">
    <value>Allow deleting unallocated Stock</value>
  </data>
  <data name="Warehouse_Lot_Items_Stock_Transfer" xml:space="preserve">
    <value>Allow transferring Stock</value>
  </data>
  <data name="Warehouse_Lot_MainInfo_Delete" xml:space="preserve">
    <value>Allow deleting a Lot</value>
  </data>
  <data name="Warehouse_Lot_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Warehouse_ReceiveCRMA" xml:space="preserve">
    <value>Allow receiving a Customer RMA</value>
  </data>
  <data name="Warehouse_ReceiveCRMA_Receive" xml:space="preserve">
    <value>Allow receiving a Customer RMA Line via Goods In process</value>
  </data>
  <data name="Warehouse_ReceivePO" xml:space="preserve">
    <value>Allow receiving a Purchase Order</value>
  </data>
  <data name="Warehouse_ReceivePO_Receive" xml:space="preserve">
    <value>Allow receiving a Purchase Order Line via Goods In process</value>
  </data>
  <data name="Warehouse_Service_Add" xml:space="preserve">
    <value>Allow adding a new Service</value>
  </data>
  <data name="Warehouse_Service_Allocations_Deallocate" xml:space="preserve">
    <value>Allow deallocating a Service</value>
  </data>
  <data name="Warehouse_Service_MainInfo_Delete" xml:space="preserve">
    <value>Allow deleting a Service</value>
  </data>
  <data name="Warehouse_Service_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Warehouse_ShipSO" xml:space="preserve">
    <value>Allow shipping a Sales Order</value>
  </data>
  <data name="Warehouse_ShipSO_Ship" xml:space="preserve">
    <value>Allow shipping a SalesOrder Line</value>
  </data>
  <data name="Warehouse_ShipSRMA" xml:space="preserve">
    <value>Allow shipping a Supplier RMA</value>
  </data>
  <data name="Warehouse_ShipSRMA_Ship" xml:space="preserve">
    <value>Allow shipping a Supplier RMA Line</value>
  </data>
  <data name="Warehouse_Stock_Add" xml:space="preserve">
    <value>Allow adding a new Stock Item</value>
  </data>
  <data name="Warehouse_Stock_Allocations_Deallocate" xml:space="preserve">
    <value>Allow deallocating a Stock Item</value>
  </data>
  <data name="Warehouse_Stock_Images_AddDelete" xml:space="preserve">
    <value>Allow adding or deleting a Stock Image</value>
  </data>
  <data name="Warehouse_Stock_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Warehouse_Stock_MainInfo_Quarantine" xml:space="preserve">
    <value>Allow quarantining Stock and GI line</value>
  </data>
  <data name="Warehouse_Stock_MainInfo_Release" xml:space="preserve">
    <value>Allow releasing a Stock Item from quarantine</value>
  </data>
  <data name="Warehouse_Stock_MainInfo_Split" xml:space="preserve">
    <value>Allow splitting a Stock Item</value>
  </data>
  <data name="Orders_PurchaseOrder_MainInfo_EditTax" xml:space="preserve">
    <value>Allow modification of default tax</value>
  </data>
  <data name="Orders_PurchaseOrder_MainInfo_EditCurrencyAndTerms" xml:space="preserve">
    <value>Allow modification of default currency and terms</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_EditTax" xml:space="preserve">
    <value>Allow modification of default tax</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_EditCurrencyAndTerms" xml:space="preserve">
    <value>Allow modification of default currency and terms</value>
  </data>
  <data name="Orders_Invoice_MainInfo_EditDivision" xml:space="preserve">
    <value>Allow editing accounts related fields</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_EditFieldsAfterAuthorisation" xml:space="preserve">
    <value>Allow editing fields after authorisation</value>
  </data>
  <data name="Contact_CompanyDetail_Addresses_Tax_AddEdit" xml:space="preserve">
    <value>Allow Add/Edit Tax information for a company</value>
  </data>
  <data name="Warehouse_Stock_PDFDocument_Add" xml:space="preserve">
    <value>Allow adding a PDF document</value>
  </data>
  <data name="Warehouse_Stock_PDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting a PDF document</value>
  </data>
  <data name="Contact_CompanyDetail_PDFDocument_Add" xml:space="preserve">
    <value>Allow adding a PDF document</value>
  </data>
  <data name="Contact_CompanyDetail_PDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting a PDF document</value>
  </data>
  <data name="Orders_CRMA_PDFDocument_Add" xml:space="preserve">
    <value>Allow adding a PDF document</value>
  </data>
  <data name="Orders_CRMA_PDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting a PDF document</value>
  </data>
  <data name="Orders_PurchaseOrder_PDFDocument_Add" xml:space="preserve">
    <value>Allow adding a PDF document</value>
  </data>
  <data name="Orders_PurchaseOrder_PDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting a PDF document</value>
  </data>
  <data name="Orders_SalesOrder_PDFDocument_Add" xml:space="preserve">
    <value>Allow adding a PDF Document</value>
  </data>
  <data name="Orders_SalesOrder_PDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting a PDF Document</value>
  </data>
  <data name="Orders_SRMA_PDFDocument_Add" xml:space="preserve">
    <value>Allow adding a PDF document</value>
  </data>
  <data name="Orders_SRMA_PDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting a PDF document</value>
  </data>
  <data name="Warehouse_GoodsIn_PDFDocument_Add" xml:space="preserve">
    <value>Allow adding a PDF document</value>
  </data>
  <data name="Warehouse_GoodsIn_PDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting a PDF document</value>
  </data>
  <data name="Setup_CompanySettings_EmailComposer" xml:space="preserve">
    <value>Allow viewing Email Composer</value>
  </data>
  <data name="Setup_CompanySettings_EmailComposer_Add_Edit" xml:space="preserve">
    <value>Allow add/edit Email Composer</value>
  </data>
  <data name="Orders_Invoice_PDFDocument_Add" xml:space="preserve">
    <value>Allow adding a PDF document</value>
  </data>
  <data name="Orders_Invoice_PDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting a PDF document</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Edit_PurchasePrice" xml:space="preserve">
    <value>Allow editing Purchase Request in Goods In Line</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Edit_ShipInCost" xml:space="preserve">
    <value>Allow editing Ship In Cost in Goods In Line</value>
  </data>
  <data name="Orders_Sourcing_AddOffer" xml:space="preserve">
    <value>Allow adding an Offer</value>
  </data>
  <data name="Orders_Sourcing_AddStockInfo" xml:space="preserve">
    <value>Allow adding Sourcing Info</value>
  </data>
  <data name="Orders_Sourcing_AddTrusted" xml:space="preserve">
    <value>Allow adding a Trusted</value>
  </data>
  <data name="Orders_Sourcing_EditStockInfo" xml:space="preserve">
    <value>Allow editing Sourcing Info</value>
  </data>
  <data name="Orders_CustomerRequirement_Sourcing_AddOffer" xml:space="preserve">
    <value>Allow adding an Offer</value>
  </data>
  <data name="Orders_CustomerRequirement_Sourcing_AddStockInfo" xml:space="preserve">
    <value>Allow adding a Stock Info</value>
  </data>
  <data name="Orders_CustomerRequirement_Sourcing_AddTrusted" xml:space="preserve">
    <value>Allow adding a Trusted</value>
  </data>
  <data name="Orders_CustomerRequirement_Sourcing_EditStockInfo" xml:space="preserve">
    <value>Allow editing a Stock Info</value>
  </data>
  <data name="Contact_AllCompanies_View" xml:space="preserve">
    <value>All companies</value>
  </data>
  <data name="Contact_Contacts_View" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="Contact_Customers_View" xml:space="preserve">
    <value>Customers</value>
  </data>
  <data name="Contact_Prospects_View" xml:space="preserve">
    <value>Prospects</value>
  </data>
  <data name="Contact_Suppliers_View" xml:space="preserve">
    <value>Suppilers</value>
  </data>
  <data name="Orders_CreditNotes_View" xml:space="preserve">
    <value>Credit Notes</value>
  </data>
  <data name="Orders_CustomerRMA_View" xml:space="preserve">
    <value>Customer RMAs</value>
  </data>
  <data name="Orders_DebitNotes_View" xml:space="preserve">
    <value>Debit Notes</value>
  </data>
  <data name="Orders_Invoices_View" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="Orders_PurchaseOrders_View" xml:space="preserve">
    <value>Purchase Orders</value>
  </data>
  <data name="Orders_PurchaseRequisitions_View" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="Orders_Quotes_View" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="Orders_Requirements_View" xml:space="preserve">
    <value>Requirements</value>
  </data>
  <data name="Orders_SalesOrders_View" xml:space="preserve">
    <value>SalesOrders</value>
  </data>
  <data name="Orders_SupplierRMA_View" xml:space="preserve">
    <value>Supplier RMAs</value>
  </data>
  <data name="TabSecurity" xml:space="preserve">
    <value>Permissions for Tab Security</value>
  </data>
  <data name="Orders_Sourcing_View" xml:space="preserve">
    <value>Allow viewing sourcing section</value>
  </data>
  <data name="Orders_CustomerRequirement_Sourcing_View" xml:space="preserve">
    <value>Allow viewing sourcing</value>
  </data>
  <data name="Orders_Invoice_BulkEmail_Email" xml:space="preserve">
    <value>Allow invoice bulk email</value>
  </data>
  <data name="Contact_CompanyDetail_AllCompanies_View" xml:space="preserve">
    <value>Allow viewing Company Detail</value>
  </data>
  <data name="Contact_CompanyDetail_Customer_View" xml:space="preserve">
    <value>Allow viewing Customer Detail</value>
  </data>
  <data name="Contact_CompanyDetail_Prospects_View" xml:space="preserve">
    <value>Allow viewing Prospects Detail</value>
  </data>
  <data name="Contact_CompanyDetail_Supplier_View" xml:space="preserve">
    <value>Allow viewing Supplier Detail</value>
  </data>
  <data name="Contact_ContactDetail_View" xml:space="preserve">
    <value>Allow viewing Contact Detail</value>
  </data>
  <data name="Contact_ManufacturerDetail_View" xml:space="preserve">
    <value>Allow viewing Manufacturer Detail</value>
  </data>
  <data name="Orders_CreditNote_View" xml:space="preserve">
    <value>Allow viewing Credit Note Detail</value>
  </data>
  <data name="Orders_CRMA_View" xml:space="preserve">
    <value>Allow viewing Customer RMA Detail</value>
  </data>
  <data name="Orders_CustomerRequirement_View" xml:space="preserve">
    <value>Allow viewing Customer Requirement Detail</value>
  </data>
  <data name="Orders_DebitNote_View" xml:space="preserve">
    <value>Allow viewing Debit Note Detail</value>
  </data>
  <data name="Orders_Invoice_View" xml:space="preserve">
    <value>Allow viewing Invoice Detail</value>
  </data>
  <data name="Orders_PurchaseOrder_View" xml:space="preserve">
    <value>Allow viewing Purchase Order Detail</value>
  </data>
  <data name="Orders_Quote_View" xml:space="preserve">
    <value>Allow viewing Quote Detail</value>
  </data>
  <data name="Orders_SalesOrder_View" xml:space="preserve">
    <value>Allow viewing Sales Order Detail</value>
  </data>
  <data name="Orders_SRMA_View" xml:space="preserve">
    <value>Allow viewing Supplier RMA Detail</value>
  </data>
  <data name="Warehouse_GoodsIn_View" xml:space="preserve">
    <value>Allow viewing GoodsIn Detail</value>
  </data>
  <data name="Warehouse_Lot_View" xml:space="preserve">
    <value>Allow viewing Lot Detail</value>
  </data>
  <data name="Warehouse_Service_View" xml:space="preserve">
    <value>Allow viewing Service Detail</value>
  </data>
  <data name="Warehouse_Stock_View" xml:space="preserve">
    <value>Allow viewing Stock Detail</value>
  </data>
  <data name="Warehouse_SupplierInvoice_Add" xml:space="preserve">
    <value>Allow adding Supplier Invoice</value>
  </data>
  <data name="Warehouse_SupplierInvoice_Lines_Add" xml:space="preserve">
    <value>Allow adding Supplier Invoice Line</value>
  </data>
  <data name="Warehouse_SupplierInvoice_Lines_Delete" xml:space="preserve">
    <value>Allow deleting Supplier Invoice Line</value>
  </data>
  <data name="Warehouse_SupplierInvoice_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Main Information</value>
  </data>
  <data name="Warehouse_SupplierInvoice_MainInfo_Notify" xml:space="preserve">
    <value>Allow notify Supplier Invoice</value>
  </data>
  <data name="Warehouse_SupplierInvoice_URNNumber_Edit" xml:space="preserve">
    <value>Allow editing URN Number</value>
  </data>
  <data name="Warehouse_SupplierInvoice_View" xml:space="preserve">
    <value>Allow viewing Supplier Invoice</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_NPR_Delete" xml:space="preserve">
    <value>Allow deleting NPR Report</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_NPR_Authorise" xml:space="preserve">
    <value>Allow authorise NPR</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_CompanyType_Edit" xml:space="preserve">
    <value>Allow editing Company Type/Quality Notes</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_Paid_Add" xml:space="preserve">
    <value>Allow adding Paid Box</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_Paid_Edit" xml:space="preserve">
    <value>Allow editing Paid Box</value>
  </data>
  <data name="Setup_CompanySettings_Printer" xml:space="preserve">
    <value>Allow viewing Printer setup</value>
  </data>
  <data name="Setup_CompanySettings_Printer_Add" xml:space="preserve">
    <value>Allow adding a new Printer</value>
  </data>
  <data name="Setup_CompanySettings_Printer_Edit" xml:space="preserve">
    <value>Allow editing a Printer</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_NPR_NewNPR" xml:space="preserve">
    <value>Allow adding a new NPR</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_AccountNotes_Edit" xml:space="preserve">
    <value>Allow edit Account Notes</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_NPR_CompletedBy" xml:space="preserve">
    <value>Allow completing NPR</value>
  </data>
  <data name="Setup_CompanySettings_LocalCurrencies" xml:space="preserve">
    <value>Allow viewing local Currencies setup</value>
  </data>
  <data name="Setup_CompanySettings_LocalCurrencies_Add" xml:space="preserve">
    <value>Allow adding a new local Currencies</value>
  </data>
  <data name="Setup_CompanySettings_LocalCurrencies_Edit" xml:space="preserve">
    <value>Allow editing a local Currencies</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_NPR_AfterAuthorise" xml:space="preserve">
    <value>Allow edit NPR after Authorise</value>
  </data>
  <data name="Setup_GlobalSettings_Certificate" xml:space="preserve">
    <value>Allow viewing Certificate &amp; Certificate Category</value>
  </data>
  <data name="Setup_GlobalSettings_Certificate_Add" xml:space="preserve">
    <value>Allow adding a Certificate &amp; Certificate Category</value>
  </data>
  <data name="Setup_GlobalSettings_Certificate_Edit" xml:space="preserve">
    <value>Allow editing a Certificate &amp; Certificate Category</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_Certificates_AddEdit" xml:space="preserve">
    <value>Allow Add/Edit certificates</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Inspection" xml:space="preserve">
    <value>Allow a Goods In line inspection</value>
  </data>
  <data name="Warehouse_Stock_Provision" xml:space="preserve">
    <value>Allow stock provision</value>
  </data>
  <data name="Warehouse_Lot_StockProvision" xml:space="preserve">
    <value>Allow lot stock provision</value>
  </data>
  <data name="Setup_CompanySettings_LabelPath" xml:space="preserve">
    <value>Allow viewing nice label path</value>
  </data>
  <data name="Setup_CompanySettings_LabelPath_Add" xml:space="preserve">
    <value>Allow adding a nice label path</value>
  </data>
  <data name="Setup_CompanySettings_LabelPath_Edit" xml:space="preserve">
    <value>Allow editing nice label path</value>
  </data>
  <data name="Orders_PurchaseOrder_EPR_RefAuthorise" xml:space="preserve">
    <value>Allow authorise EPR</value>
  </data>
  <data name="Orders_PurchaseOrder_EPR_Complete" xml:space="preserve">
    <value>Allow completing EPR</value>
  </data>
  <data name="Orders_PurchaseOrder_EPR_EditAfterAuthorise" xml:space="preserve">
    <value>Allow edit EPR after Authorise</value>
  </data>
  <data name="Orders_PurchaseOrder_EPR_Delete" xml:space="preserve">
    <value>Allow deleting EPR</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Edit_DateRequired" xml:space="preserve">
    <value>Allow edit Date Required after post</value>
  </data>
  <data name="Contact_CompanyDetail_PurchasingInfo_SupplierOnStop" xml:space="preserve">
    <value>Allow editing Supplier On Stop</value>
  </data>
  <data name="Orders_Invoice_EmailInvoice" xml:space="preserve">
    <value>Access to email Inv/Commercial/Inv inc CofC</value>
  </data>
  <data name="Orders_Invoice_PrintInvoice" xml:space="preserve">
    <value>Access to print Inv/Commercial/Inv inc CofC</value>
  </data>
  <data name="DashboardSection_View" xml:space="preserve">
    <value>Allow viewing Dashboards</value>
  </data>
  <data name="Orders_SalesOrder_Lines_Edit_PromiseDateAfterAuthorisation" xml:space="preserve">
    <value>Allow edit Date Promised after checked</value>
  </data>
  <data name="Contact_Manufacturer_MainInfo_AllowInactive" xml:space="preserve">
    <value>Allow active/inactive Manufacturer</value>
  </data>
  <data name="Orders_SalesOrder_SORPDFDocument_Delete" xml:space="preserve">
    <value>Allow deleting SOR PDF</value>
  </data>
  <data name="Orders_SalesOrder_Lines_EditDatePromisedAfterChecked" xml:space="preserve">
    <value>Allow edit date promised between current month and end of date promised</value>
  </data>
  <data name="Orders_SalesOrder_PDFDocument_DeleteAfterAuthorise" xml:space="preserve">
    <value>Allow delete Customer PO pdf after authorised</value>
  </data>
  <data name="Setup_GlobalSettings_EightDCode" xml:space="preserve">
    <value>Allow add/edit Root Cause Code</value>
  </data>
  <data name="Orders_BOM_View" xml:space="preserve">
    <value>Allow viewing HUBRFQ Detail</value>
  </data>
  <data name="Warehouse_GIStockDocument" xml:space="preserve">
    <value>Allow viewing Stock/GoodsIn Document</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_EditPriceWithoutUnpost" xml:space="preserve">
    <value>Allow editing Purchase Order line after post</value>
  </data>
  <data name="Orders_PurchaseOrder_MainInfo_EditSupplier" xml:space="preserve">
    <value>Allow edit purchase order supplier </value>
  </data>
  <data name="Orders_InternalPurchaseOrder_Line_EditPrice" xml:space="preserve">
    <value>Allow editing Internal Purchase Order Line Price</value>
  </data>
  <data name="Contact_CompanyDetail_SalesInfo_CreditStatus" xml:space="preserve">
    <value>Allow editing sales information credit status</value>
  </data>
  <data name="Orders_SRMA_View_PDFDocument" xml:space="preserve">
    <value>Allow viewing IPO Supplier RMA PDF Nugget</value>
  </data>
  <data name="Warehouse_GoodsIn_Documents_View" xml:space="preserve">
    <value>Allow viewing IPO GoodsIn PDF Nugget</value>
  </data>
  <data name="Orders_SalesOrder_AllowCheckedCompanyOnStop" xml:space="preserve">
    <value>Allow SO authorization, if Customer On Stop</value>
  </data>
  <data name="Orders_QuoteAndSO_Edit_AS9120" xml:space="preserve">
    <value>Allow editing AS9120 from Quote and SO page</value>
  </data>
  <data name="Orders_DebitNote_View_PrintAndEmail" xml:space="preserve">
    <value>Allow viewing Print/Email HUB Debit Note</value>
  </data>
  <data name="Orders_SRMA_View_HUBPrintAndEmail" xml:space="preserve">
    <value>Allow viewing Print/Email HUB Supplier RMA</value>
  </data>
  <data name="Orders_ClientInvoice_Add" xml:space="preserve">
    <value>Allow adding a new Client Invoice</value>
  </data>
  <data name="Orders_ClientInvoice_MainInfo_Edit" xml:space="preserve">
    <value>Allow editing Client Invoice </value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_Edit_SupplierHUB" xml:space="preserve">
    <value>Allow editing HUB supplier</value>
  </data>
  <data name="Orders_CreditNote_MainInfo_Export_Release" xml:space="preserve">
    <value>Allow making for export /releasing for edit</value>
  </data>
  <data name="ViewOnlyMyReports" xml:space="preserve">
    <value>Allow viewing Team, Division or Company reports</value>
  </data>
  <data name="Orders_PurchaseOrder_Lines_Can_Release" xml:space="preserve">
    <value>Allow release Purchase Order Line locked by EPR</value>
  </data>
  <data name="Orders_SalesOrder_MainInfo_Can_Pay_By_CreditCard" xml:space="preserve">
    <value>Allow adding Credit Card payment </value>
  </data>
  <data name="Orders_Invoice_PrintEmailAfterExport" xml:space="preserve">
    <value>Allow Print Invoice After Export</value>
  </data>
  <data name="Warehouse_ShipSO_SurchargeWavedOff" xml:space="preserve">
    <value>Allow shipping surcharge waived off in shipping and invoice header</value>
  </data>
  <data name="Orders_Sourcing_AddStockImportTool" xml:space="preserve">
    <value>Allow adding Stock Import Tool</value>
  </data>
  <data name="Orders_SalesOrder_Lines_EditAll" xml:space="preserve">
    <value>Allow editing all sales order</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_SplitGI" xml:space="preserve">
    <value>Allow Spliting Goods In line</value>
  </data>
  <data name="Warehouse_IHS_Allow_Export_CSV" xml:space="preserve">
    <value>Allow export IHS Catalogue</value>
  </data>
  <data name="Warehouse_IHS_Allow_View_AvgPrice" xml:space="preserve">
    <value>Allow view IHS Catalogue Average Price</value>
  </data>
  <data name="Orders_Invoice_MainInfo_EditShippedDate" xml:space="preserve">
    <value>Allow editing Invoice Shipped Date</value>
  </data>
  <data name="Orders_PurchaseOrder_MainInfo_ApprovePOAfterChecked" xml:space="preserve">
    <value>Allow approve Purchase Order without allocating to Sales Order</value>
  </data>
  <data name="Utility_BOM_Import" xml:space="preserve">
    <value>Allow viewing Utility BOM Import section</value>
  </data>
  <data name="Utility_Offer_Import" xml:space="preserve">
    <value>Allow viewing Utility Offer Import section</value>
  </data>
  <data name="Utility_XMatch_Tool" xml:space="preserve">
    <value>Allow viewing XMatch Tool</value>
  </data>
  <data name="Warehouse_SupplierInvoice_MainInfo_CanExport" xml:space="preserve">
    <value>Allow Export </value>
  </data>
  <data name="Orders_PurchaseOrder_AddCurrencyAndTermsForUnapproved" xml:space="preserve">
    <value>Allow modification of default currency and terms for unapproved supplier</value>
  </data>
  <data name="Orders_PurchaseOrder_EditCurrencyAndTermsForUnapproved" xml:space="preserve">
    <value>Allow modification of default currency and terms for unapproved supplier</value>
  </data>
  <data name="Orders_PurchaseOrder_ExportToExcel" xml:space="preserve">
    <value>Allow Export To Excel</value>
  </data>
  <data name="Orders_SalesOrder_ExportToExcle" xml:space="preserve">
    <value>Allow Export To Excel</value>
  </data>
  <data name="Orders_Invoice_MainInfo_Edit_Term" xml:space="preserve">
    <value>Allow editing Invoice Term</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Edit_Approval" xml:space="preserve">
    <value>Allow viewing Approval In Edit</value>
  </data>
  <data name="Warehouse_GoodsIn_ShortShipmentDetails_Edit" xml:space="preserve">
    <value>Allow Purchasing to Edit Change Status in short Shipment</value>
  </data>
  <data name="Warehouse_GoodsIn_ShortShipmentDetails_ClientView" xml:space="preserve">
    <value>Allow permission to view client in short Shipment</value>
  </data>
  <data name="Warehouse_GoodsIn_ShortShipmentDetails_DownloadXLS" xml:space="preserve">
    <value>Allow downlaod excel report in short Shipment</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_AccountPermission" xml:space="preserve">
    <value>Allow Finance to Edit Data within GI Screen after Release</value>
  </data>
  <data name="DashboardPowerBI_ReportAllow" xml:space="preserve">
    <value>Allow viewing BI Report</value>
  </data>
  <data name="Orders_SalesOrder_Authorise_RequestApproval" xml:space="preserve">
    <value>Allow Send Request Approval</value>
  </data>
  <data name="Orders_SalesOrder_Authorise_EnableSendSONotify" xml:space="preserve">
    <value>Allow adding/removing Notify Sales Person on Request Approval</value>
  </data>
  <data name="Warehouse_GoodsIn_ShortShipmentDetails_ChangeStatusForCancelClose" xml:space="preserve">
    <value>Allow user to change the status of a Short Shipment to cancel / close</value>
  </data>
  <data name="Warehouse_GoodsIn_ShortShipmentDetails_ChangeStatusForShipAndHold" xml:space="preserve">
    <value>Allow user to change the status of a Short Shipment to Ship Partial / Hold for Remaining</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Edit_DownloadPDF" xml:space="preserve">
    <value>Allow download PDF in Download tab of Goods In line</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_Edit_DownloadWord" xml:space="preserve">
    <value>Allow download Word in Download tab of Goods In line</value>
  </data>
  <data name="Contact_Manufacturer_Group_Add" xml:space="preserve">
    <value>Allow Add Manufacturer Group Code</value>
  </data>
  <data name="Orders_SalesOrder_Lines_ECCNLog" xml:space="preserve">
    <value>Allow viewing ECCN Log</value>
  </data>
  <data name="Warehouse_Stock_PurchasePrice_View" xml:space="preserve">
    <value>Allow viewing Purchase Price After Resale Price</value>
  </data>
  <data name="Warehouse_Lot_Available" xml:space="preserve">
    <value>Allow Show Stock Is Available</value>
  </data>
  <data name="Warehouse_Stock_MainInfo_EditLot_On_Hold" xml:space="preserve">
    <value>Allow editing Main Information On Stock Hold</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_DeleteAttachmentPermission" xml:space="preserve">
    <value>Allow delete attachments in GI Lines on attachments tab </value>
  </data>
  <data name="Orders_QuoteLines_CanAdd_FromLot" xml:space="preserve">
    <value>Allow adding a new Line from Lot</value>
  </data>
  <data name="Orders_QuoteLines_CanDelete_FromLot" xml:space="preserve">
    <value>Allow deleting a Quote Line</value>
  </data>
  <data name="SalesBomManagerSheet" xml:space="preserve">
    <value>Allow viewing Sales Bom Manager Sheet</value>
  </data>
  <data name="DashboardPowerBISales_ReportAllow" xml:space="preserve">
    <value>Allow viewing Power BI Sales Report</value>
  </data>
  <data name="DashboardPowerBIStock_ReportAllow" xml:space="preserve">
    <value>Allow viewing Power BI Sales Performance Report</value>
  </data>
  <data name="Warehouse_SupplierInvoice_AddUnReleasedGI" xml:space="preserve">
    <value>Allow adding unreleased GI lines on Supplier Invoice</value>
  </data>
  <data name="Orders_SalesOrder_ExportApproval_RequestApproval" xml:space="preserve">
    <value>Allow Export Approval</value>
  </data>
  <data name="Orders_SalesOrder_ExportApproval_SendApproval" xml:space="preserve">
    <value>Allow Send Export Approval</value>
  </data>
  <data name="Orders_SoLines_CanAdd_FromLot" xml:space="preserve">
    <value>Allow to Create SO Line From Lot</value>
  </data>
  <data name="Orders_SalesOrder_ExportApproval_EditApproval" xml:space="preserve">
    <value>Allow Edit/Edit All Export Approval Details</value>
  </data>
  <data name="Utility_Reverse_Logistics_Import_Tool" xml:space="preserve">
    <value>Allow viewing Reverse Logistics Import Tool</value>
  </data>
  <data name="Utility_Strategic_Offers_Import_Tool" xml:space="preserve">
    <value>Allow viewing Strategic Offers Import Tool</value>
  </data>
  <data name="WarehouseSection_GILines_EditInspection" xml:space="preserve">
    <value>Allow to Reopen an Inspection once completed</value>
  </data>
  <data name="WarehouseSection_GILines_QulityApprovalPermission" xml:space="preserve">
    <value>Allow Quality Approval for GI Lines</value>
  </data>
  <data name="Warehouse_GoodsIn_Lines_MANAGEAPPROVER" xml:space="preserve">
    <value>Allow Manage Approver for GI Line Query</value>
  </data>
  <data name="Warehouse_GoodsIn_ShortShipmentDetails_Add" xml:space="preserve">
    <value>Allow Permission to Add short Shipment</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_PremierCustomer_Edit" xml:space="preserve">
    <value>Allow Add/Edit premier Customer</value>
  </data>
  <data name="WarehouseSection_EditGILines_AfterStatInspection" xml:space="preserve">
    <value>Allow edit button to be visible at all times regardless of inspection state</value>
  </data>
  <data name="Utility_PriceQuote_Import" xml:space="preserve">
    <value>Allow viewing Utility Price Quote section</value>
  </data>
  <data name="Orders_Sourcing_AssignHUBRFQ" xml:space="preserve">
    <value>Allow this security group to be assigned HUB RFQS</value>
  </data>
  <data name="Setup_GlobalSettings_AS6081_Add" xml:space="preserve">
    <value>Allow adding AS6081 setup data</value>
  </data>
  <data name="Setup_GlobalSettings_AS6081_Delete" xml:space="preserve">
    <value>Allow deleting AS6081 setup data</value>
  </data>
  <data name="Setup_GlobalSettings_AS6081_Edit" xml:space="preserve">
    <value>Allow editing AS6081 setup data</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_PurchasingNotes_Edit" xml:space="preserve">
    <value>Allow edit Purchasing Notes</value>
  </data>
  <data name="Setup_GlobalSettings_PDFDocumentFileSize" xml:space="preserve">
    <value>Allow viewing Document File Size setup</value>
  </data>
  <data name="Setup_GlobalSettings_PDFDocumentFileSize_Add" xml:space="preserve">
    <value>Allow adding a new Document File Size</value>
  </data>
  <data name="Setup_GlobalSettings_PDFDocumentFileSize_Edit" xml:space="preserve">
    <value>Allow editing a Document File Size</value>
  </data>
  <data name="hypGlobalSettings_EntertainmentType" xml:space="preserve">
    <value>Allow EntertainmentType</value>
  </data>
  <data name="Setup_GlobalSettings_EntertainmentType" xml:space="preserve">
    <value>Allow viewing Entertainment Type</value>
  </data>
  <data name="Contact_CompanyDetail_GSA_Edit" xml:space="preserve">
    <value>Allow managing global sales user</value>
  </data>
  <data name="Orders_Sourcing_Edit_StrategicOffers_VirtualCostPrice" xml:space="preserve">
    <value>Allow Permission to Edit Strategic Offers Virtual Cost Price</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_InsuranceCertificates_AddEdit" xml:space="preserve">
    <value>Allow Add/Edit Insurance certificates</value>
  </data>
  <data name="Orders_SalesOrder_SOAuth_AllowReadyToShip" xml:space="preserve">
    <value>Allow viewing Allow Ready To Ship? tickbox</value>
  </data>
  <data name="Orders_Sourcing_Edit_ReverseLogistic_Bulk" xml:space="preserve">
    <value>Allow Permission to Edit Bulk Button on Reverse Logistic Nugget</value>
  </data>
  <data name="Contact_ContactDetail_Finance_Link_Accounts" xml:space="preserve">
    <value>Allow Permission to Link Accounts</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_CustomerAPI_Add" xml:space="preserve">
    <value>Allow Permission to Add Customer API</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_CustomerAPI_BomMapping" xml:space="preserve">
    <value>Allow Permission to Add Customer API Bom Mapping</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_CustomerAPI_Edit" xml:space="preserve">
    <value>Allow Permission to Edit Customer API</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_CustomerAPI_SupplierImport" xml:space="preserve">
    <value>Allow Permission to Add Customer API Supplier Import</value>
  </data>
  <data name="Orders_Sourcing_HideEdit_ReverseLogistic_Bulk" xml:space="preserve">
    <value>Allow Permission to View Bulk Edit Reverse Logistic</value>
  </data>
  <data name="Contact_MainInfo_CreditApplicationForm_ApproveReject_Accounts" xml:space="preserve">
    <value>Allow Approve and Reject Credit Application Form</value>
  </data>
  <data name="Contact_MainInfo_CreditApplicationForm_CreateSend_SalesPerson" xml:space="preserve">
    <value>Allow Create and Send Credit Application Form</value>
  </data>
  <data name="Setup_GlobalSettings_PPVBOMQualification" xml:space="preserve">
    <value>Allow viewing PPV/ BOM Qualification</value>
  </data>
  <data name="Setup_GlobalSettings_PPVBOMQualification_Add" xml:space="preserve">
    <value>Allow adding PPV/ BOM Qualification</value>
  </data>
  <data name="Setup_GlobalSettings_PPVBOMQualification_Edit" xml:space="preserve">
    <value>Allow editing PPV/ BOM Qualification</value>
  </data>
  <data name="Orders_CreditNote_BulkPrintEmail" xml:space="preserve">
    <value>Allow viewing credit note bulk print/bulk email</value>
  </data>
  <data name="Orders_DebitNote_BulkPrintEmail" xml:space="preserve">
    <value>Allow viewing debit note bulk print/bulk email</value>
  </data>
  <data name="Contact_CompanyDetail_MainInfo_PremierCustomer_Activate" xml:space="preserve">
    <value>Allow inactive/ active premier Customer</value>
  </data>
  <data name="Utility_HUBOfferImportLarge" xml:space="preserve">
    <value>Allow viewing Utility Bulk Offer Scheduled Import Tool</value>
  </data>
  <data name="Utility_ProspectiveOffer" xml:space="preserve">
    <value>Allow viewing Utility Prospective Offer Import and List section</value>
  </data>
  <data name="Utility_ProspectiveOfferDetail" xml:space="preserve">
    <value>Allow viewing Utility Prospective Offer Details</value>
  </data>
  <data name="Orders_ProsCrossSellingDetail" xml:space="preserve">
    <value>Allow viewing Prospective Cross Selling Details</value>
  </data>
  <data name="Orders_ProspectiveCrossSelling" xml:space="preserve">
    <value>Allow viewing Prospective Cross Selling Import and List section</value>
  </data>
  <data name="Setup_CompanySettings_OGELLicenses" xml:space="preserve">
    <value>Allow viewing OGEL Licenses setup</value>
  </data>
  <data name="Setup_CompanySettings_OGELLicenses_Add" xml:space="preserve">
    <value>Allow adding a new OGEL License</value>
  </data>
  <data name="Setup_CompanySettings_OGELLicenses_Edit" xml:space="preserve">
    <value>Allow editing a OGEL License</value>
  </data>
  <data name="Orders_Sourcing_BulkEdit_StrategicStock" xml:space="preserve">
    <value>Allow Permission to View Bulk Edit Strategic Logistic</value>
  </data>
  <data name="Orders_CreditNote_Generate_XML" xml:space="preserve">
    <value>Allow generating Credit Note in XML format</value>
  </data>
  <data name="Orders_Invoice_Generate_XML" xml:space="preserve">
    <value>Allow generating Invoice in XML format</value>
  </data>
  <data name="Contact_Company_Allow_Active_Inactive" xml:space="preserve">
    <value>Allow Active/Inactive Company</value>
  </data>
  <data name="Orders_BOMDetail_Import_Export_SourcingResult" xml:space="preserve">
    <value>Allow exporting HUBRFQ and importing Sourcing</value>
  </data>
</root>