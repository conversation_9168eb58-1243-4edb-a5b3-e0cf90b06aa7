//------------------------------------------------------------------------------------------------
// RP 05.01.2010:
// - add links back to related Quotes
//------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class CustomerRequirementSourcingResults : Base
    {

        #region Locals

        protected FlexiDataTable _tbl = new FlexiDataTable();
        protected IconButton _ibtnAdd;
        protected IconButton _ibtnEdit;
        protected IconButton _ibtnQuote;
        protected IconButton _ibtnDelete;
        protected MultiSelectionCount _ctlMultiSelectionCount;

        #endregion

        #region Properties

        private bool _blnCanAdd = true;
        public bool CanAdd
        {
            get { return _blnCanAdd; }
            set { _blnCanAdd = value; }
        }

        private bool _blnCanEdit = true;
        public bool CanEdit
        {
            get { return _blnCanEdit; }
            set { _blnCanEdit = value; }
        }

        private bool _blnCanQuote = true;
        public bool CanQuote
        {
            get { return _blnCanQuote; }
            set { _blnCanQuote = value; }
        }

        #endregion

        #region Overrides

        protected override void OnInit(EventArgs e)
        {
            TitleText = Functions.GetGlobalResource("Nuggets", "CustomerRequirementSourcingResults");
            base.OnInit(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            AddScriptReference("Controls.Nuggets.CusReqSourcingResults.CusReqSourcingResults.js");
            _ibtnDelete = FindIconButton("ibtnDelete");
            if (SessionManager.IsPOHub == false)
                _ibtnDelete.IconTitleResource = "DeleteClientPartWatch";
            WireUpControls();
            SetupTable();
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e)
        {
            _ibtnAdd.Visible = _blnCanAdd;
            _ibtnEdit.Visible = _blnCanEdit;
            _ibtnQuote.Visible = _blnCanQuote;
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        #region Methods

        private void WireUpControls()
        {
            _ibtnAdd = FindIconButton("ibtnAdd");
            _ibtnEdit = FindIconButton("ibtnEdit");
            _ibtnQuote = FindIconButton("ibtnQuote");
            _ctlMultiSelectionCount = (MultiSelectionCount)ctlDesignBase.FindLinksControl("ctlMultiSelectionCount");
        }

        private void SetupTable()
        {
            _tbl = (FlexiDataTable)ctlDesignBase.FindContentControl("tbl");
            _tbl.Columns.Add(new FlexiDataColumn("Supplier", "RelatedQuotes", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyCode)));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "NotesMSL", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tbl.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tbl.Columns.Add(new FlexiDataColumn("Product", "Package", Unit.Pixel(195)));
            _tbl.Columns.Add(new FlexiDataColumn("DateOffered", "By", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tbl.Columns.Add(new FlexiDataColumn("Quantity", "Status", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
            _tbl.Columns.Add(new FlexiDataColumn("Price", "BaseCurrency", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tbl.Columns.Add(new FlexiDataColumn("Region", "TermsName", WidthManager.GetWidth(WidthManager.ColumnWidth.BooleanValue)));
            _tbl.Columns.Add(new FlexiDataColumn("EstimatedShippingCost", "EstimatedShippingCostInBase", WidthManager.GetWidth(WidthManager.ColumnWidth.BooleanValue)));
            
            if(SessionManager.IsPOHub==false)
                _tbl.Columns.Add(new FlexiDataColumn("ClientPartWatchMatch", Unit.Pixel(87)));
            else
                _tbl.Columns.Add(new FlexiDataColumn("PartWatchMatch", Unit.Pixel(87)));
            
        }

        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _objQSManager.CustomerRequirementID);
            _scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
            if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
            if (_blnCanQuote) _scScriptControlDescriptor.AddElementProperty("ibtnQuote", _ibtnQuote.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlMultiSelectionCount", _ctlMultiSelectionCount.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
        }

        #endregion

    }
}
