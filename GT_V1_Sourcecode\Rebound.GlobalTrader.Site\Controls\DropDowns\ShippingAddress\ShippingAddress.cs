using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
    public partial class ShippingAddress : Base
    {

		private int _intCompanyID;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("ShippingAddress");
            AddScriptReference("Controls.DropDowns.ShippingAddress.ShippingAddress");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.ShippingAddress", ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			base.OnLoad(e);
		}

	}
}