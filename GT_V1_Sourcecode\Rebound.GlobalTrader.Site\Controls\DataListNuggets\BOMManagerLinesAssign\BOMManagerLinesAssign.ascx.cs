//   Marker     changed by      date            Remarks
//   [001]       <PERSON><PERSON>    04/09/2018    Export to csv button.
        

using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class BOMManagerLinesAssign : Base {

        #region Locals 
        protected IconButton _ibtnExportCSV;//[001]   
        protected MultiSelectionCount _ctlMultiSelectionCount;
        #endregion

        #region Overrides

        /// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {

			SetDataListNuggetType("BOMManagerLinesAssign");
			base.OnInit(e);
            WireUpControls();//[001]  
            AddScriptReference("Controls.DataListNuggets.BOMManagerLinesAssign.BOMManagerLinesAssign.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "BOMSearchAssign");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOMManagerLinesAssign", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            //_scScriptControlDescriptor.AddElementProperty("ibtnExportCSV", _ibtnExportCSV.ClientID);
			base.OnLoad(e);
		}
        protected override void OnPreRender(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }
        protected override void RenderAdditionalState()
        {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            if (!string.IsNullOrEmpty(strViewLevel))
            {
                ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
                _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
                this.OnAskPageToChangeTab();
            }
            base.RenderAdditionalState();
        }

        #endregion

        //start [001]   
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor.AddComponentProperty("ctlMultiSelectionCount", _ctlMultiSelectionCount.ClientID);
            //_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMAssign_Confirm", ctlDesignBase.ClientID);
        }
        private void WireUpControls()
        {
            _ctlMultiSelectionCount = (MultiSelectionCount)ctlDesignBase.FindLinksControl("ctlMultiSelectionCount");
            _ibtnExportCSV = (IconButton)FindIconButton("ibtnExportCSV");
            
        }
        //end [001]   
		private void SetupTable() {
            //_tbl.AllowSelection = true;
            //_tbl.AllowMultipleSelection = true;
            //_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            //_tbl.Columns.Add(new FlexiDataColumn("NameNumber", Unit.Empty, true));
            //_tbl.Columns.Add(new FlexiDataColumn("AssignedUserPartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
            //_tbl.Columns.Add(new FlexiDataColumn("CodeQuantity",  WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            //_tbl.Columns.Add(new FlexiDataColumn("Company","Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.EmailAddress), true));
            //_tbl.Columns.Add(new FlexiDataColumn("StatusSalesman", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            //_tbl.Columns.Add(new FlexiDataColumn("DateReceivedDate","DatePromised", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            //_tbl.Columns.Add(new FlexiDataColumn("TotalValueIPOBOM", Unit.Empty, true));

            //_tbl.Columns.Add(new FlexiDataColumn("Name", Unit.Empty, true));
            //_tbl.Columns.Add(new FlexiDataColumn("AssignedUser", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
            //_tbl.Columns.Add(new FlexiDataColumn("Code", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            //_tbl.Columns.Add(new FlexiDataColumn("Company", WidthManager.GetWidth(WidthManager.ColumnWidth.EmailAddress), true));
            //_tbl.Columns.Add(new FlexiDataColumn("Status", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            //_tbl.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            //_tbl.Columns.Add(new FlexiDataColumn("TotalValue", Unit.Empty, true));      

            // _tbl.Columns.Add(new FlexiDataColumn("Name", "Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));


            //soorya start
            // _tbl.Columns.Add(new FlexiDataColumn("BOMManagerCode", Unit.Pixel(120), true));
            // _tbl.Columns.Add(new FlexiDataColumn("BOMManagerName",  WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
            // _tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            // //_tbl.Columns.Add(new FlexiDataColumn("HUBRFQCode", "Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            // _tbl.Columns.Add(new FlexiDataColumn("PoHubBuyer", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsSalesman), true));
            //// _tbl.Columns.Add(new FlexiDataColumn("Salesperson", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsSalesman), true));
            // _tbl.Columns.Add(new FlexiDataColumn("ReceivedDate", "PromiseDate", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
            // _tbl.Columns.Add(new FlexiDataColumn("Status", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
            // _tbl.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
            // _tbl.Columns.Add(new FlexiDataColumn("TotalValue", Unit.Pixel(100), true));
            // // _tbl.Columns.Add(new FlexiDataColumn("Division", WidthManager.GetWidth(WidthManager.ColumnWidth.ClientName), true)); 
            //  soorya end

            _tbl.AllowSelection = true;
            _tbl.AllowMultipleSelection = true;
            _tbl.Columns.Add(new FlexiDataColumn("BOMManagerCode", Unit.Pixel(120), true));
            _tbl.Columns.Add(new FlexiDataColumn("BOMManagerName", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
            _tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            //_tbl.Columns.Add(new FlexiDataColumn("HUBRFQCode", "Salesman", WidthManager.GetWidth(WidthManager.ColumnWidth.DateCode), true));
            _tbl.Columns.Add(new FlexiDataColumn("Salesperson", "AssignedUser", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsSalesman), true));
            _tbl.Columns.Add(new FlexiDataColumn("ReceivedDate", "PromiseDate", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
            _tbl.Columns.Add(new FlexiDataColumn("Status", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
            _tbl.Columns.Add(new FlexiDataColumn("TotalValue", Unit.Pixel(100), true));
            // _tbl.Columns.Add(new FlexiDataColumn("Division", WidthManager.GetWidth(WidthManager.ColumnWidth.ClientName), true)); 



        }

    }
}