///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 19.07.2010:
// - ensure null value in ViewLevel does not cause an error
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 22.12.2009:
// - populate filters server-side
//
// RP 23.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.prototype = {

	get_strQuantityAt: function() { return this._strQuantityAt; }, set_strQuantityAt: function(value) { if (this._strQuantityAt !== value)  this._strQuantityAt = value; }, 
	get_strAvailable: function() { return this._strAvailable; }, set_strAvailable: function(value) { if (this._strAvailable !== value)  this._strAvailable = value; }, 

	initialize: function() {
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
		this._strPathToData = "controls/DataListNuggets/AllStock";
		this._strDataObject = "AllStock";
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._strQuantityAt = null;
		this._strAvailable = null;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.callBaseMethod(this, "dispose");
	},

	setupDataCall: function() {
		var strAction = "GetData_";
		var intViewLevel = this.getFilterValue("ViewLevel");
		if (intViewLevel == null || typeof(intViewLevel) == "undefined") intViewLevel = 0;
		switch (Number.parseInvariant(intViewLevel.toString())) {
			case 0: strAction += "All"; break;
			case 1: strAction += "Available"; break;
			case 2: strAction += "Quarantined"; break;
		}
		this._objData.set_DataAction(strAction);
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var strData = String.format("<a href=\"{0}\">{1}<br />", $RGT_gotoURL_Stock(row.ID), $R_FN.writePartNo(row.Part, row.ROHS));
			if (row.Location.length > 0) {
				strData += String.format(this._strQuantityAt, row.Available, row.Location);
			} else {
				strData += String.format(this._strAvailable, row.Available);
			}
			strData += "</a>";
			this._tbl.addRow([ strData ], row.ID, false);
			strData = null; row = null;
		}
	}

};
Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Stock", Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base, Sys.IDisposable);
