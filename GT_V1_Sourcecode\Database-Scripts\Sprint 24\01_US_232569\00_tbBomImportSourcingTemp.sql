﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-232569]		An.TranTan			20-Mar-2025		Create			Create new table for correction in BOM Import sourcing result function
===========================================================================================
*/
IF EXISTS (SELECT * FROM BorisGlobalTraderImports.sys.tables WHERE name = 'tbBomImportSourcingTemp')
    DROP TABLE BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp
GO
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp(
	[BomImportSourcingId] [int] IDENTITY(1,1) NOT NULL,
	[BOMNo] [int] NOT NULL,
	[CustomerRequirementNumber] [nvarchar](MAX) NULL,
	[Manufacturer] [nvarchar](MAX) NULL,
	[SupplierPart] [nvarchar](MAX) NULL,
	[OfferedQuantity] [nvarchar](MAX) NULL,
	[SupplierCost] [nvarchar](MAX) NULL,
	[SPQ] [nvarchar](MAX) NULL,
	[MOQ] [nvarchar](MAX) NULL,
	[SupplierName] [nvarchar](MAX) NULL,
	[MSL] [nvarchar](MAX) NULL,
	[Notes] [nvarchar](MAX) NULL,
	[DateCode] [nvarchar](MAX) NULL,
	[QtyInStock] [nvarchar](MAX) NULL,
	[OfferStatus] [nvarchar](MAX) NULL,
	[BuyPrice] [nvarchar](MAX) NULL,
	[SellPrice] [nvarchar](MAX) NULL,
	[ShippingCost] [nvarchar](MAX) NULL,
	[Package] [nvarchar](MAX) NULL,
	[ROHS] [nvarchar](MAX) NULL,
	[Currency] [nvarchar](MAX) NULL,
	[FactorySealed] [nvarchar](MAX) NULL,
	[Region] [nvarchar](MAX) NULL,
	[LeadTime] [nvarchar](MAX) NULL,
	[LastTimeBuy] [nvarchar](MAX) NULL,
	[DeliveryDate] [nvarchar](MAX) NULL,
	[CustomerRefNo] [nvarchar](MAX) NULL,
	[OriginalFilename] [nvarchar](200) NULL,
	[GeneratedFilename] [nvarchar](200) NULL,
	[CreatedBy] [int] NOT NULL,
	[DLUP] [datetime] NOT NULL,
PRIMARY KEY CLUSTERED 
(
	[BomImportSourcingId] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp ADD  DEFAULT (getdate()) FOR [DLUP]
GO
