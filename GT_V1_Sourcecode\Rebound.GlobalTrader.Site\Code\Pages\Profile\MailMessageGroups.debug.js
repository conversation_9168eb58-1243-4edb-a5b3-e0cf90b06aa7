///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Profile");

Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.prototype = {

	get_ctlGroups: function() { return this._ctlGroups; }, 	set_ctlGroups: function(v) { if (this._ctlGroups !== v)  this._ctlGroups = v; }, 
	get_ctlMembers: function() { return this._ctlMembers; }, 	set_ctlMembers: function(v) { if (this._ctlMembers !== v)  this._ctlMembers = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlGroups) this._ctlGroups.addSelectGroup(Function.createDelegate(this, this.ctlSecurityGroups_SelectGroup));
		if (this._ctlMembers) this._ctlMembers.addSaveEditComplete(Function.createDelegate(this, this.ctlSecurityGroupMembers_SaveEditComplete));
		if (this._ctlGroups) this._ctlGroups.addDeleteComplete(Function.createDelegate(this, this.ctlSecurityGroupMembers_DeleteComplete));
		Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlGroups) this._ctlGroups.dispose();
		if (this._ctlMembers) this._ctlMembers.dispose();
		this._ctlGroups = null;
		this._ctlMembers = null;
		Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.callBaseMethod(this, "dispose");
	},
	
	ctlSecurityGroups_SelectGroup: function() {
		this._ctlMembers._intMailGroupID = this._ctlGroups._intMailGroupID;
		this._ctlMembers.refresh();
		this._ctlGroups._tbl.resizeColumns();
		this.showNuggets(true);
	},
	
	ctlSecurityGroupMembers_SaveEditComplete: function() {
		this._ctlGroups.refresh();
	},
	
	ctlSecurityGroupMembers_DeleteComplete: function() {
		this.showNuggets(false);
	},
	
	showNuggets: function(bln) {
		this._ctlMembers.show(bln);
	}
	
};

Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups.registerClass("Rebound.GlobalTrader.Site.Pages.Profile.MailMessageGroups", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
