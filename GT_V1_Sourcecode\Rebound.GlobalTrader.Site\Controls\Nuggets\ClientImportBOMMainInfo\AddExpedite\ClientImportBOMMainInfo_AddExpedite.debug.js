﻿//Marker     Changed by      Date         Remarks
//[001]      Suhail       25/04/2018   Added contact and company name while sending mail via Add New Communication Note
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_AddExpedite = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_AddExpedite.initializeBase(this, [element]);
   // this._intCustomerRequirementID = -1;
    this._intBOMID = -1;
   // this._ReqIds = "";
    this._intBOMNo = -1;
    this._intRequestedby = -1;
    this._intUpdateByPH = -1;
    this._HUBRFQName = null;
    this._HubrfqCode = null;
    this._CompanyNo = -1;
    this._intContact2No = -1;
    //[001] Code Start
    this._companyname = null;
    this._contactname = null;
    //[001] Code End
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_AddExpedite.prototype = {

    //get_intBOMID: function () { return this._intBOMID; }, set_intBOMID: function (value) { if (this._intBOMID !== value) this._intBOMID = value; },
   
    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_AddExpedite.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function () {
      
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
           
        }
        
    },

    dispose: function() {
        if (this.isDisposed) return;
       
       // this._intBOMID = null;

        Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_AddExpedite.callBaseMethod(this, "dispose");
    },

    
    saveClicked: function () {
       
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMMainInfo");
        obj.set_DataObject("BOMMainInfo");
        obj.set_DataAction("SaveExpedite");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("AddNotes", this.getFieldValue("ctlExpediteNotes"));
        obj.addParameter("HUBRFQName", this._HUBRFQName);
        obj.addParameter("intRequestedby", this._intRequestedby);
        obj.addParameter("intUpdateByPH", this._intUpdateByPH);
        obj.addParameter("HubrfqCode", this._HubrfqCode);
        obj.addParameter("CompanyNo", this._CompanyNo);
        obj.addParameter("Contact2No", this._intContact2No);
        //[001] Code Start
        obj.addParameter("CompanyName", this._companyname);
        obj.addParameter("ContactName", this._contactname);
        //[001] Code End
        obj.addDataOK(Function.createDelegate(this, this.saveAddComplete));
        obj.addError(Function.createDelegate(this, this.saveAddError));
        obj.addTimeout(Function.createDelegate(this, this.saveAddError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveAddError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveAddComplete: function(args) {
        if (args._result.Result >0) {
            this.onSaveComplete();
        } else {
            if (args._result.Message) this._strErrorMessage = args._result.Message;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_AddExpedite.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_AddExpedite", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
