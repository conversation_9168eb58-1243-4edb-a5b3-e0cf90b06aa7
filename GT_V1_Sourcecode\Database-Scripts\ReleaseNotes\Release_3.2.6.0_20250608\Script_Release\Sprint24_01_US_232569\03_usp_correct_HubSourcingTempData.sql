﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-232569]		An.TranTan			20-Mar-2025		Create			Correct incorrect MFR/Supplier in tbBomImportSourcingTemp
===========================================================================================
*/
CREATE OR ALTER  PROCEDURE [dbo].[usp_correct_HubSourcingTempData]
    @UserID INT = 0,             
    @IncorrectValue NVARCHAR(500) = NULL,
    @NewValue NVARCHAR(500) = NULL,
	@Type NVARCHAR(10) = NULL,
	@RecordCount INT OUTPUT
AS 
BEGIN
	SET @RecordCount = 0;
	IF(@IncorrectValue = '_Blank_')
		SET @IncorrectValue = '';

	IF(@Type = 'MFR')
	BEGIN
		UPDATE BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp
		SET Manufacturer = @NewValue
		WHERE CreatedBy = @UserID
			AND ISNULL(Manufacturer,'') = @IncorrectValue;
	END
	ELSE IF (@Type = 'SUPPLIER')
	BEGIN
		UPDATE BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp
		SET SupplierName = @NewValue
		WHERE CreatedBy = @UserID
			AND ISNULL(SupplierName,'') = @IncorrectValue;
	END

	SET @RecordCount = @@ROWCOUNT;
END
GO