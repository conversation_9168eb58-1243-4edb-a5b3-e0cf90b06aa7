Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.CD_Product=function(n){Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.prototype={get_ctlProduct:function(){return this._ctlProduct},set_ctlProduct:function(n){this._ctlProduct!==n&&(this._ctlProduct=n)},get_ctlRateHistory:function(){return this._ctlRateHistory},set_ctlRateHistory:function(n){this._ctlRateHistory!==n&&(this._ctlRateHistory=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.callBaseMethod(this,"initialize")},goInit:function(){this._ctlProduct.addSelectProduct(Function.createDelegate(this,this.ctlProduct_SelectProduct));Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlProduct&&this._ctlProduct.dispose(),this._ctlRateHistory&&this._ctlRateHistory.dispose(),this._ctlProduct=null,this._ctlRateHistory=null,Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.callBaseMethod(this,"dispose"))},ctlProduct_SelectProduct:function(){this._ctlRateHistory._intProductID=this._ctlProduct._intProductID;this._ctlRateHistory.show(!0);this._ctlProduct._tbl.resizeColumns();this._ctlRateHistory.refresh()}};Rebound.GlobalTrader.Site.Pages.Setup.CD_Product.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_Product",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);