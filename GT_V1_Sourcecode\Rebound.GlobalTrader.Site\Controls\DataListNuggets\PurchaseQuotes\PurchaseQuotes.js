Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.prototype={get_intSalesPersonID:function(){return this._intSalesPersonID},set_intSalesPersonID:function(n){this._intSalesPersonID!==n&&(this._intSalesPersonID=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/PurchaseQuotes";this._strDataObject="PurchaseQuotes";Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.applySalesPersonFilter();this.getData()},dispose:function(){this.isDisposed||(this._intSalesPersonID=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_POQuote(n.POQNo,n.POQNu),$R_FN.setCleanTextValue(n.Part),$RGT_nubButton_BOM(n.BOMNo,n.BOM),n.Quantity,$R_FN.setCleanTextValue(n.CM),$R_FN.setCleanTextValue(n.Emp),n.Date],this._table.addRow(i,n.ID,!1),i=null,n=null},applySalesPersonFilter:function(){this._intSalesPersonID&&this._intSalesPersonID>0&&this.getFilterField("ctlSalesmanName").setValue(this._intSalesPersonID)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseQuotes",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);