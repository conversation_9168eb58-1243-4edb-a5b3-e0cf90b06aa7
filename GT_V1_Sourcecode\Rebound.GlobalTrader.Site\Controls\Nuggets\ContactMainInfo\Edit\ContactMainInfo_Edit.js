Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.initializeBase(this,[n]);this._intContactID=-1;this._ctlAddress=null;this._intCompanyNo=-1};Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.prototype={get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveClicked))},dispose:function(){this.isDisposed||(this._intContactID=null,Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown;this.getFieldControl("ctlCompanyAddress")._intCompanyID=this._intCompanyNo;this.getFieldDropDownData("ctlCompanyAddress")},saveClicked:function(){if(this.validateForm()&&this.validateEmail()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ContactMainInfo");n.set_DataObject("ContactMainInfo");n.set_DataAction("SaveEdit");n.addParameter("id",this._intContactID);n.addParameter("FirstName",this.getFieldValue("ctlFirstName"));n.addParameter("Surname",this.getFieldValue("ctlSurname"));n.addParameter("JobTitle",this.getFieldValue("ctlJobTitle"));n.addParameter("Tel",this.getFieldValue("ctlTel"));n.addParameter("Fax",this.getFieldValue("ctlFax"));n.addParameter("Extension",this.getFieldValue("ctlExtension"));n.addParameter("HomeTel",this.getFieldValue("ctlHomeTel"));n.addParameter("MobileTel",this.getFieldValue("ctlMobileTel"));n.addParameter("Email",this.getFieldValue("ctlEmail"));n.addParameter("TextOnlyEmail",this.getFieldValue("ctlTextOnlyEmail"));n.addParameter("Nickname",this.getFieldValue("ctlNickname"));n.addParameter("AddressNo",this.getFieldValue("ctlCompanyAddress"));n.addParameter("FinanceContact",this.getFieldValue("ctlFinanceContacts"));n.addParameter("Inactive",this.getFieldValue("ctlInactive"));n.addParameter("IsSendShipmentNotification",this.getFieldValue("ctlSendShipmentNotification"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return n||this.showError(!0),n},validateEmail:function(){return this.getFieldValue("ctlFinanceContacts")==!0&&this.checkFieldEntered("ctlEmail")==!1?(this.showError(!0,$R_RES.ContactEmailMessage),!1):!0}};Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactMainInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);