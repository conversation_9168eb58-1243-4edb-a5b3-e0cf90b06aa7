<%@ Control Language="C#" CodeBehind="GILines_AddShortShipment.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<script src="js/GILine.js"></script>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>

    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "GILines_AddShortShipment")%></Explanation>

    <Content>

        <ReboundUI_Table:Form ID="frm" runat="server">

            <ReboundUI_Form:FormField ID="ctlSupplier" runat="server" FieldID="lblSupplier" ResourceTitle="Supplier">
                <Field>
                    <asp:Label ID="lblSupplier" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlPurchaseOrder" runat="server" FieldID="lblPurchaseOrder" ResourceTitle="PurchaseOrderNo">
                <Field>
                    <asp:Label ID="lblPurchaseOrder" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlSalesContact" runat="server" FieldID="lblSalesContact" ResourceTitle="SalesContact">
                <Field>
                    <asp:Label ID="lblSalesContact" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlReferenceDetail" runat="server" FieldID="lblReferenceDetail" ResourceTitle="AdviceNotes">
                <Field>
                    <asp:Label ID="lblReferenceDetail" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlGoodsIn" runat="server" FieldID="lblGoodsIn" ResourceTitle="GoodsInNoteNo">
                <Field>
                    <asp:Label ID="lblGoodsIn" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlReceivedDate" runat="server" FieldID="lblReceivedDate" ResourceTitle="ReceivedDate">
                <Field>
                    <asp:Label ID="lblReceivedDate" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlRaisedBy" runat="server" FieldID="lblRaisedBy" ResourceTitle="RaisedBy">
                <Field>
                    <asp:Label ID="lblRaisedBy" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlBuyer" runat="server" FieldID="lblBuyer" ResourceTitle="Buyer">
                <Field>
                    <asp:Label ID="lblBuyer" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlPartNo" runat="server" FieldID="lblPartNo" ResourceTitle="PartNo">
                <Field>
                    <asp:Label ID="lblPartNo" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlManufacturer" runat="server" FieldID="lblManufacturer" ResourceTitle="Manufacturer">
                <Field>
                    <asp:Label ID="lblManufacturer" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlQuantityOrder" runat="server" FieldID="lblQuantity" ResourceTitle="QuantityOrder">
                <Field>
                    <asp:Label ID="lblQuantity" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlQuantityAdvised" runat="server" FieldID="txtQuantityAdvised" ResourceTitle="QuantityAdvised">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtQuantityAdvised" runat="server" Width="75" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlQuantityReceived" runat="server" FieldID="lblQuantityReceived" ResourceTitle="QuantityReceived">
                <Field>
                    <asp:Label ID="lblQuantityReceived" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlShortageQuantity" runat="server" FieldID="lblShortageQuantity" ResourceTitle="ShortageQuantity">
                <Field>
                    <asp:Label ID="lblShortageQuantity" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlShortageValue" runat="server" FieldID="lblShortageValue" ResourceTitle="ShortageValue">
                <Field>
                    <asp:Label ID="lblShortageValue" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
        </ReboundUI_Table:Form>


    </Content>

</ReboundUI_Form:DesignBase>
