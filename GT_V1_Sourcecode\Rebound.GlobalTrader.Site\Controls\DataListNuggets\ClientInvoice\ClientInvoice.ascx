<%--
Marker     Changed by      Date               Remarks
[001]      Prakash           07/04/2016         CR:- Client Invoice
--%>
<%@ Control Language="C#" CodeBehind="ClientInvoice.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
			    <ReboundUI_FilterDataItemRow:Numerical id="ctlInvoiceNo" runat="server" ResourceTitle="ClientInvoice" FilterField="CINo" TextBoxMaxLength="10" />
			    <ReboundUI_FilterDataItemRow:Numerical id="ctlURNNo" runat="server" ResourceTitle="URNNo" FilterField="URNNo" style="display:none" />
			    <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
			    <ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" style="display:none"/>
			    <ReboundUI_FilterDataItemRow:DropDown id="ctlStatus" runat="server" IncludeNoValue="false" ResourceTitle="Status" DropDownType="SupplierInvoiceStatus" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Status" />
			    <ReboundUI_FilterDataItemRow:DropDown ID="ctlClient" runat="server" DropDownType="Client"
                  ResourceTitle="Client" FilterField="Client" DropDownAssembly="Rebound.GlobalTrader.Site" />
			</FieldsLeft>
			<FieldsRight>
			    <ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo" TextBoxMaxLength="10" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlGINo" runat="server" ResourceTitle="GoodsIn" FilterField="GINo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlClientInvoiceDateFrom" runat="server" ResourceTitle="ClientInvoiceDateFrom" FilterField="ClientInvoiceDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlClientInvoiceDateTo" runat="server" ResourceTitle="ClientInvoiceDateTo" FilterField="ClientInvoiceDateTo" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
