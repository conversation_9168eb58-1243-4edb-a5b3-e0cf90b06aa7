﻿///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoCategory = function (element) {
	Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoCategory.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoCategory.prototype = {

	initialize: function () {
		Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoCategory.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},

	dispose: function () {
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoCategory.callBaseMethod(this, "dispose");
	},

	setupDataCall: function () {
		this._objData.set_PathToData("controls/DropDowns/ToDoCategory");
		this._objData.set_DataObject("ToDoCategory");
		this._objData.set_DataAction("GetData");
	},

	dataCallOK: function () {
		var result = this._objData._result;

		if (result.Categories) {
			for (var i = 0; i < result.Categories.length; i++) {
				this.addOption(result.Categories[i].Name, result.Categories[i].ID);
			}
		}
	}
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoCategory.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ToDoCategory", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
