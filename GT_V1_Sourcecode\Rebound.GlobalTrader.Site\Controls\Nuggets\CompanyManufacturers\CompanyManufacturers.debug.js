///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript
//-------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.initializeBase(this, [element]);
	this._inactive = false;
	this._blnNoData = false;
	this._intSupplierID = -1;
	this._intContactGroupID = 0;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.prototype = {

	get_intSupplierID: function() { return this._intSupplierID; }, set_intSupplierID: function(v) { if (this._intSupplierID !== v) this._intSupplierID = v; },
	get_tbl: function() { return this._tbl; }, 	set_tbl: function(v) { if (this._tbl !== v)  this._tbl = v; }, 
	get_ibtnEdit: function() { return this._ibtnEdit; }, 	set_ibtnEdit: function(v) { if (this._ibtnEdit !== v)  this._ibtnEdit = v; }, 
	get_ibtnAdd: function() { return this._ibtnAdd; }, 	set_ibtnAdd: function(v) { if (this._ibtnAdd !== v)  this._ibtnAdd = v; }, 
	get_ibtnDelete: function() { return this._ibtnDelete; }, 	set_ibtnDelete: function(v) { if (this._ibtnDelete !== v)  this._ibtnDelete = v; }, 
	get_ibtnView: function () { return this._ibtnView; }, set_ibtnView: function (v) { if (this._ibtnView !== v) this._ibtnView = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.callBaseMethod(this, "initialize");	
		
		//data
		this._strPathToData = "controls/Nuggets/CompanyManufacturers";
		this._strDataObject = "CompanyManufacturers";
		this.latestStarRatingConfig = null;
		this.getLatestStarRatingConfig();

		
		//nugget events
		this.addRefreshEvent(Function.createDelegate(this, this.refreshData));
		this._tbl.addSortDataEvent(Function.createDelegate(this, this.getData));
		
		//add and edit form
		if (this._ibtnEdit || this._ibtnAdd) {
			if (this._ibtnEdit) $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
			if (this._ibtnAdd) $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
			this._frmAddEdit = $find(this._aryFormIDs[0]);
			this._frmAddEdit._intSupplierID = this._intSupplierID;
			this._frmAddEdit.addShown(Function.createDelegate(this, this.addEditFormShown));
			this._frmAddEdit.addCancel(Function.createDelegate(this, this.hideAddEditForm));
			this._frmAddEdit.addSaveComplete(Function.createDelegate(this, this.saveAddEditOK));
			this._frmAddEdit.addSaveError(Function.createDelegate(this, this.saveAddEditError));
		}

		//view form
		if (this._ibtnView) {
			this._frmView = $find(this._aryFormIDs[2]);
			$R_IBTN.addClick(this._ibtnView, Function.createDelegate(this, this.showViewForm));
			this._frmView.addCancel(Function.createDelegate(this, this.hideViewForm));
			this._frmView.addSaveComplete(Function.createDelegate(this, this.saveFranchiseOK));
			this._frmView.addSaveError(Function.createDelegate(this, this.saveFranchiseError));
		}
		
		//delete form
		if (this._ibtnDelete) {
			$R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
			this._frmDelete = $find(this._aryFormIDs[1]);
			this._frmDelete.addShown(Function.createDelegate(this, this.deleteFormShown));
			this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
			this._frmDelete.addSaveComplete(Function.createDelegate(this, this.saveDeleteOK));
			this._frmDelete.addSaveError(Function.createDelegate(this, this.saveDeleteError));
		}
		
		var ddlMfrGroupBySupplier = $find('ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ddlMfrGroupBySupplier');
		if (ddlMfrGroupBySupplier) {
			ddlMfrGroupBySupplier._intCompanyID = this._intSupplierID;
			ddlMfrGroupBySupplier.addChanged(Function.createDelegate(this, this.groupCodeChanged));
        }
		
		//control events
		this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));

		this.getCompanyInactive();
		this.getFieldDropDownGroupCodeData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
		if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
		if (this._ibtnView) $R_IBTN.clearHandlers(this._ibtnView);
		if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
		if (this._frmDelete) this._frmDelete.dispose();
		if (this._frmAddEdit) this._frmAddEdit.dispose();
		if (this._frmView) this._frmView.dispose();
		if (this._tbl) this._tbl.dispose();
		this._frmDelete = null;
		this._frmAddEdit = null;
		this._frmView = null;
		this._tbl = null;
		this._ibtnAdd = null;
		this._ibtnEdit = null;
		this._ibtnDelete = null;
		this._ibtnView = null;
		this._intSupplierID = null;
		this._intContactGroupID = null;
		this._blnNoData = null;
		this._inactive = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.callBaseMethod(this, "dispose");
	},

	getData: function() { 
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.callBaseMethod(this, "getData_Start");
		this.updateButtonsEnabledState(false);	
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetManufacturers");
		obj.addParameter("id", this._intSupplierID);
		obj.addParameter("ContactGroupID", this._intContactGroupID);
		obj.addParameter("SortDir", this._tbl._enmSortDirection);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getDataError: function(args) {
		this.showError(true, args.get_ErrorMessage());
	},

	getDataComplete: function(args) { 
		var res = args._result;
		this._tbl.clearTable();
		this._blnNoData = true;
		if (res.Items) {
			for (var i = 0; i < res.Items.length; i++) {
				var row = res.Items[i];
				var poLineCount = row.IsOldData ? 'N/A' : row.POLineCount;
				let isFranchised = row.IsFranchised == true ? 'Yes' : 'No';				
				var aryData = [
					$RGT_nubButton_Manufacturer(row.MfrID, row.MfrName, row.MfrAdvisoryNotes),
					isFranchised,
					$R_FN.setCleanTextValue(poLineCount),
					$R_FN.createStarRating(row.Rating)
				];
				var objExtraData = {
					MfrName: row.MfrName
					,POLineCount: poLineCount
					,Rating: row.Rating
					,IsFranchised: row.IsFranchised
				};
				this._tbl.addRow(aryData, row.ID, false, objExtraData);
				row = null; aryData = null;
			}
			this._blnNoData = (res.Items.length == 0);

			let isNotFranchised = res.Items.some((element) => element.IsFranchised == false);
			if (isNotFranchised && this._intContactGroupID > 0) {
				$('#ibtnMarkFranchised').prop('disabled', false);
			} else {
				$('#ibtnMarkFranchised').prop('disabled', true);
            }		
		}
		var lastHeader = this._tbl._tblHeader.getElementsByClassName('last')[0];
		if (lastHeader.innerText == 'Rating') {
			var config = this.latestStarRatingConfig != null ? this.latestStarRatingConfig : 'N/A';
			lastHeader.innerHTML = 'Rating<br><span class="faded-text">(1 Star per ' + config + ' PO Lines)</span>';
			lastHeader.getElementsByClassName('faded-text')[0].style.opacity = 0.5;
		}
		this._tbl.resizeColumns();
		this.getDataOK_End();
		this.showNoData(this._blnNoData);
	},

	getCompanyInactive: function () {
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetCompanyDetailInactive");
		obj.addParameter("id", this._intSupplierID);
		obj.addDataOK(Function.createDelegate(this, this.getCompanyInactiveOK));
		obj.addError(Function.createDelegate(this, this.getCompanyInactiveError));
		obj.addTimeout(Function.createDelegate(this, this.getCompanyInactiveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getCompanyInactiveOK: function (args) {
		var result = args._result;
		this._inactive = result.Inactive;
		if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._inactive);
		if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive);
		if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, !this._inactive);
		if (this._ibtnView) $R_IBTN.enableButton(this._ibtnView, !this._inactive);
	},

	getCompanyInactiveError: function (args) {
		this.showError(true, args.get_ErrorMessage());
	},
	getFieldDropDownGroupCodeData: function () {
		var ddl = $find('ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ddlMfrGroupBySupplier')
		if (!ddl) return;
		ddl.getData();
	},
	groupCodeChanged: function () {
		this._intContactGroupID = $find('ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ddlMfrGroupBySupplier').getValue();

		this.getData();
		this.getLatestStarRatingConfig();
	},
	markFranchised: function () {
		console.log('markFranchised');
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("MarkFranchised");
		obj.addParameter("SupplierId", this._intSupplierID);
		obj.addParameter("GroupCodeId", this._intContactGroupID);
		obj.addDataOK(Function.createDelegate(this, this.markFranchisedOK));
		obj.addError(Function.createDelegate(this, this.markFranchisedError));
		obj.addTimeout(Function.createDelegate(this, this.markFranchisedError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	markFranchisedOK: function (args) {
		var result = args._result;
		if (result) {
			this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
			this.getData();
			this.getLatestStarRatingConfig();
        }
	},

	markFranchisedError: function (args) {
		this.showError(true, args.get_ErrorMessage());
	},
	getLatestStarRatingConfig: function () {
		Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.callBaseMethod(this, "getData_Start");
		this.updateButtonsEnabledState(false);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData('controls/Nuggets/ManufacturerSuppliers');
		obj.set_DataObject('ManufacturerSuppliers');
		obj.set_DataAction("GetStarRatingConfig");
		obj.addDataOK(Function.createDelegate(this, this.getConfigComplete));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getConfigComplete: function (args) {
		var res = args._result;
		if (res.Items.length > 0) {
			this.latestStarRatingConfig = res.Items[0].NumOfPO;
		}

		this.getDataOK_End();
	},

	refreshData: function () {
		this.getData();
		this.getLatestStarRatingConfig();
	},
	
	updateButtonsEnabledState: function(blnEnable) {
		if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, blnEnable);
		if (this._ibtnView) $R_IBTN.enableButton(this._ibtnView, blnEnable);
		if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, blnEnable);	
	},
	
	tbl_SelectedIndexChanged: function() {
		this.updateButtonsEnabledState(!this._inactive);
		this._frmAddEdit._intManufacturerLinkID = this._tbl._varSelectedValue;
		this._frmView._intManufacturerLinkID = this._tbl._varSelectedValue;
		this._frmDelete._intManufacturerLinkID = this._tbl._varSelectedValue;
	},
	
	showAddForm: function() {
		this._frmAddEdit.changeMode("ADD");
		this.showForm(this._frmAddEdit, true);
	},

	showEditForm: function() {
		this._frmAddEdit.changeMode("EDIT");
		this.showForm(this._frmAddEdit, true);
	},
	
	addEditFormShown: function() {
		if (this._frmAddEdit._mode == "ADD") {
			this._frmAddEdit._autManufacturers.reselect();

		} else {
			this._frmAddEdit.setFieldValue("ctlManufacturerSelected", this._tbl.getSelectedExtraData().MfrName);

		}
	},

	hideAddEditForm: function() {
		this.showForm(this._frmAddEdit, false);
		this.showNoData(this._blnNoData);
	},

	saveAddEditOK: function() {
		this.hideAddEditForm();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.getData();
		this.getFieldDropDownGroupCodeData();
	},

	saveAddEditError: function(args) {
		this.showError(true, this._frmAddEdit._strErrorMessage);
	},
	
	showDeleteForm: function() {
		this.showForm(this._frmDelete, true);
	},
	
	hideDeleteForm: function() {
		this.showForm(this._frmDelete, false);
		this.showNoData(this._blnNoData);
	},
	
	saveDeleteOK: function() {
		this.hideDeleteForm();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.getData();
	},
	
	saveDeleteError: function(args) {
		this.showError(true, this._frmDelete._strErrorMessage);
	},
	
	deleteFormShown: function() {
		this._frmDelete.setFieldValue("ctlManufacturerSelected", this._tbl.getSelectedExtraData().MfrName);
	},

	showViewForm: function () {
		let selected = this._tbl.getSelectedExtraData();
		var formular = this.latestStarRatingConfig != null ? '(1 Star per ' + this.latestStarRatingConfig.toString() + ' PO Lines)' : '(1 Star per N/A PO Lines)';
		var poLineFormular = selected.POLineCount.toString().concat(" ", formular);
		this._frmView.setFieldValue("ctlManufacturerSelected", $R_FN.setCleanTextValue(selected.MfrName));
		this._frmView.setFieldValue("ctlPOLine", poLineFormular);
		this._frmView.setFieldValue("ctlRating", selected.Rating);
		this._frmView.setFieldValue("ctlFranchise", selected.IsFranchised);
        this.showForm(this._frmView, true);
	},

	hideViewForm: function() {
        this.showForm(this._frmView, false);
        this.showNoData(this._blnNoData);
	},
	saveFranchiseOK: function () {
		this.hideViewForm();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.getData();
	},

	saveFranchiseError: function (args) {
		this.showError(true, this._frmView._strErrorMessage);
	}
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
