﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-220887]		An.TranTan			19-Nov-2024		Update			Bulk edit strategic logistics
[US-223239]		An.TranTan			12-Dev-2024		Update			Insert bulk edit history
===========================================================================================
*/
CREATE OR ALTER  PROCEDURE [dbo].[usp_update_POHub_StrategicLogisticBulk] 
	@EpoIDs NVARCHAR(MAX)
	,@Action VARCHAR(50)
	,@LoginID INT = NULL
	,@RowsAffected INT = NULL OUTPUT
AS
BEGIN
BEGIN TRANSACTION
	DECLARE @Epo TABLE(ID INT);
	DECLARE @BatchID INT;

	SELECT @BatchID = ISNULL(MAX(BatchNo),0) + 1 
	FROM BorisGlobalTraderImports.dbo.tbBulkEpoAuditLog;

	INSERT INTO @Epo(ID)
	SELECT CAST(VALUE AS INT)
	FROM STRING_SPLIT(@EpoIDs, ',')
	WHERE TRY_CAST(VALUE AS INT) IS NOT NULL;

	--save data to temp before update
	SELECT 
		epo.EpoId
		,epo.Quantity
		,epo.Inactive
		,epo.Part
		,epo.FullPart
	INTO #tempOriginalEpo
	FROM [BorisGlobalTraderImports].dbo.tbEpo epo WITH(NOLOCK)
	JOIN @Epo t on t.ID = epo.EpoId;

	IF @Action = 'Remove'
	BEGIN
		UPDATE epo
		SET epo.Inactive = 1
			,epo.InactiveBy = @LoginID
			,epo.InactiveDate = CURRENT_TIMESTAMP
			,epo.UpdatedBy = @LoginID
		FROM [BorisGlobalTraderImports].dbo.tbEpo epo
		JOIN @Epo t on t.ID = epo.EpoId;

		SELECT @RowsAffected = @@ROWCOUNT;

		/*********************insert history*********************/
		INSERT INTO BorisGlobalTraderImports.dbo.tbBulkEpoAuditLog
		(
			BatchNo
			,EpoNo
			,Part
			,FullPart
			,[Action]
			,OldValue
			,CreatedBy
			,CreatedByName
			,DLUP
		)SELECT 
			@BatchID
			,t.EpoId
			,t.Part
			,t.FullPart
			,'Remove'	--action
			,'Active'	--old value
			,@LoginID
			,CAST((l.FirstName + ' ' + l.LastName) AS NVARCHAR(256))
			,GETDATE()
		FROM #tempOriginalEpo t
		JOIN dbo.tbLogin l WITH(NOLOCK) ON l.LoginId = @LoginID
	END
	ELSE BEGIN --set quantity to zero
		UPDATE epo
		SET epo.Quantity = 0
			,epo.UpdatedBy = @LoginID
			,epo.DLUP = CURRENT_TIMESTAMP
		FROM [BorisGlobalTraderImports].dbo.tbEpo epo
		JOIN @Epo temp on temp.ID = epo.EpoId;

		SELECT @RowsAffected = @@ROWCOUNT;

		/*********************insert history*********************/
		INSERT INTO BorisGlobalTraderImports.dbo.tbBulkEpoAuditLog
		(
			BatchNo
			,EpoNo
			,Part
			,FullPart
			,[Action]
			,OldValue
			,CreatedBy
			,CreatedByName
			,DLUP
		)SELECT 
			@BatchID
			,t.EpoId
			,t.Part
			,t.FullPart
			,'Set Qty to Zero'	--action
			,t.Quantity			--old value
			,@LoginID
			,CAST((l.FirstName + ' ' + l.LastName) AS NVARCHAR(256))
			,GETDATE()
		FROM #tempOriginalEpo t
		JOIN dbo.tbLogin l WITH(NOLOCK) ON l.LoginId = @LoginID;
	END

	IF @@ERROR = 0
		COMMIT TRANSACTION
	ELSE
	BEGIN
		SET @RowsAffected = -1;
		ROLLBACK TRANSACTION
	END
	DROP TABLE #tempOriginalEpo;
END
GO


