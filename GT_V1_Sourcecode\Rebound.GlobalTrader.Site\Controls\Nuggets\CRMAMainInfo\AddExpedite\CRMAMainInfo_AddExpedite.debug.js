﻿/*
Marker     Changed by      Date         Remarks
[001]      Aashu           26/06/2018   Save the expedite note detail for CRMA
*/

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.initializeBase(this, [element]);
    this._intCustomerRMAID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.prototype = {

    get_intCustomerRMAID: function () { return this._intCustomerRMAID; }, set_intCustomerRMAID: function (value) { if (this._intCustomerRMAID !== value) this._intCustomerRMAID = value; },
   
    initialize: function() {
    Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function () {
       // alert(this._IpoBuyerName);
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
           
        }
        
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intCustomerRMAID = null;

        Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.callBaseMethod(this, "dispose");
    },

    
    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CRMAMainInfo");
        obj.set_DataObject("CRMAMainInfo");
        obj.set_DataAction("SaveCRMAInternalLog");

        obj.addParameter("intCustomerRMAId", this._intCustomerRMAID);
        obj.addParameter("AddNotes", this.getFieldValue("ctlExpediteNotes"));

        //obj.addParameter("SendMail", this.getFieldValue("ctlSendMail"));

        obj.addDataOK(Function.createDelegate(this, this.saveAddComplete));
        obj.addError(Function.createDelegate(this, this.saveAddError));
        obj.addTimeout(Function.createDelegate(this, this.saveAddError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveAddError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveAddComplete: function (args) {
        if (args._result.Result > 0) {
            this.onSaveComplete();
        } else {
            if (args._result.Message) this._strErrorMessage = args._result.Message;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
