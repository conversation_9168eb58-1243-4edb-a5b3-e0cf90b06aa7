﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-240481]		An.TranTan			21-Apr-2025		Create			Get part number for IHS API call from tbTempBOMData
[US-240481]		An.TranTan			05-May-2025		Update			Filter only parts don't have IHS data within 7 days
===========================================================================================
*/
CREATE OR ALTER PROCEDURE dbo.usp_get_BOMParts_ForIHS
	@UserId INT,                  
	@ClientId INT,                      
	@SelectedClientID INT
AS BEGIN
	DECLARE @PartTargetColumn NVARCHAR(100) = null
			, @SQL NVARCHAR(MAX) = NULL;

	;WITH cteBomImportMapping as
	(
		select 
			ColumnId        
			, value as ColumnHeading
		from BorisGlobalTraderimports.dbo.tbExcelBOMColumnHeading with(nolock)
		cross apply                
		(                
		    values                
		        ('Column1', Column1),                
		        ('Column2', Column2),                
		        ('Column3', Column3),                
				('Column4', Column4),                
		        ('Column5', Column5),                
		        ('Column6', Column6),                
				('Column7', Column7),                
		        ('Column8', Column8),                
		        ('Column9', Column9),                
				('Column10', Column10),                
		        ('Column11', Column11),                
		        ('Column12', Column12),    
				('Column13', Column13),    
				('Column14', Column14),    
				('Column15', Column15)                 
		) c(ColumnId, value)                   
		where  ClientId = @ClientId 
			AND SelectedClientID = @SelectedClientID 
			AND CreatedBy = @UserId  
			AND value IS NOT NULL
	)
	SELECT TOP 1 @PartTargetColumn = ColumnId 
	FROM cteBomImportMapping
	WHERE dbo.ufn_get_fullname(ColumnHeading) COLLATE SQL_Latin1_General_CP1_CI_AS in ('PART','PARTNO','PARTNUMBER');

	IF(@PartTargetColumn IS NULL)
	BEGIN
		SELECT '' AS Part;
		RETURN;
	END

	SET @SQL =	'SELECT DISTINCT ' + @PartTargetColumn + ' AS Part'
				+ ' FROM BorisGlobalTraderimports.dbo.tbTempBOMData WITH(NOLOCK)'
				+ ' WHERE ' + @PartTargetColumn + ' IS NOT NULL'
				+	' AND LEN(' + @PartTargetColumn + ') >=3'	--part search for IHS API requires at least 3 characters
				+	' AND ClientId = ' + CONVERT(NVARCHAR(10), @ClientId)
				+	' AND SelectedClientId = ' + CONVERT(NVARCHAR(10), @SelectedClientId)
				+	' AND CreatedBy = ' + CONVERT(NVARCHAR(10), @UserId)
				+   ' AND NOT EXISTS(
						SELECT TOP 1 1 FROM tbIHSparts ihs WITH(NOLOCK)
						WHERE ihs.DLUP BETWEEN DATEADD(DAY, -7, GETDATE()) AND GETDATE()
							AND ihs.Part = ' + @PartTargetColumn + ')'
	EXEC (@SQL);
END
GO
/*
exec usp_get_BOMParts_ForIHS
	@UserId = 6667 ,                  
	@ClientId = 101,                      
	@SelectedClientID = 101
*/
