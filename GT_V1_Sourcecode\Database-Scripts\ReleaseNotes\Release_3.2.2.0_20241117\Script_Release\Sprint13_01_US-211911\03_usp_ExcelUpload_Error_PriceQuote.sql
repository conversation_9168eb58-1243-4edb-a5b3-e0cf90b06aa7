﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201934]		An.TranTan			06-May-2024		Update			Change the comparison logic between import supplier part and requirement part.
[US-205174]		An.TranTan			17-Jun-2024		Update			Re-format script + update logic:
																	- Update column number to validate because of new column CLIENT NO insert at Column2
																	- Specific client id for tbCustomerRequirement
[US-205174]		An.TranTan			05-Jul-2024		Update			Remove logic check supplier name within client, allow get from all clients
[US-205174]		Phuc Hoang			08-Jul-2024		Update			Remove logic check Duplicate
[BUG-208825]	An.TranTan			15-Jul-2024		Update			Update validate: check cuplicate for all 25 columns
																	- Between rows in the import file
																	- For rows in import file vs existed purchase request lines.
[BUG-208826]	An.TranTan			17-Jul-2024		Update			Remove check part no and allow import price for requirement has multiple part no
[BUG-208826]	An.TranTan			19-Jul-2024		Update			Show duplicate error message for all duplicate row in import file
[BUG-208826]	An.TranTan			19-Jul-2024		Update			Using NOT EXISTS instead of LEFT JOIN when validate REQUIREMENT to prevent duplicate record in case 
																		multiple requirements have same number within a client
[BUG-208826]	An.TranTan			23-Jul-2024		Update			Update error message and get exact error line number from import file instead of ROW_NUMBER()
[BUG-211786]	An.TranTan			05-Sep-2024		Update			Validate delivery date using UK format
[US-211911]		An.TranTan			16-Oct-2024		Update			Enhance validate data base on dynamic mapping columns
[US-211911]		An.TranTan			28-Oct-2024		Update			Remove logic stripAlphahnumeric for supplier name 
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_ExcelUpload_Error_PriceQuote]
    @UserId INT = 0,
    @ClientId INT = 0,
	@TargetColumns NVARCHAR(500) = NULL,
	@SelectedColumns NVARCHAR(500) = NULL
WITH RECOMPILE
AS
BEGIN
	SET NOCOUNT ON;

	IF OBJECT_ID('tempdb..#tbPriceQuoteImport_rawData') IS NOT NULL
		DROP TABLE #tbPriceQuoteImport_rawData
	CREATE TABLE #tbPriceQuoteImport_rawData
	(
		PriceQuoteImportId INT
		, Manufacturer NVARCHAR(MAX)
		, SupplierPart NVARCHAR(MAX)
		, OfferedQuantity NVARCHAR(MAX)
		, SupplierCost NVARCHAR(MAX)
		, SPQ NVARCHAR(MAX)
		, MOQ NVARCHAR(MAX)
		, SupplierName NVARCHAR(MAX)
		, MSL NVARCHAR(MAX)
		, Notes NVARCHAR(MAX)
		, Requirement NVARCHAR(MAX)
		, ClientNo NVARCHAR(MAX)
		, DateCode NVARCHAR(MAX)
		, QtyInStock NVARCHAR(MAX)
		, OfferStatus NVARCHAR(MAX)
		, BuyPrice NVARCHAR(MAX)
		, SellPrice NVARCHAR(MAX)
		, ShippingCost NVARCHAR(MAX)
		, Package NVARCHAR(MAX)
		, ROHS NVARCHAR(MAX)
		, Currency NVARCHAR(MAX)
		, FactorySealed NVARCHAR(MAX)
		, Region NVARCHAR(MAX)
		, LeadTime NVARCHAR(MAX)
		, LastTimeBuy NVARCHAR(MAX)
		, DeliveryDate NVARCHAR(MAX)
		, LineNumber INT
		, OriginalFilename NVARCHAR(200)
		, GeneratedFilename NVARCHAR(200)
	)

	--Get raw data with dynamic columns
	DECLARE @DynamicSQL NVARCHAR(MAX);
	SET @DynamicSQL = 'INSERT INTO #tbPriceQuoteImport_rawData(' + @TargetColumns  
					+		',PriceQuoteImportId,LineNumber,OriginalFilename,GeneratedFilename) '
					+ 'SELECT ' + @SelectedColumns + ', PriceQuoteImportId, LineNumber, OriginalFilename, GeneratedFilename '
					+ 'FROM BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData '
					+ 'WHERE CreatedBy=' + CAST(@UserId AS NVARCHAR(10))
	EXEC (@DynamicSQL);

	--convert temp data to correct data type to validate
	SELECT 
		PriceQuoteImportId
		,dbo.stripAlphahtestingMfr(SUBSTRING(Manufacturer, 1, 10)) AS ManufacturerName
		,dbo.stripAlphahnumeric2(SupplierPart) AS SupplierPart
		,FLOOR(dbo.stripNumeric(OfferedQuantity)) as Quantity
		,CAST(dbo.stripNumeric(SupplierCost) as FLOAT) as SupplierCost
		,dbo.stripAlphahnumeric2(SPQ) AS SPQ
		,dbo.stripAlphahnumeric2(MOQ) AS MOQ
		--,dbo.stripAlphahnumeric(SupplierName) AS SupplierName
		,SupplierName
		,dbo.stripAlphahnumeric2(SUBSTRING(MSL, 1, 10)) AS MSL
		,dbo.stripAlphahnumeric2(SUBSTRING(Notes, 1, 1000)) AS Notes
		,TRY_CAST(dbo.stripNumeric(Requirement) AS INT) AS RequirementNumber
		,TRY_CAST(dbo.stripNumeric(ClientNo) as INT) as ClientNo
		,dbo.stripAlphahnumeric2(DateCode) AS DateCode
		,dbo.stripAlphahnumeric2(SUBSTRING(QtyInStock, 1, 40)) AS QtyInStock
		,dbo.stripAlphahnumeric2(SUBSTRING(OfferStatus, 1, 30)) AS OfferStatus
		,CAST(dbo.stripNumeric(BuyPrice) as FLOAT) as BuyPrice
		,CAST(dbo.stripNumeric(SellPrice) as FLOAT) as SellPrice
		,CAST(dbo.stripNumeric(ShippingCost) as FLOAT) as ShippingCost
		,dbo.stripAlphahnumeric(SUBSTRING(Package, 1, 200)) AS Package
		,dbo.stripAlphahnumeric2(SUBSTRING(ROHS, 1, 30)) AS ROHS
		,dbo.stripAlphahnumeric(Currency) AS CurrencyCode
		,dbo.stripAlphahnumeric(SUBSTRING(FactorySealed, 1, 30)) AS FactorySealed
		,dbo.stripAlphahnumeric(SUBSTRING(Region, 1, 10)) AS Region
		,dbo.stripAlphahnumeric(SUBSTRING(LeadTime, 1, 100)) AS LeadTime
		,dbo.stripAlphahnumeric(SUBSTRING(LastTimeBuy, 1, 10)) AS LastTimeBuy
		,dbo.stripAlphahnumeric2(DeliveryDate) AS DeliveryDate
		,LineNumber
		,OriginalFilename
		,GeneratedFilename
		,CAST('' as nvarchar(max)) as ValidationMessage
	INTO #tbPriceQuoteImport_tempData
	FROM #tbPriceQuoteImport_rawData
	
	--narrow down customer requirement to query
	SELECT cr.CustomerRequirementNumber, cr.ClientNo
	INTO #tempCustomerRequirement
	FROM tbCustomerRequirement cr WITH(NOLOCK)
		JOIN #tbPriceQuoteImport_tempData td ON td.RequirementNumber = cr.CustomerRequirementNumber
			AND td.ClientNo = cr.ClientNo
	WHERE cr.BOMNo IS NOT NULL
	GROUP BY cr.CustomerRequirementNumber, cr.ClientNo;

    /*********** Updation Validation Query **************/
    --CurrencyCode No length 3  & mandotary--                                                            
	UPDATE #tbPriceQuoteImport_tempData
	SET ValidationMessage = ValidationMessage + 'Currency Code  accepts 3 characters.<br/>'
	WHERE LEN(CurrencyCode) < 3

    --Requirement mandotary and existed in HUBRFQ--                                                            
	update TmpR
	set TmpR.ValidationMessage = TmpR.ValidationMessage + 'Requirement does not Exist or HUBRFQ does not exist for this Requirement.<br/>'
    from #tbPriceQuoteImport_tempData TmpR
	where ISNULL(TmpR.RequirementNumber, 0) = 0
		OR (not exists (select 1 from #tempCustomerRequirement
						where CustomerRequirementNumber = TmpR.RequirementNumber
							and ClientNo = TmpR.ClientNo
						))

    --- DateCode length 5--                                                            
    update temp
    set temp.ValidationMessage = case
                                     when len(temp.DateCode) > 5 and temp.DateCode <> r.DateCode then
                                         temp.ValidationMessage
                                         + 'DateCode Field only accepts 5 characters and AlphaNumeric values.<br/>'
                                     when len(temp.DateCode) > 5 then
                                         temp.ValidationMessage + 'DateCode Field only accepts 5 characters.<br/>'
                                     else
                                         temp.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData temp
        inner Join #tbPriceQuoteImport_rawData r
            on r.PriceQuoteImportId = temp.PriceQuoteImportId

	-- Check suppliername existed in GT client
	update TmpR
	set TmpR.ValidationMessage = TmpR.ValidationMessage + 'This supplier does not exist on the DMCC client, unable to import.<br/>'
	from #tbPriceQuoteImport_tempData TmpR
	where not exists (
		select 1 from dbo.tbCompany co with (nolock)
		jOIN dbo.tbCompanyAddress ca with (nolock)
                 ON co.CompanyId = ca.CompanyNo AND ca.DefaultBilling = 1
		where co.Inactive = 0 
			AND co.POApproved = 1
			AND ca.CeaseDate IS NULL
			AND co.CompanyName = TmpR.SupplierName
	)

	--- Validate delivery date format--                                                            
    update TmpR
    set TmpR.ValidationMessage = case
                                     when TRY_PARSE(TmpR.DeliveryDate AS DATE USING 'en-GB') IS NULL then
                                         TmpR.ValidationMessage + 'Delivery Date is invalid. Accept formart: dd/mm/yyyy.<br/>'
                                     else
                                         TmpR.ValidationMessage
                                 end
    from #tbPriceQuoteImport_tempData TmpR

	--- Validate duplicate for all columns within import file ---
	;WITH cteDuplicateRows AS (
		SELECT TL.PriceQuoteImportId
			,COUNT(*) OVER (PARTITION BY RL.[Column1]
										,RL.[Column2]
										,RL.[Column3]
										,RL.[Column4]
										,RL.[Column5]
										,RL.[Column6]
										,RL.[Column7]
										,RL.[Column8]
										,RL.[Column9]
										,RL.[Column10]
										,RL.[Column11]
										,RL.[Column12]
										,RL.[Column13]
										,RL.[Column14]
										,RL.[Column15]
										,RL.[Column16]
										,RL.[Column17]
										,RL.[Column18]
										,RL.[Column19]
										,RL.[ClientId]
										,RL.[Column20]
										,RL.[Column21]
										,RL.[Column22]
										,RL.[Column23]
										,RL.[Column24]
										,RL.[Column25]) AS NumberOfOccur
		FROM #tbPriceQuoteImport_tempData TL
        INNER JOIN BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData RL
			ON RL.PriceQuoteImportId = TL.PriceQuoteImportId
	)
	UPDATE temp 
	SET temp.ValidationMessage = ValidationMessage + ' This Price has duplicated record(s) within this Import file.<br/>'
	FROM #tbPriceQuoteImport_tempData temp
		JOIN cteDuplicateRows cte ON cte.PriceQuoteImportId = temp.PriceQuoteImportId
	WHERE cte.NumberOfOccur > 1

	--- Validate duplicate for import file with existed purchase request line ---
	UPDATE TL 
	SET TL.ValidationMessage = ValidationMessage + ' Unable to import, a quote with these details already exists on the HUBRFQ.'
	FROM #tbPriceQuoteImport_tempData TL
		INNER JOIN #tempCustomerRequirement tcr
			ON tcr.CustomerRequirementNumber = TL.RequirementNumber
				AND tcr.ClientNo = TL.ClientNo
		INNER JOIN tbCustomerRequirement cr
			ON cr.CustomerRequirementNumber = tcr.CustomerRequirementNumber
				AND cr.ClientNo = tcr.ClientNo
		INNER JOIN tbPurchaseRequestLine prl WITH (NOLOCK)
			ON prl.CustomerRequirementNo = cr.CustomerRequirementId
		INNER JOIN tbPurchaseRequestLineDetail prld WITH (NOLOCK)
			ON prld.PurchaseRequestLineNo = prl.PurchaseRequestLineId
		INNER JOIN tbcompany cp WITH (NOLOCK)
			ON cp.CompanyId = prld.CompanyNo
		INNER JOIN tbCurrency cu WITH (NOLOCK)
			ON cu.CurrencyId = prld.CurrencyNo
		LEFT JOIN tbOfferStatus os WITH (NOLOCK)
			ON os.OfferStatusId = prld.OfferStatusNo
		LEFT JOIN tbRegion re WITH (NOLOCK)
			ON re.RegionId = prld.RegionNO
	WHERE lower(TL.SupplierName) = lower(cp.CompanyName) COLLATE SQL_Latin1_General_CP1_CI_AI
		AND dbo.ufn_get_fullpart(TL.SupplierPart) = prl.FullPart
		AND ISNULL(TL.ROHS,'') = ISNULL(prld.ROHSStatus,'')
		AND ISNULL(TL.ManufacturerName,'') = ISNULL(prld.ManufacturerName,'')
		AND ISNULL(TL.DateCode,'') = ISNULL(prld.DateCode,'')
		AND ISNULL(TL.Package,'') = ISNULL(prld.PackageType,'')
		AND ISNULL(TL.Quantity,0) = ISNULL(prld.Quantity,0)
		AND ISNULL(TL.OfferStatus,'') = ISNULL(os.[Name],'')
		AND ISNULL(TL.SPQ, '') = ISNULL(prld.SPQ, '')
		AND ISNULL(TL.FactorySealed, '') = ISNULL(prld.FactorySealed, '')
		AND ISNULL(TL.QtyInStock, '') = ISNULL(prld.TotalQuantityAvailableInStock, '')
		AND ISNULL(TL.MOQ, '') = ISNULL(prld.MOQ, '')
		AND ISNULL(TL.LastTimeBuy, '') = ISNULL(prld.LTB, '')
		AND TL.CurrencyCode = cu.CurrencyCode
		AND ISNULL(TL.BuyPrice, 0) = ISNULL(prld.BuyPrice, 0)
		AND ISNULL(TL.SellPrice, 0) = ISNULL(prld.SellPrice, 0)
		AND ISNULL(TL.ShippingCost, 0) = ISNULL(prld.ShippingCost, 0)
		AND ISNULL(TL.LeadTime, '') = ISNULL(prld.LeadTime, '')
		AND ISNULL(TL.Region, '') = ISNULL(re.RegionName, '')
		AND TRY_PARSE(TL.DeliveryDate AS DATE USING 'en-GB') = CONVERT(Date, prld.DeliveryDate, 103)
		AND ISNULL(TL.Notes, '') = ISNULL(prld.Notes, '')
		AND ISNULL(TL.MSL, '') = ISNULL(prld.MSL, '')
 
    /*********** Select Final Query  **************/
	IF(NOT EXISTS (SELECT TOP 1 1 FROM #tbPriceQuoteImport_tempData WHERE ISNULL(ValidationMessage, '') <> ''))
	BEGIN
	--delete old data
		DELETE BorisGlobalTraderImports.dbo.tbPriceQuoteToBeImported  
        WHERE CreatedBy = @UserId

		INSERT INTO BorisGlobalTraderImports.dbo.tbPriceQuoteToBeImported
		(
			[RequirementNo]
			,[Part]
			,[ManufacturerName]
			,[SupplierName]
			,[SupplierCost]
			,[SupplierPart]
			,[LeadTime]
			,[SPQ]
			,[MOQ]
			,[Quantity]
			,[QtyInStock]
			,[DateCode]
			,[PackageName]
			,[CurrencyCode]
			,[Description]
			,[ROHS]
			,[Region]
			,[FactorySealed]
			,[OfferStatus]
			,[OriginalFilename]
			,[GeneratedFilename]
			,[ClientId]
			,[SelectedClientId]
			,[CreatedBy]
			,[ImportDate]
			,[BuyPrice]
			,[SellPrice]
			,[ShippingCost]
			,[LastTimeBuy]
			,[DeliveryDate]
			,[MSL]
			,[LineNumber]
		)
		SELECT 
			td.RequirementNumber
			,td.[SupplierPart]
			,td.[ManufacturerName]
			,td.[SupplierName]
			,td.[SupplierCost]
			,td.[SupplierPart]
			,td.[LeadTime]
			,td.[SPQ]
			,td.[MOQ]
			,td.Quantity
			,td.[QtyInStock]
			,td.DateCode
			,td.Package
			,td.[CurrencyCode]
			,td.Notes
			,td.ROHS
			,td.[Region]
			,td.FactorySealed
			,td.[OfferStatus]
			,td.[OriginalFilename]
			,td.[GeneratedFilename]
			,@ClientId
			,td.ClientNo	--[SelectedClientId]
			,@UserId
			,GETDATE()
			,td.[BuyPrice]
			,td.[SellPrice]
			,td.[ShippingCost]
			,td.[LastTimeBuy]
			,td.[DeliveryDate]
			,td.[MSL]
			,td.LineNumber
		FROM #tbPriceQuoteImport_tempData td
		
	END
	SELECT r.[LineNumber] AS 'LINE NO.',
	       r.[Requirement]  AS 'REQUIREMENT',
	       r.[ClientNo]  AS 'CLIENT NO.',
	       r.[SupplierName]  AS 'SUPPLIER NAME',
	       r.[SupplierPart]  AS 'SUPPLIER PART',
	       r.[SupplierCost]  AS 'SUPPLIER COST',
	       r.[ROHS]  AS 'ROHS',
	       r.[Manufacturer]  AS 'MANUFACTURER',
	       r.[DateCode]  AS 'DateCode',
	       r.[Package]  AS 'PACKAGE',
	       r.[OfferedQuantity] AS 'OFFERED QUANTITY',
	       r.[OfferStatus] AS 'OFFER STATUS',
	       r.[SPQ] AS 'SPQ',
	       r.[FactorySealed] AS 'FACTORY SEALED',
	       r.[QtyInStock] AS 'QTY IN STOCK',
	       r.[MOQ] AS 'MOQ',
	       r.[LastTimeBuy] AS 'LAST TIME BUY',
	       r.[Currency] AS 'CURRENCY',
	       r.[BuyPrice] AS 'BUY PRICE',
	       r.[SellPrice] AS 'SELL PRICE',
	       r.[ShippingCost] AS 'SHIPPING COST',
	       r.[LeadTime] AS 'LEAD TIME',
	       r.[Region] AS 'REGION',
	       r.[DeliveryDate] AS 'Delivery Date',
	       r.[Notes] AS 'NOTES',
	       r.[MSL] AS 'MSL',
	       td.ValidationMessage as 'Reason for Excel Upload failed',
	       r.OriginalFilename
	FROM #tbPriceQuoteImport_tempData td
	    JOIN #tbPriceQuoteImport_rawData r on r.PriceQuoteImportId = td.PriceQuoteImportId
	WHERE ISNULL(td.ValidationMessage, '') <> ''
	ORDER BY r.[LineNumber] ASC;


	DROP TABLE #tbPriceQuoteImport_rawData;
	DROP TABLE #tbPriceQuoteImport_tempData;
	DROP TABLE #tempCustomerRequirement;

	SET NOCOUNT OFF;
END
GO


