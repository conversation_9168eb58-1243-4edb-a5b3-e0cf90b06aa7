Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/EnhancedInspection");this._objData.set_DataObject("EnhancedInspection");this._objData.set_DataAction("GetData")},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Types)for(t=0;t<n.Types.length;t++)this.addOption(n.Types[t].Name,n.Types[t].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.EnhancedInspection",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);