//-----------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//
// RP 12.10.2009:
// - retrofitted changes from v3.0.34 to get Name from resource file
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ViewLevel_ReceiveCRMA : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("ViewLevel_ReceiveCRMA");
            base.ProcessRequest(context);
        }

        protected override void GetData() {
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData)) {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                jsnList.AddVariable(AddItem(0, "AwaitingReceipt"));
                jsnList.AddVariable(AddItem(1, "All"));
                jsn.AddVariable("Types", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            } else {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }

        private JsonObject AddItem(int intID, string strResourceTitle) {
            JsonObject jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", intID);
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("Tabs", strResourceTitle));
            return jsnItem;
        }

    }


}
