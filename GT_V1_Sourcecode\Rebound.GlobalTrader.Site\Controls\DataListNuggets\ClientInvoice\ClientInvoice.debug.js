///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[005]      Prakash           11/04/2014         Add Client Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.prototype = {

    get_blnShowCanNotBeExported: function() { return this._blnShowCanNotBeExported; }, set_blnShowCanNotBeExported: function(v) { if (this._blnShowCanNotBeExported !== v) this._blnShowCanNotBeExported = v; },
    get_blnPOHub: function() { return this._blnPOHub; }, set_blnPOHub: function(value) { if (this._blnPOHub !== value) this._blnPOHub = value; },
    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/ClientInvoice";
        this._strDataObject = "ClientInvoice";
        this.showFilterField("ctlStatus", (!this._blnShowCanNotBeExported));
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnShowCanNotBeExported = null;
        this._blnPOHub = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._blnShowCanNotBeExported = (this._intCurrentTab == 1);
        this.showFilterField("ctlStatus", (this._intCurrentTab == 0));
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("CanNotBeExported", this._blnShowCanNotBeExported);
    },

    getDataOK: function() {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
             var aryData ;
             if (this._blnPOHub == true) {
             aryData = [
				$RGT_nubButton_ClientInvoice(row.ID, row.No)
				, $RGT_nubButton_Company(row.CompanyNo, row.Name)
                //, $RGT_nubButton_Company(row.GI, row.GINo)
                , $R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(row.GI, row.GINo), "")
				, $R_FN.writeDoubleCellValue($RGT_nubButton_PurchaseOrder(row.PONo, row.PO),$RGT_nubButton_InternalPurchaseOrder(row.IPONo, row.IPO))
				//, $R_FN.setCleanTextValue(row.URNNumber)
				, $R_FN.setCleanTextValue(row.INVDate)
				, $R_FN.writePartNo(row.Part, "")
				, row.Value
			];
			}
			else
			{
			 aryData = [
				$RGT_nubButton_ClientInvoice(row.ID, row.No)
				, $RGT_nubButton_Company(row.CompanyNo, row.Name)
                 //, $RGT_nubButton_GoodsIn(row.GI, row.GINo)
                 , $R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(row.GI, row.GINo), "")
				, $R_FN.writeDoubleCellValue(row.PO,$RGT_nubButton_InternalPurchaseOrder(row.IPONo, row.IPO))
				//, $R_FN.setCleanTextValue(row.URNNumber)
				, $R_FN.setCleanTextValue(row.INVDate)
				, $R_FN.writePartNo(row.Part, "")
				, row.Value
			];
			
			}
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },
    updateFilterVisibility: function() {
        this.getFilterField("ctlClient").show(this._blnPOHub);
    }
    


};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ClientInvoice", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
