using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class BOMMainInfo_Notify : Base
    {

		#region Locals

        protected IconButton _ibtnSend;
        protected IconButton _ibtnSend_Footer;

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "BOMMainInfo_Notify");
            AddScriptReference("Controls.Nuggets.BOMMainInfo.Notify.BOMMainInfo_Notify.js");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpButtons();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void WireUpButtons() {
            _ibtnSend = FindIconButton("ibtnSend");
            _ibtnSend_Footer = (IconButton)FindFooterIconButton("ibtnSend");
        }

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_Notify", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnSend", _ibtnSend.ClientID);
            if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", _ibtnSend_Footer.ClientID);
        }

	}
}
