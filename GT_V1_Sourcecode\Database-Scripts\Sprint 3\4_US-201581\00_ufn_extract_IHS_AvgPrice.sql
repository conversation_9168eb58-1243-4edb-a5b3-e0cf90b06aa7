SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF(OBJECT_ID(N'dbo.ufn_extract_IHS_AvgPrice', N'FN')) IS NOT NULL
    DROP FUNCTION dbo.ufn_extract_IHS_AvgPrice;
GO
-- =============================================
-- Author:		An.TranTan
-- Create date: 12-Jun-2024S
-- Description:	Extract Avg Price from Description as format: ....Avg Price : <value> (Date Updated ....
-- =============================================
CREATE FUNCTION dbo.ufn_extract_IHS_AvgPrice
(
	@Description NVARCHAR(MAX)
)
RETURNS FLOAT
AS
BEGIN
	DECLARE @AvgPriceLabel NVARCHAR(20) = N'Avg Price :';
	DECLARE @DateUpdatedLabel NVARCHAR(20) = N'(Date Updated :';

	DECLARE @Index1 INT = CHARINDEX(@AvgPriceLabel, @Description);
	DECLARE @Index2 INT = CHARINDEX(@DateUpdatedLabel, @Description);

	DECLARE @ExtractPrice Nvarchar(30) = null;
	DECLARE @Result FLOAT = null;

	IF(@Index1 > 0  AND @Index2 > (@Index1 + LEN(@AvgPriceLabel)))
	BEGIN
		SET @ExtractPrice = LTRIM(RTRIM(SUBSTRING(@Description, @Index1 + LEN(@AvgPriceLabel), @Index2 - @Index1 - LEN(@AvgPriceLabel)))); 
		SET @Result = TRY_PARSE(@ExtractPrice AS FLOAT);
	END

	RETURN @Result;
END
GO

