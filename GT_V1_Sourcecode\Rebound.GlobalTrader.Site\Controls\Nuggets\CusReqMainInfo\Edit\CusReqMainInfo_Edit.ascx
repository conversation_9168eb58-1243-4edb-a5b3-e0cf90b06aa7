<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           21/11/2012   Please make Rosh as a compulsory field on the following:- Requirements,Quotes,PO,SO
[003]      Suhail          12/04/2018    changes MSL text to drop down  list
[004]      Ravi            29/08/2023   RP-2227 (AS6081)
--%>
<%@ Control Language="C#" CodeBehind="CusReqMainInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save"
            IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel"
            IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "CusReqMainInfo_Edit")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server" class="partsreq">

            <asp:TableRow class="">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Customer")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblCustomer" runat="server" />
                </asp:TableCell>
            </asp:TableRow>



            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Contact")%> </label>
                </asp:TableCell>
                <asp:TableCell colspan="5" >
                    <asp:Label ID="lblContact" runat="server" />
                </asp:TableCell>
            </asp:TableRow>



            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="TraceabilityError" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "RequirementforTraceability")%><span></span> *
                        </span></label>
                </asp:TableCell>
                <asp:TableCell ID="TraceabilityError1" class="fieldTD" colspan="5">
                    <ReboundDropDown:ReqData ID="ddlRequirementforTraceability" runat="server" SType="RTrace" />
                </asp:TableCell>
            </asp:TableRow>




            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="SalespersonError" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Salesperson")%><span> * </span></label>
                </asp:TableCell>
                <asp:TableCell ID="SalespersonError1" class="fieldTD" colspan="5">
                    <ReboundDropDown:Employee ID="ddlSalesman" runat="server" />
                </asp:TableCell>


            </asp:TableRow>


            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="QuantityError" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Quantity")%><span> * </span></label>
                </asp:TableCell >
                <asp:TableCell ID="QuantityError1" class="fieldTD" colspan="4">
                    <ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" TextBoxMode="Numeric" width="110" />
              <span class="sndcol">
                    <label><%=Functions.GetGlobalResource("FormFields", "Usage")%></label>
                </span>
                    <ReboundDropDown:Usage ID="ddlUsage" runat="server" Width="140" />
               
                </asp:TableCell>
                <asp:TableCell rowspan="6" class="chkblock">

<span>
                    <label><%=Functions.GetGlobalResource("FormFields", "AlternativesAccepted")%></label>
                    <ReboundUI:ImageCheckBox ID="chkAlternativesAccepted" runat="server" Enabled="true" />

                </span>
                <span>
                    <label><%=Functions.GetGlobalResource("FormFields", "TestingRequired")%></label>
                    <ReboundUI:ImageCheckBox ID="chkTestingRequired" runat="server" Enabled="true" />
                </span>
                <span>

                    <label><%=Functions.GetGlobalResource("FormFields", "RefirbsAcceptable")%></label>
                    <ReboundUI:ImageCheckBox ID="chkRefirbsAcceptable" runat="server" Enabled="true" />
                </span>
                <span>

                    <label><%=Functions.GetGlobalResource("FormFields", "FactorySealed2")%></label>
                    <ReboundUI:ImageCheckBox ID="chkFactorySealedSource" runat="server" Enabled="true" />
                </span>
                <span>

                    <label><%=Functions.GetGlobalResource("FormFields", "OrderToPlace")%></label>
                    <ReboundUI:ImageCheckBox ID="chkOrderToPlace" runat="server" Enabled="true" />
                </span>
                <span>
                    <label><%=Functions.GetGlobalResource("FormFields", "RepeatBusiness")%></label>
                    <ReboundUI:ImageCheckBox ID="chkRepeatBusiness" runat="server" Enabled="true" />
                </span>
                




                </asp:TableCell>
            </asp:TableRow>


            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="ddlTypeError" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Type")%><span> * </span></label>
                </asp:TableCell>
                <asp:TableCell ID="ddlTypeError1" class="fieldTD" colspan="4">
                    <ReboundDropDown:ReqData ID="ddlType" runat="server" SType="RType" width="110" />
                    <span class="sndcol">
                    <label><%=Functions.GetGlobalResource("FormFields", "EAU")%></label>
                    </span>
                    <ReboundUI:ReboundTextBox ID="txtEau" runat="server" Width="150" />
                </asp:TableCell>

            </asp:TableRow>




            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "PQA")%></label>
                </asp:TableCell>
                <asp:TableCell class="fieldTD">
                    <ReboundUI:ImageCheckBox ID="chkPQA" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell colspan="3">
                </asp:TableCell>
            </asp:TableRow>




            <asp:TableRow class="bottomborder" ID="showhidepartnolable" >
                <asp:TableCell ID="parterror4" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "PartNo")%><span> * </span></label>
                </asp:TableCell>
                <asp:TableCell ID="parterror2" colspan="3">
                    <asp:Label ID="lblPartNo" runat="server" UppercaseOnly="true"></asp:Label>
                    <%--<asp:Label ID="lblError" Text="" runat="server" CssClass="PartDetailsGridGoError"></asp:Label>--%>
                </asp:TableCell>
                <asp:TableCell>
                </asp:TableCell>
            </asp:TableRow>

              <asp:TableRow class="bottomborder" ID="showhidePartnotext">

                <asp:TableCell ID="parterror" class="lableTD">
                    <label id="lblparts"><%=Functions.GetGlobalResource("FormFields", "PartNo")%><span> *
                        </span></label>
                    <a id="ParttypeSearch1">
                        <img src="images/x.gif" style="height:12px;width:25px;border-width:0px;"></a>
                </asp:TableCell>
                <asp:TableCell ID="parterror1" class="fieldTD" colspan="4">
                    <ReboundUI:ReboundTextBox ID="txtPartNo" runat="server"  UppercaseOnly="true"  />
                    <asp:Label ID="lblError" Text="Type 3 chars to search" runat="server"
                        CssClass="PartDetailsGridGoError"></asp:Label>
                    <ReboundUI:ComboNew class="wdt120" ID="cmbIHS" runat="server"
                        AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="IHSPartSearch"
                        ParentControlID="cmbIHS12" FilterField="IHSPartSearch" />

                    <span class="sndcol" style="width: 38px; float: left; display: block; margin-left: 35px;">
                        <asp:Label ID="btn1" runat="server" Style="cursor: pointer;"
                            CssClass="PartDetailsGridGoBtnSearchIcon2"></asp:Label>
                        <asp:Label ID="btnClear" Text="Clear" runat="server" Style="cursor: pointer;"
                            CssClass="PartDetailsGridGoBtn2"></asp:Label>

                        <script>
                            $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt').keydown(function (e) {
                                if (e.which == 8) {
                                    if ($('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt').val().length == 1) {
                                       // $("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btnClear").trigger('click');
                                    }
                                }

                            });

                            $(document).on('click', function (e) {
                                if ($(e.target).closest("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS").length === 0) {
                                    document.getElementById('ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12aut_hypClose').click();
                                }
                                if ($(e.target).closest("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt").length > 0) {
                                    $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12aut_ctl05').removeClass('invisible');
                                }

                            });

                            $(function () {
                                $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt').keyup(function () {
                                    this.value = this.value.toLocaleUpperCase();
                                });
                            });
                            $(function () {
                                $('#SearchtxtPartNo').keyup(function () {
                                    this.value = this.value.toLocaleUpperCase();
                                });
                            });
                            

                        </script>
                    </span>

                   <a class="tooltip "
                        href="#"><%=Functions.GetGlobalResource("FormFields", "ihsInformation")%>

                        
                        <table class="tooltiptext" runat="server" style="width: 220px; text-align: left;">
                            <tr>
                                <td>
                                    <p>Following fields are coming from IHS service</p>
                                    <p>
                                        Part, Part Status, MSL,
                                        Descriptions, Packaging Code, HTS Code,
                                        Packaging Method, Country Of Origin, Manufacturer, Average Global Price
                                    </p>
                                </td>
                            </tr>
                        </table>
                    </a>

                </asp:TableCell>

            </asp:TableRow>
            <%-- [004] start --%>
            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="AS6081Error" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "AS6081Label")%><span></span>
                        </span></label>
                </asp:TableCell>
                <asp:TableCell ID="AS6081Error1" class="fieldTD" colspan="4">
                    <ReboundDropDown:CounterfeitElectronicParts ID="ddlAS6081" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <%-- [004] End --%>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label
                        title="Add all suggested Rebound alternatives for this requirement."><%=Functions.GetGlobalResource("FormFields", "Alternatives")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkAlternatives" runat="server" Checked="false" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell colspan="3">
                     <label id="lblservicemsgerror"
                        style="color: #990000; font-style: normal; font-family: Tahoma !important; font-size: 12px;"></label>
                </asp:TableCell>
            </asp:TableRow>





            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="ManufacturerError" class="lableTD">
                    <label id="lblmrf"><%=Functions.GetGlobalResource("FormFields", "Manufacturer")%><span> * </span></label>
                </asp:TableCell>
                <asp:TableCell ID="ManufacturerError1">
                    <ReboundUI:ComboNew ID="cmbManufacturer" runat="server"
                        AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers"
                        ParentControlID="cmbManufacturer" /><span id="spnManufacturer"></span>
                </asp:TableCell>
                <asp:TableCell colspan="3">
                    <label id="lblRsMFREdit"><span id="spanmfrEdit" class="MFRResticted"></span></label>
                </asp:TableCell>
            </asp:TableRow>


            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CustomerPartNo")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ReboundTextBox ID="txtCustomerPartNo" runat="server" UppercaseOnly="true" />
                </asp:TableCell>
                <asp:TableCell colspan="4">
                </asp:TableCell>
            </asp:TableRow>


            <asp:TableRow class="">
                <asp:TableCell ID="CustomerTargetPriceError" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CustomerTargetPrice")%><span> * </span></label>
                </asp:TableCell>
                <asp:TableCell ID="CustomerTargetPriceError1" class="fieldTD" colspan="4">
                    <ReboundUI:ReboundTextBox ID="txtTargetPrice" runat="server" TextBoxMode="currency"
                        FormatDecimalPlaces="true" Width="100" />

                    <a id="myplasegrde" class="magicbox" style="margin-top: -7px;margin-left:10px;"
                        href="javascript:void(0);">
                        <img src="images/blackbox.png" style="height: 29px; width: 29px; border-width: 0px;">
                        <div class="LoaderIHSPopup" id="divBlockBox" style="left:280px;">
                            <div>
                                <div class="cssloadIHSloader">Loading..</div>
                            </div>
                        </div>
                    </a>
                    <div id="mydiv" style="display: none">
                        <div id="closePoppartdetails" class="popupCloseButton">&times;</div>
                        <table class="chat-popup tbForm" id="tbpartdet" style="display: none;" cellspacing="0"
                            cellpadding="0">
                             <tr>
                                <th colspan="2" style="text-align: left;" class="title firstth"><b>Part Name:</b> <b><span
                                    id="spnpartname"></span></b>
                                </th>
                            </tr>
                            <tr>
                                <td class="title firttd">Sold to Customer: <span id="spnsoldtocuston"></span>
                                </td>

                                <td class="title secondtd">Current Customer: <span id="spnCurrentCust"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="title firttd">Sale Price: <span id="spnLastSoldPrice"></span>
                                </td>
                                <td class="title secondtd">Sale Price: <span id="spnCustLastSoldPrice"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="title firttd">Sale Date: <span id="spnlastsoldon"></span>
                                </td>
                                <td class="title secondtd">Sale Date: <span id="spnCustlastsoldon"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="title firttd">Quantity: <span id="spnLastQuantity"></span>
                                </td>

                                <td class="title secondtd">Quantity: <span id="spnCustQuantity"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="title firttd">Supplier Type: <span id="spnLastSupplierType"></span>
                                </td>

                                <td class="title secondtd">Supplier Type: <span id="spnCustSupplierType"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="title firttd">Date Code: <span id="spnLastDatecode"></span>
                                </td>

                                <td class="title secondtd">Date Code: <span id="spnCustDatecode"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="title firttd">Date Purchased: <span id="spnLastDatePurchased"></span>
                                </td>

                                <td class="title secondtd">Date Purchased: <span id="spnCustDatePurchased"></span>
                                </td>
                            </tr>
                            <tr>
                                <td class="title firttd">Customer Region: <span id="spnLastCustomerRegion"></span>
                                </td>

                                <td class="title secondtd">Customer Region: <span id="spnCustomerRegion"></span>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" class="title last-td">12 months Average Rebound Re-Sale Price: <span
                                    id="spnAvgPrice"></span>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" class="title last-td">Lowest Sale Price In Last 12 Months: <span id="spnLastPricePaid12"></span>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" class="title last-td">Best Buy Price In Last 12 Months: <span id="spnCleintBestPricePaid12"></span>
                                </td>
                            </tr>


                            <tr style="display: none;">
                                <td class="title">Stock Detail
                                    <table cellpadding="0" cellspacing="0" border="0" class="stockQuantities">
                                        <tbody>
                                            <tr>
                                                <td class="inStock">
                                                    <div class="number">
                                                        <span id="spnInStock"></span>
                                                    </div>
                                                    In Stock
                                                </td>
                                                <td class="sep">&nbsp;</td>
                                                <td class="onOrder">
                                                    <div class="number"><span id="spnOnOrder"></span></div>
                                                    On Order
                                                </td>
                                                <td class="sep">&nbsp;</td>
                                                <td class="allocated">
                                                    <div class="number"><span id="spnAllocated"></span></div>
                                                    Allocated
                                                </td>
                                                <td class="sep">&nbsp;</td>
                                                <td class="available" style="color: blueviolet">
                                                    <div class="number"><span id="spnAvailable"></span></div>
                                                    Available
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </td>
                            </tr>
                        </table>

                    </div>



                <span class="sndcol" style="margin-left: 30px;">
                    <label><%=Functions.GetGlobalResource("FormFields", "CompetitorBestoffer")%></label>
                </span>
                    <ReboundUI:ReboundTextBox style="width:120px" ID="txtCompetitorBestoffer" runat="server" TextBoxMode="currency"
                        FormatDecimalPlaces="true" Width="100" />
                </asp:TableCell>


            </asp:TableRow>

            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="CurrencyError">
                    <label><%=Functions.GetGlobalResource("FormFields", "Currency")%><span> * </span></label>

                </asp:TableCell>
                <asp:TableCell colspan="5" ID="CurrencyError1">
                    <ReboundDropDown:BuyCurrencyByGlobalNo ID="ddlCurrency" runat="server" />

                </asp:TableCell>
            </asp:TableRow>


            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "ROHS")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundDropDown:ROHSStatus ID="ddlROHS" runat="server" NoValue_Value="" InitialValue="" />
                </asp:TableCell>
                <asp:TableCell colspan="4">
                </asp:TableCell>
            </asp:TableRow>


            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label id="lblpartstaus"><%=Functions.GetGlobalResource("FormFields", "LifeCycleStage")%></label>
                </asp:TableCell>
                <asp:TableCell class="fieldTD" colspan="4">
                    <asp:Label ID="LableLifeStatus" runat="server" />
                    <span class="sndcol" style="margin-left: 73px; display: none;">
                        <label><%=Functions.GetGlobalResource("FormFields", "Obsolete")%></label>
                    </span>
                    <ReboundUI:ImageCheckBox ID="chkObsolete" runat="server" Enabled="true" style="vertical-align: bottom; display: none;" />
                    <span class="sndcol" style="width: 98px; display: none;">
                        <label><%=Functions.GetGlobalResource("FormFields", "LastTimeBuy")%></label>
                    </span>
                    <ReboundUI:ImageCheckBox ID="chkLastTimeBuy" runat="server" Enabled="true" style="vertical-align: bottom; display: none;" />
                </asp:TableCell>

                <asp:TableCell style="display:none;">
                    <label><%=Functions.GetGlobalResource("FormFields", "LastTimeBuyDate")%></label>
                    <ReboundUI:ReboundTextBox ID="textLasttimebuyDate" runat="server" Width="120" />
                    <ReboundUI:Calendar ID="calLasttimebuyDate" runat="server" RelatedTextBoxID="textLasttimebuyDate" />
                </asp:TableCell>

                <asp:TableCell style="display:none;">
                    <label><%=Functions.GetGlobalResource("FormFields", "LastTimeShipDate")%></label>
                    <ReboundUI:ReboundTextBox ID="txtLasttimeshipDate" runat="server" Width="120" />
                    <ReboundUI:Calendar ID="calLasttimeshipDate" runat="server"
                        RelatedTextBoxID="txtLasttimeshipDate" />
                </asp:TableCell>
            </asp:TableRow>

            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "DateCode")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtDateCode" runat="server" Width="60" MaxLength="5"
                        UppercaseOnly="true" />
                </asp:TableCell>
            </asp:TableRow>

            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="ProductError" class="lableTD">

                    <label id="lblMatchproduct"><%=Functions.GetGlobalResource("FormFields", "Product")%><span> *
                        </span></label>
                </asp:TableCell>
                <asp:TableCell ID="ProductError1">

                    <ReboundUI:ComboNew ID="cmbProducts" runat="server"
                        AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Products"
                        ParentControlID="cmbProducts" />
                </asp:TableCell>
                <asp:TableCell>
                    <label id="lblihsproduct"><%=Functions.GetGlobalResource("FormFields", "IHSProduct")%></label>
                    <span id="spnIHSProduct"></span>
                </asp:TableCell>

                <asp:TableCell>
                    <label id="lblihsHTSCode"><%=Functions.GetGlobalResource("FormFields", "HTSCode")%></label>
                    <span id="spnHTSCode"></span>
                </asp:TableCell>

                <asp:TableCell>
                    <label id="lblduty"><%=Functions.GetGlobalResource("FormFields", "Duty")%></label>
                    <span id="spnIHSDutyCode"></span>
                </asp:TableCell>

                <asp:TableCell>
                    <label id="lblCoo"><%=Functions.GetGlobalResource("FormFields", "CountryOfOrigin")%></label>
                    <span id="spnIHSCountryOfOrigin"></span>
                </asp:TableCell>

            </asp:TableRow>





            <asp:TableRow class="">
                <%--<asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Package")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundDropDown:Package ID="ddlPackage" runat="server" />
                </asp:TableCell>--%>
                <asp:TableCell  class="lableTD">
                    <label id="lblPackage"><%=Functions.GetGlobalResource("FormFields", "Package")%><span>
                        </span></label>
                </asp:TableCell>
                <asp:TableCell  colspan="2">
                    <ReboundUI:ComboNew class="wdt195 " ID="cmbPackage" runat="server"
                        AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Packages"
                        ParentControlID="cmbPackage" />
                    <span id="spnPackage"></span>
                </asp:TableCell>
                 <asp:TableCell colspan="3">
                 <label id="lblECCNCode"><%=Functions.GetGlobalResource("FormFields", "ECCNCode")%></label>
                <asp:TableCell style="margin-left:8px; "> 
                    <asp:Label ID="LableECCNCode" runat="server" style=" display:none;" />
                     <ReboundUI:ComboNew class="wdt195 " ID="cmbPartEccnMapped" runat="server"
                        AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="PartECCNSearch"
                        ParentControlID="cmbPartEccnMapped" />

                </asp:TableCell>    
                </asp:TableCell>
                <asp:TableCell colspan="0">
                    
                </asp:TableCell>
            </asp:TableRow>



            <asp:TableRow class="">
                <asp:TableCell class="lableTD">
                    <label id="lblmls"><%=Functions.GetGlobalResource("FormFields", "MSL")%></label>
                </asp:TableCell>
                <asp:TableCell>

                    <ReboundDropDown:MSLLevel ID="ddlMsl" runat="server" ></ReboundDropDown:MSLLevel>
                     &nbsp;&nbsp;<span id="spnMSL"></span>
                </asp:TableCell>
                <asp:TableCell colspan="4">
                </asp:TableCell>
            </asp:TableRow>



            <asp:TableRow class="">

                <asp:TableCell ID="DateRequiredError" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "DeliveryDateRequired")%><span> *
                        </span></label>

                </asp:TableCell>
                <asp:TableCell ID="DateRequiredError1">
                    <ReboundUI:ReboundTextBox ID="txtDateRequired" runat="server" />
                    <ReboundUI:Calendar ID="calDateRequired" runat="server" RelatedTextBoxID="txtDateRequired" />
                </asp:TableCell>
                <asp:TableCell colspan="4">
                </asp:TableCell>
            </asp:TableRow>





            <asp:TableRow class="">

                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "RFQClosingDate")%></label>
                </asp:TableCell>
                <asp:TableCell  colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtRFQClosingDate" runat="server" />
                    <ReboundUI:Calendar ID="calRFQClosingDate" runat="server" RelatedTextBoxID="txtRFQClosingDate" />
                </asp:TableCell>

                



            </asp:TableRow>

            <asp:TableRow class="">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CustomerDecisionDate")%></label>
                    </asp:TableCell>
                    <asp:TableCell colspan="5">
                   
                    <ReboundUI:ReboundTextBox ID="txtCustomerDecisionDate" runat="server" Width="150" />
                    <ReboundUI:Calendar ID="calCustomerDecisionDate" runat="server"
                        RelatedTextBoxID="txtCustomerDecisionDate" />
                </asp:TableCell>
            </asp:TableRow>




            <asp:TableRow class="">

                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "QuoteValidityRequired")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundDropDown:ReqData ID="ddlQuoteValidityRequired" runat="server" SType="day" />
                </asp:TableCell>
                <asp:TableCell colspan="4">
                </asp:TableCell>

            </asp:TableRow>

            <asp:TableRow class="  ">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "BOMChk")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkBOM" runat="server" Enabled="true" />
                </asp:TableCell>

                <asp:TableCell colspan="2">
                    <label><%=Functions.GetGlobalResource("FormFields", "BOMName")%></label>
                    <ReboundUI:ReboundTextBox ID="txtBOMName" runat="server" />
                </asp:TableCell>


                <asp:TableCell class="" colspan="2">
                    <label><%=Functions.GetGlobalResource("FormFields", "PartWatch")%></label>
                    <ReboundUI:ImageCheckBox ID="chkPartWatch" runat="server" Enabled="true" />
                </asp:TableCell>


            </asp:TableRow>





            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IPOBOM")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundDropDown:BOM ID="ddlBOM" runat="server" />
                </asp:TableCell>
                <asp:TableCell colspan="4">
                </asp:TableCell>
            </asp:TableRow>


            <asp:TableRow >
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "InternalNotes")%></label>
                </asp:TableCell>

                <asp:TableCell colspan="2">
                    <ReboundUI:ReboundTextBox ID="txtInstructions" runat="server" Rows="2" Style="height: 100px;width: 260px;"
                        TextMode="multiLine" CountChar="true" RelatedLabelID="lbledittxtInstructions" />
                </asp:TableCell>

                <asp:TableCell>
                    <label><%=Functions.GetGlobalResource("FormFields", "CustomerNotes")%></label>
                </asp:TableCell>

                <asp:TableCell colspan="2">

                    <ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Rows="2" TextMode="multiLine"
                        Style="height: 100px;" CountChar="true" RelatedLabelID="lbledittxtNotes" />

                </asp:TableCell>

            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                 <asp:TableCell >
                </asp:TableCell>
                  <asp:TableCell colspan="2">
                 <div style="float:left;display: inline-block;"><label><%=Functions.GetGlobalResource("FormFields", "MaxLengthCountingVIew")%></div><label id="lbledittxtInstructions"></label>
                </asp:TableCell>
               
                 <asp:TableCell >
                </asp:TableCell>
                <asp:TableCell colspan="3">
                <div style="float:left;"><label><%=Functions.GetGlobalResource("FormFields", "MaxLengthCountingVIew")%></div><label id="lbledittxtNotes"></label>
                </asp:TableCell>
             </asp:TableRow>




            <asp:TableRow CssClass="">

                <asp:TableCell class="lableTD">
                    <label
                        style="display: none;"><%=Functions.GetGlobalResource("FormFields", "TargetSellPrice")%><span> *
                        </span></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ReboundTextBox ID="txtTargetSellPrice" runat="server" Style="display: none;"
                        TextBoxMode="currency" FormatDecimalPlaces="true" />
                </asp:TableCell>
                <asp:TableCell colspan="4">
                </asp:TableCell>
            </asp:TableRow>


            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "SupportTeamMember")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ComboNew ID="cmbSalespersion" runat="server"
                        AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="SalesPersion"
                        ParentControlID="cmbSalespersion" />
                </asp:TableCell>
                <asp:TableCell colspan="4"></asp:TableCell>

            </asp:TableRow>





            <asp:TableRow>
                <asp:TableCell colspan="6">
                    <script src="js/jquery.min.js"></script>
         <style type="text/css">
                        .lableTD {
                            width: 200px;
                        }

                        .fieldTD {
                            width: 215px;
                        }
                        
          #lbledittxtInstructions {
    padding-right: 2px!important;
    vertical-align: inherit;
    font-weight: bold;
    font-size: 11px;
    color: #d3fFcC;
}
                        table.formRows textarea,
            table.formRows input,
            table.formRows select {
                width: auto;
            }

            table.formRows td {
                padding: 5px 0px 5px 0;
                white-spacing: none;
            }

            .formRows label {
                padding-right: 7px;
                vertical-align: inherit;
                font-weight: bold;
                font-size: 11px;
                color: #d3fFcC;
            }

            .formRows label b {
                padding-left: 7px;
                color: #005100;
            }

            .wdt240,
            #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtInstructions,
            #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtNotes {
                width: 250px;vertical-align: inherit;
            }

                        .wdt195,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbManufacturertxt,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbProductstxt,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtCustomerPartNo,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlContact_ddl,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlRequirementforTraceability_ddl,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlSalesman_ddl,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlCurrency_ddl,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlROHS_ddl,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPackagetxt{
                            width: 175px !important;
                            display: inline-block;

                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlPackage_ddl {
                            width: 178px !important;
                            display: inline-block;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12txt {
                            width: 140px !important;
                            float: left;
                            display: inline-block;
                        }

                        .wdt150,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_SearchtxtPartNo {
                            width: 150px !important;
                            display: inline-block;
                        }

                        #cctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtBOMName {
                            width: 154px !important;
                            display: inline-block;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtQuantity {
                            width: 103px;
                            display: inline-block;
                        }

                        .wdt140 {
                            width: 140px !important;
                            display: inline-block;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtDateRequired,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtRFQClosingDate,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtCustomerDecisionDate {
                            width: 156px !important;
                            display: inline-block;
                        }

                        .wdt120
                        {
                            width: 120px !important;
                            float: left;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtTargetPrice {
                            width: 90px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlUsage_ddl {
                            width: 154px;
                            margin-left: -4px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctlSelectCompany_ctlDB_ctlCompanyName_chkOn {
                            width: auto !important;
                        }

                        
                        #spnMSL {
                            width: auto;
                        }
                         #spnPackaging
                        {
                            width: auto;
                            margin-left: -25px;
                        }

                        table.formRows td.title {
                            width: 190px;
                        }

                        span.PartDetailsGridGoBtnSearchIcon2 {
                            margin-left: 8px;
                            width: 16px;
                            display: block;
                            float: left;
                            height: 16px;
                        }

                        span.PartDetailsGridGoBtn2 {
                            padding: 2px 0px;
                            margin-left: 0px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctlSendMailMessage_ctlBody_txtBody,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctlSendMailMessage_ctlSubject_txtSubject,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtUserMessage {
                            width: 286px !important;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctlSendMailMessage_ctlTo_txtTo {
                            width: 177px !important;
                        }

                        .wdtnote {
                            width: 320px;
                        }

                        div.quickSearch {
                            width: 57%;
                            margin-top: -2px;
                        }


                        .tooltip {
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn1 {
                            margin-left: -17px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btnClear {
                            margin-left: -7px;
                        }

                        .tooltip {
                            position: relative;
                            display: inline-block;
                            border-bottom: 1px dotted black;
                        }

                            .tooltip .tooltiptext {
                                visibility: hidden;
                                /*width: 120px;*/
                                /*height: 200px;*/
                                background-color: #fff;
                                color: #333;
                                text-align: center;
                                border-radius: 6px;
                                padding: 2px 0;
                                position: absolute;
                                z-index: 1;
                                top: 150%;
                                left: 50%;
                                margin-left: -111px;
                            }


                                .tooltip .tooltiptext::after {
                                    content: "";
                                    position: absolute;
                                    bottom: 100%;
                                    left: 50%;
                                    margin-left: -5px;
                                    border-width: 5px;
                                    border-style: solid;
                                    border-color: transparent transparent black transparent;
                                }

                            .tooltip:hover .tooltiptext {
                                visibility: visible;
                            }

                        /* Button used to open the chat form - fixed at the bottom of the page */
                        .open-button {
                            /*top: 144px; 
                        left: 1076px;*/
                            position: absolute;
                            bottom: -3px;
                            right: 15px;
                            border: 3px solid #f1f1f1;
                            z-index: 9;
                            /* bottom: 28px; */
                            right: 1px;
                            width: 25px;
                            font-size: xx-large;
                            text-align: center;
                            height: 20px;
                            /* color: black; */
                            background-color: black;
                            /*padding-top: 5px;*/
                        }

                       .open-buttons {
                /*top: 144px; 
            left: 1076px;*/
                /*position: absolute;*/
                bottom: -3px;
                /*right: 15px;*/
                border: 3px solid #f1f1f1;
                z-index: 9;
                /* bottom: 28px; */
                /*right: 1px;*/
                width: 25px;
                font-size: xx-large;
                text-align: center;
                height: 20px;
                /* color: black; */
                background-color: black;
                padding-top: 5px;
            }

            /* The popup chat - hidden by default */
            .chat-popup {
                /*display: none;
            position: absolute;*/
                bottom: 29px;
                border: 3px solid #f1f1f1;
                z-index: 9;
                bottom: 28px;
                right: 1px;
                width: 300px;
                font-size: xx-large;
                background-color: darkslategray;
                text-align: right;
                min-height: 100px;
            }


                       .popupCloseButton {
                border: 3px solid #999;
                border-radius: 50px;
                cursor: pointer;
                display: inline-block;
                font-family: arial;
                font-weight: bold;
                position: absolute;
                top: -17px;
                right: -5px;
                font-size: 25px;
                line-height: 10px;
                width: 30px;
                height: 20px;
                text-align: center;
                background-color: black;
            }

                        #mydiv {
                top: 151px;
                right: 254px;
                z-index: 9;
                font-size: xx-large;
                background-color: white;
                text-align: right;
                position: absolute;
                height: 0px;
                width: 450px;
            }

                        #mydivheader {
                            top: 191px;
                            left: 800px;
                            cursor: move;
                            z-index: 10;
                        }

                        .tbForm {
                            color: black;
                            text-align: left;
                            width: 100%;
                            margin: 6px 0px;
                        }

                        /* IHS Part Search Popup css start */


                        /* The Modal (background) */
                        .modal {
                            display: none;
                            /* Hidden by default */
                            position: fixed;
                            /* Stay in place */
                            z-index: 1;
                            /* Sit on top */
                            padding-top: 100px;
                            /* Location of the box */
                            left: 0;
                            top: 0;
                            width: 100%;
                            /* Full width */
                            height: 100%;
                            /* Full height */
                            overflow: auto;
                            /* Enable scroll if needed */
                            background-color: rgb(0, 0, 0);
                            /* Fallback color */
                            background-color: rgba(0, 0, 0, 0.4);
                            /* Black w/ opacity */
                        }

                        /* Modal Content */
                        .modal-content {
                            background-color: #66a75e;
                            margin: auto;
                            padding: 20px;
                            border: 1px solid #66a75e;
                            width: 50%;
                            position: relative;
                            overflow: hidden;
                        }

                            .modal-content table tr td,
                            .modal-content table tr th {
                                text-align: left;
                            }

                        /* The Close Button */
                        .close {
                            color: #aaaaaa;
                            float: right;
                            font-size: 28px;
                            font-weight: bold;
                        }

                            .close:hover,
                            .close:focus {
                                color: #000;
                                text-decoration: none;
                                cursor: pointer;
                            }
                        .bgbase2 {
                            float: left;
                            bottom: 26px;
                            right: 18px;
                            background-color: #5f9553;
                           
                        }

                        .okbtn {
                            cursor: pointer;
                            position: absolute;
                            bottom: 26px;
                            right: 18px;
                            
                            background: #5e845a;
                            float: left;
                            border-radius: 2px;
                            border: 1px #5e845a solid;
                            font-weight: bold;
                            display: block !important;
                        }

                            .okbtn:hover {
                                background: #56a34e;
                            }

                        .GridPartdetails table.dataTable td {
                            border-color: #5d8857;
                            border-top-color: rgb(93, 136, 87);
                            background-color: #c8fac2;
                            color: #000;
                            font-size: 10px;
                            font-family: Verdana !important;
                            position: relative;
                            border-top: none !important;
                        }

                        .LoaderPopup {
                            background: rgba(0, 0, 0, .4);
                            cursor: pointer;
                            display: none;
                            /* height: auto; */
                            height: 93%;
                            position: absolute;
                            text-align: center;
                            top: 17px;
                            width: 92%;
                            right: 10px;
                            z-index: 10000;
                        }

                        .cssload-loader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 146px;
                            top: -102%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206, 66, 51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }

                        .cssloadIHSloader {
                width: 244px;
                height: 49px;
                line-height: 49px;
                text-align: center;
                position: absolute;
                left: 51px;
                top: -102%;
                transform: translate(-50%, -50%);
                -o-transform: translate(-50%, -50%);
                -ms-transform: translate(-50%, -50%);
                -webkit-transform: translate(-50%, -50%);
                -moz-transform: translate(-50%, -50%);
                font-family: helvetica, arial, sans-serif;
                text-transform: uppercase;
                font-weight: 900;
                font-size: 18px;
                color: rgb(206, 66, 51);
                letter-spacing: 0.2em;
                background-color: aliceblue;
            }


                        .loader {
                            border: 16px solid #f3f3f3;
                            border-radius: 50%;
                            border-top: 16px solid black;
                            border-right: 16px solid black;
                            border-bottom: 16px solid black;
                            border-left: 16px solid black;
                            width: 50px;
                            height: 50px;
                            -webkit-animation: spin 2s linear infinite;
                            animation: spin 2s linear infinite;
                        }

                        @-webkit-keyframes spin {
                            0% {
                                -webkit-transform: rotate(0deg);
                            }

                            100% {
                                -webkit-transform: rotate(360deg);
                            }
                        }

                        @keyframes spin {
                            0% {
                                transform: rotate(0deg);
                            }

                            100% {
                                transform: rotate(360deg);
                            }
                        }


                        .ReqSection3 span input[type="radio"] {
                            width: auto !important;
                        }

                        .ReqSection3 span label {
                            width: 24% !important;
                            vertical-align: middle;
                            display: inline-block;
                        }



                        .tbForm tr {
                            background-color: transparent !important;
                        }

                        #mydivheader .chat-popup {
                            width: auto !important;
                        }

                        #mydivheader .popupCloseButton {
                            border: 2px solid #fff;
                            top: -11px;
                            right: -8px;
                            width: 20px;
                            height: 20px;
                        }

                        #tbpartdet {
                            box-shadow: 2px 1px 22px 3px #000;
                            width: 450px !important
                        }

                        .firstth {
                            background-color: #5490ce;
                            color: #fff;
                            font-size: 12px !important;
                            text-align: center !important;
                            padding: 10px;
                            font-weight: normal !important;
                        }

                        .firttd {
                            background-color: #1d66b0;
                            padding: 10px 15px !important;
                            color: #fff !important;
                            font-weight: normal !important;
                            width: 40% !important;
                            vertical-align: top;
                            font-size: 12px !important;
                        }

                        .secondtd {
                            background-color: #1c334a;
                            padding: 10px 15px !important;
                            color: #fff !important;
                            font-weight: normal !important;
                            font-size: 12px !important;
                            vertical-align: top;
                        }

                        .last-td {
                            background-color: #5490ce;
                            padding: 10px 15px !important;
                            color: #fff !important;
                            font-weight: normal !important;
                            font-size: 12px !important;
                            vertical-align: top;
                        }

                        .itemSearch {
                            padding: 0px;
                        }

                            .itemSearch .pagingControls {
                                border: 1px solid #333;
                                padding: 10px 5px;
                                background-color: #333;
                            }

                        #myModal {
                            height: 100% !important;
                        }

                        .modal {
                            top: 25px;
                        }

                        .modal-content {
                            box-shadow: 2px 2px 20px 1px #000;
                            border: 3px #385e26 solid;
                        }

                        .itemSearch table.dataTable td {
                            background-color: #fff !important;
                            color: #333;
                        }

                        .dataTableHeader,
                        .itemSearch table.dataTable tr th {
                            border-color: #bdbdbd;
                            background-color: #bdbdbd !important;
                            color: #333 !important;
                        }

                        .itemSearch table.dataTable tr td,
                        .itemSearch table.dataTable tr th {
                            color: #333 !important;
                        }

                        /****add screen ------***/

                        .floatna input {
                            vertical-align: middle !important;
                        }

                        .floatna input,
                        .floatna label {
                            float: none !important;
                        }



                        .GridPartdetails .dataTableHeader table.dataTable th {
                            background-color: #5e845a !important;
                            color: #ffffff;
                            border-color: #85a681 !important;
                            font-size: 9px !important;
                            font-family: Verdana !important;
                        }

                        .GridPartdetails table.dataTable td {
                            border-color: #5d8857;
                            background-color: #5d8857;
                            color: #ffffff;
                            font-size: 10px;
                            font-family: Verdana !important;
                            position: relative;
                            border-top: none !important;
                        }

                        #myModal .ReqSection2 {
                            float: left;
                            margin-right: 10px;
                            margin-left: 10px;
                            margin-top: -14px;
                        }

                            #myModal .ReqSection2 br {
                                display: none;
                            }

                            #myModal .ReqSection2 .headfield {
                            }

                            #myModal .ReqSection2 label {
                                float: left;
                                margin-right: 5px;
                                margin-top: 10px;
                            }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn5 {
                            display: none !important;
                        }

                        #myModal table#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_frm td label {
                            display: block;
                            margin-bottom: 3px;
                            text-align: left;
                            font-size: 12px;
                            color: #fff;
                        }


                        .LoaderIHSPopup {
                            background: rgba(0, 0, 0, .4);
                            cursor: pointer;
                            display: none;
                            /* height: auto; */
                            height: 100%;
                            position: absolute;
                            text-align: center;
                            top: -34px;
                            width: 95%;
                            z-index: 10000;
                        }

                        /*.cssload-loader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206,66,51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }*/

                        .modal {
                            height: auto !important;
                        }

                        .modal-content {
                            0px 0px 15px 7px #000;
                        }

                            .modal-content input {
                                padding: 12px !important;
                            }

                        .headertitlet {
                            font-size: 15px;
                        }

                        .headertitle {
                            background: #5f9553;
                            padding: 12px;
                        }

                        .allradio {
                            margin-top: 10px;
                            margin-bottom: 10px;
                            background: #80bd73;
                            padding: 10px;
                        }

                        .twobtn {
                            border-bottom: 1px #558838 solid;
                            float: left;
                            width: 100%;
                            padding-bottom: 15px;
                        }

                        .headertitle {
                            display: block;
                            float: left;
                        }

                        .model-content #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_SearchtxtPartNo {
                        }

                        .searcttype {
                            background: transparent !important;
                            margin: 0px 0 0px 0 !important;
                        }





                        table.formRows select {
                        }

                        .quickSearch .header {
                            margin-top: 19px;
                        }

                         .magicbox {
                position: absolute;
                width: 20px;
                height: 17px;
                background-repeat: no-repeat;
                background-position: center center;
            }

                        /* IHS Part Search Popup css start */




                        td a.tooltip {
                            width: auto !important;
                            background: #ecb511;
                            padding: 3px 30px;
                            border-radius: 5px;
                            color: #fff;
                            text-decoration: none !important;
                            border-bottom: 0 !important;
                            margin-left: 62px;
                        }





                        .tooltiptext td p {
                            margin: 0px !important;
                            padding-bottom: 5px !important;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_UserMsg1 {
                            font-weight: bold;
                        }

                        .quickSearch .resultsClose a {
                            color: black;
                        }

                        .firstcol {
                            float: left;
                            margin-right: 74px;
                        }

                        .nextcol {
                            float: left;
                            margin-right: 30px;
                        }



                        @media screen and (-webkit-min-device-pixel-ratio:0) {

                            /* Safari and Chrome */
                            #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_txtUserMessage {
                                margin-left: 0px;
                            }
                        }

                        .twocolcombine {
                            float: left;
                            width: 48%;
                        }

                        .width2 {
                            display: inline-block;
                        }

                            .width2 select {
                                width: 100%;
                            }


                        @media screen and (max-width: 1280px) {
                            .firstcol {
                                float: left;
                                margin-right: 74px;
                            }
                        }

                        @media screen and (min-width: 1281px) and (max-width:1600px) {
                            .firstcol {
                                float: left;
                                margin-right: 51px;
                            }
                        }


                        .quickSearch .resultsClose a {
                            color: white;
                        }

                        .quickSearch .resultsClose {
                            top: 20px;
                        }
                     

                        .bgbase {
                            float: left;
                            background-color: #5f9553;
                        }

                            .bgbase input[type="radio"] {
                                width: auto;
                            }

                        table.itemSearchFilters {
                            margin-top: 0px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn3,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn2 {
                            cursor: pointer;
                            padding: 12px 30px;
                            display: block;
                            float: left;
                            background: #44732f;
                            margin: 0px 2px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btn6 {
                            cursor: pointer;
                            display: block;
                            background: #333;
                            padding: 11px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctltblPartdetails_ctlDB_ctl12_pnlScroll {
                            max-height: 276px;
                            overflow: auto;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_btnOK_hyp {
                           color: #fff;
    padding: 25px 25px!important;
    margin: 0;
    line-height: 42px;
    
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctltblPartdetails_ctlDB_ctl12_tblHeader tr th,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ctltblPartdetails_ctlDB_ctl12_tbl tr td {
                            width: 20% !important;
                        }

                        #lblihserror {
                            display: inline-block;
                            margin-top: 5px;
                        }


                       .chkblock{width: 199px;border-left: 1px #68a960 solid;}
.chkblock span{display: block; width: 100%; float: right; text-align: right;margin: 5px;}
.bottomborder{border-bottom: 1px #68a960 solid;}
.sndcol{width: 146px;
display: inline-block;
margin-left: 19px;
text-align: right;
margin-right: 5px;}

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_LableLifeStatus {
                            width: 142px !important;
                            display: inline-block;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_chkObsolete {
                            margin-left: -2px;
                            display: inline-block;
                            margin-right: 15px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlQuoteValidityRequired_ddl,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_ddlBOM_ddl {
                            width: 180px !important;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbManufactureraut_ctl05 div.quickSearch,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbProductsaut_ctl05 div.quickSearch,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12aut_ctl05 div.quickSearch,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPackageaut_ctl05 div.quickSearch {
                            width: max-content;
                            /*margin-top: -19px;*/
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbProductsaut_ctl05 .quickSearch,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbManufactureraut_ctl05 .quickSearch,
                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbPackageaut_ctl05 .quickSearch  {
                            margin-top: -18px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbIHS12aut_ctl05 .quickSearch {
                            margin-top: -1px;
                        }

                        #ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlEdit_ctlDB_cmbSalespersionaut_ctl05 .quickSearch {
                            margin-top: -18px;
                        }
                    </style>
                     <!-- IHS Part Search Popup modal popup start -->
                    <div id="myModal" class="modal" style="height: 700px;">
                        <!-- Modal content -->
                        <div class="modal-content" style="height: 460px; 0px 0px 15px 0px #000; width: 800px;">
                            <%-- <span class="close">&times;</span>--%>
                            <div class="LoaderIHSPopup" id="divLoader">
                                <div>
                                    <div class="cssloadIHSloader">Loading..</div>
                                </div>
                            </div>
                            <asp:Label ID="btn6" Text="Cancel" runat="server" Style="cursor: pointer;"
                                CssClass="PartDetailsGridGoBtn2 close">&times;</asp:Label>
                            <div class="tophead">

                                <div class="bgbase">
                                    <span class="headertitle">Part No</span>
                                    <ReboundUI:ReboundTextBox ID="SearchtxtPartNo" runat="server" UppercaseOnly="true" placeholder="Type 3 chars to search" />
                                </div>

                                <div class="bgbase">
                                    <asp:TableCell class="ReqSection2 searcttype">
                                        <div Class="headertitle">
                                            <%=Functions.GetGlobalResource("FormFields", "SearchType")%></div>
                                        <label>
                                            <input type="radio" name="searchType" value="startswith">&nbsp;Starts with
                                        </label>
                                        <label>
                                            <input type="radio" name="searchType" value="contains"
                                                checked="checked">&nbsp;Contains
                                        </label>
                                        <label>
                                            <input type="radio" name="searchType" value="exact">&nbsp;Exact
                                        </label>
                                    </asp:TableCell>
                                </div>
                                <div class="bgbase">
                                    <asp:Label ID="btn3" Text="Go" runat="server" Style="cursor: pointer;"
                                        CssClass="PartDetailsGridGoBtn2"></asp:Label>
                                    <asp:Label ID="btn2" Text="Clear" runat="server" Style="cursor: pointer;"
                                        CssClass="PartDetailsGridGoBtn2"></asp:Label>
                                </div>

                                <div>
                                    <label id="lblihserror"
                                        style="color: #FFFFFF; font-style: normal; font-family: Tahoma !important; font-size: 12px;">
                                    </label>
                                    <label id="lblIhsServiceMessage"
                                        style="color: #FFFFFF; font-style: normal; font-family: Tahoma !important; font-size: 12px;">
                                    </label>

                                </div>


                                <ReboundItemSearch:IhsSearch ID="ctltblPartdetails" runat="server"
                                    UppercaseOnly="true" />

                                <asp:Panel ID="pnlPartDetail" runat="server">
                                </asp:Panel>

                                <div class="okbtn">
                                    
                                    <%--<a ID="btn4"  Text="Ok" runat="server" CssClass="PartDetailsGridGoBtn2"></a>--%>
                                      <ReboundUI:IconButton ID="btnOK" runat="server" IconButtonMode="HyperLink"
                                        IconCSSType="Add" CssClass="btnAddReset" IconTitleResource="OK" />
                                    <asp:Label ID="btn5" Text="Cancel" runat="server" Style="cursor: pointer;"
                                        CssClass="PartDetailsGridGoBtn2"></asp:Label>
                                </div>

                            </div>
                        </div>

                    </div>

                    <!-- IHS Part Search Popup modal popup end -->


                </asp:TableCell>

            </asp:TableRow>
        </ReboundUI_Table:Form>
        
    </Content>
</ReboundUI_Form:DesignBase>