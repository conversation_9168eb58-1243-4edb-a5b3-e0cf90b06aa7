<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
	[002]	Ravi			31-03-2023	[RP-1224] new columns, EORI number and Telephone address for tbCompanyAddress

--%>
<%@ Control Language="C#" CodeBehind="CompanyAddresses.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="Hyperlink" />
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="Hyperlink" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnCease" runat="server" IconGroup="Nugget" IconTitleResource="Delete" IconCSSType="Delete" IconButtonMode="Hyperlink" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnDefaultBill" runat="server" IconGroup="Nugget" IconTitleResource="MakeDefaultBilling" IconCSSType="Default" IconButtonMode="Hyperlink" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnDefaultShip" runat="server" IconGroup="Nugget" IconTitleResource="MakeDefaultShipping" IconCSSType="Default" IconButtonMode="Hyperlink" IsInitiallyEnabled="false" />
	</Links>
	<Content>
		<ReboundUI:FlexiDataTable ID="tblAddresses" runat="server" PanelHeight="100px" />
		<asp:Panel ID="pnlLoadingAddressInfo" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
		<asp:Panel ID="pnlAddressError" runat="server" CssClass="error invisible" />
		<asp:Panel ID="pnlAddressInfo" runat="server" CssClass="invisible">
			<div class="dataItem_Title dataItem_TitleUnderneathList"><h4><asp:Label ID="lblAddressName" runat="server" /></h4></div>
			<table class="twoCols" cellpadding="0" cellspacing="0" border="0">
				<tr>
					<td class="col1">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlAddress" runat="server" ResourceTitle="Address" />
							<ReboundUI:DataItemRow id="ctlShipVia" runat="server" ResourceTitle="ShipViaNo" />
							<ReboundUI:DataItemRow id="ctlShipViaAccount" runat="server" ResourceTitle="ShipViaAccount" />
							<ReboundUI:DataItemRow id="ctlDefaultBill" runat="server" FieldType="CheckBox" ResourceTitle="IsDefaultBill" />
							<ReboundUI:DataItemRow id="ctlDefaultShip" runat="server" FieldType="CheckBox" ResourceTitle="IsDefaultShip" />
                            <ReboundUI:DataItemRow id="ctlRegion" runat="server" ResourceTitle="Region" />
                            <ReboundUI:DataItemRow id="hidRegion" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow id="ctlDivisionHeader" runat="server" ResourceTitle="DefaultDivisionHeader" />
                            <ReboundUI:DataItemRow id="hidDivisionHeader" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlLabelType" runat="server" ResourceTitle="LabelType" />
                            <ReboundUI:DataItemRow id="hidLabelType" runat="server" FieldType="Hidden" />
						</table>
					</td>
					<td class="col2">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="AddressNotes" />
							<ReboundUI:DataItemRow id="ctlShippingNotes" runat="server" ResourceTitle="AddressShippingNotes" />
							<%--ESMS #14--%>
							<ReboundUI:DataItemRow id="ctlTaxbyAddress" runat="server" ResourceTitle="Tax" />
							<%--end--%>
							<ReboundUI:DataItemRow id="hidName" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidShipViaNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidLine1" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidLine2" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidLine3" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidTown" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidCounty" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidState" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidCountryID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidPostcode" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidTaxbyAddress" runat="server" FieldType="Hidden" />
							<%--[001] code start--%>
							<ReboundUI:DataItemRow id="ctlIncoterm" runat="server" ResourceTitle="Incoterm" />
						    <ReboundUI:DataItemRow id="hidIncotermNo" runat="server" FieldType="Hidden" />							
						    <%--[001] code end--%>							
						    <ReboundUI:DataItemRow id="ctlVatNo" runat="server" ResourceTitle="VATNo" />
							<%--[002] start--%>
							<ReboundUI:DataItemRow id="ctlCmpRegNo" runat="server" ResourceTitle="CompanyRegNo" />						
                            <ReboundUI:DataItemRow id="ctlEORINo" runat="server" ResourceTitle="EORINumber" />
                            <ReboundUI:DataItemRow id="CtlTelephoneNo" runat="server" ResourceTitle="Telephone" />
							<%--[002] end--%>
							<ReboundUI:DataItemRow id="ctlRecievingNotes" runat="server" ResourceTitle="RecievingNotes" />
                            
						</table>
					</td>
				</tr>
			</table>
		</asp:Panel>
	</Content>
	<Forms>
		<ReboundForm:CompanyAddresses_AddEdit ID="ctlAddEdit" runat="server" />
		<ReboundForm:CompanyAddresses_Confirm ID="ctlConfirm" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
