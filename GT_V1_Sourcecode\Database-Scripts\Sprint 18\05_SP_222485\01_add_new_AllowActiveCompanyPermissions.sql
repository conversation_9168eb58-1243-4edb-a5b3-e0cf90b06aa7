﻿
/*
=============================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-222485]     Phuc Hoang		 25-Dec-2024		CREATE		Company - Inactivate Supplier Ability
=============================================================================================
*/
DECLARE @NewSecurityFunction TABLE
(
	SecurityFunctionId INT
	,FunctionName NVARCHAR(300)
	,Description NVARCHAR(600)
	,SitePageNo INT
	,SiteSectionNo INT
	,ReportNo INT
	,UpdatedBy INT 
	,DLUP DATETIME 
	,InitiallyProhibitedForNewLogins BIT
	,DisplaySortOrder INT
)
INSERT INTO @NewSecurityFunction
(
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)VALUES
(
	1000314
	,'Contact_Company_Allow_Active_Inactive'
	,'Allow Active/Inactive Company'
	,NULL		    --SitePageNo
	,1				--SiteSectionNo
	,NULL			--ReportNo
	,1				--UpdatedBy
	,GETDATE()		--DLUP
	,1				--InitiallyProhibitedForNewLogins
	,4
)

INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
SELECT 
	nsf.SecurityFunctionId,
    nsf.FunctionName,
    nsf.Description,
    nsf.SitePageNo,
    nsf.SiteSectionNo,
    nsf.ReportNo,
    nsf.UpdatedBy,
    nsf.DLUP,
    nsf.InitiallyProhibitedForNewLogins,
    nsf.DisplaySortOrder
FROM @NewSecurityFunction nsf
LEFT JOIN tbSecurityFunction sf ON sf.SecurityFunctionId = nsf.SecurityFunctionId
WHERE sf.SecurityFunctionId IS NULL	--insert if not exist ID

--set default value for new permissions
DELETE tbSecurityGroupSecurityFunctionPermission
WHERE SecurityFunctionNo IN (SELECT SecurityFunctionId FROM @NewSecurityFunction);

;WITH cte AS(
	SELECT	sg.SecurityGroupId,
			sf.SecurityFunctionId,
			CASE WHEN sg.Administrator = 1 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS IsAllowed
	FROM tbSecurityGroup sg, @NewSecurityFunction sf
)
INSERT INTO tbSecurityGroupSecurityFunctionPermission 
(  
	SecurityGroupNo  
    ,SecurityFunctionNo  
    ,IsAllowed  
    ,DLUP
) 
SELECT 
	SecurityGroupId,
	SecurityFunctionId,	
	IsAllowed,
	GETDATE()
FROM cte 
