//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class ApprovedCustomersOnStop : Base {

		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "ApprovedCustomersOnStop";
			base.OnInit(e);
			AddScriptReference("Controls.HomeNuggets.ApprovedCustomersOnStop.ApprovedCustomersOnStop");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.ApprovedCustomersOnStop", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlItems", FindContentControl("pnlItems").ClientID);
			base.OnLoad(e);
		}


	}
}