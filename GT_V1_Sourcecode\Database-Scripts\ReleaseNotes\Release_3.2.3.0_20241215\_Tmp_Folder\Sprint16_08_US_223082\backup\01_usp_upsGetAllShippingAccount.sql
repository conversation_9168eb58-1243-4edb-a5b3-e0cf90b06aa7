﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210540]		Phuc Hoang			07-Aug-2024		UPDATE			[PROD Bug] DHL Interface - Label Print Issue
===========================================================================================
*/
--[dbo].[usp_upsGetAllShippingAccount]  'DHL'       
CREATE OR ALTER PROCEDURE [dbo].[usp_upsGetAllShippingAccount]  
--********************************************************************************************          
--* Implement By Vinay        
--* Implement date 28-09-2012        
--* Purpose : Get list of shipping account        
--********************************************************************************************      
@AccountType varchar(10) = 'UPS'    
AS           
 BEGIN        
            SET NOCOUNT ON;     
              
    SELECT    Active        
		 , Address1        
		 , Address2        
		 , Address3        
		 , Attention        
		 , City        
		 , CompanyName        
		 , Country        
		 , Fax        
		 , PostalCode        
		 , ShippingAccountId        
		 , [State]        
		 , TaxIDNumber        
		 , TaxIDType        
		 , Telephone        
		 , UPSAccount  
		 , LoadDefault  
		 , ISNULL(BillingCurrency,'') AS BillingCurrency    
		 , ClientNo
         , DHLDangerousGoodsIds  
		 , ISNULL(CountryName ,'') AS CountryName
		 , Email
		 , ShipType
    FROM    tbupsShippingAccount shAccount 
	where  AccountType = @AccountType   
    ORDER BY ShippingAccountId desc          
 END 
 
GO


