<%-- Marker				Changed By				Date				Remarks
	[001]				<PERSON>			28-03-2023			[RP-1326] Supplier Invoice Released GI Lines filter
		
--%>
<%@ Control Language="C#" CodeBehind="SIGILines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" ShowPaging="false"   InitialSortDirection="DESC" >
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeInvoiced" runat="server" ResourceTitle="IncludeInvoiced" />
        <ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNo"  />
        <%--<ReboundUI_FilterDataItemRow:CheckBox id="ctlUnReleasedGI" runat="server" ResourceTitle="ShowUnReleased" />--%> <%--Commented this line for [001] --%>
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlReleasedGI" runat="server" ResourceTitle="ShowReleased" /> <%--[001]--%>
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlGIDateFrom" runat="server"  ResourceTitle="GIDateFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlGIDateTo" runat="server" ResourceTitle="GIDateTo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
