Alter PROCEDURE usp_Import_BOM_By_User
    @UserID int,
    @BOMId int
AS
/*                                
 * Action: Altered   By:<PERSON><PERSON><PERSON><PERSON>   Dated:15-12-2021  Comment: Add PartWatch flag from BOM Import Utility.                                
 */
BEGIN

    IF EXISTS
    (
        SELECT (1)
        FROM BorisGlobalTraderImports.dbo.tbCusReqToBeImported
        where UserId = @UserID
    )
    BEGIN

        SET NOCOUNT ON

        declare @RowCount int = 0
        declare @OriginalFilename nvarchar(200) = null

        SELECT @RowCount = count(*),
               @OriginalFilename = BoMName
        FROM BorisGlobalTraderImports.dbo.tbCusReqToBeImported
        where UserId = @UserID
        group by BoMName

        -- ALTER TABLE tbCustomerRequirement DISABLE TRIGGER ALL  -- SG 27.10.2017 No longer required    '                              

        CREATE TABLE #TempTable
        (
            ID INT IDENTITY(1, 1),
            CustomerReqId int
        )

        DECLARE @ClientNo INT,
                @Part NVARCHAR(40),
                @ManufacturerNo INT,
                @DateCode NVARCHAR(5),
                @PackageNo INT,
                @Quantity INT,
                @Price FLOAT,
                @CurrencyNo INT,
                @ReceivedDate DATETIME,
                @Salesman INT,
                @DatePromised DATETIME,
                @Notes NVARCHAR(MAX),
                @Instructions NVARCHAR(MAX),
                @BOMName NVARCHAR(128),
                @CompanyNo INT,
                @ContactNo INT,
                @ProductNo INT,
                @CustomerPart NVARCHAR(30),
                @ROHS TINYINT,
                @PartWatch BIT,
                @CustomerRequirementNumber INT,
                @TraceabilityReq INT,
                @Manufacturer NVARCHAR(300),
                @Product NVARCHAR(300),
                @Package NVARCHAR(300),
                @CustId INT,
                @RType INT, -- SG 02.01.2018 Added ReqType field and set to 'BOM Load'                      
                @ECCNCode NVARCHAR(30),
                            --IHS information comming below                      
                @MSLName VARCHAR(20) = NULL,
                @CountryOfOriginNo int = 0,
                @LifeCycleStage NVARCHAR(100) = null,
                @HTSCode VARCHAR(20) = NULL,
                @AveragePrice FLOAT = 0,
                @Packing VARCHAR(100) = NULL,
                @PackagingSize NVARCHAR(100) = NULL,
                @Descriptions NVARCHAR(max) = NULL,
                @IHSPartsNo INT = null,
                @IHSCurrencyCode NVARCHAR(100) = NULL,
                @IHSProduct NVARCHAR(100) = NULL,
                @SequenceNo INT,
                @RowNumSequence int = 0

        --SET @RType = 14 -- RequirementDropDownData Id for 'BOM Load'                                                
        DECLARE @CustomerRequirementId INT
        -- SG 18.05.2018 - Remove any Tab, NL, CR characters from Parts                     
        UPDATE BorisGlobalTraderImports.dbo.tbCusReqToBeImported
        SET Part = LTRIM(RTRIM(REPLACE(REPLACE(REPLACE(Part, CHAR(9), ' '), CHAR(10), ' '), CHAR(13), ' ')))
        WHERE UserId = @UserID
              and (
                      Part LIKE '%' + CHAR(9) + '%'
                      OR Part LIKE '%' + CHAR(10) + '%'
                      OR Part LIKE '%' + CHAR(13) + '%'
                  )

        -- Create a temp table for send email to salesperson                                                
        SELECT tmp.ClientNo,
               tmp.Salesman,
               tmp.BOMName,
               co.CompanyId,
               cn.ContactId,
               -- , tmp.Part                                                
               lg.EMail,
               co.CompanyName
        INTO #tempCustReq
        FROM BorisGlobalTraderImports.dbo.tbCusReqToBeImported tmp
            JOIN dbo.tbCompany AS co WITH (NoLock)
                ON co.CompanyId = tmp.CompanyNo
                   AND co.ClientNo = tmp.ClientNo
                   AND NOT co.Inactive = 1
            JOIN dbo.tbContact AS cn WITH (NoLock)
                ON cn.ContactId = tmp.ContactNo
                   AND cn.CompanyNo = tmp.CompanyNo
                   AND NOT cn.Inactive = 1
            LEFT JOIN dbo.tbCurrency AS cu WITH (NoLock)
                ON cu.CurrencyDescription = tmp.Currency
                   AND cu.ClientNo = tmp.ClientNo
                   AND cu.Sell = 1
                   AND NOT cu.Inactive = 1
                   AND (
                           tmp.CurrencyNo = 0
                           OR tmp.CurrencyNo IS NULL
                       )
            LEFT JOIN dbo.tbLogin AS lg WITH (NoLock)
                ON lg.LoginId = tmp.Salesman
        WHERE UserId = @UserID
              and NOT co.CompanyId IS NULL
              AND NOT cn.ContactId IS NULL
              AND NOT tmp.Salesman IS NULL
              AND ISNULL(LEN(Part), 0) > 0
        GROUP BY tmp.ClientNo,
                 tmp.Salesman,
                 tmp.BOMName,
                 co.CompanyId,
                 cn.ContactId,
                 -- , tmp.Part                                                
                 lg.EMail,
                 co.CompanyName

        -- set up cursor                                                
        DECLARE tmpCusReqCSR CURSOR FAST_FORWARD READ_ONLY FOR
        SELECT tmp.ClientNo,
               tmp.Part,
               (
                   SELECT TOP 1
                       ManufacturerId
                   FROM tbManufacturer AS mf WITH (NoLock)
                   WHERE (
                             mf.ManufacturerName = tmp.Manufacturer
                             OR mf.FullName = tmp.Manufacturer
                             --OR mf.ManufacturerName = tmp.Manufacturer                                      
                             OR mf.ManufacturerCode = tmp.Manufacturer
                             OR mf.FullName = dbo.ufn_get_fullname(tmp.Manufacturer)
                         )
                         --OR mf.ManufacturerName = tmp.Manufacturer)                                                
                         AND NOT mf.Inactive = 1
               ) AS ManufacturerId,
               tmp.DateCode,
               --  , ( SELECT TOP 1 PackageId                                                
               --    FROM dbo.tbPackage AS pk WITH (NoLock)                                                
               --    WHERE (pk.PackageDescription = tmp.Package                                                
               --OR pk.PackageName = tmp.Package)                                                
               --    AND  NOT pk.Inactive = 1                                                
               --   ) AS PackageId         
               (
                   SELECT TOP 1
                       PackageId
                   FROM dbo.tbPackage AS pk WITH (NoLock)
                   WHERE (
                             pk.PackageDescription = ISNULL(tmp.Package, tmp.IHSPacking)
                             OR pk.PackageName = ISNULL(tmp.Package, tmp.IHSPacking)
                         )
                         AND NOT pk.Inactive = 1
               ) AS PackageId,
               ISNULL(tmp.Quantity, 0) AS Quantity,
               ISNULL(tmp.Price, 0) AS Price,
               ISNULL(tmp.CurrencyNo, cu.CurrencyId) AS CurrencyNo,
               tmp.ReceivedDate,
               tmp.Salesman,
               tmp.DatePromised,
               tmp.Notes,
               tmp.Instructions,
               tmp.BOMName,
               co.CompanyId,
               cn.ContactId,
               (
                   SELECT TOP 1
                       ProductId
                   FROM dbo.tbProduct AS pr WITH (NoLock)
                   -- SG 15.12.2017 - Changed for new product format                                                
                   WHERE (
                             pr.ProductDescription = tmp.Product
                             OR pr.ProductName = tmp.Product
                         )
                         AND NOT pr.Inactive = 1
                         --WHERE pr.ProductDescription = tmp.Product                                                
                         AND pr.ClientNo = tmp.ClientNo
               --AND NOT pr.Inactive = 1                                                
               -------------------------------------------------                                                
               ) AS ProductId,
               tmp.CustomerPart,
               ISNULL(tmp.ROHS, 0) AS ROHS,
               tmp.PartWatch,
               --, CASE WHEN ISNULL(ct.IsTraceability, 0) = 0 THEN 11 ELSE 10 END AS TraceabilityReq                    
               tmp.ReqForTraceability,
               tmp.Manufacturer,
               tmp.Product,
               tmp.Package,
               tmp.Id,
               tmp.ReqType,
               tmp.ECCNCode,
               isnull(msl.MSLLevel, 'N/A') as MSLName,
               isnull(tmp.IHSCountryOfOriginNo, 0) as IHSCountryOfOriginNo,
               tmp.IHSLifeCycleStage,
               tmp.IHSHTSCode,
               tmp.IHSAveragePrice,
               tmp.IHSPacking,
               tmp.IHSPackagingSize,
               tmp.IHSDescriptions,
               tmp.IHSPartsNo,
               tmp.IHSCurrencyCode,
               tmp.IHSProduct,
               tmp.SequenceNo
        FROM BorisGlobalTraderImports.dbo.tbCusReqToBeImported AS tmp
            JOIN dbo.tbCompany AS co WITH (NoLock)
                ON co.CompanyId = tmp.CompanyNo
                   AND co.ClientNo = tmp.ClientNo
                   AND NOT co.Inactive = 1
            JOIN dbo.tbContact AS cn WITH (NoLock)
                ON cn.ContactId = tmp.ContactNo
                   AND cn.CompanyNo = tmp.CompanyNo
                   AND NOT cn.Inactive = 1
            LEFT JOIN dbo.tbCurrency AS cu WITH (NoLock)
                ON cu.CurrencyDescription = tmp.Currency
                   AND cu.ClientNo = tmp.ClientNo
                   AND cu.Sell = 1
                   AND NOT cu.Inactive = 1
                   AND (
                           tmp.CurrencyNo = 0
                           OR tmp.CurrencyNo IS NULL
                       )
            LEFT JOIN dbo.tbCompanyType AS ct WITH (NoLock)
                ON ct.CompanyTypeId = co.TypeNo
            left join tbMSLLevel msl
                on msl.MSLLevelId = tmp.IHSMSLId
        WHERE UserId = @UserID
              and NOT co.CompanyId IS NULL
              AND NOT cn.ContactId IS NULL
              AND NOT tmp.Salesman IS NULL
              AND ISNULL(LEN(Part), 0) > 0

        OPEN tmpCusReqCSR

        FETCH NEXT FROM tmpCusReqCSR
        INTO @ClientNo,
             @Part,
             @ManufacturerNo,
             @DateCode,
             @PackageNo,
             @Quantity,
             @Price,
             @CurrencyNo,
             @ReceivedDate,
             @Salesman,
             @DatePromised,
             @Notes,
             @Instructions,
             @BOMName,
             @CompanyNo,
             @ContactNo,
             @ProductNo,
             @CustomerPart,
             @ROHS,
             @PartWatch,
             @TraceabilityReq,
             @Manufacturer,
             @Product,
             @Package,
             @CustId,
             @RType,
             @ECCNCode,
             @MSLName,
             @CountryOfOriginNo,
             @LifeCycleStage,
             @HTSCode,
             @AveragePrice,
             @Packing,
             @PackagingSize,
             @Descriptions,
             @IHSPartsNo,
             @IHSCurrencyCode,
             @IHSProduct,
             @SequenceNo
        DECLARE @Counter INT = 0 -- SG 27.10.2017 Moved from inside the cursor loop                                                

        WHILE @@FETCH_STATUS = 0
        BEGIN
            --SET @PartWatch = 0 -- SG 05.02.2019 - Force PartWatch flag                                                
            Set @RowNumSequence = @RowNumSequence + 1
            EXEC usp_select_CustomerRequirement_NextNumber @ClientNo,
                                                           100001,
                                                           @CustomerRequirementNumber OUTPUT

            SET @Counter = 0 -- SG 27.10.2017 Moved DECLARE outside the cursor loop, so need to reset value to zero                                                

            WHILE EXISTS
    (
        SELECT (1)
        FROM tbCustomerRequirement
        WHERE CustomerRequirementNumber = @CustomerRequirementNumber
              AND ClientNo = @ClientNo
    )
                  AND @Counter <= 4
            BEGIN
                EXEC usp_select_CustomerRequirement_NextNumber @ClientNo,
                                                               100001,
                                                               @CustomerRequirementNumber OUTPUT
                SET @Counter = @Counter + 1
            END

            INSERT INTO dbo.tbCustomerRequirement
            (
                CustomerRequirementNumber,
                ClientNo,
                FullPart,
                Part,
                ManufacturerNo,
                DateCode,
                PackageNo,
                Quantity,
                Price,
                CurrencyNo,
                ReceivedDate,
                Salesman,
                DatePromised,
                Notes,
                Instructions,
                Shortage,
                CompanyNo,
                ContactNo,
                Alternate,
                ProductNo,
                CustomerPart,
                Closed,
                ROHS,
                FullCustomerPart,
                BOM,
                BOMName,
                PartWatch,
                ReqForTraceability,
                ReqType, -- SG 02.01.2018 Added ReqType field and set to 'BOM Load'                                                
                bomNo,
                ECCNCode,
                reqstatus,
                MSL,
                CountryOfOriginNo,
                LifeCycleStage,
                HTSCode,
                AveragePrice,
                Packing,
                PackagingSize,
                Descriptions,
                IHSPartsNo,
                IHSCurrencyCode,
                IHSProduct,
                [Sequence]
            )
            VALUES
            (   @CustomerRequirementNumber,
                @ClientNo,
                dbo.ufn_get_fullpart(@Part),
                @Part,
                @ManufacturerNo,
                @DateCode,
                @PackageNo,
                @Quantity,
                @Price,
                @CurrencyNo,
                @ReceivedDate,
                @Salesman,
                @DatePromised,
                        --, @Notes                                                
                RTRIM(LTRIM(ISNULL(@Notes, '') + ISNULL(   (CASE
                                                                WHEN @ManufacturerNo IS NULL
                                                                     AND ISNULL(@Manufacturer, '') <> ' ' THEN
                                                                    ' | Mfr: ' + RTRIM(@Manufacturer)
                                                            END
                                                           ),
                                                           ''
                                                       ) + ISNULL(   (CASE
                                                                          WHEN @ProductNo IS NULL
                                                                               AND ISNULL(@Product, '') <> '' THEN
                                                                              ' | Prod: ' + RTRIM(@Product)
                                                                      END
                                                                     ),
                                                                     ''
                                                                 )
                            + ISNULL(   (CASE
                                             WHEN @PackageNo IS NULL
                                                  AND ISNULL(@Package, '') <> '' THEN
                                                 ' | Pack: ' + RTRIM(@Package)
                                         END
                                        ),
                                        ''
                                    )
                           )
                     ),
                @Instructions,
                0,
                @CompanyNo,
                @ContactNo,
                0,
                @ProductNo,
                @CustomerPart,
                0,
                @ROHS,
                dbo.ufn_get_fullpart(@CustomerPart),
                1,
                @BOMName,
                @PartWatch,
                @TraceabilityReq,
                @RType, -- SG 02.01.2018 Added ReqType field and set to 'BOM Load'                                                
                @BOMId,
                @ECCNCode,
                1,
                @MSLName,
                @CountryOfOriginNo,
                @LifeCycleStage,
                @HTSCode,
                @AveragePrice,
                @Packing,
                @PackagingSize,
                @Descriptions,
                @IHSPartsNo,
                @IHSCurrencyCode,
                @IHSProduct,
                @SequenceNo
            )
            SET @CustomerRequirementId = @@IDENTITY;



            -----Start Code for PartWatch Match--                                
            --EXEC usp_Add_PartWatchMatch_For_BomImport @CustomerRequirementId,@UserID                                
            INSERT INTO #TempTable
            (
                CustomerReqId
            )
            VALUES (@CustomerRequirementId)
            -----End Code for PartWatch Match--                                 

            -- Espire 05 Oct 17: Delete the record                                                
            DELETE FROM BorisGlobalTraderImports.dbo.tbCusReqToBeImported
            WHERE Id = @CustId
            -- PRINT @CustId                                                

            FETCH NEXT FROM tmpCusReqCSR
            INTO @ClientNo,
                 @Part,
                 @ManufacturerNo,
                 @DateCode,
                 @PackageNo,
                 @Quantity,
                 @Price,
                 @CurrencyNo,
                 @ReceivedDate,
                 @Salesman,
                 @DatePromised,
                 @Notes,
                 @Instructions,
                 @BOMName,
                 @CompanyNo,
                 @ContactNo,
                 @ProductNo,
                 @CustomerPart,
                 @ROHS,
                 @PartWatch,
                 @TraceabilityReq,
                 @Manufacturer,
                 @Product,
                 @Package,
                 @CustId,
                 @RType,
                 @ECCNCode,
                 @MSLName,
                 @CountryOfOriginNo,
                 @LifeCycleStage,
                 @HTSCode,
                 @AveragePrice,
                 @Packing,
                 @PackagingSize,
                 @Descriptions,
                 @IHSPartsNo,
                 @IHSCurrencyCode,
                 @IHSProduct,
                 @SequenceNo
        END

        CLOSE tmpCusReqCSR
        DEALLOCATE tmpCusReqCSR
        DECLARE @Cout int = 1
        DECLARE @Max int = 0
        DECLARE @Id int = 0
        SET @Max =
        (
            Select count(1) from #TempTable
        )
        WHILE (@Cout <= @Max)
        BEGIN
            SET @Id =
            (
                SELECT CustomerReqId from #TempTable WHERE ID = @Cout
            )
            EXEC usp_Add_PartWatchMatch_For_BomImport @Id, @UserID
            SET @Cout = @Cout + 1
        END

        -- ALTER TABLE tbCustomerRequirement ENABLE TRIGGER ALL  -- SG 27.10.2017 No longer required                                                

        -- TRUNCATE TABLE BorisGlobalTraderImports.dbo.tbCusReqToBeImported                                                

        DECLARE @SalesEmail NVARCHAR(50)
        DECLARE @EmailBody NVARCHAR(200)
        DECLARE @EmailAddress NVARCHAR(50)
        DECLARE @EmailSubject NVARCHAR(200)
        DECLARE @CustomerName VARCHAR(200)

        -- Create a cursor to send an email to sales person                                                
        DECLARE tmpCusReqEmail CURSOR FAST_FORWARD READ_ONLY FOR
        SELECT ClientNo,
               Salesman,
               BOMName,
               CompanyId,
               ContactId,
               --, Part                           
               EMail,
               CompanyName
        FROM #tempCustReq

        OPEN tmpCusReqEmail

        FETCH NEXT FROM tmpCusReqEmail
        INTO @ClientNo,
             @Salesman,
             @BOMName,
             @CompanyNo,
             @ContactNo,
             @SalesEmail,
             @CustomerName

        WHILE @@FETCH_STATUS = 0
        BEGIN

            -- SET @EmailBody = 'Company review date <br/><br/> Customer Name: ' +  @CustomerName +'<br/>'                                                
            --                      + 'Review Date: '+@ReviewDate+'<br/>'                                                
            --                      + 'Review Status: '+@ReviewStatus+'<br/>'                                                
            --                      + 'Customer Code: '+@CustomerCode+'<br/>'                                                
            --                      + 'http://**********/Boris/GlobalTrader/Con_CompanyDetail.aspx?cm='+CAST(@CompanyId as varchar(10))+'&clt=0'                                 
            SET @EmailSubject = 'Customer requirement BOM ' + @BOMName + ' has been imported into GT'
            SET @EmailBody = 'BOM ' + @BOMName + ' has been imported into GT' + '<br/>' + 'Customer: ' + @CustomerName
            SET @EmailAddress = @SalesEmail

            --EXEC [msdb].dbo.sp_send_dbmail                                                
            --  @profile_name = 'GTAdmin', -- 'GTServer'                                                
            --  @recipients = @EmailAddress,                                                
            --  @subject = @EmailSubject,                                                
            --  @body = @EmailBody,                                                
            --  @from_address = '<EMAIL>',                                              
            --  @reply_to = '<EMAIL>',                                                
            --  @body_format = 'HTML'                                                

            INSERT INTO dbo.tbCommunicationLog
            (
                SystemDocumentNo,
                ContactNo,
                CompanyNo,
                Notes,
                UpdatedBy
            )
            VALUES
            (   1, -- Cus Req                                 
                @ContactNo,
                @CompanyNo,
                'Customer Requirements BOM ' + @BOMName + ' loaded',
                0
            )

            FETCH NEXT FROM tmpCusReqEmail
            INTO @ClientNo,
                 @Salesman,
                 @BOMName,
                 @CompanyNo,
                 @ContactNo,
                 @SalesEmail,
                 @CustomerName
        END

        CLOSE tmpCusReqEmail


        insert into BorisGlobalTraderImports.dbo.tbUtilityLog
        (
            FileName,
            UtilityType,
            Clientid,
            LoginNo,
            DLUP,
            iRowCount
        )
        values
        (@OriginalFilename, 1, @ClientNo, @UserID, getdate(), @RowCount)
        --------------------------Import History End--------------------------------            

        DEALLOCATE tmpCusReqEmail

        DROP TABLE #tempCustReq
        SET NOCOUNT OFF
    END
END
