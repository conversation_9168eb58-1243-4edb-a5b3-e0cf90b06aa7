SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('usp_insert_Offer_APIBOMManager','P') IS NOT NULL
    DROP PROC [dbo].[usp_insert_Offer_APIBOMManager]
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201577]		An.TranTan			15-May-2024		Update			Refactor the insert command and add more fields to EMSOffers, set tbOffers as main Offer source
[US-201577]		An.TranTan			21-Jun-2024		Update			Insert MSL text into table Offer
===========================================================================================
*/
CREATE Procedure [dbo].[usp_insert_Offer_APIBOMManager]
    @Part NVARCHAR(30) = NULL,
    @ManufacturerNo INT = NULL,
    @ManufacturerName NVARCHAR(128) = NULL,
    @DateCode NVARCHAR(5) = NULL,
    @ProductNo INT = NULL,
    @ProductName NVARCHAR(128) = NULL,
    @PackageNo INT = NULL,
    @PackageName NVARCHAR(128) = NULL,
    @Quantity INT = NULL,
    @Price FLOAT = NULL,
    @CurrencyNo INT = NULL,
    @OriginalEntryDate DATETIME = NULL,
    --@Salesman          INT = NULL,                     
    @SupplierNo INT = NULL,
    @SupplierName NVARCHAR(128) = NULL,
    @ROHS TINYINT = NULL,
    @OfferStatusNo INT = NULL,
    @Notes NVARCHAR(128) = NULL,
    --@UpdatedBy         INT = NULL,                     
    --@ClientNo          INT = NULL,                     
    @SupplierTotalQSA NVARCHAR(50) = NULL,
    @SupplierLTB NVARCHAR(50) = NULL,
    @SupplierMOQ NVARCHAR(50) = NULL,
    --@MSL NVARCHAR(50) = NULL,
    @SPQ NVARCHAR(50) = NULL,
    @LeadTime NVARCHAR(50) = NULL,
    @FactorySealed NVARCHAR(50) = NULL,
    @ROHSStatus NVARCHAR(50) = NULL,
    --@IsPoHUB           BIT=0,                     
    @MSLNo INT = NULL,
    @BOMManagerID INT = NULL,
    @ClientID INT = NULL,
    @AlterCRNumber INT = NULL,
	--Begin more fields from US-201577
	@SupplierWarranty INT = NULL,
	@CountryOfOriginNo INT = NULL,
	@ShippingCost FLOAT = NULL,
	@SellPrice FLOAT = NULL,
	@RegionNo INT = NULL,
	@DeliveryDate DATETIME = NULL,
	@TestingRecommended BIT = NULL,
	@SellPriceLessReason NVARCHAR(MAX) = NULL,
	--End more fields from US-201577
    @APIOfferId INT output
AS
BEGIN
	Declare @AlternateStatus BIT = 0;
	SET @AlterCRNumber =  ISNULL(@AlterCRNumber, 0);
	IF (@AlterCRNumber <> 0)
	BEGIN
		SET @AlternateStatus = 1
	END

	DECLARE @SupplierType NVARCHAR(250) = '';
	SET @SupplierType = (SELECT TOP 1 ct.[Name]
							FROM tbCompanytype ct
							JOIN tbCompany c ON c.TypeNo = ct.CompanyTypeId
							WHERE c.CompanyId = @SupplierNo);

	DECLARE @MSL NVARCHAR(50) = NULL;
	SELECT @MSL = MSLLevel FROM tbMSLLevel m WHERE m.MSLLevelId = @MSLNo;
	INSERT INTO dbo.tbEMSOffers
        (
            BOMManagerNo,
            OfferID,
            fullpart,
            part,
            manufacturerno,
            ManufacturerName,
            datecode,
            productno,
            packageno,
            quantity,
            price,
            currencyno,
            CurrencyCode,
            originalentrydate,
            supplierno,
            suppliername,
            SupplierType,
            rohs,
            notes,
            dlup,
            offerstatusno,
            clientno,
            ClientId,
            ClientName,
            ClientCode,
            suppliertotalqsa,
            supplierltb,
            suppliermoq,
            msl,
            spq,
            leadtime,
            factorysealed,
            rohsstatus,
            msllevelno,
            CustomerRequirementId,
            SupplierMessage,
            Alternatestatus,
            alternateCustomerRequirementNumber,
            POHubCompanyNo,
			SupplierWarranty,
			CountryOfOriginNo,
			SellPrice,
			SellPriceLessReason,
			ShippingCost,
			RegionNo,
			DeliveryDate,
			TestingRecommended
        )
        select @BOMManagerID,
               1,
               dbo.Ufn_get_fullpart(@Part),
               @Part,
               @ManufacturerNo,
               @ManufacturerName,
               @DateCode,
               @ProductNo,
               @PackageNo,
               @Quantity,
               @Price,
               @CurrencyNo,
               cur.CurrencyCode,
               @OriginalEntryDate,
               @SupplierNo,
               @SupplierName,
               @SupplierType,
               @ROHS,
               @Notes,
               CURRENT_TIMESTAMP,
               @OfferStatusNo,
               a.clientno,   -- @ClientID,                
               a.clientno,   --@ClientID,                
               b.clientName, --(select ClientName from tbClient where ClientId = @ClientID),                
               b.clientcode, --(select ClientCode from tbClient where ClientId = @ClientID),                
               @SupplierTotalQSA,
               @SupplierLTB,
               @SupplierMOQ,
               @MSL,
               @SPQ,
               @LeadTime,
               @FactorySealed,
               @ROHSStatus,
               @MSLNo,
               a.CustomerRequirementId,
               '',
               @AlternateStatus,
               @AlterCRNumber,
               @SupplierNo,
			   @SupplierWarranty,
			   @CountryOfOriginNo,
			   @SellPrice,
			   @SellPriceLessReason,
			   @ShippingCost,
			   @RegionNo,
			   @DeliveryDate,
			   @TestingRecommended
        from tbcustomerrequirement a
            join tbClient b on b.ClientId = a.clientno
			join tbCurrency cur on cur.CurrencyId = @CurrencyNo
			 
        where bommanagerno = @BOMManagerID
			AND (
					(@AlterCRNumber <> 0 AND a.CustomerRequirementId = @AlterCRNumber)
					OR
					(@AlterCRNumber = 0 AND a.Part = @part)
				)
	--SET @APIOfferId = Scope_identity();

    /*code to insert same offer in BorisGlobalTraderImports.dbo.tbOffer  */
	DECLARE @InsertedOffer TABLE (ID int);
    INSERT INTO BorisGlobalTraderImports.dbo.tbOffer
    (
        SupplierNo,
        Part,
        FullPart,
        DateCode,
        Quantity,
        Price,
        OriginalEntryDate,
        ManufacturerNo,
        CurrencyNo,
        ProductNo,
        PackageNo,
        Notes,
        ManufacturerName,
        ProductName,
        PackageName,
        ClientNo,
        ActionType,
        OfferStatusNo,
        SupplierTotalQSA,
        SupplierLTB,
        SupplierMOQ,
        LeadTime,
        ROHSStatus,
        FactorySealed,
		MSL,
        MSLLevelNo,
        SPQ,
        ROHS,
        RefId,
        IsBomManager,
		SupplierWarranty,
		CountryOfOriginNo,
		SellPrice,
		SellPriceLessReason,
		ShippingCost,
		RegionNo,
		DeliveryDate,
		TestingRecommended,
		AlternateStatus
    )
	OUTPUT inserted.OfferId INTO @InsertedOffer(ID)
    SELECT 
		   @SupplierNo,
           @Part,
           dbo.Ufn_get_fullpart(@Part),
           @DateCode,
           @Quantity,
           @Price,
           @OriginalEntryDate,
           @ManufacturerNo,
           @CurrencyNo,
           @ProductNo,
           @PackageNo,
           @Notes,
           @ManufacturerName,
           null,	
           @PackageName,
           @ClientID,
           null,	--action type
           @OfferStatusNo,
           @SupplierTotalQSA,
           @SupplierLTB,
           @SupplierMOQ,
           @LeadTime,
           @ROHSStatus,
           @FactorySealed,
		   @MSL,
           @MSLNo,
           @SPQ,
           @ROHS,
           null,	--RefId
           1,		--IsBomManager
		   @SupplierWarranty,
		   @CountryOfOriginNo,
		   @SellPrice,
		   @SellPriceLessReason,
		   @ShippingCost,
		   @RegionNo,
		   @DeliveryDate,
		   @TestingRecommended,
		   @AlternateStatus
	SET @APIOfferId = (SELECT TOP 1 ID FROM @InsertedOffer);
END
GO


