using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {

	/// <summary>
	/// Common functionality for left-side nuggets
	/// </summary>
	public class Base : UserControl, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		public DesignBase ctlDesignBase;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		private int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;
		protected QueryStringManager _objQSManager = new QueryStringManager(HttpContext.Current.Request.QueryString);

		#endregion

		#region Properties

		protected LeftNugget _objLeftNuggetType;

		/// <summary>
		/// Title Resource (from LeftNuggets.resx)
		/// </summary>
		private string _strTitleResource = "";
		public string TitleResource {
			get { return _strTitleResource; }
			set { _strTitleResource = value; }
		}

		/// <summary>
		/// Is this initially expanded? Passed through to the design base.
		/// </summary>
		private Boolean _blnIsInitiallyExpanded = true;
		public Boolean IsInitiallyExpanded {
			get { return _blnIsInitiallyExpanded; }
			set { _blnIsInitiallyExpanded = value; }
		}

		/// <summary>
		/// Is this the first in the left-side? Passed through to the design base.
		/// </summary>
		private Boolean _blnIsFirstInLeftside = false; //the first one has to have a slightly different background
		public Boolean IsFirstInLeftside {
			get { return _blnIsFirstInLeftside; }
			set { _blnIsFirstInLeftside = value; }
		}

		/// <summary>
		/// The type of the Icon corresponding to a CSS class
		/// </summary>
		private string _strIconCssType = "";
		public string IconCssType {
			get { return _strIconCssType; }
			set { _strIconCssType = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("LeftNuggets.css");
			AddScriptReference("Controls.LeftNuggets._Bases.Base");
			ctlDesignBase = (LeftNuggets.DesignBase)Functions.FindControlRecursive(this, "ctlDB");
			if (ctlDesignBase == null) throw new Exception("A left nugget must have a Design Base control with ID 'ctlDB' - have you checked Web.config?");
			ctlDesignBase.IsInitiallyExpanded = this.IsInitiallyExpanded;
			ctlDesignBase.IsFirstInLeftside = this.IsFirstInLeftside;
			ctlDesignBase.TitleResource = TitleResource;
			ctlDesignBase.LeftNuggetType = _objLeftNuggetType;
			ctlDesignBase.IconCssType = _strIconCssType;
			base.OnInit(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region Methods

		protected void SetLeftNuggetType(string strName) {
			_objLeftNuggetType = _objSite.GetLeftNugget(strName);
		}

		/// <summary>
		/// Add AJAX script reference
		/// </summary>
		/// <param name="sr"></param>
		protected void AddScriptReference(bool blnDebug, string strAssembly, string strRef) {
			ScriptReference sr = Functions.GetScriptReference(blnDebug, strAssembly, strRef, true);
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
		}
		protected void AddScriptReference(string strRef) {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			AddScriptReference(blnDebug, "Rebound.GlobalTrader.Site", strRef);
		}

		public object FindContentControl(string strControlName) {
			return (ctlDesignBase.FindContentControl(strControlName));
		}

		public void MakeChildControls() {
			EnsureChildControls();
			ctlDesignBase.MakeChildControls();
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			if (_scScriptControlDescriptor == null) _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("blnIsExpanded", _blnIsInitiallyExpanded);
			_scScriptControlDescriptor.AddElementProperty("pnlOuter", ctlDesignBase.pnlOuter.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ancShowHideClick", ctlDesignBase.ancShowHideClick.ClientID);
			return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		System.Collections.Generic.IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }
		System.Collections.Generic.IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		#endregion

	}
}