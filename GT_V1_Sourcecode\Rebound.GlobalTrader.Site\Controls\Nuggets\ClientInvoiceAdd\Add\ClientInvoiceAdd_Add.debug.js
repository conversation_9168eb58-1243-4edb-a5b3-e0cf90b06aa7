///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//Marker     Changed by      Date               Remarks
//[001]      Vinay           06/06/2013         CR:- Supplier Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.initializeBase(this, [element]);
    this._intNewID = 0;
    this._intCompanyID = 0;
    this._strCompanyName = "";
    this._aryGoodsInLineIds = [];
    this._lnsSeperator = "/";
    this._SupplierCode = "";
    this._intGoodsInID = 0;
    this._CurrencyCode = "";
    this._TaxRate = 0;
    this._isClientInvoice = false;
    this._intInvoiceClientNo = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.prototype = {

    get_ctlSelectCompany: function() { return this._ctlSelectCompany; }, set_ctlSelectCompany: function(v) { if (this._ctlSelectCompany !== v) this._ctlSelectCompany = v; },
    get_ibtnSend: function() { return this._ibtnSend; }, set_ibtnSend: function(v) { if (this._ibtnSend !== v) this._ibtnSend = v; },
    get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    get_ibtnContinue: function() { return this._ibtnContinue; }, set_ibtnContinue: function(v) { if (this._ibtnContinue !== v) this._ibtnContinue = v; },
    get_ibtnContinue_Footer: function() { return this._ibtnContinue_Footer; }, set_ibtnContinue_Footer: function(v) { if (this._ibtnContinue_Footer !== v) this._ibtnContinue_Footer = v; },
    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_intGoodsInID: function() { return this._intGoodsInID; }, set_intGoodsInID: function(v) { if (this._intGoodsInID !== v) this._intGoodsInID = v; },
    get_strCompanyName: function() { return this._strCompanyName; }, set_strCompanyName: function(v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    get_ctlSelectSIGILines: function() { return this._ctlSelectSIGILines; }, set_ctlSelectSIGILines: function(v) { if (this._ctlSelectSIGILines !== v) this._ctlSelectSIGILines = v; },
    get_dtFromDate: function() { return this._dtFromDate; }, set_dtFromDate: function(v) { if (this._dtFromDate !== v) this._dtFromDate = v; },
    get_dtToDate: function() { return this._dtToDate; }, set_dtToDate: function(v) { if (this._dtToDate !== v) this._dtToDate = v; },
    get_lblCurrency_InvoiceAmount: function() { return this._lblCurrency_InvoiceAmount; }, set_lblCurrency_InvoiceAmount: function(value) { if (this._lblCurrency_InvoiceAmount !== value) this._lblCurrency_InvoiceAmount = value; },
    get_lblCurrency_GoodsInValue: function() { return this._lblCurrency_GoodsInValue; }, set_lblCurrency_GoodsInValue: function(value) { if (this._lblCurrency_GoodsInValue !== value) this._lblCurrency_GoodsInValue = value; },
    get_lblCurrency_Tax: function() { return this._lblCurrency_Tax; }, set_lblCurrency_Tax: function(value) { if (this._lblCurrency_Tax !== value) this._lblCurrency_Tax = value; },
    get_lblCurrency_DeliveryCharge: function() { return this._lblCurrency_DeliveryCharge; }, set_lblCurrency_DeliveryCharge: function(value) { if (this._lblCurrency_DeliveryCharge !== value) this._lblCurrency_DeliveryCharge = value; },
    get_lblCurrency_BankFee: function() { return this._lblCurrency_BankFee; }, set_lblCurrency_BankFee: function(value) { if (this._lblCurrency_BankFee !== value) this._lblCurrency_BankFee = value; },
    get_lblCurrency_CreditCardFee: function() { return this._lblCurrency_CreditCardFee; }, set_lblCurrency_CreditCardFee: function(value) { if (this._lblCurrency_CreditCardFee !== value) this._lblCurrency_CreditCardFee = value; },
    get_SelectedGI: function() { return this._SelectedGI; }, set_SelectedGI: function(value) { if (this._SelectedGI !== value) this._SelectedGI = value; },

    initialize: function () {
    
        Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addCancel(Function.createDelegate(this, this.cancelClicked));
        this.addSave(Function.createDelegate(this, this.saveHeaderThenLine));
        var fnContinue = Function.createDelegate(this, this.finishedForm);
        $R_IBTN.addClick(this._ibtnContinue, fnContinue);
        $R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);
        var fnSend = Function.createDelegate(this, this.sendMail);
        $R_IBTN.addClick(this._ibtnSend, fnSend);
        $R_IBTN.addClick(this._ibtnSend_Footer, fnSend);
        this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
        this._ctlMail._ctlRelatedForm = this;
        this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));
        this._ctlSelectCompany.addItemSelected(Function.createDelegate(this, this.selectCompany));
        this.addFieldCheckBoxClickEvent("ctlSendMail", Function.createDelegate(this, this.chooseIfSendMail));
        this._ctlSelectSIGILines.addPotentialStatusChange(Function.createDelegate(this, this.ctlSelectSIGILines_PotentialStatusChange));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
        if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlMail) this._ctlMail.dispose();
        if (this._ctlSelectCompany) this._ctlSelectCompany.dispose();
        if (this._ctlSelectSIGILines) this._ctlSelectSIGILines.dispose();
        this._ctlMail = null;
        this._ctlSelectCompany = null;
        this._ctlSelectSIGILines = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._ibtnContinue = null;
        this._ibtnContinue_Footer = null;
        this._intCompanyID = null;
        this._strCompanyName = null;
        this._intNewID = null;
        this._dtFromDate = null;
        this._dtToDate = null;
        this._GoodsInLineIds = null;
        this._lnsSeperator = null;
        this._intGoodsInID = null;
        this._SupplierCode = null;
        this._lblCurrency_InvoiceAmount = null;
        this._lblCurrency_GoodsInValue = null;
        this._lblCurrency_Tax = null;
        this._lblCurrency_DeliveryCharge = null;
        this._lblCurrency_BankFee = null;
        this._lblCurrency_CreditCardFee = null;
        this._SelectedGI = null;
        this._CurrencyCode = null;
        this._TaxRate = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.callBaseMethod(this, "dispose");
    },

    formShown: function () {
           if (this._blnFirstTimeShown) {
            $find(this.getField("ctlCurrency").ControlID).addChanged(Function.createDelegate(this, this.updateCurrency));
            $find(this.getField("ctlddlTax").ControlID).addChanged(Function.createDelegate(this, this.updateTaxRate));
            $find(this.getField("ctlClient").ControlID).addChanged(Function.createDelegate(this, this.SelectClient));
           }
          // this._ctlSelectSIGILines.showField("ctlIncludeInvoiced", false);
           this.gotoStep(2);
          
        //if (this._intCompanyID > 0) {
        //    this.gotoStep(2);
        //} else if (this._strCompanyName) {
        //    this.gotoStep(1);
        //    this._ctlSelectCompany.setFieldValue("ctlCompanyName", this._strCompanyName);
         
        //    this._ctlSelectCompany.searchClicked();
        //} else {
        //    this.gotoStep(1);
        //}
    },

    cancelClicked: function() {
        $R_FN.navigateBack();
    },


    stepChanged: function () {
       // alert("hi");
        var intStep = 2;// this._ctlMultiStep._intCurrentStep;
        $R_IBTN.enableButton(this._ibtnSave, intStep == 2);
        $R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 2);
        $R_IBTN.showButton(this._ibtnSave, intStep < 3);
        $R_IBTN.showButton(this._ibtnSave_Footer, intStep < 3);
        $R_IBTN.showButton(this._ibtnCancel, intStep < 3);
        $R_IBTN.showButton(this._ibtnCancel_Footer, intStep < 3);
        $R_IBTN.showButton(this._ibtnSend, intStep == 3);
        $R_IBTN.showButton(this._ibtnSend_Footer, intStep == 3);
        $R_IBTN.showButton(this._ibtnContinue, intStep == 3);
        $R_IBTN.showButton(this._ibtnContinue_Footer, intStep == 3);
        this._ctlMultiStep.showSteps(intStep != 3);
        if (intStep == 1) this._ctlSelectCompany.resizeColumns();
        if (intStep == 2) {
            this.getFieldDropDownData("ctlClient");
            //this.setFieldValue("ctlSupplier", $R_FN.setCleanTextValue(this._strCompanyName));
            this.getFieldDropDownData("ctlCurrency");
            this.getFieldDropDownData("ctlddlTax");
            if (this._intGoodsInID > 0) {
                this._ctlSelectSIGILines._intGoodsInID = this._intGoodsInID;
            } 
            this._ctlSelectSIGILines._CompanyNo = this._intCompanyID;
            //   this.getCompanyDefaults();
            this.showField("ctlSupplier", false);
          //  this.showField("ctlSupplierCode", false);
         //   this.showField("ctlCurrency", false);
            this.showField("ctlSupplierInvoice", false);
            //this.SelectClient();
         //   this._ctlSelectSIGILines.showField("ctlIncludeInvoiced", false);
            this._ctlSelectSIGILines.setFieldValue("ctlGIDateFrom", this._dtFromDate);
            this._ctlSelectSIGILines.setFieldValue("ctlGIDateTo", this._dtToDate);
            this._ctlSelectSIGILines.searchClicked();

        }
        if (intStep == 3) {
            this.getMessageText();
            this.setFieldValue("ctlSendMail", false);
            this.showMailButtons();
        }
    },
    SelectClient: function () {
       // alert("hi");
      //  alert(this.getFieldValue("ctlClient"));
        this._ctlSelectSIGILines._isClientInvoice = true;
        this._ctlSelectSIGILines._intInvoiceClientNo = this.getFieldValue("ctlClient");// 101;// this._intInvoiceClientNo;
        this._ctlSelectSIGILines.searchClicked();
    },
    selectCompany: function() {
        this._intCompanyID = this._ctlSelectCompany.getSelectedID();
        this._strCompanyName = this._ctlSelectCompany._tblResults.getSelectedCellValue(0);
        this.nextStep();
    },
    getCompanyDefaults: function() {
        this.showCompanyUpdateFieldsLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyPurchasingInfo");
        obj.set_DataObject("CompanyPurchasingInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("ID", this._intCompanyID);
        
        obj.addDataOK(Function.createDelegate(this, this.getCompanyOK));
        obj.addError(Function.createDelegate(this, this.getCompanyError));
        obj.addTimeout(Function.createDelegate(this, this.getCompanyError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCompanyOK: function(args) {
        var res = args._result;
        this._SupplierCode = res.SupplierCode;
    //    this.setFieldValue("ctlSupplierCode", $R_FN.setCleanTextValue(this._SupplierCode));
        this.setFieldValue("ctlCurrency", res.GlobalCurrencyNo);
        this._CurrencyCode = res.POCurrencyCode;
        this.selectDefaultCurrency();
        if (this._intGoodsInID > 0) {
            this.getDefaultGoodsIn();
        }
        this.showCompanyUpdateFieldsLoading(false);

    },

    showCompanyUpdateFieldsLoading: function(bln) {
        this.showFieldLoading("ctlCurrency", bln);
    },
    getCompanyError: function(args) {
        this.showCompanyUpdateFieldsLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    getDefaultGoodsIn: function() {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GIMainInfo");
        obj.set_DataObject("GIMainInfo");
        obj.set_DataAction("GetForPage");
        obj.addParameter("id", this._intGoodsInID);
        obj.addDataOK(Function.createDelegate(this, this.getDefaultGoodsInOK));
        obj.addError(Function.createDelegate(this, this.getDefaultGoodsInError));
        obj.addTimeout(Function.createDelegate(this, this.getDefaultGoodsInError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDefaultGoodsInOK: function(args) {
        var res = args._result;
        if (res.GlobalCurrencyNo > 0) {
            this.setFieldValue("ctlCurrency", res.GlobalCurrencyNo);
            this._CurrencyCode = res.CurrencyCode;
            this.selectDefaultCurrency();
        }
        if (res.TaxNo > 0) {
            this.setFieldValue("ctlddlTax", res.TaxNo);
            this.getTaxRate(res.TaxNo);
        }

    },
    getDefaultGoodsInError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    ctlSelectSIGILines_PotentialStatusChange: function() {
        this.setFieldValue("ctlSecondRef", $R_FN.arrayToSingleString(this._ctlSelectSIGILines._aryPONumber, this._lnsSeperator));
        this.setFieldValue("ctlNarrative", $R_FN.arrayToSingleString(this._ctlSelectSIGILines._aryGINumber, this._lnsSeperator));
        document.getElementById(this._SelectedGI).value = $R_FN.formatCurrency(this._ctlSelectSIGILines._floatTotalSelectedValue, null, 2, false);
        this._aryGoodsInLineIds = this._ctlSelectSIGILines._aryGoodsInLineIds;
    },

    // clientNo, PurchaseHubClientNo, StartDate, EndDate, amount, goodsValue,
    // tax, taxNo, taxCode,  deliveryCharge, bankFee, creditCardFee, notes, secondRef, narrative, canBeExported, updatedBy

    saveHeaderThenLine: function() {
        if (!this.validateForm()) return;
       // if (!this.validateTaxRate()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientInvoiceAdd");
        obj.set_DataObject("ClientInvoiceAdd");
        obj.set_DataAction("AddNew");
        obj.addParameter("ClientID", this.getFieldValue("ctlClient"));
      
        obj.addParameter("Amount", this.getFieldValue("ctlInvoiceAmount"));
        obj.addParameter("GoodsValue", this.getFieldValue("ctlGoodsValue"));
        obj.addParameter("Tax", this.getFieldValue("ctlTax"));
        obj.addParameter("TaxNo", this.getFieldValue("ctlddlTax"));
        obj.addParameter("TaxCode", this.getFieldDropDownExtraText("ctlddlTax"));
        obj.addParameter("DeliveryCharge", this.getFieldValue("ctlDeliveryCharge"));
        obj.addParameter("BankFee", this.getFieldValue("ctlBankFee"));
        obj.addParameter("CreditCardFee", this.getFieldValue("ctlCreditCardFee"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("SecondRef", this.getFieldValue("ctlSecondRef"));
        obj.addParameter("Narrative", this.getFieldValue("ctlNarrative"));
        //obj.addParameter("CanExported", this.getFieldValue("ctlCanExported"));

        //obj.addParameter("SupplierInvoiceNumber", this.getFieldValue("ctlSupplierInvoice"));
        //obj.addParameter("SupplierInvoiceDate", this.getFieldValue("ctlInvoiceDate"));
        //obj.addParameter("SupplierCode", this._SupplierCode);
        //obj.addParameter("SupplierName", this.getFieldValue("ctlSupplier"));
        obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
        obj.addParameter("CurrencyCode", this._CurrencyCode);


        obj.addDataOK(Function.createDelegate(this, this.saveHeaderThenLineComplete));
        obj.addError(Function.createDelegate(this, this.saveHeaderThenLineError));
        obj.addTimeout(Function.createDelegate(this, this.saveHeaderThenLineError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveHeaderThenLineError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveHeaderThenLineComplete: function(args) {
        if (args._result.NewID > 0) {
            this._intNewID = args._result.NewID;
            this.saveLine();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },
    saveLine: function() {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientInvoiceAdd");
        obj.set_DataObject("ClientInvoiceAdd");
        obj.set_DataAction("SaveLine");
        obj.addParameter("id", this._intNewID);
        obj.addParameter("GoodsInLineIDs", $R_FN.arrayToSingleString(this._aryGoodsInLineIds));
        obj.addDataOK(Function.createDelegate(this, this.saveLineOK));
        obj.addError(Function.createDelegate(this, this.saveLineError));
        obj.addTimeout(Function.createDelegate(this, this.saveLineError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveLineError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveLineOK: function(args) {
        if (args._result.Result) {
            this.showSaving(false);
            this.showInnerContent(true);
           this.finishedForm();
           // this.nextStep();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },
    validateForm: function() {
        this.onValidate();
        var blnOK = true;
        if (this._ctlMultiStep._intCurrentStep == 2) {
          //  if (!this.checkFieldEntered("ctlSupplierInvoice")) blnOK = false;
            if (!this.checkFieldEntered("ctlInvoiceDate")) blnOK = false;
            if (!this.checkFieldEntered("ctlCurrency")) blnOK = false;
            if (!this.checkFieldEntered("ctlInvoiceAmount")) blnOK = false;
            if (!this.checkFieldEntered("ctlGoodsValue")) blnOK = false;
            if (!this.checkFieldEntered("ctlTax")) blnOK = false;
            if (!this.checkFieldEntered("ctlddlTax")) blnOK = false;
            if (!this.checkFieldEntered("ctlClient")) blnOK = false;
            //if (this._SupplierCode.length <= 0) {
            //    this.showError(true, $R_RES.SupplierCodeMessage);
            //    blnOK = false;
            //    return blnOK;
            //}
            if ((this.getFieldValue("ctlSecondRef").length > 16)) {
                this.showError(true, $R_RES.SecondRefMessage);
                blnOK = false;
                return blnOK;
            }
            if ((this.getFieldValue("ctlNarrative").length > 41)) {
                this.showError(true, $R_RES.NarrativeMessage);
                blnOK = false;
                return blnOK;
            }
        //    var TotalValue = parseFloat(this.getFieldValue("ctlGoodsValue")) + parseFloat((this.getFieldValue("ctlTax")) ? this.getFieldValue("ctlTax") : "0") + parseFloat((this.getFieldValue("ctlDeliveryCharge")) ? this.getFieldValue("ctlDeliveryCharge") : "0") + parseFloat((this.getFieldValue("ctlBankFee")) ? this.getFieldValue("ctlBankFee") : "0") + parseFloat((this.getFieldValue("ctlCreditCardFee")) ? this.getFieldValue("ctlCreditCardFee") : "0");
        //    if (parseFloat(this.getFieldValue("ctlInvoiceAmount")).toFixed(5) != TotalValue.toFixed(5)) {
        //        this.showError(true, $R_RES.InvoiceAmountMessage);
        //        blnOK = false;
        //        return blnOK;
        //    }
        }
        if (this._ctlMultiStep._intCurrentStep == 3) {
            if (!this._ctlMail.validateFields()) blnOK = false;
        }
        if (!blnOK) this.showError(true);
        return blnOK;


    },

    showMailButtons: function() {
        var bln = this.getFieldValue("ctlSendMail");
        this.showField("ctlSendMailMessage", bln);
        $R_IBTN.showButton(this._ibtnSend, bln);
        $R_IBTN.showButton(this._ibtnSend_Footer, bln);
        $R_IBTN.showButton(this._ibtnContinue, !bln);
        $R_IBTN.showButton(this._ibtnContinue_Footer, !bln);
    },

    chooseIfSendMail: function() {
        this.showMailButtons();
    },

    getMessageText: function() {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewSupplierInvoice(this._intNewID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function(strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject($R_RES.NewSupplierInvoiceAdded);
    },

    validateMailForm: function() {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMail: function() {
        if (!this.validateMailForm()) return;
        Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), this._intCompanyID, Function.createDelegate(this, this.sendMailComplete));
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, false);
        $R_IBTN.showButton(this._ibtnSend_Footer, false);
    },

    sendMailComplete: function() {
        this.finishedForm();
    },

    finishedForm: function() {
        this._ctlMultiStep.showExplainLabel(false);
        this._ctlMultiStep.showSteps(false);
        $R_IBTN.showButton(this._ibtnSave, false);
        $R_IBTN.showButton(this._ibtnSave_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, false);
        $R_IBTN.showButton(this._ibtnSend_Footer, false);
        this.showSavedOK(true);
        this.onSaveComplete();
    },

    updateCurrency: function() {
        this._CurrencyCode = this.getFieldDropDownExtraText("ctlCurrency");
        $R_FN.setInnerHTML(this._lblCurrency_InvoiceAmount, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_GoodsInValue, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_Tax, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_DeliveryCharge, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_BankFee, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_CreditCardFee, this._CurrencyCode);
    },

    selectDefaultCurrency: function() {
        $R_FN.setInnerHTML(this._lblCurrency_InvoiceAmount, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_GoodsInValue, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_Tax, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_DeliveryCharge, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_BankFee, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_CreditCardFee, this._CurrencyCode);
    },

    updateTaxRate: function() {
        this.getTaxRate(this.getFieldValue("ctlddlTax"));
    },

    getTaxRate: function(taxId) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/SupplierInvoiceMainInfo");
        obj.set_DataObject("SupplierInvoiceMainInfo");
        obj.set_DataAction("GetTaxRate");
        obj.addParameter("TaxNo", taxId);
        obj.addDataOK(Function.createDelegate(this, this.getTaxRateComplete));
        obj.addError(Function.createDelegate(this, this.getTaxRateError));
        obj.addTimeout(Function.createDelegate(this, this.getTaxRateError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTaxRateError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    getTaxRateComplete: function(args) {
        if (args._result) {
            this._TaxRate = args._result.Rate;            
        }
    },

    validateTaxRate: function() {
        var blnOK = true;        
        if (parseFloat(this._TaxRate) <= 0) {
            blnOK = (parseFloat(this.getFieldValue("ctlTax")) == 0);
            if (!blnOK) {
                blnOK = confirm($R_RES.TaxValueMessage);
            }
        }
        else {
            blnOK = (parseFloat(this.getFieldValue("ctlTax")) > 0);
            if (!blnOK) {
                blnOK = confirm($R_RES.TaxValueMessage);
            }
        }
        if (!blnOK) this.showError(true);
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
