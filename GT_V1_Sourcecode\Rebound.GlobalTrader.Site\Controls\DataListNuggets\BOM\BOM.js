Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.initializeBase(this,[n]);this._frmConfirm=null;this._blnAS6081Tab=!1};Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},initialize:function(){this._ibtnPrint=$get(this._aryButtonIDs[0]);getSelected=$get("ctl00_cphMain_ctlBOMResults_ctlDB_ctl26_ddl");getSelectedGroup=$get("ctl00_cphMain_ctlBOMResults_ctlDB_ctl28_ddl");$find(this._aryButtonIDs[1]).getData();$find(this._aryButtonIDs[2]).getData();$R_IBTN.enableButton(this._ibtnPrint,!1);this._ibtnPrint&&this.getFilterField("ctlClient").show(!1);$R_IBTN.addClick(this._ibtnPrint,Function.createDelegate(this,this.showConfirmForm));this._table.addMultipleSelectionChanged(Function.createDelegate(this,this.selectionMade));this._frmConfirm=$find(this._aryFormIDs[0]);this._frmConfirm.addCancel(Function.createDelegate(this,this.hideConfirmForm));this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveCeaseComplete));this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/BOM";this._strDataObject="BOM";$find(this.getFilterField("ctlClient").get_id())._element.setAttribute("onchange",String.format('$find("{0}").getSalesPersonByClient()',this._element.id));Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.callBaseMethod(this,"initialize");$("#ctl00_cphMain_ctlBOMResults_ctlDB_txtLimitResults").val(50);$("#ctl00_cphMain_ctlBOMResults_ctlDB_pnlLinks").css("top","-17px");$("#ctl00_cphMain_ctlBOMResults_ctlDB_ctl24_lblDisabled").hide();$("#ctl00_cphMain_ctlBOMResults_ctlDB_ctl24").hide();$("#ctl00_cphMain_ctlBOMResults_ctlDB_ctl28").hide();$("#ctl00_cphMain_ctlBOMResults_ctlDB_ctl26").hide()},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.updateFilterVisibility();this.getSalesPersonDefault();this.getData()},dispose:function(){this.isDisposed||(this._blnPOHub=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this._blnAS6081Tab=this._intCurrentTab==1;this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("IsAS6081Tab",this._blnAS6081Tab);this._objData.addParameter("MyPageSize",$("#ctl00_cphMain_ctlBOMResults_ctlDB_txtLimitResults").val());this._objData.addParameter("SelectedRadio",this.getRadioButtonValue())},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_BOM(n.ID,n.Name),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.ClientCode),$R_FN.setCleanTextValue(n.RequestedBy)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.AssignedUser),$R_FN.setCleanTextValue(n.Quantity)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Code),$R_FN.setCleanTextValue(n.Part)),$R_FN.setCleanTextValue(n.Company),n.BOMStatus,n.Date,n.Value],this.getRadioButtonValue()=="Detail"?this._table.addRow(i,n.CustomerRequirementId,!1):this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlClient").show(this._blnPOHub)},showConfirmForm:function(){this._frmConfirm._strBOM=this._table._aryCurrentValues;GetGroupValue()==!0?(this._frmConfirm._selectedUse=getSelectedGroup.value,this._frmConfirm._selectedUseName=getSelectedGroup.options[getSelectedGroup.selectedIndex].text,this._frmConfirm._selectedIndex=getSelectedGroup.options[getSelectedGroup.selectedIndex].value,this._frmConfirm._IsGroupAssignment=GetGroupValue(),this._frmConfirm._strType=this.getRadioButtonValue()):(this._frmConfirm._selectedUse=getSelected.value,this._frmConfirm._selectedUseName=getSelected.options[getSelected.selectedIndex].text,this._frmConfirm._selectedIndex=getSelected.options[getSelected.selectedIndex].value,this._frmConfirm._IsGroupAssignment=GetGroupValue(),this._frmConfirm._strType=this.getRadioButtonValue());this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},saveCeaseComplete:function(){this.hideConfirmForm();this.getData()},selectionMade:function(){this._table._arySelectedIndexes.length>0&&$R_IBTN.enableButton(this._ibtnPrint,!0)},getSalesPersonByClient:function(){var n=999;this.getFilterField("ctlClient").getValue()!=null&&(n=this.getFilterField("ctlClient").getValue());this.getFilterField("ctlClientSalesperson")._ddl._intGlobalLoginClientNo=n;this.getFilterField("ctlClientSalesperson")._ddl.getData()},getSalesPersonDefault:function(){this.getFilterField("ctlClientSalesperson")._ddl._intGlobalLoginClientNo=999;this.getFilterField("ctlClientSalesperson")._ddl.getData()},getRadioButtonValue:function(){for(var i="",u=document.getElementById("ctl00_cphMain_ctlBOMResults_ctlDB_ctl21_ctlFilter_ctl11_ctl02_radHeaderDetail"),t=u.getElementsByTagName("input"),r,n=0;n<t.length;n++)if(t[n].checked){r=t[n];i=r.value;break}return i}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.BOM",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);