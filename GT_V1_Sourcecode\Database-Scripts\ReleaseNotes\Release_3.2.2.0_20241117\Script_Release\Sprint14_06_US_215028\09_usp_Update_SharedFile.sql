﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		24-Sep-2024		CREATE		Update date when offer is sent
[US-215028]		Trung Pham Van		06-Nov-2024		UPDATE		Update SentProspectiveOfferAt for customer requirement(s) 
[US-215028]		Trung Pham Van		12-Nov-2024		UPDATE		Remove unused column (SentDate)
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Update_SharedFile]
	@ProspectiveOfferId INT,
	@ProspectiveOfferLineIds VARCHAR(MAX),
	@CustomerRequirementIds VARCHAR(MAX)
AS
BEGIN
	UPDATE tbCustomerRequirement
	SET SentProspectiveOfferAt = GETDATE()
	WHERE CustomerRequirementId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@CustomerRequirementIds, ','))
END
