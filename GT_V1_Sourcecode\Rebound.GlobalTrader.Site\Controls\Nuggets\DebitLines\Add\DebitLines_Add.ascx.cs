//---------------------------------------------------------------------------------------------------------
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//---------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class DebitLines_Add : Base {

		#region Locals

		protected RadioButtonList _radSelectSource;
		protected FlexiDataTable _tblLines;
		protected List<string> _lstSources = new List<string>();

		#endregion

		#region Properties

		private bool _blnCanAddFromPOLine = true;
		public bool CanAddFromPOLine {
			get { return _blnCanAddFromPOLine; }
			set { _blnCanAddFromPOLine = value; }
		}

		private bool _blnCanAddFromService = true;
		public bool CanAddFromService {
			get { return _blnCanAddFromService; }
			set { _blnCanAddFromService = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "DebitLines_Add");
			AddScriptReference("Controls.Nuggets.DebitLines.Add.DebitLines_Add.js");
			_tblLines = (FlexiDataTable)FindContentControl("tblLines");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupSelectSourceScreen();
			SetupLinesTable();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		private void SetupLinesTable() {
			_tblLines.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tblLines.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode), true));
			_tblLines.Columns.Add(new FlexiDataColumn("Product", WidthManager.GetWidth(WidthManager.ColumnWidth.Product), true));
			_tblLines.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			_tblLines.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			_tblLines.Columns.Add(new FlexiDataColumn("ShipInCost",Unit.Empty, true));
		}

		/// <summary>
		/// Setup controls for Select Source screen
		/// </summary>
		private void SetupSelectSourceScreen() {
			_radSelectSource = (RadioButtonList)FindFieldControl("ctlSelectSource", "radSelectSource");
			if (_blnCanAddFromPOLine) AddRadioButton("FromPurchaseOrderLine", "PO");
			if (_blnCanAddFromService) AddRadioButton("FromService", "SERVICE");
			_radSelectSource.SelectedIndex = 0;
		}

		private void AddRadioButton(string strResourceTitle, string strJavascriptType) {
			_radSelectSource.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
			_lstSources.Add(strJavascriptType);
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intDebitID", _objQSManager.DebitID);
			_scScriptControlDescriptor.AddElementProperty("ibtnContinue", FindIconButton("ibtnContinue").ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", FindFooterIconButton("ibtnContinue").ClientID);
			_scScriptControlDescriptor.AddElementProperty("radSelectSource", _radSelectSource.ClientID);
			_scScriptControlDescriptor.AddElementProperty("trSelectPOLine", ((Control)FindContentControl("trSelectPOLine")).ClientID);
			_scScriptControlDescriptor.AddElementProperty("trSelectService", ((Control)FindContentControl("trSelectService")).ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLines", ((Control)FindContentControl("pnlLines")).ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblLines", _tblLines.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("ctlSelectService", ((ItemSearch.Service)FindContentControl("ctlSelectService")).ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLinesError", ((Control)FindContentControl("pnlLinesError")).ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblLinesError", ((Control)FindContentControl("lblLinesError")).ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLinesLoading", ((Control)FindContentControl("pnlLinesLoading")).ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLinesNoneFound", ((Control)FindContentControl("pnlLinesNoneFound")).ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLinesNotAvailable", ((Control)FindContentControl("pnlLinesNotAvailable")).ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblCurrency_Price", FindFieldControl("ctlPrice", "lblCurrency_Price").ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblExplain_Service", FindExplanationControl("lblExplain_Service").ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblExplain_POLine", FindExplanationControl("lblExplain_POLine").ClientID);
			_scScriptControlDescriptor.AddProperty("arySources", _lstSources);
		}

	}
}