Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.prototype={get_pnlCurrent:function(){return this._pnlCurrent},set_pnlCurrent:function(n){this._pnlCurrent!==n&&(this._pnlCurrent=n)},get_pnlOverdue:function(){return this._pnlOverdue},set_pnlOverdue:function(n){this._pnlOverdue!==n&&(this._pnlOverdue=n)},get_tblCurrent:function(){return this._tblCurrent},set_tblCurrent:function(n){this._tblCurrent!==n&&(this._tblCurrent=n)},get_tblOverdue:function(){return this._tblOverdue},set_tblOverdue:function(n){this._tblOverdue!==n&&(this._tblOverdue=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblCurrent&&this._tblCurrent.dispose(),this._tblOverdue&&this._tblOverdue.dispose(),this._pnlCurrent=null,this._pnlOverdue=null,this._tblCurrent=null,this._tblOverdue=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.callBaseMethod(this,"dispose"))},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyOpenSalesOrders");n.set_DataObject("MyOpenSalesOrders");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var r,u,t,i;for(this.showNoneFoundOrContent(n._result.Count),r=n._result,this._tblCurrent.clearTable(),this._tblCurrent.show(r.Current.length>0),i=0;i<r.Current.length;i++)t=r.Current[i],u=[$RGT_nubButton_SalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblCurrent.addRow(u,null);for(this._tblOverdue.clearTable(),this._tblOverdue.show(r.Overdue.length>0),i=0;i<r.Overdue.length;i++)t=r.Overdue[i],u=[$RGT_nubButton_SalesOrder(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblOverdue.addRow(u,null);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);