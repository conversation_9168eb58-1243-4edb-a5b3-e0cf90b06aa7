﻿using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data
{

    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Quotes : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base
    {

        protected override void GetData()
        {
            List<BLL.Quote> lst = null;
            try
            {
                lst = Quote.AutoSearch(SessionManager.ClientID ?? 0, GetFormValue_StringForNameSearchNew("search"));
                OutputQuoteList(lst);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
            base.GetData();
        }

    }
}