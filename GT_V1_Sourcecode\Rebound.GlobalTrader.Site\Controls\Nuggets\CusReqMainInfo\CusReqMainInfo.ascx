<%--
Marker     changed by      date         Remarks
[002]      Vinay          28/04/2015    ESMS Ticket Number. 	228
[003]      Umendra        23/08/2018    Adding All Alterante Button 
[004]      Umendra        16/01/2019    Adding View Tree Button
[005]      <PERSON>  28-may-2021    Clone requirement with HUBRFQ.
[006]	   <PERSON>	29-08-2023		RP-2227 (AS6081) Counterfeit Electronic Part

 --%>
<%@ Control Language="C#" CodeBehind="CusReqMainInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site"  %>
<style>
    .CloneREQ {
    background-color: #d2ffd2;
    float: left;
    width: 100%;
    min-height: 50px;
    border: 1px #88c77e solid;
    padding: 5px 0px 10px 0px;

}
        .CloneREQ a {
            color: black;
    padding: 0px;
    float: left;
    padding-left: 10px;
    padding-top: 8px;
        }
         .CloneREQ a:hover{color:#009900!important


         }
        
    .dropbtn {
    background-color: #d2ffd2;
    color: white;
    padding: 1px;
    font-size: 16px;
    border: none;
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    /*padding-right: 28px;*/
    font-size: 12px;
    white-space: nowrap;
}

.dropdown {
    position: relative;
    display: inline-block;
}

.dropdown-content {
       display: none;
    position: absolute;
    background-color: #f1f1f1;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgb(0 0 0 / 20%);
    z-index: 5;
    /*top: -1px;
    margin-left: 143px;*/
    /*padding-left:10px;*/
   
}

    .dropdown-content a {
        /*color: black;*/
        padding: 8px 7px;
        text-decoration: none;
        display: block;
    }

        .dropdown-content a:hover {
            background-color: #ddd;
        }

    .dropdown-content .notes {
        font-weight: bold;
        padding: 10px 10px 0px;
    }
    .dropdown-content .item {
        color: #0000ff;
        padding: 10px;
        display: block;
    }

        .dropdown-content .item:hover {
            background-color: #ddd;
        }

.dropdown:hover .dropdown-content {
    display: block;
}

.dropdown:hover .dropbtn {
    /*background-color: #3e8e41;*/
}

</style>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Main Information" BoxType="InPageSelection">
	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" />
		<ReboundUI:IconButton ID="ibtnQuote" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Quote" />
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="AddAlternate" IconCSSType="Add" />
		<ReboundUI:IconButton ID="ibtnClose" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Close" />
		<%--[002] code start--%>
		<ReboundUI:IconButton ID="ibtnReqPrint" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconCSSType="Print" IconTitleResource="PrintEnqForm" />
		<%--[002] code End--%>
		<%--BOM [001] code start--%>
        <%--<ReboundUI:IconButton ID="ibtnExportPurchaseHUB" runat="server" IconButtonMode="hyperlink" IsInitiallyEnabled="false" IconGroup="Nugget" IconTitleResource="ExportToPurchaseHUB" /> --%>
        		<ReboundUI:IconButton ID="ibtnExportPurchaseHUB" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ExportToPurchaseHUB"  /> 
		<%--BOM [001] code End--%>
          <%--code Start for Clone HUBRFQ--%>
        <ReboundUI:IconButton ID="ibtnPrintLabel" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Clone" IconCSSType="Add" IsInitiallyEnabled="false" />
        <%--[003] code start--%>
		<ReboundUI:IconButton ID="ibtnAddAll" runat="server" Visible="false" IconButtonMode="hyperlink" IconGroup="Nugget"   IconTitleResource="AddAllAlternate" IconCSSType="Add" />
		<%--[003] code End--%>
        <ReboundUI:IconButton  ID="ibtnMarkPossible" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="MarkFirmAlt" IconCSSType="Edit" />
 	<%--[004] code Start--%>
    <ReboundUI:IconButton ID="ibtnViewTree" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ViewTree" IconCSSType="Add" />
    	<%--[004] code End--%>
         <ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="DeleteAlternatePart" IconCSSType="Delete"  />
      	<%--[002] code start--%>
		<ReboundUI:IconButton ID="ibtnReqEccnPrint" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconCSSType="Print" IconTitleResource="PrintEnqEccnForm" />
		<%--[002] code End--%>
    </Links>
	<Content>
        <asp:Panel ID="pnlLabelTootTip" runat="server"  CssClass="topMenuRollovers  invisible" style="opacity: 1; position:absolute;width:145px;min-height:30px;">
	   <div  class="CloneREQ">
	   <asp:Panel ID="Panel3" CssClass="topMenuRolloverLink" runat="server">
	   <asp:HyperLink ID="hypCloneHUBRFQ" style="color:black"   runat="server" NavigateUrl="javascript:void(0);" ><%=Functions.GetGlobalResource("Misc", "CloneAndAddHUBRFQ")%></asp:HyperLink>
       <asp:HyperLink ID="hypCloneHUB"  style="color:black;" runat="server" NavigateUrl="javascript:void(0);" ><%=Functions.GetGlobalResource("Misc", "CloneAndSendHUB")%></asp:HyperLink>
       </asp:Panel>
	 <asp:Panel ID="pnlLabel"  runat="server"></asp:Panel>
	 </div>
	   <div class="curveBL">&nbsp;</div>
	   <div class="curveBR">&nbsp;</div>
	</asp:Panel>

		<ReboundUI:FlexiDataTable ID="tbl" runat="server" AllowSelection="true"  />
		
		<asp:Panel ID="pnlLoadingLineDetail" runat="server" CssClass="subInfoLoading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
		<asp:Panel ID="pnlLineDetailError" runat="server" CssClass="error invisible" />
		<asp:Panel id="pnlLineDetail" runat="server" CssClass="invisible">
			<h4 class="extraTopMargin"><asp:HyperLink ID="hypPrev" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingPrev">&laquo;</asp:HyperLink><asp:Label ID="lblLineTitle" runat="server" /><asp:HyperLink ID="hypNext" runat="server" NavigateUrl="javascript:void(0);" CssClass="linePagingNextPrev linePagingNext">&raquo;</asp:HyperLink></h4>
			<table class="threeCols">
				<tr>
                    <td class="col1">
      
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlCompany" runat="server" ResourceTitle="Company" />
							<ReboundUI:DataItemRow id="hidCompanyID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidCompanyName" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlContact" runat="server" ResourceTitle="Contact" />
							<ReboundUI:DataItemRow id="hidContactID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidContactName" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlQuantity" runat="server" ResourceTitle="Quantity" />
							<%--<ReboundUI:DataItemRow id="ctlPartNo" runat="server" ResourceTitle="PartNo"/>--%>
						    <ReboundUI:DataItemRow id="ctlPartNo" runat="server" FieldType="Hidden"/>
                            <ReboundUI:DataItemRow id="ctlPartNoDis" runat="server" ResourceTitle="PartNo" />
							
							<ReboundUI:DataItemRow ID="ctlAS6081" runat="server" ResourceTitle="AS6081"/> <%-- [006]--%>
							<ReboundUI:DataItemRow id="ctlROHS" runat="server" ResourceTitle="ROHS" />
							<ReboundUI:DataItemRow id="hidROHS" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlCustomerPart" runat="server" ResourceTitle="CustomerPartNo" />
							<ReboundUI:DataItemRow id="ctlManufacturer" runat="server" ResourceTitle="Manufacturer" />
							<ReboundUI:DataItemRow id="hidManufacturer" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidMfrNotes" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="hidManufacturerNo" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlDateCode" runat="server" ResourceTitle="DateCode" />
							<%--<ReboundUI:DataItemRow id="ctlProduct" runat="server" ResourceTitle="Product" />--%>
                              <ReboundUI:DataItemRow id="ctlProduct" runat="server" FieldType="Hidden"  />
                              <ReboundUI:DataItemRow id="ctlProductDis" runat="server" ResourceTitle="Product" />
							<ReboundUI:DataItemRow id="hidProductID" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow id="ctlPrdDutyCodeRate" runat="server" ResourceTitle="DutyCodeRate" />
							<ReboundUI:DataItemRow id="ctlPackage" runat="server" ResourceTitle="Package" />
							<ReboundUI:DataItemRow id="hidPackageID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlPartWatch" runat="server" ResourceTitle="PartWatch" FieldType="CheckBox" />
							<ReboundUI:DataItemRow id="ctlFactorySealed" runat="server" ResourceTitle="FactorySealed2" FieldType="CheckBox" />
                            <ReboundUI:DataItemRow id="ctlMSL" runat="server" ResourceTitle="MSL" />
                            <ReboundUI:DataItemRow id="ctlSalesOrderNo" runat="server" ResourceTitle="SalesOrderNumber" />
                            <ReboundUI:DataItemRow id="hidMSL" runat="server" FieldType="Hidden" />

                            <ReboundUI:DataItemRow id="ctlSalespersion" runat="server" ResourceTitle="SupportTeamMember" />
						<ReboundUI:DataItemRow id="hidSalesPersion" runat="server" FieldType="Hidden" />

                            <ReboundUI:DataItemRow id="ctlCountryOfOrigin" runat="server" ResourceTitle="CountryOfOrigin" />
                            <ReboundUI:DataItemRow id="ctlLifeCycleStage" runat="server"  ResourceTitle="LifeCycleStage" />
                            <ReboundUI:DataItemRow id="ctlIHSProduct" runat="server" ResourceTitle="IHSProductName" />
                            <ReboundUI:DataItemRow id="ctlHTSCode" runat="server" ResourceTitle="HTSCode" />
                            <ReboundUI:DataItemRow id="ctlECCNCode" runat="server" ResourceTitle="ECCNCode" />
                            <ReboundUI:DataItemRow id="ctlPackagingSize" runat="server" ResourceTitle="PackagingSize" />
                            <ReboundUI:DataItemRow id="ctlDescriptions" runat="server" ResourceTitle="Descriptions" />
                            <ReboundUI:DataItemRow id="ctlAveragePrice" runat="server" ResourceTitle="AveragePrice" />
                            <ReboundUI:DataItemRow id="ctlParentRequirementNo" runat="server" ResourceTitle="ParentRequirementNo" />
							
					</table>
					</td>
					<td class="col2">
						<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
							<ReboundUI:DataItemRow id="ctlTargetPrice" runat="server" ResourceTitle="CustomerTargetPrice" />
							<ReboundUI:DataItemRow id="hidPrice" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlCurrency" runat="server" ResourceTitle="Currency" />
							<ReboundUI:DataItemRow id="hidCurrencyID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlDateRequired" runat="server" ResourceTitle="CustDateRequired" />
							<ReboundUI:DataItemRow id="ctlClosed" runat="server" ResourceTitle="Closed" FieldType="CheckBox" />
							<ReboundUI:DataItemRow id="ctlClosedReason" runat="server" ResourceTitle="Reason" />
							<ReboundUI:DataItemRow id="ctlUsage" runat="server" ResourceTitle="Usage" />
							<ReboundUI:DataItemRow id="hidUsageID" runat="server" FieldType="Hidden" />
							<ReboundUI:DataItemRow id="ctlBOM" runat="server" ResourceTitle="BOMChk" FieldType="CheckBox" />
							<ReboundUI:DataItemRow id="ctlBOMName" runat="server" ResourceTitle="BOMName" />
							<ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="CustomerNotes" />
							<ReboundUI:DataItemRow id="ctlInstructions" runat="server" ResourceTitle="InternalNotes" />
							<ReboundUI:DataItemRow id="ctlPurchasingNotes" runat="server" ResourceTitle="CompanyPurchasingNotes" />
						    <ReboundUI:DataItemRow id="hidDisplayStatus" runat="server" FieldType="Hidden" />
						    <ReboundUI:DataItemRow id="hidBOMID" runat="server" FieldType="Hidden" />
						    <ReboundUI:DataItemRow id="hidBOMHeaderDisplayStatus" runat="server" FieldType="Hidden" />
						    <ReboundUI:DataItemRow id="ctlBOMHeader" runat="server" ResourceTitle="IPOBOM" />
						     <ReboundUI:DataItemRow id="ctlPQA" runat="server" ResourceTitle="PQA" FieldType="CheckBox" />
						     <ReboundUI:DataItemRow id="ctlObsolete" runat="server" ResourceTitle="Obsolete" FieldType="CheckBox"/>
							
						</table>
					</td>
					<td class="col3">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
					        
					       
							
							<ReboundUI:DataItemRow id="ctlLastTimeBuy" runat="server"  ResourceTitle="LastTimeBuy" FieldType="CheckBox"/>
							<ReboundUI:DataItemRow id="ctlRefirbsAcceptable" runat="server" ResourceTitle="RefirbsAcceptable" FieldType="CheckBox"/>
							<ReboundUI:DataItemRow id="ctlTestingRequired" runat="server"  ResourceTitle="TestingRequired" FieldType="CheckBox"/>							
							<ReboundUI:DataItemRow id="ctlTargetSellPrice" runat="server" ResourceTitle="TargetSellPrice" FieldType="Hidden"/>	
                        
                            <ReboundUI:DataItemRow id="ctlAlternativesAccepted" runat="server" ResourceTitle="AlternativesAccepted" FieldType="CheckBox"/>
							<ReboundUI:DataItemRow id="ctlRepeatBusiness" runat="server"  ResourceTitle="RepeatBusiness" FieldType="CheckBox"/>
                        						
							<ReboundUI:DataItemRow id="ctlCompetitorBestoffer" runat="server" ResourceTitle="CompetitorBestoffer" />
							
							<ReboundUI:DataItemRow id="ctlTargetSellPriceHidden" runat="server" FieldType="Hidden"  />							
							<ReboundUI:DataItemRow id="ctlCompetitorBestofferHidden" runat="server" FieldType="Hidden"  />
							
							<ReboundUI:DataItemRow id="ctlCustomerDecisionDate" runat="server" ResourceTitle="CustomerDecisionDate" />
							<ReboundUI:DataItemRow id="ctlRFQClosingDate" runat="server" ResourceTitle="RFQClosingDate" />
							<ReboundUI:DataItemRow id="ctlQuoteValidityRequiredHid" runat="server" FieldType="Hidden"/>	
							<ReboundUI:DataItemRow id="ctlQuoteValidityRequired" runat="server" ResourceTitle="QuoteValidityRequired"/>							
							<ReboundUI:DataItemRow id="ctlType" runat="server" ResourceTitle="Type" />
							<ReboundUI:DataItemRow id="ctlTypeHid" runat="server" ResourceTitle="Type" FieldType="Hidden"/>
							<ReboundUI:DataItemRow id="ctlOrderToPlace" runat="server" ResourceTitle="OrderToPlace" FieldType="CheckBox" />
							<ReboundUI:DataItemRow id="ctlRequirementforTraceability" runat="server" ResourceTitle="RequirementforTraceability" />
							<ReboundUI:DataItemRow id="ctlRequirementforTraceabilityHid" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow id="ctlEAU" runat="server" ResourceTitle="EAU" />
                            <ReboundUI:DataItemRow id="hidCustGCNo" runat="server" FieldType="Hidden" />
                            <ReboundUI:DataItemRow id="hidSalesman" runat="server" FieldType="Hidden" />

						
						</table>
					</td>
				</tr>
			</table>
		</asp:Panel>
	</Content>
	
	<Forms>
		<ReboundForm:CustomerRequirementMainInfo_Edit ID="ctlEdit" runat="server" />
		<ReboundForm:CustomerRequirementMainInfo_AddAlternate id="ctlAdd" runat="server" />
		<ReboundForm:CustomerRequirementMainInfo_Close ID="ctlClose" runat="server" />
		<ReboundForm:CustomerRequirementMainInfo_Confirm ID="ctlConfirm" runat="server" />
        <ReboundForm:CustomerRequirementMainInfo_Delete ID="ctlDelete" runat="server" />
        <ReboundForm:CustomerRequirementMainInfo_CloneHUBRFQ ID="ctlCloneHUBRFQ" runat="server" />
        <ReboundForm:CustomerRequirementMainInfo_CloneHUB ID="ctlCloneHUB" runat="server" />
        
	</Forms>
</ReboundUI_Nugget:DesignBase>
<style>
.ihspartstatusdoc{background-position: right;background-size: contain}

#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_th_0{width:20px!important;}
#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_th_1{width:179px!important;}
#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_th_2{width:71px!important;}
#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_th_3{width:271px!important;}
#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_tbl_th_4{width:114px!important;}

</style>