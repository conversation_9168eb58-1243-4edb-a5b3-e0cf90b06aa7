﻿using System;
using System.Web.UI;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns
{
    public partial class GILineBarcodesStatus : Base
    {
        protected override void OnLoad(EventArgs e)
        {
            SetDropDownType("GILineBarcodesStatus");
            AddScriptReference("Controls.DropDowns.GILineBarcodesStatus.GILineBarcodesStatus");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.GILineBarcodesStatus", ClientID);
            base.OnLoad(e);
        }

    }
}