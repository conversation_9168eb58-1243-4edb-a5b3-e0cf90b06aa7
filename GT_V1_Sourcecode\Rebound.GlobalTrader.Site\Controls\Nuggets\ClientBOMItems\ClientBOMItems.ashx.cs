/* Marker     changed by      date         Remarks
 [001]      Vinay           01/08/2012     Delete UnAllocated Stock Bug
 */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Text;
using System.IO;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class ClientBOMItems : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
                    case "ExportToCSV": ExportToCSV(); break;
                    case "ReleaseRequirement": ReleaseRequirement(); break;
                    case "Update": Update(); break;
                    case "DeleteBomItem": DeleteBomItem(); break;
                    case "UnReleaseBomItem": UnReleaseBomItem(); break;
                    case "GetItem": GetItem(); break;
                    case "NoBidRequirement": NoBidRequirement(); break;
                    case "RecallNoBidRequirement": RecallNoBidRequirement(); break;
                    case "SaveExpedite": SaveExpedite(); break;
                    case "UpdateCustomerRequirementWithHUBRFQ": UpdateCustomerRequirementWithHUBRFQ(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

        

        /// <summary>
        /// Add new quoteLine
        /// </summary>
        public void Update()
        {
            try
            {
                string ids = GetFormValue_String("ReqIds");
                string Bomid = GetFormValue_String("BomId");
              System.Boolean intUpdateStatus = CustomerRequirement.ClientBOMItemsUpdate(
                    Convert.ToInt32(Bomid)
                    , LoginID
                    , SessionManager.ClientID
                    , ids
                    , (int)BOMStatus.List.Open
                    );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", Convert.ToInt32(intUpdateStatus) > 0);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
       
		/// <summary>
		/// Gets data for a stock item
		/// </summary>
        public void GetData()
        {
            try
            {
                List<CustomerRequirement> lst = CustomerRequirement.GetClientBOMListForCustomerRequirement(ID, SessionManager.ClientID);
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                jsn.AddVariable("Count", lst.Count);
                bool blnAllHasDeliveryDate = false;
                bool blnAllHasProduct = false;
                if (lst.Count > 0)
                {
                    blnAllHasDeliveryDate = lst[0].AllSorcingHasDelDate == 0;
                    blnAllHasProduct = lst[0].AllSorcingHasProduct == 0;
                }
                foreach (CustomerRequirement cReq in lst)
                {

                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", cReq.CustomerRequirementId);
                    jsnItem.AddVariable("CustReqNo", cReq.CustomerRequirementNumber);
                    jsnItem.AddVariable("PartNo", cReq.Part);
                    //jsnItem.AddVariable("Part", (cReq.Alternate) ? String.Format("{0} ({1})", cReq.Part, Functions.GetGlobalResource("Misc", "Alternate")) : cReq.Part);
                    jsnItem.AddVariable("Part", Functions.GetPartWithAlternate(cReq.Part, cReq.AlternateStatus, cReq.Alternate));
                    jsnItem.AddVariable("Closed", cReq.Closed);
                    jsnItem.AddVariable("ClosedReason", cReq.ClosedReason);
                    jsnItem.AddVariable("MfrNo", cReq.ManufacturerNo);
                    jsnItem.AddVariable("Mfr", cReq.ManufacturerCode);
                    jsnItem.AddVariable("Product", cReq.ProductName);
                    jsnItem.AddVariable("DC", cReq.DateCode);
                    jsnItem.AddVariable("Package", cReq.PackageName);
                    jsnItem.AddVariable("Price", Functions.FormatCurrency(cReq.Price, cReq.CurrencyCode));
                    jsnItem.AddVariable("TPriceInBom", Functions.FormatCurrency(cReq.ConvertedTargetValue, cReq.BOMCurrencyCode));
                    if (cReq.CurrencyNo != SessionManager.ClientCurrencyID) jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(cReq.Price, (int)cReq.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                    jsnItem.AddVariable("Quantity", Functions.FormatNumeric(cReq.Quantity));
                    jsnItem.AddVariable("Alt", cReq.Alternate);
                    jsnItem.AddVariable("ROHS", cReq.ROHS);
                    jsnItem.AddVariable("Date", Functions.FormatDate(cReq.DatePromised));
                    jsnItem.AddVariable("CustomerPart", cReq.CustomerPart);
                    if (SessionManager.IsPOHub == true)
                    {
                        jsnItem.AddVariable("Company", cReq.ClientName);
                    }
                    else
                    {
                        jsnItem.AddVariable("Company", cReq.CompanyName);
                    }
                    jsnItem.AddVariable("CompanyNo", cReq.CompanyNo);
                    jsnItem.AddVariable("SalesmanName", cReq.SalesmanName);
                    jsnItem.AddVariable("Salesman", cReq.Salesman);
                    jsnItem.AddVariable("BOMCode", cReq.BOMCode);
                    jsnItem.AddVariable("BOMFullName", cReq.BOMFullName);
                    jsnItem.AddVariable("Instructions", Functions.ReplaceLineBreaks(cReq.Instructions));
                    //if (!cReq.Alternate)
                    //{
                    //    string strReason = (cReq.ReasonNo > 0) ? string.Format(" ({0})", cReq.ClosedReason) : "";
                    //    jsn.AddVariable("DisplayStatus", String.Format("{0}{1}", Functions.GetGlobalResource("Status", cReq.DisplayStatus), strReason));
                    //}
                    jsnItem.AddVariable("BOMNo", cReq.BOMNo);
                    jsnItem.AddVariable("Released", cReq.POHubReleaseBy > 0);
                    jsnItem.AddVariable("CMNo", cReq.CompanyNo);
                    //if(SessionManager.IsPOHub.Value)
                    //    jsnItem.AddVariable("HasSourcingResult", (cReq.SourcingResultId.HasValue && cReq.SourcingResultId > 0 && cReq.POHubCompany > 0));
                    //else
                    //    jsnItem.AddVariable("HasSourcingResult", (cReq.SourcingResultId.HasValue && cReq.SourcingResultId > 0));


                    if (SessionManager.IsPOHub.Value)
                        jsnItem.AddVariable("HasSourcingResult", cReq.HasHubSourcingResult.Value);
                    else
                        jsnItem.AddVariable("HasSourcingResult", cReq.HasClientSourcingResult.Value);


                    jsnItem.AddVariable("IsRequestToPurchaseQuote", (cReq.RequestToPOHubBy ?? 0) > 0);

                    jsnItem.AddVariable("PurchaseQuoteNumber", cReq.PurchaseQuoteNumber);
                    jsnItem.AddVariable("PurchaseQuoteId", cReq.PurchaseQuoteId);
                    jsnItem.AddVariable("IsPurchaseRequestCreated", ((cReq.PurchaseQuoteId ?? 0) > 0));
                    jsnItem.AddVariable("FactorySealed", cReq.FactorySealed == false ? "NO" : "YES");
                    jsnItem.AddVariable("MSL", cReq.MSL);
                    jsnItem.AddVariable("SourcingResult", cReq.SourcingResult > 0 ? false : true);
                    jsnItem.AddVariable("BOMStatus", cReq.BOMStatus);
                    jsnItem.AddVariable("IPOClientNo", cReq.ClientNo);
                    jsnItem.AddVariable("IsNoBid", cReq.IsNoBid);
                    jsnItem.AddVariable("IsExpeditDate", cReq.ExpeditDate.HasValue != false ? "Yes" : "");
                    jsnItem.AddVariable("UpdateByPH", cReq.UpdateByPH);
                    jsnItem.AddVariable("RequestToPOHubBy", cReq.RequestToPOHubBy);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();

                    jsnItem = null;
                }
                jsn.AddVariable("Items", jsnItems);
                jsn.AddVariable("AllHasDelDate", blnAllHasDeliveryDate);
                jsn.AddVariable("AllHasProduct", blnAllHasProduct);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                ////List<List<object>> lstData = CustomerRequirement.GetBOMListForCRList(ID, SessionManager.ClientID);
                JsonObject jsnItems = new JsonObject(true);
                string CurrencyCode = GetFormValue_String("currency_Code");
                //return saved filename to the page
                string strFilename = FileUploadManager.ExportToCSV((int)Rebound.GlobalTrader.BLL.Report.List.RequirementWithBOM, ID, CurrencyCode,"E");
                jsn.AddVariable("Filename", String.Format("{0}/{1}", FileUploadManager.GetUploadTempCSVFilePath_Relative(false), strFilename));
                OutputResult(jsn);                
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }
        /// <summary>
        /// NoBid customer requirement for client user
        /// </summary>
        public void RecallNoBidRequirement()
        {
            try
            {
                int? Bomid = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                string SalesManName = GetFormValue_String("SalesManName");
                int? SalesManNo = GetFormValue_Int("SalesManNo");
                int? CustReqNo = GetFormValue_Int("CustReqNo");
                bool blnResult = CustomerRequirement.RecallNoBidRequirement(
                    ID,
                    LoginID
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>RecallNoBidRequirement
        /// NoBid customer requirement for client user
        /// </summary>
        public void NoBidRequirement()
        {
            try
            {
                int? Bomid = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                string SalesManName = GetFormValue_String("SalesManName");
                int? SalesManNo = GetFormValue_Int("SalesManNo");
                int? CustReqNo = GetFormValue_Int("CustReqNo");
                string Notes = GetFormValue_String("Notes");
                bool blnResult = CustomerRequirement.NoBidRequirement(
                    ID,
                    LoginID,
                   Convert.ToInt32(Bomid),
                   Notes
                );

                if (blnResult)
                {
                    WebServices servic = new WebServices();
                    //BLL.CustomerRequirement bomr = BLL.CustomerRequirement.Get(ID);
                    servic.NotifyNoBidBom((SalesManNo ?? 0).ToString(), "", Functions.GetGlobalResource("Messages", "BOMNoBid"), (Bomid ?? 0), BOMCode, BOMName, BomCompanyNo, BomCompanyName, false, ID);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>
        /// Release customer requirement for client user
        /// </summary>
        public void ReleaseRequirement()
        {
            try
            {
                int? Bomid = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                string SalesManName = GetFormValue_String("SalesManName");
                int? SalesManNo = GetFormValue_Int("SalesManNo");
                int? CustReqNo = GetFormValue_Int("CustReqNo");
                bool blnResult = CustomerRequirement.ReleaseRequirement(
                    ID,
                    LoginID,
                   Convert.ToInt32(Bomid)
                );

                if (blnResult)
                {
                    WebServices servic = new WebServices();
                    //BLL.CustomerRequirement bomr = BLL.CustomerRequirement.Get(ID);
                    servic.NotifyReleaseBom((SalesManNo ?? 0).ToString(), "", Functions.GetGlobalResource("Messages", "BOMReleased"), (Bomid ?? 0), BOMCode, BOMName, BomCompanyNo, BomCompanyName, false, ID);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        /// <summary>
        /// Created by surendra
        /// Created date March 31, 2016
        /// Delete item from bom 
        /// </summary>
        public void DeleteBomItem( ) 
        { 
            int? BomID = GetFormValue_Int("BomID");
            int? RequirementID = GetFormValue_Int("RequirementID");
            bool IsDeleted = CustomerRequirement.DeleteBomItem(
                BomID, 
                RequirementID,
                LoginID,
                SessionManager.ClientID
                );
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Result", IsDeleted);
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
        }
        /// <summary>
        /// Created by surendra
        /// Created date April 18, 2016
        /// UnRelease item from bom 
        /// </summary>
        private void UnReleaseBomItem()
        {
            int? BomID = GetFormValue_Int("BomID");
            int? RequirementID = GetFormValue_Int("RequirementID");
            bool IsSuccess = CustomerRequirement.UnReleaseBomItem(BomID, RequirementID);
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("Result", IsSuccess);
            OutputResult(jsn);
            jsn.Dispose();
            jsn = null;
        }
        private string FormatTextForCSV(string strIn, bool blnLeadingComma, bool blnEndingLineFeed)
        {
            strIn = strIn.Replace(@"""", @"""""");
            return string.Format(@"{0}""{1}""{2}", (blnLeadingComma) ? "," : "", strIn, (blnEndingLineFeed) ? System.Environment.NewLine : "");
        }

        private string BlankCSVLine()
        {
            return string.Format(@"""""{0}", System.Environment.NewLine);
        }

        private void GetItem()
        {
            CustomerRequirement cReq = CustomerRequirement.GetReqBOM(ID, SessionManager.ClientID);
            OutputResult(GetItem(cReq));
            cReq = null;
        }
        /// <summary>
        /// get specific customerRequirement by key
        /// </summary>
        public JsonObject GetItem(CustomerRequirement cReq)
        {
            JsonObject jsn = null;
            if (cReq != null)
            {
                jsn = new JsonObject();
                jsn.AddVariable("CustomerRequirementNumber", cReq.CustomerRequirementNumber);
                jsn.AddVariable("CustomerNo", cReq.CompanyNo);
                jsn.AddVariable("CustomerName", cReq.CompanyName);
                jsn.AddVariable("CustomerOnStop", cReq.CompanyOnStop);
                jsn.AddVariable("Contact", cReq.ContactName);
                jsn.AddVariable("ContactNo", cReq.ContactNo);
                jsn.AddVariable("Salesman", cReq.SalesmanName);
                jsn.AddVariable("SalesmanNo", cReq.Salesman);
                jsn.AddVariable("FullPart", cReq.Part);
                jsn.AddVariable("Part", cReq.Part);
                jsn.AddVariable("CustomerPart", cReq.CustomerPart);
                jsn.AddVariable("Manufacturer", cReq.ManufacturerName);
                jsn.AddVariable("ManufacturerNo", cReq.ManufacturerNo);
                jsn.AddVariable("DateCode", cReq.DateCode);
                jsn.AddVariable("Package", cReq.PackageDescription);
                jsn.AddVariable("PackageName", cReq.PackageName);
                jsn.AddVariable("PackageNo", cReq.PackageNo);
                jsn.AddVariable("Quantity", cReq.Quantity);
                string strPrice = Functions.FormatCurrency(cReq.Price, cReq.CurrencyCode);

                if (SessionManager.IsPOHub.Value)
                {
                    double? reqInBasePrice = BLL.Currency.ConvertValueToBaseCurrency(cReq.Price, (int)cReq.CurrencyNo, DateTime.Now);
                    if (cReq.ReqGlobalCurrencyNo != cReq.ClientGlobalCurrencyNo) strPrice += String.Format(" ({0})", Functions.FormatCurrency(BLL.Currency.ConvertValueFromBaseCurrency(reqInBasePrice, (int)cReq.ClientCurrencyNo, DateTime.Now), cReq.ClientCurrencyCode));
                }
                else
                {
                    if (cReq.CurrencyNo != SessionManager.ClientCurrencyID) strPrice += String.Format(" ({0})", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(cReq.Price, (int)cReq.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                }

                jsn.AddVariable("Price",Functions.ReplaceLineBreaks( strPrice));
                jsn.AddVariable("PriceRaw", Functions.FormatCurrency(cReq.Price));
                jsn.AddVariable("Currency", Functions.ReplaceLineBreaks(Functions.FormatCurrencyDescription(cReq.CurrencyDescription, cReq.CurrencyCode)));
                jsn.AddVariable("CurrencyCode", Functions.ReplaceLineBreaks(cReq.CurrencyCode));
                jsn.AddVariable("CurrencyNo", cReq.CurrencyNo);
                jsn.AddVariable("ReceivedDate", Functions.FormatDate(cReq.ReceivedDate));
                jsn.AddVariable("DatePromised", Functions.FormatDate(cReq.DatePromised));
                jsn.AddVariable("Product", Functions.ReplaceLineBreaks(cReq.ProductDescription));
                jsn.AddVariable("ProductName", Functions.ReplaceLineBreaks(cReq.ProductName));
                jsn.AddVariable("ProductNo", cReq.ProductNo);
                jsn.AddVariable("Usage", Functions.ReplaceLineBreaks(cReq.UsageName));
                jsn.AddVariable("UsageNo", cReq.UsageNo);
                jsn.AddVariable("Shortage", cReq.Shortage);
                jsn.AddVariable("Alternate", cReq.Alternate);
                jsn.AddVariable("OriginalCReqNo", cReq.OriginalCustomerRequirementNo);
                jsn.AddVariable("ReasonNo", cReq.ReasonNo);
                jsn.AddVariable("ROHS", cReq.ROHS);
                jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(Functions.ReplaceLineBreaks(cReq.Notes)));
                jsn.AddVariable("Instructions",Functions.ReplaceLineBreaks( Functions.ReplaceLineBreaks(cReq.Instructions)));
                jsn.AddVariable("Closed", cReq.Closed);
                jsn.AddVariable("ClosedReason",Functions.ReplaceLineBreaks( cReq.ClosedReason));
                jsn.AddVariable("DLUP", Functions.FormatDLUP(cReq.DLUP, cReq.UpdatedBy));
                string strReason = (cReq.ReasonNo > 0) ? string.Format(" ({0})", cReq.ClosedReason) : "";
                jsn.AddVariable("DisplayStatus",Functions.ReplaceLineBreaks( string.Format("{0} {1}", Functions.GetGlobalResource("Status", cReq.DisplayStatus), strReason)));
                jsn.AddVariable("PartWatch", cReq.PartWatch);
                jsn.AddVariable("BOM", cReq.BOM);
                jsn.AddVariable("BOMName",Functions.ReplaceLineBreaks( cReq.BOMName));
                jsn.AddVariable("DivisionNo", cReq.DivisionNo);
                jsn.AddVariable("Division", Functions.ReplaceLineBreaks(cReq.DivisionName));
                jsn.AddVariable("Traceability", cReq.Traceability);
                jsn.AddVariable("BOMHeader",Functions.ReplaceLineBreaks( cReq.BOMHeader));
                jsn.AddVariable("BOMId", cReq.BOMNo);
                jsn.AddVariable("RequestToPOHubBy", cReq.RequestToPOHubBy);
                jsn.AddVariable("MSL", Functions.ReplaceLineBreaks(cReq.MSL));
                jsn.AddVariable("FactorySealed", cReq.FactorySealed);

                jsn.AddVariable("PQA", cReq.PQA);
                jsn.AddVariable("Obsolete", cReq.Obsolete);

                jsn.AddVariable("LastTimeBuy", cReq.LastTimeBuy);
                jsn.AddVariable("RefirbsAcceptable", cReq.RefirbsAcceptable);
                jsn.AddVariable("TestingRequired", cReq.TestingRequired);

                jsn.AddVariable("hidTargetSellPrice", Functions.FormatCurrency(cReq.TargetSellPrice));
                jsn.AddVariable("hidCompetitorBestOffer", Functions.FormatCurrency(cReq.CompetitorBestOffer));

                jsn.AddVariable("TargetSellPrice", Functions.FormatCurrency(cReq.TargetSellPrice, cReq.CurrencyCode));
                jsn.AddVariable("CompetitorBestOffer", Functions.FormatCurrency(cReq.CompetitorBestOffer, cReq.CurrencyCode));
                jsn.AddVariable("CustomerDecisionDate", Functions.FormatDate(cReq.CustomerDecisionDate));

                jsn.AddVariable("RFQClosingDate", Functions.FormatDate(cReq.RFQClosingDate));
                jsn.AddVariable("QuoteValidityRequired", cReq.QuoteValidityRequired);
                jsn.AddVariable("Type", cReq.Type);

                jsn.AddVariable("OrderToPlace", cReq.OrderToPlace);
                jsn.AddVariable("RequirementforTraceability", cReq.RequirementforTraceability);
                jsn.AddVariable("QuoteValidityText",Functions.ReplaceLineBreaks( cReq.QuoteValidityText));

                jsn.AddVariable("ReqTypeText",Functions.ReplaceLineBreaks( cReq.ReqTypeText));
                jsn.AddVariable("ReqForTraceabilityText",Functions.ReplaceLineBreaks( cReq.ReqForTraceabilityText));
                jsn.AddVariable("SourcingResult", cReq.SourcingResult > 0 ? false : true);
                jsn.AddVariable("EAU",Functions.ReplaceLineBreaks( cReq.EAU));
                jsn.AddVariable("IsNoBid", cReq.IsNoBid);
                jsn.AddVariable("IsNoBidStatus", cReq.IsNoBid == true ? Functions.GetGlobalResource("FormFields", "IsNoBid") : "");
                jsn.AddVariable("NoBidNotes",Functions.ReplaceLineBreaks( Functions.ReplaceLineBreaks(cReq.NoBidNotes)));
                jsn.AddVariable("AlternativesAccepted", cReq.AlternativesAccepted);
                jsn.AddVariable("RepeatBusiness", cReq.RepeatBusiness);
                jsn.AddVariable("DutyCodeAndRate", string.Format("{0} ({1})", cReq.DutyCode, Functions.FormatNumeric(cReq.DutyRate, 5)));
                jsn.AddVariable("MSLLevelNo", cReq.MSLLevelNo);

            }
            return jsn;
        }
        public void SaveExpedite()
        {
            try
            {
               string Subject = Functions.GetGlobalResource("Printing", "CommunicationNoteSubject");
                string ReqIds = GetFormValue_String("ReqIds");
                int HUBRFQId = ID;
                string HUBRFQName = GetFormValue_String("HUBRFQName");
                string Notes = GetFormValue_String("AddNotes");
                int RequestToPOHubBy = SessionManager.IsPOHub == true ? GetFormValue_Int("RequestToPOHubBy") : GetFormValue_Int("UpdateByPH");
                int CompanyNo = GetFormValue_Int("HUBRFQCompanyNo");
                System.Int32 Contact2No = GetFormValue_Int("Contact2No");
                int intResult = CustomerRequirement.InsertExpedite(
                      HUBRFQId,
                      GetFormValue_String("AddNotes"),
                      LoginID,
                      ReqIds,
                      RequestToPOHubBy, 
                      null,
                      null

                );
                if (intResult > 0)
                {
                    List<CustomerRequirement> lst = CustomerRequirement.GetHUBRFQReqNos(ReqIds, SessionManager.ClientID);


                    // GetFormValue_Int("intBOMNo");


                    string FullPart = "";
                    string CustomerRequirementNumber = "";
                    foreach (CustomerRequirement objDetails in lst)
                    {
                        FullPart = FullPart + "," + objDetails.FullPart;
                        CustomerRequirementNumber = CustomerRequirementNumber + "," + Convert.ToString(objDetails.CustomerRequirementNumber);

                    }
                    StringBuilder message = new StringBuilder();
                    WebServices servic = new WebServices();
         
                    string poref = string.Format("Reference HUBRFQ   : <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">{2}</a>", "javascript:void(0);", "Ord_BOMDetail.aspx?BOM=" + HUBRFQId, HUBRFQName);
                    message.Append("Message By  : " + SessionManager.LoginFullName + "<br />");
                    message.Append("Date & Time : " + Functions.FormatDate(Functions.GetUKLocalTime()) + " " + Functions.FormatTime(Functions.GetUKLocalTime()) + "<br />");
                    message.Append("Part  : " + FullPart.Remove(0, 1) + "<br />");
                    message.Append("Req Nos  : " + CustomerRequirementNumber.Remove(0, 1) + "<br />");
                    message.Append(poref + "<br /><br />");
                    message.Append("Communication Note  : " + Notes + "<br />");
                    message.Append("<br /><br />Regards,<br />" + SessionManager.LoginFullName + "<br />");

                    servic.NotifyMessageExpediteNote(Convert.ToString(RequestToPOHubBy) + "||" + Convert.ToString(Contact2No), "", Subject, Convert.ToString(message), CompanyNo, false);
                    message = null;
                    servic = null;

                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Result", intResult);
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }

        }

        /// <summary>
        /// Update Customer Requirement With HUBRFQ
        /// </summary>
        public void UpdateCustomerRequirementWithHUBRFQ()
        {
            try
            {
                int? CompanyNo = GetFormValue_Int("CompanyNo");
                int? Bomid = GetFormValue_Int("BomId");
                int? CustomerRequirementNumber = GetFormValue_Int("CustomerRequirementNumber"); ;
                System.Boolean intUpdateStatus = CustomerRequirement.ClientBOMCustomerRequirementWithHUBRFQUpdate(
                      Convert.ToInt32(Bomid)
                      , CompanyNo
                      , CustomerRequirementNumber
                      , SessionManager.ClientID
                      );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", Convert.ToInt32(intUpdateStatus) > 0);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
    }
}
