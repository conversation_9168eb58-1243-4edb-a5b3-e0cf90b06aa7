﻿using System;
using System.Collections.Generic;
using System.Data;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class ProspectiveOfferProvider : DataAccess
    {
        private static ProspectiveOfferProvider _instance = null;
        public static ProspectiveOfferProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (ProspectiveOfferProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.ProspectiveOffers.ProviderType));
                return _instance;
            }
        }

        public ProspectiveOfferProvider()
        {
            this.ConnectionString = Globals.Settings.ProspectiveOffers.ConnectionString;
        }

        public abstract List<ProspectiveOfferDetails> DataListNugget(int? teamId,
                                                            int? divisionId,
                                                            int? loginId,
                                                            int? orderBy,
                                                            int? sortDir,
                                                            int? pageIndex,
                                                            int? pageSize,
                                                            string fileNameSearch,
                                                            string partSearch,
                                                            DateTime? dateUploadForm,
                                                            DateTime? dateUploadTo,
                                                            int? importedBy,
                                                            string supplierSearch);
        public abstract void SaveImportColumnHeader(string columnList, string insertColumnList, int loginId);
        public abstract void SaveBulkImportData(DataTable dtData, string originalFilename, string generatedFilename, int loginId);
        public abstract DataTable GetRawDataHeader(int loginId);
        public abstract DataTable GetProspectiveOfferRawData(int displayLength, int displayStart, int sortCol, string sortDir, int userId);
        public abstract DataTable GetSupplierMappedColumn(int supplierId);
        public abstract void SaveSupplierColumnMapping(int supplierId,
                                               string manufacturer,
                                               string part,
                                               string quantity,
                                               string price,
                                               string description,
                                               string alterPart,
                                               string datecode,
                                               string product,
                                               string package,
                                               string rohs,
                                               string supplierPart,
                                               int currencyNo,
                                               int loginNo);
        public abstract DataTable ValidateImportData(int supplierId, int loginId);
        public abstract int ImportProspectiveOffers(int supplierId, int loginId, out string outputMessage);
        public abstract ProspectiveOfferDetails GetStatus(int proId);
        public abstract List<ProspectiveOffersLogs> GetProspectiveOffersLogs(int prospectiveOfferLineId);
        
        public abstract ProspectiveOfferLinesOffer GetProspectiveOfferLineByID(int prospectiveOfferLineId);

        public abstract List<ProspectiveOffersLogs> GetProspectiveOffersLogsSentDate(int prospectiveOfferLineId, List<int> customerRequirementId);

        public abstract List<ProspectiveOfferLines> GetProspectiveOfferLines(int proId, int curPage, int rpp);
        public abstract ProspectiveOfferLines GetProspectiveOfferLineById(int lineId);
        public abstract List<ProspectiveOfferLines> GetGTOffersDetail(int proId, List<int> proLineIds, int monthRange, int minOfferQty, int curPage, int rpp);
        public abstract void CloneHUBRFQOffer(int proId, int bomId, int existingGTId, int newCusReqId);
        public abstract int CloneCustomerRequirement(int existingGTId, int currencyNo);
        public abstract ProspectiveOfferCustomerRequirement GetExistingHUBRFQ(int gtOfferId);
        public abstract int GetNewHUBRFQ(int proId, int proLineId, int gtId, int bomId, int? clientId);
        public abstract void UpdateProspectiveOfferLine(int proId, List<int> proLineIds, List<ProspectiveOfferLines> gtOfferIds);
        public abstract void UpdateProspectiveOfferManufacturer(int proId, int proLineId, int manufacturerNo);
        public abstract void InsertProspectiveOfferLogs(int proLineId, int customerRequirementId);
    }
}
