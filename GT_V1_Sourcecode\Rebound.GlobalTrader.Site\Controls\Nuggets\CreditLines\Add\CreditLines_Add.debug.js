///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 30.03.2010:
// - remove check for ROHS being entered
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.initializeBase(this, [element]);
    this._intCreditID = 0;
    this._intLineID = 0;
    this._intCustomerRMALineID = null;
    this._intInvoiceLineID = null;
    this._intServiceID = null;
    this._strCustomerName = "";
    this._intCurrencyNo = 0;
    this._strCurrencyCode = "";
    this._dtmCreditDate = "";
    this._dtmReferenceDate = "";
    this._strInvoiceNumber = "";
    this._strCRMANumber = "";
    this._aryFirstTimeSearched = [];
    this._dblCurrencyRateToBase = 1;
    this._isClientInvoice = false;
    this._intClientInvoiveID = null;
    this._InvoiceNumber = 0;
    this._intClientInvoiceLineID = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.prototype = {

    get_strCustomerName: function () { return this._strCustomerName; }, set_strCustomerName: function (v) { if (this._strCustomerName !== v) this._strCustomerName = v; },
    get_intCreditID: function () { return this._intCreditID; }, set_intCreditID: function (v) { if (this._intCreditID !== v) this._intCreditID = v; },
    get_ibtnContinue: function () { return this._ibtnContinue; }, set_ibtnContinue: function (v) { if (this._ibtnContinue !== v) this._ibtnContinue = v; },
    get_ibtnContinue_Footer: function () { return this._ibtnContinue_Footer; }, set_ibtnContinue_Footer: function (v) { if (this._ibtnContinue_Footer !== v) this._ibtnContinue_Footer = v; },
    get_radSelectSource: function () { return this._radSelectSource; }, set_radSelectSource: function (v) { if (this._radSelectSource !== v) this._radSelectSource = v; },
    get_trSourceFromCustomerRMA: function () { return this._trSourceFromCustomerRMA; }, set_trSourceFromCustomerRMA: function (v) { if (this._trSourceFromCustomerRMA !== v) this._trSourceFromCustomerRMA = v; },
    get_ctlSourceFromCustomerRMA: function () { return this._ctlSourceFromCustomerRMA; }, set_ctlSourceFromCustomerRMA: function (v) { if (this._ctlSourceFromCustomerRMA !== v) this._ctlSourceFromCustomerRMA = v; },
    get_trSourceFromInvoice: function () { return this._trSourceFromInvoice; }, set_trSourceFromInvoice: function (v) { if (this._trSourceFromInvoice !== v) this._trSourceFromInvoice = v; },
    get_ctlSourceFromInvoice: function () { return this._ctlSourceFromInvoice; }, set_ctlSourceFromInvoice: function (v) { if (this._ctlSourceFromInvoice !== v) this._ctlSourceFromInvoice = v; },
    get_trSourceFromService: function () { return this._trSourceFromService; }, set_trSourceFromService: function (v) { if (this._trSourceFromService !== v) this._trSourceFromService = v; },
    get_ctlSourceFromService: function () { return this._ctlSourceFromService; }, set_ctlSourceFromService: function (v) { if (this._ctlSourceFromService !== v) this._ctlSourceFromService = v; },
    get_lblCurrency_Price: function () { return this._lblCurrency_Price; }, set_lblCurrency_Price: function (v) { if (this._lblCurrency_Price !== v) this._lblCurrency_Price = v; },
    get_lblLandedCost: function () { return this._lblLandedCost; }, set_lblLandedCost: function (v) { if (this._lblLandedCost !== v) this._lblLandedCost = v; },
    get_arySources: function () { return this._arySources; }, set_arySources: function (v) { if (this._arySources !== v) this._arySources = v; },
    get_tblLines: function () { return this._tblLines; }, set_tblLines: function (v) { if (this._tblLines !== v) this._tblLines = v; },

    get_trSourceFromClientInvoice: function () { return this._trSourceFromClientInvoice; }, set_trSourceFromClientInvoice: function (v) { if (this._trSourceFromClientInvoice !== v) this._trSourceFromClientInvoice = v; },
    //	get_ctlSourceFromClientInvoice: function() { return this._ctlSourceFromClientInvoice; }, 	set_ctlSourceFromClientInvoice: function(v) { if (this._ctlSourceFromClientInvoice !== v)  this._ctlSourceFromClientInvoice = v; }, 

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.callBaseMethod(this, "initialize");
        this._tblLines.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
        this.addShown(Function.createDelegate(this, this.formShown));

    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlMultiStep) this._ctlMultiStep.dispose();
        if (this._ctlSourceFromInvoice) this._ctlSourceFromInvoice.dispose();
        if (this._ctlSourceFromCustomerRMA) this._ctlSourceFromCustomerRMA.dispose();
        if (this._ctlSourceFromService) this._ctlSourceFromService.dispose();
        if (this._tblLines) this._tblLines.dispose();
        //if (this._ctlSourceFromClientInvoice) this._ctlSourceFromClientInvoice.dispose();
        if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
        if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
        if (this.getFieldControl("ctlLandedCost")) $clearHandlers(this.getFieldControl("ctlLandedCost"));
        this._radSelectSource = null;
        this._ibtnContinue = null;
        this._ibtnContinue_Footer = null;
        this._trSourceFromCustomerRMA = null;
        this._ctlSourceFromCustomerRMA = null;
        this._trSourceFromService = null;
        this._ctlSourceFromService = null;
        this._trSourceFromInvoice = null;
        this._ctlSourceFromInvoice = null;
        this._lblCurrency_Price = null;
        this._lblLandedCost = null;
        this._aryFirstTimeSearched = null;
        this._intCreditID = null;
        this._intLineID = null;
        this._intCustomerRMALineID = null;
        this._intInvoiceLineID = null;
        this._intServiceID = null;
        this._strCustomerName = null;
        this._intCurrencyNo = null;
        this._strCurrencyCode = null;
        this._dtmCreditDate = null;
        this._dtmReferenceDate = null;
        this._strInvoiceNumber = null;
        this._strCRMANumber = null;
        this._dblCurrencyRateToBase = null;
        this._trSourceFromClientInvoice = null
        //this._ctlSourceFromClientInvoice=null;
        this._isClientInvoice = null;
        this._ClInvoice = null;
        this._tblLines = null;
        this._intClientInvoiveID = null;
        this._intClientInvoiceLineID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.callBaseMethod(this, "dispose");
    },

    formShown: function () {


        if (this._blnFirstTimeShown) {
            //form events
            this.addSave(Function.createDelegate(this, this.saveClicked));

            //buttons
            var fnContinue = Function.createDelegate(this, this.continueClicked);
            $R_IBTN.addClick(this._ibtnContinue, fnContinue);
            $R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);

            //other controls
            this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));
            this._ctlSourceFromCustomerRMA.addItemSelected(Function.createDelegate(this, this.selectCustomerRMALineItem));
            this._ctlSourceFromInvoice.addItemSelected(Function.createDelegate(this, this.selectInvoiceLineItem));
            this._ctlSourceFromService.addItemSelected(Function.createDelegate(this, this.selectServiceItem));
            //this._ctlSourceFromClientInvoice.addItemSelected(Function.createDelegate(this, this.selectClientInvoiceLineItem));
            $addHandler(this.getFieldControl("ctlLandedCost"), "change", Function.createDelegate(this, this.landedCostChanged));

            //data
            this._strPathToData = "controls/Nuggets/CreditLines";
            this._strDataObject = "CreditLines";
        }
        this.resetSteps();
        this.setFormFieldsToDefaults();

       // this.ClientInvoice();


    },

    tbl_SelectedIndexChanged: function () {
        this._intInvoiceLineID = this._tblLines._varSelectedValue;
        // alert(this._intClientInvoiceLineID);
        // return;
        this.continueClicked();
        this.fillData_ClientInvoiceLine();
        $R_IBTN.enableButton(this._ibtnSave, true);
        $R_IBTN.enableButton(this._ibtnSave_Footer, true);
        this.showField("ctlService", false);
        this.showField("ctlCustomer", false);
    },

    ClientInvoice: function () {
        if (Boolean.parse(this._isClientInvoice) == true) {
            this._ClInvoice = "CLIENTINVOICE";
            this.GetClientCredit();
            this.continueClicked();
            //$R_FN.showElement(this._trSourceFromClientInvoice, true);		
        }
    },
    landedCostChanged: function () {
        this.setLandedCostInCurrency($R_FN.formatCurrency(this.getFieldValue("ctlLandedCost") * this._dblCurrencyRateToBase, this._strCurrencyCode));
    },

    setLandedCostInCurrency: function (strCost) {
        $R_FN.setInnerHTML(this._lblLandedCost, String.format("({0})", strCost));
    },

    setFieldsFromHeader: function (strCreditNumber, strCustomerName, intCurrencyNo, strCurrencyCode, strCreditDate, strInvoiceNumber, strCRMANumber, strReferenceDate, isClientInvoice, InvoiceNumber,intClientInvoiceLineNo) {

        $R_FN.setInnerHTML(this._lblCurrency_Price, strCurrencyCode);
        this.setFieldValue("ctlCredit", strCreditNumber);
        this.setFieldValue("ctlCustomer", strCustomerName);
        this._strCustomerName = strCustomerName;
        this._intCurrencyNo = intCurrencyNo;
        this._strCurrencyCode = strCurrencyCode;
        this._dtmCreditDate = strCreditDate;
        this._dtmReferenceDate = strReferenceDate;
        this._strInvoiceNumber = strInvoiceNumber;
        this._strCRMANumber = strCRMANumber;
        this._isClientInvoice = isClientInvoice;
        this._InvoiceNumber = InvoiceNumber;
        this._intClientInvoiceLineID = intClientInvoiceLineNo;
    },

    doInitialSearch: function () {
        switch (this._strSourceSelected) {
            case "CRMA":
                if (!this._aryFirstTimeSearched[0]) this.initialCRMASearch();
                this._aryFirstTimeSearched[0] = true;
                break;
            case "INVOICE":
                if (!this._aryFirstTimeSearched[1]) this.initialInvoiceSearch();
                this._aryFirstTimeSearched[1] = true;
                break;
            case "SERVICE":
                if (!this._aryFirstTimeSearched[2]) this.initialServiceSearch();
                this._aryFirstTimeSearched[2] = true;
                break;

            case "CLIENTINVOICE":
                if (!this._aryFirstTimeSearched[3]) this.initialClientInvoiceSearch();
                this._aryFirstTimeSearched[3] = true;
                break;

        }
    },

    initialCRMASearch: function () {
        $R_FN.showElement(this._trSourceFromClientInvoice, false);

        this._ctlSourceFromCustomerRMA.setFieldValue("ctlCompany", this._strCustomerName);
        if (this._strCRMANumber) this._ctlSourceFromCustomerRMA.setFieldValue("ctlCRMANo", this._strCRMANumber);
        this._ctlSourceFromCustomerRMA.getData();
    },

    initialInvoiceSearch: function () {
        $R_FN.showElement(this._trSourceFromClientInvoice, false);

        this._ctlSourceFromInvoice.setFieldValue("ctlCompany", this._strCustomerName);
        if (this._strInvoiceNumber) this._ctlSourceFromInvoice.setFieldValue("ctlInvoiceNo", this._strInvoiceNumber);
        this._ctlSourceFromInvoice.getData();
    },

    initialClientInvoiceSearch: function () {
        // this._ctlSourceFromInvoice.setFieldValue("ctlCompany", this._strCustomerName);
        //if (this._strInvoiceNumber) this._ctlSourceFromInvoice.setFieldValue("ctlInvoiceNo", this._strInvoiceNumber);
        // this._ctlSourceFromClientInvoice.getData();
    },

    initialServiceSearch: function () {
        $R_FN.showElement(this._trSourceFromClientInvoice, false);

        this._ctlSourceFromService.setFieldValue("ctlName", "%");
        this._ctlSourceFromService.getData();
    },

    continueClicked: function () {
        this.nextStep();
    },

    findWhichTypeSelected: function () {
        for (var i = 0; i < this._arySources.length; i++) {
            var rad = $get(String.format("{0}_{1}", this._radSelectSource.id, i));
            if (rad.checked) return this._arySources[i];
        }
    },

    stepChanged: function () {
        //alert("hi");
        //
        this._strSourceSelected = this.findWhichTypeSelected();
        var intStep = this._ctlMultiStep._intCurrentStep;

        if (Boolean.parse(this._isClientInvoice) == true && this._strSourceSelected == "INVOICE") {
            this._strSourceSelected = "CLIENTINVOICE";
            //intStep = 2;
        }

        var blnShowContinue = intStep == 1 || (intStep == 1 && this._strSourceSelected != "NEW");
        $R_IBTN.showButton(this._ibtnContinue, blnShowContinue);
        $R_IBTN.showButton(this._ibtnContinue_Footer, blnShowContinue);
        $R_IBTN.enableButton(this._ibtnSave, intStep == 3);
        $R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 3);
        if (intStep == 2) {
            if (this._strSourceSelected == "CRMA") this._ctlSourceFromCustomerRMA.resizeColumns();
            $R_FN.showElement(this._trSourceFromCustomerRMA, this._strSourceSelected == "CRMA");
            if (this._strSourceSelected == "INVOICE" && !Boolean.parse(this._isClientInvoice)) this._ctlSourceFromInvoice.resizeColumns();
            $R_FN.showElement(this._trSourceFromInvoice, this._strSourceSelected == "INVOICE" && !Boolean.parse(this._isClientInvoice));

            if (this._strSourceSelected == "CLIENTINVOICE" && Boolean.parse(this._isClientInvoice)) //
            {
               // alert("CLIENTINVOICE");
                //this._ctlSourceFromClientInvoice.resizeColumns();
                this._ClInvoice = "CLIENTINVOICE";
                $R_FN.showElement(this._trSourceFromClientInvoice, this._strSourceSelected == "CLIENTINVOICE" && Boolean.parse(this._isClientInvoice));
                this.GetClientCredit();
            }


            if (this._strSourceSelected == "SERVICE") this._ctlSourceFromService.resizeColumns();
            $R_FN.showElement(this._trSourceFromService, this._strSourceSelected == "SERVICE");
            this.doInitialSearch();
        }
        if (intStep == 3) {
            this.showField("ctlService", this._strSourceSelected == "SERVICE");
            this.showField("ctlServiceDescription", this._strSourceSelected == "SERVICE");
            this.showField("ctlPartNo", this._strSourceSelected != "SERVICE");
            this.showField("ctlProduct", this._strSourceSelected != "SERVICE");
            this.showField("ctlPackage", this._strSourceSelected != "SERVICE");
            this.showField("ctlROHSStatus", this._strSourceSelected != "SERVICE");
            this.showField("ctlManufacturer", this._strSourceSelected != "SERVICE");
            this.showField("ctlCustomerPart", this._strSourceSelected != "SERVICE");
            this.showField("ctlDateCode", this._strSourceSelected != "SERVICE");
            this.fillData();
        }
    },

    getDropDownsData: function () {
       // this.getFieldDropDownData("ctlProduct");
        //this.getFieldDropDownData("ctlPackage");
        this.getFieldDropDownData("ctlROHSStatus");
    },

    selectCustomerRMALineItem: function () {
        this._intCustomerRMALineID = this._ctlSourceFromCustomerRMA.getSelectedID();
        this.continueClicked();
    },

    selectInvoiceLineItem: function () {
        this._intInvoiceLineID = this._ctlSourceFromInvoice.getSelectedID();
        this.continueClicked();
    },

    selectClientInvoiceLineItem: function () {
        //	this._intInvoiceLineID = this._ctlSourceFromClientInvoice.getSelectedID();
        this.continueClicked();
    },

    selectServiceItem: function () {
        this._intServiceID = this._ctlSourceFromService.getSelectedID();
        this.continueClicked();
    },

    fillData: function () {
        switch (this._strSourceSelected) {
            case "CRMA": this.fillData_CustomerRMALine(); break;
            case "INVOICE": this.fillData_InvoiceLine(); break;
            case "SERVICE": this.fillData_Service(); break;
        }
    },

    fillDataError: function (args) {
        this.showFillDataFieldsLoading(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },


    GetClientCredit: function () {
        //alert(this._InvoiceNumber);
        this.setFormFieldsToDefaults();
        this.showFillDataFieldsLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetClientInvoice");
        //	obj.addParameter("id", this._intClientCreditID);	
        obj.addParameter("id", this._InvoiceNumber);
        obj.addParameter("LineNo", this._intClientInvoiceLineID);
        obj.addDataOK(Function.createDelegate(this, this.GetClientCreditOK));
        obj.addError(Function.createDelegate(this, this.fillDataError));
        obj.addTimeout(Function.createDelegate(this, this.fillDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    GetClientCreditOK: function (args) {
        var result = args._result;
        this._tblLines.clearTable();
        if (result.Lines) {
            for (var i = 0; i < result.Lines.length; i++) {
                var row = result.Lines[i];
                var aryData = [
				row.Num,
				//$R_FN.setCleanTextValue(row.CMName),
				$R_FN.writePartNo(row.Part, row.ROHS),
				$R_FN.setCleanTextValue(row.Date),
				row.Price,
				row.Quantity,
				$R_FN.setCleanTextValue(row.GoodsInNumber),
				$R_FN.setCleanTextValue(row.InternalPurchaseOrderNumber),
                $R_FN.setCleanTextValue(row.DebitNumber)
                ];

                this._tblLines.addRow(aryData, row.ID, false);

            }
        }


    },

    fillData_CustomerRMALine: function () {
        this.setFormFieldsToDefaults();
        this.showFillDataFieldsLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetCRMALineForNew");
        obj.addParameter("id", this._intCustomerRMALineID);
        obj.addParameter("CreditCurrencyNo", this._intCurrencyNo);
        obj.addParameter("CreditCurrencyCode", this._strCurrencyCode);
        obj.addParameter("CreditDate", this._dtmReferenceDate);
        obj.addDataOK(Function.createDelegate(this, this.fillData_CustomerRMALineOK));
        obj.addError(Function.createDelegate(this, this.fillDataError));
        obj.addTimeout(Function.createDelegate(this, this.fillDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    fillData_CustomerRMALineOK: function (args) {
        var res = args._result;
        this.setFieldValue("ctlPartNo", res.Part);
        this.setFieldValue("ctlDateCode", res.DC);
        this.setFieldValue("ctlCustomerPart", res.CustomerPart);
        this.setFieldValue("ctlPrice", res.Price);
        this.setFieldValue("ctlQuantity", res.Quantity);
        this.setFieldValue("ctlManufacturer", res.ManufacturerNo, null, $R_FN.setCleanTextValue(res.Manufacturer));
        this.setFieldValue("ctlProduct", res.ProductNo, null, $R_FN.setCleanTextValue(res.ProductDescription));
        //this.setFieldValue("ctlPackage", res.PackageNo);
        this.setFieldValue("ctlPackage", res.PackageNo, null, $R_FN.setCleanTextValue(res.PackageDescription));
        this.setFieldValue("ctlROHSStatus", res.ROHS);
        this.setFieldValue("ctlLandedCost", res.LandedCost);
        if (res.LandedCostConverted) this.setLandedCostInCurrency(res.LandedCostConverted);
        this.showFillDataFieldsLoading(false);
        this.getDropDownsData();
    },

    fillData_InvoiceLine: function () {
        this.setFormFieldsToDefaults();
        this.showFillDataFieldsLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetInvoiceLineForNew");
        obj.addParameter("id", this._intInvoiceLineID);
        obj.addParameter("CreditCurrencyNo", this._intCurrencyNo);
        obj.addParameter("CreditCurrencyCode", this._strCurrencyCode);
        obj.addParameter("CreditDate", this._dtmReferenceDate);
        obj.addDataOK(Function.createDelegate(this, this.fillData_InvoiceLineOK));
        obj.addError(Function.createDelegate(this, this.fillDataError));
        obj.addTimeout(Function.createDelegate(this, this.fillDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    fillData_InvoiceLineOK: function (args) {
        var res = args._result;
        this.setFieldValue("ctlPartNo", res.Part);
        this.setFieldValue("ctlDateCode", res.DC);
        this.setFieldValue("ctlCustomerPart", res.CustomerPart);
        this.setFieldValue("ctlPrice", res.Price);
        this.setFieldValue("ctlQuantity", res.Qty);
        this.setFieldValue("ctlManufacturer", res.ManufacturerNo, null, res.Manufacturer);
        this.setFieldValue("ctlProduct", res.ProductNo, null, $R_FN.setCleanTextValue(res.ProductDescription));
        //this.setFieldValue("ctlPackage", res.PackageNo);
        this.setFieldValue("ctlPackage", res.PackageNo, null, $R_FN.setCleanTextValue(res.PackageDescription));
        this.setFieldValue("ctlROHSStatus", res.ROHS);
        this.setFieldValue("ctlLandedCost", res.LandedCost);
        this._dblCurrencyRateToBase = res.CurrencyRate;
        if (res.LandedCostConverted) this.setLandedCostInCurrency(res.LandedCostConverted);
        this.showFillDataFieldsLoading(false);
        this.getDropDownsData();
    },

    fillData_Service: function () {
        this.setFormFieldsToDefaults();
        this.showFillDataFieldsLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetServiceForNew");
        obj.addParameter("id", this._intServiceID);
        obj.addParameter("CreditCurrencyNo", this._intCurrencyNo);
        obj.addParameter("CreditCurrencyCode", this._strCurrencyCode);
        obj.addParameter("CreditDate", this._dtmReferenceDate);
        obj.addDataOK(Function.createDelegate(this, this.fillData_ServiceOK));
        obj.addError(Function.createDelegate(this, this.fillDataError));
        obj.addTimeout(Function.createDelegate(this, this.fillDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    fillData_ServiceOK: function (args) {
        var res = args._result;
        this.setFieldValue("ctlService", res.ServiceName);
        this.setFieldValue("ctlServiceDescription", res.ServiceDescription);
        this.setFieldValue("ctlPrice", res.Price);
        this.setFieldValue("ctlQuantity", 1);
        this.setFieldValue("ctlLandedCost", res.Cost);
        if (res.CostConverted) this.setLandedCostInCurrency(res.CostConverted);
        this._dblCurrencyRateToBase = res.CurrencyRate;
        this.showFillDataFieldsLoading(false);
        this.getDropDownsData();
    },

    showFillDataFieldsLoading: function (bln) {
        this.showFieldLoading("ctlPartNo", bln);
        this.showFieldLoading("ctlDateCode", bln);
        this.showFieldLoading("ctlPrice", bln);
        this.showFieldLoading("ctlQuantity", bln);
        this.showFieldLoading("ctlManufacturer", bln);
        this.showFieldLoading("ctlProduct", bln);
        this.showFieldLoading("ctlPackage", bln);
        this.showFieldLoading("ctlROHSStatus", bln);
        this.showFieldLoading("ctlCustomerPart", bln);
        this.showFieldLoading("ctlLandedCost", bln);
    },

    fillData_ClientInvoiceLine: function () {
        this.setFormFieldsToDefaults();
        this.showFillDataFieldsLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetClientInvoiceLineForNew");
        obj.addParameter("id", this._intInvoiceLineID);
        obj.addParameter("CreditCurrencyNo", this._intCurrencyNo);
        obj.addParameter("CreditCurrencyCode", this._strCurrencyCode);
        obj.addParameter("CreditDate", this._dtmReferenceDate);
        obj.addDataOK(Function.createDelegate(this, this.fillData_ClientInvoiceLineOK));
        obj.addError(Function.createDelegate(this, this.fillDataError));
        obj.addTimeout(Function.createDelegate(this, this.fillDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    fillData_ClientInvoiceLineOK: function (args) {
        var res = args._result;
        this.setFieldValue("ctlPartNo", res.Part);
        this.setFieldValue("ctlDateCode", res.DateCode);
        this.setFieldValue("ctlCustomerPart", res.SupplierPart);
        this.setFieldValue("ctlPrice", res.Price);
        this.setFieldValue("ctlQuantity", res.QtyReceived);
        this.setFieldValue("ctlManufacturer", res.ManufacturerNo, null, res.ManufacturerName);
        this.setFieldValue("ctlProduct", res.ProductNo, null, $R_FN.setCleanTextValue(res.ProductDescription));
        //this.setFieldValue("ctlPackage", res.PackageNo);
        this.setFieldValue("ctlPackage", res.PackageNo, null, $R_FN.setCleanTextValue(res.PackageDescription));
        this.setFieldValue("ctlROHSStatus", res.ROHS);
        this.setFieldValue("ctlLandedCost", res.Landedcost);
        this._dblCurrencyRateToBase = res.CurrencyRate;
        if (res.LandedCostConverted) this.setLandedCostInCurrency(res.LandedCostConverted);
        this.showFillDataFieldsLoading(false);
        this.getDropDownsData();
    },

    saveClicked: function () {
        this.resetFormFields();
        if (this.validateForm()) this.saveEdit();
    },

    validateForm: function () {
        var blnOK = true;
        if (this._strSourceSelected == "SERVICE") {
            if (!this.checkFieldEntered("ctlService")) blnOK = false;
        } else {
            if (!this.checkFieldEntered("ctlPartNo")) blnOK = false;
        }
        if (!this.checkFieldEntered("ctlQuantity")) blnOK = false;
        if (!this.checkFieldNumeric("ctlQuantity")) blnOK = false;
        if (!this.checkFieldEntered("ctlPrice")) blnOK = false;
        if (!this.checkFieldNumeric("ctlPrice")) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    saveEdit: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        //alert(this._intInvoiceLineID)
        //return;
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("AddNew");
        obj.addParameter("id", this._intCreditID);
        obj.addParameter("LineIsService", this._strSourceSelected == "SERVICE");
        if (this._strSourceSelected == "SERVICE") {
            obj.addParameter("ServiceNo", this._intServiceID);
            obj.addParameter("Service", this.getFieldValue("ctlService"));
            obj.addParameter("ServiceDescription", this.getFieldValue("ctlServiceDescription"));
            if (Boolean.parse(this._isClientInvoice)) obj.addParameter("ClientInvLineNo", this._intClientInvoiceLineID);
        } else {
            if (this._ClInvoice == "CLIENTINVOICE") {
                obj.addParameter("ClientInvoice", "CLIENTINVOICE");
                obj.addParameter("ClientInvLineNo", this._intClientInvoiceLineID);
            }
            obj.addParameter("InvoiceLineNo", this._intInvoiceLineID);
            obj.addParameter("Part", this.getFieldValue("ctlPartNo"));
            obj.addParameter("MfrNo", this.getFieldValue("ctlManufacturer"));
            obj.addParameter("DateCode", this.getFieldValue("ctlDateCode"));
            obj.addParameter("PackageNo", this.getFieldValue("ctlPackage"));
            obj.addParameter("ProductNo", this.getFieldValue("ctlProduct"));
            obj.addParameter("CustomerPart", this.getFieldValue("ctlCustomerPart"));
            obj.addParameter("ROHS", this.getFieldValue("ctlROHSStatus"));
        }
        obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
        obj.addParameter("LandedCost", this.getFieldValue("ctlLandedCost"));
        obj.addParameter("Price", this.getFieldValue("ctlPrice"));
        obj.addParameter("CustomerRMALineNo", this._intCustomerRMALineID);
        obj.addParameter("Taxable", this.getFieldValue("ctlTaxable"));
        obj.addParameter("LineNotes", this.getFieldValue("ctlLineNotes"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditOK: function (args) {
        if (args._result.Result == true) {
            this._intLineID = args._result.NewID;
            this.onSaveComplete();
        } else {
            this.saveEditError(args);
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
