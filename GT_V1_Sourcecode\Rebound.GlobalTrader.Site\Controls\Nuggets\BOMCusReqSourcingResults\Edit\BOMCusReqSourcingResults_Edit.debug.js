///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------
//Marker     changed by      date         Remarks
//[001]      Aashu          07/06/2018     Added supplier warranty field
//[002]      Aashu          14/08/2018     REB-12322 : A tick box to recomond test the parts from HUB side.
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intSourcingResultID = -1;
    this._blnPOHub = false;
    this._PackageT = "";
    this._ProductT = "";
    this._ClientNo = -1;
    this._SupplierManufacturerName = "";
    this._supDateCode = "";
    this._intCurrencyNo = -1;
    this._floatSupPrice = 0;
    this._strActualCurCode = "";
    this._floatUpliftPrice = 0;
    this._ActBuyCurrencyNo = -1;
    this._OfferQuantity = 0;
    //[001] start
    this._NonPreferredCompany = true;
    //[001] end
    //[002] start
    this._isTestingRecommended = false;
    //[002] end
    this._intGlobalClientNo = -1;
    this._blnISAS6081Required = false;
    this._CountryNo = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit.prototype = {

    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (value) { if (this._intCustomerRequirementID !== value) this._intCustomerRequirementID = value; },
    get_lblUpliftPrice: function () { return this._lblUpliftPrice; }, set_lblUpliftPrice: function (value) { if (this._lblUpliftPrice !== value) this._lblUpliftPrice = value; },
    get_lblProduct: function () { return this._lblProduct; }, set_lblProduct: function (value) { if (this._lblProduct !== value) this._lblProduct = value; },
    get_lblPackage: function () { return this._lblPackage; }, set_lblPackage: function (value) { if (this._lblPackage !== value) this._lblPackage = value; },
    get_lblManufacturer: function () { return this._lblManufacturer; }, set_lblManufacturer: function (value) { if (this._lblManufacturer !== value) this._lblManufacturer = value; },
    get_lblClientPrice: function () { return this._lblClientPrice; }, set_lblClientPrice: function (value) { if (this._lblClientPrice !== value) this._lblClientPrice = value; },
    get_lblSupplierPrice: function () { return this._lblSupplierPrice; }, set_lblSupplierPrice: function (value) { if (this._lblSupplierPrice !== value) this._lblSupplierPrice = value; },
    get_lblQty: function () { return this._lblQty; }, set_lblQty: function (value) { if (this._lblQty !== value) this._lblQty = value; },
    //[001] start
    get_NonPreferredCompany: function () { return this._NonPreferredCompany; }, set_NonPreferredCompany: function (value) { if (this._NonPreferredCompany !== value) this._NonPreferredCompany = value; },
    //[001] end

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));

    },

    formShown: function () {
        this.showField("ctlCompanyNamelbl", false);
        this.showField("ctlCompanyNameddl", false);
        window.scroll(0, 1);
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").hide();
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            $find(this.getField("ctlLinkCurrency").ControlID).addChanged(Function.createDelegate(this, this.updateCurrency));

            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCompanyNamelbl_pnlFieldControls").append("<a id='linkReselect' class='quickSearchReselect' href='javascript:void(0);'>[ Reselect ]</a>");


            document.getElementById("linkReselect").addEventListener("click", Function.createDelegate(this, this.GetLocationDropDown));
        }
        this.showField("ctlPartWatchMatch", false);
        this.setFormFieldsToDefaults();
        this.getFieldControl("ctlCountryOfManufacture")._intGlobalLoginClientNo = this._intGlobalClientNo;
        this.getFieldDropDownData("ctlCountryOfManufacture");
        this.getFieldDropDownData("ctlROHS");

        if (this._blnISAS6081Required == true) {
            this.showField("ctlTypeOfSupplier", true);
            this.showField("ctlReasonForSupplier", true);
            this.showField("ctlRiskOfSupplier", true);
            this.getFieldDropDownData("ctlTypeOfSupplier");
            this.getFieldDropDownData("ctlReasonForSupplier");
            this.getFieldDropDownData("ctlRiskOfSupplier");

            if (!$("#spnTypeOfSupplier1").hasClass("requiredField")) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlTypeOfSupplier_tdTitle").append("<span id='spnTypeOfSupplier1' class='requiredField'>*</span>");
            }
            if (!$("#spnReasonForSupplier1").hasClass("requiredField")) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlReasonForSupplier_tdTitle").append("<span id='spnReasonForSupplier1' class='requiredField'>*</span>");
            }
            if (!$("#spnRiskOfSupplier1").hasClass("requiredField")) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlRiskOfSupplier_tdTitle").append("<span id='spnRiskOfSupplier1' class='requiredField'>*</span>");
            }
            if (!$("#spnLocation1").hasClass("requiredField")) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCompanyNameddl_tdTitle").append("<span id='spnLocation1' class='requiredField'>*</span>");
            }
        } else {
            this.showField("ctlTypeOfSupplier", false);
            this.showField("ctlReasonForSupplier", false);
            this.showField("ctlRiskOfSupplier", false);
        }
        this.getFieldControl("ctlLinkCurrency")._intCustomerClientNo = this._ClientNo;
        this.getFieldControl("ctlLinkCurrency")._intBuyCurrencyNo = this._ActBuyCurrencyNo;
        //alert(this._ActBuyCurrencyNo);
        this.getFieldDropDownData("ctlLinkCurrency");
        //        this.showField("ctlSupplier", this._blnPOHub);
        //        this.showField("ctlSupplier_Label", !this._blnPOHub);
        // this.showField("ctlProduct", !this._blnPOHub);        
        this.showField("ctlSupplierPrice", this._blnPOHub);
        this.showField("ctlPriority", false);

        this.showField("ctlPrice", this._blnPOHub);
        // this.showField("ctlCurrency", false);
        this.showField("ctlPriceClient", !this._blnPOHub);
        this.showField("ctlLinkCurrency", this._blnPOHub);
        this.setFieldValue("ctlDateCode", $R_FN.setCleanTextValue(this._supDateCode));
        //[002] start  
        this.setFieldValue("ctlTestingRecommended", this._isTestingRecommended);
        $find(this.getField("ctlCountryOfManufacture").ControlID).addChanged(Function.createDelegate(this, this.findCountryRiskData));
        //[002] end
        this.getData();
        $find(this.getField("ctlCountryOfManufacture").ControlID).addGotDataComplete(Function.createDelegate(this, this.findCountryRiskData));

        //hide SellPriceLessReason textbox in DMCC side base on condition
        document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessLabel').style.display = "none";
        this.showField("ctlSellPriceLessReason", false);
        if (this._blnPOHub) {
            document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSupplierPrice_ctl04_lblSupplierPrice").setAttribute("onkeyup", String.format("$find(\"{0}\").focusOnPrice();", this._element.id));
            document.getElementById("ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlPrice_ctl04_txtPrice").setAttribute("onkeyup", String.format("$find(\"{0}\").focusOnPrice();", this._element.id));
        }
        if ($find(this.getField("ctlManufacturer").ControlID)) $find(this.getField("ctlManufacturer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getMfrNotes));


    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intCustomerRequirementID = null;
        this._intSourcingResultID = null;
        this._blnPOHub = null;
        this._lblClientPrice = null;
        this._lblSupplierPrice = null;
        this._intCurrencyNo = null;
        this._floatSupPrice = null;
        this._strActualCurCode = null;
        this._floatUpliftPrice = null;
        this._ActBuyCurrencyNo = null;
        this._lblQty = null;
        this._OfferQuantity = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit.callBaseMethod(this, "dispose");
    },

    getData: function () {
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("GetItem");
        obj.addParameter("id", this._intSourcingResultID);
        obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
        obj.addError(Function.createDelegate(this, this.finishShowForm));
        obj.addTimeout(Function.createDelegate(this, this.finishShowForm));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataComplete: function (args) {
        var res = args._result;
        this.setFieldValue("ctlPartNo", $R_FN.setCleanTextValue(res.Part));
        this.setFieldValue("ctlDateCode", $R_FN.setCleanTextValue(res.DateCode));
        if (res.Quantity > 0)
            this.setFieldValue("ctlQuantity", res.Quantity);
        else
            this.setFieldValue("ctlQuantity", "");
        this.setFieldValue("ctlPrice", res.Price);
        this.setFieldValue("ctlPriceClient", res.Price);
        //  this.setFieldValue("ctlCurrency", res.CurrencyNo);
        //this.setFieldValue("ctlPackage", res.PackageNo);
        this.setFieldValue("ctlPackage", res.PackageNo, null, $R_FN.setCleanTextValue(res.PackageDescription));
        //if (res.ProdInc) // Changes made to avoid blank product text when product is inactive (Suhail 19-12-2017)
        //    this.setFieldValue("ctlProduct", 0, null, "");
        //else
        this.setFieldValue("ctlProduct", res.ProductNo, null, $R_FN.setCleanTextValue(res.ProductDescription));

        this.setFieldValue("ctlManufacturer", res.MfrNo, null, $R_FN.setCleanTextValue(res.Mfr) + $R_FN.createAdvisoryNotesIcon(this._MfrAdvisoryNotes, 'margin-left-10'));
        this.setFieldValue("ctlOfferStatus", res.OfferStatus);
        this.setFieldValue("ctlROHS", res.ROHS);
        this.setFieldValue("ctlNotes", res.Notes);
        this.setFieldValue("ctlSupplier_Label", res.Supplier + $R_FN.createAdvisoryNotesIcon(res.SupplierAdvisoryNotes, 'margin-left-10'));
        //this.setUpLiftPrice(res.UPLiftPrice);
        this.setFieldValue("ctlEstimatedShippingCost", res.EstimatedShippingCostValue);
        this.setFieldValue("ctlDeliveryDate", res.DeliveryDate);

        this.setFieldValue("ctlSPQ", res.SPQ);
        this.setFieldValue("ctlLeadTime", res.LeadTime);
        this.setFieldValue("ctlROHSStatus", res.ROHSStatus);
        this.setFieldValue("ctlFactorySealed", res.FactorySealed);
        //this.setFieldValue("ctlMSL", res.MSL);
        this.setFieldValue("ctlMSL", res.MSLLevelNo);

        this.setFieldValue("ctlMOQ", res.SupplierMOQ);
        this.setFieldValue("ctlLTB", res.SupplierLTB);
        this.setFieldValue("ctlTQSA", res.SupplierTotalQSA);
        this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes));
        $R_FN.setInnerHTML(this._lblProduct, '(  ' + $R_FN.setCleanTextValue(this._ProductT) + '  )');
        $R_FN.setInnerHTML(this._lblPackage, '(  ' + $R_FN.setCleanTextValue(this._PackageT) + '  )');
        $R_FN.setInnerHTML(this._lblManufacturer, '(  ' + this._SupplierManufacturerName + '  )');
        //[qty req: 00000]
        $R_FN.setInnerHTML(this._lblQty, '[ Qty Req: ' + this._OfferQuantity + ' ]');
        this.setFieldValue("ctlLinkCurrency", res.CurrencyNo);
        this._intCurrencyNo = res.CurrencyNo;
        this._floatSupPrice = res.BuySupPrice;
        this._strActualCurCode = res.ActCurCode;
        this._floatUpliftPrice = res.Price;

        //        if (this._blnPOHub)
        //            
        //        else
        //            this.setFieldValue("ctlSupplier_Label", res.SupplierNo, null, $R_FN.setCleanTextValue(res.Supplier));

        this.setFieldValue("ctlSupplierPrice", res.SupPrice);
        $R_FN.setInnerHTML(this._lblClientPrice, res.CurrencyCode);
        $R_FN.setInnerHTML(this._lblSupplierPrice, res.CurrCodeForSupPrice);
        $R_FN.setInnerHTML(this._lblUpliftPrice, res.UpliftPriceCode);

        this.setFieldValue("ctlRegion", res.RegionNo);
        this.setFieldValue("ctlPriority", res.PriorityId);
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryOfOrigin").text('(  ' + this._IHSCountryOfOriginName + ' )');
        this.setFieldValue("ctlCountryOfManufacture", res.CountryOfOriginNo);
        if (res.CountryOfOriginNo != null && res.CountryOfOriginNo > 0) {
            this._intManufacturerNo = res.CountryOfOriginNo;
            this.getRsMfr(this._intManufacturerNo);
        }
        //[001] start
        this.setFieldValue("ctlSupplierWarranty", (res.SupplierWarranty > 0 ? res.SupplierWarranty : "0"));
        this.set_NonPreferredCompany(res.NonPreferredCompany);
        //[001] end
        if (res.PartWatchMatchHUBIPO == true) {
            this.showField("ctlPartWatchMatch", true);
            this.setFieldValue("ctlPartWatchMatch", res.PartWatchMatchHUBIPO);
        }
        else {
            this.showField("ctlPartWatchMatch", false);
        }
        this.setFieldValue("ctlTypeOfSupplier", res.TypeOfSupplierNo);
        this.setFieldValue("ctlReasonForSupplier", res.ReasonForSupplierNo);
        this.setFieldValue("ctlRiskOfSupplier", res.RiskOfSupplierNo);
        if (this._blnISAS6081Required == true) {
            if (res.IsCountryFound == true) {
                this.setFieldValue("ctlCompanyNamelbl", res.CountryName);
                this.showField("ctlCompanyNamelbl", true);
                this.showField("ctlCompanyNameddl", false);
                this._CountryNo = res.CountryNo;
            }
            else {
                this.showField("ctlCompanyNamelbl", false);
                this.showField("ctlCompanyNameddl", true);
                this.getFieldDropDownData("ctlCompanyNameddl");
            }
        }
        this.setFieldValue("ctlTestingRecommended", res.IsTestingRecommended);
        if (this._blnPOHub && res.Price < res.SupPrice) {
            this.showField("ctlSellPriceLessReason", true);
            this.setFieldValue("ctlSellPriceLessReason", res.SellPriceLessReason);
            document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessLabel').style.display = "";
        }

        this.finishShowForm();
        this.storeOriginalFieldValues();
    },

    finishShowForm: function () {
        this.showLoading(false);
        this.showInnerContent(true);
        $find(this.getField("ctlProduct").ControlID)._aut._intPOHubClientNo = this._ClientNo;
        //  this.getFieldControl("ctlProduct")._intPOHubClientNo =  this._ClientNo;
        // this.getFieldDropDownData("ctlSupplier");

        // this.getFieldDropDownData("ctlCurrency");
        //this.getFieldDropDownData("ctlPackage");
        // this.getFieldDropDownData("ctlProduct");
        this.getFieldDropDownData("ctlOfferStatus");
        this.getFieldDropDownData("ctlRegion");
        //this.getFieldDropDownData("ctlPriority");
        this.getFieldDropDownData("ctlMSL");
    },

    saveClicked: function () {
        if (!this.validateForm()) return;
        var strActionChangedFields = this.getChangedFields([
            "ctlPartNo", "ctlNotes", "ctlDateCode", "ctlManufacturer"
            , "ctlProduct", "ctlPackage", "ctlQuantity"
            , "ctlDeliveryDate", "ctlSupplierPrice", "ctlPrice"
            , "ctlRegion", "ctlEstimatedShippingCost"
        ]);
        //if no fields are changed close the form (so we don't create an extraneous Stock Log)
        this.getChangedFields(); //call again to check all fields this time, so that we ensure data is saved
        if (this._aryChangedFieldIDs.length == 0) {
            this.saveEditComplete({ _result: { Result: true } });
            return;
        }
        //code for log end
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intSourcingResultID);
        obj.addParameter("Quantity", this.getFieldValue("ctlQuantity"));
        obj.addParameter("Part", this.getFieldValue("ctlPartNo"));
        obj.addParameter("Manufacturer", this.getFieldValue("ctlManufacturer"));
        obj.addParameter("DateCode", this.getFieldValue("ctlDateCode"));
        obj.addParameter("Product", this.getFieldValue("ctlProduct"));
        obj.addParameter("Package", this.getFieldValue("ctlPackage"));
        if (this._blnPOHub)
            obj.addParameter("Price", this.getFieldValue("ctlPrice"));
        else
            obj.addParameter("Price", this.getFieldValue("ctlPriceClient"));
        // obj.addParameter("Currency", this.getFieldValue("ctlCurrency"));
        obj.addParameter("Supplier", 6);
        // obj.addParameter("Supplier", this.getFieldValue("ctlSupplier"));
        obj.addParameter("ROHS", this.getFieldValue("ctlROHS"));
        obj.addParameter("OfferStatus", this.getFieldValue("ctlOfferStatus"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("SUPPrice", this.getFieldValue("ctlSupplierPrice"));
        obj.addParameter("EstimatedShippingCost", this.getFieldValue("ctlEstimatedShippingCost"));
        obj.addParameter("DeliveryDate", this.getFieldValue("ctlDeliveryDate"));

        obj.addParameter("SPQ", this.getFieldValue("ctlSPQ"));
        obj.addParameter("LeadTime", this.getFieldValue("ctlLeadTime"));
        obj.addParameter("ROHSStatus", this.getFieldValue("ctlROHSStatus"));
        obj.addParameter("ROHSStatus", "");
        obj.addParameter("FactorySealed", this.getFieldValue("ctlFactorySealed"));
        obj.addParameter("MSL", this.getFieldValue("ctlMSL"));

        obj.addParameter("SupplierTotalQSA", this.getFieldValue("ctlTQSA"));
        obj.addParameter("SupplierMOQ", this.getFieldValue("ctlMOQ"));
        obj.addParameter("SupplierLTB", this.getFieldValue("ctlLTB"));
        obj.addParameter("Region", this.getFieldValue("ctlRegion"));
        obj.addParameter("Priority", this.getFieldValue("ctlPriority"));
        obj.addParameter("Currency", this.getFieldValue("ctlLinkCurrency"));
        //alert(this.getFieldDropDownExtraText("ctlLinkCurrency"));
        obj.addParameter("LinkCurrencyNo", this.getFieldDropDownExtraText("ctlLinkCurrency"));
        //[001] start
        obj.addParameter("SupplierWarranty", this.getFieldValue("ctlSupplierWarranty"));
        //[001] end
        //[002] start
        obj.addParameter("TestingRecommended", this.getFieldValue("ctlTestingRecommended"));
        //[002] end
        obj.addParameter("CountryOfOriginNo", this.getFieldValue("ctlCountryOfManufacture"));
        obj.addParameter("ChangedFields", strActionChangedFields);
        obj.addParameter("PartWatchMatch", this.getFieldValue("ctlPartWatchMatch"));
        obj.addParameter("TypeOfSupplier", this.getFieldValue("ctlTypeOfSupplier"));
        obj.addParameter("ReasonForSupplier", this.getFieldValue("ctlReasonForSupplier"));
        obj.addParameter("RiskOfSupplier", this.getFieldValue("ctlRiskOfSupplier"));
        if (this._CountryNo > 0) {
            obj.addParameter("CountryNo", this._CountryNo);
        }
        else {
            obj.addParameter("CountryNo", this.getFieldValue("ctlCompanyNameddl"));
        }
        if (this._blnPOHub) {
            var buyPrice = parseFloat(this.getFieldValue("ctlSupplierPrice"));
            var sellPrice = parseFloat(this.getFieldValue("ctlPrice"));
            obj.addParameter("SellPriceLessReason", buyPrice > sellPrice ? this.getFieldValue("ctlSellPriceLessReason") : "");
        }


        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
            $("#ctl00_cphMain_ctlMainInfo_ctlDB_BuyAndPriceMergine").hide();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        if (this.get_NonPreferredCompany() == true && this.getFieldValue("ctlSupplierWarranty").length < 1) {
            blnOK = false;
            this.showError(true, $R_RES.SupplierWarranty);
        }
        if (this._blnISAS6081Required == true) {
            if (!this.checkFieldEntered("ctlTypeOfSupplier")) {
                blnOK = false;
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlTypeOfSupplier").addClass("formRowError");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlTypeOfSupplier_pnlLoading").removeClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlTypeOfSupplier_pnlLoading").removeClass("formFieldLoading");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlTypeOfSupplier_pnlLoading").html('<span style="">Please enter a value</span>');
            }
            else {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlTypeOfSupplier_pnlLoading").addClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlTypeOfSupplier_pnlLoading").addClass("formFieldLoading");
            }
            if (!this.checkFieldEntered("ctlReasonForSupplier")) {
                blnOK = false;
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlReasonForSupplier").addClass("formRowError");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlReasonForSupplier_pnlLoading").removeClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlReasonForSupplier_pnlLoading").removeClass("formFieldLoading");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlReasonForSupplier_pnlLoading").html('<span style="">Please enter a value</span>');
            }
            else {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlReasonForSupplier_pnlLoading").addClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlReasonForSupplier_pnlLoading").addClass("formFieldLoading");
            }
            if (!this.checkFieldEntered("ctlRiskOfSupplier")) {
                blnOK = false;
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlRiskOfSupplier").addClass("formRowError");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlRiskOfSupplier_pnlLoading").removeClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlRiskOfSupplier_pnlLoading").removeClass("formFieldLoading");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlRiskOfSupplier_pnlLoading").html('<span style="">Please enter a value</span>');
            }
            else {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlRiskOfSupplier_pnlLoading").addClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlRiskOfSupplier_pnlLoading").addClass("formFieldLoading");
            }
            if (this._CountryNo == 0) {
                if (!this.checkFieldEntered("ctlCompanyNameddl")) {
                    blnOK = false;
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCompanyNameddl").addClass("formRowError");
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCompanyNameddl_pnlLoading").removeClass("invisible");
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCompanyNameddl_pnlLoading").removeClass("formFieldLoading");
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCompanyNameddl_pnlLoading").html('<span style="margin-left: 25px;">Please enter a value</span>');
                }
                else {
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCompanyNameddl_pnlLoading").addClass("invisible");
                    $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCompanyNameddl_pnlLoading").addClass("formFieldLoading");
                }
            }


        }
        if (this._blnPOHub) {
            var buyPrice = parseFloat(this.getFieldValue("ctlSupplierPrice"));
            var sellPrice = parseFloat(this.getFieldValue("ctlPrice"));
            var sellPriceLessReason = this.getFieldValue("ctlSellPriceLessReason");
            if (buyPrice > sellPrice && sellPriceLessReason == "") {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessReason").addClass("formRowError");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessReason_pnlLoading").removeClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessReason_pnlLoading").removeClass("formFieldLoading");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessReason_pnlLoading").html('<span>Please enter a value</span>');
                blnOK = false;
            } else {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessReason").removeClass("formRowError");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessReason_pnlLoading").addClass("invisible");
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessReason_pnlLoading").addClass("formFieldLoading");
            }
        }

        this.showError(blnOK == false, "");
        return blnOK;
    },

    setUpLiftPrice: function (upLiftPrice) {
        $R_FN.setInnerHTML(this._lblUpliftPrice, upLiftPrice == undefined || upLiftPrice == null || upLiftPrice == 0 ? "" : "(" + upLiftPrice + " %)");
    },
    updateCurrency: function () {
        //alert("test");
        this.showPriceFieldsLoading(true);
        //if (!blnCurrencyChangeOnly) blnCurrencyChangeOnly = false;
        //if (!this.checkFieldEntered("ctlShipVia")) {
        //    this.setFieldInError("ctlShipVia", false);
        //    this.getShipViaError();
        //    return;
        //}
        var nDate = new Date();
        // alert(nDate.getMilliseconds());

        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("UpdateCurrency");
        obj.addParameter("ID", this.getFieldValue("ctlLinkCurrency"));
        obj.addParameter("OldCurrencyNo", this._intCurrencyNo);
        obj.addParameter("UpliftPrice", this._floatUpliftPrice);
        obj.addParameter("SupplierPrice", this._floatSupPrice);
        obj.addParameter("ActualCurCode", this._strActualCurCode);
        obj.addParameter("SourcingResultNo", this._intSourcingResultID);
        obj.addParameter("dt", nDate.getMilliseconds());
        obj.addDataOK(Function.createDelegate(this, this.updateCurrencyOK));
        obj.addError(Function.createDelegate(this, this.updateCurrencyError));
        obj.addTimeout(Function.createDelegate(this, this.updateCurrencyError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        nDate = null;
    },
    updateCurrencyOK: function (args) {
        var res = args._result;
        //alert(res.UPLiftPrice);
        this.setFieldValue("ctlPrice", res.UPLiftPrice);
        this.setFieldValue("ctlEstimatedShippingCost", res.ShipCost);
        $R_FN.setInnerHTML(this._lblSupplierPrice, res.CurrCodeForSupPrice);
        $R_FN.setInnerHTML(this._lblUpliftPrice, res.NewCurCode);
        this.showPriceFieldsLoading(false);
    },
    updateCurrencyError: function () {
        this.showPriceFieldsLoading(false);
    },

    showPriceFieldsLoading: function (bln) {
        this.showFieldLoading("ctlPrice", bln);
        this.showFieldLoading("ctlSupplierPrice", bln);
        this.showFieldLoading("ctlEstimatedShippingCost", bln);
    },
    //findCountryRiskData: function () {
    //    var result = (this.getFieldDropDownExtraText("ctlCountryOfManufacture"));
    //    if (result != "") {
    //        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text(result);
    //    }
    //    else {
    //        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text('');
    //    }
    //},
    findCountryRiskData: function () {
        //alert(this.getFieldValue("ctlCountryOfManufacture"));
        this._intManufacturerNo = this.getFieldValue("ctlCountryOfManufacture");
        this.getRsMfr(this._intManufacturerNo);

        //var result = (this.getFieldDropDownExtraText("ctlCountryOfManufacture"));
        //if (result != "") {

        //    $("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text(result);
        //}
        //else {
        //    $("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text('');
        //}
    },
    getRsMfr: function (RsManufacturerNo) {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/POReceivingLines");
        obj.set_DataObject("POReceivingLines");
        obj.set_DataAction("GetWarningMessage");
        obj.addParameter("RsManufacturerNo", RsManufacturerNo);
        obj.addDataOK(Function.createDelegate(this, this.getRsMfrOK));
        obj.addError(Function.createDelegate(this, this.getRsMfrError));
        obj.addTimeout(Function.createDelegate(this, this.getRsMfrError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getRsMfrOK: function (args) {
        var res = args._result;
        this._RestrictedMFRMessage = res.WorningMessage;
        if (this._RestrictedMFRMessage != "") {
            //this.setFieldValue("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus", $R_FN.showIHSstatusDefi("", this._RestrictedMFRMessage ));
            //$("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text(this._RestrictedMFRMessage);
            //$("#ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").text($R_FN.showIHSstatusDefi(res.LifeCycleStage, res.IHSStatusDefination);
            //$find(this.getFormControlID(this._element.id, 'lblCountryOfOrigin')).setValue("Test");
            //this.setFieldValue("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryOfOrigin", $R_FN.showRestCountry("Active", this._RestrictedMFRMessage));
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").show();
            document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus').setAttribute('title', this._RestrictedMFRMessage);
        }
        else {
            $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlCountryOfManufacture_ctl03_lblCountryRiskStatus").hide();
        }


    },
    getRsMfrError: function () {

    },
    GetLocationDropDown: function () {
        this.showField("ctlCompanyNamelbl", false);
        this.showField("ctlCompanyNameddl", true);
        this.getFieldDropDownData("ctlCompanyNameddl");
        this._CountryNo = 0;
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNamelbl").hide();
        $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlCompanyNameddl").show();
    },

    focusOnPrice: function () {
        var buyPrice = parseFloat(this.getFieldValue("ctlSupplierPrice"));
        var sellPrice = parseFloat(this.getFieldValue("ctlPrice"));
        if (sellPrice < buyPrice) {
            document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessLabel').style.display = "";
            this.showField("ctlSellPriceLessReason", true);
            if ($('#spnRequiredReason').length == 0) {
                $("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessReason_tdTitle").append("<span id='spnRequiredReason' class='requiredField'>*</span>");
            }
        } else {
            document.getElementById('ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlEdit_ctlDB_ctlSellPriceLessLabel').style.display = "none";
            $('span[id^="spnRequiredReason"]').remove();
            this.showField("ctlSellPriceLessReason", false);
        }
    },

    getMfrNotes: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        this._strPath = "controls/Nuggets/ManufacturerMainInfo";
        this._strData = "ManufacturerMainInfo";
        obj.set_PathToData(this._strPath);
        obj.set_DataObject(this._strData);
        obj.set_DataAction("GetAdvisoryNotes");
        obj.addParameter("ID", this.getFieldValue("ctlManufacturer"));
        obj.addDataOK(Function.createDelegate(this, this.getMfrNotesOK));
        obj.addError(Function.createDelegate(this, this.getMfrNotesError));
        obj.addTimeout(Function.createDelegate(this, this.getMfrNotesError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getMfrNotesOK: function (args) {
        var res = args._result;
        console.log(res)
        var selectedMfrLabel = $find(this.getField("ctlManufacturer").ControlID)._aut._lblSelectedValue;
        $(selectedMfrLabel).parent().find('.advisory-notes').remove();
        $(selectedMfrLabel).append($R_FN.createAdvisoryNotesIcon(res.MfrAdvisoryNotes, 'margin-left-10'));
    },
    getMfrNotesError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);