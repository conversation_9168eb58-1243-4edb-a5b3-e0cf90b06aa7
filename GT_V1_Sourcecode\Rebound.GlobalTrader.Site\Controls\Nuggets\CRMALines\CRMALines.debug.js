/*
Marker     changed by      date         Remarks
[001]      Abhinav       17/11/20011    ESMS Ref:25 & 34  - Virtual Stock Update & Closeing of line CRMA
[002]      Vinay           31/08/2012   Add sales order line
[003]      Suhail          15/05/2018   Added Avoidable on CRMA Line


*/
///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 25.05.2011:
// - allow editing quantity before full receipt
//
// RP 30.03.2010:
// - ensure line is fully loaded before we allow edits (to stop the wrong data being
//   edited)
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines = function(element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.initializeBase(this, [element]);
    this._intCRMAID = -1;
    this._intLineID = -1;
    this._intLineCount = 0;
    this._intLineDataCalls = 0;
    this._blnLineIsFullyReceived = false;
    this._blnLineIsPartReceived = false;
    this._blnLineIsAllocated = false;
    //[001] code start
    this._blnCRMAClosed = false;
    this._intLineQuantityReceived = 0;
    this._intLineQuantityExists = 0;
    this._intLineQuantityCRMA = 0
    this._intLineQuantityAvailable = 0;
    this._intInvoiceLineNo = 0;
    //[001] code end
    this._blnLineLoaded = false;
    this._intLineQuantityReceived = 0;
    this._Reason1="";
    this._Reason2="";
    this._Reason1Val="";
    this._Reason2Val="";
    this._isClosed=false;
    this._isEditEnable = false;
    //[003] code start
    this._isAvoidable = false;
    //[003] code end
    
    
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.prototype = {

    get_intCRMAID: function() { return this._intCRMAID; }, set_intCRMAID: function(v) { if (this._intCRMAID !== v) this._intCRMAID = v; },
    get_intLineID: function() { return this._intLineID; }, set_intLineID: function(v) { if (this._intLineID !== v) this._intLineID = v; },
    get_intContactID: function() { return this._intContactID; }, set_intContactID: function(v) { if (this._intContactID !== v) this._intContactID = v; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },
    get_ibtnDelete: function() { return this._ibtnDelete; }, set_ibtnDelete: function(v) { if (this._ibtnDelete !== v) this._ibtnDelete = v; },
    //[001] code start
    get_ibtnClose: function() { return this._ibtnClose; }, set_ibtnClose: function(v) { if (this._ibtnClose !== v) this._ibtnClose = v; },
    get_ctlTabStrip: function() { return this._ctlTabStrip; }, set_ctlTabStrip: function(v) { if (this._ctlTabStrip !== v) this._ctlTabStrip = v; },
    get_tblOpen: function() { return this._tblOpen; }, set_tblOpen: function(v) { if (this._tblOpen !== v) this._tblOpen = v; },
    get_tblClosed: function() { return this._tblClosed; }, set_tblClosed: function(v) { if (this._tblClosed !== v) this._tblClosed = v; },

    //[001] code end	
    get_tblAll: function() { return this._tblAll; }, set_tblAll: function(v) { if (this._tblAll !== v) this._tblAll = v; },
    get_hypPrev: function() { return this._hypPrev; }, set_hypPrev: function(v) { if (this._hypPrev !== v) this._hypPrev = v; },
    get_hypNext: function() { return this._hypNext; }, set_hypNext: function(v) { if (this._hypNext !== v) this._hypNext = v; },
    get_lblLineNumber: function() { return this._lblLineNumber; }, set_lblLineNumber: function(v) { if (this._lblLineNumber !== v) this._lblLineNumber = v; },
    get_pnlLineDetail: function() { return this._pnlLineDetail; }, set_pnlLineDetail: function(v) { if (this._pnlLineDetail !== v) this._pnlLineDetail = v; },
    get_pnlLoadingLineDetail: function() { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function(v) { if (this._pnlLoadingLineDetail !== v) this._pnlLoadingLineDetail = v; },
    get_pnlLineDetailError: function() { return this._pnlLineDetailError; }, set_pnlLineDetailError: function(v) { if (this._pnlLineDetailError !== v) this._pnlLineDetailError = v; },
    get_fldAllocations: function() { return this._fldAllocations; }, set_fldAllocations: function(v) { if (this._fldAllocations !== v) this._fldAllocations = v; },
    get_tblAllocations: function() { return this._tblAllocations; }, set_tblAllocations: function(v) { if (this._tblAllocations !== v) this._tblAllocations = v; },
    get_fldReceived: function() { return this._fldReceived; }, set_fldReceived: function(v) { if (this._fldReceived !== v) this._fldReceived = v; },
    get_tblReceived: function() { return this._tblReceived; }, set_tblReceived: function(v) { if (this._tblReceived !== v) this._tblReceived = v; },
    //[001] code start
    get_blnCRMAClosed: function() { return this._blnCRMAClosed; }, set_blnCRMAClosed: function(v) { if (this._blnCRMAClosed !== v) this._blnCRMAClosed = v; },
    ////[001] code end
    get_ibtnDeallocate: function() { return this._ibtnDeallocate; }, set_ibtnDeallocate: function(v) { if (this._ibtnDeallocate !== v) this._ibtnDeallocate = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.callBaseMethod(this, "initialize");

        //data
        this._strDataPath = "controls/Nuggets/CRMALines";
        this._strDataObject = "CRMALines";
        ////[001] code start
        //tab strip
        this._ctlTabStrip.addTabIndexChanged(Function.createDelegate(this, this.tabChanged));
        this.addRefreshEvent(Function.createDelegate(this, this.tabChanged));

        //nugget events                                                      
        this._tblOpen.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
        this._tblClosed.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
        this._tblAll.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
        // //[001] code end
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this._fldAllocations.addShown(Function.createDelegate(this, this.onShownAllocations));
        this._fldAllocations.addRefresh(Function.createDelegate(this, this.onRefreshAllocations));
        this._tblAllocations.addMultipleSelectionChanged(Function.createDelegate(this, this.tblAllocationsSelectionChanged));
        this._fldReceived.addShown(Function.createDelegate(this, this.onShownReceived));
        this._fldReceived.addRefresh(Function.createDelegate(this, this.onRefreshReceived));

        //other controls
        this._tblAll.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
        $addHandler(this._hypPrev, "click", Function.createDelegate(this, this.prevLine));
        $addHandler(this._hypNext, "click", Function.createDelegate(this, this.nextLine));

        //add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        //edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[1]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.hideEditForm));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }

        //delete form
        if (this._ibtnDelete) {
            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[2]);
            this._frmDelete.addCancel(Function.createDelegate(this, this.hideDeleteForm));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.deleteComplete));
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
        }

        //deallocate form
        if (this._ibtnDeallocate) {
            $R_IBTN.addClick(this._ibtnDeallocate, Function.createDelegate(this, this.showDeallocateForm));
            this._frmDeallocate = $find(this._aryFormIDs[3]);
            this._frmDeallocate.addCancel(Function.createDelegate(this, this.hideDeallocateForm));
            this._frmDeallocate.addSaveComplete(Function.createDelegate(this, this.deallocateComplete));
            this._frmDeallocate.addNotConfirmed(Function.createDelegate(this, this.hideDeallocateForm));
        }
        ////[001] code start
        //close form

        if (this._ibtnClose) {
            $R_IBTN.addClick(this._ibtnClose, Function.createDelegate(this, this.showCloseForm));
            this._frmClose = $find(this._aryFormIDs[4]);
            this._frmClose.addCancel(Function.createDelegate(this, this.hideCloseForm));
            this._frmClose.addSaveComplete(Function.createDelegate(this, this.closeComplete));
            this._frmClose.addNotConfirmed(Function.createDelegate(this, this.hideCloseForm));

        }
        //end
        //this.getData();
        //$R_FN.showElement(this._pnlLineDetail, false);

        this.tabChanged();

        if (this._frmEdit) {
            this._ctlItemsReason1 = $find(this._frmEdit.getField("ctlItemsReason1").ID);
            this._ctlItemsReason1.addItem();
            this._ctlItemsReason2 = $find(this._frmEdit.getField("ctlItemsReason2").ID);
            this._ctlItemsReason2.addItem();
        }
    },
    tabChanged: function () {
        this.clearMessages();
        this.getTabData();
    },

    getTabData: function() {
        this.enableEditButtons(false);
        $R_FN.showElement(this._pnlLineDetail, false);
        switch (this._ctlTabStrip._selectedTabIndex) {
            case 0: this.getTabData_All(); break;
            case 1: this.getTabData_Open(); break;
            case 2: this.getTabData_Closed(); break;
        }
    },

    getTabData_Open: function() {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines_Open");
        obj.addParameter("id", this._intCRMAID);
        obj.addDataOK(Function.createDelegate(this, this.getTabDataOK_Open));
        obj.addError(Function.createDelegate(this, this.getTabDataError));
        obj.addTimeout(Function.createDelegate(this, this.getTabDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTabDataOK_Open: function(args) {
        var res = args._result;
        this.processLines(args._result);
        this.getDataOK_End();
    },

    getTabData_Closed: function() {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines_Closed");
        obj.addParameter("id", this._intCRMAID);
        obj.addDataOK(Function.createDelegate(this, this.getTabDataOK_Closed));
        obj.addError(Function.createDelegate(this, this.getTabDataError));
        obj.addTimeout(Function.createDelegate(this, this.getTabDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTabDataOK_Closed: function(args) {
        var res = args._result;
        this.processLines(args._result);
        this.getDataOK_End();
    },

    getTabData_All: function() {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines");
        obj.addParameter("id", this._intCRMAID);
        obj.addDataOK(Function.createDelegate(this, this.getTabDataOK_All));
        obj.addError(Function.createDelegate(this, this.getTabDataError));
        obj.addTimeout(Function.createDelegate(this, this.getTabDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTabDataOK_All: function(args) {
        var res = args._result;
        this.processLines(args._result);
        this.getDataOK_End();
    },

    getTabDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getCurrentTable: function() {
        var tbl;
        switch (this._ctlTabStrip._selectedTabIndex) {
            case 0: tbl = this._tblAll; break;
            case 1: tbl = this._tblOpen; break;
            case 2: tbl = this._tblClosed; break;
        }
        return tbl;
    },
    ////[001] code  end
    dispose: function() {
        if (this.isDisposed) return;
        if (this._hypPrev) $clearHandlers(this._hypPrev);
        if (this._hypNext) $clearHandlers(this._hypNext);
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
        if (this._ibtnDeallocate) $R_IBTN.clearHandlers(this._ibtnDeallocate);
        if (this._tblAll) this._tblAll.dispose();
        if (this._tblAllocations) this._tblAllocations.dispose();
        if (this._tblReceived) this._tblReceived.dispose();
        if (this._fldReceived) this._fldReceived.dispose();
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmDelete) this._frmDelete.dispose();

        if (this._frmDeallocate) this._frmDeallocate.dispose();
        ////[001] code strat
        if (this._ctlTabStrip) this._ctlTabStrip.dispose();
        if (this._ibtnClose) $R_IBTN.clearHandlers(this._ibtnClose);
        if (this._tblOpen) this._tblOpen.dispose();
        if (this._tblClosed) this._tblClosed.dispose();
        if (this._tblAll) this._tblAll.dispose();
        ////[001] code end
        this._frmAdd = null;
        this._frmEdit = null;
        this._frmDelete = null;
        this._frmDeallocate = null;
        this.intCRMAID = null;
        this._intLineID = null;
        this._intContactID = null;
        this._ibtnAdd = null;
        this._ibtnEdit = null;
        this._ibtnDelete = null;
        this._tblAll = null;
        this._hypPrev = null;
        this._hypNext = null;
        this._lblLineNumber = null;
        this._pnlLineDetail = null;
        this._pnlLoadingLineDetail = null;
        this._pnlLineDetailError = null;
        this._fldAllocations = null;
        this._tblAllocations = null;
        this._fldReceived = null;
        this._tblReceived = null;
        this._ibtnDeallocate = null;
        this._isAvoidable = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.callBaseMethod(this, "dispose");
    },

    getData: function() {
        this.enableEditButtons(false);
        $R_FN.showElement(this._pnlLineDetail, false);
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines");
        obj.addParameter("id", this._intCRMAID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    enableEditButtons: function(bln) {
        if (bln) {
            var tbl = this.getCurrentTable(); if (!tbl) return;
            var obj = tbl.getSelectedExtraData(); if (!obj) return;
            var blnAllowEdit = false;
            blnAllowEdit = !obj.IsReceived;
            this._isClosed = obj.IsClosed;
            //if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._blnLineIsFullyReceived && this._blnLineLoaded);
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, this._blnLineLoaded && !this._isEditEnable);

            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, !this._blnLineIsFullyReceived && !this._blnLineIsPartReceived && !this._blnLineIsAllocated && this._blnLineLoaded && !obj.IsClosed && !this._isEditEnable);
            if (this._ibtnClose) $R_IBTN.enableButton(this._ibtnClose, !obj.IsClosed && this._blnLineLoaded);
        } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
            if (this._ibtnClose) $R_IBTN.enableButton(this._ibtnClose, false);
        }
    },

    tbl_SelectedIndexChanged: function() {
        var tbl = this.getCurrentTable();

        //var objExtra = this._tblAll.getSelectedExtraData();
        this._blnLineIsFullyReceived = tbl.getSelectedExtraData().IsFullyReceived;
        this._blnLineIsPartReceived = tbl.getSelectedExtraData().IsPartReceived;
        this._blnLineIsAllocated = tbl.getSelectedExtraData().IsAllocated;
        this._intLineQuantityReceived = tbl.getSelectedExtraData().QuantityReceived;
        this.enableEditButtons(true);
        this._intLineID = tbl._varSelectedValue;
        this.getLineData();
        ////[001] code start
        this._intLineQuantityExists = tbl.getSelectedExtraData().QuantityExists;
        this._intLineQuantityAvailable = tbl.getSelectedExtraData().QuantityShipped;
        this._intInvoiceLineNo = tbl.getSelectedExtraData().InvoiceLineNo;
        this._isEditEnable = tbl.getSelectedExtraData().IsParentCustomerRMALineNo;
    },



    //getDataOK:
    processLines: function(result) {
        this.showLoading(false);
        var tbl = this.getCurrentTable();
        this._currentAS6081; //[RP-2339]
        tbl.clearTable();
        //this._tblAll.clearTable();
        //  var result = args._result;        
        if (result.Lines) {

            for (var i = 0; i < result.Lines.length; i++) {
                var row = result.Lines[i];
                //debugger;
                this._currentAS6081 = row.AS6081 == true ? "Yes" : "No"; //[RP-2339]
                var aryData = [
					$R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.ManufacturerNo, row.Manufacturer, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
					, $R_FN.writeDoubleCellValue(row.Quantity, row.Received)
					, $R_FN.writeDoubleCellValue($RGT_nubButton_Invoice(row.InvoiceNo, row.Invoice), row.InvoiceDate)
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ReturnDate), $R_FN.setCleanTextValue(row.Reason1), row.RootCause)
                    , $R_FN.writeDoubleCellValue(this._currentAS6081)
				];

                ////[001] code start
                var intExistsQty = Number.parseLocale(row.Quantity);
                var intTotQty = Number.parseLocale(row.QuantityShipped);
                var InvoiceLineNo = row.InvoiceLineNo;
                // //[001] code end
                var strCSS = "unposted";
                var intAllocated = Number.parseLocale(row.Allocated);
                var intReceived = Number.parseLocale(row.Received);
                var blnFullyReceived = false;
                var blnPartReceived = false;
                var blnAllocated = (intAllocated > 0);
                if (intReceived > 0) {
                    if (intReceived >= parseInt(row.Quantity, 0)) {
                        blnFullyReceived = true;
                    } else {
                        blnPartReceived = true;
                    }
                }

                var objExtraData = { IsAllocated: blnAllocated, IsFullyReceived: blnFullyReceived, IsPartReceived: blnPartReceived, QuantityReceived: intReceived, QuantityExists: intExistsQty, QuantityShipped: intTotQty, IsClosed: row.Closed, InvoiceLineNo: InvoiceLineNo,IsParentCustomerRMALineNo: row.IsParentCustomerRMALineNo };
                //if(row.Closed)
                if (objExtraData.IsClosed) strCSS = "shipped";
                if (row.IsAllocated) strCSS = "allocated";
                if (objExtraData.IsPartReceived) strCSS = "partReceived";
                if (objExtraData.IsFullyReceived) strCSS = "received";
                tbl.addRow(aryData, row.ID, row.ID == this._intLineID, objExtraData, strCSS);
                row = null;
            }
        }
        this._intLineCount = this._tblAll.countRows();
        this.showContent(true);
        this.showContentLoading(false);
        tbl.resizeColumns();
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    prevLine: function() {
        var intNewIndex = this._tblAll._intSelectedIndex - 1;
        if (intNewIndex < 0) return;
        this._tblAll.selectRow(intNewIndex, true);
    },

    nextLine: function() {
        var intNewIndex = this._tblAll._intSelectedIndex + 1;
        if (intNewIndex >= this._intLineCount) return;
        this._tblAll.selectRow(intNewIndex, true);
    },

    getLineData: function() {
        this._intLineDataCalls += 1;
        this._blnLineLoaded = false;
        this.enableEditButtons(false);
        this.showLoading(true);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        $R_FN.showElement(this._pnlLineDetail, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getLineDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        this.getLineAllocations();
        this.getLineReceived();
        obj = null;
    },

    getLineDataError: function(args) {
        this.lineDataCallComplete();
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLineDetailError, true);
        $R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
    },

    getLineDataOK: function(args) {
        this.lineDataCallComplete();
        $R_FN.showElement(this._pnlLineDetailError, false);
        var result = args._result;

        var creditNoteId = "";
        if ((result.CreditIds) && (result.CreditNumbers)) {
            for (var i = 0; i < result.CreditNumbers.length; i++) {
                var row = result.CreditIds[i];
                var row1 = result.CreditNumbers[i];
                creditNoteId += $RGT_nubButton_CreditNote(row.CreditId, row1.CreditNumber);
            }
        }

        this.setFieldValue("ctlQuantity", result.Quantity);
        this.setFieldValue("ctlQuantityReceived", result.Received);
        this.setFieldValue("ctlPartNo", $RGT_nubButton_Stock(result.StockNo, result.Part, result.ROHS));
        this.setFieldValue("hidPartNo", result.Part);
        this.setFieldValue("ctlReason", $R_FN.setCleanTextValue(result.Reason1));
        this.setFieldValue("ctlReason2", $R_FN.setCleanTextValue(result.Reason2));
        if (this._ctlItemsReason1) this._ctlItemsReason1.setSubCategory($R_FN.setCleanTextValue(result.Reason1), $R_FN.setCleanTextValue(result.Reason1Val));
        if (this._ctlItemsReason2) this._ctlItemsReason2.setSubCategory($R_FN.setCleanTextValue(result.Reason2), $R_FN.setCleanTextValue(result.Reason2Val));
        this._Reason1 = $R_FN.setCleanTextValue(result.Reason1);
        this._Reason2 = $R_FN.setCleanTextValue(result.Reason2);
        this._Reason1Val = $R_FN.setCleanTextValue(result.Reason1Val);
        this._Reason2Val = $R_FN.setCleanTextValue(result.Reason2Val);

        this.setFieldValue("ctlManufacturer", $RGT_nubButton_Manufacturer(result.ManufacturerNo, result.Manufacturer, result.MfrAdvisoryNotes));
        this.setFieldValue("hidManufacturer", result.Manufacturer);
        this.setFieldValue("hidManufacturerNo", result.ManufacturerNo);
        this.setFieldValue("ctlCustomerPart", $R_FN.setCleanTextValue(result.CustomerPart));
        //this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(result.Product));
        this.setFieldValue("ctlPackage", $R_FN.setCleanTextValue(result.Package));
        this.setFieldValue("ctlDateCode", $R_FN.setCleanTextValue(result.DC));
        this.setFieldValue("ctlReturnDate", result.ReturnDate);
        this.setFieldValue("hidProductNo", result.ProductNo);
        this.setFieldValue("hidPackageNo", result.PackageNo);
        this.setFieldValue("ctlROHS", $R_FN.writeROHS(result.ROHS));
        this.setFieldValue("hidROHS", result.ROHS);
        this.setFieldValue("ctlLineNotes", $R_FN.setCleanTextValue(result.LineNotes));
        this.setFieldValue("ctlCreditNoteNos", creditNoteId);
        this.setFieldValue("ctlRootCause", result.RootCause);
        //[003] code start
        this.setFieldValue("ctlIsAvoidable", result.Avoidable);
        this._isAvoidable = result.Avoidable;
        //[003] code end
        this.showField("ctlCreditNoteNos", (creditNoteId.length > 0));
        this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(result.Product));
        //this.setFieldValue("ctlProductDis", $R_FN.showHazardous(result.Product, result.IsProdHaz));
        this.setFieldValue("ctlProductDis", $R_FN.showHazardousNew(result.Product, result.IsProdHaz, $R_FN.setCleanTextValue(result.ProductMessage)));
        this.setFieldValue("hidProductHazar", result.IsProdHaz);
        this.setFieldValue("hidPrintHaza", result.IsPrintHaz);
        this.setFieldValue("ctlAS6081", result.AS6081 == true ? "Yes" : "No");
        $R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlLines_ctlDB_ctl13_ctlAS6081_lbl", result.AS6081);
        if (result.AS6081 == true) {
            this.getAS6081BannerMessage();
        } else {
            this.clearMessages();
        }
        $R_FN.showElement(this._pnlLineDetail, true);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.setInnerHTML(this._lblLineNumber, String.format($R_RES.LineXOfY, this._tblAll._intSelectedIndex + 1, this._intLineCount));
        this._blnLineLoaded = true;
        this.enableEditButtons(true);
    },

    getLineAllocations: function() {
        this._intLineDataCalls += 1;
        this.showLoading(true);
        this._fldAllocations.showLoading(true);
        this.enableAllocationsButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLineAllocations");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getLineAllocationsOK));
        obj.addError(Function.createDelegate(this, this.getLineAllocationsError));
        obj.addTimeout(Function.createDelegate(this, this.getLineAllocationsError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineAllocationsError: function(args) {
        this.lineDataCallComplete();
        this._fldAllocations.showError(true, args.get_ErrorMessage());
    },

    getLineAllocationsOK: function(args) {
        this.lineDataCallComplete();
        var res = args._result;
        this._fldAllocations.showContent(true);
        this._fldAllocations.resetCount();
        this._tblAllocations.clearTable();
        this._fldAllocations.updateCount(res.Count);
        if (res.Lines) {
            for (var i = 0; i < res.Lines.length; i++) {
                var row = res.Lines[i];
                var aryData = [
					$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.StockNo, row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CustomerNo, row.Customer, null, null, null, row.CustomerAdvisoryNotes), $R_FN.setCleanTextValue(row.CustomerPO))
                //[002] code start
					, $R_FN.writeDoubleCellValue($RGT_nubButton_POSO(row.SalesOrderNo, row.SalesOrderNumber, row.SalesOrderLineNo), row.DatePromised)
                //[002] code end
					, $R_FN.writeDoubleCellValue($RGT_nubButton_SRMA(row.SRMANo, row.SRMANumber), row.ReturnDate)
					, $R_FN.writeDoubleCellValue(row.QuantityAllocated, row.Price)
					];
                this._tblAllocations.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
        this._tblAllocations.resizeColumns();
    },

    getLineReceived: function() {
        this._intLineDataCalls += 1;
        this.showLoading(true);
        this._fldReceived.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLineReceived");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getLineReceivedOK));
        obj.addError(Function.createDelegate(this, this.getLineReceivedError));
        obj.addTimeout(Function.createDelegate(this, this.getLineReceivedError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineReceivedError: function(args) {
        this.lineDataCallComplete();
        this._fldReceived.showError(true, args.get_ErrorMessage());
    },

    onPotentialStatusChange: function() {
        var handler = this.get_events().getHandler("PotentialStatusChange");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
    getLineReceivedOK: function(args) {
        this.lineDataCallComplete();
        var res = args._result;
        this._fldReceived.showContent(true);
        this._fldReceived.resetCount();
        this._tblReceived.clearTable();
        this._fldReceived.updateCount(res.Count);
        if (res.Items) {
            for (var i = 0; i < res.Items.length; i++) {
                var row = res.Items[i];
                var aryData = [
						$RGT_nubButton_GoodsIn(row.GoodsInNo, row.GoodsInNumber)
					, $R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.StockNo, row.Part, row.ROHS), $R_FN.setCleanTextValue(row.SupplierPart))
					, $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
					, $R_FN.writeDoubleCellValue(row.Qty, row.LandedCost)
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Location), $R_FN.setCleanTextValue(row.DateReceived))
			    ];
                this._tblReceived.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
        this._tblReceived.resizeColumns();
    },

    lineDataCallComplete: function() {
        this._intLineDataCalls -= 1;
        if (this._intLineDataCalls < 1) this.showLoading(false);
    },

    onShownAllocations: function() {
        this._tblAllocations.resizeColumns();
    },

    onRefreshAllocations: function() {
        this.getLineAllocations();
    },

    tblAllocationsSelectionChanged: function() {
        this.enableAllocationsButtons(true);
    },

    enableAllocationsButtons: function(bln) {
        if (bln) {
            if (this._ibtnDeallocate) $R_IBTN.enableButton(this._ibtnDeallocate, this._tblAllocations._aryCurrentValues.length > 0);
        } else {
            if (this._ibtnDeallocate) $R_IBTN.enableButton(this._ibtnDeallocate, false);
        }
    },

    onShownReceived: function() {
        this._tblReceived.resizeColumns();
    },

    onRefreshReceived: function() {
        this.getLineReceived();
    },

    showAddForm: function() {
        this._frmAdd._intCRMAID = this._intCRMAID;
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function() {
        this.showForm(this._frmAdd, false);
        this._tblAll.resizeColumns();
    },

    saveAddComplete: function() {
        this.hideAddForm();
        this._intLineID = this._frmAdd._intNewID;
        // this.getData();
        this.getTabData();
        this.onPotentialStatusChange();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    },

    //[001] code start 
    showEditForm: function() {
        this._frmEdit._intCRMAID = this._intCRMAID;
        this._frmEdit._intLineID = this._intLineID;
        // this._frmEdit._intQuantityReceived = this._intLineQuantityReceived;
        //  this._frmEdit._intQuantityAvailable = this._intLineQuantityAvailable;
        this._frmEdit._isClosed = this._isClosed;
        this._frmEdit._intInvoiceLineNo = this._intInvoiceLineNo;
        this._frmEdit._intLineQuantityExists = this._intLineQuantityExists;
        this._frmEdit.setFieldValue("ctlPartNo", this.getFieldValue("hidPartNo"));
        this._frmEdit.setFieldValue("ctlQuantity", this.getFieldValue("ctlQuantity"));
        this._frmEdit.setFieldValue("ctlQuantity_Lable", this.getFieldValue("ctlQuantity"));

        this._frmEdit.setFieldValue("ctlReason", this.getFieldValue("ctlReason"));
        if (this._ctlItemsReason1) this._ctlItemsReason1.setSubCategory(this._Reason1, this._Reason1Val);
        if (this._ctlItemsReason2) this._ctlItemsReason2.setSubCategory(this._Reason2, this._Reason2Val);
        //this._ctlItemsReason1.setSubCategory($R_FN.setCleanTextValue(result.Reason),$R_FN.setCleanTextValue(result.Reason1Val))
        //this._ctlItemsReason2.setSubCategory($R_FN.setCleanTextValue(result.Reason2),$R_FN.setCleanTextValue(result.Reason2Val))                 
        this._frmEdit.setFieldValue("ctlLineNotes", this.getFieldValue("ctlLineNotes"));
        this._frmEdit.setFieldValue("ctlRootCause", this.getFieldValue("ctlRootCause"));
        //[003] code start
        this._frmEdit.setFieldValue("ctlIsAvoidable", this._isAvoidable);
        //[003] code end
        this._frmEdit._blnProductHaza = Boolean.parse(this.getFieldValue("hidProductHazar"));
        this.showForm(this._frmEdit, true);
    },
    //[001] code end 
    hideEditForm: function() {
        this.showForm(this._frmEdit, false);
        this._tblAll.resizeColumns();
    },

    saveEditComplete: function() {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        // this.getData();
        this.getTabData();
        this.onPotentialStatusChange();
    },

    showDeleteForm: function() {
        this._frmDelete._intLineID = this._intLineID;
        this._frmDelete.setFieldValue("ctlPartNo", this.getFieldValue("hidPartNo"));
        this._frmDelete.setFieldValue("ctlQuantity", this.getFieldValue("ctlQuantity"));
        this._frmDelete.setFieldValue("ctlReason", this.getFieldValue("ctlReason"));
        //this._ctlItemsReason1.setSubCategory($R_FN.setCleanTextValue(result.Reason),$R_FN.setCleanTextValue(result.Reason1Val))
        //this._ctlItemsReason2.setSubCategory($R_FN.setCleanTextValue(result.Reason2),$R_FN.setCleanTextValue(result.Reason2Val))                 
        this.showForm(this._frmDelete, true);
    },

    hideDeleteForm: function() {
        this.showForm(this._frmDelete, false);
        this._tblAll.resizeColumns();
    },

    deleteComplete: function() {
        this.hideDeleteForm();
        // this.getData();
        this.getTabData();
        this.onPotentialStatusChange();
    },

    ////[001] code start
    showCloseForm: function() {
        //  alert('form close called');
        this._frmClose._intCRMAID = this._intCRMAID;
        this._frmClose._intLineID = this._intLineID;
        this._frmClose.setFieldValue("ctlPartNo", this.getFieldValue("hidPartNo"));
        this._frmClose.setFieldValue("ctlQuantity", this.getFieldValue("ctlQuantity"));
        this._frmClose.setFieldValue("ctlReason", this.getFieldValue("ctlReason"));
        //this._ctlItemsReason1.setSubCategory($R_FN.setCleanTextValue(result.Reason),$R_FN.setCleanTextValue(result.Reason1Val))
        //this._ctlItemsReason2.setSubCategory($R_FN.setCleanTextValue(result.Reason2),$R_FN.setCleanTextValue(result.Reason2Val))                 
        this.showForm(this._frmClose, true);

    }, //[001] code  end
    hideCloseForm: function() {
        this.showForm(this._frmClose, false);
        this.getCurrentTable().resizeColumns();
    },
    closeComplete: function() {
        this.hideCloseForm();
        this.getTabData();
        this.onPotentialStatusChange();
    },
    //end
    showDeallocateForm: function() {
        this._frmDeallocate._intCRMAID = this._intCRMAID;
        this._frmDeallocate._aryLineIDs = this._tblAllocations._aryCurrentValues;
        this.showForm(this._frmDeallocate, true);
    },

    hideDeallocateForm: function() {
        this.showForm(this._frmDeallocate, false);
        this._tblAll.resizeColumns();
    },

    deallocateComplete: function() {
        this.hideDeallocateForm();
        //this.getData();
        this.getTabData();
        this.onPotentialStatusChange();
    },
    // Get banner from database start
    getAS6081BannerMessage: function () {
        //debugger;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/SetupNuggets/AS6081");
        obj.set_DataObject("AS6081");
        obj.set_DataAction("GetAlertMessageByOperationType");
        obj.addParameter("operationType", "ReceiveCRMABanner");
        obj.addDataOK(Function.createDelegate(this, this.getAS6081BannerMessageOK));
        obj.addError(Function.createDelegate(this, this.getAS6081BannerMessageError));
        obj.addTimeout(Function.createDelegate(this, this.getAS6081BannerMessageError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getAS6081BannerMessageOK: function (args) {
        //this.disableSaveButton();
        var result = args._result;
        this.clearMessages();
        this.addMessage(result.Message, $R_ENUM$MessageTypeList.Warning);
    },
    getAS6081BannerMessageError: function (args) {
        console.error(`error occured while trying to fetch 'banner for AS6081'`);
    }

// Get banner from database end

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
