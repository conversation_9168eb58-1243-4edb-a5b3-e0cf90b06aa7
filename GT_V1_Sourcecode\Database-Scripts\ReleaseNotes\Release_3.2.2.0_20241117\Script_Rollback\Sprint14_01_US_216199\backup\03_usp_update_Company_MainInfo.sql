﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-207777]		An.TranTan			24-Jul-2024		UPDATE			Update Rebate Account for customer
[US-210037]		An.TranTan			22-Oct-2024		UPDATE			Update Advisory notes
===========================================================================================
*/
CREATE OR ALTER     PROCEDURE [dbo].[usp_update_Company_MainInfo]
    --********************************************************************************************                                                                             
    --* Updates the CompanyMainInfo nugget                                                                             
    --*                                                                              
    --* SK 26.04.2010:                                                                             
    --* - add salesman info                                                                             
    --*                                                                              
    --* RP 17.11.2009:                                                                             
    --* - new proc                             
    /*                       
 Marker    Owner    Date   Remarks                     
 [001]    Ravi    29-05-2023  RP-1269 (SANCTIONS - ADD BUTTON TO CONTACT COMPANIES SECTION HEADER SIMILAR TO EARI CHECK BUTTON TO SET ACCOUNT WITH A QUARANTINE DO NOT ALLOW TRADING)                     
                     
*/
    --********************************************************************************************                                                                             
    @CompanyId int,
    @CompanyName nvarchar(128),
    @ParentCompanyNo int = Null,
    @Salesman int = Null,
    @Telephone nvarchar(30) = Null,
    @Telephone800 nvarchar(30) = Null,
    @Fax nvarchar(30) = Null,
    @EMail nvarchar(128) = Null,
    @URL nvarchar(128) = Null,
    @TypeNo int = Null,
    @Tax nvarchar(25) = Null,
    @Notes nvarchar(MAX) = Null,
    @ImportantNotes nvarchar(MAX) = Null,
    @CompanyRegNo nvarchar(50) = Null,
    @UpdatedBy int = Null,
    @certificateNotes nvarchar(MAX) = Null,
    @qualityNotes nvarchar(MAX) = Null,
    @IsTraceability bit = NULL,
    @ERAIMember bit = NULL,
    @ERAIReported bit = NULL,
    @ReviewDate datetime = NULL,
    @UPLiftPrice float = NULL,
    @SupplierWarranty int = NULL,
    @EORINumber nvarchar(30) = Null,
    @ProductReqTesting BIT = NULL,
    @IsCustomer BIT = NULL,
    @IsSupplier BIT = NULL,
    @isPremierCustomer BIT = null,
    @IsTier2PremierCustomer BIT = null,
    @IsSanctioned BIT = NULL, --[001]              
    @IsRebateAccount BIT = NULL,        
    @PurchasingNotes nvarchar(max) = null,
    @GroupCodeNo INT = NULL,
	@AdvisoryNotes NVARCHAR(50) = null,
	@IsDisplayAdvisory BIT = null,
    @RowsAffected int = NULL Output
AS
BEGIN
    DECLARE @ClientNo INT
    DECLARE @RevDate datetime = NULL
    DECLARE @LastReviewDate datetime = NULL
    DECLARE @previousSalesman int

    ----IF @IsPOHub=1                                                                 
    ----BEGIN                                                                              
    ---- SELECT @ClientNo = ClientNo FROM dbo.tbCompany WHERE CompanyId = @CompanyId                                                                 
    ---- UPDATE dbo.tbCompany SET IsPOHub = NULL WHERE ClientNo = @ClientNo                                                                 
    ---- UPDATE dbo.tbCompany SET IsPOHub = 1 WHERE CompanyId = @CompanyId                                         ----END          
    select @RevDate = ReviewDate,
           @previousSalesman = Salesman,
           @ClientNo = ClientNo
    from dbo.tbCompany
    where CompanyId = @CompanyId
    --if(DATEDIFF(day,@RevDate,@ReviewDate)>=1 OR DATEDIFF(day,@RevDate,@ReviewDate)<=-1 )                                            
    if (
           (
               @RevDate IS NULL
               AND NOT @ReviewDate IS NULL
           )
           OR DATEDIFF(day, @RevDate, @ReviewDate) >= 1
       )
    begin
        select @LastReviewDate = LastReviewDate
        from tbCompany
        WHERE CompanyId = @CompanyId
        UPDATE dbo.tbCompany
        SET LastReviewDate = current_timestamp,
            PreviousReviewDate = @LastReviewDate
        WHERE CompanyId = @CompanyId
    end

    UPDATE dbo.tbCompany
    SET CompanyName = @CompanyName,
        ParentCompanyNo = @ParentCompanyNo,
        Salesman = @Salesman,
        Telephone = @Telephone,
        Telephone800 = @Telephone800,
        Fax = @Fax,
        EMail = @EMail,
        URL = @URL,
        TypeNo = @TypeNo,
        Tax = @Tax,
        Notes = @Notes,
        ImportantNotes = @ImportantNotes,
        UpdatedBy = @UpdatedBy,
        DLUP = current_timestamp,
        FullName = dbo.ufn_get_fullname(@CompanyName),
        CompanyRegNo = @CompanyRegNo,
        certificateNotes = @certificateNotes,
        qualityNotes = @qualityNotes,
        IsTraceability = @IsTraceability,
        ERAIMember = @ERAIMember,
        ERAIReported = @ERAIReported,
        ReviewDate = @ReviewDate,
        UPLiftPrice = @UPLiftPrice,
        SupplierWarranty = @SupplierWarranty,
        EORINumber = @EORINumber,
        SupplierProdReqTesting = @ProductReqTesting,
        IsCompany = @IsCustomer,
        IsSupplier = @IsSupplier,
        isPremierCustomer = @isPremierCustomer,
        IsTier2PremierCustomer = @IsTier2PremierCustomer,
        IsSanctioned = ISNULL(@IsSanctioned, 0), --[001]            
        PurchasingNotes = @PurchasingNotes,
        GroupCodeNo = @GroupCodeNo,
		IsRebateAccount = @IsRebateAccount,
		AdvisoryNotes = @AdvisoryNotes,
		IsDisplayAdvisory = ISNULL(@IsDisplayAdvisory, 0)
    WHERE CompanyId = @CompanyId

    ---- code to check if any row exists for previous salesperson or not if no then insert it in  tbCustomerTargetDraft                       
    if
    (
        select count(1)
        from tbCustomerTargetDraft
        where SalesManNo = @previousSalesman
              and CompanyNo = @CompanyId
              and YearNo = year(getdate())
    ) = 0
    BEGIN
        INSERT INTO [dbo].tbCustomerTargetDraft
        (
            SalesTargetNo,
            SalesManNo,
            CompanyNo,
            [YearNo],
            [DLUP],
            [UpdatedBy],
            [CreatedDate]
        )
        select
            (
                SELECT top 1
                    salestargetdraftid
                FROM tbSalesTargetdraft stf with (nolock)
                where stf.SalesManNo = @previousSalesman
                      and YearNo = Year(getdate())
            ),
            @previousSalesman,
            cm.Companyid,
            Year(getdate()),
            GETDATE(),
            1,
            GETDATE()
        from tbCompany cm with (nolock)
            join tbLogin lg with (nolock)
                on cm.Salesman = lg.LoginId
        where lg.LoginId = @previousSalesman
              and cm.CompanyId not in (
                                          SELECT ct.CompanyNo
                                          FROM tbCustomerTargetDraft ct with (nolock)
                                          WHERE SalesManNo = @previousSalesman
                                                and YearNo = Year(getdate())
                                      )
              and cm.inactive = 0
    END

    ---- code to check if any row exists for previous salesperson or not if no then insert it in tbCustomerTargetFinal                         
    if
    (
        select count(1)
        from tbCustomerTargetFinal
        where SalesManNo = @previousSalesman
              and CompanyNo = @CompanyId
              and YearNo = year(getdate())
    ) = 0
    BEGIN
        INSERT INTO [dbo].tbCustomerTargetFinal
        (
            SalesTargetNo,
            SalesManNo,
            CompanyNo,
            [YearNo],
            [DLUP],
            [UpdatedBy],
            [CreatedDate]
        )
        select
            (
                SELECT top 1
                    SalesTargetId
                FROM tbSalesTargetFinal stf with (nolock)
                where stf.SalesManNo = @previousSalesman
                      and YearNo = Year(getdate())
            ),
            @previousSalesman,
            cm.Companyid,
            Year(getdate()),
            GETDATE(),
            1,
            GETDATE()
        from tbCompany cm with (nolock)
            join tbLogin lg with (nolock)
                on cm.Salesman = lg.LoginId
        where lg.LoginId = @previousSalesman
              and cm.CompanyId not in (
                                          SELECT ct.CompanyNo
                                          FROM tbCustomerTargetFinal ct with (nolock)
                                          WHERE SalesManNo = @previousSalesman
                                                and YearNo = Year(getdate())
                                      )
              and cm.inactive = 0
    END

    -- Update sales                                                            
    IF (@previousSalesman is not null and (@previousSalesman <> @Salesman))
    BEGIN
        UPDATE dbo.tbCompany
        SET SalesmanUpdatedDate = current_timestamp
        WHERE CompanyId = @CompanyId

        -- Maintain history of salesman:                                                           
        INSERT INTO tbCompanySalesmanHistory
        (
            CompanyNo,
            Salesman,
            UpdatedBy,
            DLUP
        )
        VALUES
        (@CompanyId, @previousSalesman, @UpdatedBy, CURRENT_TIMESTAMP)


        ---- insert the data into customerdraft table for salesman id send from ui screen                           
        if
        (
            select count(1)
            from tbCustomerTargetDraft
            where SalesManNo = @Salesman
                  and YearNo = year(getdate())
        ) = 0
        BEGIN
            INSERT INTO tbCustomerTargetDraft
            (
                SalesTargetNo,
                SalesManNo,
                CompanyNo,
                [YearNo],
                [DLUP],
                [UpdatedBy],
                [CreatedDate]
            )
            select
                (
                    SELECT top 1
                        SalesTargetDraftId
                    FROM tbSalesTargetDraft stf
                    where stf.SalesManNo = @Salesman
                          and YearNo = year(getdate())
                ),
                @Salesman,
                cm.Companyid,
                year(getdate()),
                GETDATE(),
                1,
                GETDATE()
            from tbCompany cm
                join tbLogin lg
                    on cm.Salesman = lg.LoginId
            where lg.LoginId = @Salesman
                  and cm.CompanyId not in (
                                              SELECT ct.CompanyNo
                                              FROM tbCustomerTargetDraft ct
                                              WHERE SalesManNo = @Salesman
                                                    and YearNo = year(getdate())
                                          )
        END

        ---- insert the data into tbCustomerTargetFinal table for salesman id send from ui screen                          
        if
        (
            select count(1)
            from tbCustomerTargetFinal
            where SalesManNo = @Salesman
                  and YearNo = year(getdate())
        ) = 0
        BEGIN
            INSERT INTO tbCustomerTargetFinal
            (
                SalesTargetNo,
                SalesManNo,
                CompanyNo,
                [YearNo],
                [DLUP],
                [UpdatedBy],
                [CreatedDate],
                IsCustomerMoved
            )
            select
                (
                    SELECT top 1
                        SalesTargetDraftId
                    FROM tbSalesTargetDraft stf
                    where stf.SalesManNo = @Salesman
                          and YearNo = year(getdate())
                ),
                @Salesman,
                cm.Companyid,
                year(getdate()),
                GETDATE(),
                1,
                GETDATE(),
                0
            from tbCompany cm
                join tbLogin lg
                    on cm.Salesman = lg.LoginId
            where lg.LoginId = @Salesman
                  and cm.CompanyId not in (
                                              SELECT ct.CompanyNo
                                              FROM tbCustomerTargetDraft ct
                                              WHERE SalesManNo = @Salesman
                                                    and YearNo = year(getdate())
                                          )
        END

        --- added by arpit                                  
        ----- if customer for the SP exists then move them also                         
        declare @oldSalesTargetNo int,
                @newSalesTargetNo int

        select @oldSalesTargetNo = Max(SalesTargetId)
        from tbSalesTargetFinal
        where SalesManNo = @previousSalesman
              and YearNo = year(getdate())
        -- and (isTeamMoved=0 or IsTeamMoved is null)                                         
        select @newSalesTargetNo = MAx(SalesTargetId)
        from tbSalesTargetFinal
        where SalesManNo = @Salesman
              and YearNo = year(getdate())
        --and (isTeamMoved=1)                                             

        ---- to check if company is moved from salesperson to another                                 
        if (
           (
               select count(1)
               from tbCustomerTargetFinal
               where SalesManNo = @Salesman
                     and SalesTargetNo = @newSalesTargetNo
                     and CompanyNo = @CompanyId
                     and YearNo = year(getdate())
           ) = 0
           )
        BEGIN
            ---- the IsCustomerMoved = 1 to previous rows                           
            update tbCustomerTargetFinal
            set IsCustomerMoved = 1
            where SalesManNo = @previousSalesman
                  and SalesTargetNo = @oldSalesTargetNo
                  and CompanyNo = @CompanyId
                  and YearNo = year(getdate())

            ---- insert new row for company with new salesman                                    
            INSERT INTO [dbo].[tbCustomerTargetFinal]
            (
                [SalesTargetNo],
                [SalesManNo],
                [CompanyNo],
                [JanTarget],
                [FebTarget],
                [MarchTarget],
                [AprTarget],
                [MayTarget],
                [JuneTarget],
                [JulyTarget],
                [AugTarget],
                [SepTarget],
                [OctTarget],
                [NovTarget],
                [DecTarget],
                [YearNo],
                [DLUP],
                [UpdatedBy],
                [CreatedDate],
                IsCustomerMoved
            )
            Select @newSalesTargetNo,
                   @Salesman,
                   CompanyNo,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   NULL,
                   Year(Getdate()),
                   getdate(),
                   1,
                   getdate(),
                   0
            from [tbCustomerTargetFinal]
            where SalesManNo = @previousSalesman
                  and SalesTargetNo = @oldSalesTargetNo
                  and CompanyNo = @CompanyId
                  and YearNo = year(getdate())

            --- update the target for the customer moved to the new SP                               
            declare @monthId int
            Set @monthId = MONTH(GETDATE())

            If @monthId = 1
            BEGIN
                update tbCustomerTargetFinal
                set FebTarget = 0,
                    MarchTarget = 0,
                    AprTarget = 0,
                    MayTarget = 0,
                    JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set FebTarget = 0,
                    MarchTarget = 0,
                    AprTarget = 0,
                    MayTarget = 0,
                    JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 2
            BEGIN
                update tbCustomerTargetFinal
                set MarchTarget = 0,
                    AprTarget = 0,
                    MayTarget = 0,
                    JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set MarchTarget = 0,
                    AprTarget = 0,
                    MayTarget = 0,
                    JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 3
            BEGIN
                update tbCustomerTargetFinal
                set AprTarget = 0,
                    MayTarget = 0,
                    JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set AprTarget = 0,
                    MayTarget = 0,
                    JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 4
            BEGIN
                update tbCustomerTargetFinal
                set MayTarget = 0,
                    JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set MayTarget = 0,
                    JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 5
            BEGIN
                update tbCustomerTargetFinal
                set JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set JuneTarget = 0,
                    JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 6
            BEGIN
                update tbCustomerTargetFinal
                set JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set JulyTarget = 0,
                    AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 7
            BEGIN
                update tbCustomerTargetFinal
                set AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set AugTarget = 0,
                    SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 8
            BEGIN
                update tbCustomerTargetFinal
                set SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set SepTarget = 0,
                    OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 9
            BEGIN
                update tbCustomerTargetFinal
                set OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set OctTarget = 0,
                    NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 10
            BEGIN
                update tbCustomerTargetFinal
                set NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set NovTarget = 0,
                    DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END
            else If @monthId = 11
            BEGIN
                update tbCustomerTargetFinal
                set DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())

                update tbCustomerTargetDraft
                set DecTarget = 0
                where SalesManNo = @previousSalesman
                      and SalesTargetNo = @oldSalesTargetNo
                      and CompanyNo = @CompanyId
                      and YearNo = year(getdate())
            END

        END

    END


    --IF @ClientNo = 114                                              
    --BEGIN                                                         
    --  insert into [BorisGlobalTraderImports].dbo.tbSynCompany                                                         
    --(                                 
    --  CompanyNo ,                                                         
    --  IsCompleted,                                                         
    --  Actions           
    --)                                                         
    --VALUES                                                         
    --(                                                         
    --  @CompanyId,                                                         
    --  0,                                                         
    --  'UPDATECOMPANY'                                                         
    --)                                                         
    --END                                                         
    ----Update VAT No in Company Address--                                                 
    --UPDATE tbCompanyAddress                                 
    --SET VatNo=@Tax                                                 
    --WHERE CompanyNo=@CompanyId                                         

    SELECT @RowsAffected = @@ROWCOUNT

END;
GO


