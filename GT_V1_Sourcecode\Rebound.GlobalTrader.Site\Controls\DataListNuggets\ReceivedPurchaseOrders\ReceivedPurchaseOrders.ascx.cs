using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class ReceivedPurchaseOrders : Base {

		#region Properties
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("ReceivedPurchaseOrders");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "ReceivedPurchaseOrders");
			AddScriptReference("Controls.DataListNuggets.ReceivedPurchaseOrders.ReceivedPurchaseOrders.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("QuantityOrdered", "QuantityReceived", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			_tbl.Columns.Add(new FlexiDataColumn("GoodsInNo", "DateReceived", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			_tbl.Columns.Add(new FlexiDataColumn("SupplierPart", "SupplierInvoice", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("AirWayBill", "InvoiceTotal", Unit.Empty, true));
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders", ctlDesignBase.ClientID);
		}
	}
}
