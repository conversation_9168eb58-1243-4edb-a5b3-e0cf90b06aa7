Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.Confirm.initializeBase(this,[n]);this._intBOMID=-1;this._isAddButtonEnable=!0;this._ValidMessage="";this._blnReqInValid=!1;this._intContact2No=-1;this._blnPVVBOMValidateMessage="";this._PVVBOMCountValid=!1};Rebound.GlobalTrader.Site.Controls.Forms.Confirm.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},get_intContact2No:function(){return this._intContact2No},set_intContact2No:function(n){this._intContact2No!==n&&(this._intContact2No=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intBOMID=null,this._intContact2No=null,this._blnPVVBOMValidateMessage=null,this._PVVBOMCountValid=null,Rebound.GlobalTrader.Site.Controls.Forms.Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)),this._ctlMail=$find(this.getField("ctlSendMailMessage").ID),this._ctlMail._ctlRelatedForm=this);this.getFieldDropDownData("ctlSalesperson");this._blnReqInValid==!0?(this.showError(!0,this._ValidMessage),this.showField("ctlSalesperson",!1),this.showField("ctlSendMailMessage",!1),this.showField("ctlConfirm",!1)):this._PVVBOMCountValid==!1?(this.showError(!0,this._blnPVVBOMValidateMessage),this.showField("ctlSalesperson",!0),this.showField("ctlSendMailMessage",!0),this.showField("ctlConfirm",!0)):($("#Exp1").show(),this.showField("ctlSalesperson",!0),this.showField("ctlSendMailMessage",!0),this.showField("ctlConfirm",!0));$addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl21_ibtnBack_hyp"),"click",Function.createDelegate(this,this.noClicked1));$addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl22_ibtnBack_hyp"),"click",Function.createDelegate(this,this.noClicked1))},noClicked1:function(){this.onNotConfirmed()},yesClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/BOMMainInfo");n.set_DataObject("BOMMainInfo");n.set_DataAction("savePurchaseHUBData");n.addParameter("id",this._intBOMID);n.addParameter("BomCode",this._BomCode);n.addParameter("BomName",this._BomName);n.addParameter("BomCompanyName",this._BomCompanyName);n.addParameter("BomCompanyNo",this._BomCompanyNo);n.addParameter("AssignUserNo",this.getFieldValue("ctlSalesperson"));n.addParameter("aryRecipientLoginIDs",$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));n.addParameter("Contact2No",this._intContact2No);n.addDataOK(Function.createDelegate(this,this.saveConfirmComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},validateForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlSalesperson")||(n=!1),n||this.showError(!0),this._blnReqInValid==!0&&(n=!1,this.showError(!0,this._ValidMessage)),n},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveConfirmComplete:function(n){n._result.ValidateMessage!=""?this.showError(!0,n._result.ValidateMessage):n._result.Result==!0?(this._isAddButtonEnable=!n._result.Result,this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);