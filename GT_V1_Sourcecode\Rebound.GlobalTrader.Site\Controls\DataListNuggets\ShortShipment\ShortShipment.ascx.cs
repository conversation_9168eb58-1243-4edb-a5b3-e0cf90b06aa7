﻿using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment
{
    public partial class ShortShipment : Base
    {

        #region Locals 
        protected IconButton _ibtnExportCSV;//[001]   
        #endregion
        #region Properties
        private bool _blnCanExportCSV = true;
        public bool CanExportCSV
        {
            get { return _blnCanExportCSV; }
            set { _blnCanExportCSV = value; }
        }
        private bool _blnCanViewClient = true;
        public bool CanViewClient
        {
            get { return _blnCanViewClient; }
            set { _blnCanViewClient = value; }
        }
        #endregion
        protected override void OnInit(EventArgs e)
        {
            SetDataListNuggetType("ShortShipment");
            base.OnInit(e);
            WireUpControls();//[001]  
            TitleText = Functions.GetGlobalResource("Nuggets", "ShortShipment");
            AddScriptReference("Controls.DataListNuggets.ShortShipment.ShortShipment");
            SetupTable();
        }


        protected override void OnLoad(EventArgs e)
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnExportCSV", _ibtnExportCSV.ClientID);
            _scScriptControlDescriptor.AddProperty("IsCanViewClient", CanViewClient);
            _scScriptControlDescriptor.AddProperty("LoginClientId", SessionManager.ClientID);
            _ibtnExportCSV.Visible = CanExportCSV;
            base.OnLoad(e);
        }

        protected override void RenderAdditionalState()
        {
            base.RenderAdditionalState();
        }
        private void SetupTable()
        {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("ShortShipmentNo", "DebitNumber", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), false));
            _tbl.Columns.Add(new FlexiDataColumn("GoodsInNo", "PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), false));
            _tbl.Columns.Add(new FlexiDataColumn("Raisedby", SessionManager.IsPOHub.Value ? "PurchaseOrderNo" : "IPONo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), false));
            _tbl.Columns.Add(new FlexiDataColumn("Supplier", "ManufacturerNo", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), false));
            _tbl.Columns.Add(new FlexiDataColumn("Buyer", "IsShortageRefundIssue", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            _tbl.Columns.Add(new FlexiDataColumn("QuantityOrdered", "QuantityReceived", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            _tbl.Columns.Add(new FlexiDataColumn("ShortageQuantity", "ShortValue", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            //_tbl.Columns.Add(new FlexiDataColumn("IsClosed", "IsCancel", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            _tbl.Columns.Add(new FlexiDataColumn("DateReceived1", "Status"));
        }
        private void WireUpControls()
        {
            _ibtnExportCSV = (IconButton)FindIconButton("ibtnExportCSV");

        }
    }
}
