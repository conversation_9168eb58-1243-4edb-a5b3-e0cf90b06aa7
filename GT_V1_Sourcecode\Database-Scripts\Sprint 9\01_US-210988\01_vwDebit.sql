﻿GO

/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210988]			Phuc Hoang			20-Aug-2024		UPDATE          Error showing Debit Note (SCTASK0024621)
===========================================================================================
*/

CREATE OR ALTER VIEW [dbo].[vwDebit]                          
AS                            
SELECT                     
a.DebitId                    
,a.DebitNumber                    
,a.ClientNo                    
,a.CompanyNo                
,a.ContactNo                    
,a.Debit<PERSON>ate                    
,a.<PERSON><PERSON>o                    
,a.<PERSON>                    
,a.Buy<PERSON>                    
,a.Notes                    
,a.Instructions                    
,a.<PERSON>                    
,a.<PERSON>                    
,a.<PERSON>                    
,a.<PERSON><PERSON>                    
,a.<PERSON>pp<PERSON>No                    
,a.ReferenceDate                    
,a.SupplierInvoice                    
,a.SupplierRMA                    
,a.SupplierCredit                    
,a.UpdatedBy                    
,a.DLUP                    
,a.IncotermNo                    
,a.ClientDebitNo                          
,b.CompanyName                  
,  c.ContactName                          
,  j.CurrencyCode                          
,  j.CurrencyDescription                          
,  ISNULL(d.EmployeeName, 'N/A') AS RaiserName                          
,  e.EmployeeName           AS BuyerName                          
,  e.TeamNo                          
,  f.DivisionName                          
,  k.TaxName                          
,  g.PurchaseOrderNumber                          
,  g.DateOrdered           AS PurchaseOrderDate                          
,  h.SupplierRMANumber                          
,  ( SELECT SUM(ISNULL((z.Price * z.Quantity), 0))                          
   FROM   dbo.tbDebitLine z                          
   WHERE  z.DebitNo = a.DebitId                          
  )              AS DebitValue                          
,  dbo.ufn_get_taxrate(a.TaxNo, a.ClientNo, a.DebitDate) AS TaxRate                 
, ipo.InternalPurchaseOrderId AS InternalPurchaseOrderNo                  
, ipo.InternalPurchaseOrderNumber                
, ipo.CompanyNo AS IPOCompanyNo              
, cop.CompanyName AS IPOCompanyName             
,a.ishublocked               
, ipo.LinkMultiCurrencyNo            
,a.HeaderImageName                
-----------        
--,cr.FooterText        
, FHstry.FooterText AS FooterText        
-----------------        
,a.SysDocHazardousHistoryNo    
,a.DateExported    
,a.Exported      
,a.URNNumber  
,a.CanBeExported  
      
FROM    dbo.tbDebit a                
JOIN    dbo.tbCompany b   ON a.CompanyNo   = b.CompanyId                          
JOIN    dbo.tbContact c  ON a.ContactNo   = c.ContactId                          
LEFT JOIN    dbo.tbLogin d    ON a.RaisedBy   = d.LoginId                          
JOIN    dbo.tbLogin e    ON a.Buyer    = e.LoginId                          
JOIN    dbo.tbDivision f    ON a.DivisionNo  = f.DivisionId                          
JOIN    dbo.tbPurchaseOrder g   ON a.PurchaseOrderNo = g.PurchaseOrderId                
LEFT JOIN    dbo.tbInternalPurchaseOrder ipo   ON ipo.PurchaseOrderNo = g.PurchaseOrderId                          
LEFT JOIN dbo.tbSupplierRMA h  ON a.SupplierRMANo  = h.SupplierRMAId                          
JOIN    dbo.tbCurrency j  ON a.CurrencyNo  = j.CurrencyId                          
LEFT JOIN    dbo.tbTax k  ON a.TaxNo    = k.TaxId               
LEFT JOIN tbCompany cop ON ipo.CompanyNo = cop.CompanyId           
LEFT JOIN tbSystemDocumentFooterHistory FHstry ON FHstry.SystemDocumentFooterHistoryId=a.[SystemDocumentFooterHistoryNo] 
GO


