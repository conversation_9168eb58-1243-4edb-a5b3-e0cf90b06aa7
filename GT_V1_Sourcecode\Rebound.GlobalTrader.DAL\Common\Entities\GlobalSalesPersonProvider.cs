﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL {
	
	public abstract class GlobalSalesPersonProvider : DataAccess {
		static private GlobalSalesPersonProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public GlobalSalesPersonProvider Instance {
			get {
				if (_instance == null) _instance = (GlobalSalesPersonProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.GlobalSalesPersons.ProviderType));
				return _instance;
			}
		}
		public GlobalSalesPersonProvider() {
			this.ConnectionString = Globals.Settings.GlobalSalesPersons.ConnectionString;
		}

		#region Method Registrations
		/// <summary>
		/// GetListForSupplier
		/// Calls [usp_selectAll_ManufacturerLink_for_Supplier]
		/// </summary>
		public abstract List<GlobalSalesPersonDetails> GetListForGlobalSalesPerson(System.Int32? supplierCompanyNo, System.Int32? ClientNo=0);
		#endregion

		/// <summary>
		/// Insert
		/// Calls [usp_insert_SecurityGroupLogin]
		/// </summary>
		public abstract Int32 Insert(System.Int32? CompanyNo, System.Int32? loginNo, System.Int32? updatedBy, System.Int32? ClientNo);

		/// <summary>
		/// Delete
		/// Calls [usp_delete_SecurityGroupLogin]
		/// </summary>
		public abstract bool Delete(System.Int32? loginNo, System.Int32? CompannyNo, System.Int32? ClientNo);

	}
}