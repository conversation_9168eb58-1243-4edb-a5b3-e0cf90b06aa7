Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.initializeBase(this,[n]);this._intCompanyID=-1;this._intNewID=-1;this._ctlAddress=null};Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlAddress&&this._ctlAddress.dispose(),this._ctlAddress=null,this._intCompanyID=null,this._intNewID=null,Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.callBaseMethod(this,"dispose"))},formShown:function(){var n,t;this._blnFirstTimeShown&&(n=Function.createDelegate(this,this.saveClicked),$R_IBTN.addClick(this._ibtnSave,n),$R_IBTN.addClick(this._ibtnSave_Footer,n),t=Function.createDelegate(this,this.cancelClicked),$R_IBTN.addClick(this._ibtnCancel,t),$R_IBTN.addClick(this._ibtnCancel_Footer,t));this.setFormFieldsToDefaults();this.getFieldControl("ctlCompanyAddress")._intCompanyID=this._intCompanyID;this.getFieldDropDownData("ctlCompanyAddress")},saveClicked:function(){if(this.validateForm()&&this.validateEmail()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ContactsForCompany");n.set_DataObject("ContactsForCompany");n.set_DataAction("AddNew");n.addParameter("id",this._intCompanyID);n.addParameter("FirstName",this.getFieldValue("ctlFirstName"));n.addParameter("Surname",this.getFieldValue("ctlSurname"));n.addParameter("JobTitle",this.getFieldValue("ctlJobTitle"));n.addParameter("Tel",this.getFieldValue("ctlTel"));n.addParameter("Fax",this.getFieldValue("ctlFax"));n.addParameter("Extension",this.getFieldValue("ctlExtension"));n.addParameter("HomeTel",this.getFieldValue("ctlHomeTel"));n.addParameter("MobileTel",this.getFieldValue("ctlMobileTel"));n.addParameter("Email",this.getFieldValue("ctlEmail"));n.addParameter("TextOnlyEmail",this.getFieldValue("ctlTextOnlyEmail"));n.addParameter("Nickname",this.getFieldValue("ctlNickname"));n.addParameter("CompanyAddress",this.getFieldValue("ctlCompanyAddress"));n.addParameter("FinanceContact",this.getFieldValue("ctlFinanceContact"));n.addParameter("IsSendShipmentNotification",this.getFieldValue("ctlSendShipmentNotification"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(this._intNewID=n._result.NewID,this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return n||this.showError(!0),n},validateEmail:function(){return this.getFieldValue("ctlFinanceContact")==!0&&this.checkFieldEntered("ctlEmail")==!1?(this.showError(!0,$R_RES.ContactEmailMessage),!1):!0},cancelClicked:function(){this.onCancel()}};Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);