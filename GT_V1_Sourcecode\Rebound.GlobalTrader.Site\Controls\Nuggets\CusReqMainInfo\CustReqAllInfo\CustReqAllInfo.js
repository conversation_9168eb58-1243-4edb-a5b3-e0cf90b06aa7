Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.initializeBase(this,[n]);this._DocNo="";this._actionType="";this._DocId="";this.ReqDetailURL="Ord_CusReqDetail.aspx?req=";this.BOMDetailURL="Ord_BOMDetail.aspx?BOM=";this.QuoteDetailURL="Ord_QuoteDetail.aspx?qt=";this.SODetailURL="Ord_SODetail.aspx?so=";this.InvDetailURL="Ord_InvoiceDetail.aspx?inv=";this.PODetailURL="Ord_PODetail.aspx?po=";this.GIDetailURL="Whs_GIDetail.aspx?gi=";this.STKDetailURL="Whs_StockDetail.aspx?stk=";this.CRMADetailURL="Ord_CRMADetail.aspx?crma=";this.SRMADetailURL="Ord_SRMADetail.aspx?srma=";this.CreditDetailURL="Ord_CreditNoteDetail.aspx?crd=";this.DebitDetailURL="Ord_DebitNoteDetail.aspx?deb="};Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.prototype={get_intDocNo:function(){return this._DocNo},set_intDocNo:function(n){this._DocNo!==n&&(this._DocNo=n)},get_actionType:function(){return this._actionType},set_actionType:function(n){this._actionType!==n&&(this._actionType=n)},get_intDocId:function(){return this._DocId},set_intDocId:function(n){this._DocId!==n&&(this._DocId=n)},get_pnlCustReqAllDoc:function(){return this._pnlCustReqAllDoc},set_pnlCustReqAllDoc:function(n){this._pnlCustReqAllDoc!==n&&(this._pnlCustReqAllDoc=n)},get_pnlLoadingLineDetail:function(){return this._pnlLoadingLineDetail},set_pnlLoadingLineDetail:function(n){this._pnlLoadingLineDetail!==n&&(this._pnlLoadingLineDetail=n)},addStartGetData:function(n){this.get_events().addHandler("StartGetData",n)},removeStartGetData:function(n){this.get_events().removeHandler("StartGetData",n)},onStartGetData:function(){var n=this.get_events().getHandler("StartGetData");n&&n(this,Sys.EventArgs.Empty);alert(1)},addGotDataOK:function(n){this.get_events().addHandler("GotDataOK",n)},removeGotDataOK:function(n){this.get_events().removeHandler("GotDataOK",n)},onGotDataOK:function(){var n=this.get_events().getHandler("GotDataOK");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.callBaseMethod(this,"initialize");this.getData()},dispose:function(){this.isDisposed||(this._DocNo=null,this._pnlCustReqAllDoc=null,this._pnlLoadingLineDetail=null,this._DocId=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.callBaseMethod(this,"dispose"))},getData:function(){var n=new Rebound.GlobalTrader.Site.Data;this.showLoading(!0);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);n.set_PathToData("controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("CustomerAllInfo");n.addParameter("DocNo",this._DocNo);n.addParameter("ActionType",this._actionType);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result;$R_FN.setInnerHTML(this._pnlCustReqAllDoc,this.generateLink(t.Items));$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlContentLoading,!1);$R_FN.showElement(this._pnlContent,!0);this.showLoading(!1);this.onGotDataOK()},generateLink:function(n){var r="",t,u,f,e,o,s,h,c,l,a,v,y,p,i;switch(this._actionType){case"REQ":r="Requirement ("+this._DocId+")";break;case"BOM":r="HUBRFQ ("+this._DocId+")";break;case"Q":r="Quotes ("+this._DocId+")";break;case"SO":r="Sales Order ("+this._DocId+")";break;case"INV":r="Invoice ("+this._DocId+")";break;case"PO":r="Purchase Order ("+this._DocId+")";break;case"GI":r="Goods In ("+this._DocId+")";break;case"STK":r="Stock ("+this._DocId+")";break;case"CRMA":r="CRMA ("+this._DocId+")";break;case"SRMA":r="SRMA ("+this._DocId+")";break;case"CRD":r="Credit Note ("+this._DocId+")";break;case"DBT":r="Debit Note ("+this._DocId+")"}if(t='<ul><input class="toggle-box" id="identifier-1" type="checkbox" checked><label for="identifier-1">All Document Detail For '+r+"<\/label><div>",this._actionType!="REQ"){for(u=this.filter(n,"REQ"),t=t+'<ul><input class="toggle-box" id="identifier-2" type="checkbox" checked><label for="identifier-2">Requirement<\/label> <div>',i=0;i<u.length;i++)t=t+'<li><a href="'+this.ReqDetailURL+u[i].ID+'" target="_blank">'+u[i].Number+"<\/a><\/li>";t=t+" <\/div><\/ul>"}if(this._actionType!="BOM"){for(f=this.filter(n,"BOM"),t=t+'<ul><input class="toggle-box" id="identifier-3" type="checkbox" checked><label for="identifier-3">HUBRFQ<\/label> <div>',i=0;i<f.length;i++)t=t+'<li><a href="'+this.BOMDetailURL+f[i].ID+'" target="_blank">'+f[i].Number+"<\/a><\/li>";t=t+" <\/div><\/ul>"}if(this._actionType!="Q"){for(e=this.filter(n,"Q"),t=t+'<ul><input class="toggle-box" id="identifier-4" type="checkbox" checked><label for="identifier-4">Quote<\/label> <div>',i=0;i<e.length;i++)t=t+'<li><a href="'+this.QuoteDetailURL+e[i].ID+'" target="_blank">'+e[i].Number+"<\/a><\/li>";t=t+" <\/div><\/ul>"}if(this._actionType!="SO"){for(o=this.filter(n,"SO"),t=t+'<ul><input class="toggle-box" id="identifier-5" type="checkbox" checked><label for="identifier-5">Sales Order<\/label><div> ',i=0;i<o.length;i++)t=t+'<li><a href="'+this.SODetailURL+o[i].ID+'" target="_blank">'+o[i].Number+"<\/a><\/li> ";t=t+"<\/div> <\/ul>"}if(this._actionType!="INV"){for(s=this.filter(n,"INV"),t=t+'<ul><input class="toggle-box" id="identifier-6" type="checkbox" checked><label for="identifier-6">Invoice<\/label><div> ',i=0;i<s.length;i++)t=t+'<li><a href="'+this.InvDetailURL+s[i].ID+'" target="_blank">'+s[i].Number+"<\/a><\/li>";t=t+"<\/div><\/ul>"}if(this._actionType!="PO"){for(h=this.filter(n,"PO"),t=t+'<ul><input class="toggle-box" id="identifier-7" type="checkbox" checked><label for="identifier-7">Purchase Order<\/label><div> ',i=0;i<h.length;i++)t=t+'<li><a href="'+this.PODetailURL+h[i].ID+'" target="_blank">'+h[i].Number+"<\/a><\/li>";t=t+"<\/div><\/ul>"}if(this._actionType!="GI"){for(c=this.filter(n,"GI"),t=t+'<ul><input class="toggle-box" id="identifier-8" type="checkbox" checked><label for="identifier-8">Goods In<\/label><div> ',i=0;i<c.length;i++)t=t+'<li><a href="'+this.GIDetailURL+c[i].ID+'" target="_blank">'+c[i].Number+"<\/a><\/li>";t=t+"<\/div><\/ul>"}if(this._actionType!="STK"){for(l=this.filter(n,"STK"),t=t+'<ul><input class="toggle-box" id="identifier-9" type="checkbox" checked><label for="identifier-9">Stock<\/label><div> ',i=0;i<l.length;i++)t=t+'<li><a href="'+this.STKDetailURL+l[i].ID+'" target="_blank">'+l[i].Number+"<\/a><\/li>";t=t+"<\/div><\/ul>"}if(this._actionType!="CRMA"){for(a=this.filter(n,"CRMA"),t=t+'<ul><input class="toggle-box" id="identifier-10" type="checkbox" checked><label for="identifier-10">CRMA<\/label><div> ',i=0;i<a.length;i++)t=t+'<li><a href="'+this.CRMADetailURL+a[i].ID+'" target="_blank">'+a[i].Number+"<\/a><\/li>";t=t+"<\/div><\/ul>"}if(this._actionType!="SRMA"){for(v=this.filter(n,"SRMA"),t=t+'<ul><input class="toggle-box" id="identifier-11" type="checkbox" checked><label for="identifier-11">SRMA<\/label><div> ',i=0;i<v.length;i++)t=t+'<li><a href="'+this.SRMADetailURL+v[i].ID+'" target="_blank">'+v[i].Number+"<\/a><\/li>";t=t+"<\/div><\/ul>"}if(this._actionType!="CRD"){for(y=this.filter(n,"CRDT"),t=t+'<ul><input class="toggle-box" id="identifier-12" type="checkbox" checked><label for="identifier-12">Credit Note<\/label><div> ',i=0;i<y.length;i++)t=t+'<li><a href="'+this.CreditDetailURL+y[i].ID+'" target="_blank">'+y[i].Number+"<\/a><\/li>";t=t+"<\/div><\/ul>"}if(this._actionType!="DBT"){for(p=this.filter(n,"DBT"),t=t+'<ul><input class="toggle-box" id="identifier-13" type="checkbox" checked><label for="identifier-13">Debit Note<\/label><div> ',i=0;i<p.length;i++)t=t+'<li><a href="'+this.DebitDetailURL+p[i].ID+'" target="_blank">'+p[i].Number+"<\/a><\/li>";t=t+"<\/div><\/ul>"}return t+"<\/div> <\/ul>"},filter:function(n,t){for(var r=[],u=n.length,i=0;i<u;i++)n[i].ResultType==t&&r.push(n[i]);return r},getDataError:function(n){this.showLoading(!1);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlLineDetailError,!0);$R_FN.setInnerHTML(this._pnlLineDetailError,n.get_ErrorMessage())}};Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base,Sys.IDisposable);