﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL {
	
	public abstract class PrinterProvider : DataAccess {
		static private PrinterProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public PrinterProvider Instance {
			get {
				if (_instance == null) _instance = (PrinterProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.Printer.ProviderType));
				return _instance;
			}
		}
        public PrinterProvider()
        {
			this.ConnectionString = Globals.Settings.Printer.ConnectionString;
		}

		#region Method Registrations
				
		/// <summary>
        /// DropDownForPrinter
        /// Calls [usp_dropdown_PrinterAll]
		/// </summary>
        public abstract List<PrinterDetails> DropDownForPrinter(System.Int32? clientId);

        /// <summary>
        /// Insert
        /// Calls [usp_insert_Printer]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract Int32 Insert(System.Int32? clientNo, System.String printerName, System.String description, System.Boolean? inActive, System.Int32? updatedBy);
        /// <summary>
        /// Update
        /// Call [usp_update_Printer]
        /// </summary>
        /// <param name="printerId"></param>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract bool Update(System.Int32? printerId, System.Int32? clientNo, System.String printerName, System.String description, System.Boolean? inActive, System.Int32? updatedBy);
        /// <summary>
        /// Call [usp_selectAll_Printer_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract List<PrinterDetails> GetListForClient(System.Int32? clientId);

        /// <summary>
        /// GetListLabelSetup
        /// Calls [usp_selectAll_LabelSetup]
        /// </summary>
        public abstract List<PrinterDetails> GetListLabelSetup();

        /// <summary>
        /// Pass the primary key of the table tbListSetup
        /// Calls [usp_selectAll_LabelSetupItem]
        /// </summary>
        public abstract List<PrinterDetails> GetListLabelSetupItem(System.Int32? labelSetupId);
        /// <summary>
        /// Insert
        /// Calls [usp_insert_LabelSetupItem]
        /// </summary>
        public abstract Int32 InsertLabelSetupItem(System.Int32? labelSetupId, System.String name, System.String email, System.Int32? updatedBy);

        /// <summary>
        /// Update
        /// Calls [usp_update_LabelSetupItem]
        /// </summary>
        public abstract bool UpdateLabelSetupItem(System.Int32? labelsetupItemId, System.Int32? labelSetupId, System.String name, System.String email, System.Int32? updatedBy, System.Boolean? inactive);

        /// <summary>
        /// Insert
        /// Calls [usp_insert_Printer]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="manufactureNo"></param>
        /// <param name="notes"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract Int32 InsertMfr(System.Int32? clientNo, System.Int32? manufactureNo, System.String notes, System.Boolean? inActive, System.Int32? updatedBy, System.Int32? ManufacturerCount, System.String ManufactureName);
        
        public abstract System.String InsertMultipleManufacturers(System.Int32? clientNo, System.String manufactureNos, System.String mfrNameSuffix, System.String notes, System.Boolean? inActive, System.Int32? updatedBy);

        /// <summary>
        /// Update
        /// Call [usp_update_Printer]
        /// </summary>
        /// <param name="RestrictedManufacturerId"></param>
        /// <param name="clientNo"></param>
        /// <param name="manufactureNo"></param>
        /// <param name="Notes"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract bool UpdateMfr(System.Int32? RestrictedManufacturerId, System.Int32? clientNo, System.Int32? manufactureNo, System.String notes, System.Boolean? inActive, System.Int32? updatedBy, System.String manufactureName, System.Int32 manufactureCount);

        public abstract bool UpdateMultipleMfr(System.Int32? clientNo, System.String manufacturerNos, System.String mfrNameSuffix, System.String notes, System.Boolean? inActive, System.Int32? updatedBy);

        public abstract bool ActivateMultipleMfr(System.Int32? clientNo, System.String manufacturerNos, System.Boolean? inActive, System.Int32? updatedBy);
        /// <summary>
        /// Call [usp_selectAll_Printer_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract List<PrinterDetails> GetListForRestrictedManufacturer(System.Int32? clientId);

        public abstract List<PrinterDetails> GetItemsByMfrIds(System.Int32? clientId, System.String manufacturerNos);

        /// <summary>
		/// Get
		/// Calls [usp_Select_RestrictedManufacture]
		/// </summary>
		public abstract PrinterDetails Get(System.Int32? clientNo, System.Int32? manufactureNo);
        /// <summary>
		/// GetRestrictedManufacture
		/// Calls [usp_Select_Search_for_RestrictedManufacture]
		/// </summary>
		public abstract PrinterDetails GetRestrictedManufacture(System.Int32? clientNo, System.Int32? manufactureNo);


        /// <summary>
        /// Call [usp_selectAll_Printer_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract List<PrinterDetails> GetListForECCNCode(System.Int32? clientId, System.Boolean NotifyStatus, System.String SearchType);


        /// <summary>
        /// Call [usp_selectAll_Printer_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract List<PrinterDetails> GetListForECCNCodeMap(System.Int32? clientId);


        /// <summary>
        /// [usp_insert_ECCN]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="EccnCode"></param>
        /// <param name="Notes"></param>
        /// <param name="EccnStatus"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract Int32 InsertEccn(System.Int32? clientNo, System.String ECCNCode, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy);

        /// <summary>
        /// [usp_insert_ECCN]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="EccnCode"></param>
        /// <param name="Notes"></param>
        /// <param name="EccnStatus"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract Int32 InsertEccnClone(System.Int32? ECCNCloneId,System.Int32? clientNo, System.String ECCNCode, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy);

        /// <summary>
        /// Calls [usp_update_ECCN]
        /// </summary>
        /// <param name="ECCNId"></param>
        /// <param name="clientNo"></param>
        /// <param name="Notes"></param>
        /// <param name="EccnStatus"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract bool UpdateEccn(System.Int32? ECCNId, System.Int32? clientNo, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy);

        public abstract bool UpdateMapEccn(System.Int32? clientNo, System.String SelectedEccnIds, System.String EccnWarningMessage, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Int32? updatedBy);



        /// <summary>
        /// Insert
        /// Calls [usp_insert_Printer]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract Int32 InsertPVV(System.Int32? clientNo,  System.String description, System.Boolean? inActive, System.Int32? updatedBy);
        /// <summary>
        /// Update
        /// Call [usp_update_Printer]
        /// </summary>
        /// <param name="printerId"></param>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract bool UpdatePVV(System.Int32? printerId, System.Int32? clientNo,  System.String description, System.Boolean? inActive, System.Int32? updatedBy);

        /// <summary>
        /// Call [usp_selectAll_Printer_for_Client]
        /// </summary>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract List<PrinterDetails> GetListForPVV(System.Int32? clientId);
        #endregion

    }
}