Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlReceivingInfo:function(){return this._ctlReceivingInfo},set_ctlReceivingInfo:function(n){this._ctlReceivingInfo!==n&&(this._ctlReceivingInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlReceivingInfo&&this._ctlReceivingInfo.addGetDataComplete(Function.createDelegate(this,this.ctlReceivingInfo_GetDataComplete));this._ctlLines&&this._ctlLines.addSaveReceiveComplete(Function.createDelegate(this,this.ctlLines_SaveReceiveComplete));Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlReceivingInfo&&this._ctlReceivingInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._ctlPageTitle=null,this._ctlReceivingInfo=null,this._ctlLines=null,this._intCRMAID=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.callBaseMethod(this,"dispose"))},ctlReceivingInfo_GetDataComplete:function(){this._ctlLines._frmReceive&&this._ctlLines._frmReceive.setFieldsFromHeader(this._ctlReceivingInfo.getFieldValue("hidNo"),this._ctlReceivingInfo.getFieldValue("hidCustomer"),this._ctlReceivingInfo.getFieldValue("hidCustomerNo"),this._ctlReceivingInfo.getFieldValue("hidWarehouseNo"),this._ctlReceivingInfo.getFieldValue("hidShipViaNo"),this._ctlReceivingInfo.getFieldValue("hidCurrencyNo"),this._ctlReceivingInfo.getFieldValue("ctlCurrency"));this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlReceivingInfo.getFieldValue("hidGlobalClientNo"):null;this._ctlReceivingInfo._IsGlobalLogin=this._IsGlobalLogin},ctlLines_SaveReceiveComplete:function(){this._ctlReceivingInfo.getData()}};Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);