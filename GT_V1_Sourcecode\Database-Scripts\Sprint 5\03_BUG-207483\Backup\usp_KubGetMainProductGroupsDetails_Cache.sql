
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubGetMainProductGroupsDetails_Cache]   
@PartNo NVARCHAR(100)=null,  
@ClientID int=null 
/*
 * Action: Created		By: <PERSON><PERSON><PERSON><PERSON>		Date:03/08/2023		Comment: Add new table for holding the product details cache for KUB.
 */ 
AS    
BEGIN    
SET NOCOUNT ON;
IF((SELECT COUNT(1) FROM tb_KubGetMainProductGroupsDetailsCache WHERE Part=@PartNo AND ClientNo=@ClientID 
AND DLUP>DATEAdd(DAY,-3,GETDATE()))=0)
BEGIN
DELETE FROM tb_KubGetMainProductGroupsDetailsCache WHERE Part=@PartNo AND ClientNo=@ClientID 

INSERT INTO tb_KubGetMainProductGroupsDetailsCache    
SELECT DISTINCT 
@ClientID,
@PartNo,
pr.ProductName,
GETDATE()  
FROM tbCustomerRequirement cr  
JOIN dbo.tbProduct pr WITH (NOLOCK) ON pr.ProductId = cr.ProductNo  
Left join tbGlobalProductName gp on gp.GlobalProductNameId=pr.GlobalProductNo  
WHERE pr.ClientNo=@ClientID AND  
cr.FullPart =@PartNo  
AND (cr.DatePromised)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)
END 
SET NOCOUNT OFF;  
END 
GO


