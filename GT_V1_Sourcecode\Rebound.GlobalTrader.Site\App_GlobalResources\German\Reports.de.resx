﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActiveVendors" xml:space="preserve">
    <value>Aktive Verkäufer</value>
  </data>
  <data name="AirWayBill" xml:space="preserve">
    <value>Luft-Weise Bill</value>
  </data>
  <data name="AllocatedCost" xml:space="preserve">
    <value>Kosten</value>
  </data>
  <data name="AllocatedResale" xml:space="preserve">
    <value>Zugeteilter Weiterverkauf</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Anerkannte Kunden auf Anschlag</value>
  </data>
  <data name="AuthorisedBy" xml:space="preserve">
    <value>Vorbei autorisiert</value>
  </data>
  <data name="AutoEnteredSuppliers_Unedited" xml:space="preserve">
    <value>Selbst-Eingegebene Lieferanten (unveröffentlicht)</value>
  </data>
  <data name="Average" xml:space="preserve">
    <value>Durchschnitt</value>
  </data>
  <data name="BackOrderQuantity" xml:space="preserve">
    <value>Auftrags-Quantität</value>
  </data>
  <data name="BackOrderValue" xml:space="preserve">
    <value>Auftrags-Wert</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="BaseValue" xml:space="preserve">
    <value>Niedriger Wert</value>
  </data>
  <data name="BookedSales" xml:space="preserve">
    <value>Angemeldete Verkäufe</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>Kaufer</value>
  </data>
  <data name="BuyInCost" xml:space="preserve">
    <value>Kauf in den Kosten</value>
  </data>
  <data name="BuyInPrice" xml:space="preserve">
    <value>Kauf im Preis</value>
  </data>
  <data name="BuyInValue" xml:space="preserve">
    <value>Kauf im Wert</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>Stadt</value>
  </data>
  <data name="ClosedRequirements" xml:space="preserve">
    <value>Geschlossene Anforderungen</value>
  </data>
  <data name="ClosedRequirementsReasons" xml:space="preserve">
    <value>Closed Requirements Reasons</value>
  </data>
  <data name="CommunicationLogActivityforaUser" xml:space="preserve">
    <value>Communication Log Activity for a User</value>
  </data>
  <data name="CompaniesApprovedToPurchaseFrom" xml:space="preserve">
    <value>Firmen anerkannt, um von zu kaufen</value>
  </data>
  <data name="CompaniesNotContacted" xml:space="preserve">
    <value>Companies Not Contacted</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Firma</value>
  </data>
  <data name="CompanyGrouping" xml:space="preserve">
    <value>Grouping?</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Firmennamen</value>
  </data>
  <data name="CompanyTax" xml:space="preserve">
    <value>Firma-Steuer</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Firma-Art</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Bestätigt</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Kontakt</value>
  </data>
  <data name="ContactEmailList" xml:space="preserve">
    <value>Kontakt-eMail-Liste</value>
  </data>
  <data name="ContactFax" xml:space="preserve">
    <value>Kontakt-Telefax</value>
  </data>
  <data name="ContactName" xml:space="preserve">
    <value>Kontakt-Name</value>
  </data>
  <data name="ContactsNotContacted" xml:space="preserve">
    <value>Kontakte nicht in Verbindung getreten</value>
  </data>
  <data name="ContactTelephone" xml:space="preserve">
    <value>Kontakt-Telefon</value>
  </data>
  <data name="ContactType" xml:space="preserve">
    <value>Kontakt-Art</value>
  </data>
  <data name="Cost" xml:space="preserve">
    <value>Kosten</value>
  </data>
  <data name="CostInBase" xml:space="preserve">
    <value>Kosten in der Unterseite</value>
  </data>
  <data name="Count" xml:space="preserve">
    <value>Zählimpuls</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Land</value>
  </data>
  <data name="CountryOfImport" xml:space="preserve">
    <value>Land des Importes </value>
  </data>
  <data name="CountryOfManufacture" xml:space="preserve">
    <value>Land der Fertigung</value>
  </data>
  <data name="County" xml:space="preserve">
    <value>Grafschaft</value>
  </data>
  <data name="CreditDate" xml:space="preserve">
    <value>Gutschrift-Datum</value>
  </data>
  <data name="CreditLimit" xml:space="preserve">
    <value>Kreditlinie</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Kreditnoten</value>
  </data>
  <data name="CreditNotesforaCustomer" xml:space="preserve">
    <value>Kreditnoten für einen Kunden</value>
  </data>
  <data name="CreditNotesforaSalesperson" xml:space="preserve">
    <value>Kreditnoten für einen Verkäufer</value>
  </data>
  <data name="CreditNumber" xml:space="preserve">
    <value>Gutschrift-Zahl</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Währung</value>
  </data>
  <data name="CurrencyRate" xml:space="preserve">
    <value>Währungsstabilität</value>
  </data>
  <data name="CurrencyValue" xml:space="preserve">
    <value>Währung-Wert</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Kunde</value>
  </data>
  <data name="CustomerCode" xml:space="preserve">
    <value>Kunden-Code</value>
  </data>
  <data name="CustomerListforSalesperson" xml:space="preserve">
    <value>Kunden-Liste für Verkäufer</value>
  </data>
  <data name="CustomerOnTimeDeliveryReport" xml:space="preserve">
    <value>Kunde auf Zeit-Anlieferungs-Report</value>
  </data>
  <data name="CustomerPart" xml:space="preserve">
    <value>Kunden-Teil</value>
  </data>
  <data name="CustomerPO" xml:space="preserve">
    <value>Kunde PO</value>
  </data>
  <data name="CustomerReturnValue" xml:space="preserve">
    <value>Kunden-Rückholwert</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Kunde RMA</value>
  </data>
  <data name="CustomerRMADate" xml:space="preserve">
    <value>Kunde RMA Datum</value>
  </data>
  <data name="CustomerRMANumber" xml:space="preserve">
    <value>Kunde RMA Nr</value>
  </data>
  <data name="CustomerRMAs" xml:space="preserve">
    <value>Kunde RMAs</value>
  </data>
  <data name="CustomerStatement" xml:space="preserve">
    <value>Kunden-Aussage</value>
  </data>
  <data name="CutOffDate" xml:space="preserve">
    <value>Schneiden Sie Datum ab</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Detailed" xml:space="preserve">
    <value>Tägliche Kunden-Anforderungen durch Salesperson (einzeln aufgeführt)</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Summary" xml:space="preserve">
    <value>Tägliche Kunden-Anforderungen durch Salesperson (Zusammenfassung)</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Totals" xml:space="preserve">
    <value>Tägliche Kunden-Anforderungen durch Salesperson (Gesamtmengen)</value>
  </data>
  <data name="DailyImports" xml:space="preserve">
    <value>Tägliche Importe</value>
  </data>
  <data name="DailyImportsBySource" xml:space="preserve">
    <value>Tägliche Importe durch Source</value>
  </data>
  <data name="DatabaseManagement" xml:space="preserve">
    <value>Datenbank-Verwaltung</value>
  </data>
  <data name="DateAndLogin" xml:space="preserve">
    <value>{0} by {1}</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>Datum-Code</value>
  </data>
  <data name="DateOrdered" xml:space="preserve">
    <value>Datum bestellt</value>
  </data>
  <data name="DatePromised" xml:space="preserve">
    <value>Datum versprochen</value>
  </data>
  <data name="DateReceived" xml:space="preserve">
    <value>Datum empfing</value>
  </data>
  <data name="DaysSinceInvoice" xml:space="preserve">
    <value>Tage seit Rechnung</value>
  </data>
  <data name="DaysSinceLastContact" xml:space="preserve">
    <value>Tage, da letzt in Verbindung treten Sie</value>
  </data>
  <data name="DaysSinceLastInvoicebyContact" xml:space="preserve">
    <value>Tage seit letzte Rechnung durch Kontakt</value>
  </data>
  <data name="DaysSinceLastInvoicebyCustomer" xml:space="preserve">
    <value>Tage seit letzter Rechnung durch Kunde</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Lieferfrist</value>
  </data>
  <data name="Destination" xml:space="preserve">
    <value>Lieferfrist</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Abteilung</value>
  </data>
  <data name="DivisionName" xml:space="preserve">
    <value>Abteilungs-Name</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Dokument</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Abgabefrist</value>
  </data>
  <data name="Duration" xml:space="preserve">
    <value>Dauer</value>
  </data>
  <data name="DutyCode" xml:space="preserve">
    <value>Aufgaben-Code</value>
  </data>
  <data name="EMail" xml:space="preserve">
    <value>EM@il</value>
  </data>
  <data name="Employee" xml:space="preserve">
    <value>Angestellter</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>Angestellt-Name</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>Enddatum</value>
  </data>
  <data name="EndTime" xml:space="preserve">
    <value>Enden-Zeit</value>
  </data>
  <data name="ExcludeCustomersOnStop" xml:space="preserve">
    <value>Schließen Sie Kunden auf Anschlag aus?</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Vorname</value>
  </data>
  <data name="Freight" xml:space="preserve">
    <value>Fracht</value>
  </data>
  <data name="General" xml:space="preserve">
    <value>Allgemein</value>
  </data>
  <data name="GINumber" xml:space="preserve">
    <value>Waren zahlreich</value>
  </data>
  <data name="GoodsReceived" xml:space="preserve">
    <value>Waren empfangen</value>
  </data>
  <data name="GoodsReceivedNotInvoiced" xml:space="preserve">
    <value>Waren empfingen nicht fakturiert</value>
  </data>
  <data name="GoodsReceivedShipmentDetails" xml:space="preserve">
    <value>Waren empfingen Versand-Details</value>
  </data>
  <data name="GoodsValuation" xml:space="preserve">
    <value>Waren-Schätzung</value>
  </data>
  <data name="GPRange" xml:space="preserve">
    <value>Bruttogewinn-Strecke</value>
  </data>
  <data name="GrossProfit" xml:space="preserve">
    <value>Bruttogewinn</value>
  </data>
  <data name="GrossProfitBreakdown" xml:space="preserve">
    <value>Bruttogewinn-Zusammenbruch</value>
  </data>
  <data name="GrossProfitPercentage" xml:space="preserve">
    <value>Bruttogewinn %</value>
  </data>
  <data name="HarmonisedCode" xml:space="preserve">
    <value>Harmonisierter Code</value>
  </data>
  <data name="ImportDate" xml:space="preserve">
    <value>Import-Datum</value>
  </data>
  <data name="ImportName" xml:space="preserve">
    <value>Import-Name</value>
  </data>
  <data name="InAdvance" xml:space="preserve">
    <value>Im Voraus</value>
  </data>
  <data name="IncludeCredits" xml:space="preserve">
    <value>Schließen Sie Gutschriften ein?</value>
  </data>
  <data name="IncludeLotsOnHold" xml:space="preserve">
    <value>Schließen Sie Lose auf Einfluss ein?</value>
  </data>
  <data name="IncludeNever" xml:space="preserve">
    <value>Schließen Sie nie ein?</value>
  </data>
  <data name="IncludeOnOrder" xml:space="preserve">
    <value>Schließen Sie auf Auftrag ein?</value>
  </data>
  <data name="IncludeShipping" xml:space="preserve">
    <value>Schließen Sie Verschiffen ein?</value>
  </data>
  <data name="IncludeUnconfirmed" xml:space="preserve">
    <value>Schließen Sie unbestätigtes mit ein?</value>
  </data>
  <data name="IncludeUnpaid" xml:space="preserve">
    <value>Schließen Sie unbezahltes mit ein?</value>
  </data>
  <data name="InStock" xml:space="preserve">
    <value>Auf Lager</value>
  </data>
  <data name="IntrastatReportforEECArrivals_CustomerRMAs" xml:space="preserve">
    <value>Intrastat berichten für EWG-Ankünfte (Kunde RMAs)</value>
  </data>
  <data name="IntrastatReportforEECArrivals_Purchases" xml:space="preserve">
    <value>Intrastat berichten für EWG-Ankünfte (Käufe)</value>
  </data>
  <data name="IntrastatReportforEECDispatches_Sales" xml:space="preserve">
    <value>Intrastat berichten für EWG-Abfertigungen (Verkäufe)</value>
  </data>
  <data name="IntrastatReportforEECDispatches_SupplierRMAs" xml:space="preserve">
    <value>Intrastat berichten für EWG-Abfertigungen (Lieferant RMAs)</value>
  </data>
  <data name="InventoryLocationReport" xml:space="preserve">
    <value>Warenbestand-Positions-Report</value>
  </data>
  <data name="InventoryLocationReportforLot" xml:space="preserve">
    <value>Warenbestand-Positions-Report für Los</value>
  </data>
  <data name="InvoiceCost" xml:space="preserve">
    <value>Fakturenpreis</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Rechnungs-Datum</value>
  </data>
  <data name="InvoiceLineAllocationName" xml:space="preserve">
    <value>InvoiceLine Verteilungs-Name</value>
  </data>
  <data name="InvoiceNumber" xml:space="preserve">
    <value>Rechnungs-Nr</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Rechnungen</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumber" xml:space="preserve">
    <value>Rechnungen sortiert durch Rechnungs-Nr</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Rechnungen sortiert durch Rechnungs-Nr für einen Kunden</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Rechnungen sortiert durch Rechnungs-Nr für einen Verkäufer</value>
  </data>
  <data name="InvoiceValuation" xml:space="preserve">
    <value>Rechnungs-Schätzung</value>
  </data>
  <data name="InvoiceValue" xml:space="preserve">
    <value>Fakturenwert</value>
  </data>
  <data name="Invoicing" xml:space="preserve">
    <value>Fakturierung</value>
  </data>
  <data name="IPAddress" xml:space="preserve">
    <value>IP Adresse</value>
  </data>
  <data name="LandedCost" xml:space="preserve">
    <value>Gelandete Kosten</value>
  </data>
  <data name="LastContactDate" xml:space="preserve">
    <value>Letztes Kontakt-Datum</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nachname</value>
  </data>
  <data name="Late1DayPct" xml:space="preserve">
    <value>Spät 1 Tag %</value>
  </data>
  <data name="Late2To4DaysPct" xml:space="preserve">
    <value>Spät 2 bis 4 Tage %</value>
  </data>
  <data name="Late5DaysOrMorePct" xml:space="preserve">
    <value>Spät 5 Tage oder mehr %</value>
  </data>
  <data name="LateOnly" xml:space="preserve">
    <value>Spät nur</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="LoginName" xml:space="preserve">
    <value>Login Name</value>
  </data>
  <data name="LoginStatistics" xml:space="preserve">
    <value>Login Statistiken</value>
  </data>
  <data name="LoginStatisticsbyName" xml:space="preserve">
    <value>Login Statistiken namentliche</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Los</value>
  </data>
  <data name="LotCode" xml:space="preserve">
    <value>Los-Code</value>
  </data>
  <data name="Management" xml:space="preserve">
    <value>Management</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Hersteller</value>
  </data>
  <data name="Margin" xml:space="preserve">
    <value>Seitenrand</value>
  </data>
  <data name="Never" xml:space="preserve">
    <value />
    <comment>**BLANK**</comment>
  </data>
  <data name="NoOfAccounts" xml:space="preserve">
    <value>Nr. Rechnungsprüfungen</value>
  </data>
  <data name="NoOfCredits" xml:space="preserve">
    <value>Nr. Gutschriften</value>
  </data>
  <data name="NoOfCustomers" xml:space="preserve">
    <value>Nr. Kunden</value>
  </data>
  <data name="NoOfGoodsIn" xml:space="preserve">
    <value>Nr. Waren innen</value>
  </data>
  <data name="NoOfInvoices" xml:space="preserve">
    <value>Nr. Rechnungen</value>
  </data>
  <data name="NoOfItems" xml:space="preserve">
    <value>Nr. Einzelteile</value>
  </data>
  <data name="NoOfOffers" xml:space="preserve">
    <value>Nr. Angebote</value>
  </data>
  <data name="NoOfOffersHistory" xml:space="preserve">
    <value>Nr. Angebot-Geschichte</value>
  </data>
  <data name="NoOfOrders" xml:space="preserve">
    <value>Nr. Aufträge</value>
  </data>
  <data name="NoOfRequirements" xml:space="preserve">
    <value>Nr. Anforderungen</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NumberofAccountsbySalesperson" xml:space="preserve">
    <value>Zahl Rechnungsprüfungen durch Salesperson</value>
  </data>
  <data name="NumberofOffersbyVendor" xml:space="preserve">
    <value>Zahl von Angeboten durch Vendor</value>
  </data>
  <data name="NumberofOffersHistorybyVendor" xml:space="preserve">
    <value>Zahl der Angebot-Geschichte durch Vendor</value>
  </data>
  <data name="NumberofRequirementsbyVendor" xml:space="preserve">
    <value>Number of Requirements by Vendor</value>
  </data>
  <data name="Offers" xml:space="preserve">
    <value>Angebote</value>
  </data>
  <data name="OnStop" xml:space="preserve">
    <value>Auf Anschlag</value>
  </data>
  <data name="OnTimePct" xml:space="preserve">
    <value>Rechtzeitig %</value>
  </data>
  <data name="OpenCustomerRMAs" xml:space="preserve">
    <value>Öffnen Sie Kunden RMAs</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Öffnen Sie Kunden RMAs für einen Kunden</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Open Customer RMAs for a Customer with Reasons</value>
  </data>
  <data name="OpenCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Öffnen Sie Kunden RMAs für ein Verkäufer</value>
  </data>
  <data name="OpenCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Öffnen Sie Kunden RMAs für ein Verkäufermit Gründen</value>
  </data>
  <data name="OpenCustomerRMAswithReasons" xml:space="preserve">
    <value>Öffnen Sie Kunden RMAs mit Gründen</value>
  </data>
  <data name="OpenPurchaseOrders" xml:space="preserve">
    <value>Öffnen Sie Kaufaufträge</value>
  </data>
  <data name="OpenRequirementsbyCustomer" xml:space="preserve">
    <value>Öffnen Sie Anforderungen durch Customer</value>
  </data>
  <data name="OpenRequirementsReportBySalesperson" xml:space="preserve">
    <value> deutsch

Öffnen Sie Anforderungs-Report durch Salesperson</value>
  </data>
  <data name="OpenSales" xml:space="preserve">
    <value>Öffnen Sie Verkäufe</value>
  </data>
  <data name="OpenSalesOrders" xml:space="preserve">
    <value>Öffnen Sie Verkaufs-Aufträge</value>
  </data>
  <data name="OpenSalesOrdersforSalesperson" xml:space="preserve">
    <value>Öffnen Sie Verkaufs-Aufträge für Verkäufer</value>
  </data>
  <data name="OpenSupplierRMAs" xml:space="preserve">
    <value>Öffnen Sie Lieferanten RMAs</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Öffnen Sie Lieferanten RMAs für einen Kunden</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Öffnen Sie Lieferanten RMAs für einen Kunden mit Gründen</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Öffnen Sie Lieferanten RMAs für einen Lieferanten</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Öffnen Sie Lieferanten RMAs für einen Lieferanten mit Gründen</value>
  </data>
  <data name="OpenSupplierRMAswithReasons" xml:space="preserve">
    <value>Öffnen Sie Lieferanten RMAs mit Gründen</value>
  </data>
  <data name="OrdersPercentage" xml:space="preserve">
    <value>Bestellt %</value>
  </data>
  <data name="OrdersToBeShipped" xml:space="preserve">
    <value>Zu versendende Aufträge</value>
  </data>
  <data name="OrdersToBeShippedBySalesperson" xml:space="preserve">
    <value>Von Verkäufer zu versendende Aufträge</value>
  </data>
  <data name="OustandingCustomerInvoices" xml:space="preserve">
    <value>Oustanding Kunden-Rechnungen</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Paket</value>
  </data>
  <data name="PackageUnit" xml:space="preserve">
    <value>Paket-Maßeinheit</value>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Zahlend</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>Teil</value>
  </data>
  <data name="PartOrdered" xml:space="preserve">
    <value>Teil bestellt</value>
  </data>
  <data name="PhysicalCount" xml:space="preserve">
    <value>Körperlicher Zählimpuls</value>
  </data>
  <data name="PickSheetSalesOrdersBasic" xml:space="preserve">
    <value>Auswahl-Blatt - Verkaufs-Aufträge grundlegend</value>
  </data>
  <data name="PickSheetSalesOrdersDetailed" xml:space="preserve">
    <value>Auswahl-Blatt - Verkaufs-Aufträge führten einzeln auf</value>
  </data>
  <data name="PickSheetSupplierRMAs" xml:space="preserve">
    <value>Auswahl-Blatt - Lieferant RMAs</value>
  </data>
  <data name="PickUp" xml:space="preserve">
    <value>Heben Sie Zeit auf</value>
  </data>
  <data name="POApproved" xml:space="preserve">
    <value>PO Anerkannt</value>
  </data>
  <data name="POConfirmed" xml:space="preserve">
    <value>Bestätigt?</value>
  </data>
  <data name="PONumber" xml:space="preserve">
    <value>Kaufvertragsnummer</value>
  </data>
  <data name="PORating" xml:space="preserve">
    <value>PO Bewertung</value>
  </data>
  <data name="PostedOnly" xml:space="preserve">
    <value>Nur informierte Linien?</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Preis</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Produkt</value>
  </data>
  <data name="ProductDescription" xml:space="preserve">
    <value>Produkt-Beschreibung</value>
  </data>
  <data name="PurchaseOrdersDueIn" xml:space="preserve">
    <value>Kaufaufträge passend innen</value>
  </data>
  <data name="PurchaseOrdersDueInforBuyer" xml:space="preserve">
    <value>Kaufaufträge passend innen für Kunden</value>
  </data>
  <data name="PurchaseOrdersDueInforSalesperson" xml:space="preserve">
    <value>Kaufaufträge passend innen für Verkäufer</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Kauf-Forderungen</value>
  </data>
  <data name="PurchaseRequisitionsforaCustomer" xml:space="preserve">
    <value>Kauf-Forderungen für einen Kunden</value>
  </data>
  <data name="PurchaseRequisitionsforaSalesPerson" xml:space="preserve">
    <value>Kauf-Forderungen für eine Verkaufs-Person</value>
  </data>
  <data name="PurchaseValue" xml:space="preserve">
    <value>Kauf-Wert</value>
  </data>
  <data name="Purchasing" xml:space="preserve">
    <value>Kauf</value>
  </data>
  <data name="QualityControlNotes" xml:space="preserve">
    <value>Qualitätskontrolle-Anmerkungen</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantität</value>
  </data>
  <data name="QuantityAllocated" xml:space="preserve">
    <value>Quantität zugeteilt</value>
  </data>
  <data name="QuantityInStock" xml:space="preserve">
    <value>Quantität auf Lager</value>
  </data>
  <data name="QuantityOnOrder" xml:space="preserve">
    <value>Quantität auf Auftrag</value>
  </data>
  <data name="QuantityOrdered" xml:space="preserve">
    <value>Quantität bestellt</value>
  </data>
  <data name="RealPrice" xml:space="preserve">
    <value>Realer Preis</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Grund</value>
  </data>
  <data name="ReceivedCustomerRMAs" xml:space="preserve">
    <value>Empfangener Kunde RMAs</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Empfangener Kunde RMAs für einen Kunden</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Empfangener Kunde RMAs für einen Kunden mit Gründen</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Empfangener Kunde RMAs für ein Verkäufer</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Empfangener Kunde RMAs für ein Verkäufer mit Gründen</value>
  </data>
  <data name="ReceivedCustomerRMAswithReasons" xml:space="preserve">
    <value>Empfangener Kunde RMAs mit Gründen</value>
  </data>
  <data name="ReceivedDate" xml:space="preserve">
    <value>Empfangenes Datum</value>
  </data>
  <data name="ReceivedGoodsValuationbyCountry" xml:space="preserve">
    <value>Empfangene Waren-Schätzung durch Land</value>
  </data>
  <data name="Receiving" xml:space="preserve">
    <value>Empfangen</value>
  </data>
  <data name="ReceivingNotes" xml:space="preserve">
    <value>Frachtannahmescheine</value>
  </data>
  <data name="Requirements" xml:space="preserve">
    <value>Anforderungen</value>
  </data>
  <data name="Requisitions" xml:space="preserve">
    <value>Forderungen</value>
  </data>
  <data name="Resale" xml:space="preserve">
    <value>Weiterverkauf</value>
  </data>
  <data name="ResaleInBase" xml:space="preserve">
    <value>Weiterverkauf in der Unterseite</value>
  </data>
  <data name="ResaleValue" xml:space="preserve">
    <value>Weiterverkauf-Wert</value>
  </data>
  <data name="ReturnCost" xml:space="preserve">
    <value>Rückholkosten</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>Rückholdatum</value>
  </data>
  <data name="ReturnPrice" xml:space="preserve">
    <value>Rückholpreis</value>
  </data>
  <data name="ReturnValue" xml:space="preserve">
    <value>Rückholwert</value>
  </data>
  <data name="ROHSCompliant" xml:space="preserve">
    <value>ROHS gefällig</value>
  </data>
  <data name="RowsAffected" xml:space="preserve">
    <value>Reihen beeinflußten</value>
  </data>
  <data name="Sale" xml:space="preserve">
    <value>Verkauf</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>Verkäufe</value>
  </data>
  <data name="SalesHistory" xml:space="preserve">
    <value>Verkaufs-Geschichte</value>
  </data>
  <data name="Salesman" xml:space="preserve">
    <value>Verkäufer</value>
  </data>
  <data name="SalesmanGP" xml:space="preserve">
    <value>Verkäufer GP</value>
  </data>
  <data name="SalesmanPercent" xml:space="preserve">
    <value>Verkäufer %</value>
  </data>
  <data name="SalesOrderLineBaseValue" xml:space="preserve">
    <value>Verkaufs-Preis (Unterseite)</value>
  </data>
  <data name="SalesOrderLineQuantity" xml:space="preserve">
    <value>Verkaufs-Auftrags-Linie Quantität</value>
  </data>
  <data name="SalesOrderLineValue" xml:space="preserve">
    <value>Verkaufs-Auftrags-Linie Wert</value>
  </data>
  <data name="ShipASAP" xml:space="preserve">
    <value>Schiff so bald wie möglich </value>
  </data>
  <data name="ShippedGoodsValuationbyCountry" xml:space="preserve">
    <value>Versendete Waren-Schätzung durch LAnd</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumber" xml:space="preserve">
    <value>Versendete Aufträge sortiert durch Rechnungs-Zahl</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Versendete Aufträge sortiert durch Rechnungs-Zahl für einen Kunden</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Versendete Aufträge sortiert durch Rechnungs-Zahl für einen Verkäufer</value>
  </data>
  <data name="ShippedSales" xml:space="preserve">
    <value>Versendete Verkäufe</value>
  </data>
  <data name="ShippedSalesforLot" xml:space="preserve">
    <value>Versendete Verkäufe für Los</value>
  </data>
  <data name="ShippedSupplierRMAs" xml:space="preserve">
    <value>Versendeter Lieferant RMAs</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Versendeter Lieferant RMAs für einen Kaufer</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Versendeter Lieferant RMAs für einen Kaufer mit Gründen</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Versendeter Lieferant RMAs für einen Lieferanten</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Versendeter Lieferant RMAs für einen Lieferanten mit Gründen</value>
  </data>
  <data name="ShippedSupplierRMAswithReasons" xml:space="preserve">
    <value>Versendeter Lieferant RMAs mit Gründen</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>Verschiffen</value>
  </data>
  <data name="ShippingCharge" xml:space="preserve">
    <value>Verschiffen-Gebühr</value>
  </data>
  <data name="ShippingCost" xml:space="preserve">
    <value>Verschiffen-Kosten</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Schiff über</value>
  </data>
  <data name="SOApproved" xml:space="preserve">
    <value>SO anerkannt</value>
  </data>
  <data name="SONumber" xml:space="preserve">
    <value>Verkaufs-Auftragsnummer</value>
  </data>
  <data name="SORating" xml:space="preserve">
    <value>SO veranschlagend</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Anfangsdatum</value>
  </data>
  <data name="StartTime" xml:space="preserve">
    <value>Anlasszeit</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>Zustand</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Vorrat</value>
  </data>
  <data name="StockCount" xml:space="preserve">
    <value>Auf lagerzählimpuls</value>
  </data>
  <data name="StockDate" xml:space="preserve">
    <value>Auf lagerdatum</value>
  </data>
  <data name="StockKeepingUnit" xml:space="preserve">
    <value>Auf lager haltene Maßeinheit</value>
  </data>
  <data name="StockList" xml:space="preserve">
    <value>Bestandsliste</value>
  </data>
  <data name="StockManagement" xml:space="preserve">
    <value>Lagerverwaltung</value>
  </data>
  <data name="StockName" xml:space="preserve">
    <value>Auf lagername</value>
  </data>
  <data name="StockPart" xml:space="preserve">
    <value>Auf lagerteil</value>
  </data>
  <data name="StockValuation" xml:space="preserve">
    <value>Bewertung von Aktien</value>
  </data>
  <data name="Sum" xml:space="preserve">
    <value>Summe</value>
  </data>
  <data name="Sum_ShippingCost" xml:space="preserve">
    <value>Verschiffen-Kosten</value>
  </data>
  <data name="Summary" xml:space="preserve">
    <value>Zusammenfassung</value>
  </data>
  <data name="SummaryBookedOrdersbyCustomer" xml:space="preserve">
    <value>Zusammenfassung angemeldete Aufträge durch Kunden</value>
  </data>
  <data name="SummaryBookedOrdersbyDivision" xml:space="preserve">
    <value>Zusammenfassung angemeldete Aufträge durch Abteilung</value>
  </data>
  <data name="SummaryBookedOrdersbySalesperson" xml:space="preserve">
    <value>Zusammenfassung angemeldete Aufträge durch Verkäufer</value>
  </data>
  <data name="SummaryOpenOrdersbyCustomer" xml:space="preserve">
    <value>Zusammenfassungs-geöffnete Aufträge durch Kunden</value>
  </data>
  <data name="SummaryOpenOrdersbyDivision" xml:space="preserve">
    <value>Zusammenfassungs-geöffnete Aufträge durch Abteilung</value>
  </data>
  <data name="SummaryOpenOrdersbySalesperson" xml:space="preserve">
    <value>Zusammenfassungs-geöffnete Aufträge durch Verkäufer</value>
  </data>
  <data name="SummaryShippedSalesbyCustomer" xml:space="preserve">
    <value>Zusammenfassung versendete Verkäufe durch Kunden</value>
  </data>
  <data name="SummaryShippedSalesbyDivision" xml:space="preserve">
    <value>Zusammenfassung versendete Verkäufe durch Abteilung</value>
  </data>
  <data name="SummaryShippedSalesbySalesperson" xml:space="preserve">
    <value>Zusammenfassung versendete Verkäufe durch Verkäufer</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Lieferant</value>
  </data>
  <data name="SupplierFax" xml:space="preserve">
    <value>Lieferanten-Telefax</value>
  </data>
  <data name="SupplierOnTimeDeliveryReport" xml:space="preserve">
    <value>Lieferant auf Zeit-Anlieferungs-Report</value>
  </data>
  <data name="SupplierPart" xml:space="preserve">
    <value>Lieferanten-Teil</value>
  </data>
  <data name="SupplierReturnValue" xml:space="preserve">
    <value>Lieferanten-Rückholwert</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Lieferant RMA</value>
  </data>
  <data name="SupplierRMADate" xml:space="preserve">
    <value>Datum des Lieferanten-RMA</value>
  </data>
  <data name="SupplierRMANumber" xml:space="preserve">
    <value>Lieferanten-RMA Nr</value>
  </data>
  <data name="SupplierRMAs" xml:space="preserve">
    <value>Lieferanten-RMAs</value>
  </data>
  <data name="SupplierTelephone" xml:space="preserve">
    <value>Lieferanten-Telefon</value>
  </data>
  <data name="System" xml:space="preserve">
    <value>System</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Ziel</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Steuer</value>
  </data>
  <data name="TaxRate" xml:space="preserve">
    <value>Steuersatz</value>
  </data>
  <data name="TeamName" xml:space="preserve">
    <value>Mannschaft-Name</value>
  </data>
  <data name="Telephone" xml:space="preserve">
    <value>Telefon</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Ausdrücke</value>
  </data>
  <data name="TermsDate" xml:space="preserve">
    <value>Ausdruck-Datum</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titel</value>
  </data>
  <data name="TotalCost" xml:space="preserve">
    <value>Gesamtkosten</value>
  </data>
  <data name="TotalLandedCost" xml:space="preserve">
    <value>Gesamtmenge landete Kosten</value>
  </data>
  <data name="TotalResale" xml:space="preserve">
    <value>Gesamtweiterverkauf</value>
  </data>
  <data name="TotalSales" xml:space="preserve">
    <value>Gesamtverkäufe</value>
  </data>
  <data name="TotalShippingCost" xml:space="preserve">
    <value>Verschiffen-Gesamtkosten</value>
  </data>
  <data name="TotalTax" xml:space="preserve">
    <value>Gesamtsteuer</value>
  </data>
  <data name="TotalValue" xml:space="preserve">
    <value>Gesamtwert</value>
  </data>
  <data name="UnallocatedOnly" xml:space="preserve">
    <value>Nur geaberkannt?</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Stückpreis</value>
  </data>
  <data name="URL" xml:space="preserve">
    <value>URL</value>
  </data>
  <data name="UserList" xml:space="preserve">
    <value>Benutzer-Liste</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Benutzer</value>
  </data>
  <data name="Value" xml:space="preserve">
    <value>Wert</value>
  </data>
  <data name="ValueStockOnly" xml:space="preserve">
    <value>Nur bewerteter Vorrat?</value>
  </data>
  <data name="Vendor" xml:space="preserve">
    <value>Verkäufer</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Lager</value>
  </data>
  <data name="WeightInKgs" xml:space="preserve">
    <value>Gewicht in den Kilogramm</value>
  </data>
  <data name="ZeroValueOnly" xml:space="preserve">
    <value>Nur nullwert?</value>
  </data>
</root>