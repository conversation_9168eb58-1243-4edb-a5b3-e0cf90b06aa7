using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class GILines_CloseInspection : Base {

		#region Locals

		private string _strStartInspection;

        private Label _lblExplainStartInspection;

        #endregion

        #region Properties

        private int _intPurchaseOrderID = -1;
		public int PurchaseOrderID {
			get { return _intPurchaseOrderID; }
			set { _intPurchaseOrderID = value; }
		}

		private int _intLineID;
		public int LineID {
			get { return _intLineID; }
			set { _intLineID = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            _strStartInspection = Functions.GetGlobalResource("FormTitles", "GILines_CloseInspection");
            AddScriptReference("Controls.Nuggets.GILines.CloseInspection.GILines_CloseInspection.js");
			if (_objQSManager.PurchaseOrderID > 0) _intPurchaseOrderID = _objQSManager.PurchaseOrderID;
			WireUpControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
            _lblExplainStartInspection = (Label)ctlDesignBase.FindExplanationControl("lblExplainStartInspection");
        }

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.GILines_CloseInspection", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intPurchaseOrderID", _intPurchaseOrderID);
			_scScriptControlDescriptor.AddProperty("intLineID", _intLineID);
			_scScriptControlDescriptor.AddProperty("strStartInspection", _strStartInspection);
            _scScriptControlDescriptor.AddElementProperty("lblExplainStartInspection", _lblExplainStartInspection.ClientID);
			
        }

	}
}