﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-227222]	Ngai To				10-Jan-2025		Update			227222: [PROD Bug] Master Taxes - Display incorrect breadcrumb navigation and incorrect data in the "Current Rate" and "Current Rate 2" columns
===========================================================================================
*/

CREATE OR ALTER FUNCTION [dbo].[ufn_get_master_taxrate]
	--********************************************************************************************
	--* SG 28.01.2020:
	--* - Updated to choose newest rate when multiple rates entered for the same date
	--* SK 14.06.2010:
	--* - only compare based on absolute date i.e. avoid mistakes becuase of time elements in date
	--*******************************************************************************************
	(
	@TaxId INT,
	@ClientId INT,
	@TaxDate DATETIME,
	@IsGlobalTax BIT = 0
	)
RETURNS FLOAT
AS
BEGIN
	DECLARE @return FLOAT

	SELECT @TaxDate = datetimefromparts(year(@TaxDate), month(@TaxDate), day(@TaxDate), 23, 59, 59, 997)

	IF @IsGlobalTax = 0
	BEGIN
		SELECT TOP 1 @return = b.Rate1
		FROM tbTax a WITH (NOLOCK),
			tbTaxRate b WITH (NOLOCK)
		WHERE a.TaxId = @TaxId
			AND a.ClientNo = @ClientId
			AND a.TaxId = b.TaxNo
			AND b.TaxDate <= @TaxDate
		ORDER BY b.TaxDate DESC,
			b.TaxRateId DESC
	END
	ELSE
	BEGIN
		SELECT TOP 1 @return = b.Rate1
		FROM tbGlobalTax a WITH (NOLOCK),
			tbGlobalTaxRate b WITH (NOLOCK)
		WHERE a.GlobalTaxId = @TaxId
			AND a.GlobalTaxId = b.GlobalTaxNo
			AND b.GlobalTaxDate <= @TaxDate
		ORDER BY b.GlobalTaxDate DESC,
			b.GlobalTaxRateId DESC
	END

	RETURN isnull(@return, 0)
END
GO


