﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_update_Setting]
--********************************************************************************************
--* RP 03.11.2009
--* - new proc
--********************************************************************************************
    @SettingItemID int
  , @ClientID int = NULL
  , @SettingValue nvarchar(250) = NULL
  , @UpdatedBy int
  , @RowsAffected int = NULL OUTPUT
AS 
    IF (SELECT  count(*)
        FROM    tbSetting
        WHERE   ((@ClientID IS NULL
                  AND ClientID IS NULL)
                 OR (NOT @ClientID IS NULL
                     AND ClientID = @ClientID))
                AND SettingItemID = @SettingItemID
       ) = 0 
        BEGIN
            DECLARE @NewID int
            EXEC usp_insert_Setting --
                @SettingItemID = @SettingItemID --  
                , @ClientID = @ClientID --  
                , @SettingValue = @SettingValue --  
                , @UpdatedBy = @UpdatedBy --  
                , @NewID = @NewID OUTPUT
	
            SELECT  @RowsAffected = 1
        END
    ELSE 
        BEGIN	
            UPDATE  tbSetting
            SET     ClientID = @ClientID
                  , SettingValue = @SettingValue
                  , @UpdatedBy = @UpdatedBy
                  , DLUP = CURRENT_TIMESTAMP
            WHERE   ((@ClientID IS NULL
                      AND ClientID IS NULL)
                     OR (NOT @ClientID IS NULL
                         AND ClientID = @ClientID))
                    AND SettingItemID = @SettingItemID        

            SELECT  @RowsAffected = @@rowcount
        END
           
    
    

GO


