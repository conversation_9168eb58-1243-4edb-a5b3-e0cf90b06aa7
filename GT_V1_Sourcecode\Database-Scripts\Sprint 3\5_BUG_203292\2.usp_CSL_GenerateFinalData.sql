-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_CSL_GenerateFinalData', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_CSL_GenerateFinalData;
END
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-203292]    cuongdx			30-MAY-2024		    Alter			Changes the way to map 2 companies
[BUG-203292]    cuongdx			12-JUNE-2024		Alter			OPTIMIZER code to reduce response TIME
===========================================================================================
*/
-- Create the new procedure
CREATE PROCEDURE [dbo].[usp_CSL_GenerateFinalData] 
AS
BEGIN
	--[001] cuongdx 30-MAY-2024  BUG 203292 --  Changes the way to map 2 companies
	--create new #tempCSVImport with short name and short altname
	DROP TABLE IF EXISTS #tempCSVImport;
	CREATE TABLE #tempCSVImport
	(
		[name] [nvarchar](max) NULL,
		[addresses] [nvarchar](max) NULL,
		[altnames] [nvarchar](max) NULL,
		[NameShort] [nvarchar](400) NULL,
		[AltNameShort] [nvarchar](400) NULL,
	)

	--create index for name and altnames
	create index idx_csv_company_name_short on #tempCSVImport ([NameShort])
	create index idx_csv_company_altname_short
	on #tempCSVImport ([AltNameShort])

	--insert data of today to#tempCSVImport
	insert into #tempCSVImport
	select name,
		   addresses,
		   altnames,
		   dbo.GetFirstNWords(name, 3),
		   dbo.GetFirstNWords(altnames, 3) --,LEN(dbo.GetFirstNWords(name,3)),LEN(dbo.GetFirstNWords(altnames,3))
	from tbCSVImport
	where cast(CreatedOn as date) = cast(CURRENT_TIMESTAMP as date)

	--create table #tbCompanyTemp to have company short name
	DROP TABLE IF EXISTS #tbCompanyTemp;
	CREATE TABLE #tbCompanyTemp
	(
		[CompanyId] [int] NOT NULL,
		[CustomerCode] [nvarchar](15) NULL,
		[ClientNo] [int] NOT NULL,
		[Notes] [nvarchar](max) NULL,
		[ImportantNotes] [nvarchar](max) NULL,
		[ERAIReported] [bit] NULL,
		[CompanyName] [nvarchar](128) NOT NULL,
		[NameShort] [nvarchar](400) NULL,
	)
	--create index name short
	create index idx_company_name_short on #tbCompanyTemp (NameShort)

	--insert data to #tbCompanyTemp
	insert into #tbCompanyTemp
	select [CompanyId],
		   [CustomerCode],
		   [ClientNo],
		   [Notes],
		   [ImportantNotes],
		   [ERAIReported],
		   [CompanyName],
		   dbo.GetFirstNWords([CompanyName], 3) --,len(dbo.GetFirstNWords([CompanyName],3))
	from tbCompany src
	where (
			  len(src.CompanyName) > 3
		  )

	--[001] SOORYA VYAS 16-JAN-2024  RP-2845 --  Changes in CSL_Sanctioned sheet (Added new column IsSanctioned)    

	--- delete tbCSL_Address_Comparision table as this table will be recreated everytime when this procedure runs                      
	--- prepare the company complete address                      
	IF OBJECT_ID(N'tbCSL_Address_Comparision', N'U') IS NOT NULL
		drop table tbCSL_Address_Comparision
	------ delete the data from final table for running process day                    
	delete from tbCSLGTComparision
	where CAST(Insertedon as date) = cast(CURRENT_TIMESTAMP as date)

	---Use below step to recreate the tbCSL_Address_Comparision        
	select ca.CompanyNo,
		   src.CompanyName,
		   src.CustomerCode,
		   replace(
					  Replace(
								 Stuff(
										  Coalesce(', ' + a.AddressName, '') + Coalesce(', ' + a.Line1, '')
										  + Coalesce(', ' + a.Line2, '') + Coalesce(', ' + a.line3, '')
										  + Coalesce(', ' + a.county, '') + Coalesce(', ' + a.city, '')
										  + Coalesce(', ' + a.State, '') + Coalesce(', ' + a.city, '')
										  + Coalesce(', ' + cntry.CountryName, '') + Coalesce(',' + [Zip], ''),
										  1,
										  1,
										  ''
									  ),
								 ',',
								 ''
							 ),
					  '-',
					  ''
				  ) AS [Address],
		   a.AddressName,
		   a.Line1,
		   a.Line2,
		   a.Line3,
		   a.county,
		   a.City,
		   a.State,
		   a.CountryNo,
		   cntry.CountryName,
		   a.ZIP,
		   src.ClientNo
	into tbCSL_Address_Comparision
	from tbCompany src with (nolock)
		join tbCompanyAddress ca
			on src.CompanyId = ca.CompanyNo
		join tbAddress a
			on ca.AddressNo = a.AddressId
		left join tbCountry cntry
			on a.CountryNo = cntry.CountryId ---- 163503,159264                      
	order by ca.CompanyNo

	--insert into tbcslgtcomparision
	insert into tbCSLGTComparision
	(
		companyid,
		CustomerCode,
		ClientNo,
		ClientName,
		CompanyName,
		GT_Company_Address,
		CSL_Name,
		CSL_Address,
		CSL_ALT_Name,
		Insertedon,
		Notes,
		ImportantNotes,
		ERAIReported
	)
	(select src.CompanyId,
		   IsNUll(src.CustomerCode, '') CustomerCode,
		   src.ClientNo,
		   (
			   select tbClient.ClientName
			   from tbclient with (nolock)
			   where tbclient.ClientId = src.ClientNo
		   ) ClientName,
		   src.CompanyName,
		   gtcmpAdd.Address [GT Company Address],
		   dest.name [CSL Name],
		   IsNUll(dest.addresses, '') [CSL Address],
		   IsNull(dest.altnames, '') [CSL ALT_Name],
		   CURRENT_TIMESTAMP,
		   src.notes,
		   src.ImportantNotes,
		   case src.ERAIReported
			   when 1 then
				   'true'
			   else
				   'false'
		   end
	from #tbCompanyTemp src with (nolock)
		inner join #tempCSVImport dest with (nolock)
			on (
				   dest.NameShort like src.NameShort + '%'
			   )
		left join tbCSL_Address_Comparision gtcmpAdd with (nolock)
			on gtcmpAdd.CompanyNo = src.CompanyId
	union
	select src.CompanyId,
		   IsNUll(src.CustomerCode, '') CustomerCode,
		   src.ClientNo,
		   (
			   select tbClient.ClientName
			   from tbclient with (nolock)
			   where tbclient.ClientId = src.ClientNo
		   ) ClientName,
		   src.CompanyName,
		   gtcmpAdd.Address [GT Company Address],
		   dest.name [CSL Name],
		   IsNUll(dest.addresses, '') [CSL Address],
		   IsNull(dest.altnames, '') [CSL ALT_Name],
		   CURRENT_TIMESTAMP,
		   src.notes,
		   src.ImportantNotes,
		   case src.ERAIReported
			   when 1 then
				   'true'
			   else
				   'false'
		   end
	from #tbCompanyTemp src with (nolock)
		inner join #tempCSVImport dest with (nolock)
			on (
				   dest.AltNameShort like src.NameShort + '%'
			   )
		left join tbCSL_Address_Comparision gtcmpAdd with (nolock)
			on gtcmpAdd.CompanyNo = src.CompanyId
	)
	order by src.CompanyId

	--drop table after imported
	DROP TABLE IF EXISTS #tempCSVImport;
	DROP TABLE IF EXISTS #tbCompanyTemp;
	--select data to export excel
    select CSLGT.companyid,
           CSLGT.CustomerCode,
           CSLGT.ClientNo,
           CSLGT.ClientName,
           CSLGT.CompanyName,
           CSLGT.Notes as [General_customer_info],
           CSLGT.ImportantNotes as Accounts_notes,
           CSLGT.GT_Company_Address,
           CSL_Name,
           CSL_Address,
           CSL_ALT_Name,
           CSLGT.ERAIReported,
           CASE
               WHEN IsSanctioned = 1 THEN
                   'True'
               ELSE
                   'False'
           END as IsSanctioned --[001]                  
    from tbCSLGTComparision CSLGT
        Left join tbCompany comp
            on CSLGT.companyid = comp.companyid
        LEFT Join tbClient cl
            on cl.ClientId = CSLGT.ClientNo
    where cast(Insertedon as date) = cast(CURRENT_TIMESTAMP as date)
          AND comp.Inactive != 1
          AND cl.Inactive != 1
END
