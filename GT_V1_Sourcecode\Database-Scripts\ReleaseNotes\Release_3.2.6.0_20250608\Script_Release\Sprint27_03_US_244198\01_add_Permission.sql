﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-244198]     Cuong.Dox		 2-May-2025			CREATE		Sourcing - Permissions of Bulk Edit function needs to be updated
===========================================================================================  
*/
DECLARE @CurrentSortOrder INT;
SELECT @CurrentSortOrder = ISNULL(MAX(DisplaySortOrder),0) FROM tbSecurityFunction WHERE SiteSectionNo = 2; --Offer
IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Orders_Sourcing_BulkEdit_Log_StrategicStock'
		OR SecurityFunctionId = 20010049
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     20010049,                -- SecurityFunctionId
    'Orders_Sourcing_BulkEdit_Log_StrategicStock',            -- FunctionName
    'Allow to View Only Bulk Edit Log Strategic Stock', -- Description
    2000101,                      -- SitePageNo
    2,                        -- SiteSectionNo
    NULL,                     -- ReportNo (NULL because it's not related to a report)
    NULL,                     -- UpdatedBy (ID of the user who updated this function)
    GETDATE(),                -- DLUP (current date and time)
    0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 1                        -- DisplaySortOrder
);
END


