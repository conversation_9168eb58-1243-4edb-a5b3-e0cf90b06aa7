﻿/*   
====================================================================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-242815]		Trung Pham		05-Sep-2024		UPDATE		Update table schema
====================================================================================================================================  
*/
IF NOT EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbSO_ExportApprovalDetails' AND COLUMN_NAME = 'PartApplication'
)
BEGIN
    ALTER TABLE tbSO_ExportApprovalDetails
    ADD PartApplication NVARCHAR(100) NULL;
END

IF NOT EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbSO_ExportApprovalDetails' AND COLUMN_NAME = 'ExportControl'
)
BEGIN
    ALTER TABLE tbSO_ExportApprovalDetails
    ADD ExportControl BIT NULL;
END

IF NOT EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbSO_ExportApprovalDetails' AND COLUMN_NAME = 'AerospaceUse'
)
BEGIN
    ALTER TABLE tbSO_ExportApprovalDetails
    ADD AerospaceUse BIT NULL;
END

IF NOT EXISTS (
    SELECT 1 
    FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'tbSO_ExportApprovalDetails' AND COLUMN_NAME = 'PartTested'
)
BEGIN
    ALTER TABLE tbSO_ExportApprovalDetails
    ADD PartTested NVARCHAR(200) NULL;
END