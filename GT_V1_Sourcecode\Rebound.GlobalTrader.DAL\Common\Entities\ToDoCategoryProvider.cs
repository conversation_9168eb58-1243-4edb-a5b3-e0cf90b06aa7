﻿using System;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.DAL
{

	public abstract class ToDoCategoryProvider : DataAccess
	{
		static private ToDoCategoryProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public ToDoCategoryProvider Instance
		{
			get
			{
				if (_instance == null) _instance = (ToDoCategoryProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.ToDoCategories.ProviderType));
				return _instance;
			}
		}
		public ToDoCategoryProvider()
		{
			this.ConnectionString = Globals.Settings.Termss.ConnectionString;
		}

		#region Method Registrations

		public abstract List<ToDoCategoryDetails> DropDown();
        #endregion
    }
}