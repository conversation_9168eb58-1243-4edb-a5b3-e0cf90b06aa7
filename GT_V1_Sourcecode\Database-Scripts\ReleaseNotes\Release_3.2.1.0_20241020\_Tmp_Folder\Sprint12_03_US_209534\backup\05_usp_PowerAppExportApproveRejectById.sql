﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_PowerAppExportApproveRejectById]        
@ExportApprovalId   INT,        
@OgelNumber     NVARCHAR(MAX)='',           
@Comment     NVARCHAR(MAX)=NULL,        
@AuthorisedByEmail     NVARCHAR(MAX),        
@ActionOption    INT,    
@RequestId   INT,    
@TokenValue   NVARCHAR(MAX)        
AS        
/*        
 *[001]  Created   Abhinav <PERSON>xena  11-04-2023  Add new Proc to store Approve/Reject response from power apps.   
 *[002]  Altered   Abhinav Saxena  12-04-2023  Add new columns for from email and from loginid.       
 */        
BEGIN        
SET NOCOUNT ON         
DECLARE @IsApproved     BIT=0       
DECLARE @LoginId INT=0     
DECLARE @MilitaryUse INT=0    
DECLARE @EndUser NVARCHAR(MAX)=''    
DECLARE @SalesOrderNo INT=0    
DECLARE @ClientNo INT=0    
    
------Getting required Data----    
SELECT @SalesOrderNo=SalesOrderNo FROM tbSO_ExportApprovalStatusOGEL     
WHERE ExportApprovalId=@ExportApprovalId    
    
SELECT @ClientNo=ClientNo,@MilitaryUse=OGEL_MilitaryUse,@EndUser=OGEL_EndUserNotes     
FROM tbSalesOrder WHERE SalesOrderId=@SalesOrderNo    
    
SELECT TOP 1 @LoginId=LoginId FROM tbLogin     
WHERE Inactive=0 AND EMail=@AuthorisedByEmail AND ClientNo=@ClientNo    
--------END-------    
--Validation Logic               
DECLARE @IsValidUser BIT=0        
DECLARE @IsNotifySO  BIT=0      
SELECT @IsNotifySO=ISNULL(IsNotifySO,0) FROM tbPowerAppTokenInfo WHERE RequestId=@RequestId      
EXEC usp_PowerApp_ValidateUser @RequestId,@TokenValue,@IsValidUser OUT     
-----END-------      
CREATE TABLE #tempEmailData        
(        
SalesOrderNo   INT,        
SalesOrderNumber  INT,        
Salesman    INT,        
SalesmanName   NVARCHAR(MAX),        
SOSerialNo    INT,        
ExportApprovalStatusId INT,        
ExportApprovalStatus NVARCHAR(MAX),        
ExportApprovalId  INT,        
IsApproved    BIT,    
IsNotifySO    BIT,  
LoginId     INT,  
LoginName    NVARCHAR(MAX)        
)        
BEGIN TRY      
IF(@IsValidUser=1)     
BEGIN      
-----Update the approval response---        
IF(@ActionOption=1)        
BEGIN          
UPDATE easg SET      
easg.OGELNumber=@OgelNumber,      
easg.MilitaryUses=@MilitaryUse,      
easg.EndUser=@EndUser,      
easg.ApprovalComment=@Comment,      
easg.ApprovedBy=@LoginId,      
easg.ApprovedDate=GETDATE(),      
easg.ApprovalStatusId=CASE WHEN @OgelNumber>0 AND (SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=@ExportApprovalId)>0       
THEN 1 ELSE CASE WHEN @OgelNumber=0 AND (SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=@ExportApprovalId)>0 THEN 2     
--WHEN @OgelNumber>0 AND ((isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0))>0)   
--AND (SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=@ExportApprovalId)=0 THEN 5   
WHEN @OgelNumber>0 AND ((isnull((select top 1 m.ECCNStatus from dbo.tbECCN m   where m.ECCNCode= sol.ECCNCode ),0))=0)   
AND (SELECT COUNT(1) FROM tbSOLineEUUPDF WHERE ExportApprovalNo=@ExportApprovalId)=0 THEN 6 
WHEN easg.ApprovalStatusId=5 THEN 8 
ELSE 3 END END      
FROM tbSO_ExportApprovalStatusOGEL easg      
LEFT JOIN tbSalesOrderLine sol On easg.SalesOrderLineNo=sol.SalesOrderLineId   
WHERE ExportApprovalId=@ExportApprovalId     

UPDATE tbSO_ExportApprovalStatusOGEL 
SET ISAwaitingEEUStatus=CASE WHEN ApprovalStatusId=8 THEN 1 ELSE 0 END
WHERE ExportApprovalId=@ExportApprovalId
        
END        
ELSE IF(@ActionOption=2)        
BEGIN        
UPDATE tbSO_ExportApprovalStatusOGEL SET        
OGELNumber=@OgelNumber,        
MilitaryUses=@MilitaryUse,        
EndUser=@EndUser,        
ApprovalComment=@Comment,        
ApprovedBy=@LoginId,        
ApprovedDate=GETDATE(),        
ApprovalStatusId=4        
WHERE ExportApprovalId=@ExportApprovalId        
END        
SET @IsApproved=1;     
END    
ELSE    
BEGIN    
SET @IsApproved=0;    
END       
END TRY        
BEGIN CATCH        
SET @IsApproved=0;        
END CATCH        
        
        
IF(@IsApproved=1)        
BEGIN        
INSERT INTO #tempEmailData        
SELECT          
so.SalesOrderId,        
so.SalesOrderNumber,        
CASE WHEN easg.ApprovalStatusId !=3 THEN so.Salesman ELSE 0 END,        
lgnsp.EmployeeName,        
sol.SOSerialNo,        
easg.ApprovalStatusId,        
asm.ApprovalName,        
easg.ExportApprovalId        
, 1      
, @IsNotifySO   
, @LoginId  
, @AuthorisedByEmail     
FROM tbSO_ExportApprovalStatusOGEL easg        
LEFT OUTER JOIN tbSalesOrder so ON easg.SalesOrderNo=so.SalesOrderId        
LEFT OUTER JOIN tbLogin lgnsp ON so.Salesman=lgnsp.LoginId        
LEFT OUTER JOIN tbSalesOrderLine sol ON easg.SalesOrderLineNo=sol.SalesOrderLineId        
LEFT OUTER JOIN tbSOExportApprovalStatusMaster asm ON easg.ApprovalStatusId=asm.ApprovalStatusId        
WHERE easg.ExportApprovalId=@ExportApprovalId        
END        
ELSE        
BEGIN        
INSERT INTO #tempEmailData VALUES(0,0,0,'',0,0,'',0,0,0,0,'')        
END        
SELECT * FROm #tempEmailData        
DROP TABLE #tempEmailData        
--------------END-------------------        
SET NOCOUNT OFF        
END
GO


