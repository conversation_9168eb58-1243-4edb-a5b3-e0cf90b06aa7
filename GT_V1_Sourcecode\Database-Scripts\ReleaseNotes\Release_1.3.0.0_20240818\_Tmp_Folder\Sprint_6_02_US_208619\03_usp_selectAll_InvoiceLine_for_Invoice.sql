﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('[usp_selectAll_InvoiceLine_for_Invoice]', 'P') IS NOT NULL
	DROP PROC [dbo].[usp_selectAll_InvoiceLine_for_Invoice]
GO

CREATE PROCEDURE [dbo].[usp_selectAll_InvoiceLine_for_Invoice] @InvoiceId INT
AS
--*******************************************************************************************                              
--*  [RP-2341] Ravi  17-10-2023  AS6081 Document printing      
--*******************************************************************************************    
/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208619]			Ngai To				16-Jul-2024		UPDATE			US-208619: OGEL approval dropdown to be moved out of code to the setup screen
[US-208619]			Ngai To				23-Jul-2024		UPDATE			US-208619: Change GBOGE2024/0756 - OGEL PCB Comp MIL to GBOGE2024/00756 - OGEL PCB Comp MIL
===========================================================================================
*/
DECLARE @Space NVARCHAR(100) = '                                                   '

SELECT DISTINCT ivl.*,
	isnull((
			SELECT TOP 1 (gc.GlobalCountryName)
			FROM dbo.tbInvoiceLineAllocation ila
			LEFT JOIN dbo.tbGlobalCountryList gc ON ila.CountryOfManufactureNo = gc.GlobalCountryId
			WHERE ila.InvoiceLineNo = ivl.InvoiceLineId
			), '') AS CountryOfManufactureName_old,
	ivl.CountryOfOrigin AS CountryOfManufactureName,
	sol.SOSerialNo,
	dbo.ufn_get_serialno_byInvoiceLine(InvoiceLineId) AS SerialLineNos,
	convert(VARCHAR, sol.ContractNo) AS ContractNo,
	so.AS9120,
	sol.ProductSource,
	sol.SalesOrderNo AS SoSalesOrderNo,
	so.CustomerPO AS SoCustomerPO,
	so.SalesOrderNumber AS SoSalesOrderNumber,
	(
		CASE 
			WHEN es.OGELNumber > 0
				AND so.OGEL_Required = 1
				THEN (
						--SELECT ct.OGELNumber
						--FROM tbClient ct
						--WHERE ct.ClientId = so.ClientNO
						CASE 
							WHEN es.OGELNumber = 1
								THEN 'GBOGE2020/00615'
							WHEN es.OGELNumber = 2
								THEN 'GBOGE2024/00532 - OGEL MIL GMST'
							WHEN es.OGELNumber = 3
								THEN 'GBOGE2024/00756 - OGEL PCB Comp MIL'
							ELSE 'GBOGE2020/00615'
							END
						)
			ELSE ''
			END
		) AS OGELNumber
	-- [RP-2341] start      
	,
	CASE 
		WHEN ISNULL(sr.TypeOfSupplierNo, 0) > 0
			THEN CONCAT (
					@Space + 'Type of supplier',
					replicate(CHAR(32), 90 - len(@Space + 'Type of supplier') - 1),
					': ',
					isnull(tos.Name, ' '),
					CHAR(13),
					CHAR(10) -- new line              
					,
					@Space + 'Reason for supplier choice',
					replicate(CHAR(32), 83 - len(@Space + 'Reason for supplier choice') - 1),
					': ',
					isnull(rcs.Name, ' '),
					CHAR(13),
					CHAR(10) -- new line              
					,
					@Space + 'Risk of Supplier',
					replicate(CHAR(32), 90.9 - len(@Space + 'Risk of Supplier') - 1),
					': ',
					isnull(ros.Name, ' '),
					CHAR(13),
					CHAR(10) -- new line              
					,
					@Space + 'Location',
					replicate(CHAR(32), 94 - len(@Space + 'Location') - 1),
					': ',
					ISNULL(cou.CountryName, ' ')
					)
		ELSE ''
		END AS AS6081PrintDetails
-- [RP-2341] end      
FROM vwInvoiceLine ivl
LEFT JOIN tbSalesOrderLine sol ON isnull(ivl.SalesOrderLineNo, 0) = isnull(sol.SalesOrderLineId, 0)
LEFT JOIN tbSalesOrder so ON so.SalesOrderId = sol.SalesOrderNo
--RP-1560          
LEFT JOIN tbSO_ExportApprovalStatusOGEL es ON isnull(ivl.SalesOrderLineNo, 0) = isnull(es.SalesOrderLineNo, 0)
--end RP-1560         
-- [RP-2341] start      
LEFT JOIN tbSourcingResult sr ON sr.SourcingResultId = sol.SourcingResultNo
LEFT JOIN tbAS6081_TypeOfSupplier tos ON tos.TypeOfSupplierId = sr.TypeOfSupplierNo
LEFT JOIN tbAS6081_ReasonForChosenSupplier rcs ON rcs.ReasonForChosenSupplierId = sr.ReasonForSupplierNo
LEFT JOIN tbAS6081_RiskOfSupplier ros ON ros.RiskOfSupplierId = sr.RiskOfSupplierNo
LEFT JOIN tbCountry cou ON cou.CountryId = sr.CountryNo
-- [RP-2341] end      
WHERE ivl.InvoiceNo = @InvoiceId
	AND ivl.Inactive = 0
ORDER BY ivl.InvoiceLineId
