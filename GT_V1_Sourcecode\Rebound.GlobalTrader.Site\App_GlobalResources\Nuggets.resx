<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Addresses" xml:space="preserve">
    <value>Addresses</value>
  </data>
  <data name="CommunicationLogType" xml:space="preserve">
    <value>Communication Log Type</value>
  </data>
  <data name="CompanyAdd" xml:space="preserve">
    <value>Add New Company</value>
  </data>
  <data name="GroupCodeCompanyAdd" xml:space="preserve">
    <value>GROUP CODE MANAGEMENT</value>
  </data>
  <data name="CompanyMainInfo" xml:space="preserve">
    <value>Main Company Information</value>
  </data>
  <data name="CompanyManufacturers" xml:space="preserve">
    <value>Manufacturers Supplied</value>
  </data>
  <data name="CompanyPurchasingInfo" xml:space="preserve">
    <value>Purchasing Information</value>
  </data>
  <data name="CompanySalesInfo" xml:space="preserve">
    <value>Sales Information</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="CommunicationLog" xml:space="preserve">
    <value>Contact Log</value>
  </data>
  <data name="ContactExtendedInfo" xml:space="preserve">
    <value>Extended Contact Information</value>
  </data>
  <data name="ContactMainInfo" xml:space="preserve">
    <value>Main Contact Information</value>
  </data>
  <data name="ContactsForCompany" xml:space="preserve">
    <value>Contacts for {0}</value>
  </data>
  <data name="CountingMethod" xml:space="preserve">
    <value>Counting Method</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="CreditAdd" xml:space="preserve">
    <value>Add New Credit Note</value>
  </data>
  <data name="CreditLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="CreditMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="Credits" xml:space="preserve">
    <value>Credit</value>
  </data>
  <data name="CRMAAdd" xml:space="preserve">
    <value>Add New Customer RMA</value>
  </data>
  <data name="CRMALines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="CRMAMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="CRMAReceivingInfo" xml:space="preserve">
    <value>Receiving Information</value>
  </data>
  <data name="CRMAReceivingLines" xml:space="preserve">
    <value>Lines for Receiving</value>
  </data>
  <data name="CRMAS" xml:space="preserve">
    <value>Customer RMAs</value>
  </data>
  <data name="CRMAsReceive" xml:space="preserve">
    <value>Receive CRMAs</value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="CurrencyRateHistory" xml:space="preserve">
    <value>Exchange Rate History</value>
  </data>
  <data name="CustomerRequirementAdd" xml:space="preserve">
    <value>Add New Requirement</value>
  </data>
  <data name="CustomerRequirementMainInfo" xml:space="preserve">
    <value>Parts Required</value>
  </data>
  <data name="CustomerRequirements" xml:space="preserve">
    <value>Customer Requirements</value>
  </data>
  <data name="CustomerRequirementSourcingResults" xml:space="preserve">
    <value>Sourcing Results</value>
  </data>
  <data name="DebitAdd" xml:space="preserve">
    <value>Add New Debit Note</value>
  </data>
  <data name="DebitLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="DebitMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="Debits" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="DivisionMembers" xml:space="preserve">
    <value>Division Members</value>
  </data>
  <data name="Divisions" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="DocFooters" xml:space="preserve">
    <value>Document Footer Messages</value>
  </data>
  <data name="DocHeaderImage" xml:space="preserve">
    <value>Document Header Image</value>
  </data>
  <data name="Feedback" xml:space="preserve">
    <value>Feedback</value>
  </data>
  <data name="GIAdd" xml:space="preserve">
    <value>Add New Goods In Note</value>
  </data>
  <data name="GILines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="GIMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="GlobalCountryList" xml:space="preserve">
    <value>Master Country List</value>
  </data>
  <data name="GlobalCurrencyList" xml:space="preserve">
    <value>Master Currency List</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Goods In</value>
  </data>
  <data name="IndustryType" xml:space="preserve">
    <value>Industry Type</value>
  </data>
  <data name="InvoiceAdd" xml:space="preserve">
    <value>Add New Invoice</value>
  </data>
  <data name="InvoiceLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="InvoiceLinesDeleted" xml:space="preserve">
    <value>Deleted Lines</value>
  </data>
  <data name="InvoiceMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="Invoices" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="LotAdd" xml:space="preserve">
    <value>Add New Lot</value>
  </data>
  <data name="LotItems" xml:space="preserve">
    <value>Lot Items</value>
  </data>
  <data name="LotMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="Lots" xml:space="preserve">
    <value>Lots</value>
  </data>
  <data name="MailMessageGroupMembers" xml:space="preserve">
    <value>Mail Group Members</value>
  </data>
  <data name="MailMessageGroups" xml:space="preserve">
    <value>Mail Groups</value>
  </data>
  <data name="MailMessages" xml:space="preserve">
    <value>Mail Messages</value>
  </data>
  <data name="ManufacturerAdd" xml:space="preserve">
    <value>Add New Manufacturer</value>
  </data>
  <data name="ManufacturerCompanies" xml:space="preserve">
    <value>Related Companies</value>
  </data>
  <data name="ManufacturerMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="ManufacturerSuppliers" xml:space="preserve">
    <value>Suppliers Who Distribute</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Package</value>
  </data>
  <data name="POAdd" xml:space="preserve">
    <value>Add New Purchase Order</value>
  </data>
  <data name="POLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="POMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="POReceivingInfo" xml:space="preserve">
    <value>Receiving Information</value>
  </data>
  <data name="POReceivingLines" xml:space="preserve">
    <value>Lines for Receiving</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="ProductDutyRateHistory" xml:space="preserve">
    <value>Duty Rate History</value>
  </data>
  <data name="PurchaseOrders" xml:space="preserve">
    <value>Purchase Orders</value>
  </data>
  <data name="PurchaseOrdersReceive" xml:space="preserve">
    <value>Receive Purchase Orders</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="PurReqMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="QuoteAdd" xml:space="preserve">
    <value>Add New Quote</value>
  </data>
  <data name="QuoteLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="QuoteMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="Quotes" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Close Reasons</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="ReportVariables" xml:space="preserve">
    <value>Report Parameters</value>
  </data>
  <data name="SalesOrders" xml:space="preserve">
    <value>Sales Orders</value>
  </data>
  <data name="SalesOrdersShip" xml:space="preserve">
    <value>Ship Sales Orders</value>
  </data>
  <data name="SecurityGroupMembers" xml:space="preserve">
    <value>Group Members</value>
  </data>
  <data name="SecurityGroupPermissionsGeneral" xml:space="preserve">
    <value>General Permissions</value>
  </data>
  <data name="SecurityGroupPermissionsPages" xml:space="preserve">
    <value>Page Permissions</value>
  </data>
  <data name="SecurityGroupPermissionsReports" xml:space="preserve">
    <value>Report Permissions</value>
  </data>
  <data name="SecurityGroups" xml:space="preserve">
    <value>Security Groups</value>
  </data>
  <data name="SecurityUserGroups" xml:space="preserve">
    <value>Security Groups</value>
  </data>
  <data name="SecurityUsers" xml:space="preserve">
    <value>Security Users</value>
  </data>
  <data name="SelectPart" xml:space="preserve">
    <value>Select Part</value>
  </data>
  <data name="Sequencer" xml:space="preserve">
    <value>Sequence Numbers</value>
  </data>
  <data name="ServiceAdd" xml:space="preserve">
    <value>Add New Service</value>
  </data>
  <data name="ServiceAllocations" xml:space="preserve">
    <value>Allocations</value>
  </data>
  <data name="ServiceMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="Services" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="ServicesForLots" xml:space="preserve">
    <value>Services for {0}</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>Shipping</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Shipping Methods</value>
  </data>
  <data name="SOAdd" xml:space="preserve">
    <value>Add New Sales Order</value>
  </data>
  <data name="SOAuthorisation" xml:space="preserve">
    <value>Authorisation Information</value>
  </data>
  <data name="SOLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="SOMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="SOShippingInfo" xml:space="preserve">
    <value>Shipping Information</value>
  </data>
  <data name="SOShippingLines" xml:space="preserve">
    <value>Lines for Shipping</value>
  </data>
  <data name="Sourcing" xml:space="preserve">
    <value>Sourcing</value>
  </data>
  <data name="SourcingLinks" xml:space="preserve">
    <value>Sourcing Links</value>
  </data>
  <data name="SRMAAdd" xml:space="preserve">
    <value>Add New Supplier RMA</value>
  </data>
  <data name="SRMALines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="SRMAMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="SRMAS" xml:space="preserve">
    <value>Supplier RMAs</value>
  </data>
  <data name="SRMAShippingInfo" xml:space="preserve">
    <value>Shipping Information</value>
  </data>
  <data name="SRMAShippingLines" xml:space="preserve">
    <value>Lines for Shipping</value>
  </data>
  <data name="SRMAsShip" xml:space="preserve">
    <value>Ship SRMAs</value>
  </data>
  <data name="Stock" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="StockAdd" xml:space="preserve">
    <value>Add New Stock Item</value>
  </data>
  <data name="StockAllocations" xml:space="preserve">
    <value>Allocations</value>
  </data>
  <data name="StockForLots" xml:space="preserve">
    <value>Stock for {0}</value>
  </data>
  <data name="StockImages" xml:space="preserve">
    <value>Images</value>
  </data>
  <data name="StockLog" xml:space="preserve">
    <value>Log</value>
  </data>
  <data name="StockLogReason" xml:space="preserve">
    <value>Stock Log Reason</value>
  </data>
  <data name="StockMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="StockRelatedStock" xml:space="preserve">
    <value>Related Stock</value>
  </data>
  <data name="StockTransactions" xml:space="preserve">
    <value>Related Transactions</value>
  </data>
  <data name="TableActivity" xml:space="preserve">
    <value>Recent Activity</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="TaxRateHistory" xml:space="preserve">
    <value>Rate History</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="TeamMembers" xml:space="preserve">
    <value>Team Members</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Terms</value>
  </data>
  <data name="ToDo" xml:space="preserve">
    <value>To Do List</value>
  </data>
  <data name="Transactions" xml:space="preserve">
    <value>Transactions</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="UserPreferences" xml:space="preserve">
    <value>Preferences</value>
  </data>
  <data name="UserProfile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="AppSettings" xml:space="preserve">
    <value>Application Settings</value>
  </data>
  <data name="Incoterm" xml:space="preserve">
    <value>Incoterms</value>
  </data>
  <data name="PDFTitle" xml:space="preserve">
    <value>PDF Documents</value>
  </data>
  <data name="EUUPDFTitle" xml:space="preserve">
    <value>PDF Document</value>
  </data>
  <data name="EmailComposer" xml:space="preserve">
    <value>Email Composer</value>
  </data>
  <data name="LocalCurrency" xml:space="preserve">
    <value>Local Currency</value>
  </data>
  <data name="SupplierInvoice" xml:space="preserve">
    <value>Supplier Invoice</value>
  </data>
  <data name="SupplierInvoiceAdd" xml:space="preserve">
    <value>Add New Supplier Invoice</value>
  </data>
  <data name="SupplierInvoiceMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="SupplierInvoiceLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="SORPDFTitle" xml:space="preserve">
    <value>Customer PO Only</value>
  </data>
  <data name="NPRNotify" xml:space="preserve">
    <value>Notify NPR</value>
  </data>
  <data name="Printer" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="CertificateCategory" xml:space="preserve">
    <value>Certificate Category</value>
  </data>
  <data name="Certificate" xml:space="preserve">
    <value>Certificate</value>
  </data>
  <data name="CompanyCertificate" xml:space="preserve">
    <value>Certificate</value>
  </data>
  <data name="LabelFullPath" xml:space="preserve">
    <value>Nice Label Path</value>
  </data>
  <data name="EightDCode" xml:space="preserve">
    <value>Root Cause Category</value>
  </data>
  <data name="EightDSubCategory" xml:space="preserve">
    <value>Root Cause Sub Category</value>
  </data>
  <data name="ExchangeRateHistory" xml:space="preserve">
    <value>Exchange Rate History</value>
  </data>
  <data name="EPRNotify" xml:space="preserve">
    <value>EPR Notify</value>
  </data>
  <data name="EXCELTitle" xml:space="preserve">
    <value>Uploaded Documents</value>
  </data>
  <data name="Npr" xml:space="preserve">
    <value>NPR</value>
  </data>
  <data name="CustomerRequirementsPrint" xml:space="preserve">
    <value>Print Customer Requirements Enquiry Form</value>
  </data>
  <data name="PrintEnqForm" xml:space="preserve">
    <value>Print Enquiry Form</value>
  </data>
  <data name="SORPDFTitleNew" xml:space="preserve">
    <value>SOR</value>
  </data>
  <data name="BillOfMaterial" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="BOMAdd" xml:space="preserve">
    <value>Add New HUBRFQ</value>
  </data>
  <data name="BOMMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="InternalPurchaseOrders" xml:space="preserve">
    <value>Internal Purchase Orders</value>
  </data>
  <data name="InternalPOAdd" xml:space="preserve">
    <value>Add New Internal Purchase Order</value>
  </data>
  <data name="InternalPOLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="InternalPOMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="POQuoteLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="POQuoteMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="PurchaseQuotes" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="BOMItems" xml:space="preserve">
    <value>HUBRFQ Items</value>
  </data>
  <data name="POHubSourcing" xml:space="preserve">
    <value>Sourcing</value>
  </data>
  <data name="POQuoteAdd" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="CSV_Import" xml:space="preserve">
    <value>CSV File</value>
  </data>
  <data name="CsvExportHistory" xml:space="preserve">
    <value>Log</value>
  </data>
  <data name="CsvUploadHistory" xml:space="preserve">
    <value>File Upload History</value>
  </data>
  <data name="ClientInvoices" xml:space="preserve">
    <value>Client Invoices</value>
  </data>
  <data name="String1" xml:space="preserve">
    <value />
  </data>
  <data name="InvoiceSetting" xml:space="preserve">
    <value>Invoice</value>
  </data>
  <data name="ClientInvoiceLines" xml:space="preserve">
    <value>Lines</value>
  </data>
  <data name="ClientInvoiceMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="PurchaseRequestLineDetail" xml:space="preserve">
    <value>Price Request Line Detail</value>
  </data>
  <data name="HUBRFQLines" xml:space="preserve">
    <value>HUBRFQ Lines</value>
  </data>
  <data name="PRQ" xml:space="preserve">
    <value>Price Request Quotes</value>
  </data>
  <data name="GlobalTax" xml:space="preserve">
    <value>Master Taxes List</value>
  </data>
  <data name="GlobalTaxRateHistory" xml:space="preserve">
    <value>Master Tax Rate History</value>
  </data>
  <data name="CustomerRequirementSourcingResultsHub" xml:space="preserve">
    <value>Quote to Client</value>
  </data>
  <data name="LogHistory" xml:space="preserve">
    <value>Log History</value>
  </data>
  <data name="ClientInvoiceAdd" xml:space="preserve">
    <value>Add New Client Invoice</value>
  </data>
  <data name="ExpediteHistory" xml:space="preserve">
    <value>Expedite Notes</value>
  </data>
  <data name="GlobalProduct" xml:space="preserve">
    <value>Global Product Group</value>
  </data>
  <data name="GlobalProductDutyRateHistory" xml:space="preserve">
    <value>Client Duty Rate</value>
  </data>
  <data name="GlobalSecurityGroupMembers" xml:space="preserve">
    <value>Global Group Members</value>
  </data>
  <data name="CommunicationNotes" xml:space="preserve">
    <value>Communication Note</value>
  </data>
  <data name="GlobalProductName" xml:space="preserve">
    <value>Global Product Category</value>
  </data>
  <data name="GTUpdate" xml:space="preserve">
    <value>GT Update Notification</value>
  </data>
  <data name="SourcingImages" xml:space="preserve">
    <value>Image (Max {0}) </value>
  </data>
  <data name="SOPaymentFileList" xml:space="preserve">
    <value>SO Payment Files</value>
  </data>
  <data name="ImagesAttached" xml:space="preserve">
    <value>Images Attached</value>
  </data>
  <data name="AllDocumentInfo" xml:space="preserve">
    <value>All Document Detail</value>
  </data>
  <data name="LabelSetupItem" xml:space="preserve">
    <value>Master Status List</value>
  </data>
  <data name="LabelSetup" xml:space="preserve">
    <value>Master Status</value>
  </data>
  <data name="CustomerRequirementsBOMImport" xml:space="preserve">
    <value>BOM Import</value>
  </data>
  <data name="ClientImportBOMMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="ClientBOMAdd" xml:space="preserve">
    <value>Add New BOM</value>
  </data>
  <data name="ClientBOMItems" xml:space="preserve">
    <value>Client BOM Items</value>
  </data>
  <data name="BOMImport" xml:space="preserve">
    <value>BOM Import</value>
  </data>
  <data name="BOMImport_Form" xml:space="preserve">
    <value>BOM Import Form</value>
  </data>
  <data name="UploadedExcelFiles" xml:space="preserve">
    <value>Uploaded Excel Files</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="IHSCatalogue" xml:space="preserve">
    <value>Product Catalogue</value>
  </data>
  <data name="MasterLogin" xml:space="preserve">
    <value>MasterLogin</value>
  </data>
  <data name="UtilityOfferImport" xml:space="preserve">
    <value>Offers Import Tool</value>
  </data>
  <data name="UtilityBOMImport" xml:space="preserve">
    <value>Utility BOM Import</value>
    <comment>Utility BOM Import</comment>
  </data>
  <data name="UtilityXMatchImport" xml:space="preserve">
    <value>X-Match Import</value>
  </data>
  <data name="UtilityHUBOfferImport" xml:space="preserve">
    <value>Strategic Offers Import Tool</value>
  </data>
  <data name="PODPDFTitle" xml:space="preserve">
    <value>POD PDF</value>
  </data>
  <data name="ShortShipment" xml:space="preserve">
    <value>Short Shipment</value>
  </data>
  <data name="ShortShipmentNotify" xml:space="preserve">
    <value>Short Shipment Notify</value>
  </data>
  <data name="POApprovals" xml:space="preserve">
    <value>Supplier Approvals</value>
  </data>
  <data name="GILineNotify" xml:space="preserve">
    <value>Goods In Line Notify</value>
  </data>
  <data name="RestrictedManufacture" xml:space="preserve">
    <value>Restricted Manufacturer</value>
  </data>
  <data name="TextWarningMessage" xml:space="preserve">
    <value>Warning Messages</value>
  </data>
  <data name="SSMainInfo" xml:space="preserve">
    <value>Main Information</value>
  </data>
  <data name="PORPDFTitleNew" xml:space="preserve">
    <value>POR</value>
  </data>
  <data name="ECCN" xml:space="preserve">
    <value>ECCN</value>
  </data>
  <data name="UtilityStockImport" xml:space="preserve">
    <value>Utility Stock Import</value>
  </data>
  <data name="CompanyProspects" xml:space="preserve">
    <value>Prospects Qualification</value>
  </data>
  <data name="ToDoList" xml:space="preserve">
    <value>To Do List</value>
  </data>
  <data name="ToDoListType" xml:space="preserve">
    <value>To Do List Type</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="UtilityLog" xml:space="preserve">
    <value>Utility Log</value>
  </data>
  <data name="GlobalProductMainCategory" xml:space="preserve">
    <value>Global Product Main Category</value>
  </data>
  <data name="CONTACTGROUP" xml:space="preserve">
    <value>Add in Manufacturer Group Code</value>
  </data>
  <data name="ExportApprovalStatus" xml:space="preserve">
    <value>Export Approval Status</value>
  </data>
  <data name="OGELLinesExport" xml:space="preserve">
    <value>OGEL Lines</value>
  </data>
  <data name="EndUserUndertakingForm" xml:space="preserve">
    <value>End User Undertaking Form</value>
  </data>
  <data name="Utility_ReverseLogisticsImport" xml:space="preserve">
    <value>Reverse Logistics Import Tool</value>
  </data>
  <data name="BOMManager" xml:space="preserve">
    <value>BOM Manager</value>
  </data>
  <data name="UtilityBOMManagerImport" xml:space="preserve">
    <value>BOM Manager Import</value>
  </data>
  <data name="CompanyApiCustomer" xml:space="preserve">
    <value>Customer API</value>
  </data>
  <data name="UtilityPriceQuoteImport" xml:space="preserve">
    <value>Price Quote Import Tool</value>
    <comment>Utility Price Quote Import</comment>
  </data>
  <data name="AS6081" xml:space="preserve">
    <value>Type Of Suppliers</value>
  </data>
  <data name="AS6081_ROS" xml:space="preserve">
    <value>Risk Of Supplier</value>
  </data>
  <data name="AS6081_RCS" xml:space="preserve">
    <value>Reason For Chosen Supplier</value>
  </data>
  <data name="HubRFQAssignmentHistory" xml:space="preserve">
    <value>RFQ Assignment History</value>
  </data>
  <data name="PDFDocumentFileSize" xml:space="preserve">
    <value>Document File Size</value>
  </data>
  <data name="EntertainmentType" xml:space="preserve">
    <value>Entertainment Type</value>
  </data>
  <data name="CompanyGlobalSalesPDetails" xml:space="preserve">
    <value>Global Sales Person Details</value>
  </data>
  <data name="GSA" xml:space="preserve">
    <value>Global Sales Access</value>
  </data>
  <data name="ClientInvoiceHeader" xml:space="preserve">
    <value>Client Invoice Header</value>
  </data>
  <data name="ClientInvoiceHeaderImage" xml:space="preserve">
    <value>Client Invoice Header Image</value>
  </data>
  <data name="CompanyInsuranceCertificate" xml:space="preserve">
    <value>Company Insurance Certificate</value>
  </data>
  <data name="CIPPODPDFTitle" xml:space="preserve">
    <value>Certificate PDF</value>
  </data>
  <data name="BOMSearchAssign" xml:space="preserve">
    <value>Assign BOM</value>
  </data>
  <data name="UtilityAlternativeImport" xml:space="preserve">
    <value>Utility Alternative Import</value>
  </data>
  <data name="CompanyFinanceInfo" xml:space="preserve">
    <value>Finance</value>
  </data>
  <data name="PPVBOM" xml:space="preserve">
    <value>PPV/ BOM Qualification</value>
  </data>
  <data name="BOMPVV" xml:space="preserve">
    <value>PPV/ BOM Qualification</value>
  </data>
  <data name="Utility_ProspectiveOffers" xml:space="preserve">
    <value>Prospective Offers</value>
  </data>
  <data name="UtilityProspectiveOfferImport" xml:space="preserve">
    <value>Prospective Offer Import Tool</value>
  </data>
  <data name="UtilityHUBOfferImportLarge" xml:space="preserve">
    <value>Bulk Offer Scheduled Import</value>
  </data>
  <data name="Orders_ProspectiveCrossSelling" xml:space="preserve">
    <value>Prospective Cross Selling</value>
  </data>
  <data name="ProsCrossSellImportTool" xml:space="preserve">
    <value>Prospective Cross Selling Import Tool</value>
  </data>
  <data name="OGELLicenses" xml:space="preserve">
    <value>OGELLicenses</value>
  </data>
  <data name="StarRating" xml:space="preserve">
    <value>Star Rating</value>
  </data>
  <data name="IHSAdd" xml:space="preserve">
    <value>Add Part Information</value>
  </data>
  <data name="InvoiceExportHistory" xml:space="preserve">
    <value>Invoice Export History</value>
  </data>
</root>