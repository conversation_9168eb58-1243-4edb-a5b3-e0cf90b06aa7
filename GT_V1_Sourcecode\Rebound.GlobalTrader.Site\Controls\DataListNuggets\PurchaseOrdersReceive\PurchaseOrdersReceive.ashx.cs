//------------------------------------------------------------------------------------
// RP 15.02.2010:
// - fixed Company filter not working on "ready to receive" search
//------------------------------------------------------------------------------------
using System;
using System.Data;
/* Marker     changed by      date         Remarks
/* [0001]      Abhinav <PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class PurchaseOrdersReceive : Base
    {

        private string _strCallType = "";

        public override void ProcessRequest(HttpContext context)
        {
            base.ProcessRequest(context);
            if (Action == "GetData_All") GetData_All();
        }

        protected override void GetData()
        {
            _strCallType = "READY";
            List<PurchaseOrderLine> lst = PurchaseOrderLine.DataListNuggetReadyToReceive(
                SessionManager.ClientID
                , GetFormValue_NullableInt("SortIndex")
                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("PageSize", 10)
                  //[0001] start code
                  //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                 //[0001] end code
                 //, GetFormValue_StringForNameSearch("Contact")
                 , GetFormValue_StringForNameSearchDecode("Contact")
                 //, GetFormValue_StringForNameSearch("Company")
                 , GetFormValue_StringForNameSearchDecode("Company")
                , GetFormValue_NullableInt("Buyer")
                , GetFormValue_NullableInt("PONoLo")
                , GetFormValue_NullableInt("PONoHi")
                , GetFormValue_NullableDateTime("DateOrderedFrom")
                , GetFormValue_NullableDateTime("DateOrderedTo")
                , GetFormValue_NullableDateTime("ExpediteDateFrom")
                , GetFormValue_NullableDateTime("ExpediteDateTo")
                , GetFormValue_NullableDateTime("DeliveryDateFrom")
                , GetFormValue_NullableDateTime("DeliveryDateTo")
                , GetFormValue_NullableInt("IPONoLo")
                , GetFormValue_NullableInt("IPONoHi")
                , GetFormValue_NullableInt("Client")
                , GetFormValue_Boolean("IsGlobalLogin")
            );
            ProcessRows(lst);
            lst = null;
            base.GetData();
        }

        private void GetData_All()
        {
            _strCallType = "ALL";
            List<PurchaseOrderLine> lst = PurchaseOrderLine.DataListNuggetAllForReceiving(
                SessionManager.ClientID
                , GetFormValue_NullableInt("SortIndex")
                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("PageSize", 10)
                  //[0001] start code
                  //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                 //[0001] end code
                 //, GetFormValue_StringForNameSearch("Contact")
                 , GetFormValue_StringForNameSearchDecode("Contact")
                 //, GetFormValue_StringForNameSearch("Company")
                 , GetFormValue_StringForNameSearchDecode("Company")
                , GetFormValue_NullableInt("Buyer")
                , GetFormValue_Boolean("RecentOnly")
                , GetFormValue_Boolean("IncludeClosed")
                , GetFormValue_NullableInt("PONoLo")
                , GetFormValue_NullableInt("PONoHi")
                , GetFormValue_NullableDateTime("DateOrderedFrom")
                , GetFormValue_NullableDateTime("DateOrderedTo")
                , GetFormValue_NullableDateTime("ExpediteDateFrom")
                , GetFormValue_NullableDateTime("ExpediteDateTo")
                , GetFormValue_NullableDateTime("DeliveryDateFrom")
                , GetFormValue_NullableDateTime("DeliveryDateTo")
                , GetFormValue_NullableInt("IPONoLo")
                , GetFormValue_NullableInt("IPONoHi")
                , SessionManager.IsPOHub
                , GetFormValue_NullableInt("Client")
                , GetFormValue_Boolean("IsGlobalLogin")
            );
            ProcessRows(lst);
            lst = null;
            base.GetData();
        }

        private void ProcessRows(List<PurchaseOrderLine> lst)
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnRows = new JsonObject(true);
            jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnRow = new JsonObject();
                jsnRow.AddVariable("ID", lst[i].PurchaseOrderNo);
                jsnRow.AddVariable("No", lst[i].PurchaseOrderNumber);
                jsnRow.AddVariable("Part", lst[i].Part);
                jsnRow.AddVariable("ROHS", lst[i].ROHS);
                jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].QuantityOrdered));
                jsnRow.AddVariable("Oustanding", Functions.FormatNumeric(lst[i].QuantityOutstanding));
                jsnRow.AddVariable("CM", lst[i].CompanyName);
                jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                jsnRow.AddVariable("Contact", lst[i].ContactName);
                jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
                jsnRow.AddVariable("Delivery", Functions.FormatDate(lst[i].DeliveryDate));
                jsnRow.AddVariable("Ship", Functions.FormatDate(lst[i].EarliestShipDate));
                jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
                jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
                jsnRow.AddVariable("IPOID", lst[i].InternalPurchaseOrderNo);
                jsnRow.AddVariable("IPONo", lst[i].InternalPurchaseOrderNumber);
                jsnRow.AddVariable("ClientName", lst[i].ClientName);
                jsnRow.AddVariable("PoClientName", lst[i].PoClientName);
                jsnRow.AddVariable("ReceivePOStatus", lst[i].ReceivePOStatus);
                jsnRows.AddVariable(jsnRow);
                jsnRow.Dispose();
                jsnRow = null;
            }
            jsn.AddVariable("Results", jsnRows);
            OutputResult(jsn);
            jsnRows.Dispose(); jsnRows = null;
            jsn.Dispose(); jsn = null;
            SaveState();
        }

        protected override void AddFilterStates()
        {
            AddExplicitFilterState("CallType", _strCallType);
            AddFilterState("Part");
            AddFilterState("Contact");
            AddFilterState("Company");
            AddFilterState("Buyer");
            AddFilterState("RecentOnly");
            AddFilterState("IncludeClosed");
            AddFilterState("PONo");
            AddFilterState("DateOrderedFrom");
            AddFilterState("DateOrderedTo");
            AddFilterState("ExpediteDateFrom");
            AddFilterState("ExpediteDateTo");
            AddFilterState("DeliveryDateFrom");
            AddFilterState("DeliveryDateTo");
            AddFilterState("IPONo");
            base.AddFilterStates();
        }

    }
}