﻿<%@ Control Language="C#" CodeBehind="SIDebitNotes.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.SIDebitNotes" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" ShowPaging="false"   InitialSortDirection="DESC">
    <FieldsLeft>
        <ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNoNPR"  />
    </FieldsLeft>
    <FieldsRight>
        <ReboundUI_FilterDataItemRow:DateSelect id="ctlDebitNoteDateFrom" runat="server"  ResourceTitle="DebitDateFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDebitNoteDateTo" runat="server" ResourceTitle="DebitDateTo" />
    </FieldsRight>
</ReboundUI_ItemSearch:DesignBase>