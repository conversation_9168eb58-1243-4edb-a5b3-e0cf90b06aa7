<%--//Marker     Changed by      Date         Remarks
//    [003]      Suhail          15/05/2018   Added Avoidable on CRMA Line--%>
<%--[001]      Vinay           15/06/2018    [REB-11304]: CHG-570795 Hazarders product type
    [RP-2339]	Ravi		11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens    
--%>

<%@ Control Language="C#" CodeBehind="CRMALines_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true"
    ShowQuickHelp="false" NumberOfSteps="3" MultiStepControlID="ctlMultiStep">
    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save"
            IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel"
            IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Explanation>
        <%=Functions.GetGlobalResource("FormExplanations", "CRMALines_Add")%></Explanation>
    <Explanation>
        <ReboundUI:MultiStep ID="ctlMultiStep" runat="server">
            <Items>
                <ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="CRMALines_Add_SelectItem"
                    IsSelected="true" />
                <ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="CRMALines_Add_EnterDetail" />
            </Items>
        </ReboundUI:MultiStep>
    </Explanation>
    <Content>
        <!-- Step 1 ------------------------------------------------------------------->
        <ReboundUI_Table:Form ID="frmStep1" runat="server">
            <asp:TableRow ID="trSelectInvoiceLine" runat="server">
                <asp:TableCell ID="tdSelectInvoiceLine" runat="server">
                    <asp:Panel ID="pnlLines" runat="server" CssClass="itemSearch invisible">
                        <h5>
                            <%=Functions.GetGlobalResource("Misc", "InvoiceLinesForCRMA")%></h5>
                        <asp:Panel ID="pnlLinesError" runat="server" CssClass="itemSearchError invisible">
                            <asp:Label ID="lblLinesError" runat="server" /></asp:Panel>
                        <asp:Panel ID="pnlLinesLoading" runat="server" CssClass="loading invisible">
                            <%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
                        <asp:Panel ID="pnlLinesNoneFound" runat="server" CssClass="noneFound invisible">
                            <%=Functions.GetGlobalResource("NotFound", "Generic")%></asp:Panel>
                        <ReboundUI:FlexiDataTable ID="tblLines" runat="server" AllowSelection="true" AllowMultipleSelection="false" />
                    </asp:Panel>
                    <asp:Panel ID="pnlLinesNotAvailable" runat="server" CssClass="noneFound formMessage invisible">
                        <%=Functions.GetGlobalResource("NotFound", "CRMANotAvailable")%></asp:Panel>
                </asp:TableCell>
            </asp:TableRow>
        </ReboundUI_Table:Form>
        <!-- Step 2 ------------------------------------------------------------------->
        <ReboundUI_Table:Form ID="frmStep2" runat="server">
            <ReboundUI_Form:FormField ID="ctlCustomerRMA" runat="server" FieldID="lblCustomerRMA"
                ResourceTitle="CustomerRMANo">
                <Field>
                    <asp:Label ID="lblCustomerRMA" runat="server" /></Field>
            </ReboundUI_Form:FormField>

            <%--[RP-2339] start--%>
            <ReboundUI_Form:FormField id="ctlAS6081" runat="server" FieldID="lblAS6081" ResourceTitle="AS6081Filter">
                <Field><asp:Label ID="lblAS6081" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <%--[RP-2339] end--%>

            <ReboundUI_Form:FormField ID="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
                <Field>
                    <asp:Label ID="lblCustomer" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlPartNo" runat="server" FieldID="lblPartNo" ResourceTitle="PartNo">
                <Field>
                    <asp:Label ID="lblPartNo" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlQuantityShipped" runat="server" FieldID="lblQuantityShipped"
                ResourceTitle="QuantityShipped">
                <Field>
                    <asp:Label ID="lblQuantityShipped" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlQuantityAllocated" runat="server" FieldID="lblQuantityAllocated"
                ResourceTitle="QuantityAllocated">
                <Field>
                    <asp:Label ID="lblQuantityAllocated" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlQuantity" runat="server" FieldID="txtQuantity" ResourceTitle="Quantity"
                IsRequiredField="true">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="100" TextBoxMode="Numeric" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlReturnDate" runat="server" FieldID="txtReturnDate"
                ResourceTitle="ReturnDate" IsRequiredField="true">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtReturnDate" runat="server" Width="140" />
                    <ReboundUI:Calendar ID="calReturnDate" runat="server" RelatedTextBoxID="txtReturnDate" />
                </Field>
            </ReboundUI_Form:FormField>
            
			<ReboundUI_Form:FormField id="ctlReason" runat="server" FieldID="ctlItemsReason1" ResourceTitle="Reason" DisplayRequiredFieldMarkerOnly="true">
				<Field>
				<ReboundUI_Table:Base id="tblItemsReason1" runat="server">
				<ReboundUI_FormFieldCollection:HoverMenuList id="ctlItemsReason1" runat="server" style="margin:0px;"  />
				</ReboundUI_Table:Base>				
				<%--<ReboundUI:ReboundTextBox ID="txtReason" runat="server" Width="400" TextMode="MultiLine" Rows="8" />--%>
				</Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlReasion2" runat="server" FieldID="tblItemsReason2" ResourceTitle="Reason2">
				<Field>
				<ReboundUI_Table:Base id="tblItemsReason2" runat="server">
				<ReboundUI_FormFieldCollection:HoverMenuList id="ctlItemsReason2" runat="server" style="margin:0px;" />				
				</ReboundUI_Table:Base>
				<%--<ReboundUI:ReboundTextBox ID="txtReason" runat="server" Width="400" TextMode="MultiLine" Rows="8" />--%>
				</Field>
			</ReboundUI_Form:FormField>
             <%--[003] Code start--%>
            <ReboundUI_Form:FormField ID="ctlIsAvoidable" runat="server" FieldID="chkIsAvoidable" ResourceTitle="IsAvoidable">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkIsAvoidable" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>
			 <%--[003] Code end--%>
			<ReboundUI_Form:FormField id="ctlLineNotes" runat="server" FieldID="txtLineNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtLineNotes" runat="server" Width="400" TextMode="multiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlRootCause" runat="server" FieldID="txtRootCause" ResourceTitle="RootCause">
				<Field><ReboundUI:ReboundTextBox ID="txtRootCause" runat="server" Width="400" TextMode="multiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
			
           <%-- <ReboundUI_Form:FormField ID="ctlReason" runat="server" FieldID="txtReason" ResourceTitle="Reason"
                IsRequiredField="true">
                <Field>
                    
                    <ReboundUI:ReboundTextBox ID="txtReason" runat="server" Width="400" TextMode="MultiLine"
                        Rows="8" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField ID="ctlLineNotes" runat="server" FieldID="txtLineNotes"
                ResourceTitle="Notes">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtLineNotes" runat="server" Width="400" TextMode="multiLine"
                        Rows="2" /></Field>
            </ReboundUI_Form:FormField>--%>
            <%--[002] start code--%>
           <ReboundUI_Form:FormField id="ctlPrintHazWar" runat="server" FieldID="chkPrintHazWar" ResourceTitle="PrintHazWarning" >
				<Field><ReboundUI:ImageCheckBox ID="chkPrintHazWar" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
           <%--[002] start end--%>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>
