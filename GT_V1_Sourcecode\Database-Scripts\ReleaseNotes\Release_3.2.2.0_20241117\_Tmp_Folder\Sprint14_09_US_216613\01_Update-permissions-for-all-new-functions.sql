﻿/*   
===========================================================================================  
TASK	UPDATED BY      DATE			ACTION		DESCRIPTION  
-		An.TranTan		30-Oct-2024		Create		Correct value of new security functions for all security groups
===========================================================================================  
*/
--Backup tbSecurityGroupSecurityFunctionPermission
SELECT *
INTO [tbSecurityGroupSecurityFunctionPermission_bk_24_11_01]
FROM [dbo].[tbSecurityGroupSecurityFunctionPermission]
GO

DECLARE @tbSecurityFunctions TABLE (SecurityFunctionId INT);

--get SecurityFunctionId of new security functions
INSERT INTO @tbSecurityFunctions (SecurityFunctionId)
SELECT SecurityFunctionId
FROM tbSecurityFunction
WHERE FunctionName IN (
	'Contact_CompanyDetail_MainInfo_PremierCustomer_Activate'
	,'Orders_ProspectiveCrossSelling'
	,'Orders_ProsCrossSellingDetail'
	,'Setup_CompanySettings_OGELLicenses'
	,'Setup_CompanySettings_OGELLicenses_Add'
	,'Setup_CompanySettings_OGELLicenses_Edit'
	,'Utility_HUBOfferImportLarge'
)

/* Point No.1: Set default value of new functions for all groups: ADMIN: ACCESS, Other: NOT-ACCESS */
DECLARE @SecurityGroupId INT
DECLARE @SecurityFunctionId INT
DECLARE @IsAdmin BIT

DECLARE @dbcursor CURSOR SET @dbcursor = CURSOR
FOR
SELECT sg.SecurityGroupId,
	SecurityFunctionId,
	sg.Administrator AS IsAdmin
FROM tbSecurityGroup sg,
	@tbSecurityFunctions sf

OPEN @dbcursor

FETCH NEXT
FROM @dbcursor
INTO @SecurityGroupId,
	@SecurityFunctionId,
	@IsAdmin

WHILE @@FETCH_STATUS = 0
BEGIN
	IF NOT EXISTS (
			SELECT TOP 1 1
			FROM tbSecurityGroupSecurityFunctionPermission
			WHERE SecurityGroupNo = @SecurityGroupId
				AND SecurityFunctionNo = @SecurityFunctionId
			)
	BEGIN
		PRINT 'InSert ' + CAST(@SecurityGroupId AS VARCHAR(10)) + ' ' + CAST(@SecurityFunctionId AS VARCHAR(10)) + ' ' + CAST(@IsAdmin AS VARCHAR(10))

		INSERT INTO tbSecurityGroupSecurityFunctionPermission (
			SecurityGroupNo,
			SecurityFunctionNo,
			IsAllowed,
			DLUP,
			UpdatedBy
			)
		SELECT @SecurityGroupId,
			@SecurityFunctionId,
			@IsAdmin, --default: allow for admin only
			GETDATE(),
			1 --system administrator
	END
	ELSE
	BEGIN
		PRINT 'Existing ' + CAST(@SecurityGroupId AS VARCHAR(10)) + ' ' + CAST(@SecurityFunctionId AS VARCHAR(10)) + ' ' + CAST(@IsAdmin AS VARCHAR(10))
	END

	FETCH NEXT
	FROM @dbcursor
	INTO @SecurityGroupId,
		@SecurityFunctionId,
		@IsAdmin
END
/*============END===========*/

/* Point No.2: Set InitiallyProhibitedForNewLogins = 1 for new functions so it's value will be NO-ACCESS when new created groups */
UPDATE sf
SET 
	InitiallyProhibitedForNewLogins = 1,
	UpdatedBy = 1, --System Administrator
	DLUP = GETDATE()
FROM tbSecurityFunction sf
JOIN @tbSecurityFunctions tsf on tsf.SecurityFunctionId = sf.SecurityFunctionId
WHERE sf.InitiallyProhibitedForNewLogins = 0
/*============END===========*/

/*====Point No.3 : Update permisison values of function 'Allow inactive/ active premier Customer' to all groups ====*/
DECLARE @ActivePremierCustomerPermissionId INT;

--get 'Allow inactive/ active premier Customer' function ID
SELECT TOP 1 @ActivePremierCustomerPermissionId = SecurityFunctionId 
FROM tbSecurityFunction
WHERE FunctionName = 'Contact_CompanyDetail_MainInfo_PremierCustomer_Activate'

--update value = ACCESS for Admin and Sales Director
;with cteAdminAndDirectorGroup as(
	select SecurityGroupId from tbSecurityGroup
	where Administrator = 1 OR SecurityGroupName = 'Sales - Access Level - Premier Accounts Director'
)
UPDATE sgf
SET 
	sgf.IsAllowed = 1,
	UpdatedBy = 1, --System Administrator
	DLUP = GETDATE()
FROM tbSecurityGroupSecurityFunctionPermission sgf
JOIN cteAdminAndDirectorGroup cte on cte.SecurityGroupId = sgf.SecurityGroupNo
WHERE sgf.SecurityFunctionNo = @ActivePremierCustomerPermissionId
	AND sgf.IsAllowed = 0

--update value = NO-ACCESS for other groups
;with cteOtherGroup as(
	select SecurityGroupId from tbSecurityGroup
	where Administrator = 0 AND SecurityGroupName <> 'Sales - Access Level - Premier Accounts Director'
)
UPDATE sgf
SET 
	sgf.IsAllowed = 0,
	UpdatedBy = 1, --System Administrator
	DLUP = GETDATE()
FROM tbSecurityGroupSecurityFunctionPermission sgf
JOIN cteOtherGroup cte on cte.SecurityGroupId = sgf.SecurityGroupNo
WHERE sgf.SecurityFunctionNo = @ActivePremierCustomerPermissionId
	AND sgf.IsAllowed = 1
/*============END===========*/
GO

