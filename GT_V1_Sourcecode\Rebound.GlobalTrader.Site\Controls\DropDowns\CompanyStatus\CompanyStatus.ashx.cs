//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanyStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("Region");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            var companyStatus = new List<BLL.Country>()
            {
                new BLL.Country { RegionId = -1, RegionName = "All"},
                new BLL.Country { RegionId = 2, RegionName = "Active-Only"},
                new BLL.Country { RegionId = 1, RegionName = "Inactive Only"},
            };
            JsonObject jsn = new JsonObject();
            JsonObject jsnCompanyStatus = new JsonObject(true);
            foreach (BLL.Country status in companyStatus)
            {
                JsonObject jsnStatus = new JsonObject();
                jsnStatus.AddVariable("ID", status.RegionId);
                jsnStatus.AddVariable("Name", status.RegionName);
                jsnCompanyStatus.AddVariable(jsnStatus);
                jsnStatus.Dispose(); jsnStatus = null;
            }
            jsn.AddVariable("CompanyStatus", jsnCompanyStatus);
            jsnCompanyStatus.Dispose();
            jsnCompanyStatus = null;
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
    }
}
