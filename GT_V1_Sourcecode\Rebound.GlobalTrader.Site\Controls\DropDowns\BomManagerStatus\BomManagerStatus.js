Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.initializeBase(this,[n]);this._intCompanyID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/BomManagerStatus");this._objData.set_DataObject("BomManagerStatus");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.BomManagerStatus)for(n=0;n<t.BomManagerStatus.length;n++)this.addOption(t.BomManagerStatus[n].Name,t.BomManagerStatus[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BomManagerStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);