﻿/*
Marker     Changed by               Date         Remarks
[001]      A<PERSON><PERSON><PERSON>           29/07/2021   Implement a new dropdown for supplier Approval Status
*/
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class SupplierApprovalStatusDetails
    {
        #region Constructors

        public SupplierApprovalStatusDetails() { }

        #endregion

        #region Properties

        /// <summary>
        /// PurchaseOrderStatusId (from Table)
        /// </summary>
        public System.Int32 SupplierApprovalStatusId { get; set; }
        /// <summary>
        /// Name (from Table)
        /// </summary>
        public System.String Name { get; set; }

        #endregion
    }
}
