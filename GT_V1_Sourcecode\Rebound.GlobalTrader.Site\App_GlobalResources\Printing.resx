﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="AdviceNote" xml:space="preserve">
    <value>Advice Note</value>
  </data>
  <data name="AirwayBill" xml:space="preserve">
    <value>Airway Bill No</value>
  </data>
  <data name="Allocation" xml:space="preserve">
    <value>Allocation</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="Attention" xml:space="preserve">
    <value>Attention</value>
  </data>
  <data name="AuthorisedBy" xml:space="preserve">
    <value>Authorised By</value>
  </data>
  <data name="Boxes" xml:space="preserve">
    <value>Boxes</value>
  </data>
  <data name="BuyBase" xml:space="preserve">
    <value>Buy Base</value>
  </data>
  <data name="BuyCurrency" xml:space="preserve">
    <value>Buy Currency</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="BuyTerms" xml:space="preserve">
    <value>Buy Terms</value>
  </data>
  <data name="CertificateOfConformance" xml:space="preserve">
    <value>Certificate Of Conformity</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="CountingMethod" xml:space="preserve">
    <value>Counting Method</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="CountryOfOrigin" xml:space="preserve">
    <value>Country Of Origin</value>
  </data>
  <data name="CreditNote" xml:space="preserve">
    <value>Credit Note</value>
  </data>
  <data name="CustNo" xml:space="preserve">
    <value>Cust No</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="CustomerNo" xml:space="preserve">
    <value>Customer No</value>
  </data>
  <data name="CustomerPartNo" xml:space="preserve">
    <value>Customer Part No</value>
  </data>
  <data name="CustomerPONumber" xml:space="preserve">
    <value>Customer PO No</value>
  </data>
  <data name="CustomerRMA" xml:space="preserve">
    <value>Customer RMA</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>DC</value>
  </data>
  <data name="DateCodeReceived" xml:space="preserve">
    <value>Date Code Received</value>
  </data>
  <data name="DateCodeRequired" xml:space="preserve">
    <value>Date Code Required</value>
  </data>
  <data name="DateInspected" xml:space="preserve">
    <value>Date Inspected</value>
  </data>
  <data name="DateIssued" xml:space="preserve">
    <value>Date of Issue</value>
  </data>
  <data name="DateOrdered" xml:space="preserve">
    <value>Date Ordered</value>
  </data>
  <data name="DatePromised" xml:space="preserve">
    <value>Date Promised</value>
  </data>
  <data name="DateQuoted" xml:space="preserve">
    <value>Date Quoted</value>
  </data>
  <data name="DateRaised" xml:space="preserve">
    <value>Date Raised</value>
  </data>
  <data name="DateReceived" xml:space="preserve">
    <value>Date Received</value>
  </data>
  <data name="DateShipped" xml:space="preserve">
    <value>Date Shipped</value>
  </data>
  <data name="DC" xml:space="preserve">
    <value>DC</value>
  </data>
  <data name="DebitNote" xml:space="preserve">
    <value>Debit Note</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="DimensionalWeight" xml:space="preserve">
    <value>Dimensional Weight</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="Duty" xml:space="preserve">
    <value>Duty</value>
  </data>
  <data name="EarliestShipmentDate" xml:space="preserve">
    <value>Earliest Shipment Date</value>
  </data>
  <data name="ETA" xml:space="preserve">
    <value>ETA</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="FreeOnBoard" xml:space="preserve">
    <value>F.O.B.</value>
  </data>
  <data name="Freight" xml:space="preserve">
    <value>Freight</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="GoodsInNote" xml:space="preserve">
    <value>Goods In Note</value>
  </data>
  <data name="GoodsReceivingItem" xml:space="preserve">
    <value>Goods Receiving Item</value>
  </data>
  <data name="GP" xml:space="preserve">
    <value>GP</value>
  </data>
  <data name="GPPercent" xml:space="preserve">
    <value>GP %</value>
  </data>
  <data name="InspectedBy" xml:space="preserve">
    <value>Inspected By</value>
  </data>
  <data name="Invoice" xml:space="preserve">
    <value>Invoice</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Invoice Date</value>
  </data>
  <data name="LandedCost" xml:space="preserve">
    <value>Landed Cost</value>
  </data>
  <data name="LineHeader_Amount" xml:space="preserve">
    <value>Amount</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_Buy" xml:space="preserve">
    <value>Buy</value>
  </data>
  <data name="LineHeader_BuyCurrency" xml:space="preserve">
    <value>Buy Currency</value>
  </data>
  <data name="LineHeader_BuyTerms" xml:space="preserve">
    <value>Buy Terms</value>
  </data>
  <data name="LineHeader_Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="LineHeader_CountryOfOrigin" xml:space="preserve">
    <value>Country of Origin</value>
  </data>
  <data name="LineHeader_CustomerPartNo" xml:space="preserve">
    <value>Customer Part</value>
    <comment>Max 30 chars</comment>
  </data>
  <data name="LineHeader_Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="LineHeader_DateCode" xml:space="preserve">
    <value>DC</value>
    <comment>Max 5 chars</comment>
  </data>
  <data name="LineHeader_DateShipped" xml:space="preserve">
    <value>Date Shipped</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_DeliveryDate" xml:space="preserve">
    <value>Del Date</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_DueDate" xml:space="preserve">
    <value>Due Date</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_Duty" xml:space="preserve">
    <value>Duty</value>
  </data>
  <data name="LineHeader_ETA" xml:space="preserve">
    <value>ETA</value>
  </data>
  <data name="LineHeader_LandedCost" xml:space="preserve">
    <value>Landed Cost</value>
  </data>
  <data name="LineHeader_Manufacturer" xml:space="preserve">
    <value>Mfr</value>
    <comment>Max 5 chars</comment>
  </data>
  <data name="LineHeader_Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="LineHeader_Package" xml:space="preserve">
    <value>Pack</value>
    <comment>Max 5 chars</comment>
  </data>
  <data name="LineHeader_PartNo" xml:space="preserve">
    <value>Part No</value>
    <comment>Max 30 chars</comment>
  </data>
  <data name="LineHeader_Photos" xml:space="preserve">
    <value>Photos</value>
  </data>
  <data name="LineHeader_PO" xml:space="preserve">
    <value>PO</value>
  </data>
  <data name="LineHeader_Price" xml:space="preserve">
    <value>Price</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="LineHeader_ProductDutyCode" xml:space="preserve">
    <value>Duty Code</value>
  </data>
  <data name="LineHeader_Quantity" xml:space="preserve">
    <value>Qty</value>
    <comment>Max 10 chars</comment>
  </data>
  <data name="LineHeader_QuantityCredited" xml:space="preserve">
    <value>Qty Credited</value>
    <comment>Max 10 chars</comment>
  </data>
  <data name="LineHeader_QuantityOrdered" xml:space="preserve">
    <value>Qty Ordered</value>
    <comment>Max 10 chars</comment>
  </data>
  <data name="LineHeader_QuantityShipped" xml:space="preserve">
    <value>Qty Shipped</value>
    <comment>Max 10 chars</comment>
  </data>
  <data name="LineHeader_Reason" xml:space="preserve">
    <value>Reason</value>
    <comment>Max 30 chars</comment>
  </data>
  <data name="LineHeader_Reference" xml:space="preserve">
    <value>Reference</value>
  </data>
  <data name="LineHeader_ReturnDate" xml:space="preserve">
    <value>Return Date</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_ROHS" xml:space="preserve">
    <value>ROHS</value>
  </data>
  <data name="LineHeader_Sell" xml:space="preserve">
    <value>Sell</value>
  </data>
  <data name="LineHeader_SellCurrency" xml:space="preserve">
    <value>Sell Currency</value>
  </data>
  <data name="LineHeader_SellTerms" xml:space="preserve">
    <value>Sell Terms</value>
  </data>
  <data name="LineHeader_ShipCost" xml:space="preserve">
    <value>Ship Cost</value>
  </data>
  <data name="LineHeader_Supplier" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="LineHeader_SupplierPartNo" xml:space="preserve">
    <value>Supplier Part</value>
    <comment>Max 30 chars</comment>
  </data>
  <data name="LineHeader_Taxable" xml:space="preserve">
    <value>Taxable?</value>
  </data>
  <data name="LineHeader_TotalBuy" xml:space="preserve">
    <value>Total Buy</value>
  </data>
  <data name="LineHeader_TotalPrice" xml:space="preserve">
    <value>Total Price</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="LineHeader_TotalSell" xml:space="preserve">
    <value>Total Sell</value>
  </data>
  <data name="LineHeader_UnitPrice" xml:space="preserve">
    <value>Unit Price</value>
    <comment>Max 12 chars</comment>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Mfr</value>
  </data>
  <data name="ManufacturerReceived" xml:space="preserve">
    <value>Mfr Received</value>
  </data>
  <data name="ManufacturerRequired" xml:space="preserve">
    <value>Mfr Required</value>
  </data>
  <data name="MFG" xml:space="preserve">
    <value>Mfg</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Package</value>
  </data>
  <data name="PackageReceived" xml:space="preserve">
    <value>Package Received</value>
  </data>
  <data name="PackageRequired" xml:space="preserve">
    <value>Package Required</value>
  </data>
  <data name="PackingSlip" xml:space="preserve">
    <value>Packing Slip</value>
  </data>
  <data name="PartMarking" xml:space="preserve">
    <value>Part Marking</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="PartNoReceived" xml:space="preserve">
    <value>Part No Received</value>
  </data>
  <data name="PartNoRequired" xml:space="preserve">
    <value>Part No Required</value>
  </data>
  <data name="Photos" xml:space="preserve">
    <value>Photo(s)</value>
  </data>
  <data name="PO" xml:space="preserve">
    <value>PO</value>
  </data>
  <data name="POCRMA" xml:space="preserve">
    <value>PO / CRMA</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="Printed" xml:space="preserve">
    <value>Printed</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="ProFormaInvoice" xml:space="preserve">
    <value>Pro Forma Invoice</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Purchase Order</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="QuantityCredited" xml:space="preserve">
    <value>Qty Credited</value>
  </data>
  <data name="QuantityOrdered" xml:space="preserve">
    <value>Qty Ordered</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>Qty Received</value>
  </data>
  <data name="QuantityRequired" xml:space="preserve">
    <value>Qty Required</value>
  </data>
  <data name="QuantityShipped" xml:space="preserve">
    <value>Qty Shipped</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Quote</value>
  </data>
  <data name="RaisedBy" xml:space="preserve">
    <value>Raised By</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Reason</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Received</value>
  </data>
  <data name="ReceivedBy" xml:space="preserve">
    <value>Received By</value>
  </data>
  <data name="Reference" xml:space="preserve">
    <value>Reference</value>
  </data>
  <data name="RefInvoice" xml:space="preserve">
    <value>Ref Invoice</value>
  </data>
  <data name="RefPurchaseOrder" xml:space="preserve">
    <value>Reference Purchase Order</value>
  </data>
  <data name="Required" xml:space="preserve">
    <value>Required</value>
  </data>
  <data name="ReturnAddress" xml:space="preserve">
    <value>Return Address</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>Expected Return</value>
  </data>
  <data name="RMA" xml:space="preserve">
    <value>RMA</value>
  </data>
  <data name="ROHS" xml:space="preserve">
    <value>ROHS Compliance</value>
  </data>
  <data name="ROHSReceived" xml:space="preserve">
    <value>ROHS Received</value>
  </data>
  <data name="ROHSRequired" xml:space="preserve">
    <value>ROHS Required</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Order Acknowledgement</value>
  </data>
  <data name="Salesperson" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="SellBase" xml:space="preserve">
    <value>Sell Base</value>
  </data>
  <data name="SellCurrency" xml:space="preserve">
    <value>Sell Currency</value>
  </data>
  <data name="SellTerms" xml:space="preserve">
    <value>Sell Terms</value>
  </data>
  <data name="SerialNosRecorded" xml:space="preserve">
    <value>Serial Nos Recorded</value>
  </data>
  <data name="ShipASAP" xml:space="preserve">
    <value>Ship ASAP</value>
  </data>
  <data name="ShipCost" xml:space="preserve">
    <value>Ship Cost</value>
  </data>
  <data name="ShippedBy" xml:space="preserve">
    <value>Shipped By</value>
  </data>
  <data name="ShippedVia" xml:space="preserve">
    <value>Shipped Via</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>Shipping Cost</value>
  </data>
  <data name="ShippingCharge" xml:space="preserve">
    <value>Shipping Charge</value>
  </data>
  <data name="ShippingCost" xml:space="preserve">
    <value>Shipping Cost</value>
  </data>
  <data name="ShipTo" xml:space="preserve">
    <value>Ship To</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Ship Via</value>
  </data>
  <data name="SignedPurchashing" xml:space="preserve">
    <value>Signed Purchasing</value>
  </data>
  <data name="SignedSales" xml:space="preserve">
    <value>Signed Sales</value>
  </data>
  <data name="SoldTo" xml:space="preserve">
    <value>Sold To</value>
  </data>
  <data name="SOReport" xml:space="preserve">
    <value>Sales Order Report for SO</value>
  </data>
  <data name="SubTotal" xml:space="preserve">
    <value>Subtotal</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="SupplierInvoice" xml:space="preserve">
    <value>Supplier Invoice</value>
  </data>
  <data name="SupplierPartNo" xml:space="preserve">
    <value>Supplier Part No</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="Symbol_Weight_KG" xml:space="preserve">
    <value>kg</value>
  </data>
  <data name="Symbol_Weight_Pounds" xml:space="preserve">
    <value>lb</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>Taxable</value>
  </data>
  <data name="TaxName" xml:space="preserve">
    <value>Tax Name</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Terms</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="TotalAllocSell" xml:space="preserve">
    <value>Total Allocated Sell</value>
  </data>
  <data name="TotalBuy" xml:space="preserve">
    <value>Total Buy</value>
  </data>
  <data name="TotalPrice" xml:space="preserve">
    <value>Total Price</value>
  </data>
  <data name="TotalSell" xml:space="preserve">
    <value>Total Sell</value>
  </data>
  <data name="TotalWeight" xml:space="preserve">
    <value>Total Weight</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
    <value>Unit Price</value>
  </data>
  <data name="VATNo" xml:space="preserve">
    <value>VAT No</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="YourCreditNote" xml:space="preserve">
    <value>Your Credit Note</value>
  </data>
  <data name="YourDebitNote" xml:space="preserve">
    <value>Your Debit Note</value>
  </data>
  <data name="YourInvoice" xml:space="preserve">
    <value>Your Invoice</value>
  </data>
  <data name="YourPONumber" xml:space="preserve">
    <value>Your PO </value>
  </data>
  <data name="YourReturnNote" xml:space="preserve">
    <value>Your Return Note</value>
  </data>
  <data name="Incoterms" xml:space="preserve">
    <value>Incoterms</value>
  </data>
  <data name="ShippingAccount" xml:space="preserve">
    <value>Shipping A/C</value>
  </data>
  <data name="Allocations" xml:space="preserve">
    <value>Allocations</value>
  </data>
  <data name="CustomerSupplier" xml:space="preserve">
    <value>Customer / Supplier</value>
  </data>
  <data name="SalespersonAuthoriser" xml:space="preserve">
    <value>Salesperson / Authoriser</value>
  </data>
  <data name="SOSRMA" xml:space="preserve">
    <value>SO / SRMA</value>
  </data>
  <data name="CompRegNo" xml:space="preserve">
    <value>Company Reg. No.</value>
  </data>
  <data name="ShippingNotes" xml:space="preserve">
    <value>Shipping Notes</value>
  </data>
  <data name="QFreight" xml:space="preserve">
    <value>Estimated Freight Only</value>
  </data>
  <data name="Advice_Note" xml:space="preserve">
    <value>Advice Note</value>
  </data>
  <data name="Customer_RMA" xml:space="preserve">
    <value>Customer RMA</value>
  </data>
  <data name="NPRReport" xml:space="preserve">
    <value>NPR Report for GoodsIn</value>
  </data>
  <data name="NPR_No" xml:space="preserve">
    <value>NPR No</value>
  </data>
  <data name="Part_No" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="PO_NO" xml:space="preserve">
    <value>PO NO</value>
  </data>
  <data name="QLocation" xml:space="preserve">
    <value>Q Location</value>
  </data>
  <data name="Raised_By" xml:space="preserve">
    <value>Raised By</value>
  </data>
  <data name="RejectedQty" xml:space="preserve">
    <value>Rejected Qty</value>
  </data>
  <data name="SalesOrderNo" xml:space="preserve">
    <value>SO No</value>
  </data>
  <data name="Supplier_RMA" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="TotalRejectedValue" xml:space="preserve">
    <value>Total Rejected value</value>
  </data>
  <data name="Unit_Cost" xml:space="preserve">
    <value>Unit Cost</value>
  </data>
  <data name="Authorise_Name" xml:space="preserve">
    <value>Authorise Name</value>
  </data>
  <data name="Authorise_signature" xml:space="preserve">
    <value>Authorise by/signature</value>
  </data>
  <data name="Closure" xml:space="preserve">
    <value>6. Closure (Logistics only to complete)</value>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="NPRCompletedByNo" xml:space="preserve">
    <value>Completed by</value>
  </data>
  <data name="LogisticSRMADate" xml:space="preserve">
    <value>Date raised</value>
  </data>
  <data name="DebitNoteNo" xml:space="preserve">
    <value>Debit note no</value>
  </data>
  <data name="IncurredCostToSales_Stock" xml:space="preserve">
    <value>Incurred cost to sales. Yes/No</value>
  </data>
  <data name="Move_into_Stock" xml:space="preserve">
    <value>4c: Move into stock (Sales or Purchasing to complete)</value>
  </data>
  <data name="NPRHeading_ActionRequired" xml:space="preserve">
    <value>4. Action Required (Sales/Purchasing to complete). Please complete either 4a,4b,4c or 4d</value>
  </data>
  <data name="NPRHeading_AllCustomer" xml:space="preserve">
    <value>All customers unauthorized return should be recorded in Section 3: Non Conformance details.</value>
  </data>
  <data name="NPRHeading_NonConformance" xml:space="preserve">
    <value>3. Non Conformance Details. Reasons for rejection (Warehouse/Sales/Quality)</value>
  </data>
  <data name="NPRHeading_Nonconforming" xml:space="preserve">
    <value>Nonconforming Product Report</value>
  </data>
  <data name="NPRHeading_OrderDetail" xml:space="preserve">
    <value>2. Order Details (Warehouse to complete)</value>
  </data>
  <data name="NPRHeading_Originator" xml:space="preserve">
    <value>1. Originator (Warehouse to complete)</value>
  </data>
  <data name="OutworkerName" xml:space="preserve">
    <value>Outworker Name</value>
  </data>
  <data name="OutworkerPONo" xml:space="preserve">
    <value>Outworker P/o No</value>
  </data>
  <data name="Required_outwork" xml:space="preserve">
    <value>4d: Required outwork (Sales or Purchasing to complete)</value>
  </data>
  <data name="Return_to_supplier" xml:space="preserve">
    <value>4a : Return to supplier (Purchasing to complete)</value>
  </data>
  <data name="Sales_detail_Authorisation" xml:space="preserve">
    <value>5. Sales detail &amp;amp; Authorisation</value>
  </data>
  <data name="Sales_Person" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="Scrap" xml:space="preserve">
    <value>4b : Scrap (Purchasing to complete)</value>
  </data>
  <data name="LogisticSRMANo" xml:space="preserve">
    <value>SRMA No</value>
  </data>
  <data name="StockLocation" xml:space="preserve">
    <value>Stock location. &lt;br /&gt;(EPO/Consignment department only)</value>
  </data>
  <data name="SupplierRef" xml:space="preserve">
    <value>Supplier Reference</value>
  </data>
  <data name="Supplier_RMA_No" xml:space="preserve">
    <value>Supplier RMA No</value>
  </data>
  <data name="Supplier_Ship_Via" xml:space="preserve">
    <value>Supplier Ship Via</value>
  </data>
  <data name="Supplier_Ship_via_no" xml:space="preserve">
    <value>Supplier Ship via a/c no</value>
  </data>
  <data name="SupplierToCredit" xml:space="preserve">
    <value>Supplier to credit</value>
  </data>
  <data name="SupplierType" xml:space="preserve">
    <value>Supplier Type</value>
  </data>
  <data name="BankFee" xml:space="preserve">
    <value>Bank Fee</value>
  </data>
  <data name="ExchangeRateSummary" xml:space="preserve">
    <value>Exchange rate used 1 {0} = {1} {2} {3}</value>
  </data>
  <data name="BillTo" xml:space="preserve">
    <value>Bill To</value>
  </data>
  <data name="LocalEquivalent" xml:space="preserve">
    <value>Local equivalent is for internal purpose and not to be used for payment, please pay in the currency the invoice has been issued in</value>
  </data>
  <data name="LineHeader_Details" xml:space="preserve">
    <value>Details</value>
  </data>
  <data name="LineHeader_UpdatedBy" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="NPRNO" xml:space="preserve">
    <value>NPR No</value>
  </data>
  <data name="NPRdate" xml:space="preserve">
    <value>NPR Date</value>
  </data>
  <data name="DebitNoteDate" xml:space="preserve">
    <value>Debit Note Date</value>
  </data>
  <data name="NPRCompleteddATE" xml:space="preserve">
    <value>Complete Date</value>
  </data>
  <data name="IncurredCostTo" xml:space="preserve">
    <value>Incurred cost to sales. Yes/No</value>
  </data>
  <data name="NEWNPR" xml:space="preserve">
    <value>New NPR</value>
  </data>
  <data name="SUPPNO" xml:space="preserve">
    <value>Supp No.</value>
  </data>
  <data name="TraceabilityNeeded" xml:space="preserve">
    <value>** Please verify that the source of supply meets the customer’s requirements **</value>
  </data>
  <data name="ShipToVatNo" xml:space="preserve">
    <value>Ship to VAT No</value>
  </data>
  <data name="QtyAdvised" xml:space="preserve">
    <value>Qty Advised</value>
  </data>
  <data name="NPRComments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="NPRReason1" xml:space="preserve">
    <value>Reason1</value>
  </data>
  <data name="NPRReason2" xml:space="preserve">
    <value>Reason2</value>
  </data>
  <data name="AuthoriseDate" xml:space="preserve">
    <value>Authorise Date</value>
  </data>
  <data name="Emailedto" xml:space="preserve">
    <value>Emailed to:</value>
  </data>
  <data name="LineHeader_LineNo" xml:space="preserve">
    <value>Line No</value>
  </data>
  <data name="CommercialInvoice" xml:space="preserve">
    <value>Commercial Invoice</value>
  </data>
  <data name="CommercialInvoiceDate" xml:space="preserve">
    <value>Commercial Invoice Date</value>
  </data>
  <data name="InvoiceIncludeCocf" xml:space="preserve">
    <value>Invoice Include Cofc</value>
  </data>
  <data name="DimensionalWeightNew" xml:space="preserve">
    <value>Dimensional&amp;nbsp;Wt</value>
  </data>
  <data name="SalesOrderNew" xml:space="preserve">
    <value>SOA</value>
  </data>
  <data name="TotalWeightNew" xml:space="preserve">
    <value>Total Wt</value>
  </data>
  <data name="DeliveryDateEPR" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="InAdvance" xml:space="preserve">
    <value>In Advance</value>
  </data>
  <data name="OtherSpecify" xml:space="preserve">
    <value>Other (Specify)</value>
  </data>
  <data name="SupplierNew" xml:space="preserve">
    <value>Supplier New</value>
  </data>
  <data name="UponReceipt" xml:space="preserve">
    <value>Upon Receipt</value>
  </data>
  <data name="ValueCurrency" xml:space="preserve">
    <value>Value &amp; Currency</value>
  </data>
  <data name="CeditCard" xml:space="preserve">
    <value>Cedit Card</value>
  </data>
  <data name="Cheque" xml:space="preserve">
    <value>Cheque</value>
  </data>
  <data name="Manager_AdvancePay" xml:space="preserve">
    <value> Advance Payment on Open Orders?</value>
  </data>
  <data name="Manager_Authorized" xml:space="preserve">
    <value>Authorized</value>
  </data>
  <data name="Manager_Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="Manager_Countersign" xml:space="preserve">
    <value>Countersigned (where applicable)</value>
  </data>
  <data name="Manager_Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Manager_EARIMember" xml:space="preserve">
    <value>EARI Member ?</value>
  </data>
  <data name="Manager_EARIReported" xml:space="preserve">
    <value>EARI Reported ?</value>
  </data>
  <data name="Manager_FORStock" xml:space="preserve">
    <value> FOR Stock ?</value>
  </data>
  <data name="Manager_OutStanding" xml:space="preserve">
    <value>Out-standing Debit notes on P/L?</value>
  </data>
  <data name="Manager_PayAuthBy" xml:space="preserve">
    <value>Payment Authorized By</value>
  </data>
  <data name="Manager_SalesLedger" xml:space="preserve">
    <value>Sales Ledger Terms</value>
  </data>
  <data name="Manager_SalesOverdue" xml:space="preserve">
    <value>Sales Ledger Overdue?</value>
  </data>
  <data name="Manager_SORSigned" xml:space="preserve">
    <value>SOR Signed ?</value>
  </data>
  <data name="Manager_TotalValue" xml:space="preserve">
    <value>Total Value</value>
  </data>
  <data name="Manager_TotalValue1" xml:space="preserve">
    <value>Total Value</value>
  </data>
  <data name="Manager_ValuesCorrect" xml:space="preserve">
    <value> Values Correct?</value>
  </data>
  <data name="PaymentAuthDate" xml:space="preserve">
    <value> Date</value>
  </data>
  <data name="PaymentComment" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="RefAddress" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="RefAddress1" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="RefAddress2" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="RefComment" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="RefEmail" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="RefEmail1" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="RefEmail2" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="RefFax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="RefFax1" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="RefFax2" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="RefName" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="RefName1" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="RefName2" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="RefTEL" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="RefTel1" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="RefTel2" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Sales_Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Sales_FormaAttached" xml:space="preserve">
    <value>Pro Forma Attached ?</value>
  </data>
  <data name="Sales_RaisedBy" xml:space="preserve">
    <value>Raised by</value>
  </data>
  <data name="Sales_TotalValue" xml:space="preserve">
    <value>Total Value</value>
  </data>
  <data name="TT" xml:space="preserve">
    <value>TT</value>
  </data>
  <data name="EPRNO" xml:space="preserve">
    <value>EPR NO</value>
  </data>
  <data name="NetSpecify" xml:space="preserve">
    <value>NetSpecify</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="Del" xml:space="preserve">
    <value>Del</value>
  </data>
  <data name="ENQUIRYPROGRESS" xml:space="preserve">
    <value>ENQUIRY / PROGRESS</value>
  </data>
  <data name="Item" xml:space="preserve">
    <value>Item</value>
  </data>
  <data name="MPDC" xml:space="preserve">
    <value>(Manufacturer/Package/date Code)</value>
  </data>
  <data name="OrderNo" xml:space="preserve">
    <value>Order No.</value>
  </data>
  <data name="Qty" xml:space="preserve">
    <value>Qty</value>
  </data>
  <data name="Quoted" xml:space="preserve">
    <value>Quoted</value>
  </data>
  <data name="SalesRep" xml:space="preserve">
    <value>Sales Rep</value>
  </data>
  <data name="SONo" xml:space="preserve">
    <value>S.O.No.</value>
  </data>
  <data name="SpecialComments" xml:space="preserve">
    <value>Special Comments</value>
  </data>
  <data name="TakenBy" xml:space="preserve">
    <value>Taken By</value>
  </data>
  <data name="Target" xml:space="preserve">
    <value>Target</value>
  </data>
  <data name="Telephone" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="CustReqPrint" xml:space="preserve">
    <value>Requirements Enquiry Print</value>
  </data>
  <data name="DimensionalWeightBulk" xml:space="preserve">
    <value>Dimensional Wt</value>
  </data>
  <data name="LineHeader_BatchReference" xml:space="preserve">
    <value>Batch Ref</value>
  </data>
  <data name="ClientInvoice" xml:space="preserve">
    <value>Client Invoice</value>
  </data>
  <data name="LineHeader_Buyer" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="LineHeader_CompanyType" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="LineHeader_Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="LineHeader_GPValue" xml:space="preserve">
    <value>GP Value</value>
  </data>
  <data name="LineHeader_PromiseDate" xml:space="preserve">
    <value>Promise Date</value>
  </data>
  <data name="LineHeader_SupplierName" xml:space="preserve">
    <value>Supplier Name</value>
  </data>
  <data name="LineHeader_UpliftAmount" xml:space="preserve">
    <value>Uplift Amount</value>
  </data>
  <data name="LineHeader_UpliftPrice" xml:space="preserve">
    <value>Uplift Price</value>
  </data>
  <data name="PurchaseOrderReport" xml:space="preserve">
    <value>Purchase Order Report</value>
  </data>
  <data name="DivisionOfContact" xml:space="preserve">
    <value>Division of contact from client</value>
  </data>
  <data name="LineHeader_ResellValue" xml:space="preserve">
    <value>Resell Price</value>
  </data>
  <data name="LineHeader_GP" xml:space="preserve">
    <value>GP</value>
  </data>
  <data name="LineHeader_GPPercent" xml:space="preserve">
    <value>GP %</value>
  </data>
  <data name="LineHeader_TotalCostPrice" xml:space="preserve">
    <value>Total Cost </value>
  </data>
  <data name="LineHeader_TotalSellPrice" xml:space="preserve">
    <value>Total Sell </value>
  </data>
  <data name="SubTotalCostPrice" xml:space="preserve">
    <value>Total Cost</value>
  </data>
  <data name="SubTotalGP" xml:space="preserve">
    <value>Total GP</value>
  </data>
  <data name="SubTotalGPPercent" xml:space="preserve">
    <value>Total GP %</value>
  </data>
  <data name="SubTotalSellPrice" xml:space="preserve">
    <value>Total Sell</value>
  </data>
  <data name="ClientRefNo" xml:space="preserve">
    <value>Ref No</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="ProductDutyCode" xml:space="preserve">
    <value>Duty Code Pack</value>
  </data>
  <data name="ExpeditNoteSubject" xml:space="preserve">
    <value>New Expedite Note Created</value>
  </data>
  <data name="CompanyOnStop" xml:space="preserve">
    <value>** Sales order has been processed on an account currently on stop **</value>
  </data>
  <data name="LineHeader_IPO" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="LineHeader_IPONo" xml:space="preserve">
    <value>IPO No.</value>
  </data>
  <data name="InternalPurchaseOrder" xml:space="preserve">
    <value>Internal Purchase Order</value>
  </data>
  <data name="HUBRFQ" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="CommunicationNoteSubject" xml:space="preserve">
    <value>New Communication Note Created</value>
  </data>
  <data name="EstimatedFreight" xml:space="preserve">
    <value>Estimated Freight Only</value>
  </data>
  <data name="LineHeader_DutyCode" xml:space="preserve">
    <value>Duty Code</value>
  </data>
  <data name="DutyCode" xml:space="preserve">
    <value>Duty Code</value>
  </data>
  <data name="BuyerName" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="ShipToAdd" xml:space="preserve">
    <value>Ship To Address</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="Reply" xml:space="preserve">
    <value>Reply</value>
  </data>
  <data name="DOCNO" xml:space="preserve">
    <value>Document log</value>
  </data>
  <data name="CustomerRejectionNo" xml:space="preserve">
    <value>Customer Rejection No</value>
  </data>
  <data name="ErrorOnEmail" xml:space="preserve">
    <value>Error on email</value>
  </data>
  <data name="BilledTo" xml:space="preserve">
    <value>Bill To</value>
  </data>
  <data name="ClientVATNo" xml:space="preserve">
    <value>VAT</value>
  </data>
  <data name="SOPromiseDate" xml:space="preserve">
    <value>SO-SO Promise Date</value>
  </data>
  <data name="ContractNo" xml:space="preserve">
    <value>Contract No.</value>
  </data>
  <data name="POLineSerialNo" xml:space="preserve">
    <value>PO Line Serial No</value>
  </data>
  <data name="SOLineNo" xml:space="preserve">
    <value>Line No</value>
  </data>
  <data name="LineHeader_ProfitPer" xml:space="preserve">
    <value>Profit (%)</value>
  </data>
  <data name="DivisionChanged" xml:space="preserve">
    <value>Division Changed</value>
  </data>
  <data name="EORINumbe" xml:space="preserve">
    <value>EORI No</value>
  </data>
  <data name="EORINumber" xml:space="preserve">
    <value>EORI No</value>
  </data>
  <data name="LineHeader_TotalPriceToClient" xml:space="preserve">
    <value>Price To Client</value>
  </data>
  <data name="LineHeader_TotalProfit" xml:space="preserve">
    <value>Profit ( % )</value>
  </data>
  <data name="LineHeader_ClientResellValue" xml:space="preserve">
    <value>Client Resale Price</value>
  </data>
  <data name="PrintCount" xml:space="preserve">
    <value>Print : </value>
  </data>
  <data name="LineHeader_CustomerPOSoOrder" xml:space="preserve">
    <value>Customer PO   ( Sales Order )</value>
  </data>
  <data name="GIReport" xml:space="preserve">
    <value>GoodsIn Report for</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="GoodsInNo" xml:space="preserve">
    <value>GoodsIn No.</value>
  </data>
  <data name="ReceivedDate" xml:space="preserve">
    <value>Received Date</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="WarehouseNotes" xml:space="preserve">
    <value>Notes To Warehouse</value>
  </data>
  <data name="CountryOfManufactureName" xml:space="preserve">
    <value>Country Of Manufacture Name</value>
  </data>
  <data name="GeneralInspectionNotes" xml:space="preserve">
    <value>General Inspection Notes</value>
  </data>
  <data name="HICStatus" xml:space="preserve">
    <value>HICStatus</value>
  </data>
  <data name="MSL" xml:space="preserve">
    <value>MSL</value>
  </data>
  <data name="QCNotes" xml:space="preserve">
    <value>Instruction to WH &amp; Quality control notes</value>
  </data>
  <data name="GIQueryScreenReport" xml:space="preserve">
    <value>GI Query Report</value>
  </data>
  <data name="CorrectDateCode" xml:space="preserve">
    <value>Correct Date Code</value>
  </data>
  <data name="CorrectHICStatus" xml:space="preserve">
    <value>Correct HIC Status</value>
  </data>
  <data name="CorrectManufacturerName" xml:space="preserve">
    <value>Correct Mfr Name</value>
  </data>
  <data name="CorrectMSLLevel" xml:space="preserve">
    <value>Correct MSL</value>
  </data>
  <data name="CorrectPackageType" xml:space="preserve">
    <value>Correct Package Type</value>
  </data>
  <data name="CorrectPartNo" xml:space="preserve">
    <value>Correct Part No</value>
  </data>
  <data name="GIQuery" xml:space="preserve">
    <value>GI Query</value>
  </data>
  <data name="BillingAddress" xml:space="preserve">
    <value>Billing Address</value>
  </data>
  <data name="QtyAllocated" xml:space="preserve">
    <value>Qty Allocated</value>
  </data>
  <data name="QtyOutstanding" xml:space="preserve">
    <value>Qty Outstanding</value>
  </data>
  <data name="SalesOrder_print" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="LineHeader_ECCNCode" xml:space="preserve">
    <value>ECCN Code</value>
  </data>
  <data name="InvoiceCurrency" xml:space="preserve">
    <value>Invoice Currency</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="Stockprint" xml:space="preserve">
    <value>Allocated Stock Routing Check Card</value>
  </data>
  <data name="Asap" xml:space="preserve">
    <value>Asap</value>
  </data>
  <data name="Lines and quantites for SalesOrder" xml:space="preserve">
    <value>Lines and quantites for SalesOrder</value>
  </data>
  <data name="NAME" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Packaging" xml:space="preserve">
    <value>Packaging</value>
  </data>
  <data name="Picked_by:" xml:space="preserve">
    <value>Picked By:</value>
  </data>
  <data name="ROHScomplience" xml:space="preserve">
    <value>ROHS Compliance</value>
  </data>
  <data name="Saleorderno" xml:space="preserve">
    <value>SO No</value>
  </data>
  <data name="SalesorderRequirements" xml:space="preserve">
    <value>Sales Order Requirements</value>
  </data>
  <data name="Shipment date" xml:space="preserve">
    <value>Shipment date</value>
  </data>
  <data name="ShippingMethod" xml:space="preserve">
    <value>ShippingMethod</value>
  </data>
  <data name="StockinQuantity" xml:space="preserve">
    <value>StockinQuantity</value>
  </data>
  <data name="stocklotcode" xml:space="preserve">
    <value>Stock lot code</value>
  </data>
  <data name="[#QUANTITYINSTOCK#]" xml:space="preserve">
    <value>[#QUANTITYINSTOCK#]</value>
  </data>
  <data name="Inspected_by:" xml:space="preserve">
    <value>Inspected By:</value>
  </data>
  <data name="GSTNO" xml:space="preserve">
    <value>GST NO</value>
  </data>
  <data name="OGELStatusWarning" xml:space="preserve">
    <value>THIS ORDER CONTAINS AN OGEL LINE</value>
  </data>
  <data name="LineHeader_CountryOrigin" xml:space="preserve">
    <value>Country of Origins</value>
  </data>
  <data name="LineHeader_OGELNumber" xml:space="preserve">
    <value>OGEL Number</value>
  </data>
  <data name="Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="PDF" xml:space="preserve">
    <value>PDF</value>
  </data>
  <data name="XML" xml:space="preserve">
    <value>XML</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="PackingSlipIncludeInvoiceAndCofc" xml:space="preserve">
    <value>Packing Slip include invoice and CofC</value>
  </data>
</root>