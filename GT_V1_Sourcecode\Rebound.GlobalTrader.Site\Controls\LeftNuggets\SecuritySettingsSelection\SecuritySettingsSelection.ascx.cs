using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class SecuritySettingsSelection : Selection {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("SecuritySettingsSelection");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			AddItems();
		}

		private void AddItems() {
			HtmlControl ul = new HtmlGenericControl("ul");
			ul.Controls.Add(AddHeading(Functions.GetGlobalResource("Misc", "Setup_Security")));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_SecuritySettings_Groups)) ul.Controls.Add(AddItem("Setup_Security_Groups"));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_SecuritySettings_Users)) ul.Controls.Add(AddItem("Setup_Security_Users"));
			_plhItems.Controls.Add(ul);
		}

	}
}