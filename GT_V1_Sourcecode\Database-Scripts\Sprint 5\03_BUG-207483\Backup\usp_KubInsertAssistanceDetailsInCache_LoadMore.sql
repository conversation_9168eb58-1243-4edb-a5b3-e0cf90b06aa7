
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubInsertAssistanceDetailsInCache_LoadMore]              
@PartNumber  NVARCHAR(100)=NULL,              
@ClientID  INT=0              
AS              
BEGIN              
SET NOCOUNT ON              
DECLARE @TotalGPbasedLastActualBuyPrice nvarchar(MAX)=null                        
DECLARE @LowestSalesPriceLast12Months nvarchar(MAX)=null                        
DECLARE @NumberOfPartsSoldLast12months nvarchar(MAX)=null                        
DECLARE @LastEnquiredDateOfPart nvarchar(MAX)=null                          
DECLARE @LastQuotedPrice nvarchar(MAX)=null                          
DECLARE @LastSoldPrice nvarchar(MAX)=null                          
DECLARE @LastHubprice nvarchar(MAX)=null              
Declare @DefaultCurrency int = 0;            
Declare @DefaultCurrencyCode nvarchar(max);            
              
select @DefaultCurrency= CurrencyNo, @DefaultCurrencyCode= cr.CurrencyCode from tbClient c             
left join tbCurrency cr on c.CurrencyNo = cr.CurrencyId            
where ClientId = @ClientID;            
            
------Total GP based on last actual buy price with date----                            
     CREATE TABLE #Invoices (                    
          InvoiceID INT    
  , InvoiceNumber INT                   
        , CurrencyRate DECIMAL(16,5)                     
        , ShippingCost DECIMAL(16,5)                     
        , Freight DECIMAL(16,5)    
  , InvoiceDate DATETIME                   
        )                    
    CREATE TABLE #InvoicePreSummary (                    
          InvoiceID INT  
  , InvoiceNumber INT                    
        , ShippingCost DECIMAL(16,5)                    
        , Freight DECIMAL(16,5)                     
        , LandedCost DECIMAL(16,5)   
  , Quantity INT                    
        , InvoiceValue DECIMAL(16,5)                  
        , ClientNo INT  
  , InvoiceDate DATETIME                    
        )                    
                    
    INSERT  INTO #Invoices                    
            SELECT  InvoiceID   
         , InvoiceNumber                   
                  , dbo.ufn_get_exchange_rate(a.CurrencyNo, a.InvoiceDate)                    
                  , isnull(a.ShippingCost,0)                    
                  , isnull(a.Freight,0)  
      , a.InvoiceDate                    
            FROM    dbo.tbInvoice a                    
            WHERE   a.ClientNo = @ClientID                
            AND CAST(a.InvoiceDate AS DATE)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)        
    INSERT  INTO #InvoicePreSummary                    
            SELECT                 
                   i.InvoiceID   
      , i.InvoiceNumber                    
                  , CONVERT(DECIMAL(16,5),i.ShippingCost)                    
                  , i.Freight / i.CurrencyRate                    
                  , isnull(sum(CONVERT(DECIMAL(16,5),ila.LandedCost)), 0)  
      , isnull(ila.Quantity,0)                    
                  , isnull(sum((ila.Price * ila.Quantity) / i.CurrencyRate), 0)                
                  , ila.ClientNo  
      , i.InvoiceDate                    
            FROM    #Invoices i                    
            LEFT JOIN dbo.tbInvoiceLine il ON il.InvoiceNo = i.InvoiceId                    
            LEFT JOIN dbo.vwInvoiceLineAllocation ila ON ila.InvoiceLineNo = il.InvoiceLineId                    
            Where ila.ClientNo=@ClientID AND il.FullPart=@PartNumber                
            GROUP BY                 
                    i.InvoiceID                    
                  , i.ShippingCost                    
                  , i.Freight / i.CurrencyRate                   
      , ila.ClientNo,i.InvoiceNumber,ila.Quantity,i.InvoiceDate  
                                                 
SELECT   
TOP 1 
@TotalGPbasedLastActualBuyPrice=
CAST(CONVERT(DECIMAL(16,2),((InvoiceValue+Freight)-((LandedCost*Quantity)+ShippingCost))) AS NVARCHAR(25))+' '+cr.CurrencyCode
+'  ('+CAST(FORMAT(InvoiceDate,'dd-MM-yyyy') AS NVARCHAR(40))+')'   

  
 FROM #InvoicePreSummary i   
LEFT JOIN tbClient cl ON i.ClientNo=cl.ClientId                
LEFT JOIN tbCurrency cr ON cl.CurrencyNo=cr.CurrencyId     
ORDER BY InvoiceDate DESC             
DROP TABLE #Invoices                    
DROP TABLE #InvoicePreSummary    
        
                     
-------------End---------------------------------                                             
                       
                        
---Lowest sales price in the last 12 months.(Rolling)---                            
SELECT TOP 1 @LowestSalesPriceLast12Months=                          
CASE WHEN ISNULL(sol.Price,0)>0 THEN                        
CAST(ISNULL(                       
CONVERT(DECIMAL(16,5),                        
dbo.ufn_convert_currency_value(MIN(sol.Price),so.CurrencyNo,@DefaultCurrency,GETDATE())),0)                        
AS NVARCHAR(100))+' '+@DefaultCurrencyCode+      
'<span class="actualCurrency">['+CAST(CONVERT(DECIMAL(16,5),sol.Price) AS NVARCHAR(100))+      
' '+cr.CurrencyCode+']</span>'+'<a class="documentachor" href="Ord_SODetail.aspx?so='+        
CAST(so.SalesOrderId AS NVARCHAR(20))+        
'" target="_blank">('+CAST(so.SalesOrderNumber AS NVARCHAR(25)) +')</a>'               
ELSE '0' END                        
FROM tbSalesOrderLine sol                            
LEFT OUTER JOIN tbSalesOrder so ON sol.SalesOrderNo=so.SalesOrderId                                          
LEFT OUTER JOIN tbCurrency cr ON so.CurrencyNo=cr.CurrencyId                          
WHERE so.ClientNo=@ClientID                          
AND   sol.FullPart=@PartNumber                         
AND  (sol.DatePromised)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)                         
AND sol.Price>0                             
GROUP BY so.CurrencyNo            
,cr.CurrencyCode            
,cr.CurrencyId,sol.Price,      
so.SalesOrderId,so.SalesOrderNumber                              
-----------------END-------------------------------                  
                        
                           
                        
-----Number of parts sold for the last 12 months-------                            
SELECT @NumberOfPartsSoldLast12months= ISNULL(SUM(ila.Quantity),0) FROM tbInvoiceLine inl                            
LEFT OUTER JOIN tbInvoice iv ON inl.InvoiceNo=iv.InvoiceId     
LEFT OUTER JOIN dbo.tbInvoiceLineAllocation ila  ON inl.InvoiceLineId=ila.InvoiceLineNo                           
WHERE iv.ClientNo=@ClientID  AND inl.FullPart=@PartNumber                             
AND (inl.ShippedDate)>CAST(DATEADD(MONTH,-12,GETDATE()) AS DATE)                           
-----------------END-------------------------------                            
                      
                        
---Last Enquired Date for selected Part---                            
SELECT  top 1 @LastEnquiredDateOfPart=                            
 FORMAT(cust.ReceivedDate,'dd-MM-yyyy' ) +'<a class="documentachor" href="Ord_CusReqDetail.aspx?req='+        
CAST(cust.CustomerRequirementId AS NVARCHAR(20))+        
'" target="_blank">('+CAST(cust.CustomerRequirementNumber AS NVARCHAR(25)) +')</a>'                          
FROM tbCustomerRequirement cust                         
WHERE ClientNo=@ClientID                          
AND FullPart=@PartNumber                        
ORDER BY DatePromised DESC                        
-----------------END-------------------------------                            
                        
---Latest Quoted Price---                            
SELECT TOP 1 @LastQuotedPrice=                             
CASE WHEN ISNULL(qol.Price,0)>0 THEN                        
CAST(ISNULL(                        
CONVERT(DECIMAL(16,5),dbo.ufn_convert_currency_value(qol.Price,qo.CurrencyNo,@DefaultCurrency,GETDATE())),0) AS NVARCHAR(100))+                        
' '+@DefaultCurrencyCode+' '+ '<span class="actualCurrency">['+      
CAST(CONVERT(DECIMAL(16,5),qol.Price) AS NVARCHAR(100))+' '+cr.CurrencyCode+      
']</span>'+                       
'   ('+CAST(FORMAT(qo.DateQuoted,'dd-MM-yyyy') AS NVARCHAR(40))+')<a class="documentachor" href="Ord_QuoteDetail.aspx?qt='+        
CAST(qo.QuoteId AS NVARCHAR(20))+        
'" target="_blank">('+CAST(qo.QuoteNumber AS NVARCHAR(25)) +')</a>'                            
ELSE '0' END                        
FROM tbQuoteLine qol                             
LEFT OUTER JOIN tbQuote qo ON qol.QuoteNo=qo.QuoteId            
LEFT OUTER JOIN tbCurrency cr ON qo.CurrencyNo=cr.CurrencyId                          
WHERE qo.ClientNo=@ClientID                        
AND qol.FullPart=@PartNumber                           
ORDER BY qo.DateQuoted DESC                           
                        
---------END-----------                            
                            
---Latest Invoiced Price---                            
SELECT TOP 1  @LastSoldPrice=                          
CASE WHEN ISNULL(inl.Price,0)>0 THEN                        
CAST(ISNULL(                       
CONVERT(DECIMAL(16,5),dbo.ufn_convert_currency_value(inl.Price,iv.CurrencyNo,@DefaultCurrency,GETDATE())),0) AS NVARCHAR(100))+                        
' '+@DefaultCurrencyCode+' '+'<span class="actualCurrency">['+      
CAST(CONVERT(DECIMAL(16,5),inl.Price) AS NVARCHAR(100))+' '+      
cr.CurrencyCode      
+']</span>'+                        
'   ('+CAST(FORMAT(iv.InvoiceDate,'dd-MM-yyyy') AS NVARCHAR(40))+')<a class="documentachor" href="Ord_InvoiceDetail.aspx?inv='+        
CAST(iv.InvoiceId AS NVARCHAR(20))+        
'" target="_blank">('+CAST(iv.InvoiceNumber AS NVARCHAR(25)) +')</a>'                         
ELSE '0' END                        
FROM tbinvoiceLine inl                             
LEFT OUTER JOIN tbInvoice iv ON inl.invoiceNo=iv.InvoiceId                            
LEFT OUTER JOIN tbCurrency cr ON iv.CurrencyNo=cr.CurrencyId                          
WHERE iv.ClientNo=@ClientID                         
AND inl.FullPart=@PartNumber                            
ORDER BY iv.InvoiceDate DESC                            
--------END----------                            
                            
---Latest Quoted Price shared by HUB---                            
SELECT TOP 1 @LastHubprice=                          
CASE WHEN ISNULL(sr.Price,0)>0 THEN                        
CAST(ISNULL(                        
CONVERT(DECIMAL(16,5),dbo.ufn_convert_currency_value(sr.Price,sr.ClientCurrencyNo,@DefaultCurrency,GETDATE())),0) AS NVARCHAR(100))+                          
' '+@DefaultCurrencyCode+' '+ '<span class="actualCurrency">['+      
 CAST(CONVERT(DECIMAL(16,5),sr.Price) AS NVARCHAR(100))+' '+cr.CurrencyCode+']</span>'+                       
'   ('+CAST(FORMAT(sr.DLUP,'dd-MM-yyyy') AS NVARCHAR(40))+')<a class="documentachor" href="Ord_CusReqDetail.aspx?req='+        
CAST(cust.CustomerRequirementId AS NVARCHAR(20))+        
'" target="_blank">('+CAST(cust.CustomerRequirementNumber AS NVARCHAR(25)) +')</a>'                        
ELSE '0' END FROM tbSourcingresult sr          
LEFT OUTER JOIN tbCustomerRequirement cust ON sr.CustomerRequirementNo=cust.CustomerRequirementId                      
LEFT OUTER JOIN tbCurrency cr ON cr.CurrencyId = sr.CurrencyNo                        
WHERE sr.FullPart=@PartNumber AND sr.SourcingTable IN ('EPPH','EXPH','OFPH','RLPH')                            
ORDER BY sr.DLUP  DESC                        
-------END---------                
              
----Update DATA---              
UPDATE tbKubAssistanceDetailsCache SET              
TotalGPbasedLastActualBuyPrice=@TotalGPbasedLastActualBuyPrice,              
LowestSalesPriceLast12Months=@LowestSalesPriceLast12Months,              
NumberOfPartsSoldLast12months=@NumberOfPartsSoldLast12months,              
LastEnquiredDateOfPart=@LastEnquiredDateOfPart,              
LastQuotedPrice=@LastQuotedPrice,              
LastSoldPrice=@LastSoldPrice,              
LastHubprice=@LastHubprice              
WHERE ClientID=@ClientID AND              
PartNo=@PartNumber                
              
SET NOCOUNT OFF              
END 
GO


