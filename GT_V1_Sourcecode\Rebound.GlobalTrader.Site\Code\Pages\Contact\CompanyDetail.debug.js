///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 14.01.2010:
// - always show ManufacturersSupplied nugget
//
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for company section
[002]      Aashu            07/06/2018  removed _lblNonPreferredCompany field
[003]      A<PERSON><PERSON>    13-Sep-2018    [REB-12820]:Provision to add Global Security on Contact Section
[004]      Ravi            31-05-2023    RP-1269
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Contact");

Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail = function (el) {
    Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.initializeBase(this, [el]);
    this._aryGotTabData = [];
};

Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.prototype = {

    get_ctlPageTitle: function () { return this._ctlPageTitle; }, set_ctlPageTitle: function (v) { if (this._ctlPageTitle !== v) this._ctlPageTitle = v; },
    get_intCurrentTab: function () { return this._intCurrentTab; }, set_intCurrentTab: function (v) { if (this._intCurrentTab !== v) this._intCurrentTab = v; },
    get_pnlMain: function () { return this._pnlMain; }, set_pnlMain: function (v) { if (this._pnlMain !== v) this._pnlMain = v; },
    get_pnlDetail: function () { return this._pnlDetail; }, set_pnlDetail: function (v) { if (this._pnlDetail !== v) this._pnlDetail = v; },
    get_ctlMainCompanyInfo: function () { return this._ctlMainCompanyInfo; }, set_ctlMainCompanyInfo: function (v) { if (this._ctlMainCompanyInfo !== v) this._ctlMainCompanyInfo = v; },
    get_ctlContactLog: function () { return this._ctlContactLog; }, set_ctlContactLog: function (v) { if (this._ctlContactLog !== v) this._ctlContactLog = v; },
    get_ctlTransactions: function () { return this._ctlTransactions; }, set_ctlTransactions: function (v) { if (this._ctlTransactions !== v) this._ctlTransactions = v; },
    get_ctlAddresses: function () { return this._ctlAddresses; }, set_ctlAddresses: function (v) { if (this._ctlAddresses !== v) this._ctlAddresses = v; },
    get_ctlPurchasingInfo: function () { return this._ctlPurchasingInfo; }, set_ctlPurchasingInfo: function (v) { if (this._ctlPurchasingInfo !== v) this._ctlPurchasingInfo = v; },
    get_ctlSalesInfo: function () { return this._ctlSalesInfo; }, set_ctlSalesInfo: function (v) { if (this._ctlSalesInfo !== v) this._ctlSalesInfo = v; },
    get_ctlManufacturersSupplied: function () { return this._ctlManufacturersSupplied; }, set_ctlManufacturersSupplied: function (v) { if (this._ctlManufacturersSupplied !== v) this._ctlManufacturersSupplied = v; },
    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_pnlAddress: function () { return this._pnlAddress; }, set_pnlAddress: function (v) { if (this._pnlAddress !== v) this._pnlAddress = v; },
    get_lblAddress: function () { return this._lblAddress; }, set_lblAddress: function (v) { if (this._lblAddress !== v) this._lblAddress = v; },
    get_pnlParentCompany: function () { return this._pnlParentCompany; }, set_pnlParentCompany: function (v) { if (this._pnlParentCompany !== v) this._pnlParentCompany = v; },
    get_lblCompanyType: function () { return this._lblCompanyType; }, set_lblCompanyType: function (v) { if (this._lblCompanyType !== v) this._lblCompanyType = v; },
    get_lblSupplierRating: function () { return this._lblSupplierRating; }, set_lblSupplierRating: function (v) { if (this._lblSupplierRating !== v) this._lblSupplierRating = v; },
    get_ctlRatingCustomer: function () { return this._ctlRatingCustomer; }, set_ctlRatingCustomer: function (v) { if (this._ctlRatingCustomer !== v) this._ctlRatingCustomer = v; },
    get_lblCustomerRating: function () { return this._lblCustomerRating; }, set_lblCustomerRating: function (v) { if (this._lblCustomerRating !== v) this._lblCustomerRating = v; },
    get_ctlRatingSupplier: function () { return this._ctlRatingSupplier; }, set_ctlRatingSupplier: function (v) { if (this._ctlRatingSupplier !== v) this._ctlRatingSupplier = v; },
    get_pnlImportantNotes: function () { return this._pnlImportantNotes; }, set_pnlImportantNotes: function (v) { if (this._pnlImportantNotes !== v) this._pnlImportantNotes = v; },
    get_lblImportantNotes: function () { return this._lblImportantNotes; }, set_lblImportantNotes: function (v) { if (this._lblImportantNotes !== v) this._lblImportantNotes = v; },
    get_pnlOnStop: function () { return this._pnlOnStop; }, set_pnlOnStop: function (v) { if (this._pnlOnStop !== v) this._pnlOnStop = v; },
    get_lblCompanyIsSanctioned: function () { return this._lblCompanyIsSanctioned; }, set_lblCompanyIsSanctioned: function (v) { if (this._lblCompanyIsSanctioned !== v) this._lblCompanyIsSanctioned = v; },
    //[004] end
    get_lblCompanyOnStop: function () { return this._lblCompanyOnStop; }, set_lblCompanyOnStop: function (v) { if (this._lblCompanyOnStop !== v) this._lblCompanyOnStop = v; },
    get_btnTransactions: function () { return this._btnTransactions; }, set_btnTransactions: function (v) { if (this._btnTransactions !== v) this._btnTransactions = v; },
    //[001] code start
    get_ctlCMPDocuments: function () { return this._ctlCMPDocuments; }, set_ctlCMPDocuments: function (v) { if (this._ctlCMPDocuments !== v) this._ctlCMPDocuments = v; },
    //[001] code end
    get_ctlCMPDocumentsNew: function () { return this._ctlCMPDocumentsNew; }, set_ctlCMPDocumentsNew: function (v) { if (this._ctlCMPDocumentsNew !== v) this._ctlCMPDocumentsNew = v; },
    //Certificate
    get_pnlCertificate: function () { return this._pnlCertificate; }, set_pnlCertificate: function (v) { if (this._pnlCertificate !== v) this._pnlCertificate = v; },
    get_ctlCompanyCertificate: function () { return this._ctlCompanyCertificate; }, set_ctlCompanyCertificate: function (v) { if (this._ctlCompanyCertificate !== v) this._ctlCompanyCertificate = v; },

    get_lblInsurance: function () { return this._lblInsurance; }, set_lblInsurance: function (v) { if (this._lblInsurance !== v) this._lblInsurance = v; },
    get_pnlInsurance: function () { return this._pnlInsurance; }, set_pnlInsurance: function (v) { if (this._pnlInsurance !== v) this._pnlInsurance = v; },
    get_lblInsuranceFileNo: function () { return this._lblInsuranceFileNo; }, set_lblInsuranceFileNo: function (v) { if (this._lblInsuranceFileNo !== v) this._lblInsuranceFileNo = v; },
    get_strStopStatus: function () { return this._strStopStatus; }, set_strStopStatus: function (v) { if (this._strStopStatus !== v) this._strStopStatus = v; },
    get_blnEditHubSupplier: function () { return this._blnEditHubSupplier; }, set_blnEditHubSupplier: function (v) { if (this._blnEditHubSupplier !== v) this._blnEditHubSupplier = v; },
    //[003] start
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    get_pnlAPICustomer: function () { return this._pnlAPICustomer; }, set_pnlAPICustomer: function (v) { if (this._pnlAPICustomer !== v) this._pnlAPICustomer = v; },
    get_ctlCompanyAPICustomer: function () { return this._ctlCompanyAPICustomer; }, set_ctlCompanyAPICustomer: function (v) { if (this._ctlCompanyAPICustomer !== v) this._ctlCompanyAPICustomer = v; },
    //[003] end
    get_pnlOnPremierCustomer: function () { return this._pnlOnPremierCustomer; }, set_pnlOnPremierCustomer: function (v) { if (this._pnlOnPremierCustomer !== v) this._pnlOnPremierCustomer = v; },
    get_pnlOnTier2PremierCustomer: function () { return this._pnlOnTier2PremierCustomer; }, set_pnlOnTier2PremierCustomer: function (v) { if (this._pnlOnTier2PremierCustomer !== v) this._pnlOnTier2PremierCustomer = v; },
    /*get_ctlCompanyInsuranceCertificate: function () { return this._ctlCompanyInsuranceCertificate; }, set_ctlCompanyInsuranceCertificate: function (v) { if (this._ctlCompanyInsuranceCertificate !== v) this._ctlCompanyInsuranceCertificate = v; },*/
    get_ctlCRMProspects: function () { return this._ctlCRMProspects; }, set_ctlCRMProspects: function (v) { if (this._ctlCRMProspects !== v) this._ctlCRMProspects = v; },
    get_ctlGSA: function () { return this._ctlGSA; }, set_ctlGSA: function (v) { if (this._ctlGSA !== v) this._ctlGSA = v; },
    get_ctlFinanceInfo: function () { return this._ctlFinanceInfo; }, set_ctlFinanceInfo: function (v) { if (this._ctlFinanceInfo !== v) this._ctlFinanceInfo = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.callBaseMethod(this, "initialize");
    },

    goInit: function () {
        if (this._ctlMainCompanyInfo) this._ctlMainCompanyInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainCompanyInfo_GetDataComplete));
        if (this._ctlAddresses) this._ctlAddresses.addGetDataComplete(Function.createDelegate(this, this.ctlAddresses_GetDataComplete));
        if (this._ctlPurchasingInfo) this._ctlPurchasingInfo.addGetDataComplete(Function.createDelegate(this, this.ctlPurchasingInfo_GetDataComplete));
        if (this._ctlSalesInfo) this._ctlSalesInfo.addGetDataComplete(Function.createDelegate(this, this.ctlSalesInfo_GetDataComplete));
        if (this._ctlMainCompanyInfo) this._ctlMainCompanyInfo.addInactiveCompanyComplete(Function.createDelegate(this, this._ctlMainCompanyInfo_InactiveCompanyComplete));
        //[001] code start
        if (this._ctlCMPDocuments) this._ctlCMPDocuments.getData();
        //[001] code end
        if (this._ctlCMPDocumentsNew) this._ctlCMPDocumentsNew.getData();
        if (this._ctlMainCompanyInfo) {
            this.changeTab(this._intCurrentTab);
        } else {
            $R_FN.showElement(this._pnlParentCompany, false);
            $R_FN.showElement(this._pnlAddress, false);
            $R_FN.showElement(this._pnlCompanyType, false);
            $R_FN.showElement(this._pnlRatings, false);
            $R_FN.showElement(this._pnlImportantNotes, false);
            $R_FN.showElement(this._pnlOnStop, false);
            $R_FN.showElement(this._pnlOnPremierCustomer, false);
            $R_FN.showElement(this._pnlOnTier2PremierCustomer, false);
            $R_FN.showElement(this._lblCompanyIsSanctioned, false);
            $R_FN.showElement(this._lblCompanyOnStop, false);
        }

        //Assign Hub supplier edit property on child control
        // alert("Detail=" + this._blnEditHubSupplier);
        //if (this._ctlMainCompanyInfo) this._ctlMainCompanyInfo._blnEditHubSupplier = this._blnEditHubSupplier;
        //if (this._ctlAddresses) this._ctlAddresses._blnEditHubSupplier;
        //if (this._ctlPurchasingInfo) this._ctlPurchasingInfo._blnEditHubSupplier;
        //if (this._ctlSalesInfo) this._ctlPurchasingInfo._blnEditHubSupplier;
        Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.callBaseMethod(this, "goInit");
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._element) $clearHandlers(this._element);
        if (this._ctlPageTitle) this._ctlPageTitle.dispose();
        if (this._ctlMainCompanyInfo) this._ctlMainCompanyInfo.dispose();
        if (this._ctlContactLog) this._ctlContactLog.dispose();
        if (this._ctlTransactions) this._ctlTransactions.dispose();
        if (this._ctlAddresses) this._ctlAddresses.dispose();
        if (this._ctlPurchasingInfo) this._ctlPurchasingInfo.dispose();
        if (this._ctlSalesInfo) this._ctlSalesInfo.dispose();
        if (this._ctlManufacturersSupplied) this._ctlManufacturersSupplied.dispose();
        if (this._ctlRatingCustomer) this._ctlRatingCustomer.dispose();
        if (this._ctlRatingSupplier) this._ctlRatingSupplier.dispose();
        if (this._btnTransactions) this._btnTransactions.dispose();
        //[001] code start
        if (this._ctlCMPDocuments) this._ctlCMPDocuments.dispose();
        //[001] code end
        if (this._ctlCMPDocumentsNew) this._ctlCMPDocumentsNew.dispose();
        if (this._ctlCompanyCertificate) this._ctlCompanyCertificate.dispose();
        /*if (this._ctlCompanyInsuranceCertificate) this._ctlCompanyInsuranceCertificate.dispose();*/
        
        if (this._ctlCompanyAPICustomer) this._ctlCompanyAPICustomer.dispose();
        this._ctlPageTitle = null;
        this._pnlMain = null;
        this._pnlDetail = null;
        this._ctlMainCompanyInfo = null;
        this._ctlContactLog = null;
        this._ctlTransactions = null;
        this._ctlAddresses = null;
        this._ctlPurchasingInfo = null;
        this._ctlSalesInfo = null;
        this._ctlManufacturersSupplied = null;
        this._pnlAddress = null;
        this._lblAddress = null;
        this._pnlParentCompany = null;
        this._lblCompanyType = null;
        this._lblSupplierRating = null;
        this._ctlRatingCustomer = null;
        this._lblCustomerRating = null;
        this._ctlRatingSupplier = null;
        this._pnlImportantNotes = null;
        this._lblImportantNotes = null;
        this._pnlOnStop = null;
        this._lblCompanyOnStop = null; //[004]
        this._lblCompanyIsSanctioned = null; //[004]
        this._pnlOnPremierCustomer = null;
        this._pnlOnTier2PremierCustomer = null;

        this._btnTransactions = null;
        this._aryGotTabData = null;
        //[001] code start
        this._ctlCMPDocuments = null;
        //[001] code end
        this._ctlCMPDocumentsNew = null;
        this._pnlCertificate = null;
        this._pnlAPICustomer = null;
        this._ctlCompanyCertificate = null;
        /*this._ctlCompanyInsuranceCertificate = null;*/
        
        this.ctlCompanyAPICustomer = null;
        this._lblInsurance = null;
        this._pnlInsurance = null;
        this.lblInsuranceFileNo = null;
        this._strStopStatus = null;
        this._blnEditHubSupplier = null;
        //[003] start
        this._IsGlobalLogin = null;
        //[003] end
        Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.callBaseMethod(this, "dispose");
    },

    changeTab: function (intTab) {
        this._intCurrentTab = intTab;
        this._ctlPageTitle.selectTab(intTab);
        $R_FN.showElement(this._pnlMain, intTab == 0);
        $R_FN.showElement(this._pnlDetail, intTab == 1);
        //certificate
        $R_FN.showElement(this._pnlCertificate, intTab == 2);
        $R_FN.showElement(this._pnlAPICustomer, intTab == 3);
        if (!this._aryGotTabData[intTab]) {
            if (intTab == 0) {
                this._ctlMainCompanyInfo.getData();
                this._ctlContactLog.getData();
                this._ctlAddresses.initialGetData();

                //get sales and purchasing info here so that we can update the transactions button availability correctly
                this._ctlSalesInfo.getData();
                this._ctlPurchasingInfo.getData();
            } else if (intTab == 1) {
                this._ctlSalesInfo.getData();
                this._ctlPurchasingInfo.getData();
                this._ctlTransactions.initialGetData();
                this._ctlManufacturersSupplied.getData();
            }
            else {
                this._ctlCompanyCertificate.getData();
                /*this._ctlCompanyInsuranceCertificate.getData();*/
                
                //API Customer 
                this._ctlCompanyAPICustomer.getData();
            }
            this._aryGotTabData[intTab] = true;
        }
    },

    ctlContactList_SelectContact: function () {
        this._intContactID = this._ctlContactList._intContactID;
    },

    ctlMainCompanyInfo_GetDataComplete: function () {
        var globalLoginClientNo = this._ctlMainCompanyInfo.getFieldValue("hidGlobalClientNo");
        //[003] start
        this._ctlAddresses._globalLoginClientNo = globalLoginClientNo;
        this._ctlSalesInfo._globalLoginClientNo = globalLoginClientNo;
        this._ctlPurchasingInfo._globalLoginClientNo = globalLoginClientNo;
        this._ctlManufacturersSupplied._globalLoginClientNo = globalLoginClientNo;
        this._ctlTransactions._globalLoginClientNo = globalLoginClientNo;
        this._ctlCompanyCertificate._globalLoginClientNo = globalLoginClientNo;
        /*this._ctlCompanyInsuranceCertificate._globalLoginClientNo = globalLoginClientNo;*/
        
        this._ctlCompanyAPICustomer._globalLoginClientNo = globalLoginClientNo;
        //[003] end
        this.updateCompanyName();
        this.updateParentCo();
        this.updateCompanyType();
        this.updateImportantNotes();
        this.updateOnPremierCustomer();
        this.updateTier2OnPremierCustomer();

        //alert(this._blnEditHubSupplier);
        this._ctlMainCompanyInfo.enableButtons(this._blnEditHubSupplier);
        this.updateOnStop();
        if (this._btnTransactions) {
            //test2
            //User can create PO in the case the above field is checked for Supplier. But PO not be approved until the Supplier is approved from the company's Details tab. 
            // alert(!(Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("hidSupplierOnStop"))));
            this._btnTransactions._blnApprovedForPOs = (Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("ctlhidSupplier")) && !(Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("hidSupplierOnStop"))));
            this._btnTransactions.updateApproval();
        }
    },

    ctlSalesInfo_GetDataComplete: function () {

        this.updateInsuranceDetails();
        this.updateCustomerRating();
        this.updateRatingsPanelVisibility();
        this.updateOnStop();


        if (this._btnTransactions) {
            this._btnTransactions._blnApprovedForSOs = Boolean.parse(this._ctlSalesInfo.getFieldValue("hidIsApproved"));
            //Comment: PO link disappear due to company Onstop property
            //this._btnTransactions._blnOnStop = this._ctlSalesInfo.getFieldValue("ctlIsOnStop");
            //if(this._ctlPurchasingInfo.getFieldValue("hidSupOnStop")) this._btnTransactions._blnApprovedForPOs = (Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidIsApproved")) && !(Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidSupOnStop"))));
            //User can create PO in the case the above field is checked for Supplier. But PO not be approved until the Supplier is approved from the company's Details tab. 
            // alert(!(Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("hidSupplierOnStop"))));
            this._btnTransactions._blnApprovedForPOs = (Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("ctlhidSupplier")) && !(Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("hidSupplierOnStop"))));
            if (this._ctlPurchasingInfo.getFieldValue("hidSupOnStop")) this._btnTransactions._blnOnStop = Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidSupOnStop"));
            this._btnTransactions.updateApproval();
        }
    },

    ctlPurchasingInfo_GetDataComplete: function () {
        this.updateSupplierRating();
        this.updateRatingsPanelVisibility();
        if (this._btnTransactions) {
            this._btnTransactions._blnOnStop = Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidSupOnStop"));
            //User can create PO in the case the above field is checked for Supplier. But PO not be approved until the Supplier is approved from the company's Details tab. 
            // this._btnTransactions._blnApprovedForPOs = (Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidIsApproved")) && !(Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidSupOnStop"))));
            // this._btnTransactions._blnApprovedForPOs = !(Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidSupOnStop")));
            this._btnTransactions._blnApprovedForPOs = (Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("ctlhidSupplier")) && !(Boolean.parse(this._ctlMainCompanyInfo.getFieldValue("hidSupplierOnStop"))));
            this._btnTransactions.updateApproval();
        }
        if (this._ctlPurchasingInfo) this._ctlPurchasingInfo.enableButtons(this._blnEditHubSupplier);
    },

    ctlAddresses_GetDataComplete: function () {
        this.updateAddress();
        if (this._ctlAddresses) {
            this._ctlAddresses._blnEditHubSupplier = this._blnEditHubSupplier;
            this._ctlAddresses.enableEditButtons(true);
        }
    },
    _ctlMainCompanyInfo_InactiveCompanyComplete: function () {
        if (this._ctlMainCompanyInfo) this._ctlMainCompanyInfo.getData();
        if (this._ctlManufacturersSupplied) this._ctlManufacturersSupplied.getCompanyInactive();
        if (this._ctlAddresses) this._ctlAddresses.getCompanyInactive();
        if (this._ctlCRMProspects) this._ctlCRMProspects.getCompanyInactive();
        if (this._ctlContactLog) this._ctlContactLog.getCompanyInactive();
        if (this._ctlGSA) this._ctlGSA.getCompanyInactive();
        if (this._ctlSalesInfo) this._ctlSalesInfo.getData();
        if (this._ctlPurchasingInfo) this._ctlPurchasingInfo.getData();
        if (this._ctlFinanceInfo) this._ctlFinanceInfo.getCompanyInactive();
        if (this._ctlCompanyCertificate) this._ctlCompanyCertificate.getCompanyInactive();
        if (this._ctlCompanyCertificate) this._ctlCompanyCertificate.getData();
        if (this._ctlCompanyAPICustomer) this._ctlCompanyAPICustomer.getCompanyInactive();
    },

    updateCompanyName: function () {
        var strCompanyName = $R_FN.setCleanTextValue(this._ctlMainCompanyInfo.getFieldValue("ctlCompanyName"));
        strCompanyName = strCompanyName + " (" + this._ctlMainCompanyInfo._intCompanyID + ")  " + this._strStopStatus;
        this._ctlPageTitle.updateTitle(strCompanyName);
        //alert(this._strStopStatus);
        // this._ctlPageTitle.updateCompanyTitle(strCompanyName,this._ctlMainCompanyInfo._intCompanyID);
        this._ctlAddresses._strCompanyName = strCompanyName;
        this._ctlPageTitle.updateTitleColor("pageTitleEARI", this._ctlMainCompanyInfo.getFieldValue("ctlEARIReported"));
    },

    updateParentCo: function () {
        var strParentCo = $R_FN.setCleanTextValue(this._ctlMainCompanyInfo.getFieldValue("hidParentCompany"));
        var intParentCoID = this._ctlMainCompanyInfo.getFieldValue("hidParentCompanyID");
        $R_FN.showElement(this._pnlParentCompany, (intParentCoID > 0));
        $R_FN.setInnerHTML(this._hypParentCompany, strParentCo);
        if (this._hypParentCompany) this._hypParentCompany.href = $RGT_gotoURL_Company(intParentCoID);
    },

    updateAddress: function () {
        var strAddress = $R_FN.setCleanTextValue(this._ctlAddresses._strDefaultAddress);
        $R_FN.showElement(this._pnlAddress, (strAddress.length > 0));
        $R_FN.setInnerHTML(this._lblAddress, strAddress);
    },

    updateCompanyType: function () {
        var strCompanyType = $R_FN.setCleanTextValue(this._ctlMainCompanyInfo.getFieldValue("ctlCompanyType"));
        $R_FN.showElement(this._pnlCompanyType, (strCompanyType.length > 0));
        $R_FN.setInnerHTML(this._lblCompanyType, strCompanyType);
    },

    updateSupplierRating: function () {
        this._ctlRatingSupplier.setRating(this._ctlPurchasingInfo.getFieldValue("ctlRating"));
        $R_FN.showElement(this._lblSupplierRating, Boolean.parse(this._ctlPurchasingInfo.getFieldValue("hidIsApproved")));
    },

    updateCustomerRating: function () {
        this._ctlRatingCustomer.setRating(this._ctlSalesInfo.getFieldValue("ctlRating"));
        $R_FN.showElement(this._lblCustomerRating, Boolean.parse(this._ctlSalesInfo.getFieldValue("hidIsApproved")));
    },

    updateRatingsPanelVisibility: function () {
        $R_FN.showElement(this._pnlRatings, ($R_FN.isElementVisible(this._lblSupplierRating) || $R_FN.isElementVisible(this._lblCustomerRating)));
    },

    updateOnStop: function () {
        //debugger;
        // [004] added or condition to check ctlSanction also in below statement
        $R_FN.showElement(this._pnlOnStop, (this._ctlSalesInfo.getFieldValue("ctlIsOnStop") || this._ctlMainCompanyInfo.getFieldValue("ctlSanction")));

        //$R_FN.showElement(this._pnlOnStop, (this._ctlSalesInfo.getFieldValue("ctlIsOnStop")));
    },
    updateOnPremierCustomer: function () {
        $R_FN.showElement(this._pnlOnPremierCustomer, this._ctlMainCompanyInfo.getFieldValue("ctlPremierCustomer"));

    },
    updateTier2OnPremierCustomer: function () {
        $R_FN.showElement(this._pnlOnTier2PremierCustomer, this._ctlMainCompanyInfo.getFieldValue("ctlTier2PremierCustomer"));

    },

    updateImportantNotes: function () {
        //var strImportantNotes = this._ctlMainCompanyInfo.getFieldValue("ctlImportantNotes");
        var strImportantNotes = MainInfoCompanyImportantNotes;
        if (!strImportantNotes) strImportantNotes = "";
        $R_FN.showElement(this._pnlImportantNotes, (strImportantNotes.length > 0));
        $R_FN.setInnerHTML(this._lblImportantNotes, strImportantNotes);

      // start RP-2772 rollback
            //this.OnNotesTextChange();
             //$(".ImportantNotesReadMoreLink").trigger("click");
      // end RP-2772 rollback
    },
    updateInsuranceDetails: function () {

        var InsuranceFileNo = this._ctlSalesInfo.getFieldValue("ctlInsuranceFileNo");
        var InsuredAmount = this._ctlSalesInfo.getFieldValue("ctlInsuredAmount");
        $R_FN.showElement(this._pnlInsurance, (InsuranceFileNo.length > 0));
        $R_FN.setInnerHTML(this._lblInsurance, InsuredAmount);
        $R_FN.setInnerHTML(this.lblInsuranceFileNo, InsuranceFileNo);
        var strCompanyName = $R_FN.setCleanTextValue(this._ctlMainCompanyInfo.getFieldValue("ctlCompanyName"));
        strCompanyName = strCompanyName + " (" + this._ctlMainCompanyInfo._intCompanyID + ")  " + this._strStopStatus;
        this._ctlPageTitle.updateTitle(strCompanyName);
    },

 // start RP-2772 rollback
    //OnNotesTextChange: function () {

    //    var moretext = '<p style="margin-top:2px;margin-buttom:2px">' + "...Read More" + '</p>';
    //    var lesstext = '<p style="margin-top:2px;margin-buttom:2px">' + "Read Less" + '</p>';
    //    var showChar = 200;
    //    $('#ctl00_cphMain_ctlPageTitle_ctl20_lblImportantNotes').each(function () {
    //        var content = $(this).html();

    //        if (content.length > showChar) {

    //            var c = content.substr(0, showChar);
    //            var h = content.substr(showChar - 1, content.length - showChar);

    //            var html = c + '<br></span><span class="MoreImportantNotes"><span>' + h + '</span><a href="" class="ImportantNotesReadMoreLink">' + lesstext + '</a></span>';

    //            $(this).html(html);
    //        }
    //        $(".ImportantNotesReadMoreLink").click(function () {
    //            if ($(this).hasClass("ImportantNotesReadLessLink")) {
    //                $(this).removeClass("ImportantNotesReadLessLink");
    //                $(this).html(lesstext);
    //            } else {
    //                $(this).addClass("ImportantNotesReadLessLink");
    //                $(this).html(moretext);
    //            }
    //            $(this).parent().prev().toggle();
    //            $(this).prev().toggle();
    //            return false;
    //        });
    //    });
    //}
 // end RP-2772 rollback
};

Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Contact.CompanyDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
