using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
    public partial class SIGILines : Base
    {


        private bool _blnShowIncludeInvoice = true;
        public bool ShowIncludeInvoice
        {
            get { return _blnShowIncludeInvoice; }
            set { _blnShowIncludeInvoice = value; }
        }
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            SetItemSearchType("SIGILines");
            AddScriptReference("Controls.ItemSearch.SIGILines.SIGILines.js");
		}

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.SIGILines", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("ShowIncludeInvoice", _blnShowIncludeInvoice);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
            ctlDesignBase.tblResults.AllowSelection = false;
            ctlDesignBase.tblResults.AllowMultipleSelection = false;
            ctlDesignBase.tblResults.ScrollBars = ScrollBars.Vertical;
            ctlDesignBase.tblResults.PanelHeight = 250;
            ctlDesignBase.tblResults.InsideDataListNugget = true;
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(40), false, HorizontalAlign.Center));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("GoodsIn", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("GIDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", Unit.Empty, true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DebitNumber", Unit.Empty, false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Quantity", Unit.Pixel(70), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("UnitPrice", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Value", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("ShippingCost", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), false));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("NPR", Unit.Pixel(60), false));
			base.OnPreRender(e);
		}
	}
}