﻿namespace Rebound.GlobalTrader.Site.Areas.BOM.Models
{
    public class KubAssistanceForBOM
    {
        public string PartNo { get; set; }
        public string FullPartNo { get; set; }
        public string NumberOfRequirement { get; set; }
        public string LastQuotedPrice { get; set; }
        public string LastHubprice { get; set; }
        public string NumberOfInvoice { get; set; }
        public string LastestHubRFQName { get; set; }
        public string LastestHubNumberDate { get; set; }
        public string LastestHubRFQId { get; set; }
        public string LastSoldPrice { get; set; }
        public string LastHighestSoldPrice { get; set; }
        public string LastLowestSoldPrice { get; set; }
        public string NumberOfQuote { get; set; }
        public string NumberQuoteToSalesOrder { get; set; }
        public string LastUpdatedDate { get; set; }
        public string LastDatePartSoldToBomCustomer { get; set; }
        public string IHSResultForPartNo { get; set; }
        public string LyticaResultForPartNo { get; set; }
        public bool IsEnalbe { get; set; }
    }
}