//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Messages {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Messages() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Messages", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new HUBRFQ.
        /// </summary>
        internal static string AddNotAllowed_BOM {
            get {
                return ResourceManager.GetString("AddNotAllowed_BOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Company.
        /// </summary>
        internal static string AddNotAllowed_Company {
            get {
                return ResourceManager.GetString("AddNotAllowed_Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Credit Note.
        /// </summary>
        internal static string AddNotAllowed_Credit {
            get {
                return ResourceManager.GetString("AddNotAllowed_Credit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Customer RMA.
        /// </summary>
        internal static string AddNotAllowed_CRMA {
            get {
                return ResourceManager.GetString("AddNotAllowed_CRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Debit Note.
        /// </summary>
        internal static string AddNotAllowed_Debit {
            get {
                return ResourceManager.GetString("AddNotAllowed_Debit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Goods In Note.
        /// </summary>
        internal static string AddNotAllowed_GoodsIn {
            get {
                return ResourceManager.GetString("AddNotAllowed_GoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Group Code.
        /// </summary>
        internal static string AddNotAllowed_GroupCodeCompany {
            get {
                return ResourceManager.GetString("AddNotAllowed_GroupCodeCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Group Code.
        /// </summary>
        internal static string AddNotAllowed_GroupCodeCompanyAdd {
            get {
                return ResourceManager.GetString("AddNotAllowed_GroupCodeCompanyAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Product Catalogue.
        /// </summary>
        internal static string AddNotAllowed_IHS {
            get {
                return ResourceManager.GetString("AddNotAllowed_IHS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Invoice.
        /// </summary>
        internal static string AddNotAllowed_Invoice {
            get {
                return ResourceManager.GetString("AddNotAllowed_Invoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Lot.
        /// </summary>
        internal static string AddNotAllowed_Lot {
            get {
                return ResourceManager.GetString("AddNotAllowed_Lot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Manufacturer.
        /// </summary>
        internal static string AddNotAllowed_Manufacturer {
            get {
                return ResourceManager.GetString("AddNotAllowed_Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Price Quotes.
        /// </summary>
        internal static string AddNotAllowed_PriceQuoteUtility {
            get {
                return ResourceManager.GetString("AddNotAllowed_PriceQuoteUtility", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Purchase Order.
        /// </summary>
        internal static string AddNotAllowed_PurchaseOrder {
            get {
                return ResourceManager.GetString("AddNotAllowed_PurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Quote.
        /// </summary>
        internal static string AddNotAllowed_Quote {
            get {
                return ResourceManager.GetString("AddNotAllowed_Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Customer Requirement.
        /// </summary>
        internal static string AddNotAllowed_Requirement {
            get {
                return ResourceManager.GetString("AddNotAllowed_Requirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Sales Order.
        /// </summary>
        internal static string AddNotAllowed_SalesOrder {
            get {
                return ResourceManager.GetString("AddNotAllowed_SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Service.
        /// </summary>
        internal static string AddNotAllowed_Service {
            get {
                return ResourceManager.GetString("AddNotAllowed_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Supplier RMA.
        /// </summary>
        internal static string AddNotAllowed_SRMA {
            get {
                return ResourceManager.GetString("AddNotAllowed_SRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Stock Item.
        /// </summary>
        internal static string AddNotAllowed_Stock {
            get {
                return ResourceManager.GetString("AddNotAllowed_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, you do not have permissions to add a new Supplier Invoice.
        /// </summary>
        internal static string AddNotAllowed_SupplierInvoice {
            get {
                return ResourceManager.GetString("AddNotAllowed_SupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All modules are currently visible.
        /// </summary>
        internal static string AllModulesVisible {
            get {
                return ResourceManager.GetString("AllModulesVisible", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, there was a problem. The following details have been noted in your Computer&apos;s event log and emailed to Rebound staff for attention..
        /// </summary>
        internal static string ApplicationError {
            get {
                return ResourceManager.GetString("ApplicationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To edit this, please remove all lines.
        /// </summary>
        internal static string AS9120Message {
            get {
                return ResourceManager.GetString("AS9120Message", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Assigned.
        /// </summary>
        internal static string BOMAssigned {
            get {
                return ResourceManager.GetString("BOMAssigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager No Bid.
        /// </summary>
        internal static string BOMManagerNoBid {
            get {
                return ResourceManager.GetString("BOMManagerNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Recall.
        /// </summary>
        internal static string BOMManagerRecall {
            get {
                return ResourceManager.GetString("BOMManagerRecall", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Recall No Bid.
        /// </summary>
        internal static string BOMManagerRecallNoBid {
            get {
                return ResourceManager.GetString("BOMManagerRecallNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Released.
        /// </summary>
        internal static string BOMManagerReleased {
            get {
                return ResourceManager.GetString("BOMManagerReleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ No-Bid.
        /// </summary>
        internal static string BOMNoBid {
            get {
                return ResourceManager.GetString("BOMNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Released.
        /// </summary>
        internal static string BOMReleased {
            get {
                return ResourceManager.GetString("BOMReleased", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You are using {0} Version {1}&lt;br /&gt;We recommend you use Firefox 2+, Internet Explorer 8+, Chrome 1+ or Safari 3+..
        /// </summary>
        internal static string BrowserWarning {
            get {
                return ResourceManager.GetString("BrowserWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signed for and on behalf of Rebound Electronics (UK) Ltd..
        /// </summary>
        internal static string CCLineMessage {
            get {
                return ResourceManager.GetString("CCLineMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager {0} has been closed..
        /// </summary>
        internal static string CloseBOMManager {
            get {
                return ResourceManager.GetString("CloseBOMManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Company is Sanctioned.
        /// </summary>
        internal static string CompanySanctioned {
            get {
                return ResourceManager.GetString("CompanySanctioned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to approve this order?.
        /// </summary>
        internal static string ConfirmApprove {
            get {
                return ResourceManager.GetString("ConfirmApprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have chosen to auto approve SOs: All unauthorised SOs will be authorised and cannot be undone. Are you sure you would like to continue?.
        /// </summary>
        internal static string ConfirmAppSettingsEditAutoApproveSO {
            get {
                return ResourceManager.GetString("ConfirmAppSettingsEditAutoApproveSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to authorise this order?.
        /// </summary>
        internal static string ConfirmAuthorise {
            get {
                return ResourceManager.GetString("ConfirmAuthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to authorise this order? &lt;br /&gt; You are checking/authorising an order against an account currently on stop..
        /// </summary>
        internal static string ConfirmAuthoriseCompanyOnStop {
            get {
                return ResourceManager.GetString("ConfirmAuthoriseCompanyOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to de-authorise this order?.
        /// </summary>
        internal static string ConfirmDeauthorise {
            get {
                return ResourceManager.GetString("ConfirmDeauthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this address?.
        /// </summary>
        internal static string ConfirmDeleteCompanyAddress {
            get {
                return ResourceManager.GetString("ConfirmDeleteCompanyAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to un-approve this order?.
        /// </summary>
        internal static string ConfirmDisapprove {
            get {
                return ResourceManager.GetString("ConfirmDisapprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to export this invoice?.
        /// </summary>
        internal static string ConfirmExport {
            get {
                return ResourceManager.GetString("ConfirmExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to export this Credit Note?.
        /// </summary>
        internal static string ConfirmExportCredit {
            get {
                return ResourceManager.GetString("ConfirmExportCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to export this Debit Note?.
        /// </summary>
        internal static string ConfirmExportDebit {
            get {
                return ResourceManager.GetString("ConfirmExportDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to hold this invoice export?.
        /// </summary>
        internal static string ConfirmHold {
            get {
                return ResourceManager.GetString("ConfirmHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to No-Bid this HUBRFQ?.
        /// </summary>
        internal static string ConfirmNoBid {
            get {
                return ResourceManager.GetString("ConfirmNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This functionality allows a stopped company&apos;s sales order to ship, Are you sure?.
        /// </summary>
        internal static string ConfirmReadyToShip {
            get {
                return ResourceManager.GetString("ConfirmReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to Recall No-Bid this HUBRFQ?.
        /// </summary>
        internal static string ConfirmRecallNoBid {
            get {
                return ResourceManager.GetString("ConfirmRecallNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to release this invoice for editing?.
        /// </summary>
        internal static string ConfirmRelease {
            get {
                return ResourceManager.GetString("ConfirmRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to release this Credit Note for editing?.
        /// </summary>
        internal static string ConfirmReleaseCredit {
            get {
                return ResourceManager.GetString("ConfirmReleaseCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to release this Debit Note for editing?.
        /// </summary>
        internal static string ConfirmReleaseDebit {
            get {
                return ResourceManager.GetString("ConfirmReleaseDebit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to Release selected PO line locked by EPR?.
        /// </summary>
        internal static string ConfirmReleasePO {
            get {
                return ResourceManager.GetString("ConfirmReleasePO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to un-hold this invoice export?.
        /// </summary>
        internal static string ConfirmUnHold {
            get {
                return ResourceManager.GetString("ConfirmUnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to Un-Release this PO line?.
        /// </summary>
        internal static string ConfirmUnReleasePO {
            get {
                return ResourceManager.GetString("ConfirmUnReleasePO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO already created from sales order..
        /// </summary>
        internal static string CreateIPOAlreadyExist {
            get {
                return ResourceManager.GetString("CreateIPOAlreadyExist", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Some error occured while processing your request..
        /// </summary>
        internal static string CreateIPOFailed {
            get {
                return ResourceManager.GetString("CreateIPOFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Available Stock is running out, please check the associated Stock or update SO&apos;s QTY.
        /// </summary>
        internal static string CreateIPOFailedNotEnoughStock {
            get {
                return ResourceManager.GetString("CreateIPOFailedNotEnoughStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales order line not in sourcing result..
        /// </summary>
        internal static string CreateIPONotInSR {
            get {
                return ResourceManager.GetString("CreateIPONotInSR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New IPO created.
        /// </summary>
        internal static string CreateIPOSubject {
            get {
                return ResourceManager.GetString("CreateIPOSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you for your order. Please note your order will not be processed until your Sales Order is checked.
        /// </summary>
        internal static string CreateIPOSuccess {
            get {
                return ResourceManager.GetString("CreateIPOSuccess", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error: No data found.
        /// </summary>
        internal static string DataNotFound {
            get {
                return ResourceManager.GetString("DataNotFound", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, there was a problem sending your message, please try again or ask your System Administrator to check the Email settings.
        /// </summary>
        internal static string DocumentSendError {
            get {
                return ResourceManager.GetString("DocumentSendError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your message was sent successfully.
        /// </summary>
        internal static string DocumentSentOK {
            get {
                return ResourceManager.GetString("DocumentSentOK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Double-click a message to view.
        /// </summary>
        internal static string DoubleClickToViewMail {
            get {
                return ResourceManager.GetString("DoubleClickToViewMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your GT login password: {0}.
        /// </summary>
        internal static string ForgotMessage {
            get {
                return ResourceManager.GetString("ForgotMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your credentials has been sent to your email address.
        /// </summary>
        internal static string ForgotPasswordEmailSent {
            get {
                return ResourceManager.GetString("ForgotPasswordEmailSent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot your password.
        /// </summary>
        internal static string ForgotPwdSubject {
            get {
                return ResourceManager.GetString("ForgotPwdSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear User,
        ///
        ///As requested, please find bellow the login detail 
        ///
        ///
        ///{0}
        ///
        ///Thanks,
        ///Admin.
        /// </summary>
        internal static string ForgotUserName {
            get {
                return ResourceManager.GetString("ForgotUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgot your user name and password.
        /// </summary>
        internal static string ForgotUserSubject {
            get {
                return ResourceManager.GetString("ForgotUserSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NOTIFICATION - Sales Order(s) Deallocation.
        /// </summary>
        internal static string GIQuarantine {
            get {
                return ResourceManager.GetString("GIQuarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods-In Line ({0}) status.
        /// </summary>
        internal static string GIQuerySales {
            get {
                return ResourceManager.GetString("GIQuerySales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hidden modules.
        /// </summary>
        internal static string HiddenModules {
            get {
                return ResourceManager.GetString("HiddenModules", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Raised {0} : Reverse Logistic Offer Match.
        /// </summary>
        internal static string HUBRFQRaised {
            get {
                return ResourceManager.GetString("HUBRFQRaised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, there was an error.
        /// </summary>
        internal static string LandedCostCalculationError {
            get {
                return ResourceManager.GetString("LandedCostCalculationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, there was a problem with your application licence.
        /// </summary>
        internal static string LoginLicenceProblem {
            get {
                return ResourceManager.GetString("LoginLicenceProblem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, there was a problem with your application licence&lt;br /&gt;You have exceeded your permitted number of users..
        /// </summary>
        internal static string LoginLicenceProblem_TooManyUsers {
            get {
                return ResourceManager.GetString("LoginLicenceProblem_TooManyUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that password is not recognised.
        /// </summary>
        internal static string LoginPasswordNotRecognised {
            get {
                return ResourceManager.GetString("LoginPasswordNotRecognised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that username or email is not recognised.
        /// </summary>
        internal static string LoginUsernameEmailNotRecognised {
            get {
                return ResourceManager.GetString("LoginUsernameEmailNotRecognised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, that username is not recognised.
        /// </summary>
        internal static string LoginUsernameNotRecognised {
            get {
                return ResourceManager.GetString("LoginUsernameNotRecognised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have new message(s).
        /// </summary>
        internal static string NewMailMessages {
            get {
                return ResourceManager.GetString("NewMailMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to There is no accounting information to display.
        /// </summary>
        internal static string NoAccountingInfo {
            get {
                return ResourceManager.GetString("NoAccountingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Approval Status.
        /// </summary>
        internal static string OGELApprovalStatus {
            get {
                return ResourceManager.GetString("OGELApprovalStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string OnPremierCustomer {
            get {
                return ResourceManager.GetString("OnPremierCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Company is On Stop.
        /// </summary>
        internal static string OnStop {
            get {
                return ResourceManager.GetString("OnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string OnTier2PremierCustomer {
            get {
                return ResourceManager.GetString("OnTier2PremierCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PRODUCT IS ERAI HIGH RISK.
        /// </summary>
        internal static string PartERAIMessage {
            get {
                return ResourceManager.GetString("PartERAIMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have a new Partwatch Match.
        /// </summary>
        internal static string PartWatchMatchMsg {
            get {
                return ResourceManager.GetString("PartWatchMatchMsg", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order {0}  Line ({1}) Closed.
        /// </summary>
        internal static string POCloseSubject {
            get {
                return ResourceManager.GetString("POCloseSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please share the required folder or contact network administration.
        /// </summary>
        internal static string PrintLabelFolderMessage {
            get {
                return ResourceManager.GetString("PrintLabelFolderMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ {0} sent for Price Request.
        /// </summary>
        internal static string PurchaseRequest {
            get {
                return ResourceManager.GetString("PurchaseRequest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager {0} sent for Price Request.
        /// </summary>
        internal static string PurchaseRequestBOMManager {
            get {
                return ResourceManager.GetString("PurchaseRequestBOMManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failure – Due to Page Refresh.
        /// </summary>
        internal static string RefereshMessage {
            get {
                return ResourceManager.GetString("RefereshMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please enter all required parameters.
        /// </summary>
        internal static string ReportParameterValidationError {
            get {
                return ResourceManager.GetString("ReportParameterValidationError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sorry, there was a problem sending your message, please try again or ask your System Administrator to check the Email settings.
        /// </summary>
        internal static string RFQSendError {
            get {
                return ResourceManager.GetString("RFQSendError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your message was sent successfully.
        /// </summary>
        internal static string RFQSentOK {
            get {
                return ResourceManager.GetString("RFQSentOK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear Reverse Logistics Team Member,
        ///
        ///A HUBRFQ has been raised for a part that we have in stock. Please review the details of the {0} here: 
        ///Company Name: {1}
        ///
        ///{2}
        ///
        ///&lt;i&gt;Note: Each notification email only contains a maximum of 20 records&lt;/i&gt;
        ///
        ///Please visit and action.
        ///
        ///Kind Regards
        ///Global Trader.
        /// </summary>
        internal static string RLStockIntroduction1 {
            get {
                return ResourceManager.GetString("RLStockIntroduction1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear Reverse Logistics Team Member,
        ///
        ///HUBRFQ(s) has been raised for a part which we have in stock. Please review details of the HUBRFQ(s) here:
        ///{0}
        ///&lt;i&gt;Note: Each notification email only contains a maximum of 20 records&lt;/i&gt;
        ///
        ///Please visit and action.
        ///
        ///Kind Regards
        ///Global Trader.
        /// </summary>
        internal static string RLStockIntroduction2 {
            get {
                return ResourceManager.GetString("RLStockIntroduction2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear Reverse Logistics Team Member,&lt;br/&gt;
        ///
        ///A HUBRFQ has been raised for a part that we have in stock. Please review the details of the {0} here:&lt;br/&gt;
        ///Company Name: {1}.
        /// </summary>
        internal static string RLStockIntroductionOutlook1 {
            get {
                return ResourceManager.GetString("RLStockIntroductionOutlook1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dear Reverse Logistics Team Member,&lt;br/&gt;&lt;br/&gt;
        ///
        ///HUBRFQ(s) has been raised for a part which we have in stock. Please review details of the HUBRFQ(s) here:
        ///.
        /// </summary>
        internal static string RLStockIntroductionOutlook2 {
            get {
                return ResourceManager.GetString("RLStockIntroductionOutlook2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ(s) raised for RL In-Stock {0}.
        /// </summary>
        internal static string RLStockSubject {
            get {
                return ResourceManager.GetString("RLStockSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A HUBRFQ {0} raised for RL In-Stock.
        /// </summary>
        internal static string RLStockSubject1 {
            get {
                return ResourceManager.GetString("RLStockSubject1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your changes were saved successfully.
        /// </summary>
        internal static string SavedOK {
            get {
                return ResourceManager.GetString("SavedOK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select address to view or edit.
        /// </summary>
        internal static string SelectAddress {
            get {
                return ResourceManager.GetString("SelectAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select contact to view.
        /// </summary>
        internal static string SelectContactToView {
            get {
                return ResourceManager.GetString("SelectContactToView", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order {0} Export Approval Notification.
        /// </summary>
        internal static string SendExportApproval {
            get {
                return ResourceManager.GetString("SendExportApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sending.
        /// </summary>
        internal static string Sending {
            get {
                return ResourceManager.GetString("Sending", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The total of open orders takes the company over its credit limit, this Sales Order cannot be authorised.
        /// </summary>
        internal static string SOAuthorisation_OverCreditLimit {
            get {
                return ResourceManager.GetString("SOAuthorisation_OverCreditLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales order has been checked: {0}.
        /// </summary>
        internal static string SOAuthorise {
            get {
                return ResourceManager.GetString("SOAuthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The order currency differs from the customers base currency, this could lead to a variance in line total vs order value..
        /// </summary>
        internal static string SOLines_DifferCurrency {
            get {
                return ResourceManager.GetString("SOLines_DifferCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This line would take the company over its credit limit, it cannot be posted.
        /// </summary>
        internal static string SOLines_OverCreditLimit {
            get {
                return ResourceManager.GetString("SOLines_OverCreditLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This line would take the company over its credit limit, it cannot be created IPO.
        /// </summary>
        internal static string SOLines_OverCreditLimitIPO {
            get {
                return ResourceManager.GetString("SOLines_OverCreditLimitIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing Result provided by PO HUb for this salesorder line(s) ( {0} ) are recalled. Please contact your buyer.
        /// </summary>
        internal static string SOLRecalled {
            get {
                return ResourceManager.GetString("SOLRecalled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The balance of all posted line takes the company over its credit limit, this Sales Order cannot be posted.
        /// </summary>
        internal static string SOPostLine_OverCreditLimit {
            get {
                return ResourceManager.GetString("SOPostLine_OverCreditLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order ~SONO~ Approval Request.
        /// </summary>
        internal static string SORRequestSubject {
            get {
                return ResourceManager.GetString("SORRequestSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This Stock is Quarantined.
        /// </summary>
        internal static string StockQuarantined {
            get {
                return ResourceManager.GetString("StockQuarantined", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval By Line Manager.
        /// </summary>
        internal static string SupplierApprovalByLineManager {
            get {
                return ResourceManager.GetString("SupplierApprovalByLineManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval Completed.
        /// </summary>
        internal static string SupplierApprovalByQuality {
            get {
                return ResourceManager.GetString("SupplierApprovalByQuality", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval Request Escalated.
        /// </summary>
        internal static string SupplierApprovalForQualityEscalate {
            get {
                return ResourceManager.GetString("SupplierApprovalForQualityEscalate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval Request For Quality Team Review.
        /// </summary>
        internal static string SupplierApprovalForQualityReview {
            get {
                return ResourceManager.GetString("SupplierApprovalForQualityReview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Approval request is no longer required for PO :.
        /// </summary>
        internal static string SupplierApprovalSupplierChnage {
            get {
                return ResourceManager.GetString("SupplierApprovalSupplierChnage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SUPPLIER IS ERAI REPORTED.
        /// </summary>
        internal static string SupplierERAIMessage {
            get {
                return ResourceManager.GetString("SupplierERAIMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this address?.
        /// </summary>
        internal static string SureDeleteAddress {
            get {
                return ResourceManager.GetString("SureDeleteAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this contact?.
        /// </summary>
        internal static string SureDeleteContact {
            get {
                return ResourceManager.GetString("SureDeleteContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this contact log item?.
        /// </summary>
        internal static string SureDeleteContactLogItem {
            get {
                return ResourceManager.GetString("SureDeleteContactLogItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You searched for.
        /// </summary>
        internal static string YouSearchedFor {
            get {
                return ResourceManager.GetString("YouSearchedFor", resourceCulture);
            }
        }
    }
}
