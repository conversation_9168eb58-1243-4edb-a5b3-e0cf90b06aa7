﻿/*     
===========================================================================================    
TASK   UPDATED BY   DATE   ACTION  DESCRIPTION    
[US-215493]  CuongDox  2-Nov-2024  CREATE  Get Prospective Offer logs By Id proId and CusrId
===========================================================================================    
*/  
IF OBJECT_ID('tbSupplierInvoiceNotMatched', 'U') IS NOT NULL
BEGIN
    DROP TABLE tbSupplierInvoiceNotMatched;
END;

CREATE TABLE tbSupplierInvoiceNotMatched (
    SupplierInvoiceNotMatchedID INT IDENTITY(1,1) PRIMARY KEY,
    SupplierInvoiceNo VARCHAR(50) NOT NULL,
    InvoiceAmount FLOAT NOT NULL,
    SubTotalAmount FLOAT NOT NULL,
	ClientNo INT NOT NULL,
    DLUP DATETIME NOT NULL DEFAULT GETDATE()
);
