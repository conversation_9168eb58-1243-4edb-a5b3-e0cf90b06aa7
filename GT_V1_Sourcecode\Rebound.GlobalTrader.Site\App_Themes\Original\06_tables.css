﻿/* special classes for data tables */
/*********************************************************************/
table.stockQuantities
{
    width: 100%;
    margin-bottom: 15px;
}

    table.stockQuantities td
    {
        width: 24%;
        background-repeat: repeat-x;
        background-position: bottom left;
        text-align: center;
        font-family: Lucida Sans Unicode, Arial !important;
        padding: 10px 0px;
    }

        table.stockQuantities td.sep
        {
            width: 1%;
            padding: 0px;
        }

        table.stockQuantities td .number
        {
            font-size: 24px;
            font-family: Lucida Sans Unicode, Arial !important;
        }

        table.stockQuantities td.inStock
        {
            background-image: url(images/stock/instock.jpg);
            color: #ffffff;
            border: solid 1px #0020A0;
        }

        table.stockQuantities td.onOrder
        {
            background-image: url(images/stock/onorder.jpg);
            color: #ffffff;
            border: solid 1px #0060A0;
        }

        table.stockQuantities td.allocated
        {
            background-image: url(images/stock/allocated.jpg);
            border: solid 1px #A06000;
        }

        table.stockQuantities td.available
        {
            background-image: url(images/stock/available.jpg);
            border: solid 1px #9C9C00;
        }

        table.stockQuantities td.quarantined
        {
            background-image: url(images/stock/quarantined.jpg);
            border: solid 1px #9C9C00;
        }

table.dataTable td.quoteLineClosed, table.dataTable td.poLineClosed, table.dataTable td.contactInactive, table.dataTable td.ceased, table.dataTable tr:hover td.quoteLineClosed, table.dataTable tr:hover td.poLineClosed, table.dataTable tr:hover td.contactInactive, table.dataTable tr:hover td.ceased, table.dataTable tr.selected td.quoteLineClosed, table.dataTable tr.selected td.poLineClosed, table.dataTable tr.selected td.contactInactive, table.dataTable tr.selected td.ceased
{
    color: #999999;
}

table.dataTable td.secUserInactive
{
    color: #71a15e;
}

table.dataTable td.posted
{
    font-size: 11px;
}

table.dataTable td.posted_First, table.dataTable td.unposted_First, table.dataTable td.allocated_First, table.dataTable td.shipped_First, table.dataTable td.partShipped_First, table.dataTable td.giInspected_First, table.dataTable td.inactive_First, table.dataTable td.readyToShip_First, table.dataTable td.notReadyToShip_First, table.dataTable td.received_First, table.dataTable td.partReceived_First
{
    background-repeat: no-repeat;
    background-position: 2px 2px;
    padding-left: 19px;
}

table.dataTable td.posted_First
{
    background-image: url(images/tables/posted.gif);
}

table.dataTable tr:hover td.posted_First
{
    background-image: url(images/tables/posted_x.gif);
}

table.dataTable tr.selected td.posted_First
{
    background-image: url(images/tables/posted_sel.gif);
}

table.dataTable td.allocated_First
{
    background-image: url(images/tables/allocated.gif);
}

table.dataTable tr:hover td.allocated_First
{
    background-image: url(images/tables/allocated_x.gif);
}

table.dataTable tr.selected td.allocated_First
{
    background-image: url(images/tables/allocated_sel.gif);
}

table.dataTable tr td.giInspected_First
{
    background-image: url(images/tables/gi_inspected_x.gif);
}

table.dataTable tr:hover td.giInspected_First
{
    background-image: url(images/tables/gi_inspected_x.gif);
}

table.dataTable tr.selected td.giInspected_First
{
    background-image: url(images/tables/gi_inspected_sel.gif);
}

table.dataTable td.shipped_First
{
    background-image: url(images/tables/shipped.gif);
}

table.dataTable tr:hover td.shipped_First
{
    background-image: url(images/tables/shipped_x.gif);
}

table.dataTable tr.selected td.shipped_First
{
    background-image: url(images/tables/shipped_sel.gif);
}

table.dataTable td.partShipped_First
{
    background-image: url(images/tables/partshipped.gif);
}

table.dataTable tr:hover td.partShipped_First
{
    background-image: url(images/tables/partshipped_x.gif);
}

table.dataTable tr.selected td.partShipped_First
{
    background-image: url(images/tables/partshipped_sel.gif);
}

table.dataTable td.received_First
{
    background-image: url(images/tables/shipped.gif);
}

table.dataTable tr:hover td.received_First
{
    background-image: url(images/tables/shipped_x.gif);
}

table.dataTable tr.selected td.received_First
{
    background-image: url(images/tables/shipped_sel.gif);
}

table.dataTable td.partReceived_First
{
    background-image: url(images/tables/partshipped.gif);
}

table.dataTable tr:hover td.partReceived_First
{
    background-image: url(images/tables/partshipped_x.gif);
}

table.dataTable tr.selected td.partReceived_First
{
    background-image: url(images/tables/partshipped_sel.gif);
}

table.dataTable td.notReadyToShip_First
{
    background-image: url(images/tables/notok.gif);
}

table.dataTable tr:hover td.notReadyToShip_First
{
    background-image: url(images/tables/notok_x.gif);
}

table.dataTable tr.selected td.notReadyToShip_First
{
    background-image: url(images/tables/notok_sel.gif);
}

table.dataTable td.readyToShip_First
{
    background-image: url(images/tables/ready.gif);
}

table.dataTable tr:hover td.readyToShip_First
{
    background-image: url(images/tables/ready_x.gif);
}

table.dataTable tr.selected td.readyToShip_First
{
    background-image: url(images/tables/ready_sel.gif);
}

table.dataTable td.toDoIncompleteReminder_First, table.dataTable td.toDoIncompleteNoReminder_First, table.dataTable td.toDoCompleteReminder_First, table.dataTable td.toDoCompleteNoReminder_First
{
    background-repeat: no-repeat;
    background-position: 5px 4px;
    padding-left: 40px;
}

table.dataTable td.toDoIncompleteReminder_First
{
    background-image: url(images/tables/todo_incomplete_remind.gif);
}

table.dataTable td.toDoIncompleteNoReminder_First
{
    background-image: url(images/tables/todo_incomplete.gif);
}

table.dataTable td.toDoCompleteNoReminder_First
{
    background-image: url(images/tables/todo_complete.gif);
}

table.dataTable tr.selected td.toDoIncompleteReminder_First
{
    background-image: url(images/tables/todo_incomplete_remind_sel.gif);
}

table.dataTable tr.selected td.toDoIncompleteNoReminder_First
{
    background-image: url(images/tables/todo_incomplete_sel.gif);
}

table.dataTable tr.selected td.toDoCompleteNoReminder_First
{
    background-image: url(images/tables/todo_complete_sel.gif);
}

table.dataTable td.toDoCompleteReminder, table.dataTable td.toDoCompleteNoReminder, table.dataTable tr:hover td.toDoCompleteReminder, tr:hover table.dataTable td.toDoCompleteNoReminder
{
    color: #999999 !important;
    text-decoration: line-through;
}

table.dataTable td.secGroupLocked, table.dataTable td.secUserAdmin, table.dataTable td.locked, table.dataTable td.defaultAddress, table.dataTable td.defaultContact, table.dataTable td.defaultWarehouse
{
    font-weight: bold;
}

table.dataTable td.inactive, table.dataTable tr:hover td.inactive, table.dataTable tr.selected td.inactive, table.dataTable tr td.futureTaxRate
{
    color: #999999;
}

table.dataTable td.inactive_First
{
    background-image: url(images/tables/inactive.gif);
    background-position: 3px 4px;
}

table.dataTable tr:hover td.inactive_First
{
    background-image: url(images/tables/inactive_x.gif);
}

table.dataTable tr.selected td.inactive_First
{
    background-image: url(images/tables/inactive_sel.gif);
}

table.dataTable tr td.virtual
{
    font-style: italic;
}

.release-item-background
{
    background-color: Orange;
}

.release-Bom-background
{
    background-color: Orange!important;
}


.sourcingNotesIsHUB
{
    color: red !important;
}

table.dataTable td.sourcingNotesIsHUB
{
    color: red !important;
}

table.dataTable td.ceased2 {
    color: #8d7c7c !important;
    pointer-events: none !important;
    cursor: default;
}

.ibtnCalc_White a
{
    color: #FFFFFF!important;
    font-weight: bold;
}

.RowColor
{
    background-color: #8E3471 !important;
}

.yellow-background
{
    background-color: rgb(255, 255, 0)!important;
    color: #000000!important;
}
.red-background {
    background-color: #ff6464 !important;
    color: #000000 !important;
}
.red-backgroundpart {
    background-color: #ff6464 !important;
    color: #000000 !important;
}
    .red-backgroundpart:hover {
        background-color: #ff6464 !important;
        color: #000000 !important;
    }
        .red-backgroundpart:hover td {
            background-color: #ff6464 !important;
            color: #000000 !important;
        }
    .RowColorInactive {
        background-color: #BBEDB7 !important;
        color: #999999 !important;
    }

.IsImportant
{
    background-color: #BBEDB7 !important;
    color: #999999 !important;
}

.rowRedBackground
{
    background-color: rgb(255, 100, 100)!important;
}
.rowGreyBackground 
{
    background-color: rgb(211, 211, 211) !important;
}
.rowAmberBackground
{
    background-color: rgb(255, 191, 0)!important;
}
.rowGreenBackground
{
    background-color: rgb(50, 205, 50)!important;
}
.rowOrangeBackground
{
    background-color: Orange;
}
.rowStockBackground
{
    background-color: #db9494;
}
.rowOfferBackground
{
    background-color: #9dbced;
}
.rowExcessBackground
{
    background-color: #edcd6d;
}
.MFRResticted {
    color: #9b1b1b;
    font-style: normal;
    font-family: Tahoma !important;
    font-size: 13px !important;
    padding-left: 9px;
}

#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlAdd_ctlDB_ctlManufacturer_ctl03_cmbManufacturer,
#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlManufacturer_ctl04_cmbManufacturer,
#ctl00_cphMain_ctlQuoteLines_ctlDB_ctl14_frmAdd_ctlDB_ctlManufacturer_ctl03_cmbManufacturer,
#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlManufacturer_ctl03_cmbManufacturer,
#ctl00_cphMain_ctlRestrictedManufacture_ctlDB_ctl14_frmEdit_ctlDB_ctlManufacturer_ctl04_cmbManufacturer,
#ctl00_cphMain_ctlQuoteLines_ctlDB_ctl14_frmEdit_ctlDB_ctlManufacturer_ctl03_cmbManufacturer,
#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ctlManufacturer_ctl03_cmbManufacturer,
#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ctlManufacturer_ctl04_cmbManufacturer {
    width: auto;
    float: left;
}
