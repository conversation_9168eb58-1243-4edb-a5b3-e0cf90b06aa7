﻿//Marker     Changed by         Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>     10/09/2021    Added class for precogs supplier.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class PrecogsSupplierProvider : DataAccess
    {
        static private PrecogsSupplierProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public PrecogsSupplierProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (PrecogsSupplierProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.PrecogsSuppliers.ProviderType));
                return _instance;
            }
        }
        public PrecogsSupplierProvider()
        {
            this.ConnectionString = Globals.Settings.RohsStatuss.ConnectionString;
        }

        #region Method Registrations

        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_ROHSStatus]
        /// </summary>
        public abstract List<PurchaseMethodDetails> DropDown();





        #endregion

    }
}
