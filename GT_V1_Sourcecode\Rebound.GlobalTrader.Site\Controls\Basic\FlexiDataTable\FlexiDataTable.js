Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.FlexiDataTable=function(n){Rebound.GlobalTrader.Site.Controls.FlexiDataTable.initializeBase(this,[n]);this.ControlType_FlexiDataTable=!0;this._intCurrentPageSize=10;this._intCurrentPage=1;this._intSortColumnIndex=0;this._intOriginalSortColumnIndex=0;this._strFilterExpression="";this._strSortExpression="";this._intStartRow=0;this._intEndRow=0;this._intTotalRecords=0;this._intSelectedIndex=-1;this._varSelectedValue="";this._blnShowHeader=!0;this._intDoubleClickTimeout=0;this._aryHeaderCellIDs=[];this._aryColumnAlignment=[];this._aryColumnWidth=[];this._aryColumnClientSortFormat=[];this._aryCurrentValues=[];this._arySelectedIndexes=[];this._aryValues=[];this._aryExtraData=[];this._blnEnabled=!0;this._intLastRowClicked=null;this._intCountSelected=0;this._aryAsyncDataToAdd=[];this._intAsyncRowsToAdd=0;this._intAsyncRowsAdded=0;this._blnColumnsResizing=!1;this._enmOriginalSortDirection=null;this._intHandleLeft=-1};Rebound.GlobalTrader.Site.Controls.FlexiDataTable.prototype={get_blnSelected:function(){return this._blnSelected},set_blnSelected:function(n){this._blnSelected!==n&&(this._blnSelected=n)},get_blnIsScrollable:function(){return this._blnIsScrollable},set_blnIsScrollable:function(n){this._blnIsScrollable!==n&&(this._blnIsScrollable=n)},get_intCurrentPageSize:function(){return this._intCurrentPageSize},set_intCurrentPageSize:function(n){this._intCurrentPageSize!==n&&(this._intCurrentPageSize=n)},get_intCurrentPage:function(){return this._intCurrentPage},set_intCurrentPage:function(n){this._intCurrentPage!==n&&(this._intCurrentPage=n)},get_blnFiltersOn:function(){return this._blnFiltersOn},set_blnFiltersOn:function(n){this._blnFiltersOn!==n&&(this._blnFiltersOn=n)},get_intSortColumnIndex:function(){return this._intSortColumnIndex},set_intSortColumnIndex:function(n){this._intSortColumnIndex!==n&&(this._intSortColumnIndex=n)},get_enmSortDirection:function(){return this._enmSortDirection},set_enmSortDirection:function(n){this._enmSortDirection!==n&&(this._enmSortDirection=n)},get_strFilterExpression:function(){return this._strFilterExpression},set_strFilterExpression:function(n){this._strFilterExpression!==n&&(this._strFilterExpression=n)},get_strSortExpression:function(){return this._strSortExpression},set_strSortExpression:function(n){this._strSortExpression!==n&&(this._strSortExpression=n)},get_intStartRow:function(){return this._intStartRow},set_intStartRow:function(n){this._intStartRow!==n&&(this._intStartRow=n)},get_intEndRow:function(){return this._intEndRow},set_intEndRow:function(n){this._intEndRow!==n&&(this._intEndRow=n)},get_intTotalRecords:function(){return this._intTotalRecords},set_intTotalRecords:function(n){this._intTotalRecords!==n&&(this._intTotalRecords=n)},get_aryHeaderCellIDs:function(){return this._aryHeaderCellIDs},set_aryHeaderCellIDs:function(n){this._aryHeaderCellIDs!==n&&(this._aryHeaderCellIDs=n)},get_aryColumnIsSortable:function(){return this._aryColumnIsSortable},set_aryColumnIsSortable:function(n){this._aryColumnIsSortable!==n&&(this._aryColumnIsSortable=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_tblHeader:function(){return this._tblHeader},set_tblHeader:function(n){this._tblHeader!==n&&(this._tblHeader=n)},get_blnAllowSelection:function(){return this._blnAllowSelection},set_blnAllowSelection:function(n){this._blnAllowSelection!==n&&(this._blnAllowSelection=n)},get_blnAllowMultipleSelection:function(){return this._blnAllowMultipleSelection},set_blnAllowMultipleSelection:function(n){this._blnAllowMultipleSelection!==n&&(this._blnAllowMultipleSelection=n)},get_aryCurrentValues:function(){return this._aryCurrentValues},set_aryCurrentValues:function(n){this._aryCurrentValues!==n&&(this._aryCurrentValues=n)},get_aryValues:function(){return this._aryValues},set_aryValues:function(n){this._aryValues!==n&&(this._aryValues=n)},get_intSelectedIndex:function(){return this._intSelectedIndex},set_intSelectedIndex:function(n){this._intSelectedIndex!==n&&(this._intSelectedIndex=n)},get_varSelectedValue:function(){return this._varSelectedValue},set_varSelectedValue:function(n){this._varSelectedValue!==n&&(this._varSelectedValue=n)},get_blnInsideDataListNugget:function(){return this._blnInsideDataListNugget},set_blnInsideDataListNugget:function(n){this._blnInsideDataListNugget!==n&&(this._blnInsideDataListNugget=n)},get_blnShowHeader:function(){return this._blnShowHeader},set_blnShowHeader:function(n){this._blnShowHeader!==n&&(this._blnShowHeader=n)},get_aryExtraData:function(){return this._aryExtraData},set_aryExtraData:function(n){this._aryExtraData!==n&&(this._aryExtraData=n)},get_aryColumnAlignment:function(){return this._aryColumnAlignment},set_aryColumnAlignment:function(n){this._aryColumnAlignment!==n&&(this._aryColumnAlignment=n)},get_aryColumnWidth:function(){return this._aryColumnWidth},set_aryColumnWidth:function(n){this._aryColumnWidth!==n&&(this._aryColumnWidth=n)},get_blnAllowDoubleClick:function(){return this._blnAllowDoubleClick},set_blnAllowDoubleClick:function(n){this._blnAllowDoubleClick!==n&&(this._blnAllowDoubleClick=n)},get_pnlScroll:function(){return this._pnlScroll},set_pnlScroll:function(n){this._pnlScroll!==n&&(this._pnlScroll=n)},get_pnlHandleFooter:function(){return this._pnlHandleFooter},set_pnlHandleFooter:function(n){this._pnlHandleFooter!==n&&(this._pnlHandleFooter=n)},get_blnAllowResize:function(){return this._blnAllowResize},set_blnAllowResize:function(n){this._blnAllowResize!==n&&(this._blnAllowResize=n)},get_rceResizing:function(){return this._rceResizing},set_rceResizing:function(n){this._rceResizing!==n&&(this._rceResizing=n)},get_blnAllowClientSideSorting:function(){return this._blnAllowClientSideSorting},set_blnAllowClientSideSorting:function(n){this._blnAllowClientSideSorting!==n&&(this._blnAllowClientSideSorting=n)},get_aryColumnClientSortFormat:function(){return this._aryColumnClientSortFormat},set_aryColumnClientSortFormat:function(n){this._aryColumnClientSortFormat!==n&&(this._aryColumnClientSortFormat=n)},addSortDataEvent:function(n){this.get_events().addHandler("SortData",n)},removeSortDataEvent:function(n){this.get_events().removeHandler("SortData",n)},onSortData:function(){var n=this.get_events().getHandler("SortData");n&&n(this,Sys.EventArgs.Empty)},addFilterDataEvent:function(n){this.get_events().addHandler("FilterData",n)},removeFilterDataEvent:function(n){this.get_events().removeHandler("FilterData",n)},onFilterData:function(){var n=this.get_events().getHandler("FilterData");n&&n(this,Sys.EventArgs.Empty)},addPageChangedEvent:function(n){this.get_events().addHandler("PageChange",n)},removePageChangedEvent:function(n){this.get_events().removeHandler("PageChange",n)},onPageChanged:function(){var n=this.get_events().getHandler("PageChange");n&&n(this,Sys.EventArgs.Empty)},addSelectedIndexChanged:function(n){this.get_events().addHandler("SelectedIndexChanged",n)},removeSelectedIndexChanged:function(n){this.get_events().removeHandler("SelectedIndexChanged",n)},onSelectedIndexChanged:function(){var n=this.get_events().getHandler("SelectedIndexChanged");n&&n(this,Sys.EventArgs.Empty)},addMultipleSelectionChanged:function(n){this.get_events().addHandler("MultipleSelectionChanged",n)},removeMultipleSelectionChanged:function(n){this.get_events().removeHandler("MultipleSelectionChanged",n)},onMultipleSelectionChanged:function(){var n=this.get_events().getHandler("MultipleSelectionChanged");n&&n(this,Sys.EventArgs.Empty)},addDoubleClick:function(n){this.get_events().addHandler("DoubleClick",n)},removeDoubleClick:function(n){this.get_events().removeHandler("DoubleClick",n)},onDoubleClick:function(){var n=this.get_events().getHandler("DoubleClick");n&&n(this,Sys.EventArgs.Empty)},addResize:function(n){this.get_events().addHandler("Resize",n)},removeResize:function(n){this.get_events().removeHandler("Resize",n)},onResize:function(){var n=this.get_events().getHandler("Resize");n&&n(this,Sys.EventArgs.Empty)},addAddAsyncRow:function(n){this.get_events().addHandler("AddAsyncRow",n)},removeAddAsyncRow:function(n){this.get_events().removeHandler("AddAsyncRow",n)},onAddAsyncRow:function(){var n=this.get_events().getHandler("AddAsyncRow");n&&n(this,Sys.EventArgs.Empty)},addAsyncDataAdditionComplete:function(n){this.get_events().addHandler("AsyncDataAdditionComplete",n)},removeAsyncDataAdditionComplete:function(n){this.get_events().removeHandler("AsyncDataAdditionComplete",n)},onAsyncDataAdditionComplete:function(){var n=this.get_events().getHandler("AsyncDataAdditionComplete");n&&n(this,Sys.EventArgs.Empty)},addClientSortDataEvent:function(n){this.get_events().addHandler("ClientSortData",n)},removeClientSortDataEvent:function(n){this.get_events().removeHandler("ClientSortData",n)},onClientSortData:function(){var n=this.get_events().getHandler("ClientSortData");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FlexiDataTable.callBaseMethod(this,"initialize");this.setupColumns();this._blnShowHeader&&$addHandler(window,"resize",Function.createDelegate(this,this.resizeColumns));this._blnAllowResize&&(this._pnlScroll.style.width="100%",this.setupResizing());this._enmOriginalSortDirection=this._enmSortDirection;this._intOriginalSortColumnIndex=this._intSortColumnIndex;this.calculateStartAndEndRow()},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._tbl&&(this.unhookTableRowEvents(),$clearHandlers(this._tbl)),this._blnShowHeader&&$clearHandlers(window),this._enmSortDirection=null,this._enmOriginalSortDirection=null,this._tbl=null,this._tblHeader=null,this._pnlScroll=null,this._pnlHandleFooter=null,this._rceResizing=null,this._blnSelected=null,this._blnIsScrollable=null,this._intCurrentPageSize=null,this._intCurrentPage=null,this._blnFiltersOn=null,this._intSortColumnIndex=null,this._intOriginalSortColumnIndex=null,this._strFilterExpression=null,this._strSortExpression=null,this._intStartRow=null,this._intEndRow=null,this._intTotalRecords=null,this._blnAllowSelection=null,this._blnAllowMultipleSelection=null,this._intSelectedIndex=null,this._varSelectedValue=null,this._blnInsideDataListNugget=null,this._blnShowHeader=null,this._blnAllowDoubleClick=null,this._blnAllowResize=null,this._blnAllowClientSideSorting=null,this._aryHeaderCellIDs=null,this._aryColumnIsSortable=null,this._aryAsyncDataToAdd=null,this._aryColumnClientSortFormat=null,this._aryCurrentValues=null,this._aryValues=null,this._aryExtraData=null,this._aryColumnAlignment=null,this._aryColumnWidth=null,this._blnColumnsResizing=null,Rebound.GlobalTrader.Site.Controls.FlexiDataTable.callBaseMethod(this,"dispose"),this.isDisposed=!0)},setupColumns:function(){for(var t,n=0,i=this._aryHeaderCellIDs.length;n<i;n++)this._aryColumnIsSortable[n]&&(t=$get(this._aryHeaderCellIDs[n]),Sys.UI.DomElement.addCssClass(t,"sortable"),t.setAttribute("onmouseover",String.format("$find('{0}').showColumnMouseOver({1});",this._element.id,n)),t.setAttribute("onmouseout",String.format("$find('{0}').showColumnMouseOut({1});",this._element.id,n)),t.setAttribute("onclick",String.format("$find('{0}').doSort({1});",this._element.id,n)),this.formatColumn(n),t=null)},formatColumns:function(){for(var n=0,t=this._aryHeaderCellIDs.length;n<t;n++)this._aryColumnIsSortable[n]&&this.formatColumn(n)},formatColumn:function(n){this.clearColumnSortDirection(n);n==this._intSortColumnIndex&&this.setColumnSortDirection(n)},setInitialSortDirection:function(n){this._enmSortDirection=n;this._enmOriginalSortDirection=n;this._intOriginalSortColumnIndex=this._intSortColumnIndex;this.setColumnSortDirection(this._intSortColumnIndex)},resetInitialSortDirection:function(){this._enmSortDirection=this._enmOriginalSortDirection;this._intSortColumnIndex=this._intOriginalSortColumnIndex;for(var n=0,t=this._aryHeaderCellIDs.length;n<t;n++)this.clearColumnSortDirection(n);this.setColumnSortDirection(this._intSortColumnIndex)},prevPage:function(){this.changePage(this.limitPage(this._intCurrentPage-1))},nextPage:function(){this.changePage(this.limitPage(this._intCurrentPage+1))},changePage:function(n){this._intCurrentPage=n;this.calculateStartAndEndRow();this.calculatePages();this.onPageChanged()},calculateStartAndEndRow:function(){this.calculateStartRow();this.calculateEndRow()},calculateStartRow:function(){this._intStartRow=this._intCurrentPageSize==0?0:Math.max(this._intCurrentPageSize*(this._intCurrentPage-1),0)+1},calculateEndRow:function(){this._intEndRow=this._intCurrentPageSize==0?this._intTotalRecords:this._intStartRow+this._intCurrentPageSize-1},calculatePages:function(){this._intTotalPages=this._intCurrentPageSize==0?1:Math.ceil(this._intTotalRecords/this._intCurrentPageSize);this._intCurrentPage=this.limitPage(this._intCurrentPage)},limitPage:function(n){return Math.max(Math.min(n,this._intTotalPages),1)},clearTable:function(){this.unhookTableRowEvents();for(var n=this._tbl.rows.length-1;n>=0;n--)this._tbl.deleteRow(n);Array.clear(this._aryCurrentValues);Array.clear(this._aryValues);Array.clear(this._aryExtraData);Array.clear(this._arySelectedIndexes);this._intSelectedIndex=-1;this._varSelectedValue="";this.resetColumnWidths()},resetColumnWidths:function(){for(var t,n=0,i=this._aryHeaderCellIDs.length;n<i;n++)t="",this._aryColumnWidth[n]!=""&&(t="width:"+this._aryColumnWidth[n]),$get(this._aryHeaderCellIDs[n]).setAttribute("style",t),t=null},doFilter:function(){$R_FN.showElement(this._pnlLoading,!0);this._intCurrentPage=1;this._intStartRow=1;this.calculateEndRow();this.onFilterData();this.calculatePages()},showColumnMouseOver:function(n){var t=$get(this._aryHeaderCellIDs[n]);Sys.UI.DomElement.addCssClass(t,"highlight");t=null},showColumnMouseOut:function(n){var t=$get(this._aryHeaderCellIDs[n]);Sys.UI.DomElement.removeCssClass(t,"highlight");t=null},doSort:function(n){n==this._intSortColumnIndex?this.swapSortDirection():this._enmSortDirection=$R_ENUM$SortColumnDirection.ASC;this._intSortColumnIndex=n;this.formatColumns();this._blnAllowClientSideSorting?($R_TABLESORT.doSort(this._element.id,this._intSortColumnIndex),this.onClientSortData()):(this._intCurrentPage=1,this.calculatePages(),$R_FN.showElement(this._pnlLoading,!0),this.onSortData())},getID:function(n,t){return String.format("{0}_{1}{2}",this._tbl.id,n,t)},finishResizeColumns:function(){var t,n,i;if(!this._blnColumnsResizing&&(t=Sys.UI.DomElement.getBounds(this._element),t.width!=0)&&this._blnShowHeader){if(this._blnAllowResize&&(this.calculateResizeHandleLeft(),this.repositionResizingHandle()),this._tbl.rows.length==0){this._tblHeader.style.width="100%";return}for(this._blnColumnsResizing=!0,$R_FN.setWidthFromOneToAnother(this._tbl,this._tblHeader),n=0,i=this._aryHeaderCellIDs.length;n<i;n++){var r=this._tbl.rows[0]?this._tbl.rows[0].cells[n]:null,u=this._tblHeader.rows[0]?this._tblHeader.rows[0].cells[n]:null,f=this._blnInsideDataListNugget?-7:-8;n==0&&(Sys.Browser.agent==Sys.Browser.Safari||Sys.Browser.agent==Sys.Browser.Opera)&&(f+=1);r&&u&&n<i-1&&$R_FN.setWidthFromOneToAnother(r,u,f);r=null;u=null}t=null;this._blnColumnsResizing=!1}},resizeColumns:function(){setTimeout(Function.createDelegate(this,this.finishResizeColumns),5)},clearSetColumnWidths:function(){for(var t,n=0,i=this._aryHeaderCellIDs.length;n<i;n++)t=this._tblHeader.rows[0]?this._tblHeader.rows[0].cells[n]:null,t&&(t.style.width=""),t=null},addRow:function(n,t,i,r,u){var o,s,e,c,f,h,l;if(n){for(u||(u=""),o=this._tbl.insertRow(-1),s=o.rowIndex,o.setAttribute("bui_tableRowIndex",s),this._aryValues[s]=t,e=0,c=n.length;e<c;e++){if(e>=this._aryHeaderCellIDs.length)break;f=document.createElement("td");e==0&&(f.className="first");e==c-1&&(f.className="last");u!=""&&(f.className+=" "+u,e==0&&(f.className+=String.format(" {0}_First",u)),e==c-1&&(f.className+=String.format(" {0}_Last",u)));switch(this._aryColumnAlignment[e]){case $R_ENUM$HorizontalAlign.Center:f.className+=" alignCenter";break;case $R_ENUM$HorizontalAlign.Right:f.className+=" alignRight";break;case $R_ENUM$HorizontalAlign.Justify:f.className+=" alignJustify"}n[e]+="";h=n[e].toString().trim();(h.length==0||h=="undefined")&&(h="&nbsp;");f.innerHTML=h;f.setAttribute("bui_tableRowIndex",s);o.appendChild(f);s==0&&(f.style.width=this._tblHeader.rows[0].cells[e].style.width);f=null;h=null}l=this.get_element().id;this._blnAllowSelection&&($addHandler(o,"mouseup",Function.createDelegate(this,this.rowMouseUp)),$addHandler(o,"mousedown",Function.createDelegate(this,this.rowMouseDown)));Array.add(this._aryExtraData,r);o=null;i&&this.selectRow(s,!0);this._blnAllowResize&&this.repositionResizingHandle()}},addRowRowColor:function(n,t,i,r,u,f,e,o){var c,l,h,v,s,a,y;if(n){for(u||(u=""),c=this._tbl.insertRow(-1),l=c.rowIndex,c.setAttribute("bui_tableRowIndex",l),this._aryValues[l]=t,h=0,v=n.length;h<v;h++){if(h>=this._aryHeaderCellIDs.length)break;s=document.createElement("td");h==0&&(s.className="first");h==v-1&&(s.className="last");u!=""&&(s.className+=" "+u,h==0&&(s.className+=String.format(" {0}_First",u)),h==v-1&&(s.className+=String.format(" {0}_Last",u)));switch(this._aryColumnAlignment[h]){case $R_ENUM$HorizontalAlign.Center:s.className+=" alignCenter";break;case $R_ENUM$HorizontalAlign.Right:s.className+=" alignRight";break;case $R_ENUM$HorizontalAlign.Justify:s.className+=" alignJustify"}n[h]+="";a=n[h].toString().trim();(a.length==0||a=="undefined")&&(a="&nbsp;");s.innerHTML=a;s.setAttribute("bui_tableRowIndex",l);c.appendChild(s);l==0&&(s.style.width=this._tblHeader.rows[0].cells[h].style.width);s=null;a=null}y=this.get_element().id;this._blnAllowSelection&&($addHandler(c,"mouseup",Function.createDelegate(this,this.rowMouseUp)),$addHandler(c,"mousedown",Function.createDelegate(this,this.rowMouseDown)));Array.add(this._aryExtraData,r);c=null;i&&this.selectRow(l,!0);this._blnAllowResize&&this.repositionResizingHandle();e&&this.RowColor(l,o)}},RowColor:function(n,t){Sys.UI.DomElement.addCssClass(this._tbl.rows[n],t)},rowMouseDown:function(){$R_FN.clearAllSelections()},rowMouseUp:function(n){var t=$R_FN.findParentElementOfType(n.target,"tr"),i=Number.parseInvariant(t.getAttribute("bui_tableRowIndex").toString());$R_FN.clearAllSelections();this.selectRow(i,!1,!1,n.ctrlKey,n.shiftKey);t=null},selectRow:function(n,t,i,r,u){var e,o,f;if(this._blnEnabled){if(this._blnAllowDoubleClick){if(this._blnInDoubleClick&&n==this._intLastRowIndexClicked){this._intLastRowIndexClicked=n;this.endDoubleClickTest();this._varSelectedDoubleClickValue=this._aryValues[n];this.onDoubleClick();return}this._intLastRowIndexClicked=n;this.endDoubleClickTest();this._intDoubleClickTimeout=setTimeout(String.format("$find('{0}').endDoubleClickTest();",this._element.id),$R_DOUBLE_CLICK_TIME);this._blnInDoubleClick=!0}if(this._blnAllowMultipleSelection){if(r&&!u)Array.contains(this._arySelectedIndexes,n)?this.doUnselectRow(n):this.doSelectRow(n,!0);else if(u&&!r)for(this.clearSelection(),e=this._intLastRowClicked>n?n:this._intLastRowClicked,o=this._intLastRowClicked>n?this._intLastRowClicked:n,f=e;f<=o;f++)Array.contains(this._arySelectedIndexes,f)||this.doSelectRow(f,!0);else this.clearSelection(),this.doSelectRow(n,!0);i||this.onMultipleSelectionChanged()}else{if(n==this._intSelectedIndex)return;this.clearSelection();this.doSelectRow(n);i||setTimeout(Function.createDelegate(this,this.onSelectedIndexChanged),0);t&&this.scrollToRowAfterPause(n)}this._intLastRowClicked=n}},endDoubleClickTest:function(){clearTimeout(this._intDoubleClickTimeout);this._blnInDoubleClick=!1},getSelectedExtraData:function(n){return typeof n=="undefined"&&(n=this._intSelectedIndex),this._aryExtraData[n]},clearSelection:function(n){Array.clear(this._aryCurrentValues);Array.clear(this._arySelectedIndexes);this._intSelectedIndex=null;this._varSelectedValue=null;for(var t=0,i=this._tbl.rows.length;t<i;t++)Sys.UI.DomElement.removeCssClass(this._tbl.rows[t],"selected");this._intCountSelected=0;n&&(this._blnAllowMultipleSelection?this.onMultipleSelectionChanged():this.onSelectedIndexChanged())},selectAllRows:function(n){if(this._blnAllowMultipleSelection){this.clearSelection();for(var t=0,i=this._tbl.rows.length;t<i;t++)this.doSelectRow(t,!0);n&&this.onMultipleSelectionChanged()}},doSelectRow:function(n,t){t?(Array.add(this._aryCurrentValues,this._aryValues[n]),Array.add(this._arySelectedIndexes,n)):(this._intSelectedIndex=n,this._varSelectedValue=this._aryValues[n]);Sys.UI.DomElement.addCssClass(this._tbl.rows[n],"selected");Sys.UI.DomElement.removeCssClass(this._tbl.rows[n],"hover");this._intCountSelected+=1},doUnselectRow:function(n){Array.remove(this._aryCurrentValues,this._aryValues[n]);Array.remove(this._arySelectedIndexes,n);Sys.UI.DomElement.removeCssClass(this._tbl.rows[n],"selected");this._intCountSelected-=1},show:function(n){$R_FN.showElement(this.get_element(),n)},enable:function(n){this._blnEnabled=n},getSelectedCellValue:function(n,t){var i,r;if(typeof t=="undefined"&&(t=this._intSelectedIndex),i=this._tbl.rows[t],i)return n>i.cells.length-1?void 0:(r=i.cells[n].innerHTML,i=null,r)},countRows:function(){return this._tbl?this._tbl.rows?this._tbl.rows.length:0:0},swapSortDirection:function(){this._enmSortDirection=this._enmSortDirection==$R_ENUM$SortColumnDirection.ASC?$R_ENUM$SortColumnDirection.DESC:$R_ENUM$SortColumnDirection.ASC},clearColumnSortDirection:function(n){Sys.UI.DomElement.removeCssClass($get(this._aryHeaderCellIDs[n]),"sortASC");Sys.UI.DomElement.removeCssClass($get(this._aryHeaderCellIDs[n]),"sortDESC")},setColumnSortDirection:function(n){this._intSortColumnIndex=Number.parseInvariant(n.toString());Sys.UI.DomElement.addCssClass($get(this._aryHeaderCellIDs[n]),String.format("sort{0}",this._enmSortDirection==$R_ENUM$SortColumnDirection.ASC?"ASC":"DESC"))},removeRow:function(n){this._tbl.rows[n]&&$clearHandlers(this._tbl.rows[n]);this._tbl.deleteRow(n);Array.removeAt(this._aryCurrentValues,n);Array.removeAt(this._aryValues,n);Array.removeAt(this._aryExtraData,n);Array.removeAt(this._arySelectedIndexes,n)},scrollToRow:function(n){n<0&&(n=0);var t=null;(n<this._tbl.rows.length&&(t=this._tbl.rows[n]),t)&&(this._pnlScroll.scrollTop+=Sys.UI.DomElement.getBounds(t).y-Sys.UI.DomElement.getBounds(this._pnlScroll).y,t=null,this._blnAllowResize&&this.repositionResizingHandle())},scrollToRowAfterPause:function(n){setTimeout(String.format("$find('{0}').scrollToRow({1});",this._element.id,n),10)},changeSelectability:function(n){this._blnAllowSelection=n;n?Sys.UI.DomElement.addCssClass(this._tbl,"dataTableSelectable"):Sys.UI.DomElement.removeCssClass(this._tbl,"dataTableSelectable")},changeMultipleSelectability:function(n){this._blnAllowMultipleSelection=n;n?Sys.UI.DomElement.addCssClass(this._tbl,"dataTableSelectable"):Sys.UI.DomElement.removeCssClass(this._tbl,"dataTableSelectable")},setupResizing:function(){if(this._blnAllowResize){if(!this._rceResizing._initialized){setTimeout(Function.createDelegate(this,this.setupResizing),10);return}$R_FN.showElement(this._rceResizing._handle,!0);setTimeout(Function.createDelegate(this,this.repositionResizingHandle),500);$addHandler(this._tbl,"click",Function.createDelegate(this,this.repositionResizingHandle));this._rceResizing.add_resizing(Function.createDelegate(this,this.tableResizing));this._rceResizing.add_resizebegin(Function.createDelegate(this,this.tableResizing));this._rceResizing.add_resize(Function.createDelegate(this,this.tableResized))}},tableResized:function(){this.onResize()},tableResizing:function(n){this._blnAllowResize&&this._rceResizing&&(n.get_element().style.width="",this.repositionResizingHandle(),$R_FN.clearAllSelections())},repositionResizingHandle:function(){if(this._blnAllowResize&&this._rceResizing&&this._intHandleLeft){this._intHandleLeft<=0&&this.calculateResizeHandleLeft();var n=Number.parseInvariant(this._rceResizing.get_Size().height.toString());Sys.Browser.agent==Sys.Browser.Firefox&&(n+=Math.round(this._pnlScroll.scrollTop));Sys.UI.DomElement.setLocation(this._rceResizing._handle,this._intHandleLeft,n);n=null}},calculateResizeHandleLeft:function(){this._blnAllowResize&&this._rceResizing&&(this._intHandleLeft=Math.round((Sys.UI.DomElement.getBounds(this._pnlHandleFooter).width-Sys.UI.DomElement.getBounds(this._rceResizing._handle).width)/2)-15)},unhookTableRowEvents:function(){if(this._blnAllowSelection)for(var n=0,t=this._tbl.rows;n<t;n++)this._tbl.rows[n]&&$clearHandlers(this._tbl.rows[n])},resetAsyncData:function(){this._aryAsyncDataToAdd=[];this._intAsyncRowsToAdd=0;this._intAsyncRowsAdded=0},addDataForAsyncAddition:function(n){n&&Array.addRange(this._aryAsyncDataToAdd,n);this._intAsyncRowsToAdd=this._aryAsyncDataToAdd.length},startAddingRowsAsync:function(){this._intAsyncRowsAdded=0;this._aryAsyncDataToAdd||(this._aryAsyncDataToAdd=[]);this._intAsyncRowsToAdd=this._aryAsyncDataToAdd.length;this._aryAsyncDataToAdd.length>0?this.addRowAsync():this.finishedAddingRowsAsync()},addRowAsync:function(){if(this._intAsyncRowsAdded<this._intAsyncRowsToAdd){this._intAsyncRowsAdded==0&&this.resizeColumns();this._objAsyncDataRow=this._aryAsyncDataToAdd[this._intAsyncRowsAdded];this.onAddAsyncRow();this._intAsyncRowsAdded+=1;var n=this._element.id,t=function(){$find(n).addRowAsync()};setTimeout(t,0)}else this.finishedAddingRowsAsync()},finishedAddingRowsAsync:function(){this.resetAsyncData();this.resizeColumns();this.onAsyncDataAdditionComplete()}};Rebound.GlobalTrader.Site.Controls.FlexiDataTable.registerClass("Rebound.GlobalTrader.Site.Controls.FlexiDataTable",Sys.UI.Control,Sys.IDisposable);