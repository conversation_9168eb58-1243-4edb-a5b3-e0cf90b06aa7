﻿
/****** Object:  View [dbo].[VW_GetOGELLine]    Script Date: 7/16/2024 11:26:23 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

ALTER VIEW [dbo].[VW_GetOGELLine] 
AS
Select  tbinvline.InvoiceLineId, tbso.salesorderId, tbso.salesordernumber,tbsoline.SOSerialNo,tbinv.AirWayBill,
tbsoline.ECCNCode as 'CommodityCode',tbsoline.ECCNCode,-- eccn.Notes,
tbcl.OGELNumber ,  tbApprvdtls.MilitaryUseNo as OGEL_MilitaryUse, tbApprvdtls.EndDestinationCountryNo as OGEL_EndDestinationCountry,tbso.clientno,tbinv.InvoiceNumber,tbso.DateOrdered,tbsoline.DatePromised
from dbo.tbSalesOrder tbso join dbo.tbSalesOrderLine tbsoline on tbso.salesorderid = tbsoline.salesorderno
left join tbSO_ExportApprovalStatusOGEL tbApprvhdr on tbso.SalesOrderId = tbApprvhdr.SalesOrderNo and tbsoline.SalesOrderLineId = tbApprvhdr.SalesOrderLineNo
left join tbSO_ExportApprovalDetails tbApprvdtls on tbApprvhdr.ExportApprovalId = tbApprvdtls.ExportApprovalNo
left join dbo.tbinvoice tbinv on tbso.salesorderid = tbinv.salesorderno
left join dbo.tbInvoiceLine tbinvline on tbinv.invoiceid = tbinvline.invoiceno and tbsoline.SalesOrderLineId = tbinvline.SalesOrderLineNo
join dbo.tbclient tbcl on tbso.clientno = tbcl.clientid
join tbCountry tbc on tbc.CountryId = tbApprvdtls.EndDestinationCountryNo
--left join dbo.tbeccn eccn on eccn.ECCNCode=tbsoline.ECCNCode
--where tbc.OGEL =1 
where
(CASE WHEN ISNULL(tbApprvhdr.OGELNumber,0)>0 THEN 1           
ELSE CASE WHEN (ISNULL(tbc.OGEL,0)>0) AND [dbo].[ufn_OGELGetFromCountry](tbApprvhdr.SalesOrderLineNo)=1 THEN 1 ELSE 0 END END)=1
GO


