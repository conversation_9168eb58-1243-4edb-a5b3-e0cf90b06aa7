Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.initializeBase(this,[n]);this._intBOMID=-1;this._intCountTab=0;this._intCountStock=0;this._isEnable=!1;this._intCustomerRequirementID=-1;this._blnRequirementClosed=!1;this._intCompanyID=-1;this._hasSourcingResult=!1;this._blnRequirementReleased=!1;this._allExistInSourcingResult=!1;this._intSelectedLineNo=0;this._BomCode="";this._BomName="";this._BomCompanyName="";this._BomCompanyNo=0;this._SalesManNo=0;this._SalesManName=0;this._lineLength=0;this._isRequestToPurchaseQuote=!1;this._blnAllHasProduct=!1;this._blnAllHasDelDate=!1;this._blnAllItemHasDelDate=!1;this._blnAllItemHasProduct=!1;this._blnCanRelease=!1;this._isClosed=!1;this._blnCanRecal=!0;this._CustReqNo=-1;this._blnCanNoBid=!0;this._isNoBid=!1;this._ReqIds=[];this._ReqNos=[];this._BOMNo=-1;this._UpdateByPH=-1;this._ReqSalesman=-1;this._intContact2No=-1;this._RequestToPOHubBy=-1;this._RequestToPOHubByNo=-1;this._SupportTeamMemberNo=null;this._blnRelease=!1;this._blnCanApplyPartWatch=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_tblStock:function(){return this._tblStock},set_tblStock:function(n){this._tblStock!==n&&(this._tblStock=n)},get_ctlTabStrip:function(){return this._ctlTabStrip},set_ctlTabStrip:function(n){this._ctlTabStrip!==n&&(this._ctlTabStrip=n)},get_ctlTabStock:function(){return this._ctlTabStock},set_ctlTabStock:function(n){this._ctlTabStock!==n&&(this._ctlTabStock=n)},get_IsEnable:function(){return this._isEnable},set_isEnable:function(n){this._isEnable=n},get_ibtnRelease:function(){return this._ibtnRelease},set_ibtnRelease:function(n){this._ibtnRelease!==n&&(this._ibtnRelease=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnUnRelease:function(){return this._ibtnUnRelease},set_ibtnUnRelease:function(n){this._ibtnUnRelease!==n&&(this._ibtnUnRelease=n)},get_pnlLineDetail:function(){return this._pnlLineDetail},set_pnlLineDetail:function(n){this._pnlLineDetail!==n&&(this._pnlLineDetail=n)},get_pnlLoadingLineDetail:function(){return this._pnlLoadingLineDetail},set_pnlLoadingLineDetail:function(n){this._pnlLoadingLineDetail!==n&&(this._pnlLoadingLineDetail=n)},get_pnlLineDetailError:function(){return this._pnlLineDetailError},set_pnlLineDetailError:function(n){this._pnlLineDetailError!==n&&(this._pnlLineDetailError=n)},get_ibtnNoBid:function(){return this._ibtnNoBid},set_ibtnNoBid:function(n){this._ibtnNoBid!==n&&(this._ibtnNoBid=n)},get_ibtnRecallNoBid:function(){return this._ibtnRecallNoBid},set_ibtnRecallNoBid:function(n){this._ibtnRecallNoBid!==n&&(this._ibtnRecallNoBid=n)},get_ibtnNote:function(){return this._ibtnNote},set_ibtnNote:function(n){this._ibtnNote!==n&&(this._ibtnNote=n)},get_ibtnImportSrcReslt:function(){return this._ibtnImportSrcReslt},set_ibtnImportSrcReslt:function(n){this._ibtnImportSrcReslt!==n&&(this._ibtnImportSrcReslt=n)},get_ibtnApplyPartwatch:function(){return this._ibtnApplyPartwatch},set_ibtnApplyPartwatch:function(n){this._ibtnApplyPartwatch!==n&&(this._ibtnApplyPartwatch=n)},get_ibtnRemovePartwatch:function(){return this._ibtnRemovePartwatch},set_ibtnRemovePartwatch:function(n){this._ibtnRemovePartwatch!==n&&(this._ibtnRemovePartwatch=n)},get_ibtnExportToExcel:function(){return this._ibtnExportToExcel},set_ibtnExportToExcel:function(n){this._ibtnExportToExcel!==n&&(this._ibtnExportToExcel=n)},get_ibtnHubImportSR:function(){return this._ibtnHubImportSR},set_ibtnHubImportSR:function(n){this._ibtnHubImportSR!==n&&(this._ibtnHubImportSR=n)},addStartGetData:function(n){this.get_events().addHandler("StartGetData",n)},removeStartGetData:function(n){this.get_events().removeHandler("StartGetData",n)},onStartGetData:function(){var n=this.get_events().getHandler("StartGetData");n&&n(this,Sys.EventArgs.Empty)},addGotDataOK:function(n){this.get_events().addHandler("GotDataOK",n)},removeGotDataOK:function(n){this.get_events().removeHandler("GotDataOK",n)},onGotDataOK:function(){var n=this.get_events().getHandler("GotDataOK");n&&n(this,Sys.EventArgs.Empty)},addPartSelected:function(n){this.get_events().addHandler("PartSelected",n)},removePartSelected:function(n){this.get_events().removeHandler("PartSelected",n)},onPartSelected:function(){var n=this.get_events().getHandler("PartSelected");n&&n(this,Sys.EventArgs.Empty)},addCallBeforeRelease:function(n){this.get_events().addHandler("CallBeforeRelease",n)},removeCallBeforeRelease:function(n){this.get_events().removeHandler("CallBeforeRelease",n)},onCallBeforeRelease:function(){var n=this.get_events().getHandler("CallBeforeRelease");n&&n(this,Sys.EventArgs.Empty)},addRefereshAfterRelease:function(n){this.get_events().addHandler("RefereshAfterRelease",n)},removeRefereshAfterRelease:function(n){this.get_events().removeHandler("RefereshAfterRelease",n)},onRefereshAfterRelease:function(){var n=this.get_events().getHandler("RefereshAfterRelease");n&&n(this,Sys.EventArgs.Empty)},addImportSourcingResultSuccess:function(n){this.get_events().addHandler("ImportSourcingResultSuccess",n)},removeImportSourcingResultSuccess:function(n){this.get_events().removeHandler("ImportSourcingResultSuccess",n)},onImportSourcingResultSuccess:function(){var n=this.get_events().getHandler("ImportSourcingResultSuccess");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){$("#As6081WarningMsg").hide();$("#dvtxt").html("");Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tblStock.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._ibtnRelease&&($R_IBTN.addClick(this._ibtnRelease,Function.createDelegate(this,this.showReleaseForm)),this._frmConfirm=$find(this._aryFormIDs[0]),this._frmConfirm._BomCode=this._BomCode,this._frmConfirm._BomName=this._BomName,this._frmConfirm._BomCompanyName=this._BomCompanyName,this._frmConfirm._BomCompanyNo=this._BomCompanyNo,this._frmConfirm._SalesManName=this._SalesManName,this._frmConfirm._CustReqNo=this._CustReqNo,this._frmConfirm.addCancel(Function.createDelegate(this,this.hideConfirmForm)),this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveCeaseComplete)),this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm)));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[1]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)),this._frmAdd.addNotConfirmed(Function.createDelegate(this,this.hideAddForm)),this._frmAdd._intBOMID=this._intBOMID);this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[2]),this._frmDelete.addCancel(Function.createDelegate(this,this.hideDeleteForm)),this._frmDelete._intBOMID=this._intBOMID,this._frmDelete._intCustomerRequirementID=this._intCustomerRequirementID,this._frmDelete.addSaveComplete(Function.createDelegate(this,this.DeleteComplete)),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.hideDeleteForm)));this._ibtnUnRelease&&($R_IBTN.addClick(this._ibtnUnRelease,Function.createDelegate(this,this.showUnReleaseForm)),this._frmUnRelease=$find(this._aryFormIDs[3]),this._frmUnRelease.addCancel(Function.createDelegate(this,this.hideUnReleaseForm)),this._frmUnRelease._intBOMID=this._intBOMID,this._frmUnRelease._intCustomerRequirementID=this._intCustomerRequirementID,this._frmUnRelease.addSaveComplete(Function.createDelegate(this,this.UnReleaseComplete)),this._frmUnRelease.addNotConfirmed(Function.createDelegate(this,this.hideUnReleaseForm)));this._ibtnNoBid&&($R_IBTN.addClick(this._ibtnNoBid,Function.createDelegate(this,this.showNoBidConfirmForm)),this._frmNoBidConfirm=$find(this._aryFormIDs[4]),this._frmNoBidConfirm._BomCode=this._BomCode,this._frmNoBidConfirm._BomName=this._BomName,this._frmNoBidConfirm._BomCompanyName=this._BomCompanyName,this._frmNoBidConfirm._BomCompanyNo=this._BomCompanyNo,this._frmNoBidConfirm._SalesManNo=this._SalesManNo,this._frmNoBidConfirm._SalesManName=this._SalesManName,this._frmNoBidConfirm._CustReqNo=this._CustReqNo,this._frmNoBidConfirm.addCancel(Function.createDelegate(this,this.hideNoBidConfirmForm)),this._frmNoBidConfirm.addSaveComplete(Function.createDelegate(this,this.saveNoBidConfirmComplete)),this._frmNoBidConfirm.addNotConfirmed(Function.createDelegate(this,this.hideNoBidConfirmForm)));this._ibtnRecallNoBid&&($R_IBTN.addClick(this._ibtnRecallNoBid,Function.createDelegate(this,this.showRecallNoBidConfirmForm)),this._frmRecallNoBidConfirm=$find(this._aryFormIDs[4]),this._frmRecallNoBidConfirm._BomCode=this._BomCode,this._frmRecallNoBidConfirm._BomName=this._BomName,this._frmRecallNoBidConfirm._BomCompanyName=this._BomCompanyName,this._frmRecallNoBidConfirm._BomCompanyNo=this._BomCompanyNo,this._frmRecallNoBidConfirm._SalesManNo=this._SalesManNo,this._frmRecallNoBidConfirm._SalesManName=this._SalesManName,this._frmRecallNoBidConfirm._CustReqNo=this._CustReqNo,this._frmRecallNoBidConfirm.addCancel(Function.createDelegate(this,this.hideRecallNoBidConfirmForm)),this._frmRecallNoBidConfirm.addSaveComplete(Function.createDelegate(this,this.saveRecallNoBidConfirmComplete)),this._frmRecallNoBidConfirm.addNotConfirmed(Function.createDelegate(this,this.hideRecallNoBidConfirmForm)));this._ibtnNote&&($R_IBTN.addClick(this._ibtnNote,Function.createDelegate(this,this.showExpediteNoteForm)),this._frmAddExpediteNote=$find(this._aryFormIDs[5]),this._frmAddExpediteNote.addCancel(Function.createDelegate(this,this.cancelAddExpediteNoteForm)),this._frmAddExpediteNote.addSaveComplete(Function.createDelegate(this,this.saveAddExpediteNoteComplete)));this._ibtnApplyPartwatch&&($R_IBTN.addClick(this._ibtnApplyPartwatch,Function.createDelegate(this,this.showApplyPartwatchForm)),this._frmApplyPartwatch=$find(this._aryFormIDs[7]),this._frmApplyPartwatch.addCancel(Function.createDelegate(this,this.hideApplyPartwatchForm)),this._frmApplyPartwatch._intBOMID=this._intBOMID,this._frmApplyPartwatch._intCustomerRequirementID=this._intCustomerRequirementID,this._frmApplyPartwatch.addSaveComplete(Function.createDelegate(this,this.saveApplyPartwatchConfirmComplete)),this._frmApplyPartwatch.addNotConfirmed(Function.createDelegate(this,this.hideApplyPartwatchForm)));this._ibtnRemovePartwatch&&($R_IBTN.addClick(this._ibtnRemovePartwatch,Function.createDelegate(this,this.showRemovePartwatchForm)),this._frmRemovePartwatch=$find(this._aryFormIDs[8]),this._frmRemovePartwatch.addCancel(Function.createDelegate(this,this.hideRemovePartwatchForm)),this._frmRemovePartwatch._intBOMID=this._intBOMID,this._frmRemovePartwatch._intCustomerRequirementID=this._intCustomerRequirementID,this._frmRemovePartwatch.addSaveComplete(Function.createDelegate(this,this.saveRemovePartwatchConfirmComplete)),this._frmRemovePartwatch.addNotConfirmed(Function.createDelegate(this,this.hideRemovePartwatchForm)));this._ibtnExportToExcel&&$R_IBTN.addClick(this._ibtnExportToExcel,Function.createDelegate(this,this.exportToExcel));this._ibtnHubImportSR&&(this._frmHubImportSR=$find(this._aryFormIDs[6]),this._frmHubImportSR.addCancel(Function.createDelegate(this,this.cancelHubImportSRForm)),$R_IBTN.addClick(this._ibtnHubImportSR,Function.createDelegate(this,this.showHubImportSRForm)));this.getData();document.getElementById("myplasegrde").addEventListener("click",Function.createDelegate(this,this.getPartDetaildata));document.getElementById("closePoppartdetails").addEventListener("click",Function.createDelegate(this,this.hidpartdetaildive))},dispose:function(){this.isDisposed||(this._frmTransfer&&this._frmTransfer.dispose(),this._ctlTabStrip&&this._ctlTabStrip.dispose(),this._ctlTabStock&&this._ctlTabStock.dispose(),this._tblStock&&this._tblStock.dispose(),this._frmConfirm&&this._frmConfirm.dispose(),this._frmHubImportSR&&this._frmHubImportSR.dispose(),this._intBOMID=null,this._ctlTabStrip=null,this._tblStock=null,this._ibtnExportCSV=null,this._pnlSummary=null,this._ibtnRelease=null,this._intCustomerRequirementID=null,this._blnRequirementClosed=null,this._blnRequirementReleased=null,this._blnPOHub=null,this._ibtnAdd=null,this._intCompanyID=null,this._intSelectedLineNo=null,this._blnAllHasDelDate=null,this._blnAllItemHasDelDate=null,this._blnAllHasProduct=null,this._blnAllItemHasProduct=null,this._blnCanRelease=null,this._isClosed=null,this._ibtnNoBid=null,this._isNoBid=null,this._ibtnRecallNoBid=null,this._RequestToPOHubByNo=null,this._ReqSalesman=null,this._SupportTeamMemberNo=null,this._ibtnImportSrcReslt=null,this._ibtnRemovePartwatch=null,this._ibtnExportToExcel=null,this._ibtnApplyPartwatch=null,this._blnCanApplyPartWatch=null,this._ibtnHubImportSR=null,Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("GetData");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onStartGetData()},getDataOK:function(n){var i=n._result,f,e,t,o,s,a,r,h,c;if(this._tblStock.clearTable(),this.showLoading(!1),this._intCustomerRequirementID=-1,this._blnRequirementReleased=!1,this._intCompanyID=-1,this.enableButtons(!1),this._ctlTabStock.addCountToTitle(0),this.showLoading(!1),i.Items){for(e=!1,r=0;r<i.Items.length;r++){t=i.Items[r];this._lineLength=i.Items.length;this._BomCode=t.BOMCode;this._BomName=t.BOMFullName;this._BomCompanyName=t.Company;this._BomCompanyNo=t.CompanyNo;this._SalesManNo=t.Salesman;this._SalesManName=t.SalesmanName;this._RequestToPOHubBy=t.RequestToPOHubBy;this._UpdateByPH=t.UpdateByPH;var v=t.IsRequestToPurchaseQuote&&!this._isClosed,l="No",u="cusReqMainPart";t.Alt||(u="cusReqMainPart");t.Released&&(u="readyToShip");t.Released==!1&&t.HasSourcingResult==!0&&(u="allocated");this._blnPOHub==!0&&t.PriceIssueBuyAndSell==!0&&(u="notReadyToShip",e=!0);t.PartWatchHUBIPO==!0&&(l="Yes");this._allExistInSourcingResult==!1&&(this._allExistInSourcingResult=t.HasSourcingResult);this.disableItemAddButton(!t.IsRequestToPurchaseQuote&&!this._inActive);this._isRequestToPurchaseQuote=t.IsRequestToPurchaseQuote;f=[$R_FN.setCleanTextValue(t.IsAssignedToMe==!0?this.writeCheckbox(t.ID,r,this._tblStock):""),$R_FN.setCleanTextValue($R_FN.setCleanTextValue(t.Alt==!0?$R_FN.showYellowTextImportant($RGT_nubButton_CustomerRequirement(t.ID,t.CustReqNo)):$RGT_nubButton_CustomerRequirement(t.ID,t.CustReqNo))),this._blnPOHub==!0?$R_FN.writeDoubleCellValue(t.Quantity,$R_FN.setCleanTextValue(l)):$R_FN.setCleanTextValue(t.Quantity),$R_FN.writeDoubleCellValue($R_FN.writePartNo(t.Alt==!0?$R_FN.showYellowTextImportant(t.Part):t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Company)+$R_FN.createAdvisoryNotesIcon(t.CompanyAdvisoryNotes,"margin-left-10"),$R_FN.setCleanTextValue(t.Date)),$R_FN.writeDoubleCellValue(t.TPriceInBom,t.SalesmanName),$R_FN.writeDoubleCellValue(t.MSL,t.FactorySealed),$R_FN.writeDoubleCellValue(t.IsAs6081Required,""),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.AssignedTo),""),this._blnPOHub==!0?$R_FN.writeDoubleCellValue(t.IsPurchaseRequestCreated==!0?"YES":"-",$R_FN.setCleanTextValue(t.Instructions)):$R_FN.setCleanTextValue(t.Instructions)];o={Part:$R_FN.setCleanTextValue(t.PartNo),Closed:t.Closed,IsAssignedToMe:t.IsAssignedToMe,Released:t.Released,CMNo:t.CMNo,HasSourcingResult:t.HasSourcingResult,SourcingResult:t.SourcingResult,IPOClientNo:t.IPOClientNo,CustomerRequirementNumber:t.CustReqNo,IsNoBid:t.IsNoBid,BOMNo:t.BOMNo,CMP:t.Company,CMPNo:t.CompanyNo,isPartWatchHUBIPO:t.PartWatchHUBIPO,IsAs6081Required:t.IsAs6081Required,MfrNo:t.MfrNo,MfrCode:t.Mfr};this._tblStock.addRow(f,t.ID,t.ID==this._intSelectedLineNo,o,u);bomStatus=t.BOMStatus;t.IsAssignedToMe==!0&&(this.registerCheckBox(t.ID,r,!1,v,this._tblStock),s=this.getCheckBox(r,this._tblStock),s._element.setAttribute("onClick",String.format('$find("{0}").getCheckedCellValue({1},{2});',this._element.id,r,t.ID)),s=null);t=null;f=null;o=null;this._tblStock.resizeColumns();this._ctlTabStock.addCountToTitle(r+1)}if(e==!0)for(a=document.getElementsByClassName("first notReadyToShip notReadyToShip_First alignCenter"),r=0;r<a.length;r++)document.getElementsByClassName("first notReadyToShip notReadyToShip_First alignCenter")[r].setAttribute("title","Sourcing Results having price issue kindly check and verify.")}i.Items?(this._intCountStock=i.Count,this._blnAllItemHasDelDate=i.AllHasDelDate,this._blnAllItemHasProduct=i.AllHasProduct,h=i.Items.find(n=>n.Released==!1),c=i.Items.find(n=>n.BOMStatus!="CLOSED"&&n.BOMStatus!="RELEASED"),this._ibtnExportToExcel&&$R_IBTN.enableButton(this._ibtnExportToExcel,h||c),this._ibtnHubImportSR&&$R_IBTN.enableButton(this._ibtnHubImportSR,h||c)):(this._ibtnExportToExcel&&$R_IBTN.enableButton(this._ibtnExportToExcel,!1),this._ibtnHubImportSR&&$R_IBTN.enableButton(this._ibtnHubImportSR,!1));this.disableItemAddButton(!this._blnRequestedToPoHub&&!this._inActive&&!this._isClosed);this.showInvalidImportWarning(i.HasImportFile&&(!i.Items||i.Items.length==0));this.getDataOK_End();Array.clear(this._ReqIds)},addCustReqRows:function(n,t){var t,i,f,e,r,u;if(t<n.length)for(t=0;t<n.length;t++)i=n[t],this._lineLength=n.length,this._BomCode=i.BOMCode,this._BomName=i.BOMFullName,this._BomCompanyName=i.Company,this._BomCompanyNo=i.CompanyNo,this._SalesManNo=i.Salesman,this._SalesManName=i.SalesmanName,this._RequestToPOHubBy=i.RequestToPOHubBy,this._UpdateByPH=i.UpdateByPH,f=i.IsRequestToPurchaseQuote&&!this._isClosed,e="cusReqMainPart",this._allExistInSourcingResult==!1&&(this._allExistInSourcingResult=i.HasSourcingResult),this.disableItemAddButton(!i.IsRequestToPurchaseQuote&&!this._inActive),this._isRequestToPurchaseQuote=i.IsRequestToPurchaseQuote,aryData=[this.writeCheckbox(i.ID,t,this._tblStock),$R_FN.setCleanTextValue($R_FN.setCleanTextValue(i.Alt==!0?$R_FN.showYellowTextImportant($RGT_nubButton_CustomerRequirement(i.ID,i.CustReqNo)):$RGT_nubButton_CustomerRequirement(i.ID,i.CustReqNo))),$R_FN.writeDoubleCellValue(i.Quantity),$R_FN.writeDoubleCellValue($R_FN.writePartNo(i.Alt==!0?$R_FN.showYellowTextImportant(i.Part):i.Part,i.ROHS),$R_FN.setCleanTextValue(i.CustomerPart)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(i.MfrNo,i.Mfr),$R_FN.setCleanTextValue(i.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(i.Product),$R_FN.setCleanTextValue(i.Package)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(i.Company),$R_FN.setCleanTextValue(i.Date)),$R_FN.writeDoubleCellValue(i.TPriceInBom,i.SalesmanName),$R_FN.writeDoubleCellValue(i.MSL,i.FactorySealed),this._blnPOHub==!0?$R_FN.writeDoubleCellValue(i.IsPurchaseRequestCreated==!0?"YES":"-",$R_FN.setCleanTextValue(i.Instructions)):$R_FN.setCleanTextValue(i.Instructions)],r={Part:$R_FN.setCleanTextValue(i.PartNo),Closed:i.Closed,Released:i.Released,CMNo:i.CMNo,HasSourcingResult:i.HasSourcingResult,SourcingResult:i.SourcingResult,IPOClientNo:i.IPOClientNo,CustomerRequirementNumber:i.CustReqNo,IsNoBid:i.IsNoBid,BOMNo:i.BOMNo},this._tblStock.addRow(aryData,i.ID,i.ID==this._intSelectedLineNo,r,e),bomStatus=i.BOMStatus,this.registerCheckBox(i.ID,t,!1,f,this._tblStock),u=this.getCheckBox(t,this._tblStock),u._element.setAttribute("onClick",String.format('$find("{0}").getCheckedCellValue({1},{2});',this._element.id,t,i.ID)),u=null,i=null,aryData=null,r=null,this._tblStock.resizeColumns(),this._ctlTabStock.addCountToTitle(t+1)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableButtons:function(n){n?(this._ibtnRelease&&$R_IBTN.enableButton(this._ibtnRelease,this._intCustomerRequirementID>0&&!this._blnRequirementReleased&&this._blnPOHub&&this._hasSourcingResult&&!this._isClosed&&this._tblStock.getSelectedExtraData().IsAssignedToMe==!0),this._ibtnUnRelease&&$R_IBTN.enableButton(this._ibtnUnRelease,this._intCustomerRequirementID>0&&this._blnRequirementReleased&&this._blnPOHub&&this._hasSourcingResult&&this._blnCanRecal&&!this._isClosed&&this._tblStock.getSelectedExtraData().IsAssignedToMe==!0),this._ibtnNoBid&&$R_IBTN.enableButton(this._ibtnNoBid,this._intCustomerRequirementID>0&&!this._hasSourcingResult&&this._blnPOHub&&!this._isClosed&&!this._isNoBid&&this._tblStock.getSelectedExtraData().IsAssignedToMe==!0),this._ibtnRecallNoBid&&$R_IBTN.enableButton(this._ibtnRecallNoBid,this._intCustomerRequirementID>0&&!this._hasSourcingResult&&this._blnPOHub&&!this._isClosed&&this._isNoBid&&this._tblStock.getSelectedExtraData().IsAssignedToMe==!0)):(this._ibtnNoBid&&$R_IBTN.enableButton(this._ibtnNoBid,!1),this._ibtnRecallNoBid&&$R_IBTN.enableButton(this._ibtnRecallNoBid,!1),this._ibtnRelease&&$R_IBTN.enableButton(this._ibtnRelease,!1),this._ibtnUnRelease&&$R_IBTN.enableButton(this._ibtnUnRelease,!1),this._ibtnNote&&$R_IBTN.enableButton(this._ibtnNote,!1),this._ibtnApplyPartwatch&&$R_IBTN.enableButton(this._ibtnApplyPartwatch,!1),this._ibtnRemovePartwatch&&$R_IBTN.enableButton(this._ibtnRemovePartwatch,!1))},saveError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},showExportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("ExportToCSV");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},exportToExcel:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("ExportToExcel");n._intTimeoutMilliseconds=3e5;n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.exportToExcel_OK));n.addError(Function.createDelegate(this,this.exportToExcel_Error));n.addTimeout(Function.createDelegate(this,this.exportToExcel_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportToExcel_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportToExcel_Error:function(n){this.showError(!0,n.get_ErrorMessage())},showAddForm:function(){this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1)},saveAddComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showReleaseForm:function(){this.onCallBeforeRelease();this._blnCanRelease&&(this._frmConfirm._intRequirementLineID=this._intCustomerRequirementID,this._frmConfirm._intBOMID=this._intBOMID,this._frmConfirm._BomCode=this._BomCode,this._frmConfirm._BomName=this._BomName,this._frmConfirm._BomCompanyName=this._BomCompanyName,this._frmConfirm._BomCompanyNo=this._BomCompanyNo,this._frmConfirm._SalesManNo=this._RequestToPOHubByNo,this._frmConfirm._SalesManName=this._SalesManName,this._frmConfirm._CustReqNo=this._CustReqNo,this._frmConfirm._ReqSalesman=this._ReqSalesman,this._frmConfirm._SupportTeamMemberNo=this._SupportTeamMemberNo,this.showForm(this._frmConfirm,!0))},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},showNoBidConfirmForm:function(){this._blnCanNoBid&&(this._frmNoBidConfirm._intRequirementLineID=this._intCustomerRequirementID,this._frmNoBidConfirm._intBOMID=this._intBOMID,this._frmNoBidConfirm._BomCode=this._BomCode,this._frmNoBidConfirm._BomName=this._BomName,this._frmNoBidConfirm._BomCompanyName=this._BomCompanyName,this._frmNoBidConfirm._BomCompanyNo=this._BomCompanyNo,this._frmNoBidConfirm._SalesManNo=this._SalesManNo,this._frmNoBidConfirm._SalesManName=this._SalesManName,this._frmNoBidConfirm._CustReqNo=this._CustReqNo,this._frmNoBidConfirm._RecallNoBid=!1,this._frmNoBidConfirm.setFieldValue("ctlNotes",""),this._frmNoBidConfirm.changeMode("NoBid"),this.showForm(this._frmNoBidConfirm,!0))},hideNoBidConfirmForm:function(){this.showForm(this._frmNoBidConfirm,!1)},saveNoBidConfirmComplete:function(){this.hideNoBidConfirmForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onRefereshAfterRelease()},showRecallNoBidConfirmForm:function(){this._blnCanNoBid&&(this._frmRecallNoBidConfirm._intRequirementLineID=this._intCustomerRequirementID,this._frmRecallNoBidConfirm._RecallNoBid=!0,this._frmRecallNoBidConfirm._CustReqNo=this._CustReqNo,this._frmRecallNoBidConfirm.setFieldValue("ctlNotes",""),this._frmRecallNoBidConfirm.changeMode("RecallNoBid"),this.showForm(this._frmRecallNoBidConfirm,!0))},hideRecallNoBidConfirmForm:function(){this.showForm(this._frmRecallNoBidConfirm,!1)},saveRecallNoBidConfirmComplete:function(){this.hideRecallNoBidConfirmForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onRefereshAfterRelease()},showDeleteForm:function(){this._frmDelete._intCustomerRequirementID=this._intCustomerRequirementID;this._frmDelete._intBOMID=this._intBOMID;this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1)},showApplyPartwatchForm:function(){this._frmApplyPartwatch._intCustomerRequirementID=this._intCustomerRequirementID;this._frmApplyPartwatch._ReqIds=this._ReqIds;this._frmApplyPartwatch._intBOMID=this._intBOMID;this._frmApplyPartwatch._intBOMNo=this._BOMNo;this._frmApplyPartwatch._RequestToPOHubBy=this._RequestToPOHubBy;this._frmApplyPartwatch._UpdateByPH=this._UpdateByPH;this._frmApplyPartwatch._BomName=this._BomName;this._frmApplyPartwatch._BomCompanyNo=this._BomCompanyNo;this._frmApplyPartwatch._intContact2No=this._intContact2No;this.showForm(this._frmApplyPartwatch,!0)},showRemovePartwatchForm:function(){this._frmRemovePartwatch._intCustomerRequirementID=this._intCustomerRequirementID;this._frmRemovePartwatch._ReqIds=this._ReqIds;this._frmRemovePartwatch._intBOMID=this._intBOMID;this._frmRemovePartwatch._intBOMNo=this._BOMNo;this._frmRemovePartwatch._RequestToPOHubBy=this._RequestToPOHubBy;this._frmRemovePartwatch._UpdateByPH=this._UpdateByPH;this._frmRemovePartwatch._BomName=this._BomName;this._frmRemovePartwatch._BomCompanyNo=this._BomCompanyNo;this._frmRemovePartwatch._intContact2No=this._intContact2No;this.showForm(this._frmRemovePartwatch,!0)},hideRemovePartwatchForm:function(){this.showForm(this._frmRemovePartwatch,!1)},saveRemovePartwatchConfirmComplete:function(){this.hideRemovePartwatchForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onRefereshAfterRelease()},hideApplyPartwatchForm:function(){this.showForm(this._frmApplyPartwatch,!1)},saveApplyPartwatchConfirmComplete:function(){this.hideApplyPartwatchForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onRefereshAfterRelease()},showUnReleaseForm:function(){this._frmUnRelease._intCustomerRequirementID=this._intCustomerRequirementID;this._frmUnRelease._intBOMID=this._intBOMID;this.showForm(this._frmUnRelease,!0)},hideUnReleaseForm:function(){this.showForm(this._frmUnRelease,!1)},UnReleaseComplete:function(){this.hideDeleteForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onRefereshAfterRelease()},saveCeaseComplete:function(){this.hideConfirmForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onRefereshAfterRelease()},DeleteComplete:function(){this.hideDeleteForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1);this.getData()},enableBOMProvision:function(n){this._ibtnStockProvision&&$R_IBTN.enableButton(this._ibtnStockProvision,n&&this._blnProvisionLoaded)},tbl_SelectedIndexChanged:function(){SetAssignToMe(this._tblStock.getSelectedExtraData().IsAssignedToMe);this._tblStock.getSelectedExtraData().IsAs6081Required=="Yes"?SetAS6081Required(!0):SetAS6081Required(!1);var n=this._tblStock.getSelectedExtraData().MfrNo,t=this._tblStock.getSelectedExtraData().MfrCode;n&&this.StartRefreshLylicaAPIData(this._tblStock.getSelectedExtraData().Part,t,n);this.enableDisableItemDeleteButton(this._isRequestToPurchaseQuote);this._intCustomerRequirementID=this._tblStock._varSelectedValue;this._intSelectedLineNo=this._tblStock._varSelectedValue;this._blnRequirementClosed=this._tblStock.getSelectedExtraData().Closed;this._blnRequirementReleased=this._tblStock.getSelectedExtraData().Released;this._intCompanyID=this._tblStock.getSelectedExtraData().CMNo;this._hasSourcingResult=this._tblStock.getSelectedExtraData().HasSourcingResult;this._CustReqNo=this._tblStock.getSelectedExtraData().CustomerRequirementNumber;this._isNoBid=this._tblStock.getSelectedExtraData().IsNoBid;this._blnCanApplyPartWatch=this._tblStock.getSelectedExtraData().isPartWatchHUBIPO;this.enableButtons(!0);this.onPartSelected();this.onGotDataOK();this.getLineData()},getSelectedPartNo:function(){return this._tblStock.getSelectedExtraData().Part},getIPOClientNo:function(){return this._tblStock.getSelectedExtraData().IPOClientNo},enableItemReleaseButton:function(n){this._ibtnRelease&&$R_IBTN.enableButton(this._ibtnRelease,this._intCustomerRequirementID>0&&!this._blnRequirementReleased&&this._blnPOHub&&n&&!this._isClosed&&this._tblStock.getSelectedExtraData().IsAssignedToMe==!0)},disableItemAddButton:function(n){this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,n)},enableDisableItemDeleteButton:function(n){this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,this._lineLength>0&&!n&&!this._isClosed)},StartRefreshLylicaAPIData:function(n,t,i){n=this.beautifyPartNumber(n);$.ajax({type:"POST",contentType:"application/json",url:window.location.origin+"/controls/Nuggets/CusReqAdd/CusReqAdd.ashx?action=RefreshLyticaAPIAfter3Days&PartNumber="+n+"&mfr="+t+"&mfrNo="+i,async:!0,error:function(){}})},beautifyPartNumber:function(n){return n=n.replace(" (Alternate)",""),n=n.replace("&","_AMPERSAND_"),n=n.replace("#","_HASH_"),n.replace("=","_EQUALS_")},getLineData:function(){$("#As6081WarningMsg").hide();$("#dvtxt").html("");$("#divBlockBox").hide();CloseKubPopup();ShowKubIcon(!1);this._blnLineLoaded=!1;var n=new Rebound.GlobalTrader.Site.Data;this.showLoading(!0);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);n.set_PathToData("controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("GetItem");n.addParameter("id",this._intCustomerRequirementID);n.addDataOK(Function.createDelegate(this,this.getLineDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineDataOK:function(n){var t=n._result,u,o,r,v,y;if(t.IsAllowedEnableKub&&LoadParNumberDetailsForKub(t),t.IsAs6081Required=="Yes"?($("#As6081WarningMsg").show(),$("#dvtxt").html("This part requires AS6081 compliance, please ensure the appropriate supplier is chosen to fulfil this requirement.")):($("#As6081WarningMsg").hide(),$("#dvtxt").html("")),u="",this.setFieldValue("hidCompanyID",t.CustomerNo),this.setFieldValue("hidCompanyName",$R_FN.setCleanTextValue(t.CustomerName)),this.setFieldValue("hidContactID",t.ContactNo),this.setFieldValue("hidContactName",$R_FN.setCleanTextValue(t.Contact)),this.setFieldValue("ctlQuantity",t.Quantity),this.setFieldValue("ctlPartNo",t.Part),this.setFieldValue("ctlAs6081Required",t.IsAs6081Required),t.IsAs6081Required=="Yes"?$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlAs6081Required_lbl").css("background-color","Yellow"):$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlAs6081Required_lbl").css("background-color","white"),this.setFieldValue("ctlQuantity",t.Quantity),t.IsPDFAvailable)if(u="app_themes/original/images/IconButton/pdficon-clear.png",t.StockAvailableDetail!=null&&t.StockAvailableDetail!=""){var i=t.StockAvailableDetail.split("-"),s=i[0],h=i[1],c=i[2],l=i[3],e=i[4],a="",f="Whs_StockDetail.aspx?stk="+e+"",p=$R_FN.setCleanTextValue(String.format("&nbsp;&nbsp;<a style='padding-left:15px' href=\"javascript:void(0);\" onclick=\"$RGT_openBomItemIHS({0},{1})\" title=\"Click to View docs\"><img style='margin-bottom:-5px' border='0'  src="+u+" width='20' height='18'><\/a>",t.IHSPartsId,1));this.setFieldValue("ctlPartNo",$R_FN.showStockAvailableHUBRFQ(t.Part,s,h,c,l,f,a,p))}else this.setFieldValue("ctlPartNo",$R_FN.setCleanTextValue(t.Part+String.format('&nbsp;&nbsp;<a style=\'float:right\' href="javascript:void(0);" onclick="$RGT_openBomItemIHS({0},{1})" title="Click to View docs"><img border=\'0\'  src='+u+" width='20' height='18'><\/a>",t.IHSPartsId,1)));else if(t.StockAvailableDetail!=null&&t.StockAvailableDetail!=""){var i=t.StockAvailableDetail.split("-"),s=i[0],h=i[1],c=i[2],l=i[3],e=i[4],a="",f=f="Whs_StockBrowse.aspx";parseInt(e)>0&&(f="Whs_StockDetail.aspx?stk="+e+"");this.setFieldValue("ctlPartNo",$R_FN.showStockAvailableNew(t.Part,s,h,c,l,f,a))}else this.setFieldValue("ctlPartNo",t.Part);if(this.setFieldValue("ctlCustomerPart",$R_FN.setCleanTextValue(t.CustomerPart)),this.setFieldValue("ctlManufacturer",$RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer,t.MfrAdvisoryNotes)),this.setFieldValue("hidManufacturer",$R_FN.setCleanTextValue(t.Manufacturer)),this.setFieldValue("hidManufacturerNo",t.ManufacturerNo),this.setFieldValue("hidMfrAdvisoryNotes",t.MfrAdvisoryNotes),this.setFieldValue("ctlDateCode",t.DateCode),this.setFieldValue("ctlProduct",$R_FN.setCleanTextValue(t.Product)),this.setFieldValue("hidProductID",t.ProductNo),this.setFieldValue("ctlPackage",$R_FN.setCleanTextValue(t.Package)),this.setFieldValue("hidPackageID",t.PackageNo),this.setFieldValue("ctlTargetPrice",t.Price),this.setFieldValue("hidPrice",t.PriceRaw),this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.Currency)),this.setFieldValue("hidCurrencyID",t.CurrencyNo),this.setFieldValue("ctlDateRequired",t.DatePromised),this.setFieldValue("ctlUsage",$R_FN.setCleanTextValue(t.Usage)),this.setFieldValue("hidUsageID",$R_FN.setCleanTextValue(t.UsageNo)),this.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(t.Notes)),this.setFieldValue("ctlInstructions",$R_FN.setCleanTextValue(t.Instructions)),this.setFieldValue("ctlROHS",$R_FN.writeROHS(t.ROHS)),this.setFieldValue("hidROHS",t.ROHS),this.setFieldValue("ctlClosedReason",t.ClosedReason),this.setFieldValue("hidDisplayStatus",$R_FN.setCleanTextValue(t.DisplayStatus)),this.setFieldValue("ctlPartWatch",t.PartWatch),this.setFieldValue("ctlBOM",t.BOM),this.setFieldValue("ctlBOMName",t.BOMName),this.setFieldValue("hidBOMID",t.BOMId),this.setFieldValue("hidBOMHeaderDisplayStatus",t.RequestToPOHubBy==null?!0:!1),this.setFieldValue("ctlMSL",t.MSL),this.setFieldValue("ctlFactorySealed",t.FactorySealed),this.setFieldValue("ctlPQA",t.PQA),this.setFieldValue("ctlObsolete",t.Obsolete),this.setFieldValue("ctlLastTimeBuy",t.LastTimeBuy),this.setFieldValue("ctlRefirbsAcceptable",t.RefirbsAcceptable),this.setFieldValue("ctlTestingRequired",t.TestingRequired),this.setFieldValue("ctlTargetSellPrice",t.TargetSellPrice),this.setFieldValue("ctlCompetitorBestoffer",t.CompetitorBestOffer),this.setFieldValue("ctlCustomerDecisionDate",t.CustomerDecisionDate),this.setFieldValue("ctlRFQClosingDate",t.RFQClosingDate),this.setFieldValue("ctlQuoteValidityRequiredHid",t.QuoteValidityRequired),this.setFieldValue("ctlQuoteValidityRequired",t.QuoteValidityText),this.setFieldValue("ctlTypeHid",t.Type),this.setFieldValue("ctlType",t.ReqTypeText),this.setFieldValue("ctlOrderToPlace",t.OrderToPlace),this.setFieldValue("ctlRequirementforTraceability",t.ReqForTraceabilityText),this.setFieldValue("ctlRequirementforTraceabilityHid",t.RequirementforTraceability),this.setFieldValue("ctlTargetSellPriceHidden",t.hidTargetSellPrice),this.setFieldValue("ctlCompetitorBestofferHidden",t.hidCompetitorBestOffer),this.setFieldValue("ctlEAU",t.EAU),this.setFieldValue("ctlCustomerRefNo",t.CustomerRefNo),this.setFieldValue("ctlIsNoBid",$R_FN.showLargeFonts(t.IsNoBidStatus)),this.showField("ctlIsNoBid",t.IsNoBid),this.setFieldValue("ctlIsNoBidNotes",t.NoBidNotes),this.showField("ctlIsNoBidNotes",t.IsNoBid),this.setFieldValue("ctlClosed",t.Closed==!1?$R_FN.showLargeFontsWithColor("No"):$R_FN.showLargeFonts("Yes")),this.setFieldValue("ctlAlternativesAccepted",t.AlternativesAccepted),this.setFieldValue("ctlRepeatBusiness",t.RepeatBusiness),this.setFieldValue("ctlPrdDutyCodeRate",t.DutyCodeAndRate),this._isNoBid=t.IsNoBid,this._blnCanRecal=t.SourcingResult,this.setFieldValue("hidMSL",t.MSLLevelNo),this.setFieldValue("ctlCountryOfOrigin",t.CountryOfOrigin),this.setFieldValue("ctlLifeCycleStage",$R_FN.showIHSstatusDefi(t.LifeCycleStage,t.IHSStatusDefination)),this.setFieldValue("ctlPackagingSize",t.PackagingSize),this.setFieldValue("ctlDescriptions",t.Descriptions),t.IHSProductNo!=0&&t.ProductNo!=0?(this.setFieldValue("ctlIHSProduct",t.IHSProduct),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlIHSProduct_lbl").css("color","")):this.setFieldValue("ctlIHSProduct",t.IHSProduct),t.IHSHTSCode!=""?(this.setFieldValue("ctlHTSCode",t.HTSCode),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_ctlHTSCode_lbl").css("color","")):this.setFieldValue("ctlHTSCode",t.HTSCode),t.IHSDutyCode!=null,this._IsPOHub=t.IsPOHub,this._IsPOHub==!0?(this.showField("ctlAveragePrice",!0),this.setFieldValue("ctlAveragePrice",t.AveragePrice),this.showField("ctlMarketLeading",!0),this.setFieldValue("ctlMarketLeading",t.MarketLeading),this.showField("ctlTargetPriceAPI",!0),this.setFieldValue("ctlTargetPriceAPI",t.TargetPrice),$("#CmbLyticaManufacture").show(),this.showField("ctlCustomerRefNo",!0)):(this.showField("ctlAveragePrice",!1),this.showField("ctlMarketLeading",!1),this.showField("ctlTargetPriceAPI",!1),$("#CmbLyticaManufacture").hide(),this.showField("ctlCustomerRefNo",!1)),t.IHSECCNSCodeDefination!=null&&t.IHSECCNSCodeDefination!=""?this.setFieldValue("ctlECCNCode",$R_FN.showIHSECCNCodeDefi(t.ECCNCode,t.IHSECCNSCodeDefination)):this.setFieldValue("ctlECCNCode",t.ECCNCode),o="",t.PurchaseRequestId&&t.PurchaseRequestNumber)for(r=0;r<t.PurchaseRequestNumber.length;r++)v=t.PurchaseRequestId[r],y=t.PurchaseRequestNumber[r],o+=$RGT_nubButton_POQuote(v.PurchaseRequestId,y.PurchaseRequestNumber),r%5==0&&r>4&&(o+="<br/>");this._RequestToPOHubByNo=t.RequestToPOHubBy;this._ReqSalesman=t.SalesmanNo;this._SupportTeamMemberNo=t.SupportTeamMemberNo;this.enableButtons(!0);$R_FN.showElement(this._pnlLineDetail,!0);$R_FN.showElement(this._pnlLoadingLineDetail,!1);this.showLoading(!1);this.onGotDataOK();$("#lblRsMFR").hide();$("#spanmfr").text("");t.LyticaManufacturerRef?($("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").addClass("invisible"),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").val(t.LyticaManufacturerRef),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl04").removeClass("invisible"),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03").removeClass("invisible"),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03").html(t.LyticaManufacturerRef),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl05").addClass("invisible")):($("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").removeClass("invisible"),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").val(""),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl04").addClass("invisible"),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03").addClass("invisible"),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl05").addClass("invisible"));$find(this._element.id+"_ctl13_cmbManufacturer")&&$find(this._element.id+"_ctl13_cmbManufacturer")._aut.addSelectionMadeEvent(Function.createDelegate(this,this.RsMfrChanged))},getLineDataError:function(n){this.showLoading(!1);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlLineDetailError,!0);$R_FN.setInnerHTML(this._pnlLineDetailError,n.get_ErrorMessage());Array.clear(this._ReqIds)},ResetMFR:function(){$("#spanmfr").text("")},RsMfrChanged:function(){var n=$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").val();this.getRsMfr(n)},getRsMfr:function(n){var t=new Rebound.GlobalTrader.Site.Data;t.set_PathToData("controls/Nuggets/BOMItems");t.set_DataObject("BOMItems");t.set_DataAction("GetLyticaManufacturer");t.addParameter("RsManufacturerName",n);t.addParameter("CustomerRequirementID",this._intCustomerRequirementID);t.addDataOK(Function.createDelegate(this,this.getRsMfrOK));$R_DQ.addToQueue(t);$R_DQ.processQueue();t=null},getRsMfrOK:function(n){var t=n._result;t!=null?($("#lblRsMFR").show(),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufacturertxt").addClass("invisible"),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl04").removeClass("invisible"),$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl13_cmbManufactureraut_ctl03").removeClass("invisible"),this.setFieldValue("ctlAveragePrice",t.AveragePrice),this.setFieldValue("ctlMarketLeading",t.MarketLeading),this.setFieldValue("ctlTargetPriceAPI",t.TargetPrice),$("#lytica-data").html(`AV Price: ${t.AveragePrice}   M/Leading: ${t.MarketLeading}   Target: ${t.TargetPrice}   P/Status: ${t.LifeCycleStatus}`)):$("#lblRsMFR").hide()},showExpediteNoteForm:function(){this._frmAddExpediteNote._ReqIds=this._ReqIds;this._frmAddExpediteNote._intBOMID=this._intBOMID;this._frmAddExpediteNote._intBOMNo=this._BOMNo;this._frmAddExpediteNote._RequestToPOHubBy=this._RequestToPOHubBy;this._frmAddExpediteNote._UpdateByPH=this._UpdateByPH;this._frmAddExpediteNote._BomName=this._BomName;this._frmAddExpediteNote._BomCompanyNo=this._BomCompanyNo;this._frmAddExpediteNote._intContact2No=this._intContact2No;this._frmAddExpediteNote.setFieldValue("ctlExpediteNotes","");this.showForm(this._frmAddExpediteNote,!0)},saveAddExpediteNoteComplete:function(){this._ibtnNote&&$R_IBTN.enableButton(this._ibtnNote,!1);this._ReqIds=[];this.showForm(this._frmAddExpediteNote,!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},cancelAddExpediteNoteForm:function(){this.showForm(this._frmAddExpediteNote,!1);this.showContent(!0);Array.clear(this._ReqIds)},cancelImportSourcingForm:function(){this.showForm(this._frmImportSourcing,!1);this.getData()},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t,i),u=this.getControlID("chkImg",t,i);return String.format('<div class="imageCheckBoxDisabled" id="{0}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /> <\/div>',r,u,"off")},getControlID:function(n,t,i){return String.format("{0}_{1}{2}",i._element.id,n,t)},getCheckBox:function(n,t){return $find(this.getControlID("chk",n,t))},registerCheckBox:function(n,t,i,r,u){var e=this.getControlID("chk",t,u),o=this.getControlID("chkImg",t,u),f=this.getCheckBox(t,u);f&&(f.dispose(),f=null);eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",[["blnChecked",i],["blnEnabled",r],["img",String.format('$get("{0}")',o)]],e))},getCheckedCellValue:function(n,t){var i=this._tblStock,r=this.getCheckBox(n,i),u=r._blnChecked,f=i._tbl.rows[n];f&&(u?($R_IBTN.enableButton(this._ibtnNote,!0),this._isClosed?this._blnPOHub==!0&&($R_IBTN.enableButton(this._ibtnApplyPartwatch,!0),$R_IBTN.enableButton(this._ibtnRemovePartwatch,!0)):this._blnPOHub==!0&&(this._tblStock.getSelectedExtraData().isPartWatchHUBIPO==!0?($R_IBTN.enableButton(this._ibtnApplyPartwatch,!1),$R_IBTN.enableButton(this._ibtnRemovePartwatch,!0)):($R_IBTN.enableButton(this._ibtnApplyPartwatch,!0),$R_IBTN.enableButton(this._ibtnRemovePartwatch,!1))),Array.add(this._ReqIds,t)):($R_IBTN.enableButton(this._ibtnNote,!1),this._blnPOHub==!0&&($R_IBTN.enableButton(this._ibtnApplyPartwatch,!1),$R_IBTN.enableButton(this._ibtnRemovePartwatch,!1)),Array.remove(this._ReqIds,t)),this._ReqIds.length==0?($R_IBTN.enableButton(this._ibtnNote,!1),this._blnPOHub==!0&&($R_IBTN.enableButton(this._ibtnApplyPartwatch,!1),$R_IBTN.enableButton(this._ibtnRemovePartwatch,!1))):$R_IBTN.enableButton(this._ibtnNote,!0))},enableItemUnReleaseButton:function(){},getPartDetaildata:function(){var n=this._tblStock.getSelectedExtraData().Part;n.length>0?(this.getPartDetail(n),this.showDetailDiv(!0)):this.showDetailDiv(!1)},showDetailDiv:function(n){n?($("#mydiv").show(),$("#tbpartdet").show()):($("#mydiv").hide(),$("#tbpartdet").hide())},getPartDetail:function(n){$("#divBlockBox").hide();var t=new Rebound.GlobalTrader.Site.Data;t.set_PathToData("controls/Nuggets/CusReqAdd");t.set_DataObject("CusReqAdd");t.set_DataAction("GetPartDetail");t.addParameter("partNo",n);t.addParameter("CompanyNo",this._tblStock.getSelectedExtraData().CMPNo);t.addDataOK(Function.createDelegate(this,this.setPartDetail));t.addError(Function.createDelegate(this,this.getPartDetailError));t.addTimeout(Function.createDelegate(this,this.getPartDetailError));$R_DQ.addToQueue(t);$R_DQ.processQueue();t=null},setPartDetail:function(n){var i,t;for(res=n._result,i=0;i<res.LastPriceCustDetails.length;i++)t=res.LastPriceCustDetails[i],$("#spnpartname").text(this._tblStock.getSelectedExtraData().Part),$("#spnLastSoldPrice").text(t.LastPricePaidByCust),$("#spnsoldtocuston").text($R_FN.setCleanTextValue(t.LastSoldtoCustomer)),$("#spnAvgPrice").text(t.LastAverageReboundPriceSold),$("#spnlastsoldon").text(t.LastSoldOn),$("#spnLastQuantity").text(t.LastQuantity),$("#spnLastSupplierType").text($R_FN.setCleanTextValue(t.LastSupplierType)),$("#spnLastDatecode").text($R_FN.setCleanTextValue(t.LastDatecode)),$("#spnLastDatePurchased").text(t.LastDatePurchased),$("#spnLastCustomerRegion").text($R_FN.setCleanTextValue(t.LastCustomerRegion)),$("#spnCustLastSoldPrice").text(t.CustLastPricePaidByCust),$("#spnCurrentCust").text(this._tblStock.getSelectedExtraData().CMP),$("#spnCustlastsoldon").text(t.CustLastSoldOn),$("#spnCustQuantity").text(t.CustQuantity),$("#spnCustSupplierType").text($R_FN.setCleanTextValue(t.CustSupplierType)),$("#spnCustDatecode").text($R_FN.setCleanTextValue(t.CustDatecode)),$("#spnCustDatePurchased").text(t.CustDatePurchased),$("#spnCustomerRegion").text($R_FN.setCleanTextValue(t.CustomerRegion)),$("#spnLastPricePaid12").text(t.BestLastPricePaid12),$("#spnCleintBestPricePaid12").text(t.CleintBestPricePaid12);$("#divBlockBox").hide()},getPartDetailError:function(){},hidpartdetaildive:function(){this.showDetailDiv(!1)},showImportSourcingResultForm:function(){this.showForm(this._frmImportSourcing,!0)},showHubImportSRForm:function(){this._frmHubImportSR._intBOMID=this._intBOMID;this.showForm(this._frmHubImportSR,!0)},cancelHubImportSRForm:function(){var i=this._frmHubImportSR.checkUnsaveData(),n,t;(!i||(n="Your input data has not been saved and will be cleared for the action. Do you want to continue?",t=confirm(n),t))&&(this._frmHubImportSR.resetForm(),this.showForm(this._frmHubImportSR,!1),this.onImportSourcingResultSuccess())},showInvalidImportWarning:function(n){if($("#BomImportWarning").remove(),n){var t=String.format('<span id="BomImportWarning" title="{0}" class="ihspartstatusdoc"><\/span>',"HUBRFQ Items are invalid. Kindly check and use the 'BOM Import' function on the 'Uploaded Document' section to modify.");$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl06").parent().append(t)}}};Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMItems",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);Rebound.GlobalTrader.Site.Functions.openIHSPDFWindow=function(n){var t=String.format("IHSPDFDocument.aspx?ihs="+n);window.open(t,"winIHSPDFDocument","left=20,top=20,width=900,height=400,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes")};$RGT_openIHSDoc=function(n){$R_FN.openIHSPDFWindow(n)};