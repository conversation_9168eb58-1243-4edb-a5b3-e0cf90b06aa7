/*
Marker     Changed by      Date          Remarks
[001]      Abhinav         25/04/2014    ESMS #111 Quick Jump for NPR
[002]     <PERSON><PERSON>    16/02/2017    Quick Jump of IPO & HUBRFQ
[003]     <PERSON><PERSON><PERSON>      01/06/2018    Quick Jump in Global Warehouse
[004]      Soorya          03/03/2023    RP-1048 Remove AI code
*/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
//using Microsoft.ApplicationInsights; // [004]

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets.Data
{

    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class QuickJump : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                try
                {
                    switch (Action)
                    {
                        case "GetRequirementID": GetRequirementID(); break;
                        case "GetQuoteID": GetQuoteID(); break;
                        case "GetPurchaseOrderID": GetPurchaseOrderID(); break;
                        case "GetSalesOrderID": GetSalesOrderID(); break;
                        case "GetInvoiceID": GetInvoiceID(); break;
                        case "GetCRMAID": GetCRMAID(); break;
                        case "GetSRMAID": GetSRMAID(); break;
                        case "GetGoodsInID": GetGoodsInID(); break;
                        case "GetCreditID": GetCreditID(); break;
                        case "GetDebitID": GetDebitID(); break;
                        //[0001] start
                        case "GetNPRGILineID": GetNPRGILineID(); break;
                        //[0001] end
                        //[002] start
                        case "GetInternalPurchaseOrderID": GetInternalPurchaseOrderID(); break;
                        case "GetHUBRFQID": GetHUBRFQID(); break;
                        case "GetStockID": GetStockID(); break;
                            //[002] end
                    }
                }
                catch(Exception ex)
                {
                    //[004] 
                    //var ai = new TelemetryClient(); // or re-use an existing instance
                    //ai.TrackTrace("Page URL:" + HttpContext.Current.Request.UrlReferrer + ",Quick Jump : " + Action);
                    //ai.TrackException(ex);
                    new Errorlog().LogMessage("Error at ProcessRequest in QuickJump.cs : " + ex.Message);
                    WriteError(ex);
                }
            }
        }

        private void GetRequirementID()
        {
            CustomerRequirement cr = CustomerRequirement.GetIdByNumber(GetFormValue_Int("No"), SessionManager.ClientID);
            if (cr != null)
            {
                //Error
               // cr = null;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ID", cr.CustomerRequirementId);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            cr = null;
        }

        private void GetQuoteID()
        {
            Quote qo = Quote.GetIDFromNumber(GetFormValue_Int("No"), SessionManager.ClientID);
            if (qo != null)
            {
                //Error
                //qo = null;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ID", qo.QuoteId);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            qo = null;
        }

        private void GetPurchaseOrderID()
        {
            //[003] start
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<PurchaseOrder> lstpo = PurchaseOrder.GetIDByNumber(GetFormValue_Int("No"), SessionManager.ClientID, SessionManager.IsGlobalUser.Value == true ? 1 : 0);
            if (lstpo != null)
            {
                foreach (PurchaseOrder po in lstpo)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", po.PurchaseOrderId);
                    jsnItem.AddVariable("ItemDescription", po.PONumberDetail);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("QJSearchResult", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            lstpo = null;
            //[003] end
        }

        private void GetSalesOrderID()
        {
            //[003]  start
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<SalesOrder> lstso = SalesOrder.GetIDByNumber(GetFormValue_Int("No"), SessionManager.ClientID, SessionManager.IsGlobalUser.Value == true ? 1 : 0);
            if (lstso != null)
            {
                foreach (SalesOrder so in lstso)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", so.SalesOrderId);
                    jsnItem.AddVariable("ItemDescription", so.SalesOrderNumberDetail);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("QJSearchResult", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            lstso = null;
            //[003]	 end
        }

        private void GetInvoiceID()
        {
            //[003] start
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<Invoice> lstinv = Invoice.GetIDByNumber(GetFormValue_Int("No"), SessionManager.ClientID, SessionManager.IsGlobalUser.Value == true ? 1 : 0);
            if (lstinv != null)
            {
                foreach (Invoice inv in lstinv)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", inv.InvoiceId);
                    jsnItem.AddVariable("ItemDescription", inv.InvoiceDetail);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("QJSearchResult", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            lstinv = null;
            //[003] end;
        }

        private void GetCRMAID()
        {
            //[003]  start
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<CustomerRma> lstcrma = CustomerRma.GetIDByNumber(GetFormValue_Int("No"), SessionManager.ClientID, SessionManager.IsGlobalUser.Value == true ? 1 : 0);
            if (lstcrma != null)
            {
                foreach (CustomerRma po in lstcrma)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", po.CustomerRMAId);
                    jsnItem.AddVariable("ItemDescription", po.CRMANumberDetail);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("QJSearchResult", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            lstcrma = null;
            //[003]	 end
        }

        private void GetSRMAID()
        {
            //[003]  start
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<SupplierRma> lstsrma = SupplierRma.GetIDByNumber(GetFormValue_Int("No"), SessionManager.ClientID, SessionManager.IsGlobalUser.Value == true ? 1 : 0);
            if (lstsrma != null)
            {
                foreach (SupplierRma po in lstsrma)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", po.SupplierRMAId);
                    jsnItem.AddVariable("ItemDescription", po.SRMANumberDetail);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("QJSearchResult", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            lstsrma = null;
            //[003]	 end
        }

        private void GetGoodsInID()
        {
            //[003]  start
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);

            List<GoodsIn> lstgi = GoodsIn.GetIDByNumber(GetFormValue_Int("No"), SessionManager.ClientID, SessionManager.IsGlobalUser.Value == true ? 1 : 0);
            if (lstgi != null)
            {
                foreach (GoodsIn GI in lstgi)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", GI.GoodsInId);
                    jsnItem.AddVariable("ItemDescription", GI.GoodsInNumberDetail);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("QJSearchResult", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            lstgi = null;
            //[003]	 end
        }
        private void GetCreditID()
        {
            Credit credit = Credit.GetIdByNumber(GetFormValue_Int("No"), SessionManager.ClientID);
            if (credit != null)
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ID", credit.CreditId);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            credit = null;
        }

        private void GetDebitID()
        {
            //[003]  start
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<Debit> lstdebit = Debit.GetIdByNumber(GetFormValue_Int("No"), SessionManager.ClientID, SessionManager.IsGlobalUser.Value == true ? 1 : 0);
            if (lstdebit != null)
            {
                foreach (Debit d in lstdebit)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", d.DebitId);
                    jsnItem.AddVariable("ItemDescription", d.DebitDetailNumber);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("QJSearchResult", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            lstdebit = null;
            //[003]	 end
        }
        //[0001] start
        private void GetNPRGILineID()
        {
            string strNPR = string.Empty;
            string[] strNo = GetFormValue_String("No").Split('-');
            if (strNo != null)
                strNPR = strNo[strNo.Length - 1];
            ReportNPR npr = ReportNPR.Get(Int32.Parse(strNPR), SessionManager.ClientID);
            if (npr != null)
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ID", npr.GoodsInLineId);
                jsn.AddVariable("NPRID", npr.NPRId);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            npr = null;
            strNPR = null;
            strNo = null;
        }
        //[0001] end
        //[002] start
        private void GetInternalPurchaseOrderID()
        {
            InternalPurchaseOrder po = InternalPurchaseOrder.GetIDByNumber(GetFormValue_Int("No"), SessionManager.ClientID);
            if (po != null)
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ID", po.InternalPurchaseOrderId);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            po = null;
        }
        private void GetHUBRFQID()
        {
            BOM bom = BOM.GetIDByNumber(GetFormValue_String("No"), SessionManager.ClientID);
            if (bom != null)
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ID", bom.BOMId);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            bom = null;
        }
        //[002] end
        private void GetStockID()
        {
            Quote STK = Quote.GetIDFromStockNumber(GetFormValue_Int("No"), SessionManager.ClientID);
            if (STK != null)
            {
                //Error
                //qo = null;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ID", STK.StockId);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
            STK = null;
        }

    }
}
