///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.prototype = {

	initialize: function() {
		this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
		this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this._strPathToData = "controls/DataListNuggets/ReceivedPurchaseOrders";
		this._strDataObject = "ReceivedPurchaseOrders";
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.callBaseMethod(this, "initialize");
	},
	
	initAfterBaseIsReady: function() {
		this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
		this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.callBaseMethod(this, "dispose");
	},
					
	pageTabChanged: function() {
		this._table._intCurrentPage = 1;
		this._enmViewLevel = this._intCurrentTab;
		this.getData();
	},

	setupDataCall: function() {
		this._objData.addParameter("ViewLevel", this._enmViewLevel);
	},

	getDataOK: function(args) {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				$RGT_nubButton_ReceivedPurchaseOrder(row.ID, row.No)
				, $R_FN.writeDoubleCellValue(row.Qty, row.QtyR)
				, $R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(row.GINo, row.GI), $R_FN.setCleanTextValue(row.DateR))
				, $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
				, $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.SuppPart), $R_FN.setCleanTextValue(row.SuppInv))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.AWB), $R_FN.setCleanTextValue(row.InvTotal))
			];
			this._table.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
	
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
