///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 30.03.2010:
// - ensure line is fully loaded before we allow edits (to stop the wrong data being
//   edited)
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.initializeBase(this, [element]);
    this._intCreditID = -1;
    this._intLineID = -1;
    this._intLineCount = 0;
    this._intLineDataCalls = 0;
    this._blnLineIsService = false;
    this._blnLineLoaded = false;
    this._CreditLineIds = [];
    this._IsPoHub = false;
    this._isEditEnable = false;
    this._hidInvoiceNumber = false;
    this._blnHubLogin = false;
    this._blnFronClientInvoice = false;
    this._blnExported = false;
};
Array.prototype.remove = function (value) {
    if (this.indexOf(value) !== -1) {
        this.splice(this.indexOf(value), 1);
        return true;
    } else {
        return false;
    };
};
Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.prototype = {

    get_intCreditID: function () { return this._intCreditID; }, set_intCreditID: function (v) { if (this._intCreditID !== v) this._intCreditID = v; },
    get_intLineID: function () { return this._intLineID; }, set_intLineID: function (v) { if (this._intLineID !== v) this._intLineID = v; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },
    get_ibtnDelete: function () { return this._ibtnDelete; }, set_ibtnDelete: function (v) { if (this._ibtnDelete !== v) this._ibtnDelete = v; },
    get_tbl: function () { return this._tbl; }, set_tbl: function (v) { if (this._tbl !== v) this._tbl = v; },
    get_hypPrev: function () { return this._hypPrev; }, set_hypPrev: function (v) { if (this._hypPrev !== v) this._hypPrev = v; },
    get_hypNext: function () { return this._hypNext; }, set_hypNext: function (v) { if (this._hypNext !== v) this._hypNext = v; },
    get_lblLineNumber: function () { return this._lblLineNumber; }, set_lblLineNumber: function (v) { if (this._lblLineNumber !== v) this._lblLineNumber = v; },
    get_pnlLineDetail: function () { return this._pnlLineDetail; }, set_pnlLineDetail: function (v) { if (this._pnlLineDetail !== v) this._pnlLineDetail = v; },
    get_pnlLoadingLineDetail: function () { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function (v) { if (this._pnlLoadingLineDetail !== v) this._pnlLoadingLineDetail = v; },
    get_pnlLineDetailError: function () { return this._pnlLineDetailError; }, set_pnlLineDetailError: function (v) { if (this._pnlLineDetailError !== v) this._pnlLineDetailError = v; },
    get_lblSubTotal: function () { return this._lblSubTotal; }, set_lblSubTotal: function (v) { if (this._lblSubTotal !== v) this._lblSubTotal = v; },
    get_lblTax: function () { return this._lblTax; }, set_lblTax: function (v) { if (this._lblTax !== v) this._lblTax = v; },
    get_lblFreight: function () { return this._lblFreight; }, set_lblFreight: function (v) { if (this._lblFreight !== v) this._lblFreight = v; },
    get_lblTotal: function () { return this._lblTotal; }, set_lblTotal: function (v) { if (this._lblTotal !== v) this._lblTotal = v; },
    get_ibtnConfirm: function () { return this._ibtnConfirm; }, set_ibtnConfirm: function (v) { if (this._ibtnConfirm !== v) this._ibtnConfirm = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.callBaseMethod(this, "initialize");

        //data
        this._strDataPath = "controls/Nuggets/CreditLines";
        this._strDataObject = "CreditLines";

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));

        //other controls
        this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));
        $addHandler(this._hypPrev, "click", Function.createDelegate(this, this.prevLine));
        $addHandler(this._hypNext, "click", Function.createDelegate(this, this.nextLine));
        this._frmEdit = $find(this._aryFormIDs[1]);
        //alert(this._hidInvoiceNumber);
        //add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        //edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[1]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.hideEditForm));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }

        //delete form
        if (this._ibtnDelete) {
            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[2]);
            this._frmDelete.addCancel(Function.createDelegate(this, this.hideDeleteForm));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.deleteComplete));
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.hideDeleteForm));
        }

        //delete form
        if (this._ibtnConfirm) {
            $R_IBTN.addClick(this._ibtnConfirm, Function.createDelegate(this, this.showConfirmForm));
            this._frmConfirm = $find(this._aryFormIDs[3]);
            this._frmConfirm.addCancel(Function.createDelegate(this, this.hideConfirmForm));
            this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.ConfirmComplete));
            this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));
        }

        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._hypPrev) $clearHandlers(this._hypPrev);
        if (this._hypNext) $clearHandlers(this._hypNext);
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
        if (this.tbl) this.tbl.dispose();
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmDelete) this._frmDelete.dispose();
        this.intCreditID = null;
        this.intLineID = null;
        this.ibtnAdd = null;
        this.ibtnEdit = null;
        this.ibtnDelete = null;
        this.tbl = null;
        this.hypPrev = null;
        this.hypNext = null;
        this.lblLineNumber = null;
        this.pnlLineDetail = null;
        this.pnlLoadingLineDetail = null;
        this.pnlLineDetailError = null;
        this.lblSubTotal = null;
        this.lblTax = null;
        this.lblFreight = null;
        this.lblTotal = null;
        this._blnHubLogin = null;
        this._blnFronClientInvoice = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.callBaseMethod(this, "dispose");
    },

    getData: function () {
        this.enableEditButtons(false);
        $R_FN.showElement(this._pnlLineDetail, false);
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines");
        obj.addParameter("id", this._intCreditID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    enableEditButtons: function (bln) {
        if (bln) {
            if (!Boolean.parse(this._blnHubLogin) && Boolean.parse(this._blnFronClientInvoice)) {
                //if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, this._blnLineLoaded && !this._isEditEnable);
                if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, false);
                if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
                if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
                if (this._ibtnConfirm) $R_IBTN.enableButton(this._ibtnConfirm, false);
            }
            else {
                //if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, this._blnLineLoaded && !this._isEditEnable);
                if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._blnExported);
                if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, this._blnLineLoaded && !this._blnExported);
                if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, this._blnLineLoaded && !this._blnExported);
                if (this._ibtnConfirm) $R_IBTN.enableButton(this._ibtnConfirm, this._CreditLineIds.length > 0 && !this._blnExported);
            }
        } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
            if (this._ibtnConfirm) $R_IBTN.enableButton(this._ibtnConfirm, false);
        }
    },

    tbl_SelectedIndexChanged: function () {
        //$("#divCrn").show();
        //$("#hypShowHideCrn").val("-");
        this._isEditEnable = this._tbl.getSelectedExtraData().ParentCreditLineId;
        //alert(this._isEditEnable);
        this.enableEditButtons(true);
        this._intLineID = this._tbl._varSelectedValue;
        this.getLineData();
        //$R_FN.scrollPageToElement(this._element);
    },

    getDataOK: function (args) {
        this.showLoading(false);
        var result = args._result;
        this._tbl.clearTable();
        if (result.Lines) {
            for (var i = 0; i < result.Lines.length; i++) {
                var row = result.Lines[i];
               // alert(row.ParentCreditLineNo);
                //this._IsPoHub=row.IsPoHub;
                //  var IsCheckBoxEnabled =row.IsPoHub==true?false:(row.IsIpo==false?false:(row.ParentCreditLineId ==false?false:true)) ;
                var IsCheckBoxEnabled = row.IsPoHub == true ? false : (row.IsIpo == false ? false : (row.IsClientInvoiceLineId == false ? false : (row.ParentCreditLineNo == false ? false : (row.ParentCreditLineId == true ? false : true))));
                var aryData = [
				 this.writeCheckbox(row.ID, i, this._tbl),
					$R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.ManufacturerNo, row.Manufacturer, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
					, row.Quantity
					, $R_FN.writeDoubleCellValue(row.Price, row.LC)
					, $R_FN.writeDoubleCellValue(row.Total, row.Tax)
                ];
                var objExtraData = { ParentCreditLineId: row.ParentCreditLineId };
                this._tbl.addRow(aryData, row.ID, row.ID == this._intLineID, objExtraData);
                this.registerCheckBox(row.ID, i, false, IsCheckBoxEnabled, this._tbl);
                var chk = this.getCheckBox(i, this._tbl);
                chk._element.setAttribute("onClick", String.format("$find(\"{0}\").getCheckedCellValue({1},{2});", this._element.id, i, row.ID));
                chk = null;
                row = null;
            }
        }
        this._intLineCount = this._tbl.countRows();
        $R_FN.setInnerHTML(this._lblSubTotal, result.SubTotal);
        $R_FN.setInnerHTML(this._lblTax, result.Tax);
        $R_FN.setInnerHTML(this._lblFreight, result.Freight);
        $R_FN.setInnerHTML(this._lblTotal, result.Total);
        this.showContent(true);
        this.showContentLoading(false);
        this._tbl.resizeColumns();
        this.enableEditButtons(true);
    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    writeCheckbox: function (varID, intIndex, tbl) {
        var strChkID = this.getControlID("chk", intIndex, tbl);
        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /> </div>", strChkID, strChkImageID, "off");
        return str;
    },
    getControlID: function (str, i, tbl) {
        return String.format("{0}_{1}{2}", tbl._element.id, str, i);
    },
    getCheckBox: function (intCheckBox, tbl) {
        return $find(this.getControlID("chk", intCheckBox, tbl));
    },
    registerCheckBox: function (varID, intIndex, blnChecked, blnEnabled, tbl) {

        var strChkID = this.getControlID("chk", intIndex, tbl);

        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);

        var chk = this.getCheckBox(intIndex, tbl);

        if (chk) {
            chk.dispose();
            chk = null;
        }

        eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", [["blnChecked", blnChecked], ["blnEnabled", blnEnabled], ["img", String.format("$get(\"{0}\")", strChkImageID)]], strChkID));

    },

    getCheckedCellValue: function (intIndex, ID) {
        var tbl = this._tbl;
        var chk = this.getCheckBox(intIndex, tbl);
        var IsChecked = chk._blnChecked;
        var tr = tbl._tbl.rows[intIndex];
        if (!tr) return;
        if (IsChecked) {
            this._CreditLineIds.push(ID);
        }
        else {
            this._CreditLineIds.remove(ID);
        }
        if (this._CreditLineIds.length > 0) {
            $R_IBTN.enableButton(this._ibtnConfirm, true);
        }
        else {
            $R_IBTN.enableButton(this._ibtnConfirm, false);
        }
    },

    prevLine: function () {
        var intNewIndex = this._tbl._intSelectedIndex - 1;
        if (intNewIndex < 0) return;
        this._tbl.selectRow(intNewIndex, true);
    },

    nextLine: function () {
        var intNewIndex = this._tbl._intSelectedIndex + 1;
        if (intNewIndex >= this._intLineCount) return;
        this._tbl.selectRow(intNewIndex, true);
    },

    getLineData: function () {
        this._intLineDataCalls += 1;
        this._blnLineLoaded = false;
        this.enableEditButtons(false);
        this.showLoading(true);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        $R_FN.showElement(this._pnlLineDetail, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getLineDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineDataOK: function (args) {
        this._intLineDataCalls -= 1;
        if (this._intLineDataCalls < 1) this.showLoading(false);
        $R_FN.showElement(this._pnlLineDetailError, false);
        var result = args._result;
        this.setFieldValue("ctlQuantity", (result.Quantity == null) ? 0 : result.Quantity);
        this.setFieldValue("ctlPrice", $R_FN.setCleanTextValue(result.Price));
        this.setFieldValue("hidPriceRaw", result.PriceRaw);
        this.setFieldValue("ctlPartNo", $RGT_nubButton_Stock(result.StockNo, result.Part, result.ROHS));
        this.setFieldValue("hidPartNo", $R_FN.setCleanTextValue(result.Part));
        this.setFieldValue("ctlManufacturer", $RGT_nubButton_Manufacturer(result.ManufacturerNo, result.Manufacturer, result.MfrAdvisoryNotes));
        this.setFieldValue("hidManufacturer", $R_FN.setCleanTextValue(result.Manufacturer));
        this.setFieldValue("ctlCustomerPart", $R_FN.setCleanTextValue(result.CustomerPart));
        this.setFieldValue("ctlProduct", $R_FN.setCleanTextValue(result.Product));
        this.setFieldValue("ctlPackage", $R_FN.setCleanTextValue(result.Package));
        this.setFieldValue("ctlROHS", $R_FN.writeROHS(result.ROHS));
        this.setFieldValue("ctlDateCode", $R_FN.setCleanTextValue(result.DC));
        this.setFieldValue("ctlTaxable", result.Taxable);
        this.setFieldValue("ctlLandedCost", result.LandedCost);
        this.setFieldValue("hidLandedCostRaw", result.LandedCostRaw);
        this.setFieldValue("ctlLineNotes", $R_FN.setCleanTextValue(result.LineNotes));
        this._blnLineIsService = result.IsService;
        this._frmEdit._dblCurrencyRateToBase = result.CurrencyRate;
        this._frmEdit._strCurrencyCode = result.CurrencyCode;
        this.setFieldValue("ctlService", $RGT_nubButton_Service(result.ServiceNo, result.Part));
        this.setFieldValue("hidService", $R_FN.setCleanTextValue(result.Part));
        this.setFieldValue("ctlServiceDescription", $R_FN.setCleanTextValue(result.CustomerPart));
        $R_FN.showElement(this._pnlLineDetail, true);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.setInnerHTML(this._lblLineNumber, String.format($R_RES.LineXOfY, this._tbl._intSelectedIndex + 1, this._intLineCount));
        this.showField("ctlPartNo", !this._blnLineIsService);
        this.showField("ctlManufacturer", !this._blnLineIsService);
        this.showField("ctlCustomerPart", !this._blnLineIsService);
        this.showField("ctlProduct", !this._blnLineIsService);
        this.showField("ctlPackage", !this._blnLineIsService);
        this.showField("ctlROHS", !this._blnLineIsService);
        this.showField("ctlDateCode", !this._blnLineIsService);
        this.showField("ctlService", this._blnLineIsService);
        this.showField("ctlServiceDescription", this._blnLineIsService);
        this._blnLineLoaded = true;
        this.enableEditButtons(true);
    },

    getLineDataError: function (args) {
        this._intLineDataCalls -= 1;
        if (this._intLineDataCalls < 1) this.showLoading(false);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLineDetailError, true);
        $R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
    },

    showAddForm: function () {
        this._frmAdd._intCreditID = this._intCreditID;
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function () {
        this.showForm(this._frmAdd, false);
        this._tbl.resizeColumns();
    },

    saveAddComplete: function () {
        this.hideAddForm();
        this._intLineID = this._frmAdd._intLineID;
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    },

    showEditForm: function () {
        this._frmEdit._intLineID = this._intLineID;
        this._frmEdit._blnLineIsService = this._blnLineIsService;
        this._frmEdit.setFieldValue("ctlPartNo", this.getFieldValue("ctlPartNo"));
        this._frmEdit.setFieldValue("ctlManufacturer", this.getFieldValue("hidManufacturer"));
        this._frmEdit.setFieldValue("ctlDateCode", this.getFieldValue("ctlDateCode"));
        this._frmEdit.setFieldValue("ctlPackage", this.getFieldValue("ctlPackage"));
        this._frmEdit.setFieldValue("ctlProduct", this.getFieldValue("ctlProduct"));
        this._frmEdit.setFieldValue("ctlQuantity", this.getFieldValue("ctlQuantity"));
        this._frmEdit.setFieldValue("ctlPrice", this.getFieldValue("hidPriceRaw"));
        this._frmEdit.setFieldValue("ctlService", this.getFieldValue("hidService"));
        this._frmEdit.setFieldValue("ctlServiceDescription", this.getFieldValue("ctlServiceDescription"));
        this._frmEdit.setFieldValue("ctlLandedCost", this.getFieldValue("hidLandedCostRaw"));
        this._frmEdit.setFieldValue("ctlLineNotes", this.getFieldValue("ctlLineNotes"));
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function () {
        this.showForm(this._frmEdit, false);
        this._tbl.resizeColumns();
    },

    saveEditComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },

    showConfirmForm: function () {
        this._frmConfirm._intLineID = this._intLineID;
        this._frmConfirm._CreditLineIds = this._CreditLineIds.toString();

        this.showForm(this._frmConfirm, true);
    },
    hideConfirmForm: function () {
        this.showForm(this._frmConfirm, false);
        this._tbl.resizeColumns();
    },
    ConfirmComplete: function () {
        this.hideConfirmForm();
        this.getData();
    },
    showDeleteForm: function () {
        this._frmDelete._intLineID = this._intLineID;
        this._frmDelete._blnLineIsService = this._blnLineIsService;
        this._frmDelete.setFieldValue("ctlPartNo", this.getFieldValue("ctlPartNo"));
        this._frmDelete.setFieldValue("ctlManufacturer", this.getFieldValue("hidManufacturer"));
        this._frmDelete.setFieldValue("ctlDateCode", this.getFieldValue("ctlDateCode"));
        this._frmDelete.setFieldValue("ctlPackage", this.getFieldValue("ctlPackage"));
        this._frmDelete.setFieldValue("ctlProduct", this.getFieldValue("ctlProduct"));
        this._frmDelete.setFieldValue("ctlService", this.getFieldValue("hidService"));
        this._frmDelete.setFieldValue("ctlServiceDescription", this.getFieldValue("ctlServiceDescription"));
        this.showForm(this._frmDelete, true);
    },

    hideDeleteForm: function () {
        this.showForm(this._frmDelete, false);
        this._tbl.resizeColumns();
    },

    deleteComplete: function () {
        this.hideDeleteForm();
        this.getData();
    }

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
