Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.prototype={get_intDocNo:function(){return this._intDocNo},set_intDocNo:function(n){this._intDocNo!==n&&(this._intDocNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intDocNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/IPOList");this._objData.set_DataObject("IPOList");this._objData.set_DataAction("GetData");this._objData.addParameter("intDocNo",this._intDocNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.IPOList)for(n=0;n<t.IPOList.length;n++)this.addOption(t.IPOList[n].Name,t.IPOList[n].ID,t.IPOList[n].Code)}};Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.IPOList",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);