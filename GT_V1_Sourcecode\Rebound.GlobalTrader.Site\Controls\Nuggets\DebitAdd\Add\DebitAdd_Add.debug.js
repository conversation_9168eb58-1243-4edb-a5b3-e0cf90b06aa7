///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
// 
// RP 04.01.2010:
// - make RaisedBy a required field
//
// RP 18.12.2009:
// - allow passing a company name to initially search for (task 357)
//Marker     Changed by      Date         Remarks
//[001]      Vinay           30/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
//[002]      Vinay           30/10/2012   Add link in the purchaseOrder section to create SRMA and Debit notes
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.initializeBase(this, [element]);
	this._intNewID = 0;
	this._intCompanyID = 0;
	this._intLoginID = 0;
	this._intContactID = 0;
	this._intDivisionID = 0;
	this._intPOID = 0;
	this._intSRMAID = 0;
	this._intBuyerID = 0;
	this._intCurrencyID = 0;
	this._intTaxID = 0;
	this._strCompanyName = "";
	this._strContactName = "";
	this._strSearchCompanyName = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.prototype = {

	get_ctlSelectPO: function() { return this._ctlSelectPO; }, 	set_ctlSelectPO: function(v) { if (this._ctlSelectPO !== v)  this._ctlSelectPO = v; }, 
	get_intCompanyID: function() { return this._intCompanyID; }, 	set_intCompanyID: function(v) { if (this._intCompanyID !== v)  this._intCompanyID = v; }, 
	get_strCompanyName: function() { return this._strCompanyName; }, 	set_strCompanyName: function(v) { if (this._strCompanyName !== v)  this._strCompanyName = v; }, 
	get_intLoginID: function() { return this._intLoginID; }, set_intLoginID: function(v) { if (this._intLoginID !== v) this._intLoginID = v; }, 
	get_intContactID: function() { return this._intContactID; }, 	set_intContactID: function(v) { if (this._intContactID !== v)  this._intContactID = v; }, 
	get_strContactName: function() { return this._strContactName; }, 	set_strContactName: function(v) { if (this._strContactName !== v)  this._strContactName = v; }, 
	get_ibtnSend: function() { return this._ibtnSend; }, 	set_ibtnSend: function(v) { if (this._ibtnSend !== v)  this._ibtnSend = v; }, 
	get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, 	set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v)  this._ibtnSend_Footer = v; }, 
	get_ibtnContinue: function() { return this._ibtnContinue; }, 	set_ibtnContinue: function(v) { if (this._ibtnContinue !== v)  this._ibtnContinue = v; }, 
	get_ibtnContinue_Footer: function() { return this._ibtnContinue_Footer; }, 	set_ibtnContinue_Footer: function(v) { if (this._ibtnContinue_Footer !== v)  this._ibtnContinue_Footer = v; }, 
	get_lblCurrency_Freight: function() { return this._lblCurrency_Freight; }, 	set_lblCurrency_Freight: function(v) { if (this._lblCurrency_Freight !== v)  this._lblCurrency_Freight = v; },
	get_strSearchCompanyName: function() { return this._strSearchCompanyName; }, set_strSearchCompanyName: function(v) { if (this._strSearchCompanyName !== v) this._strSearchCompanyName = v; },
	//[003] code start
	get_intQSPurchaseOrderID: function() { return this._intQSPurchaseOrderID; }, set_intQSPurchaseOrderID: function(v) { if (this._intQSPurchaseOrderID !== v) this._intQSPurchaseOrderID = v; },
	//[003] code end

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.callBaseMethod(this, "initialize");
		this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
		this._ctlMail._ctlRelatedForm = this;
		this.addCancel(Function.createDelegate(this, this.cancelClicked));
		this.addSave(Function.createDelegate(this, this.saveClicked));
		this.addShown(Function.createDelegate(this, this.formShown));
		this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));
		this._ctlSelectPO.addItemSelected(Function.createDelegate(this, this.selectPO));
		this.addFieldCheckBoxClickEvent("ctlSendMail", Function.createDelegate(this, this.chooseIfSendMail));
		var fnContinue = Function.createDelegate(this, this.finishedForm);
		$R_IBTN.addClick(this._ibtnContinue, fnContinue);
		$R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);
		var fnSend = Function.createDelegate(this, this.sendMail);
		$R_IBTN.addClick(this._ibtnSend, fnSend);
		$R_IBTN.addClick(this._ibtnSend_Footer, fnSend);
		//[001] code start
		this.getFieldDropDownData("ctlIncoterm");
		//[001] code end
		
		//[003] code start
		if (this._intQSPurchaseOrderID != null && this._intQSPurchaseOrderID > 0) {
		    this.getPOFromPODetail();
		}
		//[003] code end
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
		if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
		if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
		if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
		if (this._ctlSelectPO) this._ctlSelectPO.dispose();
		this._ctlSelectPO = null;
		this._intCompanyID = null;
		this._strCompanyName = null;
		this._intLoginID = null;
		this._intContactID = null;
		this._strContactName = null;
		this._ibtnSend = null;
		this._ibtnSend_Footer = null;
		this._ibtnContinue = null;
		this._ibtnContinue_Footer = null;
		this._lblCurrency_Freight = null;
		this._strSearchCompanyName = null;
		this._intLoginID = null;
		this._intContactID = null;
		this._intDivisionID = null;
		this._intPOID = null;
		this._intSRMAID = null;
		this._intBuyerID = null;
		this._intCurrencyID = null;
		this._intTaxID = null;
		//[003] code start
		this._intQSPurchaseOrderID = null;
		//[003] code end
		Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.callBaseMethod(this, "dispose");
	},
	
	formShown: function() {
		this.resetSteps();
		if (this._intCompanyID > 0) {
			this.doInitialCompanySearch();
		} else if (this._strSearchCompanyName) {
			this._ctlSelectPO.setFieldValue("ctlCompany", this._strSearchCompanyName);
			this._ctlSelectPO.searchClicked();
		}
		//[003] code start
		if (this._intQSPurchaseOrderID != null && this._intQSPurchaseOrderID > 0) {
		    this.gotoStep(2);
		}
		else {
		    this.gotoStep(1);
		}
		//[003] code end
	},
	
	doInitialCompanySearch: function() {
		if (!this._ctlSelectPO._initialized) setTimeout(Function.createDelegate(this, this.doInitialCompanySearch), 100);
		this._ctlSelectPO.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(this._strCompanyName));
		this._ctlSelectPO.setFieldValue("ctlContact", $R_FN.setCleanTextValue(this._strContactName));
		this._ctlSelectPO.getData();
	},

	getPO: function() {
		this.showPOFieldsLoading(true);
		$R_FN.showElement(this._pnlLines, false);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/POMainInfo");
		obj.set_DataObject("POMainInfo");
		obj.set_DataAction("GetData");
		obj.addParameter("ID", this._intPOID);
		obj.addDataOK(Function.createDelegate(this, this.getPOOK));
		obj.addError(Function.createDelegate(this, this.getPOError));
		obj.addTimeout(Function.createDelegate(this, this.getPOError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getPOOK: function(args) {
		var res = args._result;
		this.setFieldValue("ctlCompany", $R_FN.setCleanTextValue(res.SupplierName));
		this.setFieldValue("ctlPONumber", res.PONumber);
		this.setFieldValue("ctlContact", $R_FN.setCleanTextValue(res.Contact));
		this.setFieldValue("ctlBuyer", $R_FN.setCleanTextValue(res.Buyer));
		this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.Currency));
		this.setFieldValue("ctlTax", $R_FN.setCleanTextValue(res.Tax));
		this.setFieldValue("ctlDivision", $R_FN.setCleanTextValue(res.Division));
		this.setFieldValue("ctlReferenceDate", res.DateOrdered);
		this.setFieldValue("ctlRaisedBy", this._intLoginID);
		this.setFieldValue("ctlFreight", "0.00");
		//[001] code start
		this.setFieldValue("ctlIncoterm", res.IncotermNo);
		//[001] code end
		this.getFieldDropDownData("ctlRaisedBy");
		$R_FN.setInnerHTML(this._lblCurrency_Freight, res.CurrencyCode);
		this._intCompanyID = res.SupplierNo;
		this._intContactID = res.ContactNo;
		this._intDivisionID = res.DivisionNo;
		this._intTaxID = res.TaxNo;
		this._intBuyerID = res.BuyerNo;
		this._intCurrencyID = res.CurrencyNo;
		this.showPOFieldsLoading(false);
	},

	getPOError: function(args) {
		this.showPOFieldsLoading(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	showPOFieldsLoading: function(bln) {
		this.showFieldLoading("ctlCompany", bln);
		this.showFieldLoading("ctlPONumber", bln);
		this.showFieldLoading("ctlContact", bln);
		this.showFieldLoading("ctlBuyer", bln);
		this.showFieldLoading("ctlCurrency", bln);
		this.showFieldLoading("ctlTax", bln);
		this.showFieldLoading("ctlDivision", bln);
		this.showFieldLoading("ctlReferenceDate", bln);
		this.showFieldLoading("ctlRaisedBy", bln);
	},

	cancelClicked: function() {
		$R_FN.navigateBack();
	},
	
	stepChanged: function() {
		var intStep = this._ctlMultiStep._intCurrentStep;
		$R_IBTN.showButton(this._ibtnSend, intStep == 3);
		$R_IBTN.showButton(this._ibtnSend_Footer, intStep == 3);
		$R_IBTN.enableButton(this._ibtnSave, intStep == 2);
		$R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 2);
		$R_IBTN.showButton(this._ibtnSave, intStep != 3);
		$R_IBTN.showButton(this._ibtnSave_Footer, intStep != 3);
		$R_IBTN.showButton(this._ibtnCancel, intStep != 3);
		$R_IBTN.showButton(this._ibtnCancel_Footer, intStep != 3);
		$R_IBTN.showButton(this._ibtnContinue, intStep == 3);
		$R_IBTN.showButton(this._ibtnContinue_Footer, intStep == 3);
		if (intStep == 1) this._ctlSelectPO.resizeColumns();
		this._ctlMultiStep.showSteps(intStep != 3);
		if (intStep == 3) {
			this.getMessageText();
			this.setFieldValue("ctlSendMail", false);
			this.showMailButtons();
		}
	},
	
	selectPO: function() {
		this._intPOID = this._ctlSelectPO.getSelectedID();
		this.getPO();
		this.nextStep(); 
	},
	
	saveClicked: function() {
		if (!this.validateForm()) return;
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/DebitAdd");
		obj.set_DataObject("DebitAdd");
		obj.set_DataAction("AddNew");
		obj.addParameter("RaisedBy", this.getFieldValue("ctlRaisedBy"));
		obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
		obj.addParameter("Instructions", this.getFieldValue("ctlInstructions"));
		obj.addParameter("DebitDate", $R_FN.shortDate());
		obj.addParameter("ReferenceDate", this.getFieldValue("ctlReferenceDate"));
		obj.addParameter("Freight", this.getFieldValue("ctlFreight"));
		obj.addParameter("TaxNo", this._intTaxID);
		obj.addParameter("PurchaseOrderNo", this._intPOID);
		obj.addParameter("SupplierRMANo", this._intSRMAID);
		obj.addParameter("Buyer", this._intBuyerID);
		obj.addParameter("CurrencyNo", this._intCurrencyID);
		obj.addParameter("CMNo", this._intCompanyID);
		obj.addParameter("ContactNo", this._intContactID);
		obj.addParameter("DivisionNo", this._intDivisionID);
		obj.addParameter("SupplierInvoice", this.getFieldValue("ctlSupplierInvoice"));
		obj.addParameter("SupplierReturn", this.getFieldValue("ctlSupplierReturn"));
		obj.addParameter("SupplierCredit", this.getFieldValue("ctlSupplierCredit"));
		//[001] code start
		obj.addParameter("IncotermNo", this.getFieldValue("ctlIncoterm"));
		//[001] code end
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.NewID > 0) {
			this._intNewID = args._result.NewID;
			this.showSaving(false);
			this.showInnerContent(true);
			this.nextStep();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = true;
		if (this._ctlMultiStep._intCurrentStep == 2) {
		    if (!this.checkFieldEntered("ctlRaisedBy")) blnOK = false;
		    //[001] code start
		    if (!this.checkFieldEntered("ctlIncoterm")) blnOK = false;
		    //[001] code end
		}
		if (this._ctlMultiStep._intCurrentStep == 3) {
			if (!this._ctlMail.validateFields()) blnOK = false;
		}
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	showMailButtons: function() {
		var bln = this.getFieldValue("ctlSendMail");
		this.showField("ctlSendMailMessage", bln);
		$R_IBTN.showButton(this._ibtnSend, bln);
		$R_IBTN.showButton(this._ibtnSend_Footer, bln);
		$R_IBTN.showButton(this._ibtnContinue, !bln);
		$R_IBTN.showButton(this._ibtnContinue_Footer, !bln);
	},
	
	chooseIfSendMail: function() {
		this.showMailButtons();
	},
	
	getMessageText: function() {
		Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewDebitNote(this._intNewID, Function.createDelegate(this, this.getMessageTextComplete));
	},
	
	getMessageTextComplete: function(strMsg) {
		this._ctlMail.setValue_Body(strMsg);
		this._ctlMail.setValue_Subject($R_RES.NewDebitNoteAdded);
	},
	
	validateMailForm: function() {
		var blnOK = this._ctlMail.validateFields();
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	sendMail: function() {
		if (!this.validateMailForm()) return;
		Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), this._intNewID, Function.createDelegate(this, this.sendMailComplete));
		$R_IBTN.showButton(this._ibtnSave, false);
		$R_IBTN.showButton(this._ibtnSave_Footer, false);
		$R_IBTN.showButton(this._ibtnSend, false);
		$R_IBTN.showButton(this._ibtnSend_Footer, false);
	},
	
	sendMailComplete: function() {
		this.finishedForm();
	},
	
	finishedForm: function() {
		this._ctlMultiStep.showExplainLabel(false);
		this._ctlMultiStep.showSteps(false);
		$R_IBTN.showButton(this._ibtnSave, false);
		$R_IBTN.showButton(this._ibtnSave_Footer, false);
		$R_IBTN.showButton(this._ibtnSend, false);
		$R_IBTN.showButton(this._ibtnSend_Footer, false);
		this.showSavedOK(true);
		this.onSaveComplete();
	},
	//[002] code start
	getPOFromPODetail: function() {
	    this._intPOID = this._intQSPurchaseOrderID;
	    this.getPO();
	    this.nextStep(); 
	}
	//[002] code end

};

Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
