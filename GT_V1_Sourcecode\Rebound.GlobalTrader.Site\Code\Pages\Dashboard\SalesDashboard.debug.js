///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Dashboard");

Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard = function(el) {
Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.prototype = {

    get_strSaleskUrl: function() { return this._strSaleskUrl; }, set_strSaleskUrl: function(v) { if (this._strSaleskUrl !== v) this._strSaleskUrl = v; },


    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.callBaseMethod(this, "initialize");
    },

    goInit: function() {

        //alert(this._strStockurl);
        document.getElementById("ifrmQlikSalesOrder").src = this._strSaleskUrl;

        Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._strSaleskUrl = null;

        Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.callBaseMethod(this, "dispose");
    }



};
Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard.registerClass("Rebound.GlobalTrader.Site.Pages.Dashboard.SalesDashboard", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
