/*=================================================================================================
Task			UPDATED BY			DATE			ACTION		DESCRIPTION
[BUG-240844]		phudang		08-April-2025		CREATE		240844: Country Not Showing in Global Settings Client
===================================================================================================
*/

-- Step 1: Create a backup table if it doesn't exist
IF OBJECT_ID('dbo.tbClientAddress_Backup_08042025', 'U') IS NULL
BEGIN
    CREATE TABLE dbo.tbClientAddress_Backup_08042025 (
        AddressId INT PRIMARY KEY,
        CountryNo INT
    );

	-- Step 2: Backup original values
	MERGE dbo.tbClientAddress_Backup_08042025 AS target
	USING (
		SELECT AddressId, CountryNo
		FROM dbo.tbClientAddress
		WHERE AddressId IN (2, 6, 7, 9, 10)
	) AS source
	ON target.AddressId = source.AddressId
	WHEN MATCHED THEN 
		UPDATE SET target.CountryNo = source.CountryNo
	WHEN NOT MATCHED THEN
		INSERT (AddressId, CountryNo)
		VALUES (source.AddressId, source.CountryNo);
END;

-- Step 3: Update Script --
-- Singapore
UPDATE dbo.tbClientAddress SET CountryNo = 612 WHERE AddressId = 2;

-- Iterum Engineering - HongKong
UPDATE dbo.tbClientAddress SET CountryNo = 1013 WHERE AddressId = 6;

-- DMCC - United Arab Emirates
UPDATE dbo.tbClientAddress SET CountryNo = 1087 WHERE AddressId = 7;

-- Schweiz AG - Switzerland
UPDATE dbo.tbClientAddress SET CountryNo = 1156 WHERE AddressId = 9;

-- Mission 4 PTY Limited - Australia
UPDATE dbo.tbClientAddress SET CountryNo = 1339 WHERE AddressId = 10;