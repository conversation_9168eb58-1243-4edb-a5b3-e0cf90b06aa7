//Marker     changed by      date         Remarks
//[002]      Vinay          28/04/2015    ESMS Ticket Number. 	228
//[004]      <PERSON><PERSON>        24/08/2018     Adding of All Alternate button.
//[005]      <PERSON><PERSON>  16-Jan-2019    Add View Tree Button.
//[006]      <PERSON>  28-may-2021    Clone requirement with HUBRFQ.

using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;


namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class CustomerRequirementMainInfo : Base
    {

        #region Locals

        protected FlexiDataTable _tbl;
        protected IconButton _ibtnEdit;
        protected IconButton _ibtnQuote;
        protected IconButton _ibtnAdd;
        protected IconButton _ibtnDelete;

        //[004] code start
        protected IconButton _ibtnAddAll;
        //[004] code end
        protected IconButton _ibtnClose;
        //[002] code start
        protected IconButton _ibtnReqPrint;
        protected IconButton _ibtnReqEccnPrint;
        //[002] code end
        //[003] BOM code start
        protected IconButton _ibtnExportPurchaseHUB;
        //[003] BOM code end
        protected IconButton _ibtnMarkPossible;
        //[005] BOM code start
        protected IconButton _ibtnViewTree;
        //[005] BOM code end

        //[006] Clone requirement with HUBRFQ
        protected IconButton _ibtnPrintLabel;
        protected Panel _pnlLabelTootTip;
        protected Panel _pnlLabel;
        protected HyperLink _hypCloneHUBRFQ;
        protected HyperLink _hypCloneHUB;
        //end


        #endregion

        #region Properties

        private string _strCustomerRequirementNumber;
        public string CustomerRequirementNumber
        {
            get { return _strCustomerRequirementNumber; }
            set { _strCustomerRequirementNumber = value; }
        }

        private bool _blnCanAddAlternate;
        public bool CanAddAlternate
        {
            get { return _blnCanAddAlternate; }
            set { _blnCanAddAlternate = value; }
        }

        private bool _blnCanEdit = true;
        public bool CanEdit
        {
            get { return _blnCanEdit; }
            set { _blnCanEdit = value; }
        }

        private bool _blnCanQuote = true;
        public bool CanQuote
        {
            get { return _blnCanQuote; }
            set { _blnCanQuote = value; }
        }

        private bool _blnCanClose = true;
        public bool CanClose
        {
            get { return _blnCanClose; }
            set { _blnCanClose = value; }
        }

        //[002] code start
        private bool _blnCanPrint = true;
        public bool CanPrint
        {
            get { return _blnCanPrint; }
            set { _blnCanPrint = value; }
        }
        //[002] code end
        private bool _blnCanDelete = true;
        public bool CanDelete
        {
            get { return _blnCanDelete; }
            set { _blnCanDelete = value; }
        }

        private bool _blnCanPrintLabel = true;
        public bool CanPrintLabel
        {
            get { return _blnCanPrintLabel; }
            set { _blnCanPrintLabel = value; }
        }
        private bool _IsGSAEditPermission = true;
        public bool IsGSAEditPermission
        {
            get { return _IsGSAEditPermission; }
            set { _IsGSAEditPermission = value; }
        }
        private bool _IsDiffrentClient = false;
        public bool IsDiffrentClient
        {
            get { return _IsDiffrentClient; }
            set { _IsDiffrentClient = value; }
        }
        private bool _IsGSA = false;
        public bool IsGSA
        {
            get { return _IsGSA; }
            set { _IsGSA = value; }
        }
        #endregion

        #region Overrides

        protected override void OnInit(EventArgs e)
        {
            TitleText = Functions.GetGlobalResource("Nuggets", "CustomerRequirementMainInfo");
            base.OnInit(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            AddScriptReference("Controls.Nuggets.CusReqMainInfo.CusReqMainInfo.js");
            _ibtnEdit = FindIconButton("ibtnEdit");
            _ibtnQuote = FindIconButton("ibtnQuote");
            _ibtnAdd = FindIconButton("ibtnAdd");
            _ibtnAddAll = FindIconButton("ibtnAddAll");//[004]
            _ibtnClose = FindIconButton("ibtnClose");
            //[002] code start
            _ibtnReqPrint = FindIconButton("ibtnReqPrint");
            _ibtnReqEccnPrint = FindIconButton("ibtnReqEccnPrint");
            //[002] code end
            //[003] BOM code start
            _ibtnExportPurchaseHUB = (IconButton)FindIconButton("ibtnExportPurchaseHUB");
            //[003] BOM code end
            _ibtnMarkPossible = (IconButton)FindIconButton("ibtnMarkPossible");
            _ibtnViewTree = FindIconButton("ibtnViewTree");//[006]
            _ibtnDelete = FindIconButton("ibtnDelete");
            
            _ibtnPrintLabel = FindIconButton("ibtnPrintLabel");
            _pnlLabelTootTip = (Panel)Functions.FindControlRecursive(this, "pnlLabelTootTip");
            _pnlLabel = (Panel)FindContentControl("pnlLabel");
            _hypCloneHUBRFQ = (HyperLink)Functions.FindControlRecursive(this, "hypCloneHUBRFQ");
            _hypCloneHUB = (HyperLink)Functions.FindControlRecursive(this, "hypCloneHUB");
            SetupTable();
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e)
        {
            Boolean IsIPOHUB = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["IsIPOHUB"]);
            _ibtnEdit.Visible = _blnCanEdit;
            _ibtnQuote.Visible = _blnCanQuote;
            _ibtnAdd.Visible = _blnCanAddAlternate;
            _ibtnClose.Visible = _blnCanClose;
            _ibtnReqPrint.Visible = _blnCanPrint;
            _ibtnReqEccnPrint.Visible = _blnCanPrint;
            _ibtnExportPurchaseHUB.Visible = IsIPOHUB;
            _ibtnDelete.Visible = _blnCanDelete;
            _ibtnPrintLabel.Visible = true;
            if (_IsDiffrentClient == true)
            {
                if (SessionManager.IsGSA == true)
                {
                    if (_IsGSAEditPermission == true)
                    {
                        _ibtnEdit.Visible = true;
                        _ibtnQuote.Visible = true;
                        _ibtnAdd.Visible = true;
                        _ibtnClose.Visible = true;
                        _ibtnReqPrint.Visible = true;
                        _ibtnReqEccnPrint.Visible = true;
                        _ibtnExportPurchaseHUB.Visible = IsIPOHUB;
                        _ibtnDelete.Visible = true;
                        _ibtnPrintLabel.Visible = true;
                        _ibtnMarkPossible.Visible = true;
                    }
                    else
                    {
                        _ibtnEdit.Visible = false;
                        _ibtnQuote.Visible = false;
                        _ibtnAdd.Visible = false;
                        _ibtnClose.Visible = false;
                        _ibtnReqPrint.Visible = false;
                        _ibtnReqEccnPrint.Visible = false;
                        _ibtnExportPurchaseHUB.Visible = false;
                        _ibtnDelete.Visible = false;
                        _ibtnPrintLabel.Visible = false;
                        _ibtnMarkPossible.Visible = false;
                    }
                }
            }
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        #region Methods

        private void SetupTable()
        {
            _tbl = (FlexiDataTable)ctlDesignBase.FindContentControl("tbl");
            _tbl.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(20), false, HorizontalAlign.Center));
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tbl.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tbl.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
            _tbl.Columns.Add(new FlexiDataColumn("Quantity", "DateRequired", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tbl.Columns.Add(new FlexiDataColumn("TargetPrice"));

        }

        private void SetupScriptDescriptors()
        {
            _IsGSA = SessionManager.IsGSA.Value;
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementMainInfo", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _objQSManager.CustomerRequirementID);
            if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
            if (_blnCanQuote) _scScriptControlDescriptor.AddElementProperty("ibtnQuote", _ibtnQuote.ClientID);
            if (_blnCanAddAlternate) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnAddAll", _ibtnAddAll.ClientID);//[004]
            if (_blnCanClose) _scScriptControlDescriptor.AddElementProperty("ibtnClose", _ibtnClose.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", FindContentControl("pnlLoadingLineDetail").ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetailError", FindContentControl("pnlLineDetailError").ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLineDetail", FindContentControl("pnlLineDetail").ClientID);
            _scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _objQSManager.CustomerRequirementID);
            _scScriptControlDescriptor.AddProperty("strCustomerRequirementNumber", _strCustomerRequirementNumber);
            _scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypPrev", FindContentControl("hypPrev").ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypNext", FindContentControl("hypNext").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblLineTitle", FindContentControl("lblLineTitle").ClientID);
            if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
            //[002] code start
            if (_blnCanPrint) _scScriptControlDescriptor.AddElementProperty("ibtnReqPrint", _ibtnReqPrint.ClientID);
            if (_blnCanPrint) _scScriptControlDescriptor.AddElementProperty("ibtnReqEccnPrint", _ibtnReqEccnPrint.ClientID);
            //[002] code end

            //[003] BOM code start
            _scScriptControlDescriptor.AddElementProperty("ibtnExportPurchaseHUB", _ibtnExportPurchaseHUB.ClientID);
            //[003] BOM code end
            _scScriptControlDescriptor.AddElementProperty("ibtnMarkPossible", _ibtnMarkPossible.ClientID);
            //[005] code start
            _scScriptControlDescriptor.AddElementProperty("ibtnViewTree", _ibtnViewTree.ClientID);
            //[005] code end
            _scScriptControlDescriptor.AddElementProperty("ibtnPrintLabel", _ibtnPrintLabel.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLabelTootTip", _pnlLabelTootTip.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlLabel", _pnlLabel.ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypCloneHUBRFQ", _hypCloneHUBRFQ.ClientID);
            _scScriptControlDescriptor.AddElementProperty("hypCloneHUB", _hypCloneHUB.ClientID);
            _scScriptControlDescriptor.AddProperty("IsDiffrentClient", _IsDiffrentClient);
            _scScriptControlDescriptor.AddProperty("IsGSAEditPermission", _IsGSAEditPermission);
            _scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);


        }

        #endregion

    }
}
