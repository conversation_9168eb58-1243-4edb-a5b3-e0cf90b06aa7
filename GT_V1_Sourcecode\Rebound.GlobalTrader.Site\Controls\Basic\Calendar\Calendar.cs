using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Globalization;
using System.Threading;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Calendar runat=server></{0}:Calendar>")]
	public class Calendar : Panel, IScriptControl, INamingContainer {

		#region Locals

		private Image _imgCal;
		private Panel _pnlCalendar;
		protected ScriptManager _sm;
		private Table _tbl;
		private HyperLink _hypOff;

		#endregion

		#region Properties

		/// <summary>
		/// Control to update with the date value
		/// </summary>
		private TextBox _ctlRelatedTextBox;

		/// <summary>
		/// Control to update with the date value
		/// </summary>
		private string _strRelatedTextBoxID;
		public string RelatedTextBoxID {
			get { return _strRelatedTextBoxID; }
			set { _strRelatedTextBoxID = value; }
		}

		/// <summary>
		/// Should the result be output as a Birthday, e.g. "09 February"
		/// </summary>
		private bool _blnFormatAsBirthday = false;
		public bool FormatAsBirthday {
			get { return _blnFormatAsBirthday; }
			set { _blnFormatAsBirthday = value; }
		}

		#endregion

		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("Calendar.css");
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			CssClass = "calendarOuter";
			Panel pnlControls = ControlBuilders.CreatePanelInsideParent(this, "calendarControls");
			_imgCal = ControlBuilders.CreateImageInsideParent(pnlControls, "calImage", "~/images/x.gif");
			_pnlCalendar = ControlBuilders.CreatePanelInsideParent(this, "calendarPanel");
			Panel pnlHeader = ControlBuilders.CreatePanelInsideParent(_pnlCalendar, "calendarTopHeader");
			ControlBuilders.CreateLiteralInsideParent(pnlHeader, "&nbsp;");
			_hypOff = ControlBuilders.CreateHyperLinkInsideParent(pnlHeader, "", "javascript:void(0);", "x");
			_tbl = ControlBuilders.CreateTableInsideParent(_pnlCalendar, "calendar");

			Functions.SetCSSVisibility(_pnlCalendar, false);
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			_ctlRelatedTextBox = (TextBox)Functions.FindControlRecursive(this.Parent.Parent, _strRelatedTextBoxID);
			_ctlRelatedTextBox.ReadOnly = true;
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.Calendar.Calendar", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Calendar", this.ClientID);
			descriptor.AddElementProperty("txt", _ctlRelatedTextBox.ClientID);
			descriptor.AddElementProperty("imgCal", _imgCal.ClientID);
			descriptor.AddElementProperty("pnlCalendar", _pnlCalendar.ClientID);
			descriptor.AddElementProperty("tbl", _tbl.ClientID);
			descriptor.AddElementProperty("hypOff", _hypOff.ClientID);
			descriptor.AddProperty("blnFormatAsBirthday", _blnFormatAsBirthday);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion

	}

}