﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER VIEW [dbo].[Exs_vwInvoice] AS
SELECT  Invoice.CustName as 'Invoice.CustName', Invoice.Quantity as 'Invoice.Quantity', Invoice.[Unit Sale] as 'Invoice.UnitSale',
Invoice.PartNumber as 'Invoice.PartNumber',Invoice.InvoiceDate as 'Invoice.InvoiceDate', Invoice.FullPN as 'Invoice.FullPN', 
Purchase.[Purchase Order Number] as 'Purchase.PurchaseOrderNumber', 
Purchase.Company as 'Purchase.Company', Purchase.Part as 'Purchase.Part', Purchase.Quantity as 'Purchase.Quantity', 
Purchase.Price as 'Purchase.Price', Purchase.Currency as 'Purchase.Currency', Purchase.Value as 'Purchase.Value', Purchase.[Landed Cost] as 'Purchase.LandedCost', 
Purchase.Cost as 'Purchase.Cost', Invoice.InvoiceNo as 'Invoice.InvoiceNo', Invoice.ClientName as 'Invoice.ClientName', 
Invoice.ClientNo as 'Invoice.ClientNo', Invoice.Sls as 'Invoice.Sls'
 FROM vw_re_Invoice Invoice 
LEFT JOIN  vw_re_Purchase Purchase ON (Invoice.PurchaseOrderLineId = Purchase.PurchaseOrderLineId) AND (Invoice.GoodsInLineNo = Purchase.GoodsInLineId)
WHERE (((Invoice.Quantity)>0) AND ((Invoice.[Unit Sale])>0));

GO


