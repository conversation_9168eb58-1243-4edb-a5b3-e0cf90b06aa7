﻿//Marker     Changed by                 Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>           20/09/2021     Add new dropdown for supplier contact.
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class SupplierContact : Base {

        private int _intCompanyID;
        public int CompanyID
        {
            get { return _intCompanyID; }
            set { _intCompanyID = value; }
        }
        protected override void OnInit(EventArgs e) {
			CanAddTo = false;
            //[001] code start
			//IncludeNoValue = false;
			//NoValue_Value = "0";
			//InitialValue = "0";
            //[001] code end
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("SupplierContact");
            AddScriptReference("Controls.DropDowns.SupplierContact.SupplierContact");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierContact", ClientID);
            _scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
            base.OnLoad(e);
		}

	}
}