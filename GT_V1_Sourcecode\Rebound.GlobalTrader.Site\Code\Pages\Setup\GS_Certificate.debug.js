///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.prototype = {

    get_ctlCertificatecategory: function() { return this._ctlCertificatecategory; }, set_ctlCertificatecategory: function(v) { if (this._ctlCertificatecategory !== v) this._ctlCertificatecategory = v; },
    get_ctlCertificate: function() { return this._ctlCertificate; }, set_ctlCertificate: function(v) { if (this._ctlCertificate !== v) this._ctlCertificate = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlCertificatecategory) this._ctlCertificatecategory.addSelectCategory(Function.createDelegate(this, this.ctlCertificatecategory_SelectCategory));
        // if (this._ctlCertificate) this._ctlCertificate.addChangedData(Function.createDelegate(this, this.ctlCurrencyRates_ChangedData));
        Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlCertificatecategory) this._ctlCertificatecategory.dispose();
        if (this._ctlCertificate) this._ctlCertificate.dispose();
        this._ctlCertificatecategory = null;
        this._ctlCertificate = null;
        Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.callBaseMethod(this, "dispose");
    },

    ctlCertificatecategory_SelectCategory: function() {
        this._ctlCertificate._intCertificatecategoryID = this._ctlCertificatecategory._intCertificateCategoryID;
        this._ctlCertificate._tbl.resizeColumns();
        this._ctlCertificate.show(true);
        this._ctlCertificate.refresh();
    }

};

Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_Certificate", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
