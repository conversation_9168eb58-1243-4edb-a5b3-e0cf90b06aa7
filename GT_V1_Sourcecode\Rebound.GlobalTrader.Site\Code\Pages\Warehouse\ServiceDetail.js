Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.ServiceDetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.ServiceDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.ServiceDetail.prototype={get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.ServiceDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addGotData(Function.createDelegate(this,this.ctlMainInfo_GotData));Rebound.GlobalTrader.Site.Pages.Warehouse.ServiceDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlPageTitle=null,this._ctlMainInfo=null,Rebound.GlobalTrader.Site.Pages.Warehouse.ServiceDetail.callBaseMethod(this,"dispose"))},ctlMainInfo_GotData:function(){this._ctlPageTitle.updateTitle(this._ctlMainInfo.getFieldValue("ctlName"))}};Rebound.GlobalTrader.Site.Pages.Warehouse.ServiceDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ServiceDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);