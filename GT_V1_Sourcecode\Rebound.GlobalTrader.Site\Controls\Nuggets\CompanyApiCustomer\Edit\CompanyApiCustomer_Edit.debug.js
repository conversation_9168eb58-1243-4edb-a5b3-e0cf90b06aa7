///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.initializeBase(this, [element]);
    this._intLineID = -1;
    this._intCompanyID = 0;
    this.txtemailcheck = false;
    this.txtphncheck = false;
    this.txtpswrd1check = false;
    this.txtpswrd2check = false;
    /*this.confirmpswrd = false;*/
    this.txtContactPerson = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.prototype = {

    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intLineID = null;
        this._intCompanyID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.callBaseMethod(this, "dispose");
    },

    formShown: function () {

        if (this._blnFirstTimeShown) {
            // this.getFieldDropDownData("ctlCategory");
            // alert("test");
            //$find(this.getField("ctlCategory").ControlID).addChanged(Function.createDelegate(this, this.getCertificateByCategory));
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }

    },
    //getCertificateByCategory: function() {
    //    this.showCertificateFieldsLoading(true);
    //    this.getFieldComponent("ctlCertificate")._intCategoryID = this.getFieldValue("ctlCategory");
    //    this.getFieldDropDownData("ctlCertificate");
    //    this.showCertificateFieldsLoading(false);
    //},
    //showCertificateFieldsLoading: function(bln) {
    //    this.showFieldLoading("ctlCertificate", bln);
    //},

    //saveClicked: function () {
    //    debugger;
    //    if (!this.validateForm()) return;
    //    var obj = new Rebound.GlobalTrader.Site.Data();
    //    obj.set_PathToData("controls/Nuggets/CompanyApiCustomer");
    //    obj.set_DataObject("CompanyApiCustomer");
    //    obj.set_DataAction("SaveEdit");
    //    obj.addParameter("id", this._intLineID);
    //    obj.addParameter("Email", this.getFieldValue("ctlEmail"));
    //    obj.addParameter("Mobile", this.getFieldValue("ctlMobile"));
    //    obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
    //    obj.addParameter("Password", this.getFieldValue("ctlPassword"));
    //    obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
    //    obj.addError(Function.createDelegate(this, this.saveEditError));
    //    obj.addTimeout(Function.createDelegate(this, this.saveEditError));
    //    $R_DQ.addToQueue(obj);
    //    $R_DQ.processQueue();
    //    obj = null;
    //},
    saveClicked: function () {
        var test = this.validatedetails();
        //if (test == 3) {
        if (this.txtpswrd1check == true) {
            //if (!this.validateForm()) return;
            debugger;
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/CompanyApiCustomer");
            obj.set_DataObject("CompanyApiCustomer");
            obj.set_DataAction("SaveEdit");
            obj.addParameter("id", this._intLineID);
            obj.addParameter("Email", this.getFieldValue("ctlEmail"));
            var countryCodeVal = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlMobile_ctl03_txtCountryCode").val();
            obj.addParameter("CountryCode", countryCodeVal);
            obj.addParameter("ContactName", this.getFieldValue("ctlContactPerson"));
            obj.addParameter("Mobile", this.getFieldValue("ctlMobile"));
            obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
            obj.addParameter("IsBomUser", this.getFieldValue("ctlBomUser"));
            obj.addParameter("InSupUser", this.getFieldValue("ctlSupplierUser"));
            obj.addParameter("Password", this.getFieldValue("ctlPassword"));
            obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
            obj.addError(Function.createDelegate(this, this.saveEditError));
            obj.addTimeout(Function.createDelegate(this, this.saveEditError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
        } else {
            this.showError(true);
            /* this.alertmassage();*/
            this.resetFormFields();
        }
        //if (this.validateForm()) this.addNew();
        /*this.resetFormFields();*/
    },

    validatedetails: function () {
        debugger;
        var checkflag = false;
        
        var pswrd1 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword_ctl03_txtPassword").val();
        //var pswrd2 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").val();

        if (pswrd1 != "" && pswrd1 != " " && pswrd1 != undefined && pswrd1.length >= 16) {
            checkflag = true;
            this.txtpswrd1check = true;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword").css("background-color", "#56954E");
        }
        else {
            checkflag = false;
            this.txtpswrd1check = false;
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword").css("background-color", "#990000");
        }
        //if (pswrd2 != "" && pswrd2 != " " && pswrd2 != undefined && pswrd2.length >= 16) {
        //    checkflag = true;
        //    this.txtpswrd2check = true;
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword").css("background-color", "#56954E");
        //}
        //else {
        //    checkflag = false;
        //    this.txtpswrd2check = false;
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword").css("background-color", "#990000");
        //}
        //if (pswrd1 != "" && pswrd1 != " " && pswrd1 != undefined  && pswrd1.length >= 16) {
        //    checkflag = true;
        //    this.confirmpswrd = true;
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword").css("background-color", "#56954E");
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword").css("background-color", "#56954E");
        //}
        //else {
        //    checkflag = false;
        //    this.confirmpswrd = false;
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword").css("background-color", "#990000");
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword").css("background-color", "#990000");
        //}
        /*var phoneno = /[0-9]/g;*/
        /* var phoneno = /^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im;*/
        //var temp = 0;
        //var password1 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword_ctl03_txtPassword").val();
        //var password2 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").val();
        //var textbox = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlEmail_ctl04_txtEmail").val();
        //var textbox2 = $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlMobile_ctl03_txtMobile").val();
        //if (password1 === '') {

        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").css("background-color", "#990000");
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword_ctl03_txtPassword").css("background-color", "#990000");
        //}
        //var test2 = this.validate10digitnumber(textbox2);
        //var email = this.validateEmail(textbox);
        //if (password1 == password2) {
        //    temp++;
        //}
        //else {
        //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").css("background-color", "#990000");
        //    temp--;
        //}
        ///* !isNaN(textbox2) && Number.isInteger(textbox2) && textbox2.trim() !== '' && textbox2 === textbox2.toString() && !textbox2.includes('.')*/
        //if (test2 == true && textbox2 !== '') {
        //    //$('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile').next().show();
        //    //return false;
        //    temp++
        //}
        //else {
        //    temp--;
        //}
        //if (email == true) {
        //    //$('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile').next().show();
        //    //return false;
        //    temp++
        //}
        //else {
        //    temp--;
        //}
        ////if (textbox = '@') {
        ////    //$('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail_ctl04_txtEmail').next().show();
        ////    //return false;
        ////    temp++
        ////}
        //return temp;
    },
    validateEmail: function (string) {
        debugger;
        if (! /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(string)) {
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlEmail_ctl04_txtEmail").css("background-color", "#990000");
            return false;
        } else {
            $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlEmail_ctl04_txtEmail").css("background-color", "#56954E");
            return true;
        }



    },
    //validate10digitnumber: function (string) {
    //    if (!/^[\+]?[(]?[0-9]{3}[)]?[-\s\.]?[0-9]{3}[-\s\.]?[0-9]{4,6}$/im.test(string)) {
    //        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlMobile_ctl03_txtMobile").css("background-color", "#990000");
    //        return false;
    //    }
    //    $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlMobile_ctl03_txtMobile").css("background-color", "#56954E");
    //    return true;
    //},

    validate10digitnumber: function (string_or_number) {
        var mobile = string_or_number;
        if (mobile.length != 10) {
            return false;
        }
        intRegex = /[0-9 -()+]+$/;
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#56954E");
        is_mobile = true;
        for (var i = 0; i < 10; i++) {
            if (intRegex.test(mobile[i])) {
                continue;
            }
            else {
                $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color", "#990000");
                is_mobile = false;
                break;
            }
        }
        return is_mobile;

    },




    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    }
};

$(document).ready(function () {


    $("#checkbox2").click(function () {
        debugger;
        var dp = $('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword').attr('type');
        if (dp == 'password') {
            $('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword').attr('type', 'text');
        } else {
            $('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword').attr('type', 'password');

        }
    });

    $("#btngenpass2").click(function () {
        debugger;
        var pq = generateP();
        /* alert(pq);*/
        /* document.getElementById("ctlPassword").setAttribute('value', pq);*/
        $("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword_ctl03_txtPassword").val(pq);
       /* $('#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword_ctl03_txtPassword').attr('type', 'text');*/
        /* this.setFieldValue("#ctlPassword", generateP());*/
    });


    function generateP() {
        var pass = '';
        var str = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ' +
            'abcdefghijklmnopqrstuvwxyz123456789';

        for (let i = 1; i < 18; i++) {
            var char = Math.floor(Math.random()
                * str.length + 1);

            pass += str.charAt(char)
        }

        return pass;
    };
    //    $.validator.addMethod
    //    $("#frmCertificate").validate({
    //        rules: {
    //            //This section we need to place our custom rule for the control.  
    //            <%=txtEmail.UniqueID %>:(
    //        required: true
    //    )
    //        },
    //        messages: {
    //            //This section we need to place our custom validation message for each control.  
    //             <%=txtName.UniqueID %>: {
    //    required: "Name is required."
    //},
    //        },
    //    });
    //});

});

Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
