﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-223239]		An.TranTan			12-Dev-2024		CREATE			Get bulk edit log for strategic stock
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_get_BulkEditLog_Epo] 
	@OrderBy INT = 1
	,@SortDir INT = 1
	,@PageIndex INT = 0
	,@PageSize INT = 10
	,@PartSearch NVARCHAR(30) = NULL
	,@UpdatedBy INT = NULL
	,@DateFrom DATETIME = NULL
	,@DateTo DATETIME = NULL
AS
DECLARE @StartPage INT ,@EndPage INT
SET @StartPage = (@PageIndex * @PageSize + 1)
SET @EndPage = ((@PageIndex + 1) * @PageSize)

IF (@DateFrom IS NOT NULL)
	SET @DateFrom = dbo.ufn_get_start_of_day_for_date(@DateFrom);

IF (@DateTo IS NOT NULL)
	SET @DateTo = dbo.ufn_get_end_of_day_for_date(@DateTo);

;WITH cteSearch
AS (
	SELECT bel.BulkEpoAuditLogId AS SourcingAuditLogId
		,bel.EpoNo AS SourcingId
		,bel.BatchNo
		,'StrategicStock' AS SourcingType
		,bel.Part AS PartNo
		,bel.[Action]
		,bel.CreatedByName AS UpdatedBy
		,bel.DLUP AS UpdatedDate
		,bel.OldValue
		,ROW_NUMBER() OVER (
			ORDER BY          
				CASE WHEN @OrderBy = 1 AND @SortDir = 2 THEN bel.BatchNo END DESC
				,CASE WHEN @OrderBy = 1 THEN bel.BatchNo END
				,CASE WHEN @OrderBy = 2 AND @SortDir = 2 THEN bel.Part END DESC
				,CASE WHEN @OrderBy = 2 THEN bel.Part END
				,CASE WHEN @OrderBy = 3 AND @SortDir = 2 THEN bel.[Action] END DESC
				,CASE WHEN @OrderBy = 3 THEN bel.[Action] END
				,CASE WHEN @OrderBy = 4 AND @SortDir = 2 THEN bel.CreatedByName END DESC
				,CASE WHEN @OrderBy = 4 THEN bel.CreatedByName END
				,CASE WHEN @OrderBy = 5 AND @SortDir = 2 THEN bel.DLUP END DESC
				,CASE WHEN @OrderBy = 5 THEN bel.DLUP END
			) AS RowNum
	FROM BorisGlobalTraderImports.dbo.tbBulkEpoAuditLog bel WITH(NOLOCK)
	WHERE (@PartSearch IS NULL OR bel.FullPart LIKE @PartSearch)
		AND (@UpdatedBy IS NULL OR bel.CreatedBy = @UpdatedBy)
		AND (@DateFrom IS NULL OR bel.DLUP >= @DateFrom)
		AND (@DateTo IS NULL OR bel.DLUP <= @DateTo)
	)
--final results
SELECT *
	,( SELECT count(*) FROM cteSearch) AS RowCnt
FROM cteSearch
WHERE RowNum BETWEEN @StartPage AND @EndPage
ORDER BY RowNum
GO

/*
exec usp_get_BulkEditLog_Epo
*/
