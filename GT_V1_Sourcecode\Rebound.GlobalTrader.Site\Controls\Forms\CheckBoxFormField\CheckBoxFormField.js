Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.initializeBase(this,[n]);this._blnVisible=!0};Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.prototype={get_tdLeft:function(){return this._tdLeft},set_tdLeft:function(n){this._tdLeft!==n&&(this._tdLeft=n)},get_chk:function(){return this._chk},set_chk:function(n){this._chk!==n&&(this._chk=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._chk&&this._chk.dispose(),this._tdLeft=null,this._chk=null,Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.callBaseMethod(this,"dispose"),this.isDisposed=!0)},setText:function(n){$R_FN.setInnerHTML(this._tdLeft,n)},setValue:function(n){this._chk.setValue(n)},getValue:function(){return this._chk.getValue()},show:function(n){this._blnVisible=n;$R_FN.showElement(this.get_element(),n)},resetField:function(){},enableCheckBox:function(n){this.enableButton(n)}};Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CheckBoxFormField",Sys.UI.Control,Sys.IDisposable);