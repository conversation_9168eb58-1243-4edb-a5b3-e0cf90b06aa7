//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/07/2012   This need for Rebound- Invoice bulk Emailer
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class ContactMainInfo : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					case "SaveEdit": SaveEdit(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		public JsonObject GetData(Contact con) {           
			JsonObject jsn = null;
			if (con != null) {
				jsn = new JsonObject();
				jsn.AddVariable("FirstName", con.FirstName);
				jsn.AddVariable("Surname", con.LastName);
				jsn.AddVariable("JobTitle", con.Title);
				jsn.AddVariable("Tel", con.Telephone);
				jsn.AddVariable("Fax", con.Fax);
				jsn.AddVariable("TelExt", con.Extension);
				jsn.AddVariable("HomeTel", con.HomeTelephone);
				jsn.AddVariable("MobileTel", con.MobileTelephone);
				jsn.AddVariable("Email", con.EMail);
				jsn.AddVariable("IsEmailTextOnly", con.TextOnlyEmail);
				jsn.AddVariable("Nickname", con.Salutation);
                jsn.AddVariable("CompanyAddress", con.AddressNo);
				jsn.AddVariable("DLUP", Functions.FormatDLUP(con.DLUP, con.UpdatedBy));
                //[001] code start
                jsn.AddVariable("FinanceContact", con.FinanceContact);
                jsn.AddVariable("Inactive", con.Inactive);
                jsn.AddVariable("CompanyNo", con.CompanyNo);
                jsn.AddVariable("IsSendShipmentNotification", con.IsSendShipmentNotification);
                //[001] code end
                if (con.AddressNo > 0)
                {
                    Address addr = Address.Get(con.AddressNo);
                    if (addr != null)
                    {
                        //jsn.AddVariable("PersonalAddress", Functions.ReplaceLineBreaks(AddressManager.ToLongString(addr)));
                        //jsn.AddVariable("AddressName", addr.AddressName);
                        //jsn.AddVariable("Address1", addr.Line1);
                        //jsn.AddVariable("Address2", addr.Line2);
                        //jsn.AddVariable("Address3", addr.Line3);
                        //jsn.AddVariable("Town", addr.City);
                        //jsn.AddVariable("County", addr.County);
                        //jsn.AddVariable("CountryNo", addr.CountryNo);
                        //jsn.AddVariable("Postcode", addr.ZIP);

                     //  string CompanyAdd =addr.AddressName+' '+ Convert.ToString(addr.Line1) + ' ' + addr.City +' '+ Convert.ToString(addr.ZIP);
                       jsn.AddVariable("CompanyAdd", Functions.ReplaceLineBreaks(AddressManager.ToLongString(addr)));
                    }
                    addr = null;
                }
			}
			con = null;
			return jsn;
		}

		private void GetData() {
			BLL.Contact con = BLL.Contact.Get(ID);
			if (con == null) {
				WriteErrorDataNotFound();
			} else {
				OutputResult(GetData(con));
			}
			con = null;
		}

		private void SaveEdit() {
			Contact con = null;
			try {
				bool blnOK = true;
				JsonObject jsn = new JsonObject();
				con = Contact.Get(ID);
				if (con != null) {
					con.FirstName = GetFormValue_String("FirstName");
					con.LastName = GetFormValue_String("Surname");
					con.Title = GetFormValue_String("JobTitle");
					con.Telephone = GetFormValue_String("Tel");
					con.Fax = GetFormValue_String("Fax");
					con.Extension = GetFormValue_String("Extension");
					con.HomeTelephone = GetFormValue_String("HomeTel");
					con.MobileTelephone = GetFormValue_String("MobileTel");
					con.EMail = GetFormValue_String("Email");
					con.TextOnlyEmail = GetFormValue_Boolean("TextOnlyEmail");
					con.Salutation = GetFormValue_String("Nickname");
					con.UpdatedBy = LoginID;
                    con.AddressNo = GetFormValue_Int("AddressNo");
                    //[001] code start
                    con.FinanceContact = GetFormValue_Boolean("FinanceContact");
                    con.Inactive = GetFormValue_Boolean("Inactive");
                    con.IsSendShipmentNotification = GetFormValue_Boolean("IsSendShipmentNotification");
                    //[001] code end
                    //Address - decide whether to insert, update or delete
                    //int intAddressID = GetFormValue_Int("AddressID");
                    //if (intAddressID > 0) {
                    //    if (GetFormValue_Boolean("HasAddress")) {
                    //        Address addr = Address.Get(intAddressID);
                    //        if (addr != null) {
                    //            addr.AddressName = GetFormValue_String("AddressName");
                    //            addr.Line1 = GetFormValue_String("Address1");
                    //            addr.Line2 = GetFormValue_String("Address2");
                    //            addr.Line3 = GetFormValue_String("Address3");
                    //            addr.City = GetFormValue_String("Town");
                    //            addr.County = GetFormValue_String("County");
                    //            addr.CountryNo = GetFormValue_NullableInt("Country");
                    //            addr.ZIP = GetFormValue_String("Postcode");
                    //            addr.UpdatedBy = LoginID;
                    //            addr.Update();
                    //        }
                    //    } else {
                    //        Address.Delete(intAddressID);
                    //        con.AddressNo = null;
                    //    }
                    //} else {
                    //    if (GetFormValue_Boolean("HasAddress")) {
                    //        int intNewAddressID = Address.Insert(
                    //            GetFormValue_String("AddressName")
                    //            , GetFormValue_String("Address1")
                    //            , GetFormValue_String("Address2")
                    //            , GetFormValue_String("Address3")
                    //            , GetFormValue_String("Town")
                    //            , GetFormValue_String("County")
                    //            , ""
                    //            , GetFormValue_NullableInt("Country", null)
                    //            , GetFormValue_String("Postcode")
                    //            , LoginID
                    //        );
                    //        con.AddressNo = intNewAddressID;
                    //    }
                    //}
                    blnOK = con.Update();
				}
				jsn.AddVariable("Result", blnOK);
				OutputResult(jsn);
			} catch (Exception ex) {
				WriteError(ex);
			} finally {
				con = null;
			}
		}

	}
}
