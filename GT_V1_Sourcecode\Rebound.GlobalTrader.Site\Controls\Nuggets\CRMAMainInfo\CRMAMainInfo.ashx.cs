/*
 *Marker    Date        Changed By      Remarks
 *[001]     26/06/2018  <PERSON><PERSON><PERSON>     Save internal log for CRMA
 *[RP-2339]	Ravi		11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens
 */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CRMAMainInfo : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					case "SaveEdit": SaveEdit(); break;
					case "GetReceived": GetReceived(); break;
                    case "SaveCRMAInternalLog": SaveCRMAInternalLog(); break;
                    //[001] start
                    case "GetExpediteHistory": GetExpediteHistory(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}
		/// <summary>
		/// get specific customerRMA by key
		/// </summary>
		public JsonObject GetData(CustomerRma crma) {
			JsonObject jsn = null;
			if (crma != null) {
				jsn = new JsonObject();
				jsn.AddVariable("CRMANumber", crma.CustomerRMANumber);
				jsn.AddVariable("CustomerNo", crma.CompanyNo);
				jsn.AddVariable("Customer", crma.CompanyName);
				string companyNotes = Company.GetAdvisoryNotes(crma.CompanyNo);
				jsn.AddVariable("CustomerAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
				jsn.AddVariable("Contact", crma.ContactName);
				jsn.AddVariable("ContactNo", crma.ContactNo);
				jsn.AddVariable("Warehouse", crma.WarehouseName);
				jsn.AddVariable("WarehouseNo", crma.WarehouseNo);
				jsn.AddVariable("Authoriser", crma.AuthoriserName);
				jsn.AddVariable("AuthorisedBy", crma.AuthorisedBy);
				jsn.AddVariable("Salesman", crma.SalesmanName);
				jsn.AddVariable("SalesmanNo", crma.Salesman);
				jsn.AddVariable("Division", crma.DivisionName);
				jsn.AddVariable("DivisionNo", crma.DivisionNo);
				jsn.AddVariable("InvoiceNo", crma.InvoiceNo);
				jsn.AddVariable("Invoice", crma.InvoiceNumber);
				jsn.AddVariable("TaxNo", crma.TaxNo);
				jsn.AddVariable("Tax", crma.TaxName);
				jsn.AddVariable("SalesOrderNo", crma.SalesOrderNo);
				jsn.AddVariable("SalesOrder", crma.SalesOrderNumber);
				jsn.AddVariable("ShippingAccountNo", crma.Account);
				jsn.AddVariable("ShipViaNo", crma.ShipViaNo);
				jsn.AddVariable("ShipVia", crma.ShipViaName);
                jsn.AddVariable("Incoterm", crma.IncotermName);
                jsn.AddVariable("IncotermNo", crma.IncotermNo);
                jsn.AddVariable("RMADate", Functions.FormatDate(crma.CustomerRMADate));
				jsn.AddVariable("InvoiceDate", Functions.FormatDate(crma.InvoiceDate));
				jsn.AddVariable("Currency", Functions.FormatCurrencyDescription(crma.CurrencyDescription, crma.CurrencyCode));
				jsn.AddVariable("CurrencyNo", crma.CurrencyNo);
				jsn.AddVariable("CurrencyCode", crma.CurrencyCode);
				jsn.AddVariable("DLUP", Functions.FormatDLUP(crma.DLUP, crma.UpdatedBy));
				jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(crma.Notes));
				jsn.AddVariable("Instructions", Functions.ReplaceLineBreaks(crma.Instructions));
                jsn.AddVariable("IsPoHub", SessionManager.IsPOHub == true ? false : true);
                jsn.AddVariable("IsHubAutoCRMA", crma.ClientCustomerRMANo > 0);
                jsn.AddVariable("RefNumber", Functions.FormatNumeric(crma.RefNumber));
                jsn.AddVariable("ClientNo", crma.ClientNo);
                jsn.AddVariable("CustomerRejectionNo", crma.CustomerRejectionNo);
				jsn.AddVariable("AS6081", crma.AS6081);//[RP-2339]

			}
			crma = null;
			return jsn;
		}
		public void GetData() {
			CustomerRma crma = CustomerRma.Get(ID);
			OutputResult(GetData(crma));
			crma = null;
		}

		/// <summary>
		/// Update an existing customerRMA
		/// </summary>
		public void SaveEdit() {
			try {
				bool blnResult = CustomerRma.Update(
					ID
					, GetFormValue_Int("DivisionNo")
					, GetFormValue_Int("WarehouseNo")
					, GetFormValue_Int("AuthorisedBy")
					, GetFormValue_DateTime("RMADate")
					, GetFormValue_Int("ShipVia")
					, GetFormValue_String("ShippingAccount")
					, GetFormValue_String("Notes")
					, GetFormValue_String("Instructions")
                    , GetFormValue_NullableInt("Incoterm")
					, LoginID
                    , GetFormValue_String("CustomerRejectionNo")
                );
				JsonObject jsn = new JsonObject();
				jsn.AddVariable("Result", blnResult);
				OutputResult(jsn);
				jsn.Dispose();
				jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}

		/// <summary>
		/// get all goodsIn for specified CRMA
		/// </summary>
		private void GetReceived() {
			try {
				List<GoodsInLine> lst = GoodsInLine.GetListForCustomerRMA(ID);
				var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x => x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);
				
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				jsn.AddVariable("Count", lst.Count);
				foreach (GoodsInLine gil in lst) {
					string mfrNotes = !Functions.HasNumbericValue(gil.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)gil.ManufacturerNo).AdvisoryNotes;
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", gil.GoodsInLineId);
					jsnItem.AddVariable("GoodsInNo", gil.GoodsInNo);
					jsnItem.AddVariable("GoodsInNumber", gil.GoodsInNumber);
					jsnItem.AddVariable("PartNo", gil.Part);
					jsnItem.AddVariable("ROHS", gil.ROHS);
					jsnItem.AddVariable("SupplierPart", gil.SupplierPart);
					jsnItem.AddVariable("Mfr", gil.ManufacturerCode);
					jsnItem.AddVariable("MfrNo", gil.ManufacturerNo);
					jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
					jsnItem.AddVariable("DC", gil.DateCode);
					jsnItem.AddVariable("Product", gil.ProductName);
					jsnItem.AddVariable("Package", gil.PackageName);
					jsnItem.AddVariable("Mfr", gil.ManufacturerCode);
					jsnItem.AddVariable("MfrNo", gil.ManufacturerNo);
					jsnItem.AddVariable("Quantity", Functions.FormatNumeric(gil.Quantity));
					jsnItem.AddVariable("Location", gil.Location);
					jsnItem.AddVariable("Receiver", gil.ReceiverName);
					jsnItem.AddVariable("ReceivedDate", Functions.FormatDate(gil.DateReceived));
					jsnItem.AddVariable("StockNo", gil.StockNo);
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose(); jsnItem = null;
				}
				jsn.AddVariable("Received", jsnItems);
				OutputResult(jsn);
				jsn.Dispose(); jsn = null;
			} catch (Exception e) {
				WriteError(e);
			}
		}
        //[001] start
        /// <summary>
        /// save internal log for CRMA
        /// </summary>
        public void SaveCRMAInternalLog()
        {
            try
            {
                int intCustomerRMAId = GetFormValue_Int("intCustomerRMAId");
                
                int intResult = CustomerRma.InsertCRMAInternalLog(
                        intCustomerRMAId,
                        GetFormValue_String("AddNotes"),
                        LoginID
                        );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void GetExpediteHistory()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<CustomerRma> lst = CustomerRma.GetCRMAInternalLog(ID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("CRMAExpediteNotesId", lst[i].CRMAExpediteNotesId);
                jsnItem.AddVariable("Notes", Functions.ReplaceLineBreaks(lst[i].Notes));
                jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DLUP));
                jsnItem.AddVariable("Time", Functions.FormatTime(lst[i].DLUP));
                jsnItem.AddVariable("EmployeeName", lst[i].EmployeeName);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst = null;
            jsn.AddVariable("ExpHist", jsnItems);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
        //[001] end
	}
}
