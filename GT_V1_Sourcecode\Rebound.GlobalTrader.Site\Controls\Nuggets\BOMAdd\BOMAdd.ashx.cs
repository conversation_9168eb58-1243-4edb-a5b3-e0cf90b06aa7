//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - clear related dropdown cache
//Marker     changed by      date         Remarks

//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;
using System.Configuration;
using static Rebound.GlobalTrader.Site.Code.Common.IHSManager;
using System.Web.Helpers;
using System.Web.Script.Serialization;
using Rebound.GlobalTrader.Site.Code.Common;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BOMAdd : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "AddNew": AddNew(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Add new BOM
        /// </summary>
        public void AddNew()
        {
            try
            {
                //CacheManager.ClearStoredDropDown("BOM", new object[] { SessionManager.ClientID });
                BOM bom = new BOM();
                bom.ClientNo = (int)SessionManager.ClientID;
                bom.BOMName = GetFormValue_String("Name") + "-" + SessionManager.ClientID.ToString();
                bom.CompanyNo = GetFormValue_Int("Company");
                bom.ContactNo = GetFormValue_Int("Contact");
                bom.Inactive = GetFormValue_Boolean("Inactive");
                bom.Notes = GetFormValue_String("Notes");

                bom.UpdateRequirement = 0;
                bom.Status = (int)Rebound.GlobalTrader.BLL.BOMStatus.List.New;
                bom.CurrencyNo = GetFormValue_NullableInt("CurrencyNo");
                bom.CurrentSupplier = GetFormValue_String("CurrentSupplier");
                bom.QuoteRequired = GetFormValue_NullableDateTime("QuoteRequired");
                bom.UpdatedBy = LoginID;
                bom.AS9120 = GetFormValue_Boolean("AS9120");
                bom.Contact2Id = GetFormValue_NullableInt("Contact2", null);
                bom.BOMId = bom.Insert();
                var generatedBOMID = GetFormValue_String("GeneratedBomID");
                //add HUBRFQ lines from uploaded BOM file
                string originalFileName = GetFormValue_String("OriginalFilename");
                string generatedFileName = GetFormValue_String("GeneratedFilename");
                string bomCompanyName = GetFormValue_String("CompanyName");
                if (bom.BOMId > 0 && !string.IsNullOrEmpty(originalFileName) && !string.IsNullOrEmpty(generatedFileName))
                {
                    int AssignUserNo = GetFormValue_Int("AssignUserNo");
                    string aryRecipientLoginIDsCC = GetFormValue_String("aryRecipientLoginIDs");

                    int addedLinesCount = AddLines(SessionManager.LoginID ?? 0,
                                                   bom.ClientNo,
                                                   bom.ClientNo,
                                                   bom.BOMId, //updateBOMId
                                                   bom.BOMName,
                                                   GetFormValue_Int("Company"),
                                                   "",//company name
                                                   bom.Contact2Id ?? 0,//salesmanId
                                                   GetFormValue_Int("Contact"),
                                                   "", //contactName
                                                   bom.CurrencyNo ?? 0,//defaultCurrencyId
                                                   "",//currencyName
                                                   0,//ReqforTraceabilityId
                                                   0,//TypeId
                                                   bom.QuoteRequired ?? DateTime.Now,
                                                   originalFileName,
                                                   generatedFileName);

                    //change status to RFQ and send email to users
                    if (AssignUserNo > 0)
                    {
                        bool blnOK = BOM.UpdatePurchaseQuote(bom.BOMId, LoginID, (int)BOMStatus.List.RPQ, AssignUserNo, out string ValidateMessage);
                        if (blnOK)
                        {
                            WebServices servic = new WebServices();
                            string BOMCode = string.Format("{0}-{1}", bom.ClientNo.ToString(), bom.BOMId.ToString());
                            string BOMName = bom.BOMName;
                            string BomCompanyName = bomCompanyName;
                            int? BomCompanyNo = bom.CompanyNo;
                            System.Int32 Contact2No = GetFormValue_Int("Contact2No"); // Also send mail to  Added Contact 2  05-04-2018
                            servic.NotifyPurchaseRequestBom(AssignUserNo.ToString(), (SessionManager.POHubMailGroupId ?? 0).ToString(), string.Format(Functions.GetGlobalResource("Messages", "PurchaseRequest"), BOMName), BOMCode, BOMName, bom.BOMId, BomCompanyName, BomCompanyNo, aryRecipientLoginIDsCC);
                            List<CustomerRequirement> customerRequirements = CustomerRequirement.GetHUBRFQHasRLStock(ID);
                            servic.RLStockNotification(string.Format(Functions.GetGlobalResource("Messages", "RLStockSubject1"), BOMCode), customerRequirements, true, bom.BOMId, BomCompanyName, BOMCode);
                        }
                    }
                }
                if (bom.BOMId > 0)
                {
                    //insert PPV
                    bom.UpdatePVV(generatedBOMID);
                }

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", bom.BOMId > 0);
                jsn.AddVariable("NewID", bom.BOMId);
                jsn.AddVariable("ValidationMessage", bom.ValidationMessage);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Try to create HUBRFQ lines from uploaded BOM data
        /// </summary>
        /// <param name="context"></param>
        private int AddLines(int loginId,
                              int clientId,
                              int selectedClientId,
                              int updateBomId,
                              string bomName,
                              int companyId,
                              string companyName,
                              int salesmanId,
                              int contactId,
                              string contactName,
                              int defaultCurrencyId,
                              string currencyName,
                              int ReqforTraceabilityId,
                              int TypeId,
                              DateTime dateRequired,
                              string originalFileName,
                              string generatedFileName
                             )
        {
            try
            {
                DataTable dtColumnList = Stock.GetBOMExcelHeaderFrom(SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, selectedClientId, 0);

                if (dtColumnList == null || dtColumnList.Rows.Count == 0)
                    return 0;
                (string Labels, string Names) = BuildMappingColumnString(dtColumnList);
                DataTable errorTable = Stock.GetExportToExcelError_BomImport(SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, selectedClientId, Names, Labels, Names);
                if (errorTable?.Rows.Count > 0)
                    return 0;

                //call IHS API
                List<string> parts = Stock.GetBOMPartsForIHS(loginId, clientId, selectedClientId);
                if (parts != null && parts.Count > 0)
                {
                    string token = GetIHSAuthToken();
                    if (!string.IsNullOrEmpty(token))
                    {
                        List<IHSManager.Root> results = new List<IHSManager.Root>();
                        foreach (string part in parts)
                        {
                            var apiResponse = GetIHSPart(token, part);
                            results.AddRange(apiResponse);
                        }

                        if(results.Count > 0)
                        {
                            int intResult = CustomerRequirement.InsertIHSApiXML(
                                   SessionManager.ClientID,
                                   SessionManager.LoginID,
                                   ListToXml(results)
                                  );
                        }
                    }
                }

                int recordCount = BLL.Stock.saveBOMImportData(loginId,
                                                              clientId,
                                                              selectedClientId,
                                                              Labels,
                                                              Names,
                                                              Names,
                                                              "",//fileColName
                                                              currencyName,//ddlCurrency
                                                              out string errorMessage,
                                                              bomName,
                                                              companyName,
                                                              contactName,
                                                              salesmanId,
                                                              companyId,
                                                              contactId,
                                                              false,//PartWatch,
                                                              defaultCurrencyId,
                                                              true,//OverRideCurrency
                                                              "CreateHUBRFQ",
                                                              out string NewBomCode,
                                                              out int NewBomid,
                                                              ReqforTraceabilityId,
                                                              TypeId,
                                                              dateRequired,
                                                              updateBomId,
                                                              originalFileName,
                                                              generatedFileName);
                return recordCount;
            }
            catch (Exception e)
            {
                WriteError(e);
                return 0;
            }
        }


        #region private methods
        private (string Labels, string Names) BuildMappingColumnString(DataTable dtColumnList)
        {
            List<string> columnLabels = new List<string>();
            List<string> columnNames = new List<string>();

            foreach (DataRow row in dtColumnList.Rows)
            {
                ///build dynamic column string, base on UtilityBOMImport.js
                var columnLabel = Functions.StripAlphabet(row["ColumnHeading"].ToString());
                var columnName = row["ExcelHeaderid"].ToString();
                var targetLabel = FindTheTargetColumn(columnLabel.ToUpper());
                if (targetLabel != null && !columnLabels.Contains(targetLabel))
                {
                    columnLabels.Add(targetLabel);
                    columnNames.Add(columnName);
                }
            }
            return (string.Join(",", columnLabels), string.Join(",", columnNames));
        }

        private string FindTheTargetColumn(string columnLabel)
        {
            string[] allowDCLabels = { "DATECODE", "DTCD", "DC" };
            string[] allowMfrLabels = { "MFR", "MFG", "MANUFACTURER" };
            string[] allowPartLabels = { "PART", "PARTNO", "PARTNUMBER" };
            string[] allowCusPartLabels = { "CUSTOMERPART", "CUSTOMERPARTNO", "CUSTPART", "CUSTPARTNO" };
            string[] allowPackageLabels = { "PACKAGE", "PACK" };
            string[] allowProductLabels = { "PRODUCT", "PROD" };
            string[] allowQuantityLabels = { "QUANTITY", "QNTY", "QTY" };
            string[] allowInstructionLabels = { "INSTRUCTIONS", "INSTR" };

            if (Array.IndexOf(allowPartLabels, columnLabel) >= 0)
            {
                return "Part";
            }
            if (Array.IndexOf(allowCusPartLabels, columnLabel) >= 0)
            {
                return "CustomerPart";
            }
            if (Array.IndexOf(allowPackageLabels, columnLabel) >= 0)
            {
                return "PackageName";
            }
            if (Array.IndexOf(allowDCLabels, columnLabel) >= 0)
            {
                return "DateCode";
            }
            if (Array.IndexOf(allowInstructionLabels, columnLabel) >= 0)
            {
                return "Instructions";
            }
            if (Array.IndexOf(allowMfrLabels, columnLabel) >= 0)
            {
                return "ManufacturerName";
            }
            if (columnLabel.Equals("ROHS"))
            {
                return "ROHS";
            }
            if (columnLabel.Equals("NOTES"))
            {
                return "Notes";
            }
            if (Array.IndexOf(allowProductLabels, columnLabel) >= 0)
            {
                return "ProductName";
            }
            if (columnLabel.Equals("PRICE"))
            {
                return "Price";
            }
            if (Array.IndexOf(allowQuantityLabels, columnLabel) >= 0)
            {
                return "Quantity";
            }
            return null;
        }


        #endregion
        #region IHS API CALL
        private string GetIHSAuthToken()
        {
            string IHSUserName = Convert.ToString(ConfigurationManager.AppSettings["IHSUserName"]);
            string IHSUserPass = Convert.ToString(ConfigurationManager.AppSettings["IHSUserPass"]);
            string IHSTokenGenURL = Convert.ToString(ConfigurationManager.AppSettings["IHSTokenGenURL"]);
            try
            {
                var client = new HttpUtils.RestClient()
                {
                    EndPoint = IHSTokenGenURL,
                    Method = HttpVerb.POST,
                    PostData = "{\n\t\"username\":\"" + IHSUserName + "\",\n\t\"password\":\"" + IHSUserPass + "\"\n}",
                    tokenRespose = null
                };
                string jsonResponse = client.MakeRequest();
                if (string.IsNullOrEmpty(jsonResponse))
                    return string.Empty;

                System.Web.Script.Serialization.JavaScriptSerializer jsSerializer = new System.Web.Script.Serialization.JavaScriptSerializer();
                var data = jsSerializer.Deserialize<TokenRespose>(jsonResponse);
                return data.authToken;
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMAdd.ashx class, Method name : GetIHSAuthToken. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return string.Empty;
            }
        }

        private List<IHSManager.Root> GetIHSPart(string authToken, string part)
        {
            try
            {
                List<IHSManager.Root> results = new List<Root>();
                string IHSPartSearchURL = Convert.ToString(ConfigurationManager.AppSettings["IHSPartSearchURL"]);
                string strJson = "{\r\n   \"prtNbr\": \"'" + part + "'\",\r\n   \"searchType\":\"contains\",\r\n   \"collections\":[\"prices\"]\r\n   \r\n   \r\n}";
                var client = new HttpUtils.RestClient
                {
                    EndPoint = IHSPartSearchURL,
                    Method = HttpVerb.POST,
                    PostData = strJson,
                    tokenRespose = authToken
                };
                var jsonResponse = client.MakeRequest();

                if (!string.IsNullOrEmpty(jsonResponse))
                {
                    JavaScriptSerializer jsSerializer = new JavaScriptSerializer();
                    results = jsSerializer.Deserialize<List<IHSManager.Root>>(jsonResponse);
                }
                return results;
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Inside BOMAdd.ashx class, Method name : GetIHSPart. Exception details:" + (ex.InnerException == null ? ex.Message : ex.InnerException.Message));
                return new List<Root>();
            }
        }
        #endregion
    }
}
