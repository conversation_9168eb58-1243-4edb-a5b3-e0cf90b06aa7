﻿---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  
  
-- =============================================        
-- Author:  Surendra        
-- Create date: February 23, 2016        
-- Description: To get IPO header Information        
-- =============================================        
/*     
===========================================================================================    
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION    
[US-225812]     Trung Pham		 20-Apr-2025		UPDATE		Get country and related warning message 
===========================================================================================    
*/
CREATE OR ALTER PROCEDURE usp_select_InternalPurchaseOrder         
@InternalPurchaseOrderId int           
AS        
BEGIN        
 SET NOCOUNT ON;        
 Declare @vSupplierRMAIds Varchar(1000)              
 Declare @vSupplierRMANumbers Varchar(1000)               
 Declare @vDebitIds Varchar(1000)              
 Declare @vDebitNumbers Varchar(1000)      
 DECLARE @PurchaseOrderNo INT    
 SET @PurchaseOrderNo=(Select TOP 1 ipo.PurchaseOrderNo from dbo.tbInternalPurchaseOrder ipo WHERE ipo.InternalPurchaseOrderId=@InternalPurchaseOrderId)    
 Execute usp_select_SupplierRMA_By_PurchaseOrder @PurchaseOrderNo ,@vSupplierRMAIds Out,@vSupplierRMANumbers Out              
 Execute usp_select_Debit_By_PurchaseOrder @PurchaseOrderNo, @vDebitIds Out,@vDebitNumbers Out              
          
    Declare @vEPRIds Varchar(1000)               
    Select @vEPRIds = COALESCE(@vEPRIds + ',','') + COALESCE(Cast(EPRId As Varchar),'')                    
    From tbEPR                
    Where PurchaseOrderId = @PurchaseOrderNo AND isnull(Inactive,0) = 0 order by DLUP desc            
           
               
SELECT *,              
    @vSupplierRMAIds As 'SupplierRMAIds',              
    @vSupplierRMANumbers As 'SupplierRMANumbers',              
    @vDebitIds As 'DebitIds',              
    @vDebitNumbers As 'DebitNumbers' ,            
    @vEPRIds as 'EPRIds'     
    ,(select RegionName from tbRegion where RegionId=ipo.RegionNo) as RegionName          
	, ct.CountryName
	, swm.WarningText
	, CAST(CASE 
		WHEN EXISTS (SELECT 1 FROM tbSystemWarningMessage WHERE SystemWarningMessageId = swm.SystemWarningMessageId )
		THEN 1 ELSE 0 END AS BIT
	) AS IsHasCountryMessage
from dbo.vwInternalPurchaseOrder ipo                
LEFT JOIN dbo.tbCompany c ON c.CompanyId = ipo.CompanyNo
LEFT JOIN dbo.tbCompanyAddress ca ON c.CompanyId = ca.CompanyNo AND ca.DefaultBilling = 1
 LEFT JOIN dbo.tbAddress bad ON ca.AddressNo = bad.AddressId
 LEFT JOIN dbo.tbCountry ct ON bad.CountryNo = ct.CountryId AND ISNULL(ct.InActive, 0) = 0
 LEFT JOIN dbo.tbSystemWarningMessage swm ON ct.CountryId = swm.ApplyTo AND swm.ClientNo = ipo.ClientNo AND ISNULL(swm.InActive, 0) = 0
WHERE ipo.InternalPurchaseOrderId = @InternalPurchaseOrderId        
END   
  
  