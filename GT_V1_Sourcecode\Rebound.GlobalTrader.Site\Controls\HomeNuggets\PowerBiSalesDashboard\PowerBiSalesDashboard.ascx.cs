//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets
{
	public partial class PowerBiSalesDashboard : Base
	{

		//protected SimpleDataTable _tblSalesDashboard;

		protected override void OnInit(EventArgs e)
		{
			this.HomePageNuggetType = "PowerBISalesDashboard";
			base.OnInit(e);
			//_tblSalesDashboard = (SimpleDataTable)FindContentControl("tblSalesDashboard");
			AddScriptReference("Controls.HomeNuggets.PowerBiSalesDashboard.PowerBiSalesDashboard.js");
		}

		protected override void OnLoad(EventArgs e)
		{
			//table headings
			//_tblSalesDashboard.Columns.Add(new SimpleDataColumn(""));
			//string url = ConfigurationManager.AppSettings["PowerBiProjUrl"].ToString();
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard", this.ctlDesignBase.ClientID);
			//_scScriptControlDescriptor.AddProperty("powerBiUrl", url);
			//setup javascript
			//_scScriptControlDescriptor.AddComponentProperty("tblSalesDashboard", _tblSalesDashboard.ClientID);
			//_scScriptControlDescriptor.AddComponentProperty("powerBiUrl", ConfigurationManager.AppSettings["PowerBiProjUrl"]);
			//_scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
			base.OnLoad(e);
		}
	}
}