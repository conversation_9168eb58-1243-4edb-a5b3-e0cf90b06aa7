﻿
GO
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================
*/
CREATE OR ALTER PROCEDURE usp_update_HUBOfferImportLargeFile_Temp
    @JsonData NVARCHAR(MAX),
	@UpdatedBy INT,
	@RecordCount INT OUTPUT
AS
BEGIN
    -- Parse JSON and update the table
    UPDATE p
    SET
		p.UpdatedBy = @UpdatedBy,
        p.MPN = j.MPN,
		p.COST = REPLACE(j.COST, ',', ''),
		p.LeadTime = j.LeadTime,
		p.SPQ = CASE 
             WHEN ISNULL(LTRIM(RTRIM(REPLACE(j.SPQ, ',', ''))), '') = '' THEN '0'
             ELSE REPLACE(j.SPQ, ',', '')
          END,
		p.MOQ = CASE 
             WHEN ISNULL(LTRIM(RTRIM(REPLACE(j.MOQ, ',', ''))), '') = '' THEN '0'
             ELSE REPLACE(j.MOQ, ',', '')
          END,
		p.Remarks = j.Remarks,
		p.OfferedDate = j.OfferedDate

    FROM BorisGlobalTraderimports.dbo.tbOfferImportByExcelTemp p
    INNER JOIN OPENJSON(@JsonData)
        WITH (
            OfferTempId INT , 
			MPN NVARCHAR(MAX) ,
			MFR NVARCHAR(MAX) ,
			COST NVARCHAR(MAX) ,
			LeadTime NVARCHAR(MAX) ,
			SPQ NVARCHAR(MAX) ,
			MOQ NVARCHAR(MAX) ,
			Remarks NVARCHAR(MAX) ,
			OfferedDate NVARCHAR(MAX) ,
			Vendor NVARCHAR(MAX)
        ) j
    ON p.OfferTempId = j.OfferTempId;
	SELECT @RecordCount = @@ROWCOUNT 
END
GO