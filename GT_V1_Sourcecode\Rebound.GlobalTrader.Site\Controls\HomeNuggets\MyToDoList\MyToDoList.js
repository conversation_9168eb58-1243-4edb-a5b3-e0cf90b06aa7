Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.prototype={get_tblMessages:function(){return this._tblMessages},set_tblMessages:function(n){this._tblMessages!==n&&(this._tblMessages=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblMessages&&this._tblMessages.dispose(),this._tblMessages=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblMessages.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();this._tblMessages.clearTable();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyToDoList");n.set_DataObject("MyToDoList");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,r,t,u;for(this.showNoneFoundOrContent(i.Count),r=0;r<i.Items.length;r++)t=i.Items[r],u=[$R_FN.setCleanTextValue(t.Due),t.IsCompleted?$RGT_nubButton_ToDo_CusClass(t.ID,t.Item,""):$RGT_nubButton_ToDo_CusClass(t.ID,t.Item,"todo_red"),$RGT_nubButton_Company(t.CompanyNo,t.CompanyName)],this._tblMessages.addRow(u,null);this._tblMessages.show(i.Count>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyToDoList",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);