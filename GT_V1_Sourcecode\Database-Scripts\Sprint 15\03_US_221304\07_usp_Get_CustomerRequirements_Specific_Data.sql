﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		24-Sep-2024		CREATE		Get specific columns by CustomerRequirementId
[US-221304]		Trung Pham Van		21-Nov-2024		Update		Edit new price
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_CustomerRequirements_Specific_Data]
	@CustomerRequirementId INT
AS
BEGIN
	SELECT cr.CustomerRequirementId
	,cr.ClientNo
	,cr.CompanyNo
	,ISNULL(c.Salesman, cr.ContactNo) AS Salesman
	,cr.CurrencyNo
	,cr.NewOfferPriceFromProspective
	FROM tbCustomerRequirement cr
	LEFT JOIN tbCompany c ON c.CompanyId = cr.CompanyNo
	WHERE CustomerRequirementId = @CustomerRequirementId
END
