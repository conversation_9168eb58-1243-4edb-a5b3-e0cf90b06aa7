///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//[001]      A<PERSON><PERSON>     07-Aug-2018  [REB-12084]:Lock PO lines when EPR is authorised
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.initializeBase(this, [element]);
    this._intPurchaseOrderID = 0;
    this._intLineID = -1;
    this._blnPartReceivedEdit = false;
    this._blnRestrictedEdit = false;
    this._intLineReceived = 0;
    this._intIPOClientNo = -1;
    this._blnClientPO = false;
    this._intGlobalClientNo = -1;
    this._blnProductHaza = false;
    //[001] start
    this._blnCanEditQty = true;
    this._blnCanEditPrice = true;
    this._TypeNo = "";
    this._IsSendToLineManager = false;
    this._IsSendToQuality = false;
    //[001] end
};

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.prototype = {

    get_intPurchaseOrderID: function () { return this._intPurchaseOrderID; }, set_intPurchaseOrderID: function (v) { if (this._intPurchaseOrderID !== v) this._intPurchaseOrderID = v; },
    get_strTitleMessage: function () { return this._strTitleMessage; }, set_strTitleMessage: function (v) { if (this._strTitleMessage !== v) this._strTitleMessage = v; },
    get_lblCurrency: function () { return this._lblCurrency; }, set_lblCurrency: function (v) { if (this._lblCurrency !== v) this._lblCurrency = v; },
    get_lblTotalShipInCost: function () { return this._lblTotalShipInCost; }, set_lblTotalShipInCost: function (v) { if (this._lblTotalShipInCost !== v) this._lblTotalShipInCost = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.callBaseMethod(this, "initialize");
        $R_IBTN.showButton(this._ibtnNotify, false);
        $R_IBTN.showButton(this._ibtnNotify_Footer, false);

        this.addShown(Function.createDelegate(this, this.formShown));
       
        
        
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intPurchaseOrderID = null;
        this._strTitleMessage = null;
        if (this._ctlMail) this._ctlMail.dispose();
        this._intLineID = null;
        this._IsSendToLineManager = null;
        this._IsSendToQuality = null;
        this._blnPartReceivedEdit = null;
        this._blnRestrictedEdit = null;
        this._intLineReceived = null;
        this._lblCurrency = null;
        this._lblTotalShipInCost = null;
        this._intIPOClientNo = null;
        this._blnClientPO = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.callBaseMethod(this, "dispose");
    },

    getEditApprovalData: function () {
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
        obj.set_DataObject("ExportApprovalStatus");
        obj.set_DataAction("GetEditApprovalData");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getEditApprovalDataOK));
        obj.addError(Function.createDelegate(this, this.getEditApprovalDataError));
        obj.addTimeout(Function.createDelegate(this, this.getEditApprovalDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getEditApprovalDataError: function (args) {
        this.showInnerContent(true);
        this.showLoading(false);
    },

    getEditApprovalDataOK: function (args) {
        var result = args._result;
        this.setFieldValue("ctlSalesPerson", result.SalesmanName);
        this.setFieldValue("ctlSalesOrder", result.SalesOrderNo);
        this.setFieldValue("ctlSOLineNo", result.SOSerialNo);
        this.setFieldValue("ctlCustomer", result.CustomerName);
        this.setFieldValue("ctlPartNumber", result.Part);
        this.setFieldValue("ctlDestinationCountry", result.DestinationCountryId);
        this.setFieldValue("ctlMilitaryuse", result.MilitaryUseId);
        this.setFieldValue("ctlEndUser", result.EndUserText);
        this.setFieldValue("ctlWarehouse", result.ShipFromWarehouse);
        this.setFieldValue("ctlCountry", result.ShipFromCountry);
        this.setFieldValue("ctlCustomerName", result.ShipToCustomerName);
        this.setFieldValue("ctlCustomerCountry", result.ShipToCustomerCountry);
        this.setFieldValue("ctlCommodityCode", result.CommodityCode);
        this.setFieldValue("ctlECCN", result.ECCN);
        this.setFieldValue("ctlPartApplication", result.PartApplication);
        ($get('ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmEdit_ctlDB_ctlExportControl_ctl04_ddlExportControl')).value = result.ExportControl;
        ($get('ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmEdit_ctlDB_ctlAerospaceUse_ctl04_ddlAerospaceUse')).value = result.AerospaceUse;
        this.setFieldValue("ctlPartTested", result.PartTested);
        if ((result.ExportApprovalStatusId == 3 || result.ExportApprovalStatusId == 4 || result.ExportApprovalStatusId == 5 || result.ExportApprovalStatusId == 7)) {
            $R_IBTN.enableButton(this._ibtnSave, true);
            $R_IBTN.enableButton(this._ibtnSave_Footer, true);
        } else {
            $R_IBTN.enableButton(this._ibtnSave, false);
            $R_IBTN.enableButton(this._ibtnSave_Footer, false);
        }
        this.showInnerContent(true);
        this.showLoading(false);
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            var ddl = ($get('ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmEdit_ctlDB_ctlExportControl_ctl04_ddlExportControl'));
            if (ddl) {
                ddl.addEventListener('change', function () {
                    if (ddl.value === "1") {
                        alert("Kindly upload the respective document under PDF Document on the SO Detail page");
                    }
                });
            }
            this.addSave(Function.createDelegate(this, this.saveClicked));
           
        }
        this.getEditApprovalData();
        


        



        if (this._blnClientPO) {

        }
        else {

        }
        this.getFieldDropDownData("ctlDestinationCountry");
        this.getFieldDropDownData("ctlMilitaryuse");

        if (this._intGlobalClientNo > 0) {


        } else {

        }
        //[001] start
        if (this._blnCanEditPrice == true) {

        }
        else {

        }
        if (this._blnCanEditQty == true) {

        }
        else {

        }
        //[001] end





    },
    createPDF: function () {
        var baseUrl = (window.location).href;
        var url = new URL(baseUrl);
        $R_FN.openPrintWindow($R_ENUM$PrintObject.PrintTermCondtionPurchase, 0);
    },
    ShowPopupWeblinkEvidence: function () {
        $RGT_openSupplierApprovalDoc(this._intLineID, "EVDNS", 1);
    },
    ShowPopupTROne: function () {
        $RGT_openSupplierApprovalDoc(this._intLineID, "TR1", 1);
    },
    ShowPopupTRTwo: function () {
        $RGT_openSupplierApprovalDoc(this._intLineID, "TR2", 1);
    },
    ShowPopupTRThree: function () {
        $RGT_openSupplierApprovalDoc(this._intLineID, "TR3", 1);
    },
    ShowPopupDevicePictures: function () {
        $RGT_openSupplierApprovalImages(this._intLineID, "DVCPCTR", 1);
    },
    ShowPopupManufacturersPictures: function () {
        $RGT_openSupplierApprovalImages(this._intLineID, "MNFCTRPCTR", 1);
    },
    ShowPopupTraceabilityPictures: function () {
        $RGT_openSupplierApprovalImages(this._intLineID, "TRCBLTYPCTR", 1);
    },
    ShowMailWizard: function () {
        var MethodId = 0;

        if (MethodId == 1) {

        }
        else {

        }
    },
    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
        obj.set_DataObject("ExportApprovalStatus");
        obj.set_DataAction("SaveExportApprovalDetails");
        obj.addParameter("id", this._intLineID);
        obj.addParameter("DestinationCountryNo", this.getFieldValue("ctlDestinationCountry"));
        obj.addParameter("MilitaryuseNo", this.getFieldValue("ctlMilitaryuse"));
        obj.addParameter("EndUserText", this.getFieldValue("ctlEndUser"));
        obj.addParameter("PartApplication", this.getFieldValue("ctlPartApplication"));
        obj.addParameter("ExportControl", this.getFieldValue("ctlExportControl"));
        obj.addParameter("AerospaceUse", this.getFieldValue("ctlAerospaceUse"));
        obj.addParameter("PartTested", this.getFieldValue("ctlPartTested"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditOK));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditOK: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();

            $("#AllocationErrorMsg").hide();
            $("#dvtxt").html("");
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    SaveInDraftMode: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/POApprovals");
        obj.set_DataObject("POApprovals");
        obj.set_DataAction("SaveInDraftMode");
        obj.addParameter("id", this._intLineID);

        obj.addParameter("PrecogsSupplierNo", this.getFieldValue("ctlDestinationCountry"));
        obj.addParameter("To", this.getFieldValue("ctlTo"));
        obj.addParameter("Subject", this._ctlMail.getValue_Subject());
        obj.addParameter("Message", this._ctlMail.getValue_Body());
        obj.addParameter("Comment", this.getFieldValue("ctlComment"));
        obj.addDataOK(Function.createDelegate(this, this.SaveInDraftModeOK));
        obj.addError(Function.createDelegate(this, this.SaveInDraftModeError));
        obj.addTimeout(Function.createDelegate(this, this.SaveInDraftModeError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    SaveInDraftModeError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    SaveInDraftModeOK: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = true;
        if (!this.checkFieldEntered("ctlDestinationCountry")) blnOK = false;
        if (!this.checkFieldEntered("ctlMilitaryuse")) blnOK = false;
        if (!this.checkFieldEntered("ctlEndUser")) blnOK = false;
        if (!this.checkFieldEntered("ctlPartApplication")) blnOK = false;
        if (!this.checkFieldEntered("ctlPartTested")) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;

    },

    setFieldsFromHeader: function (strPONumber, strSupplierName, strCurrencyCode, strTotalShipInCostText) {
        $R_FN.setInnerHTML(this._lblTotalShipInCost, strTotalShipInCostText);
        $R_FN.setInnerHTML(this._lblCurrency, strCurrencyCode);
        this.setFieldValue("ctlPurchaseOrder", strPONumber);
        this.setFieldValue("ctlSalesOrder", strSupplierName);
    },
    productChange: function () {

    },
    stepChanged: function () {
        var intStep = this._ctlMultiStep._intCurrentStep;
        $("#spnexplntn").hide();
        $R_IBTN.showButton(this._ibtnSave, intStep != 2);
        $R_IBTN.showButton(this._ibtnSave_Footer, intStep != 2);
       
        $R_IBTN.showButton(this._ibtnCancel, intStep != 2);
        $R_IBTN.showButton(this._ibtnCancel_Footer, intStep != 2);

        this.setFieldValue("ctlSendMailSupplier", false);
        this.setFieldValue("ctlSendMailLineManager", false);
        this.setFieldValue("ctlSendMailQuality", false);
        this.setFieldValue("ctlUpDateLineManager", false);
    },
    EnableNotifyButton: function () {
        var blnSupplier = this.getFieldValue("ctlSendMailSupplier");
        var blnLineManager = this.getFieldValue("ctlSendMailLineManager");
        var blnQulity = this.getFieldValue("ctlSendMailQuality");
        var blnUpdateLineManager = this.getFieldValue("ctlUpDateLineManager");
        if ((blnSupplier == true) || (blnLineManager == true) || (blnQulity == true) || (blnUpdateLineManager == true)) {

        }
        else {

        }
        if (blnLineManager == true) {
            this.showField("ctlLineManager", true);
            this.showField("ctlSendMailQuality", false);
        }
        else {
            this.showField("ctlLineManager", false);

            if (this._IsSendToLineManager == false)
                this.showField("ctlSendMailQuality", true);
        }
        if (blnQulity == true) {
            this.showField("ctlSendMailLineManager", false);
            alert("Now Line Manager Approval is not required for this supplier approval.")
        }
        else {
            if (this._IsSendToQuality == false)
                this.showField("ctlSendMailLineManager", true);
        }
        if (blnUpdateLineManager == true) {
            this.showField("ctlUpdateLineManagerlist", true);
        }
        else {
            this.showField("ctlUpdateLineManagerlist", false);
        }
    },
    ExitForm: function () {
        this.onSaveComplete();
    },
    NotifySUpplierApproval: function () {

        if (this.getFieldValue("ctlSendMailLineManager") == true) {
            if ((this.getFieldValue("ctlLineManager") != null)) {
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/POApprovals");
                obj.set_DataObject("POApprovals");
                obj.set_DataAction("NotifySupplierApproval");
                obj.addParameter("id", this._intLineID);
                //obj.addParameter("To", this.getFieldValue("ctlTo"));
                obj.addParameter("Subject", this._ctlMail.getValue_Subject());
                obj.addParameter("Message", this._ctlMail.getValue_Body());
                obj.addParameter("IsSendToSupplier", this.getFieldValue("ctlSendMailSupplier"));
                obj.addParameter("IsSendToLinemanager", this.getFieldValue("ctlSendMailLineManager"));
                obj.addParameter("LineManagerId", this.getFieldValue("ctlLineManager"));
                obj.addParameter("IsSendToQuality", this.getFieldValue("ctlSendMailQuality"));
                obj.addParameter("Comment", this.getFieldValue("ctlComment"));
                obj.addDataOK(Function.createDelegate(this, this.NotifySUpplierApprovalOK));
                obj.addError(Function.createDelegate(this, this.NotifySUpplierApprovalError));
                obj.addTimeout(Function.createDelegate(this, this.NotifySUpplierApprovalError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;
                this.showSaving(true);
            }
            else {
                alert("Please select Line Manager.")
            }
        }
        else if (this.getFieldValue("ctlUpDateLineManager") == true) {
            if ((this.getFieldValue("ctlUpdateLineManagerlist") != null)) {
                var obj = new Rebound.GlobalTrader.Site.Data();
                obj.set_PathToData("controls/Nuggets/POApprovals");
                obj.set_DataObject("POApprovals");
                obj.set_DataAction("NotifySupplierApproval");
                obj.addParameter("id", this._intLineID);
                obj.addParameter("Subject", this._ctlMail.getValue_Subject());
                obj.addParameter("Message", this._ctlMail.getValue_Body());
                obj.addParameter("IsSendToSupplier", this.getFieldValue("ctlSendMailSupplier"));
                obj.addParameter("IsSendToLinemanager", this.getFieldValue("ctlSendMailLineManager"));
                obj.addParameter("LineManagerId", this.getFieldValue("ctlLineManager"));
                obj.addParameter("IsSendToQuality", this.getFieldValue("ctlSendMailQuality"));
                obj.addParameter("IsUpdateLinemanager", this.getFieldValue("ctlUpDateLineManager"));
                obj.addParameter("UpdateLineManagerId", this.getFieldValue("ctlUpdateLineManagerlist"));
                obj.addParameter("Comment", this.getFieldValue("ctlComment"));
                obj.addDataOK(Function.createDelegate(this, this.NotifySUpplierApprovalOK));
                obj.addError(Function.createDelegate(this, this.NotifySUpplierApprovalError));
                obj.addTimeout(Function.createDelegate(this, this.NotifySUpplierApprovalError));
                $R_DQ.addToQueue(obj);
                $R_DQ.processQueue();
                obj = null;
                this.showSaving(true);
            }
            else {
                alert("Please select Line Manager to update.")
            }
        }
        else {
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj.set_PathToData("controls/Nuggets/POApprovals");
            obj.set_DataObject("POApprovals");
            obj.set_DataAction("NotifySupplierApproval");
            obj.addParameter("id", this._intLineID);
            obj.addParameter("Subject", this._ctlMail.getValue_Subject());
            obj.addParameter("Message", this._ctlMail.getValue_Body());
            obj.addParameter("IsSendToSupplier", this.getFieldValue("ctlSendMailSupplier"));
            obj.addParameter("IsSendToLinemanager", this.getFieldValue("ctlSendMailLineManager"));
            obj.addParameter("LineManagerId", this.getFieldValue("ctlLineManager"));
            obj.addParameter("IsSendToQuality", this.getFieldValue("ctlSendMailQuality"));
            obj.addParameter("Comment", this.getFieldValue("ctlComment"));
            obj.addDataOK(Function.createDelegate(this, this.NotifySUpplierApprovalOK));
            obj.addError(Function.createDelegate(this, this.NotifySUpplierApprovalError));
            obj.addTimeout(Function.createDelegate(this, this.NotifySUpplierApprovalError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
            obj = null;
            this.showSaving(true);
        }


    },
    NotifySUpplierApprovalError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    NotifySUpplierApprovalOK: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
