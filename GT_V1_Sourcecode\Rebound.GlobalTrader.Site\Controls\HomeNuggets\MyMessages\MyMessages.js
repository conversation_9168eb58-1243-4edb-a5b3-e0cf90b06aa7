Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages.initializeBase(this,[n]);this._tblMessages=null};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages.prototype={get_tblMessages:function(){return this._tblMessages},set_tblMessages:function(n){this._tblMessages!==n&&(this._tblMessages=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblMessages&&this._tblMessages.dispose(),this._tblMessages=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblMessages.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();this._tblMessages.clearTable();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyMessages");n.set_DataObject("MyMessages");n.set_DataAction("GetData");n._intTimeOutMiliseconds=1;n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,r,t,u;for(this.showNoneFoundOrContent(i.Count),r=0;r<i.Items.length;r++)t=i.Items[r],u=[$R_FN.setCleanTextValue(t.From),$RGT_nubButton_MailMessage(t.ID,t.Subject),$R_FN.setCleanTextValue(t.DateSent)],this._tblMessages.addRow(u,null),t=null;this._tblMessages.show(i.Count>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyMessages",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);