﻿ IF (EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbImportActivity_Epo'
	AND COLUMN_NAME IN ('Inactive', 'InactiveBy', 'InactiveDate', 'ImportId')))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbImportActivity_Epo
		DROP COLUMN Inactive, InactiveBy, InactiveDate, ImportId;
	END
 IF (EXISTS (SELECT 1 FROM BorisGlobalTraderimports.INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbEpo'
	AND COLUMN_NAME IN ('Inactive', 'InactiveBy', 'InactiveDate', 'ImportId')))
	BEGIN
		ALTER TABLE BorisGlobalTraderimports.dbo.tbEpo
		DROP COLUMN Inactive, InactiveBy, InactiveDate, ImportId;
	END