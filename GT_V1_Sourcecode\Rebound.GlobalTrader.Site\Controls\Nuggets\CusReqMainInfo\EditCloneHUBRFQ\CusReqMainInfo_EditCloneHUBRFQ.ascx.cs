using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CusReqMainInfo_EditCloneHUBRFQ : Base {

		#region Locals
        protected RadioButtonList _radFactorySealedSource;
        protected RadioButtonList _radObsolete;
        protected RadioButtonList _radLastTimeBuy;
        protected RadioButtonList _radRefirbsAcceptable;
        protected RadioButtonList _radTestingRequired;
        protected RadioButtonList _radAlternativesAccepted;
        protected RadioButtonList _radRepeatBusiness;
        protected FlexiDataTable _tblPartdetails;
        protected Panel _pnlPartDetail;
        // protected Panel _pnlPartdetails;

        protected List<string> _lstSources = new List<string>();
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CloneHUBRFQ");
			AddScriptReference("Controls.Nuggets.CusReqMainInfo.EditCloneHUBRFQ.CusReqMainInfo_EditCloneHUBRFQ.js");
            WireUpControls();
            SetupTable();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
            SetupSelectSourceScreen();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

        private void SetupSelectSourceScreen()
        {
            //AddRadioButton("Yes", "YES");
            //AddRadioButton("No", "NO");


        }
        private void SetupTable()
        {

           // _tblPartdetails.AllowSelection = true;
           // _tblPartdetails.Columns.Add(new FlexiDataColumn("PartNo", Unit.Pixel(50)));
           // _tblPartdetails.Columns.Add(new FlexiDataColumn("Manufacturer", Unit.Pixel(65)));
           // _tblPartdetails.Columns.Add(new FlexiDataColumn("MSL", Unit.Pixel(22)));
           // _tblPartdetails.Columns.Add(new FlexiDataColumn("CountryOfOrigin", Unit.Pixel(65)));
           // _tblPartdetails.Columns.Add(new FlexiDataColumn("Packaging", Unit.Pixel(30)));
           //// _tblPartdetails.Columns.Add(new FlexiDataColumn("PackagingSize",WidthManager.GetWidth(WidthManager.ColumnWidth.Package)));
           // _tblPartdetails.Columns.Add(new FlexiDataColumn("Descriptions"));
        }
        private void AddRadioButton(string strResourceTitle, string strJavascriptType)
        {
            //_radFactorySealedSource.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radObsolete.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radLastTimeBuy.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radRefirbsAcceptable.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radTestingRequired.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radAlternativesAccepted.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_radRepeatBusiness.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
            //_lstSources.Add(strJavascriptType);
        }
        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
          
            //_radFactorySealedSource = (RadioButtonList)ctlDesignBase.FindControl("radFactorySealedSource");
            //_radObsolete = (RadioButtonList)ctlDesignBase.FindControl("radObsolete");
            //_radLastTimeBuy = (RadioButtonList)ctlDesignBase.FindControl("radLastTimeBuy");
            //_radRefirbsAcceptable = (RadioButtonList)ctlDesignBase.FindControl("radRefirbsAcceptable");
            //_radTestingRequired = (RadioButtonList)ctlDesignBase.FindControl("radTestingRequired");
            //_radAlternativesAccepted = (RadioButtonList)ctlDesignBase.FindControl("radAlternativesAccepted");
            //_radRepeatBusiness = (RadioButtonList)ctlDesignBase.FindControl("radRepeatBusiness");

            //_tblPartdetails = (FlexiDataTable)ctlDesignBase.FindContentControl("tblPartdetails");
            _pnlPartDetail = (Panel)ctlDesignBase.FindContentControl("pnlPartDetail");
            // _pnlPartdetails = (Panel)Functions.FindControlRecursive(this, "pnlPartdetails");

        }
		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CusReqMainInfo_EditCloneHUBRFQ", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intCustomerRequirementID", _objQSManager.CustomerRequirementID);
            //_scScriptControlDescriptor.AddElementProperty("radFactorySealedSource", _radFactorySealedSource.ClientID);

            //_scScriptControlDescriptor.AddElementProperty("radObsolete", _radObsolete.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radLastTimeBuy", _radLastTimeBuy.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radRefirbsAcceptable", _radRefirbsAcceptable.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radTestingRequired", _radTestingRequired.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radAlternativesAccepted", _radAlternativesAccepted.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("radRepeatBusiness", _radRepeatBusiness.ClientID);
            //_scScriptControlDescriptor.AddProperty("arySources", _lstSources);
            //_scScriptControlDescriptor.AddComponentProperty("tblPartdetails", _tblPartdetails.ClientID);
            _scScriptControlDescriptor.AddElementProperty("pnlPartDetail", _pnlPartDetail.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctltblPartdetails", ((ItemSearch.Base)FindContentControl("ctltblPartdetails")).ctlDesignBase.ClientID);

            // _tblPromiseReasonLog = (FlexiDataTable)ctlDesignBase.FindContentControl("tblPartdetails");
            //_scScriptControlDescriptor.AddElementProperty("tblPartdetails", _tblPartdetails.ClientID);


            //_scScriptControlDescriptor.AddElementProperty("btn1", FindFieldControl("ctlPartNo", "btn1").ClientID);
            //_scScriptControlDescriptor.AddElementProperty("btn2", FindFieldControl("ctlPartNo", "btn2").ClientID);
            //_scScriptControlDescriptor.AddElementProperty("lblError", FindFieldControl("ctlPartNo", "lblError").ClientID);

        }

	}
}