Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Profile");Rebound.GlobalTrader.Site.Pages.Profile.Profile=function(n){Rebound.GlobalTrader.Site.Pages.Profile.Profile.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Profile.Profile.prototype={get_ctlLeft_RecentlyViewed:function(){return this._ctlLeft_RecentlyViewed},set_ctlLeft_RecentlyViewed:function(n){this._ctlLeft_RecentlyViewed!==n&&(this._ctlLeft_RecentlyViewed=n)},get_ctlUserPreferences:function(){return this._ctlUserPreferences},set_ctlUserPreferences:function(n){this._ctlUserPreferences!==n&&(this._ctlUserPreferences=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Profile.Profile.callBaseMethod(this,"initialize")},goInit:function(){this._ctlUserPreferences&&this._ctlUserPreferences.addSaveEditComplete(Function.createDelegate(this,this.ctlUserPreferences_SaveEditComplete));Rebound.GlobalTrader.Site.Pages.Profile.Profile.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlLeft_RecentlyViewed&&this._ctlLeft_RecentlyViewed.dispose(),this._ctlUserPreferences&&this._ctlUserPreferences.dispose(),this._ctlLeft_RecentlyViewed=null,this._ctlUserPreferences=null,Rebound.GlobalTrader.Site.Pages.Profile.Profile.callBaseMethod(this,"dispose"))},ctlUserPreferences_SaveEditComplete:function(){this._ctlLeft_RecentlyViewed.refresh()}};Rebound.GlobalTrader.Site.Pages.Profile.Profile.registerClass("Rebound.GlobalTrader.Site.Pages.Profile.Profile",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);