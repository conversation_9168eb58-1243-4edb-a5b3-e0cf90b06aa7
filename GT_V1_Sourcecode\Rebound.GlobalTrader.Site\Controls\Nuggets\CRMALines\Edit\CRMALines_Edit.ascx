<%--//Marker     Changed by      Date         Remarks
//    [003]      Suhail          15/05/2018   Added Avoidable on CRMA Line--%>
<%@ Control Language="C#" CodeBehind="CRMALines_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CRMALines_Edit")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">

            <ReboundUI_Form:FormField id="ctlCustomerRMA" runat="server" FieldID="lblCustomerRMA" ResourceTitle="CustomerRMANo" >
	            <Field><asp:Label ID="lblCustomerRMA" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
	            <Field><asp:Label ID="lblCustomer" runat="server" /></Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="lblPartNo" ResourceTitle="PartNo">
	            <Field><asp:Label ID="lblPartNo" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="txtQuantity" ResourceTitle="Quantity" IsRequiredField="true">
				<Field>				    
				    <ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="100" TextBoxMode="Numeric" />				
				    
				</Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlQuantity_Lable" runat="server" FieldID="lblQuentity" ResourceTitle="Quantity">
				<Field>				    
				    <asp:Label ID="lblQuentity" runat="server" />				    
				    
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlReason" runat="server" FieldID="ctlItemsReason1" ResourceTitle="Reason" DisplayRequiredFieldMarkerOnly="true" >
                <Field> 
				<ReboundUI_Table:Base id="tblItemsReason1" runat="server">
				<ReboundUI_FormFieldCollection:HoverMenuList id="ctlItemsReason1" runat="server" style="margin:0px;"  />
				</ReboundUI_Table:Base>				
				<%--<ReboundUI:ReboundTextBox ID="txtReason" runat="server" Width="400" TextMode="MultiLine" Rows="8" />--%>
				</Field>			
		 </ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlReasion2" runat="server" FieldID="tblItemsReason2" ResourceTitle="Reason2">
				<Field>
				<ReboundUI_Table:Base id="tblItemsReason2" runat="server">
				<ReboundUI_FormFieldCollection:HoverMenuList id="ctlItemsReason2" runat="server" style="margin:0px;" />				
				</ReboundUI_Table:Base>
				</Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField ID="ctlIsAvoidable" runat="server" FieldID="chkIsAvoidable" ResourceTitle="IsAvoidable">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkIsAvoidable" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlLineNotes" runat="server" FieldID="txtLineNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtLineNotes" runat="server" Width="400" TextMode="multiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlRootCause" runat="server" FieldID="txtRootCause" ResourceTitle="RootCause">
				<Field><ReboundUI:ReboundTextBox ID="txtRootCause" runat="server" Width="400" TextMode="multiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>

            <%--[002] start code--%>
           <ReboundUI_Form:FormField id="ctlPrintHazWar" runat="server" FieldID="chkPrintHazWar" ResourceTitle="PrintHazWarning" >
				<Field><ReboundUI:ImageCheckBox ID="chkPrintHazWar" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
           <%--[002] start end--%>
			
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
