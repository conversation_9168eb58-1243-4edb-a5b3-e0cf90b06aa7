GO

IF object_id('ufn_convert_BOM_Req', 'FN') IS NOT NULL
BEGIN
    DROP FUNCTION [dbo].[ufn_convert_BOM_Req]
END
GO
/*
-- ==========================================================================================
-- TASK      	UPDATED BY     		DATE         ACTION 			DESCRIPTION                                    
-- US-201231 	Phuc.HoangDinh     	24-04-2024   CREATE				Create for ticket US-201231 [RP-2609] BOM Manager Communication Notes Per Line
-- ==========================================================================================
*/

CREATE FUNCTION [dbo].[ufn_convert_BOM_Req] 
(
	-- Add the parameters for the function here
	@groupid int
)
RETURNS varchar(100)
AS
BEGIN
	-- Declare the return variable here
	DECLARE @temp VARCHAR(MAX)
SELECT @temp = COALESCE(@temp+', ' ,'') + cast(CustomerReqNo as varchar(20) )
FROM tbBOMExpediteNotes where GroupID = @groupid 

---SELECT @temp

	-- Return the result of the function
	RETURN @temp

END

GO