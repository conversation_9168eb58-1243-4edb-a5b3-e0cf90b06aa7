﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_tbOfferImportByExcelTemp] (
    @FileID int,               -- Search keyword for filtering
    @PageNumber INT = 1,       -- Page number (default is 1)
    @PageSize INT = 10,        -- Number of rows per page (default is 10)
	@ShowMismatchOnly BIT = 0
)
AS
BEGIN
	WITH CTE_Offer AS (
		SELECT 
			ROW_NUMBER() OVER (ORDER BY [OfferTempId]) AS RowNum, 
			[OfferTempId] AS OfferredID,   
			CASE 
				WHEN MPN IS NULL THEN 'MPN is NULL'  
				WHEN LEN(MPN) > 256 THEN 'MPN length > 255' 
				ELSE NULL  
			END AS [MPNMessage],
			MPN,
			CASE 
				WHEN COST IS NULL THEN 'COST is NULL'
				WHEN ISNUMERIC(COST) = 0 THEN 'COST is not a valid' 
				WHEN TRY_CAST(REPLACE(COST, ',', '') AS FLOAT) IS NULL THEN 'COST is not a valid decimal'
				WHEN TRY_CAST(COST AS FLOAT) < 0 THEN 'COST is not a valid'
				ELSE NULL                                          
			END AS [COSTMessage],
			COST,
			CASE 
				WHEN LEN([LeadTime]) > 256 THEN 'LeadTime length > 255'  
				ELSE NULL  
			END AS [LeadTimeMessage],
			[LeadTime],
			CASE 
				WHEN LEN([SPQ]) > 256 THEN 'SPQ length > 255' 
				WHEN ISNUMERIC([SPQ]) = 0 THEN 'SPQ is not a valid'
				WHEN TRY_CAST(REPLACE([SPQ], ',', '') AS INT) IS NULL THEN 'SPQ is not a valid'
				WHEN TRY_CAST([SPQ] AS INT) < 0 THEN 'SPQ is not a valid' 
				ELSE NULL  
			END AS [SPQMessage],
			[SPQ],
			CASE 
				WHEN MOQ IS NULL THEN 'MOQ is NULL'
				WHEN ISNUMERIC([MOQ]) = 0 THEN 'MOQ is not a valid'
				WHEN TRY_CAST(REPLACE([MOQ], ',', '') AS INT) IS NULL THEN 'MOQ is not a valid'
				WHEN TRY_CAST([MOQ] AS INT) < 0 THEN 'MOQ is not a valid' 
				ELSE NULL                                          
			END AS [MOQMessage],
			MOQ,
			CASE 
				WHEN TRY_CONVERT(DATE, OfferedDate, 103) IS NULL OR
					 (OfferedDate NOT LIKE '[0-3][0-9]/[0-1][0-9]/[1-9][0-9][0-9][0-9]' 
					  AND OfferedDate NOT LIKE '[1-9]/[1-9]/[1-9][0-9][0-9][0-9]') THEN
					 'Offered Date is not in dd/MM/yyyy or d/M/yyyy format'
				ELSE NULL                                          
			END AS OfferedDateMessage,
			OfferedDate,
			CASE 
				WHEN cmp.CompanyName IS NULL THEN 'Vendor does not exist'  
				ELSE NULL
			END AS VendorMessage,
			Vendor,
			temp.ClientNo,
            temp.DLUP,
            temp.GeneratedFileName,
			MFR,
			Remarks,
			'' as RemarksMessage,
			temp.OfferTempId
			,CASE 
				WHEN manu.ManufacturerName IS NULL THEN 'Manufacturer does not exist'  
				ELSE NULL
			END AS MFRMessage
			,GTMFR
			,GTVendor
		FROM 
			BorisGlobalTraderImports.dbo.[tbOfferImportByExcelTemp] temp WITH (NOLOCK)
		OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbCompany cmp 
			WHERE cmp.CompanyName = temp.Vendor AND cmp.ClientNo = 114 AND cmp.IsSupplier = 1
		) cmp
		OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbManufacturer manu 
			WHERE manu.ManufacturerName = temp.MFR AND manu.Inactive = 0
		) manu
		INNER JOIN BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile lf 
			ON lf.ID = temp.HUBOfferImportLargeFileID AND lf.status = 'Awaiting Data Correction'
		WHERE 
			HUBOfferImportLargeFileID = @FileID
			AND (
				@ShowMismatchOnly = 0 OR(
				MPN IS NULL 
				OR LEN(MPN) > 256
				OR LEN([LeadTime]) > 256
				OR TRY_CAST(REPLACE([SPQ], ',', '') AS INT) IS NULL
				OR TRY_CAST([SPQ] AS INT) < 0
				OR ISNUMERIC([SPQ]) = 0
				OR LEN([SPQ]) > 256
				OR TRY_CAST(REPLACE([MOQ], ',', '') AS INT) IS NULL
				OR TRY_CAST([MOQ] AS INT) < 0
				OR ISNUMERIC([MOQ]) = 0
				OR MOQ IS NULL 
				OR (OfferedDate NOT LIKE '[0-3][0-9]/[0-1][0-9]/[1-9][0-9][0-9][0-9]' 
					AND OfferedDate NOT LIKE '[1-9]/[1-9]/[1-9][0-9][0-9][0-9]')
				OR TRY_CAST(REPLACE(COST, ',', '') AS FLOAT) IS NULL
				OR TRY_CAST(COST AS FLOAT) < 0
				OR ISNUMERIC(COST) = 0
				OR COST IS NULL 
				OR cmp.CompanyName IS NULL
				OR manu.ManufacturerName IS NULL
				)
			)
	)
	SELECT *
	FROM CTE_Offer
	WHERE RowNum BETWEEN (@PageNumber - 1) * @PageSize + 1 AND @PageNumber * @PageSize
	ORDER BY RowNum;

	SELECT 
			COUNT(*) AS TotalCount
		FROM 
			BorisGlobalTraderimports.dbo.tbOfferImportByExcelTemp temp WITH (NOLOCK)
		OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbCompany cmp 
			WHERE cmp.CompanyName = temp.Vendor AND cmp.ClientNo = 114 AND cmp.IsSupplier = 1
		) cmp
		OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbManufacturer manu 
			WHERE manu.ManufacturerName = temp.MFR AND manu.Inactive = 0
		) manu
		INNER JOIN BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile lf 
			ON lf.ID = temp.HUBOfferImportLargeFileID AND lf.status = 'Awaiting Data Correction'
		WHERE 
			HUBOfferImportLargeFileID = @FileID
			AND
			(
				@ShowMismatchOnly = 0 OR
				(MPN IS NULL 
				OR LEN(MPN) > 256
				OR LEN([LeadTime]) > 256
				OR TRY_CAST(REPLACE([SPQ], ',', '') AS INT) IS NULL
				OR TRY_CAST([SPQ] AS INT) < 0
				OR ISNUMERIC([SPQ]) = 0
				OR LEN([SPQ]) > 256
				OR TRY_CAST(REPLACE([MOQ], ',', '') AS INT) IS NULL
				OR TRY_CAST([MOQ] AS INT) < 0
				OR ISNUMERIC([MOQ]) = 0
				OR MOQ IS NULL 
				OR (OfferedDate NOT LIKE '[0-3][0-9]/[0-1][0-9]/[1-9][0-9][0-9][0-9]' 
					AND OfferedDate NOT LIKE '[1-9]/[1-9]/[1-9][0-9][0-9][0-9]')
				OR TRY_CAST(REPLACE(COST, ',', '') AS FLOAT) IS NULL
				OR TRY_CAST(COST AS FLOAT) < 0
				OR ISNUMERIC(COST) = 0
				OR COST IS NULL 
				OR cmp.CompanyName IS NULL
				OR manu.ManufacturerName IS NULL
				)
			);

	SELECT 
			COUNT(*) AS TotalRecordsError
		FROM 
			BorisGlobalTraderimports.dbo.tbOfferImportByExcelTemp temp WITH (NOLOCK)
		OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbCompany cmp 
			WHERE cmp.CompanyName = temp.Vendor AND cmp.ClientNo = 114 AND cmp.IsSupplier = 1
		) cmp
		OUTER APPLY (
			SELECT TOP 1 * FROM dbo.tbManufacturer manu 
			WHERE manu.ManufacturerName = temp.MFR AND manu.Inactive = 0
		) manu
		INNER JOIN BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile lf 
			ON lf.ID = temp.HUBOfferImportLargeFileID AND lf.status = 'Awaiting Data Correction'
		WHERE 
			HUBOfferImportLargeFileID = @FileID
			AND
			(
				(MPN IS NULL 
				OR LEN(MPN) > 256
				OR LEN([LeadTime]) > 256
				OR TRY_CAST(REPLACE([SPQ], ',', '') AS INT) IS NULL
				OR TRY_CAST([SPQ] AS INT) < 0
				OR ISNUMERIC([SPQ]) = 0
				OR LEN([SPQ]) > 256
				OR TRY_CAST(REPLACE([MOQ], ',', '') AS INT) IS NULL
				OR TRY_CAST([MOQ] AS INT) < 0
				OR ISNUMERIC([MOQ]) = 0
				OR MOQ IS NULL 
				OR (OfferedDate NOT LIKE '[0-3][0-9]/[0-1][0-9]/[1-9][0-9][0-9][0-9]' 
					AND OfferedDate NOT LIKE '[1-9]/[1-9]/[1-9][0-9][0-9][0-9]')
				OR TRY_CAST(REPLACE(COST, ',', '') AS FLOAT) IS NULL
				OR TRY_CAST(COST AS FLOAT) < 0
				OR ISNUMERIC(COST) = 0
				OR COST IS NULL 
				OR cmp.CompanyName IS NULL
				OR manu.ManufacturerName IS NULL
				)
			);

END;
GO


