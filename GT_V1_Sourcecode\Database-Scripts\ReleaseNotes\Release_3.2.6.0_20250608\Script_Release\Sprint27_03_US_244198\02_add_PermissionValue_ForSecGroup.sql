﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-244198]     Cuong.Dox		 2-May-2025			CREATE		Sourcing - Permissions of Bulk Edit function needs to be updated
===========================================================================================  
*/
DECLARE @tbSecurityFunctions TABLE (SecurityFunctionId INT);
INSERT INTO @tbSecurityFunctions VALUES(20010049);

DECLARE @tbHubSecurityGroups TABLE(SecurityGroupId INT, IsAdmin BIT);
INSERT INTO @tbHubSecurityGroups
SELECT 
	SecurityGroupId, 
	Administrator
FROM tbSecurityGroup

--Clear all existing permissions
DELETE tbSecurityGroupSecurityFunctionPermission
WHERE SecurityFunctionNo IN (SELECT SecurityFunctionId FROM @tbSecurityFunctions)

;WITH cte AS(
	SELECT SecurityGroupId,
		SecurityFunctionId,
		IsAdmin
	FROM @tbHubSecurityGroups, @tbSecurityFunctions
)
INSERT INTO tbSecurityGroupSecurityFunctionPermission 
(  
	SecurityGroupNo  
    ,SecurityFunctionNo  
    ,IsAllowed  
    ,DLUP
) 
SELECT 
	SecurityGroupId,
	SecurityFunctionId,	
	IsAdmin, --default: allow for admin only
	GETDATE()
FROM cte 
