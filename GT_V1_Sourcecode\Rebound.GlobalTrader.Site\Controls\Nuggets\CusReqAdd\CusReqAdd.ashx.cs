//-----------------------------------------------------------------------------------------
// RP 17.12.2009:
// - add Internal Notes (Instructions) field
//Marker     Changed by      Date         Remarks
//[001]      Vinay           21/11/2012   Please make Rosh as a compulsory field on the following:- Requirements,Quotes,PO,SO
//[002]     A<PERSON><PERSON>      25-Jan-2019  Sales Dashboard Changes/ Req Dashboard Headings  
//[003]    Abhinav <PERSON>    14-July-2021 Add new logic for partwatch matching.
//[004]    Abhinav <PERSON>    10-Aug-2021  Add customerReqNumber to the partwatch notification
//[005]      Soorya          03/03/2023   RP-1048 Remove AI code
//[006]     Ravi             29-08-2023   RP-2227 AS6081
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.IO;
//using System.Linq;
using System.Net;
using System.Text;
using HttpUtils;
using IHSPart;
using System.Text.RegularExpressions;
using System.Data.Common;
using System.Reflection;
using Rebound.GlobalTrader.Site.Code.Common;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Linq;
//using Microsoft.ApplicationInsights; //[005] 

using System.Configuration;
using Rebound.GlobalTrader.DAL;
using System.Web.Script.Serialization;
using Newtonsoft.Json;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CustomerRequirementAdd : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                if (context.Request.QueryString["action"] != null)
                    Action = context.Request.QueryString["action"];

                switch (Action)
                {
                    case "AddNew": AddNew(); break;
                    case "GetData": GetData(); break;
                    case "GetDataGrid": GetDataGrid(); break;
                    case "GetPartDetail": GetPartDetail(); break;
                    case "GetIHSPartDetails": GetIHSPartDetails(); break;
                    case "SaveIHSPartDetail": SaveIHSPartDetail(); break;
                    case "GetRestrictedManufacturer": GetRestrictedManufacturer(); break;
                    case "GetIHSPartEccnDetail": GetIHSPartEccnDetail(); break;
                    case "GetIHSEccnCodeDetail": GetIHSEccnCodeDetail(); break;
                    case "GetKubAssistanceDetails": GetKubAssistanceDetails(); break;//Anuj
                    case "GetKubCountryWiseSaleDetails": GetKubCountryWiseSaleDetails(); break; // Anuj
                    case "GetKubTop3BuyPriceDetails": GetKubTop3BuyPriceDetails(); break; // Anuj
                    case "GetKubLast10RecentQuoteDetails": GetKubLast10RecentQuoteDetails(context); break; // Anuj
                    case "GetKubAvgPriceDetails": GetKubAvgPriceDetails(context); break; // Anuj
                    case "GetKubTotalLineInvoiceDetails": GetKubTotalLineInvoiceDetails(context); break; // Anuj
                    case "GetKubMainProductGroupDetails": GetKubMainProductGroupDetails(context); break; // Anuj
                    case "GetKubConfigData": GetKubConfigData(); break; // Anuj
                    case "StartKubCache": StartKubCache();break;
                    case "ShowReadMoreData": ShowReadMoreData(); break;
                    case "StartKubCacheForBrowsePage":StartKubCacheForBrowsePage(context); break;
                    case "IsAllowedEnabled": IsAllowedEnabled(context); break;
                    case "GetGPCalculationDetails": GetGPCalculationDetails(); break;
                    case "GetGPLastSaleCalculationDetails": GetGPLastSaleCalculationDetails(); break;
                    case "UpsertLyticaAPIData": UpsertLyticaAPIData(); break;
                    case "RefreshLyticaAPIAfter3Days": RefreshLyticaAPIAfter3Days(context); break;
                    case "LyticaAPIFromBOM": LyticaAPIFromBOM(context); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        private void RefreshLyticaAPIAfter3Days(HttpContext context)
        {
            try
            {
                string partNo = string.IsNullOrEmpty(context.Request.QueryString["PartNumber"]) ? string.Empty : context.Request.QueryString["PartNumber"];
                partNo = Functions.BeautifyPartNumber(partNo);
                string mfrCode = string.IsNullOrEmpty(context.Request.QueryString["mfr"]) ? string.Empty : context.Request.QueryString["mfr"];
                string mfrNo = string.IsNullOrEmpty(context.Request.QueryString["mfrNo"]) ? string.Empty : context.Request.QueryString["mfrNo"];
                bool isUpdated = BLL.CustomerRequirement.RefreshLyticaDataAfter3days(partNo, mfrCode, Convert.ToInt32(mfrNo));
                if (isUpdated) 
                {
                    TriggerLyticaAPI(partNo, mfrCode, true, mfrNo);
                }
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at RefreshLyticaAPIAfter3Days in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
        }

        private void UpsertLyticaAPIData()
        {
            try
            {
                string partNo = GetFormValue_String("partNo");
                string manufactureName = GetFormValue_String("MfrFullName");
                TriggerLyticaAPI(partNo, manufactureName, false);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error at UpsertLyticaAPIData in CusReqAdd.ashx.cs : " + ex.Message);
                WriteError(ex);
                ServiceStatus = false;

            }
        }

        private void LyticaAPIFromBOM(HttpContext context)
        {
            var configs = BLL.SettingItem.GetList();
            var config = configs.Find(x => x.SettingItemName.Equals("LyticaApiTurnOn", StringComparison.InvariantCultureIgnoreCase));

            if (config.DefaultValue.Equals("false", StringComparison.InvariantCultureIgnoreCase))
            {
                return;
            }

            DataTable dtcount = new DataTable();
            dtcount.Clear();
            int displayLength = 25000;
            int displayStart = 0;
            int sortCol = 0;
            string sortDir = "asc";
            string search = context.Request.Params["search[value]"];
            int selectedClientId = int.Parse(context.Request.Params["SelectedclientId"]);
            dtcount = Stock.GetBOMManagerDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, selectedClientId, 0);
            Dictionary<string, List<string>> partMFRs = new Dictionary<string, List<string>>();
            for (int i = 0; i < dtcount.Rows.Count; i++)
            {
                string part = dtcount.Rows[i][4].ToString();
                if (partMFRs.ContainsKey(part))
                {
                    partMFRs[part].Add(dtcount.Rows[i][3].ToString());
                }
                else
                {
                    partMFRs.Add(part, new List<string> { dtcount.Rows[i][3].ToString() });
                }
            }
            string partNo = '"' + String.Join("|", new List<string>(partMFRs.Keys)) + '"';
            TriggerLyticaAPIFromBOM(partNo, partMFRs);
        }

        private void TriggerLyticaAPIFromBOM(string partNo, Dictionary<string, List<string>> partMFRs)
        {
            List<LyticaAPI> LyticaData = new List<LyticaAPI>();
            string partJson = @"{""origMPN"": " + partNo + "}";
            LyticaData = APIIntegration.GetLyticaDataFromApi(partJson);
            string jsonData = "";
            LyticaData = LyticaData.Where(x => !string.IsNullOrEmpty(x.Manufacturer)).ToList();
            if (LyticaData.Count > 0)
            {
                //List<string> mfrs = partMFRs.Values.SelectMany(x => x).ToList();
                //List<string> mfrsNotInAPI = mfrs.Where(x => !LyticaData.Any(y => y.Manufacturer.Equals(x))).ToList();
                //foreach (string mfr in mfrsNotInAPI)
                //{
                //    LyticaAPI element = new LyticaAPI
                //    { 
                //        OriginalPartSearched = partMFRs.FirstOrDefault(x => x.Value.Any(value => value == mfr)).Key,
                //        Manufacturer = mfr
                //    };
                //    LyticaData.Add(element);
                //};
                
                jsonData = JsonConvert.SerializeObject(LyticaData);
                CustomerRequirement.UpsertLyticaAPI(jsonData, SessionManager.LoginID);
            }
            //else
            //{
            //    List<LyticaAPI> data = new List<LyticaAPI>();
            //    foreach (var dict in partMFRs)
            //    {
            //        if (dict.Value.Count > 0)
            //        {
            //            data.Add(dict.Value.Select(y => new LyticaAPI { OriginalPartSearched = dict.Key, Manufacturer = y}).FirstOrDefault());
            //        }
            //        data.Add(new LyticaAPI { OriginalPartSearched = dict.Key, Manufacturer = dict.Value.FirstOrDefault() });
            //    }
            //    jsonData = JsonConvert.SerializeObject(data);
            //}
            //CustomerRequirement.UpsertLyticaAPI(jsonData, SessionManager.LoginID);
        }

        private static void TriggerLyticaAPI(string partNo, string manufactureName, bool isMfrCode, string mfrNo = "")
        {
            var configs = BLL.SettingItem.GetList();
            var config = configs.Find(x => x.SettingItemName.Equals("LyticaApiTurnOn", StringComparison.InvariantCultureIgnoreCase));

            if (config.DefaultValue.Equals("false", StringComparison.InvariantCultureIgnoreCase))
            {
                return;
            }

            List<LyticaAPI> LyticaData = new List<LyticaAPI>();
            string partJson = @"{""origMPN"": " + '"' + partNo + '"' + "}";
            LyticaData = APIIntegration.GetLyticaDataFromApi(partJson);
            LyticaData = LyticaData.Where(x => !string.IsNullOrEmpty(x.Manufacturer)).ToList();

            if (LyticaData.Count > 0)
            {
                //var data = new LyticaAPI();
                //string jsonData = "";
                //manufactureName = isMfrCode ? CustomerRequirement.GetManufacturerNameByCode(manufactureName, mfrNo) : manufactureName;
                //bool isContainMfr = LyticaData.Any(x => x.Manufacturer.Equals(manufactureName));
                //data = isContainMfr ? LyticaData.Where(x => x.OriginalPartSearched.Equals(partNo) && x.Manufacturer.Equals(manufactureName)).FirstOrDefault()
                //    : data = new LyticaAPI { OriginalPartSearched = partNo, Manufacturer = manufactureName };
                string jsonData = JsonConvert.SerializeObject(LyticaData);
                CustomerRequirement.UpsertLyticaAPI(jsonData, SessionManager.LoginID);
            }
        }

        public void GetKubConfigData()
        {
           var Settinglist =  BLL.Setting.GetListValues(null);
            
            string json = Newtonsoft.Json.JsonConvert.SerializeObject(Settinglist.Where(x => x.SettingItemID == 17).ToList());
            _context.Response.Write(json);
            //_context.Response.Write(jsn.Result);

        }

        /// <summary>
        /// Add new customerRequirement
        /// </summary>
        public void AddNew()
        {
            try
            {
                string SalesmanName = GetFormValue_String("SalesmanName");
                int SalesManNo = GetFormValue_Int("SalesmanNo");
                string altPartNo = GetFormValue_String("PartNo");
                int intResult = CustomerRequirement.Insert(
                    SessionManager.ClientID
                    , GetFormValue_String("PartNo")
                    , GetFormValue_NullableInt("Mfr")
                    , GetFormValue_String("DateCd")
                    , GetFormValue_NullableInt("Package")
                    , GetFormValue_NullableInt("Quantity")
                    , GetFormValue_NullableDouble("Price")
                    , GetFormValue_NullableInt("CurrencyNo")
                    , DateTime.Now
                    , GetFormValue_NullableInt("SalesmanNo", LoginID)
                    , GetFormValue_NullableDateTime("DateRequired", DateTime.Now)
                    , GetFormValue_String("Notes")
                    , GetFormValue_String("Instructions")
                    , false
                    , GetFormValue_NullableInt("CMNo")
                    , GetFormValue_NullableInt("ContactNo")
                    , GetFormValue_NullableInt("Usage")
                    , false
                    , 0
                    , 0
                    , GetFormValue_NullableInt("Product")
                    , GetFormValue_String("CustomerPart")
                    , false
                    //[001] code start
                    , GetFormValue_NullableByte("ROHS")
                    //[001] code end
                    , LoginID
                    , GetFormValue_Boolean("PartWatch")
                    , GetFormValue_Boolean("BOM")
                    , GetFormValue_String("BOMName")
                    , GetFormValue_NullableInt("BOMNo")
                    , GetFormValue_Boolean("FactorySealed")
                    , GetFormValue_String("MSL")

                    , GetFormValue_Boolean("PQA")
                    , GetFormValue_Boolean("ObsoleteChk")
                    , GetFormValue_Boolean("LastTimeBuyChk")
                    , GetFormValue_Boolean("RefirbsAcceptableChk")
                    , GetFormValue_Boolean("TestingRequiredChk")
                    , GetFormValue_NullableDouble("TargetSellPrice")
                    , GetFormValue_NullableDouble("CompetitorBestOffer")
                    , GetFormValue_NullableDateTime("CustomerDecisionDate", DateTime.Now)
                    , GetFormValue_NullableDateTime("RFQClosingDate", DateTime.Now)
                    , GetFormValue_NullableInt("QuoteValidityRequired")
                    , GetFormValue_NullableInt("Type")
                    , GetFormValue_Boolean("OrderToPlace")
                    , GetFormValue_NullableInt("RequirementforTraceability")
                    , GetFormValue_String("EAU")
                    , GetFormValue_Boolean("AlternativesAccepted")
                    , GetFormValue_Boolean("RepeatBusiness")
                    , GetFormValue_NullableInt("SalesPersion")
                    //ihs code start 
                    , GetFormValue_String("CountryOfOrigin")
                    , GetFormValue_NullableInt("CountryOfOriginNo")
                    , GetFormValue_String("LifeCycleStage")
                    , GetFormValue_String("HTSCode")
                    , GetFormValue_NullableDouble("AveragePrice")
                    , GetFormValue_String("Packaging")
                    , GetFormValue_String("PackagingSize")
                    , GetFormValue_String("Descriptions")
                    , GetFormValue_NullableInt("IHSPartsId")
                    , GetFormValue_String("ihsCurrencyCode")
                    , GetFormValue_String("IHSProduct")
                    , GetFormValue_String("ECCNCode")
                    , GetFormValue_NullableInt("ECCNNo")
                    , GetFormValue_NullableBoolean("CounterfeitElectronicPart",0) //[006]

                    //ihs code end

                    );
                if (intResult > 0)
                {
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("NewID", intResult);
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                    bool Alternatives = Convert.ToBoolean(GetFormValue_String("Alternatives"));
                    if (Alternatives == true)
                    {
                     int intResults = BLL.CustomerRequirement.InsertAsAllAlternate(
                     SessionManager.ClientID
                    , altPartNo
                    , intResult);
             
                    }
                    #region PartWatch Match offer logic
                    DataTable dtOffer = CustomerRequirement.GetPartMachInfo(SessionManager.ClientID, GetFormValue_StringForPartSearch("PartNo"), intResult);

                    if (Convert.ToInt32(dtOffer.Rows[0]["CUstomerReqNo"]) > 0)
                    {
                        WebServices servic = new WebServices();
                        string strToLoginsArray = string.Empty;
                        //strToLoginsArray = LoginID.ToString();
                        strToLoginsArray = SalesManNo.ToString();
                        servic.NotifyPartWatchMatchMessage(strToLoginsArray, "", Functions.GetGlobalResource("Messages", "PartWatchMatchMsg"), 0, SalesmanName, intResult.ToString(), Convert.ToString(dtOffer.Rows[0]["CUstomerReqNo"]));
                        servic = null;
                    }
                    NotifyECCNREQ(intResult, LoginID);

                    #endregion
                }
                else if (intResult == -1)
                {
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("NewID", -1);
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception ex)
            {
                //[005]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("CusReqAdd.ashx: AddNew");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at AddNew in CusReqAdd.ashx.cs : " + ex.Message);
                WriteError(ex);
            }
        }
        private void GetData()
        {
            List<BLL.Part> lst = null;
            try
            {
                lst = BLL.Part.CustReqPart(SessionManager.ClientID, GetFormValue_StringForPartSearch("search"), GetFormValue_String("ManufacturerNo"), GetFormValue_String("DateCode"));
                JsonObject jsn = new JsonObject();
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ID", lst[i].PartName);
                        jsnRow.AddVariable("Name", lst[i].PartName);
                        jsnRow.AddVariable("PartsName", lst[i].PartNameWithManufacture);
                        jsnRow.AddVariable("ManufacturerNo", lst[i].ManufacturerNo);
                        jsnRow.AddVariable("Productname", lst[i].Productname);
                        jsnRow.AddVariable("Packagename", lst[i].Packagename);
                        jsnRow.AddVariable("DateCode", lst[i].DateCode);
                        jsnRow.AddVariable("ProductNo", lst[i].ProductNo);
                        jsnRow.AddVariable("PackageNo", lst[i].PackageNo);
                        jsnRow.AddVariable("ManufacturerName", lst[i].ManufacturerName);
                        jsnRow.AddVariable("ROHSNo", lst[i].ROHSNo);
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                jsn.AddVariable("ToolTip", jsnRows);
                OutputResult(jsn);
                jsnRows.Dispose(); jsnRows = null;
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }




        }

        class IHSServies
        {



            public class PartDetail
            {
                public string msl { get; set; }
                public string pckMethod { get; set; }
                public string pckCd { get; set; }
            }

            public class PartInfo
            {
                public string avgPrice { get; set; }
            }

            public class MfrPart
            {
                public string mfrFullName { get; set; }
                public string coo { get; set; }
            }
            public class ExpCompliance
            {
                public string htsCd { get; set; }
                public string eccn { get; set; }
            }

            public class Attributes
            {
                public MfrPart mfrPart { get; set; }
                public ExpCompliance expCompliance { get; set; }
                public PartDetail partDetail { get; set; }
                public PartInfo partInfo { get; set; }
                // public Collections collections { get; set; }

            }
            public class Price
            {
                public string source { get; set; }
                public double price { get; set; }
                public string currency { get; set; }
                public string telephone { get; set; }
                public string email { get; set; }
                public string priceAvailabilityLink { get; set; }
            }
            //public Price prices { get; set; }
            public class Collections
            {
                public List<Price> prices { get; set; }
            }

            public class Root
            {
                public Int64 id { get; set; }
                public string prtNbr { get; set; }
                public string mfrName { get; set; }
                public string prtStatus { get; set; }
                public string prtDesc { get; set; }
                public Attributes attributes { get; set; }
                public Collections collections { get; set; }



                public Nullable<int> ManufacturerNo { get; set; }
                public string Descriptions { get; set; }
                public string CountryOfOrigin { get; set; }
                public Nullable<int> CountryOfOriginNo { get; set; }
                public string LifeCycleStage { get; set; }

                public string MSL { get; set; }
                public Nullable<int> MSLNo { get; set; }
                public string HTSCode { get; set; }
                public Nullable<double> AveragePrice { get; set; }
                public string Packaging { get; set; }
                public Nullable<int> PackagingSize { get; set; }
                public Nullable<int> UpdatedBy { get; set; }
                public Nullable<System.DateTime> OriginalEntryDate { get; set; }
                public Nullable<System.DateTime> DLUP { get; set; }
                public Nullable<bool> Inactive { get; set; }
            }
        }
        class TokenRespose
        {
            public string authToken { get; set; }

        }
        private TokenRespose tokenRespose;
        string token = string.Empty;
        int Tokenvalidornot = 0;
        bool ServiceStatus = true;
        private int GetTokenNumber(string Tockenno, int tokenid)
        {
            try
            {
                string TokenNumber = "";
                Tokenvalidornot = BLL.Part.GetIHSTokenData(out TokenNumber);
                return Tokenvalidornot;
            }
            catch (Exception e)
            {
                WriteError(e);
                return 0;
            }
            finally
            {

            }
        }
        public void InsertTokenNumber(string TokenNumberInsert)
        {
            try
            {
                int intResult = CustomerRequirement.InsertTokenNumberFromAPI(
                    TokenNumberInsert
                    );
                if (intResult > 0)
                {

                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private void GetDataGrid()
        {
            string IHSUserName = Convert.ToString(ConfigurationManager.AppSettings["IHSUserName"]);
            string IHSUserPass = Convert.ToString(ConfigurationManager.AppSettings["IHSUserPass"]);
            string IHSTokenGenURL = Convert.ToString(ConfigurationManager.AppSettings["IHSTokenGenURL"]);
            try
            {
                //check token validity expire after 24 hr. code start
                //string TokenNumber = "";
                //Tokenvalidornot = BLL.Part.GetIHSTokenData(out TokenNumber);

                ////check token validity expire after 24 hr. code end
                var client = new HttpUtils.RestClient();
                //if (Tokenvalidornot > 0)
                //{
                //    if (!string.IsNullOrEmpty(TokenNumber))
                //    {
                //        GettbIHSpart(TokenNumber);
                //    }
                //}
                //else
                //{

                    client.EndPoint = IHSTokenGenURL;//@"https://4donlinetest.ihs.com/parts-saas/auth";
                    client.Method = HttpVerb.POST;
                    //client.PostData = "{\n\t\"username\":\"rebounde2\",\n\t\"password\":\"kmHs2L#vn8M\"\n}";
                    client.PostData = "{\n\t\"username\":\"" + IHSUserName + "\",\n\t\"password\":\"" + IHSUserPass + "\"\n}";
                    client.tokenRespose = null;
                    string json = client.MakeRequest();
                if (json != null || json !="")
                {
                    ServiceStatus = true;
                    //JavaScriptSerializer Serializer = new JavaScriptSerializer();
                    //var data1 = Serializer.Deserialize<TokenRespose>(json);

                    System.Web.Script.Serialization.JavaScriptSerializer jsSerializer = new System.Web.Script.Serialization.JavaScriptSerializer();
                    var data1 = jsSerializer.Deserialize<TokenRespose>(json);

                    token = data1.authToken;
                    //InsertTokenNumber(token);
                    GettbIHSpart(token);
                    //need to save token number in tbihstoken into  database .
                    // }
                }
                else
                {
                    ServiceStatus = false; 
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("ServiceStatus", ServiceStatus);
                jsn.AddVariable("Result", true);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
           
        }
        private void GettbIHSpart(string taktoken)
        {
            try
            {
   
                string IHSPartSearchURL = Convert.ToString(ConfigurationManager.AppSettings["IHSPartSearchURL"]);
                string strJson = "{\r\n   \"prtNbr\": \"'" + GetFormValue_String("partsearch") + "'\",\r\n   \"searchType\":\"" + GetFormValue_StringForPartSearch("searchType") + "\",\r\n   \"collections\":[\"prices\"]\r\n   \r\n   \r\n}";
                var client = new HttpUtils.RestClient();
                client.EndPoint = IHSPartSearchURL;
                client.Method = HttpVerb.POST;
                client.PostData = strJson;
                client.tokenRespose = taktoken;
                var json2 = client.MakeRequest();
                 if (json2 != null)
                {
                    ServiceStatus = true;
                    JavaScriptSerializer jsSerializer2 = new JavaScriptSerializer();
                    var data = jsSerializer2.Deserialize<List<IHSServies.Root>>(json2);
                    int intResult = CustomerRequirement.InsertIHSApiXML(
                           SessionManager.ClientID,
                           SessionManager.LoginID,
                           ParamsToXml(json2)
                          );
                    //GetDataGridPopupIHSTable();
                }
                else
                {
                    ServiceStatus = false;
                }
            }
            catch (Exception ex)
            {
                WriteError(ex);
                ServiceStatus = false;

            }
        
    }
        private static string ParamsToXml(string json2)
        {
            System.Text.StringBuilder strBuilder = new System.Text.StringBuilder();
            string strinAvgPrice = string.Empty;
            System.Web.Script.Serialization.JavaScriptSerializer jsSerializer2 = new System.Web.Script.Serialization.JavaScriptSerializer();
            var data = jsSerializer2.Deserialize<List<IHSServies.Root>>(json2);
            strBuilder.Append("<IHSResults>");
            foreach (IHSServies.Root item in data)
            {

                
                strBuilder.Append("<IHSResult>");
                strBuilder.Append("<IHSID>");
                strBuilder.Append(item.id);
                strBuilder.Append("</IHSID>");
                // Part
                strBuilder.Append("<prtNbr>");
                //strBuilder.Append(item.prtNbr);
                if (!string.IsNullOrEmpty(item.prtNbr))
                    strBuilder.Append(item.prtNbr.Replace("&", "&amp;"));
                else
                    strBuilder.Append("");
                strBuilder.Append("</prtNbr>");
                //Manuf Name
                strBuilder.Append("<mfrName>");
                strBuilder.Append(item.mfrName);
                strBuilder.Append("</mfrName>");

                strBuilder.Append("<prtStatus>");
                strBuilder.Append(item.prtStatus);
                strBuilder.Append("</prtStatus>");

                strBuilder.Append("<prtDesc>");
                //strBuilder.Append(item.prtDesc);
                if (!string.IsNullOrEmpty(item.prtDesc))
                {
                    strinAvgPrice = "";
                    if (item.attributes.partInfo != null)
                    {
                        strinAvgPrice = Convert.ToString(item.attributes.partInfo.avgPrice);
                        strBuilder.Append(item.prtDesc.Replace("&", "&amp;") + " , Avg Price : " + strinAvgPrice + " (Date Updated : " + DateTime.Now.ToString()+")");
                    }
                    else
                    {
                        strBuilder.Append(item.prtDesc.Replace("&", "&amp;"));
                    }

                    //strBuilder.Append(item.prtDesc.Replace("&", "&amp;"));
                }
                else
                {
                    strBuilder.Append("");
                }
                strBuilder.Append("</prtDesc>");

                //strBuilder.Append("<prtDesc>");
                //strBuilder.Append(item.prtDesc);
                //strBuilder.Append("</prtDesc>");

                //attributes
                if (item.attributes.mfrPart != null)
                {
                    strBuilder.Append("<mfrFullName>");
                    if (!string.IsNullOrEmpty(item.attributes.mfrPart.mfrFullName))
                        strBuilder.Append(item.attributes.mfrPart.mfrFullName.Replace("&", "&amp;"));
                    else
                        strBuilder.Append("");
                    strBuilder.Append("</mfrFullName>");

                    strBuilder.Append("<coo>");
                    strBuilder.Append(item.attributes.mfrPart.coo);
                    strBuilder.Append("</coo>");


                }
                else
                {
                    strBuilder.Append("<mfrFullName>");
                    strBuilder.Append("");
                    strBuilder.Append("</mfrFullName>");

                    strBuilder.Append("<coo>");
                    strBuilder.Append("");
                    strBuilder.Append("</coo>");
                }
                //expCompliance
                if (item.attributes.expCompliance != null)
                {
                    strBuilder.Append("<htsCd>");
                    strBuilder.Append(item.attributes.expCompliance.htsCd);
                    strBuilder.Append("</htsCd>");

                    strBuilder.Append("<eccn>");
                    strBuilder.Append(item.attributes.expCompliance.eccn);
                    strBuilder.Append("</eccn>");

                }
                else
                {

                    strBuilder.Append("<htsCd>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</htsCd>");

                    strBuilder.Append("<eccn>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</eccn>");
                }
                //Attribute part details
                if (item.attributes.partDetail != null)
                {
                    strBuilder.Append("<msl>");
                    strBuilder.Append(item.attributes.partDetail.msl);
                    strBuilder.Append("</msl>");

                    strBuilder.Append("<pckMethod>");
                    strBuilder.Append(item.attributes.partDetail.pckMethod);
                    strBuilder.Append("</pckMethod>");

                    strBuilder.Append("<pckCd>");
                    strBuilder.Append(item.attributes.partDetail.pckCd);
                    strBuilder.Append("</pckCd>");



                }
                else
                {

                    strBuilder.Append("<msl>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</msl>");

                    strBuilder.Append("<pckMethod>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</pckMethod>");

                    strBuilder.Append("<pckCd>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</pckCd>");

                }
                //Collections Price
                if (item.collections != null)
                {

                    strBuilder.Append("<source>");
                    strBuilder.Append(item.collections.prices[0].source);
                    strBuilder.Append("</source>");

                    strBuilder.Append("<price>");
                    strBuilder.Append(item.collections.prices[0].price);
                    strBuilder.Append("</price>");

                    strBuilder.Append("<currency>");
                    strBuilder.Append(item.collections.prices[0].currency);
                    strBuilder.Append("</currency>");

                    strBuilder.Append("<telephone>");
                    strBuilder.Append(item.collections.prices[0].telephone);
                    strBuilder.Append("</telephone>");

                    strBuilder.Append("<email>");
                    strBuilder.Append(item.collections.prices[0].email);
                    strBuilder.Append("</email>");

                    strBuilder.Append("<priceAvailabilityLink>");
                    strBuilder.Append(item.collections.prices[0].priceAvailabilityLink);
                    strBuilder.Append("</priceAvailabilityLink>");

                }
                else
                {
                    strBuilder.Append("<source>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</source>");

                    strBuilder.Append("<price>");
                    strBuilder.Append("0");
                    strBuilder.Append("</price>");

                    strBuilder.Append("<currency>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</currency>");

                    strBuilder.Append("<telephone>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</telephone>");

                    strBuilder.Append("<email>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</email>");

                    strBuilder.Append("<priceAvailabilityLink>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</priceAvailabilityLink>");
                }
                strBuilder.Append("</IHSResult>");
            }





            strBuilder.Append("</IHSResults>");
            return strBuilder.ToString();
        }
        private void GetDataGridPopupIHSTable()
        {
            List<BLL.Part> lst = null;
            try
            {
                lst = BLL.Part.CustReqPartsGRIDIHSAPI(
                      SessionManager.ClientID, GetFormValue_StringForPartSearch("partsearch")
                     ,GetFormValue_StringForPartSearch("searchType")
                     ,GetFormValue_NullableInt("SortIndex")
                     ,GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                     ,GetFormValue_NullableInt("PageIndex", 0)
                     ,GetFormValue_NullableInt("PageSize", 10)
                    );
                JsonObject jsn = new JsonObject();

                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ID", lst[i].PartName);
                        jsnRow.AddVariable("Name", Functions.ReplaceLineBreaks(lst[i].PartNameWithManufacture));
                        jsnRow.AddVariable("Ids", lst[i].ManufacturerNo + "|" + lst[i].ProductNo + "|" + lst[i].PackageNo);
                        jsnRow.AddVariable("ManufacturerNo", lst[i].ManufacturerNo);
                        jsnRow.AddVariable("Manufacturer", lst[i].ManufacturerName);

                        jsnRow.AddVariable("Descriptions", Functions.ReplaceLineBreaks( lst[i].Descriptions));
                        if (string.IsNullOrEmpty(lst[i].Descriptions))
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                        else if (lst[i].Descriptions.Length <= 10)
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                        else
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions.Substring(0, 10)));

                        jsnRow.AddVariable("ROHSNo", lst[i].ROHSNo);
                        jsnRow.AddVariable("ROHSName", lst[i].ROHSName);
                        jsnRow.AddVariable("CountryOfOrigin", lst[i].CountryOfOrigin);
                        jsnRow.AddVariable("CountryOfOriginNo", lst[i].CountryOfOriginNo);
                        jsnRow.AddVariable("PartStatus", Functions.ReplaceLineBreaks(lst[i].PartStatus));
                        jsnRow.AddVariable("HTSCode", Functions.ReplaceLineBreaks(lst[i].HTSCode));
                        jsnRow.AddVariable("AveragePrice", lst[i].AveragePrice);
                        jsnRow.AddVariable("Packaging", lst[i].Packaging);
                        jsnRow.AddVariable("PackagingSize", lst[i].PackagingSize);
                        jsnRow.AddVariable("IHSPartsId", lst[i].IHSPartsId);
                        jsnRow.AddVariable("ResultType", lst[i].ResultType);
                        jsnRow.AddVariable("ihsCurrencyCode", lst[i].IHSCurrencyCode);
                        jsnRow.AddVariable("ProdDesc",Functions.ReplaceLineBreaks( lst[i].ProductDescription));
                        jsnRow.AddVariable("ProdNo", lst[i].ProductNo);
                        jsnRow.AddVariable("IHSProdDesc", Functions.ReplaceLineBreaks(lst[i].IHSProductDescription));
                        jsnRow.AddVariable("IHSDutyCode", lst[i].IHSDutyCode);
                        jsnRow.AddVariable("RowCnt", lst[i].RowCnt);
                        jsnRow.AddVariable("ManufacturerFullName", lst[i].ManufacturerFullName);
                        jsnRow.AddVariable("IHSProduct", lst[i].IHSProduct);
                        
                        
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                //jsn.AddVariable("PartDetails", jsnRows);
                //OutputResult(jsn);
                //jsn.Dispose(); jsn = null;

                //
                jsn.AddVariable("PartDetails", jsnRows);
                OutputResult(jsn);
                jsnRows.Dispose();
                jsnRows = null;
                jsn.Dispose();
                jsn = null;
                lst = null;
               // Rebound.GlobalTrader.Site.Data.Base.GetData();
                //
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

         //IHS code end
        //[002] start
        private void GetPartDetail()
        {
            List<BLL.Part> lst = null;
            try
            {
                string partNo = GetFormValue_StringForPartSearch("partNo");
                Int32? ClientID = SessionManager.ClientID;
                lst = BLL.Part.GetPartDetail(partNo, ClientID, GetFormValue_Int("CompanyNo"));
                JsonObject jsn = new JsonObject();
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("LastPricePaidByCust", Functions.FormatCurrency(lst[i].LastPricePaidByCust, lst[i].PaidByCustCurrencyCode), 2);
                        jsnRow.AddVariable("LastAverageReboundPriceSold", Functions.FormatCurrency(lst[i].LastAverageReboundPriceSold, SessionManager.ClientCurrencyCode, 2));
                        jsnRow.AddVariable("LastSoldOn", (string.IsNullOrEmpty(Functions.FormatDate(lst[i].LastSoldOn))) ? "N/A" : Functions.FormatDate(lst[i].LastSoldOn));
                        jsnRow.AddVariable("LastSoldtoCustomer", (string.IsNullOrEmpty(lst[i].LastSoldtoCustomer)) ? "N/A" : lst[i].LastSoldtoCustomer);
                        jsnRow.AddVariable("LastQuantity", lst[i].LastQuantity);
                        jsnRow.AddVariable("LastSupplierType", (string.IsNullOrEmpty(lst[i].LastSupplierType)) ? "N/A" : lst[i].LastSupplierType);
                        jsnRow.AddVariable("LastDatecode", (string.IsNullOrEmpty(lst[i].LastDatecode)) ? "N/A" : lst[i].LastDatecode);
                        jsnRow.AddVariable("LastDatePurchased", (string.IsNullOrEmpty(Functions.FormatDate(lst[i].LastDatePurchased))) ? "N/A" : Functions.FormatDate(lst[i].LastDatePurchased));
                        jsnRow.AddVariable("LastCustomerRegion", (string.IsNullOrEmpty(lst[i].LastCustomerRegion)) ? "N/A" : lst[i].LastCustomerRegion);
                        jsnRow.AddVariable("CustLastPricePaidByCust", Functions.FormatCurrency(lst[i].CustLastPricePaidByCust, lst[i].CustPaidByCustCurrencyCode), 2);
                        jsnRow.AddVariable("CustLastAvgReboundPriceSold", Functions.FormatCurrency(lst[i].CustLastAvgReboundPriceSold, SessionManager.ClientCurrencyCode, 2));
                        jsnRow.AddVariable("CustLastSoldOn", (string.IsNullOrEmpty( Functions.FormatDate(lst[i].CustLastSoldOn)))?"N/A" : Functions.FormatDate(lst[i].CustLastSoldOn));
                        jsnRow.AddVariable("CustLastSoldtoCustomer", (string.IsNullOrEmpty(lst[i].CustLastSoldtoCustomer)) ? "N/A" : lst[i].CustLastSoldtoCustomer);
                        jsnRow.AddVariable("CustQuantity", lst[i].CustQuantity);
                        jsnRow.AddVariable("CustSupplierType", (string.IsNullOrEmpty(lst[i].CustSupplierType)) ? "N/A" : lst[i].CustSupplierType);
                        jsnRow.AddVariable("CustDatecode", (string.IsNullOrEmpty(lst[i].CustDatecode)) ? "N/A" : lst[i].CustDatecode);
                        jsnRow.AddVariable("CustDatePurchased", (string.IsNullOrEmpty(Functions.FormatDate(lst[i].CustDatePurchased))) ? "N/A" : Functions.FormatDate(lst[i].CustDatePurchased));
                        jsnRow.AddVariable("CustomerRegion", (string.IsNullOrEmpty(lst[i].CustomerRegion)) ? "N/A" : lst[i].CustomerRegion);
                        jsnRow.AddVariable("BestLastPricePaid12", Functions.FormatCurrency(lst[i].BestLastPricePaid12, SessionManager.ClientCurrencyCode, 2));
                        jsnRow.AddVariable("CleintBestPricePaid12", Functions.FormatCurrency(lst[i].CleintBestPricePaid12, SessionManager.ClientCurrencyCode, 2));

                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                jsn.AddVariable("LastPriceCustDetails", jsnRows);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
       
            }
            catch (Exception e)
            {
                //[005]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("CusReqAdd.ashx: GetPartDetail");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at GetPartDetail() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }


            //List<BLL.Part> lst = null;
            //try
            //{
            //    string partNo = GetFormValue_StringForPartSearch("partNo");
            //    Int32? ClientID = SessionManager.ClientID;
            //    DataSet ds = BLL.Part.GetPartDetail(partNo, ClientID, GetFormValue_Int("CompanyNo"));
            //    JsonObject jsn = new JsonObject();

            //    if (ds != null && ds.Tables[0] != null && ds.Tables[0].Rows.Count > 0)
            //    {

            //        jsn.AddVariable("LastPricePaidByCust", Functions.FormatCurrency(ds.Tables[0].Rows[0]["LastPricePaidByCust"], Convert.ToString(ds.Tables[0].Rows[0]["PaidByCustCurrencyCode"]), 2));
            //        jsn.AddVariable("LastAverageReboundPriceSold", Functions.FormatCurrency(ds.Tables[0].Rows[0]["LastAverageReboundPriceSold"], SessionManager.ClientCurrencyCode, 2));
            //        if (Convert.ToDouble(ds.Tables[0].Rows[0]["CustLastAvgReboundPriceSold"]) > 0)
            //            jsn.AddVariable("LastSoldOn", Functions.FormatDate(Convert.ToDateTime(ds.Tables[0].Rows[0]["LastSoldOn"])));
            //        else
            //            jsn.AddVariable("LastSoldOn", "");
            //        jsn.AddVariable("LastSoldtoCustomer", ds.Tables[0].Rows[0]["LastSoldtoCustomer"]);

            //        jsn.AddVariable("CustLastPricePaidByCust", Functions.FormatCurrency(ds.Tables[0].Rows[0]["CustLastPricePaidByCust"], Convert.ToString(ds.Tables[0].Rows[0]["CustPaidByCustCurrencyCode"]), 2));
            //        jsn.AddVariable("CustLastAvgPriceSold", Functions.FormatCurrency(ds.Tables[0].Rows[0]["CustLastAvgReboundPriceSold"], SessionManager.ClientCurrencyCode, 2));
            //        if (Convert.ToDouble(ds.Tables[0].Rows[0]["CustLastAvgReboundPriceSold"]) > 0)
            //            jsn.AddVariable("CustLastSoldOn", Functions.FormatDate(Convert.ToDateTime(ds.Tables[0].Rows[0]["CustLastSoldOn"])));
            //        else
            //            jsn.AddVariable("CustLastSoldOn", "");
            //        jsn.AddVariable("CustLastSoldtoCustomer", ds.Tables[0].Rows[0]["CustLastSoldtoCustomer"]);
            //    }
            //    else
            //    {
            //        jsn.AddVariable("LastPricePaidByCust", "0");
            //        jsn.AddVariable("LastAverageReboundPriceSold", "0");
            //        jsn.AddVariable("LastSoldOn", "");
            //        jsn.AddVariable("LastSoldtoCustomer", "");

            //        jsn.AddVariable("CustLastPricePaidByCust", "0");
            //        jsn.AddVariable("CustLastAvgPriceSold", "0");
            //        jsn.AddVariable("CustLastSoldOn", "");
            //        jsn.AddVariable("CustLastSoldtoCustomer", "");
            //    }
            //    ////set stock detail
            //    //if (ds != null && ds.Tables[1] != null && ds.Tables[1].Rows.Count > 0)
            //    //{
            //    //    jsn.AddVariable("QuantityInStock", ds.Tables[1].Rows[0]["QuantityInStock"]);
            //    //    jsn.AddVariable("QuantityOnOrder", ds.Tables[1].Rows[0]["QuantityOnOrder"]);
            //    //    jsn.AddVariable("QuantityAllocated", ds.Tables[1].Rows[0]["QuantityAllocated"]);
            //    //    jsn.AddVariable("QuantityAvailable", ds.Tables[1].Rows[0]["QuantityAvailable"]);
            //    //}
            //    //else
            //    //{
            //    //    jsn.AddVariable("QuantityInStock", "0");
            //    //    jsn.AddVariable("QuantityOnOrder", "0");
            //    //    jsn.AddVariable("QuantityAllocated", "0");
            //    //    jsn.AddVariable("QuantityAvailable", "0");
            //    //}

            //    OutputResult(jsn);
            //    jsn.Dispose(); jsn = null;
            //}
            //catch (Exception e)
            //{
            //    WriteError(e);
            //}
            //finally
            //{
            //    //lst = null;
            //}
        }
        //[002] end
        private void GetDataGridPopupOld()
        {
            List<BLL.Part> lst = null;
            try
            {
                lst = BLL.Part.CustReqPartsGRID(SessionManager.ClientID, GetFormValue_StringForPartSearch("searchType"));
                JsonObject jsn = new JsonObject();

                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();


                        jsnRow.AddVariable("ID", lst[i].PartName);
                        jsnRow.AddVariable("Name", lst[i].PartNameWithManufacture);

                        jsnRow.AddVariable("Ids", lst[i].ManufacturerNo + "|" + lst[i].ProductNo + "|" + lst[i].PackageNo);
                        jsnRow.AddVariable("Manufacturer", lst[i].ManufacturerName);
                        jsnRow.AddVariable("Product", lst[i].Productname);
                        jsnRow.AddVariable("Package", lst[i].Packagename);
                        jsnRow.AddVariable("DateCode", lst[i].DateCode);
                        jsnRow.AddVariable("DateCodeOriginal", lst[i].DateCodeOriginal);
                        jsnRow.AddVariable("ManufacturerNo", lst[i].ManufacturerNo);
                        jsnRow.AddVariable("ProductNo", lst[i].ProductNo);
                        jsnRow.AddVariable("PackageNo", lst[i].PackageNo);
                        jsnRow.AddVariable("ROHSNo", lst[i].ROHSNo);
                        jsnRow.AddVariable("ProductDescription", lst[i].ProductDescription);
                        jsnRow.AddVariable("PrdInactive", Convert.ToBoolean(lst[i].ProductInactive));
                        //[002] start
                        jsnRow.AddVariable("StockId", lst[i].StockId);
                        jsnRow.AddVariable("ResultType", lst[i].ResultType);
                        //[002] end

                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                jsn.AddVariable("PartDetails", jsnRows);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        private void GetIHSPartDetails()
        {
           
            List<BLL.Part> lst = null;
            try
            {
                lst = BLL.Part.CustReqIHSPartsDetails(
                      SessionManager.ClientID, GetFormValue_NullableInt("IHSPartsId")
                     , GetFormValue_String("MSLName")
                     , GetFormValue_NullableInt("MSLNo")
                     , GetFormValue_String("Manufacturer")
                     , GetFormValue_String("IHSProdDesc")
                     , GetFormValue_String("Packaging")
                     , GetFormValue_String("HTSCode")
                     , GetFormValue_String("IHSDutyCode")
                     , GetFormValue_String("CountryOfOrigin")
                     , GetFormValue_NullableInt("CountryOfOriginNo")
                     , GetFormValue_String("PackagingSize")
                    );
                      
                JsonObject jsn = new JsonObject();
               
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ID", lst[i].PartName);
                        jsnRow.AddVariable("Name", Functions.ReplaceLineBreaks(lst[i].PartNameWithManufacture));
                        jsnRow.AddVariable("Ids", lst[i].ManufacturerNo + "|" + lst[i].ProductNo + "|" + lst[i].PackageNo);
                        jsnRow.AddVariable("ManufacturerNo", lst[i].ManufacturerNo);
                        jsnRow.AddVariable("Manufacturer", lst[i].ManufacturerName);

                        jsnRow.AddVariable("Descriptions", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                        if (string.IsNullOrEmpty(lst[i].Descriptions))
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                        else if (lst[i].Descriptions.Length <= 10)
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                        else
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions.Substring(0, 10)));

                        jsnRow.AddVariable("ROHSNo", lst[i].ROHSNo);
                        jsnRow.AddVariable("ROHSName", lst[i].ROHSName);
                        jsnRow.AddVariable("CountryOfOrigin", lst[i].CountryOfOrigin);
                        jsnRow.AddVariable("CountryOfOriginNo", lst[i].CountryOfOriginNo);
                        jsnRow.AddVariable("PartStatus", Functions.ReplaceLineBreaks(lst[i].PartStatus));
                        jsnRow.AddVariable("HTSCode", Functions.ReplaceLineBreaks(lst[i].HTSCode));
                        jsnRow.AddVariable("AveragePrice", lst[i].AveragePrice);
                        jsnRow.AddVariable("Packaging", lst[i].Packaging);
                        jsnRow.AddVariable("PackagingSize", lst[i].PackagingSize);
                        jsnRow.AddVariable("IHSPartsId", lst[i].IHSPartsId);
                        jsnRow.AddVariable("ResultType", lst[i].ResultType);
                        jsnRow.AddVariable("ihsCurrencyCode", lst[i].IHSCurrencyCode);
                        jsnRow.AddVariable("ProdDesc", Functions.ReplaceLineBreaks(lst[i].ProductDescription));
                        jsnRow.AddVariable("ProdNo", lst[i].ProductNo);
                        jsnRow.AddVariable("IHSProdDesc", Functions.ReplaceLineBreaks(lst[i].IHSProductDescription));
                        jsnRow.AddVariable("IHSDutyCode", lst[i].IHSDutyCode);
                        //jsnRow.AddVariable("RowCnt", lst[i].RowCnt);
                        jsnRow.AddVariable("ManufacturerFullName", lst[i].ManufacturerFullName);
                        jsnRow.AddVariable("IHSProduct", lst[i].IHSProduct);
                        jsnRow.AddVariable("PackageId", lst[i].PackageId);
                        if (lst[i].PackageId > 0)
                        {
                            Functions.ClearFormCacheByParam("17", "Package", 0);
                        }
                        jsnRow.AddVariable("PackageDescription", Functions.ReplaceLineBreaks(lst[i].PackageDescription));
                        jsnRow.AddVariable("ECCNCode", lst[i].ECCNCode);

                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
               
                jsn.AddVariable("Result", jsnRows);
                 OutputResult(jsn);
                jsnRows.Dispose();
                jsnRows = null;
                jsn.Dispose();
                jsn = null;
                lst = null;
               
            }
            catch (Exception e)
            {
                //[005]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("CusReqAdd.ashx: GetIHSPartDetail");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at GetIHSPartDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        private void SaveIHSPartDetail()
        {
            
            List<BLL.Part> lst = null;
            string IHSValueResult = string.Empty;
            try
            {
                IHSValueResult = GetFormValue_String("IHSResult");
                lst = BLL.Part.InsertIHSApiXMLOrSelect(
                     SessionManager.ClientID,
                         SessionManager.LoginID,
                         IHSValueResult
                    );

                JsonObject jsn = new JsonObject();

                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ID", lst[i].PartName);
                        jsnRow.AddVariable("Name", Functions.ReplaceLineBreaks(lst[i].PartNameWithManufacture));
                        jsnRow.AddVariable("Ids", lst[i].ManufacturerNo + "|" + lst[i].ProductNo + "|" + lst[i].PackageNo);
                        jsnRow.AddVariable("ManufacturerNo", lst[i].ManufacturerNo);
                        jsnRow.AddVariable("Manufacturer", lst[i].ManufacturerName);

                        jsnRow.AddVariable("Descriptions", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                        if (string.IsNullOrEmpty(lst[i].Descriptions))
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                        else if (lst[i].Descriptions.Length <= 10)
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                        else
                            jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions.Substring(0, 10)));

                        jsnRow.AddVariable("ROHSNo", lst[i].ROHSNo);
                        jsnRow.AddVariable("ROHSName", lst[i].ROHSName);
                        jsnRow.AddVariable("CountryOfOrigin", lst[i].CountryOfOrigin);
                        jsnRow.AddVariable("CountryOfOriginNo", lst[i].CountryOfOriginNo);
                        jsnRow.AddVariable("PartStatus", Functions.ReplaceLineBreaks(lst[i].PartStatus));
                        jsnRow.AddVariable("HTSCode", Functions.ReplaceLineBreaks(lst[i].HTSCode));
                        jsnRow.AddVariable("AveragePrice", lst[i].AveragePrice);
                        jsnRow.AddVariable("Packaging", lst[i].Packaging);
                        jsnRow.AddVariable("PackagingSize", lst[i].PackagingSize);
                        jsnRow.AddVariable("IHSPartsId", lst[i].IHSPartsId);
                        jsnRow.AddVariable("ResultType", lst[i].ResultType);
                        jsnRow.AddVariable("ihsCurrencyCode", lst[i].IHSCurrencyCode);
                        jsnRow.AddVariable("ProdDesc", Functions.ReplaceLineBreaks(lst[i].ProductDescription));
                        jsnRow.AddVariable("ProdNo", lst[i].ProductNo);
                        jsnRow.AddVariable("IHSProdDesc", Functions.ReplaceLineBreaks(lst[i].IHSProductDescription));
                        jsnRow.AddVariable("IHSDutyCode", lst[i].IHSDutyCode);
                        //jsnRow.AddVariable("RowCnt", lst[i].RowCnt);
                        jsnRow.AddVariable("ManufacturerFullName", lst[i].ManufacturerFullName);
                        jsnRow.AddVariable("IHSProduct", lst[i].IHSProduct);
                        jsnRow.AddVariable("PackageId", lst[i].PackageId);
                        if (lst[i].PackageId > 0)
                        {
                            Functions.ClearFormCacheByParam("17", "Package", 0);
                        }
                        jsnRow.AddVariable("ECCNCode", Functions.ReplaceLineBreaks(lst[i].ECCNCode));
                        jsnRow.AddVariable("PackageDescription", Functions.ReplaceLineBreaks(lst[i].PackageDescription));
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }

                jsn.AddVariable("Result", jsnRows);
                OutputResult(jsn);
                jsnRows.Dispose();
                jsnRows = null;
                jsn.Dispose();
                jsn = null;
                lst = null;

            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at SaveIHSPartDetail() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        
        /// <summary>
        /// Get Shipping method cost and charge
        /// </summary>
        private void GetRestrictedManufacturer()
        {
            BLL.Printer rm = null;
            try
            {
                int mfrID = GetFormValue_Int("RsManufacturerNo");
                rm = BLL.Printer.GetRestrictedManufacture(SessionManager.ClientID, mfrID);
                JsonObject jsn = new JsonObject();
                if (rm == null)
                {
                    jsn.AddVariable("isRestrictedManufacturer", 0);
                    jsn.AddVariable("RestrictedMFRMessage", "");
                }
                else
                {
                    
                    jsn.AddVariable("isRestrictedManufacturer", rm.isRestrictedManufacturer);
                    jsn.AddVariable("RestrictedMFRMessage", rm.RestrictedMFRMessage);
                    
                }
                string mfrNotes = Manufacturer.GetAdvisoryNotes(mfrID, (int)SessionManager.ClientID);
                jsn.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
                rm = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        //
        private void GetIHSPartEccnDetail()
        {
            int intloginid = SessionManager.LoginID ?? 0;
            List<BLL.Part> lst = null;
            try
            {
                lst = BLL.Part.CustReqIHSPartEccnDetails(
                      SessionManager.ClientID, 
                       GetFormValue_String("PartEccnCode"),
                       intloginid,
                       GetFormValue_String("PartNo")
             
                    );

                JsonObject jsn = new JsonObject();

                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("PartEccnMappedId", lst[i].PartEccnMappedId);
                        jsnRow.AddVariable("ECCNNo", lst[i].ECCNNo);
                        jsnRow.AddVariable("ECCNCode", Functions.ReplaceLineBreaks(lst[i].ECCNCode));
                        jsnRow.AddVariable("ECCNStatus", lst[i].ECCNStatus);
                        jsnRow.AddVariable("ECCNWarning", Functions.ReplaceLineBreaks(lst[i].ECCNWarning));
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                if (lst.Count == 0)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("PartEccnMappedId", 0);
                    jsnRow.AddVariable("ECCNNo", "");
                    jsnRow.AddVariable("ECCNCode", "");
                    jsnRows.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }

                jsn.AddVariable("Result", jsnRows);
                OutputResult(jsn);
                jsnRows.Dispose();
                jsnRows = null;
                jsn.Dispose();
                jsn = null;
                lst = null;

            }
            catch (Exception e)
            {
                //[005]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("CusReqAdd.ashx: GetIHSPartEccnDetail");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at GetIHSPartEccnDetail() in CusReqAdd.ashx.cs : " + e.Message);

                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        //
        private void GetIHSEccnCodeDetail()
        {
            int intloginid = SessionManager.LoginID ?? 0;
            List<BLL.Part> lst = null;
            try
            {
                lst = BLL.Part.CustReqIHSEccnCode(
                      SessionManager.ClientID,
                       GetFormValue_String("ECCNCode"),
                       intloginid
                       

                    );

                JsonObject jsn = new JsonObject();

                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ECCNNo", lst[i].ECCNNo);
                        jsnRow.AddVariable("ECCNCode", Functions.ReplaceLineBreaks(lst[i].ECCNCode));
                        jsnRow.AddVariable("ECCNStatus", lst[i].ECCNStatus);
                        jsnRow.AddVariable("ECCNWarning", Functions.ReplaceLineBreaks(lst[i].ECCNWarning));
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                if (lst.Count == 0)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ECCNNo", 0);
                    jsnRow.AddVariable("ECCNCode", "");
                    jsnRows.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }

                jsn.AddVariable("Result", jsnRows);
                OutputResult(jsn);
                jsnRows.Dispose();
                jsnRows = null;
                jsn.Dispose();
                jsn = null;
                lst = null;

            }
            catch (Exception e)
            {
                //[005]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("CusReqAdd.ashx: GetIHSPartEccnCodeDetail");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at GetIHSEccnCodeDetail() in CusReqAdd.ashx.cs : " + e.Message);

                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        private void NotifyECCNREQ(int? ID, int? LoginID)
        {
            int ? intMailGroupNo = null;
            CustomerRequirement creq = CustomerRequirement.Get(ID);
            if (creq != null)
            {
                intMailGroupNo = BLL.MailGroup.GetECCNRestrictedUsenotifcationMailGroupNo(SessionManager.ClientID);
                Rebound.GlobalTrader.Site.WebServices service = new WebServices();
                if (creq.SupportTeamMemberNo > 0)
                {
                    service.SendECCNMessageEditReq(creq.CustomerRequirementId, "", Convert.ToString(intMailGroupNo), "", null, null, LoginID);
                }
                else
                {
                    service.SendECCNMessageEditReq(creq.CustomerRequirementId, "", Convert.ToString(intMailGroupNo), "", null, null, LoginID);

                }
                service = null;
            }
        }


        #region KUB for Add new requirement page
        private void GetKubTop3BuyPriceDetails()
        {
            List<BLL.KubTop3BuyPrice> lst = null;
            try
            {
                string partNo = GetFormValue_StringForPartSearch("partNo");
                lst = BLL.KubTop3BuyPrice.GetKubTop3BuyPriceDetails(partNo, Convert.ToInt32(SessionManager.ClientID));
                JsonObject jsn = new JsonObject();
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("POID", (string.IsNullOrEmpty(lst[i].POID)) ? "N/A" : lst[i].POID);
                        jsnRow.AddVariable("PONo", (string.IsNullOrEmpty(lst[i].PONo)) ? "N/A" : lst[i].PONo);
                        jsnRow.AddVariable("Date", (string.IsNullOrEmpty(lst[i].Date)) ? "N/A" : lst[i].Date);
                        jsnRow.AddVariable("Price", (string.IsNullOrEmpty(lst[i].Price)) ? "N/A" : lst[i].Price);
                        jsnRow.AddVariable("IsClientPrice", lst[i].IsClientPrice);
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                    }
                }
                jsn.AddVariable("fetchKubPODetails", jsnRows);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;

            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubPODetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        private void GetKubCountryWiseSaleDetails()
        {
            List<BLL.KubCountryWiseSale> lst = null;
            try
            {
                string partNo = GetFormValue_StringForPartSearch("partNo");
                lst = BLL.KubCountryWiseSale.GetKubCountryWiseSaleDetails(partNo, Convert.ToInt32(SessionManager.ClientID));
                JsonObject jsn = new JsonObject();
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("Countries", (string.IsNullOrEmpty(lst[i].Countries)) ? "N/A" : lst[i].Countries);
                        jsnRow.AddVariable("NoOfSales", (string.IsNullOrEmpty(lst[i].NoOfSales)) ? "N/A" : lst[i].NoOfSales);
                        jsnRow.AddVariable("ReSale", (string.IsNullOrEmpty(lst[i].ReSale)) ? "N/A" : lst[i].ReSale);
                        jsnRow.AddVariable("UnShippedReSale", (string.IsNullOrEmpty(lst[i].UnShippedReSale)) ? "N/A" : lst[i].UnShippedReSale);
                        jsnRow.AddVariable("UnShippedNoOfSales", (string.IsNullOrEmpty(lst[i].UnShippedNoOfSales)) ? "N/A" : lst[i].UnShippedNoOfSales);
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                    }
                }
                jsn.AddVariable("fetchKubCountriesDetails", jsnRows);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;

            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubCountriesDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        private void GetKubAssistanceDetails()
        {
            BLL.KubAssistance obj = null;
            try
            {
                string partNo = GetFormValue_StringForPartSearch("partNo");
                obj = BLL.KubAssistance.GetKubAssistanceDetails(partNo, Convert.ToInt32(SessionManager.ClientID));
                JsonObject jsnRow = new JsonObject();
                jsnRow.AddVariable("PartNo", (string.IsNullOrEmpty(obj.PartNo)) ? "N/A" : obj.PartNo);
                jsnRow.AddVariable("TotalPartsTreaded", (string.IsNullOrEmpty(obj.TotalPartsTreaded)) ? "N/A" : obj.TotalPartsTreaded);
                jsnRow.AddVariable("TotalGP", (string.IsNullOrEmpty(obj.TotalGP)) ? "N/A" : obj.TotalGP);
                jsnRow.AddVariable("AverageBestPrice", (string.IsNullOrEmpty(obj.AverageBestPrice)) ? "N/A" : obj.AverageBestPrice);
                jsnRow.AddVariable("LastUpdatedDate", obj.LastUpdatedDate);
                jsnRow.AddVariable("IsClientPrice", obj.IsClientPrice);


                OutputResult(jsnRow);
                jsnRow.Dispose();
                jsnRow = null;
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                obj = null;
            }

        }
        public void StartKubCache()
        {
            BLL.KubAssistance obj = null;
            try
            {
                string partNo = GetFormValue_StringForPartSearch("partNo");
                obj = BLL.KubAssistance.StartKubCache(partNo, Convert.ToInt32(SessionManager.ClientID));
                JsonObject jsnRow = new JsonObject();
                jsnRow.AddVariable("Result",1);
                
                OutputResult(jsnRow);
                jsnRow.Dispose();
                jsnRow = null;
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                obj = null;
            }
        }
        public void ShowReadMoreData()
        {
            BLL.KubAssistance obj = null;
            try
            {
                string partNo = GetFormValue_StringForPartSearch("partNo");
                obj = BLL.KubAssistance.ShowReadMoreData(partNo, Convert.ToInt32(SessionManager.ClientID));
                JsonObject jsnRow = new JsonObject();
                jsnRow.AddVariable("TotalGPbasedLastActualBuyPrice", (string.IsNullOrEmpty(obj.TotalGPbasedLastActualBuyPrice)) ? "N/A" : obj.TotalGPbasedLastActualBuyPrice);
                jsnRow.AddVariable("LowestSalesPriceLast12Months", (string.IsNullOrEmpty(obj.LowestSalesPriceLast12Months)) ? "N/A" : obj.LowestSalesPriceLast12Months);
                jsnRow.AddVariable("NumberOfPartsSoldLast12months", (string.IsNullOrEmpty(obj.NumberOfPartsSoldLast12months)) ? "N/A" : obj.NumberOfPartsSoldLast12months);
                jsnRow.AddVariable("LastEnquiredDateOfPart", (string.IsNullOrEmpty(obj.LastEnquiredDateOfPart)) ? "N/A" : obj.LastEnquiredDateOfPart);
                jsnRow.AddVariable("LastQuotedPrice", (string.IsNullOrEmpty(obj.LastQuotedPrice)) ? "N/A" : obj.LastQuotedPrice);
                jsnRow.AddVariable("LastSoldPrice", (string.IsNullOrEmpty(obj.LastSoldPrice)) ? "N/A" : obj.LastSoldPrice);
                jsnRow.AddVariable("LastHubprice", (string.IsNullOrEmpty(obj.LastHubprice)) ? "N/A" : obj.LastHubprice);
                OutputResult(jsnRow);
                jsnRow.Dispose();
                jsnRow = null;
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                obj = null;
            }
        }

        public void IsAllowedEnabled(HttpContext context)
        {
            string partNo = string.IsNullOrEmpty(context.Request.QueryString["PartNumber"]) ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["PartNumber"].ToString());
            string CustomerReqId = string.IsNullOrEmpty(context.Request.QueryString["CustomerReqId"]) ? string.Empty : context.Request.QueryString["CustomerReqId"];
            List<BLL.KubLast10RecentQuote> lst = null;
            try
            {
                lst = BLL.KubLast10RecentQuote.IsAllowedEnabled(partNo, CustomerReqId, Convert.ToInt32(SessionManager.ClientID));
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(lst);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error in StartKubCacheForBrowsePage()  in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        public void GetGPCalculationDetails()
        {
            List<BLL.KubCountryWiseSale> lst = null;
            try
            {
                string partNo = GetFormValue_StringForPartSearch("partNo");
                lst = BLL.KubCountryWiseSale.GetGPCalculationDetails(partNo, Convert.ToInt32(SessionManager.ClientID));
                JsonObject jsn = new JsonObject();
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("InvoiceId", (string.IsNullOrEmpty(lst[i].InvoiceId)) ? "N/A" : lst[i].InvoiceId);
                        jsnRow.AddVariable("InvoiceNumber", (string.IsNullOrEmpty(lst[i].InvoiceNumber)) ? "N/A" : lst[i].InvoiceNumber);
                        jsnRow.AddVariable("Quantity", (string.IsNullOrEmpty(lst[i].Quantity)) ? "N/A" : lst[i].Quantity);
                        jsnRow.AddVariable("ShippingCost", (string.IsNullOrEmpty(lst[i].ShippingCost)) ? "N/A" : lst[i].ShippingCost);
                        jsnRow.AddVariable("Freight", (string.IsNullOrEmpty(lst[i].Freight)) ? "N/A" : lst[i].Freight);
                        jsnRow.AddVariable("LandedCost", (string.IsNullOrEmpty(lst[i].LandedCost)) ? "N/A" : lst[i].LandedCost);
                        jsnRow.AddVariable("InvoiceValue", (string.IsNullOrEmpty(lst[i].InvoiceValue)) ? "N/A" : lst[i].InvoiceValue);
                        jsnRow.AddVariable("TOTAL", (string.IsNullOrEmpty(lst[i].TOTAL)) ? "N/A" : lst[i].TOTAL);
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                    }
                }
                jsn.AddVariable("fetchKubCountriesDetails", jsnRows);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;

            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubCountriesDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }

        public void GetGPLastSaleCalculationDetails()
        {
            List<BLL.KubCountryWiseSale> lst = null;
            try
            {
                string partNo = GetFormValue_StringForPartSearch("partNo");
                lst = BLL.KubCountryWiseSale.GetGPLastSaleCalculationDetails(partNo, Convert.ToInt32(SessionManager.ClientID));
                JsonObject jsn = new JsonObject();
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("InvoiceId", (string.IsNullOrEmpty(lst[i].InvoiceId)) ? "N/A" : lst[i].InvoiceId);
                        jsnRow.AddVariable("InvoiceNumber", (string.IsNullOrEmpty(lst[i].InvoiceNumber)) ? "N/A" : lst[i].InvoiceNumber);
                        jsnRow.AddVariable("Quantity", (string.IsNullOrEmpty(lst[i].Quantity)) ? "N/A" : lst[i].Quantity);
                        jsnRow.AddVariable("ShippingCost", (string.IsNullOrEmpty(lst[i].ShippingCost)) ? "N/A" : lst[i].ShippingCost);
                        jsnRow.AddVariable("Freight", (string.IsNullOrEmpty(lst[i].Freight)) ? "N/A" : lst[i].Freight);
                        jsnRow.AddVariable("LandedCost", (string.IsNullOrEmpty(lst[i].LandedCost)) ? "N/A" : lst[i].LandedCost);
                        jsnRow.AddVariable("InvoiceValue", (string.IsNullOrEmpty(lst[i].InvoiceValue)) ? "N/A" : lst[i].InvoiceValue);
                        jsnRow.AddVariable("TOTAL", (string.IsNullOrEmpty(lst[i].TOTAL)) ? "N/A" : lst[i].TOTAL);
                        jsnRows.AddVariable(jsnRow);
                        jsnRow.Dispose();
                    }
                }
                jsn.AddVariable("fetchKubCountriesDetails", jsnRows);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;

            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubCountriesDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        #endregion

        #region Kub For requirement browse page
        private void GetKubMainProductGroupDetails(HttpContext context)
        {
            List<BLL.KubMainProductGroup> lst = null;
            try
            {
                string partNo = string.IsNullOrEmpty(context.Request.QueryString["PartNumber"]) ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["PartNumber"]);
                lst = BLL.KubMainProductGroup.FetchKubMainProductGroupDetails(partNo, Convert.ToInt32(SessionManager.ClientID));
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(lst);
                _context.Response.Write(json);

            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubMainProductGroupDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        private void GetKubTotalLineInvoiceDetails(HttpContext context)
        {
            string partNo = string.IsNullOrEmpty(context.Request.QueryString["PartNumber"]) ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["PartNumber"]);
            string CustomerReqId = string.IsNullOrEmpty(context.Request.QueryString["CustomerReqId"]) ? string.Empty : context.Request.QueryString["CustomerReqId"];
            BLL.KubTotalLineInvoice obj = null;
            try
            {
                obj = BLL.KubTotalLineInvoice.GetKubTotalLineInvoiceDetails(partNo, Convert.ToInt32(SessionManager.ClientID),Convert.ToInt32(CustomerReqId));
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(obj);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubTotalLineInvoiceDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                obj = null;
            }
        }
        private void GetKubAvgPriceDetails(HttpContext context)
        {
            BLL.KubAvgPrice obj = null;
            try
            {
                string partNo = string.IsNullOrEmpty(context.Request.QueryString["PartNumber"]) ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["PartNumber"]);
                obj = BLL.KubAvgPrice.GetKubAvgPriceDetails(partNo, Convert.ToInt32(SessionManager.ClientID));
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(obj);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubPriceDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                obj = null;
            }
        }
        private void GetKubLast10RecentQuoteDetails(HttpContext context)
        {
            string partNo = string.IsNullOrEmpty(context.Request.QueryString["PartNumber"]) ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["PartNumber"]);
            string CustomerReqId = string.IsNullOrEmpty(context.Request.QueryString["CustomerReqId"]) ? string.Empty : context.Request.QueryString["CustomerReqId"];
            List<BLL.KubLast10RecentQuote> lst = null;
            try
            {
                lst = BLL.KubLast10RecentQuote.GetKubLast10RecentQuoteDetails(partNo, CustomerReqId, Convert.ToInt32(SessionManager.ClientID));
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(lst);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error at GetKubLast10RecentQuoteDetails() in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        public void StartKubCacheForBrowsePage(HttpContext context)
        {
            string partNo = string.IsNullOrEmpty(context.Request.QueryString["PartNumber"]) ? string.Empty : RemovePunctuationRetainingPercentSigns(context.Request.QueryString["PartNumber"]);
            string CustomerReqId = string.IsNullOrEmpty(context.Request.QueryString["CustomerReqId"]) ? string.Empty : context.Request.QueryString["CustomerReqId"];
            List<BLL.KubLast10RecentQuote> lst = null;
            try
            {
                lst = BLL.KubLast10RecentQuote.StartKubCacheForBrowsePage(partNo, CustomerReqId, Convert.ToInt32(SessionManager.ClientID));
                string json = Newtonsoft.Json.JsonConvert.SerializeObject(lst);
                _context.Response.Write(json);
            }
            catch (Exception e)
            {
                new Errorlog().LogMessage("Error in StartKubCacheForBrowsePage()  in CusReqAdd.ashx.cs : " + e.Message);
                WriteError(e);
            }
            finally
            {
                lst = null;
            }
        }
        #endregion


        public static string RemovePunctuation(string strIn)
        {
            return Regex.Replace(strIn.Trim(), @"\W*", "");
        }
        public static string RemovePunctuationRetainingPercentSigns(string strIn)
        {
            strIn = strIn.Trim();
            string strOut = RemovePunctuation(strIn);
            if (strIn.StartsWith("%")) strOut = string.Format("%{0}", strOut);
            if (strIn.EndsWith("%")) strOut = string.Format("{0}%", strOut);
            return strOut;
        }
    }
}
