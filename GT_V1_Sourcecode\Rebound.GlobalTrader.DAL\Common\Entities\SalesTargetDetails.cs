﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
	
	public class SalesTargetDetails {
		
		#region Constructors
		
		public SalesTargetDetails() { }

		#endregion

		#region Properties

		public System.Int32 LoginId { get; set; }
		public string EmployeeName { get; set; }
		public string PersonType { get; set; }
		public System.Double? JanTarget { get; set; }
		public System.Double? FebTarget { get; set; }
		public System.Double? MarchTarget { get; set; }
		public System.Double? AprTarget { get; set; }
		public System.Double? MayTarget { get; set; }
		public System.Double? JuneTarget { get; set; }
		public System.Double? JulyTarget { get; set; }
		public System.Double? AugTarget { get; set; }
		public System.Double? SepTarget { get; set; }
		public System.Double? OctTarget { get; set; }
		public System.Double? NovTarget { get; set; }
		public System.Double? DecTarget { get; set; }
		public System.Double? AllocatedPer { get; set; }
		public System.Double? TotalTarget { get; set; }
		public System.Int32 RowId { get; set; }
        public System.Int32 RecordCount { get; set; }
		public System.Int32 IsCustMoved { get; set; }
		#endregion

	}
}