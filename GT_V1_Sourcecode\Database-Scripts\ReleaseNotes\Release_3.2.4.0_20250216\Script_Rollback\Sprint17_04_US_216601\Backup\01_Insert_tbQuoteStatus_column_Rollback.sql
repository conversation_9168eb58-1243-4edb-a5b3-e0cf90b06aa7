﻿
GO
/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216601]	    Phuc Hoang			12-Dec-2024		Update			216601: Quote - New status matrix
==========================================================================================================
*/

IF EXISTS (SELECT 1 FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'New')
BEGIN
	DELETE FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'New'
END

GO

IF EXISTS (SELECT 1 FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'Partially Offered')
BEGIN
	DELETE FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'Partially Offered'
END

GO

IF EXISTS (SELECT 1 FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'Partially Accepted')
BEGIN
	DELETE FROM [dbo].[tbQuoteStatus] WHERE [Name] = 'Partially Accepted'
END

GO