Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.ApprovalRequestTo=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.ApprovalRequestTo.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.ApprovalRequestTo.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.ApprovalRequestTo.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("ApprovalRequestTo")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.AutoSearch.ApprovalRequestTo.callBaseMethod(this,"dispose")},dataReturned:function(){var t,i,n;if(this._result&&this._result.TotalRecords>0)for(t=0,i=this._result.Results.length;t<i;t++)n=this._result.Results[t],strHTML="",n.Type.toUpperCase()=="GROUP"&&(strHTML+='<div class="mailGroup">'),strHTML+=n.Name,n.Type.toUpperCase()=="GROUP"&&(strHTML+="<\/div>"),this.addResultItem(strHTML,n.Name,n.ID,n.Type),strHTML=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.ApprovalRequestTo.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.ApprovalRequestTo",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);