Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.initializeBase(this,[n]);this._searchType=null};Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.prototype={get_searchType:function(){return this._searchType},set_searchType:function(n){this._searchType!==n&&(this._searchType=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("PartSearch")},dispose:function(){this.isDisposed||(this._searchType=null,Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.callBaseMethod(this,"dispose"))},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",this._enmResultsActionType!=$R_ENUM$AutoSearchResultsActionType.Navigate&&(i=$R_FN.setCleanTextValue(n.Name)),this.addResultItem(i,$R_FN.setCleanTextValue(n.Name),n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.PartSearch",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);