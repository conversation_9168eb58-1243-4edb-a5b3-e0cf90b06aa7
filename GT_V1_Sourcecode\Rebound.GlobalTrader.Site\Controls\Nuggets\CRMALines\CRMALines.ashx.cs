
/*

Marker     changed by      date         Remarks

[001]      Abhinav       17/11/20011   ESMS Ref:25 & 34  - Virtual Stock Update & Closeing of line CRMA
[003]      Suhail          15/05/2018   Added Avoidable on CRMA Line
[004]      <PERSON> Gupta     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing

*/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CRMALines : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetLines": GetLines(); break;
                    case "GetData": GetData(); break;
                    //[001] code start
                    case "GetLines_Open": GetLines_Open(); break;
                    case "GetLines_Closed": GetLines_Closed(); break;
                    case "CloseLine": CloseLine(); break;
                    case "GetQtyLine": GetQtyCRMALines(); break;
                    //[001] code end
                    case "AddNew": AddNew(); break;
                    case "AddNewWithAllocation": AddNewWithAllocation(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "Delete": Delete(); break;
                    case "GetInvoiceLineForNew": GetInvoiceLineForNew(); break;
                    case "GetInvoiceLineData": GetInvoiceLineData(); break;
                    case "GetLineAllocations": GetLineAllocations(); break;
                    case "GetLineReceived": GetLineReceived(); break;
                    case "GetInvoiceLineAllocationCandidates": GetInvoiceLineAllocationCandidates(); break;
                    case "GetLineAllocationData": GetLineAllocationData(); break;
                    case "DeleteAllocation": DeleteAllocation(); break;
                    case "GetCategory": GetCategory(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// get all customerRMALines for specified customerRMA
        /// </summary>
        private void GetLines()
        {
            try
            {
                List<CustomerRmaLine> lst = CustomerRmaLine.GetListForCustomerRMA(ID);
                var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x => x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);
                
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                foreach (CustomerRmaLine ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", ln.CustomerRMALineId);
                    jsnItem.AddVariable("Part", ln.Part);
                    jsnItem.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                    jsnItem.AddVariable("Manufacturer", ln.ManufacturerCode);
                    string mfrNotes = !Functions.HasNumbericValue(ln.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)ln.ManufacturerNo).AdvisoryNotes;
                    jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                    //jsnItem.AddVariable("Product", ln.ProductName);
                    jsnItem.AddVariable("Product", ln.ProductDescription);
                    jsnItem.AddVariable("Package", ln.PackageName);
                    jsnItem.AddVariable("DC", ln.DateCode);
                    //[001] code start
                    jsnItem.AddVariable("QuantityShipped", Functions.FormatNumeric(ln.QuantityShipped));
                    jsnItem.AddVariable("InvoiceLineNo", ln.InvoiceLineNo);
                    //[001] code end
                    jsnItem.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
                    jsnItem.AddVariable("Received", Functions.FormatNumeric(ln.QuantityReceived));
                    jsnItem.AddVariable("Outstanding", Functions.FormatNumeric(ln.Quantity - ln.QuantityReceived));
                    jsnItem.AddVariable("Allocated", Functions.FormatNumeric(ln.QuantityAllocated));
                    jsnItem.AddVariable("ReturnDate", Functions.FormatDate(ln.ReturnDate));
                    jsnItem.AddVariable("CustomerPart", ln.CustomerPart);
                    jsnItem.AddVariable("ROHS", ln.ROHS);
                    jsnItem.AddVariable("Reason", Functions.ReplaceLineBreaks(ln.Reason));
                    jsnItem.AddVariable("InvoiceNo", ln.InvoiceNo);
                    jsnItem.AddVariable("Invoice", ln.InvoiceNumber);
                    jsnItem.AddVariable("InvoiceDate", Functions.FormatDate(ln.InvoiceDate));
                    jsnItem.AddVariable("Closed", ln.Closed);
                    jsnItem.AddVariable("IsParentCustomerRMALineNo", ln.ParentCustomerRMALineNo > 0 ? true : false);
                    jsnItem.AddVariable("AS6081", ln.AS6081);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("Lines", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }


        /// <summary>
        /// get Qty for customerRMALines for specified customerRMA
        /// </summary>
        private void GetQtyCRMALines()
        {
            try
            {
                CustomerRmaLine obj = CustomerRmaLine.GetQtyForCustomerRMA(ID, InvoiceLineNo);
                
                JsonObject jsn = new JsonObject();                                
                JsonObject jsnItem = new JsonObject();

                jsnItem.AddVariable("ID", ID);
                jsnItem.AddVariable("QuantityShipped", Functions.FormatNumeric(obj.QuantityShipped));
                jsnItem.AddVariable("QuantityCRMA", Functions.FormatNumeric(obj.QuantityCRMA));
                jsnItem.AddVariable("QuantityReceived", Functions.FormatNumeric(obj.QuantityReceived));
                jsnItem.AddVariable("InvoiceNo", obj.InvoiceLineNo);
                jsnItem.AddVariable("QuantityAvailable", obj.QuantityAvailable);
                
                jsn.AddVariable("Lines", jsnItem);

                jsnItem.Dispose(); jsnItem = null;

                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// get open CRMALines for specified CRMA
        /// /// </summary>
        private void GetLines_Open()
        {
            //JsonObject jsn = new JsonObject();
            //jsn = ProcessLineList(CustomerRmaLine.GetListOpenForCRMA(ID));
            //OutputResult(jsn);
            //jsn.Dispose(); jsn = null;
            List<CustomerRmaLine> lst = CustomerRmaLine.GetListOpenForCRMA(ID);
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            foreach (CustomerRmaLine ln in lst)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", ln.CustomerRMALineId);
                jsnItem.AddVariable("Part", ln.Part);
                jsnItem.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                jsnItem.AddVariable("Manufacturer", ln.ManufacturerCode);
                jsnItem.AddVariable("Product", ln.ProductName);
                jsnItem.AddVariable("Package", ln.PackageName);
                jsnItem.AddVariable("DC", ln.DateCode);
                jsnItem.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
                jsnItem.AddVariable("Received", Functions.FormatNumeric(ln.QuantityReceived));
                jsnItem.AddVariable("Outstanding", Functions.FormatNumeric(ln.Quantity - ln.QuantityReceived));
                jsnItem.AddVariable("Allocated", Functions.FormatNumeric(ln.QuantityAllocated));
                jsnItem.AddVariable("ReturnDate", Functions.FormatDate(ln.ReturnDate));
                jsnItem.AddVariable("CustomerPart", ln.CustomerPart);
                jsnItem.AddVariable("ROHS", ln.ROHS);
                jsnItem.AddVariable("Reason", Functions.ReplaceLineBreaks(ln.Reason));
                jsnItem.AddVariable("InvoiceNo", ln.InvoiceNo);
                jsnItem.AddVariable("Invoice", ln.InvoiceNumber);
                jsnItem.AddVariable("InvoiceDate", Functions.FormatDate(ln.InvoiceDate));
                jsnItem.AddVariable("Closed", ln.Closed);
                jsnItem.AddVariable("InvoiceLineNo", ln.InvoiceLineNo);
                //[001] code start
                jsnItem.AddVariable("QuantityShipped", Functions.FormatNumeric(ln.QuantityShipped));
                //[001] code end
                jsnItem.AddVariable("AS6081", ln.AS6081);//[RP-2339]
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            jsn.AddVariable("Lines", jsnItems);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }

        /// <summary>
        /// get closed CRMALines for specified CRMA
        /// </summary>
        private void GetLines_Closed()
        {
            JsonObject jsn = new JsonObject();
            jsn = ProcessLineList(CustomerRmaLine.GetListClosedForCRMA(ID));
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }



        private JsonObject ProcessLineList(List<CustomerRmaLine> lst)
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            foreach (CustomerRmaLine ln in lst)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", ln.CustomerRMALineId);
                jsnItem.AddVariable("Part", ln.Part);
                jsnItem.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                jsnItem.AddVariable("Manufacturer", ln.ManufacturerCode);
                jsnItem.AddVariable("Product", ln.ProductName);
                jsnItem.AddVariable("Package", ln.PackageName);
                jsnItem.AddVariable("DC", ln.DateCode);
                jsnItem.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
                jsnItem.AddVariable("Received", Functions.FormatNumeric(ln.QuantityReceived));
                jsnItem.AddVariable("Outstanding", Functions.FormatNumeric(ln.Quantity - ln.QuantityReceived));
                jsnItem.AddVariable("Allocated", Functions.FormatNumeric(ln.QuantityAllocated));
                jsnItem.AddVariable("ReturnDate", Functions.FormatDate(ln.ReturnDate));
                jsnItem.AddVariable("CustomerPart", ln.CustomerPart);
                jsnItem.AddVariable("ROHS", ln.ROHS);
                jsnItem.AddVariable("Reason", Functions.ReplaceLineBreaks(ln.Reason));
                jsnItem.AddVariable("InvoiceNo", ln.InvoiceNo);
                jsnItem.AddVariable("Invoice", ln.InvoiceNumber);
                jsnItem.AddVariable("InvoiceDate", Functions.FormatDate(ln.InvoiceDate));
                jsnItem.AddVariable("InvoiceLineNo", ln.InvoiceLineNo);
                jsnItem.AddVariable("Closed", ln.Closed);
                //[001] code start
                jsnItem.AddVariable("QuantityShipped", Functions.FormatNumeric(ln.QuantityShipped));
                //[001] code end
                jsnItem.AddVariable("AS6081", ln.AS6081); //[RP-2339]
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            jsn.AddVariable("Lines", jsnItems);
            return jsn;
        }

        /// <summary>


        /// get customerRmaLine by key
        /// </summary>
        private void GetData()
        {
            CustomerRmaLine ln = null;
            try
            {
                ln = CustomerRmaLine.Get(ID);
                if (ln == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("CRMANo", ln.CustomerRMANo);
                    jsn.AddVariable("InvoiceLineNo", ln.InvoiceLineNo);
                    jsn.AddVariable("Part", ln.Part);
                    jsn.AddVariable("ManufacturerNo", ln.ManufacturerNo);
                    jsn.AddVariable("Manufacturer", ln.ManufacturerName);
                    string mfrNotes = Manufacturer.GetAdvisoryNotes(ln.ManufacturerNo ?? 0, (int)SessionManager.ClientID);
                    jsn.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                    jsn.AddVariable("Package", ln.PackageDescription);
                    jsn.AddVariable("Quantity", Functions.FormatNumeric(ln.Quantity));
                    jsn.AddVariable("Received", Functions.FormatNumeric(ln.QuantityReceived));
                    jsn.AddVariable("ReturnDate", Functions.FormatDate(ln.ReturnDate));
                    jsn.AddVariable("Reason", Functions.ReplaceLineBreaks(ln.Reason));
                    jsn.AddVariable("ProductNo", ln.ProductNo);
                    jsn.AddVariable("ProductName", ln.ProductName);
                    jsn.AddVariable("Product", ln.ProductDescription);
                    jsn.AddVariable("PackageNo", ln.PackageNo);
                    jsn.AddVariable("PackageName", ln.PackageName);
                    jsn.AddVariable("Package", ln.PackageDescription);
                    jsn.AddVariable("CustomerPart", ln.CustomerPart);
                    jsn.AddVariable("DC", ln.DateCode);
                    jsn.AddVariable("ROHS", ln.ROHS);
                    jsn.AddVariable("LineNotes", Functions.ReplaceLineBreaks(ln.LineNotes));
                    jsn.AddVariable("StockNo", ln.StockNo);
                    jsn.AddVariable("CreditIds", this.GetJsonObject(ln.CreditIds, "CreditId"));
                    jsn.AddVariable("CreditNumbers", this.GetJsonObject(ln.CreditNumbers, "CreditNumber"));
                    jsn.AddVariable("Reason1", ln.Reason1);
                    jsn.AddVariable("Reason2", ln.Reason2);
                    jsn.AddVariable("Reason1Val", ln.Reason1Val);
                    jsn.AddVariable("Reason2Val", ln.Reason2Val);
                    jsn.AddVariable("RootCause", Functions.ReplaceLineBreaks(ln.RootCause));
                    //[003] Code Start
                    jsn.AddVariable("Avoidable", ln.Avoidable);
                    //[003] Code end
                    jsn.AddVariable("IsProdHaz", Convert.ToBoolean(ln.IsProdHazardous));
                    jsn.AddVariable("IsPrintHaz", Convert.ToBoolean(ln.PrintHazardous));
                    jsn.AddVariable("ReqSerialNo", Convert.ToBoolean(ln.ReqSerialNo));
                    //[004] Code Start
                    jsn.AddVariable("IsOrderViaIPOonly", Convert.ToBoolean(ln.IsOrderViaIPOonly));
                    //[004] Code end
                    jsn.AddVariable("IsOrderViaIPOonly", Convert.ToBoolean(ln.IsOrderViaIPOonly));
                    Product objcReq = Product.GetHazardousProductStatusMessage(ln.ProductNo, Convert.ToBoolean(ln.IsProdHazardous), Convert.ToBoolean(ln.IsOrderViaIPOonly), ln.ClientNo);
                    if (objcReq != null)
                        jsn.AddVariable("ProductMessage", Functions.ReplaceLineBreaks(objcReq.ProductMessage));
                    else
                        jsn.AddVariable("ProductMessage", "");
                    jsn.AddVariable("AS6081", ln.AS6081);
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                ln = null;
            }
        }

        /// <summary>
        /// Add new customerRmaLine
        /// </summary>
        public void AddNew()
        {
            int customerRMAId = 0;
            int customerRMANumber = 0;
            try
            {
                int intNewCustomerRmaLineID = CustomerRmaLine.Insert(
                    ID
                    , GetFormValue_Int("InvoiceLineNo")
                    , GetFormValue_DateTime("ReturnDate")
                    , ""
                    , GetFormValue_String("Reason1")
                    , GetFormValue_String("Reason2")                    
                    , GetFormValue_Int("Quantity")
                    , GetFormValue_String("LineNotes")
                    , GetFormValue_String("RootCause")
                    , LoginID
                    , out customerRMAId
                    , out customerRMANumber
                    , null
                    //[003] Code Start
                    , GetFormValue_Boolean("Avoidable")
                    //[003] Code end
                    ,false
                    , GetFormValue_NullableBoolean("AS6081",false)
                );

                if (customerRMAId > 0)
                {
                    WebServices service = new WebServices();
                    service.NotifyCustomerRMAToPoHub("", (SessionManager.POHubMailGroupId ?? 0).ToString(), Functions.GetGlobalResource("MailTemplates", "CustomerRMAToPoHub"), customerRMAId);
                }

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewCustomerRmaLineID > 0);
                jsn.AddVariable("NewID", intNewCustomerRmaLineID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Add new customerRmaLine & customerRmaLineAllocation
        /// </summary>
        public void AddNewWithAllocation()
        {
            bool blnOK = true;
            int intCustomerRmaLineID = 0;
            int customerRMAId = 0;
            int customerRMANumber = 0;
            try
            {
                int intNewCustomerRmaLineID = CustomerRmaLine.Insert(
                    ID
                    , GetFormValue_Int("InvoiceLineNo")
                    , GetFormValue_DateTime("ReturnDate")
                     , GetFormValue_String("Reason")
                    , GetFormValue_String("Reason1")
                    , GetFormValue_String("Reason2")
                    , GetFormValue_Int("Quantity")
                    , GetFormValue_String("LineNotes")
                    , GetFormValue_String("RootCause")
                    , LoginID
                    , out customerRMAId
                    , out customerRMANumber
                    , GetFormValue_Int("StockNo")
                    , GetFormValue_Boolean("Avoidable")
                    , GetFormValue_NullableBoolean("PrintHazWar", 0)
                    , GetFormValue_NullableBoolean("AS6081", false)
                ); ;
                if (customerRMAId > 0)
                {
                    WebServices service = new WebServices();
                    service.NotifyCustomerRMAToPoHub("", (SessionManager.POHubMailGroupId ?? 0).ToString(), Functions.GetGlobalResource("MailTemplates", "CustomerRMAToPoHub"), customerRMAId);
                }
                if (intNewCustomerRmaLineID < 1) blnOK = false;
                intCustomerRmaLineID = intNewCustomerRmaLineID;
                //now add the new InvoiceLineAllocation(s)
                int intNewCustomerRmaLineAllocationID = CustomerRmaLineAllocation.Insert(
                    intCustomerRmaLineID
                    , GetFormValue_Int("InvoiceLineAllocationNo")
                    , GetFormValue_Int("Quantity")
                    , null
                    , LoginID
                );
                if (intNewCustomerRmaLineAllocationID < 1) blnOK = false;
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                jsn.AddVariable("NewID", intNewCustomerRmaLineID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Update an existing customerRmaLine
        /// </summary>
        public void SaveEdit()
        {
            try
            {
                bool blnOK = CustomerRmaLine.Update(ID, GetFormValue_Int("Quantity"), "", GetFormValue_String("Reason1"), GetFormValue_String("LineNotes"), GetFormValue_String("RootCause"), GetFormValue_String("Reason2"), LoginID,
                    //[003] Code Start
                    GetFormValue_Boolean("Avoidable"), GetFormValue_NullableBoolean("PrintHazWar",0)
                    //[003] Code end
                    );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Delete
        /// </summary>
        public void Delete()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", CustomerRmaLine.Delete(ID));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Gets data for a new Line based on an invoiceLine
        /// </summary>
        public void GetInvoiceLineForNew()
        {
            InvoiceLine il = null;
            try
            {
                il = InvoiceLine.Get(ID);
                if (il == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Quantity", Functions.FormatNumeric(il.QuantityShipped));
                    jsn.AddVariable("Part", il.Part);
                    jsn.AddVariable("DateCd", il.DateCode);
                    jsn.AddVariable("Manufacturer", il.ManufacturerName);
                    jsn.AddVariable("Product", il.ProductDescription);
                    jsn.AddVariable("PackageName", il.PackageDescription);
                    jsn.AddVariable("CustomerPart", il.CustomerPart);
                    jsn.AddVariable("ROHS", il.ROHS);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                il = null;
            }
        }

        /// <summary>
        /// get invoiceLines 
        /// </summary>
        private void GetInvoiceLineData()
        {
            try
            {
                CustomerRma crma = CustomerRma.Get(ID);
                List<InvoiceLine> lst = InvoiceLine.GetListForInvoice(crma.InvoiceNo);
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                jsn.AddVariable("Count", lst.Count);
                foreach (InvoiceLine il in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", il.InvoiceLineId);
                    jsnItem.AddVariable("Part", il.Part);
                    jsnItem.AddVariable("Quantity", Functions.FormatNumeric(il.QuantityShipped));
                    jsnItem.AddVariable("Manufacturer", il.ManufacturerName);
                    jsnItem.AddVariable("Product", il.ProductDescription);
                    jsnItem.AddVariable("Package", il.PackageDescription);
                    jsnItem.AddVariable("CustomerPart", il.CustomerPart);
                    jsnItem.AddVariable("ROHS", il.ROHS);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        //<summary>
        //Get Allocations for a line
        //</summary>
        public void GetLineAllocations()
        {
            try
            {
                List<Allocation> lst = Allocation.GetListForCustomerRMALine(ID);
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                jsn.AddVariable("Count", lst.Count);
                foreach (Allocation ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", ln.AllocationId);
                    jsnItem.AddVariable("Part", ln.Part);
                    jsnItem.AddVariable("CustomerPart", ln.CustomerPart);
                    jsnItem.AddVariable("QuantityAllocated", Functions.FormatNumeric(ln.QuantityAllocated));
                    jsnItem.AddVariable("StockNo", ln.StockNo);
                    jsnItem.AddVariable("Salesman", ln.SalesmanName);
                    jsnItem.AddVariable("CustomerNo", ln.CompanyNo);
                    jsnItem.AddVariable("Customer", ln.CompanyName);
                    string companyNotes = Company.GetAdvisoryNotes(ln.CompanyNo);
                    jsnItem.AddVariable("CustomerAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
                    if (ln.SupplierRMANo > 0)
                    {
                        jsnItem.AddVariable("SRMANo", ln.SupplierRMANo);
                        jsnItem.AddVariable("SRMANumber", ln.SupplierRMANumber);
                        jsnItem.AddVariable("ReturnDate", Functions.FormatDate(ln.ReturnDate));
                    }
                    else
                    {
                        jsnItem.AddVariable("SalesOrderNo", ln.SalesOrderNo);
                        jsnItem.AddVariable("SalesOrderNumber", ln.SalesOrderNumber);
                        jsnItem.AddVariable("SalesOrderLineNo", ln.SalesOrderLineNo);
                        jsnItem.AddVariable("DatePromised", Functions.FormatDate(ln.DatePromised));
                        jsnItem.AddVariable("CustomerPO", ln.CustomerPO);
                        jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.SellCurrencyCode));
                    }
                    //[003] code start
                    jsnItem.AddVariable("SalesOrderLineNo", ln.SalesOrderLineNo);
                    //[003] code end
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("Lines", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        private void GetLineReceived()
        {
            try
            {
                List<GoodsInLine> lst = GoodsInLine.GetListForCustomerRMALine(ID);
                var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x => x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);
                
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                jsn.AddVariable("Count", lst.Count);
                foreach (GoodsInLine gl in lst)
                {
                    string mfrNotes = !Functions.HasNumbericValue(gl.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)gl.ManufacturerNo).AdvisoryNotes;
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", gl.GoodsInLineId);
                    jsnItem.AddVariable("GoodsInNo", gl.GoodsInNo);
                    jsnItem.AddVariable("GoodsInNumber", gl.GoodsInNumber);
                    jsnItem.AddVariable("Qty", Functions.FormatNumeric(gl.Quantity));
                    jsnItem.AddVariable("Part", gl.Part);
                    jsnItem.AddVariable("SupplierPart", gl.SupplierPart);
                    jsnItem.AddVariable("DC", gl.DateCode);
                    jsnItem.AddVariable("StockNo", gl.StockNo);
                    jsnItem.AddVariable("Mfr", gl.ManufacturerCode);
                    jsnItem.AddVariable("MfrNo", gl.ManufacturerNo);
                    jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                    jsnItem.AddVariable("Product", gl.ProductName);
                    jsnItem.AddVariable("Package", gl.PackageName);
                    jsnItem.AddVariable("LandedCost", Functions.FormatCurrency(gl.LandedCost, SessionManager.ClientCurrencyCode));
                    jsnItem.AddVariable("Location", gl.Location);
                    jsnItem.AddVariable("DateReceived", Functions.FormatDate(gl.DateReceived, false));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("Items", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        public void GetInvoiceLineAllocationCandidates()
        {
            try
            {
                List<InvoiceLineAllocation> lst = InvoiceLineAllocation.GetListCandidatesForCustomerRMA(ID);
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                foreach (InvoiceLineAllocation ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("Cost", Functions.FormatCurrency(ln.LandedCost, ln.CurrencyCode));
                    jsnItem.AddVariable("Qty", Functions.FormatNumeric(ln.Quantity));
                    jsnItem.AddVariable("QtyAllocated", Functions.FormatNumeric(ln.QuantityAllocatedToCRMA));
                    jsnItem.AddVariable("QtyRemaining", Functions.FormatNumeric(ln.Quantity - ln.QuantityAllocatedToCRMA));
                    jsnItem.AddVariable("LineID", ln.InvoiceLineNo);
                    jsnItem.AddVariable("ID", ln.InvoiceLineAllocationId);
                    jsnItem.AddVariable("Part", ln.Part);
                    jsnItem.AddVariable("ROHS", ln.ROHS);
                    jsnItem.AddVariable("Product", ln.ProductDescription);
                    jsnItem.AddVariable("Package", ln.PackageDescription);
                    jsnItem.AddVariable("Mfr", ln.ManufacturerCode);
                    jsnItem.AddVariable("DC", ln.DateCode);
                    jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                    jsnItem.AddVariable("Location", ln.Location);
                    jsnItem.AddVariable("Lot", ln.LotName);
                    jsnItem.AddVariable("InvoiceDate", Functions.FormatDate(ln.InvoiceDate));
                    jsnItem.AddVariable("SO", ln.SalesOrderNumber);
                    jsnItem.AddVariable("SoLineNo", ln.SOSerialNo);

                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Lines", jsnItems);
                jsn.AddVariable("Count", lst.Count);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        public void GetLineAllocationData()
        {
            try
            {
                InvoiceLineAllocation ila = InvoiceLineAllocation.GetCandidateForCustomerRMA(ID, GetFormValue_Int("CRMAID"));
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Part", ila.Part);
                jsn.AddVariable("Quantity", Functions.FormatNumeric(ila.Quantity));
                jsn.AddVariable("QuantityAllocated", Functions.FormatNumeric(ila.QuantityAllocated));
                jsn.AddVariable("QuantityCRMAAlloc", Functions.FormatNumeric(ila.QuantityAllocatedToCRMA));
                jsn.AddVariable("InvoiceLineNo", ila.InvoiceLineNo);
                jsn.AddVariable("StockNo", ila.StockNo);
                jsn.AddVariable("AS6081", ila.AS6081);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Delete allocation
        /// </summary>
        private void DeleteAllocation()
        {
            try
            {
                Array ary = Functions.JavascriptStringToArray(GetFormValue_String("LineIDs"));
                bool blnResult = true;
                for (int i = 0; i < ary.Length; i++)
                {
                    blnResult = blnResult && Allocation.Delete(Convert.ToInt32(ary.GetValue(i)), LoginID);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Close
        /// </summary>
        public void CloseLine()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", CustomerRmaLine.UpdateClose(ID, LoginID));
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private JsonObject GetJsonObject(string strValues, string strName)
        {
            JsonObject jsnItems = new JsonObject(true);
            Array strArray = Functions.JavascriptStringToArray(strValues, new string[] { "," });
            for (int i = 0; i < strArray.Length; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable(strName, strArray.GetValue(i));
                jsnItems.AddVariable(jsnItem); jsnItem = null;
            }
            strArray = null;
            return jsnItems;
        }
        /// <summary>
        /// Get List of 8D Category
        /// </summary>
        public void GetCategory()
        {
            try
            {
                JsonObject jsn = null;
                jsn = new JsonObject();
                JsonObject jsnEightDCat = new JsonObject(true);
                List<EightDCode> lst8DCat = EightDCode.GetListCategory();
                if (lst8DCat != null) 
                {
                    foreach (EightDCode dc in lst8DCat)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("CategoryName", dc.PrefixDescription);
                        jsnItem.AddVariable("Prefix", dc.Prefix);
                        jsnItem.AddVariable("SubCategory", dc.CodeDescription);
                        jsnItem.AddVariable("CatSubCode", dc.Code);
                        jsnItem.AddVariable("SubCatId", dc.Analysis8DSubCategoryID);
                        jsnEightDCat.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                }
                jsn.AddVariable("EightDCode", jsnEightDCat);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
    }
}