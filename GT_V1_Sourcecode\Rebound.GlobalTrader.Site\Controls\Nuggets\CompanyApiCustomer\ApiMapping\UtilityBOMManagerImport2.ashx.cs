using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Web.Script.Serialization;
using System.Text;
using System.Text.RegularExpressions;
using System.Configuration;
using Microsoft.Azure.Storage.Auth;
using Microsoft.Azure.Storage;
using Microsoft.Azure.Storage.Blob;
using System.IO;
using System.Net;
using System.Linq;
using System.Reflection;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class UtilityBOMManagerImport2 : Rebound.GlobalTrader.Site.Data.Base
    {
        string dbServer = null;
        bool IsServerLocal = false;
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                if (context.Request.QueryString["action"] != null)
                    Action = context.Request.QueryString["action"];
                switch (Action)
                {
                    //case "AddNew": AddNew(); break;
                    case "GetData": GetData(context); break;
                    case "GetExcelHeaderFrom": GetExcelHeaderFrom(context); break;
                    case "GetDataHeader": GetCSVDataHeader(context); break; //d
                    case "ImportData": ImportCSVData(context); break;
                    case "GetExcelHeader": GetExcelHeader(context); break;
                    case "GenrateBOMData": GenrateBOMData(context); break;
                    case "GetClient": GetClient(context); break;
                    case "GetCurrency": GetCurrency(context); break;
                    case "ImportBOMdata": ImportBOMdata(context); break;
                    case "DeleteRecord": DeleteRecord(context); break;
                    case "GetCompanyAndOtherMasterData": GetCompanyAndOtherMasterData(context); break;
                    case "DeleteTempMapping": DeleteTempMapping(context); break;
                    case "SaveSupplierColumnMapping": SaveSupplierColumnMapping(context); break;
                    case "GetSupplierMappedColumn": GetSupplierMappedColumn(context); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }


        private void SaveSupplierColumnMapping(HttpContext context)
        {
            try
            {
                string InsertMeppedColumnList = string.Empty;
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int CompanyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
                InsertMeppedColumnList = string.IsNullOrEmpty(context.Request.QueryString["SaveMappingColumnlist"]) ? "" : Convert.ToString((context.Request.QueryString["SaveMappingColumnlist"]));
                string MappingHeaderColumns = string.IsNullOrEmpty(context.Request.QueryString["MappingHeaderColumns"]) ? "" : context.Request.QueryString["MappingHeaderColumns"];
                Stock.SaveCompanyColumnMappingBOMManager(CompanyId, InsertMeppedColumnList.TrimEnd(','), clientType_con);
                Stock.SaveBOMManagerExcelColumnHeaderDetails(InsertMeppedColumnList.TrimEnd(','), MappingHeaderColumns, SessionManager.ClientID, CompanyId, SessionManager.LoginID);
            }
            catch (Exception)
            {

            }
        }
        private void DeleteTempMapping(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int CompanyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
                Stock.DeleteCompanyTempMappingBOMManager(CompanyId, clientType_con);
            }
            catch (Exception)
            {

            }
        }
        public void GetSupplierMappedColumn(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int CompanyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
                DataTable dtSpResult = Stock.GetCompanyMappedColumn(CompanyId, clientType_con);
                context.Response.Write(ConvertHeaderToJsonObj(dtSpResult));

            }
            catch (Exception)
            {

                WriteErrorDataNotFound();
            }
        }
        /// <summary>
        /// Add new purchaseOrder
        /// </summary>
        //public void AddNew() {
        //    try {
        //        int intNewPurchaseOrderID = PurchaseOrder.Insert(
        //            SessionManager.ClientID,
        //            GetFormValue_NullableInt("CMNo"),
        //            GetFormValue_NullableInt("ContactNo"),
        //            GetFormValue_NullableDateTime("DateOrdered", DateTime.Now),
        //            GetFormValue_NullableInt("WarehouseNo"),
        //            GetFormValue_NullableInt("CurrencyNo"),
        //            GetFormValue_NullableInt("BuyerNo"),
        //            GetFormValue_NullableInt("ShipViaNo"),
        //            GetFormValue_String("Account"),
        //            GetFormValue_NullableInt("TermsNo"),
        //            null,
        //            null,
        //            GetFormValue_NullableDouble("TotalShipInCost"),
        //            GetFormValue_NullableInt("DivisionNo"),
        //            GetFormValue_NullableInt("TaxNo"),
        //            GetFormValue_String("Notes"),
        //            GetFormValue_String("Instructions"),
        //            false,
        //            GetFormValue_Boolean("Confirmed"),
        //            GetFormValue_NullableInt("PurchaseOrderImportCountryNo"),
        //            GetFormValue_String("FreeOnBoard"),
        //            0,
        //            false,
        //            GetFormValue_NullableInt("IncotermNo"),
        //            LoginID
        //        );

        //        if (intNewPurchaseOrderID > 0) {
        //            int intSalesOrderLineID = Convert.ToInt32(GetFormValue_NullableInt("SOLineID"));
        //            //copy SO line to PO if we have come from a Pur Req
        //            if (intSalesOrderLineID > 0) {
        //                int intNewPurchaseOrderLineID = InsertPurchaseOrderLineFromSalesOrderLine(intNewPurchaseOrderID, intSalesOrderLineID);
        //                //now allocate
        //                Stock stk = Stock.GetForPurchaseOrderLine(intNewPurchaseOrderLineID);
        //                int intNewAllocationID = Allocation.Insert(
        //                      stk.StockId
        //                    , intSalesOrderLineID
        //                    , stk.QuantityOnOrder
        //                    , null
        //                    , LoginID
        //                );
        //                stk = null;
        //            }
        //            JsonObject jsn = new JsonObject();
        //            jsn.AddVariable("NewID", intNewPurchaseOrderID);
        //            OutputResult(jsn);
        //            jsn.Dispose();
        //            jsn = null;
        //        } else {
        //            WriteErrorSQLActionFailed("Insert");
        //        }
        //    } catch (Exception e) {
        //        WriteError(e);
        //    }
        //}

        //private int InsertPurchaseOrderLineFromSalesOrderLine(int intNewPurchaseOrderID, int intSalesOrderLineID) {
        //    return PurchaseOrderLine.InsertFromSalesOrderLine(intSalesOrderLineID, intNewPurchaseOrderID, LoginID);
        //}
        private void GetExcelHeaderFrom(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                DataTable dtColumnList = Stock.GetBOMManagerExcelHeaderFrom(SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, SelectedclientId, clientType_con);
                JavaScriptSerializer js = new JavaScriptSerializer();
                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void GetCSVDataHeader(HttpContext context)
        {
            try
            {
                //DataTable dtColumnList = Stock.GetCustTableAllColumn();
                //JavaScriptSerializer js = new JavaScriptSerializer();
                //context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private string ConvertHeaderToJsonObj(DataTable dt)
        {
            StringBuilder JsonString = new StringBuilder();
            if (dt != null && dt.Rows.Count > 0)
            {
                //JsonString.Append("[");
                JsonString.Append("{");
                JsonString.Append("\"data\":");
                JsonString.Append("[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    JsonString.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        if (j < dt.Columns.Count - 1)
                        {
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\",");
                        }
                        else if (j == dt.Columns.Count - 1)
                        {
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\"");
                        }
                    }
                    if (i == dt.Rows.Count - 1)
                    {
                        JsonString.Append("}");
                    }
                    else
                    {
                        JsonString.Append("},");
                    }
                }
                JsonString.Append("]");
                JsonString.Append("}");
                //JsonString.Append("]");

                return Regex.Replace(JsonString.ToString(), @"\r\n?|\n", "");
            }
            else
            {
                return null;
            }
        }
        public void ImportCSVData(HttpContext context)
        {
            try
            {
                bool IsLimitExceeded = false;
                string LimitErrorMessage = "";
                string originalFilename = GetFormValue_String("originalFilename");
                string generatedFilename = GetFormValue_String("generatedFilename");
                string chkcolumnheader = GetFormValue_String("ColumnHeader");
                int clientType_con = GetFormValue_Int("SelectedClientType");
                int FormatId = GetFormValue_Int("FormatId");
                string Delimiter = GetFormValue_String("Delimiter");

                string filepathtempfolder = FileUploadManager.GetTemporaryUploadFilePath() + generatedFilename;
                string filepath = ConfigurationManager.AppSettings["ExcelDocumentPhysicalURLForAzureBlob"].ToString() + @"STOCKIMPORT/" + generatedFilename;
                //String strorageconn = ConfigurationManager.AppSettings.Get("StorageConnectionString");
                //CloudStorageAccount storageacc = CloudStorageAccount.Parse(strorageconn);
                // CloudBlobClient client = storageacc.CreateCloudBlobClient();
                string accountname = ConfigurationManager.AppSettings.Get("StorageName");
                string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
                StorageCredentials creden = new StorageCredentials(accountname, accesskey);
                CloudStorageAccount acc = new CloudStorageAccount(creden, useHttps: true);
                CloudBlobClient client = acc.CreateCloudBlobClient();
                CloudBlobContainer cont = client.GetContainerReference("gtdocmgmt");
                if (cont.Exists())
                {
                    CloudBlobDirectory directory = cont.GetDirectoryReference("UTILITYBOM");
                    CloudBlockBlob cblob = directory.GetBlockBlobReference(GetFormValue_String("generatedFilename"));
                    if (cblob.Exists())
                    {
                        string fileExtension = Path.GetExtension(filepath);
                        int SelectedclientId = int.Parse(string.IsNullOrEmpty(GetFormValue_String("ClientId")) ? "0" : GetFormValue_String("ClientId"));
                        //DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepath, chkcolumnheader) : ConvertExcelToDataTable(filepathtempfolder, chkcolumnheader, generatedFilename);//ReadExcel(filepath)/or/ReadCsvFile(filepath);
                        //DataTable dt = fileExtension == ".csv" ? ConvertCSVToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);//ReadExcel(filepath)/or/ReadCsvFile(filepath);
                        DataTable dt = fileExtension == ".csv" ? ReadCsvFile(filepathtempfolder, chkcolumnheader, generatedFilename) : ConvertExcelToDataTableNew(filepathtempfolder, chkcolumnheader, generatedFilename);
                        string DefaultCurrency = GetFormValue_String("DefaultCurrency");

                        //int filelogid = 0;
                        //this.SaveExcelHeaderManager(dt, SelectedclientId, clientType_con);
                        //this.saveExcelData_BulkSaveManager(dt, originalFilename, generatedFilename, SelectedclientId, clientType_con, DefaultCurrency);

                        ////Vinay: 05 May 2021: Dispose unused object
                        //dt.Dispose();

                        //JsonObject jsn = new JsonObject();
                        //jsn.AddVariable("FileLogId", filelogid);
                        //OutputResult(jsn);
                        //jsn.Dispose(); jsn = null;
                        int filelogid = 0;
                        if (dt.Rows.Count <= Convert.ToInt32(ConfigurationManager.AppSettings["MaxUploadRowCount"]))
                        {
                            this.SaveExcelHeaderManager(dt, SelectedclientId, clientType_con);
                            this.saveExcelData_BulkSaveManager(dt, originalFilename, generatedFilename, SelectedclientId, clientType_con, DefaultCurrency);

                            //Vinay: 05 May 2021: Dispose unused object
                        }
                        else
                        {
                            cblob.DeleteIfExists();
                            IsLimitExceeded = true;
                            LimitErrorMessage = "Maximum limit should not exceed " + ConfigurationManager.AppSettings["MaxUploadRowCount"].ToString() + " rows.";
                        }
                        dt.Dispose();

                        JsonObject jsn = new JsonObject();
                        jsn.AddVariable("FileLogId", filelogid);
                        jsn.AddVariable("IsLimitExceeded", IsLimitExceeded);
                        jsn.AddVariable("LimitErrorMessage", LimitErrorMessage);
                        OutputResult(jsn);
                        jsn.Dispose(); jsn = null;
                    }
                    else
                    {
                        throw new Exception("File not uploaded successfully.", new Exception("FileNotFound"));
                    }
                    //Vinay: 05 May 2021: Dispose unused object
                    cont = null;
                    client = null;
                    acc = null;
                }

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        public DataTable ReadCsvFile(string filepath, string chkhead, string FileName)
        {
            DataTable dtCsv = new DataTable();
            dtCsv.Clear();
            Regex CSVParser = new Regex(",(?=(?:[^\"]*\"[^\"]*\")*(?![^\"]*\"))");
            try

            {

                string Fulltext;
                WebClient web = new WebClient();
                System.IO.Stream stream = web.OpenRead(filepath);
                using (StreamReader sr = new StreamReader(stream, Encoding.Default))
                {
                    while (!sr.EndOfStream)
                    {
                        Fulltext = sr.ReadToEnd().ToString(); //read full file text  
                        string[] rows = Fulltext.Split('\n'); //split full file text into rows  
                        if (chkhead == "YES")
                        {
                            for (int i = 0; i < rows.Length - 1; i++)
                            {
                                //string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
                                {
                                    if (i == 0)
                                    {
                                        for (int j = 0; j < rowValues.Length; j++)
                                        {
                                            dtCsv.Columns.Add(Functions.CleanDatabaseFilter(Functions.ReplaceLineBreaks(Functions.CleanJunkCharInCSV(Functions.FormatStringForDatabase((rowValues[j].ToString()))))));

                                        }

                                    }
                                    else
                                    {

                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                }
                            }
                        }
                        else
                        {
                            for (int i = 0; i < rows.Length - 1; i++)
                            {
                                //string[] rowValues = rows[i].Split(','); //split each row with comma to get individual values  
                                string[] rowValues = CSVParser.Split(rows[i]); //split each row with comma to get individual values  
                                {
                                    if (i == 0)
                                    {
                                        int counter = 1;

                                        for (int j = 0; j < rowValues.Length; j++)
                                        {
                                            dtCsv.Columns.Add("F" + counter); //Add header if not have header like F1
                                            counter++;

                                        }
                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                    else
                                    {

                                        DataRow dr = dtCsv.NewRow();
                                        for (int k = 0; k < rowValues.Length; k++)
                                        {

                                            dr[k] = Functions.CleanJunkCharInCSV(Functions.CleanDatabaseForStockTool((rowValues[k].ToString())));
                                            //dr[k] = Functions.FormatStringForDatabase(Functions.CleanDatabaseForStockTool(Functions.CleanJunkCharInCSV((rowValues[k].ToString()))));
                                        }
                                        dtCsv.Rows.Add(dr); //add other rows 

                                    }
                                }
                            }
                        }
                    }
                }


                //Vinay: 05 May 2021: Dispose unused object
                stream.Dispose();
                web.Dispose();
                web = null;
                CSVParser = null;
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
            }
            catch (Exception)
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processing the csv file.Kindly review the data in the csv file.", new Exception("CSVDataError"));
            }
            return dtCsv;
        }
        public DataTable ConvertExcelToDataTableNew(string FilePath, string chkhead, string FileName)
        {
            DataTable dt = new DataTable();
            FileInfo fi = new FileInfo(FilePath);
            try
            {
                if (fi.Exists)
                {
                    List<string> sheets = ExcelAdapter.GetSheet(FilePath);
                    if (sheets.Count > 0)
                    {
                        if (chkhead == "YES")
                        { dt = ExcelAdapter.ReadExcel(FilePath, sheets[0]); }
                        else
                        {
                            dt = ExcelAdapter.ReadExcel(FilePath, sheets[0], false);
                            int i = 1;
                            int c = 0;
                            foreach (DataColumn column in dt.Columns)
                            {
                                if (i <= 15)
                                {
                                    dt.Columns[c].ColumnName = "F" + i;
                                }
                                ++i;
                                ++c;

                            }
                        }

                    }



                }
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);

            }
            catch (Exception)
            {
                string Deletetempfolderfile = FileUploadManager.GetTemporaryUploadFilePath() + FileName;
                System.IO.File.Delete(Deletetempfolderfile);
                throw new Exception("Error Occured while processing the excel file.Kindly review the data in the excel file.", new Exception("ExcelDataError"));
            }
            return dt;
        }
        private void SaveExcelHeaderManager(DataTable dtData, int SelectedclientId, int clientType_con)
        {
            try
            {
                string columnList = string.Empty;
                string insertColumnList = string.Empty;
                int i = 1;
                foreach (DataColumn column in dtData.Columns)
                {
                    if (i <= 15)
                    {
                        columnList = columnList + "'" + column.ColumnName + "',";
                        insertColumnList = insertColumnList + "Column" + i.ToString() + ",";
                    }
                    ++i;
                }
                if (!string.IsNullOrEmpty(columnList))
                    columnList = columnList.Substring(0, columnList.Length - 1);
                if (!string.IsNullOrEmpty(insertColumnList))
                    insertColumnList = insertColumnList.Substring(0, insertColumnList.Length - 1);

                BLL.Stock.SaveBOMExcelHeaderManager(columnList, insertColumnList, SessionManager.ClientID, SelectedclientId, SessionManager.LoginID ?? 0, clientType_con);
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void saveExcelData_BulkSaveManager(DataTable dtData, string originalFilename, string generatedFilename, int SelectedclientId, int clientType_con, string DefaultCurrency)
        {
            try
            {
                DataTable tempStock = new DataTable("BorisGlobalTraderImports.dbo.tbTempBomManagerData");
                // Copy the DataTable to SQL Server using SqlBulkCopy
                BLL.Stock.saveBOMExcelBulkSaveManager(tempStock, dtData, originalFilename, generatedFilename, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, clientType_con, DefaultCurrency);
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }



        }
        public void GetData(HttpContext context)
        {

            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int displayLength = int.Parse(context.Request.Params["Length"]);
                int displayStart = int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "asc";// context.Request.Params["order[0][dir]"];
                string search = context.Request.Params["search[value]"];
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                DataTable dtStcokResult = Stock.GetBOMManagerDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, clientType_con);
                int total = Convert.ToInt32(dtStcokResult.Rows.Count == 0 ? "0" : dtStcokResult.Rows[0]["totalcount"].ToString());
                context.Response.Write(DataTableToJsonObj(dtStcokResult, total, total));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private void GetExcelHeader(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
                DataTable dtColumnList = Stock.GetBOMManagerExcelHeader(SessionManager.ClientID ?? 0, SessionManager.LoginID ?? 0, SelectedclientId, clientType_con);
                JavaScriptSerializer js = new JavaScriptSerializer();
                context.Response.Write(ConvertHeaderToJsonObj(dtColumnList));
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private string DataTableToJsonObj(DataTable dt, int iTotalRecords, int iTotalDisplayRecords)
        {
            StringBuilder JsonString = new StringBuilder();
            if (dt != null && dt.Rows.Count > 0)
            {
                //JsonString.Append("[");
                JsonString.Append("{\"iTotalRecords\":" + iTotalRecords + ",");
                JsonString.Append("\"iTotalDisplayRecords\":" + iTotalDisplayRecords + ",");
                JsonString.Append("\"data\":");
                JsonString.Append("[");
                for (int i = 0; i < dt.Rows.Count; i++)
                {
                    JsonString.Append("{");
                    for (int j = 0; j < dt.Columns.Count; j++)
                    {
                        if (j < dt.Columns.Count - 1)
                        {
                            //JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\",");
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString() + "\",");
                        }
                        else if (j == dt.Columns.Count - 1)
                        {
                            //JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString().Replace(@"\r", "").Replace(@"\n", "").Replace(@"\", "") + "\"");
                            JsonString.Append("\"" + dt.Columns[j].ColumnName.ToString() + "\":" + "\"" + dt.Rows[i][j].ToString() + "\"");
                        }
                    }
                    if (i == dt.Rows.Count - 1)
                    {
                        JsonString.Append("}");
                    }
                    else
                    {
                        JsonString.Append("},");
                    }
                }
                JsonString.Append("]");
                JsonString.Append("}");
                //JsonString.Append("]");


            }
            else
            {
                JsonString.Append("{\"iTotalRecords\":" + iTotalRecords + ",");
                JsonString.Append("\"iTotalDisplayRecords\":" + iTotalDisplayRecords + ",");
                JsonString.Append("\"data\":");
                JsonString.Append("[]}");
            }
            return Regex.Replace(JsonString.ToString(), @"\r\n?|\n", "");
        }
        private void GenrateBOMData(HttpContext context)
        {
            string Column_Lable = "";
            string insertDataList = string.Empty;
            string Column_Name = "";

            DataTable dtGenrateImport = new DataTable();
            dtGenrateImport.Clear();

            try
            {
                #region Stock checkbox paramete
                
                int RequirementforTraceabilityId = string.IsNullOrEmpty(context.Request.QueryString["RequirementforTraceability"]) ? 0 : int.Parse(context.Request.QueryString["RequirementforTraceability"]);
                int TypeId = string.IsNullOrEmpty(context.Request.QueryString["Type"]) ? 0 : int.Parse(context.Request.QueryString["Type"]);
                DateTime DateRequired = string.IsNullOrEmpty(context.Request.QueryString["DateRequired"]) ? DateTime.Now : Convert.ToDateTime((context.Request.QueryString["DateRequired"]));
                //int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int CompanyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
                string CompanyNameText = string.IsNullOrEmpty(context.Request.QueryString["CompanyName"]) ? "" : Convert.ToString((context.Request.QueryString["CompanyName"]));
                //int recordType = string.IsNullOrEmpty(context.Request.QueryString["recordType"]) ? 0 : int.Parse(context.Request.QueryString["recordType"]);
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);

                //checkbox parameter
                #endregion

                #region Stock  Dropdwon parameter
                string ddlCurrency = string.IsNullOrEmpty(context.Request.QueryString["ddlCurrency"]) ? "" : Convert.ToString((context.Request.QueryString["ddlCurrency"]));
                string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
                //mapped selected column list
                insertDataList = string.IsNullOrEmpty(context.Request.QueryString["insertDataList"]) ? "" : Convert.ToString((context.Request.QueryString["insertDataList"]));
                Column_Lable = string.IsNullOrEmpty(context.Request.QueryString["Column_Lable"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Lable"]));
                Column_Name = string.IsNullOrEmpty(context.Request.QueryString["Column_Name"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Name"]));

                string ContactText = string.IsNullOrEmpty(context.Request.QueryString["ContactName"]) ? "" : Convert.ToString((context.Request.QueryString["ContactName"]));
                int ContactId = string.IsNullOrEmpty(context.Request.QueryString["ContactId"]) ? 0 : int.Parse(context.Request.QueryString["ContactId"]);

                string FixedCurrency = string.Empty;
                //if (string.IsNullOrEmpty(ddlCurrency))
                //{ ddlCurrency = "GBP"; }
                //else
                //{
                //    if (ddlCurrency == "< Not Imported >")
                //    { ddlCurrency = "GBP"; }
                //    else { FixedCurrency = ddlCurrency; }
                //}
                bool OverRideCurrency = string.IsNullOrEmpty(context.Request.QueryString["chkOverRide"]) ? false : bool.Parse(context.Request.QueryString["chkOverRide"]);
                string DefaultCurrencyName = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyName"]) ? "" : Convert.ToString((context.Request.QueryString["DefaultCurrencyName"]));
                int DefaultCurrencyId = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyId"]) ? 0 : int.Parse(context.Request.QueryString["DefaultCurrencyId"]);
                string CurrencyColumnName = "";//string.IsNullOrEmpty(context.Request.QueryString["CurrencyColumnName"]) ? "" : Convert.ToString((context.Request.QueryString["CurrencyColumnName"]));
                if (OverRideCurrency)
                {
                    ddlCurrency = DefaultCurrencyName;
                }
                #endregion
                #region Stock Import Genrate JSON Table


                int displayLength = 20;//string.IsNullOrEmpty(context.Request.Params["Length"]) ? 20 : int.Parse(context.Request.Params["Length"]);//int.Parse(context.Request.Params["Length"]);
                int displayStart = 0;//string.IsNullOrEmpty(context.Request.Params["Start"]) ? 0 : int.Parse(context.Request.Params["Start"]);//int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;//string.IsNullOrEmpty(context.Request.Params["order[0][column]"]) ? 0 : int.Parse(context.Request.Params["order[0][column]"]);// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "";//"asc";// context.Request.Params["order[0][dir]"];
                string search = "";//context.Request.Params["search[value]"];
                dtGenrateImport = Stock.GetBOMManagerGenrateTempData(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, insertDataList.TrimEnd(','), ddlCurrency, CompanyNameText, 0, Column_Lable.TrimEnd(','), Column_Name.TrimEnd(','), ContactText, OverRideCurrency, DefaultCurrencyName, CurrencyColumnName, DefaultCurrencyId);
                int total = 20;//Convert.ToInt32(dtGenrateImport.Rows.Count == 0 ? "0" : dtGenrateImport.Rows[0]["TotalCount"].ToString());
                dtGenrateImport.Columns.Remove("TotalCount");
                dtGenrateImport.Columns.Remove("RowNum");
                if (!OverRideCurrency)
                {
                    if (dtGenrateImport.Columns.Contains("Currency"))
                    {
                        foreach (DataRow dr in dtGenrateImport.Rows) // search whole table
                        {
                            if (string.IsNullOrEmpty(dr["Currency"].ToString())) // if id==2
                            {
                                dr["Currency"] = DefaultCurrencyName; //change the name
                                                                      //break; break or not depending on you
                            }
                        }
                    }
                }

                var serializer = new System.Web.Script.Serialization.JavaScriptSerializer();

                TestData t = new TestData();
                TestData1 t1 = new TestData1();
                List<columnsinfo> _col = new List<columnsinfo>();

                for (int i = 0; i <= dtGenrateImport.Columns.Count - 1; i++)
                {
                    _col.Add(new columnsinfo { title = dtGenrateImport.Columns[i].ColumnName, data = dtGenrateImport.Columns[i].ColumnName });
                }
                t1.columns = _col;
                string col = (string)serializer.Serialize(_col);
                t.columns = col;
                var lst = dtGenrateImport.AsEnumerable().Select(r => r.Table.Columns.Cast<DataColumn>().Select(c => new KeyValuePair<string, object>(c.ColumnName, r[c.Ordinal])).ToDictionary(z => z.Key, z => z.Value)).ToList();
                t1.data = lst;
                string data = serializer.Serialize(lst);
                t.data = data;
                string strNewDate = serializer.Serialize(t1);
                string teststring = "{\"iTotalRecords\":" + total + "," + "\"iTotalDisplayRecords\":" + total + "," + strNewDate.TrimStart('{');
                context.Response.Write(teststring);

                #endregion


            }
            catch (Exception ex)
            {
                TestData1 err = new TestData1();
                err.IsError = true;
                err.ErrorMessage = ex.Message;
                //err.ErrorMessage += "<br />" + ex.StackTrace.Replace(System.Environment.NewLine, "<br />");
                context.Response.Write(new JavaScriptSerializer().Serialize(err));
                //WriteError(ex);
            }


        }
        public void GetClient(HttpContext context)
        {
            try
            {
                int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                //if (clientType_con == 1)
                //{
                //    //(uk/hk)
                //    DataTable dtClient = BLL.Stock.GetClientName(SessionManager.ClientID, clientType_con);
                //    context.Response.Write(ConvertDataTableToJSON(dtClient));
                //}
                //else 
                if (clientType_con == 2)
                {
                    //LocalSqlServer uk
                    // DataTable dtClient = BLL.Stock.GetClientName(SessionManager.ClientID, clientType_con);
                    //context.Response.Write(ConvertDataTableToJSON(dtClient));

                    DataTable dtClient = GetMasterClient();
                    context.Response.Write(ConvertDataTableToJSON(dtClient));

                }
                else if (clientType_con == 3)
                {
                    //GTSqlServer hk
                    DataTable dtClient = BLL.Stock.GetClientName(SessionManager.ClientID, clientType_con);
                    context.Response.Write(ConvertDataTableToJSON(dtClient));
                }






                //jsn.Dispose();
                //jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        private string ConvertDataTableToJSON(DataTable dt)
        {
            System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
            serializer.MaxJsonLength = Int32.MaxValue;
            List<Dictionary<string, object>> rows = new List<Dictionary<string, object>>();
            Dictionary<string, object> row;
            foreach (DataRow dr in dt.Rows)
            {
                row = new Dictionary<string, object>();
                foreach (DataColumn col in dt.Columns)
                {
                    row.Add(col.ColumnName, dr[col]);
                }
                rows.Add(row);
            }
            return serializer.Serialize(rows);
        }
        //public void GetCurrency(HttpContext context)
        //{
        //    try
        //    {
        //        int clientID = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);
        //        int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
        //        DataTable dtClient = BLL.Stock.GetCurrency(clientID, clientType_con);
        //        context.Response.Write(ConvertDataTableToJSON(dtClient));
        //        //jsn.Dispose();
        //        //jsn = null;
        //    }
        //    catch (Exception ex)
        //    {
        //        WriteError(ex);
        //    }
        //}
        private void ImportBOMdata(HttpContext context)
        {
            string Column_Lable = "";
            string insertDataList = "";
            string Column_Name = "";
            try
            {
                #region Bom tool paramete
                int ReqforTraceabilityId = string.IsNullOrEmpty(context.Request.QueryString["RequirementforTraceability"]) ? 0 : int.Parse(context.Request.QueryString["RequirementforTraceability"]);
                int TypeId = string.IsNullOrEmpty(context.Request.QueryString["Type"]) ? 0 : int.Parse(context.Request.QueryString["Type"]);
                DateTime DateRequired = string.IsNullOrEmpty(context.Request.QueryString["DateRequired"]) ? DateTime.Now : Convert.ToDateTime((context.Request.QueryString["DateRequired"]));
                int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);
                string ActionPerform = string.IsNullOrEmpty(context.Request.QueryString["ActionPerform"]) ? "" : Convert.ToString((context.Request.QueryString["ActionPerform"]));
                string SaveImportOrHubRFQ = string.IsNullOrEmpty(context.Request.QueryString["SaveImportOrHubRFQ"]) ? "" : Convert.ToString((context.Request.QueryString["SaveImportOrHubRFQ"]));
                
                //checkbox parameter
                #endregion

                #region Bom  Dropdwon parameter
                string ddlCurrency = string.IsNullOrEmpty(context.Request.QueryString["ddlCurrency"]) ? "" : Convert.ToString((context.Request.QueryString["ddlCurrency"]));
                string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
                //mapped selected column list
                Column_Lable = string.IsNullOrEmpty(context.Request.QueryString["Column_Lable"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Lable"]));
                insertDataList = string.IsNullOrEmpty(context.Request.QueryString["insertDataList"]) ? "" : Convert.ToString((context.Request.QueryString["insertDataList"]));
                Column_Name = string.IsNullOrEmpty(context.Request.QueryString["Column_Name"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Name"]));


                #endregion
                DataTable dtcount = new DataTable();
                dtcount.Clear();
                int displayLength = 11;//int.Parse(context.Request.Params["Length"]);
                int displayStart = 0;//int.Parse(context.Request.Params["Start"]);
                int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
                string sortDir = "asc";// context.Request.Params["order[0][dir]"];
                string search = context.Request.Params["search[value]"];
                dtcount = Stock.GetBOMManagerDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, 0);
                string TotalCount = dtcount.Rows[0]["TotalCount"].ToString();
                string OriginalFilename = dtcount.Rows[0]["OriginalFilename"].ToString();

                string BomName = string.IsNullOrEmpty(context.Request.QueryString["BomName"]) ? "" : Convert.ToString((context.Request.QueryString["BomName"]));
                string CompanyName = string.IsNullOrEmpty(context.Request.QueryString["CompanyName"]) ? "" : Convert.ToString((context.Request.QueryString["CompanyName"]));
                string ContactName = string.IsNullOrEmpty(context.Request.QueryString["ContactName"]) ? "" : Convert.ToString((context.Request.QueryString["ContactName"]));
                int SalesmanId = string.IsNullOrEmpty(context.Request.QueryString["SalesmanId"]) ? 0 : int.Parse(context.Request.QueryString["SalesmanId"]);
                int CompanyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
                int ContactId = string.IsNullOrEmpty(context.Request.QueryString["ContactId"]) ? 0 : int.Parse(context.Request.QueryString["ContactId"]);
                //bool PartWatch = string.IsNullOrEmpty(context.Request.QueryString["PartWatch"]) ? false : bool.Parse(context.Request.QueryString["PartWatch"]);
                bool PartWatch = string.IsNullOrEmpty(context.Request.QueryString["ApplyPartWatch"]) ? false : bool.Parse(context.Request.QueryString["ApplyPartWatch"]);
                #region Stock Import Save data in SQL Table
                if (ActionPerform == "ImportData")
                {

                    string fileColName = "BOMInfoName";
                    string FixedCurrency = string.Empty;
                    bool OverRideCurrency = string.IsNullOrEmpty(context.Request.QueryString["chkOverRide"]) ? false : bool.Parse(context.Request.QueryString["chkOverRide"]);
                    string DefaultCurrencyName = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyName"]) ? "" : Convert.ToString((context.Request.QueryString["DefaultCurrencyName"]));
                    int DefaultCurrencyId = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyId"]) ? 0 : int.Parse(context.Request.QueryString["DefaultCurrencyId"]);
                    if (OverRideCurrency)
                    {
                        ddlCurrency = DefaultCurrencyName;
                    }
                    else
                    {
                        ddlCurrency = "";
                    }

                    string insertcolumndata = insertDataList.TrimEnd(',');
                    string errorMessage = "";
                    string NewBomCode = "";
                     int NewBomid = 0;
                    int recordCount = BLL.Stock.saveBOMManagerImportData(SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, Column_Lable.TrimEnd(','), Column_Name.TrimEnd(','), insertcolumndata, fileColName, ddlCurrency, out errorMessage, BomName, CompanyName, ContactName, SalesmanId, CompanyId, ContactId, PartWatch, DefaultCurrencyId, OverRideCurrency, SaveImportOrHubRFQ,out NewBomCode,out NewBomid, ReqforTraceabilityId, TypeId,DateRequired,null);

                    //context.Response.Write(TotalCount+","+OriginalFilename);
                    context.Response.Write(recordCount + "," + OriginalFilename + "," + errorMessage + "," + NewBomid + "," + NewBomCode);
                }
                #endregion

            }
            catch (Exception ex)
            {
                TestData1 err = new TestData1();
                err.IsError = true;
                err.ErrorMessage = ex.InnerException.Message;
                //err.ErrorMessage += "<br />" + ex.StackTrace.Replace(System.Environment.NewLine, "<br />");
                context.Response.Write(new JavaScriptSerializer().Serialize(err));
                //WriteError(ex);
            }


        }
        private void DeleteRecord(HttpContext context)
        {
            try
            {
                int clientType_con = 0;//string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
                int SelectedclientId = int.Parse(context.Request.QueryString["SelectedclientId"].ToString());
                Stock.DeleteBOMManagerRecord(SelectedclientId, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, clientType_con);
            }
            catch (Exception)
            {

            }
        }
        public void GetCompanyAndOtherMasterData(HttpContext context)
        {
            try
            {
                int companyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
                DataTable dtResult = Stock.GetContactWithCurrency(companyId);

                context.Response.Write(ConvertDataTableToJSON(dtResult));

            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }
        protected void GetCurrency(HttpContext context)
        {
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = string.IsNullOrEmpty(context.Request.QueryString["ClientId"]) ? 0 : int.Parse(context.Request.QueryString["ClientId"]);

            //string strOptions = CacheManager.SerializeOptions(new object[] { (intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID });
            //JsonObject jsn = new JsonObject();
            //JsonObject jsnList = new JsonObject(true);
            List<BLL.Currency> lst = BLL.Currency.DropDownSellForClient((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID);
            DataTable dt = new DataTable();
            dt.Columns.Add("ID", typeof(System.Int32));
            dt.Columns.Add("Name", typeof(System.String));
            dt.Columns.Add("ResultType", typeof(System.String));
            for (int i = 0; i < lst.Count; i++)
            {
                //JsonObject jsnItem = new JsonObject();
                //jsnItem.AddVariable("ID", lst[i].CurrencyId);
                //jsnItem.AddVariable("Name", String.Format("{1} - {0}", lst[i].CurrencyDescription, lst[i].CurrencyCode));
                //jsnItem.AddVariable("Code", lst[i].CurrencyCode);
                //jsnList.AddVariable(jsnItem);
                //jsnItem.Dispose(); jsnItem = null;
                DataRow row = dt.NewRow();
                row["ID"] = lst[i].CurrencyId;
                row["Name"] = lst[i].CurrencyDescription;
                row["ResultType"] = "Currencies";
                dt.Rows.Add(row);
            }
            //lst.Clear(); lst = null;
            //jsn.AddVariable("Currencies", jsnList);
            //jsnList.Dispose(); jsnList = null;
            context.Response.Write(ConvertDataTableToJSON(dt));
            //jsn.Dispose(); jsn = null;
        }
        public class TestData
        {
            public string jsondata { get; set; }
            public string columns { get; set; }
            public string data { get; set; }
        }
        public class TestData1
        {
            public List<columnsinfo> columns { get; set; }
            public List<Dictionary<string, object>> data { get; set; }

            public bool IsError { get; set; }

            public string ErrorMessage { get; set; }
        }
        public class columnsinfo
        {
            public string title { get; set; }
            public string data { get; set; }
        }
        protected DataTable GetMasterClient()
        {
            Int32? intMastLoginNo = SessionManager.MasterLoginNo;
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.Client> lst = BLL.Client.GeClientByMaster(intMastLoginNo);
            DataTable dt = new DataTable();
            dt.Columns.Add("ClientId", typeof(System.Int32));
            dt.Columns.Add("ClientName", typeof(System.String));
            dt.Columns.Add("ClientCode", typeof(System.String));
            for (int i = 0; i < lst.Count; i++)
            {
                DataRow row = dt.NewRow();
                row["ClientId"] = lst[i].ClientId;
                row["ClientName"] = lst[i].ClientName;
                row["ClientCode"] = lst[i].ClientCode;
                dt.Rows.Add(row);
            }
            return dt;
        }

    }
}
