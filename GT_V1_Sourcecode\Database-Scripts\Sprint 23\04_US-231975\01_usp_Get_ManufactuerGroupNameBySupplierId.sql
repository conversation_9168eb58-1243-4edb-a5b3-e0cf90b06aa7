﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-231975]     Phuc Hoang		 05-Mar-2025		CREATE		IPO - MFR Group Code Franchised Tick Box Further Enhancement
===========================================================================================================================================================  
*/

CREATE OR ALTER PROC [dbo].[usp_Get_ManufactuerGroupNameBySupplierId] (
	@SupplierId INT = NULL,
	@ClientNo INT = NULL,
	@Inactive INT = 0
)
AS

BEGIN
	SELECT DISTINCT cg.ItemId AS ContactGroupId, cg.ContactName, cg.Code
	FROM tbCompany c    
		JOIN tbManufacturerLink mfl ON mfl.SupplierCompanyNo = c.CompanyId 
		JOIN [dbo].[tbManufacturer] mfr ON mfr.ManufacturerId = mfl.ManufacturerNo
		JOIN [dbo].[tbContactGroup] cg ON cg.ItemId = mfr.ContactGroupID
	WHERE c.CompanyId = @SupplierId 
		AND ISNULL(mfr.Inactive, 0) = 0 
		AND ISNULL(cg.Inactive, 0) = 0
END

GO
