﻿//Marker     Changed by      Date         Remarks  
//[001]      Vinay           15/10/2012   Display supplier type in stock grid

using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class ExcessDetails {
		
		#region Constructors
		
		public ExcessDetails() { }
		
		#endregion
		
		#region Properties
        public System.Boolean isIncludeAltPart { get; set; }
		/// <summary>
		/// ExcessId (from Table)
		/// </summary>
		public System.Int32 ExcessId { get; set; }
		/// <summary>
		/// ExcessName (from Table)
		/// </summary>
		public System.String ExcessName { get; set; }
		/// <summary>
		/// FullPart (from Table)
		/// </summary>
		public System.String FullPart { get; set; }
		/// <summary>
		/// Part (from Table)
		/// </summary>
		public System.String Part { get; set; }
		/// <summary>
		/// ManufacturerNo (from Table)
		/// </summary>
		public System.Int32? ManufacturerNo { get; set; }
		/// <summary>
		/// DateCode (from Table)
		/// </summary>
		public System.String DateCode { get; set; }
		/// <summary>
		/// ProductNo (from Table)
		/// </summary>
		public System.Int32? ProductNo { get; set; }
		/// <summary>
		/// PackageNo (from Table)
		/// </summary>
		public System.Int32? PackageNo { get; set; }
		/// <summary>
		/// Quantity (from Table)
		/// </summary>
		public System.Int32 Quantity { get; set; }
		/// <summary>
		/// Price (from Table)
		/// </summary>
		public System.Double Price { get; set; }
		/// <summary>
		/// CurrencyNo (from Table)
		/// </summary>
		public System.Int32? CurrencyNo { get; set; }
		/// <summary>
		/// OriginalEntryDate (from Table)
		/// </summary>
		public System.DateTime? OriginalEntryDate { get; set; }
		/// <summary>
		/// Salesman (from Table)
		/// </summary>
		public System.Int32? Salesman { get; set; }
		/// <summary>
		/// CompanyNo (from Table)
		/// </summary>
		public System.Int32 CompanyNo { get; set; }
		/// <summary>
		/// ROHS (from Table)
		/// </summary>
		public System.Byte? ROHS { get; set; }
		/// <summary>
		/// UpdatedBy (from Table)
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP (from Table)
		/// </summary>
		public System.DateTime DLUP { get; set; }
		/// <summary>
		/// OfferStatusNo (from Table)
		/// </summary>
		public System.Int32? OfferStatusNo { get; set; }
		/// <summary>
		/// OfferStatusChangeDate (from Table)
		/// </summary>
		public System.DateTime? OfferStatusChangeDate { get; set; }
		/// <summary>
		/// OfferStatusChangeLoginNo (from Table)
		/// </summary>
		public System.Int32? OfferStatusChangeLoginNo { get; set; }
		/// <summary>
		/// CompanyName (from Table)
		/// </summary>
		public System.String CompanyName { get; set; }
		/// <summary>
		/// Notes (from Table)
		/// </summary>
		public System.String Notes { get; set; }
		/// <summary>
		/// ManufacturerName (from Table)
		/// </summary>
		public System.String ManufacturerName { get; set; }
		/// <summary>
		/// ProductName (from Table)
		/// </summary>
		public System.String ProductName { get; set; }
		/// <summary>
		/// PackageName (from Table)
		/// </summary>
		public System.String PackageName { get; set; }
		/// <summary>
		/// ClientNo (from Table)
		/// </summary>
		public System.Int32? ClientNo { get; set; }
		/// <summary>
		/// SupplierName (from usp_source_Excess)
		/// </summary>
		public System.String SupplierName { get; set; }
		/// <summary>
		/// SupplierEmail (from usp_source_Excess)
		/// </summary>
		public System.String SupplierEmail { get; set; }
		/// <summary>
		/// ManufacturerCode (from usp_source_Excess)
		/// </summary>
		public System.String ManufacturerCode { get; set; }
		/// <summary>
		/// CurrencyCode (from usp_source_Excess)
		/// </summary>
		public System.String CurrencyCode { get; set; }
		/// <summary>
		/// SalesmanName (from usp_source_Excess)
		/// </summary>
		public System.String SalesmanName { get; set; }
		/// <summary>
		/// OfferStatusChangeEmployeeName (from usp_source_Excess)
		/// </summary>
		public System.String OfferStatusChangeEmployeeName { get; set; }
		/// <summary>
		/// ClientName (from usp_source_Excess)
		/// </summary>
		public System.String ClientName { get; set; }
		/// <summary>
		/// ClientDataVisibleToOthers (from usp_source_Excess)
		/// </summary>
		public System.Boolean? ClientDataVisibleToOthers { get; set; }
        //[001] code start
        /// <summary>
        /// SupplierType
        /// </summary>
        public System.String SupplierType { get; set; }
        //[001] code end
        public System.String ClientCode { get; set; }
        public System.String MSL { get; set; }
        public System.String SPQ { get; set; }
        public System.String LeadTime { get; set; }
        public System.String RoHSStatus { get; set; }
        public System.String FactorySealed { get; set; }
        public System.String SupplierTotalQSA { get; set; }
        public System.String SupplierMOQ { get; set; }
        public System.String SupplierLTB { get; set; }
        public System.String ProductDescription { get; set; }
        public System.Boolean? ProductInactive { get; set; }
        public System.Int32? MSLLevelNo { get; set; }
        public System.String productNameDescrip { get; set; }
        
        //[001] start
        public string TableType { get; set; }
        //[001] end
        public System.Int32? RowNum { get; set; }
        public System.Int32? TotalCount { get; set; }
        public System.String PackageDescription { get; set; }
		public System.String SupplierMessage { get; set; }
		#endregion

	}
}