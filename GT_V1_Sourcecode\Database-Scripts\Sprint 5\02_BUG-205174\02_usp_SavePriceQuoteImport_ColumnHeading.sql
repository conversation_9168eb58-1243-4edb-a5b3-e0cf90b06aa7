﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('dbo.usp_SavePriceQuoteImport_ColumnHeading', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_SavePriceQuoteImport_ColumnHeading
END
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-205174]		An.TranTan			28-Jun-2024		Update			Refactor condition to delete and insert PriceQuoteImport_ColumnHeading
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_SavePriceQuoteImport_ColumnHeading]
    @InserColumnList NVARCHAR(3000),
    @SelectColumns NVARCHAR(3000),
    @ClientId INT,
    @SelectedClientId int,
    @UserId INT
AS
BEGIN
    IF EXISTS
    (
        SELECT TOP 1 1
        FROM BorisGlobalTraderImports.dbo.tbPriceQuoteImportColumnHeading
        WHERE ClientId = @ClientId
              and SelectedClientId = @SelectedClientId
              AND CreatedBy = @UserId
    )
    BEGIN
        delete from BorisGlobalTraderImports.dbo.tbPriceQuoteImportColumnHeading
        where ClientId = @ClientId
              AND CreatedBy = @UserId
              and SelectedClientId = @SelectedClientID
        delete from BorisGlobalTraderImports.dbo.tbPriceQuoteImport_tempData
        where ClientId = @ClientId
              AND CreatedBy = @UserId
              and SelectedClientId = @SelectedClientID
    END

    DECLARE @DynamicQuery NVARCHAR(4000) = '';
    SET @InserColumnList
        = 'INSERT INTO BorisGlobalTraderImports.dbo.tbPriceQuoteImportColumnHeading(' + @InserColumnList
    SET @InserColumnList = @InserColumnList + ', ClientId,SelectedClientId,CreatedBy';
    SET @DynamicQuery
        = @DynamicQuery + @InserColumnList + ') Select ' + @SelectColumns + ',' + CONVERT(NVARCHAR(10), @ClientId)
          + ',' + CONVERT(NVARCHAR(10), @SelectedClientId) + +',' + CONVERT(NVARCHAR(10), @UserId);
    EXEC (@DynamicQuery)
END
GO

