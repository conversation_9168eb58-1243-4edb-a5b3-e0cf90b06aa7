/*
Marker     changed by      date         Remarks
[001]      Vinay           07/04/2015   Ticket Number. 	221
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Net.Mail;
using System.Net.Mime;
using System.Xml;
using System.Xml.Xsl;
using System.Xml.XPath;
using System.Diagnostics;
using System.Reflection;
using System.IO;
using System.Text;
using Rebound.GlobalTrader.Site;
using System.Net;
using Microsoft.Azure.Storage.Blob;

namespace Rebound.GlobalTrader.Site
{
    public class OutgoingMailManager
    {

        public static bool SendUserMail(string strSubject, string strBody, MailAddressCollection adrTo, MailAddress adrFrom, MailAddressCollection adrCC, MailAddressCollection adrBCC, MailAddress adrReplyTo, bool blnHTMLFormat)
        {
            bool blnOK = true;
            //bool allowOnProd = true;
            try
            {
                System.Net.Mail.MailMessage msg = new System.Net.Mail.MailMessage();

                bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
                bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
                var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);

                foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
                if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
                if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
                if (isAllowed)
                {
                    msg.From = new MailAddress(mailFromUAT);
                }
                else
                {
                    if (adrFrom != null) msg.From = adrFrom;
                }
                if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
                if (isAllowed)
                {
                    msg.Subject = "Test Mail ";
                }
                msg.Subject += strSubject;
                msg.Body = strBody;
                msg.IsBodyHtml = blnHTMLFormat;
                //mail
                SmtpClient client = new SmtpClient();

                if (allowOnProd)
                {
                    client.Send(msg);
                }
                else if (isAllowed)
                {
                    SendMailOnUAT(msg, client);
                }
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in sending mail at method SendUserMail : " + ex.Message);
                blnOK = false;
            }
            return blnOK;
        }

        public static bool SendUserMailDocument(string strSubject, MailAddressCollection adrTo, MailAddress adrFrom, MailAddressCollection adrCC, MailAddressCollection adrBCC, MailAddress adrReplyTo, string strDocumentText, string strHeaderImage, string strHeaderImageID)
        {
            bool blnOK = true;

            try
            {
                bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
                bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
                var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);

                System.Net.Mail.MailMessage msg = new System.Net.Mail.MailMessage();
                foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
                if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
                if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
                if (isAllowed)
                {
                    msg.From = new MailAddress(mailFromUAT);
                }
                else
                {
                    if (adrFrom != null) msg.From = adrFrom;
                }
                if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
                if (isAllowed)
                {
                    msg.Subject = "Test Mail ";
                }
                msg.Subject += strSubject;
                msg.IsBodyHtml = true;
                msg.BodyEncoding = Encoding.UTF8;
                msg.Body = strDocumentText;

                //attach the header image
                string strAttachmentPath = HttpContext.Current.Server.MapPath(strHeaderImage);
                if (File.Exists(strAttachmentPath))
                {
                    BuildSettings objSettings = new BuildSettings();
                    Attachment logo = new Attachment(strAttachmentPath);
                    logo.ContentDisposition.Inline = true;
                    logo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                    msg.Attachments.Add(logo);
                    logo.ContentId = strHeaderImageID;
                    //logo.ContentType.MediaType = "image/";
                    logo.ContentType.Name = Path.GetFileName(strAttachmentPath);
                }

                //send mail
                SmtpClient client = new SmtpClient();

                if (allowOnProd)
                {
                    client.Send(msg);
                }
                else if (isAllowed)
                {
                    SendMailOnUAT(msg, client);
                }
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in sending mail at method SendUserMailDocument : " + ex.Message);
                blnOK = false;
            }
            return blnOK;
        }

        public static bool SendUserMailDocument(string strSubject, MailAddressCollection adrTo, MailAddress adrFrom, MailAddressCollection adrCC, MailAddressCollection adrBCC, MailAddress adrReplyTo, string strDocumentText, string strHeaderImage, string strHeaderImageID, string strSignatureImage, string strSignatureImageID, string strTermConditionImage, string strTermConditionImageID)
        {
            bool blnOK = true;
            try
            {
                bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
                bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
                var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);

                System.Net.Mail.MailMessage msg = new System.Net.Mail.MailMessage();
                foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
                if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
                if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
                if (isAllowed)
                {
                    msg.From = new MailAddress(mailFromUAT);
                }
                else
                {
                    if (adrFrom != null) msg.From = adrFrom;
                }
                if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
                if (isAllowed)
                {
                    msg.Subject = "Test Mail ";
                }
                msg.Subject += strSubject;
                msg.IsBodyHtml = true;
                msg.BodyEncoding = Encoding.UTF8;
                msg.Body = strDocumentText;

                //[001] code start
                //attach the header image
                //string strAttachmentPath = HttpContext.Current.Server.MapPath(strHeaderImage);
                string strAttachmentPath = strHeaderImage;
                //[001] code end
                if (File.Exists(strAttachmentPath))
                {
                    BuildSettings objSettings = new BuildSettings();
                    Attachment logo = new Attachment(strAttachmentPath);
                    logo.ContentDisposition.Inline = true;
                    logo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                    msg.Attachments.Add(logo);
                    logo.ContentId = strHeaderImageID;
                    //logo.ContentType.MediaType = "image/";
                    logo.ContentType.Name = Path.GetFileName(strAttachmentPath);
                }

                //attach the signature image
                //[001] code start
                string strSignaturePath = strSignatureImage;// HttpContext.Current.Server.MapPath(strSignatureImage);
                //[001] code end
                if (File.Exists(strSignaturePath))
                {
                    BuildSettings objSettings = new BuildSettings();
                    Attachment signaturelogo = new Attachment(strSignaturePath);
                    signaturelogo.ContentDisposition.Inline = true;
                    signaturelogo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                    msg.Attachments.Add(signaturelogo);
                    signaturelogo.ContentId = strSignatureImageID;
                    //logo.ContentType.MediaType = "image/";
                    signaturelogo.ContentType.Name = Path.GetFileName(strSignaturePath);
                }

                string strTermConditionPath = strTermConditionImage;// HttpContext.Current.Server.MapPath(strSignatureImage);
                                                                    //[001] code end

                if (File.Exists(strTermConditionPath))
                {
                    BuildSettings objSettings = new BuildSettings();
                    Attachment termslogo = new Attachment(strTermConditionPath);
                    termslogo.ContentDisposition.Inline = true;
                    termslogo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                    msg.Attachments.Add(termslogo);
                    termslogo.ContentId = strTermConditionImageID;
                    //logo.ContentType.MediaType = "image/";
                    termslogo.ContentType.Name = Path.GetFileName(strTermConditionPath);
                }

                //send mail
                SmtpClient client = new SmtpClient();

                if (allowOnProd)
                {
                    client.Send(msg);
                }
                else if (isAllowed)
                {
                    SendMailOnUAT(msg, client);
                }
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in sending mail at method SendUserMailDocument : " + ex.Message);
                blnOK = false;
            }
            return blnOK;
        }
        public static bool SendUserMailDocument(string strSubject, MailAddressCollection adrTo, MailAddress adrFrom, MailAddressCollection adrCC, MailAddressCollection adrBCC, MailAddress adrReplyTo, string strDocumentText, string strHeaderImage, string strHeaderImageID, string strSignatureImage, string strSignatureImageID, string strTermConditionImage, string strTermConditionImageID, string strTermsPDFPath)
        {
            bool blnOK = true;
            try
            {
                bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
                bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
                var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);

                System.Net.Mail.MailMessage msg = new System.Net.Mail.MailMessage();
                foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
                if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
                if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
                if (isAllowed)
                {
                    msg.From = new MailAddress(mailFromUAT);
                }
                else
                {
                    if (adrFrom != null) msg.From = adrFrom;
                }
                if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
                if (isAllowed)
                {
                    msg.Subject = "Test Mail ";
                }
                msg.Subject += strSubject;
                msg.IsBodyHtml = true;
                msg.BodyEncoding = Encoding.UTF8;
                msg.Body = strDocumentText;

                //[001] code start
                //attach the header image
                //string strAttachmentPath = HttpContext.Current.Server.MapPath(strHeaderImage);
                string strAttachmentPath = strHeaderImage;
                //[001] code end
                string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
                string bothirl = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", strAttachmentPath, sasURL, "docheaders");
                Uri blobUri = new Uri(bothirl);
                CloudBlockBlob blob = new CloudBlockBlob(blobUri);
                if (blob.Exists())
                {
                    BuildSettings objSettings = new BuildSettings();

                    var stream = new MemoryStream();
                    blob.DownloadToStream(stream);
                    //stream.Seek(0, SeekOrigin.Begin);
                    stream.Position = 0;
                    ContentType content = new ContentType(MediaTypeNames.Image.Jpeg);
                    Attachment logo = new Attachment(stream, content);
                    // email.Image = new Attachment(stream, content);
                    // using (MemoryStream memoryStream = new MemoryStream())
                    // {
                    // MemoryStream memoryStream = new MemoryStream();
                    // blob.DownloadToStream(memoryStream);

                    // System.Net.Mime.ContentType ct = new System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Image.Jpeg);

                    logo.ContentDisposition.Inline = true;
                    logo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                    msg.Attachments.Add(logo);
                    logo.ContentId = strHeaderImageID;
                    //logo.ContentType.MediaType = "image/";
                    // logo.ContentType.Name = Path.GetFileName(strAttachmentPath);
                    logo.ContentType.Name = strAttachmentPath;
                    //  }
                }

                //attach the signature image
                //[001] code start
                string strSignaturePath = strSignatureImage;// HttpContext.Current.Server.MapPath(strSignatureImage);
                //[001] code end
                //  string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
                if (!string.IsNullOrEmpty(strSignaturePath))
                {
                    string blobSignatureURL = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", strSignaturePath, sasURL, "signature");
                    Uri blobUri_Sig = new Uri(blobSignatureURL);
                    CloudBlockBlob blob_sig = new CloudBlockBlob(blobUri_Sig);

                    if (blob_sig.Exists())
                    {
                        // using (MemoryStream memoryStream = new MemoryStream())
                        // {
                        // MemoryStream memoryStreamSig = new MemoryStream();
                        // blob_sig.DownloadToStream(memoryStreamSig);
                        var memoryStreamSig = new MemoryStream();
                        blob_sig.DownloadToStream(memoryStreamSig);
                        //stream.Seek(0, SeekOrigin.Begin);
                        memoryStreamSig.Position = 0;
                        ContentType content = new ContentType(MediaTypeNames.Image.Jpeg);
                        Attachment signaturelogo = new Attachment(memoryStreamSig, content);
                        //System.Net.Mime.ContentType ctsig = new System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Image.Jpeg);
                        BuildSettings objSettings = new BuildSettings();
                        // Attachment signaturelogo = new Attachment(strSignaturePath);
                        // Attachment signaturelogo = new Attachment(memoryStreamSig, ctsig);
                        signaturelogo.ContentDisposition.Inline = true;
                        signaturelogo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                        msg.Attachments.Add(signaturelogo);
                        signaturelogo.ContentId = strSignatureImageID;
                        //logo.ContentType.MediaType = "image/";
                        // signaturelogo.ContentType.Name = Path.GetFileName(strSignaturePath);
                        signaturelogo.ContentType.Name = strSignaturePath;
                        // }
                    }
                }

                string strTermConditionPath = strTermConditionImage;// HttpContext.Current.Server.MapPath(strSignatureImage);
                //[001] code end
                //Espire: 30 June 20: In case terms and condition not available in image formate then attached the PDF
                if (!string.IsNullOrEmpty(strTermConditionPath))
                {
                    string blobTermsURL = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", strTermConditionPath, sasURL, "terms");
                    Uri blobUri_Terms = new Uri(blobTermsURL);
                    CloudBlockBlob blob_Terms = new CloudBlockBlob(blobUri_Terms);

                    if (blob_Terms.Exists())
                    {
                        // using (MemoryStream memoryStream = new MemoryStream())
                        //{
                        MemoryStream memoryStream_Terms = new MemoryStream();
                        blob_Terms.DownloadToStream(memoryStream_Terms);
                        memoryStream_Terms.Position = 0;
                        System.Net.Mime.ContentType ctTerms = new System.Net.Mime.ContentType(System.Net.Mime.MediaTypeNames.Image.Jpeg);
                        BuildSettings objSettings = new BuildSettings();
                        Attachment termslogo = new Attachment(memoryStream_Terms, ctTerms);
                        termslogo.ContentDisposition.Inline = true;
                        termslogo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                        msg.Attachments.Add(termslogo);
                        termslogo.ContentId = strTermConditionImageID;
                        //logo.ContentType.MediaType = "image/";
                        //termslogo.ContentType.Name = Path.GetFileName(strTermConditionPath);
                        termslogo.ContentType.Name = strTermConditionPath;
                        //}
                    }
                }
                else
                {
                    if (!string.IsNullOrEmpty(strTermsPDFPath))
                    {
                        string blobTermsPDFURL = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", strTermsPDFPath, sasURL, "terms");
                        Uri blobUri_TermsPDF = new Uri(blobTermsPDFURL);
                        CloudBlockBlob blob_TermsPDF = new CloudBlockBlob(blobUri_TermsPDF);

                        if (blob_TermsPDF.Exists())
                        {
                            Attachment pdf = null;
                            WebClient User = new WebClient();
                            blob_TermsPDF.FetchAttributes();//Fetch blob's properties
                            Byte[] FileBuffer = new byte[blob_TermsPDF.Properties.Length];

                            //Byte[] FileBuffer = User.DownloadData(strTermsPDFPath);
                            Stream stream = new MemoryStream(FileBuffer);
                            ContentType ct = new ContentType(MediaTypeNames.Application.Pdf);
                            pdf = new Attachment(stream, ct);
                            pdf.ContentType.MediaType = MediaTypeNames.Application.Pdf;
                            msg.Attachments.Add(pdf);
                            pdf.ContentType.Name = "Terms_conditions.PDF";// Path.GetFileName(strTermsPDFPath);
                            User.Dispose();
                        }
                    }
                }
                //send mail
                SmtpClient client = new SmtpClient();

                if (allowOnProd)
                {
                    client.Send(msg);
                }
                else if (isAllowed)
                {
                    SendMailOnUAT(msg, client);
                }
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in sending mail at method SendUserMailDocument overload : " + ex.Message);
                blnOK = false;
            }
            return blnOK;
        }

        //[001] code start

        //public static bool SendUserMailDocumentWithPDF(string strSubject, MailAddressCollection adrTo, MailAddress adrFrom, MailAddressCollection adrCC, MailAddressCollection adrBCC, MailAddress adrReplyTo, string strPDFPath, string strBodyText)
        //{
        //    bool blnOK = true;
        //    Attachment pdf = null;
        //    try
        //    {
        //        System.Net.Mail.MailMessage msg = new System.Net.Mail.MailMessage();
        //        foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
        //        if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
        //        if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
        //        if (adrFrom != null) msg.From = adrFrom;
        //        if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
        //        msg.Subject = strSubject;
        //        msg.IsBodyHtml = true;
        //        msg.BodyEncoding = Encoding.UTF8;
        //        //msg.Body = strDocumentText;
        //        msg.Body = strBodyText;// MailTemplateManager.GetMessage_CertificateOfConformance(strSubject, SessionManager.LoginFullName);

        //        //attach the header image
        //        // string strAttachmentPath = HttpContext.Current.Server.MapPath(strHeaderImage);
        //        //if (File.Exists(strAttachmentPath))
        //        //{
        //        //    BuildSettings objSettings = new BuildSettings();
        //        //    Attachment logo = new Attachment(strAttachmentPath);
        //        //    logo.ContentDisposition.Inline = true;
        //        //    logo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
        //        //    msg.Attachments.Add(logo);
        //        //    logo.ContentId = strHeaderImageID;
        //        //    //logo.ContentType.MediaType = "image/";
        //        //    logo.ContentType.Name = Path.GetFileName(strAttachmentPath);
        //        //}

        //        //attached a pdf email



        //        //System.IO.MemoryStream ms = new System.IO.MemoryStream();
        //        //System.IO.StreamWriter writer = new System.IO.StreamWriter(ms);
        //        // writer.Write(strPDFPath);
        //        //  writer.Write("Hello its my sample file");
        //        //writer.Flush();
        //        // writer.Dispose();
        //        // 

        //        WebClient User = new WebClient();
        //        Byte[] FileBuffer = User.DownloadData(strPDFPath);
        //        Stream stream = new MemoryStream(FileBuffer);
        //        ContentType ct = new ContentType(MediaTypeNames.Application.Pdf);
        //        pdf = new Attachment(stream, ct);
        //        pdf.ContentType.MediaType = MediaTypeNames.Application.Pdf;
        //        msg.Attachments.Add(pdf);
        //        pdf.ContentType.Name = Path.GetFileName(strPDFPath);


        //        //send mail
        //        SmtpClient client = new SmtpClient();
        //        client.Send(msg);



        //        msg.Attachments.Clear();
        //        msg.Dispose();
        //        stream.Dispose();
        //        ct = null;

        //        if (File.Exists(strPDFPath))
        //            File.Delete(strPDFPath);
        //    }
        //    catch
        //    {
        //        blnOK = false;
        //    }
        //    return blnOK;
        //}
        public static bool SendUserMailDocumentWithPDF(string strSubject, MailAddressCollection adrTo, MailAddress adrFrom, MailAddressCollection adrCC, MailAddressCollection adrBCC, MailAddress adrReplyTo, string strPDFPath, string FileName, string strBodyText)
        {
            bool blnOK = true;
            Attachment pdf = null;
            try
            {
                bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
                bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
                var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);

                System.Net.Mail.MailMessage msg = new System.Net.Mail.MailMessage();
                foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
                if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
                if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
                if (isAllowed)
                {
                    msg.From = new MailAddress(mailFromUAT);
                }
                else
                {
                    if (adrFrom != null)
                        msg.From = adrFrom;
                }
                if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
                if (isAllowed)
                {
                    msg.Subject = "Test Mail ";
                }
                msg.Subject += strSubject;
                msg.IsBodyHtml = true;
                msg.BodyEncoding = Encoding.UTF8;
                //msg.Body = strDocumentText;
                msg.Body = strBodyText;// MailTemplateManager.GetMessage_CertificateOfConformance(strSubject, SessionManager.LoginFullName);

                //attach the header image
                // string strAttachmentPath = HttpContext.Current.Server.MapPath(strHeaderImage);
                //if (File.Exists(strAttachmentPath))
                //{
                //    BuildSettings objSettings = new BuildSettings();
                //    Attachment logo = new Attachment(strAttachmentPath);
                //    logo.ContentDisposition.Inline = true;
                //    logo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                //    msg.Attachments.Add(logo);
                //    logo.ContentId = strHeaderImageID;
                //    //logo.ContentType.MediaType = "image/";
                //    logo.ContentType.Name = Path.GetFileName(strAttachmentPath);
                //}

                //attached a pdf email



                //System.IO.MemoryStream ms = new System.IO.MemoryStream();
                //System.IO.StreamWriter writer = new System.IO.StreamWriter(ms);
                // writer.Write(strPDFPath);
                //  writer.Write("Hello its my sample file");
                //writer.Flush();
                // writer.Dispose();
                // 

                WebClient User = new WebClient();
                Byte[] FileBuffer = User.DownloadData(strPDFPath);
                Stream stream = new MemoryStream(FileBuffer);
                ContentType ct = new ContentType(MediaTypeNames.Application.Pdf);
                pdf = new Attachment(stream, ct);
                pdf.ContentType.MediaType = MediaTypeNames.Application.Pdf;
                msg.Attachments.Add(pdf);
                pdf.ContentType.Name = FileName;// Path.GetFileName(strPDFPath);


                //send mail
                SmtpClient client = new SmtpClient();

                if (allowOnProd)
                {
                    client.Send(msg);
                }
                else if (isAllowed)
                {
                    SendMailOnUAT(msg, client);
                }

                msg.Attachments.Clear();
                msg.Dispose();
                stream.Dispose();
                User.Dispose();
                ct = null;

                if (File.Exists(strPDFPath))
                    File.Delete(strPDFPath);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in sending mail at method SendUserMailDocumentWithPDF : " + ex.Message);
                blnOK = false;
            }
            return blnOK;
        }

        public static bool SendUserMailDocumentWithZip(string strSubject,
                                                        MailAddressCollection adrTo,
                                                        MailAddress adrFrom,
                                                        MailAddressCollection adrCC,
                                                        MailAddressCollection adrBCC,
                                                        MailAddress adrReplyTo,
                                                        string zipFilePath,
                                                        string FileName,
                                                        string strBodyText)
        {
            bool blnOK = true;
            MailMessage msg = new MailMessage();
            Attachment attachment = new Attachment(zipFilePath);
            try
            {
                bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
                bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
                var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);

                foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
                if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
                if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
                if (isAllowed)
                {
                    msg.From = new MailAddress(mailFromUAT);
                }
                else
                {
                    if (adrFrom != null)
                        msg.From = adrFrom;
                }
                if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
                if (isAllowed)
                {
                    msg.Subject = "Test Mail ";
                }
                msg.Subject += strSubject;
                msg.IsBodyHtml = true;
                msg.BodyEncoding = Encoding.UTF8;
                msg.Body = strBodyText;

                //attach zip file
                attachment.ContentType.MediaType = MediaTypeNames.Application.Zip;
                attachment.ContentType.Name = FileName + ".zip";
                msg.Attachments.Add(attachment);

                //send mail
                SmtpClient client = new SmtpClient();

                if (allowOnProd)
                {
                    client.Send(msg);
                }
                else if (isAllowed)
                {
                    SendMailOnUAT(msg, client);
                }
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in sending mail at method SendUserMailDocumentWithXML : " + ex.Message);
                blnOK = false;
            }
            finally
            {
                msg.Attachments.Clear();
                attachment.Dispose();
                msg.Dispose();

                if (File.Exists(zipFilePath))
                    File.Delete(zipFilePath);
            }
            return blnOK;
        }

        //[001] code end

        public static MailAddress GetEmailAddress(EmailAddressList enmAddress)
        {
            MailAddress ma = new MailAddress("<EMAIL>");
            switch (enmAddress)
            {
                case EmailAddressList.System:
                    //do nothing, use the default
                    break;
                case EmailAddressList.Test:
                    ma = new MailAddress("<EMAIL>");
                    break;
                case EmailAddressList.CurrentUser:
                    ma = new MailAddress(SessionManager.LoginEmail);
                    break;
            }
            return ma;
        }

        private static Attachment TextToAttachment(string strText, string strDocumentTitle)
        {
            byte[] bytData = Encoding.Unicode.GetBytes(strText);
            MemoryStream ms = new MemoryStream(bytData);
            Attachment att = new Attachment(ms, string.Format("{0}.txt", strDocumentTitle), "text/plain");
            ms = null; bytData = null;
            return att;
        }

        #region Enumerations

        public enum EmailAddressList
        {
            System,
            CurrentUser,
            Test
        }

        #endregion


        public static bool SendAttachedDocumentToUsers(string strSubject, MailAddressCollection adrTo, MailAddress adrFrom, MailAddressCollection adrCC, MailAddressCollection adrBCC, MailAddress adrReplyTo, string strDocumentText, string strHeaderImage, string strHeaderID)
        {
            bool blnOK = true;
            try
            {
                bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
                bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
                var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);

                System.Net.Mail.MailMessage msg = new System.Net.Mail.MailMessage();
                foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
                if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
                if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
                if (isAllowed)
                {
                    msg.From = new MailAddress(mailFromUAT);
                }
                else
                {
                    if (adrFrom != null) msg.From = adrFrom;
                }
                if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
                if (isAllowed)
                {
                    msg.Subject = "Test Mail ";
                }
                msg.Subject += strSubject;
                msg.BodyEncoding = Encoding.UTF8;
                //msg.Body = strDocumentText;                
                msg.IsBodyHtml = true;
                msg.BodyEncoding = Encoding.UTF8;
                msg.Body = strDocumentText;
                //attach the header image
                string strAttachmentPath = HttpContext.Current.Server.MapPath("~/User/UploadTemp/" + strHeaderImage);
                if (File.Exists(strAttachmentPath))
                {
                    BuildSettings objSettings = new BuildSettings();
                    Attachment csvFile = new Attachment(strAttachmentPath);
                    csvFile.ContentDisposition.Inline = true;
                    csvFile.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                    msg.Attachments.Add(csvFile);
                    csvFile.ContentId = strHeaderID;
                    csvFile.ContentType.Name = Path.GetFileName(strAttachmentPath);
                }
                //send mail
                SmtpClient client = new SmtpClient();

                if (allowOnProd)
                {
                    client.Send(msg);
                }
                else if (isAllowed)
                {
                    SendMailOnUAT(msg, client);
                }

                msg.Attachments.Clear();
                msg.Dispose();

                DeleteOldFile(HttpContext.Current.Server.MapPath("~/User/UploadTemp"));
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in sending mail at method SendAttachedDocumentToUsers : " + ex.Message);
                blnOK = false;
            }
            return blnOK;
        }

        public static void DeleteOldFile(string strFolderPath)
        {
            foreach (var item in Directory.GetFiles(strFolderPath, "*.csv"))
            {
                try
                {
                    FileInfo info = new FileInfo(item);
                    // Delete the file only if it's at least a day old
                    if (info.CreationTime < DateTime.Now.AddDays(-1))
                        File.Delete(item);
                }
                catch (IOException ex)
                {
                    EventLog log = new EventLog();
                    log.Log = "Application";
                    log.Source = "ImageHelper";

                    log.WriteEntry("Error trying to delete file: " + ex.ToString(), EventLogEntryType.Error);
                    log.Close();
                }
                catch (Exception exGeneral)
                {
                    EventLog log = new EventLog();
                    log.Log = "Application";
                    log.Source = "ImageHelper";

                    log.WriteEntry("Error trying to delete file: " + exGeneral.ToString(), EventLogEntryType.Error);
                    log.Close();
                }
            }

        }

        public static string BodyMailMessage(string bodyMessage, string SupplierName)
        {
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.Append("Dear " + SupplierName + ",");
            strBuilder.Append("<br/><br/>");
            strBuilder.Append(bodyMessage);
            ////strBuilder.Append("Kindly find the attached " + strTitle + " regarding our requirements.<br/>");
            ////strBuilder.Append("Please update the price column of the sheet & send us back for further processing.");
            strBuilder.Append("<br/><br/>");
            strBuilder.Append("Thanks,<br/>");
            strBuilder.Append(SessionManager.LoginFullName + ",");
            return strBuilder.ToString();
        }

        public static string BodyMailMessageForAssignToMe(string BOMIdList, string selectedUserName)
        {
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.Append("Following HUBRFQ has been assigned to " + selectedUserName + " on date: " + Functions.FormatDate(Functions.GetUKLocalTime()) + ".<br/><br/>");
            //string strMessageTemp = string.Format("HUBRFQ : <a onclick=\"{0}\" href=\"javascript:void(0);\">\"NPR</a>:", string.Format("$RGT_openNPRWindow({0} ,{1})", goodsInLineId, nprId));
            strBuilder.Append("HUBRFQ : " + BOMIdList);
            return strBuilder.ToString();
        }
        public static bool SendUserMailDocumentWithExcelAndPDF(string strSubject, MailAddressCollection adrTo, MailAddress adrFrom, MailAddressCollection adrCC, MailAddressCollection adrBCC, MailAddress adrReplyTo, string strPDFPath, string FileName, string strBodyText, string strExcelPath, string ExcelFileName)
        {
            bool blnOK = true;
            Attachment pdf = null;
            //bool allowOnProd = true;
            try
            {
                bool isAllowed = BLL.Setting.IsEmailAllowOnUAT(SessionManager.ClientID);
                bool allowOnProd = Convert.ToBoolean(System.Configuration.ConfigurationManager.AppSettings["DefaultProdEmail"]);
                var mailFromUAT = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_From_UAT"]);

                System.Net.Mail.MailMessage msg = new System.Net.Mail.MailMessage();
                foreach (MailAddress ad in adrTo) { msg.To.Add(ad); }
                if (adrCC != null) foreach (MailAddress ad in adrCC) { msg.CC.Add(ad); }
                if (adrBCC != null) foreach (MailAddress ad in adrBCC) { msg.Bcc.Add(ad); }
                if (isAllowed)
                {
                    msg.From = new MailAddress(mailFromUAT);
                }
                else
                {
                    if (adrFrom != null)
                        msg.From = adrFrom;
                }
                if (adrReplyTo != null) msg.ReplyTo = adrReplyTo;
                if (isAllowed)
                {
                    msg.Subject = "Test Mail ";
                }
                msg.Subject += strSubject;
                msg.IsBodyHtml = true;
                msg.BodyEncoding = Encoding.UTF8;
                //msg.Body = strDocumentText;
                msg.Body = strBodyText;//MailTemplateManager.GetMessage_CertificateOfConformance(strSubject, SessionManager.LoginFullName);

                //attach the header image
                // string strAttachmentPath = HttpContext.Current.Server.MapPath(strHeaderImage);
                //if (File.Exists(strAttachmentPath))
                //{
                //    BuildSettings objSettings = new BuildSettings();
                //    Attachment logo = new Attachment(strAttachmentPath);
                //    logo.ContentDisposition.Inline = true;
                //    logo.ContentDisposition.DispositionType = DispositionTypeNames.Inline;
                //    msg.Attachments.Add(logo);
                //    logo.ContentId = strHeaderImageID;
                //    //logo.ContentType.MediaType = "image/";
                //    logo.ContentType.Name = Path.GetFileName(strAttachmentPath);
                //}

                //attached a pdf email



                //System.IO.MemoryStream ms = new System.IO.MemoryStream();
                //System.IO.StreamWriter writer = new System.IO.StreamWriter(ms);
                // writer.Write(strPDFPath);
                //  writer.Write("Hello its my sample file");
                //writer.Flush();
                // writer.Dispose();
                // 

                WebClient User = new WebClient();
                Byte[] FileBuffer = User.DownloadData(strPDFPath);
                Stream stream = new MemoryStream(FileBuffer);
                ContentType ct = new ContentType(MediaTypeNames.Application.Pdf);
                pdf = new Attachment(stream, ct);
                pdf.ContentType.MediaType = MediaTypeNames.Application.Pdf;
                msg.Attachments.Add(pdf);
                pdf.ContentType.Name = FileName;

                // Excel File attachment
                WebClient Excel = new WebClient();
                Byte[] FileBufferExcel = Excel.DownloadData(strExcelPath);
                Stream streamExcel = new MemoryStream(FileBufferExcel);
                ContentType exceltype = new ContentType("application/vnd.ms-excel");
                Attachment attachmentExcel = new Attachment(streamExcel, exceltype);
                attachmentExcel.ContentType.MediaType = "application/vnd.ms-excel";
                attachmentExcel.ContentType.Name = ExcelFileName;
                msg.Attachments.Add(attachmentExcel);


                //send mail
                SmtpClient client = new SmtpClient();

                if (allowOnProd)
                {
                    client.Send(msg);
                }
                else if (isAllowed)
                {
                    SendMailOnUAT(msg, client);
                }

                msg.Attachments.Clear();
                msg.Dispose();
                stream.Dispose();
                User.Dispose();
                ct = null;

                if (File.Exists(strPDFPath))
                    File.Delete(strPDFPath);

                if (File.Exists(strExcelPath))
                    File.Delete(strExcelPath);
            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error in sending mail at method SendUserMailDocumentWithPDF : " + ex.Message);
                blnOK = false;
            }
            return blnOK;
        }

        public static string BodyMailMessageForAssignToMeAS6081(string BOMIdList, string selectedUserName, string bomIds = "", string ByName = "")
        {
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.Append("Dear " + selectedUserName + ",<br/><br/>Following HUBRFQ has been assigned to you on date: " + Functions.FormatDate(Functions.GetUKLocalTime()) + " by " + ByName + ".<br/><br/>");
            //string strMessageTemp = string.Format("HUBRFQ : <a onclick=\"{0}\" href=\"javascript:void(0);\">\"NPR</a>:", string.Format("$RGT_openNPRWindow({0} ,{1})", goodsInLineId, nprId));
            strBuilder.Append("<b>HUBRFQ : </b><br/>");
            string[] Ids = bomIds.Split(',');
            string[] Names = BOMIdList.Split(',');
            string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
            for (int i = 0; i < Ids.Length; i++)
            {
                strBuilder.Append("<a href='" + url + "Ord_BOMDetail.aspx?BOM=" + Ids[i] + " '>" + Names[i] + "</a>");
                if (i != (Ids.Length - 1))
                {
                    strBuilder.Append(", ");
                }
            }
            string msgFooter = "<br/><br />Please take action as soon as possible.<br />Kind Regards,<br />Global Trader";
            strBuilder.Append(msgFooter);
            return strBuilder.ToString();
        }
        public static string BodyMailMessageForRequesterToMeAS6081(string BOMIdList, string selectedUserName, string bomIds = "", string RequesterName = "")
        {
            StringBuilder strBuilder = new StringBuilder();
            strBuilder.Append("Dear " + RequesterName + ",<br/><br/>Following HUBRFQ has been assigned to " + selectedUserName + " on date: " + Functions.FormatDate(Functions.GetUKLocalTime()) + ".<br/><br/>");
            //string strMessageTemp = string.Format("HUBRFQ : <a onclick=\"{0}\" href=\"javascript:void(0);\">\"NPR</a>:", string.Format("$RGT_openNPRWindow({0} ,{1})", goodsInLineId, nprId));
            strBuilder.Append("<b>HUBRFQ : </b><br/>");
            string[] Ids = bomIds.Split(',');
            string[] Names = BOMIdList.Split(',');
            string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
            for (int i = 0; i < Ids.Length; i++)
            {
                strBuilder.Append("<a href='" + url + "Ord_BOMDetail.aspx?BOM=" + Ids[i] + " '>" + Names[i] + "</a>");
                if (i != (Ids.Length - 1))
                {
                    strBuilder.Append(", ");
                }
            }
            string msgFooter = "<br/><br />Please take action as soon as possible.<br />Kind Regards,<br />Global Trader";
            strBuilder.Append(msgFooter);

            return strBuilder.ToString();
        }

        private static void SendMailOnUAT(System.Net.Mail.MailMessage msg, SmtpClient client)
        {
            var mailTo = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["Email_To_UAT"]);
            if (String.IsNullOrEmpty(mailTo))
            {
                return;
            }

            var allowCCEmail = msg.CC.Contains(new MailAddress(mailTo));
            
            //Clear all Email
            msg.To.Clear();
            msg.CC.Clear();
            msg.Bcc.Clear();
            
            msg.To.Add(mailTo); // Only send to Email config on UAT Environment

            if (allowCCEmail)
            {
                msg.CC.Add(mailTo); // Only CC yo Email config on UAT Environment
            }

            client.Send(msg);
        }
    }
}
