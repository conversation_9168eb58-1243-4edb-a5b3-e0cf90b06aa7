//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class AllPurchaseOrdersDueIn : Base {

		protected SimpleDataTable _tblDueIn;
		protected PageHyperLink _lnkMore;

		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "AllPurchaseOrdersDueIn";
			base.OnInit(e);
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.AllPurchaseOrdersDueIn.AllPurchaseOrdersDueIn.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.AllPurchaseOrdersDueIn", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblDueIn", _tblDueIn.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlMore", FindContentControl("pnlMore").ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlDueIn", FindContentControl("pnlDueIn").ClientID);
			_lnkMore.AddQueryStringVariable(QueryStringManager.QueryStringVariables.BypassSavedState, true);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblDueIn = (SimpleDataTable)FindContentControl("tblDueIn");
			_tblDueIn.Columns.Add(new SimpleDataColumn("PurchaseOrder", Unit.Pixel(65)));
			_tblDueIn.Columns.Add(new SimpleDataColumn("Supplier"));
			_tblDueIn.Columns.Add(new SimpleDataColumn("DueDate", Unit.Pixel(75)));
			_lnkMore = (PageHyperLink)FindContentControl("lnkMore");
		}
	}
}