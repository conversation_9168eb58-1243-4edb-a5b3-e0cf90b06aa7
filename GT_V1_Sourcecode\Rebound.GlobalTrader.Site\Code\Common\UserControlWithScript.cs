using System;
using System.Collections.Generic;
using System.Text;
using System.Reflection;
using System.Web.UI;
using System.Web;

namespace Rebound.GlobalTrader.Site {
	public class UserControlWithScript : System.Web.UI.UserControl, IScriptControl, INamingContainer {

		protected ScriptManager _sm;

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}


		#region IScriptControl Members

		/// <summary>
		/// GetScriptDescriptors
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ScriptDescriptor> GetScriptDescriptors() { return null; }

		/// <summary>
		/// GetScriptReferences
		/// </summary>
		/// <returns></returns>
		public IEnumerable<ScriptReference> GetScriptReferences() { return null; }

		#endregion
	}
}