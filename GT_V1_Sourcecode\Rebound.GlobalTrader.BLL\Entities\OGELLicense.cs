﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL {
    public partial class OGELLicense : BizObject
    {		
        #region Properties

        protected static DAL.OGELLicenseElement Settings
        {
            get { return Globals.Settings.OGELLicense; }
        }
        /// <summary>
        /// Ogel Id
        /// </summary>
        public System.Int32 OgelId { get; set; }
        /// <summary>
        /// Ogel Number
        /// </summary>
        public System.String OgelNumber { get; set; }
        /// <summary>
        /// Description
        /// </summary>
        public System.String Description { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// Inactive
        /// </summary>
        public System.Boolean Inactive { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }
        /// <summary>
        /// EmployeeName
        /// </summary>
        public System.String EmployeeName { get; set; }

        #endregion

        #region Methods

        /// <summary>
        /// Calls [usp_insert_OGELLicense]
        /// </summary>
        /// <param name="ogelNumber"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static int Insert(System.String ogelNumber, System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.OGELLicense.Insert(ogelNumber, description, inActive, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_update_OGELLicense]
        /// </summary>
        /// <param name="ogelId"></param>
        /// <param name="ogelNumber"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static int Update(System.Int32? ogelId, System.String ogelNumber, System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.OGELLicense.Update(ogelId, ogelNumber, description, inActive, updatedBy);
        }

        /// <summary>
        /// GetListForClient
        /// Calls [usp_selectAll_OGELLicenses]
        /// </summary>
        public static List<OGELLicense> GetList()
        {
            List<OGELLicenseDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.OGELLicense.GetList();
            if (lstDetails == null)
            {
                return new List<OGELLicense>();
            }
            else
            {
                List<OGELLicense> lst = new List<OGELLicense>();
                foreach (OGELLicenseDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.OGELLicense obj = new Rebound.GlobalTrader.BLL.OGELLicense();
                    obj.OgelId = objDetails.OgelId;
                    obj.OgelNumber = objDetails.OgelNumber;
                    obj.Description = objDetails.Description;
                    obj.Inactive = objDetails.Inactive;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.EmployeeName = objDetails.EmployeeName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        #endregion
    }
}