﻿
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_AddExpedite = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_AddExpedite.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intBOMID = -1;
    this._ReqIds = "";
    this._intBOMNo = -1;
    this._RequestToPOHubBy = -1;
    this._UpdateByPH = -1;
    this._BomName = null;
    this._BomCompanyNo = -1;
    this._intContact2No = -1;
    this._reqSalespeson = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_AddExpedite.prototype = {

    get_intBOMID: function () { return this._intBOMID; }, set_intBOMID: function (value) { if (this._intBOMID !== value) this._intBOMID = value; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_AddExpedite.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this._ctlMail = $find(this.getField("dllCCUsersForHUBRFQ").ID);
            this._ctlMail._ctlRelatedForm = this;
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this.getFieldDropDownData("ctlSendToGroup");
        }
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intBOMID = null;
        this._ctlMail = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_AddExpedite.callBaseMethod(this, "dispose");
    },


    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        obj.set_DataAction("SaveExpedite");
        obj.addParameter("id", this._intBOMID);
        obj.addParameter("AddNotes", this.getFieldValue("ctlExpediteNotes"));
        obj.addParameter("SendToGroup", this.getFieldValue("ctlSendToGroup"));
        obj.addParameter("CCUserId", $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));
        obj.addParameter("ReqIds", $R_FN.arrayToSingleString(this._ReqIds, ","));
        obj.addParameter("UpdateByPH", this._UpdateByPH);
        obj.addParameter("RequestToPOHubBy", this._RequestToPOHubBy);
        obj.addParameter("HUBRFQName", this._BomName);
        obj.addParameter("HUBRFQCompanyNo", this._BomCompanyNo);
        obj.addParameter("Contact2No", this._intContact2No);
        obj.addParameter("ReqsalesPerson", this._reqSalespeson);
        obj.addDataOK(Function.createDelegate(this, this.saveAddComplete));
        obj.addError(Function.createDelegate(this, this.saveAddError));

        obj.addTimeout(Function.createDelegate(this, this.saveAddError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveAddError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveAddComplete: function (args) {
        if (args._result.Result > 0) {
            this.onSaveComplete();
        } else {
            if (args._result.Message) this._strErrorMessage = args._result.Message;
            this.onSaveError();
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_AddExpedite.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_AddExpedite", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
