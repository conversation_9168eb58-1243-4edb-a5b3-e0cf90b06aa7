Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.initializeBase(this,[n]);this._tblMyQualityGIQueries=null};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.prototype={get_tblMyQualityGIQueries:function(){return this._tblMyQualityGIQueries},set_tblMyQualityGIQueries:function(n){this._tblMyQualityGIQueries!==n&&(this._tblMyQualityGIQueries=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblMyQualityGIQueries&&this._tblMyQualityGIQueries.dispose(),this._tblMyQualityGIQueries=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this._tblMyQualityGIQueries.show(!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.callBaseMethod(this,"setupLoadingState")},getData:function(){this.setupLoadingState();this._tblMyQualityGIQueries.clearTable();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyQualityGIQueries");n.set_DataObject("MyQualityGIQueries");n.set_DataAction("GetData");n._intTimeOutMiliseconds=1;n.addParameter("rowcount",this._intRowCount);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,r,t,u;for(this.showNoneFoundOrContent(i.Count),r=0;r<i.Items.length;r++)t=i.Items[r],u=[$RGT_nubButton_QueryGILine(t.GoodsInId,t.GoodsInLineId,t.GINumber,!0),$RGT_nubButton_SalesOrder(t.SalesOrderNo,t.SalesOrderNumber),$R_FN.setCleanTextValue(t.DateSent),$R_FN.setCleanTextValue(t.Status)],this._tblMyQualityGIQueries.addRow(u,null),t=null;this._tblMyQualityGIQueries.show(i.Count>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyQualityGIQueries",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);