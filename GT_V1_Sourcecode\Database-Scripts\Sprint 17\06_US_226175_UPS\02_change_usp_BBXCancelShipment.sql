﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_BBXCancelShipment]    Script Date: 12/18/2024 2:10:04 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-211896]		Phuc Hoang			18-Sep-2024		UPDATE			[PROD Bug] DHL - Rebound API invoice BBX
[US-226175]     Phuc Hoang		    18-Dec-2024		UPDATE		    [PROD Bug] UPS - Hanging Shipments
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_BBXCancelShipment]                                      
(                                     
  @vInvoiceIDs Varchar(Max),                    
  @vClientID int ,                                      
  @vRetMsg Varchar(100) Out,                                      
  @vIsSuccess bit Out                                      
)                                      
/*                                      
CREATED BY : Manoj KUMAR                                          
CREATED ON : 04-01-2023                                          
PURPOSE    : To cancel/void shipment                                    
*/                                      
AS                                              
BEGIN                                            
  BEGIN TRY                                      
  Update tbInvoice Set                                     
        [AirWayBill] = NULL,                            
        [ShippingCost] = ShippingSurchargeValue,                                   
        [Boxes] = NULL,                                   
        [Weight] = NULL,                                   
        [DimensionalWeight] = NULL, 
		[DHLParentInvoiceNo] = NULL,
        [IsUpsInvoiceExported] = 0,
		[IsGTServiceUpdated] = 0,
        [Notes]=CASE WHEN (CHARINDEX('|#',isnull(Notes,''))>0) AND   (CHARINDEX('#|',isnull(Notes,'')) >0)                       
                THEN STUFF(isnull(Notes,''), CHARINDEX('|#',isnull(Notes,'')), (CHARINDEX('#|',isnull(Notes,''))-CHARINDEX('|#',isnull(Notes,'')))+2, '')                       
                ELSE isnull(Notes,'')  END                      
  Where InvoiceNumber In (select InvoiceNo from ChildInvoiceAWBDHL where ParentInvoiceAWBDHLId in (          
  select ParentInvoiceAWBDHLId from ChildInvoiceAWBDHL where InvoiceNo in (select String from dbo.[ufn_splitString](@vInvoiceIDs,',')) and IsActive=1))                      
  and ClientNo = @vClientID 
          
  Update ParentInvoiceAWBDHL set IsActive=0 where ParentInvoiceAWBDHLId in 
  (select ch.ParentInvoiceAWBDHLId from ChildInvoiceAWBDHL ch 
 join (select String from dbo.[ufn_splitString](@vInvoiceIDs,',')) tt on ch.InvoiceNo=tt.String where ch.IsActive=1 AND ch.ClientId = @vClientID)        
            
 select String from dbo.[ufn_splitString](@vInvoiceIDs,','); 
        
  Update ChildInvoiceAWBDHL set IsActive=0 where ParentInvoiceAWBDHLId in 
     (select ch.ParentInvoiceAWBDHLId from ChildInvoiceAWBDHL ch 
 join (select String from dbo.[ufn_splitString](@vInvoiceIDs,',')) tt on ch.InvoiceNo=tt.String where ch.IsActive=1 AND ch.ClientId = @vClientID)        
     
 Update ChildInvoiceAWBDHL set sendStatus=0 where InvoiceNo in            
     (select ch.InvoiceNo from ChildInvoiceAWBDHL ch        
 join (select String from dbo.[ufn_splitString](@vInvoiceIDs,',')) tt on ch.InvoiceNo=tt.String where ch.sendStatus=1 AND ch.ClientId = @vClientID)    
        
                  
   -- Update invoice freight to same as shipping cost when shipping method                  
   -- (Match Shipping cost value to freight field) of this invoice is checked                   
    DECLARE @MyCursor CURSOR                  
    DECLARE @InvoiceID INT                
    SET @MyCursor = CURSOR FAST_FORWARD                    
    FOR                 
                    
    SELECT InvoiceId FROM tbInvoice WHERE InvoiceNumber In (select String from dbo.[ufn_splitString](@vInvoiceIDs,','))                       
    and ClientNo = @vClientID                 
                    
    OPEN @MyCursor 
   FETCH NEXT FROM @MyCursor 
   INTO @InvoiceID     
    WHILE @@FETCH_STATUS = 0        
      BEGIN  
  DECLARE @IsSameAsShipping BIT                  
                  
  SELECT @IsSameAsShipping = ISNULL(IsSameAsShipCost,0) FROM tbShipVia WHERE ShipViaId =(SELECT ShipViaNo FROM tbInvoice                   
     Where InvoiceId = @InvoiceID)                    
                                      
  IF @IsSameAsShipping = 1                  
  BEGIN                  
     UPDATE tbInvoice SET Freight=NULL WHERE InvoiceId = @InvoiceID           
  END                  
                     
          
        FETCH NEXT FROM @MyCursor                    
        INTO @InvoiceID                    
      END                    
    CLOSE @MyCursor                    
    DEALLOCATE @MyCursor           
                   
   -- End : 27 Jan 2015             
                                     
 IF @@ERROR > 0                              
 BEGIN              
  Set @vRetMsg = 'Shipment Cancellation Failed!'                                    
  Set @vIsSuccess = 0                                      
 END                                    
 ELSE                                
 BEGIN                                      
  Set @vRetMsg = 'Shipment Cancelled Successfully!'                                      
  Set @vIsSuccess = 1                                      
 END                                      
                    
  END TRY                                    
  BEGIN CATCH                                    
 Set @vRetMsg = ERROR_MESSAGE()                                    
 Set @vIsSuccess = 0                                    
  END CATCH                                    
                                            
END            
GO


