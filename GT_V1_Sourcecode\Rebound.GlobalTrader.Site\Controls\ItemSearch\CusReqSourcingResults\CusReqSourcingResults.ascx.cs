using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
	public partial class CustomerRequirementSourcingResults : Base {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			SetItemSearchType("CusReqSourcingResults");
			AddScriptReference("Controls.ItemSearch.CusReqSourcingResults.CusReqSourcingResults.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			ctlDesignBase.MakeChildControls();
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Requirement", "Supplier", Unit.Empty, true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("DateOffered", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Quantity", "Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("IsPoHub", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode), true));
			base.OnPreRender(e);
		}

	}
}