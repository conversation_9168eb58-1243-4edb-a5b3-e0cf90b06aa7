<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Abbreviation" xml:space="preserve">
    <value>Abbreviation</value>
  </data>
  <data name="Account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="AddressName" xml:space="preserve">
    <value>Address Name</value>
  </data>
  <data name="AdminSecurityGroupName" xml:space="preserve">
    <value>Administrators Security Group Name</value>
  </data>
  <data name="AdminUserName" xml:space="preserve">
    <value>Administrator User Name</value>
  </data>
  <data name="AirWayBill" xml:space="preserve">
    <value>Air Way Bill</value>
  </data>
  <data name="Allocated" xml:space="preserve">
    <value>Allocated</value>
  </data>
  <data name="AmountOwed" xml:space="preserve">
    <value>Amount Owed</value>
  </data>
  <data name="Anniversary" xml:space="preserve">
    <value>Anniversary</value>
  </data>
  <data name="ApplyDuty" xml:space="preserve">
    <value>Apply Duty?</value>
  </data>
  <data name="ApprovedCustomer" xml:space="preserve">
    <value>Approved Customer</value>
  </data>
  <data name="ApprovedSupplier" xml:space="preserve">
    <value>Approved Supplier</value>
  </data>
  <data name="AuthorisationHistory" xml:space="preserve">
    <value>Authorisation History</value>
  </data>
  <data name="AuthorisedBy" xml:space="preserve">
    <value>Authorised By</value>
  </data>
  <data name="Authoriser" xml:space="preserve">
    <value>Authoriser</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Available</value>
  </data>
  <data name="BackgroundImage" xml:space="preserve">
    <value>Background Image</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Balance</value>
  </data>
  <data name="BalanceWithBoth" xml:space="preserve">
    <value>Balance with this Order &amp;&amp; Posted Lines</value>
  </data>
  <data name="BalanceWithOpenOrders" xml:space="preserve">
    <value>Balance with all Posted Lines</value>
  </data>
  <data name="BalanceWithThisOrder" xml:space="preserve">
    <value>Balance with this Order</value>
  </data>
  <data name="BankFee" xml:space="preserve">
    <value>Bank Fee</value>
  </data>
  <data name="BillingAddress" xml:space="preserve">
    <value>Billing Address</value>
  </data>
  <data name="BillingAddressName" xml:space="preserve">
    <value>Billing Address Name</value>
  </data>
  <data name="BillToAddress" xml:space="preserve">
    <value>Bill to Address</value>
  </data>
  <data name="BillToAddressName" xml:space="preserve">
    <value>Billing Address Name</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>Birthday</value>
  </data>
  <data name="Boxes" xml:space="preserve">
    <value>Boxes</value>
  </data>
  <data name="Budget" xml:space="preserve">
    <value>Budget</value>
  </data>
  <data name="Buy" xml:space="preserve">
    <value>Buy</value>
  </data>
  <data name="Buyer" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="BuyerName" xml:space="preserve">
    <value>Buyer</value>
  </data>
  <data name="BuyPrice" xml:space="preserve">
    <value>Buying Price</value>
  </data>
  <data name="BuyShipVia" xml:space="preserve">
    <value>Buy Ship Via</value>
  </data>
  <data name="BuyShipViaAccount" xml:space="preserve">
    <value>Buy Ship Via Acct</value>
  </data>
  <data name="BuyShipViaNo" xml:space="preserve">
    <value>Buy Ship Via No</value>
  </data>
  <data name="Caption" xml:space="preserve">
    <value>Caption</value>
  </data>
  <data name="CeaseDate" xml:space="preserve">
    <value>Cease Date</value>
  </data>
  <data name="Charge" xml:space="preserve">
    <value>Charge</value>
  </data>
  <data name="Child1" xml:space="preserve">
    <value>Child 1</value>
  </data>
  <data name="Child1Birthday" xml:space="preserve">
    <value>Child 1 Birthday</value>
  </data>
  <data name="Child1Name" xml:space="preserve">
    <value>Child 1 Name</value>
  </data>
  <data name="Child1Sex" xml:space="preserve">
    <value>Child 1 Gender</value>
  </data>
  <data name="Child2" xml:space="preserve">
    <value>Child 2</value>
  </data>
  <data name="Child2Birthday" xml:space="preserve">
    <value>Child 2 Birthday</value>
  </data>
  <data name="Child2Name" xml:space="preserve">
    <value>Child 2 Name</value>
  </data>
  <data name="Child2Sex" xml:space="preserve">
    <value>Child 2 Gender</value>
  </data>
  <data name="Child3" xml:space="preserve">
    <value>Child 3</value>
  </data>
  <data name="Child3Birthday" xml:space="preserve">
    <value>Child 3 Birthday</value>
  </data>
  <data name="Child3Name" xml:space="preserve">
    <value>Child 3 Name</value>
  </data>
  <data name="Child3Sex" xml:space="preserve">
    <value>Child 3 Gender</value>
  </data>
  <data name="City" xml:space="preserve">
    <value>City</value>
  </data>
  <data name="CityTown" xml:space="preserve">
    <value>Town/City</value>
  </data>
  <data name="CloseAllAlternates" xml:space="preserve">
    <value>Close all alternates?</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="CloseReason" xml:space="preserve">
    <value>Close Reason</value>
  </data>
  <data name="Code" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="Code1" xml:space="preserve">
    <value>Code 1</value>
  </data>
  <data name="Code2" xml:space="preserve">
    <value>Code 2</value>
  </data>
  <data name="CodeN" xml:space="preserve">
    <value>Code N</value>
  </data>
  <data name="CodeY" xml:space="preserve">
    <value>Code Y</value>
  </data>
  <data name="CofCNotes" xml:space="preserve">
    <value>Certificate of Conformity Notes</value>
  </data>
  <data name="Company" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="CompanyName" xml:space="preserve">
    <value>Company</value>
  </data>
  <data name="CompanyType" xml:space="preserve">
    <value>Company Type</value>
  </data>
  <data name="Confirm" xml:space="preserve">
    <value>Please confirm</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="ContactFirstName" xml:space="preserve">
    <value>Contact First Name</value>
  </data>
  <data name="ContactLastName" xml:space="preserve">
    <value>Contact Last Name</value>
  </data>
  <data name="ContactLogItem" xml:space="preserve">
    <value>Contact Log Item</value>
  </data>
  <data name="ContactName" xml:space="preserve">
    <value>Contact Name</value>
  </data>
  <data name="Cost" xml:space="preserve">
    <value>Cost</value>
  </data>
  <data name="CountingMethod" xml:space="preserve">
    <value>Counting Method</value>
  </data>
  <data name="Country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="ShipToCountry" xml:space="preserve">
    <value>Ship to Country</value>
  </data>
  <data name="CountryOfManufacture" xml:space="preserve">
    <value>Country of Manufacture/Origin</value>
  </data>
  <data name="County" xml:space="preserve">
    <value>County</value>
  </data>
  <data name="CountyState" xml:space="preserve">
    <value>County/State</value>
  </data>
  <data name="CreditDate" xml:space="preserve">
    <value>Credit Date</value>
  </data>
  <data name="CreditDateFrom" xml:space="preserve">
    <value>Credit Date From</value>
  </data>
  <data name="CreditDateTo" xml:space="preserve">
    <value>Credit Date To</value>
  </data>
  <data name="CreditHistory" xml:space="preserve">
    <value>Credit History</value>
  </data>
  <data name="CreditLimit" xml:space="preserve">
    <value>Credit Limit</value>
  </data>
  <data name="CreditNo" xml:space="preserve">
    <value>Credit No</value>
  </data>
  <data name="CRMA" xml:space="preserve">
    <value>Customer RMA</value>
  </data>
  <data name="CRMADate" xml:space="preserve">
    <value>Customer RMA Date</value>
  </data>
  <data name="CRMANo" xml:space="preserve">
    <value>Customer RMA </value>
  </data>
  <data name="Currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="Current" xml:space="preserve">
    <value>Not Overdue</value>
  </data>
  <data name="CurrentRate" xml:space="preserve">
    <value>Current Rate</value>
  </data>
  <data name="CusReqNo" xml:space="preserve">
    <value>Customer Req No</value>
  </data>
  <data name="Customer" xml:space="preserve">
    <value>Customer</value>
  </data>
  <data name="CustomerDebit" xml:space="preserve">
    <value>Customer Debit No</value>
  </data>
  <data name="CustomerName" xml:space="preserve">
    <value>Customer Name</value>
  </data>
  <data name="CustomerNo" xml:space="preserve">
    <value>Customer No</value>
  </data>
  <data name="CustomerNotes" xml:space="preserve">
    <value>Notes to customer</value>
  </data>
  <data name="CustomerPart" xml:space="preserve">
    <value>Customer Part</value>
  </data>
  <data name="CustomerPartNo" xml:space="preserve">
    <value>Cust Part No</value>
  </data>
  <data name="CustomerPO" xml:space="preserve">
    <value>Customer PO</value>
  </data>
  <data name="CustomerPONo" xml:space="preserve">
    <value>Customer PO No</value>
  </data>
  <data name="CustomerPurchaseOrderNo" xml:space="preserve">
    <value>Customer PO</value>
  </data>
  <data name="CustomerRating" xml:space="preserve">
    <value>Customer Rating</value>
  </data>
  <data name="CustomerReturn" xml:space="preserve">
    <value>Customer Return No</value>
  </data>
  <data name="CustomerRMADate" xml:space="preserve">
    <value>Customer RMA Date</value>
  </data>
  <data name="CustomerRMADateFrom" xml:space="preserve">
    <value>Customer RMA Date From</value>
  </data>
  <data name="CustomerRMADateTo" xml:space="preserve">
    <value>Customer RMA Date To</value>
  </data>
  <data name="CustomerRMANo" xml:space="preserve">
    <value>Customer RMA</value>
  </data>
  <data name="DatabaseServer" xml:space="preserve">
    <value>Database Server</value>
  </data>
  <data name="DatabaseUsername" xml:space="preserve">
    <value>Database Username</value>
  </data>
  <data name="DatabaseUserPassword" xml:space="preserve">
    <value>Database Password</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="DateAuthorised" xml:space="preserve">
    <value>Date Authorised</value>
  </data>
  <data name="DateCode" xml:space="preserve">
    <value>Date Code</value>
  </data>
  <data name="DateDeliveredFrom" xml:space="preserve">
    <value>Date Delivered From</value>
  </data>
  <data name="DateDeliveredTo" xml:space="preserve">
    <value>Date Delivered To</value>
  </data>
  <data name="DateInspected" xml:space="preserve">
    <value>Date Released</value>
  </data>
  <data name="DateInvoiced" xml:space="preserve">
    <value>Date Invoiced</value>
  </data>
  <data name="DateInvoicedFrom" xml:space="preserve">
    <value>Date Invoiced From</value>
  </data>
  <data name="DateInvoicedTo" xml:space="preserve">
    <value>Date Invoiced To</value>
  </data>
  <data name="DateOrdered" xml:space="preserve">
    <value>Date Ordered</value>
  </data>
  <data name="DateOrderedFrom" xml:space="preserve">
    <value>Date Ordered From</value>
  </data>
  <data name="DateOrderedTo" xml:space="preserve">
    <value>Date Ordered To</value>
  </data>
  <data name="DatePromised" xml:space="preserve">
    <value>Date Promised</value>
  </data>
  <data name="DatePromisedFrom" xml:space="preserve">
    <value>Date Promised From</value>
  </data>
  <data name="DatePromisedTo" xml:space="preserve">
    <value>Date Promised To</value>
  </data>
  <data name="DateQuoted" xml:space="preserve">
    <value>Date Quoted</value>
  </data>
  <data name="DateQuotedFrom" xml:space="preserve">
    <value>Date Quoted From</value>
  </data>
  <data name="DateQuotedTo" xml:space="preserve">
    <value>Date Quoted To</value>
  </data>
  <data name="DateReceivedFrom" xml:space="preserve">
    <value>Date Received From</value>
  </data>
  <data name="DateReceivedTo" xml:space="preserve">
    <value>Date Received To</value>
  </data>
  <data name="DateRequired" xml:space="preserve">
    <value>Date Required</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="Days120" xml:space="preserve">
    <value>120+ days</value>
  </data>
  <data name="Days30" xml:space="preserve">
    <value>30 - 59 days</value>
  </data>
  <data name="Days60" xml:space="preserve">
    <value>60 - 89 days</value>
  </data>
  <data name="Days90" xml:space="preserve">
    <value>90 - 119 days</value>
  </data>
  <data name="DebitDate" xml:space="preserve">
    <value>Debit Date</value>
  </data>
  <data name="DebitDateFrom" xml:space="preserve">
    <value>Debit Date From</value>
  </data>
  <data name="DebitDateTo" xml:space="preserve">
    <value>Debit Date To</value>
  </data>
  <data name="DebitNo" xml:space="preserve">
    <value>Debit No</value>
  </data>
  <data name="DefaultContactNo" xml:space="preserve">
    <value>Default Contact</value>
  </data>
  <data name="DefaultDivisionName" xml:space="preserve">
    <value>Default Division Name</value>
  </data>
  <data name="DefaultHomepageTab" xml:space="preserve">
    <value>Default Homepage Tab</value>
  </data>
  <data name="DefaultLanguage" xml:space="preserve">
    <value>Default Language</value>
  </data>
  <data name="DefaultListPageSize" xml:space="preserve">
    <value>Default Browse Page Size</value>
  </data>
  <data name="DefaultListPageView" xml:space="preserve">
    <value>Default Browse Page View</value>
  </data>
  <data name="DefaultShippingAccountNo" xml:space="preserve">
    <value>Default Shipping Account No</value>
  </data>
  <data name="DefaultShipVia" xml:space="preserve">
    <value>Default Ship Via</value>
  </data>
  <data name="DefaultTeamName" xml:space="preserve">
    <value>Default Team Name</value>
  </data>
  <data name="DeliverByDate" xml:space="preserve">
    <value>Deliver By Date</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="DeliveryDateFrom" xml:space="preserve">
    <value>Delivery Date From</value>
  </data>
  <data name="DeliveryDateTo" xml:space="preserve">
    <value>Delivery Date To</value>
  </data>
  <data name="DeliveryNotes" xml:space="preserve">
    <value>Delivery Notes</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="DimensionalWeight" xml:space="preserve">
    <value>Dimensional Weight</value>
  </data>
  <data name="Division" xml:space="preserve">
    <value>Division</value>
  </data>
  <data name="Document" xml:space="preserve">
    <value>Document</value>
  </data>
  <data name="DueDate" xml:space="preserve">
    <value>Due Date</value>
  </data>
  <data name="DueTime" xml:space="preserve">
    <value>Due Time</value>
  </data>
  <data name="DutyCode" xml:space="preserve">
    <value>Duty Code</value>
  </data>
  <data name="DutyFreeCode" xml:space="preserve">
    <value>Duty-Free Harmonised Code</value>
  </data>
  <data name="DutyHarmonisedCode" xml:space="preserve">
    <value>Duty Harmonised Code</value>
  </data>
  <data name="EditDetails" xml:space="preserve">
    <value>Edit Details</value>
  </data>
  <data name="EECMember" xml:space="preserve">
    <value>EEC Member</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="EmployeeName" xml:space="preserve">
    <value>Employee</value>
  </data>
  <data name="EstimatedFreight" xml:space="preserve">
    <value>Estimated Freight</value>
  </data>
  <data name="ETA" xml:space="preserve">
    <value>ETA</value>
  </data>
  <data name="ExpediteDate" xml:space="preserve">
    <value>Expedite Date</value>
  </data>
  <data name="ExpediteDateFrom" xml:space="preserve">
    <value>Expedite Date From</value>
  </data>
  <data name="ExpediteDateTo" xml:space="preserve">
    <value>Expedite Date To</value>
  </data>
  <data name="ExpediteNotes" xml:space="preserve">
    <value>Expedite Notes</value>
  </data>
  <data name="ExportData" xml:space="preserve">
    <value>Export?</value>
  </data>
  <data name="Exported" xml:space="preserve">
    <value>Exported?</value>
  </data>
  <data name="Extension" xml:space="preserve">
    <value>Extension</value>
  </data>
  <data name="FavouriteSport" xml:space="preserve">
    <value>Favourite Sport</value>
  </data>
  <data name="FavouriteTeam" xml:space="preserve">
    <value>Favourite Team</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="Feedback" xml:space="preserve">
    <value>Your Feedback</value>
  </data>
  <data name="File" xml:space="preserve">
    <value>File</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>First Name</value>
  </data>
  <data name="FOB" xml:space="preserve">
    <value>FOB</value>
  </data>
  <data name="Folder" xml:space="preserve">
    <value>Folder</value>
  </data>
  <data name="FooterText" xml:space="preserve">
    <value>Footer Text</value>
  </data>
  <data name="FreeOnBoard" xml:space="preserve">
    <value>Free On Board</value>
  </data>
  <data name="Freight" xml:space="preserve">
    <value>Freight</value>
  </data>
  <data name="From" xml:space="preserve">
    <value>From</value>
  </data>
  <data name="Funds" xml:space="preserve">
    <value>Funds</value>
  </data>
  <data name="Gender" xml:space="preserve">
    <value>Gender</value>
  </data>
  <data name="GeneralUsersSecurityGroupName" xml:space="preserve">
    <value>General Users Security Group Name</value>
  </data>
  <data name="GenericDatabaseTimeout" xml:space="preserve">
    <value>Generic database timeout</value>
  </data>
  <data name="GINo" xml:space="preserve">
    <value>Goods In Note</value>
  </data>
  <data name="GoodsIn" xml:space="preserve">
    <value>Goods In</value>
  </data>
  <data name="GoodsInNo" xml:space="preserve">
    <value>Goods In Note</value>
  </data>
  <data name="GoodsInUpdateType" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="GoodsInValue" xml:space="preserve">
    <value>Goods In Value</value>
  </data>
  <data name="GP" xml:space="preserve">
    <value>GP</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Group</value>
  </data>
  <data name="Hobbies" xml:space="preserve">
    <value>Hobbies</value>
  </data>
  <data name="HomeEmail" xml:space="preserve">
    <value>Home Email</value>
  </data>
  <data name="HomeFax" xml:space="preserve">
    <value>Home Fax</value>
  </data>
  <data name="HomeTel" xml:space="preserve">
    <value>Tel 2</value>
  </data>
  <data name="HomeTelephone" xml:space="preserve">
    <value>Home Telephone</value>
  </data>
  <data name="IgnoreRowLimit" xml:space="preserve">
    <value>Ignore Row Limit</value>
  </data>
  <data name="ImportantNotes" xml:space="preserve">
    <value>Important Notes</value>
  </data>
  <data name="InAdvance" xml:space="preserve">
    <value>In Advance</value>
  </data>
  <data name="IncludeClosed" xml:space="preserve">
    <value>Include Closed?</value>
  </data>
  <data name="IncludeHistory" xml:space="preserve">
    <value>Include History</value>
  </data>
  <data name="IncludeInvoiced" xml:space="preserve">
    <value>Include Invoiced?</value>
  </data>
  <data name="IncludePaid" xml:space="preserve">
    <value>Include Paid</value>
  </data>
  <data name="IncludeZeroStock" xml:space="preserve">
    <value>Include Zero Stock?</value>
  </data>
  <data name="IndustryType" xml:space="preserve">
    <value>Industry Type</value>
  </data>
  <data name="Initial" xml:space="preserve">
    <value>Initial</value>
  </data>
  <data name="InspectedBy" xml:space="preserve">
    <value>Released By</value>
  </data>
  <data name="InStock" xml:space="preserve">
    <value>In Stock</value>
  </data>
  <data name="Instructions" xml:space="preserve">
    <value>Instructions</value>
  </data>
  <data name="InternalInstructions" xml:space="preserve">
    <value>Internal Instructions</value>
  </data>
  <data name="InternalNotes" xml:space="preserve">
    <value>Internal notes</value>
  </data>
  <data name="InvoiceAmount" xml:space="preserve">
    <value>Invoice Amount</value>
  </data>
  <data name="InvoiceDate" xml:space="preserve">
    <value>Invoice Date</value>
  </data>
  <data name="InvoiceNo" xml:space="preserve">
    <value>Invoice No</value>
  </data>
  <data name="IsApproved" xml:space="preserve">
    <value>Approved?</value>
  </data>
  <data name="IsApprovedCustomer" xml:space="preserve">
    <value>Approved Customer?</value>
  </data>
  <data name="IsApprovedSupplier" xml:space="preserve">
    <value>Approved Supplier?</value>
  </data>
  <data name="IsClosed" xml:space="preserve">
    <value>Closed?</value>
  </data>
  <data name="IsConfirmed" xml:space="preserve">
    <value>Confirmed?</value>
  </data>
  <data name="IsConsignment" xml:space="preserve">
    <value>Consignment?</value>
  </data>
  <data name="IsDefaultBill" xml:space="preserve">
    <value>Default Bill?</value>
  </data>
  <data name="IsDefaultShip" xml:space="preserve">
    <value>Default Ship?</value>
  </data>
  <data name="IsDutyPayable" xml:space="preserve">
    <value>Duty?</value>
  </data>
  <data name="IsEmailTextOnly" xml:space="preserve">
    <value>Text Only Email?</value>
  </data>
  <data name="IsExported" xml:space="preserve">
    <value>Exported?</value>
  </data>
  <data name="IsHighPriority" xml:space="preserve">
    <value>High Priority?</value>
  </data>
  <data name="IsInactive" xml:space="preserve">
    <value>Inactive?</value>
  </data>
  <data name="IsIncluded" xml:space="preserve">
    <value>Included</value>
  </data>
  <data name="IsListPriority" xml:space="preserve">
    <value>List Priority?</value>
  </data>
  <data name="IsOnHold" xml:space="preserve">
    <value>On Hold?</value>
  </data>
  <data name="IsOnStop" xml:space="preserve">
    <value>On Stop?</value>
  </data>
  <data name="IsPaid" xml:space="preserve">
    <value>Paid?</value>
  </data>
  <data name="IsPosted" xml:space="preserve">
    <value>Posted</value>
  </data>
  <data name="IsProspect" xml:space="preserve">
    <value>Prospect?</value>
  </data>
  <data name="IsQuarantined" xml:space="preserve">
    <value>Quarantined?</value>
  </data>
  <data name="IsROHSCompliant" xml:space="preserve">
    <value>ROHS Compliant?</value>
  </data>
  <data name="IsShipASAP" xml:space="preserve">
    <value>Ship ASAP</value>
  </data>
  <data name="IsShippingWaived" xml:space="preserve">
    <value>Waive Shipping &amp; Shipping Surcharge?</value>
  </data>
  <data name="IsTaxable" xml:space="preserve">
    <value>Taxable?</value>
  </data>
  <data name="JobTitle" xml:space="preserve">
    <value>Job Title</value>
  </data>
  <data name="LandedCost" xml:space="preserve">
    <value>Landed Cost</value>
  </data>
  <data name="LastContacted" xml:space="preserve">
    <value>Last Contacted</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Last Name</value>
  </data>
  <data name="LastYear" xml:space="preserve">
    <value>Last Year</value>
  </data>
  <data name="LineDetail" xml:space="preserve">
    <value>Line Detail</value>
  </data>
  <data name="ListName" xml:space="preserve">
    <value>List Name</value>
  </data>
  <data name="ListNo" xml:space="preserve">
    <value>List No</value>
  </data>
  <data name="ListViewResultsLimit" xml:space="preserve">
    <value>List Page Results Limit</value>
  </data>
  <data name="LMGp" xml:space="preserve">
    <value>Last Month GP</value>
  </data>
  <data name="LMGpPct" xml:space="preserve">
    <value>Last Month GP (%)</value>
  </data>
  <data name="LMPct" xml:space="preserve">
    <value>Last Month %</value>
  </data>
  <data name="Location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="LoginTimeout" xml:space="preserve">
    <value>Login timeout</value>
  </data>
  <data name="LogNotes" xml:space="preserve">
    <value>Log Notes</value>
  </data>
  <data name="LogType" xml:space="preserve">
    <value>Log Type</value>
  </data>
  <data name="Lot" xml:space="preserve">
    <value>Lot</value>
  </data>
  <data name="LTAir" xml:space="preserve">
    <value>Delivery Lead Time - Air</value>
  </data>
  <data name="LTSurface" xml:space="preserve">
    <value>Delivery Lead Time - Surface</value>
  </data>
  <data name="LYGp" xml:space="preserve">
    <value>Last Year GP</value>
  </data>
  <data name="LYGpPct" xml:space="preserve">
    <value>Last Year GP (%)</value>
  </data>
  <data name="LYPct" xml:space="preserve">
    <value>Last Year %</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>Manager</value>
  </data>
  <data name="Manufacturer" xml:space="preserve">
    <value>Manufacturer</value>
  </data>
  <data name="Manufacturers" xml:space="preserve">
    <value>Manufacturers</value>
  </data>
  <data name="MaritalStatus" xml:space="preserve">
    <value>Marital Status</value>
  </data>
  <data name="MasterCountryName" xml:space="preserve">
    <value>Master Country Name</value>
  </data>
  <data name="Message" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="MessageAlertShown" xml:space="preserve">
    <value>Show Message Alert?</value>
  </data>
  <data name="MessageCheckTimeout" xml:space="preserve">
    <value>Message check interval</value>
  </data>
  <data name="Mobile" xml:space="preserve">
    <value>Mobile</value>
  </data>
  <data name="MobileTel" xml:space="preserve">
    <value>Mobile Tel</value>
  </data>
  <data name="MTDGp" xml:space="preserve">
    <value>MTD GP</value>
  </data>
  <data name="MTDPct" xml:space="preserve">
    <value>MTD %</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Name1" xml:space="preserve">
    <value>Name 1</value>
  </data>
  <data name="Name2" xml:space="preserve">
    <value>Name 2</value>
  </data>
  <data name="NewLot" xml:space="preserve">
    <value>New Lot</value>
  </data>
  <data name="NewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="Nickname" xml:space="preserve">
    <value>Nickname</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="NumberChildren" xml:space="preserve">
    <value>Number of Children</value>
  </data>
  <data name="NumberRecentlyViewedPages" xml:space="preserve">
    <value>Recently viewed pages</value>
  </data>
  <data name="OfferDate" xml:space="preserve">
    <value>Offer Date</value>
  </data>
  <data name="OfferPrice" xml:space="preserve">
    <value>Offer Price</value>
  </data>
  <data name="OfferStatus" xml:space="preserve">
    <value>Offer Status</value>
  </data>
  <data name="OldPassword" xml:space="preserve">
    <value>Old Password</value>
  </data>
  <data name="OnOrder" xml:space="preserve">
    <value>On Order</value>
  </data>
  <data name="OpenCost" xml:space="preserve">
    <value>Open Landed Cost</value>
  </data>
  <data name="OpenFreight" xml:space="preserve">
    <value>Open Freight Charges</value>
  </data>
  <data name="OpenGpPct" xml:space="preserve">
    <value>Open GP (%)</value>
  </data>
  <data name="OpenPOs" xml:space="preserve">
    <value>Open Purchase Orders</value>
  </data>
  <data name="OpenSales" xml:space="preserve">
    <value>Open Sales Value</value>
  </data>
  <data name="OpenSOs" xml:space="preserve">
    <value>Open Sales Orders</value>
  </data>
  <data name="OpenSOTotal" xml:space="preserve">
    <value>Posted Sales Order Total</value>
  </data>
  <data name="OrderValue" xml:space="preserve">
    <value>This Order Value</value>
  </data>
  <data name="OutstandingInvoices" xml:space="preserve">
    <value>Outstanding Invoices</value>
  </data>
  <data name="OverduePOs" xml:space="preserve">
    <value>Overdue Purchase Orders</value>
  </data>
  <data name="OverdueSOs" xml:space="preserve">
    <value>Overdue Sales Orders</value>
  </data>
  <data name="OverrideInvoiceHeader" xml:space="preserve">
    <value>Revert to company header on invoice?</value>
  </data>
  <data name="Package" xml:space="preserve">
    <value>Package</value>
  </data>
  <data name="PackageAbbreviation" xml:space="preserve">
    <value>Pack.</value>
  </data>
  <data name="PackageUnit" xml:space="preserve">
    <value>Package Unit</value>
  </data>
  <data name="Paid" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="ParentCompany" xml:space="preserve">
    <value>Parent Company</value>
  </data>
  <data name="PartMarkings" xml:space="preserve">
    <value>Part Markings</value>
  </data>
  <data name="Partner" xml:space="preserve">
    <value>Partner</value>
  </data>
  <data name="PartnerBirthday" xml:space="preserve">
    <value>Partner Birthday</value>
  </data>
  <data name="PartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="PartOf" xml:space="preserve">
    <value>Part Of</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Pct" xml:space="preserve">
    <value>%</value>
  </data>
  <data name="PermissionValue" xml:space="preserve">
    <value>Permission Value</value>
  </data>
  <data name="PersonalAddress" xml:space="preserve">
    <value>Personal Address</value>
  </data>
  <data name="PersonalAddressName" xml:space="preserve">
    <value>Personal Address Name</value>
  </data>
  <data name="PickUp" xml:space="preserve">
    <value>Pick Up</value>
  </data>
  <data name="Postcode" xml:space="preserve">
    <value>Postcode*</value>
  </data>
  <data name="Posted" xml:space="preserve">
    <value>Posted</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Price</value>
  </data>
  <data name="PrintNotes" xml:space="preserve">
    <value>Print Notes</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="ProductAbbreviation" xml:space="preserve">
    <value>Prod.</value>
  </data>
  <data name="Progress" xml:space="preserve">
    <value>Progress</value>
  </data>
  <data name="PromisedDate" xml:space="preserve">
    <value>Date Promised</value>
  </data>
  <data name="PurchaseOrder" xml:space="preserve">
    <value>Purchase Order</value>
  </data>
  <data name="PurchaseOrderNo" xml:space="preserve">
    <value>Purchase Order</value>
  </data>
  <data name="PurchasePrice" xml:space="preserve">
    <value>Purchase Price</value>
  </data>
  <data name="QCNotes" xml:space="preserve">
    <value>Quality Control Notes</value>
  </data>
  <data name="QualityControlNotes" xml:space="preserve">
    <value>Quality Control Notes</value>
  </data>
  <data name="Quantities" xml:space="preserve">
    <value>Quantities</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantity</value>
  </data>
  <data name="QuantityAllocated" xml:space="preserve">
    <value>Qty Allocated</value>
  </data>
  <data name="QuantityAuthorised" xml:space="preserve">
    <value>Qty Authorised</value>
  </data>
  <data name="QuantityAvailable" xml:space="preserve">
    <value>Qty Available</value>
  </data>
  <data name="QuantityAvailableForSplit" xml:space="preserve">
    <value>Qty Available for Split</value>
  </data>
  <data name="QuantityBackOrder" xml:space="preserve">
    <value>Qty BackOrder</value>
  </data>
  <data name="QuantityInStock" xml:space="preserve">
    <value>Qty In Stock</value>
  </data>
  <data name="QuantityOnOrder" xml:space="preserve">
    <value>Qty On Order</value>
  </data>
  <data name="QuantityOrdered" xml:space="preserve">
    <value>Qty Ordered</value>
  </data>
  <data name="QuantityOutstanding" xml:space="preserve">
    <value>Qty Outstanding</value>
  </data>
  <data name="QuantityReceived" xml:space="preserve">
    <value>Qty Received</value>
  </data>
  <data name="QuantityRequired" xml:space="preserve">
    <value>Qty Required</value>
  </data>
  <data name="QuantityShipped" xml:space="preserve">
    <value>Qty Shipped</value>
  </data>
  <data name="QuantityToSplit" xml:space="preserve">
    <value>Quantity to Split</value>
  </data>
  <data name="Quarantine" xml:space="preserve">
    <value>Quarantine</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Quarantined</value>
  </data>
  <data name="QuarantineThisItem" xml:space="preserve">
    <value>Quarantine this Item?</value>
  </data>
  <data name="QuoteNo" xml:space="preserve">
    <value>Quote No</value>
  </data>
  <data name="RaisedBy" xml:space="preserve">
    <value>Raised By</value>
  </data>
  <data name="Rate" xml:space="preserve">
    <value>Rate</value>
  </data>
  <data name="Rate2" xml:space="preserve">
    <value>Rate 2</value>
  </data>
  <data name="Rating" xml:space="preserve">
    <value>Rating</value>
  </data>
  <data name="Reason" xml:space="preserve">
    <value>Reason 1</value>
  </data>
  <data name="ReceivedBy" xml:space="preserve">
    <value>Received By</value>
  </data>
  <data name="ReceivedDate" xml:space="preserve">
    <value>Received Date</value>
  </data>
  <data name="ReceivedDateFrom" xml:space="preserve">
    <value>Received Date From</value>
  </data>
  <data name="ReceivedDateTo" xml:space="preserve">
    <value>Received Date To</value>
  </data>
  <data name="ReceivingInstructions" xml:space="preserve">
    <value>Receiving Instructions</value>
  </data>
  <data name="ReceivingNotes" xml:space="preserve">
    <value>Receiving Notes</value>
  </data>
  <data name="RecentOnly" xml:space="preserve">
    <value>Recent Only?</value>
  </data>
  <data name="Reference" xml:space="preserve">
    <value>Reference</value>
  </data>
  <data name="ReferenceDate" xml:space="preserve">
    <value>Reference Date</value>
  </data>
  <data name="RelatedToDos" xml:space="preserve">
    <value>Related To Dos</value>
  </data>
  <data name="Reminder" xml:space="preserve">
    <value>Reminder</value>
  </data>
  <data name="ReminderDate" xml:space="preserve">
    <value>Reminder Date</value>
  </data>
  <data name="ReminderText" xml:space="preserve">
    <value>Reminder Text</value>
  </data>
  <data name="ReminderTime" xml:space="preserve">
    <value>Reminder Time</value>
  </data>
  <data name="ReplyTo" xml:space="preserve">
    <value>Reply To</value>
  </data>
  <data name="RepriceOpenOrders" xml:space="preserve">
    <value>Reprice Open Orders?</value>
  </data>
  <data name="RequiredDate" xml:space="preserve">
    <value>Date Required</value>
  </data>
  <data name="RequirementNo" xml:space="preserve">
    <value>Requirement No</value>
  </data>
  <data name="ResalePrice" xml:space="preserve">
    <value>Resale Price</value>
  </data>
  <data name="ResetQuantity" xml:space="preserve">
    <value>Reset Quantity?</value>
  </data>
  <data name="ReturnDate" xml:space="preserve">
    <value>Return Date</value>
  </data>
  <data name="RMADate" xml:space="preserve">
    <value>RMA Date</value>
  </data>
  <data name="RoHS" xml:space="preserve">
    <value>RoHS</value>
  </data>
  <data name="Salesman" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="Salesman2Percent" xml:space="preserve">
    <value>Additional salesperson %</value>
  </data>
  <data name="SalesmanName" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="SalesOrder" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="SalesOrderNo" xml:space="preserve">
    <value>Sales Order</value>
  </data>
  <data name="Salesperson" xml:space="preserve">
    <value>Salesperson</value>
  </data>
  <data name="Salesperson2" xml:space="preserve">
    <value>Additional Salesperson</value>
  </data>
  <data name="ScheduledCall" xml:space="preserve">
    <value>Scheduled Call</value>
  </data>
  <data name="ScheduledCallFor" xml:space="preserve">
    <value>For</value>
  </data>
  <data name="SecurityGroupName" xml:space="preserve">
    <value>Security Group Name</value>
  </data>
  <data name="SecurityGroupNo" xml:space="preserve">
    <value>Security Group</value>
  </data>
  <data name="SecurityPageCode" xml:space="preserve">
    <value>Security Page Code</value>
  </data>
  <data name="SecurityPageFunctionNo" xml:space="preserve">
    <value>Security Page Function </value>
  </data>
  <data name="SelectedGroups" xml:space="preserve">
    <value>Selected Groups</value>
  </data>
  <data name="SelectedLogins" xml:space="preserve">
    <value>Selected Logins</value>
  </data>
  <data name="SelectItem" xml:space="preserve">
    <value>Select Item</value>
  </data>
  <data name="SelectMasterCountry" xml:space="preserve">
    <value>Select Master Country</value>
  </data>
  <data name="SelectMasterCurrency" xml:space="preserve">
    <value>Select Master Currency</value>
  </data>
  <data name="SelectNewOrExistingHeader" xml:space="preserve">
    <value>Select a New or an Existing Header</value>
  </data>
  <data name="SelectSource" xml:space="preserve">
    <value>Select Source</value>
  </data>
  <data name="SelectTarget" xml:space="preserve">
    <value>Select Target</value>
  </data>
  <data name="Sell" xml:space="preserve">
    <value>Sell</value>
  </data>
  <data name="SellPrice" xml:space="preserve">
    <value>Selling Price</value>
  </data>
  <data name="SellShipVia" xml:space="preserve">
    <value>Sell Ship Via</value>
  </data>
  <data name="SellShipViaAccount" xml:space="preserve">
    <value>Sell Ship Via Account</value>
  </data>
  <data name="SellShipViaNo" xml:space="preserve">
    <value>Sell Ship Via No</value>
  </data>
  <data name="SerialNosRecorded" xml:space="preserve">
    <value>Serial Nos Recorded?</value>
  </data>
  <data name="Service" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="ShipASAP" xml:space="preserve">
    <value>Ship ASAP</value>
  </data>
  <data name="ShipCost" xml:space="preserve">
    <value>Default Ship In Cost</value>
  </data>
  <data name="ShipFreight" xml:space="preserve">
    <value>Shipped Freight Charges</value>
  </data>
  <data name="ShipFrom" xml:space="preserve">
    <value>Ship From</value>
  </data>
  <data name="ShipGpPct" xml:space="preserve">
    <value>Shipped GP (%)</value>
  </data>
  <data name="ShipInCost" xml:space="preserve">
    <value>Ship In Cost</value>
  </data>
  <data name="ShipmentsListTitle" xml:space="preserve">
    <value>Parts to be shipped:</value>
  </data>
  <data name="Shipped" xml:space="preserve">
    <value>Shipped</value>
  </data>
  <data name="ShippedBy" xml:space="preserve">
    <value>Shipped By</value>
  </data>
  <data name="ShippedFrom" xml:space="preserve">
    <value>Shipped From</value>
  </data>
  <data name="Shipper" xml:space="preserve">
    <value>Shipper</value>
  </data>
  <data name="Shipping" xml:space="preserve">
    <value>Shipping</value>
  </data>
  <data name="ShippingAccountNo" xml:space="preserve">
    <value>Shipping A/C</value>
  </data>
  <data name="ShippingCost" xml:space="preserve">
    <value>Shipping Cost</value>
  </data>
  <data name="ShippingInstructions" xml:space="preserve">
    <value>Shipping Instr</value>
  </data>
  <data name="ShippingNotes" xml:space="preserve">
    <value>Shipping Notes</value>
  </data>
  <data name="ShippingStatus" xml:space="preserve">
    <value>Shipping Status</value>
  </data>
  <data name="ShipSales" xml:space="preserve">
    <value>Shipped Sales Value</value>
  </data>
  <data name="ShipTo" xml:space="preserve">
    <value>Ship To</value>
  </data>
  <data name="ShipToAddress" xml:space="preserve">
    <value>Ship To Address</value>
  </data>
  <data name="ShipToAddressName" xml:space="preserve">
    <value>Ship To Address Name</value>
  </data>
  <data name="ShipVia" xml:space="preserve">
    <value>Ship Via</value>
  </data>
  <data name="ShipViaAccount" xml:space="preserve">
    <value>Ship Via Account</value>
  </data>
  <data name="ShipViaNo" xml:space="preserve">
    <value>Ship Via</value>
  </data>
  <data name="ShouldMailBeSent" xml:space="preserve">
    <value>Send Mail?</value>
  </data>
  <data name="SMTPHost" xml:space="preserve">
    <value>SMTP Host</value>
  </data>
  <data name="SMTPPassword" xml:space="preserve">
    <value>SMTP Password</value>
  </data>
  <data name="SMTPPort" xml:space="preserve">
    <value>SMTP Port</value>
  </data>
  <data name="SMTPUsername" xml:space="preserve">
    <value>SMTP Username</value>
  </data>
  <data name="SOName" xml:space="preserve">
    <value>SO Name</value>
  </data>
  <data name="SplitQuantities" xml:space="preserve">
    <value>Split Quantities</value>
  </data>
  <data name="SRMADate" xml:space="preserve">
    <value>Supplier RMA Date</value>
  </data>
  <data name="SRMANo" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="StandardShipping" xml:space="preserve">
    <value>Standard Shipping</value>
  </data>
  <data name="State" xml:space="preserve">
    <value>State</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="StockKeepingUnit" xml:space="preserve">
    <value>Stock Keeping Unit</value>
  </data>
  <data name="StockLogReason" xml:space="preserve">
    <value>Log Reason</value>
  </data>
  <data name="Subject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="SubTotal" xml:space="preserve">
    <value>SubTotal</value>
  </data>
  <data name="SuccessfulSaveMessageTime" xml:space="preserve">
    <value>Successful save message time</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="SupplierCredit" xml:space="preserve">
    <value>Supplier Credit</value>
  </data>
  <data name="SupplierInvoice" xml:space="preserve">
    <value>Supplier Invoice</value>
  </data>
  <data name="SupplierName" xml:space="preserve">
    <value>Supplier Name</value>
  </data>
  <data name="SupplierNo" xml:space="preserve">
    <value>Supplier No</value>
  </data>
  <data name="SupplierNotes" xml:space="preserve">
    <value>Supplier Notes</value>
  </data>
  <data name="SupplierPart" xml:space="preserve">
    <value>Supplier Part</value>
  </data>
  <data name="SupplierPartNo" xml:space="preserve">
    <value>Supplier Part No</value>
  </data>
  <data name="SupplierRating" xml:space="preserve">
    <value>Supplier Rating</value>
  </data>
  <data name="SupplierReturn" xml:space="preserve">
    <value>Supplier Return</value>
  </data>
  <data name="SupplierRMA" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="SupplierRMADate" xml:space="preserve">
    <value>Supplier RMA Date</value>
  </data>
  <data name="SupplierRMADateFrom" xml:space="preserve">
    <value>Supplier RMA Date From</value>
  </data>
  <data name="SupplierRMADateTo" xml:space="preserve">
    <value>Supplier RMA Date To</value>
  </data>
  <data name="SupplierRMANo" xml:space="preserve">
    <value>Supplier RMA</value>
  </data>
  <data name="Surname" xml:space="preserve">
    <value>Surname</value>
  </data>
  <data name="Symbol" xml:space="preserve">
    <value>Symbol</value>
  </data>
  <data name="TableName" xml:space="preserve">
    <value>Activity</value>
  </data>
  <data name="TargetPrice" xml:space="preserve">
    <value>Target Price</value>
  </data>
  <data name="TargetPriceLytica" xml:space="preserve">
    <value>Target Price (70th Percentile)</value>
  </data>
  <data name="Tax" xml:space="preserve">
    <value>Tax</value>
  </data>
  <data name="Tax1On2" xml:space="preserve">
    <value>Tax 1 On 2</value>
  </data>
  <data name="Taxable" xml:space="preserve">
    <value>Taxable?</value>
  </data>
  <data name="TaxCode" xml:space="preserve">
    <value>Tax Code</value>
  </data>
  <data name="TaxName" xml:space="preserve">
    <value>Tax Name</value>
  </data>
  <data name="Team" xml:space="preserve">
    <value>Team</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Telephone" xml:space="preserve">
    <value>Telephone</value>
  </data>
  <data name="Telephone800" xml:space="preserve">
    <value>0800 Number</value>
  </data>
  <data name="TelExt" xml:space="preserve">
    <value>Ext</value>
  </data>
  <data name="TelPrefix" xml:space="preserve">
    <value>Dialling Code</value>
  </data>
  <data name="Terms" xml:space="preserve">
    <value>Terms</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Time</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="TMGpPct" xml:space="preserve">
    <value>This Month GP (%)</value>
  </data>
  <data name="To" xml:space="preserve">
    <value>To</value>
  </data>
  <data name="Total" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="TotalCost" xml:space="preserve">
    <value>Total Landed Cost</value>
  </data>
  <data name="TotalFreight" xml:space="preserve">
    <value>Total Freight Charges</value>
  </data>
  <data name="TotalGpPct" xml:space="preserve">
    <value>Total GP (%)</value>
  </data>
  <data name="TotalLandedCost" xml:space="preserve">
    <value>Total Landed Cost</value>
  </data>
  <data name="TotalSales" xml:space="preserve">
    <value>Total Sales Value</value>
  </data>
  <data name="TotalShipInCost" xml:space="preserve">
    <value>Recommended Ship In Cost</value>
  </data>
  <data name="TotalWork" xml:space="preserve">
    <value>Total Work</value>
  </data>
  <data name="TYGpPct" xml:space="preserve">
    <value>This Year GP (%)</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Unauthorised" xml:space="preserve">
    <value>Unauthorised</value>
  </data>
  <data name="UnauthorisedOnly" xml:space="preserve">
    <value>Unauthorised Only?</value>
  </data>
  <data name="UnselectedGroups" xml:space="preserve">
    <value>Unselected Groups</value>
  </data>
  <data name="UnselectedLogins" xml:space="preserve">
    <value>Unselected Logins</value>
  </data>
  <data name="UpdateBillToAddress" xml:space="preserve">
    <value>Update Bill To Address?</value>
  </data>
  <data name="UpdateShipToAddress" xml:space="preserve">
    <value>Update Ship To Address?</value>
  </data>
  <data name="URL" xml:space="preserve">
    <value>Website</value>
  </data>
  <data name="Usage" xml:space="preserve">
    <value>Usage</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>User</value>
  </data>
  <data name="UserField1" xml:space="preserve">
    <value>User Field 1</value>
  </data>
  <data name="UserField10" xml:space="preserve">
    <value>User Field 10</value>
  </data>
  <data name="UserField2" xml:space="preserve">
    <value>User Field 2</value>
  </data>
  <data name="UserField3" xml:space="preserve">
    <value>User Field 3</value>
  </data>
  <data name="UserField4" xml:space="preserve">
    <value>User Field 4</value>
  </data>
  <data name="UserField5" xml:space="preserve">
    <value>User Field 5</value>
  </data>
  <data name="UserField6" xml:space="preserve">
    <value>User Field 6</value>
  </data>
  <data name="UserField7" xml:space="preserve">
    <value>User Field 7</value>
  </data>
  <data name="UserField8" xml:space="preserve">
    <value>User Field 8</value>
  </data>
  <data name="UserField9" xml:space="preserve">
    <value>User Field 9</value>
  </data>
  <data name="Username" xml:space="preserve">
    <value>Username</value>
  </data>
  <data name="VATNumber" xml:space="preserve">
    <value>VAT No</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="Weight" xml:space="preserve">
    <value>Weight</value>
  </data>
  <data name="WeightInPounds" xml:space="preserve">
    <value>Weight (lbs)?</value>
  </data>
  <data name="YearToDate" xml:space="preserve">
    <value>Year To Date</value>
  </data>
  <data name="YTDGp" xml:space="preserve">
    <value>YTD GP</value>
  </data>
  <data name="YTDPct" xml:space="preserve">
    <value>YTD %</value>
  </data>
  <data name="ViewLevel" xml:space="preserve">
    <value>View Level</value>
  </data>
  <data name="SaveDLNState" xml:space="preserve">
    <value>Save searches by default?</value>
  </data>
  <data name="MaxStockImages" xml:space="preserve">
    <value>Maximum images per Stock</value>
  </data>
  <data name="Incoterm" xml:space="preserve">
    <value>Incoterms</value>
  </data>
  <data name="AddedBy" xml:space="preserve">
    <value>Added by</value>
  </data>
  <data name="LogDateFrom" xml:space="preserve">
    <value>Log Date From</value>
  </data>
  <data name="LogDateTo" xml:space="preserve">
    <value>Log Date To</value>
  </data>
  <data name="Selected" xml:space="preserve">
    <value>Selected</value>
  </data>
  <data name="Unselected" xml:space="preserve">
    <value>Unselected</value>
  </data>
  <data name="NewUser" xml:space="preserve">
    <value>New User</value>
  </data>
  <data name="OldUser" xml:space="preserve">
    <value>Old User</value>
  </data>
  <data name="OriginalOfferDate" xml:space="preserve">
    <value>Original Offer Date</value>
  </data>
  <data name="OriginalOfferPrice" xml:space="preserve">
    <value>Original Offer Price</value>
  </data>
  <data name="OriginalOfferSupplier" xml:space="preserve">
    <value>Original Offer Supplier</value>
  </data>
  <data name="IsVirtual" xml:space="preserve">
    <value>Virtual?</value>
  </data>
  <data name="IncludeShippingOnHomepageStats" xml:space="preserve">
    <value>Include shipping on Homepage Statistics?</value>
  </data>
  <data name="BOM" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="BOMName" xml:space="preserve">
    <value>BOM Name</value>
  </data>
  <data name="PartWatch" xml:space="preserve">
    <value>PartWatch</value>
  </data>
  <data name="OwnDataVisibleToOthers" xml:space="preserve">
    <value>Data is visible to other companies?</value>
  </data>
  <data name="ShouldPhotosBeCopied" xml:space="preserve">
    <value>Copy images?</value>
  </data>
  <data name="NewLocation" xml:space="preserve">
    <value>New location</value>
  </data>
  <data name="AutoApprovePO" xml:space="preserve">
    <value>Automatically approve PO?</value>
  </data>
  <data name="ApprovalHistory" xml:space="preserve">
    <value>Purchase Order Approval History</value>
  </data>
  <data name="IncludeReceived" xml:space="preserve">
    <value>Include Received?</value>
  </data>
  <data name="IncludeShipped" xml:space="preserve">
    <value>Include Shipped?</value>
  </data>
  <data name="DefaultPORating" xml:space="preserve">
    <value>Default Vendor Rating</value>
  </data>
  <data name="DefaultSORating" xml:space="preserve">
    <value>Default Customer Rating</value>
  </data>
  <data name="CanBeExported" xml:space="preserve">
    <value>Approved for Export</value>
  </data>
  <data name="CreditCardFee" xml:space="preserve">
    <value>Credit Card Fee</value>
  </data>
  <data name="DeliveryCharge" xml:space="preserve">
    <value>Delivery Charge</value>
  </data>
  <data name="GoodsValue" xml:space="preserve">
    <value>Goods Value</value>
  </data>
  <data name="HomepageTopSalespeople" xml:space="preserve">
    <value>Homepage top salespeople</value>
  </data>
  <data name="AutoApproveSO" xml:space="preserve">
    <value>Automatically approve SO?</value>
  </data>
  <data name="CompanyRegNo" xml:space="preserve">
    <value>Company Reg. No</value>
  </data>
  <data name="PaidOnly" xml:space="preserve">
    <value>Paid Only</value>
  </data>
  <data name="SOAuthorisedBy" xml:space="preserve">
    <value>Checked By</value>
  </data>
  <data name="SODateAuthorised" xml:space="preserve">
    <value>Date Checked</value>
  </data>
  <data name="SOUnauthorisedOnly" xml:space="preserve">
    <value>Unchecked Only?</value>
  </data>
  <data name="Zipcode" xml:space="preserve">
    <value>Zip Code</value>
  </data>
  <data name="PrintCount" xml:space="preserve">
    <value>No Of Print</value>
  </data>
  <data name="MSLLevel" xml:space="preserve">
    <value>MSL</value>
  </data>
  <data name="MaxPDFDocuments" xml:space="preserve">
    <value>Maximum PDF Documents</value>
  </data>
  <data name="ReceiveEmail" xml:space="preserve">
    <value>Receive Email?</value>
  </data>
  <data name="FinanceContact" xml:space="preserve">
    <value>Finance Contact?</value>
  </data>
  <data name="EmailBody" xml:space="preserve">
    <value>Email Body</value>
  </data>
  <data name="Incoterms" xml:space="preserve">
    <value>Incoterms</value>
  </data>
  <data name="Quote" xml:space="preserve">
    <value>Quote</value>
  </data>
  <data name="PurchaseCountry" xml:space="preserve">
    <value>Country of Purchase</value>
  </data>
  <data name="ExportedOnly" xml:space="preserve">
    <value>Exported Only</value>
  </data>
  <data name="Credit" xml:space="preserve">
    <value>Credit Note</value>
  </data>
  <data name="Debit" xml:space="preserve">
    <value>Debit</value>
  </data>
  <data name="SRMA" xml:space="preserve">
    <value>SRMA</value>
  </data>
  <data name="FailedOnly" xml:space="preserve">
    <value>Failed Only</value>
  </data>
  <data name="ApplyBankFee" xml:space="preserve">
    <value>Apply Bank Fee</value>
  </data>
  <data name="Sum" xml:space="preserve">
    <value>Sum</value>
  </data>
  <data name="BankChargeFee" xml:space="preserve">
    <value>Bank Charge Fee</value>
  </data>
  <data name="AlternatePart" xml:space="preserve">
    <value>Alternate Part No</value>
  </data>
  <data name="StockDate" xml:space="preserve">
    <value>Stock Date</value>
  </data>
  <data name="PromiseDate" xml:space="preserve">
    <value>Promise Date</value>
  </data>
  <data name="CompanyImportantNotes" xml:space="preserve">
    <value>Accounts notes</value>
  </data>
  <data name="CompanyNotes" xml:space="preserve">
    <value>General customer info</value>
  </data>
  <data name="AddressNotes" xml:space="preserve">
    <value>General info</value>
  </data>
  <data name="AddressShippingNotes" xml:space="preserve">
    <value>Notes for packing slip</value>
  </data>
  <data name="POInternalNotes" xml:space="preserve">
    <value>Notes to warehouse</value>
  </data>
  <data name="POLineNotes" xml:space="preserve">
    <value>PO line notes</value>
  </data>
  <data name="POReceivingNotes" xml:space="preserve">
    <value>Instruction to Warehouse</value>
  </data>
  <data name="POSupplierNotes" xml:space="preserve">
    <value>Notes to supplier</value>
  </data>
  <data name="QuoteInstructions" xml:space="preserve">
    <value>Instruction to warehouse</value>
  </data>
  <data name="Quotenotes" xml:space="preserve">
    <value>Printed line notes</value>
  </data>
  <data name="ReceivingPOInternalInstructions" xml:space="preserve">
    <value>Internal notes</value>
  </data>
  <data name="ReceivingPONotes" xml:space="preserve">
    <value>Notes to supplier</value>
  </data>
  <data name="SONotes" xml:space="preserve">
    <value>Printed line notes to invoice</value>
  </data>
  <data name="SOShippingInstructions" xml:space="preserve">
    <value>Shipping Instructions to WH only</value>
  </data>
  <data name="ReceivingPOQualityControlNotes" xml:space="preserve">
    <value>Instruction to WH &amp; Quality control notes</value>
  </data>
  <data name="ShipSOInstructions" xml:space="preserve">
    <value>Shipping instruction</value>
  </data>
  <data name="StockQualityControlNotes" xml:space="preserve">
    <value>Instruction to WH &amp; Quality control notes</value>
  </data>
  <data name="InvoiceNotExport" xml:space="preserve">
    <value>Invoice Not Exported</value>
  </data>
  <data name="AdviceNotes" xml:space="preserve">
    <value>Advice Note</value>
  </data>
  <data name="InsToQualityControlNotes" xml:space="preserve">
    <value>Instructions to Warehouse</value>
  </data>
  <data name="NotesToWarehouse" xml:space="preserve">
    <value>Notes To Warehouse</value>
  </data>
  <data name="PrintedLineNotes" xml:space="preserve">
    <value>Printed line notes to invoice</value>
  </data>
  <data name="ShippingInstruction" xml:space="preserve">
    <value>Shipping Instruction</value>
  </data>
  <data name="ShippingNotesAck" xml:space="preserve">
    <value>Notes for packing slip and acknowledgement</value>
  </data>
  <data name="DatePicked" xml:space="preserve">
    <value>Date Picked</value>
  </data>
  <data name="ExchangeRate" xml:space="preserve">
    <value>Exchange Rate</value>
  </data>
  <data name="Apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="InvoicePaidDate" xml:space="preserve">
    <value>Paid Date</value>
  </data>
  <data name="LocalCurrency" xml:space="preserve">
    <value>Local Currency</value>
  </data>
  <data name="MasterCurrency" xml:space="preserve">
    <value>Master Currency</value>
  </data>
  <data name="NPRPrinted" xml:space="preserve">
    <value>NPR Printed</value>
  </data>
  <data name="IncludeExported" xml:space="preserve">
    <value>Include Exported ?</value>
  </data>
  <data name="SupplierInvoiceDateFrom" xml:space="preserve">
    <value>Supplier Invoice Date From</value>
  </data>
  <data name="SupplierInvoiceDateTo" xml:space="preserve">
    <value>Supplier Invoice Date To</value>
  </data>
  <data name="URNNo" xml:space="preserve">
    <value>URN No</value>
  </data>
  <data name="Narrative" xml:space="preserve">
    <value>Narrative</value>
  </data>
  <data name="SecondRef" xml:space="preserve">
    <value>Second Ref</value>
  </data>
  <data name="SelectedGI" xml:space="preserve">
    <value>Selected GI Value</value>
  </data>
  <data name="GIDateFrom" xml:space="preserve">
    <value>GI Date From</value>
  </data>
  <data name="GIDateTo" xml:space="preserve">
    <value>GI Date To</value>
  </data>
  <data name="URNNumber" xml:space="preserve">
    <value>URN Number</value>
  </data>
  <data name="SupplierCode" xml:space="preserve">
    <value>Supplier Code</value>
  </data>
  <data name="PurchaseTaxCode" xml:space="preserve">
    <value>Purchase Tax Code</value>
  </data>
  <data name="SupplierInvoices" xml:space="preserve">
    <value>Supplier Invoices</value>
  </data>
  <data name="AutoExportSI" xml:space="preserve">
    <value>Automatically export supplier invoice</value>
  </data>
  <data name="CompanyCode" xml:space="preserve">
    <value>Company Code</value>
  </data>
  <data name="NPRReport" xml:space="preserve">
    <value> NPR Report</value>
  </data>
  <data name="Printer" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="AS9120" xml:space="preserve">
    <value>Source of Supply Required</value>
  </data>
  <data name="ProductSource" xml:space="preserve">
    <value>Product Source</value>
  </data>
  <data name="CertificationNotes" xml:space="preserve">
    <value>Review Status</value>
  </data>
  <data name="QualityNotes" xml:space="preserve">
    <value>Quality/scope of supply</value>
  </data>
  <data name="IsTraceability" xml:space="preserve">
    <value>Traceability required</value>
  </data>
  <data name="NotExported" xml:space="preserve">
    <value>Not Exported</value>
  </data>
  <data name="PrinterName" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="VATNo" xml:space="preserve">
    <value>VAT No</value>
  </data>
  <data name="ERAIMember" xml:space="preserve">
    <value>ERAI Member</value>
  </data>
  <data name="ERAIReported" xml:space="preserve">
    <value>ERAI Reported</value>
  </data>
  <data name="DefaultShipCountry" xml:space="preserve">
    <value>Default Ship from Country</value>
  </data>
  <data name="ReviewDate" xml:space="preserve">
    <value>Review Date</value>
  </data>
  <data name="CertificateCategoryName" xml:space="preserve">
    <value>Category Name</value>
  </data>
  <data name="CertificateName" xml:space="preserve">
    <value>Certificate Name</value>
  </data>
  <data name="ExpiryDate" xml:space="preserve">
    <value>Expiry Date</value>
  </data>
  <data name="StartDate" xml:space="preserve">
    <value>Start Date</value>
  </data>
  <data name="CertificateNumber" xml:space="preserve">
    <value>Certificate Number</value>
  </data>
  <data name="CurrentLandedCost" xml:space="preserve">
    <value>Current Landed Cost</value>
  </data>
  <data name="EnterValue" xml:space="preserve">
    <value>Enter Value</value>
  </data>
  <data name="NewLandedCost" xml:space="preserve">
    <value>New Landed Cost</value>
  </data>
  <data name="PrimaryLandedCost" xml:space="preserve">
    <value>Primary Landed Cost</value>
  </data>
  <data name="StockChoice" xml:space="preserve">
    <value>  </value>
  </data>
  <data name="StockProvisionLandedCost" xml:space="preserve">
    <value>Landed Cost</value>
  </data>
  <data name="NewStockProvision" xml:space="preserve">
    <value>New Stock Provision</value>
  </data>
  <data name="TotalCurrentLandedCost" xml:space="preserve">
    <value>Total Current Landed Cost</value>
  </data>
  <data name="TotalPrimaryLandedCost" xml:space="preserve">
    <value>Total Primary Landed Cost</value>
  </data>
  <data name="SaveConfirm" xml:space="preserve">
    <value>Please Confirm</value>
  </data>
  <data name="TotalNewLandedCost" xml:space="preserve">
    <value>Total New Landed Cost </value>
  </data>
  <data name="CustPONo" xml:space="preserve">
    <value>Customer PO number</value>
  </data>
  <data name="LotNo" xml:space="preserve">
    <value>Lot No</value>
  </data>
  <data name="COC" xml:space="preserve">
    <value>Include CofC</value>
  </data>
  <data name="LabelFullPath" xml:space="preserve">
    <value>Label Path</value>
  </data>
  <data name="LabelPath" xml:space="preserve">
    <value>Nice Label Path</value>
  </data>
  <data name="NiceLabelPath" xml:space="preserve">
    <value>Select Label Path</value>
  </data>
  <data name="EightDCodeOrder" xml:space="preserve">
    <value>Sort Order</value>
  </data>
  <data name="EightDCodePrifix" xml:space="preserve">
    <value>Prefix</value>
  </data>
  <data name="EightDCodePrifixDescription" xml:space="preserve">
    <value>Prefix Description</value>
  </data>
  <data name="EightDSubCatCode" xml:space="preserve">
    <value>Code</value>
  </data>
  <data name="EightDSubCatCodeDescription" xml:space="preserve">
    <value>Code Description</value>
  </data>
  <data name="Reason2" xml:space="preserve">
    <value>Reason 2</value>
  </data>
  <data name="RootCause" xml:space="preserve">
    <value>Root Cause</value>
  </data>
  <data name="ConflictResource" xml:space="preserve">
    <value>Conflict Resource</value>
  </data>
  <data name="FreightCost" xml:space="preserve">
    <value>Match Freight to the Shipping cost on invoice</value>
  </data>
  <data name="NprNo" xml:space="preserve">
    <value>NPR Number</value>
  </data>
  <data name="GINoNpr" xml:space="preserve">
    <value>GI Number</value>
  </data>
  <data name="PurchaseOrderNoNPR" xml:space="preserve">
    <value>Purchase Order Number</value>
  </data>
  <data name="IsCompleted" xml:space="preserve">
    <value> Completed</value>
  </data>
  <data name="IsAuthorised" xml:space="preserve">
    <value> Authorised</value>
  </data>
  <data name="Action" xml:space="preserve">
    <value>Action</value>
  </data>
  <data name="NprRaisedDateFrom" xml:space="preserve">
    <value>NPR Raised Date From</value>
  </data>
  <data name="NprRaisedDateTo" xml:space="preserve">
    <value>NPR Raised Date To</value>
  </data>
  <data name="ExpediteHistory" xml:space="preserve">
    <value>Expedite History</value>
  </data>
  <data name="AddNotes" xml:space="preserve">
    <value>Add Notes</value>
  </data>
  <data name="SendToGroup" xml:space="preserve">
    <value>Send To Group</value>
  </data>
  <data name="OnStop" xml:space="preserve">
    <value>On Stop?</value>
  </data>
  <data name="ADUserName" xml:space="preserve">
    <value>AD Username</value>
  </data>
  <data name="GroupAccess" xml:space="preserve">
    <value>Group Access</value>
  </data>
  <data name="IncludeInvoiceEmbedImage" xml:space="preserve">
    <value>Print/Email inv/pck slip as PDF</value>
  </data>
  <data name="LotLockForCust" xml:space="preserve">
    <value>Lock for customer</value>
  </data>
  <data name="EnterUserName" xml:space="preserve">
    <value>Please enter the user name</value>
  </data>
  <data name="EnterEmail" xml:space="preserve">
    <value>Please enter the email</value>
  </data>
  <data name="SelectClient" xml:space="preserve">
    <value>Please select the client</value>
  </data>
  <data name="EditPromiseDate" xml:space="preserve">
    <value>Allow edit Date Promised period between current month and Date Promise month</value>
  </data>
  <data name="BatchReference" xml:space="preserve">
    <value>Batch Reference</value>
  </data>
  <data name="IsActive" xml:space="preserve">
    <value>IsActive?</value>
  </data>
  <data name="ClientName" xml:space="preserve">
    <value>Client Name</value>
  </data>
  <data name="InternalPurchaseOrderNo" xml:space="preserve">
    <value>IPO No</value>
  </data>
  <data name="Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="SupplierPrice" xml:space="preserve">
    <value>Supplier Price</value>
  </data>
  <data name="UpliftPrice" xml:space="preserve">
    <value>Uplift Sell Price</value>
  </data>
  <data name="InternalPO" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="POQuoteNo" xml:space="preserve">
    <value>Price Request No</value>
  </data>
  <data name="IPurchaseOrderNo" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="IsPoHub" xml:space="preserve">
    <value>PoHub?</value>
  </data>
  <data name="IPO" xml:space="preserve">
    <value>IPO</value>
  </data>
  <data name="IPOBOM" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="IPOLineNotes" xml:space="preserve">
    <value>IPO line notes</value>
  </data>
  <data name="Log_Details" xml:space="preserve">
    <value>Log Details</value>
  </data>
  <data name="PurchaseQuote" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="PurchaseQuoteDate" xml:space="preserve">
    <value>Price Request Date</value>
  </data>
  <data name="AddMore" xml:space="preserve">
    <value>Add More</value>
  </data>
  <data name="BOMChk" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="DateFrom" xml:space="preserve">
    <value>Date From</value>
  </data>
  <data name="DateTo" xml:space="preserve">
    <value>Date To</value>
  </data>
  <data name="IPOBOMName" xml:space="preserve">
    <value>HUBRFQ Name</value>
  </data>
  <data name="CurrentSupplier" xml:space="preserve">
    <value>Current Supplier</value>
  </data>
  <data name="QuoteRequired" xml:space="preserve">
    <value>Quote Required </value>
  </data>
  <data name="UPLiftPercent" xml:space="preserve">
    <value>Uplift Price (%)</value>
  </data>
  <data name="ClientInvoice" xml:space="preserve">
    <value>Client Invoice </value>
  </data>
  <data name="ClientInvoiceDateFrom" xml:space="preserve">
    <value>Client Invoice Date From</value>
  </data>
  <data name="ClientInvoiceDateTo" xml:space="preserve">
    <value>Client Invoice Date To</value>
  </data>
  <data name="InternalPurchaseOrder" xml:space="preserve">
    <value>Internal Purchase Order</value>
  </data>
  <data name="IsGrouped" xml:space="preserve">
    <value>Is Grouped?</value>
  </data>
  <data name="Duration" xml:space="preserve">
    <value>Duration</value>
  </data>
  <data name="FactorySealed" xml:space="preserve">
    <value>Factory Sealed (Y/N)</value>
  </data>
  <data name="LeadTime" xml:space="preserve">
    <value>Lead Time (Weeks)</value>
  </data>
  <data name="MSL" xml:space="preserve">
    <value>MSL</value>
  </data>
  <data name="SPQ" xml:space="preserve">
    <value>Standard Pack Quantity (SPQ)</value>
  </data>
  <data name="Part" xml:space="preserve">
    <value>Part</value>
  </data>
  <data name="EstimatedShippingCost" xml:space="preserve">
    <value>Estimated Shipping Cost</value>
  </data>
  <data name="LeadTimeWKS" xml:space="preserve">
    <value>Lead Time (Weeks)</value>
  </data>
  <data name="LTB" xml:space="preserve">
    <value>Last time buy (LTB) - (Y/N)</value>
  </data>
  <data name="ManufacturerName" xml:space="preserve">
    <value>Manufacturer Name</value>
  </data>
  <data name="MOQ" xml:space="preserve">
    <value>Minimum Order Quantity (MOQ)</value>
  </data>
  <data name="PackageType" xml:space="preserve">
    <value>Package Type</value>
  </data>
  <data name="PartMPNQuoted" xml:space="preserve">
    <value>MPN Quoted</value>
  </data>
  <data name="ProductType" xml:space="preserve">
    <value>Product Type</value>
  </data>
  <data name="ROHSYN" xml:space="preserve">
    <value>Rohs (Y/N)</value>
  </data>
  <data name="TotalQSA" xml:space="preserve">
    <value>Total quantity of stock available</value>
  </data>
  <data name="UnitPriceUSD" xml:space="preserve">
    <value>Unit Price</value>
  </data>
  <data name="TQSA" xml:space="preserve">
    <value>Total Quantity of Stock Available</value>
  </data>
  <data name="ROHSStatus" xml:space="preserve">
    <value>ROHS Status (Y/N)</value>
  </data>
  <data name="HUBRFQ" xml:space="preserve">
    <value>Assign HUBRFQ</value>
  </data>
  <data name="InternalPOLines_Edit" xml:space="preserve">
    <value>Edit Internal Purchase Order Line</value>
  </data>
  <data name="OnlyFromIPO" xml:space="preserve">
    <value>Only From IPO</value>
  </data>
  <data name="CompetitorBestoffer" xml:space="preserve">
    <value>Competitor Best offer</value>
  </data>
  <data name="CustomerDecisionDate" xml:space="preserve">
    <value>Customer Decision Date</value>
  </data>
  <data name="LastTimeBuy" xml:space="preserve">
    <value>Last time buy</value>
  </data>
  <data name="Obsolete" xml:space="preserve">
    <value>Obsolete</value>
  </data>
  <data name="OrderToPlace" xml:space="preserve">
    <value>Order To Place</value>
  </data>
  <data name="PQA" xml:space="preserve">
    <value>Partial Quantity Acceptable</value>
  </data>
  <data name="QuoteValidityRequired" xml:space="preserve">
    <value>Quote Validity Required</value>
  </data>
  <data name="RefirbsAcceptable" xml:space="preserve">
    <value>Refurbs Acceptable</value>
  </data>
  <data name="RequirementforTraceability" xml:space="preserve">
    <value>Requirement for Traceability</value>
  </data>
  <data name="RFQClosingDate" xml:space="preserve">
    <value>RFQ Closing Date</value>
  </data>
  <data name="TargetSellPrice" xml:space="preserve">
    <value>Target Sell Price</value>
  </data>
  <data name="TestingRequired" xml:space="preserve">
    <value>Testing Required</value>
  </data>
  <data name="Releasedby" xml:space="preserve">
    <value>Released by</value>
  </data>
  <data name="Requestedby" xml:space="preserve">
    <value>Requested by</value>
  </data>
  <data name="AssignedUser" xml:space="preserve">
    <value>Assigned User</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Region</value>
  </data>
  <data name="RegionName" xml:space="preserve">
    <value>Region Name</value>
  </data>
  <data name="PohubOnly" xml:space="preserve">
    <value>PoHub Only</value>
  </data>
  <data name="RefNo" xml:space="preserve">
    <value>Reference No</value>
  </data>
  <data name="InsuranceHistory" xml:space="preserve">
    <value>Insurance History</value>
  </data>
  <data name="InsuranceFileNo" xml:space="preserve">
    <value>Insurance File No</value>
  </data>
  <data name="InsuredAmount" xml:space="preserve">
    <value>Insured Amount</value>
  </data>
  <data name="AgencySOAuthoriser" xml:space="preserve">
    <value>Agency SO Authoriser</value>
  </data>
  <data name="Agency" xml:space="preserve">
    <value>Agency</value>
  </data>
  <data name="StopStatus" xml:space="preserve">
    <value>Stop Status</value>
  </data>
  <data name="ClientInvoiceNo" xml:space="preserve">
    <value>Client Invoice No</value>
  </data>
  <data name="Consildate" xml:space="preserve">
    <value>Consolidate</value>
  </data>
  <data name="EAU" xml:space="preserve">
    <value>Estimated Annual Usage</value>
  </data>
  <data name="ApplyPOBankFee" xml:space="preserve">
    <value>Apply PO Bank Fee</value>
  </data>
  <data name="TermsName" xml:space="preserve">
    <value>Supplier Term</value>
  </data>
  <data name="PoDeliveryDate" xml:space="preserve">
    <value>PO Delivery Date</value>
  </data>
  <data name="CustDateRequired" xml:space="preserve">
    <value>Customer Date Required</value>
  </data>
  <data name="LockUpdateClient" xml:space="preserve">
    <value>Lock update from Client</value>
  </data>
  <data name="ClientDebitNo" xml:space="preserve">
    <value>Clients Debit No</value>
  </data>
  <data name="CC" xml:space="preserve">
    <value>CC</value>
  </data>
  <data name="CustomerTargetPrice" xml:space="preserve">
    <value>Customer Target Price</value>
  </data>
  <data name="IsNoBid" xml:space="preserve">
    <value>No-Bid</value>
  </data>
  <data name="HUBRFQIsNoBid" xml:space="preserve">
    <value>HUBRFQ No-Bid</value>
  </data>
  <data name="HUBRFQIsReleased" xml:space="preserve">
    <value>HUBRFQ Released</value>
  </data>
  <data name="PreviousReviewDate" xml:space="preserve">
    <value>Previous Review Date</value>
  </data>
  <data name="BuyPrice1" xml:space="preserve">
    <value>Buy Price</value>
  </data>
  <data name="SellCurrency" xml:space="preserve">
    <value>Sell Currency</value>
  </data>
  <data name="GIN" xml:space="preserve">
    <value>GIN</value>
  </data>
  <data name="SOLineNo" xml:space="preserve">
    <value>Clone SO Line No</value>
  </data>
  <data name="ReqBOMName" xml:space="preserve">
    <value>BOM Name</value>
  </data>
  <data name="NoBidNotes" xml:space="preserve">
    <value>No-Bid Notes</value>
  </data>
  <data name="AlternativesAccepted" xml:space="preserve">
    <value>Alternatives Accepted</value>
  </data>
  <data name="RepeatBusiness" xml:space="preserve">
    <value>Regular/Repeat business</value>
  </data>
  <data name="RecievingNotes" xml:space="preserve">
    <value>Shipping and receiving instructions to wh only</value>
  </data>
  <data name="SupplierHistory" xml:space="preserve">
    <value>Supplier Change History</value>
  </data>
  <data name="IPONo" xml:space="preserve">
    <value>IPO No</value>
  </data>
  <data name="PartDetails" xml:space="preserve">
    <value>Part Details</value>
  </data>
  <data name="CustPO" xml:space="preserve">
    <value>Customer PO</value>
  </data>
  <data name="AssignTo" xml:space="preserve">
    <value>Assign To</value>
  </data>
  <data name="NotesToInvoice" xml:space="preserve">
    <value>Notes to Invoice</value>
  </data>
  <data name="CreditNoteNo" xml:space="preserve">
    <value>Credit Note No</value>
  </data>
  <data name="LastYearNew" xml:space="preserve">
    <value>Last year [spend | profit (%)]:</value>
  </data>
  <data name="YearToDateNew" xml:space="preserve">
    <value>Year to date [spend | profit (%)]:</value>
  </data>
  <data name="ShipSurchargePer" xml:space="preserve">
    <value>Shipping Surcharge (%)</value>
  </data>
  <data name="LotNumber" xml:space="preserve">
    <value>Lot Number</value>
  </data>
  <data name="DutyCodeRate" xml:space="preserve">
    <value>Duty Code&lt;br/&gt;(Rate %)</value>
  </data>
  <data name="NotifySales" xml:space="preserve">
    <value>Notify Salesperson?</value>
  </data>
  <data name="IsHazarders" xml:space="preserve">
    <value>Hazarders?</value>
  </data>
  <data name="ReqSerailNo" xml:space="preserve">
    <value>Serial No. required</value>
  </data>
  <data name="SubGroup" xml:space="preserve">
    <value>BOX</value>
  </data>
  <data name="SerialNo" xml:space="preserve">
    <value>Serial Number</value>
  </data>
  <data name="CompanyAddress" xml:space="preserve">
    <value>Company Address</value>
  </data>
  <data name="QtyTobeShpped" xml:space="preserve">
    <value>Shipped Quantity</value>
  </data>
  <data name="IncludeOrderSent" xml:space="preserve">
    <value>SO sent to customer</value>
  </data>
  <data name="OrderSenttoCust" xml:space="preserve">
    <value>SOs sent to customer only</value>
  </data>
  <data name="txtReason" xml:space="preserve">
    <value>Reason</value>
  </data>
  <data name="IsImportant" xml:space="preserve">
    <value>Mark as Important</value>
  </data>
  <data name="Contact2" xml:space="preserve">
    <value>CC communication notes to</value>
  </data>
  <data name="GTUpdateTitle" xml:space="preserve">
    <value>Title</value>
  </data>
  <data name="IsGTUpdateSendMail" xml:space="preserve">
    <value>Send mail to users</value>
  </data>
  <data name="IsGTUpdateShowHome" xml:space="preserve">
    <value>Popup show on Home</value>
  </data>
  <data name="GTAppUpdateVersion" xml:space="preserve">
    <value>GT Updated Version</value>
  </data>
  <data name="GTUpdateName" xml:space="preserve">
    <value>GT Update Name</value>
  </data>
  <data name="Contact2Details" xml:space="preserve">
    <value>Optional: Select an additional salesperson to receive HUBRFQ communication notes from Purchase Hub</value>
  </data>
  <data name="CustomerRejectionNo" xml:space="preserve">
    <value>Customer Rejection No</value>
  </data>
  <data name="NonPreferredCompany" xml:space="preserve">
    <value>Non Preferred Company</value>
  </data>
  <data name="MaxExpoCreditLimit" xml:space="preserve">
    <value>Max Exposure Credit Limit</value>
  </data>
  <data name="ContractNo" xml:space="preserve">
    <value>Contract No</value>
  </data>
  <data name="IsAvoidable" xml:space="preserve">
    <value>Avoidable</value>
  </data>
  <data name="SupplierWarranty" xml:space="preserve">
    <value>Supplier Warranty</value>
  </data>
  <data name="Days1" xml:space="preserve">
    <value>01- 29 Days</value>
  </data>
  <data name="SendMailToIPO" xml:space="preserve">
    <value>Send email to IPO Purchasing</value>
  </data>
  <data name="SendMailToSales" xml:space="preserve">
    <value>Send email to sales and Support Team Member</value>
  </data>
  <data name="PrintHazWarning" xml:space="preserve">
    <value>Print hazardous warning</value>
  </data>
  <data name="DateConfirmed" xml:space="preserve">
    <value>Date Confirmed</value>
  </data>
  <data name="InternalLog" xml:space="preserve">
    <value>Internal Log</value>
  </data>
  <data name="Important" xml:space="preserve">
    <value>Only show marked as important</value>
  </data>
  <data name="ReceiptNo" xml:space="preserve">
    <value>Receipt No</value>
  </data>
  <data name="TestingRecommended" xml:space="preserve">
    <value>Testing Recommended</value>
  </data>
  <data name="IsSorcingImage" xml:space="preserve">
    <value>Images attached</value>
  </data>
  <data name="EPR" xml:space="preserve">
    <value>EPR</value>
  </data>
  <data name="SelectedShip" xml:space="preserve">
    <value>Total Ship Value</value>
  </data>
  <data name="StatusReason" xml:space="preserve">
    <value>Hold Reason</value>
  </data>
  <data name="ImportStatus" xml:space="preserve">
    <value>Import Status</value>
  </data>
  <data name="ShipInCostHis" xml:space="preserve">
    <value>Ship In Cost History</value>
  </data>
  <data name="TotalValue" xml:space="preserve">
    <value>Total Value</value>
  </data>
  <data name="QuoteStatus" xml:space="preserve">
    <value>Quote Status</value>
  </data>
  <data name="EndDate" xml:space="preserve">
    <value>End Date</value>
  </data>
  <data name="ReceivedEndDate" xml:space="preserve">
    <value>Received End Date</value>
  </data>
  <data name="ReceivedStartDate" xml:space="preserve">
    <value>Received Start Date</value>
  </data>
  <data name="AllowViewTeamReport" xml:space="preserve">
    <value>Allow viewing Team, Division or Company reports</value>
  </data>
  <data name="CustomerReq" xml:space="preserve">
    <value>Customer Requirement</value>
  </data>
  <data name="SalesOrderNumber" xml:space="preserve">
    <value>Sales Order Number</value>
  </data>
  <data name="ShippingNoteToWHOnly" xml:space="preserve">
    <value>Shipping Note to WH only</value>
  </data>
  <data name="PromiseReason" xml:space="preserve">
    <value>Promise Reason</value>
  </data>
  <data name="PowerBIUserName" xml:space="preserve">
    <value>Power BI Username</value>
  </data>
  <data name="PowerBIPassword" xml:space="preserve">
    <value>Power BI Password</value>
  </data>
  <data name="PromiseReasonLog" xml:space="preserve">
    <value>Promise Reason Log</value>
  </data>
  <data name="DateImportFrom" xml:space="preserve">
    <value>Date Import From</value>
  </data>
  <data name="DateImportTo" xml:space="preserve">
    <value>Date Import To</value>
  </data>
  <data name="BOMCode" xml:space="preserve">
    <value>BOM Code</value>
  </data>
  <data name="RecordsProcessed" xml:space="preserve">
    <value>Records Processed</value>
  </data>
  <data name="RecordsRemaining" xml:space="preserve">
    <value>Records Remaining</value>
  </data>
  <data name="DefaultCurrency" xml:space="preserve">
    <value>Default Currency</value>
  </data>
  <data name="RecordRemaining" xml:space="preserve">
    <value>Record Remaining</value>
  </data>
  <data name="MarkComplete" xml:space="preserve">
    <value>Mark Complete</value>
  </data>
  <data name="HUBRFQName" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="PreferredWarehouse" xml:space="preserve">
    <value>Preferred Warehouse</value>
  </data>
  <data name="FreightPercentage" xml:space="preserve">
    <value>Uplift by percentage</value>
  </data>
  <data name="IncreaseFreight" xml:space="preserve">
    <value>Match freight to the Shipping Cost on Invoice</value>
  </data>
  <data name="PackagingSlip" xml:space="preserve">
    <value>Packaging Slip</value>
  </data>
  <data name="NotesForInv" xml:space="preserve">
    <value>Notes to Print on Invoice</value>
  </data>
  <data name="ShipToVATNo" xml:space="preserve">
    <value>ShipTo VAT No</value>
  </data>
  <data name="AppliedSurcharge" xml:space="preserve">
    <value>Shipping Surcharge:</value>
  </data>
  <data name="ApplyDivisionHeader" xml:space="preserve">
    <value>Apply Division Header</value>
  </data>
  <data name="SplitQty" xml:space="preserve">
    <value>Quantity Split</value>
  </data>
  <data name="InvoiceAutoExport" xml:space="preserve">
    <value>Activate Invoice Auto Export</value>
  </data>
  <data name="Priority" xml:space="preserve">
    <value>Priority</value>
  </data>
  <data name="ClientBillTo" xml:space="preserve">
    <value>Client Bill To</value>
  </data>
  <data name="CustomerCode" xml:space="preserve">
    <value>Customer Code</value>
  </data>
  <data name="SupportTeamMember" xml:space="preserve">
    <value>Support Team Member to update</value>
  </data>
  <data name="SalesPersion" xml:space="preserve">
    <value>Support Team Member</value>
  </data>
  <data name="InvoiceHold" xml:space="preserve">
    <value>Hold Export</value>
  </data>
  <data name="IncludeOnHold" xml:space="preserve">
    <value>Include on-Hold</value>
  </data>
  <data name="countryOforigin" xml:space="preserve">
    <value>Country Of Origin</value>
  </data>
  <data name="HtcCode" xml:space="preserve">
    <value>HTC Code</value>
  </data>
  <data name="HTSCode" xml:space="preserve">
    <value>HTS Code</value>
  </data>
  <data name="LifeCycleStage" xml:space="preserve">
    <value>Part Status</value>
  </data>
  <data name="PackagingSize" xml:space="preserve">
    <value>Packaging Size</value>
  </data>
  <data name="Packing" xml:space="preserve">
    <value>Packaging</value>
  </data>
  <data name="SearchType" xml:space="preserve">
    <value>Search Type</value>
  </data>
  <data name="AveragePrice" xml:space="preserve">
    <value>Avg. Price</value>
  </data>
  <data name="AveragePriceLytica" xml:space="preserve">
    <value>Avg. Price (50th Percentile)</value>
  </data>
  <data name="MarketLeadingLytica" xml:space="preserve">
    <value>Market Leading</value>
  </data>
  <data name="Descriptions" xml:space="preserve">
    <value>Descriptions</value>
  </data>
  <data name="Duty" xml:space="preserve">
    <value>Duty % </value>
  </data>
  <data name="IHSProduct" xml:space="preserve">
    <value>IHS Product</value>
  </data>
  <data name="SearchtxtPartNo" xml:space="preserve">
    <value>Part No</value>
  </data>
  <data name="IHSProductName" xml:space="preserve">
    <value>IHS Product</value>
  </data>
  <data name="Alternatives" xml:space="preserve">
    <value>Add all suggested Rebound alternatives</value>
  </data>
  <data name="PriceRequest" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="LastTimeBuyDate" xml:space="preserve">
    <value>Last Time Buy Date</value>
  </data>
  <data name="LastTimeShipDate" xml:space="preserve">
    <value>Last Time Ship Date</value>
  </data>
  <data name="LifeStatus" xml:space="preserve">
    <value>Life Status</value>
  </data>
  <data name="DeliveryDateRequired" xml:space="preserve">
    <value>Delivery Date Required</value>
  </data>
  <data name="NotifyothersoftheNewReq" xml:space="preserve">
    <value>Notify others of the New Requirement</value>
  </data>
  <data name="UserMessaage" xml:space="preserve">
    <value>User Message</value>
  </data>
  <data name="FactorySealed2" xml:space="preserve">
    <value>Factory Sealed</value>
  </data>
  <data name="ADLoginName" xml:space="preserve">
    <value>AD Login Name</value>
  </data>
  <data name="EmployeeNames" xml:space="preserve">
    <value>Employee Name</value>
  </data>
  <data name="ECCNCode" xml:space="preserve">
    <value>ECCN Code</value>
  </data>
  <data name="AppliedShippingSurcharge" xml:space="preserve">
    <value>Apply Shipping Surcharge ?</value>
  </data>
  <data name="ShippingSurcharge" xml:space="preserve">
    <value>Shipping Surcharge</value>
  </data>
  <data name="ihsInformation" xml:space="preserve">
    <value>IHS Information</value>
  </data>
  <data name="DivisionHeader" xml:space="preserve">
    <value>Division Header</value>
  </data>
  <data name="SalesDivision" xml:space="preserve">
    <value>Division Sales</value>
  </data>
  <data name="CheckedStatus" xml:space="preserve">
    <value>Checked ?</value>
  </data>
  <data name="IsSendShipmentNotification" xml:space="preserve">
    <value>Send Shipment Notification?</value>
  </data>
  <data name="REQStatus" xml:space="preserve">
    <value>REQ Line Status</value>
  </data>
  <data name="EORINumber" xml:space="preserve">
    <value>EORI No</value>
  </data>
  <data name="ParentRequirementNo" xml:space="preserve">
    <value>Parent Requirement No</value>
  </data>
  <data name="InsuredAmountCurrency" xml:space="preserve">
    <value>Insured Amount Currency</value>
  </data>
  <data name="Profit" xml:space="preserve">
    <value>Profit</value>
  </data>
  <data name="QuantityAdvised" xml:space="preserve">
    <value>Quantity Advised</value>
  </data>
  <data name="SalesContact" xml:space="preserve">
    <value>Sales Contact</value>
  </data>
  <data name="ShortageQuantity" xml:space="preserve">
    <value>Shortage Quantity</value>
  </data>
  <data name="ShortageValue" xml:space="preserve">
    <value>Shortage Value</value>
  </data>
  <data name="QuantityOrder" xml:space="preserve">
    <value>Quantity Order</value>
  </data>
  <data name="DateReceived" xml:space="preserve">
    <value>Received Date</value>
  </data>
  <data name="ShortageFulfilled" xml:space="preserve">
    <value>Should the shortage order be fulfilled?</value>
  </data>
  <data name="IsShortageRefundIssue" xml:space="preserve">
    <value>Credit refund given?</value>
  </data>
  <data name="GoodsInNoteNo" xml:space="preserve">
    <value>Goods In No</value>
  </data>
  <data name="IsShortageRefundIssued" xml:space="preserve">
    <value>Will a credit / refund be issued to cover the value of the shortage?</value>
  </data>
  <data name="ShortageRefundIssue" xml:space="preserve">
    <value>Additional comments</value>
  </data>
  <data name="Comment" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="DebitNoteNo" xml:space="preserve">
    <value>Debit Note Number</value>
  </data>
  <data name="ShortShipOrderFulFill" xml:space="preserve">
    <value>Should the shortage order be fulfilled?</value>
  </data>
  <data name="PartWatchMatch" xml:space="preserve">
    <value>PartWatch Match</value>
  </data>
  <data name="CloneSendHub" xml:space="preserve">
    <value>Send this to Purchase Hub</value>
  </data>
  <data name="IsOrderViaIPOonly" xml:space="preserve">
    <value>Order via IPO only?</value>
  </data>
  <data name="ProductReqTesting" xml:space="preserve">
    <value>Supplier product may need testing</value>
  </data>
  <data name="RepeatOrder" xml:space="preserve">
    <value>Repeat Order</value>
  </data>
  <data name="SupplierApproval" xml:space="preserve">
    <value>Supplier Approval</value>
  </data>
  <data name="SupplierVatNumber" xml:space="preserve">
    <value>Supplier VAT Number</value>
  </data>
  <data name="WebSiteAddress" xml:space="preserve">
    <value>Supplier Website</value>
  </data>
  <data name="SAApprovedOrder" xml:space="preserve">
    <value>Approved Orders in Last 12 months</value>
  </data>
  <data name="SAPurchasingMethod" xml:space="preserve">
    <value>Purchasing Method</value>
  </data>
  <data name="SATradeRefOne" xml:space="preserve">
    <value>Trade Reference 1</value>
  </data>
  <data name="SATradeRefThree" xml:space="preserve">
    <value>Trade Reference 3</value>
  </data>
  <data name="SATradeRefTwo" xml:space="preserve">
    <value>Trade Reference 2</value>
  </data>
  <data name="SAWeblinkEvidence" xml:space="preserve">
    <value>Franchise Weblink or evidence</value>
  </data>
  <data name="SAPrecogsSupplier" xml:space="preserve">
    <value>Customer Defined Vendor</value>
  </data>
  <data name="SADevicePicture" xml:space="preserve">
    <value>Device Pictures</value>
  </data>
  <data name="SAManufacturersPictures" xml:space="preserve">
    <value>Manufacturers Label Pictures</value>
  </data>
  <data name="SATraceabilityPictures" xml:space="preserve">
    <value>Traceability Pictures</value>
  </data>
  <data name="SupplierType" xml:space="preserve">
    <value>Supplier Type</value>
  </data>
  <data name="CorrectDateCode" xml:space="preserve">
    <value>Date Code</value>
  </data>
  <data name="CorrectHIC" xml:space="preserve">
    <value>Query - HIC Status</value>
  </data>
  <data name="CorrectManufacturer" xml:space="preserve">
    <value>Query - Manufacturer</value>
  </data>
  <data name="CorrectMSL" xml:space="preserve">
    <value>Query - MSL</value>
  </data>
  <data name="CorrectPackage" xml:space="preserve">
    <value>Query - Packaging Type</value>
  </data>
  <data name="CorrectPartNo" xml:space="preserve">
    <value>Query - Part Number</value>
  </data>
  <data name="HICStatus" xml:space="preserve">
    <value>HIC Status</value>
  </data>
  <data name="IsDateCodeCorrect" xml:space="preserve">
    <value>Correct Date Code?</value>
  </data>
  <data name="IsFullQuantityReceived" xml:space="preserve">
    <value>Full Quantity Received?</value>
  </data>
  <data name="IsHICCorrect" xml:space="preserve">
    <value>Raise HIC Status Query?</value>
  </data>
  <data name="IsManufacturerCorrect" xml:space="preserve">
    <value>Correct Manufacturer?</value>
  </data>
  <data name="IsMSLCorrect" xml:space="preserve">
    <value>MSL Correct?</value>
  </data>
  <data name="IsPackageCorrect" xml:space="preserve">
    <value>Packaging Type Correct?</value>
  </data>
  <data name="IsPartNoCorrect" xml:space="preserve">
    <value>Correct Part Number?</value>
  </data>
  <data name="BakingLevelAdded" xml:space="preserve">
    <value>Baking Label added</value>
  </data>
  <data name="CorrectRohsStatus" xml:space="preserve">
    <value>Query - ROHS Status</value>
  </data>
  <data name="EnhancedInpectionRequired" xml:space="preserve">
    <value>Enhanced Inspection Required</value>
  </data>
  <data name="GeneralInspectionNotes" xml:space="preserve">
    <value>General Inspection Notes</value>
  </data>
  <data name="IsInspectionConducted" xml:space="preserve">
    <value>Inspection conducted in accordance with Supplier Type, Batch Sample Table RGFM09 and Material Inspection Guidelines RGFM42</value>
  </data>
  <data name="IsRohsStatusCorrect" xml:space="preserve">
    <value>ROHS Status Correct?</value>
  </data>
  <data name="LotCodeReq" xml:space="preserve">
    <value>LOT Code Required</value>
  </data>
  <data name="PackageBreakdownInfo" xml:space="preserve">
    <value>Query - Packaging</value>
  </data>
  <data name="PackagingBreakdown" xml:space="preserve">
    <value>Packaging Breakdown</value>
  </data>
  <data name="DateCodeReceived" xml:space="preserve">
    <value>Date Code Received</value>
  </data>
  <data name="IsDateCodeRequired" xml:space="preserve">
    <value>All Date Code Required?</value>
  </data>
  <data name="EmailTo" xml:space="preserve">
    <value>Email To</value>
  </data>
  <data name="EmailToPurchasing" xml:space="preserve">
    <value>Purchasing</value>
  </data>
  <data name="EmailToQualityApproval" xml:space="preserve">
    <value>Quality Approval</value>
  </data>
  <data name="EmailToSales" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="GIAllQueries" xml:space="preserve">
    <value>All GI Query</value>
  </data>
  <data name="GIQueryApproved" xml:space="preserve">
    <value>Approved</value>
  </data>
  <data name="GIQueryReply" xml:space="preserve">
    <value>Query Reply</value>
  </data>
  <data name="PDFReportRequired" xml:space="preserve">
    <value>PDF Report Required</value>
  </data>
  <data name="QuarantineProduct" xml:space="preserve">
    <value>Quarantine Product</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="ApprovedBy" xml:space="preserve">
    <value>Approved By</value>
  </data>
  <data name="ReqLotNo" xml:space="preserve">
    <value>Lot Code required</value>
  </data>
  <data name="IPOPurchasing" xml:space="preserve">
    <value>IPO Purchasing email group</value>
  </data>
  <data name="MaxLengthCountingVIew" xml:space="preserve">
    <value>Character Count (2000 chrs max) : </value>
  </data>
  <data name="AllSourcingResults" xml:space="preserve">
    <value>Sourcing Results</value>
  </data>
  <data name="LineDetails" xml:space="preserve">
    <value>Line Details</value>
  </data>
  <data name="EnhancedInspection" xml:space="preserve">
    <value>Enhanced Inspection</value>
  </data>
  <data name="CheckedBy" xml:space="preserve">
    <value>Inspected by</value>
  </data>
  <data name="StockImagesList" xml:space="preserve">
    <value>Image List</value>
  </data>
  <data name="IsExportPDF" xml:space="preserve">
    <value>Is Export To PDF?</value>
  </data>
  <data name="IsExportWord" xml:space="preserve">
    <value>Is Export To Word?</value>
  </data>
  <data name="IsHighRisk" xml:space="preserve">
    <value>Sanctions?</value>
  </data>
  <data name="SATrmAndCndtnAttchmnt" xml:space="preserve">
    <value>Term &amp; Condition Attachment </value>
  </data>
  <data name="SATermAndCondetion" xml:space="preserve">
    <value>General Terms and Condition of purchase.pdf</value>
  </data>
  <data name="ApplyTo" xml:space="preserve">
    <value>Apply To</value>
  </data>
  <data name="ApplyToCatagory" xml:space="preserve">
    <value>Apply To Catagory</value>
  </data>
  <data name="WarningName" xml:space="preserve">
    <value>Warning Name</value>
  </data>
  <data name="WarningText" xml:space="preserve">
    <value>Warning Message</value>
  </data>
  <data name="GoodsInNumber" xml:space="preserve">
    <value>Goods In Number</value>
  </data>
  <data name="PartNumber" xml:space="preserve">
    <value>Part Number</value>
  </data>
  <data name="PackagingType" xml:space="preserve">
    <value>Packaging Type</value>
  </data>
  <data name="QtyReceived" xml:space="preserve">
    <value>Quantity Received</value>
  </data>
  <data name="RohsStatusGI" xml:space="preserve">
    <value>ROHS Status</value>
  </data>
  <data name="QryMsgNotifyTo" xml:space="preserve">
    <value>Notify To:</value>
  </data>
  <data name="QueryMsgApprovalStatus" xml:space="preserve">
    <value>Approval Status</value>
  </data>
  <data name="QMIsPurchassingApproved" xml:space="preserve">
    <value>Purchasing Approval</value>
  </data>
  <data name="QMIsQualityApproved" xml:space="preserve">
    <value>Quality Approval</value>
  </data>
  <data name="QMIsSalesApproved" xml:space="preserve">
    <value>Sales Approval</value>
  </data>
  <data name="QMApprovalName" xml:space="preserve">
    <value>Sent To</value>
  </data>
  <data name="QMApprovedDate" xml:space="preserve">
    <value>Date and Time</value>
  </data>
  <data name="QMDepartment" xml:space="preserve">
    <value>Department</value>
  </data>
  <data name="QMRaisedBy" xml:space="preserve">
    <value>Raised By</value>
  </data>
  <data name="QMStatus" xml:space="preserve">
    <value>Status</value>
  </data>
  <data name="QueryDateCode" xml:space="preserve">
    <value>Query-Date Code</value>
  </data>
  <data name="QMSendTO" xml:space="preserve">
    <value>Send To:</value>
  </data>
  <data name="Factory" xml:space="preserve">
    <value>Factory</value>
  </data>
  <data name="NumberOfPacks" xml:space="preserve">
    <value>Number of Packs</value>
  </data>
  <data name="PackagingTotal" xml:space="preserve">
    <value>Packaging Total:</value>
  </data>
  <data name="PackSize" xml:space="preserve">
    <value>Pack Size</value>
  </data>
  <data name="QueryQuantity" xml:space="preserve">
    <value>Query- Quantity</value>
  </data>
  <data name="Sealed" xml:space="preserve">
    <value>Sealed</value>
  </data>
  <data name="ManufacturerSupplied" xml:space="preserve">
    <value>Manufacturer Supplied</value>
  </data>
  <data name="notexportedinvoices" xml:space="preserve">
    <value>Not Exported Invoices</value>
  </data>
  <data name="DefaultDivisionHeader" xml:space="preserve">
    <value>Default Division Header</value>
  </data>
  <data name="WarningMessage" xml:space="preserve">
    <value>Warning Message</value>
  </data>
  <data name="ADEmail" xml:space="preserve">
    <value>AD Email</value>
  </data>
  <data name="IsECCNStatus" xml:space="preserve">
    <value>ECCN Warning</value>
  </data>
  <data name="ECCN_Code" xml:space="preserve">
    <value>ECCN Code</value>
  </data>
  <data name="ShortShipmentHistory" xml:space="preserve">
    <value>Short Shipment History</value>
  </data>
  <data name="IsCancel" xml:space="preserve">
    <value>Cancel?</value>
  </data>
  <data name="ShortShipment" xml:space="preserve">
    <value>Short Shipment</value>
  </data>
  <data name="ChangeStatus" xml:space="preserve">
    <value>&lt;b&gt;Change Status&lt;/b&gt;</value>
  </data>
  <data name="ShowExchangeRate" xml:space="preserve">
    <value>Show Exchange Rate</value>
  </data>
  <data name="PONumber" xml:space="preserve">
    <value>PO Number</value>
  </data>
  <data name="ActeoneTest" xml:space="preserve">
    <value>Acetone Test</value>
  </data>
  <data name="Fail" xml:space="preserve">
    <value>Fail</value>
  </data>
  <data name="Isopropryle" xml:space="preserve">
    <value>Isopropyl Alcohol Test</value>
  </data>
  <data name="NA" xml:space="preserve">
    <value>N/A</value>
  </data>
  <data name="Pass" xml:space="preserve">
    <value>Pass</value>
  </data>
  <data name="ShouldMailBeSenttoLineManager" xml:space="preserve">
    <value>Line Manager Approval</value>
  </data>
  <data name="ShouldMailBeSenttoSupplier" xml:space="preserve">
    <value>Email Supplier T&amp;C</value>
  </data>
  <data name="QMSConfigApprover" xml:space="preserve">
    <value>Manage Approvers:</value>
  </data>
  <data name="QMSCurrentPurchasingApprover" xml:space="preserve">
    <value>Current Purchasing Approver:</value>
  </data>
  <data name="QMSCurrentSalesApprover" xml:space="preserve">
    <value>Current Sales Approver:</value>
  </data>
  <data name="QMSInvitePurchasingApprover" xml:space="preserve">
    <value>Invite new Purchasing Approver:</value>
  </data>
  <data name="QMSInviteSalesApprover" xml:space="preserve">
    <value>Invite new Sales Approver:</value>
  </data>
  <data name="QueryBakingLevel" xml:space="preserve">
    <value>Query - Baking</value>
  </data>
  <data name="SONumber" xml:space="preserve">
    <value>SO Number</value>
  </data>
  <data name="GIQCC" xml:space="preserve">
    <value>CC</value>
  </data>
  <data name="BatchCode" xml:space="preserve">
    <value>Batch Code</value>
  </data>
  <data name="AuthorisedDate" xml:space="preserve">
    <value>Authorised Date</value>
  </data>
  <data name="AuthorisedNote" xml:space="preserve">
    <value>Authorised Note</value>
  </data>
  <data name="DeliveryDateCustomer" xml:space="preserve">
    <value>Delivery Date To Customer</value>
  </data>
  <data name="DeliveryDateRebound" xml:space="preserve">
    <value>Delivery Date To Rebound</value>
  </data>
  <data name="PaymentTerms" xml:space="preserve">
    <value>Payment Terms</value>
  </data>
  <data name="SAGTClintPo" xml:space="preserve">
    <value>GT Client For PO</value>
  </data>
  <data name="SaHubRFQQuote" xml:space="preserve">
    <value>HUBRFQ/Quote No</value>
  </data>
  <data name="SAMargin" xml:space="preserve">
    <value>Margin</value>
  </data>
  <data name="SAreboundPurchaser" xml:space="preserve">
    <value>Rebound Purchaser (Division)</value>
  </data>
  <data name="ShipFromCountry" xml:space="preserve">
    <value>Ship From Country</value>
  </data>
  <data name="TotalValueOfPo" xml:space="preserve">
    <value>Total Value Of PO and Currency</value>
  </data>
  <data name="SAChooseLineManger" xml:space="preserve">
    <value>Select Line Manager</value>
  </data>
  <data name="ShouldMailBeSenttoQuality" xml:space="preserve">
    <value>Quality Approval</value>
  </data>
  <data name="SAUpdateLineManager" xml:space="preserve">
    <value>Update Line Manager</value>
  </data>
  <data name="SAPartERAI" xml:space="preserve">
    <value>Has the Part been reported on ERAI</value>
  </data>
  <data name="ExportedDate" xml:space="preserve">
    <value>Exported Date</value>
  </data>
  <data name="RequesterNote" xml:space="preserve">
    <value>Requestor Notes</value>
  </data>
  <data name="SALineManagerNote" xml:space="preserve">
    <value>Line Manager Notes</value>
  </data>
  <data name="SAQualityNote" xml:space="preserve">
    <value>Quality Approval Notes</value>
  </data>
  <data name="SASupplierRMACount" xml:space="preserve">
    <value>RMAs in the last 12 months</value>
  </data>
  <data name="SAWarrantyperiod" xml:space="preserve">
    <value>Warranty Period</value>
  </data>
  <data name="SAPOCount" xml:space="preserve">
    <value>POs in the last 12 months</value>
  </data>
  <data name="Commodities" xml:space="preserve">
    <value>Commodities</value>
  </data>
  <data name="CreditInfo" xml:space="preserve">
    <value>Credit Information</value>
  </data>
  <data name="ElectronicSpend" xml:space="preserve">
    <value>Electronic Spend</value>
  </data>
  <data name="EndCustomer" xml:space="preserve">
    <value>End Customer or CEM</value>
  </data>
  <data name="FinalAssembly" xml:space="preserve">
    <value>Final Assembly</value>
  </data>
  <data name="FrequencyOfPurchase" xml:space="preserve">
    <value>Frequency Of Purchase</value>
  </data>
  <data name="Industry" xml:space="preserve">
    <value>Industry</value>
  </data>
  <data name="MFRBoardLevel" xml:space="preserve">
    <value>MFR Board Level</value>
  </data>
  <data name="ProspectType" xml:space="preserve">
    <value>Prospect Type</value>
  </data>
  <data name="Turnover" xml:space="preserve">
    <value>Turnover</value>
  </data>
  <data name="HealthRating" xml:space="preserve">
    <value>Health Rating %</value>
  </data>
  <data name="LimitedEstimate" xml:space="preserve">
    <value>Credit Limit Potential</value>
  </data>
  <data name="TaskDate" xml:space="preserve">
    <value>Task Date</value>
  </data>
  <data name="CreatedDateFrom" xml:space="preserve">
    <value>Created Date From</value>
  </data>
  <data name="CreatedDateTo" xml:space="preserve">
    <value>Created Date To</value>
  </data>
  <data name="TaskDateFrom" xml:space="preserve">
    <value>Task Date From</value>
  </data>
  <data name="TaskDateTo" xml:space="preserve">
    <value>Task Date To</value>
  </data>
  <data name="TaskStatus" xml:space="preserve">
    <value>Task Status</value>
  </data>
  <data name="TaskType" xml:space="preserve">
    <value>Task Type</value>
  </data>
  <data name="CurrentApprover" xml:space="preserve">
    <value>Current Approver</value>
  </data>
  <data name="QualityApprover" xml:space="preserve">
    <value>Quality inbox</value>
  </data>
  <data name="MFRLabel" xml:space="preserve">
    <value>MFR Label</value>
  </data>
  <data name="Review" xml:space="preserve">
    <value>Task for Review</value>
  </data>
  <data name="ReviewOnly" xml:space="preserve">
    <value>Task for Review</value>
  </data>
  <data name="GeneralNotes" xml:space="preserve">
    <value>General Notes</value>
  </data>
  <data name="PrintDateCode" xml:space="preserve">
    <value>Printable Date Code</value>
  </data>
  <data name="TaskReminderDate" xml:space="preserve">
    <value>Task Reminder Date</value>
  </data>
  <data name="IgnoreDivHeader" xml:space="preserve">
    <value>Ignore the selected ‘Apply Division header’ only on invoices</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="AccountNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="IsRestrictedProduct" xml:space="preserve">
    <value>Restricted Product</value>
  </data>
  <data name="EnablePowerApp" xml:space="preserve">
    <value>Enable Power App Notification</value>
  </data>
  <data name="SendEmail" xml:space="preserve">
    <value>Allow Email Sending on UAT</value>
  </data>
  <data name="EI_Charges" xml:space="preserve">
    <value>Enhanced Inspection Charges</value>
  </data>
  <data name="StockNo" xml:space="preserve">
    <value>Stock No</value>
  </data>
  <data name="SelectedProduct" xml:space="preserve">
    <value>Unmapped product list</value>
  </data>
  <data name="UnselectedProduct" xml:space="preserve">
    <value>Mapped product list</value>
  </data>
  <data name="SSNumber" xml:space="preserve">
    <value>Shipment No</value>
  </data>
  <data name="ExternalNotes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="IsGeneralNotesAdd" xml:space="preserve">
    <value>Is General Notes Add?</value>
  </data>
  <data name="SelectAllImage" xml:space="preserve">
    <value>Select All Image</value>
  </data>
  <data name="EccnMessage" xml:space="preserve">
    <value>Message</value>
  </data>
  <data name="EccnSubject" xml:space="preserve">
    <value>Subject</value>
  </data>
  <data name="IsECCNClientNotification" xml:space="preserve">
    <value>Client Notification</value>
  </data>
  <data name="GroupCode" xml:space="preserve">
    <value>Group Code</value>
  </data>
  <data name="GroupName" xml:space="preserve">
    <value>Group Name</value>
  </data>
  <data name="GroupDiscription" xml:space="preserve">
    <value>Group Description</value>
  </data>
  <data name="ShowUnReleased" xml:space="preserve">
    <value>Show Only Unreleased GI</value>
  </data>
  <data name="EIRequestComment" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="SIGIUnreleased" xml:space="preserve">
    <value>Please Provide Comment For Unreleased GI Line</value>
  </data>
  <data name="OGEL" xml:space="preserve">
    <value>OGEL</value>
  </data>
  <data name="EndDestinationCountry" xml:space="preserve">
    <value>End Destination Country</value>
  </data>
  <data name="EndUser" xml:space="preserve">
    <value>End User</value>
  </data>
  <data name="MilitaryUse" xml:space="preserve">
    <value>Military Use?</value>
  </data>
  <data name="OGELDestinationCountry" xml:space="preserve">
    <value>Destination Country</value>
  </data>
  <data name="OgelEndUser" xml:space="preserve">
    <value>End User</value>
  </data>
  <data name="OGELEUUForm" xml:space="preserve">
    <value>EUU Form</value>
  </data>
  <data name="OgelMilitaryUse" xml:space="preserve">
    <value>Military Use?</value>
  </data>
  <data name="OgelNumber" xml:space="preserve">
    <value>OGEL Number</value>
  </data>
  <data name="OGELSalesPerson" xml:space="preserve">
    <value>Sales Person</value>
  </data>
  <data name="OGELSOLine" xml:space="preserve">
    <value>SO Line Number</value>
  </data>
  <data name="OGELNo" xml:space="preserve">
    <value>OGEL No</value>
  </data>
  <data name="ShowReleased" xml:space="preserve">
    <value>Show Released GI Only</value>
  </data>
  <data name="DestinationCountry" xml:space="preserve">
    <value>Destination Country</value>
  </data>
  <data name="OGELLines" xml:space="preserve">
    <value>OGEL Lines</value>
  </data>
  <data name="ShippingSoon" xml:space="preserve">
    <value>Shipping Soon?</value>
  </data>
  <data name="SelectedECCNCode" xml:space="preserve">
    <value>ECCN Code List</value>
  </data>
  <data name="UnselectedECCNCode" xml:space="preserve">
    <value>ECCN Code List</value>
  </data>
  <data name="GILines_CloseInspection" xml:space="preserve">
    <value>Complete Inspection</value>
  </data>
  <data name="GILines_StartInspection" xml:space="preserve">
    <value>Start Inspection</value>
  </data>
  <data name="Cl_BarCodeScan" xml:space="preserve">
    <value>Barcodes scanned ticked?</value>
  </data>
  <data name="Cl_PartTicked" xml:space="preserve">
    <value>Part number filled/ticked</value>
  </data>
  <data name="Cl_PBComplete" xml:space="preserve">
    <value>Package breakdown completed</value>
  </data>
  <data name="Cl_PhotosAttached" xml:space="preserve">
    <value>Photos attached?</value>
  </data>
  <data name="Cl_QueryFormat" xml:space="preserve">
    <value>All open queries answered and approved?</value>
  </data>
  <data name="GiBarcodeScan" xml:space="preserve">
    <value>Barcode Recorded</value>
  </data>
  <data name="RecvCorrectManufacturer" xml:space="preserve">
    <value>Received - Manufacturer</value>
  </data>
  <data name="RecvCorrectMSL" xml:space="preserve">
    <value>Received - MSL</value>
  </data>
  <data name="RecvCorrectPackage" xml:space="preserve">
    <value>Received - Packaging Type</value>
  </data>
  <data name="RecvCorrectPartNo" xml:space="preserve">
    <value>Received - Part Number</value>
  </data>
  <data name="RecvCorrectRohsStatus" xml:space="preserve">
    <value>Received - ROHS Status</value>
  </data>
  <data name="GiBarcodeScanRemark" xml:space="preserve">
    <value>Barcode Scan Remark</value>
  </data>
  <data name="InspectionHis" xml:space="preserve">
    <value>Inspection History</value>
  </data>
  <data name="GIQueryStatus" xml:space="preserve">
    <value>Query Status</value>
  </data>
  <data name="PremierCustomer" xml:space="preserve">
    <value>Premier Customer</value>
  </data>
  <data name="SuplierType" xml:space="preserve">
    <value>Supplier Type</value>
  </data>
  <data name="LastOrderDate" xml:space="preserve">
    <value>Last Order Date</value>
  </data>
  <data name="SelectIndustryType" xml:space="preserve">
    <value>Select IndustryType</value>
  </data>
  <data name="UnselectedIndustryType" xml:space="preserve">
    <value>Unselected IndustryType</value>
  </data>
  <data name="Tier2PremierCustomer" xml:space="preserve">
    <value>Tier 2 Premier Customer</value>
  </data>
  <data name="IncludeCustomTemplate" xml:space="preserve">
    <value>Attach Customer Template</value>
  </data>
  <data name="KubConfig" xml:space="preserve">
    <value>Enable Kub</value>
  </data>
  <data name="BomUse" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="ContactName*" xml:space="preserve">
    <value>Contact Name*</value>
  </data>
  <data name="Mobile*" xml:space="preserve">
    <value>Contact(Mobile)*</value>
  </data>
  <data name="Password*" xml:space="preserve">
    <value>Password*</value>
  </data>
  <data name="SupUse" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="RequiredEndDate" xml:space="preserve">
    <value>HUBRFQ Required End Date</value>
  </data>
  <data name="RequiredStartDate" xml:space="preserve">
    <value>HUBRFQ Required Start Date</value>
  </data>
  <data name="CompanyPurchasingNotes" xml:space="preserve">
    <value>Purchasing Notes</value>
  </data>
  <data name="BOMManagerNoBid" xml:space="preserve">
    <value>BOM Manager No-Bid</value>
  </data>
  <data name="BOMManagerRecall" xml:space="preserve">
    <value>BOM Manager Recall</value>
  </data>
  <data name="BOMManagerRecallNoBid" xml:space="preserve">
    <value>BOM Manager Recall No-Bid</value>
  </data>
  <data name="BOMManagerRelease" xml:space="preserve">
    <value>BOM Manager Release</value>
  </data>
  <data name="BOMManagerPartialRelease" xml:space="preserve">
    <value>BOM Manager Partial Release</value>
  </data>
  <data name="Quantity Needed" xml:space="preserve">
    <value>Quantity Needed</value>
  </data>
  <data name="AS6081Label" xml:space="preserve">
    <value>Inhouse  AS6081  testing &lt;br&gt;required?*</value>
  </data>
  <data name="AS6081" xml:space="preserve">
    <value>Inhouse AS6081 testing required?</value>
  </data>
  <data name="IsAS6081Required" xml:space="preserve">
    <value>AS6081</value>
  </data>
  <data name="AS6081Filter" xml:space="preserve">
    <value>Inhouse AS6081 testing required?</value>
  </data>
  <data name="AS6081RCS" xml:space="preserve">
    <value>Reason For Chosen Supplier</value>
  </data>
  <data name="AS6081ROS" xml:space="preserve">
    <value>Risk Of Supplier</value>
  </data>
  <data name="DocumentSizeByte" xml:space="preserve">
    <value>Document File Size In Byte</value>
  </data>
  <data name="DocumentSizeMB" xml:space="preserve">
    <value>Document File Size In MB</value>
  </data>
  <data name="DocumentType" xml:space="preserve">
    <value>Document Type</value>
  </data>
  <data name="MaxDocumentSize" xml:space="preserve">
    <value>Max Document Size </value>
  </data>
  <data name="MailGroup" xml:space="preserve">
    <value>Mail to be send to</value>
  </data>
  <data name="AS6081TOS" xml:space="preserve">
    <value>Type Of Supplier</value>
  </data>
  <data name="AS6081MainInfoLabel" xml:space="preserve">
    <value>Inhouse AS6081 Part Included?</value>
  </data>
  <data name="ClientUPLiftPrice" xml:space="preserve">
    <value>Uplift Price</value>
  </data>
  <data name="EntertainmentType" xml:space="preserve">
    <value>Entertainment Type</value>
  </data>
  <data name="STO" xml:space="preserve">
    <value>Stock Transfer Order</value>
  </data>
  <data name="IndustryAreaType" xml:space="preserve">
    <value>Area discussed</value>
  </data>
  <data name="ChkSanctions" xml:space="preserve">
    <value>Sanctions</value>
  </data>
  <data name="EntertainDate" xml:space="preserve">
    <value>Entertainment Date</value>
  </data>
  <data name="GIRename" xml:space="preserve">
    <value>RENAME ATTACHMENT</value>
  </data>
  <data name="ClientInvoiceHeader" xml:space="preserve">
    <value>Client Invoice Header</value>
  </data>
  <data name="PreventSOR" xml:space="preserve">
    <value>Prevent SOR sign off</value>
  </data>
  <data name="TermsWarning" xml:space="preserve">
    <value>TermsWarning</value>
  </data>
  <data name="VirtualCostPrice" xml:space="preserve">
    <value>Virtual Cost Price</value>
  </data>
  <data name="LandedCostFrom" xml:space="preserve">
    <value>Landed Cost From</value>
  </data>
  <data name="LandedCostTo" xml:space="preserve">
    <value>Landed Cost To</value>
  </data>
  <data name="InsuranceCeriticateName" xml:space="preserve">
    <value>Insurance Ceriticate Name</value>
  </data>
  <data name="InsuranceCeriticateNumber" xml:space="preserve">
    <value>Insurance Ceriticate Number</value>
  </data>
  <data name="InspectionCompletedDetails" xml:space="preserve">
    <value>Inspection completed for the Goods In Line</value>
  </data>
  <data name="InspectionReopenDetails" xml:space="preserve">
    <value>Inspection re-open for the Goods In Line</value>
  </data>
  <data name="InspectionstartedDetails" xml:space="preserve">
    <value>Inspection started for the Goods In Line</value>
  </data>
  <data name="InsuranceCertificateNo" xml:space="preserve">
    <value>Certificate No</value>
  </data>
  <data name="Exported?" xml:space="preserve">
    <value>Exported?</value>
  </data>
  <data name="ApprovedforExport" xml:space="preserve">
    <value>Approved for Export</value>
  </data>
  <data name="ctlURNnumber" xml:space="preserve">
    <value>URNnumber</value>
  </data>
  <data name="AssignedUserLead" xml:space="preserve">
    <value>Assigned Lead</value>
  </data>
  <data name="AllowReadyToShip" xml:space="preserve">
    <value>Allow Ready To Ship?</value>
  </data>
  <data name="BomMailGroups" xml:space="preserve">
    <value>Assign Group</value>
  </data>
  <data name="CertificateCategory" xml:space="preserve">
    <value>Certificate Category</value>
  </data>
  <data name="SelectedCompanies" xml:space="preserve">
    <value>Selected Companies</value>
  </data>
  <data name="UnselectedCompanies" xml:space="preserve">
    <value>Unselected Companies</value>
  </data>
  <data name="AS6081new" xml:space="preserve">
    <value>AS6081 testing required?</value>
  </data>
  <data name="String" xml:space="preserve">
    <value />
  </data>
  <data name="AS6081Filternew" xml:space="preserve">
    <value>AS6081 testing required?</value>
  </data>
  <data name="ApplyOnCI" xml:space="preserve">
    <value>Apply On Client Invoice</value>
  </data>
  <data name="ApplyOnPo" xml:space="preserve">
    <value>Apply On PO</value>
  </data>
  <data name="AS6081Included" xml:space="preserve">
    <value>AS6081 Part Included?</value>
  </data>
  <data name="PVVQuestion" xml:space="preserve">
    <value>PVV Question</value>
  </data>
  <data name="LabelType" xml:space="preserve">
    <value>Show Vat Label</value>
  </data>
  <data name="OCRGEN" xml:space="preserve">
    <value>Auto-Imported OCR Invoices</value>
  </data>
  <data name="CustomerGroupCode" xml:space="preserve">
    <value>Customer Group Code</value>
  </data>
  <data name="GroupCodeName" xml:space="preserve">
    <value>Group Code Name</value>
  </data>
  <data name="RebateAccount" xml:space="preserve">
    <value>Rebate Account</value>
  </data>
  <data name="DebitAmount" xml:space="preserve">
    <value>Debit Amount</value>
  </data>
  <data name="DebitNoteRef" xml:space="preserve">
    <value>Debit Note Ref</value>
  </data>
  <data name="VATFilter" xml:space="preserve">
    <value>VAT ID</value>
  </data>
  <data name="HUBCC" xml:space="preserve">
    <value>HUB CC</value>
  </data>
  <data name="SalesCC" xml:space="preserve">
    <value>Sales CC</value>
  </data>
  <data name="InvoiceFormat" xml:space="preserve">
    <value>Invoice Format</value>
  </data>
  <data name="SourceFileName" xml:space="preserve">
    <value>Source File Name</value>
  </data>
  <data name="UploadFromDate" xml:space="preserve">
    <value>Upload From Date</value>
  </data>
  <data name="UploadToDate" xml:space="preserve">
    <value>Upload To Date</value>
  </data>
  <data name="IsFromProspectiveOffer" xml:space="preserve">
    <value>Generated from Prospective Offer</value>
  </data>
  <data name="PrOUploadedBy" xml:space="preserve">
    <value>Prospective Offer Uploaded By</value>
  </data>
  <data name="ImportedBy" xml:space="preserve">
    <value>Imported By</value>
  </data>
  <data name="AdvisoryNotes" xml:space="preserve">
    <value>Advisory Notes</value>
  </data>
  <data name="DisplayAdvisory" xml:space="preserve">
    <value>Display Advisory throughout all screens</value>
  </data>
  <data name="CreditFormat" xml:space="preserve">
    <value>Credit Note Format</value>
  </data>
  <data name="SIDescrepancy" xml:space="preserve">
    <value>Invoices with a Total Discrepancy</value>
  </data>
  <data name="MFRNameSuffix" xml:space="preserve">
    <value>MFR Name Suffix</value>
  </data>
  <data name="InactivateRestrictedMFR" xml:space="preserve">
    <value>Inactivate Restricted MFR</value>
  </data>
  <data name="SendMailToClientBuyer" xml:space="preserve">
    <value>Send to IPO Buyer</value>
  </data>
  <data name="SendMailToClientSupport" xml:space="preserve">
    <value>Send to IPO Support</value>
  </data>
  <data name="SendMailToHubBuyer" xml:space="preserve">
    <value>Send to PO Buyer</value>
  </data>
  <data name="SendMailToHubSupport" xml:space="preserve">
    <value>Send to PO Support</value>
  </data>
  <data name="EditedBy" xml:space="preserve">
    <value>Edited By</value>
  </data>
  <data name="EditedDateFrom" xml:space="preserve">
    <value>Edited Date From</value>
  </data>
  <data name="EditedDateTo" xml:space="preserve">
    <value>Edited Date To</value>
  </data>
  <data name="NumOfPO" xml:space="preserve">
    <value>Number of Purchase Order</value>
  </data>
  <data name="POLineCount" xml:space="preserve">
    <value>PO Line Count</value>
  </data>
  <data name="Franchise" xml:space="preserve">
    <value>Is Franchised?</value>
  </data>
  <data name="CustomerRefNo" xml:space="preserve">
    <value>Customer Ref No.</value>
  </data>
  <data name="QuoteNumber" xml:space="preserve">
    <value>Quote Number</value>
  </data>
  <data name="DailyReminder" xml:space="preserve">
    <value>Daily Reminder</value>
  </data>
  <data name="TaskCategory" xml:space="preserve">
    <value>Task Category</value>
  </data>
  <data name="Attachment" xml:space="preserve">
    <value>Attachment</value>
  </data>
  <data name="Body" xml:space="preserve">
    <value>Body</value>
  </data>
  <data name="CampaignName" xml:space="preserve">
    <value>Campaign Name</value>
  </data>
  <data name="Schedule" xml:space="preserve">
    <value>Schedule</value>
  </data>
  <data name="OnHold" xml:space="preserve">
    <value>On Hold</value>
  </data>
  <data name="ScheduleDate" xml:space="preserve">
    <value>Schedule Date</value>
  </data>
  <data name="ScheduleTime" xml:space="preserve">
    <value>Schedule Time</value>
  </data>
  <data name="IsUnsubscribe" xml:space="preserve">
    <value>Email Campaign Unsubscribe?</value>
  </data>
  <data name="SubscriptionStatus" xml:space="preserve">
    <value>Email Campaing Subscription</value>
  </data>
  <data name="Salutation" xml:space="preserve">
    <value>Salutation</value>
  </data>
  <data name="Signature" xml:space="preserve">
    <value>Signature</value>
  </data>
  <data name="PartStatus" xml:space="preserve">
    <value>Part Status</value>
  </data>
  <data name="PackageCode" xml:space="preserve">
    <value>Packaging Code</value>
  </data>
  <data name="ServiceCharge" xml:space="preserve">
    <value>Service Charge?</value>
  </data>
  <data name="ServiceChargeAmount" xml:space="preserve">
    <value>Service Charge Amount</value>
  </data>
  <data name="BillToCountry" xml:space="preserve">
    <value>Bill To Country</value>
  </data>
  <data name="CapacitorOrResistor" xml:space="preserve">
    <value>Is the part a capacitor or resistor</value>
  </data>
  <data name="CustomerConcession" xml:space="preserve">
    <value>If yes, please advise Testing or Customer Concession</value>
  </data>
  <data name="MouserLT" xml:space="preserve">
    <value>Mouser LT</value>
  </data>
  <data name="DigikeyLT" xml:space="preserve">
    <value>DigiKey LT</value>
  </data>
  <data name="TraceabilityLink" xml:space="preserve">
    <value>Traceability Link</value>
  </data>
  <data name="ShipFromWarehouse" xml:space="preserve">
    <value>Ship From Warehouse:</value>
  </data>
  <data name="ShipToCustomerName" xml:space="preserve">
    <value>Ship to Customer Name:</value>
  </data>
  <data name="ShipToCustomerCountry" xml:space="preserve">
    <value>Ship to Customer Country:</value>
  </data>
  <data name="CommodityCode" xml:space="preserve">
    <value>Commodity Code:</value>
  </data>
  <data name="ECCN" xml:space="preserve">
    <value>ECCN:</value>
  </data>
  <data name="PartApplication" xml:space="preserve">
    <value>Part Application:</value>
  </data>
  <data name="ExportControl" xml:space="preserve">
    <value>If Export Control is required, has the supplier verified they have the appropriate license</value>
  </data>
  <data name="AerospaceUse" xml:space="preserve">
    <value>Aerospace Use</value>
  </data>
  <data name="PartTested" xml:space="preserve">
    <value>Will the parts be tested and where (Please also advise Company Test House name)</value>
  </data>
</root>