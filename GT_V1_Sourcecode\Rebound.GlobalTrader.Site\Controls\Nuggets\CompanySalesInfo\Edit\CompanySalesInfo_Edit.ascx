<%--
     Marker     Changed by      Date         Remarks
<%--[001]      Suhail          02/05/2018   Added Credit Limit2  --%>
<%--[002]      <PERSON>     21/01/2020   Added maxlength="1" for StopStatus  --%>
<%@ Control Language="C#" CodeBehind="CompanySalesInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>	    
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanySalesInfo_Edit")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
	
			<ReboundUI_Form:FormField id="ctlSalesperson" runat="server" FieldID="ddlSalesperson" ResourceTitle="Salesperson"  IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlSalesperson" runat="server" /></Field>
			</ReboundUI_Form:FormField>
							
			<ReboundUI_Form:FormField id="ctlIsApproved" runat="server" FieldID="chkApproved" ResourceTitle="IsApproved">
				<Field><ReboundUI:ImageCheckBox ID="chkApproved" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			<%--[002] start--%>
				<ReboundUI_Form:FormField id="ctlStopStatus" runat="server" FieldID="txtStopStatus" ResourceTitle="StopStatus" >
				<Field><ReboundUI:ReboundTextBox ID="txtStopStatus" runat="server"  maxlength="1" Width="50" /></Field>
			</ReboundUI_Form:FormField>		
            <%--[002] end--%>
			<ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency"  IsRequiredField="true">
				<Field><ReboundDropDown:SellCurrency ID="ddlCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlCustomerNo" runat="server" FieldID="txtCustomerNo" ResourceTitle="CustomerNo">
				<Field><ReboundUI:ReboundTextBox ID="txtCustomerNo" runat="server" Width="150" /></Field>
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlTerms" runat="server" FieldID="ddlTerms" ResourceTitle="Terms"  IsRequiredField="true">
				<Field><ReboundDropDown:Terms ID="ddlTerms" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
			<ReboundUI_Form:FormField id="ctlTerms" runat="server" FieldID="ddlTerms" ResourceTitle="Terms"  IsRequiredField="true">
				<Field><ReboundDropDown:SellTerms ID="ddlTerms" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		<%--	<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="ddlTax" ResourceTitle="Tax"  IsRequiredField="true">
				<Field><ReboundDropDown:Tax ID="ddlTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
			
			<ReboundUI_Form:FormField id="ctlRating" runat="server" FieldID="starsRating" ResourceTitle="Rating">
				<Field><ReboundUI:StarRating ID="starsRating" runat="server" /></Field>
			</ReboundUI_Form:FormField>
							
			<ReboundUI_Form:FormField id="ctlIsOnStop" runat="server" FieldID="chkOnStop" ResourceTitle="IsOnStop">
				<Field><ReboundUI:ImageCheckBox ID="chkOnStop" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
							
			<ReboundUI_Form:FormField id="ctlIsShippingWaived" runat="server" FieldID="chkShippingWaived" ResourceTitle="IsShippingWaived">
				<Field><ReboundUI:ImageCheckBox ID="chkShippingWaived" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlShipVia" runat="server" FieldID="ddlShipVia" ResourceTitle="DefaultShipVia">
				<Field><ReboundDropDown:SellShipMethod ID="ddlShipVia" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlShippingAccountNo" runat="server" FieldID="txtShippingAccountNo" ResourceTitle="DefaultShippingAccountNo">
				<Field><ReboundUI:ReboundTextBox ID="txtShippingAccountNo" runat="server" Width="150" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="ddlContact" ResourceTitle="DefaultContactNo">
				<Field><ReboundDropDown:ContactsForCompany ID="ddlContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
							
			<ReboundUI_Form:FormField id="ctlCreditLimit" runat="server" FieldID="txtCreditLimit" ResourceTitle="MaxExpoCreditLimit">
				<Field><ReboundUI:ReboundTextBox ID="txtCreditLimit" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="2" Width="100" /> <asp:Label ID="lblCurrency_CreditLimit" runat="server" /></Field>
			</ReboundUI_Form:FormField>
             <%--  [002] Code Start--%>
            <ReboundUI_Form:FormField id="ctlActualCreditLimit" runat="server" FieldID="txtCreditLimit2" ResourceTitle="CreditLimit">
				<Field><ReboundUI:ReboundTextBox ID="txtCreditLimit2" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="2" Width="100" /> <asp:Label ID="lblCurrency_ActualCreditLimit" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			 <%--  [002] Code End--%>
			<ReboundUI_Form:FormField id="ctlInsuranceFileNo" runat="server" FieldID="txtInsuranceFileNo" ResourceTitle="InsuranceFileNo">
				<Field><ReboundUI:ReboundTextBox ID="txtInsuranceFileNo" runat="server"   Width="150" /> </Field>
			</ReboundUI_Form:FormField>
			  <%--  Code Start for  add Insured Amount Currency--%>
            <ReboundUI_Form:FormField id="ctlInsuredAmountCurrency" runat="server" FieldID="ddlInsuredAmountCurrency" ResourceTitle="InsuredAmountCurrency"  IsRequiredField="false">
				<Field><ReboundDropDown:SellCurrency ID="ddlInsuredAmountCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>
            <%--   Code End--%>
				<ReboundUI_Form:FormField id="ctlInsuredAmount" runat="server" FieldID="txtInsuredAmount" ResourceTitle="InsuredAmount">
				<Field><ReboundUI:ReboundTextBox ID="txtInsuredAmount" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="2" Width="100" /> <asp:Label ID="lblCurrency_Insurance" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlNotesToInvoice" runat="server" FieldID="txtNotesToInvoice" ResourceTitle="NotesToInvoice">
				<Field><ReboundUI:ReboundTextBox ID="txtNotesToInvoice" runat="server" Width="450" Rows="2" TextMode="multiLine" /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlPreferredWarehouse" runat="server" FieldID="ddlPreferredWarehouse" ResourceTitle="PreferredWarehouse">
				<Field><ReboundDropDown:PreferredWarehouse ID="ddlPreferredWarehouse" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            
		</ReboundUI_Table:Form>
	</Content>
	
</ReboundUI_Form:DesignBase>
