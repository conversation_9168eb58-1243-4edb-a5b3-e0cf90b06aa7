//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - clear related dropdown cache
//--------------------------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      Suhail       25/04/2018   Added contact and company name while sending mail via Add New Communication Note
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.IO;
using System.Text;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BOMMainInfo : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                    switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "Delete": Delete(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "ExportToCSV": ExportToCSV(); break;
                    case "savePurchaseHUBData": savePurchaseHUBData(); break;
                    case "BOMReleaseRequirement": BOMReleaseRequirement(); break;
                    case "UpdateBOMByPH": UpdateBOMByPH(); break;
                    case "updateBOMStatusToClosed": updateBOMStatusToClosed(); break;
                    case "BOMNoBidRequirement": BOMNoBidRequirement(); break;
                    case "SaveExpedite": SaveExpedite(); break;
                    case "GetAllSourcingResult":GetAllSourcingResult(); break;
                        
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets data for a stock item
        /// </summary>
        public JsonObject GetData(BOM bom)
        {
            JsonObject jsn = null;
            bool IsAssignedToMe = true;
            try
            {
                if (bom != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("Code", bom.BOMCode);
                    jsn.AddVariable("Name", bom.BOMName);
                    jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(bom.Notes));
                    jsn.AddVariable("DLUP", Functions.FormatDLUP(bom.DLUP, bom.UpdatedBy));
                    jsn.AddVariable("InActive", bom.Inactive);
                    jsn.AddVariable("Company", Functions.ReplaceLineBreaks(bom.CompanyName ));
                    jsn.AddVariable("CompanyType", Functions.ReplaceLineBreaks(bom.CompanyType));
                    jsn.AddVariable("Contact", Functions.ReplaceLineBreaks(bom.ContactName));
                    jsn.AddVariable("CompanyNo", bom.CompanyNo);
                    string companyNotes = Company.GetAdvisoryNotes(bom.CompanyNo ?? 0);
                    jsn.AddVariable("CompanyAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
                    jsn.AddVariable("ContactNo", bom.ContactNo);
                    jsn.AddVariable("blnReqToPoHub", bom.RequestToPOHubBy > 0);
                    jsn.AddVariable("blnRelease", bom.StatusValue == (int)BLL.BOMStatus.List.Released);
                    jsn.AddVariable("IsPoHub", SessionManager.IsPOHub);
                    jsn.AddVariable("blnBomCount", bom.BomCount);
                    jsn.AddVariable("BOMStatus", bom.BOMStatus);
                    jsn.AddVariable("StatusValue", bom.StatusValue);
                    jsn.AddVariable("CurrencyCode", bom.CurrencyCode);
                    jsn.AddVariable("CurrencyNo", bom.CurrencyNo);
                    jsn.AddVariable("Currency_Code", bom.Currency_Code);
                    jsn.AddVariable("CurrentSupplier", bom.CurrentSupplier);
                    jsn.AddVariable("QuoteRequired", Functions.FormatDate(bom.QuoteRequired));
                    jsn.AddVariable("AllItemHasSourcing", bom.AllItemHasSourcing > 0);
                    jsn.AddVariable("AS9120", bom.AS9120);
                    jsn.AddVariable("Requestedby", bom.Requestedby);
                    jsn.AddVariable("Releasedby", bom.Releasedby);
                    jsn.AddVariable("IsClosed", bom.StatusValue == (int)BLL.BOMStatus.List.Closed);
                    jsn.AddVariable("UpdatedBy", bom.UpdatedBy);
                    jsn.AddVariable("isNoBidCount", bom.NoBidCount > 0 ? true : false);
                    jsn.AddVariable("UpdateByPH", bom.UpdateByPH);
                    jsn.AddVariable("RequestToPOHubBy", bom.RequestToPOHubBy);
                    jsn.AddVariable("AssignedUser", bom.AssignedUser);
                    jsn.AddVariable("Contact2Id", bom.Contact2Id);
                    jsn.AddVariable("Contact2Name", Functions.ReplaceLineBreaks(bom.Contact2Name));
                    jsn.AddVariable("ValidMessage", bom.ValidationMessage);
                    jsn.AddVariable("IsReqInValid", bom.IsReqInValid);
                    jsn.AddVariable("ReqSalesPerson", Functions.ReplaceLineBreaks(bom.ReqSalesPerson));
                    jsn.AddVariable("SupportTeamMemberNo", Functions.ReplaceLineBreaks(bom.SupportTeamMemberNoAsString));
                    jsn.AddVariable("ClientNo", bom.ClientNo);

                    string[] Ids = bom.AssignedUserIds.Split(',');
                    if (Ids[0] == "-2")
                    {
                        IsAssignedToMe = true;
                    }
                    else
                    {
                        for (int i = 0; i < Ids.Length; i++)
                        {
                            if (Convert.ToInt32(Ids[i]) == SessionManager.LoginID)
                            {
                                IsAssignedToMe = true;
                            }
                            else
                            {
                                IsAssignedToMe = false;
                            }
                            if (IsAssignedToMe == true)
                            {
                                break;
                            }
                        }
                    }
                    
                    jsn.AddVariable("IsAssignToMe", IsAssignedToMe);
                    jsn.AddVariable("AS6081", bom.AS6081);
                    jsn.AddVariable("PurchasingNotes", Functions.ReplaceLineBreaks(bom.PurchasingNotes));
                    jsn.AddVariable("PVVBOMValidateMessage", Functions.ReplaceLineBreaks(bom.PVVBOMValidateMessage));
                    jsn.AddVariable("PVVBOMCountValid", bom.PVVBOMCountValid);
                    jsn.AddVariable("IsFromProspectiveOffer", bom.IsFromProspectiveOffer);
                    jsn.AddVariable("UploadedBy", bom.UploadedBy);
                }
                    
                
                bom = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            return jsn;
        }

        public void GetData()
        {
            BOM bom = BOM.Get(ID);
            OutputResult(GetData(bom));
            bom = null;
        }

        private void Delete()
        {
            try
            {
                //CacheManager.ClearStoredDropDown("BOM", new object[] { SessionManager.ClientID });
                JsonObject jsn = new JsonObject();
                bool blnOK = BOM.UpdateDelete(ID, LoginID);
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }

        private void SaveEdit()
        {
            try
            {
                //CacheManager.ClearStoredDropDown("BOM", new object[] { SessionManager.ClientID });
                JsonObject jsn = new JsonObject();
                bool blnOK = true;
                BOM bom = BOM.Get(ID);
                if (bom != null)
                {
                    // bom.BOMCode = GetFormValue_String("Code");
                    bom.BOMName = GetFormValue_String("Name") + "-" + SessionManager.ClientID.ToString();
                    bom.Notes = GetFormValue_String("Notes");
                    bom.UpdatedBy = LoginID;
                    bom.Inactive = GetFormValue_Boolean("Inactive");
                    bom.CompanyName = GetFormValue_String("Company");
                    bom.ContactNo = GetFormValue_Int("Contact");
                    bom.ClientNo = (int)SessionManager.ClientID;
                    bom.CurrencyNo = GetFormValue_Int("Currency");
                    bom.CurrentSupplier = GetFormValue_String("CurrentSupplier");
                    bom.QuoteRequired = GetFormValue_NullableDateTime("QuoteRequired");
                    bom.AS9120 = GetFormValue_Boolean("AS9120");
                    bom.Contact2Id = GetFormValue_NullableInt("Contact2No");
                    blnOK = bom.Update();
                }
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }

        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                ////List<List<object>> lstData = CustomerRequirement.GetBOMListForCRList(ID, SessionManager.ClientID);currencyCode               
                JsonObject jsnItems = new JsonObject(true);
                string CurrencyCode = GetFormValue_String("Currency_Code");
                //return saved filename to the page
                string strFilename = FileUploadManager.ExportToCSV((int)Rebound.GlobalTrader.BLL.Report.List.RequirementWithBOM, ID, CurrencyCode, "E");
                jsn.AddVariable("Filename", String.Format("{0}/{1}", FileUploadManager.GetTemporaryUploadFilePath(), strFilename));
                OutputResult(jsn);
            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }


        /// <summary>
        /// Writes report to a CSV file and returns the filename 
        /// </summary>
        /// <returns></returns>
        public void savePurchaseHUBData()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                String ValidateMessage = null;
                System.Int32 AssignUserNo = GetFormValue_Int("AssignUserNo");
                if (AssignUserNo > 0)
                {
                    bool blnOK = BOM.UpdatePurchaseQuote(ID, LoginID, (int)BOMStatus.List.RPQ, AssignUserNo, out ValidateMessage);
                    if (blnOK)
                    {
                        WebServices servic = new WebServices();
                        string BOMCode = GetFormValue_String("BOMCode");
                        string BOMName = GetFormValue_String("BOMName");
                        string BomCompanyName = GetFormValue_String("BomCompanyName");
                        int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                        string aryRecipientLoginIDsCC = GetFormValue_String("aryRecipientLoginIDs");

                        System.Int32 Contact2No = GetFormValue_Int("Contact2No"); // Also send mail to  Added Contact 2  05-04-2018

                        servic.NotifyPurchaseRequestBom(AssignUserNo.ToString(), (SessionManager.POHubMailGroupId ?? 0).ToString(), string.Format(Functions.GetGlobalResource("Messages", "PurchaseRequest"), BOMName), BOMCode, BOMName, ID, BomCompanyName, BomCompanyNo, aryRecipientLoginIDsCC);

                        List<CustomerRequirement> customerRequirements = CustomerRequirement.GetHUBRFQHasRLStock(ID);
                        servic.RLStockNotification(string.Format(Functions.GetGlobalResource("Messages", "RLStockSubject1"), BOMCode), customerRequirements, true, ID, BomCompanyName, BOMCode);
                    }
                    jsn.AddVariable("Result", blnOK);
                    jsn.AddVariable("ValidateMessage", ValidateMessage);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }


        /// <summary>
        /// Release customer requirement for client user
        /// </summary>
        public void BOMNoBidRequirement()
        {
            try
            {
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                int? UpdatedBy = GetFormValue_Int("UpdatedBy");
                string Notes = GetFormValue_String("Notes");
                //rp-225 salesman no passed to nobid function [value is coming with seperator ||]
                string SalesmanNo = GetFormValue_String("SalesmanNo");
                bool blnResult = CustomerRequirement.BOMNoBidRequirement(
                    ID,
                    LoginID,
                    Notes
                );
                if (blnResult)
                {
                    WebServices servic = new WebServices();
                    // BLL.BOM bom = BLL.BOM.Get(ID);
                    string loginArr = string.IsNullOrEmpty(SalesmanNo) == true ? (UpdatedBy ?? 0).ToString() : (UpdatedBy ?? 0).ToString() + "||" + SalesmanNo.TrimEnd(new char[] { '|' }).TrimEnd(new char[] { '|' });
                    servic.NotifyNoBidBom(loginArr, "", Functions.GetGlobalResource("Messages", "BOMNoBid"), ID, BOMCode, BOMName, BomCompanyNo, BomCompanyName, true, ID);
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>BOMNoBidRequirement
        /// Release customer requirement for client user
        /// </summary>
        public void BOMReleaseRequirement()
        {
            try
            {
                int BomId = GetFormValue_Int("BomId");
                string BOMCode = GetFormValue_String("BOMCode");
                string BOMName = GetFormValue_String("BOMName");
                string BomCompanyName = GetFormValue_String("BomCompanyName");
                int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                int? UpdatedBy = GetFormValue_Int("UpdatedBy");
                int? RequestedBy = GetFormValue_Int("RequestedBy");
                bool blnResult = CustomerRequirement.BOMReleaseRequirement(
                    //ID,
                    BomId,
                    LoginID
                );
                if (blnResult)
                {
                    string ReqSalesPerson = "";
                    string SupportTeamMemberNo = "";

                    if (SessionManager.IsPOHub == true)
                    {
                        ReqSalesPerson = GetFormValue_String("ReqsalesPerson");
                        SupportTeamMemberNo = GetFormValue_String("SupportTeamMemberNo");

                        if (!string.IsNullOrEmpty(ReqSalesPerson))
                        {
                            ReqSalesPerson = ReqSalesPerson.TrimEnd('|');
                        }

                        if (!string.IsNullOrEmpty(SupportTeamMemberNo))
                        {
                            SupportTeamMemberNo = SupportTeamMemberNo.TrimEnd('|');
                        }
                    }

                    string strRequestBy = (RequestedBy ?? 0).ToString();
                    WebServices servic = new WebServices();

                    string notifyRecipients = strRequestBy;
                    if (!string.IsNullOrEmpty(ReqSalesPerson))
                    {
                        notifyRecipients += "||" + ReqSalesPerson;
                    }
                    if (!string.IsNullOrEmpty(SupportTeamMemberNo))
                    {
                        notifyRecipients += "||" + SupportTeamMemberNo;
                    }

                    servic.NotifyReleaseBom(
                        notifyRecipients,
                        "",
                        Functions.GetGlobalResource("Messages", "BOMReleased"),
                        BomId,
                        BOMCode,
                        BOMName,
                        BomCompanyNo,
                        BomCompanyName,
                        true,
                        ID
                    );
                }

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnResult);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Assigning BOM Ids to Loggedin User.
        /// </summary>
        private void UpdateBOMByPH()
        {
            try
            {
                string BOMIdList = GetFormValue_String("BOMIds");//2,3,4 etc
                string selectedUser = GetFormValue_String("SelectedUser");//2344
                string selectedUserName = GetFormValue_String("SelectedUserName");
                System.Boolean? IsGroupAssignment = GetFormValue_NullableBoolean("IsGroupAssignment");
                string strType = GetFormValue_String("strType");
                bool blnOK = BOM.UpdateBOMByPH(BOMIdList, Convert.ToInt32(selectedUser),SessionManager.LoginID, IsGroupAssignment, strType);
                //bool blnOK = BOM.UpdateBOMByPH(BOMIdList, SessionManager.LoginID);
                string strToLoginsArrayAssignedToMe = string.Empty;
                if (blnOK)
                {
                    WebServices servic = new WebServices();
                    BLL.BOM bomAssignedToMe = null;
                    List<BLL.BOM> bomRequesterDetails = null;
                    if (strType== "Header")
                    {
                        if (IsGroupAssignment == true)
                        {
                            bomAssignedToMe = BLL.BOM.GetBomDetailsForAS6081AssignedToMe(BOMIdList);
                            if (!string.IsNullOrEmpty(selectedUser))
                            {
                                BLL.BOM groupUsers=  BLL.BOM.GetSecurityUserDetails(Convert.ToInt32(selectedUser));
                                strToLoginsArrayAssignedToMe += groupUsers.UpdatedByList;
                            }
                        }
                        else
                        {
                            bomAssignedToMe = BLL.BOM.GetBomDetailsForAS6081AssignedToMe(BOMIdList);
                            if (!string.IsNullOrEmpty(selectedUser))
                            {
                                strToLoginsArrayAssignedToMe +=  selectedUser;
                            }
                        }
                        servic.NotifyAssignedToPOHUBUserAS6081(strToLoginsArrayAssignedToMe, "", Functions.GetGlobalResource("Messages", "BOMAssigned"), bomAssignedToMe.BOMName, selectedUserName, bomAssignedToMe.BOMIds, SessionManager.LoginFullName);

                        bomRequesterDetails = BLL.BOM.GetBomDetailsForRequester(BOMIdList);
                        foreach(BOM requesterDetails in bomRequesterDetails)
                        {
                            servic.NotifyRequesterToPOHUBUserAS6081(requesterDetails.RequesterId.ToString(), "", Functions.GetGlobalResource("Messages", "BOMAssigned"), requesterDetails.BOMName, selectedUserName, requesterDetails.BOMIds, requesterDetails.Requestedby);
                        }
                        
                    }
                    else
                    {
                        StringBuilder MessageBody = new StringBuilder();
                        List<BLL.BOM> ReqAssignedToMe = null;
                        if (IsGroupAssignment == true)
                        {
                           ReqAssignedToMe = BLL.BOM.GetRequirementDetailsForAS6081AssignedToMe(BOMIdList);
                            if (!string.IsNullOrEmpty(selectedUser))
                            {
                                BLL.BOM groupUsers = BLL.BOM.GetSecurityUserDetails(Convert.ToInt32(selectedUser));
                                strToLoginsArrayAssignedToMe += groupUsers.UpdatedByList;
                            }
                            MessageBody.Append("<table style='border-collapse: collapse;'><tr>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'></th>");
                            //MessageBody.Append("<th>Requirement No</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Bom Name</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Part No</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Mfr</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Company</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Quantity</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Target Price</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Quote Required</th></tr>");
                            string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
                            foreach (BLL.BOM obj in ReqAssignedToMe)
                            {
                                MessageBody.Append("<tr>");
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'><a href='" + url + "/Ord_CusReqDetail.aspx?req=" + obj.CustomerRequirementId + "'>" + obj.CustomerRequirementNo + "</a></td>");
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'><a href='" + url + "/Ord_BOMDetail.aspx?BOM=" + obj.BOMIds + "'>" + obj.BOMName + "</a></td>");
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'>" + obj.Part + "</td>");
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'>" + obj.Manufacturer + "</td>");
                                MessageBody.AppendFormat("<td style='border: 1px solid; padding: 8px;'>{0}</td>", obj.CompanyName);
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'>" + Convert.ToString(obj.Quantity) + "</td>");
                                MessageBody.AppendFormat("<td style='border: 1px solid; padding: 8px;'>{0}</td>", Functions.FormatCurrency(obj.TargetSellPrice, obj.CurrencyCode));
                                MessageBody.AppendFormat("<td style='border: 1px solid; padding: 8px;'>{0}</td>", obj.QuoteRequired?.ToString("dd-MM-yyyy"));
                                MessageBody.Append("</tr>");
                            }
                            MessageBody.Append("</table>");
                        }
                        else
                        {
                            ReqAssignedToMe  = BLL.BOM.GetRequirementDetailsForAS6081AssignedToMe(BOMIdList);
                            if (!string.IsNullOrEmpty(selectedUser))
                            {
                                strToLoginsArrayAssignedToMe += selectedUser;
                            }
                            MessageBody.Append("<table style='border-collapse: collapse;'><tr>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'></th>");
                            //MessageBody.Append("<th>Requirement No</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Bom Name</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Part No</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Mfr</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Company</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Quantity</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Target Price</th>");
                            MessageBody.Append("<th style='border: 1px solid; padding: 8px;'>Quote Required</th></tr>");

                            string url = string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"]));
                            int index = 1;
                            foreach (BLL.BOM obj in ReqAssignedToMe)
                            {
                                MessageBody.Append("<tr>");
                                MessageBody.AppendFormat("<td style='border: 1px solid; padding: 8px;'>{0}</td>", index.ToString());
                                //MessageBody.Append("<td><a href='"+url+ "/Ord_CusReqDetail.aspx?req=" + obj.CustomerRequirementId+"'>"+obj.CustomerRequirementNo+"</a></td>");
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'><a href='" + url + "/Ord_BOMDetail.aspx?BOM=" + obj.BOMIds + "'>" + obj.BOMName + "</a></td>");
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'>" + obj.Part + "</td>");
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'>" + obj.Manufacturer + "</td>");
                                MessageBody.AppendFormat("<td style='border: 1px solid; padding: 8px;'>{0}</td>", obj.CompanyName);
                                MessageBody.Append("<td style='border: 1px solid; padding: 8px;'>" + Convert.ToString(obj.Quantity)+"</td>");
                                MessageBody.AppendFormat("<td style='border: 1px solid; padding: 8px;'>{0}</td>", Functions.FormatCurrency(obj.TargetSellPrice, obj.CurrencyCode)); 
                                MessageBody.AppendFormat("<td style='border: 1px solid; padding: 8px;'>{0}</td>", obj.QuoteRequired?.ToString("dd-MM-yyyy"));
                                MessageBody.Append("</tr>");
                                index++;
                            }
                            MessageBody.Append("</table>");
                        }
                        servic.NotifyAssignedToRequirementHubUserAS6081(strToLoginsArrayAssignedToMe, "", Functions.GetGlobalResource("Messages", "BOMAssigned"), selectedUserName, SessionManager.LoginFullName, MessageBody);

                        bomRequesterDetails = BLL.BOM.GetRequirementDetailsForRequestedUser(BOMIdList);
                        foreach (BOM requesterDetails in bomRequesterDetails)
                        {
                            servic.NotifyRequesterToRequirementHUBUserAS6081(requesterDetails.RequesterId.ToString(), "", Functions.GetGlobalResource("Messages", "BOMAssigned"), selectedUserName, requesterDetails.Requestedby, MessageBody);
                        }
                    }
                    
                }
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }


        /// <summary>
        /// update BOM Status To Closed
        /// </summary>
        /// <returns></returns>
        public void updateBOMStatusToClosed()
        {
            JsonObject jsn = new JsonObject();
            try
            {
                bool blnOK = BOM.UpdateBOMStatusToClosed(ID, LoginID, (int)BOMStatus.List.Closed);
                //if (blnOK)
                //{
                //WebServices servic = new WebServices();
                //string BOMCode = GetFormValue_String("BOMCode");
                //string BOMName = GetFormValue_String("BOMName");
                //string BomCompanyName = GetFormValue_String("BomCompanyName");
                //int? BomCompanyNo = GetFormValue_Int("BomCompanyNo");
                //servic.NotifyPurchaseRequestBom("", (SessionManager.POHubMailGroupId ?? 0).ToString(), Functions.GetGlobalResource("Messages", "PurchaseRequest"), BOMCode, BOMName, ID, BomCompanyName, BomCompanyNo);
                // }
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
        }

        public void SaveExpedite()
        {

            try
            {

                string Subject = Functions.GetGlobalResource("Printing", "CommunicationNoteSubject");
                string Notes = GetFormValue_String("AddNotes");
                System.Int32 Contact2No = GetFormValue_Int("Contact2No");
                int HUBRFQId = ID;
                string HUBRFQName = GetFormValue_String("HUBRFQName");
                int RequestToPOHubBy = SessionManager.IsPOHub == true ? GetFormValue_Int("intRequestedby") : GetFormValue_Int("intUpdateByPH");
                string HubrfqCode = GetFormValue_String("HubrfqCode");
                int CompanyNo = GetFormValue_Int("CompanyNo");
                //[001] Code Start
                string CompanyName = GetFormValue_String("CompanyName");
                string ContactName = GetFormValue_String("ContactName");
                string ReqSalesPerson = "";
                if (SessionManager.IsPOHub == true)
                {
                    ReqSalesPerson = GetFormValue_String("ReqsalesPerson");
                    if (!string.IsNullOrEmpty(ReqSalesPerson))
                    {
                        ReqSalesPerson = ReqSalesPerson.TrimEnd('|').TrimEnd('|');
                        //ReqSalesPerson = ReqSalesPerson.TrimEnd('|');
                    }
                }
                //[001] Code End
                int intResult = CustomerRequirement.InsertHUBRFQExpedite(
                    ID,
                      Notes,
                      LoginID,
                      RequestToPOHubBy

                );
                if (intResult > 0)
                {
                    //  List<CustomerRequirement> lst = CustomerRequirement.GetBOMListForCustomerRequirement(ID, SessionManager.ClientID);




                    //string FullPart = "";
                    //string CustomerRequirementNumber = "";
                    //foreach (CustomerRequirement objDetails in lst)
                    //{
                    //    FullPart = FullPart + "," + objDetails.FullPart;
                    //    CustomerRequirementNumber = CustomerRequirementNumber + "," + Convert.ToString(objDetails.CustomerRequirementNumber);

                    //}
                    StringBuilder message = new StringBuilder();
                    WebServices servic = new WebServices();
                    string poref = string.Format("Reference HUBRFQ   : <a onclick=\"{0}\" target=\"_blank\" href=\"{1}\">{2}</a>", "javascript:void(0);", string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["redirecturi"])) + "Ord_BOMDetail.aspx?BOM=" + HUBRFQId, HUBRFQName);
                    message.Append("Message By  : " + SessionManager.LoginFullName + "<br />");
                    message.Append("Date & Time  : " + Functions.FormatDate(Functions.GetUKLocalTime()) + " " + Functions.FormatTime(Functions.GetUKLocalTime()) + "<br />");
                    message.Append("Code  : " + HubrfqCode + "<br />");
                    // message.Append("Req Nos  : " + CustomerRequirementNumber.Remove(0, 1) + "<br />");
                    message.Append(poref + "<br /><br />");
                    //[001] Code Start
                    message.Append("Company  : " + CompanyName + "<br />");
                    message.Append("Contact  : " + ContactName + "<br />");
                    //[001] Code End
                    message.Append("Communication Note  : " + Notes + "<br />");

                    message.Append("<br /><br />Regards,<br />" + SessionManager.LoginFullName + "<br />");

                    servic.NotifyMessageExpediteNote(Convert.ToString(RequestToPOHubBy) + "||" + Convert.ToString(Contact2No) + "||" + ReqSalesPerson, "", Subject, Convert.ToString(message), CompanyNo, false);

                    message = null;
                    servic = null;

                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("Result", intResult);
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }

        }

        private void GetAllSourcingResult()
        {
            int? BomId = GetFormValue_Int("BomId");
            List<SourcingResult> lst = SourcingResult.GetListForBOMReleaseAll(BomId, Convert.ToBoolean(SessionManager.IsPOHub));
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", ln.SourcingResultId);
                        jsnItem.AddVariable("REQID", ln.CustomerRequirementNo);
                        jsnItem.AddVariable("Part", ln.Part);
                        jsnItem.AddVariable("DeliveryDate", Functions.FormatDate(ln.DeliveryDate));
                        jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                        jsnItem.AddVariable("BuyPrice", Functions.FormatCurrency(ln.OriginalPrice, ln.ActualCurrencyCode));
                        jsnItem.AddVariable("UnitSellPrice", ln.Price);
                        jsnItem.AddVariable("UnitBuyPrice", ln.OriginalPrice);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Results", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
            }
        }
    }
}
