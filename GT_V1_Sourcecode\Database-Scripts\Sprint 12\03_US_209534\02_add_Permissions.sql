﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209534]     NgaiTo		 	17-Sep-2024			UPDATE		209534: OGEL approval dropdown to be moved out of code to the setup screen
===========================================================================================  
*/
DECLARE @CurrentSortOrder INT;
SELECT @CurrentSortOrder = ISNULL(MAX(DisplaySortOrder),0) FROM tbSecurityFunction WHERE SiteSectionNo = 6; --Setup

IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Setup_CompanySettings_OGELLicenses'
		OR SecurityFunctionId = 6011607
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     6011607,                -- SecurityFunctionId
    'Setup_CompanySettings_OGELLicenses',            -- FunctionName
    'Setup_CompanySettings_OGELLicenses', -- Description
    NULL,                      -- SitePageNo
    6,                        -- SiteSectionNo
    NULL,                     -- ReportNo (NULL because it's not related to a report)
    NULL,                     -- UpdatedBy (ID of the user who updated this function)
    GETDATE(),                -- DLUP (current date and time)
    0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 1                        -- DisplaySortOrder
);
END

IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Setup_CompanySettings_OGELLicenses_Add'
		OR SecurityFunctionId = 6011608
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     6011608,                -- SecurityFunctionId
    'Setup_CompanySettings_OGELLicenses_Add',            -- FunctionName
    'Setup_CompanySettings_OGELLicenses_Add', -- Description
    NULL,                      -- SitePageNo
    6,                        -- SiteSectionNo
    NULL,                     -- ReportNo (NULL because it's not related to a report)
    NULL,                     -- UpdatedBy (ID of the user who updated this function)
    GETDATE(),                -- DLUP (current date and time)
    0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 2                        -- DisplaySortOrder
);
END

IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Setup_CompanySettings_OGELLicenses_Edit'
		OR SecurityFunctionId = 6011609
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     6011609,                -- SecurityFunctionId
    'Setup_CompanySettings_OGELLicenses_Edit',            -- FunctionName
    'Setup_CompanySettings_OGELLicenses_Edit', -- Description
    NULL,                      -- SitePageNo
    6,                        -- SiteSectionNo
    NULL,                     -- ReportNo (NULL because it's not related to a report)
    NULL,                     -- UpdatedBy (ID of the user who updated this function)
    GETDATE(),                -- DLUP (current date and time)
    0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 3                        -- DisplaySortOrder
);
END