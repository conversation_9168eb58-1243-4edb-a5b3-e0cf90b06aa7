Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.GoodsIn=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.GoodsIn.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.GoodsIn.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this._strPathToData="controls/DataListNuggets/GoodsIn";this._strDataObject="GoodsIn";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.GoodsIn.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.GoodsIn.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.addParameter("UninspectedOnly",this.getFilterValue("ViewLevel")=="1")},getDataOK:function(){for(var n,t,i=0,r=this._objResult.Results.length;i<r;i++)n=this._objResult.Results[i],t=String.format('<a href="{0}"><b>{1}<\/b> - {2}',$RGT_gotoURL_GoodsIn(n.ID),n.No,$R_FN.setCleanTextValue(n.CM)),n.Part.length>0&&(t+=String.format("<br />{0} x {1}<\/a>",n.Quantity,$R_FN.writePartNo(n.Part,n.ROHS))),t+="<\/a>",this._tbl.addRow([t],n.ID,!1),t=null,n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.GoodsIn.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.GoodsIn",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);