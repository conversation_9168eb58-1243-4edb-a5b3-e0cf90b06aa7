//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Printing {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Printing() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Printing", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account.
        /// </summary>
        internal static string Account {
            get {
                return ResourceManager.GetString("Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Action.
        /// </summary>
        internal static string Action {
            get {
                return ResourceManager.GetString("Action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advice Note.
        /// </summary>
        internal static string AdviceNote {
            get {
                return ResourceManager.GetString("AdviceNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advice Note.
        /// </summary>
        internal static string Advice_Note {
            get {
                return ResourceManager.GetString("Advice_Note", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Airway Bill No.
        /// </summary>
        internal static string AirwayBill {
            get {
                return ResourceManager.GetString("AirwayBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocation.
        /// </summary>
        internal static string Allocation {
            get {
                return ResourceManager.GetString("Allocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocations.
        /// </summary>
        internal static string Allocations {
            get {
                return ResourceManager.GetString("Allocations", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        internal static string Amount {
            get {
                return ResourceManager.GetString("Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Asap.
        /// </summary>
        internal static string Asap {
            get {
                return ResourceManager.GetString("Asap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Attention.
        /// </summary>
        internal static string Attention {
            get {
                return ResourceManager.GetString("Attention", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise Date.
        /// </summary>
        internal static string AuthoriseDate {
            get {
                return ResourceManager.GetString("AuthoriseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorised By.
        /// </summary>
        internal static string AuthorisedBy {
            get {
                return ResourceManager.GetString("AuthorisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise Name.
        /// </summary>
        internal static string Authorise_Name {
            get {
                return ResourceManager.GetString("Authorise_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise by/signature.
        /// </summary>
        internal static string Authorise_signature {
            get {
                return ResourceManager.GetString("Authorise_signature", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bank Fee.
        /// </summary>
        internal static string BankFee {
            get {
                return ResourceManager.GetString("BankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bill To.
        /// </summary>
        internal static string BilledTo {
            get {
                return ResourceManager.GetString("BilledTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Billing Address.
        /// </summary>
        internal static string BillingAddress {
            get {
                return ResourceManager.GetString("BillingAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bill To.
        /// </summary>
        internal static string BillTo {
            get {
                return ResourceManager.GetString("BillTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Boxes.
        /// </summary>
        internal static string Boxes {
            get {
                return ResourceManager.GetString("Boxes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Base.
        /// </summary>
        internal static string BuyBase {
            get {
                return ResourceManager.GetString("BuyBase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Currency.
        /// </summary>
        internal static string BuyCurrency {
            get {
                return ResourceManager.GetString("BuyCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buyer.
        /// </summary>
        internal static string Buyer {
            get {
                return ResourceManager.GetString("Buyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buyer.
        /// </summary>
        internal static string BuyerName {
            get {
                return ResourceManager.GetString("BuyerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Terms.
        /// </summary>
        internal static string BuyTerms {
            get {
                return ResourceManager.GetString("BuyTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cedit Card.
        /// </summary>
        internal static string CeditCard {
            get {
                return ResourceManager.GetString("CeditCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Certificate Of Conformity.
        /// </summary>
        internal static string CertificateOfConformance {
            get {
                return ResourceManager.GetString("CertificateOfConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cheque.
        /// </summary>
        internal static string Cheque {
            get {
                return ResourceManager.GetString("Cheque", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice.
        /// </summary>
        internal static string ClientInvoice {
            get {
                return ResourceManager.GetString("ClientInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ref No.
        /// </summary>
        internal static string ClientRefNo {
            get {
                return ResourceManager.GetString("ClientRefNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VAT.
        /// </summary>
        internal static string ClientVATNo {
            get {
                return ResourceManager.GetString("ClientVATNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 6. Closure (Logistics only to complete).
        /// </summary>
        internal static string Closure {
            get {
                return ResourceManager.GetString("Closure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        internal static string Comments {
            get {
                return ResourceManager.GetString("Comments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commercial Invoice.
        /// </summary>
        internal static string CommercialInvoice {
            get {
                return ResourceManager.GetString("CommercialInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Commercial Invoice Date.
        /// </summary>
        internal static string CommercialInvoiceDate {
            get {
                return ResourceManager.GetString("CommercialInvoiceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Communication Note Created.
        /// </summary>
        internal static string CommunicationNoteSubject {
            get {
                return ResourceManager.GetString("CommunicationNoteSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ** Sales order has been processed on an account currently on stop **.
        /// </summary>
        internal static string CompanyOnStop {
            get {
                return ResourceManager.GetString("CompanyOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Reg. No..
        /// </summary>
        internal static string CompRegNo {
            get {
                return ResourceManager.GetString("CompRegNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contract No..
        /// </summary>
        internal static string ContractNo {
            get {
                return ResourceManager.GetString("ContractNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct Date Code.
        /// </summary>
        internal static string CorrectDateCode {
            get {
                return ResourceManager.GetString("CorrectDateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct HIC Status.
        /// </summary>
        internal static string CorrectHICStatus {
            get {
                return ResourceManager.GetString("CorrectHICStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct Mfr Name.
        /// </summary>
        internal static string CorrectManufacturerName {
            get {
                return ResourceManager.GetString("CorrectManufacturerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct MSL.
        /// </summary>
        internal static string CorrectMSLLevel {
            get {
                return ResourceManager.GetString("CorrectMSLLevel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct Package Type.
        /// </summary>
        internal static string CorrectPackageType {
            get {
                return ResourceManager.GetString("CorrectPackageType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Correct Part No.
        /// </summary>
        internal static string CorrectPartNo {
            get {
                return ResourceManager.GetString("CorrectPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Counting Method.
        /// </summary>
        internal static string CountingMethod {
            get {
                return ResourceManager.GetString("CountingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        internal static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Of Manufacture Name.
        /// </summary>
        internal static string CountryOfManufactureName {
            get {
                return ResourceManager.GetString("CountryOfManufactureName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Of Origin.
        /// </summary>
        internal static string CountryOfOrigin {
            get {
                return ResourceManager.GetString("CountryOfOrigin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note.
        /// </summary>
        internal static string CreditNote {
            get {
                return ResourceManager.GetString("CreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cust No.
        /// </summary>
        internal static string CustNo {
            get {
                return ResourceManager.GetString("CustNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        internal static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Name.
        /// </summary>
        internal static string CustomerName {
            get {
                return ResourceManager.GetString("CustomerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer No.
        /// </summary>
        internal static string CustomerNo {
            get {
                return ResourceManager.GetString("CustomerNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Part No.
        /// </summary>
        internal static string CustomerPartNo {
            get {
                return ResourceManager.GetString("CustomerPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO No.
        /// </summary>
        internal static string CustomerPONumber {
            get {
                return ResourceManager.GetString("CustomerPONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Rejection No.
        /// </summary>
        internal static string CustomerRejectionNo {
            get {
                return ResourceManager.GetString("CustomerRejectionNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CustomerRMA {
            get {
                return ResourceManager.GetString("CustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer / Supplier.
        /// </summary>
        internal static string CustomerSupplier {
            get {
                return ResourceManager.GetString("CustomerSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string Customer_RMA {
            get {
                return ResourceManager.GetString("Customer_RMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirements Enquiry Print.
        /// </summary>
        internal static string CustReqPrint {
            get {
                return ResourceManager.GetString("CustReqPrint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DC.
        /// </summary>
        internal static string DateCode {
            get {
                return ResourceManager.GetString("DateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Code Received.
        /// </summary>
        internal static string DateCodeReceived {
            get {
                return ResourceManager.GetString("DateCodeReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Code Required.
        /// </summary>
        internal static string DateCodeRequired {
            get {
                return ResourceManager.GetString("DateCodeRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Inspected.
        /// </summary>
        internal static string DateInspected {
            get {
                return ResourceManager.GetString("DateInspected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date of Issue.
        /// </summary>
        internal static string DateIssued {
            get {
                return ResourceManager.GetString("DateIssued", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Ordered.
        /// </summary>
        internal static string DateOrdered {
            get {
                return ResourceManager.GetString("DateOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Promised.
        /// </summary>
        internal static string DatePromised {
            get {
                return ResourceManager.GetString("DatePromised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Quoted.
        /// </summary>
        internal static string DateQuoted {
            get {
                return ResourceManager.GetString("DateQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Raised.
        /// </summary>
        internal static string DateRaised {
            get {
                return ResourceManager.GetString("DateRaised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Received.
        /// </summary>
        internal static string DateReceived {
            get {
                return ResourceManager.GetString("DateReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Shipped.
        /// </summary>
        internal static string DateShipped {
            get {
                return ResourceManager.GetString("DateShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DC.
        /// </summary>
        internal static string DC {
            get {
                return ResourceManager.GetString("DC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note.
        /// </summary>
        internal static string DebitNote {
            get {
                return ResourceManager.GetString("DebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note Date.
        /// </summary>
        internal static string DebitNoteDate {
            get {
                return ResourceManager.GetString("DebitNoteDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit note no.
        /// </summary>
        internal static string DebitNoteNo {
            get {
                return ResourceManager.GetString("DebitNoteNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Del.
        /// </summary>
        internal static string Del {
            get {
                return ResourceManager.GetString("Del", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date.
        /// </summary>
        internal static string DeliveryDate {
            get {
                return ResourceManager.GetString("DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date.
        /// </summary>
        internal static string DeliveryDateEPR {
            get {
                return ResourceManager.GetString("DeliveryDateEPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Description.
        /// </summary>
        internal static string Description {
            get {
                return ResourceManager.GetString("Description", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dimensional Weight.
        /// </summary>
        internal static string DimensionalWeight {
            get {
                return ResourceManager.GetString("DimensionalWeight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dimensional Wt.
        /// </summary>
        internal static string DimensionalWeightBulk {
            get {
                return ResourceManager.GetString("DimensionalWeightBulk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dimensional&amp;nbsp;Wt.
        /// </summary>
        internal static string DimensionalWeightNew {
            get {
                return ResourceManager.GetString("DimensionalWeightNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division.
        /// </summary>
        internal static string Division {
            get {
                return ResourceManager.GetString("Division", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Changed.
        /// </summary>
        internal static string DivisionChanged {
            get {
                return ResourceManager.GetString("DivisionChanged", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division of contact from client.
        /// </summary>
        internal static string DivisionOfContact {
            get {
                return ResourceManager.GetString("DivisionOfContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document log.
        /// </summary>
        internal static string DOCNO {
            get {
                return ResourceManager.GetString("DOCNO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date.
        /// </summary>
        internal static string DueDate {
            get {
                return ResourceManager.GetString("DueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty.
        /// </summary>
        internal static string Duty {
            get {
                return ResourceManager.GetString("Duty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Code.
        /// </summary>
        internal static string DutyCode {
            get {
                return ResourceManager.GetString("DutyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Earliest Shipment Date.
        /// </summary>
        internal static string EarliestShipmentDate {
            get {
                return ResourceManager.GetString("EarliestShipmentDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Emailed to:.
        /// </summary>
        internal static string Emailedto {
            get {
                return ResourceManager.GetString("Emailedto", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ENQUIRY / PROGRESS.
        /// </summary>
        internal static string ENQUIRYPROGRESS {
            get {
                return ResourceManager.GetString("ENQUIRYPROGRESS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EORI No.
        /// </summary>
        internal static string EORINumbe {
            get {
                return ResourceManager.GetString("EORINumbe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EORI No.
        /// </summary>
        internal static string EORINumber {
            get {
                return ResourceManager.GetString("EORINumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EPR NO.
        /// </summary>
        internal static string EPRNO {
            get {
                return ResourceManager.GetString("EPRNO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error on email.
        /// </summary>
        internal static string ErrorOnEmail {
            get {
                return ResourceManager.GetString("ErrorOnEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimated Freight Only.
        /// </summary>
        internal static string EstimatedFreight {
            get {
                return ResourceManager.GetString("EstimatedFreight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ETA.
        /// </summary>
        internal static string ETA {
            get {
                return ResourceManager.GetString("ETA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange rate used 1 {0} = {1} {2} {3}.
        /// </summary>
        internal static string ExchangeRateSummary {
            get {
                return ResourceManager.GetString("ExchangeRateSummary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Expedite Note Created.
        /// </summary>
        internal static string ExpeditNoteSubject {
            get {
                return ResourceManager.GetString("ExpeditNoteSubject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fax.
        /// </summary>
        internal static string Fax {
            get {
                return ResourceManager.GetString("Fax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to F.O.B..
        /// </summary>
        internal static string FreeOnBoard {
            get {
                return ResourceManager.GetString("FreeOnBoard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight.
        /// </summary>
        internal static string Freight {
            get {
                return ResourceManager.GetString("Freight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to From.
        /// </summary>
        internal static string From {
            get {
                return ResourceManager.GetString("From", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Inspection Notes.
        /// </summary>
        internal static string GeneralInspectionNotes {
            get {
                return ResourceManager.GetString("GeneralInspectionNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Query.
        /// </summary>
        internal static string GIQuery {
            get {
                return ResourceManager.GetString("GIQuery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Query Report.
        /// </summary>
        internal static string GIQueryScreenReport {
            get {
                return ResourceManager.GetString("GIQueryScreenReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GoodsIn Report for.
        /// </summary>
        internal static string GIReport {
            get {
                return ResourceManager.GetString("GIReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GoodsIn No..
        /// </summary>
        internal static string GoodsInNo {
            get {
                return ResourceManager.GetString("GoodsInNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Note.
        /// </summary>
        internal static string GoodsInNote {
            get {
                return ResourceManager.GetString("GoodsInNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods Receiving Item.
        /// </summary>
        internal static string GoodsReceivingItem {
            get {
                return ResourceManager.GetString("GoodsReceivingItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP.
        /// </summary>
        internal static string GP {
            get {
                return ResourceManager.GetString("GP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP %.
        /// </summary>
        internal static string GPPercent {
            get {
                return ResourceManager.GetString("GPPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GST NO.
        /// </summary>
        internal static string GSTNO {
            get {
                return ResourceManager.GetString("GSTNO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HICStatus.
        /// </summary>
        internal static string HICStatus {
            get {
                return ResourceManager.GetString("HICStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string HUBRFQ {
            get {
                return ResourceManager.GetString("HUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Advance.
        /// </summary>
        internal static string InAdvance {
            get {
                return ResourceManager.GetString("InAdvance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incoterms.
        /// </summary>
        internal static string Incoterms {
            get {
                return ResourceManager.GetString("Incoterms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incurred cost to sales. Yes/No.
        /// </summary>
        internal static string IncurredCostTo {
            get {
                return ResourceManager.GetString("IncurredCostTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incurred cost to sales. Yes/No.
        /// </summary>
        internal static string IncurredCostToSales_Stock {
            get {
                return ResourceManager.GetString("IncurredCostToSales_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspected By.
        /// </summary>
        internal static string InspectedBy {
            get {
                return ResourceManager.GetString("InspectedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspected By:.
        /// </summary>
        internal static string Inspected_by_ {
            get {
                return ResourceManager.GetString("Inspected_by:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Purchase Order.
        /// </summary>
        internal static string InternalPurchaseOrder {
            get {
                return ResourceManager.GetString("InternalPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice.
        /// </summary>
        internal static string Invoice {
            get {
                return ResourceManager.GetString("Invoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Currency.
        /// </summary>
        internal static string InvoiceCurrency {
            get {
                return ResourceManager.GetString("InvoiceCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Date.
        /// </summary>
        internal static string InvoiceDate {
            get {
                return ResourceManager.GetString("InvoiceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Include Cofc.
        /// </summary>
        internal static string InvoiceIncludeCocf {
            get {
                return ResourceManager.GetString("InvoiceIncludeCocf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Item.
        /// </summary>
        internal static string Item {
            get {
                return ResourceManager.GetString("Item", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost.
        /// </summary>
        internal static string LandedCost {
            get {
                return ResourceManager.GetString("LandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Amount.
        /// </summary>
        internal static string LineHeader_Amount {
            get {
                return ResourceManager.GetString("LineHeader_Amount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batch Ref.
        /// </summary>
        internal static string LineHeader_BatchReference {
            get {
                return ResourceManager.GetString("LineHeader_BatchReference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy.
        /// </summary>
        internal static string LineHeader_Buy {
            get {
                return ResourceManager.GetString("LineHeader_Buy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Currency.
        /// </summary>
        internal static string LineHeader_BuyCurrency {
            get {
                return ResourceManager.GetString("LineHeader_BuyCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buyer.
        /// </summary>
        internal static string LineHeader_Buyer {
            get {
                return ResourceManager.GetString("LineHeader_Buyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Terms.
        /// </summary>
        internal static string LineHeader_BuyTerms {
            get {
                return ResourceManager.GetString("LineHeader_BuyTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Resale Price.
        /// </summary>
        internal static string LineHeader_ClientResellValue {
            get {
                return ResourceManager.GetString("LineHeader_ClientResellValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Type.
        /// </summary>
        internal static string LineHeader_CompanyType {
            get {
                return ResourceManager.GetString("LineHeader_CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string LineHeader_Contact {
            get {
                return ResourceManager.GetString("LineHeader_Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        internal static string LineHeader_Country {
            get {
                return ResourceManager.GetString("LineHeader_Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country of Origin.
        /// </summary>
        internal static string LineHeader_CountryOfOrigin {
            get {
                return ResourceManager.GetString("LineHeader_CountryOfOrigin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country of Origins.
        /// </summary>
        internal static string LineHeader_CountryOrigin {
            get {
                return ResourceManager.GetString("LineHeader_CountryOrigin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Part.
        /// </summary>
        internal static string LineHeader_CustomerPartNo {
            get {
                return ResourceManager.GetString("LineHeader_CustomerPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO   ( Sales Order ).
        /// </summary>
        internal static string LineHeader_CustomerPOSoOrder {
            get {
                return ResourceManager.GetString("LineHeader_CustomerPOSoOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string LineHeader_Date {
            get {
                return ResourceManager.GetString("LineHeader_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DC.
        /// </summary>
        internal static string LineHeader_DateCode {
            get {
                return ResourceManager.GetString("LineHeader_DateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Shipped.
        /// </summary>
        internal static string LineHeader_DateShipped {
            get {
                return ResourceManager.GetString("LineHeader_DateShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Del Date.
        /// </summary>
        internal static string LineHeader_DeliveryDate {
            get {
                return ResourceManager.GetString("LineHeader_DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details.
        /// </summary>
        internal static string LineHeader_Details {
            get {
                return ResourceManager.GetString("LineHeader_Details", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date.
        /// </summary>
        internal static string LineHeader_DueDate {
            get {
                return ResourceManager.GetString("LineHeader_DueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty.
        /// </summary>
        internal static string LineHeader_Duty {
            get {
                return ResourceManager.GetString("LineHeader_Duty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Code.
        /// </summary>
        internal static string LineHeader_DutyCode {
            get {
                return ResourceManager.GetString("LineHeader_DutyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Code.
        /// </summary>
        internal static string LineHeader_ECCNCode {
            get {
                return ResourceManager.GetString("LineHeader_ECCNCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ETA.
        /// </summary>
        internal static string LineHeader_ETA {
            get {
                return ResourceManager.GetString("LineHeader_ETA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP.
        /// </summary>
        internal static string LineHeader_GP {
            get {
                return ResourceManager.GetString("LineHeader_GP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP %.
        /// </summary>
        internal static string LineHeader_GPPercent {
            get {
                return ResourceManager.GetString("LineHeader_GPPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GP Value.
        /// </summary>
        internal static string LineHeader_GPValue {
            get {
                return ResourceManager.GetString("LineHeader_GPValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO.
        /// </summary>
        internal static string LineHeader_IPO {
            get {
                return ResourceManager.GetString("LineHeader_IPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPO No..
        /// </summary>
        internal static string LineHeader_IPONo {
            get {
                return ResourceManager.GetString("LineHeader_IPONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost.
        /// </summary>
        internal static string LineHeader_LandedCost {
            get {
                return ResourceManager.GetString("LineHeader_LandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line No.
        /// </summary>
        internal static string LineHeader_LineNo {
            get {
                return ResourceManager.GetString("LineHeader_LineNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mfr.
        /// </summary>
        internal static string LineHeader_Manufacturer {
            get {
                return ResourceManager.GetString("LineHeader_Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string LineHeader_Notes {
            get {
                return ResourceManager.GetString("LineHeader_Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Number.
        /// </summary>
        internal static string LineHeader_OGELNumber {
            get {
                return ResourceManager.GetString("LineHeader_OGELNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pack.
        /// </summary>
        internal static string LineHeader_Package {
            get {
                return ResourceManager.GetString("LineHeader_Package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No.
        /// </summary>
        internal static string LineHeader_PartNo {
            get {
                return ResourceManager.GetString("LineHeader_PartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Photos.
        /// </summary>
        internal static string LineHeader_Photos {
            get {
                return ResourceManager.GetString("LineHeader_Photos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO.
        /// </summary>
        internal static string LineHeader_PO {
            get {
                return ResourceManager.GetString("LineHeader_PO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price.
        /// </summary>
        internal static string LineHeader_Price {
            get {
                return ResourceManager.GetString("LineHeader_Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        internal static string LineHeader_Product {
            get {
                return ResourceManager.GetString("LineHeader_Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Code.
        /// </summary>
        internal static string LineHeader_ProductDutyCode {
            get {
                return ResourceManager.GetString("LineHeader_ProductDutyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit (%).
        /// </summary>
        internal static string LineHeader_ProfitPer {
            get {
                return ResourceManager.GetString("LineHeader_ProfitPer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Promise Date.
        /// </summary>
        internal static string LineHeader_PromiseDate {
            get {
                return ResourceManager.GetString("LineHeader_PromiseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty.
        /// </summary>
        internal static string LineHeader_Quantity {
            get {
                return ResourceManager.GetString("LineHeader_Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Credited.
        /// </summary>
        internal static string LineHeader_QuantityCredited {
            get {
                return ResourceManager.GetString("LineHeader_QuantityCredited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Ordered.
        /// </summary>
        internal static string LineHeader_QuantityOrdered {
            get {
                return ResourceManager.GetString("LineHeader_QuantityOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Shipped.
        /// </summary>
        internal static string LineHeader_QuantityShipped {
            get {
                return ResourceManager.GetString("LineHeader_QuantityShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason.
        /// </summary>
        internal static string LineHeader_Reason {
            get {
                return ResourceManager.GetString("LineHeader_Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference.
        /// </summary>
        internal static string LineHeader_Reference {
            get {
                return ResourceManager.GetString("LineHeader_Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resell Price.
        /// </summary>
        internal static string LineHeader_ResellValue {
            get {
                return ResourceManager.GetString("LineHeader_ResellValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return Date.
        /// </summary>
        internal static string LineHeader_ReturnDate {
            get {
                return ResourceManager.GetString("LineHeader_ReturnDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS.
        /// </summary>
        internal static string LineHeader_ROHS {
            get {
                return ResourceManager.GetString("LineHeader_ROHS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell.
        /// </summary>
        internal static string LineHeader_Sell {
            get {
                return ResourceManager.GetString("LineHeader_Sell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Currency.
        /// </summary>
        internal static string LineHeader_SellCurrency {
            get {
                return ResourceManager.GetString("LineHeader_SellCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Terms.
        /// </summary>
        internal static string LineHeader_SellTerms {
            get {
                return ResourceManager.GetString("LineHeader_SellTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Cost.
        /// </summary>
        internal static string LineHeader_ShipCost {
            get {
                return ResourceManager.GetString("LineHeader_ShipCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        internal static string LineHeader_Supplier {
            get {
                return ResourceManager.GetString("LineHeader_Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Name.
        /// </summary>
        internal static string LineHeader_SupplierName {
            get {
                return ResourceManager.GetString("LineHeader_SupplierName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Part.
        /// </summary>
        internal static string LineHeader_SupplierPartNo {
            get {
                return ResourceManager.GetString("LineHeader_SupplierPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable?.
        /// </summary>
        internal static string LineHeader_Taxable {
            get {
                return ResourceManager.GetString("LineHeader_Taxable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Buy.
        /// </summary>
        internal static string LineHeader_TotalBuy {
            get {
                return ResourceManager.GetString("LineHeader_TotalBuy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Cost .
        /// </summary>
        internal static string LineHeader_TotalCostPrice {
            get {
                return ResourceManager.GetString("LineHeader_TotalCostPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Price.
        /// </summary>
        internal static string LineHeader_TotalPrice {
            get {
                return ResourceManager.GetString("LineHeader_TotalPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price To Client.
        /// </summary>
        internal static string LineHeader_TotalPriceToClient {
            get {
                return ResourceManager.GetString("LineHeader_TotalPriceToClient", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Profit ( % ).
        /// </summary>
        internal static string LineHeader_TotalProfit {
            get {
                return ResourceManager.GetString("LineHeader_TotalProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Sell.
        /// </summary>
        internal static string LineHeader_TotalSell {
            get {
                return ResourceManager.GetString("LineHeader_TotalSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Sell .
        /// </summary>
        internal static string LineHeader_TotalSellPrice {
            get {
                return ResourceManager.GetString("LineHeader_TotalSellPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Price.
        /// </summary>
        internal static string LineHeader_UnitPrice {
            get {
                return ResourceManager.GetString("LineHeader_UnitPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By.
        /// </summary>
        internal static string LineHeader_UpdatedBy {
            get {
                return ResourceManager.GetString("LineHeader_UpdatedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uplift Amount.
        /// </summary>
        internal static string LineHeader_UpliftAmount {
            get {
                return ResourceManager.GetString("LineHeader_UpliftAmount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Uplift Price.
        /// </summary>
        internal static string LineHeader_UpliftPrice {
            get {
                return ResourceManager.GetString("LineHeader_UpliftPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lines and quantites for SalesOrder.
        /// </summary>
        internal static string Lines_and_quantites_for_SalesOrder {
            get {
                return ResourceManager.GetString("Lines and quantites for SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Local equivalent is for internal purpose and not to be used for payment, please pay in the currency the invoice has been issued in.
        /// </summary>
        internal static string LocalEquivalent {
            get {
                return ResourceManager.GetString("LocalEquivalent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date raised.
        /// </summary>
        internal static string LogisticSRMADate {
            get {
                return ResourceManager.GetString("LogisticSRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMA No.
        /// </summary>
        internal static string LogisticSRMANo {
            get {
                return ResourceManager.GetString("LogisticSRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Advance Payment on Open Orders?.
        /// </summary>
        internal static string Manager_AdvancePay {
            get {
                return ResourceManager.GetString("Manager_AdvancePay", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorized.
        /// </summary>
        internal static string Manager_Authorized {
            get {
                return ResourceManager.GetString("Manager_Authorized", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        internal static string Manager_Comments {
            get {
                return ResourceManager.GetString("Manager_Comments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Countersigned (where applicable).
        /// </summary>
        internal static string Manager_Countersign {
            get {
                return ResourceManager.GetString("Manager_Countersign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string Manager_Date {
            get {
                return ResourceManager.GetString("Manager_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EARI Member ?.
        /// </summary>
        internal static string Manager_EARIMember {
            get {
                return ResourceManager.GetString("Manager_EARIMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EARI Reported ?.
        /// </summary>
        internal static string Manager_EARIReported {
            get {
                return ResourceManager.GetString("Manager_EARIReported", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  FOR Stock ?.
        /// </summary>
        internal static string Manager_FORStock {
            get {
                return ResourceManager.GetString("Manager_FORStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Out-standing Debit notes on P/L?.
        /// </summary>
        internal static string Manager_OutStanding {
            get {
                return ResourceManager.GetString("Manager_OutStanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Payment Authorized By.
        /// </summary>
        internal static string Manager_PayAuthBy {
            get {
                return ResourceManager.GetString("Manager_PayAuthBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Ledger Terms.
        /// </summary>
        internal static string Manager_SalesLedger {
            get {
                return ResourceManager.GetString("Manager_SalesLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Ledger Overdue?.
        /// </summary>
        internal static string Manager_SalesOverdue {
            get {
                return ResourceManager.GetString("Manager_SalesOverdue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOR Signed ?.
        /// </summary>
        internal static string Manager_SORSigned {
            get {
                return ResourceManager.GetString("Manager_SORSigned", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Value.
        /// </summary>
        internal static string Manager_TotalValue {
            get {
                return ResourceManager.GetString("Manager_TotalValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Value.
        /// </summary>
        internal static string Manager_TotalValue1 {
            get {
                return ResourceManager.GetString("Manager_TotalValue1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Values Correct?.
        /// </summary>
        internal static string Manager_ValuesCorrect {
            get {
                return ResourceManager.GetString("Manager_ValuesCorrect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mfr.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mfr Received.
        /// </summary>
        internal static string ManufacturerReceived {
            get {
                return ResourceManager.GetString("ManufacturerReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mfr Required.
        /// </summary>
        internal static string ManufacturerRequired {
            get {
                return ResourceManager.GetString("ManufacturerRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mfg.
        /// </summary>
        internal static string MFG {
            get {
                return ResourceManager.GetString("MFG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4c: Move into stock (Sales or Purchasing to complete).
        /// </summary>
        internal static string Move_into_Stock {
            get {
                return ResourceManager.GetString("Move_into_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to (Manufacturer/Package/date Code).
        /// </summary>
        internal static string MPDC {
            get {
                return ResourceManager.GetString("MPDC", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MSL.
        /// </summary>
        internal static string MSL {
            get {
                return ResourceManager.GetString("MSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string NAME {
            get {
                return ResourceManager.GetString("NAME", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NetSpecify.
        /// </summary>
        internal static string NetSpecify {
            get {
                return ResourceManager.GetString("NetSpecify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New NPR.
        /// </summary>
        internal static string NEWNPR {
            get {
                return ResourceManager.GetString("NEWNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        internal static string NPRComments {
            get {
                return ResourceManager.GetString("NPRComments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed by.
        /// </summary>
        internal static string NPRCompletedByNo {
            get {
                return ResourceManager.GetString("NPRCompletedByNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete Date.
        /// </summary>
        internal static string NPRCompleteddATE {
            get {
                return ResourceManager.GetString("NPRCompleteddATE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Date.
        /// </summary>
        internal static string NPRdate {
            get {
                return ResourceManager.GetString("NPRdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4. Action Required (Sales/Purchasing to complete). Please complete either 4a,4b,4c or 4d.
        /// </summary>
        internal static string NPRHeading_ActionRequired {
            get {
                return ResourceManager.GetString("NPRHeading_ActionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All customers unauthorized return should be recorded in Section 3: Non Conformance details..
        /// </summary>
        internal static string NPRHeading_AllCustomer {
            get {
                return ResourceManager.GetString("NPRHeading_AllCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 3. Non Conformance Details. Reasons for rejection (Warehouse/Sales/Quality).
        /// </summary>
        internal static string NPRHeading_NonConformance {
            get {
                return ResourceManager.GetString("NPRHeading_NonConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nonconforming Product Report.
        /// </summary>
        internal static string NPRHeading_Nonconforming {
            get {
                return ResourceManager.GetString("NPRHeading_Nonconforming", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2. Order Details (Warehouse to complete).
        /// </summary>
        internal static string NPRHeading_OrderDetail {
            get {
                return ResourceManager.GetString("NPRHeading_OrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1. Originator (Warehouse to complete).
        /// </summary>
        internal static string NPRHeading_Originator {
            get {
                return ResourceManager.GetString("NPRHeading_Originator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR No.
        /// </summary>
        internal static string NPRNO {
            get {
                return ResourceManager.GetString("NPRNO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason1.
        /// </summary>
        internal static string NPRReason1 {
            get {
                return ResourceManager.GetString("NPRReason1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason2.
        /// </summary>
        internal static string NPRReason2 {
            get {
                return ResourceManager.GetString("NPRReason2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Report for GoodsIn.
        /// </summary>
        internal static string NPRReport {
            get {
                return ResourceManager.GetString("NPRReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR No.
        /// </summary>
        internal static string NPR_No {
            get {
                return ResourceManager.GetString("NPR_No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to THIS ORDER CONTAINS AN OGEL LINE.
        /// </summary>
        internal static string OGELStatusWarning {
            get {
                return ResourceManager.GetString("OGELStatusWarning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order No..
        /// </summary>
        internal static string OrderNo {
            get {
                return ResourceManager.GetString("OrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Other (Specify).
        /// </summary>
        internal static string OtherSpecify {
            get {
                return ResourceManager.GetString("OtherSpecify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outworker Name.
        /// </summary>
        internal static string OutworkerName {
            get {
                return ResourceManager.GetString("OutworkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outworker P/o No.
        /// </summary>
        internal static string OutworkerPONo {
            get {
                return ResourceManager.GetString("OutworkerPONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package.
        /// </summary>
        internal static string Package {
            get {
                return ResourceManager.GetString("Package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Received.
        /// </summary>
        internal static string PackageReceived {
            get {
                return ResourceManager.GetString("PackageReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Required.
        /// </summary>
        internal static string PackageRequired {
            get {
                return ResourceManager.GetString("PackageRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packaging.
        /// </summary>
        internal static string Packaging {
            get {
                return ResourceManager.GetString("Packaging", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packing Slip.
        /// </summary>
        internal static string PackingSlip {
            get {
                return ResourceManager.GetString("PackingSlip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packing Slip include invoice and CofC.
        /// </summary>
        internal static string PackingSlipIncludeInvoiceAndCofc {
            get {
                return ResourceManager.GetString("PackingSlipIncludeInvoiceAndCofc", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Marking.
        /// </summary>
        internal static string PartMarking {
            get {
                return ResourceManager.GetString("PartMarking", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No.
        /// </summary>
        internal static string PartNo {
            get {
                return ResourceManager.GetString("PartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No Received.
        /// </summary>
        internal static string PartNoReceived {
            get {
                return ResourceManager.GetString("PartNoReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No Required.
        /// </summary>
        internal static string PartNoRequired {
            get {
                return ResourceManager.GetString("PartNoRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No.
        /// </summary>
        internal static string Part_No {
            get {
                return ResourceManager.GetString("Part_No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Date.
        /// </summary>
        internal static string PaymentAuthDate {
            get {
                return ResourceManager.GetString("PaymentAuthDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comment.
        /// </summary>
        internal static string PaymentComment {
            get {
                return ResourceManager.GetString("PaymentComment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDF.
        /// </summary>
        internal static string PDF {
            get {
                return ResourceManager.GetString("PDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Photo(s).
        /// </summary>
        internal static string Photos {
            get {
                return ResourceManager.GetString("Photos", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Picked By:.
        /// </summary>
        internal static string Picked_by_ {
            get {
                return ResourceManager.GetString("Picked_by:", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO.
        /// </summary>
        internal static string PO {
            get {
                return ResourceManager.GetString("PO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO / CRMA.
        /// </summary>
        internal static string POCRMA {
            get {
                return ResourceManager.GetString("POCRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Line Serial No.
        /// </summary>
        internal static string POLineSerialNo {
            get {
                return ResourceManager.GetString("POLineSerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO NO.
        /// </summary>
        internal static string PO_NO {
            get {
                return ResourceManager.GetString("PO_NO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price.
        /// </summary>
        internal static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        internal static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print : .
        /// </summary>
        internal static string PrintCount {
            get {
                return ResourceManager.GetString("PrintCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printed.
        /// </summary>
        internal static string Printed {
            get {
                return ResourceManager.GetString("Printed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        internal static string Product {
            get {
                return ResourceManager.GetString("Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Code Pack.
        /// </summary>
        internal static string ProductDutyCode {
            get {
                return ResourceManager.GetString("ProductDutyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pro Forma Invoice.
        /// </summary>
        internal static string ProFormaInvoice {
            get {
                return ResourceManager.GetString("ProFormaInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order.
        /// </summary>
        internal static string PurchaseOrder {
            get {
                return ResourceManager.GetString("PurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Report.
        /// </summary>
        internal static string PurchaseOrderReport {
            get {
                return ResourceManager.GetString("PurchaseOrderReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instruction to WH &amp; Quality control notes.
        /// </summary>
        internal static string QCNotes {
            get {
                return ResourceManager.GetString("QCNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Estimated Freight Only.
        /// </summary>
        internal static string QFreight {
            get {
                return ResourceManager.GetString("QFreight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Q Location.
        /// </summary>
        internal static string QLocation {
            get {
                return ResourceManager.GetString("QLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty.
        /// </summary>
        internal static string Qty {
            get {
                return ResourceManager.GetString("Qty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Advised.
        /// </summary>
        internal static string QtyAdvised {
            get {
                return ResourceManager.GetString("QtyAdvised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Allocated.
        /// </summary>
        internal static string QtyAllocated {
            get {
                return ResourceManager.GetString("QtyAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Outstanding.
        /// </summary>
        internal static string QtyOutstanding {
            get {
                return ResourceManager.GetString("QtyOutstanding", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Credited.
        /// </summary>
        internal static string QuantityCredited {
            get {
                return ResourceManager.GetString("QuantityCredited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Ordered.
        /// </summary>
        internal static string QuantityOrdered {
            get {
                return ResourceManager.GetString("QuantityOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Received.
        /// </summary>
        internal static string QuantityReceived {
            get {
                return ResourceManager.GetString("QuantityReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Required.
        /// </summary>
        internal static string QuantityRequired {
            get {
                return ResourceManager.GetString("QuantityRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Shipped.
        /// </summary>
        internal static string QuantityShipped {
            get {
                return ResourceManager.GetString("QuantityShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote.
        /// </summary>
        internal static string Quote {
            get {
                return ResourceManager.GetString("Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quoted.
        /// </summary>
        internal static string Quoted {
            get {
                return ResourceManager.GetString("Quoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raised By.
        /// </summary>
        internal static string RaisedBy {
            get {
                return ResourceManager.GetString("RaisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raised By.
        /// </summary>
        internal static string Raised_By {
            get {
                return ResourceManager.GetString("Raised_By", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason.
        /// </summary>
        internal static string Reason {
            get {
                return ResourceManager.GetString("Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received.
        /// </summary>
        internal static string Received {
            get {
                return ResourceManager.GetString("Received", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received By.
        /// </summary>
        internal static string ReceivedBy {
            get {
                return ResourceManager.GetString("ReceivedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Date.
        /// </summary>
        internal static string ReceivedDate {
            get {
                return ResourceManager.GetString("ReceivedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        internal static string RefAddress {
            get {
                return ResourceManager.GetString("RefAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        internal static string RefAddress1 {
            get {
                return ResourceManager.GetString("RefAddress1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Address.
        /// </summary>
        internal static string RefAddress2 {
            get {
                return ResourceManager.GetString("RefAddress2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comment.
        /// </summary>
        internal static string RefComment {
            get {
                return ResourceManager.GetString("RefComment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string RefEmail {
            get {
                return ResourceManager.GetString("RefEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string RefEmail1 {
            get {
                return ResourceManager.GetString("RefEmail1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string RefEmail2 {
            get {
                return ResourceManager.GetString("RefEmail2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference.
        /// </summary>
        internal static string Reference {
            get {
                return ResourceManager.GetString("Reference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fax.
        /// </summary>
        internal static string RefFax {
            get {
                return ResourceManager.GetString("RefFax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fax.
        /// </summary>
        internal static string RefFax1 {
            get {
                return ResourceManager.GetString("RefFax1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fax.
        /// </summary>
        internal static string RefFax2 {
            get {
                return ResourceManager.GetString("RefFax2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ref Invoice.
        /// </summary>
        internal static string RefInvoice {
            get {
                return ResourceManager.GetString("RefInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string RefName {
            get {
                return ResourceManager.GetString("RefName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string RefName1 {
            get {
                return ResourceManager.GetString("RefName1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string RefName2 {
            get {
                return ResourceManager.GetString("RefName2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reference Purchase Order.
        /// </summary>
        internal static string RefPurchaseOrder {
            get {
                return ResourceManager.GetString("RefPurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tel.
        /// </summary>
        internal static string RefTEL {
            get {
                return ResourceManager.GetString("RefTEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tel.
        /// </summary>
        internal static string RefTel1 {
            get {
                return ResourceManager.GetString("RefTel1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tel.
        /// </summary>
        internal static string RefTel2 {
            get {
                return ResourceManager.GetString("RefTel2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejected Qty.
        /// </summary>
        internal static string RejectedQty {
            get {
                return ResourceManager.GetString("RejectedQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reply.
        /// </summary>
        internal static string Reply {
            get {
                return ResourceManager.GetString("Reply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required.
        /// </summary>
        internal static string Required {
            get {
                return ResourceManager.GetString("Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4d: Required outwork (Sales or Purchasing to complete).
        /// </summary>
        internal static string Required_outwork {
            get {
                return ResourceManager.GetString("Required_outwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return Address.
        /// </summary>
        internal static string ReturnAddress {
            get {
                return ResourceManager.GetString("ReturnAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expected Return.
        /// </summary>
        internal static string ReturnDate {
            get {
                return ResourceManager.GetString("ReturnDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4a : Return to supplier (Purchasing to complete).
        /// </summary>
        internal static string Return_to_supplier {
            get {
                return ResourceManager.GetString("Return_to_supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RMA.
        /// </summary>
        internal static string RMA {
            get {
                return ResourceManager.GetString("RMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Compliance.
        /// </summary>
        internal static string ROHS {
            get {
                return ResourceManager.GetString("ROHS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Compliance.
        /// </summary>
        internal static string ROHScomplience {
            get {
                return ResourceManager.GetString("ROHScomplience", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Received.
        /// </summary>
        internal static string ROHSReceived {
            get {
                return ResourceManager.GetString("ROHSReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Required.
        /// </summary>
        internal static string ROHSRequired {
            get {
                return ResourceManager.GetString("ROHSRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO No.
        /// </summary>
        internal static string Saleorderno {
            get {
                return ResourceManager.GetString("Saleorderno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Order Acknowledgement.
        /// </summary>
        internal static string SalesOrder {
            get {
                return ResourceManager.GetString("SalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOA.
        /// </summary>
        internal static string SalesOrderNew {
            get {
                return ResourceManager.GetString("SalesOrderNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO No.
        /// </summary>
        internal static string SalesOrderNo {
            get {
                return ResourceManager.GetString("SalesOrderNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Requirements.
        /// </summary>
        internal static string SalesorderRequirements {
            get {
                return ResourceManager.GetString("SalesorderRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order.
        /// </summary>
        internal static string SalesOrder_print {
            get {
                return ResourceManager.GetString("SalesOrder_print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson.
        /// </summary>
        internal static string Salesperson {
            get {
                return ResourceManager.GetString("Salesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson / Authoriser.
        /// </summary>
        internal static string SalespersonAuthoriser {
            get {
                return ResourceManager.GetString("SalespersonAuthoriser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Rep.
        /// </summary>
        internal static string SalesRep {
            get {
                return ResourceManager.GetString("SalesRep", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string Sales_Date {
            get {
                return ResourceManager.GetString("Sales_Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5. Sales detail &amp;amp; Authorisation.
        /// </summary>
        internal static string Sales_detail_Authorisation {
            get {
                return ResourceManager.GetString("Sales_detail_Authorisation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pro Forma Attached ?.
        /// </summary>
        internal static string Sales_FormaAttached {
            get {
                return ResourceManager.GetString("Sales_FormaAttached", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson.
        /// </summary>
        internal static string Sales_Person {
            get {
                return ResourceManager.GetString("Sales_Person", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raised by.
        /// </summary>
        internal static string Sales_RaisedBy {
            get {
                return ResourceManager.GetString("Sales_RaisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Value.
        /// </summary>
        internal static string Sales_TotalValue {
            get {
                return ResourceManager.GetString("Sales_TotalValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4b : Scrap (Purchasing to complete).
        /// </summary>
        internal static string Scrap {
            get {
                return ResourceManager.GetString("Scrap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Base.
        /// </summary>
        internal static string SellBase {
            get {
                return ResourceManager.GetString("SellBase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Currency.
        /// </summary>
        internal static string SellCurrency {
            get {
                return ResourceManager.GetString("SellCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sell Terms.
        /// </summary>
        internal static string SellTerms {
            get {
                return ResourceManager.GetString("SellTerms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial Nos Recorded.
        /// </summary>
        internal static string SerialNosRecorded {
            get {
                return ResourceManager.GetString("SerialNosRecorded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship ASAP.
        /// </summary>
        internal static string ShipASAP {
            get {
                return ResourceManager.GetString("ShipASAP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Cost.
        /// </summary>
        internal static string ShipCost {
            get {
                return ResourceManager.GetString("ShipCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipment date.
        /// </summary>
        internal static string Shipment_date {
            get {
                return ResourceManager.GetString("Shipment date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped By.
        /// </summary>
        internal static string ShippedBy {
            get {
                return ResourceManager.GetString("ShippedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Via.
        /// </summary>
        internal static string ShippedVia {
            get {
                return ResourceManager.GetString("ShippedVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Cost.
        /// </summary>
        internal static string Shipping {
            get {
                return ResourceManager.GetString("Shipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping A/C.
        /// </summary>
        internal static string ShippingAccount {
            get {
                return ResourceManager.GetString("ShippingAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Charge.
        /// </summary>
        internal static string ShippingCharge {
            get {
                return ResourceManager.GetString("ShippingCharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Cost.
        /// </summary>
        internal static string ShippingCost {
            get {
                return ResourceManager.GetString("ShippingCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ShippingMethod.
        /// </summary>
        internal static string ShippingMethod {
            get {
                return ResourceManager.GetString("ShippingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Notes.
        /// </summary>
        internal static string ShippingNotes {
            get {
                return ResourceManager.GetString("ShippingNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship To.
        /// </summary>
        internal static string ShipTo {
            get {
                return ResourceManager.GetString("ShipTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship To Address.
        /// </summary>
        internal static string ShipToAdd {
            get {
                return ResourceManager.GetString("ShipToAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship to VAT No.
        /// </summary>
        internal static string ShipToVatNo {
            get {
                return ResourceManager.GetString("ShipToVatNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Via.
        /// </summary>
        internal static string ShipVia {
            get {
                return ResourceManager.GetString("ShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signed Purchasing.
        /// </summary>
        internal static string SignedPurchashing {
            get {
                return ResourceManager.GetString("SignedPurchashing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Signed Sales.
        /// </summary>
        internal static string SignedSales {
            get {
                return ResourceManager.GetString("SignedSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sold To.
        /// </summary>
        internal static string SoldTo {
            get {
                return ResourceManager.GetString("SoldTo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line No.
        /// </summary>
        internal static string SOLineNo {
            get {
                return ResourceManager.GetString("SOLineNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to S.O.No..
        /// </summary>
        internal static string SONo {
            get {
                return ResourceManager.GetString("SONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO-SO Promise Date.
        /// </summary>
        internal static string SOPromiseDate {
            get {
                return ResourceManager.GetString("SOPromiseDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Report for SO.
        /// </summary>
        internal static string SOReport {
            get {
                return ResourceManager.GetString("SOReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO / SRMA.
        /// </summary>
        internal static string SOSRMA {
            get {
                return ResourceManager.GetString("SOSRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Special Comments.
        /// </summary>
        internal static string SpecialComments {
            get {
                return ResourceManager.GetString("SpecialComments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to StockinQuantity.
        /// </summary>
        internal static string StockinQuantity {
            get {
                return ResourceManager.GetString("StockinQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock location. &lt;br /&gt;(EPO/Consignment department only).
        /// </summary>
        internal static string StockLocation {
            get {
                return ResourceManager.GetString("StockLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock lot code.
        /// </summary>
        internal static string stocklotcode {
            get {
                return ResourceManager.GetString("stocklotcode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated Stock Routing Check Card.
        /// </summary>
        internal static string Stockprint {
            get {
                return ResourceManager.GetString("Stockprint", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subject.
        /// </summary>
        internal static string Subject {
            get {
                return ResourceManager.GetString("Subject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Subtotal.
        /// </summary>
        internal static string SubTotal {
            get {
                return ResourceManager.GetString("SubTotal", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Cost.
        /// </summary>
        internal static string SubTotalCostPrice {
            get {
                return ResourceManager.GetString("SubTotalCostPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total GP.
        /// </summary>
        internal static string SubTotalGP {
            get {
                return ResourceManager.GetString("SubTotalGP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total GP %.
        /// </summary>
        internal static string SubTotalGPPercent {
            get {
                return ResourceManager.GetString("SubTotalGPPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Sell.
        /// </summary>
        internal static string SubTotalSellPrice {
            get {
                return ResourceManager.GetString("SubTotalSellPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        internal static string Supplier {
            get {
                return ResourceManager.GetString("Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoice {
            get {
                return ResourceManager.GetString("SupplierInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier New.
        /// </summary>
        internal static string SupplierNew {
            get {
                return ResourceManager.GetString("SupplierNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Part No.
        /// </summary>
        internal static string SupplierPartNo {
            get {
                return ResourceManager.GetString("SupplierPartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Reference.
        /// </summary>
        internal static string SupplierRef {
            get {
                return ResourceManager.GetString("SupplierRef", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SupplierRMA {
            get {
                return ResourceManager.GetString("SupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier to credit.
        /// </summary>
        internal static string SupplierToCredit {
            get {
                return ResourceManager.GetString("SupplierToCredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Type.
        /// </summary>
        internal static string SupplierType {
            get {
                return ResourceManager.GetString("SupplierType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string Supplier_RMA {
            get {
                return ResourceManager.GetString("Supplier_RMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA No.
        /// </summary>
        internal static string Supplier_RMA_No {
            get {
                return ResourceManager.GetString("Supplier_RMA_No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Ship Via.
        /// </summary>
        internal static string Supplier_Ship_Via {
            get {
                return ResourceManager.GetString("Supplier_Ship_Via", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Ship via a/c no.
        /// </summary>
        internal static string Supplier_Ship_via_no {
            get {
                return ResourceManager.GetString("Supplier_Ship_via_no", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supp No..
        /// </summary>
        internal static string SUPPNO {
            get {
                return ResourceManager.GetString("SUPPNO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to kg.
        /// </summary>
        internal static string Symbol_Weight_KG {
            get {
                return ResourceManager.GetString("Symbol_Weight_KG", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to lb.
        /// </summary>
        internal static string Symbol_Weight_Pounds {
            get {
                return ResourceManager.GetString("Symbol_Weight_Pounds", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taken By.
        /// </summary>
        internal static string TakenBy {
            get {
                return ResourceManager.GetString("TakenBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target.
        /// </summary>
        internal static string Target {
            get {
                return ResourceManager.GetString("Target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        internal static string Tax {
            get {
                return ResourceManager.GetString("Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Taxable.
        /// </summary>
        internal static string Taxable {
            get {
                return ResourceManager.GetString("Taxable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Name.
        /// </summary>
        internal static string TaxName {
            get {
                return ResourceManager.GetString("TaxName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tel.
        /// </summary>
        internal static string Tel {
            get {
                return ResourceManager.GetString("Tel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telephone.
        /// </summary>
        internal static string Telephone {
            get {
                return ResourceManager.GetString("Telephone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms.
        /// </summary>
        internal static string Terms {
            get {
                return ResourceManager.GetString("Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To.
        /// </summary>
        internal static string To {
            get {
                return ResourceManager.GetString("To", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total.
        /// </summary>
        internal static string Total {
            get {
                return ResourceManager.GetString("Total", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Allocated Sell.
        /// </summary>
        internal static string TotalAllocSell {
            get {
                return ResourceManager.GetString("TotalAllocSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Buy.
        /// </summary>
        internal static string TotalBuy {
            get {
                return ResourceManager.GetString("TotalBuy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Price.
        /// </summary>
        internal static string TotalPrice {
            get {
                return ResourceManager.GetString("TotalPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Rejected value.
        /// </summary>
        internal static string TotalRejectedValue {
            get {
                return ResourceManager.GetString("TotalRejectedValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Sell.
        /// </summary>
        internal static string TotalSell {
            get {
                return ResourceManager.GetString("TotalSell", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Weight.
        /// </summary>
        internal static string TotalWeight {
            get {
                return ResourceManager.GetString("TotalWeight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Wt.
        /// </summary>
        internal static string TotalWeightNew {
            get {
                return ResourceManager.GetString("TotalWeightNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ** Please verify that the source of supply meets the customer’s requirements **.
        /// </summary>
        internal static string TraceabilityNeeded {
            get {
                return ResourceManager.GetString("TraceabilityNeeded", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TT.
        /// </summary>
        internal static string TT {
            get {
                return ResourceManager.GetString("TT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Price.
        /// </summary>
        internal static string UnitPrice {
            get {
                return ResourceManager.GetString("UnitPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Cost.
        /// </summary>
        internal static string Unit_Cost {
            get {
                return ResourceManager.GetString("Unit_Cost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upon Receipt.
        /// </summary>
        internal static string UponReceipt {
            get {
                return ResourceManager.GetString("UponReceipt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value &amp; Currency.
        /// </summary>
        internal static string ValueCurrency {
            get {
                return ResourceManager.GetString("ValueCurrency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VAT No.
        /// </summary>
        internal static string VATNo {
            get {
                return ResourceManager.GetString("VATNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes To Warehouse.
        /// </summary>
        internal static string WarehouseNotes {
            get {
                return ResourceManager.GetString("WarehouseNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to XML.
        /// </summary>
        internal static string XML {
            get {
                return ResourceManager.GetString("XML", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Credit Note.
        /// </summary>
        internal static string YourCreditNote {
            get {
                return ResourceManager.GetString("YourCreditNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Debit Note.
        /// </summary>
        internal static string YourDebitNote {
            get {
                return ResourceManager.GetString("YourDebitNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Invoice.
        /// </summary>
        internal static string YourInvoice {
            get {
                return ResourceManager.GetString("YourInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your PO .
        /// </summary>
        internal static string YourPONumber {
            get {
                return ResourceManager.GetString("YourPONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Return Note.
        /// </summary>
        internal static string YourReturnNote {
            get {
                return ResourceManager.GetString("YourReturnNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [#QUANTITYINSTOCK#].
        /// </summary>
        internal static string @__QUANTITYINSTOCK__ {
            get {
                return ResourceManager.GetString("[#QUANTITYINSTOCK#]", resourceCulture);
            }
        }
    }
}
