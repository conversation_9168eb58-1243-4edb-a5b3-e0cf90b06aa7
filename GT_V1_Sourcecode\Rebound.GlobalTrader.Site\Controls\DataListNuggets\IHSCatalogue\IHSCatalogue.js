Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.prototype={get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_blnShowUninspectedOnly:function(){return this._blnShowUninspectedOnly},set_blnShowUninspectedOnly:function(n){this._blnShowUninspectedOnly!==n&&(this._blnShowUninspectedOnly=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_AllowAvgPrice:function(){return this._AllowAvgPrice},set_AllowAvgPrice:function(n){this._AllowAvgPrice!==n&&(this._AllowAvgPrice=n)},get_redirectFromMFR:function(){return this._redirectFromMFR},set_redirectFromMFR:function(n){this._redirectFromMFR!==n&&(this._redirectFromMFR=n)},initialize:function(){$("#ctl00_cphMain_ctlPageTitle_tab1").hide();$("#ctl00_cphMain_ctlPageTitle_tab2").hide();this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/IHSCatalogue";this._strDataObject="IHSCatalogue";Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.callBaseMethod(this,"initialize");this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.exportCSV))},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this._redirectFromMFR&&(this.setFilterFieldValue("ctlManufacturer",this._redirectFromMFR),this.applyFilter());this.getData()},dispose:function(){this.isDisposed||(this._blnShowUninspectedOnly=null,this._blnPOHub=null,this._AllowAvgPrice=null,this._ibtnExportCSV&&$R_IBTN.clearHandlers(this._ibtnExportCSV),this._ibtnExportCSV=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._blnShowUninspectedOnly=this._intCurrentTab==1;this.updateFilterVisibility();this.getData();this.showContentLoading(!1)},setupDataCall:function(){},getDataOK:function(){for(var n,r,t="",i=0,u=this._objResult.Results.length;i<u;i++)t="",imagepath="",n=this._objResult.Results[i],n.IsPDFAvailable==!0?(t=!0,imagepath="app_themes/original/images/IconButton/pdficon.jpg"):n.IsPDFAvailable==!1&&(t=!1),r=this._blnPOHub&&this._AllowAvgPrice?[$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part),$R_FN.setCleanTextValue(n.LifeCycleStage)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr),$R_FN.setCleanTextValue(n.MSLName)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.CountryOfOrigin),$R_FN.setCleanTextValue(n.HTSCode)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Packaging),$R_FN.setCleanTextValue(n.PackagingSize)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.AveragePrice),$R_FN.setCleanTextValue(n.Date)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Descriptions),$R_FN.setCleanTextValue(n.ECCNCode)),$R_FN.setCleanTextValue(t==!0?String.format('&nbsp;&nbsp;<center><a href="javascript:void(0);" onclick="$RGT_openIHSDoc({0},{1})" title="Click to View and add docs"><img border=\'0\'  src='+imagepath+" width='30' height='26'><\/center><\/a>",n.IHSPartsId,0):String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openIHSDoc({0},{1})" style=\'text-decoration:none;\' title="Click to add docs"><center><b>+<\/b><\/center><\/a>',n.IHSPartsId,0)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.APIImportedData),"")]:[$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part),$R_FN.setCleanTextValue(n.LifeCycleStage)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr),$R_FN.setCleanTextValue(n.MSLName)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.CountryOfOrigin),$R_FN.setCleanTextValue(n.HTSCode)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Packaging),$R_FN.setCleanTextValue(n.PackagingSize)),$R_FN.setCleanTextValue(n.Date),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Descriptions),$R_FN.setCleanTextValue(n.ECCNCode)),$R_FN.setCleanTextValue(t==!0?String.format('&nbsp;&nbsp;<center><a href="javascript:void(0);" onclick="$RGT_openIHSDoc({0},{1})"   title="Click to View and add docs"><img border=\'0\'  src='+imagepath+" width='30' height='26'><\/center><\/a>",n.IHSPartsId,0):String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openIHSDoc({0},{1})" style=\'text-decoration:none;\'title="Click to add docs"><center><b>+<\/b><\/center><\/a>',n.IHSPartsId,0)),$R_FN.setCleanTextValue($R_FN.setCleanTextValue(n.APIImportedData))],this._table.addRow(r,n.IHSPartsId,!1),r=null,n=null},exportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/DataListNuggets/IHSCatalogue");n.set_DataObject("IHSCatalogue");n.addParameter("ViewLevel",this._enmViewLevel);n.set_DataAction("ExportToCSV");n._intTimeoutMilliseconds=5e5;n.addParameter("SortIndex",this._sortIndex);n.addParameter("SortDir",this._sortDir);n.addParameter("PageIndex",this._pageIndex);n.addParameter("PageSize",this._pageSize);n.addParameter("Part",this.getFilterFieldValue("ctlPart"));n.addParameter("Manufacturer",this.getFilterFieldValue("ctlManufacturer"));n.addParameter("countryOforigin",this.getFilterFieldValue("ctlcountryOforigin"));n.addParameter("MSL",this.getFilterFieldValue("ctlMSL"));n.addParameter("HtcCode",this.getFilterFieldValue("ctlHtcCode"));n.addParameter("Description",this.getFilterFieldValue("ctlDescription"));n.addParameter("RecentOnly",this.getFilterFieldValue("ctlRecentOnly"));n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);