﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
=================================================================================================================================================
TASK      		UPDATED BY   DATE			ACTION 		DESCRIPTION
[US-244339]		Phuc Hoang   08-May-2025	Update		Invoice - Inconsistent (0.01) final Invoice Amount on the printed Invoice and Import file
=================================================================================================================================================
*/

CREATE OR ALTER VIEW [dbo].[vwInvoiceExportPV]
/*****************************************************************************/
--* SG 01.10.2021
--* - Updated to use Exported flag instead of auto-export
--* SG 12.02.2021
--* - Added Tax name for Sabine (rate is also in view, but not shown in report)
--* SG 11.02.2021
--* - Corrected issue with Tax missing from Freight
--* RP 10.02.2011
--* - add Terms
--* RP 03.02.2011
--* - add Freight, Tax, GrossValue
/*****************************************************************************/
AS
SELECT	'I' AS OrderType,
		RIGHT('0' + CONVERT(varchar, DAY(iv.InvoiceDate)), 2) + RIGHT('0' + CONVERT(varchar, MONTH(iv.InvoiceDate)), 2) + CONVERT(varchar, YEAR(iv.InvoiceDate)) AS InvoiceDate,
		iv.ClientNo,
		iv.InvoiceNumber,
		ISNULL(iv.CustomerCode, ' ') AS CustomerNumber,
		iv.CompanyName AS CustomerName,
		ISNULL(iv.VATNo, ' ') AS CustomerVATRegNumber,
		co.CountryName AS ShipToCountryCode,
		iv.CurrencyCode AS Currency,
		dbo.ufn_get_exchange_rate(iv.CurrencyNo, iv.InvoiceDate) AS ExchangeRate,
		ISNULL(iv.InvoiceValue, 0) AS InvoiceAmount,
		CASE iv.TaxRate WHEN 0 THEN 'N' ELSE 'Y' END AS ChargeVAT,
		' ' AS CreditNoteComments,
		iv.SalesmanName,
		iv.DivisionName,
		iv.DivisionNo AS Division,
		ROUND(iv.Freight, 2) AS Freight,
		ROUND((ISNULL((
				SELECT	SUM(CASE
							WHEN ivl.Taxable = 'Y' OR ivl.Taxable = '1'
							THEN (ivl.Price * ivl.QuantityShipped)
							ELSE 0
							END) AS Expr1
				FROM	dbo.vwInvoiceLine AS ivl (NoLock)
				WHERE	(InvoiceNo = iv.InvoiceId)
				), 0)
			+ iv.Freight) * (iv.TaxRate / 100), 2) AS Tax,
		ROUND(((ISNULL((
				SELECT	SUM(CASE
							WHEN ivl.Taxable = 'Y' OR ivl.Taxable = '1'
							THEN (ivl.Price * ivl.QuantityShipped)
							ELSE 0
							END) AS Expr1
				FROM	dbo.vwInvoiceLine AS ivl (NoLock)
				WHERE	(InvoiceNo = iv.InvoiceId)
				), 0)
			+ ROUND(iv.Freight, 2)) * (ROUND(iv.TaxRate, 2) / 100)) + ROUND(ISNULL(iv.InvoiceValue, 0), 2) + ROUND(iv.Freight, 2), 2) AS GrossValue,
		iv.TermsName AS Terms,
		iv.TaxName,
		ROUND(iv.TaxRate, 2) AS TaxRate,
		iv.InvoiceId
FROM			dbo.vwInvoice AS iv WITH (NoLock)
INNER JOIN		dbo.tbAddress AS ad (NoLock) ON iv.ShipToAddressNo = ad.AddressId
LEFT OUTER JOIN	dbo.tbCountry AS co (NoLock) ON ad.CountryNo = co.CountryId
--Espire: 04 June 20
INNER JOIN tbClient c on iv.ClientNo = c.ClientId
-- WHERE  --Espire: 04 June 20
-- ISNULL	(c.IsAutoInvoiceExport, 1) = 1
-- AND		ISNULL(iv.IsAutoInvoiceExport, 1) = 1
-- AND		(iv.ClientNo = 108)
-- AND		(iv.Exported = 0)
-- SG 01.10.2021
JOIN	dbo.tbInvoice AS i WITH (NoLock) ON i.InvoiceId = iv.InvoiceId
WHERE	(iv.ClientNo = 108)
AND		(iv.InvoiceDate >= '2021-01-01')
AND		(i.DateExported IS NULL)
AND	(	(ISNULL(c.IsAutoInvoiceExport, 0) = 0 AND iv.Exported = 1)
	OR	(ISNULL(c.IsAutoInvoiceExport, 0) = 1 AND ISNULL(iv.IsAutoInvoiceExport, 1) = 1))
GO
