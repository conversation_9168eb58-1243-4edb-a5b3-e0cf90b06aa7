﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_selectAll_RestrictedManufacturer_for_Client]    Script Date: 11/29/2024 4:13:16 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

                
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_RestrictedManufacturer_for_Client]          
@ClientId int        
AS            
SELECT rm.RestrictedManufacturerId      
  ,  rm.ManufacturerNo      
  ,  isnull((select m.ManufacturerName from tbManufacturer m where m.ManufacturerId=rm.ManufacturerNo),'All Manufacture')  ManufactureName    
  ,  rm.ClientNo            
  ,  rm.Notes            
  ,  rm.Inactive            
  ,  rm.UpdatedBy            
  ,  rm.DLUP     
  , (select count(ManufacturerName) from tbManufacturer r  where r.Inactive=0 and r.ManufacturerName=    
  (select m.ManufacturerName from tbManufacturer m where m.Inactive=0 and m.ManufacturerId=rm.ManufacturerNo )) as ManufacturerCount           
  FROM  tbRestrictedManufacturer rm            
  WHERE rm.ClientNo = @ClientId            
ORDER BY  rm.Notes   
GO


