Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.initializeBase(this,[n]);this.getSelectedGroup=null};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.prototype={get_enmContactListType:function(){return this._enmContactListType},set_enmContactListType:function(n){this._enmContactListType!==n&&(this._enmContactListType=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},initialize:function(){this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/Companies";this._strDataObject="Companies";Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.callBaseMethod(this,"initialize");this._frmAdd=$find(this._aryFormIDs[0]);this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm));this._frmAdd.addSaveComplete(Function.createDelegate(this,this.addComplete));this.showGroupCodeName();this.getSelectedGroup=$get("ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlClientName_ddl_ddl")},getFormControlID:function(n,t){return String.format("{0}_{1}",n,t)},initAfterBaseIsReady:function(){this.updateFilterVisibility();this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.getData()},dispose:function(){this.isDisposed||(this._IsGSA=null,this._IsGlobalLogin=null,this._enmContactListType=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){var n="GetData_";switch(this._enmContactListType){case $R_ENUM$CompanyListType.AllCompanies:n+="Companies";break;case $R_ENUM$CompanyListType.Customers:n+="Customers";break;case $R_ENUM$CompanyListType.Suppliers:n+="Suppliers";break;case $R_ENUM$CompanyListType.Prospects:n+="Prospects"}this._objData.set_DataAction(n);this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("CallType",this._enmContactListType);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var u,f,e="",t=0,o=this._objResult.Results.length;t<o;t++){var n=this._objResult.Results[t],i="",r="";e=n.TaskCount>0?!0:!1;switch(this._enmContactListType){case $R_ENUM$CompanyListType.AllCompanies:r=$R_FN.setCleanTextValue(n.CustomerCode);i=$R_FN.setCleanTextValue(n.SupplierCode);break;case $R_ENUM$CompanyListType.Customers:r=$R_FN.setCleanTextValue(n.CustomerCode);break;case $R_ENUM$CompanyListType.Suppliers:i=$R_FN.setCleanTextValue(n.SupplierCode)}u="";u=" <img src='../../../../images/crmicons/"+n.CRMCompleteCount+".png'>";f=[$R_FN.writeDoubleCellValueAlignRightSupplier(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.ID,n.Name+" ("+n.ID+") ",null,this._enmContactListType,n.OnStop,n.AdvisoryNotes)+"<\/span>":$RGT_nubButton_Company(n.ID,n.Name+" ("+n.ID+") ",null,this._enmContactListType,n.OnStop,n.AdvisoryNotes),r,i,n.Inactive),$R_FN.setCleanTextValue(u),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Type),$R_FN.setCleanTextValue(n.Terms)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.City),$R_FN.setCleanTextValue(n.Country)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Tel),$R_FN.setCleanTextValue(n.Email)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.SalespersonName),n.SalesTurnOver),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.LastContact),n.blnMakeYellow==!1?String.format("<a href=\"javascript:void(0);\" title='Add task' onclick=\"$find('{0}').showAddForm({1},'{2}');\">Add Task<\/a>",this._element.id,n.ID,n.Name)+"&nbsp;&nbsp;&nbsp"+String.format("<a href=\"javascript:void(0);\" title='View task' onclick=\"$find('{0}').redirectToDetails({1},'{2}');\">"+(n.TaskCount+" Task")+"<\/a>",this._element.id,n.ID,n.Name):""),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.InsuranceCertificateNo),$R_FN.setCleanTextValue(n.CertificateCategoryName)),$R_FN.setCleanTextValue(n.ClientName)];this._table.addRow(f,n.ID,!1,"",null,this.get_element().id,"showMoreInfo","hideMoreInfo",[n.ID]);f=null;n=null}},updateFilterVisibility:function(){this.getFilterField("ctlClientName").show(this._IsGlobalLogin||this._IsGSA)},showGroupCodeName:function(){this._enmContactListType!==$R_ENUM$CompanyListType.Customers?($("#ctl00_cphMain_ctlPageTitle_ctl20_hypAddGroupCode").hide(),this._enmContactListType!==$R_ENUM$CompanyListType.AllCompanies?($("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlGroupCodeName").hide(),$("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlCompSupType").hide(),$("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlVATIDs").hide()):($("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlGroupCodeName").show(),$("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlVATIDs").show(),$("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlCompSupType").show())):($("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlGroupCodeName").show(),$("#ctl00_cphMain_ctlPageTitle_ctl20_hypAddGroupCode").show(),$("#ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlVATIDs").hide())},showAddForm:function(n,t){this._frmAdd.setFormFieldsToDefaults();this._frmAdd.setFieldValue("ctlDueTime","09:00");this._frmAdd.setFieldValue("ctlReminderTime","09:00");this._frmAdd.setFieldValue("ctlCompanyNew",n,null,t);this._frmAdd.setFieldValue("ctlCompany",'<a style="color:white;" href="Con_CompanyDetail.aspx?cm='+n+'">'+$R_FN.setCleanTextValue(t)+"<\/a>");this._frmAdd._intCategoryID=1;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this._frmAdd.resetFormData();this.showForm(this._frmAdd,!1)},addComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},redirectToDetails:function(n,t){location.href="Prf_ToDo.aspx?cm="+n+"&cmn="+$R_FN.setCleanTextValue(t);"_blank"},applyFilter:function(){if(isClientChecked=$get("ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_ctlClientName_chkOn").checked,this.getSelectedGroup.options[this.getSelectedGroup.selectedIndex]!=undefined&&this.getSelectedGroup.options[this.getSelectedGroup.selectedIndex].innerText=="All"){var n=0;for(const t of["ctlName","ctlType","ctlCity","ctllstCountry","ctlTel","ctlState","ctlCounty","ctlRelatedManufac","ctlGroupCodeName","ctlCompSupType","ctlInsuranceCertificateNo","ctlCertificateCategoryNo","ctlSalesperson","ctlSupplierRating","ctlCustomerRating","ctlCustomerNo","ctlZipcode","ctlRegion","ctlEmail","ctlIndustryType","ctlVATIDs"]){const i="ctl00_cphMain_ctlCompanies_ctlDB_ctl16_ctlFilter_"+t+"_chkOn",r=$get(i).checked;if(r){n++;break}}if(n==0&&isClientChecked){alert("Please ensure that at least one other filter is selected to be able to view data from all companies in all clients");return}}this.onFilterData()}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);