Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.LyticaManufacturers=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.LyticaManufacturers.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.LyticaManufacturers.prototype={get_ShowInactive:function(){return this._ShowInactive},set_ShowInactive:function(n){this._ShowInactive!==n&&(this._ShowInactive=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.LyticaManufacturers.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("LyticaManufacturers")},dispose:function(){this.isDisposed||(this._ShowInactive=null,Rebound.GlobalTrader.Site.Controls.AutoSearch.LyticaManufacturers.callBaseMethod(this,"dispose"))},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",i=this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.Navigate?$RGT_nubButton_Manufacturer(n.ID,n.Name):$R_FN.setCleanTextValue(n.Name),this.addResultItem(i,$R_FN.setCleanTextValue(n.Name),n.ID,this._blnIsSelectionAllowed),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.LyticaManufacturers.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.LyticaManufacturers",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);