///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//
// RP 17.12.2009:
// - Clear button handlers on dispose
//
// RP 14.10.2009:
// - Retrofit bugfixes from v3.0.34: fixed not searching after invalid search (task 341)
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.Base = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.initializeBase(this, [element]);
	this._blnFirstCall = true;
	this._blnIsSearching = false;
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.prototype = {

	get_intItemSearchID: function() { return this._intItemSearchID; }, 	set_intItemSearchID: function(v) { if (this._intItemSearchID !== v)  this._intItemSearchID = v; }, 
	get_pnlLoading: function() { return this._pnlLoading; }, 	set_pnlLoading: function(v) { if (this._pnlLoading !== v)  this._pnlLoading = v; }, 
	get_pnlError: function() { return this._pnlError; }, 	set_pnlError: function(v) { if (this._pnlError !== v)  this._pnlError = v; }, 
	get_lblError: function() { return this._lblError; }, 	set_lblError: function(v) { if (this._lblError !== v)  this._lblError = v; }, 
	get_tblResults: function() { return this._tblResults; }, 	set_tblResults: function(v) { if (this._tblResults !== v)  this._tblResults = v; }, 
	get_pnlContent: function() { return this._pnlContent; }, 	set_pnlContent: function(v) { if (this._pnlContent !== v)  this._pnlContent = v; }, 
	get_pnlNoneFound: function() { return this._pnlNoneFound; }, 	set_pnlNoneFound: function(v) { if (this._pnlNoneFound !== v)  this._pnlNoneFound = v; }, 
	get_ctlPagingButtons: function() { return this._ctlPagingButtons; }, 	set_ctlPagingButtons: function(v) { if (this._ctlPagingButtons !== v)  this._ctlPagingButtons = v; }, 
	get_ibtnSearch: function() { return this._ibtnSearch; }, 	set_ibtnSearch: function(v) { if (this._ibtnSearch !== v)  this._ibtnSearch = v; }, 
	get_objFieldIDs: function() { return this._objFieldIDs; }, 	set_objFieldIDs: function(v) { if (this._objFieldIDs !== v)  this._objFieldIDs = v; }, 
	get_aryFieldIDs: function() { return this._aryFieldIDs; }, 	set_aryFieldIDs: function(v) { if (this._aryFieldIDs !== v)  this._aryFieldIDs = v; }, 
	get_enmInitialSortDirection: function() { return this._enmInitialSortDirection; }, 	set_enmInitialSortDirection: function(v) { if (this._enmInitialSortDirection !== v)  this._enmInitialSortDirection = v; }, 

	addSetupData: function(handler) { this.get_events().addHandler("SetupData", handler); },
	removeSetupData: function(handler) { this.get_events().removeHandler("SetupData", handler); },
	onSetupData: function() { 
		var handler = this.get_events().getHandler("SetupData");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	addGetData: function(handler) { this.get_events().addHandler("GetData", handler); },
	removeGetData: function(handler) { this.get_events().removeHandler("GetData", handler); },
	onGetData: function() { 
		var handler = this.get_events().getHandler("GetData");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	addGetDataComplete: function(handler) { this.get_events().addHandler("GetDataComplete", handler); },
	removeGetDataComplete: function(handler) { this.get_events().removeHandler("GetDataComplete", handler); },
	onGetDataComplete: function() { 
		var handler = this.get_events().getHandler("GetDataComplete");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	addSearched: function(handler) { this.get_events().addHandler("Searched", handler); },
	removeSearched: function(handler) { this.get_events().removeHandler("Searched", handler); },
	onSearched: function() { 
		var handler = this.get_events().getHandler("Searched");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	addItemSelected: function(handler) { this.get_events().addHandler("ItemSelected", handler); },
	removeItemSelected: function(handler) { this.get_events().removeHandler("ItemSelected", handler); },
	onItemSelected: function() { 
		var handler = this.get_events().getHandler("ItemSelected");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.callBaseMethod(this, "initialize");
		$R_IBTN.addClick(this._ibtnSearch, Function.createDelegate(this, this.searchClicked));
		if (this._ctlPagingButtons) {
			this._ctlPagingButtons.show(true);
			this._ctlPagingButtons.addPageSizeClickEvent(Function.createDelegate(this, this.pageSizeClick));
			this._ctlPagingButtons.addPageChangedEvent(Function.createDelegate(this, this.pageNumberChanged));
		}
		this._tblResults.addSortDataEvent(Function.createDelegate(this, this.getData));
		this._tblResults.addFilterDataEvent(Function.createDelegate(this, this.getData));
		this._tblResults.addSelectedIndexChanged(Function.createDelegate(this, this.tableItemClicked));
		this._tblResults.setInitialSortDirection(this._enmInitialSortDirection);
		this._tblResults._enmSortDirection = this._enmInitialSortDirection;
		this._blnFirstCall = null;
		this._blnIsSearching = null;
		this.setFilterFieldEnterPressedEvents();
		this.getDropDownsData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._ibtnSearch) $R_IBTN.clearHandlers(this._ibtnSearch);
		if (this._tblResults) this._tblResults.dispose();
		if (this._ctlPagingButtons) this._ctlPagingButtons.dispose();
		this._pnlLoading = null;
		this._pnlError = null;
		this._lblError = null;
		this._tblResults = null;
		this._pnlContent = null;
		this._pnlNoneFound = null;
		this._ctlPagingButtons = null;
		this._ibtnSearch = null;
		this._objFieldIDs = null;
		this._aryFieldIDs = null;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	searchClicked: function(intInitialID) {
		if (this._blnIsSearching) return;
		if (!intInitialID) intInitialID = null;
		this._intInitialID = intInitialID;
		this.resetToFirstPage();
		this.getData();
	},
	
	getField: function(strField) {
		var strControlID = eval("this._objFieldIDs." + strField);
		if (!strControlID) eval(String.format("FormFieldNotFound_{0}()", strField));
		return $find(strControlID);
	},
	
	setFieldValue: function(strField, varValue) {
		if (typeof(varValue) == "undefined") return;
		var fld = this.getField(strField);
		if (fld) fld.setValue(varValue);
		fld = null;
	},
	
	getFieldValue: function(strField) {
		var fld = this.getField(strField);
		if (!fld) return;
		if (!fld._blnOn) return;
		return fld.getValue();
	},
	
	getFieldValue_Min: function(strField) {
		var fld = this.getField(strField);
		if (!fld) return;
		if (!fld._blnOn) return;
		return fld.getMinValue();
	},
	
	getFieldValue_Max: function(strField) {
		var fld = this.getField(strField);
		if (!fld) return;
		if (!fld._blnOn) return;
		return fld.getMaxValue();
	},
	
	showError: function(bln, strMsg) {
		$R_FN.showElement(this._pnlError, bln);
		if (bln) {
			if (strMsg == "") strMsg = $R_RES.DatabaseError;
			$R_FN.setInnerHTML(this._lblError, strMsg);
			$R_FN.showElement(this._pnlLoading, false);
			$R_FN.showElement(this._pnlContent, false);
			$R_FN.showElement(this._pnlNoneFound, false);
		}
	},
	
	showLoading: function(bln, blnHideContent) {
		$R_FN.showElement(this._pnlLoading, bln);
		if (bln) {
			$R_FN.showElement(this._pnlError, false);
			if (blnHideContent) {
				$R_FN.showElement(this._pnlContent, false);
			} else {
				Sys.UI.DomElement.addCssClass(this._pnlContent, 'itemSearchTableLoading');
			}
			$R_FN.showElement(this._pnlNoneFound, false);
		}
	},

	showResults: function(bln) {
		$R_FN.showElement(this._pnlContent, true);
		Sys.UI.DomElement.removeCssClass(this._pnlContent, 'itemSearchTableLoading');
		if (bln) {
			$R_FN.showElement(this._pnlError, false);
			$R_FN.showElement(this._pnlLoading, false);
			$R_FN.showElement(this._pnlNoneFound, false);
		}
	},

	showNoneFound: function(bln) {
		$R_FN.showElement(this._pnlNoneFound, true);
		if (bln) {
			$R_FN.showElement(this._pnlContent, false);
			$R_FN.showElement(this._pnlError, false);
			$R_FN.showElement(this._pnlLoading, false);
		}
	},
	
	getData: function() {
		if (!this.checkSomeFieldsEntered()) return;
		this.showLoading(true, this._blnFirstCall);
		this._objData = new Rebound.GlobalTrader.Site.Data();
		this.onSetupData();
		this.setIsSearching(true);
		this._objData.addParameter("ISID", this._intItemSearchID);
		this._objData.addParameter("Order", this._tblResults._intSortColumnIndex + 1);
		this._objData.addParameter("SortDir", this._tblResults._enmSortDirection);
		this._objData.addParameter("PageIndex", this._tblResults._intCurrentPage - 1);
		this._objData.addParameter("PageSize", this._tblResults._intCurrentPageSize);
		this._objData.addDataOK(Function.createDelegate(this, this.getDataComplete));
		this._objData.addError(Function.createDelegate(this, this.getDataError));
		this._objData.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(this._objData);
		$R_DQ.processQueue();
		this._objData = null;
		this.onGetData();
		this._blnFirstCall = false;
	},
	
	getDataComplete: function(args) {
		this.setIsSearching(false);
		this._objResult = args._result;
		if (this._objResult.Results.length > 0) {
			this._tblResults.clearTable();
			this._tblResults._intTotalRecords = this._objResult.Count;
			this._tblResults.calculatePages();
			this._tblResults.calculateStartAndEndRow();
			this.updatePaging();
			this.onGetDataComplete();
			this.showResults(true);
			this._tblResults.resizeColumns();
		} else {
			this.showNoneFound(true);
		}
		this.onSearched();
	},
	
	getDataError: function(args) {
		this.setIsSearching(false);
		this.showError(true, args.get_ErrorMessage());
	},
	
	checkSomeFieldsEntered: function() {
		for (var i = 0, l = this._aryFieldIDs.length; i < l; i++) {
			var fld = $find(this._aryFieldIDs[i]);
			if (fld._blnOn) { fld = null; return true; }
			fld = null;
		}
		return false;
	},
	
	pageSizeClick: function() {
		if (this._blnIsSearching) return;
		if (!this._ctlPagingButtons) return;
		if (this._ctlPagingButtons._intCurrentPageSize == this._tblResults._intCurrentPageSize) return;
		this._tblResults._intCurrentPage = 1;
		this._ctlPagingButtons._intCurrentPage = 1;
		this._tblResults._intCurrentPageSize = this._ctlPagingButtons._intCurrentPageSize;
		this.getData();
	},
	
	pageNumberChanged: function() {
		if (this._blnIsSearching) return;
		if (!this._ctlPagingButtons) return;
		if (this._ctlPagingButtons._intCurrentPage == this._tblResults._intCurrentPage) return;
		this._tblResults.changePage(this._ctlPagingButtons._intCurrentPage);
		this.getData();
	},

	updatePaging: function() {
		if (!this._ctlPagingButtons) return;
		this._ctlPagingButtons._intTotalResults = this._tblResults._intTotalRecords;
		this._ctlPagingButtons._intTotalPages = this._tblResults._intTotalPages;
		this._ctlPagingButtons._intCurrentPage = this._tblResults._intCurrentPage;
		this._ctlPagingButtons.updatePageDisplay();
	},
	
	tableItemClicked: function() {
		if (this._blnIsSearching) return;
		this.onItemSelected();
	},
	
	getSelectedID: function() {
		return this._tblResults._varSelectedValue;
	},
	
	resetToFirstPage: function() {
		if (this._ctlPagingButtons) this._ctlPagingButtons._intCurrentPage = 1;
		this._tblResults.changePage(1);
	},
	
	resizeColumns: function() {
		if (this._tblResults) this._tblResults.resizeColumns();
	},

	getDropDownsData: function() {
		if (this._intCheckDropDownTimer != 0) clearTimeout(this._intCheckDropDownTimer);
		for (var i = 0, l = this._aryFieldIDs.length; i < l; i++) {
			var fld = $find(this._aryFieldIDs[i]);
			if (fld) {
				if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown") {
					if (fld.get_isInitialized()) {
						fld.getDropDownData();
					} else {
						this._intCheckDropDownTimer = setTimeout(Function.createDelegate(this, this.getDropDownsData), 5);
					}
				}
			}
		}
	},

	setIsSearching: function(bln) {
		this._blnIsSearching = bln;
		if (this._ctlPagingButtons) this._ctlPagingButtons.enable(!bln);
		if (this._tblResults) this._tblResults.enable(!bln);
	},
	
	setFilterFieldEnterPressedEvents: function() {
		for (var i = 0, l = this._aryFieldIDs.length; i < l; i++) {
		var fld = $find(this._aryFieldIDs[i]);
			if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox" 
			|| Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical" ) {
				fld.addEnterPressed(Function.createDelegate(this, this.searchClicked));
			}
		}
	}

};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Base.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Base", Sys.UI.Control, Sys.IDisposable);
