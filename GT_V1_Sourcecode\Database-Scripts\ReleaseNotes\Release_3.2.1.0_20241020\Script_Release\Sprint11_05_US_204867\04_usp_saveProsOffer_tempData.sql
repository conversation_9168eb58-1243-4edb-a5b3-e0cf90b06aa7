﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		23-Sep-2024		Create		Insert temp data when import prospective offers
===========================================================================================  
*/   
CREATE OR ALTER Procedure [dbo].[usp_saveProsOffer_tempData]    
    @UploadedData UploadedProspectiveOffer READONLY,    
    @originalFilename VARCHAR(max),    
    @generatedFilename VARCHAR(max),    
    @UserId INT 
AS    
BEGIN    
    SET NOCOUNT ON;
	--Clear previous temp data
	DELETE BorisGlobalTraderimports.dbo.tbProspectiveOffer_tempData WHERE CreatedBy = @UserId;
	
    INSERT INTO BorisGlobalTraderimports.dbo.tbProspectiveOffer_tempData    
    (    
        [Column1],    
        [Column2],    
        [Column3],    
        [Column4],    
        [Column5],    
        [Column6],    
        [Column7],    
        [Column8],    
        [Column9],    
        [Column10],    
        [Column11],
		[LineNumber],  
        [OriginalFilename],    
        [GeneratedFilename],    
        [CreatedBy],
		[CreatedDate]
    )    
    SELECT
		[Column1],    
        [Column2],    
        [Column3],    
        [Column4],    
        [Column5],    
        [Column6],    
        [Column7],    
        [Column8],    
        [Column9],    
        [Column10],    
        [Column11],
		[LineNumber],
        @originalFilename,    
        @generatedFilename,    
        @userId,
		GETDATE()
    FROM @UploadedData    
END  