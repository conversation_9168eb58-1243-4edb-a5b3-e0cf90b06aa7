﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_Select_Search_for_RestrictedManufacture]                         
(                      
@ClientNo    int ,                     
@ManufacturerNo    int =null          
                
)                      
AS                        
BEGIN             
Declare @RestrictedMFRMessage nvarchar(400)          
declare @isRestrictedManufacturer int=0          
SET @RestrictedMFRMessage=''                  
--select count(*)as isRestrictedManufacturer from tbRestrictedManufacturer rm where rm.ManufacturerNo=@ManufacturerNo and rm.ClientNo=@ClientNo                 
select @isRestrictedManufacturer=count(*) from tbRestrictedManufacturer rm where rm.ManufacturerNo=@ManufacturerNo and rm.ClientNo=@ClientNo and rm.Inactive=0     

  IF (ISNULL(@isRestrictedManufacturer,0)=1)                  
   BEGIN                  
   select @RestrictedMFRMessage= WarningText from tbSystemWarningMessage where WarningNo=5 and ApplyToCatagoryNo=1 and ApplyTo=0  and ClientNo=@ClientNo and InActive=0                
   if exists(select WarningText from  tbSystemWarningMessage  where WarningNo=5 and ApplyToCatagoryNo=1 and ApplyTo=@ManufacturerNo  and ClientNo=@ClientNo and InActive=0)                
   begin                
    select @RestrictedMFRMessage= WarningText from  tbSystemWarningMessage  where WarningNo=5 and ApplyToCatagoryNo=1 and ApplyTo=@ManufacturerNo  and ClientNo=@ClientNo and InActive=0               
 select @isRestrictedManufacturer as isRestrictedManufacturer,@RestrictedMFRMessage as RestrictedMFRMessage       
   end           
   else          
   begin          
   select @isRestrictedManufacturer as isRestrictedManufacturer,@RestrictedMFRMessage as RestrictedMFRMessage       
   end               
   END           
   else           
   begin          
   select @isRestrictedManufacturer as isRestrictedManufacturer,@RestrictedMFRMessage as RestrictedMFRMessage       
   end          
END   

GO


