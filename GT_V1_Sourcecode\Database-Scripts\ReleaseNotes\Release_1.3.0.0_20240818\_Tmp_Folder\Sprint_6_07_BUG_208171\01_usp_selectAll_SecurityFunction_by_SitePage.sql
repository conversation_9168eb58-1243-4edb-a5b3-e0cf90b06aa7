  
  
Alter PROCEDURE [dbo].[usp_selectAll_SecurityFunction_by_SitePage]   
--*******************************************************************************************  
--* RP 15.08.09:  
--* - added ordering by new field DisplaySortOrder  
--*******************************************************************************************  

/*
[BUG-208171]	Cuong Do 13-8-24	Update		hide 20010016 Allow adding Stock Import Tool
*/
    @SitePageNo int  
AS   
    SELECT  sf.SecurityFunctionId  
          , sf.FunctionName  
          , sf.SitePageNo  
    FROM    tbSecurityFunction sf  
    JOIN    tbSitePage sp ON sp.SitePageId = sf.SitePageNo  
    WHERE   sf.SitePageNo = @SitePageNo  
            AND NOT sf.SitePageNo IS NULL  
  --and sf.SitePageNo in (3000102,3000502,3000802)  
            AND sf.ReportNo IS NULL  
			AND sf.SecurityFunctionId <> 20010016
    ORDER BY sf.DisplaySortOrder  
  
  
  