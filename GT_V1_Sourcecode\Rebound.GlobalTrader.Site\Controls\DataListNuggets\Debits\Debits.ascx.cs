using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class Debits : Base {


        #region Properties
        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }
		private bool _IsGSA = false;
		public bool IsGSA
		{
			get { return _IsGSA; }
			set { _IsGSA = value; }
		}

		#endregion
		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			IsGSA = SessionManager.IsGSA.Value;
			IsGlobalLogin = SessionManager.IsGlobalUser.Value;
			SetDataListNuggetType("Debits");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "Debits");
			AddScriptReference("Controls.DataListNuggets.Debits.Debits.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Debits", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
			_scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
			base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            if (!string.IsNullOrEmpty(strViewLevel)) {
                ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
                _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
                this.OnAskPageToChangeTab();
            }
            base.RenderAdditionalState();
        }

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", Unit.Empty, true));
			_tbl.Columns.Add(new FlexiDataColumn("DebitDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			_tbl.Columns.Add(new FlexiDataColumn("PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("SupplierInvoice", WidthManager.GetWidth(WidthManager.ColumnWidth.ExternalCompanyDocument), true));
            if (_IsGlobalLogin == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("Client", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            }
        }

	}
}