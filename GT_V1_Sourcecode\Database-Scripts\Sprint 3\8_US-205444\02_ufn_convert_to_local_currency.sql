SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF(OBJECT_ID(N'dbo.ufn_convert_to_local_currency', N'FN')) IS NOT NULL
    DROP FUNCTION dbo.ufn_convert_to_local_currency;
GO
-- =============================================
-- Author:		An.TranTan
-- Create date: 14-Jun-2024
-- Description:	Convert value to local currency of target client
-- =============================================
CREATE FUNCTION [dbo].[ufn_convert_to_local_currency] (
      @ValueToBeConverted float
    , @LocalCurrencyFromId int
    , @LocalCurrencyToId int
    , @ExchangeRateDate datetime = NULL
	, @ClientID int
    )
RETURNS float
AS BEGIN
	DECLARE @ClientLocalCurrencyId INT;
	DECLARE @ReturnValue FLOAT;

	SELECT TOP 1 @ClientLocalCurrencyId = cl.LocalCurrencyNo
	FROM tbClient cl WITH(NOLOCK)  
	JOIN tbGlobalCurrencyList gcu WITH(NOLOCK) on cl.LocalCurrencyNo = gcu.GlobalCurrencyId      
	WHERE cl.ClientId = @ClientID;

	SET @ReturnValue =	CASE
							WHEN @LocalCurrencyFromId = @LocalCurrencyToId 
								THEN @ValueToBeConverted
							WHEN @LocalCurrencyFromId = @ClientLocalCurrencyId 
								THEN @ValueToBeConverted * dbo.ufn_get_local_exchange_rate(@LocalCurrencyToId, @ExchangeRateDate)
							WHEN @LocalCurrencyToId = @ClientLocalCurrencyId
								THEN @ValueToBeConverted / dbo.ufn_get_local_exchange_rate(@LocalCurrencyFromId, @ExchangeRateDate)
							ELSE
								(@ValueToBeConverted / dbo.ufn_get_local_exchange_rate(@LocalCurrencyFromId, @ExchangeRateDate)) * dbo.ufn_get_local_exchange_rate(@LocalCurrencyToId, @ExchangeRateDate)
						END
	RETURN @ReturnValue;
END

GO


