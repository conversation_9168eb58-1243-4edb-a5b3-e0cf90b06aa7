<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Add" xml:space="preserve">
    <value>Add</value>
  </data>
  <data name="AddedFromCancelledPartialReceipts" xml:space="preserve">
    <value>{0} part(s) added from cancelled partial receipt</value>
  </data>
  <data name="AddedFromCRMA" xml:space="preserve">
    <value>Added {0} part(s) on Customer RMA {1}</value>
  </data>
  <data name="AddedFromPartialReceipt" xml:space="preserve">
    <value>Created {0} pieces from Partial Receipt on Goods In {1}</value>
  </data>
  <data name="AddFromPOLine" xml:space="preserve">
    <value>Moved {0} part(s) to client Purchase Order {1}</value>
  </data>
  <data name="AllocatedToSalesOrder" xml:space="preserve">
    <value>{0} part(s) allocated to Sales Order {1}</value>
  </data>
  <data name="AllocatedToSRMA" xml:space="preserve">
    <value>{0} part(s) allocated to Supplier RMA {1}</value>
  </data>
  <data name="AllocationRemovedFromSalesOrder" xml:space="preserve">
    <value>{0} part(s) removed from allocation to Sales Order {1}</value>
  </data>
  <data name="AllocationRemovedFromSRMA" xml:space="preserve">
    <value>{0} part(s) removed from allocation to Supplier RMA {1}</value>
  </data>
  <data name="AutoCreatedFromCancelledReceipt" xml:space="preserve">
    <value>Automatically created from cancelled receipt</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="CancelledReceipt" xml:space="preserve">
    <value>Cancelled receipt</value>
  </data>
  <data name="CancelledShipmentForDebit" xml:space="preserve">
    <value>Cancelled shipment of {0} parts for Debit Note {1}</value>
  </data>
  <data name="CancelledShipmentForInvoice" xml:space="preserve">
    <value>Cancelled shipment of {0} parts for Invoice {1}</value>
  </data>
  <data name="ChangesFromReceivePO" xml:space="preserve">
    <value>Changes made from Received Purchase Order {0}</value>
  </data>
  <data name="ChangesOnGoodsIn" xml:space="preserve">
    <value>Changes made on Goods In {0}</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="EditPOLine" xml:space="preserve">
    <value>Edit of Purchase Order {0} adding {1} part(s)</value>
  </data>
  <data name="EditPOLine_NegativeQuantity" xml:space="preserve">
    <value>Edit of Purchase Order {0} removing {1} part(s)</value>
  </data>
  <data name="EditPOLine_NoQuantityChange" xml:space="preserve">
    <value>Edit of Purchase Order {0}</value>
  </data>
  <data name="Inspected" xml:space="preserve">
    <value>Released</value>
  </data>
  <data name="LinkPO" xml:space="preserve">
    <value>Linked to Purchase Order {0}</value>
  </data>
  <data name="LinkPO_UnknownPO" xml:space="preserve">
    <value>Linked to Purchase Order</value>
  </data>
  <data name="ManuallyAdded" xml:space="preserve">
    <value>Manually added stock</value>
  </data>
  <data name="POLineDeleted" xml:space="preserve">
    <value>Purchase order line item deleted</value>
  </data>
  <data name="POLineDeleted_FromPO" xml:space="preserve">
    <value>(from Purchase Order {0})</value>
  </data>
  <data name="Quarantine" xml:space="preserve">
    <value>Quarantined</value>
  </data>
  <data name="QuarantineRemoved" xml:space="preserve">
    <value>Removed from quarantine</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Received {0} part(s)</value>
  </data>
  <data name="ReceivedOnGoodsIn" xml:space="preserve">
    <value>Received {0} part(s) on Goods In {1}</value>
  </data>
  <data name="Ship" xml:space="preserve">
    <value>Shipped</value>
  </data>
  <data name="ShippedInvoice" xml:space="preserve">
    <value>Shipped {0} part(s) on Invoice {1}</value>
  </data>
  <data name="ShippedSRMA" xml:space="preserve">
    <value>Shipped {0} part(s) on Supplier RMA {1}</value>
  </data>
  <data name="Split" xml:space="preserve">
    <value>Split</value>
  </data>
  <data name="SplitNew" xml:space="preserve">
    <value>Created by splitting {0} parts from {1}</value>
  </data>
  <data name="SplitNew_UnknownSourceStock" xml:space="preserve">
    <value>an existing stock entry</value>
  </data>
  <data name="SplitOriginal" xml:space="preserve">
    <value>Split {0} pieces from this entry to {1}</value>
  </data>
  <data name="SplitOriginal_UnknownSourceStock" xml:space="preserve">
    <value>Split {0} pieces from this entry</value>
  </data>
  <data name="UnlinkPO" xml:space="preserve">
    <value>Unlinked from Purchase Order {0}</value>
  </data>
  <data name="EditCRMALine" xml:space="preserve">
    <value>Edit of Customer RMA {0} adding {1} part(s)</value>
  </data>
  <data name="EditCRMALine_NegativeQuantity" xml:space="preserve">
    <value>Edit of Customer RMA {0} removing {1} part(s)</value>
  </data>
  <data name="EditCRMALine_NoQuantityChange" xml:space="preserve">
    <value>Edit of Customer RMA {0}</value>
  </data>
  <data name="EditCRMA" xml:space="preserve">
    <value>Edit of Customer RMA {0} adding {1} part(s)</value>
  </data>
  <data name="CRMALineDeleted" xml:space="preserve">
    <value>CRMA line item deleted</value>
  </data>
  <data name="CRMALineDeleted_FromCRMA" xml:space="preserve">
    <value>(from CRMA {0})</value>
  </data>
  <data name="StockWrittenDown" xml:space="preserve">
    <value>Stock Written Down</value>
  </data>
  <data name="PhysicalInspect" xml:space="preserve">
    <value>Physically Inspected</value>
  </data>
  <data name="ShipInCostUpdateWithBankFee" xml:space="preserve">
    <value>Shipping cost updated with PO bank fee</value>
  </data>
  <data name="GISplit" xml:space="preserve">
    <value>Split from GoodsIn {0} pieces from this entry to {1}</value>
  </data>
  <data name="ImportedFromCSV" xml:space="preserve">
    <value>Imported From CSV</value>
  </data>
  <data name="AddFromIPOLine" xml:space="preserve">
    <value>Added {0} part(s) on Internal Purchase Order {1}</value>
  </data>
  <data name="StockLandedCostUpdated" xml:space="preserve">
    <value>Stock Landed Cost Update From GI</value>
  </data>
  <data name="InspectionCompletedDetails" xml:space="preserve">
    <value>Complete Inspection</value>
  </data>
  <data name="InspectionReopenDetails" xml:space="preserve">
    <value>Reopen Inspection</value>
  </data>
  <data name="InspectionstartedDetails" xml:space="preserve">
    <value>Start Inspection</value>
  </data>
  <data name="ReleasedWhenQuarantined" xml:space="preserve">
    <value>GI Line released to Quarantine</value>
  </data>
  <data name="CMRAsStock" xml:space="preserve">
    <value>{0} part(s) has been returned by Customer RMA {1} through Stock {2}</value>
  </data>
  <data name="CRMAsInitialStock" xml:space="preserve">
    <value>{0} part(s) has been returned from Stock {1}</value>
  </data>
</root>