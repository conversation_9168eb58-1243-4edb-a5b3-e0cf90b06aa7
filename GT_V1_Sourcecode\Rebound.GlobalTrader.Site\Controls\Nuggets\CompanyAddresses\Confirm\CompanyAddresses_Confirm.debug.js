///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.initializeBase(this, [element]);
	this._intAddressID = -1;
	this._intCompanyID = -1;
	this._intCompanyAddressID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.prototype = {

	get_intAddressID: function() { return this._intAddressID; }, 	set_intAddressID: function(value) { if (this._intAddressID !== value)  this._intAddressID = value; }, 
	get_intCompanyID: function() { return this._intCompanyID; }, 	set_intCompanyID: function(value) { if (this._intCompanyID !== value)  this._intCompanyID = value; }, 
	get_strTitle_Cease: function() { return this._strTitle_Cease; }, 	set_strTitle_Cease: function(value) { if (this._strTitle_Cease !== value)  this._strTitle_Cease = value; }, 
	get_strTitle_DefaultBill: function() { return this._strTitle_DefaultBill; }, 	set_strTitle_DefaultBill: function(value) { if (this._strTitle_DefaultBill !== value)  this._strTitle_DefaultBill = value; }, 
	get_strTitle_DefaultShip: function() { return this._strTitle_DefaultShip; }, 	set_strTitle_DefaultShip: function(value) { if (this._strTitle_DefaultShip !== value)  this._strTitle_DefaultShip = value; }, 
	get_strExplanation_Cease: function() { return this._strExplanation_Cease; }, 	set_strExplanation_Cease: function(value) { if (this._strExplanation_Cease !== value)  this._strExplanation_Cease = value; }, 
	get_strExplanation_DefaultBill: function() { return this._strExplanation_DefaultBill; }, 	set_strExplanation_DefaultBill: function(value) { if (this._strExplanation_DefaultBill !== value)  this._strExplanation_DefaultBill = value; }, 
	get_strExplanation_DefaultShip: function() { return this._strExplanation_DefaultShip; }, 	set_strExplanation_DefaultShip: function(value) { if (this._strExplanation_DefaultShip !== value)  this._strExplanation_DefaultShip = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
		this.addModeChanged(Function.createDelegate(this, this.modeChanged));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._ctlConfirm = null;
		this._intAddressID = null;
		this._intCompanyID = null;
		this._intCompanyAddressID = null;
		this._strTitle_Cease = null;
		this._strTitle_DefaultBill = null;
		this._strTitle_DefaultShip = null;
		this._strExplanation_Cease = null;
		this._strExplanation_DefaultBill = null;
		this._strExplanation_DefaultShip = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
	},

	yesClicked: function() {
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/CompanyAddresses");
		obj.set_DataObject("CompanyAddresses");
		switch (this._mode) {
			case "CEASE": obj.set_DataAction("CeaseAddress"); break;
			case "DEFAULT_BILL": obj.set_DataAction("MakeDefaultBill"); break;
			case "DEFAULT_SHIP": obj.set_DataAction("MakeDefaultShip"); break;
		}
		obj.addParameter("id", this._intCompanyAddressID);
		obj.addParameter("CMNo", this._intCompanyID);
		obj.addDataOK(Function.createDelegate(this, this.saveComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},
	
	modeChanged: function() {
		switch (this._mode){
			case "CEASE": 
				this.changeTitle(this._strTitle_Cease); 
				this.changeExplanation(this._strExplanation_Cease); 
				break;
			case "DEFAULT_BILL": 
				this.changeTitle(this._strTitle_DefaultBill); 
				this.changeExplanation(this._strExplanation_DefaultBill); 
				break;
			case "DEFAULT_SHIP": 
				this.changeTitle(this._strTitle_DefaultShip); 
				this.changeExplanation(this._strExplanation_DefaultShip); 
				break;
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
