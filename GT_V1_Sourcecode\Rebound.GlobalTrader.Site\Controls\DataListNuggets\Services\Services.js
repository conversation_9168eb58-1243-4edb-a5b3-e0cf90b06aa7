Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.prototype={get_intLotID:function(){return this._intLotID},set_intLotID:function(n){this._intLotID!==n&&(this._intLotID=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/Services";this._strDataObject="Services";Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this._intLotID>0&&this.getFilterField("ctlLot").show(!1);this.getData()},dispose:function(){this.isDisposed||(this._intLotID=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.callBaseMethod(this,"dispose"))},setupDataCall:function(){},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_Service(n.ID,n.Name),$R_FN.setCleanTextValue(n.Desc),$RGT_nubButton_Lot(n.LotNo,n.Lot),n.Cost,n.Price],this._table.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);