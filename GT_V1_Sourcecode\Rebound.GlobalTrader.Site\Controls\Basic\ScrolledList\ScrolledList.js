Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.ScrolledList=function(n){Rebound.GlobalTrader.Site.Controls.ScrolledList.initializeBase(this,[n]);this._intNumberOfItems=0;this._intCurrentColumn=1};Rebound.GlobalTrader.Site.Controls.ScrolledList.prototype={get_intNumberOfColumns:function(){return this._intNumberOfColumns},set_intNumberOfColumns:function(n){this._intNumberOfColumns!==n&&(this._intNumberOfColumns=n)},get_td1:function(){return this._td1},set_td1:function(n){this._td1!==n&&(this._td1=n)},get_td2:function(){return this._td2},set_td2:function(n){this._td2!==n&&(this._td2=n)},get_td3:function(){return this._td3},set_td3:function(n){this._td3!==n&&(this._td3=n)},get_td4:function(){return this._td4},set_td4:function(n){this._td4!==n&&(this._td4=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ScrolledList.callBaseMethod(this,"initialize");this.clear()},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._intNumberOfItems=null,this._intCurrentColumn=null,this._td1=null,this._td2=null,this._td3=null,this._td4=null,Rebound.GlobalTrader.Site.Controls.ScrolledList.callBaseMethod(this,"dispose"),this.isDisposed=!0)},addItem:function(n){this._intCurrentColumn>this._intNumberOfColumns&&(this._intCurrentColumn=1);var t=eval("this._td"+this._intCurrentColumn);t&&(n=String.format('<div class="dataItem">{0}<\/div>',n),t.innerHTML!="&nbsp;"&&(n=String.format("{0}{1}",t.innerHTML,n)),$R_FN.setInnerHTML(t,n),this._intNumberOfItems+=1,this._intCurrentColumn+=1,t=null)},clear:function(){this._td1&&$R_FN.setInnerHTML(this._td1,"&nbsp;");this._td2&&$R_FN.setInnerHTML(this._td2,"&nbsp;");this._td3&&$R_FN.setInnerHTML(this._td3,"&nbsp;");this._td4&&$R_FN.setInnerHTML(this._td4,"&nbsp;");this._intNumberOfItems=0;this._intCurrentColumn=1}};Rebound.GlobalTrader.Site.Controls.ScrolledList.registerClass("Rebound.GlobalTrader.Site.Controls.ScrolledList",Sys.UI.Control,Sys.IDisposable);