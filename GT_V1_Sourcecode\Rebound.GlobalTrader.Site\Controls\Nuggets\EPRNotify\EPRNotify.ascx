<%@ Control Language="C#" CodeBehind="EPRNotify.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.EPRNotify" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<Content>
		<asp:PlaceHolder ID="plhAddNotAllowed" runat="server" Visible="false">
			<div class="addNotAllowed"><%=Functions.GetGlobalResource("Messages", "AddNotAllowed_EPRNotify")%></div>
		</asp:PlaceHolder>
	</Content>
	<Forms>
		<ReboundForm:EPRNotify_Notify ID="ctlNotify" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
