Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.initializeBase(this,[n]);this._intContactID=-1;this._strContactName=""};Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.prototype={get_strExplain_PO:function(){return this._strExplain_PO},set_strExplain_PO:function(n){this._strExplain_PO!==n&&(this._strExplain_PO=n)},get_strTitle_PO:function(){return this._strTitle_PO},set_strTitle_PO:function(n){this._strTitle_PO!==n&&(this._strTitle_PO=n)},get_strExplain_SO:function(){return this._strExplain_SO},set_strExplain_SO:function(n){this._strExplain_SO!==n&&(this._strExplain_SO=n)},get_strTitle_SO:function(){return this._strTitle_SO},set_strTitle_SO:function(n){this._strTitle_SO!==n&&(this._strTitle_SO=n)},get_strExplain_POLedger:function(){return this._strExplain_POLedger},set_strExplain_POLedger:function(n){this._strExplain_POLedger!==n&&(this._strExplain_POLedger=n)},get_strTitle_POLedger:function(){return this._strTitle_POLedger},set_strTitle_POLedger:function(n){this._strTitle_POLedger!==n&&(this._strTitle_POLedger=n)},get_strExplain_SOLedger:function(){return this._strExplain_SOLedger},set_strExplain_SOLedger:function(n){this._strExplain_SOLedger!==n&&(this._strExplain_SOLedger=n)},get_strTitle_SOLedger:function(){return this._strTitle_SOLedger},set_strTitle_SOLedger:function(n){this._strTitle_SOLedger!==n&&(this._strTitle_SOLedger=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addModeChanged(Function.createDelegate(this,this.modeChanged))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intContactID=null,this._strContactName=null,this._strExplain_PO=null,this._strTitle_PO=null,this._strExplain_SO=null,this._strTitle_SO=null,this._strExplain_POLedger=null,this._strTitle_POLedger=null,this._strExplain_SOLedger=null,this._strTitle_SOLedger=null,Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},modeChanged:function(){this._mode=="SO"?(this.changeTitle(this._strTitle_SO),this.changeExplanation(this._strExplain_SO)):this._mode=="PO"?(this.changeTitle(this._strTitle_PO),this.changeExplanation(this._strExplain_PO)):this._mode=="POLedger"?(this.changeTitle(this._strTitle_POLedger),this.changeExplanation(this._strExplain_POLedger)):(this.changeTitle(this._strTitle_SOLedger),this.changeExplanation(this._strExplain_SOLedger))},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ContactsForCompany");n.set_DataObject("ContactsForCompany");this._mode=="SO"?n.set_DataAction("MakeDefaultSO"):this._mode=="PO"?n.set_DataAction("MakeDefaultPO"):this._mode=="POLedger"?n.set_DataAction("MakeDefaultPOLedger"):n.set_DataAction("MakeDefaultSOLedger");n.addParameter("ID",this._intCompanyID);n.addParameter("ContactID",this._intContactID);n.addDataOK(Function.createDelegate(this,this.saveDeleteComplete));n.addError(Function.createDelegate(this,this.saveDeleteError));n.addTimeout(Function.createDelegate(this,this.saveDeleteError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.onNotConfirmed()},saveDeleteError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveDeleteComplete:function(n){this.showSaving(!1);n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);