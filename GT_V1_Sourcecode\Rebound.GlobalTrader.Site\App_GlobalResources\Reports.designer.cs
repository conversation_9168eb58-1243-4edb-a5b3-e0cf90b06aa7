//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Reports {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Reports() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Reports", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parameter Details.
        /// </summary>
        internal static string Action {
            get {
                return ResourceManager.GetString("Action", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active Vendors.
        /// </summary>
        internal static string ActiveVendors {
            get {
                return ResourceManager.GetString("ActiveVendors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adjsutment.
        /// </summary>
        internal static string Adjsutment {
            get {
                return ResourceManager.GetString("Adjsutment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Air Way Bill.
        /// </summary>
        internal static string AirWayBill {
            get {
                return ResourceManager.GetString("AirWayBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated Cost.
        /// </summary>
        internal static string AllocatedCost {
            get {
                return ResourceManager.GetString("AllocatedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocated Resale.
        /// </summary>
        internal static string AllocatedResale {
            get {
                return ResourceManager.GetString("AllocatedResale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved.
        /// </summary>
        internal static string Approved {
            get {
                return ResourceManager.GetString("Approved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Customers On Stop.
        /// </summary>
        internal static string ApprovedCustomersOnStop {
            get {
                return ResourceManager.GetString("ApprovedCustomersOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorised By.
        /// </summary>
        internal static string AuthorisedBy {
            get {
                return ResourceManager.GetString("AuthorisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto-Entered Suppliers (unedited).
        /// </summary>
        internal static string AutoEnteredSuppliers_Unedited {
            get {
                return ResourceManager.GetString("AutoEnteredSuppliers_Unedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Average.
        /// </summary>
        internal static string Average {
            get {
                return ResourceManager.GetString("Average", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avg. Price (50th Percentile).
        /// </summary>
        internal static string AveragePriceLytica {
            get {
                return ResourceManager.GetString("AveragePriceLytica", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Avoidable.
        /// </summary>
        internal static string Avoidable {
            get {
                return ResourceManager.GetString("Avoidable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back Order Quantity.
        /// </summary>
        internal static string BackOrderQuantity {
            get {
                return ResourceManager.GetString("BackOrderQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back Order Value.
        /// </summary>
        internal static string BackOrderValue {
            get {
                return ResourceManager.GetString("BackOrderValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Balance.
        /// </summary>
        internal static string Balance {
            get {
                return ResourceManager.GetString("Balance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bank Fee.
        /// </summary>
        internal static string BankFee {
            get {
                return ResourceManager.GetString("BankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bank Fee In Base.
        /// </summary>
        internal static string BankFeeInBase {
            get {
                return ResourceManager.GetString("BankFeeInBase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Base Value.
        /// </summary>
        internal static string BaseValue {
            get {
                return ResourceManager.GetString("BaseValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Number.
        /// </summary>
        internal static string BOMNumber {
            get {
                return ResourceManager.GetString("BOMNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Booked Sales.
        /// </summary>
        internal static string BookedSales {
            get {
                return ResourceManager.GetString("BookedSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Email Invoice Status.
        /// </summary>
        internal static string BulkEmailInvoiceStatus {
            get {
                return ResourceManager.GetString("BulkEmailInvoiceStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buyer.
        /// </summary>
        internal static string Buyer {
            get {
                return ResourceManager.GetString("Buyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy In Cost.
        /// </summary>
        internal static string BuyInCost {
            get {
                return ResourceManager.GetString("BuyInCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy In Price.
        /// </summary>
        internal static string BuyInPrice {
            get {
                return ResourceManager.GetString("BuyInPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy In Value.
        /// </summary>
        internal static string BuyInValue {
            get {
                return ResourceManager.GetString("BuyInValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked.
        /// </summary>
        internal static string Checked {
            get {
                return ResourceManager.GetString("Checked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to City.
        /// </summary>
        internal static string City {
            get {
                return ResourceManager.GetString("City", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed Requirements.
        /// </summary>
        internal static string ClosedRequirements {
            get {
                return ResourceManager.GetString("ClosedRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed Requirements Reasons.
        /// </summary>
        internal static string ClosedRequirementsReasons {
            get {
                return ResourceManager.GetString("ClosedRequirementsReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        internal static string Comments {
            get {
                return ResourceManager.GetString("Comments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Communication Log Activity for a User.
        /// </summary>
        internal static string CommunicationLogActivityforaUser {
            get {
                return ResourceManager.GetString("CommunicationLogActivityforaUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Companies Approved To Purchase From.
        /// </summary>
        internal static string CompaniesApprovedToPurchaseFrom {
            get {
                return ResourceManager.GetString("CompaniesApprovedToPurchaseFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Companies Not Contacted.
        /// </summary>
        internal static string CompaniesNotContacted {
            get {
                return ResourceManager.GetString("CompaniesNotContacted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Grouping?.
        /// </summary>
        internal static string CompanyGrouping {
            get {
                return ResourceManager.GetString("CompanyGrouping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Name.
        /// </summary>
        internal static string CompanyName {
            get {
                return ResourceManager.GetString("CompanyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company No.
        /// </summary>
        internal static string CompanyNo {
            get {
                return ResourceManager.GetString("CompanyNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Tax.
        /// </summary>
        internal static string CompanyTax {
            get {
                return ResourceManager.GetString("CompanyTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Type.
        /// </summary>
        internal static string CompanyType {
            get {
                return ResourceManager.GetString("CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed.
        /// </summary>
        internal static string Confirmed {
            get {
                return ResourceManager.GetString("Confirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Email.
        /// </summary>
        internal static string ContactEmail {
            get {
                return ResourceManager.GetString("ContactEmail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Email List.
        /// </summary>
        internal static string ContactEmailList {
            get {
                return ResourceManager.GetString("ContactEmailList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Fax.
        /// </summary>
        internal static string ContactFax {
            get {
                return ResourceManager.GetString("ContactFax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Name.
        /// </summary>
        internal static string ContactName {
            get {
                return ResourceManager.GetString("ContactName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Problems.
        /// </summary>
        internal static string ContactProblems {
            get {
                return ResourceManager.GetString("ContactProblems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts Not Contacted.
        /// </summary>
        internal static string ContactsNotContacted {
            get {
                return ResourceManager.GetString("ContactsNotContacted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Telephone.
        /// </summary>
        internal static string ContactTelephone {
            get {
                return ResourceManager.GetString("ContactTelephone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Type.
        /// </summary>
        internal static string ContactType {
            get {
                return ResourceManager.GetString("ContactType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost.
        /// </summary>
        internal static string Cost {
            get {
                return ResourceManager.GetString("Cost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cost In Base.
        /// </summary>
        internal static string CostInBase {
            get {
                return ResourceManager.GetString("CostInBase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Count.
        /// </summary>
        internal static string Count {
            get {
                return ResourceManager.GetString("Count", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country.
        /// </summary>
        internal static string Country {
            get {
                return ResourceManager.GetString("Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Of Import .
        /// </summary>
        internal static string CountryOfImport {
            get {
                return ResourceManager.GetString("CountryOfImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Country Of Manufacture.
        /// </summary>
        internal static string CountryOfManufacture {
            get {
                return ResourceManager.GetString("CountryOfManufacture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to County.
        /// </summary>
        internal static string County {
            get {
                return ResourceManager.GetString("County", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to By.
        /// </summary>
        internal static string CreatedBy {
            get {
                return ResourceManager.GetString("CreatedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Date.
        /// </summary>
        internal static string CreditDate {
            get {
                return ResourceManager.GetString("CreditDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Limit.
        /// </summary>
        internal static string CreditLimit {
            get {
                return ResourceManager.GetString("CreditLimit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes.
        /// </summary>
        internal static string CreditNotes {
            get {
                return ResourceManager.GetString("CreditNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes for a Customer.
        /// </summary>
        internal static string CreditNotesforaCustomer {
            get {
                return ResourceManager.GetString("CreditNotesforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes for a Salesperson.
        /// </summary>
        internal static string CreditNotesforaSalesperson {
            get {
                return ResourceManager.GetString("CreditNotesforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Number.
        /// </summary>
        internal static string CreditNumber {
            get {
                return ResourceManager.GetString("CreditNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Notes.
        /// </summary>
        internal static string CRInstructionsNotes {
            get {
                return ResourceManager.GetString("CRInstructionsNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency.
        /// </summary>
        internal static string Currency {
            get {
                return ResourceManager.GetString("Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency.
        /// </summary>
        internal static string CurrencyCode {
            get {
                return ResourceManager.GetString("CurrencyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency Description.
        /// </summary>
        internal static string CurrencyDescription {
            get {
                return ResourceManager.GetString("CurrencyDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency No.
        /// </summary>
        internal static string CurrencyNo {
            get {
                return ResourceManager.GetString("CurrencyNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency Problems.
        /// </summary>
        internal static string CurrencyProblems {
            get {
                return ResourceManager.GetString("CurrencyProblems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency Rate.
        /// </summary>
        internal static string CurrencyRate {
            get {
                return ResourceManager.GetString("CurrencyRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Currency Value.
        /// </summary>
        internal static string CurrencyValue {
            get {
                return ResourceManager.GetString("CurrencyValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Landed Cost.
        /// </summary>
        internal static string CurrentLandedCost {
            get {
                return ResourceManager.GetString("CurrentLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        internal static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Code.
        /// </summary>
        internal static string CustomerCode {
            get {
                return ResourceManager.GetString("CustomerCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer List for Salesperson.
        /// </summary>
        internal static string CustomerListforSalesperson {
            get {
                return ResourceManager.GetString("CustomerListforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer No.
        /// </summary>
        internal static string CustomerNo {
            get {
                return ResourceManager.GetString("CustomerNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer On Time Delivery Report.
        /// </summary>
        internal static string CustomerOnTimeDeliveryReport {
            get {
                return ResourceManager.GetString("CustomerOnTimeDeliveryReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Part.
        /// </summary>
        internal static string CustomerPart {
            get {
                return ResourceManager.GetString("CustomerPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer PO.
        /// </summary>
        internal static string CustomerPO {
            get {
                return ResourceManager.GetString("CustomerPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirement Number.
        /// </summary>
        internal static string CustomerRequirementNumber {
            get {
                return ResourceManager.GetString("CustomerRequirementNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Return Value.
        /// </summary>
        internal static string CustomerReturnValue {
            get {
                return ResourceManager.GetString("CustomerReturnValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CustomerRMA {
            get {
                return ResourceManager.GetString("CustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA Date.
        /// </summary>
        internal static string CustomerRMADate {
            get {
                return ResourceManager.GetString("CustomerRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA Number.
        /// </summary>
        internal static string CustomerRMANumber {
            get {
                return ResourceManager.GetString("CustomerRMANumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMAs.
        /// </summary>
        internal static string CustomerRMAs {
            get {
                return ResourceManager.GetString("CustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Statement.
        /// </summary>
        internal static string CustomerStatement {
            get {
                return ResourceManager.GetString("CustomerStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cut Off Date.
        /// </summary>
        internal static string CutOffDate {
            get {
                return ResourceManager.GetString("CutOffDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Customer Requirements by Salesperson (Detailed).
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Detailed {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Detailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Customer Requirements by Salesperson (Summary).
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Summary {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Customer Requirements by Salesperson (Totals).
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Totals {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Totals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Imports.
        /// </summary>
        internal static string DailyImports {
            get {
                return ResourceManager.GetString("DailyImports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Imports By Source.
        /// </summary>
        internal static string DailyImportsBySource {
            get {
                return ResourceManager.GetString("DailyImportsBySource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Report Log.
        /// </summary>
        internal static string DailyReportLog {
            get {
                return ResourceManager.GetString("DailyReportLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Database Management.
        /// </summary>
        internal static string DatabaseManagement {
            get {
                return ResourceManager.GetString("DatabaseManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} by {1}.
        /// </summary>
        internal static string DateAndLogin {
            get {
                return ResourceManager.GetString("DateAndLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Code.
        /// </summary>
        internal static string DateCode {
            get {
                return ResourceManager.GetString("DateCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Line Confirmed Date.
        /// </summary>
        internal static string DateConfirmed {
            get {
                return ResourceManager.GetString("DateConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Ordered.
        /// </summary>
        internal static string DateOrdered {
            get {
                return ResourceManager.GetString("DateOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Promised.
        /// </summary>
        internal static string DatePromised {
            get {
                return ResourceManager.GetString("DatePromised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Received.
        /// </summary>
        internal static string DateReceived {
            get {
                return ResourceManager.GetString("DateReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date Required.
        /// </summary>
        internal static string DateRequired {
            get {
                return ResourceManager.GetString("DateRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days Since Invoice.
        /// </summary>
        internal static string DaysSinceInvoice {
            get {
                return ResourceManager.GetString("DaysSinceInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days Since Last Contact.
        /// </summary>
        internal static string DaysSinceLastContact {
            get {
                return ResourceManager.GetString("DaysSinceLastContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days Since Last Invoice by Contact.
        /// </summary>
        internal static string DaysSinceLastInvoicebyContact {
            get {
                return ResourceManager.GetString("DaysSinceLastInvoicebyContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days Since Last Invoice by Customer.
        /// </summary>
        internal static string DaysSinceLastInvoicebyCustomer {
            get {
                return ResourceManager.GetString("DaysSinceLastInvoicebyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delivery Date.
        /// </summary>
        internal static string DeliveryDate {
            get {
                return ResourceManager.GetString("DeliveryDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Destination.
        /// </summary>
        internal static string Destination {
            get {
                return ResourceManager.GetString("Destination", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division.
        /// </summary>
        internal static string Division {
            get {
                return ResourceManager.GetString("Division", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Division Name.
        /// </summary>
        internal static string DivisionName {
            get {
                return ResourceManager.GetString("DivisionName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Document.
        /// </summary>
        internal static string Document {
            get {
                return ResourceManager.GetString("Document", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Due Date.
        /// </summary>
        internal static string DueDate {
            get {
                return ResourceManager.GetString("DueDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duration.
        /// </summary>
        internal static string Duration {
            get {
                return ResourceManager.GetString("Duration", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Code.
        /// </summary>
        internal static string DutyCode {
            get {
                return ResourceManager.GetString("DutyCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Duty Rate.
        /// </summary>
        internal static string DutyRate {
            get {
                return ResourceManager.GetString("DutyRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN.
        /// </summary>
        internal static string ECCN {
            get {
                return ResourceManager.GetString("ECCN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EMail.
        /// </summary>
        internal static string EMail {
            get {
                return ResourceManager.GetString("EMail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        internal static string emailStatus {
            get {
                return ResourceManager.GetString("emailStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee.
        /// </summary>
        internal static string Employee {
            get {
                return ResourceManager.GetString("Employee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Employee Name.
        /// </summary>
        internal static string EmployeeName {
            get {
                return ResourceManager.GetString("EmployeeName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Date.
        /// </summary>
        internal static string EndDate {
            get {
                return ResourceManager.GetString("EndDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Time.
        /// </summary>
        internal static string EndTime {
            get {
                return ResourceManager.GetString("EndTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exchange Rate.
        /// </summary>
        internal static string ExchangeRate {
            get {
                return ResourceManager.GetString("ExchangeRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exclude Customers On Stop?.
        /// </summary>
        internal static string ExcludeCustomersOnStop {
            get {
                return ResourceManager.GetString("ExcludeCustomersOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite Date.
        /// </summary>
        internal static string ExpediteDate {
            get {
                return ResourceManager.GetString("ExpediteDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expedite Notes.
        /// </summary>
        internal static string ExpediteNotes {
            get {
                return ResourceManager.GetString("ExpediteNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Factory Sealed.
        /// </summary>
        internal static string FactorySealed {
            get {
                return ResourceManager.GetString("FactorySealed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed Only.
        /// </summary>
        internal static string FailedOnly {
            get {
                return ResourceManager.GetString("FailedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to First Name.
        /// </summary>
        internal static string FirstName {
            get {
                return ResourceManager.GetString("FirstName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Freight.
        /// </summary>
        internal static string Freight {
            get {
                return ResourceManager.GetString("Freight", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General.
        /// </summary>
        internal static string General {
            get {
                return ResourceManager.GetString("General", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Number.
        /// </summary>
        internal static string GINumber {
            get {
                return ResourceManager.GetString("GINumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Received Date.
        /// </summary>
        internal static string GIReceivedDate {
            get {
                return ResourceManager.GetString("GIReceivedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods Received.
        /// </summary>
        internal static string GoodsReceived {
            get {
                return ResourceManager.GetString("GoodsReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods Received Not Invoiced.
        /// </summary>
        internal static string GoodsReceivedNotInvoiced {
            get {
                return ResourceManager.GetString("GoodsReceivedNotInvoiced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods Received Shipment Details.
        /// </summary>
        internal static string GoodsReceivedShipmentDetails {
            get {
                return ResourceManager.GetString("GoodsReceivedShipmentDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods Valuation.
        /// </summary>
        internal static string GoodsValuation {
            get {
                return ResourceManager.GetString("GoodsValuation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gross Profit Range.
        /// </summary>
        internal static string GPRange {
            get {
                return ResourceManager.GetString("GPRange", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gross Profit.
        /// </summary>
        internal static string GrossProfit {
            get {
                return ResourceManager.GetString("GrossProfit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gross Profit Breakdown.
        /// </summary>
        internal static string GrossProfitBreakdown {
            get {
                return ResourceManager.GetString("GrossProfitBreakdown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gross Profit %.
        /// </summary>
        internal static string GrossProfitPercentage {
            get {
                return ResourceManager.GetString("GrossProfitPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Harmonised Code.
        /// </summary>
        internal static string HarmonisedCode {
            get {
                return ResourceManager.GetString("HarmonisedCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB Buyer.
        /// </summary>
        internal static string HUBBuyer {
            get {
                return ResourceManager.GetString("HUBBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUB PO Number.
        /// </summary>
        internal static string HUBPONumber {
            get {
                return ResourceManager.GetString("HUBPONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Date.
        /// </summary>
        internal static string ImportDate {
            get {
                return ResourceManager.GetString("ImportDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Name.
        /// </summary>
        internal static string ImportName {
            get {
                return ResourceManager.GetString("ImportName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Advance.
        /// </summary>
        internal static string InAdvance {
            get {
                return ResourceManager.GetString("InAdvance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Credits?.
        /// </summary>
        internal static string IncludeCredits {
            get {
                return ResourceManager.GetString("IncludeCredits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Lots On Hold?.
        /// </summary>
        internal static string IncludeLotsOnHold {
            get {
                return ResourceManager.GetString("IncludeLotsOnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Never?.
        /// </summary>
        internal static string IncludeNever {
            get {
                return ResourceManager.GetString("IncludeNever", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include On Order?.
        /// </summary>
        internal static string IncludeOnOrder {
            get {
                return ResourceManager.GetString("IncludeOnOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Shipping?.
        /// </summary>
        internal static string IncludeShipping {
            get {
                return ResourceManager.GetString("IncludeShipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Unconfirmed?.
        /// </summary>
        internal static string IncludeUnconfirmed {
            get {
                return ResourceManager.GetString("IncludeUnconfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Unpaid?.
        /// </summary>
        internal static string IncludeUnpaid {
            get {
                return ResourceManager.GetString("IncludeUnpaid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incoterms.
        /// </summary>
        internal static string Incoterms {
            get {
                return ResourceManager.GetString("Incoterms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to In Stock.
        /// </summary>
        internal static string InStock {
            get {
                return ResourceManager.GetString("InStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrastat Report for EEC Arrivals (Customer RMAs).
        /// </summary>
        internal static string IntrastatReportforEECArrivals_CustomerRMAs {
            get {
                return ResourceManager.GetString("IntrastatReportforEECArrivals_CustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrastat Report for EEC Arrivals (Purchases).
        /// </summary>
        internal static string IntrastatReportforEECArrivals_Purchases {
            get {
                return ResourceManager.GetString("IntrastatReportforEECArrivals_Purchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrastat Report for EEC Dispatches (Sales).
        /// </summary>
        internal static string IntrastatReportforEECDispatches_Sales {
            get {
                return ResourceManager.GetString("IntrastatReportforEECDispatches_Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrastat Report for EEC Dispatches (Supplier RMAs).
        /// </summary>
        internal static string IntrastatReportforEECDispatches_SupplierRMAs {
            get {
                return ResourceManager.GetString("IntrastatReportforEECDispatches_SupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Purchasing Information.
        /// </summary>
        internal static string InvalidCompanyPurchasingInfo {
            get {
                return ResourceManager.GetString("InvalidCompanyPurchasingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Sales Information.
        /// </summary>
        internal static string InvalidCompanySalesInfo {
            get {
                return ResourceManager.GetString("InvalidCompanySalesInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Only?.
        /// </summary>
        internal static string InvalidOnly {
            get {
                return ResourceManager.GetString("InvalidOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory Location Report.
        /// </summary>
        internal static string InventoryLocationReport {
            get {
                return ResourceManager.GetString("InventoryLocationReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory Location Report for Lot.
        /// </summary>
        internal static string InventoryLocationReportforLot {
            get {
                return ResourceManager.GetString("InventoryLocationReportforLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Cost.
        /// </summary>
        internal static string InvoiceCost {
            get {
                return ResourceManager.GetString("InvoiceCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Date.
        /// </summary>
        internal static string InvoiceDate {
            get {
                return ResourceManager.GetString("InvoiceDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string InvoiceEmailId {
            get {
                return ResourceManager.GetString("InvoiceEmailId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Email Status.
        /// </summary>
        internal static string InvoiceEmailStatus {
            get {
                return ResourceManager.GetString("InvoiceEmailStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to InvoiceLine Allocation Name.
        /// </summary>
        internal static string InvoiceLineAllocationName {
            get {
                return ResourceManager.GetString("InvoiceLineAllocationName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice No.
        /// </summary>
        internal static string InvoiceNo {
            get {
                return ResourceManager.GetString("InvoiceNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Number.
        /// </summary>
        internal static string InvoiceNumber {
            get {
                return ResourceManager.GetString("InvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Price.
        /// </summary>
        internal static string InvoicePrice {
            get {
                return ResourceManager.GetString("InvoicePrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Quantity.
        /// </summary>
        internal static string InvoiceQuantity {
            get {
                return ResourceManager.GetString("InvoiceQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string Invoices {
            get {
                return ResourceManager.GetString("Invoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices Sorted by Invoice Number.
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumber {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices Sorted by Invoice Number for a Customer.
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumberforaCustomer {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumberforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices Sorted by Invoice Number for a Salesperson.
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumberforaSalesperson {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumberforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Valuation.
        /// </summary>
        internal static string InvoiceValuation {
            get {
                return ResourceManager.GetString("InvoiceValuation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Value.
        /// </summary>
        internal static string InvoiceValue {
            get {
                return ResourceManager.GetString("InvoiceValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoicing.
        /// </summary>
        internal static string Invoicing {
            get {
                return ResourceManager.GetString("Invoicing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IP Address.
        /// </summary>
        internal static string IPAddress {
            get {
                return ResourceManager.GetString("IPAddress", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Row.
        /// </summary>
        internal static string iRowCount {
            get {
                return ResourceManager.GetString("iRowCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Landed Cost.
        /// </summary>
        internal static string LandedCost {
            get {
                return ResourceManager.GetString("LandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Contact Date.
        /// </summary>
        internal static string LastContactDate {
            get {
                return ResourceManager.GetString("LastContactDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Name.
        /// </summary>
        internal static string LastName {
            get {
                return ResourceManager.GetString("LastName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Late 1 Day %.
        /// </summary>
        internal static string Late1DayPct {
            get {
                return ResourceManager.GetString("Late1DayPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Late 2 to 4 Days %.
        /// </summary>
        internal static string Late2To4DaysPct {
            get {
                return ResourceManager.GetString("Late2To4DaysPct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Late 5 Days Or More %.
        /// </summary>
        internal static string Late5DaysOrMorePct {
            get {
                return ResourceManager.GetString("Late5DaysOrMorePct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Late Only.
        /// </summary>
        internal static string LateOnly {
            get {
                return ResourceManager.GetString("LateOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lead time.
        /// </summary>
        internal static string LeadTime {
            get {
                return ResourceManager.GetString("LeadTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lead time (wks).
        /// </summary>
        internal static string LeadTimeWks {
            get {
                return ResourceManager.GetString("LeadTimeWks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Location.
        /// </summary>
        internal static string Location {
            get {
                return ResourceManager.GetString("Location", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string LogDate {
            get {
                return ResourceManager.GetString("LogDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Name.
        /// </summary>
        internal static string LoginName {
            get {
                return ResourceManager.GetString("LoginName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Statistics.
        /// </summary>
        internal static string LoginStatistics {
            get {
                return ResourceManager.GetString("LoginStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Statistics by Name.
        /// </summary>
        internal static string LoginStatisticsbyName {
            get {
                return ResourceManager.GetString("LoginStatisticsbyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot.
        /// </summary>
        internal static string Lot {
            get {
                return ResourceManager.GetString("Lot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot Code.
        /// </summary>
        internal static string LotCode {
            get {
                return ResourceManager.GetString("LotCode", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot Stock Provision.
        /// </summary>
        internal static string LotStockProvision {
            get {
                return ResourceManager.GetString("LotStockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LTB.
        /// </summary>
        internal static string LTB {
            get {
                return ResourceManager.GetString("LTB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lytica Manufacture.
        /// </summary>
        internal static string LyticaManufacture {
            get {
                return ResourceManager.GetString("LyticaManufacture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Management.
        /// </summary>
        internal static string Management {
            get {
                return ResourceManager.GetString("Management", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer.
        /// </summary>
        internal static string Manufacturer {
            get {
                return ResourceManager.GetString("Manufacturer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer Name.
        /// </summary>
        internal static string ManufacturerName {
            get {
                return ResourceManager.GetString("ManufacturerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Margin.
        /// </summary>
        internal static string Margin {
            get {
                return ResourceManager.GetString("Margin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Market Leading (Lytica).
        /// </summary>
        internal static string MarketLeadingLytica {
            get {
                return ResourceManager.GetString("MarketLeadingLytica", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MOQ.
        /// </summary>
        internal static string MOQ {
            get {
                return ResourceManager.GetString("MOQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MPN Quoted.
        /// </summary>
        internal static string MPNQuoted {
            get {
                return ResourceManager.GetString("MPNQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MSL.
        /// </summary>
        internal static string MSL {
            get {
                return ResourceManager.GetString("MSL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        internal static string Name {
            get {
                return ResourceManager.GetString("Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string Never {
            get {
                return ResourceManager.GetString("Never", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Landed Cost.
        /// </summary>
        internal static string NewLandedCost {
            get {
                return ResourceManager.GetString("NewLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Accounts.
        /// </summary>
        internal static string NoOfAccounts {
            get {
                return ResourceManager.GetString("NoOfAccounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Credits.
        /// </summary>
        internal static string NoOfCredits {
            get {
                return ResourceManager.GetString("NoOfCredits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Customers.
        /// </summary>
        internal static string NoOfCustomers {
            get {
                return ResourceManager.GetString("NoOfCustomers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of GoodsIn.
        /// </summary>
        internal static string NoOfGoodsIn {
            get {
                return ResourceManager.GetString("NoOfGoodsIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Invoices.
        /// </summary>
        internal static string NoOfInvoices {
            get {
                return ResourceManager.GetString("NoOfInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Items.
        /// </summary>
        internal static string NoOfItems {
            get {
                return ResourceManager.GetString("NoOfItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Offers.
        /// </summary>
        internal static string NoOfOffers {
            get {
                return ResourceManager.GetString("NoOfOffers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Offers History.
        /// </summary>
        internal static string NoOfOffersHistory {
            get {
                return ResourceManager.GetString("NoOfOffersHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Orders.
        /// </summary>
        internal static string NoOfOrders {
            get {
                return ResourceManager.GetString("NoOfOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No of Requirements.
        /// </summary>
        internal static string NoOfRequirements {
            get {
                return ResourceManager.GetString("NoOfRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string Notes {
            get {
                return ResourceManager.GetString("Notes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Accounts by Salesperson.
        /// </summary>
        internal static string NumberofAccountsbySalesperson {
            get {
                return ResourceManager.GetString("NumberofAccountsbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Items.
        /// </summary>
        internal static string NumberOfItems {
            get {
                return ResourceManager.GetString("NumberOfItems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Offers by Vendor.
        /// </summary>
        internal static string NumberofOffersbyVendor {
            get {
                return ResourceManager.GetString("NumberofOffersbyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Offers History by Vendor.
        /// </summary>
        internal static string NumberofOffersHistorybyVendor {
            get {
                return ResourceManager.GetString("NumberofOffersHistorybyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Requirements by Vendor.
        /// </summary>
        internal static string NumberofRequirementsbyVendor {
            get {
                return ResourceManager.GetString("NumberofRequirementsbyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers.
        /// </summary>
        internal static string Offers {
            get {
                return ResourceManager.GetString("Offers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Stop.
        /// </summary>
        internal static string OnStop {
            get {
                return ResourceManager.GetString("OnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On Time %.
        /// </summary>
        internal static string OnTimePct {
            get {
                return ResourceManager.GetString("OnTimePct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs.
        /// </summary>
        internal static string OpenCustomerRMAs {
            get {
                return ResourceManager.GetString("OpenCustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs for a Customer.
        /// </summary>
        internal static string OpenCustomerRMAsforaCustomer {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs for a Customer with Reasons.
        /// </summary>
        internal static string OpenCustomerRMAsforaCustomerwithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaCustomerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs for a Saleperson.
        /// </summary>
        internal static string OpenCustomerRMAsforaSaleperson {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaSaleperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs for a Saleperson with Reasons.
        /// </summary>
        internal static string OpenCustomerRMAsforaSalepersonwithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaSalepersonwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs with Reasons.
        /// </summary>
        internal static string OpenCustomerRMAswithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Purchase Orders.
        /// </summary>
        internal static string OpenPurchaseOrders {
            get {
                return ResourceManager.GetString("OpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Purchase Orders by Company Type.
        /// </summary>
        internal static string OpenPurchaseOrdersbyCompanyType {
            get {
                return ResourceManager.GetString("OpenPurchaseOrdersbyCompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Purchase Orders by Supplier.
        /// </summary>
        internal static string OpenPurchaseOrdersbySupplier {
            get {
                return ResourceManager.GetString("OpenPurchaseOrdersbySupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Quotes.
        /// </summary>
        internal static string OpenQuotes {
            get {
                return ResourceManager.GetString("OpenQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Requirements by Customer.
        /// </summary>
        internal static string OpenRequirementsbyCustomer {
            get {
                return ResourceManager.GetString("OpenRequirementsbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Requirements Report By Salesperson.
        /// </summary>
        internal static string OpenRequirementsReportBySalesperson {
            get {
                return ResourceManager.GetString("OpenRequirementsReportBySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Sales.
        /// </summary>
        internal static string OpenSales {
            get {
                return ResourceManager.GetString("OpenSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Sales Orders.
        /// </summary>
        internal static string OpenSalesOrders {
            get {
                return ResourceManager.GetString("OpenSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Sales Orders for Salesperson.
        /// </summary>
        internal static string OpenSalesOrdersforSalesperson {
            get {
                return ResourceManager.GetString("OpenSalesOrdersforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs.
        /// </summary>
        internal static string OpenSupplierRMAs {
            get {
                return ResourceManager.GetString("OpenSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs for a Buyer.
        /// </summary>
        internal static string OpenSupplierRMAsforaBuyer {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs for a Buyer with Reasons.
        /// </summary>
        internal static string OpenSupplierRMAsforaBuyerwithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaBuyerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs for a Supplier.
        /// </summary>
        internal static string OpenSupplierRMAsforaSupplier {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs for a Supplier with Reasons.
        /// </summary>
        internal static string OpenSupplierRMAsforaSupplierwithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaSupplierwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs with Reasons.
        /// </summary>
        internal static string OpenSupplierRMAswithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders %.
        /// </summary>
        internal static string OrdersPercentage {
            get {
                return ResourceManager.GetString("OrdersPercentage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders To Be Shipped.
        /// </summary>
        internal static string OrdersToBeShipped {
            get {
                return ResourceManager.GetString("OrdersToBeShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders To Be Shipped By Salesperson.
        /// </summary>
        internal static string OrdersToBeShippedBySalesperson {
            get {
                return ResourceManager.GetString("OrdersToBeShippedBySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Original Landed Cost.
        /// </summary>
        internal static string OriginalLandedCost {
            get {
                return ResourceManager.GetString("OriginalLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Oustanding Customer Invoices.
        /// </summary>
        internal static string OustandingCustomerInvoices {
            get {
                return ResourceManager.GetString("OustandingCustomerInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package.
        /// </summary>
        internal static string Package {
            get {
                return ResourceManager.GetString("Package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Name.
        /// </summary>
        internal static string Packagename {
            get {
                return ResourceManager.GetString("Packagename", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Type.
        /// </summary>
        internal static string PackageType {
            get {
                return ResourceManager.GetString("PackageType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Package Unit.
        /// </summary>
        internal static string PackageUnit {
            get {
                return ResourceManager.GetString("PackageUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paid.
        /// </summary>
        internal static string Paid {
            get {
                return ResourceManager.GetString("Paid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part.
        /// </summary>
        internal static string Part {
            get {
                return ResourceManager.GetString("Part", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part Ordered.
        /// </summary>
        internal static string PartOrdered {
            get {
                return ResourceManager.GetString("PartOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Physical Count.
        /// </summary>
        internal static string PhysicalCount {
            get {
                return ResourceManager.GetString("PhysicalCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick Sheet - Sales Orders Basic.
        /// </summary>
        internal static string PickSheetSalesOrdersBasic {
            get {
                return ResourceManager.GetString("PickSheetSalesOrdersBasic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick Sheet - Sales Orders Detailed.
        /// </summary>
        internal static string PickSheetSalesOrdersDetailed {
            get {
                return ResourceManager.GetString("PickSheetSalesOrdersDetailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick Sheet - Supplier RMAs.
        /// </summary>
        internal static string PickSheetSupplierRMAs {
            get {
                return ResourceManager.GetString("PickSheetSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick Up Time.
        /// </summary>
        internal static string PickUp {
            get {
                return ResourceManager.GetString("PickUp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Approved.
        /// </summary>
        internal static string POApproved {
            get {
                return ResourceManager.GetString("POApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirmed?.
        /// </summary>
        internal static string POConfirmed {
            get {
                return ResourceManager.GetString("POConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Delivery Status.
        /// </summary>
        internal static string PODeliveryStatus {
            get {
                return ResourceManager.GetString("PODeliveryStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Line No.
        /// </summary>
        internal static string POLineNo {
            get {
                return ResourceManager.GetString("POLineNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Number.
        /// </summary>
        internal static string PONumber {
            get {
                return ResourceManager.GetString("PONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO Rating.
        /// </summary>
        internal static string PORating {
            get {
                return ResourceManager.GetString("PORating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Posted Lines Only?.
        /// </summary>
        internal static string PostedOnly {
            get {
                return ResourceManager.GetString("PostedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PQ Id.
        /// </summary>
        internal static string PQId {
            get {
                return ResourceManager.GetString("PQId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price.
        /// </summary>
        internal static string Price {
            get {
                return ResourceManager.GetString("Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product.
        /// </summary>
        internal static string Product {
            get {
                return ResourceManager.GetString("Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Description.
        /// </summary>
        internal static string ProductDescription {
            get {
                return ResourceManager.GetString("ProductDescription", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Name.
        /// </summary>
        internal static string ProductName {
            get {
                return ResourceManager.GetString("ProductName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Product Type.
        /// </summary>
        internal static string ProductType {
            get {
                return ResourceManager.GetString("ProductType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders Due In.
        /// </summary>
        internal static string PurchaseOrdersDueIn {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders Due In for Buyer.
        /// </summary>
        internal static string PurchaseOrdersDueInforBuyer {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueInforBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders Due In for Salesperson.
        /// </summary>
        internal static string PurchaseOrdersDueInforSalesperson {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueInforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PurchaseQuoteLineId.
        /// </summary>
        internal static string PurchaseQuoteLineId {
            get {
                return ResourceManager.GetString("PurchaseQuoteLineId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions.
        /// </summary>
        internal static string PurchaseRequisitions {
            get {
                return ResourceManager.GetString("PurchaseRequisitions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions for a Customer.
        /// </summary>
        internal static string PurchaseRequisitionsforaCustomer {
            get {
                return ResourceManager.GetString("PurchaseRequisitionsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions for a Sales Person.
        /// </summary>
        internal static string PurchaseRequisitionsforaSalesPerson {
            get {
                return ResourceManager.GetString("PurchaseRequisitionsforaSalesPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Value.
        /// </summary>
        internal static string PurchaseValue {
            get {
                return ResourceManager.GetString("PurchaseValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchasing.
        /// </summary>
        internal static string Purchasing {
            get {
                return ResourceManager.GetString("Purchasing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Unallocated (In Stock/On Order).
        /// </summary>
        internal static string QtyUnallocatedAll {
            get {
                return ResourceManager.GetString("QtyUnallocatedAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Unallocated (On Order).
        /// </summary>
        internal static string QtyUnallocatedOnOrder {
            get {
                return ResourceManager.GetString("QtyUnallocatedOnOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Qty Unallocated (In Stock).
        /// </summary>
        internal static string QtyUnallocatedStock {
            get {
                return ResourceManager.GetString("QtyUnallocatedStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Control Notes.
        /// </summary>
        internal static string QualityControlNotes {
            get {
                return ResourceManager.GetString("QualityControlNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity.
        /// </summary>
        internal static string Quantity {
            get {
                return ResourceManager.GetString("Quantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Allocated.
        /// </summary>
        internal static string QuantityAllocated {
            get {
                return ResourceManager.GetString("QuantityAllocated", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity In Stock.
        /// </summary>
        internal static string QuantityInStock {
            get {
                return ResourceManager.GetString("QuantityInStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity On Order.
        /// </summary>
        internal static string QuantityOnOrder {
            get {
                return ResourceManager.GetString("QuantityOnOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Ordered.
        /// </summary>
        internal static string QuantityOrdered {
            get {
                return ResourceManager.GetString("QuantityOrdered", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Quoted.
        /// </summary>
        internal static string QuantityQuoted {
            get {
                return ResourceManager.GetString("QuantityQuoted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Received.
        /// </summary>
        internal static string QuantityReceived {
            get {
                return ResourceManager.GetString("QuantityReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quantity Shipped.
        /// </summary>
        internal static string QuantityShipped {
            get {
                return ResourceManager.GetString("QuantityShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Date.
        /// </summary>
        internal static string QuoteDate {
            get {
                return ResourceManager.GetString("QuoteDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Number.
        /// </summary>
        internal static string QuoteNumber {
            get {
                return ResourceManager.GetString("QuoteNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Quotes {
            get {
                return ResourceManager.GetString("Quotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random Stock Check.
        /// </summary>
        internal static string RandomStockCheck {
            get {
                return ResourceManager.GetString("RandomStockCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Real Price.
        /// </summary>
        internal static string RealPrice {
            get {
                return ResourceManager.GetString("RealPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reason.
        /// </summary>
        internal static string Reason {
            get {
                return ResourceManager.GetString("Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received By.
        /// </summary>
        internal static string ReceivedBy {
            get {
                return ResourceManager.GetString("ReceivedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs.
        /// </summary>
        internal static string ReceivedCustomerRMAs {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs for a Customer.
        /// </summary>
        internal static string ReceivedCustomerRMAsforaCustomer {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs for a Customer with Reasons.
        /// </summary>
        internal static string ReceivedCustomerRMAsforaCustomerwithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaCustomerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs for a Saleperson.
        /// </summary>
        internal static string ReceivedCustomerRMAsforaSaleperson {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaSaleperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs for a Saleperson with Reasons.
        /// </summary>
        internal static string ReceivedCustomerRMAsforaSalepersonwithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaSalepersonwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs with Reasons.
        /// </summary>
        internal static string ReceivedCustomerRMAswithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Date.
        /// </summary>
        internal static string ReceivedDate {
            get {
                return ResourceManager.GetString("ReceivedDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Goods Valuation by Country.
        /// </summary>
        internal static string ReceivedGoodsValuationbyCountry {
            get {
                return ResourceManager.GetString("ReceivedGoodsValuationbyCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receiving.
        /// </summary>
        internal static string Receiving {
            get {
                return ResourceManager.GetString("Receiving", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receiving Notes.
        /// </summary>
        internal static string ReceivingNotes {
            get {
                return ResourceManager.GetString("ReceivingNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Released.
        /// </summary>
        internal static string Released {
            get {
                return ResourceManager.GetString("Released", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report Name.
        /// </summary>
        internal static string ReportName {
            get {
                return ResourceManager.GetString("ReportName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report Type.
        /// </summary>
        internal static string ReportType {
            get {
                return ResourceManager.GetString("ReportType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Required Date.
        /// </summary>
        internal static string RequiredDate {
            get {
                return ResourceManager.GetString("RequiredDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirement Number.
        /// </summary>
        internal static string RequirementNumber {
            get {
                return ResourceManager.GetString("RequirementNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirements.
        /// </summary>
        internal static string Requirements {
            get {
                return ResourceManager.GetString("Requirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requisitions.
        /// </summary>
        internal static string Requisitions {
            get {
                return ResourceManager.GetString("Requisitions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resale.
        /// </summary>
        internal static string Resale {
            get {
                return ResourceManager.GetString("Resale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resale In Base.
        /// </summary>
        internal static string ResaleInBase {
            get {
                return ResourceManager.GetString("ResaleInBase", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resale Value.
        /// </summary>
        internal static string ResaleValue {
            get {
                return ResourceManager.GetString("ResaleValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return Cost.
        /// </summary>
        internal static string ReturnCost {
            get {
                return ResourceManager.GetString("ReturnCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return Date.
        /// </summary>
        internal static string ReturnDate {
            get {
                return ResourceManager.GetString("ReturnDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return Price.
        /// </summary>
        internal static string ReturnPrice {
            get {
                return ResourceManager.GetString("ReturnPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Return Value.
        /// </summary>
        internal static string ReturnValue {
            get {
                return ResourceManager.GetString("ReturnValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Compliant.
        /// </summary>
        internal static string ROHSCompliant {
            get {
                return ResourceManager.GetString("ROHSCompliant", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ROHS Status.
        /// </summary>
        internal static string ROHSStatus {
            get {
                return ResourceManager.GetString("ROHSStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rohs (Y/N).
        /// </summary>
        internal static string RohsYN {
            get {
                return ResourceManager.GetString("RohsYN", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rows Affected.
        /// </summary>
        internal static string RowsAffected {
            get {
                return ResourceManager.GetString("RowsAffected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sale.
        /// </summary>
        internal static string Sale {
            get {
                return ResourceManager.GetString("Sale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales.
        /// </summary>
        internal static string Sales {
            get {
                return ResourceManager.GetString("Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales History.
        /// </summary>
        internal static string SalesHistory {
            get {
                return ResourceManager.GetString("SalesHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson.
        /// </summary>
        internal static string Salesman {
            get {
                return ResourceManager.GetString("Salesman", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesman2.
        /// </summary>
        internal static string Salesman2 {
            get {
                return ResourceManager.GetString("Salesman2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson GP.
        /// </summary>
        internal static string SalesmanGP {
            get {
                return ResourceManager.GetString("SalesmanGP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SalesMan Name.
        /// </summary>
        internal static string SalesManName {
            get {
                return ResourceManager.GetString("SalesManName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Salesperson %.
        /// </summary>
        internal static string SalesmanPercent {
            get {
                return ResourceManager.GetString("SalesmanPercent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Price (Base).
        /// </summary>
        internal static string SalesOrderLineBaseValue {
            get {
                return ResourceManager.GetString("SalesOrderLineBaseValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Line Quantity.
        /// </summary>
        internal static string SalesOrderLineQuantity {
            get {
                return ResourceManager.GetString("SalesOrderLineQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Line Value.
        /// </summary>
        internal static string SalesOrderLineValue {
            get {
                return ResourceManager.GetString("SalesOrderLineValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sent Date.
        /// </summary>
        internal static string Sentdate {
            get {
                return ResourceManager.GetString("Sentdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship ASAP.
        /// </summary>
        internal static string ShipASAP {
            get {
                return ResourceManager.GetString("ShipASAP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped By.
        /// </summary>
        internal static string ShippedBy {
            get {
                return ResourceManager.GetString("ShippedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Goods Valuation by Country.
        /// </summary>
        internal static string ShippedGoodsValuationbyCountry {
            get {
                return ResourceManager.GetString("ShippedGoodsValuationbyCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Orders Sorted by Invoice Number.
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumber {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Orders Sorted by Invoice Number for a Customer.
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumberforaCustomer {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumberforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Orders Sorted by Invoice Number for a Salesperson.
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumberforaSalesperson {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumberforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Sales.
        /// </summary>
        internal static string ShippedSales {
            get {
                return ResourceManager.GetString("ShippedSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Sales for Lot.
        /// </summary>
        internal static string ShippedSalesforLot {
            get {
                return ResourceManager.GetString("ShippedSalesforLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs.
        /// </summary>
        internal static string ShippedSupplierRMAs {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs for a Buyer.
        /// </summary>
        internal static string ShippedSupplierRMAsforaBuyer {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs for a Buyer with Reasons.
        /// </summary>
        internal static string ShippedSupplierRMAsforaBuyerwithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaBuyerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs for a Supplier.
        /// </summary>
        internal static string ShippedSupplierRMAsforaSupplier {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs for a Supplier with Reasons.
        /// </summary>
        internal static string ShippedSupplierRMAsforaSupplierwithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaSupplierwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs with Reasons.
        /// </summary>
        internal static string ShippedSupplierRMAswithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping.
        /// </summary>
        internal static string Shipping {
            get {
                return ResourceManager.GetString("Shipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Charge.
        /// </summary>
        internal static string ShippingCharge {
            get {
                return ResourceManager.GetString("ShippingCharge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Cost.
        /// </summary>
        internal static string ShippingCost {
            get {
                return ResourceManager.GetString("ShippingCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Status.
        /// </summary>
        internal static string ShipStatus {
            get {
                return ResourceManager.GetString("ShipStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Via.
        /// </summary>
        internal static string ShipVia {
            get {
                return ResourceManager.GetString("ShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Approved.
        /// </summary>
        internal static string SOApproved {
            get {
                return ResourceManager.GetString("SOApproved", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Line No.
        /// </summary>
        internal static string SOLineNo {
            get {
                return ResourceManager.GetString("SOLineNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Number.
        /// </summary>
        internal static string SONumber {
            get {
                return ResourceManager.GetString("SONumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Rating.
        /// </summary>
        internal static string SORating {
            get {
                return ResourceManager.GetString("SORating", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO Sent To Customer.
        /// </summary>
        internal static string SOSentToCustomer {
            get {
                return ResourceManager.GetString("SOSentToCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SPQ.
        /// </summary>
        internal static string SPQ {
            get {
                return ResourceManager.GetString("SPQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Date.
        /// </summary>
        internal static string StartDate {
            get {
                return ResourceManager.GetString("StartDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Time.
        /// </summary>
        internal static string StartTime {
            get {
                return ResourceManager.GetString("StartTime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to State.
        /// </summary>
        internal static string State {
            get {
                return ResourceManager.GetString("State", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock.
        /// </summary>
        internal static string Stock {
            get {
                return ResourceManager.GetString("Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Count.
        /// </summary>
        internal static string StockCount {
            get {
                return ResourceManager.GetString("StockCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Date.
        /// </summary>
        internal static string StockDate {
            get {
                return ResourceManager.GetString("StockDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Id.
        /// </summary>
        internal static string StockId {
            get {
                return ResourceManager.GetString("StockId", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Keeping Unit.
        /// </summary>
        internal static string StockKeepingUnit {
            get {
                return ResourceManager.GetString("StockKeepingUnit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock List.
        /// </summary>
        internal static string StockList {
            get {
                return ResourceManager.GetString("StockList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Management.
        /// </summary>
        internal static string StockManagement {
            get {
                return ResourceManager.GetString("StockManagement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Name.
        /// </summary>
        internal static string StockName {
            get {
                return ResourceManager.GetString("StockName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Part.
        /// </summary>
        internal static string StockPart {
            get {
                return ResourceManager.GetString("StockPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Valuation.
        /// </summary>
        internal static string StockValuation {
            get {
                return ResourceManager.GetString("StockValuation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Late Only.
        /// </summary>
        internal static string String1 {
            get {
                return ResourceManager.GetString("String1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sum.
        /// </summary>
        internal static string Sum {
            get {
                return ResourceManager.GetString("Sum", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary.
        /// </summary>
        internal static string Summary {
            get {
                return ResourceManager.GetString("Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Booked Orders by Customer.
        /// </summary>
        internal static string SummaryBookedOrdersbyCustomer {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Booked Orders by Division.
        /// </summary>
        internal static string SummaryBookedOrdersbyDivision {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Booked Orders by Salesperson.
        /// </summary>
        internal static string SummaryBookedOrdersbySalesperson {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Open Orders by Customer.
        /// </summary>
        internal static string SummaryOpenOrdersbyCustomer {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Open Orders by Division.
        /// </summary>
        internal static string SummaryOpenOrdersbyDivision {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Open Orders by Salesperson.
        /// </summary>
        internal static string SummaryOpenOrdersbySalesperson {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Shipped Sales by Customer.
        /// </summary>
        internal static string SummaryShippedSalesbyCustomer {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Shipped Sales by Division.
        /// </summary>
        internal static string SummaryShippedSalesbyDivision {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Shipped Sales by Salesperson.
        /// </summary>
        internal static string SummaryShippedSalesbySalesperson {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipping Cost.
        /// </summary>
        internal static string Sum_ShippingCost {
            get {
                return ResourceManager.GetString("Sum_ShippingCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        internal static string Supplier {
            get {
                return ResourceManager.GetString("Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Fax.
        /// </summary>
        internal static string SupplierFax {
            get {
                return ResourceManager.GetString("SupplierFax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier No.
        /// </summary>
        internal static string SupplierNo {
            get {
                return ResourceManager.GetString("SupplierNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notes.
        /// </summary>
        internal static string SupplierNotes {
            get {
                return ResourceManager.GetString("SupplierNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier On Time Delivery Report.
        /// </summary>
        internal static string SupplierOnTimeDeliveryReport {
            get {
                return ResourceManager.GetString("SupplierOnTimeDeliveryReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Part.
        /// </summary>
        internal static string SupplierPart {
            get {
                return ResourceManager.GetString("SupplierPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Return Value.
        /// </summary>
        internal static string SupplierReturnValue {
            get {
                return ResourceManager.GetString("SupplierReturnValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SupplierRMA {
            get {
                return ResourceManager.GetString("SupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Date.
        /// </summary>
        internal static string SupplierRMADate {
            get {
                return ResourceManager.GetString("SupplierRMADate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Number.
        /// </summary>
        internal static string SupplierRMANumber {
            get {
                return ResourceManager.GetString("SupplierRMANumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMAs.
        /// </summary>
        internal static string SupplierRMAs {
            get {
                return ResourceManager.GetString("SupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Telephone.
        /// </summary>
        internal static string SupplierTelephone {
            get {
                return ResourceManager.GetString("SupplierTelephone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System.
        /// </summary>
        internal static string System {
            get {
                return ResourceManager.GetString("System", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target.
        /// </summary>
        internal static string Target {
            get {
                return ResourceManager.GetString("Target", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Price.
        /// </summary>
        internal static string TargetPrice {
            get {
                return ResourceManager.GetString("TargetPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Target Price (70th Percentile).
        /// </summary>
        internal static string TargetPriceLytica {
            get {
                return ResourceManager.GetString("TargetPriceLytica", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax.
        /// </summary>
        internal static string Tax {
            get {
                return ResourceManager.GetString("Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Problems.
        /// </summary>
        internal static string TaxProblems {
            get {
                return ResourceManager.GetString("TaxProblems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tax Rate.
        /// </summary>
        internal static string TaxRate {
            get {
                return ResourceManager.GetString("TaxRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Team Name.
        /// </summary>
        internal static string TeamName {
            get {
                return ResourceManager.GetString("TeamName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Telephone.
        /// </summary>
        internal static string Telephone {
            get {
                return ResourceManager.GetString("Telephone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms.
        /// </summary>
        internal static string Terms {
            get {
                return ResourceManager.GetString("Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms Date.
        /// </summary>
        internal static string TermsDate {
            get {
                return ResourceManager.GetString("TermsDate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Terms Problems.
        /// </summary>
        internal static string TermsProblems {
            get {
                return ResourceManager.GetString("TermsProblems", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        internal static string Title {
            get {
                return ResourceManager.GetString("Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Cost.
        /// </summary>
        internal static string TotalCost {
            get {
                return ResourceManager.GetString("TotalCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Landed Cost.
        /// </summary>
        internal static string TotalLandedCost {
            get {
                return ResourceManager.GetString("TotalLandedCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total POs.
        /// </summary>
        internal static string TotalPOs {
            get {
                return ResourceManager.GetString("TotalPOs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Resale.
        /// </summary>
        internal static string TotalResale {
            get {
                return ResourceManager.GetString("TotalResale", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Sales.
        /// </summary>
        internal static string TotalSales {
            get {
                return ResourceManager.GetString("TotalSales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Shipping Cost.
        /// </summary>
        internal static string TotalShippingCost {
            get {
                return ResourceManager.GetString("TotalShippingCost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total SRMAs.
        /// </summary>
        internal static string TotalSRMAs {
            get {
                return ResourceManager.GetString("TotalSRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Tax.
        /// </summary>
        internal static string TotalTax {
            get {
                return ResourceManager.GetString("TotalTax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Value.
        /// </summary>
        internal static string TotalValue {
            get {
                return ResourceManager.GetString("TotalValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to TQSA.
        /// </summary>
        internal static string TQSA {
            get {
                return ResourceManager.GetString("TQSA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unallocated Only?.
        /// </summary>
        internal static string UnallocatedOnly {
            get {
                return ResourceManager.GetString("UnallocatedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Price.
        /// </summary>
        internal static string UnitPrice {
            get {
                return ResourceManager.GetString("UnitPrice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to URL.
        /// </summary>
        internal static string URL {
            get {
                return ResourceManager.GetString("URL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User List.
        /// </summary>
        internal static string UserList {
            get {
                return ResourceManager.GetString("UserList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Users.
        /// </summary>
        internal static string Users {
            get {
                return ResourceManager.GetString("Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Value.
        /// </summary>
        internal static string Value {
            get {
                return ResourceManager.GetString("Value", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Valued Stock Only?.
        /// </summary>
        internal static string ValueStockOnly {
            get {
                return ResourceManager.GetString("ValueStockOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vendor.
        /// </summary>
        internal static string Vendor {
            get {
                return ResourceManager.GetString("Vendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Weight In Kgs.
        /// </summary>
        internal static string WeightInKgs {
            get {
                return ResourceManager.GetString("WeightInKgs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zero Value Only?.
        /// </summary>
        internal static string ZeroValueOnly {
            get {
                return ResourceManager.GetString("ZeroValueOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Zip Code.
        /// </summary>
        internal static string ZipCode {
            get {
                return ResourceManager.GetString("ZipCode", resourceCulture);
            }
        }
    }
}
