Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Invoices=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Invoices.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Invoices.prototype={initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/InvoiceLines";this._strDataObject="InvoiceLines";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Invoices.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Invoices.callBaseMethod(this,"dispose")},getDataOK:function(){for(var n,t,i=0,r=this._objResult.Results.length;i<r;i++)n=this._objResult.Results[i],t=String.format('<a href="{0}"><b>{1}<\/b> - {2}',$RGT_gotoURL_Invoice(n.ID),n.No,$R_FN.setCleanTextValue(n.CM)),n.Part.length>0&&(t+=String.format("<br />{0} x {1}<\/a>",n.Quantity,$R_FN.writePartNo(n.Part,n.ROHS))),t+="<\/a>",this._tbl.addRow([t],n.ID,!1),t=null,n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Invoices.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Invoices",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);