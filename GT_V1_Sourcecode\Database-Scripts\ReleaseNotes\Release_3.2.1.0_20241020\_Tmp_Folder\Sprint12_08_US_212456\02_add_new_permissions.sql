﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213080]     An.TranTan		 10-Oct-2024		CREATE		Add permission allow view list - import - view detail prospective cross selling
===========================================================================================  
*/
DECLARE @CurrentSortOrder INT;
SELECT @CurrentSortOrder = ISNULL(MAX(DisplaySortOrder),0) FROM tbSecurityFunction WHERE SiteSectionNo = 2; --Orders
select @CurrentSortOrder
IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Orders_ProspectiveCrossSelling'
		OR SecurityFunctionId = 20010025
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     20010025,                -- SecurityFunctionId
    'Orders_ProspectiveCrossSelling',            -- FunctionName
    'Allow viewing Prospective Cross Selling Import and List section', -- Description
    NULL,                      -- SitePageNo
    2,                        -- SiteSectionNo
    NULL,                     -- ReportNo (NULL because it's not related to a report)
    NULL,                     -- UpdatedBy (ID of the user who updated this function)
    GETDATE(),                -- DLUP (current date and time)
    0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 1                        -- DisplaySortOrder
);
END

IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Orders_ProsCrossSellingDetail'
		OR SecurityFunctionId = 20010029
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     20010029,                -- SecurityFunctionId
    'Orders_ProsCrossSellingDetail',            -- FunctionName
    'Allow viewing Prospective Cross Selling Details', -- Description
    NULL,                      -- SitePageNo
    2,                        -- SiteSectionNo
    NULL,                     -- ReportNo (NULL because it's not related to a report)
    NULL,                     -- UpdatedBy (ID of the user who updated this function)
    GETDATE(),                -- DLUP (current date and time)
    0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 2                        -- DisplaySortOrder
);
END
