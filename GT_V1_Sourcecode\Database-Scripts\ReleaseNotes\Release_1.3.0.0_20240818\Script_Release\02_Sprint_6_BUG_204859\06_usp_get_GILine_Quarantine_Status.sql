﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-204859]		Cuong.DoX			12-JULY-2024		Create			Return quarantine status of the GI Line
===========================================================================================
*/

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		Cuongdx
-- Create date: 12 Jul 2024
-- Description:	Return quarantine status of the GI Line
-- =====================================
CREATE OR ALTER PROCEDURE usp_get_GILine_Quarantine_Status
	-- Add the parameters for the stored procedure here
	@GoodsInLineId INT,
	@Unavailable BIT OUT
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
    -- Insert statements for Sale Email here
	SELECT @Unavailable = ISNULL(gil.Unavailable,0)
	FROM tbGoodsInLine gil
	WHERE gil.GoodsInLineId = @GoodsInLineId

END	

GO
