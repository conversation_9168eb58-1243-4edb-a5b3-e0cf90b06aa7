﻿/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for different section
*/
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class EndUserUndertakingPDF_DeleteDragDrop : Base
    {


        #region Locals

        #endregion

        #region Properties

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "PDF_Delete");
            AddScriptReference("Controls.Nuggets.EndUserUndertakingPDFDragDrop.Delete.EndUserUndertakingPDF_DeleteDragDrop.js");
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.EndUserUndertakingPDF_DeleteDragDrop", ctlDesignBase.ClientID);
        }

    }
}