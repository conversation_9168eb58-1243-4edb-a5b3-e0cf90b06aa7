//-----------------------------------------------------------------------------------------
// RP 05.01.2010:
// - add links back to related Quotes
//code[00112020] put ln.Notes inside ReplaceLineBreaks function by surendra 10-11-2016
//Marker     changed by      date         Remarks
//[001]      Aashu           20/06/2018   [REB-11754]: MSL level
//[002]     Abhinav <PERSON>xena  14-07-2021    Add new field partwatchmatch
// [003] Bhooma&Sunil   11/08/2021     Added method for delete req partwatch match
//[004]      Abhinav <PERSON>xena 10-09-2021     Remove partwatch from manual adding sourcing result
//[005]      Abhinav Saxena  30-09-2021    Add Hub offer directly via partwatch
//[006]      Abhinav <PERSON>xena 04-10-2021      Add new flag for different client
//[007]      <PERSON>rya          03/03/2023   RP-1048 Remove AI code
//[007]      <PERSON>rya          03/03/2023   RP-2340 (RP-2226)
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Linq;
//using Microsoft.ApplicationInsights; //[007] 

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CustomerRequirementSourcingResults : Rebound.GlobalTrader.Site.Data.Base
    {
        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    case "GetItem": GetItem(); break;
                    case "AddNew": AddNew(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "GetDataItem": GetDataItem(); break;
                    case "GetDataCopy": GetDataCopy(); break;
                    case "DeletePartWatchMatch": DeletePartWatchMatch(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        public override bool IsReusable
        {
            get { return false; }
        }


        private void GetDataItem()
        {
            List<SourcingResult> lst = SourcingResult.GetListForSourcing(ID, GetFormValue_NullableBoolean("FromQuote", false));
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {

                        //If the sourcing come from PO HUb, it will not display until release by PO Hub
                        if (ln.POHubReleaseBy == 0 && Convert.ToBoolean(SessionManager.IsPOHub) == false && ln.POHubCompanyNo.HasValue)
                            continue;

                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", ln.SourcingResultId);
                        if (ln.SourcingTable == "PQ" || ln.SourcingTable == "OFPH" || ln.SourcingTable == "EXPH")
                        {
                            jsnItem.AddVariable("SupplierNo", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubCompanyNo : ln.ClientCompanyNo);
                            jsnItem.AddVariable("Supplier", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubSupplierName : ln.ClientSupplierName);
                            jsnItem.AddVariable("IsHub", true);
                            jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                            if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode));
                            }
                            //jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode)); 
                        }
                        else
                        {
                            jsnItem.AddVariable("SupplierNo", ln.SupplierNo);
                            jsnItem.AddVariable("Supplier", ln.SupplierName);
                            jsnItem.AddVariable("IsHub", false);

                            jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                            if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                            }
                        }
                        jsnItem.AddVariable("Part", ln.Part);
                        jsnItem.AddVariable("Product", ln.ProductName);
                        jsnItem.AddVariable("Package", ln.PackageName);
                        jsnItem.AddVariable("Qty", ln.Quantity);
                        jsnItem.AddVariable("MfrNo", ln.ManufacturerNo);
                        jsnItem.AddVariable("Mfr", ln.ManufacturerCode);
                        jsnItem.AddVariable("DC", ln.DateCode);
                        jsnItem.AddVariable("Date", Functions.FormatDate(ln.OfferStatusChangeDate));
                        jsnItem.AddVariable("By", (String.IsNullOrEmpty(ln.SalesmanName)) ? ln.OfferStatusChangeEmployeeName : ln.SalesmanName);
                        jsnItem.AddVariable("Notes", Functions.ReplaceLineBreaks(ln.Notes));
                        BLL.Printer rm = BLL.Printer.GetRestrictedManufacture(SessionManager.ClientID, ln.ManufacturerNo);
                        if (rm == null)
                        {
                            jsnItem.AddVariable("isRestrictedManufacturer", null);
                        }
                        else
                        {

                            jsnItem.AddVariable("isRestrictedManufacturer", rm.isRestrictedManufacturer);

                        }
                        //get price in base currency too if it's not already


                        //if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                        //{
                        //    if (!SessionManager.IsPOHub.Value)
                        //    {
                        //        jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode));                                
                        //    }

                        //}

                        //status
                        string strStatus = "";
                        if (ln.OfferStatusNo != null) strStatus = Functions.GetGlobalResource("OfferStatus", (OfferStatus.List)ln.OfferStatusNo);
                        jsnItem.AddVariable("Status", strStatus);

                        //related quotes
                        JsonObject jsnQuotes = new JsonObject(true);
                        foreach (BLL.Quote qt in Quote.GetListForSourcingResult(ln.SourcingResultId))
                        {
                            JsonObject jsnQuote = new JsonObject();
                            jsnQuote.AddVariable("ID", qt.QuoteId);
                            jsnQuote.AddVariable("No", qt.QuoteNumber);
                            jsnQuotes.AddVariable(jsnQuote);
                            jsnQuote.Dispose(); jsnQuote = null;
                        }
                        jsnItem.AddVariable("Quotes", jsnQuotes);
                        jsnQuotes.Dispose(); jsnQuotes = null;

                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Results", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    WriteError(e);
                }
            }
        }


        private void GetData()
        {
            List<SourcingResult> lst = SourcingResult.GetListForCustomerRequirement(ID);
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    var listMfrNotes = Manufacturer.GetAdvisoryNotes(lst.Select(x => x.ManufacturerNo).ToList(), (int)SessionManager.ClientID);

                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {

                        //If the sourcing come from PO HUb, it will not display until release by PO Hub
                        if(ln.PartWatchMatch==false)
                        if (ln.POHubReleaseBy == 0 && Convert.ToBoolean(SessionManager.IsPOHub) == false && ln.POHubCompanyNo.HasValue)
                            continue;

                        JsonObject jsnItem = new JsonObject();
                        string companyNotes = string.Empty;
                        jsnItem.AddVariable("ID", ln.SourcingResultId);
                        double? dbEstShipCostInBase = BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.ClientCurrencyNo, DateTime.Now);
                        if (ln.SourcingTable == "PQ" || ln.SourcingTable == "OFPH" || ln.SourcingTable == "EXPH" || ln.SourcingTable == "HUBSTK")
                        {
                            //jsnItem.AddVariable("SupplierNo", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubCompanyNo : ln.ClientCompanyNo);
                            //jsnItem.AddVariable("Supplier", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubSupplierName : ln.ClientSupplierName);
                            if (Convert.ToBoolean(SessionManager.IsPOHub))
                            {
                                jsnItem.AddVariable("SupplierNo", ln.POHubCompanyNo);
                                jsnItem.AddVariable("Supplier", ln.POHubSupplierName);
                                companyNotes = Company.GetAdvisoryNotes(ln.POHubCompanyNo ?? 0);
                                jsnItem.AddVariable("SupplierAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));

                            }
                            else
                            {
                                jsnItem.AddVariable("SupplierNo", ln.ClientCompanyNo);
                                jsnItem.AddVariable("Supplier", ln.ClientSupplierName);
                                companyNotes = Company.GetAdvisoryNotes(ln.ClientCompanyNo ?? 0);
                                jsnItem.AddVariable("SupplierAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
                            }
                            
                            jsnItem.AddVariable("IsHub", true);
                            jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                            if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                if (!SessionManager.IsPOHub.Value)
                                {

                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                    jsnItem.AddVariable("EstimatedShippingCostInBase", dbEstShipCostInBase.HasValue && dbEstShipCostInBase.Value > 0 ? Functions.FormatCurrency(dbEstShipCostInBase.Value, SessionManager.ClientCurrencyCode) : "");

                                }
                                else
                                {
                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                }
                            }
                            //jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode)); 

                        }
                        else
                        {
                            jsnItem.AddVariable("SupplierNo", ln.SupplierNo);
                            jsnItem.AddVariable("Supplier", ln.SupplierName);
                            companyNotes = Company.GetAdvisoryNotes(ln.SupplierNo ?? 0);
                            jsnItem.AddVariable("SupplierAdvisoryNotes", Functions.ReplaceLineBreaks(companyNotes));
                            jsnItem.AddVariable("IsHub", false);

                            jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                            if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                if (!SessionManager.IsPOHub.Value)
                                {
                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                    //jsnItem.AddVariable("EstimatedShippingCostInBase",BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.CurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP)>0? Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.CurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode):"");
                                    jsnItem.AddVariable("EstimatedShippingCostInBase", dbEstShipCostInBase.HasValue && dbEstShipCostInBase.Value > 0 ? Functions.FormatCurrency(dbEstShipCostInBase.Value, SessionManager.ClientCurrencyCode) : "");
                                }
                            }
                        }
                        jsnItem.AddVariable("Part", ln.Part);
                        jsnItem.AddVariable("Product", ln.ProductName);
                        jsnItem.AddVariable("Package", ln.PackageName);
                        jsnItem.AddVariable("Qty", ln.Quantity);
                        jsnItem.AddVariable("MfrNo", ln.ManufacturerNo);
                        jsnItem.AddVariable("Mfr", ln.ManufacturerCode);
                        string mfrNotes = !Functions.HasNumbericValue(ln.ManufacturerNo) ? "" : listMfrNotes.Find(x => x.ManufacturerId == (int)ln.ManufacturerNo).AdvisoryNotes;
                        jsnItem.AddVariable("MfrAdvisoryNotes", Functions.ReplaceLineBreaks(mfrNotes));
                        jsnItem.AddVariable("DC", ln.DateCode);
                        jsnItem.AddVariable("Date", Functions.FormatDate(ln.OfferStatusChangeDate));
                        jsnItem.AddVariable("By", (String.IsNullOrEmpty(ln.SalesmanName)) ? ln.OfferStatusChangeEmployeeName : ln.SalesmanName);
                        //code[00112020] put ln.Notes inside ReplaceLineBreaks function
                        jsnItem.AddVariable("Notes", Functions.ReplaceLineBreaks(ln.Notes));

                        jsnItem.AddVariable("IsClosed", ln.IsClosed);
                        jsnItem.AddVariable("IsSoCreated", ln.IsSoCreated);
                        jsnItem.AddVariable("EstimatedShippingCost", ln.EstimatedShippingCost > 0 ? Functions.FormatCurrency(ln.EstimatedShippingCost, ln.CurrencyCode) : "");
                        jsnItem.AddVariable("PartWatchMatch", ln.PartWatchMatch);
                        jsnItem.AddVariable("DiffrentClientOffer", ln.DiffrentClientOffer);
                        jsnItem.AddVariable("ClientCode", ln.ClientCode);
                        //get price in base currency too if it's not already


                        //if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                        //{
                        //    if (!SessionManager.IsPOHub.Value)
                        //    {
                        //        jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode));                                
                        //    }

                        //}

                        //status
                        string strStatus = "";
                        if (ln.OfferStatusNo != null) strStatus = Functions.GetGlobalResource("OfferStatus", (OfferStatus.List)ln.OfferStatusNo);
                        jsnItem.AddVariable("Status", strStatus);

                        jsnItem.AddVariable("RegionName", ln.RegionName);
                        jsnItem.AddVariable("TermsName", ln.TermsName);
                        jsnItem.AddVariable("SupplierType", ln.SupplierType);
                        if (!string.IsNullOrEmpty(ln.MSL))
                            jsnItem.AddVariable("NotesMSL", string.Format("{0}/{1}", Functions.ReplaceLineBreaks(ln.Notes), ln.MSL));
                        else
                            jsnItem.AddVariable("NotesMSL", string.Format("{0}", Functions.ReplaceLineBreaks(ln.Notes)));
                        //related quotes
                        JsonObject jsnQuotes = new JsonObject(true);
                        foreach (BLL.Quote qt in Quote.GetListForSourcingResult(ln.SourcingResultId))
                        {
                            JsonObject jsnQuote = new JsonObject();
                            jsnQuote.AddVariable("ID", qt.QuoteId);
                            jsnQuote.AddVariable("No", qt.QuoteNumber);
                            jsnQuotes.AddVariable(jsnQuote);
                            jsnQuote.Dispose(); jsnQuote = null;
                        }
                        jsnItem.AddVariable("Quotes", jsnQuotes);
                        //[001] start
                        jsnItem.AddVariable("MSL", ln.MSL);
                        //[001] end
                        jsnQuotes.Dispose(); jsnQuotes = null;

                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Results", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    //[007]
                    //var ai = new TelemetryClient();
                    //ai.TrackTrace("ReqSourcingResult: GetData");
                    //ai.TrackException(e);
                    new Errorlog().LogMessage("Error at GetData() in CusReqSourcingResults.ashx.cs : " + e.Message);

                    WriteError(e);
                }
            }
        }

        private void GetDataCopy()
        {
            List<SourcingResult> lst = SourcingResult.GetListForCustomerRequirementCopy(ID);
            if (lst == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                try
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    foreach (SourcingResult ln in lst)
                    {

                        //If the sourcing come from PO HUb, it will not display until release by PO Hub
                        if (ln.POHubReleaseBy == 0 && Convert.ToBoolean(SessionManager.IsPOHub) == false && ln.POHubCompanyNo.HasValue)
                            continue;

                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", ln.SourcingResultId);
                        double? dbEstShipCostInBase = BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.ClientCurrencyNo, DateTime.Now);
                        if (ln.SourcingTable == "PQ" || ln.SourcingTable == "OFPH" || ln.SourcingTable == "EXPH")
                        {
                            jsnItem.AddVariable("SupplierNo", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubCompanyNo : ln.ClientCompanyNo);
                            jsnItem.AddVariable("Supplier", Convert.ToBoolean(SessionManager.IsPOHub) ? ln.POHubSupplierName : ln.ClientSupplierName);
                            jsnItem.AddVariable("IsHub", true);
                            jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                            if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                if (!SessionManager.IsPOHub.Value)
                                {

                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                    jsnItem.AddVariable("EstimatedShippingCostInBase", dbEstShipCostInBase.HasValue && dbEstShipCostInBase.Value > 0 ? Functions.FormatCurrency(dbEstShipCostInBase.Value, SessionManager.ClientCurrencyCode) : "");

                                }
                                else
                                {
                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                }
                            }
                            //jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode)); 

                        }
                        else
                        {
                            jsnItem.AddVariable("SupplierNo", ln.SupplierNo);
                            jsnItem.AddVariable("Supplier", ln.SupplierName);
                            jsnItem.AddVariable("IsHub", false);

                            jsnItem.AddVariable("Price", Functions.FormatCurrency(ln.Price, ln.CurrencyCode));
                            if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                            {
                                if (!SessionManager.IsPOHub.Value)
                                {
                                    jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.CurrencyNo, DateTime.Now), SessionManager.ClientCurrencyCode));
                                    //jsnItem.AddVariable("EstimatedShippingCostInBase",BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.CurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP)>0? Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.EstimatedShippingCost, ln.CurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode):"");
                                    jsnItem.AddVariable("EstimatedShippingCostInBase", dbEstShipCostInBase.HasValue && dbEstShipCostInBase.Value > 0 ? Functions.FormatCurrency(dbEstShipCostInBase.Value, SessionManager.ClientCurrencyCode) : "");
                                }
                            }
                        }
                        jsnItem.AddVariable("Part", ln.Part);
                        jsnItem.AddVariable("Product", ln.ProductName);
                        jsnItem.AddVariable("Package", ln.PackageName);
                        jsnItem.AddVariable("Qty", ln.Quantity);
                        jsnItem.AddVariable("MfrNo", ln.ManufacturerNo);
                        jsnItem.AddVariable("Mfr", ln.ManufacturerCode);
                        jsnItem.AddVariable("DC", ln.DateCode);
                        jsnItem.AddVariable("Date", Functions.FormatDate(ln.OfferStatusChangeDate));
                        jsnItem.AddVariable("By", (String.IsNullOrEmpty(ln.SalesmanName)) ? ln.OfferStatusChangeEmployeeName : ln.SalesmanName);
                        //code[00112020] put ln.Notes inside ReplaceLineBreaks function
                        jsnItem.AddVariable("Notes", Functions.ReplaceLineBreaks(ln.Notes));

                        jsnItem.AddVariable("IsClosed", ln.IsClosed);
                        jsnItem.AddVariable("IsSoCreated", ln.IsSoCreated);
                        jsnItem.AddVariable("EstimatedShippingCost", ln.EstimatedShippingCost > 0 ? Functions.FormatCurrency(ln.EstimatedShippingCost, ln.CurrencyCode) : "");
                        //get price in base currency too if it's not already


                        //if (ln.CurrencyNo != SessionManager.ClientCurrencyID)
                        //{
                        //    if (!SessionManager.IsPOHub.Value)
                        //    {
                        //        jsnItem.AddVariable("PriceInBase", Functions.FormatCurrency(BLL.Currency.ConvertValueToBaseCurrency(ln.Price, ln.ClientCurrencyNo, (ln.OriginalEntryDate.HasValue) ? ln.OriginalEntryDate.Value : ln.DLUP), SessionManager.ClientCurrencyCode));                                
                        //    }

                        //}

                        //status
                        string strStatus = "";
                        if (ln.OfferStatusNo != null) strStatus = Functions.GetGlobalResource("OfferStatus", (OfferStatus.List)ln.OfferStatusNo);
                        jsnItem.AddVariable("Status", strStatus);

                        jsnItem.AddVariable("RegionName", ln.RegionName);
                        jsnItem.AddVariable("TermsName", ln.TermsName);
                        jsnItem.AddVariable("SupplierType", ln.SupplierType);
                        //related quotes
                        JsonObject jsnQuotes = new JsonObject(true);
                        foreach (BLL.Quote qt in Quote.GetListForSourcingResult(ln.SourcingResultId))
                        {
                            JsonObject jsnQuote = new JsonObject();
                            jsnQuote.AddVariable("ID", qt.QuoteId);
                            jsnQuote.AddVariable("No", qt.QuoteNumber);
                            jsnQuotes.AddVariable(jsnQuote);
                            jsnQuote.Dispose(); jsnQuote = null;
                        }
                        jsnItem.AddVariable("Quotes", jsnQuotes);
                        jsnQuotes.Dispose(); jsnQuotes = null;

                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    jsn.AddVariable("Results", jsnItems);
                    OutputResult(jsn);
                    jsn.Dispose(); jsn = null;
                }
                catch (Exception e)
                {
                    //[007]
                    //var ai = new TelemetryClient();
                    //ai.TrackTrace("ReqSourcingResult: GetDataCopy");
                    //ai.TrackException(e);
                    new Errorlog().LogMessage("Error at GetDataCopy() in CusReqSourcingResults.ashx.cs : " + e.Message);

                    WriteError(e);
                }
            }
        }

        private void GetItem()
        {
            SourcingResult sr = SourcingResult.Get(ID);
            if (sr != null)
            {
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Part", sr.Part);
                jsn.AddVariable("DateCode", sr.DateCode);
                jsn.AddVariable("Quantity", sr.Quantity);
                jsn.AddVariable("Price", Functions.FormatCurrency(sr.Price));
                jsn.AddVariable("CurrencyNo", sr.CurrencyNo);
                jsn.AddVariable("PackageNo", sr.PackageNo);
                jsn.AddVariable("ProductNo", sr.ProductNo);
                jsn.AddVariable("Mfr", sr.ManufacturerName);
                jsn.AddVariable("MfrNo", sr.ManufacturerNo);
                jsn.AddVariable("OfferStatus", sr.OfferStatusNo);
                jsn.AddVariable("ROHS", sr.ROHS);
                jsn.AddVariable("Supplier", sr.SupplierName);
                jsn.AddVariable("SupplierNo", sr.SupplierNo);
                jsn.AddVariable("Notes", Functions.ReplaceLineBreaks(sr.Notes));
                jsn.AddVariable("ProductDescription", sr.ProductDescription);
                jsn.AddVariable("MSLLevelNo", sr.MSLLevelNo);
                jsn.AddVariable("PackageDescription", sr.PackageDescription);
                jsn.AddVariable("PartWatchMatch", sr.PartWatchMatch);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                WriteErrorDataNotFound();
            }
        }

        public void SaveEdit()
        {
            try
            {
                //update sourcing result
                //(proc also updates related Offer, where appropriate)
                bool blnOK = SourcingResult.Update(
                    ID
                    , GetFormValue_String("Part")
                    , GetFormValue_NullableInt("Manufacturer")
                    , GetFormValue_String("DateCode")
                    , GetFormValue_NullableInt("Product")
                    , GetFormValue_NullableInt("Package")
                    , GetFormValue_NullableInt("Quantity")
                    , GetFormValue_NullableDouble("Price")
                    , GetFormValue_Int("Currency")
                    , GetFormValue_NullableInt("OfferStatus")
                    , GetFormValue_NullableInt("Supplier")
                    , GetFormValue_NullableByte("ROHS")
                    , GetFormValue_String("Notes")
                    , LoginID
                    , GetFormValue_NullableInt("MSL")
                    , GetFormValue_Boolean("PartWatchMatch")
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", blnOK);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                //[007]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("ReqSourcingResult: SaveEdit");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at SaveEdit() in CusReqSourcingResults.ashx.cs : " + e.Message);

                WriteError(e);
            }
        }

        private void AddNew()
        {
            try
            {
                System.Boolean? FoundPartWatchMatct;
                System.Int32? ReqOwnerId;
                System.String ReqOwnerName;
                System.String CusReqNumber;
                System.Int32? offerID = -1;
                //add the new sourcing result
                int intNewID = BLL.SourcingResult.Insert(
                    GetFormValue_Int("CustomerRequirementID")
                    , null
                    , GetFormValue_String("Notes")
                    , GetFormValue_String("Part")
                    , GetFormValue_NullableInt("Manufacturer")
                    , GetFormValue_String("DateCode")
                    , GetFormValue_NullableInt("Product")
                    , GetFormValue_NullableInt("Package")
                    , GetFormValue_NullableInt("Quantity")
                    , GetFormValue_NullableDouble("Price")
                    , GetFormValue_NullableInt("Currency")
                    , DateTime.Now
                    , LoginID
                    , GetFormValue_NullableInt("OfferStatus")
                    , GetFormValue_NullableInt("SupplierNo")
                    , GetFormValue_NullableByte("ROHS")
                    , SessionManager.ClientID
                    , LoginID
                    , GetFormValue_NullableInt("MSL")
                    , GetFormValue_NullableBoolean("blnSupHasCurrency")
                    , out offerID
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewID > 0);
                jsn.AddVariable("NewID", intNewID);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
                #region Add Logic for matching requirement
                if (offerID > 0)
                {
                    DataTable dtMatchedReq;


                    int? Result = 0;
                    dtMatchedReq = Offer.GetPartMatchRequirementDetails(SessionManager.ClientID, offerID);
                    Result = dtMatchedReq.Rows.Count;
                    if (Result > 0)
                    {

                        WebServices servic = new WebServices();
                        string strToLoginsArray = string.Empty;
                        string strRequirementId = string.Empty;
                        for (int i = 0; i < Result; i++)
                        {
                            //strToLoginsArray = dtMatchedReq.Rows[i]["UpdatedBy"].ToString();
                            strToLoginsArray = dtMatchedReq.Rows[i]["Salesman"].ToString();
                            strRequirementId = dtMatchedReq.Rows[i]["CustomerRequirementId"].ToString();
                            servic.NotifyPartWatchMatchMessage(strToLoginsArray, "", Functions.GetGlobalResource("Messages", "PartWatchMatchMsg"), 0, dtMatchedReq.Rows[i]["EmployeeName"].ToString(), strRequirementId, dtMatchedReq.Rows[i]["CustomerRequirementNo"].ToString());
                        }

                    }



                }
                #endregion
            }
            catch (Exception e)
            {
                //[007]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("ReqSourcingResult: AddNew");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at AddNew() in CusReqSourcingResults.ashx.cs : " + e.Message);

                WriteError(e);
            }
        }
        private void DeletePartWatchMatch()
        {
            try
            {
                int Result = BLL.SourcingResult.DeletePartWatchMatch(
                    GetFormValue_String("SourcingResultIDs")
                );

                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", Result > 0);
                jsn.AddVariable("NewID", Result);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                //[007]
                //var ai = new TelemetryClient();
                //ai.TrackTrace("ReqSourcingResult: DeletePartWatchMatch");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at DeletePartWatchMatch() in CusReqSourcingResults.ashx.cs : " + e.Message);

                WriteError(e);
            }
        }
    }
}
