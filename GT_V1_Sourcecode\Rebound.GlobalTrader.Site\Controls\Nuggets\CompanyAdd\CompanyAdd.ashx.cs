/**********************************************************************************************
Marker     changed by      date         Remarks
[001]      Abhinav       02/09/20011   ESMS Ref:12 - Add new field "Company Registration No" 
[002]      Vinay         11/06/2012    This need to Add Incoterms field in company section
[003]      Vinay          09/07/2012   This need for Rebound- Invoice bulk Emailer
* **********************************************************************************************/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class CompanyAdd : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "AddNew": AddNew(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Add new purchaseOrder
		/// </summary>
        /// //[1001] code start
		public void AddNew() {
            bool? IsPohub = SessionManager.IsPOHub;
			try {
				int intNewID = BLL.Company.Insert(
					  SessionManager.ClientID
					, GetFormValue_String("CompanyName")
					, DateTime.Now
					, null
					, GetFormValue_NullableInt("Salesman")
					, null
					, GetFormValue_String("Telephone")
					, GetFormValue_String("Telephone800")
					, GetFormValue_String("Fax")
					, null
					, GetFormValue_String("EMail")
					, GetFormValue_String("URL")
					, GetFormValue_String("Notes")
                    , GetFormValue_String("VatNo")
                    , GetFormValue_NullableInt("CompanyType")
					, null
					, false
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, false
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, null
					, LoginID
                    , GetFormValue_String("CompRegNo")
                    , GetFormValue_String("CertificateNotes")
                    , GetFormValue_String("QualityNotes")
                    , IsPohub
                    , GetFormValue_String("EORINumber")
                );
                //[1001] code end

				if (intNewID > 0) {
					//add address
					int intAddressID = BLL.Address.Insert(
						GetFormValue_String("AddressName")
						, GetFormValue_String("AddressLine1")
						, GetFormValue_String("AddressLine2")
						, GetFormValue_String("AddressLine3")
						, GetFormValue_String("Town")
						, GetFormValue_String("County")
						, GetFormValue_String("State")
						, GetFormValue_NullableInt("Country")
						, GetFormValue_String("Postcode")
						, LoginID
                        , null
                        , null
					);

					//company address row
                    //[002] code start
					if (intAddressID > 0) {
						BLL.CompanyAddress.Insert(
							intNewID
							, intAddressID
							, null
							, null
							, null
							, null
                            , null 
							, LoginID
                            , null
                            , GetFormValue_String("VatNo")
                            , null
							,null
						);
					}
                    //[002] code end
					//add contact
					BLL.Contact.Insert(
						  string.Format("{0} {1}", GetFormValue_String("FirstName").Trim(), GetFormValue_String("LastName").Trim())
						, null
						, GetFormValue_String("FirstName")
						, GetFormValue_String("LastName")
						, null
						, null
						, null
						, null
						, null
						, null
						, null
						, intNewID
						, null
						, null
						, null
						, false
						, LoginID
                        //[003] code start
                        , null
                        //[003] code end
                        ,null
					);

					JsonObject jsn = new JsonObject();
					jsn.AddVariable("NewID", intNewID);
					OutputResult(jsn);
					jsn.Dispose(); jsn = null;
				} else {
					WriteErrorSQLActionFailed("Insert");
				}
			} catch (Exception e) {
                
				WriteError(e);
			}
		}
	}
}
