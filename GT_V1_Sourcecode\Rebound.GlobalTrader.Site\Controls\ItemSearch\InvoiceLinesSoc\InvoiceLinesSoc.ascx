<%@ Control Language="C#" CodeBehind="InvoiceLinesSoc.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.InvoiceLinesSoc" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlInvoiceNo" runat="server" ResourceTitle="InvoiceNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlContact" runat="server" ResourceTitle="Contact" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludePaid" runat="server" ResourceTitle="IncludePaid" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerPO" runat="server" ResourceTitle="CustomerPO" />
		<ReboundUI_FilterDataItemRow:Numerical id="ctlSalesOrderNo" runat="server" ResourceTitle="SalesOrderNo" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateInvoicedFrom" runat="server" ResourceTitle="DateInvoicedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateInvoicedTo" runat="server" ResourceTitle="DateInvoicedTo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
