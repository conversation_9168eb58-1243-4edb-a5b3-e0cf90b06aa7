///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.prototype = {

    get_ctlLocalCurrency: function() { return this._ctlLocalCurrency; }, set_ctlLocalCurrency: function(v) { if (this._ctlLocalCurrency !== v) this._ctlLocalCurrency = v; },
    get_ctlExchangeRates: function() { return this._ctlExchangeRates; }, set_ctlExchangeRates: function(v) { if (this._ctlExchangeRates !== v) this._ctlExchangeRates = v; },

    initialize: function() {

        Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlLocalCurrency) this._ctlLocalCurrency.addSelectCurrency(Function.createDelegate(this, this.ctlLocalCurrency_SelectCurrency));
        //if (this._ctlExchangeRates) this._ctlExchangeRates.addChangedData(Function.createDelegate(this, this.ctlExchangeRates_ChangedData));
        Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlLocalCurrency) this._ctlLocalCurrency.dispose();
        this._ctlLocalCurrency = null;
        this._ctlExchangeRates = null;
        Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.callBaseMethod(this, "dispose");
    },

    ctlLocalCurrency_SelectCurrency: function() {
        this._ctlExchangeRates._intCurrencyID = this._ctlLocalCurrency._intLocalCurrencyID;
        // this._ctlExchangeRates._strCurrency = this._ctlLocalCurrency._strCurrency;
        this._ctlExchangeRates.show(!this._ctlLocalCurrency._blnCurrencyIsBase);
        this._ctlExchangeRates.refresh();
        this._ctlLocalCurrency._tbl.resizeColumns();
    }
    //    
    //    ctlCurrencyRates_ChangedData: function() {
    //        this._ctlCurrency.getData();
    //    }

};

Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_LocalCurrency", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
