Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Reports");Rebound.GlobalTrader.Site.Pages.Reports=function(n){Rebound.GlobalTrader.Site.Pages.Reports.initializeBase(this,[n]);this._aryReportPanelIDs=[];this._aryReportSearchText=[];this._aryGroupNuggetIDs=[];this._aryCategoryHeaderIDs=[];this._isChecked=!1};Rebound.GlobalTrader.Site.Pages.Reports.prototype={get_ctlControlStrip:function(){return this._ctlControlStrip},set_ctlControlStrip:function(n){this._ctlControlStrip!==n&&(this._ctlControlStrip=n)},get_txtSearch:function(){return this._txtSearch},set_txtSearch:function(n){this._txtSearch!==n&&(this._txtSearch=n)},get_hypClear:function(){return this._hypClear},set_hypClear:function(n){this._hypClear!==n&&(this._hypClear=n)},get_ibtnCollapseAll:function(){return this._ibtnCollapseAll},set_ibtnCollapseAll:function(n){this._ibtnCollapseAll!==n&&(this._ibtnCollapseAll=n)},get_ibtnExpandAll:function(){return this._ibtnExpandAll},set_ibtnExpandAll:function(n){this._ibtnExpandAll!==n&&(this._ibtnExpandAll=n)},get_aryGroupNuggetIDs:function(){return this._aryGroupNuggetIDs},set_aryGroupNuggetIDs:function(n){this._aryGroupNuggetIDs!==n&&(this._aryGroupNuggetIDs=n)},get_aryCategoryHeaderIDs:function(){return this._aryCategoryHeaderIDs},set_aryCategoryHeaderIDs:function(n){this._aryCategoryHeaderIDs!==n&&(this._aryCategoryHeaderIDs=n)},get_aryReportPanelIDs:function(){return this._aryReportPanelIDs},set_aryReportPanelIDs:function(n){this._aryReportPanelIDs!==n&&(this._aryReportPanelIDs=n)},get_aryReportSearchText:function(){return this._aryReportSearchText},set_aryReportSearchText:function(n){this._aryReportSearchText!==n&&(this._aryReportSearchText=n)},get_chkIsForPOHub:function(){return this._chkIsForPOHub},set_chkIsForPOHub:function(n){this._chkIsForPOHub!==n&&(this._chkIsForPOHub=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Reports.callBaseMethod(this,"initialize");this._strCKExp=1},goInit:function(){this._txtSearch&&$addHandler(this._txtSearch,"keyup",Function.createDelegate(this,this.searchReports));this._hypClear&&$addHandler(this._hypClear,"click",Function.createDelegate(this,this.clearSearch));this._ibtnExpandAll&&$R_IBTN.addClick(this._ibtnExpandAll,Function.createDelegate(this,this.expandNuggets));this._ibtnCollapseAll&&$R_IBTN.addClick(this._ibtnCollapseAll,Function.createDelegate(this,this.collapseNuggets));this._chkIsForPOHub&&this._chkIsForPOHub.addClick(Function.createDelegate(this,this.CheckBox));this._chkIsForPOHub&&this.showOnlyHubReport(this._chkIsForPOHub._blnChecked)},dispose:function(){this.isDisposed||(this._txtSearch&&$clearHandlers(this._txtSearch),this._hypClear&&$clearHandlers(this._hypClear),this._ibtnExpandAll&&$R_IBTN.clearHandlers(this._ibtnExpandAll),this._ibtnCollapseAll&&$R_IBTN.clearHandlers(this._ibtnCollapseAll),this._ctlControlStrip&&this._ctlControlStrip.dispose(),this._ctlControlStrip=null,this._txtSearch=null,this._hypClear=null,this._ibtnCollapseAll=null,this._ibtnExpandAll=null,this._aryGroupNuggetIDs=null,this._aryCategoryHeaderIDs=null,this._aryReportPanelIDs=null,this._aryReportSearchText=null,_chkIsForPOHub&&(this._chkIsForPOHub=null),Rebound.GlobalTrader.Site.Pages.Reports.callBaseMethod(this,"dispose"))},CheckBox:function(){this._isChecked=this._chkIsForPOHub._blnChecked?!0:!1;$R_FN.setCookie("isChecked",this._isChecked,this._strCKExp);this.showOnlyHubReport(this._isChecked)},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},searchReports:function(){var c=this._txtSearch.value.trim().toUpperCase(),r,u,n,t,i,h,f,o,e,s;if(this.showClear(),this.setNuggetsRolledUpState(!1),c.length==0)this.resetReports();else{for(r=[],u=[],n=0,t=this._aryReportPanelIDs.length;n<t;n++)i=$get(this._aryReportPanelIDs[n]),i&&(h=this._aryReportSearchText[n].toUpperCase().indexOf(c)>=0,$R_FN.showElement(i,h),h&&(this.addToCount(r,i.getAttribute("GroupID")),this.addToCount(u,i.getAttribute("CategoryID")))),i=null;for(n=0,t=this._aryCategoryHeaderIDs.length;n<t;n++)f=$get(this._aryCategoryHeaderIDs[n]),f&&(o=Number.parseInvariant(f.getAttribute("CategoryID")),u[o]||(u[o]=0),$R_FN.showElement(f,u[o]>0)),f=null;for(n=0,t=this._aryGroupNuggetIDs.length;n<t;n++)e=$find(this._aryGroupNuggetIDs[n]),e&&(s=Number.parseInvariant(e._element.getAttribute("GroupID")),r[s]||(r[s]=0),$R_FN.showElement(e._element,r[s]>0)),e=null}},expandNuggets:function(){this.setNuggetsRolledUpState(!1)},collapseNuggets:function(){this.setNuggetsRolledUpState(!0)},setNuggetsRolledUpState:function(n){for(var t,i=0,r=this._aryGroupNuggetIDs.length;i<r;i++)t=$find(this._aryGroupNuggetIDs[i]),t&&t._blnIsRolledUp!=n&&t.toggleRollUp(),t=null;$R_IBTN.showButton(this._ibtnExpandAll,n);$R_IBTN.showButton(this._ibtnCollapseAll,!n)},resetReports:function(){for(var i,r,u,n=0,t=this._aryReportPanelIDs.length;n<t;n++)i=$get(this._aryReportPanelIDs[n]),i&&$R_FN.showElement(i,!0),i=null;for(n=0,t=this._aryCategoryHeaderIDs.length;n<t;n++)r=$get(this._aryCategoryHeaderIDs[n]),r&&$R_FN.showElement(r,!0),r=null;for(n=0,t=this._aryGroupNuggetIDs.length;n<t;n++)u=$find(this._aryGroupNuggetIDs[n]),u&&$R_FN.showElement(u._element,!0),u=null},addToCount:function(n,t){var i=Number.parseInvariant(t);n[i]||(n[i]=0);n[i]+=1},clearSearch:function(){this._txtSearch.value="";this.resetReports();this.showClear()},showClear:function(){$R_FN.showElement(this._hypClear,this._txtSearch.value.trim().length>0)},showOnlyHubReport:function(n){for(var t,i=0,r=this._aryReportPanelIDs.length;i<r;i++)t=$get(this._aryReportPanelIDs[i]),t&&t.getAttribute("class")!="reportListItemHUB"&&$R_FN.showElement(t,!n),t=null}};Rebound.GlobalTrader.Site.Pages.Reports.registerClass("Rebound.GlobalTrader.Site.Pages.Reports",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);