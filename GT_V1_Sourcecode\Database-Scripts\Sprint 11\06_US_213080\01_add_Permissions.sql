﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213080]     An.TranTan		 27-Sep-2024		CREATE		Add permission allow view list - import - view detail prospective offers
===========================================================================================  
*/
DECLARE @CurrentSortOrder INT;
SELECT @CurrentSortOrder = ISNULL(MAX(DisplaySortOrder),0) FROM tbSecurityFunction WHERE SiteSectionNo = 8; --Utility

IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Utility_ProspectiveOffer'
		OR SecurityFunctionId = 7000011
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     7000011,                -- SecurityFunctionId
    'Utility_ProspectiveOffer',            -- FunctionName
    'Allow viewing Utility Prospective Offer Import and List section', -- Description
    NULL,                      -- SitePageNo
    8,                        -- SiteSectionNo
    NULL,                     -- ReportNo (NULL because it's not related to a report)
    NULL,                     -- UpdatedBy (ID of the user who updated this function)
    GETDATE(),                -- DLUP (current date and time)
    0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 1                        -- DisplaySortOrder
);
END

IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Utility_ProspectiveOfferDetail'
		OR SecurityFunctionId = 7000012
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     7000012,                -- SecurityFunctionId
    'Utility_ProspectiveOfferDetail',            -- FunctionName
    'Allow viewing Utility Prospective Offer Details', -- Description
    NULL,                      -- SitePageNo
    8,                        -- SiteSectionNo
    NULL,                     -- ReportNo (NULL because it's not related to a report)
    NULL,                     -- UpdatedBy (ID of the user who updated this function)
    GETDATE(),                -- DLUP (current date and time)
    0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 2                        -- DisplaySortOrder
);
END
