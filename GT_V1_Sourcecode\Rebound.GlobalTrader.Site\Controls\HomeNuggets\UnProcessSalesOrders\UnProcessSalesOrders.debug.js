///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.prototype = {

    get_pnlOpen: function() { return this._pnlOpen; }, set_pnlOpen: function(value) { if (this._pnlOpen !== value) this._pnlOpen = value; },
    get_pnlOverdue: function() { return this._pnlOverdue; }, set_pnlOverdue: function(value) { if (this._pnlOverdue !== value) this._pnlOverdue = value; },
    get_tblOpen: function() { return this._tblOpen; }, set_tblOpen: function(value) { if (this._tblOpen !== value) this._tblOpen = value; },
    get_tblOverdue: function() { return this._tblOverdue; }, set_tblOverdue: function(value) { if (this._tblOverdue !== value) this._tblOverdue = value; },
    get_pnlMore: function() { return this._pnlMore; }, set_pnlMore: function(value) { if (this._pnlMore !== value) this._pnlMore = value; },
    //get_lnkMore: function() { return this._lnkMore; }, set_lnkMore: function(value) { if (this._lnkMore !== value) this._lnkMore = value; },

    get_myLoginID: function() { return this.myLoginID; }, set_myLoginID: function(value) { if (this.myLoginID !== value) this.myLoginID = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.callBaseMethod(this, "initialize");
         this.getData();
         this.addRefreshEvent(Function.createDelegate(this, this.getData));
        
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._tblOpen) this._tblOpen.dispose();
        if (this._tblOverdue) this._tblOverdue.dispose();
        this._pnlOpen = null;
        this._pnlOverdue = null;
        this._tblOpen = null;
        this._tblOverdue = null;
        this._pnlMore = null;

        Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.callBaseMethod(this, "dispose");
    },

    setupLoadingState: function() {
        $R_FN.showElement(this._pnlMore, false);
        $R_FN.showElement(this._pnlOpen, false);
        $R_FN.showElement(this._pnlOverdue, false);
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.callBaseMethod(this, "setupLoadingState");
    },

    showNoData: function(bln) {
        this.showContent(true);
        $R_FN.showElement(this._pnlNoData, bln);
    },

    getData: function() {
     
        this.setupLoadingState();
//        if (this._intLoginID_Other > 0) {
//            this._lnkMore.href = $RGT_gotoURL_SalesOrderBrowse(this._intLoginID_Other);
//            //"Ord_SOBrowse.aspx?bss=" + true + "&sp=" + this._intLoginID_Other;
//        }
//        else{
//            this._lnkMore.href = $RGT_gotoURL_SalesOrderBrowse(this.myLoginID);
//        }



        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/HomeNuggets/UnProcessSalesOrders");
        obj.set_DataObject("UnProcessSalesOrders");
        obj.set_DataAction("GetData");
        obj.addParameter("rowcount", this._intRowCount);
        obj.addParameter("OtherLoginID", this._intLoginID_Other);
        obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
        obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
        obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataComplete: function(args) {
   
        this.showNoData(args._result.Count == 0);
        $R_FN.showElement(this._pnlMore, true);
        var result = args._result;
        var aryData, row;
        //open
        this._tblOpen.clearTable();
        for (var i = 0; i < result.OpenSO.length; i++) {
            row = result.OpenSO[i];
            aryData = [
				$RGT_nubButton_SalesOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Due
				];
            this._tblOpen.addRow(aryData, null);
            row = null;
        }
        $R_FN.showElement(this._pnlOpen, result.OpenSO.length > 0);
        
        this.hideLoading();
    }

};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
