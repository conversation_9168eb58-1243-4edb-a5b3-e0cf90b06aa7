﻿/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[US-217008]         Cuong DoX	        15-Nov-2024         Alter           add SupportTeamMemberNo  
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_update_InternalPurchaseOrder]                     
--*****************************************************************************************                    
                
--*****************************************************************************************                    
@InternalPurchaseOrderId  int ,                    
@CurrencyNo    int ,                    
@ContactNo    int ,                    
@Buyer     int ,                    
@DivisionNo    int ,                    
@UpdatedBy    int    = Null ,    
@DivisionHeaderNo int =null,                   
@RowsAffected   int = NULL Output,                    
@SupportTeamMemberNo int = null             
AS                    
BEGIN      
                    
 UPDATE dbo.tbInternalPurchaseOrder                    
 SET           
   CurrencyNo   = @CurrencyNo                     
 --, ContactNo   = @ContactNo                     
 , Buyer    = @Buyer                     
 , DivisionNo   = @DivisionNo                    
 , UpdatedBy   = @UpdatedBy                    
 , DLUP    = current_timestamp        
 , DivisionHeaderNo=@DivisionHeaderNo      
 ,SupportTeamMemberNo = @SupportTeamMemberNo
 WHERE InternalPurchaseOrderId  = @InternalPurchaseOrderId            
          
 SELECT @RowsAffected  = @@ROWCOUNT       
      
END      
      
    
  
  