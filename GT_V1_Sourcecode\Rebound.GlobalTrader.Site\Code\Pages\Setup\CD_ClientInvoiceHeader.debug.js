///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.prototype = {

	get_ctlClientInvoiceHeader: function() { return this._ctlClientInvoiceHeader; }, 	set_ctlClientInvoiceHeader: function(v) { if (this._ctlClientInvoiceHeader !== v)  this._ctlClientInvoiceHeader = v; }, 
	//get_ctlClientInvoiceHeaderMembers: function() { return this._ctlClientInvoiceHeaderMembers; }, 	set_ctlClientInvoiceHeaderMembers: function(v) { if (this._ctlClientInvoiceHeaderMembers !== v)  this._ctlClientInvoiceHeaderMembers = v; }, 
	get_ctlClientInvoiceHeaderImage: function () { return this._ctlClientInvoiceHeaderImage; }, set_ctlClientInvoiceHeaderImage: function (v) { if (this._ctlClientInvoiceHeaderImage !== v) this._ctlClientInvoiceHeaderImage = v; },

	initialize: function () {
		debugger
		Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		this._ctlClientInvoiceHeader.addSelectClientInvoiceHeader(Function.createDelegate(this, this.ctlClientInvoiceHeader_SelectClientInvoiceHeader));
		Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlClientInvoiceHeader) this._ctlClientInvoiceHeader.dispose();
		//if (this._ctlClientInvoiceHeaderMembers) this._ctlClientInvoiceHeaderMembers.dispose();
		if (this._ctlClientInvoiceHeaderImage) this._ctlClientInvoiceHeaderImage.dispose();
		this._ctlClientInvoiceHeader = null;
		//this._ctlClientInvoiceHeaderMembers = null;
		this._ctlClientInvoiceHeaderImage = null;
		Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.callBaseMethod(this, "dispose");
	},
	
	ctlClientInvoiceHeader_SelectClientInvoiceHeader: function() {
		//this._ctlClientInvoiceHeaderMembers._intClientInvoiceHeaderID = this._ctlClientInvoiceHeader._intClientInvoiceHeaderID;
		//this._ctlClientInvoiceHeaderMembers.refresh();
		this._ctlClientInvoiceHeaderImage._intClientInvoiceHeaderID = this._ctlClientInvoiceHeader._intClientInvoiceHeaderID;
		this._ctlClientInvoiceHeaderImage.refresh();
		this._ctlClientInvoiceHeader._tbl.resizeColumns();
		this.showNuggets(true);
	},
	
	showNuggets: function(bln) {
		//this._ctlClientInvoiceHeaderMembers.show(bln);
		this._ctlClientInvoiceHeaderImage.show(bln);
	}
	
};

Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.CD_ClientInvoiceHeader", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
