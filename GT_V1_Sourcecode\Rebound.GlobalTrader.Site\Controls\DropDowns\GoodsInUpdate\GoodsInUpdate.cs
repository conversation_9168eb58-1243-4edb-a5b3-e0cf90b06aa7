﻿//-----------------------------------------------------------------------------------------
// RP 12.10.2009:
// - newly created from retrofitting changes from v3.0.34 (task 322)
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class GoodsInUpdate : Base {

		protected override void OnInit(EventArgs e) {
			CanAddTo = false;
			InitialValue = ((int)GoodsInUpdateList.ReceiptLinkedStockAndShipments).ToString();
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
			SetDropDownType("GoodsInUpdate");
			AddScriptReference("Controls.DropDowns.GoodsInUpdate.GoodsInUpdate");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.GoodsInUpdate", ClientID);
			base.OnLoad(e);
		}

		public enum GoodsInUpdateList {
			ReceiptOnly = 1,
			ReceiptAndLinkedStock = 2,
			ReceiptLinkedStockAndShipments = 3
		}

	}
}