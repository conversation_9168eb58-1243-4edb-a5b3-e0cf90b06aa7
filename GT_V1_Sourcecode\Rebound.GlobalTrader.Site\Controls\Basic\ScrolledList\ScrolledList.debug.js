///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//
// RP 08.12.2009:
// - New control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.ScrolledList = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ScrolledList.initializeBase(this, [element]);
	this._intNumberOfItems = 0;
	this._intCurrentColumn = 1;
};

Rebound.GlobalTrader.Site.Controls.ScrolledList.prototype = {

	get_intNumberOfColumns: function() { return this._intNumberOfColumns; }, 	set_intNumberOfColumns: function(v) { if (this._intNumberOfColumns !== v)  this._intNumberOfColumns = v; }, 
	get_td1: function() { return this._td1; }, 	set_td1: function(v) { if (this._td1 !== v)  this._td1 = v; }, 
	get_td2: function() { return this._td2; }, 	set_td2: function(v) { if (this._td2 !== v)  this._td2 = v; }, 
	get_td3: function() { return this._td3; }, 	set_td3: function(v) { if (this._td3 !== v)  this._td3 = v; }, 
	get_td4: function() { return this._td4; }, 	set_td4: function(v) { if (this._td4 !== v)  this._td4 = v; }, 
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ScrolledList.callBaseMethod(this, "initialize");
		this.clear();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._intNumberOfItems = null;
		this._intCurrentColumn = null;
		this._td1 = null;
		this._td2 = null;
		this._td3 = null;
		this._td4 = null;
		Rebound.GlobalTrader.Site.Controls.ScrolledList.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	addItem: function(strItem) {
		if (this._intCurrentColumn > this._intNumberOfColumns) this._intCurrentColumn = 1;
		var td = eval("this._td" + this._intCurrentColumn);
		if (td) {
			strItem = String.format("<div class=\"dataItem\">{0}</div>", strItem);
			if (td.innerHTML != "&nbsp;") strItem = String.format("{0}{1}", td.innerHTML, strItem);
			$R_FN.setInnerHTML(td, strItem);
			this._intNumberOfItems += 1;
			this._intCurrentColumn += 1; 
			td = null;
		}
	},
	
	clear: function() {
		if (this._td1) $R_FN.setInnerHTML(this._td1, "&nbsp;");
		if (this._td2) $R_FN.setInnerHTML(this._td2, "&nbsp;");
		if (this._td3) $R_FN.setInnerHTML(this._td3, "&nbsp;");
		if (this._td4) $R_FN.setInnerHTML(this._td4, "&nbsp;");
		this._intNumberOfItems = 0;
		this._intCurrentColumn = 1;
	}
	
};

Rebound.GlobalTrader.Site.Controls.ScrolledList.registerClass("Rebound.GlobalTrader.Site.Controls.ScrolledList", Sys.UI.Control, Sys.IDisposable);