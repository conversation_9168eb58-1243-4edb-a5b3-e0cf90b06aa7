Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.initializeBase(this,[n]);this._intClientInvoiceID=-1;this._strClientInvoiceNumber=-1;this._intInvoiceClientNo=-1;this._isPOHUb=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.prototype={get_intClientInvoiceID:function(){return this._intClientInvoiceID},set_intClientInvoiceID:function(n){this._intClientInvoiceID!==n&&(this._intClientInvoiceID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnNotify:function(){return this._ibtnNotify},set_ibtnNotify:function(n){this._ibtnNotify!==n&&(this._ibtnNotify=n)},get_blnCanEditMainInfo:function(){return this._blnCanEditMainInfo},set_blnCanEditMainInfo:function(n){this._blnCanEditMainInfo!==n&&(this._blnCanEditMainInfo=n)},get_blnCanEditURNNumber:function(){return this._blnCanEditURNNumber},set_blnCanEditURNNumber:function(n){this._blnCanEditURNNumber!==n&&(this._blnCanEditURNNumber=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit._intClientInvoiceID=this._intClientInvoiceID,this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnNotify&&($R_IBTN.addClick(this._ibtnNotify,Function.createDelegate(this,this.showNotifyForm)),this._frmNotify=$find(this._aryFormIDs[1]),this._frmNotify.addCancel(Function.createDelegate(this,this.cancelNotifyForm)),this._frmNotify.addSaveComplete(Function.createDelegate(this,this.saveNotifyComplete)),this._frmNotify.addNotConfirmed(Function.createDelegate(this,this.hideNotifyForm)));this.getData()},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnNotify&&$R_IBTN.clearHandlers(this._ibtnNotify),this._frmEdit&&this._frmEdit.dispose(),this._frmNotify&&this._frmNotify.dispose(),this._ibtnEdit=null,this._ibtnNotify=null,this._frmEdit=null,this._frmNotify=null,this._intClientInvoiceID=null,this._strClientInvoiceNumber=null,this._blnCanEditMainInfo=null,this._blnCanEditURNNumber=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();this.enableButtons(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");n.set_DataObject("ClientInvoiceMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intClientInvoiceID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result;this.setFieldValue("ctlClientInvoice",$R_FN.setCleanTextValue(t.ClientInvoiceNumber));this.setFieldValue("ctlInvoiceDate",t.ClientInvoiceDate);this.setFieldValue("ctlCurrency",t.CurrencyCode);this.setFieldValue("ctlInvoiceAmount",t.InvoiceAmount);this.setFieldValue("ctlGoodsValue",t.GoodsValue);this.setFieldValue("ctlTax",t.Tax);this.setFieldValue("ctlTaxName",t.TaxName);this.setFieldValue("ctlPOOrder",t.Receiver);this.setFieldValue("ctlDeliveryCharge",t.DeliveryCharge);this.setFieldValue("ctlBankFee",t.BankFee);this.setFieldValue("ctlCreditCardFee",t.CreditCardFee);this.setFieldValue("ctlNotes",t.Notes);this.setFieldValue("ctlPOOrder",$RGT_nubButton_PurchaseOrder(t.PONo,t.PO));t.InternalPurchaseOrderNo>0?this.setFieldValue("ctlInternalPurchaseOrder",$RGT_nubButton_InternalPurchaseOrder(t.InternalPurchaseOrderNo,t.InternalPurchaseOrderNumber)):this.showField("ctlInternalPurchaseOrder",!1);this.setFieldValue("hidSupplier",t.SupplierName);this.setFieldValue("hidCompnayNo",t.CompanyNo);this.setFieldValue("hidCurrency",t.CurrencyNo);this.setFieldValue("hidInvoiceAmount",t.InvoiceAmountValue);this.setFieldValue("hidGoodsInValue",t.GoodsValueValue);this.setFieldValue("hidTax",t.TaxValue);this.setFieldValue("hidDeliveryCharges",t.DeliveryChargeValue);this.setFieldValue("hidBankFee",t.BankFeeValue);this.setFieldValue("hidCreditCardFee",t.CreditCardFeeValue);this.setFieldValue("hidTaxId",t.TaxNo);this.setFieldValue("ctlSecondRef",$R_FN.setCleanTextValue(t.SecondRef));this.setFieldValue("ctlNarrative",$R_FN.setCleanTextValue(t.Narrative));this._strClientInvoiceNumber=t.ClientInvoiceNumber;this._intInvoiceClientNo=t.InvoiceClientNo;this._isPOHUb=t.IsPOHub;this.setDLUP(t.DLUP);this.enableButtons(!0);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableButtons:function(n){n?(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,n&&this._blnCanEditMainInfo&&this._isPOHUb),this._ibtnNotify&&$R_IBTN.enableButton(this._ibtnNotify,!0)):(this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnNotify&&$R_IBTN.enableButton(this._ibtnNotify,!1))},showEditForm:function(){this._frmEdit.setFieldValue("ctlSupplier",this.getFieldValue("hidSupplier"));this._frmEdit.setFieldValue("ctlClientInvoice",this.getFieldValue("ctlClientInvoice"));this._frmEdit.setFieldValue("ctlInvoiceDate",this.getFieldValue("ctlInvoiceDate"));this._frmEdit.setFieldValue("ctlCurrency",this.getFieldValue("hidCurrency"));this._frmEdit.setFieldValue("ctlInvoiceAmount",this.getFieldValue("hidInvoiceAmount"));this._frmEdit.setFieldValue("ctlGoodsValue",this.getFieldValue("hidGoodsInValue"));this._frmEdit.setFieldValue("ctlTax",this.getFieldValue("hidTax"));this._frmEdit.setFieldValue("ctlDeliveryCharge",this.getFieldValue("hidDeliveryCharges"));this._frmEdit.setFieldValue("ctlBankFee",this.getFieldValue("hidBankFee"));this._frmEdit.setFieldValue("ctlCreditCardFee",this.getFieldValue("hidCreditCardFee"));this._frmEdit.setFieldValue("ctlNotes",$R_FN.setCleanTextValue(this.getFieldValue("ctlNotes")));this._frmEdit.setFieldValue("ctlddlTax",this.getFieldValue("hidTaxId"));this._frmEdit.setFieldValue("ctlSecondRef",$R_FN.setCleanTextValue(this.getFieldValue("ctlSecondRef")));this._frmEdit.setFieldValue("ctlNarrative",$R_FN.setCleanTextValue(this.getFieldValue("ctlNarrative")));var n=this.getFieldValue("ctlCurrency");this._frmEdit._TaxNo=this.getFieldValue("hidTaxId");this._frmEdit._CurrencyCode=n;$R_FN.setInnerHTML(this._frmEdit._lblCurrency_InvoiceAmount,n);$R_FN.setInnerHTML(this._frmEdit._lblCurrency_GoodsInValue,n);$R_FN.setInnerHTML(this._frmEdit._lblCurrency_Tax,n);$R_FN.setInnerHTML(this._frmEdit._lblCurrency_DeliveryCharge,n);$R_FN.setInnerHTML(this._frmEdit._lblCurrency_BankFee,n);$R_FN.setInnerHTML(this._frmEdit._lblCurrency_CreditCardFee,n);this.showForm(this._frmEdit,!0);n=null},hideEditForm:function(){this.showForm(this._frmEdit,!1)},cancelEdit:function(){this.hideEditForm()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showNotifyForm:function(){this._frmNotify._intClientInvoiceID=this._intClientInvoiceID;this._frmNotify._intCompanyID=this.getFieldValue("hidCompnayNo");this._frmNotify._strClientInvoiceNumber=this._strClientInvoiceNumber;this.showForm(this._frmNotify,!0)},hideNotifyForm:function(){this.showForm(this._frmNotify,!1)},cancelNotifyForm:function(){this.showForm(this._frmNotify,!1);this.showContent(!0)},saveNotifyComplete:function(){this.showForm(this._frmNotify,!1);this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully)}};Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);