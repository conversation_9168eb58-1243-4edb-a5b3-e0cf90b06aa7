using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
    public partial class EPRNotify : Base
    {

		#region Locals

		protected PlaceHolder plhAddNotAllowed;
		protected IconButton ibtnAdd;

		#endregion

		#region Properties

        private int _intEPRID;
        public int EPRID
        {
            get { return _intEPRID; }
            set { _intEPRID = value; }
        }

        private bool _blnCanNotify = true;
        public bool CanNotify {
            get { return _blnCanNotify; }
            set { _blnCanNotify = value; }
        }

      

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            AddScriptReference("Controls.Nuggets.EPRNotify.EPRNotify.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "EPRNotify");
			_intEPRID = _objQSManager.EPRId;
			ibtnAdd = FindIconButton("ibtnAdd");
			plhAddNotAllowed = (PlaceHolder)FindContentControl("plhAddNotAllowed");
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
            plhAddNotAllowed.Visible = !_blnCanNotify;
            ibtnAdd.Visible = _blnCanNotify;
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.EPRNotify", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnAdd", ibtnAdd.ClientID);
        }
	}
}
