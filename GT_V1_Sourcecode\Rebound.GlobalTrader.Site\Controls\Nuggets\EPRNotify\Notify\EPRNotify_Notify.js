Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify=function(n){Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.prototype={get_intEPRID:function(){return this._intEPRID},set_intEPRID:function(n){this._intEPRID!==n&&(this._intEPRID=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_strMessageText:function(){return this._strMessageText},set_strMessageText:function(n){this._strMessageText!==n&&(this._strMessageText=n)},get_strEPRNo:function(){return this._strEPRNo},set_strEPRNo:function(n){this._strEPRNo!==n&&(this._strEPRNo=n)},get_intPO:function(){return this._intPO},set_intPO:function(n){this._intPO!==n&&(this._intPO=n)},get_intPONumber:function(){return this._intPONumber},set_intPONumber:function(n){this._intPONumber!==n&&(this._intPONumber=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addCancel(Function.createDelegate(this,this.cancelClicked))},formShown:function(){this._blnFirstTimeShown&&($R_IBTN.addClick(this._ibtnSend,Function.createDelegate(this,this.sendMail)),$R_IBTN.addClick(this._ibtnSend_Footer,Function.createDelegate(this,this.sendMail)),this._ctlMail=$find(this.getField("ctlSendMailMessage").ID),this._ctlMail._ctlRelatedForm=this,this.getMessageTextComplete(this._strMessageText))},dispose:function(){this.isDisposed||(this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlMail&&this._ctlMail.dispose(),this._ctlMail=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._intPO=null,this._strEPRNo=null,this._strMessageText=null,this._intEPRID=null,this._intPONumber=null,Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.callBaseMethod(this,"dispose"))},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyNPR(this._intEPRID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject(String.format($R_RES.NotifyEPR,this._strEPRNo))},sendMail:function(){this.validateForm()&&(this.showLoading(!0),this.enableButton(!1),Rebound.GlobalTrader.Site.WebServices.NotifyEPRMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),"",this._intEPRID,this._intPO,$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames,"/"),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupNames,"/"),this._intPONumber,Function.createDelegate(this,this.sendMailComplete)))},validateForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMailComplete:function(){this.showLoading(!1);this.showSavedOK(!0);location.href=$RGT_gotoURL_PurchaseOrder(this._intPO);this.enableButton(!0)},cancelClicked:function(){this._intPO<=0?$R_FN.navigateBack():location.href=$RGT_gotoURL_PurchaseOrder(this._intPO)},enableButton:function(n){$R_IBTN.enableButton(this._ibtnSend,n);$R_IBTN.enableButton(this._ibtnSend_Footer,n)}};Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);