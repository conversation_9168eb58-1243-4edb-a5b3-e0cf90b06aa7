//--------------------------------------------------------------------------------------------------------
// RP 04.12.2009:
// - add TooltipResource Property
//--------------------------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	[DefaultProperty("")]
	[ToolboxData("<{0}:FormField runat=server></{0}:FormField>")]
	public class FormField : TableRow, INamingContainer {

		#region Locals

		//protected ScriptManager _sm;
		private TableCell _tdTitle;
		private TableCell _tdItem;
		private PlaceHolder _plhFieldControls;
		private Panel _pnlFieldControls;
		private Panel _pnlExplain;
		private Panel _pnlMessages;
		private PlaceHolder _plhMessages;
		private Panel _pnlLoading;

		#endregion

		#region Properties
		/// <summary>
		/// Client ID of the actual field control (textbox, checkbox etc)
		/// </summary>
		private string _strFieldID;
		public string FieldID {
			get { return _strFieldID; }
			set { _strFieldID = value; }
		}

		/// <summary>
		/// control reference to the field control
		/// </summary>
		private Control _ctlFieldControl;
		public Control FieldControl {
			get { return _ctlFieldControl; }
		}

		/// <summary>
		/// Field container
		/// </summary>
		private ITemplate _tmpFieldTemplate = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		public ITemplate Field {
			get { return _tmpFieldTemplate; }
			set { _tmpFieldTemplate = value; }
		}

		/// <summary>
		/// Error/warning messages container
		/// </summary>
		private ArrayList _lstMessages = new ArrayList();
		public ArrayList Messages {
			get { return _lstMessages; }
			set { _lstMessages = value; }
		}

		/// <summary>
		/// Is this a required field?
		/// </summary>
		private bool _blnIsRequiredField = false;
		public bool IsRequiredField {
			get { return _blnIsRequiredField; }
			set { _blnIsRequiredField = value; }
		}

		/// <summary>
		/// Is this going to display as a required field (but not auto validate as one)?
		/// </summary>
		private bool _blnDisplayRequiredFieldMarkerOnly = false;
		public bool DisplayRequiredFieldMarkerOnly {
			get { return _blnDisplayRequiredFieldMarkerOnly; }
			set { _blnDisplayRequiredFieldMarkerOnly = value; }
		}

		/// <summary>
		/// Check if this is a valid email address
		/// </summary>
		private bool _blnCheckForValidEmail = false;
		public bool CheckForValidEmail {
			get { return _blnCheckForValidEmail; }
			set { _blnCheckForValidEmail = value; }
		}


		/// <summary>
		/// Check if this is a valid email address
		/// </summary>
		private bool _blnCheckForValidURL;
		public bool CheckForValidURL {
			get { return _blnCheckForValidURL; }
			set { _blnCheckForValidURL = value; }
		}

		/// <summary>
		/// Should quick help be shown?
		/// </summary>
		private bool _blnShowQuickHelp;
		public bool ShowQuickHelp {
			get { return _blnShowQuickHelp; }
			set { _blnShowQuickHelp = value; }
		}

		/// <summary>
		/// Title from the FormFields resx file
		/// </summary>
		private string _strResourceTitle = "";
		public string ResourceTitle {
			get { return _strResourceTitle; }
			set { _strResourceTitle = value; }
		}

		/// <summary>
		/// Resource for the field's Explanation from FormFieldsExplain resx file
		/// </summary>
		private string _strExplanationResource = "";
		public string ExplanationResource {
			get { return _strExplanationResource; }
			set { _strExplanationResource = value; }
		}

		/// <summary>
		/// Default value
		/// </summary>
		private string _objDefaultValue;
		public string DefaultValue {
			get { return _objDefaultValue; }
			set { _objDefaultValue = value; }
		}


		/// <summary>
		/// Is the field numeric (and thus should have it's value checked as a valid numeric?)
		/// </summary>
		private bool _blnIsNumeric = false;
		public bool IsNumeric {
			get { return _blnIsNumeric; }
		}

		private FormFieldControlType _enmFieldType;
		public FormFieldControlType FieldType {
			get { return _enmFieldType; }
			set { _enmFieldType = value; }
		}

		/// <summary>
		/// Tooltip resource from the FormFieldsExplain.resx file
		/// </summary>
		private string _strTooltipResource = "";
		public string TooltipResource {
			get { return _strTooltipResource; }
			set { _strTooltipResource = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			EnsureChildControls();
			base.OnInit(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {

			Attributes["titleResource"] = _strResourceTitle;
			if (_strTooltipResource != "") ToolTip = Functions.GetGlobalResource("FormFieldsExplain", _strTooltipResource);

			//title cell
			_tdTitle = new TableCell();
			_tdTitle.ID = "tdTitle";
			_tdTitle.CssClass = "title";
			Cells.Add(_tdTitle);
			if (_strResourceTitle != "") {
				ControlBuilders.CreateLiteralInsideParent(_tdTitle, Functions.GetGlobalResource("FormFields", _strResourceTitle));
				if (IsRequiredField || DisplayRequiredFieldMarkerOnly) ControlBuilders.CreateLabelInsideParent(_tdTitle, "requiredField", "*");
			}

			//item
			_tdItem = new TableCell();
			_tdItem.ID = "tdItem";
			_tdItem.CssClass = "item";
			Cells.Add(_tdItem);
			_pnlFieldControls = ControlBuilders.CreatePanelInsideParent(_tdItem);
			_pnlFieldControls.ID = "pnlFieldControls";
			_plhFieldControls = ControlBuilders.CreatePlaceHolderInsideParent(_pnlFieldControls);

			//populate fields
			if (_tmpFieldTemplate != null) {
				Container lncContainer = new Container();
				_tmpFieldTemplate.InstantiateIn(lncContainer);
				_plhFieldControls.Controls.Add(lncContainer);
				SetupFieldControl();
			}

			//messages
			_pnlMessages = ControlBuilders.CreatePanelInsideParent(_pnlFieldControls, "formMessages");
			_pnlMessages.ID = "pnlMessages";
			Functions.SetCSSVisibility(_pnlMessages, false);
			_plhMessages = ControlBuilders.CreatePlaceHolderInsideParent(_pnlMessages);

			//explain
			_pnlExplain = ControlBuilders.CreatePanelInsideParent(_pnlFieldControls, "formExplain");
			_pnlExplain.ID = "pnlExplain";
			Functions.SetCSSVisibility(_pnlExplain, false);
			if (_strExplanationResource != "") ControlBuilders.CreateLabelInsideParent(_pnlExplain, "", Functions.GetGlobalResource("FormFieldsExplain", _strExplanationResource));

			//loading
			_pnlLoading = ControlBuilders.CreatePanelInsideParent(_tdItem, "formFieldLoading");
			_pnlLoading.ID = "pnlLoading";
			Functions.SetCSSVisibility(_pnlLoading, false);

			//check text box modes and set flags
			if (_enmFieldType == FormFieldControlType.TextBox) {
				if (_ctlFieldControl is ReboundTextBox) {
					ReboundTextBox.TextBoxModeList enmMode = ((ReboundTextBox)_ctlFieldControl).TextBoxMode;
					_blnIsNumeric = (enmMode == ReboundTextBox.TextBoxModeList.Currency || enmMode == ReboundTextBox.TextBoxModeList.Numeric);
				}
			}

			base.CreateChildControls();
		}


		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			for (int i = 0; i < _lstMessages.Count; i++) {
				Panel pnl = ControlBuilders.CreatePanelInsideParent(_pnlMessages);
				ControlBuilders.CreateLiteralInsideParent(pnl, _lstMessages[i].ToString());
				pnl.Dispose(); pnl = null;
				_pnlMessages.CssClass = "formMessages";
			}
			base.Render(writer);
		}

		#endregion

		private void SetupFieldControl() {
			_ctlFieldControl = (Control)Rebound.GlobalTrader.Site.Functions.FindControlRecursive(_plhFieldControls, _strFieldID);
			if (_ctlFieldControl == null) throw new Exception(String.Format("Control with ID '{0}' is not found in FormField '{1}'", _strFieldID, ID));

			//set field control type (default to textbox)
			_enmFieldType = FormFieldControlType.TextBox;
			if (_ctlFieldControl is ImageCheckBox) _enmFieldType = FormFieldControlType.CheckBox;
			if (_ctlFieldControl is DropDowns.Base) _enmFieldType = FormFieldControlType.DropDown;
			if (_ctlFieldControl is Controls.StarRating) _enmFieldType = FormFieldControlType.StarRating;
			if (_ctlFieldControl is Controls.Confirmation) _enmFieldType = FormFieldControlType.Confirmation;
			if (_ctlFieldControl is Controls.TimeSelect) _enmFieldType = FormFieldControlType.TimeSelect;
			if (_ctlFieldControl is Literal || _ctlFieldControl is Label) _enmFieldType = FormFieldControlType.Literal;
			if (_ctlFieldControl is Controls.FileUpload) _enmFieldType = FormFieldControlType.FileUpload;
			if (_ctlFieldControl is Controls.Combo) _enmFieldType = FormFieldControlType.Combo;
		}

		/// <summary>
		/// Find control in field placeholder
		/// </summary>
		/// <param name="strControlName">Control ID to find.</param>
		/// <returns>The found Control or null</returns>
		public object FindFieldControl(string strControlName) {
			EnsureChildControls();
			return (Functions.FindControlRecursive(_plhFieldControls.Controls[0], strControlName));
		}

		public void AddFieldControl(Control ctl) {
			EnsureChildControls();
			_plhFieldControls.Controls.Add(ctl);
			SetupFieldControl();
		}

		public void SetFieldInError() {
			CssClass = "formRowError";
		}

		#region Enumerations

		/// <summary>
		/// FormFieldControlType (for javascript)
		/// *** MAKE SURE THIS ENUM IS COPIED TO JAVASCRIPT ***
		/// </summary>
		public enum FormFieldControlType {
			TextBox,
			DropDown,
			CheckBox,
			StarRating,
			Confirmation,
			Literal,
			TimeSelect,
			DateSelect,
			FieldCollection,
			FileUpload,
			Combo
		}

		#endregion

	}
}
