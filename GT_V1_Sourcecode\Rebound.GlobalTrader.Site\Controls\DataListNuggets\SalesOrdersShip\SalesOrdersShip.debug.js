///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.prototype = {

	get_blnShowAllOrders: function() { return this._blnShowAllOrders; }, set_blnShowAllOrders: function(v) { if (this._blnShowAllOrders !== v)  this._blnShowAllOrders = v; }, 
	get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    get_ibtnExportCSV: function () { return this._ibtnExportCSV; }, set_ibtnExportCSV: function (v) { if (this._ibtnExportCSV !== v) this._ibtnExportCSV = v; },//[001]
    get_sortIndex: function () { return this._sortIndex; }, set_sortIndex: function (v) { if (this._sortIndex !== v) this._sortIndex = v; },
    get_sortDir: function () { return this._sortDir; }, set_sortDir: function (v) { if (this._sortDir !== v) this._sortDir = v; },
    get_pageIndex: function () { return this._pageIndex; }, set_pageIndex: function (v) { if (this._pageIndex !== v) this._pageIndex = v; },
    get_pageSize: function () { return this._pageSize; }, set_pageSize: function (v) { if (this._pageSize !== v) this._pageSize = v; },

    initialize: function () {
        //this.getFilterField("ctlStatus")._blnAllTab = true;
		this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
		this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this._strPathToData = "controls/DataListNuggets/SalesOrdersShip";
		this._strDataObject = "SalesOrdersShip";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.callBaseMethod(this, "initialize");
        $("#ctl00_cphMain_ctlSalesOrders_ctlDB_ctl16_ctlFilter_ctlShipVia_ddl_ddl").css("width", "80%");
        if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.exportCSV));
	},
	
	initAfterBaseIsReady: function() {
		this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
		this.updateFilterVisibility();
		this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._blnShowAllOrders = null;
        this._IsGlobalLogin = null;
        if (this._ibtnExportCSV) $R_IBTN.clearHandlers(this._ibtnExportCSV);//[001]
        this._ibtnExportCSV = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.callBaseMethod(this, "dispose");

	},
						
	pageTabChanged: function() {
		this._table._intCurrentPage = 1;
		this._blnShowAllOrders = (this._intCurrentTab == 1);
		this.updateFilterVisibility();
		this.getData();
	},

	setupDataCall: function() {
		var strAction = "GetData";
	    if (this._blnShowAllOrders) strAction += "_All";
	    this._objData.set_DataAction(strAction);
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
        
	},
    getDataOK: function () {
        

		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
		    var row = this._objResult.Results[i];
		    var aryData;
		    if (this._IsGlobalLogin) {
		        aryData = [
                        $RGT_nubButton_ShipSalesOrder(row.ID, row.No)
                        , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                        , row.Allocated
                        , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.CustPONO))
                        , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.DateOrdered), $R_FN.setCleanTextValue(row.DatePromised))
                        , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.RequiredDate), row.ShipASAP)
                        , row.Status
                        , row.OGELRequired ? $R_FN.setCleanTextValue(row.OGELApprovalStatus) : ""
                        , row.SOSerialNumber
                        , $R_FN.setCleanTextValue(row.ClientName)
		        ];
		    } else {
		        aryData = [
                    $RGT_nubButton_ShipSalesOrder(row.ID, row.No)
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , row.Allocated
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.CustPONO))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.DateOrdered), $R_FN.setCleanTextValue(row.DatePromised))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.RequiredDate), row.ShipASAP)
                    , row.Status
                    , row.OGELRequired ? $R_FN.setCleanTextValue(row.OGELApprovalStatus) : "Not Required"
                    , row.SOSerialNumber
		        ];
            }
            this._sortIndex = row.ExpSortIndex;
            this._sortDir = row.ExpSortDir;
            this._pageIndex = row.ExpPageIndex;
            this._pageSize = row.ExpPageSize
			this._table.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	},
	
    updateFilterVisibility: function () {
        
		this.getFilterField("ctlRecentOnly").show(this._blnShowAllOrders);
		this.getFilterField("ctlIncludeClosed").show(this._blnShowAllOrders);
        this.getFilterField("ctlClientName").show(this._IsGlobalLogin);
        //this.getFilterField("ctlStatus").show(this._blnShowAllOrders);
        //this.getFilterField("ctlReadyStatus").show(!this._blnShowAllOrders);
    },
    exportCSV: function () {
        
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/DataListNuggets/SalesOrdersShip");
        obj.set_DataObject("SalesOrdersShip");
        obj.set_DataAction("ExportToCSV");
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
        if (this._blnShowAllOrders)
            obj.addParameter("Type", "All");
        else
            obj.addParameter("Type", "Ready");
        obj.addParameter("EXPSortIndex", this._sortIndex);
        obj.addParameter("ExpSortDir", this._sortDir);
        obj.addParameter("ExpPageIndex", this._pageIndex);
        obj.addParameter("ExpPageSize", this._pageSize);
        obj.addParameter("ExpPart", this.getFilterFieldValue("ctlPart"));
        obj.addParameter("ExpContact", null);
        obj.addParameter("ExpCMName", this.getFilterFieldValue("ctlCompanyName"));
        obj.addParameter("ExpSalesman", this.getFilterFieldValue("ctlSalesman"));
        obj.addParameter("ExpCustPO", null);
        obj.addParameter("ExpSONoLo", this.getFilterFieldValue("ctlSONo"));
        obj.addParameter("ExpSONoHi", this.getFilterFieldValue("ctlSONo"));
        obj.addParameter("ExpDateOrderedFrom", null);
        obj.addParameter("ExpDateOrderedTo", null);
        obj.addParameter("ExpDatePromisedFrom", this.getFilterFieldValue("ctlDatePromisedFrom"));
        obj.addParameter("ExpDatePromisedTo", this.getFilterFieldValue("ctlDatePromisedTo"));
        obj.addParameter("ExpClient", this.getFilterFieldValue("ctlClientName"));
        obj.addParameter("ExpWarehouse", this.getFilterFieldValue("ctlWarehouse"));
        obj.addParameter("ExpBuyShipMethod", this.getFilterFieldValue("ctlShipVia"));
        obj.addParameter("ExpShipASAP", this.getFilterFieldValue("ctlIsShipASAP"));
        obj.addParameter("ExpLocation", this.getFilterFieldValue("ctlLocation"));
        obj.addParameter("ExpEnhancedInspection", this.getFilterFieldValue("ctlEnhancedInspection"));
        obj.addParameter("ExpCheckedBy", this.getFilterFieldValue("ctlCheckedBy"));
        obj.addParameter("ExpReadyStatus", this.getFilterFieldValue("ctlReadyStatus"));
        obj.addParameter("ExpStatus", this.getFilterFieldValue("ctlStatus"));
        obj.addParameter("ExpIncludeClosed", this.getFilterFieldValue("ctlIncludeClosed"));
        obj.addParameter("ExpRecentOnly", this.getFilterFieldValue("ctlRecentOnly"));
        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    }
	
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrdersShip", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
