<%@ Control Language="C#" CodeBehind="DebitLines_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.DebitLines_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false" NumberOfSteps="3" MultiStepControlID="ctlMultiStep">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Explanation>
		<ReboundUI:MultiStep ID="ctlMultiStep" runat="server">
			<Items>
				<ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="DebitLine_Add_SelectSource" IsSelected="true" />
				<ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="DebitLine_Add_SelectItem" />
				<ReboundUI:MultiStepItem ID="ctlItem3" runat="server" ResourceTitle="DebitLine_Add_EditDetails" />
			</Items>
		</ReboundUI:MultiStep>
		<asp:Label ID="lblExplain_POLine" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("FormExplanations", "DebitLine_Add_SelectPOLine") %></asp:Label>
		<asp:Label ID="lblExplain_Service" runat="server" CssClass="invisible"><%=Functions.GetGlobalResource("FormExplanations", "DebitLine_Add_SelectService") %></asp:Label>
	</Explanation>
	
	<Content>
	
		<!-- Step 1 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep1" runat="server">
		
			<ReboundUI_Form:FormField id="ctlSelectSource" runat="server" FieldID="radSelectSource" ResourceTitle="SelectSource">
				<Field><asp:RadioButtonList ID="radSelectSource" runat="server" RepeatDirection="Vertical" RepeatLayout="Flow" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
		<!-- Step 2 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep2" runat="server">
		
			<asp:TableRow id="trSelectPOLine" runat="server">
				<asp:TableCell id="tdSelectPOLine" runat="server">
					<asp:Panel id="pnlLines" runat="server" CssClass="itemSearch invisible">
						<h5><%=Functions.GetGlobalResource("Misc", "POLinesForDebitNote")%></h5>
						<asp:Panel id="pnlLinesError" runat="server" CssClass="itemSearchError invisible"><asp:Label id="lblLinesError" runat="server" /></asp:Panel>
						<asp:Panel id="pnlLinesLoading" runat="server" CssClass="loading invisible"><%=Functions.GetGlobalResource("Misc", "Loading")%></asp:Panel>
						<asp:Panel id="pnlLinesNoneFound" runat="server" CssClass="noneFound invisible"><%=Functions.GetGlobalResource("NotFound", "Generic")%></asp:Panel>
						<ReboundUI:FlexiDataTable ID="tblLines" runat="server" AllowSelection="true" AllowMultipleSelection="false" />
					</asp:Panel>
					<asp:Panel id="pnlLinesNotAvailable" runat="server" CssClass="noneFound formMessage invisible"><%=Functions.GetGlobalResource("NotFound", "PONotAvailable")%></asp:Panel>
				</asp:TableCell>
			</asp:TableRow>

			<asp:TableRow id="trSelectService" runat="server">
				<asp:TableCell id="tdSelectService" runat="server"><ReboundItemSearch:Service id="ctlSelectService" runat="server" /></asp:TableCell>
			</asp:TableRow>
			
		</ReboundUI_Table:Form>
		
		<!-- Step 3 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep3" runat="server">
		
            <ReboundUI_Form:FormField id="ctlDebit" runat="server" FieldID="lblDebit" ResourceTitle="DebitNo" >
	            <Field><asp:Label ID="lblDebit" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="lblSupplier" ResourceTitle="Supplier">
	            <Field><asp:Label ID="lblSupplier" runat="server" /></Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="txtPartNo" ResourceTitle="PartNo" IsRequiredField="true">
				<Field>
					<ReboundUI:ReboundTextBox ID="txtPartNo" runat="server" Width="250" UppercaseOnly="true" />
					<ReboundAutoSearch:PartSearch ID="autPartNo" runat="server" RelatedTextBoxID="txtPartNo" ResultsHeight="150" Width="250" ResultsActionType="RaiseEvent" TriggerByButton="true" />
				</Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlService" runat="server" FieldID="txtService" ResourceTitle="Service" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtService" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlServiceDescription" runat="server" FieldID="txtServiceDescription" ResourceTitle="Description">
				<Field><ReboundUI:ReboundTextBox ID="txtServiceDescription" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlSupplierPart" runat="server" FieldID="txtSupplierPart" ResourceTitle="SupplierPartNo">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierPart" runat="server" Width="150" UppercaseOnly="true" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlROHSStatus" runat="server" FieldID="ddlROHSStatus" ResourceTitle="ROHS" DisplayRequiredFieldMarkerOnly="true">
                <Field><ReboundDropDown:ROHSStatus ID="ddlROHSStatus" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
			<ReboundUI_Form:FormField id="ctlDateCode" runat="server" FieldID="txtDateCode" ResourceTitle="DateCode">
				<Field><ReboundUI:ReboundTextBox ID="txtDateCode" runat="server" Width="60" MaxLength="5" UppercaseOnly="true" /></Field>			
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="txtQuantity" ResourceTitle="Quantity" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="80" TextBoxMode="numeric" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPrice" runat="server" FieldID="txtPrice" ResourceTitle="Price" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> <asp:Label id="lblCurrency_Price" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlManufacturer" runat="server" FieldID="cmbManufacturer" ResourceTitle="Manufacturer">
				<Field><ReboundUI:Combo ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="cmbProducts" ResourceTitle="Product"  >
				<Field><ReboundUI:Combo ID="cmbProducts" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="450" PanelHeight="250"  AutoSearchControlType="Products" /></Field>
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="ddlProduct" ResourceTitle="Product">
				<Field><ReboundDropDown:Product ID="ddlProduct" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>

			<%--<ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="ddlPackage" ResourceTitle="Package">
				<Field><ReboundDropDown:Package ID="ddlPackage" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
             <ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="cmbPackage" ResourceTitle="Package">
				<Field>
                    <ReboundUI:Combo ID="cmbPackage" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site"  AutoSearchControlType="Packages" />
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlTaxable" runat="server" FieldID="chkTaxable" ResourceTitle="Taxable" DefaultValue="true">
				<Field><ReboundUI:ImageCheckBox ID="chkTaxable" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlLineNotes" runat="server" FieldID="txtLineNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtLineNotes" runat="server" Width="400" TextMode="multiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlPrintHazWar" runat="server" FieldID="chkPrintHazWar" ResourceTitle="PrintHazWarning" >
				<Field><ReboundUI:ImageCheckBox ID="chkPrintHazWar" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
