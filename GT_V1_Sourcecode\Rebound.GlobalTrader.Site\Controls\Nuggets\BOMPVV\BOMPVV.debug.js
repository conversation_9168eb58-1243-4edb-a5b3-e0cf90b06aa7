///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      Suhail          25/04/2018   Added contact and company name while sending mail via Add New Communication Note
//[002]      Umendra Gupta   21-Jan-2019  Add View Tree Button.
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.initializeBase(this, [element]);
    this._intBOMID = -1;
    this._blnHasRequirement = false;
    this._blnRequestedToPoHub = false;
    this._blnRelease = false;
    this._isAddButtonEnable = true;
    this._isPurchaseHub = false;
    this._intCurrencyNo = -1;
    this._BomCode = "";
    this._BomName = "";
    this._BomCompanyName = "";
    this._BomCompanyNo = 0;
    this._intContact2No = -1;
    this._stringCurrency = null;
    this._inActive = false;
    this._BomContactname = "";
    this._BomContactNo = 0;
    // this._blnPOHub = false;
    this._CurrentSupplier = "";
    this._QuoteRequired = "";
    this._blnAllHasDelDate = false;
    this._blnAllHasProduct = false;
    this._blnCanReleaseAll = false;
    this._blnAllItemHasSourcing = false;
    this.BOMStatus = "";
    this._isClosed = false;

    this._UpdatedBy = null;
    this._blnCanNoBidAll = true;
    this._isNoBidCount = false;
    this._RequestToPOHubBy = -1;
    this._UpdateByPH = -1;
    this._blnReqInValid = false;
    this._ValidMessage = "";
    this._BomClientNo = -1;
    this._blnMerginCanReleaseAll = false;
    this._UnitBuyPrice = null;
    this._UnitSellPrice = null;
    this._IsRecord = false;
    this._IsView = false;
    
};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.prototype = {
    get_intBOMID: function () { return this._intBOMID; }, set_intBOMID: function (value) { if (this._intBOMID !== value) this._intBOMID = value; },
    get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_ibtnDelete: function () { return this._ibtnDelete; }, set_ibtnDelete: function (value) { if (this._ibtnDelete !== value) this._ibtnDelete = value; },
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (value) { if (this._blnPOHub !== value) this._blnPOHub = value; },
    get_IsDiffrentClient: function () { return this._IsDiffrentClient; }, set_IsDiffrentClient: function (value) { if (this._IsDiffrentClient !== value) this._IsDiffrentClient = value; },
    get_ClientId: function () { return this._ClientId; }, set_ClientId: function (value) { if (this._ClientId !== value) this._ClientId = value; },
    get_tblPVVBOM: function () { return this._tblPVVBOM; }, set_tblPVVBOM: function (value) { if (this._tblPVVBOM !== value) this._tblPVVBOM = value; },
    get_ibtnView: function () { return this._ibtnView; }, set_ibtnView: function (value) { if (this._ibtnView !== value) this._ibtnView = value; },
    
    


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.callBaseMethod(this, "initialize");
        this._strPathToData = "controls/Nuggets/BOMPVV";
        this._strDataObject = "BOMPVV";
        //this.addRefreshEvent(Function.createDelegate(this, this.GetCheckData));
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this.showLoading(false);
        this.showContent(true);
        this.showContentLoading(false);
       
        this.getData();
        this.GetCheckData();
        
        
        
        //edit form
        if (this._ibtnEdit || this._ibtnView) {

            if (this._ibtnEdit) $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            if (this._ibtnView) $R_IBTN.addClick(this._ibtnView, Function.createDelegate(this, this.showEditViewForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit._intBOMID = this._intBOMID;
            this._frmEdit._BomCode = this._BomCode;
            this._frmEdit._BomName = this._BomName;
            this._frmEdit._BomCompanyName = this._BomCompanyName;
            this._frmEdit._BomCompanyNo = this._BomCompanyNo;
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }
        //Delete
        if (this._ibtnDelete) {

            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[1]);
            this._frmDelete._intBOMID = this._intBOMID;
            this._frmDelete._BomCode = this._BomCode;
            this._frmDelete._BomName = this._BomName;
            this._frmDelete._BomCompanyName = this._BomCompanyName;
            this._frmDelete._BomCompanyNo = this._BomCompanyNo;
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.cancelDelete));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.saveDeleteComplete));
        }

        this.getData();



    },


    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);


        if (this._tblPVVBOM) this._tblPVVBOM.dispose();

        this._tblPVVBOM = null;
        
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmDelete) this._frmDelete.dispose();
        this._IsRecord = null;
        this._intBOMID = null;
        this._ibtnEdit = null;
        this._ibtnDelete = null;
        this._frmEdit = null;
        this._frmDelete = null;
        
        this._IsDiffrentClient = null;
        this._ClientId = null;
        this._blnHasRequirement = null;
        this._blnPOHub = null;
        this._blnRequestedToPoHub = null;
        this._blnRelease = null;
        this._intCurrencyNo = null;
        this._blnAllHasDelDate = null;
        this._blnAllHasProduct = null;
        this._blnCanReleaseAll = null;
        this._blnAllItemHasSourcing = null;
        this._ibtnClose = null;
        this._ibtnNoBid = null;
        this._ibtnNote = null;
        this._ValidMessage = null;
        this._intContact2No = null;
        this._ibtnCrossMatch = null;
        this._blnMerginCanReleaseAll = null;
        this._UnitBuyPrice = null;
        this._UnitSellPrice = null;
        this._IsView = null;
        this._PVVAnswerId = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.callBaseMethod(this, "dispose");
    },
    ////////////////////////////////////////
    GetCheckData: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMPVV");
        obj.set_DataObject("BOMPVV");
        obj.set_DataAction("GetCheckData");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getDatacheckOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },
    getDatacheckOK: function (args) {
        var res = args._result;
        if (res.Results.length==0) {
            $R_FN.showElement(this._ibtnEdit, false);
            $("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl12_lblPVVBOMIHS").show();
            document.getElementById('ctl00_cphMain_ctlBOMPVV_ctlDB_ctl12_lblPVVBOMIHS').setAttribute('title', "for visibility of Edit button. Please Add PPV/ BOM Qualification  Question,s from company Setting in Setup");
        }
        else {
            $R_FN.showElement(this._ibtnEdit, true);
            $("#ctl00_cphMain_ctlBOMPVV_ctlDB_ctl12_lblPVVBOMIHS").hide();
            
        }

    },

    ////////////////////////////////////////////////
    getData: function () {
        this.getData_Start();
        this.showContent(true);
       // this.showCreditHistoryError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intBOMID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.onSave();
    },
    getDataOK: function (args) {
        
        var res = args._result;
        var aryData, row;
        this._tblPVVBOM.clearTable();
        this.showLoading(false);
        if (res.Items) {
           
            for (var i = 0; i < res.Items.length; i++) {
                var row = res.Items[i];
                this._BomCompanyNo = row.PVVAnswerId;
                aryData = [
                    $R_FN.setCleanTextValue(row.PVVQuestionName),
                    $R_FN.setCleanTextValue(row.PVVAnswerName)

                ];
               
                this._tblPVVBOM.addRow(aryData, row.ID, false);

                row = null; aryData = null; 
                this._tblPVVBOM.resizeColumns();

            }
           
            if (res.Items.length > 0) {
                this._IsRecord = true;
                
            }
            else {
                this._IsRecord = false;

            }
            
            
        }
        
        if (this._BomCompanyNo == 0) {
            
            this._inActive = false
        }
        else {
            this._inActive = true;
        }
        this.enableButtons(true);
        this.getDataOK_End();
        
    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
   

    enableButtons: function (bln) {
        //alert(this._PVVAnswerId);
        if (bln) {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._blnPOHub);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, bln && !this._blnPOHub && this._IsRecord && this._inActive);
            if (this._ibtnView) $R_IBTN.enableButton(this._ibtnView, bln && this._IsRecord);
        } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
            if (this._ibtnView) $R_IBTN.enableButton(this._ibtnView, false);

            
        }

    },

    showEditForm: function () {
        this._frmEdit._intBOMID = this._intBOMID;
        this._frmEdit._IsView = false;
        this.showForm(this._frmEdit, true);
    },

    showEditViewForm: function () {
        
        this._frmEdit._intBOMID = this._intBOMID;
        this._frmEdit._IsView = true;
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function () {
        this.showForm(this._frmEdit, false);
    },

    cancelEdit: function () {
        this.hideEditForm();
    },

    saveEditComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
  

    showDeleteForm: function () {

        this._frmDelete._intBOMID = this._intBOMID;
        //this._frmDelete._BomCode = this._BomCode;
        //this._frmDelete._BomName = this._BomName;
        //this._frmDelete._BomCompanyName = this._BomCompanyName;
        //this._frmDelete._BomCompanyNo = this._BomCompanyNo;
        /*this._frmDelete.setFieldValue("ctlName", this.getFieldValue("ctlName"));*/
        this.showForm(this._frmDelete, true);
    },
   
    hideDeleteForm: function () {
        this.showForm(this._frmDelete, false);
    },

      
    cancelDelete: function () {
        this.hideDeleteForm();
    },
   

    saveDeleteComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    }


   
    

};

Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.BOMPVV", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
