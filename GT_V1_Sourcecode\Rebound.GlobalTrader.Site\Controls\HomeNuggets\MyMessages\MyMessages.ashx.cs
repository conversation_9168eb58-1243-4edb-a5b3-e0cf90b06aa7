using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class MyMessages : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                List<MailMessage> lst = MailMessage.GetListForRecipient(LoginID);
                if (lst == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    jsn.AddVariable("Count", lst.Count);
                    for (int i = 0; i < lst.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lst[i].MailMessageId);
                        jsnItem.AddVariable("From", lst[i].FromLoginName);
                        jsnItem.AddVariable("Subject", lst[i].Subject);
                        jsnItem.AddVariable("DateSent", Functions.FormatDate(lst[i].DateSent, false, true));
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("Items", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lst = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
		}


	}
}
