Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.initializeBase(this,[n]);this._intCRMAID=0;this._intLineID=-1;this._intQuantityReceived=0;this._intQuantityAvailable=0;this._intLineQuantityExists=0;this._intInvoiceLineNo=0;this._intQtyShipped=0;this._intQtyCRMA=0;this._intQtyReceived=0;this._isClosed=!1;this._blnProductHaza=!1};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));this.getInvoiceLines();this.showField("ctlQuantity",!this._isClosed);this.showField("ctlQuantity_Lable",this._isClosed);this._ctlItemsReason1=$find(this.getField("ctlItemsReason1").ID);this._ctlItemsReason1.addItem();this._ctlItemsReason2=$find(this.getField("ctlItemsReason2").ID);this._ctlItemsReason2.addItem();this._ctlItemsReason1.hidMenuPanel();this._ctlItemsReason2.hidMenuPanel();this.enableFieldCheckBox("ctlPrintHazWar",this._blnProductHaza)},dispose:function(){this.isDisposed||(this._intCRMAID=null,this._intLineID=null,this._intQuantityReceived=null,this._intQuantityAvailable=null,this._intLineQuantityExists=null,this._intInvoiceLineNo=null,this._intQtyShipped=null,this._intQtyCRMA=null,this._intQtyReceived=null,this._isClosed=null,this._blnProductHaza=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.callBaseMethod(this,"dispose"))},setFieldsFromHeader:function(n,t){this.setFieldValue("ctlCustomerRMA",n);this.setFieldValue("ctlCustomer",t)},getInvoiceLines:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("GetQtyLine");n.addParameter("ID",this._intLineID);n.addParameter("invoiceLineID",this._intInvoiceLineNo);n.addDataOK(Function.createDelegate(this,this.getInvoiceLinesOK));n.addError(Function.createDelegate(this,this.getInvoiceLinesError));n.addTimeout(Function.createDelegate(this,this.getInvoiceLinesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getInvoiceLinesOK:function(n){var i=n._result,r=!1,t;i.Lines&&($R_FN.showElement(this._pnlLines,!0),$R_FN.showElement(this._pnlLinesNotAvailable,!1),t=i.Lines,this._intQuantityAvailable=t.QuantityAvailable,this._intQtyShipped=t.QuantityShipped,this._intQtyCRMA=t.QuantityCRMA,this._intQtyReceived=t.QuantityReceived,aryData=null,t=null,r=!0)},getInvoiceLinesError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveClicked:function(){if((this._isClosed!=!1||this.validateForm())&&this.validateReason()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("SaveEdit");n.addParameter("id",this._intLineID);n.addParameter("LineNotes",this.getFieldValue("ctlLineNotes"));n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("Reason1",this._ctlItemsReason1.getSubCategory());n.addParameter("Reason2",this._ctlItemsReason2.getSubCategory());n.addParameter("RootCause",this.getFieldValue("ctlRootCause"));n.addParameter("Avoidable",this.getFieldValue("ctlIsAvoidable"));n.addParameter("PrintHazWar",this.getFieldValue("ctlPrintHazWar"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return this.checkNumericFieldLessThanOrEqualTo("ctlQuantity",this._intQuantityAvailable)||(n=!1),this.checkNumericFieldGreaterThanOrEqualTo("ctlQuantity",Math.max(1,this._intQtyReceived))||(n=!1),n||this.showError(!0),n},validateReason:function(){var n=!0;return this._ctlItemsReason1._txtReason.value==""&&(n=!1,this.showError(!0,$R_RES.ResReason1Value)),n}};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);