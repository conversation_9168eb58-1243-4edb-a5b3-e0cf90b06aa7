using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class CRMAMainInfo_Edit : Base
    {

        #region Locals

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "CRMAMainInfo_Edit");
            AddScriptReference("Controls.Nuggets.CRMAMainInfo.Edit.CRMAMainInfo_Edit.js");
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            WireUpControls();
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Wire up controls to the ascx
        /// </summary>
        private void WireUpControls()
        {
        }

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCRMAID", _objQSManager.CRMAID);
        }

    }
}