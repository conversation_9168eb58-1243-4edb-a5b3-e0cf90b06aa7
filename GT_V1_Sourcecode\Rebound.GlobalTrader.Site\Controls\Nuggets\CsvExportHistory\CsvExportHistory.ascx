<%@ Control Language="C#" CodeBehind="CsvExportHistory.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">

	
	<Content>
		<table class="twoCols">
			<tr>
			
				<td class="col3">
					<%--<div class="dataItem_Title"><%=Functions.GetGlobalResource("FormFields", "Log_Details")%></div>
					--%><ReboundUI:FlexiDataTable ID="tblCreditHistory" runat="server" PanelHeight="150" />
				</td>
			</tr>
		</table>
	</Content>
	
	
</ReboundUI_Nugget:DesignBase>
