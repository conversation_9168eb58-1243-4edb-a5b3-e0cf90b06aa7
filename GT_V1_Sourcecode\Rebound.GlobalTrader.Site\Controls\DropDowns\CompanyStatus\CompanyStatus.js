Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intPOHubClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/CompanyStatus");this._objData.set_DataObject("CompanyStatus");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.CompanyStatus)for(n=0;n<t.CompanyStatus.length;n++)this.addOption(t.CompanyStatus[n].Name,t.CompanyStatus[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.CompanyStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);