///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 05.01.2010:
// - add links back to related Quotes
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      Shashi Keshar   03/11/2016   Notes Color changed with Red if Sourcing notes come from HUB.
//[002]      Aashu Singh     20/06/2018   [REB-11754]: MSL level
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.initializeBase(this, [element]);
    this._intCustomerRequirementID = -1;
    this._intCompanyID = -1;
    this._blnRequirementClosed = false;
    this._blnFromPOHub = false;
    this._HasQuote = false;
    this._IsSoCreated = false;
    this._IsClosed = false;
    this._IsDifferentClient = false;
    //[002] start
    this._msl = true;
    //[002] end
    this._mfrAdvisoryNotes = "";
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.prototype = {

    get_intCustomerRequirementID: function () { return this._intCustomerRequirementID; }, set_intCustomerRequirementID: function (v) { if (this._intCustomerRequirementID !== v) this._intCustomerRequirementID = v; },
    get_tbl: function () { return this._tbl; }, set_tbl: function (value) { if (this._tbl !== value) this._tbl = value; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function () { return this._ibtnEdit; }, set_ibtnEdit: function (v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },
    get_ibtnQuote: function () { return this._ibtnQuote; }, set_ibtnQuote: function (v) { if (this._ibtnQuote !== v) this._ibtnQuote = v; },
    get_ctlMultiSelectionCount: function () { return this._ctlMultiSelectionCount; }, set_ctlMultiSelectionCount: function (value) { if (this._ctlMultiSelectionCount !== value) this._ctlMultiSelectionCount = value; },
    get_ibtnDelete: function () { return this._ibtnDelete; }, set_ibtnDelete: function (v) { if (this._ibtnDelete !== v) this._ibtnDelete = v; },
    addAddFormShown: function (handler) { this.get_events().addHandler("AddFormShown", handler); },
    removeAddFormShown: function (handler) { this.get_events().removeHandler("AddFormShown", handler); },
    onAddFormShown: function () {
        var handler = this.get_events().getHandler("AddFormShown");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addSourcingResultAdded: function (handler) { this.get_events().addHandler("SourcingResultAdded", handler); },
    removeSourcingResultAdded: function (handler) { this.get_events().removeHandler("SourcingResultAdded", handler); },
    onSourcingResultAdded: function () {
        var handler = this.get_events().getHandler("SourcingResultAdded");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.callBaseMethod(this, "initialize");
       
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this._tbl.addMultipleSelectionChanged(Function.createDelegate(this, this.selectResult));
        this._ctlMultiSelectionCount.registerTable(this._tbl);

        if (this._ibtnQuote) $R_IBTN.addClick(this._ibtnQuote, Function.createDelegate(this, this.doQuote));

        //Add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.cancelAdd));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        //Edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[1]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }
        // Delete Part Watch Match
        if (this._ibtnDelete) {
            $R_IBTN.addClick(this._ibtnDelete, Function.createDelegate(this, this.showPartWatchDeleteForm));
            this._frmDelete = $find(this._aryFormIDs[2]);
            //this._frmDelete._intCustomerRequirementID = this._intCustomerRequirementID;
            this._frmDelete.addCancel(Function.createDelegate(this, this.cancelPartWatchDelete));
            this._frmDelete.addNotConfirmed(Function.createDelegate(this, this.cancelPartWatchDelete));
            this._frmDelete.addSaveComplete(Function.createDelegate(this, this.savePartWatchDeleteComplete));
        }

        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnQuote) $R_IBTN.clearHandlers(this._ibtnQuote);
        if (this._tbl) this._tbl.dispose();
        if (this._ctlMultiSelectionCount) this._ctlMultiSelectionCount.dispose();
        this._intCustomerRequirementID = null;
        this._intCompanyID = null;
        this._tbl = null;
        this._ibtnAdd = null;
        this._ibtnEdit = null;
        this._ibtnQuote = null;
        this._ctlMultiSelectionCount = null;
        this._blnRequirementClosed = null;
        this._blnFromPOHub = null;
        this._msl = null;
        if (this._ibtnDelete) $R_IBTN.clearHandlers(this._ibtnDelete);
        if (this._frmDelete) this._frmDelete.dispose();
        this._frmDelete = null;
        this._ibtnDelete = null;
        this._mfrAdvisoryNotes = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.callBaseMethod(this, "dispose");
    },

    getData: function () {
        this._ctlMultiSelectionCount.clearAll();
        this.getData_Start();
        this.enableButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CusReqSourcingResults");
        obj.set_DataObject("CusReqSourcingResults");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intCustomerRequirementID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function (args) {
        this._blnFromPOHub = false;
        var res = args._result;
        this._tbl.clearTable();
        var RowColor = "";
        for (var i = 0, l = res.Results.length; i < l; i++) {
            var row = res.Results[i];
            var strQuotes = "";
            //[001]  Start Code
            var strCSS = "";
            if (row.IsHub) strCSS = "sourcingNotesIsHUB";
            //[001]  End Code
            for (var q = 0, lq = row.Quotes.length; q < lq; q++) {
                strQuotes += $RGT_nubButton_Quote(row.Quotes[q].ID, row.Quotes[q].No);
            }
            var aryData = [
                (!row.IsHub) ? (!row.DiffrentClientOffer) ? $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.SupplierNo, row.Supplier, null, null, null, row.SupplierAdvisoryNotes), strQuotes)
                    : $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Supplier) + $R_FN.createAdvisoryNotesIcon(row.SupplierAdvisoryNotes, 'margin-left-10') + "<br />" + row.ClientCode, strQuotes)
                    : $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Supplier) + $R_FN.createAdvisoryNotesIcon(row.SupplierAdvisoryNotes, 'margin-left-10') + "<br />" + row.SupplierType, strQuotes)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Part), "<span class =" + strCSS + ">" + $R_FN.setCleanTextValue(row.NotesMSL) + "</span>")
                , $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Date), $R_FN.setCleanTextValue(row.By))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Qty), $R_FN.setCleanTextValue(row.Status))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Price), $R_FN.setCleanTextValue(row.PriceInBase))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.RegionName), $R_FN.setCleanTextValue(row.TermsName))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.EstimatedShippingCost), $R_FN.setCleanTextValue(row.EstimatedShippingCostInBase))
                //, $R_FN.setCleanTextValue(row.PartWatchMatch == true ? "<input type='checkbox' checked onclick='return false'/>" :"<input type='checkbox' onclick='return false'/>")
                , $R_FN.setCleanTextValue(row.PartWatchMatch == true ? "<input type='checkbox' checked onclick='return false'/>" : "<input type='checkbox' onclick='return false'/>")
            ];
            var objExtra = {
                POHub: row.IsHub,
                HasQuote: row.Quotes.length > 0 ? false : true,
                IsClosed: row.IsClosed,
                IsSoCreated: row.IsSoCreated,
                MSL: row.MSL,
                QLineID: row.ID,
                IsPartWatchMatch: row.PartWatchMatch,
                IsDifferentClient: row.DiffrentClientOffer,
                SupplierAdvisoryNotes: row.SupplierAdvisoryNotes,
                MfrAdvisoryNotes: row.MfrAdvisoryNotes
            };
            RowColor = row.PartWatchMatch == true ? "rowGreyBackground" : "";
            this._tbl.addRowRowColor(aryData, row.ID, false, objExtra, null, null, row.PartWatchMatch, RowColor);
            row = null;
            strQuotes = null;
        }
        this._tbl.resizeColumns();
        this.getDataOK_End();
        this.showNoData(res.Results.length == 0);
    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    selectResult: function () {
        this.clearMessages();
        var objExtra = this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[0]);
        if (objExtra) {
            this._blnFromPOHub = objExtra.POHub;
            this._IsSoCreated = objExtra.IsSoCreated;
            this._IsClosed = objExtra.IsClosed;
            this._IsDifferentClient = objExtra.IsDifferentClient;
            if ((objExtra.HasQuote == false) && (this._blnFromPOHub == true)) {
                this._HasQuote = false;
            }
            else {
                this._HasQuote = true;
            }
        }
        this.enableButtons(true);
    },
    //[004] start
    checkMSLValue: function () {
        var blnOK = true;
        var selectedRows = this._tbl._aryCurrentValues;
        var allData = this._tbl._aryExtraData;
        //this._msl = true;
        for (i = 0; i < selectedRows.length; i++) {
            for (j = 0; j < allData.length; j++) {
                if (allData[j].QLineID === selectedRows[i] && allData[j].MSL === "") {
                    this._msl = false;
                    blnOK = false;
                }
            }
        }
        return blnOK;
    },
    //[004] end
    enableButtons: function (bln) {
        if (bln) {
            var partwatch = false;
            for (var i = 0; i < this._tbl._aryCurrentValues.length;i++)
            {
                if (this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[i]).IsPartWatchMatch == true) {
                    partwatch = true;
                }
                else {
                    partwatch = false;
                    break;
                }
            }
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._blnRequirementClosed);
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._blnRequirementClosed && this._tbl._aryCurrentValues.length == 1 && !this._blnFromPOHub && this._IsDifferentClient == false);
            if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnQuote, !this._blnRequirementClosed && this._tbl._aryCurrentValues.length > 0 && !(this._IsSoCreated || this._IsClosed) && this._IsDifferentClient == false);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, this._tbl._aryCurrentValues.length > 0 && !this._blnRequirementClosed && partwatch==true);
        } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnQuote) $R_IBTN.enableButton(this._ibtnQuote, false);
            if (this._ibtnDelete) $R_IBTN.enableButton(this._ibtnDelete, false);
        }
    },

    doQuote: function () {
        this.clearMessages();
        if (!this.checkMSLValue()) {
            this.addMessage("Kindly update MSL", $R_ENUM$MessageTypeList.Error);
            return false;
        }
        else {
            location.href = $RGT_gotoURL_QuoteAdd(this._intCompanyID, null, this._intCustomerRequirementID, $R_FN.arrayToSingleString(this._tbl._aryCurrentValues));
        }
    },

    showAddForm: function () {
        this._frmAdd.setFormFieldsToDefaults();
        this.onAddFormShown();
        this._frmAdd._intCustomerRequirementID = this._intCustomerRequirementID;
        this._frmAdd._strPartNo = this._strPartNo;
        this._frmAdd._intSupplierId = -1;
        this._frmAdd._mfrAdvisoryNotes = $('#ctl00_cphMain_ctlMainInfo_ctlDB_ctl13_hidMfrNotes_hid').val();
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function () {
        this.showForm(this._frmAdd, false);
        this._tbl.resizeColumns();
    },

    cancelAdd: function () {
        this.hideAddForm();
    },

    saveAddComplete: function () {
        this.getData();
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSourcingResultAdded();
    },

    showEditForm: function () {
        this._frmEdit._intSourcingResultID = this._tbl._aryCurrentValues[0];

        var objExtra = this._tbl.getSelectedExtraData(this._tbl._arySelectedIndexes[0]);
        this._frmEdit._supplierAdvisoryNotes = objExtra.SupplierAdvisoryNotes;
        this._frmEdit._mfrAdvisoryNotes = objExtra.MfrAdvisoryNotes;
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function () {
        this.showForm(this._frmEdit, false);
        this._tbl.resizeColumns();
    },

    cancelEdit: function () {
        this.hideEditForm();
    },

    saveEditComplete: function () {
        this.getData();
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },
    showPartWatchDeleteForm: function () {

        this._frmDelete._intSourcingResultID = this._tbl._aryCurrentValues[0];
        this._frmDelete._aryCurrentValues = $R_FN.arrayToSingleString(this._tbl._aryCurrentValues)
        this.showForm(this._frmDelete, true);
    },

    cancelPartWatchDelete: function () {
        this.showForm(this._frmDelete, false);
        this._tbl.resizeColumns();
    },

    savePartWatchDeleteComplete: function () {
        this.getData();
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    }

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
