///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField.initializeBase(this, [element]);
	this._blnVisible = true;
	this._ctlRelatedForm = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField.prototype = {

	get_td: function() { return this._td; }, 	set_td: function(value) { if (this._td !== value)  this._td = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._blnVisible = true;
		this._ctlRelatedForm = null;
		this._td = null;
		Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	setText: function(str) {
		$R_FN.setInnerHTML(this._td, str);
	},
	
	show: function(blnShow) {
		this._blnVisible = blnShow;
		$R_FN.showElement(this.get_element(), blnShow);
	},
	
	resetField: function() {
		//do nothing - needed when iterating all form controls.
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField", Sys.UI.Control, Sys.IDisposable);
