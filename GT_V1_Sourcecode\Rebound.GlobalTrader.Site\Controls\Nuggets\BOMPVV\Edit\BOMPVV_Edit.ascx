
<%--
Marker     changed by      date         Remarks

--%>
<%@ Control Language="C#" CodeBehind="BOMPVV_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMMainInfo_Edit")%></Explanation>

	<Content>
		<ReboundUI_Table:Form ID="frm" runat="server">
           <asp:TableRow>
                <asp:TableCell class="title" RowSpan="2" Style="width: 100%">
         <div style="width:100%;overflow: scroll; border: solid 1px; overflow-x: hidden; overflow-y: scroll;  height: 90%; border-style: ridge;" >
        <div id="PVVBOMDiv">
	        </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                </asp:TableCell>
            </asp:TableRow>
     </ReboundUI_Table:Form>
	
	</Content>
</ReboundUI_Form:DesignBase>
