﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209534]     NgaiTo		 	17-Sep-2024			UPDATE		209534: OGEL approval dropdown to be moved out of code to the setup screen
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_update_OGELLicense] (
	@OgelId INT,
	@OgelNumber NVARCHAR(250),
	@Description NVARCHAR(500) = NULL,
	@InActive BIT = NULL,
	@UpdatedBy INT = NULL,
	@RowsAffected INT = NULL OUTPUT
	)
AS
BEGIN
	-- Check for existing tbOGELLicense with the same OgelNumber
	IF EXISTS (SELECT 1 FROM tbOGELLicense
			WHERE OgelNumber = LTRIM(RTRIM(@OgelNumber))
			AND OgelId != LTRIM(RTRIM(@OgelId)))
	BEGIN
		SET @RowsAffected = - 1;
	END
	ELSE
	BEGIN
		BEGIN    
            UPDATE dbo.tbOGELLicense
			SET OgelNumber = @OgelNumber,
				Description = @Description,
				Inactive = @Inactive,
				UpdatedBy = @UpdatedBy,
				DLUP = current_timestamp
			WHERE OgelId = @OgelId
			END
        SET @RowsAffected = @@ROWCOUNT; 
	END

	SELECT @RowsAffected;
END
GO