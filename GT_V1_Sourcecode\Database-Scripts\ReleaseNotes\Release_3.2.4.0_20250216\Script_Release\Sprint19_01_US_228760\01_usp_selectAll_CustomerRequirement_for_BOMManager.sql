﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_selectAll_CustomerRequirement_for_BOMManager]    Script Date: 1/6/2025 2:41:42 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*  
===========================================================================================  
TASK		 UPDATED BY     DATE			ACTION		DESCRIPTION  
[US-210037]  An.TranTan		24-Oct-2024		UPDATE		Get mfr Advisory Notes
[US-221009]  An.TranTan     29-Nov-2024		Update		Prioritize manufacturer restricted message
[US-228760]  Phuc Hoang     06-Jan-2025		Update		Quote - Retrieve Mapping incorrect Customer Template
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_CustomerRequirement_for_BOMManager] 
	@BOMManagerNo INT
	,@ClientID INT = 0
	,@IsPoHub BIT = 0
	,@Part VARCHAR(100) = NULL
	,@curPage INT = 1
	,@Rpp INT = 10
	,@ReqType INT = 0
AS
BEGIN
	IF (@ReqType = 1)
	BEGIN
		SELECT d.*
			,e.GeneratedFilename
			,'' AS POHubReleaseName
			,'1' PackageName
			,'1' ProductName
			,'1' CurrencyCode
			,'1' SalesmanName
			,'1' ManufacturerCode
			,'1' CompanyName
			,'1'
			,'1' BOMManagerHeader
			,'1' BOMManagerFullName
			,'1' SourcingResult
			,'1' RequestToPOHubBy
			,'1' BOMManagerFullName
			,'1' ConvertedTargetValue
			,'1' BOMManagerCurrencyCode
			,'1' ClientName
			,'1' MSL
			,'1' BOMManagerCode
			,'1' AllSorcingHasDelDate
			,'1' AllSorcingHasProduct
			,'1' SourcingResult
			,'1' BOMManagerStatus
			,'1' Line
			,'1' LineValue
			,'1' ManufacturerName
			,'1' DateCode
			,'1' Price
			,'1' HasHubSourcingResult
			,'1' IsPrimarySourcing
			,'1' IsPrimarySourcing
			,'1' QuoteGenerated
			,'1' AutoSourcingStatus
			,'1' ReqStatusText
			,'1' TotalRecords
			,'1' OfferCount
			,'1' UpdateByPH
			,'1' SupportTeamMemberName
			,'1' QuoteId
			,a.QuoteNumber QuoteNo
			,'0' AS productid
			,'' AS ProductDescription
			,'' AS MfrAdvisoryNotes
		FROM tbQuote a
		JOIN tbQuoteLine b ON a.QuoteId = b.QuoteNo
		JOIN tbSourcingResult c ON b.SourcingResultNo = c.SourcingResultId
		JOIN tbCustomerRequirement d ON c.CustomerRequirementNo = d.CustomerRequirementId
		JOIN tbBOMManager e ON d.BOMManagerNo = e.BOMManagerId
		WHERE a.QuoteId = @BOMManagerNo

		RETURN
	END

	DECLARE @AllSorcingHasDelDate INT
	DECLARE @AllSorcingHasProduct INT
	DECLARE @SourcingResult INT
	DECLARE @SourcingResultId INT
	DECLARE @TotalRecords INT ,@skip INT;

	SELECT @AllSorcingHasDelDate = count(*)
	FROM tbCustomerRequirement cr
	JOIN tbBOMManager bm ON bm.BOMManagerId = cr.BOMManagerNo
	JOIN tbSourcingResult sr ON cr.CustomerRequirementId = sr.CustomerRequirementNo
	WHERE bm.BOMManagerId = @BOMManagerNo
		AND sr.SourcingTable IN ('PQ','OFPH','EXPH')
		AND sr.DeliveryDate IS NULL

	SELECT @AllSorcingHasProduct = count(*)
	FROM tbCustomerRequirement cr
	JOIN tbBOMManager bm ON bm.BOMManagerId = cr.BOMManagerNo
	JOIN tbSourcingResult sr ON cr.CustomerRequirementId = sr.CustomerRequirementNo
	WHERE bm.BOMManagerId = @BOMManagerNo
		AND sr.SourcingTable IN ('PQ','OFPH','EXPH')
		AND sr.ProductNo IS NULL

	SELECT @TotalRecords = count(CustomerRequirementId)
	FROM tbCustomerRequirement cr
	WHERE cr.BOMManagerNo = @BOMManagerNo
		AND cr.FullPart = CASE 
			WHEN isnull(@Part, '') = ''
				THEN cr.fullpart
			ELSE @Part
			END

	SET @skip = (@Rpp * (@curPage - 1))
	IF (@skip >= @TotalRecords AND @TotalRecords > 0)
	BEGIN
		SET @curPage = CAST(@TotalRecords / @Rpp AS INT)
		SET @skip = CAST((@Rpp * (@curPage - 1)) AS INT)
	END

	SELECT cr.CustomerRequirementId
		,cr.CustomerRequirementNumber
		,cr.ClientNo
		,REPLACE(cr.FullPart, '"', '') AS FullPart
		,REPLACE(cr.Part, '"', '') AS Part
		,cr.ManufacturerNo
		,cr.DateCode
		,cr.PackageNo
		,cr.Quantity
		,cr.Price
		,cr.CurrencyNo
		,cr.Salesman
		,cr.DatePromised
		,cr.Instructions
		,cr.Notes
		,cr.CompanyNo
		,cr.Alternate
		,cr.CustomerPart
		,cr.Closed
		,cr.ROHS
		,cr.UpdatedBy
		,cr.DLUP
		,cr.FactorySealed
		,cr.MSL
		,cr.PartialQuantityAcceptable
		,cr.Obsolete
		,cr.LastTimeBuy
		,cr.RefirbsAcceptable
		,cr.TestingRequired
		,cr.TargetSellPrice
		,cr.CompetitorBestOffer
		,cr.CustomerDecisionDate
		,cr.RFQClosingDate
		,cr.QuoteValidityRequired
		,cr.ReqType
		,cr.OrderToPlace
		,cr.ReqForTraceability
		,cr.ReleaseNote
		,lg.EmployeeName AS SalesmanName
		,co.CompanyName
		,cu.CurrencyCode
		,pr.ProductName
		,mf.ManufacturerCode
		,mf.ManufacturerName
		,Sequence AS Line
		,(cast(cr.Price AS DECIMAL(12, 2)) * cr.Quantity) AS LineValue
		,pk.PackageName
		,ct.IsTraceability
		,bom.BOMManagerName AS BOMManagerHeader
		,cr.BOMManagerNo
		,cr.BOMName
		,cr.POHubReleaseBy
		,bom.RequestToPOHubBy
		,bom.BOMManagerCode
		,bom.BOMManagerName AS BOMManagerFullName
		,bom.CurrencyNo AS BOMManagerCurrencyNo
		,bom.DLUP AS BOMManagerDate
		,bom.UpdateByPH
		,CASE bom.[Status]
			WHEN 1
				THEN 'NEW'
			WHEN 2
				THEN 'OPEN'
			WHEN 3
				THEN 'RPQ'
			WHEN 4
				THEN 'PARTIAL RELEASED'
			WHEN 5
				THEN 'RELEASED'
			WHEN 6
				THEN 'CLOSED'
			END AS BOMManagerStatus
		,c.ClientName
		--,  (SELECT TOP 1 SourcingResultId FROM tbSourcingResult sr WHERE sr.CustomerRequirementNo=cr.CustomerRequirementId ) AS SourcingResultId                                                                            
		--, (SELECT TOP 1 POHubCompanyNo FROM tbSourcingResult WHERE CustomerRequirementNo=cr.CustomerRequirementId and POHubCompanyNo is not null ) AS POHubCompany                                                                               
		,cob.CurrencyCode AS BOMManagerCurrencyCode
		,dbo.ufn_convert_currency_value(cr.Price, cr.CurrencyNo, bom.CurrencyNo, bom.DLUP) AS ConvertedTargetValue
		,@AllSorcingHasDelDate AS AllSorcingHasDelDate
		,@AllSorcingHasProduct AS AllSorcingHasProduct
		,0 AS SourcingResult
		,cr.HasClientSourcingResult
		,cr.HasHubSourcingResult
		--,@SourcingResultId AS SourcingResult            
		,cr.IsNoBid
		,cr.ExpediteDate
		,CASE 
			WHEN cr.Alternate = 1
				AND cr.AlternateStatus IS NULL
				THEN cast(1 AS TINYINT)
			ELSE cr.AlternateStatus
			END AS AlternateStatus
		-- 1-Alternate,2-Possible Alternate,3-Firm Alternate                                                         
		,cr.SupportTeamMemberNo
		,stm.EmployeeName AS SupportTeamMemberName
		,(
			--- query added by arpit 15.03.2022                                                    
			--- to get the row if any discripancy is found                            
			SELECT TOP 1 PriceIssueBuyAndSell
			FROM (
				SELECT CASE 
						WHEN A.ActualPrice >= A.Price
							THEN 1
						ELSE 0
						END PriceIssueBuyAndSell
				FROM (
					SELECT CASE 
							WHEN sr.PartWatchMatch = 1
								AND sr.POHubCompanyNo IS NOT NULL
								AND cr.ClientNo != 114
								AND @IsPoHub = 0
								THEN 0
							ELSE sr.Price
							END Price
						,sr.ActualPrice
					FROM dbo.tbSourcingResult sr
					WHERE sr.CustomerRequirementNo = cr.CustomerRequirementId
						AND (
							sr.PartWatchMatch = 1
							OR (
								(
									@IsPoHub = 0
									AND (
										sr.IsReleased = 1
										OR sr.POHubCompanyNo IS NULL
										)
									)
								OR (
									@IsPoHub = 1
									AND NOT sr.POHubCompanyNo IS NULL
									)
								)
							)
						AND ISNULL(sr.PartWatchMatch, 0) = 0
					) A
				) B
			WHERE B.PriceIssueBuyAndSell = 1
			) PriceIssueBuyAndSell
		,CASE 
			WHEN (
					SELECT TOP 1 tbsrc.IsPrimarySourcing
					FROM tbSourcingResult tbsrc
					WHERE cr.CustomerRequirementId = tbsrc.CustomerRequirementNo
						AND tbsrc.IsPrimarySourcing = 1
						AND cr.reqstatus > 3
						AND isnull(tbsrc.IsSoftDelete, 0) = 0
					) = 1
				THEN convert(BIT, 1)
			ELSE convert(BIT, 0)
			END AS IsPrimarySourcing
		,CASE 
			WHEN (
					SELECT TOP 1 tbsrc.Closed
					FROM tbSourcingResult tbsrc
					WHERE cr.CustomerRequirementId = tbsrc.CustomerRequirementNo
						AND tbsrc.IsPrimarySourcing = 1
					) = 1
				THEN convert(BIT, 1)
			ELSE convert(BIT, 0)
			END AS QuoteGenerated
		,(
			SELECT TOP 1 tbqtln.quoteno
			FROM tbSourcingResult tbsrcrslt --on tbsrcrslt.CustomerRequirementNo = cr.CustomerRequirementId                                          
			JOIN tbQuoteLine tbqtln ON tbqtln.SourcingResultNo = tbsrcrslt.SourcingResultId
			WHERE tbsrcrslt.CustomerRequirementNo = cr.CustomerRequirementId
			) AS QuoteId
		,(
			SELECT TOP 1 tbqt.QuoteNumber
			FROM tbSourcingResult tbsrcrslt --on tbsrcrslt.CustomerRequirementNo = cr.CustomerRequirementId                                          
			JOIN tbQuoteLine tbqtln ON tbqtln.SourcingResultNo = tbsrcrslt.SourcingResultId
			JOIN tbQuote tbqt ON tbqtln.QuoteNo = tbqt.QuoteId
			WHERE tbsrcrslt.CustomerRequirementNo = cr.CustomerRequirementId
			) AS QuoteNo
		,cr.REQStatus
		,CASE --when ((select top 1  IsSoftDelete from tbSourcingResult tbasrc            
			-- join tbautosource tbauto on cr.bommanagerno=tbauto.bommanagerno and cr.CustomerRequirementId = tbauto.CustomerRequirementId           
			-- where tbasrc.CustomerRequirementNo = cr.CustomerRequirementId and isnobid=1 and REQStatus<2 and tbauto.isdeleted <>1)=1) then 'Recalled NoBid'            
			WHEN (
					(
						SELECT TOP 1 IsSoftDelete
						FROM tbSourcingResult tbasrc
						WHERE tbasrc.CustomerRequirementNo = cr.CustomerRequirementId
							AND REQStatus = 3
						) = 1
					)
				THEN 'Recalled'
			WHEN (tbreqstts.[Name]) <> 'New'
				THEN tbreqstts.[Name]
			WHEN cr.isnobid = 1
				THEN 'NoBid'
			ELSE ('Awaiting Offer')
			END AS ReqStatusText
		,CASE 
			WHEN (
					SELECT count(1)
					FROM tbautosource tbasrc
					WHERE tbasrc.BOMManagerNo = bom.BOMManagerId
						AND tbasrc.customerrequirementid = cr.customerrequirementid
						AND isnull(tbasrc.isdeleted, 0) = 0
					) > 0
				THEN convert(BIT, 1)
			ELSE convert(BIT, 0)
			END AS AutoSourcingStatus
		,@TotalRecords AS TotalRecords
		,CASE 
			WHEN (cr.ReqStatus = 3)
				THEN (
						SELECT count(1)
						FROM tbAutoSource ar
						WHERE ar.CustomerRequirementId = cr.CustomerRequirementID
							AND isnull(ar.isdeleted, 0) = 0
							AND ar.BomManagerNo = @BOMManagerNo
						)
			WHEN (cr.ReqStatus > 3)
				THEN (
						SELECT Count(1)
						FROM tbSourcingResult sr
						JOIN tbautosource tbasrc ON sr.AutoSourceID = tbasrc.SourceId
						WHERE tbasrc.CustomerRequirementid = cr.CustomerRequirementId
							AND isnull(tbasrc.isdeleted, 0) = 0
							AND isnull(sr.IsSoftDelete, 0) = 0
						)
			ELSE '0'
			END AS OfferCount
		,bom.GeneratedFilename
		,(
			SELECT EmployeeName
			FROM tblogin
			WHERE loginid = cr.POHubReleaseBy
			) AS POHubReleaseName
		,pr.Productid
		,pr.ProductName + ' ->' + pr.ProductDescription + ' ->' + Pr.DutyCode + ' (' + cast(ISNULL(dbo.ufn_get_productdutyrate(pr.ProductId, getdate()), 0) AS NVARCHAR(18)) + ')' AS ProductDescription
		,dbo.ufn_get_MfrNotes(mf.ManufacturerId,@ClientID) AS MfrAdvisoryNotes
	FROM dbo.tbCustomerRequirement cr WITH(NOLOCK)
	JOIN dbo.tbCompany co WITH(NOLOCK) ON cr.CompanyNo = co.CompanyId
	LEFT JOIN dbo.tbCurrency cu WITH(NOLOCK) ON cr.CurrencyNo = cu.CurrencyId
	LEFT JOIN dbo.tbLogin lg WITH(NOLOCK) ON cr.Salesman = lg.LoginId
	--LEFT JOIN dbo.tbContact cn  ON cr.ContactNo = cn.ContactId                                                                      
	LEFT JOIN dbo.tbProduct pr WITH(NOLOCK) ON cr.ProductNo = pr.ProductId
	LEFT JOIN dbo.tbPackage pk WITH(NOLOCK) ON cr.PackageNo = pk.PackageId
	LEFT JOIN dbo.tbManufacturer mf WITH(NOLOCK) ON cr.ManufacturerNo = mf.ManufacturerId
	--LEFT JOIN dbo.tbUsage us   ON cr.UsageNo = us.UsageId                                                          
	--LEFT JOIN dbo.tbReason re   ON cr.ReasonNo = re.ReasonId                                                                      
	--LEFT JOIN dbo.tbDivision dv ON lg.DivisionNo = dv.DivisionId                                                    
	LEFT JOIN tbCompanyType ct WITH(NOLOCK) ON ct.CompanyTypeId = co.TypeNo
	LEFT JOIN tbBOMManager bom WITH(NOLOCK) ON bom.BOMManagerId = cr.BOMManagerNo
	LEFT JOIN tbCurrency cob WITH(NOLOCK) ON bom.CurrencyNo = cob.CurrencyId
	LEFT JOIN tbClient c WITH(NOLOCK) ON cr.ClientNo = c.ClientId
	LEFT JOIN dbo.tbLogin stm WITH(NOLOCK) ON cr.SupportTeamMemberNo = stm.LoginId
	LEFT JOIN tbreqstatus tbreqstts WITH(NOLOCK) ON cr.reqstatus = tbreqstts.REQStatusId
	WHERE cr.BOMManagerNo = @BOMManagerNo
		AND cr.FullPart = CASE 
			WHEN isnull(@Part, '') = ''
				THEN cr.fullpart
			ELSE @Part
			END
	ORDER BY cr.sequence ASC
		--OFFSET @skip ROWS                                        
		--FETCH NEXT @Rpp ROWS ONLY                                    
END
GO


