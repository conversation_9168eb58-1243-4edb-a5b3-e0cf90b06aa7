﻿//-----------------------------------------------------------------------------------------
// RP 30.12.2009:
// - don't let default values override saved state
// 
// RP 23.12.2009:
// - push rendering of saved state onto server
// 
// RP 14.10.2009:
// - added Lock / Unlock buttons for saving state
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.ComponentModel;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Text;
using System.Collections.Generic;
using System.Collections.Specialized;
using System.Collections;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {

	public class Base : Nuggets.Base, INamingContainer {

		protected DataListNugget _objDataListNugget;
		public FlexiDataTable _tbl = new FlexiDataTable();
		protected Panel _pnlNoData;
		public Panel _pnlFilters;
		protected PagingButtons _ctlPagingButtonsTop;
		protected PagingButtons _ctlPagingButtonsBottom;
		public Filter _ctlFilter;
		private ArrayList _aryFilterFieldIDs = new ArrayList();
		protected PlaceHolder _plhButtons;
		protected MultiSelectionCount _ctlMultiSelectionCount;
		protected Label _lblSelectModeResults;
		protected ReboundTextBox _txtLimitResults;
		List<string> _lstButtonIDs = new List<string>();
		protected IconButton _ibtnCancel;
		protected string _strDataListNuggetSubType = "";
		protected ConcurrentDictionary<string, string> _dctFilterIDs = new ConcurrentDictionary<string, string>();
		protected bool _blnHasSavedState = false; 
		protected int _intCurrentTab;

		#region Properties

		/// <summary>
		/// Client IDs of filter field controls
		/// </summary>
		protected ConcurrentDictionary<string, string> _dctFilterFieldClientIDs = new ConcurrentDictionary<string, string>();
		public ConcurrentDictionary<string, string> FilterFieldClientIDs {
			get { return _dctFilterFieldClientIDs; }
			set { _dctFilterFieldClientIDs = value; }
		}

		/// <summary>
		/// Is the Filter initially open?
		/// </summary>
		private bool _blnIsFilterInitiallyOpen = true;
		public bool IsFilterInitiallyOpen {
			get { return _blnIsFilterInitiallyOpen; }
			set { _blnIsFilterInitiallyOpen = value; }
		}

		private bool _blnAllowSelection = false;
		public bool AllowSelection {
			get { return _blnAllowSelection; }
			set { _blnAllowSelection = value; }
		}

		private int _intResultsLimit = 50;
		public int ResultsLimit {
			get { return _intResultsLimit; }
			set { _intResultsLimit = value; }
		}

		private bool _blnAllowSavingState = true;
		public bool AllowSavingState {
			get { return _blnAllowSavingState; }
			set { _blnAllowSavingState = value; }
		}

		private bool _blnInitiallySaveState = Convert.ToBoolean(SessionManager.SaveDataListNuggetStateByDefault);
		public bool InitiallySaveState {
			get { return _blnInitiallySaveState; }
			set { _blnInitiallySaveState = value; }
		}

		private bool _blnBypassState = false;
		public bool BypassState {
			get { return _blnBypassState; }
			set { _blnBypassState = value; }
		}

		private DataListNuggetState _objSavedState;
		public DataListNuggetState SavedState {
			get { return _objSavedState; }
			set { _objSavedState = value; }
		}

		protected ViewLevelList _enmViewLevel = ViewLevelList.My;
		public ViewLevelList ViewLevel {
			get { return _enmViewLevel; }
			set { _enmViewLevel = value; }
		}

		#endregion

		#region Events

		public event EventHandler AskPageToChangeTab;
		protected void OnAskPageToChangeTab() {
			if (this.AskPageToChangeTab != null) {
				AskPageToChangeTab(this, new EventArgs());
			}
		}

		#endregion

		#region Overrides

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.DataListNuggets._Bases.Base.js");
			((Pages.Base)Page).AddCSSFile("DataListNuggets.css");
		}

		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			GetSavedState(); //virtual sub called by subclass to get state from data store, populates _objSavedState
			if (_objSavedState != null) _blnHasSavedState = _objSavedState.HasState;
			if (ctlDesignBase.Links != null) ctlDesignBase.pnlBox.CssClass += " dataListNuggetWithButtons";
			SetupScriptDescriptors();
			if (_blnAllowSelection) Functions.SetCSSVisibility(_ctlFilter._ibtnOff, false);
			BypassState = _objQSManager.BypassSavedState;
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			if (_blnBypassState) _blnInitiallySaveState = false;
			if (!_blnBypassState && !_blnAllowSelection) RenderSavedState();
			_scScriptControlDescriptor.AddProperty("enmViewLevel", _enmViewLevel);
			base.OnPreRender(e);
		}

		protected override void CreateChildControls() {
			base.CreateChildControls();

			if (_blnAllowSelection) {
				Panel pnl = ControlBuilders.CreatePanelInsideParent(ctlDesignBase.pnlLinks, "dlnSelectModeLinks");
				_ctlMultiSelectionCount = new MultiSelectionCount();
				pnl.Controls.Add(_ctlMultiSelectionCount);
				_plhButtons = ControlBuilders.CreatePlaceHolderInsideParent(pnl);
				Panel pnlRight = ControlBuilders.CreatePanelInsideParent(pnl, "dlnSelectModeRight");
				_lblSelectModeResults = ControlBuilders.CreateLabelInsideParent(pnlRight, "dlnSelectModeResults");
				ControlBuilders.CreateLiteralInsideParent(pnlRight, Functions.GetGlobalResource("Misc", "ResultsLimit") + ":&nbsp;");
				_txtLimitResults = new ReboundTextBox();
				_txtLimitResults.ID = "txtLimitResults";
				_txtLimitResults.TextBoxMode = ReboundTextBox.TextBoxModeList.Numeric;
				_txtLimitResults.Width = 40;
				pnlRight.Controls.Add(_txtLimitResults);
			} else {
				//top paging controls
				_ctlPagingButtonsTop = new PagingButtons();
				_ctlPagingButtonsTop.ShowLockButtons = _blnAllowSavingState;
				ctlDesignBase.pnlLinks.Controls.Add(_ctlPagingButtonsTop);
				_ibtnCancel = new IconButton();
				_ibtnCancel.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
				_ibtnCancel.IconGroup = IconButton.IconGroupList.Nugget;
				_ibtnCancel.IconTitleResource = "Cancel";
				_ibtnCancel.Alignment = "Left";
				_ibtnCancel.CssClass = "invisible dlnCancelButton";
				_ibtnCancel.ID = "ibtnCancel";
				ctlDesignBase.pnlLinks.Controls.Add(_ibtnCancel);

				//bottom paging controls
				_ctlPagingButtonsBottom = new PagingButtons();
				_ctlPagingButtonsBottom.ShowLockButtons = _blnAllowSavingState;
				ctlDesignBase.pnlFooterContent.Controls.Add(_ctlPagingButtonsBottom);
			}

			//filters panel
			_pnlFilters = ControlBuilders.CreatePanel();
			_pnlFilters.CssClass = (_blnIsFilterInitiallyOpen) ? "" : "invisible";
			ctlDesignBase.pnlBoxInner.Controls.AddAt(3, _pnlFilters);

			//add Filters container
			if (ctlDesignBase.Filters != null) {
				Container cnt = new Container();
				ctlDesignBase.Filters.InstantiateIn(cnt);
				_pnlFilters.Controls.Add(cnt);
				cnt.Dispose(); cnt = null;
				_ctlFilter = (Filter)Functions.FindControlRecursive(_pnlFilters, "ctlFilter");
				if (_ctlFilter != null) {
					//Add filter fields to collections for javascript
					_ctlFilter.MakeChildControls();
					_dctFieldIDs.Clear();
					_dctFilterIDs.Clear();
					_aryFilterFieldIDs.Clear();
					foreach (Control ctl in _ctlFilter.tblColLeft.Controls) { AddFilterField(ctl); }
					foreach (Control ctl in _ctlFilter.tblColRight.Controls) { AddFilterField(ctl); }
				}
			}

			//No data found
			_pnlNoData = ControlBuilders.CreatePanel("noData");
			Functions.SetCSSVisibility(_pnlNoData, false);
			ControlBuilders.CreateLiteralInsideParent(_pnlNoData, Functions.GetGlobalResource("NotFound", "Generic"));
			ctlDesignBase.pnlContent.Controls.Add(_pnlNoData);

			//table
			_tbl.ID = "tbl";
			_tbl.InsideDataListNugget = true;
			_tbl.CssClass = "dataListNugget";
			_tbl.PageSize = (int)SessionManager.DefaultListPageSize; //default page size
			ctlDesignBase.pnlContent.Controls.Add(_tbl);
		}
		#endregion

		#region Methods

		private void AddFilterField(Control ctl) {
			if (ctl is FilterDataItemRows.Base) {
				_aryFilterFieldIDs.Add(ctl.ClientID);
				if (!_dctFieldIDs.ContainsKey(ctl.ID)) _dctFilterFieldClientIDs.TryAdd(ctl.ID, (ctl.ClientID));
				_dctFilterIDs.TryAdd(((FilterDataItemRows.Base)ctl).FilterField, ctl.ID);
				((FilterDataItemRows.Base)ctl).SetDefaultValue();
			}
		}

		private void RenderSavedState() {
			if (!_blnHasSavedState) return;
			_tbl.PageSize = _objSavedState.PageSize;
			_tbl.SortColumnDirection = (SortColumnDirection)_objSavedState.SortDirection;
			_tbl.SortColumnIndex = _objSavedState.SortIndex;
			_tbl.CurrentPage = _objSavedState.Page;
			foreach (DataListNuggetFilterState st in _objSavedState.FilterStates) {
				FilterDataItemRows.Base flt = GetFilter(st.Name);
				if (flt != null) {
					//reset filter to remove any defaults
					flt.Reset();

					//now set the filter value if it is on
					if (st.IsOn) {
						SetFilterValue(flt, st.Name, st.Value, st.Comparison);
						flt.Enable(st.IsShown);
					}
				}
			}
			RenderAdditionalState(); //raise virtual sub so subclasses can add state
		}

		protected void SetFilterValue(FilterDataItemRows.Base flt, string strName, object objValue, Controls.DropDowns.NumericalComparison.NumericalComparisonType enmNumericalComparisonType) {
			if (flt is FilterDataItemRows.CheckBox) {
				if (objValue == null || objValue.ToString() == "") objValue = false;
				((FilterDataItemRows.CheckBox)flt).SetInitialValue(Convert.ToBoolean(objValue));
			} else if (flt is FilterDataItemRows.DropDown) {
				((FilterDataItemRows.DropDown)flt).SetInitialValue(objValue);
			} else if (flt is FilterDataItemRows.DateSelect) {
				((FilterDataItemRows.DateSelect)flt).SetInitialValue(objValue);
			} else if (flt is FilterDataItemRows.Numerical) {
				((FilterDataItemRows.Numerical)flt).SetInitialValue(objValue.ToString(), Convert.ToInt32(enmNumericalComparisonType));
			} else if (flt is FilterDataItemRows.StarRating) {
				if (objValue == null || objValue.ToString() == "") objValue = 0;
				((FilterDataItemRows.StarRating)flt).SetInitialValue(Convert.ToInt32(objValue), Convert.ToInt32(enmNumericalComparisonType));
			} else if (flt is FilterDataItemRows.TextBox) {
				((FilterDataItemRows.TextBox)flt).SetInitialValue(objValue.ToString());
			}
		}
		protected void SetFilterValue(string strName, object objValue, Controls.DropDowns.NumericalComparison.NumericalComparisonType enmNumericalComparisonType) {
			SetFilterValue(GetFilter(strName), strName, objValue, enmNumericalComparisonType);
		}
		protected void SetFilterValue(string strName, object objValue) {
			SetFilterValue(GetFilter(strName), strName, objValue, Rebound.GlobalTrader.Site.Controls.DropDowns.NumericalComparison.NumericalComparisonType.EqualTo);
		}



		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor.AddComponentProperty("Table", _tbl.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlFilters", _pnlFilters.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlNoData", _pnlNoData.ClientID);
			_scScriptControlDescriptor.AddProperty("blnIsFilterInitiallyOpen", _blnIsFilterInitiallyOpen);
			_scScriptControlDescriptor.AddProperty("blnAllowSelection", _blnAllowSelection);
			_scScriptControlDescriptor.AddProperty("intDataListNuggetID", _objDataListNugget.ID);
			_scScriptControlDescriptor.AddProperty("blnAllowSavingState", _blnAllowSavingState);
			_scScriptControlDescriptor.AddProperty("strDataListNuggetSubType", _strDataListNuggetSubType);
			_scScriptControlDescriptor.AddProperty("blnSaveState", _blnInitiallySaveState);
			if (_ctlFilter != null) {
				_scScriptControlDescriptor.AddElementProperty("ibtnReset", _ctlFilter._ibtnReset.ClientID);
				_scScriptControlDescriptor.AddElementProperty("ibtnApply", _ctlFilter._ibtnApply.ClientID);
				_scScriptControlDescriptor.AddElementProperty("ibtnOff", _ctlFilter._ibtnOff.ClientID);
				_scScriptControlDescriptor.AddProperty("aryFilterFieldIDs", _aryFilterFieldIDs);
				_scScriptControlDescriptor.AddProperty("objFilterFieldIDs", _dctFilterFieldClientIDs);
			}
			if (_blnAllowSelection) {
				_scScriptControlDescriptor.AddComponentProperty("ctlMultiSelectionCount", _ctlMultiSelectionCount.ClientID);
				_scScriptControlDescriptor.AddProperty("strSelectModeResultsText", Functions.GetGlobalResource("Misc", "ShowingXOfYResults"));
				_scScriptControlDescriptor.AddProperty("aryButtonIDs", _lstButtonIDs);
				_scScriptControlDescriptor.AddElementProperty("txtLimitResults", _txtLimitResults.ClientID);
				_scScriptControlDescriptor.AddProperty("intResultsLimit", _intResultsLimit);
				_scScriptControlDescriptor.AddElementProperty("lblSelectModeResults", _lblSelectModeResults.ClientID);
			} else {
				_scScriptControlDescriptor.AddProperty("intPageSize", (int)SessionManager.DefaultListPageSize);
				_scScriptControlDescriptor.AddComponentProperty("ctlPagingButtonsTop", _ctlPagingButtonsTop.ClientID);
				_scScriptControlDescriptor.AddComponentProperty("ctlPagingButtonsBottom", _ctlPagingButtonsBottom.ClientID);
				_scScriptControlDescriptor.AddElementProperty("ibtnCancel", _ibtnCancel.ClientID);

			}
		}

		protected void SetDataListNuggetType(string strType) {
			_objDataListNugget = _objSite.GetDataListNugget(strType);
		}

		protected void SetDataListNuggetType(string strType, string strSubType) {
			SetDataListNuggetType(strType);
			_strDataListNuggetSubType = strSubType;
		}

		protected void SetDataListNuggetType(string strType, Enum enmSubType) {
			SetDataListNuggetType(strType);
			_strDataListNuggetSubType = (Convert.ToInt32(enmSubType)).ToString();
		}

		protected void SetDataListNuggetType(int intDataListNuggetID) {
			_objDataListNugget = _objSite.GetDataListNugget(intDataListNuggetID);
		}

		protected void SetDataListNuggetType(int intDataListNuggetID, string strSubType) {
			SetDataListNuggetType(intDataListNuggetID);
			_strDataListNuggetSubType = strSubType;
		}

		protected void SetDataListNuggetType(int intDataListNuggetID, Enum enmSubType) {
			SetDataListNuggetType(intDataListNuggetID);
			_strDataListNuggetSubType = (Convert.ToInt32(enmSubType)).ToString();
		}

		protected void SetDataListNuggetType(Enum enmType) {
			_objDataListNugget = _objSite.GetDataListNugget(Convert.ToInt32(enmType));
		}

		protected void SetDataListNuggetType(Enum enmType, string strSubType) {
			SetDataListNuggetType(enmType);
			_strDataListNuggetSubType = strSubType;
		}

		protected void SetDataListNuggetType(Enum enmType, Enum enmSubType) {
			SetDataListNuggetType(enmType);
			_strDataListNuggetSubType = (Convert.ToInt32(enmSubType)).ToString();
		}

		/// <summary>
		/// Find control in Filters template
		/// </summary>
		/// <param name="strControlName">Control ID to find</param>
		/// <returns>The found Control or null</returns>
		public object FindFilterControl(string strControlName) {
			EnsureChildControls();
			if (_ctlFilter == null) return null;
			return _ctlFilter.FindFieldControl(strControlName);
		}

		public void MakeChildControls() {
			EnsureChildControls();
			ctlDesignBase.MakeChildControls();
		}

		public void AddButton(string strIconButtonTitleResource) {
			EnsureChildControls();
			IconButton ibtn = new IconButton();
			ibtn.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			ibtn.IconGroup = IconButton.IconGroupList.Nugget;
			ibtn.IconTitleResource = strIconButtonTitleResource;
			ibtn.Alignment = "Right";
			_plhButtons.Controls.Add(ibtn);
			_lstButtonIDs.Add(ibtn.ClientID);  
            
		}

        public void AddButton(string strIconButtonTitleResource, bool viewButton)
        {
            EnsureChildControls();
            IconButton ibtn = new IconButton();
            ibtn.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
            ibtn.IconGroup = IconButton.IconGroupList.Nugget;
            ibtn.IconTitleResource = strIconButtonTitleResource;
            ibtn.Alignment = "Right";
            ibtn.Visible = viewButton;
            _plhButtons.Controls.Add(ibtn);
            _lstButtonIDs.Add(ibtn.ClientID);
        }

		protected FilterDataItemRows.Base GetFilter(string strFilterField) {
			string strID = GetFilterID(strFilterField);
			if (strID == "") return null;
			return (FilterDataItemRows.Base)FindFilterControl(strID);
		}

		protected string GetFilterID(string strFilterField) {
			string strID = "";
			if (_dctFilterIDs.TryGetValue(strFilterField, out strID)) return strID;
			return "";
		}

		protected string GetSavedStateValue(string strFilterField) {
			if (_objSavedState == null) return "";
			return _objSavedState.GetStateValue(strFilterField);
		}

		protected void ResetAllState() {
			//clear all filters
			foreach (DataListNuggetFilterState st in _objSavedState.FilterStates) {
				FilterDataItemRows.Base flt = GetFilter(st.Name);
				if (flt != null) flt.Reset();
			}
			SavedState = null;
			InitiallySaveState = false;
		}

        public void DropDownList(string strIconButtonTitleResource)
        {
            EnsureChildControls();
            DropDowns.PoHubBuyer poHub = new Rebound.GlobalTrader.Site.Controls.DropDowns.PoHubBuyer();

            //DropDownList ddl = new DropDownList();
            ////ddl.icon
            //ddl.ID = ID;
            //ddl.Width = 200;
            //ddl.Items.Add(new ListItem("< Select >", ""));
            //ddl.Items.Add(new ListItem("One", "1"));
            //ddl.Items.Add(new ListItem("Two", "2"));
            //ddl.Items.Add(new ListItem("Three", "3"));

            HtmlGenericControl space = new HtmlGenericControl();
            space.InnerHtml = "&nbsp";

            //IconButton ibtn = new IconButton();
            //ibtn.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
            //ddl.IconGroup = IconButton.IconGroupList.Nugget;
            //ibtn.IconTitleResource = strIconButtonTitleResource;
            //ddl.Alignment = "Right";
            //_plhButtons.Controls.Add(ibtn);


            _plhButtons.Controls.Add(space);

            _plhButtons.Controls.Add(poHub);
            _lstButtonIDs.Add(poHub.ClientID);
            
        }
        public void DropDownListAssignUser(string strIconButtonTitleResource)
        {
            EnsureChildControls();
            DropDowns.AssignSecurityGroup poHub = new Rebound.GlobalTrader.Site.Controls.DropDowns.AssignSecurityGroup();

            //DropDownList ddl = new DropDownList();
            ////ddl.icon
            //ddl.ID = ID;
            //ddl.Width = 200;
            //ddl.Items.Add(new ListItem("< Select >", ""));
            //ddl.Items.Add(new ListItem("One", "1"));
            //ddl.Items.Add(new ListItem("Two", "2"));
            //ddl.Items.Add(new ListItem("Three", "3"));

            HtmlGenericControl space = new HtmlGenericControl();
            space.InnerHtml = "&nbsp";

            //IconButton ibtn = new IconButton();
            //ibtn.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
            //ddl.IconGroup = IconButton.IconGroupList.Nugget;
            //ibtn.IconTitleResource = strIconButtonTitleResource;
            //ddl.Alignment = "Right";
            //_plhButtons.Controls.Add(ibtn);


            _plhButtons.Controls.Add(space);

            _plhButtons.Controls.Add(poHub);
            _lstButtonIDs.Add(poHub.ClientID);

        }

        #endregion

        #region Virtual Methods

        protected virtual void RenderAdditionalState() { }
		protected virtual void GetSavedState() {
			SavedState = DataListNuggetStateManager.GetDataListNuggetState(_objDataListNugget.ID, _strDataListNuggetSubType);
		}
                
		#endregion

	}
}
