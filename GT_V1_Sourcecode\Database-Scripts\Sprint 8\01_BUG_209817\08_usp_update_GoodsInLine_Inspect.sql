SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('usp_update_GoodsInLine_Inspect','P') IS NOT NULL
DROP PROC [dbo].usp_update_GoodsInLine_Inspect
GO
/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
[BUG-209817]		cuongdx			   24-JUL-2024		    Alter	        Add quarantine status
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_update_GoodsInLine_Inspect]
    --**********************************************************************************************      
    --* SK 15.09.2009:      
    --* - Do not update PartMarkings, SerialNos or CountingMethod here      
    --*       
    --* SK 20.08.2009:      
    --* - PartMarkings is now a text column      
    --*       
    --* SK 09.08.2009:      
    --* - add PartMarkings, CountingMethod & SerialNosRecorded      
    --*       
    --* RP 03.06.2009:      
    --* - Added code from triggers here so we can pass more parameters and do more explicit updates      
    --**********************************************************************************************      
    @GoodsInLineId int,
    --  , @CountingMethodNo int = NULL      
    --  , @SerialNosRecorded bit = NULL      
    --  , @PartMarkings nvarchar(50) = NULL      
    @InspectedBy int,
    @Quarantined BIT = 0,
    @RowsAffected int = NULL OUTPUT
AS
DECLARE @IsInspected INT
SELECT @IsInspected = ISNULL(InspectedBy, 0)
FROM dbo.tbGoodsInLine
WHERE GoodsInLineId = @GoodsInLineId

IF @IsInspected = 0
BEGIN
    DECLARE @StockLogMsgNo INT = 31
    SET @StockLogMsgNo = CASE 
                        WHEN @Quarantined = 0 THEN 31
                        WHEN @Quarantined = 1 THEN 47
                        ELSE 31
                     END;
    UPDATE dbo.tbGoodsInLine
    SET InspectedBy = @InspectedBy,
        --    , CountingMethodNo = @CountingMethodNo      
        --    , SerialNosRecorded = @SerialNosRecorded      
        --    , PartMarkings = @PartMarkings      
        DateInspected = CURRENT_TIMESTAMP
    WHERE GoodsInLineId = @GoodsInLineId

    SELECT @RowsAffected = @@ROWCOUNT

    --updat Stock Log if we've done an Inspect      
    IF NOT @InspectedBy IS NULL
    BEGIN

        --declare vars for StockLog       
        DECLARE @StockNo int,
                @InStockQuantity int,
                @OnOrderQuantity int,
                @InspectedQuantity int,
                @StockLogNo int,
                @PurchaseOrderId int

        --get details for stockLog and the new values       
        SELECT @StockNo = st.StockId,
               @InStockQuantity = st.QuantityInStock,
               @OnOrderQuantity = st.QuantityOnOrder,
               @InspectedQuantity = gil.Quantity,
               @PurchaseOrderId = pol.PurchaseOrderNo
        FROM dbo.tbStock st
            LEFT JOIN tbGoodsInLine gil
                ON gil.PurchaseOrderLineNo = st.PurchaseOrderLineNo
                   AND gil.GoodsInLineId = st.GoodsInLineNo
            LEFT JOIN tbPurchaseOrderLine pol
                ON gil.PurchaseOrderLineNo = pol.PurchaseOrderLineId
        WHERE GoodsInLineId = @GoodsInLineId

        --insert stock log      
        EXEC usp_insert_StockLog @StockLogTypeNo = @StockLogMsgNo,                 --Inspected ReceivePO      
                                 @StockNo = @StockNo,                  --      
                                 @QuantityInStock = @InStockQuantity,  --      
                                 @QuantityOnOrder = @OnOrderQuantity,  --      
                                 @ActionQuantity = @InspectedQuantity, --      
                                 @PurchaseOrderNo = @PurchaseOrderId,  --      
                                 @UpdatedBy = @InspectedBy,            --      
                                 @StockLogId = @StockLogNo OUTPUT
    END
end
else
begin
    SELECT @RowsAffected = 1
end