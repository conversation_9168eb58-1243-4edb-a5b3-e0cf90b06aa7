Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/PurchaseTaxCode");this._objData.set_DataObject("PurchaseTaxCode");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Taxes)for(n=0;n<t.Taxes.length;n++)this.addOption(t.Taxes[n].Name,t.Taxes[n].ID,t.Taxes[n].Code)}};Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);