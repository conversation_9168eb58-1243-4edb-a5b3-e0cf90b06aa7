﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
============================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-211896]		Phuc Hoang			18-Sep-2024		UPDATE			[PROD Bug] DHL - Rebound API invoice BBX
============================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_AddUpdateParentInvoiceDHL]                                             
(                                                
  @StatementId Int = 0,                                              
  @AWBNo bigint,                    
  @ParentID varchar(100),                    
  @ChildID bigint,                    
  @InvoiceLabel varchar(500),                    
  @ParentLabel varchar(500),                    
  @InvoiceNo varchar(500), 
  @ClientNo INT = 0,
  @WPXJson nvarchar(max),
  @Message varchar(300) OUTPUT,                        
  @IsValid bit OUTPUT,                      
  @ReturnId int OUTPUT                       
)                                     
AS    
--------25-04-2023--------------   
--Created By: Manoj Kumar                                              
--Created On: 07 oct 2022//Altered By Manoj :26-dec=2022                          
--Purpose: To add/update parent invoice                                           
BEGIN        
  Declare @ShipperNo varchar(50);
  
    IF EXISTS(SELECT 'X' FROM ParentInvoiceAWBDHL WHERE AWBNo = @AWBNo and IsActive=1)                          
		BEGIN                          
			update ParentInvoiceAWBDHL set IsActive=0 where AWBNo= @AWBNo;      
		END   
    IF Exists(Select 'X' from ParentInvoiceAWBDHL where IsActive=1)  
		Begin  
			update ParentInvoiceAWBDHL set IsActive=0 where ParentInvoiceAWBDHLId in   
			(Select pia.ParentInvoiceAWBDHLId  
			from ParentInvoiceAWBDHL pia  
			Join ChildInvoiceAWBDHL cia on pia.childId=cia.childId     
			where pia.IsActive=1 and cia.SendStatus=0)  
		End  
   
    select @ShipperNo=ShipperNo from ChildInvoiceAWBDHL      
    where AWBNo=@AWBNo and SendStatus=1; 
      
    INSERT INTO ParentInvoiceAWBDHL (AWBNo,ParentID,ChildID,InvoiceFile,InvoiceLabel,InvoiceNo)                                              
    VALUES (@AWBNo,@ParentID,@ChildID,@invoiceLabel,@ParentLabel,reverse(stuff(reverse(@InvoiceNo), 1, 1, '')))                      
                              
    Declare @ParentInvoiceAWBDHLId int=0;                    
    Set @ParentInvoiceAWBDHLId=SCOPE_IDENTITY();                    
                          
    exec usp_AddUpdateChildInvoiceDHL 1, @AWBNo, @ParentID, @ChildID, '', 0, @ParentInvoiceAWBDHLId, @InvoiceNo,               
    @ParentLabel, @InvoiceLabel, @WPXJson,'', 0, 0,@ShipperNo, 0, @Message, @IsValid, @ReturnId;       
          
    SELECT ROW_NUMBER()over(order by value)Id,value InvoiceNo,@ParentID ParentID into #tmpUpdateInvoice                   
    FROM STRING_SPLIT(@InvoiceNo, ','); 
                          
    update t set t.AirWayBill=ci.AWBNo, t.IsUpsInvoiceExported=1, t.DHLParentInvoiceNo = @ParentID                 
    from #tmpUpdateInvoice ti 
    join tbInvoice t on t.InvoiceNumber=ti.InvoiceNo 
    join ChildInvoiceAWBDHL ci on ci.InvoiceNo=ti.InvoiceNo and ci.ParentID=ti.ParentID                  
    where ci.IsActive=1 and t.ClientNo=@ClientNo
                          
    drop table #tmpUpdateInvoice;                  
                           
    SET @Message = 'Declare statement has been added successfully.'                            
    SET @IsValid = 1                        
    SET @ReturnId =  @ParentInvoiceAWBDHLId                    
END   
GO


