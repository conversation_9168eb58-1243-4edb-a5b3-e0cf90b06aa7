﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
=============================================================================================================================================
[US-239025]		    Phuc Hoang			09-Apr-2025		UPDATE			Utility - Xmatch to display Salesperson for Requirements and Invoices
=============================================================================================================================================
*/


CREATE OR ALTER VIEW [dbo].[vw_re_Cust_Req]  
--* SG 20.04.2022  
--* - Added CustomerRequirementNo  
AS  
SELECT  lg.EmployeeName                      AS Salesperson,  
        CAST(cr.ReceivedDate AS DATE)        AS [Date],  
        co.CompanyName                       AS Customer,  
        cr.Part                              AS [Part Number],  
        ISNULL(cr.Quantity, 0)               AS Qty,  
        mf.ManufacturerName                  AS Mfg,  
        cr.DateCode                          AS DC,  
        ISNULL(cr.Price * cr.Quantity, 0)    AS Value,  
        cu.CurrencyCode                      AS Funds,  
        cr.FullPart                          AS FullPN,  
        dbo.ufn_get_basepart(cr.Part)        AS BasePN,  
        cr.ClientNo                          AS ClientNo,  
        cr.Price                             AS Price,  
        cr.CustomerRequirementNumber         AS CustomerRequirementNumber,  
        ct.ClientName                        AS ClientName,  
        cr.ContactNo                         AS ContactNo,  
        cr.DatePromised                      AS DatePromised,  
        cr.BOMNo                             AS BOMNo,  
        cr.CustomerRequirementId             AS CustomerRequirementNo,  
        dr.ServiceName                       AS 'Type',
		lgcom.EmployeeName                   AS CompanySalesperson
          
FROM    dbo.tbCustomerRequirement AS cr    WITH (NoLock) INNER JOIN  
        dbo.tbCompany AS co                WITH (NoLock) ON cr.CompanyNo = co.CompanyId INNER JOIN  
        dbo.tbClient AS ct                 WITH (NoLock) ON cr.ClientNo = ct.ClientId LEFT OUTER JOIN  
        dbo.tbCurrency AS cu               WITH (NoLock) ON cr.CurrencyNo = cu.CurrencyId LEFT OUTER JOIN  
        dbo.tbLogin AS lg                  WITH (NoLock) ON cr.Salesman = lg.LoginId LEFT OUTER JOIN  
		dbo.tbLogin AS lgcom               WITH (NoLock) ON co.Salesman = lgcom.LoginId LEFT OUTER JOIN 
        dbo.tbManufacturer AS mf           WITH (NoLock) ON mf.ManufacturerId = cr.ManufacturerNo  
        join tbRequirementDropDownData dr on cr.ReqType=dr.Id 
GO

IF NOT EXISTS (SELECT * FROM sys.fn_listextendedproperty(N'MS_DiagramPane1' , N'SCHEMA',N'dbo', N'VIEW',N'vw_re_Cust_Req', NULL,NULL))
	EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "cr"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 114
               Right = 274
            End
            DisplayFlags = 280
            TopColumn = 32
         End
         Begin Table = "co"
            Begin Extent = 
               Top = 114
               Left = 38
               Bottom = 222
               Right = 270
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "tbClient"
            Begin Extent = 
               Top = 6
               Left = 312
               Bottom = 114
               Right = 523
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "cu"
            Begin Extent = 
               Top = 222
               Left = 38
               Bottom = 330
               Right = 215
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "lg"
            Begin Extent = 
               Top = 222
               Left = 253
               Bottom = 330
               Right = 425
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "mf"
            Begin Extent = 
               Top = 330
               Left = 38
               Bottom = 438
               Right = 210
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 10
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
      ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Cust_Req'
ELSE
BEGIN
	EXEC sys.sp_updateextendedproperty @name=N'MS_DiagramPane1', @value=N'[0E232FF0-B466-11cf-A24F-00AA00A3EFFF, 1.00]
Begin DesignProperties = 
   Begin PaneConfigurations = 
      Begin PaneConfiguration = 0
         NumPanes = 4
         Configuration = "(H (1[40] 4[20] 2[20] 3) )"
      End
      Begin PaneConfiguration = 1
         NumPanes = 3
         Configuration = "(H (1 [50] 4 [25] 3))"
      End
      Begin PaneConfiguration = 2
         NumPanes = 3
         Configuration = "(H (1 [50] 2 [25] 3))"
      End
      Begin PaneConfiguration = 3
         NumPanes = 3
         Configuration = "(H (4 [30] 2 [40] 3))"
      End
      Begin PaneConfiguration = 4
         NumPanes = 2
         Configuration = "(H (1 [56] 3))"
      End
      Begin PaneConfiguration = 5
         NumPanes = 2
         Configuration = "(H (2 [66] 3))"
      End
      Begin PaneConfiguration = 6
         NumPanes = 2
         Configuration = "(H (4 [50] 3))"
      End
      Begin PaneConfiguration = 7
         NumPanes = 1
         Configuration = "(V (3))"
      End
      Begin PaneConfiguration = 8
         NumPanes = 3
         Configuration = "(H (1[56] 4[18] 2) )"
      End
      Begin PaneConfiguration = 9
         NumPanes = 2
         Configuration = "(H (1 [75] 4))"
      End
      Begin PaneConfiguration = 10
         NumPanes = 2
         Configuration = "(H (1[66] 2) )"
      End
      Begin PaneConfiguration = 11
         NumPanes = 2
         Configuration = "(H (4 [60] 2))"
      End
      Begin PaneConfiguration = 12
         NumPanes = 1
         Configuration = "(H (1) )"
      End
      Begin PaneConfiguration = 13
         NumPanes = 1
         Configuration = "(V (4))"
      End
      Begin PaneConfiguration = 14
         NumPanes = 1
         Configuration = "(V (2))"
      End
      ActivePaneConfig = 0
   End
   Begin DiagramPane = 
      Begin Origin = 
         Top = 0
         Left = 0
      End
      Begin Tables = 
         Begin Table = "cr"
            Begin Extent = 
               Top = 6
               Left = 38
               Bottom = 114
               Right = 274
            End
            DisplayFlags = 280
            TopColumn = 32
         End
         Begin Table = "co"
            Begin Extent = 
               Top = 114
               Left = 38
               Bottom = 222
               Right = 270
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "tbClient"
            Begin Extent = 
               Top = 6
               Left = 312
               Bottom = 114
               Right = 523
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "cu"
            Begin Extent = 
               Top = 222
               Left = 38
               Bottom = 330
               Right = 215
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "lg"
            Begin Extent = 
               Top = 222
               Left = 253
               Bottom = 330
               Right = 425
            End
            DisplayFlags = 280
            TopColumn = 0
         End
         Begin Table = "mf"
            Begin Extent = 
               Top = 330
               Left = 38
               Bottom = 438
               Right = 210
            End
            DisplayFlags = 280
            TopColumn = 0
         End
      End
   End
   Begin SQLPane = 
   End
   Begin DataPane = 
      Begin ParameterDefaults = ""
      End
      Begin ColumnWidths = 10
         Width = 284
         Width = 1500
         Width = 1500
         Width = 1500
      ' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Cust_Req'
END
GO

IF NOT EXISTS (SELECT * FROM sys.fn_listextendedproperty(N'MS_DiagramPane2' , N'SCHEMA',N'dbo', N'VIEW',N'vw_re_Cust_Req', NULL,NULL))
	EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPane2', @value=N'   Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 4815
         Alias = 2025
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 3495
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Cust_Req'
ELSE
BEGIN
	EXEC sys.sp_updateextendedproperty @name=N'MS_DiagramPane2', @value=N'   Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
         Width = 1500
      End
   End
   Begin CriteriaPane = 
      Begin ColumnWidths = 11
         Column = 4815
         Alias = 2025
         Table = 1170
         Output = 720
         Append = 1400
         NewValue = 1170
         SortType = 1350
         SortOrder = 1410
         GroupBy = 1350
         Filter = 3495
         Or = 1350
         Or = 1350
         Or = 1350
      End
   End
End
' , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Cust_Req'
END
GO

IF NOT EXISTS (SELECT * FROM sys.fn_listextendedproperty(N'MS_DiagramPaneCount' , N'SCHEMA',N'dbo', N'VIEW',N'vw_re_Cust_Req', NULL,NULL))
	EXEC sys.sp_addextendedproperty @name=N'MS_DiagramPaneCount', @value=2 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Cust_Req'
ELSE
BEGIN
	EXEC sys.sp_updateextendedproperty @name=N'MS_DiagramPaneCount', @value=2 , @level0type=N'SCHEMA',@level0name=N'dbo', @level1type=N'VIEW',@level1name=N'vw_re_Cust_Req'
END
GO


