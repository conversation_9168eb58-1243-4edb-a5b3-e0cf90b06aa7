Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.prototype={get_tblUploadHistory:function(){return this._tblUploadHistory},set_tblUploadHistory:function(n){this._tblUploadHistory!==n&&(this._tblUploadHistory=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/CsvUploadHistory";this._strDataObject="CsvUploadHistory";this.addRefreshEvent(Function.createDelegate(this,this.getHistory));this.showLoading(!1);this.showContent(!0);this.showContentLoading(!1);this.getHistory()},dispose:function(){this.isDisposed||(this._tblUploadHistory&&this._tblUploadHistory.dispose(),this._tblUploadHistory=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.callBaseMethod(this,"dispose"))},getHistory:function(){this.showLoadingHistory(!0);this.showContent(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLog");n.addDataOK(Function.createDelegate(this,this.getHistoryOK));n.addError(Function.createDelegate(this,this.getHistoryError));n.addTimeout(Function.createDelegate(this,this.getHistoryError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getHistoryOK:function(n){res=n._result;this.showLoading(!1);this._tblUploadHistory.clearTable();this.processHistory(this._tblUploadHistory);this._tblUploadHistory.resizeColumns()},getHistoryError:function(n){this.showLoading(!1);this.showHistoryError(!0,n.get_ErrorMessage())},showLoadingHistory:function(n){this.showLoading(n)},showHistoryGetData:function(n){n&&this.showLoading(!1)},processHistory:function(n){var i,t,r;if(res.CreditHist)for(i=0;i<res.CreditHist.length;i++)t=res.CreditHist[i],r=[$R_FN.setCleanTextValue(t.Message),t.Date],n.addRow(r,t.ID,!1),t=null,r=null}};Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);