Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.Ellipses=function(n){Rebound.GlobalTrader.Site.Controls.Ellipses.initializeBase(this,[n]);this._objData=null};Rebound.GlobalTrader.Site.Controls.Ellipses.prototype={get_hypValue:function(){return this._hypValue},set_hypValue:function(n){this._hypValue!==n&&(this._hypValue=n)},get_hyp:function(){return this._hyp},set_hyp:function(n){this._hyp!==n&&(this._hyp=n)},get_lblLoading:function(){return this._lblLoading},set_lblLoading:function(n){this._lblLoading!==n&&(this._lblLoading=n)},get_lblError:function(){return this._lblError},set_lblError:function(n){this._lblError!==n&&(this._lblError=n)},get_blnShowParentheses:function(){return this._blnShowParentheses},set_blnShowParentheses:function(n){this._blnShowParentheses!==n&&(this._blnShowParentheses=n)},get_blnShowParenthesesAfterResult:function(){return this._blnShowParenthesesAfterResult},set_blnShowParenthesesAfterResult:function(n){this._blnShowParenthesesAfterResult!==n&&(this._blnShowParenthesesAfterResult=n)},addSetupData:function(n){this.get_events().addHandler("SetupData",n)},removeSetupData:function(n){this.get_events().removeHandler("SetupData",n)},onSetupData:function(){var n=this.get_events().getHandler("SetupData");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Ellipses.callBaseMethod(this,"initialize");this._hyp&&$addHandler(this._hyp,"click",Function.createDelegate(this,this.getData));this._hypValue&&$addHandler(this._hypValue,"click",Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._hyp&&$clearHandlers(this._hyp),this._hypValue&&$clearHandlers(this._hypValue),this._objData&&this._objData.dispose(),this._objData=null,this._hypValue=null,this._hyp=null,this._lblLoading=null,this._lblError=null,this._blnShowParentheses=null,this._blnShowParenthesesAfterResult=null,Rebound.GlobalTrader.Site.Controls.Ellipses.callBaseMethod(this,"dispose"),this.isDisposed=!0)},getData:function(){this.showLoading();this._objData=new Rebound.GlobalTrader.Site.Data;this.onSetupData();this._objData.addDataOK(Function.createDelegate(this,this.getDataComplete));this._objData.addTimeout(Function.createDelegate(this,this.showTimeOut));this._objData.addError(Function.createDelegate(this,this.showError));this._objData.addTimeout(Function.createDelegate(this,this.showError));$R_DQ.addToQueue(this._objData,!1);$R_DQ.processQueue();this._objData=null},getDataComplete:function(n){var t=n._result.Value;this._blnShowParenthesesAfterResult&&(t=String.format("({0})",t));$R_FN.setInnerHTML(this._hypValue,t);this.showContent()},hide:function(){$R_FN.showElement(this.get_element(),!1)},show:function(){$R_FN.showElement(this.get_element(),!0)},reset:function(){$R_FN.showElement(this._hypValue,!1);$R_FN.showElement(this._lblLoading,!1);$R_FN.showElement(this._lblError,!1);$R_FN.showElement(this._hyp,!0)},showLoading:function(){$R_FN.showElement(this._hypValue,!1);$R_FN.showElement(this._lblLoading,!0);$R_FN.showElement(this._lblError,!1);$R_FN.showElement(this._hyp,!1)},showError:function(){this.reset()},showTimeOut:function(){this.reset()},showContent:function(){$R_FN.showElement(this._hypValue,!0);$R_FN.showElement(this._lblLoading,!1);$R_FN.showElement(this._lblError,!1);$R_FN.showElement(this._hyp,!1)}};Rebound.GlobalTrader.Site.Controls.Ellipses.registerClass("Rebound.GlobalTrader.Site.Controls.Ellipses",Sys.UI.Control,Sys.IDisposable);