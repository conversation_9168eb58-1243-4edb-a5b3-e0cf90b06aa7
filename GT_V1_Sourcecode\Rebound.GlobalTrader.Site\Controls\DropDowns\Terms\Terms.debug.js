///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Terms = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.Terms.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Terms.prototype = {
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DropDowns.Terms.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intGlobalLoginClientNo=null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.Terms.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 		
		this._objData.set_PathToData("controls/DropDowns/Terms");
		this._objData.set_DataObject("Terms");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("GlobalLoginClientNo", this._intGlobalLoginClientNo);

	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Terms) {
			for (var i = 0; i < result.Terms.length; i++) {
				this.addOption(result.Terms[i].Name, result.Terms[i].ID);
			}
		}
	}

};

Rebound.GlobalTrader.Site.Controls.DropDowns.Terms.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Terms", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
