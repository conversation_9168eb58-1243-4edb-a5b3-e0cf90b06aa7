Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.CommunicationLogType=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.CommunicationLogType.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.CommunicationLogType.prototype={get_blnIncludeNewSystemDocuments:function(){return this._blnIncludeNewSystemDocuments},set_blnIncludeNewSystemDocuments:function(n){this._blnIncludeNewSystemDocuments!==n&&(this._blnIncludeNewSystemDocuments=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.CommunicationLogType.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._blnIncludeNewSystemDocuments=null,Rebound.GlobalTrader.Site.Controls.DropDowns.CommunicationLogType.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/CommunicationLogType");this._objData.set_DataObject("CommunicationLogType");this._objData.set_DataAction("GetData");this._objData.addParameter("IncludeNewSystemDocuments",this._blnIncludeNewSystemDocuments)},dataCallOK:function(){var r=this._objData._result,t,i,n;if(r.Types){for(t=r.Types,i=0;i<t.length;i++)n=t[i],this._blnIncludeNewSystemDocuments?this.addOption(n.Name,n.ID,n.Type):this.addOption(n.Name,n.ID),n=null;t=null}}};Rebound.GlobalTrader.Site.Controls.DropDowns.CommunicationLogType.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.CommunicationLogType",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);