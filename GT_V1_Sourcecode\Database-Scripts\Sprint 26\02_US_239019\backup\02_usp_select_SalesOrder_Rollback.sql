/*   
===========================================================================================  
TASK        UPDATED BY       DATE          ACTION    DESCRIPTION  
[001]   <PERSON>   20/01/2020  Update   Add two column GlobalCurrencyName,LocalCurrencyid  
[RP-1238]  <PERSON>  09-10-2023  Update   If EI selected and not booked, checked + Request approval button should be greyed out  
[RP-236]  Abhinav Saxena  10-01-2024  Update   For RP-236.  
[RP-2623]  Abhinav Saxena  18-01-2024  Update   For RP-2623.  
[US-201037]  An.TranTan   22-04-2024  Update   Add flag to decide disable OGEL field.  
===========================================================================================  
*/  
  
CREATE OR ALTER PROCEDURE [dbo].[usp_select_SalesOrder]                  
 @SalesOrderId int,            
 @LoginId      INT=0,            
 @Clientno     INT=0                                          
AS   
BEGIN                             
 DECLARE @IsPaidByCreditCard bit=0;                               
 --[001]                            
Declare @SOLineNo int=0;                            
Declare @WareHouseNo int=0;                            
select top 1 @SOLineNo = salesorderlineid from tbsalesorderLine where SalesOrderNo = @SalesOrderId and Posted=1                            
select top 1 @WareHouseNo = sk.warehouseno from  dbo.tbAllocation al JOIN  tbStock sk ON sk.StockId = al.StockNo                             
where al.SalesOrderLineNo= @SOLineNo                                      
declare @LocalCurrencyNo int=0;                            
select @LocalCurrencyNo = LocalCurrencyNo from tbwarehouse where WarehouseId = @WareHouseNo         
        
---Start RP-2623---        
DECLARE @IsRedFlaggedSO BIT=0;        
DECLARE @LineSerialNo NVARCHAR(500)='';        
CREATE TABLE #RedFlagCheckTbl        
(        
ID INT IDENTITY(1,1),        
DatePromised DATETIME,        
Quantity INT,        
QuantityInStock  INT,        
SOSerialNo     INT,        
SalesOrderNo   INT        
)        
        
INSERT INTO #RedFlagCheckTbl        
SELECT         
sol.DatePromised,        
sol.Quantity,        
ISNULL((Select sum(isnull(QuantityInStock,0))  from tbStock stk                              
                             JOIN dbo.tbAllocation  al on al.StockNo = stk.StockId                               
                             WHERE sol.SalesOrderLineId = al.SalesOrderLineNo ),0) AS QuantityInStock        
,sol.SOSerialNo,sol.SalesOrderNo         
FROM tbSalesOrderLine sol WHERE sol.SalesOrderNo=@SalesOrderId AND ISNULL(sol.Closed,0)=0       
        
        
SELECT @IsRedFlaggedSO=CAST(CASE WHEN COUNT(1)>0 THEN 1 ELSE 0 END AS BIT) FROM #RedFlagCheckTbl        
WHERE --Quantity!=QuantityInStock AND     
(DATEDIFF(DAY, GETDATE(),DatePromised ))<=0        
        
SELECT  @LineSerialNo=STUFF((SELECT ', ' + CAST(SOSerialNo AS NVARCHAR(100))        
                      FROM #RedFlagCheckTbl b         
                      WHERE b.SalesOrderNo = a.SalesOrderNo     
  --AND b.Quantity!=b.QuantityInStock     
  AND (DATEDIFF(DAY, GETDATE(),b.DatePromised ))<=0        
       ORDER BY SOSerialNo ASC        
                      FOR XML PATH('')), 1, 1, '')        
FROM #RedFlagCheckTbl a WHERE a.SalesOrderNo=@SalesOrderId        
GROUP BY SalesOrderNo        
        
----END-----        
                               
--[001]                    
                
    
 IF EXISTS(SELECT 1 FROM tbSOPaymentInfo WHERE SalesOrderNo = @SalesOrderId AND INACTIVE=0)                                    
    SET @IsPaidByCreditCard = CAST(1 AS BIT)                                    
    SELECT  so.*                                          
          , soval.LineSubTotal                                          
          , soval.TotalTax                                          
          , soval.TotalValue                                          
    , dbo.ufn_check_CurrencyInSameFaimly(so.ClientNo,so.CurrencyNo) as IsCurrencyInSameFaimly                                       
    ,@IsPaidByCreditCard AS IsPaidByCreditCard                                     
 ,ISNULL(w.WarehouseName,'') AS PreferredWarehouseName                                  
 ,iSNULL(w.WarehouseId,0) AS  PreferredWarehouseNo                                  
 , g.GlobalCurrencyName                                  
  ,iSNULL(@LocalCurrencyNo,0) as LocalCurrencyid                         
  , CAST([dbo].[ufn_OGELGetSOExportApproved] (@SalesOrderId) AS BIT) AS IsExportApprove                      
  ,[dbo].[ufn_SOAUthDisabledReason] (@SalesOrderId) AS SOAuthDisabledReason                        
  ,c.PurchasingNotes              
  ,so.CompanyOnStop              
  , dbo.ufn_GetSecurityPermissions(@LoginId,@Clientno,30005032) AS IsAllowCheckViewPermission          
  , ISNULL(so.IsAllowReadyTOShip,0)    AS AllowReadyToShipTicked          
  , ISNULL(@IsRedFlaggedSO,0) AS RedFlagged        
  , ISNULL(@LineSerialNo,'') AS LineSerialNo          
  --, @EiSelectedButNotBooked as EiSelectedButNotBooked  
  , dbo.ufn_check_OGEL_Disabled(@SalesOrderId) AS IsOGELDisabled --[US-201037]  
    FROM    vwSalesOrder so                                          
    JOIN    dbo.ufn_get_salesOrder_values(@SalesOrderId) soval ON so.SalesOrderId = soval.SalesOrderId                                     
 LEFT JOIN dbo.tbCompany c ON c.CompanyId = so.CompanyNo                                  
 LEFT JOIN dbo.tbWarehouse w ON w.WarehouseId = c.WarehouseNo                              
 --[001] code start                                      
 left join tbGlobalCurrencyList g on g.GlobalCurrencyId = so.GlobalCurrencyNo                                  
 --[001] code end                              
    WHERE   so.SalesOrderId = @SalesOrderId   
END  