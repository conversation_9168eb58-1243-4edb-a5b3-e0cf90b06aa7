Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.initializeBase(this,[n]);this._intCompanyAddressID=-1;this._intCompanyID=-1;this._blnCanEditTax=!1;this._vatNo=""};Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.prototype={get_intCompanyAddressID:function(){return this._intCompanyAddressID},set_intCompanyAddressID:function(n){this._intCompanyAddressID!==n&&(this._intCompanyAddressID=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strTitleEdit:function(){return this._strTitleEdit},set_strTitleEdit:function(n){this._strTitleEdit!==n&&(this._strTitleEdit=n)},get_strTitleAdd:function(){return this._strTitleAdd},set_strTitleAdd:function(n){this._strTitleAdd!==n&&(this._strTitleAdd=n)},get_strExplanationEdit:function(){return this._strExplanationEdit},set_strExplanationEdit:function(n){this._strExplanationEdit!==n&&(this._strExplanationEdit=n)},get_strExplanationAdd:function(){return this._strExplanationAdd},set_strExplanationAdd:function(n){this._strExplanationAdd!==n&&(this._strExplanationAdd=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addModeChanged(Function.createDelegate(this,this.changeTitleOnMode))},dispose:function(){this.isDisposed||(this._ctlAddress&&this._ctlAddress.dispose(),this._ctlAddress=null,this._intCompanyAddressID=null,this._intCompanyID=null,this._strTitleEdit=null,this._strTitleAdd=null,this._strExplanationEdit=null,this._strExplanationAdd=null,this._blnCanEditTax=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._strPathToData="controls/Nuggets/CompanyAddresses",this._strDataObject="CompanyAddresses",this.addSaveClick(Function.createDelegate(this,this.saveClicked)),this.addCancelClick(Function.createDelegate(this,this.cancelClicked)),this._ctlAddress=$find(this.getField("ctlAddress").ID),this._ctlAddress._ctlRelatedForm=this);this.resetFormFields();this.setFormFieldsToDefaults();this.getFieldControl("ctlCountry")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlShipVia")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlTax")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldDropDownData("ctlLabelType");this.getFieldDropDownData("ctlCountry");this.getFieldDropDownData("ctlShipVia");this.getFieldDropDownData("ctlTax");this.showField("ctlIncoterm",!0);this.getFieldDropDownData("ctlIncoterm");this.getFieldDropDownData("ctlRegion");this.getFieldDropDownData("ctlDivisionHeader");this._mode=="ADD"?(this.showField("ctlTax",!0),this.allowEditingTax(!0),this.showField("ctlVatNo",!0),this.allowEditingVat(!0)):(this.showField("ctlTax",this._blnCanEditTax),this.allowEditingTax(this._blnCanEditTax),this.showField("ctlVatNo",this._blnCanEditTax),this.allowEditingVat(this._blnCanEditTax))},saveClicked:function(){if(this.resetFormFields(),this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);this._mode=="ADD"?n.set_DataAction("AddNew"):(n.set_DataAction("SaveEdit"),n.addParameter("ID",this._intCompanyAddressID));n.addParameter("CMNo",this._intCompanyID);n.addParameter("Name",this.getFieldValue("ctlAddressName"));n.addParameter("Line1",this.getFieldValue("ctlLine1"));n.addParameter("Line2",this.getFieldValue("ctlLine2"));n.addParameter("Line3",this.getFieldValue("ctlLine3"));n.addParameter("County",this.getFieldValue("ctlCounty"));n.addParameter("City",this.getFieldValue("ctlTown"));n.addParameter("State",this.getFieldValue("ctlState"));n.addParameter("Country",this.getFieldValue("ctlCountry"));n.addParameter("Zip",this.getFieldValue("ctlPostcode"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("ShipViaNo",this.getFieldValue("ctlShipVia"));n.addParameter("ShipViaAccount",this.getFieldValue("ctlShipViaAccount"));n.addParameter("TaxbyAddress",this.getFieldValue("ctlTax"));n.addParameter("IncotermNo",this.getFieldValue("ctlIncoterm"));n.addParameter("ShippingNotes",this.getFieldValue("ctlShippingNotes"));n.addParameter("VatNo",this.getFieldValue("ctlVatNo"));n.addParameter("RNotes",this.getFieldValue("ctlRecieveNotes"));n.addParameter("Region",this.getFieldValue("ctlRegion"));n.addParameter("DivisionHeaderNo",this.getFieldValue("ctlDivisionHeader"));n.addParameter("LabelTypeNo",this.getFieldValue("ctlLabelType"));n.addDataOK(Function.createDelegate(this,this.saveAddEditComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},saveError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveAddEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():this.saveError(n)},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():this.saveError(n)},validateForm:function(){var n=this.autoValidateFields();return this._ctlAddress.validateFields()||(n=!1),n||this.showError(!0),n},cancelClicked:function(){this.onCancel()},changeTitleOnMode:function(){switch(this._mode){case"ADD":this.changeTitle(this._strTitleAdd);this.changeExplanation(this._strExplanationAdd);break;case"EDIT":this.changeTitle(this._strTitleEdit);this.changeExplanation(this._strExplanationEdit)}},allowEditingTax:function(n){this.showField("ctlTax",n);this.showField("ctlTax_Label",!n)},allowEditingVat:function(n){this.showField("ctlVatNo",n);this.showField("ctlVatNo_Label",!n)}};Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_AddEdit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);