﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class PackageDetails {
		
		#region Constructors
		
		public PackageDetails() { }
		
		#endregion
		
		#region Properties
	
		/// <summary>
		/// PackageId (from Table)
		/// </summary>
		public System.Int32 PackageId { get; set; }
		/// <summary>
		/// PackageName (from Table)
		/// </summary>
		public System.String PackageName { get; set; }
		/// <summary>
		/// PackageDescription (from Table)
		/// </summary>
		public System.String PackageDescription { get; set; }
		/// <summary>
		/// Inactive (from Table)
		/// </summary>
		public System.Boolean Inactive { get; set; }
		/// <summary>
		/// UpdatedBy (from Table)
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP (from Table)
		/// </summary>
		public System.DateTime DLUP { get; set; }
        /// <summary>
        /// ClientId (from Table)
        /// </summary>
        public System.Int32 ClientId { get; set; }
        /// <summary>
        /// ClientName (from Table)
        /// </summary>
        public System.String ClientName { get; set; }
        /// <summary>
        /// ParentClientNo (from Table)
        /// </summary>
        public System.Int32? ParentClientNo { get; set; }
        /// <summary>
        /// CurrencyNo (from Table)
        /// </summary>
        public System.Int32 CurrencyNo { get; set; }
        /// <summary>
        /// Telephone (from Table)
        /// </summary>
        public System.String Telephone { get; set; }
        /// <summary>
        /// Fax (from Table)
        /// </summary>
        public System.String Fax { get; set; }
        /// <summary>
        /// EMail (from Table)
        /// </summary>
        public System.String EMail { get; set; }
        /// <summary>
        /// URL (from Table)
        /// </summary>
        public System.String URL { get; set; }
        /// <summary>
        /// ClientBillTo (from Table)
        /// </summary>
        public System.String ClientBillTo { get; set; }
        /// <summary>
        /// CustomerCode (from Table)
        /// </summary>
        public System.String CustomerCode { get; set; }
        /// <summary>
        /// TaxNo (from Table)
        /// </summary>
        public System.Int32 TaxNo { get; set; }
        /// <summary>
        /// TermsNo (from Table)
        /// </summary>
        public System.Int32 TermsNo { get; set; }
        /// <summary>
        /// ShipViaNo (from Table)
        /// </summary>
        public System.Int32 ShipViaNo { get; set; }
        /// <summary>
        /// ShipViaName (from Table)
        /// </summary>
        public System.String ShipViaName { get; set; }
        /// <summary>
        /// TermsName (from Table)
        /// </summary>
        public System.String TermsName { get; set; }
        /// <summary>
        /// TaxName (from Table)
        /// </summary>
        public System.String TaxName { get; set; }
        public System.Int32 MasterLoginId { get; set; }
        public System.String ADLoginName { get; set; }
        public System.String EmployeeName { get; set; }
        /// <summary>
		/// ECCNId
		/// </summary>
		public System.Int32 ECCNId { get; set; }
        /// <summary>
        /// ECCNCode
        /// </summary>
        public System.String ECCNCode { get; set; }
        public System.Boolean? ECCNStatus { get; set; }
        public System.String ECCNWarning { get; set; }

        //
        public System.String addressName { get; set; }
        public System.String line1 { get; set; }
        public System.String line2 { get; set; }
        public System.String line3 { get; set; }
        public System.String city { get; set; }
        public System.String county { get; set; }
        public System.String state { get; set; }
        public System.Int32? countryNo { get; set; }
        public System.String zip { get; set; }
        /// <summary>
        #endregion

    }
}