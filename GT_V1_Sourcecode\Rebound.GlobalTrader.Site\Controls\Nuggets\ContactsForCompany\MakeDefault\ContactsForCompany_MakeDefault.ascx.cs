//Marker     Changed by      Date         Remarks
//[001]      Vinay           09/10/2012   Degete Ref:#26#  - Add two more columns contact to identify Default Purchase ledger and Default Sales ledger
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class ContactsForCompany_MakeDefault : Base {

		#region Locals

		#endregion

		#region Properties

		private int _intContactID;
		public int ContactID {
			get { return _intContactID; }
			set { _intContactID = value; }
		}

		/// <summary>
		/// CompanyID
		/// </summary>
		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "ContactsForCompany_MakeDefault");
			AddScriptReference("Controls/Nuggets/ContactsForCompany/MakeDefault/ContactsForCompany_MakeDefault");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			WireUpControls();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.ContactsForCompany_MakeDefault", ctlDesignBase.ClientID);

			_scScriptControlDescriptor.AddProperty("strExplain_PO", Functions.GetGlobalResource("FormExplanations", "ContactsForCompany_MakeDefaultPO"));
			_scScriptControlDescriptor.AddProperty("strExplain_SO", Functions.GetGlobalResource("FormExplanations", "ContactsForCompany_MakeDefaultSO"));

            //[001] code start
            _scScriptControlDescriptor.AddProperty("strExplain_POLedger", Functions.GetGlobalResource("FormExplanations", "ContactsForCompany_MakeDefaultPOLedger"));
            _scScriptControlDescriptor.AddProperty("strExplain_SOLedger", Functions.GetGlobalResource("FormExplanations", "ContactsForCompany_MakeDefaultSOLedger"));
            //[001] code end

			_scScriptControlDescriptor.AddProperty("strTitle_PO", Functions.GetGlobalResource("FormTitles", "ContactsForCompany_MakeDefaultPO"));
			_scScriptControlDescriptor.AddProperty("strTitle_SO", Functions.GetGlobalResource("FormTitles", "ContactsForCompany_MakeDefaultSO"));

            //[001] code start
            _scScriptControlDescriptor.AddProperty("strTitle_POLedger", Functions.GetGlobalResource("FormTitles", "ContactsForCompany_MakeDefaultPOLedger"));
            _scScriptControlDescriptor.AddProperty("strTitle_SOLedger", Functions.GetGlobalResource("FormTitles", "ContactsForCompany_MakeDefaultSOLedger"));
            //[001] code end
		}

	}
}