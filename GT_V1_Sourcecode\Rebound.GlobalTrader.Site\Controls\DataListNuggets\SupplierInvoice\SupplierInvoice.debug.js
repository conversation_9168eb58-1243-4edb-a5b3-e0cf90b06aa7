///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[001]      Vinay           11/06/2013         CR:- Supplier Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.prototype = {

    get_blnShowCanNotBeExported: function () { return this._blnShowCanNotBeExported; }, set_blnShowCanNotBeExported: function (v) { if (this._blnShowCanNotBeExported !== v) this._blnShowCanNotBeExported = v; },

    initialize: function () {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/SupplierInvoice";
        this._strDataObject = "SupplierInvoice";
        this.showFilterField("ctlStatus", (!this._blnShowCanNotBeExported));
        this.showFilterField("ctlStatusReason", this._blnShowCanNotBeExported);
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.callBaseMethod(this, "initialize");
    },


    initAfterBaseIsReady: function () {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._blnShowCanNotBeExported = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function () {
        this._table._intCurrentPage = 1;
        this._blnShowCanNotBeExported = (this._intCurrentTab == 1);
        this.showFilterField("ctlStatus", (this._intCurrentTab == 0));
        this.showFilterField("ctlStatusReason", (this._intCurrentTab == 1));
        this.getData();
    },

    setupDataCall: function () {
        this._objData.addParameter("CanNotBeExported", this._blnShowCanNotBeExported);
    },

    getDataOK: function () {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
                row.CanBeExported ? "<input class='SupplierInvoiceCheckbox' type='checkbox' name='SelectSupplierInvoice" + row.ID + "'" + "value='" + row.ID + "' checked disabled>"
                    : (row.MatchPercentage != null && parseInt(row.MatchPercentage) == 100) ? "<input class='SupplierInvoiceCheckbox' type='checkbox' name='SelectSupplierInvoice" + row.ID + "'" + "value='" + row.ID + "'>"
                        : "<input class='SupplierInvoiceCheckbox' type='checkbox' name='SelectSupplierInvoice" + row.ID + "'" + "value='" + row.ID + "' disabled>"
                , $RGT_nubButton_SupplierInvoice(row.ID, row.No)
                , $RGT_nubButton_Company(row.CompanyNo, row.Name)
                , $R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(row.GI, row.GINo), (!row.HasIPO) ? $RGT_nubButton_PurchaseOrder(row.PONo, row.PO) : $RGT_nubButton_InternalPurchaseOrder(row.PONo, row.PO))
                , $R_FN.setCleanTextValue(row.URNNumber)
                , $R_FN.setCleanTextValue(row.INVDate)
                , $R_FN.writePartNo(row.Part, "")
                , row.Value
                , $R_FN.writeDoubleCellValue(row.ItemCount, (row.MatchPercentage != null ? "Matched: " + row.MatchPercentage + "%" : ""))
                //, row.ItemCount
                //, (row.MatchPercentage != null ? "Matched: " + row.MatchPercentage + "%" : "")
                /*, "Test Summary"*/
                , "<a class='SupplierInvoiceViewDetail' data-value='" + row.ID + "'>View Detail</a>"
            ];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }

        $('.SupplierInvoiceCheckbox').change(function () {
            if ($(this).prop("checked")) {
                $("[value='" + $(this).prop("value") + "']").prop("checked", true);
            }
            else {
                $("[value='" + $(this).prop("value") + "']").prop("checked", false);
            }
        });

        $(".SupplierInvoiceViewDetail").click(function () {
            var SupplierInvoiceID = $(this).data("value");
            $R_FN.openSupplierInvoiceDetailWindow(SupplierInvoiceID);
        });
    }


};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);