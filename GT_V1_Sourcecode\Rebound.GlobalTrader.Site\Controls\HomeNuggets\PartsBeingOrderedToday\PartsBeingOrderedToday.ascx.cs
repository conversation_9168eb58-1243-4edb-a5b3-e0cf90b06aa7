//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class PartsBeingOrderedToday : Base {

		protected Panel _pnlPartsOrdered;
		protected SimpleDataTable _tblPartsOrdered;
		protected PageHyperLink _lnkMore;

		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "PartsBeingOrderedToday";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.PartsBeingOrderedToday.PartsBeingOrderedToday.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlPartsOrdered", _pnlPartsOrdered.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblPartsOrdered", _tblPartsOrdered.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlMore", FindContentControl("pnlMore").ClientID);
			_lnkMore.AddQueryStringVariable(QueryStringManager.QueryStringVariables.BypassSavedState, true);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblPartsOrdered.Columns.Add(new SimpleDataColumn("PurchaseOrder", Unit.Pixel(65)));
			_tblPartsOrdered.Columns.Add(new SimpleDataColumn("Part"));
			_tblPartsOrdered.Columns.Add(new SimpleDataColumn("Value"));
			_tblPartsOrdered.Columns.Add(new SimpleDataColumn("Buyer", Unit.Pixel(125)));
		}

		private void WireUpControls() {
			_pnlPartsOrdered = (Panel)FindContentControl("pnlPartsOrdered");
			_tblPartsOrdered = (SimpleDataTable)FindContentControl("tblPartsOrdered");
			_lnkMore = (PageHyperLink)FindContentControl("lnkMore");
		}

	}
}