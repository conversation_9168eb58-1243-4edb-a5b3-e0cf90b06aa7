﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_GetExcelStockHeaderColumnFrom_PriceQuote', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_GetExcelStockHeaderColumnFrom_PriceQuote
END
GO

CREATE PROCEDURE [dbo].[usp_GetExcelStockHeaderColumnFrom_PriceQuote]    
 @ClientId INT,                            
 @UserId INT ,                        
 @SelectedClientID INT                           
AS                            
BEGIN                            
  SELECT DISTINCT VALUE AS ColumnHeading, 'Column' + cast(ROW_NUMBER ()OVER(ORDER BY PriceQuoteImportHeaderId) AS VARCHAR(10)) AS ExcelHeaderid              
  from BorisGlobalTraderimports.dbo.[tbPriceQuoteImportColumnHeading]                    
 --select value as ColumnHeading, concat('Column',ROW_NUMBER ()over(order by ExcelHeaderid))as ExcelHeaderid  from BorisGlobalTraderimports.dbo.tbExcelStockColumnHeading                       
cross apply                      
(                      
    values                      
        ('Column1', Column1),                      
        ('Column2', Column2),                      
        ('Column3', Column3),                      
        ('Column4', Column4),                      
        ('Column5', Column5),                      
        ('Column6', Column6),                      
        ('Column7', Column7),                      
        ('Column8', Column8),                      
        ('Column9', Column9),                      
        ('Column10', Column10),                      
        ('Column11', Column11),                      
        ('Column12', Column12),          
        ('Column12', Column13) ,          
        ('Column12', Column14) ,          
        ('Column12', Column15) ,      
        ('Column16', Column16),          
        ('Column17', Column17) ,          
        ('Column18', Column18) ,          
        ('Column19', Column19) ,     
        ('Column20', Column20) ,          
        ('Column21', Column21) ,          
        ('Column22', Column22),  
  ('Column23', Column23),
   ('Column24', Column24) 
) c(Column1, value)                         
WHERE  ClientId = @ClientId and SelectedClientID=@SelectedClientID AND CreatedBy = @UserId  and VALUE is not null                      
ORDER BY ExcelHeaderid ASC                      
                           
END
GO


