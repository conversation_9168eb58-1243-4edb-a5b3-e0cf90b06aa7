﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



CREATE OR ALTER PROCEDURE [dbo].[usp_update_Quote]           
--******************************************************************************************          
--* SK 15.02.2010:          
--* - add IncotermNo          
--*Marker     changed by      date          Remarks        
--*[001]      Vinay           21/01/2014   CR:- Add AS9120 Requirement in GT application        
--******************************************************************************************          
    @QuoteId int          
  , @Notes nvarchar(max) = NULL          
  , @Instructions nvarchar(max) = NULL          
  , @Closed bit          
  , @ContactNo int          
  , @DateQuoted datetime          
  , @CurrencyNo int          
  , @Salesman int          
  , @DivisionNo int          
  , @Freight float = NULL          
  , @TermsNo int = NULL          
  , @IncotermNo int = NULL           
  , @UpdatedBy int = NULL          
  --[001] code start        
  , @AS9120 bit = NULL         
  --[001] code end        
  , @IsImportant bit = 0        
  , @QuoteStatus int = 0       
  , @SupportTeamMemberNo int=null  
  , @DivisionHeaderNo int  =null    
  , @RowsAffected int = NULL OUTPUT          
          
AS   
begin          
          
    DECLARE @Count integer          
          
    SELECT  @Count = count(*)          
    FROM    dbo.tbQuote          
    WHERE   QuoteId = @QuoteId          
          
    IF @Count > 0           
        BEGIN          
            UPDATE  dbo.tbQuote          
            SET     Notes = @Notes          
                  , Instructions = @Instructions          
                  , Closed = @Closed          
                  , ContactNo = @ContactNo          
                  , DateQuoted = @DateQuoted          
                  , CurrencyNo = @CurrencyNo          
                  , Salesman = @Salesman          
                  , DivisionNo = @DivisionNo          
                  , Freight = @Freight          
                  , TermsNo = @TermsNo          
                  , IncotermNo = @IncotermNo          
                  , UpdatedBy = @UpdatedBy          
                  , DLUP = CURRENT_TIMESTAMP          
                  --[001] code start        
                  , AS9120= @AS9120        
                  --[001] code end        
      , IsImportant = @IsImportant        
      , QuoteStatus = @QuoteStatus      
   ,SupportTeamMemberNo=@SupportTeamMemberNo 
   ,DivisionHeaderNo=@DivisionHeaderNo       
            WHERE   QuoteId = @QuoteId          
        END          
          
    SELECT  @RowsAffected = @@ROWCOUNT   
end


GO


