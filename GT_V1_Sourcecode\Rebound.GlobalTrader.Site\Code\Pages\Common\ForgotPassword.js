Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.ForgotPassword");Rebound.GlobalTrader.Site.Pages.ForgotPassword=function(){this._intPauseTime=250;this._selectedType=0};Rebound.GlobalTrader.Site.Pages.ForgotPassword.prototype={get_ctlUser:function(){return this._ctlUser},set_ctlUser:function(n){this._ctlUser!==n&&(this._ctlUser=n)},initialize:function(){for(var t,n=0;n<2;n++)t=$get(String.format("{0}_{1}","ctlForgotChoice_ctl02_rad",n)),t&&$addHandler(t,"click",Function.createDelegate(this,this.changedSelection)),t=null;this.changedSelection()},dispose:function(){this.isDisposed||(this._pnlLogin=null,this.isDisposed=!0)},changedSelection:function(){this.getSelectedItem();$R_FN.showElement(document.getElementById("ctlUsername"),this._selectedType==0);$R_FN.showElement(document.getElementById("ctlUserEmail"),this._selectedType==1);$R_FN.showElement(document.getElementById("ctlActiveClient"),this._selectedType==1)},getSelectedItem:function(){for(var t,n=0;n<2;n++)if(t=$get(String.format("{0}_{1}","ctlForgotChoice_ctl02_rad",n)),t&&t.checked){this._selectedType=Number.parseInvariant(t.value.toString());return}},doReboundClientChoice:function(n,t){Rebound.GlobalTrader.Site.WebServices.DoReboundClientChoice(n,t,Function.createDelegate(this,this.doReboundClientChoiceOK))},doReboundClientChoiceOK:function(n){n.length>0&&(location.href=n)}};Rebound.GlobalTrader.Site.Pages.ForgotPassword.registerClass("Rebound.GlobalTrader.Site.Pages.ForgotPassword",null,Sys.IDisposable);