﻿/*  
===========================================================================================  
TASK			UPDATED BY      DATE         ACTION    DESCRIPTION  
[US-203561]		An.TranTan		22-Aug-2024  UPDATE    Get SellPriceLessReason
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_SourcingResult]                                                                                    
@SourcingResultId int                                                                                                                          
AS             
--***************************************************************************************************                                             
--* Action: Altered  By: Abhinav Saxena  Date: 08-09-2023 Comment: For RP-2228  (AS6081).      
--* [002] Action: Altered  By: Abhinav Saxena  Date: 12-09-2023 Comment: For RP-2340  (AS6081).      
--***************************************************************************************************                                                        
BEGIN                                      
    declare @MyNotes varchar(100)=null                                   
   set @MyNotes =[dbo].[ufn_get_spq_sourcingResult_new] (@SourcingResultId,'SR')                                  
                                                    
SELECT  sr.SourcingResultId                                                    
 , sr.CustomerRequirementNo                                                    
 , sr.SourcingTable                                                    
 , sr.SourcingTableItemNo                                                    
 , sr.TypeName                                                    
 , sr.FullPart                                                    
 , sr.Part                                                    
 , sr.ManufacturerNo                                                    
 , sr.DateCode                                                    
 , sr.ProductNo                                                    
 , sr.PackageNo                                                    
 , sr.Quantity                                                    
 , sr.Price                                                    
 , sr.CurrencyNo                                                    
 , sr.OriginalEntryDate                                                    
 , sr.Salesman                                                    
 , sr.SupplierNo                                                    
 , sr.UpdatedBy                                                    
 , sr.DLUP                                                    
 , sr.ROHS                                                    
 , sr.OfferStatusNo                                                    
 , sr.OfferStatusChangeDate                                                    
 , sr.OfferStatusChangeLoginNo                                                    
 , sr.Notes                                                    
 , co.CompanyName AS SupplierName                                                    
 , mf.ManufacturerName                                                    
 , mf.ManufacturerCode                                                    
 , cr.CustomerPart                                                    
 , sr.SupplierPrice                                                  
 , pco.CompanyName AS POHubSupplierName                                                 
 , sr.POHubCompanyNo                                              
 , pco.UPLiftPrice                                                  
 , sr.ClientCompanyNo                                                    
 , coc.CompanyName AS ClientSupplierName                                          
 , sr.EstimatedShippingCost                                           
 , sr.ClientCurrencyNo                                           
 , sr.DeliveryDate                                      
 ,sr.SPQ                                      
 ,sr.LeadTime                                      
 ,sr.ROHSStatus                                      
 ,sr.FactorySealed                                      
 ,sr.MSL                                         
 ,sr.SupplierMOQ                                      
 ,sr.SupplierTotalQSA                                     
 ,sr.SupplierLTB                                     
 ,sr.SupplierNotes                                     
 ,cr.ClientNo                                     
 ,sr.regionNo                    
 , cu.CurrencyCode                                     
 , sr.ActualCurrencyNo                                    
 , cua.CurrencyCode as ActualCurrencyCode                                    
 , sr.ActualPrice                              
,@MyNotes as SourcingNotes                                  
  ,pr.ProductDescription                                  
 --, isnull(pr.Inactive,0) as ProductInactive                                  
 , CAST(0 as bit) as ProductInactive                                  
 , cr.MSL                                  
 , sr.MSLLevelNo                                 
 , sr.SupplierWarranty                              
 , ISNULL(ct.NonPreferredCompany,0) as NonPreferredCompany                                
 , ml.MSLLevel                             
 , sr.PriorityNo                        
 , cr.CountryOfOriginNo as IHSCountryOfOriginNo                         
 , coo.GlobalCountryName  as IHSCountryOfOriginName                         
 , sr.IHSCountryOfOriginNo  as CountryOfOriginNo                   
 , pkg.PackageDescription                  
 , sr.partWatchMatch                  
 , sr.PartWatchMatchHUBIPO              
 , ISNULL(sr.TypeOfSupplierNo,0) AS TypeOfSupplierNo             
 , ISNULL(sr.ReasonForSupplierNo,0) AS ReasonForSupplierNo            
 , ISNULL(sr.RiskOfSupplierNo,0) AS RiskOfSupplierNo       
 , ISNULL(cr.AS6081,0) AS AS6081 --[002]     
 , udf.IsCountryFound    
 , udf.CountryName    
 , udf.CountryNo
 , sr.SellPriceLessReason
 , ISNULL(sr.TestRecommended, CAST(0 AS BIT)) as IsTestingRecommended
FROM    dbo.tbSourcingResult sr                                                    
LEFT JOIN dbo.tbCompany co ON co.CompanyId    = sr.SupplierNo                                                
LEFT  JOIN dbo.tbManufacturer mf   ON mf.ManufacturerId   = sr.ManufacturerNo                                
LEFT JOIN dbo.tbCustomerRequirement cr   ON cr.CustomerRequirementId = sr.CustomerRequirementNo                                                   
LEFT JOIN dbo.tbCompany pco   ON pco.CompanyId    = sr.POHubCompanyNo                                                
LEFT  JOIN dbo.tbCompany coc  ON sr.ClientCompanyNo    = coc.CompanyId                                           
LEFT JOIN tbCurrency cu on sr.CurrencyNo = cu.CurrencyId                                       
LEFT JOIN tbCurrency cua on sr.ActualCurrencyNo = cua.CurrencyId                                     
LEFT JOIN tbProduct pr on sr.ProductNo=pr.ProductId                                
LEFT JOIN tbCompanyType ct on co.TypeNo = ct.CompanyTypeId                                       
LEFT JOIN tbMSLLevel ml on sr.MSLLevelNo = ml.MSLLevelId                       
LEFT JOIN  tbGlobalCountryList coo on cr.CountryOfOriginNo=coo.GlobalCountryId                     
LEFT JOIN tbPackage pkg on pkg.PackageId=sr.PackageNo     
CROSS APPLY [dbo].ufn_AS6081_GetSupplierCountryDetails(sr.SourcingResultId,pco.CompanyId) udf               
WHERE   sr.SourcingResultId   = @SourcingResultId             
END;   