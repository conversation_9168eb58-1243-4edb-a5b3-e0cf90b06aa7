﻿/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
[US-223401]     An.TranTan		 21-Feb-2025		UPDATE		Add uploaded rows count
=========================================================================================================================================================
*/
--IF NOT EXISTS (
--    SELECT 1 
--    FROM INFORMATION_SCHEMA.COLUMNS
--    WHERE TABLE_NAME = 'BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp' 
--    AND COLUMN_NAME = 'HUBOfferImportLargeFileID'
--)
--BEGIN
--    ALTER TABLE BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp
--    ADD HUBOfferImportLargeFileID INT;
--END;
IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp', 'HUBOfferImportLargeFileID') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp ADD HUBOfferImportLargeFileID INT
END
GO

--IF NOT EXISTS (
--    SELECT 1 
--    FROM INFORMATION_SCHEMA.COLUMNS
--    WHERE TABLE_NAME = 'BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp' 
--    AND COLUMN_NAME = 'GTMFR'
--)
--BEGIN
--    ALTER TABLE BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp
--    ADD GTMFR NVARCHAR(MAX);
--END;
IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp', 'GTMFR') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp ADD GTMFR NVARCHAR(MAX)
END
GO

--IF NOT EXISTS (
--    SELECT 1 
--    FROM INFORMATION_SCHEMA.COLUMNS
--    WHERE TABLE_NAME = 'BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp' 
--    AND COLUMN_NAME = 'GTVendor'
--)
--BEGIN
--    ALTER TABLE BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp
--    ADD GTVendor NVARCHAR(MAX);
--END;
IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp', 'GTVendor') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp ADD GTVendor NVARCHAR(MAX)
END
GO

--IF NOT EXISTS (
--    SELECT 1 
--    FROM INFORMATION_SCHEMA.COLUMNS
--    WHERE TABLE_NAME = 'BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp' 
--    AND COLUMN_NAME = 'UpdatedBy'
--)
--BEGIN
--    ALTER TABLE BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp
--    ADD UpdatedBy INT;
--END;
IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp', 'UpdatedBy') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOfferImportByExcelTemp ADD UpdatedBy INT
END
GO

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile', 'UploadedRowCount') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbHUBOfferImportLargeFile ADD UploadedRowCount INT NULL
END
GO