﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
-- =============================================  
-- Author:  Surendra  
-- Create date: February 23, 2016  
-- Description: To get internal purchase order line information  
-- =============================================  
CREATE OR ALTER PROCEDURE [dbo].[usp_select_InternalPurchaseOrderLine]   
@InternalPurchaseOrderLineId int   
AS
/*       
===========================================================================================      
TASK			UPDATED BY		DATE			ACTION      DESCRIPTION     
[BUG-243646]	AnTran			08-May-2025		Update		Convert Shipping cost from DMCC currency to IPO client currency  
===========================================================================================      
*/
BEGIN
DECLARE @HUBClientNo INT = 114
		,@ClientCurrencyInDmcc INT
		,@HUBCurrencyExchangeRate FLOAT
		,@ClientNo INT
		,@DateOrdered DATETIME

SELECT @ClientNo = ipol.ClientNo
		,@DateOrdered = ipol.DateOrdered
FROM  dbo.vwInternalPurchaseOrderLine ipol     
WHERE ipol.InternalPurchaseOrderLineId = @InternalPurchaseOrderLineId;

--get hub currency exchange rate at IPO Client
SELECT TOP 1 @ClientCurrencyInDmcc = c.CurrencyId
FROM tbCurrency c with(nolock)
JOIN tbCurrency cc WITH(NOLOCK) on cc.GlobalCurrencyNo = c.GlobalCurrencyNo
JOIN tbClient cl WITH(NOLOCK) on cl.CurrencyNo = cc.CurrencyId
WHEre c.ClientNo = @HUBClientNo
	AND c.Inactive = 0
	AND cl.ClientId = @ClientNo;

SET @HUBCurrencyExchangeRate = dbo.ufn_get_exchange_rate(@ClientCurrencyInDmcc, @DateOrdered);

SELECT *
	--,dbo.ufn_convert_between_client_currencies(ipol.ShipInCost, @DmccClientNo, ipol.ClientNo, ipol.DateOrdered) AS ShipInCostInBaseClient
	, ipol.ShipInCost * @HUBCurrencyExchangeRate AS ShipInCostInBaseClient
	, @HUBCurrencyExchangeRate AS HubCurrencyExchangeRate
FROM  dbo.vwInternalPurchaseOrderLine ipol     
WHERE ipol.InternalPurchaseOrderLineId = @InternalPurchaseOrderLineId    
END 

GO


