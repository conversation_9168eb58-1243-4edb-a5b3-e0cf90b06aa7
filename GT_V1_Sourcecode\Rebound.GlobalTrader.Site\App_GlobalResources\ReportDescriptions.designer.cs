//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "********")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ReportDescriptions {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ReportDescriptions() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.ReportDescriptions", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all Vendors active between the specified dates.
        /// </summary>
        internal static string ActiveVendors {
            get {
                return ResourceManager.GetString("ActiveVendors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all customers currently on stop..
        /// </summary>
        internal static string ApprovedCustomersOnStop {
            get {
                return ResourceManager.GetString("ApprovedCustomersOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all Suppliers by date range that have been added to the system as new companies when Information has been imported from stock disks and emails..
        /// </summary>
        internal static string AutoEnteredSuppliers_Unedited {
            get {
                return ResourceManager.GetString("AutoEnteredSuppliers_Unedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all bulk email invoice status within a specified date range..
        /// </summary>
        internal static string BulkEmailInvoiceStatus {
            get {
                return ResourceManager.GetString("BulkEmailInvoiceStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of requirements closed for each reason..
        /// </summary>
        internal static string ClosedRequirementsReasons {
            get {
                return ResourceManager.GetString("ClosedRequirementsReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all communication log activities for the selected user and date range..
        /// </summary>
        internal static string CommunicationLogActivityforaUser {
            get {
                return ResourceManager.GetString("CommunicationLogActivityforaUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all suppliers that have been approved to buy from and their linked vendor names..
        /// </summary>
        internal static string CompaniesApprovedToPurchaseFrom {
            get {
                return ResourceManager.GetString("CompaniesApprovedToPurchaseFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all companies not contacted since the selected cutoff date..
        /// </summary>
        internal static string CompaniesNotContacted {
            get {
                return ResourceManager.GetString("CompaniesNotContacted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all contact email addresses by country..
        /// </summary>
        internal static string ContactEmailList {
            get {
                return ResourceManager.GetString("ContactEmailList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all company contacts not contacted since the selected cutoff date..
        /// </summary>
        internal static string ContactsNotContacted {
            get {
                return ResourceManager.GetString("ContactsNotContacted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All credit notes for items returned within a specified date range.
        /// </summary>
        internal static string CreditNotes {
            get {
                return ResourceManager.GetString("CreditNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All credit notes for a customer for items returned within a specified date range.
        /// </summary>
        internal static string CreditNotesforaCustomer {
            get {
                return ResourceManager.GetString("CreditNotesforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All credit notes for a salesperson for items returned within a specified date range.
        /// </summary>
        internal static string CreditNotesforaSalesperson {
            get {
                return ResourceManager.GetString("CreditNotesforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists customers for a salesperson.
        /// </summary>
        internal static string CustomerListforSalesperson {
            get {
                return ResourceManager.GetString("CustomerListforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists delivery statistics by cutsomer within the specified date range.  Orders are considered on time if they are shipped on or before the promissed date..
        /// </summary>
        internal static string CustomerOnTimeDeliveryReport {
            get {
                return ResourceManager.GetString("CustomerOnTimeDeliveryReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all outstanding invoices for the selected customer..
        /// </summary>
        internal static string CustomerStatement {
            get {
                return ResourceManager.GetString("CustomerStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the customer requirements entered each day by salesperson.
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Detailed {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Detailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of customer requirements entered daily for each customer by salesperson.
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Summary {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of customer requirements entered daily by salesperson.
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Totals {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Totals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists details by date range of stock offers and reqs from suppliers..
        /// </summary>
        internal static string DailyImports {
            get {
                return ResourceManager.GetString("DailyImports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists details by date range of stock offers and reqs from the supplier broken down by the source of the information..
        /// </summary>
        internal static string DailyImportsBySource {
            get {
                return ResourceManager.GetString("DailyImportsBySource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Report Log Detials.
        /// </summary>
        internal static string DailyReportLog {
            get {
                return ResourceManager.GetString("DailyReportLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of days since a contact has been invoiced..
        /// </summary>
        internal static string DaysSinceLastInvoicebyContact {
            get {
                return ResourceManager.GetString("DaysSinceLastInvoicebyContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of days since a customer has been invoiced..
        /// </summary>
        internal static string DaysSinceLastInvoicebyCustomer {
            get {
                return ResourceManager.GetString("DaysSinceLastInvoicebyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A summary of stock received by date range.
        /// </summary>
        internal static string GoodsReceived {
            get {
                return ResourceManager.GetString("GoodsReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A summary of stock received but not invoiced by date range.
        /// </summary>
        internal static string GoodsReceivedNotInvoiced {
            get {
                return ResourceManager.GetString("GoodsReceivedNotInvoiced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A summary of stock received with shipment details by date range..
        /// </summary>
        internal static string GoodsReceivedShipmentDetails {
            get {
                return ResourceManager.GetString("GoodsReceivedShipmentDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Breaks down shipped orders within a specified date range by gross profit ranges..
        /// </summary>
        internal static string GrossProfitBreakdown {
            get {
                return ResourceManager.GetString("GrossProfitBreakdown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all commodities received from EEC countries, excluding those shipped within the country of receipt..
        /// </summary>
        internal static string IntrastatReportforEECArrivals_CustomerRMAs {
            get {
                return ResourceManager.GetString("IntrastatReportforEECArrivals_CustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all commodities received from EEC countries, excluding those shipped within the country of receipt..
        /// </summary>
        internal static string IntrastatReportforEECArrivals_Purchases {
            get {
                return ResourceManager.GetString("IntrastatReportforEECArrivals_Purchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all commodities shipped to EEC countries, excluding those shipped within the country of dispatch..
        /// </summary>
        internal static string IntrastatReportforEECDispatches_Sales {
            get {
                return ResourceManager.GetString("IntrastatReportforEECDispatches_Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all commodities shipped to EEC countries, excluding those shipped within the country of dispatch..
        /// </summary>
        internal static string IntrastatReportforEECDispatches_SupplierRMAs {
            get {
                return ResourceManager.GetString("IntrastatReportforEECDispatches_SupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all Companies with invalid or missing Purchasing information.
        /// </summary>
        internal static string InvalidCompanyPurchasingInfo {
            get {
                return ResourceManager.GetString("InvalidCompanyPurchasingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all Companies with invalid or missing Sales information.
        /// </summary>
        internal static string InvalidCompanySalesInfo {
            get {
                return ResourceManager.GetString("InvalidCompanySalesInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All inventory items ordered by location.
        /// </summary>
        internal static string InventoryLocationReport {
            get {
                return ResourceManager.GetString("InventoryLocationReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All inventory items for a selected lot ordered by location..
        /// </summary>
        internal static string InventoryLocationReportforLot {
            get {
                return ResourceManager.GetString("InventoryLocationReportforLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all invoice line items within a specified date range ordered by invoice number, with gross profit calculations..
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumber {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all invoice line items for the selected customer within a specified date range ordered by invoice number, with gross profit calculations..
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumberforaCustomer {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumberforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all invoice line items for the selected salesperson within a specified date range ordered by invoice number, with gross profit calculations..
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumberforaSalesperson {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumberforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List number of times users have logged in between specified date ranges..
        /// </summary>
        internal static string LoginStatistics {
            get {
                return ResourceManager.GetString("LoginStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List number of times specific users have logged in between specified date ranges..
        /// </summary>
        internal static string LoginStatisticsbyName {
            get {
                return ResourceManager.GetString("LoginStatisticsbyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of accounts linked to a sales person..
        /// </summary>
        internal static string NumberofAccountsbySalesperson {
            get {
                return ResourceManager.GetString("NumberofAccountsbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of offers for each vendor..
        /// </summary>
        internal static string NumberofOffersbyVendor {
            get {
                return ResourceManager.GetString("NumberofOffersbyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of historic offers for each vendor..
        /// </summary>
        internal static string NumberofOffersHistorybyVendor {
            get {
                return ResourceManager.GetString("NumberofOffersHistorybyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the number of requirements for each vendor..
        /// </summary>
        internal static string NumberofRequirementsbyVendor {
            get {
                return ResourceManager.GetString("NumberofRequirementsbyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open customer RMAs with costing information, where available..
        /// </summary>
        internal static string OpenCustomerRMAs {
            get {
                return ResourceManager.GetString("OpenCustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open customer RMAs for the selected customer with costing information, where available..
        /// </summary>
        internal static string OpenCustomerRMAsforaCustomer {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open customer RMAs with reasons for return and for the selected customer with costing information, where available..
        /// </summary>
        internal static string OpenCustomerRMAsforaCustomerwithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaCustomerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open customer RMAs for the selected salesperson with costing information, where available..
        /// </summary>
        internal static string OpenCustomerRMAsforaSaleperson {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaSaleperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open customer RMAs with reasons for return and for the selected salesperson with costing information, where available..
        /// </summary>
        internal static string OpenCustomerRMAsforaSalepersonwithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaSalepersonwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open customer RMAs with reasons for return and costing information, where available..
        /// </summary>
        internal static string OpenCustomerRMAswithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all open purchase orders.
        /// </summary>
        internal static string OpenPurchaseOrders {
            get {
                return ResourceManager.GetString("OpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all open purchase orders for a specified company type.
        /// </summary>
        internal static string OpenPurchaseOrdersbyCompanyType {
            get {
                return ResourceManager.GetString("OpenPurchaseOrdersbyCompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all open purchase orders for a supplier.
        /// </summary>
        internal static string OpenPurchaseOrdersbySupplier {
            get {
                return ResourceManager.GetString("OpenPurchaseOrdersbySupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all open quotes.
        /// </summary>
        internal static string OpenQuotes {
            get {
                return ResourceManager.GetString("OpenQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists total number of open reqs against each customer.
        /// </summary>
        internal static string OpenRequirementsbyCustomer {
            get {
                return ResourceManager.GetString("OpenRequirementsbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shows all open requirements for the selected salesperson.
        /// </summary>
        internal static string OpenRequirementsReportBySalesperson {
            get {
                return ResourceManager.GetString("OpenRequirementsReportBySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open sales orders with allocation information and gross profit calculations..
        /// </summary>
        internal static string OpenSalesOrders {
            get {
                return ResourceManager.GetString("OpenSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open sales orders for the specified salesperson with allocation information and gross profit calculations..
        /// </summary>
        internal static string OpenSalesOrdersforSalesperson {
            get {
                return ResourceManager.GetString("OpenSalesOrdersforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open supplier RMAs..
        /// </summary>
        internal static string OpenSupplierRMAs {
            get {
                return ResourceManager.GetString("OpenSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open supplier RMAs for the selected buyer..
        /// </summary>
        internal static string OpenSupplierRMAsforaBuyer {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open supplier RMAs with reasons for return for the selected buyer..
        /// </summary>
        internal static string OpenSupplierRMAsforaBuyerwithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaBuyerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open supplier RMAs for the selected supplier..
        /// </summary>
        internal static string OpenSupplierRMAsforaSupplier {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open supplier RMAs with reasons for return for the selected supplier..
        /// </summary>
        internal static string OpenSupplierRMAsforaSupplierwithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaSupplierwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all open supplier RMAs with reasons for return..
        /// </summary>
        internal static string OpenSupplierRMAswithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All outstanding orders to be shipped up until the selected date.
        /// </summary>
        internal static string OrdersToBeShipped {
            get {
                return ResourceManager.GetString("OrdersToBeShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All outstanding orders to be shipped up until the selected date by salesperson.
        /// </summary>
        internal static string OrdersToBeShippedBySalesperson {
            get {
                return ResourceManager.GetString("OrdersToBeShippedBySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all outstanding invoices..
        /// </summary>
        internal static string OustandingCustomerInvoices {
            get {
                return ResourceManager.GetString("OustandingCustomerInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all sales orders due and ready to ship..
        /// </summary>
        internal static string PickSheetSalesOrdersBasic {
            get {
                return ResourceManager.GetString("PickSheetSalesOrdersBasic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all sales orders due and ready to ship with additional details (lot number, resale values, PO #, etc)..
        /// </summary>
        internal static string PickSheetSalesOrdersDetailed {
            get {
                return ResourceManager.GetString("PickSheetSalesOrdersDetailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all Supplier RMAs due and ready to ship.
        /// </summary>
        internal static string PickSheetSupplierRMAs {
            get {
                return ResourceManager.GetString("PickSheetSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All outstanding posted purchase orders due in within the selected date range..
        /// </summary>
        internal static string PurchaseOrdersDueIn {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All outstanding posted purchase orders due in for a buyer within the selected date range.
        /// </summary>
        internal static string PurchaseOrdersDueInforBuyer {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueInforBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All outstanding posted purchase orders due in for a salesperson within the selected date range..
        /// </summary>
        internal static string PurchaseOrdersDueInforSalesperson {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueInforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all posted sales order items that are not fully allocated..
        /// </summary>
        internal static string PurchaseRequisitions {
            get {
                return ResourceManager.GetString("PurchaseRequisitions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all posted sales order items for a customer that are not fully allocated..
        /// </summary>
        internal static string PurchaseRequisitionsforaCustomer {
            get {
                return ResourceManager.GetString("PurchaseRequisitionsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to List all posted sales order items for a sales person that are not fully allocated..
        /// </summary>
        internal static string PurchaseRequisitionsforaSalesPerson {
            get {
                return ResourceManager.GetString("PurchaseRequisitionsforaSalesPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gives a random list of stock for a warehouse..
        /// </summary>
        internal static string RandomStockCheck {
            get {
                return ResourceManager.GetString("RandomStockCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all received customer RMAs with costing information, where available..
        /// </summary>
        internal static string ReceivedCustomerRMAs {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all received customer RMAs for the selected customer with costing information, where available..
        /// </summary>
        internal static string ReceivedCustomerRMAsforaCustomer {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all received customer RMAs with reasons for return for the selected customer with costing information, where available..
        /// </summary>
        internal static string ReceivedCustomerRMAsforaCustomerwithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaCustomerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all received customer RMAs for the selected salesperson with costing information, where available..
        /// </summary>
        internal static string ReceivedCustomerRMAsforaSaleperson {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaSaleperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all received customer RMAs with reasons for return for the selected salesperson with costing information, where available..
        /// </summary>
        internal static string ReceivedCustomerRMAsforaSalepersonwithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaSalepersonwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all received customer RMAs with reasons for return and costing information, where available..
        /// </summary>
        internal static string ReceivedCustomerRMAswithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the value of stock received by country within the specified date range..
        /// </summary>
        internal static string ReceivedGoodsValuationbyCountry {
            get {
                return ResourceManager.GetString("ReceivedGoodsValuationbyCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists the value of stock shipped by country within the specified date range..
        /// </summary>
        internal static string ShippedGoodsValuationbyCountry {
            get {
                return ResourceManager.GetString("ShippedGoodsValuationbyCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped orders within a specified date range ordered by invoice number broken down by lot (when applicable)..
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumber {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped orders for the selected customer within a specified date range ordered by invoice number broken down by lot (when applicable)..
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumberforaCustomer {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumberforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped orders for the selected salesperson (total invoice value not split by salesperson) within a specified date range ordered by invoice number broken down by lot (when applicable)..
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumberforaSalesperson {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumberforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shows details of shipped line items for a lot within a specified date range..
        /// </summary>
        internal static string ShippedSalesforLot {
            get {
                return ResourceManager.GetString("ShippedSalesforLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped supplier RMAs..
        /// </summary>
        internal static string ShippedSupplierRMAs {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped supplier RMAs for the selected buyer..
        /// </summary>
        internal static string ShippedSupplierRMAsforaBuyer {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped supplier RMAs with reasons for return for the selected buyer..
        /// </summary>
        internal static string ShippedSupplierRMAsforaBuyerwithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaBuyerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped supplier RMAs for the selected supplier..
        /// </summary>
        internal static string ShippedSupplierRMAsforaSupplier {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped supplier RMAs with reasons for return for the selected supplier..
        /// </summary>
        internal static string ShippedSupplierRMAsforaSupplierwithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaSupplierwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all shipped supplier RMAs with reasons for return..
        /// </summary>
        internal static string ShippedSupplierRMAswithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all stock for a warehouse by location with extra columns for manually entering physical counts and notes..
        /// </summary>
        internal static string StockCount {
            get {
                return ResourceManager.GetString("StockCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists all stock with the ability to filter out items on order and/or allocated items and/or item from lots which are on hold..
        /// </summary>
        internal static string StockList {
            get {
                return ResourceManager.GetString("StockList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All stock items ordered by total value.
        /// </summary>
        internal static string StockValuation {
            get {
                return ResourceManager.GetString("StockValuation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs, values, gross profit, margin for sales orders by customer booked within a specified date range..
        /// </summary>
        internal static string SummaryBookedOrdersbyCustomer {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs, values, gross profit, margin for sales orders by division booked within a specified date range..
        /// </summary>
        internal static string SummaryBookedOrdersbyDivision {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs, values, gross profit, margin for sales orders by salesperson booked within a specified date range..
        /// </summary>
        internal static string SummaryBookedOrdersbySalesperson {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs, values, gross profit, margin for open orders by customer for a specified date range..
        /// </summary>
        internal static string SummaryOpenOrdersbyCustomer {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs, values, gross profit, margin for open orders by division for a specified date range..
        /// </summary>
        internal static string SummaryOpenOrdersbyDivision {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs, values, gross profit, margin for open orders by salesperson for a specified date range..
        /// </summary>
        internal static string SummaryOpenOrdersbySalesperson {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs, values, gross profit, margin for shipped orders by customer for a specified date range..
        /// </summary>
        internal static string SummaryShippedSalesbyCustomer {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs, values, gross profit, margin for shipped orders by division for a specified date range..
        /// </summary>
        internal static string SummaryShippedSalesbyDivision {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show total costs (split by salesperson), values, gross profit, margin for shipped orders by salesperson for a specified date range..
        /// </summary>
        internal static string SummaryShippedSalesbySalesperson {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists delivery statistics by supplier within the specified date range.  Orders are considered on time if they are received on or before the delivery date on the purchase order..
        /// </summary>
        internal static string SupplierOnTimeDeliveryReport {
            get {
                return ResourceManager.GetString("SupplierOnTimeDeliveryReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lists details of all users..
        /// </summary>
        internal static string UserList {
            get {
                return ResourceManager.GetString("UserList", resourceCulture);
            }
        }
    }
}
