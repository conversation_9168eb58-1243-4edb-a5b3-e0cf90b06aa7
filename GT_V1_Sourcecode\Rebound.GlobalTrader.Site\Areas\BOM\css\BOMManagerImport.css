﻿
        .formHeader {
            background-color: #56954E;
            background-repeat: repeat-x;
            background-position: bottom;
            color: #FFFFFF;
            padding: 5px 0px 2px;
        }

        .main_container {
            font-family: tahoma;
            font-size: 11px;
            position: relative;
            background-color: #56954E;
            display: block;
            color: #ffffff;
        }

        h4 {
            border-bottom: dotted 1px #cccccc;
            padding-bottom: 2px;
            margin-bottom: 5px;
            padding-left: 5px;
        }

        .download_section {
            width: 100%;
            display: inline-block;
            margin-top: 20px;
        }

            .download_section tr td {
                float: left;
                font-size: 11px;
                font-weight: bold;
            }

                .download_section tr td a {
                    background-color: #85d279;
                    padding: 10px;
                    color: #2c6e23;
                    font-weight: bold;
                    border-radius: 5px;
                    margin-left: 10px;
                    text-decoration: none;
                }

        .formInstructions {
            font-family: Tahoma;
            position: relative;
            margin: 0px 5px;
            font-size: 12px;
        }

        .container-greenbase {
            background-color: #56954e;
            color: #fff;
            font-size: 11px;
            font-family: tahoma;
            padding: 10px;
        }

        fieldset {
            border: 1px #6cab63 solid;
            margin-bottom: 10px;
        }

        legend {
            display: block;
            padding-left: 1px;
            padding-right: 5px;
            color: #fff;
            font-family: Tahoma;
        }

        table {
            width: 100%;
        }

            table td {
                vertical-align: top;
            }

                table td.firstcol {
                    width: 94%;
                }

        .radio-option {
            margin-bottom: 10px;
        }

        select {
            min-width: 110px;
            margin-right: 20px;
            border: 0px #fff solid;
            font-size: 11px;
            padding: 2px;
            border-radius: 2px;
        }

        .col3 {
            display: flex;
            margin: 3px 0px
        }

            .col3 label {
                min-width: 100px;
                display: inline-block;
                text-align: right;
                margin-right: 4px;
                font-weight: bold;
            }

        .ui-autocomplete{
            max-height:200px;
            overflow:scroll;
        }

       .ui-autocomplete li{
            font-size:11px;
        }

            #ddlClient {
                width: 170px !important;
            }

        .col3 label {
            min-width: 100px;
            display: inline-block;
            text-align: right;
            margin-right: 4px;
            font-weight: bold;
        }

        .input_cont {
            width: 185px !important;
            border: 0px #fff solid;
            border-radius: 2px;
            font-size: 11px;
            padding: 3px 5px;
        }

            .input_cont:focus {
                border: 0px #fff solid !important;
            }


        .ddlClient {
            width: 195px !important;
        }

        .select_box {
            width: 120px;
        }

        .reset {
            background-color: #85d279;
            color: #2c6e23;
            border: 1px #2c6e23 solid;
            padding: 2px 0;
            margin-top: 5px;
            border-radius: 5px;
            border-style: none;
        }

        table.formRows textarea, table.formRows input, table.formRows select {
            font-size: 11px;
            border-style: none;
            padding: 2px;
        }

        .btn {
            min-width: 93px;
        }

        .right {
            color: #3a5a35;
            padding: 7px 20px;
            border: 1px #427f3a solid;
            width: 9%;
            white-space: normal;
            float: right;
            border-radius: 5px;
            background-color: #8fb18c;
            font-weight: bold;
            font-size: 12px;
            margin-top: 4px;
            cursor: pointer;
        }

.field_sec {
    margin-top: 20px;
    padding: 12px 10px;
    margin-right: 103px;
}

            .field_sec table select {
                width: 150px;
            }

        .exlsfile {
            width: 100%;
            background: #d5d5d5;
            margin-top: 5px;
            float: left;
        }

            .exlsfile .header {
                background: #434343;
                padding: 5px 10px;
            }

        .file {
            position: relative;
            display: inline-block;
            cursor: pointer;
            margin-right: 10px;
        }

        .label {
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 15px;
        }

        .upload {
            border-radius: 5px;
            display: block;
            width: 100px;
            height: 30px;
            background-color: #158684;
            border-style: none;
            color: #fff;
            font-weight: bold;
            box-shadow: 0px 3px #0e6493;
        }

        .upload_txt {
            font-size: 11px;
            padding: 6px 10px;
            border-style: dashed;
            font-weight: bold;
            border-radius: 5px;
            border-color: #c9d1cc;
            color: #c9d1cc;
        }

        td, th {
            padding-bottom: 10px;
        }

        div.pq-theme * {
            line-height: normal;
            font-family: inherit;
            font-size: 10px !important;
        }

        .boxStandard .boxHeaderInner {
            height: 48px !important;
            background-color: #9fe994;
        }

        .ui-corner-top {
        }

            .ui-corner-top b {
                font-size: 12px !important;
                color: #000 !important;
            }

        .searchtable {
            width: 48%;
        }

        .boxStandard .boxContent {
            padding: 0px;
        }

        .pq-grid-title, .pq-group-header {
            background: #dddddd;
            border-color: #dddddd;
            padding: 10px;
        }

        .pq-grid-row {
            background: #e4f6e2;
            color: #1d1919;
        }

        .pq-grid-col, .pq-grid-number-col {
            color: #fff;
            font-weight: 600;
            background-color: #575a57;
        }

            .pq-grid-col:hover {
                background: #5ab94c !important;
            }

        #grid_md {
            width: 1316px !important;
            /*margin: 0 auto;*/
            border-radius: 0px !important;
            border: 0px solid #aaaaaa !important;
        }

        .dataFilter h5 {
            font-size: 10px;
            font-family: Lucida Sans, Arial;
            text-transform: uppercase;
            font-weight: bold;
            margin: 10px 0px;
            padding-bottom: 3px;
            border-bottom: dotted 1px #90db89;
        }

        .dataFilter {
            background-color: #FFFFFF;
            border-color: #AAE2A0;
            border-style: solid;
            border-width: 0px 1px;
            left: 0px;
            padding: 1px 1px 0px 1px;
            position: relative;
            top: 0px;
            color: #009900;
        }

        .dataFilterInner {
            background-color: #BBF2B3;
            padding: 5px 5px 0px;
            height: 64px;
        }

        .boxLinks {
            position: relative;
            top: 8px;
            padding: 0 0 0 5px !important;
        }

        .pagingControls {
            position: relative;
            top: 10px;
        }

        .pagingControlsLeft {
            position: absolute;
            left: 5px;
            font-size: 11px;
            color: #1fa00e;
        }

        .pagingControlsRight {
            position: absolute;
            right: 5px;
            font-size: 11px;
            top: -3px;
            color: #1fa00e;
        }

        .boxContent {
            width: 100%;
            overflow: scroll;
            border: solid 1px;
            overflow-x: scroll;
            overflow-y: scroll;
            height: 300px;
            border-style: ridge;
        }

        .resulttable {
            overflow: auto;
            margin: 0 auto;
        }

        .BackButton {
            background-repeat: no-repeat;
            height: 10px;
            width: 11px;
            position: absolute;
            top: 3px;
            right: 24px;
            margin: 0px;
            padding: 0px;
            cursor: pointer;
        }

        .ajax-file-upload {
            padding: 7px 25px !important;
            width: auto !important;
            background-color: #158684 !important;
            color: #fff !important;
            margin: 0 !important;
        }

        .ajax-file-upload {
            height: 24px !important;
        }

        #table1_length, #table1_info {
            padding: 10px;
            /*background: #3a6c34;*/
            color: #666;
        }

        #table1_paginate {
            margin-top: 5px;
        }

            #table1_paginate a {
                padding: 5px;
                margin: 5px;
                background: #3a6c34;
                color: #fff;
            }

#CurrentDateTime {
    display: inline;
    position: relative;
    top: 3px;
    width: 150px;
}

