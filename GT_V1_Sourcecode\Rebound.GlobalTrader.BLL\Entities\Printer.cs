﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL {
    public partial class Printer : BizObject
    {		
        #region Properties

        protected static DAL.PrinterElement Settings
        {
            get { return Globals.Settings.Printer; }
        }

        /// <summary>
        /// Printer Id
        /// </summary>
        public System.Int32 PrinterId { get; set; }
        /// <summary>
        /// Client No
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// Printer Name
        /// </summary>
        public System.String PrinterName { get; set; }
        /// <summary>
        /// Printer Description
        /// </summary>
        public System.String PrinterDescription { get; set; }
        /// <summary>
        /// DLUP
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// Inactive
        /// </summary>
        public System.Boolean Inactive { get; set; }
        /// <summary>
        /// UpdatedBy
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }

        //Properties for List setup screen: 

        public System.Int32? ListSetpId { get; set; }
        public System.String ListSetupName { get; set; }
        public System.String Descriptions { get; set; }


        //Properties for setup list item screen: 

        public System.Int32? LabelSetupItemId { get; set; }
        public System.String LabelSetupItemName { get; set; }
        public System.String LabelSetupOther { get; set; }

        
        public System.Int32? RestrictedManufacturerId { get; set; }
        public System.String Notes { get; set; }
        public System.Int32? ManufactureNo { get; set; }
        public System.String ManufactureName { get; set; }
        public System.Int32? ManufacturerCount { get; set; }
        public System.Int32? isRestrictedManufacturer { get; set; }
        public System.String RestrictedMFRMessage { get; set; }
        /// <summary>
        /// ECCNId
        /// </summary>
        public System.Int32? ECCNId { get; set; }
        /// <summary>
        /// ECCNCode
        /// </summary>
        public System.String ECCNCode { get; set; }
        /// <summary>
        /// ECCNStatus
        /// </summary>
        public System.Boolean ECCNStatus { get; set; }


        
        /// <summary>
        /// ECCNNotify
        /// </summary>
        public System.Boolean ECCNNotify { get; set; }
        /// <summary>
        /// EccnSubject
        /// </summary>
        public System.String EccnSubject { get; set; }
        /// <summary>
        /// EccnMessage
        /// </summary>
        public System.String EccnMessage { get; set; }

        /// <summary>
        /// EmployeeName
        /// </summary>
        public System.String EmployeeName { get; set; }

        public System.String MFRNameSuffix { get; set; }


        #endregion

        #region Methods

        /// <summary>
        /// DropDownForClient
        /// Calls [usp_dropdown_PrinterAll]
        /// </summary>
        public static List<Printer> DropDownForPrinter(System.Int32? clientId)
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.DropDownForPrinter(clientId);
			if (lstDetails == null) {
                return new List<Printer>();
			} else {
				List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
					Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
					obj.PrinterId = objDetails.PrinterId;
					obj.PrinterName = objDetails.PrinterName;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

        /// <summary>
        /// [usp_insert_Printer]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static int Insert(System.Int32? clientNo, System.String printerName, System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Printer.Insert(clientNo, printerName, description, inActive, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_update_Printer]
        /// </summary>
        /// <param name="printerId"></param>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static bool Update(System.Int32? printerId, System.Int32? clientNo, System.String printerName, System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Printer.Update(printerId, clientNo, printerName, description, inActive, updatedBy);
        }

        /// <summary>
        /// GetListForClient
        /// Calls [usp_selectAll_Printer_for_Client]
        /// </summary>
        public static List<Printer> GetListForClient(System.Int32? clientId)
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetListForClient(clientId);
            if (lstDetails == null)
            {
                return new List<Printer>();
            }
            else
            {
                List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
                    obj.PrinterId = objDetails.PrinterId;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.PrinterName = objDetails.PrinterName;
                    obj.PrinterDescription = objDetails.PrinterDescription;
                    obj.Inactive = objDetails.Inactive;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

       

        #endregion


        #region Method for Label setup section
        /// <summary>
        /// GetList
        /// Calls [usp_selectAll_LabelSetup]
        /// </summary>
        public static List<Printer> GetListLabelSetup()
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetListLabelSetup();
            if (lstDetails == null)
            {
                return new List<Printer>();
            }
            else
            {
                List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
                    obj.ListSetpId = objDetails.ListSetpId;
                    obj.ListSetupName = objDetails.ListSetupName;
                    obj.Descriptions = objDetails.Descriptions;
                    obj.Inactive = objDetails.Inactive;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// Pass the primary key of the table tbListSetup
        /// Calls [usp_selectAll_LabelSetupItem]
        /// </summary>
        public static List<Printer> GetListLabelSetupItem(System.Int32? labelSetupId)
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetListLabelSetupItem(labelSetupId);
            if (lstDetails == null)
            {
                return new List<Printer>();
            }
            else
            {
                List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
                    obj.LabelSetupItemId = objDetails.LabelSetupItemId;
                    obj.LabelSetupItemName = objDetails.LabelSetupItemName;
                    obj.LabelSetupOther = objDetails.LabelSetupOther;
                    obj.Inactive = objDetails.Inactive;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        /// <summary>
        /// Insert
        /// Calls [usp_insert_LabelSetupItem]
        /// </summary>
        public static Int32 InsertLabelSetupItem(System.Int32? labelSetupId, System.String name, System.String email, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Printer.InsertLabelSetupItem(labelSetupId, name, email, updatedBy);
            return objReturn;
        }

        /// <summary>
        /// Insert
        /// Calls [usp_update_LabelSetupItem]
        /// </summary>
        public static bool UpdateLabelSetupItem(System.Int32? labelsetupItemId, System.Int32? labelSetupId, System.String name, System.String email, System.Int32? updatedBy,System.Boolean? inactive)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Printer.UpdateLabelSetupItem(labelsetupItemId, labelSetupId, name, email,  updatedBy,inactive);
        }
        #endregion


        /// <summary>
        /// [usp_insert_RestrictedManufacture]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="manufactureNo"></param>
        /// <param name="Notes"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static int InsertMfr(System.Int32? clientNo, System.Int32? manufactureNo, System.String notes, System.Boolean? inActive, System.Int32? updatedBy,System.Int32? ManufacturerCount, System.String ManufactureName)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Printer.InsertMfr(clientNo, manufactureNo, notes, inActive, updatedBy, ManufacturerCount, ManufactureName);
            return objReturn;
        }

        public static System.String InsertMultipleManufacturers(System.Int32? clientNo, System.String manufactureNos, System.String mfrNameSuffix, System.String notes, System.Boolean? inActive, System.Int32? updatedBy)
        {
            System.String objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Printer.InsertMultipleManufacturers(clientNo, manufactureNos, mfrNameSuffix, notes, inActive, updatedBy);
            return objReturn;
        }

        /// <summary>
        /// Calls [usp_update_RestrictedManufacturer]
        /// </summary>
        /// <param name="RestrictedManufacturerId"></param>
        /// <param name="clientNo"></param>
        /// <param name="manufactureNo"></param>
        /// <param name="notes"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static bool UpdateMfr(System.Int32? RestrictedManufacturerId, System.Int32? clientNo, System.Int32? manufactureNo, System.String notes, System.Boolean? inActive, System.Int32? updatedBy ,System.String manufactureName, System.Int32 manufactureCount)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Printer.UpdateMfr(RestrictedManufacturerId, clientNo, manufactureNo, notes, inActive, updatedBy, manufactureName, manufactureCount);
        }

        public static bool UpdateMultipleMfr(System.Int32? clientNo, System.String manufacturerNos, System.String mfrNameSuffix, System.String notes, System.Boolean? inActive, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Printer.UpdateMultipleMfr(clientNo, manufacturerNos, mfrNameSuffix, notes, inActive, updatedBy);
        }

        public static bool ActivateMultipleMfr(System.Int32? clientNo, System.String manufacturerNos, System.Boolean? inActive, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Printer.ActivateMultipleMfr(clientNo, manufacturerNos, inActive, updatedBy);
        }

        /// <summary>
        /// GetListForClient
        /// Calls [usp_selectAll_Printer_for_Client]
        /// </summary>
        public static List<Printer> GetListForRestrictedManufacturer(System.Int32? clientId)
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetListForRestrictedManufacturer(clientId);
            if (lstDetails == null)
            {
                return new List<Printer>();
            }
            else
            {
                List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
                    obj.RestrictedManufacturerId = objDetails.RestrictedManufacturerId;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ManufactureNo = objDetails.ManufactureNo;
                    obj.ManufactureName = objDetails.ManufactureName;
                    obj.Notes = objDetails.Notes;
                    obj.Inactive = objDetails.Inactive;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ManufacturerCount = objDetails.ManufacturerCount;
                    obj.MFRNameSuffix = objDetails.MFRNameSuffix;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        public static List<Printer> GetItemsByMfrIds(System.Int32? clientId, System.String manufacturerNos)
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetItemsByMfrIds(clientId, manufacturerNos);
            if (lstDetails == null)
            {
                return new List<Printer>();
            }
            else
            {
                List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
                    obj.ManufactureName = objDetails.ManufactureName;
                    obj.ManufacturerCount = objDetails.ManufacturerCount;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
		/// Get
		/// Calls [usp_Select_RestrictedManufacture]
		/// </summary>
		public static Printer Get(System.Int32? clientNo, System.Int32? manufactureNo)
        {
            Rebound.GlobalTrader.DAL.PrinterDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.Get(clientNo, manufactureNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Printer obj = new Printer();
                obj.ManufacturerCount = objDetails.ManufacturerCount;
                obj.ManufactureName = objDetails.ManufactureName;
                objDetails = null;
                return obj;
            }
        }

        /// <summary>
		/// GetRestrictedManufacture
		/// Calls [usp_Select_Search_for_RestrictedManufacture]
		/// </summary>
		public static Printer GetRestrictedManufacture(System.Int32? clientNo, System.Int32? manufactureNo)
        {
            Rebound.GlobalTrader.DAL.PrinterDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetRestrictedManufacture(clientNo, manufactureNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                Printer obj = new Printer();
                obj.isRestrictedManufacturer = objDetails.isRestrictedManufacturer;
                obj.RestrictedMFRMessage = objDetails.RestrictedMFRMessage;
                objDetails = null;
                return obj;
            }
        }


        /// <summary>
        /// GetListForClient
        /// Calls [usp_selectAll_Printer_for_Client]
        /// </summary>
        public static List<Printer> GetListForECCNCode(System.Int32? clientId, System.Boolean NotifyStatus, System.String SearchType)
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetListForECCNCode(clientId, NotifyStatus, SearchType);
            if (lstDetails == null)
            {
                return new List<Printer>();
            }
            else
            {
                List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
                    obj.ECCNId = objDetails.ECCNId;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.Notes = objDetails.Notes;
                    obj.ECCNStatus = objDetails.ECCNStatus;
                    obj.Inactive = objDetails.Inactive;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ECCNNotify = objDetails.ECCNNotify;
                    obj.EccnSubject = objDetails.EccnSubject;
                    obj.EccnMessage = objDetails.EccnMessage;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }

        /// <summary>
        /// GetListForClient
        /// Calls [usp_selectAll_Printer_for_Client]
        /// </summary>
        public static List<Printer> GetListForECCNCodeMap(System.Int32? clientId)
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetListForECCNCodeMap(clientId);
            if (lstDetails == null)
            {
                return new List<Printer>();
            }
            else
            {
                List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
                    obj.ECCNId = objDetails.ECCNId;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.ECCNCode = objDetails.ECCNCode;
                    obj.Notes = objDetails.Notes;
                    obj.ECCNStatus = objDetails.ECCNStatus;
                    obj.Inactive = objDetails.Inactive;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.ECCNNotify = objDetails.ECCNNotify;
                    obj.EccnSubject = objDetails.EccnSubject;
                    obj.EccnMessage = objDetails.EccnMessage;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }


        /// <summary>
        /// [usp_insert_ECCN]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="EccnCode"></param>
        /// <param name="Notes"></param>
        /// <param name="EccnStatus"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static int InsertEccn(System.Int32? clientNo, System.String ECCNCode, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Printer.InsertEccn(clientNo, ECCNCode, notes, ECCNStatus, ECCNNotify, EccnSubject, EccnMessage, inActive, updatedBy);
            return objReturn;
        }

        /// <summary>
        /// [usp_insert_ECCNClone]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="EccnCode"></param>
        /// <param name="Notes"></param>
        /// <param name="EccnStatus"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static int InsertEccnClone(System.Int32? ECCNCloneId,System.Int32? clientNo, System.String ECCNCode, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Printer.InsertEccnClone(ECCNCloneId,clientNo, ECCNCode, notes, ECCNStatus, ECCNNotify, EccnSubject, EccnMessage, inActive, updatedBy);
            return objReturn;
        }

        /// <summary>
        /// Calls [usp_update_RestrictedManufacturer]
        /// </summary>
        /// <param name="RestrictedManufacturerId"></param>
        /// <param name="clientNo"></param>
        /// <param name="manufactureNo"></param>
        /// <param name="notes"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static bool UpdateEccn(System.Int32? ECCNId, System.Int32? clientNo, System.String notes, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Boolean? inActive, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Printer.UpdateEccn(ECCNId, clientNo, notes, ECCNStatus, ECCNNotify, EccnSubject, EccnMessage, inActive, updatedBy);
        }

        public static bool UpdateMapEccn(System.Int32? clientNo, System.String SelectedEccnIds, System.String EccnWarningMessage, System.Boolean? ECCNStatus, System.Boolean? ECCNNotify, System.String EccnSubject, System.String EccnMessage, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Printer.UpdateMapEccn(clientNo, SelectedEccnIds, EccnWarningMessage, ECCNStatus, ECCNNotify, EccnSubject, EccnMessage,  updatedBy);
        }


        //////////////////////PVV 
        /// /// <summary>
        /// [usp_insert_Printer]
        /// </summary>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static int InsertPVV(System.Int32? clientNo, System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.Printer.InsertPVV(clientNo, description, inActive, updatedBy);
            return objReturn;
        }
        /// <summary>
        /// Calls [usp_update_Printer]
        /// </summary>
        /// <param name="printerId"></param>
        /// <param name="clientNo"></param>
        /// <param name="printerName"></param>
        /// <param name="description"></param>
        /// <param name="inActive"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public static bool UpdatePVV(System.Int32? printerId, System.Int32? clientNo,  System.String description, System.Boolean? inActive, System.Int32? updatedBy)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.Printer.UpdatePVV(printerId, clientNo,  description, inActive, updatedBy);
        }


        /// <summary>
        /// GetListForClient
        /// Calls [usp_selectAll_PVVQuestion_for_Client]
        /// </summary>
        public static List<Printer> GetListForPVV(System.Int32? clientId)
        {
            List<PrinterDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.Printer.GetListForPVV(clientId);
            if (lstDetails == null)
            {
                return new List<Printer>();
            }
            else
            {
                List<Printer> lst = new List<Printer>();
                foreach (PrinterDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.Printer obj = new Rebound.GlobalTrader.BLL.Printer();
                    obj.PrinterId = objDetails.PrinterId;
                    obj.ClientNo = objDetails.ClientNo;
                    obj.PrinterDescription = objDetails.PrinterDescription;
                    obj.Inactive = objDetails.Inactive;
                    obj.UpdatedBy = objDetails.UpdatedBy;
                    obj.DLUP = objDetails.DLUP;
                    obj.EmployeeName = objDetails.EmployeeName;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        ////////////////////////

    }
}