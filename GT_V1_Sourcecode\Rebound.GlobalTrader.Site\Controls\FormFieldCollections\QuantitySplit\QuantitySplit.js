Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.initializeBase(this,[n]);this._intTotalWidth=300;this._intSplits=1;this._intTotalQuantity=400;this._aryQuantities=[];this._intTotalWidth=300;this._aryWidths=[];this._intMaxSplits=5;this._intSepWidth=5};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.prototype={get_hypItemSplit:function(){return this._hypItemSplit},set_hypItemSplit:function(n){this._hypItemSplit!==n&&(this._hypItemSplit=n)},get_hypItemUnsplit:function(){return this._hypItemUnsplit},set_hypItemUnsplit:function(n){this._hypItemUnsplit!==n&&(this._hypItemUnsplit=n)},get_pnlItems:function(){return this._pnlItems},set_pnlItems:function(n){this._pnlItems!==n&&(this._pnlItems=n)},get_intTotalQuantity:function(){return this._intTotalQuantity},set_intTotalQuantity:function(n){this._intTotalQuantity!==n&&(this._intTotalQuantity=n)},get_intTotalWidth:function(){return this._intTotalWidth},set_intTotalWidth:function(n){this._intTotalWidth!==n&&(this._intTotalWidth=n)},get_intMaxSplits:function(){return this._intMaxSplits},set_intMaxSplits:function(n){this._intMaxSplits!==n&&(this._intMaxSplits=n)},initialize:function(){$addHandler(this._hypItemSplit,"click",Function.createDelegate(this,this.addSplit));$addHandler(this._hypItemUnsplit,"click",Function.createDelegate(this,this.removeSplit));this._aryQuantities=[this._intTotalQuantity];this._aryWidths=[this._intTotalWidth];Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._hypItemSplit&&$clearHandlers(this._hypItemSplit),this._hypItemUnsplit&&$clearHandlers(this._hypItemUnsplit),this._hypItemSplit=null,this._hypItemUnsplit=null,this._pnlItems=null,this._aryQuantities=null,this._aryWidths=null,this._intTotalQuantity=null,this._intMaxSplits=null,this._intTotalWidth=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.callBaseMethod(this,"dispose"))},addSplit:function(){this._intSplits!=this._intMaxSplits&&(this._aryQuantities[this._intSplits-1]=Math.max(1,Math.floor(this._aryQuantities[this._intSplits-1]/2)),this._aryQuantities[this._intSplits]=this._aryQuantities[this._intSplits-1],this._intSplits+=1,this.adjustFirstQuantity(),this.calculateWidths(),this.displaySplits())},removeSplit:function(){this._intSplits!=1&&(this._intSplits-=1,this._aryQuantities[0]+=this._aryQuantities[this._intSplits],this._aryQuantities.splice(this._intSplits,this._intMaxSplits),this.adjustFirstQuantity(),this.calculateWidths(),this.displaySplits())},ensureCollectiveExhaustion:function(n,t){for(var i=0;n.sum()<t;)n[i]+=1,i+=1,i>=this._intSplits&&(i=0)},calculateWidths:function(){for(var t=this._intTotalWidth-this._intSepWidth*(this._intSplits-1),n=0;n<this._intSplits;n++)this._aryWidths[n]=Math.floor(t/this._intSplits)},reset:function(){this._intSplits=1;this._aryQuantities=[this._intTotalQuantity];this._aryWidths=[this._intTotalWidth];this.displaySplits()},displaySplits:function(){var n;this.ensureCollectiveExhaustion(this._aryQuantities,this._intTotalQuantity);var t="",i=this._element.id,r=0;for(n=0;n<this._intSplits;n++)t+=String.format('<div class="{2}" style="left:{0}px; width:{1}px;"',r,this._aryWidths[n],n==0?"splitStockItemSelected":"splitStockItem"),n>0&&(t+=String.format(" onclick=\"$find('{0}').editItem({1});\"",i,n)),t+=String.format('><span id="{0}">{1}<\/span>',this.getControlID("item",n),this._aryQuantities[n]),n>0&&(t+=String.format('<input class="invisible" type="text" id="{0}" value="{1}" onkeypress="if (event.keyCode == Sys.UI.Key.enter) {{ $find(\'{2}\').finishEditItem({3}); return false; }} else if (event.keyCode == 27) {{ $find(\'{2}\').showTextBox(false, {3}); }};" />',this.getControlID("txt",n),this._aryQuantities[n],i,n)),t+="<\/div>",r+=this._aryWidths[n]+this._intSepWidth;$R_FN.setInnerHTML(this._pnlItems,t)},getControlID:function(n,t){return String.format("{0}_{1}{2}",this._element.id,n,t)},showButtons:function(){$R_FN.showElement($get("hypItemSplit"),this._intSplits<this._intMaxSplits);$R_FN.showElement($get("hypItemUnsplit"),this._intSplits>1)},updateQuantities:function(){for(var n=0;n<this._intSplits;n++)$R_FN.setInnerHTML($get(this.getControlID("item",n)),this._aryQuantities[n]),$get(this.getControlID("txt",n)).value=this._aryQuantities[n]},editItem:function(n){this.showTextBox(!0,n)},finishEditItem:function(n){this.showTextBox(!1,n);var t=$get(this.getControlID("txt",n));Number.parseLocale(eval(t.value).toString())&&(this._aryQuantities[n]=Math.max(Math.min(Number.parseLocale(eval(t.value).toString()),this._intTotalQuantity-(this._aryQuantities.sum()-this._aryQuantities[0]-this._aryQuantities[n])-1),1),this.adjustFirstQuantity(),this.displaySplits())},showTextBox:function(n,t){$R_FN.showElement($get(this.getControlID("item",t)),!n);var i=$get(this.getControlID("txt",t));$R_FN.showElement(i,n);n&&(i.value=this._aryQuantities[t],i.select(),i.focus())},adjustFirstQuantity:function(){this._aryQuantities[0]=Math.max(this._intTotalQuantity-(this._aryQuantities.sum()-this._aryQuantities[0]),1)},getQuantitiesString:function(){return $R_FN.arrayToSingleString(this._aryQuantities)}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);