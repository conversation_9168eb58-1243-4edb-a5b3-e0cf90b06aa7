GO

/****** Object:  StoredProcedure [dbo].[usp_insert_HUBRFQMainInfo_ExpediteNote]    Script Date: 4/22/2024 3:13:00 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('usp_insert_HUBRFQMainInfo_ExpediteNote', 'P') IS NOT NULL
	DROP PROC usp_insert_HUBRFQMainInfo_ExpediteNote
GO
/*
-- ==========================================================================================
-- TASK      	UPDATED BY     			DATE         ACTION 			DESCRIPTION                                    
-- 001 			Shashi Keshar     		19-07-2017   Update				Save Expedite Notes
-- US-201306 	Hau Nguyen Hoang Trung  26-04-2024   Update				Add more CCUserID to send email
-- ==========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_insert_HUBRFQMainInfo_ExpediteNote]        
    @HUBRFQId INT        
  , @ExpediteNotes   nvarchar(MAX) = Null     
  , @UpdatedBy INT 
  , @EmailSendTo INT = 0
  , @NewId INT = NULL OUTPUT        
AS         
BEGIN     
 Declare @ID int , @Group int= 0 , @RequestedToPOHUB int

      -- select top 1 @RequestedToPOHUB=UpdateByPH from tbBOM where BOMId= @HUBRFQId                         
   BEGIN                 
   
    INSERT  INTO dbo.tbHUBRFQExpediteNotes (        
            HUBRFQNo    
          , ExpediteNotes              
          , UpdatedBy    
          , DLUP  
		 ,MailSendTo
		  ,CCUserID
          )        
    VALUES  (        
              @HUBRFQId        
            , @ExpediteNotes       
            , @UpdatedBy        
            , CURRENT_TIMESTAMP
			, @EmailSendTo 
			, (SELECT Contact2Name FROM vwBOM WHERE BOMId = @HUBRFQId)
            )  
			
			 if(@Group=0)
			begin
			 set @Group=  SCOPE_IDENTITY()
			 end
			  update tbHUBRFQExpediteNotes set GroupID=@Group  where [HUBRFQExpediteNotesId]=SCOPE_IDENTITY()
			  update tbCustomerRequirement set ExpediteDate= CURRENT_TIMESTAMP where CustomerRequirementId=@ID
      
   END 
                             
           
         
         
   
          
    SET @NewId = SCOPE_IDENTITY()      
                
  	SELECT  @NewId 
         
END 



GO


