﻿//Marker     Changed by               Date         Remarks
//[001]      <PERSON><PERSON><PERSON><PERSON>           31/08/2023   Add new module AS6081 for RP-2226
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class AS6081: BizObject
    {
        #region Properties

        protected static DAL.AS6081Element Settings
        {
            get { return Globals.Settings.AS6081s; }
        }

        /// <summary>
        /// IncotermId
        /// </summary>
        public System.Int32 TypeOfSupplierId { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        public System.String Name { get; set; }

        public System.Int32? Id { get; set; }
        public System.Int32? AssignmentHistoryId { get; set; }
        public System.String DocumentNumber { get; set; }
        public System.Int32? DocumentId { get; set; }
        public System.String AssignedTo { get; set; }
        public System.String AssignedBy { get; set; }
        public System.DateTime? LogDate { get; set; }
        public System.String AssignmentType { get; set; }
        public System.Boolean? IsCountryFound { get; set; }
        public System.String CountryName { get; set; }
        public System.Int32? CountryNo { get; set; }

        #endregion

        #region Methods

        #region Type Of Supplier Setup Section
        public static Int32 InsertTypeOfSupplier(System.String name, System.Int32? LoginId)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.InsertTypeOfSupplier(name,LoginId);
            return objReturn;
        }
        public static List<AS6081> GetListTypeOfSupplier()
        {
            List<AS6081Details> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.GetListTypeOfSupplier();
            if (lstDetails == null)
            {
                return new List<AS6081>();
            }
            else
            {
                List<AS6081> lst = new List<AS6081>();
                foreach (AS6081Details objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.AS6081 obj = new Rebound.GlobalTrader.BLL.AS6081();
                    obj.TypeOfSupplierId = objDetails.TypeOfSupplierId;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static bool UpdateTypeOfSupplier(System.Int32? TypeOfSupplierId, System.String Name, System.Int32? LoginId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.AS6081.UpdateTypeOfSupplier(TypeOfSupplierId, Name, LoginId);
        }
        public static bool DeleteTypeOfSupplier(System.Int32? TypeOfSupplierId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.AS6081.DeleteTypeOfSupplier(TypeOfSupplierId);
        }
        public static Int32 ValidateTypeOfSupplier(System.Int32? ID,System.String Name, System.Int32? Screen)
        {
            int objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.ValidateTypeOfSupplier(ID,Name,Screen);
            return objReturn;
        }
        #endregion

        #region Reason For Chosen Supplier Setup Section
        public static Int32 InsertReasonForChosenSupplier(System.String name, System.Int32? LoginId)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.InsertReasonForChosenSupplier(name, LoginId);
            return objReturn;
        }
        public static List<AS6081> GetListReasonForChosenSupplier()
        {
            List<AS6081Details> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.GetListReasonForChosenSupplier();
            if (lstDetails == null)
            {
                return new List<AS6081>();
            }
            else
            {
                List<AS6081> lst = new List<AS6081>();
                foreach (AS6081Details objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.AS6081 obj = new Rebound.GlobalTrader.BLL.AS6081();
                    obj.TypeOfSupplierId = objDetails.TypeOfSupplierId;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static bool UpdateReasonForChosenSupplier(System.Int32? ReasonForChosenSupplierId, System.String Name, System.Int32? LoginId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.AS6081.UpdateReasonForChosenSupplier(ReasonForChosenSupplierId, Name, LoginId);
        }
        public static bool DeleteReasonForChosenSupplier(System.Int32? ReasonForChosenSupplierId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.AS6081.DeleteReasonForChosenSupplier(ReasonForChosenSupplierId);
        }
        public static Int32 ValidateReasonForChosenSupplier(System.Int32? ID, System.String Name, System.Int32? Screen)
        {
            int objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.ValidateReasonForChosenSupplier(ID, Name, Screen);
            return objReturn;
        }
        #endregion

        #region Risk Of Supplier Setup Section
        public static Int32 InsertRiskOfSupplier(System.String name, System.Int32? LoginId)
        {
            Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.InsertRiskOfSupplier(name, LoginId);
            return objReturn;
        }
        public static List<AS6081> GetListRiskOfSupplier()
        {
            List<AS6081Details> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.GetListRiskOfSupplier();
            if (lstDetails == null)
            {
                return new List<AS6081>();
            }
            else
            {
                List<AS6081> lst = new List<AS6081>();
                foreach (AS6081Details objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.AS6081 obj = new Rebound.GlobalTrader.BLL.AS6081();
                    obj.TypeOfSupplierId = objDetails.TypeOfSupplierId;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static bool UpdateRiskOfSupplier(System.Int32? RiskOfSupplierId, System.String Name, System.Int32? LoginId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.AS6081.UpdateRiskOfSupplier(RiskOfSupplierId, Name, LoginId);
        }
        public static bool DeleteRiskOfSupplier(System.Int32? RiskOfSupplierId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.AS6081.DeleteRiskOfSupplier(RiskOfSupplierId);
        }
        public static Int32 ValidateRiskOfSupplier(System.Int32? ID, System.String Name, System.Int32? Screen)
        {
            int objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.ValidateRiskOfSupplier(ID, Name, Screen);
            return objReturn;
        }
        #endregion

        #region Master Dropdowns for AS6081
        public static List<AS6081> DropDownTypeOfSupplier()
        {
            List<AS6081Details> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.DropDownTypeOfSupplier();
            if (lstDetails == null)
            {
                return new List<AS6081>();
            }
            else
            {
                List<AS6081> lst = new List<AS6081>();
                foreach (AS6081Details objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.AS6081 obj = new Rebound.GlobalTrader.BLL.AS6081();
                    obj.Id = objDetails.Id;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static List<AS6081> DropDownReasonForSupplier()
        {
            List<AS6081Details> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.DropDownReasonForSupplier();
            if (lstDetails == null)
            {
                return new List<AS6081>();
            }
            else
            {
                List<AS6081> lst = new List<AS6081>();
                foreach (AS6081Details objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.AS6081 obj = new Rebound.GlobalTrader.BLL.AS6081();
                    obj.Id = objDetails.Id;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static List<AS6081> DropDownRiskOfSupplier()
        {
            List<AS6081Details> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.DropDownRiskOfSupplier();
            if (lstDetails == null)
            {
                return new List<AS6081>();
            }
            else
            {
                List<AS6081> lst = new List<AS6081>();
                foreach (AS6081Details objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.AS6081 obj = new Rebound.GlobalTrader.BLL.AS6081();
                    obj.Id = objDetails.Id;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        public static List<AS6081> DropDownAssignSecurityGroup(System.Int32? ClientNo)
        {
            List<AS6081Details> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.DropDownAssignSecurityGroup(ClientNo);
            if (lstDetails == null)
            {
                return new List<AS6081>();
            }
            else
            {
                List<AS6081> lst = new List<AS6081>();
                foreach (AS6081Details objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.AS6081 obj = new Rebound.GlobalTrader.BLL.AS6081();
                    obj.Id = objDetails.Id;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }
        #endregion

        #region Approver change history
        public static List<AS6081> GetApproverChnageLog(System.Int32? ID)
        {
            List<AS6081Details> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.GetApproverChnageLog(ID);
            if (lstDetails == null)
            {
                return new List<AS6081>();
            }
            else
            {
                List<AS6081> lst = new List<AS6081>();
                foreach (AS6081Details objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.AS6081 obj = new Rebound.GlobalTrader.BLL.AS6081();
                    obj.AssignmentHistoryId = objDetails.AssignmentHistoryId;
                    obj.DocumentId = objDetails.DocumentId;
                    obj.DocumentNumber = objDetails.DocumentNumber;
                    obj.AssignedTo = objDetails.AssignedTo;
                    obj.AssignedBy = objDetails.AssignedBy;
                    obj.AssignmentType = objDetails.AssignmentType;
                    obj.LogDate = objDetails.LogDate;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }

        }
        #endregion

        #region Get Country
        public static AS6081 GetCountry(System.Int32? SupplierNo)
        {
            Rebound.GlobalTrader.DAL.AS6081Details objDetails = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.GetCountry(SupplierNo);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                AS6081 obj = new AS6081();
                obj.IsCountryFound = objDetails.IsCountryFound;
                obj.CountryName = objDetails.CountryName;
                obj.CountryNo = objDetails.CountryNo;
                
                objDetails = null;
                return obj;
            }
        }
        #endregion
        #endregion
    }
}
