<%@ Control Language="C#" CodeBehind="SRMAsShip.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAsShip" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlSRMANo" runat="server" ResourceTitle="SRMANo" FilterField="SRMANo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyerName" runat="server" ResourceTitle="BuyerName" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Buyer" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlSRMANotes" runat="server" ResourceTitle="Notes" InitialSearchType="Contains" FilterField="SRMANotes" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlSRMADateFrom" runat="server" ResourceTitle="SupplierRMADateFrom" FilterField="SRMADateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlSRMADateTo" runat="server" ResourceTitle="SupplierRMADateTo" FilterField="SRMADateTo" />
			    <ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />	
            </FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
