﻿using System;
using System.Collections;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.HtmlControls;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Xml.Linq;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class EXCELDocuments_DeleteDragDrop : Base
    {
        #region Locals

        #endregion

        #region Properties

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "EXCEL_Delete");
            AddScriptReference("Controls.Nuggets.EXCELDocumentsDragDrop.Delete.EXCELDocuments_DeleteDragDrop.js");
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.EXCELDocuments_DeleteDragDrop", ctlDesignBase.ClientID);
        }

    }
}