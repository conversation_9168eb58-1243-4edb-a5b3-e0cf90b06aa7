﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public class ClientAddressDetails
    {
        public ClientAddressDetails()
        {

        }

		#region Properties
		/// <summary>
		/// AddressId (from Table)
		/// </summary>
		public System.Int32 AddressId { get; set; }
		/// <summary>
		/// AddressName (from Table)
		/// </summary>
		public System.String AddressName { get; set; }
		/// <summary>
		/// Line1 (from Table)
		/// </summary>
		public System.String Line1 { get; set; }
		/// <summary>
		/// Line2 (from Table)
		/// </summary>
		public System.String Line2 { get; set; }
		/// <summary>
		/// Line3 (from Table)
		/// </summary>
		public System.String Line3 { get; set; }
		/// <summary>
		/// County (from Table)
		/// </summary>
		public System.String County { get; set; }
		/// <summary>
		/// City (from Table)
		/// </summary>
		public System.String City { get; set; }
		/// <summary>
		/// State (from Table)
		/// </summary>
		public System.String State { get; set; }
		/// <summary>
		/// CountryNo (from Table)
		/// </summary>
		public System.Int32? CountryNo { get; set; }
		/// <summary>
		/// ZIP (from Table)
		/// </summary>
		public System.String ZIP { get; set; }
		/// <summary>
		/// Inactive (from Table)
		/// </summary>
		public System.Boolean Inactive { get; set; }
		/// <summary>
		/// UpdatedBy (from Table)
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP (from Table)
		/// </summary>
		public System.DateTime DLUP { get; set; }
		public System.String CountryName { get; set; }
		#endregion
	}
}
