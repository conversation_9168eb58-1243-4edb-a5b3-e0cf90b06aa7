﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-211911]		An.TranTan		19-Oct-2024		CREATE		Replace usp_Import_PriceQuoteImport
[US-211911]		An.TranTan		29-Oct-2024		CREATE		Compare both requirement & supplier part when insert lines to prevent duplicate
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Import_PriceQuote]
    @UserID INT
AS
BEGIN
	DECLARE @PriceQuoteNo INT = 0,
			@PriceQuoteNumber INT = 0,
			@ImportMessage NVARCHAR(2000) = NULL,
			@RecordCount INT = 0,
			@OriginalFilename nvarchar(200) = null,
			@UtilityTypeId INT = NULL;
	
    IF EXISTS
    (
        SELECT (1)
        FROM BorisGlobalTraderImports.dbo.[tbPriceQuoteToBeImported]
        WHERE CreatedBy = @UserID
    )
    BEGIN
	BEGIN TRY
		BEGIN TRANSACTION
        SET NOCOUNT ON
		IF OBJECT_ID('tempdb..#tmpPriceQuoteToBeImported') IS NOT NULL
			DROP TABLE #tmpPriceQuoteToBeImported
		CREATE TABLE #tmpPriceQuoteToBeImported(
			[PriceQuoteImportId] [int],
			[RequirementNo] [int] NULL,
			[RequirementNumber] [int] NULL,
			[Part] [nvarchar](40) NULL,
			[ManufacturerName] [nvarchar](128) NULL,
			[SupplierNo] int NULL,
			[SupplierName] [nvarchar](128) NULL,
			[SupplierCost] [float] NULL,
			[SupplierPart] [nvarchar](100) NULL,
			[LeadTime] [nvarchar](50) NULL,
			[SPQ] [nvarchar](10) NULL,
			[MOQ] [nvarchar](100) NULL,
			[Quantity] [int] NULL,
			[QtyInStock] [nvarchar](20) NULL,
			[DateCode] [nvarchar](100) NULL,
			[PackageName] [nvarchar](100) NULL,
			[CurrencyNo] int NULL,
			[CurrencyCode] [nvarchar](5) NULL,
			[Description] [nvarchar](500) NULL,
			[ROHS] [nvarchar](200) NULL,
			[RegionNo] int NULL,
			[Region] [nvarchar](200) NULL,
			[FactorySealed] [nvarchar](30) NULL,
			[OfferStatusNo] int NULL,
			[OfferStatus] [nvarchar](30) NULL,
			[OriginalFilename] [nvarchar](100) NULL,
			[GeneratedFilename] [nvarchar](100) NULL,
			[ClientId] [int] NULL,
			[SelectedClientId] [int] NULL,
			[CreatedBy] [int] NULL,
			[ImportDate] [datetime] NULL,
			[BuyPrice] [float] NULL,
			[SellPrice] [float] NULL,
			[ShippingCost] [float] NULL,
			[LastTimeBuy] [varchar](50) NULL,
			[DeliveryDate] [varchar](50) NULL,
			[MSLNo] int NULL,
			[MSL] [varchar](100) NULL,
			[LineNumber] [int] NULL
		)
       
        INSERT INTO #tmpPriceQuoteToBeImported
		(
			[PriceQuoteImportId]
			,[RequirementNo]
			,[RequirementNumber]
			,[Part]
			,[ManufacturerName]
			,[SupplierNo]
			,[SupplierName]
			,[SupplierCost]
			,[SupplierPart]
			,[LeadTime]
			,[SPQ]
			,[MOQ]
			,[Quantity]
			,[QtyInStock]
			,[DateCode]
			,[PackageName]
			,[CurrencyNo]
			,[CurrencyCode]
			,[Description]
			,[ROHS]
			,[RegionNo]
			,[Region]
			,[FactorySealed]
			,[OfferStatusNo]
			,[OfferStatus]
			,[OriginalFilename]
			,[GeneratedFilename]
			,[ClientId]
			,[SelectedClientId]
			,[CreatedBy]
			,[ImportDate]
			,[BuyPrice]
			,[SellPrice]
			,[ShippingCost]
			,[LastTimeBuy]
			,[DeliveryDate]
			,[MSLNo]
			,[MSL]
			,[LineNumber]
		)
        SELECT 
			Epo.[PriceQuoteImportId]
			,cr.CustomerRequirementId
			,cr.CustomerRequirementNumber
			,Epo.[Part]
			,Epo.[ManufacturerName]
			,co.CompanyId AS [SupplierNo]
			,Epo.[SupplierName]
			,Epo.[SupplierCost]
			,Epo.[SupplierPart]
			,Epo.[LeadTime]
			,Epo.[SPQ]
			,Epo.[MOQ]
			,Epo.[Quantity]
			,Epo.[QtyInStock]
			,Epo.[DateCode]
			--,O AS [PackageNo]
			,Epo.[PackageName]
			,cur.CurrencyId AS [CurrencyNo]
			,Epo.[CurrencyCode]
			,Epo.[Description]
			,Epo.[ROHS]
			,(
                   SELECT TOP 1
                       RegionId
                   FROM tbRegion c
                   WHERE lower(c.RegionName) = lower(epo.Region) COLLATE SQL_Latin1_General_CP1_CI_AI
            ) AS [RegionNo]
			,Epo.[Region]
			,Epo.[FactorySealed]
			,(
                   SELECT TOP 1
                       OfferStatusId
                   FROM tbOfferStatus c
                   where lower(c.Name) = lower(epo.OfferStatus) COLLATE SQL_Latin1_General_CP1_CI_AI
            ) AS [OfferStatusNo]
			,Epo.[OfferStatus]
			,Epo.[OriginalFilename]
			,Epo.[GeneratedFilename]
			,Epo.[ClientId]
			,Epo.[SelectedClientId]
			,Epo.[CreatedBy]
			,Epo.[ImportDate]
			,Epo.[BuyPrice]
			,Epo.[SellPrice]
			,Epo.[ShippingCost]
			,Epo.[LastTimeBuy]
			,Epo.[DeliveryDate]
			,(
                   SELECT TOP 1
                       MSLLevelId
                   FROM tbMSLLevel c
                   WHERE lower(c.MSLLevel) = lower(epo.MSL) COLLATE SQL_Latin1_General_CP1_CI_AI
						OR lower(c.MSLLevel) = lower('MSL ' + epo.MSL)
            ) AS [MSLNo]
			,Epo.[MSL]
			,Epo.[LineNumber]
        FROM BorisGlobalTraderImports.dbo.[tbPriceQuoteToBeImported] AS Epo
            INNER JOIN dbo.tbcustomerrequirement cr WITH (NOLOCK)
                ON EPO.RequirementNo = cr.CustomerRequirementNumber
                   AND EPO.SelectedClientId = cr.ClientNo
			LEFT JOIN (
				select MIN(co.CompanyId) AS CompanyId, co.CompanyName 
				from dbo.tbCompany co with (nolock)
				join dbo.tbCompanyAddress ca with (nolock)
					ON co.CompanyId = ca.CompanyNo AND ca.DefaultBilling = 1
				where co.Inactive = 0 
					AND co.POApproved = 1
					AND ca.CeaseDate IS NULL
				group by co.CompanyName
			) co ON co.CompanyName = epo.SupplierName
			LEFT JOIN (
				SELECT tc.CurrencyId, tc.CurrencyCode
                FROM dbo.tbCurrency tc
                WHERE tc.Buy = 1 AND ISNULL(Inactive, 0) = 0 AND ClientNo = 114
			) cur ON cur.CurrencyCode = epo.CurrencyCode
        WHERE ISNULL(LEN(Epo.[SupplierPart]), 0) > 0
              and epo.CreatedBy = @UserID

        /****************** To insert Purchase Request Header / tbPurchaseRequest ***************************/

        DECLARE @ClientNo INT,
                @Division INT,
                @Notes VARCHAR(500),
                @PurchaseRequestId INT = 0;
        SELECT TOP 1
            @ClientNo = [ClientId],
            @Notes = [Description]
        from #tmpPriceQuoteToBeImported                                                      
        
        SELECT @Division = DivisionNo
        from tbLogin with(nolock)
        where loginid = @UserID

        EXEC usp_insert_PurchaseRequest @ClientNo,
                                        @Notes,
                                        @UserID,
                                        @UserID,
                                        @Division,
                                        @PurchaseRequestId OUTPUT,
                                        1
        SET @PriceQuoteNo = @PurchaseRequestId;
		SELECT @PriceQuoteNo = PurchaseRequestId,
				@PriceQuoteNumber = PurchaseRequestNumber
		FROM tbPurchaseRequest WHERE PurchaseRequestId = @PurchaseRequestId;
        /*********************************************/

		/************************ Insert PurchaseRequestLine and PurchaseRequestLineDetail ************************/
		DECLARE @InsertedPurchaseRequestLine TABLE (PurchaseRequestLineId INT);

		;WITH ctePriceQuote AS
		(
			SELECT DISTINCT [RequirementNo], [SupplierPart]
            FROM #tmpPriceQuoteToBeImported
		)
		INSERT INTO dbo.tbPurchaseRequestLine
	    (
	        PurchaseRequestNo,
	        CustomerRequirementNo,
	        BOMNo,
	        FullPart,
	        Part,
			--SupplierPart,
	        Price,
	        Closed,
	        UpdatedBy,
	        DLUP
	    )
		OUTPUT INSERTED.PurchaseRequestLineId INTO @InsertedPurchaseRequestLine(PurchaseRequestLineId)
	    SELECT @PurchaseRequestId,
	           cr.CustomerRequirementId,
	           cr.BOMNo,
	           --cr.FullPart,
	           --cr.Part,
			   dbo.ufn_get_fullpart(pq.SupplierPart),
			   pq.SupplierPart,
			   --pq.SupplierPart,
	           0,
	           0,
	           @UserID,
	           CURRENT_TIMESTAMP
	    FROM dbo.tbCustomerRequirement cr
	        JOIN ctePriceQuote pq ON pq.[RequirementNo] = cr.CustomerRequirementId;

        INSERT INTO tbPurchaseRequestLineDetail
        (
            PurchaseRequestLineNo,
            CompanyNo,
            Price,
            SPQ,
            LeadTime,
            ROHSStatus,
            FactorySealed,
            MSL,
            UpdatedBy,
            DLUP,
            ManufacturerName,
            DateCode,
            PackageType,
            ProductType,
            MOQ,
            TotalQuantityAvailableInStock,
            LTB,
            Notes,
            CurrencyNo,
            MSLLevelNo,
            UpdateSource,
            UpdateDate,
            Quantity,
            ShippingCost,
            DeliveryDate,
            RegionNO,
            OfferStatusNo,
            --ROHSStatusNo,
            BuyPrice,
            SellPrice
        )
        SELECT tpl.PurchaseRequestLineId as PurchaseRequestLineNo,
               epo.SupplierNo, --CAST(epo.SupplierNo as int),
               /*[001]*/
               --Epo.[SupplierCost],    
               Epo.BuyPrice,
               /*[001]*/
               Epo.[SPQ],
               Epo.[LeadTime],
			   CASE 
					WHEN ISNULL(Epo.[ROHS], '') = 'Y' THEN 'Y'
					ELSE 'N' 
			   END AS ROHSStatus,
               Epo.[FactorySealed],
               EPO.MSL as MSL,
               CreatedBy as UpdatedBy,
               Getdate() as DLUP,
               Epo.[ManufacturerName],
               Epo.[DateCode],
               Epo.[PackageName],
               NULL as [ProductType],
               Epo.[MOQ],
               Epo.[QtyInStock],
               Epo.[LastTimeBuy],
               Epo.[Description] as Notes,
               Epo.[CurrencyNo] AS CurrencyNo,
               Epo.[MSLNo] AS [MSLLevelNo],
               'Insert by PQ Utility' AS UpdateSource,
               GETDATE() AS UpdateDate,
               Epo.Quantity,
               Epo.ShippingCost,
               CONVERT(DATETIME, Epo.DeliveryDate, 103),
               Epo.RegionNo,
               Epo.[OfferStatusNo],
               --CAST(Epo.ROHS AS INT),
               Epo.BuyPrice,
               Epo.SellPrice
		FROM #tmpPriceQuoteToBeImported Epo
			INNER JOIN dbo.tbPurchaseRequestLine tpl
				on Epo.RequirementNo = tpl.CustomerRequirementNo
				and epo.SupplierPart = tpl.Part
			INNER JOIN @InsertedPurchaseRequestLine ipl
				on ipl.PurchaseRequestLineId = tpl.PurchaseRequestLineId;

        /************************ END ************************/
		
        /*[001]*/
		SELECT @RecordCount = COUNT(*),
			@OriginalFilename = OriginalFilename
		FROM BorisGlobalTraderImports.dbo.[tbPriceQuoteToBeImported] 
		WHERE CreatedBy = @UserID
		GROUP BY OriginalFilename;

        INSERT INTO tbPurchaseQuoteCSVLog
        (
            PurchaseQuoteNo,
            FileName,
            Status,
            Message,
            LogDate
        )VALUES(@PurchaseRequestId, @OriginalFilename, 1, 'Purchase request imported in GT', GETDATE())

        INSERT INTO dbo.tbPurchaseQuoteCSV
        (
            PurchaseQuoteNo,
            Caption,
            FileName,
            UpdatedBy,
            DLUP
        )VALUES(@PurchaseRequestId, 'Purchase request imported in GT', @OriginalFilename, @UserID, GETDATE())

        INSERT INTO tbPriceRequestLog
        (
            PurchaseRequestLineNo,
            CompanyNo,
            ReadedFileName,
            StatusType,
            UnitPrice,
            AddedDate,
            DLUP
        )
        SELECT tpl.PurchaseRequestLineID,
               Epo.SupplierNo,
               Epo.originalfilename,
               'Inserted',
               /*[001]*/
               --Epo.[SupplierCost],    
               Epo.BuyPrice,
               /*[001]*/
               GETDATE(),
               GETDATE()
		FROM #tmpPriceQuoteToBeImported Epo
			INNER JOIN dbo.tbPurchaseRequestLine tpl
				on Epo.RequirementNo = tpl.CustomerRequirementNo
			INNER JOIN @InsertedPurchaseRequestLine ipl
				on ipl.PurchaseRequestLineId = tpl.PurchaseRequestLineId;

        /********* For insertion in Sourcing table RP-2210 ********/
        EXEC [usp_insert_SourcingResult_From_PurchaseRequestQuoteUtility] @PurchaseRequestId

        /********* END*************************************************/
        ---------------------------Import History------------------------------------                                                                                         
		SELECT TOP 1 @UtilityTypeId = UtilityTypeId FROM dbo.tbUtilityType WHERE UtilityTypeName = 'Price Quote Import Tool';
        INSERT INTO BorisGlobalTraderImports.dbo.tbUtilityLog
        (
            FileName,
            UtilityType,
            Clientid,
            LoginNo,
            DLUP,
            iRowCount
        )
        VALUES
        (@OriginalFilename, @UtilityTypeId, @ClientNo, @UserID, GETDATE(), @RecordCount)
		SET @ImportMessage = 'Import Success';
        INSERT INTO BorisGlobalTraderImports.dbo.[tbPriceQuote_ImportHistory]
        (
            ImportDate,
            ImportName,
            RowsAffected,
            Target,
            ClientType
        )
        SELECT GETDATE(),
               @OriginalFilename,
               count(*),
               @ImportMessage,
               1
        FROM #tmpPriceQuoteToBeImported a

		INSERT INTO BorisGlobalTraderImports.dbo.tbImportActivity
		(
			[SupplierName]
			,[ImportName]
			,[RowsAffected]
			,[Target]
			,[SelectedClientId]
			,[CreateBy]
			,[ClientType]
		)VALUES('', @OriginalFilename, @RecordCount, @ImportMessage, @ClientNo, @UserID, 1)
        ----------------------------Import History End--------------------------------                                                                   

		--DELETE temp data
        DELETE BorisGlobalTraderImports.dbo.[tbPriceQuoteToBeImported] WHERE CreatedBy = @UserID
		DELETE BorisGlobalTraderimports.dbo.[tbPriceQuoteImport_tempData] WHERE CreatedBy = @UserID                 
		DELETE BorisGlobalTraderimports.dbo.[tbPriceQuoteImportColumnHeading] WHERE CreatedBy = @UserID 

        DROP TABLE #tmpPriceQuoteToBeImported

        SET NOCOUNT OFF
		COMMIT TRANSACTION;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION

		SET @ImportMessage = Error_message();

		DROP TABLE #tmpPriceQuoteToBeImported;
		SET NOCOUNT OFF;
	END CATCH
    END

	--Return result
	SELECT 
		@PriceQuoteNo AS PriceQuoteNo
		,@PriceQuoteNumber AS PriceQuoteNumber
		,@RecordCount AS RecordCount
		,@ImportMessage AS ImportMessage
END
GO
/*test script
exec usp_Import_PriceQuote @UserID = 6670

*/

