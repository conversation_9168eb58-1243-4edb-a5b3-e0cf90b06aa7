<%@ Control Language="C#" CodeBehind="ClientImportBOMMainInfo_Delete.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Delete" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMMainInfo_Delete")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlName" runat="server" FieldID="lblName" ResourceTitle="Name">
				<Field><asp:Label ID="lblName" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
