using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
    public partial class GITempLotNumber : Base
    {

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            SetItemSearchType("GITempLotNumber");
			AddScriptReference("Controls.ItemSearch.GITempLotNumber.GITempLotNumber.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.ItemSearch.GITempLotNumber", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
            ctlDesignBase.MakeChildControls();
            //EnsureChildControls();
            //IconButton ibtn = new IconButton();
            //ibtn.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
            //ibtn.IconGroup = IconButton.IconGroupList.Nugget;
            //ibtn.IconTitleResource = "SelectAll";
           // ibtn.Alignment = "Left";
            //ctlDesignBase.Controls.Add(ibtn);
            //ctlDesignBase.MakeChildControls();
            //ctlDesignBase.tblResults.AllowMultipleSelection = false;
            //ctlDesignBase.tblResults.AllowSelection = true;
            //ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SubGroup", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
            //ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("LotNo", Unit.Empty, true));
            
            ctlDesignBase.tblResults.AllowSelection = true;
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("SubGroup", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("LotNo", WidthManager.GetWidth(WidthManager.ColumnWidth.Warehouse)));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("GI", WidthManager.GetWidth(WidthManager.ColumnWidth.Warehouse)));
            ctlDesignBase.tblResults.Columns.Add(new FlexiDataColumn("Action", Unit.Empty, false));
			

            //_tblLotNodetails.AllowMultipleSelection = true;
            //_tblLotNodetails.AllowSelection = true;
            //_tblLotNodetails.Columns.Add(new FlexiDataColumn("SubGroup", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));
            //_tblLotNodetails.Columns.Add(new FlexiDataColumn("LotNo", Unit.Empty, true));

			base.OnPreRender(e);
		}

       
	}
}