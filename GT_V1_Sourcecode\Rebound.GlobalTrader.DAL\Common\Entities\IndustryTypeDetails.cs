﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class IndustryTypeDetails {
		
		#region Constructors
		
		public IndustryTypeDetails() { }
		
		#endregion
		
		#region Properties
	
		/// <summary>
		/// IndustryTypeId (from Table)
		/// </summary>
		public System.Int32 IndustryTypeId { get; set; }
		/// <summary>
		/// Name (from Table)
		/// </summary>
		public System.String Name { get; set; }

		public System.String WarningName { get; set; }
		/// <summary>
		/// Inactive (from Table)
		/// </summary>
		public System.Boolean Inactive { get; set; }
		/// <summary>
		/// UpdatedBy (from Table)
		/// </summary>
		public System.Int32? UpdatedBy { get; set; }
		/// <summary>
		/// DLUP (from Table)
		/// </summary>
		public System.DateTime? DLUP { get; set; }
		/// <summary>
		/// SystemWarningMessageid
		/// </summary>
		public System.Int32 SystemWarningMessageid { get; set; }

		#endregion

	}
}