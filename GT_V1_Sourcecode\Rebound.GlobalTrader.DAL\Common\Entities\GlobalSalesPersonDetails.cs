﻿/* Marker    changed by      date           Remarks
  [001]      Vinay           11/08/2014     ESMS  Ticket Number: 	200
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class GlobalSalesPersonDetails
	{
		
		#region Constructors
		
		public GlobalSalesPersonDetails() { }

		#endregion

		#region Properties
		public System.Int32? GlobalSalesPersonId { get; set; }
		public System.Int32? CompanyNo { get; set; }
        public System.String SalesPersonName { get; set; }
		public System.String ClientName { get; set; }
		public System.Int32? LoginNo { get; set; }

		#endregion

	}
}