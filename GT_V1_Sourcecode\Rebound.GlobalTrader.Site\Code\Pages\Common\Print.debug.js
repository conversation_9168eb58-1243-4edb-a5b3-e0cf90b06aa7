///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 16.07.2010:
// - remove unused properties
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");

Rebound.GlobalTrader.Site.Pages.Print = function() { 
	Rebound.GlobalTrader.Site.Pages.Print.initializeBase(this);
	this._frmEmail = null;
	this._pnlEmailLinks = null;
	this._strInitialToEmail = "";
	this._strInitialToContactName = "";
	this._strInitialSubject = "";
};

Rebound.GlobalTrader.Site.Pages.Print.prototype = {

	get_frmEmail: function() { return this._frmEmail; }, 	set_frmEmail: function(v) { if (this._frmEmail !== v)  this._frmEmail = v; }, 
	get_pnlEmailLinks: function() { return this._pnlEmailLinks; }, 	set_pnlEmailLinks: function(v) { if (this._pnlEmailLinks !== v)  this._pnlEmailLinks = v; }, 
	get_strInitialToEmail: function() { return this._strInitialToEmail; }, 	set_strInitialToEmail: function(v) { if (this._strInitialToEmail !== v)  this._strInitialToEmail = v; }, 
	get_strInitialToContactName: function() { return this._strInitialToContactName; }, 	set_strInitialToContactName: function(v) { if (this._strInitialToContactName !== v)  this._strInitialToContactName = v; }, 
	get_strInitialSubject: function() { return this._strInitialSubject; }, 	set_strInitialSubject: function(v) { if (this._strInitialSubject !== v)  this._strInitialSubject = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.WebServices.CheckLoggedIn(Function.createDelegate(this, this.loginOK), Function.createDelegate(this, this.closeWindow));
		if (this._frmEmail) {
			this._frmEmail.addCancel(Function.createDelegate(this, this.closeWindow));
			this._frmEmail.addSaveComplete(Function.createDelegate(this, this.closeWindow));
			this.showForm(true);
			this._frmEmail._ctlTo.clearItems();
			this._frmEmail._ctlTo.addItem(null, null, $R_RES.EmailTo, this._strInitialToEmail.trim(), "", String.format("({0})", this._strInitialToContactName));
			this._frmEmail.setFieldValue("ctlSubject", this._strInitialSubject);
		}
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._frmEmail) this._frmEmail.dispose();
		this._frmEmail = null;
		this._pnlEmailLinks = null;
		this._strInitialToEmail = null;
		this._strInitialToContactName = null;
		this._strInitialSubject = null;
		this.isDisposed = true;
	},
		
	loginOK: function(result) { 
		if (!result) {
			//alert($R_RES.PopupLoggedOut);
			window.close();
		}
	},
	
	closeWindow: function() { 
		window.close(); 
	},
	
	showForm: function(bln) {
		this._frmEmail.show(bln);
		$R_FN.showElement(this._pnlEmailLinks, bln);
	}

};
Rebound.GlobalTrader.Site.Pages.Print.registerClass("Rebound.GlobalTrader.Site.Pages.Print", null, Sys.IDisposable);
