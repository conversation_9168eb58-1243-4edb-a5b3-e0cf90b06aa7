Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday.prototype={get_pnlPartsOrdered:function(){return this._pnlPartsOrdered},set_pnlPartsOrdered:function(n){this._pnlPartsOrdered!==n&&(this._pnlPartsOrdered=n)},get_tblPartsOrdered:function(){return this._tblPartsOrdered},set_tblPartsOrdered:function(n){this._tblPartsOrdered!==n&&(this._tblPartsOrdered=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblPartsOrdered&&this._tblPartsOrdered.dispose(),this._pnlPartsOrdered=null,this._tblPartsOrdered=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlPartsOrdered,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/PartsBeingOrderedToday");n.set_DataObject("PartsBeingOrderedToday");n.set_DataAction("GetData");n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,u,t,r;for($R_FN.showElement(this._pnlMore,!0),this._tblPartsOrdered.clearTable(),r=0;r<i.PartsOrderedToday.length;r++)t=i.PartsOrderedToday[r],u=[$RGT_nubButton_PurchaseOrder(t.ID,t.No),t.Part,t.LineValue,t.Buyer],isPoApproved=t.PoApprovedBy==0?!0:!1,RowColor=isPoApproved==!0?"red-backgroundpart":"",this._tblPartsOrdered.addRowRowColor(u,t.ID,!1,null,null,isPoApproved,RowColor);$R_FN.showElement(this._pnlPartsOrdered,i.PartsOrderedToday.length>0);this.hideLoading();this.showNoneFoundOrContent(i.PartsOrderedToday.length)}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PartsBeingOrderedToday",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);