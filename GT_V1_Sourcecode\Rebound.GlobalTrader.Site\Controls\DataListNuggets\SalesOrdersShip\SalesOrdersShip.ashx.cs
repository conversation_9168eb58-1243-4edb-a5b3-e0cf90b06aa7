/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */
/* [0002]      A<PERSON><PERSON><PERSON>   28-09-2021 Add new columns for Ship SO Epic(Gr2-44) */
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Configuration;
using Rebound.GlobalTrader.Site.Enumerations;
using Rebound.GlobalTrader.Site.Code.Common;
namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class SalesOrdersShip : Base {

		private string _strCallType = "";

		public override void ProcessRequest(HttpContext context) {
			base.ProcessRequest(context);
			if (Action == "GetData_All") GetData_All();
            if (Action == "ExportToCSV") ExportToCSV();

        }

		protected override void GetData() {
			_strCallType = "READY";
			JsonObject jsn = new JsonObject();
			JsonObject jsnRows = new JsonObject(true);
			List<SalesOrderLine> lst = SalesOrderLine.DataListNuggetReadyToShip(
				SessionManager.ClientID,
				GetFormValue_NullableInt("SortIndex"),
				GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
				GetFormValue_NullableInt("PageIndex", 0),
				GetFormValue_NullableInt("PageSize", 10),
				 //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                   GetFormValue_PartForLikeSearch("Part"),
                //[0001] end code
               // GetFormValue_StringForNameSearch("Contact"),
                GetFormValue_StringForNameSearchDecode("Contact"),
                //GetFormValue_StringForNameSearch("CMName"),
                 GetFormValue_StringForNameSearchDecode("CMName"),
				GetFormValue_NullableInt("Salesman"),
				GetFormValue_StringForSearch("CustPO"),
				GetFormValue_NullableInt("SONoLo"),
				GetFormValue_NullableInt("SONoHi"),
				GetFormValue_NullableDateTime("DateOrderedFrom"),
				GetFormValue_NullableDateTime("DateOrderedTo"),
				GetFormValue_NullableDateTime("DatePromisedFrom"),
				GetFormValue_NullableDateTime("DatePromisedTo"),
                GetFormValue_NullableInt("Client"),
                GetFormValue_Boolean("IsGlobalLogin"),
                GetFormValue_NullableInt("Warehouse"),
                GetFormValue_NullableInt("BuyShipMethod"),
                GetFormValue_Boolean("ShipASAP"),
                GetFormValue_StringForSearch("Location"),
                GetFormValue_Boolean("EnhancedInspection"),
                GetFormValue_NullableInt("CheckedBy"), 
                GetFormValue_NullableInt("ReadyStatus")

            );
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].SalesOrderNo);
				jsnRow.AddVariable("No", lst[i].SalesOrderNumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("StockNo", lst[i].StockNo);
				jsnRow.AddVariable("Allocated", Functions.FormatNumeric(lst[i].AllocatedQuantity));
				jsnRow.AddVariable("DateOrdered", Functions.FormatDate(lst[i].DateOrdered));
                jsnRow.AddVariable("DatePromised", Functions.FormatDate(lst[i].DatePromised));
                jsnRow.AddVariable("RequiredDate", Functions.FormatDate(lst[i].RequiredDate));
				jsnRow.AddVariable("ShipASAP", lst[i].ShipASAP ? "Yes" : "No");
				jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
				jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("CustPONO", lst[i].CustomerPO);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
                //if (GetFormValue_NullableInt("ReadyStatus") == 1 || GetFormValue_NullableInt("ReadyStatus") == null)
                jsnRow.AddVariable("Status", Functions.GetGlobalResource("Status", "ReadyToShip"));
                //else if(GetFormValue_NullableInt("ReadyStatus") == 7)
                //    jsnRow.AddVariable("Status", "Hold for the remaining balance");
                //else if(GetFormValue_NullableInt("ReadyStatus") == 8)
                //    jsnRow.AddVariable("Status", "Ship Partial");
                jsnRow.AddVariable("ClientName", lst[i].ClientName);
                jsnRow.AddVariable("QuantityInStock", lst[i].QuantityInStock);
                jsnRow.AddVariable("WarehouseName", lst[i].WarehouseName);
                jsnRow.AddVariable("Location", lst[i].Location);
                jsnRow.AddVariable("ShipViaName", lst[i].ShipViaName);
                jsnRow.AddVariable("EnhancedInspectionReq", lst[i].EnhancedInspectionReq);
                jsnRow.AddVariable("CheckedBy", lst[i].CheckedBy);
                jsnRow.AddVariable("ExpPageSize", GetFormValue_NullableInt("PageSize", 10));
                jsnRow.AddVariable("ExpPageIndex", GetFormValue_NullableInt("PageIndex", 0));
                jsnRow.AddVariable("ExpSortIndex", GetFormValue_NullableInt("SortIndex"));
                jsnRow.AddVariable("ExpSortDir", GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC));
                jsnRow.AddVariable("SOSerialNumber", lst[i].SOSerialNo);
                jsnRow.AddVariable("OGELApprovalStatus", lst[i].OGELApprovalStatus);
                jsnRow.AddVariable("OGELRequired", lst[i].OGELRequired);
                jsnRows.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRows);
			OutputResult(jsn);
			jsnRows.Dispose(); jsnRows = null;
			jsn.Dispose(); jsn = null;
			base.GetData();
		}

		private void GetData_All() {
			_strCallType = "ALL";
			JsonObject jsn = new JsonObject();
			JsonObject jsnRows = new JsonObject(true);
			List<SalesOrderLine> lst = SalesOrderLine.DataListNuggetAllForShipping(
				SessionManager.ClientID
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
			    //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                   ,GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
               // GetFormValue_StringForNameSearch("Contact"),
                ,GetFormValue_StringForNameSearchDecode("Contact")
                //GetFormValue_StringForNameSearch("CMName"),
                 ,GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_StringForSearch("CustPO")
				, GetFormValue_Boolean("RecentOnly")
				, GetFormValue_Boolean("IncludeClosed")
				, GetFormValue_NullableInt("SONoLo")
				, GetFormValue_NullableInt("SONoHi")
				, GetFormValue_NullableDateTime("DateOrderedFrom")
				, GetFormValue_NullableDateTime("DateOrderedTo")
				, GetFormValue_NullableDateTime("DatePromisedFrom")
				, GetFormValue_NullableDateTime("DatePromisedTo")
                , GetFormValue_NullableInt("Client")
                , GetFormValue_Boolean("IsGlobalLogin")
                , GetFormValue_NullableInt("Warehouse")
                , GetFormValue_NullableInt("BuyShipMethod")
                , GetFormValue_Boolean("ShipASAP")
                , GetFormValue_StringForSearch("Location")
                , GetFormValue_Boolean("EnhancedInspection")
                , GetFormValue_NullableInt("CheckedBy")
                , GetFormValue_NullableInt("Status")
            );
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].SalesOrderNo);
				jsnRow.AddVariable("No", lst[i].SalesOrderNumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("StockNo", lst[i].StockNo);
				jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode));
				jsnRow.AddVariable("Allocated", Functions.FormatNumeric(lst[i].QuantityAllocated));
				jsnRow.AddVariable("DateOrdered", Functions.FormatDate(lst[i].DateOrdered));
				jsnRow.AddVariable("RequiredDate", Functions.FormatDate(lst[i].RequiredDate));
				jsnRow.AddVariable("ShipASAP", lst[i].ShipASAP ? "Yes" : "No");
				jsnRow.AddVariable("CustomerPart", lst[i].CustomerPart);
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("CustPONO", lst[i].CustomerPO);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
                jsnRow.AddVariable("Status", SalesOrderManager.FormatLineAllocationShippingStatus(lst[i].LineCannotShipReasons));
                jsnRow.AddVariable("DatePromised", Functions.FormatDate(lst[i].DatePromised));
                jsnRow.AddVariable("ClientName", lst[i].ClientName);
                jsnRow.AddVariable("QuantityInStock", lst[i].QuantityInStock);
                jsnRow.AddVariable("WarehouseName", lst[i].WarehouseName);
                jsnRow.AddVariable("Location", lst[i].Location);
                jsnRow.AddVariable("ShipViaName", lst[i].ShipViaName);
                jsnRow.AddVariable("EnhancedInspectionReq", lst[i].EnhancedInspectionReq);
                jsnRow.AddVariable("CheckedBy", lst[i].CheckedBy);
                jsnRow.AddVariable("ExpPageSize", GetFormValue_NullableInt("PageSize", 10));
                jsnRow.AddVariable("ExpPageIndex", GetFormValue_NullableInt("PageIndex", 0));
                jsnRow.AddVariable("ExpSortIndex", GetFormValue_NullableInt("SortIndex"));
                jsnRow.AddVariable("ExpSortDir", GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC));
                jsnRow.AddVariable("SOSerialNumber", lst[i].SOSerialNo);
                jsnRow.AddVariable("OGELApprovalStatus", lst[i].OGELApprovalStatus);
                jsnRow.AddVariable("OGELRequired", lst[i].OGELRequired);
                jsnRows.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRows);
			OutputResult(jsn);
			jsnRows.Dispose(); jsnRows = null;
			jsn.Dispose(); jsn = null;
			SaveState();
		}

        public void ExportToCSV()
        {
            JsonObject jsn = new JsonObject();
            string filePath = string.Empty;
            DataTable dtResult=new DataTable();
            string strFilename = String.Format("ShipSO_u{0}r{1}.xlsx", LoginID, 0);
            try
            {
                string strType = GetFormValue_StringForSearch("Type");

               if(strType.ToUpper()=="ALL")
                {
                    dtResult= SalesOrderLine.DataListNuggetAllForShipping_Export(
                                   SessionManager.ClientID
                                   , GetFormValue_NullableInt("EXPSortIndex")
                                   , GetFormValue_NullableInt("ExpSortDir", SortColumnDirection.ASC)
                                   , GetFormValue_NullableInt("ExpPageIndex", 0)
                                   , GetFormValue_NullableInt("ExpPageSize", 10)
                                   , GetFormValue_PartForLikeSearch("ExpPart")
                                   , GetFormValue_StringForNameSearchDecode("ExpContact")
                                   , GetFormValue_StringForNameSearchDecode("ExpCMName")
                                   , GetFormValue_NullableInt("ExpSalesman")
                                   , GetFormValue_StringForSearch("ExpCustPO")
                                   , GetFormValue_Boolean("ExpRecentOnly")
                                   , GetFormValue_Boolean("ExpIncludeClosed")
                                   , GetFormValue_NullableInt("ExpSONoLo")
                                   , GetFormValue_NullableInt("ExpSONoHi")
                                   , GetFormValue_NullableDateTime("ExpDateOrderedFrom")
                                   , GetFormValue_NullableDateTime("ExpDateOrderedTo")
                                   , GetFormValue_NullableDateTime("ExpDatePromisedFrom")
                                   , GetFormValue_NullableDateTime("ExpDatePromisedTo")
                                   , GetFormValue_NullableInt("ExpClient")
                                   , GetFormValue_Boolean("IsGlobalLogin")
                                   , GetFormValue_NullableInt("ExpWarehouse")
                                   , GetFormValue_NullableInt("ExpBuyShipMethod")
                                   , GetFormValue_Boolean("ExpShipASAP")
                                   , GetFormValue_StringForSearch("ExpLocation")
                                   , GetFormValue_Boolean("ExpEnhancedInspection")
                                   , GetFormValue_NullableInt("ExpCheckedBy")
                                   , GetFormValue_NullableInt("ExpStatus")
                               );
                    filePath = (new EPPlusExportUtility()).ExportDataTableToCSVShipSO(dtResult, strFilename, "SHIP_SO_ALL");
                }
               else if(strType.ToUpper() == "READY")
                {
                dtResult = SalesOrderLine.DataListNuggetReadyToShip_Export(
                SessionManager.ClientID,
                GetFormValue_NullableInt("EXPSortIndex"),
                GetFormValue_NullableInt("ExpSortDir", SortColumnDirection.ASC),
                GetFormValue_NullableInt("ExpPageIndex", 0),
                GetFormValue_NullableInt("ExpPageSize", 10),
                GetFormValue_PartForLikeSearch("ExpPart"),
                GetFormValue_StringForNameSearchDecode("ExpContact"),
                GetFormValue_StringForNameSearchDecode("ExpCMName"),
                GetFormValue_NullableInt("ExpSalesman"),
                GetFormValue_StringForSearch("ExpCustPO"),
                GetFormValue_NullableInt("ExpSONoLo"),
                GetFormValue_NullableInt("ExpSONoHi"),
                GetFormValue_NullableDateTime("ExpDateOrderedFrom"),
                GetFormValue_NullableDateTime("ExpDateOrderedTo"),
                GetFormValue_NullableDateTime("ExpDatePromisedFrom"),
                GetFormValue_NullableDateTime("ExpDatePromisedTo"),
                GetFormValue_NullableInt("ExpClient"),
                GetFormValue_Boolean("IsGlobalLogin"),
                GetFormValue_NullableInt("ExpWarehouse"),
                GetFormValue_NullableInt("ExpBuyShipMethod"),
                GetFormValue_Boolean("ExpShipASAP"),
                GetFormValue_StringForSearch("ExpLocation"),
                GetFormValue_Boolean("ExpEnhancedInspection"),
                GetFormValue_NullableInt("ExpCheckedBy"),
                GetFormValue_NullableInt("ExpReadyStatus")
            );
                    filePath = (new EPPlusExportUtility()).ExportDataTableToCSVShipSO(dtResult, strFilename, "SHIP_SO_READYTOSHIP");
                }
               
                jsn.AddVariable("Filename", filePath);
                OutputResult(jsn);

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
                jsn.Dispose();
                jsn = null;
            }
        }
        protected override void AddFilterStates() {
			AddExplicitFilterState("CallType", _strCallType);
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Salesman");
			AddFilterState("CustPO");
			AddFilterState("SONo");
			AddFilterState("DateOrderedFrom");
			AddFilterState("DateOrderedTo");
			AddFilterState("DatePromisedFrom");
			AddFilterState("DatePromisedTo");
			AddFilterState("RecentOnly");
			AddFilterState("IncludeClosed");
            AddFilterState("Warehouse");
            AddFilterState("BuyShipMethod");
            AddFilterState("Location");
            AddFilterState("CheckedBy");
            AddFilterState("ShipASAP");
            AddFilterState("EnhancedInspection");
            AddFilterState("ReadyStatus");
            AddFilterState("Status");
            base.AddFilterStates();
		}

	}
}
