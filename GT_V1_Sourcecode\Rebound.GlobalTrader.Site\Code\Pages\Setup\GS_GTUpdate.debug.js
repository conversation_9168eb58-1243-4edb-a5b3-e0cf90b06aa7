///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate = function(el) { 
    Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.prototype = {

    get_ctlGTUpdate: function () { return this._ctlGTUpdate; }, set_ctlGTUpdate: function (v) { if (this._ctlGTUpdate !== v) this._ctlGTUpdate = v; },
    // get_ctlCertificate: function() { return this._ctlCertificate; }, set_ctlCertificate: function(v) { if (this._ctlCertificate !== v) this.ctlGTUpdate = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlGTUpdate) this._ctlGTUpdate.addSelectCategory(Function.createDelegate(this, this.ctlCertificatecategory_SelectCategory));
        // if (this._ctlCertificate) this._ctlCertificate.addChangedData(Function.createDelegate(this, this.ctlCurrencyRates_ChangedData));
        Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlGTUpdate) this._ctlGTUpdate.dispose();
       // if (this._ctlCertificate) this._ctlCertificate.dispose();
        this._ctlGTUpdate = null;
        //this._ctlCertificate = null;
        Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.callBaseMethod(this, "dispose");
    },

    ctlCertificatecategory_SelectCategory: function() {
        //this._ctlCertificate._intCertificatecategoryID = this._ctlGTUpdate._intCertificateCategoryID;
        //this._ctlCertificate._tbl.resizeColumns();
        //this._ctlCertificate.show(true);
        //this._ctlCertificate.refresh();
    }

};

Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_GTUpdate", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
