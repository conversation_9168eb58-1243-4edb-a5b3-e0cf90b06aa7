using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class Services : Base {

		#region Locals

		#endregion

		#region Properties

			#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("Services");
			base.OnInit(e);
			AddScriptReference("Controls.DataListNuggets.Services.Services.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "Services");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}


		#endregion

		private void SetupTable() {
			_tbl.Columns.Add(new FlexiDataColumn("Name", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
			_tbl.Columns.Add(new FlexiDataColumn("Description", Unit.Empty, true));
			_tbl.Columns.Add(new FlexiDataColumn("Lot", WidthManager.GetWidth(WidthManager.ColumnWidth.ListName), true));
			_tbl.Columns.Add(new FlexiDataColumn("Cost", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			_tbl.Columns.Add(new FlexiDataColumn("Resale", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services", ctlDesignBase.ClientID);
		}
	}
}