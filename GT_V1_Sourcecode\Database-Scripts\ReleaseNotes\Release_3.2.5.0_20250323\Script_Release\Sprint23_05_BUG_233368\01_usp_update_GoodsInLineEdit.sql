﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_update_GoodsInLineEdit]                                                                                                                     
--**********************************************************************************************                                                                                                                                                               
--*[003]      Ravi     21-03-2023   [RP-968] Barcode scan code and remarks                                               
--*[004]      Abhinav <PERSON>xena  09-05-2023  [RP-1530] Add new query columns.                                    
--*[005]      Abhinav <PERSON>  20-07-2023  [RP-1952] Stock Not Update comment in Ticket by <PERSON>                                
--*[006]      Ravi  RP-2062                           
--*[007]      Abhinav <PERSON>xena  04-08-2023 [RP-2003]

/* 
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-205091]		Trung Pham			20-Jun-2024		FIX				205091: [PROD Bug] The unstable data is missing in temp table. Copy from live table to temp table in order to consist
																		Update records that have inspection status = 1 means inprogress/ start inspection
[BUG-233368]		Trung Pham			11-Mar-2025		FIX				Make consistence between client and supplier invoice when editing GILine
===========================================================================================
*/
--**********************************************************************************************                                                                                                                       
 (                                                                                                                                                                                                           
 @GoodsInLineId Int,                                                                                                                                                                              
 @ActShipInCost Decimal=NULL,                                                                                                                                                                              
 @QualityControlNotes NVarChar(200)=NULL,                                                                                                                                                                              
 @Location NVarChar(200)=NULL,                                                                                                                                                                              
 @LotNo Int=NULL,                                                                                                                                                                              
 @UpdateStock Bit=NULL,                                                                                                                                                                              
 @UpdateShipments Bit=NULL,                                                                                                                                                                                
 @UpdatedBy Int=NULL,                                                                                                                                                                              
 @IsFullQuantityReceived Bit=NULL,                                                                                                                                                                              
 @quantityReceived Int=NULL,                                                                                                                                                                              
 @IsPartNumberCorrect Bit=NULL,                                                                                                                                                                              
 @CorrectPartNo NVarChar(200)=NULL,                                                                                                                                                                              
 @IsManufacturerCorrect Bit=NULL,                                    
 @CorrectManufacturerNo Int=NULL,                             
 @IsDateCodeCorrect Bit=NULL,               
 @CorrectDateCode NVarChar(200)=NULL,       
 @IsPackageCorrect Bit=NULL,                                                   
 @CorrectPackageNo Int=NULL,                                                                                
 @IsMSLCorrect Bit=NULL,                                                 
 @CorrectMslNo NVarChar(200)=NULL,                                                            
 @IsHICCorrect Bit=NULL,                                                                                                                            
 @CorrectHIC NVarChar(200)=NULL,                                                                                                    
 @IsRohsStatusCorrect Bit=NULL,                                                                               
 @CorrectStatusNo Int=NULL,                                                                                        
 @CountryOfManufacture Int=NULL,                                                                                                                            
 @CountingMethodNo Int=NULL,                                                                                           
 @IsSerialNosRecorded Bit=NULL,                                                                                                            
 @IsLotCodeReq Bit=NULL,                                                                                       
 @IsEnhancedInpection Bit=NULL,                                                                                                                                                    
 @GeneralInspectionNotes NVarChar(1500)=NULL,                                                                                                                                    
 @IsBakingYes Bit=NULL,                                                                                                                                                                
 @IsBakingNo Bit=NULL,                                                                                                           
 @IsBakingNA Bit=NULL,                                                                                                                           
 @IsInspectionConducted Bit=NULL,                                                                                                                                                   
 @SupplierPart NVarChar(200)=NULL,                                                                                                                                                                        
 @ProductNo Int=NULL,                                                                                                                                                                            
 @Price Float=NULL,                                                                                                                                                                          
 @ClientPrice Float=NULL,                                                                                                                                                                          
 @Unavailable Bit=NULL,                                                                                                                                                                     
 @ChangedFields NVarChar(200)=NULL,                                                                                                                                                                              
 @CurrencyNo Int=NULL,                                                                                                                                                                              
 @ReqSerailNo  Bit=NULL,      
 @PartMarkings NVarChar(200)=NULL,                                                        
 @LineNotes NVarChar(200)=NULL,                                      
 @PrintHazWar Bit=NULL,                                                                                                    
 @PreviousDLUP NVarChar(23)=NULL,                                                                                                                            
 @ShipInCost float=NULL,                                                                                                                        
 @Quantity int   =NULL,                                                                               
 @IsDateCodeRequired Bit=NULL,                                                                                                                              
 @PackageBreakdownInfo NVarChar(200)=NULL,                                                                                                                                                             
 @HICStatus int =null,                                                                                                                
 @RowsAffected int = NULL OUTPUT ,                                                                     
 @ErrorMessage varchar(200) = null OUTPUT,                                                                                                                             
 @PackBreakDownJSON NVARCHAR(max),                                                              
 @IsBySendQueryBtn BIT=NULL ,                                                                                                                                        
 @ActeoneTestStatus INT=NULL,                                                                                      
 @IsopropryleStatus INT=NULL,                                                                                                                               
 @ActeoneTest varchar(500)=NULL,                                                                                                           
 @Isopropryle varchar(500)=NULL,                                                                            
 @QueryBakingLevel varchar(500)=NULL,                                                                                                                          
 @EnhInpectionReqId INT =NULL,                                                                                     
 @PrintDateCode VARCHAR(5)=NULL,                                                      
 @IsPackageBreakdownChnaged BIT=0                                                
 --[003] start                                                
 , @HasBarcodeScan int = null                                                
 , @BarcodeScanRemarks nvarchar(2000) = null                                            
 , @PartNoQuery nvarchar(300) = null                                           
 , @ManufacturerQuery nvarchar(300) = null                                           
 , @PackagingTypeQuery nvarchar(300) = null                                           
 , @MslQuery nvarchar(300) = null                                           
 , @RohsQuery nvarchar(300) = null                                      
 , @ReaiseGeneralQuery BIT=null                                             
 --[003] end                                                                                                   
 )                                                                              
 AS --         
 declare @goodsinid int=0;  
 select @goodsinid=GoodsInNo from tbGoodsInLine where GoodsInLineId = @GoodsInLineId  
 print @goodsinid  
 UPDATE  dbo.tbGoodsIn    
    SET       
    isupdated = 1   
    WHERE   GoodsInId = @goodsinid        
 BEGIN                                                                                                                                         
  --get received date for landed cost calculation                                                                                                   
  SET  @ErrorMessage = ''                                                                                                                                               
 ------[007] Fix For Shipping Cost-----                        
 DECLARE @ShipInCostChange FLOAT =null                              
 DECLARE @IsShipInCostChanged BIT=0;                              
 SELECT               
 @ShipInCostChange=ShipInCost                              
 FROM tbGoodsInLine                          
 WHERE GoodsInLineId=@GoodsInLineId                              
 IF(@ShipInCost!=@ShipInCostChange)                              
 BEGIN                      SET @IsShipInCostChanged=1;                              
 END                              
 --------------END---------------                                  
                                                                                            
  --declare variables                                                                                 
 DECLARE @OldLandedCost float                                                                                                                                                                  
    , @OldQuantity int                                        
    , @PurchaseOrderLineNo int                                   
    , @PurchaseOrderNo int                                  
    , @PurchaseOrderLineQuantity int                                                                                                                                 
    , @CRMALineNo int                                                                                                                                        
    , @CRMALineQuantity int                                                                                                                                                                                                    
    , @LandedCost float                                                                   
    , @ChangedQuantity int                                               
    , @StockId int                                                                                                                                                  
   , @QuantityInStock int                                                                
    , @QuantityOnOrder int                                                                                                                                                       
    , @StockLogId int                                                                                                                   
    , @GoodsInNo int                                                                                                                          
    , @StockIsForCRMA bit                                                                                             
    , @IPOCurrencyNo int                                                                                                                                                  
  -- , @ClientPrice FLOAT                                                                                                                                                                                      
  , @ClientLandedCost FLOAT                                                                                                                                
    , @GoodsInNumber  INT                                                                                          
    , @PartNo NVARCHAR(200)                                                                                
    , @InspectedBy Int=NULL                                                                          
    SET @InspectedBy=(Select InspectedBy from tbGoodsInLine WITH (NOLOCK)  where GoodsInLineId=@GoodsInLineId)                                                                  
   IF(@CorrectPartNo ='' or @CorrectPartNo is null)                                                                                                                
   SET @PartNo=(Select Part from tbGoodsInLine WITH (NOLOCK)  where GoodsInLineId=@GoodsInLineId)                                                         
   ELSE               
    SET @PartNo= @CorrectPartNo              
  declare @TotalShipCost float                                                                                                                               
  -- declare @TotalChildShipCost float            
   declare @TotalQuantity int                                                                                                                           
    declare @TotalChildQty int                                                                                                                
  -- declare @SplittedShippingCost float                                 
  declare @hasBarCodeScanTemp int;                              
  declare @barcodeScanRemarksTemp nvarchar(2000);                              
  IF(@IsShipInCostChanged=1)  --[007]                              
  BEGIN                                                                                                                                                                               
  select @TotalChildQty = sum(quantity) from tbGoodsInLine WITH (NOLOCK)  where  ParentGILineNo = @GoodsInLineId                                                                                                
  set @TotalShipCost = round(isnull(@ShipInCost,0),5)                                                                                      
  set @TotalQuantity = isnull(@Quantity,0) +isnull( @TotalChildQty,0)                                                                                                                               
  set @ShipInCost = dbo.[ufn_getPercentageShipCost](@TotalQuantity,ISNULL(@Quantity,0),@TotalShipCost)                                                                                                                                  
  --dbo.ufn_calculateShipCostForSplitQty(@TotalQuantity,@Quantity,@TotalShipCost)                                                                                                                                                 
  END                                
 ------------[005] Block For RP-1952 (Stock Not Update)------                                  
 DECLARE @ProductNoComp  INT=0                                
 DECLARE @ClientPriceComp FLOAT=0       
 DECLARE @PriceComp FLOAT=0                                
 DECLARE @ShipInCostComp FLOAT=0                                
 DECLARE @LineNotesComp  NVARCHAR(MAX)=''                                
 SELECT @ProductNoComp=ProductNo,                                
 @ClientPriceComp=ClientPrice,                                
 @ShipInCostComp=ShipInCost,                                
 @LineNotesComp=Notes,      
 @PriceComp=Price                                
 FROM tbGoodsInLine                                 
 WHERE GoodsInLineId=@GoodsInLineId                                
 IF((@ProductNoComp !=@ProductNo) OR (@ClientPriceComp !=@ClientPrice)                                 
 OR(@ShipInCostComp !=@ShipInCost) OR(@LineNotesComp !=@LineNotes) OR (@PriceComp!=@Price))                                
 BEGIN                                
 SET @UpdateStock=1;                                
 END                                
 ------------------------END---------------------------                                  
                          
                                                                                              
   DECLARE @DateReceived datetime,@CurrentDLUP VARCHAR(23)                                                                                                                                     
   SELECT  @DateReceived = gi.DateReceived  ,@CurrentDLUP =CONVERT(VARCHAR(23),gil.DLUP,121),@GoodsInNo=gi.GoodsInId,@GoodsInNumber=gi.GoodsInNumber                                          
                                          
  FROM    tbGoodsIn gi WITH (NOLOCK)                                                                        
  JOIN    tbGoodsInLine gil ON gi.GoodsInId = gil.GoodsInNo                                                                        
  WHERE   gil.GoodsInLineId = @GoodsInLineId                                                                                                              
                     
   --IF( @PreviousDLUP = @CurrentDLUP)                                                                                          
   IF( 1=1)                                                          
 BEGIN       
   
  --get old values                                                     
  SELECT  @OldLandedCost = ISNULL(gil.LandedCost,0)                                                                            
     , @OldQuantity = ISNULL(gil.Quantity,0)                                                                                         
    , @PurchaseOrderLineNo = gil.PurchaseOrderLineNo                                                                      
     , @PurchaseOrderLineQuantity = ISNULL(pol.Quantity,0)                                       
    , @CRMALineNo = gil.CustomerRMALineNo                                                                                                                                                                            
     , @CRMALineQuantity = ISNULL(cln.Quantity,0)                                                                                                                                     
  FROM    tbGoodsInLine gil  WITH (NOLOCK)                                                                                        
  LEFT JOIN tbPurchaseOrderLine pol ON gil.PurchaseOrderLineNo = pol.PurchaseOrderLineId                                                                                                                
  LEFT JOIN tbCustomerRMALine cln ON gil.CustomerRMALineNo = cln.CustomerRMALineId                                        
    WHERE   GoodsInLineId = @GoodsInLineId                                                                               
                                                                                                                                                                                                        
  --are we on affecting Stock relating to a CRMA?                                                                                                     
  SET @StockIsForCRMA = 0                                                                                                                                                                                                    
  IF (isnull(@CRMALineNo, 0) > 0)                                                                                                                                                  
   SET @StockIsForCRMA = 1                                                                                                                                           
                                                                                                                                                                                                        
  --decide whether to Apply Duty to landed cost calculation                                                                                                                                              
  DECLARE @ApplyDuty bit                                                                                                                                                                  
  SET @ApplyDuty = (SELECT    isnull(cn.Duty, 0)                                                                               
        FROM  dbo.tbPurchaseOrderLine pol WITH (NOLOCK)                                                                                                                                                   
     JOIN      dbo.tbPurchaseOrder po ON po.PurchaseOrderId = pol.PurchaseOrderNo                                                               
                                                                             
                         
        LEFT JOIN dbo.tbCountry cn ON cn.CountryId = po.ImportCountryNo                                  
     WHERE     pol.PurchaseOrderLineId = @PurchaseOrderLineNo                     
     )                                                                                    
                                                                                   
    --Calculate landedcost for CRMA                                        
    IF (ISNULL(@StockIsForCRMA,0) = 1)                                                   
    BEGIN                                                                        
    Declare @ClientBaseCurrencyNo Int                                                                                                  
    --get Client Base Currency                                                                                                                                                                                                          
    SELECT  @ClientBaseCurrencyNo = cl.CurrencyNo                                                                  
  FROM    tbCustomerRMALine cln WITH (NOLOCK)                                                                   
   JOIN   tbInvoiceLine il ON cln.InvoiceLineNo = il.InvoiceLineId                                                                                                           
    JOIN   tbInvoice iv ON iv.InvoiceId = il.InvoiceNo                                                                             
    JOIN    tbClient cl ON iv.ClientNo = cl.ClientId                                                                                                                           
      WHERE   cln.CustomerRMALineId = @CRMALineNo                                                                                                              
                                                                                                 
  --get Landed Cost, old and new         
          
  IF ((SELECT COUNT(1) FROM dbo.tbstock ST  INNER JOIN dbo.tbSourcingresult Sr on ST.ParentStockNo=SR.SourcingTableItemNo where  SR.SourcingTable='HUBSTK' and ST.GoodsInLineNo=@GoodsInLineId)>0)         
BEGIN         
SELECT @LandedCost = ipol.Price         
FROM tbPurchaseOrderLine pol WITH (NOLOCK)         
JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo         
WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineNo         
END        
ELSE        
BEGIN                                                                       
  SET @LandedCost = isnull(dbo.ufn_calculateLandedCost(ISNULL(@Quantity,0), @DateReceived, @ClientBaseCurrencyNo, ISNULL(@Price,0), ISNULL(@ShipInCost,0), 0, @ProductNo), 0)                                                                                 
  
    
      
        
END          
END                                                                                                                                      
   ELSE                                                                                                                                                                  
   BEGIN                                                                                                                                                                                   
  --get Landed Cost, old and new           
  IF ((SELECT COUNT(1) FROM dbo.tbstock ST  INNER JOIN dbo.tbSourcingresult Sr on ST.ParentStockNo=SR.SourcingTableItemNo where  SR.SourcingTable='HUBSTK' and ST.GoodsInLineNo=@GoodsInLineId)>0)         
BEGIN         
SELECT @LandedCost = ipol.Price         
FROM tbPurchaseOrderLine pol WITH (NOLOCK)         
JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo         
WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineNo         
END        
ELSE        
BEGIN                                     
  SET @LandedCost = isnull(dbo.ufn_calculateLandedCost(@Quantity, @DateReceived, @CurrencyNo, @Price, @ShipInCost, @ApplyDuty, @ProductNo), 0)                                                                       
        
END          
-- BY Espire 11 May 2016                                                                                                                                                                                            
    IF EXISTS(SELECT ipol.InternalPurchaseOrderLineId FROM tbPurchaseOrderLine pol WITH (NOLOCK)                                      
      JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo                                                                                                                    
     WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineNo)                                                                                                             
    BEGIN                                   
                                                                      
                                                                                                    
     DECLARE @IPOCountryNo int                                                                          
     SELECT @IPOCurrencyNo = ipo.CurrencyNo, @IPOCountryNo=ipo.ImportCountryNo                                                                                                                        
     FROM tbInternalPurchaseOrder ipo WITH (NOLOCK)                                                                
   JOIN tbInternalPurchaseOrderLine ipol ON ipo.InternalPurchaseOrderId = ipol.InternalPurchaseOrderNo                                                                   
                                                       
    WHERE ipol.PurchaseOrderLineNo = @PurchaseOrderLineNo                                                                            
                                                                                                                                                                                      
   DECLARE @ApplyDutyInClient BIT                                                                
                                                                                                                  
       SET @ApplyDutyInClient = (SELECT    ISNULL(cn.Duty, 0)                                       
          FROM    dbo.tbPurchaseOrderLine pol WITH (NOLOCK)                                                                                                                                                                           
    JOIN      dbo.tbPurchaseOrder po ON po.PurchaseOrderId = pol.PurchaseOrderNo                                                                                                                     
      LEFT JOIN dbo.tbCountry cn ON cn.CountryId = @IPOCountryNo                                                                                                                                                                            
    WHERE     pol.PurchaseOrderLineId = @PurchaseOrderLineNo)                                                                                                                                                                                         
             
IF ((SELECT COUNT(1) FROM dbo.tbstock ST  INNER JOIN dbo.tbSourcingresult Sr on ST.StockId=SR.SourcingTableItemNo where  SR.SourcingTable='HUBSTK' and ST.GoodsInLineNo=@GoodsInLineId)>0)         
BEGIN         
SELECT @ClientLandedCost = ipol.Price         
FROM tbPurchaseOrderLine pol WITH (NOLOCK)         
JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo         
WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineNo         
END        
ELSE        
BEGIN                                                                                                                                             
     SET @ClientLandedCost = dbo.ufn_calculateLandedCost(@Quantity, @DateReceived, @IPOCurrencyNo, @ClientPrice, @ShipInCost, @ApplyDutyInClient, @ProductNo)                                                                                                  
 
    
       
        
END         
IF ((SELECT COUNT(1) FROM dbo.tbstock ST  INNER JOIN dbo.tbSourcingresult Sr on ST.ParentStockNo=SR.SourcingTableItemNo where  SR.SourcingTable='HUBSTK' and ST.GoodsInLineNo=@GoodsInLineId)>0)         
BEGIN         
SELECT @LandedCost = ipol.Price         
FROM tbPurchaseOrderLine pol WITH (NOLOCK)         
JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo         
WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineNo         
END        
ELSE        
BEGIN                                                                                        
    SET @LandedCost = @Price / dbo.ufn_get_exchange_rate(@CurrencyNo, @DateReceived)           
END                                                                                                                                                                                 
END                                                                                                               
ELSE                                                                                                                                 
BEGIN                                                                                                        
    SET @ClientPrice = NULL                                                                                                                
    SET @ClientLandedCost = NULL                                          
END                                                           
END                                                                                                                 
                       
    --[006] start                              
  --print 'BarcodeScanRemraks = '+@BarcodeScanRemarks;                              
                               
  select @hasBarCodeScanTemp= hasbarcodeScan,@barcodeScanRemarksTemp = BarcodeScanRemarks                                
  from tbGoodsinline where GoodsInLineId = @GoodsInLineId;                              
    -- print 'barcodeTempRemarks';                              
    --print @barcodeScanRemarksTemp;                              
  --if @HasBarcodeScan = 0 and (@BarcodeScanRemarks  = 'Loading...' or @BarcodeScanRemarks = '' or @BarcodeScanRemarks is null)                              
  --begin                              
                                  
  -- set @BarcodeScanRemarks = @barcodeScanRemarksTemp;                              
  -- set @HasBarcodeScan = @hasBarCodeScanTemp                              
  -- --print 'change value';                              
  -- --print @HasBarcodeScan;                              
  -- --print @BarcodeScanRemarks;                              
  --end                 
                  
  if @BarcodeScanRemarks  = 'Loading...'                              
  begin                              
                                  
   set @BarcodeScanRemarks = @barcodeScanRemarksTemp;                              
   set @HasBarcodeScan = @hasBarCodeScanTemp                             
  end                  
                                  
 --[006] end            
DECLARE @IPOPrice Float=0                           
  SELECT  @IPOPrice=ipol.Price         
     FROM tbPurchaseOrderLine pol WITH (NOLOCK)         
     JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo         
     WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineNo                              
  --update GoodsInLine                                                                                                                         
  UPDATE  dbo.tbGoodsInLine                                                                                                                                                               
  SET      FullPart = dbo.ufn_get_fullpart(@PartNo)                                                                                                                                                
     ,Part = @PartNo                                                                                                                                                                                
     ,ManufacturerNo = IsNull(@CorrectManufacturerNo, ManufacturerNo)                               
      --DateCode = @DateCode                                                                                                                                                                                               
     ,PackageNo = IsNull(@CorrectPackageNo, PackageNo)                                                            
      ,Quantity = @Quantity                                                                                                                                                                                               
      , Price = @Price                                                                                                                                           
     , ShipInCost = @ShipInCost                                                                                                         
     , QualityControlNotes = @QualityControlNotes                                                                                              
     , Location = @Location                                                                                                                                                                                  
     , LotNo = @LotNo                                                                                                                       
     , ProductNo = @ProductNo                                                                          
     , SupplierPart = @SupplierPart                                                                                                                                                  
     , ROHS = IsNull(@CorrectStatusNo, ROHS)                                                                                                                                                                       
     , CountryOfManufacture = @CountryOfManufacture                     
     , LandedCost =CASE WHEN((SELECT COUNT(1) FROM dbo.tbstock ST  INNER JOIN dbo.tbSourcingresult Sr on ST.StockId=SR.SourcingTableItemNo where  SR.SourcingTable='HUBSTK' and ST.GoodsInLineNo=@GoodsInLineId)>0) THEN        
  @IPOPrice ELSE        
   /*case when isnull(ParentGILineNo,0) > 0 then LandedCost else*/ @LandedCost  /*end*/ END                                                                                                                                           
          
          
            
              
                 
                 
                       
     --,  Unavailable =case when Unavailable=1 THEN 1 else @Unavailable  end                                      
     , Notes = @LineNotes                                                                                                                   
    , CountingMethodNo = @CountingMethodNo                             
     --, SerialNosRecorded = @IsSerialNosRecorded                        
     , PartMarkings = @PartMarkings                           
     , UpdatedBy = @UpdatedBy                                      
     , DLUP = CURRENT_TIMESTAMP                                                       
     , FullSupplierPart = dbo.ufn_get_fullpart(@SupplierPart)                                                                                                                         
 --, ClientLandedCost = case when isnull(ParentGILineNo,0) > 0 then ClientLandedCost else @ClientLandedCost  end                                                                                         
  , ClientLandedCost = @ClientLandedCost         
  , ClientPrice =  @ClientPrice                                                 
  ,IsFullQtyRecieved       =   @IsFullQuantityReceived                                                                                                                                 
 ,IsPartNoCorrect   =   @IsPartNumberCorrect                                                            
 ,CorrectPartNo    =   @CorrectPartNo                                                                                      
 ,IsManufacturerCorrect  =   @IsManufacturerCorrect                                        
 ,CorrectManufacturer  =   @CorrectManufacturerNo                                                                                                                                                                       
 ,IsDateCodeCorrect   =   @IsDateCodeCorrect                                                 
 ,CorrectDateCode   =   @CorrectDateCode                                                                                                                                                       
 --,IsDateCodeRequired   =   @IsDateCodeRequired                                                                                    
 ,IsPackageTypeCorrect  =   @IsPackageCorrect                                                                                                                                                        
 ,CorrectPackageType   =   @CorrectPackageNo                                                                                                                              
 ,IsMSLLevelCorrect   =   @IsMSLCorrect                                                                                                                                               
 ,CorrectMSLLevel   =   @CorrectMslNo                                                                                           
 ,HICStatus     =   @HICStatus                                                                             
 ,IsHICStatusCorrect   =   @IsHICCorrect                                               
 ,CorrectHICStatus   =   @CorrectHIC                                                                       
 ,PKGBreakdownMismatch  =   @PackageBreakdownInfo                                    
 ,IsROHSStatusCorrect  =   @IsRohsStatusCorrect                                                               
 ,CorrectROHSStatus   =   @CorrectStatusNo                                                                                      
 ,IsLotCodesReq    =   @IsLotCodeReq                                                                                                                                                                              
 ,BakingLevelAdded   =   case when @IsBakingYes=1 then 1 when @IsBakingNo=1 then 2 when @IsBakingNA=1 then 3 else null end                                                                                                                                    
  
    
      
        
          
          
          
          
            
              
                 
                 
 ,EnhancedInspectionReq  =   @IsEnhancedInpection                                                                
 ,GeneralInspectionNotes  =   @GeneralInspectionNotes                                                                       
 ,IsInspectionConducted  =   @IsInspectionConducted                                                                                                                       
 , ReqSerialNo = @ReqSerailNo                                                                               
  , MSLLevel = IsNull(@CorrectMslNo, MSLLevel)                                                                                                                                                                                           
  --, PrintHazardous = @PrintHazWar                                                              
  ,ActeoneTestStatus=@ActeoneTestStatus                                 
  ,IsopropryleStatus=@IsopropryleStatus                                                                                                                                       
   ,ActeoneTest=@ActeoneTest                                                                                   
 ,Isopropryle=@Isopropryle                                                                                                     
    ,QueryBakeLevel=@QueryBakingLevel                                                                                                    
    ,EnhInpectionReqId=@EnhInpectionReqId                             
    ,PrintableDC=@PrintDateCode                                                 
   --[003] start                                                
    , HasBarCodeScan = @HasBarcodeScan                                                
    , BarCodeScanRemarks = @BarcodeScanRemarks                                            
    , PartNoQuery=@PartNoQuery                                          
    , ManufacturerQuery=@ManufacturerQuery                                          
    , PackagingTypeQuery=@PackagingTypeQuery                                          
    , MslQuery=@MslQuery                                          
    , RohsQuery=@RohsQuery                                     
    , ISReaiseGeneralQuery=@ReaiseGeneralQuery                                             
    --[003] end                                                  
  WHERE   GoodsInLineId = @GoodsInLineId                                                                                                                                                                                                    
                                                                                                                     
  SELECT  @RowsAffected = @@rowcount                                                                                                                                       
                                                          
  --update InvoiceLineAllocation               
  IF @UpdateShipments = 1                                                                                   
  BEGIN                                                                                                                                      
    UPDATE  tbInvoiceLineAllocation            
    SET  LandedCost = isnull(ClientLandedCost, @LandedCost )                                                                                                 
      --ClientLandedCost = @ClientLandedCost                                                                                                                                          
    FROM    tbInvoiceLineAllocation ila  WITH (NOLOCK)                                                                                       
    JOIN  tbGoodsInLine gil ON gil.GoodsInLineId = ila.GoodsInLineNo                                                                                                                                                                      
    WHERE   GoodsInLineId = @GoodsInLineId                                                                                   
   END                                      
   
  --get changed quantity                                                                                               
  SET @ChangedQuantity = ISNULL(@Quantity,0) - ISNULL(@OldQuantity,0)                           
                                                                                                                                                                                                    
  -- get Stock details for PO                                                                                                                                                              
  IF @StockIsForCRMA = 0                                                                           
   BEGIN                                                   
    --get the stockId to use on the stockLog and the new values (PO)                                                                                                                   
    SELECT  @StockId = st.StockId                                                                                                     
   , @QuantityInStock = CASE WHEN (@InspectedBy IS NULL) THEN @Quantity ELSE (ISNULL(st.QuantityInStock,0) + ISNULL(@ChangedQuantity,0))  END --As per GI tkt GR2-229 --st.QuantityInStock + @ChangedQuantity                                                 
  
    
      
       
   , @QuantityOnOrder = ISNULL(st.QuantityOnOrder,0)                                                                                                                                                                                               
       , @GoodsInNo = gil.GoodsInNo                                                             
    FROM    dbo.tbStock st  WITH (NOLOCK)                                                                                                          
    JOIN    tbGoodsInLine gil ON gil.PurchaseOrderLineNo = st.PurchaseOrderLineNo                                                                                                                                                                              
  
    
     
        
          
          
          
          
          
          
            
               
                       
                          
                            
                               
 AND gil.GoodsInLineId = st.GoodsInLineNo                                                                                                                 
    WHERE   GoodsInLineId = @GoodsInLineId                                                                                                                                                                                             
  END                                                                                                                            
  ELSE                       
   -- get Stock details for CRMA                                                                              
   BEGIN                                                                                                                                                                                          
    --get the stockId to use on the stockLog and the new values (CRMA)                                                                              
   SELECT  @StockId = st.StockId                                                                                                                                                   
       , @QuantityInStock = CASE WHEN (@InspectedBy IS NULL) THEN @Quantity ELSE (ISNULL(st.QuantityInStock,0) + ISNULL(@ChangedQuantity,0)) END --As per GI tkt GR2-229 --st.QuantityInStock + @ChangedQuantity                                               
  
    
      
        
         
          
          
          
          
          
           
       , @QuantityOnOrder = ISNULL(st.QuantityOnOrder,0)                                                                                              
     , @GoodsInNo = gil.GoodsInNo                                  
     FROM    dbo.tbStock st  WITH (NOLOCK)                                                                          
    JOIN    tbGoodsInLine gil ON gil.CustomerRMALineNo = st.CustomerRMALineNo                                             
    AND gil.GoodsInLineId = st.GoodsInLineNo                                            
    WHERE   GoodsInLineId = @GoodsInLineId                                                                                                                                                                                                    
   END                                                
                                       
  --update stock with GIL details                                                                                                          
  IF (@UpdateStock = 1)                                           
  BEGIN                                                                
    --[001] Code Start                                                                                                                                                                                                     
    Declare @vvInspectedDate datetime                                                                                                            
    select @vvInspectedDate=DateInspected from tbGoodsInLine WITH (NOLOCK)  where GoodsInLineId=@GoodsInLineId                                                               
         
           
          
          
            
              
               
                  
                     
    --[001]Code End                                                                                                                                                                                                  
                                                              
    UPDATE  dbo.tbStock                                                                                     
  SET         FullPart = dbo.ufn_get_fullpart(@PartNo) ,                                                                                                                                        
    Part = @PartNo,                                                                                                                                             
 ManufacturerNo = IsNull(@CorrectManufacturerNo, ManufacturerNo)  ,                                                                                                                     
        --DateCode = @DateCode                                                                                                                                                                                                    
       PackageNo = IsNull(@CorrectPackageNo, PackageNo) ,                                                                     
        QuantityInStock = @QuantityInStock,                                                                                                
        QualityControlNotes = @QualityControlNotes                                                                                                                                                                 
       --[001] Code Start                                                                                                 
    ,Location = Case When @vvInspectedDate IS NOT NULL then Location else @Location end                                                                                                                                                                        
  
   
      
               
   --[001]Cope End                                                                                    
      , LotNo = @LotNo                                                                                  
       , ProductNo = @ProductNo   
       , SupplierPart = @SupplierPart                                                                                                                                                                      
       , ROHS = @CorrectStatusNo                                                                           
       , LandedCost = @LandedCost                                                                                                 
  --,  Unavailable =case when Unavailable=1 THEN 1 else @Unavailable  end                                                                      
     , CountryOfManufacture = @CountryOfManufacture                                                                                                   
     , CountingMethodNo = @CountingMethodNo                                                                                       
    , PartMarkings = @PartMarkings                                                                                                                                                                                                    
       , UpdatedBy = @UpdatedBy                                                                          
       , DLUP = CURRENT_TIMESTAMP                                                     
   , ClientLandedCost = @ClientLandedCost      
   , ClientPrice = @ClientPrice              
    , MSLLevel = @CorrectMslNo                                                                                                                                                                                         
    WHERE   StockID = @StockId                                                                                                                                                         
                                                                                                                                                                          
    --Espire Start : 30 Dec : SWiss client stock update                                                                                                
     declare @ClientNo int                                                                                                                                                    
    select @ClientNo = ClientNo from dbo.tbStock WITH (NOLOCK)  WHERE   StockID = @StockId                                                                                                                   
    IF @ClientNo = 109 and @StockId > 0                                                                                                                                                                     
    BEGIN                                                                                                            
   UPDATE  dbo.tbStock                                                                                                                                                                        
    SET     FullPart = dbo.ufn_get_fullpart(@PartNo),                                                                                                                                                                                                    
    Part = @PartNo,                                                                        
    ManufacturerNo = IsNull(@CorrectManufacturerNo, ManufacturerNo)  ,                                                                                                                                      
    --DateCode = @DateCode                    
    --PackageNo = IsNull(@CorrectPackageNo, PackageNo),                                                                                          
     QuantityInStock = @QuantityInStock                                                                                 
     , QualityControlNotes = @QualityControlNotes         
   --[001] Code Start                         
   , Location = Case When @vvInspectedDate IS NOT NULL then Location else @Location end                                         
   --[001]Cope End                                                                                                                                                                      
   --, LotNo = @LotNo                                                                                                                                                                                                    
   --, ProductNo = @ProductNo                             
   , SupplierPart = @SupplierPart                                                                                                                                                        
   , ROHS = @CorrectStatusNo                                                                                                
   , LandedCost = @LandedCost * dbo.ufn_get_exchange_rate(196, isnull(StockDate,getdate()))                                                                                                   
   --,  Unavailable =case when Unavailable=1 THEN 1 else @Unavailable  end                          
   --, CountryOfManufacture = @CountryOfManufacture                               
   , CountingMethodNo = @CountingMethodNo                                                                                                                                                
   , PartMarkings = @PartMarkings                                                                                                                                                                                       
   , UpdatedBy =99999 -- @UpdatedBy                                                                                                                             
   , DLUP = CURRENT_TIMESTAMP                                                                                                    
   , ClientLandedCost = @ClientLandedCost* dbo.ufn_get_exchange_rate(196, isnull(StockDate,getdate()))                                                                                                                 
   , ClientPrice = @ClientPrice* dbo.ufn_get_exchange_rate(196, isnull(StockDate,getdate()))                                                                                                                                                                  
  
    
      
        
          
          
          
          
          
          
            
              
                
                 
                        
                          
   --, MSLLevel = @MSLLevel                  
   WHERE ISNULL(RefId109,0) = @StockId and ClientNo=117                                                                                                                                                                                  
    END                                                                                                                                                
    --Espire End : 30 Dec : SWiss client stock update                                                                                                
                                                           
    -- get PO details for Stock Log                                                                                                                                           SELECT  @PurchaseOrderNo = pol.PurchaseOrderNo                                   
  
    
      
        
         
          
          
          
          
          
            
               
    SELECT  @PurchaseOrderNo = pol.PurchaseOrderNo                            
    FROM    tbGoodsInLine  gil  WITH (NOLOCK)                                                                               
   JOIN    tbPurchaseOrderLine pol ON gil.PurchaseOrderLineNo = pol.PurchaseOrderLineId                                                                            
                                                                                         
    --insert Stock Log (only if we've changed fields that affect stock)                                                                              
 IF len(@ChangedFields) > 0                                                                                                                                                                                                     
      BEGIN                                                                                                  
   EXEC usp_insert_StockLog @StockLogTypeNo = 34 --ChangesOnGoodsIn                                                                                                                                                                          
    , @StockNo = @StockId --                                                                                                
       , @QuantityInStock = @QuantityInStock --                                                                                                                                                                             
       , @QuantityOnOrder = @QuantityOnOrder --                                                  
     , @ActionQuantity = @ChangedQuantity --                                                    
       , @Detail = @ChangedFields --                                                
       , @PurchaseOrderNo = @PurchaseOrderNo --                                                                                                        
       , @GoodsInLineNo = @GoodsInLineId --                                                                                                                                                                         
  , @GoodsInNo = @GoodsInNo --                                                                                                                                                                                                    
        , @UpdatedBy = @UpdatedBy --                                                                                                 
       , @StockLogId = @StockLogId OUTPUT                                                                                           
     END                                                                                                                                                                                                    
                                                                                                                
    -- has this changed the quantity downwards (which would make it a partial receipt)?                                                                                                                                              
    IF @ChangedQuantity < 0                                                                                                                                            
     AND ((@StockIsForCRMA = 1 AND @Quantity < @CRMALineQuantity)                                                                        
                        
    OR (@StockIsForCRMA = 0                             
        AND @Quantity < @PurchaseOrderLineQuantity))                                                                                                                                                           
     BEGIN                                                                                          
   --get the Absolute Changed Quantity (but limit the total to the PO or CRMA Line total                                                          
      DECLARE @AbsoluteChangedQuantity int                                                                         
      SET @AbsoluteChangedQuantity = abs(@ChangedQuantity)                                   
                                                           
      IF (@StockIsForCRMA = 1)                                                                                                                                                                    
 BEGIN                                                         
        DECLARE @QuantityFromOtherStockOnSameCRMALine int                                                                                                                                        
     SELECT  @QuantityFromOtherStockOnSameCRMALine = isnull(sum(Quantity), 0)                                                                                      
  FROM    tbGoodsInLine WITH (NOLOCK)                                                                                                        
      WHERE   CustomerRMALineNo = @CRMALineNo                                                                                                            
          AND NOT GoodsInLineId = @GoodsInLineId                                                                                                                                                          
                                                                                                                             
        SET @AbsoluteChangedQuantity = dbo.ufn_min(@AbsoluteChangedQuantity, @CRMALineQuantity - @QuantityFromOtherStockOnSameCRMALine - @Quantity)                                                                            
        SET @AbsoluteChangedQuantity = dbo.ufn_max(@AbsoluteChangedQuantity, 0)                                                                                                                                                          
      END                                                                                                                                     
      ELSE                                                                                                                                                                                                     
       BEGIN                                                                                             
        DECLARE @QuantityFromOtherStockOnSamePOLine int                                                                                                                                                                        
        SELECT  @QuantityFromOtherStockOnSamePOLine = isnull(sum(Quantity), 0)                                                                                                                                                                                
  
        FROM    tbGoodsInLine                            
        WHERE   PurchaseOrderLineNo = @PurchaseOrderLineNo                                                                                                                                                                                                
        AND NOT GoodsInLineId = @GoodsInLineId                                                                                                                                         
                                                                                                                                                           
     SET @AbsoluteChangedQuantity = dbo.ufn_min(@AbsoluteChangedQuantity, @PurchaseOrderLineQuantity - @QuantityFromOtherStockOnSamePOLine - @Quantity)                                                                                                        
 
    
      
        
          
          
          
          
          
           
           
               
                
                  
     SET @AbsoluteChangedQuantity = dbo.ufn_max(@AbsoluteChangedQuantity, 0)                                            
   END                                             
                                                                                                                 
   --Create new stock, update allocations etc                                                                                
     EXEC usp_update_Allocation_AfterPartialReceive --                                                                                                                      
       @GoodsInLineNo = @GoodsInLineId --                        
      , @GoodsInNo = @GoodsInNo --                                                                       
       , @QuantityInserted = @Quantity --                                  
       , @QuantityOnOrderChange = @AbsoluteChangedQuantity --                                                                                                                  
       , @UpdatedBy = @UpdatedBy --                                                                                                                                                       
  END                                                                                                                         
                                                                                                                                                            
    -- has this changed the quantity upwards (which would back out a partial receipt)?                                                                                                                                         
   IF (@ChangedQuantity > 0)                                                             
     BEGIN                                                                                                                                                                 
      EXEC usp_update_Allocation_AfterIncreaseGIQuantity --                                                  
      @GoodsInLineNo = @GoodsInLineId --                                                                                                
       , @OldQuantity = @OldQuantity --                                        
       , @NewQuantity = @Quantity --                                                                                                                                                                                                      
       , @UpdatedBy = @UpdatedBy --                                                                   
                                                                                                                
     END                                                                
   END                                                                   
                                                                                                                                                                                                
  --update closed status on PO and PO Lines                                
  EXEC usp_PurchaseOrderLine_Update_Closed_Status @PurchaseOrderLineNo                                                                                                                                                                                        
  
    
      
       
                                                     
  --Update closed status on CRMA and CRMA Lines                                                                                                                                             
  IF @StockIsForCRMA = 1                                                                                                                
  EXEC usp_CustomerRMALine_Update_Closed_Status @CRMALineNo                                                                          
                                                               
      
  IF EXISTS (SELECT 1 FROM tbGoodsInLine WITH (NOLOCK) WHERE ParentGILineNo = @GoodsInLineId)                           
                        
  BEGIN                            
  IF(@IsShipInCostChanged=1)                          
  BEGIN                                                                                                               
  UPDATE tbGoodsInLine SET ShipInCost=dbo.[ufn_getPercentageShipCost](@TotalQuantity,Quantity,@TotalShipCost)                                                                
     , LandedCost=@LandedCost,ClientLandedCost=@ClientLandedCost, Price = @Price, ClientPrice=@ClientPrice  WHERE ParentGILineNo = @GoodsInLineId            
                         
                         
  END                                                        
                                         
   --update landed cost of related stock                                                                                                                                                                         
                                                                                                                                                                                 
  --   UPDATE tbStock SET  LandedCost=@LandedCost,ClientLandedCost=@ClientLandedCost  WHERE GoodsInLineNo IN (                                                                                                                                                 
 
    
  --SELECT GoodsInLineId  FROM  tbGoodsInLine WITH (NOLOCK)  WHERE   ParentGILineNo = @GoodsInLineId  )                                                                                                                                                        
  
    
     
       
          
          
         
           
            
             
                
                   
                    
                        
                          
                            
                              
                                                                                                                                                                           
  IF @OldLandedCost <> @LandedCost                                                                                                                  
 BEGIN                                                                                                                                             
     INSERT INTO tbStockLog                                                                                                                                                                                
   (                                                                                                                                 
   StockLogTypeNo,                                                                                       
   StockNo,                                                                        
   QuantityInStock,                                                                                      
    QuantityOnOrder,                                                                                  
   GoodsInLineNo,                                                                                                                                                                                
   UpdatedBy,                                                            
   DLUP,                                                                                               
    Detail,                                                                                                                  
   ChangeNotes,                                                                                                         
   GoodsInNo                                                                                                                            
    )                                             
   SELECT                                       
   34,                     
   StockId,                                                                                                                                               
   QuantityInStock,                                                       
    QuantityOnOrder,                                                                                         
   GoodsInLineNo,                                                                                                             
   @UpdatedBy,                                                                                                                        
   GETDATE(),                                                                        
   'LandedCost��'+cast(isnull(@LandedCost,'') as nvarchar(20)),                                                   
   'Landed Cost update from GI',                                                                                                                                                  
   @GoodsInNo                                                                          
 FROM tbStock WITH (NOLOCK)    WHERE GoodsInLineNo IN (                                                                                                                               
      SELECT GoodsInLineId  FROM      tbGoodsInLine WITH (NOLOCK)  WHERE   ParentGILineNo = @GoodsInLineId  )                                                                                                                                             
  END                                          
                                                                                                                      
  END                                                                                                                         
                
   END                                                                                                                                                                                  
   ELSE                                                                                                                                                                                  
   BEGIN                                                                                                     
   SET @ErrorMessage = 'Please refresh your browser to proceed'                                                                                                                        
   END                                                                                                                                                       
                               
                               
                        
                              
 DELETE FROM tbPackagingBreakdownInfo where GoodsInLineNo=@GoodsInLineId                                                                                                                                              
INSERT INTO tbPackagingBreakdownInfo (GoodsInLineNo,FactorySealed,NumberofPacks,PackSize,DateCode,BatchCode,PackagingTypeId,MFRLabelId,Total,DLUP,UpdatedBy)                                                                                                  
   
   
       
        
         
          
          
          
           
              
                
                  
 SELECT  @GoodsInLineId,FactorySealed, NumberofPacks,CAST(PackSize as FLOAT),DateCode,BatchCode,PackagingTypeId,MFRLabelId,CAST(Total as FLOAT),CURRENT_TIMESTAMP,@UpdatedBy                                                                                   
 
                                                                    
  FROM OPENJSON(@PackBreakDownJSON)                                                                                                                                                              
  WITH (FactorySealed bit,                                                                                        
  NumberofPacks int,                                                                                                                                                              
  PackSize VARCHAR(100),                          
  DateCode VARCHAR(100),                                                                                         
  BatchCode VARCHAR(100),                                                                                                                                                             
  Total VARCHAR(100),                                                                  
  PackagingTypeId INT,                                                                                                    
    MFRLabelId INT                                                                                                                                
  )                                                
 IF(@IsBySendQueryBtn=1)                                                                
  BEGIN                                                                   
   DECLARE @GIQueryHtml NVARCHAR(MAX);                                                                                                                                    
   DECLARE @GIQueryHtmlExtra NVARCHAR(MAX);                                                                             
   DECLARE @htmlResult NVARCHAR(MAX);                                                                                                                                               
 DECLARE @GIQueryId int;       
   DECLARE @IsVarianceFound  BIT =NULL;                                                                                     
   DECLARE @IsVarianceExtraFound  BIT =NULL;                                              
   DECLARE @htmlForApprovalScreen NVARCHAR(MAX)=NULL                                            
   DECLARE @htmlForApprovalScreenExtra NVARCHAR(MAX)=NULL                                      
   DECLARE @Columns NVARCHAR(MAX)=''                                                                                                                                        
   EXEC usp_select_GoodsInVariance_Query  @GoodsInLineId, @GIQueryHtml OUTPUT,@IsVarianceFound  OUTPUT,@GIQueryHtmlExtra OUTPUT ,@htmlResult OUTPUT ,@IsVarianceExtraFound OUTPUT,@htmlForApprovalScreen OUTPUT,                                   
   @htmlForApprovalScreenExtra OUTPUT,@Columns OUTPUT                                                
                                       
                                       
                                          
                                                                            
                                                                                                                 
 -- [001] Start                                                              
 DECLARE @PackageQuery NVARCHAR(max) =''                                              
 DECLARE @OutPackQuery NVARCHAR(MAX) =''                                                                                                                   
 IF EXISTS(SELECT 1 from tbPackagingBreakdownInfo where GoodsInLineNo=@GoodsInLineId)                                                                
 BEGIN                                                                         
 SET @PackageQuery=N'                                                                                                                    
 SELECT CASE WHEN pkgInfo.FactorySealed=1 THEN ''Yes'' ELSE ''No'' END as ''Factory Sealed'',                                                                              
 pkgInfo.NumberofPacks as ''Number Of Packs'',                                                                                
 CAST(pkgInfo.PackSize as numeric(36,2)) as ''Pack Size'',                                      
 CASE WHEN vGIL.PODateCode=pkgInfo.DateCode THEN pkgInfo.DateCode ELSE ''<span>''+ pkgInfo.DateCode+''</span>'' END as ''DateCode'',                                                                                        
 ISNULL(pkgInfo.BatchCode,'''') as ''Batch Code'',                                                                                                                    
 ISNULL(pkg.PackageDescription,'''') as ''Packaging Type'',            
 ISNULL(mfr.MFRLabelName,'''') as ''MFR Label'',                                             
 cast(pkgInfo.Total as numeric(36,2)) as Total                                                                                                                     
 FROM tbPackagingBreakdownInfo pkgInfo            
 LEFT join tbPackage pkg on pkg.PackageId=pkgInfo.PackagingTypeId                               
 LEFT JOIN vwGoodsInLine vGIL on vGIL.GoodsInLineId=pkgInfo.GoodsInLineNo             
 LEFT JOIN tbMFRLabel mfr ON pkgInfo.MFRLabelId=mfr.MFRLabelId                                                              
 WHERE pkgInfo.GoodsInLineNo='+CAST(@GoodsInLineId  as VARCHAR(100))                                                                         
                                                                                                              
 EXECUTE spQueryToHtmlTable @PackageQuery,null, @html=@OutPackQuery output                                                                             
 SET @OutPackQuery=replace(replace(@OutPackQuery, '&lt;', '<'), '&gt;', '>')                                                                                         
 SET @OutPackQuery='<div id="dvPackBreakInfo"></br> <b style="font-size: 18px;">Packaging Breakdown Info</b>: </br> </br>'+@OutPackQuery+'</div>'                                                                                                             
  
    
     
 END                                                                                                                  
 -- [001] End                                                                                
 DECLARE @IsQueryExists BIT=0                                      
   SET @IsQueryExists=(SELECT CASE WHEN COUNT(Approval_Id)>0 THEN CAST(1 as BIT) ELSE CAST(0 as BIT) END from tbGiLineQueryApprover where GI_LineNo=@GoodsInLineId)                                                                                            
 
   IF(@IsQueryExists=0)                                                                  
   BEGIN                                                                                                                                 
   if((@GIQueryHtml IS NOT null or @GIQueryHtml !=@htmlResult) and @IsVarianceFound=1)                                                                                 
   BEGIN                                                                                                  
    IF NOT EXISTS(SELECT GI_QueryId from tbGiQueryMessage WITH (NOLOCK)  WHERE QueryMessage=@GIQueryHtml and GILineNo= @GoodsInLineId and InDraftMode=0)                                                                                                       
  
    
     
        
          
          
          
           
            
              
               
                   
                   
                       
                         
                         
                            
                              
                                
                                  
                                    
                   
                                       
    BEGIN                                                                                
  IF ((SELECT COUNT(1) from tbGiQueryMessage WITH (NOLOCK)  where GILineNo= @GoodsInLineId and InDraftMode=1) >0)                                                                                                                                      
  BEGIN                                                                                                                 
    UPDATE tbGiQueryMessage set QUERYMessage=@GIQueryHtml,HtmlQueryVarianceExtra=@GIQueryHtmlExtra,HtmlPackBreakDownInfo=@OutPackQuery                                             
    ,QueryMessageApprover=@htmlForApprovalScreen,HtmlQueryVarianceExtraApprover=@htmlForApprovalScreenExtra                                   
    WHERE GILineNo= @GoodsInLineId and InDraftMode=1                                                                                                                    
                                              
                                               
                                                          
  END                                                                      
  ELSE                                                                                                     
  BEGIN                                                                          
  INSERT INTO dbo.tbGiQueryMessage(GoodInNo,GILineNo,QueryMessage,UpdatedBy,DLUP,INActive,IsInitialMessage,InDraftMode,GI_QueryNumber,HtmlQueryVarianceExtra,HtmlPackBreakDownInfo,QueryMessageApprover,HtmlQueryVarianceExtraApprover)                       
  
     
      
        
          
          
          
          
           
               
                
                  
  VALUES(@GoodsInNo,@GoodsInLineId,@GIQueryHtml,@UpdatedBy,CURRENT_TIMESTAMP,0,1,1,null,@GIQueryHtmlExtra,@OutPackQuery,@htmlForApprovalScreen,@htmlForApprovalScreenExtra)                                          
  SET @GIQueryId= @@IDENTITY                                                                                                                                              
  UPDATE dbo.tbGiQueryMessage                                                                                                                                                
  SET GI_QueryNumber=(CAST(@GoodsInNumber AS nvarchar(100))+'-'+CAST(@GIQueryId AS nvarchar(100)))                                              
  WHERE GI_QueryId=@GIQueryId                                                                           
  END                                                                                                                                            
  END                                                                       
  END                                                                                     
  ELSE                                                                                                                                  
  BEGIN                                                                                                           
   IF (@IsVarianceExtraFound)=1                                                                                                                                  
   BEGIN                                                                                                                                  
   UPDATE tbGiQueryMessage set HtmlQueryVarianceExtra=@GIQueryHtmlExtra where GILineNo= @GoodsInLineId and InDraftMode=1                                                                                         
   END                                           
                                                                                                                                    
  END                                         
   END                                                                                                                                              
 END                                                                                      
 -----------FOR GR2-261-------                                                                                
 IF(@PrintDateCode IS NOT NULL)               
 BEGIN                                                                                
 UPDATE tbStock SET DateCode=@PrintDateCode where StockId=@StockId                                                                                
 END                                                                                
 -----------------------------                                                           
                                                        
 -----[002] code start-----------                                                        
 CREATE TABLE #SupplierInvoiceNumber                                                        
 (                                                        
 ID  INT IDENTITY(1,1),                                                        
 SupplierInvoiceNo INT                                                        
 )                                                        
 DECLARE @Total decimal(15,5)=0, @count INT=1,@MaxCount INT=0                                                        
 INSERT INTO #SupplierInvoiceNumber                                                        
 SELECT SupplierInvoiceNo FROM tbSupplierInvoiceLine WHERE GoodsInLineNo=@GoodsInLineId                                                        
                             
-- Update Unit price based on system generated invoice condition (IsPOHub)----
 UPDATE sil                                                        
 SET UnitPrice= CASE WHEN comp.IsPOHub = 1 THEN @ClientPrice ELSE @Price END,
	Landedcost=@LandedCost, 
	QtyReceived=@Quantity
 FROM tbSupplierInvoiceLine sil
 JOIN tbSupplierInvoice si ON si.SupplierInvoiceId = sil.SupplierInvoiceNo
 JOIN tbCompany comp ON comp.CompanyId = si.CompanyNo                                                 
 WHERE GoodsInLineNo = @GoodsInLineId

 SELECT @MaxCount=COUNT(1) FROM #SupplierInvoiceNumber                                                        
                                                        
 WHILE(@count<=@MaxCount)                                                        
 BEGIN                                                        
 SET @Total=0                              
 SELECT  @Total+=ISNULL(UnitPrice,0)*ISNULL(QtyReceived,0) FROM tbSupplierInvoiceLine                                                         
 WHERE SupplierInvoiceNo=(SELECT SupplierInvoiceNo FROM #SupplierInvoiceNumber WHERE ID=@count)                                                
                                                        
 UPDATE tbSupplierInvoice SET GoodsValue=@Total,                                                        
 InvoiceAmount=ISNULL(@Total,0)+ISNULL(Tax,0)+ISNULL(DeliveryCharge,0)+ISNULL(BankFee,0)                                                        
 WHERE SupplierInvoiceId=(SELECT SupplierInvoiceNo FROM #SupplierInvoiceNumber WHERE ID=@count)                                                        
 SET @count=@count+1;                                                        
 END                        

 --------Package Breakdown calculation----                                                    
                                    
 DECLARE @ColGI_Query INT=0                                     
 SELECT @ColGI_Query=GI_QueryId FROM tbGiQueryMessage WHERE GILineNo=@GoodsInLineId AND IsInitialMessage=1                                    
 DELETE FROM tbGI_QueryFixedColumn WHERE GI_QueryNo=@ColGI_Query                                    
 INSERT INTO tbGI_QueryFixedColumn(GI_QueryNo,ColumnName)                                    
 SELECT @ColGI_Query,val FROM SplitString(@Columns,',');                                                  
 -----------END--------------                                                      
 DROP TABLE #SupplierInvoiceNumber   
 -----[002] code end-------------            
 
 ---------Update tbGI_TempInspectionData---------------
 DECLARE @ClientId INT = 0;
 SELECT @ClientId=ClientNo FROM tbGoodsIn WHERE GoodsInId=(          
  SELECT GoodsInNo FROM tbGoodsInLine WHERE GoodsInLineId=@GoodsInLineId)
 DELETE FROM tbGI_TempInspectionData WHERE GoodsInLineId=@GoodsInLineId          
  EXEC usp_select_GoodsInLine_Copy @GoodsInLineId,@UpdatedBy,@ClientId   
 END     
GO


