﻿
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-215434]		Phuc Hoang			06-Nov-2024		Update			Lytica Price should apply fuzzy logic for inserting & displaying
===========================================================================================
*/

IF COL_LENGTH('dbo.tbCustomerRequirement', 'LyticaManufacturerRef') IS NULL 
BEGIN
	ALTER TABLE dbo.tbCustomerRequirement
	ADD LyticaManufacturerRef NVARCHAR(256) NULL;

END

GO

IF COL_LENGTH('dbo.tbCustomerRequirement', 'LyticaAveragePrice') IS NULL 
BEGIN
	ALTER TABLE dbo.tbCustomerRequirement
	ADD LyticaAveragePrice FLOAT NULL;

END

GO

IF COL_LENGTH('dbo.tbCustomerRequirement', 'LyticaTargetPrice') IS NULL 
BEGIN
	ALTER TABLE dbo.tbCustomerRequirement
	ADD LyticaTargetPrice FLOAT NULL;

END

GO

IF COL_LENGTH('dbo.tbCustomerRequirement', 'LyticaMarketLeading') IS NULL 
BEGIN
	ALTER TABLE dbo.tbCustomerRequirement
	ADD LyticaMarketLeading FLOAT NULL;

END