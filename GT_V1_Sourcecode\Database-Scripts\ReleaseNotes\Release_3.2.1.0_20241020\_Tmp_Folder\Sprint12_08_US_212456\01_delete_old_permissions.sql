﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213080]     An.TranTan		 10-Oct-2024		CREATE		Delete permissions no longer use
===========================================================================================  
*/
DECLARE @tbSecurityFunctions TABLE (SecurityFunctionId INT);
INSERT INTO @tbSecurityFunctions 
SELECT SecurityFunctionId
FROM tbSecurityFunction
WHERE FunctionName = 'Utility_ProspectiveOffer'
	or FunctionName = 'Utility_ProspectiveOfferDetail';

DELETE tbSecurityFunction WHERE SecurityFunctionId IN (SELECT SecurityFunctionId FROM @tbSecurityFunctions);
DELETE tbSecurityGroupSecurityFunctionPermission WHERE SecurityFunctionNo IN (SELECT SecurityFunctionId FROM @tbSecurityFunctions);
--Delete recent view records
DELETE tbRecentlyViewed WHERE PageUrl LIKE 'Utility_ProspectiveOffer%'