Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.prototype={get_pnlOpen:function(){return this._pnlOpen},set_pnlOpen:function(n){this._pnlOpen!==n&&(this._pnlOpen=n)},get_pnlOverdue:function(){return this._pnlOverdue},set_pnlOverdue:function(n){this._pnlOverdue!==n&&(this._pnlOverdue=n)},get_tblOpen:function(){return this._tblOpen},set_tblOpen:function(n){this._tblOpen!==n&&(this._tblOpen=n)},get_tblOverdue:function(){return this._tblOverdue},set_tblOverdue:function(n){this._tblOverdue!==n&&(this._tblOverdue=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},get_lnkMore:function(){return this._lnkMore},set_lnkMore:function(n){this._lnkMore!==n&&(this._lnkMore=n)},get_myLoginID:function(){return this.myLoginID},set_myLoginID:function(n){this.myLoginID!==n&&(this.myLoginID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblOpen&&this._tblOpen.dispose(),this._tblOverdue&&this._tblOverdue.dispose(),this._pnlOpen=null,this._pnlOverdue=null,this._tblOpen=null,this._tblOverdue=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlOpen,!1);$R_FN.showElement(this._pnlOverdue,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();this._lnkMore.href=this._intLoginID_Other>0?$RGT_gotoURL_CusReqBrowse(this._intLoginID_Other):$RGT_gotoURL_CusReqBrowse(this.myLoginID);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/MyOpenRequirements");n.set_DataObject("MyOpenRequirements");n.set_DataAction("GetData");n.addParameter("rowcount",this._intRowCount);n.addParameter("OtherLoginID",this._intLoginID_Other);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var r,u,t,i;for(this.showNoData(n._result.Count==0),$R_FN.showElement(this._pnlMore,!0),r=n._result,this._tblOpen.clearTable(),i=0;i<r.OpenRQ.length;i++)t=r.OpenRQ[i],u=[$RGT_nubButton_CustomerRequirement(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblOpen.addRow(u,null);for($R_FN.showElement(this._pnlOpen,r.OpenRQ.length>0),this._tblOverdue.clearTable(),i=0;i<r.OverdueRQ.length;i++)t=r.OverdueRQ[i],u=[$RGT_nubButton_CustomerRequirement(t.ID,t.No),$RGT_nubButton_Company(t.CMNo,t.CM),t.Due],this._tblOverdue.addRow(u,null);$R_FN.showElement(this._pnlOverdue,r.OverdueRQ.length>0);this.hideLoading()}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);