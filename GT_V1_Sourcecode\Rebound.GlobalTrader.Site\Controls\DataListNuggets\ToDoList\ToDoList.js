Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.initializeBase(this,[n]);this._frmConfirm=null;this._frmAdd=null;this._frmEdit=null;this._intQuoteID=null;this._intCompanyID=0;this._intToDoID=-1;this._intTaskCategoryID=1;this._strCompanyName="";this._companyCategoryId=1;this._quoteCategoryId=2;this._quoteMinReminderDate=null};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.prototype={get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_sortIndex:function(){return this._sortIndex},set_sortIndex:function(n){this._sortIndex!==n&&(this._sortIndex=n)},get_sortDir:function(){return this._sortDir},set_sortDir:function(n){this._sortDir!==n&&(this._sortDir=n)},get_pageIndex:function(){return this._pageIndex},set_pageIndex:function(n){this._pageIndex!==n&&(this._pageIndex=n)},get_pageSize:function(){return this._pageSize},set_pageSize:function(n){this._pageSize!==n&&(this._pageSize=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnMarkComplete:function(){return this._ibtnMarkComplete},set_ibtnMarkComplete:function(n){this._ibtnMarkComplete!==n&&(this._ibtnMarkComplete=n)},get_ibtnMarkIncomplete:function(){return this._ibtnMarkIncomplete},set_ibtnMarkIncomplete:function(n){this._ibtnMarkIncomplete!==n&&(this._ibtnMarkIncomplete=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_ctlSelect:function(){return this._ctlSelect},set_ctlSelect:function(n){this._ctlSelect!==n&&(this._ctlSelect=n)},get_intToDoID:function(){return this._intToDoID},set_intToDoID:function(n){this._intToDoID!==n&&(this._intToDoID=n)},initialize:function(){this._table.addMultipleSelectionChanged(Function.createDelegate(this,this.tableSelect));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/ToDoList";this._strDataObject="ToDoList";Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.callBaseMethod(this,"initialize");this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.exportCSV));this._ibtnAdd&&$R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm));this._frmAdd=$find(this._aryFormIDs[2]);this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm));this._frmAdd.addSaveComplete(Function.createDelegate(this,this.addComplete));this._ibtnEdit&&$R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm));this._frmEdit=$find(this._aryFormIDs[0]);this._frmEdit.addCancel(Function.createDelegate(this,this.hideEditForm));this._frmEdit.addSaveComplete(Function.createDelegate(this,this.editComplete));this._ibtnDelete&&$R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm));this._ibtnMarkComplete&&$R_IBTN.addClick(this._ibtnMarkComplete,Function.createDelegate(this,this.showMarkCompleteForm));this._ibtnMarkIncomplete&&$R_IBTN.addClick(this._ibtnMarkIncomplete,Function.createDelegate(this,this.showMarkIncompleteForm));this._frmConfirm=$find(this._aryFormIDs[1]);this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.hideConfirmForm));this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.confirmComplete));this._intCompanyID>0&&($("#ctl00_cphMain_ctlToDoList_ctlDB_ctl17_ctlFilter_ctlCustomerName_txt").val(this._strCompanyName),$("#ctl00_cphMain_ctlToDoList_ctlDB_ctl17_ctlFilter_ctlCustomerName_chkOn").prop("checked",!0),$("#ctl00_cphMain_ctlToDoList_ctlDB_ctl17_ctlFilter_ctlCustomerName_lblField").removeClass(),$("#ctl00_cphMain_ctlToDoList_ctlDB_ctl17_ctlFilter_ctlCustomerName_txt").focus());this._ctlSelect.registerTable(this._table);this.addRefreshEvent(Function.createDelegate(this,this.initAfterBaseIsReady));$find(this.getFilterField("ctlTaskCategory").get_id())._element.setAttribute("onchange",String.format('$find("{0}").onCategoryChange()',this._element.id))},initAfterBaseIsReady:function(){this.onCategoryChange();this.getData()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},dispose:function(){this.isDisposed||(this._ibtnExportCSV&&$R_IBTN.clearHandlers(this._ibtnExportCSV),this._ibtnExportCSV=null,this._intCompanyID=null,this._strCompanyName=null,this._intQuoteID=null,this._ctlSelect&&this._ctlSelect.dispose(),this._ctlSelect=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intToDoID)},getDataOK:function(){var t,u,n,i,r,f;for(this._ctlSelect.resetCount(),t=0,u=this._objResult.Results.length;t<u;t++){n=this._objResult.Results[t];let u="";n.TaskCategoryNo==2&&(u=$RGT_nubButton_Quote(n.QuoteNo,n.QuoteNumber));i=["&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"+$R_FN.setCleanTextValue(n.TaskDateFrom),$R_FN.setCleanTextValue(n.TaskDateTo),n.HasReminder==!0?$R_FN.setCleanTextValue(n.TaskReminderDate):"",$R_FN.setCleanTextValue(n.TaskTitle),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.TaskType),$R_FN.setCleanTextValue(n.TaskCategory)),$R_FN.setCleanTextValue(n.TaskStatus),$R_FN.setCleanTextValue(n.CustomerName),$R_FN.setCleanTextValue(n.SalesPersonName),u];r=n.IsComplete?"toDoComplete":"toDoIncomplete";r+=n.HasReminder&&!n.IsComplete?"Reminder":"NoReminder";f={Complete:n.IsComplete,TaskCategoryNo:n.TaskCategoryNo,QuoteNo:n.QuoteNo};this._table.addRow(i,n.ToDoListId,!1,f,r);i=null;n=null;this._table._intCountSelected=0}},exportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/DataListNuggets/ToDoList");n.set_DataObject("ToDoList");n.set_DataAction("ExportToCSV");n._intTimeoutMilliseconds=5e5;n.addParameter("SortIndex",this._sortIndex);n.addParameter("SortDir",this._sortDir);n.addParameter("PageIndex",this._pageIndex);n.addParameter("PageSize",this._pageSize);n.addParameter("TaskReminderDate",this.getFilterFieldValue("ctlTaskReminderDate"));n.addParameter("TaskDateFrom",this.getFilterFieldValue("ctlTaskDateFrom"));n.addParameter("TaskDateTo",this.getFilterFieldValue("ctlTaskDateTo"));n.addParameter("TaskType",this.getFilterFieldValue("ctlTaskType"));n.addParameter("TaskStatus",this.getFilterFieldValue("ctlTaskStatus"));n.addParameter("CustomerName",this.getFilterFieldValue("ctlCustomerName"));n.addParameter("Salesman",this.getFilterFieldValue("ctlSalesperson"));n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},tableSelect:function(){this._intToDoID=this._table._aryCurrentValues[0];this._intTaskCategoryID=this._table.getSelectedExtraData(this._table._arySelectedIndexes[0]).TaskCategoryNo;this._intQuoteID=this._table.getSelectedExtraData(this._table._arySelectedIndexes[0]).QuoteNo;this.enableButtons(!0)},enableButtons:function(n){n?($R_IBTN.enableButton(this._ibtnEdit,this._table._intCountSelected==1),$R_IBTN.enableButton(this._ibtnDelete,this._table._intCountSelected>0),this._table._intCountSelected>1?($R_IBTN.showButton(this._ibtnMarkComplete,!0),$R_IBTN.showButton(this._ibtnMarkIncomplete,!0)):this._table._intCountSelected==1&&this._table._arySelectedIndexes.length>0&&($R_IBTN.showButton(this._ibtnMarkComplete,!this._table.getSelectedExtraData(this._table._arySelectedIndexes[0]).Complete),$R_IBTN.showButton(this._ibtnMarkIncomplete,this._table.getSelectedExtraData(this._table._arySelectedIndexes[0]).Complete)),$R_IBTN.enableButton(this._ibtnMarkComplete,this._table._intCountSelected>0),$R_IBTN.enableButton(this._ibtnMarkIncomplete,this._table._intCountSelected>0)):($R_IBTN.enableButton(this._ibtnEdit,!1),$R_IBTN.enableButton(this._ibtnDelete,!1),$R_IBTN.enableButton(this._ibtnMarkComplete,!1),$R_IBTN.enableButton(this._ibtnMarkIncomplete,!1))},showEditForm:function(){this._frmEdit._intToDoID=this._intToDoID;this._frmEdit._intCategoryID=this._intTaskCategoryID;this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},editComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intToDoID=0;this.getData()},showDeleteForm:function(){this._frmConfirm.changeMode("DELETE");this.showConfirmForm()},showMarkCompleteForm:function(){this._frmConfirm.changeMode("MARK_COMPLETE");this.showConfirmForm()},showMarkIncompleteForm:function(){this._frmConfirm.changeMode("MARK_INCOMPLETE");this.showConfirmForm()},showConfirmForm:function(){this._frmConfirm._aryToDoIDs=this._table._aryCurrentValues;this.showForm(this._frmConfirm,!0)},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},confirmComplete:function(){this.hideConfirmForm();this._intToDoID=0;this.getData()},showAddForm:function(){this._frmAdd.setFormFieldsToDefaults();this._frmAdd.setFieldValue("ctlDueTime","09:00");this._frmAdd.setFieldValue("ctlReminderTime","09:00");this.showForm(this._frmAdd,!0)},hideAddForm:function(){this._frmAdd.resetFormData();this.showForm(this._frmAdd,!1)},addComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this._intToDoID=0;this.getData()},onCategoryChange:function(){let n=this.getFilterFieldValue("ctlTaskCategory");this.getFilterField("ctlQuoteNumber").show(n==this._quoteCategoryId)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ToDoList",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);