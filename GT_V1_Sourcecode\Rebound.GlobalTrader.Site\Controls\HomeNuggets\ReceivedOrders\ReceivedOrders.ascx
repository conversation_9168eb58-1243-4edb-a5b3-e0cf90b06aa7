<%@ Control Language="C#" CodeBehind="ReceivedOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.ReceivedOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<Content>
		<div class="homepageNugget">
			<ReboundUI:SimpleDataTable ID="tblReceived" runat="server" AllowSelection="false" />
		</div>
	</Content>
</ReboundUI_Nugget:DesignBase>
