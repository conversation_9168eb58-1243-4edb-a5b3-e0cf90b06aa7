﻿body, div, p, td, select, input, textarea {
	font-family: Tahoma !important;
}
h1, .subTitle {
	font-family: Lucida Sans Unicode !important;
	font-weight: normal !important;
}
#loginContainer h1 {
	background-image: url(images/misc/logo.gif);
}
.logo {
	background-image: url(images/misc/logo.gif);
}
.homeNugget {
	border: 1px solid #bbbbbb;
	padding: 10px;
	margin-bottom: 5px;
}
.homeNuggetMoreLink {
	border-top: 1px dotted #CCCCCC;
	margin-top: 5px;
	padding-top: 5px;
}
span.companyOnStop {
	background-color: #ff0000;
	color: #ffffff;
	font-family: Tahoma,Arial,sans-serif;
	font-size: 9px;
	font-weight: bold;
	padding: 1px 4px;
	margin: 0px;
	background-image: url(images/misc/onstop.gif);
	background-repeat: no-repeat;
	background-position: 2px center;
	display: inline-block;
	margin-left: -9px;
	text-indent: -9999999px;
	width: 13px;
}
span.companyOnStop:hover {
	padding-left: 14px;
	text-indent: 0px;
	width: auto;
}
.companyOnStop {
	margin-top: 10px;
	margin-bottom: 7px;
}
.companyOnStopInner {
	background-color: #ff0000;
	border: solid 1px #000000;
	padding: 5px;
	font-weight: bold;
	color: #ffffff;
}
.stockQuarantined {
	margin-top: 10px;
	margin-bottom: 7px;
}
.stockQuarantinedInner {
	background-color: Red;
	border: solid 1px #000000;
	padding: 5px;
	font-weight: bold;
	color: #ffffff;
}
.sourcingPartSearch {
	background-color: #C2FFB8;
	color: #72B058;
	font-size: 11px;
	margin: 0px 0px 20px;
	padding: 10px 5px;
	font-weight:bold;
}
.sourcingPartSearch {
	background-color: #C2FFB8;
	color: #72B058;
	font-size: 11px;
	margin: -3px 0px 20px;
	padding: 10px 5px;
	font-weight:bold;
}
.sourcingPartSearch textarea {
	border:1px solid #97DD8F;
	-moz-border-radius-bottomleft:4px;
	-moz-border-radius-bottomright:4px;
	-moz-border-radius-topleft:4px;
	-moz-border-radius-topright:4px;
}
.sourcingPartSearch a {
	color: #72B058;
	text-decoration:none;
	font-weight:normal;
}
.sourcingPartSearch a:hover {
	text-decoration:underline;
}
.sourcingPartSearchCollapse, .sourcingPartSearchJump {
	margin-top:5px;
}
.sourcingPartSearchCollapse {
	padding-right:3px;
}
.sourcingRecentParts {
	padding: 25px 10px 10px 25px;
}
.addNotAllowed {
	background-position: 3px top;
	background-repeat: no-repeat;
	padding: 5px 0px 12px 42px;
	background-image: url(images/messages/info.png);
	color: #666666;
	font-size: 14px;
	margin: 10px 0px;
}
/* rohs */
/********************************************************/
.rohs {
	display: inline;
	background-repeat: no-repeat;
	background-position: right 2px;
	padding-right: 28px;
	font-size: 11px;
	white-space: nowrap;
}
.rohsCompliant {
	background-image: url(images/rohs/compliant.gif);
}
.rohsNonCompliant {
	background-image: url(images/rohs/non_compliant.gif);
}
.rohsExempt {
	background-image: url(images/rohs/exempt.gif);
}
.rohsROHS2 {
	background-image: url(images/rohs/ROHS2.gif);
}
.rohsROHS56 {
	background-image: url(images/rohs/ROHS56.gif);
}
.rohsROHS66 {
	background-image: url(images/rohs/ROHS66.gif);
}

.hazardousUPrice {
	display: inline;
	background-repeat: no-repeat;
	background-position: 82px -1px;
	padding-right: 28px;
	font-size: 11px;
	white-space: nowrap;
	/*background-image: url(images/hazardous/Hazardouslogo.png);*/
	/*background-image: url(images/hazardous/Hazardousone.png);*/
	background-image: url(images/hazardous/Hazardousone.png);
}

.hazardous {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 13px;
    white-space: nowrap;
    /*background-image: url(images/hazardous/Hazardouslogo.png);*/
    /*background-image: url(images/hazardous/Hazardousone.png);*/
    background-image: url(images/hazardous/Hazardousone.png);
}

.hazardousIpo {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 13px;
    white-space: nowrap;
    background-image: url(images/hazardous/IPOprodone.png);
}

.hazardousRh {
    display: inline;
    background-repeat: no-repeat;
    background-position: right 2px;
    padding-right: 28px;
    font-size: 13px;
    white-space: nowrap;
    background-image: url(images/hazardous/Restrictedprodone.png);
}



/* Stock Quantities */
/********************************************************/
.stockQuantities {
	position: relative;
	height: 20px;
	margin-top: 5px;
}
.stockQuantities .lines1 {
	position: relative;
}
.stockQuantities .text {
	left: 255px;
	line-height: 12px;
	font-size: 11px;
	top: 1px;
}
.stockQuantities .quantity, .stockQuantities .back, .stockQuantities .text {
	height: 14px;
	position: absolute;
}
.stockQuantities .back {
	background-image: url(images/stock/top_bg.png);
	background-repeat: repeat-x;
	background-position: bottom left;
	background-color: Transparent;
	width: 250px;
	top: 3px;
}
.stockQuantities div.inStock {
	background-color: #0033FF;
	top: 0px;
}
.stockQuantities div.onOrder {
	background-color: #0099FF;
}
.stockQuantities div.allocated {
	background-color: #FE9900;
	height: 7px;
	top: 10px;
}
.stockQuantities div.available {
	background-color: #FFFF00;
}
.stockQuantities .text .inStock {
	background-color: transparent;
	color: #0033FF;
}
.stockQuantities .text .onOrder {
	background-color: transparent;
	color: #0099FF;
}
.stockQuantities .text .allocated {
	background-color: transparent;
	color: #FE9900;
}
.stockQuantities .text .available {
	background-color: transparent;
	color: #009900;
	font-weight: bold;
}
/* Stock Images */
/********************************************************/
.stockImage {
	float: left;
	margin: 0px 5px 5px 0px;
	padding: 2px;
	border: 1px solid #cccccc;
	position: relative;
	cursor:default;
}
.stockImageDelete {
	position: absolute;
	top: 3px;
	right: 3px;
	background-image: url(images/stock/imgdelete.png);
	background-repeat: no-repeat;
	background-position:center center;
	display: none;
	visibility: hidden;
	width:19px;
	height:19px;
	cursor:pointer;
}
.stockImage:hover .stockImageDelete {
	display: block;
	visibility: visible;
}
.stockImageDelete:hover {
	border:solid 1px #666666;
	top: 2px;
	right: 2px;
}
.stockImageCaption {
	color: #666666;
	text-align: center;
	font-size: 10px;
	padding: 5px 2px;
	margin-top: 2px;
	border-top: 1px dotted #cccccc;
}
.uploadFileTypes {
	font-size: 11px;
	font-style: italic;
}
/* Stock Log */
/********************************************************/
.stockLogChangedField {
	border-bottom: dotted 1px #999999;
}
.stockLogChangedValue {
	color:#008800;
}
/* security Reports */
/***************************************/
table.secReports {
	width: 95%;
}
table.secReports td.item {
	width: 12px;
}
h4.secReports {
	margin-top: 10px;
}
h5.secReports {
	margin-top: 5px;
}
/* Reports */
/***************************************/
div.reportList h5 {
	width:100%;
	clear:both;
}
div.reportListItem {
	background-image: url(images/misc/report.gif);
	background-position: left 5px;
	background-repeat: no-repeat;
	margin-top: 4px;
	margin-bottom: 12px;
	padding-left: 27px;
	padding-top: 3px;
	width:40%;
	float:left;
	margin-right:20px;
	min-height:20px;
}



div.reportListItem a {
	text-decoration: none;
}
div.reportListItem a:hover {
	text-decoration: underline;
}
div.reportListItem blockquote {
	font-size: 11px;
	color: #000000;
	padding: 0px;
	margin: 0px 0px 0px 1px;
}


div.reportListItemHUB {
	background-image: url(images/misc/report.gif);
	background-position: left 5px;
	background-repeat: no-repeat;
	margin-top: 4px;
	margin-bottom: 12px;
	padding-left: 27px;
	padding-top: 3px;
	width:40%;
	float:left;
	margin-right:20px;
	min-height:20px;
}


div.reportListItemHUB a {
	text-decoration: none;
}
div.reportListItemHUB a:hover {
	text-decoration: underline;
}
div.reportListItemHUB blockquote {
	font-size: 11px;
	color: #000000;
	padding: 0px;
	margin: 0px 0px 0px 1px;
}

.reportsClearing {
	height:1px;
	line-height:1px;
	clear:both;
}
.reportParameters input, .reportParameters select, .reportParameters textarea {
	border:1px solid #cccccc;
}
/* titlebar icons */
/***************************************/
.titleBarIcons {
	display:inline;
}
.titleBarIcon img {
	border-style:none;
}
.titleBarIcon {
	background-position:center center;
	background-repeat:no-repeat;
	margin-right:3px;
}
.titleBarIcon_Profile {
	background-image:url(images/smallicons/profile.gif);
}
.titleBarIcon_Mail {
	background-image:url(images/smallicons/mail.gif);
}
.titleBarIcon_ToDo {
	background-image:url(images/smallicons/todo.gif);
}
.titleBarIcon_Profile:hover {
	background-image:url(images/smallicons/profile_x.gif);
}
.titleBarIcon_Mail:hover {
	background-image:url(images/smallicons/mail_x.gif);
}
.titleBarIcon_ToDo:hover {
	background-image:url(images/smallicons/todo_x.gif);
}
.titleBarIcon_Profile img {
	width:11px;
	height:12px;
}
.titleBarIcon_Mail img {
	width:12px;
	height:9px;
}
.titleBarIcon_ToDo img {
	width:11px;
	height:11px;
}
/* Client List (login) */
/***************************************/
.clientListOuter {
	margin:24px 0px 50px 6px;
}
.clientList {
	margin-top:10px;
	border-top:dotted 1px #cccccc;
}
.clientList .item {
	padding:5px 0px;
	border-bottom:dotted 1px #cccccc;
	background-image:url(images/nubs/nub.gif);
	background-repeat:no-repeat;
	background-position:5px 7px;
	padding-left:18px;
}
.clientList .item a.nubButton {
	padding-left:0px;
	background-image:none;
}
.clientList .item a {
	text-decoration:none;
}
.clientList .item a:hover {
	text-decoration:underline;
}


.pdfDocument {
	float: left;
	margin: 7px 5px 5px 0px;
	padding: 2px;
	border: 1px solid #cccccc;
	position: relative;
	cursor:default;
}
.pdfDocumentDelete {
	position: absolute;
	top: 3px;
	right: 3px;
	background-image: url(images/stock/imgdelete.png);
	background-repeat: no-repeat;
	background-position:center center;
	display: none;
	visibility: hidden;
	width:19px;
	height:19px;
	cursor:pointer;
}
.pdfDocument:hover .pdfDocumentDelete {
	display: block;
	visibility: visible;
}
.pdfDocumentDelete:hover {
	border:solid 1px #666666;
	top: 2px;
	right: 2px;
}
.pdfDocumentCaption {
	color: #666666;
	text-align: center;
	font-size: 10px;
	padding: 5px 2px;
	margin-top: 2px;
	border-top: 1px dotted #cccccc;
}
.pdfInvoiceExportDocuments {
	color: #666666;
	text-align: center;
	font-size: 8px;
}
.showLargeFonts {
	display: inline;
	background-repeat: no-repeat;
	
	font-size: 20px;
    color:red;
	
}

.showLargeFontsWithColor {
	display: inline;
	background-repeat: no-repeat;
	
	font-size: 20px;
    color:blue;
	
}
span.SupplierStatusChanges {
	padding: 10px 3px;
	margin: 0px;
	background-image: url(images/misc/SupplierChange.gif);
	background-repeat: no-repeat;
	background-position: 5px bottom;
	display: inline-block;
	margin-left: -0px;
	text-indent: -9999999px;
	width: 20px;
}

span.SupplierChange {
	font-size: 9px;
	color:red;
	width: 20px;
}

.margin-left10px {
/*margin-left:37px;*/
}
.float-right
{
}

span.PartDetailsGridGoBtn {
	padding: 2px 7px;
	margin: 0px;
	background-image: url(images/IconButton/formbody/select.gif);
	background-repeat: no-repeat;
    margin-left: -2px;
	
}
span.PartDetailsGridGoBtnSearchIcon {
	padding: 2px 9px;
	margin: 0px;
	background-image: url(images/IconButton/formbody/SearachIcon.png);
	background-repeat: no-repeat;
    margin-left: 4px;
	
}
span.PartDetailsGridGoBtnSearchIcon2 {
	padding: 2px 7px;
	margin: 0px;
	background-image: url(images/IconButton/formbody/SearachIcon.png);
	background-repeat: no-repeat;
    margin-left: -2px;
	
}
span.PartDetailsGridGoBtn2 {
    padding: 2px 7px;
    margin-left: 7px;
}
span.PartDetailsGridGoError {
color:brown;
 margin-left: 10px;
}
/*////////////////----- For Quote Line IsImportant Button ------/////////////////////*/

span.quoteOnStop {
	/*background-color: #ff0000;*/
	color:red;
	font-family: Tahoma,Arial,sans-serif;
	font-size: 9px;
	font-weight: bold;
    margin-left: -9px;
    margin: 0px;
    padding: 1px 4px;
	padding: 1px 4px;
	margin: 0px;
	/*background-image: url(images/misc/imp.png);
	background-repeat: no-repeat;
	background-position: 2px center;
	display: inline-block;*/
	margin-left: -9px;
	text-indent: -9999999px;
	width: 5px;
}
span.quoteOnStop:hover {
	padding-left: 4px;
	text-indent: 0px;
   	width: auto;
}
.quoteOnStop {
	margin-top: 10px;
	margin-bottom: 7px;
}
/*.quoteOnStopInner {
	background-color: #ff0000;
	border: solid 1px #000000;
	padding: 5px;
	font-weight: bold;
	color: #ffffff;
}*/


span.companyOnStop11 {
	background-color:transparent;
	color: black;
	font-family: Tahoma,Arial,sans-serif;
	font-size: 9px;
	font-weight: bold;
	padding: 1px 4px;
	margin: 0px;
	background-image: url(images/misc/onstop.gif);
	background-repeat: no-repeat;
	background-position: 2px center;
	display: inline-block;
	margin-left: -9px;
	text-indent: -9999999px;
	width: 5px;
    border-color:black;
}
/*span.companyOnStop11:hover {
	padding-left: 14px;
	text-indent: 0px;
	width: auto;
}*/
.companyOnStop11 {
	margin-top: 10px;
	margin-bottom: 7px;
}
.companyOnStop11Inner {
	background-color: #ff0000;
	border: solid 1px #000000;
	padding: 5px;
	font-weight: bold;
	color: #ffffff;
}

.ihspartstatusdoc {
	display: inline;
	background-repeat: no-repeat;
	background-position: right 2px;
	padding-right: 30px;
	font-size: 11px;
    white-space: nowrap;
    background-image: url(images/hazardous/ihspartstatuspng.png);background-size: contain;
background-position: right;
   
}

.poCourntySectionWarnning {
	display: inline;
	background-repeat: no-repeat;
	background-position: right 2px;
	padding-right: 30px;
	font-size: 11px;
	white-space: nowrap;
	background-image: url(images/hazardous/ihspartstatuspng.png);
	background-size: contain;
	background-position: right;
	white-space: pre-line;
}

span.countryWarningMessage {
	display: inline;
	background-repeat: no-repeat;
	background-position: right 2px;
	padding-right: 30px;
	font-size: 11px;
	white-space: nowrap;
	background-image: url(images/hazardous/Hazardous.png);
	background-size: contain;
	background-position: right;
}

span.PremierCustomer {
	background-color: #ff0000;
	color: #ffffff;
	font-family: Tahoma,Arial,sans-serif;
	font-size: 9px;
	font-weight: bold;
	padding: 1px 4px;
	margin: 0px;
	background-image: url(images/misc/onstop.gif);
	background-repeat: no-repeat;
	background-position: 2px center;
	display: inline-block;
	margin-left: -9px;
	text-indent: -9999999px;
	width: 5px;
}

	span.PremierCustomer:hover {
		padding-left: 14px;
		text-indent: 0px;
		width: auto;
	}

.PremierCustomer {
	margin-top: 10px;
	margin-bottom: 7px;
}

.PremierCustomerInner {
	padding: 13px;
	font-weight: bold;
	color: #ffffff;
	background-image: url(images/hazardous/premier_icon.png);
	background-repeat: no-repeat;
	display: block;
}


span.Tier2PremierCustomer {
	background-color: #ff0000;
	color: #ffffff;
	font-family: Tahoma,Arial,sans-serif;
	font-size: 9px;
	font-weight: bold;
	padding: 1px 4px;
	margin: 0px;
	background-image: url(images/misc/onstop.gif);
	background-repeat: no-repeat;
	background-position: 2px center;
	display: inline-block;
	margin-left: -9px;
	text-indent: -9999999px;
	width: 5px;
}

	span.Tier2PremierCustomer:hover {
		padding-left: 14px;
		text-indent: 0px;
		width: auto;
	}

.Tier2PremierCustomer {
	margin-top: 10px;
	margin-bottom: 7px;
}

.Tier2PremierCustomerInner {
	padding: 13px;
	font-weight: bold;
	color: #ffffff;
	background-image: url(images/hazardous/tier_customer_icon.png);
	background-repeat: no-repeat;
	display: block;
}
#ctl00_cphMain_ctlSourcing_ctlDB_ctl13_hypNewNPR {
    font-size: 11px !important;
    margin-left: 20px;
    font-family: Lucida Sans Unicode, Arial;
}

.advisory-notes {
	display: inline;
	background-repeat: no-repeat;
	padding-right: 30px;
	font-size: 11px;
	white-space: nowrap;
	background-image: url(images/hazardous/exclamation.png);
	background-size: contain;
	background-position: left;
	/*margin-left: -25px;*/
}
.dataTable .advisory-notes {
	padding-right: 40px
}
.margin-left-10 {
	margin-left: 10px;
}
