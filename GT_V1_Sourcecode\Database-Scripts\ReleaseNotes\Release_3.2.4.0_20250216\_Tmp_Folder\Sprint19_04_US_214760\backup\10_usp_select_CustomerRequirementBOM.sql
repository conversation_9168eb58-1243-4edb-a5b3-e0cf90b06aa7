﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213481]     Phuc Hoang		 06-Oct-2024		UPDATE		Show Stock Alert on HUBRFQ page as Requirement page
[US-215434]	    Phuc Hoang	     06-Nov-2024	    Update	    Lytica Price should apply fuzzy logic for inserting & displaying
=============================================================================================  
*/

CREATE OR ALTER    PROCEDURE [dbo].[usp_select_CustomerRequirementBOM]                 
@CustomerRequirementId int,
@ClientNo INT
--*===========================================================================================  
--* Action: Altered  By: Abhinav Saxena  Date:01-09-2023  Comment: For RP-2228  
--*===========================================================================================                     
AS   
BEGIN                   
   Declare @SourcingResultId int=0          
     declare @PurchaseRequestId nvarchar(1000),            
   @PurchaseRequestNumber nvarchar(1000)          
   --select @PurchaseRequestId=pr.PurchaseRequestId ,@PurchaseRequestNumber= pr.PurchaseRequestNumber  from tbPurchaseRequest pr          
   --uncomment bellow line to use comma seprated      
   select @PurchaseRequestId=COALESCE(@PurchaseRequestId + ',','') + COALESCE(Cast( pr.PurchaseRequestId As Varchar),''),      
   @PurchaseRequestNumber=COALESCE(@PurchaseRequestNumber + ',','') + COALESCE(Cast( pr.PurchaseRequestNumber As Varchar),'')  from tbPurchaseRequest pr       
   join tbPurchaseRequestLine prl on prl.PurchaseRequestNo = pr.PurchaseRequestId where prl.CustomerRequirementNo=@CustomerRequirementId            
   -- sart prev Query             
--   SELECT @SourcingResultId = count(1) FROM tbCustomerRequirement cr                               
--JOIN tbBOM bm ON bm.BOMId = cr.BOMNo                               
--JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                
--Join tbQuoteLine ql on sr.SourcingResultId=ql.SourcingResultNo                            
--WHERE cr.CustomerRequirementId = @CustomerRequirementId               
-- End Prv Query              
            
-- SELECT  @SourcingResultId = count(1) FROM tbCustomerRequirement cr                               
--JOIN tbBOM bm ON bm.BOMId = cr.BOMNo                               
--JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                
--Join tbQuoteLine ql on sr.SourcingResultId=ql.SourcingResultNo               
--join tbSalesOrderLine sol on ql.QuoteLineId=sol.QuoteLineNo              
--join tbSalesOrder so on sol.SalesOrderNo=so.SalesOrderId            
--join  tbInternalPurchaseOrder ipo on so.SalesOrderId=ipo.SalesOrderNo                    
--WHERE cr.CustomerRequirementId = @CustomerRequirementId            
             
  SELECT  @SourcingResultId = count(1) FROM tbCustomerRequirement cr                                 
JOIN tbSourcingResult sr on cr.CustomerRequirementId = sr.CustomerRequirementNo           
join  tbInternalPurchaseOrderLine ipo on sr.SourcingResultId=ipo.SourcingResultNo                    
WHERE cr.CustomerRequirementId = @CustomerRequirementId;

DECLARE @manufacturerName NVARCHAR(256) = NULL,
		@manufacturerNo INT = 0,
		@partNumber NVARCHAR(256) = NULL,
		@LyticaManufacturerRef NVARCHAR(256) = NULL,
		@LyticaAveragePrice FLOAT = NULL,
		@LyticaTargetPrice FLOAT = NULL,
		@LyticaMarketLeading FLOAT = NULL;

	SELECT TOP 1 @partNumber = Part, @manufacturerNo = ManufacturerNo, @LyticaManufacturerRef = LyticaManufacturerRef 
		FROM dbo.tbCustomerRequirement 
		WHERE CustomerRequirementId = @CustomerRequirementId;

	IF ISNULL(@LyticaManufacturerRef, '') = ''
	BEGIN
		SELECT TOP 1 @manufacturerName = ManufacturerName FROM dbo.tbManufacturer WHERE ManufacturerId = @manufacturerNo;

		SELECT TOP 1 @LyticaManufacturerRef = Manufacturer, @LyticaAveragePrice = AveragePrice, @LyticaTargetPrice = TargetPrice, @LyticaMarketLeading = MarketLeading
			FROM dbo.tbLyticaAPI
			WHERE OriginalPartSearched = @partNumber 
			AND ISNULL(Inactive, 0) = 0
			AND (ISNULL(AveragePrice, 0) + ISNULL(TargetPrice, 0) + ISNULL(MarketLeading, 0)) > 0
			AND (
				Manufacturer = ISNULL(@manufacturerName, '') 
				OR Manufacturer LIKE ISNULL(@manufacturerName, '')  + '%'
				OR @manufacturerName LIKE ISNULL(Manufacturer, '') + '%' 
				OR Manufacturer LIKE ISNULL([dbo].[ufn_GetFirstWord](@manufacturerName), '') + '%'
			);

		UPDATE dbo.tbCustomerRequirement
		SET LyticaManufacturerRef = @LyticaManufacturerRef 
			,LyticaAveragePrice = @LyticaAveragePrice
			,LyticaTargetPrice = @LyticaTargetPrice
			,LyticaMarketLeading = @LyticaMarketLeading
		WHERE CustomerRequirementId = @CustomerRequirementId;
	END
              
SELECT * ,                  
( select ServiceName from [tbRequirementDropDownData] where id=cr.QuoteValidityRequired) as QuoteValidityText,                  
( select ServiceName from [tbRequirementDropDownData] where id=cr.ReqType) as ReqTypeText,                  
( select ServiceName from [tbRequirementDropDownData] where id=cr.ReqForTraceability) as ReqForTraceabilityText                
, @SourcingResultId as SourcingResult               
, cuc.CurrencyCode as ClientCurrencyCode              
, cuc.GlobalCurrencyNo as ClientGlobalCurrencyNo           
, dbo.ufn_get_productdutyrate(cr.ProductNo,getdate()) as ProductDutyRate             
, ( select top 1 MSLLevelId from tbMSLLevel where MSLLevel=cr.MSL) as MSLLevelNo        
,@PurchaseRequestId as PurchaseRequestId        
,@PurchaseRequestNumber as PurchaseRequestNumber    
 ,cast(dbo.ufn_GetECCNMessage(cr.ECCNCode,cr.ClientNo)as nvarchar(900))as IHSECCNCodeDefination  
 , CASE WHEN ISNULL(cr.AS6081,0)=1 THEN 'Yes' ELSE 'No' END AS IsAs6081Required     
 ,cr.IsPDFAvailable,cr.IHSPartsId
 ,cast(dbo.ufn_GetStockAvailableDetail(cr.Part,@ClientNo,null)as nvarchar(900))as StockAvailableDetail 

FROM  dbo.vwCustomerRequirement cr                  
LEFT JOIN dbo.tbCurrency cuc   ON cr.ClientCurrencyNo = cuc.CurrencyId                    
WHERE cr.CustomerRequirementId = @CustomerRequirementId           
END          
  
  
GO


