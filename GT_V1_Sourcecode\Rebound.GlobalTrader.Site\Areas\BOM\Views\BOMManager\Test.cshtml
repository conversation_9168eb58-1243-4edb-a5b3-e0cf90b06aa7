﻿
@{
    Layout = null;
}

<!DOCTYPE html>

<html>
<head>
    <meta name="viewport" content="width=device-width" />
    <script src='https://code.jquery.com/jquery-2.2.4.min.js'></script>
    <script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js'></script>
    <!-- Latest compiled and minified CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" integrity="sha384-1q8mTJOASx8j1Au+a5WDVnPi2lkFfwwEAa8hDDdjZlpLegxhjVME1fgjWPGmkzs7" crossorigin="anonymous">

    <script src="https://unpkg.com/jquery@2.2.4/dist/jquery.js"></script>


    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css'>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.15.0/popper.min.js'></script>
    <script src='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/js/bootstrap.min.js'></script>



    <script src="https://unpkg.com/jquery@2.2.4/dist/jquery.js"></script>

    <!--jQueryUI version 1.11.4 -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css" />
    <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    @*<script src="https://code.jquery.com/jquery-3.6.0.js"></script>
        <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>*@

    <!--ParamQuery Grid css files-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.dev.css" />

    <!--add pqgrid.ui.css for jQueryUI theme support-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.ui.dev.css" />

    <!--ParamQuery Grid custom theme e.g., office, bootstrap, rosy, chocolate, etc (optional)-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/themes/bootstrap/pqgrid.css" />

    <!--ParamQuery Grid js files-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqgrid.dev.js"></script>

    <!--ParamQuery Grid localization file-->
    <script src="~/paramquery-8.1.0/localize/pq-localize-en.js"></script>

    <!--Include pqTouch file to provide support for touch devices (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqTouch/pqtouch.min.js"></script>

    <!--Include jsZip file to support xlsx and zip export (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/jsZip-2.5.0/jszip.min.js"></script>
    @*<script type="text/javascript" src="~/Areas/BOM/js/BOMSearch.js"></script>*@

    <script src="http://www.guriddo.net/demo/js/jquery.min.js"></script>
    <!-- This is the Javascript file of jqGrid -->
    <script src="http://www.guriddo.net/demo/js/trirand/jquery.jqGrid.min.js"></script>
    <!-- This is the localization file of the grid controlling messages, labels, etc.
    <!-- We support more than 40 localizations -->
    <script src="http://www.guriddo.net/demo/js/trirand/i18n/grid.locale-en.js"></script>

    <!-- A link to a jQuery UI ThemeRoller theme, more than 22 built-in and many more custom -->
    <link rel="stylesheet" type="text/css" media="screen" href="http://www.guriddo.net/demo/css/jquery-ui.css" />
    <!-- The link to the CSS that the grid needs -->
    <link rel="stylesheet" type="text/css" media="screen" href="http://www.guriddo.net/demo/css/trirand/ui.jqgrid.css" />
    <title>Test</title>
</head>
<body>
    <div>This is Test Page.</div>
    <div class="box boxStandard">
        <div class="boxInner">
            <div class="boxTL"></div>
            <div class="boxTR"></div>
            <div class="boxHeader">
            </div>
            <div class="boxContent">
                <div class="resulttable">
                    <div id="grid1Pager1" class="gpager"></div>
                    <div id="grid_paging"></div>
                    <div id="grid1Pager2" class="gpager"></div>
                </div>
            </div>
            <div class="boxBL"></div>
            <div class="boxBR"></div>

        </div>
    </div>
    <div id='pager1' class="pagerclass" style='text-align: center;'></div>
    <table id='list' cellpadding='0' cellspacing='0'></table>
    <div id='pager2' class="pagerclass" style='text-align: center;'></div>

    <input type="button" value="load data" id="btnload" />
    <script type="text/javascript">
        var $grid = jQuery('#list')
        $(document).ready(function () {
            console.log('ready');
            //LoadData();
            LoadJQGridData();
        });
        $('#btnload').click(function () {
            LoadJQGridData();
        });
        function LoadJQGridData() {

            //{ title: "Order ID", width: 100, dataIndx: "BOMManagerId" },
            //{ title: "Customer Name", width: 130, dataIndx: "BOMManagerName" }

           

            $grid.jqGrid({
                url: 'Paging?SearchType=3',
                
                datatype: 'json',
                colNames: ['CompanyName', 'CompanyName'],
                colModel: [
                    { name: 'CompanyName', index: 'CompanyName', hidden: false },
                    { name: 'CompanyName', index: 'CompanyName', width: 90, sortable: true }
                ],
                rowNum: 3,
                width: 960,
                height: 'auto',
                rowList: [3,5,10, 20, 50],
                //imgpath: '/themes/redmond/images',
                toppager: true,
                pager: jQuery('.pagerclass'),
                toolbar: [true, "top"],
                //sortname: 'id',
                hidegrid: false,
                viewrecords: true,
                sortorder: 'asc',
                caption: 'User Collection',
                loadComplete: function (data) {
                    debugger;
                    alert('data loaded');
                },
            }).navGrid('#pager1', { cloneToTop: true, refresh:false, edit: false, add: false, del: false, search: false }).
                jqGrid('navButtonAdd', "#pager1", { caption: "Add",
                    id: "lblrecordcount",//.addClass("my-desired-class"),
                    caption: "1218", buttonicon: "none",
                });
            debugger;
            $grid.jqGrid('navButtonAdd', "#pager1", {
                id: "lblrecordcount1",//.addClass("my-desired-class"),
                caption: "results |", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', "#pager1", {
                id: "lblrecordcount3",//.addClass("my-desired-class"),
                caption: "Per Page:", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', "#pager1", {
                id: "lbl5",//.addClass("my-desired-class"),
                caption: "5", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', "#pager1", {
                id: "lbl10",//.addClass("my-desired-class"),
                caption: "10", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', "#pager1", {
                id: "lbl25",//.addClass("my-desired-class"),
                caption: "25", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', "#pager1", {
                id: "lbl50",//.addClass("my-desired-class"),
                caption: "50", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', "#pager1", {
                id: "lblnewfilter",//.addClass("my-desired-class"),
                caption: "Filter", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', "#pager1", {
                id: "lblnewlock",//.addClass("my-desired-class"),
                caption: "Lock", buttonicon: "none"
            });
            
            //$grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
            //    caption: "Add",
            //    buttonicon: "ui-icon-add",
            //    onClickButton: function () {
            //        alert("Adding Row");
            //    },
            //    position: "last"
            //},
            //);
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lblrecordcount",//.addClass("my-desired-class"),
                caption: "1218", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lblresults |",//.addClass("my-desired-class"),
                caption: "results", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lblPerPage",//.addClass("my-desired-class"),
                caption: "Per Page:", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lblpage5",//.addClass("my-desired-class"),
                caption: "5", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lblpage10",//.addClass("my-desired-class"),
                caption: "10", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lblpage25",//.addClass("my-desired-class"),
                caption: "25", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lblpage50",//.addClass("my-desired-class"),
                caption: "50", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lblFilter",//.addClass("my-desired-class"),
                caption: "Filter", buttonicon: "none"
            });
            $grid.jqGrid('navButtonAdd', '#' + $grid[0].id + "_toppager", {
                id: "lbllock",//.addClass("my-desired-class"),
                caption: "Lock", buttonicon: "none"
            });


            $('#aaa').addClass("my-desired-class");
                //.navButtonAdd('#pager1', {
                //    cloneToTop: true,
                //    caption: "Add",
                //    buttonicon: "ui-icon-add",
                //    onClickButton: function () {
                //        alert("Adding Row");
                //    },
                //    position: "last"
                //})
                //.navButtonAdd('#pager1', {
                //    cloneToTop: true,
                //    caption: "Add",
                //    buttonicon: "ui-icon-add",
                //    onClickButton: function () {
                //        alert("Adding Row");
                //    },
                //    position: "last"
                //})
            ;

        }
        function LoadData() {
            console.log('loaddata method');
            var colModel = [
                { title: "Order ID", width: 100, dataIndx: "BOMManagerId" },
                { title: "Customer Name", width: 130, dataIndx: "BOMManagerName" }
            ];
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "GET",
                url: "GridPagingTest",
                //url: "/pro/invoice.php",//for PHP
                getData: function (dataJSON) {
                    //var data = dataJSON.data;
                    //return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                    var data = dataJSON.data;
                    return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                }
            };
            var grid1 = $("#grid_paging").pqGrid({
                width: "auto", height: 450,
                dataModel: dataModel,
                colModel: colModel,
                //freezeCols: 2,
                pageModel: { type: "remote", rPP: 20, strRpp: "{0}" },
                numberCell: { show: false },

                showBottom: true
                //sortable: false,
                //selectionModel: { swipe: false },
                //wrap: false, hwrap: false,
                //virtualX: true, virtualY: true,
                //numberCell: { resizable: true, width: 30, title: "#" },
                //title: "Shipping Orders",
                //resizable: true
            });//.pqGrid('refreshDataAndView');
        }
        function paging() {
            console.log('paging method');
            var colModel = [
                { title: "Order ID", width: 100, dataIndx: "BOMManagerId" },
                { title: "Customer Name", width: 130, dataIndx: "BOMManagerName" }
            ];
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "GET",
                url: "GridPagingTest",
                //url: "/pro/invoice.php",//for PHP
                getData: function (dataJSON) {
                    //var data = dataJSON.data;
                    //return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                    var data = dataJSON.data;
                    return { curPage: dataJSON.curPage, totalRecords: dataJSON.totalRecords, data: data };
                }
            };
            var grid1 = $("#grid_paging").pqGrid({
                width: 900,
                height: 400,
                collapsible: false,
                pageModel: { type: "local", rPP: 20, strRpp: "{0}", strDisplay: "{0} to {1} of {2}" },
                dataModel: dataModel,
                colModel: colM,
                wrap: false, hwrap: false,
                freezeCols: 2,
                //move paging toolbar to top in create event callback.
                create: function (ui) {
                    var $pager = this.pager().widget();
                    if ($pager && $pager.length) {
                        $pager = $pager.detach();
                        this.widget().find(".pq-grid-top").append($pager);
                    }
                },
                numberCell: { resizable: true, title: "#" },
                showBottom: false,
                title: "Shipping Orders",
                resizable: true
            });//.pqGrid('refreshDataAndView');
        }
    </script>
</body>
</html>
