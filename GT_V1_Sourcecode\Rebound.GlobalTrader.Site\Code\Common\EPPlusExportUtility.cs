﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using Rebound.GlobalTrader.BLL;
using OfficeOpenXml;
using OfficeOpenXml.Drawing;
using OfficeOpenXml.Style;
using System.Drawing;
using System.Text;
using System.IO;
using iTextSharp.text;
using System.Threading;
using System.Data;
using Microsoft.Azure.Storage.Blob;

namespace Rebound.GlobalTrader.Site.Code.Common
{
    internal class EPPlusExportUtility
    {
        #region Property

        private bool blnHavePrintHazar { get; set; }
        private int startRowIndex { get; set; }
        int sFontSize = 8;
        int mFontSize = 9;
        private int smallFontSize { get { return sFontSize; } set { sFontSize = value; } }
        private int mediumFontSize { get { return mFontSize; } set { mFontSize = value; } }
        private int fromRowIndex { get; set; }
        private int fromColIndex { get; set; }
        private int toRowIndex { get; set; }
        private int toColIndex { get; set; }

        #endregion

        #region Method

        public EPPlusExportUtility()
        {
            //
            // TODO: Add constructor logic here
            //
        }
        public string PrintQuotes(Quote quote, out string FilePath)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = "QuoteReport" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".xlsx";
                FilePath = strFilename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage excelPackage = new ExcelPackage(fileInfo))
                {

                    excelPackage.Workbook.Properties.Author = "Global Trader";
                    excelPackage.Workbook.Properties.Title = "Quote Report";
                    excelPackage.Workbook.Properties.Subject = "Quote Report";

                    //Create a sheet
                    ExcelWorksheet ws = CreateSheet(excelPackage, "Quote");
                    #region Add Client Logo
                    string logopath = "";
                    if (string.IsNullOrEmpty(quote.HeaderImageNameQuote))
                        logopath = FileUploadManager.GetDocumentHeaderImageNameNew(quote.DivisionNo, true);//AppDomain.CurrentDomain.BaseDirectory + @"/images/client_101.jpg";
                    else
                        logopath = quote.HeaderImageNameQuote;
                    if (!string.IsNullOrEmpty(logopath))
                    {
                        try
                        {
                            AddImage(ws, 3, 0, 13, 6, logopath);
                        }
                        catch
                        { }
                    }
                    #endregion

                    #region Add main header detail
                    AddMainHeaderDetail(ws, quote);
                    #endregion

                    #region Add Table Header
                    AddTableHeader(ws);
                    #endregion

                    #region Add Table Data
                    AddTableData(ws, quote);
                    #endregion

                    #region Add footer detail
                    AddFooterData(ws, quote);
                    #endregion

                    #region export the generated excel
                    excelPackage.Save();

                    //using (var memoryStream = new MemoryStream())
                    //{
                    //    byte[] excel = excelPackage.GetAsByteArray();
                    //    string filename = Guid.NewGuid().ToString() + DateTime.Now.ToString("yyyyMMddHHmmss") + "_New" + ".pdf";
                    //    HttpContext.Current.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    //    HttpContext.Current.Response.AddHeader("content-disposition", "attachment; filename=" + filename + ".xlsx");
                    //    excelPackage.SaveAs(memoryStream);
                    //    memoryStream.WriteTo(HttpContext.Current.Response.OutputStream);
                    //    HttpContext.Current.Response.Flush();
                    //    HttpContext.Current.Response.End();
                    //}
                    #endregion


                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return filepath;
        }
        public string PrintCustomQuotes(Quote quote, out string FilePath, DataTable dt, string documentFormat)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = "QuoteReport" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + "." + documentFormat;
                FilePath = strFilename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                filepath = fullFilePath;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage excelPackage = new ExcelPackage(fileInfo))
                {

                    excelPackage.Workbook.Properties.Author = "Global Trader";
                    excelPackage.Workbook.Properties.Title = "Quote Report";
                    excelPackage.Workbook.Properties.Subject = "Quote Report";

                    //Create a sheet
                    ExcelWorksheet ws = CreateSheet(excelPackage, "Quote");
                    //#region Add Client Logo
                    //string logopath = "";
                    //if (string.IsNullOrEmpty(quote.HeaderImageNameQuote))
                    //    logopath = FileUploadManager.GetDocumentHeaderImageNameNew(quote.DivisionNo, true);//AppDomain.CurrentDomain.BaseDirectory + @"/images/client_101.jpg";
                    //else
                    //    logopath = quote.HeaderImageNameQuote;
                    //if (!string.IsNullOrEmpty(logopath))
                    //{
                    //    try
                    //    {
                    //        AddImage(ws, 3, 0, 13, 6, logopath);
                    //    }
                    //    catch
                    //    { }
                    //}
                    //#endregion

                    //#region Add main header detail
                    //AddMainHeaderDetail(ws, quote);
                    //#endregion

                    //#region Add Table Header
                    AddCustomTable(ws, dt);
                    //#endregion

                    //#region Add Table Data
                    //AddTableDataCustomQuote(ws, quote);
                    //#endregion

                    //#region Add footer detail
                    //AddFooterData(ws, quote);
                    //#endregion

                    #region export the generated excel
                    excelPackage.Save();

                    //using (var memoryStream = new MemoryStream())
                    //{
                    //    byte[] excel = excelPackage.GetAsByteArray();
                    //    string filename = Guid.NewGuid().ToString() + DateTime.Now.ToString("yyyyMMddHHmmss") + "_New" + ".pdf";
                    //    HttpContext.Current.Response.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
                    //    HttpContext.Current.Response.AddHeader("content-disposition", "attachment; filename=" + filename + ".xlsx");
                    //    excelPackage.SaveAs(memoryStream);
                    //    memoryStream.WriteTo(HttpContext.Current.Response.OutputStream);
                    //    HttpContext.Current.Response.Flush();
                    //    HttpContext.Current.Response.End();
                    //}
                    #endregion


                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return filepath;
        }
        public string PrintPurchaseOrder(PurchaseOrder purchaseOrder, out string FilePath)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = "PurchaseOrderReport" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".xlsx";
                FilePath = strFilename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage excelPackage = new ExcelPackage(fileInfo))
                {

                    excelPackage.Workbook.Properties.Author = "Global Trader";
                    excelPackage.Workbook.Properties.Title = "Purchase Order Report";
                    excelPackage.Workbook.Properties.Subject = "Purchase Order Report";

                    //Create a sheet
                    ExcelWorksheet ws = CreateSheet(excelPackage, "PurchaseOrder");
                    #region Add Client Logo
                    string logopath = "";
                    if (string.IsNullOrEmpty(purchaseOrder.HeaderImageNamePo))
                        logopath = FileUploadManager.GetDocumentHeaderImageNameNew(purchaseOrder.DivisionNo, true);//AppDomain.CurrentDomain.BaseDirectory + @"/images/client_101.jpg";
                    else
                        logopath = purchaseOrder.HeaderImageNamePo;
                    if (!string.IsNullOrEmpty(logopath))
                    {
                        try
                        {
                            AddImage(ws, 3, 0, 13, 6, logopath);
                        }
                        catch
                        { }
                    }
                    #endregion

                    #region Add main header detail
                    AddMainHeaderDetailPurchaseOrder(ws, purchaseOrder);
                    #endregion

                    #region Add Table Header
                    AddTableHeaderPurchaseOrder(ws);
                    #endregion

                    #region Add Table Data
                    AddTableDataPurchaseOrder(ws, purchaseOrder);
                    #endregion

                    #region Add footer detail
                    AddFooterDataPurchaseOrder(ws, purchaseOrder);
                    #endregion

                    #region export the generated excel
                    excelPackage.Save();


                    #endregion


                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return filepath;
        }

        public string PrintSalesOrder(SalesOrder salesOrder, out string FilePath)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = "SalesOrderReport" + DateTime.Now.ToString("yyyyMMddHHmmssfff") + ".xlsx";
                FilePath = strFilename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage excelPackage = new ExcelPackage(fileInfo))
                {

                    excelPackage.Workbook.Properties.Author = "Global Trader";
                    excelPackage.Workbook.Properties.Title = "Sales Order Report";
                    excelPackage.Workbook.Properties.Subject = "Sales Order Report";

                    //Create a sheet
                    ExcelWorksheet ws = CreateSheet(excelPackage, "SalesOrder");
                    #region Add Client Logo
                    string logopath = "";
                    if (string.IsNullOrEmpty(salesOrder.HeaderImageNameSo))
                        logopath = FileUploadManager.GetDocumentHeaderImageNameNew(salesOrder.DivisionNo, true);//AppDomain.CurrentDomain.BaseDirectory + @"/images/client_101.jpg";
                    else
                        logopath = salesOrder.HeaderImageNameSo;
                    if (!string.IsNullOrEmpty(logopath))
                    {
                        try
                        {
                            AddImage(ws, 3, 0, 13, 6, logopath);
                        }
                        catch
                        { }
                    }
                    #endregion

                    #region Add main header detail
                    AddMainHeaderDetailSalesOrder(ws, salesOrder);
                    #endregion

                    #region Add Table Header
                    AddTableHeaderSalesOrder(ws);
                    #endregion

                    #region Add Table Data
                    AddTableDataSalesOrder(ws, salesOrder);
                    #endregion

                    #region Add footer detail
                    AddFooterDataSalesOrder(ws, salesOrder);
                    #endregion

                    #region export the generated excel
                    excelPackage.Save();


                    #endregion


                }
            }
            catch (Exception ex)
            {
                throw ex;
            }
            return filepath;
        }
        private void AddFooterData(ExcelWorksheet ws, Quote quote)
        {
            startRowIndex = startRowIndex + 2;
            string strRowIdex = startRowIndex.ToString();
            string strHazardousPrint = "";
            if (string.IsNullOrEmpty(quote.SysDocHazardousHistoryText))
                strHazardousPrint = GetHazardousNotes(SystemDocument.FooterDocument.PrintHazardous, (int)SessionManager.ClientID);
            else
                strHazardousPrint = quote.SysDocHazardousHistoryText;
            string footerNote = string.Empty;
            if (blnHavePrintHazar && !string.IsNullOrEmpty(strHazardousPrint))
            {
                string hazardousNote = "## " + strHazardousPrint;
                footerNote = footerNote + hazardousNote;
            }
            string strFooter = "";
            if (string.IsNullOrEmpty(quote.FooterTextQuote))
                strFooter = GetFooterNotes(SystemDocument.ListForPrint.Quote);
            else
                strFooter = quote.FooterTextQuote;
            if (!string.IsNullOrEmpty(strFooter))
            {
                footerNote = footerNote + (!string.IsNullOrEmpty(footerNote) ? Environment.NewLine : "");
                footerNote = footerNote + strFooter;
            }
            if (Convert.ToBoolean(quote.AS9120))
            {
                string strFooterAS9120 = "";
                if (string.IsNullOrEmpty(quote.SysDocAS9120HistoryText))
                    strFooterAS9120 = GetFooterNotes(SystemDocument.ListForPrint.AS9120);
                else
                    strFooterAS9120 = quote.SysDocAS9120HistoryText;
                if (!string.IsNullOrEmpty(strFooterAS9120))
                {
                    footerNote = footerNote + (!string.IsNullOrEmpty(footerNote) ? Environment.NewLine : "");
                    footerNote = footerNote + strFooterAS9120;
                }
            }
            ws.Cells["A" + strRowIdex + ":" + "N" + strRowIdex].Value = footerNote;
            ws.Cells["A" + strRowIdex + ":" + "N" + strRowIdex].Merge = true;
            ws.Cells["A" + strRowIdex + ":" + "N" + strRowIdex].Style.WrapText = true;
            ws.Cells["A" + strRowIdex + ":" + "N" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["A" + strRowIdex + ":" + "N" + strRowIdex].Style.Font.Size = smallFontSize;
            ws.Row(startRowIndex).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(startRowIndex).Height = 600;


        }
        private void AddFooterDataPurchaseOrder(ExcelWorksheet ws, PurchaseOrder purchaseOrder)
        {
            startRowIndex = startRowIndex + 2;
            string strRowIdex = startRowIndex.ToString();
            string strHazardousPrint = "";
            if (string.IsNullOrEmpty(purchaseOrder.SysDocHazardousHistoryText))
                strHazardousPrint = GetHazardousNotes(SystemDocument.FooterDocument.PrintHazardous, (int)SessionManager.ClientID);
            else
                strHazardousPrint = purchaseOrder.SysDocHazardousHistoryText;
            string footerNote = string.Empty;
            if (blnHavePrintHazar && !string.IsNullOrEmpty(strHazardousPrint))
            {
                string hazardousNote = "## " + strHazardousPrint;
                footerNote = footerNote + hazardousNote;
            }
            string strFooter = "";
            if (string.IsNullOrEmpty(purchaseOrder.FooterTextPo))
                strFooter = GetFooterNotes(SystemDocument.ListForPrint.PurchaseOrderReport);
            else
                strFooter = purchaseOrder.FooterTextPo;
            if (!string.IsNullOrEmpty(strFooter))
            {
                footerNote = footerNote + (!string.IsNullOrEmpty(footerNote) ? Environment.NewLine : "");
                footerNote = footerNote + strFooter;
            }

            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Value = footerNote;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Merge = true;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.WrapText = true;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.Font.Size = smallFontSize;
            ws.Row(startRowIndex).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(startRowIndex).Height = 600;


        }
        private void AddFooterDataSalesOrder(ExcelWorksheet ws, SalesOrder salesOrder)
        {
            startRowIndex = startRowIndex + 2;
            string strRowIdex = startRowIndex.ToString();
            string strHazardousPrint = "";
            if (string.IsNullOrEmpty(salesOrder.SysDocHazardousHistoryText))
                strHazardousPrint = GetHazardousNotes(SystemDocument.FooterDocument.PrintHazardous, (int)SessionManager.ClientID);
            else
                strHazardousPrint = salesOrder.SysDocHazardousHistoryText;
            string footerNote = string.Empty;
            if (blnHavePrintHazar && !string.IsNullOrEmpty(strHazardousPrint))
            {
                string hazardousNote = strHazardousPrint;
                footerNote = footerNote + hazardousNote;
            }
            string strCOO = "";
            if (string.IsNullOrEmpty(salesOrder.SysDocCOOHistoryText))
                strCOO = GetHazardousNotes(SystemDocument.FooterDocument.CountryOfOrigin, (int)SessionManager.ClientID);
            else
                strCOO = salesOrder.SysDocCOOHistoryText;

            if (!string.IsNullOrEmpty(strCOO))
            {
                footerNote = footerNote + "Country of Origin" + System.Environment.NewLine + strCOO;
            }
            string strFooterAs9120 = "";
            if (string.IsNullOrEmpty(salesOrder.SysDocAS9120HistoryText))
                strFooterAs9120 = GetFooterNotes(SystemDocument.ListForPrint.AS9120, salesOrder.ClientNo);// new DAL().GetForClientAndDocument(so.ClientNo, Properties.Settings.Default.SystemDocumentNo);
            else
                strFooterAs9120 = salesOrder.SysDocAS9120HistoryText;
            if (Convert.ToBoolean(salesOrder.AS9120))
            {
                footerNote = footerNote + System.Environment.NewLine + strFooterAs9120;
            }
            string strFooter = "";
            if (string.IsNullOrEmpty(salesOrder.FooterTextSo))
                strFooter = GetFooterNotes(SystemDocument.ListForPrint.PurchaseOrderReport);
            else
                strFooter = salesOrder.FooterTextSo;
            if (!string.IsNullOrEmpty(strFooter))
            {
                footerNote = footerNote + (!string.IsNullOrEmpty(footerNote) ? Environment.NewLine : "");
                footerNote = footerNote + strFooter;
            }

            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Value = footerNote;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Merge = true;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.WrapText = true;
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["A" + strRowIdex + ":" + "M" + strRowIdex].Style.Font.Size = smallFontSize;
            ws.Row(startRowIndex).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(startRowIndex).Height = 600;


        }
        private void AddTableData(ExcelWorksheet ws, Quote quote)
        {

            startRowIndex = 16;
            fromRowIndex = startRowIndex; fromColIndex = 1; toColIndex = 13;
            string strRowIdex = string.Empty;
            List<QuoteLine> lstQO = QuoteLine.GetListForQuote(quote.QuoteId);
            double dblSubTotal = 0;
            foreach (QuoteLine ln in lstQO)
            {
                if (!ln.Closed || (ln.Closed && ln.NotQuoted))
                {
                    strRowIdex = startRowIndex.ToString();
                    ws.Row(startRowIndex).Height = 30;
                    ws.Row(startRowIndex).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                    ws.Row(startRowIndex).Style.WrapText = true;

                    double dblLineTotal = 0;
                    if (!ln.Closed) dblLineTotal = (double)ln.Price * (double)ln.Quantity;

                    string strProductSource = GetFormattedProductSourceForPrint(Convert.ToBoolean(quote.AS9120), (ln.ProductSource.HasValue) ? ln.ProductSource : null);

                    StringBuilder strLineNotes = new StringBuilder();
                    if (!string.IsNullOrEmpty(strProductSource))
                        strLineNotes.Append(System.Environment.NewLine + strProductSource);

                    if (!string.IsNullOrEmpty(ln.LineNotes))
                        strLineNotes.Append(System.Environment.NewLine + ln.LineNotes);
                    if (!string.IsNullOrEmpty(ln.MSL))
                        strLineNotes.Append(System.Environment.NewLine + ln.MSL);

                    string TotalNotes = strLineNotes.ToString();

                    ws.Cells["A" + strRowIdex].Value = Convert.ToString(Functions.FormatNumeric(ln.Quantity));
                    ws.Cells["B" + strRowIdex].Value = GetFormattedPartForPrint(ln.Part, null);
                    ws.Cells["C" + strRowIdex].Value = GetFormattedROHSForPrint((ln.ROHS.HasValue) ? ln.ROHS : null);
                    ws.Cells["D" + strRowIdex].Value = ln.CustomerPart;
                    ws.Cells["E" + strRowIdex].Value = ln.ManufacturerCode;
                    ws.Cells["F" + strRowIdex].Value = ln.DateCode;
                    ws.Cells["G" + strRowIdex].Value = ln.ProductName;
                    ws.Cells["H" + strRowIdex].Value = ln.DutyCode;
                    ws.Cells["I" + strRowIdex].Value = ln.PackageName;
                    ws.Cells["J" + strRowIdex].Value = ln.ETA;
                    ws.Cells["K" + strRowIdex].Value = TotalNotes;
                    ws.Cells["L" + strRowIdex].Value = ln.NotQuoted == true ? Functions.GetGlobalResource("Misc", "NotQuoted") : Functions.FormatCurrency(ln.Price, 5);
                    ws.Cells["M" + strRowIdex].Value = ln.NotQuoted == true ? "_" : Functions.FormatCurrency(dblLineTotal, 2);
                    ws.Cells["N" + strRowIdex].Value = ln.NotQuoted == true ? " " : ln.CurrencyCode;

                    ws.Cells["A" + strRowIdex + ":N" + strRowIdex].Style.Font.Name = "Tahoma";
                    dblSubTotal += dblLineTotal;
                    ++startRowIndex;
                    if (ln.PrintHazardous.Value)
                        blnHavePrintHazar = true;
                }
            }
            #region SubTotal & Total
            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();

            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Value = Functions.GetGlobalResource("Printing", "SubTotal");
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Merge = true;
            ws.Cells["M" + strRowIdex].Value = Functions.FormatCurrency(dblSubTotal, 2);
            ws.Cells["N" + strRowIdex].Value = quote.CurrencyCode;


            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Value = Functions.GetGlobalResource("Printing", "EstimatedFreight");
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Merge = true;
            ws.Cells["M" + strRowIdex].Value = Functions.FormatCurrency(quote.Freight, 2);
            ws.Cells["N" + strRowIdex].Value = quote.CurrencyCode;

            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Value = Functions.GetGlobalResource("Printing", "Total");
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["J" + strRowIdex + ":" + "L" + strRowIdex].Merge = true;
            ws.Cells["M" + strRowIdex].Value = Functions.FormatCurrency(quote.Freight + dblSubTotal, 2);
            ws.Cells["N" + strRowIdex].Value = quote.CurrencyCode;

            #endregion
            //set font size
            toRowIndex = startRowIndex;
            ws.Cells[fromRowIndex, fromColIndex, toRowIndex, toColIndex].Style.Font.Size = mediumFontSize;

        }
        private void AddTableDataPurchaseOrder(ExcelWorksheet ws, PurchaseOrder purchaseOrder)
        {

            startRowIndex = 19;
            fromRowIndex = startRowIndex; fromColIndex = 1; toColIndex = 13;
            string strRowIdex = string.Empty;
            List<PurchaseOrderLine> lstPO = PurchaseOrderLine.GetListForPurchaseOrder(purchaseOrder.PurchaseOrderId);
            double dblSubTotal = 0;
            double dbTax = 0;
            foreach (PurchaseOrderLine ln in lstPO)
            {
                if (!ln.Closed)
                {
                    strRowIdex = startRowIndex.ToString();
                    ws.Row(startRowIndex).Height = 30;
                    ws.Row(startRowIndex).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                    ws.Row(startRowIndex).Style.WrapText = true;

                    double dblLineTotal = 0;
                    if (!ln.Closed) dblLineTotal = (double)ln.Price * (double)ln.Quantity;

                    ws.Cells["A" + strRowIdex].Value = Convert.ToString(Functions.FormatNumeric(ln.Quantity));
                    ws.Cells["B" + strRowIdex].Value = GetFormattedPartForPrint(ln.Part, (ln.ROHS.HasValue) ? ln.ROHS : null);
                    ws.Cells["C" + strRowIdex].Value = ln.SupplierPart;
                    ws.Cells["D" + strRowIdex].Value = Functions.FormatDate(ln.DeliveryDate);
                    ws.Cells["E" + strRowIdex].Value = ln.MSLevel;
                    ws.Cells["F" + strRowIdex].Value = ln.ManufacturerCode;
                    ws.Cells["G" + strRowIdex].Value = ln.DateCode;
                    ws.Cells["H" + strRowIdex].Value = ln.ProductName;
                    ws.Cells["I" + strRowIdex].Value = ln.ProductDutyCode;
                    ws.Cells["J" + strRowIdex].Value = ln.PackageName;
                    ws.Cells["K" + strRowIdex].Value = Functions.FormatCurrency(ln.Price, 5);
                    ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(dblLineTotal, 2);
                    ws.Cells["M" + strRowIdex].Value = ln.CurrencyCode;
                    ws.Cells["A" + strRowIdex + ":M" + strRowIdex].Style.Font.Name = "Tahoma";
                    dblSubTotal += dblLineTotal;
                    dbTax += (ln.Taxable) ? (double)(dblLineTotal * (((ln.TaxRate == null) ? 0 : (double)ln.TaxRate) / 100)) : 0;
                    ++startRowIndex;
                    if (ln.PrintHazardous.Value)
                        blnHavePrintHazar = true;
                }
            }
            #region SubTotal & Total
            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();

            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Value = Functions.GetGlobalResource("Printing", "SubTotal");
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Merge = true;
            ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(dblSubTotal, 2);
            ws.Cells["M" + strRowIdex].Value = purchaseOrder.CurrencyCode;


            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Value = purchaseOrder.TaxName + " @ " + Functions.FormatPercentage(purchaseOrder.TaxRate);
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Merge = true;
            ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(dbTax, 2);
            ws.Cells["M" + strRowIdex].Value = purchaseOrder.CurrencyCode;

            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Value = Functions.GetGlobalResource("Printing", "Total");
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Merge = true;
            ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(dbTax + dblSubTotal, 2);
            ws.Cells["M" + strRowIdex].Value = purchaseOrder.CurrencyCode;

            #endregion
            //set font size
            toRowIndex = startRowIndex;
            ws.Cells[fromRowIndex, fromColIndex, toRowIndex, toColIndex].Style.Font.Size = mediumFontSize;

        }

        private void AddTableDataSalesOrder(ExcelWorksheet ws, SalesOrder salesOrder)
        {

            startRowIndex = 20;
            fromRowIndex = startRowIndex; fromColIndex = 1; toColIndex = 13;
            string strRowIdex = string.Empty;
            //List<SalesOrderLine> lstSO= SalesOrderLine.GetListForSalesOrder(salesOrder.SalesOrderId,false);
            List<SalesOrderLine> lstSO = SalesOrderLine.GetListForConsolidateSalesOrder(salesOrder.SalesOrderId, false);
            double dblSubTotal = 0;
            double dbTax = 0;

            foreach (SalesOrderLine ln in lstSO)
            {
                if (!ln.Closed)
                {
                    strRowIdex = startRowIndex.ToString();
                    ws.Row(startRowIndex).Height = 30;
                    ws.Row(startRowIndex).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
                    ws.Row(startRowIndex).Style.WrapText = true;

                    double dblLineTotal = 0;
                    if (!ln.Closed) dblLineTotal = (double)ln.Price * (double)ln.Quantity;
                    string strProductSource = GetFormattedProductSourceForPrint(Convert.ToBoolean(salesOrder.AS9120), (ln.ProductSource.HasValue) ? ln.ProductSource : null);
                    ws.Cells["A" + strRowIdex].Value = Convert.ToString(Functions.FormatNumeric(ln.Quantity));
                    ws.Cells["B" + strRowIdex].Value = GetFormattedPartForPrint(ln.Part, (ln.ROHS.HasValue) ? ln.ROHS : null);
                    ws.Cells["C" + strRowIdex].Value = ln.CustomerPart;
                    ws.Cells["D" + strRowIdex].Value = Functions.FormatDate(ln.DatePromised);
                    ws.Cells["E" + strRowIdex].Value = strProductSource;
                    ws.Cells["F" + strRowIdex].Value = ln.ManufacturerCode + "\r\n" + ln.CountryOfOrigin;
                    ws.Cells["G" + strRowIdex].Value = ln.DateCode;
                    ws.Cells["H" + strRowIdex].Value = ln.ProductName;
                    ws.Cells["I" + strRowIdex].Value = ln.DutyCode;
                    ws.Cells["J" + strRowIdex].Value = ln.PackageName;
                    ws.Cells["K" + strRowIdex].Value = Functions.FormatCurrency(ln.Price, 5);
                    ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(dblLineTotal, 2);
                    ws.Cells["M" + strRowIdex].Value = ln.CurrencyCode;
                    ws.Cells["A" + strRowIdex + ":M" + strRowIdex].Style.Font.Name = "Tahoma";
                    dblSubTotal += dblLineTotal;
                    dbTax += (ln.IsLineTaxable) ? (double)(dblLineTotal * (((salesOrder.TaxRate == null) ? 0 : (double)salesOrder.TaxRate) / 100)) : 0;
                    ++startRowIndex;
                    if (ln.PrintHazardous.Value)
                        blnHavePrintHazar = true;
                }
            }
            #region SubTotal & Total
            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();

            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Value = Functions.GetGlobalResource("Printing", "SubTotal");
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Merge = true;
            ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(salesOrder.LineSubTotal, 2);
            ws.Cells["M" + strRowIdex].Value = salesOrder.CurrencyCode;

            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Value = Functions.GetGlobalResource("Printing", "Freight");
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Merge = true;
            ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(salesOrder.Freight, 2);
            ws.Cells["M" + strRowIdex].Value = salesOrder.CurrencyCode;

            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Value = salesOrder.TaxName + " @ " + Functions.FormatPercentage(salesOrder.TaxRate);
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Merge = true;
            ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(salesOrder.TotalTax, 2);
            ws.Cells["M" + strRowIdex].Value = salesOrder.CurrencyCode;

            ws.Cells["A" + strRowIdex].Value = salesOrder.Instructions;
            ws.Cells["A" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["A" + strRowIdex + ":" + "C" + strRowIdex].Merge = true;


            ++startRowIndex;
            strRowIdex = startRowIndex.ToString();
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Value = Functions.GetGlobalResource("Printing", "Total");
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Bold = true;
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Style.Font.Name = "Tahoma";
            ws.Cells["I" + strRowIdex + ":" + "K" + strRowIdex].Merge = true;
            ws.Cells["L" + strRowIdex].Value = Functions.FormatCurrency(salesOrder.TotalValue, 2);
            ws.Cells["M" + strRowIdex].Value = salesOrder.CurrencyCode;

            #endregion
            //set font size
            toRowIndex = startRowIndex;
            ws.Cells[fromRowIndex, fromColIndex, toRowIndex, toColIndex].Style.Font.Size = mediumFontSize;

        }
        private void AddTableHeader(ExcelWorksheet ws)
        {
            ws.Row(15).Height = 30;
            ws.Row(15).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(15).Style.WrapText = true;
            ws.Cells["A14:J14"].Merge = true;

            ws.Cells["A15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Quantity");

            ws.Cells["B15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_PartNo");

            ws.Cells["C15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_ROHS");

            ws.Cells["D15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_CustomerPartNo");

            ws.Cells["E15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Manufacturer");

            ws.Cells["F15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_DateCode");

            ws.Cells["G15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Product");

            ws.Cells["H15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_DutyCode");

            ws.Cells["I15"].Value = Environment.NewLine + Functions.GetGlobalResource("Printing", "LineHeader_Package");

            ws.Cells["J15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_ETA");

            ws.Cells["K15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Notes");

            ws.Cells["L15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_UnitPrice");

            ws.Cells["M15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_TotalPrice");

            ws.Cells["N15"].Value = "";

            ws.Cells["A15:N15"].Style.Font.Bold = true;
            ws.Cells["A15:N15"].Style.Font.Name = "Tahoma,Arial,Sans-serif";
            ws.Cells["A15:N15"].Style.Fill.PatternType = ExcelFillStyle.Solid;
            ws.Cells["A15:N15"].Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#E0E0E0"));
            //set font size
            ws.Cells["A15:M15"].Style.Font.Size = mediumFontSize;

        }
        private void AddCustomTable(ExcelWorksheet ws, DataTable dt)
        {
            ws.Row(15).Height = 30;
            ws.Row(15).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(15).Style.WrapText = true;
            ws.Cells["A14:J14"].Merge = true;

            int RowIndex = 1;
            foreach (DataRow row in dt.Rows)
            {
                char AlphaIndex = 'A';
                ws.Cells[AlphaIndex++ + RowIndex.ToString()].Value = row["StockCode"];
                ws.Cells[AlphaIndex++ + RowIndex.ToString()].Value = row["Description"];
                ws.Cells[AlphaIndex++ + RowIndex.ToString()].Value = row["Part"];
                ws.Cells[AlphaIndex++ + RowIndex.ToString()].Value = row["RFQ"];
                ws.Cells[AlphaIndex++ + RowIndex.ToString()].Value = row["UnitPrice"];
                ws.Cells[AlphaIndex++ + RowIndex.ToString()].Value = row["Product"];
                ws.Cells[AlphaIndex++ + RowIndex.ToString()].Value = row["QuotedRate"];
                RowIndex++;
            }
            //ws.Cells["A15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Quantity");

            //ws.Cells["B15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_PartNo");

            //ws.Cells["C15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_CustomerPartNo");

            //ws.Cells["D15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Manufacturer");

            //ws.Cells["E15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_DateCode");

            //ws.Cells["F15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Product");

            //ws.Cells["G15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_DutyCode");

            //ws.Cells["H15"].Value = Environment.NewLine + Functions.GetGlobalResource("Printing", "LineHeader_Package");

            //ws.Cells["I15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_ETA");

            //ws.Cells["J15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Notes");

            //ws.Cells["K15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_UnitPrice");

            //ws.Cells["L15"].Value = Functions.GetGlobalResource("Printing", "LineHeader_TotalPrice");

            //ws.Cells["M15"].Value = "";

            //ws.Cells["A15:M15"].Style.Font.Bold = true;
            //ws.Cells["A15:M15"].Style.Font.Name = "Tahoma,Arial,Sans-serif";
            //ws.Cells["A15:M15"].Style.Fill.PatternType = ExcelFillStyle.Solid;
            //ws.Cells["A15:M15"].Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#E0E0E0"));
            ////set font size
            //ws.Cells["A15:M15"].Style.Font.Size = mediumFontSize;

        }
        private void AddTableHeaderPurchaseOrder(ExcelWorksheet ws)
        {
            ws.Row(18).Height = 30;
            ws.Row(18).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(18).Style.WrapText = true;
            ws.Cells["A17:J17"].Merge = true;

            ws.Cells["A18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Quantity");

            ws.Cells["B18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_PartNo");

            ws.Cells["C18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_SupplierPartNo");

            ws.Cells["D18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_DeliveryDate");

            ws.Cells["E18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Notes");

            ws.Cells["F18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Manufacturer");

            ws.Cells["G18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_DateCode");

            ws.Cells["H18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Product");

            ws.Cells["I18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_ProductDutyCode");

            ws.Cells["J18"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Package");

            ws.Cells["K18"].Value = Functions.GetGlobalResource("Printing", "Price");

            ws.Cells["L18"].Value = Functions.GetGlobalResource("Printing", "Amount");

            ws.Cells["M18"].Value = "";

            ws.Cells["A18:M18"].Style.Font.Bold = true;
            ws.Cells["A18:M18"].Style.Font.Name = "Tahoma,Arial,Sans-serif";
            ws.Cells["A18:M18"].Style.Fill.PatternType = ExcelFillStyle.Solid;
            ws.Cells["A18:M18"].Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#E0E0E0"));
            //set font size
            ws.Cells["A18:M18"].Style.Font.Size = mediumFontSize;

        }
        private void AddTableHeaderSalesOrder(ExcelWorksheet ws)
        {
            ws.Row(19).Height = 40;
            ws.Row(19).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(19).Style.WrapText = true;
            ws.Cells["A18:J18"].Merge = true;

            ws.Cells["A19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Quantity");

            ws.Cells["B19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_PartNo");

            ws.Cells["C19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_CustomerPartNo");

            ws.Cells["D19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_DueDate");

            ws.Cells["E19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Notes");

            ws.Cells["F19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Manufacturer") + "\r\n" + Functions.GetGlobalResource("Printing", "LineHeader_CountryOrigin");

            ws.Cells["G19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_DateCode");

            ws.Cells["H19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Product");

            ws.Cells["I19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_ProductDutyCode");

            ws.Cells["J19"].Value = Functions.GetGlobalResource("Printing", "LineHeader_Package");

            ws.Cells["K19"].Value = Functions.GetGlobalResource("Printing", "Price");

            ws.Cells["L19"].Value = Functions.GetGlobalResource("Printing", "Amount");

            ws.Cells["M19"].Value = "";

            ws.Cells["A19:M19"].Style.Font.Bold = true;
            ws.Cells["A19:M19"].Style.Font.Name = "Tahoma,Arial,Sans-serif";
            ws.Cells["A19:M19"].Style.Fill.PatternType = ExcelFillStyle.Solid;
            ws.Cells["A19:M19"].Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#E0E0E0"));
            //set font size
            ws.Cells["A19:M19"].Style.Font.Size = mediumFontSize;

        }
        private void AddMainHeaderDetail(ExcelWorksheet ws, Quote quote)
        {
            fromRowIndex = 9; fromColIndex = 1; toRowIndex = 14; toColIndex = 14;

            string quoteText = Functions.GetGlobalResource("Printing", "Quote") + " " + quote.QuoteNumber;//"Quote 1070073";
            //ws.Cells[7, 1].Value = quoteText + "           Print :"+ 1 + "  Date : "+DateTime.Now;
            ws.Cells[7, 1].Value = quoteText;
            ws.Cells[7, 1, 8, toColIndex].Merge = true;
            ws.Cells[7, 1, 8, toColIndex].Style.Font.Bold = true;
            ws.Cells[7, 1, 8, toColIndex].Style.Font.UnderLine = true;
            ws.Cells[7, 1, 8, toColIndex].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            ws.Column(1).Width = 15;
            ws.Column(6).Width = 15;
            ws.Row(9).Height = 30;
            ws.Row(9).Style.VerticalAlignment = ExcelVerticalAlignment.Top;

            string billTo = Functions.GetGlobalResource("Printing", "TO");
            ws.Cells["A9"].Value = billTo;
            ws.Cells["A9"].Style.Font.Bold = true;
            ws.Cells["A9"].Style.Font.Name = "Tahoma";

            ws.Cells["B9:D9"].Value = getAddress(quote);
            ws.Cells["B9:D9"].Merge = true;
            ws.Cells["B9:D9"].Style.WrapText = true;

            ws.Cells["A10"].Value = Functions.GetGlobalResource("Printing", "ATTENTION");
            ws.Cells["A10"].Style.Font.Bold = true;
            ws.Cells["A10"].Style.Font.Name = "Tahoma";
            ws.Cells["B10:D10"].Value = quote.ContactName;
            ws.Cells["B10:D10"].Merge = true;

            ws.Cells["A11"].Value = Functions.GetGlobalResource("Printing", "FROM");
            ws.Cells["A11"].Style.Font.Bold = true;
            ws.Cells["A11"].Style.Font.Name = "Tahoma";
            ws.Cells["B11:D11"].Value = quote.SalesmanName;
            ws.Cells["B11:D11"].Merge = true;

            ws.Cells["J9"].Value = Functions.GetGlobalResource("Printing", "DateQuoted");
            ws.Cells["J9"].Style.Font.Bold = true;
            ws.Cells["J9"].Style.Font.Name = "Tahoma";
            ws.Cells["J9"].Style.Font.Name = "Tahoma";
            ws.Cells["J9:K9"].Merge = true;
            ws.Cells["L9:N9"].Value = Functions.FormatDate(quote.DateQuoted);
            ws.Cells["L9:N9"].Merge = true;

            Company company = Company.Get(quote.CompanyNo);
            ws.Cells["J10"].Value = Functions.GetGlobalResource("Printing", "TEL");
            ws.Cells["J10"].Style.Font.Bold = true;
            ws.Cells["J10"].Style.Font.Name = "Tahoma";
            ws.Cells["J10:K10"].Merge = true;
            ws.Cells["L10:N10"].Value = company.Telephone;
            ws.Cells["L10:N10"].Merge = true;

            ws.Cells["J11"].Value = Functions.GetGlobalResource("Printing", "FAX");
            ws.Cells["J11"].Style.Font.Bold = true;
            ws.Cells["J11"].Style.Font.Name = "Tahoma";
            ws.Cells["J11:K11"].Merge = true;
            ws.Cells["L11:N11"].Value = company.Fax;
            ws.Cells["L11:N11"].Merge = true;

            ws.Cells["J12"].Value = Functions.GetGlobalResource("Printing", "TERMS");
            ws.Cells["J12"].Style.Font.Bold = true;
            ws.Cells["J12"].Style.Font.Name = "Tahoma";
            ws.Cells["J12:K12"].Merge = true;
            ws.Cells["L12:N12"].Value = quote.TermsName;
            ws.Cells["L12:N12"].Merge = true;

            ws.Cells["J13"].Value = Functions.GetGlobalResource("Printing", "JNCOTERMS");
            ws.Cells["J13"].Style.Font.Bold = true;
            ws.Cells["J13"].Style.Font.Name = "Tahoma";
            ws.Cells["J13:K13"].Merge = true;
            ws.Cells["L13:N13"].Value = quote.IncotermName;
            ws.Cells["L13:N13"].Merge = true;
            //set font size
            ws.Cells[fromRowIndex, fromColIndex, toRowIndex, toColIndex].Style.Font.Size = mediumFontSize;
        }
        private void AddMainHeaderDetailPurchaseOrder(ExcelWorksheet ws, PurchaseOrder purchaseOrder)
        {
            fromRowIndex = 9; fromColIndex = 1; toRowIndex = 16; toColIndex = 13;
            Address billToAddress = Company.GetDefaultBillingAddress(purchaseOrder.CompanyNo);
            string poText = Functions.GetGlobalResource("Printing", "PurchaseOrder") + " " + purchaseOrder.PurchaseOrderNumber;
            ws.Cells[7, 1].Value = poText;
            ws.Cells[7, 1, 8, toColIndex].Merge = true;
            ws.Cells[7, 1, 8, toColIndex].Style.Font.Bold = true;
            ws.Cells[7, 1, 8, toColIndex].Style.Font.UnderLine = true;
            ws.Cells[7, 1, 8, toColIndex].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            ws.Column(1).Width = 15;
            ws.Column(6).Width = 15;
            ws.Row(9).Height = 30;
            ws.Row(9).Style.VerticalAlignment = ExcelVerticalAlignment.Top;

            string billTo = Functions.GetGlobalResource("Printing", "LineHeader_Supplier");
            ws.Cells["A9"].Value = billTo;
            ws.Cells["A9"].Style.Font.Bold = true;
            ws.Cells["A9"].Style.Font.Name = "Tahoma";

            ws.Cells["B9:D9"].Value = purchaseOrder.CompanyName + Functions.ReplaceLineBreaks(AddressManager.ToLongString(billToAddress));
            ws.Cells["B9:D9"].Merge = true;
            ws.Cells["B9:D9"].Style.WrapText = true;

            Company company = Company.Get(purchaseOrder.CompanyNo);
            ws.Cells["A10"].Value = Functions.GetGlobalResource("Printing", "TEL");
            ws.Cells["A10"].Style.Font.Bold = true;
            ws.Cells["A10"].Style.Font.Name = "Tahoma";
            ws.Cells["B10:D10"].Value = company.Telephone;
            ws.Cells["B10:D10"].Merge = true;

            ws.Cells["A11"].Value = Functions.GetGlobalResource("Printing", "FAX");
            ws.Cells["A11"].Style.Font.Bold = true;
            ws.Cells["A11"].Style.Font.Name = "Tahoma";
            ws.Cells["B11:D11"].Value = company.Fax;
            ws.Cells["B11:D11"].Merge = true;

            ws.Cells["I11"].Value = Functions.GetGlobalResource("Printing", "VATNo");
            ws.Cells["I11"].Style.Font.Bold = true;
            ws.Cells["I11"].Style.Font.Name = "Tahoma";
            ws.Cells["I11:J11"].Merge = true;
            ws.Cells["K11:M11"].Value = purchaseOrder.VATNO;
            ws.Cells["K11:M11"].Merge = true;

            ws.Cells["A12"].Value = Functions.GetGlobalResource("Printing", "SUPPNO");
            ws.Cells["A12"].Style.Font.Bold = true;
            ws.Cells["A12"].Style.Font.Name = "Tahoma";
            ws.Cells["B12:D12"].Value = purchaseOrder.SupplierCode;
            ws.Cells["B12:D12"].Merge = true;

            ws.Cells["A13:M13"].Merge = true;

            ws.Cells["A14"].Value = Functions.GetGlobalResource("Printing", "Buyer");
            ws.Cells["A14"].Style.Font.Bold = true;
            ws.Cells["A14"].Style.Font.Name = "Tahoma";
            ws.Cells["B14:C14"].Value = purchaseOrder.BuyerName;
            ws.Cells["B14:C14"].Merge = true;

            ws.Cells["A15"].Value = Functions.GetGlobalResource("Printing", "DateOrdered");
            ws.Cells["A15"].Style.Font.Bold = true;
            ws.Cells["A15"].Style.Font.Name = "Tahoma";
            ws.Cells["B15:C15"].Value = Functions.FormatDate(purchaseOrder.DateOrdered);
            ws.Cells["B15:C15"].Merge = true;

            ws.Cells["A16"].Value = Functions.GetGlobalResource("Printing", "INCOTERMS");
            ws.Cells["A16"].Style.Font.Bold = true;
            ws.Cells["A16"].Style.Font.Name = "Tahoma";
            ws.Cells["B16:C16"].Value = purchaseOrder.IncotermName;
            ws.Cells["B16:C16"].Merge = true;

            ws.Cells["D14"].Value = Functions.GetGlobalResource("Printing", "Attention");
            ws.Cells["D14"].Style.Font.Bold = true;
            ws.Cells["D14"].Style.Font.Name = "Tahoma";
            ws.Cells["E14:F14"].Value = purchaseOrder.ContactName;
            ws.Cells["E14:F14"].Merge = true;

            ws.Cells["D15"].Value = Functions.GetGlobalResource("Printing", "ShipVia");
            ws.Cells["D15"].Style.Font.Bold = true;
            ws.Cells["D15"].Style.Font.Name = "Tahoma";
            ws.Cells["E15:F15"].Value = purchaseOrder.ShipViaName;
            ws.Cells["E15:F15"].Merge = true;

            ws.Cells["G14"].Value = Functions.GetGlobalResource("Printing", "FreeOnBoard");
            ws.Cells["G14"].Style.Font.Bold = true;
            ws.Cells["G14"].Style.Font.Name = "Tahoma";
            ws.Cells["H14:I14"].Value = purchaseOrder.FreeOnBoard;
            ws.Cells["H14:I14"].Merge = true;

            ws.Cells["G15"].Value = Functions.GetGlobalResource("Printing", "TERMS");
            ws.Cells["G15"].Style.Font.Bold = true;
            ws.Cells["G15"].Style.Font.Name = "Tahoma";
            ws.Cells["H15:I15"].Value = purchaseOrder.TermsName;
            ws.Cells["H15:I15"].Merge = true;

            ws.Cells["I9"].Value = Functions.GetGlobalResource("Printing", "BilledTo");
            ws.Cells["I9"].Style.Font.Bold = true;
            ws.Cells["I9"].Style.Font.Name = "Tahoma";
            ws.Cells["I9"].Style.Font.Name = "Tahoma";
            ws.Cells["I9:J9"].Merge = true;
            ws.Cells["K9:M9"].Value = purchaseOrder.ClientName + ' ' + purchaseOrder.BilledToAddress;
            ws.Cells["K9:M9"].Merge = true;
            ws.Cells["K9:M9"].Style.WrapText = true;


            ws.Cells["I10"].Value = Functions.GetGlobalResource("Printing", "ShipTo");
            ws.Cells["I10"].Style.Font.Bold = true;
            ws.Cells["I10"].Style.Font.Name = "Tahoma";
            ws.Cells["I10:J10"].Merge = true;
            ws.Cells["K10:M10"].Value = purchaseOrder.WarehouseName + ' ' + Functions.ReplaceLineBreaks(AddressManager.ToLongString(purchaseOrder.WarehouseAddress));
            ws.Cells["K10:M10"].Merge = true;
            ws.Cells["K10:M10"].Style.WrapText = true;

            //set font size
            ws.Cells[fromRowIndex, fromColIndex, toRowIndex, toColIndex].Style.Font.Size = mediumFontSize;
        }
        private void AddMainHeaderDetailSalesOrder(ExcelWorksheet ws, SalesOrder salesOrder)
        {
            fromRowIndex = 9; fromColIndex = 1; toRowIndex = 17; toColIndex = 13;
            Address billToAddress = Company.GetDefaultBillingAddress(salesOrder.CompanyNo);
            string poText = Functions.GetGlobalResource("Printing", "SalesOrder_print") + " " + salesOrder.SalesOrderNumber;
            ws.Cells[7, 1].Value = poText;
            ws.Cells[7, 1, 8, toColIndex].Merge = true;
            ws.Cells[7, 1, 8, toColIndex].Style.Font.Bold = true;
            ws.Cells[7, 1, 8, toColIndex].Style.Font.UnderLine = true;
            ws.Cells[7, 1, 8, toColIndex].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;

            ws.Column(1).Width = 15;
            ws.Column(6).Width = 15;
            ws.Row(9).Height = 30;
            ws.Row(9).Style.VerticalAlignment = ExcelVerticalAlignment.Top;

            string billTo = Functions.GetGlobalResource("Printing", "SoldTo");
            ws.Cells["A9"].Value = billTo;
            ws.Cells["A9"].Style.Font.Bold = true;
            ws.Cells["A9"].Style.Font.Name = "Tahoma";

            ws.Cells["B9:D9"].Value = Functions.ReplaceLineBreaks(AddressManager.ToLongString(billToAddress));
            ws.Cells["B9:D9"].Merge = true;
            ws.Cells["B9:D9"].Style.WrapText = true;

            Company company = Company.Get(salesOrder.CompanyNo);
            ws.Cells["A10"].Value = Functions.GetGlobalResource("Printing", "TEL");
            ws.Cells["A10"].Style.Font.Bold = true;
            ws.Cells["A10"].Style.Font.Name = "Tahoma";
            ws.Cells["B10:D10"].Value = company.Telephone;
            ws.Cells["B10:D10"].Merge = true;

            ws.Cells["A11"].Value = Functions.GetGlobalResource("Printing", "FAX");
            ws.Cells["A11"].Style.Font.Bold = true;
            ws.Cells["A11"].Style.Font.Name = "Tahoma";
            ws.Cells["B11:D11"].Value = company.Fax;
            ws.Cells["B11:D11"].Merge = true;

            ws.Cells["A12"].Value = salesOrder.dBillLabelTypeName;
            ws.Cells["A12"].Style.Font.Bold = true;
            ws.Cells["A12"].Style.Font.Name = "Tahoma";
            ws.Cells["B12:D12"].Merge = true;
            ws.Cells["B12:D12"].Value = salesOrder.VATNo;
            ws.Cells["B12:D12"].Merge = true;

            ws.Cells["A13"].Value = Functions.GetGlobalResource("Printing", "CustNo");
            ws.Cells["A13"].Style.Font.Bold = true;
            ws.Cells["A13"].Style.Font.Name = "Tahoma";
            ws.Cells["B13:D13"].Value = salesOrder.CustomerCode;
            ws.Cells["B13:D13"].Merge = true;

            ws.Cells["A15:M15"].Merge = true;

            ws.Cells["A16"].Value = Functions.GetGlobalResource("Printing", "Sales_Person");
            ws.Cells["A16"].Style.Font.Bold = true;
            ws.Cells["A16"].Style.Font.Name = "Tahoma";
            ws.Cells["B16:C16"].Value = salesOrder.SalesmanName;
            ws.Cells["B16:C16"].Merge = true;

            ws.Cells["A17"].Value = Functions.GetGlobalResource("Printing", "DateOrdered");
            ws.Cells["A17"].Style.Font.Bold = true;
            ws.Cells["A17"].Style.Font.Name = "Tahoma";
            ws.Cells["B17:C17"].Value = Functions.FormatDate(salesOrder.DateOrdered);
            ws.Cells["B17:C17"].Merge = true;

            ws.Cells["A14"].Value = Functions.GetGlobalResource("Printing", "INCOTERMS");
            ws.Cells["A14"].Style.Font.Bold = true;
            ws.Cells["A14"].Style.Font.Name = "Tahoma";
            ws.Cells["B14:D14"].Value = salesOrder.IncotermName;
            ws.Cells["B14:D14"].Merge = true;



            ws.Cells["D16"].Value = Functions.GetGlobalResource("Printing", "Buyer");
            ws.Cells["D16"].Style.Font.Bold = true;
            ws.Cells["D16"].Style.Font.Name = "Tahoma";
            ws.Cells["E16:F16"].Value = salesOrder.ContactName;
            ws.Cells["E16:F16"].Merge = true;

            ws.Cells["D17"].Value = Functions.GetGlobalResource("Printing", "ShipVia");
            ws.Cells["D17"].Style.Font.Bold = true;
            ws.Cells["D17"].Style.Font.Name = "Tahoma";
            ws.Cells["E17:F17"].Value = salesOrder.ShipViaName;
            ws.Cells["E17:F17"].Merge = true;

            ws.Cells["G16"].Value = Functions.GetGlobalResource("Printing", "CustomerPONumber");
            ws.Cells["G16"].Style.Font.Bold = true;
            ws.Cells["G16"].Style.Font.Name = "Tahoma";
            ws.Cells["H16:I16"].Value = salesOrder.CustomerPO;
            ws.Cells["H16:I16"].Merge = true;

            ws.Cells["G17"].Value = Functions.GetGlobalResource("Printing", "TERMS");
            ws.Cells["G17"].Style.Font.Bold = true;
            ws.Cells["G17"].Style.Font.Name = "Tahoma";
            ws.Cells["H17:I17"].Value = salesOrder.TermsName;
            ws.Cells["H17:I17"].Merge = true;

            ws.Cells["I9"].Value = Functions.GetGlobalResource("Printing", "ShipTo");
            ws.Cells["I9"].Style.Font.Bold = true;
            ws.Cells["I9"].Style.Font.Name = "Tahoma";
            ws.Cells["I9"].Style.Font.Name = "Tahoma";
            ws.Cells["I9:J9"].Merge = true;
            ws.Cells["K9:M9"].Value = Functions.ReplaceLineBreaks(AddressManager.ToLongString(billToAddress));
            ws.Cells["K9:M9"].Merge = true;
            ws.Cells["K9:M9"].Style.WrapText = true;

            //set font size
            ws.Cells[fromRowIndex, fromColIndex, toRowIndex, toColIndex].Style.Font.Size = mediumFontSize;
        }
        private string GetFooterNotes(SystemDocument.ListForPrint enmSystemDocument, int ClientNo)
        {
            string strNotes = "";
            BLL.SystemDocumentFooter ft = BLL.SystemDocumentFooter.GetForClientAndDocument((int)ClientNo, (int)enmSystemDocument);
            if (ft != null) strNotes = (String.IsNullOrEmpty(ft.FooterText)) ? "" : ft.FooterText;
            ft = null;
            return strNotes;
        }
        private string getAddress(Quote quote)
        {
            Address address = Company.GetDefaultBillingAddress(quote.CompanyNo);
            StringBuilder str = new StringBuilder();
            if (!string.IsNullOrEmpty(address.AddressName))
                str.Append(address.AddressName + System.Environment.NewLine);

            if (!string.IsNullOrEmpty(address.Line1))
                str.Append(address.Line1 + System.Environment.NewLine);

            if (!string.IsNullOrEmpty(address.Line2))
                str.Append(address.Line2 + System.Environment.NewLine);

            if (!string.IsNullOrEmpty(address.Line3))
                str.Append(address.Line3 + System.Environment.NewLine);

            if (!string.IsNullOrEmpty(address.City))
                str.Append(address.City + System.Environment.NewLine);

            if (!string.IsNullOrEmpty(address.County))
                str.Append(address.County + System.Environment.NewLine);

            if (!string.IsNullOrEmpty(address.State))
                str.Append(address.State + System.Environment.NewLine);

            if (!string.IsNullOrEmpty(address.ZIP))
                str.Append(address.ZIP + System.Environment.NewLine);

            if (!string.IsNullOrEmpty(address.CountryName))
                str.Append(address.CountryName);

            return str.ToString();
        }
        private void AddImage(ExcelWorksheet ws, int columnStartIndex, int rowStartIndex, int columnEndIndex, int roEnddIndex, string filePath)
        {
            if (!string.IsNullOrEmpty(filePath))
            {
                string sasURL = AzureBlobSA.GetSasUrl("gtdocmgmt");
                string bothirl = AzureBlobSA.GetSasBlobUrl("gtdocmgmt", filePath, sasURL, "docheaders");
                Uri blobUr = new Uri(bothirl);
                CloudBlockBlob blob = new CloudBlockBlob(blobUr);
                using (MemoryStream memoryStream = new MemoryStream())
                {
                    blob.DownloadToStream(memoryStream);

                    //if (File.Exists(logopath))

                    //How to Add a Image using EP Plus
                    Bitmap image = new Bitmap(memoryStream);
                    ExcelPicture picture = null;
                    if (image != null)
                    {
                        picture = ws.Drawings.AddPicture("pic" + rowStartIndex + columnStartIndex.ToString(), image);
                        picture.From.Column = columnStartIndex;
                        picture.From.Row = rowStartIndex;
                        picture.From.ColumnOff = Pixel2MTU(columnEndIndex); //Two pixel space for better alignment
                        picture.From.RowOff = Pixel2MTU(roEnddIndex);//Two pixel space for better alignment
                        picture.SetSize(500, 119);
                    }
                }
                ws.Cells[1, 1, 6, 14].Merge = true;//[int FromRow, int FromCol, int ToRow, int ToCol]
                ws.Cells[1, 1, 6, 14].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                //ws.Cells[1, 1, 3, 8].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
            }
        }
        private ExcelWorksheet CreateSheet(ExcelPackage p, string sheetName)
        {
            p.Workbook.Worksheets.Add(sheetName);
            ExcelWorksheet ws = p.Workbook.Worksheets[1];
            ws.Name = sheetName; //Setting Sheet's name
            ws.Cells.Style.Font.Size = 11; //Default font size for whole sheet
            ws.Cells.Style.Font.Name = "Calibri"; //Default Font name for whole sheet

            return ws;
        }
        private int Pixel2MTU(int pixels)
        {
            int mtus = pixels * 9525;
            return mtus;
        }
        private string GetHazardousNotes(SystemDocument.FooterDocument enmSystemDocument, int ClientNo)
        {
            string strNotes = "";
            BLL.SystemDocumentFooter ft = BLL.SystemDocumentFooter.GetForClientAndDocument((int)ClientNo, (int)enmSystemDocument);
            if (ft != null) strNotes = (String.IsNullOrEmpty(ft.FooterText)) ? "" : ft.FooterText;
            ft = null;
            return strNotes;
        }
        private string GetFormattedPartForPrint(string strPart, int? intROHS)
        {
            string strPartROHS = "";
            switch (intROHS)
            {
                case 0:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSUnknown"));
                    break;
                case 1:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSCompliant"));
                    break;
                case 2:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSNonCompliant"));
                    break;
                case 3:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSExempt"));
                    break;
                case 4:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSNotApplicable"));
                    break;
                case 5:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSROHS2"));
                    break;
                case 6:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSROHS56"));
                    break;
                case 7:
                    strPartROHS = string.Format(" ({0})", Functions.GetGlobalResource("Misc", "ROHSROHS66"));
                    break;
                default:
                    strPartROHS = string.Empty;
                    break;

            }
            string strPartOut = string.Format("{0}{1}", strPart.Trim(), strPartROHS);
            return strPartOut;
        }

        private string GetFormattedROHSForPrint(int? intROHS)
        {
            string strPartROHS = "";
            switch(intROHS)
            {
                case 0:
                    strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSUnknown"));
                    break;
                case 1:
                    strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSCompliant"));
                    break;
                case 2:
                    strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSNonCompliant"));
                    break;
                case 3:
                    strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSExempt"));
                    break;
                case 4:
                    strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSNotApplicable"));
                    break;
                case 5:
                    strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSROHS2"));
                    break;
                case 6:
                    strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSROHS56"));
                    break;
                case 7:
                    strPartROHS = string.Format("{0}", Functions.GetGlobalResource("Misc", "ROHSROHS66"));
                    break;
                default:
                    strPartROHS = string.Empty;
                    break;

            }
            string strPartOut = string.Format("{0}", strPartROHS);
            return strPartOut;
        }

        private string GetFormattedProductSourceForPrint(bool As9120, int? intProductSource)
        {
            string strPartROHSProductSource = "";
            ProductSource obj = ProductSource.Get(intProductSource);
            if (obj != null)
            {
                strPartROHSProductSource = string.Format("[{0}]", obj.Name);
                //switch (intProductSource)
                //{

                //    case 1:
                //        strPartROHSProductSource = string.Format("[{0}]", Functions.GetGlobalResource("Misc", "NonPreferred"));
                //        break;
                //    case 2:
                //        strPartROHSProductSource = string.Format("[{0}]", Functions.GetGlobalResource("Misc", "Traceable"));
                //        break;
                //    case 3:
                //        strPartROHSProductSource = string.Format("[{0}]", Functions.GetGlobalResource("Misc", "Trusted"));
                //        break;
                //    default:
                //        strPartROHSProductSource = string.Empty;
                //        break;

                //}
            }
            string strPartOut = string.Empty;
            if (!string.IsNullOrEmpty(strPartROHSProductSource) && As9120 == true)
                strPartOut = strPartROHSProductSource;
            else
                strPartOut = strPartOut.Trim();
            return strPartOut;
        }
        //private string GetFormattedProductSourceForPrint(bool As9120, int? intProductSource)
        //{
        //    string strPartROHSProductSource = "";
        //    switch (intProductSource)
        //    {
        //        case 1:
        //            strPartROHSProductSource = string.Format("[{0}]", Functions.GetGlobalResource("Misc", "NonPreferred"));
        //            break;
        //        case 2:
        //            strPartROHSProductSource = string.Format("[{0}]", Functions.GetGlobalResource("Misc", "Traceable"));
        //            break;
        //        case 3:
        //            strPartROHSProductSource = string.Format("[{0}]", Functions.GetGlobalResource("Misc", "Trusted"));
        //            break;
        //        default:
        //            strPartROHSProductSource = string.Empty;
        //            break;

        //    }
        //    string strPartOut = string.Empty;
        //    if (!string.IsNullOrEmpty(strPartROHSProductSource) && As9120 == true)
        //        strPartOut = strPartROHSProductSource;
        //    else
        //        strPartOut = strPartOut.Trim();
        //    return strPartOut;
        //}
        private string GetFooterNotes(SystemDocument.ListForPrint enmSystemDocument)
        {
            string strNotes = "";
            BLL.SystemDocumentFooter ft = BLL.SystemDocumentFooter.GetForClientAndDocument((int)SessionManager.ClientID, (int)enmSystemDocument);
            if (ft != null) strNotes = (String.IsNullOrEmpty(ft.FooterText)) ? "" : ft.FooterText;
            ft = null;
            return strNotes;
        }
        public string ExportDataTableToCSV(DataTable dt, string filename, string sheetName = "Result")
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = filename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage pck = new ExcelPackage(fileInfo))
                {
                    ExcelWorksheet ws = pck.Workbook.Worksheets.Add(sheetName);
                    ws.Cells["A1"].LoadFromDataTable(dt, true);
                    pck.Save();
                }
            }
            catch (Exception)
            {

            }
            return filepath;
        }

        public string ExportDataTableToExcelHUBRFQ(DataTable dt, string filename)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = filename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage pck = new ExcelPackage(fileInfo))
                {
                    ExcelWorksheet ws = pck.Workbook.Worksheets.Add("HUBRFQResult");
                    AddTableHeaderHUBRFQ(ws);
                    ws.Cells["A2"].LoadFromDataTable(dt, true);
                    pck.Save();
                }
            }
            catch (Exception ex)
            {

            }
            return filepath;
        }

        private void AddTableHeaderHUBRFQ(ExcelWorksheet ws)
        {
            ws.Row(1).Height = 50;
            ws.Row(1).Style.VerticalAlignment = ExcelVerticalAlignment.Top;
            ws.Row(1).Style.WrapText = true;

            StringBuilder sb = new StringBuilder();
            sb.Append("Note: Please do not modify the information highlighted in blue (A to G).\n");
            sb.Append("- If no bid is available, update the Price column with \"N/A\".\n");
            sb.Append("- If multiple sourcing entries exist for a single Requirement, copy the corresponding HUBRFQ row (blue-highlighted) and input the sourcing details accordingly.");

            ws.Cells["A1:AE1"].Merge = true;
            ws.Cells["A1:AE1"].Style.Font.Name = "Tahoma,Arial,Sans-serif";
            ws.Cells["A1:AE1"].Value = sb.ToString();
            ws.Cells["A1:AE1"].Style.Font.Color.SetColor(System.Drawing.ColorTranslator.FromHtml("#f36262"));

            ws.Cells["A2:G2"].Style.Font.Bold = true;
            ws.Cells["A2:G2"].Style.Font.Name = "Tahoma,Arial,Sans-serif";
            ws.Cells["A2:G2"].Style.Fill.PatternType = ExcelFillStyle.Solid;
            ws.Cells["A2:G2"].Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#76b2f1"));
            ws.Cells["A2:G2"].Style.Font.Size = mediumFontSize;

            ws.Cells["H2:AE2"].Style.Font.Bold = true;
            ws.Cells["H2:AE2"].Style.Font.Name = "Tahoma,Arial,Sans-serif";
            ws.Cells["H2:AE2"].Style.Fill.PatternType = ExcelFillStyle.Solid;
            ws.Cells["H2:AE2"].Style.Fill.BackgroundColor.SetColor(System.Drawing.ColorTranslator.FromHtml("#b3eca7"));
            ws.Cells["H2:AE2"].Style.Font.Size = mediumFontSize;
        }

        public string ExportDataTableToCSVOGEL(DataTable dt, string filename)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = filename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage pck = new ExcelPackage(fileInfo))
                {
                    ExcelWorksheet ws = pck.Workbook.Worksheets.Add("OGEL Lines");
                    ws.Cells["A1"].LoadFromDataTable(dt, true);
                    pck.Save();
                }
            }
            catch (Exception)
            {

            }
            return filepath;
        }
        public string ExportDataTableToCSVIHS(DataTable dt, string filename)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = filename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage pck = new ExcelPackage(fileInfo))
                {
                    ExcelWorksheet ws = pck.Workbook.Worksheets.Add("IHSResult");
                    ws.Cells["A1"].LoadFromDataTable(dt, true);
                    pck.Save();
                }
            }
            catch (Exception)
            {

            }
            return filepath;
        }

        public string ExportDataTableToExcel(DataTable dtr, DataTable dtrl, string filename)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = filename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage pck = new ExcelPackage(fileInfo))
                {
                    ExcelWorksheet wscrossmatchrequest = pck.Workbook.Worksheets.Add("CrossMatch_Requirement");
                    ExcelWorksheet wscrossmatchrequestlist = pck.Workbook.Worksheets.Add("CrossMatch_Requirement_Sourcing");

                    wscrossmatchrequest.Cells["A1"].LoadFromDataTable(dtr, true);
                    wscrossmatchrequestlist.Cells["A1"].LoadFromDataTable(dtrl, true);

                    pck.Save();
                }
            }
            catch (Exception)
            {

            }
            return filepath;
        }
        public string ExportDataTableToCSVShipSO(DataTable dt, string filename, string TabName)
        {
            string filepath = string.Empty;
            try
            {
                string strFilename = filename;
                filepath = FileUploadManager.GetCSVFilePath_Relative(false) + "/" + strFilename;//FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                string fullFilePath = FileUploadManager.GetCSVFilePath_Absolute() + "/" + strFilename;
                #region Create Empty File
                FileInfo fileInfo = new FileInfo(fullFilePath);


                #endregion
                if (File.Exists(fullFilePath))
                {
                    File.Delete(fullFilePath);
                }
                using (ExcelPackage pck = new ExcelPackage(fileInfo))
                {
                    ExcelWorksheet ws = pck.Workbook.Worksheets.Add(TabName);
                    ws.Cells["A1"].LoadFromDataTable(dt, true);
                    pck.Save();
                }
            }
            catch (Exception)
            {

            }
            return filepath;
        }
        #endregion

    }
}