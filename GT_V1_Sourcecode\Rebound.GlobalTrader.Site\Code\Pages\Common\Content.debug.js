///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");

Rebound.GlobalTrader.Site.Pages.Content = function(element) { 
	Rebound.GlobalTrader.Site.Pages.Content.initializeBase(this, [element]);
	this._intTimeoutTopIcons = -1;
	this._blnLeftPanelVisible = false;
	this._blnAlertsOpen = false;
	this._blnMessagesOpen = false;
	this._blnModalBackgroundOnOriginally = false;
	this._objMessages = {};
};

Rebound.GlobalTrader.Site.Pages.Content.prototype = {

	get_bdyBody: function() { return this._bdyBody; }, 	set_bdyBody: function(v) { if (this._bdyBody !== v)  this._bdyBody = v; }, 
	get_strPageTheme: function() { return this._strPageTheme; }, 	set_strPageTheme: function(v) { if (this._strPageTheme !== v)  this._strPageTheme = v; }, 
	get_ancCloseLeft: function() { return this._ancCloseLeft; }, 	set_ancCloseLeft: function(v) { if (this._ancCloseLeft !== v)  this._ancCloseLeft = v; }, 
	get_pnlLeftContent: function() { return this._pnlLeftContent; }, 	set_pnlLeftContent: function(v) { if (this._pnlLeftContent !== v)  this._pnlLeftContent = v; }, 
	get_pnlLeftButton: function() { return this._pnlLeftButton; }, 	set_pnlLeftButton: function(v) { if (this._pnlLeftButton !== v)  this._pnlLeftButton = v; }, 
	get_pnlShadow: function() { return this._pnlShadow; }, 	set_pnlShadow: function(v) { if (this._pnlShadow !== v)  this._pnlShadow = v; }, 
	get_blnLeftPanelVisible: function() { return this._blnLeftPanelVisible; }, 	set_blnLeftPanelVisible: function(v) { if (this._blnLeftPanelVisible !== v)  this._blnLeftPanelVisible = v; }, 
	get_pnlNewMessages: function() { return this._pnlNewMessages; }, 	set_pnlNewMessages: function(v) { if (this._pnlNewMessages !== v)  this._pnlNewMessages = v; }, 
	get_lblNewMessagesTitle: function() { return this._lblNewMessagesTitle; }, 	set_lblNewMessagesTitle: function(v) { if (this._lblNewMessagesTitle !== v)  this._lblNewMessagesTitle = v; }, 
	get_tblNewMessages: function() { return this._tblNewMess3ages; }, 	set_tblNewMessages: function(v) { if (this._tblNewMessages !== v)  this._tblNewMessages = v; }, 
	get_hypCloseNewMessages: function() { return this._hypCloseNewMessages; }, 	set_hypCloseNewMessages: function(v) { if (this._hypCloseNewMessages !== v)  this._hypCloseNewMessages = v; }, 
	get_blnPageShouldCheckMessages: function() { return this._blnPageShouldCheckMessages; }, 	set_blnPageShouldCheckMessages: function(v) { if (this._blnPageShouldCheckMessages !== v)  this._blnPageShouldCheckMessages = v; }, 
	get_pnlToDoAlert: function() { return this._pnlToDoAlert; }, 	set_pnlToDoAlert: function(v) { if (this._pnlToDoAlert !== v)  this._pnlToDoAlert = v; }, 
	get_tblAlerts: function() { return this._tblAlerts; }, 	set_tblAlerts: function(v) { if (this._tblAlerts !== v)  this._tblAlerts = v; }, 
	get_ibtnOpenItem: function() { return this._ibtnOpenItem; }, 	set_ibtnOpenItem: function(v) { if (this._ibtnOpenItem !== v)  this._ibtnOpenItem = v; }, 
	get_ibtnDismiss: function() { return this._ibtnDismiss; }, 	set_ibtnDismiss: function(v) { if (this._ibtnDismiss !== v)  this._ibtnDismiss = v; }, 
	get_ibtnSnooze: function() { return this._ibtnSnooze; }, 	set_ibtnSnooze: function(v) { if (this._ibtnSnooze !== v)  this._ibtnSnooze = v; }, 
	get_pnlMainArea: function() { return this._pnlMainArea; }, 	set_pnlMainArea: function(v) { if (this._pnlMainArea !== v)  this._pnlMainArea = v; }, 
	get_divMainArea: function() { return this._divMainArea; }, 	set_divMainArea: function(v) { if (this._divMainArea !== v)  this._divMainArea = v; }, 
	get_pnlTitleBar: function() { return this._pnlTitleBar; }, 	set_pnlTitleBar: function(v) { if (this._pnlTitleBar !== v)  this._pnlTitleBar = v; }, 
	get_objBackgroundImages: function() { return this._objBackgroundImages; }, 	set_objBackgroundImages: function(v) { if (this._objBackgroundImages !== v)  this._objBackgroundImages = v; }, 
	get_ddlSnoozeTime: function() { return this._ddlSnoozeTime; }, 	set_ddlSnoozeTime: function(v) { if (this._ddlSnoozeTime !== v)  this._ddlSnoozeTime = v; }, 
	get_ctlToolTip: function() { return this._ctlToolTip; }, 	set_ctlToolTip: function(v) { if (this._ctlToolTip !== v)  this._ctlToolTip = v; }, 
	get_pnlIcons: function() { return this._pnlIcons; }, 	set_pnlIcons: function(v) { if (this._pnlIcons !== v)  this._pnlIcons = v; }, 
    get_hypProfile: function () { return this._hypProfile; }, set_hypProfile: function (v) { if (this._hypProfile !== v) this._hypProfile = v; }, 
    //Espire 14 Nov 2019
    get_ddlClientByMaster: function () { return this._ddlClientByMaster; }, set_ddlClientByMaster: function (v) { if (this._ddlClientByMaster !== v) this._ddlClientByMaster = v; },
    get_intMasterLoginNo: function () { return this._intMasterLoginNo; }, set_intMasterLoginNo: function (v) { if (this._intMasterLoginNo !== v) this._intMasterLoginNo = v; },
    get_intDefaultClientNo: function () { return this._intDefaultClientNo; }, set_intDefaultClientNo: function (v) { if (this._intDefaultClientNo !== v) this._intDefaultClientNo = v; },

	addCheckLoggedIn: function(handler) { this.get_events().addHandler("CheckLoggedIn", handler); },
	removeCheckLoggedIn: function(handler) { this.get_events().removeHandler("CheckLoggedIn", handler); },
	onCheckLoggedIn: function() {
		var handler = this.get_events().getHandler("CheckLoggedIn");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addGetAlerts: function(handler) { this.get_events().addHandler("GetAlerts", handler); },
	removeGetAlerts: function(handler) { this.get_events().removeHandler("GetAlerts", handler); },
	onGetAlerts: function() {
		var handler = this.get_events().getHandler("GetAlerts");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addSnoozeAlert: function(handler) { this.get_events().addHandler("SnoozeAlert", handler); },
	removeSnoozeAlert: function(handler) { this.get_events().removeHandler("SnoozeAlert", handler); },
	onSnoozeAlert: function() {
		var handler = this.get_events().getHandler("SnoozeAlert");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addDismissAlert: function(handler) { this.get_events().addHandler("DismissAlert", handler); },
	removeDismissAlert: function(handler) { this.get_events().removeHandler("DismissAlert", handler); },
	onDismissAlert: function() {
		var handler = this.get_events().getHandler("DismissAlert");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addGotoAlert: function(handler) { this.get_events().addHandler("GotoAlert", handler); },
	removeGotoAlert: function(handler) { this.get_events().removeHandler("GotoAlert", handler); },
	onGotoAlert: function() {
		var handler = this.get_events().getHandler("GotoAlert");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	addCheckForMessages: function(handler) { this.get_events().addHandler("CheckForMessages", handler); },
	removeCheckForMessages: function(handler) { this.get_events().removeHandler("CheckForMessages", handler); },
	onCheckForMessages: function() {
	    var handler = this.get_events().getHandler("CheckForMessages");
	  	if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addAddMailMessages: function(handler) { this.get_events().addHandler("AddMailMessages", handler); },
	removeAddMailMessages: function(handler) { this.get_events().removeHandler("AddMailMessages", handler); },
	onAddMailMessages: function() {
		var handler = this.get_events().getHandler("AddMailMessages");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	initialize: function() {
		this._objBackgroundImages = Sys.Serialization.JavaScriptSerializer.deserialize(this._objBackgroundImages);
		$addHandler(this._ancCloseLeft, "click", Function.createDelegate(this, this.toggleLeftBar));
		this._intLoginTimeoutID = setTimeout(Function.createDelegate(this, this.logout), ($R_LOGIN_TIMEOUT * 60 * 1000));
		this.onCheckLoggedIn();
		
		if (this._blnPageShouldCheckMessages) {
			this.checkForMessages();
			this._tblAlerts.addSelectedIndexChanged(Function.createDelegate(this, this.selectAlert));
			$R_IBTN.addClick(this._ibtnOpenItem, Function.createDelegate(this, this.gotoAlert));
			$R_IBTN.addClick(this._ibtnDismiss, Function.createDelegate(this, this.dismissAlert));
			$R_IBTN.addClick(this._ibtnSnooze, Function.createDelegate(this, this.snoozeAlert));
			$addHandler(this._hypCloseNewMessages, "click", Function.createDelegate(this, this.closeNewMessages));
		}
		$addHandler(window, "resize", Function.createDelegate(this, this.windowResize));
		this.addCheckLoggedIn(Function.createDelegate(this, this.doCheckLoggedIn));
		this.addGetAlerts(Function.createDelegate(this, this.doGetAlerts));
		this.addSnoozeAlert(Function.createDelegate(this, this.doSnoozeAlert));
		this.addDismissAlert(Function.createDelegate(this, this.doDismissAlert));
		this.addGotoAlert(Function.createDelegate(this, this.doGotoAlert));
		this.addCheckForMessages(Function.createDelegate(this, this.doCheckForMessages));
		
		this.addAddMailMessages(Function.createDelegate(this, this.doAddMessages));
		$addHandler(this._pnlIcons, "mouseover", Function.createDelegate(this, this.showTopIcons));
		$addHandler(this._hypProfile, "mouseover", Function.createDelegate(this, this.showTopIcons));
		$addHandler(this._pnlIcons, "mouseout", Function.createDelegate(this, this.hideTopIcons));
        $addHandler(this._hypProfile, "mouseout", Function.createDelegate(this, this.hideTopIcons));

        //14 Nov 2019: SSO
        //alert(this._intMasterLoginNo);
        this._ddlClientByMaster.clearDropDown();
        this._ddlClientByMaster._intMasterLoginNo = this._intMasterLoginNo;
        this._ddlClientByMaster.getData();
        this._ddlClientByMaster.addChanged(Function.createDelegate(this, this.LoginWithOtherClient));
        //var fn = this.setDropdownValue();
        //setTimeout(fn, 6000);
        setTimeout(Function.createDelegate(this, this.setDropdownValue), 800);
		Rebound.GlobalTrader.Site.Pages.Content.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		//do nothing - handled by subclasses
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this.clearTimeout();
		if (this._pnlIcons) $clearHandlers(this._pnlIcons);
		if (this._hypProfile) $clearHandlers(this._hypProfile);
		if (this._element) $clearHandlers(this._element);
		if (window) $clearHandlers(window);
		if (this._ibtnOpenItem) $R_IBTN.clearHandlers(this._ibtnOpenItem);
		if (this._ibtnDismiss) $R_IBTN.clearHandlers(this._ibtnDismiss);
		if (this._ibtnSnooze) $R_IBTN.clearHandlers(this._ibtnSnooze);
		if (this._hypCloseNewMessages) $clearHandlers(this._hypCloseNewMessages);
		if (this._ancCloseLeft) $clearHandlers(this._ancCloseLeft);
		if (this._tblNewMessages) this._tblNewMessages.dispose();
		if (this._tblAlerts) this._tblAlerts.dispose();
		if (this._ddlSnoozeTime) this._ddlSnoozeTime.dispose();
		if (this._ctlToolTip) this._ctlToolTip.dispose();
		this._strPageTheme = null;
		this._ancCloseLeft = null;
		this._pnlLeftContent =  null;
		this._pnlLeftButton =  null;
		this._pnlShadow =  null;
		this._blnLeftPanelVisible = null;
		this._pnlNewMessages = null;
		this._lblNewMessagesTitle = null;
		this._tblNewMessages = null;
		this._hypCloseNewMessages = null;
		this._blnPageShouldCheckMessages = null;
		this._pnlToDoAlert = null;
		this._tblAlerts = null;
		this._ibtnOpenItem = null;
		this._ibtnDismiss = null;
		this._ibtnSnooze = null;
		this._pnlMainArea = null;
		this._divMainArea = null;
		this._pnlTitleBar = null;
		this._objBackgroundImages = null;
		this._ddlSnoozeTime = null;
		this._bdyBody = null;
		this._ctlToolTip = null;
		this._hypProfile = null;
		this._pnlIcons = null;
        this._intTimeoutTopIcons = null;
        this._ddlClientByMaster = null;
		Rebound.GlobalTrader.Site.Pages.Content.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	showTopIcons: function() {
		this.clearTimeout();
		$R_FN.showElement(this._pnlIcons, true);
	},
	
	hideTopIcons: function() {
		var strID = this._element.id;
		var fn = function() { $find(strID).finishHideTopIcons(); };
		this._intTimeoutTopIcons = setTimeout(fn, 50);
	},
	
	finishHideTopIcons: function() {
		this.clearTimeout();
		$R_FN.showElement(this._pnlIcons, false);
	},

	toggleLeftBar: function() {
		this._blnLeftPanelVisible = !this._blnLeftPanelVisible;
		this._pnlMainArea.className = (this._blnLeftPanelVisible) ? "mainArea_On" : "mainArea_Off";
		this._pnlLeftContent.className = (this._blnLeftPanelVisible) ? "leftbarContent" : "invisible";
		this._pnlLeftButton.className = (this._blnLeftPanelVisible) ? "leftbarButton_On" : "leftbarButton_Off";
		this._pnlShadow.className = (this._blnLeftPanelVisible) ? "invisible" : "mainAreaShadow";
		this.resizeAllTables();
		Rebound.GlobalTrader.Site.WebServices.SetLeftPanelVisibleSession(this._blnLeftPanelVisible);
	},
	
	windowResize: function() {
		this.resizeAllTables();
	},
	
	resizeAllTables: function() {
		var aryComponents = Sys.Application.getComponents();
		for (var i = 0, l = aryComponents.length; i < l; i++) {
			if (aryComponents[i].ControlType_FlexiDataTable) aryComponents[i].resizeColumns();
		}
		aryComponents = null;
	},
	
	checkLoginSuccess: function(result) {
		if (!result) this.logout();
	},
	
	checkLoginFailed: function() {
		this.logout();
	},
	
	logout: function() {
		this.stopCheckForMessages();
		clearTimeout(this._intLoginTimeoutID);
		location.href = String.format("{0}?ret={1}", $R_URL_Logout, escape(location.href));
	},
	
	startCheckForMessages: function() {
		this.stopCheckForMessages();
		this._intMessageCheckTimeoutID = setTimeout(Function.createDelegate(this, this.checkForMessages), ($R_MESSAGE_CHECK_TIMEOUT * 60 * 1000));
	},
	
	stopCheckForMessages: function(){
		clearTimeout(this._intMessageCheckTimeoutID);
	},
	
	checkForMessages: function() {
		this.stopCheckForMessages();
		if (!this._blnMessagesOpen) this.onCheckForMessages();
	},
	
	checkForMessagesSuccess: function(strResult) {
		this.startCheckForMessages();
		if (strResult != "") {
			var objResult = Sys.Serialization.JavaScriptSerializer.deserialize(strResult);

			if (objResult.LOGGED_OUT == "") {
				location.href = $R_URL_Logout;
				return;
			}
			
			//Messages
			if (objResult.Messages && this._blnPageShouldCheckMessages) {
				this._tblNewMessages.clearTable();
				this._objMessages = objResult.Messages;
				this.onAddMailMessages();
				this._objMessages = null;
				if (objResult.Messages.length > 0) {
					this._tblNewMessages.resizeColumns();
					$R_FN.showElement(this._pnlNewMessages, true);
					this._blnMessagesOpen = true;
					if ($R_SHOW_MESSAGE_ALERT) alert(this._lblNewMessagesTitle.innerHTML);
				}
			}
			
			//To Do Alerts
			if (objResult.Alerts && !this._blnAlertsOpen) this.populateAlerts(objResult.Alerts);
		}
	},
	
	getAlerts: function() {
		this.onGetAlerts();
	},

	getAlertsComplete: function(strResult) {
		if (strResult != "") {
			var objResult = Sys.Serialization.JavaScriptSerializer.deserialize(strResult);
			if (objResult.Alerts) this.populateAlerts(objResult.Alerts);
		}
	},
		
	populateAlerts: function(obj) {
		this.enableAlertButtons(false);
		this._tblAlerts.clearTable();
		for (var i = 0, l = obj.length; i < l; i++) {
			var td = obj[i];
			var aryData = [
				$R_FN.setCleanTextValue(td.Text),
				td.DueDate
			];
			this._tblAlerts.addRow(aryData, td.ID, false);
		}
		if (obj.length > 0) {
			this._tblAlerts.resizeColumns();
			if (!this._blnAlertsOpen) {
				$R_FN.showElement(this._pnlToDoAlert, true);
				var intY = Math.max(175, $R_FN.windowScrollPosition() + 25);
				this._pnlToDoAlert.style.top = String.format('{0}px', intY);
				this._blnModalBackgroundOnOriginally = $R_FN.isElementVisible($get('modalBG'));
				$R_FN.showModalBG(true);
			}
			this._blnAlertsOpen = true;
		} else {
			this.closeAlerts();
		}
	},
	
	closeAlerts: function() {
		if (this._blnAlertsOpen) {
			if (!this._blnModalBackgroundOnOriginally) $R_FN.showModalBG(false);
			this._blnAlertsOpen = false;
			$R_FN.showElement(this._pnlToDoAlert, false);
		}
	},
	
	checkForMessagesFailed: function(){
		this.startCheckForMessages();
	},
	
	selectAlert: function() {
		this.enableAlertButtons(true);
	},
	
	enableAlertButtons: function(bln) {
		if (bln) {
			$R_IBTN.enableButton(this._ibtnOpenItem, this._tblAlerts._varSelectedValue > 0);
			$R_IBTN.enableButton(this._ibtnDismiss, this._tblAlerts._varSelectedValue > 0);
			$R_IBTN.enableButton(this._ibtnSnooze, this._tblAlerts._varSelectedValue > 0);
		} else {
			$R_IBTN.enableButton(this._ibtnOpenItem, false);
			$R_IBTN.enableButton(this._ibtnDismiss, false);
			$R_IBTN.enableButton(this._ibtnSnooze, false);
		}
	},
	
	gotoAlert: function() {
		this.onGotoAlert();
	},	
		
	dismissAlert: function() {
		this.onDismissAlert();
	},

	dismissAlertComplete: function() {
		this.getAlerts();
	},
	
	snoozeAlert: function() {
		this.onSnoozeAlert();
	},

	snoozeAlertComplete: function() {
		this.getAlerts();
	},
	
	changeBackgroundImage: function(strImage) {
		this._bdyBody.style.backgroundImage = String.format("url({0})", this._objBackgroundImages[strImage.toLowerCase()]);
	},
	
	closeNewMessages: function() {
		$R_FN.showElement(this._pnlNewMessages, false);
	},

	doCheckLoggedIn: function() {
		Rebound.GlobalTrader.Site.WebServices.CheckLoggedIn(Function.createDelegate(this, this.checkLoginSuccess), Function.createDelegate(this, this.checkLoginFailed));
	},
	
	doGetAlerts: function() {
		Rebound.GlobalTrader.Site.WebServices.CheckForAlerts(Function.createDelegate(this, this.getAlertsComplete));
	},
	
	doSnoozeAlert: function() {
		Rebound.GlobalTrader.Site.WebServices.SnoozeAlert(this._tblAlerts._varSelectedValue, this._ddlSnoozeTime.getValue(), Function.createDelegate(this, this.snoozeAlertComplete));
	},
	
	doDismissAlert: function() {
		Rebound.GlobalTrader.Site.WebServices.DismissAlert(this._tblAlerts._varSelectedValue, Function.createDelegate(this, this.dismissAlertComplete));
	},
		
	doGotoAlert: function() {
		location.href = $RGT_gotoURL_ToDo(this._tblAlerts._varSelectedValue);
	},	

	doCheckForMessages: function() {

		Rebound.GlobalTrader.Site.WebServices.CheckForMessages(Function.createDelegate(this, this.checkForMessagesSuccess), Function.createDelegate(this, this.checkForMessagesFailed));
	},
	
	doAddMessages: function() {
		for (var i = 0, l = this._objMessages.length; i < l; i++) {
			var row = this._objMessages[i];
			var aryData = [
				$R_FN.setCleanTextValue(row.From)
				, $RGT_nubButton_MailMessage(row.ID, row.Subject)
				, row.DateSent
			];
			this._tblNewMessages.addRow(aryData, row.ID, false, null, "newMessage");
			row = null;
		}
	},
	
	clearTimeout: function() {
		if (this._intTimeoutTopIcons != -1) clearTimeout(this._intTimeoutTopIcons);
    },
    LoginWithOtherClient: function () {
        //  alert("Hi");
        //this.stopCheckForMessages();
        //clearTimeout(this._intLoginTimeoutID);
        location.href = String.format("{0}?ret={1}&CID={2}", "LoginWithOtherClient.aspx", escape(location.href), this._ddlClientByMaster.getValue());
    },
    setDropdownValue: function () {
        //alert(this._intDefaultClientNo);
        this._ddlClientByMaster.setValue(this._intDefaultClientNo);
    }
   
	
};

Rebound.GlobalTrader.Site.Pages.Content.registerClass("Rebound.GlobalTrader.Site.Pages.Content", Sys.UI.Control, Sys.IDisposable);
