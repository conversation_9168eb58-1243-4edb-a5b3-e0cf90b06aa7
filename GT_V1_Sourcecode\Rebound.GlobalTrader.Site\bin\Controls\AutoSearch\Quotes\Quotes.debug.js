﻿///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes = function (element) {
	Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.prototype = {

	initialize: function () {
		Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
		this.setupDataObject("Quotes");
	},

	dispose: function () {
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.callBaseMethod(this, "dispose");
	},

	dataReturned: function () {
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				var strHTML = $R_FN.setCleanTextValue(res.QuoteNumber);
				this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.QuoteNumber), res.ID);
				strHTML = null; res = null;
			}
		}
	}

};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Quotes", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
