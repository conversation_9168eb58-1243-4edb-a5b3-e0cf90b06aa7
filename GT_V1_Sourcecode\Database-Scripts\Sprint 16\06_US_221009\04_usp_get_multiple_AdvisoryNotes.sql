﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*   
===========================================================================================  
TASK        UPDATED BY       DATE           ACTION    DESCRIPTION  
[US-210037]  An.TranTan      23-Oct-2024	Create    Get multiple Manufacturer advisory notes  
[US-221009]  An.TranTan      29-Nov-2024	Update	  Prioritize restricted message  
===========================================================================================  
*/  
CREATE OR ALTER   PROCEDURE  [dbo].[usp_get_multiple_AdvisoryNotes]      
  @IDs NVARCHAR(MAX)
  ,@ClientID INT = 0
AS  
BEGIN
	SET NOCOUNT ON;

	DECLARE @RestrictedMessage NVARCHAR(MAX) = '';
	SELECT @RestrictedMessage = WarningText 
	FROM tbSystemWarningMessage WITH(NOLOCK) 
	WHERE WarningNo = 5	--Restricted manufacturer
		AND ApplyToCatagoryNo = 1	
		AND ClientNo = 0	
		AND InActive = 0;

	;with cte as(
		select distinct cast(value as int) as MfrId
		FROM STRING_SPLIT(@IDs, ',')
	)select
		m.ManufacturerId,
		CASE
			WHEN rm.RestrictedManufacturerId IS NOT NULL THEN @RestrictedMessage
			WHEN ISNULL(IsDisplayAdvisory, 0) = 1 THEN AdvisoryNotes 
			ELSE '' 
		END AS AdvisoryNotes
	from cte
	join tbManufacturer m WITH(NOLOCK) on m.ManufacturerId = cte.MfrId
	left join tbRestrictedManufacturer rm WITH(NOLOCK) on rm.ManufacturerNo = m.ManufacturerId
		AND rm.Inactive = 0
		AND rm.ClientNo = @ClientID

	SET NOCOUNT OFF;
END
GO


