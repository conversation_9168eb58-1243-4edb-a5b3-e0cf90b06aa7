using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:AllCountries runat=server></{0}:AllCountries>")]
	public class AllCountries : Base {
        protected bool _blnIncludeSelected = true;
        public bool IncludeSelected
        {
            get { return _blnIncludeSelected; }
            set { _blnIncludeSelected = value; }
        }

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.AutoSearch.AllCountries.AllCountries");
			SetAutoSearchType("AllCountries");
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			CharactersToEnterBeforeSearch = 2;
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		#endregion

		/// <summary>
		/// SetupScriptDescriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries", ClientID);
            _scScriptControlDescriptor.AddProperty("blnIncludeSelected", _blnIncludeSelected);
        }

	}
}