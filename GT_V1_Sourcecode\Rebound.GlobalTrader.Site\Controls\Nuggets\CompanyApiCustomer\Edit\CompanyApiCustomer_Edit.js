Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.initializeBase(this,[n]);this._intLineID=-1;this._intCompanyID=0;this.txtemailcheck=!1;this.txtphncheck=!1;this.txtpswrd1check=!1;this.txtpswrd2check=!1;this.txtContactPerson=!1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intLineID=null,this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked))},saveClicked:function(){var i=this.validatedetails(),n,t;this.txtpswrd1check==!0?(n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/CompanyApiCustomer"),n.set_DataObject("CompanyApiCustomer"),n.set_DataAction("SaveEdit"),n.addParameter("id",this._intLineID),n.addParameter("Email",this.getFieldValue("ctlEmail")),t=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlMobile_ctl03_txtCountryCode").val(),n.addParameter("CountryCode",t),n.addParameter("ContactName",this.getFieldValue("ctlContactPerson")),n.addParameter("Mobile",this.getFieldValue("ctlMobile")),n.addParameter("InActive",this.getFieldValue("ctlInActive")),n.addParameter("IsBomUser",this.getFieldValue("ctlBomUser")),n.addParameter("InSupUser",this.getFieldValue("ctlSupplierUser")),n.addParameter("Password",this.getFieldValue("ctlPassword")),n.addDataOK(Function.createDelegate(this,this.saveEditComplete)),n.addError(Function.createDelegate(this,this.saveEditError)),n.addTimeout(Function.createDelegate(this,this.saveEditError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null):(this.showError(!0),this.resetFormFields())},validatedetails:function(){var t=!1,n=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword_ctl03_txtPassword").val();n!=""&&n!=" "&&n!=undefined&&n.length>=16?(t=!0,this.txtpswrd1check=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword").css("background-color","#56954E")):(t=!1,this.txtpswrd1check=!1,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword").css("background-color","#990000"))},validateEmail:function(n){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(n)?($("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlEmail_ctl04_txtEmail").css("background-color","#56954E"),!0):($("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlEmail_ctl04_txtEmail").css("background-color","#990000"),!1)},validate10digitnumber:function(n){var i=n,t;if(i.length!=10)return!1;for(intRegex=/[0-9 -()+]+$/,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color","#56954E"),is_mobile=!0,t=0;t<10;t++)if(intRegex.test(i[t]))continue;else{$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color","#990000");is_mobile=!1;break}return is_mobile},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return n||this.showError(!0),n}};$(document).ready(function(){function n(){var n="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789",i;for(let r=1;r<18;r++)i=Math.floor(Math.random()*t.length+1),n+=t.charAt(i);return n}$("#checkbox2").click(function(){var n=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").attr("type");n=="password"?$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").attr("type","text"):$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").attr("type","password")});$("#btngenpass2").click(function(){var t=n();$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmEdit_ctlDB_ctlPassword_ctl03_txtPassword").val(t)})});Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);