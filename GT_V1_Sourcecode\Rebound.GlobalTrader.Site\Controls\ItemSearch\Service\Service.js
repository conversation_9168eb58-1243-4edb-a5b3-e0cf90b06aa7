Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.Service=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.initializeBase(this,[n]);this._intMaxResults=50;this._GlobalClientNo=-1};Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.prototype={get_intMaxResults:function(){return this._intMaxResults},set_intMaxResults:function(n){this._intMaxResults!==n&&(this._intMaxResults=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||(this._intMaxResults=null,this._GlobalClientNo=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/Service");this._objData.set_DataObject("Service");this._objData.set_DataAction("GetData");this._objData.addParameter("Name",this.getFieldValue("ctlName"));this._objData.addParameter("GlobalClientNo",this._GlobalClientNo)},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r&&t<this._intMaxResults;t++)n=this._objResult.Results[t],i=[$R_FN.setCleanTextValue(n.Name),$R_FN.setCleanTextValue(n.Desc),$R_FN.setCleanTextValue(n.Cost),$R_FN.setCleanTextValue(n.Price)],this._tblResults.addRow(i,n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.Service.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Service",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);