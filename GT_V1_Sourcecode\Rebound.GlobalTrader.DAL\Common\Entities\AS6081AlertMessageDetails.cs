﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
/*
    MARKER          DATE            OWNER               REMARSKS
    [001]           08-09-2023      Ravi                RP-2340 Created class and added properties
 */
namespace Rebound.GlobalTrader.DAL.Common.Entities
{
    public class AS6081AlertMessageDetails
    {
        #region Constructor
        public AS6081AlertMessageDetails()
        {
                
        }
        #endregion

        #region Properties
        public System.Int32 AlertMessageId { get; set; }
        public System.String ShortName { get; set; }

        public System.String Message { get; set; }

        public System.String Description { get; set; }

        public System.Int32? UpdatedBy { get; set; }

        public System.DateTime? DLUP { get; set; }

        public System.Boolean ? AS6081 { get; set; }
        #endregion
    }
}
