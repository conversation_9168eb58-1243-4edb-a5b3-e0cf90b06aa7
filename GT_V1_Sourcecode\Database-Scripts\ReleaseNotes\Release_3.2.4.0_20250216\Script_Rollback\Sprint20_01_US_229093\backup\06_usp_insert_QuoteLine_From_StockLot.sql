﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_QuoteLine_From_StockLot]  
   @QuoteNo int            
 , @LotNo int           
 , @StockNo nvarchar(max) = NULL            
 , @UpdatedBy int = NULL           
 , @QuoteLineId int OUTPUT                                       
                                          
AS                                           
  BEGIN                                          
  insert into  tbQuoteLine (QuoteNo,FullPart,Part,ManufacturerNo,DateCode,PackageNo,Quantity,   Price,      ProductNo,StockNo,ROHS,UpdatedBy,MSLLevel)          
  select @QuoteNo,  dbo.ufn_get_fullpart(Part), Part,ManufacturerNo,DateCode,PackageNo    ,ISNULL(QuantityInStock,0),ISNULL(ResalePrice,0),    
  ProductNo,StockId,ROHS,@UpdatedBy,MSLLevel from     tbStock    
  where LotNo=@LotNo and StockId in (select val from  dbo.SplitString(@StockNo,','))                                 
  SET @QuoteLineId = scope_identity()        
  if(@QuoteLineId>1)                            
  begin    
  update tbStock set BookedLotQuoteNo=@QuoteNo , IsBookedLotQuote=1  where LotNo=@LotNo and StockId in (select val from  dbo.SplitString(@StockNo,','))    
  end    
                  
END 
GO


