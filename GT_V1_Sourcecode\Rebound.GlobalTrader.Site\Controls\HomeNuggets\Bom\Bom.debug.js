///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom = function(element) {
    Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.prototype = {

    get_pnlReady: function() { return this._pnlReady; }, set_pnlReady: function(value) { if (this._pnlReady !== value) this._pnlReady = value; },
    get_tblReady: function() { return this._tblReady; }, set_tblReady: function(value) { if (this._tblReady !== value) this._tblReady = value; },

    get_pnlPartial: function() { return this._pnlPartial; }, set_pnlPartial: function(value) { if (this._pnlPartial !== value) this._pnlPartial = value; },
    get_tblPartial: function() { return this._tblPartial; }, set_tblPartial: function(value) { if (this._tblPartial !== value) this._tblPartial = value; },


    get_pnlNew: function() { return this._pnlNew; }, set_pnlNew: function(value) { if (this._pnlNew !== value) this._pnlNew = value; },
    get_tblNew: function() { return this._tblNew; }, set_tblNew: function(value) { if (this._tblNew !== value) this._tblNew = value; },

    get_pnlRFQ: function() { return this._pnlRFQ; }, set_pnlRFQ: function(value) { if (this._pnlRFQ !== value) this._pnlRFQ = value; },
    get_tblRFQ: function() { return this._tblRFQ; }, set_tblRFQ: function(value) { if (this._tblRFQ !== value) this._tblRFQ = value; },

    get_pnlClosed: function() { return this._pnlClosed; }, set_pnlClosed: function(value) { if (this._pnlClosed !== value) this._pnlClosed = value; },
    get_tblClosed: function() { return this._tblClosed; }, set_tblClosed: function(value) { if (this._tblClosed !== value) this._tblClosed = value; },
    
    get_pnlNoBid: function () { return this._pnlNoBid; }, set_pnlNoBid: function (value) { if (this._pnlNoBid !== value) this._pnlNoBid = value; },
    get_tblNoBid: function () { return this._tblNoBid; }, set_tblNoBid: function (value) { if (this._tblNoBid !== value) this._tblNoBid = value; },

    get_pnlMore: function() { return this._pnlMore; }, set_pnlMore: function(value) { if (this._pnlMore !== value) this._pnlMore = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
    },

    dispose: function() {
    if (this.isDisposed) return;
        
        if (this._tblReady) this._tblReady.dispose();
        this._pnlReady = null;
        this._tblReady = null;

        if (this._tblPartial) this._tblPartial.dispose();
        this._pnlPartial = null;
        this._tblPartial = null;
        
        if (this._tblNew) this._tblNew.dispose();
        this._pnlNew = null;
        this._tblNew = null;
        
        if (this._tblRFQ) this._tblRFQ.dispose();
        this._pnlRFQ = null;
        this._tblRFQ = null;
        
        if (this._tblClosed) this._tblClosed.dispose();
        this._pnlClosed = null;
        this._tblClosed = null;
        
        this._pnlMore = null;
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.callBaseMethod(this, "dispose");
    },

    setupLoadingState: function() {
        $R_FN.showElement(this._pnlMore, false);
        $R_FN.showElement(this._pnlReady, false);
        $R_FN.showElement(this._pnlNew, false);
        $R_FN.showElement(this._pnlRFQ, false);
        $R_FN.showElement(this._pnlClosed, false);
        Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.callBaseMethod(this, "setupLoadingState");
    },

    showNoData: function(bln) {
        this.showContent(true);
        $R_FN.showElement(this._pnlNoData, bln);
    },

    getData: function() {
        this.setupLoadingState();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/HomeNuggets/Bom");
        obj.set_DataObject("Bom");
        obj.set_DataAction("GetData");
        obj.addParameter("rowcount", this._intRowCount);
        obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
        obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
        obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataComplete: function(args) {
        this.showNoData(args._result.Count == 0);
        $R_FN.showElement(this._pnlMore, args._result.Count > 0);
        var result = args._result;
        var aryData, row;
        //Ready
        this._tblReady.clearTable();
        for (var i = 0; i < result.Ready.length; i++) {
            row = result.Ready[i];
            var strCSS = "";

            //if (row.IsReleased == true) { strCSS = ""; }
            aryData = [
			$RGT_nubButton_BOM(row.BOMId, row.BOMCode),
			$R_FN.setCleanTextValue(row.Name),
			row.Date,
			row.QuoteRequired
				];
            this._tblReady.addRow(aryData, null, null, null, strCSS);
        }
        $R_FN.showElement(this._pnlReady, result.Ready.length > 0);


        //Partial
        this._tblPartial.clearTable();
        for (var i = 0; i < result.Partial.length; i++) {
            row = result.Partial[i];
            var strCSS = "";
            aryData = [
			$RGT_nubButton_BOM(row.BOMId, row.BOMCode),
			$R_FN.setCleanTextValue(row.Name),
			row.Date,
			row.QuoteRequired
				];
            this._tblPartial.addRow(aryData, null, null, strCSS);
        }
        $R_FN.showElement(this._pnlPartial, result.Partial.length > 0);

        //New
        if (result.IsHub==false) {
            this._tblNew.clearTable();
            for (var i = 0; i < result.New.length; i++) {
                row = result.New[i];
                aryData = [
				$RGT_nubButton_BOM(row.BOMId, row.BOMCode),
			$R_FN.setCleanTextValue(row.Name),
			row.Date,
			row.QuoteRequired
				];
                this._tblNew.addRow(aryData, null);
                row = null;
            }
            $R_FN.showElement(this._pnlNew, result.New.length > 0);
        }
        //RFQ
        this._tblRFQ.clearTable();
        for (var i = 0; i < result.RFQ.length; i++) {
            row = result.RFQ[i];
            aryData = [
				$RGT_nubButton_BOM(row.BOMId, row.BOMCode),
			$R_FN.setCleanTextValue(row.Name),
			row.Date,
			row.QuoteRequired
				];
            this._tblRFQ.addRow(aryData, null);
            row = null;
        }
        $R_FN.showElement(this._pnlRFQ, result.RFQ.length > 0);
        
        //Closed
        this._tblClosed.clearTable();
        for (var i = 0; i < result.Closed.length; i++) {
            row = result.Closed[i];
            var strCSS = "";
            aryData = [
			$RGT_nubButton_BOM(row.BOMId, row.BOMCode),
			$R_FN.setCleanTextValue(row.Name),
			row.Date,
			row.QuoteRequired
				];
            this._tblClosed.addRow(aryData, null, null, null, strCSS);
            row = null;
        }
        $R_FN.showElement(this._pnlClosed, result.Closed.length > 0);

        //NoBid
        this._tblNoBid.clearTable();
        for (var i = 0; i < result.NoBid.length; i++) {
            row = result.NoBid[i];
            var strCSS = "";
            aryData = [
			$RGT_nubButton_BOM(row.BOMId, row.BOMCode),
			$R_FN.setCleanTextValue(row.Name),
			row.Date,
			row.QuoteRequired
            ];
            this._tblNoBid.addRow(aryData, null, null, null, strCSS);
            row = null;
        }
        $R_FN.showElement(this._pnlNoBid, result.NoBid.length > 0);
        this.hideLoading();
    }
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.Bom", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
