//-------------------------------------------------------------------------------------------
// RP 12.01.2010:
// - new function FormatConvertedCurrency to display strings like: 100.00 GBP (120.00 USD)
//	[001] For IHS wild card symbol removal
//-------------------------------------------------------------------------------------------
//[0001]     Arpit Mody      07/03/2023     //RP-25
// [0002]    Soorya Vyas	27/10/2023      RP-2594    Daylight saving date conversion
using System;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Reflection;
using System.ComponentModel;
using System.Resources;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using System.Text;
using System.IO;
using System.Web.UI.WebControls;
using System.Collections;
using System.Threading;
using System.Globalization;
using System.Diagnostics;


/// <summary>
/// Functions, static class
/// </summary>
namespace Rebound.GlobalTrader.Site {
	public static partial class Functions {

		/// <summary>
		/// Ensures a clean filter value taken from a user entry
		/// </summary>
		/// <returns>Cleaned filter value</returns>
		public static string CleanDatabaseFilter(string str) {
			// Regex search and replace
			Regex regex = new Regex(@"[:!""£^&*$%()_+|@~#';\/]", RegexOptions.None);
			str = regex.Replace(str, "");
			return str;
		}

		/// <summary>
		/// Formats a URL with 'http://' at the start of it
		/// </summary>
		/// <param name="strURL">URL to format</param>
		/// <returns>URL with 'http://' added</returns>
		public static string FormatWebAddress(string strURL) {
			strURL = strURL.Trim();
			if (strURL != "") {
				if (!strURL.StartsWith("http")) strURL = string.Format("http://{0}", strURL);
			}
			return strURL;
		}

		/// <summary>
		/// Formats a string coming from Javascript output
		/// </summary>
		/// <param name="str"></param>
		/// <returns></returns>
		public static string FormatStringForDatabase(string str) {
			str = str.Replace("<br>", "\r\n");
			str = str.Replace("<br/>", "\r\n");
			str = str.Replace("<br />", "\r\n");
			str = str.Replace(@":QUOTE:", @"""");
			str = str.Replace(@":PLUS:", @"+");
			str = str.Replace(@":AND:", @"&");
			return str;
		}
		
		//	[001]
		/// <summary>
		/// This function check for wild card symbol % and replace this with blank for IHS
		/// </summary>
		/// <param name="str"></param>
		/// <returns></returns>
		public static string FormatStringForDatabase_IHS(string str)
		{
			str = str.Replace("<br>", "\r\n");
			str = str.Replace("<br/>", "\r\n");
			str = str.Replace("<br />", "\r\n");
			str = str.Replace(@":QUOTE:", @"""");
			str = str.Replace(@":PLUS:", @"+");
			str = str.Replace(@":AND:", @"&");
			str = str.Replace("%", @"");
			return str;
		}

		/// <summary>
		/// Formats a currency amount
		/// </summary>
		/// <param name="objAmount"></param>
		/// <param name="strCurrency"></param>
		/// <param name="intFigures"></param>
		/// <returns></returns>
		public static string FormatCurrency(object objAmount, string strCurrency, int intFigures, bool blnCommaThousands) {
			if (objAmount == null) objAmount = 0;
			CultureInfo ci = new CultureInfo(SessionManager.Culture);
			Double dbl = Convert.ToDouble(objAmount);
			string strReturn = dbl.ToString(string.Format("{0}{1}", (blnCommaThousands) ? "n" : "f", intFigures), ci);
			if (!String.IsNullOrEmpty(strCurrency)) strReturn += string.Format(" {0}", strCurrency.Trim());
			return strReturn.Trim();
		}
		public static string FormatCurrency(object objAmount, string strCurrency, bool blnCommaThousands) { return FormatCurrency(objAmount, strCurrency, 5, false); }
		public static string FormatCurrency(object objAmount, string strCurrency, int intFigures) { return FormatCurrency(objAmount, strCurrency, intFigures, false); }
		public static string FormatCurrency(object objAmount, int intFigures) { return FormatCurrency(objAmount, "", intFigures, false); }
		public static string FormatCurrency(object objAmount, string strCurrency) { return FormatCurrency(objAmount, strCurrency, 5, false); }
		public static string FormatCurrency(object objAmount) { return FormatCurrency(objAmount, "", 5, false); }
		public static string FormatCurrencyForReport(object objAmount, int intFigures) { return FormatCurrency(objAmount, "", intFigures, true); }
		public static string FormatCurrencyForReport(object objAmount, string strCurrency) { return FormatCurrency(objAmount, strCurrency, 5, true); }
		public static string FormatCurrencyForReport(object objAmount) { return FormatCurrency(objAmount, "", 5, true); }


		/// <summary>
		/// Formats a currency description string
		/// </summary>
		public static string FormatCurrencyDescription(string strDescription, string strCode) {
			string strReturn = "";
			if (!string.IsNullOrEmpty(strCode) && !string.IsNullOrEmpty(strDescription)) {
				strReturn = String.Format("{0} ({1})", strDescription, strCode);
			} else {
				if (!string.IsNullOrEmpty(strCode)) strReturn = strCode;
				if (!string.IsNullOrEmpty(strDescription)) strReturn = strDescription;
			}
			return strReturn;
		}

		/// <summary>
		/// Formats a Currency string with two values, first currency passed comes first
		/// e.g. 100.00 USD (90.00 GBP)
		/// </summary>
		public static string FormatConvertedCurrency(double? dblValue1, int? intCurrencyNo1, string strCurrencyCode1, double? dblValue2, int? intCurrencyNo2, string strCurrencyCode2, int? intDecimalPlaces) {
			if (dblValue1 == null) dblValue1 = 0;
			if (dblValue2 == null) dblValue2 = 0;
			if (intDecimalPlaces == null) intDecimalPlaces = 2;
			if (intCurrencyNo1 == intCurrencyNo2 || dblValue1 == 0 || intCurrencyNo1 == null || intCurrencyNo1 == 0) {
				return Functions.FormatCurrency((double)dblValue1, strCurrencyCode1, (int)intDecimalPlaces,true);
			} else {
				return String.Format("{0} ({1})", Functions.FormatCurrency((double)dblValue1, strCurrencyCode1, (int)intDecimalPlaces,true), Functions.FormatCurrency((double)dblValue2, strCurrencyCode2, (int)intDecimalPlaces,true));
			}

		}

		/// <summary>
		/// Formats a Percentage Amount
		/// </summary>
		/// <param name="objAmount"></param>
		/// <param name="intFigures">Decimal places - defaults to 1</param>
		/// <param name="blnAddPercentSign">Should percent sign be added - defaults to true</param>
		/// <returns></returns>
		public static string FormatPercentage(object objAmount, int intFigures, bool blnAddPercentSign) {
			string strReturn = FormatNumeric(objAmount, intFigures);
			if (blnAddPercentSign) strReturn += "%";
			return strReturn;
		}
		public static string FormatPercentage(object objAmount, int intFigures) { return FormatPercentage(objAmount, intFigures, true); }
		public static string FormatPercentage(object objAmount) { return FormatPercentage(objAmount, 1, true); }

		/// <summary>
		/// Formats a Numeric Amount
		/// </summary>
		/// <param name="objAmount"></param>
		/// <param name="intFigures">Decimal places - defaults to 0</param>
		/// <returns></returns>
		public static string FormatNumeric(object objAmount, int intFigures, bool blnCommaThousands) {
			CultureInfo ci = new CultureInfo(SessionManager.Culture);
			if (objAmount == null) objAmount = 0;
			Double dbl = Convert.ToDouble(objAmount);
			return dbl.ToString(string.Format("{0}{1}", (blnCommaThousands) ? "n" : "f", intFigures), ci);
		}
		public static string FormatNumeric(object objAmount, int intFigures) { return FormatNumeric(objAmount, intFigures, false); }
		public static string FormatNumeric(object objAmount) { return FormatNumeric(objAmount, 0, false); }
		public static string FormatNumericForReport(object objAmount, int intFigures) { return FormatNumeric(objAmount, intFigures, true); }
		public static string FormatNumericForReport(object objAmount) { return FormatNumeric(objAmount, 0, true); }

		/// <summary>
		/// Formats a date
		/// </summary>
		/// <param name="dtm"></param>
		/// <param name="blnIncludeTime">Should time be included?</param>
		/// <returns></returns>
		public static string FormatDateBSTtime(DateTime? dtm, bool blnLongDate, bool blnIncludeTime, bool blnLongTime) {
			string strReturn = "";
			if (dtm != null) {
				DateTime dtm2 = (DateTime)dtm;
				if (dtm2.Year == 1 && dtm2.Month == 1 && dtm2.Day == 1) {
					strReturn = "";
				} else {
					CultureInfo ci = new CultureInfo(SessionManager.Culture);
					strReturn = dtm2.ToString((blnLongDate) ? "D" : "d", ci);
					TimeZoneInfo ukTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GMT Standard Time");
					DateTime utcNow = DateTime.UtcNow;
					DateTime ukTime = TimeZoneInfo.ConvertTimeFromUtc(utcNow, ukTimeZone);
					//if (blnIncludeTime) strReturn += string.Format(" {0}", ukTime.ToString((blnLongTime) ? "T" : "t", ci)) + " " + "(BST)"; //[0002]
					if (blnIncludeTime) strReturn += string.Format(" {0}", ukTime.ToString((blnLongTime) ? "T" : "t", ci)) + " " + CheckDayLightSaving(ukTime); //[0002]
					ci = null;
                }
            }
			return strReturn;
		}
		public static string FormatDate(DateTime? dtm, bool blnLongDate, bool blnIncludeTime, bool blnLongTime)
		{
			string strReturn = "";
			if (dtm != null)
			{
				DateTime dtm2 = (DateTime)dtm;
				if (dtm2.Year == 1 && dtm2.Month == 1 && dtm2.Day == 1)
				{
					strReturn = "";
				}
				else
				{
					CultureInfo ci = new CultureInfo(SessionManager.Culture);
					strReturn = dtm2.ToString((blnLongDate) ? "D" : "d", ci);
					if (blnIncludeTime) strReturn += string.Format(" {0}", dtm2.ToString((blnLongTime) ? "T" : "t", ci));
					ci = null;
				}
			}
			return strReturn;
		}
		public static string FormatDateStaticBST(DateTime? dtm, bool blnLongDate, bool blnIncludeTime, bool blnLongTime)
		{
			string strReturn = "";
			if (dtm != null)
			{
				DateTime dtm2 = (DateTime)dtm;
				if (dtm2.Year == 1 && dtm2.Month == 1 && dtm2.Day == 1)
				{
					strReturn = "";
				}
				else
				{
					CultureInfo ci = new CultureInfo(SessionManager.Culture);
					strReturn = dtm2.ToString((blnLongDate) ? "D" : "d", ci) ;
					//if (blnIncludeTime) strReturn += string.Format(" {0}", dtm2.ToString((blnLongTime) ? "T" : "t", ci)) + " " + "(BST)"; //[0002]
					if (blnIncludeTime) strReturn += string.Format(" {0}", dtm2.ToString((blnLongTime) ? "T" : "t", ci)) + " " + CheckDayLightSaving(dtm2);   //[0002]
					ci = null;
				}
			}
			return strReturn;
		}
		
		
		/// <summary>
		/// Gets the UK time.
		/// </summary>
		/// <returns>The UK time.</returns>
		public static DateTime GetUKLocalTime()
		{

			TimeZoneInfo ukTimeZone = TimeZoneInfo.FindSystemTimeZoneById("GMT Standard Time");
			DateTime currentUkTime = TimeZoneInfo.ConvertTimeFromUtc(DateTime.UtcNow, ukTimeZone);
			return currentUkTime;
		}

		public static string FormatDate(DateTime? dtm, bool blnLongDate, bool blnIncludeTime) { return FormatDate(dtm, blnLongDate, blnIncludeTime, false); }
		public static string FormatDateBSTtime(DateTime? dtm, bool blnLongDate, bool blnIncludeTime) { return FormatDateBSTtime(dtm, blnLongDate, blnIncludeTime, false); }
		public static string FormatDateStaticBST(DateTime? dtm, bool blnLongDate, bool blnIncludeTime) { return FormatDateStaticBST(dtm, blnLongDate, blnIncludeTime, false); }
		public static string FormatDate(DateTime? dtm, bool blnLongDate) { return FormatDate(dtm, blnLongDate, false, false); }
		public static string FormatDate(DateTime? dtm) { return FormatDate(dtm, false, false); }
		public static string FormatUSDate(DateTime? dtm) { 
			return FormatDate(dtm, false, false).Replace("/", "-"); 
		}

		public static string FormatBirthday(DateTime? dtm) {
			string strReturn = "";
			if (dtm != null) {
				DateTime dtm2 = (DateTime)dtm;
				CultureInfo ci = new CultureInfo(SessionManager.Culture);
				strReturn = dtm2.ToString("m", ci);
			}
			return strReturn;
		}

		/// <summary>
		/// Formats a gender into a string from a number (usually given in a dropdown)
		/// </summary>
		/// <param name="intGender"></param>
		/// <returns></returns>
		public static string FormatGender(int? intGender) {
			string strGender = "";
			if (intGender == 1) strGender = Functions.GetGlobalResource("Misc", "Female");
			if (intGender == 2) strGender = Functions.GetGlobalResource("Misc", "Male");
			return strGender;
		}

		/// <summary>
		/// Replaces lines breaks with 'br' tags
		/// </summary>
		public static string ReplaceLineBreaks(string strIn, string strReplacement) {
			if (strIn == null) strIn = "";
			string strOut = strIn.Trim();
			strOut = strOut.Replace("\r\n", strReplacement);
			strOut = strOut.Replace("\r", strReplacement);
			strOut = strOut.Replace("\n", strReplacement);
			return strOut;
		}
		public static string ReplaceLineBreaks(string strIn) {
			return Functions.ReplaceLineBreaks(strIn, "<br />");
		}

		/// <summary>
		/// Adds controls to a given control from a given template
		/// </summary>
		public static void AddControlsFromTemplate(Control ctl, ITemplate tmp) {
			Container cnt = new Container();
			tmp.InstantiateIn(cnt);
			ctl.Controls.Add(cnt);
			cnt.Dispose();
		}

		/// <summary>
		/// Formats an "x day(s) ago" string
		/// </summary>
		public static string FormatDaysAgo(object objDaysAgo) {
			string strOut = "&nbsp;";
			if (objDaysAgo != null) {
				int intDaysAgo;
				if (int.TryParse(objDaysAgo.ToString(), out intDaysAgo)) {
					switch (intDaysAgo) {
						case 0: strOut = Functions.GetGlobalResource("Misc", "Today"); break;
						case 1: strOut = Functions.GetGlobalResource("Misc", "Yesterday"); break;
						default: strOut = String.Format(Functions.GetGlobalResource("Misc", "XDaysAgo"), intDaysAgo); break;
					}
				}
			}
			return strOut;
		}

		public static object FormatTime(DateTime? dtm) {
			string strReturn = "&nbsp;";
			if (dtm != null) {
				DateTime dtm2 = (DateTime)dtm;
				CultureInfo ci = new CultureInfo(SessionManager.Culture);
				strReturn = dtm2.ToString("t", ci);
			}
			return strReturn;
		}

		public static string FormatDLUP(DateTime? dtm, string strLoginName) {
			string str = FormatDate(dtm, true, true);
			if (!String.IsNullOrEmpty(strLoginName)) str = String.Format(GetGlobalResource("misc", "UpdatedByDateAndUser"), str, strLoginName);
			return str;
		}

		public static string FormatStringWithPaddingToFixedWidth(object objValue, int intWidth) { return FormatStringWithPaddingToFixedWidth(objValue, intWidth, HorizontalAlign.Left); }
		public static string FormatStringWithPaddingToFixedWidth(object objValue, int intWidth, HorizontalAlign enmHorizontalAlignment) {
			string strOut = objValue.ToString().Trim();
			int i = 1;
			while (strOut.Length < intWidth) {
				bool blnPutSpaceAfter = true;
				if (enmHorizontalAlignment == HorizontalAlign.Right) blnPutSpaceAfter = false;
				if (enmHorizontalAlignment == HorizontalAlign.Center) blnPutSpaceAfter = (Math.IEEERemainder((double)i, 2) == 0);
				strOut = (blnPutSpaceAfter) ? strOut + " " : " " + strOut;
				i += 1;
			}
			return strOut;
		}

		public static string RemoveHTMLTags(string strIn) {
			Regex regex = new Regex("</?(.*)>", RegexOptions.IgnoreCase | RegexOptions.Multiline);
			return regex.Replace(strIn, string.Empty);
		}

		public static string RemovePunctuation(string strIn) {
			return Regex.Replace(strIn.Trim(), @"\W*", "");
		}

		/// <summary>
		/// Removes punctuation but retains an initial percent so we can do a begins with search
		/// </summary>
		public static string RemovePunctuationRetainingPercentSigns(string strIn) {
			strIn = strIn.Trim();
			string strOut = RemovePunctuation(strIn);
			if (strIn.StartsWith("%")) strOut = string.Format("%{0}", strOut);
			if (strIn.EndsWith("%")) strOut = string.Format("{0}%", strOut);
			return strOut;
		}
		public static string RemovePunctuationRetainingPercentSignsCompanyName(string strIn)
		{
			strIn = strIn.Trim();
		    //string strOut = RemovePunctuation(strIn);
			string strOut = strIn;
			//if (strIn.StartsWith("%")) strOut = string.Format("%{0}", strOut);
			//if (strIn.EndsWith("%")) strOut = string.Format("{0}%", strOut);
			return strOut;
		}
		public static string RemovePunctuationRetainingPercentSignsNew(string strIn)
        {
            strIn = strIn.Trim();
            string strOut = strIn;
            if (strIn.StartsWith("%")) strOut = string.Format("%{0}", strOut);
            if (strIn.EndsWith("%")) strOut = string.Format("{0}%", strOut);
            return strOut;
        }
        public static string UrlDecodeRetainingPercentSigns(string strIn) {
			if (string.IsNullOrEmpty(strIn)) strIn = "";
			strIn = strIn.Trim();
			string strOut = HttpUtility.UrlDecode(strIn);
			if (strIn.StartsWith("%")) strOut = string.Format("%{0}", strOut);
			if (strIn.EndsWith("%")) strOut = string.Format("{0}%", strOut);
			return strOut;
		}

        /// <summary>
        /// Ensures a clean filter value taken from a user entry
        /// </summary>
        /// <returns>Cleaned filter value</returns>
        public static string CleanJunkCharInCSV(string str)
        {
			// Regex search and replace
			Regex regex = new Regex(@"[–—–]", RegexOptions.None);
			str = regex.Replace(str, "-");
            return str;
        }

        /// <summary>
        /// Ensures a clean filter value taken from a user entry
        /// </summary>
        /// <returns>Cleaned Stock Import Tool value</returns>
        public static string CleanDatabaseForStockTool(string str)
        {
            // Regex search and replace for Stock Import Tool
            //Regex regex = new Regex(@"[!""�^&*$%()|@~#';]", RegexOptions.None);
            //Regex regex = new Regex(@"[!""�^&*$%|@~#;]", RegexOptions.None);
            //last change at 10-08-2021
            //The import tool seems to be stripping out the # in Part and Customer Part numbers.
            //Regex regex = new Regex(@"[!""^*%|@~#;]", RegexOptions.None);
            //code changed end's
            Regex regex = new Regex(@"[!""^*%|@~;]", RegexOptions.None);
            str = regex.Replace(str, "");
            return str;
        }

        public static string GetFullPart(string str)
        {
            Regex regex = new Regex(@"[^a-zA-Z0-9]", RegexOptions.None);
            str = regex.Replace(str, "");
            return str;
        }

		//[0001]
		public static string CleanCarriageReturnTabAndNewLineCharacter(string str)
		{
			Regex regex = new Regex(@"\t|\n|\r", RegexOptions.None);
			str = regex.Replace(str, "");
			return str;
		}


		//[0002] Start
		public static string CheckDayLightSaving(DateTime ukTime)
		{
			TimeZoneInfo timeZone = TimeZoneInfo.FindSystemTimeZoneById("GMT Standard Time");
			bool isDaylightSavingTime = timeZone.IsDaylightSavingTime(ukTime);
			string msg = isDaylightSavingTime == true ? "BST" : "UTC";
			return msg;
		}
		//[0002] End

		public static string FormatPriceForAPI(double? value)
		{
			return value == null || value == 0 ? "0.00" : value.ToString();
		}

		public static string BeautifyPartNumber(string partNumber)
        {
			partNumber = partNumber.Replace("_AMPERSAND_", "&");
			partNumber = partNumber.Replace("_HASH_", "#");
			partNumber = partNumber.Replace("_EQUALS_", "=");
			partNumber = partNumber.Replace("_PLUS_", "+");
			return partNumber;
		}
	}

}