﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="BOMMainInfo_AddExpedite.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_AddExpedite" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>



<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMItems_AddExpedite")%></Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">	
			
			
			<ReboundUI_Form:FormField id="ctlExpediteNotes" runat="server" FieldID="txtExpediteNotes" ResourceTitle="AddNotes" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtExpediteNotes" TextMode="multiLine" runat="server" Width="255" Rows="2" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
