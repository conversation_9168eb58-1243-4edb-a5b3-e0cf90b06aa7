using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using System.Configuration;
using  Rebound.GlobalTrader.Site.Code.Common;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data {

	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class IHSPartSearch : Rebound.GlobalTrader.Site.Controls.AutoSearch.Data.Base {
        bool ServiceStatus = true;
		protected override void GetData() {
            List<IHSManager.Root> lstIHS = null;
			try {
                //lst = BLL.Part.AutoSearch(SessionManager.ClientID, GetFormValue_StringForPartSearch("search"));
                string strToken = this.GetToken();
                lstIHS = this.GettbIHSpart(strToken);
                //if (lstIHS.Count > 0)
                //{
                    JsonObject jsn = new JsonObject();
                    jsn.AddVariable("TotalRecords", lstIHS.Count);
                    JsonObject jsnRows = new JsonObject(true);
                    for (int i = 0; i < lstIHS.Count; i++)
                    {
                        if (i < lstIHS.Count)
                        {
                            string strPart = "";
                            string strihsvalues = ParamsToXml(lstIHS[i]);
                            strPart = lstIHS[i].prtNbr;
                            if (lstIHS[i].attributes.mfrPart != null)
                            {
                                strPart += "->" + lstIHS[i].attributes.mfrPart.mfrFullName;
                            }
                            if (!string.IsNullOrEmpty(lstIHS[i].prtStatus))
                                strPart += "->" + lstIHS[i].prtStatus;

                            if (!string.IsNullOrEmpty(lstIHS[i].HTSCode))
                                strPart += "->" + lstIHS[i].HTSCode;

                            //   if (!string.IsNullOrEmpty(lstIHS[i].HTSCode))

                            //string[] str = lstIHS[i].prtDesc.Split(',');
                            //if (str.Length > 0)
                            //{
                            //    strPart += "->" + str[0];
                            //}
                            //else
                            //    strPart += "->" + lstIHS[i].prtDesc;


                            JsonObject jsnRow = new JsonObject();
                            jsnRow.AddVariable("ID", lstIHS[i].id);
                            jsnRow.AddVariable("Name", Functions.ReplaceLineBreaks(strPart));
                            jsnRow.AddVariable("ExtraValue", Functions.ReplaceLineBreaks(strihsvalues));
                            jsnRows.AddVariable(jsnRow);
                            jsnRow.Dispose();
                            jsnRow = null;
                        }
                    }

                    jsn.AddVariable("ServiceStatus", ServiceStatus);
                    jsn.AddVariable("Results", jsnRows);
                    OutputResult(jsn);
                    jsnRows.Dispose(); jsnRows = null;
                    jsn.Dispose(); jsn = null;
                //}
			} catch (Exception e) {
				WriteError(e);
			} finally {
                lstIHS = null;
			}
			base.GetData();
		}

        private List<IHSManager.Root> GettbIHSpart(string taktoken)
        {
            List<IHSManager.Root> lst = new List<IHSManager.Root>();
            List<IHSManager.Root> lst1 = new List<IHSManager.Root>();
            try
            {
                string IHSPartSearchURL = Convert.ToString(ConfigurationManager.AppSettings["IHSPartSearchURL"]);
                string strSearchType = GetFormValue_String("txtGroup");
                // string strJson = "{\r\n   \"prtNbr\": \"'" + GetFormValue_String("partsearch") + "'\",\r\n   \"searchType\":\"" + GetFormValue_StringForPartSearch("searchType") + "\",\r\n   \"collections\":[\"prices\"]\r\n   \r\n   \r\n}";
                 string strJson = "{\r\n   \"prtNbr\": \"'" + GetFormValue_String("search") + "'\",\r\n   \"searchType\":\"" + strSearchType + "\",\r\n   \"collections\":[\"prices\"]\r\n   \r\n   \r\n}";
                //string strJson = "{\r\n   \"prtNbr\": \"'" + GetFormValue_String("search") + "'\",\r\n   \"searchType\":\"" + strSearchType + "\",\r\n   \"collections\":[\"prices\"]\r\n   \r\n  , \r\n  \"orderby\":[{\"direction\": \"descending\", \"field\": \"string\"}]\r\n   \r\n }";

                var client = new HttpUtils.RestClient();
                client.EndPoint = IHSPartSearchURL;
                client.Method = HttpVerb.POST;
                client.PostData = strJson;
                client.tokenRespose = taktoken;
                var json2 = client.MakeRequest();
                if (json2 != null)
                {
                    ServiceStatus = true;
                    System.Web.Script.Serialization.JavaScriptSerializer jsSerializer2 = new System.Web.Script.Serialization.JavaScriptSerializer();
                    lst1 = jsSerializer2.Deserialize<List<IHSManager.Root>>(json2);

                    lst = (from s in lst1
                          orderby s.prtNbr ascending
                           select s).ToList();

                    lst1 = null;
                }
                else
                {
                    ServiceStatus = false;
                }
            }
            catch (Exception ex)
            {
                WriteError(ex);
                ServiceStatus = false;

            }
            return lst;

        }
        private int GetTokenNumber(string Tockenno, int tokenid)
        {

            try
            {
                string TokenNumber = "";
               int Tokenvalidornot = BLL.Part.GetIHSTokenData(out TokenNumber);
                return Tokenvalidornot;

            }
            catch (Exception e)
            {

                WriteError(e);
                return 0;
            }
            finally
            {

            }
        }
        public void InsertTokenNumber(string TokenNumberInsert)
        {
            try
            {
                int intResult = CustomerRequirement.InsertTokenNumberFromAPI(
                    TokenNumberInsert
                    );
                if (intResult > 0)
                {

                }
                else
                {
                    WriteErrorSQLActionFailed("Insert");
                }
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }
        private string GetToken()
        {
            string token = string.Empty;
            string IHSUserName = Convert.ToString(ConfigurationManager.AppSettings["IHSUserName"]);
            string IHSUserPass = Convert.ToString(ConfigurationManager.AppSettings["IHSUserPass"]);
            string IHSTokenGenURL = Convert.ToString(ConfigurationManager.AppSettings["IHSTokenGenURL"]);

            try
            {
                //check token validity expire after 24 hr. code start
               // string TokenNumber = "";
               //int Tokenvalidornot = BLL.Part.GetIHSTokenData(out TokenNumber);

                
               // if (Tokenvalidornot > 0)
               // {
               //     if (!string.IsNullOrEmpty(TokenNumber))
               //     {
               //         token = TokenNumber;
               //     }
               // }
               // else
               // {
                    //check token validity expire after 24 hr. code end
                    var client = new HttpUtils.RestClient();
                    client.EndPoint = IHSTokenGenURL;//@"https://4donlinetest.ihs.com/parts-saas/auth";
                    client.Method = HttpVerb.POST;
                    //client.PostData = "{\n\t\"username\":\"rebounde2\",\n\t\"password\":\"kmHs2L#vn8M\"\n}";
                    client.PostData = "{\n\t\"username\":\"" + IHSUserName + "\",\n\t\"password\":\"" + IHSUserPass + "\"\n}";
                    client.tokenRespose = null;
                    var json = client.MakeRequest();
                if (json != null)
                {
                    System.Web.Script.Serialization.JavaScriptSerializer jsSerializer = new System.Web.Script.Serialization.JavaScriptSerializer();
                    var data1 = jsSerializer.Deserialize<IHSManager.TokenRespose>(json);
                    token = data1.authToken;
                    ServiceStatus = true;
                }
                else {
                    ServiceStatus = false;

                }
                   // InsertTokenNumber(token);
                    //need to save token number in tbihstoken into  database .
              //  }

            }
            catch (Exception e)
            {
                WriteError(e);
            }
            finally
            {
              
            }
            return token;

        }

        private static string ParamsToXml(IHSManager.Root item)
        {
            System.Text.StringBuilder strBuilder = new System.Text.StringBuilder();
            string strinAvgPrice = string.Empty;
            strBuilder.Append("<IHSResults>");
            if(item!=null)
            {


                strBuilder.Append("<IHSResult>");
                strBuilder.Append("<IHSID>");
                strBuilder.Append(item.id);
                strBuilder.Append("</IHSID>");
                // Part
                strBuilder.Append("<prtNbr>");
                //strBuilder.Append(item.prtNbr);
                if (!string.IsNullOrEmpty(item.prtNbr))
                    strBuilder.Append(item.prtNbr.Replace("&", "&amp;"));
                else
                    strBuilder.Append("");
                strBuilder.Append("</prtNbr>");
                //Manuf Name
                strBuilder.Append("<mfrName>");
                strBuilder.Append(item.mfrName);
                strBuilder.Append("</mfrName>");

                strBuilder.Append("<prtStatus>");
                strBuilder.Append(item.prtStatus);
                strBuilder.Append("</prtStatus>");

                strBuilder.Append("<prtDesc>");
                //strBuilder.Append(item.prtDesc);
                if (!string.IsNullOrEmpty(item.prtDesc))
                {
                    strinAvgPrice = "";
                    if (item.attributes.partInfo != null)
                    {
                        strinAvgPrice = Convert.ToString(item.attributes.partInfo.avgPrice);
                        //strBuilder.Append(item.prtDesc.Replace("&", "&amp;") + " , Avg Price : " + strinAvgPrice);
                        strBuilder.Append(item.prtDesc.Replace("&", "&amp;") + " , Avg Price : " + strinAvgPrice + " (Date Updated : " + DateTime.Now.ToString() +")");
                    }
                    else {
                        strBuilder.Append(item.prtDesc.Replace("&", "&amp;"));
                    }
                    
                   
                }
                else
                {
                    strBuilder.Append("");
                }
                strBuilder.Append("</prtDesc>");

                //strBuilder.Append("<prtDesc>");
                //strBuilder.Append(item.prtDesc);
                //strBuilder.Append("</prtDesc>");

                //attributes
                if (item.attributes.mfrPart != null)
                {
                    strBuilder.Append("<mfrFullName>");
                    if (!string.IsNullOrEmpty(item.attributes.mfrPart.mfrFullName))
                        strBuilder.Append(item.attributes.mfrPart.mfrFullName.Replace("&","&amp;"));
                    else
                        strBuilder.Append("");
                    strBuilder.Append("</mfrFullName>");

                    strBuilder.Append("<coo>");
                    strBuilder.Append(item.attributes.mfrPart.coo);
                    strBuilder.Append("</coo>");


                }
                else
                {
                    strBuilder.Append("<mfrFullName>");
                    strBuilder.Append("");
                    strBuilder.Append("</mfrFullName>");

                    strBuilder.Append("<coo>");
                    strBuilder.Append("");
                    strBuilder.Append("</coo>");
                }
                //expCompliance
                if (item.attributes.expCompliance != null)
                {
                    strBuilder.Append("<htsCd>");
                    strBuilder.Append(item.attributes.expCompliance.htsCd);
                    strBuilder.Append("</htsCd>");

                    strBuilder.Append("<eccn>");
                    strBuilder.Append(item.attributes.expCompliance.eccn);
                    strBuilder.Append("</eccn>");

                }
                else
                {

                    strBuilder.Append("<htsCd>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</htsCd>");

                    strBuilder.Append("<eccn>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</eccn>");
                }
                //Attribute part details
                if (item.attributes.partDetail != null)
                {
                    strBuilder.Append("<msl>");
                    strBuilder.Append(item.attributes.partDetail.msl);
                    strBuilder.Append("</msl>");

                    strBuilder.Append("<pckMethod>");
                    strBuilder.Append(item.attributes.partDetail.pckMethod);
                    strBuilder.Append("</pckMethod>");

                    strBuilder.Append("<pckCd>");
                    strBuilder.Append(item.attributes.partDetail.pckCd);
                    strBuilder.Append("</pckCd>");



                }
                else
                {

                    strBuilder.Append("<msl>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</msl>");

                    strBuilder.Append("<pckMethod>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</pckMethod>");

                    strBuilder.Append("<pckCd>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</pckCd>");

                }
                //Collections Price
                if (item.collections != null)
                {

                    strBuilder.Append("<source>");
                    strBuilder.Append(item.collections.prices[0].source);
                    strBuilder.Append("</source>");

                    strBuilder.Append("<price>");
                    strBuilder.Append(item.collections.prices[0].price);
                    strBuilder.Append("</price>");

                    strBuilder.Append("<currency>");
                    strBuilder.Append(item.collections.prices[0].currency);
                    strBuilder.Append("</currency>");

                    strBuilder.Append("<telephone>");
                    strBuilder.Append(item.collections.prices[0].telephone);
                    strBuilder.Append("</telephone>");

                    strBuilder.Append("<email>");
                    strBuilder.Append(item.collections.prices[0].email);
                    strBuilder.Append("</email>");

                    strBuilder.Append("<priceAvailabilityLink>");
                    strBuilder.Append(item.collections.prices[0].priceAvailabilityLink);
                    strBuilder.Append("</priceAvailabilityLink>");

                }
                else
                {
                    strBuilder.Append("<source>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</source>");

                    strBuilder.Append("<price>");
                    strBuilder.Append("0");
                    strBuilder.Append("</price>");

                    strBuilder.Append("<currency>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</currency>");

                    strBuilder.Append("<telephone>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</telephone>");

                    strBuilder.Append("<email>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</email>");

                    strBuilder.Append("<priceAvailabilityLink>");
                    strBuilder.Append(" ");
                    strBuilder.Append("</priceAvailabilityLink>");
                }
                strBuilder.Append("</IHSResult>");
            }





            strBuilder.Append("</IHSResults>");
            return strBuilder.ToString();
        }
    }
}