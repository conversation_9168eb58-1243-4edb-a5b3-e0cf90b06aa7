<%@ Control Language="C#" CodeBehind="CompanyGlobalSalesPDetails_Delete.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyGlobalSalesPDetails_Delete" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CompanyManufacturers_Delete")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField ID="ctlManufacturerSelected" FieldID="lblManufacturerSelected" runat="server" ResourceTitle="Manufacturer" >
				<Field>
					<asp:Label ID="lblManufacturerSelected" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
