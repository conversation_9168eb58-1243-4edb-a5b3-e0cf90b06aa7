///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.StarRating = function(element) { 
	Rebound.GlobalTrader.Site.Controls.StarRating.initializeBase(this, [element]);
	this._aryStars = [];
};
	
Rebound.GlobalTrader.Site.Controls.StarRating.prototype = {

	get_intMaxRating: function() { return this._intMaxRating; }, set_intMaxRating: function(value) { if (this._intMaxRating !== value) this._intMaxRating = value; },
	get_intInitialRating: function() { return this._intInitialRating; }, set_intInitialRating: function(value) { if (this._intInitialRating !== value) this._intInitialRating = value; },
	get_intCurrentRating: function() { return this._intCurrentRating; }, set_intCurrentRating: function(value) { if (this._intCurrentRating !== value) this._intCurrentRating = value; },
	get_blnReadOnly: function() { return this._blnReadOnly; }, set_blnReadOnly: function(value) { if (this._blnReadOnly !== value) this._blnReadOnly = value; },

	addChanged: function(handler) { this.get_events().addHandler("Changed", handler); },
	removeChanged: function(handler) { this.get_events().removeHandler("Changed", handler); },
	onChanged: function() {
		var handler = this.get_events().getHandler("Changed");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.StarRating.callBaseMethod(this, "initialize");
		if (!this._blnReadOnly) $addHandler(this.get_element(), "mouseout", Function.createDelegate(this, this.onMouseOut));
		this._intCurrentRating = this._intInitialRating;
		this.setupStars();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._aryStars = null;
		this._intMaxRating = null;
		this._intInitialRating = null;
		this._intCurrentRating = null;
		this._blnReadOnly = null;
		Rebound.GlobalTrader.Site.Controls.StarRating.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},

	setupStars: function() {
		this._aryStars = this.get_element().childNodes;
	},
	
	onMouseOut: function() {
		this.setRating(this._intCurrentRating);
	},
	
	starMouseOver: function(intStar) {
		for (var i = 0, l = this._intMaxRating; i < l; i++) {
			this.setStar(i, (i <= intStar), "starsFilled");
		}
	},
	
	starClick: function(intStar) {
		intStar = (intStar == this._intMaxRating) ? 0 : intStar + 1;
		this.setRating(intStar);
	},
	
	setRating: function(intNumberOfStars) {
		this._intCurrentRating = intNumberOfStars;
		for (var i = 0, l = this._intMaxRating; i < l; i++) {
			this.setStar(i, (i < intNumberOfStars), "starsSaved");
		}
		this.onChanged();
	},
	
	resetRating: function() {
		this.setRating(this._intInitialRating);
	},
	
	setStar: function(iStar, blnSet, strCssClass) {
		this._aryStars[iStar].className = (blnSet) ? "stars " + strCssClass : "stars starsEmpty";
	}

};

Rebound.GlobalTrader.Site.Controls.StarRating.registerClass("Rebound.GlobalTrader.Site.Controls.StarRating", Sys.UI.Control, Sys.IDisposable);