﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_InvoiceLine_for_SalesOrder]        
--*******************************************************************************************        
--* RP 14.06.2010:        
--* - remove slow view        
--*          
--* SK 24.11.2009:        
--* - include column ShippedByName for use in SOShippingLines display        
--*          
--* SK 10.08.2009:        
--* - invoice lines are now made inactive rather than deleting  - ignore all inactive lines        
--*          
--* RP 07.07.2009:        
--* - cut down fields to only those referenced in the App to speed up calls from 14 secs!        
--*******************************************************************************************        
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-222245]	NgaiTo		 		28-Nov-2024		UPDATE			222245: [PROD Bug] OGEL - Incorrect warning message and add red highlight
===========================================================================================
*/
	@SalesOrderId int
AS         
    SELECT  ivl.InvoiceNo        
          , ivl.InvoiceLineId        
          , iv.InvoiceNumber        
          , ivl.Part        
          , ivl.CustomerPart        
          , ivl.ROHS        
          , ivl.ManufacturerNo        
          , mf.ManufacturerCode        
          , mf.ManufacturerName        
          , ivl.ProductNo        
          , pr.ProductName        
    , pr.ProductDescription        
          , ivl.PackageNo        
          , pk.PackageName        
          , ivl.DateCode        
          , iv.InvoiceDate        
          , cu.CurrencyCode        
          , ivl.Price        
          , lgSO.EmployeeName AS SalesmanName        
          , lgShipped.EmployeeName AS ShippedByName        
    ,so.SOSerialNo        
    ,sm.ApprovalName as OGELApprovalStatus  
 ,s.OGEL_Required as OGELRequired  
 ,(CASE 
				WHEN ISNULL(es.OGELNumber, 0) > 0
					AND ISNULL(s.OGEL_Required, 0) = 1
					THEN (SELECT ISNULL((SELECT OgelNumber FROM tbOGELLicense WHERE OgelId = es.OGELNumber AND Inactive = 0), ''))
				ELSE ''
				END
			) AS OGELNumber,
			(CASE WHEN LEN((CASE 
				WHEN ISNULL(es.OGELNumber, 0) > 0
					AND ISNULL(s.OGEL_Required, 0) = 1
					THEN (SELECT ISNULL((SELECT OgelNumber FROM tbOGELLicense WHERE OgelId = es.OGELNumber AND Inactive = 0), ''))
				ELSE ''
				END
			)) > 0 THEN 1 ELSE 0 END) AS ShowOGELWarning
    FROM    dbo.tbSalesOrderLine so        
    JOIN    dbo.tbInvoiceLine ivl ON ivl.SalesOrderLineNo = so.SalesOrderLineId        
    JOIN    dbo.tbInvoice iv ON ivl.InvoiceNo = iv.InvoiceId        
    LEFT JOIN dbo.tbManufacturer mf ON mf.ManufacturerId = ivl.ManufacturerNo        
    LEFT JOIN dbo.tbPackage pk ON pk.PackageId = ivl.PackageNo        
    LEFT JOIN dbo.tbProduct pr ON pr.ProductId = ivl.ProductNo        
    LEFT JOIN dbo.tbLogin lgShipped ON lgShipped.LoginId = ivl.Shippedby        
    LEFT JOIN dbo.tbCurrency cu ON cu.CurrencyId = iv.CurrencyNo        
    LEFT JOIN dbo.tbLogin lgSO ON lgSO.LoginId = iv.Salesman    
 LEFT JOIN tbSO_ExportApprovalStatusOGEL es on es.SalesOrderLineNo = so.SalesOrderLineId        
    LEFT JOIN tbSOExportApprovalStatusMaster sm on sm.ApprovalStatusId = es.ApprovalStatusId  
 LEFT JOIN tbSalesOrder s on s.SalesOrderId = so.SalesOrderNo  
    WHERE   so.SalesOrderNo = @SalesOrderId        
            AND ivl.Inactive = 0        
    ORDER BY InvoiceLineId 
GO


