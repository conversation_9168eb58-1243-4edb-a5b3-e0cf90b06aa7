﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210037]		An.TranTan			22-Oct-2024		UPDATE			Update Advisory notes for tbCompany & tbManufacturer
===========================================================================================
*/
IF COL_LENGTH('dbo.tbCompany', 'AdvisoryNotes') IS NULL
BEGIN
   ALTER TABLE dbo.tbCompany ADD AdvisoryNotes NVARCHAR(50) NULL
END
GO
IF COL_LENGTH('dbo.tbCompany', 'IsDisplayAdvisory') IS NULL
BEGIN
   ALTER TABLE dbo.tbCompany ADD IsDisplayAdvisory BIT NULL
END
GO
IF COL_LENGTH('dbo.tbManufacturer', 'AdvisoryNotes') IS NULL
BEGIN
   ALTER TABLE dbo.tbManufacturer ADD AdvisoryNotes NVARCHAR(50) NULL
END
GO
IF COL_LENGTH('dbo.tbManufacturer', 'IsDisplayAdvisory') IS NULL
BEGIN
   ALTER TABLE dbo.tbManufacturer ADD IsDisplayAdvisory BIT NULL
END
GO