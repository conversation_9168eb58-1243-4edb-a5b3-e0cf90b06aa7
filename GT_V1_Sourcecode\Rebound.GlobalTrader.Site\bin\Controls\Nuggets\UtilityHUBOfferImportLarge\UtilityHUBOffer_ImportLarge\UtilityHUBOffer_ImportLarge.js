Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge=function(n){Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.initializeBase(this,[n]);this._importedFileId=0};Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.prototype={get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_intMasterLoginNo:function(){return this._intMasterLoginNo},set_intMasterLoginNo:function(n){this._intMasterLoginNo!==n&&(this._intMasterLoginNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addCancel(Function.createDelegate(this,this.cancelClicked))},getFormControlID:function(n,t){return String.format("{0}_{1}",n,t)},formShown:function(){},getContact:function(){getCompanyAndOtherMasterData.call(this)},dispose:function(){this.isDisposed||(this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlMail&&this._ctlMail.dispose(),this._ctlMail=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._intMasterLoginNo=null,this._importedFileId=nul,Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.callBaseMethod(this,"dispose"))},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyNPR(this._intNPRID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject(String.format($R_RES.NotifyNPR,this._strNPRNo));this._ctlMail.addNewLoginRecipient(this._intBuyerId,this._strBuyerName)},sendMail:function(){this.validateForm()&&(this.showLoading(!0),this.enableButton(!1),Rebound.GlobalTrader.Site.WebServices.NotifyNPRMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),"",this._intNPRID,this._intGoodsInLineId,$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames,"/"),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupNames,"/"),Function.createDelegate(this,this.sendMailComplete)))},validateForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMailComplete:function(){this.showLoading(!1);this.showSavedOK(!0);location.href=$RGT_gotoURL_GoodsIn(this._intGoodsIn);this.enableButton(!0)},cancelClicked:function(){window.location.href="Default.aspx"},enableButton:function(n){$R_IBTN.enableButton(this._ibtnSend,n);$R_IBTN.enableButton(this._ibtnSend_Footer,n)},importExcelData:function(n,t){$("#divLoader").show();var i=new Rebound.GlobalTrader.Site.Data;i._intTimeoutMilliseconds=2e5;i.set_PathToData("controls/Nuggets/UtilityHUBOfferImportLarge");i.set_DataObject("UtilityHUBOfferImportLarge");i.set_DataAction("ImportData");i.addParameter("originalFilename",n);i.addParameter("generatedFilename",t);i.addDataOK(Function.createDelegate(this,this.importExcelDataOK));i.addError(Function.createDelegate(this,this.importExcelDataError));i.addTimeout(Function.createDelegate(this,this.importExcelDataError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null},importExcelDataOK:function(n){message=n._result.message;$("#divLoader").hide();alert(message);fileId=n._result.FileLogId;this._importedFileId=fileId;PopulateGridForValidation(fileId)},importExcelDataError:function(n){alert(n._errorMessage.split("<br/>")[0]);$("#divLoader").hide()}};Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.UtilityHUBOffer_ImportLarge",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);