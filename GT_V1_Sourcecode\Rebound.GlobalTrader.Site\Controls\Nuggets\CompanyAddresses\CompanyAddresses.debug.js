///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

//-----------------------------------------------------------------------------------------
// RP 30.03.2010:
// - ensure line is fully loaded before we allow edits (to stop the wrong data being
//   edited)
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript
//Marker     Changed by      Date         Remarks
//[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//[002]      Vinay           13/03/2014   EMS Ref No: 104
//[003]      Aashu Singh     13-Sep-2018    [REB-12820]:Provision to add Global Security on Contact Section
//[004]      Ravi            31-03-2023     [RP-1224]
//[005]      Tanbir Akhtar   28-0602023   [RP-1668]error on rendering Special character on main screen 
//[006]      Devendra Sikarwar 11-12-2023 [RP-2719] Prod Issue removing default Comapny Vat No on Address Vat No field
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._intCompanyAddressID = -1;
	this._strCompanyName = "";
	this._blnLineLoaded = false;
    this._blnEditHubSupplier = true;
    this._intCompanyAddressIDNew = -1;
    this._inactive = false;

    //[004] start
    //this._EORINumber = "";
    //this._telephone = "";
    //this._CompanyRegistrationNo = "";
    this._CompanyAddressVatNo = "";
    this._companyVatNo = "";
    //this._blnCompanyAddressVatUpdate = false; // in [RP-1224] jira comments Jeff mentioned - N.B. Do not overwrite any existing VAT numbers in the address - only update if blank
    //[004] end
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
    get_intCompanyAddressID: function() { return this._intCompanyAddressID; }, set_intCompanyAddressID: function(value) { if (this._intCompanyAddressID !== value) this._intCompanyAddressID = value; },
    get_strAddressType: function() { return this._strAddressType; }, set_strAddressType: function(value) { if (this._strAddressType !== value) this._strAddressType = value; },
    get_tblAddresses: function() { return this._tblAddresses; }, set_tblAddresses: function(value) { if (this._tblAddresses !== value) this._tblAddresses = value; },
    get_lblType: function() { return this._lblType; }, set_lblType: function(value) { if (this._lblType !== value) this._lblType = value; },
    get_ibtnDefaultBill: function() { return this._ibtnDefaultBill; }, set_ibtnDefaultBill: function(value) { if (this._ibtnDefaultBill !== value) this._ibtnDefaultBill = value; },
    get_ibtnDefaultShip: function() { return this._ibtnDefaultShip; }, set_ibtnDefaultShip: function(value) { if (this._ibtnDefaultShip !== value) this._ibtnDefaultShip = value; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(value) { if (this._ibtnAdd !== value) this._ibtnAdd = value; },
    get_ibtnCease: function() { return this._ibtnCease; }, set_ibtnCease: function(value) { if (this._ibtnCease !== value) this._ibtnCease = value; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_pnlLoadingAddressInfo: function() { return this._pnlLoadingAddressInfo; }, set_pnlLoadingAddressInfo: function(value) { if (this._pnlLoadingAddressInfo !== value) this._pnlLoadingAddressInfo = value; },
    get_pnlAddressError: function() { return this._pnlAddressError; }, set_pnlAddressError: function(value) { if (this._pnlAddressError !== value) this._pnlAddressError = value; },
    get_pnlAddressInfo: function() { return this._pnlAddressInfo; }, set_pnlAddressInfo: function(value) { if (this._pnlAddressInfo !== value) this._pnlAddressInfo = value; },
    get_lblAddressName: function() { return this._lblAddressName; }, set_lblAddressName: function(value) { if (this._lblAddressName !== value) this._lblAddressName = value; },
    get_strCompanyName: function() { return this._strCompanyName; }, set_strCompanyName: function(value) { if (this._strCompanyName !== value) this._strCompanyName = value; },
    get_blnCanEditTax: function() { return this._blnCanEditTax; }, set_blnCanEditTax: function(value) { if (this._blnCanEditTax !== value) this._blnCanEditTax = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.callBaseMethod(this, "initialize");

        //data
        this._strPathToData = "controls/Nuggets/CompanyAddresses";
        this._strDataObject = "CompanyAddresses";

        //events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));
        this.addGetDataClicked(Function.createDelegate(this, this.initialGetData));
        this._tblAddresses.addSelectedIndexChanged(Function.createDelegate(this, this.tblAddresses_SelectedIndexChanged));

        //Add or Edit form
        if (this._ibtnAdd || this._ibtnEdit) {
            if (this._ibtnAdd) $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            if (this._ibtnEdit) $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmAddEdit = $find(this._aryFormIDs[0]);
            this._frmAddEdit.addCancel(Function.createDelegate(this, this.cancelAddEdit));
            this._frmAddEdit.addSaveComplete(Function.createDelegate(this, this.saveAddEditComplete));
            this._frmAddEdit._intCompanyID = this._intCompanyID;
            this._frmAddEdit._blnCanEditTax = this._blnCanEditTax;
        }

        //Confirmation form
        if (this._ibtnCease || this._ibtnDefaultBill) {
            if (this._ibtnCease) $R_IBTN.addClick(this._ibtnCease, Function.createDelegate(this, this.showCeaseForm));
            if (this._ibtnDefaultBill) $R_IBTN.addClick(this._ibtnDefaultBill, Function.createDelegate(this, this.showDefaultBillForm));
            if (this._ibtnDefaultShip) $R_IBTN.addClick(this._ibtnDefaultShip, Function.createDelegate(this, this.showDefaultShipForm));
            this._frmConfirm = $find(this._aryFormIDs[1]);
            this._frmConfirm.addCancel(Function.createDelegate(this, this.hideConfirmForm));
            this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveCeaseComplete));
            this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));
            this._frmConfirm._intCompanyID = this._intCompanyID;
            this._frmConfirm._strAddressType = this._strAddressType;
        }
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnCease) $R_IBTN.clearHandlers(this._ibtnCease);
        if (this._ibtnDefaultBill) $R_IBTN.clearHandlers(this._ibtnDefaultBill);
        if (this._ibtnDefaultShip) $R_IBTN.clearHandlers(this._ibtnDefaultShip);
        if (this._frmAddEdit) this._frmAddEdit.dispose();
        if (this._frmConfirm) this._frmConfirm.dispose();
        if (this._tblAddresses) this._tblAddresses.dispose();
        this._frmAddEdit = null;
        this._frmConfirm = null;
        this._tblAddresses = null;
        this._lblType = null;
        this._ibtnDefaultBill = null;
        this._ibtnDefaultShip = null;
        this._ibtnAdd = null;
        this._ibtnCease = null;
        this._ibtnEdit = null;
        this._pnlLoadingAddressInfo = null;
        this._pnlAddressError = null;
        this._pnlAddressInfo = null;
        this._lblAddressName = null;
        this._blnCanEditTax = null;
        this._blnEditHubSupplier = null;
        this._inactive = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.callBaseMethod(this, "dispose");
    },

    initialGetData: function() {
        this.showContent(true);
        this.getData();
        this.getCompanyInactive();
    },

    getData: function () {
        this.getData_Start();
        this.enableEditButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetAddressList");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function(args) {
        this.getDataOK_Start();
        var result = args._result;
        this._tblAddresses.clearTable();
        if (result.Addresses) {
            for (var i = 0; i < result.Addresses.length; i++) {
                var row = result.Addresses[i];
                var aryData = [
					$R_FN.setCleanTextValue(row.Name)
				, $R_FN.setCleanTextValue(row.Long)
				, (row.DefaultBill) ? $R_RES.Yes : "-"
				, (row.DefaultShip) ? $R_RES.Yes : "-"
				];
                var strCss = (row.DefaultBill || row.DefaultShip) ? "defaultAddress" : "";
                this._tblAddresses.addRow(aryData, row.ID, (row.ID == this._intCompanyAddressID), null, strCss);
                row = null;
            }
            this._strDefaultAddress = result.DefaultAddress;
        }
        this._tblAddresses.resizeColumns();
        if (this._intCompanyAddressID > 0) this.getAddressData();
        this.getDataOK_End();
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getCompanyInactive: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetCompanyDetailInactive");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getCompanyInactiveOK));
        obj.addError(Function.createDelegate(this, this.getCompanyInactiveError));
        obj.addTimeout(Function.createDelegate(this, this.getCompanyInactiveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCompanyInactiveOK: function (args) {
        var result = args._result;
        if (result) {
            this._inactive = result.Inactive;
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._inactive);
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive);
            if (this._ibtnCease) $R_IBTN.enableButton(this._ibtnCease, !this._inactive);
            if (this._ibtnDefaultBill) $R_IBTN.enableButton(this._ibtnDefaultBill, !this._inactive);
            if (this._ibtnDefaultShip) $R_IBTN.enableButton(this._ibtnDefaultShip, !this._inactive);
        }
    },

    getCompanyInactiveError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    enableEditButtons: function(blnEnable) {
        if (blnEnable) {
            var blnDefaultBill = (this._tblAddresses.getSelectedCellValue(2) != "-");
            var blnDefaultShip = (this._tblAddresses.getSelectedCellValue(3) != "-");
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._inactive && this._blnEditHubSupplier);
            if (this._ibtnDefaultBill) $R_IBTN.enableButton(this._ibtnDefaultBill, !this._inactive && !blnDefaultBill && this._blnLineLoaded && this._blnEditHubSupplier);
            if (this._ibtnDefaultShip) $R_IBTN.enableButton(this._ibtnDefaultShip, !this._inactive && !blnDefaultShip && this._blnLineLoaded && this._blnEditHubSupplier);
            if (this._ibtnCease) $R_IBTN.enableButton(this._ibtnCease, !this._inactive && !blnDefaultBill && !blnDefaultShip && this._blnLineLoaded && this._blnEditHubSupplier);
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive && this._blnLineLoaded && this._blnEditHubSupplier);
        } else {
            if (this._ibtnDefaultBill) $R_IBTN.enableButton(this._ibtnDefaultBill, false);
            if (this._ibtnDefaultShip) $R_IBTN.enableButton(this._ibtnDefaultShip, false);
            if (this._ibtnCease) $R_IBTN.enableButton(this._ibtnCease, false);
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
        }
    },

    showAddForm: function () {
        //[003] start
        this._frmAddEdit._globalLoginClientNo = this._globalLoginClientNo;
        //[003] end
        this._frmAddEdit.changeMode("ADD");
        this.showForm(this._frmAddEdit, true);        
        this._frmAddEdit._blnCanEditTax = this._blnCanEditTax;
        this._frmAddEdit.setFieldValue("ctlName", this._strCompanyName);
        this._frmAddEdit.setFieldValue("ctlAddressName", this._strCompanyName);
        this._frmAddEdit.setFieldValue("ctlTax_Label", "");
    },

    showEditForm: function () {

        

        this._frmAddEdit.changeMode("EDIT");
        //[003] start
        this._frmAddEdit._globalLoginClientNo = this._globalLoginClientNo;
        //[003] end
        this.showForm(this._frmAddEdit, true);
        //this._frmAddEdit._intCompanyAddressID = this._intCompanyAddressID;
        this._frmAddEdit._intCompanyAddressID = this._intCompanyAddressIDNew;
        this._frmAddEdit._intCompanyID = this._intCompanyID;
        this._frmAddEdit.setFieldValue("ctlName", this._strCompanyName);
        this._frmAddEdit.setFieldValue("ctlAddressName", this.getFieldValue("hidName"));
        this._frmAddEdit.setFieldValue("ctlLine1", this.getFieldValue("hidLine1"));
        this._frmAddEdit.setFieldValue("ctlLine2", this.getFieldValue("hidLine2"));
        this._frmAddEdit.setFieldValue("ctlLine3", this.getFieldValue("hidLine3"));
        this._frmAddEdit.setFieldValue("ctlTown", this.getFieldValue("hidTown"));
        this._frmAddEdit.setFieldValue("ctlCounty", this.getFieldValue("hidCounty"));
        this._frmAddEdit.setFieldValue("ctlState", this.getFieldValue("hidState"));
        this._frmAddEdit.setFieldValue("ctlCountry", this.getFieldValue("hidCountryID"));
        this._frmAddEdit.setFieldValue("ctlPostcode", this.getFieldValue("hidPostcode"));
        this._frmAddEdit.setFieldValue("ctlNotes", this.getFieldValue("ctlNotes"));
        this._frmAddEdit.setFieldValue("ctlShipVia", this.getFieldValue("hidShipViaNo"));
        this._frmAddEdit.setFieldValue("ctlShipViaAccount", this.getFieldValue("ctlShipViaAccount"));
        this._frmAddEdit.setFieldValue("ctlShippingNotes", this.getFieldValue("ctlShippingNotes"));

        this._frmAddEdit.setFieldValue("ctlTax", this.getFieldValue("hidTaxbyAddress"));
        this._frmAddEdit.setFieldValue("ctlTax_Label", this.getFieldValue("ctlTaxbyAddress"));
        //[002] code start
        this._frmAddEdit.setFieldValue("ctlVatNo", this.getFieldValue("ctlVatNo")); //[004] condition implemented 
        this._frmAddEdit.setFieldValue("ctlVatNo_Label", this.getFieldValue("ctlVatNo")); // show vat no in a lable
        //[002] code end  
        this._frmAddEdit._blnCanEditTax = this._blnCanEditTax;
        //[001] code start
        this._frmAddEdit.setFieldValue("ctlIncoterm", this.getFieldValue("hidIncotermNo"));
        //[001] code end
        this._frmAddEdit.setFieldValue("ctlRecieveNotes", this.getFieldValue("ctlRecievingNotes"));
        this._frmAddEdit.setFieldValue("ctlRegion", this.getFieldValue("hidRegion"));

        this._frmAddEdit.setFieldValue("ctlDivisionHeader", this.getFieldValue("hidDivisionHeader"));
        this._frmAddEdit.setFieldValue("ctlLabelType", this.getFieldValue("hidLabelType"));
        this.CheckCompanyAddressVatUpdatable();
        //[RP-1224] start
        //
   
        /*this._frmAddEdit.setFieldValue("ctlVatNo", this.getFieldValue("CompanyVatNo"));*/
        //this._frmAddEdit.setFieldValue("CtlEORI", this._EORINumber);
        //this._frmAddEdit.setFieldValue("CtlTelephone", this._telephone);

       //[RP-1224] end
    },

    tblAddresses_SelectedIndexChanged: function () {
        //
        this._intCompanyAddressID = this._tblAddresses._varSelectedValue;
        this._intCompanyAddressIDNew = this._tblAddresses._varSelectedValue;
        this.getAddressData();
        this.enableEditButtons(!this._inactive);
        //$R_FN.scrollPageToElement(this._element);
    },

    getAddressData: function() {
        this.showLoading(true);
        this._blnLineLoaded = false;
        this.enableEditButtons(false);
        $R_FN.showElement(this._pnlAddressInfo, false);
        $R_FN.showElement(this._pnlLoadingAddressInfo, true);
        $R_FN.showElement(this._pnlAddressError, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetAddress");
        obj.addParameter("ID", this._intCompanyAddressID);
        obj.addDataOK(Function.createDelegate(this, this.getAddressDataOK));
        obj.addError(Function.createDelegate(this, this.getAddressDataError));
        obj.addTimeout(Function.createDelegate(this, this.getAddressDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getAddressDataOK: function (args) {
        
        $R_FN.showElement(this._pnlAddressError, false);
        var result = args._result;
        //Code Start[005]
        $R_FN.setInnerHTML(this._lblAddressName, $R_FN.setCleanTextValue(result.Name));
        //Code End [005]
        this.setFieldValue("ctlAddress", result.Long);
        this.setFieldValue("ctlNotes", result.Notes);
        this.setFieldValue("ctlShipVia", result.ShipVia);
        this.setFieldValue("ctlShipViaAccount", result.ShipViaAccount);
        this.setFieldValue("hidName", result.Name);
        this.setFieldValue("hidLine1", result.Address1);
        this.setFieldValue("hidLine2", result.Address2);
        this.setFieldValue("hidLine3", result.Address3);
        this.setFieldValue("hidTown", result.City);
        this.setFieldValue("hidCounty", result.County);
        this.setFieldValue("hidState", result.State);
        this.setFieldValue("hidCountryID", result.CountryID);
        this.setFieldValue("hidShipViaNo", result.ShipViaNo);
        this.setFieldValue("hidPostcode", result.ZIP);
        this.setFieldValue("ctlDefaultBill", result.DefaultBill);
        this.setFieldValue("ctlDefaultShip", result.DefaultShip);
        this.setFieldValue("ctlShippingNotes", result.ShippingNotes);
        //ESMS #14--
        this.setFieldValue("hidTaxbyAddress", result.TaxbyAddress);        
        this.setFieldValue("ctlTaxbyAddress", result.TaxValue);
        // END
        //[001] code start
        this.setFieldValue("hidIncotermNo", result.IncotermNo);
        this.setFieldValue("ctlIncoterm", result.Incoterm);
        //[001] code end
        //[002] code start
        this.setFieldValue("ctlVatNo", result.VatNo);
        //[002] code end
        this.setFieldValue("ctlRecievingNotes", result.RNotes);

        this.setFieldValue("hidRegion", result.RegionNo);
        this.setFieldValue("ctlRegion", result.Region);

        this.setFieldValue("hidDivisionHeader", result.DivisionHeaderNo);
        this.setFieldValue("ctlDivisionHeader", $R_FN.setCleanTextValue(result.DivisionHeaderName));
        this.setFieldValue("hidLabelType", result.LabelTypeNo);
        this.setFieldValue("ctlLabelType", $R_FN.setCleanTextValue(result.LabelTypeName));

        //[004] start
        //[006] start
        /*
        if (result.VatNo == null || result.VatNo == '') {
            // if vat no in company address is null than show company vat address in companyAddressLine
            this._companyVatNo = $("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlVATNumber_lbl").text();
            this._CompanyAddressVatNo = result.VatNo;

            this.setFieldValue("ctlVatNo", this._companyVatNo );
        }
        */
        this.setFieldValue("ctlVatNo", result.VatNo);
         //[006] end
        this.setFieldValue("ctlEORINo", $("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlEORINumber_lbl").text());
        this.setFieldValue("CtlTelephoneNo", $("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_ctl13_ctlTel_lbl").text());
        this.setFieldValue("ctlCmpRegNo", result.CompanyRegNo)

        //[004] end
        $R_FN.showElement(this._pnlAddressInfo, true);
        $R_FN.showElement(this._pnlLoadingAddressInfo, false);
        this.showLoading(false);
        this._blnLineLoaded = true;
        this.enableEditButtons(!this._inactive);
    },

    getAddressDataError: function(args) {
        this.showLoading(false);
        $R_FN.showElement(this._pnlLoadingAddressInfo, false);
        $R_FN.showElement(this._pnlAddressError, true);
        $R_FN.setInnerHTML(this._pnlAddressError, args.get_ErrorMessage());
    },

    cancelAddEdit: function() {
        this.showForm(this._frmAddEdit, false);
    },

    saveAddEditComplete: function() {
        this.showForm(this._frmAddEdit, false);
        this.getData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onSaveEditComplete();
    },

    showCeaseForm: function() {
        this._frmConfirm.changeMode("CEASE");
        this.showConfirmForm();
    },

    showConfirmForm: function() {
        this._frmConfirm._intCompanyAddressID = this._intCompanyAddressID;
        this._frmConfirm.setFieldValue("ctlAddress", this._tblAddresses.getSelectedCellValue(1));
        this.showForm(this._frmConfirm, true);
    },

    hideConfirmForm: function() {
        this.showForm(this._frmConfirm, false);
    },

    saveCeaseComplete: function() {
        this.hideConfirmForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intCompanyAddressID = -1;
        this.getData();
    },

    showDefaultBillForm: function() {
        this._frmConfirm.changeMode("DEFAULT_BILL");
        this.showConfirmForm();
    },

    showDefaultShipForm: function() {
        this._frmConfirm.changeMode("DEFAULT_SHIP");
        this.showConfirmForm();
    },
    //[004] start
    CheckCompanyAddressVatUpdatable: function() {
        //
        // below code is to disable vat number on edit page if vat number is already exists 
        //this._CompanyAddressVatNo = this.getFieldValue("ctlVatNo");
        //if (this._CompanyAddressVatNo.trim() == '' || this._CompanyAddressVatNo.trim().length == 0) {
        //    this._blnCompanyAddressVatUpdate = true;
        //    $("#ctl00_cphMain_ctlAddresses_ctlDB_ctl14_ctlAddEdit_ctlDB_ctlVatNo_ctl03_txtVatNo").removeAttr("disabled");

        //    $("#ctl00_cphMain_ctlAddresses_ctlDB_ctl14_ctlAddEdit_ctlDB_ctlVatNo").removeClass("invisible")
        //    $("#ctl00_cphMain_ctlAddresses_ctlDB_ctl14_ctlAddEdit_ctlDB_ctlVatNo_Label").addClass("invisible")
            
        //} else {
        //    this._blnCompanyAddressVatUpdate = false;
        //    $("#ctl00_cphMain_ctlAddresses_ctlDB_ctl14_ctlAddEdit_ctlDB_ctlVatNo_ctl03_txtVatNo").attr("disabled", true);

        //    $("#ctl00_cphMain_ctlAddresses_ctlDB_ctl14_ctlAddEdit_ctlDB_ctlVatNo").addClass("invisible")
        //    $("#ctl00_cphMain_ctlAddresses_ctlDB_ctl14_ctlAddEdit_ctlDB_ctlVatNo_Label").removeClass("invisible")
        //}
    }
     //[004] end
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyAddresses", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
