Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.initializeBase(this,[n]);this._intNewID=0;this._intCompanyID=0;this._intLoginID=0;this._intContactID=0;this._intDivisionID=0;this._strCompanyName="";this._strContactName="";this._strSearchCompanyName=""};Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.prototype={get_ctlSelectInvoice:function(){return this._ctlSelectInvoice},set_ctlSelectInvoice:function(n){this._ctlSelectInvoice!==n&&(this._ctlSelectInvoice=n)},get_intLoginID:function(){return this._intLoginID},set_intLoginID:function(n){this._intLoginID!==n&&(this._intLoginID=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_strContactName:function(){return this._strContactName},set_strContactName:function(n){this._strContactName!==n&&(this._strContactName=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},get_strSearchCompanyName:function(){return this._strSearchCompanyName},set_strSearchCompanyName:function(n){this._strSearchCompanyName!==n&&(this._strSearchCompanyName=n)},get_intDefaultWarehouseID:function(){return this._intDefaultWarehouseID},set_intDefaultWarehouseID:function(n){this._intDefaultWarehouseID!==n&&(this._intDefaultWarehouseID=n)},get_intQSInvoiceID:function(){return this._intQSInvoiceID},set_intQSInvoiceID:function(n){this._intQSInvoiceID!==n&&(this._intQSInvoiceID=n)},initialize:function(){var n,t;Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.callBaseMethod(this,"initialize");this._ctlMail=$find(this.getField("ctlSendMailMessage").ID);this._ctlMail._ctlRelatedForm=this;this.addCancel(Function.createDelegate(this,this.cancelClicked));this.addSave(Function.createDelegate(this,this.saveClicked));this.addShown(Function.createDelegate(this,this.formShown));this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged));this._ctlSelectInvoice.addItemSelected(Function.createDelegate(this,this.selectInvoice));this.addFieldCheckBoxClickEvent("ctlSendMail",Function.createDelegate(this,this.chooseIfSendMail));n=Function.createDelegate(this,this.finishedForm);$R_IBTN.addClick(this._ibtnContinue,n);$R_IBTN.addClick(this._ibtnContinue_Footer,n);t=Function.createDelegate(this,this.sendMail);$R_IBTN.addClick(this._ibtnSend,t);$R_IBTN.addClick(this._ibtnSend_Footer,t);this._intQSInvoiceID!=null&&this._intQSInvoiceID>0&&this.getInvoiceFromInvoiceDetail()},dispose:function(){this.isDisposed||(this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlMail&&this._ctlMail.dispose(),this._ctlSelectInvoice&&this._ctlSelectInvoice.dispose(),this._ctlMultiStep&&this._ctlMultiStep.dispose(),this._ctlMail=null,this._ctlSelectInvoice=null,this._ctlMultiStep=null,this._intLoginID=null,this._intCompanyID=null,this._strCompanyName=null,this._intContactID=null,this._strContactName=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._strSearchCompanyName=null,this._intQSInvoiceID=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.callBaseMethod(this,"dispose"))},formShown:function(){this.resetSteps();this._strSearchCompanyName?(this._ctlSelectInvoice.setFieldValue("ctlCompany",this._strSearchCompanyName),this._ctlSelectInvoice.searchClicked()):this._intCompanyID>0&&this.doInitialCompanySearch();this._intQSInvoiceID!=null&&this._intQSInvoiceID>0?this.gotoStep(2):this.gotoStep(1)},doInitialCompanySearch:function(){this._ctlSelectInvoice._initialized||setTimeout(Function.createDelegate(this,this.doInitialCompanySearch),100);this._strCompanyName!=""&&this._ctlSelectInvoice.setFieldValue("ctlCompany",this._strCompanyName);this._strContactName!=""&&this._ctlSelectInvoice.setFieldValue("ctlContact",this._strContactName);this._strCompanyName!=""&&this._strContactName!=""&&this._ctlSelectInvoice.getData()},getInvoice:function(){this.showInvoiceUpdateFieldsLoading(!0);$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/InvoiceMainInfo");n.set_DataObject("InvoiceMainInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intInvoiceID);n.addDataOK(Function.createDelegate(this,this.getInvoiceOK));n.addError(Function.createDelegate(this,this.getInvoiceError));n.addTimeout(Function.createDelegate(this,this.getInvoiceError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getInvoiceOK:function(n){var t=n._result;this.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(t.Customer));this.setFieldValue("ctlShipToAddress",$R_FN.setCleanTextValue(t.BillToAddress));this.setFieldValue("ctlInvoiceNumber",t.InvoiceNumber);this.setFieldValue("ctlContact",$R_FN.setCleanTextValue(t.Contact));this.setFieldValue("ctlDivision",$R_FN.setCleanTextValue(t.DivisionName));this.setFieldValue("ctlRMADate",$R_FN.shortDate());this.getFieldDropDownData("ctlWarehouse");this.getFieldDropDownData("ctlShipVia");this.getFieldDropDownData("ctlAuthorisedBy");this.getFieldDropDownData("ctlIncoterm");this._intCompanyID=t.CustomerNo;this._intContactID=t.ContactNo;this._intDivisionID=t.DivisionNo;this.setFieldValue("ctlAuthorisedBy",this._intLoginID);this.setFieldValue("ctlIncoterm",t.DefaultIncotermNo);this.showInvoiceUpdateFieldsLoading(!1);this.setFieldValue("ctlAS6081",t.AS6081==!0?"Yes":"No")},getInvoiceError:function(n){this.showInvoiceUpdateFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},showInvoiceUpdateFieldsLoading:function(n){this.showFieldLoading("ctlCompany",n);this.showFieldLoading("ctlShipToAddress",n);this.showFieldLoading("ctlInvoiceNumber",n);this.showFieldLoading("ctlContact",n);this.showFieldLoading("ctlDivision",n);this.showFieldLoading("ctlRMADate",n);this.showFieldLoading("ctlAuthorisedBy",n)},cancelClicked:function(){$R_FN.navigateBack()},stepChanged:function(){var n=this._ctlMultiStep._intCurrentStep;$R_IBTN.showButton(this._ibtnSend,n==3);$R_IBTN.showButton(this._ibtnSend_Footer,n==3);$R_IBTN.enableButton(this._ibtnSave,n==2);$R_IBTN.enableButton(this._ibtnSave_Footer,n==2);$R_IBTN.showButton(this._ibtnSave,n!=3);$R_IBTN.showButton(this._ibtnSave_Footer,n!=3);$R_IBTN.showButton(this._ibtnCancel,n!=3);$R_IBTN.showButton(this._ibtnCancel_Footer,n!=3);$R_IBTN.showButton(this._ibtnContinue,n==3);$R_IBTN.showButton(this._ibtnContinue_Footer,n==3);this._ctlMultiStep.showSteps(n!=3);n==1&&this._ctlSelectInvoice.resizeColumns();n==2&&this.setFieldValue("ctlWarehouse",this._intDefaultWarehouseID);n==3&&(this.getMessageText(),this.setFieldValue("ctlSendMail",!1),this.showMailButtons())},selectInvoice:function(){this._intInvoiceID=this._ctlSelectInvoice.getSelectedID();this.getInvoice();this.nextStep()},saveClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAAdd");n.set_DataObject("CRMAAdd");n.set_DataAction("AddNew");n.addParameter("AuthorisedBy",this.getFieldValue("ctlAuthorisedBy"));n.addParameter("ShipViaNo",this.getFieldValue("ctlShipVia"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("Instructions",this.getFieldValue("ctlInstructions"));n.addParameter("Account",this.getFieldValue("ctlAccount"));n.addParameter("RMADate",this.getFieldValue("ctlRMADate"));n.addParameter("InvoiceNo",this._intInvoiceID);n.addParameter("WarehouseNo",this.getFieldValue("ctlWarehouse"));n.addParameter("CMNo",this._intCompanyID);n.addParameter("ContactNo",this._intContactID);n.addParameter("DivisionNo",this._intDivisionID);n.addParameter("IncotermNo",this.getFieldValue("ctlIncoterm"));n.addParameter("CustomerRejectionNo",this.getFieldValue("ctlCustomerRejectionNo"));n.addParameter("AS6081",this.connvertAs6081ToBoolean());n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.NewID>0?(this._intNewID=n._result.NewID,this.showSaving(!1),this.showInnerContent(!0),this.nextStep()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this._ctlMultiStep._intCurrentStep==2&&(this.checkFieldEntered("ctlWarehouse")||(n=!1),this.checkFieldEntered("ctlShipVia")||(n=!1),this.checkFieldEntered("ctlAuthorisedBy")||(n=!1),this.checkFieldEntered("ctlRMADate")||(n=!1),this.checkFieldEntered("ctlIncoterm")||(n=!1)),this._ctlMultiStep._intCurrentStep==3&&(this._ctlMail.validateFields()||(n=!1)),n||this.showError(!0),n},showMailButtons:function(){var n=this.getFieldValue("ctlSendMail");this.showField("ctlSendMailMessage",n);$R_IBTN.showButton(this._ibtnSend,n);$R_IBTN.showButton(this._ibtnSend_Footer,n);$R_IBTN.showButton(this._ibtnContinue,!n);$R_IBTN.showButton(this._ibtnContinue_Footer,!n)},chooseIfSendMail:function(){this.showMailButtons()},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewCustomerRMA(this._intNewID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject($R_RES.NewCustomerRMAAdded)},validateMailForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMail:function(){this.validateMailForm()&&(Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),this._intNewID,Function.createDelegate(this,this.sendMailComplete)),$R_IBTN.showButton(this._ibtnSave,!1),$R_IBTN.showButton(this._ibtnSave_Footer,!1),$R_IBTN.showButton(this._ibtnSend,!1),$R_IBTN.showButton(this._ibtnSend_Footer,!1))},sendMailComplete:function(){this.finishedForm()},finishedForm:function(){this._ctlMultiStep.showExplainLabel(!1);this._ctlMultiStep.showSteps(!1);$R_IBTN.showButton(this._ibtnSave,!1);$R_IBTN.showButton(this._ibtnSave_Footer,!1);$R_IBTN.showButton(this._ibtnSend,!1);$R_IBTN.showButton(this._ibtnSend_Footer,!1);this.showSavedOK(!0);this.onSaveComplete()},getInvoiceFromInvoiceDetail:function(){this._intInvoiceID=this._intQSInvoiceID;this.getInvoice();this.nextStep()},connvertAs6081ToBoolean:function(){return selectedvalue=this.getFieldValue("ctlAS6081"),this._AS6081=selectedvalue=="Yes"?!0:!1,this._AS6081}};Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);