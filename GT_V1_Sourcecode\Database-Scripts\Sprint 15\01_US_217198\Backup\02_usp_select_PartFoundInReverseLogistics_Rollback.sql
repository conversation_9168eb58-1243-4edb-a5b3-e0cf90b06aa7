﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_select_PartFoundInReverseLogistics]    Script Date: 11/15/2024 5:36:50 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
-- ====================================================================================================================================================================================
-- TASK      		UPDATED BY     		DATE         ACTION 	DESCRIPTION                                    
-- 001 				Devendra <PERSON>  	20-12-2023   Update		RP-2722/RP-2528 (The email notification should not be going to the RL team if they have offers with 0 quantity for that HUB RFQ.)
-- US-201305		Phuc.HoangDinh		24-04-2024	 Update		Update for US-201305 [Production bug] RL team are not being notified when new requirement has been raised	
-- US-220810		Phuc.HoangDinh		13-Nov-2024	 Update		[UAT Bug] Issue when sending Customer Requirements to HUB
-- ====================================================================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_PartFoundInReverseLogistics]                                          
	@PartNo NVARCHAR(128) = NULL               
AS                                   
BEGIN                            
    DECLARE @fullPartNo NVARCHAR(128) = [dbo].[ufn_get_fullpart](@PartNo)

	SELECT DISTINCT TOP 1 COUNT(ReverseLogisticid) AS Partcount, Part
	FROM BorisGlobalTraderImports.dbo.tbReverseLogistic
	WHERE
		/*[001]*/
		--  part=@PartNo 
		-- [dbo].[ufn_get_fullpart] will be trim '%'
		-- Example: SELECT  [dbo].[ufn_get_fullpart]('SA606DK%') as Part => Result: 'SA606DK'
		FullPart LIKE ISNULL(@fullPartNo,'') + '%'
		AND ISNULL(Quantity, 0) <> 0
	/*[001]*/
	GROUP BY ReverseLogisticid, Part             
                                  
END 

GO


