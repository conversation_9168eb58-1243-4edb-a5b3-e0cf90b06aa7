Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.ImageCheckBox=function(n){Rebound.GlobalTrader.Site.Controls.ImageCheckBox.initializeBase(this,[n]);this._blnChecked=!1};Rebound.GlobalTrader.Site.Controls.ImageCheckBox.prototype={get_blnChecked:function(){return this._blnChecked},set_blnChecked:function(n){this._blnChecked!==n&&(this._blnChecked=n)},get_blnEnabled:function(){return this._blnEnabled},set_blnEnabled:function(n){this._blnEnabled!==n&&(this._blnEnabled=n)},get_img:function(){return this._img},set_img:function(n){this._img!==n&&(this._img=n)},addClick:function(n){this.get_events().addHandler("Click",n)},removeClick:function(n){this.get_events().removeHandler("Click",n)},onClick:function(){var n=this.get_events().getHandler("Click");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ImageCheckBox.callBaseMethod(this,"initialize");this.enableButton(this._blnEnabled)},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._blnChecked=null,this._blnEnabled=null,this._img=null,Rebound.GlobalTrader.Site.Controls.ImageCheckBox.callBaseMethod(this,"dispose"),this.isDisposed=!0)},enableButton:function(n){this._blnEnabled=n;this.get_element().className=this._blnEnabled?"imageCheckBox":"imageCheckBoxDisabled";this._blnEnabled?($clearHandlers(this.get_element()),$addHandler(this.get_element(),"click",Function.createDelegate(this,this.toggleChecked))):$clearHandlers(this.get_element())},setChecked:function(n){this._blnChecked=n;this.updateDisplay()},toggleChecked:function(){this._blnChecked=!this._blnChecked;this.updateDisplay();this.onClick()},updateDisplay:function(){this._img.className=this._blnChecked?"on":"off";this._beingHovered&&(this._img.className+="Over")},onMouseOver:function(){this._beingHovered=!0;this.updateDisplay()},onMouseOut:function(){this._beingHovered=!1;this._img.className=this._blnChecked?"on":"off"}};Rebound.GlobalTrader.Site.Controls.ImageCheckBox.registerClass("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",Sys.UI.Control,Sys.IDisposable);