///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.prototype = {

    get_intEPRID: function() { return this._intEPRID; }, set_intEPRID: function(v) { if (this._intEPRID !== v) this._intEPRID = v; },
    get_ibtnSend: function() { return this._ibtnSend; }, set_ibtnSend: function(value) { if (this._ibtnSend !== value) this._ibtnSend = value; },
    get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    get_strMessageText: function() { return this._strMessageText; }, set_strMessageText: function(v) { if (this._strMessageText !== v) this._strMessageText = v; },
    get_strEPRNo: function() { return this._strEPRNo; }, set_strEPRNo: function(v) { if (this._strEPRNo !== v) this._strEPRNo = v; },
    get_intPO: function() { return this._intPO; }, set_intPO: function(v) { if (this._intPO !== v) this._intPO = v; },
    get_intPONumber: function() { return this._intPONumber; }, set_intPONumber: function(v) { if (this._intPONumber !== v) this._intPONumber = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addCancel(Function.createDelegate(this, this.cancelClicked));
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
            $R_IBTN.addClick(this._ibtnSend, Function.createDelegate(this, this.sendMail));
            $R_IBTN.addClick(this._ibtnSend_Footer, Function.createDelegate(this, this.sendMail));
            this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
            this._ctlMail._ctlRelatedForm = this;
            this.getMessageTextComplete(this._strMessageText);
        }
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlMail) this._ctlMail.dispose();
        this._ctlMail = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._intPO = null;
        this._strEPRNo = null;
        this._strMessageText = null;
        this._intEPRID = null;
        this._intPONumber = null;
        Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.callBaseMethod(this, "dispose");
    },

    getMessageText: function() {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyNPR(this._intEPRID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function(strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject(String.format($R_RES.NotifyEPR, this._strEPRNo));
        //  this._ctlMail.addNewLoginRecipient(this._intBuyerId, this._strBuyerName);
    },
    //[001] code start
    sendMail: function() {
        if (!this.validateForm()) return;
        this.showLoading(true);
        this.enableButton(false);
        Rebound.GlobalTrader.Site.WebServices.NotifyEPRMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), "", this._intEPRID, this._intPO, $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames, "/"), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupNames, "/"),this._intPONumber, Function.createDelegate(this, this.sendMailComplete));
    },
    //[001] code end
    validateForm: function() {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMailComplete: function() {
        this.showLoading(false);
        this.showSavedOK(true);
        location.href = $RGT_gotoURL_PurchaseOrder(this._intPO);
        this.enableButton(true);
    },
    cancelClicked: function() {
        if (this._intPO <= 0)
            $R_FN.navigateBack();
        else
            location.href = $RGT_gotoURL_PurchaseOrder(this._intPO);
    },
    enableButton: function(bln) {
        $R_IBTN.enableButton(this._ibtnSend, bln);
        $R_IBTN.enableButton(this._ibtnSend_Footer, bln);
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EPRNotify_Notify", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
