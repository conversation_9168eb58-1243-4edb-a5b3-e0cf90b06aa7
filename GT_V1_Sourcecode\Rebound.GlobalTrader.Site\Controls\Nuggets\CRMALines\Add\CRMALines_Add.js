Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.initializeBase(this,[n]);this._intCRMAID=0;this._intLineID=0;this._intNewID=0;this._intLineAllocationID=0;this._strPathToData="";this._strDataObject="";this._intQuantityAvailable=0;this._intStockNo=-1};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_ctlSelectInvoiceLine:function(){return this._ctlSelectInvoiceLine},set_ctlSelectInvoiceLine:function(n){this._ctlSelectInvoiceLine!==n&&(this._ctlSelectPOLine=n)},get_lblSelectInvoiceLine:function(){return this._lblSelectInvoiceLine},set_lblSelectInvoiceLine:function(n){this._lblSelectInvoiceLine!==n&&(this._lblSelectPOLine=n)},get_trSelectInvoiceLine:function(){return this._trSelectInvoiceLine},set_trSelectInvoiceLine:function(n){this._trSelectInvoiceLine!==n&&(this._trSelectPOLine=n)},get_pnlLines:function(){return this._pnlLines},set_pnlLines:function(n){this._pnlLines!==n&&(this._pnlLines=n)},get_tblLines:function(){return this._tblLines},set_tblLines:function(n){this._tblLines!==n&&(this._tblLines=n)},get_pnlLinesError:function(){return this._pnlLinesError},set_pnlLinesError:function(n){this._pnlLinesError!==n&&(this._pnlLinesError=n)},get_lblLinesError:function(){return this._lblLinesError},set_lblLinesError:function(n){this._lblLinesError!==n&&(this._lblLinesError=n)},get_pnlLinesLoading:function(){return this._pnlLinesLoading},set_pnlLinesLoading:function(n){this._pnlLinesLoading!==n&&(this._pnlLinesLoading=n)},get_pnlLinesNoneFound:function(){return this._pnlLinesNoneFound},set_pnlLinesNoneFound:function(n){this._pnlLinesNoneFound!==n&&(this._pnlLinesNoneFound=n)},get_pnlLinesNotAvailable:function(){return this._pnlLinesNotAvailable},set_pnlLinesNotAvailable:function(n){this._pnlLinesNotAvailable!==n&&(this._pnlLinesNotAvailable=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._tblLines&&this._tblLines.dispose(),this._ctlSelectInvoiceLine&&this._ctlSelectInvoiceLine.dispose(),this._intCRMAID=null,this._intLineID=null,this._intNewID=null,this._intLineAllocationID=null,this._strPathToData=null,this._strDataObject=null,this._intQuantityAvailable=null,this._ctlSelectInvoiceLine=null,this._lblSelectInvoiceLine=null,this._trSelectInvoiceLine=null,this._pnlLines=null,this._tblLines=null,this._pnlLinesError=null,this._lblLinesError=null,this._pnlLinesLoading=null,this._pnlLinesNoneFound=null,this._pnlLinesNotAvailable=null,this._intStockNo=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),$R_IBTN.enableButton(this._ibtnSave,!1),$R_IBTN.enableButton(this._ibtnSave_Footer,!1),this._tblLines.addSelectedIndexChanged(Function.createDelegate(this,this.selectInvoiceLine)),$R_IBTN.enableButton(this._ibtnSave,!1),$R_IBTN.enableButton(this._ibtnSave_Footer,!1),this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged)),this._strPathToData="controls/Nuggets/CRMALines",this._strDataObject="CRMALines",this._ctlItemsReason1=$find(this.getField("ctlItemsReason1").ID),this._ctlItemsReason1.addItem(),this._ctlItemsReason2=$find(this.getField("ctlItemsReason2").ID),this._ctlItemsReason2.addItem());this._ctlItemsReason2.blankReason();this._ctlItemsReason1.blankReason();this.gotoStep(1);this.setFieldValue("ctlPrintHazWar",!1)},setFieldsFromHeader:function(n,t){this.setFieldValue("ctlCustomerRMA",n);this.setFieldValue("ctlCustomer",t)},stepChanged:function(){var n=this._ctlMultiStep._intCurrentStep;n==1&&this.getInvoiceLines();$R_IBTN.enableButton(this._ibtnSave,n==2);$R_IBTN.enableButton(this._ibtnSave_Footer,n==2)},getInvoiceLines:function(){this._tblLines.clearTable();$R_FN.showElement(this._pnlLines,!1);$R_FN.showElement(this._pnlLinesNotAvailable,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("GetInvoiceLineAllocationCandidates");n.addParameter("ID",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getInvoiceLinesOK));n.addError(Function.createDelegate(this,this.getInvoiceLinesError));n.addTimeout(Function.createDelegate(this,this.getInvoiceLinesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getInvoiceLinesOK:function(n){var u=n._result,i=!1,r,e,t,f;if(u.Lines)for($R_FN.showElement(this._pnlLines,!0),$R_FN.showElement(this._pnlLinesNotAvailable,!1),r=0,e=u.Lines.length;r<e;r++)t=u.Lines[r],f=[$R_FN.writePartNo(t.Part,t.ROHS),t.Qty,t.QtyAllocated,t.QtyRemaining,$R_FN.setCleanTextValue(t.Price),$R_FN.showSerialNumber(t.SO,t.SoLineNo),$R_FN.setCleanTextValue(t.InvoiceDate)],this._tblLines.addRow(f,t.ID,!1),f=null,t=null,i=!0;this._ctlMultiStep.showSteps(i);this._tblLines.resizeColumns();$R_FN.showElement(this._pnlLines,i);$R_FN.showElement(this._pnlLinesNotAvailable,!i)},getInvoiceLinesError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},selectInvoiceLine:function(){this._intLineAllocationID=this._tblLines._varSelectedValue;this.getItemData();this.nextStep()},getItemData:function(){this._intStockNo=-1;this.setFormFieldsToDefaults();this.showLoading(!0);$R_FN.showElement(this._pnlLoadingItemDetail,!0);$R_FN.showElement(this._pnlItemDetailError,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("GetLineAllocationData");n.addParameter("ID",this._intLineAllocationID);n.addParameter("CRMAID",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getItemDataOK));n.addError(Function.createDelegate(this,this.getItemDataError));n.addTimeout(Function.createDelegate(this,this.getItemDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getItemDataError:function(n){$R_FN.showElement(this._pnlLoadingItemDetail,!1);$R_FN.showElement(this._pnlItemDetailError,!0);$R_FN.setInnerHTML(this._pnlItemDetailError,n.get_ErrorMessage())},getItemDataOK:function(n){$R_FN.showElement(this._pnlItemDetailError,!1);var t=n._result;this.setFieldValue("ctlPartNo",t.Part);this.setFieldValue("ctlQuantityShipped",t.Quantity);this.setFieldValue("ctlQuantityAllocated",t.QuantityCRMAAlloc);this._intQuantityAvailable=Math.max(0,Number.parseLocale(t.Quantity.toString())-Number.parseLocale(t.QuantityCRMAAlloc.toString()));this.setFieldValue("ctlQuantity",this._intQuantityAvailable);this.setFieldValue("ctlReturnDate",$R_FN.shortDate());this._intLineID=t.InvoiceLineNo;this._intStockNo=t.StockNo;this.setFieldValue("ctlAS6081",t.AS6081==!0?"Yes":"No");$R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmAdd_ctlDB_ctlAS6081_ctl03_lblAS6081",res.AS6081);$R_FN.showElement(this._pnlItemDetail,!0);$R_FN.showElement(this._pnlLoadingItemDetail,!1);this.showLoading(!1);this.showInnerContent(!0)},saveClicked:function(){this.resetFormFields();this.validateForm()&&this.saveEdit()},validateForm:function(){var n=this.autoValidateFields();return this.checkNumericFieldLessThanOrEqualTo("ctlQuantity",this._intQuantityAvailable)||(n=!1),this.checkNumericFieldGreaterThan("ctlQuantity",0)||(n=!1),n||this.showError(!0),n},validateReason:function(){var n=!0;return this._ctlItemsReason1._txtReason.value==""&&(n=!1,this.showError(!0,$R_RES.ResReason1Value)),n},saveEdit:function(){if(this.validateForm()&&this.validateReason()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("AddNewWithAllocation");n.addParameter("id",this._intCRMAID);n.addParameter("InvoiceLineNo",this._intLineID);n.addParameter("InvoiceLineAllocationNo",this._intLineAllocationID);n.addParameter("ReturnDate",this.getFieldValue("ctlReturnDate"));n.addParameter("Reason","");n.addParameter("Reason1",this._ctlItemsReason1.getSubCategory());n.addParameter("Reason2",this._ctlItemsReason2.getSubCategory());n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("LineNotes",this.getFieldValue("ctlLineNotes"));n.addParameter("RootCause",this.getFieldValue("ctlRootCause"));n.addParameter("StockNo",this._intStockNo);n.addParameter("Avoidable",this.getFieldValue("ctlIsAvoidable"));n.addParameter("PrintHazWar",this.getFieldValue("ctlPrintHazWar"));n.addParameter("AS6081",this.connvertAs6081ToBoolean());n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditOK:function(n){n._result.Result==!0?(this._intNewID=n._result.NewID,this.onSaveComplete()):this.saveEditError(n)},connvertAs6081ToBoolean:function(){return selectedvalue=this.getFieldValue("ctlAS6081"),this._AS6081=selectedvalue=="Yes"?!0:!1,this._AS6081}};Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);