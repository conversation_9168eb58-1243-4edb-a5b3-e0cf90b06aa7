///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date         Remarks
//[001]      Shashi Keshar   24/10/2016   Added New Link print document in Reference of Hub. IsHUB
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.prototype = {

	get_intDebitID: function() { return this._intDebitID; }, 	set_intDebitID: function(v) { if (this._intDebitID !== v)  this._intDebitID = v; }, 
	get_ctlMainInfo: function() { return this._ctlMainInfo; }, 	set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v)  this._ctlMainInfo = v; }, 
	get_ctlLines: function() { return this._ctlLines; }, 	set_ctlLines: function(v) { if (this._ctlLines !== v)  this._ctlLines = v; }, 
	get_btnPrint: function() { return this._btnPrint; }, 	set_btnPrint: function(v) { if (this._btnPrint !== v)  this._btnPrint = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.callBaseMethod(this, "initialize");
	},

	goInit: function() {
		if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printDebitNote));
		if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailDebitNote));
		if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
		if (this._ctlMainInfo) this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_SaveEditComplete));
	    //[001] Start Here
		if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
	    //[001] End Here
		this.setFieldsFromMainInfo();
		Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlMainInfo) this._ctlMainInfo.dispose();
		if (this._ctlLines) this._ctlLines.dispose();
		if (this._btnPrint) this._btnPrint.dispose();
		this._btnPrint = null;
		this._ctlMainInfo = null;
		this._ctlLines = null;
		Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.callBaseMethod(this, "dispose");
	},
	
	printDebitNote: function() {
		$R_FN.openPrintWindow($R_ENUM$PrintObject.DebitNote, this._intDebitID);
	},
	
	emailDebitNote: function() {
		$R_FN.openPrintWindow($R_ENUM$PrintObject.DebitNote, this._intDebitID, true);
	},
	
	ctlMainInfo_GetDataComplete: function() {
		this.setFieldsFromMainInfo();
	},
	
	ctlMainInfo_SaveEditComplete: function () {
	    if (this._ctlLines) this._ctlLines._intGlobalClientNo = this._ctlMainInfo.getFieldValue("hidGlobalClientNo");
	    if (this._ctlLines) this._ctlLines._blnGlobalUser = this._ctlMainInfo._blnGlobalLogin;
		this._ctlLines.getData();
	},
    //[001] Statrt Here
	printOtherDocs: function () {
	   // alert("hi");
	    if (this._btnPrint._strExtraButtonClickCommand == "PrintHUBDebit") $R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBDebit, this._intDebitID);
	    if (this._btnPrint._strExtraButtonClickCommand == "EmailHUBDebit") $R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBDebit, this._intDebitID, true);
	    // Added functionality of Debit Credit (HTML Format) to invoke method CREmail  - Suhail
	    if (this._btnPrint._strExtraButtonClickCommand == "EmailDBHTML") $R_FN.openPrintWindow($R_ENUM$PrintObject.DBEmail, this._intDebitID, true);
	    if (this._btnPrint._strExtraButtonClickCommand == "EmailHUBDebitHtml") $R_FN.openPrintWindow($R_ENUM$PrintObject.EmailHUBDebitHtml, this._intDebitID, true);
	    if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intDebitID, false, "DebitNote");

	},
    //[001] End Here
	setFieldsFromMainInfo: function() {
		if (this._ctlLines._frmEdit) this._ctlLines._frmEdit.setCurrency(this._ctlMainInfo.getFieldValue("hidCurrencyCode"));
		if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromHeader(this._ctlMainInfo.getFieldValue("hidCurrencyNo"), this._ctlMainInfo.getFieldValue("hidCurrencyCode"), this._ctlMainInfo.getFieldValue("hidDebitNumber"), this._ctlMainInfo.getFieldValue("hidSupplierName"), this._ctlMainInfo.getFieldValue("hidPONo"), this._ctlMainInfo.getFieldValue("ctlDebitDate"));
		if (this._ctlLines) this._ctlLines._intGlobalClientNo = this._ctlMainInfo.getFieldValue("hidGlobalClientNo");
		if (this._ctlLines) this._ctlLines._blnGlobalUser = this._ctlMainInfo._blnGlobalLogin;
	}

};

Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.DebitDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
