///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.BOM = function(element) {
Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.initializeBase(this, [element]);
this._intCompanyID = null;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.prototype = {
get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; }, 

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.callBaseMethod(this, "initialize");
        this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
    },

    dispose: function() {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.callBaseMethod(this, "dispose");
    },

    setupDataCall: function() {    
        this._objData.set_PathToData("controls/DropDowns/BOM");
        this._objData.set_DataObject("BOM");
        this._objData.set_DataAction("GetData");
        this._objData.addParameter("id", this._intCompanyID);
    },

    dataCallOK: function() {
        //
        var result = this._objData._result;
    // alert(result);
        if (result.BOM) {
            for (var i = 0; i < result.BOM.length; i++) {
                this.addOption(result.BOM[i].Name, result.BOM[i].ID);
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.DropDowns.BOM.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BOM", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
