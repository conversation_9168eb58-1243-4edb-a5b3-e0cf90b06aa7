Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.prototype={get_blnIncludeSelected:function(){return this._blnIncludeSelected},set_blnIncludeSelected:function(n){this._blnIncludeSelected!==n&&(this._blnIncludeSelected=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._blnIncludeSelected=null,Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData(this._strDataPathModification+"controls/DropDowns/GlobalCurrencyList");this._objData.set_DataObject("GlobalCurrencyList");this._objData.set_DataAction("GetData");this._objData.addParameter("IncludeSelected",this._blnIncludeSelected)},dataCallOK:function(){var t=this._objData._result,n;if(t.Currencies)for(n=0;n<t.Currencies.length;n++)this.addOption(t.Currencies[n].Name,t.Currencies[n].ID,t.Currencies[n].Code)}};Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GlobalCurrencyList",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);