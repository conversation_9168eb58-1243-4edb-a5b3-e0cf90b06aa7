Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base=function(n){Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.prototype={get_lblField:function(){return this._lblField},set_lblField:function(n){this._lblField!==n&&(this._lblField=n)},get_chkOn:function(){return this._chkOn},set_chkOn:function(n){this._chkOn!==n&&(this._chkOn=n)},get_strFilterField:function(){return this._strFilterField},set_strFilterField:function(n){this._strFilterField!==n&&(this._strFilterField=n)},get_enmFieldType:function(){return this._enmFieldType},set_enmFieldType:function(n){this._enmFieldType!==n&&(this._enmFieldType=n)},get_strDefaultValue:function(){return this._strDefaultValue},set_strDefaultValue:function(n){this._strDefaultValue!==n&&(this._strDefaultValue=n)},addFieldEnabledEvent:function(n){this.get_events().addHandler("FieldEnabled",n)},removeFieldEnabledEvent:function(n){this.get_events().removeHandler("FieldEnabled",n)},onFieldEnabled:function(){var n=this.get_events().getHandler("FieldEnabled");n&&n(this,Sys.EventArgs.Empty)},addFieldDisabledEvent:function(n){this.get_events().addHandler("FieldDisabled",n)},removeFieldDisabledEvent:function(n){this.get_events().removeHandler("FieldDisabled",n)},onFieldDisabled:function(){var n=this.get_events().getHandler("FieldDisabled");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){$addHandler(this._chkOn,"click",Function.createDelegate(this,this.toggleEnableField));this._blnOn=this._chkOn.checked;Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._chkOn&&$clearHandlers(this._chkOn),this._chkOn=null,this._lblField=null,Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.callBaseMethod(this,"dispose"),this.isDisposed=!0)},toggleEnableField:function(){this._blnOn=!this._blnOn;this.enableField(this._blnOn)},explicitEnableField:function(){enableField(!0)},enableField:function(n){this._blnOn=Boolean.parse(n.toString());this._blnOn?(this._chkOn.checked||(this._chkOn.checked=!0),Sys.UI.DomElement.removeCssClass(this._lblField,"filterDisabled"),this.onFieldEnabled()):(this._chkOn.checked&&(this._chkOn.checked=!1),Sys.UI.DomElement.addCssClass(this._lblField,"filterDisabled"),this.onFieldDisabled())},show:function(n){$R_FN.showElement(this._element,n)},resetToDefault:function(){this.setValue(this._strDefaultValue)}};Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base",Sys.UI.Control,Sys.IDisposable);