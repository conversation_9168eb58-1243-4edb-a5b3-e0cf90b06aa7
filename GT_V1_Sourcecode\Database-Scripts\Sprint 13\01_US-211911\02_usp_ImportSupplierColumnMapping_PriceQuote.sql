﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-211911]		An.TranTan		16-Oct-2024		CREATE		Add client no, get mapping specific for user
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_ImportSupplierColumnMapping_PriceQuote]                      
	@UserId INT      
AS
BEGIN                
SELECT TOP 1    
	[ManufacturerColumn],                
	[PartColumn],                  
	[QuantityColumn],                 
	[PriceColumn],                 
	[DateCodeColumn],                
	[ProductColumn],                
	[PackageColumn],                
	[FixedCurrencyColumn],                
	[DescriptionColumn],                 
	[AlternatePartColumn],                
	[SupplierPartColumn],                
	[ROHSColumn],         
	[FileType],        
	[RequirementColumn],        
	[SupplierNameColumn],        
	[SPQColumn],        
	[MOQColumn],        
	[QtyInStockColumn],         
	[LeadTimeColumn],        
	[OfferStatusColumn],         
	[FactorySealedColumn],         
	[Region]  AS RegionColumn ,    
	[Last_Time_Buy] AS Last_Time_BuyColumn ,  
	[Buy_Price] AS Buy_PriceColumn ,  
	[Sell_Price] AS Sell_PriceColumn ,  
	[Shipping_Cost] AS Shipping_CostColumn ,  
	[DeliveryDateColumn],
	MSLColumn,
	ClientNoColumn
FROM BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote 
WHERE CreatedBy = @UserId           
ORDER BY importDate DESC    
    
END   
GO


