<%--
Marker     Changed by      Date         Remarks
[001]      Vinay           30/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
--%>
<%@ Control Language="C#" CodeBehind="DebitMainInfo_Edit.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.DebitMainInfo_Edit" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "DebitMainInfo_Edit")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
		
			<ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="lblSupplier" ResourceTitle="Supplier">
				<Field><asp:Label ID="lblSupplier" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="lblContact" ResourceTitle="Contact">
				<Field><asp:Label ID="lblContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlDivision" runat="server" FieldID="ddlDivision" ResourceTitle="Division" IsRequiredField="true">
				<Field><ReboundDropDown:Division ID="ddlDivision" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlBuyer" runat="server" FieldID="ddlBuyer" ResourceTitle="Buyer" IsRequiredField="true">
				<Field><ReboundDropDown:Employee ID="ddlBuyer" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlRaisedBy" runat="server" FieldID="ddlRaisedBy" ResourceTitle="RaisedBy" IsRequiredField="true"> <%--removed this tag due to form validation error--%> <%--IsRequiredField="true"--%>
				<Field><ReboundDropDown:Employee ID="ddlRaisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlRaisedByLbl" runat="server" FieldID="lblRaisedBy" ResourceTitle="RaisedBy" >
				<Field><asp:Label ID="lblRaisedBy" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlDebitDate" runat="server" FieldID="txtDebitDate" ResourceTitle="DebitDate" IsRequiredField="true">
	            <Field>
		            <ReboundUI:ReboundTextBox ID="txtDebitDate" runat="server" Width="150" />
		            <ReboundUI:Calendar ID="calDebitDate" runat="server" RelatedTextBoxID="txtDebitDate" />
	            </Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlReferenceDate" runat="server" FieldID="txtReferenceDate" ResourceTitle="ReferenceDate" IsRequiredField="true">
	            <Field>
		            <ReboundUI:ReboundTextBox ID="txtReferenceDate" runat="server" Width="150" />
		            <ReboundUI:Calendar ID="calReferenceDate" runat="server" RelatedTextBoxID="txtReferenceDate" />
	            </Field>
            </ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlSupplierInvoice" runat="server" FieldID="txtSupplierInvoice" ResourceTitle="SupplierInvoice">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierInvoice" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSupplierReturn" runat="server" FieldID="txtSupplierReturn" ResourceTitle="SupplierReturn">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierReturn" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSupplierCredit" runat="server" FieldID="txtSupplierDebit" ResourceTitle="SupplierCredit">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierDebit" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlPurchaseOrder" runat="server" FieldID="lblPurchaseOrder" ResourceTitle="PurchaseOrderNo">
				<Field><asp:Label ID="lblPurchaseOrder" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlSupplierRMA" runat="server" FieldID="lblSupplierRMA" ResourceTitle="SupplierRMANo">
				<Field><asp:Label ID="lblSupplierRMA" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="ddlTax" ResourceTitle="Tax" IsRequiredField="true">
				<Field><ReboundDropDown:Tax ID="ddlTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency" IsRequiredField="true">
				<Field><ReboundDropDown:BuyCurrency ID="ddlCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlFreight" runat="server" FieldID="txtFreight" ResourceTitle="Freight">
				<Field><ReboundUI:ReboundTextBox ID="txtFreight" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> <asp:Label ID="lblCurrency_Freight" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			  <%--[001] code start--%>
			  <ReboundUI_Form:FormField id="ctlIncoterm" runat="server" FieldID="ddlIncoterm" ResourceTitle="Incoterm" IsRequiredField="true">
			  <Field><ReboundDropDown:Incoterm ID="ddlIncoterm" runat="server" /></Field>
			  </ReboundUI_Form:FormField>
			  <%--[001] code end--%>

			<ReboundUI_Form:FormField id="ctlInstructions" runat="server" FieldID="txtInstructions" ResourceTitle="Instructions">
				<Field><ReboundUI:ReboundTextBox ID="txtInstructions" runat="server" Width="400" Rows="2" TextMode="multiLine" /></Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="SupplierNotes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="400" Rows="2" TextMode="multiLine" /></Field>
			</ReboundUI_Form:FormField>
		
            <ReboundUI_Form:FormField id="ctlLockUpdateClient" runat="server" FieldID="chkLockUpdateClient" ResourceTitle="LockUpdateClient">
				<Field><ReboundUI:ImageCheckBox ID="chkLockUpdateClient" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlURNNumber" runat="server" FieldID="txtURNNumber" ResourceTitle="URNNumber">
				<Field><ReboundUI:ReboundTextBox ID="txtURNNumber" runat="server" Width="200" /></Field>
			</ReboundUI_Form:FormField>

		</ReboundUI_Table:Form>

	</Content>
	
</ReboundUI_Form:DesignBase>
