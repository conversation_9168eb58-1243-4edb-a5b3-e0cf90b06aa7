using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class BackgroundImage : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

		protected override void GetData() {
			JsonObject jsn = new JsonObject();
			JsonObject jsnList = new JsonObject(true);
			jsnList.AddVariable(GetJsonBackgroundItem("brick"));
			jsnList.AddVariable(GetJsonBackgroundItem("fern"));
			jsnList.AddVariable(GetJsonBackgroundItem("flowers"));
            jsnList.AddVariable(GetJsonBackgroundItem("fruit"));
            jsnList.AddVariable(GetJsonBackgroundItem("map"));
			jsnList.AddVariable(GetJsonBackgroundItem("metal"));
			jsnList.AddVariable(GetJsonBackgroundItem("office"));
			jsnList.AddVariable(GetJsonBackgroundItem("palm"));
			jsnList.AddVariable(GetJsonBackgroundItem("plants"));
			jsnList.AddVariable(GetJsonBackgroundItem("silk"));
            jsnList.AddVariable(GetJsonBackgroundItem("sky"));
            jsnList.AddVariable(GetJsonBackgroundItem("snowflake"));
            jsnList.AddVariable(GetJsonBackgroundItem("spark"));
            jsnList.AddVariable(GetJsonBackgroundItem("sun"));
			jsnList.AddVariable(GetJsonBackgroundItem("sunflowers"));
            jsnList.AddVariable(GetJsonBackgroundItem("wood"));
            jsnList.AddVariable(GetJsonBackgroundItem("xmaslights"));
			jsn.AddVariable("Types", jsnList);
			jsnList.Dispose(); jsnList = null;
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}

		private JsonObject GetJsonBackgroundItem(string strValue) {
			JsonObject jsnItem = new JsonObject();
			jsnItem.AddVariable("ID", strValue);
			jsnItem.AddVariable("Name", Functions.GetGlobalResource("BackgroundImages", strValue));
			return jsnItem;
		}
	}
}
