Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.initializeBase(this,[n]);this._aryFilterFieldIDs=[];this._intCheckDropDownTimer=0;this._blnGettingData=!1;this._intPageSize=10;this._strPathToData="";this._strDataObject="";this._aryFilterFieldsToInit=[];this._intCountFilterFieldsToInit=0;this._intCountDropDownsToCheckForData=0;this._objStateData={};this._blnInitialisedControls=!1;this._enmInitialSortDirection=1;this._intSortColumnIndex=0};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.prototype={get_Table:function(){return this._table},set_Table:function(n){this._table!==n&&(this._table=n)},get_ctlPagingButtonsTop:function(){return this._ctlPagingButtonsTop},set_ctlPagingButtonsTop:function(n){this._ctlPagingButtonsTop!==n&&(this._ctlPagingButtonsTop=n)},get_ctlPagingButtonsBottom:function(){return this._ctlPagingButtonsBottom},set_ctlPagingButtonsBottom:function(n){this._ctlPagingButtonsBottom!==n&&(this._ctlPagingButtonsBottom=n)},get_pnlFilters:function(){return this._pnlFilters},set_pnlFilters:function(n){this._pnlFilters!==n&&(this._pnlFilters=n)},get_pnlNoData:function(){return this._pnlNoData},set_pnlNoData:function(n){this._pnlNoData!==n&&(this._pnlNoData=n)},get_aryFilterFieldIDs:function(){return this._aryFilterFieldIDs},set_aryFilterFieldIDs:function(n){this._aryFilterFieldIDs!==n&&(this._aryFilterFieldIDs=n)},get_objFilterFieldIDs:function(){return this._objFilterFieldIDs},set_objFilterFieldIDs:function(n){this._objFilterFieldIDs!==n&&(this._objFilterFieldIDs=n)},get_ibtnReset:function(){return this._ibtnReset},set_ibtnReset:function(n){this._ibtnReset!==n&&(this._ibtnReset=n)},get_ibtnApply:function(){return this._ibtnApply},set_ibtnApply:function(n){this._ibtnApply!==n&&(this._ibtnApply=n)},get_ibtnOff:function(){return this._ibtnOff},set_ibtnOff:function(n){this._ibtnOff!==n&&(this._ibtnOff=n)},get_ibtnCancel:function(){return this._ibtnCancel},set_ibtnCancel:function(n){this._ibtnCancel!==n&&(this._ibtnCancel=n)},get_strFilterExpression:function(){return this._strFilterExpression},set_strFilterExpression:function(n){this._strFilterExpression!==n&&(this._strFilterExpression=n)},get_intCurrentTab:function(){return this._intCurrentTab},set_intCurrentTab:function(n){this._intCurrentTab!==n&&(this._intCurrentTab=n)},get_blnIsFilterInitiallyOpen:function(){return this._blnIsFilterInitiallyOpen},set_blnIsFilterInitiallyOpen:function(n){this._blnIsFilterInitiallyOpen!==n&&(this._blnIsFilterInitiallyOpen=n)},get_strState:function(){return this._strState},set_strState:function(n){this._strState!==n&&(this._strState=n)},get_blnAllowSelection:function(){return this._blnAllowSelection},set_blnAllowSelection:function(n){this._blnAllowSelection!==n&&(this._blnAllowSelection=n)},get_strSelectModeResultsText:function(){return this._strSelectModeResultsText},set_strSelectModeResultsText:function(n){this._strSelectModeResultsText!==n&&(this._strSelectModeResultsText=n)},get_aryButtonIDs:function(){return this._aryButtonIDs},set_aryButtonIDs:function(n){this._aryButtonIDs!==n&&(this._aryButtonIDs=n)},get_txtLimitResults:function(){return this._txtLimitResults},set_txtLimitResults:function(n){this._txtLimitResults!==n&&(this._txtLimitResults=n)},get_intResultsLimit:function(){return this._intResultsLimit},set_intResultsLimit:function(n){this._intResultsLimit!==n&&(this._intResultsLimit=n)},get_ctlMultiSelectionCount:function(){return this._ctlMultiSelectionCount},set_ctlMultiSelectionCount:function(n){this._ctlMultiSelectionCount!==n&&(this._ctlMultiSelectionCount=n)},get_lblSelectModeResults:function(){return this._lblSelectModeResults},set_lblSelectModeResults:function(n){this._lblSelectModeResults!==n&&(this._lblSelectModeResults=n)},get_intDataListNuggetID:function(){return this._intDataListNuggetID},set_intDataListNuggetID:function(n){this._intDataListNuggetID!==n&&(this._intDataListNuggetID=n)},get_intPageSize:function(){return this._intPageSize},set_intPageSize:function(n){this._intPageSize!==n&&(this._intPageSize=n)},get_blnAllowSavingState:function(){return this._blnAllowSavingState},set_blnAllowSavingState:function(n){this._blnAllowSavingState!==n&&(this._blnAllowSavingState=n)},get_strDataListNuggetSubType:function(){return this._strDataListNuggetSubType},set_strDataListNuggetSubType:function(n){this._strDataListNuggetSubType!==n&&(this._strDataListNuggetSubType=n)},get_blnSaveState:function(){return this._blnSaveState},set_blnSaveState:function(n){this._blnSaveState!==n&&(this._blnSaveState=n)},get_enmViewLevel:function(){return this._enmViewLevel},set_enmViewLevel:function(n){this._enmViewLevel!==n&&(this._enmViewLevel=n)},addPageSizeClickEvent:function(n){this.get_events().addHandler("pagesizeclick",n)},removePageSizeClickEvent:function(n){this.get_events().removeHandler("pagesizeclick",n)},onPageSizeClick:function(){var n=this.get_events().getHandler("pagesizeclick");n&&n(this,Sys.EventArgs.Empty)},addSortDataEvent:function(n){this.get_events().addHandler("SortData",n)},removeSortDataEvent:function(n){this.get_events().removeHandler("SortData",n)},onSortData:function(){var n=this.get_events().getHandler("SortData");n&&n(this,Sys.EventArgs.Empty)},addFilterDataEvent:function(n){this.get_events().addHandler("filterdata",n)},removeFilterDataEvent:function(n){this.get_events().removeHandler("filterdata",n)},onFilterData:function(){var n=this.get_events().getHandler("filterdata");n&&n(this,Sys.EventArgs.Empty)},addPageChangedEvent:function(n){this.get_events().addHandler("PageChanged",n)},removePageChangedEvent:function(n){this.get_events().removeHandler("PageChanged",n)},onPageChanged:function(){var n=this.get_events().getHandler("PageChanged");n&&n(this,Sys.EventArgs.Empty)},addPageTabChangedEvent:function(n){this.get_events().addHandler("PageTabChanged",n)},removePageTabChangedEvent:function(n){this.get_events().removeHandler("PageTabChanged",n)},onPageTabChanged:function(){var n=this.get_events().getHandler("PageTabChanged");n&&n(this,Sys.EventArgs.Empty)},addSetupDataCallEvent:function(n){this.get_events().addHandler("SetupDataCall",n)},removeSetupDataCallEvent:function(n){this.get_events().removeHandler("SetupDataCall",n)},onSetupDataCall:function(){var n=this.get_events().getHandler("SetupDataCall");n&&n(this,Sys.EventArgs.Empty)},addGetDataOKEvent:function(n){this.get_events().addHandler("GetDataOK",n)},removeGetDataOKEvent:function(n){this.get_events().removeHandler("GetDataOK",n)},onGetDataOK:function(){var n=this.get_events().getHandler("GetDataOK");n&&n(this,Sys.EventArgs.Empty)},addDataCallErrorEvent:function(n){this.get_events().addHandler("DataCallError",n)},removeDataCallErrorEvent:function(n){this.get_events().removeHandler("DataCallError",n)},onDataCallError:function(){var n=this.get_events().getHandler("DataCallError");n&&n(this,Sys.EventArgs.Empty)},addInitCompleteEvent:function(n){this.get_events().addHandler("InitComplete",n)},removeInitCompleteEvent:function(n){this.get_events().removeHandler("InitComplete",n)},onInitComplete:function(){if(!this._blnInitialisedControls){this._blnInitialisedControls=!0;var n=this.get_events().getHandler("InitComplete");n&&n(this,Sys.EventArgs.Empty)}},addStateRenderedEvent:function(n){this.get_events().addHandler("StateRendered",n)},removeStateRenderedEvent:function(n){this.get_events().removeHandler("StateRendered",n)},onStateRendered:function(){var n=this.get_events().getHandler("StateRendered");n&&n(this,Sys.EventArgs.Empty)},addAskPageToChangeTab:function(n){this.get_events().addHandler("AskPageToChangeTab",n)},removeAskPageToChangeTab:function(n){this.get_events().removeHandler("AskPageToChangeTab",n)},onAskPageToChangeTab:function(){var n=this.get_events().getHandler("AskPageToChangeTab");n&&n(this,Sys.EventArgs.Empty)},addBaseControlsInitialized:function(n){this.get_events().addHandler("BaseControlsInitialized",n)},removeBaseControlsInitialized:function(n){this.get_events().removeHandler("BaseControlsInitialized",n)},onBaseControlsInitialized:function(){var n=this.get_events().getHandler("BaseControlsInitialized");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){var n,i,t;for(Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.callBaseMethod(this,"initialize"),this._blnAllowSelection?(this._ctlMultiSelectionCount.registerTable(this._table),$R_TXTBOX.addEnterPressedEvent(this._txtLimitResults,Function.createDelegate(this,this.resultsLimitPressEnter)),$addHandler(this._txtLimitResults,"blur",Function.createDelegate(this,this.setResultsLimit)),this._table._blnFiltersOn=!0,this._blnIsFilterInitiallyOpen=!0):(this._ctlPagingButtonsTop.pageSizeClick(this._table._intCurrentPageSize),this._ctlPagingButtonsBottom.pageSizeClick(this._table._intCurrentPageSize),this._ctlPagingButtonsTop.addPageSizeClickEvent(Function.createDelegate(this,this.topPageSizeClick)),this._ctlPagingButtonsBottom.addPageSizeClickEvent(Function.createDelegate(this,this.bottomPageSizeClick)),this._ctlPagingButtonsTop.addPageChangedEvent(Function.createDelegate(this,this.topPageNumberChanged)),this._ctlPagingButtonsBottom.addPageChangedEvent(Function.createDelegate(this,this.bottomPageNumberChanged)),this._ctlPagingButtonsTop.addFilterStateChangeEvent(Function.createDelegate(this,this.topFilterStateChanged)),this._ctlPagingButtonsBottom.addFilterStateChangeEvent(Function.createDelegate(this,this.bottomFilterStateChanged)),this._ctlPagingButtonsTop.addStateLockChanged(Function.createDelegate(this,this.updateLockStateFromTopPagingButtons)),this._ctlPagingButtonsBottom.addStateLockChanged(Function.createDelegate(this,this.updateLockStateFromBottomPagingButtons)),this._ctlPagingButtonsTop.addShowLockLoading(Function.createDelegate(this,this.showLockLoadingFromTopPagingButtons)),this._ctlPagingButtonsBottom.addShowLockLoading(Function.createDelegate(this,this.showLockLoadingFromBottomPagingButtons)),this._enmInitialSortDirection=this._table._enmSortDirection,this._intInitialSortColumnIndex=this._table._intSortColumnIndex,this.addPageChangedEvent(Function.createDelegate(this,this.getData)),this.filterStateChanged(this._blnIsFilterInitiallyOpen,!0),this._ctlPagingButtonsBottom.setFilter(this._blnIsFilterInitiallyOpen),this._ctlPagingButtonsTop.setFilter(this._blnIsFilterInitiallyOpen),this._ibtnCancel&&$R_IBTN.addClick(this._ibtnCancel,Function.createDelegate(this,this.cancelClicked)),$R_FN.showElement(this._ctlPagingButtonsTop._element,!1),$R_FN.showElement(this._ctlPagingButtonsBottom._element,!1),this._ibtnCancel&&$R_IBTN.showButton(this._ibtnCancel,!1)),this._table.addSortDataEvent(Function.createDelegate(this,this.onSortData)),this._table.addFilterDataEvent(Function.createDelegate(this,this.onFilterData)),this.addSortDataEvent(Function.createDelegate(this,this.getData)),this.addFilterDataEvent(Function.createDelegate(this,this.getData)),this.addPageSizeClickEvent(Function.createDelegate(this,this.getData)),this._ibtnReset&&$R_IBTN.addClick(this._ibtnReset,Function.createDelegate(this,this.resetFilter)),this._ibtnApply&&$R_IBTN.addClick(this._ibtnApply,Function.createDelegate(this,this.applyFilter)),this._ibtnOff&&$R_IBTN.addClick(this._ibtnOff,Function.createDelegate(this,this.turnOffFilter)),this.addRefreshEvent(Function.createDelegate(this,this.applyFilter)),this.showLoading(!this._blnIsFilterInitiallyOpen),this.setFilterFieldEnterPressedEvents(),this._intCurrentTab||(this._intCurrentTab=0),this.showContent(!1),this._aryFilterFieldsToInit=[],n=0,i=this._aryFilterFieldIDs.length;n<i;n++)t=$find(this._aryFilterFieldIDs[n]),(Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown"||Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating")&&Array.add(this._aryFilterFieldsToInit,t),t=null;if(this._intCountFilterFieldsToInit=this._aryFilterFieldsToInit.length,this._intCountFilterFieldsToInit==0)this.controlsInitialized();else for(n=0;n<this._intCountFilterFieldsToInit;n++)this.ensureFilterFieldControlInitialized(this._aryFilterFieldsToInit[n])},ensureFilterFieldControlInitialized:function(n){if(n.get_isInitialized())this._intCountFilterFieldsToInit-=1,this._intCountFilterFieldsToInit==0&&(this._aryFilterFieldsToInit=null,this.controlsInitialized());else{var t=this._element.id,i=function(){$find(t).ensureFilterFieldControlInitialized(n)};setTimeout(i,5)}},controlsInitialized:function(){this._blnIsFilterInitiallyOpen?(this.onBaseControlsInitialized(),this.getDropDownsData(!0)):(this.onBaseControlsInitialized(),this.completionOfInit())},dispose:function(){this.isDisposed||this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._ibtnReset&&$R_IBTN.clearHandlers(this._ibtnReset),this._ibtnApply&&$R_IBTN.clearHandlers(this._ibtnApply),this._ibtnOff&&$R_IBTN.clearHandlers(this._ibtnOff),this._table&&this._table.dispose(),this._ctlPagingButtonsTop&&this._ctlPagingButtonsTop.dispose(),this._ctlPagingButtonsBottom&&this._ctlPagingButtonsBottom.dispose(),this._objData&&this._objData.dispose(),this._ctlMultiSelectionCount&&this._ctlMultiSelectionCount.dispose(),this._table=null,this._ctlPagingButtonsTop=null,this._ctlPagingButtonsBottom=null,this._pnlFilters=null,this._pnlNoData=null,this._aryFilterFieldIDs=null,this._objFilterFieldIDs=null,this._ibtnReset=null,this._ibtnApply=null,this._ibtnOff=null,this._txtLimitResults=null,this._ctlMultiSelectionCount=null,this._lblSelectModeResults=null,this._aryButtonIDs=null,this._objData=null,this._aryFilterFieldsToInit=null,this._enmViewLevel=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.callBaseMethod(this,"dispose"))},clearState:function(){Rebound.GlobalTrader.Site.WebServices.ClearDataListNuggetState(this._intDataListNuggetID,this._strDataListNuggetSubType);Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.callBaseMethod(this,"clearState")},setTabFromViewLevelStateVariable:function(){var n=this._enmViewLevel;this._intCurrentTab=this.getStateVariableByName("ViewLevel").Value;typeof this._intCurrentTab=="undefined"&&(this._intCurrentTab=n);this._intCurrentTab==null&&(this._intCurrentTab=n);this._intCurrentTab=Number.parseInvariant(this._intCurrentTab.toString());this._enmViewLevel=this._intCurrentTab;n=null},getDropDownsData:function(n){var t,i,r;if(n)for(i=0,r=this._aryFilterFieldIDs.length;i<r;i++)t=$find(this._aryFilterFieldIDs[i]),Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown"&&(this._intCountDropDownsToCheckForData+=1),t=null;for(i=0,r=this._aryFilterFieldIDs.length;i<r;i++)t=$find(this._aryFilterFieldIDs[i]),Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown"&&(n&&t._ddl.addGotDataComplete(Function.createDelegate(this,this.gotDropDownDataComplete)),t.getDropDownData()),t=null;n&&this._intCountDropDownsToCheckForData==0&&this.completionOfInit()},gotDropDownDataComplete:function(){this._intCountDropDownsToCheckForData-=1;this._intCountDropDownsToCheckForData==0&&this.completionOfInit()},completionOfInit:function(){this.displayLockState();this.onInitComplete()},getData:function(){this._blnGettingData||(this.showError(!1),this.clearMessages(),$R_FN.showElement(this._pnlLinks,!0),this._ibtnCancel&&$R_IBTN.showButton(this._ibtnCancel,!0),this.showResultsPanels(!1),this.enableButtons(!1),this._ctlPagingButtonsTop&&$R_FN.showElement(this._ctlPagingButtonsTop.get_element(),!1),this._ctlPagingButtonsBottom&&$R_FN.showElement(this._ctlPagingButtonsBottom.get_element(),!1),this.cancelDataCall(),this._objData=new Rebound.GlobalTrader.Site.Data,this._objData._intTimeoutMilliseconds=6e5,this._objData.set_DataAction("GetData"),this._objData.set_PathToData(this._strPathToData),this._objData.set_DataObject(this._strDataObject),this.addFilterParameters(this._objData),this.onSetupDataCall(),this._objData.addParameter("DLNID",this._intDataListNuggetID),this._objData.addParameter("SortIndex",this._table._intSortColumnIndex+1),this._objData.addParameter("SortDir",this._table._enmSortDirection),this._objData.addParameter("PageIndex",this._table._intCurrentPage-1),this._objData.addParameter("PageSize",this._table._intCurrentPageSize),this._objData.addParameter("SaveState",this._blnSaveState),this._objData.addParameter("DLNSubType",this._strDataListNuggetSubType),this._blnAllowSelection&&this._objData.addParameter("ResultsLimit",this._intResultsLimit),this._objData.addDataOK(Function.createDelegate(this,this.dataListNugget_getDataOK)),this._objData.addError(Function.createDelegate(this,this.dataListNugget_getDataError)),this._objData.addTimeout(Function.createDelegate(this,this.dataListNugget_getDataError)),this._blnGettingData=!0,$R_DQ.addToQueue(this._objData),$R_DQ.processQueue())},dataListNugget_getDataOK:function(n){this._blnGettingData=!1;this._objResult=n._result;this._table._intTotalRecords=this._objResult.TotalRecords;this._blnAllowSelection||(this._ctlPagingButtonsTop._intTotalResults=this._objResult.TotalRecords,this._ctlPagingButtonsBottom._intTotalResults=this._objResult.TotalRecords);this._table.clearTable();this.showContentLoading(!1);this.onGetDataOK();this.showError(!1);this.showResultsPanels(!0);this._ibtnCancel&&$R_IBTN.showButton(this._ibtnCancel,!1);this._table._intTotalRecords>0?this.showResultsTable():this.showNoData();this._blnAllowSelection?(this._txtLimitResults.value=this._intResultsLimit,$R_FN.setInnerHTML(this._lblSelectModeResults,String.format(this._strSelectModeResultsText,Math.min(this._intResultsLimit,this._table._intTotalRecords),this._table._intTotalRecords))):(this._table.calculatePages(),this.updatePaging());this._table.resizeColumns();this.enableButtons(!0)},dataListNugget_getDataError:function(n){this._blnGettingData=!1;this.showError(!0,n.get_ErrorMessage());this._ibtnCancel&&$R_IBTN.showButton(this._ibtnCancel,!1);this.onDataCallError();this.enableButtons(!0)},topPageSizeClick:function(){this._blnGettingData||(this._ctlPagingButtonsBottom.pageSizeClick(this._ctlPagingButtonsTop._intCurrentPageSize),this.pageSizeClick(this._ctlPagingButtonsTop._intCurrentPageSize))},bottomPageSizeClick:function(){this._blnGettingData||(this._ctlPagingButtonsTop.pageSizeClick(this._ctlPagingButtonsBottom._intCurrentPageSize),this.pageSizeClick(this._ctlPagingButtonsBottom._intCurrentPageSize))},pageSizeClick:function(n){this._blnGettingData||n!=this._table._intCurrentPageSize&&(this._table._intCurrentPageSize=n,this._table.calculatePages(),this._table.calculateStartAndEndRow(),this.updatePaging(),this.onPageChanged())},topPageNumberChanged:function(){this._ctlPagingButtonsBottom._intCurrentPage=this._ctlPagingButtonsTop._intCurrentPage;this.pageNumberChanged(this._ctlPagingButtonsTop._intCurrentPage)},bottomPageNumberChanged:function(){this._ctlPagingButtonsTop._intCurrentPage=this._ctlPagingButtonsBottom._intCurrentPage;this.pageNumberChanged(this._ctlPagingButtonsBottom._intCurrentPage)},pageNumberChanged:function(n){n!=this._table._intCurrentPage&&(this._table.changePage(n),this.updatePaging(),this.onPageChanged())},setPage:function(n){this._blnGettingData||(this._table._intCurrentPage=n,this.getData())},updatePaging:function(){this._blnAllowSelection||(this._ctlPagingButtonsTop._intTotalResults=this._table._intTotalRecords,this._ctlPagingButtonsTop._intTotalPages=this._table._intTotalPages,this._ctlPagingButtonsTop._intCurrentPage=this._table._intCurrentPage,this._ctlPagingButtonsTop.updatePageDisplay(),this._ctlPagingButtonsBottom._intTotalResults=this._table._intTotalRecords,this._ctlPagingButtonsBottom._intTotalPages=this._table._intTotalPages,this._ctlPagingButtonsBottom._intCurrentPage=this._table._intCurrentPage,this._ctlPagingButtonsBottom.updatePageDisplay())},topFilterStateChanged:function(){this._blnGettingData||(this._ctlPagingButtonsBottom.setFilter(this._ctlPagingButtonsTop._blnFiltersOn),this.filterStateChanged(this._ctlPagingButtonsTop._blnFiltersOn))},bottomFilterStateChanged:function(){this._blnGettingData||(this._ctlPagingButtonsTop.setFilter(this._ctlPagingButtonsBottom._blnFiltersOn),this.filterStateChanged(this._ctlPagingButtonsTop._blnFiltersOn))},turnOffFilter:function(){this._blnGettingData||(this._ctlPagingButtonsBottom.setFilter(!1),this._ctlPagingButtonsTop.setFilter(!1),this.filterStateChanged(!1))},filterStateChanged:function(n,t){this._table._blnFiltersOn=n;this.showFilters(n);n?this._strFilterExpression!=""&&(this._table._strFilterExpression=this._strFilterExpression,t||this.onFilterData()):this._strFilterExpression!=""&&(this._table._strFilterExpression="",t||this.onFilterData())},showFilters:function(n){$R_FN.showElement(this._pnlFilters,n);n&&this.getDropDownsData()},showNoData:function(){this._table.show(!1);$R_FN.showElement(this._pnlNoData,!0)},showResultsTable:function(){this._table.show(!0);$R_FN.showElement(this._pnlNoData,!1)},showResultsPanels:function(n){n&&(this.showContent(!0),this.showContentLoading(!1),$R_FN.showElement(this._pnlNoData,!1),this._blnAllowSelection||($R_FN.showElement(this._ctlPagingButtonsTop._element,!0),$R_FN.showElement(this._ctlPagingButtonsBottom._element,!0)));this.showLoading(!n);n?Sys.UI.DomElement.removeCssClass(this._table.get_element(),"dataListNuggetLoading"):Sys.UI.DomElement.addCssClass(this._table.get_element(),"dataListNuggetLoading");this.showRefresh(n)},resetFilter:function(){for(var t,n=0,i=this._aryFilterFieldIDs.length;n<i;n++)t=$find(this._aryFilterFieldIDs[n]),t.reset(),t.resetToDefault(),t=null;this._blnAllowSelection&&(this._txtLimitResults.value=50,this._intResultsLimit=50)},applyFilter:function(){this._blnGettingData||this.onFilterData()},getFilterField:function(n){var t=eval("this._objFilterFieldIDs."+n);return t||eval(String.format("FilterFieldNotFound_{0}()",n)),$find(t)},getFilterFieldByFilterName:function(n){for(var i=null,t=0,r=this._aryFilterFieldIDs.length;t<r;t++)if($find(this._aryFilterFieldIDs[t])._strFilterField==n){i=$find(this._aryFilterFieldIDs[t]);break}return i},showFilterField:function(n,t){var i=this.getFilterField(n);i&&i.show(t)},enableFilterField:function(n,t){var i=this.getFilterField(n);i&&i.enableField(t)},getFilterFieldValue:function(n){var t=this.getFilterField(n),i;if(t)return t._blnOn?(i=t.getValue(),t=null,i):void 0},setFilterFieldValue:function(n,t){var i=this.getFilterField(n);i&&i.setValue(t)},getFilterFieldDropDownText:function(n){var t=this.getFilterField(n),i;if(t)return t._blnOn?(i=t.getText(),t=null,i):void 0},getFilterFieldDropDownExtraText:function(n){var t=this.getFilterField(n),i;if(t)return t._blnOn?(i=t.getExtraText(),t=null,i):void 0},getFilterFieldValue_Min:function(n){var t=this.getFilterField(n),i;if(t)return t._blnOn?(i=t.getMinValue(),t=null,i):void 0},getFilterFieldValue_Max:function(n){var t=this.getFilterField(n),i;if(t)return t._blnOn?(i=t.getMaxValue(),t=null,i):void 0},enableButtons:function(n){this._ibtnReset&&$R_IBTN.enableButton(this._ibtnReset,n);this._ibtnApply&&$R_IBTN.enableButton(this._ibtnApply,n);this._ibtnOff&&$R_IBTN.enableButton(this._ibtnOff,n)},setFilterFieldEnterPressedEvents:function(){for(var t,n=0,i=this._aryFilterFieldIDs.length;n<i;n++)t=$find(this._aryFilterFieldIDs[n]),(Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox"||Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical")&&t.addEnterPressed(Function.createDelegate(this,this.applyFilterAfterEnter))},applyFilterAfterEnter:function(){for(var n,i,t=0,r=this._aryFilterFieldIDs.length;t<r;t++)n=$find(this._aryFilterFieldIDs[t]),i=Object.getTypeName(n),i=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox"&&n.textBoxBlur(),i=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical"&&n.onBlur(),i=null,n=null;this.applyFilter()},addFilterParameters:function(n){var i,r,t;if(this._table._blnFiltersOn)for(i=0,r=this._aryFilterFieldIDs.length;i<r;i++)this._aryFilterFieldIDs[i]&&(t=$find(this._aryFilterFieldIDs[i]),t&&(t._blnOn&&(Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical"||Object.getTypeName(t)=="Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating"?(n.addParameter(String.format("{0}Lo",t._strFilterField),t.getMinValue()),n.addParameter(String.format("{0}Hi",t._strFilterField),t.getMaxValue()),n.addParameter(String.format("{0}_Comparison",t._strFilterField),t._ddl.value)):n.addParameter(t._strFilterField,t.getValue())),n.addParameter(String.format("{0}_IsShown",t._strFilterField),t._blnOn),n.addParameter(String.format("{0}_IsOn",t._strFilterField),t._blnOn),n.addParameter(String.format("{0}_Type",t._strFilterField),t._enmFieldType)),t=null)},resultsLimitPressEnter:function(){this.setResultsLimit()&&this.getData()},setResultsLimit:function(){return this._txtLimitResults.value.length==0?(this._txtLimitResults.value=this._intResultsLimit,!1):this._intResultsLimit==this._txtLimitResults.value?!1:(this._intResultsLimit=Number.parseInvariant(this._txtLimitResults.value),this._intResultsLimit<1&&(this._intResultsLimit=1),!0)},cancelDataCall:function(){this._blnGettingData=!1;this._objData&&this._objData.cancel()},cancelClicked:function(){this.cancelDataCall();this._ibtnCancel&&$R_IBTN.showButton(this._ibtnCancel,!1);this.showResultsPanels(!0);this.enableButtons(!0);this.clearMessages();this.addMessage($R_RES.SearchCancelled,$R_ENUM$MessageTypeList.Warning)},updateLockState:function(n){var t=this._blnSaveState!=n;this._blnSaveState=n;t&&(this._blnSaveState?this.saveState():this.clearState());this._blnAllowSelection||this.displayLockState()},displayLockState:function(){this._ctlPagingButtonsTop&&this._ctlPagingButtonsTop.setLockState(this._blnSaveState);this._ctlPagingButtonsBottom&&this._ctlPagingButtonsBottom.setLockState(this._blnSaveState)},updateLockStateFromTopPagingButtons:function(){this._ctlPagingButtonsBottom&&(this._ctlPagingButtonsBottom.setLockState(this._ctlPagingButtonsTop._blnStateLocked,!0),this.updateLockState(this._ctlPagingButtonsTop._blnStateLocked))},updateLockStateFromBottomPagingButtons:function(){this._ctlPagingButtonsTop&&(this._ctlPagingButtonsTop.setLockState(this._ctlPagingButtonsBottom._blnStateLocked,!0),this.updateLockState(this._ctlPagingButtonsBottom._blnStateLocked))},showLockLoadingFromTopPagingButtons:function(){this._ctlPagingButtonsBottom.showLockLoading(!0)},showLockLoadingFromBottomPagingButtons:function(){this._ctlPagingButtonsTop.showLockLoading(!0)},clearState:function(){},saveState:function(){this._blnSaveState&&(this.enableButtons(!1),this._objData=new Rebound.GlobalTrader.Site.Data,this.addFilterParameters(this._objData),this.onSetupDataCall(),this._objData.set_PathToData(this._strPathToData),this._objData.set_DataObject(this._strDataObject),this._objData.set_DataAction("SaveState"),this._objData.addParameter("DLNID",this._intDataListNuggetID),this._objData.addParameter("DLNSubType",this._strDataListNuggetSubType),this._objData.addParameter("SortIndex",this._table._intSortColumnIndex+1),this._objData.addParameter("SortDir",this._table._enmSortDirection),this._objData.addParameter("PageIndex",this._table._intCurrentPage-1),this._objData.addParameter("PageSize",this._table._intCurrentPageSize),this._objData.addParameter("SaveState",!0),this._objData.addDataOK(Function.createDelegate(this,this.saveStateOK)),this._objData.addError(Function.createDelegate(this,this.saveStateOK)),this._objData.addTimeout(Function.createDelegate(this,this.saveStateOK)),$R_DQ.addToQueue(this._objData),$R_DQ.processQueue())},saveStateOK:function(){this.enableButtons(!0)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base",Rebound.GlobalTrader.Site.Controls.Nuggets.Base,Sys.IDisposable);