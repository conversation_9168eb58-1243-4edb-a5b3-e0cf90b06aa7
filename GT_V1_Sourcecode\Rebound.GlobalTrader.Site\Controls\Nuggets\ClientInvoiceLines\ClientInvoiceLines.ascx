<%--
Marker     Changed by      Date               Remarks
[005]      Prakash           11/04/2014         Add Client Invoice
--%>
<%@ Control Language="C#" CodeBehind="ClientInvoiceLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Lines" BoxType="Standard" >

	<Links>
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" IsInitiallyEnabled="false"  />
		<ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Delete" IconCSSType="Delete" IsInitiallyEnabled="false"   />
	</Links>
	
	<Content>
		<ReboundUI:FlexiDataTable ID="tblAll" runat="server" AllowSelection="true" PanelHeight="150" />
		
		<asp:Panel ID="pnlSummary" runat="server" CssClass="dataTableSummary">
			<div class="dataTableSummaryField">
				<div class="title"><%=Functions.GetGlobalResource("FormFields", "Total")%></div>
				<div class="item"><asp:Label ID="lblTotal" runat="server" /></div>
			</div>
		</asp:Panel>
		
	
			
	</Content>
	
	<Forms>
		<ReboundForm:ClientInvoiceLines_Add id="frmAdd" runat="server" />
		<ReboundForm:ClientInvoiceLines_Delete id="frmDelete" runat="server" />
	</Forms>
	
</ReboundUI_Nugget:DesignBase>
