///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.CCUsersTo = function(element){
    Rebound.GlobalTrader.Site.Controls.AutoSearch.CCUsersTo.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.CCUsersTo.prototype = {

	initialize: function () {
        Rebound.GlobalTrader.Site.Controls.AutoSearch.CCUsersTo.callBaseMethod(this, "initialize");
		this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
		this.setupDataObject("CCUsersTo");
		var kili=$('#ctl1_cphMain_ctlBOMItems_ctlDB_ctl14_ctlExpedite_ctlDB_ctlSendToGroup_ctl04_ddlSendToGroup_ddl').val();
		/*obj.addParameter("SendToGroup", kili);*/
	},
	
	dispose: function(){
		if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.AutoSearch.CCUsersTo.callBaseMethod(this, "dispose");
	},
	
	dataReturned: function(){
		if (!this._result) return;
		if (this._result.TotalRecords > 0) {
			for (var i = 0, l = this._result.Results.length; i < l; i++) {
				var res = this._result.Results[i];
				strHTML = "";
				if (res.Type.toUpperCase() == "GROUP") strHTML += '<div class="mailGroup">';
				strHTML += res.Name;
				if (res.Type.toUpperCase() == "GROUP") strHTML += "</div>";
				this.addResultItem(strHTML, res.Name, res.ID, res.Type);
				strHTML = null;
				res = null;
			}
		}
	}
};
Rebound.GlobalTrader.Site.Controls.AutoSearch.CCUsersTo.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.CCUsersTo", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
