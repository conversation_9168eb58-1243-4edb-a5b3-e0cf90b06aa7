<%@ Control Language="C#" CodeBehind="GILines_ImageUpload.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILines_ImageUpload" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<link href="css/uploadfile.css" rel="stylesheet">
<script src="js/jquery.min.js"></script>
<script src="js/jquery.uploadfile.js"></script>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false"
    AllowQuickHelp="false">

    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>

    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "GILines_ImageUpload")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server" class="noClass">
            <ReboundUI_Form:FormField ID="ctlCaption" runat="server" FieldID="txtCaption" ResourceTitle="Caption" IsRequiredField="true">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtCaption" runat="server" Width="255" Rows="2" MaxLength="50" />
                </Field>
            </ReboundUI_Form:FormField>
            <asp:TableRow>
                <asp:TableCell>
            &nbsp;
                </asp:TableCell>
                <asp:TableCell>
                        <br />
                        <div id="Imagesingleupload5" >Upload</div>
                	    <script type="text/javascript">
                	    $(document).ready(function () {
                	        var dragdropObj = $("#Imagesingleupload5").uploadFile({
                	            url: "DocImage.ashx?mxs=1&type=IMAGEUPLOAD&IsDragDrop=true",
                	            allowedTypes: "jpg,jpeg,bmp",
                	            fileName: "myfile",
                	            autoSubmit: false,
                	            multiple: true,
                	            maxFileSize: 7900000,
                	            showStatusAfterSuccess: false,
                	            showCancel: true,
                	            showDone: true,
                	            uploadDiv: "excelipload",
                	            dynamicFormData: function () {
                	                var data = { DocId: $find("<%=this.ClientID%>")._intLineID }
                	                return data;
                	            },
                	            //maxFileCount: 1,

                                onSuccess: function (files, data, xhr) {
                	                var originalFilename = '';
                	                var generatedFilename = '';
                	                originalFilename = files[0];
                	                var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
                	                generatedFilename = json.FileName;
                	                $find("<%=this.ClientID%>").saveGILineImage(originalFilename, generatedFilename);
                	            },
                                onSelect: function (fup) {
                	                var result = true;
                	                $find("<%=this.ClientID%>")._dragobj = dragdropObj;

                	                return result;
                	            }
                	        });
                	        
                	    });


		</script>
        <div class="clearing"></div>
                </asp:TableCell>
                <asp:TableCell>
                  
                </asp:TableCell>
            </asp:TableRow>
        </ReboundUI_Table:Form>

    </Content>



</ReboundUI_Form:DesignBase>

