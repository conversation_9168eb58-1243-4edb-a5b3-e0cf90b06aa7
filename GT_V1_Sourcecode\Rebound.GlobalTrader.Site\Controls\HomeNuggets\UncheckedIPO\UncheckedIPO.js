Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.prototype={get_pnlPartsOrdered:function(){return this._pnlPartsOrdered},set_pnlPartsOrdered:function(n){this._pnlPartsOrdered!==n&&(this._pnlPartsOrdered=n)},get_tblPartsOrdered:function(){return this._tblPartsOrdered},set_tblPartsOrdered:function(n){this._tblPartsOrdered!==n&&(this._tblPartsOrdered=n)},get_pnlMore:function(){return this._pnlMore},set_pnlMore:function(n){this._pnlMore!==n&&(this._pnlMore=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData))},dispose:function(){this.isDisposed||(this._tblPartsOrdered&&this._tblPartsOrdered.dispose(),this._pnlPartsOrdered=null,this._tblPartsOrdered=null,this._pnlMore=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlMore,!1);$R_FN.showElement(this._pnlPartsOrdered,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/UncheckedIPO");n.set_DataObject("UncheckedIPO");n.set_DataAction("GetData");n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,u,t,r;for($R_FN.showElement(this._pnlMore,!0),this._tblPartsOrdered.clearTable(),r=0;r<i.UnapprovedIPOToday.length;r++)t=i.UnapprovedIPOToday[r],u=[$RGT_nubButton_InternalPurchaseOrder(t.ID,t.No),t.IsPOHub==!0?t.CompanyName:$RGT_nubButton_Company(t.CompanyId,t.CompanyName),t.EmployeeName,t.Date],this._tblPartsOrdered.addRow(u,null);$R_FN.showElement(this._pnlPartsOrdered,i.UnapprovedIPOToday.length>0);this.hideLoading();this.showNoneFoundOrContent(i.UnapprovedIPOToday.length)}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);