﻿
GO


CREATE OR ALTER PROCEDURE [dbo].[usp_insert_SourcingResult_From_TrustedPH]          
--***************************************************************************************************          
--* RP 16.02.2010:          
--* - add @UpdatedBy          
--***************************************************************************************************          
    @CustomerRequirementNo int          
  , @ExcessNo int          
  , @UpdatedBy int      
  , @IsClone bit = 0      
  , @SourcingResultId int OUTPUT    
  , @LinkCurrencyMsg varchar(150)='' OUTPUT         
AS           
    BEGIN   
	 IF (@CustomerRequirementNo <= 0)     
       BEGIN 
	   return
	   end       
               SET @LinkCurrencyMsg = ''     
     --check if we have a row already          
        IF (SELECT  count(*)          
            FROM    tbSourcingResult          
            WHERE   CustomerRequirementNo = @CustomerRequirementNo          
                    AND SourcingTable = 'EXPH'          
                    AND SourcingTableItemNo = @ExcessNo          
           ) = 0           
            BEGIN          
          
		DECLARE @ClientCompanyNo INT              
		DECLARE @CompanyNo INT              
		DECLARE @ClientNo INT              
		-- DECLARE @UPLiftPrice FLOAT            
		-- DECLARE @Price FLOAT            
		DECLARE @ClientCurrencyNo INT          
		declare @ManufacturerNo int           
		declare @ProductNo int            
		declare @PackageNo int      
		DECLARE @HubCurrencyNo INT     
		DECLARE @ClientLinkCurrencyNo INT     
		declare @LinkMultiCurrencyNo int    
		DECLARE @OfferCurrencyNo INT   
		DECLARE @BuyExchangeRate float        
		DECLARE @HubExchangeRate float    
		DECLARE @DefaultPOCompanyNo INT      
		DECLARE @GlobalProductNo int  
		DECLARE @ClientProductNo int    
		DECLARE @HubCurrencyName varchar(20)
		DECLARE @ClientCode varchar(20)  
                          
                          
   SELECT @ClientNo = ClientNo, @ManufacturerNo=IsNULL(ManufacturerNo,0),@ProductNo=IsNULL(ProductNo,0),@PackageNo=IsNULL(PackageNo,0)  FROM tbCustomerRequirement where CustomerRequirementId = @CustomerRequirementNo               
   SELECT TOP 1 @ClientCompanyNo = CompanyId FROM tbCompany WHERE ClientNo = @ClientNo AND IsPOHub =1              
   
   
  
 IF EXISTS(SELECT 1 FROM [BorisGlobalTraderImports].dbo.tbExcess WHERE ExcessId =  @ExcessNo)
 BEGIN
          
		SELECT @OfferCurrencyNo = CurrencyNo, @DefaultPOCompanyNo = c.POCurrencyNo , @GlobalProductNo = isnull(p.GlobalProductNo,0) 
		FROM [BorisGlobalTraderImports].dbo.tbExcess e    
		LEFT JOIN dbo.tbCompany c on e.CompanyNo = c.CompanyId   
		LEFT JOIN tbProduct p on e.ProductNo = p.ProductId  
		WHERE e.ExcessId = @ExcessNo  
  
		SELECT @LinkMultiCurrencyNo = l.LinkMultiCurrencyId ,@ClientCurrencyNo = l.SupplierCurrencyNo  
		from tbCurrency c left join tbLinkMultiCurrency l   on l.GlobalCurrencyNo = c.GlobalCurrencyNo   
		WHERE  l.ClientNo = @ClientNo and c.ClientNo = 114 AND c.CurrencyId = @OfferCurrencyNo  
  
		SELECT  @HubCurrencyNo = dbo.ufn_get_HUB_DefaultCurrencyNo(ISNULL(@OfferCurrencyNo,@DefaultPOCompanyNo),@ClientNo,114)  
		--Get the buy exchange rate .66  
		SELECT @BuyExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@OfferCurrencyNo, isnull(@DefaultPOCompanyNo,0)), GETDATE())    
		--Get Hub Exchange rate             
		SELECT @HubExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@HubCurrencyNo, 0), GETDATE())   
  
		select top 1 @ClientProductNo = ProductId from tbProduct where GlobalProductNo = @GlobalProductNo and ClientNo = @ClientNo and isnull(Inactive,0) = 0  

	      IF @LinkMultiCurrencyNo IS NULL
		   BEGIN
		     SELECT @ClientCode = ClientCode  FROM tbclient where clientid=@ClientNo
			 select @HubCurrencyName = CurrencyCode   from tbCurrency where CurrencyId = @OfferCurrencyNo
		     SET @LinkCurrencyMsg = 'Cannot use '+@HubCurrencyName+' currency for the '+@ClientCode+' client. Kindly contact administrator.';
			 SET @SourcingResultId = 0
			 RETURN
		   END

                INSERT  INTO [dbo].[tbSourcingResult] (          
     [CustomerRequirementNo]          
     , [SourcingTable]          
     , [SourcingTableItemNo]          
     , [FullPart]          
     , [Part]          
     , [ManufacturerNo]          
     , [DateCode]          
     , [ProductNo]          
     , [PackageNo]          
     , [Quantity]          
     , [Price]          
     , [CurrencyNo]          
     , [OriginalEntryDate]          
     , [Salesman]          
     , [OfferStatusNo]      
     , [OfferStatusChangeDate]          
     , [OfferStatusChangeLoginNo]          
     , [SupplierNo]          
     , [UpdatedBy]          
     , [DLUP]          
     , [TypeName]          
     , [Notes]          
     , [ROHS]          
     , POHubCompanyNo          
     , SupplierPrice                
     , ClientCompanyNo          
     , ClientCurrencyNo          
     , EstimatedShippingCost        
     , SupplierManufacturerName              
     , SupplierDateCode              
     , SupplierPackageType              
     , SupplierProductType              
     , SupplierMOQ              
     , SupplierTotalQSA              
     , SupplierLTB              
     , SupplierNotes               
     , SPQ        
     , LeadTime      
     , ROHSStatus        
     , FactorySealed        
     , MSL   
     , Buyer  
  , ActualPrice  
     , ActualCurrencyNo  
  , ExchangeRate       
  , LinkMultiCurrencyNo  
  , MSLLevelNo  
  ,	SupplierWarranty
         )
               
                        
      SELECT @CustomerRequirementNo      
      , 'EXPH'          
      , @ExcessNo          
      , eph.[FullPart]          
      , eph.[Part]          
      --, isnull(@ManufacturerNo, 0)     
	 , isnull(mfr.ManufacturerId ,0)         
      , eph.[DateCode]          
      , isnull(@ClientProductNo ,@ProductNo)     
      , isnull(@PackageNo, 0)    
   , case when @IsClone = 1 then isnull(eph.[Quantity], 0) else  0  end  
      --, isnull(eph.[Quantity], 0)          
   , (  
     (isnull(eph.Price,0) / @BuyExchangeRate)   
         +  ((isnull(eph.Price,0) / @BuyExchangeRate) * ISNULL(company.UPLiftPrice,0))/100   
    )   
    * @HubExchangeRate  
      --, isnull(eph.[CurrencyNo], 0)    
   , @HubCurrencyNo        
      , eph.[OriginalEntryDate]          
      , isnull(eph.[Salesman], 0)          
      , eph.[OfferStatusNo]          
      , eph.[OfferStatusChangeDate]          
      , eph.[OfferStatusChangeLoginNo]          
      , eph.[CompanyNo]          
      , @UpdatedBy          
      , getdate()          
      , ''          
      , eph.[Notes]          
      , eph.[ROHS]          
      , eph.[CompanyNo]                
   , (  
      (isnull(eph.Price,0) / @BuyExchangeRate)  
  )   
  --* @HubExchangeRate  
      , @ClientCompanyNo             
      , @ClientCurrencyNo        
      , (isnull(country.ShippingCost,0))* @HubExchangeRate  
      , mfr.ManufacturerName            
      , eph.DateCode            
      , pk.PackageName            
      --, pr.ProductName Commented on 11 Dec 2017  
   , pr.ProductDescription  
      , eph.SupplierMOQ            
      --, eph.TotalQuantityAvailableInStock          
      , case when ISNUMERIC(eph.SupplierTotalQSA) = 1 then dbo.[stripNumeric](eph.SupplierTotalQSA) else 0 end          
      , eph.SupplierLTB            
      , eph.Notes           
      ,eph.SPQ        
      ,eph.LeadTime        
      ,eph.ROHSStatus        
      ,eph.FactorySealed        
      ,eph.MSL   
      ,@UpdatedBy     
 , ISNULL(eph.Price, 0)    
 , ISNULL(eph.CurrencyNo, isnull(company.POCurrencyNo,0))    
 , dbo.ufn_get_exchange_rate(ISNULL(eph.CurrencyNo, isnull(company.POCurrencyNo,0)), GETDATE())    
 , @LinkMultiCurrencyNo  
 , eph.MSLLevelNo
 ,	company.SupplierWarranty  
FROM   [BorisGlobalTraderImports].dbo.tbExcess eph            
 LEFT JOIN dbo.tbCompany company on eph.CompanyNo = company.CompanyId         
 LEFT JOIN dbo.tbCountry country on country.CountryId = company.DefaultPOShipCountryNo   AND company.ClientNo=country.ClientNo      
 LEFT JOIN dbo.tbManufacturer mfr ON mfr.ManufacturerId=eph.ManufacturerNo      
 LEFT JOIN dbo.tbProduct pr ON pr.ProductId=eph.ProductNo      
 LEFT JOIN dbo.tbPackage pk ON pk.PackageId=eph.PackageNo         
 WHERE  eph.ExcessId = @ExcessNo          
 
 END
 ELSE -- Get data from archieve database
 BEGIN
        SELECT @OfferCurrencyNo = CurrencyNo, @DefaultPOCompanyNo = c.POCurrencyNo , @GlobalProductNo = isnull(p.GlobalProductNo,0) 
		FROM [BorisGlobalTraderImports].dbo.tbExcess_Arc e    
		LEFT JOIN dbo.tbCompany c on e.CompanyNo = c.CompanyId   
		LEFT JOIN tbProduct p on e.ProductNo = p.ProductId  
		WHERE e.ExcessId = @ExcessNo  
  
		SELECT @LinkMultiCurrencyNo = l.LinkMultiCurrencyId ,@ClientCurrencyNo = l.SupplierCurrencyNo  
		from tbCurrency c left join tbLinkMultiCurrency l   on l.GlobalCurrencyNo = c.GlobalCurrencyNo   
		WHERE  l.ClientNo = @ClientNo and c.ClientNo = 114 AND c.CurrencyId = @OfferCurrencyNo  
  
		SELECT  @HubCurrencyNo = dbo.ufn_get_HUB_DefaultCurrencyNo(ISNULL(@OfferCurrencyNo,@DefaultPOCompanyNo),@ClientNo,114)  
		--Get the buy exchange rate .66  
		SELECT @BuyExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@OfferCurrencyNo, isnull(@DefaultPOCompanyNo,0)), GETDATE())    
		--Get Hub Exchange rate             
		SELECT @HubExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@HubCurrencyNo, 0), GETDATE())   
  
		select top 1 @ClientProductNo = ProductId from tbProduct where GlobalProductNo = @GlobalProductNo and ClientNo = @ClientNo and isnull(Inactive,0) = 0  

		  IF @LinkMultiCurrencyNo IS NULL
		   BEGIN
		     SELECT @ClientCode = ClientCode  FROM tbclient where clientid=@ClientNo
			 select @HubCurrencyName = CurrencyCode   from tbCurrency where CurrencyId = @OfferCurrencyNo
		     SET @LinkCurrencyMsg = 'Cannot use '+@HubCurrencyName+' currency for the '+@ClientCode+' client. Kindly contact administrator.';
			 SET @SourcingResultId = 0
			 RETURN
		   END

      INSERT  INTO [dbo].[tbSourcingResult] (          
     [CustomerRequirementNo]          
     , [SourcingTable]          
     , [SourcingTableItemNo]          
     , [FullPart]          
     , [Part]          
     , [ManufacturerNo]          
     , [DateCode]          
     , [ProductNo]          
     , [PackageNo]          
     , [Quantity]          
     , [Price]          
     , [CurrencyNo]          
     , [OriginalEntryDate]          
     , [Salesman]          
     , [OfferStatusNo]          
     , [OfferStatusChangeDate]          
     , [OfferStatusChangeLoginNo]          
     , [SupplierNo]          
     , [UpdatedBy]          
     , [DLUP]          
     , [TypeName]          
     , [Notes]          
     , [ROHS]          
     , POHubCompanyNo          
     , SupplierPrice                
     , ClientCompanyNo          
     , ClientCurrencyNo          
     , EstimatedShippingCost        
     , SupplierManufacturerName              
     , SupplierDateCode              
     , SupplierPackageType              
     , SupplierProductType              
     , SupplierMOQ              
     , SupplierTotalQSA              
     , SupplierLTB              
     , SupplierNotes               
     , SPQ        
     , LeadTime      
     , ROHSStatus        
     , FactorySealed        
     , MSL   
     , Buyer  
  , ActualPrice  
     , ActualCurrencyNo  
  , ExchangeRate       
  , LinkMultiCurrencyNo  
  , MSLLevelNo  
  ,	SupplierWarranty
         )
               
                        
      SELECT @CustomerRequirementNo      
      , 'EXPH'          
      , @ExcessNo          
      , eph.[FullPart]          
      , eph.[Part]      
      --, isnull(@ManufacturerNo, 0)    
	   , isnull(mfr.ManufacturerId ,0)     
      , eph.[DateCode]          
      , isnull(@ClientProductNo ,@ProductNo)     
      , isnull(@PackageNo, 0)    
   , case when @IsClone = 1 then isnull(eph.[Quantity], 0) else  0  end  
      --, isnull(eph.[Quantity], 0)          
   , (  
     (isnull(eph.Price,0) / @BuyExchangeRate)   
         +  ((isnull(eph.Price,0) / @BuyExchangeRate) * ISNULL(company.UPLiftPrice,0))/100   
    )   
    * @HubExchangeRate  
      --, isnull(eph.[CurrencyNo], 0)    
   , @HubCurrencyNo        
      , eph.[OriginalEntryDate]          
      , isnull(eph.[Salesman], 0)          
      , eph.[OfferStatusNo]          
      , eph.[OfferStatusChangeDate]          
      , eph.[OfferStatusChangeLoginNo]          
      , eph.[CompanyNo]          
      , @UpdatedBy          
      , getdate()          
      , ''          
      , eph.[Notes]          
      , eph.[ROHS]          
      , eph.[CompanyNo]                
   , (  
      (isnull(eph.Price,0) / @BuyExchangeRate)  
  )   
  --* @HubExchangeRate  
      , @ClientCompanyNo             
      , @ClientCurrencyNo        
      , (isnull(country.ShippingCost,0))* @HubExchangeRate  
      , mfr.ManufacturerName            
      , eph.DateCode            
      , pk.PackageName            
      --, pr.ProductName Commented on 11 Dec 2017  
   , pr.ProductDescription  
      , eph.SupplierMOQ            
      --, eph.TotalQuantityAvailableInStock          
      , case when ISNUMERIC(eph.SupplierTotalQSA) = 1 then dbo.[stripNumeric](eph.SupplierTotalQSA) else 0 end         
      , eph.SupplierLTB            
      , eph.Notes           
      ,eph.SPQ        
      ,eph.LeadTime        
      ,eph.ROHSStatus        
      ,eph.FactorySealed        
      ,eph.MSL   
      ,@UpdatedBy     
 , ISNULL(eph.Price, 0)    
 , ISNULL(eph.CurrencyNo, isnull(company.POCurrencyNo,0))    
 , dbo.ufn_get_exchange_rate(ISNULL(eph.CurrencyNo, isnull(company.POCurrencyNo,0)), GETDATE())    
 , @LinkMultiCurrencyNo  
 , eph.MSLLevelNo
 ,	company.SupplierWarranty          
 FROM   [BorisGlobalTraderArchive].dbo.tbExcess_Arc eph                                       
 LEFT JOIN dbo.tbCompany company on eph.CompanyNo = company.CompanyId         
 LEFT JOIN dbo.tbCountry country on country.CountryId = company.DefaultPOShipCountryNo   AND company.ClientNo=country.ClientNo      
 LEFT JOIN dbo.tbManufacturer mfr ON mfr.ManufacturerId=eph.ManufacturerNo      
 LEFT JOIN dbo.tbProduct pr ON pr.ProductId=eph.ProductNo      
 LEFT JOIN dbo.tbPackage pk ON pk.PackageId=eph.PackageNo         
 WHERE  eph.ExcessId =@ExcessNo  
 END
                                  
          
    SET @SourcingResultId = scope_identity()        
    UPDATE tbCustomerRequirement set HasHubSourcingResult = 1 where CustomerRequirementId = @CustomerRequirementNo  
            END          
           ELSE           
            BEGIN          
                SET @SourcingResultId = 1 ; --spoof an OK result          
            END          
       
    END 










GO


