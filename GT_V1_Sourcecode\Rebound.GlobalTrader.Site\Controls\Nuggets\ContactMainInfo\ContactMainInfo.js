Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.initializeBase(this,[n]);this._intCompanyID=-1;this._intContactID=-1};Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[0]),this._frmEdit.addSave(Function.createDelegate(this,this.saveEdit)),this._frmEdit.addCancel(Function.createDelegate(this,this.cancelEdit)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)))},dispose:function(){this.isDisposed||(this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._frmEdit&&this._frmEdit.dispose(),this._ibtnEdit=null,this._frmEdit=null,this._intCompanyID=null,this._intContactID=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.callBaseMethod(this,"dispose"))},getData:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ContactMainInfo");n.set_DataObject("ContactMainInfo");n.set_DataAction("GetData");n.addParameter("id",this._intContactID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result;this.setFieldValue("ctlFirstName",t.FirstName);this.setFieldValue("ctlSurname",t.Surname);this.setFieldValue("ctlJobTitle",t.JobTitle);this.setFieldValue("ctlTel",t.Tel);this.setFieldValue("ctlFax",t.Fax);this.setFieldValue("ctlTelExt",t.TelExt);this.setFieldValue("ctlHomeTel",t.HomeTel);this.setFieldValue("ctlMobileTel",t.MobileTel);this.setFieldValueEmail("ctlEmail",t.Email);this.setFieldValue("ctlIsEmailTextOnly",t.IsEmailTextOnly);this.setFieldValue("ctlNickname",t.Nickname);this.setFieldValue("ctlPersonalAddress",t.CompanyAdd);this.setFieldValue("hidFinanceContacts",t.FinanceContact);this.setFieldValue("hidCompanyAddress",t.CompanyAddress);this.setFieldValue("hidInactive",t.Inactive);this.setFieldValue("ctlIsSendShipmentNotification",t.IsSendShipmentNotification);this.setDLUP(t.DLUP);this.getDataOK_End()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},saveEdit:function(){},cancelEdit:function(){this.hideEditForm();this.showContent(!0)},showEditForm:function(){this._frmEdit._intCompanyNo=this._intCompanyID;this._frmEdit._intContactID=this._intContactID;this._frmEdit.setFieldValue("ctlFirstName",this.getFieldValue("ctlFirstName"));this._frmEdit.setFieldValue("ctlSurname",this.getFieldValue("ctlSurname"));this._frmEdit.setFieldValue("ctlJobTitle",this.getFieldValue("ctlJobTitle"));this._frmEdit.setFieldValue("ctlTel",this.getFieldValue("ctlTel"));this._frmEdit.setFieldValue("ctlFax",this.getFieldValue("ctlFax"));this._frmEdit.setFieldValue("ctlExtension",this.getFieldValue("ctlTelExt"));this._frmEdit.setFieldValue("ctlHomeTel",this.getFieldValue("ctlHomeTel"));this._frmEdit.setFieldValue("ctlMobileTel",this.getFieldValue("ctlMobileTel"));this._frmEdit.setFieldValue("ctlEmail",this.getFieldValue("ctlEmail"));this._frmEdit.setFieldValue("ctlTextOnlyEmail",this.getFieldValue("ctlIsEmailTextOnly"));this._frmEdit.setFieldValue("ctlNickname",this.getFieldValue("ctlNickname"));this._frmEdit.setFieldValue("ctlCompanyAddress",this.getFieldValue("hidCompanyAddress"));this._frmEdit.setFieldValue("ctlFinanceContacts",Boolean.parse(this.getFieldValue("hidFinanceContacts")));this._frmEdit.setFieldValue("ctlInactive",Boolean.parse(this.getFieldValue("hidInactive")));this._frmEdit.setFieldValue("ctlSendShipmentNotification",this.getFieldValue("ctlIsSendShipmentNotification"));this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1)},saveEditComplete:function(){this.hideEditForm();this.showContentLoading(!1);this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete()},saveEditError:function(){this.showError(!0,this._frmEdit._strErrorMessage)}};Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);