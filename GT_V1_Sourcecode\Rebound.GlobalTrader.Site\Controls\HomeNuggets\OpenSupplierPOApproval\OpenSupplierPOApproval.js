Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.prototype={get_pnlOpenSupplierPOApproval:function(){return this._pnlOpenSupplierPOApproval},set_pnlOpenSupplierPOApproval:function(n){this._pnlOpenSupplierPOApproval!==n&&(this._pnlOpenSupplierPOApproval=n)},get_tblOpenSupplierPOApproval:function(){return this._tblOpenSupplierPOApproval},set_tblOpenSupplierPOApproval:function(n){this._tblOpenSupplierPOApproval!==n&&(this._tblOpenSupplierPOApproval=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getData));this.getData()},dispose:function(){this.isDisposed||(this._tblOpenSupplierPOApproval&&this._tblOpenSupplierPOApproval.dispose(),this._pnlOpenSupplierPOApproval=null,this._tblOpenSupplierPOApproval=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.callBaseMethod(this,"dispose"))},setupLoadingState:function(){$R_FN.showElement(this._pnlOpenSupplierPOApproval,!1);Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.callBaseMethod(this,"setupLoadingState")},showNoData:function(n){this.showContent(!0);$R_FN.showElement(this._pnlNoData,n)},getData:function(){this.setupLoadingState();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/HomeNuggets/OpenSupplierPOApproval");n.set_DataObject("OpenSupplierPOApproval");n.set_DataAction("GetData");n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.homeNuggetDataError));n.addTimeout(Function.createDelegate(this,this.homeNuggetDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataComplete:function(n){var i=n._result,u,t,r;for(this._tblOpenSupplierPOApproval.clearTable(),r=0;r<i.POApprovalStatus.length;r++)t=i.POApprovalStatus[r],u=[$RGT_nubButton_PurchaseOrder(t.ID,t.No),t.LineManagerApproveStatus,t.QualityStatus,t.Date,t.DateOrdered],this._tblOpenSupplierPOApproval.addRow(u,null);$R_FN.showElement(this._pnlOpenSupplierPOApproval,i.POApprovalStatus.length>0);this.hideLoading();this.showNoneFoundOrContent(i.POApprovalStatus.length)}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.OpenSupplierPOApproval",Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base,Sys.IDisposable);