<%@ Control Language="C#" CodeBehind="RequiredClientBomItem.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.RequiredClientBomItem" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">

	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlReqNo" runat="server" ResourceTitle="RequirementNo"/>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<%--<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />--%>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlBOM" runat="server" ResourceTitle="ReqBOMName" />
	</FieldsLeft>
	<FieldsRight>
		<%--<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" />--%>
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedFrom" runat="server" ResourceTitle="DateReceivedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedTo" runat="server" ResourceTitle="DateReceivedTo" />
		<%--<ReboundUI_FilterDataItemRow:DropDown id="ctlClient" runat="server" ResourceTitle="Client" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" />--%>
	</FieldsRight>
	
</ReboundUI_ItemSearch:DesignBase>
