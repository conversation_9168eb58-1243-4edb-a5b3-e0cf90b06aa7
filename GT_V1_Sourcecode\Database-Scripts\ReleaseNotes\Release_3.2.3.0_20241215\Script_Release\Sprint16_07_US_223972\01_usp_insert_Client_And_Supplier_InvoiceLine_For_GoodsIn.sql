﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_insert_Client_And_Supplier_InvoiceLine_For_GoodsIn]    Script Date: 12/4/2024 10:57:57 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-223972]	    Phuc Hoang			03-Dec-2024		Update			usp_create_Client_And_Supplier_Invoice
==========================================================================================================
*/

CREATE OR ALTER  PROCEDURE  [dbo].[usp_insert_Client_And_Supplier_InvoiceLine_For_GoodsIn]                            
--********************************************************************************************                          
--* Marker     Changed by      Date         Remarks                                
--* [001]      Vinay           06/04/2016   Group the client invoice      
--********************************************************************************************                          
    @ClientNo int        
  , @PurchaseHubClientNo int = 114         
  , @ClientInvoiceNo INT      
  , @SupplierInvoiceNo INT      
  , @GoodsIn INT = NULL   
  , @TaxMatrixNo int 
  , @HubTaxNo INT
  , @HubSellCurrencyNo INT       
  , @LinkMultiCurrencyNo INT
  , @SplitPONumber INT               
  --, @InvoiceId int OUTPUT                          
AS                           
    BEGIN                          
      
    DECLARE @GoodsInNumbers NVARCHAR(1000)  
    DECLARE @PurchaseOrderNumbers NVARCHAR(1000)  
    DECLARE @Narratives NVARCHAR(1000)  
    DECLARE @SecondRefs NVARCHAR(1000) 
    
      IF @TaxMatrixNo < = 0
	  BEGIN           
		SELECT  @HubTaxNo = TaxNo FROM tbClient WHERE ClientId = @ClientNo              
	  END
	        
 --Get gi data in temp table       
 SELECT                       
      gil.GoodsInLineId                        
    , gi.GoodsInId                        
    , ISNULL(ipl.Price,0) as Price         
    , gil.Part                        
    , gil.Quantity                        
    , gil.LandedCost                        
    , gil.PurchaseOrderLineNo   
    , po.PurchaseOrderNumber  
    , gi.GoodsInNumber 
	, po.Buyer  
	, po.DivisionNo 
	, gi.DateReceived                   
     INTO #TEMPGoodsInInvoice  FROM tbGoodsInLine gil JOIN tbGoodsIn gi ON gi.GoodsInId = gil.GoodsInNo                 
		JOIN  tbInternalPurchaseOrderLine ipl ON gil.PurchaseOrderLineNo = ipl.PurchaseOrderLineNo      
		LEFT JOIN tbInternalPurchaseOrder IP ON ipl.InternalPurchaseOrderNo = IP.InternalPurchaseOrderId     
		LEFT JOIN tbPurchaseOrderLine pol on gil.PurchaseOrderLineNo = pol.PurchaseOrderLineId  
		LEFT JOIN tbPurchaseOrder po on pol.PurchaseOrderNo = po.PurchaseOrderId   
    WHERE IP.ClientNo = @ClientNo AND gil.GoodsInNo = @GoodsIn AND ISNULL(gil.IsInvoiceCreated,0) = 0 
		AND  gil.DateInspected IS NOT NULL and gil.CustomerRMALineNo is null 
		AND IP.HubCurrencyNo = @HubSellCurrencyNo AND gil.LinkMultiCurrencyNo = @LinkMultiCurrencyNo
		AND dbo.ufn_splitP_Number(po.PurchaseOrderNumber)= @SplitPONumber 
    
    
  -----------Narrative And SecondRef Start        
    SET @Narratives=(SELECT STUFF((SELECT DISTINCT '/' + CAST(GoodsInNumber AS VARCHAR)  FROM #TEMPGoodsInInvoice tcn1  FOR XML PATH('')) ,1,1,''))  
    SET @SecondRefs=(SELECT STUFF((SELECT DISTINCT '/' + CAST(PurchaseOrderNumber AS VARCHAR)  FROM #TEMPGoodsInInvoice tcn1  FOR XML PATH('')) ,1,1,''))  
        
      -------------------------Narrative  
      IF(LEN(@Narratives)>40)  
      BEGIN  
         SET @GoodsInNumbers=(SELECT SUBSTRING((SELECT STUFF((SELECT DISTINCT '/' + CAST(GoodsInNumber AS VARCHAR)  FROM #TEMPClientInvoice tcn1  FOR XML PATH('')) ,1,1,'')) ,0,40))  
      END  
        
      ELSE  
      BEGIN  
      SET @GoodsInNumbers=@Narratives;  
      END  
     ----------------------------Second Ref   
     IF(LEN(@SecondRefs)>16)  
      BEGIN  
        SET @PurchaseOrderNumbers=(SELECT SUBSTRING((SELECT STUFF((SELECT DISTINCT '/' + CAST(PurchaseOrderNumber AS VARCHAR)  FROM #TEMPClientInvoice tcn2  FOR XML PATH('')) ,1,1,'')) ,0,16))  
      END  
        
      ELSE  
      BEGIN  
      SET @PurchaseOrderNumbers=@SecondRefs  
      END  
 -----------Narrative And SecondRef End     
   
                            
-- Insert into client invoice line      
INSERT INTO tbClientInvoiceLine                    
(                    
   ClientInvoiceNo                    
  ,GoodsInLineNo                    
  ,GoodsInNo                    
  ,UnitPrice                  
  ,Part                    
  ,QtyReceived                    
  ,Landedcost           
  ,PurchaseOrderLineNo                    
  ,DLUP                    
  ,UpdatedBy 
  , Salesman   
  , DivisionNo
  , LinkMultiCurrencyNo
      )                    
  SELECT 
   @ClientInvoiceNo                  
 , GoodsInLineId                        
 , GoodsInId                        
 , Price --GA
 , Part                        
 , Quantity                        
 , LandedCost                        
 , PurchaseOrderLineNo   
 , CURRENT_TIMESTAMP                    
 , 0
 , Buyer  
 , DivisionNo
 , @LinkMultiCurrencyNo
  FROM #TEMPGoodsInInvoice      
        
    -- Insert into supplier invoice line      
      
  INSERT INTO tbSupplierInvoiceLine                    
      (                    
   SupplierInvoiceNo                    
  ,GoodsInLineNo                    
  ,GoodsInNo                    
  ,UnitPrice                    
  ,Part                    
  ,QtyReceived                    
  ,Landedcost                    
  ,PurchaseOrderLineNo                    
  ,DLUP                    
  ,UpdatedBy                    
      )                    
  SELECT                         
  @SupplierInvoiceNo                    
 , GoodsInLineId                        
 , GoodsInId                        
 , Price --GA         
 , Part                        
 , Quantity                        
 , LandedCost                        
 , PurchaseOrderLineNo                        
 , CURRENT_TIMESTAMP                    
 , 0                       
  FROM #TEMPGoodsInInvoice      
      
  -- update GI , if supplier invoice created      
  UPDATE tbGoodsInLine SET IsInvoiceCreated = 1       
  FROM tbGoodsInLine gil JOIN #TEMPGoodsInInvoice tmp  ON tmp.GoodsInLineId = gil.GoodsInLineId    
  
   DECLARE @InvoiceAmount FLOAT=0        
     DECLARE @Tax FLOAT=0      
    DECLARE @DeliveryCharge FLOAT=0      
    DECLARE @CreditCardFee FLOAT=0      
    DECLARE @BankFee FLOAT=0      
	declare @TotalInvoiceAmount float 

	--select @InvoiceAmount = SUM(ISNULL((ISNULL(z.ClientPrice,ipl.Price) * z.Quantity), 0))    
 --  FROM dbo.tbGoodsInLine z     
 -- JOIN tbPurchaseOrderLine pol ON z.PurchaseOrderLineNo = pol.PurchaseOrderLineId    
 -- JOIN tbInternalPurchaseOrderLine ipl on pol.PurchaseOrderLineId = ipl.PurchaseOrderLineNo    
 -- WHERE z.GoodsInNo  in(select distinct GoodsInId from #TEMPGoodsInInvoice)


  	select @InvoiceAmount = Round(SUM(ISNULL((ISNULL(UnitPrice,0) * QtyReceived), 0)),2)    
    FROM tbSupplierInvoiceLine  where SupplierInvoiceNo =  @SupplierInvoiceNo  
    
     SELECT  @Tax = ROUND(sum((ISNULL(dbo.ufn_get_taxrate(@HubTaxNo, @PurchaseHubClientNo, isnull(DateReceived, getdate())),0)/100)*(Price*Quantity)),2)
      FROM  #TEMPGoodsInInvoice

     
     SELECT 
     --@Tax= Round(SUM((ISNULL(dbo.ufn_get_taxrate(@HubTaxNo, @PurchaseHubClientNo, isnull(DateReceived, getdate())),0)/100)*@InvoiceAmount),2)
     @DeliveryCharge= Round(sum(isnull(DeliveryCharge,0)),2),@CreditCardFee= Round(sum(isnull(CreditCardFee,0)),2), 
	 @BankFee=Round(sum(isnull(BankFee,0)),2)  FROM tbGoodsIn WHERE GoodsInId 
	 in(select distinct GoodsInId from #TEMPGoodsInInvoice)

	 set @TotalInvoiceAmount = isnull(@InvoiceAmount,0) + isnull(@Tax,0) + isnull(@DeliveryCharge,0) + isnull(@CreditCardFee,0) + isnull(@BankFee,0)  
      
     --UPDATE tbSupplierInvoice SET  WHERE SupplierInvoiceId=@SupplierInvoiceNo      
    -- UPDATE tbClientInvoice SET SecondRef=@PurchaseOrderNumbers,Narrative=@GoodsInNumbers WHERE ClientInvoiceId=@ClientInvoiceNo    


	 UPDATE tbSupplierInvoice SET 
	 InvoiceAmount=@TotalInvoiceAmount,GoodsValue=@InvoiceAmount,Tax=@Tax,
	 DeliveryCharge=@DeliveryCharge,CreditCardFee=@CreditCardFee,BankFee=@BankFee,
	 SecondRef=@PurchaseOrderNumbers,Narrative=@GoodsInNumbers
	 WHERE SupplierInvoiceId=@SupplierInvoiceNo 
	  
	       
     UPDATE tbClientInvoice SET SecondRef=@PurchaseOrderNumbers,Narrative=@GoodsInNumbers,InvoiceAmount=@TotalInvoiceAmount,GoodsValue=@InvoiceAmount,Tax=@Tax,
	 DeliveryCharge=@DeliveryCharge,CreditCardFee=@CreditCardFee,BankFee=@BankFee WHERE ClientInvoiceId=@ClientInvoiceNo

	 UPDATE tbScheduledInvoice SET LastRun = GETDATE() WHERE ClientNo = @ClientNo 
       
      DROP TABLE #TEMPGoodsInInvoice 
                          
    END 

GO


