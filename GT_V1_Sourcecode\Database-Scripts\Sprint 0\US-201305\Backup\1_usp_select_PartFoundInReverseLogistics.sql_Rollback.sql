ALTER PROCEDURE [dbo].[usp_select_PartFoundInReverseLogistics]                                          
@PartNo NVARCHAR(50) =NULL               
/*            
    --*Marker     changed by      date         Remarks            
    --*[001]  <PERSON><PERSON>  20-Dec-2023     RP-2722/RP-2528 (The email notification should not be going to the RL team if they have offers with 0 quantity for that HUB RFQ.)            
*/            
AS                                   
BEGIN                            
   SELECT DISTINCT  TOP 1 COUNT(ReverseLogisticid)AS Partcount,Part FROM BorisGlobalTraderImports.dbo.tbReverseLogistic WHERE 
    /*[001]*/  
	--  part=@PartNo 
    fullpart=[dbo].[ufn_get_fullpart](@PartNo)       
 AND ISNULL(Quantity,0)<>0            
   /*[001]*/            
   GROUP BY ReverseLogisticid,Part              
                                  
END 

GO