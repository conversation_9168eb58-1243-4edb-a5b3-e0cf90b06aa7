﻿.iconButton_Nugget_Cease {
	background-image: url(images/buttons/nuggets/cease.gif);
}
.iconButton_Nugget_Cease_Disabled {
	background-image: url(images/buttons/nuggets/cease_x.gif);
}
.iconButton_Nugget_OK {
	background-image: url(images/buttons/nuggets/ok.gif);
}
.iconButton_Nugget_OK_Disabled {
	background-image: url(images/buttons/nuggets/ok_x.gif);
}
.iconButton_Nugget_Source {
	background-image: url(images/buttons/nuggets/source.gif);
}
.iconButton_Nugget_Source_Disabled {
	background-image: url(images/buttons/nuggets/source_x.gif);
}
.iconButton_Nugget_RequestForQuote {
	background-image: url(images/buttons/nuggets/rfq.gif);
}
.iconButton_Nugget_RequestForQuote_Disabled {
	background-image: url(images/buttons/nuggets/rfq_x.gif);
}
.iconButton_Nugget_Post, .iconButton_Nugget_PostAll {
	background-image: url(images/buttons/nuggets/post.gif);
}
.iconButton_Nugget_Post_Disabled, .iconButton_Nugget_PostAll_Disabled {
	background-image: url(images/buttons/nuggets/post_x.gif);
}
.iconButton_Nugget_Unpost, .iconButton_Nugget_UnpostAll {
	background-image: url(images/buttons/nuggets/unpost.gif);
}
.iconButton_Nugget_Unpost_Disabled, .iconButton_Nugget_UnpostAll_Disabled {
	background-image: url(images/buttons/nuggets/unpost_x.gif);
}
.iconButton_Nugget_ReceiveSelected {
	background-image: url(images/buttons/nuggets/receive.gif);
}
.iconButton_Nugget_ReceiveSelected_Disabled {
	background-image: url(images/buttons/nuggets/receive_x.gif);
}
.iconButton_Nugget_SelectAllAvailableForShipping {
	background-image: url(images/buttons/nuggets/select.gif);
}
.iconButton_Nugget_SelectAllAvailableForShipping_Disabled {
	background-image: url(images/buttons/nuggets/select_x.gif);
}
.iconButton_Nugget_ShipSelected {
	background-image: url(images/buttons/nuggets/ship.gif);
}
.iconButton_Nugget_ShipSelected_Disabled {
	background-image: url(images/buttons/nuggets/ship_x.gif);
}
.iconButton_Nugget_ShipAllSelected {
	background-image: url(images/buttons/nuggets/ship_all.gif);
}
.iconButton_Nugget_ShipAllSelected_Disabled {
	background-image: url(images/buttons/nuggets/ship_all_x.gif);
}
.iconButton_Nugget_Split {
	background-image: url(images/buttons/nuggets/split.gif);
}
.iconButton_Nugget_Split_Disabled {
	background-image: url(images/buttons/nuggets/split_x.gif);
}
.iconButton_Nugget_MakeAvailable {
	background-image: url(images/buttons/nuggets/release.gif);
}
.iconButton_Nugget_MakeAvailable_Disabled {
	background-image: url(images/buttons/nuggets/release_x.gif);
}
.iconButton_Nugget_Quarantine {
	background-image: url(images/buttons/nuggets/quarantine.gif);
}
.iconButton_Nugget_Quarantine_Disabled {
	background-image: url(images/buttons/nuggets/quarantine_x.gif);
}
.iconButton_Nugget_TransferStockToDifferentLot, .iconButton_Nugget_TransferServiceToDifferentLot {
	background-image: url(images/buttons/nuggets/transfer.gif);
}
.iconButton_Nugget_TransferStockToDifferentLot_Disabled, .iconButton_Nugget_TransferServiceToDifferentLot_Disabled {
	background-image: url(images/buttons/nuggets/transfer_x.gif);
}
.iconButton_Nugget_ReplaceImage {
	background-image: url(images/buttons/nuggets/replace.gif);
}
.iconButton_Nugget_ReplaceImage_Disabled {
	background-image: url(images/buttons/nuggets/replace_x.gif);
}
.iconButton_Nugget_Allocate {
	background-image: url(images/buttons/nuggets/allocate.gif);
}
.iconButton_Nugget_Allocate_Disabled {
	background-image: url(images/buttons/nuggets/allocate_x.gif);
}
.iconButton_Nugget_Authorise {
	background-image: url(images/buttons/so/authorise.gif);
}
.iconButton_Nugget_Authorise_Disabled {
	background-image: url(images/buttons/so/authorise_x.gif);
}
.iconButton_Nugget_Checked {
	background-image: url(images/buttons/so/authorise.gif);
}
.iconButton_Nugget_Checked_Disabled {
	background-image: url(images/buttons/so/authorise_x.gif);
}
.iconButton_Nugget_Deallocate {
	background-image: url(images/buttons/nuggets/deallocate.gif);
}
.iconButton_Nugget_Deallocate_Disabled {
	background-image: url(images/buttons/nuggets/deallocate_x.gif);
}
.iconButton_Nugget_Deauthorise {
	background-image: url(images/buttons/so/deauthorise.gif);
}
.iconButton_Nugget_Deauthorise_Disabled {
	background-image: url(images/buttons/so/deauthorise_x.gif);
}
.iconButton_Nugget_UnChecked {
	background-image: url(images/buttons/so/deauthorise.gif);
}
.iconButton_Nugget_UnChecked_Disabled {
	background-image: url(images/buttons/so/deauthorise_x.gif);
}
.iconButton_Nugget_Inspect {
	background-image: url(images/buttons/nuggets/inspect.gif);
}
.iconButton_Nugget_Inspect_Disabled {
	background-image: url(images/buttons/nuggets/inspect_x.gif);
}
.iconButton_Nugget_GetCounts {
	background-image: url(images/buttons/nuggets/count.gif);
}
.iconButton_Nugget_GetCounts_Disabled {
	background-image: url(images/buttons/nuggets/count_x.gif);
}
.iconButton_Nugget_Quote {
	background-image: url(images/buttons/nuggets/quote.gif);
}
.iconButton_Nugget_Quote_Disabled {
	background-image: url(images/buttons/nuggets/quote_x.gif);
}
.iconButton_Nugget_Send {
	background-image: url(images/buttons/nuggets/send.gif);
}
.iconButton_Nugget_Send_Disabled {
	background-image: url(images/buttons/nuggets/send_x.gif);
}
.iconButton_Nugget_Reply {
	background-image: url(images/buttons/nuggets/reply.gif);
}
.iconButton_Nugget_Reply_Disabled {
	background-image: url(images/buttons/nuggets/reply_x.gif);
}
.iconButton_Nugget_Forward {
	background-image: url(images/buttons/nuggets/forward.gif);
}
.iconButton_Nugget_Forward_Disabled {
	background-image: url(images/buttons/nuggets/forward_x.gif);
}
.iconButton_Nugget_NewMessage {
	background-image: url(images/buttons/nuggets/new_message.gif);
}
.iconButton_Nugget_NewMessage_Disabled {
	background-image: url(images/buttons/nuggets/new_message_x.gif);
}
.iconButton_Nugget_DeleteMessage {
	background-image: url(images/buttons/nuggets/delete_message.gif);
}
.iconButton_Nugget_DeleteMessage_Disabled {
	background-image: url(images/buttons/nuggets/delete_message_x.gif);
}
.iconButton_Nugget_MoveMessage {
	background-image: url(images/buttons/nuggets/move_message.gif);
}
.iconButton_Nugget_MoveMessage_Disabled {
	background-image: url(images/buttons/nuggets/move_message_x.gif);
}
.iconButton_Nugget_NewFolder {
	background-image: url(images/buttons/nuggets/new_folder.gif);
}
.iconButton_Nugget_NewFolder_Disabled {
	background-image: url(images/buttons/nuggets/new_folder_x.gif);
}
.iconButton_Nugget_DeleteFolder {
	background-image: url(images/buttons/nuggets/delete_folder.gif);
}
.iconButton_Nugget_DeleteFolder_Disabled {
	background-image: url(images/buttons/nuggets/delete_folder_x.gif);
}
.iconButton_Nugget_EditFolder {
	background-image: url(images/buttons/nuggets/edit_folder.gif);
}
.iconButton_Nugget_EditFolder_Disabled {
	background-image: url(images/buttons/nuggets/edit_folder_x.gif);
}
.iconButton_Nugget_MarkAsToDo {
	background-image: url(images/buttons/nuggets/todo.gif);
}
.iconButton_Nugget_MarkAsToDo_Disabled {
	background-image: url(images/buttons/nuggets/todo_x.gif);
}
.iconButton_Nugget_MarkComplete {
	background-image: url(images/buttons/nuggets/complete.gif);
}
.iconButton_Nugget_MarkComplete_Disabled {
	background-image: url(images/buttons/nuggets/complete_x.gif);
}
.iconButton_Nugget_MarkIncomplete {
	background-image: url(images/buttons/nuggets/incomplete.gif);
}
.iconButton_Nugget_MarkIncomplete_Disabled {
	background-image: url(images/buttons/nuggets/incomplete_x.gif);
}
.iconButton_Nugget_RunReport {
	background-image: url(images/buttons/nuggets/report.gif);
}
.iconButton_Nugget_RunReport_Disabled {
	background-image: url(images/buttons/nuggets/report_x.gif);
}
.iconButton_Nugget_ChangePassword {
	background-image: url(images/buttons/nuggets/changepwd.gif);
}
.iconButton_Nugget_ChangePassword_Disabled {
	background-image: url(images/buttons/nuggets/changepwd_x.gif);
}
.iconButton_Nugget_ResetPassword {
	background-image: url(images/buttons/nuggets/changepwd.gif);
}
.iconButton_Nugget_ResetPassword_Disabled {
	background-image: url(images/buttons/nuggets/changepwd_x.gif);
}
.iconButton_Nugget_Receive {
	background-image: url(images/buttons/nuggets/receive.gif);
}
.iconButton_Nugget_Receive_Disabled {
	background-image: url(images/buttons/nuggets/receive_x.gif);
}
.iconButton_Nugget_Ship, .iconButton_Nugget_ShipAll {
	background-image: url(images/buttons/nuggets/ship.gif);
}
.iconButton_Nugget_Ship_Disabled, .iconButton_Nugget_ShipAll_Disabled {
	background-image: url(images/buttons/nuggets/ship_x.gif);
}
.iconButton_Nugget_EditMembers, .iconButton_Nugget_CompanyContacts {
	background-image: url(images/buttons/nuggets/members.gif);
}
.iconButton_Nugget_EditMembers_Disabled, .iconButton_Nugget_CompanyContacts_Disabled {
	background-image: url(images/buttons/nuggets/members_x.gif);
}
.iconButton_Nugget_Enable {
	background-image: url(images/buttons/nuggets/enable.gif);
}
.iconButton_Nugget_Enable_Disabled {
	background-image: url(images/buttons/nuggets/enable_x.gif);
}
.iconButton_Nugget_EditCurrentRates {
	background-image: url(images/buttons/nuggets/count.gif);
}
.iconButton_Nugget_EditCurrentRates_Disabled {
	background-image: url(images/buttons/nuggets/count_x.gif);
}
.iconButton_Nugget_TransferStock, .iconButton_Nugget_TransferService {
	background-image: url(images/buttons/nuggets/transfer.gif);
}
.iconButton_Nugget_TransferStock_Disabled, .iconButton_Nugget_TransferService_Disabled {
	background-image: url(images/buttons/nuggets/transfer_x.gif);
}
.iconButton_Nugget_CreateSalesOrder {
	background-image: url(images/buttons/nuggets/create_so.gif);
}
.iconButton_Nugget_CreateSalesOrder_Disabled {
	background-image: url(images/buttons/nuggets/create_so_x.gif);
}
.iconButton_Nugget_Notify {
	background-image: url(images/buttons/nuggets/notify.gif);
}
.iconButton_Nugget_Notify_Disabled {
	background-image: url(images/buttons/nuggets/notify_x.gif);
}
.iconButton_Nugget_SendToSupplier
{
    background-image: url(images/buttons/nuggets/notify.gif);
}
.iconButton_Nugget_SendToSupplier_Disabled {
	background-image: url(images/buttons/nuggets/notify_x.gif);
}
.iconButton_Nugget_CreatePDF {
	background-image: url(images/buttons/nuggets/create_pdf.gif);
}
.iconButton_Nugget_CreatePDF_Disabled {
	background-image: url(images/buttons/nuggets/create_pdf_x.gif);
}
.iconButton_Nugget_ViewPDF {
	background-image: url(images/buttons/nuggets/view_pdf.gif);
}
.iconButton_Nugget_ViewPDF_Disabled {
	background-image: url(images/buttons/nuggets/view_pdf_x.gif);
}
.iconButton_Nugget_CreatePurchaseOrder {
	background-image: url(images/buttons/nuggets/create_so.gif);
}
.iconButton_Nugget_CreatePurchaseOrder_Disabled {
	background-image: url(images/buttons/nuggets/create_so_x.gif);
}
.iconButton_Nugget_Clone {
	background-image: url(images/buttons/nuggets/clone.gif);
}
.iconButton_Nugget_Clone_Disabled {
	background-image: url(images/buttons/nuggets/clone_x.gif);
}
.iconButton_Nugget_Approve {
	background-image: url(images/buttons/po/approve.gif);
}
.iconButton_Nugget_Approve_Disabled {
	background-image: url(images/buttons/po/approve_x.gif);
}
.iconButton_Nugget_Disapprove {
	background-image: url(images/buttons/po/disapprove.gif);
}
.iconButton_Nugget_Disapprove_Disabled {
	background-image: url(images/buttons/po/disapprove_x.gif);
}
.iconButton_Nugget_Export {
	background-image: url(images/buttons/inv/export.gif);
}
.iconButton_Nugget_Export_Disabled {
	background-image: url(images/buttons/inv/export_x.gif);
}
.iconButton_Nugget_Release {
	background-image: url(images/buttons/inv/release.gif);
}
.iconButton_Nugget_ReleaseAll {
	background-image: url(images/buttons/inv/release.gif);
}
.iconButton_Nugget_ReleaseAll_Disabled {
	background-image: url(images/buttons/inv/release_x.gif);
}
.iconButton_Nugget_UnRelease {
	background-image: url(images/buttons/inv/release.gif);
}
.iconButton_Nugget_UnRelease_Disabled {
	background-image: url(images/buttons/inv/release_x.gif);
}
.iconButton_Nugget_Release_Disabled {
	background-image: url(images/buttons/inv/release_x.gif);
}

.iconButton_Nugget_NoBidAll {
	background-image: url(images/buttons/inv/NoBid.gif);
}
.iconButton_Nugget_NoBidAll_Disabled {
	background-image: url(images/buttons/inv/NoBid_x.gif);
}

.iconButton_Nugget_NoBid {
	background-image: url(images/buttons/inv/NoBid.gif);
}
.iconButton_Nugget_NoBid_Disabled {
	background-image: url(images/buttons/inv/NoBid_x.gif);
}

/* Button Strip icons */
/* ******************************************* */
.iconButton_ButtonStrip_ExpandAll {
	background-image: url(images/buttons/Buttonstrip/expand.png);
}
.iconButton_ButtonStrip_ExpandAll_Disabled {
	background-image: url(images/buttons/Buttonstrip/expand_x.png);
}
.iconButton_ButtonStrip_CollapseAll {
	background-image: url(images/buttons/Buttonstrip/collapse.png);
}
.iconButton_ButtonStrip_CollapseAll_Disabled {
	background-image: url(images/buttons/Buttonstrip/collapse_x.png);
}

/* icons in subtitle bar*/
/* ******************************************* */
a.iconButton_Sub_Company {
	background-image: url(images/buttons/subtitle/company.gif);
}
a.iconButton_Sub_New {
	background-image: url(images/buttons/subtitle/new.gif);
}
a.iconButton_Sub_Orders {
	background-image: url(images/buttons/subtitle/orders.gif);
}
a.iconButton_Sub_Data {
	background-image: url(images/buttons/subtitle/data.gif);
}
a.iconButton_Sub_Transactions {
	background-image: url(images/buttons/subtitle/transactions.gif);
}
a.iconButton_Sub_Print {
	background-image: url(images/buttons/subtitle/print.gif);
}

