///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - add full disposing event
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows");

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._blnOn = false;
		Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.callBaseMethod(this, "dispose");
	},
	
	getValue: function() {
		return this._blnOn;
	},
	
	setValue: function(v) {
		this.enableField(v);
	},
		
	reset: function() {
		this.enableField(false);
	}
	
};

Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox.registerClass("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.CheckBox", Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Base, Sys.IDisposable);
