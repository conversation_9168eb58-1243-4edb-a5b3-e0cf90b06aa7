///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Salesperson filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.prototype = {
    get_intSalesPersonID: function() { return this._intSalesPersonID; }, set_intSalesPersonID: function(value) { if (this._intSalesPersonID !== value) this._intSalesPersonID = value; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (v) { if (this._IsGSA !== v) this._IsGSA = v; },
    get_ibtnViewTask: function () { return this._ibtnViewTask; }, set_ibtnViewTask: function (v) { if (this._ibtnViewTask !== v) this._ibtnViewTask = v; },

    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/Quotes";
        this._strDataObject = "Quotes";
        if (this._ibtnViewTask) $R_IBTN.addClick(this._ibtnViewTask, Function.createDelegate(this, this.viewTask));
        this._frmAdd = $find(this._aryFormIDs[0]);
        this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
        this._frmAdd.addSaveComplete(Function.createDelegate(this, this.addComplete));
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.applySalesPersonFilter();
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intSalesPersonID = null;
        this._IsGSA = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
    },

    getDataOK: function () {
        var chkdatestatus = '';
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            chkdatestatus = '';
            var row = this._objResult.Results[i];
            if (row.DateOfferStatus == 'Green')
                chkdatestatus = 'green';
            else if (row.DateOfferStatus == 'Amber')
                chkdatestatus = '#FFBF00';
            else if (row.DateOfferStatus == 'Red')
                chkdatestatus = 'Red';
            else if (row.DateOfferStatus == 'White')
                chkdatestatus = 'White';
            else
                chkdatestatus = 'White';

            var addTaskHtml = String.format("<a href=\"javascript:void(0);\" title='Add task' onclick=\"$find('{0}').showAddForm({1},'{2}','{3}','{4}');\">" + "Add Task" + "</a>",
                this._element.id, row.ID, row.No, $R_FN.setCleanTextValue(row.CM), row.QStatus
                  ) + "&nbsp;&nbsp;&nbsp;";
            var viewTaskHtml = String.format("<a href=\"javascript:void(0);\" title='View task' onclick=\"$find('{0}').redirectToDetails({1},'{2}');\" style=\"color: {3};\">" + (row.TaskCount + " Task") + "</a>",
                this._element.id, row.ID, row.No, row.HasUnFinishedTask ? 'red' : '');
            
            var aryData = [
				$RGT_nubButton_Quote(row.ID, row.No)
				, $R_FN.writePartNo(row.Part, row.ROHS)
				, row.Price
				, row.Quantity
                , $R_FN.writeDoubleCellValue(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM) + '</span>':$RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                , $R_FN.setCleanTextValue(row.Date)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.QuoteOfferedDate), chkdatestatus == 'White' ? "<span style='background-color:" + chkdatestatus + "!important;float: right;margin-top: -17px;height: 20px;width: 20px;visibility: hidden;'></span>" : "<span style='background-color:" + chkdatestatus + "!important;float: right;margin-top: -17px;height: 20px;width: 20px;'></span>")
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Salesman),$R_FN.setCleanTextValue(row.QStatus))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.TotalValue), $R_FN.setCleanTextValue(row.TotalBase))
                , $R_FN.setCleanTextValue(row.OfferProfit)
                , (addTaskHtml + viewTaskHtml)
			];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },
    updateFilterVisibility: function() {
        this.getFilterField("ctlSalesmanName").show(this._enmViewLevel != 0);
        this.getFilterField("ctlClientName").show(this._IsGSA);
    },
    applySalesPersonFilter: function() {
    if ((this._intSalesPersonID) && this._intSalesPersonID > 0)
        this.getFilterField("ctlSalesmanName").setValue(this._intSalesPersonID);
    },
    viewTask: function () {
        location.href = ('Prf_ToDo.aspx?Category=2'), '_blank';
    },
    showAddForm: function (Id, Number, CustomerName, QuoteStatus) {
        this._frmAdd.setFormFieldsToDefaults();
        this._frmAdd.setFieldValue("ctlDueTime", "09:00");
        this._frmAdd.setFieldValue("ctlReminderTime", "09:00");
        //if (OfferedDate) {
        //    this._frmAdd.setFieldValue("ctlReminderDate", OfferedDate);
        //    this._frmAdd.setFieldValue("ctlReminderTime", OfferedTime);
        //}
        this._frmAdd.setFieldValue("ctlQuotes", Id, null, Number);
        this._frmAdd._intCategoryID = 2 //quote
        //this._frmAdd._quoteMinReminderDate = OfferedDate;
        this._frmAdd._customerName = CustomerName;
        this._frmAdd._quoteStatus = QuoteStatus;
        this.showForm(this._frmAdd, true);
    },
    hideAddForm: function () {
        this._frmAdd.resetFormData();
        this.showForm(this._frmAdd, false);
    },

    addComplete: function () {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },
    redirectToDetails: function (Id, number) {
        location.href = ('Prf_ToDo.aspx?qn=' + number +'&Category=2'), '_blank';
    }
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Quotes", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
