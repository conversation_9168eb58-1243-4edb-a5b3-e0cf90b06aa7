using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;
using System.Text;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site {
	public class SalesOrderManager {

		internal static string FormatLineAllocationShippingStatus(List<BLL.SalesOrderLine.LineCannotShipReasonList> lst) {
			string strOut = "";
			if (lst.Count == 0) {
				strOut = Functions.GetGlobalResource("Status", "ReadyToShip");
			} else {
				foreach (BLL.SalesOrderLine.LineCannotShipReasonList reason in lst) {
					if (strOut != "") strOut += "<br />";
					strOut += Functions.GetGlobalResource("Status", reason);                 
				}

			}
			return strOut;
		}
	}
}
