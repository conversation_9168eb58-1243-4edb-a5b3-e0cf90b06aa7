﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlToDoProvider : ToDoProvider
    {
        /// <summary>
        /// Delete ToDo
        /// Calls [usp_delete_ToDo]
        /// </summary>
        public override bool Delete(System.Int32? toDoId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_ToDo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ToDoId", SqlDbType.Int).Value = toDoId;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete ToDo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_ToDo]
        /// </summary>
        public override Int32 Insert(System.Int32? loginNo,
                                     System.String subject,
                                     System.DateTime? dateAdded,
                                     System.DateTime? dueDate,
                                     System.String toDoText,
                                     System.Int32? priority,
                                     System.Boolean? isComplete,
                                     System.DateTime? reminderDate,
                                     System.String reminderText,
                                     System.Boolean? reminderHasBeenNotified,
                                     System.Int32? companyNo,
                                     System.Int32? relatedMailMessageNo,
                                     System.Int32? updatedBy,
                                     System.Boolean? hasReview,
                                     System.Int32? ToDoListTypeId,
                                     System.Int32? Contact,
                                     System.Int32? QuoteNo,
                                     System.Int32? ToDoCategoryNo,
                                     System.Boolean? DailyReminder,
                                     System.Int32? SalesOrderNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ToDo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
                cmd.Parameters.Add("@Subject", SqlDbType.NVarChar).Value = subject;
                cmd.Parameters.Add("@DateAdded", SqlDbType.DateTime).Value = dateAdded;
                cmd.Parameters.Add("@DueDate", SqlDbType.DateTime).Value = dueDate;
                cmd.Parameters.Add("@ToDoText", SqlDbType.NVarChar).Value = toDoText;
                cmd.Parameters.Add("@Priority", SqlDbType.Int).Value = priority;
                cmd.Parameters.Add("@IsComplete", SqlDbType.Bit).Value = isComplete;
                cmd.Parameters.Add("@ReminderDate", SqlDbType.DateTime).Value = reminderDate;
                cmd.Parameters.Add("@ReminderText", SqlDbType.NVarChar).Value = reminderText;
                cmd.Parameters.Add("@ReminderHasBeenNotified", SqlDbType.Bit).Value = reminderHasBeenNotified;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cmd.Parameters.Add("@RelatedMailMessageNo", SqlDbType.Int).Value = relatedMailMessageNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@HasReview", SqlDbType.Bit).Value = hasReview;
                cmd.Parameters.Add("@ToDoListTypeId", SqlDbType.Int).Value = ToDoListTypeId;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = Contact;
                cmd.Parameters.Add("@QuoteNo", SqlDbType.Int).Value = QuoteNo;
                cmd.Parameters.Add("@ToDoCategoryNo", SqlDbType.Int).Value = ToDoCategoryNo;
                cmd.Parameters.Add("@DailyReminder", SqlDbType.Bit).Value = DailyReminder;
                cmd.Parameters.Add("@SalesOrderNo", SqlDbType.Int).Value = SalesOrderNo;
                cmd.Parameters.Add("@ToDoId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ToDoId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert ToDo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Get 
        /// Calls [usp_select_ToDo]
        /// </summary>
        public override ToDoDetails Get(System.Int32? toDoId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_ToDo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ToDoId", SqlDbType.Int).Value = toDoId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetToDoFromReader(reader);
                    ToDoDetails obj = new ToDoDetails();
                    obj.ToDoId = GetReaderValue_Int32(reader, "ToDoId", 0);
                    obj.LoginNo = GetReaderValue_NullableInt32(reader, "LoginNo", null);
                    obj.Subject = GetReaderValue_String(reader, "Subject", "");
                    obj.DateAdded = GetReaderValue_NullableDateTime(reader, "DateAdded", null);
                    obj.DueDate = GetReaderValue_NullableDateTime(reader, "DueDate", null);
                    obj.ToDoText = GetReaderValue_String(reader, "ToDoText", "");
                    obj.Priority = GetReaderValue_NullableInt32(reader, "Priority", null);
                    obj.IsComplete = GetReaderValue_Boolean(reader, "IsComplete", false);
                    obj.ReminderDate = GetReaderValue_NullableDateTime(reader, "ReminderDate", null);
                    obj.ReminderText = GetReaderValue_String(reader, "ReminderText", "");
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
                    obj.ReminderHasBeenNotified = GetReaderValue_Boolean(reader, "ReminderHasBeenNotified", false);
                    obj.RelatedMailMessageNo = GetReaderValue_NullableInt32(reader, "RelatedMailMessageNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.LoginName = GetReaderValue_String(reader, "LoginName", "");
                    obj.DailyReminder = GetReaderValue_Boolean(reader, "DailyReminder", false);
                    obj.HasReview = GetReaderValue_NullableBoolean(reader, "HasReview", null);
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyId", null);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ToDoListTypeId = GetReaderValue_NullableInt32(reader, "TypeNo", null);
                    obj.Contact = GetReaderValue_NullableInt32(reader, "ContactNo", null);
                    obj.QuoteNumber = GetReaderValue_Int32(reader, "QuoteNumber", 0);
                    obj.QuoteNo = GetReaderValue_Int32(reader, "QuoteNo", 0);
                    obj.CustomerName = GetReaderValue_String(reader, "CustomerName", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ToDo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListAlertForLogin 
        /// Calls [usp_selectAll_ToDo_Alert_for_Login]
        /// </summary>
        public override List<ToDoDetails> GetListAlertForLogin(System.Int32? loginNo, System.DateTime? now)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_ToDo_Alert_for_Login", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
                cmd.Parameters.Add("@Now", SqlDbType.DateTime).Value = now;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ToDoDetails> lst = new List<ToDoDetails>();
                while (reader.Read())
                {
                    ToDoDetails obj = new ToDoDetails();
                    obj.ToDoId = GetReaderValue_Int32(reader, "ToDoId", 0);
                    obj.DueDate = GetReaderValue_NullableDateTime(reader, "DueDate", null);
                    obj.ReminderText = GetReaderValue_String(reader, "ReminderText", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ToDos", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListByLogin 
        /// Calls [usp_selectAll_ToDo_by_Login]
        /// </summary>
        public override List<ToDoDetails> GetListByLogin(System.Int32? loginNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_ToDo_by_Login", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ToDoDetails> lst = new List<ToDoDetails>();
                while (reader.Read())
                {
                    ToDoDetails obj = new ToDoDetails();
                    obj.ToDoId = GetReaderValue_Int32(reader, "ToDoId", 0);
                    obj.LoginNo = GetReaderValue_NullableInt32(reader, "LoginNo", null);
                    obj.Subject = GetReaderValue_String(reader, "Subject", "");
                    obj.DateAdded = GetReaderValue_NullableDateTime(reader, "DateAdded", null);
                    obj.DueDate = GetReaderValue_NullableDateTime(reader, "DueDate", null);
                    obj.ToDoText = GetReaderValue_String(reader, "ToDoText", "");
                    obj.Priority = GetReaderValue_NullableInt32(reader, "Priority", null);
                    obj.IsComplete = GetReaderValue_Boolean(reader, "IsComplete", false);
                    obj.ReminderDate = GetReaderValue_NullableDateTime(reader, "ReminderDate", null);
                    obj.ReminderText = GetReaderValue_String(reader, "ReminderText", "");
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
                    obj.ReminderHasBeenNotified = GetReaderValue_Boolean(reader, "ReminderHasBeenNotified", false);
                    obj.RelatedMailMessageNo = GetReaderValue_NullableInt32(reader, "RelatedMailMessageNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.LoginName = GetReaderValue_String(reader, "LoginName", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ToDos", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForMailMessage 
        /// Calls [usp_selectAll_ToDo_for_MailMessage]
        /// </summary>
        public override List<ToDoDetails> GetListForMailMessage(System.Int32? mailMessageNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_ToDo_for_MailMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@MailMessageNo", SqlDbType.Int).Value = mailMessageNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ToDoDetails> lst = new List<ToDoDetails>();
                while (reader.Read())
                {
                    ToDoDetails obj = new ToDoDetails();
                    obj.ToDoId = GetReaderValue_Int32(reader, "ToDoId", 0);
                    obj.LoginNo = GetReaderValue_NullableInt32(reader, "LoginNo", null);
                    obj.Subject = GetReaderValue_String(reader, "Subject", "");
                    obj.DateAdded = GetReaderValue_NullableDateTime(reader, "DateAdded", null);
                    obj.DueDate = GetReaderValue_NullableDateTime(reader, "DueDate", null);
                    obj.ToDoText = GetReaderValue_String(reader, "ToDoText", "");
                    obj.Priority = GetReaderValue_NullableInt32(reader, "Priority", null);
                    obj.IsComplete = GetReaderValue_Boolean(reader, "IsComplete", false);
                    obj.ReminderDate = GetReaderValue_NullableDateTime(reader, "ReminderDate", null);
                    obj.ReminderText = GetReaderValue_String(reader, "ReminderText", "");
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
                    obj.ReminderHasBeenNotified = GetReaderValue_Boolean(reader, "ReminderHasBeenNotified", false);
                    obj.RelatedMailMessageNo = GetReaderValue_NullableInt32(reader, "RelatedMailMessageNo", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.LoginName = GetReaderValue_String(reader, "LoginName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ToDos", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update ToDo
        /// Calls [usp_update_ToDo]
        /// </summary>
        public override bool Update(System.Int32? toDoId, System.Int32? loginNo, System.String subject, System.DateTime? dueDate, System.String toDoText, System.Int32? priority, System.Boolean? isComplete, System.DateTime? reminderDate, System.String reminderText, System.Boolean? reminderHasBeenNotified, System.Int32? companyNo, System.Int32? relatedMailMessageNo, System.Int32? updatedBy, System.Boolean? HasReview, System.Int32? ToDoListTypeId, System.Int32? ContactNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ToDo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ToDoId", SqlDbType.Int).Value = toDoId;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;
                cmd.Parameters.Add("@Subject", SqlDbType.NVarChar).Value = subject;
                cmd.Parameters.Add("@DueDate", SqlDbType.DateTime).Value = dueDate;
                cmd.Parameters.Add("@ToDoText", SqlDbType.NVarChar).Value = toDoText;
                cmd.Parameters.Add("@Priority", SqlDbType.Int).Value = priority;
                cmd.Parameters.Add("@IsComplete", SqlDbType.Bit).Value = isComplete;
                cmd.Parameters.Add("@ReminderDate", SqlDbType.DateTime).Value = reminderDate;
                cmd.Parameters.Add("@ReminderText", SqlDbType.NVarChar).Value = reminderText;
                cmd.Parameters.Add("@ReminderHasBeenNotified", SqlDbType.Bit).Value = reminderHasBeenNotified;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyNo;
                cmd.Parameters.Add("@RelatedMailMessageNo", SqlDbType.Int).Value = relatedMailMessageNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@HasReview", SqlDbType.Bit).Value = HasReview;
                cmd.Parameters.Add("@ToDoListTypeId", SqlDbType.Int).Value = ToDoListTypeId;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = ContactNo;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update ToDo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update ToDo
        /// Calls [usp_update_ToDo_Complete]
        /// </summary>
        public override bool UpdateComplete(System.Int32? toDoId, System.Boolean? isComplete, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ToDo_Complete", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ToDoId", SqlDbType.Int).Value = toDoId;
                cmd.Parameters.Add("@IsComplete", SqlDbType.Bit).Value = isComplete;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update ToDo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update ToDo
        /// Calls [usp_update_ToDo_Dismiss]
        /// </summary>
        public override bool UpdateDismiss(System.Int32? toDoId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ToDo_Dismiss", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ToDoId", SqlDbType.Int).Value = toDoId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update ToDo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update ToDo
        /// Calls [usp_update_ToDo_Snooze]
        /// </summary>
        public override bool UpdateSnooze(System.Int32? toDoId, System.Int32? snoozeMinutes, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ToDo_Snooze", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ToDoId", SqlDbType.Int).Value = toDoId;
                cmd.Parameters.Add("@SnoozeMinutes", SqlDbType.Int).Value = snoozeMinutes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update ToDo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_selectall_toDoListTask]
        /// </summary>
        /// <param name="createdDateFrom"></param>
        /// <param name="createdDateTo"></param>
        /// <param name="taskDateFrom"></param>
        /// <param name="taskDateTo"></param>
        /// <param name="taskType"></param>
        /// <param name="taskStatus"></param>
        /// <param name="customerName"></param>
        /// <param name="salesPerson"></param>
        /// <returns></returns>
        public override List<ToDoDetails> DataListNugget(System.DateTime? createdDateFrom,
                                                         System.DateTime? createdDateTo,
                                                         System.DateTime? taskDateFrom,
                                                         System.DateTime? taskDateTo,
                                                         System.Int32? taskType,
                                                         System.String taskStatus,
                                                         System.String customerName,
                                                         System.Int32? salesPerson,
                                                         System.Int32? loginId,
                                                         System.Int32? clientId,
                                                         System.Int32? OrderBy,
                                                         System.Int32? SortDir,
                                                         System.Int32? PageIndex,
                                                         System.Int32? PageSize,
                                                         System.Boolean ReviewOnly,
                                                         System.DateTime? TaskReminderDate,
                                                         int? TodoID,
                                                         int? taskCategoryNo,
                                                         string quoteNumber)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectall_toDoListTask", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = OrderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = SortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = PageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = PageSize;
                cmd.Parameters.Add("@CreatedDateFrom", SqlDbType.DateTime).Value = createdDateFrom;
                cmd.Parameters.Add("@CreatedDateTo", SqlDbType.DateTime).Value = createdDateTo;
                cmd.Parameters.Add("@TaskDateFrom", SqlDbType.DateTime).Value = taskDateFrom;
                cmd.Parameters.Add("@TaskDateTo", SqlDbType.DateTime).Value = taskDateTo;
                cmd.Parameters.Add("@TaskType", SqlDbType.Int).Value = taskType;
                cmd.Parameters.Add("@TaskStatus", SqlDbType.VarChar).Value = taskStatus;
                cmd.Parameters.Add("@CustomerName", SqlDbType.VarChar).Value = customerName;
                cmd.Parameters.Add("@SalesPerson", SqlDbType.Int).Value = salesPerson;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ReviewOnly", SqlDbType.Bit).Value = ReviewOnly;
                cmd.Parameters.Add("@TaskReminderDate", SqlDbType.DateTime).Value = TaskReminderDate;
                cmd.Parameters.Add("@ToDoID", SqlDbType.Int).Value = TodoID;
                cmd.Parameters.Add("@CategoryNo", SqlDbType.Int).Value = taskCategoryNo;
                cmd.Parameters.Add("@QuoteNumber", SqlDbType.NVarChar).Value = quoteNumber;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ToDoDetails> lst = new List<ToDoDetails>();
                while (reader.Read())
                {
                    ToDoDetails obj = new ToDoDetails();
                    obj.ToDoListId = GetReaderValue_Int32(reader, "ToDoListId", 0);
                    obj.CreatedDateFrom = GetReaderValue_NullableDateTime(reader, "CreatedDateFrom", null);
                    obj.CreatedDateTo = GetReaderValue_NullableDateTime(reader, "CreatedDateTo", null);
                    obj.TaskDateFrom = GetReaderValue_NullableDateTime(reader, "TaskDateFrom", null);
                    obj.TaskDateTo = GetReaderValue_NullableDateTime(reader, "TaskDateTo", null);
                    obj.TaskType = GetReaderValue_String(reader, "TaskType", "");
                    obj.TaskStatus = GetReaderValue_String(reader, "TaskStatus", "");
                    obj.CustomerName = GetReaderValue_String(reader, "CustomerName", "");
                    obj.SalesPersonName = GetReaderValue_String(reader, "SalesPersonName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.ReminderDate = GetReaderValue_NullableDateTime(reader, "ReminderDate", null);
                    obj.IsComplete = GetReaderValue_Boolean(reader, "IsComplete", false);
                    obj.TaskTitle = GetReaderValue_String(reader, "TaskTitle", "");
                    obj.TaskReminderDate = GetReaderValue_NullableDateTime(reader, "TaskReminderDate", null);
                    obj.ToDoCategoryNo = GetReaderValue_Int32(reader, "ToDoCategoryNo", 0);
                    obj.ToDoCategoryName = GetReaderValue_String(reader, "ToDoCategoryName", "");
                    obj.QuoteNo = GetReaderValue_Int32(reader, "QuoteNo", 0);
                    obj.QuoteNumber = GetReaderValue_Int32(reader, "QuoteNumber", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get (DataListNugget) of to do list task", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override DataTable DataListNugget_Export(System.DateTime? createdDateFrom, System.DateTime? createdDateTo, System.DateTime? taskDateFrom, System.DateTime? taskDateTo, System.String taskType, System.String taskStatus, System.String customerName, System.Int32? salesPerson, System.Int32? loginId, System.Int32? clientId, System.Int32? OrderBy, System.Int32? SortDir, System.Int32? PageIndex, System.Int32? PageSize, System.Boolean ReviewOnly, System.DateTime? TaskReminderDate)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_ToDoListTask_Export", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = OrderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = SortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = PageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = PageSize;
                cmd.Parameters.Add("@CreatedDateFrom", SqlDbType.DateTime).Value = createdDateFrom;
                cmd.Parameters.Add("@CreatedDateTo", SqlDbType.DateTime).Value = createdDateTo;
                cmd.Parameters.Add("@TaskDateFrom", SqlDbType.DateTime).Value = taskDateFrom;
                cmd.Parameters.Add("@TaskDateTo", SqlDbType.DateTime).Value = taskDateTo;
                cmd.Parameters.Add("@TaskType", SqlDbType.VarChar).Value = taskType;
                cmd.Parameters.Add("@TaskStatus", SqlDbType.VarChar).Value = taskStatus;
                cmd.Parameters.Add("@CustomerName", SqlDbType.VarChar).Value = customerName;
                cmd.Parameters.Add("@SalesPerson", SqlDbType.Int).Value = salesPerson;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ReviewOnly", SqlDbType.Bit).Value = ReviewOnly;
                cmd.Parameters.Add("@TaskReminderDate", SqlDbType.DateTime).Value = TaskReminderDate;
                cn.Open();
                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);
                return ds.Tables[0];
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get (DataListNugget) of to do list task", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<ToDoDetails> GetToDoList()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_ToDoListType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ToDoDetails> lst = new List<ToDoDetails>();
                while (reader.Read())
                {
                    ToDoDetails obj = new ToDoDetails();
                    obj.ToDoListId = GetReaderValue_Int32(reader, "ToDoListTypeId", 0);
                    obj.ToDoTypeName = GetReaderValue_String(reader, "ToDoListTypeName", "");
                    obj.ToDoTypeDescription = GetReaderValue_String(reader, "ToDoListTypeDescription", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ToDoList", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override ToDoDetails GetToDoListType(System.Int32? ToDoListId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_ToDoListType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ToDoListId", SqlDbType.Int).Value = ToDoListId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetPackageFromReader(reader);
                    ToDoDetails obj = new ToDoDetails();
                    obj.ToDoListId = GetReaderValue_Int32(reader, "ToDoListTypeId", 0);
                    obj.ToDoTypeName = GetReaderValue_String(reader, "ToDoListTypeName", "");
                    obj.ToDoTypeDescription = GetReaderValue_String(reader, "ToDoListTypeDescription", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ToDoListType", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override Int32 InsertToDoListType(System.String ToDoTypeName, System.String ToDoTypeDescription, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ToDoListType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ToDoTypeName", SqlDbType.NVarChar).Value = ToDoTypeName;
                cmd.Parameters.Add("@ToDoTypeDescription", SqlDbType.NVarChar).Value = ToDoTypeDescription;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@ToDoListId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@ToDoListId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert ToDoListType", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override bool UpdateToDoListType(System.Int32? ToDoListId, System.String ToDoTypeName, System.String ToDoTypeDescription, System.Boolean? Inactive, System.Int32? UpdatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ToDoListType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ToDoListId", SqlDbType.Int).Value = ToDoListId;
                cmd.Parameters.Add("@ToDoTypeName", SqlDbType.NVarChar).Value = ToDoTypeName;
                cmd.Parameters.Add("@ToDoTypeDescription", SqlDbType.NVarChar).Value = ToDoTypeDescription;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = Inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update UpdateToDoListType", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<ToDoDetails> DropDownToDoListType()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_ToDoListType", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<ToDoDetails> lst = new List<ToDoDetails>();
                while (reader.Read())
                {
                    ToDoDetails obj = new ToDoDetails();
                    obj.ToDoListId = GetReaderValue_Int32(reader, "ToDoListTypeId", 0);
                    obj.ToDoTypeName = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ToDoListType", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}