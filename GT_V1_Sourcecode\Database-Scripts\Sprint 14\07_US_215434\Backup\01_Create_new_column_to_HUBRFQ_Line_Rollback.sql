﻿
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-215434]		Phuc Hoang			06-Nov-2024		Update			Lytica Price should apply fuzzy logic for inserting & displaying
===========================================================================================
*/

IF COL_LENGTH('dbo.tbCustomerRequirement', 'LyticaManufacturerRef') IS NOT NULL 
BEGIN
	ALTER TABLE dbo.tbCustomerRequirement
	DROP COLUMN LyticaManufacturerRef;

END

GO

IF COL_LENGTH('dbo.tbCustomerRequirement', 'LyticaAveragePrice') IS NOT NULL 
BEGIN
	ALTER TABLE dbo.tbCustomerRequirement
	DROP COLUMN LyticaAveragePrice;

END

GO

IF COL_LENGTH('dbo.tbCustomerRequirement', 'LyticaTargetPrice') IS NOT NULL 
BEGIN
	ALTER TABLE dbo.tbCustomerRequirement
	DROP COLUMN LyticaTargetPrice;

END

GO

IF COL_LENGTH('dbo.tbCustomerRequirement', 'LyticaMarketLeading') IS NOT NULL 
BEGIN
	ALTER TABLE dbo.tbCustomerRequirement
	DROP COLUMN LyticaMarketLeading;

END