//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class DivisionKpi : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("DivisionKpi");
            base.ProcessRequest(context);
        }

        protected override void GetData() {

            //get client no
            int? intSelectedClientNo;
            intSelectedClientNo = GetFormValue_NullableInt("intSelClientNo");
            int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");

            string strCacheOptions = CacheManager.SerializeOptions(new object[] { intGlobalLoginClientNo.HasValue ? intGlobalLoginClientNo.Value : SessionManager.ClientID, (intSelectedClientNo.HasValue) ? intSelectedClientNo.Value : 0 });
            string strCachedData =CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData)) {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.Division> lst = BLL.Division.DropDownForKpiClient((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0)? intGlobalLoginClientNo.Value : ((intSelectedClientNo.HasValue && intSelectedClientNo.Value > 0) ? intSelectedClientNo.Value : SessionManager.ClientID), SessionManager.LoginID ?? 0);
                for (int i = 0; i < lst.Count; i++) {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].DivisionId);
                    jsnItem.AddVariable("Name", lst[i].DivisionName);
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                lst.Clear(); lst = null;
                jsn.AddVariable("DivisionKpis", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            } else {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
