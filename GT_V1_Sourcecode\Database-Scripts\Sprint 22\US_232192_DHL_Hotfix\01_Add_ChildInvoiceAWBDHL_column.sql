﻿
/*   
========================================================================================
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-232192]		Phuc Hoang		20-Feb-2025		UPDATE		[PROD Bug] DHL BBX Shipments
======================================================================================== 
*/

GO

IF COL_LENGTH('ChildInvoiceAWBDHL','NumberOfBoxes') IS NULL
BEGIN
	ALTER TABLE [dbo].[ChildInvoiceAWBDHL] ADD [NumberOfBoxes] [int] NULL;
END

IF COL_LENGTH('ChildInvoiceAWBDHL','DimensionalWeight') IS NULL
BEGIN
	ALTER TABLE [dbo].[ChildInvoiceAWBDHL] ADD [DimensionalWeight] FLOAT NULL;
END

IF COL_LENGTH('ChildInvoiceAWBDHL','InitialNote') IS NULL
BEGIN
	ALTER TABLE [dbo].[ChildInvoiceAWBDHL] ADD [InitialNote] NVARCHAR(500) NULL;
END


GO
