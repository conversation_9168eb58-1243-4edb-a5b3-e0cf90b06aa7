﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-210037]  An.TranTan   23-Oct-2024	create    Get Manufacturer advisory notes  
[US-221009]  An.TranTan   29-Nov-2024	update    update logic get manufacturer notes 
[US-214629]  Trung Pham   16-Dec-2024	update    Get number of Purchase Order and star rating belong to supplier
===========================================================================================  
*/ 
CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_ManufacturerLink_for_Supplier]  
@SupplierCompanyNo int
,@ClientID INT = 0
AS  

WITH POLineCountData AS (
	SELECT 
		COUNT(DISTINCT pol.PurchaseOrderLineId) AS POLineCount,
		c.CompanyId,
		pol.ManufacturerNo
	FROM tbCompany c
	JOIN tbManufacturerLink l ON l.SupplierCompanyNo = c.CompanyId
	JOIN tbPurchaseOrder po ON po.CompanyNo = c.CompanyId
	JOIN tbPurchaseOrderLine pol ON pol.PurchaseOrderNo = po.PurchaseOrderId
	WHERE c.CompanyId = @SupplierCompanyNo
	  AND pol.ManufacturerNo = l.ManufacturerNo
	GROUP BY c.CompanyId, pol.ManufacturerNo
)

SELECT a.ManufacturerLinkId  
,  a.ManufacturerNo  
,  b.ManufacturerName  
,  a.SupplierCompanyNo   
,  c.CompanyName  As SupplierName   
,  a.ManufacturerRating  
,  a.SupplierRating  
,  a.UpdatedBy  
,  a.DLUP
, dbo.ufn_get_MfrNotes(b.ManufacturerId,@ClientID) AS AdvisoryNotes
, a.StarRating AS StarRating
, ISNULL(d.POLineCount,0) AS POLineCount
FROM  dbo.tbManufacturerLink a  
JOIN dbo.tbManufacturer b  
 ON a.ManufacturerNo = b.ManufacturerId  
JOIN dbo.tbCompany c  
 ON a.SupplierCompanyNo = c.CompanyId  
LEFT JOIN POLineCountData d ON d.CompanyId = c.CompanyId AND d.ManufacturerNo = a.ManufacturerNo
WHERE a.SupplierCompanyNo = @SupplierCompanyNo  
ORDER BY SupplierName  
  
  
  
  
GO


