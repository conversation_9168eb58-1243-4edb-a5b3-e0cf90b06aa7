<%-- Marker     changed by      date         Remarks  
     [001]      <PERSON><PERSON><PERSON> kumar     22/11/2011  ESMS Ref:21 - Add Country search option in SO
     [002]      <PERSON><PERSON><PERSON>     17-Aug-2018 Provision to add Global Security in Sales Order 
     [003]      <PERSON>    RP-2338  AS6081 Search/Filter functionality on different pages
--%>

<%@ Control Language="C#" CodeBehind="SalesOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <Links>
        <ReboundUI:IconButton ID="ibtnExportCSV" runat="server" Style="margin-left:8px;" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ExportToExcel" />
        <ReboundUI:IconButton ID="ibtnViewTask" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ViewTask" IconCSSType="Add"  IsInitiallyEnabled="true" />
    </Links>
    <Filters>
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
            <FieldsLeft>
                <ReboundUI_FilterDataItemRow:Numerical ID="ctlSONo" runat="server" ResourceTitle="SalesOrderNo" FilterField="SONo" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
                <ReboundUI_FilterDataItemRow:CheckBox ID="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />
                <%-- <ReboundUI_FilterDataItemRow:CheckBox id="ctlUnauthorisedOnly" runat="server" ResourceTitle="SOUnauthorisedOnly" FilterField="UnauthorisedOnly" />--%>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
                <%--[001]Code Start--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlCountry" runat="server" ResourceTitle="Country" DropDownType="Country" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Country" />
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlCheckedStatus" runat="server" ResourceTitle="CheckedStatus" DropDownType="SOCheckedStatus" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="SOCheckedStatus" />
                <%--[001]Code End--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlStatus" runat="server" ResourceTitle="status" DropDownType="SalesOrderStatus" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="SalesOrderStatus" />
            </FieldsLeft>
            <FieldsRight>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCustomerPO" runat="server" ResourceTitle="CustomerPurchaseOrderNo" FilterField="CustPO" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" FilterField="DateOrderedFrom" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" FilterField="DateOrderedTo" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlDatePromisedFrom" runat="server" ResourceTitle="DatePromisedFrom" FilterField="DatePromisedFrom" />
                <ReboundUI_FilterDataItemRow:DateSelect ID="ctlDatePromisedTo" runat="server" ResourceTitle="DatePromisedTo" FilterField="DatePromisedTo" />

                <ReboundUI_FilterDataItemRow:TextBox ID="ctlContractNo" runat="server" ResourceTitle="ContractNo" FilterField="ContractNo" />
                <%--   <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeSentOrder" runat="server" ResourceTitle="OrderSenttoCust" FilterField="IncludeOrderSent" />--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlIncludeSentOrder" runat="server" ResourceTitle="OrderSenttoCust" DropDownType="SOsSentToCust" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="IncludeOrderSent" />
                <%--[002]Code Start--%>
                <ReboundUI_FilterDataItemRow:DropDown ID="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client" />
                <%--[002]Code End--%>
                <%--<ReboundUI_FilterDataItemRow:DropDown id="ctlStatus" runat="server" ResourceTitle="status" DropDownType="SOStatus" DropDownAssembly="Rebound.GlobalTrader.Site"  FilterField="SOStatus"/>--%>
                <ReboundUI_FilterDataItemRow:DropDown id="ctlAS6081" runat="server" ResourceTitle="AS6081Filternew" DropDownType="CounterfeitElectronicParts" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="AS6081" /> <%--[003]--%>
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>
    </Filters>
    <Forms>
		<ReboundForm:MailMessages_MarkAsToDo id="ctlAdd" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
<div style="padding-left: 560px;">
    <table>
        <tr>
            <td>
                <canvas id="myCanvasgree" width="20" height="20" style="border: 1px solid #000000; font-weight: bold; background-color: green; width:12px;"></canvas>
            </td>
            <td><span style="font-size:10px;">Promise date more than 1 day ahead</span></td>
            <td>
                <canvas id="myCanvasamber" width="20" height="20" style="border: 1px solid #000000;font-weight: bold; background-color: #FFBF00; width:12px;"></canvas>
            </td>
            <td><span style="font-size:10px;">Promise Date just 1 day ahead </span></td>
            <td>
                <canvas id="myCanvasred" width="20" height="20" style="border: 1px solid #000000;font-weight: bold; background-color: red; width:12px;"></canvas>
            </td>
            <td><span style="font-size:10px;">Promise date same day or passed</span></td>
        </tr>
    </table>
</div>


