<%@ Control Language="C#" CodeBehind="CusReqSourcingResults.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CustomerRequirementSourcingResults" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Main Information" BoxType="Standard">
	<Links>
		<ReboundUI:MultiSelectionCount id="ctlMultiSelectionCount" runat="server" />
		<ReboundUI:IconButton ID="ibtnAdd" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Add" IconCSSType="Add" />
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IsInitiallyEnabled="false" />
		<ReboundUI:IconButton ID="ibtnQuote" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Quote" IsInitiallyEnabled="false" />
        <ReboundUI:IconButton ID="ibtnDelete" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="DeletePartWatchMatch" IconCSSType="Delete"  />
	</Links>
	<Content>
		<ReboundUI:FlexiDataTable ID="tbl" runat="server" AllowMultipleSelection="true" PanelHeight="180" />
	</Content>
	<Forms>
		<ReboundForm:CustomerRequirementSourcingResults_Add ID="ctlAdd" runat="server" />
		<ReboundForm:CustomerRequirementSourcingResults_Edit ID="ctlEdit" runat="server" />
        <ReboundForm:CustomerRequirementSourcingResults_Delete ID="ctlDelete" runat="server" />
	</Forms>
</ReboundUI_Nugget:DesignBase>
