///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.initializeBase(this, [element]);
	this._aryComponents = [];
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.prototype = {

	get_tbl: function() { return this._tbl; }, 	set_tbl: function(v) { if (this._tbl !== v)  this._tbl = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this._aryComponents = null;
		this._tbl = null;
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.callBaseMethod(this, "dispose");
	},
	
	addRow: function(intIndex, varID, aryData) {
		var tr = this._tbl.insertRow(-1);
		tr.id = this.getControlID("tr", intIndex);
		for (var i = 0, l = aryData.length; i < l; i++) {
			var td = document.createElement("td");
			var strID = this.getControlID("txt", intIndex, i);
			td.innerHTML = String.format("<input id=\"{0}\" type=\"text\" style=\"width: 100%;\" value=\"{1}\" />", strID, aryData[i]);
			tr.appendChild(td);
			td = null;
		}
		tr = null; td = null;
	},
	
	getControlID: function(str, intIndex, i) {
		return String.format("{0}_{1}{2}_{3}", this._element.id, str, intIndex, i);
	},
	
	clearRows: function() {
		for (var i = this._tbl.rows.length - 1; i >= 1; i--) { this._tbl.deleteRow(i); }
	}

};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.TabularData", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);