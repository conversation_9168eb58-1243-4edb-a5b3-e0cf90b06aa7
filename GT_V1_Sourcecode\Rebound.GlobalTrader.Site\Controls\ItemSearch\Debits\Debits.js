Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.initializeBase(this,[n]);this._intGlobalClientNo=-1};Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||(this._intGlobalClientNo=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/Debits");this._objData.set_DataObject("Debits");this._objData.set_DataAction("GetData");this._objData.addParameter("Contact",this.getFieldValue("ctlContact"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("Salesman",this.getFieldValue("ctlSalesman"));this._objData.addParameter("DebitNoLo",this.getFieldValue_Min("ctlDebitNo"));this._objData.addParameter("DebitNoHi",this.getFieldValue_Max("ctlDebitNo"));this._objData.addParameter("SRMANoLo",this.getFieldValue_Min("ctlSupplierRMANo"));this._objData.addParameter("SRMANoHi",this.getFieldValue_Max("ctlSupplierRMANo"));this._objData.addParameter("PONoLo",this.getFieldValue_Min("ctlPurchaseOrderNo"));this._objData.addParameter("PONoHi",this.getFieldValue_Max("ctlPurchaseOrderNo"));this._objData.addParameter("DebitDateFrom",this.getFieldValue("ctlDebitDateFrom"));this._objData.addParameter("DebitDateTo",this.getFieldValue("ctlDebitDateTo"));this._objData.addParameter("GlobalClientNo",this._intGlobalClientNo)},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.setCleanTextValue(n.Contact),$R_FN.setCleanTextValue(n.Date),$R_FN.setCleanTextValue(n.Salesman),n.PO,n.SRMA],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);