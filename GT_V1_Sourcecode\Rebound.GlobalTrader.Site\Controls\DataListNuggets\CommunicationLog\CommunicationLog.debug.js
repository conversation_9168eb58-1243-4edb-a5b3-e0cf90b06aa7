///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 10.11.2009:
// - New control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.initializeBase(this, [element]);
	this._blnNoData = false;
	this._inactive = false;
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.prototype = {

	get_blnForContact: function() { return this._blnForContact; }, 	set_blnForContact: function(value) { if (this._blnForContact !== value)  this._blnForContact = value; }, 
	get_enmCompanyListType: function() { return this._enmCompanyListType; }, 	set_enmCompanyListType: function(value) { if (this._enmCompanyListType !== value)  this._enmCompanyListType = value; }, 
	get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(value) { if (this._intCompanyID !== value) this._intCompanyID = value; },
	get_intContactID: function() { return this._intContactID; }, set_intContactID: function(value) { if (this._intContactID !== value) this._intContactID = value; },
	get_ibtnEdit: function() { return this._ibtnEdit; }, 	set_ibtnEdit: function(value) { if (this._ibtnEdit !== value)  this._ibtnEdit = value; }, 
	get_ibtnAdd: function() { return this._ibtnAdd; }, 	set_ibtnAdd: function(value) { if (this._ibtnAdd !== value)  this._ibtnAdd = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.callBaseMethod(this, "initialize");
		this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
		this.addBaseControlsInitialized(Function.createDelegate(this, this.baseControlsInitialized));
		this._strPathToData = "controls/DataListNuggets/CommunicationLog";
		this._strDataObject = "CommunicationLog";
		this._table.addSelectedIndexChanged(Function.createDelegate(this, this.selectedItem));
		if (this._ibtnAdd || this._ibtnEdit) {
			if (this._ibtnAdd) $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
			if (this._ibtnEdit) $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
			this._frmAddEdit = $find(this._aryFormIDs[0]);
			this._frmAddEdit.addCancel(Function.createDelegate(this, this.hideAddEditForm));
			this._frmAddEdit.addSaveComplete(Function.createDelegate(this, this.saveAddEditComplete));
			this._frmAddEdit.addSaveError(Function.createDelegate(this, this.saveAddEditError));
		}
	},
	
	baseControlsInitialized: function() {
		this.getFilterField("ctlContact")._ddl._intCompanyID = this._intCompanyID;
		if (this._blnForContact) this.getFilterField("ctlContact").setValue(this._intCompanyID);		
		this.showFilterField("ctlContact", !this._blnForContact);
	},
	
	initAfterBaseIsReady: function() {
		this.getData();
		this.getCompanyInactive();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._frmAddEdit) this._frmAddEdit.dispose();
		if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
		if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
		this._blnForContact = null;
		this._enmCompanyListType = null;
		this._intCompanyID = null;
		this._intContactID = null;
		this._ibtnEdit = null;
		this._ibtnAdd = null;
		this._blnNoData = null;
		this._inactive = null;
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.callBaseMethod(this, "dispose");
	},

	setupDataCall: function() {
		this._objData.addParameter("ForContact", this._blnForContact);
		this._objData.addParameter("CompanyNo", this._intCompanyID);
		if (this._blnForContact) this._objData.addParameter("ContactNo", this._intContactID);
		this._objData.addParameter("CallType", this.getFilterFieldDropDownExtraText("ctlType"));
		this.updateButtonsEnabledState(false);
	},

	getDataOK: function() {
		this._blnNoData = true;	
		for (var i = 0, l = this._objResult.Items.length; i < l; i++) {
			var row = this._objResult.Items[i];
			var aryData = [
				$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Date), $R_FN.setCleanTextValue(row.EnteredBy))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Type), row.KeyNo ? $RGT_nubButton_SystemDocument(row.SysDocNo, row.KeyNo, row.SysDocNumber) : row.SysDocNumber)
				, $RGT_nubButton_Contact(row.ContactID, row.Contact, this._enmCompanyListType)
				, $R_FN.setCleanTextValue(row.Details) 
				];
			var objExtraData = {ContactID:row.ContactID, TypeID:row.TypeID, Frozen:row.Frozen};
			this._table.addRow(aryData, row.ID, false, objExtraData);
			aryData = null; row = null;
			this._blnNoData = true;			
		}
		this.showNoData(this._blnNoData);
	},
	getCompanyInactive: function () {
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetCompanyDetailInactive");
		obj.addParameter("id", this._intCompanyID);
		obj.addDataOK(Function.createDelegate(this, this.getCompanyInactiveOK));
		obj.addError(Function.createDelegate(this, this.getCompanyInactiveError));
		obj.addTimeout(Function.createDelegate(this, this.getCompanyInactiveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	getCompanyInactiveOK: function (args) {
		var result = args._result;
		this._inactive = result.Inactive;
		if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !result.Inactive);
		if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !result.Inactive);
	},

	getCompanyInactiveError: function (args) {
		this.showError(true, args.get_ErrorMessage());
	},
		
	updateButtonsEnabledState: function(blnEnable) {
		if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive && blnEnable && !this._table.getSelectedExtraData().Frozen);
	},

	selectedItem: function() {
		this._frmAddEdit._intLogItemID = this._table._varSelectedValue;
		this.updateButtonsEnabledState(true);
	},
	
	showAddForm: function() {
		this._frmAddEdit.changeMode("ADD");
		this._frmAddEdit._intCompanyID = this._intCompanyID;
		if (this._blnForContact) this._frmAddEdit._intContactID = this._intContactID;
		this._frmAddEdit._intDataListNuggetID = this._intDataListNuggetID;
		this.showForm(this._frmAddEdit, true);
	},

	showEditForm: function() {
		this._frmAddEdit.changeMode("EDIT");
		if (this._blnForContact) this._frmAddEdit._intContactID = this._intContactID;
		this._frmAddEdit._intCompanyID = this._intCompanyID;
		this._frmAddEdit._intDataListNuggetID = this._intDataListNuggetID;
		this.showForm(this._frmAddEdit, true);
		this._frmAddEdit.setFieldValue("ctlContact", this._table.getSelectedExtraData().ContactID);
		this._frmAddEdit.setFieldValue("ctlLogType", this._table.getSelectedExtraData().TypeID);
		this._frmAddEdit.setFieldValue("ctlNotes", this._table.getSelectedCellValue(3));
	},

	hideAddEditForm: function() {
	    this.showForm(this._frmAddEdit, false);
	    //comment by anand / 10. Contact Log Issue
	    //this.showNoData(this._blnNoData);
        //end
	},

	saveAddEditComplete: function() {
		this.hideAddEditForm();
		this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
		this.onSaveEditComplete();
		this.getData();
	},
	
	saveAddEditError: function() {
		this.hideAddEditForm();
		this.showError(true, this._frmAddEdit._strErrorMessage);
	}
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
