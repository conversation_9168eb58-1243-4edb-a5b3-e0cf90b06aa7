﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208862]		Phuc Hoang			16-Jul-2024		Create			Manual Trigger: Refreshing Lytica API
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_LyticaAPIData_By_PartMfr]

    @partNo VARCHAR(256),
	@manufacturerCode VARCHAR(256),
    @manufacturerName VARCHAR(1024)

AS

BEGIN
    SELECT TOP 1 * FROM tbLyticaAPI l
	INNER JOIN tbManufacturer m ON l.Manufacturer = m.ManufacturerName
	WHERE l.OriginalPartSearched = @partNo 
	AND (m.ManufacturerCode = @manufacturerCode OR l.Manufacturer = @manufacturerName)
	ORDER BY l.DLUP DESC
END

GO
