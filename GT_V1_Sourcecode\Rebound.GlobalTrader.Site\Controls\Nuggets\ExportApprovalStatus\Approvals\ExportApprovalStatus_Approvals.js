Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals.initializeBase(this,[n]);this._intLineID=-1;this._strQualityApprove=null;this._strTitle_ExportApprove=null;this._lblExplainQualityApprove=null;this._lblExplainQualityDecline=null;this._lblExplainExportApprove=null;this._lblExplainLineManagerDecline=null;this._lblExplainLineManagerIndpdt=null;this._lblExplainQualityEscalate=null;this._aryUnpostedLineIDs=[];this._aryPostedLineIDs=[];this._intExportApprovalID=0;this._ctlConfirm=null};Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals.prototype={get_intExportApprovalID:function(){return this._intExportApprovalID},set_intExportApprovalID:function(n){this._intExportApprovalID!==n&&(this._intExportApprovalID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},get_strTitle_ExportApprove:function(){return this._strTitle_ExportApprove},set_strTitle_ExportApprove:function(n){this._strTitle_ExportApprove!==n&&(this._strTitle_ExportApprove=n)},get_lblExplainExportApprove:function(){return this._lblExplainExportApprove},set_lblExplainExportApprove:function(n){this._lblExplainExportApprove!==n&&(this._lblExplainExportApprove=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&(this.getFieldDropDownData("ctlOGELNumber"),this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.approveClicked)),this._ctlConfirm.addClickRejectEvent(Function.createDelegate(this,this.rejectClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)),document.getElementById("ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmApprovals_ctlDB_ctlEUUForm_ctl03_ibtnGeneratePDF_hyp").style.color="white",$addHandler($get("ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmApprovals_ctlDB_ctlEUUForm_ctl03_ibtnGeneratePDF_hyp"),"click",this.createPDF));SetExportApprovalId(this._intLineID);this.getExportApprovalData();(this._mode=="QUALITY_APPROVE"||this._mode=="QUALITY_DECLINE"||this._mode=="QUALITY_ESCALATE"||this._mode=="LINEMANAGER_INDEPDNTTEST")&&(this._IsEscalate?(this.showField("ctlLineManagerNote",!1),this.showField("ctlRequesterNote",!1),this.showField("ctlQualityNote",!0)):(this.showField("ctlLineManagerNote",!0),this.showField("ctlQualityNote",!1)));this.checkMode()},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intLineID=null,this._strTitle_ExportApprove=null,this._lblExplainExportApprove=null,this._aryUnpostedLineIDs=null,this._aryPostedLineIDs=null,this._intExportApprovalID=null,Rebound.GlobalTrader.Site.Controls.Forms.POLines_Post.callBaseMethod(this,"dispose"))},approveClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ExportApprovalStatus");n.set_DataObject("ExportApprovalStatus");n.set_DataAction("ApproveExportRequest");n.addParameter("id",this._intLineID);n.addParameter("OGELNumber",this.getFieldValue("ctlOGELNumber"));n.addParameter("MilitaryUse",0);n.addParameter("EndUser",this.getFieldValue("ctlEndUser"));n.addParameter("Comment",this.getFieldValue("ctlComment"));n.addParameter("SendEmail",this.getFieldValue("ctlSendMail"));n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},rejectClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ExportApprovalStatus");n.set_DataObject("ExportApprovalStatus");n.set_DataAction("RejectExportRequest");n.addParameter("id",this._intLineID);n.addParameter("OGELNumber",this.getFieldValue("ctlOGELNumber"));n.addParameter("MilitaryUse",0);n.addParameter("EndUser",this.getFieldValue("ctlEndUser"));n.addParameter("Comment",this.getFieldValue("ctlComment"));n.addParameter("SendEmail",this.getFieldValue("ctlSendMail"));n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed();this.setFieldValue("ctlOGELNumber","");this.setFieldValue("ctlEndUser","");this.setFieldValue("ctlComment","")},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result==!0?(this.onSaveComplete(),this.setFieldValue("ctlOGELNumber",""),this.setFieldValue("ctlEndUser",""),this.setFieldValue("ctlComment",""),setTimeout(function(){$("#ctl00_cphMain_ctlAuthorisation_ctlDB_imgRefresh").trigger("click")},1500)):(this._strErrorMessage=n._errorMessage,this.onSaveError())},checkMode:function(){switch(this._mode){case"LINEMANAGER_APPROVE":this.changeTitle(this._strTitle_ExportApprove)}$R_FN.showElement(this._lblExplainExportApprove,this._mode=="LINEMANAGER_APPROVE")},setFieldsFromHeader:function(n,t){this.setFieldValue("ctlPurchaseOrder",n);this.setFieldValue("ctlSalesPerson",t)},getExportApprovalData:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ExportApprovalStatus");n.set_DataObject("ExportApprovalStatus");n.set_DataAction("GetExportApprovalData");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getExportApprovalDataOK));n.addError(Function.createDelegate(this,this.getExportApprovalDataError));n.addTimeout(Function.createDelegate(this,this.getExportApprovalDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getExportApprovalDataError:function(){this.showInnerContent(!0);this.showLoading(!1)},getExportApprovalDataOK:function(n){var t=n._result;this.setFieldValue("ctlSalesPerson",t.SalesmanName);this.setFieldValue("ctlSalesOrder",t.SalesOrderNo);this.setFieldValue("ctlSOLineNo",t.SOSerialNo);this.setFieldValue("ctlCustomer",t.CustomerName);this.setFieldValue("ctlPartNumber",t.Part);this.setFieldValue("ctlDestinationCountry",t.DestinationCountry);this.setFieldValue("ctlMilitaryuseName",t.MilitaryUseName);this.setFieldValue("ctlEndUser",t.EndUser);t.ISPdfAttached==!0?(this.showField("ctlEUUForm",!0),this.showField("ctlEUUFormLbl",!1)):(this.showField("ctlEUUForm",!1),this.showField("ctlEUUFormLbl",!0));this.showInnerContent(!0);this.showLoading(!1)},createPDF:function(){var n=GetExportApprovalId(),t=window.location.href,i=new URL(t);$R_FN.openPrintWindow($R_ENUM$PrintObject.EUUForm,n)}};Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);