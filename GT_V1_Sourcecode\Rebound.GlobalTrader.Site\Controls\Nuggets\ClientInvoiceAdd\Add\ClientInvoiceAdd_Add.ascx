<%--
Marker     Changed by      Date               Remarks
[001]      Vinay           06/06/2013         CR:- Supplier Invoice
--%>
<%@ Control Language="C#" CodeBehind="ClientInvoiceAdd_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceAdd_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation>
	
		<ReboundUI:MultiStep ID="ctlMultiStep" runat="server">
		
			<Items>
				<ReboundUI:MultiStepItem ID="ctlItem1" runat="server" ResourceTitle="SupplierInvoiceAdd_SelectCompany" IsSelected="true" />
				<ReboundUI:MultiStepItem ID="ctlItem2" runat="server" ResourceTitle="SupplierInvoiceAdd_EnterDetail" />
				<ReboundUI:MultiStepItem ID="ctlItem3" runat="server" ResourceTitle="SupplierInvoiceAdd_Notify" />
			</Items>
			
		</ReboundUI:MultiStep>
		
	</Explanation>

	<Content>
	
		<!-- Step 1 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep1" runat="server">
		
			<asp:TableRow id="trSelectCompany" runat="server">
				<asp:TableCell id="tdSelectCompany" runat="server"><ReboundItemSearch:Supplier id="ctlSelectCompany" runat="server" ForPOs="true" /></asp:TableCell>
			</asp:TableRow>
			
		</ReboundUI_Table:Form>

		<!-- Step 2 ------------------------------------------------------------------->
		
		<ReboundUI_Table:Form id="frmStep2" runat="server">
		
			<ReboundUI_Form:FormField ID="ctlClient" runat="server" FieldID="ddlClient" ResourceTitle="Client"  IsRequiredField="true">                          
             <Field> <ReboundDropDown:Client ID="ddlClient" runat="server"  /> </Field>
             </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="lblSupplier"  ResourceTitle="Supplier">
				<Field><asp:Label ID="lblSupplier" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		<%--	
			<ReboundUI_Form:FormField id="ctlSupplierCode" runat="server" FieldID="lblSupplierCode"  ResourceTitle="SupplierCode">
				<Field><asp:Label ID="lblSupplierCode" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
		
			
			<ReboundUI_Form:FormField id="ctlSupplierInvoice" runat="server" FieldID="txtSupplierInvoice"  ResourceTitle="ClientInvoice">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierInvoice" runat="server" TextBoxMode="Normal" MaxLength="100"  Width="150" /> </Field>
			</ReboundUI_Form:FormField>
			
		     <ReboundUI_Form:FormField id="ctlInvoiceDate" runat="server" FieldID="txtInvoiceDate" ResourceTitle="InvoiceDate" IsRequiredField="true">
	            <Field>
		            <ReboundUI:ReboundTextBox ID="txtInvoiceDate" runat="server" Width="150" />
		            <ReboundUI:Calendar ID="calInvoiceDate" runat="server" RelatedTextBoxID="txtInvoiceDate" />
	            </Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency"  IsRequiredField="true" >
				<Field><ReboundDropDown:Currency ID="ddlCurrency" runat="server" IncludeSelected="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlInvoiceAmount" runat="server" FieldID="txtInvoiceAmount" IsRequiredField="true" ResourceTitle="InvoiceAmount">
				<Field><ReboundUI:ReboundTextBox ID="txtInvoiceAmount" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="5" Width="100" />  <asp:Label id="lblCurrency_InvoiceAmount" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlGoodsValue" runat="server" FieldID="txtGoodsValue" IsRequiredField="true" ResourceTitle="GoodsValue">
				<Field><ReboundUI:ReboundTextBox ID="txtGoodsValue" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="5" Width="100" />  <asp:Label id="lblCurrency_GoodsInValue" runat="server" />&nbsp;&nbsp;&nbsp;&nbsp;<asp:Label ID="lbl_SelectedGI" runat="server"><%=Functions.GetGlobalResource("FormFields", "SelectedGI")%></asp:Label>&nbsp;<ReboundUI:ReboundTextBox ID="txtSelectedGI" runat="server" TextBoxMode="currency" BackColor="Yellow"   DecimalPlaces="5"  Width="150" ReadOnly="true"/></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlTax" runat="server" FieldID="txtTax" ResourceTitle="Tax" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtTax" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="5" Width="100" />  <asp:Label id="lblCurrency_Tax" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			  <ReboundUI_Form:FormField id="ctlddlTax" runat="server" FieldID="ddlTax" ResourceTitle="Tax"  IsRequiredField="true">
				<Field><ReboundDropDown:PurchaseTaxCode ID="ddlTax" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlDeliveryCharge" runat="server" FieldID="txtDeliveryCharge" ResourceTitle="DeliveryCharge">
				<Field><ReboundUI:ReboundTextBox ID="txtDeliveryCharge" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="5" Width="100" />  <asp:Label id="lblCurrency_DeliveryCharge" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlBankFee" runat="server" FieldID="txtBankFee" ResourceTitle="BankFee">
				<Field><ReboundUI:ReboundTextBox ID="txtBankFee" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="5" Width="100" />  <asp:Label id="lblCurrency_BankFee" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlCreditCardFee" runat="server" FieldID="txtCreditCardFee" ResourceTitle="CreditCardFee">
				<Field><ReboundUI:ReboundTextBox ID="txtCreditCardFee" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" DecimalPlaces="5" Width="100" />  <asp:Label id="lblCurrency_CreditCardFee" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<%--<ReboundUI_Form:FormField id="ctlCanExported" runat="server" FieldID="chkCanExported" ResourceTitle="CanBeExported">
				<Field><ReboundUI:ImageCheckBox ID="chkCanExported" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>--%>
			
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>
				
			
			
			<ReboundUI_Form:FormField id="ctlSecondRef" runat="server" FieldID="txtSecondRef" ResourceTitle="SecondRef">
				<Field><ReboundUI:ReboundTextBox ID="txtSecondRef" runat="server"  MaxLength="16"  Width="150" /> <asp:Label ID="lbl_SecondRef" runat="server"><%=Functions.GetGlobalResource("Misc", "SecondRef")%></asp:Label> </Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlNarrative" runat="server" FieldID="txtNarrative" ResourceTitle="Narrative">
				<Field><ReboundUI:ReboundTextBox ID="txtNarrative" runat="server"  MaxLength="41"  Width="150" /> <asp:Label ID="lbl_Narrative" runat="server"><%=Functions.GetGlobalResource("Misc", "Narrative")%></asp:Label></Field>
			</ReboundUI_Form:FormField>
			
			
		 <asp:TableRow >
			<asp:TableCell id="TableCell2"  ColumnSpan="2" ></asp:TableCell>
		</asp:TableRow> 
		
		 <asp:TableRow   style="border-top:1px dotted #CCCCCC;margin-top:10px;">
			<asp:TableCell id="TableCell1"  ColumnSpan="2" runat="server"><ReboundItemSearch:SIGILines id="ctlSelectSIGILines" runat="server" ShowIncludeInvoice="false" /></asp:TableCell>
		</asp:TableRow> 
		</ReboundUI_Table:Form>
		 
	   
	  
		<!-- Step 3 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep3" runat="server">
	
			<ReboundUI_Form:FormField id="ctlSendMail" runat="server" FieldID="chkSendMail" ResourceTitle="ShouldMailBeSent">
				<Field><ReboundUI:ImageCheckBox ID="chkSendMail" runat="server" Enabled="true" Checked="true" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundFormFieldCollection:SendMailMessage id="ctlSendMailMessage" runat="server" />
			
		</ReboundUI_Table:Form>
	

	</Content>
	
</ReboundUI_Form:DesignBase>
