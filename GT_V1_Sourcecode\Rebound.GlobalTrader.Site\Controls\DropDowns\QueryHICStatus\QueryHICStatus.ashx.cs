//--------------------------------------------------------------------------------------------------------
// Action: Created          By:<PERSON><PERSON><PERSON><PERSON>       Dated:09-02-2022    Comment: Add new dropdown for query HIC Staus.
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class QueryHICStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("QueryHICStatus");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.GoodsInLine> lst = BLL.GoodsInLine.GetQueryHICStatus();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].QueryHICId);
                jsnItem.AddVariable("Name", lst[i].QueryHICStatus);
                jsnList.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            jsn.AddVariable("Types", jsnList);
            jsnList.Dispose(); jsnList = null;
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
    }
}
