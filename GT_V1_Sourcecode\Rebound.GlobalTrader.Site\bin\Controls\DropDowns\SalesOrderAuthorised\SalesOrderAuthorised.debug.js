///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised = function(element) {
	Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.initializeBase(this, [element]);
	this._intInvoiceID = null;
};

Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.prototype = {

	get_intInvoiceID: function () { return this._intInvoiceID; }, set_intInvoiceID: function (value) { if (this._intInvoiceID !== value) this._intInvoiceID = value; }, 
	
	initialize: function() 	{
		Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._intInvoiceID = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/SalesOrderAuthorised");
		this._objData.set_DataObject("SalesOrderAuthorised");
		this._objData.set_DataAction("GetData");

		var baseUrl = (window.location).href;
		var url = new URL(baseUrl);
		var invoiceID = url.searchParams.get("inv");

		this._objData.addParameter("InvoiceId", invoiceID);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Items) {
			for (var i = 0; i < result.Items.length; i++) {
				this.addOption(result.Items[i].Name, result.Items[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
