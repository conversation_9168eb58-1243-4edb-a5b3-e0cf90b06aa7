﻿CREATE OR ALTER PROCEDURE [dbo].[usp_insert_InvoiceEmail]               
 @InvoiceNos xml ,                
 @UpdatedBy int ,    
 @AttachCOC bit=0,  
 @AttachPackagingSlip bit=0
AS                
-- =============================================                
-- Author:  <<PERSON><PERSON>>                
-- Create date: <13 july 2012>                
-- Description: <Rebound- Invoice bulk Emailer>
-- =============================================                
BEGIN                
      SET ANSI_PADDING ON            
      SET ANSI_WARNINGS ON            
      SELECT x.InvoiceID.query('ID').value('.', 'int') as InvoiceID INTO #TEMP_Invoice FROM @InvoiceNos.nodes('Invoices/Invoice') as x(InvoiceID)              
      SET ANSI_PADDING OFF            
      SET ANSI_WARNINGS OFF            
   --Insert into tbInvoiceEmail table            
      INSERT INTO tbInvoiceEmail(              
  ClientNo,              
  InvoiceNo,              
  ContactNo,              
  ContactEmail,              
  SentStatus,              
  UpdatedBy,              
  EmailStatus,              
  DLUP ,    
  AttachCOC ,  
  AttachPackagingSlip
      )                
      SELECT Inv.ClientNo,              
      Inv.InvoiceId,              
      Con.ContactId,              
      Con.EMail,0,              
      @UpdatedBy,'Pending',GETDATE(),@AttachCOC  ,   @AttachPackagingSlip       
      FROM tbContact Con LEFT OUTER JOIN tbInvoice Inv  ON Con.CompanyNo=Inv.CompanyNo         
      join #TEMP_Invoice tmp on inv.InvoiceId=tmp.InvoiceID where Con.FinanceContact=1 and con.Inactive=0                
           
   -- Popolate           
      --SELECT  distinct             
      --Inv.InvoiceNumber          
      --FROM tbContact Con LEFT OUTER JOIN tbInvoice Inv                
      --ON Con.CompanyNo=Inv.CompanyNo join               
      --#TEMP_Invoice tmp on inv.InvoiceId=tmp.InvoiceID where Con.FinanceContact=0          
Select * from (          
               Select           
                    inv.InvoiceNumber           
                   ,IsNull((Select COUNT(*) from tbContact           
                    where IsNull(FinanceContact,0)=1 and Inactive=0 and CompanyNo= inv.CompanyNo           
                    Group By CompanyNo),0) As 'CountFinanceContact'          
               from tbInvoice inv          
               Inner Join #TEMP_Invoice tmp on inv.InvoiceId=tmp.InvoiceID           
             ) as table1          
where CountFinanceContact=0          
          
        -- Drop the temp table            
      DROP TABLE #TEMP_Invoice            
END             
    
  
  
  