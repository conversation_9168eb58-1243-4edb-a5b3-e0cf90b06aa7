IF OBJECT_ID('usp_insert_BOMExpediteNote', 'P') IS NOT NULL
	DROP PROC usp_insert_BOMExpediteNote
GO
/*
-- ==========================================================================================
-- TASK      		UPDATED BY     		DATE         ACTION 			DESCRIPTION                                    
-- US-201231 		Phuc.HoangDinh     	24-04-2024   CREATE				Create for ticket US-201231 [RP-2609] BOM Manager Communication Notes Per Line
-- ==========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_insert_BOMExpediteNote]            
    @BOMId INT            
  , @ExpediteNotes   nvarchar(MAX) = Null         
  , @UpdatedBy INT     
  , @ReqIds   nvarchar(200) = Null     
  , @GroupId nvarchar(100)=Null    
  , @EmailSendTo INT = 0    
  , @CCUserID nvarchar(MAX) = Null  
  , @SendToGroup nvarchar(10) = Null  
  , @NewId INT = NULL OUTPUT            
AS             
BEGIN         
 Declare @ID int , @Group int= 0 , @RequestedToPOHUB int    
    
 --select top 1 @RequestedToPOHUB=UpdateByPH from tbBOM where BOMId= @BOMId     
    
 DECLARE db_cursorExpediteNote CURSOR FOR                                        
    SELECT * FROM dbo.Ufn_split_salesorderline(@ReqIds)                                        
                                      
   OPEN db_cursorExpediteNote                                        
                                      
   FETCH next FROM db_cursorExpediteNote INTO @ID                
                                  
   WHILE @@FETCH_STATUS = 0                                        
   BEGIN                     
       
   INSERT  INTO dbo.tbBOMExpediteNotes (            
           BOMNo        
          ,ExpediteNotes                  
          ,UpdatedBy        
          ,DLUP      
          ,CustomerReqNo      
          ,MailSendTo  
		  ,CCUserID  
		  ,SendToGroup  
          )            
    VALUES  (            
              @BOMId            
            , @ExpediteNotes           
            , @UpdatedBy            
            , CURRENT_TIMESTAMP    
            ,(select CustomerRequirementNumber from tbCustomerRequirement  where CustomerRequirementId=@ID)    
            ,@EmailSendTo     
			,@CCUserID  
			,@SendToGroup  
            )     
   if(@Group=0)    
   begin    
    set @Group=  SCOPE_IDENTITY()    
    end    
     update tbBOMExpediteNotes set GroupID=@Group  where [BOMExpediteNotesId]=SCOPE_IDENTITY()    
     update tbCustomerRequirement set ExpediteDate= CURRENT_TIMESTAMP where CustomerRequirementId=@ID    
    FETCH next FROM db_cursorExpediteNote INTO @ID       
   END     
  
   CLOSE db_cursorExpediteNote             
   DEALLOCATE db_cursorExpediteNote       
         
   SET @NewId = SCOPE_IDENTITY()          
   SELECT  @NewId     
             
END      


GO