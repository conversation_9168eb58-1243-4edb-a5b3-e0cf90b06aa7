using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CompanyAddresses_Confirm : Base {

		#region Locals

		private string _strTitle_Cease;
		private string _strTitle_DefaultBill;
		private string _strTitle_DefaultShip;
		private string _strExplanation_Cease;
		private string _strExplanation_DefaultBill;
		private string _strExplanation_DefaultShip;

		#endregion

		#region Properties

		private int _intAddressID;
		public int AddressID {
			get { return _intAddressID; }
			set { _intAddressID = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			_strTitle_Cease = Functions.GetGlobalResource("FormTitles", "CompanyAddresses_Cease");
			_strTitle_DefaultBill = Functions.GetGlobalResource("FormTitles", "CompanyAddresses_DefaultBill");
			_strTitle_DefaultShip = Functions.GetGlobalResource("FormTitles", "CompanyAddresses_DefaultShip");
			_strExplanation_Cease = Functions.GetGlobalResource("FormExplanations", "CompanyAddresses_Cease");
			_strExplanation_DefaultBill = Functions.GetGlobalResource("FormExplanations", "CompanyAddresses_DefaultBill");
			_strExplanation_DefaultShip = Functions.GetGlobalResource("FormExplanations", "CompanyAddresses_DefaultShip");
			AddScriptReference("Controls.Nuggets.CompanyAddresses.Confirm.CompanyAddresses_Confirm");
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanyAddresses_Confirm", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intAddressID", _intAddressID);
			_scScriptControlDescriptor.AddProperty("strTitle_Cease", _strTitle_Cease);
			_scScriptControlDescriptor.AddProperty("strTitle_DefaultBill", _strTitle_DefaultBill);
			_scScriptControlDescriptor.AddProperty("strTitle_DefaultShip", _strTitle_DefaultShip);
			_scScriptControlDescriptor.AddProperty("strExplanation_Cease", _strExplanation_Cease);
			_scScriptControlDescriptor.AddProperty("strExplanation_DefaultBill", _strExplanation_DefaultBill);
			_scScriptControlDescriptor.AddProperty("strExplanation_DefaultShip", _strExplanation_DefaultShip);
		}


	}
}