﻿//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Web;

//namespace Rebound.GlobalTrader.Site.Controls.DropDowns.MailGroups

//--------------------------------------------------------------------------------------------------------
// Action: Created  By: Devendra      Dated: 10-01-2024       Comment: For RP-2727.
    //--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class MailGroups : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("MailGroups");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
           
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.MailGroup> lst = BLL.MailGroup.GetListForClient(SessionManager.ClientID);
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].MailGroupId);
                jsnItem.AddVariable("Name", lst[i].Name);
                jsnList.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            jsn.AddVariable("Types", jsnList);
            jsnList.Dispose(); jsnList = null;
           
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
          
        }
    }
}
