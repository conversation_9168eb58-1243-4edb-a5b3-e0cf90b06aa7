﻿IF COL_LENGTH('dbo.tbSalesOrderStatus', 'Description') IS NULL
BEGIN
   ALTER TABLE dbo.tbSalesOrderStatus ADD [Description] NVARCHAR(100) NULL
END
GO
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'New' WHERE SalesOrderStatusId = 1
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Unposted' WHERE SalesOrderStatusId = 2
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Part Posted' WHERE SalesOrderStatusId = 3
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Posted' WHERE SalesOrderStatusId = 4
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Part Allocated' WHERE SalesOrderStatusId = 5
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Allocated' WHERE SalesOrderStatusId = 6
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Checked (Part Allocated)' WHERE SalesOrderStatusId = 7
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Checked' WHERE SalesOrderStatusId = 8
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Part Shipped' WHERE SalesOrderStatusId = 9
UPDATE dbo.tbSalesOrderStatus SET [Description] = 'Complete' WHERE SalesOrderStatusId = 10
GO

UPDATE tbToDoCategory set Inactive = 0 WHERE ToDoCategoryId = 4 --sales order
GO

IF COL_LENGTH('dbo.tbToDo', 'SalesOrderNo') IS NULL
BEGIN
   ALTER TABLE dbo.tbToDo ADD [SalesOrderNo] INT NULL
END
GO
