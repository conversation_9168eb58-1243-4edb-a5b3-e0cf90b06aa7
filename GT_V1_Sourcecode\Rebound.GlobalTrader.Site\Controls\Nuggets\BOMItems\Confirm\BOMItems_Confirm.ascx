<%@ Control Language="C#" CodeBehind="BOMItems_Confirm.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMItem_PartialRelease")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">		     
						
			

            <ReboundUI_Form:FormField id="ctlSerialNoDetail" runat="server" ResourceTitle="AllSourcingResults">
				<Field>	
                    
                        <asp:Panel ID="pnlSerialNodetails" runat="server" CssClass="GridSerialdetails" Width="85%">
                      <Reboundui:FlexiDataTable id="tblAllReleasedetails"  runat="server"  PanelHeight="200"  AllowSelection="true"/>
                       </asp:Panel>            
				</Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>		 
	</Content>
</ReboundUI_Form:DesignBase>
<style>
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_0,
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_1,
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_2,
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_3,
    #ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail_ctl03_tblAllReleasedetails_th_4 {
        border-color: #bdbdbd;    background-color: #bdbdbd !important;    color: #333 !important;
    }
</style>