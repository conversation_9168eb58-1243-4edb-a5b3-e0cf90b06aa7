<%@ Control Language="C#" CodeBehind="ClientImportBOMMainInfo_NoBid.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_NoBid" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMItem_NoBid")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
            <ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" Rows="2" TextMode="MultiLine" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlRelease" runat="server" FieldID="ctlConfirmRelease" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmRelease" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
