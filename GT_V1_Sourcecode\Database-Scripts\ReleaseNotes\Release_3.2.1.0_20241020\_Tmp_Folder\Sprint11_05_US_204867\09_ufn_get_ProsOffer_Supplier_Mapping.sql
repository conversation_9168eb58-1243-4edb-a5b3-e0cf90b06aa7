﻿/*   
===========================================================================================  
TASK			UPDATED BY      DATE			ACTION		DESCRIPTION  
[US-204867]		An.TranTan		25-Sep-2024		Create		Get mapping columns to insert data for prospective offer import
===========================================================================================  
*/
CREATE OR ALTER FUNCTION dbo.ufn_get_ProsOffer_Supplier_Mapping(@SupplierNo int)
RETURNS @retSupplierMappingInfo TABLE (
	ColumnNameList nvarchar(500),
	ColumnOrderList nvarchar(500)
)
AS
BEGIN
	DECLARE @ColumnNameList nvarchar(500) = '';
	DECLARE @ColumnOrderList nvarchar(500) = '';

	DECLARE @Manufacturer NVARCHAR(10),
		@Part NVARCHAR(10),
		@Quantity NVARCHAR(10),
		@Price NVARCHAR(10),
		@Description NVARCHAR(10),
		@AlterPart NVARCHAR(10),
		@DateCode NVARCHAR(10),
		@Product NVARCHAR(10),
		@Package NVARCHAR(10),
		@ROHS NVARCHAR(10),
		@SupplierPart NVARCHAR(10);

	SELECT 
		@Manufacturer = Manufacturer,
		@Part = Part,
		@Quantity = Quantity,
		@Price = Price,
		@Description = [Description],
		@AlterPart = AlterPart,
		@DateCode = DateCode,
		@Product  = Product,
		@Package = Package,
		@ROHS = ROHS,
		@SupplierPart = SupplierPart 
	FROM 
		BorisGlobalTraderimports.dbo.tbProspectiveOffer_ColumnMapping
	WHERE SupplierNo = @SupplierNo;
	
	IF ISNULL(@Manufacturer, '') <> ''
	BEGIN
		SET @ColumnNameList += 'Manufacturer,';
		SET @ColumnOrderList += @Manufacturer + ',';
	END
	IF ISNULL(@Part, '') <> ''
	BEGIN
		SET @ColumnNameList += 'Part,';
		SET @ColumnOrderList += @Part + ',';
	END
	IF ISNULL(@Quantity, '') <> ''
	BEGIN
		SET @ColumnNameList += 'Quantity,';
		SET @ColumnOrderList += @Quantity + ',';
	END
	IF ISNULL(@Price, '') <> ''
	BEGIN
		SET @ColumnNameList += 'Price,';
		SET @ColumnOrderList += @Price + ',';
	END
	IF ISNULL(@Description, '') <> ''
	BEGIN
		SET @ColumnNameList += 'Description,';
		SET @ColumnOrderList += @Description + ',';
	END
	IF ISNULL(@AlterPart, '') <> ''
	BEGIN
		SET @ColumnNameList += 'AlterPart,';
		SET @ColumnOrderList += @AlterPart + ',';
	END
	IF ISNULL(@DateCode, '') <> ''
	BEGIN
		SET @ColumnNameList += 'Datecode,';
		SET @ColumnOrderList += @DateCode + ',';
	END
	IF ISNULL(@Product, '') <> ''
	BEGIN
		SET @ColumnNameList += 'Product,';
		SET @ColumnOrderList += @Product + ',';
	END
	IF ISNULL(@Package, '') <> ''
	BEGIN
		SET @ColumnNameList += 'Package,';
		SET @ColumnOrderList += @Package + ',';
	END
	IF ISNULL(@ROHS, '') <> ''
	BEGIN
		SET @ColumnNameList += 'ROHSName,';
		SET @ColumnOrderList += @ROHS + ',';
	END
	IF ISNULL(@SupplierPart, '') <> ''
	BEGIN
		SET @ColumnNameList += 'SupplierPart';
		SET @ColumnOrderList += @SupplierPart;
	END
	--remove last comma if exist
	IF @ColumnNameList LIKE '%,'
		SET @ColumnNameList = LEFT(@ColumnNameList, LEN(@ColumnNameList) -1)
	IF @ColumnOrderList LIKE '%,'
		SET @ColumnOrderList = LEFT(@ColumnOrderList, LEN(@ColumnOrderList) -1)

	INSERT INTO @retSupplierMappingInfo SELECT @ColumnNameList, @ColumnOrderList;
	RETURN;
END
/*--test script
	SELECT * FROM dbo.ufn_get_ProsOffer_Supplier_Mapping(234399)
*/

