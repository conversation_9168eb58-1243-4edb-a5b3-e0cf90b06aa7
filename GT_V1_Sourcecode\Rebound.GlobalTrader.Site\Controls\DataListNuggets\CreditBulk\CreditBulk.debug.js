///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Salesperson filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
//
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.initializeBase(this, [element]);
    this._format = null;
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.prototype = {
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (v) { if (this._blnPOHub !== v) this._blnPOHub = v; },
    get_blnHubCredit: function () { return this._blnHubCredit; }, set_blnHubCredit: function (v) { if (this._blnHubCredit !== v) this._blnHubCredit = v; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (v) { if (this._IsGSA !== v) this._IsGSA = v; },
    get_ClientNo: function () { return this._ClientNo; }, set_ClientNo: function (v) { if (this._ClientNo !== v) this._ClientNo = v; },
    get_AllowGenerateXml: function () { return this._AllowGenerateXml; }, set_AllowGenerateXml: function (v) { if (this._AllowGenerateXml !== v) this._AllowGenerateXml = v; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.callBaseMethod(this, "initialize");
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._ibtnPrint = $get(this._aryButtonIDs[0]);
        this._ibtnEmail = $get(this._aryButtonIDs[1]);
        this._frmConfirm = $find(this._aryFormIDs[0]);
        this._frmConfirm.addCancel(Function.createDelegate(this, this.hideConfirmForm));
        this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveCeaseComplete));
        this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));
        if (this._ibtnEmail) $R_IBTN.addClick(this._ibtnEmail, Function.createDelegate(this, this.showConfirmForm));
        //if (this._ibtnPrint) $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.printCredit));
        if (this._ibtnPrint) $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.showFormatPopup));
        document.getElementById("btnFormatAction").addEventListener("click", Function.createDelegate(this, this.selectFormatAndPrint));
        document.getElementById("btnFormatCancel").addEventListener("click", Function.createDelegate(this, this.closeFormatPopup));

        this.enableBulkButtons(false);
        this._table.addMultipleSelectionChanged(Function.createDelegate(this, this.selectionMade));
        this._strPathToData = "controls/DataListNuggets/CreditBulk";
        this._strDataObject = "CreditBulk";
        this.getData();
    },
    enableBulkButtons: function (bln) {
        if (this._ibtnPrint) $R_IBTN.enableButton(this._ibtnPrint, bln);
        //[001] code start
        if (this._ibtnEmail) $R_IBTN.enableButton(this._ibtnEmail, bln);
        //[001] code end
    },
    selectionMade: function () {
        this.enableBulkButtons(this._table._arySelectedIndexes.length > 0);
    },
    initAfterBaseIsReady: function () {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.updateFilterVisibility();
        this.hubCreditFilterVisibility();
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._blnHubCredit = null;
        this._blnPOHub = null;
        this._IsGSA = null;
        this._ClientNo = null;
        this._format = null;
        this.closeFormatPopup();
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.callBaseMethod(this, "dispose");
    },

    pageTabChanged: function () {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.hubCreditFilterVisibility();
        this.getData();
    },

    setupDataCall: function () {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        this._objData.addParameter("blnHubCredit", this._blnHubCredit);
        this._objData.addParameter("PageSizeLimit", $("#ctl00_cphMain_ctlCredits_ctlDB_txtLimitResults").val());
    },

    getDataOK: function () {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
                $RGT_nubButton_CreditNote(row.ID, row.No)
                , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                , $R_FN.writeDoubleCellValue(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM) + '</span>' : $RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                , $R_FN.setCleanTextValue(row.Date)
                , $R_FN.writeDoubleCellValue($RGT_nubButton_Invoice(row.InvNo, row.Invoice), $RGT_nubButton_ClientInvoice(row.ClientInvoiceNo, row.ClientInvoiceNumber))
                , $R_FN.setCleanTextValue(row.CustPO)
                , $R_FN.setCleanTextValue(row.Total)
            ];
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function () {
        this.getFilterField("ctlSalesman").show(this._enmViewLevel != 0);
        this.getFilterField("ctlPohubOnly").show(this._blnPOHub);
        this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);
        this.getFilterField("ctlClientInvNo").show(false);
        this.getFilterField("ctlClientInvNo").enableField(false);
        this.getFilterField("ctlClientName").show(this._IsGSA);
    },
    hubCreditFilterVisibility: function () {
        this.getFilterField("ctlSalesman").show(this._enmViewLevel != 0 && !this._blnHubCredit);
        this.getFilterField("ctlCreditNotes").show(!this._blnHubCredit);
        this.getFilterField("ctlCompanyName").show(!this._blnHubCredit);
        this.getFilterField("ctlContactName").show(!this._blnHubCredit);
        this.getFilterField("ctlCRMANo").show(!this._blnHubCredit);
        this.getFilterField("ctlClientInvNo").show(true);

    },
    showConfirmForm: function () {
        this._frmConfirm._strCredits = this._table._aryCurrentValues;
        this._frmConfirm._ClientNo = this._ClientNo;
        this._frmConfirm._AllowGenerateXml = this._AllowGenerateXml;
        this.showForm(this._frmConfirm, true);
    },
    hideConfirmForm: function () {
        this.showForm(this._frmConfirm, false);
    },
    saveCeaseComplete: function () {
        this.hideConfirmForm();
    },
    printCredit: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CreditMainInfo");
        obj.set_DataObject("CreditMainInfo");
        obj.set_DataAction("SaveCreditPrint");
        obj.addParameter("CreditsPrintId", this._table._aryCurrentValues);
        obj.addDataOK(Function.createDelegate(this, this.printCreditsaveComplete));
        obj.addError(Function.createDelegate(this, this.printCreditError));
        obj.addTimeout(Function.createDelegate(this, this.printCreditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
    },
    printCreditError: function (args) {
        //this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    printCreditsaveComplete: function (args) {
        var PrintId = args._result.Result;
        var url = args._result.Url;
        //window.open(url + "/Print.aspx?pro=60&id=" + PrintId, "winPrint", "left=20,top=20,width=950,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
        if (this._format == 'XML') {
            window.open("Print.aspx?pro=64&id=" + PrintId, "winPrintBulkCredit", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
        } else {
            window.open("Print.aspx?pro=60&id=" + PrintId, "winPrintBulkCredit", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
        }
    },
    //test: function (args) {

    //},
    showFormatPopup: function () {
        let defaultOption = (this._AllowGenerateXml && this._ClientNo == 108) ? 'XML' : 'PDF';
        $("#overlay").css("display", "block");
        $('input:radio[name=rdFormat]').val([defaultOption]);
        $('#optionXML').css("display", this._AllowGenerateXml ? 'block' : 'none');
        $("#formatModal").dialog("open");
    },
    selectFormatAndPrint: function () {
        this._format = $('input[name="rdFormat"]:checked').val();
        this.printCredit();
        this.closeFormatPopup();
    },
    closeFormatPopup: function () {
        $("#overlay").css("display", "none");
        $("#formatModal").dialog("close");
    }
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CreditBulk", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
