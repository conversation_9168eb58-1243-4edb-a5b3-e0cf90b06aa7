using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class MyGIQueries : Base {

		#region Controls
		SimpleDataTable _tblMyGIQueries;
		#endregion

		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "MyGIQueries";
			base.OnInit(e);
			_tblMyGIQueries = (SimpleDataTable)FindContentControl("tblMyGIQueries");
			AddScriptReference("Controls.HomeNuggets.MyGIQueries.MyGIQueries.js");
		}

		protected override void OnLoad(EventArgs e) {
			//table headings
			_tblMyGIQueries.Columns.Add(new SimpleDataColumn("GINumber"));
            _tblMyGIQueries.Columns.Add(new SimpleDataColumn("SONumber"));
            _tblMyGIQueries.Columns.Add(new SimpleDataColumn("QueryDate"));
            _tblMyGIQueries.Columns.Add(new SimpleDataColumn("GIApprovers"));
            _tblMyGIQueries.Columns.Add(new SimpleDataColumn("GIStatus"));

            //setup javascript
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyGIQueries", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblMyGIQueries", _tblMyGIQueries.ClientID);
			base.OnLoad(e);
		}


	}
}