///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.initializeBase(this, [element]);
	this._intCompanyID = -1;
	this._intContactID = -1;
	this._intLogItemID = -1;
	this._intSchedulerID = -1;
	this._intDataListNuggetID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.prototype = {

	get_intCompanyID: function() { return this._intCompanyID; }, 	set_intCompanyID: function(value) { if (this._intCompanyID !== value)  this._intCompanyID = value; }, 
	get_strTitleEdit: function() { return this._strTitleEdit; }, 	set_strTitleEdit: function(value) { if (this._strTitleEdit !== value)  this._strTitleEdit = value; }, 
	get_strTitleAdd: function() { return this._strTitleAdd; }, 	set_strTitleAdd: function(value) { if (this._strTitleAdd !== value)  this._strTitleAdd = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.callBaseMethod(this, "initialize");
		this.addModeChanged(Function.createDelegate(this, this.changeTitleOnMode));
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intCompanyID = null;
		this._strTitleEdit = null;
		this._strTitleAdd = null;
		this._intCompanyID = null;
		this._intContactID = null;
		this._intLogItemID = null;
		this._intSchedulerID = null;
		this._intDataListNuggetID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.callBaseMethod(this, "dispose");
	},
		
	formShown: function() {
		if (this._blnFirstTimeShown) {
			//data
			this._strPathToData = "controls/DataListNuggets/CommunicationLog";
			this._strDataObject = "CommunicationLog";

			//form events
			this.addSaveClick(Function.createDelegate(this, this.saveClicked));
			this.addCancelClick(Function.createDelegate(this, this.cancelClicked));
		}
		this.resetFormFields();
		if (this._mode == "ADD") this.setFormFieldsToDefaults();
		this.updateDropDowns();
		this.showError(false);
	},

	saveClicked: function() {
		this.resetFormFields();
		if (!this.validateForm()) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		if (this._mode == "ADD") {
			obj.set_DataAction("AddLogItem");
			obj.addParameter("TypeNo", this.getFieldValue("ctlLogType"));
			obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
			obj.addParameter("ContactNo", this.getFieldValue("ctlContact"));
			obj.addParameter("CMNo", this._intCompanyID);
		} else {
			obj.set_DataAction("UpdateLogItem");
			obj.addParameter("ID", this._intLogItemID);
			obj.addParameter("TypeNo", this.getFieldValue("ctlLogType"));
			obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
			obj.addParameter("ContactNo", this.getFieldValue("ctlContact"));
			obj.addParameter("CMNo", this._intCompanyID);
		}
		obj.addParameter("DLNID", this._intDataListNuggetID);
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this.saveEditError(args);
		}
	},	

	validateForm: function() {
		var blnOK = true;
		if (!this.checkFieldEntered("ctlContact")) blnOK = false;
		if (!this.checkFieldEntered("ctlLogType")) blnOK = false;
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	cancelClicked: function() {
		this.onCancel(); //raise event
	},
	
	changeTitleOnMode: function() {
		switch (this._mode){
			case "ADD": this.changeTitle(this._strTitleAdd); break;
			case "EDIT": this.changeTitle(this._strTitleEdit); break;
		}
	},
	
	updateDropDowns: function() {
		this.getFieldControl("ctlContact")._intCompanyID = this._intCompanyID;
		this.setFieldValue("ctlContact", this._intContactID);
		this.getFieldDropDownData("ctlContact");
		this.getFieldDropDownData("ctlLogType");
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CommunicationLog_AddEdit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
