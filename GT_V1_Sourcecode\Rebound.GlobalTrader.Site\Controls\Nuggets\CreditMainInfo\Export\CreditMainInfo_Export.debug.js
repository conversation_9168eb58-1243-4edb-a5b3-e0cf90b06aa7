///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// SK 12.04.2010:
// - new proc
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.initializeBase(this, [element]);
    this._intCreditID = 0;
	this._strTitle_Export = null;
	this._strTitle_Release = null;
	this._lblExplainExport = null;
	this._lblExplainRelease = null;
	//locals
	this._ctlConfirm = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.prototype = {

    get_intCreditID: function () { return this._intCreditID; }, set_intCreditID: function (v) { if (this._intCreditID !== v) this._intCreditID = v; },
    get_strTitle_Export: function() { return this._strTitle_Export; }, set_strTitle_Export: function(value) { if (this._strTitle_Export !== value) this._strTitle_Export = value; },
    get_strTitle_Release: function() { return this._strTitle_Release; }, set_strTitle_Release: function(value) { if (this._strTitle_Release !== value) this._strTitle_Release = value; },
    get_lblExplainExport: function() { return this._lblExplainExport; }, set_lblExplainExport: function(value) { if (this._lblExplainExport !== value) this._lblExplainExport = value; },
    get_lblExplainRelease: function() { return this._lblExplainRelease; }, set_lblExplainRelease: function(value) { if (this._lblExplainRelease !== value) this._lblExplainRelease = value; }, 

	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
		this.checkMode();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._ctlConfirm = null;
		this._intInvoiceID = null;
		this._strTitle_Export = null;
		this._strTitle_Release = null;
		this._lblExplainExport = null;
		this._lblExplainRelease = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.callBaseMethod(this, "dispose");
	},

	yesClicked: function() {
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/CreditMainInfo");
		obj.set_DataObject("CreditMainInfo");
		obj.set_DataAction("ExportRelease");
		obj.addParameter("id", this._intCreditID);
		var blnExport = (this._mode == "EXPORT");
		obj.addParameter("Exported", blnExport);
		obj.addDataOK(Function.createDelegate(this, this.saveComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},
	
	checkMode: function() {
		switch (this._mode){
		    case "EXPORT": this.changeTitle(this._strTitle_Export); break;
		    case "RELEASE": this.changeTitle(this._strTitle_Release); break;
		}
		$R_FN.showElement(this._lblExplainExport, this._mode == "EXPORT");
		$R_FN.showElement(this._lblExplainRelease, this._mode == "RELEASE");
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Export", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
