IF OBJECT_ID('usp_select_Company_MainInfo', 'P') IS NOT NULL
	DROP PROC [dbo].[usp_select_Company_MainInfo]
GO
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201556]		Phuc Hoang			30-May-2024		UPDATE			Implement Group Code Functionality for Premier Accounts
===========================================================================================
*/

CREATE PROCEDURE   [dbo].[usp_select_Company_MainInfo]              
    --******************************************************************************************                                             
    --* Gets the Main info for a company                                             
    --*                                              
    --* SK 26.04.2010:                                             
    --* - add salesman info                                             
    --*                                              
    --* RP 17.11.2009:                                             
    --* - new proc                        
              
    /*                 
 Marker   Owner    Date   Remarks               
 [001]   Ravi    29-05-2023  RP-1269               
*/              
    --******************************************************************************************                                             
    @CompanyId int              
AS              

declare @creditlimitids nvarchar(max)

select @creditlimitids = coalesce(@creditlimitids + '|', '') + cast(creditlimitid as nvarchar(20))
from tbcreditlimit
where CompanyId = @CompanyId

SELECT co.CompanyId                                             
          , co.CompanyName                                             
          , co.ParentCompanyNo                                             
          , cop.CompanyName AS ParentCompanyName                                             
          , co.Telephone                                             
          , co.Telephone800                                             
          , co.Fax                                             
          , co.EMail                                             
          , co.URL                                             
          , co.TypeNo                                             
          , typ.[Name] AS CompanyType                                             
          , co.Tax                                             
          , co.CompanyRegNo                                           
          , co.Notes                                             
          , co.ImportantNotes                                             
          , co.OnStop                                             
          , co.UpdatedBy                                             
          , co.DLUP                                             
          , co.Salesman                                             
          , lg.EmployeeName AS SalesmanName                                        
          , co.certificateNotes                                       
          , co.qualityNotes                                        
          , co.isTraceability                                        
          , co.ERAIMember                                     
          , co.ERAIReported                                   
          , co.ReviewDate                                 
          , co.UPLiftPrice                               
          , co.IsPOHub                               
          , co.LastReviewDate                            
          , co.PreviousReviewDate                         
          , co.SupplierWarranty                      
    , co.EORINumber                            
    , DATEDIFF(day,isnull(co.SalesmanUpdatedDate, co.DLUP), getdate() )  as SalesAccountInDays                           
    , co.ClientNo                       
 , co.SupplierProdReqTesting                   
 , ISNULL(co.IsSupplier,0) AS ISSupplier                 
 , ISNULL(co.IsCompany,0) AS ISCustomer                 
 , co.POApproved                 
 , co.SOApproved                 
 , co.SupplierOnStop               
 ,co.isPremierCustomer           
 ,co.IsTier2PremierCustomer          
 , ISNULL(co.IsSanctioned,0) AS IsSanctioned--[001]        
 ,co.PurchasingNotes      
 ,@creditlimitids as CreditLimitIds
 ,co.GroupCodeNo
 ,cg.ContactName as CustomerGroupName
FROM tbCompany co              
    LEFT JOIN dbo.tbCompany cop ON co.ParentCompanyNo = cop.CompanyId              
    LEFT JOIN dbo.tbCompanyType typ ON co.TypeNo = typ.CompanyTypeId       
    LEFT JOIN dbo.tbLogin lg ON co.Salesman = lg.LoginId
	LEFT JOIN dbo.tbContactGroup cg ON co.GroupCodeNo = cg.ItemId
WHERE   co.CompanyId = @CompanyId       
      
GO


