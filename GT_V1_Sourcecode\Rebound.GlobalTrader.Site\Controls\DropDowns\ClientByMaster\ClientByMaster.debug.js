///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 06.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.prototype = {

    get_intMasterLoginNo: function () { return this._intMasterLoginNo; }, set_intMasterLoginNo: function (v) { if (this._intMasterLoginNo !== v) this._intMasterLoginNo = v; },


	initialize: function() {
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
		Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.callBaseMethod(this, "initialize");
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		this._intMasterLoginNo = null;
		Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() { 
		this._objData.set_PathToData("controls/DropDowns/ClientByMaster");
		this._objData.set_DataObject("ClientByMaster");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("MasterLoginNo", this._intMasterLoginNo);
//		this._objData.addParameter("LimitToCurrentUsersDivision", this._blnLimitToCurrentUsersDivision);
//		this._objData.addParameter("ExcludeCurrentUser", this._blnExcludeCurrentUser);
	},
	
	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Clients) {
			for (var i = 0; i < result.Clients.length; i++) {
				this.addOption(result.Clients[i].Name, result.Clients[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ClientByMaster", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
