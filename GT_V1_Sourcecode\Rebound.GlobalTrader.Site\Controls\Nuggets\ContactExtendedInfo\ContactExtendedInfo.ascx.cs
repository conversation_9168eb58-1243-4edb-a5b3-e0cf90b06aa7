using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site.Pages.Contact;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class ContactExtendedInfo : Base {

		#region Locals

		protected IconButton _ibtnEdit;
		//protected Forms.ContactExtendedInfo_Edit _ctlContactExtendedInfo_Edit;

		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		private int _intContactID = -1;
		public int ContactID {
			get { return _intContactID; }
			set { _intContactID = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.ContactExtendedInfo.ContactExtendedInfo");
			TitleText = Functions.GetGlobalResource("Nuggets", "ContactExtendedInfo");
			if (_intCompanyID < 0) _intCompanyID = _objQSManager.CompanyID;
			if (_intContactID < 0) _intContactID = _objQSManager.ContactID;
		}

		protected override void OnLoad(EventArgs e) {
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnEdit.Visible = _blnCanEdit;
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.ContactExtendedInfo", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
			_scScriptControlDescriptor.AddProperty("intContactID", _intContactID);
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnEdit = FindIconButton("ibtnEdit");
			//_ctlContactExtendedInfo_Edit = (Forms.ContactExtendedInfo_Edit)ctlDesignBase.FindFormControl("ctlContactExtendedInfo_Edit");
		}

	}
}
