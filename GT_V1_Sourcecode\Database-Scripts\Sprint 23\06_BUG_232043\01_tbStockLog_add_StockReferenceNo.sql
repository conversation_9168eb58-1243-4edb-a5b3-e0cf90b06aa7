﻿/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-232043]     CuongDox		 12-Mar-2025		CREATE		Bug 232043: [PROD Bug] Stock is Quarantined until release
============================================================================================================================  
*/
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
           WHERE TABLE_NAME = 'tbStockLog' 
           AND COLUMN_NAME = 'StockReferenceNo')
BEGIN
    ALTER TABLE tbStockLog
	ADD StockReferenceNo INT NULL;
END