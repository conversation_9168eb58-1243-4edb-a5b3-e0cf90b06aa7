<%@ Control Language="C#" CodeBehind="GoodsIn.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.GoodsIn" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" />
		<ReboundUI_FilterDataItemRow:Numerical id="ctlGINo" runat="server" ResourceTitle="GINo" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeInvoiced" runat="server" ResourceTitle="IncludeInvoiced" />
		<ReboundUI_FilterDataItemRow:DropDown id="ctlReceivedBy" runat="server" ResourceTitle="ReceivedBy" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
		<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlCRMANo" runat="server" ResourceTitle="CRMANo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlAirWayBill" runat="server" ResourceTitle="AirWayBill" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedFrom" runat="server" ResourceTitle="DateReceivedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedTo" runat="server" ResourceTitle="DateReceivedTo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
