using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Threading;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:LanguageSwitch runat=server></{0}:LanguageSwitch>")]
	public class LanguageSwitch : Panel, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected PlaceHolder _plhLanguages;
		private List<string> _lstLinkClientIDs = new List<string>();

		#endregion

		#region Properties

		private List<LanguageSwitchLink> _lstLinks = new List<LanguageSwitchLink>();
		public List<LanguageSwitchLink> Links {
			get { return _lstLinks; }
			set { _lstLinks = value; }
		}

		#endregion

		#region Constructors

		public LanguageSwitch() { }

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("LanguageSwitch.css");
			CssClass = "languageSelect";
			base.OnInit(e);
		}

		protected override void CreateChildControls() {
			_plhLanguages = new PlaceHolder();
			Controls.Add(_plhLanguages);
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {

			//move the currently selected to the top
			for (int i = 0; i < _lstLinks.Count; i++) {
				if (_lstLinks[i].IsCurrent) {
					LanguageSwitchLink lsl = _lstLinks[i];
					_lstLinks.RemoveAt(i);
					_lstLinks.Insert(0, lsl);
					lsl.Dispose();
					lsl = null;
				}
			}

			//add all language buttons to the page and store the client ids
			foreach (LanguageSwitchLink lsl in _lstLinks) {
				_plhLanguages.Controls.Add(lsl);
				_lstLinkClientIDs.Add(lsl.ClientID);
			}

			//add script reference
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		public void AddLanguage(GlobalLanguage.List enmLanguage) {
			EnsureChildControls();
			LanguageSwitchLink lsl = new LanguageSwitchLink(enmLanguage);
			lsl.ID = string.Format("lnk{0}", GlobalLanguage.GetLanguageCode(enmLanguage).ToUpper());
			_plhLanguages.Controls.Add(lsl);
			_lstLinks.Add(lsl);
			lsl.Dispose();
			lsl = null;
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.LanguageSwitch.LanguageSwitch", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LanguageSwitch", this.ClientID);
			descriptor.AddProperty("aryLinkIDs", _lstLinkClientIDs);
			descriptor.AddProperty("strCurrentCulture", SessionManager.Culture);

			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}