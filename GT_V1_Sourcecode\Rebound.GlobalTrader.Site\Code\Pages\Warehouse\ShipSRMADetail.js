Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail=function(n){Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.prototype={get_intSRMAID:function(){return this._intSRMAID},set_intSRMAID:function(n){this._intSRMAID!==n&&(this._intSRMAID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlLines:function(){return this._ctlLines},set_ctlLines:function(n){this._ctlLines!==n&&(this._ctlLines=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlMainInfo&&this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this,this.ctlMainInfo_GetDataComplete));this._ctlLines&&this._ctlLines.addSaveShipComplete(Function.createDelegate(this,this.ctlLines_SaveShipComplete));Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlLines&&this._ctlLines.dispose(),this._intSRMAID=null,this._ctlMainInfo=null,this._ctlLines=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.callBaseMethod(this,"dispose"))},ctlMainInfo_GetDataComplete:function(){this._ctlLines._frmShip&&this._ctlLines._frmShip.setFieldsFromHeader(this._ctlMainInfo.getFieldValue("hidSupplierNo"),this._ctlMainInfo.getFieldValue("hidSupplierName"),this._ctlMainInfo.getFieldValue("hidNo"),this._ctlMainInfo.getFieldValue("ctlShipToAddress"),this._ctlMainInfo.getFieldValue("hidContactNo"),this._ctlMainInfo.getFieldValue("hidContactName"),this._ctlMainInfo.getFieldValue("hidAuthorisedBy"),this._ctlMainInfo.getFieldValue("ctlAuthoriser"),this._ctlMainInfo.getFieldValue("hidDivisionNo"),this._ctlMainInfo.getFieldValue("hidCurrencyNo"),this._ctlMainInfo.getFieldValue("ctlCurrency"),this._ctlMainInfo.getFieldValue("hidCurrencyCode"),this._ctlMainInfo.getFieldValue("hidPurchaseOrderNo"),this._ctlMainInfo.getFieldValue("hidPurchaseOrderNumber"),this._ctlMainInfo.getFieldValue("hidTaxNo"),this._ctlMainInfo.getFieldValue("ctlTax"),this._ctlMainInfo.getFieldValue("ctlNotes"),this._ctlMainInfo.getFieldValue("ctlInstructions"),this._ctlMainInfo.getFieldValue("hidPOBuyerNo"),this._ctlMainInfo.getFieldValue("hidPOBuyerName"));this._ctlLines._intGlobalClientNo=this._IsGlobalLogin==!0?this._ctlMainInfo.getFieldValue("hidGlobalClientNo"):null;this._ctlMainInfo._IsGlobalLogin=this._IsGlobalLogin},ctlLines_SaveShipComplete:function(){this._ctlMainInfo.getData()}};Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);