﻿using Rebound.GlobalTrader.DAL.Common.Entities;
using System;


namespace Rebound.GlobalTrader.BLL
{
    public partial class AS6081AlertMessage : BizObject
    {
        #region Properties
        protected static DAL.AS6081Element Settings
        {
            get { return Globals.Settings.AS6081s; }
        }

        public System.Int32 AlertMessageId { get; set; }
        public System.String ShortName { get; set; }

        public System.String Message { get; set; }

        public System.String Description { get; set; }

        public System.Int32? UpdatedBy { get; set; }

        public System.DateTime? DLUP { get; set; }
        public System.Boolean? AS6081 { get; set; }
        #endregion

        #region Methods
        /// <summary>
        /// 
        /// </summary>
        /// <param name="alertMessageId"></param>
        /// <returns>AS6081AlertMessage</returns>
        /// <exception cref="Exception"></exception>
        public static AS6081AlertMessage GetAlertMessage(System.Int32 alertMessageId)
        {
            try
            {
                AS6081AlertMessageDetails objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.GetAlertMessageById(alertMessageId);
                if (objReturn != null)
                {
                    return new AS6081AlertMessage
                    {
                        AlertMessageId = objReturn.AlertMessageId,
                        ShortName = objReturn.ShortName,
                        Message = objReturn.Message,
                    };
                }
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
            return new AS6081AlertMessage();
        }

        public static AS6081AlertMessage GetAlertMessage(System.String operationType)
        {
            try
            {
                AS6081AlertMessageDetails objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.GetAlertMessageByOperationType(operationType);
                if (objReturn != null)
                {
                    return new AS6081AlertMessage
                    {
                        AlertMessageId = objReturn.AlertMessageId,
                        ShortName = objReturn.ShortName,
                        Message = objReturn.Message,
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            return new AS6081AlertMessage();
        }

        public static AS6081AlertMessage GetAlertMessageForSoPrint(System.String operationType, System.Int32 soID)
        {
            try
            {
                AS6081AlertMessageDetails objReturn = Rebound.GlobalTrader.DAL.SiteProvider.AS6081.GetAlertMessageByOperationTypeForSoPrint(operationType, soID);
                if (objReturn != null)
                {
                    return new AS6081AlertMessage
                    {
                        AlertMessageId = objReturn.AlertMessageId,
                        ShortName = objReturn.ShortName,
                        Message = objReturn.Message,
                        AS6081 = objReturn.AS6081
                    };
                }
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
            return new AS6081AlertMessage();
        }
        #endregion
    }
}
