using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:MultiStepItem runat=server></{0}:MultiStepItem>")]
	public class MultiStepItem : TableCell, INamingContainer {

		#region Locals

		private HyperLink _hyp;

		#endregion

		#region Properties

		/// <summary>
		/// ResourceTitle
		/// </summary>
		private string _strResourceTitle;
		public string ResourceTitle {
			get { return _strResourceTitle; }
			set { _strResourceTitle = value; }
		}

		/// <summary>
		/// Is it selected?
		/// </summary>
		private bool _blnIsSelected = false;
		public bool IsSelected {
			get { return _blnIsSelected; }
			set { _blnIsSelected = value; }
		}

		/// <summary>
		/// The control containing the content for the MultiStepItem
		/// </summary>
		private string _strRelatedContentFormID;
		public string RelatedContentFormID {
			get { return _strRelatedContentFormID; }
			set { _strRelatedContentFormID = value; }
		}
		private Tables.Form _pnlRelatedContentForm;
		public Tables.Form RelatedContentForm {
			get { return _pnlRelatedContentForm; }
			set { _pnlRelatedContentForm = value; }
		}

		/// <summary>
		/// Step number of this MultiStepItem within its parent MultiStep control
		/// </summary>
		private int _intStepNumber;
		internal int StepNumber {
			get { return _intStepNumber; }
			set { _intStepNumber = value; }
		}

		/// <summary>
		/// Parent MultiStep
		/// </summary>
		private MultiStep _ctlParentMultiStep;
		public MultiStep ParentMultiStep {
			get { return _ctlParentMultiStep; }
			set { _ctlParentMultiStep = value; }
		}

		/// <summary>
		/// Client ID of Hyperlink
		/// </summary>
		public string LinkID {
			get { return _hyp.ClientID; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			base.OnLoad(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			CssClass = string.Format("step step{0}", _ctlParentMultiStep.CurrentStepTimeString(_intStepNumber));
			_hyp = ControlBuilders.CreateHyperLinkInsideParent(this, string.Format("step{0}", _intStepNumber), "javascript:void(0);", Functions.GetGlobalResource("MultiStepTitles", _strResourceTitle));
			_hyp.Attributes["onclick"] = "void(0);";
			base.CreateChildControls();
		}

		#endregion

		/// <summary>
		/// Publicly accessible call to EnsureChildControls()
		/// </summary>
		internal void MakeChildControls() {
			EnsureChildControls();
		}

		/// <summary>
		/// Finds a control within the MultiStepItem content
		/// </summary>
		/// <param name="strName"></param>
		/// <returns></returns>
		public Control FindContentControl(string strName) {
			return Functions.FindControlRecursive(_pnlRelatedContentForm, strName);
		}

	}
}