<%@ Control Language="C#" CodeBehind="CRMALines_Deallocate.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CRMALines_Deallocate" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CRMALines_Deallocate")%></Explanation>
	
	<Content>
	
		<ReboundUI_Table:Form id="frm" runat="server">
		
            <ReboundUI_Form:FormField id="ctlCustomerRMA" runat="server" FieldID="lblCustomerRMA" ResourceTitle="CustomerRMANo" >
	            <Field><asp:Label ID="lblCustomerRMA" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            
            <ReboundUI_Form:FormField id="ctlCustomer" runat="server" FieldID="lblCustomer" ResourceTitle="Customer">
	            <Field><asp:Label ID="lblCustomer" runat="server" /></Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
		</ReboundUI_Table:Form>
		
	</Content>
	
</ReboundUI_Form:DesignBase>
