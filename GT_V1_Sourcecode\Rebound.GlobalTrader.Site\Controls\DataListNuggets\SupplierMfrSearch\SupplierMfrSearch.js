Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.prototype={get_blnShowUninspectedOnly:function(){return this._blnShowUninspectedOnly},set_blnShowUninspectedOnly:function(n){this._blnShowUninspectedOnly!==n&&(this._blnShowUninspectedOnly=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.updateFilterVisibility();this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/SupplierMfrSearch";this._strDataObject="SupplierMfrSearch";Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.showContentLoading(!1)},dispose:function(){this.isDisposed||(this._blnShowUninspectedOnly=null,this._blnPOHub=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._blnShowUninspectedOnly=this._intCurrentTab==1;this.updateFilterVisibility();this.showContentLoading(!1)},setupDataCall:function(){this._objData.addParameter("UninspectedOnly",this._blnShowUninspectedOnly);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(n.ID,n.No),n.QueryRaised==!0?"<a target='_blank' href='Whs_GIDetail.aspx?gi="+n.ID+"&landedgilineid="+n.GoodsInLineId+"&islandedquery=true'><img src='../App_Themes/Original/images/buttons/nuggets/quote.gif' title='Query Raised For GI Line.' id='imgQuery'/><\/a>":""),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$RGT_nubButton_Manufacturer(n.MfrNo,n.Mfr)),n.Quantity,$R_FN.writeDoubleCellValue($R_FN.showSupplierMessage($RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.SuppMessage)),$R_FN.setCleanTextValue(n.AWB)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Date),$R_FN.setCleanTextValue(n.Receiver)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.DelivDate),$RGT_nubButton_PurchaseOrder(n.PONo,n.PO)),$R_FN.setCleanTextValue(n.GoodInLineMessage),$R_FN.setCleanTextValue(n.ClientName)],this._table.addRow(i,n.ID,!1),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlClientName").show(this._blnPOHub||this._IsGlobalLogin)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierMfrSearch",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);