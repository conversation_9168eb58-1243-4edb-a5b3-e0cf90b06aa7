<%@ Control Language="C#" CodeBehind="PurchaseOrdersReceive.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo" TextBoxMaxLength="10" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyer" runat="server" ResourceTitle="Buyer" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Buyer" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="Company" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlIPO" runat="server" ResourceTitle="InternalPurchaseOrderNo" FilterField="IPONo" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" FilterField="DateOrderedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" FilterField="DateOrderedTo" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDeliveryDateFrom" runat="server" ResourceTitle="DeliveryDateFrom" FilterField="DeliveryDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDeliveryDateTo" runat="server" ResourceTitle="DeliveryDateTo" FilterField="DeliveryDateTo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlExpediteDateFrom" runat="server" ResourceTitle="ExpediteDateFrom" FilterField="ExpediteDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlExpediteDateTo" runat="server" ResourceTitle="ExpediteDateTo" FilterField="ExpediteDateTo" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
