Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.initializeBase(this,[n]);this._intCompanyID=0;this.txtemailcheck=!1;this.txtphncheck=!1;this.txtpswrd1check=!1;this.txtpswrd2check=!1;this.confirmpswrd=!1;this.txtContactPerson=!1;this.BOMCheck=!1;this.checkflag=!1;this.name=!1};Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._strPathToData="controls/Nuggets/CompanyApiCustomer",this._strDataObject="CompanyApiCustomer");this.setFormFieldsToDefaults()},saveClicked:function(){var n=this.getFieldValue("ctlBomUser"),t=this.getFieldValue("ctlSupplierUser"),i=this.validatedetails();n||t?this.BOMCheck=!0:(this.BOMCheck=!1,alert("Please select atleast one from BOM and Supplier"),this.showError(!0));this.txtemailcheck==!0&&this.txtphncheck==!0&&this.txtpswrd1check==!0&&this.BOMCheck==!0&&this.checkflag==!0&&this.name==!0?this.addNew():this.showError(!0)},alertmassage:function(){alert("please fill the correct value")},resetFormFields:function(){$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color","#56954E");$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword").css("background-color","#56954E");$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color","#56954E");$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color","#56954E");$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color","#56954E")},validatedetails:function(){var n=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword_ctl03_txtPassword").val(),u=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").val(),i=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail_ctl04_txtEmail").val(),r=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile").val(),f=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtMobile").val(),t=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson_ctl03_txtContactPerson").val();i.length==0?(this.checkflag=!1,this.txtemailcheck=!1,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color","#990000"),alert("Please enter email address!")):this.validateEmail(i)==!0?(this.checkflag=!0,this.txtemailcheck=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color","#56954E")):(this.checkflag=!1,this.txtemailcheck=!1,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color","#990000"));this.validateName(t)==!0?(this.checkflag=!0,this.name=!0,this.txtContactPerson=!0):(this.checkflag=!1,this.name=!1,this.txtContactPerson=!1);this.validate10digitnumber(r)==!0?(this.checkflag=!0,this.txtphncheck=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color","#56954E")):(this.checkflag=!1,this.txtphncheck=!1,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color","#990000"));t!=""&&t!=undefined&&t.length>2?(this.checkflag=!0,this.txtContactPerson=!0):(this.checkflag=!1,this.txtContactPerson=!1,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color","#990000"));n!=""&&n!=" "&&n!=undefined&&n.length>=16?(this.checkflag=!0,this.txtpswrd1check=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color","#56954E")):(this.checkflag=!1,this.txtpswrd2check=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color","#990000"));n!=""&&n!=" "&&n!=undefined&&n.length>=16?(this.checkflag=!0,this.confirmpswrd=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color","#56954E"),$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword").css("background-color","#56954E")):(this.checkflag=!1,this.confirmpswrd=!1,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword").css("background-color","#990000"),$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword").css("background-color","#990000"))},validateEmail:function(n){return/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/.test(n)?(this.checkflag=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color","#56954E"),!0):(this.checkflag=!1,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlEmail").css("background-color","#990000"),alert("You have entered an invalid email address!"),!1)},validateName:function(n){return/^[a-zA-Z\s]+$/.test(n)?(this.checkflag=!0,this.name=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color","#56954E !important"),!0):($("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlContactPerson").css("background-color","#990000 !important"),this.checkflag=!1,this.name=!1,alert("Only Alphabets are allowed"),!1)},validate10digitnumber:function(n){var t=n;return t.length!=10?!1:(intRegex=/^((?!(0))[0-9]{10})$/,intRegex.test(t)?(is_mobile=!0,this.checkflag=!0,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color","#56954E")):(is_mobile=!1,this.checkflag=!1,$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile").css("background-color","#990000"),alert("Invalid mobile no")),is_mobile)},validateForm:function(){var n=!0;return n=this.autoValidateFields(),n||this.showError(!0),n},addNew:function(){var n,t;n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("AddNew");n.addParameter("id",this._intCompanyID);n.addParameter("Email",this.getFieldValue("ctlEmail"));n.addParameter("ContactName",this.getFieldValue("ctlContactPerson"));t=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlMobile_ctl03_txtCountryCode").val();n.addParameter("CountryCode",t);n.addParameter("Mobile",this.getFieldValue("ctlMobile"));n.addParameter("InActive",this.getFieldValue("ctlInActive"));n.addParameter("IsBomUser",this.getFieldValue("ctlBomUser"));n.addParameter("InSupUser",this.getFieldValue("ctlSupplierUser"));n.addParameter("Password",this.getFieldValue("ctlPassword"));n.addDataOK(Function.createDelegate(this,this.saveEditOK));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},saveEditError:function(){alert("Person Exist");this.onSaveComplete()},saveEditOK:function(n){n._result.Result==!0?(this._intLineID=n._result.NewID,this.onSaveComplete()):this.saveEditError(n)}};$(document).ready(function(){function n(){var n="",t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz123456789",i;for(let r=1;r<18;r++)i=Math.floor(Math.random()*t.length+1),n+=t.charAt(i);return n}$("#checkbox").click(function(){var n=$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").attr("type");n=="password"?$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").attr("type","text"):$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlConfirmPassword_ctl03_txtConfirmPassword").attr("type","password")});$("#btngenpass").click(function(){var t=n();$("#ctl00_cphMain_ctlCompanyAPICustomer_ctlDB_ctl14_frmAdd_ctlDB_ctlPassword_ctl03_txtPassword").val(t)})});Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);