using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets {
	public partial class CompanySettingsSelection : Selection {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("CompanySettingsSelection");
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base", ctlDesignBase.ClientID);
			AddItems();
		}

		private void AddItems() {
			HtmlControl ul = new HtmlGenericControl("ul");
			ul.Controls.Add(AddHeading(Functions.GetGlobalResource("Misc", "Setup_CompanySettings")));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_AppSettings)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_ApplicationSettings));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_Divisions)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_Division));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_ClientInvoiceHeader)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_ClientInvoiceHeader));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_Countries)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_Country));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_Currencies)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_Currency));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_MailGroups)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_MailGroups));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_PrintedDocuments)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_PrintedDocuments));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_Products)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_Product));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_SequenceNumbers)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_SequenceNumber));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_ShippingMethods)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_ShippingMethod));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_SourcingLinks)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_SourcingLinks));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_StockLogReasons)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_StockLogReason));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_Taxes)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_Tax));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_Teams)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_Team));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_Terms)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_Terms));
			if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_Warehouses)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanyDetails_Warehouse));
            if (SecurityManager.CheckSectionPermission(Rebound.GlobalTrader.BLL.SecurityFunction.List.Setup_CompanySettings_RestrictedManufacture)) ul.Controls.Add(AddItem(BLL.SitePage.List.Setup_CompanySettings_RestrictedManufacture));
            _plhItems.Controls.Add(ul);
		}
	}
}