Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.Orders=function(n){Rebound.GlobalTrader.Site.Pages.Orders.Orders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.Orders.prototype={get_ctlEllipses_CusReqs:function(){return this._ctlEllipses_CusReqs},set_ctlEllipses_CusReqs:function(n){this._ctlEllipses_CusReqs!==n&&(this._ctlEllipses_CusReqs=n)},get_txtCusReqNo:function(){return this._txtCusReqNo},set_txtCusReqNo:function(n){this._txtCusReqNo!==n&&(this._txtCusReqNo=n)},get_ibtnGo_CusReq:function(){return this._ibtnGo_CusReq},set_ibtnGo_CusReq:function(n){this._ibtnGo_CusReq!==n&&(this._ibtnGo_CusReq=n)},get_ctlEllipses_Quotes:function(){return this._ctlEllipses_Quotes},set_ctlEllipses_Quotes:function(n){this._ctlEllipses_Quotes!==n&&(this._ctlEllipses_Quotes=n)},get_txtQuoteNo:function(){return this._txtQuoteNo},set_txtQuoteNo:function(n){this._txtQuoteNo!==n&&(this._txtQuoteNo=n)},get_ibtnGo_Quote:function(){return this._ibtnGo_Quote},set_ibtnGo_Quote:function(n){this._ibtnGo_Quote!==n&&(this._ibtnGo_Quote=n)},get_ctlEllipses_SalesOrders:function(){return this._ctlEllipses_SalesOrders},set_ctlEllipses_SalesOrders:function(n){this._ctlEllipses_SalesOrders!==n&&(this._ctlEllipses_SalesOrders=n)},get_txtSalesOrderNo:function(){return this._txtSalesOrderNo},set_txtSalesOrderNo:function(n){this._txtSalesOrderNo!==n&&(this._txtSalesOrderNo=n)},get_ibtnGo_SalesOrder:function(){return this._ibtnGo_SalesOrder},set_ibtnGo_SalesOrder:function(n){this._ibtnGo_SalesOrder!==n&&(this._ibtnGo_SalesOrder=n)},get_ctlEllipses_Invoices:function(){return this._ctlEllipses_Invoices},set_ctlEllipses_Invoices:function(n){this._ctlEllipses_Invoices!==n&&(this._ctlEllipses_Invoices=n)},get_txtInvoiceNo:function(){return this._txtInvoiceNo},set_txtInvoiceNo:function(n){this._txtInvoiceNo!==n&&(this._txtInvoiceNo=n)},get_ibtnGo_Invoice:function(){return this._ibtnGo_Invoice},set_ibtnGo_Invoice:function(n){this._ibtnGo_Invoice!==n&&(this._ibtnGo_Invoice=n)},get_ctlEllipses_PurchaseOrders:function(){return this._ctlEllipses_PurchaseOrders},set_ctlEllipses_PurchaseOrders:function(n){this._ctlEllipses_PurchaseOrders!==n&&(this._ctlEllipses_PurchaseOrders=n)},get_txtPurchaseOrderNo:function(){return this._txtPurchaseOrderNo},set_txtPurchaseOrderNo:function(n){this._txtPurchaseOrderNo!==n&&(this._txtPurchaseOrderNo=n)},get_ibtnGo_PurchaseOrder:function(){return this._ibtnGo_PurchaseOrder},set_ibtnGo_PurchaseOrder:function(n){this._ibtnGo_PurchaseOrder!==n&&(this._ibtnGo_PurchaseOrder=n)},get_ctlEllipses_CRMAs:function(){return this._ctlEllipses_CRMAs},set_ctlEllipses_CRMAs:function(n){this._ctlEllipses_CRMAs!==n&&(this._ctlEllipses_CRMAs=n)},get_txtCRMANo:function(){return this._txtCRMANo},set_txtCRMANo:function(n){this._txtCRMANo!==n&&(this._txtCRMANo=n)},get_ibtnGo_CRMA:function(){return this._ibtnGo_CRMA},set_ibtnGo_CRMA:function(n){this._ibtnGo_CRMA!==n&&(this._ibtnGo_CRMA=n)},get_ctlEllipses_SRMAs:function(){return this._ctlEllipses_SRMAs},set_ctlEllipses_SRMAs:function(n){this._ctlEllipses_SRMAs!==n&&(this._ctlEllipses_SRMAs=n)},get_txtSRMANo:function(){return this._txtSRMANo},set_txtSRMANo:function(n){this._txtSRMANo!==n&&(this._txtSRMANo=n)},get_ibtnGo_SRMA:function(){return this._ibtnGo_SRMA},set_ibtnGo_SRMA:function(n){this._ibtnGo_SRMA!==n&&(this._ibtnGo_SRMA=n)},get_ctlEllipses_PurReqs:function(){return this._ctlEllipses_PurReqs},set_ctlEllipses_PurReqs:function(n){this._ctlEllipses_PurReqs!==n&&(this._ctlEllipses_PurReqs=n)},get_ctlEllipses_Credits:function(){return this._ctlEllipses_Credits},set_ctlEllipses_Credits:function(n){this._ctlEllipses_Credits!==n&&(this._ctlEllipses_Credits=n)},get_txtCreditNo:function(){return this._txtCreditNo},set_txtCreditNo:function(n){this._txtCreditNo!==n&&(this._txtCreditNo=n)},get_ibtnGo_Credit:function(){return this._ibtnGo_Credit},set_ibtnGo_Credit:function(n){this._ibtnGo_Credit!==n&&(this._ibtnGo_Credit=n)},get_ctlEllipses_Debits:function(){return this._ctlEllipses_Debits},set_ctlEllipses_Debits:function(n){this._ctlEllipses_Debits!==n&&(this._ctlEllipses_Debits=n)},get_txtDebitNo:function(){return this._txtDebitNo},set_txtDebitNo:function(n){this._txtDebitNo!==n&&(this._txtDebitNo=n)},get_ibtnGo_Debit:function(){return this._ibtnGo_Debit},set_ibtnGo_Debit:function(n){this._ibtnGo_Debit!==n&&(this._ibtnGo_Debit=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.Orders.callBaseMethod(this,"initialize")},goInit:function(){this._strPathToData="controls/LeftNuggets/QuickJump";this._strDataObject="QuickJump";$R_IBTN.addClick(this._ibtnGo_SalesOrder,Function.createDelegate(this,this.goSalesOrder));$R_IBTN.addClick(this._ibtnGo_CusReq,Function.createDelegate(this,this.goCusReq));$R_IBTN.addClick(this._ibtnGo_Quote,Function.createDelegate(this,this.goQuote));$R_IBTN.addClick(this._ibtnGo_Invoice,Function.createDelegate(this,this.goInvoice));$R_IBTN.addClick(this._ibtnGo_PurchaseOrder,Function.createDelegate(this,this.goPurchaseOrder));$R_IBTN.addClick(this._ibtnGo_CRMA,Function.createDelegate(this,this.goCRMA));$R_IBTN.addClick(this._ibtnGo_SRMA,Function.createDelegate(this,this.goSRMA));$R_IBTN.addClick(this._ibtnGo_Credit,Function.createDelegate(this,this.goCredit));$R_IBTN.addClick(this._ibtnGo_Debit,Function.createDelegate(this,this.goDebit));$R_TXTBOX.addEnterPressedEvent(this._txtSalesOrderNo,Function.createDelegate(this,this.goSalesOrder));$R_TXTBOX.addEnterPressedEvent(this._txtCusReqNo,Function.createDelegate(this,this.goCusReq));$R_TXTBOX.addEnterPressedEvent(this._txtQuoteNo,Function.createDelegate(this,this.goQuote));$R_TXTBOX.addEnterPressedEvent(this._txtInvoiceNo,Function.createDelegate(this,this.goInvoice));$R_TXTBOX.addEnterPressedEvent(this._txtPurchaseOrderNo,Function.createDelegate(this,this.goPurchaseOrder));$R_TXTBOX.addEnterPressedEvent(this._txtCRMANo,Function.createDelegate(this,this.goCRMA));$R_TXTBOX.addEnterPressedEvent(this._txtSRMANo,Function.createDelegate(this,this.goSRMA));$R_TXTBOX.addEnterPressedEvent(this._txtCreditNo,Function.createDelegate(this,this.goCredit));$R_TXTBOX.addEnterPressedEvent(this._txtDebitNo,Function.createDelegate(this,this.goDebit));Rebound.GlobalTrader.Site.Pages.Orders.Orders.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ibtnGo_Sourcing&&$R_IBTN.clearHandlers(this._ibtnGo_Sourcing),this._ibtnGo_SalesOrder&&$R_IBTN.clearHandlers(this._ibtnGo_SalesOrder),this._ibtnGo_CusReq&&$R_IBTN.clearHandlers(this._ibtnGo_CusReq),this._ibtnGo_Quote&&$R_IBTN.clearHandlers(this._ibtnGo_Quote),this._ibtnGo_Invoice&&$R_IBTN.clearHandlers(this._ibtnGo_Invoice),this._ibtnGo_PurchaseOrder&&$R_IBTN.clearHandlers(this._ibtnGo_PurchaseOrder),this._ibtnGo_CRMA&&$R_IBTN.clearHandlers(this._ibtnGo_CRMA),this._ibtnGo_SRMA&&$R_IBTN.clearHandlers(this._ibtnGo_SRMA),this._ibtnGo_Credit&&$R_IBTN.clearHandlers(this._ibtnGo_Credit),this._ibtnGo_Debit&&$R_IBTN.clearHandlers(this._ibtnGo_Debit),this._ctlEllipses_CusReqs&&this._ctlEllipses_CusReqs.dispose(),this._ctlEllipses_Quotes&&this._ctlEllipses_Quotes.dispose(),this._ctlEllipses_SalesOrders&&this._ctlEllipses_SalesOrders.dispose(),this._ctlEllipses_Invoices&&this._ctlEllipses_Invoices.dispose(),this._ctlEllipses_PurchaseOrders&&this._ctlEllipses_PurchaseOrders.dispose(),this._ctlEllipses_CRMAs&&this._ctlEllipses_CRMAs.dispose(),this._ctlEllipses_SRMAs&&this._ctlEllipses_SRMAs.dispose(),this._ctlEllipses_PurReqs&&this._ctlEllipses_PurReqs.dispose(),this._ctlEllipses_Credits&&this._ctlEllipses_Credits.dispose(),this._ctlEllipses_Debits&&this._ctlEllipses_Debits.dispose(),this._txtCusReqNo&&$R_TXTBOX.clearEvents(this._txtCusReqNo),this._txtQuoteNo&&$R_TXTBOX.clearEvents(this._txtQuoteNo),this._txtSalesOrderNo&&$R_TXTBOX.clearEvents(this._txtSalesOrderNo),this._txtInvoiceNo&&$R_TXTBOX.clearEvents(this._txtInvoiceNo),this._txtPurchaseOrderNo&&$R_TXTBOX.clearEvents(this._txtPurchaseOrderNo),this._txtCRMANo&&$R_TXTBOX.clearEvents(this._txtCRMANo),this._txtSRMANo&&$R_TXTBOX.clearEvents(this._txtSRMANo),this._txtCreditNo&&$R_TXTBOX.clearEvents(this._txtCreditNo),this._txtDebitNo&&$R_TXTBOX.clearEvents(this._txtDebitNo),this._strPathToData=null,this._strDataObject=null,this._ibtnGo_SalesOrder=null,this._ibtnGo_CusReq=null,this._ibtnGo_Quote=null,this._ibtnGo_Invoice=null,this._ibtnGo_PurchaseOrder=null,this._ibtnGo_CRMA=null,this._ibtnGo_SRMA=null,this._ibtnGo_Credit=null,this._ibtnGo_Debit=null,this._ctlEllipses_CusReqs=null,this._txtCusReqNo=null,this._ctlEllipses_Quotes=null,this._txtQuoteNo=null,this._ctlEllipses_SalesOrders=null,this._txtSalesOrderNo=null,this._ctlEllipses_Invoices=null,this._txtInvoiceNo=null,this._ctlEllipses_PurchaseOrders=null,this._txtPurchaseOrderNo=null,this._ctlEllipses_CRMAs=null,this._txtCRMANo=null,this._ctlEllipses_SRMAs=null,this._txtSRMANo=null,this._ctlEllipses_PurReqs=null,this._ctlEllipses_Credits=null,this._txtCreditNo=null,this._ctlEllipses_Debits=null,this._txtDebitNo=null,Rebound.GlobalTrader.Site.Pages.Orders.Orders.callBaseMethod(this,"dispose"))},countRequirements:function(){var n=this._ctlEllipses_CusReqs._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountRequirements")},countSOs:function(){var n=this._ctlEllipses_SalesOrders._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountSOs")},countQuotes:function(){var n=this._ctlEllipses_Quotes._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountQuotes")},countInvoices:function(){var n=this._ctlEllipses_Invoices._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountInvoices")},countPOs:function(){var n=this._ctlEllipses_PurchaseOrders._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountPOs")},countCRMAs:function(){var n=this._ctlEllipses_CRMAs._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountCRMAs")},countSRMAs:function(){var n=this._ctlEllipses_SRMAs._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountSRMAs")},countPurReqs:function(){var n=this._ctlEllipses_PurReqs._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountPurReqs")},countCredits:function(){var n=this._ctlEllipses_Credits._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountCredits")},countDebits:function(){var n=this._ctlEllipses_Debits._objData;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("CountDebits")},goSourcing:function(){this._txtSourcingPartNo.value.trim().length<1||(location.href=$RGT_gotoURL_Sourcing(this._txtSourcingPartNo.value,!0))},goSalesOrder:function(){if(!(this._txtSalesOrderNo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetSalesOrderID");n.addParameter("No",this._txtSalesOrderNo.value.trim());n.addDataOK(Function.createDelegate(this,this.goSalesOrderComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goSalesOrderComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_SalesOrder(n._result.ID))},goQuote:function(){if(!(this._txtQuoteNo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetQuoteID");n.addParameter("No",this._txtQuoteNo.value.trim());n.addDataOK(Function.createDelegate(this,this.goQuoteComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goQuoteComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_Quote(n._result.ID))},goCusReq:function(){if(!(this._txtCusReqNo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetRequirementID");n.addParameter("No",this._txtCusReqNo.value.trim());n.addDataOK(Function.createDelegate(this,this.goCusReqComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goCusReqComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_CustomerRequirement(n._result.ID))},goPurchaseOrder:function(){if(!(this._txtPurchaseOrderNo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetPurchaseOrderID");n.addParameter("No",this._txtPurchaseOrderNo.value.trim());n.addDataOK(Function.createDelegate(this,this.goPurchaseOrderComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goPurchaseOrderComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_PurchaseOrder(n._result.ID))},goCRMA:function(){if(!(this._txtCRMANo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCRMAID");n.addParameter("No",this._txtCRMANo.value.trim());n.addDataOK(Function.createDelegate(this,this.goCRMAComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goCRMAComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_CRMA(n._result.ID))},goSRMA:function(){if(!(this._txtSRMANo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetSRMAID");n.addParameter("No",this._txtSRMANo.value.trim());n.addDataOK(Function.createDelegate(this,this.goSRMAComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goSRMAComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_SRMA(n._result.ID))},goInvoice:function(){if(!(this._txtInvoiceNo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetInvoiceID");n.addParameter("No",this._txtInvoiceNo.value.trim());n.addDataOK(Function.createDelegate(this,this.goInvoiceComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goInvoiceComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_Invoice(n._result.ID))},goCredit:function(){if(!(this._txtCreditNo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCreditID");n.addParameter("No",this._txtCreditNo.value.trim());n.addDataOK(Function.createDelegate(this,this.goCreditComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goCreditComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_CreditNote(n._result.ID))},goDebit:function(){if(!(this._txtDebitNo.value.trim().length<1)){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetDebitID");n.addParameter("No",this._txtDebitNo.value.trim());n.addDataOK(Function.createDelegate(this,this.goDebitComplete));$R_DQ.addToQueue(n);$R_DQ.processQueue()}},goDebitComplete:function(n){n._result.ID&&(location.href=$RGT_gotoURL_DebitNote(n._result.ID))}};Rebound.GlobalTrader.Site.Pages.Orders.Orders.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.Orders",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);