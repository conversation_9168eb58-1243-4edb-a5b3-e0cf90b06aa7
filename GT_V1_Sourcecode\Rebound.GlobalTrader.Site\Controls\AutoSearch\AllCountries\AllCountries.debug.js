///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries = function (element) {
    Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.prototype = {
    get_blnIncludeSelected: function () { return this._blnIncludeSelected; }, set_blnIncludeSelected: function (v) { if (this._blnIncludeSelected !== v) this._blnIncludeSelected = v; },
    get_intGlobalLoginClientNo: function () { return this._intGlobalLoginClientNo; }, set_intGlobalLoginClientNo: function (v) { if (this._intGlobalLoginClientNo !== v) this._intGlobalLoginClientNo = v; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.callBaseMethod(this, "initialize");
        this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
        this.setupDataObject("AllCountries");
    },

    dispose: function () {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.callBaseMethod(this, "dispose");
    },

    dataReturned: function () {
        if (!this._result) return;
        if (this._result.TotalRecords > 0) {
            for (var i = 0, l = this._result.Results.length; i < l; i++) {
                var res = this._result.Results[i];
                var strHTML = "";

                if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
                    strHTML = $R_FN.setCleanTextValue(res.Name);
                } else {
                    strHTML = $R_FN.setCleanTextValue(res.Name);
                }
                this.addResultItem(strHTML, $R_FN.setCleanTextValue(res.Name), res.ID);
                strHTML = null; res = null;
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.AllCountries", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
