﻿.main_container-enable {
    display: block;
}
.main_container-disable {
    display: none;
}

.close_icon {
    background-image: url('../../../images/Kub/AddNewRequirementPage/close_icon.svg');
    background-repeat: no-repeat;
    width: 16px;
    height: 16px;
    float: right;
    margin: 0px;
    cursor: pointer;
}

.table_content table {
    width: 100%;
    color: #fff;
}

    .table_content table thead th {
        background-color: #47863F;
        padding: 6px 0px;
        text-align: center;
    }

.table_content tbody tr:nth-child(even) {
    background: #EEFFEC
}

.table_content tbody tr:nth-child(odd) {
    background: #FFF
}

.table_content table tbody {
    color: #2D2D2D;
}

    .table_content table tbody tr td {
        padding: 6px 10px;
        font-size: 10px;
        font-weight: bold;
        font-family: Tahoma
    }

        .table_content table tbody tr td input {
            border: none;
            background-color: #488740;
            color: #fff;
            padding: 5px 10px;
            font-weight: bold;
            font-family: Tahoma;
            border-radius: 3px;
            letter-spacing: 1px;
        }


.accordion {
    background: none !important;
    color: #444;
    cursor: pointer;
    width: 96%;
    border: none;
    text-align: left;
    outline: none;
    font-size: 10px;
    transition: 0.4s;
    font-weight: bold;
    font-family: Tahoma;
    padding: 0px !important;
    margin-top: 3px;
}

.data_tb tr td a {
    background-repeat: no-repeat;
    background-position: left 2px;
    margin-left: 20px;
    padding-left: 13px;
    height: 12px;
    text-decoration: none;
    /*color: #5E6EFF;*/
}

#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_frmStep3 {
    display: none !important;
}

.accordion:after {
    content: ' View Details ';
    color: #5E6EFF;
    font-weight: bold;
    float: left;
    margin-left: 13px;
    background-image: url('../../../images/Kub/AddNewRequirementPage/icon_down.svg');
    background-position: 68px center;
    padding-right: 19px;
    background-size: 12px;
    background-repeat: no-repeat;
}

.active:after {
    content: " View Details ";
    background-image: url('../../../images/Kub/AddNewRequirementPage/icon_up.svg');
    background-position: right center;
    background-repeat: no-repeat;
}

.panel {
    padding: 0 10px;
    background-color: #EEEEEE;
    max-height: 0;
    overflow: hidden;
    overflow-y:auto;
    transition: max-height 0.2s ease-out;
    margin-left: -9px;
    width: 100%;
}

.text_left {
    width: 48%;
    float: left;
}

.text_right {
    width: 50.25%;
    float: right;
    text-align: left;
}

    .text_right span {
        font-size: 10px !important;
    }

.data_tb {
    padding: 5px 5px;
    margin: 10px 0px;
    border-collapse: collapse;
}

    .data_tb thead tr th {
        text-align: left;
        padding: 6px 10px;
    }

    .data_tb thead tr th, .data_tb tbody tr td {
        border-right: 1px dashed #BFBFBF;
    }

        /*.data_tb thead tr th:nth-child(3), .data_tb tbody tr td:nth-child(3) {
            border-right: none;
        }*/
        .data_tb thead tr th:nth-last-child(1), .data_tb tbody tr td:nth-last-child(1) {
            border-right: none;
        }

#more {
    display: none;
}

.read_btn {
    border: none;
    border-radius: 4px;
    color: #fff;
    background-color: #006600;
    margin-top: 8px;
    font-family: Tahoma;
    font-size: 11px;
    padding: 4px 8px 4px 4px;
}

.LastUpdate {
    color: #999999;
    font-size: 10px;
    font-style: italic;
    text-align: right;
    justify-content: flex-end;
    display: flex;
    width: 99%;
}

.documentachor {
    color: blue;
    margin-left: 10px;
    font-style: italic;
}

.actualCurrency {
    margin-left: 2%;
    color: green;
}

.no-left-spacing{
    margin-left: 0 !important;
    padding-left: 0 !important;
}


/*.Splitmodal {
    display: none;*/ /* Hidden by default */
    /*position: absolute;*/ /* Stay in place */
    /*z-index: 100013;*/ /* Sit on top */
    /*padding-top: 100px;*/ /* Location of the box */
    /*left: -1px;
    top: -134px;
    width: 101%;*/ /* Full width */
    /*height: 140%;*/ /* Full height */

    /*background-color: rgb(0,0,0);*/ /* Fallback color */
    /*background-color: rgba(0,0,0,0.9);*/ /* Black w/ opacity */
/*}*/

/* Modal Content */
/*.Splitmodal-content {
    background-color: #47863F;
    padding: 20px;
    border: 1px solid #47863F;
    width: 25%;
    border-radius: 6px;
    min-height: 160px;
    position: relative;
    left: 32%;
    top: 22%;
}

    .Splitmodal-content .close {
        color: #fff;
        float: right;
        font-size: 26px !important;
        font-weight: 600;
        opacity: 1 !important;
        margin-top: -10px;
    }

        .Splitmodal-content .close:hover,
        .Splitmodal-content .close:focus {
            color: #000;
            text-decoration: none;
            cursor: pointer;
        }

    .Splitmodal-content h4 {
        font-size: 14px;
        color: #fff;
        font-family: Tahoma;
    }

.lblBr {
    color: white;
    font-weight: bold;
}*/
