﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");
Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.initializeBase(this, [element]);
};
Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.prototype = {
    get_sectionID: function () { return this._sectionID; }, set_sectionID: function (value) { if (this._sectionID !== value) this._sectionID = value; },
    get_pnlPDFDocuments: function () { return this._pnlPDFDocuments; }, set_pnlPDFDocuments: function (value) { if (this._pnlPDFDocuments !== value) this._pnlPDFDocuments = value; },
    get_IsPDFAvailable: function () { return this._IsPDFAvailable; }, set_IsPDFAvailable: function (value) { if (this._IsPDFAvailable !== value) this._IsPDFAvailable = value; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.callBaseMethod(this, "initialize");
        this.addRefreshEvent(Function.createDelegate(this, this.showPDFPanel));
        this.getData();
    },
    dispose: function () {
        if (this.isDisposed) return;
        this._sectionID = null;
        this._pnlPDFDocuments = null;
        this._IsPDFAvailable = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.callBaseMethod(this, "dispose");
    },
    getData: function () {
        if (!this._IsPDFAvailable) { this.pdfNotAvailable(true); return; }
        this.getData_Start();
        this._intCountPDF == 0;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/InvoiceExportHistory");
        obj.set_DataObject("InvoiceExportHistory");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._sectionID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getDataOK: function (args) {
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlPDFDocuments, "");
        var iconpath = result.IconPath;
        var strPDF = "";
        if (result.Items) {
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];
                strPDF += "<div class=\"pdfDocument\">";
                strPDF += String.format("<a href=\"{0}\" target=\"_blank\"><img width=\"80px\" id=\"{1}_img{2}\" src=\"{3}\" border=\"0\" /></a><p class=\"pdfInvoiceExportDocuments\">{4}</p>"
                    , $R_FN.setCleanTextValue(row.FilePath), this._element.id, i
                    , row.FileType === "pdf" ? iconpath + "pdficon.jpg" : iconpath + "xmlicon.jpg"
                    , row.ActionType);
                strPDF += "<div class=\"pdfDocumentCaption\">";
                if (row.PrefixAction) strPDF += "[" + row.PrefixAction + "] " + row.ActionType + "." + row.FileType + "<br />";
                strPDF += row.Date;
                if (row.By) strPDF += "<br />" + row.By;
                strPDF += "</div>";
                strPDF += "</div>";
                row = null;
            }
            this._intCountPDF = result.Items.length;
        }
        $R_FN.setInnerHTML(this._pnlPDFDocuments, strPDF);
        this.getDataOK_End();
        this.showNoData(!this._IsPDFAvailable);
        this.showPanel(this._IsPDFAvailable)
    },
    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    pdfNotAvailable: function (bln) {
        if (bln) {
            $R_FN.setInnerHTML(this._pnlPDFDocuments, "");
            this.getDataOK_End();
            this.showNoData(true);
        }
    },
    showPDFPanel: function () {
        this._IsPDFAvailable = true;
        this.showPanel(true);
        this.getData();
    },
    showPanel: function (bln) {
        $R_FN.showElement(this._pnlPDFDocuments, bln);
    },
};
Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.InvoiceExportHistory", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
