//Marker     Changed by      Date               Remarks
//[001]      Vinay           11/06/2013         CR:- Supplier Invoice
//[002]      Soorya          03/03/2023   RP-1048 Remove AI code
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
//using Microsoft.ApplicationInsights; //[002]

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class SupplierInvoice : Base
    {

        private string _strCallType;

        protected override void GetData()
        {
            try
            {

                _strCallType = "ALL";
                if (GetFormValue_Boolean("CanNotBeExported")) _strCallType = "CANNOTBEEXPORTED";

                SupplierInvoiceStatus enmStatus = (SupplierInvoiceStatus)GetFormValue_Int("Status");
                bool ocr = GetFormValue_Boolean("OCR");
                List<BLL.SupplierInvoice> lst = BLL.SupplierInvoice.DataListNugget(
                    SessionManager.ClientID
                    , GetFormValue_NullableInt("SortIndex")
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    , GetFormValue_NullableInt("PageSize", 10)
                    , GetFormValue_String("SupplierInvoiceNo")
                    , GetFormValue_NullableInt("URNNoLo")
                    , GetFormValue_NullableInt("URNNoHi")
                    , GetFormValue_NullableInt("PONoLo")
                    , GetFormValue_NullableInt("PONoHi")
                    , GetFormValue_NullableInt("GINoLo")
                    , GetFormValue_NullableInt("GINoHi")
                    , GetFormValue_NullableDateTime("SupplierInvoiceDateFrom")
                    , GetFormValue_NullableDateTime("SupplierInvoiceDateTo")
                    //, GetFormValue_StringForNameSearch("CMName")
                    , GetFormValue_StringForNameSearchDecode("CMName")
                    , GetFormValue_Boolean("RecentOnly")
                    , (enmStatus == SupplierInvoiceStatus.ExportedOnly && _strCallType == "ALL") ? (bool?)true : null
                    , (enmStatus == SupplierInvoiceStatus.ApproveandUnExported && _strCallType == "ALL") ? (bool?)true : null
                    , GetFormValue_NullableBoolean("CanNotBeExported", null)
                    , GetFormValue_NullableInt("IPONoLo")
                    , GetFormValue_NullableInt("IPONoHi")
                    , GetFormValue_NullableInt("Reason")
                    , GetFormValue_Boolean("OCR")
                    , GetFormValue_Boolean("Descrepancy")
                );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
                JsonObject jsnRowsArray = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ID", lst[i].SupplierInvoiceID);
                        jsnRow.AddVariable("No", lst[i].SupplierInvoiceNumber);
                        jsnRow.AddVariable("Name", lst[i].SupplierName);
                        jsnRow.AddVariable("CompanyNo", lst[i].CompanyNo);
                        jsnRow.AddVariable("GINo", lst[i].GoodsInNumber);
                        jsnRow.AddVariable("GI", lst[i].GoodsInNo);
                        jsnRow.AddVariable("PO", lst[i].PurchaseOrderNumber);
                        jsnRow.AddVariable("PONo", lst[i].PurchaseOrderNo);
                        jsnRow.AddVariable("INVDate", Functions.FormatDate(lst[i].SupplierInvoiceDate));
                        jsnRow.AddVariable("Part", lst[i].Part);
                        jsnRow.AddVariable("Value", Functions.FormatCurrency(lst[i].InvoiceAmount, lst[i].CurrencyCode, 5));
                        jsnRow.AddVariable("URNNumber", (lst[i].URNNumber.HasValue) ? Convert.ToString(lst[i].URNNumber.Value) : "");
                        jsnRow.AddVariable("HasIPO", lst[i].InternalPurchaseOrderId.HasValue);
                        jsnRow.AddVariable("CanBeExported", lst[i].CanbeExported);
                        jsnRow.AddVariable("ItemCount", lst[i].ItemCount);
                        jsnRow.AddVariable("MatchPercentage", lst[i].MatchPercentage);
                        jsnRowsArray.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose();
                jsnRowsArray = null;
                jsn.Dispose();
                jsn = null;
                lst = null;
                base.GetData();
            }
            catch (Exception e)
            {
                //[002] 
                //var ai = new TelemetryClient();

                //ai.TrackTrace("SupplierInvoice: GetData");
                //ai.TrackException(e);
                new Errorlog().LogMessage("Error at GetData in SupplierInvoice.ashx.cs : " + e.Message);
                WriteError(e);
                throw;
            }
        }

        protected override void AddFilterStates()
        {
            AddExplicitFilterState("CallType", _strCallType);
            AddFilterState("SupplierInvoiceNo");
            AddFilterState("URNNo");
            AddFilterState("PONo");
            AddFilterState("GINo");
            AddFilterState("SupplierInvoiceDateFrom");
            AddFilterState("SupplierInvoiceDateTo");
            AddFilterState("CMName");
            AddFilterState("RecentOnly");
            AddFilterState("Status");
            AddFilterState("IPONo");
            base.AddFilterStates();
        }

    }
}
