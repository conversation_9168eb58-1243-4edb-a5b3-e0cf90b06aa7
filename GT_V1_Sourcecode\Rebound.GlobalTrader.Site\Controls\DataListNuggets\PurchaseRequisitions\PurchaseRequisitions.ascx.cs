using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class PurchaseRequisitions : Base {

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("PurchaseRequisitions");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "PurchaseRequisitions");
			AddScriptReference("Controls.DataListNuggets.PurchaseRequisitions.PurchaseRequisitions.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseRequisitions", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
            string strViewLevel = this.GetSavedStateValue("ViewLevel");
            if (!string.IsNullOrEmpty(strViewLevel)) {
                ((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
                _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
                this.OnAskPageToChangeTab();
            }
            base.RenderAdditionalState();
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue), true));
			_tbl.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			_tbl.Columns.Add(new FlexiDataColumn("DatePromised", Unit.Empty, true));
		}

	}
}