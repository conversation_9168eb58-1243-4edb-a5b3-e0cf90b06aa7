<%--
Marker     changed by      date         Remarks
[001]      Umendra         18/01/2019    Adding View Tree Button
--%>
<%@ Control Language="C#" CodeBehind="CreditMainInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.CreditMainInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" TitleText="Main Information" BoxType="Standard">

	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" />
        <ReboundUI:IconButton ID="ibtnExport" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Export" IconCSSType="Export" />
		<ReboundUI:IconButton ID="ibtnRelease" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="Release" IconCSSType="Release" />
	   <%--[001] code Start--%>
        <ReboundUI:IconButton ID="ibtnViewTree" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="ViewTree" IconCSSType="Add" />
    	<%--[001] code End--%>
    </Links>
	
	<Content>
		<table class="twoCols">
			<tr>
				<td class="col1">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlCustomerName" runat="server" ResourceTitle="Customer" />
						<ReboundUI:DataItemRow id="ctlContact" runat="server" ResourceTitle="Contact" />
						<ReboundUI:DataItemRow id="ctlDivision" runat="server" ResourceTitle="Division" />
						<ReboundUI:DataItemRow id="ctlDivisionHeader" runat="server" ResourceTitle="DivisionHeader" />
						<ReboundUI:DataItemRow id="hidDivisionHeaderNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlSalesperson" runat="server" ResourceTitle="Salesperson" />
						<ReboundUI:DataItemRow id="hidSalespersonNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlSalesperson2" runat="server" ResourceTitle="Salesperson2" />
						<ReboundUI:DataItemRow id="hidSalesperson2No" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidSalesman2Percent" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="ctlRaiser" runat="server" ResourceTitle="RaisedBy" />

						<ReboundUI:DataItemRow id="ctlSalesDivision" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidSalesDivision" runat="server" FieldType="Hidden" />
                        

						<ReboundUI:DataItemRow id="ctlSep1" runat="server" FieldType="SeparatorWithLine" />
						<ReboundUI:DataItemRow id="ctlCreditDate" runat="server" ResourceTitle="CreditDate" />
						<ReboundUI:DataItemRow id="ctlReferenceDate" runat="server" ResourceTitle="ReferenceDate" />
						<ReboundUI:DataItemRow id="ctlInvoice" runat="server" ResourceTitle="InvoiceNo" />
                        <ReboundUI:DataItemRow id="ctlClientInvoice" runat="server" ResourceTitle="ClientInvoiceNo" />
						<ReboundUI:DataItemRow id="ctlSalesOrder" runat="server" ResourceTitle="SalesOrderNo" />
						<ReboundUI:DataItemRow id="ctlCustomerRMA" runat="server" ResourceTitle="CustomerRMANo" />

						<ReboundUI:DataItemRow id="ctlSep3" runat="server" FieldType="SeparatorWithLine" />
						<ReboundUI:DataItemRow id="ctlCustomerPO" runat="server" ResourceTitle="CustomerPO" />
						<ReboundUI:DataItemRow id="ctlCustomerReturn" runat="server" ResourceTitle="CustomerReturn" />
						<ReboundUI:DataItemRow id="ctlCustomerDebit" runat="server" ResourceTitle="CustomerDebit" />
						<ReboundUI:DataItemRow id="hidCreditNumber" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidSalesOrderNumber" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidInvoiceNumber" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCRMANumber" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCustomerName" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCurrencyNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCurrencyCode" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidFreight" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidDivisionNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidContactName" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidSalesmanNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidRaiserNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidTaxNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidShipViaNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidFreightRaw" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidShippingCostRaw" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidIncotermNo" runat="server" FieldType="Hidden" />
					</table>
				</td>
				<td class="col2">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlTax" runat="server" ResourceTitle="Tax" />
						<ReboundUI:DataItemRow id="ctlCurrency" runat="server" ResourceTitle="Currency" />

						<ReboundUI:DataItemRow id="ctlSep4" runat="server" FieldType="SeparatorWithLine" />
						<ReboundUI:DataItemRow id="ctlShipVia" runat="server" ResourceTitle="ShipVia" />
						<ReboundUI:DataItemRow id="ctlIncoterm" runat="server" ResourceTitle="Incoterm" />
					    <ReboundUI:DataItemRow id="ctlShippingAccountNo" runat="server" ResourceTitle="ShippingAccountNo" />
						<ReboundUI:DataItemRow id="ctlShippingCost" runat="server" ResourceTitle="ShippingCost" />
						<ReboundUI:DataItemRow id="ctlFreight" runat="server" ResourceTitle="Freight" />
						

						<ReboundUI:DataItemRow id="ctlSep5" runat="server" FieldType="SeparatorWithLine" />
						<ReboundUI:DataItemRow id="ctlNotes" runat="server" ResourceTitle="CustomerNotes" />
						<ReboundUI:DataItemRow id="ctlInstructions" runat="server" ResourceTitle="Instructions" />
							<ReboundUI:DataItemRow id="ctlCreditNoteBankFee" runat="server" ResourceTitle="BankFee" />
							<ReboundUI:DataItemRow id="ctlRefNo" runat="server" ResourceTitle="RefNo" />
                        <ReboundUI:DataItemRow id="hidIsClientInvoice" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="hidClientInvoiceLineNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="hidHubLogin" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="ctlExchangeRate" runat="server" ResourceTitle="ExchangeRate" />
					</table>
				</td>
			</tr>
		</table>
	</Content>
	
	<Forms>
		<ReboundForm:CreditMainInfo_Edit ID="ctlCreditEdit" runat="server" />
        <ReboundForm:CreditMainInfo_Export ID="frmExport" runat="server" />
	</Forms>

</ReboundUI_Nugget:DesignBase>
