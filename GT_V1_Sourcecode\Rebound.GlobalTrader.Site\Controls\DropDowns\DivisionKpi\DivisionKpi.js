Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.prototype={get_intSelClientNo:function(){return this._intSelClientNo},set_intSelClientNo:function(n){this._intSelClientNo!==n&&(this._intSelClientNo=n)},get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._intSelClientNo=null,this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/DivisionKpi");this._objData.set_DataObject("DivisionKpi");this._objData.set_DataAction("GetData");this._objData.addParameter("intSelClientNo",this._intSelClientNo);this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.DivisionKpis)for(n=0;n<t.DivisionKpis.length;n++)this.addOption(t.DivisionKpis[n].Name,t.DivisionKpis[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.DivisionKpi",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);