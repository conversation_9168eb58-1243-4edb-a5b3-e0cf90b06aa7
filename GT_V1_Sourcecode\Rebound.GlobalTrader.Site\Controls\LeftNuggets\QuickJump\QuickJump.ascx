<%@ Control Language="C#" CodeBehind="QuickJump.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_LeftNugget:DesignBase ID="ctlDB" runat="server" IconCssType="Search">
    <Content>
        <div class="leftNuggetField">
            <div class="item">
                <ReboundUI:ReboundTextBox ID="txt" runat="server" Width="140" UppercaseOnly="true" CssClass="field" />
                &nbsp;<ReboundUI:IconButton ID="ibtnGo" runat="server" IconGroup="LeftNugget" IconButtonMode="Hyperlink" Href="javascript:void(0);" IconTitleResource="Go" Alignment="Right" />
              <%--  <br />
                <ReboundUI:IconButton ID="ibtnShowRelDoc" runat="server" IconGroup="LeftNugget" IconButtonMode="Hyperlink" Href="javascript:void(0);" IconTitleResource="ShowRelDocument" Alignment="Right" />--%>
            </div>
        </div>
        <asp:Panel ID="pnlQJSearchResultTooltip" runat="server" CssClass="topMenuRollovers invisible" Style="opacity: 1; position: absolute; width: 145px; min-height: 30px;">
            <div class="topMenuRolloversContent">
                <asp:Panel ID="pnlQJSearchResult" CssClass="leftNuggetQuickJumpContent" runat="server"></asp:Panel>
            </div>
            <div class="curveBL">&nbsp;</div>
            <div class="curveBR">&nbsp;</div>
        </asp:Panel>
        <asp:Panel ID="pnlNotFound" CssClass="leftNuggetNotFound invisible" runat="server"><%=Functions.GetGlobalResource("NotFound", "Document")%></asp:Panel>
        <div class="leftNuggetField">
            <div class="item">
                <asp:RadioButtonList ID="rad" runat="server" RepeatLayout="Table" RepeatColumns="2" RepeatDirection="Vertical" />
            </div>
        </div>
    </Content>


</ReboundUI_LeftNugget:DesignBase>
