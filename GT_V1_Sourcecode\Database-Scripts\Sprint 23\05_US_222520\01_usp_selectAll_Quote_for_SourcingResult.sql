﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-222520]		An.TranTan			11-Mar-2025		Update			Get additional data of quote for Task creation in HUBRFQ detail screen
===========================================================================================
*/
CREATE OR ALTER PROC [dbo].[usp_selectAll_Quote_for_SourcingResult]
    @SourcingResultNo INT
AS
    SELECT  DISTINCT qt.QuoteId
          , qt.QuoteNumber
		  , qs.[Name] as QuoteStatusName
		  , c.CompanyName
		  , (SELECT COUNT(*) FROM tbToDo WHERE QuoteNo = qt.QuoteId) AS TaskCount 
		  , CASE WHEN EXISTS(SELECT TOP 1 1 FROM tbToDo WHERE QuoteNo = qt.QuoteId AND IsComplete = 0) THEN 1 ELSE 0 END AS HasUnFinishedTask 
    FROM    dbo.tbQuoteLine ql WITH(NOLOCK)
    JOIN    dbo.tbQuote qt WITH(NOLOCK) ON ql.QuoteNo = qt.QuoteId
	JOIN	dbo.tbCompany c WITH(NOLOCK) on c.CompanyId = qt.CompanyNo
	JOIN	dbo.tbQuoteStatus qs WITH(NOLOCK) on qs.QuoteStatusId = qt.QuoteStatus
    WHERE ql.SourcingResultNo = @SourcingResultNo
GO
