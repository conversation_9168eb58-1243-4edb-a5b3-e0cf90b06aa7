Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.initializeBase(this,[n]);this._intAddressID=-1;this._blnAllowNonEntry=!1};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.prototype={get_blnAllowNonEntry:function(){return this._blnAllowNonEntry},set_blnAllowNonEntry:function(n){this._blnAllowNonEntry!==n&&(this._blnAllowNonEntry=n)},get_blnRequireFields:function(){return this._blnRequireFields},set_blnRequireFields:function(n){this._blnRequireFields!==n&&(this._blnRequireFields=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._intAddressID=null,this._blnAllowNonEntry=null,this._blnRequireFields=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.callBaseMethod(this,"dispose"))},validateFields:function(){var n=!0;return this.resetFields(),this._blnRequireFields&&(this.addressHasBeenEntered()||!this._blnAllowNonEntry)&&(this.checkEnteredAddressField("ctlAddressName")||(n=!1),this.checkEnteredAddressField("ctlLine1")||(n=!1),this.checkEnteredAddressField("ctlTown")||(n=!1),this.checkEnteredAddressField("ctlCountry")||(n=!1)),n},checkEnteredAddressField:function(n){var t=!0;return this._ctlRelatedForm.checkFieldEntered(n)||(this._ctlRelatedForm.setFieldInError(n,!0,$R_RES.RequiredFieldMissingMessage),t=!1),t},resetFields:function(){this._ctlRelatedForm.resetFieldError("ctlAddressName");this._ctlRelatedForm.resetFieldError("ctlLine1");this._ctlRelatedForm.resetFieldError("ctlLine2");this._ctlRelatedForm.resetFieldError("ctlLine3");this._ctlRelatedForm.resetFieldError("ctlTown");this._ctlRelatedForm.resetFieldError("ctlCounty");this._ctlRelatedForm.resetFieldError("ctlState");this._ctlRelatedForm.resetFieldError("ctlCountry");this._ctlRelatedForm.resetFieldError("ctlPostcode")},setFieldsToDefaults:function(){this._ctlRelatedForm.setFieldToDefault("ctlAddressName");this._ctlRelatedForm.setFieldToDefault("ctlLine1");this._ctlRelatedForm.setFieldToDefault("ctlLine2");this._ctlRelatedForm.setFieldToDefault("ctlLine3");this._ctlRelatedForm.setFieldToDefault("ctlTown");this._ctlRelatedForm.setFieldToDefault("ctlCounty");this._ctlRelatedForm.setFieldToDefault("ctlState");this._ctlRelatedForm.setFieldToDefault("ctlCountry");this._ctlRelatedForm.setFieldToDefault("ctlPostcode")},addressHasBeenEntered:function(){var n=!1;return this._ctlRelatedForm.getFieldValue("ctlAddressName").length>0&&(n=!0),this._ctlRelatedForm.getFieldValue("ctlLine1").length>0&&(n=!0),this._ctlRelatedForm.getFieldValue("ctlLine2").length>0&&(n=!0),this._ctlRelatedForm.getFieldValue("ctlLine3").length>0&&(n=!0),this._ctlRelatedForm.getFieldValue("ctlTown").length>0&&(n=!0),this._ctlRelatedForm.getFieldValue("ctlCounty").length>0&&(n=!0),this._ctlRelatedForm.getFieldValue("ctlState").length>0&&(n=!0),this._ctlRelatedForm.getFieldControl("ctlCountry").isSetAsNoValue()||(n=!0),this._ctlRelatedForm.getFieldValue("ctlPostcode").length>0&&(n=!0),n}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.Address",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);