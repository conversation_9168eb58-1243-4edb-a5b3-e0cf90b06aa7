using System;
using System.Web.Services;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data
{
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class SourcingBulkEditLog : Rebound.GlobalTrader.Site.Data.ItemSearch.Base
	{

		protected override void GetData()
		{
			try
			{
				List<SourcingAuditLog> lst = SourcingAuditLog.GetBulkEditLog(
					GetFormValue_NullableInt("Order", 0),
					GetFormValue_NullableInt("SortDir", SortColumnDirection.DESC),
					GetFormValue_NullableInt("PageIndex", 0),
					GetFormValue_NullableInt("PageSize", 10),
					GetFormValue_String("SourcingType"),
					GetFormValue_StringForPartSearch("PartNo"),
					GetFormValue_NullableInt("EditedBy"),
					GetFormValue_NullableDateTime("EditedDateFrom"),
					GetFormValue_NullableDateTime("EditedDateTo")
				);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
                foreach (var ln in lst)
                {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", ln.SourcingAuditLogId);
					jsnItem.AddVariable("BatchNo", ln.BatchId);
					jsnItem.AddVariable("PartNo", ln.PartNo);
					jsnItem.AddVariable("Action", ln.Action);
					jsnItem.AddVariable("OldValue", ln.OldValue);
					jsnItem.AddVariable("UpdatedBy", ln.UpdatedBy);
					jsnItem.AddVariable("UpdatedDate", Functions.FormatDate(ln.UpdatedDate));
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose();
					jsnItem = null;
				}
				
				jsn.AddVariable("Count", lst.Count > 0 ? lst[0].TotalCount : 0);
				jsn.AddVariable("Results", jsnItems);
				OutputResult(jsn);
				jsnItems.Dispose();
				jsnItems = null;
				jsn.Dispose();
				jsn = null;
			}
			catch (Exception ex)
			{
				WriteError(ex);
			}
			base.GetData();
		}
	}
}
