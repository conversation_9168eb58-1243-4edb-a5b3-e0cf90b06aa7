<%@ Control Language="C#" CodeBehind="CreditLines_Confirm.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Confirm" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">		     
						
			<ReboundUI_Form:FormField id="frmConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmation" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>		 
	</Content>
</ReboundUI_Form:DesignBase>
