Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intCurrencyID=-1;this._intCompanyID=-1;this._blnReqValidated=!0;this._ctlCompany="";this._strCustomerRequirementNumber=""};Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.prototype={get_intCustomerRequirementID:function(){return this._intCustomerRequirementID},set_intCustomerRequirementID:function(n){this._intCustomerRequirementID!==n&&(this._intCustomerRequirementID=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intCustomerRequirementID=null,this._intCurrencyID=-0,this._intCompanyID=null,this._ctlCompany="",this._strCustomerRequirementNumber="",Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)));this.getFieldControl("ctlBOMHeader")._intCompanyID=this._intCompanyID;this.getFieldDropDownData("ctlBOMHeader");$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_lblCustomer").text(this._ctlCompany);$("#ctl00_cphMain_ctlMainInfo_ctlDB_ctl14_ctlCloneHUBRFQ_ctlDB_lblCustReqNo").text(this._strCustomerRequirementNumber);$addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl23_ibtnBack_hyp"),"click",Function.createDelegate(this,this.noClicked1));$addHandler(document.getElementById("ctl00_cphMain_ctlMainInfo_ctlDB_ctl24_ibtnBack_hyp"),"click",Function.createDelegate(this,this.noClicked1))},noClicked1:function(){this.onNotConfirmed()},yesClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CusReqMainInfo");n.set_DataObject("CusReqMainInfo");n.set_DataAction("CloneRequirementDataHUBRFQ");n.addParameter("id",this._intCustomerRequirementID);n.addParameter("HUBRFQId",this.getFieldValue("ctlBOMHeader"));n.addDataOK(Function.createDelegate(this,this.saveCloneHUBRFQComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()}},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},validateForm:function(){this.onValidate();var n=!0;return this.checkFieldEntered("ctlBOMHeader")||(n=!1),this._blnReqValidated==!1&&(n=!1,this.showError(!0,"Some mandatory data is missing from this requirement. Please go back and fill in the missing data.")),n},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveCloneHUBRFQComplete:function(n){var t=n._result;n._result.Result>0?(this.showSavedOK(!0),location.href=$RGT_gotoURL_CustomerRequirement(n._result.Result)):this.showError(!0,"Error!!")}};Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CloneHUBRFQ",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);