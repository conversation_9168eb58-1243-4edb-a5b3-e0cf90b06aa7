Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.initializeBase(this,[n]);this._intLineID=0;this._intRequirementLineID=0;this._strCompanyName="";this._intBOMID=0};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_ctlReqsWithBOM:function(){return this._ctlReqsWithBOM},set_ctlReqsWithBOM:function(n){this._ctlReqsWithBOM!==n&&(this._ctlReqsWithBOM=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlReqsWithBOM&&this._ctlReqsWithBOM.dispose(),this._trSourceFromRequirement=null,this._ctlReqsWithBOM=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveAdd)),this._ctlReqsWithBOM.addItemSelected(Function.createDelegate(this,this.selectRequirementItem)),this._ctlReqsWithBOM._intBOMID=this._intBOMID,this._strPathToData="controls/Nuggets/BOMItems",this._strDataObject="BOMItems");this._ctlReqsWithBOM._tblResults.clearTable();this._ctlReqsWithBOM.resizeColumns();this._ctlReqsWithBOM.setFieldValue("ctlDateReceivedFrom",$R_FN.oneWeekAgo());this._ctlReqsWithBOM.setFieldValue("ctlDateReceivedTo",$R_FN.shortDate());this._ctlReqsWithBOM.searchClicked()},loadDropDowns:function(){this.getFieldDropDownData("ctlROHS")},selectRequirementItem:function(){this._intRequirementLineID=this._ctlReqsWithBOM.getSelectedID();this.continueClicked()},validateForm:function(){this.onValidate();var n=!0;return this._ctlReqsWithBOM._tblResults._aryCurrentValues.length==0&&(n=!1,this.showError(!0,"Please select any line")),n||this.showError(!0),n},saveAdd:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("Update");n.addParameter("BomId",this._intBOMID);n.addParameter("ReqIds",$R_FN.arrayToSingleString(this._ctlReqsWithBOM._tblResults._aryCurrentValues,","));n.addDataOK(Function.createDelegate(this,this.saveAddOK));n.addError(Function.createDelegate(this,this.saveAddError));n.addTimeout(Function.createDelegate(this,this.saveAddError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveAddError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveAddOK:function(n){n._result.Result==!0?(this._intLineID=n._result.NewID,this.onSaveComplete()):this.saveEditError(n)}};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);