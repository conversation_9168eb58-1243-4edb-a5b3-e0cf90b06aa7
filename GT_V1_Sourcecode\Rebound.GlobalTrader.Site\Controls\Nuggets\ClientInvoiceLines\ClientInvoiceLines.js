Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.initializeBase(this,[n]);this._intLineID=-1;this._intLineCount=0;this._blnLineLoaded=!1;this._floatTotal=0;this._aryAddedGoodsInLineIds=[];this._blnExported=!1;this._intInvoiceClientNo=-1;this._isPOHUb=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.prototype={get_intSIID:function(){return this._intSIID},set_intSIID:function(n){this._intSIID!==n&&(this._intSIID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},get_intCompanyNo:function(){return this._intCompanyNo},set_intCompanyNo:function(n){this._intCompanyNo!==n&&(this._intCompanyNo=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnEdit!==n&&(this._ibtnAdd=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_tblAll:function(){return this._tblAll},set_tblAll:function(n){this._tblAll!==n&&(this._tblAll=n)},get_pnlSummary:function(){return this._pnlSummary},set_pnlSummary:function(n){this._pnlSummary!==n&&(this._pnlSummary=n)},get_lblTotal:function(){return this._lblTotal},set_lblTotal:function(n){this._lblTotal!==n&&(this._lblTotal=n)},get_ibtnPrint:function(){return this._ibtnPrint},set_ibtnPrint:function(n){this._ibtnPrint!==n&&(this._ibtnPrint=n)},get_blnPOHub:function(){return this._blnPOHub},set_blnPOHub:function(n){this._blnPOHub!==n&&(this._blnPOHub=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.callBaseMethod(this,"initialize");this._strDataPath="controls/Nuggets/ClientInvoiceLines";this._strDataObject="ClientInvoiceLines";this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tblAll.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[1]),this._frmDelete.addCancel(Function.createDelegate(this,this.hideDeleteForm)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.deleteComplete)),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.hideDeleteForm)));this._ibtnPrint&&$R_IBTN.addClick(this._ibtnPrint,Function.createDelegate(this,this.printPackingSlip))},dispose:function(){this.isDisposed||(this._hypPrev&&$clearHandlers(this._hypPrev),this._hypNext&&$clearHandlers(this._hypNext),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._frmAdd&&this._frmAdd.dispose(),this._frmAdd=null,this._ibtnAdd=null,this._ibtnDelete=null,this._tblAll=null,this._pnlSummary=null,this._lblTotal=null,this._intSIID=null,this._intLineID=null,this._blnLineLoaded=null,this._floatTotal=null,this._aryAddedGoodsInLineIds=null,this._intCompanyNo=null,this._blnExported=null,this._ibtnPrint&&$R_IBTN.clearHandlers(this._ibtnPrint),this._ibtnPrint=null,this._blnPOHub=null,Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.callBaseMethod(this,"dispose"))},getData:function(){this.enableButtons(!1);this._blnLineLoaded=!1;this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines");n.addParameter("id",this._intSIID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var i,r,t,u;if(this.showLoading(!1),this._tblAll.clearTable(),i=n._result,this._floatTotal=0,Array.clear(this._aryAddedGoodsInLineIds),i.Lines)for(r=0;r<i.Lines.length;r++)t=i.Lines[r],u=this._blnPOHub==!0?[$RGT_nubButton_GoodsIn(t.GoodsInNo,t.GoodsInNumber),$R_FN.writeDoubleCellValue($RGT_nubButton_PurchaseOrder(t.PONo,t.PO),$RGT_nubButton_InternalPurchaseOrder(t.IPONo,t.IPO)),$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(t.StockNo,t.Part,t.ROHS),$R_FN.setCleanTextValue(t.SupplierPart)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Manufacturer),$R_FN.setCleanTextValue(t.DateCode)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.PackageName)),$R_FN.setCleanTextValue(t.DateReceived),$R_FN.writeDoubleCellValue(t.QtyReceived,t.UnitPrice),t.LineTotal]:[$RGT_nubButton_GoodsIn(t.GoodsInNo,t.GoodsInNumber),$R_FN.writeDoubleCellValue(t.PO,$RGT_nubButton_InternalPurchaseOrder(t.IPONo,t.IPO)),$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(t.StockNo,t.Part,t.ROHS),$R_FN.setCleanTextValue(t.SupplierPart)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Manufacturer),$R_FN.setCleanTextValue(t.DateCode)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.PackageName)),$R_FN.setCleanTextValue(t.DateReceived),$R_FN.writeDoubleCellValue(t.QtyReceived,t.UnitPrice),t.LineTotal],this._tblAll.addRow(u,t.ClientInvoiceLineId,t.ClientInvoiceLineId==this._intLineID),Array.add(this._aryAddedGoodsInLineIds,t.GoodsInLineNo),t=null;$R_FN.setInnerHTML(this._lblTotal,i.Total);this._floatTotal=i.TotalValue;this._isPOHUb=i.IsPOHub;this.showContent(!0);this.showContentLoading(!1);this._blnLineLoaded=!0;this._tblAll.resizeColumns();this.enableButtons(!0);this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,i.Lines&&i.Lines.length>0&&this._intLineID>0&&!this._blnExported)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableButtons:function(n){n?(this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,this._blnLineLoaded&&!this._blnExported&&this._isPOHUb),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,this._blnLineLoaded&&this._intLineCount>0&&!this._blnExported&&this._isPOHUb)):(this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!1),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1))},tbl_SelectedIndexChanged:function(){this._intLineID=this._tblAll._varSelectedValue;this._intLineCount=this._tblAll.countRows();this.enableButtons(!0)},showAddForm:function(){this._frmAdd._intClientInvoiceID=this._intSIID;this._frmAdd._intCompanyID=this._intCompanyNo;this._frmAdd._floatLineTotal=this._floatTotal;this._frmAdd._aryAddedGoodsInLineIds=this._aryAddedGoodsInLineIds;this._frmAdd._intInvoiceClientNo=this._intInvoiceClientNo;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1);this._tblAll.resizeColumns()},saveAddComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},showDeleteForm:function(){this._frmDelete._intLineID=this._intLineID;this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1);this._tblAll.resizeColumns()},deleteComplete:function(){this.hideDeleteForm();this._intLineID=-1;this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},printPackingSlip:function(){$R_FN.openPrintWindow($R_ENUM$PrintObject.PackingSlip,this._intInvoiceID)}};Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceLines",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);