﻿//Marker     Changed by      Date               Remarks
//[001]      Vinay           24/06/2013         CR:- Supplier Invoice
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
    public partial class PurchaseTaxCode : Base
    {

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("PurchaseTaxCode");
            AddScriptReference("Controls.DropDowns.PurchaseTaxCode.PurchaseTaxCode");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.PurchaseTaxCode", ClientID);
			base.OnLoad(e);
		}

	}
}