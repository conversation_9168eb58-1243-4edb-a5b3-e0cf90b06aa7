using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:StarRating runat=server></{0}:StarRating>")]
	public class StarRating : WebControlWithScript, IScriptControl, INamingContainer {

		#region Locals

		#endregion

		#region Properties

		private int _intMaxRating = 5;
		public object MaxRating {
			get { return _intMaxRating; }
			set {
				if (value == null) value = "0";
				string str = value.ToString();
				if (str == "") str = "0";
				_intMaxRating = int.Parse(str);
			}
		}

		private int _intCurrentRating;
		public object CurrentRating {
			get { return _intCurrentRating; }
			set {
				if (value == null) value = "0";
				string str = value.ToString();
				if (str == "") str = "0";
				_intCurrentRating = int.Parse(str);
			}
		}

		private bool _blnReadOnly;
		public bool ReadOnly {
			get { return _blnReadOnly; }
			set { _blnReadOnly = value; }
		}

		#endregion


		#region Overrides


		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			((Pages.Base)Page).AddCSSFile("StarRating.css");
		}

		protected override void CreateChildControls() {
			CssClass = "starsOuter";
			if (_blnReadOnly) CssClass += " starsOuterReadOnly";
			for (int i = 0; i <= _intMaxRating; i++) {
				Label lblStar = new Label();
				lblStar.ID = string.Format("star{0}", i);
				lblStar.CssClass = "stars starsEmpty";
				if (!_blnReadOnly) {
					if (i != _intMaxRating) lblStar.Attributes["onmouseover"] = string.Format("$find('{0}').starMouseOver({1})", ClientID, i);
					lblStar.Attributes["onclick"] = string.Format("$find('{0}').starClick({1})", ClientID, i);
				}
				if (i < _intCurrentRating) lblStar.CssClass = "stars starsSaved";
				if (i == _intMaxRating) lblStar.CssClass = "stars starsZero";
				ControlBuilders.CreateImageInsideParent(lblStar, "", "~/images/x.gif", 13, 15);
				Controls.Add(lblStar);
			}

			base.CreateChildControls();
		}

		#endregion

		#region IScriptControl Members

		protected new virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.StarRating.StarRating", true) };
		}

		protected new virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.StarRating", this.ClientID);
			descriptor.AddProperty("intMaxRating", _intMaxRating);
			descriptor.AddProperty("intInitialRating", _intCurrentRating);
			descriptor.AddProperty("blnReadOnly", _blnReadOnly);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion

	}
}