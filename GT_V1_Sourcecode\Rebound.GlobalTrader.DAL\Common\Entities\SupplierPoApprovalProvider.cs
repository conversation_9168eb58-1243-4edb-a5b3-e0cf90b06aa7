﻿/* Marker     changed by      date         Remarks  
[001]      A<PERSON><PERSON><PERSON>     25-Aug-2021   Add for Supplier Po Approval.
[002]      Ab<PERSON>av <PERSON>a     28-Oct-2021   Add new method for logging T&C email.
[003]      Abhinav <PERSON>     04-Jan-2022   Add subject in Email log.
[004]      Ab<PERSON>av <PERSON>     14-Feb-2022   Add new notes field on Approval popup.
[005]      Ab<PERSON>av <PERSON>     15-Mar-2022   Add approval permissions.
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{
    public abstract class SupplierPoApprovalProvider : DataAccess
    {
        static private SupplierPoApprovalProvider _instance = null;

        static public SupplierPoApprovalProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (SupplierPoApp<PERSON>Provider)Activator.CreateInstance(Type.GetType(Globals.Settings.SupplierPoApprovals.ProviderType));
                return _instance;
            }
        }
        public SupplierPoApprovalProvider()
        {
            this.ConnectionString = Globals.Settings.SupplierPoApprovals.ConnectionString;
            this.GTConnectionString = Globals.Settings.SupplierPoApprovals.GTConnectionString;
        }

        #region Method Registrations



        /// <summary>
        /// Line Manager Approvals.
        /// Calls [usp_PoSupplierLineManagerApproval]
        /// </summary>
        public abstract List<SupplierPoApprovalDetails> LineManagerApprovalDeclineIndependentTest(System.Int32? SupplierApprovalId, System.Int32? Status, System.Int32? UpdatedBy, System.Int32? clientId, System.String Notes);

        /// <summary>
        /// Get selected data for update screen.
        /// Calls [usp_select_TradeRefForSupplier]
        /// </summary>
        public abstract SupplierPoApprovalDetails Get(System.Int32? SupplierApprovalId);

        /// <summary>
        /// Update and get the line manager details.
        /// Calls [usp_update_LineManagerDetails]
        /// </summary>
        public abstract SupplierPoApprovalDetails UpdateLineManagerDetails(System.Int32? SupplierApprovalId, System.Int32? LineManagerId, System.Boolean? IsSendToSupplier, System.Boolean? IsSendToLineManager, System.Int32? LoginId, System.Boolean? isSendToQuality, System.Int32? ClientId);

        /// <summary>
        /// Update and get the line manager details.
        /// Calls [usp_Change_LineManager]
        /// </summary>
        public abstract SupplierPoApprovalDetails ChangeLineManager(System.Int32? SupplierApprovalId, System.Int32? LineManagerId, System.Int32? LoginId, System.Int32? ClientId);


        /// <summary>
        /// Get data for Supplier Approval History tab.
        /// Calls [usp_selectSupplierApprovalPOHistory]
        /// </summary>
        public abstract List<SupplierPoApprovalDetails> GetData_SupplierApprovalHistory(System.Int32? purchaseOrderId);

        /// <summary>
        /// Get data for Approval Status tab.
        /// Calls [usp_selectSupplierPoApproval]
        /// </summary>
        public abstract List<SupplierPoApprovalDetails> GetData_ApprovalStatus(System.Int32? purchaseOrderId, System.Int32? LoginId, System.Int32? ClientNo);

        /// <summary>
        /// Get Data for Trade Reference tab.
        /// Calls [usp_selectSupplierApprovalTradeReferences]
        /// </summary>
        public abstract List<SupplierPoApprovalDetails> GetData_TradeReference(System.Int32? purchaseOrderId, System.Int32? ClientNo);

        /// <summary>
        /// Update
        /// Calls [usp_update_PurchaseOrderLine]
        /// </summary>
        //[002] start
        public abstract bool Update(System.Int32? SupplierApprovalId, System.String FranchiseweblinkOrEvidence, System.String TradeReferenceOne, System.String TradeReferenceTwo, System.String TradeRefrenceThree, System.Int32? PurchasingMethodNo, System.Int32? PrecogsSupplierNo, System.Int32? UpdatedBy, System.Boolean? InDraftMode, System.String CommentText);


        /// <summary>
        /// Quality Team Approvals.
        /// Calls [usp_PoSupplierQualityApproval]
        /// </summary>
        public abstract List<SupplierPoApprovalDetails> QualityApproveDecline(System.Int32? purchaseOrderLineId, System.Int32? Approved, System.Int32? updatedBy, System.String Notes, System.Boolean? PartERAI);

        /// <summary>
        /// Email Data.
        /// Calls [usp_GetSupplierApprovalEmailData]
        /// </summary>
        public abstract List<SupplierPoApprovalDetails> GetEmailData(System.Int32? PurchaseOrderNo);

        /// <summary>
        /// Quality Team Escalate.
        /// Calls [usp_PoSupplierQualityEscalate]
        /// </summary>
        public abstract List<SupplierPoApprovalDetails> QualityEscalate(System.Int32? SupplierApprovalId, System.Int32? updatedBy, System.Int32? ClientId, System.String Notes, System.Boolean? isPartERAI);
        /// <summary>
        /// InsertTrade Refrence PDF.
        /// Calls [usp_PO_SA_InsertTradeRefPDF]
        /// </summary>
        public abstract int InsertTradeRefPDF(System.Int32? ID, System.String caption, System.String tempFile, System.Int32 LoginID, System.String UploadType);

        /// <summary>
        /// GetListForSupplierApproval.
        /// Calls [usp_selectAll_PDF_for_SupplierApproval]
        /// </summary>
        public abstract List<PDFDocumentDetails> GetPDFListForSupplierApproval(System.Int32? SupplierApprovalId, System.String UploadType);

        /// <summary>
        /// Delete purchase order pdf
        /// Calls[usp_delete_SupplierApprovalPDF]
        /// </summary>
        /// <param name="PurchaseOrderPdfId"></param>
        /// <returns></returns>
        public abstract bool DeleteSupplierApprovalPDF(System.Int32? SATradeReferencePDFID);
        public abstract int InsertSupplierAprrovalImage(System.Int32? ID, System.String caption, System.String tempFile, System.Int32 LoginID, System.String UploadType);
        public abstract List<SupplierPoApprovalDetails> GetImageListForSupplierApproval(System.Int32? SupplierApprovalId, System.String UploadType);
        public abstract bool DeleteSupplierApprovalImage(System.Int32? SAImageID);

        /// <summary>
        /// Insert Term and Condetion Email Log.
        /// Calls [usp_Insert_SupplierApprovalTnCEmailLog]
        /// </summary>
        public abstract int InsertTermCondetionEmailLog(System.Int32? SupplierApprovalNo, System.Int32? SendFromId, System.String SendToEmail, System.Int32? SupplierNo, System.String Subject, System.Boolean? IsNotTCEmail, System.Int32? SendToId);
        #endregion

    }
}
