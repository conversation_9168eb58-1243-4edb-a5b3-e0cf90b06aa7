///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// Contains functionality common to all LeftNugget DataLists in GT
//
// RP 06.01.2010:
// - dispose everything fully
//
// RP 22.12.2009:
// - populate filters server-side
//
// RP 02.11.2009:
// - reset page to 1 when Search clicked (except the first time)
// - allow complete hiding of a filter
//
// RP 23.10.2009:
// - bring into line with changes to DataListNugget
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.initializeBase(this, [element]);
	this._blnIsExpanded = true;
	this._aryFilterButtonIDs = [];
	this._aryFilterIDs = [];
	this._aryFilterShown = [];
	this._blnFiltersShown = false;
	this._intTimeoutID_Filters = -1;
	this._aryFilterFieldsToInit = [];
	this._intCountFilterFieldsToInit = 0;
	this._intCountDropDownsToCheckForData = 0;
	this._objStateData = {};
	this._blnInitialisedControls = false;
	this._enmInitialSortDirection = 1;
	this._intInitialSortColumnIndex = 0;
	this._blnFirstSearch = true;
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.prototype = {


	get_tbl: function() { return this._tbl; }, 	set_tbl: function(value) { if (this._tbl !== value)  this._tbl = value; }, 
	get_pnlTable: function() { return this._pnlTable; }, 	set_pnlTable: function(value) { if (this._pnlTable !== value)  this._pnlTable = value; }, 
	get_pnlLoading: function() { return this._pnlLoading; }, 	set_pnlLoading: function(value) { if (this._pnlLoading !== value)  this._pnlLoading = value; }, 
	get_pnlPagingContent: function() { return this._pnlPagingContent; }, 	set_pnlPagingContent: function(value) { if (this._pnlPagingContent !== value)  this._pnlPagingContent = value; }, 
	get_hypPrev: function() { return this._hypPrev; }, 	set_hypPrev: function(value) { if (this._hypPrev !== value)  this._hypPrev = value; }, 
	get_lblPageNo: function() { return this._lblPageNo; }, 	set_lblPageNo: function(value) { if (this._lblPageNo !== value)  this._lblPageNo = value; }, 
	get_lblTotalPages: function() { return this._lblTotalPages; }, 	set_lblTotalPages: function(value) { if (this._lblTotalPages !== value)  this._lblTotalPages = value; }, 
	get_hypNext: function() { return this._hypNext; }, 	set_hypNext: function(value) { if (this._hypNext !== value)  this._hypNext = value; }, 
	get_intPageSize: function() { return this._intPageSize; }, 	set_intPageSize: function(value) { if (this._intPageSize !== value)  this._intPageSize = value; }, 
	get_aryFilterButtonIDs: function() { return this._aryFilterButtonIDs; }, 	set_aryFilterButtonIDs: function(value) { if (this._aryFilterButtonIDs !== value)  this._aryFilterButtonIDs = value; }, 
	get_aryFilterIDs: function() { return this._aryFilterIDs; }, 	set_aryFilterIDs: function(value) { if (this._aryFilterIDs !== value)  this._aryFilterIDs = value; }, 
	get_aryFilterShown: function() { return this._aryFilterShown; }, 	set_aryFilterShown: function(value) { if (this._aryFilterShown !== value)  this._aryFilterShown = value; }, 
	get_pnlShowFilters: function() { return this._pnlShowFilters; }, 	set_pnlShowFilters: function(value) { if (this._pnlShowFilters !== value)  this._pnlShowFilters = value; }, 
	get_pnlFilters: function() { return this._pnlFilters; }, 	set_pnlFilters: function(value) { if (this._pnlFilters !== value)  this._pnlFilters = value; }, 
	get_pnlLock: function() { return this._pnlLock; }, 	set_pnlLock: function(value) { if (this._pnlLock !== value)  this._pnlLock = value; }, 
	get_pnlPagingControls: function() { return this._pnlPagingControls; }, 	set_pnlPagingControls: function(value) { if (this._pnlPagingControls !== value)  this._pnlPagingControls = value; }, 
	get_pnlNoData: function() { return this._pnlNoData; }, 	set_pnlNoData: function(value) { if (this._pnlNoData !== value)  this._pnlNoData = value; }, 
	get_pnlSearch: function() { return this._pnlSearch; }, 	set_pnlSearch: function(value) { if (this._pnlSearch !== value)  this._pnlSearch = value; }, 
	get_trSearch: function() { return this._trSearch; }, 	set_trSearch: function(value) { if (this._trSearch !== value)  this._trSearch = value; }, 
	get_intDataListNuggetID: function() { return this._intDataListNuggetID; }, 	set_intDataListNuggetID: function(value) { if (this._intDataListNuggetID !== value)  this._intDataListNuggetID = value; }, 
	get_blnAllowSavingState: function() { return this._blnAllowSavingState; }, 	set_blnAllowSavingState: function(value) { if (this._blnAllowSavingState !== value)  this._blnAllowSavingState = value; }, 
	get_strDataListNuggetSubType: function() { return this._strDataListNuggetSubType; }, 	set_strDataListNuggetSubType: function(value) { if (this._strDataListNuggetSubType !== value)  this._strDataListNuggetSubType = value; }, 
	get_blnSaveState: function() { return this._blnSaveState; }, 	set_blnSaveState: function(value) { if (this._blnSaveState !== value)  this._blnSaveState = value; }, 
	get_intPageSizeForState: function() { return this._intPageSizeForState; }, 	set_intPageSizeForState: function(value) { if (this._intPageSizeForState !== value)  this._intPageSizeForState = value; }, 
	get_enmViewLevel: function() { return this._enmViewLevel; }, 	set_enmViewLevel: function(value) { if (this._enmViewLevel !== value)  this._enmViewLevel = value; }, 

	addSetupDataCallEvent: function(handler) { this.get_events().addHandler("SetupDataCall", handler); },
	removeSetupDataCallEvent: function(handler) { this.get_events().removeHandler("SetupDataCall", handler); },
	onSetupDataCall: function() {
		var handler = this.get_events().getHandler("SetupDataCall");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
		
	addGetDataOKEvent: function(handler) { this.get_events().addHandler("GetDataOK", handler); },
	removeGetDataOKEvent: function(handler) { this.get_events().removeHandler("GetDataOK", handler); },
	onGetDataOK: function() {
		var handler = this.get_events().getHandler("GetDataOK");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
		
	addDataCallErrorEvent: function(handler) { this.get_events().addHandler("DataCallError", handler); },
	removeDataCallErrorEvent: function(handler) { this.get_events().removeHandler("DataCallError", handler); },
	onDataCallError: function() {
		var handler = this.get_events().getHandler("DataCallError");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
		
	addInitCompleteEvent: function(handler) { this.get_events().addHandler("InitComplete", handler); },
	removeInitCompleteEvent: function(handler) { this.get_events().removeHandler("InitComplete", handler); },
	onInitComplete: function() {
		if (this._blnInitialisedControls) return;
		this._blnInitialisedControls = true;
		var handler = this.get_events().getHandler("InitComplete");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.callBaseMethod(this, "initialize");
		this._tbl.addSortDataEvent(Function.createDelegate(this, this.sortData));
		this._tbl._intCurrentPageSize = this._intPageSize;
		this._tbl._intCurrentPage = 1;
		this._enmInitialSortDirection = this._tbl._enmSortDirection;
		this._intInitialSortColumnIndex = this._tbl._intSortColumnIndex;
		$R_FN.showElement(this._pnlLock, false);
		$addHandler(this._hypPrev, "click", Function.createDelegate(this, this.prevPage));
		$addHandler(this._hypNext, "click", Function.createDelegate(this, this.nextPage));
		$addHandler(this._pnlSearch, "click", Function.createDelegate(this, this.clickSearch));
		$addHandler(this._pnlLock, "click", Function.createDelegate(this, this.clickLock));
		$addHandler(this._pnlShowFilters, "mouseover", Function.createDelegate(this, this.showFilters));
		$addHandler(this._pnlShowFilters, "mouseout", Function.createDelegate(this, this.hideFilters));
		$addHandler(this._pnlFilters, "mouseover", Function.createDelegate(this, this.showFilters));
		$addHandler(this._pnlFilters, "mouseout", Function.createDelegate(this, this.hideFilters));
		$addHandler(this._pnlPagingContent, "mouseover", Function.createDelegate(this, this.cancelFilterHideTimeout));
		$addHandler(this._pnlPagingContent, "mouseout", Function.createDelegate(this, this.hideFilters));
		this.setFilterFieldEnterPressedEvents();
		$R_FN.showElement(this._pnlTable, false);

		//step through all filter fields to find if any have controls that need to be initialized before we continue
		//and also to set click events on buttons
		this._aryFilterFieldsToInit = [];
		for (var i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			var fld = $find(this._aryFilterIDs[i]);
			if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown" || Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating") Array.add(this._aryFilterFieldsToInit, fld);
			fld = null;
			var el = $get(this._aryFilterButtonIDs[i]);
			if (el) $addHandler(el, "click", Function.createDelegate(this, this.showFilterItem));
			el = null;			
		}
		this._intCountFilterFieldsToInit = this._aryFilterFieldsToInit.length;
		if (this._intCountFilterFieldsToInit == 0) {
			this.controlsInitialized();
		} else {
			for (i = 0; i < this._intCountFilterFieldsToInit; i++) {
				this.ensureFilterFieldControlInitialized(this._aryFilterFieldsToInit[i]);
			}
		}
	},
	
	ensureFilterFieldControlInitialized: function(fld) {
		if (fld.get_isInitialized()) {
			this._intCountFilterFieldsToInit -= 1;
			if (this._intCountFilterFieldsToInit == 0) {
				this._aryFilterFieldsToInit = null;
				this.controlsInitialized();
			}
		} else {
			var strID = this._element.id;
			var fn = function() { $find(strID).ensureFilterFieldControlInitialized(fld); };
			setTimeout(fn, 5);
		}
	},
	
	controlsInitialized: function() {
		this.getDropDownsData(true);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._element) $clearHandlers(this._element);
		if (this._hypPrev) $clearHandlers(this._hypPrev);
		if (this._hypNext) $clearHandlers(this._hypNext);
		if (this._pnlSearch) $clearHandlers(this._pnlSearch);
		if (this._pnlShowFilters) $clearHandlers(this._pnlShowFilters);
		if (this._pnlFilters) $clearHandlers(this._pnlFilters);
		if (this._pnlLock) $clearHandlers(this._pnlLock);
		if (this._pnlPagingContent) $clearHandlers(this._pnlPagingContent);
		for (var i = 0, l = this._aryFilterButtonIDs.length; i < l; i++) {
			var el = $get(this._aryFilterButtonIDs[i]);
			if (el) $clearHandlers(el);
			el = null;			
		}
		if (this._tbl) this._tbl.dispose();
		if (this._objData) this._objData.dispose();
		this._tbl = null;
		this._pnlTable = null;
		this._pnlLoading = null;		
		this._pnlPagingContent = null;
		this._hypPrev = null;
		this._lblPageNo = null;
		this._lblTotalPages = null;
		this._hypNext = null;
		this._aryFilterButtonIDs = null;
		this._aryFilterIDs = null;
		this._aryFilterShown = null;
		this._pnlShowFilters = null;
		this._pnlFilters = null;
		this._pnlLock = null;
		this._pnlSearch = null;
		this._trSearch = null;
		this._pnlNoData = null;
		this._pnlPagingControls = null;
		this._objData = null;
		this._aryFilterFieldsToInit = null;
		this._intPageSizeForState = null;
		this._enmViewLevel = null;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.callBaseMethod(this, "dispose");
	},

	clearState: function() { 
		Rebound.GlobalTrader.Site.WebServices.ClearDataListNuggetState(this._intDataListNuggetID, this._strDataListNuggetSubType);
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.callBaseMethod(this, "clearState");
	},
	
	getFilterFieldByFilterName: function(strName) {
		var fld = null;
		for (var i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			if ($find(this._aryFilterIDs[i])._strFilterField == strName) {
				fld = $find(this._aryFilterIDs[i]);
				break;
			}
		}
		return fld;
	},
	
	getFilterValue: function(strName) {
		var objOut = null;
		var fld = this.getFilterFieldByFilterName(strName);
		if (fld) objOut = fld.getValue();
		fld = null;
		return objOut;
	},
		
	setFilterValue: function(strName, value) {
		var fld = this.getFilterFieldByFilterName(strName);
		if (fld) {
			fld.setValue(value);
			this.doShowFilterItemByName(strName, true);
		}
		fld = null;
	},

	getStateVariableByName: function(strName) {
		if (!this._objStateData) return {};
		for (var i = 0, l = this._objStateData.Filters.length; i < l; i++) {
			var flt = this._objStateData.Filters[i];
			if (strName.toUpperCase() == flt.Name.toUpperCase()) return flt;
			flt = null;
		}
		return {};
	},
	
	getStateValue: function(strName) {
		var objOut = null;
		var fld = this.getStateVariableByName(strName);
		if (fld) objOut = fld.Value;
		fld = null;
		return objOut;
	},

	resetState: function() {
		this._tbl._intCurrentPage = 1;
		this._tbl._intCurrentPageSize = this._intPageSize;
		this._tbl._enmSortDirection = this._enmInitialSortDirection;
		this._tbl._intSortColumnIndex = this._intInitialSortColumnIndex;
		for (var i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			var fld = $find(this._aryFilterIDs[i]);
			fld.reset();
			fld.resetToDefault();
			if (fld._strDefaultValue != "" && typeof(fld._strDefaultValue) != "undefined" && fld._strDefaultValue != null) this.doShowFilterItem(i, true);
			fld = null;
		}
	},

	getDropDownsData: function(blnCheckForCompletion) {
		var fld;

		//if we have to check for completion of dropdowns getting data we need to count them all first
		//otherwise one may finish too soon and we'll tell the control it can get data which will cause errors
		if (blnCheckForCompletion) {
			for (var i = 0, l = this._aryFilterIDs.length; i < l; i++) {
				fld = $find(this._aryFilterIDs[i]);
				if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown") this._intCountDropDownsToCheckForData += 1;
				fld = null;
			}
		}
		
		//get the data
		for (i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			fld = $find(this._aryFilterIDs[i]);
			if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown") {
				if (blnCheckForCompletion) fld._ddl.addGotDataComplete(Function.createDelegate(this, this.gotDropDownDataComplete));
				fld.getDropDownData();
			}
			fld = null;
		}

		if (blnCheckForCompletion && this._intCountDropDownsToCheckForData == 0) this.completionOfInit();
	},
	
	gotDropDownDataComplete: function() {
		this._intCountDropDownsToCheckForData -= 1;
		if (this._intCountDropDownsToCheckForData == 0) this.completionOfInit();
	},
	
	completionOfInit: function() {
		this.displayLockState();
		this.showLoading(false);
		$R_FN.showElement(this._pnlTable, false);
		this.onInitComplete();
	},

	getData: function() {
		if (this._blnGettingData) return;
		this._blnFirstSearch = false;
		this.showLoading(true);
		if (this._ibtnCancel) $R_IBTN.showButton(this._ibtnCancel, true);
		this._objData = new Rebound.GlobalTrader.Site.Data();
		this._objData.set_DataAction("GetData");
		this._objData.set_PathToData(this._strPathToData);
		this._objData.set_DataObject(this._strDataObject);
		this.onSetupDataCall();
		this._objData.addParameter("DLNID", this._intDataListNuggetID);
		this._objData.addParameter("DLNSubType", this._strDataListNuggetSubType);		
		this._objData.addParameter("SortIndex", this._tbl._intSortColumnIndex + 1);
		this._objData.addParameter("SortDir", this._tbl._enmSortDirection);
		this._objData.addParameter("PageIndex", this._tbl._intCurrentPage - 1);
		this._objData.addParameter("PageSize", this._tbl._intCurrentPageSize);
		this._objData.addParameter("PageSizeForState", this._intPageSizeForState);
		this._objData.addParameter("SaveState", this._blnSaveState);
		this.addFilterParameters(this._objData);
		this._objData.addDataOK(Function.createDelegate(this, this.dataList_getDataOK));
		this._objData.addError(Function.createDelegate(this, this.dataList_getDataError));
		this._objData.addTimeout(Function.createDelegate(this, this.dataList_getDataError));
		$R_DQ.addToQueue(this._objData);
		$R_DQ.processQueue();
		this._blnGettingData = true;
		this._objData = null;
	},

	dataList_getDataOK: function(args) {
		this._blnGettingData = false;
		this._objResult = args._result;
		this._tbl._intTotalRecords = this._objResult.TotalRecords;
		this._tbl.clearTable();
		this.onGetDataOK();
		this._tbl.calculatePages();
		this.updatePagingDisplay();
		this.showLoading(false);
		this.showNoData(this._objResult.TotalRecords == 0);
		this._tbl.resizeColumns();
	},
	
	dataList_getDataError: function(args) {
		this._blnGettingData = false;
		this.showLoading(false);
		this.onDataCallError();
	},
	
	updatePagingDisplay: function() {
		$R_FN.setInnerHTML(this._lblPageNo, this._tbl._intCurrentPage);
		$R_FN.setInnerHTML(this._lblTotalPages, this._tbl._intTotalPages);
	},
	
	prevPage: function() {
		if (this._tbl._intCurrentPage <= 1) return;
		this._tbl.prevPage();
		this.updatePagingDisplay();
		this.getData();
	},
	
	nextPage: function() {	
		if (this._tbl._intCurrentPage >= this._tbl._intTotalPages) return;
		this._tbl.nextPage();
		this.updatePagingDisplay();
		this.getData();
	},
	
	sortData: function() {
		this.getData();
	},
	
	showLoading: function(bln) {
		$R_FN.showElement(this._pnlLoading, bln);
		$R_FN.showElement(this._pnlPagingContent, !bln);
		$R_FN.showElement(this._pnlTable, !bln);		
		$R_FN.showElement(this._trSearch, !bln);		
		$R_FN.showElement(this._pnlShowFilters, !bln);
		$R_FN.showElement(this._pnlLock, !bln);
		if (bln) $R_FN.showElement(this._pnlNoData, false);
	},
	
	showNoData: function(bln) {
		$R_FN.showElement(this._pnlNoData, bln);
		$R_FN.showElement(this._pnlPagingControls, !bln);
		$R_FN.showElement(this._pnlTable, !bln);
		//set page to 1 if we've had no data (in case changes by other users have taken data from us)		
		if (bln) this._tbl._intCurrentPage = 1;
	},
	
	showFilterItem: function(sender, eventArgs) {
		var el = sender.target;
		if (!el) return;
		var intItemClicked = Number.parseInvariant(el.getAttribute("bgt_leftDataList_filter"));
		this.doShowFilterItem(intItemClicked, !this._aryFilterShown[intItemClicked]);
	},

	doShowFilterItemByName: function(strName, bln) {
		var i = 0;
		for (i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			if ($find(this._aryFilterIDs[i])._strFilterField == strName) break;
		}
		this.doShowFilterItem(i, bln);
	},
	
	doShowFilterItem: function(i, bln) {
		this._aryFilterShown[i] = bln;
		
		//show filter on/off link
		var elShowLink = $get(this._aryFilterButtonIDs[i]);
		Sys.UI.DomElement.removeCssClass(elShowLink, "filterItemOn");
		Sys.UI.DomElement.removeCssClass(elShowLink, "filterItemOff");
		Sys.UI.DomElement.addCssClass(elShowLink, (this._aryFilterShown[i]) ? "filterItemOn" : "filterItemOff");		
		elShowLink = null;
		
		//show filter
		var elFilter = $get(this._aryFilterIDs[i]);
		if (elFilter) $R_FN.showElement(elFilter, this._aryFilterShown[i]);
		elFilter = null;
	},
	
	completelyHideFilterItem: function(strName, bln) {
		var i = 0;
		for (i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			if ($find(this._aryFilterIDs[i])._strFilterField == strName) break;
		}
		$R_FN.showElement($get(this._aryFilterButtonIDs[i]), !bln);
		$R_FN.showElement($get(this._aryFilterIDs[i]), !bln);
	},

	showFilters: function(sender, eventArgs) {
		this.cancelFilterHideTimeout();	
		if (this._blnFiltersShown) return;
		$R_FN.showElement(this._pnlFilters, true);
		this._blnFiltersShown = true;
	},
	
	cancelFilterHideTimeout: function() {
		clearTimeout(this._intTimeoutID_Filters);
	},
	
	hideFilters: function(sender, eventArgs) {
		if (!this._blnFiltersShown) return;
		this.cancelFilterHideTimeout();	
		this._intTimeoutID_Filters = setTimeout(Function.createDelegate(this, this.finishHideFilters), 100);
	},
	
	finishHideFilters: function() {
		this.cancelFilterHideTimeout();	
		$R_FN.showElement(this._pnlFilters, false);
		this._blnFiltersShown = false;
	},
	
	setFilterFieldEnterPressedEvents: function() {
		for (var i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			var fld = $find(this._aryFilterIDs[i]);
			if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.TextBox" 
			|| Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical" ) {
				fld.addEnterPressed(Function.createDelegate(this, this.getData));
			}
		}
	},
	
	addFilterParameters: function(obj) {
		for (var i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			if (this._aryFilterIDs[i]) {
				var fld = $find(this._aryFilterIDs[i]);
				if (fld) {
					if (fld._blnOn && this._aryFilterShown[i]) {
						if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.Numerical" || Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating") {
							obj.addParameter(String.format("{0}Lo", fld._strFilterField), fld.getMinValue());
							obj.addParameter(String.format("{0}Hi", fld._strFilterField), fld.getMaxValue());
							obj.addParameter(String.format("{0}_Comparison", fld._strFilterField), fld._ddl.value);
						} else if (Object.getTypeName(fld) == "Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown") {
							if (fld._ddl.isSetAsNoValue() || fld._ddl._blnFirstTimeIn) {
								fld.enableField(false);
							} else {
								obj.addParameter(fld._strFilterField, fld.getValue());
							}
						} else {
							obj.addParameter(fld._strFilterField, fld.getValue());
						}
					}
					obj.addParameter(String.format("{0}_IsShown", fld._strFilterField), this._aryFilterShown[i]);
					obj.addParameter(String.format("{0}_IsOn", fld._strFilterField), fld._blnOn);
					obj.addParameter(String.format("{0}_Type", fld._strFilterField), fld._enmFieldType);
				}
				fld = null;
			}
		}
	},
	
	clickLock: function() {
		if (this._blnGettingData) return;
		this.showLockLoading();
		this.updateLockState(!this._blnSaveState);
	},
	
	updateLockState: function(blnLocked) {
		//have we have changed state? 
		var blnStateWasChanged = (this._blnSaveState != blnLocked);
		
		//update our locked state for the DLN
		this._blnSaveState = blnLocked;
		
		//save or clear state (only if state was changed)
		if (blnStateWasChanged) {
			if (this._blnSaveState) {
				this.saveState();
			} else {
				this.clearState();
			}
		}
		this.displayLockState();
	},	
	
	displayLockState: function() {
		//update display of lock
		Sys.UI.DomElement.removeCssClass(this._pnlLock, "lockLoading");
		if (this._blnSaveState) {
			Sys.UI.DomElement.addCssClass(this._pnlLock, "locked");
			Sys.UI.DomElement.removeCssClass(this._pnlLock, "unlocked");
		} else {
			Sys.UI.DomElement.addCssClass(this._pnlLock, "unlocked");
			Sys.UI.DomElement.removeCssClass(this._pnlLock, "locked");
		}
	},
		
	showLockLoading: function() {
		Sys.UI.DomElement.addCssClass(this._pnlLock, "lockLoading");
		Sys.UI.DomElement.removeCssClass(this._pnlLock, "locked");
		Sys.UI.DomElement.removeCssClass(this._pnlLock, "unlocked");
	},
	
	cancelDataCall: function() {
		this._blnGettingData = false;
		if (this._objData) this._objData.cancel();
	},
	
	cancelClicked: function() {
		this.cancelDataCall();
		if (this._ibtnCancel) $R_IBTN.showButton(this._ibtnCancel, false);
		this.showResultsPanels(true);
		//this.enableButtons(true);
		this.clearMessages();
		this.addMessage($R_RES.SearchCancelled, $R_ENUM$MessageTypeList.Warning);
		//this.showNoData(true);
	},
		
	resetFilter: function() {
		for (var i = 0, l = this._aryFilterIDs.length; i < l; i++) {
			var fld = $find(this._aryFilterIDs[i]);
			fld.reset();
			fld.resetToDefault();
			fld = null;
		}
	},
	
	clearState: function() {
		//do nothing - allow superclasses to handle it
	},
	
	saveState: function() {
		if (!this._blnSaveState) return;
		//this.enableButtons(false);
		this._objData = new Rebound.GlobalTrader.Site.Data();
		this.addFilterParameters(this._objData);
		this.onSetupDataCall();
		this._objData.set_PathToData(this._strPathToData);
		this._objData.set_DataObject(this._strDataObject);
		this._objData.set_DataAction("SaveState");
		this._objData.addParameter("DLNID", this._intDataListNuggetID);
		this._objData.addParameter("DLNSubType", this._strDataListNuggetSubType);		
		this._objData.addParameter("SortIndex", this._tbl._intSortColumnIndex + 1);
		this._objData.addParameter("SortDir", this._tbl._enmSortDirection);
		this._objData.addParameter("PageIndex", this._tbl._intCurrentPage - 1);
		this._objData.addParameter("PageSize", this._intPageSizeForState);
		this._objData.addParameter("SaveState", true);
		this._objData.addDataOK(Function.createDelegate(this, this.saveStateOK));
		this._objData.addError(Function.createDelegate(this, this.saveStateOK));
		this._objData.addTimeout(Function.createDelegate(this, this.saveStateOK));
		$R_DQ.addToQueue(this._objData);
		$R_DQ.processQueue();
	},
	
	saveStateOK: function() {
		//this.enableButtons(true);
	},
	
	clickSearch: function() {
		if (!this._blnFirstSearch) this._tbl._intCurrentPage = 1;
		this.getData();
	}
	
};
Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base", Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base, Sys.IDisposable);
