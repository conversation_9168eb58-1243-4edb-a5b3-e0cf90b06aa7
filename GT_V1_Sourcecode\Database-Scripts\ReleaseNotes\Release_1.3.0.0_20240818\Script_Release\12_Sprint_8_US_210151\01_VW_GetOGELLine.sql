﻿
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

ALTER VIEW [dbo].[VW_GetOGELLine]
AS
/*
===========================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-208619]			Ngai To				16-Jul-2024		UPDATE			US-208619: OGEL approval dropdown to be moved out of code to the setup screen
[US-208619]			Ngai To				23-Jul-2024		UPDATE			US-208619: Change GBOGE2024/0756 - OGEL PCB Comp MIL to GBOGE2024/00756 - OGEL PCB Comp MIL
[US-210151]			Phuc Hoang			05-Aug-2024		CREATE          OGEL Lines Screen to remove ECCN Description
===========================================================================================
*/
SELECT tbinvline.InvoiceLineId,
	tbso.salesorderId,
	tbso.salesordernumber,
	tbsoline.SOSerialNo,
	tbinv.AirWayBill,
	tbsoline.ECCNCode AS 'CommodityCode',
	tbsoline.ECCNCode, -- eccn.Notes,
	--tbcl.OGELNumber,
	CASE 
		WHEN tbApprvhdr.OGELNumber = 1
			THEN 'GBOGE2020/00615'
		WHEN tbApprvhdr.OGELNumber = 2
			THEN 'GBOGE2024/00532 - OGEL MIL GMST'
		WHEN tbApprvhdr.OGELNumber = 3
			THEN 'GBOGE2024/00756 - OGEL PCB Comp MIL'
		ELSE 'GBOGE2020/00615'
		END AS OGELNumber,
	tbApprvdtls.MilitaryUseNo AS OGEL_MilitaryUse,
	tbApprvdtls.EndDestinationCountryNo AS OGEL_EndDestinationCountry,
	tbso.clientno,
	tbinv.InvoiceNumber,
	tbso.DateOrdered,
	tbsoline.DatePromised,
	tbsoline.FullPart,
	tbsoline.Part as PartNumber

FROM dbo.tbSalesOrder tbso
JOIN dbo.tbSalesOrderLine tbsoline ON tbso.salesorderid = tbsoline.salesorderno
LEFT JOIN tbSO_ExportApprovalStatusOGEL tbApprvhdr ON tbso.SalesOrderId = tbApprvhdr.SalesOrderNo
	AND tbsoline.SalesOrderLineId = tbApprvhdr.SalesOrderLineNo
LEFT JOIN tbSO_ExportApprovalDetails tbApprvdtls ON tbApprvhdr.ExportApprovalId = tbApprvdtls.ExportApprovalNo
LEFT JOIN dbo.tbinvoice tbinv ON tbso.salesorderid = tbinv.salesorderno
LEFT JOIN dbo.tbInvoiceLine tbinvline ON tbinv.invoiceid = tbinvline.invoiceno
	AND tbsoline.SalesOrderLineId = tbinvline.SalesOrderLineNo
JOIN dbo.tbclient tbcl ON tbso.clientno = tbcl.clientid
JOIN tbCountry tbc ON tbc.CountryId = tbApprvdtls.EndDestinationCountryNo
--left join dbo.tbeccn eccn on eccn.ECCNCode=tbsoline.ECCNCode
--where tbc.OGEL =1 
WHERE (
		CASE 
			WHEN ISNULL(tbApprvhdr.OGELNumber, 0) > 0
				THEN 1
			ELSE CASE 
					WHEN (ISNULL(tbc.OGEL, 0) > 0)
						AND [dbo].[ufn_OGELGetFromCountry](tbApprvhdr.SalesOrderLineNo) = 1
						THEN 1
					ELSE 0
					END
			END
		) = 1
GO
