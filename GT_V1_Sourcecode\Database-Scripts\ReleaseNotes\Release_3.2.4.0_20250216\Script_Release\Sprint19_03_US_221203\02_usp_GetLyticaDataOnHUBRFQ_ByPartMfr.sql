﻿  
/*     
=======================================================================================================================   
TASK         UPDATED BY       DATE          ACTION    DESCRIPTION    
[US-215434]  Phuc Hoang       06-Nov-2024   CREATE    Lytica Price should apply fuzzy logic for inserting & displaying    
[US-221203]  CuongDoX   7-Jan-2025  UPDATE   Update Lytica from selected 
=======================================================================================================================  
*/    
CREATE OR ALTER PROCEDURE [dbo].[usp_GetLyticaDataOnHUBRFQ_ByPartMfr]    
(      
 @CusReqNo INT = 0,  
 @mfrName NVARCHAR(256) = NULL  
)  
AS    
    
BEGIN    
 DECLARE @partNumber NVARCHAR(256) = NULL,  
   @LyticaManufacturerRef NVARCHAR(256) = NULL,  
   @LyticaAveragePrice FLOAT = NULL,  
   @LyticaTargetPrice FLOAT = NULL,  
   @LyticaMarketLeading FLOAT = NULL, 
   @LifeCycleStatus NVARCHAR(200)
 SELECT TOP 1 @partNumber = Part FROM dbo.tbCustomerRequirement WHERE CustomerRequirementId = @CusReqNo;  
   
 SELECT TOP 1 @LyticaManufacturerRef = Manufacturer, @LyticaAveragePrice = AveragePrice, @LyticaTargetPrice = TargetPrice, @LyticaMarketLeading = MarketLeading, @LifeCycleStatus = LifeCycleStatus 
  FROM dbo.tbLyticaAPI  
  WHERE OriginalPartSearched = @partNumber AND Manufacturer = @mfrName  
  AND ISNULL(Inactive, 0) = 0  
  AND (ISNULL(AveragePrice, 0) + ISNULL(TargetPrice, 0) + ISNULL(MarketLeading, 0)) > 0  
  
 UPDATE dbo.tbCustomerRequirement  
 SET LyticaManufacturerRef = @LyticaManufacturerRef   
  ,LyticaAveragePrice = @LyticaAveragePrice  
  ,LyticaTargetPrice = @LyticaTargetPrice  
  ,LyticaMarketLeading = @LyticaMarketLeading  
 WHERE CustomerRequirementId = @CusReqNo;  
  
 SELECT @LyticaAveragePrice AS AveragePrice, @LyticaTargetPrice AS TargetPrice, @LyticaMarketLeading AS MarketLeading, @LyticaManufacturerRef AS LyticaManufacturerRef, @LifeCycleStatus AS LifeCycleStatus 
  
END    
    