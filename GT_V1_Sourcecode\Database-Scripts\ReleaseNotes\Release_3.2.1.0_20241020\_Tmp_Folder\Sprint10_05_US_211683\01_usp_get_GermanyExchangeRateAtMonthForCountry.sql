﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-211683]     An.TranTan		 11-SEP-2024		CREATE		German Invoice: get ExchangeRate at Invoice month for country
[US-211683]     An.TranTan		 12-SEP-2024		CREATE		Update get fuzzy country name
===========================================================================================  
*/
GO
CREATE OR ALTER PROC usp_get_GermanyExchangeRateAtMonthForCountry (
	@BillToAddressNo int = null,
	@InvoiceDate datetime = null
)
AS

BEGIN
	Declare @Country varchar(50);
	SELECT @Country  = ct.CountryName
	FROM tbCountry ct WITH (NOLOCK)
		JOIN tbAddress a WITH (NOLOCK) ON ct.CountryId = a.CountryNo
	WHERE a.AddressId = @BillToAddressNo

	SELECT 
		ExchangeRateId,
		(SELECT MONTH(MonthInYear + ' 1 2024')) as MonthNum, 
		MonthInYear,
		FromCurrency,
		ToCurrency,
		ExchangeRate
	INTO #tempExchangeRate
	FROM 
		(SELECT TOP 1 ExchangeRateId, CountryName, FromCurrency, ToCurrency, January, February, March, April, May, June, July, August, September, October, November, December
			FROM tbGermanyExchangeRate
			WHERE CountryName LIKE @Country + '%'
				AND YEAR(DLUP) = YEAR(@InvoiceDate)
			ORDER BY DLUP DESC
	 ) AS SourceTable
	UNPIVOT 
		(ExchangeRate FOR MonthInYear IN (January, February, March, April, May, June, July, August, September, October, November, December)) AS UnpivotedTable;

	SELECT FromCurrency,
		ToCurrency,
		CAST(ExchangeRate AS DECIMAL(9,5)) AS ExchangeRate
	FROM #tempExchangeRate
	WHERE MonthNum = MONTH(@InvoiceDate) AND ExchangeRate > 0
	
	DROP TABLE #tempExchangeRate
END

GO

/*Test script
EXEC usp_get_GermanyExchangeRateAtMonthForCountry
	@BillToAddressNo = 334842,
	@InvoiceDate = '2024-08-02'
*/
