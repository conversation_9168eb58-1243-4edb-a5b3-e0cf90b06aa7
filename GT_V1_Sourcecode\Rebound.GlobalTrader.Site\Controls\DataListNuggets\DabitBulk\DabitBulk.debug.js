///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - hide Buyer filter on "My" tab
//
// RP 06.01.2010:
// - fully dispose everything
//
//
//
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk = function(element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.prototype = {
    get_blnPOHub: function () { return this._blnPOHub; }, set_blnPOHub: function (v) { if (this._blnPOHub !== v) this._blnPOHub = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    get_IsGSA: function () { return this._IsGSA; }, set_IsGSA: function (v) { if (this._IsGSA !== v) this._IsGSA = v; },

    initialize: function() {
        this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._ibtnPrint = $get(this._aryButtonIDs[0]);
        this._ibtnEmail = $get(this._aryButtonIDs[1]);
        this._frmConfirm = $find(this._aryFormIDs[0]);
        this._frmConfirm.addCancel(Function.createDelegate(this, this.hideConfirmForm));
        this._frmConfirm.addSaveComplete(Function.createDelegate(this, this.saveCeaseComplete));
        this._frmConfirm.addNotConfirmed(Function.createDelegate(this, this.hideConfirmForm));
        if (this._ibtnEmail) $R_IBTN.addClick(this._ibtnEmail, Function.createDelegate(this, this.showConfirmForm));
        if (this._ibtnPrint) $R_IBTN.addClick(this._ibtnPrint, Function.createDelegate(this, this.printDebit));
        this.enableBulkButtons(false);
        this._table.addMultipleSelectionChanged(Function.createDelegate(this, this.selectionMade));
        this._strPathToData = "controls/DataListNuggets/DabitBulk";
        this._strDataObject = "DabitBulk";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.callBaseMethod(this, "initialize");
    },

    initAfterBaseIsReady: function() {
        this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._IsGlobalLogin = null;
        this._IsGSA = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.callBaseMethod(this, "dispose");
    },
    enableBulkButtons: function (bln) {
        if (this._ibtnPrint) $R_IBTN.enableButton(this._ibtnPrint, bln);
        //[001] code start
        if (this._ibtnEmail) $R_IBTN.enableButton(this._ibtnEmail, bln);
        //[001] code end
    },
    selectionMade: function () {
        this.enableBulkButtons(this._table._arySelectedIndexes.length > 0);
    },
    pageTabChanged: function() {
        this._table._intCurrentPage = 1;
        this._enmViewLevel = this._intCurrentTab;
        this.updateFilterVisibility();
        this.getData();
    },

    setupDataCall: function() {
        this._objData.addParameter("ViewLevel", this._enmViewLevel);
        this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
        this._objData.addParameter("PageSizeLimit", $("#ctl00_cphMain_ctlDebits_ctlDB_txtLimitResults").val());
    },

    getDataOK: function(args) {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData;
            if (this._IsGlobalLogin) {
                aryData = [
                    $RGT_nubButton_DebitNote(row.ID, row.No)
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                    , $R_FN.writeDoubleCellValue($R_FN.showSupplierMessage(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM)+ '</span>' :$RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.SuppMessage)), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    , $R_FN.setCleanTextValue(row.Date)
                    , $RGT_nubButton_PurchaseOrder(row.PONo, row.PurchaseOrder)
                    , $R_FN.setCleanTextValue(row.SupplierInvoice)
                    , $R_FN.setCleanTextValue(row.ClientName)
                ];
            }
            else {
                aryData = [
                    $RGT_nubButton_DebitNote(row.ID, row.No)
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                    , $R_FN.writeDoubleCellValue($R_FN.showSupplierMessage(row.blnMakeYellow == true ? '<span style="background-color:yellow;">' + $RGT_nubButton_Company(row.CMNo, row.CM)+ '</span>':$RGT_nubButton_Company(row.CMNo, row.CM), $R_FN.setCleanTextValue(row.SuppMessage)), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    , $R_FN.setCleanTextValue(row.Date)
                    , $RGT_nubButton_PurchaseOrder(row.PONo, row.PurchaseOrder)
                    , $R_FN.setCleanTextValue(row.SupplierInvoice)
                ];
            }
            this._table.addRow(aryData, row.ID, false);
            aryData = null; row = null;
        }
    },

    updateFilterVisibility: function() {
        this.getFilterField("ctlBuyerName").show(this._enmViewLevel != 0);
         this.getFilterField("ctlPohubOnly").show(this._blnPOHub);
         this.getFilterField("ctlPohubOnly").enableField(this._blnPOHub);
        this.getFilterField("ctlClientName").show(this._IsGlobalLogin || this._IsGSA);
    },
    showConfirmForm: function () {
        this._frmConfirm._strDebits = this._table._aryCurrentValues;
        this.showForm(this._frmConfirm, true);
    },
    hideConfirmForm: function () {
        this.showForm(this._frmConfirm, false);
    },
    saveCeaseComplete: function () {
        this.hideConfirmForm();
    },
    printDebit: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/DebitMainInfo");
        obj.set_DataObject("DebitMainInfo");
        obj.set_DataAction("SaveDebitPrint");
        obj.addParameter("DebitsPrintId", this._table._aryCurrentValues);
        obj.addDataOK(Function.createDelegate(this, this.printDebitsaveComplete));
        obj.addError(Function.createDelegate(this, this.printDebitError));
        obj.addTimeout(Function.createDelegate(this, this.printDebitError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
    },
    printDebitError: function (args) {
        //this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    printDebitsaveComplete: function (args) {
        var PrintId = args._result.Result;
        var url = args._result.Url;
        //window.open(url + "/Print.aspx?pro=61&id=" + PrintId, "winPrint", "left=20,top=20,width=950,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
        window.open("Print.aspx?pro=61&id=" + PrintId, "winPrintBulkDebit", "left=20,top=20,width=870,height=674,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
    },
    test: function (args) {
       
    }

};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.DabitBulk", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
