﻿//Marker     changed by      date         Remarks
//[001]      <PERSON><PERSON><PERSON>    10/11/20011   ESMS Ref:19 - Change link to "Add New Requirement to This Company"   
//[002]      Vinay          12/03/2014    ESMS Ref:98 - Add/edit printer (NiceLabel) in setup screen  
//[003]      <PERSON><PERSON><PERSON>    07-Aug-2018   [REB-12084]:Lock PO lines when EPR is authorised
//[004]      <PERSON><PERSON><PERSON>    09-Aug-2018   REB-12161 : credit card payments
//[005]      <PERSON><PERSON><PERSON><PERSON> 09-Dec-2021   Add Allow export to excel for PO and SO screen.
//[005]      Abhinav <PERSON>xena 09-Dec-2021   Add Allow export to excel for PO and SO screen.
//[006]      <PERSON>    07-Jan-2022   Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
//[007]      <PERSON><PERSON>    23-Jan-2024  Permission for Bulk Edit Button -RP-2603
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.BLL
{
    public partial class SecurityFunction
    {
        /// <summary>
        /// An enum representation of the 'tbSecurityFunction' table.
        /// </summary>
        /// <remark>This enumeration contains the items contained in the table tbSecurityFunction</remark>
        [Serializable]
        public enum List
        {
            
            ContactSection_View = 1,
            OrdersSection_View = 2,
            WarehouseSection_View = 3,
            ReportsSection_View = 4,
            SetupSection_View = 5,
            OverallSetup = 6,
            AllowPrinting = 7,
            AllowEmailing = 8,
            ViewDifferentUsersHomepage = 9,
            DashboardSection_View = 10,
            ViewOnlyMyReports = 11,
            PriceEditPoHub = 12,
            CompanyStatusStop=13,
            Utility_View = 14,
            PowerBI_View = 20010043,
            ViewAllStatistics = 121,
            ViewTopSalesman = 122,
            Contact_Company_Add = 1000000,
            Contact_Manufacturer_Add = 1000001,
            Contact_Manufacturer_Group_Add = 20010033,
            Contact_Company_Allow_Active_Inactive = 1000314,
            Contact_CompanyDetail_MainInfo_Edit = 1000101,
            Contact_CompanyDetail_ContactLog_Add = 1000102,
            Contact_CompanyDetail_ContactLog_Edit = 1000103,
            Contact_CompanyDetail_Addresses_Add = 1000104,
            Contact_CompanyDetail_Addresses_Edit = 1000105,
            Contact_CompanyDetail_Addresses_Cease = 1000106,
            Contact_CompanyDetail_Addresses_MakeDefault = 1000107,
            Contact_CompanyDetail_PurchasingInfo_Edit = 1000108,
            Contact_CompanyDetail_SalesInfo_Edit = 1000109,
            Contact_CompanyDetail_ManufacturersSupplied_Add = 1000110,
            Contact_CompanyDetail_ManufacturersSupplied_Edit = 1000111,
            Contact_CompanyDetail_ManufacturersSupplied_Delete = 1000112,
            Contact_CompanyDetail_Addresses_Tax_AddEdit = 1000113,
            Contact_CompanyDetail_PDFDocument_Add = 1000114,
            Contact_CompanyDetail_PDFDocument_Delete = 1000115,
            Contact_CompanyDetail_Customer_View = 1000116,
            Contact_CompanyDetail_Supplier_View = 1000117,
            Contact_CompanyDetail_Prospects_View = 1000118,
            Contact_CompanyDetail_AllCompanies_View = 1000119,
            Contact_CompanyDetail_MainInfo_CompanyType_Edit = 1000120,
            Contact_CompanyDetail_MainInfo_AccountNotes_Edit = 1000121,
            Contact_CompanyDetail_MainInfo_Certificates_AddEdit = 1000122,
            Contact_CompanyDetail_PurchasingInfo_SupplierOnStop = 1000123,
            Contact_CompanyDetail_SalesInfo_CreditStatus = 1000124,
            Contact_CompanyDetail_MainInfo_Edit_SupplierHUB = 1000125,
            Contact_CompanyDetail_MainInfo_PremierCustomer_Edit = 1000126,
            Contact_CompanyDetail_MainInfo_Tier2PremierCustomer_Edit = 1000127,
            Contact_CompanyDetail_MainInfo_PurchasingNotes_Edit= 1000128,
            Contact_CompanyDetail_MainInfo_PremierCustomer_Activate = 1000129,
            //Customer API permision start
            Contact_CompanyDetail_MainInfo_CustomerAPI_Add = 1000133,
            Contact_CompanyDetail_MainInfo_CustomerAPI_Edit = 1000134,
            Contact_CompanyDetail_MainInfo_CustomerAPI_BomMapping = 1000135,
            Contact_CompanyDetail_MainInfo_CustomerAPI_SupplierImport = 1000136,
            //Customer API permision End
            Contact_CompanyDetail_GSA_Edit = 1000132,
            Contact_ContactDetail_ContactList_Add = 1000201,
            Contact_ContactDetail_ContactList_Delete = 1000202,
            Contact_ContactDetail_MainInfo_Edit = 1000203,
            Contact_ContactDetail_ExtendedInfo_Edit = 1000204,
            Contact_ContactDetail_ContactLog_Add = 1000205,
            Contact_ContactDetail_ContactLog_Edit = 1000206,
            Contact_ContactDetail_ContactList_MakeSODefault = 1000207,
            Contact_ContactDetail_ContactList_MakePODefault = 1000208,
            Contact_ContactDetail_View = 1000209,
            Contact_ManufacturerDetail_MainInfo_Edit = 1000301,
            Contact_ManufacturerDetail_RelatedCompanies_Delete = 1000302,
            Contact_ManufacturerDetail_RelatedCompanies_Add = 1000303,
            Contact_ManufacturerDetail_Suppliers_Add = 1000304,
            Contact_ManufacturerDetail_Suppliers_Edit = 1000305,
            Contact_ManufacturerDetail_Suppliers_Delete = 1000306,
            Contact_ManufacturerDetail_View = 1000307,
            //001
            Contact_Manufacturer_PDFDocument_Add = 1000308,
            Contact_Manufacturer_PDFDocument_Delete = 1000309,
            //001
            Contact_Manufacturer_MainInfo_AllowInactive = 1000310,
            Contact_ContactDetail_Finance_Link_Accounts = 1000311,
            Contact_MainInfo_CreditApplicationForm_CreateSend_SalesPerson = 1000312,
            Contact_MainInfo_CreditApplicationForm_ApproveReject_Accounts = 1000313,


            Orders_CustomerRequirement_Add = 2000001,
            Orders_Quote_Add = 2000002,
            Orders_SalesOrder_Add = 2000003,
            Orders_Invoice_Add = 2000004,
            Orders_PurchaseOrder_Add = 2000005,
            Orders_CRMA_Add = 2000006,
            Orders_SRMA_Add = 2000007,
            Orders_CreditNote_Add = 2000008,
            Orders_CreditNote_BulkPrintEmail = ********,
            Orders_DebitNote_BulkPrintEmail= ********,
            Orders_DebitNote_Add = 2000009,
            Orders_Invoice_BulkPrint = 2000010,
            Orders_Quote_Add_FromNew = 2000011,
            Orders_Quote_Add_FromReq = 2000012,
            Orders_Credit_Add_FromInvoice = 2000013,
            Orders_Credit_Add_FromCRMA = 2000014,
            //[001]Code Start
            Orders_CustomerRequirement_Detail_Add = 2000015,
            Orders_ClientInvoice_BulkPrint = 2000016,
            Orders_QuoteAndSO_Edit_AS9120 = 2000017,
            //[001]Code End
            Orders_CustomerRequirement_Sourcing_View = 2000100,
            Orders_CustomerRequirement_PartsRequired_AddAlternate = 2000101,
            Orders_CustomerRequirement_MainInformation_Edit = 2000102,
            Orders_CustomerRequirement_SourcingResults_Add = 2000103,
            Orders_CustomerRequirement_SourcingResults_Edit = 2000104,
            Orders_CustomerRequirement_Sourcing_EditOffer = 2000105,
            Orders_CustomerRequirement_Sourcing_RFQ = 2000106,
            Orders_CustomerRequirement_Sourcing_AddToRequirement = 2000107,
            Orders_CustomerRequirement_PartsRequired_Close = 2000108,
            Orders_CustomerRequirement_Sourcing_AddStockInfo = 2000109,
            Orders_CustomerRequirement_Sourcing_EditStockInfo = 2000110,
            Orders_CustomerRequirement_Sourcing_AddOffer = 2000111,
            Orders_CustomerRequirement_Sourcing_AddTrusted = 2000112,
            Orders_CustomerRequirement_View = 2000113,
            Orders_Quote_MainInfo_Edit = 2000201,
            Orders_QuoteLines_Add = 2000202,
            Orders_QuoteLines_Edit = 2000203,
            Orders_QuoteLines_Close = 2000204,
            Orders_Quote_Print = 2000205,
            Orders_Quote_Email = 2000206,
            Orders_QuoteLines_CanAdd_NewLine = 2000207,
            Orders_QuoteLines_CanAdd_Req = 2000208,
            Orders_QuoteLines_CanAdd_Service = 2000209,
            Orders_QuoteLines_CanAdd_Stock = 2000210,
            Orders_QuoteLines_CanAdd_SourcingResult = 2000211,
            Orders_Quote_View = 2000212,
            SalesBomManagerSheet = 2000213,                          /*Added By Manoj Kumar RP-66*/
            // From Lot
            Orders_QuoteLines_CanAdd_FromLot = 2000214,
            Orders_QuoteLines_CanDelete_FromLot = 2000215,
            // end
            Orders_PurchaseOrder_MainInfo_Edit = 2000301,
            Orders_PurchaseOrder_Lines_Add = 2000302,
            Orders_PurchaseOrder_Lines_Edit = 2000303,
            Orders_PurchaseOrder_Lines_Post = 2000304,
            Orders_PurchaseOrder_Lines_Unpost = 2000305,
            Orders_PurchaseOrder_Lines_Delete = 2000306,
            Orders_PurchaseOrder_Lines_Deallocate = 2000307,
            Orders_PurchaseOrder_Print = 2000308,
            Orders_PurchaseOrder_Email = 2000309,
            Orders_PurchaseOrder_MainInfo_Close = 2000311,
            Orders_PurchaseOrder_Lines_CanAdd_NewLine = 2000312,
            Orders_PurchaseOrder_Lines_CanAdd_POReq = 2000313,
            Orders_PurchaseOrder_Lines_CanAdd_PO = 2000314,
            Orders_PurchaseOrder_Lines_CanAdd_Stock = 2000315,
            Orders_PurchaseOrder_Lines_CanAdd_Req = 2000316,
            Orders_PurchaseOrder_Lines_Close = 2000317,
            Orders_PurchaseOrder_EditTax = 2000318,
            Orders_PurchaseOrder_EditCurrencyAndTerms = 2000525,
            Orders_PurchaseOrder_MainInfo_Approve = 2000319,
            Orders_PurchaseOrder_MainInfo_Disapprove = 2000320,
            Orders_PurchaseOrder_MainInfo_EditTax = 2000321,
            Orders_PurchaseOrder_MainInfo_EditCurrencyAndTerms = 2000334,
            Orders_PurchaseOrder_MainInfo_ApprovePOAfterChecked = 2000335,
            Orders_PurchaseOrder_PDFDocument_Add = 2000322,
            Orders_PurchaseOrder_PDFDocument_Delete = 2000323,
            Orders_PurchaseOrder_View = 2000324,
            Orders_PurchaseOrder_EPR_RefAuthorise = 2000325,
            Orders_PurchaseOrder_EPR_EditAfterAuthorise = 2000326,
            Orders_PurchaseOrder_EPR_Complete = 2000327,
            Orders_PurchaseOrder_EPR_Delete = 2000328,
            Orders_PurchaseOrder_Expedite_Add = 2000329,
            Orders_PurchaseOrder_Lines_EditPriceWithoutUnpost = 2000330,
            Orders_PurchaseOrder_MainInfo_EditSupplier = 2000331,
            //[003] start
            Orders_PurchaseOrder_Lines_Can_Release = 2000333,
            //[003] end
            Orders_PurchaseOrder_EditCurrencyAndTermsForUnapproved = 2000336,
            Orders_PurchaseOrder_AddCurrencyAndTermsForUnapproved = 2000337,
            //[005] Code Start
            Orders_PurchaseOrder_ExportToExcel = 20010018,
            //[005] Code end
            Orders_PurchaseOrder_PORPDFDocument_Add = 20010019,
            Orders_PurchaseOrder_PORPDFDocument_Delete = 20010020,
            Orders_InternalPurchaseOrder_Line_EditPrice = 2000332,

            Orders_SalesOrder_MainInfo_Edit = 2000401,
            Orders_SalesOrder_MainInfo_Authorise = 2000402,
            Orders_SalesOrder_MainInfo_Deauthorise = 2000403,
            Orders_SalesOrder_Lines_Add = 2000404,
            Orders_SalesOrder_Lines_Edit = 2000405,
            Orders_SalesOrder_Lines_Post = 2000406,
            Orders_SalesOrder_Lines_Unpost = 2000407,
            Orders_SalesOrder_Lines_Delete = 2000408,
            Orders_SalesOrder_Lines_Allocate = 2000409,
            Orders_SalesOrder_Lines_Deallocate = 2000410,
            Orders_SalesOrder_Print = 2000411,
            Orders_SalesOrder_ProForma_Print = 2000412,
            Orders_SalesOrder_Email = 2000413,
            Orders_SalesOrder_ProForma_Email = 2000414,
            Orders_SalesOrder_SOReport_Print = 2000415,
            Orders_SalesOrder_MainInfo_Notify = 2000416,
            Orders_SalesOrder_MainInfo_Close = 2000417,
            Orders_SalesOrder_EditTax = 2000418,
            
            Orders_SalesOrder_EditCurrencyAndTerms = 2000534,
            Orders_SalesOrder_Lines_Close = 2000419,
            Orders_SalesOrder_Lines_CanAdd_NewLine = 2000420,
            Orders_SalesOrder_Lines_CanAdd_Req = 2000421,
            Orders_SalesOrder_Lines_CanAdd_Quote = 2000422,
            Orders_SalesOrder_Lines_CanAdd_SO = 2000423,
            Orders_SalesOrder_Lines_CanAdd_Stock = 2000424,
            Orders_SalesOrder_Lines_CanAdd_Service = 2000425,
            Orders_SalesOrder_MainInfo_EditShippingCosts = 2000426,
            Orders_SalesOrder_MainInfo_EditTax = 2000427,
           
            Orders_SalesOrder_MainInfo_EditCurrencyAndTerms = 2000440,
            Orders_SalesOrder_MainInfo_EditFieldsAfterAuthorisation = 2000428,
            Orders_SalesOrder_PDFDocument_Add = 2000429,
            Orders_SalesOrder_PDFDocument_Delete = 2000430,
            Orders_SalesOrder_View = 2000431,
            Orders_SalesOrder_MainInfo_Paid_Add = 2000432,
            Orders_SalesOrder_MainInfo_Paid_Edit = 2000433,
            Orders_SalesOrder_Lines_Edit_DateRequired = 2000434,

            Orders_SalesOrder_SORPDFDocument_Delete = 2000435,
            Orders_SalesOrder_Lines_EditDatePromisedAfterChecked = 2000436,
            Orders_SalesOrder_PDFDocument_DeleteAfterAuthorise = 2000437,
            Orders_SalesOrder_AllowCheckedCompanyOnStop = 2000438,
            //[004] start
            Orders_SalesOrder_MainInfo_Can_Pay_By_CreditCard = 2000439,
            //[004] end
            Orders_SalesOrder_Lines_EditAll = 2000441,
            //doc and excel
            Orders_SalesOrder_SOExcelDocDocument_Add = 2000442,
            Orders_SalesOrder_SOExcelDocDocument_Delete = 2000443,

            //LOT start
            Orders_SoLines_CanAdd_FromLot = 2000445,
            Orders_SoLines_CanDelete_FromLot = 2000446,
            //Lot code end
            //[005] Code Start
            Orders_SalesOrder_ExportToExcle = 20010019,
            //[005] Code end
            //code end
            Orders_SalesOrder_Authorise_RequestApproval = 20010027,
            Orders_SalesOrder_ExportApproval_RequestApproval = 20010044,
            Orders_SalesOrder_ExportApproval_SendApproval= 20010045,
            Orders_SalesOrder_ExportApproval_EditApproval = 20010046,
            Orders_SalesOrder_SOAuth_AllowReadyToShip = 30005032,
            Orders_SalesOrder_Authorise_EnableSendSONotify = 20010028,
            Orders_SalesOrder_Lines_ECCNLog= 2000444,

            Orders_Invoice_MainInfo_Edit = 2000501,
            Orders_Invoice_Lines_Add = 2000502,
            Orders_Invoice_Print = 2000503,
            Orders_Invoice_Email = 2000504,
            Orders_Invoice_PrintPackingSlip = 2000505,
            Orders_Invoice_EmailPackingSlip = 2000506,
            Orders_Invoice_PrintCertificateOfConformance = 2000507,
            Orders_Invoice_EmailCertificateOfConformance = 2000508,
            Orders_Invoice_Lines_Edit = 2000509,
            Orders_Invoice_Lines_Delete = 2000510,
            Orders_Invoice_Lines_EditAllocation = 2000511,
            Orders_Invoice_DeletedLines = 2000512,
            Orders_Invoice_Lines_CanAdd_NewLine = 2000513,
            Orders_Invoice_Lines_CanAdd_Service = 2000514,
            Orders_Invoice_MainInfo_Export = 2000515,
            Orders_Invoice_MainInfo_Release = 2000516,
            Orders_Invoice_MainInfo_EditDivision = 2000517,
            Orders_Invoice_PDFDocument_Add = 2000518,
            Orders_Invoice_PDFDocument_Delete = 2000519,
            Orders_Invoice_BulkEmail_Email = 2000520,
            Orders_Invoice_View = 2000521,
            Orders_Invoice_PrintInvoice = 2000522,
            Orders_Invoice_EmailInvoice = 2000523,
            Orders_Invoice_PrintEmailAfterExport = 2000524,
            Orders_Invoice_MainInfo_EditShippedDate = 2000526,
            Orders_Invoice_MainInfo_Edit_Term = 2000527,
            Orders_Invoice_Generate_XML = 20010031,

            Orders_CRMA_MainInfo_Edit = 2000601,
            Orders_CRMA_Lines_Edit = 2000602,
            Orders_CRMA_Lines_Add = 2000603,
            Orders_CRMA_Lines_Delete = 2000604,
            Orders_CRMA_Print = 2000605,
            Orders_CRMA_Email = 2000606,
            Orders_CRMA_Lines_Deallocate = 2000607,
            Orders_CRMA_PDFDocument_Add = 2000608,
            Orders_CRMA_PDFDocument_Delete = 2000609,
            Orders_CRMA_View = 2000610,

            Orders_SRMA_MainInfo_Edit = 2000701,
            Orders_SRMA_Lines_Edit = 2000702,
            Orders_SRMA_Lines_Add = 2000703,
            Orders_SRMA_Lines_Delete = 2000704,
            Orders_SRMA_Print = 2000705,
            Orders_SRMA_Email = 2000706,
            Orders_SRMA_PDFDocument_Add = 2000707,
            Orders_SRMA_PDFDocument_Delete = 2000708,
            Orders_SRMA_View = 2000709,
            Orders_SRMA_View_PDFDocument = 2000710,
            Orders_SRMA_View_HUBPrintAndEmail = 2000711,

            Orders_CreditNote_MainInfo_Edit = 2000801,
            Orders_CreditNote_Lines_Add = 2000802,
            Orders_CreditNote_Lines_Edit = 2000803,
            Orders_CreditNote_Lines_Delete = 2000804,
            Orders_CreditNote_Print = 2000805,
            Orders_CreditNote_Email = 2000806,
            Orders_CreditNote_Lines_CanAdd_CRMA = 2000807,
            Orders_CreditNote_Lines_CanAdd_Invoice = 2000808,
            Orders_CreditNote_Lines_CanAdd_Service = 2000809,
            Orders_CreditNote_View = 2000810,
            Orders_CreditNote_MainInfo_Export_Release = 2000811,
            Orders_CreditNote_Generate_XML = 20010032,

            Orders_DebitNote_MainInfo_Edit = 2000901,
            Orders_DebitNote_Lines_Add = 2000902,
            Orders_DebitNote_Lines_Edit = 2000903,
            Orders_DebitNote_Lines_Delete = 2000904,
            Orders_DebitNote_Print = 2000905,
            Orders_DebitNote_Email = 2000906,
            Orders_DebitNote_Lines_CanAdd_PO = 2000907,
            Orders_DebitNote_Lines_CanAdd_Service = 2000908,
            Orders_DebitNote_View = 2000909,
           // Orders_CreditNote_MainInfo_Release = 2000912,
            Orders_DebitNote_View_PrintAndEmail = 2000910,
            Orders_DebitNote_MainInfo_Export_Release = 2000911,

            Orders_Sourcing_View = 2001000,
            Orders_Sourcing_EditOffer = 2001001,
            Orders_Sourcing_RFQ = 2001002,
            Orders_Sourcing_AddToRequirement = 2001003,
            Orders_Sourcing_AddStockInfo = 2001004,
            Orders_Sourcing_EditStockInfo = 2001005,
            Orders_Sourcing_AddOffer = 2001006,
            Orders_Sourcing_AddTrusted = 2001007,
            Orders_Sourcing_AssignHUBRFQ= 30005030,
            Orders_BOMDetail_Import_Export_SourcingResult = 20010034,
            Orders_BOM_Add =2001008,
            Orders_BOM_View = 20010081,
            Orders_ClientInvoice_Add=2001009,
            Orders_ClientInvoice_MainInfo_Edit = 20010010,
            Orders_ClientInvoice_MainInfo_Notify = 20010011,
            Orders_ClientInvoice_URNNumber_Edit = 20010012,
            Orders_ClientInvoice_Lines_Add = 20010013,
            Orders_ClientInvoice_Lines_Delete = 20010014,
            Orders_BOM_AssignToMe = 20010015,
            Orders_Sourcing_AddStockImportTool = 20010016,
            Orders_Sourcing_AddStockImportEpoTool = 20010017,

            Orders_Sourcing_Edit_StrategicOffers_VirtualCostPrice= 20010022,
            Orders_Sourcing_Edit_ReverseLogistic_Bulk= 20010023,//[007]
            Orders_Sourcing_HideEdit_ReverseLogistic_Bulk = 20010024,//[007]
            Orders_ProspectiveCrossSelling = 20010025,
            Orders_ProsCrossSellingDetail = 20010029,
            Orders_Sourcing_BulkEdit_StrategicStock = 20010030,
            Orders_Sourcing_BulkEdit_StrategicStockLog = 20010049,

            Warehouse_ReceivePO = 3000001,
            Warehouse_ShipSO = 3000002,
            Warehouse_ReceiveCRMA = 3000003,
            Warehouse_ShipSRMA = 3000004,
            Warehouse_Stock_Add = 3000006,
            Warehouse_Service_Add = 3000007,
            Warehouse_Lot_Add = 3000008,
            Warehouse_SupplierInvoice_Add = 3000009,
            Warehouse_GIStockDocument =3000010,
            Warehouse_IHS_Allow_Export_CSV = 3000011,
            Warehouse_IHS_Allow_View_AvgPrice = 3000012,

            Warehouse_GoodsIn_MainInfo_Edit = 3000101,
            WarehouseSection_GILines_EditInspection = ********,
            WarehouseSection_GILines_QulityApprovalPermission = ********,
            WarehouseSection_EditGILines_AfterStatInspection= ********,
            Warehouse_GoodsIn_Lines_Edit = 3000102,
            Warehouse_GoodsIn_Lines_Delete = 3000103,
            Warehouse_GoodsIn_Lines_Print = 3000104,
            Warehouse_GoodsIn_Lines_Inspect = 3000105,
            Warehouse_GoodsIn_Lines_EditAfterInspection = 3000106,
            Warehouse_GoodsIn_MainInfo_EditAccountsInfo = 3000107,
            Warehouse_GoodsIn_MainInfo_EditExportedFlag = 3000108,
            Warehouse_GoodsIn_PDFDocument_Add = 3000109,
            Warehouse_GoodsIn_PDFDocument_Delete = 3000110,
            Warehouse_GoodsIn_Lines_Edit_PurchasePrice = 3000111,
            Warehouse_GoodsIn_Lines_Edit_ShipInCost = 3000112,
            Warehouse_GoodsIn_View = 3000113,
            Warehouse_GoodsIn_Lines_NPR_Delete = 3000114,
            Warehouse_GoodsIn_Lines_NPR_Authorise = 3000115,
            Warehouse_GoodsIn_Lines_NPR_NewNPR = 3000116,
            Warehouse_GoodsIn_Lines_NPR_CompletedBy = 3000117,
            Warehouse_GoodsIn_Lines_NPR_AfterAuthorise = 3000118,
            Warehouse_GoodsIn_Lines_Inspection = 3000119,
            Warehouse_GoodsIn_Documents_View = 3000120,
            Warehouse_GoodsIn_Lines_SplitGI = 3000121,
            Warehouse_GoodsIn_ShortShipmentDetails_Edit = 3000914,
            Warehouse_GoodsIn_ShortShipmentDetails_ClientView = 3000915,
            Warehouse_GoodsIn_ShortShipmentDetails_DownloadXLS = 3000916,
            Warehouse_GoodsIn_ShortShipmentDetails_ChangeStatusForShipAndHold = 3000917,
            Warehouse_GoodsIn_ShortShipmentDetails_ChangeStatusForCancelClose = 3000918,
            Warehouse_GoodsIn_ShortShipmentDetails_Add = 3000122,
            Warehouse_GoodsIn_Lines_AccountPermission = ********,
            Warehouse_Stock_MainInfo_Edit = 3000201,
            Warehouse_Stock_MainInfo_Split = 3000202,
            Warehouse_Stock_MainInfo_Quarantine = 3000203,
            Warehouse_Stock_MainInfo_Release = 3000204,
            Warehouse_Stock_Allocations_Deallocate = 3000205,
            Warehouse_Stock_Images_AddDelete = 3000206,
            Warehouse_Stock_PDFDocument_Add = 3000207,
            Warehouse_Stock_PDFDocument_Delete = 3000208,
            Warehouse_Stock_View = 3000209,
            Warehouse_Stock_Provision = 3000210,
            Warehouse_Stock_MainInfo_EditLot_On_Hold = 3000211,
            Warehouse_Service_MainInfo_Edit = 3000301,
            Warehouse_Service_MainInfo_Delete = 3000302,
            Warehouse_Service_Allocations_Deallocate = 3000303,
            Warehouse_Service_View = 3000304,
            
            Warehouse_Lot_MainInfo_Edit = 3000401,
            Warehouse_Lot_MainInfo_Delete = 3000402,
            Warehouse_Lot_Items_Stock_Transfer = 3000403,
            Warehouse_Lot_Items_Stock_Delete = 3000404,
            Warehouse_Lot_Items_Services_Transfer = 3000405,
            Warehouse_Lot_Items_Services_Delete = 3000406,
            Warehouse_Lot_View = 3000407,
            Warehouse_Lot_StockProvision = 3000408,
            Warehouse_ReceivePO_Receive = 3000501,
            Warehouse_ReceiveCRMA_Receive = 3000601,
            Warehouse_ShipSO_Ship = 3000701,
            Warehouse_ShipSO_SurchargeWavedOff = 3000702,
            Warehouse_ShipSRMA_Ship = 3000801,
            Warehouse_SupplierInvoice_View = 3000901,
            Warehouse_SupplierInvoice_MainInfo_Edit = 3000902,
            Warehouse_SupplierInvoice_MainInfo_Notify = 3000903,
            Warehouse_SupplierInvoice_URNNumber_Edit = 3000904,
            Warehouse_SupplierInvoice_Lines_Add = 3000905,
            Warehouse_SupplierInvoice_Lines_Delete = 3000906,
            Warehouse_GoodsIn_Lines_Edit_DownloadPDF = 3000910,
            Warehouse_GoodsIn_Lines_Edit_DownloadWord = 3000911,
            Warehouse_SupplierInvoice_MainInfo_CanExport = 3000907,
            Profile_MailMessages = 4000101,
            Profile_MailMessage_Add = 4000102,
            Profile_ToDo = 4000201,
            Profile_ToDo_Add = 4000203,
            Profile_ToDo_Edit = 4000204,
            Profile_ToDo_Delete = 4000205,
            Profile_ToDo_MarkComplete = 4000206,
            Warehouse_SupplierInvoice_AddUnReleasedGI = 20010040,
            Profile_ToDo_MarkIncomplete = 4000207,
            ViewReport_1 = 5000000,
            ViewReport_2 = 5000001,
            ViewReport_3 = 5000002,
            ViewReport_4 = 5000003,
            ViewReport_5 = 5000004,
            ViewReport_6 = 5000005,
            ViewReport_7 = 5000006,
            ViewReport_8 = 5000007,
            ViewReport_9 = 5000008,
            ViewReport_10 = 5000009,
            ViewReport_11 = 5000010,
            ViewReport_12 = 5000011,
            ViewReport_13 = 5000012,
            ViewReport_14 = 5000013,
            ViewReport_15 = 5000014,
            ViewReport_16 = 5000015,
            ViewReport_17 = 5000016,
            ViewReport_18 = 5000017,
            ViewReport_19 = 5000018,
            ViewReport_20 = 5000019,
            ViewReport_21 = 5000020,
            ViewReport_22 = 5000021,
            ViewReport_23 = 5000022,
            ViewReport_24 = 5000023,
            ViewReport_25 = 5000024,
            ViewReport_26 = 5000025,
            ViewReport_27 = 5000026,
            ViewReport_28 = 5000027,
            ViewReport_29 = 5000028,
            ViewReport_30 = 5000029,
            ViewReport_31 = 5000030,
            ViewReport_32 = 5000031,
            ViewReport_33 = 5000032,
            ViewReport_34 = 5000033,
            ViewReport_35 = 5000034,
            ViewReport_36 = 5000035,
            ViewReport_37 = 5000036,
            ViewReport_38 = 5000037,
            ViewReport_39 = 5000038,
            ViewReport_40 = 5000039,
            ViewReport_41 = 5000040,
            ViewReport_42 = 5000041,
            ViewReport_43 = 5000042,
            ViewReport_44 = 5000043,
            ViewReport_45 = 5000044,
            ViewReport_46 = 5000045,
            ViewReport_47 = 5000046,
            ViewReport_48 = 5000047,
            ViewReport_49 = 5000048,
            ViewReport_50 = 5000049,
            ViewReport_51 = 5000050,
            ViewReport_52 = 5000051,
            ViewReport_53 = 5000052,
            ViewReport_54 = 5000053,
            ViewReport_55 = 5000054,
            ViewReport_56 = 5000055,
            ViewReport_57 = 5000056,
            ViewReport_58 = 5000057,
            ViewReport_59 = 5000058,
            ViewReport_60 = 5000059,
            ViewReport_61 = 5000060,
            ViewReport_62 = 5000061,
            ViewReport_63 = 5000062,
            ViewReport_64 = 5000063,
            ViewReport_65 = 5000064,
            ViewReport_66 = 5000065,
            ViewReport_67 = 5000066,
            ViewReport_68 = 5000067,
            ViewReport_69 = 5000068,
            ViewReport_70 = 5000069,
            ViewReport_71 = 5000070,
            ViewReport_72 = 5000071,
            ViewReport_73 = 5000072,
            ViewReport_74 = 5000073,
            ViewReport_75 = 5000074,
            ViewReport_76 = 5000075,
            ViewReport_77 = 5000076,
            ViewReport_78 = 5000077,
            ViewReport_79 = 5000078,
            ViewReport_80 = 5000079,
            ViewReport_81 = 5000080,
            ViewReport_82 = 5000081,
            ViewReport_83 = 5000082,
            ViewReport_84 = 5000083,
            ViewReport_85 = 5000084,
            ViewReport_86 = 5000085,
            ViewReport_87 = 5000086,
            ViewReport_88 = 5000087,
            ViewReport_89 = 5000088,
            ViewReport_90 = 5000089,
            ViewReport_91 = 5000090,
            ViewReport_92 = 5000091,
            ViewReport_93 = 5000092,
            ViewReport_94 = 5000093,
            ViewReport_95 = 5000094,
            ViewReport_96 = 5000095,
            ViewReport_97 = 5000096,
            ViewReport_98 = 5000097,
            ViewReport_99 = 5000098,
            ViewReport_101 = 5000100,
            ViewReport_102 = 5000101,
            ViewReport_103 = 5000102,
            ViewReport_104 = 5000103,
            ViewReport_105 = 5000104,
            ViewReport_106 = 5000105,
            ViewReport_107 = 5000106,
            ViewReport_108 = 5000107,
            ViewReport_109 = 5000108,
            ViewReport_110 = 5000109,
            ViewReport_111 = 5000110,
            ViewReport_112 = 5000111,
            Setup_CompanySettings = 6010001,
            Setup_CompanySettings_Countries = 6010101,
            Setup_CompanySettings_Countries_Add = 6010102,
            Setup_CompanySettings_Countries_Edit = 6010103,
            Setup_CompanySettings_Currencies = 6010201,
            Setup_CompanySettings_Currencies_Add = 6010202,
            Setup_CompanySettings_Currencies_Edit = 6010203,
            Setup_CompanySettings_Currencies_EditRates = 6010204,
            Setup_CompanySettings_CurrencyRateHistory_Edit = 6010205,
            Setup_CompanySettings_CurrencyRateHistory_Delete = 6010206,
            Setup_CompanySettings_Divisions = 6010301,
            Setup_CompanySettings_ClientInvoiceHeader = 12000017,  // RP-2048 ClientInvoiceHeader
            Setup_CompanySettings_Divisions_Add = 6010302,
            Setup_CompanySettings_Divisions_Edit = 6010303,
            Setup_CompanySettings_Divisions_DocumentHeader_AddDelete = 6010304,
            Setup_CompanySettings_Products = 6010401,
            Setup_CompanySettings_Products_Add = 6010402,
            Setup_CompanySettings_Products_Edit = 6010403,
            Setup_CompanySettings_SequenceNumbers = 6010501,
            Setup_CompanySettings_ShippingMethods = 6010601,
            Setup_CompanySettings_ShippingMethods_Add = 6010602,
            Setup_CompanySettings_ShippingMethods_Edit = 6010603,
            Setup_CompanySettings_SourcingLinks = 6010701,
            Setup_CompanySettings_SourcingLinks_Add = 6010702,
            Setup_CompanySettings_SourcingLinks_Delete = 6010703,
            Setup_CompanySettings_SourcingLinks_Edit = 6010704,
            Setup_CompanySettings_Warehouses = 6010801,
            Setup_CompanySettings_Warehouses_Add = 6010802,
            Setup_CompanySettings_Warehouses_Edit = 6010803,
            Setup_CompanySettings_Warehouses_SetDefault = 6010804,
            Setup_CompanySettings_Taxes = 6010901,
            Setup_CompanySettings_Taxes_Add = 6010902,
            Setup_CompanySettings_Taxes_Edit = 6010903,
            Setup_CompanySettings_Taxes_EditRates = 6010904,
            Setup_CompanySettings_Taxes_DeleteFutureRate = 6010905,
            Setup_CompanySettings_Teams = 6011001,
            Setup_CompanySettings_Teams_Add = 6011002,
            Setup_CompanySettings_Teams_Edit = 6011003,
            Setup_CompanySettings_Terms = 6011101,
            Setup_CompanySettings_Terms_Add = 6011102,
            Setup_CompanySettings_Terms_Edit = 6011103,
            Setup_CompanySettings_PrintedDocuments = 6011201,
            Setup_CompanySettings_PrintedDocuments_HeaderImage_AddDelete = 6011202,
            Setup_CompanySettings_PrintedDocuments_DocumentFooters_Edit = 6011203,
            Setup_CompanySettings_MailGroups = 6011301,
            Setup_CompanySettings_MailGroups_Add = 6011302,
            Setup_CompanySettings_MailGroups_Edit = 6011303,
            Setup_CompanySettings_MailGroups_Delete = 6011304,
            Setup_CompanySettings_AppSettings = 6011401,
            Setup_CompanySettings_AppSettings_Edit = 6011402,
            //[002] code start
            Setup_CompanySettings_Printer = 6011501,
            Setup_CompanySettings_Printer_Add = 6011502,
            Setup_CompanySettings_Printer_Edit = 6011503,
            //[002] code end

            Setup_CompanySettings_LocalCurrencies = 6011601,
            Setup_CompanySettings_LocalCurrencies_Add = 6011602,
            Setup_CompanySettings_LocalCurrencies_Edit = 6011603,

            Setup_GlobalSettings_PPVBOMQualification = 6011604,
            Setup_GlobalSettings_PPVBOMQualification_Add = 6011605,
            Setup_GlobalSettings_PPVBOMQualification_Edit = 6011606,

            Setup_CompanySettings_OGELLicenses = 6011607,
            Setup_CompanySettings_OGELLicenses_Add = 6011608,
            Setup_CompanySettings_OGELLicenses_Edit = 6011609,

            Setup_CompanySettings_LabelPath = 6011701,
            Setup_CompanySettings_LabelPath_Add = 6011702,
            Setup_CompanySettings_LabelPath_Edit = 6011703,

            Setup_CompanySettings_GlobalTaxes = 6010904,
            Setup_CompanySettings_GlobalTaxes_Add = 6010905,
            Setup_CompanySettings_GlobalTaxes_Edit = 6010906,
            Setup_CompanySettings_GlobalTaxes_EditRates = 6010907,
            Setup_CompanySettings_GlobalTaxes_DeleteFutureRate = 6010908,


           
            Setup_CompanySettings_RestrictedManufacture = 6010909,
            Setup_CompanySettings_RestrictedManufacture_Add = 6010910,
            Setup_CompanySettings_RestrictedManufacture_Edit = 6010911,

            //[006] code start
            Setup_CompanySettings_ECCN = 6010912,
            Setup_CompanySettings_ECCN_Add = 6010913,
            Setup_CompanySettings_ECCN_Edit = 6010914,
            //[006] code end

            Setup_GlobalSettings = 6020001,
            Setup_GlobalSettings_CommunicationLogTypes = 6020101,
            Setup_GlobalSettings_CommunicationLogTypes_Add = 6020102,
            Setup_GlobalSettings_CommunicationLogTypes_Edit = 6020103,
            Setup_GlobalSettings_CompanyTypes = 6020201,
            Setup_GlobalSettings_PDFDocumentFileSize = 6030303,
            Setup_GlobalSettings_PDFDocumentFileSize_Edit = 6030304,
            Setup_GlobalSettings_PDFDocumentFileSize_Add = 6030305,
            Setup_GlobalSettings_CompanyType_Edit = 6020202,
            Setup_GlobalSettings_CompanyType_Add = 6020203,
            Setup_GlobalSettings_IndustryTypes = 6020301,

            Setup_GlobalSettings_EntertainmentType = 3000936,

            Setup_GlobalSettings_EntertainmentType_Add = 3000937,
            Setup_GlobalSettings_EntertainmentType_Edit = 3000938,
            Setup_GlobalSettings_IndustryType_Add = 6020302,
            Setup_GlobalSettings_IndustryType_Edit = 6020303,
            Setup_GlobalSettings_MasterCountryList = 6020401,
            Setup_GlobalSettings_MasterCountryList_Edit = 6020402,
            Setup_GlobalSettings_MasterCountryList_Add = 6020403,
            Setup_GlobalSettings_MasterCurrencyList = 6020501,
            Setup_GlobalSettings_MasterCurrencyList_Add = 6020502,
            Setup_GlobalSettings_MasterCurrencyList_Edit = 6020503,
            Setup_GlobalSettings_Packages = 6020601,
            Setup_GlobalSettings_Package_Add = 6020602,
            Setup_GlobalSettings_Package_Edit = 6020603,
            Setup_GlobalSettings_ProductTypes = 6020701,
            Setup_GlobalSettings_ProductType_Add = 6020702,
            Setup_GlobalSettings_ProductType_Edit = 6020703,
            Setup_GlobalSettings_Reasons = 6020801,
            Setup_GlobalSettings_Reason_Add = 6020802,
            Setup_GlobalSettings_Reason_Edit = 6020803,
            Setup_CompanySettings_StockLogReasons = 6020901,
            Setup_CompanySettings_StockLogReasons_Add = 6020902,
            Setup_CompanySettings_StockLogReasons_Edit = 6020903,
            Setup_GlobalSettings_CountingMethods = 6021001,
            Setup_GlobalSettings_CountingMethod_Add = 6021002,
            Setup_GlobalSettings_CountingMethod_Edit = 6021003,
            Setup_GlobalSettings_AppSettings = 6021101,
            Setup_GlobalSettings_AppSettings_Edit = 6021102,
            Setup_GlobalSettings_Incoterms = 6022001,
            Setup_GlobalSettings_Incoterm_Add = 6022002,
            Setup_GlobalSettings_Incoterm_Edit = 6022003,
            Setup_GlobalSettings_Client = 6022004,
            Setup_GlobalSettings_Client_Add = 6022005,
            Setup_GlobalSettings_Client_Edit = 6022006,
            Setup_GlobalSettings_MasterLogin = 6022007,
            Setup_GlobalSettings_MasterLogin_Confirm = 6022008,
            Setup_GlobalSettings_AS6081 = 6022009,
            Setup_GlobalSettings_AS6081_Add = 6022010,
            Setup_GlobalSettings_AS6081_Edit= 6022011,
            Setup_GlobalSettings_AS6081_Delete = 6022012,
            Setup_SecuritySettings = 6030001,
            Setup_SecuritySettings_GeneralPermissions_Edit = 6030101,
            Setup_SecuritySettings_GroupMembers_Edit = 6030102,
            Setup_SecuritySettings_Groups = 6030103,
            Setup_SecuritySettings_Groups_Add = 6030104,
            Setup_SecuritySettings_Groups_Delete = 6030105,
            Setup_SecuritySettings_Groups_Edit = 6030106,
            Setup_SecuritySettings_ReportPermissions_Edit = 6030107,
            Setup_SecuritySettings_Users = 6030108,
            Setup_SecuritySettings_Users_Add = 6030109,
            Setup_SecuritySettings_Users_Disable = 6030110,
            Setup_SecuritySettings_Users_Enable = 6030111,
            Setup_SecuritySettings_Users_Groups_Edit = 6030112,
            Setup_SecuritySettings_Users_Profile_ChangePassword = 6030113,
            Setup_SecuritySettings_Users_Profile_Edit = 6030114,
            Setup_SecuritySettings_Users_Profile_ResetPassword = 6030115,
            Setup_SecuritySettings_Groups_Clone = 6030116,
            Setup_SecuritySettings_Users_TransferAccounts = 6030117,
            Setup_CompanySettings_EmailComposer = 6030300,
            Setup_CompanySettings_EmailComposer_Add_Edit = 6030301,

            //
            Setup_GlobalSettings_Certificate = 6030400,
            Setup_GlobalSettings_Certificate_Add = 6030401,
            Setup_GlobalSettings_Certificate_Edit = 6030402,
            Setup_GlobalSettings_EightDCode = 6030403,

            Setup_GlobalSettings_Product = 6030501,
            DashboardPowerBI_ReportAllow = ********,
            DashboardPowerBISales_ReportAllow = ********,
            DashboardPowerBIStock_ReportAllow = ********,
            #region TabSecurityFunction

            Contact_AllCompanies_View = 10001,
            Contact_Customers_View = 10002,
            Contact_Suppliers_View = 10003,
            Contact_Prospects_View = 10004,
            Contact_Contacts_View = 10005,
            Orders_Requirements_View = 10006,
            Orders_Quotes_View = 10007,
            Orders_SalesOrders_View = 10008,
            Orders_Invoices_View = 10009,
            Orders_PurchaseOrders_View = 10010,
            Orders_PurchaseRequisitions_View = 10011,
            Orders_CustomerRMA_View = 10012,
            Orders_SupplierRMA_View = 10013,
            Orders_CreditNotes_View = 10014,
            Orders_DebitNotes_View = 10015,
            Orders_ClientInvoices_View = 10016,
            /*Internal Purchase Order*/
            Internal_Orders_CRMA_MainInfo_Edit  = 30005022,
            Internal_Orders_CRMA_Lines_Add = 30005023,
            Internal_Orders_CRMA_Lines_Edit = 30005024,
            Setup_GlobalSettings_InvoiceSetting=664545464,
            #endregion


            Utility_BOM_Import = 7000000,
            Utility_Offer_Import = 7000001,
            Utility_XMatch_Tool = 7000002,
            Utility_Stock_Import = 7000003,
            Utility_Log_Import = 7000004,
            Utility_Reverse_Logistics_Import_Tool = 7000005,
            Utility_Strategic_Offers_Import_Tool= 7000006,
                Utility_BOM_Manager_Import = 7000007,
            BOMManager = 7000008,
            Utility_PriceQuote_Import = 7000007,
            Utility_Offer_Import_Large = 7000010,
            Utility_ProspectiveOffer = 7000011,
            Utility_ProspectiveOfferDetail = 7000012
        }


    }
}
