///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber = function(element) { 
    Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.initializeBase(this, [element]);
    this._intGoodsInLineNo = -1;
    this._intInvoiceLineNo = -1;
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.prototype = {
    addPotentialStatusChange: function (handler) { this.get_events().addHandler("PotentialStatusChange", handler); },
    removePotentialStatusChange: function (handler) { this.get_events().removeHandler("PotentialStatusChange", handler); },
    onPotentialStatusChange: function () {
        var handler = this.get_events().getHandler("PotentialStatusChange");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
		this.addSearched(Function.createDelegate(this, this.doSearched));
	    //
	    //this.getField("ctlGroup").get_ddl._intGoodsInLineNo = 10;
		
		//alert($find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl"));
	    //this.getField("ctlGroup").ControlID
		//alert(this._intGoodsInLineNo);
		//$find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl")._intGoodsInLineNo = this._intGoodsInLineNo;
	},



	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intGoodsInLineNo = null;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.callBaseMethod(this, "dispose");
	},
	refereshGroup: function () {
	    $find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl")._intGoodsInLineNo = this._intGoodsInLineNo;
	    $find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl").getData();
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/GISerialNumber");
		this._objData.set_DataObject("GISerialNumber");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Group", this.getFieldValue("ctlGroup"));
	    this._objData.addParameter("Serial", this.getFieldValue("ctlSerialNo"));
		//this._objData.addParameter("serialNoLo", this.getFieldValue_Min("ctlSerialNo"));
		//this._objData.addParameter("serialNoHi", this.getFieldValue_Max("ctlSerialNo"));
	    this._objData.addParameter("GoodsInLineNo", this._intGoodsInLineNo);
	    this._objData.addParameter("InvoiceLineNo", this._intInvoiceLineNo);
	},
	getGroupValue: function () {
	    return this.getFieldValue("ctlGroup");
	},
	
	doGetDataComplete: function () {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
		    var row = this._objResult.Results[i];
		    var aryData = [
             $R_FN.setCleanTextValue( row.Group),
               row.SerialNo
		    ];
		    var objExtraData = {
		        SerialNoId: row.ID,
		        SubGroup: row.Group,
		        SerialNo: row.SerialNo
		    };
		    this._tblResults.addRow(aryData, row.ID, false, objExtraData);
		    aryData = null; row = null;
		   
		}
		//this.onPotentialStatusChange();
	},
	doSearched: function () {
	    this.onPotentialStatusChange();
	},
	refereshCRMAGroup: function () {
	    $find("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl")._intInvoiceLineNo = this._intInvoiceLineNo;
	    $find("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl").getData();
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
