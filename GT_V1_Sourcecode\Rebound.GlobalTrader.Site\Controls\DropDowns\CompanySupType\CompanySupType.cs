﻿using System;
using System.Web.UI;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns
{
    public partial class CompanySupType : Base
    {
        protected override void OnLoad(EventArgs e)
        {
            SetDropDownType("CompanySupType");
            AddScriptReference("Controls.DropDowns.CompanySupType.CompanySupType");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.CompanySupType", ClientID);
            base.OnLoad(e);
        }
    }
}