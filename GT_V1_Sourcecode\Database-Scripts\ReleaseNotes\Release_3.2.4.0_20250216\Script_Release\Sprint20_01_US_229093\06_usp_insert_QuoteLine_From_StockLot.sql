﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
==========================================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-216601]	    Phuc Hoang			12-Dec-2024		Update			216601: Quote - New status matrix
[US-229093]		Ngai To				21-Jan-2024		Update			229093: Quote - Add traffic light colouring coding based on date offered
==========================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_QuoteLine_From_StockLot]  
   @QuoteNo int            
 , @LotNo int           
 , @StockNo nvarchar(max) = NULL            
 , @UpdatedBy int = NULL           
 , @QuoteLineId int OUTPUT
 , @QuoteStatusName NVARCHAR(128) OUTPUT
                                          
AS                                           
  BEGIN                                          
  insert into  tbQuoteLine (QuoteNo,FullPart,Part,ManufacturerNo,DateCode,PackageNo,Quantity,   Price,      ProductNo,StockNo,ROHS,UpdatedBy,MSLLevel)          
  select @QuoteNo,  dbo.ufn_get_fullpart(Part), Part,ManufacturerNo,DateCode,PackageNo    ,ISNULL(QuantityInStock,0),ISNULL(ResalePrice,0),    
  ProductNo,StockId,ROHS,@UpdatedBy,MSLLevel from     tbStock    
  where LotNo=@LotNo and StockId in (select val from  dbo.SplitString(@StockNo,','))                                 
  SET @QuoteLineId = scope_identity()        
  if(@QuoteLineId>1)                            
  begin    
	update tbStock set BookedLotQuoteNo=@QuoteNo , IsBookedLotQuote=1  where LotNo=@LotNo and StockId in (select val from  dbo.SplitString(@StockNo,','));

	DECLARE @QuoteStatusNo INT = NULL;

	SELECT TOP 1 @QuoteStatusNo=QuoteStatus FROM tbQuote WHERE QuoteId = @QuoteNo;

	IF @QuoteStatusNo = 1 --Offered
	BEGIN 
		SET @QuoteStatusNo = 6; --Partially Offered
	END
	ELSE IF @QuoteStatusNo = 2 --Accepted
	BEGIN 
		SET @QuoteStatusNo = 7; --Partially Accepted
	END
	ELSE IF @QuoteStatusNo = 3 --Declined
	BEGIN 
		SET @QuoteStatusNo = 8; --Partially Declined
	END

	UPDATE tbQuote 
	SET QuoteStatus = @QuoteStatusNo
	WHERE QuoteId = @QuoteNo;

	IF @QuoteStatusNo = 1 OR @QuoteStatusNo = 6 -- Offered - Partially Offered
	BEGIN
		UPDATE tbQuote
		SET QuoteOfferedDate = CURRENT_TIMESTAMP
		WHERE QuoteId = @QuoteNo
	END

	SELECT TOP 1 @QuoteStatusName=[Name] FROM tbQuoteStatus WHERE QuoteStatusId = @QuoteStatusNo;
  end    
                  
END 
GO


