<%@ Control Language="C#" CodeBehind="SalesOrderLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrderLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlSalesOrderNo" runat="server" ResourceTitle="SalesOrderNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlOnlyFromIPO" runat="server" ResourceTitle="OnlyFromIPO" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerPO" runat="server" ResourceTitle="CustomerPO" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedFrom" runat="server" ResourceTitle="DatePromisedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDatePromisedTo" runat="server" ResourceTitle="DatePromisedTo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
