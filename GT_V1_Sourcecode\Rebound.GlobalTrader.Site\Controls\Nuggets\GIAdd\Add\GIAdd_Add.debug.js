///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.initializeBase(this, [element]);
	this._intNewID = 0;
	this._intCompanyID = 0;
	this._intLoginID = 0;
	this._strCompanyName = "";
};

Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.prototype = {

	get_ctlSelectPO: function() { return this._ctlSelectPO; }, 	set_ctlSelectPO: function(v) { if (this._ctlSelectPO !== v)  this._ctlSelectPO = v; }, 
	get_intLoginID: function() { return this._intLoginID; }, set_intLoginID: function(v) { if (this._intLoginID !== v) this._intLoginID = v; }, 
	get_intCompanyID: function() { return this._intCompanyID; }, 	set_intCompanyID: function(v) { if (this._intCompanyID !== v)  this._intCompanyID = v; }, 
	get_strCompanyName: function() { return this._strCompanyName; }, 	set_strCompanyName: function(v) { if (this._strCompanyName !== v)  this._strCompanyName = v; }, 
	get_ibtnSend: function() { return this._ibtnSend; }, 	set_ibtnSend: function(v) { if (this._ibtnSend !== v)  this._ibtnSend = v; }, 
	get_ibtnSend_Footer: function() { return this._ibtnSend_Footer; }, 	set_ibtnSend_Footer: function(v) { if (this._ibtnSend_Footer !== v)  this._ibtnSend_Footer = v; }, 
	get_ibtnContinue: function() { return this._ibtnContinue; }, 	set_ibtnContinue: function(v) { if (this._ibtnContinue !== v)  this._ibtnContinue = v; }, 
	get_ibtnContinue_Footer: function() { return this._ibtnContinue_Footer; }, 	set_ibtnContinue_Footer: function(v) { if (this._ibtnContinue_Footer !== v)  this._ibtnContinue_Footer = v; }, 
	
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.callBaseMethod(this, "initialize");
		this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
		this._ctlMail._ctlRelatedForm = this;
		this.addCancel(Function.createDelegate(this, this.cancelClicked));
		this.addSave(Function.createDelegate(this, this.saveClicked));
		this._ctlMultiStep.addStepChanged(Function.createDelegate(this, this.stepChanged));
		this._ctlSelectPO.addItemSelected(Function.createDelegate(this, this.selectPurchaseOrder));
		this.addFieldCheckBoxClickEvent("ctlSendMail", Function.createDelegate(this, this.chooseIfSendMail));
		var fnContinue = Function.createDelegate(this, this.finishedForm);
		$R_IBTN.addClick(this._ibtnContinue, fnContinue);
		$R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);
		var fnSend = Function.createDelegate(this, this.sendMail);
		$R_IBTN.addClick(this._ibtnSend, fnSend);
		$R_IBTN.addClick(this._ibtnSend_Footer,fnSend);
		this.formShown();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
		if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
		if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
		if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
		if (this._ctlSelectPO) this._ctlSelectPO.dispose();
		if (this._ctlMail) this._ctlMail.dispose();
		this._ctlMail = null;
		this._ctlSelectPO = null;
		this._ibtnContinue = null;
		this._ibtnContinue_Footer = null;
		this._ibtnSend = null;
		this._ibtnSend_Footer = null;
		this._intNewID = null;
		this._intCompanyID = null;
		this._intLoginID = null;
		this._strCompanyName = null;
		Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.callBaseMethod(this, "dispose");
	},
	
	formShown: function() {
		this.resetSteps();
		if (this._intCompanyID > 0) this.doInitialCompanySearch();
		this.gotoStep(1);
	},
	
	doInitialCompanySearch: function() {
		if (!this._ctlSelectPO._initialized) setTimeout(Function.createDelegate(this, this.doInitialCompanySearch), 100);
		this._ctlSelectPO.setFieldValue("ctlCompany",this._strCompanyName);
		this._ctlSelectPO.getData();
	},

	selectPurchaseOrder: function() {
		this._intPurchaseOrderID = this._ctlSelectPO.getSelectedID();
		this.getPurchaseOrder();
		this.nextStep(); 
	},
	
	getPurchaseOrder: function() {
		$R_FN.showElement(this._pnlLines, false);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/POMainInfo");
		obj.set_DataObject("POMainInfo");
		obj.set_DataAction("GetData");
		obj.addParameter("ID", this._intPurchaseOrderID);
		obj.addDataOK(Function.createDelegate(this, this.getPurchaseOrderOK));
		obj.addError(Function.createDelegate(this, this.getPurchaseOrderError));
		obj.addTimeout(Function.createDelegate(this, this.getPurchaseOrderError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getPurchaseOrderOK: function(args) {
		var res = args._result;
		this.setFieldsFromPurchaseOrder(res);
	},
	
	setFieldsFromPurchaseOrder: function(res) {
		if (!res) return;
		this.setFieldValue("ctlPurchaseOrder", res.PONumber);
		this.setFieldValue("ctlSupplier", res.SupplierName);
		this._intSupplierID = res.SupplierNo;
		this.getFieldDropDownData("ctlWarehouse");
		this.getFieldDropDownData("ctlShipVia");
		this.getFieldDropDownData("ctlCurrency");
		this.getFieldDropDownData("ctlReceivedBy");
		this.setFieldValue("ctlWarehouse", res.WarehouseNo);
		this.setFieldValue("ctlShipVia", res.ShipViaNo);
		this.setFieldValue("ctlCurrency", res.CurrencyNo);
		this.setFieldValue("ctlReceivedBy", this._intLoginID);
		this.setFieldValue("ctlDateReceived", $R_FN.shortDate());
		this.setFieldValue("ctlReceivingNotes", res.Instructions);
	},

	getPurchaseOrderError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	cancelClicked: function() {
		$R_FN.navigateBack();
	},
	
	stepChanged: function() {
		var intStep = this._ctlMultiStep._intCurrentStep;
		$R_IBTN.showButton(this._ibtnSend, intStep == 3);
		$R_IBTN.showButton(this._ibtnSend_Footer, intStep == 3);
		$R_IBTN.enableButton(this._ibtnSave, intStep == 2);
		$R_IBTN.enableButton(this._ibtnSave_Footer, intStep == 2);
		$R_IBTN.showButton(this._ibtnSave, intStep != 3);
		$R_IBTN.showButton(this._ibtnSave_Footer, intStep != 3);
		$R_IBTN.showButton(this._ibtnCancel, intStep != 3);
		$R_IBTN.showButton(this._ibtnCancel_Footer, intStep != 3);
		$R_IBTN.showButton(this._ibtnContinue, intStep == 3);
		$R_IBTN.showButton(this._ibtnContinue_Footer, intStep == 3);
		this._ctlMultiStep.showSteps(intStep != 3);
		if (intStep == 3) {
			this.getMessageText();
			this.setFieldValue("ctlSendMail", false);
			this.showMailButtons();
		}
	},
	
	saveClicked: function() {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GIAdd");
        obj.set_DataObject("GIAdd");
        obj.set_DataAction("AddNew");
        obj.addParameter("ShipViaNo", this.getFieldValue("ctlShipVia"));
        obj.addParameter("AirWayBill", this.getFieldValue("ctlAirWayBill"));
        obj.addParameter("Reference", this.getFieldValue("ctlReference"));
        obj.addParameter("CMNo", this._intSupplierID);
        obj.addParameter("Notes", this.getFieldValue("ctlReceivingNotes"));
        obj.addParameter("DateReceived", this.getFieldValue("ctlDateReceived"));
        obj.addParameter("ReceivedBy", this.getFieldValue("ctlReceivedBy"));
        obj.addParameter("PONo", this._intPurchaseOrderID);
        obj.addParameter("WarehouseNo", this.getFieldValue("ctlWarehouse"));
        obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.NewID > 0) {
			this._intNewID = args._result.NewID;
			this.showSaving(false);
			this.showInnerContent(true);
			this.nextStep();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = true;
		if (this._ctlMultiStep._intCurrentStep == 2) {
			if (!this.checkFieldEntered("ctlWarehouse")) blnOK = false;
			if (!this.checkFieldEntered("ctlReference")) blnOK = false;
			if (!this.checkFieldEntered("ctlReceivedBy")) blnOK = false;
			if (!this.checkFieldEntered("ctlDateReceived")) blnOK = false;
		}
		if (this._ctlMultiStep._intCurrentStep == 3) {
			if (!this._ctlMail.validateFields()) blnOK = false;
		}
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	showMailButtons: function() {
		var bln = this.getFieldValue("ctlSendMail");
		this.showField("ctlSendMailMessage", bln);
		$R_IBTN.showButton(this._ibtnSend, bln);
		$R_IBTN.showButton(this._ibtnSend_Footer, bln);
		$R_IBTN.showButton(this._ibtnContinue, !bln);
		$R_IBTN.showButton(this._ibtnContinue_Footer, !bln);
	},
	
	chooseIfSendMail: function() {
		this.showMailButtons();
	},
	
	getMessageText: function() {
		Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewGoodsIn(this._intNewID, Function.createDelegate(this, this.getMessageTextComplete));
	},
	
	getMessageTextComplete: function(strMsg) {
		this._ctlMail.setValue_Body(strMsg);
		this._ctlMail.setValue_Subject($R_RES.NewGoodsInAdded);
	},
	
	validateMailForm: function() {
		var blnOK = this._ctlMail.validateFields();
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	sendMail: function() {
		if (!this.validateMailForm()) return;
		Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), this._intNewID, Function.createDelegate(this, this.sendMailComplete));
		$R_IBTN.showButton(this._ibtnSave, false);
		$R_IBTN.showButton(this._ibtnSave_Footer, false);
		$R_IBTN.showButton(this._ibtnSend, false);
		$R_IBTN.showButton(this._ibtnSend_Footer, false);
	},
	
	sendMailComplete: function() {
		this.finishedForm();
	},
	
	finishedForm: function() {
		this._ctlMultiStep.showExplainLabel(false);
		this._ctlMultiStep.showSteps(false);
		$R_IBTN.showButton(this._ibtnSave, false);
		$R_IBTN.showButton(this._ibtnSave_Footer, false);
		$R_IBTN.showButton(this._ibtnSend, false);
		$R_IBTN.showButton(this._ibtnSend_Footer, false);
		this.showSavedOK(true);
		this.onSaveComplete();
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GIAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
