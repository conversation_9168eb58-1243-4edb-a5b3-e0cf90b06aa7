﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201556]		Ha<PERSON>			31-May-2024		CREATE			Get Detail Group code contact
[US-203247]		Trung Pham			08-Aug-2024		UPDATE			Get Inactive column
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_nugget_GetDetailsByNameOrCodeCompany] 
   @NameSearch nvarchar(50) = NULL,
   @CodeSearch nvarchar(50) = NULL,
   @CodeType nvarchar(100) = NULL        
      
    WITH RECOMPILE        
      
AS         
BEGIN  
    SET NOCOUNT ON;
     
    BEGIN  
        WITH cteSearch AS (
            SELECT ItemId as Id,
                   Code,
                   ContactName as [Name],
				   Inactive
            FROM dbo.tbContactGroup
            WHERE ((@NameSearch IS NULL) OR (ContactName LIKE @NameSearch + '%'))
              AND ((@CodeSearch IS NULL) OR (Code LIKE @CodeSearch + '%'))
              AND ((@CodeType IS NULL) OR (ContactGroupType = @CodeType))
              AND (ItemId IS NOT NULL)
        )          
        SELECT *              
        FROM cteSearch;  
    END  
END;
GO


