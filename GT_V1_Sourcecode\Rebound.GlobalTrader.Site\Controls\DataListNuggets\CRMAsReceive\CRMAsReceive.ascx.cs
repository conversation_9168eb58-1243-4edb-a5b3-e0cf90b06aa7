//-----------------------------------------------------------------------------------------
// RP 23.12.2009:
// - render state on server
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class CRMAsReceive : Base {

		#region Properties

		private bool _blnShowAllOrders = false;
		public bool ShowAllOrders {
			get { return _blnShowAllOrders; }
			set { _blnShowAllOrders = value; }
		}
        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("CRMAsReceive");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "CRMAsReceive");
			AddScriptReference("Controls.DataListNuggets.CRMAsReceive.CRMAsReceive.js");
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CRMAsReceive", ctlDesignBase.ClientID);
			base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
			int intTab = 0;
			if (string.IsNullOrEmpty(_objQSManager.SearchPartNo)) {
				var strCallType = this.GetSavedStateValue("CallType").ToUpper();
				if (string.IsNullOrEmpty(strCallType)) strCallType = "READY";
				if (strCallType == "READY") intTab = 0;
				if (strCallType == "ALL") intTab = 1;
			}
			((Pages.Content)Page).CurrentTab = intTab;
			this.OnAskPageToChangeTab();
			base.RenderAdditionalState();
		}

		protected override void OnPreRender(EventArgs e) {
			_scScriptControlDescriptor.AddProperty("blnShowAllOrders", _blnShowAllOrders);
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
			base.OnPreRender(e);
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Quantity", "QuantityOutstanding", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
			_tbl.Columns.Add(new FlexiDataColumn("CustomerRMADate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			_tbl.Columns.Add(new FlexiDataColumn("InvoiceNo", Unit.Empty, true));
            if (_IsGlobalLogin == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("Client", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            }
		}

	}
}