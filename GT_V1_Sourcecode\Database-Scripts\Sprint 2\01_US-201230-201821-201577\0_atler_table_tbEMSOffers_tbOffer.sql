/*Alter table tbEMSOffers*/
IF COL_LENGTH('dbo.tbEMSOffers', 'SupplierWarranty') IS NULL
BEGIN
   ALTER TABLE tbEMSOffers ADD SupplierWarranty INT NULL
END

IF COL_LENGTH('dbo.tbEMSOffers', 'CountryOfOriginNo') IS NULL
BEGIN
   ALTER TABLE tbEMSOffers ADD CountryOfOriginNo INT NULL
END

IF COL_LENGTH('dbo.tbEMSOffers', 'SellPrice') IS NULL
BEGIN
   ALTER TABLE tbEMSOffers ADD SellPrice FLOAT NULL
END

IF COL_LENGTH('dbo.tbEMSOffers', 'SellPriceLessReason') IS NULL
BEGIN
   ALTER TABLE tbEMSOffers ADD SellPriceLessReason NVARCHAR(MAX) NULL
END

IF COL_LENGTH('dbo.tbEMSOffers', 'ShippingCost') IS NULL
BEGIN
   ALTER TABLE tbEMSOffers ADD ShippingCost FLOAT NULL
END

IF COL_LENGTH('dbo.tbEMSOffers', 'RegionNo') IS NULL
BEGIN
   ALTER TABLE tbEMSOffers ADD RegionNo INT NULL
END

IF COL_LENGTH('dbo.tbEMSOffers', 'DeliveryDate') IS NULL
BEGIN
   ALTER TABLE tbEMSOffers ADD DeliveryDate DATETIME NULL
END

IF COL_LENGTH('dbo.tbEMSOffers', 'TestingRecommended') IS NULL
BEGIN
   ALTER TABLE tbEMSOffers ADD TestingRecommended BIT NULL
END

/*Alter table BorisGlobalTraderImports.dbo.tbOffer*/
IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'SupplierWarranty') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD SupplierWarranty INT NULL
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'CountryOfOriginNo') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD CountryOfOriginNo INT NULL
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'SellPrice') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD SellPrice FLOAT NULL
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'SellPriceLessReason') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD SellPriceLessReason NVARCHAR(MAX) NULL
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'ShippingCost') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD ShippingCost FLOAT NULL
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'RegionNo') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD RegionNo INT NULL
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'DeliveryDate') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD DeliveryDate DATETIME NULL
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'TestingRecommended') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD TestingRecommended BIT NULL
END

IF COL_LENGTH('BorisGlobalTraderImports.dbo.tbOffer', 'AlternateStatus') IS NULL
BEGIN
   ALTER TABLE BorisGlobalTraderImports.dbo.tbOffer ADD AlternateStatus BIT NULL
END