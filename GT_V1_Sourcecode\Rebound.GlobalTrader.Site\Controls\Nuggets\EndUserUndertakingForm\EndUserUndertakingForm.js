Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.initializeBase(this,[n]);this._intSalesOrderID=-1;this._intLineID=-1;this.solineIds=[];this._ibtnEEUTemp=null};Array.prototype.remove=function(n){return this.indexOf(n)!==-1?(this.splice(this.indexOf(n),1),!0):!1};Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.prototype={get_intSalesOrderID:function(){return this._intSalesOrderID},set_intSalesOrderID:function(n){this._intSalesOrderID!==n&&(this._intSalesOrderID=n)},get_tblAll:function(){return this._tblAll},set_tblAll:function(n){this._tblAll!==n&&(this._tblAll=n)},get_ibtnEUUForm:function(){return this._ibtnEUUForm},set_ibtnEUUForm:function(n){this._ibtnEUUForm!==n&&(this._ibtnEUUForm=n)},addRefreshEvent:function(n){this.get_events().addHandler("Refresh",n)},removeRefreshEvent:function(n){this.get_events().removeHandler("Refresh",n)},onRefreshChange:function(){var n=this.get_events().getHandler("RefreshChange");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){this._strDataPath="controls/Nuggets/EndUserUndertakingForm";this._strDataObject="EndUserUndertakingForm";Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.callBaseMethod(this,"initialize");this.addRefreshEvent(Function.createDelegate(this,this.getTabData_All));this.getTabData_All();this.getEEUFileData()},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.callBaseMethod(this,"dispose")},getTabData_All:function(){var t=new Date,i=t.getTime(),n;this.getData_Start();n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines_All");n.addParameter("id",this._intSalesOrderID);n.addParameter("CurrentTime",i);n.addParameter("GetForUserUnderTakingForm","EUU");n.addDataOK(Function.createDelegate(this,this.getTabDataOK_All));n.addError(Function.createDelegate(this,this.getTabDataError));n.addTimeout(Function.createDelegate(this,this.getTabDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getTabDataOK_All:function(n){this.showLoading(!1);this._tblAll.clearTable();this.solineIds=[];this.addLinesData(this._tblAll,n._result,"LinesAll");this._tblAll.resizeColumns()},getTabDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCurrentTable:function(){return this._tblAll},addLinesData:function(n,t,i){var n=this.getCurrentTable(),u;if(n.enable(!0),this._intSalesOrderLineID>0&&(this._intLineID=this._intSalesOrderLineID),t[i])for(u=0;u<t[i].length;u++){var r=t[i][u],f=r.ServiceNo>0,s=r.IsSourcingResultExist,e=[r.LineNo,$R_FN.writeDoubleCellValue($R_FN.writePartNo(r.Part,r.ROHS),$R_FN.setCleanTextValue(r.CustomerPart)),r.EUUFormRequired?"Yes":"No",r.EUUPDFUploadDate,r.EUUUPDFploadName,r.EUUFormRequired?r.IsEUUPDFAvailable?String.format("&nbsp;&nbsp;<center><a href=\"javascript:void(0);\" onclick=\"$RGT_openEndUserUndertakingDoc({0})\" title=\"Click to View and add docs\"><img border='0'  src=app_themes/original/images/IconButton/pdficon.jpg width='30' height='26'><\/center><\/a>",r.LineID):String.format('&nbsp;&nbsp;<a href="javascript:void(0);" onclick="$RGT_openEndUserUndertakingDoc({0})" style=\'text-decoration:none;\' title="Click to add docs"><center><b><img border=\'0\'  src=app_themes/Original/images/buttons/sourcing/history_add.gif ><\/b><\/center><\/a>',r.LineID):""],o={IsService:f,Inactive:r.Inactive,lEccnCode:r.ECCNCode,lEccnCodeNo:r.ECCNCodeNo,lPart:r.Part};n.addRow(e,r.LineID,r.LineID==this._intLineID,o,"");r=null}this._intLineCount=n.countRows();i=="LinesAll"&&(this._blnContainLine=this._intLineCount>0);$R_FN.setInnerHTML(this._lblSubTotal,t.SubTotal);$R_FN.setInnerHTML(this._lblFreight,t.Freight);$R_FN.setInnerHTML(this._lblTax,t.Tax);$R_FN.setInnerHTML(this._lblTotal,t.Total);this._bnlIsConfirmedAll=t.IsConfirmedAll;this._totalLinePrice=t.TotalVal;this._ibtnPostAll&&$R_IBTN.showButton(this._ibtnPostAll,this._aryLinesForPosting.length>0);this._ibtnUnpostAll&&$R_IBTN.showButton(this._ibtnUnpostAll,this._aryLinesForUnposting.length>0);this.showContent(!0);this.showContentLoading(!1);this._intSalesOrderLineID>0&&(this._intSalesOrderLineID=-1);this._anyLinePosted=t.AnyLinePosted;this._totalFreight=t.TotalFreight;this._taxRate=t.TaxRate;this.onRefreshChange()},getEEUFileData:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/SetupNuggets/SourcingLinks");n.set_DataObject("SourcingLinks");n.set_DataAction("GetData");n.addDataOK(Function.createDelegate(this,this.getEEUFileDataOK));n.addError(Function.createDelegate(this,this.getEEUFileDataError));n.addTimeout(Function.createDelegate(this,this.getEEUFileDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getEEUFileDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getEEUFileDataOK:function(n){var r=n._result,u,t,i;if($R_FN.setInnerHTML(this._ibtnEUUForm,""),u="",r.Items)for(t=0;t<r.Items.length;t++)i=r.Items[t],i.Name=="EUU Forms"&&(u+=i.URL),i=null;$("#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEUUForm_hyp").attr("href",u);$("#ctl00_cphMain_SOEndUserUndertakingForm_ctlDB_ctl12_ibtnEUUForm_hyp").attr("target","_blank");this.getDataOK_End()}};Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.EndUserUndertakingForm",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);