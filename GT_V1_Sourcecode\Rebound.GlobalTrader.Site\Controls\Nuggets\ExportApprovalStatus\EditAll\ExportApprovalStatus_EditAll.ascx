<%--
Marker     Changed by      Date         Remarks
[001]      A<PERSON><PERSON><PERSON>  09/09/2021   Add update supplier approval details form
[002]      A<PERSON><PERSON><PERSON>  27/10/2021   Add new draft button.
--%>
<%@ Control Language="C#" CodeBehind="ExportApprovalStatus_EditAll.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_EditAll" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
	
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ExportApproval_Edit")%></Explanation>
	
	<Content>
	<!-- Step 1 ------------------------------------------------------------------->
		<ReboundUI_Table:Form id="frmStep1" runat="server">
             <ReboundUI_Form:FormField id="ctlDestinationCountry" runat="server" FieldID="ddlDestinationCountry" ResourceTitle="EndDestinationCountry" IsRequiredField="true" >
                <Field><ReboundDropDown:Country ID="ddlDestinationCountry" runat="server" IncludeNoValue="true" NoValue_Value="" InitialValue="" /></Field>
            </ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlMilitaryuse" runat="server" FieldID="ddlMilitaryuse" ResourceTitle="OgelMilitaryUse" IsRequiredField="true" >
                <Field><ReboundDropDown:Military ID="ddlMilitaryuse" runat="server" IncludeNoValue="true" NoValue_Value="" InitialValue="" /></Field>
            </ReboundUI_Form:FormField>

              
            <ReboundUI_Form:FormField id="ctlEndUser" runat="server" FieldID="txtEndUser" ResourceTitle="OgelEndUser" IsRequiredField="true" >
				<Field><ReboundUI:ReboundTextBox ID="txtEndUser" runat="server" Style="height:49px; width:310px" Width="310" TextMode="MultiLine" Rows="2" CountChar="true" /></Field>          
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
		
		
          
	</Content>
	
</ReboundUI_Form:DesignBase>



