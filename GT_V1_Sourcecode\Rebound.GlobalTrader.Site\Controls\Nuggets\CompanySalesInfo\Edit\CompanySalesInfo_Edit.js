Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.initializeBase(this,[n]);this._intCompanyID=-1;this._blnFirstTimeApprovedClicked=!0;this._blnApproved=!1};Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_lblCurrency_CreditLimit:function(){return this._lblCurrency_CreditLimit},set_lblCurrency_CreditLimit:function(n){this._lblCurrency_CreditLimit!==n&&(this._lblCurrency_CreditLimit=n)},get_intDefaultRating:function(){return this._intDefaultRating},set_intDefaultRating:function(n){this._intDefaultRating!==n&&(this._intDefaultRating=n)},get_intMailGroupNo:function(){return this._intMailGroupNo},set_intMailGroupNo:function(n){this._intMailGroupNo!==n&&(this._intMailGroupNo=n)},get_lblCurrency_ActualCreditLimit:function(){return this._lblCurrency_ActualCreditLimit},set_lblCurrency_ActualCreditLimit:function(n){this._lblCurrency_ActualCreditLimit!==n&&(this._lblCurrency_ActualCreditLimit=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._chkApproved&&this._chkApproved.dispose(),this._intCompanyID=null,this._lblCurrency_CreditLimit=null,this._chkApproved=null,this._intMailGroupNo=null,this._strMessage=null,this._blnApproved=null,this._lblCurrency_ActualCreditLimit=null,Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.callBaseMethod(this,"dispose"))},formShown:function(){this.showField("ctlShipVia",!1);this.showField("ctlShippingAccountNo",!1);this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._chkApproved=$find(this.getField("ctlIsApproved").ControlID),this._chkApproved.addClick(Function.createDelegate(this,this.approveClicked)));this.getFieldControl("ctlSalesperson")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlCurrency")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlTerms")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlShipVia")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldControl("ctlInsuredAmountCurrency")._intGlobalLoginClientNo=this._globalLoginClientNo;this.getFieldDropDownData("ctlCurrency");this.getFieldDropDownData("ctlSalesperson");this.getFieldDropDownData("ctlTerms");this.getFieldDropDownData("ctlShipVia");this.getFieldDropDownData("ctlContact");this.getFieldDropDownData("ctlPreferredWarehouse");this._blnApproved=this._chkApproved._blnChecked;this.getFieldDropDownData("ctlInsuredAmountCurrency")},saveClicked:function(){if(this.validateForm()&&this.validateCustomerNo()&&this.InsuredCurrencyAmount()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CompanySalesInfo");n.set_DataObject("CompanySalesInfo");n.set_DataAction("SaveEdit");n.addParameter("id",this._intCompanyID);n.addParameter("Salesman",this.getFieldValue("ctlSalesperson"));n.addParameter("IsApproved",this.getFieldValue("ctlIsApproved"));n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency"));n.addParameter("CustomerNo",this.getFieldValue("ctlCustomerNo"));n.addParameter("Rating",this.getFieldValue("ctlRating"));n.addParameter("TermsNo",this.getFieldValue("ctlTerms"));n.addParameter("OnStop",this.getFieldValue("ctlIsOnStop"));n.addParameter("IsShippingWaived",this.getFieldValue("ctlIsShippingWaived"));n.addParameter("ShipViaNo",null);n.addParameter("ShippingAccountNo",null);n.addParameter("ContactNo",this.getFieldValue("ctlContact"));n.addParameter("CreditLimit",this.getFieldValue("ctlCreditLimit"));n.addParameter("InsuranceFileNo",this.getFieldValue("ctlInsuranceFileNo"));n.addParameter("InsuredAmount",this.getFieldValue("ctlInsuredAmount"));n.addParameter("StopStatus",this.getFieldValue("ctlStopStatus"));n.addParameter("NotesToInvoice",this.getFieldValue("ctlNotesToInvoice"));n.addParameter("ActualCreditLimit",this.getFieldValue("ctlActualCreditLimit"));n.addParameter("WarehouseNo",this.getFieldValue("ctlPreferredWarehouse"));n.addParameter("InsuredAmountCurrencyNo",this.getFieldValue("ctlInsuredAmountCurrency"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(!this._blnApproved&&this._chkApproved._blnChecked&&this.getMessageText(),this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()},cancelClicked:function(){this.onCancel()},setCurrencyLabel:function(n){$R_FN.setInnerHTML(this._lblCurrency_CreditLimit,n);$R_FN.setInnerHTML(this._lblCurrency_ActualCreditLimit,n)},approveClicked:function(){this._blnFirstTimeApprovedClicked&&this._chkApproved._blnChecked&&this.setFieldValue("ctlRating",this._intDefaultRating);this._blnFirstTimeApprovedClicked=!1},validateCustomerNo:function(){return this.getFieldValue("ctlIsApproved")==!0&&this.checkFieldEntered("ctlCustomerNo")==!1?(this.showError(!0,$R_RES.CustomerNoMessage),!1):!0},InsuredCurrencyAmount:function(){return this.getFieldValue("ctlInsuredAmount")>0&&this.checkFieldEntered("ctlInsuredAmountCurrency")==!1?(this.showError(!0,$R_RES.InsuredCurrencyAmount),!1):!0},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_CompanySO(this._intCompanyID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this.sendMail(n)},sendMail:function(n){Rebound.GlobalTrader.Site.WebServices.NotifyMessage("",this._intMailGroupNo,$R_RES.CompanyApproveSubject,n,this._intCompanyID,Function.createDelegate(this,this.sendMailComplete))},sendMailComplete:function(){}};Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanySalesInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);