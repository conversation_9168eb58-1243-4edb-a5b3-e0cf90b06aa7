Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.ContactsForCompany=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.ContactsForCompany.initializeBase(this,[n]);this._intCompanyID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.ContactsForCompany.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.ContactsForCompany.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.DropDowns.ContactsForCompany.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/ContactsForCompany");this._objData.set_DataObject("ContactsForCompany");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.Items)for(n=0;n<t.Items.length;n++)this.addOption(t.Items[n].Name,t.Items[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.ContactsForCompany.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.ContactsForCompany",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);