//Code Merge for GI Screen
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class MyGIQueries : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets the main data
        /// </summary>
        private void GetData()
        {
            try
            {
                List<BLL.GoodsInLine> lst = null;
                lst = GoodsInLine.GetMyGIQueries(LoginID, SessionManager.ClientID);
                if (lst == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    jsn.AddVariable("Count", lst.Count);
                    for (int i = 0; i < lst.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("GoodsInId", lst[i].GoodsInId);
                        jsnItem.AddVariable("GINumber", lst[i].GoodsInNo);
                        jsnItem.AddVariable("GoodsInLineId", lst[i].GoodsInLineId);
                        jsnItem.AddVariable("SalesOrderNumber", lst[i].SalesOrderNumber);
                        jsnItem.AddVariable("SalesOrderNo", lst[i].SalesOrderNo);
                        jsnItem.AddVariable("DateSent", Functions.FormatDate(lst[i].DeliveryDate, false, true));
                        jsnItem.AddVariable("Approvers", lst[i].Approvers);
                        jsnItem.AddVariable("Status", lst[i].Status);

                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("Items", jsnItems);
                    jsnItems.Dispose();
                    jsnItems = null;
                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lst = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


    }
}
