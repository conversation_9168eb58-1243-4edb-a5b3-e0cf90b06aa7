///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.Orders = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Orders.Orders.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Orders.Orders.prototype = {

	get_ctlEllipses_CusReqs: function() { return this._ctlEllipses_CusReqs; }, 	set_ctlEllipses_CusReqs: function(v) { if (this._ctlEllipses_CusReqs !== v)  this._ctlEllipses_CusReqs = v; }, 
	get_txtCusReqNo: function() { return this._txtCusReqNo; }, 	set_txtCusReqNo: function(v) { if (this._txtCusReqNo !== v)  this._txtCusReqNo = v; }, 
	get_ibtnGo_CusReq: function() { return this._ibtnGo_CusReq; }, 	set_ibtnGo_CusReq: function(v) { if (this._ibtnGo_CusReq !== v)  this._ibtnGo_CusReq = v; }, 
	get_ctlEllipses_Quotes: function() { return this._ctlEllipses_Quotes; }, 	set_ctlEllipses_Quotes: function(v) { if (this._ctlEllipses_Quotes !== v)  this._ctlEllipses_Quotes = v; }, 
	get_txtQuoteNo: function() { return this._txtQuoteNo; }, 	set_txtQuoteNo: function(v) { if (this._txtQuoteNo !== v)  this._txtQuoteNo = v; }, 
	get_ibtnGo_Quote: function() { return this._ibtnGo_Quote; }, 	set_ibtnGo_Quote: function(v) { if (this._ibtnGo_Quote !== v)  this._ibtnGo_Quote = v; }, 
	get_ctlEllipses_SalesOrders: function() { return this._ctlEllipses_SalesOrders; }, 	set_ctlEllipses_SalesOrders: function(v) { if (this._ctlEllipses_SalesOrders !== v)  this._ctlEllipses_SalesOrders = v; }, 
	get_txtSalesOrderNo: function() { return this._txtSalesOrderNo; }, 	set_txtSalesOrderNo: function(v) { if (this._txtSalesOrderNo !== v)  this._txtSalesOrderNo = v; }, 
	get_ibtnGo_SalesOrder: function() { return this._ibtnGo_SalesOrder; }, 	set_ibtnGo_SalesOrder: function(v) { if (this._ibtnGo_SalesOrder !== v)  this._ibtnGo_SalesOrder = v; }, 
	get_ctlEllipses_Invoices: function() { return this._ctlEllipses_Invoices; }, 	set_ctlEllipses_Invoices: function(v) { if (this._ctlEllipses_Invoices !== v)  this._ctlEllipses_Invoices = v; }, 
	get_txtInvoiceNo: function() { return this._txtInvoiceNo; }, 	set_txtInvoiceNo: function(v) { if (this._txtInvoiceNo !== v)  this._txtInvoiceNo = v; }, 
	get_ibtnGo_Invoice: function() { return this._ibtnGo_Invoice; }, 	set_ibtnGo_Invoice: function(v) { if (this._ibtnGo_Invoice !== v)  this._ibtnGo_Invoice = v; }, 
	get_ctlEllipses_PurchaseOrders: function() { return this._ctlEllipses_PurchaseOrders; }, 	set_ctlEllipses_PurchaseOrders: function(v) { if (this._ctlEllipses_PurchaseOrders !== v)  this._ctlEllipses_PurchaseOrders = v; }, 
	get_txtPurchaseOrderNo: function() { return this._txtPurchaseOrderNo; }, 	set_txtPurchaseOrderNo: function(v) { if (this._txtPurchaseOrderNo !== v)  this._txtPurchaseOrderNo = v; }, 
	get_ibtnGo_PurchaseOrder: function() { return this._ibtnGo_PurchaseOrder; }, 	set_ibtnGo_PurchaseOrder: function(v) { if (this._ibtnGo_PurchaseOrder !== v)  this._ibtnGo_PurchaseOrder = v; }, 
	get_ctlEllipses_CRMAs: function() { return this._ctlEllipses_CRMAs; }, 	set_ctlEllipses_CRMAs: function(v) { if (this._ctlEllipses_CRMAs !== v)  this._ctlEllipses_CRMAs = v; }, 
	get_txtCRMANo: function() { return this._txtCRMANo; }, 	set_txtCRMANo: function(v) { if (this._txtCRMANo !== v)  this._txtCRMANo = v; }, 
	get_ibtnGo_CRMA: function() { return this._ibtnGo_CRMA; }, 	set_ibtnGo_CRMA: function(v) { if (this._ibtnGo_CRMA !== v)  this._ibtnGo_CRMA = v; }, 
	get_ctlEllipses_SRMAs: function() { return this._ctlEllipses_SRMAs; }, 	set_ctlEllipses_SRMAs: function(v) { if (this._ctlEllipses_SRMAs !== v)  this._ctlEllipses_SRMAs = v; }, 
	get_txtSRMANo: function() { return this._txtSRMANo; }, 	set_txtSRMANo: function(v) { if (this._txtSRMANo !== v)  this._txtSRMANo = v; }, 
	get_ibtnGo_SRMA: function() { return this._ibtnGo_SRMA; }, 	set_ibtnGo_SRMA: function(v) { if (this._ibtnGo_SRMA !== v)  this._ibtnGo_SRMA = v; }, 
	get_ctlEllipses_PurReqs: function() { return this._ctlEllipses_PurReqs; }, 	set_ctlEllipses_PurReqs: function(v) { if (this._ctlEllipses_PurReqs !== v)  this._ctlEllipses_PurReqs = v; }, 
	get_ctlEllipses_Credits: function() { return this._ctlEllipses_Credits; }, 	set_ctlEllipses_Credits: function(v) { if (this._ctlEllipses_Credits !== v)  this._ctlEllipses_Credits = v; }, 
	get_txtCreditNo: function() { return this._txtCreditNo; }, 	set_txtCreditNo: function(v) { if (this._txtCreditNo !== v)  this._txtCreditNo = v; }, 
	get_ibtnGo_Credit: function() { return this._ibtnGo_Credit; }, 	set_ibtnGo_Credit: function(v) { if (this._ibtnGo_Credit !== v)  this._ibtnGo_Credit = v; }, 
	get_ctlEllipses_Debits: function() { return this._ctlEllipses_Debits; }, 	set_ctlEllipses_Debits: function(v) { if (this._ctlEllipses_Debits !== v)  this._ctlEllipses_Debits = v; }, 
	get_txtDebitNo: function() { return this._txtDebitNo; }, 	set_txtDebitNo: function(v) { if (this._txtDebitNo !== v)  this._txtDebitNo = v; }, 
	get_ibtnGo_Debit: function() { return this._ibtnGo_Debit; }, 	set_ibtnGo_Debit: function(v) { if (this._ibtnGo_Debit !== v)  this._ibtnGo_Debit = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Orders.Orders.callBaseMethod(this, "initialize");
	},

	goInit: function() {
		this._strPathToData = "controls/LeftNuggets/QuickJump";
		this._strDataObject = "QuickJump";
		$R_IBTN.addClick(this._ibtnGo_SalesOrder, Function.createDelegate(this, this.goSalesOrder));
		$R_IBTN.addClick(this._ibtnGo_CusReq, Function.createDelegate(this, this.goCusReq));
		$R_IBTN.addClick(this._ibtnGo_Quote, Function.createDelegate(this, this.goQuote));
		$R_IBTN.addClick(this._ibtnGo_Invoice, Function.createDelegate(this, this.goInvoice));
		$R_IBTN.addClick(this._ibtnGo_PurchaseOrder, Function.createDelegate(this, this.goPurchaseOrder));
		$R_IBTN.addClick(this._ibtnGo_CRMA, Function.createDelegate(this, this.goCRMA));
		$R_IBTN.addClick(this._ibtnGo_SRMA, Function.createDelegate(this, this.goSRMA));
		$R_IBTN.addClick(this._ibtnGo_Credit, Function.createDelegate(this, this.goCredit));
		$R_IBTN.addClick(this._ibtnGo_Debit, Function.createDelegate(this, this.goDebit));
		$R_TXTBOX.addEnterPressedEvent(this._txtSalesOrderNo, Function.createDelegate(this, this.goSalesOrder));
		$R_TXTBOX.addEnterPressedEvent(this._txtCusReqNo, Function.createDelegate(this, this.goCusReq));
		$R_TXTBOX.addEnterPressedEvent(this._txtQuoteNo, Function.createDelegate(this, this.goQuote));
		$R_TXTBOX.addEnterPressedEvent(this._txtInvoiceNo, Function.createDelegate(this, this.goInvoice));
		$R_TXTBOX.addEnterPressedEvent(this._txtPurchaseOrderNo, Function.createDelegate(this, this.goPurchaseOrder));
		$R_TXTBOX.addEnterPressedEvent(this._txtCRMANo, Function.createDelegate(this, this.goCRMA));
		$R_TXTBOX.addEnterPressedEvent(this._txtSRMANo, Function.createDelegate(this, this.goSRMA));
		$R_TXTBOX.addEnterPressedEvent(this._txtCreditNo, Function.createDelegate(this, this.goCredit));
		$R_TXTBOX.addEnterPressedEvent(this._txtDebitNo, Function.createDelegate(this, this.goDebit));
		Rebound.GlobalTrader.Site.Pages.Orders.Orders.callBaseMethod(this, "goInit");
	},

	dispose: function() {
		if (this.isDisposed) return;
		if (this._ibtnGo_Sourcing) $R_IBTN.clearHandlers(this._ibtnGo_Sourcing);
		if (this._ibtnGo_SalesOrder) $R_IBTN.clearHandlers(this._ibtnGo_SalesOrder);
		if (this._ibtnGo_CusReq) $R_IBTN.clearHandlers(this._ibtnGo_CusReq);
		if (this._ibtnGo_Quote) $R_IBTN.clearHandlers(this._ibtnGo_Quote);
		if (this._ibtnGo_Invoice) $R_IBTN.clearHandlers(this._ibtnGo_Invoice);
		if (this._ibtnGo_PurchaseOrder) $R_IBTN.clearHandlers(this._ibtnGo_PurchaseOrder);
		if (this._ibtnGo_CRMA) $R_IBTN.clearHandlers(this._ibtnGo_CRMA);
		if (this._ibtnGo_SRMA) $R_IBTN.clearHandlers(this._ibtnGo_SRMA);
		if (this._ibtnGo_Credit) $R_IBTN.clearHandlers(this._ibtnGo_Credit);
		if (this._ibtnGo_Debit) $R_IBTN.clearHandlers(this._ibtnGo_Debit);
		if (this._ctlEllipses_CusReqs) this._ctlEllipses_CusReqs.dispose();
		if (this._ctlEllipses_Quotes) this._ctlEllipses_Quotes.dispose();
		if (this._ctlEllipses_SalesOrders) this._ctlEllipses_SalesOrders.dispose();
		if (this._ctlEllipses_Invoices) this._ctlEllipses_Invoices.dispose();
		if (this._ctlEllipses_PurchaseOrders) this._ctlEllipses_PurchaseOrders.dispose();
		if (this._ctlEllipses_CRMAs) this._ctlEllipses_CRMAs.dispose();
		if (this._ctlEllipses_SRMAs) this._ctlEllipses_SRMAs.dispose();
		if (this._ctlEllipses_PurReqs) this._ctlEllipses_PurReqs.dispose();
		if (this._ctlEllipses_Credits) this._ctlEllipses_Credits.dispose();
		if (this._ctlEllipses_Debits) this._ctlEllipses_Debits.dispose();
		if (this._txtCusReqNo) $R_TXTBOX.clearEvents(this._txtCusReqNo);
		if (this._txtQuoteNo) $R_TXTBOX.clearEvents(this._txtQuoteNo);
		if (this._txtSalesOrderNo) $R_TXTBOX.clearEvents(this._txtSalesOrderNo);
		if (this._txtInvoiceNo) $R_TXTBOX.clearEvents(this._txtInvoiceNo);
		if (this._txtPurchaseOrderNo) $R_TXTBOX.clearEvents(this._txtPurchaseOrderNo);
		if (this._txtCRMANo) $R_TXTBOX.clearEvents(this._txtCRMANo);
		if (this._txtSRMANo) $R_TXTBOX.clearEvents(this._txtSRMANo);
		if (this._txtCreditNo) $R_TXTBOX.clearEvents(this._txtCreditNo);
		if (this._txtDebitNo) $R_TXTBOX.clearEvents(this._txtDebitNo);
		this._strPathToData = null;
		this._strDataObject = null;
		this._ibtnGo_SalesOrder = null;
		this._ibtnGo_CusReq = null;
		this._ibtnGo_Quote = null;
		this._ibtnGo_Invoice = null;
		this._ibtnGo_PurchaseOrder = null;
		this._ibtnGo_CRMA = null;
		this._ibtnGo_SRMA = null;
		this._ibtnGo_Credit = null;
		this._ibtnGo_Debit = null;
		this._ctlEllipses_CusReqs = null;
		this._txtCusReqNo = null;
		this._ctlEllipses_Quotes = null;
		this._txtQuoteNo = null;
		this._ctlEllipses_SalesOrders = null;
		this._txtSalesOrderNo = null;
		this._ctlEllipses_Invoices = null;
		this._txtInvoiceNo = null;
		this._ctlEllipses_PurchaseOrders = null;
		this._txtPurchaseOrderNo = null;
		this._ctlEllipses_CRMAs = null;
		this._txtCRMANo = null;
		this._ctlEllipses_SRMAs = null;
		this._txtSRMANo = null;
		this._ctlEllipses_PurReqs = null;
		this._ctlEllipses_Credits = null;
		this._txtCreditNo = null;
		this._ctlEllipses_Debits = null;
		this._txtDebitNo = null;

		Rebound.GlobalTrader.Site.Pages.Orders.Orders.callBaseMethod(this, "dispose");
	},

	countRequirements: function() {
		var obj = this._ctlEllipses_CusReqs._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountRequirements");
	},
	
	countSOs: function() {
		var obj = this._ctlEllipses_SalesOrders._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountSOs");
	},
	
	countQuotes: function() {
		var obj = this._ctlEllipses_Quotes._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountQuotes");
	},
	
	countInvoices: function() {
		var obj = this._ctlEllipses_Invoices._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountInvoices");
	},
	
	countPOs: function() {
		var obj = this._ctlEllipses_PurchaseOrders._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountPOs");
	},
	
	countCRMAs: function() {
		var obj = this._ctlEllipses_CRMAs._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountCRMAs");
	},
	
	countSRMAs: function() {
		var obj = this._ctlEllipses_SRMAs._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountSRMAs");
	},
	
	countPurReqs: function() {
		var obj = this._ctlEllipses_PurReqs._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountPurReqs");
	},	
	
	countCredits: function() {
		var obj = this._ctlEllipses_Credits._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountCredits");
	},	
	
	countDebits: function() {
		var obj = this._ctlEllipses_Debits._objData;
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("CountDebits");
	},	
	
	goSourcing: function() {
		if (this._txtSourcingPartNo.value.trim().length < 1) return;
		location.href = $RGT_gotoURL_Sourcing(this._txtSourcingPartNo.value, true);
	},
	
	goSalesOrder: function() {
		if (this._txtSalesOrderNo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetSalesOrderID");
		obj.addParameter("No", this._txtSalesOrderNo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goSalesOrderComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goSalesOrderComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_SalesOrder(args._result.ID);
	},

	goQuote: function() {
		if (this._txtQuoteNo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetQuoteID");
		obj.addParameter("No", this._txtQuoteNo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goQuoteComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goQuoteComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_Quote(args._result.ID);
	},
	
	goCusReq: function() {
		if (this._txtCusReqNo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetRequirementID");
		obj.addParameter("No", this._txtCusReqNo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goCusReqComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goCusReqComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_CustomerRequirement(args._result.ID);
	},
	
	goPurchaseOrder: function() {
		if (this._txtPurchaseOrderNo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetPurchaseOrderID");
		obj.addParameter("No", this._txtPurchaseOrderNo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goPurchaseOrderComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goPurchaseOrderComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_PurchaseOrder(args._result.ID);
	},
	
	goCRMA: function() {
		if (this._txtCRMANo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetCRMAID");
		obj.addParameter("No", this._txtCRMANo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goCRMAComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goCRMAComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_CRMA(args._result.ID);
	},
	
	goSRMA: function() {
		if (this._txtSRMANo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetSRMAID");
		obj.addParameter("No", this._txtSRMANo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goSRMAComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goSRMAComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_SRMA(args._result.ID);
	},
	
	goInvoice: function() {
		if (this._txtInvoiceNo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetInvoiceID");
		obj.addParameter("No", this._txtInvoiceNo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goInvoiceComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goInvoiceComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_Invoice(args._result.ID);
	},

	goCredit: function() {
		if (this._txtCreditNo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetCreditID");
		obj.addParameter("No", this._txtCreditNo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goCreditComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goCreditComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_CreditNote(args._result.ID);
	}, 
	
	goDebit: function() {
		if (this._txtDebitNo.value.trim().length < 1) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData(this._strPathToData);
		obj.set_DataObject(this._strDataObject);
		obj.set_DataAction("GetDebitID");
		obj.addParameter("No", this._txtDebitNo.value.trim());
		obj.addDataOK(Function.createDelegate(this, this.goDebitComplete));
		//obj.addError(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
	},
	
	goDebitComplete: function(args) {
		if (args._result.ID) location.href = $RGT_gotoURL_DebitNote(args._result.ID);
	}	
};
Rebound.GlobalTrader.Site.Pages.Orders.Orders.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.Orders", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
