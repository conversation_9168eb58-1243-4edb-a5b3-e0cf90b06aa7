///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
//-----------------------------------------------------------------------------------------
// RP 19.07.2009:
// - remove keycode 46 as it maps to "." and causes problems with integer fields
//
// RP 19.07.2009:
// - add keycode 46 for Delete button (for some reason Sys.UI.Key.del does not map correctly)
//
// RP 30.10.2009:
// - allow removal of initial settings (onfocus)
//
// RP 21.10.2009:
// - allow auto updating of decimal places
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.ReboundTextBox = function () {
    this.message = null;
};

var $R_TXTBOX = Rebound.GlobalTrader.Site.Controls.ReboundTextBox;
$R_TXTBOX._objEnterEvents = {};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.dispose = function() {
	if ($R_TXTBOX.isDisposed) return;
	$R_TXTBOX._objEnterEvents = null;
    $R_TXTBOX.isDisposed = true;
    this.message = null;
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.initTextBox = function (txt, enmTextBoxMode, blnAllowEnter, blnUppercaseOnly, blnFixedWidthFont, blnFormatDecimalPlaces, intDecimalPlaces, blnValidatePasteChar, blnCountChar, strRelatedLabelID) {
	if (!txt) return;
	txt.removeAttribute("onfocus");
    if (typeof strRelatedLabelID === "undefined") { 
    $R_TXTBOX.setCountChar(txt, blnCountChar,null); 
	$R_TXTBOX.setMode(txt, enmTextBoxMode, blnAllowEnter,null);}
    else { $R_TXTBOX.setCountChar(txt, blnCountChar,strRelatedLabelID.id);
    $R_TXTBOX.setMode(txt, enmTextBoxMode, blnAllowEnter,strRelatedLabelID.id);}
    //$R_TXTBOX.setMode(txt, enmTextBoxMode, blnAllowEnter);
	$R_TXTBOX.setUpperCaseOnly(txt, blnUppercaseOnly);
	$R_TXTBOX.setFixedWidth(txt, blnFixedWidthFont);
	if (blnFormatDecimalPlaces) txt.setAttribute("onblur", String.format("{0} $R_TXTBOX.setDecimalPlacesOnBlur(this, {1});", txt.getAttribute("onblur"), intDecimalPlaces));
    $R_TXTBOX.checkNumberOnly(txt, enmTextBoxMode, blnValidatePasteChar);
    //counting character for call setCountchar methoad
   //if (typeof strRelatedLabelID === "undefined") { 
   // $R_TXTBOX.setCountChar(txt, blnCountChar,null);}
   // else { $R_TXTBOX.setCountChar(txt, blnCountChar,strRelatedLabelID.id);}
    
   
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setMode = function(txt, enmMode, blnAllowEnter,chkmaxlenthnotes) {
//Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setMode = function(txt, enmMode, blnAllowEnter) {
    if (!txt) return;
    if (blnAllowEnter == null) blnAllowEnter = false;
    var strAllowed = "";
    var strNumerics = "48, 49, 50, 51, 52, 53, 54, 55, 56, 57";
    var strNumericsDash = "45, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57";
    var strAlphabet = "65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90";
    var strAlphabet1 = "97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110 , 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122";
    var strStandard = "Sys.UI.Key.enter, Sys.UI.Key.backspace, Sys.UI.Key.tab, Sys.UI.Key.esc, Sys.UI.Key.space, Sys.UI.Key.pageUp, Sys.UI.Key.pageDown, Sys.UI.Key.end, Sys.UI.Key.home, Sys.UI.Key.left, Sys.UI.Key.up, Sys.UI.Key.right, Sys.UI.Key.down, Sys.UI.Key.del";
    var intDecimalSep = Sys.CultureInfo.CurrentCulture.numberFormat.CurrencyDecimalSeparator.charCodeAt(0);
    var intTimeSep = Sys.CultureInfo.CurrentCulture.dateTimeFormat.TimeSeparator.charCodeAt(0);
    switch (enmMode) {
        case $R_ENUM$TextBoxMode.Normal: strAllowed = "[]"; break;
        case $R_ENUM$TextBoxMode.Numeric: strAllowed = String.format("[{0}, {1}]", strStandard, strNumerics); break;
        // 
        case $R_ENUM$TextBoxMode.NumericDash: strAllowed = String.format("[{0}, {1}]", strStandard, strNumericsDash); break;
        // 
        case $R_ENUM$TextBoxMode.Currency: strAllowed = String.format("[{0}, {1}, {2}]", strStandard, strNumerics, intDecimalSep); break;
        case $R_ENUM$TextBoxMode.Time: strAllowed = String.format("[{0}, {1}, {2}, {3}]", strStandard, strNumerics, intTimeSep, intDecimalSep); break;
        // 
        case $R_ENUM$TextBoxMode.AlphaNumeric: strAllowed = String.format("[{0}, {1}, {2}, {3}]", strStandard, strNumerics, strAlphabet, strAlphabet1); break;
        // 
    }
    if (strAllowed == "") strAllowed = "[]";
    txt.setAttribute("onkeypress", String.format("return $R_TXTBOX.checkKeyPress(event, {0}, {1}, []);", blnAllowEnter, strAllowed));
    txt.setAttribute("B_TXTBOX_Mode", enmMode);
    if(chkmaxlenthnotes!=null){txt.setAttribute("maxlength", "2000");}
    strAllowed = null;
    strNumerics = null;
    strStandard = null;
    intDecimalSep = null;
    intTimeSep = null;
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setUpperCaseOnly = function(txt, bln) {
	if (!txt) return;
	if (bln) {
		txt.setAttribute("onblur", "$R_TXTBOX.setToUpperCaseOnBlur(this);");
	} else {
		var strOnBlur = txt.getAttribute("onblur");
		if (strOnBlur != null) txt.setAttribute("onblur", strOnBlur.replace("$R_TXTBOX.setToUpperCaseOnBlur(this);", "").trim());
		strOnBlur = null;
	}
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.checkNumberOnly = function (txt, enmTextBoxMode, blnValidatePasteChar) {
   // alert(blnValidatePasteChar);
    if (!txt) return;  
    if (enmTextBoxMode == 1 || blnValidatePasteChar==true) {
        txt.setAttribute("onblur", "$R_TXTBOX.setNumberOnly(this);");
    } else {
        var strOnBlur = txt.getAttribute("onblur");
        if (strOnBlur != null) txt.setAttribute("onblur", strOnBlur.replace("$R_TXTBOX.setNumberOnly(this);", "").trim());
        strOnBlur = null;
    }
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setFixedWidth = function(txt, bln) {
	if (!txt) return;
	if (bln) {
		Sys.UI.DomElement.addCssClass(txt, "fixedWidth");
	} else {
		Sys.UI.DomElement.removeCssClass(txt, "fixedWidth");
	}
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.getTextBoxMode = function(txt) {
	if (!txt) return;
	return txt.getAttribute("B_TXTBOX_Mode");
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.checkKeyPress = function(e, blnAllowEnter, aryAllowed, aryProhibited) {
	var key; 
	try {
		//get keycode
		if (e.keyCode) key = e.keyCode;
		if (e.which) key = e.which;
		
		//check for enter press event
		if (key == Sys.UI.Key.enter) {
			if ($R_TXTBOX._objEnterEvents[e.target.id]) {
				try { $R_TXTBOX._objEnterEvents[e.target.id](e.target); } catch (ex) { alert(ex); }
				return false;
			} else {
				if (!blnAllowEnter) return false;
			}
		}

		if (e.ctrlKey) {
			if (key == 99 || key == 118 || key == 120) return true; //allow CTRL-C, CTRL-V, CTRL-X
		}
		var blnOK;
		if (aryAllowed) {
			if (aryAllowed.length > 0) {
				blnOK = false;
				for (var i = 0, l = aryAllowed.length; i < l; i++) {
					if (key == aryAllowed[i]) { 
						blnOK = true; 
						break; 
					}
				}
				return blnOK;
			}
		}
		if (aryProhibited) {
			if (aryProhibited.length > 0) {
				blnOK = true;
				for (var i2 = 0, l2 = aryProhibited.length; i < l2; i++) {
					if (key == aryProhibited[i2]) { 
						blnOK = false; 
						break; 
					}
				}
				return blnOK;
			}
		}
	} finally {
		key = null;
	}
	return true;	
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setToUpperCaseOnBlur = function(txt) {
	if (!txt) return;
	txt.value = txt.value.toUpperCase();
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setDecimalPlacesOnBlur = function(txt, intDecimalPlaces) {
	if (!txt) return;
	txt.value = $R_FN.formatCurrency(txt.value, "", intDecimalPlaces);
};


Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setNumberOnly = function (txt) {
    if (!txt) return;

    var isValid =false;
    isValid= $R_FN.isValidNumber(txt.value);
    if(!isValid){
        txt.value = 0;
        }
};
Rebound.GlobalTrader.Site.Controls.ReboundTextBox.addEnterPressedEvent = function(txt, fn) {
	if (!txt) return;
	if (!txt.id) return;
	if (!$R_TXTBOX._objEnterEvents) $R_TXTBOX._objEnterEvents = {};
	$R_TXTBOX._objEnterEvents[txt.id] = fn;
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.removeInitialSettings = function(txt) {
	if (!txt) return;
	txt.removeAttribute("onfocus");
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.clearEvents = function(txt) {
	if (!txt) return;
	if (!txt.id) return;
	if (!$R_TXTBOX._objEnterEvents) return;
	$R_TXTBOX._objEnterEvents[txt.id] = null;
};

//add methad for  //count to view counting characters in  notes fields P8015/1
Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setCountChar = function (txt, bln,controlid) {
    if (!txt) return;
    if (bln) {
        txt.setAttribute("onkeyup", "$R_TXTBOX.setCountCharOnkeyUp(this,"+controlid+");");

    } else {
        var strOnKeyUp = txt.getAttribute("onkeyup");
        if (strOnKeyUp != null) txt.setAttribute("onkeyup", strOnKeyUp.replace("$R_TXTBOX.setCountCharOnkeyUp(this,"+controlid+");", "").trim());
        strOnKeyUp = null;
    }
};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.setCountCharOnkeyUp = function (txt,controlid) {
    if (!txt) return;
    var txtcount = 0;
    var cl = 0;
   // txtcount = txt.value.length;
   //$R_FN.setInnerHTML(document.getElementById(controlid.id),txtcount);
    txtcount = txt.value.length;
    cl = 2000 - txtcount;
    if (cl < 0) {
        //$('#charcount').addClass('negative-numbers');
    }
    if (cl > 0) {
        //$('#charcount').removeClass('negative-numbers');
    }
    //$('#charcount').text(cl);
    $R_FN.setInnerHTML(document.getElementById(controlid.id), cl);

};

Rebound.GlobalTrader.Site.Controls.ReboundTextBox.registerClass("Rebound.GlobalTrader.Site.Controls.ReboundTextBox");