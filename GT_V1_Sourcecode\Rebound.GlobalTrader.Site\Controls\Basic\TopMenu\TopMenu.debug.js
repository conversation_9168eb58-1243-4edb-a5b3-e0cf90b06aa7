///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 13.01.2010:
// - fix mouseovers not showing if item has no sub-menu items
//
// RP 06.01.2010:
// - dispose everything fully
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.TopMenu = function(element) { 
	Rebound.GlobalTrader.Site.Controls.TopMenu.initializeBase(this, [element]);
	this._aryListItemIDs = [];
	this._aryHoverPanelClientIDs = [];
	this._intTimeout = -1;
	this._intCurrentRollover = -1;
	this._blnRolloverShown = false;
};

Rebound.GlobalTrader.Site.Controls.TopMenu.prototype = {

	get_aryListItemIDs: function() { return this._aryListItemIDs; }, 	set_aryListItemIDs: function(v) { if (this._aryListItemIDs !== v)  this._aryListItemIDs = v; }, 
	get_pnlRollovers: function() { return this._pnlRollovers; }, 	set_pnlRollovers: function(v) { if (this._pnlRollovers !== v)  this._pnlRollovers = v; }, 
	get_aryHoverPanelClientIDs: function() { return this._aryHoverPanelClientIDs; }, 	set_aryHoverPanelClientIDs: function(v) { if (this._aryHoverPanelClientIDs !== v)  this._aryHoverPanelClientIDs = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.TopMenu.callBaseMethod(this, "initialize");
		
		//setup mouse handlers for submenus
		for (var i = 0, l = this._aryListItemIDs.length; i < l; i++) {
			var pnl = (this._aryHoverPanelClientIDs[i] == null) ? null : $get(this._aryHoverPanelClientIDs[i]);
			var li = (this._aryListItemIDs[i] == null) ? null : $get(this._aryListItemIDs[i]);
			if (li) {
				li.setAttribute("bui_topMenuID", i);
				$addHandler(li, "mouseover", Function.createDelegate(this, this.rolloverElement));
				$addHandler(li, "mouseout", Function.createDelegate(this, this.rolloutElement));
				if (pnl) {
					$addHandler(pnl, "mouseover", Function.createDelegate(this, this.rolloverElement));
					$addHandler(pnl, "mouseout", Function.createDelegate(this, this.rolloutElement));
					var hyp = $get(pnl.id + "_hyp");
					if (hyp) $addHandler(li, "mouseover", Function.createDelegate(this, this.rolloverElement));
					hyp = null;
				}
			}
			pnl = null;
		}		
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		this.clearTimeout();
		for (var i = 0, l = this._aryListItemIDs.length; i < l; i++) {
			var pnl = (this._aryHoverPanelClientIDs[i] == null) ? null : $get(this._aryHoverPanelClientIDs[i]);
			var li = (this._aryListItemIDs[i] == null) ? null : $get(this._aryListItemIDs[i]);
			if (li) $clearHandlers(li);
			if (pnl) {
				$clearHandlers(pnl);
				var hyp = $get(pnl.id + "_hyp");
				if (hyp) $clearHandlers(hyp);
				hyp = null;
			}
			pnl = null; 
			li = null;
		}
		this._aryHoverPanelClientIDs = null;
		this._aryListItemIDs = null;
		this._pnlRollovers = null;
		this._intTimeout = null;
		this._intCurrentRollover = null;
		this._blnRolloverShown = null;
		Rebound.GlobalTrader.Site.Controls.TopMenu.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	rolloverElement: function(sender, eventArgs) {
		this.clearTimeout();
		var li = $R_FN.findParentElementOfType(sender.target, "LI");
		if (!li) return;

		//show hover state
		Sys.UI.DomElement.addCssClass(li, "hover");
		
		//set current rollover
		var intRollover = Number.parseInvariant(li.getAttribute("bui_topMenuID"));
		if (this._blnRolloverShown && intRollover == this._intCurrentRollover) return;
		this._blnRolloverShown = true;
		this._intCurrentRollover = intRollover;
		
		//show the correct hover contents
		for (var i = 0, l = this._aryHoverPanelClientIDs.length; i < l; i++) {
			var pnl = (this._aryHoverPanelClientIDs[i] == null) ? null : $get(this._aryHoverPanelClientIDs[i]);
			if (pnl) $R_FN.showElement(pnl, i == this._intCurrentRollover);
			var li2 = (this._aryListItemIDs[i] == null) ? null : $get(this._aryListItemIDs[i]);
			if (li2) Sys.UI.DomElement.removeCssClass(li2, "hover");
			pnl = null;
			li2 = null;
		}
		
		//show the rollover panel
		if (this._aryHoverPanelClientIDs[this._intCurrentRollover]) {
			$R_FN.showElement(this._pnlRollovers, true);
			$R_FN.setElementOpacity(this._pnlRollovers, 100);

			//set position of rollover to underneath relevant list item
			this._pnlRollovers.style.width = "";
			var objScreenSize = Sys.UI.DomElement.getBounds(document.body);
			var objPos_li = Sys.UI.DomElement.getBounds(li);
			var objPos_pnl = Sys.UI.DomElement.getBounds(this._pnlRollovers);
			if (objPos_pnl.width < objPos_li.width) {
				objPos_pnl.width = objPos_li.width;
				this._pnlRollovers.style.width = objPos_pnl.width + "px";
			}
			var intY = 29;
			var intX = objPos_li.x;
			if (objPos_pnl.width > objPos_li.width) intX -= Math.round((objPos_pnl.width - objPos_li.width) / 2);
			//if ((objPos_li.x + objPos_pnl.width) > objScreenSize.width) intX -= (objPos_pnl.width - objPos_li.width);
			Sys.UI.DomElement.setLocation(this._pnlRollovers, intX, intY);

			//tidy up
			objScreenSize = null;
			objPos_li = null;
			objPos_pnl = null; 
			intX = null; 
			intY = null;
		} else {
			$R_FN.showElement(this._pnlRollovers, false);
		}
		
		//tidy up
		li = null;
		intRollover = null;
	},
	
	rolloutElement: function(sender, eventArgs) {
		this._intTimeout = setTimeout(Function.createDelegate(this, this.hideHoverPanel), 100);
	},
	
	hideHoverPanel: function() {
		var li = (this._aryListItemIDs[this._intCurrentRollover] == null) ? null : $get(this._aryListItemIDs[this._intCurrentRollover]);
		if (li) Sys.UI.DomElement.removeCssClass(li, "hover");
		li = null;
		$R_FN.showElement(this._pnlRollovers, false);
		this._intCurrentRollover = -1;
		this._blnRolloverShown = false;
	},

	cancelHideHoverPanel: function(sender, eventArgs) {
		clearTimeout(this._intTimeout);
	},
	
	clearTimeout: function() {
		if (this._intTimeout != -1) clearTimeout(this._intTimeout);
	}

};

Rebound.GlobalTrader.Site.Controls.TopMenu.registerClass("Rebound.GlobalTrader.Site.Controls.TopMenu", Sys.UI.Control, Sys.IDisposable);