///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.prototype = {

	get_pnlShipped: function() { return this._pnlShipped; }, 	set_pnlShipped: function(value) { if (this._pnlShipped !== value)  this._pnlShipped = value; }, 
	get_tblShipped: function() { return this._tblShipped; }, 	set_tblShipped: function(value) { if (this._tblShipped !== value)  this._tblShipped = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblShipped) this._tblShipped.dispose();
		this._pnlShipped = null;
		this._tblShipped = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlShipped, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.callBaseMethod(this, "setupLoadingState");
	},
	
	getData: function() {
		this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/TodaysShippedOrders");
		obj.set_DataObject("TodaysShippedOrders");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		var result = args._result;
		this.showNoneFoundOrContent(result.Count);
		//shipped
		this._tblShipped.clearTable();
		for (var i = 0; i < result.Shipped.length; i++) {
			var row = result.Shipped[i];
			var aryData = [
				$RGT_nubButton_SalesOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Due
				];
			this._tblShipped.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlShipped, result.Shipped.length > 0);
		this.hideLoading();
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.TodaysShippedOrders", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
