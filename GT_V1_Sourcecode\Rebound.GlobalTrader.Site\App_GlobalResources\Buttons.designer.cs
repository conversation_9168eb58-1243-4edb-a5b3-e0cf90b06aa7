//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class Buttons {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Buttons() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.Buttons", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activate Company.
        /// </summary>
        internal static string ActivateCompany {
            get {
                return ResourceManager.GetString("ActivateCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        internal static string Add {
            get {
                return ResourceManager.GetString("Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add All Alternate.
        /// </summary>
        internal static string AddAllAlternate {
            get {
                return ResourceManager.GetString("AddAllAlternate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Alternate.
        /// </summary>
        internal static string AddAlternate {
            get {
                return ResourceManager.GetString("AddAlternate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Contact.
        /// </summary>
        internal static string AddContact {
            get {
                return ResourceManager.GetString("AddContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add/Edit.
        /// </summary>
        internal static string AddEdit {
            get {
                return ResourceManager.GetString("AddEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Expedite Note.
        /// </summary>
        internal static string AddExpediteNote {
            get {
                return ResourceManager.GetString("AddExpediteNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Folder.
        /// </summary>
        internal static string AddFolder {
            get {
                return ResourceManager.GetString("AddFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Data.
        /// </summary>
        internal static string AddLineItem {
            get {
                return ResourceManager.GetString("AddLineItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New.
        /// </summary>
        internal static string AddNew {
            get {
                return ResourceManager.GetString("AddNew", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Communication Note.
        /// </summary>
        internal static string AddNewHUBFRQNote {
            get {
                return ResourceManager.GetString("AddNewHUBFRQNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Offer.
        /// </summary>
        internal static string AddOffer {
            get {
                return ResourceManager.GetString("AddOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Scheduled Call.
        /// </summary>
        internal static string AddScheduledCall {
            get {
                return ResourceManager.GetString("AddScheduledCall", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment.
        /// </summary>
        internal static string AddShortShipment {
            get {
                return ResourceManager.GetString("AddShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Sourcing Info.
        /// </summary>
        internal static string AddStockInfo {
            get {
                return ResourceManager.GetString("AddStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Routing Card.
        /// </summary>
        internal static string AddStockRoutingCard {
            get {
                return ResourceManager.GetString("AddStockRoutingCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Suggestion.
        /// </summary>
        internal static string AddSuggestion {
            get {
                return ResourceManager.GetString("AddSuggestion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Task.
        /// </summary>
        internal static string AddTask {
            get {
                return ResourceManager.GetString("AddTask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add to HUBRFQ.
        /// </summary>
        internal static string AddToHUBRFQ {
            get {
                return ResourceManager.GetString("AddToHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add To Requirement.
        /// </summary>
        internal static string AddToRequirement {
            get {
                return ResourceManager.GetString("AddToRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Trusted.
        /// </summary>
        internal static string AddTrusted {
            get {
                return ResourceManager.GetString("AddTrusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Users.
        /// </summary>
        internal static string AddUsers {
            get {
                return ResourceManager.GetString("AddUsers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to  Add All.
        /// </summary>
        internal static string All {
            get {
                return ResourceManager.GetString("All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Companies.
        /// </summary>
        internal static string AllCompanies {
            get {
                return ResourceManager.GetString("AllCompanies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocate.
        /// </summary>
        internal static string Allocate {
            get {
                return ResourceManager.GetString("Allocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply.
        /// </summary>
        internal static string Apply {
            get {
                return ResourceManager.GetString("Apply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply Partwatch.
        /// </summary>
        internal static string ApplyPartwatch {
            get {
                return ResourceManager.GetString("ApplyPartwatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approval.
        /// </summary>
        internal static string Approval {
            get {
                return ResourceManager.GetString("Approval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approve.
        /// </summary>
        internal static string Approve {
            get {
                return ResourceManager.GetString("Approve", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string Arrow {
            get {
                return ResourceManager.GetString("Arrow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign.
        /// </summary>
        internal static string Assign {
            get {
                return ResourceManager.GetString("Assign", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign Lead.
        /// </summary>
        internal static string AssignLead {
            get {
                return ResourceManager.GetString("AssignLead", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign.
        /// </summary>
        internal static string AssignToMe {
            get {
                return ResourceManager.GetString("AssignToMe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise.
        /// </summary>
        internal static string Authorise {
            get {
                return ResourceManager.GetString("Authorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back.
        /// </summary>
        internal static string Back {
            get {
                return ResourceManager.GetString("Back", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BomMapping.
        /// </summary>
        internal static string BomMapping {
            get {
                return ResourceManager.GetString("BomMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse.
        /// </summary>
        internal static string Browse {
            get {
                return ResourceManager.GetString("Browse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactivate/ Bulk Inactivate.
        /// </summary>
        internal static string BulkActivate {
            get {
                return ResourceManager.GetString("BulkActivate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add/ Bulk Add.
        /// </summary>
        internal static string BulkAdd {
            get {
                return ResourceManager.GetString("BulkAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit/ Bulk Edit.
        /// </summary>
        internal static string BulkEdit {
            get {
                return ResourceManager.GetString("BulkEdit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Edit Log.
        /// </summary>
        internal static string BulkEditLog {
            get {
                return ResourceManager.GetString("BulkEditLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculate.
        /// </summary>
        internal static string Calculate {
            get {
                return ResourceManager.GetString("Calculate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Calculate.
        /// </summary>
        internal static string Calculate_StckP {
            get {
                return ResourceManager.GetString("Calculate_StckP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Calculator.
        /// </summary>
        internal static string Calculator {
            get {
                return ResourceManager.GetString("Calculator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string Cancel {
            get {
                return ResourceManager.GetString("Cancel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        internal static string CancelShortShipment {
            get {
                return ResourceManager.GetString("CancelShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel update.
        /// </summary>
        internal static string Cancelupdate {
            get {
                return ResourceManager.GetString("Cancelupdate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cease.
        /// </summary>
        internal static string Cease {
            get {
                return ResourceManager.GetString("Cease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Company.
        /// </summary>
        internal static string ChangeCompany {
            get {
                return ResourceManager.GetString("ChangeCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Password.
        /// </summary>
        internal static string ChangePassword {
            get {
                return ResourceManager.GetString("ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checked.
        /// </summary>
        internal static string Checked {
            get {
                return ResourceManager.GetString("Checked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check Supplier / Manufacturer Data.
        /// </summary>
        internal static string checkSupplierMf {
            get {
                return ResourceManager.GetString("checkSupplierMf", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear Default.
        /// </summary>
        internal static string ClearDefault {
            get {
                return ResourceManager.GetString("ClearDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone.
        /// </summary>
        internal static string Clone {
            get {
                return ResourceManager.GetString("Clone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone this line and add to Requirement.
        /// </summary>
        internal static string CloneAddToRequirement {
            get {
                return ResourceManager.GetString("CloneAddToRequirement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone &amp; Add HUBRFQ.
        /// </summary>
        internal static string CloneAndAddHUBRFQ {
            get {
                return ResourceManager.GetString("CloneAndAddHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone &amp; Send HUB.
        /// </summary>
        internal static string CloneAndSendHUB {
            get {
                return ResourceManager.GetString("CloneAndSendHUB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        internal static string Close {
            get {
                return ResourceManager.GetString("Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close.
        /// </summary>
        internal static string CloseShortShipment {
            get {
                return ResourceManager.GetString("CloseShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Collapse All.
        /// </summary>
        internal static string CollapseAll {
            get {
                return ResourceManager.GetString("CollapseAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company.
        /// </summary>
        internal static string Company {
            get {
                return ResourceManager.GetString("Company", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        internal static string CompanyContacts {
            get {
                return ResourceManager.GetString("CompanyContacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete &amp; Save.
        /// </summary>
        internal static string CompleteSave {
            get {
                return ResourceManager.GetString("CompleteSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm.
        /// </summary>
        internal static string Confirm {
            get {
                return ResourceManager.GetString("Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm All.
        /// </summary>
        internal static string ConfirmAll {
            get {
                return ResourceManager.GetString("ConfirmAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts.
        /// </summary>
        internal static string Contacts {
            get {
                return ResourceManager.GetString("Contacts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue.
        /// </summary>
        internal static string Continue {
            get {
                return ResourceManager.GetString("Continue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        internal static string ContinueAfterSelectWarehouse {
            get {
                return ResourceManager.GetString("ContinueAfterSelectWarehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone this line.
        /// </summary>
        internal static string CreateClone {
            get {
                return ResourceManager.GetString("CreateClone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Credit Note For POHUB.
        /// </summary>
        internal static string CreateCreditNoteForPOHUB {
            get {
                return ResourceManager.GetString("CreateCreditNoteForPOHUB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create PDF.
        /// </summary>
        internal static string CreatePDF {
            get {
                return ResourceManager.GetString("CreatePDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create PO.
        /// </summary>
        internal static string CreatePurchaseOrder {
            get {
                return ResourceManager.GetString("CreatePurchaseOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create SO.
        /// </summary>
        internal static string CreateSalesOrder {
            get {
                return ResourceManager.GetString("CreateSalesOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Account/ Credit Application Form.
        /// </summary>
        internal static string CreditApplicationForm {
            get {
                return ResourceManager.GetString("CreditApplicationForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cross Match.
        /// </summary>
        internal static string CrossMatch {
            get {
                return ResourceManager.GetString("CrossMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customers.
        /// </summary>
        internal static string Customers {
            get {
                return ResourceManager.GetString("Customers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data.
        /// </summary>
        internal static string Data {
            get {
                return ResourceManager.GetString("Data", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deallocate.
        /// </summary>
        internal static string Deallocate {
            get {
                return ResourceManager.GetString("Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deauthorise.
        /// </summary>
        internal static string Deauthorise {
            get {
                return ResourceManager.GetString("Deauthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default.
        /// </summary>
        internal static string Default {
            get {
                return ResourceManager.GetString("Default", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default Billing.
        /// </summary>
        internal static string DefaultBilling {
            get {
                return ResourceManager.GetString("DefaultBilling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default Shipping.
        /// </summary>
        internal static string DefaultShipping {
            get {
                return ResourceManager.GetString("DefaultShipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        internal static string Delete {
            get {
                return ResourceManager.GetString("Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Alternate.
        /// </summary>
        internal static string DeleteAlternate {
            get {
                return ResourceManager.GetString("DeleteAlternate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Alternate Part.
        /// </summary>
        internal static string DeleteAlternatePart {
            get {
                return ResourceManager.GetString("DeleteAlternatePart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete PartWatch.
        /// </summary>
        internal static string DeleteClientPartWatch {
            get {
                return ResourceManager.GetString("DeleteClientPartWatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Folder.
        /// </summary>
        internal static string DeleteFolder {
            get {
                return ResourceManager.GetString("DeleteFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Future Rate.
        /// </summary>
        internal static string DeleteFutureRate {
            get {
                return ResourceManager.GetString("DeleteFutureRate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Message(s).
        /// </summary>
        internal static string DeleteMessage {
            get {
                return ResourceManager.GetString("DeleteMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete HUB Partwatch.
        /// </summary>
        internal static string DeletePartWatchMatch {
            get {
                return ResourceManager.GetString("DeletePartWatchMatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Receipt.
        /// </summary>
        internal static string DeleteReceipt {
            get {
                return ResourceManager.GetString("DeleteReceipt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Unallocated Service.
        /// </summary>
        internal static string DeleteUnallocatedService {
            get {
                return ResourceManager.GetString("DeleteUnallocatedService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Unallocated Services.
        /// </summary>
        internal static string DeleteUnallocatedServices {
            get {
                return ResourceManager.GetString("DeleteUnallocatedServices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Unallocated Stock.
        /// </summary>
        internal static string DeleteUnallocatedStock {
            get {
                return ResourceManager.GetString("DeleteUnallocatedStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deselect.
        /// </summary>
        internal static string Deselect {
            get {
                return ResourceManager.GetString("Deselect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable.
        /// </summary>
        internal static string Disable {
            get {
                return ResourceManager.GetString("Disable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unapprove.
        /// </summary>
        internal static string Disapprove {
            get {
                return ResourceManager.GetString("Disapprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dismiss.
        /// </summary>
        internal static string Dismiss {
            get {
                return ResourceManager.GetString("Dismiss", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download EUU Form Template.
        /// </summary>
        internal static string DownloadEEUFormTemplate {
            get {
                return ResourceManager.GetString("DownloadEEUFormTemplate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Draft.
        /// </summary>
        internal static string Draft {
            get {
                return ResourceManager.GetString("Draft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit.
        /// </summary>
        internal static string Edit {
            get {
                return ResourceManager.GetString("Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit-All.
        /// </summary>
        internal static string EditAll {
            get {
                return ResourceManager.GetString("EditAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit All Export Approval.
        /// </summary>
        internal static string EditAllExportApproval {
            get {
                return ResourceManager.GetString("EditAllExportApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit All.
        /// </summary>
        internal static string EditAll_Eccn {
            get {
                return ResourceManager.GetString("EditAll_Eccn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Alternative Part.
        /// </summary>
        internal static string EditAltPart {
            get {
                return ResourceManager.GetString("EditAltPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Bank Charge Fee.
        /// </summary>
        internal static string EditBankFee {
            get {
                return ResourceManager.GetString("EditBankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Edit.
        /// </summary>
        internal static string EditBulkEpo {
            get {
                return ResourceManager.GetString("EditBulkEpo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Current Rates.
        /// </summary>
        internal static string EditCurrentRates {
            get {
                return ResourceManager.GetString("EditCurrentRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Strategic Offers.
        /// </summary>
        internal static string EditEpo {
            get {
                return ResourceManager.GetString("EditEpo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Export Approval.
        /// </summary>
        internal static string EditExportApproval {
            get {
                return ResourceManager.GetString("EditExportApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Folder.
        /// </summary>
        internal static string EditFolder {
            get {
                return ResourceManager.GetString("EditFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit info - After Release.
        /// </summary>
        internal static string EditinfoAfterRelease {
            get {
                return ResourceManager.GetString("EditinfoAfterRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Members.
        /// </summary>
        internal static string EditMembers {
            get {
                return ResourceManager.GetString("EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Offer.
        /// </summary>
        internal static string EditOffer {
            get {
                return ResourceManager.GetString("EditOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Rates.
        /// </summary>
        internal static string EditRates {
            get {
                return ResourceManager.GetString("EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Receipt.
        /// </summary>
        internal static string EditReceipt {
            get {
                return ResourceManager.GetString("EditReceipt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Reverse Logistic.
        /// </summary>
        internal static string EditReverseLogistic {
            get {
                return ResourceManager.GetString("EditReverseLogistic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Edit.
        /// </summary>
        internal static string EditReverseLogisticsBulk {
            get {
                return ResourceManager.GetString("EditReverseLogisticsBulk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Shipping Info.
        /// </summary>
        internal static string EditShippingInfo {
            get {
                return ResourceManager.GetString("EditShippingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Trust.
        /// </summary>
        internal static string EditTrust {
            get {
                return ResourceManager.GetString("EditTrust", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Book Slot.
        /// </summary>
        internal static string EI_FetchSlot {
            get {
                return ResourceManager.GetString("EI_FetchSlot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email.
        /// </summary>
        internal static string Email {
            get {
                return ResourceManager.GetString("Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Empty Shipment.
        /// </summary>
        internal static string EmptyShortShipment {
            get {
                return ResourceManager.GetString("EmptyShortShipment", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable.
        /// </summary>
        internal static string Enable {
            get {
                return ResourceManager.GetString("Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to End Quarantine.
        /// </summary>
        internal static string EndQuarantine {
            get {
                return ResourceManager.GetString("EndQuarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EPR.
        /// </summary>
        internal static string EPR {
            get {
                return ResourceManager.GetString("EPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Expand All.
        /// </summary>
        internal static string ExpandAll {
            get {
                return ResourceManager.GetString("ExpandAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export.
        /// </summary>
        internal static string Export {
            get {
                return ResourceManager.GetString("Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Download.
        /// </summary>
        internal static string ExportPDFWord {
            get {
                return ResourceManager.GetString("ExportPDFWord", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export to CSV.
        /// </summary>
        internal static string ExportToCSV {
            get {
                return ResourceManager.GetString("ExportToCSV", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export To Excel.
        /// </summary>
        internal static string ExportToExcel {
            get {
                return ResourceManager.GetString("ExportToExcel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send to Purchase Hub.
        /// </summary>
        internal static string ExportToPurchaseHUB {
            get {
                return ResourceManager.GetString("ExportToPurchaseHUB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send email.
        /// </summary>
        internal static string ForgotPassword {
            get {
                return ResourceManager.GetString("ForgotPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send email.
        /// </summary>
        internal static string ForgotUserName {
            get {
                return ResourceManager.GetString("ForgotUserName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forward.
        /// </summary>
        internal static string Forward {
            get {
                return ResourceManager.GetString("Forward", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Forgotten login details.
        /// </summary>
        internal static string FWDPassword {
            get {
                return ResourceManager.GetString("FWDPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GenerateLogin.
        /// </summary>
        internal static string GenerateLogin {
            get {
                return ResourceManager.GetString("GenerateLogin", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Get Counts.
        /// </summary>
        internal static string GetCounts {
            get {
                return ResourceManager.GetString("GetCounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Get Data.
        /// </summary>
        internal static string GetData {
            get {
                return ResourceManager.GetString("GetData", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Section.
        /// </summary>
        internal static string GIButtons {
            get {
                return ResourceManager.GetString("GIButtons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Click Here to Add Photos or View.
        /// </summary>
        internal static string GIImage {
            get {
                return ResourceManager.GetString("GIImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        internal static string GIPrintBtn {
            get {
                return ResourceManager.GetString("GIPrintBtn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Query.
        /// </summary>
        internal static string GiQueryMessage {
            get {
                return ResourceManager.GetString("GiQueryMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Split.
        /// </summary>
        internal static string GISplitSave {
            get {
                return ResourceManager.GetString("GISplitSave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete Inspection.
        /// </summary>
        internal static string GI_CloseInspection {
            get {
                return ResourceManager.GetString("GI_CloseInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Inspection.
        /// </summary>
        internal static string GI_StartInspection {
            get {
                return ResourceManager.GetString("GI_StartInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go.
        /// </summary>
        internal static string Go {
            get {
                return ResourceManager.GetString("Go", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Members.
        /// </summary>
        internal static string GSAManageMember {
            get {
                return ResourceManager.GetString("GSAManageMember", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Header.
        /// </summary>
        internal static string Header {
            get {
                return ResourceManager.GetString("Header", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Header.
        /// </summary>
        internal static string HeaderDelete {
            get {
                return ResourceManager.GetString("HeaderDelete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Help.
        /// </summary>
        internal static string Help {
            get {
                return ResourceManager.GetString("Help", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home.
        /// </summary>
        internal static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import Sourcing Result.
        /// </summary>
        internal static string ImportSourcingResult {
            get {
                return ResourceManager.GetString("ImportSourcingResult", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inactivate Company.
        /// </summary>
        internal static string InactivateCompany {
            get {
                return ResourceManager.GetString("InactivateCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make contact Inactive.
        /// </summary>
        internal static string InActive {
            get {
                return ResourceManager.GetString("InActive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Physical Inspect.
        /// </summary>
        internal static string Inspect {
            get {
                return ResourceManager.GetString("Inspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Log.
        /// </summary>
        internal static string InternalLog {
            get {
                return ResourceManager.GetString("InternalLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Manager Approve.
        /// </summary>
        internal static string LineManagerApproval {
            get {
                return ResourceManager.GetString("LineManagerApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Manager Decline.
        /// </summary>
        internal static string LineManagerDecline {
            get {
                return ResourceManager.GetString("LineManagerDecline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approve Independent Testing Recommended.
        /// </summary>
        internal static string LineManagerIndp {
            get {
                return ResourceManager.GetString("LineManagerIndp", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link/Unlink.
        /// </summary>
        internal static string LinkAccount {
            get {
                return ResourceManager.GetString("LinkAccount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link Account.
        /// </summary>
        internal static string Link_Account {
            get {
                return ResourceManager.GetString("Link Account", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login.
        /// </summary>
        internal static string Login {
            get {
                return ResourceManager.GetString("Login", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logout.
        /// </summary>
        internal static string Logout {
            get {
                return ResourceManager.GetString("Logout", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release from Quarantine.
        /// </summary>
        internal static string MakeAvailable {
            get {
                return ResourceManager.GetString("MakeAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default Billing.
        /// </summary>
        internal static string MakeDefaultBilling {
            get {
                return ResourceManager.GetString("MakeDefaultBilling", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default for POs.
        /// </summary>
        internal static string MakeDefaultPO {
            get {
                return ResourceManager.GetString("MakeDefaultPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default PO Ledger.
        /// </summary>
        internal static string MakeDefaultPOLedger {
            get {
                return ResourceManager.GetString("MakeDefaultPOLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default Shipping.
        /// </summary>
        internal static string MakeDefaultShipping {
            get {
                return ResourceManager.GetString("MakeDefaultShipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default for SOs.
        /// </summary>
        internal static string MakeDefaultSO {
            get {
                return ResourceManager.GetString("MakeDefaultSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default SO Ledger.
        /// </summary>
        internal static string MakeDefaultSOLedger {
            get {
                return ResourceManager.GetString("MakeDefaultSOLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturers.
        /// </summary>
        internal static string Manufacturers {
            get {
                return ResourceManager.GetString("Manufacturers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Map product to category.
        /// </summary>
        internal static string Map {
            get {
                return ResourceManager.GetString("Map", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark All Franchised.
        /// </summary>
        internal static string MarkAllFranchised {
            get {
                return ResourceManager.GetString("MarkAllFranchised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark Complete.
        /// </summary>
        internal static string MarkasComplete {
            get {
                return ResourceManager.GetString("MarkasComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create To Do Item.
        /// </summary>
        internal static string MarkAsToDo {
            get {
                return ResourceManager.GetString("MarkAsToDo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark Complete.
        /// </summary>
        internal static string MarkComplete {
            get {
                return ResourceManager.GetString("MarkComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark as Firm Alternate.
        /// </summary>
        internal static string MarkFirmAlt {
            get {
                return ResourceManager.GetString("MarkFirmAlt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark Franchised.
        /// </summary>
        internal static string MarkFranchised {
            get {
                return ResourceManager.GetString("MarkFranchised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark Incomplete.
        /// </summary>
        internal static string MarkIncomplete {
            get {
                return ResourceManager.GetString("MarkIncomplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Further 6 Months.
        /// </summary>
        internal static string More {
            get {
                return ResourceManager.GetString("More", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move Message(s).
        /// </summary>
        internal static string MoveMessage {
            get {
                return ResourceManager.GetString("MoveMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New.
        /// </summary>
        internal static string New {
            get {
                return ResourceManager.GetString("New", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Folder.
        /// </summary>
        internal static string NewFolder {
            get {
                return ResourceManager.GetString("NewFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Message.
        /// </summary>
        internal static string NewMessage {
            get {
                return ResourceManager.GetString("NewMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No.
        /// </summary>
        internal static string No {
            get {
                return ResourceManager.GetString("No", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No-Bid.
        /// </summary>
        internal static string NoBid {
            get {
                return ResourceManager.GetString("NoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No-Bid All.
        /// </summary>
        internal static string NoBidAll {
            get {
                return ResourceManager.GetString("NoBidAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string Notify {
            get {
                return ResourceManager.GetString("Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR.
        /// </summary>
        internal static string NPR {
            get {
                return ResourceManager.GetString("NPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit NPR Status.
        /// </summary>
        internal static string NPRPrintStatus {
            get {
                return ResourceManager.GetString("NPRPrintStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Off.
        /// </summary>
        internal static string Off {
            get {
                return ResourceManager.GetString("Off", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EUU Form.pdf.
        /// </summary>
        internal static string OGELEUUForm {
            get {
                return ResourceManager.GetString("OGELEUUForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OK.
        /// </summary>
        internal static string OK {
            get {
                return ResourceManager.GetString("OK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hold Export.
        /// </summary>
        internal static string OnHold {
            get {
                return ResourceManager.GetString("OnHold", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Item.
        /// </summary>
        internal static string OpenItem {
            get {
                return ResourceManager.GetString("OpenItem", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders.
        /// </summary>
        internal static string Orders {
            get {
                return ResourceManager.GetString("Orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay By Credit Card.
        /// </summary>
        internal static string PayByCreditCard {
            get {
                return ResourceManager.GetString("PayByCreditCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspect.
        /// </summary>
        internal static string PhysicalInspect {
            get {
                return ResourceManager.GetString("PhysicalInspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print POR.
        /// </summary>
        internal static string POR {
            get {
                return ResourceManager.GetString("POR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Post.
        /// </summary>
        internal static string Post {
            get {
                return ResourceManager.GetString("Post", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Post All.
        /// </summary>
        internal static string PostAll {
            get {
                return ResourceManager.GetString("PostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print.
        /// </summary>
        internal static string Print {
            get {
                return ResourceManager.GetString("Print", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print All.
        /// </summary>
        internal static string PrintAll {
            get {
                return ResourceManager.GetString("PrintAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ECCN Log.
        /// </summary>
        internal static string PrintEnqEccnForm {
            get {
                return ResourceManager.GetString("PrintEnqEccnForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Enquiry Form.
        /// </summary>
        internal static string PrintEnqForm {
            get {
                return ResourceManager.GetString("PrintEnqForm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Label.
        /// </summary>
        internal static string PrintLabel {
            get {
                return ResourceManager.GetString("PrintLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Nice Label.
        /// </summary>
        internal static string PrintLabelForNiceWatch {
            get {
                return ResourceManager.GetString("PrintLabelForNiceWatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Label.
        /// </summary>
        internal static string PrintNiceLabel {
            get {
                return ResourceManager.GetString("PrintNiceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Preview.
        /// </summary>
        internal static string PrintPreview {
            get {
                return ResourceManager.GetString("PrintPreview", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ProForma.
        /// </summary>
        internal static string PrintProForma {
            get {
                return ResourceManager.GetString("PrintProForma", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospects.
        /// </summary>
        internal static string Prospects {
            get {
                return ResourceManager.GetString("Prospects", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Approve.
        /// </summary>
        internal static string QualityApproval {
            get {
                return ResourceManager.GetString("QualityApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Decline.
        /// </summary>
        internal static string QualityDecline {
            get {
                return ResourceManager.GetString("QualityDecline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Escalate.
        /// </summary>
        internal static string QualityEscalate {
            get {
                return ResourceManager.GetString("QualityEscalate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantine.
        /// </summary>
        internal static string Quarantine {
            get {
                return ResourceManager.GetString("Quarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantine.
        /// </summary>
        internal static string QuarantineProduct {
            get {
                return ResourceManager.GetString("QuarantineProduct", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to QuickAdd.
        /// </summary>
        internal static string QuickAdd {
            get {
                return ResourceManager.GetString("QuickAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote.
        /// </summary>
        internal static string Quote {
            get {
                return ResourceManager.GetString("Quote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready To Ship.
        /// </summary>
        internal static string ReadyToShip {
            get {
                return ResourceManager.GetString("ReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recall No-Bid.
        /// </summary>
        internal static string RecallNoBid {
            get {
                return ResourceManager.GetString("RecallNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive.
        /// </summary>
        internal static string Receive {
            get {
                return ResourceManager.GetString("Receive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Selected.
        /// </summary>
        internal static string ReceiveSelected {
            get {
                return ResourceManager.GetString("ReceiveSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add/Edit Lot Code.
        /// </summary>
        internal static string RecordLotNo {
            get {
                return ResourceManager.GetString("RecordLotNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add/Edit Serial No.
        /// </summary>
        internal static string RecordSerialNo {
            get {
                return ResourceManager.GetString("RecordSerialNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refresh to load data.
        /// </summary>
        internal static string RefGrid {
            get {
                return ResourceManager.GetString("RefGrid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reject.
        /// </summary>
        internal static string Reject {
            get {
                return ResourceManager.GetString("Reject", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release.
        /// </summary>
        internal static string Release {
            get {
                return ResourceManager.GetString("Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release All.
        /// </summary>
        internal static string ReleaseAll {
            get {
                return ResourceManager.GetString("ReleaseAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release.
        /// </summary>
        internal static string ReleaseLine {
            get {
                return ResourceManager.GetString("ReleaseLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release Stock.
        /// </summary>
        internal static string ReleaseStock {
            get {
                return ResourceManager.GetString("ReleaseStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Partwatch.
        /// </summary>
        internal static string RemovePartwatch {
            get {
                return ResourceManager.GetString("RemovePartwatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replace Image.
        /// </summary>
        internal static string ReplaceImage {
            get {
                return ResourceManager.GetString("ReplaceImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reply.
        /// </summary>
        internal static string Reply {
            get {
                return ResourceManager.GetString("Reply", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report.
        /// </summary>
        internal static string Report {
            get {
                return ResourceManager.GetString("Report", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report NPR.
        /// </summary>
        internal static string ReportNPR {
            get {
                return ResourceManager.GetString("ReportNPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to STO.
        /// </summary>
        internal static string ReportSTO {
            get {
                return ResourceManager.GetString("ReportSTO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request For Quote.
        /// </summary>
        internal static string RequestForQuote {
            get {
                return ResourceManager.GetString("RequestForQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        internal static string Reset {
            get {
                return ResourceManager.GetString("Reset", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset current print setting.
        /// </summary>
        internal static string ResetConsolidate {
            get {
                return ResourceManager.GetString("ResetConsolidate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        internal static string ResetPassword {
            get {
                return ResourceManager.GetString("ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Edit History.
        /// </summary>
        internal static string ReverseLogisticBulkEditHistory {
            get {
                return ResourceManager.GetString("ReverseLogisticBulkEditHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Run Report.
        /// </summary>
        internal static string RunReport {
            get {
                return ResourceManager.GetString("RunReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Exit.
        /// </summary>
        internal static string saBtnExit {
            get {
                return ResourceManager.GetString("saBtnExit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notify.
        /// </summary>
        internal static string saBtnNotify {
            get {
                return ResourceManager.GetString("saBtnNotify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Email Log.
        /// </summary>
        internal static string SAEmailLog {
            get {
                return ResourceManager.GetString("SAEmailLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to General Terms and Condition of purchase.pdf.
        /// </summary>
        internal static string SATermAndCondetion {
            get {
                return ResourceManager.GetString("SATermAndCondetion", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to +.
        /// </summary>
        internal static string SAUpload {
            get {
                return ResourceManager.GetString("SAUpload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        internal static string Save {
            get {
                return ResourceManager.GetString("Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save and exit.
        /// </summary>
        internal static string SaveAndExit {
            get {
                return ResourceManager.GetString("SaveAndExit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save and Release.
        /// </summary>
        internal static string SaveAndRelease {
            get {
                return ResourceManager.GetString("SaveAndRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save and Send.
        /// </summary>
        internal static string SaveAndSend {
            get {
                return ResourceManager.GetString("SaveAndSend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save as Default.
        /// </summary>
        internal static string SaveAsDefault {
            get {
                return ResourceManager.GetString("SaveAsDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save as HUBRFQ.
        /// </summary>
        internal static string SaveasHUBRFQ {
            get {
                return ResourceManager.GetString("SaveasHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save and exit.
        /// </summary>
        internal static string SaveExit {
            get {
                return ResourceManager.GetString("SaveExit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export.
        /// </summary>
        internal static string SaveForExport {
            get {
                return ResourceManager.GetString("SaveForExport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        internal static string Search {
            get {
                return ResourceManager.GetString("Search", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go.
        /// </summary>
        internal static string SearchCompany {
            get {
                return ResourceManager.GetString("SearchCompany", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select.
        /// </summary>
        internal static string Select {
            get {
                return ResourceManager.GetString("Select", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select All.
        /// </summary>
        internal static string SelectAll {
            get {
                return ResourceManager.GetString("SelectAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select All Available For Shipping.
        /// </summary>
        internal static string SelectAllAvailableForShipping {
            get {
                return ResourceManager.GetString("SelectAllAvailableForShipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send.
        /// </summary>
        internal static string Send {
            get {
                return ResourceManager.GetString("Send", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request Approval.
        /// </summary>
        internal static string SendPowerSOR {
            get {
                return ResourceManager.GetString("SendPowerSOR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send query.
        /// </summary>
        internal static string SendQuery {
            get {
                return ResourceManager.GetString("SendQuery", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send To Supplier.
        /// </summary>
        internal static string SendToSupplier {
            get {
                return ResourceManager.GetString("SendToSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm SO sent.
        /// </summary>
        internal static string SentOrder {
            get {
                return ResourceManager.GetString("SentOrder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship.
        /// </summary>
        internal static string Ship {
            get {
                return ResourceManager.GetString("Ship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship All.
        /// </summary>
        internal static string ShipAll {
            get {
                return ResourceManager.GetString("ShipAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Selected.
        /// </summary>
        internal static string ShipSelected {
            get {
                return ResourceManager.GetString("ShipSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show.
        /// </summary>
        internal static string Show {
            get {
                return ResourceManager.GetString("Show", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View.
        /// </summary>
        internal static string ShowRelDocument {
            get {
                return ResourceManager.GetString("ShowRelDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Stock As Available.
        /// </summary>
        internal static string ShowStockAvailable {
            get {
                return ResourceManager.GetString("ShowStockAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save.
        /// </summary>
        internal static string SIGISave {
            get {
                return ResourceManager.GetString("SIGISave", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Snooze.
        /// </summary>
        internal static string Snooze {
            get {
                return ResourceManager.GetString("Snooze", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source.
        /// </summary>
        internal static string Source {
            get {
                return ResourceManager.GetString("Source", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split.
        /// </summary>
        internal static string Split {
            get {
                return ResourceManager.GetString("Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split GI Line.
        /// </summary>
        internal static string SplitGI {
            get {
                return ResourceManager.GetString("SplitGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import to Sourcing Strategic Offers.
        /// </summary>
        internal static string StockImportEPOTool {
            get {
                return ResourceManager.GetString("StockImportEPOTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers Import Tool.
        /// </summary>
        internal static string StockImportTool {
            get {
                return ResourceManager.GetString("StockImportTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Provision.
        /// </summary>
        internal static string StockProvision {
            get {
                return ResourceManager.GetString("StockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SupplierMapping.
        /// </summary>
        internal static string SupplierMapping {
            get {
                return ResourceManager.GetString("SupplierMapping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Suppliers.
        /// </summary>
        internal static string Suppliers {
            get {
                return ResourceManager.GetString("Suppliers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transactions.
        /// </summary>
        internal static string Transactions {
            get {
                return ResourceManager.GetString("Transactions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Accounts.
        /// </summary>
        internal static string TransferAccounts {
            get {
                return ResourceManager.GetString("TransferAccounts", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Service.
        /// </summary>
        internal static string TransferService {
            get {
                return ResourceManager.GetString("TransferService", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Services to Different Lot.
        /// </summary>
        internal static string TransferServicesToDifferentLot {
            get {
                return ResourceManager.GetString("TransferServicesToDifferentLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Stock.
        /// </summary>
        internal static string TransferStock {
            get {
                return ResourceManager.GetString("TransferStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Stock to Different Lot.
        /// </summary>
        internal static string TransferStockToDifferentLot {
            get {
                return ResourceManager.GetString("TransferStockToDifferentLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unchecked.
        /// </summary>
        internal static string UnChecked {
            get {
                return ResourceManager.GetString("UnChecked", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un Hold Export.
        /// </summary>
        internal static string UnHoldInvoice {
            get {
                return ResourceManager.GetString("UnHoldInvoice", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpost.
        /// </summary>
        internal static string Unpost {
            get {
                return ResourceManager.GetString("Unpost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpost All.
        /// </summary>
        internal static string UnpostAll {
            get {
                return ResourceManager.GetString("UnpostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ReCall.
        /// </summary>
        internal static string UnRelease {
            get {
                return ResourceManager.GetString("UnRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UnRelease .
        /// </summary>
        internal static string UnReleaseLine {
            get {
                return ResourceManager.GetString("UnReleaseLine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update.
        /// </summary>
        internal static string Update {
            get {
                return ResourceManager.GetString("Update", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload.
        /// </summary>
        internal static string Upload {
            get {
                return ResourceManager.GetString("Upload", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View.
        /// </summary>
        internal static string View {
            get {
                return ResourceManager.GetString("View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Image.
        /// </summary>
        internal static string ViewImage {
            get {
                return ResourceManager.GetString("ViewImage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View saved PDF.
        /// </summary>
        internal static string ViewPDF {
            get {
                return ResourceManager.GetString("ViewPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Task.
        /// </summary>
        internal static string ViewTask {
            get {
                return ResourceManager.GetString("ViewTask", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Tree.
        /// </summary>
        internal static string ViewTree {
            get {
                return ResourceManager.GetString("ViewTree", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create IPO.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Yes.
        /// </summary>
        internal static string Yes {
            get {
                return ResourceManager.GetString("Yes", resourceCulture);
            }
        }
    }
}
