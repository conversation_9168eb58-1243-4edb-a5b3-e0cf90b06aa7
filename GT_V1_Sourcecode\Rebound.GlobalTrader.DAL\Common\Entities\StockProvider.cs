﻿/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for stock section
[002]      Vinay           17/04/2014   CR:- Stock Provision
[003]      Vinay           30/07/2015   ESMS Ticket No: 255
[004]      A<PERSON><PERSON><PERSON>  17/10/2022   (RP-31) Add new methods for this lot chnages.
[005]      <PERSON>     21/02/2023   (RP-31) Ticket No: 217.
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL
{

    public abstract class StockProvider : DataAccess
    {
        static private StockProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public StockProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (StockProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.Stocks.ProviderType));
                return _instance;
            }
        }
        public StockProvider()
        {
            this.ConnectionString = Globals.Settings.Stocks.ConnectionString;
            this.GTConnectionString = Globals.Settings.Stocks.GTConnectionString;
        }

        #region Method Registrations

        /// <summary>
        /// AutoSearch
        /// Calls [usp_autosearch_Stock]
        /// </summary>
        public abstract List<StockDetails> AutoSearch(System.Int32? clientId, System.String nameSearch);
        /// <summary>
        /// CountForClient
        /// Calls [usp_count_Stock_for_Client]
        /// </summary>
        public abstract Int32 CountForClient(System.Int32? clientId);
        /// <summary>
        /// DataListNugget
        /// Calls [usp_datalistnugget_Stock]
        /// </summary>
        public abstract List<StockDetails> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Boolean? quarantine, System.String partSearch, System.Int32? lotNo, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.String supplierPartSearch, System.String supplierNameSearch, System.String locationSearch, System.Int32? warehouseNo, System.Boolean? recentOnly, System.Int32? customerRmaNoLo, System.Int32? customerRmaNoHi, System.Boolean? includeZeroStock, System.Int32? clientSearch, int? isPoHub, Boolean IsGlobalLogin, System.Int32? stockNoLo, System.Int32? stockNoHi, System.Boolean? AS6081);
        /// <summary>
        /// Delete
        /// Calls [usp_delete_Stock]
        /// </summary>
        public abstract bool Delete(System.Int32? stockId);
        /// <summary>
        /// DeleteUnallocatedForLot
        /// Calls [usp_delete_Stock_Unallocated_for_Lot]
        /// </summary>
        public abstract bool DeleteUnallocatedForLot(System.Int32? lotNo);
        /// <summary>
        /// 
        /// Calls [usp_Import_Stock]
        /// </summary>
        /// <summary>
        /// 2
        /// Calls [usp_Import_Stock_2]
        /// </summary>
        /// <summary>
        /// Insert
        /// Calls [usp_insert_Stock]
        /// </summary>
        public abstract Int32 Insert(System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.String stockLogChangeNotes, System.String location, System.Int32? countryOfManufacture, System.String partMarkings, System.Int32? countingMethodNo, System.Int32? updatedBy, System.Int32? divisionNo, System.String mslLevel, System.Boolean AS6081, System.Double? ClientUPLiftPrice);
        /// <summary>
        /// InsertIdentityOff
        /// Calls [usp_insert_Stock_Identity_Off]
        /// </summary>
        public abstract Int32 InsertIdentityOff(System.Int32? stockId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? goodsInLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.String stockLogChangeNotes, System.String location, System.Int32? updatedBy);
        /// <summary>
        /// InsertSplit
        /// Calls [usp_insert_Stock_Split]
        /// </summary>
        public abstract Int32 InsertSplit(System.Int32? stockId, System.Int32? quantitySplit, System.String location, System.Int32? updatedBy);
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_Stock]
        /// </summary>
        public abstract List<StockDetails> ItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.Boolean? forRmAs, System.Int32? supplierRmaNo, System.Boolean? includeQuarantined, System.Boolean? includeLotsOnHold, System.Int32? poNoLo, System.Int32? poNoHi, System.Int32? crmaNoLo, System.Int32? crmaNoHi, System.Int32? warehouseNo, System.String location, System.Int32? incLockCustNo);
        /// <summary>
        /// ItemSearch
        /// Calls [usp_itemsearch_IpoStock]
        /// </summary>
        public abstract List<StockDetails> IpoItemSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.Boolean? forRmAs, System.Int32? supplierRmaNo, System.Boolean? includeQuarantined, System.Boolean? includeLotsOnHold, System.Int32? poNoLo, System.Int32? poNoHi, System.Int32? crmaNoLo, System.Int32? crmaNoHi, System.Int32? warehouseNo, System.String location, System.Int32? incLockCustNo, int? salesOrderNo, System.Boolean? stopNONIpoStock);
        /// <summary>
        /// Get
        /// Calls [usp_select_Stock]
        /// </summary>
        public abstract StockDetails Get(System.Int32? stockId);
        /// <summary>
        /// Get 
        /// Calls [USP_GetSTOIds]
        /// </summary>
        public abstract DataTable GetSTOIds(Boolean? IsHub, System.Int32? stockId);
        /// <summary>
        /// Get 
        /// Calls [USP_GetSTO]
        /// </summary>
        public abstract DataTable GetSTO(System.Int32 STOId);
        /// <summary>
        /// GetForCustomerRMALine
        /// Calls [usp_select_Stock_for_CustomerRMALine]
        /// </summary>
        public abstract StockDetails GetForCustomerRMALine(System.Int32? customerRmaLineId);
        /// <summary>
        /// GetForPage
        /// Calls [usp_select_Stock_for_Page]
        /// </summary>
        public abstract StockDetails GetForPage(System.Int32? stockId);
        /// <summary>
        /// GetForPurchaseOrderLine
        /// Calls [usp_select_Stock_for_PurchaseOrderLine]
        /// </summary>
        public abstract StockDetails GetForPurchaseOrderLine(System.Int32? purchaseOrderLineId);
        /// <summary>
        /// GetListForLot
        /// Calls [usp_selectAll_Stock_for_Lot]
        /// </summary>
        public abstract List<StockDetails> GetListForLot(System.Int32? lotId);
        /// <summary>
        /// usp_selectNonZero_Stock_for_Lot
        /// </summary>
        /// <param name="lotId"></param>
        /// <returns></returns>
        public abstract List<StockDetails> GetListForNonZeroStockLot(System.Int32? lotId);
        /// <summary>
        /// GetListRelatedStock
        /// Calls [usp_selectAll_Stock_RelatedStock]
        /// </summary>
        public abstract List<StockDetails> GetListRelatedStock(System.Int32? stockNo);
        /// <summary>
        /// Source
        /// Calls [usp_source_Stock]
        /// </summary>
        public abstract List<StockDetails> Source(System.Int32? clientId, System.String partSearch, bool IsServerLocal);

        //[003] code start
        /// <summary>
        /// Update
        /// Calls [usp_update_Stock]
        /// </summary>
        public abstract bool Update(System.Int32? stockId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? warehouseNo, System.Int32? clientNo, System.String qualityControlNotes, System.Int32? purchaseOrderNo, System.Int32? purchaseOrderLineNo, System.Int32? customerRmaNo, System.Int32? customerRmaLineNo, System.Int32? quantityInStock, System.Int32? quantityOnOrder, System.Int32? productNo, System.Double? resalePrice, System.Boolean? unavailable, System.Int32? lotNo, System.Double? landedCost, System.String supplierPart, System.Byte? rohs, System.Int32? packageUnit, System.Int32? stockKeepingUnit, System.Int32? updatedBy, System.String stockLogDetail, System.String stockLogChangeNotes, System.Int32? stockLogReasonNo, System.String location, System.Int32? countryOfManufacture, System.Boolean? updateShipments, System.String partMarkings, System.Int32? countingMethodNo, System.Int32? divisionNo,System.Boolean? isClientUpdate, System.String mslLevelout, System.Double? ClientUPLiftPrice, out System.String strLotMessage, System.Boolean AS6081 = false);
        //[003] code end
        /// <summary>
        /// UpdateQuarantined
        /// Calls [usp_update_Stock_Quarantined]
        /// </summary>
        public abstract bool UpdateQuarantined(System.Int32? stockId, System.Boolean? quarantine, System.String location, System.Int32? updatedBy);
        /// <summary>
        /// call [usp_Get_All_GILine_ID_From_Stock]
        /// </summary>
        /// <param name="stockId"></param>
        /// <returns></returns>
        public abstract List<int> GetAllGILineIDsFromStock(System.Int32? stockId);
        /// <summary>
        /// call [usp_update_GILine_Quarantined_From_Stock]
        /// </summary>
        /// <param name="giLineId"></param>
        /// <param name="logInID"></param>
        /// <param name="quarantine"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public abstract bool UpdateGILineQuarantineFromStock(int giLineId, int updatedBy, int? clientId);
        /// <summary>
        /// UpdateTransferLot
        /// Calls [usp_update_Stock_Transfer_Lot]
        /// </summary>
        public abstract bool UpdateTransferLot(System.Int32? oldNotNo, System.Int32? newLotNo);
        // [001] code start
        /// <summary>
        /// Get PDF list for stock
        /// Calls [usp_selectAll_PDF_for_Stock]
        /// </summary>
        /// <param name="SalesOrderId"></param>
        /// <returns></returns>
        public abstract List<PDFDocumentDetails> GetPDFListForStock(System.Int32? StockId);

        /// <summary>
        /// Insert PDF for stock
        /// Calls [usp_insert_StockPDF]
        /// </summary>
        /// <param name="StockId"></param>
        /// <param name="Caption"></param>
        /// <param name="FileName"></param>
        /// <param name="UpdatedBy"></param>
        /// <returns></returns>
        public abstract Int32 Insert(System.Int32? StockId, System.String Caption, System.String FileName, System.Int32? UpdatedBy);
        /// <summary>
        /// Delete stock pdf
        /// Calls[usp_delete_StockPDF]
        /// </summary>
        /// <param name="StockPdfId"></param>
        /// <returns></returns>
        public abstract bool DeleteStockPDF(System.Int32? StockPdfId);
        // [001] code end
        //[002] code start
        /// <summary>
        /// Calls [usp_update_Stock_Provision]
        /// </summary>
        /// <param name="stockId"></param>
        /// <param name="landedCost"></param>
        /// <param name="newLandedCost"></param>
        /// <param name="updatedBy"></param>
        /// <returns></returns>
        public abstract bool UpdateStockProvision(System.Int32? stockId, System.Double? newLandedCost, System.Int32? updatedBy, System.Int32? percentageValue);
        //[002] code end
        public abstract bool UpdateHubLandedCost(System.Int32? stockId, System.Double? newLandedCost, System.Double? resalePrice, System.Int32? updatedBy);
        /// <summary>
        /// Calls [usp_select_BomClients]
        /// [005]
        /// </summary>      
        public abstract DataTable GetBomClient(System.Int32? clientId, System.Int32? UserId);
        public abstract DataTable GetBomCusReq(System.Int32? clientId, System.Int32? BomNO, System.Int32? UserId);
        //public abstract List<List<object>> GetBOMrCRList(System.Int32? BOMNo, System.Int32? clientID);
        //[005]
        public abstract DataTable GetClient(System.Int32? clientId);
        public abstract DataTable GetIndustryType(System.Int32? clientId);


        #endregion

        /// <summary>
        /// Returns a new StockDetails instance filled with the DataReader's current record data
        /// </summary>        
        protected virtual StockDetails GetStockFromReader(DbDataReader reader)
        {
            StockDetails stock = new StockDetails();
            if (reader.HasRows)
            {
                stock.StockId = GetReaderValue_Int32(reader, "StockId", 0); //From: [Table]
                stock.FullPart = GetReaderValue_String(reader, "FullPart", ""); //From: [Table]
                stock.Part = GetReaderValue_String(reader, "Part", ""); //From: [Table]
                stock.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null); //From: [Table]
                stock.DateCode = GetReaderValue_String(reader, "DateCode", ""); //From: [Table]
                stock.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null); //From: [Table]
                stock.WarehouseNo = GetReaderValue_NullableInt32(reader, "WarehouseNo", null); //From: [Table]
                stock.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0); //From: [Table]
                stock.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", ""); //From: [Table]
                stock.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null); //From: [Table]
                stock.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null); //From: [Table]
                stock.QuantityInStock = GetReaderValue_Int32(reader, "QuantityInStock", 0); //From: [Table]
                stock.QuantityOnOrder = GetReaderValue_Int32(reader, "QuantityOnOrder", 0); //From: [Table]
                stock.Location = GetReaderValue_String(reader, "Location", ""); //From: [Table]
                stock.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null); //From: [Table]
                stock.ResalePrice = GetReaderValue_NullableDouble(reader, "ResalePrice", null); //From: [Table]
                stock.Unavailable = GetReaderValue_Boolean(reader, "Unavailable", false); //From: [Table]
                stock.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null); //From: [Table]
                stock.LandedCost = GetReaderValue_NullableDouble(reader, "LandedCost", null); //From: [Table]
                stock.SupplierPart = GetReaderValue_String(reader, "SupplierPart", ""); //From: [Table]
                stock.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0); //From: [Table]
                stock.PackageUnit = GetReaderValue_NullableInt32(reader, "PackageUnit", null); //From: [Table]
                stock.StockKeepingUnit = GetReaderValue_NullableInt32(reader, "StockKeepingUnit", null); //From: [Table]
                stock.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null); //From: [Table]
                stock.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null); //From: [Table]
                stock.GoodsInLineNo = GetReaderValue_NullableInt32(reader, "GoodsInLineNo", null); //From: [Table]
                stock.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null); //From: [Table]
                stock.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue); //From: [Table]
                stock.FullSupplierPart = GetReaderValue_String(reader, "FullSupplierPart", ""); //From: [Table]
                stock.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null); //From: [Table]
                stock.PartMarkings = GetReaderValue_String(reader, "PartMarkings", ""); //From: [Table]
                stock.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null); //From: [Table]
                stock.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", ""); //From: [usp_datalistnugget_Stock]
                stock.QuantityAllocated = GetReaderValue_Int32(reader, "QuantityAllocated", 0); //From: [usp_datalistnugget_Stock]
                stock.WarehouseName = GetReaderValue_String(reader, "WarehouseName", ""); //From: [usp_datalistnugget_Stock]
                stock.LotName = GetReaderValue_String(reader, "LotName", ""); //From: [usp_datalistnugget_Stock]
                stock.SupplierNo = GetReaderValue_NullableInt32(reader, "SupplierNo", null); //From: [usp_datalistnugget_Stock]
                stock.SupplierName = GetReaderValue_String(reader, "SupplierName", ""); //From: [usp_datalistnugget_Stock]
                stock.QuantityAvailable = GetReaderValue_NullableInt32(reader, "QuantityAvailable", null); //From: [usp_datalistnugget_Stock]
                stock.StatusNo = GetReaderValue_NullableInt32(reader, "StatusNo", null); //From: [usp_datalistnugget_Stock]
                stock.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null); //From: [usp_datalistnugget_Stock]
                stock.PODeliveryDate = GetReaderValue_NullableDateTime(reader, "PODeliveryDate", null); //From: [usp_itemsearch_Stock]
                stock.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null); //From: [usp_itemsearch_Stock]
                stock.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null); //From: [usp_itemsearch_Stock]
                stock.CustomerRMADate = GetReaderValue_NullableDateTime(reader, "CustomerRMADate", null); //From: [usp_itemsearch_Stock]
                stock.PackageName = GetReaderValue_String(reader, "PackageName", ""); //From: [usp_select_Stock]
                stock.PackageDescription = GetReaderValue_String(reader, "PackageDescription", ""); //From: [usp_select_Stock]
                stock.ProductName = GetReaderValue_String(reader, "ProductName", ""); //From: [usp_select_Stock]
                stock.ProductDescription = GetReaderValue_String(reader, "ProductDescription", ""); //From: [usp_select_Stock]
                stock.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", ""); //From: [usp_select_Stock]
                stock.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", ""); //From: [usp_select_Stock]
                stock.LotCode = GetReaderValue_String(reader, "LotCode", ""); //From: [usp_select_Stock]
                stock.Buyer = GetReaderValue_NullableInt32(reader, "Buyer", null); //From: [usp_select_Stock]
                stock.BuyerName = GetReaderValue_String(reader, "BuyerName", ""); //From: [usp_select_Stock]
                stock.GoodsInNo = GetReaderValue_NullableInt32(reader, "GoodsInNo", null); //From: [usp_select_Stock]
                stock.GoodsInPrice = GetReaderValue_NullableDouble(reader, "GoodsInPrice", null); //From: [usp_select_Stock]
                stock.GoodsInShipInCost = GetReaderValue_NullableDouble(reader, "GoodsInShipInCost", null); //From: [usp_select_Stock]
                stock.GoodsInNumber = GetReaderValue_NullableInt32(reader, "GoodsInNumber", null); //From: [usp_select_Stock]
                stock.GoodsInCurrencyNo = GetReaderValue_NullableInt32(reader, "GoodsInCurrencyNo", null); //From: [usp_select_Stock]
                stock.StockDate = GetReaderValue_DateTime(reader, "StockDate", DateTime.MinValue); //From: [usp_select_Stock]
                stock.ROHSStatus = GetReaderValue_String(reader, "ROHSStatus", ""); //From: [usp_select_Stock]
                stock.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", ""); //From: [usp_select_Stock]
                stock.PurchasePrice = GetReaderValue_NullableDouble(reader, "PurchasePrice", null); //From: [usp_select_Stock]
                stock.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", ""); //From: [usp_select_Stock]
                stock.StockLogDetail = GetReaderValue_String(reader, "StockLogDetail", ""); //From: [usp_select_Stock]
                stock.StockLogChangeNotes = GetReaderValue_String(reader, "StockLogChangeNotes", ""); //From: [usp_select_Stock]
                stock.StockLogReasonNo = GetReaderValue_NullableInt32(reader, "StockLogReasonNo", null); //From: [usp_select_Stock]
                stock.UpdateShipments = GetReaderValue_NullableBoolean(reader, "UpdateShipments", null); //From: [usp_select_Stock]
                stock.RelationType = GetReaderValue_String(reader, "RelationType", ""); //From: [usp_selectAll_Stock_RelatedStock]
                stock.ClientName = GetReaderValue_String(reader, "ClientName", ""); //From: [usp_source_Stock]
                stock.ClientDataVisibleToOthers = GetReaderValue_NullableBoolean(reader, "ClientDataVisibleToOthers", null); //From: [usp_source_Stock]
            }
            return stock;
        }

        /// <summary>
        /// Returns a collection of StockDetails objects with the data read from the input DataReader
        /// </summary>                
        protected virtual List<StockDetails> GetStockCollectionFromReader(DbDataReader reader)
        {
            List<StockDetails> stocks = new List<StockDetails>();
            while (reader.Read()) stocks.Add(GetStockFromReader(reader));
            return stocks;
        }

        #region Lot stock provision start

        /// <summary>
        /// usp_selectAll_LotStockProvision
        /// </summary>
        /// <param name="lotId"></param>
        /// <returns></returns>
        public abstract List<StockDetails> GetListStockProvision(System.Int32? lotId);

        /// <summary>
        /// Insert all stock values againest lot id
        /// usp_insert_LotStockDetails
        /// </summary>
        /// <param name="LotId"> lot id</param>
        /// <param name="Percentage">percentage</param>
        /// <param name="UpdatedBy"> updated by</param>
        /// <returns></returns>
        public abstract Int32 InserLotStock(System.Int32? LotId, System.Double? Percentage, System.Int32? UpdatedBy, Double? TotalPrimaryLandedCost, Double? TotalCurrentLandedCost);
        #endregion

        public abstract List<GoodsInLineDetails> GetSerial(System.Int32? stockId);

        public abstract List<StockDetails> GetCustreqtest(System.Int32? BomNo, System.Int32? clientId);


        #region Stock Import Tool
        /// <summary>
        /// GetClientName
        /// Calls [usp_select_clients]
        /// </summary>
        public abstract DataTable GetClientName(System.Int32? clientId, System.Int32? clientType_con);
        /// <summary>
        /// GetImportActivity
        /// Calls [usp_select_importactivity]
        /// </summary>
        public abstract DataTable GetImportActivity(System.Int32? clientId, System.Int32? clientType_con);
        /// <summary>
        /// GetSupplier
        /// Calls [usp_select_suppliers]
        /// </summary>
        public abstract DataTable GetSupplier(System.Int32? clientId);
        /// <summary>
        /// GetCurrency
        /// Calls [usp_select_currencies_buy]
        /// </summary>
        public abstract DataTable GetCurrency(System.Int32? clientId, System.Int32? clientType_con);
        /// <summary>
        /// SaveExcelHeader
        /// Calls [usp_SaveStockExcelColumn]
        /// </summary>
        public abstract void SaveExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);
        /// <summary>
        /// saveExcelData
        /// Calls [Not Used]
        /// </summary>
        public abstract void saveExcelData(DataTable dtstock, int clientType_con);
        /// <summary>
        /// [usp_saveExcelBulkSave]
        /// Calls [SQL BULK COPY INSERT]
        /// </summary>
        public abstract void saveExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con);
        /// <summary>
        /// GetStockDetailFromTemp
        /// Calls [usp_GetStockNewData]
        /// </summary>
        public abstract DataTable GetStockDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);
        /// <summary>
        /// GetStockDetailFromTemp_PriceQuote
        /// Calls [usp_GetStockNewData_PriceQuote]
        /// </summary>
        public abstract DataTable GetStockDetailFromTemp_PriceQuote(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);

        /// <summary>
        /// GetStockImportactivity
        /// Calls [Not Used]
        /// </summary>
        public abstract DataTable GetStockImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid);
        /// <summary>
        /// GetClientName
        /// Calls [usp_GetDynamicHeaderColumn]
        /// </summary>
        public abstract DataTable GetStockGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name);
        /// <summary>
        /// GetCustTableAllColumn
        /// Calls [usp_GetAllColunStockReq]
        /// </summary>
        public abstract DataTable GetCustTableAllColumn();
        /// <summary>
        /// GetClientName
        /// Calls [usp_GetExcelStockHeaderColumn]
        /// </summary>
        public abstract DataTable GetExcelHeader(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        /// <summary>
        /// GetImportExcelHeader
        /// Calls [usp_GetDynamicImportHeaderColumn]
        /// </summary>
        public abstract DataTable GetImportExcelHeader(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, string selectedColumnlist, int clientType_con);
        /// <summary>
        /// GetExcelHeaderFrom
        /// Calls [usp_GetExcelStockHeaderColumn]
        /// </summary>
        public abstract DataTable GetExcelHeaderFrom(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        /// <summary>
        /// DeleteRecord
        /// Calls []
        /// </summary>
        public abstract int DeleteRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con);
        /// <summary>
        /// DeleteTempMapping
        /// Calls [usp_deleteTempStockRecord]
        /// </summary>
        public abstract int DeleteTempMapping(System.Int32 SupplierId, int clientType_con);
        /// <summary>
        /// SaveSupplierColumnMapping
        /// Calls [usp_insert_ImportColumnMapping]
        /// </summary>
        public abstract int SaveSupplierColumnMapping(System.Int32 SupplierId, string insertMapColumnList, int clientType_con);
        /// <summary>
        /// GetSupplierMappedColumn
        /// Calls [usp_ImportSupplierColumnMapping]
        /// </summary>
        public abstract DataTable GetSupplierMappedColumn(System.Int32 SupplierId, int clientType_con);
        /// <summary>
        /// saveStockImportData
        /// Calls [usp_Select_ImportStockData]
        /// </summary>
        public abstract int saveStockImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage);
        /// <summary>
        /// usp_InsertOrUpdate_OfferImportdata
        /// Calls [usp_InsertOrUpdate_OfferImportdata]
        /// </summary>
        public abstract int InsertUpdateStockImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage);

        /// <summary>
        /// SaveImportActivity
        /// Calls [usp_insert_ImportActivity]
        /// </summary>
        public abstract int SaveImportActivity(string insertDataList, int clientType_con);
        public abstract int Delete_OfferRecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType);
        public abstract int Delete_TrustedRecord(string TrustedId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType);
        public abstract int Delete_POQuotesRecord(string POQuotesId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType);

        public abstract int CrossMatchSearchLog(System.Int32 BomId, System.Int32 userId, int ClientId, string logdetails);
        //codee add for CrossMatch Filter Save
        /// <summary>
        /// Insert
        /// Calls [usp_offer_Filter_CrossMatchRequirement]
        /// </summary>
        public abstract Int32 FilterOfferCrossMatchReq(System.Int32 offerId, out System.String errorMessage);
        //end

        //codee add for CrossMatch Filter Delete
        /// <summary>
        /// Insert
        /// Calls [usp_offer_Delete_CrossMatchRequirement]
        /// </summary>
        public abstract Int32 DeleteFilterOfferCrossMatchReq(int offerId, out System.String errorMessage);
        //end


        //codee add for CrossMatch Log Delete
        /// <summary>
        /// Insert
        /// Calls [usp_AutoLog_Delete_CrossMatch]
        /// </summary>
        public abstract Int32 DeleteAutoLogCrossMatchReq(int BomId, int Userid, out System.String errorMessage);
        //end

        //codee add for CrossMatch Export To excel
        /// <summary>
        /// Select for Export
        /// Calls [usp_ExportToExcel_CrossMatch]
        /// </summary>
        public abstract DataTable ExportToCSVCrossMatch(System.Int32? BomID, System.Int32? CustReqId, System.Int32? clientId, System.Int32? UserId);

        //codee add for CrossMatch Export To excel
        /// <summary>
        /// Select for Export
        /// Calls [usp_ExportToExcel_CrossMatch_list]
        /// </summary>
        public abstract DataTable ExportToCSVCrossMatchlist(System.Int32? BomID, System.Int32? CustReqId, System.Int32? clientId, System.Int32? UserId);

        /// <summary>
        /// SaveExcelHeader
        /// Calls [usp_SaveStockExcelColumn]
        /// </summary>
        public abstract void SaveExcelEpoHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);

        /// <summary>
        /// Insert Excel Epo Bulk
        /// Calls [SQL BULK COPY INSERT]
        /// </summary>
        public abstract void saveExcelEpoBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con);

        /// <summary>
        /// Select Epo Header Column 
        /// Calls [usp_GetEpoExcelHeaderColumn]
        /// </summary>
        public abstract DataTable GetEpoExcelHeader(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);

        /// <summary>
        /// Select Epo Data from temp table
        /// Calls [usp_GetEpobindTempData]
        /// </summary>
        public abstract DataTable GetEpoDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);

        /// <summary>
        /// Delete Epo Record
        /// Calls [usp_deleteTempEpoRecord]
        /// </summary>
        public abstract int DeleteEpoRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con);
        /// <summary>
        /// Get Genrate Epo Data
        /// Calls [usp_GenrateDynamicEpoData]
        /// </summary>
        public abstract DataTable GetEpoGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string SupplierNameText, int clientType_con, System.Double? UpliftpValue, int NoChangesToSourcingSheet);


        /// <summary>
        /// Insert Epo Data
        /// Calls [usp_Select_ImportEpoData]
        /// </summary>
        public abstract int saveEpoImportData(int userId, int ClientId, int SelectedclientId, int SupplierId, int recordType, int clientType_con, System.Double? UpliftPercentage, int NoChangesToSourcingSheet, out System.String errorMessage);

        /// <summary>
        /// Insert Epo Import Activity Data
        /// Calls [usp_Select_EpoImportEpoData]
        /// </summary>
        public abstract int SaveEpoImportActivity(string insertDataList, int clientType_con);

        /// <summary>
        /// Get Epo Import Activity Data
        /// Calls [usp_select_Epoimportactivity]
        /// </summary>
        public abstract DataTable GetEpoImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid, int isInactive);

        /// <summary>
        /// Inactivate Epo Import Activity Data
        /// Calls [usp_Inactivate_EpoImportActivity]
        /// </summary>
        public abstract InsertedData InactivateEpoImportActivity(int clientId, int selectedImportId, int userId);

        /// <summary>
        /// Insert Bom Sourcing Bulk
        /// Calls [SQL BULK COPY INSERT]
        /// </summary>
        public abstract void LeftSideExcelSourcingResuleBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int bomId);
        /// <summary>
        /// Insert Bom Sourcing Bulk
        /// Calls [SQL BULK COPY INSERT]
        /// </summary>
        public abstract void RightSideExcelSourcingResuleBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int bomId);

        /// <summary>
        /// Get Bom Sourcing Data
        /// Calls [usp_select_Epoimportactivity]
        /// </summary>
        public abstract DataTable GetLeftSideExcelSourcingResult(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int userLoginid, System.Int32 bomId);

        /// <summary>
        /// Get Bom Sourcing Data
        /// Calls [usp_select_Epoimportactivity]
        /// </summary>
        public abstract DataTable GetRightSideExcelSourcingResult(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int userLoginid, System.Int32 bomId);

        /// <summary>
        /// Delete Bom Sourcing Record
        /// Calls [usp_deleteTempRecordfromLeftandRight]
        /// </summary>
        public abstract int DeleteLeftAndRightData(System.Int32 userId, int ClientId, System.Int32 bomId);

        /// <summary>
        /// Delete Bom Re-ProcessData Record
        /// Calls [usp_Multivendor_Process_Offers]
        /// </summary>
        public abstract int BomReProcessData(System.Int32 bomId, System.Int32 userId, int ClientId);

        /// <summary>
        /// Get Bom Sourcing Data
        /// Calls [usp_select_StatusFileExitOrNot]
        /// </summary>
        public abstract DataTable GetStatusFileExitOrNot(System.Int32 bomId, int ClientId, int userLoginid, string originalFilename);


        /// <summary>
        /// Delete Bom Sourcing Record
        /// Calls [usp_deleteTempRecordfromLeftandRight]
        /// </summary>
        public abstract int updatecellvalueMfrVender(System.Int32 userId, int ClientId, System.Int32? SourcingResultId, string updatestatus, string manufacturename, string Vendor);
        public abstract int updatecellvalueMfr(System.Int32 userId, int ClientId, System.Int32? SourcingResultId, string manufacturename);
        public abstract DataTable GetBOMGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId);
        public abstract DataTable GetBOMManagerGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId);
        public abstract int saveBOMImportData(int userId,
                                              int ClientId,
                                              int SelectedclientId,
                                              string Column_Lable,
                                              string Column_Name,
                                              string insertDataList,
                                              string fileColName,
                                              string ddlCurrency,
                                              out System.String errorMessage,
                                              string BomName,
                                              string CompanyName,
                                              string ContactName,
                                              int SalesmanId,
                                              int CompanyId,
                                              int ContactId,
                                              bool PartWatch,
                                              int DefaultCurrencyId,
                                              bool OverRideCurrency,
                                              string SaveImportOrHubRFQ,
                                              out System.String NewBomCode,
                                              out System.Int32 NewBomid,
                                              System.Int32? ReqforTraceabilityId,
                                              System.Int32? TypeId,
                                              System.DateTime DateRequired,
                                              int? updateBomId,
                                              string originalFileName,
                                              string generatedFileName);
        public abstract int saveHUBOfferImportLarge(int userId, int ClientId, string originalFileName, string generatedFileName, string generatedErrorFileName, string status);
        public abstract int CheckExistHUBOfferImportLarge(string originalFileName);
        public abstract HUBOfferImportLargeFileTempListDetails GetHUBOfferImportLargeFileTempList(int fileId, bool isShowMismatchOnly, int pageNumber, int pageSize);
        public abstract int UpdateHUBOfferImportLargeFileTempList(string jsonData, int updatedBy);
        public abstract int SaveHUBOfferImportLargeTemp(DataTable dt);
        public abstract int GenerateGTVendorMFRHUBOfferImportLargeFileTemp(int id);
        public abstract int UpdateHUBOfferImportLargeFileStatus(int id, string status, int updatedBy);
        public abstract int CountInvalidHUBOfferImportLargeFileTemp(int id);
        public abstract List<HUBOfferImportLargeFileTempDetails> GetGTVendorMFRHUBOfferImportLargeFileTemp(string ids);

        public abstract int saveBOMManagerImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out System.String errorMessage, string BomName, string CompanyName, string ContactName, int SalesmanId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, out System.String NewBomCode, out System.Int32 NewBomid, System.Int32? ReqforTraceabilityId, System.Int32? TypeId, System.DateTime DateRequired, System.Int32? LastMappingId);
        public abstract DataTable GetBOMDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);
        public abstract void SaveBOMExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);
        //Manager
        public abstract DataTable GetBOMManagerDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);
        public abstract void SaveBOMExcelHeaderManager(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);
        public abstract void saveBOMExcelBulkSaveManager(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency);
        //Manager end

        public abstract void saveBOMExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency);

        public abstract DataTable GetBOMExcelHeaderFrom(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        public abstract DataTable GetBOMManagerExcelHeaderFrom(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        public abstract DataTable GetBOMManagerExcelHeaderFromNew(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        public abstract DataTable GetBOMExcelHeader(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        public abstract DataTable GetBOMManagerExcelHeader(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        public abstract int DeleteBOMRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con);
        public abstract int DeleteBOMManagerRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con);

        public abstract DataTable GetContactWithCurrency(int companyId);
        public abstract DataTable GetMasterDataApi(int companyId);

        /// <summary>
        /// GetDivisionKpi
        /// Calls [[usp_dropdown_DivisionKpi_for_Client]]
        /// </summary>
        public abstract DataTable GetDivisionKpi(System.Int32? clientId, System.Int32? userId);
        /// <summary>
        /// GetTeamKpi
        /// Calls [[usp_dropdown_TeamKpi_for_Client]]
        /// </summary>
        public abstract DataTable GetTeamKpi(System.Int32? clientId, System.Int32? userId);


        #region Editable KPI Grid Soorya

        public abstract DataTable GetCurrencySymbol(System.Int32? clientId, System.String CurrencyCode);
        public abstract DataTable GetDivisionTeamNameByLoginid(System.Int32? clientId, System.Int32? userId, string selectKPIName);
        public abstract DataTable GetDDLDivisionKpi(System.Int32? clientId, System.Int32? userId);

        public abstract DataTable GetDDLTeamKpi(System.Int32? clientId, System.Int32? userId, System.Int32? DivisionTargetNo);

        public abstract DataTable GetDDLSalesKpi(System.Int32? clientId, System.Int32? userId, System.Int32? Teamtargetno);

        public abstract DataTable GetTeamKpiDivision(int DivisionID);
        public abstract DataTable GetTeamKpiSalesman(int TeamTargetID);

        #endregion

        //1 start
        public abstract void SaveStockExcelHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);
        public abstract void saveStockExcelBulkSave(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con, string DefaultCurrency);
        public abstract DataTable GetStockExcelHeaderFrom(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        public abstract DataTable GetStockExcelHeader(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        public abstract int DeleteStockRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con);
        public abstract DataTable GetStockGenrateTempDataFromDB(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string CompanyNameText, int clientType_con, string Column_Lable, string Column_Name, string ContactText, bool OverRideCurrency, string DefaultCurrencyName, string CurrencyColumnName, int DefaultCurrencyId, string SupplierNameText);
        public abstract int saveStockImportDataFromDB(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, string ddlCurrency, out System.String errorMessage, string BomName, string CompanyName, string ContactName, int SalesmanId, int CompanyId, int ContactId, bool PartWatch, int DefaultCurrencyId, bool OverRideCurrency, string SaveImportOrHubRFQ, System.Int32? LotId, System.Int32? DivisionId, System.String MSLLevel, System.Int32? SupplierId);
        public abstract DataTable GetStocksDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);
        public abstract DataTable GetSupplierSearch(System.Int32? mfrid, System.Int32? proCatgdid, System.Int32? suppid, System.String industryType, System.String supplierType, System.Int32? lastOrderDate, System.Int32? prodId, System.Int32? GlobalProductNameId, System.Int32? MfrGroupId, System.Int32? clientId);
        public abstract DataTable GetSupplierType(System.Int32? ClientNo, System.Int32? MfrId);
        public abstract DataTable ProductCategory();
        public abstract DataTable GetProductByCategory(System.Int32? ClientNo, System.Int32? ProdId);
        public abstract DataTable GetGlobalProductNameByCategory(System.Int32? ProductCategoryId);


        //1 end

        /// <summary>
        /// SaveSupplierColumnMapping
        /// Calls [usp_insert_BomImportColumnMapping]
        /// </summary>
        public abstract int SaveCompanyColumnMapping(System.Int32 CompanyId, string insertMapColumnList, int clientType_con);
        /// <summary>
        /// SaveBOMManagerExcelColumnHeaderDetails
        /// Calls [usp_SaveBOMManagerExcelColumnHeaderDetails]
        /// </summary>
        public abstract int SaveBOMManagerExcelColumnHeaderDetails(string insertMapColumnList, string MappingHeaderColumns, int? clientId, int CompanyId, int? LoggedId, Boolean mappingType);


        public abstract int SaveBOMManagerAPI(string insertMapColumnList, string MappingHeaderColumns, int? clientId, int CompanyId, int? LoggedId, Boolean mappingType);

        /// <summary>
        /// SaveSupplierColumnMapping
        /// Calls [usp_insert_BomManagerImportColumnMapping]
        /// </summary>
        public abstract int SaveCompanyColumnMappingBOMManager(System.Int32 CompanyId, string insertMapColumnList, int clientType_con);

        /// <summary>
        /// DeleteTempMapping
        /// Calls [usp_deleteTempStockRecord]
        /// </summary>
        public abstract int DeleteCompanyTempMapping(System.Int32 CompanyId, int clientType_con);
        public abstract int DeleteCompanyTempMappingBOMManager(System.Int32 CompanyId, int clientType_con);

        /// <summary>
        /// GetSupplierMappedColumn
        /// Calls [usp_ImportSupplierColumnMapping]
        /// </summary>
        public abstract DataTable GetCompanyMappedColumn(System.Int32 CompanyId, int clientType_con);
        public abstract DataTable GetCompanyMappedColumnBOMManager(System.Int32 CompanyId, int clientType_con);

        public abstract DataTable GetClientDivisionName(System.Int32? ClientID, System.Int32? clientType_con);
        public abstract DataTable GetClientWarehouse(System.Int32? ClientID, System.Int32? clientType_con);
        public abstract DataTable GetClientLot(System.Int32? ClientID, System.Int32? clientType_con);

        public abstract DataTable GetCompanyType(System.Int32? SelectedCompanyId);

        public abstract int Delete_EPORecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType);

        /// <summary>
        /// GetStockImportactivity
        /// Calls [Not Used]
        /// </summary>
        public abstract DataTable GetLogImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid);
        #endregion


        //code start for Revers Logistic
        /// <summary>
        /// SaveExcelHeader
        /// Calls [usp_SaveStockExcelColumn]
        /// </summary>
        public abstract void SaveExcelReverseLogisticHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);

        /// <summary>
        /// Insert Excel Epo Bulk
        /// Calls [SQL BULK COPY INSERT]
        /// </summary>
        public abstract void saveExcelReverseLogisticBulkSave(DataTable tempEpo, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con);

        /// <summary>
        /// Select Epo Header Column 
        /// Calls [usp_GetEpoExcelHeaderColumn]
        /// </summary>
        public abstract DataTable GetReverseLogisticExcelHeader(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);

        /// <summary>
        /// Select Epo Data from temp table
        /// Calls [usp_GetEpobindTempData]
        /// </summary>
        public abstract DataTable GetReverseLogisticDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);

        /// <summary>
        /// Delete Epo Record
        /// Calls [usp_deleteTempEpoRecord]
        /// </summary>
        public abstract int DeleteReverseLogisticRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con);
        /// <summary>
        /// Get Genrate Epo Data
        /// Calls [usp_GenrateDynamicEpoData]
        /// </summary>
        public abstract DataTable GetReverseLogisticGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string SupplierNameText, int clientType_con, System.Double? UpliftpValue, int NoChangesToSourcingSheet);


        /// <summary>
        /// Insert Epo Data
        /// Calls [usp_Select_ImportEpoData]
        /// </summary>
        public abstract int saveReverseLogisticImportData(int userId, int ClientId, int SelectedclientId, int SupplierId, int recordType, int clientType_con, System.Double? UpliftPercentage, int NoChangesToSourcingSheet, out System.String errorMessage);

        /// <summary>
        /// Insert Epo Import Activity Data
        /// Calls [usp_Select_EpoImportEpoData]
        /// </summary>
        public abstract int SaveReverseLogisticImportActivity(string insertDataList, int clientType_con);

        /// <summary>
        /// Get Epo Import Activity Data
        /// Calls [usp_select_Epoimportactivity]
        /// </summary>
        public abstract DataTable GetReverseLogisticImportactivity(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid, Boolean InactiveId);

        public abstract int Delete_ReverseLogisticRecord(string OfferId, System.Int32 BomId, System.Int32 userId, int ClientId, string OfferType);
        /// <summary>
        /// GetExportToExcelError
        /// Calls [[usp_ExcelUpload_Error_ReverseLogistics]]
        /// </summary>
        public abstract DataTable GetExportToExcelError(int userid, int ClientId, int SelectedclientId);
        //code end for Rverse Logistick
        public abstract DataTable InactivateReverseLogistics(int userid, int ClientId, int SelectedImportId);
        public abstract DataTable GetExportToExcelError_BomManager(int userid, int ClientId, int SelectedclientId, string mappingcolumlist, string fileHeaderCheck);
        public abstract DataTable GetExportToExcelError_PriceQuote(int userid, int clientId, string targetColumns, string selectedColumns); 

        public abstract DataTable GetPriceQuoteGenrateTempData(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name);

        public abstract DataTable GetPriceQuoteDetailFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);

        public abstract DataTable GetExcelHeader_PriceQuote(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);

        public abstract void SaveExcelHeader_PriceQuote(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);

        /// <summary>
        /// GetExcelHeaderFrom
        /// Calls [usp_GetExcelStockHeaderColumnFrom_PriceQuote]
        /// </summary>
        public abstract DataTable GetExcelHeaderFrom_PriceQuote(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        /// <summary>
        /// [usp_saveExcelBulkSave_PriceQuote]
        /// Calls [SQL BULK COPY INSERT]
        /// </summary>
        public abstract void saveExcelBulkSave_PriceQuote(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con);
        public abstract int InsertUpdatePriceQuoteImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage, out System.Int32 PriceQuoteNo);
        public abstract int DeleteRecord_PriceQuote(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con);
        /// GetSupplierMappedColumn_PriceQuote
        /// Calls [usp_ImportSupplierColumnMapping_PriceQuote]
        /// </summary>
        public abstract DataTable GetSupplierMappedColumn_PriceQuote(int userId);
        public abstract int SaveSupplierColumnMapping_PriceQuote(int userId, string insertMapColumnList);
        public abstract DataTable GetStockImportactivity_PriceQuote(int displayLength, int displayStart, int sortCol, string sortDir, string search, int ClientId, int clientType_con, int SelectedClientId, int userLoginid);

        /// <summary>
		
        //[005] code start
        //for Altranative Tool  code start
        /// <summary>
        /// Calls [usp_Insert_AltranativeHeaderExcelColumn]
        /// </summary>
        public abstract void SaveAltranativeHeader(string columnList, string insertColumnList, System.Int32? clientId, int? SelectedclientId, int? loginID, int clientType_con);
        /// <summary>
        /// Calls [usp_Insert_AltranativeExcelRows]
        /// </summary>
        public abstract void InsertAltranativeExcelRows(DataTable tempStock, DataTable dtData, string originalFilename, string generatedFilename, System.Int32 userId, System.Int32 clientId, int? SelectedclientId, int clientType_con);
        /// <summary>
        /// Calls [usp_Select_All_AltranativeRowsFromTemp]
        /// </summary>
        public abstract DataTable Get_All_AltranativeRowsFromTemp(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);
        /// <summary>
        /// Calls [usp_Select_ALL_AltranativeHeaderColumnTemp]
        /// </summary>
        public abstract DataTable Get_ALL_AltranativeHeaderColumnTemp(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        /// <summary>
        /// GetExcelHeaderFrom
        /// Calls [usp_Select_ALL_AltHeaderColumnDropdownBind]
        /// </summary>
        public abstract DataTable Get_Select_ALL_AltHeaderColumnDropdownBind(System.Int32 clientBomId, System.Int32 userId, System.Int32 SelectedclientId, int clientType_con);
        /// <summary>
        /// DeleteRecord
        /// <summary>
        /// Calls [usp_Delete_AltranativeTempRecord]
        /// </summary>
        public abstract int DeleteAltranativeTempRecord(System.Int32 SelectedclientId, System.Int32 userId, int ClientId, int clientType_con);
        /// <summary>
        /// DeleteTempMapping
        /// Calls [usp_Delete_AltranativeTempMapping]
        /// </summary>
        public abstract int DeleteAltranativeTempMapping(System.Int32 SupplierId, int clientType_con);
        /// <summary>
        /// SaveSupplierColumnMapping
        /// Calls [usp_Insert_AltranativeSupplierColumnMapping]
        /// </summary>
        public abstract int InsertAltranativeSupplierColumnMapping(System.Int32 SupplierId, string insertMapColumnList, int clientType_con);
        /// <summary>
        /// GetSupplierMappedColumn
        /// Calls [usp_Select_AltranativeSupplierMappedColumn]
        /// </summary>
        public abstract DataTable GetAltranativeSupplierMappedColumn(System.Int32 SupplierId, int clientType_con);

        /// <summary>
        /// Calls [usp_Select_GenerateAlternativeGridBind]
        /// </summary>
        public abstract DataTable GenerateAlternativeGridBind(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, string ColumnList, string ddlCurrency, string SupplierNameText, int clientType_con, string Column_Lable, string Column_Name);

        public abstract DataTable GetExportToExcelError_StockImport(int userid, int ClientId, int SelectedclientId, string ColumnList, string Column_Lable, string Column_Name);
        /// <summary>
        /// GetExportToExcelError
        /// Calls [[usp_ExcelUpload_Error_StrategicOffer]]
        /// </summary>
        public abstract DataTable GetExportToExcelError_StrategicOffer(int userid, int ClientId, int SelectedclientId);

        /// <summary>
        /// GetExportToExcelError
        /// Calls [usp_ExcelUpload_Error_BomImport]
        /// </summary>
        public abstract DataTable GetExportToExcelError_BomImport(int userid, int ClientId, int SelectedclientId, string ColumnList, string Column_Lable, string Column_Name);

        /// <summary>
        /// Calls [usp_Insert_Alternative_ImportData]
        /// </summary>
        public abstract int InsertAlternativeImportData(int userId, int ClientId, int SelectedclientId, string Column_Lable, string Column_Name, string insertDataList, string fileColName, int SupplierId, string ddlCurrency, int recordType, int clientType_con, out System.String errorMessage);
        /// <summary>
        /// Calls [usp_Select_GenerateDataAlternativeForCount]
        /// </summary>
        public abstract DataTable GenerateDataAlternativeForCount(int displayLength, int displayStart, int sortCol, string sortDir, string search, int userid, int ClientId, int SelectedclientId, int clientType_con);
        //for Altranative Tool  code end
        //[005] code start
        public abstract DataTable GetPriceQuoteDataTobeImported(int displayLength, int userId);
        public abstract int ImportPriceQuote(int userId, out int priceQuoteId, out int priceQuoteNumber, out string message);
        public abstract void SaveBOMImportSourcingData(DataTable dtData, string originalFilename, string generatedFilename, int hubrfqId, int loginId);
        public abstract int ImportBOMSourcingResults(int loginId, out string outputMessage);
        public abstract DataTable GetHUBOfferImportLargeHistory(int displayLength, int displayStart, int sortCol, string sortDir);
        public abstract void UpdateHUBOfferImportLargeCount(int logindId, int importId, int total);
        public abstract void DeleteHUBOfferImportLarge(int logindId, int importId);
        public abstract int UpdateVendorsOfferImportByExcelTemp(int importFileId, string incorrectVendor, string newVendor, int clientNo, int updatedBy);
        public abstract int UpdateMFRsOfferImportByExcelTemp(int importFileId, string incorrectMFR, string newMFR, int clientNo, int updatedBy);
        public abstract bool DeleteBomTempData(int loginId, string generatedFileName, int clientId, int selectedClientId);
        public abstract HubSourcingResultImportTempDetails GetHubSourcingTempData(int loginId, int pageNumber, int pageSize, bool showMismatchOnly);
        public abstract int DeleteHubSourcingTempData(int loginId);
        public abstract int CorrectHubSourcingTempData(string incorrectValue, string newValue, string type, int loginId);
        public abstract int BulkSaveHubSourcingTempData(string jsonData, int loginId);
        public abstract List<string> GetBOMPartsForIHS(int loginId, int clientId, int selectedClientId);
    }
}
