///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo = function(element) {
	Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.initializeBase(this, [element]);
	this._intQuoteID = null;
	this._intMessageID = null;
	this._ctlToDoID = null;
	this._quoteMinReminderDate = null;
	this._quoteStatus = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
		this._ctlToDo = this.getField("ctlToDo");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._ctlToDo = null;
		this._ctlToDoID = null;
		this._intQuoteID = null;
		this._intMessageID = null;
		this._quoteMinReminderDate = null;
		this._quoteStatus = null;

		Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.callBaseMethod(this, "dispose");
	},
	
	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addSave(Function.createDelegate(this, this.saveClicked));
			this._ctlToDo = $find(this.getField("ctlToDo").ID);
			this._ctlToDoID = this._ctlToDo._element.id;
			this._ctlToDo._ctlRelatedForm = this;
			this._ctlToDo.setupReminderClick();
		}
		this._ctlToDo._intQuoteID = this._intQuoteID;
        this._ctlToDo._intMessageID = this._intMessageID;
		this._ctlToDo._intCategoryID = 2;
		this._ctlToDo._quoteMinReminderDate = this._quoteMinReminderDate;
		this._ctlToDo._quoteStatus = this._quoteStatus;
		this.setFieldValue("ctlReminder", true);
		this._ctlToDo.selectReminder();
		this.getFieldDropDownData("ctlToDoListType");
	},
	
	saveClicked: function() {
		if (!this.validateForm()) return false;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/QuoteMainInfo");
		obj.set_DataObject("QuoteMainInfo");
		obj.set_DataAction("AddToDoTask");
		obj.addParameter("QuoteNo", this._intQuoteID);
        this._ctlToDo.addFieldsToDataObject(obj);
		obj.addDataOK(Function.createDelegate(this, this.saveOK));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	validateForm: function() {
		this.onValidate();
		var bln = this._ctlToDo.validateFields();
		if (!bln) this.showError(true);
		return bln;
	},
	
	saveError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveOK: function(args) {
		if (args._result.Result) {
			this.onSaveComplete();
		} else {
			this.saveError(args);
		}
	},
	getFormControlID: function (ParentId, controlID) {
		var str = "";
		str = String.format("#{0}_{1}", ParentId, controlID);
		return str;
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.QuoteMainInfo_MarkAsToDo", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
