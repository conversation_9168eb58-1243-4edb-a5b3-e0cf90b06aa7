<%@ Control Language="C#" CodeBehind="Services.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Services" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlName" runat="server" ResourceTitle="Name" FilterField="Name" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlDescription" runat="server" ResourceTitle="Description" FilterField="Description" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlLot" runat="server" ResourceTitle="Lot" DropDownType="Lot" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="LotNo" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
