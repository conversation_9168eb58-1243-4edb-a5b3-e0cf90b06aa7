﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
======================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-232569]		An.TranTan		 21-Mar-2025		Create		Bulk save inline edits
======================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_bulksave_HubSourcingTempData]
    @JsonData NVARCHAR(MAX),
	@UpdatedBy INT,
	@RecordCount INT OUTPUT
AS
BEGIN
	UPDATE temp
	SET temp.CreatedBy = @UpdatedBy
		,temp.DLUP = GETDATE()
		,temp.CustomerRequirementNumber = j.Requirement
		,temp.[SupplierPart] = j.SupplierPart
		,temp.[OfferedQuantity] = j.OfferedQuantity
		,temp.[SupplierCost] = j.SupplierCost
		,temp.[SPQ] = j.SPQ
		,temp.[MOQ] = j.MOQ
		,temp.[MSL] = j.MSL
		,temp.[Notes] = j.Notes
		,temp.[DateCode] = j.DateCode
		,temp.[QtyInStock] = j.QtyInStock
		,temp.[OfferStatus] = j.OfferStatus
		,temp.[BuyPrice] = j.BuyPrice
		,temp.[SellPrice] = j.SellPrice
		,temp.[ShippingCost] = j.ShippingCost
		,temp.[Package] = j.Package
		,temp.[ROHS] = j.ROHS
		,temp.[Currency] = j.Currency
		,temp.[FactorySealed] = j.FactorySealed
		,temp.[Region] = j.Region
		,temp.[LeadTime] = j.LeadTime
		,temp.[LastTimeBuy] = j.LastTimeBuy
		,temp.[DeliveryDate] = j.DeliveryDate
		,temp.[CustomerRefNo] = j.CustomerRefNo
	FROM BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp temp
	INNER JOIN OPENJSON(@JsonData)
        WITH (
            BomImportSourcingId INT,
			Requirement NVARCHAR(MAX),
			CustomerRefNo NVARCHAR(MAX),
			SupplierPart NVARCHAR(MAX),
			SupplierCost NVARCHAR(MAX),
			ROHS NVARCHAR(MAX),
			DateCode NVARCHAR(MAX),
			Package NVARCHAR(MAX),
			OfferedQuantity NVARCHAR(MAX),
			OfferStatus NVARCHAR(MAX),
			SPQ NVARCHAR(MAX),
			FactorySealed NVARCHAR(MAX),
			QtyInStock NVARCHAR(MAX),
			MOQ NVARCHAR(MAX),
			LastTimeBuy NVARCHAR(MAX),
			Currency NVARCHAR(MAX),
			BuyPrice NVARCHAR(MAX),
			SellPrice NVARCHAR(MAX),
			ShippingCost NVARCHAR(MAX),
			LeadTime NVARCHAR(MAX),
			Region NVARCHAR(MAX),
			DeliveryDate NVARCHAR(MAX),
			MSL NVARCHAR(MAX),
			Notes NVARCHAR(MAX)
        ) j
	ON temp.BomImportSourcingId = j.BomImportSourcingId

	SELECT @RecordCount = @@ROWCOUNT 
END
GO


