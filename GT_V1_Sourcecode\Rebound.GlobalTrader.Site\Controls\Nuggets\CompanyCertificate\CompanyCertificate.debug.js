///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate = function(element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.initializeBase(this, [element]);
    this._intCompanyID = -1;
    this._blnLineLoaded = false;
    this._inactive = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_ibtnAdd: function() { return this._ibtnAdd; }, set_ibtnAdd: function(v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(v) { if (this._ibtnEdit !== v) this._ibtnEdit = v; },
    get_tbl: function() { return this._tbl; }, set_tbl: function(v) { if (this._tbl !== v) this._tbl = v; },


    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.callBaseMethod(this, "initialize");

        //data
        this._strDataPath = "controls/Nuggets/CompanyCertificate";
        this._strDataObject = "CompanyCertificate";

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));

        //other controls
        this._tbl.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChanged));


        //add form
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[0]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }

        //edit form
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[1]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.hideEditForm));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }

        this.getCompanyInactive();
        this.getData();
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._tbl) this._tbl.dispose();
        this._frmAdd = null;
        this._frmEdit = null;
        this._intCompanyID = null;
        this._blnLineLoaded = null;
        this._inactive = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.callBaseMethod(this, "dispose");
    },

    enableEditButtons: function(bln) {
        if (bln) {
            if (this._ibtnAdd) $R_IBTN.enableButton(this._ibtnAdd, !this._inactive );
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive && this._blnLineLoaded);
        } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
        }
    },

    tbl_SelectedIndexChanged: function() {
        this._intLineID = this._tbl._varSelectedValue;
        if (this._intLineID)
            this._blnLineLoaded = true;
        this.enableEditButtons(true);
    },

    getData: function() {
        this._blnLineLoaded = false;
        $R_FN.showElement(this._pnlLineDetail, false);
        this.enableEditButtons(false);
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetCertificates");
        obj.addParameter("CompanyID", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function (args) {
        var chkdatestatus = false;
        imagepath = "app_themes/original/images/IconButton/pdficon.jpg";
        var cIPPDFDocClassTrue = this._inactive ? "style=\"pointer-events: none; opacity: 0.5;\"" : "";
        var cIPPDFDocClassFalse = this._inactive ? "style=\"text-decoration:none; pointer-events: none; opacity: 0.5;\"" : "style=\"text-decoration:none\"";
        var result = args._result;
        this.showLoading(false);
        this._tbl.clearTable();
        if (result.Lines) {
            for (var i = 0; i < result.Lines.length; i++) {
                var row = result.Lines[i];
                chkdatestatus = row.isCIPPDFAvailable;
                var aryData = [
					$R_FN.setCleanTextValue(row.CeriticateName)
					, $R_FN.setCleanTextValue(row.CategoryName)
					, $R_FN.setCleanTextValue(row.CertificateNum)
					, $R_FN.setCleanTextValue(row.StartDate)
                    , $R_FN.setCleanTextValue(row.ExpiryDate)
                    , $R_FN.setCleanTextValue((chkdatestatus == true)
                        ? String.format("&nbsp;&nbsp;<center><a href=\"javascript:void(0);\" onclick=\"$RGT_openCIPPDFDoc({0},{1})\"" + cIPPDFDocClassTrue + "title=\"Click to View and add docs\"><img border='0'" + cIPPDFDocClassTrue +" src=" + imagepath + " width='30' height='26'></center></a>", row.ID, this._intCompanyID)
                        : String.format("&nbsp;&nbsp;<a href=\"javascript:void(0);\" onclick=\"$RGT_openCIPPDFDoc({0},{1})\"" + cIPPDFDocClassFalse + " title=\"Click to add docs\"><center><b><img border='0'" + cIPPDFDocClassTrue + "src='app_themes/Original/images/buttons/sourcing/history_add.gif'></b></center></a>", row.ID, this._intCompanyID))
					];
                var objExtra = {
                    Inactive: row.Inactive,
                    CategoryID: row.CategoryNo,
                    Desc: $R_FN.setCleanTextValue(row.Desc),
                    CertificateID: row.CertificateID
                };
                var strCSS = (row.Inactive) ? "ceased" : "";
                this._tbl.addRow(aryData, row.ID, row.ID == this._intLineID, objExtra, strCSS);
                row = null; strCSS = null;
            }
        }
        this._intLineCount = this._tbl.countRows();
        this.showContent(true);
        this.showContentLoading(false);
        this._tbl.resizeColumns();
        if (this._intLineID > 0) this.tbl_SelectedIndexChanged();
        this.enableEditButtons(true);
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getCompanyInactive: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetCompanyDetailInactive");
        obj.addParameter("id", this._intCompanyID);
        obj.addDataOK(Function.createDelegate(this, this.getCompanyInactiveOK));
        obj.addError(Function.createDelegate(this, this.getCompanyInactiveError));
        obj.addTimeout(Function.createDelegate(this, this.getCompanyInactiveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getCompanyInactiveOK: function (args) {
        var result = args._result;
        this._inactive = result.Inactive;
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnAdd, !this._inactive);
        if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, !this._inactive);
    },

    getCompanyInactiveError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    showEditForm: function() {
       // alert(this._tbl.getSelectedExtraData().CategoryID);
        this._frmEdit.getFieldDropDownData("ctlCategory");
        this._frmEdit.getFieldComponent("ctlCertificate")._intCategoryID = this._tbl.getSelectedExtraData().CategoryID;
        this._frmEdit.getFieldDropDownData("ctlCertificate");

        this._frmEdit._intCompanyID = this._intCompanyID;
        this._frmEdit._intLineID = this._intLineID;

        this._frmEdit.setFieldValue("ctlCategory", this._tbl.getSelectedExtraData().CategoryID);
        this._frmEdit.setFieldValue("ctlCertificate", this._tbl.getSelectedExtraData().CertificateID);
        this._frmEdit.setFieldValue("ctlCertificateNumbre", this._tbl.getSelectedCellValue(2));
        this._frmEdit.setFieldValue("ctlStartDate", this._tbl.getSelectedCellValue(3));
        this._frmEdit.setFieldValue("ctlExpiryDate", this._tbl.getSelectedCellValue(4));
        this._frmEdit.setFieldValue("ctlInActive", this._tbl.getSelectedExtraData().Inactive);
        this._frmEdit.setFieldValue("ctlDescription", this._tbl.getSelectedExtraData().Desc);
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function() {
        this.showForm(this._frmEdit, false);
    },

    saveEditComplete: function() {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },

    showAddForm: function() {
        this._frmAdd._intCompanyID = this._intCompanyID;
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function() {
        this.showForm(this._frmAdd, false);
    },

    saveAddComplete: function() {
        this.hideAddForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this._intLineID = this._frmAdd._intNewID;
        this.getData();
    }


};

Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyCertificate", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
