﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--============================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-229094]     An.TranTan		 21-Jan-2025		UPDATE		Trigger alter vwToDo for new columns added
[US-225842]     An.TranTan		 03-Apr-2025		UPDATE		Trigger alter vwToDo for column SalesOrderNo added
==============================================================================================================================  
*/
CREATE OR ALTER   VIEW [dbo].[vwToDo]          
AS          
SELECT     dbo.tbLogin.EmployeeName AS LoginName, dbo.tbToDo.* , co.CompanyId,      
case when ISNULL(co.SOApproved,0)=1 and ISNULL(co.POApproved,0)=1 then CompanyName +' (SO : ' + c2.CurrencyCode +' , PO : '+ c.CurrencyCode +')'        
  when ISNULL(co.SOApproved,0)=1 THEN CompanyName +' (SO : ' + c2.CurrencyCode+')' WHEN         
ISNULL(co.POApproved,0)=1 THEN CompanyName +' (PO : ' + c.CurrencyCode+')' ELSE CompanyName +' (No CUR)' END as CompanyName      
FROM         dbo.tbToDo LEFT OUTER JOIN          
                      dbo.tbLogin ON dbo.tbToDo.LoginNo = dbo.tbLogin.LoginId       
       LEFT OUTER JOIN dbo.tbCompany co  on co.CompanyId=dbo.tbToDo.CompanyNo      
       left join tbCurrency c on co.POCurrencyNo = c.CurrencyId        
                      left join tbCurrency c2 on c2.CurrencyId=co.SOCurrencyNo     

GO
