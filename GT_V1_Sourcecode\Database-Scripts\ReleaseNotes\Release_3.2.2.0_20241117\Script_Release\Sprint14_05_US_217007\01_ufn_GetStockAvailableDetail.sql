﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213481]     Phuc Hoang		 06-Oct-2024		UPDATE		Show Stock Alert on HUBRFQ page as Requirement page
[US-217007]     An.TranTan		 05-Nov-2024		UPDATE		Exclude stock from inactive clients
=============================================================================================  
*/
CREATE OR ALTER FUNCTION [dbo].[ufn_GetStockAvailableDetail] (
	@PartNo NVARCHAR(50) = NULL
	,@ClientNo INT = 0
	,@stockid INT = 0
)
RETURNS NVARCHAR(400)
AS
BEGIN
	DECLARE @StockAvailableMessage NVARCHAR(400)
	DECLARE @QuantityInStock NVARCHAR(100)
	DECLARE @QuantityOnOrder NVARCHAR(100)
	DECLARE @QuantityAllocated NVARCHAR(100)
	DECLARE @QuantityAvailable NVARCHAR(100)
	DECLARE @stockNo NVARCHAR(100)

	SET @StockAvailableMessage = ''

	IF ISNULL(@stockid, 0) <> 0
	BEGIN
		IF EXISTS (
				SELECT TOP 1 1
				FROM vwStock
				JOIN tbClient on ClientId = ClientNo
				WHERE stockid = @stockid and tbClient.Inactive = 0
				)
		BEGIN
			SELECT TOP 1 @stockNo = stockid
				,@QuantityInStock = QuantityInStock
				,@QuantityOnOrder = QuantityOnOrder
				,@QuantityAllocated = QuantityAllocated
				,@QuantityAvailable = QuantityAvailable
			FROM vwStock
			JOIN tbClient on ClientId = ClientNo
			WHERE stockid = @stockid and tbClient.Inactive = 0
			ORDER BY vwStock.dlup DESC
		END
	END
	ELSE
	BEGIN
		IF EXISTS (
				SELECT TOP 1 1
				FROM vwStock
				JOIN tbClient on ClientId = ClientNo
				WHERE Part = @PartNo and tbClient.Inactive = 0
				)
		BEGIN
			SELECT TOP 1 @stockNo = stockid
			FROM vwStock
			JOIN tbClient on ClientId = ClientNo
			WHERE Part = @PartNo
				AND QuantityAvailable > 0
				AND ClientNo = @ClientNo
				AND tbClient.Inactive = 0
			ORDER BY vwStock.dlup DESC

			SELECT @QuantityInStock = SUM(QuantityInStock)
				,@QuantityOnOrder = SUM(QuantityOnOrder)
				,@QuantityAllocated = SUM(QuantityAllocated)
				,@QuantityAvailable = SUM(QuantityAvailable)
			FROM vwStock
			JOIN tbClient on ClientId = ClientNo
			WHERE Part = @PartNo AND tbClient.Inactive = 0
		END
	END

	--SET @StockAvailableMessage = @StockAvailableMessage +  @QuantityInStock +' In Stock -'+ @QuantityOnOrder +' On Order -'+ @QuantityAllocated +' Allocated -'+ @QuantityAvailable +' Available -' + @stockNo            
	IF (@QuantityAvailable > 0)
	BEGIN
		SET @StockAvailableMessage = @StockAvailableMessage + ' In Stock : ' + @QuantityInStock + ' -' + ' On Order : ' + @QuantityOnOrder + '-' + ' Allocated : ' + @QuantityAllocated + '-' + ' Available : ' + @QuantityAvailable + '-' + ISNULL(@stockNo, 0)
	END

	RETURN @StockAvailableMessage
END
GO


