using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class GILines_AddShortShipment : Base {

		#region Locals

		//protected Image imgCalculate;

		#endregion

		#region Properties

		private int _intGIID = -1;
		public int GIID {
			get { return _intGIID; }
			set { _intGIID = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "GILines_AddShortShipment");
			AddScriptReference("Controls.Nuggets.GILines.AddShortShipment.GILines_AddShortShipment.js");
			if (_objQSManager.GoodsInID > 0) _intGIID = _objQSManager.GoodsInID;
			//WireUpControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			//imgCalculate.ImageUrl = Functions.GetThemeImage("nuggets/lcc.gif", Page.Theme);
			base.OnPreRender(e);
		}

		#endregion
		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.GILines_AddShortShipment", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intGIID", _intGIID);	
		}

	}
}