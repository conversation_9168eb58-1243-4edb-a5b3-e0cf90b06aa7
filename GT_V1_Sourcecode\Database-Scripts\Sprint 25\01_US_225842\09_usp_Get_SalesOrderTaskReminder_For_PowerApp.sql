﻿/*
--===============================================================================================================================  
TASK            UPDATED BY         DATE             ACTION        DESCRIPTION  
[US-225842]		An.TranTan		   08-Apr-2025		CREATE		  Get task reminder type Sales Order to send notification in PowerApp
=================================================================================================================================  
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_Get_SalesOrderTaskReminder_For_PowerApp]
AS
BEGIN
	SET NOCOUNT ON;
	-----------Power App url---------
	Declare @PowerAppUrl NVARCHAR(MAX)='';
	SELECT TOP 1 @PowerAppUrl = FlowUrl
	FROM tbPowerApp_urls WITH(NOLOCK) WHERE FlowName = 'SalesOrder_TaskReminder_Notification'

	--------------------------------------------------
	SELECT
	    @PowerAppUrl AS PowerAppUrl
	    ,td.ToDoId
	    ,td.LoginNo
	    ,td.[Subject]
	    ,td.DateAdded
	    ,td.DueDate
	    ,td.ToDoText
	    ,td.IsComplete
	    ,td.ReminderDate
	    ,td.ReminderText
	    ,td.SalesOrderNo
		,so.SalesOrderNumber
	    ,td.ReminderHasBeenNotified
	    ,td.UpdatedBy
	    ,td.DLUP
	    ,ISNULL(td.HasReview, CAST(0 AS BIT)) AS TaskForReview
	    ,td.[Name]
	    ,td.TypeNo
	    ,lg.EmployeeName AS CreatorName
	    ,lg.EMail AS ToCreatorEmail
		,so.Salesman AS SalesmanNo
		,lgSale.EmployeeName AS SalesmanName
	    ,lgSale.EMail As SalesmanEmail
	  FROM  dbo.tbToDo td WITH(NOLOCK)
	  JOIN dbo.tbSalesOrder so WITH(NOLOCK) ON so.SalesOrderId = td.SalesOrderNo
	  JOIN dbo.tbLogin lg WITH(NOLOCK) ON td.UpdatedBy = lg.LoginId
	  JOIN dbo.tbLogin lgSale ON so.Salesman = lgSale.LoginId
	  WHERE td.ToDoCategoryNo = 4 --Only query Sales Order category
	  AND ISNULL(td.ReminderText, '') <> '' 
	  AND DATEPART(HOUR, td.ReminderDate) = DATEPART(HOUR, GETDATE())
	  AND DATEPART(MINUTE, td.ReminderDate) >= DATEPART(MINUTE, GETDATE()) 
	  AND DATEPART(MINUTE, td.ReminderDate) <= DATEPART(MINUTE, GETDATE()) + 14
END
