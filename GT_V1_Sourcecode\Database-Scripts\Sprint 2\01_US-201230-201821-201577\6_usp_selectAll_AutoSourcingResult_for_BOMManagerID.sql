SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF OBJECT_ID('usp_selectAll_AutoSourcingResult_for_BOMManagerID','P') IS NOT NULL
    DROP PROC [dbo].[usp_selectAll_AutoSourcingResult_for_BOMManagerID]
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-201230]		An.TranTan			23-04-2024		Update			Get addition information of sourcing results
[US-201821]		An.TranTan			07-May-2024		Update			Remove AS6081 related information
[US-201577]		An.TranTan			22-May-2024		Update			Update get package and product description instead of name
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_selectAll_AutoSourcingResult_for_BOMManagerID]                                                                                                                                   
	@BOMManagerID int                                              
	, @CustomerReqID int                                              
	, @IsPOHUB bit = 0                    
	, @curPage int = 1                    
	, @Rpp int = 5                    
AS                                                                                                                      
                                                                                                                    
DECLARE @BOMCurrencyNo INT                                                                                                                    
DECLARE @BOMCurrencyCode NVARCHAR(5)                                                                                                                                                                           
DECLARE @ClientNo INT
DECLARE @TotalRecords INT, @skip INT
DECLARE @SupplierName NVARCHAR(50) = N'Rebound Electronics International Services DMCC';
DECLARE @SupplierNo INT;


SET @ClientNo = (SELECT DISTINCT(Clientno) 
				FROM tbCustomerRequirement 
				WHERE CustomerRequirementId = @CustomerReqID 
					AND BOMManagerNo = @BOMManagerID)
SET @SupplierNo = (SELECT TOP 1 CompanyId
					FROM dbo.tbCompany
					WHERE IsPOHub = 1
						AND Inactive = 0
						AND ClientNo = @ClientNo)

SELECT @TotalRecords = count(sr.SourcingResultId) 
FROM tbSourcingResult sr                    
  LEFT JOIN tbBOMManager bm on bm.BOMManagerId = @BOMManagerID  
  JOIN dbo.tbautosource tbas on sr.autosourceid = tbas.sourceid           
WHERE sr.CustomerRequirementNo = @CustomerReqID          
     AND isnull(tbas.isdeleted,0)=0                  
     AND isnull(sr.IsSoftDelete,0)= 0                
     AND (bm.[Status] >= 4)  
	 
SET @skip = (@Rpp * (@curPage - 1))                         
IF (@skip >= @TotalRecords and @TotalRecords > 0)                    
BEGIN                    
	SET @curPage = CAST(@TotalRecords/@Rpp as int)                    
	SET @skip = CAST((@Rpp * (@curPage - 1)) as int)                     
END                    
                     
SELECT                     
   sr.CustomerRequirementNo as CustomerRequirementId                                      
  ,sr.SourcingResultId                                                                                                                                                 
  , sr.FullPart                                                                                                                                
  , sr.Part                                                                                                                                
  , sr.ManufacturerNo                                                                                                                                
  , sr.DateCode                                                                                                                                
  , sr.ProductNo                                                                                                                                
  , sr.PackageNo                                                                                                                                
  , cr.Quantity                                                                                                                                                        
  , sr.ActualPrice          
  , sr.ActualCurrencyNo          
  , (select CurrencyCode from tbCurrency where CurrencyId = sr.ActualCurrencyNo) as ActualCurrencyCode          
  , sr.Salesman                                                                                                                
  , sr.UpdatedBy                                                                                                
  , sr.DLUP                                                          
  , sr.ROHS                                                                                            
  , sr.Notes                                                           
  , mf.ManufacturerName                         
  , mf.ManufacturerCode                              
  , ISNULL(@SupplierNo, 0) AS SupplierNo
  , @SupplierName AS SupplierName                                                                                                                      
  , pr.ProductDescription AS ProductName                                                                                                                                
  , pk.PackageDescription AS PackageName                                                                                                                                
  , pk.PackageDescription                                                                                                                                     
  , sr.Price          
  , sr.CurrencyNo          
  , cu.CurrencyCode          
  , cr.POHubReleaseBy                                                                                                          
  , sr.ClientCompanyNo                                                                                                                                                                       
  , bm.ClientCurrencyNo                                                                                                                       
  , (select CurrencyCode from tbCurrency where CurrencyId = bm.ClientCurrencyNo) as ClientCurrencyCode                                                                                                                                                                   
  , sr.DateCode                                                                                                               
  , sr.SupplierMOQ                                                                                                                                                                                    
  , sr.SPQ                                                                                                                                                            
  , sr.MSL
  , sr.MSLLevelNo
  , ml.MSLLevel AS MSLLevelText
  , cr.ClientNo                                                                       
  ,(SELECT COUNT(1) FROM tbAutoSource where ISNULL(BOMStatus,0)=1 and BOMManagerNo = @BOMManagerID and isnull(IsDeleted,0)=-0 ) as SourcingReleasedCount                                                                                                       
  ,cr.CountryOfOriginNo as IHSCountryOfOriginNo                                                              
  ,cro.GlobalCountryName  as IHSCountryOfOriginName                                                                                                              
  ,rh.[Description] as ROHSDescription                                                  
  --,sr.PartWatchMatchHUBIPO                                               
  ,cr.DatePromised as DeliveryDate                
  ,(select EmployeeName from tbLogin where LoginId = cr.POHubReleaseBy) as SalesmanName                                          
  ,cr.DatePOHubRelease as OfferDate                                  
  ,cr.REQStatus                                
  ,ql.QuoteNo as QuoteID                                
  ,qt.QuoteNumber               
  --,(Select top 1 up.UpliftPercentage from tbUpliftPercentage_BOMManager up where up.CustomerRequirementNo = sr.CustomerRequirementNo and isnull(up.IsDeleted,0)=0) as UpliftPercentage                    
  ,sr.IsPrimarySourcing                    
  ,@TotalRecords as TotalRecords              
  ,rg.RegionName              
  ,tct.[Name] as SupplierType          
  --,  tbas.isdeleted,sr.IsSoftDelete        
  --,tbas.sourceid
  , ISNULL(sr.supplierWarranty,0) AS SupplierWarranty
  , CAST(ISNULL(sr.TestRecommended,0) AS BIT) AS IsTestingRecommended
  , coo.GlobalCountryName AS CountryOfOrigin
  , sr.SupplierTotalQSA
  , sr.SupplierLTB
  , sr.IsImageAvailable
  , sr.LeadTime
  , sr.FactorySealed
 FROM  dbo.tbSourcingResult sr                     
 JOIN dbo.tbautosource tbas on sr.autosourceid = tbas.sourceid                  
 LEFT JOIN dbo.tbManufacturer mf ON sr.ManufacturerNo = mf.ManufacturerID                                                                                                               
 LEFT JOIN dbo.tbCurrency cu ON sr.CurrencyNo = cu.CurrencyID                                                                                                                                                                                                                          
 LEFT JOIN dbo.tbProduct pr ON sr.ProductNo    = pr.ProductId                                                                                                                                
 LEFT JOIN dbo.tbPackage pk ON sr.PackageNo    = pk.PackageId                                                                                                                                                                                                                           
 LEFT JOIN dbo.tbCompany coc ON sr.SupplierNo = coc.CompanyId              
 LEFT JOIN tbcompanytype tct on tct.CompanyTypeId = coc.TypeNo              
 LEFT JOIN tbCompanyAddress ca on ca.CompanyNo = coc.CompanyId and ca.DefaultBilling = 1             
 LEFT JOIN tbAddress ad on ad.AddressId = ca.AddressNo              
 LEFT JOIN tbRegion rg on rg.RegionId = ad.RegionNo              
 LEFT JOIN  dbo.tbCustomerRequirement cr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                                                                                                                                                                                                                                                     
 LEFT JOIN  tbGlobalCountryList cro on cr.CountryOfOriginNo=cro.GlobalCountryId
 LEFT JOIN  tbGlobalCountryList coo on sr.IHSCountryOfOriginNo = coo.GlobalCountryId
 LEFT JOIN tbBOMManager bm on bm.BOMManagerId = @BOMManagerID                                
 LEFT JOIN tbQuoteLine ql on ql.SourcingResultNo = sr.SourcingResultId                                
 LEFT JOIN tbQuote qt on qt.QuoteId = ql.QuoteNo
 LEFT JOIN tbMSLLevel ml on ml.MSLLevelId = sr.MSLLevelNo
 LEFT JOIN tbROHSStatus rh on rh.ROHSStatusId = sr.ROHS
 WHERE sr.CustomerRequirementNo = @CustomerReqID          
	AND isnull(tbas.isdeleted,0)=0                  
	AND isnull(sr.IsSoftDelete,0)= 0                
	AND(bm.[Status] >= 4)                     
 ORDER BY cr.[sequence]         
  OFFSET @skip ROWS                                      
  FETCH NEXT @Rpp ROWS ONLY       
GO


