///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[001]      Vinay           12/06/2013         CR:- Supplier Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.prototype = {

    get_intSupplierInvoiceID: function() { return this._intSupplierInvoiceID; }, set_intSupplierInvoiceID: function(v) { if (this._intSupplierInvoiceID !== v) this._intSupplierInvoiceID = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlLines: function () { return this._ctlLines; }, set_ctlLines: function (v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_ctlSIPDFDragDrop: function () { return this._ctlSIPDFDragDrop }, set_ctlSIPDFDragDrop: function (a) { if (this._ctlSIPDFDragDrop !== a) { this._ctlSIPDFDragDrop = a } },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        if (this._ctlSIPDFDragDrop) { this._ctlSIPDFDragDrop.getData() }
        Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        this._ctlMainInfo = null;
        this._ctlLines = null;
        this._ctlSIPDFDragDrop = null;
        Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.callBaseMethod(this, "dispose");
    },


    ctlMainInfo_GetDataComplete: function() {
        if (this._ctlLines) {
            this._ctlLines._blnExported = this._ctlMainInfo.getFieldValue("ctlExported");
            this._ctlLines.getData();
        }
    }

};
Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.SupplierInvoiceDetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
