//---------------------------------------------------------------------------------------------------------
// RP 18.12.2009:
// - allow passing a company name to initially search for (task 357)
//
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//Marker     Changed by      Date         Remarks
//[001]      Vinay           30/10/2012   Add link in the Invoice section to create CRMA and Credit
//---------------------------------------------------------------------------------------------------------using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using System;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class CreditAdd_Add : Base {

		#region Locals

		protected RadioButtonList _radSelectSource;
		protected IconButton _ibtnContinue;
		protected IconButton _ibtnContinue_Footer;
		protected TableRow _trSelectFromInvoice;
		protected ItemSearch.Invoices _ctlSelectFromInvoice;
		protected TableRow _trSelectFromCRMA;
		protected ItemSearch.CRMAs _ctlSelectFromCRMA;
		protected List<string> _lstSources = new List<string>();

        protected TableRow _trSelectFromClientInvoice;
        protected ItemSearch.ClientInvoices _ctlSelectFromClientInvoice;

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "CreditAdd_Add");
			AddScriptReference("Controls.Nuggets.CreditAdd.Add.CreditAdd_Add.js");
			WireUpControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupSelectSourceScreen();
			WireUpButtons();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		#region Properties

		private bool _blnCanAddFromInvoice = true;
		public bool CanAddFromInvoice {
			get { return _blnCanAddFromInvoice; }
			set { _blnCanAddFromInvoice = value; }
		}

		private bool _blnCanAddFromCRMA = true;
		public bool CanAddFromCRMA {
			get { return _blnCanAddFromCRMA; }
			set { _blnCanAddFromCRMA = value; }
		}

        private bool _blnCanAddFromClientInvoice = true;
        public bool CanAddFromClientInvoice
        {
            get { return _blnCanAddFromClientInvoice; }
            set { _blnCanAddFromClientInvoice = value; }
        }

		#endregion

		/// <summary>
		/// Setup controls for Select Source screen
		/// </summary>
		private void SetupSelectSourceScreen() {
			if (_blnCanAddFromInvoice) AddRadioButton("FromInvoice", "INVOICE");
			if (_blnCanAddFromCRMA) AddRadioButton("FromCustomerRMA", "CRMA");
            if (SessionManager.IsPOHub == true)
            {
                if (_blnCanAddFromClientInvoice) AddRadioButton("FromClientInvoice", "CLIENTINVOICE");
            }
			_radSelectSource.SelectedIndex = 0;
		}

		private void AddRadioButton(string strResourceTitle, string strJavascriptType) {
			_radSelectSource.Items.Add(new ListItem(Functions.GetGlobalResource("Misc", strResourceTitle)));
			_lstSources.Add(strJavascriptType);
		}

		/// <summary>
		/// Wire up controls to the ascx
		/// </summary>
		private void WireUpControls() {
			_radSelectSource = (RadioButtonList)FindFieldControl("ctlSelectSource", "radSelectSource");
			_trSelectFromInvoice = (TableRow)FindContentControl("trSelectFromInvoice");
			_ctlSelectFromInvoice = (ItemSearch.Invoices)FindContentControl("ctlSelectFromInvoice");
			_trSelectFromCRMA = (TableRow)FindContentControl("trSelectFromCRMA");
			_ctlSelectFromCRMA = (ItemSearch.CRMAs)FindContentControl("ctlSelectFromCRMA");
            _trSelectFromClientInvoice = (TableRow)FindContentControl("trSelectFromClientInvoice");
            _ctlSelectFromClientInvoice = (ItemSearch.ClientInvoices)FindContentControl("ctlSelectFromClientInvoice");
		}

		private void WireUpButtons() {
			_ibtnContinue = (IconButton)FindIconButton("ibtnContinue");
			_ibtnContinue_Footer = (IconButton)FindFooterIconButton("ibtnContinue");
		}

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CreditAdd_Add", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnSend", FindIconButton("ibtnSend").ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", FindFooterIconButton("ibtnSend").ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnContinue", (FindIconButton("ibtnContinue").ClientID));
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", (FindFooterIconButton("ibtnContinue").ClientID));
			_scScriptControlDescriptor.AddElementProperty("radSelectSource", _radSelectSource.ClientID);
			_scScriptControlDescriptor.AddElementProperty("trSelectFromInvoice", _trSelectFromInvoice.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("ctlSelectFromInvoice", _ctlSelectFromInvoice.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("trSelectFromCRMA", _trSelectFromCRMA.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("ctlSelectFromCRMA", _ctlSelectFromCRMA.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("intLoginID", SessionManager.LoginID);
			_scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
			_scScriptControlDescriptor.AddProperty("strCompanyName", _objQSManager.CompanyName);
			_scScriptControlDescriptor.AddProperty("intContactID", _objQSManager.ContactID);
			_scScriptControlDescriptor.AddProperty("strContactName", _objQSManager.ContactName);
			_scScriptControlDescriptor.AddElementProperty("lblCurrency_Freight", FindFieldControl("ctlFreight", "lblCurrency_Freight").ClientID);
			_scScriptControlDescriptor.AddProperty("arySources", _lstSources);
			_scScriptControlDescriptor.AddProperty("strSearchCompanyName", _objQSManager.SearchCompanyName);
            //[001] code start
            _scScriptControlDescriptor.AddProperty("intQSInvoiceID", _objQSManager.InvoiceID);
            //[001] code end

            _scScriptControlDescriptor.AddElementProperty("trSelectFromClientInvoice", _trSelectFromClientInvoice.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlSelectFromClientInvoice", _ctlSelectFromClientInvoice.ctlDesignBase.ClientID);
		}

	}
}
