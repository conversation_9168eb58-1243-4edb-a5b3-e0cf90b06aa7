﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Accepted" xml:space="preserve">
    <value>Accepted</value>
  </data>
  <data name="Allocated" xml:space="preserve">
    <value>Allocated</value>
  </data>
  <data name="Amended" xml:space="preserve">
    <value>Amended</value>
  </data>
  <data name="Authorised" xml:space="preserve">
    <value>Checked</value>
  </data>
  <data name="AuthorisedPartAllocated" xml:space="preserve">
    <value>Checked (Part Allocated)</value>
  </data>
  <data name="Available" xml:space="preserve">
    <value>Available</value>
  </data>
  <data name="Available_PartAllocated" xml:space="preserve">
    <value>Available (Part Allocated)</value>
  </data>
  <data name="Available_Unallocated" xml:space="preserve">
    <value>Available (Unallocated)</value>
  </data>
  <data name="AwaitsInspection" xml:space="preserve">
    <value>Awaits Inspection</value>
  </data>
  <data name="Checked" xml:space="preserve">
    <value>Checked</value>
  </data>
  <data name="Closed" xml:space="preserve">
    <value>Closed</value>
  </data>
  <data name="CompanyOnStop" xml:space="preserve">
    <value>Company On Stop</value>
  </data>
  <data name="CompanyOverCreditLimit" xml:space="preserve">
    <value>Company over credit limit</value>
  </data>
  <data name="Complete" xml:space="preserve">
    <value>Complete</value>
  </data>
  <data name="Confirmed" xml:space="preserve">
    <value>Confirmed</value>
  </data>
  <data name="Declined" xml:space="preserve">
    <value>Declined</value>
  </data>
  <data name="Despatched" xml:space="preserve">
    <value>Despatched</value>
  </data>
  <data name="FullyAllocated" xml:space="preserve">
    <value>Fully Allocated</value>
  </data>
  <data name="Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="Inspected" xml:space="preserve">
    <value>Released</value>
  </data>
  <data name="InTransit" xml:space="preserve">
    <value>In Transit</value>
  </data>
  <data name="InvoicePaid" xml:space="preserve">
    <value>Invoice Paid</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>New</value>
  </data>
  <data name="NoneInStock" xml:space="preserve">
    <value>None in stock</value>
  </data>
  <data name="NotInspected" xml:space="preserve">
    <value>Goods in not released</value>
  </data>
  <data name="Offered" xml:space="preserve">
    <value>Offered</value>
  </data>
  <data name="OnOrder" xml:space="preserve">
    <value>On Order</value>
  </data>
  <data name="Open" xml:space="preserve">
    <value>Open</value>
  </data>
  <data name="Ordered" xml:space="preserve">
    <value>Ordered</value>
  </data>
  <data name="PartAllocated" xml:space="preserve">
    <value>Part Allocated</value>
  </data>
  <data name="PartInspected" xml:space="preserve">
    <value>Part Released</value>
  </data>
  <data name="PartPosted" xml:space="preserve">
    <value>Part Posted</value>
  </data>
  <data name="PartReceived" xml:space="preserve">
    <value>Part Received</value>
  </data>
  <data name="PartShipped" xml:space="preserve">
    <value>Part Shipped</value>
  </data>
  <data name="Placed" xml:space="preserve">
    <value>Placed</value>
  </data>
  <data name="Posted" xml:space="preserve">
    <value>Posted</value>
  </data>
  <data name="Quarantined" xml:space="preserve">
    <value>Quarantined</value>
  </data>
  <data name="ReadyToShip" xml:space="preserve">
    <value>Ready to ship</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Received</value>
  </data>
  <data name="SalesOrderClosed" xml:space="preserve">
    <value>SO closed</value>
  </data>
  <data name="Shipped" xml:space="preserve">
    <value>Shipped</value>
  </data>
  <data name="ShortShipped" xml:space="preserve">
    <value>Short Shipped</value>
  </data>
  <data name="StockInQuarantine" xml:space="preserve">
    <value>Stock in Quarantine</value>
  </data>
  <data name="TermsInAdvanceNotOK" xml:space="preserve">
    <value>Unpaid on advance terms</value>
  </data>
  <data name="Unallocated" xml:space="preserve">
    <value>Unallocated</value>
  </data>
  <data name="Unauthorised" xml:space="preserve">
    <value>Unchecked</value>
  </data>
  <data name="Unposted" xml:space="preserve">
    <value>Unposted</value>
  </data>
  <data name="OnHoldStatus" xml:space="preserve">
    <value>[Stop Status:M]</value>
  </data>
  <data name="AwaitingPOApproval" xml:space="preserve">
    <value>Awaiting PO Approval</value>
  </data>
  <data name="AwaitingSOCheck" xml:space="preserve">
    <value>Awaiting SO Check</value>
  </data>
  <data name="POApproved" xml:space="preserve">
    <value>PO Approved</value>
  </data>
  <data name="NotPosted" xml:space="preserve">
    <value>Un Posted</value>
  </data>
  <data name="HoldRemainBalance" xml:space="preserve">
    <value>Hold for the remaining balance</value>
  </data>
  <data name="ShipPartial" xml:space="preserve">
    <value>Ship Partial</value>
  </data>
  <data name="HighRiskCountryStatus" xml:space="preserve">
    <value>Selected country having potential high failure rate. </value>
  </data>
  <data name="GIAwaitingQuery" xml:space="preserve">
    <value>GI Awaiting Query</value>
  </data>
  <data name="GIInspected" xml:space="preserve">
    <value>GI Inspected</value>
  </data>
  <data name="GIPartiallyInspected" xml:space="preserve">
    <value>GI Partially Inspected</value>
  </data>
  <data name="GIPartiallyQuarantined" xml:space="preserve">
    <value>GI Partially Quarantined</value>
  </data>
  <data name="GIPartiallyReleased" xml:space="preserve">
    <value>GI Partially Released</value>
  </data>
  <data name="GIQuarantined" xml:space="preserve">
    <value>GI Quarantined</value>
  </data>
  <data name="GIReceived" xml:space="preserve">
    <value>GI Received</value>
  </data>
  <data name="GIReleased" xml:space="preserve">
    <value>GI Released</value>
  </data>
</root>