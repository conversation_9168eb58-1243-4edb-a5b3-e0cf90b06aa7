Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.prototype={get_intSalesPersonID:function(){return this._intSalesPersonID},set_intSalesPersonID:function(n){this._intSalesPersonID!==n&&(this._intSalesPersonID=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_ibtnViewTask:function(){return this._ibtnViewTask},set_ibtnViewTask:function(n){this._ibtnViewTask!==n&&(this._ibtnViewTask=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/SalesOrders";this._strDataObject="SalesOrders";Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.callBaseMethod(this,"initialize");this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.exportCSV));this._ibtnViewTask&&$R_IBTN.addClick(this._ibtnViewTask,Function.createDelegate(this,this.viewTask));this._frmAdd=$find(this._aryFormIDs[0]);this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm));this._frmAdd.addSaveComplete(Function.createDelegate(this,this.addComplete))},initAfterBaseIsReady:function(){this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.applySalesPersonFilter();this.updateFilterVisibility();this.getData()},dispose:function(){this.isDisposed||(this._intSalesPersonID=null,this._IsGlobalLogin=null,this._IsGSA=null,this._frmAdd&&this._frmAdd.dispose(),this._frmAdd=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){var t="",i,r,n;for(this._sortIndex=this._objResult.SortIndex,this._sortDir=this._objResult.SortDir,this._pageIndex=this._objResult.PageIndex,this._pageSize=this._objResult.PageSize,i=0,r=this._objResult.Results.length;i<r;i++){t="";n=this._objResult.Results[i];t=n.DatePromisedStatus=="Green"?"green":n.DatePromisedStatus=="Amber"?"#FFBF00":n.DatePromisedStatus=="Red"?"Red":n.DatePromisedStatus=="White"?"White":"White";var f=String.format("<a href=\"javascript:void(0);\" title='Add task' onclick=\"$find('{0}').showAddForm({1},'{2}','{3}');\">Add Task<\/a>",this._element.id,n.ID,n.No,$R_FN.setCleanTextValue(n.CM))+"&nbsp;&nbsp;&nbsp;",e=String.format("<a href=\"javascript:void(0);\" title='View task' onclick=\"$find('{0}').redirectToDetails('{1}');\" style=\"color: {2};\">"+(n.TaskCount+" Task")+"<\/a>",this._element.id,n.No,n.HasUnFinishedTask?"red":""),u=[$RGT_nubButton_SalesOrder(n.ID,n.No),$R_FN.writeDoubleCellValue($R_FN.writePartNo(n.Part,n.ROHS),$R_FN.setCleanTextValue(n.CustPONO)),$R_FN.writeTriCellValue(n.Quantity,n.QuantityShipped,n.QuantityInStock),$R_FN.writeDoubleCellValue(n.blnMakeYellow==!0?'<span style="background-color:yellow;">'+$RGT_nubButton_Company(n.CMNo,n.CM)+"<\/span>":$RGT_nubButton_Company(n.CMNo,n.CM),$RGT_nubButton_Contact(n.ContactNo,n.Contact)),$R_FN.setCleanTextValue(n.DateOrdered),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.DatePromised),t=="White"?"<span style='background-color:"+t+"!important;float: right;margin-top: -17px;height: 20px;width: 20px;visibility: hidden;'><\/span>":"<span style='background-color:"+t+"!important;float: right;margin-top: -17px;height: 20px;width: 20px;'><\/span>"),$R_FN.setCleanTextValue(n.Status),$R_FN.setCleanTextValue(n.ContractNo),f+e];this._table.addRow(u,n.ID,!1);u=null;n=null}},updateFilterVisibility:function(){this.getFilterField("ctlSalesman").show(this._enmViewLevel!=0);this.getFilterField("ctlClientName").show(this._IsGlobalLogin||this._IsGSA)},applySalesPersonFilter:function(){this._intSalesPersonID&&this._intSalesPersonID>0&&this.getFilterField("ctlSalesman").setValue(this._intSalesPersonID)},exportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("ExportToCSV");n._intTimeoutMilliseconds=5e5;n.addParameter("ViewLevel",this._enmViewLevel);n.addParameter("SortIndex",this._sortIndex);n.addParameter("SortDir",this._sortDir);n.addParameter("PageIndex",this._pageIndex);n.addParameter("PageSize",this._pageSize);n.addParameter("SONoLo",this.getFilterFieldValue_Min("ctlSONo"));n.addParameter("SONoHi",this.getFilterFieldValue_Max("ctlSONo"));n.addParameter("Part",this.getFilterFieldValue("ctlPart"));n.addParameter("RecentOnly",this.getFilterFieldValue("ctlRecentOnly"));n.addParameter("IncludeClosed",this.getFilterFieldValue("ctlIncludeClosed"));n.addParameter("CMName",this.getFilterFieldValue("ctlCompanyName"));n.addParameter("Contact",this.getFilterFieldValue("ctlContactName"));n.addParameter("Country",this.getFilterFieldValue("ctlCountry"));n.addParameter("SOCheckedStatus",this.getFilterFieldValue("ctlCheckedStatus"));n.addParameter("SalesOrderStatus",this.getFilterFieldValue("ctlStatus"));n.addParameter("Salesman",this.getFilterFieldValue("ctlSalesman"));n.addParameter("CustPO",this.getFilterFieldValue("ctlCustomerPO"));n.addParameter("DateOrderedFrom",this.getFilterFieldValue("ctlDateOrderedFrom"));n.addParameter("DateOrderedTo",this.getFilterFieldValue("ctlDateOrderedTo"));n.addParameter("DatePromisedFrom",this.getFilterFieldValue("ctlDatePromisedFrom"));n.addParameter("DatePromisedTo",this.getFilterFieldValue("ctlDatePromisedTo"));n.addParameter("ContractNo",this.getFilterFieldValue("ctlContractNo"));n.addParameter("IncludeOrderSent",this.getFilterFieldValue("ctlIncludeSentOrder"));n.addParameter("IsGlobalLogin",this._IsGlobalLogin);n.addParameter("IsGSA",this._IsGSA);n.addParameter("Client",this.getFilterFieldValue("ctlClientName"));n.addParameter("AS6081",this.getFilterFieldValue("ctlAS6081"));n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())},viewTask:function(){location.href="Prf_ToDo.aspx?Category=4";"_blank"},showAddForm:function(n,t,i){this._frmAdd.setFormFieldsToDefaults();this._frmAdd.setFieldValue("ctlDueTime","09:00");this._frmAdd.setFieldValue("ctlReminderTime","09:00");this._frmAdd.setFieldValue("ctlSalesOrder",n,null,t);this._frmAdd._intCategoryID=4;this._frmAdd._customerName=i;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this._frmAdd.resetFormData();this.showForm(this._frmAdd,!1)},addComplete:function(){this.hideAddForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},redirectToDetails:function(n){location.href="Prf_ToDo.aspx?so="+n+"&Category=4";"_blank"}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SalesOrders",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);