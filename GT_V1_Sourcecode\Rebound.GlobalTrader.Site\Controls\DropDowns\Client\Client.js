Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Client=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Client.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Client.prototype={get_IsGSA:function(){return this._IsGSA},set_IsGSA:function(n){this._IsGSA!==n&&(this._IsGSA=n)},get_IsGlobalUser:function(){return this._IsGlobalUser},set_IsGlobalUser:function(n){this._IsGlobalUser!==n&&(this._IsGlobalUser=n)},initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.Client.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.Client.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Client");this._objData.set_DataObject("Client");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Clients)for(location.pathname=="/Con_CompanyBrowse.aspx"&&(location.search=="?clt=0"||location.search=="?clt=1")&&(this._IsGlobalUser||this._IsGSA)&&this.addOption("All",-1),n=0;n<t.Clients.length;n++)this.addOption(t.Clients[n].Name,t.Clients[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Client.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Client",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);