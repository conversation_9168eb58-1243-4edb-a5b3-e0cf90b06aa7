﻿using System.Web;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    /// <summary>
    /// Summary description for CounterfeitElectronicParts
    /// </summary>
    public class CounterfeitElectronicParts : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("CounterfeitElectrionicParts");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            JsonObject jsnItem = new JsonObject();

            jsnItem.AddVariable("ID", "0");
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Select"));
            jsnList.AddVariable(jsnItem);

            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", "2");
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "No"));
            jsnList.AddVariable(jsnItem);

            jsnItem = new JsonObject();
            jsnItem.AddVariable("ID", "1");
            jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Yes"));
            jsnList.AddVariable(jsnItem);
            jsn.AddVariable("Types", jsnList);
            jsnItem.Dispose(); 
            jsnList.Dispose(); 
            OutputResult(jsn);
            jsn.Dispose();
        }
    }
}