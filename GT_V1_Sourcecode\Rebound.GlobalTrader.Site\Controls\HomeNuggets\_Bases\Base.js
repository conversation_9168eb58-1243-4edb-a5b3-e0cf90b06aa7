Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base=function(n){Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.initializeBase(this,[n]);this._intCompanyID=-1;this._intLoginID_Other=0;this._strNoData_My="";this._strNoData_Other="";this._isIncludeCredit=!1};Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intRowCount:function(){return this._intRowCount},set_intRowCount:function(n){this._intRowCount!==n&&(this._intRowCount=n)},get_intLoginID_My:function(){return this._intLoginID_My},set_intLoginID_My:function(n){this._intLoginID_My!==n&&(this._intLoginID_My=n)},get_strTitle_My:function(){return this._strTitle_My},set_strTitle_My:function(n){this._strTitle_My!==n&&(this._strTitle_My=n)},get_strTitle_Other:function(){return this._strTitle_Other},set_strTitle_Other:function(n){this._strTitle_Other!==n&&(this._strTitle_Other=n)},get_strNoData_My:function(){return this._strNoData_My},set_strNoData_My:function(n){this._strNoData_My!==n&&(this._strNoData_My=n)},get_strNoData_Other:function(){return this._strNoData_Other},set_strNoData_Other:function(n){this._strNoData_Other!==n&&(this._strNoData_Other=n)},get_blnCanShowForAnotherUser:function(){return this._blnCanShowForAnotherUser},set_blnCanShowForAnotherUser:function(n){this._blnCanShowForAnotherUser!==n&&(this._blnCanShowForAnotherUser=n)},get_isIncludeCredit:function(){return this._isIncludeCredit},set_isIncludeCredit:function(n){this._isIncludeCredit!==n&&(this._isIncludeCredit=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._intCompanyID=null,this._intRowCount=null,this._intLoginID_My=null,this._strTitle_My=null,this._strTitle_Other=null,this._strNoData_My=null,this._strNoData_Other=null,this._blnCanShowForAnotherUser=null,this._intLoginID_Other=null,this._isIncludeCredit=null,Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.callBaseMethod(this,"dispose"))},setupLoadingState:function(){this.showLoading(!0);this.showContentLoading(!0);this.showContent(!1);this.showNoData(!1)},showNoneFoundOrContent:function(n){n>0?this.showHomeNuggetContent(!0):this.showNoData(!0)},hideLoading:function(){this.showLoading(!1);this.showContentLoading(!1)},showHomeNuggetContent:function(n){$R_FN.showElement(this._pnlContent,n);n&&($R_FN.showElement(this._pnlError,!1),$R_FN.showElement(this._pnlLoading,!1))},addHTMLContentItem:function(n,t){var i=document.createElement("div"),r;i.className="contentItem";$R_FN.setInnerHTML(i,n);r=t?t:this._pnlContent;r.appendChild(i);r=null;t=null;i=null},homeNuggetDataError:function(n){this.showNoData(!1);this.showError(!0,n._errorMessage)},changeUser:function(n,t,i=false){this._blnCanShowForAnotherUser&&(this._intLoginID_Other=n,this.setTitle(String.format(this._strTitle_Other,t)),this.setNoDataMessage(String.format(this._strNoData_Other,t)),this._isIncludeCredit=i)},revertUserToCurrentLogin:function(){this._blnCanShowForAnotherUser&&(this._intLoginID_Other=0,this.setTitle(this._strTitle_My),this.setNoDataMessage(this._strNoData_My))}};Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base",Rebound.GlobalTrader.Site.Controls.Nuggets.Base,Sys.IDisposable);