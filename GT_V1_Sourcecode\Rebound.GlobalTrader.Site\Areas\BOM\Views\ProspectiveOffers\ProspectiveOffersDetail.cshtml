﻿@{
    Layout = null;
}

<!DOCTYPE html>

<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta charset="utf-8" />
    <title>Prospective Offers Details</title>
    <link rel="stylesheet" href="~/Areas/BOM/css/BOMManagerPOHub.css">

    @*<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css'>*@
    <link href="~/Areas/BOM/css/po-hub-libs/4.1.3-bootstrap.min.css" rel="stylesheet" />

    @*<script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/3.4.1/jquery.min.js'></script>
        <script src='https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.15.0/popper.min.js'></script>
        <script src='https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.3.1/js/bootstrap.min.js'></script>
        <script src='https://code.jquery.com/jquery-2.2.4.min.js'></script>
        <script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/js/bootstrap.min.js'></script>*@
    <script src="~/Areas/BOM/js/po-hub-libs/3.4.1-jquery.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/1.15.0-popper.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/4.3.1-bootstrap.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/jquery-2.2.4.min.js"></script>
    <script src="~/Areas/BOM/js/po-hub-libs/3.3.6-bootstrap.min.js"></script>

    <!-- Latest compiled and minified CSS -->
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.6/css/bootstrap.min.css" integrity="sha384-1q8mTJOASx8j1Au+a5WDVnPi2lkFfwwEAa8hDDdjZlpLegxhjVME1fgjWPGmkzs7" crossorigin="anonymous">
    @*<link href="~/Areas/BOM/css/po-hub-libs/3.3.6-bootstrap.min.css" rel="stylesheet" />*@

    @*<script src="https://unpkg.com/jquery@2.2.4/dist/jquery.js"></script>*@
    <script src="~/Areas/BOM/js/po-hub-libs/2.2.4-dist-jquery.js"></script>

    <!--jQueryUI version 1.11.4 -->
    @*<link rel="stylesheet" href="https://code.jquery.com/ui/1.11.4/themes/smoothness/jquery-ui.css" />
        <script src="https://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
        <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">*@
    <link href="~/Areas/BOM/css/po-hub-libs/1.11.4-themes-smoothness-jquery-ui.css" rel="stylesheet" />
    <script src="~/Areas/BOM/js/po-hub-libs/1.11.4-jquery-ui.min.js"></script>
    @*<link href="~/Areas/BOM/css/po-hub-libs/1.13.2-themes-base-jquery-ui.css" rel="stylesheet" />*@
    <link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <script src="~/Areas/BOM/js/POHubDatePicker.js"></script>
    <!--ParamQuery Grid css files-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.dev.css" />
    <!--add pqgrid.ui.css for jQueryUI theme support-->
    <link rel="stylesheet" href="~/paramquery-8.1.0/pqgrid.ui.dev.css" />
    <!--ParamQuery Grid custom theme e.g., office, bootstrap, rosy, chocolate, etc (optional)-->
    @*<link rel="stylesheet" href="~/paramquery-8.1.0/themes/bootstrap/pqgrid.css" />*@   @*commented this file  version1.0*@
    <!--ParamQuery Grid js files-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqgrid.dev.js"></script>
    <!--ParamQuery Grid localization file-->
    <script src="~/paramquery-8.1.0/localize/pq-localize-en.js"></script>
    <!--Include pqTouch file to provide support for touch devices (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/pqTouch/pqtouch.min.js"></script>
    <!--Include jsZip file to support xlsx and zip export (optional)-->
    <script type="text/javascript" src="~/paramquery-8.1.0/jsZip-2.5.0/jszip.min.js"></script>
    <link href="~/Areas/BOM/css/bom-manager-order-po-hub.css" rel="stylesheet" />

    <link href="~/Areas/BOM/css/prospective-offers.css" rel="stylesheet" />
</head>
<body>
    <div class="boxContent">
        <div class="pageTitle">
            <h3>Prospective Offers Details</h3>
        </div>
        <div class="BomLink ">
            <a id="SourceFile"><span></span></a><br>
            <a class="itemTitle" id="SupplierName"><span></span></a>
            <br>
            <a class="itemTitle" id="UploadBy"><span></span></a><br>
            <a id="Date" class="date"><span></span></a><br>
            <a id="NoOfRows"><span></span></a><br>
        </div>
        <div class="pageTitleTopRight">
        </div>
    </div>
    <div id="divPartBom" class="section_container">
        <div class="section_one" id="headingThree">
            <div class="topTL"></div>
            <div class="topTR"></div>
            <div id="head_in_DivPartBom" class="head_in">
                <div class="top_inner">
                    <h4>Prospective Offers Summary</h4>
                    <img id="refreshPrOItem" class="refresh" src="/Areas/BOM/Images/refresh.gif" style="width:11px; height:10px; float:right; position:absolute; right:28px; top:4px; display:block; cursor:pointer">
                    <a id="ShowPartBomDiv" role="button" class="sec_tab"></a>
                    <div id="divBOMItemButtons" class="boxlink">
                        <a id="editRef" href="#"><span class="linkbuttoncss EditMfr" id="editMfr">Edit Manufacturer</span></a>
                    </div>
                </div>
            </div>
        </div>
        <div id="DivPartBomGrid">
            <div id="grid_md"></div>
        </div>
    </div>
    <div class="section_container">
        <div class="section_one" id="headingThree">
            <div class="topTL"></div>
            <div class="topTR"></div>
            <div id="head_in_DivGTOffer" class="head_in">
                <div class="top_inner">
                    <h4>Matching HUBRFQ - Offers</h4>
                    <img id="refreshGTOffers" class="refresh" src="/Areas/BOM/Images/refresh.gif" style="width:11px; height:10px; float:right; position:absolute; right:24px; top:4px; cursor:pointer">
                    <p style="color: forestgreen; font-weight:bold; font-size:11px; margin-left:5px;">Please select from the below historic offers, lines which are suitable for cross selling to the selected customers</p>
                    <a id="ShowMatchingGTOffer" role="button" class="sec_tab"></a>
                    <div id="divBOMItemButtons" class="boxlink" style="margin-bottom: 10px">
                        <a id="sendGTOffers" href="#"><span class="linkbuttoncss SendOffer" id="sendOfferBtn">Send Offers</span></a>
                        <a><span class="linkbuttoncss add_comm_note disabled" id="btnOfferHistory">Offer History</span></a>
                        <a href="#"><span class="linkbuttoncss Cancel" id="cancelBtn">Cancel</span></a>
                    </div>
                </div>
            </div>
        </div>
        <div style="background-color: #BBF2B3; padding: 5px" id="filterGTOffers">
            <div style="color:seagreen">
                <table class="dataItems">
                    <tr>
                        <td>
                            <label class="labelFilter" style="width:175px !important">
                                GT HUBRFQ Historic Data Range
                            </label>
                        </td>
                        <td>
                            <select name="monthRanges" id="month" style="margin-left:19px; height:20px" onchange="monthChanged(this)">
                                <option value="3">Up to 3 months</option>
                                <option value="6">Up to 6 months</option>
                                <option value="9">Up to 9 months</option>
                                <option value="12" class="selected" selected="selected">Up to 12 months</option>
                            </select><br />
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label class="labelFilter">Minimum Offer Qty</label>
                        </td>
                        <td>
                            <a style="background-color:white;border:1px solid;">>=</a>
                            <input type="text" id="input1" style="color:black" oninput="validateInputNumeric(this)" onblur="hideMessage(this)" placeholder="0"/>
                            <p id="errorMessage1" style="color: red; display: none;">Please enter only numeric characters!</p>
                        </td>
                    </tr>
                    <tr>
                        <td><label class="labelFilter">Minimum SO Line Value</label></td>
                        <td>
                            <a style="background-color:white;border:1px solid;">>=</a>
                            <input type="text" id="input2" style="color:black" oninput="validateInputNumeric(this)" onblur="hideMessage(this)" placeholder="0"/>
                            <p id="errorMessage2" style="color: red; display: none;">Please enter only numeric characters!</p>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input type="button" id="btnUnmatchedMfr" title="Show Historic Alternative manufacturers" value="Show Historic Alternative manufacturers"
                                   style="width:150px; white-space:normal" onclick="UnOrMatchedMfr()"/>
                        </td>
                        <td>
                            <b style="margin-left:1350px; float:right; font-style:italic;" id="message">Results shown based on Part Number and Manufacturer</b>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        <div id="divGTOffers">
            <div id="grid_GTOffers"></div>
        </div>
    </div>
    
    <div class="modal bd-example-modal-lg" id="OfferHistoryModal" role="dialog">
        <input type="hidden" id="OfferHistoryID" />
        <div>
            <div class="modal-content" style="background-color: #AAE2A0;">
                <div class="main_header">
                    <h6 id="OfferHistoryTitle">
                        Offer History
                    </h6>
                    <a id="btnRemindSalespersonOfferHistory" class="RemindSalespersonOfferHistory BtnSubmitEdit disabled" rel="modal:close">Remind Salesperson</a>
                    &nbsp;&nbsp;&nbsp;
                    <a id="btnCancelOfferHistory" class="CancelOfferHistory BtnCloseEdit" rel="modal:close">Cancel</a>
                </div>
                <div class="form_container">
                    <div class="header_text">
                        <h4>
                            TRACK AND REMIND SALESPERSON HUBRFQ - PROSPECTIVE OFFERS
                        </h4>
                        <div class="formInstructions"></div>
                    </div>
                    <div class="modal-body">
                        <div id="OfferHistoryBody">
                            <div class="details_col">
                                <table class="table_one">
                                    <tbody>
                                        <tr>
                                            <td class="name_text">Source File Name</td>
                                            <td class="name_right"><label id="lblSourceFileName"></label></td>
                                        </tr>
                                        <tr>
                                            <td class="name_text">Uploaded Row</td>
                                            <td class="name_right"><label id="lblUploadedRowLogs"></label></td>
                                        </tr>
                                        <tr>
                                            <td class="name_text">Part Number</td>
                                            <td class="name_right"><label id="lblPartNumber"></label></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="OfferHistoryGridContainer">
                                <div class="OfferHistory_grid">
                                    <div id="grid_OfferHistory"></div>
                                </div>
                                <div class="alt_heading" style="margin-top:10px">
                                    Detail Log
                                </div>
                                <div class="detailLog_grid">
                                    <div id="grid_DetailLog"></div>
                                </div>
                            </div>
                            <br>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal bd-example-modal-md" id="EditMfrModal" role="dialog">
        <div>
            <div class="modal-content" style="background-color: #AAE2A0;">
                <div class="main_header">
                    <h6>Manufacturer Information</h6>
                    <a id="btnSaveMfr" class="SaveMfr BtnSubmitEdit" rel="modal:close">Save</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a id="btnCancelMfr" class="CancelMfr BtnCloseEdit" rel="modal:close">Cancel</a>
                </div>
                <div class="form_container">
                    <div class="header_text">
                        <h4>EDIT MANUFACTURER INFORMATION</h4>
                        <div class="formInstructions">Amend the details and press <b>Save</b></div>
                    </div>
                    <div class="modal-body edit_modal">
                        <div style="display:none; margin-bottom:20px" id="EditManufacturerValidationError" class="errorSummary">
                            There was problem with your manufacturer<br>Please check below and try again.
                        </div>
                        <table class="offer_tbl">
                            <tr>
                                <td class="column-title">
                                    Source File Name
                                </td>
                                <td class="column-value">
                                    <div>
                                        <label id="lblFileName"></label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Uploaded Row
                                </td>
                                <td class="column-value">
                                    <div>
                                        <label id="lblUploadedRow"></label>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="column-title">
                                    Part Number
                                </td>
                                <td class="column-value">
                                    <div>
                                        <label id="lblPartNo"></label>
                                    </div>
                                </td>
                            </tr>
                            <tr id="RowEditManufacturer">
                                <td class="column-title">
                                    Manufacturer<span class="mandetory">*</span>
                                </td>
                                <td class="column-value">
                                    <div class="row" id="search-manufacturer">
                                        <div class="col">
                                            <input id="textEditManufacturer" class="ui-autocomplete-input" type="text" placeholder="Type 2 chars to search" />
                                            <input type="hidden" id="hiddenEditManufacturer" />
                                        </div>
                                    </div>
                                    <div class="row" id="display-manufacturer">
                                        <div class="col">
                                            <label id="lblEditManufacturer"></label>
                                            <a class="re-select" id="ReselectEditManufacturer">[Reselect]</a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="row-error" style="display:none" id="RowEditManufacturerError">
                                <td class="column-title">&nbsp;</td>
                                <td class="column-value">
                                    <div class="formMessages">Please enter a value</div>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <!-- Modal Footer -->
                </div>
                <input type="hidden" id="hdnSourceId" />
                <div class="formNotes">
                    <span class="mandetory">*</span> denotes a required field
                </div>
                <div class="footer_area">
                    <a id="btnSaveMfr" class="SaveMfr BtnSubmitEdit" rel="modal:close">Save</a>
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <a id="btnCancelMfrFooter" class="CancelMfr BtnCloseEdit" rel="modal:close">Cancel</a>
                </div>
            </div>
        </div>
    </div>
    <div class="modal bd-example-modal-lg" id="SendOfferModal" role="dialog">
        <div>
            <div class="modal-content" style="background-color: #AAE2A0;">
                <div class="main_header">
                    <h6 id="SendOfferTitle"></h6>
                </div>
                <div class="form_container">
                    <div class="header_text">
                        <h4><label id="ActionText"></label></h4>
                        <div class="formInstructions"></div>
                    </div>
                    <div class="modal-body">
                        <div id="GTOfferSource" class="">
                            <div class="row">
                                <div class="col-md-auto" style="width: 14%; float: left; color: #d3fFcC; font-weight:bold;">
                                    Please Confirm
                                </div>
                                <div class="col" style="width:85%; float:left;">
                                    <a id="btnSBConfirmModal" class="ConfirmSendOffers yes_icon" rel="modal:close">Yes</a>&nbsp;
                                    <a id="btnSBCancelModal" class="CancelSendOffers n_icon" rel="modal:close">No</a>
                                    <input type="hidden" id="hdnReqType" />
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col-md-auto" style="width: 14%; float: left; color: #d3fFcC; font-weight: bold;">
                                    <span id="lblResult">Matching HUBRFQ - Offers</span>
                                </div>
                            </div>
                            <br>
                            <div class="row">
                                <div class="col" style="width:100%; float:left; display:flex;    ">
                                    <div id="grid_SB"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script type="text/javascript">
        var firstload = true;
        var OffersLineRefresh = true;
        var partload = false;
        var editsource = false;
        var defaultOption = '<option value="-1">&lt; Select &gt;</option>';
        var refershOption = '<option selected="true" value="Loading">loading</option>';
        var optionStart = "<option value='";
        var optionEnd = "</option>";
        var listParts = [];
        var lineIds = [];
        var ReqlogIds = []
        var listGTIds = [];
        var listGTIds_logs = [];
        var firstLine;
        var dropdownUrl = window.location.origin + "/Controls/Nuggets/CrossMatch/CrossMatch.ashx";
        var unSentOffers = [];
        var selectedProspetiveOfferLines = [];
        var isUnmatch = false;
        $(document).ready(function () {
            var qrystr1 = new URLSearchParams(window.location.search);
            var qrystrkey1 = qrystr1.get("PROId");
            if (!firstLine) {
                $("#editMfr").prop('disabled', true).css('opacity', 0.5);
                $("#editRef").addClass("disable-click");
                $("#btnOfferHistory").prop('disabled', true).css('opacity', 0.5);
                $("#btnRemindSalespersonOfferHistory").prop('disabled', true).css('opacity', 0.5);
                $("#btnOfferHistory").addClass("disable-click");
                $("#btnRemindSalespersonOfferHistory").addClass("disable-click");

            }
            disableSendOfferButton();

            GetProspectiveOffersStatus(qrystrkey1);
            LoadProsOfferItem1(true);
            LoadGTOffersBelong([], true);

            $("#grid_md .pq-page-next").click(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                PrOLineGridHeightChange(recordsLeft, rpp);

            });
            $("#grid_md .pq-page-prev").click(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                PrOLineGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md .pq-page-first").click(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = 1;
                var recordsLeft = totalRecords - (rpp * curPage);
                PrOLineGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md .pq-page-last").click(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.totalPages;
                var recordsLeft = totalRecords - (rpp * curPage);
                PrOLineGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md .pq-page-select").change(function () {

                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                PrOLineGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_md .pq-page-current").change(function () {
                var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = $('#grid_md .pq-page-current').val();
                var recordsLeft = totalRecords - (rpp * curPage);
                PrOLineGridHeightChange(recordsLeft, rpp);
            });

            $("#grid_GTOffers .pq-page-next").click(function () {
                var gridInstanceRPP = $('#grid_GTOffers').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                GTOffersGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_GTOffers .pq-page-prev").click(function () {
                var gridInstanceRPP = $('#grid_GTOffers').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                GTOffersGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_GTOffers .pq-page-first").click(function () {
                var gridInstanceRPP = $('#grid_GTOffers').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = 1;
                var recordsLeft = totalRecords - (rpp * curPage);
                GTOffersGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_GTOffers .pq-page-last").click(function () {
                var gridInstanceRPP = $('#grid_GTOffers').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.totalPages;
                var recordsLeft = totalRecords - (rpp * curPage);
                GTOffersGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_GTOffers").on('change', '.pq-page-select', function () {
                var gridInstanceRPP = $('#grid_GTOffers').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = gridInstanceRPP.options.pageModel.curPage;
                var recordsLeft = totalRecords - (rpp * curPage);
                GTOffersGridHeightChange(recordsLeft, rpp);
            });
            $("#grid_GTOffers .pq-page-current").change(function () {

                var gridInstanceRPP = $('#grid_GTOffers').pqGrid('instance');
                var rpp = gridInstanceRPP.options.pageModel.rPP;
                var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                var curPage = $('#grid_GTOffers .pq-page-current').val();
                var recordsLeft = totalRecords - (rpp * curPage);
                GTOffersGridHeightChange(recordsLeft, rpp);
            });
        });

        function GetProspectiveOffersStatus(proId) {
            $.ajax({
                processData: true,
                contentType: 'application/json',
                type: 'POST',
                //asynch: false,
                url: 'GetProspectiveOffersStatus?ProId=' + proId,
                dataType: "json",
                success: function (data) {
                    $('#SourceFile').text("Source File: " + data.SourceFile);
                    $('#SupplierName').text("Supplier Name: " + data.SupplierName);
                    $('#UploadBy').text("Upload By: " + data.UploadBy);
                    $('#Date').text("Date: " + data.Date);
                    $('#NoOfRows').text("No of Rows: " + data.NoOfRows);
                },
                error: function (err) { }
            });
        }
        function LoadProsOfferItem1(refreshGrid) {
            var qrystr = new URLSearchParams(window.location.search);
            var ProId = qrystr.get("PROId");
            var colModel = [
                {
                    title: "ProspectiveOfferLineId",
                    dataIndx: "ProspectiveOfferLineId",
                    hidden: true
                },
                {
                    title: "<span title='Uploaded Row No#'>Uploaded Row No#</span>",
                    cb: { header: true, select: false, all: false },
                    width: "7%",
                    type: 'text',
                    sortable: false,
                    render: function (ui) {
                        return "<span>" + ui.rowData.Id.toString() + "</span ><br/>";
                    }
                },
                {
                    title: "<span title='Part No'>Part No",
                    width: "15%",
                    dataIndx: "PartNo",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.PartNo.toString() + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Quantity Offered",
                    width: "10%",
                    dataIndx: "QuantityOffered",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.QuantityOffered.toString() + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Prospective Offer Price",
                    width: "10%",
                    dataIndx: "UploadedOfferPrice",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.UploadedOfferPrice.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "IHS Avg Price",
                    width: "10%",
                    dataIndx: "IHSAvgPrice",
                    render: function (ui) {
                        if (ui.rowData.IsFromGT == 0) {
                            var htmlstr = "<span>" + ui.rowData.IHSAvgPrice.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                            return htmlstr;
                        }
                        else {
                            return '';
                        }
                    }
                },
                {
                    title: "Lytica Avg Price",
                    width: "10%",
                    dataIndx: "LyticaAvgPrice",
                    render: function (ui) {
                        return "<span>" + ui.rowData.LyticaAvgPrice.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                    }
                },
                {
                    title: "GT Lowest Offer",
                    width: "10%",
                    dataIndx: "LowestOffer",
                    render: function (ui) {
                        return "<span>" + ui.rowData.LowestOffer.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                    }
                },
                {
                    title: "GT Highest Offer",
                    width: "10%",
                    dataIndx: "HighestOffer",
                    render: function (ui) {
                        return "<span>" + ui.rowData.HighestOffer.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                    }
                },
                {
                    title: "<span title='Number of Reqs in last 12 months'>Number of Reqs in last 12 months</span>",
                    width: "10%",
                    render: function (ui) {
                        if (ui.rowData.IsFromGT == 0) {
                            return "<span>" + ui.rowData.GTReqCount.toString() + "</span ><br/>";
                        }
                        else {
                            return '';
                        }
                    }
                },
                {
                    title: "Manufacturer",
                    width: "10%",
                    dataIndx: "Manufacturer",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.Manufacturer.toString() + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Notes",
                    width: "10%",
                    dataIndx: "Notes",
                    render: function (ui) {
                        if (ui.rowData.Notes != null && ui.rowData.Manufacturer == "") {
                            return "<span>|Mfr: " + ui.rowData.Notes.toString() + "</span>";
                        }
                        else {
                            return "";
                        }
                    }
                }
            ];

            //main object to be passed to pqGrid constructor.
            var qrystr = new URLSearchParams(window.location.search);
            var qrystrkey = qrystr.get("PROId");
            var month = document.getElementById("month").value;

            var dataModel = {
                location: "remote",
                method: "POST",
                url: 'GetPrOLineItem?PROId=' + qrystrkey,
                data: { proId: parseInt(qrystrkey) },
                postData: function () {
                    return { proId: parseInt(qrystrkey) }
                },
                getData: function (dataJSON) {
                    var data = dataJSON;

                    if (dataJSON.length > 0) {
                        dataJSON
                    }
                    var totalRecords = 0;
                    if (data.length != 0) {
                        totalRecords = data[0].TotalCount;
                    }
                    var cur_page = 1;
                    if (dataJSON.length != 0) {
                        cur_page = dataJSON[0].curpage;
                    }
                    return { curPage: cur_page, totalRecords: totalRecords, data: data };
                }
            };


            var grid1 = $("#grid_md").pqGrid({
                width: "auto",
                height: 250,
                selectionModel: { type: 'row', native: true, all: true },
                dataModel: dataModel,
                rowHt: 35,
                hoverMode: 'cell',
                hwrap: false,
                wrap: false,
                colModel: colModel,
                complete: function (event, ui) {
                    var gridInstance = grid1.pqGrid('getInstance');

                    var gridInstanceRPP = $('#grid_md').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    PrOLineGridHeightChange(recordsLeft, rpp);
                    FormatFinalData();
                },
                cellKeyDown: function (evt, ui) {
                    var sr = this.SelectRow();
                    var currentIndx = ui.rowIndx;
                    var LastIndx = ((this.options.pageModel.curPage - 1) * this.options.pageModel.rPP) + this.pdata.length;
                    var StartIndx = ((this.options.pageModel.curPage - 1) * this.options.pageModel.rPP);
                    if (evt.keyCode == $.ui.keyCode.DOWN) {
                        if (currentIndx == LastIndx - 1) {
                            return;
                        }
                        sr.removeAll();
                        sr.add({ rowIndx: ui.rowIndx + 1 });
                    } else if (evt.keyCode == $.ui.keyCode.UP) {
                        if (currentIndx == StartIndx) {
                            return;
                        }
                        sr.removeAll();
                        sr.add({ rowIndx: ui.rowIndx - 1 });
                    }
                },
                editable: false,
                pageModel: { type: "local", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                numberCell: { show: false },
                rowSelect: function (evt, ui) {
                    let minQty = $("#input1").val();
                    let minSOVal = $("#input2").val();
                    let month = document.getElementById("month").value;
                    disableSendOfferButton();
                    if (ui.addList[0] != undefined) {
                        if (ui.deleteList[0] != undefined) {
                            let dels = ui.deleteList.map(x => x.rowData.ProspectiveOfferLineId);
                            lineIds = lineIds.filter(x => !dels.includes(x));
                            lineIds = lineIds.concat(ui.addList.map(x => x.rowData.ProspectiveOfferLineId));
                        }
                        else {
                            lineIds = lineIds.concat(ui.addList.map(x => x.rowData.ProspectiveOfferLineId));
                        }
                        if (lineIds.length > 0) {
                            $("#editMfr").prop('disabled', false).css('opacity', 1);
                            $("#editRef").removeClass("disable-click");
                            $("#btnOfferHistory").prop('disabled', false).css('opacity', 1);
                            $("#btnOfferHistory").removeClass("disable-click");

                            firstLine = ui.addList[0].rowData;
                            selectedProspetiveOfferLines = [...new Set([...selectedProspetiveOfferLines, ...ui.addList.map(x => x.rowData)])];
                        }
                        LoadGTOffersBelong(lineIds, minQty, minSOVal, month, true);
                    }
                    else {
                        if (ui.deleteList[0] != undefined) {
                            let dels = ui.deleteList.map(x => x.rowData.ProspectiveOfferLineId);
                            lineIds = lineIds.filter(x => !dels.includes(x));
                            lineIds = lineIds.concat(ui.addList.map(x => x.rowData.ProspectiveOfferLineId));
                            if (lineIds.length > 0){
                                selectedProspetiveOfferLines = [...new Set([...selectedProspetiveOfferLines, ...ui.addList.map(x => x.rowData)])]
                                .filter(item => !dels.includes(item.ProspectiveOfferLineId));
                            }
                            if (lineIds.length == 0) {
                                $("#editMfr").prop('disabled', true).css('opacity', 0.5);
                                $("#editRef").addClass("disable-click");
                                firstLine = undefined;
                                selectedProspetiveOfferLines = [];
                                unSentOffers = [];
                            }
                        }
                        LoadGTOffersBelong(lineIds, minQty, minSOVal, month, true);
                    }
                }
            });
        }

        function CheckValuesBeforeCall() {
            let minQty = $("#input1").val();
            let minSOVal = $("#input2").val();
            let month = document.getElementById("month").value;
            LoadGTOffersBelong(lineIds, minQty, minSOVal, month, true);
        }

        function LoadGTOffersBelong(ids, minQty, minSOVal, month, refreshGrid) {
            var colModel = [
                {
                    title: "Part No",
                    width: "12%",
                    dataIndx: "PartNo",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.PartNo.toString() + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "<span title='Customer (REQ Date)'>Customer (REQ Date)</span>",
                    width: "12%",
                    dataIndx: "HUBRFQCustomer",
                    render: function (ui) {
                        if (ui.rowData.IsFromGT == 1) {
                            const title = ui.rowData.HUBRFQCustomer.toString();
                            var htmlstr = "<span title='" + title + "'><a href=\"../../Ord_BOMDetail.aspx?BOM=" + ui.rowData.BOMNo.toString() + "\">" + ui.rowData.HUBRFQCustomer.toString() + "</a></span ><br/>";
                            return htmlstr;
                        }
                    }
                },
                {
                    title: "<span title='Quantity + Price + Date Offered By Hub (Offered Date)'>Quantity + Price + Date Offered By Hub (Offered Date)</span>",
                    width: "15%",
                    dataIndx: "QuantityOffered",
                    render: function (ui) {
                        const title = ui.rowData.QuantityOffered.toString() + " QTY " 
                            + ui.rowData.UploadedOfferPrice.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + " "
                            + ui.rowData.DateRelease;
                        var htmlstr = "<span title='" + title + "'>" + title + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "IHS Avg Price",
                    width: "10%",
                    dataIndx: "IHSAvgPrice",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.IHSAvgPrice.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Lytica Avg Price",
                    width: "10%",
                    dataIndx: "LyticaAvgPrice",
                    render: function (ui) {
                        var htmlstr = "<span>" + ui.rowData.LyticaAvgPrice.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                        return htmlstr;
                    }
                },
                {
                    title: "Quote Price + QTY",
                    width: "10%",
                    render: function (ui) {
                        return "<span>" + ui.rowData.QuotePrice.toFixed(5).toString() + ' ' + ui.rowData.Currency.toString() + ' ' + ui.rowData.QuoteQTY.toString() + ' QTY' + "</span ><br/>";
                    }
                },
                {
                    title: "<span title='SO Price + QTY + Date'>SO Price + QTY + Date</span>",
                    width: "10%",
                    render: function (ui) {
                        let date = "";
                        if (ui.rowData.SODate != null) {
                            const timestampMatch = ui.rowData.SODate.match(/\/Date\((\d+)\)\//);
                            if (timestampMatch) {
                                const timestamp = parseInt(timestampMatch[1], 10);
                                const parsedDate = new Date(timestamp);
                                date = parsedDate.toLocaleDateString('en-GB'); // Format as DD/MM/YYYY
                            }
                        }
                        return "<span>" + ui.rowData.SOPrice.toFixed(5).toString() + ' ' + ui.rowData.Currency.toString() + ' ' + ui.rowData.SOQTY.toString() + ' QTY'
                            + " " + date + "</span ><br/>";
                    }
                },
                {
                    title: "<span title='SO line order value'>SO line order value</span>",
                    width: "10%",
                    render: function (ui) {
                        return "<span>" + ui.rowData.SOLineValue.toFixed(5).toString() + ' ' + ui.rowData.Currency.toString() + "</span ><br/>";
                    }
                },
                {
                    title: "Manufacturer",
                    width: "11%",
                    dataIndx: "Manufacturer",
                    render: function (ui) {
                        const title = ui.rowData.Manufacturer.toString();
                        var htmlstr = "<span title='" + title + "'>" + title + "</span ><br/>";
                        return htmlstr;
                    }
                }
            ];
            let mfrs = [];
            let selectedMfrs = selectedProspetiveOfferLines.map(x => x.Manufacturer);
            mfrs = !isUnmatch ? selectedMfrs : mfrs;


            var qrystr = new URLSearchParams(window.location.search);
            var qrystrkey = qrystr.get("PROId");
            var dataSales = { proId: qrystrkey, ids, mfrsName: mfrs, monthRange: parseInt(month), minOfferQty: minQty, minSOVal: minSOVal }
            var dataModel = {
                location: "remote",
                method: "POST",
                url: 'GetGTOffersDetail',
                postData: function () {
                    // Define the JSON data to send with the POST request
                    return dataSales;
                },
                getData: function (dataJSON) {
                    if (ids.length == 0) {
                        data = []
                    }
                    else {
                        var data = dataJSON;
                        if (dataJSON.length > 0) {
                            data = dataJSON;
                        }
                        if (lineIds && (data.some(obj => obj['SentProspectiveOfferAt'] === "") || data.some(obj => !obj['IsProspectiveSent']))) {
                            enableSendOfferButton();
                        }
                        else {
                            disableSendOfferButton();
                        }
                        var totalRecords = data.length !== 0 ? data[0].TotalCount : 0;
                        var cur_page = dataJSON.length !== 0 ? dataJSON[0].curpage : 1;
                    }
                    let originalData = [...selectedProspetiveOfferLines, ...data];
                    if (isUnmatch)
                    {
                        data = data.filter(value => !selectedMfrs.includes(value.Manufacturer));
                    }
                    unSentOffers = JSON.parse(JSON.stringify(originalData));
                    return { curPage: cur_page, totalRecords: totalRecords, data: data };
                }
            };

            var grid1 = $("#grid_GTOffers").pqGrid({
                width: "auto",
                height: 272,
                selectionModel: { type: 'row', native: true, all: true },
                dataModel: dataModel,
                rowHt: 35,
                hoverMode: 'cell',
                hwrap: false,
                wrap: false,
                colModel: colModel,
                editable: false,
                pageModel: { type: "local", rPP: 5, strRpp: "{0}", rPPOptions: [5, 10] },
                numberCell: { show: false },
                complete: function (event, ui) {
                    var gridInstance = grid1.pqGrid('getInstance');

                    var gridInstanceRPP = $('#grid_GTOffers').pqGrid('instance');
                    var rpp = gridInstanceRPP.options.pageModel.rPP;
                    var totalRecords = gridInstanceRPP.options.pageModel.totalRecords;
                    var curPage = gridInstanceRPP.options.pageModel.curPage;
                    var recordsLeft = totalRecords - (rpp * curPage);
                    GTOffersGridHeightChange(recordsLeft, rpp);
                },
                cellKeyDown: function (evt, ui) {
                    var sr = this.SelectRow();
                    var currentIndx = ui.rowIndx;
                    var LastIndx = ((this.options.pageModel.curPage - 1) * this.options.pageModel.rPP) + this.pdata.length;
                    var StartIndx = ((this.options.pageModel.curPage - 1) * this.options.pageModel.rPP);
                    if (evt.keyCode == $.ui.keyCode.DOWN) {
                        if (currentIndx == LastIndx - 1) {
                            return;
                        }
                        sr.removeAll();
                        sr.add({ rowIndx: ui.rowIndx + 1 });
                    } else if (evt.keyCode == $.ui.keyCode.UP) {
                        if (currentIndx == StartIndx) {
                            return;
                        }
                        sr.removeAll();
                        sr.add({ rowIndx: ui.rowIndx - 1 });
                    }
                }
            });
            if (refreshGrid) {
                grid1.pqGrid('refreshDataAndView');
            }
        }

        function FormatFinalData() {
            var grid1 = $('#grid_md').pqGrid('instance');
            var rowcount = grid1.getTotalRows();
            for (var i = 0; i < rowcount; i++) {
                var importedFlag = grid1.getRowData({ rowIndx: i }).IsFromGT;
                if (importedFlag == 0) {
                    grid1.addClass({ rowIndx: i, cls: 'highlighted-row' });
                }
            }
        }

        function PrOLineGridHeightChange(recordsLeft, rpp) {

            var height;

            if (recordsLeft < 0) {
                recordsLeft = rpp + recordsLeft;
                height = recordsLeft * 35 + 40;
            }
            else {
                height = rpp * 35 + 40;
            }
            $('#grid_md .pq-grid-center-o').css('height', height + 10);
            $('#grid_md .pq-grid-center').css('height', height + 10);
            $('#grid_md .pq-body-outer').css('height', height + 10);
            $('#grid_md').pqGrid('option', 'height', height + 50).pqGrid('refresh');
        }

        function GTOffersGridHeightChange(recordsLeft, rpp) {
            var height;

            if (recordsLeft < 0) {
                recordsLeft = rpp + recordsLeft;
                height = recordsLeft * 35 + 40;
            }
            else {
                height = rpp * 35 + 40;
            }

            $('#grid_GTOffers .pq-grid-center-o').css('height', height + 15);
            $('#grid_GTOffers .pq-grid-center').css('height', height + 9);
            $('#grid_GTOffers .pq-body-outer').css('height', height + 7);
            $('#grid_GTOffers .pq-body-outer').css('padding-bottom', '8px');
            $('#grid_GTOffers').pqGrid('option', 'height', height + 60).pqGrid('refresh');
        }

        function filterApply() {
            let minQty = $("#input1").val();
            let minSOVal = $("#input2").val();
            let month = document.getElementById("month").value;
            LoadGTOffersBelong(lineIds, minQty, minSOVal, month, true);
        };

        $("#refreshPrOItem").click(refreshPrOItem);

        function refreshPrOItem() {
            var gridInstance = $('#grid_md').pqGrid('getInstance');
            deSelectedOffers();
            disableSendOfferButton();
            $('#grid_md').pqGrid('refreshDataAndView');
            LoadProsOfferItem1(true);
            lineIds = []
            listGTIds = []
            firstLine = undefined;
            resetUnmatchedMfrBtn();
            filterApply();
        }

        $("#refreshGTOffers").click(refreshGTOffers);

        function refreshGTOffers() {
            $('#grid_GTOffers').pqGrid('refreshDataAndView');
            listGTIds = [];
            $("#input1").val('');
            $("#input2").val('');
            document.getElementById("month").value = 12;
            resetUnmatchedMfrBtn();
            deSelectedOffers();
            disableSendOfferButton();
            filterApply();
        }

        function resetUnmatchedMfrBtn() {
            isUnmatch = false;
            document.getElementById("btnUnmatchedMfr").value = "Show Historic Alternative manufacturers";
            document.getElementById("btnUnmatchedMfr").textContent = "Show Historic Alternative manufacturers";
            document.getElementById("btnUnmatchedMfr").title = "Show Historic Alternative manufacturers";
            document.getElementById("message").innerHTML = "Results shown based on Part Number and Manufacturer";
        }

        $("#sendOfferBtn").click(function () {
            $('#SendOfferTitle').text("Sending Prospective Offer(s)");
            $('#ActionText').text('Are you sure to send this prospective offer(s) to Salesperson ?');
            LoadSendOfferModal();
        });

        $("#cancelBtn").click(function () {
            window.parent.location.href = window.location.origin + '/' + window.parent.$R_URL_Utility_ProspectiveOffer;
        })

        function validateInputNumeric(inputField) {
            const errorMessage = document.getElementById("errorMessage" + inputField.id.slice(-1));
            const regex = /^[0-9]*$/;

            if (!regex.test(inputField.value)) {
                errorMessage.style.display = "block";
                document.getElementById("input" + inputField.id.slice(-1)).value = "";
            } else {
                errorMessage.style.display = "none";
                filterApply();
            }
        }

        function UnOrMatchedMfr() {
            isUnmatch = document.getElementById("btnUnmatchedMfr").value == "Show Historic Alternative manufacturers" ? true : false;
            if (isUnmatch) {
                document.getElementById("btnUnmatchedMfr").value = "Show Only Historic Matching Manufacturers";
                document.getElementById("btnUnmatchedMfr").textContent = "Show Only Historic Matching Manufacturers";
                document.getElementById("btnUnmatchedMfr").title = "Show Only Historic Matching Manufacturers";
                document.getElementById("message").innerHTML = "Results shown based on Part Number";
                $('#message').css("margin-left", '1450px');
            } else {
                document.getElementById("btnUnmatchedMfr").value = "Show Historic Alternative manufacturers";
                document.getElementById("btnUnmatchedMfr").textContent = "Show Historic Alternative manufacturers";
                document.getElementById("btnUnmatchedMfr").title = "Show Historic Alternative manufacturers";
                document.getElementById("message").innerHTML = "Results shown based on Part Number and Manufacturer";
                $('#message').css("margin-left", '1350px');
            }
            filterApply();
        }

        function monthChanged(inputField) {
            const monthSelect = document.getElementById("month").value;
            filterApply();
        }

        function hideMessage(inputField) {
            const errorMessage = document.getElementById("errorMessage" + inputField.id.slice(-1));
            errorMessage.style.display = "none";
        }


        function sendGTOffers(qrystrkey, ids, gtIds) {
            var dataModel = {
                location: "remote",
                method: "POST",
                url: 'SendOffers',
                data: { proId: qrystrkey, ids, gtOfferIds: gtIds },
                getData: function (dataJSON) {
                    if (ids.length == 0) {
                        data = []
                    }
                    else {
                        var data = dataJSON;

                        if (dataJSON.length > 0) {
                            dataJSON
                        }
                        var totalRecords = 0;
                        if (data.length != 0) {
                            totalRecords = data[0].TotalCount;
                        }
                        var cur_page = 1;
                        if (dataJSON.length != 0) {
                            cur_page = dataJSON[0].curpage;
                        }
                    }

                    return { curPage: cur_page, totalRecords: totalRecords, data: data };
                }
            };
            $.ajax({
                processData: true,
                contentType: 'application/json; charset=utf-8',
                type: dataModel.method,
                url: dataModel.url,
                dataType: 'json',
                data: JSON.stringify(dataModel.data),
                success: function (response) {
                    setTimeout(function () {
                        alert("Send offer(s) successfully.");
                    }, 1000);
                    listGTIds = [];
                },
                error: function (err) { }
            });
        }

        // Edit Manufacturer
        $('#ShowPartBomDiv').click(function () {
            if ($('#DivPartBomGrid').css("display") == 'none') {
                $('#DivPartBomGrid').css("display", 'block');
                $('#refreshPrOItem').css("display", 'block');
                $('#divBOMItemButtons').css("display", 'block');
                $('#filterGTOffers').css("display", 'block');
                $('#headingThree').removeClass('headingOneCorner');
                $('#ShowPartBomDiv').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
                $('#head_in_DivPartBom').removeClass('hide_top');

            }
            else {
                $('#DivPartBomGrid').css("display", 'none');
                $('#refreshPrOItem').css("display", 'none');
                $('#divBOMItemButtons').css("display", 'none');
                $('#filterGTOffers').css("display", 'none');
                $('#headingThree').addClass('headingOneCorner');
                $('#ShowPartBomDiv').css("background-image", "url(/Areas/BOM/Images/show.gif)");
                $('#head_in_DivPartBom').addClass('hide_top');
            }
        });

        $('#ShowMatchingGTOffer').click(function () {
            if ($('#divGTOffers').css("display") == 'none') {
                $('#divGTOffers').css("display", 'block');
                $('#refreshGTOffers').css("display", 'block');
                $('#ShowMatchingGTOffer').css("background-image", "url(/Areas/BOM/Images/hide.gif)");
            }
            else {
                $('#divGTOffers').css("display", 'none');
                $('#refreshGTOffers').css("display", 'none');
                $('#ShowMatchingGTOffer').css("background-image", "url(/Areas/BOM/Images/show.gif)");
            }
        });

        $("#editMfr").click(function () {
            var modal = document.getElementById("EditMfrModal");
            modal.style.display = "block"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            if (firstLine != undefined) {
                if (firstLine.ManufacturerNo && firstLine.Manufacturer.trim() != "") {
                    $('#search-manufacturer').css("display", "none");
                    $('#display-manufacturer').css("display", "block");
                    document.getElementById("hiddenEditManufacturer").value = firstLine.ManufacturerNo;
                    $('#lblEditManufacturer').text(firstLine.Manufacturer);
                } else {
                    $('#search-manufacturer').css("display", "block");
                    $('#display-manufacturer').css("display", "none");
                    $('#hiddenEditManufacturer').val("");
                    $('#textEditManufacturer').val("");
                    $('#lblEditManufacturer').text("");
                }
                document.getElementById("lblFileName").textContent = document.getElementById("SourceFile").text;
                $('#lblUploadedRow').text(firstLine.Id);
                $('#lblPartNo').text(firstLine.PartNo);
            }
        });

        $("#EditMfrModal .CancelMfr").click(function () {
            var modal = document.getElementById("EditMfrModal");
            resetEditMfrModal();
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        });

        $("#textEditManufacturer").autocomplete({
            minLength: 2,
            source: function (request, response) {
                // Fetch data
                $.ajax({
                    processData: true,
                    contentType: 'application/json',
                    type: 'GET',
                    url: dropdownUrl + '?action=GetManufacturer&MrfSearch=' + $("#textEditManufacturer").val(),
                    dataType: "json",
                    success: function (data) {
                        autocompleteCount = data.length;
                        response(data);
                    },
                    error: function (err) {
                        console.log("error when fetch manufacturer", err);
                    }
                });
            },
            open: function (event, ui) {
                $("ul").prepend('<li class ="AutoCompleteHeader""><div>' + autocompleteCount + ' result(s)</div></li>');
                autocompleteCount = 0;
            },
            select: function (event, ui) {
                $('#search-manufacturer').css("display", "none");
                $('#display-manufacturer').css("display", "block");
                $('#hiddenEditManufacturer').val(ui.item.value);
                $('#lblEditManufacturer').text(SetCleanText(ui.item.label, true));
            }
        }).autocomplete("instance")._renderItem = function (ul, item) {
            return $("<li>")
                .append("<div>" + SetCleanText(item.label, true) + "</div>")
                .appendTo(ul);
        };
        function SetCleanText(strIn, blnReplaceLineBreaks) {
            if (typeof (strIn) == "undefined") strIn = "";
            strIn = (strIn + "").trim();
            strIn = strIn.replace(/(:PLUS:)/g, "+");
            strIn = strIn.replace(/(:QUOTE:)/g, '"');
            strIn = strIn.replace(/((:AND:)|(&amp;))/g, "&");
            if (blnReplaceLineBreaks) strIn = strIn.replace(/(\n)/g, "<br />");
            return strIn;
        };
        $("#ReselectEditManufacturer").click(function () {
            $('#search-manufacturer').css("display", "block");
            $('#display-manufacturer').css("display", "none");
            $('#hiddenEditManufacturer').val("");
            $('#textEditManufacturer').val("");
            $('#lblEditManufacturer').text("");
        });
        $('.SaveMfr').click(function () {
            if (validateEditMfrForm()) {
                saveEditMfr();
            }
            else {
                console.log("edit manufacturer form is not valid");
                $("#EditManufacturerValidationError").css("display", "block");
            }
        });
        function validateEditMfrForm() {
            var isValid = true;
            if (!$('#hiddenEditManufacturer').val() || $('#lblEditManufacturer').text() == "") {
                $("#RowEditManufacturer").css("background-color", "#990000");
                $("#RowEditManufacturerError").css("display", "block");
                isValid = false;
            } else {
                $("#RowEditManufacturer").css("background-color", "#56954E");
                $("#RowEditManufacturerError").css("display", "none");
            }
            return isValid;
        };

        function saveEditMfr() {
            var qrystr = new URLSearchParams(window.location.search);
            var queryVal = qrystr.get("PROId");
            var mfr = {
                proId: queryVal,
                proLineId: firstLine.ProspectiveOfferLineId,
                manufacturerNo: $("#hiddenEditManufacturer").val()
            }

            $.ajax({
                contentType: 'application/json',
                type: 'POST',
                url: 'UpdateProspectiveOfferManufacturer',
                dataType: "json",
                async: true,
                data: JSON.stringify(mfr),
                success: function (data) {
                    setTimeout(function () {
                        alert("Manufacturer updated successfully.");
                    }, 1000);
                },
                error: function (err) {
                    console.log('Error in edit manufacturer');
                },
                complete: function () {
                    firstLine = undefined;
                    resetEditMfrModal();
                    var modal = document.getElementById("EditMfrModal");
                    modal.style.display = "none"
                    $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
                    refreshPrOItem();
                }
            });
        }
        function resetEditMfrModal() {
            $('#search-manufacturer').css("display", "block");
            $('#display-manufacturer').css("display", "none");
            $('#hiddenEditManufacturer').val("");
            $('#textEditManufacturer').val("");
            $('#lblEditManufacturer').text("");
            $("#RowEditManufacturer").css("background-color", "#56954E");
            $("#RowEditManufacturerError").css("display", "none");
            if (firstLine === undefined){
                $("#editMfr").prop('disabled', true).css('opacity', 0.5);
                $("#editRef").addClass("disable-click");
                disableSendOfferButton();
                unSentOffers = [];
                selectedProspetiveOfferLines = [];
            }
        }

        // Prospective Offer Logs
        function LoadProspectiveOfferLogs() {
            var colModel = [
                {
                    title: "<label>Select All</label><br/><input id='headerCheckbox' type='checkbox' />",
                    cb: { header: true, select: true, all: true },
                    width: "5%",
                    dataIndx: 'checkedColumn',
                    type: 'checkbox',
                    sortable: false,
                    editable: function (ui) {
                        return true;
                    },
                    dataType: 'bool'
                },
                {
                    title: "HUBRFQ No <br> Received Date",
                    width: "14%",
                    dataIndx: "BOMId",
                    render: function (ui) {

                        var dateString = ui.rowData.ReceivedDate.toString();
                        // Extract the timestamp using a regular expression
                        const timestamp = parseInt(dateString.match(/\d+/)[0], 10);

                        // Create a new Date object with the timestamp
                        const date = new Date(timestamp);
                        var receiveDate = date.toLocaleDateString('en-GB');

                        var title = ui.rowData.BOMNo.toString();
                        var htmlstr = "<span title='" + title + "'><a href=\"../../Ord_BOMDetail.aspx?BOM=" + ui.rowData.BOMId.toString() + "\">" + ui.rowData.BOMNo.toString() + "</a></span ><br/>";
                        htmlstr += `<span title='${receiveDate}'>${receiveDate}</span>`
                        return htmlstr;
                    }
                },
                {
                    title: "Customer",
                    width: "10%",
                    dataIndx: "HUBRFQCustomer",
                    /*dataType: "float",*/
                    render: function (ui) {

                        var title = ui.rowData.HUBRFQCustomer.toString();
                        var htmlstr = "<span title='" + title + "'>" + ui.rowData.HUBRFQCustomer.toString() + "</span >";               
                        return htmlstr;
                    }
                },
                {
                    title: "Part No <br>Customer Part No",
                    width: "10%",
                    dataIndx: "PartNo",
                    render: function (ui) {
                        var title = ui.rowData.PartNo.toString();
                        var htmlstr = "<span title='" + title + "'>" + ui.rowData.PartNo.toString() + "</span ><br/>";
                        htmlstr += `<span title='${ui.rowData.CustomerPartNo}'>${ui.rowData.CustomerPartNo}</span>`
                        return htmlstr;
                    }
                },
                {
                    title: "MFR <br>DC",
                    width: "10%",
                    dataIndx: "Manufacturer",
                    render: function (ui) {
                        var title = ui.rowData.Manufacturer.toString();
                        var htmlstr = "<span title='" + title + "'>" + ui.rowData.Manufacturer.toString() + "</span ><br/>";
                        htmlstr += `<span title='${ui.rowData.DateCode}'>${ui.rowData.DateCode}</span>`
                        return htmlstr;
                    }
                },
                {
                    title: "IHS Avg Price <br>Lytica Avg Price",
                    width: "10%",
                    dataIndx: "IHSAvgPrice",
                    render: function (ui) {
                        var title = ui.rowData.IHSAvgPrice.toString();
                        var htmlstr = "<span title='" + title + "'>" + ui.rowData.IHSAvgPrice.toString() + "</span ><br/>";
                        htmlstr += `<span title='${ui.rowData.LyticaAvgPrice}'>${ui.rowData.LyticaAvgPrice}</span>`
                        return htmlstr;
                    }
                },
                {
                    title: "Product <br>Package",
                    width: "10%",
                    dataIndx: "ProductName",
                    render: function (ui) {
                        var title = ui.rowData.ProductName.toString();
                        var htmlstr = "<span title='" + title + "'>" + ui.rowData.ProductName.toString() + "</span ><br/>";
                        htmlstr += `<span title='${ui.rowData.PackageName}'>${ui.rowData.PackageName}</span>`
                        return htmlstr;
                    }
                },
                {
                    title: "Prospective Offer Price (Supplier Offer Price) <br>Qty",
                    width: "10%",
                    dataIndx: "ProductName",
                    render: function (ui) {
                        let title = "";
                        let htmlstr = "";
                        if (ui.rowData.NewOfferPriceFromProspective != null) {
                            title = parseFloat(ui.rowData.NewOfferPriceFromProspective).toFixed(5).toString() + " " + ui.rowData.Currency.toString() 
                            + " (" + parseFloat(ui.rowData.Price).toFixed(5).toString() + " " + ui.rowData.Currency.toString() + ")";
                            htmlstr = "<span title='" + title + "'>" + title + "</span ><br/>";
                            htmlstr += `<span title='${ui.rowData.Quantity}'>${ui.rowData.Quantity}</span>`;
                            return htmlstr;
                        } 
                        else {
                            title = parseFloat(ui.rowData.Price).toFixed(5).toString() + " " + ui.rowData.Currency.toString() 
                            + " (" + parseFloat(ui.rowData.Price).toFixed(5).toString() + " " + ui.rowData.Currency.toString() + ")";
                            htmlstr = "<span title='" + title + "'>" + title + "</span ><br/>";
                            htmlstr += `<span title='${ui.rowData.Quantity}'>${ui.rowData.Quantity}</span>`;
                            return htmlstr;
                        }
                    }
                },
                {
                    title: "Salesperson",
                    width: "10%",
                    dataIndx: "SalesPerson",
                    render: function (ui) {

                        var title = ui.rowData.SalesPerson.toString();
                        var htmlstr = "<span title='" + title + "'>" + ui.rowData.SalesPerson.toString() + "</span >";
                        return htmlstr;
                    }
                },
                {
                    title: "HUBRFQ Created?",
                    width: "10%",
                    dataIndx: "SalesPerson",
                    render: function (ui) {
                        if (ui.rowData.HUBRFQCreatedNo == 0)
                            return ""
                        var title = ui.rowData.HUBRFQCreatedNo.toString();
                        var htmlstr = "<span title='" + title + "'><a href=\"../../Ord_BOMDetail.aspx?BOM=" + ui.rowData.HUBRFQCreatedId.toString() + "\">" + ui.rowData.HUBRFQCreatedNo.toString() + "</a></span >";
                        return htmlstr;
                    }
                },
            ];
            var lineID = firstLine.ProspectiveOfferLineId;
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "GET",
                //recIndx: "FElectronicsId",
                url: 'GetProspectiveOffersLogs?prospectiveOfferLineId=' + lineID,
                getData: function (dataJSON) {
                    var headerJson = dataJSON.DataLine;
                    $('#lblSourceFileName').text(headerJson.SourceFileName);
                    $('#lblUploadedRowLogs').text(headerJson.ImportRowCount);
                    $('#lblPartNumber').text(headerJson.PartNo);

                    dataJSON = dataJSON.DataLogs;
                    APIGridFirstLoad = false;
                    var data = dataJSON;
                    var totalRecords = dataJSON.length;

                    var cur_page = 1;
                    return { curPage: cur_page, totalRecords: totalRecords, data: dataJSON };
                }
            };
            var grid1 = $("#grid_OfferHistory").pqGrid({
                width: "auto", height: 261,
                selectionModel: { type: null, native: true, all: true },
                dataModel: dataModel,
                rowHt: 35,
                hwrap: false,
                wrap: false,
                hoverMode: 'cell',
                colModel: colModel,
                complete: function (event, ui) {
                },
                editable: false,
                postRenderInterval: -1,
                numberCell: { show: false },
                rowSelect: function (evt, ui) {
                    if (ui.addList[0] != undefined) {
                        if (ui.deleteList[0] != undefined) {
                            let dels = ui.deleteList.map(x => x.rowData.CustomerReqId);
                            ReqlogIds = ReqlogIds.filter(x => !dels.includes(x));
                            ReqlogIds = ReqlogIds.concat(ui.addList.map(x => x.rowData.CustomerReqId));
                        }
                        else {
                            ReqlogIds = ReqlogIds.concat(ui.addList.map(x => x.rowData.CustomerReqId));
                        }
                        if (ReqlogIds.length > 0) {
                            LoadGTOffersLogSentDate(firstLine.ProspectiveOfferLineId, ReqlogIds);
                            $("#btnRemindSalespersonOfferHistory").prop('disabled', false).css('opacity', 1);
                            $("#btnRemindSalespersonOfferHistory").removeClass("disable-click");

                        }
                    }
                    else {
                        if (ui.deleteList[0] != undefined) {
                            let dels = ui.deleteList.map(x => x.rowData.CustomerReqId);
                            ReqlogIds = ReqlogIds.filter(x => !dels.includes(x));
                            ReqlogIds = ReqlogIds.concat(ui.addList.map(x => x.rowData.CustomerReqId));
                            if (ReqlogIds.length == 0) {
                                $("#btnRemindSalespersonOfferHistory").prop('disabled', true).css('opacity', 0.5);
                                $("#btnRemindSalespersonOfferHistory").addClass("disable-click");

                                LoadGTOffersLogSentDate(firstLine.ProspectiveOfferLineId, [0]);

                            }
                            else {
                                LoadGTOffersLogSentDate(firstLine.ProspectiveOfferLineId, ReqlogIds);

                            }
                        }
                    }
                }
            })
            grid1.pqGrid('refreshDataAndView');
        }
        $("#btnOfferHistory").click(function () {
            LoadProspectiveOfferLogs();
            LoadGTOffersLogSentDate(0, 0);
            var modal = document.getElementById("OfferHistoryModal");
            //AddOfferDropDownBind();
            modal.style.display = "block"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        })

        function LoadGTOffersLogSentDate(ProspectiveOfferLineId, CustomerRequirementIds) {
            var colModel = [
                {
                    title: "Manufacturer",
                    width: "30%",
                    dataIndx: "Manufacturer",
                },
                {
                    title: "Part",
                    width: "30%",
                    dataIndx: "PartNo",
                    /*dataType: "float",*/
                },
                {
                    title: "Send Date Time",
                    width: "39%",
                    dataIndx: "SentDate",
                    dataType: 'date',
                    format: 'dd/MM/yyyy',
                    render: function (ui) {
                        var dateString = ui.rowData.SentDate.toString();
                        // Extract the timestamp using a regular expression
                        const timestamp = parseInt(dateString.match(/\d+/)[0], 10);

                        // Create a new Date object with the timestamp
                        const date = new Date(timestamp);
                        return date.toLocaleDateString('en-GB', {
                            day: '2-digit',
                            month: '2-digit',
                            year: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit',
                            second: '2-digit'
                        });
                    }
                }
            ];
            var dataSales = { prospectiveOfferLineId: ProspectiveOfferLineId, customerRequirementIds: CustomerRequirementIds }
            var dataModel = {
                location: "remote",
                dataType: "JSON",
                method: "POST",
                //recIndx: "FElectronicsId",
                url: 'GetProspectiveOffersLogsSentDate',
                postData: function () {
                    return dataSales;
                },
                getData: function (dataJSON) {
                    var totalRecords = dataJSON.length;
                    var cur_page = 1;
                    return { curPage: cur_page, totalRecords: totalRecords, data: dataJSON };
                }
            };
            var grid1 = $("#grid_DetailLog").pqGrid({
                width: "auto", height: 261,
                selectionModel: { type: null, native: true },
                dataModel: dataModel,
                rowHt: 35,
                hwrap: false,
                wrap: false,
                hoverMode: 'cell',
                colModel: colModel,
                complete: function (event, ui) {

                },
                editable: false,
                postRenderInterval: -1, //synchronous post rendering.;

                numberCell: { show: false },
                rowSelect: function (evt, ui) {
                }
            })
            //if (refreshGrid) {
            grid1.pqGrid('refreshDataAndView');
        }

        $("#btnCancelOfferHistory").click(function () {
            var modal = document.getElementById("OfferHistoryModal");
            modal.style.display = "none";
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        });

        $('#btnRemindSalespersonOfferHistory').click(function () {
            var grid = $('#grid_OfferHistory').pqGrid('instance');
            if (grid.Checkbox('checkedColumn').getCheckedNodes().length > 0) {
                grid.Checkbox('checkedColumn').getCheckedNodes().forEach(function (rd) {
                    listGTIds_logs.push({
                        CustomerReqId: rd.CustomerReqId,
                        ProspectiveOfferLineId: rd.ProspectiveOfferLineId
                    });
                });

                if (listGTIds_logs.length > 20) {
                    alert("The selected Offers should not exceed 20 rows. Please re-select your options before sending the Offers.")
                    listGTIds_logs = [];
                    return;
                }
                var qrystr = new URLSearchParams(window.location.search);
                var qrystrkey = qrystr.get("PROId");
                if (!confirm("Are you sure would like to remind the selected offers to Salesperson")) {
                    return
                }
                if (listGTIds_logs.length == 0) {
                    alert('Please select a row to remind offers.');
                    return;
                }
                sendGTOffers(qrystrkey, lineIds, listGTIds_logs);

            };

        });

        //Send Offer Modal
        function LoadSendOfferModal() {
            $('#GTOfferSource').css("display", "block");
            var modal = document.getElementById("SendOfferModal");
            modal.style.display = "block"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
            LoadSendGTOfferData();
        }

        $('.CancelSendOffers').click(function () {
            deSelectedOffers();
            closeSendOfferModal();
            $("#grid_SB").pqGrid("instance");
            $("#grid_SB").pqGrid('destroy');
        });

        function LoadSendGTOfferData() {
            var grid = $("#grid_SB").pqGrid("instance")
            if (grid != null) $("#grid_SB").pqGrid('refreshDataAndView');
            else {
                var colModel = [];
                colModel = [
                    {
                        title: "<label>Select All</label><br/><input id='headerCheckbox' type='checkbox' />",
                        cb: { header: true, select: true, all: true },
                        width: "5%",
                        dataIndx: 'checkedColumn',
                        type: 'checkbox',
                        sortable: false,
                        editor: { select: false },
                        editable: true,
                        dataType: 'bool'
                    },
                    {
                        title: "<span title='Row No From Uploaded Sheet' style='white-space:normal'>Row No From Uploaded Sheet</span>",
                        width: "5%",
                        dataIndx: "LineNumber",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 0) {
                                return ui.rowData.Id.toString();
                            }
                            else
                                return '';
                        },
                        editable: false
                    },
                    {
                        title: "Part No",
                        width: "10%",
                        dataIndx: "PartNo",
                        render: function (ui) {
                            var htmlstr = "<span>" + ui.rowData.PartNo.toString() + "</span ><br/>";
                            return htmlstr;
                        },
                        editable: false
                    },
                    {
                        title: "Customer (REQ Date)",
                        width: "15%",
                        dataIndx: "HUBRFQCustomer",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                var title = ui.rowData.HUBRFQCustomer.toString();
                                var htmlstr = "<span title='" + title + "'><a href=\"../../Ord_BOMDetail.aspx?BOM=" + ui.rowData.BOMNo.toString() + "\">" + ui.rowData.HUBRFQCustomer.toString() + "</a></span ><br/>";
                                return htmlstr;
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    },
                    {
                        title: "Quantity Offered",
                        width: "5%",
                        dataIndx: "QuantityOffered",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                var htmlstr = "<span>" + ui.rowData.QuantityOffered.toString() + "</span ><br/>";
                                return htmlstr;
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    },
                    {
                        title: "<span title='Prospective Offer Price (Supplier Offer Price)' style='white-space:normal'>Prospective Offer Price (Supplier Offer Price)</span>",
                        width: "15%",
                        dataIndx: "NewOfferPriceFromProspective",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                if (ui.rowData.NewOfferPriceFromProspective != null) {
                                    return "<span>" + parseFloat(ui.rowData.NewOfferPriceFromProspective).toFixed(5).toString() + " " + ui.rowData.Currency.toString()
                                        + " (" + parseFloat(ui.rowData.UploadedOfferPrice).toFixed(5).toString() + " " + ui.rowData.Currency.toString() + ")</span ><br/>";
                                } else {
                                    return "<span>" + parseFloat(ui.rowData.ProspectiveOfferPrice).toFixed(5).toString() + " " + ui.rowData.Currency.toString()
                                        + " (" + parseFloat(ui.rowData.UploadedOfferPrice).toFixed(5).toString() + " " + ui.rowData.Currency.toString() + ")</span ><br/>";
                                }
                            } else {
                                return '';
                            }
                        },
                        editModel: { clicksToEdit: 1, keyUpDown: true },
                        editor: {
                            select: true, // Select the value when editing starts
                            type: "textbox",
                            attr: "type='float'",
                            init: function (ui) {
                                const cellValue = ui.$cell.find("input").val() == "" ? ui.rowData.ProspectiveOfferPrice.toFixed(5) : parseFloat(ui.$cell.find("input").val()).toFixed(5); // Get the current cell value
                                ui.$cell.find("input").val(cellValue); // Ensure the input field is pre-filled with the value
                                ui.$cell.find("input").css({
                                    "height": "27px",
                                    "font-size": "12px"
                                });
                            },
                        },
                        validations: [{ type: 'gte', value: 0, msg: "should be >= 0" }],
                    },
                    {
                        title: "IHS Avg Price",
                        width: "10%",
                        dataIndx: "IHSAvgPrice",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                var htmlstr = "<span>" + ui.rowData.IHSAvgPrice.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                                return htmlstr;
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    },
                    {
                        title: "Lytica Avg Price",
                        width: "10%",
                        dataIndx: "LyticaAvgPrice",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                var htmlstr = "<span>" + ui.rowData.LyticaAvgPrice.toFixed(5).toString() + " " + ui.rowData.Currency.toString() + "</span ><br/>";
                                return htmlstr;
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    },
                    {
                        title: "Quote Price + QTY",
                        width: "10%",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                return "<span>" + ui.rowData.QuotePrice.toFixed(5).toString() + ' ' + ui.rowData.Currency.toString() + ' ' + ui.rowData.QuoteQTY.toString() + ' QTY' + "</span ><br/>";
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    },
                    {
                        title: "<span title='SO Price + QTY + Date'>SO Price + QTY + Date</span>",
                        width: "15%",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                let date = "";
                                if (ui.rowData.SODate != null) {
                                    const timestampMatch = ui.rowData.SODate.match(/\/Date\((\d+)\)\//);
                                    if (timestampMatch) {
                                        const timestamp = parseInt(timestampMatch[1], 10);
                                        const parsedDate = new Date(timestamp);
                                        date = parsedDate.toLocaleDateString('en-GB'); // Format as DD/MM/YYYY
                                    }
                                }
                                return "<span>" + ui.rowData.SOPrice.toFixed(5).toString() + ' ' + ui.rowData.Currency.toString() + ' ' + ui.rowData.SOQTY.toString() + ' QTY' + " " + date + "</span ><br/>";
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    },
                    {
                        title: "<span title='SO line order value'>SO line order value</span>",
                        width: "10%",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                return "<span>" + ui.rowData.SOLineValue.toFixed(5).toString() + ' ' + ui.rowData.Currency.toString() + "</span ><br/>";
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    },
                    {
                        title: "Manufacturer",
                        width: "10%",
                        dataIndx: "Manufacturer",
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                var htmlstr = "<span>" + ui.rowData.Manufacturer.toString() + "</span ><br/>";
                                return htmlstr;
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    },
                    {
                        title: "Salesperson",
                        width: "10%",
                        dataIndx: "FileShared",
                        dataType: 'string',
                        render: function (ui) {
                            if (ui.rowData.IsFromGT == 1) {
                                return "<span>" + ui.rowData.FileShared.toString() + "</span ><br/>";
                            }
                            else {
                                return '';
                            }
                        },
                        editable: false
                    }
                ];

                var qrystr = new URLSearchParams(window.location.search);
                var BomManagerID = qrystr.get("BOM");
                var lstMergeCells = []
                handleUnsentOffers();

                var grid1 = $("#grid_SB").pqGrid({
                    width: "auto",
                    height: 340,
                    editModel: { onBlur: 'save' },
                    selectionModel: { type: null, native: true },
                    dataModel: { data: unSentOffers },
                    colModel: colModel,
                    postRenderInterval: -1,
                    numberCell: { show: false },
                    complete: function (evt, ui) {
                        $('#headerCheckbox').change(function () {
                            FormatFinalDataForSendOffers();
                        });
                        FormatFinalDataForSendOffers();
                    }
                });
                var grid1 = $('#grid_SB').pqGrid('instance');
                grid1.on('scroll', function (event, ui) {
                    FormatFinalDataForSendOffers();
                    // Fetch and display additional rows here based on startRowIndex and visibleRows
                    // Example: Fetch additional data and update the grid
                });
            }
        }
        function FormatFinalDataForSendOffers() {
            var grid1 = $('#grid_SB').pqGrid('instance');
            var rowcount = grid1.getTotalRows();
            for (var i = 0; i < rowcount; i++) {
                var headerflag = grid1.getRowData({ rowIndx: i }).IsFromGT;
                if (headerflag == 0) {
                    try {
                        grid1.getCell({ rowIndx: i, dataIndx: "checkedColumn" })[0].children[0].children[0].remove();
                    } catch (e) {

                    }
                    grid1.addClass({ rowIndx: i, cls: 'highlighted-row' });
                    //grid1("addClass", { rowIndx: i, cls: 'highlighted-row' });
                }

            }
        }
        function handleUnsentOffers() {
            // Filter unSentOffers based on SentProspectiveOfferAt and Id
            unSentOffers = unSentOffers.filter(x => (!x.IsProspectiveSent || !x.SentProspectiveOfferAt) || x.Id);

            // Sort unSentOffers by ProspectiveOfferLineId (ascending) and Id (descending)
            unSentOffers.sort((a, b) =>
                a.ProspectiveOfferLineId - b.ProspectiveOfferLineId || b.Id - a.Id
            );

            // Count occurrences of each ProspectiveOfferLineId
            const countByProspectiveOfferLineId = unSentOffers.reduce((acc, { ProspectiveOfferLineId }) => {
                acc[ProspectiveOfferLineId] = (acc[ProspectiveOfferLineId] || 0) + 1;
                return acc;
            }, {});

            // Filter unSentOffers to include only those with more than one occurrence of ProspectiveOfferLineId
            unSentOffers = unSentOffers.filter(x => countByProspectiveOfferLineId[x.ProspectiveOfferLineId] > 1);
            unSentOffers.forEach(obj => {
                if (obj.Id !== 0) {
                    obj.pq_rowselect = false;
                }
            });
        }
        $('.ConfirmSendOffers').click(function () {
            var grid = $('#grid_SB').pqGrid('instance');
            if (grid.Checkbox('checkedColumn').getCheckedNodes().length > 0) {
                grid.Checkbox('checkedColumn').getCheckedNodes().forEach(function (rd) {
                    listGTIds.push({
                        CustomerReqId: rd.CustomerReqId,
                        ProspectiveOfferLineId: rd.ProspectiveOfferLineId,
                        NewOfferPriceFromProspective: rd.NewOfferPriceFromProspective
                    });
                });

                if (listGTIds.length > 20) {
                    alert("The selected Offers should not exceed 20 rows. Please re-select your options before sending the Offers.")
                    listGTIds = [];
                    return;
                }
                var qrystr = new URLSearchParams(window.location.search);
                var qrystrkey = qrystr.get("PROId");
                if (listGTIds.length > 0) {
                    sendGTOffers(qrystrkey, lineIds, listGTIds);
                }
                else {
                    alert('Please select row.');
                }
            };
            closeSendOfferModal();
            disableSendOfferButton();
            refreshGTOffers();
            $("#grid_SB").pqGrid("instance");
            $("#grid_SB").pqGrid('destroy');
        });

        function deSelectedOffers() {
            var grid = $("#grid_SB").pqGrid("instance");
            if (grid == undefined) {
                return;
            }
            else {
                grid.setSelection(null);
            }
        };

        function closeSendOfferModal() {
            var modal = document.getElementById("SendOfferModal");
            modal.style.display = "none"
            $('html, body', window.parent.document).animate({ scrollTop: 240 }, "fast");
        };

        function disableSendOfferButton() {
            $("#sendGTOffers").prop('disabled', true).css('opacity', 0.5);
            $("#sendGTOffers").addClass("disable-click");
        };

        function enableSendOfferButton() {
            $("#sendGTOffers").prop('disabled', false).css('opacity', 1);
            $("#sendGTOffers").removeClass("disable-click");
        };
    </script>
</body>
</html>
