///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     changed by      date         Remarks

//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.initializeBase(this, [element]);
    this._intNewID = 0;
    this._intCompanyID = 0;
    this._strCompanyName = "";
    this._strOriginalFilename = "";
    this._strGeneratedFilename = "";
    this._IsRecord = false;
    this._strGeneratedID = "";
    this._BomCompanyNo = 0;
    this._inActive = false;
    this._hasZeroAnswerId = false;
    this._firstTimeUpload = true;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.prototype = {

    get_intCompanyID: function () { return this._intCompanyID; }, set_intCompanyID: function (v) { if (this._intCompanyID !== v) this._intCompanyID = v; },
    get_strCompanyName: function () { return this._strCompanyName; }, set_strCompanyName: function (v) { if (this._strCompanyName !== v) this._strCompanyName = v; },
    get_tblPVVBOM: function () { return this._tblPVVBOM; }, set_tblPVVBOM: function (value) { if (this._tblPVVBOM !== value) this._tblPVVBOM = value; },
    get_ibtnEditPPV: function () { return this._ibtnEditPPV; }, set_ibtnEditPPV: function (value) { if (this._ibtnEditPPV !== value) this._ibtnEditPPV = value; },
    get_ibtnDeletePPV: function () { return this._ibtnDeletePPV; }, set_ibtnDeletePPV: function (value) { if (this._ibtnDeletePPV !== value) this._ibtnDeletePPV = value; },
    get_ibtnViewPPV: function () { return this._ibtnViewPPV; }, set_ibtnViewPPV: function (value) { if (this._ibtnViewPPV !== value) this._ibtnViewPPV = value; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.callBaseMethod(this, "initialize");
        this.addCancel(Function.createDelegate(this, this.cancelClicked));
        this.addSave(Function.createDelegate(this, this.saveClicked));
        this.addShown(Function.createDelegate(this, this.formShown));
        document.getElementById("removeUploadedFile").addEventListener("click", Function.createDelegate(this, function (event) {
            event.preventDefault();
            this.removeFile();
        }));
    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intNewID = null;
        this._strGeneratedID = null;
        this._intCompanyID = null;
        this._strCompanyName = null;
        this._strOriginalFilename = null;
        this._strGeneratedFilename = null;
        if (this._tblPVVBOM) this._tblPVVBOM.dispose();
        this._tblPVVBOM = null;
        this._BomCompanyNo = null;

        this._ibtnDeletePPV = null;
        this._ibtnEditPPV = null;
        this._ibtnViewPPV = null;
        this._IsRecord = null;
        this._inActive = null;            ;
        this._firstTimeUpload = null;

        Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.callBaseMethod(this, "dispose");
    },

    cancelClicked: function () {
        $R_FN.navigateBack();
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            //register for ctlSendMailMessage
            this._ctlMail = $find(this.getField("ctlSendMailMessage").ID);
            this._ctlMail._ctlRelatedForm = this;
        }
        // $find(this.getField("ctlSalesman").ControlID).addChanged(Function.createDelegate(this, this.getSalesman));
        if (this._intCompanyID > 0) {  //Worked for selected Company id with code.
            this.setFieldValue("ctlCompany", this._intCompanyID, null, $R_FN.setCleanTextValue(this._strCompanyName));
            this.getContact();
        }
        else {  //Normal BOM Add          
            if ($find(this.getField("ctlCompany").ControlID)) $find(this.getField("ctlCompany").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getContact));
        }
        this.getFieldDropDownData("ctlCurrency");
        this.getFieldDropDownData("ctlSalesman");
        this.getPPVData();

        if (this._strGeneratedFilename) {
            this.showPurchaseHubFields(true);
        } else {
            this.showPurchaseHubFields(false);
        }
    },
    getPPVData: function () {
        //this.getData_Start();
        //this.showContent(true);
        // this.showCreditHistoryError(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMPVV");
        obj.set_DataObject("BOMPVV");
        obj.set_DataAction("GetDataTemp");
        obj.addParameter("idGenerated", this._strGeneratedID);
        obj.addDataOK(Function.createDelegate(this, this.getPPVDataOK));
        obj.addError(Function.createDelegate(this, this.getPPVDataError));
        obj.addTimeout(Function.createDelegate(this, this.getPPVDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getPPVDataOK: function (args) {

        var res = args._result;
        var aryData, row;
        this._tblPVVBOM.clearTable();
        //this.showLoading(false);
        if (res.Items) {
            
            for (var i = 0; i < res.Items.length; i++) {
                var row = res.Items[i];
                
                this._BomCompanyNo = row.PVVAnswerId;
                aryData = [
                    $R_FN.setCleanTextValue(row.PVVQuestionName),
                    $R_FN.setCleanTextValue(row.PVVAnswerName)

                ];

                this._tblPVVBOM.addRow(aryData, row.ID, false);

                row = null; aryData = null;
                this._tblPVVBOM.resizeColumns();

            }

            if (res.Items.length > 0) {
                this._IsRecord = true;

            }
            else {
                this._IsRecord = false;

            }
            this._hasZeroAnswerId = res.Items.some(item => item.PVVAnswerName === "");

        }

        if (this._BomCompanyNo == 0) {

            this._inActive = false
        }
        else {
            this._inActive = true;
        }
        this.enableButtons(true);


    },
    enableButtons: function (bln) {
        if (bln) {
            if (this._ibtnEditPPV) $R_IBTN.enableButton(this._ibtnEditPPV, true);
            if (this._ibtnDeletePPV) $R_IBTN.enableButton(this._ibtnDeletePPV, bln && this._IsRecord && this._inActive);
            if (this.get_ibtnViewPPV) $R_IBTN.enableButton(this._ibtnViewPPV, bln && this._IsRecord);
        } else {
            if (this.get_ibtnEditPPV) $R_IBTN.enableButton(this._ibtnEditPPV, false);
            if (this.get_ibtnDeletePPV) $R_IBTN.enableButton(this._ibtnDeletePPV, false);
            if (this._ibtnViewPPV) $R_IBTN.enableButton(this._ibtnViewPPV, false);


        }

    },
    getPPVDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },


    getContact: function () {
        this.getSOData();
        this.getFieldControl("ctlContact")._intCompanyID = this.getFieldValue("ctlCompany");
        this.getFieldDropDownData("ctlContact");
    },
    getSOData: function () {
        //this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanySalesInfo");
        obj.set_DataObject("CompanySalesInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this.getFieldValue("ctlCompany"));
        obj.addDataOK(Function.createDelegate(this, this.getSODataOK));
        obj.addError(Function.createDelegate(this, this.getSODataError));
        obj.addTimeout(Function.createDelegate(this, this.getSODataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getSODataOK: function (args) {
        var res = args._result;
        //  alert(res.IsTraceability);
        // alert(res.CurrencyNo);
        this.setFieldValue("ctlCurrency", res.CurrencyNo);
        this.setFieldValue("ctlContact", res.ContactNo);
        this.setFieldValue("ctlAS9120", res.IsTraceability);
        //this.getDataOK_End();
    },

    getSODataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    saveClicked: function () {
        if (!this.validateForm()) return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMAdd");
        obj.set_DataObject("BOMAdd");
        obj.set_DataAction("AddNew");
        obj.addParameter("Name", this.getFieldValue("ctlName"));
        obj.addParameter("Company", this.getFieldValue("ctlCompany"));
        obj.addParameter("Contact", this.getFieldValue("ctlContact"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        //[001] code start
        //obj.addParameter("Customer", this.getFieldValue("ctlCustomer"));
        //[001] code end
        obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
        obj.addParameter("CurrentSupplier", this.getFieldValue("ctlCurrentSupplier"));
        obj.addParameter("QuoteRequired", this.getFieldValue("ctlQuoteRequired"));
        obj.addParameter("AS9120", this.getFieldValue("ctlAS9120"));
        obj.addParameter("Contact2", this.getFieldValue("ctlSalesman"));
        obj.addParameter("OriginalFilename", this._strOriginalFilename);
        obj.addParameter("GeneratedFilename", this._strGeneratedFilename);
        obj.addParameter("GeneratedBomID", this._strGeneratedID);
        obj.addParameter("AssignUserNo", this.getFieldValue("ctlBuyer"));
        let selectedCompanyName = this.stripCurrency($find(this.getField("ctlCompany").ControlID)._strSelectedText);
        obj.addParameter("CompanyName", selectedCompanyName);
        obj.addParameter("aryRecipientLoginIDs", $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));

        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {

        if (args._result.Result) {
            if (args._result.NewID > 0) {
                this._intNewID = args._result.NewID;
                this.showSavedOK(true);
                this.onSaveComplete();
            }
        } else {
            this.showError(true, args._result.ValidationMessage);
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        //if bom file uploaded
        if (this._strGeneratedFilename) {
            if (!this.checkFieldEntered('ctlBuyer')) {
                blnOK = false;
                this.setFieldInError("ctlBuyer", true, $R_RES.RequiredFieldMissingMessage);
            }
            if (this._hasZeroAnswerId) {
                blnOK = false;
                this.showError(true, "Please make sure to answer all the PPV/BOM Qualification questions.")
            }
        }
        return blnOK;
    },

    //getSalesman: function () {
    //    this.showSalesmanFieldsLoading(true);
    //    if (!this.checkFieldEntered("ctlSalesman")) {
    //        this.getSalesmanError();
    //        return;
    //    }
    //    var obj = new Rebound.GlobalTrader.Site.Data();
    //    obj.set_PathToData("controls/SetupNuggets/SecurityUsers");
    //    obj.set_DataObject("SecurityUsers");
    //    obj.set_DataAction("GetItem");
    //    obj.addParameter("ID", this.getFieldValue("ctlSalesman"));
    //    obj.addDataOK(Function.createDelegate(this, this.getSalesmanOK));
    //    obj.addError(Function.createDelegate(this, this.getSalesmanError));
    //    obj.addTimeout(Function.createDelegate(this, this.getSalesmanError));
    //    $R_DQ.addToQueue(obj);
    //    $R_DQ.processQueue();
    //    obj = null;
    //},

    //getSalesmanOK: function (args) {
    //    var res = args._result;
    //    this.setFieldValue("ctlDivision", res.DivisionNo);
    //    this.getFieldDropDownData("ctlDivision");
    //    this.setFieldValue("ctlDivision_Label", $R_FN.setCleanTextValue(res.Division));
    //    this.showSalesmanFieldsLoading(false);
    //},

    //getSalesmanError: function (args) {
    //    this.showSalesmanFieldsLoading(false);
    //    this._strErrorMessage = args._errorMessage;
    //    this.onSaveError();
    //}

    importExcelData: function (originalFilename, generatedFilename) {
        this._strOriginalFilename = originalFilename;
        this._strGeneratedFilename = generatedFilename;
        this.showFileProcessing(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj._intTimeoutMilliseconds = 200000;
        obj.set_PathToData("controls/Nuggets/UtilityBOMImport");
        obj.set_DataObject("UtilityBOMImport");
        obj.set_DataAction("ImportData");
        obj.addParameter("originalFilename", originalFilename);
        obj.addParameter("generatedFilename", generatedFilename);
        obj.addParameter("ClientId", $('#ctl00_ddlClientByMaster_ddl').val());
        obj.addParameter("ColumnHeader", "YES");
        obj.addDataOK(Function.createDelegate(this, this.importExcelDataOK));
        obj.addError(Function.createDelegate(this, this.importExcelDataError));
        obj.addTimeout(Function.createDelegate(this, this.importExcelDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    importExcelDataOK: function (args) {
        $('#uploadedFileName').text(this._strOriginalFilename);
        $('#uploadedFile').css("display", "block");
        var IsLimitExceeded = args._result.IsLimitExceeded;
        let message = IsLimitExceeded ? args._result.LimitErrorMessage : "BOM Import file upload successfully!";
        alert(message);
        this.showFileProcessing(false);
        //When BOM file upload => HUBRFQ create with status = RFQ, which requires Buyer/CC
        this.showPurchaseHubFields(true);
    },
    importExcelDataError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        $("#excelipload").prop('disabled', false).css('opacity', 5.5);
        $('input:file').filter(function () {
            return this.files.length == 0
        }).prop('disabled', false);
        this.showFileProcessing(false);
        this._strOriginalFilename = null;
        this._strGeneratedFilename = null;
    },
    removeFile: function () {
        let isConfirmed = confirm("Are you sure to remove this file?");
        if (!isConfirmed) return;
        if (!this._strGeneratedFilename) return;
        this.showFileProcessing(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj._intTimeoutMilliseconds = 200000;
        obj.set_PathToData("controls/Nuggets/UtilityBOMImport");
        obj.set_DataObject("UtilityBOMImport");
        obj.set_DataAction("RemoveFile");
        obj.addParameter("generatedFilename", this._strGeneratedFilename);
        obj.addParameter("ClientId", $('#ctl00_ddlClientByMaster_ddl').val());
        obj.addDataOK(Function.createDelegate(this, this.removeFileOK));
        obj.addError(Function.createDelegate(this, this.removeFileError));
        obj.addTimeout(Function.createDelegate(this, this.removeFileError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    removeFileOK: function (args) {
        if (args._result.Status) {
            $("#excelipload").prop('disabled', false).css('opacity', 5.5);
            $('input:file').filter(function () {
                return this.files.length == 0
            }).prop('disabled', false);
            this._strOriginalFilename = null;
            this._strGeneratedFilename = null;
            $('#uploadedFileName').text("");
            $('#uploadedFile').css("display", "none");

            alert("File remove successfully!")
        }
        this.showFileProcessing(false);
        this.showPurchaseHubFields(false);
    },
    removeFileError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        this.showFileProcessing(false);
    },

    showPurchaseHubFields: function (isShow) {
        if (isShow && this._firstTimeUpload) {
            $('#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ctlBuyer_tdTitle').append('<span class="requiredField">*</span>');
            this.getFieldDropDownData("ctlBuyer");
            this._firstTimeUpload = false;
        }
        this.showFormField("ctlBuyer", isShow);
        this.showFormField("ctlSendMailMessage", isShow);
    },

    stripCurrency: function (input) {
        if (!input) return "";
        let lastParenIndex = input.lastIndexOf('(');
        return lastParenIndex > 0 ? input.substring(0, lastParenIndex).trim() : input;
    },

    showFileProcessing: function (isProcessing) {
        if (isProcessing) {
            $R_IBTN.enableButton(this._ibtnSave, false);
            $R_IBTN.enableButton(this._ibtnSave_Footer, false);
            $('#divLoader').show();
        } else {
            $R_IBTN.enableButton(this._ibtnSave, true);
            $R_IBTN.enableButton(this._ibtnSave_Footer, true);
            $('#divLoader').hide();
        }
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
