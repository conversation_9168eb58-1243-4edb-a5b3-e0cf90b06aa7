﻿using System;
using System.Collections;
using System.Data;
using System.Linq;
using System.Web;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Xml.Linq;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    /// <summary>
    /// Summary description for $codebehindclassname$
    /// </summary>
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class Completed : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("Completed");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData))
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "1");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "Completed"));
                jsnList.AddVariable(jsnItem);

                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "2");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "NotCompleted"));
                jsnList.AddVariable(jsnItem);

                jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", "3");
                jsnItem.AddVariable("Name", Functions.GetGlobalResource("misc", "All"));
                jsnList.AddVariable(jsnItem);



                jsn.AddVariable("Items", jsnList);
                jsnItem.Dispose(); jsnItem = null;
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            else
            {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
