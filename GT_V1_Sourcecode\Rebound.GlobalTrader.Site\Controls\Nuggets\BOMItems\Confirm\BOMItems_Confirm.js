Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.initializeBase(this,[n]);this._intRequirementLineID=-1;this._intBOMID=-1;this._CustReqNo=-1;this._ReqSalesman=-1;this._SupportTeamMemberNo=null;this._UnitBuyPrice=null;this._UnitSellPrice=null};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.prototype={get_BomCode:function(){return this._BomCode},set_BomCode:function(n){this._BomCode!==n&&(this._BomCode=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},get_BomCompanyName:function(){return this._BomCompanyName},set_BomCompanyName:function(n){this._BomCompanyName!==n&&(this._BomCompanyName=n)},get_BomCompanyNo:function(){return this._BomCompanyNo},set_BomCompanyNo:function(n){this._BomCompanyNo!==n&&(this._BomCompanyNo=n)},get_SalesManNo:function(){return this._SalesManNo},set_SalesManNo:function(n){this._SalesManNo!==n&&(this._SalesManNo=n)},get_SalesManName:function(){return this._SalesManName},set_SalesManName:function(n){this._SalesManName!==n&&(this._SalesManName=n)},get_tblAllReleasedetails:function(){return this._tblAllReleasedetails},set_tblAllReleasedetails:function(n){this._tblAllReleasedetails!==n&&(this._tblAllReleasedetails=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._ctlConfirm=null,this._intRequirementLineID=null,this._ReqSalesman=null,this._SupportTeamMemberNo=null,this._tblAllReleasedetails=null,this._UnitBuyPrice=null,this._UnitSellPrice=null,Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown?(this._tblAllReleasedetails.clearTable(),this.getSerialDetail(this._intRequirementLineID),this._tblAllReleasedetails.resizeColumns(),this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked))):(this._tblAllReleasedetails.clearTable(),this.getSerialDetail(this._intRequirementLineID),this._tblAllReleasedetails.resizeColumns())},getSerialDetail:function(n){var t=new Rebound.GlobalTrader.Site.Data;t.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");t.set_DataObject("BOMCusReqSourcingResults");t.set_DataAction("GetData");t.addParameter("id",n);t.addDataOK(Function.createDelegate(this,this.getAllReleaseOK));t.addError(Function.createDelegate(this,this.getAllReleaseError));t.addTimeout(Function.createDelegate(this,this.getAllReleaseError));$R_DQ.addToQueue(t);$R_DQ.processQueue();t=null},getAllReleaseOK:function(n){var f,i,r,u,t;if(res=n._result,f=0,i=null,res.Results.length>0)for(r=null,u=0;u<res.Results.length;u++)t=res.Results[u],i=null,t.UnitBuyPrice!=null&&t.UnitSellPrice!=null&&(t.UnitBuyPrice>=t.UnitSellPrice?(t.UnitBuyPrice>t.UnitSellPrice&&(i="The buy price ("+t.BuyPrice+") of selected part is greater then the sell price ("+t.Price+") "),t.UnitBuyPrice==t.UnitSellPrice&&(i="The buy price ("+t.BuyPrice+") of selected part is equal to the sell price ("+t.Price+")"),r=[$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Supplier),$R_FN.writePartNo(t.Part)),$R_FN.setCleanTextValue(t.BuyPrice),$R_FN.setCleanTextValue(t.Price),$R_FN.setCleanTextValue($R_FN.setCleanTextValue(i))],f++):$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail").hide()),f>0?($("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail").show(),this._tblAllReleasedetails.addRow(r,t.REQID,!1)):$("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail").hide(),t=null,r=null;else $("#ctl00_cphMain_ctlBOMItems_ctlDB_ctl14_ctlConfirm_ctlDB_ctlSerialNoDetail").hide()},getAllReleaseError:function(){},showLoadingAllRelease:function(){},showAllReleaseError:function(){},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/BOMItems");n.set_DataObject("BOMItems");n.set_DataAction("ReleaseRequirement");n.addParameter("id",this._intRequirementLineID);n.addParameter("BomId",this._intBOMID);n.addParameter("BomCode",this._BomCode);n.addParameter("BomName",this._BomName);n.addParameter("BomCompanyName",this._BomCompanyName);n.addParameter("BomCompanyNo",this._BomCompanyNo);n.addParameter("SalesManName",this._SalesManName);n.addParameter("SalesManNo",this._SalesManNo);n.addParameter("CustReqNo",this._CustReqNo);n.addParameter("Reqsalesman",this._ReqSalesman);n.addParameter("SupportTeamMemberNo",this._SupportTeamMemberNo);n.addDataOK(Function.createDelegate(this,this.saveComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveComplete:function(n){this.showSaving(!1);n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);