using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class ReceivedOrders : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {
            try
            {
                List<PurchaseOrder> lstReceived = PurchaseOrder.GetListRecentlyReceived(SessionManager.ClientID, RowCount, Convert.ToBoolean(SessionManager.IsPOHub));
                if (lstReceived == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    //received
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstReceived.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lstReceived[i].PurchaseOrderId);
                        jsnItem.AddVariable("No", lstReceived[i].PurchaseOrderNumber);
                        jsnItem.AddVariable("Received", Functions.FormatDate(lstReceived[i].DateReceived));
                        jsnItem.AddVariable("CM", lstReceived[i].CompanyName);
                        jsnItem.AddVariable("CMNo", lstReceived[i].CompanyNo);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("Received", jsnItems);
                    jsn.AddVariable("Count", lstReceived.Count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lstReceived = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


	}
}
