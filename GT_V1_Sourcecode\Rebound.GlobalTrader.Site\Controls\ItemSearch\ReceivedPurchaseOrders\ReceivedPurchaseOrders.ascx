<%@ Control Language="C#" CodeBehind="ReceivedPurchaseOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.ReceivedPurchaseOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNo" TextBoxMaxLength="10"  />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlContact" runat="server" ResourceTitle="Contact" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyer" runat="server" ResourceTitle="Buyer" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" />
		<ReboundUI_FilterDataItemRow:Numerical id="ctlInternalPurchaseOrderNo" runat="server" ResourceTitle="InternalPurchaseOrderNo" FilterField="IPONo" />
	</FieldsRight>
</ReboundUI_ItemSearch:DesignBase>
