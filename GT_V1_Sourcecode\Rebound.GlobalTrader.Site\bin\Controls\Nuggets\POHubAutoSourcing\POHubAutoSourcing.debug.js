﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.initializeBase(this, [element]);
    this._cusReqID = -1;
    this._partNo = "";
    this._blnRequirementClosed = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.prototype = {
    get_cusReqID: function () { return this._cusReqID; }, set_cusReqID: function (value) { if (this._cusReqID !== value) this._cusReqID = value; },
    get_tblAutoSourcing: function () { return this._tblAutoSourcing; }, set_tblAutoSourcing: function (value) { if (this._tblAutoSourcing !== value) this._tblAutoSourcing = value; },
    updateSourcingResultComplete: function (handler) { this.get_events().addHandler("SourcingResultUpdated", handler); },
    removeSourcingResultComplete: function (handler) { this.get_events().removeHandler("SourcingResultUpdated", handler); },
    onSourcingResultUpdated: function () {
        var handler = this.get_events().getHandler("SourcingResultUpdated");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.callBaseMethod(this, "initialize");
        this._strPathToData = "controls/Nuggets/POHubSourcing";
        this._strDataObject = "POHubSourcing";
        this.addRefreshEvent(Function.createDelegate(this, this.getAutoSourcing));
        this._tblAutoSourcing.addSortDataEvent(Function.createDelegate(this, this.getAutoSourcing));
        this.showLoading(false);
        this.showContent(true);
        this.showContentLoading(false);
        this.showValidateMessage(false);
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._tblAutoSourcing) this._tblAutoSourcing.dispose();
        this._tblAutoSourcing = null;
        this._cusReqID = null;
        this._partNo = null;
        this._blnRequirementClosed = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.callBaseMethod(this, "dispose");
    },

    getAutoSourcing: function () {
        this.showValidateMessage(false);
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetAutoSourcing");
        obj.addParameter("CRID", this._cusReqID);
        obj.addParameter("PartNo", this._partNo);
        obj.addParameter("SortIndex", this._tblAutoSourcing._intSortColumnIndex);
        obj.addParameter("SortDir", this._tblAutoSourcing._enmSortDirection);
        obj.addDataOK(Function.createDelegate(this, this.getAutoSourcingOK));
        obj.addError(Function.createDelegate(this, this.getAutoSourcingError));
        obj.addTimeout(Function.createDelegate(this, this.getAutoSourcingError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getAutoSourcingOK: function (args) {
        var res = args._result;
        this._tblAutoSourcing.clearTable();
        this.renderTable(this._tblAutoSourcing, res);
        this._tblAutoSourcing.resizeColumns();
        this.getDataOK_End();
        this.showNoData(res.Count == 0);
    },

    getAutoSourcingError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    renderTable: function (tbl, res) {
        if (res.Items) {
            for (var i = 0; i < res.Items.length; i++) {
                var actionBtnHtml = "<span class='disabled'>Add Offer</span>";
                var row = res.Items[i];

                if (row.AddedToReq) {
                    if (row.AllowRemoveOffer) {
                        actionBtnHtml = String.format("<a href=\"javascript:void(0);\" class=\"link-button nubButtonAlignLeft\" title='Remove Offer' onclick=\"$find('{0}').removeOffer('{1}');\">Remove Offer</a>", this._element.id, row.SourcingResultId);
                    } else {
                        actionBtnHtml = "<span class='disabled'>Remove Offer</span>";
                    }
                } else if (this.checkEnableAddToOffer(row)) {
                    actionBtnHtml = String.format("<a href=\"javascript:void(0);\" class=\"link-button nubButtonAlignLeft\" title='Add Offer' onclick=\"$find('{0}').addOffer('{1}');\">Add Offer</a>", this._element.id, row.ID);
                }

                var aryData = [
                    actionBtnHtml
                    , $R_FN.writePartNo(row.PartNo, row.ROHS)
                    , $R_FN.setCleanTextValue(row.SourcingTypeDescription)
                    , $R_FN.setCleanTextValue(row.Supplier)
                    , $RGT_nubButton_Manufacturer(row.MfrNo, row.MfrCode)
                    , $R_FN.setCleanTextValue(row.Product)
                    , $R_FN.setCleanTextValue(row.DateOrdered)
                    , $R_FN.setCleanTextValue(row.Quantity)
                    , $R_FN.setCleanTextValue(row.BuyPrice)
                    , $R_FN.setCleanTextValue(row.SellPrice)
                ];
                var objExtraData = {
                    SourcingType: row.SourcingType
                    , DivisionStatus: row.DivisionStatus
                    , IsCurrentClient: row.IsCurrentClient
                    , IsSourcingHub: row.IsSourcingHub
                    , IsRestrictMfr: row.IsRestrictMfr
                };
                tbl.addRow(aryData, row.ID, false, objExtraData);
                row = null; aryData = null;
            }
            if (res.LyticaResult) {
                $('#lytica-data').html($R_FN.setCleanTextValue(res.LyticaResult))
            }
            if (res.IHSResult) {
                $('#ihs-data').html($R_FN.setCleanTextValue(res.IHSResult))
            }
        }
    },

    checkEnableAddToOffer: function (row) {
        var enable = true;
        switch (row.SourcingType) {
            case "HUBSTK": enable = row.DivisionStatus == 1; break;
            case "EPPH":
            case "RLPH":
                enable = !this._blnRequirementClosed && row.IsCurrentClient && !row.IsSourcingHub; break;
            case "OFPH":
                enable = !this._blnRequirementClosed && row.IsCurrentClient && !row.IsSourcingHub && !row.IsRestrictMfr; break;
            default: break;
        }
        return enable;
    },

    addOffer: function (ID) {
        var isComfirmed = confirm("Are you sure you want to add");
        if (!isComfirmed) return;
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("AddAutoSourcing");
        obj.addParameter("ID", this._cusReqID);
        obj.addParameter("AutoSourceID", ID);
        obj.addDataOK(Function.createDelegate(this, this.addOfferOK));
        obj.addError(Function.createDelegate(this, this.addOfferError));
        obj.addTimeout(Function.createDelegate(this, this.addOfferError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    addOfferOK: function (args) {
        if (args._result.Result) {
            this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
            this.onSourcingResultUpdated();
            return;
        }
        if (args._result.Msg != "undefined" && args._result.Msg.length > 0) {
            this.showLoading(false);
            this.showValidateMessage(true,args._result.Msg);
            return;
        }
        this.showError(true, args.get_ErrorMessage());
    },
    addOfferError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    removeOffer: function (ID) {
        var isComfirmed = confirm("Are you sure you want to remove");
        if (!isComfirmed) return;
        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("DeleteItem");
        obj.addParameter("ListOfIds", ID);
        obj.addDataOK(Function.createDelegate(this, this.removeOfferOK));
        obj.addError(Function.createDelegate(this, this.removeOfferError));
        obj.addTimeout(Function.createDelegate(this, this.removeOfferError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    removeOfferOK: function (args) {
        if (args._result.Result) {
            this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
            this.onSourcingResultUpdated();
        } else {
            this.showError(true, args.get_ErrorMessage());
        }
    },
    removeOfferError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    showValidateMessage: function (isShow, msg) {
        if (isShow) {
            $('#error-detail').html(msg);
            $('.error-summary').show();
        } else {
            $('#error-detail').html("");
            $('.error-summary').hide();
        }
    }
};

Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.POHubAutoSourcing", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
