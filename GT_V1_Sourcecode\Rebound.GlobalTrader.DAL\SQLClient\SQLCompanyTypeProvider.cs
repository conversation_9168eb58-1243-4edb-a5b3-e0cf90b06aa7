﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlCompanyTypeProvider : CompanyTypeProvider {
		/// <summary>
		/// Delete CompanyType
		/// Calls [usp_delete_CompanyType]
		/// </summary>
		public override bool Delete(System.Int32? companyTypeId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_delete_CompanyType", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@CompanyTypeId", SqlDbType.Int).Value = companyTypeId;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to delete CompanyType", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// DropDown 
		/// Calls [usp_dropdown_CompanyType]
        /// </summary>
		public override List<CompanyTypeDetails> DropDown() {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_dropdown_CompanyType", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<CompanyTypeDetails> lst = new List<CompanyTypeDetails>();
				while (reader.Read()) {
					CompanyTypeDetails obj = new CompanyTypeDetails();
					obj.CompanyTypeId = GetReaderValue_Int32(reader, "CompanyTypeId", 0);
					obj.Name = GetReaderValue_String(reader, "Name", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get CompanyTypes", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
		/// <summary>
		/// Create a new row
		/// Calls [usp_insert_CompanyType]
		/// </summary>
        public override Int32 Insert(System.String name, System.Boolean? Traceability, System.Boolean? NonPreferredCompany)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_insert_CompanyType", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
                cmd.Parameters.Add("@Traceability", SqlDbType.Bit).Value = Traceability;
                cmd.Parameters.Add("@NonPreferredCompany", SqlDbType.Bit).Value = NonPreferredCompany;
                cmd.Parameters.Add("@CompanyTypeId", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (Int32)cmd.Parameters["@CompanyTypeId"].Value;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to insert CompanyType", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// Get 
		/// Calls [usp_select_CompanyType]
        /// </summary>
		public override CompanyTypeDetails Get(System.Int32? companyTypeId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_select_CompanyType", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@CompanyTypeId", SqlDbType.Int).Value = companyTypeId;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
				if (reader.Read()) {
					//return GetCompanyTypeFromReader(reader);
					CompanyTypeDetails obj = new CompanyTypeDetails();
					obj.CompanyTypeId = GetReaderValue_Int32(reader, "CompanyTypeId", 0);
					obj.Name = GetReaderValue_String(reader, "Name", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
					return obj;
				} else {
					return null;
				}
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get CompanyType", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// GetList 
		/// Calls [usp_selectAll_CompanyType]
        /// </summary>
		public override List<CompanyTypeDetails> GetList() {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_selectAll_CompanyType", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<CompanyTypeDetails> lst = new List<CompanyTypeDetails>();
				while (reader.Read()) {
					CompanyTypeDetails obj = new CompanyTypeDetails();
					obj.CompanyTypeId = GetReaderValue_Int32(reader, "CompanyTypeId", 0);
					obj.Name = GetReaderValue_String(reader, "Name", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.Traceability = GetReaderValue_Boolean(reader, "IsTraceability", false);
                    obj.NonPreferredCompany= GetReaderValue_Boolean(reader, "NonPreferredCompany", false);
                    lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get CompanyTypes", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// Update CompanyType
		/// Calls [usp_update_CompanyType]
        /// </summary>
        public override bool Update(System.String name, System.Int32? companyTypeId, System.Boolean? inactive, System.Int32? updatedBy, System.Boolean? Traceability,System.Boolean? NonPreferredCompany)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_CompanyType", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@Name", SqlDbType.NVarChar).Value = name;
				cmd.Parameters.Add("@CompanyTypeId", SqlDbType.Int).Value = companyTypeId;
				cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@Traceability", SqlDbType.Bit).Value = Traceability;
                cmd.Parameters.Add("@NonPreferredCompany", SqlDbType.Bit).Value = NonPreferredCompany;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to update CompanyType", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}



		///////////////////////////Document File Size///////////////
		/// <summary>
		/// Insert
		/// Calls [usp_insert_DocumentfileSize]
		/// </summary>
		public override Int32 InsertfileSize(System.Int32? DocumentType, System.Int32? DocumentSizeMB, System.Int32? DocumentSizeByte, System.String Notes, System.Boolean? Inactive, System.Int32? UpdatedBy, System.Int32? ClientNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_insert_DocumentfileSize", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@DocumentType", SqlDbType.Int).Value = DocumentType;
				cmd.Parameters.Add("@DocumentSizeMB ", SqlDbType.Int).Value = DocumentSizeMB;
				cmd.Parameters.Add("@DocumentSizeByte", SqlDbType.BigInt).Value = DocumentSizeByte;
				cmd.Parameters.Add("@Notes ", SqlDbType.NVarChar).Value = Notes;
				cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = Inactive;
				cmd.Parameters.Add("@UpdatedBy ", SqlDbType.Int).Value = UpdatedBy;
				cmd.Parameters.Add("@ClientNo ", SqlDbType.Int).Value = ClientNo;
				cmd.Parameters.Add("@DocumentId", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (Int32)cmd.Parameters["@DocumentId"].Value;
	
			}
			catch (SqlException sqlex)
			{
				throw new Exception("Failed to insert Document File Size", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		/// <summary>
		/// Update
		/// Calls [usp_update_DocumentfileSize]
		/// </summary>
		public override bool UpdatefileSize(System.Int32? DocumentTypeId, System.Int32? DocumentType, System.Int32? DocumentSizeMB, System.Int32? DocumentSizeByte, System.String Notes, System.Boolean? Inactive, System.Int32? UpdatedBy, System.Int32? ClientNo)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_DocumentfileSize", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@DocumentId", SqlDbType.Int).Value = DocumentTypeId;
				cmd.Parameters.Add("@DocumentType", SqlDbType.Int).Value = DocumentType;
				cmd.Parameters.Add("@DocumentSizeMB ", SqlDbType.Int).Value = DocumentSizeMB;
				cmd.Parameters.Add("@DocumentSizeByte", SqlDbType.BigInt).Value = DocumentSizeByte;
				cmd.Parameters.Add("@Notes ", SqlDbType.NVarChar).Value = Notes;
				cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = Inactive;
				cmd.Parameters.Add("@UpdatedBy ", SqlDbType.Int).Value = UpdatedBy;

				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to update Document File Size", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}

		/// /// <summary>
		/// GetfileSizeList
		/// Calls [usp_select_GetDocumentfileSize]
		/// </summary>
		public override CompanyTypeDetails GetfileSize(System.Int32? DocumentTypeId)
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_select_GetDocumentfileSize", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@DocumentId", SqlDbType.Int).Value = DocumentTypeId;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
				if (reader.Read())
				{
					CompanyTypeDetails obj = new CompanyTypeDetails();
					obj.DocumentTypeId = GetReaderValue_Int32(reader, "DocumentId", 0);
					obj.DocumentType = GetReaderValue_Int32(reader, "DocumentId", 0);
					obj.DocumentSizeMB = GetReaderValue_Int32(reader, "DocumentMB", 0);
					obj.DocumentSizeByte = GetReaderValue_Int32(reader, "DocumentByte", 0);
					obj.Notes = GetReaderValue_String(reader, "DocumentNotes", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
					obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
					obj.DocumentName = GetReaderValue_String(reader, "DocumentName", "");


					return obj;
				}
				else
				{
					return null;
				}
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get Document File Size", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		/// <summary>
		/// GetfileSizeList
		/// Calls [usp_selectAll_DocumentfileSize]
		/// </summary>
		public override List<CompanyTypeDetails> GetfileSizeList()
		{
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try
			{
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_selectAll_DocumentfileSize", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<CompanyTypeDetails> lst = new List<CompanyTypeDetails>();
				while (reader.Read())
				{
					CompanyTypeDetails obj = new CompanyTypeDetails();
					obj.DocumentTypeId = GetReaderValue_Int32(reader, "DocumentId", 0);
					obj.DocumentType = GetReaderValue_Int32(reader, "DocumentId", 0);
					obj.DocumentSizeMB = GetReaderValue_Int32(reader, "DocumentMB", 0);
					obj.DocumentSizeByte = GetReaderValue_Int32(reader, "DocumentByte", 0);
					obj.Notes = GetReaderValue_String(reader, "DocumentNotes", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null);
					obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
					obj.DocumentName = GetReaderValue_String(reader, "DocumentName", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			}
			catch (SqlException sqlex)
			{
				//LogException(sqlex);
				throw new Exception("Failed to get document File Size", sqlex);
			}
			finally
			{
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		




	}
}