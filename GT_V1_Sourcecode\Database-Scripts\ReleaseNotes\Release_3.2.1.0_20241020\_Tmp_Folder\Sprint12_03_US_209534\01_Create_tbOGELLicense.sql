﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209534]     NgaiTo		 17-Sep-2024		CREATE		Add table tbOGELLicense
===========================================================================================  
*/
DROP TABLE IF EXISTS tbOGELLicense;

CREATE TABLE tbOGELLicense (
	[OgelId] [int] IDENTITY(1, 1) NOT NULL,
	[OgelNumber] [nvarchar](250) NOT NULL,
	[Description] [nvarchar](500) NULL,
	[Inactive] [bit] NULL,
	[DLUP] [datetime] NOT NULL,
	[UpdatedBy] [int] NULL,
	PRIMARY KEY CLUSTERED ([OgelId] ASC) WITH (
		PAD_INDEX = OFF,
		STATISTICS_NORECOMPUTE = OFF,
		IGNORE_DUP_KEY = OFF,
		ALLOW_ROW_LOCKS = ON,
		ALLOW_PAGE_LOCKS = ON
		) ON [PRIMARY]
	) ON [PRIMARY]
GO

ALTER TABLE [dbo].[tbOGELLicense] ADD DEFAULT(getdate())
FOR [DLUP]
GO

ALTER TABLE [dbo].[tbOGELLicense] ADD DEFAULT((0))
FOR [Inactive]
GO