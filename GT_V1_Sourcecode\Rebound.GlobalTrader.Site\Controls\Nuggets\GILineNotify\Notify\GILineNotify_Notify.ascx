<%@ Control Language="C#" CodeBehind="GILineNotify_Notify.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">

    <Links>
        <ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "GILineNotify_Notify")%></Explanation>
    <Content>

        <ReboundUI_Table:Form ID="frmStep1" runat="server" class="partsreq">

            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="GoodsInUpdateTypeError" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "GoodsInUpdateType")%></label>
                </asp:TableCell>
                <asp:TableCell ID="GoodsInUpdateTypeError1">
                    <ReboundDropDown:GoodsInUpdate ID="ddlUpdateType" runat="server" />
                    <asp:Label ID="lblUpdateType" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "GoodsInNo")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblGoodsIn" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Supplier")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="2">
                    <asp:Label ID="lblSupplier" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "SupplierType")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="2">
                    <asp:Label ID="lblSupplierType" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "AirWayBill")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblAirWayBill" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Reference")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblReference" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell ID="LocationError" class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Location")%></label>
                </asp:TableCell>
                <asp:TableCell ID="LocationError1" colspan="5">
                    <asp:Label ID="lblLocation" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Lot")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblLot" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "InsToQualityControlNotes")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtQualityControlNotes" runat="server" Width="350" Style="opacity: 0.5;" TextMode="MultiLine" Rows="3" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>

            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "QuantityOrdered")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblQuantityOrdered" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsFullQuantityReceived")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkFullQuantityReceived" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD" ID="tcQuantity">
                    <label><%=Functions.GetGlobalResource("FormFields", "QuantityReceived")%></label>
                </asp:TableCell>
                <asp:TableCell ID="tctxtQuantity">
                    <ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" Width="200px" TextBoxMode="Numeric" Style="opacity: 0.5;" ReadOnly="true" />
                    <asp:Label ID="lblQuantity" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "PartNo")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblPartNo" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsPartNoCorrect")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkPartNumberCorrect" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD" ID="CorrectPartNoError">
                    <label><%=Functions.GetGlobalResource("FormFields", "CorrectPartNo")%></label>
                </asp:TableCell>
                <asp:TableCell ID="CorrectPartNoError1">
                    <ReboundUI:ReboundTextBox ID="txtCorrectPartNo" runat="server" Width="200" Style="opacity: 0.5;" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Manufacturer")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblManufacturer" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsManufacturerCorrect")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkManufacturerCorrect" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CorrectManufacturer")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="Label1" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder" row>
                <asp:TableCell class="lableTD" RowSpan="2">
                    <label><%=Functions.GetGlobalResource("FormFields", "DateCode")%></label>
                </asp:TableCell>
                <asp:TableCell RowSpan="2">
                    <asp:Label ID="lblDateCode" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsDateCodeCorrect")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkDateCodeCorrect" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CorrectDateCode")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ReboundTextBox ID="txtCorrectDateCode" runat="server" Width="200" Style="opacity: 0.5;" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder" row>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsDateCodeRequired")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkDateCodeRequired" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label id="lblDateCodeReceived"><%=Functions.GetGlobalResource("FormFields", "DateCodeReceived")%></label>
                </asp:TableCell>
                <asp:TableCell style="width: 205px;">
                    <ReboundUI:FlexiDataTable ID="tblDateCode" runat="server" Style="max-width: 205px;" PanelHeight="72px" AllowSelection="false" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Package")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblPackage" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsPackageCorrect")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkPackageCorrect" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CorrectPackage")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblCorrectPackage" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "MSL")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblMSL" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsMSLCorrect")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkMSLCorrect" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD" ID="CorrectMSLError">
                    <label><%=Functions.GetGlobalResource("FormFields", "CorrectMSL")%></label>
                </asp:TableCell>
                <asp:TableCell ID="CorrectMSLError1">
                    <asp:Label ID="lblCorrectMSL" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "HICStatus")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblHICStatus" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsHICCorrect")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkHICCorrect" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CorrectHIC")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ReboundTextBox ID="txtCorrectHIC" runat="server" Width="200" Style="opacity: 0.5;" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "PackagingBreakdown")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="3">
                    <ReboundUI:FlexiDataTable ID="tblPackagingBreakdown" runat="server" Style="max-width: 480px;" PanelHeight="60px" ScrollBars="None" AllowSelection="false" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "PackageBreakdownInfo")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ReboundTextBox ID="txtPackageBreakdownInfo" runat="server" Width="200" Style="opacity: 0.5;" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CorrectRohsStatus")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblCorrectRohsStatus" runat="server" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "IsRohsStatusCorrect")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkRohsStatusCorrect" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CorrectRohsStatus")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <%--<asp:Label ID="lblROHSStatus" runat="server" />--%>
                     <ReboundDropDown:ROHSStatus ID="ddlROHSStatus" runat="server" IncludeNoValue="true" NoValue_Value="" InitialValue="" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CountryOfManufacture")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblCountryOfManufacture" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "CountingMethod")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblCountingMethod" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "ReqSerailNo")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ImageCheckBox ID="chkReqSerailNo" runat="server" Enabled="true" />
                    <asp:Label value="Yes" Text="Yes" runat="server" />
                </asp:TableCell>
            </asp:TableRow>

            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "LotCodeReq")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ImageCheckBox ID="chkLotCodeReq" runat="server" Enabled="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "EnhancedInpectionRequired")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ImageCheckBox ID="chkEnhancedInpectionRequired" runat="server" Enabled="true" />
                    <asp:Label value="Yes" Text="Yes" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "GeneralInspectionNotes")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtGeneralInspectionNotes" runat="server" Width="350" TextMode="multiLine" Style="opacity: 0.5;" Rows="3" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "BakingLevelAdded")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkbakingYes" runat="server" Enabled="true" />
                    <label for="html">Yes</label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkbakingNo" runat="server" Enabled="true" />
                    <label for="html">No</label>
                </asp:TableCell>
                <asp:TableCell colspan="3">
                    <ReboundUI:ImageCheckBox ID="chkbakingNA" runat="server" Enabled="true" />
                    <label for="html">NA</label>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD" colspan="4">
                   <label><%=Functions.GetGlobalResource("FormFields", "IsInspectionConducted")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="2">
                    <ReboundUI:ImageCheckBox ID="chkInspectionConducted" runat="server" Enabled="true" />
                    <asp:Label value="Yes" Text="Yes" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD" colspan="1">
		             <label><%=Functions.GetGlobalResource("FormFields", "ReceivingNotes")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblReceivingNotes" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD" colspan="1">
		<label><%=Functions.GetGlobalResource("FormFields", "SupplierPartNo")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtSupplierPart" runat="server" Width="150" UppercaseOnly="true" Style="opacity: 0.5;" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD" ID="ProductsError" colspan="1">
		            <label><%=Functions.GetGlobalResource("FormFields", "Product")%></label>
                </asp:TableCell>
                <asp:TableCell ID="ProductsError1" colspan="5">
                    <asp:Label ID="lblProducts" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD" colspan="1">
		<label><%=Functions.GetGlobalResource("FormFields", "PurchasePrice")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" Style="opacity: 0.5;" ReadOnly="true" />
                    <asp:Label ID="lblCurrency_Price" runat="server" />
                    <ReboundUI:ReboundTextBox ID="txtPrice_IPO" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" Style="opacity: 0.5;" ReadOnly="true" />
                    <asp:Label ID="lblCurrency_Price_IPO" runat="server" />

                    <asp:Label ID="lblPrice" runat="server" />
                    <asp:Label ID="lblCurrency_PriceLabel" runat="server" />
                    <asp:Label ID="lblPrice_IPO" runat="server" />
                    <asp:Label ID="lblCurrency_PriceLabel_IPO" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD" ID="ShipInCostError">
		<label><%=Functions.GetGlobalResource("FormFields", "ShipInCost")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5" ID="ShipInCostError1">
                    <ReboundUI:ReboundTextBox ID="txtShipInCost" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" Style="opacity: 0.5;" ReadOnly="true" />
                    <asp:Label ID="lblShipInCost" runat="server" />
                    <%=Rebound.GlobalTrader.Site.SessionManager.ClientBaseCurrencyCode%>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
		<label><%=Functions.GetGlobalResource("FormFields", "Quarantine")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ImageCheckBox ID="chkUnavailable" runat="server" Enabled="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
		<label><%=Functions.GetGlobalResource("FormFields", "SerialNosRecorded")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ImageCheckBox ID="chkSerialNosRecorded" runat="server" Enabled="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
		<label><%=Functions.GetGlobalResource("FormFields", "BatchReference")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtPartMarkings" runat="server" Width="200" Style="opacity: 0.5;" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Notes")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtLineNotes" runat="server" Width="350" Style="opacity: 0.5;" TextMode="multiLine" Rows="3" ReadOnly="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="bottomborder">
                <asp:TableCell class="lableTD">
		<label><%=Functions.GetGlobalResource("FormFields", "PrintHazWarning")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ImageCheckBox ID="chkPrintHazWar" runat="server" Enabled="true" />
                </asp:TableCell>
            </asp:TableRow>

            <asp:TableRow class="mailbody" Style="display: none">
                <asp:TableCell>
                    <label><%=Functions.GetGlobalResource("FormFields", "EmailTo")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <label class="EmailTo"><%=Functions.GetGlobalResource("FormFields", "EmailToSales")%></label>
                    <ReboundUI:ImageCheckBox ID="chkSales" runat="server" Enabled="true" />
                    <label class="EmailTo"><%=Functions.GetGlobalResource("FormFields", "EmailToPurchasing")%></label>
                    <ReboundUI:ImageCheckBox ID="chkPurchasing" runat="server" Enabled="true" />
                    <label class="EmailTo"><%=Functions.GetGlobalResource("FormFields", "EmailToQualityApproval")%></label>
                    <ReboundUI:ImageCheckBox ID="chkQualityApproval" runat="server" Enabled="true" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    <label><%=Functions.GetGlobalResource("FormFields", "GIAllQueries")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="txtGIAllQueries" runat="server" Width="350" TextMode="multiLine" Rows="10" Enabled="false" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow class="mailbody">
                <asp:TableCell>
                    <label><%=Functions.GetGlobalResource("FormFields", "GIQueryReply")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundUI:ReboundTextBox ID="GIQueryReply" runat="server" Width="350" TextMode="multiLine" Rows="10" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow ID="trQueryApprovedSales" Style="display: none">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "EmailToSales")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundDropDown:GILineQueryStatus ID="ddlQueryApprovedSales" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow ID="trQueryApprovedPurchase" Style="display: none">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "EmailToPurchasing")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundDropDown:GILineQueryStatus ID="ddlQueryApprovedPurchase" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow ID="trQueryApprovedQuality" Style="display: none">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "EmailToQualityApproval")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <ReboundDropDown:GILineQueryStatus ID="ddlQueryApprovedQuality" runat="server" />
                </asp:TableCell>
            </asp:TableRow>

            <asp:TableRow>
                <asp:TableCell>
                    <label><%=Functions.GetGlobalResource("FormFields", "PDFReportRequired")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkPDFReportRequired" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell colspan="4">
                    <ReboundUI:IconButton ID="ibtnGeneratePDF" runat="server" IconGroup="Nugget" IconTitleResource="CreatePDF" IconButtonMode="HyperLink" Href="javascript:void(0);" />
                </asp:TableCell>

            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    <label><%=Functions.GetGlobalResource("FormFields", "QuarantineProduct")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <ReboundUI:ImageCheckBox ID="chkQuarantineProduct" runat="server" Enabled="true" />
                </asp:TableCell>
                <asp:TableCell colsapn="4">
                    <ReboundUI:IconButton ID="ibnQuarantineProduct" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="NPR" IconCSSType="Print" />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell></asp:TableCell>
                <asp:TableCell></asp:TableCell>
                <asp:TableCell colsapn="4">
                    <ReboundUI:IconButton ID="ibnAddImage" runat="server" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="GIImage" IconCSSType="Edit" />
                </asp:TableCell>
            </asp:TableRow>
        </ReboundUI_Table:Form>

    </Content>

</ReboundUI_Form:DesignBase>
