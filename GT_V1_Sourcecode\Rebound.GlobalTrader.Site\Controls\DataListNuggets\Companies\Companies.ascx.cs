//----------------------------------------------------------------------------------------------------------------
// RP 23.12.2009:
// - render state on server
//
// RP 30.11.2009:
// - allow passing of an initial company name search
/*
 Marker     ChangedBy       Date            Remarks
 [001]      Aashu Singh     13-Sep-2018     [REB-12820]:Provision to add Global Security on Contact Section
 */
//----------------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class Companies : Base {

        #region Locals 
        //protected IconButton _ibtnAdd;
        #endregion
        #region Properties

        private Controls.DataListNuggets.CompanyListType _enmCompanyListType;
		public Controls.DataListNuggets.CompanyListType CompanyListType {
			get { return _enmCompanyListType; }
			set { _enmCompanyListType = value; }
		}
        //[001] start
        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }
		//[001] end

		private bool _IsGSA = false;
		public bool IsGSA
		{
			get { return _IsGSA; }
			set { _IsGSA = value; }
		}
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
            //[001] start
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
            //[001] end
			base.OnInit(e);
            WireUpControls();//[001]  
            AddScriptReference("Controls.DataListNuggets.Companies.Companies.js");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_IsGSA = SessionManager.IsGSA.Value;
			SetDataListNuggetType("Companies", _enmCompanyListType);
			TitleText = Functions.GetGlobalResource("CompanyListType", _enmCompanyListType);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Companies", ctlDesignBase.ClientID);
            //[001] start
            _scScriptControlDescriptor.AddProperty("IsGlobalLogin", _IsGlobalLogin);
			_scScriptControlDescriptor.AddProperty("IsGSA", _IsGSA);
			//_scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			//[001] end

			base.OnLoad(e);
		}

		protected override void GetSavedState() {
			base.GetSavedState();

			//don't render state if we are searching for a Company
			if (!string.IsNullOrEmpty(_objQSManager.CompanyName)) {
				ResetAllState();
				SetFilterValue("Name", _objQSManager.CompanyName);
				_enmViewLevel = ViewLevelList.Company;
			}
		}

		protected override void RenderAdditionalState() {
			string strViewLevel = this.GetSavedStateValue("ViewLevel");
			if (!string.IsNullOrEmpty(strViewLevel)) _enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
			base.RenderAdditionalState();
		}

		protected override void OnPreRender(EventArgs e) {
			base.OnPreRender(e);
			_scScriptControlDescriptor.AddProperty("enmContactListType", _enmCompanyListType);
			((Pages.Content)Page).CurrentTab = Convert.ToInt32(_enmViewLevel);
			this.OnAskPageToChangeTab();
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.Columns.Add(new FlexiDataColumn("CompanyName", "Code", Unit.Empty, true));
            _tbl.Columns.Add(new FlexiDataColumn("ProspectQualification", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
            _tbl.Columns.Add(new FlexiDataColumn("CompanyType", "Terms", Unit.Pixel(150), true));
			_tbl.Columns.Add(new FlexiDataColumn("City", "Country", WidthManager.GetWidth(WidthManager.ColumnWidth.City), true));
			_tbl.Columns.Add(new FlexiDataColumn("Tel","Email", WidthManager.GetWidth(WidthManager.ColumnWidth.TelNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Salesperson","Turnover", WidthManager.GetWidth(WidthManager.ColumnWidth.LoginName), true));
			_tbl.Columns.Add(new FlexiDataColumn("LastContacted", "ToDoList", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
			_tbl.Columns.Add(new FlexiDataColumn("CertificateNo", "CertificateCategory", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
			//_tbl.Columns.Add(new FlexiDataColumn("LastContacted", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));
			//         _tbl.Columns.Add(new FlexiDataColumn("ToDoList", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime), true));

			//[001] start
			if (_IsGlobalLogin == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("Client", WidthManager.GetWidth(WidthManager.ColumnWidth.CompanyName), true));
            }
            //[001] end
            
        }
        private void WireUpControls()
        {
            //_ibtnAdd = (IconButton)FindIconButton("ibtnAdd");

        }
    }

	public enum CompanyListType {
		AllCompanies,
		Customers,
		Suppliers,
		Prospects,
		Manufacturers,
		Contacts
	}

}