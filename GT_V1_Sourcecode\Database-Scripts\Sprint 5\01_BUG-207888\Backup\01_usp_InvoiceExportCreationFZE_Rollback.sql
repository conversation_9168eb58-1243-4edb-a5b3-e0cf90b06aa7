-- Drop the procedure if it already exists
IF OBJECT_ID('dbo.usp_InvoiceExportCreationFZE', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_InvoiceExportCreationFZE;
END

SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_InvoiceExportCreationFZE]                                                                                                                    

AS

TRUNCATE 
TABLE	dbo.tbTempInvoiceExportFZE
--Delete from dbo.tbTempInvoiceExport where DoNotReport = 1
--
--INSERT
--INTO	dbo.tbTempInvoiceExportFZE
--	(	[CustomerNumber]
--	,	[InvoiceNumber]
--	,	[CustomerPONumber]
--	,	[InvoiceDate]
--	,	[GoodsValue]
--	,	[ShippingValue]
--	,	[InvoiceTax]
--	,	[Currency]
--	,	[TaxCode]
--	,	[BankFee]
--	,	[ExchangeRate]
--	)
--SELECT	[CustomerCode]
--	,	[InvoiceNumber]
--	,	[CustomerPONumber]
--	,	[InvoiceDate]
--	,	[TotalValue]
--	,	[Freight]
--	,	[TotalTax]
--	,	[Currency]
--	,	[TaxCode]
--	,	[InvoiceBankFee]
--	,	[ExchangeRate]
--FROM	dbo.vwInvoiceExport
--Where ClientNo = 114
----JOIN	dbo.vwClient 
----	ON	dbo.vwClient.[ParentClientNo]	is null
--ORDER BY 2

-- **Add from ClientInvoice for HUB Sales
INSERT
INTO	dbo.tbTempInvoiceExportFZE
	(	[CustomerNumber]
	,	[InvoiceNumber]
	,	[CustomerPONumber]
	,	[InvoiceDate]
	,	[GoodsValue]
	,	[ShippingValue]
	,	[InvoiceTax]
	,	[Currency]
	,	[TaxCode]
	,	[BankFee]
	,	[ExchangeRate]
	)
SELECT	[CustomerCode]
	,	[InvoiceNumber]
	,	[CustomerPONumber]
	,	[InvoiceDate]
	,	[TotalValue]
	,	[Freight]
	,	[TotalTax]
	,	[Currency]
	,	[TaxCode]
	,	[InvoiceBankFee]
	,	[ExchangeRate]
FROM	dbo.vwInvoiceExportFZE_Int
WHERE	ClientNo = 114
AND		ISNULL([TaxCode], 0) > 0
ORDER BY 2
-- ***

INSERT
INTO	dbo.tbTempInvoiceExportFZE
	(	[CustomerNumber]
	,	[InvoiceNumber]
	,	[CustomerPONumber]
	,	[InvoiceDate]
	,	[GoodsValue]
	,	[ShippingValue]
	,	[InvoiceTax]
	,	[Currency]
	,	[TaxCode]
	,	[IsCredit]
	,	[BankFee]
	,	[ExchangeRate]
	)
SELECT	[CustomerCode]
	,	[InvoiceNumber]
	,	[CustomerPONumber]
	,	[InvoiceDate]
	,	[TotalValue]
	,	[Freight]
	,	[TotalTax]
	,	[Currency]
	,	[TaxCode]
	,	1
	,	[CreditNoteBankFee]
	,	[ExchangeRate]
FROM	dbo.vwInvoiceExportCreditFZE
Where	ClientNo = 114
--JOIN	dbo.vwClient 
--	ON	dbo.vwClient.[ParentClientNo]	is null
AND		ISNULL([TaxCode], 0) > 0
ORDER BY 2
GO