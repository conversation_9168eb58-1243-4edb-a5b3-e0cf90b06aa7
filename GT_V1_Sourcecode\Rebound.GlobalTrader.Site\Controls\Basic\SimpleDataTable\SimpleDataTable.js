Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");Rebound.GlobalTrader.Site.Controls.SimpleDataTable=function(n){Rebound.GlobalTrader.Site.Controls.SimpleDataTable.initializeBase(this,[n]);this.ControlType_SimpleDataTable=!0;this._blnShowHeader=!0;this._aryColumnAlignment=[];this._aryColumnWidth=[];this._aryExtraData=[];this._blnColumnsResized=!1};Rebound.GlobalTrader.Site.Controls.SimpleDataTable.prototype={get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_blnInsideDataListNugget:function(){return this._blnInsideDataListNugget},set_blnInsideDataListNugget:function(n){this._blnInsideDataListNugget!==n&&(this._blnInsideDataListNugget=n)},get_blnShowHeader:function(){return this._blnShowHeader},set_blnShowHeader:function(n){this._blnShowHeader!==n&&(this._blnShowHeader=n)},get_aryExtraData:function(){return this._aryExtraData},set_aryExtraData:function(n){this._aryExtraData!==n&&(this._aryExtraData=n)},get_aryColumnAlignment:function(){return this._aryColumnAlignment},set_aryColumnAlignment:function(n){this._aryColumnAlignment!==n&&(this._aryColumnAlignment=n)},get_aryColumnWidth:function(){return this._aryColumnWidth},set_aryColumnWidth:function(n){this._aryColumnWidth!==n&&(this._aryColumnWidth=n)},addAddAsyncRow:function(n){this.get_events().addHandler("AddAsyncRow",n)},removeAddAsyncRow:function(n){this.get_events().removeHandler("AddAsyncRow",n)},onAddAsyncRow:function(){var n=this.get_events().getHandler("AddAsyncRow");n&&n(this,Sys.EventArgs.Empty)},addAsyncDataAdditionComplete:function(n){this.get_events().addHandler("AsyncDataAdditionComplete",n)},removeAsyncDataAdditionComplete:function(n){this.get_events().removeHandler("AsyncDataAdditionComplete",n)},onAsyncDataAdditionComplete:function(){var n=this.get_events().getHandler("AsyncDataAdditionComplete");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.SimpleDataTable.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._tbl&&(this.unhookTableRowEvents(),$clearHandlers(this._tbl)),this._tbl=null,this._aryColumnAlignment=null,this._aryColumnWidth=null,Rebound.GlobalTrader.Site.Controls.SimpleDataTable.callBaseMethod(this,"dispose"),this.isDisposed=!0)},clearTable:function(){this.unhookTableRowEvents();for(var n=this._tbl.rows.length-1;n>0;n--)this._tbl.deleteRow(n);Array.clear(this._aryExtraData)},getID:function(n,t){return String.format("{0}_{1}{2}",this._tbl.id,n,t)},addRow:function(n,t,i,r,u){var s,c,e,h,f,o;if(n){for(u||(u=""),s=this._tbl.insertRow(-1),c=s.rowIndex,s.setAttribute("bui_tableRowIndex",c),e=0,h=n.length;e<h;e++){if(e>=this._aryColumnAlignment.length)break;f=document.createElement("td");e==0&&(f.className="first");e==h-1&&(f.className="last");u!=""&&(f.className+=" "+u,e==0&&(f.className+=String.format(" {0}_First",u)),e==h-1&&(f.className+=String.format(" {0}_Last",u)));switch(this._aryColumnAlignment[e]){case $R_ENUM$HorizontalAlign.Center:f.className+=" alignCenter";break;case $R_ENUM$HorizontalAlign.Right:f.className+=" alignRight";break;case $R_ENUM$HorizontalAlign.Justify:f.className+=" alignJustify"}n[e]+="";o=n[e].toString().trim();(o.length==0||o=="undefined")&&(o="&nbsp;");f.innerHTML=o;f.setAttribute("bui_tableRowIndex",c);s.appendChild(f);f=null;o=null}Array.add(this._aryExtraData,r);s=null}},addRowRowColor:function(n,t,i,r,u,f,e){var c,l,s,a,o,h;if(n){for(u||(u=""),c=this._tbl.insertRow(-1),l=c.rowIndex,c.setAttribute("bui_tableRowIndex",l),s=0,a=n.length;s<a;s++){if(s>=this._aryColumnAlignment.length)break;o=document.createElement("td");s==0&&(o.className="first");s==a-1&&(o.className="last");u!=""&&(o.className+=" "+u,s==0&&(o.className+=String.format(" {0}_First",u)),s==a-1&&(o.className+=String.format(" {0}_Last",u)));switch(this._aryColumnAlignment[s]){case $R_ENUM$HorizontalAlign.Center:o.className+=" alignCenter";break;case $R_ENUM$HorizontalAlign.Right:o.className+=" alignRight";break;case $R_ENUM$HorizontalAlign.Justify:o.className+=" alignJustify"}n[s]+="";h=n[s].toString().trim();(h.length==0||h=="undefined")&&(h="&nbsp;");o.innerHTML=h;o.setAttribute("bui_tableRowIndex",l);c.appendChild(o);o=null;h=null}Array.add(this._aryExtraData,r);c=null;f&&this.RowColor(l,e)}},RowColor:function(n,t){Sys.UI.DomElement.addCssClass(this._tbl.rows[n],t)},show:function(n){$R_FN.showElement(this.get_element(),n)},enable:function(n){this._blnEnabled=n},countRows:function(){return this._tbl?this._tbl.rows?this._tbl.rows.length:0:0},removeRow:function(n){this._tbl.rows[n]&&$clearHandlers(this._tbl.rows[n]);this._tbl.deleteRow(n);Array.removeAt(this._aryExtraData,n)},scrollToRow:function(n){n<0&&(n=0);var t=null;(n<this._tbl.rows.length&&(t=this._tbl.rows[n]),t)&&(this._pnlScroll.scrollTop+=Sys.UI.DomElement.getBounds(t).y-Sys.UI.DomElement.getBounds(this._pnlScroll).y,t=null)},scrollToRowAfterPause:function(n){setTimeout(String.format("$find('{0}').scrollToRow({1});",this._element.id,n),10)},unhookTableRowEvents:function(){for(var n=0,t=this._tbl.rows;n<t;n++)this._tbl.rows[n]&&$clearHandlers(this._tbl.rows[n])},resetAsyncData:function(){this._aryAsyncDataToAdd=[];this._intAsyncRowsToAdd=0;this._intAsyncRowsAdded=0},addDataForAsyncAddition:function(n){n&&Array.addRange(this._aryAsyncDataToAdd,n);this._intAsyncRowsToAdd=this._aryAsyncDataToAdd.length},startAddingRowsAsync:function(){this._intAsyncRowsAdded=0;this._aryAsyncDataToAdd||(this._aryAsyncDataToAdd=[]);this._intAsyncRowsToAdd=this._aryAsyncDataToAdd.length;this._aryAsyncDataToAdd.length>0?this.addRowAsync():this.finishedAddingRowsAsync()},addRowAsync:function(){if(this._intAsyncRowsAdded<this._intAsyncRowsToAdd){this._objAsyncDataRow=this._aryAsyncDataToAdd[this._intAsyncRowsAdded];this.onAddAsyncRow();this._intAsyncRowsAdded+=1;var n=this._element.id,t=function(){$find(n).addRowAsync()};setTimeout(t,0)}else this.finishedAddingRowsAsync()},finishedAddingRowsAsync:function(){this.resetAsyncData();this.resizeColumns();this.onAsyncDataAdditionComplete()}};Rebound.GlobalTrader.Site.Controls.SimpleDataTable.registerClass("Rebound.GlobalTrader.Site.Controls.SimpleDataTable",Sys.UI.Control,Sys.IDisposable);