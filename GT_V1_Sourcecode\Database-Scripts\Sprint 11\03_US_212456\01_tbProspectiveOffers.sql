﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-212456]     An.TranTan		 18-SEP-2024		CREATE		Create new tables for prospective offers
===========================================================================================  
*/
IF OBJECT_ID(N'dbo.tbProspectiveOffers', N'U') IS NULL
BEGIN
	CREATE TABLE dbo.tbProspectiveOffers (
		ProspectiveOfferId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
		SupplierNo INT NOT NULL,
		SourceFileName NVARCHAR(100),
		ImportRowCount INT NOT NULL,
		ImportStatus VARCHAR(50) NULL,
		CreatedBy INT NOT NULL,
		DLUP DATETIME NOT NULL DEFAULT GETDATE(),
		UpdatedBy INT NULL,
		UpdatedDate DATETIME NULL
	);
END
GO

IF OBJECT_ID(N'dbo.tbProspectiveOfferLines', N'U') IS NULL
BEGIN
	CREATE TABLE dbo.tbProspectiveOfferLines (
		ProspectiveOfferLineId INT IDENTITY(1,1) NOT NULL PRIMARY KEY,
		ProspectiveOfferNo INT NOT NULL,
		ManufacturerNo INT NULL,
		Part NVARCHAR(30) NULL,
		AlternativePart NVARCHAR(30) NULL,
		Quantity INT NOT NULL,
		Price FLOAT NOT NULL,
		[Description] NVARCHAR(500) NULL,
		DateCode NVARCHAR(5) NULL,
		ProductNo INT NULL,
		PackageId INT NULL,
		ROHSStatus NVARCHAR(50) NULL,
		CurrencyNo INT NULL,
		SupplierPart NVARCHAR(30) NULL,
		CreatedBy INT NOT NULL,
		DLUP DATETIME NOT NULL DEFAULT GETDATE(),
		UpdatedBy INT NULL,
		UpdatedDate DATETIME NULL
	);
END
GO



