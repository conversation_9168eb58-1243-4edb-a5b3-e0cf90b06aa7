/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class PurchaseRequisitions : Base {

		/// <summary>
		/// Gets the main data
		/// </summary>
		protected override void GetData() {
			JsonObject jsn = new JsonObject();

			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

			//get data	
			List<SalesOrderLine> lst = SalesOrderLine.DataListNuggetAsPurchaseRequisition(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
				//, GetFormValue_StringForNameSearch("Contact")
                 , GetFormValue_StringForNameSearchDecode("Contact")
				//, GetFormValue_StringForNameSearch("CMName")
                , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Salesman")
				, GetFormValue_NullableInt("SONoLo")
				, GetFormValue_NullableInt("SONoHi")
				, GetFormValue_NullableDateTime("DateOrderedFrom")
				, GetFormValue_NullableDateTime("DateOrderedTo")
			);

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format data
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].SalesOrderLineId);
				jsnRow.AddVariable("No", string.Format("{0}/{1}", lst[i].SalesOrderNumber, lst[i].SalesOrderLineId));
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode));
				jsnRow.AddVariable("Quantity", lst[i].BackOrderQuantity);
				jsnRow.AddVariable("DateOrdered", Functions.FormatDate(lst[i].DateOrdered));
				jsnRow.AddVariable("DatePromised", Functions.FormatDate(lst[i].DatePromised));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("Contact", lst[i].ContactName);
				jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
				jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
				jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Salesman");
			AddFilterState("SONo");
			AddFilterState("DateOrderedFrom");
			AddFilterState("DateOrderedTo");
			base.AddFilterStates();
		}

	}
}
