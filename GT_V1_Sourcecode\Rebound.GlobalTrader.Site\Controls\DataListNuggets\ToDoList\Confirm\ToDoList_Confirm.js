Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.initializeBase(this,[n]);this._aryToDoIDs=[]};Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.prototype={get_aryToDoIDs:function(){return this._aryToDoIDs},set_aryToDoIDs:function(n){this._aryToDoIDs!==n&&(this._aryToDoIDs=n)},get_strTitle_Delete:function(){return this._strTitle_Delete},set_strTitle_Delete:function(n){this._strTitle_Delete!==n&&(this._strTitle_Delete=n)},get_strTitle_MarkComplete:function(){return this._strTitle_MarkComplete},set_strTitle_MarkComplete:function(n){this._strTitle_MarkComplete!==n&&(this._strTitle_MarkComplete=n)},get_strTitle_MarkIncomplete:function(){return this._strTitle_MarkIncomplete},set_strTitle_MarkIncomplete:function(n){this._strTitle_MarkIncomplete!==n&&(this._strTitle_MarkIncomplete=n)},get_strExplanation_Delete:function(){return this._strExplanation_Delete},set_strExplanation_Delete:function(n){this._strExplanation_Delete!==n&&(this._strExplanation_Delete=n)},get_strExplanation_MarkComplete:function(){return this._strExplanation_MarkComplete},set_strExplanation_MarkComplete:function(n){this._strExplanation_MarkComplete!==n&&(this._strExplanation_MarkComplete=n)},get_strExplanation_MarkIncomplete:function(){return this._strExplanation_MarkIncomplete},set_strExplanation_MarkIncomplete:function(n){this._strExplanation_MarkIncomplete!==n&&(this._strExplanation_MarkIncomplete=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addModeChanged(Function.createDelegate(this,this.modeChanged))},dispose:function(){this.isDisposed||(this._ctlConfirm&&this._ctlConfirm.dispose(),this._aryToDoIDs=null,this._ctlConfirm=null,this._strTitle_Delete=null,this._strTitle_MarkComplete=null,this._strTitle_MarkIncomplete=null,this._strExplanation_Delete=null,this._strExplanation_MarkComplete=null,this._strExplanation_MarkIncomplete=null,Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},modeChanged:function(){switch(this._mode){case"DELETE":this.changeTitle(this._strTitle_Delete);this.changeExplanation(this._strExplanation_Delete);break;case"MARK_COMPLETE":this.changeTitle(this._strTitle_MarkComplete);this.changeExplanation(this._strExplanation_MarkComplete);break;case"MARK_INCOMPLETE":this.changeTitle(this._strTitle_MarkIncomplete);this.changeExplanation(this._strExplanation_MarkIncomplete)}},yesClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ToDo");n.set_DataObject("ToDo");switch(this._mode){case"DELETE":n.set_DataAction("Delete");break;case"MARK_COMPLETE":n.set_DataAction("Mark");n.addParameter("Complete",!0);break;case"MARK_INCOMPLETE":n.set_DataAction("Mark");n.addParameter("Complete",!1)}n.addParameter("IDs",$R_FN.arrayToSingleString(this._aryToDoIDs));n.addDataOK(Function.createDelegate(this,this.saveConfirmComplete));n.addError(Function.createDelegate(this,this.saveConfirmError));n.addTimeout(Function.createDelegate(this,this.saveConfirmError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.onNotConfirmed()},saveConfirmError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveConfirmComplete:function(n){n._result.Result==!0?this.onSaveComplete():this.saveConfirmError(n)}};Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);