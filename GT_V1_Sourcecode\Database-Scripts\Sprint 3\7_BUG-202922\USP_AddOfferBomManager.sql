SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('USP_AddOfferBomManager', 'P') IS NOT NULL
    DROP PROC dbo.USP_AddOfferBomManager
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-202402]		An.TranTan			03-May-2024		Update			FOR EMS offers: Insert more data from tbEMSOffers to tbAutoSource
[US-202577]		An.TranTan			16-May-2024		Update			FOR EMS offers: Add more fields to tbAutoSource to match HUBRFQ
[US-204008]		An.TranTan			22-May-2024		Update			Update get data from vwSourcingData to insert to tbAutoSource
[BUG-202922]	An.TranTan			03-Jun-2024		Update			Specific columns when insert into #tempSourcingData to avoid error when number of columns updated
																	- cover for all offers: manual/Future API/ Xmatch
[BUG-202922]	An.TranTan			21-Jun-2024		Update			Using ufn_get_fullpart to get fullpart to prevent subquery return 2 or more records
===========================================================================================
*/
CREATE Procedure [dbo].[USP_AddOfferBomManager]                        
(                                
	@BOMManagerNo int,                          
	@SourcingId int,                          
	@UpdatedBy int,                          
	@SourceType int,                    
	@OfferSource varchar(max) null,
	@CustomerRequirementId int = null
)                                
WITH RECOMPILE                                
AS                                
BEGIN                                
                                                          
BEGIN TRAN                              
BEGIN TRY                              
                              
CREATE TABLE #tempSourcingData
(
    BOMManagerNo int null,
    OfferId int null,
    FullPart nvarchar(30) COLLATE Latin1_General_CI_AS null,
    Part nvarchar(30) null,
    ManufacturerNo int null,
    DateCode nvarchar(5) null,
    ProductNo int null,
    PackageNo int null,
    Quantity int null,
    Price float null,
    OriginalEntryDate datetime null,
    Salesman int null,
    SupplierNo int null,
    CurrencyNo int null,
    ROHS tinyint null,
    UpdatedBy int null,
    DLUP datetime null,
    OfferStatusNo int null,
    OfferStatusChangeDate datetime null,
    OfferStatusChangeLoginNo int null,
    ManufacturerCode nvarchar(5) null,
    ProductName nvarchar(100) null,
    CurrencyCode nvarchar(5) null,
    CurrencyDescription nvarchar(30) null,
    SupplierName nvarchar(128) null,
    ManufacturerName nvarchar(50) null,
    SupplierEmail nvarchar(128) null,
    SalesmanName nvarchar(128) null,
    OfferStatusChangeEmployeeName nvarchar(128) null,
    PackageName nvarchar(60) null,
    Notes nvarchar(800) null,
    ClientNo int null,
    ClientId int null,
    ClientName nvarchar(128) null,
    ClientDataVisibleToOthers bit null,
    SupplierType nvarchar(250) null,
    ClientCode nvarchar(10) null,
    SPQ nvarchar(10) null,
    LeadTime nvarchar(50) null,
    ROHSStatus nvarchar(50) null,
    FactorySealed nvarchar(50) null,
	MSLLevelNo int null,
    MSL nvarchar(250) null,
    IPOBOMNo int null,
    SupplierTotalQSA nvarchar(20) null,
    SupplierLTB nvarchar(50) null,
    SupplierMOQ nvarchar(50) null,
    SupplierMessage nvarchar(400) null,
    SourcingType varchar(100),
    CustomerRequirementId int,
    Risk varchar(100),
    IsLock bit,
    BomStatus bit,
    POHubCompanyNo int,
	SupplierWarranty INT NULL,
	CountryOfOriginNo INT NULL,
	SellPrice FLOAT NULL,
	SellPriceLessReason NVARCHAR(MAX) NULL,
	ShippingCost FLOAT NULL,
	RegionNo INT NULL,
	DeliveryDate DATETIME NULL,
	TestingRecommended BIT NULL
)                               
                      
Declare @DeliveryDate datetime

create table #Parttemp
(
    PartNames varchar(100) COLLATE Latin1_General_CI_AS,
    CustomerRequirementId int,
    BomManagerNo int,
    DeliveryDate datetime
)

insert into #Parttemp
select cus.FullPart,
       CustomerRequirementId,
       BOMManagerNo,
       DatePromised
from dbo.tbCustomerRequirement cus
where cus.BOMManagerNo = @BOMManagerNo

select top 1  @DeliveryDate = DeliveryDate from #Parttemp

--for EMS offers   
if(@SourceType = 1)                          
begin
	DECLARE @SupplierType NVARCHAR(250) = '';
	IF(EXISTS(SELECT 1 FROM BorisGlobalTraderImports.dbo.tbOffer WHERE OfferId = @SourcingId))
	BEGIN
		SET @SupplierType = (SELECT TOP 1 ct.[Name]
							FROM tbCompanytype ct
							JOIN tbCompany c ON c.TypeNo = ct.CompanyTypeId
							JOIN BorisGlobalTraderImports.dbo.tbOffer o ON o.SupplierNo = c.CompanyId
							WHERE o.OfferId = @SourcingId);
	
		insert into #tempSourcingData
		(
		    BOMManagerNo,
		    OfferId,
		    FullPart,
		    Part,
		    ManufacturerNo,
		    DateCode,
		    ProductNo,
		    PackageNo,
		    Quantity,
		    Price,
		    OriginalEntryDate,
		    Salesman,
		    SupplierNo,
		    CurrencyNo,
		    ROHS,
		    UpdatedBy,
		    DLUP,
		    OfferStatusNo,
		    OfferStatusChangeDate,
		    OfferStatusChangeLoginNo,
		    ManufacturerCode,
		    ProductName,
		    CurrencyCode,
		    CurrencyDescription,
		    SupplierName,
		    ManufacturerName,
		    SupplierEmail,
		    SalesmanName,
		    OfferStatusChangeEmployeeName,
		    PackageName,
		    Notes,
		    ClientNo,
		    ClientId,
		    ClientName,
		    ClientDataVisibleToOthers,
		    SupplierType,
		    ClientCode,
		    SPQ,
		    LeadTime,
		    ROHSStatus,
		    FactorySealed,
		    MSLLevelNo,
		    MSL,
		    IPOBOMNo,
		    SupplierTotalQSA,
		    SupplierLTB,
		    SupplierMOQ,
		    SupplierMessage,
		    SourcingType,
		    CustomerRequirementId,
		    Risk,
		    IsLock,
		    BomStatus,
		    POHubCompanyNo,
		    SupplierWarranty,
		    CountryOfOriginNo,
		    SellPrice,
		    SellPriceLessReason,
		    ShippingCost,
		    RegionNo,
		    DeliveryDate,
		    TestingRecommended
		)		
		select 
			@BOMManagerNo
			,OfferId 
			,FullPart
			,Part                
			,a.ManufacturerNo                
			,DateCode
			,ProductNo
			,PackageNo
			,Quantity
			,Price
			,OriginalEntryDate
			,null as Salesman                                
			,SupplierNo
			,a.CurrencyNo
			,ROHS
			,a.UpdatedBy
			,a.DLUP
			,OfferStatusNo
			,OfferStatusChangeDate
			,OfferStatusChangeLoginNo
			,ma.ManufacturerCode                                
			,ProductName
			,cr.CurrencyCode
			,cr.CurrencyDescription
			,SupplierName
			,ma.ManufacturerName
			,null as SupplierEmail
			,null as SalesmanName
			,null as OfferStatusChangeEmployeeName                                
			,PackageName
			,a.Notes
			,a.ClientNo
			,a.ClientNo
			,null as ClientName
			,null as ClientDataVisibleToOthers
			,@SupplierType
			,null as ClientCode
			,SPQ
			,LeadTime
			,ROHSStatus
			,FactorySealed
			,MSLLevelNo
			,MSL
			,null as IPOBOMNo
			,SupplierTotalQSA
			,SupplierLTB
			,SupplierMOQ
			,'' as SupplierMessage
			,'Offers'		--SourcingType
			,@CustomerRequirementId
			,null			--Risk
			,null			--islock
			,null			--bomstatus
			,a.SupplierNo
			,a.SupplierWarranty
			,a.CountryOfOriginNo
			,a.SellPrice
			,a.SellPriceLessReason
			,a.ShippingCost
			,a.RegionNo
			,a.DeliveryDate
			,a.TestingRecommended
			from BorisGlobalTraderImports.dbo.tbOffer a
			left join dbo.tbCurrency cr on cr.CurrencyId = a.CurrencyNo
			left join dbo.tbManufacturer ma on ma.ManufacturerId = a.ManufacturerNo
			where a.OfferId = @SourcingId
	END
	ELSE
	BEGIN
		insert into #tempSourcingData
		(
		    BOMManagerNo,
		    OfferId,
		    FullPart,
		    Part,
		    ManufacturerNo,
		    DateCode,
		    ProductNo,
		    PackageNo,
		    Quantity,
		    Price,
		    OriginalEntryDate,
		    Salesman,
		    SupplierNo,
		    CurrencyNo,
		    ROHS,
		    UpdatedBy,
		    DLUP,
		    OfferStatusNo,
		    OfferStatusChangeDate,
		    OfferStatusChangeLoginNo,
		    ManufacturerCode,
		    ProductName,
		    CurrencyCode,
		    CurrencyDescription,
		    SupplierName,
		    ManufacturerName,
		    SupplierEmail,
		    SalesmanName,
		    OfferStatusChangeEmployeeName,
		    PackageName,
		    Notes,
		    ClientNo,
		    ClientId,
		    ClientName,
		    ClientDataVisibleToOthers,
		    SupplierType,
		    ClientCode,
		    SPQ,
		    LeadTime,
		    ROHSStatus,
		    FactorySealed,
		    MSLLevelNo,
		    MSL,
		    IPOBOMNo,
		    SupplierTotalQSA,
		    SupplierLTB,
		    SupplierMOQ,
		    SupplierMessage,
		    SourcingType,
		    CustomerRequirementId,
		    Risk,
		    IsLock,
		    BomStatus,
		    POHubCompanyNo,
		    SupplierWarranty,
		    CountryOfOriginNo,
		    SellPrice,
		    SellPriceLessReason,
		    ShippingCost,
		    RegionNo,
		    DeliveryDate,
		    TestingRecommended
		) 
		select 
			@BOMManagerNo
			,OfferId 
			,FullPart
			,Part                
			,a.ManufacturerNo                
			,DateCode
			,ProductNo
			,PackageNo
			,Quantity
			,Price
			,OriginalEntryDate
			,Salesman                                
			,SupplierNo
			,a.CurrencyNo
			,ROHS
			,a.UpdatedBy
			,a.DLUP
			,OfferStatusNo
			,OfferStatusChangeDate
			,OfferStatusChangeLoginNo
			,ma.ManufacturerCode                                
			,ProductName
			,cr.CurrencyCode
			,cr.CurrencyDescription
			,SupplierName
			,ma.ManufacturerName
			,SupplierEmail
			,SalesmanName
			,OfferStatusChangeEmployeeName                                
			,PackageName
			,a.Notes
			,a.ClientNo
			,a.ClientId
			,ClientName
			,ClientDataVisibleToOthers
			,SupplierType
			,ClientCode
			,null as SPQ
			,null LeadTime
			,null ROHSStatus
			,null FactorySealed
			,null MSLLevelNo
			,null as MSL
			,null as IPOBOMNo
			,null SupplierTotalQSA
			,null SupplierLTB
			,null SupplierMOQ
			,'' as SupplierMessage
			,'Offers'		--SourcingType
			,@CustomerRequirementId
			,null			--Risk
			,null			--islock
			,null			--bomstatus
			,a.SupplierNo
			,null as SupplierWarranty
			,null CountryOfOriginNo
			,0 as SellPrice
			,'' as SellPriceLessReason
			,null as ShippingCost
			,null RegionNo
			,null DeliveryDate
			,null as TestingRecommended
		from vwSourcingData a
		left join dbo.tbCurrency cr on cr.CurrencyId = a.CurrencyNo
		left join dbo.tbManufacturer ma on ma.ManufacturerId = a.ManufacturerNo
		where a.OfferId = @SourcingId
	END
end                                
                             
                             
--for APi offers                             
if(@SourceType = 2)                          
begin
 DECLARE @POHubCompanyNo INT;
 SET @POHubCompanyNo = (select top 1 CompanyId
						from tbCompany
						where CompanyName = 'Future Electronics'
						      and ClientNo = 114
						      and IsSupplier = 1
						      and POApproved = 1);
 if(@OfferSource = 'Manual Offer')                    
  begin                    
	insert into #tempSourcingData
	(
	    BOMManagerNo,
	    OfferId,
	    FullPart,
	    Part,
	    ManufacturerNo,
	    DateCode,
	    ProductNo,
	    PackageNo,
	    Quantity,
	    Price,
	    OriginalEntryDate,
	    Salesman,
	    SupplierNo,
	    CurrencyNo,
	    ROHS,
	    UpdatedBy,
	    DLUP,
	    OfferStatusNo,
	    OfferStatusChangeDate,
	    OfferStatusChangeLoginNo,
	    ManufacturerCode,
	    ProductName,
	    CurrencyCode,
	    CurrencyDescription,
	    SupplierName,
	    ManufacturerName,
	    SupplierEmail,
	    SalesmanName,
	    OfferStatusChangeEmployeeName,
	    PackageName,
	    Notes,
	    ClientNo,
	    ClientId,
	    ClientName,
	    ClientDataVisibleToOthers,
	    SupplierType,
	    ClientCode,
	    SPQ,
	    LeadTime,
	    ROHSStatus,
	    FactorySealed,
	    MSLLevelNo,
	    MSL,
	    IPOBOMNo,
	    SupplierTotalQSA,
	    SupplierLTB,
	    SupplierMOQ,
	    SupplierMessage,
	    SourcingType,
	    CustomerRequirementId,
	    Risk,
	    IsLock,
	    BomStatus,
	    POHubCompanyNo,
	    SupplierWarranty,
	    CountryOfOriginNo,
	    SellPrice,
	    SellPriceLessReason,
	    ShippingCost,
	    RegionNo,
	    DeliveryDate,
	    TestingRecommended
	)
	select a.BOMManagerNo,
	       a.APIID,
	       a.FullPart,
	       a.Part,
	       mf.ManufacturerId,
	       a.DateCode,
	       a.ProductNo,
	       a.PackageNo,
	       a.Quantity,
	       a.Price,
	       OriginalEntryDate,
	       a.Salesman,
	       SupplierNo,
	       a.CurrencyNo,
	       a.ROHS,
	       null,
	       a.DLUP,
	       OfferStatusNo,
	       OfferStatusChangeDate,
	       OfferStatusChangeLoginNo,
	       a.ManufacturerCode,
	       ProductName,
	       cr.CurrencyCode,
	       cr.CurrencyDescription,
	       SupplierName,
	       mf.ManufacturerName,
	       'SupplierEmail',
	       'SalesmanName',
	       'OfferStatusChangeEmployeeName',
	       PackageName,
	       'Notes',
	       a.ClientNo,
	       cl.ClientId,
	       cl.ClientName,
	       0,
	       0,
	       cl.ClientCode,
	       SPQ,
	       LeadTime,
	       ROHSStatus,
	       a.FactorySealed,
		   null,
	       a.MSL,
	       IPOBOMNo,
	       SupplierTotalQSA,
	       SupplierLTB,
	       SupplierMOQ,
	       'SupplierMessage',
	       'API-Manual',
	       b.customerrequirementid,
	       null,
	       null,
	       null,
	       @POHubCompanyNo,
		   null,	-- SupplierWarranty,
		   null,	-- CountryOfOriginNo,
		   null,	-- SellPrice,
		   null,	-- SellPriceLessReason,
		   null,	-- ShippingCost,
		   null,	-- RegionNo,
		   null,	-- DeliveryDate,
		   null		-- TestingRecommended
	from tbAPIOffers a
	    join tbClient cl
	        on a.ClientNo = cl.ClientId
	    join tbCustomerRequirement b
	        on b.BOMManagerNo = @BOMManagerNo
	           and a.FullPart = b.FullPart
	    join tbCurrency cr
	        on a.CurrencyNo = cr.CurrencyId
	    join tbManufacturer mf
	        on a.ManufacturerNo = mf.ManufacturerId
	where a.APIID = @SourcingId
  end                    
  if(@OfferSource = 'Future Electronics')                    
  begin                    
	Declare 
			@PackageId INT,
			@Package nvarchar(30),
			@ManufacturerId INT,
			@ManufacturerName nvarchar(max), 
			@DateCode int, 
			@ROHS nvarchar(10),
			@FeUnitPrice FLOAT,
			@FeCurrencyNo INT;                    
                       
	(SELECT @Package = [packageType],
	        @ManufacturerName = [manufacturerName],
	        @DateCode = [dateCode],
	        @ROHS = [rohs]
	 FROM
	 (
	     SELECT [name],
	            [value],
	            [SupplierAPINo]
	     FROM tbSupplierAPIPartAttributes
	     where SupplierAPINo = @SourcingId
	 ) PartAttributes
	 PIVOT
	 (
	     min([value])
	     FOR [name] IN ([packageType], [manufacturerName], [dateCode], [rohs], [leadFree], [description (en)], ECCN)
	 ) AS PivotTable)
	 
	 SET @ManufacturerId =(SELECT TOP 1 ManufacturerId FROM tbManufacturer mf WHERE mf.manufacturerName = @ManufacturerName);
	 SET @PackageId = (select top 1 PackageId from tbPackage where PackageName = @Package);
	 SET @FeUnitPrice = (select top 1
					    CAST(UnitPrice AS FLOAT)
					from tbSupplierAPIPricing
					where SupplierAPINo = @SourcingId
					order by QuantityFrom);
	 SET @FeCurrencyNo = (
						     select top 1
						         c.CurrencyId
						     from tbCurrency c
							 JOIN tbSupplierAPI tbs ON tbs.CurrencyCode = c.CurrencyCode
						     where tbs.SupplierAPIId = @SourcingId
						           and c.ClientNo = 114
						 );

                    
	insert into #tempSourcingData
	(
	    BOMManagerNo,
	    OfferId,
	    FullPart,
	    Part,
	    ManufacturerNo,
	    DateCode,
	    ProductNo,
	    PackageNo,
	    Quantity,
	    Price,
	    OriginalEntryDate,
	    Salesman,
	    SupplierNo,
	    CurrencyNo,
	    ROHS,
	    UpdatedBy,
	    DLUP,
	    OfferStatusNo,
	    OfferStatusChangeDate,
	    OfferStatusChangeLoginNo,
	    ManufacturerCode,
	    ProductName,
	    CurrencyCode,
	    CurrencyDescription,
	    SupplierName,
	    ManufacturerName,
	    SupplierEmail,
	    SalesmanName,
	    OfferStatusChangeEmployeeName,
	    PackageName,
	    Notes,
	    ClientNo,
	    ClientId,
	    ClientName,
	    ClientDataVisibleToOthers,
	    SupplierType,
	    ClientCode,
	    SPQ,
	    LeadTime,
	    ROHSStatus,
	    FactorySealed,
	    MSLLevelNo,
	    MSL,
	    IPOBOMNo,
	    SupplierTotalQSA,
	    SupplierLTB,
	    SupplierMOQ,
	    SupplierMessage,
	    SourcingType,
	    CustomerRequirementId,
	    Risk,
	    IsLock,
	    BomStatus,
	    POHubCompanyNo,
	    SupplierWarranty,
	    CountryOfOriginNo,
	    SellPrice,
	    SellPriceLessReason,
	    ShippingCost,
	    RegionNo,
	    DeliveryDate,
	    TestingRecommended
	)
	select @BOMManagerNo,
	       tbs.SupplierAPIId,
	       dbo.ufn_get_fullpart(tbs.PartNumber),
	       tbs.PartNumber,
	       @ManufacturerId,
	       @DateCode,
	       null, --Product                    
	       @PackageId,
	       tbs.QuantityAvailable,
	       @FeUnitPrice,
	       tbs.DLUP,
	       0,    --Salesman                    
	       @POHubCompanyNo,        --Supplier No
	       @FeCurrencyNo,
	       null, --@ROHS                    
	       null, --UpdateBy
	       tbs.DLUP,
	       null, --OfferStatusNo                    
	       null, --OfferStatusChangeDate                    
	       null, --OfferStatusChangeLoginNo                    
	       null, --ManufacturerCode       
	       null, --ProductName                    
	       tbs.CurrencyCode,
	       null, --cr.CurrencyDescription                    
	       tbs.DatasourceName,
	       @ManufacturerName,
	       'SupplierEmail',
	       'SalesmanName',
	       'OfferStatusChangeEmployeeName',
	       @Package,
	       'Notes',
	       tbs.ClientNo,
	       cl.ClientId,
	       cl.ClientName,
	       0,
	       0,
	       cl.ClientCode,
	       null, --SPQ                    
	       CONCAT(tbs.FactoryLeadTime, ' ', tbs.FactoryLeadTimeUnits),
	       @ROHS,
	       null, --FactorySealed
		   null, --MSLLevelNo
	       null, --MSL                    
	       null, --IPOBOMNo                    
	       tbs.QuantityAvailable,
	       null, -- SupplierLTB                    
	       tbs.QuantityMinimum,
	       'SupplierMessage',
	       'API-FE',
	       --(
	       --    select CustomerRequirementID
	       --    from tbcustomerRequirement
	       --    where part = tbs.PartNumber
	       --          and BOMManagerNo = @BOMManagerNo
	       --),
		   @CustomerRequirementId,
	       null,
	       null,
	       null,
	       @POHubCompanyNo,
		   null, -- SupplierWarranty,
		   null, -- CountryOfOriginNo,
		   null, -- SellPrice,
		   null, -- SellPriceLessReason,
		   null, -- ShippingCost,
		   null, -- RegionNo,
		   null, -- DeliveryDate,
		   null -- TestingRecommended
	from tbSupplierAPI tbs
	    join tbClient cl
	        on tbs.ClientNo = cl.ClientId
	where tbs.SupplierAPIId = @SourcingId
  end                     
end                    
                              
--for Xmatch               
if(@SourceType = 3)                          
begin                          
	insert into #tempSourcingData
	(
	    BOMManagerNo,
	    OfferId,
	    FullPart,
	    Part,
	    ManufacturerNo,
	    DateCode,
	    ProductNo,
	    PackageNo,
	    Quantity,
	    Price,
	    OriginalEntryDate,
	    Salesman,
	    SupplierNo,
	    CurrencyNo,
	    ROHS,
	    UpdatedBy,
	    DLUP,
	    OfferStatusNo,
	    OfferStatusChangeDate,
	    OfferStatusChangeLoginNo,
	    ManufacturerCode,
	    ProductName,
	    CurrencyCode,
	    CurrencyDescription,
	    SupplierName,
	    ManufacturerName,
	    SupplierEmail,
	    SalesmanName,
	    OfferStatusChangeEmployeeName,
	    PackageName,
	    Notes,
	    ClientNo,
	    ClientId,
	    ClientName,
	    ClientDataVisibleToOthers,
	    SupplierType,
	    ClientCode,
	    SPQ,
	    LeadTime,
	    ROHSStatus,
	    FactorySealed,
	    MSLLevelNo,
	    MSL,
	    IPOBOMNo,
	    SupplierTotalQSA,
	    SupplierLTB,
	    SupplierMOQ,
	    SupplierMessage,
	    SourcingType,
	    CustomerRequirementId,
	    Risk,
	    IsLock,
	    BomStatus,
	    POHubCompanyNo,
	    SupplierWarranty,
	    CountryOfOriginNo,
	    SellPrice,
	    SellPriceLessReason,
	    ShippingCost,
	    RegionNo,
	    DeliveryDate,
	    TestingRecommended
	)
	select a.BOMManagerNo,
	       a.SalesXMatchID,
	       a.FullPart,
	       a.Part,
	       a.ManufacturerNo,
	       a.DateCode,
	       a.ProductNo,
	       a.PackageNo,
	       a.Quantity,
	       a.UnitPrice, --a.Price,                  
	       OriginalEntryDate,
	       a.Salesman,
	       SupplierNo,
	       a.CurrencyNo,
	       a.ROHS,
	       null,
	       a.DLUP,
	       OfferStatusNo,
	       OfferStatusChangeDate,
	       OfferStatusChangeLoginNo,
	       0,           -- mf.ManufacturerCode,                  
	       ProductName,
	       0,           --cr.CurrencyCode,                  
	       '',          --cr.CurrencyDescription,                  
	       'SupplierName' SupplierName,
	       '',          --mf.ManufacturerName,                  
	       'SupplierEmail',
	       'SalesmanName',
	       'OfferStatusChangeEmployeeName',
	       PackageName,
	       'Notes',
	       a.ClientNo,
	       cl.ClientId,
	       cl.ClientName,
	       0,
	       0,
	       cl.ClientCode,
	       SPQ,
	       LeadTime,
	       ROHSStatus,
	       a.FactorySealed,
		   a.MSLLevelNo,
	       a.MSL,
	       IPOBOMNo,
	       SupplierTotalQSA,
	       SupplierLTB,
	       SupplierMOQ,
	       'SupplierMessage',
	       'XMatch / Sourcing',
	       b.CustomerRequirementId,
	       null,
	       null,
	       null,
	       a.supplierNo,
		   null,-- SupplierWarranty,
		   null,-- CountryOfOriginNo,
		   null,-- SellPrice,
		   null,-- SellPriceLessReason,
		   null,-- ShippingCost,
		   null,-- RegionNo,
		   null,-- DeliveryDate,
		   null-- TestingRecommended
	from tbSalesXMatch a
	    join tbClient cl
	        on a.ClientNo = cl.ClientId
	    join tbCustomerRequirement b
	        on b.CustomerRequirementId = @CustomerRequirementId
	where a.SalesXMatchID = @SourcingId                        
end                                          
                  
SELECT 
	   Dense_rank() over (partition by fullpart order by DLUP desc, price desc) as RowNumber,
       ROW_NUMBER() OVER (partition by fullpart order by DLUP desc, price desc) AS RowID,
	   OfferId,
       SupplierName,
       Fullpart,
       ManufacturerName,
       cast(price as decimal(30, 5)) as Price,
       cast(ISNULL(SellPrice, 0.0) as decimal(30, 5)) as Resale,
       cast(0.0 as decimal(30, 5)) as Profit,
       cast(0.0 as decimal(30, 5)) as Margin,
       OriginalEntryDate,
       --supplierMOQ,
       SPQ,
       Quantity as ADJQty,
       cast(0.0 as decimal(30, 5)) as StockQty,
       cast(0.0 as decimal(30, 5)) as Excess,
       cast(0.0 as decimal(30, 5)) as [Value],
       --'' as DC,
       --0 as LTDays,
       DLUP,
       SourcingType,
       ProductNo,
       PackageNo,
       ManufacturerNo,
       --rohs,
       BOMManagerNo,
       ClientCode,
       ClientNo,
       Salesman,
       CustomerRequirementId,
       Risk,
       Notes,
       IsLock,
       BomStatus,
       SupplierNo,
       CurrencyNo,
       POHubCompanyNo,
	   DateCode,
	   SupplierTotalQSA,
	   SupplierLTB,
	   SupplierMOQ,
	   LeadTime,
	   ROHS,
	   FactorySealed,
	   MSL,
	   MSLLevelNo
	   ,OfferStatusNo
	   ,OfferStatusChangeDate
	   ,OfferStatusChangeLoginNo
	   ,SupplierWarranty
	   ,CountryOfOriginNo
	   ,SellPrice
	   ,SellPriceLessReason
	   ,cast(ISNULL(ShippingCost,0.0) as decimal(30, 5)) as ShippingCost
	   ,RegionNo
	   ,DeliveryDate
	   ,ROHSStatus
	   ,TestingRecommended
INTO #tempRawData
FROM #tempSourcingData
ORDER BY Fullpart, RowNumber                              
                      
                        
select * into #tempLowestPrice from #tempRawData where rownumber = 1 and rowid =1                                 
                                      
update #tempLowestPrice set  spq = case when isnull(spq,'')='' then 0 else case when TRY_PARSE(spq AS INT)<> NULL then convert(int, convert(float,spq)) else 0 end  end     --   where isnull(spq,'')=''                             
                                                      
update #tempLowestPrice                                 
set spq = case when isnull(spq,'')='' then 0 else spq end                                
, SupplierMOQ = case when isnull(SupplierMOQ,'')='' then 0 else convert(int, convert(float,SupplierMOQ)) end                                
                          
                                
-- ADJQTY                                
--update a                                 
--set ADJQty =                                
--case when TRY_PARSE(a.spq as int) is not null then                                
-- case when a.spq>0 then  b.Quantity else b.Quantity end                                
-- else 0 end ,                       
-- a.customerrequirementid = b.CustomerRequirementId                                
--from #tempLowestPrice a join tbCustomerRequirement b on a.customerrequirementid= b.customerrequirementid      
----and a.FullPart =b.FullPart      
--where b.BOMManagerNo = @BOMManagerNo                                
                                                                
--profit                                
update  a                                 
set Profit =                               
--cast(case when a.price>0                                 
--   then                               
   (cast(a.Resale as decimal(30,5)) - cast(a.Price   as decimal(30,5)))* ADJQty                            
   --else 0 end as decimal(30,5))                                
from #tempLowestPrice a join tbCustomerRequirement b on a.customerrequirementid= b.customerrequirementid       
--and a.FullPart =b.FullPart      
where b.BOMManagerNo = @BOMManagerNo                                
                                         
--Margin                                
update a                                 
set Margin =                              
case when a.Resale >0 then                              
cast(cast(100 as decimal(30,5))-((cast(100  as decimal(30,5))/cast(a.Resale  as decimal(30,5))* cast(a.Price  as decimal(30,5)))) as decimal(30,5))                                 
else 0 end                              
from #tempLowestPrice a join tbCustomerRequirement b on a.customerrequirementid= b.customerrequirementid       
--and a.FullPart =b.FullPart       
where b.BOMManagerNo = @BOMManagerNo                              
                                
--Excess                                
update a                                 
set Excess=                              
case when b.Quantity >0 then                              
cast((                                
cast(100 as decimal(30,5))                                
/cast(b.Quantity as decimal(30,5))                                
* ADJQty)                                 
/cast(100 as decimal(30,5)) as decimal(30,5))                                
else 0 end                              
from #tempLowestPrice a join tbCustomerRequirement b on a.customerrequirementid= b.customerrequirementid      
--and a.FullPart =b.FullPart       
where b.BOMManagerNo = @BOMManagerNo                                
                                                                                        
-- Value                                
update #tempLowestPrice                                 
set [Value]= ADJQty * cast(Resale as decimal(30,5))                                
                                
update a set a.suppliername = b.companyname
from #tempLowestPrice  a join tbcompany b on a.supplierno = b.companyid
where isnull(a.suppliername,'')=''

declare @AuToSourceId INT;     
insert into tbAutoSource 
	( offerid
	, VendorName
	, VendorCategory
	, VendorType
	, ManufacturerName
	, FullPart
	, part
	, ProductNo
	, PackageNo
	, ManufacturerNo
	, cost
	, Resale
	, Profit
	, Margin
	, SupplierMOQ
	, SupplierTotalQSA
	, SupplierLTB
	, SPQ
	, MOQ
	, MSL
	, ROHS
	, ADJQty
	, Excess
	, StockQty
	, DateCode
	, LT
	, Risk
	, Notes
	, UpdatedBy
	, DLUP
	, BOMManagerNo
	, CustomerRequirementId
	, Islock
	, BOMStatus
	, CurrencyNo
	, SupplierNo
	, DeliveryDate
	, POHubCompanyNo
	, FactorySealed
	, MSLLevelNo
	, OfferStatusNo
	, OfferStatusChangeDate
	, OfferStatusChangeLoginNo
	, SupplierWarranty
	, IHSCountryOfOriginNo
	, EstimatedShippingCost
	, RegionNo
	, ROHSStatus
	, TestRecommended)                                
select 
	OfferId
	,SupplierName					--VendorName
	,SourcingType					--VendorCategory		
	,1								--VendorType
	, ManufacturerName
	,FullPart
	,FullPart						--part
	,ProductNo
	,PackageNo
	,ManufacturerNo
	,Price							--cost
	,Resale
	,isnull(Profit,0)				
	,Margin
	,SupplierMOQ
	,SupplierTotalQSA
	,SupplierLTB
	,SPQ
	,SupplierMOQ
	,MSL							
	,ROHS
	,ADJQty
	,Excess
	,StockQty
	,DateCode								--DateCode
	,LeadTime							--LT
	,Risk
	,Notes
	,null							--UpdatedBy
	,DLUP
	,@BOMManagerNo
	,customerrequirementid
	,islock
	,bomstatus
	,CurrencyNo
	,SupplierNo
	,ISNULL(DeliveryDate, @DeliveryDate)
	,POHubCompanyNo
	,FactorySealed
	,MSLLevelNo
	,OfferStatusNo
	,OfferStatusChangeDate
	,OfferStatusChangeLoginNo
	,SupplierWarranty
	,CountryOfOriginNo
	,ShippingCost
	,RegionNo
	,ROHSStatus
	,TestingRecommended
from #tempLowestPrice --where FullPart not in (select fullpart from tbAutoSource where BOMManagerNo = @BOMManagerNo)                               
 
SET @AuToSourceId = Scope_identity();
                  
update cr                        
set                        
REQStatus = 3                        
from                        
tbCustomerRequirement cr                        
join #tempLowestPrice b on b.customerrequirementid = cr.CustomerRequirementId                        
where cr.BOMManagerNo = @BOMManagerNo and cr.CustomerRequirementId= b.CustomerRequirementId --and cr.FullPart = b.FullPart                        
                        
                    
declare @bommanagerstatus int                    
select @bommanagerstatus = [Status] from tbbommanager where BOMManagerId = @BOMManagerNo                    
                    
if(@bommanagerstatus < 3)                
begin                    
	update tbBomManager                    
	set [Status] = 3              
	where BOMManagerId = @BOMManagerNo              
end

/*Insert reason why cost price > resale price to tbAudit_BOMManager*/
INSERT INTO dbo.tbAudit_BOMManager                      
  (BOMManagerNo            
  ,CustomerRequirementNo            
  ,AutoSourceNo              
  ,Reason              
  ,CostPrice            
  ,ResellPrice            
  ,FullPart            
  ,UpdatedBy            
  ,DLUP )
SELECT 
	@BOMManagerNo,
	p.CustomerRequirementId,
	@AuToSourceId,
	p.SellPriceLessReason,
	p.Price,
	p.Resale,
	p.FullPart,
	@UpdatedBy,
	GETDATE()
FROM #tempLowestPrice p
WHERE p.Price > p.Resale AND ISNULL(p.SellPriceLessReason, '') <> ''

drop table #tempSourcingData                                
drop table #tempRawData                                
drop table #tempLowestPrice                                
                               
select 'Success' as 'Status','Offer Added.' as 'Message'                              
                          
commit tran                              
                            
end try                              
begin catch                              
	rollback tran                              
	select 'Fail' as 'Status',ERROR_MESSAGE() as 'Message'            
end catch                            
end 
GO


