Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.initializeBase(this,[n]);this._intCompanyID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intCompanyID=null,Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/MfrGroupBySupplier");this._objData.set_DataObject("MfrGroupBySupplier");this._objData.set_DataAction("GetData");this._objData.addParameter("SupplierId",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.Items)for(n=0;n<t.Items.length;n++)this.addOption(t.Items[n].Name,t.Items[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.MfrGroupBySupplier",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);