﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Services;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data
{
    /// <summary>
    /// Summary description for GILineBarcodesStatus
    /// </summary>
    /// 
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]

    public class GILineBarcodesStatus : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            SetDropDownType("GILineBarcodesStatus");
            base.ProcessRequest(context);
        }
        protected override void GetData()
        {
            try
            {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.GoodsInLine> lst = BLL.GoodsInLine.GetGILineBarcodeStatusDropdown(loginId: SessionManager.LoginID, clientId: SessionManager.ClientID);

                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].GIBarcodeScanStatusId);
                    jsnItem.AddVariable("Name", lst[i].GIBarcodesScanStatusName);
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                lst.Clear(); lst = null;
                jsn.AddVariable("Types", jsnList);
                jsnList.Dispose(); jsnList = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;

            }
            catch (Exception ex)
            {
                new Errorlog().LogMessage("Error Occured at EIStatus handler " + ex.InnerException.Message);
            }


        }


    }

}