///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 17.11.2009:
// - get the YTD / last year values in one hit
// - don't get data straight away because the Company Detail page now switches tabs
//   with javascript


/*

Marker     Changed By         Date          Remarks

[001]      Pankaj Kumar       27/10/20011   ESMS Ref:10 - Data drop out since Thursday

*/
//-------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.initializeBase(this, [element]);
	
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.prototype = {
  
    get_tblUploadHistory: function() { return this._tblUploadHistory; }, set_tblUploadHistory: function(value) { if (this._tblUploadHistory !== value) this._tblUploadHistory = value; },
   
    initialize: function() {
     Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.callBaseMethod(this, "initialize");
       //data
        this._strPathToData = "controls/Nuggets/CsvUploadHistory";
        this._strDataObject = "CsvUploadHistory";

       this.addRefreshEvent(Function.createDelegate(this, this.getHistory));
     //this.showRefresh(false);
     this.showLoading(false);
     this.showContent(true);
     this.showContentLoading(false);
     this.getHistory();
    },

    dispose: function() {
        if (this.isDisposed) return;
       
        if (this._tblUploadHistory) this._tblUploadHistory.dispose();
      
        this._tblUploadHistory = null;
       
        Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.callBaseMethod(this, "dispose");
    },
    getHistory: function() {
        this.showLoadingHistory(true);
     this.showContent(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strPathToData);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLog");
        obj.addDataOK(Function.createDelegate(this, this.getHistoryOK));
        obj.addError(Function.createDelegate(this, this.getHistoryError));
        obj.addTimeout(Function.createDelegate(this, this.getHistoryError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getHistoryOK: function(args) {
        res = args._result;
          this.showLoading(false);     
        this._tblUploadHistory.clearTable();
        this.processHistory(this._tblUploadHistory);
        this._tblUploadHistory.resizeColumns();
    },

    getHistoryError: function(args) {
          this.showLoading(false);
        this.showHistoryError(true, args.get_ErrorMessage());
    },

    showLoadingHistory: function(blnShow) {
      
        this.showLoading(blnShow);
      
    },

  

    showHistoryGetData: function(blnShow) {
      
        if (blnShow) {
            this.showLoading(false);
          
        }
    },

    processHistory: function(tbl) {
        if (res.CreditHist) {
            for (var i = 0; i < res.CreditHist.length; i++) {
                var row = res.CreditHist[i];
                var aryData = [
					$R_FN.setCleanTextValue(row.Message),			
					row.Date 
						];
                tbl.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
    }

 
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CsvUploadHistory", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
