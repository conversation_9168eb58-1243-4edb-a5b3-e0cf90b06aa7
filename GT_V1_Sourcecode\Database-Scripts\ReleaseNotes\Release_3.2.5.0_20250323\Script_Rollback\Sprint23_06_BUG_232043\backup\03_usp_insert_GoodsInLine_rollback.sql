﻿CREATE OR ALTER PROCEDURE [dbo].[usp_insert_GoodsInLine]                
    /******************************************************************************************                                                     
Espire: 02 Jan 2012                                                     
        Added proc to update close status of CRMA and CRMA line                                     
Espire: 03 Aug 2015                                   
Pull Division No from PO into stock                        
                    
Marker              Owner               Date                Remarks                  
[RP-881]               Ravi <PERSON>     16-11-2023         Quarantine stock if Company is 'On Stop' in Purchasing information section (go to details tab of company)                  
[005]               <PERSON>        27-07-2023          RP-2230 - AS6081 Shipping/ Warehouse            
                  
******************************************************************************************/                
    @GoodsInNo INT                                                       
  ,                
    @PurchaseOrderLineNo INT = NULL                                                       
  ,                
    @Part NVARCHAR(30)                                                       
  ,                
    @ManufacturerNo INT = NULL                                                       
  ,                
    @DateCode NVARCHAR(5) = NULL                                                       
  ,                
    @PackageNo INT = NULL                                                       
  ,                
    @Quantity INT                                                       
  ,                
    @Price FLOAT                                                       
  ,                
    @ShipInCost FLOAT = NULL                                                       
  ,                
    @QualityControlNotes NVARCHAR(MAX) = NULL                                                       
  ,                
    @Location NVARCHAR(10)                                                       
  ,                
    @LotNo INT = NULL                                                       
  ,                
    @ProductNo INT = NULL                                                       
  ,                
    @CurrencyNo INT                                                       
  ,                
    @CustomerRMALineNo INT = NULL                                                       
  ,                
    @SupplierPart NVARCHAR(30) = NULL                                                       
  ,                
    @ROHS TINYINT  = NULL                                                     
  ,                
    @CountryOfManufacture INT = NULL                                                       
  ,                
    @Unavailable BIT                                                       
  ,                
    @Notes NVARCHAR(128) = NULL                                                       
  ,                
    @ChangedFields NVARCHAR(MAX) = NULL                                                       
  ,                
    @CountingMethodNo INT                                                       
  ,                
    @SerialNosRecorded BIT                                                       
  ,                
    @PartMarkings NVARCHAR(50) = NULL                                                       
  ,                
    @UpdatedBy INT = NULL                               
  ,                
    @ClientPrice FLOAT = NULL                             
  ,                
    @ReqSerialNo BIT  = 0                
  ,                
    @GoodsInLineId INT OUTPUT                           
 ,                
    @strMessage nvarchar(300) OUTPUT                
                
AS                
                
BEGIN                
    SET NOCOUNT ON                
    SET TRANSACTION ISOLATION LEVEL REPEATABLE READ                
    --------------------------------------------------------------                     
    --[001] code start                                   
    --ihs data passing start                                
    declare                                   
    --@CountryOfOrigin nvarchar(50)=null,                                         
    @CountryOfOriginNo int=null,                                         
    @LifeCycleStage nvarchar(50)=null,                                         
    @HTSCode varchar(20)=null,                                        
    @AveragePrice  float=null,                                         
    @Packing varchar(60)=null,                                         
    @PackagingSize varchar(100)=null  ,                                     
    @Descriptions nvarchar(max)=null ,                               
    @IHSProduct varchar(100)=null  ,                
    @ECCNCode varchar(100)=null,                           
    @DivisionHeaderNo int = null,                    
    @StockIds int = NULL--[003]                    
    , @AS6081 bit = 0 --[005]            
                
    if(@PurchaseOrderLineNo is not null)                                                   
    begin                
        select                
            --@CountryOfOrigin =  CountryOfOrigin,                                                   
            @CountryOfOriginNo = CountryOfOriginNo,                
            @LifeCycleStage = LifeCycleStage,                
            @HTSCode = HTSCode,                
            @AveragePrice  = AveragePrice,                
            @Packing = Packing,                
            @PackagingSize = PackagingSize,                
            @Descriptions =Descriptions   ,                
            @IHSProduct=IHSProduct,                
            @ECCNCode  = ECCNCode              
   , @AS6081 = AS6081 --[005]            
        from tbPurchaseOrderLine                
        where PurchaseOrderLineId = @PurchaseOrderLineNo                
    end                
    --[003] ihs code end                                    
    --[001] code end                        
    --------------------------------------------------------------                     
                
    DECLARE @IsPOReceive BIT                                                       
            , @QuantityOutstanding INT                
    -- is this a PO receive?                                                       
    SET @IsPOReceive = 1                
    IF ISNULL(@CustomerRMALineNo, 0) > 0                                                        
        SET @IsPOReceive = 0                
    --get correct price for landed cost and quantity outstanding                                                       
    DECLARE @LandedCost FLOAT,                                 
                @ClientLandedCost FLOAT,                                 
                @IPOCurrencyNo INT  ,                         
        -- Apply PO bank fee,                         
                @TermPOBankFee FLOAT,                         
                @POBankFee FLOAT ,                         
                @IsApplyPOBankFee bit,                         
                @IsPOBankFeeApplyInGI bit                
    DECLARE @LinkMultiCurrencyNo INT                
    SET @POBankFee = NULL                
    SET @LinkMultiCurrencyNo = NULL                
    DECLARE @MSL nvarchar(70)                
                
    SET @strMessage=''                
                
                
    IF @IsPOReceive = 1                                                        
                                 
        BEGIN                
                
        DECLARE @ApplyDuty BIT                
                
        SET @ApplyDuty = (SELECT ISNULL(cn.Duty, 0)                
        FROM dbo.tbPurchaseOrderLine pol                
            JOIN dbo.tbPurchaseOrder po ON po.PurchaseOrderId = pol.PurchaseOrderNo                
            LEFT JOIN dbo.tbCountry cn ON cn.CountryId = po.ImportCountryNo                
        WHERE     pol.PurchaseOrderLineId = @PurchaseOrderLineNo)                
                
        -- Check Bank Fee apply or Not                         
        declare @PurchaseOrderNo int                
        SELECT @IsApplyPOBankFee = ISNULL(c.IsApplyPOBankFee,0), @TermPOBankFee = c.POBankFee, @PurchaseOrderNo = b.PurchaseOrderId, @MSL = a.MSLLevel                
        FROM dbo.tbPurchaseOrderLine a                
            JOIN tbPurchaseOrder b ON a.PurchaseOrderNo = b.PurchaseOrderId                
            JOIN tbTerms c on b.TermsNo = c.TermsId                
        WHERE   a.PurchaseOrderLineId = @PurchaseOrderLineNo                
                
        IF @IsApplyPOBankFee = 1                         
            BEGIN                
            DECLARE @COUNT INT                
            SELECT @COUNT =  COUNT(GoodsInLineId)                
            FROM tbGoodsInLine a JOIN tbPurchaseOrderLine b                
                ON a.PurchaseOrderLineNo = b.PurchaseOrderLineId                
            where b.PurchaseOrderNo = @PurchaseOrderNo AND NOT POBankFee IS NULL                
            IF ISNULL(@COUNT,0) = 0                          
                    BEGIN                
                SET @POBankFee = @TermPOBankFee           
           END                
        END                
                
        set @ShipInCost = @ShipInCost + isnull(@POBankFee,0)                
        --for PO Receive do standard Landed Cost calculation                          
        SET @LandedCost = dbo.ufn_calculateLandedCost(@Quantity, GETDATE(), @CurrencyNo, @Price, @ShipInCost, @ApplyDuty, @ProductNo)                
                
               
        -- BY Espire 11 May 2016                               
        IF EXISTS(SELECT ipol.InternalPurchaseOrderLineId                
        FROM tbPurchaseOrderLine pol                
            JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo                
        WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineNo)                                 
            BEGIN                
                
            DECLARE @IPOClientNo int                
            DECLARE @IPOCountryNo int                
                
            -- DECLARE @GlobalCountryNo int                            
                
            SELECT @IPOCurrencyNo = ipo.CurrencyNo, @IPOClientNo = ipo.ClientNo, @IPOCountryNo=ipo.ImportCountryNo,                
                @LinkMultiCurrencyNo = ipo.LinkMultiCurrencyNo                
            FROM tbInternalPurchaseOrder ipo JOIN tbInternalPurchaseOrderLine ipol                
                ON ipo.InternalPurchaseOrderId = ipol.InternalPurchaseOrderNo                
            WHERE ipol.PurchaseOrderLineNo = @PurchaseOrderLineNo                
                
            DECLARE @ApplyDutyInClient BIT                
                
            SET @ApplyDutyInClient = (SELECT ISNULL(cn.Duty, 0)                
            FROM dbo.tbPurchaseOrderLine pol                
                JOIN dbo.tbPurchaseOrder po ON po.PurchaseOrderId = pol.PurchaseOrderNo                
                LEFT JOIN dbo.tbCountry cn ON cn.CountryId = @IPOCountryNo                
            WHERE     pol.PurchaseOrderLineId = @PurchaseOrderLineNo)                
                
            SET @ClientLandedCost = dbo.ufn_calculateLandedCost(@Quantity, GETDATE(), @IPOCurrencyNo, @ClientPrice, @ShipInCost, @ApplyDutyInClient, @ProductNo)                
                
            -- dbo.ufn_get_exchange_rate(@CurrencyNo, getdate())                         
            SET @LandedCost = @Price / dbo.ufn_get_exchange_rate(@CurrencyNo, getdate())                
        END                           
   ELSE                         
   BEGIN                
            SET @ClientPrice = NULL                
            SET @ClientLandedCost = NULL                
        END                
                
        --get line quantity yet to be received                                                       
                
        SELECT @QuantityOutstanding = ISNULL(Quantity, 0) - (SELECT ISNULL(SUM(Quantity), 0)                
            FROM dbo.tbGoodsInLine                
            WHERE PurchaseOrderLineNo = @PurchaseOrderLineNo)                
        FROM dbo.tbPurchaseOrderLine                
        WHERE   PurchaseOrderLineId = @PurchaseOrderLineNo                
                
    END                                    
    ELSE                                      
        BEGIN                
                
        DECLARE @GoodsInCurrencyNo INT                           
   , @ClientBaseCurrencyNo INT                
                
        --for CRMA Receive, get Landed Cost from Invoice Line                                               
          
        SELECT @Price = LandedCost                
        FROM tbCustomerRMALine cln                
            JOIN vwInvoiceLine il ON cln.InvoiceLineNo = il.InvoiceLineId                
        WHERE   cln.CustomerRMALineId = @CustomerRMALineNo                
                
        --get Client Base Currency                                               
                
        SELECT @ClientBaseCurrencyNo = cl.CurrencyNo                
        FROM tbCustomerRMALine cln                
JOIN tbInvoiceLine il ON cln.InvoiceLineNo = il.InvoiceLineId                
            JOIN tbInvoice iv ON iv.InvoiceId = il.InvoiceNo                
            JOIN tbClient cl ON iv.ClientNo = cl.ClientId                
        WHERE   cln.CustomerRMALineId = @CustomerRMALineNo                
                
        --calculate Landed Cost (without duty)                                           
                
        SET @LandedCost = dbo.ufn_calculateLandedCost(@Quantity, GETDATE(), @ClientBaseCurrencyNo, @Price, @ShipInCost, 0, @ProductNo)                
                
                
        -- Need to change in @ClientLandedCost                                 
        -- BY Espire 11 May 2016                               
        IF EXISTS(SELECT ipol.InternalPurchaseOrderLineId                
        FROM tbPurchaseOrderLine pol                
            JOIN tbInternalPurchaseOrderLine ipol ON pol.PurchaseOrderLineId = ipol.PurchaseOrderLineNo                
        WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineNo)                                 
     BEGIN                
  SELECT @IPOCurrencyNo = ipo.CurrencyNo, @ClientPrice = ipol.Price, @LinkMultiCurrencyNo = ipo.LinkMultiCurrencyNo                
            FROM tbInternalPurchaseOrder ipo                
                JOIN tbInternalPurchaseOrderLine ipol ON ipo.InternalPurchaseOrderId = ipol.InternalPurchaseOrderNo                
            WHERE ipol.PurchaseOrderLineNo = @PurchaseOrderLineNo                
            SET @ClientLandedCost = dbo.ufn_calculateLandedCost(@Quantity, GETDATE(), @ClientBaseCurrencyNo, @ClientPrice, @ShipInCost, 0, @ProductNo)                
                
        END                          
   ELSE                         
   BEGIN                
            SET @ClientPrice = NULL                
            SET @ClientLandedCost = NULL                
        END                
                
                
                
        --get line quantity yet to be received                                                       
                
        SELECT @QuantityOutstanding = ISNULL(Quantity, 0) - (SELECT ISNULL(SUM(Quantity), 0)                
            FROM dbo.tbGoodsInLine                
            WHERE     CustomerRMALineNo = @CustomerRmaLineNo)                
        FROM dbo.tbCustomerRMALine                
        WHERE   CustomerRMALineId = @CustomerRmaLineNo                
    END                
    BEGIN TRANSACTION                
                
    BEGIN TRY                                          
    --Get PO Delivery Date and PO Promise Date from PurchaseLine table                                     
    DECLARE @PODeliveryDate DATETIME                                     
    DECLARE @POPromiseDate DATETIME                            
    declare @WareHouseNo INT                         
 --DECLARE @ReqSerialNo BIT                         
                             
    declare @GIClientNo int                       
 declare @IsManufaturerRestricted bit                       
 SET @IsManufaturerRestricted=0                       
    SELECT @WareHouseNo = WarehouseNo, @GIClientNo=ClientNo                
    FROM tbGoodsIn                
    where GoodsInId = @GoodsInNo                         
                       
 IF EXISTS( SELECT 'A'                
    FROM tbRestrictedManufacturer                
    where ClientNo=@GIClientNo and ManufacturerNo=@ManufacturerNo)                       
 BEGIN                
        SET @IsManufaturerRestricted=1                
        SET @strMessage='Associated stock will be Quarantine'                
    END                       
                       
                       
                             
      SELECT @PODeliveryDate = DeliveryDate                         
 , @POPromiseDate = PromiseDate                
    FROM tbPurchaseOrderLine                
    WHERE PurchaseOrderLineId = @PurchaseOrderLineNo                            
                            
   IF @IsPOReceive  = 0 AND ISNULL(@SerialNosRecorded,0) = 1                         
   BEGIN                
        SET @ReqSerialNo = 1                
    END                         
                                     
    --Insert GI line                                                           
    INSERT  INTO dbo.tbGoodsInLine                
        (GoodsInNo                
        , PurchaseOrderLineNo                
        , FullPart                
        , Part            
        , ManufacturerNo                
        , DateCode                
        , PackageNo                
        , Quantity                
        , Price                
        , ShipInCost                
        , QualityControlNotes                
        , Location                
        , LotNo                
        , ProductNo                
        , LandedCost                
        , CustomerRMALineNo                
        , SupplierPart                
        , ROHS                
        , CountryOfManufacture                
        , Unavailable                
        , Notes                
        , CountingMethodNo                
        , SerialNosRecorded                
        , PartMarkings                
        , UpdatedBy                
        , FullSupplierPart             
        , PODeliveryDate                
        , POPromiseDate                
        , ClientLandedCost -- BY Espire 11 May 2016                                 
        , ClientPrice -- BY Espire 11 May 2016                           
        , POBankFee                
        , LinkMultiCurrencyNo                
        , ReqSerialNo                
        , MSLLevel                
        --[003] ihs code start                                                
        --, CountryOfOrigin                                                   
        ,CountryOfOriginNo                
        ,LifeCycleStage                
        ,HTSCode                
        ,AveragePrice                
        ,Packing                
        ,PackagingSize                
        ,Descriptions                
        ,IHSProduct                
        ,ECCNCode                
        --[003] ihs code end             
  , AS6081 --[005]            
        )                
                
    VALUES                
        (@GoodsInNo                                                       
           , @PurchaseOrderLineNo                                                       
           , dbo.ufn_get_fullpart(@Part)                                                       
           , @Part                                            
           , @ManufacturerNo                                                       
           , @DateCode                                                       
           , @PackageNo                                                       
           , @Quantity                                                       
           , @Price                                                       
           , @ShipInCost                                                       
           , @QualityControlNotes                                                       
          , @Location                                                       
           , @LotNo       
           , @ProductNo                                                       
           , @LandedCost                                                       
           , @CustomerRMALineNo                                                       
           , @SupplierPart                                                       
           , @ROHS                                                       
           , @CountryOfManufacture                                                       
           , @Unavailable                                                       
           , @Notes                                                       
           , @CountingMethodNo                                                       
           , @SerialNosRecorded                        
           , @PartMarkings                                                       
  , @UpdatedBy                               
           , dbo.ufn_get_fullpart(@SupplierPart)                                      
           , @PODeliveryDate                                     
           , @POPromiseDate                         
           , @ClientLandedCost  -- BY Espire 11 May 2016                                 
           , @ClientPrice  -- BY Espire 11 May 2016                           
     , @POBankFee                             
     , @LinkMultiCurrencyNo                           
   , @ReqSerialNo                         
, @MSL                         
--[003] ihs code start                                                    
  --, @CountryOfOrigin                                                   
 , @CountryOfOriginNo                                                   
 , @LifeCycleStage                                                   
 , @HTSCode                                                   
 , @AveragePrice                                                   
 , @Packing                                                   
 , @PackagingSize                                               
 , @Descriptions                                         
 , @IHSProduct                                        
 , @ECCNCode                                              
 --[003] ihs code end             
 , @AS6081 --[005]            
   )                                   
               
 IF (SELECT Count(1) from tbGoodsinline where GoodsInNo = @GoodsInNo and ISNULL(AS6081,0) = 1) > 0        
 BEGIN        
  UPDATE tbGoodsIn set AS6081 = 1 where GoodsInId = @GoodsInNo;        
 END        
 --ELSE        
 --BEGIN        
 -- UPDATE tbGoodsIn set AS6081 = 0 where GoodsInId = @GoodsInNo;        
 --END        
    SET @GoodsInLineId = SCOPE_IDENTITY()                                                       
    SET NOCOUNT ON                                                       
    DECLARE @StockId INT                          
      , @NewQuantityInStock INT                                                       
      , @NewQuantityOnOrder INT                                                       
      , @QuantityAllocated INT                                                       
      , @StockLogId INT                                                       
      , @SalesOrderLineNo INT                                                       
  , @SupplierRMALineNo INT                   
      , @AllocationId INT                                    
      , @NewStockId INT                                        
      , @FirstUnreceivedStockId INT                                                       
      , @OverReceiveQuantity INT                                          
      -- Division No from PO : 03 Aug 2015                                   
      , @DivisionNo INT                  
                                 
                                                    
                                 
    --Variables for Stock cursor                                       
                                 
    DECLARE @curStock_LeftToReceive INT                                  
     , @curStock_StockID INT                                                       
      , @curStock_QuantityInStock INT                                                       
      , @curStock_QuantityOnOrder INT                                                       
      , @curStock_ReceiveQuantity INT                                   
    IF @IsPOReceive = 1                                                        
        BEGIN                
        --check if we are over-receiving                                                       
        SET @OverReceiveQuantity = 0                
        IF (@Quantity > @QuantityOutstanding)                                                        
                BEGIN                
            --SET @OverReceiveQuantity = 1                                                        
            SELECT TOP 1                
                @FirstUnreceivedStockId = StockID                
            FROM dbo.tbStock                
            WHERE   PurchaseOrderLineNo = @PurchaseOrderLineNo --274073                                                       
                AND GoodsInLineNo IS NULL                
                AND QuantityOnOrder > 0                
                AND CustomerRMALineNo IS NULL                
            -- GA 22/11/2013                                                      
            ORDER BY QuantityOnOrder                
            SET @OverReceiveQuantity = @Quantity - @QuantityOutstanding                
        END                
                
        -- Get Division No from PO : 03 Aug 2015                                   
                
        SELECT TOP 1                
            @DivisionNo = ISNULL(ip.DivisionNo, P.DivisionNo)                
        FROM tbPurchaseOrder p                
            JOIN tbPurchaseOrderLine l ON p.PurchaseOrderId = l.PurchaseOrderNo                
            LEFT JOIN tbInternalPurchaseOrder ip ON p.PurchaseOrderId = ip.PurchaseOrderNo                
        WHERE L.PurchaseOrderLineId = @PurchaseOrderLineNo                
                
        --step through all un-received stock lines for the PO Line                                                       
                
        DECLARE curStock CURSOR FAST_FORWARD READ_ONLY                                    
            FOR                                                
                SELECT stk.StockID                                                       
                      , stk.QuantityOnOrder                                    
                      , stk.QuantityInStock                
        FROM dbo.tbStock stk                
        WHERE   stk.PurchaseOrderLineNo = @PurchaseOrderLineNo --274073                                         
            AND stk.GoodsInLineNo IS NULL                
            AND stk.QuantityOnOrder > 0                
            AND stk.CustomerRMALineNo IS NULL                
        -- GA 22/11/2013                                               
        ORDER BY stk.QuantityOnOrder                
        SET @curStock_LeftToReceive = @Quantity                
        OPEN curStock                
        FETCH NEXT FROM curStock INTO @curStock_StockID, @curStock_QuantityOnOrder, @curStock_QuantityInStock     
        WHILE @@FETCH_STATUS = 0                    
                BEGIN                
            --is there any left to receive?                                                   
            IF (@curStock_LeftToReceive > 0)                            
     BEGIN                
                --how much should be received into this stock line?                                
                SET @curStock_ReceiveQuantity = dbo.ufn_min(@curStock_QuantityOnOrder, @curStock_LeftToReceive)                
                --if this is an over-receive and we're on the first line, add the extra quantity                                                   
                IF @OverReceiveQuantity > 0 AND @curStock_StockId = @FirstUnreceivedStockId                                                        
                                SET @curStock_ReceiveQuantity = @curStock_ReceiveQuantity + @OverReceiveQuantity                
    --update stock with GI info                                
                UPDATE  dbo.tbStock                                                       
                            SET     QuantityInStock = @curStock_QuantityInStock + @curStock_ReceiveQuantity                                                       
                                  , QuantityOnOrder = dbo.ufn_max(0, @curStock_QuantityOnOrder - @curStock_ReceiveQuantity)                                                       
                                  , GoodsInLineNo = @GoodsInLineId                                                       
                                  , Unavailable = case when @IsManufaturerRestricted=1 then 1 else  @Unavailable   end                                                    
                                  , Location = @Location                                                       
                                  , LotNo = @LotNo                                                       
                             , LandedCost = @LandedCost                                    
                                  , SupplierPart = @SupplierPart                                                       
                                  , ROHS = @ROHS                   
                                  , ProductNo = @ProductNo                                           
                                  , PackageNo = @PackageNo                                                       
                                  , DateCode = @DateCode                                                       
                                  , ManufacturerNo = @ManufacturerNo                                                       
                                  , Part = @Part                                                       
                                  , FullPart = dbo.ufn_get_fullpart(@Part)                                                 
                                  , QualityControlNotes = @QualityControlNotes                      
                                  , CountryOfManufacture = @CountryOfManufacture                                                       
                                  , CountingMethodNo = @CountingMethodNo                                                       
                                  , PartMarkings = @PartMarkings                                                       
                                  , UpdatedBy = @UpdatedBy                                                       
                                  , DLUP = CURRENT_TIMESTAMP                              
                                  , IsManual = 0                                
                                 , StockDate = ISNULL(StockDate, GetDate())                                     
                                  , DivisionNo = @DivisionNo --Espire : 03 Aug 2015                                    
                                  , ClientLandedCost = @ClientLandedCost                                 
                  , ClientPrice = @ClientPrice                           
                 , WarehouseNo = @WareHouseNo  -- Espire : 21st Oct 2016                             
                                 , MSLLevel = @MSL         
         , AS6081 = @AS6081        
                      FROM dbo.tbStock stk                                       
                            WHERE   stk.StockId = @curStock_StockId                
                --get fields for stock log                                                       
                SELECT @NewQuantityInStock = tbStock.QuantityInStock                                                       
                                  , @NewQuantityOnOrder = tbStock.QuantityOnOrder                
                FROM dbo.tbStock                
                WHERE   StockId = @curStock_StockId                
                
                --Espire: 30 Dec 2019: Only for client 117 stock update                         
                declare @ClientNo int                
                select @ClientNo = ClientNo                
                from tbStock                
                where StockId = @curStock_StockId                
                IF @ClientNo = 109 and @curStock_StockId > 0 and @UpdatedBy <> 1953                       
                    BEGIN                
                    IF EXISTS(SELECT *                
                    FROM tbStock                
           where ISNULL(RefId109,0) = @curStock_StockId and ClientNo=117 )                       
                    BEGIN                
                        UPDATE  dbo.tbStock                                                       
                            SET     QuantityInStock = @curStock_QuantityInStock + @curStock_ReceiveQuantity                                                       
                                  , QuantityOnOrder = dbo.ufn_max(0, @curStock_QuantityOnOrder - @curStock_ReceiveQuantity)                                                       
                                  --, GoodsInLineNo = @GoodsInLineId                                 
                                 -- , Unavailable = @Unavailable                          
                                , Unavailable = case when @IsManufaturerRestricted=1 then 1 else  @Unavailable   end                                                      
                                  , Location = @Location                                                       
                                --  , LotNo = @LotNo                                                       
                                  , LandedCost = @LandedCost * dbo.ufn_get_exchange_rate(196, isnull(stk.StockDate,getdate()))                                   
                                  , SupplierPart = @SupplierPart                                                       
                                  , ROHS = @ROHS                                                       
                                  --, ProductNo = @ProductNo                                           
                                  --, PackageNo = @PackageNo                                                       
                   , DateCode = @DateCode                                                       
                                 -- , ManufacturerNo = @ManufacturerNo                                                       
                                  , Part = @Part                                                       
                                  , FullPart = dbo.ufn_get_fullpart(@Part)                                                 
                                  , QualityControlNotes = @QualityControlNotes                                                    
                                  --, CountryOfManufacture = @CountryOfManufacture                                                       
                 --, CountingMethodNo = @CountingMethodNo                             
                                  , PartMarkings = @PartMarkings                                                       
                                  , UpdatedBy =99999 -- @UpdatedBy                                                       
                                  , DLUP = CURRENT_TIMESTAMP                                         
                                  , IsManual = 0                                    
   , StockDate = ISNULL(StockDate, GetDate())                                  
                                ---, DivisionNo = @DivisionNo --Espire : 03 Aug 2015                           
                                  , ClientLandedCost = @ClientLandedCost * dbo.ufn_get_exchange_rate(196, isnull(stk.StockDate,getdate()))                                  
                                  , ClientPrice = @ClientPrice * dbo.ufn_get_exchange_rate(196, isnull(stk.StockDate,getdate()))                            
                                --- , WarehouseNo = @WareHouseNo  --  Espire : 21st Oct 2016                                                   , MSLLevel = @MSL                         
              FROM dbo.tbStock stk                                                       
                            WHERE   ISNULL(stk.RefId109,0) = @curStock_StockId and stk.ClientNo=117                
                    END                       
     ELSE                       
     BEGIN                
                
                        INSERT INTO [dbo].[tbStock]                
                            ([FullPart]                
                            ,[Part]                
                            ,[ManufacturerNo]                
                            ,[DateCode]                
                       ,[PackageNo]                
          ,[WarehouseNo]                
                            ,[ClientNo]                
                            ,[QualityControlNotes]                
                            ,[PurchaseOrderNo]                
                            ,[PurchaseOrderLineNo]                
                            ,[QuantityInStock]                
                            ,[QuantityOnOrder]                
                            ,[Location]                
                            ,[ProductNo]                
                            ,[ResalePrice]                
                            ,[Unavailable]                
                            ,[LotNo]                
                            ,[LandedCost]                
                            ,[SupplierPart]                
                            ,[ROHS]                
                            ,[PackageUnit]                
                            ,[StockKeepingUnit]                
                            ,[CustomerRMANo]                
                            ,[CustomerRMALineNo]                
                            ,[GoodsInLineNo]                
                            ,[UpdatedBy]                
                            ,[DLUP]                
                            ,[FullSupplierPart]                
                            ,[CountryOfManufacture]                
                            ,[PartMarkings]                
                            ,[CountingMethodNo]                
    ,[IsImageAvailable]                
                            ,[IsPDFAvailable]                
                            ,[IsManual]                
                            ,[StockDate]                
                            ,[HasSplit]                
                            ,[ParentStockNo]                
                            ,[OriginalLandedCost]                
                            ,[ManualStockSplitDate]                
                            ,[FirstStockProvisionDate]                
                            ,[LastStockProvisionDate]                
                            ,[DivisionNo]                
                         ,[ClientLandedCost]                
                            ,[ClientPrice]                
                            ,[MSLLevel]                
                            , RefId109                
                            )                
                        SELECT [FullPart]                       
          , [Part]                       
          , [ManufacturerNo]                       
          , [DateCode]                       
          , [PackageNo]                       
          , (select TOP 1                
                                a.WarehouseId                
                            from tbWarehouse a                
                            where a.RefID109=  s.[WarehouseNo] )                       
                    , 117                       
                    , [QualityControlNotes]                       
                    , NULL --  [PurchaseOrderNo]                       
                    , NULL --[PurchaseOrderLineNo]                       
                    , [QuantityInStock]                       
                    , [QuantityOnOrder]                       
                    , [Location]                       
            , (select TOP 1                
                                b.ProductId                
                            from tbProduct b                
                            where  b.RefId109 =  s.[ProductNo])                       
                    , [ResalePrice]                       
                    --,[Unavailable]                       
       , case when @IsManufaturerRestricted=1 then 1 else  Unavailable   end                         
                    , (select TOP 1                
                                c.LotId                
                            from tbLot c                
                            where c.RefId109 = s.[LotNo])                       
                    , [LandedCost]* dbo.ufn_get_exchange_rate(196, isnull(s.StockDate,getdate()))                         
                    , [SupplierPart]                       
                    , [ROHS]                       
                    , [PackageUnit]                       
                    , [StockKeepingUnit]                       
                    , NULL --[CustomerRMANo]                       
                    , NULL --[CustomerRMALineNo]                       
                    , NULL --[GoodsInLineNo]                       
                    , 99999 --[UpdatedBy]               
                    , GETDATE()                       
 , [FullSupplierPart]                       
                    , [CountryOfManufacture]                       
                    , [PartMarkings]                       
                    , [CountingMethodNo]                       
                    , [IsImageAvailable]                       
                    , [IsPDFAvailable]                       
                    , 1 --[IsManual]                       
                    , [StockDate]                       
                    , NULL --[HasSplit]                       
                    , NULL --[ParentStockNo]                       
                    , [OriginalLandedCost]                       
                    , [ManualStockSplitDate]                       
                    , [FirstStockProvisionDate]                       
                    , [LastStockProvisionDate]                       
                    , ( select TOP 1                
                                d.DivisionId                
                            from tbDivision d                
                            where d.RefID109= s.[DivisionNo])                       
                    , [ClientLandedCost]* dbo.ufn_get_exchange_rate(196, isnull(s.StockDate,getdate()))                         
                    , [ClientPrice]* dbo.ufn_get_exchange_rate(196, isnull(s.StockDate,getdate()))                         
               , [MSLLevel]              
                    , StockId                
                        from tbStock s                
                        where StockId=@curStock_StockId                
                    END                
                
                
       END                
                --end   30 Dec 2019: Only for client 117 stock update                                  
                
                
                
                --insert stock log                                       
                EXEC usp_insert_StockLog --                                                       
                                  @StockLogTypeNo = 16 --received quantity on goods in                                               
                                , @StockNo = @curStock_StockId --                                                       
                                , @QuantityInStock = @NewQuantityInStock --                                                    
                                , @QuantityOnOrder = 0 --always 0 because partial receipt splits off to a new stock                                                       
                                , @GoodsInLineNo = @GoodsInLineId --                                                       
                                , @GoodsInNo = @GoodsInNo --                                        
                                , @ActionQuantity = @Quantity --                              
                                , @Detail = @ChangedFields --                                        
                                , @UpdatedBy = @UpdatedBy --                             
                                , @StockLogId = @StockLogId OUTPUT                
                --set the amount left to receive                                                             
                SET @curStock_LeftToReceive = dbo.ufn_max(0, @curStock_LeftToReceive - @curStock_ReceiveQuantity)                
            END                
            --get next                                                       
            FETCH NEXT FROM curStock INTO @curStock_StockID, @curStock_QuantityOnOrder, @curStock_QuantityInStock                
        END                
        CLOSE curStock                
        DEALLOCATE curStock                
        --now see if there's still some left - partial receipt                                                       
        IF (SELECT QuantityOnOrder                
        FROM dbo.tbStock                
        WHERE   dbo.tbStock.GoodsInLineNo = @GoodsInLineId) > 0                                                        
                BEGIN                
            --Create new stock, update allocations etc                       
            EXEC usp_update_Allocation_AfterPartialReceive --                                                       
    @GoodsInLineNo = @GoodsInLineId --                                                         
                        , @GoodsInNo = @GoodsInNo --                                               
                        , @QuantityInserted = @Quantity --                                   
                        , @QuantityOnOrderChange = @NewQuantityOnOrder --                                                        
                        , @UpdatedBy = @UpdatedBy                
        --                                               
        END                
    END                                   
    ELSE                                                        
                                 
  -- CRMA receive                                                       
                                 
        BEGIN                
        --check if we are over-receiving                                
        SET @OverReceiveQuantity = 0                
        IF (@Quantity > @QuantityOutstanding)                                
           BEGIN                
            SELECT TOP 1                
       @FirstUnreceivedStockId = stk.StockID                
            FROM dbo.tbStock stk                
                JOIN tbGoodsInLine gil ON stk.CustomerRMALineNo = gil.CustomerRMALineNo                
            WHERE   gil.GoodsInLineId = @GoodsInLineId AND stk.GoodsInLineNo IS NULL AND stk.QuantityOnOrder > 0                
            ORDER BY stk.QuantityOnOrder                
            SET @OverReceiveQuantity = @Quantity - @QuantityOutstanding                
        END                
                
        --step through all un-received stock lines for the CRMA Line                                                  
        DECLARE curStock CURSOR FAST_FORWARD READ_ONLY  FOR                              
            SELECT stk.StockID                                
   , stk.QuantityOnOrder                                  
                      , stk.QuantityInStock                
        FROM dbo.tbStock stk                
            JOIN tbGoodsInLine gil ON stk.CustomerRMALineNo = gil.CustomerRMALineNo                
        WHERE   gil.GoodsInLineId = @GoodsInLineId AND stk.GoodsInLineNo IS NULL AND stk.QuantityOnOrder > 0                
        ORDER BY stk.QuantityOnOrder                
        SET @curStock_LeftToReceive = @Quantity                
        OPEN curStock                
                
        FETCH NEXT FROM curStock INTO @curStock_StockID, @curStock_QuantityOnOrder, @curStock_QuantityInStock                
        WHILE @@FETCH_STATUS = 0                                                        
                BEGIN                
            --is there any left to receive?                                      
            IF (@curStock_LeftToReceive > 0)                                                       
                        BEGIN                
                --how much should be received into this stock line?                                   
                SET @curStock_ReceiveQuantity = dbo.ufn_min(@curStock_QuantityOnOrder, @curStock_LeftToReceive)                
                --if this is an over-receive and we're on the first line, add the extra quantity                                       
                IF @OverReceiveQuantity > 0 AND @curStock_StockId = @FirstUnreceivedStockId                                                        
                         SET @curStock_ReceiveQuantity = @curStock_ReceiveQuantity + @OverReceiveQuantity                
                --Get division no : Espire - 17 Sep 2015                                 
                DECLARE @POLIneNo int                
                DECLARE @CustRMALineNo INT                
                SELECT @POLIneNo = stk.PurchaseOrderLineNo , @CustRMALineNo = stk.CustomerRMALineNo                
                FROM dbo.tbStock stk                
                WHERE   stk.StockId = @curStock_StockId                
                IF ISNULL(@POLIneNo,0) <> 0                                   
      BEGIN                
                    SELECT TOP 1                
                        @DivisionNo = stk.divisionNO                
                    from tbStock stk                
                    where stk.PurchaseOrderLineNo = @POLIneNo                
                    IF ISNULL(@DivisionNo,0) = 0                                   
            BEGIN                
                        SELECT TOP 1                
                            @DivisionNo = P.DivisionNo                
                        FROM tbPurchaseOrder p                
                            JOIN tbPurchaseOrderLine l ON p.PurchaseOrderId = l.PurchaseOrderNo                
         WHERE L.PurchaseOrderLineId = @POLIneNo                
                    END                
       END                                  
         ELSE                                  
          BEGIN                
                    ---Manual Stock-----                                  
                    DECLARE @InvLnNo int        
                    Select @InvLnNo = crma.InvoiceLineNo                
                    from dbo.tbCustomerRMALine crma                
                    where CustomerRMALineId =  @CustRMALineNo                
                    Select TOP 1                
                        @DivisionNo = st.DivisionNo                
                    from dbo.tbStock st JOIN dbo.tbInvoiceLine ivl ON ivl.StockNo = st.StockId                
                    where ivl.InvoiceLineId = @InvLnNo                
                END                
                UPDATE  dbo.tbStock                                
              SET     QuantityInStock = @curStock_QuantityInStock + @curStock_ReceiveQuantity                                                       
                       , QuantityOnOrder = dbo.ufn_max(0, @curStock_QuantityOnOrder - @curStock_ReceiveQuantity)                                                       
                      , GoodsInLineNo = @GoodsInLineId                                                       
                      --, Unavailable = @Unavailable                          
      , Unavailable = case when @IsManufaturerRestricted=1 then 1 else  @Unavailable   end                           
                      , Location = @Location                                                       
  , LotNo = @LotNo                                                       
                      , LandedCost = @LandedCost                      
 , SupplierPart = @SupplierPart                                          
                , ROHS = @ROHS                      
    , ProductNo = @ProductNo                                                       
                      , PackageNo = @PackageNo                                           
                      , DateCode = @DateCode                                                       
                      , ManufacturerNo = @ManufacturerNo                                            
  , Part = @Part                                     
                , FullPart = dbo.ufn_get_fullpart(@Part)                                                       
                      , QualityControlNotes = @QualityControlNotes                                       
                      , CountryOfManufacture = @CountryOfManufacture                                                       
                      , CountingMethodNo = @CountingMethodNo                                                  
                      , PartMarkings = @PartMarkings                                                       
                , UpdatedBy = @UpdatedBy                                                       
     , DLUP = CURRENT_TIMESTAMP                                                       
                      , IsManual = 0                                         
                      , StockDate = ISNULL(StockDate, GetDate())                                  
                      , DivisionNo = @DivisionNo                                    
                      , ClientLandedCost = @ClientLandedCost                                 
                      , ClientPrice = @ClientPrice                     
       , MSLLevel = stk.MSLLevel                         
                FROM dbo.tbStock stk                                                       
          WHERE   stk.StockId = @curStock_StockId                
                
                --Espire: 30 Dec 2019: Only for client 117 stock update                         
                declare @ClientNo1 int                
                select @ClientNo1 = ClientNo                
                from tbStock                
                where StockId = @curStock_StockId                
                
                IF @ClientNo1 = 109 and @curStock_StockId > 0 and @UpdatedBy <> 1953                       
       BEGIN                
                    UPDATE  dbo.tbStock                                
       SET    QuantityInStock = @curStock_QuantityInStock + @curStock_ReceiveQuantity                                                       
       , QuantityOnOrder = dbo.ufn_max(0, @curStock_QuantityOnOrder - @curStock_ReceiveQuantity)                                                       
       --, GoodsInLineNo = @GoodsInLineId                                                  
      -- , Unavailable = @Unavailable                          
   , Unavailable = case when @IsManufaturerRestricted=1 then 1 else  @Unavailable   end                                                         
       , Location = @Location                                                       
     --  , LotNo = @LotNo                                                       
       , LandedCost = @LandedCost* dbo.ufn_get_exchange_rate(196, isnull(stk.StockDate,getdate()))                                                        
       , SupplierPart = @SupplierPart                           
       , ROHS = @ROHS                                             
     ---  , ProductNo = @ProductNo                                                       
      -- , PackageNo = @PackageNo                                           
       , DateCode = @DateCode                                                       
      -- , ManufacturerNo = @ManufacturerNo                                            
       , Part = @Part                                     
       , FullPart = dbo.ufn_get_fullpart(@Part)                                                       
       , QualityControlNotes = @QualityControlNotes                                       
     --  , CountryOfManufacture = @CountryOfManufacture                                            
       , CountingMethodNo = @CountingMethodNo                                                       
       , PartMarkings = @PartMarkings                                                       
       , UpdatedBy = 99999 ---@UpdatedBy                                                       
       , DLUP = CURRENT_TIMESTAMP                                                       
       , IsManual = 0                                         
       , StockDate = ISNULL(StockDate, GetDate())                                  
     --- , DivisionNo = @DivisionNo                                    
       , ClientLandedCost = @ClientLandedCost* dbo.ufn_get_exchange_rate(196, isnull(stk.StockDate,getdate()))                                 
, ClientPrice = @ClientPrice* dbo.ufn_get_exchange_rate(196, isnull(stk.StockDate,getdate()))                           
       , MSLLevel = stk.MSLLevel                         
       FROM dbo.tbStock stk                                                       
       WHERE   isnull(stk.RefId109,0) = @curStock_StockId and stk.ClientNo=117                
                END                
                
                --End:Espire: 30 Dec 2019: Only for client 117 stock update                               
                
                --get fields for stock log                                                     
                SELECT @NewQuantityInStock = tbStock.QuantityInStock                                                       
                      , @NewQuantityOnOrder = tbStock.QuantityOnOrder                
                FROM dbo.tbStock                
                WHERE   dbo.tbStock.GoodsInLineNo = @GoodsInLineId         
                --insert stock log                                                       
                EXEC usp_insert_StockLog --                                         
                @StockLogTypeNo = 16 --received quantity on goods in                                                       
                ,@StockNo = @curStock_StockId --                                                       
                , @QuantityInStock = @NewQuantityInStock --                                                       
                , @QuantityOnOrder = 0 --@NewQuantityOnOrder --                                                       
               , @GoodsInLineNo = @GoodsInLineId --                                                       
                , @GoodsInNo = @GoodsInNo --                       
                , @ActionQuantity = @Quantity --                                                       
                , @Detail = @ChangedFields --                                    
                , @UpdatedBy = @UpdatedBy --                                                       
               , @StockLogId = @StockLogId OUTPUT                
                
                --set the amount left to receive                  
                SET @curStock_LeftToReceive = dbo.ufn_max(0, @curStock_LeftToReceive - @curStock_ReceiveQuantity)                
            END                
  --get next                                                       
            FETCH NEXT FROM curStock INTO @curStock_StockID, @curStock_QuantityOnOrder, @curStock_QuantityInStock                
        END                
        CLOSE curStock                
        DEALLOCATE curStock                
        --now see if this is a partial receipt                                                       
        IF @NewQuantityOnOrder > 0                                                      
                BEGIN                
            --Create new stock, update allocations etc                                                       
            EXEC usp_update_Allocation_AfterPartialReceive --                                       
                    @GoodsInLineNo = @GoodsInLineId --                          
            , @GoodsInNo = @GoodsInNo --                                
                          , @QuantityInserted = @Quantity --                                 
  , @QuantityOnOrderChange = @NewQuantityOnOrder --                                                        
                          , @UpdatedBy = @UpdatedBy                
        --                                        
        END                
    END                                                       
    IF @PurchaseOrderLineNo > 0                                               
      BEGIN                
        --Update closed status on PO and PO Lines                                                       
        EXEC usp_PurchaseOrderLine_Update_Closed_Status @PurchaseOrderLineNo                
    END                                               
    ELSE IF @CustomerRMALineNo > 0                                               
    BEGIN                
                
        --Update closed status on CRMA and CRMA Lines                                     
        EXEC usp_CustomerRMALine_Update_Closed_Status @CustomerRMALineNo                
    END                    
  --[004] start                    
-- as per RP-881, if the SupplierOnStop is set to 1, than at the time of Receive GI, qurantine the stock.                    
   declare @IsSanctioned bit = 0, @CompanyNo int, @RowsQuarantined int;                   
   select @CompanyNo = CompanyNo                
   from tbGoodsIn                
   where GoodsInId = @GoodsInNo;                  
   select @IsSanctioned = IsSanctioned                
   from tbCompany                
   WHERE CompanyId = @CompanyNo                   
                      
   select @StockIds = stockid                
   from tbStock            
   where GoodsInLineNo = @GoodsInLineId;                   
   if (@IsSanctioned = 1) and (@StockIds is not NULL)                    
       begin                
        --print 'in stock quarantine condition'                  
           
       exec usp_update_Stock_Quarantined @StockIds,@IsSanctioned, @Location, @UpdatedBy, @RowsAffected = @RowsQuarantined                
                
       if @RowsQuarantined > 0                  
           BEGIN                
           SET @strMessage = (Select Message from tbAS6081_alertMessages where AlertMessageId = 8)                
       END                
   end                    
    --[004] end                    
        SET NOCOUNT OFF                                                   
        COMMIT TRANSACTION                                                
  END TRY                                
                                 
BEGIN CATCH                                 
    ROLLBACK TRANSACTION                                
    SET @GoodsInLineId = 0                                                   
END CATCH                
END     