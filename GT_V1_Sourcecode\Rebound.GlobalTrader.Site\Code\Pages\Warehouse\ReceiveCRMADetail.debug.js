///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.prototype = {

	get_intCRMAID: function() { return this._intCRMAID; }, 	set_intCRMAID: function(v) { if (this._intCRMAID !== v)  this._intCRMAID = v; }, 
	get_ctlPageTitle: function() { return this._ctlPageTitle; }, 	set_ctlPageTitle: function(v) { if (this._ctlPageTitle !== v)  this._ctlPageTitle = v; }, 
	get_ctlReceivingInfo: function() { return this._ctlReceivingInfo; }, 	set_ctlReceivingInfo: function(v) { if (this._ctlReceivingInfo !== v)  this._ctlReceivingInfo = v; }, 
	get_ctlLines: function() { return this._ctlLines; }, 	set_ctlLines: function(v) { if (this._ctlLines !== v)  this._ctlLines = v; }, 
	get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlReceivingInfo) this._ctlReceivingInfo.addGetDataComplete(Function.createDelegate(this, this.ctlReceivingInfo_GetDataComplete));
		if (this._ctlLines) this._ctlLines.addSaveReceiveComplete(Function.createDelegate(this, this.ctlLines_SaveReceiveComplete));
		Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlPageTitle) this._ctlPageTitle.dispose();
		if (this._ctlReceivingInfo) this._ctlReceivingInfo.dispose();
		if (this._ctlLines) this._ctlLines.dispose();
		this._ctlPageTitle = null;
		this._ctlReceivingInfo = null;
		this._ctlLines = null;
		this._intCRMAID = null;
		this._IsGlobalLogin=null;
		Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.callBaseMethod(this, "dispose");
	},
	
	ctlReceivingInfo_GetDataComplete: function () {
	  //  alert(this._IsGlobalLogin);
	   // alert(this._ctlReceivingInfo.getFieldValue("hidGlobalClientNo"));
	    if (this._ctlLines._frmReceive) this._ctlLines._frmReceive.setFieldsFromHeader(this._ctlReceivingInfo.getFieldValue("hidNo"), this._ctlReceivingInfo.getFieldValue("hidCustomer"), this._ctlReceivingInfo.getFieldValue("hidCustomerNo"), this._ctlReceivingInfo.getFieldValue("hidWarehouseNo"), this._ctlReceivingInfo.getFieldValue("hidShipViaNo"), this._ctlReceivingInfo.getFieldValue("hidCurrencyNo"), this._ctlReceivingInfo.getFieldValue("ctlCurrency"));
		this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlReceivingInfo.getFieldValue("hidGlobalClientNo") : null;
		this._ctlReceivingInfo._IsGlobalLogin = this._IsGlobalLogin;
	},
	
	ctlLines_SaveReceiveComplete: function() {
		this._ctlReceivingInfo.getData();
	}

};
Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ReceiveCRMADetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
