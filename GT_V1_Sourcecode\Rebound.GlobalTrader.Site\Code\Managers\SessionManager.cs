using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site {
	/// <summary>
	/// Session Manager class
	/// NB: Session in this system uses secure cookies
	/// </summary>
	public class SessionManager {

		protected const string COOKIE_STRING = "GTCookie";
		protected static bool EncryptCookieNames = true;
		protected static bool EncryptCookieValues = true;

		#region Properties

		public static int? LoginID {
			get { return GetSubCookieValue_Int(Rebound.GlobalTrader.Site.SessionManager.ParentSessionKey.MainInfo, SessionKey.LoginID); }
			set {
				SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginID, value);
				if (!value.HasValue) {
					SessionManager.EndSession();
				} else {
					IsLoggedIn = true;
				}
			}
		}

        public static bool? IsPOHub
        {
            get
            {
                bool bln = false;
                string str = GetSubCookieValue(ParentSessionKey.Preferences, SessionKey.IsPOHub);
                if (string.IsNullOrEmpty(str))
                {
                    bln = false;
                }
                else
                {
                    bool.TryParse(str, out bln);
                }
                return bln;
            }
            set
            {
                SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.IsPOHub, value);
            }
        }
        public static int? POHubMailGroupId
        {
            get
            {
                int _POHubMailGroupId = default(int);
                string strPOHubMailGroupId = GetSubCookieValue(ParentSessionKey.Preferences, SessionKey.POHubMailGroupId);
                if (string.IsNullOrEmpty(strPOHubMailGroupId))
                {
                    _POHubMailGroupId = 0;
                }
                else
                {
                    int.TryParse(strPOHubMailGroupId, out _POHubMailGroupId);
                }
                return _POHubMailGroupId;
            }
            set
            {
                SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.POHubMailGroupId, value);
            }
        }

		public static string LoginFirstName {
			get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginFirstName); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginFirstName, value); }
		}

        public static string AdLogin
        {
            get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ADLogin); }
            set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ADLogin, value); }

        }

		public static string LoginLastName {
			get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginLastName); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginLastName, value); }
		}

		public static string LoginFullName {
			get { return String.Format("{0} {1}", LoginFirstName, LoginLastName); }
		}

        public static string LocalInstanceName
        {
            get { return String.Format("{0}", Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["LocalInstanceName"])); }
        }

        public static string DefaultSourcingSelection
        {
            
            get {
                string strInstance = "UK";
                if (!string.IsNullOrEmpty(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["DefaultSourcingSelection"])))
                    strInstance = Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["DefaultSourcingSelection"]);
                return String.Format("{0}",strInstance);

            }
        }
        /// <summary>
        /// Is the user logged in?
        /// NB - this only checks the Server Session
        /// </summary>
        public static bool IsLoggedIn {
			get {
                int get = 10;
                return Convert.ToBoolean(HttpContext.Current.Session[SessionKey.LoggedIn.ToString()]);
            }
			set {
                int set = 11;
                HttpContext.Current.Session[SessionKey.LoggedIn.ToString()] = value;
            }
		}

		public static string Culture {
			get {
				string strCulture = GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.Culture);
				if (String.IsNullOrEmpty(strCulture)) strCulture = "en-GB"; //default to British english
				return strCulture;
			}
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.Culture, value); }
		}

		public static bool? LeftPanelVisible {
			get {
				bool bln = true;
				string str = GetSubCookieValue(ParentSessionKey.Preferences, SessionKey.LeftPanelVisible);
				if (string.IsNullOrEmpty(str)) {
					bln = true;
				} else {
					bool.TryParse(str, out bln);
				}
				return bln;
			}
			set {
				SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.LeftPanelVisible, value);
			}
		}

		public static int? DefaultListPageSize {
			get {
				int? intPageSize = GetSubCookieValue_Int(ParentSessionKey.Preferences, SessionKey.DefaultListPageSize);
				if (intPageSize == null) intPageSize = 10;
				return intPageSize;
			}
			set { SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.DefaultListPageSize, value); }
		}

		public static string BackgroundImage {
			get { return GetSubCookieValue(ParentSessionKey.Preferences, SessionKey.BackgroundImage); }
			set { SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.BackgroundImage, value); }
		}

		public static bool? SaveDataListNuggetStateByDefault {
			get {
				bool bln = true;
				string str = GetSubCookieValue(ParentSessionKey.Preferences, SessionKey.SaveDataListNuggetStateByDefault);
				if (string.IsNullOrEmpty(str)) {
					bln = true;
				} else {
					bool.TryParse(str, out bln);
				}
				return bln;
			}
			set {
				SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.SaveDataListNuggetStateByDefault, value);
			}
		}

        public static int? PrinterNo
        {
            get
            {
                int? intPrinterNo = GetSubCookieValue_Int(ParentSessionKey.Preferences, SessionKey.PrinterNo);                
                return intPrinterNo;
            }
            set { SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.PrinterNo, value); }
        }

        public static int? LabelPathNo
        {
            get
            {
                int? intLabelPathNo = GetSubCookieValue_Int(ParentSessionKey.Preferences, SessionKey.LabelPathNo);
                return intLabelPathNo;
            }
            set { SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.LabelPathNo, value); }
        }

        /// <summary>
        /// Is the user has Global permission
        /// NB - this only checks the Server Session
        /// </summary>
        public static bool? IsGlobalUser
        {
            get { return Convert.ToBoolean(HttpContext.Current.Session[SessionKey.IsGlobalUser.ToString()]); }
            set { HttpContext.Current.Session[SessionKey.IsGlobalUser.ToString()] = value; }
        }
		public static bool? IsGSA
		{
			get { return Convert.ToBoolean(HttpContext.Current.Session[SessionKey.IsGSA.ToString()]); }
			set { HttpContext.Current.Session[SessionKey.IsGSA.ToString()] = value; }
		}
		public static bool? IsGSAViewPermission
		{
			get { return Convert.ToBoolean(HttpContext.Current.Session[SessionKey.IsGSAViewPermission.ToString()]); }
			set { HttpContext.Current.Session[SessionKey.IsGSAViewPermission.ToString()] = value; }
		}

		/// <summary>
		/// Which general permissions does the user have?
		/// </summary>
		/// <returns></returns>
		//public static Dictionary<BLL.SecurityFunction.List, bool> GeneralPermissions {
		//	get { return (Dictionary<BLL.SecurityFunction.List, bool>)HttpContext.Current.Session[SessionKey.GeneralPermissions.ToString()]; }
		//	set { HttpContext.Current.Session[SessionKey.GeneralPermissions.ToString()] = value; }
		//}
		//[0001] code start
		public static ConcurrentDictionary<BLL.SecurityFunction.List, bool> GeneralPermissions
        {
            get { return (ConcurrentDictionary<BLL.SecurityFunction.List, bool>)HttpContext.Current.Session[SessionKey.GeneralPermissions.ToString()]; }
            set { HttpContext.Current.Session[SessionKey.GeneralPermissions.ToString()] = value; }
        }
        //[0001] code end

        /// <summary>
        /// Which Section permissions does the user have?
        /// </summary>
        /// <returns></returns>
        //      public static Dictionary<BLL.SecurityFunction.List, bool> SectionPermissions {
        //	get { return (Dictionary<BLL.SecurityFunction.List, bool>)HttpContext.Current.Session[SessionKey.SectionPermissions.ToString()]; }
        //	set { HttpContext.Current.Session[SessionKey.SectionPermissions.ToString()] = value; }
        //}
        //[0001] code start
        public static  ConcurrentDictionary<BLL.SecurityFunction.List, bool> SectionPermissions
        {
            get { return (ConcurrentDictionary<BLL.SecurityFunction.List, bool>)HttpContext.Current.Session[SessionKey.SectionPermissions.ToString()]; }
            set { HttpContext.Current.Session[SessionKey.SectionPermissions.ToString()] = value; }
        }
        //[0001] code start

        //public static Dictionary<int, bool> WarehousePermission
        //{
        //    get { return (Dictionary<int, bool>)HttpContext.Current.Session[SessionKey.WarehousePermission.ToString()]; }
        //    set { HttpContext.Current.Session[SessionKey.WarehousePermission.ToString()] = value; }
        //}
        //[0001] code start
        public static ConcurrentDictionary<int, bool> WarehousePermission
        {
            get { return (ConcurrentDictionary<int, bool>)HttpContext.Current.Session[SessionKey.WarehousePermission.ToString()]; }
            set { HttpContext.Current.Session[SessionKey.WarehousePermission.ToString()] = value; }
        }
		//UtilityPermission
		public static ConcurrentDictionary<int, bool> UtilityPermission
		{
			get { return (ConcurrentDictionary<int, bool>)HttpContext.Current.Session[SessionKey.UtilityPermission.ToString()]; }
			set { HttpContext.Current.Session[SessionKey.UtilityPermission.ToString()] = value; }
		}
		//OrdersPermission
		public static ConcurrentDictionary<int, bool> OrdersPermission
		{
			get { return (ConcurrentDictionary<int, bool>)HttpContext.Current.Session[SessionKey.OrdersPermission.ToString()]; }
			set { HttpContext.Current.Session[SessionKey.OrdersPermission.ToString()] = value; }
		}
		//[0001] code end
		public static bool? IsDivision
        {
            get
            {
                bool bln = false;
                string str = GetSubCookieValue(ParentSessionKey.Preferences, SessionKey.IsDivision);
                if (string.IsNullOrEmpty(str))
                {
                    bln = false;
                }
                else
                {
                    bool.TryParse(str, out bln);
                }
                return bln;
            }
            set
            {
                SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.IsDivision, value);
            }
        }
        public static bool? IsTeam
        {
            get
            {
                bool bln = false;
                string str = GetSubCookieValue(ParentSessionKey.Preferences, SessionKey.IsTeam);
                if (string.IsNullOrEmpty(str))
                {
                    bln = false;
                }
                else
                {
                    bool.TryParse(str, out bln);
                }
                return bln;
            }
            set
            {
                SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.IsTeam, value);
            }
        }
        #endregion

        #region Main Info Cookie

        public static int? ClientID {
			get { return GetSubCookieValue_Int(ParentSessionKey.MainInfo, SessionKey.ClientID); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientID, value); }
		}

		public static int? LoginTeamID {
			get { return GetSubCookieValue_Int(ParentSessionKey.MainInfo, SessionKey.LoginTeamID); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginTeamID, value); }
		}

		public static int? LoginDivisionID {
			get { return GetSubCookieValue_Int(ParentSessionKey.MainInfo, SessionKey.LoginDivisionID); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginDivisionID, value); }
		}

		public static string LoginDivisionName {
			get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginDivisionName); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginDivisionName, value); }
		}

		public static string LoginEmail {
			get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginEmail); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.LoginEmail, value); }
		}

		public static string ClientName {
			get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientName); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientName, value); }

		}

		public static int? ClientCurrencyID {
			get { return GetSubCookieValue_Int(ParentSessionKey.MainInfo, SessionKey.ClientCurrencyID); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientCurrencyID, value); }
		}

		public static string ClientCurrencyCode {
			get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientCurrencyCode); }
			set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientCurrencyCode, value); }
		}

        public static string ClientLocalCurrencyCode
        {
            get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientLocalCurrencyCode); }
            set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientLocalCurrencyCode, value); }
        }

        public static string ClientCode
        {
            get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientCode); }
            set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientCode, value); }
        }


        //Es

        public static int? ClientBaseCurrencyID
        {
            get { return GetSubCookieValue_Int(ParentSessionKey.MainInfo, SessionKey.ClientBaseCurrencyID ); }
            set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientBaseCurrencyID, value); }
        }

        //public static string ClientBaseCurrencyCode
        //{
        //    get { return GetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientBaseCurrencyCode ); }
        //    set { SetSubCookieValue(ParentSessionKey.MainInfo, SessionKey.ClientBaseCurrencyCode, value); }
        //}

        public static string ClientBaseCurrencyCode
        {
            get
            {
                if (HttpContext.Current.Session["OtherClntCurrency"] != null)
                {
                    return Convert.ToString(HttpContext.Current.Session["OtherClntCurrency"]);
                }
                else
                {
                    return " ";
                }
            }
            set { HttpContext.Current.Session["OtherClntCurrency"] = value; }
        }

        public static Int32? GlobalClientNo
        {
            get
            {
                if (HttpContext.Current.Session["GlobalClientNo"] != null)
                {
                    return Convert.ToInt32(HttpContext.Current.Session["GlobalClientNo"]);
                }
                else
                {
                    return null;
                }
            }
            set { HttpContext.Current.Session["GlobalClientNo"] = value; }
        }
        public static string PowerBIUsername
        {
            get
            {
                if (HttpContext.Current.Session["PowerBIUsername"] != null)
                {
                    return Convert.ToString(HttpContext.Current.Session["PowerBIUsername"]);
                }
                else
                {
                    return " ";
                }
            }
            set { HttpContext.Current.Session["PowerBIUsername"] = value; }
        }
        public static string PowerBIPassword
        {
            get
            {
                if (HttpContext.Current.Session["PowerBIPassword"] != null)
                {
                    return Convert.ToString(HttpContext.Current.Session["PowerBIPassword"]);
                }
                else
                {
                    return " ";
                }
            }
            set { HttpContext.Current.Session["PowerBIPassword"] = value; }
        }
        public static Int32? MasterLoginNo
        {
            get
            {
                if (HttpContext.Current.Session["MasterLoginNo"] != null)
                {
                    return Convert.ToInt32(HttpContext.Current.Session["MasterLoginNo"]);
                }
                else
                {
                    return 0;
                }
            }
            set { HttpContext.Current.Session["MasterLoginNo"] = value; }
        }
        public static Int32? DefaultClientNo
        {
            get
            {
                if (HttpContext.Current.Session["DefaultClientNo"] != null)
                {
                    return Convert.ToInt32(HttpContext.Current.Session["DefaultClientNo"]);
                }
                else
                {
                    return SessionManager.ClientID;
                }
            }
            set { HttpContext.Current.Session["DefaultClientNo"] = value; }
        }
        #endregion

        #region Preferences Cookie

        public static bool? ShowMessageAlert {
			get {
				bool? bln = GetSubCookieValue_Bool(ParentSessionKey.Preferences, SessionKey.ShowMessageAlert);
				if (bln == null) bln = true;
				return bln;
			}
			set { SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.ShowMessageAlert, value); }
		}

		public static ViewLevelList DefaultListPageView {
			get {
				int? intDecoded = GetSubCookieValue_Int(ParentSessionKey.Preferences, SessionKey.DefaultListPageView);
				if (intDecoded == null) {
					return ViewLevelList.My;
				} else {
					return (ViewLevelList)Convert.ToInt32(intDecoded);
				}
			}
			set { SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.DefaultListPageView, (int)value); }
		}

		public static Pages.Default.TabList DefaultHomepageTab {
			get {
				int? intDecoded = GetSubCookieValue_Int(ParentSessionKey.Preferences, SessionKey.DefaultHomepageTab);
				if (intDecoded == null) {
					return Pages.Default.TabList.Today;
				} else {
					return (Pages.Default.TabList)Convert.ToInt32(intDecoded);
				}
			}
			set { SetSubCookieValue(ParentSessionKey.Preferences, SessionKey.DefaultHomepageTab, (int)value); }
		}

		#endregion

		#region Cookie Functions

		protected static void CreateCookie(string strKey, object objValue) {
			HttpCookie ck = new HttpCookie(strKey);
			ck.Expires = DateTime.Now.AddDays(10);
			if (EncryptCookieValues) {
				ck.Value = Functions.EncryptString(objValue.ToString(), COOKIE_STRING);
			} else {
				ck.Value = objValue.ToString();
			}
			HttpContext.Current.Response.Cookies.Add(ck);
		}
		protected static void CreateCookie(Enum enmKey, object objValue) {
			CreateCookie(GetKey(enmKey), objValue);
		}
		protected static void CreateCookie(Enum enmKey) {
			CreateCookie(enmKey, null);
		}

		protected static string GetCookieValue(string strKey) {
			string strOut = null;
			HttpCookie ck = HttpContext.Current.Request.Cookies[strKey];
			if (ck != null) {
				if (EncryptCookieValues) {
					strOut = Functions.DecryptString(ck.Value, COOKIE_STRING);
				} else {
					strOut = ck.Value.ToString();
				}
			}
			return strOut;
		}
		protected static string GetCookieValue(Enum enmKey) {
			return GetCookieValue(GetKey(enmKey));
		}

		protected static int? GetCookieValue_Int(string strKey) {
			int? intReturn = null;
			string strValue = GetCookieValue(strKey);
			if (!string.IsNullOrEmpty(strValue)) {
				int intOut;
				if (int.TryParse(strValue, out intOut)) intReturn = intOut;
			}
			return intReturn;
		}
		protected static int? GetCookieValue_Int(Enum enmKey) {
			return GetCookieValue_Int(GetKey(enmKey));
		}

		protected static bool? GetCookieValue_Bool(string strKey) {
			bool? blnReturn = false;
			string strValue = GetCookieValue(strKey);
			if (!string.IsNullOrEmpty(strValue)) {
				bool blnOut;
				if (bool.TryParse(strValue, out blnOut)) blnReturn = blnOut;
			}
			return blnReturn;
		}
		protected static bool? GetCookieValue_Bool(Enum enmKey) {
			return GetCookieValue_Bool(GetKey(enmKey));
		}

		protected static void SetSubCookieValue(Enum enmParent, Enum enmSub, object objValue) {
			string strKey = GetKey(enmParent);
			HttpCookie ckParent = HttpContext.Current.Response.Cookies[strKey];
			ckParent.Expires = DateTime.Now.AddDays(10);
			if (objValue == null) {
				ckParent[GetSubKey(enmSub)] = null;
			} else {
				string strValue = objValue.ToString();
				if (EncryptCookieValues) strValue = Functions.EncryptString(objValue.ToString(), COOKIE_STRING);
				ckParent[GetSubKey(enmSub)] = strValue;
			}
		}

		protected static string GetSubCookieValue(Enum enmParent, Enum enmSub) {
			string strOut = null;
			try {
				HttpCookie ckParent = HttpContext.Current.Request.Cookies[GetKey(enmParent)];
				if (ckParent != null) {
					string strSubKey = GetSubKey(enmSub);
					if (ckParent[strSubKey] != null) {
						if (EncryptCookieValues) {
							strOut = Functions.DecryptString(ckParent[strSubKey], COOKIE_STRING);
						} else {
							strOut = ckParent[strSubKey].ToString();
						}
					}
				}
			} catch {
				strOut = null;
			}
			return strOut;
		}

		protected static int? GetSubCookieValue_Int(Enum enmParent, Enum enmKey) {
			int? intReturn = null;
			string strValue = GetSubCookieValue(enmParent, enmKey);
			if (!string.IsNullOrEmpty(strValue)) {
				int intOut;
				if (int.TryParse(strValue, out intOut)) intReturn = intOut;
			}
			return intReturn;
		}

		protected static bool? GetSubCookieValue_Bool(Enum enmParent, Enum enmKey) {
			bool? blnReturn = null;
			string strValue = GetSubCookieValue(enmParent, enmKey);
			if (!string.IsNullOrEmpty(strValue)) {
				bool blnOut;
				if (bool.TryParse(strValue, out blnOut)) blnReturn = blnOut;
			}
			return blnReturn;
		}

		protected static void DeleteCookie(string strKey) {
			HttpCookie ck = HttpContext.Current.Response.Cookies[strKey];
			if (ck != null) ck.Expires = DateTime.Now.AddDays(-1);
		}

		protected static void DeleteCookie(Enum enmKey) {
			DeleteCookie(GetKey(enmKey));
		}

		#endregion

		#region Methods

		public static bool CheckLoggedIn() {
			bool blnLoggedInResult = false;
			if (LoginID == 0) {
				//we are logged in if this is the Rebound Admin user
				blnLoggedInResult = true;
			} else {
				blnLoggedInResult = IsLoggedIn;
			}
			return blnLoggedInResult;
		}

		protected static string GetKey(Enum enmKey) {
			if (EncryptCookieNames) {
				return Functions.MD5Hash(string.Format("{0}{1}ahfy", Site.GetInstance().ApplicationNameShort, Convert.ToInt32(enmKey)));
			} else {
				return string.Format("{0}{1}", Site.GetInstance().ApplicationNameShort, enmKey);
			}
		}

		protected static string GetSubKey(Enum enmKey) {
			if (EncryptCookieNames) {
				return Functions.MD5Hash(string.Format("{0}xgqf", Convert.ToInt32(enmKey)));
			} else {
				return string.Format("{0}", enmKey);
			}
		}

		public static void MaintainStateWithoutFullPostback() {
			//copies all request cookies to the response cookies collection
			foreach (string strKey in HttpContext.Current.Request.Cookies.AllKeys) {
				HttpContext.Current.Response.Cookies.Add(HttpContext.Current.Request.Cookies[strKey]);
			}
		}

       

		/// <summary>
		/// Clears session
		/// </summary>
		public static void EndSession() {
			ClearSession();
			HttpContext.Current.Session.Abandon();
		}

		protected static void ClearSession() {
			DeleteCookie(ParentSessionKey.MainInfo);
			DeleteCookie(ParentSessionKey.Preferences);
			DeleteCookie(ParentSessionKey.PersonalPermissions);
			HttpContext.Current.Session.Clear();
		}

		/// <summary>
		/// Creates a blank session
		/// </summary>
		public static void CreateSession() {
			HttpContext.Current.Session[SessionKey.LoggedIn.ToString()] = false;
		}

		internal static void StoreGeneralLoginItems(Rebound.GlobalTrader.BLL.Login lg) {
			SessionManager.LoginID = lg.LoginId;
			SessionManager.LoginFirstName = lg.FirstName;
			SessionManager.LoginLastName = lg.LastName;
			SessionManager.LoginEmail = lg.EMail;
            SessionManager.AdLogin = lg.ADLogin;
            SessionManager.IsPOHub = lg.IsPOHub;
            SessionManager.POHubMailGroupId = lg.POHubMailGroupId;
            SessionManager.IsGlobalUser = lg.IsGlobalUser;
            SessionManager.PowerBIUsername = lg.PowerBIUsername;
            SessionManager.PowerBIPassword = lg.PowerBIPassword;
            SessionManager.MasterLoginNo = lg.MasterLoginId;
            SessionManager.DefaultClientNo = lg.DefaultClientNo;
            SessionManager.IsDivision = lg.IsDivision;
            SessionManager.IsTeam = lg.IsTeam;
			SessionManager.IsGSA = lg.IsGSA;
			SessionManager.IsGSAViewPermission = lg.IsGSAViewPermission;


		}

		internal static void StoreClientLoginItems(Rebound.GlobalTrader.BLL.Login lg) {
			SessionManager.ClientID = lg.ClientNo;
			SessionManager.ClientName = lg.ClientName;
            SessionManager.ClientCode = lg.ClientCode;
			SessionManager.ClientCurrencyID = lg.ClientCurrencyNo;
			SessionManager.ClientCurrencyCode = lg.ClientCurrencyCode;
            SessionManager.ClientLocalCurrencyCode = lg.ClientLocalCurrencyCode;
			SessionManager.LoginTeamID = lg.TeamNo;
			SessionManager.LoginDivisionID = lg.DivisionNo;
			SessionManager.LoginDivisionName = lg.DivisionName;
            SessionManager.IsPOHub = lg.IsPOHub;
            SessionManager.POHubMailGroupId = lg.POHubMailGroupId;
            SessionManager.IsDivision = lg.IsDivision;
            SessionManager.IsTeam = lg.IsTeam;
			SessionManager.IsGSA = lg.IsGSA;
			SessionManager.IsGSAViewPermission = lg.IsGSAViewPermission;

		}

		internal static void StoreLoginPreferences(Rebound.GlobalTrader.BLL.LoginPreference pref) {
			SessionManager.ShowMessageAlert = pref.ShowMessageAlert;
			SessionManager.Culture = pref.DefaultSiteLanguageCode;
			SessionManager.DefaultListPageSize = pref.DefaultListPageSize;
			SessionManager.DefaultHomepageTab = (Pages.Default.TabList)pref.DefaultHomePageTab;
			SessionManager.DefaultListPageView = (ViewLevelList)pref.DefaultListPageView;
			SessionManager.BackgroundImage = pref.BackgroundImage;
			SessionManager.SaveDataListNuggetStateByDefault = pref.SaveDataListNuggetStateByDefault;
            SessionManager.PrinterNo = pref.PrinterNo;
            SessionManager.LabelPathNo = pref.LabelPathNo;
		}

		public static void StorePermission(BLL.SecurityFunction.List enmFunction, bool blnAllowed) {
			HttpCookie ckPermission = GetPermissionsCookie();
			string strValue = blnAllowed.ToString();
			if (EncryptCookieValues) strValue = Functions.EncryptString(blnAllowed.ToString(), COOKIE_STRING);
			GetPermissionsCookie()[GetPersonalPermissionSubKey(enmFunction)] = strValue;
		}

		private static HttpCookie GetPermissionsCookie() {
			string strKey = GetKey(ParentSessionKey.PersonalPermissions);
			HttpCookie ckPermission = HttpContext.Current.Response.Cookies[strKey];
			ckPermission = HttpContext.Current.Response.Cookies[strKey];
			ckPermission.Expires = DateTime.Now.AddDays(10);
			return ckPermission;
		}

		public static bool? GetPermission(BLL.SecurityFunction.List enmFunction) {
			bool? blnAllowed = null;
			try {
				HttpCookie ckPermission = GetPermissionsCookie();
				bool blnOut;
				if (bool.TryParse(ckPermission[GetPersonalPermissionSubKey(enmFunction)], out blnOut)) blnAllowed = blnOut;
			} catch {
				blnAllowed = true;
			}
			return blnAllowed;
		}

		private static string GetPersonalPermissionSubKey(BLL.SecurityFunction.List enmFunction) {
			if (EncryptCookieNames) {
				return Functions.MD5Hash(string.Format("{0}dq9g", (int)enmFunction));
			} else {
				return enmFunction.ToString();
			}
		}

		public static bool CheckLoggedInOnDatabase() {
			bool blnLoggedInResult = false;
			try {
				int intSessions = 0;
				intSessions = BLL.Session.CountForLogin(LoginID, HttpContext.Current.Session.SessionID);
				blnLoggedInResult = (intSessions > 0);
			} catch { }
			return blnLoggedInResult;
		}

		#endregion

		#region Enumerations

		public enum ParentSessionKey {
			MainInfo,
			Preferences,
			PersonalPermissions
		}

		public enum SessionKey {
			LoginID,
			LoggedIn,
			Culture,
			LoginLastName,
			LoginFirstName,
			LoginFullName,
			LeftPanelVisible,
			DefaultListPageSize,
			BackgroundImage,
			SaveDataListNuggetStateByDefault,
            PrinterNo,
            LabelPathNo,
            ADLogin,

			ClientID,
			ClientName,
            ClientCode,
			ClientCurrencyID,
			ClientCurrencyCode,
            ClientLocalCurrencyCode,
			LoginTeamID,
			LoginDivisionID,
			LoginDivisionName,
			LoginEmail,
            IsPOHub,
            POHubMailGroupId,
			GeneralPermissions,
			SectionPermissions,
			ShowMessageAlert,
			DefaultHomepageTab,
			DefaultListPageView,
            SalesOrderNo,
            IsGlobalUser,
            ClientBaseCurrencyID,
            ClientBaseCurrencyCode,
            WarehousePermission,
            IsDivision,
            IsTeam,
			UtilityPermission,
			IsGSA,
			IsGSAViewPermission,
			OrdersPermission
		}

		#endregion

	}
}