﻿//Marker     Changed by         Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>     20/09/2021    added new class for the Supplier contact.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class SupplierContactDetails
    {
        #region Constructors

        public SupplierContactDetails() { }

        #endregion

        #region Properties

        /// <summary>
        /// ROHSStatusId (from Table)
        /// </summary>
        public System.String SupplierContactEmail { get; set; }
        /// <summary>
        /// Name (from Table)
        /// </summary>
        public System.String Name { get; set; }

        #endregion
    }
}
