///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_NoBid = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_NoBid.initializeBase(this, [element]);
	this._intBOMID = -1;
	this._SalesmanNo = null;
	this._UpdatedBy = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_NoBid.prototype = {

	get_intBOMID: function() { return this._intBOMID; }, 	set_intBOMID: function(value) { if (this._intBOMID !== value)  this._intBOMID = value; },
	get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
	get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
	get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
	get_BomCompanyNo: function () { return this._BomCompanyNo; }, set_BomCompanyNo: function (value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
	get_SalesmanNo: function () { return this._SalesmanNo; }, set_SalesmanNo: function (value) { if (this._SalesmanNo !== value) this._SalesmanNo = value; },
	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_NoBid.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intBOMID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_NoBid.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlRelease = this.getFieldComponent("ctlRelease");
			this._ctlRelease.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlRelease.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
	},

	yesClicked: function () {
	  
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/BOMMainInfo");
		obj.set_DataObject("BOMMainInfo");
		obj.set_DataAction("BOMNoBidRequirement");
		obj.addParameter("id", this._intBOMID);
		obj.addParameter("BomCode", this._BomCode);
		obj.addParameter("BomName", this._BomName);
		obj.addParameter("BomCompanyName", this._BomCompanyName);
		obj.addParameter("BomCompanyNo", this._BomCompanyNo);
		obj.addParameter("UpdatedBy", this._UpdatedBy);
		obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
		//rp-225 salesman no passed to nobid function
		obj.addParameter("SalesmanNo", this._SalesmanNo);
		obj.addDataOK(Function.createDelegate(this, this.saveReleaseComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveReleaseComplete: function(args) {
		 if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_NoBid.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMMainInfo_NoBid", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
