//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ReportTitles {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ReportTitles() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.ReportTitles", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active Vendors from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ActiveVendors {
            get {
                return ResourceManager.GetString("ActiveVendors", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Buyers.
        /// </summary>
        internal static string AllBuyers {
            get {
                return ResourceManager.GetString("AllBuyers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Salespeople.
        /// </summary>
        internal static string AllSalespeople {
            get {
                return ResourceManager.GetString("AllSalespeople", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Warehouses.
        /// </summary>
        internal static string AllWarehouses {
            get {
                return ResourceManager.GetString("AllWarehouses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Customers On Stop.
        /// </summary>
        internal static string ApprovedCustomersOnStop {
            get {
                return ResourceManager.GetString("ApprovedCustomersOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto Entered Suppliers ([#STARTDATE#] - [#ENDDATE#]).
        /// </summary>
        internal static string AutoEnteredSuppliers_Unedited {
            get {
                return ResourceManager.GetString("AutoEnteredSuppliers_Unedited", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Email Invoice Status Report from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string BulkEmailInvoiceStatus {
            get {
                return ResourceManager.GetString("BulkEmailInvoiceStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Closed Requirements Reasons.
        /// </summary>
        internal static string ClosedRequirementsReasons {
            get {
                return ResourceManager.GetString("ClosedRequirementsReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Communication Log Activity for [#EMPLOYEE#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string CommunicationLogActivityforaUser {
            get {
                return ResourceManager.GetString("CommunicationLogActivityforaUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Companies Approved To Purchase From.
        /// </summary>
        internal static string CompaniesApprovedToPurchaseFrom {
            get {
                return ResourceManager.GetString("CompaniesApprovedToPurchaseFrom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Contacted Since [#CUTOFFDATE#].
        /// </summary>
        internal static string CompaniesNotContacted {
            get {
                return ResourceManager.GetString("CompaniesNotContacted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Email List in [#COUNTRY#].
        /// </summary>
        internal static string ContactEmailList {
            get {
                return ResourceManager.GetString("ContactEmailList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contacts Not Contacted Since [#CUTOFFDATE#].
        /// </summary>
        internal static string ContactsNotContacted {
            get {
                return ResourceManager.GetString("ContactsNotContacted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string CreditNotes {
            get {
                return ResourceManager.GetString("CreditNotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string CreditNotesforaCustomer {
            get {
                return ResourceManager.GetString("CreditNotesforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Notes for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string CreditNotesforaSalesperson {
            get {
                return ResourceManager.GetString("CreditNotesforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer List for [#SALESPERSON#].
        /// </summary>
        internal static string CustomerListforSalesperson {
            get {
                return ResourceManager.GetString("CustomerListforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer On Time Delivery Report from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string CustomerOnTimeDeliveryReport {
            get {
                return ResourceManager.GetString("CustomerOnTimeDeliveryReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Statement for [#COMPANY_NAME#]  [#LATE_ONLY#].
        /// </summary>
        internal static string CustomerStatement {
            get {
                return ResourceManager.GetString("CustomerStatement", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Customer Requirements Details for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Detailed {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Detailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Customer Requirements Summary for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Summary {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Summary", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Customer Requirements Totals for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string DailyCustomerRequirementsbySalesperson_Totals {
            get {
                return ResourceManager.GetString("DailyCustomerRequirementsbySalesperson_Totals", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Imports ([#STARTDATE#] - [#ENDDATE#]).
        /// </summary>
        internal static string DailyImports {
            get {
                return ResourceManager.GetString("DailyImports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Imports By Source ([#STARTDATE#] - [#ENDDATE#]).
        /// </summary>
        internal static string DailyImportsBySource {
            get {
                return ResourceManager.GetString("DailyImportsBySource", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Daily Report Log.
        /// </summary>
        internal static string DailyReportLog {
            get {
                return ResourceManager.GetString("DailyReportLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days Since Last Invoice by Contact.
        /// </summary>
        internal static string DaysSinceLastInvoicebyContact {
            get {
                return ResourceManager.GetString("DaysSinceLastInvoicebyContact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Days Since Last Invoice by Customer.
        /// </summary>
        internal static string DaysSinceLastInvoicebyCustomer {
            get {
                return ResourceManager.GetString("DaysSinceLastInvoicebyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Email Status.
        /// </summary>
        internal static string EmailStatus {
            get {
                return ResourceManager.GetString("EmailStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods received from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string GoodsReceived {
            get {
                return ResourceManager.GetString("GoodsReceived", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods Received Not Invoiced.
        /// </summary>
        internal static string GoodsReceivedNotInvoiced {
            get {
                return ResourceManager.GetString("GoodsReceivedNotInvoiced", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods Received Shipment Details from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string GoodsReceivedShipmentDetails {
            get {
                return ResourceManager.GetString("GoodsReceivedShipmentDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Gross Profit Breakdown from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string GrossProfitBreakdown {
            get {
                return ResourceManager.GetString("GrossProfitBreakdown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Confirmed.
        /// </summary>
        internal static string IncludeConfirmed {
            get {
                return ResourceManager.GetString("IncludeConfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Includes Credits.
        /// </summary>
        internal static string IncludesCredits {
            get {
                return ResourceManager.GetString("IncludesCredits", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Includes Shipping.
        /// </summary>
        internal static string IncludesShipping {
            get {
                return ResourceManager.GetString("IncludesShipping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Include Unconfirmed.
        /// </summary>
        internal static string IncludeUnconfirmed {
            get {
                return ResourceManager.GetString("IncludeUnconfirmed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrastat Report for EEC Arrivals (Customer RMAs) for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string IntrastatReportforEECArrivals_CustomerRMAs {
            get {
                return ResourceManager.GetString("IntrastatReportforEECArrivals_CustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrastat Report for EEC Arrivals (Purchases) for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string IntrastatReportforEECArrivals_Purchases {
            get {
                return ResourceManager.GetString("IntrastatReportforEECArrivals_Purchases", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrastat Report for EEC Dispatches (Sales) for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string IntrastatReportforEECDispatches_Sales {
            get {
                return ResourceManager.GetString("IntrastatReportforEECDispatches_Sales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Intrastat Report for EEC Dispatches (Supplier RMAs) for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string IntrastatReportforEECDispatches_SupplierRMAs {
            get {
                return ResourceManager.GetString("IntrastatReportforEECDispatches_SupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Companies with Invalid / Missing Purchasing Information [#INVALID_ONLY#].
        /// </summary>
        internal static string InvalidCompanyPurchasingInfo {
            get {
                return ResourceManager.GetString("InvalidCompanyPurchasingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Companies with Invalid / Missing Sales Information [#INVALID_ONLY#].
        /// </summary>
        internal static string InvalidCompanySalesInfo {
            get {
                return ResourceManager.GetString("InvalidCompanySalesInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invalid Only.
        /// </summary>
        internal static string InvalidOnly {
            get {
                return ResourceManager.GetString("InvalidOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory Location Report for [#WAREHOUSE#].
        /// </summary>
        internal static string InventoryLocationReport {
            get {
                return ResourceManager.GetString("InventoryLocationReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inventory Location Report for Lot [#LOT#] at [#WAREHOUSE#] .
        /// </summary>
        internal static string InventoryLocationReportforLot {
            get {
                return ResourceManager.GetString("InventoryLocationReportforLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices Sorted by Invoice Number from [#STARTDATE#] to [#ENDDATE#] [#INCLUDING_SHIPPING#].
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumber {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices Sorted by Invoice Number for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumberforaCustomer {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumberforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices Sorted by Invoice Number for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string InvoicesSortedbyInvoiceNumberforaSalesperson {
            get {
                return ResourceManager.GetString("InvoicesSortedbyInvoiceNumberforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Late Only.
        /// </summary>
        internal static string LateOnly {
            get {
                return ResourceManager.GetString("LateOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Statistics  ([#STARTDATE#] - [#ENDDATE#]).
        /// </summary>
        internal static string LoginStatistics {
            get {
                return ResourceManager.GetString("LoginStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Login Statistics for [#USERNAME#] ([#STARTDATE#] - [#ENDDATE#]).
        /// </summary>
        internal static string LoginStatisticsbyName {
            get {
                return ResourceManager.GetString("LoginStatisticsbyName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [#LOT#].
        /// </summary>
        internal static string LotStockProvision {
            get {
                return ResourceManager.GetString("LotStockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number of Accounts by Salesperson.
        /// </summary>
        internal static string NumberofAccountsbySalesperson {
            get {
                return ResourceManager.GetString("NumberofAccountsbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers by Supplier .
        /// </summary>
        internal static string NumberofOffersbyVendor {
            get {
                return ResourceManager.GetString("NumberofOffersbyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers by Supplier - History.
        /// </summary>
        internal static string NumberofOffersHistorybyVendor {
            get {
                return ResourceManager.GetString("NumberofOffersHistorybyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirements by Customer.
        /// </summary>
        internal static string NumberofRequirementsbyVendor {
            get {
                return ResourceManager.GetString("NumberofRequirementsbyVendor", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenCustomerRMAs {
            get {
                return ResourceManager.GetString("OpenCustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenCustomerRMAsforaCustomer {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenCustomerRMAsforaCustomerwithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaCustomerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenCustomerRMAsforaSaleperson {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaSaleperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenCustomerRMAsforaSalepersonwithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAsforaSalepersonwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Customer RMAs from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenCustomerRMAswithReasons {
            get {
                return ResourceManager.GetString("OpenCustomerRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Purchase Orders from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenPurchaseOrders {
            get {
                return ResourceManager.GetString("OpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Purchase Orders for [#COMPANY_TYPE#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenPurchaseOrdersbyCompanyType {
            get {
                return ResourceManager.GetString("OpenPurchaseOrdersbyCompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Purchase Orders for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenPurchaseOrdersbySupplier {
            get {
                return ResourceManager.GetString("OpenPurchaseOrdersbySupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Quotes.
        /// </summary>
        internal static string OpenQuotes {
            get {
                return ResourceManager.GetString("OpenQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Requirements by Customer.
        /// </summary>
        internal static string OpenRequirementsbyCustomer {
            get {
                return ResourceManager.GetString("OpenRequirementsbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Requirements for [#SALESPERSON#].
        /// </summary>
        internal static string OpenRequirementsReportBySalesperson {
            get {
                return ResourceManager.GetString("OpenRequirementsReportBySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Sales Orders [#POSTED_STATUS#].
        /// </summary>
        internal static string OpenSalesOrders {
            get {
                return ResourceManager.GetString("OpenSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Sales Orders for [#SALESPERSON#][#POSTED_STATUS#].
        /// </summary>
        internal static string OpenSalesOrdersforSalesperson {
            get {
                return ResourceManager.GetString("OpenSalesOrdersforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenSupplierRMAs {
            get {
                return ResourceManager.GetString("OpenSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenSupplierRMAsforaBuyer {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenSupplierRMAsforaBuyerwithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaBuyerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenSupplierRMAsforaSupplier {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenSupplierRMAsforaSupplierwithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAsforaSupplierwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Supplier RMAs from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string OpenSupplierRMAswithReasons {
            get {
                return ResourceManager.GetString("OpenSupplierRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outstanding Orders to be Shipped [#DUEDATE#].
        /// </summary>
        internal static string OrdersToBeShipped {
            get {
                return ResourceManager.GetString("OrdersToBeShipped", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outstanding Orders to be Shipped [#DUEDATE#] for [#SALESPERSON#] .
        /// </summary>
        internal static string OrdersToBeShippedBySalesperson {
            get {
                return ResourceManager.GetString("OrdersToBeShippedBySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outstanding Customer Invoices [#LATE_ONLY#].
        /// </summary>
        internal static string OustandingCustomerInvoices {
            get {
                return ResourceManager.GetString("OustandingCustomerInvoices", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick Sheet for [#DUEDATE#].
        /// </summary>
        internal static string PickSheetSalesOrdersBasic {
            get {
                return ResourceManager.GetString("PickSheetSalesOrdersBasic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick Sheet for [#DUEDATE#].
        /// </summary>
        internal static string PickSheetSalesOrdersDetailed {
            get {
                return ResourceManager.GetString("PickSheetSalesOrdersDetailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Pick Sheet for [#DUEDATE#].
        /// </summary>
        internal static string PickSheetSupplierRMAs {
            get {
                return ResourceManager.GetString("PickSheetSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Posted Lines Only.
        /// </summary>
        internal static string PostedOnly {
            get {
                return ResourceManager.GetString("PostedOnly", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders Due In from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string PurchaseOrdersDueIn {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders Due In for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string PurchaseOrdersDueInforBuyer {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueInforBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Orders Due In for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string PurchaseOrdersDueInforSalesperson {
            get {
                return ResourceManager.GetString("PurchaseOrdersDueInforSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Price Request summary.
        /// </summary>
        internal static string PurchaseQuote {
            get {
                return ResourceManager.GetString("PurchaseQuote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions.
        /// </summary>
        internal static string PurchaseRequisitions {
            get {
                return ResourceManager.GetString("PurchaseRequisitions", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions for [#COMPANY_NAME#].
        /// </summary>
        internal static string PurchaseRequisitionsforaCustomer {
            get {
                return ResourceManager.GetString("PurchaseRequisitionsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions for [#SALESPERSON#].
        /// </summary>
        internal static string PurchaseRequisitionsforaSalesPerson {
            get {
                return ResourceManager.GetString("PurchaseRequisitionsforaSalesPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Random Stock Check for [#WAREHOUSE#].
        /// </summary>
        internal static string RandomStockCheck {
            get {
                return ResourceManager.GetString("RandomStockCheck", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ReceivedCustomerRMAs {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ReceivedCustomerRMAsforaCustomer {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs with Reasons for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ReceivedCustomerRMAsforaCustomerwithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaCustomerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ReceivedCustomerRMAsforaSaleperson {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaSaleperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs with Reasons for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ReceivedCustomerRMAsforaSalepersonwithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAsforaSalepersonwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Customer RMAs with Reasons from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ReceivedCustomerRMAswithReasons {
            get {
                return ResourceManager.GetString("ReceivedCustomerRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Goods Valuation by Country for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ReceivedGoodsValuationbyCountry {
            get {
                return ResourceManager.GetString("ReceivedGoodsValuationbyCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Requirement summary With HUBRFQ.
        /// </summary>
        internal static string RequirementWithBOM {
            get {
                return ResourceManager.GetString("RequirementWithBOM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Goods Valuation by Country for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ShippedGoodsValuationbyCountry {
            get {
                return ResourceManager.GetString("ShippedGoodsValuationbyCountry", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Orders from [#STARTDATE#] to [#ENDDATE#] Ordered by Invoice Number.
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumber {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Orders for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#] Ordered by Invoice Number.
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumberforaCustomer {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumberforaCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Orders for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#] Ordered by Invoice Number.
        /// </summary>
        internal static string ShippedOrdersSortedbyInvoiceNumberforaSalesperson {
            get {
                return ResourceManager.GetString("ShippedOrdersSortedbyInvoiceNumberforaSalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Sales for Lot [#LOT#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ShippedSalesforLot {
            get {
                return ResourceManager.GetString("ShippedSalesforLot", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ShippedSupplierRMAs {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAs", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ShippedSupplierRMAsforaBuyer {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaBuyer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ShippedSupplierRMAsforaBuyerwithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaBuyerwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ShippedSupplierRMAsforaSupplier {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaSupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ShippedSupplierRMAsforaSupplierwithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAsforaSupplierwithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Supplier RMAs from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string ShippedSupplierRMAswithReasons {
            get {
                return ResourceManager.GetString("ShippedSupplierRMAswithReasons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Count for [#WAREHOUSE#].
        /// </summary>
        internal static string StockCount {
            get {
                return ResourceManager.GetString("StockCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock List.
        /// </summary>
        internal static string StockList {
            get {
                return ResourceManager.GetString("StockList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Valuation for [#WAREHOUSE#].
        /// </summary>
        internal static string StockValuation {
            get {
                return ResourceManager.GetString("StockValuation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Booked Orders by Customer from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping.
        /// </summary>
        internal static string SummaryBookedOrdersbyCustomer {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Booked Orders by Division from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping.
        /// </summary>
        internal static string SummaryBookedOrdersbyDivision {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Booked Orders by Salesperson from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping.
        /// </summary>
        internal static string SummaryBookedOrdersbySalesperson {
            get {
                return ResourceManager.GetString("SummaryBookedOrdersbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Open Orders by Customer from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping.
        /// </summary>
        internal static string SummaryOpenOrdersbyCustomer {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Open Orders by Division from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping.
        /// </summary>
        internal static string SummaryOpenOrdersbyDivision {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Open Orders by Salesperson from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping.
        /// </summary>
        internal static string SummaryOpenOrdersbySalesperson {
            get {
                return ResourceManager.GetString("SummaryOpenOrdersbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Shipped Sales by Customer from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping[#INCLUDING_CREDITS#].
        /// </summary>
        internal static string SummaryShippedSalesbyCustomer {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbyCustomer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Shipped Sales by Division from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping[#INCLUDING_CREDITS#].
        /// </summary>
        internal static string SummaryShippedSalesbyDivision {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbyDivision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Summary Shipped Sales by Salesperson from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping[#INCLUDING_CREDITS#].
        /// </summary>
        internal static string SummaryShippedSalesbySalesperson {
            get {
                return ResourceManager.GetString("SummaryShippedSalesbySalesperson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier On Time Delivery Report from [#STARTDATE#] to [#ENDDATE#].
        /// </summary>
        internal static string SupplierOnTimeDeliveryReport {
            get {
                return ResourceManager.GetString("SupplierOnTimeDeliveryReport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to User List.
        /// </summary>
        internal static string UserList {
            get {
                return ResourceManager.GetString("UserList", resourceCulture);
            }
        }
    }
}
