﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
CREATE OR ALTER PROCEDURE [dbo].[usp_datalistnugget_ClientInvoice]--108,@PageSize=50                 
--********************************************************************************************                        
   --* Created By :- Prakash Choudhary                      
   --* Created Date: - 08-04-2016                      
   --* Description :- Search Client invoice                      
--********************************************************************************************   
/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[BUG-243934]     NgaiTo		 	05-May-2025			Update		243934: [PROD Bug] CInvoice - GT Not showing Client Invoices without Lines in Search
===========================================================================================  
*/
    @ClientId int                        
  , @OrderBy int = 1                        
  , @SortDir int = 1                        
  , @PageIndex int = 0                        
  , @PageSize int = 10                        
  , @CINoLo int = NULL                        
  , @CINoHi int = NULL                              
  , @URNNoLo int = NULL                        
  , @URNNoHi int = NULL                        
  , @PurchaseOrderNoLo int = NULL                        
  , @PurchaseOrderNoHi int = NULL                        
  , @GoodsInNoLo int = NULL                        
  , @GoodsInNoHi int = NULL                       
  , @ClientInvoiceDateFrom datetime = NULL                        
  , @ClientInvoiceDateTo datetime = NULL                         
  , @CMSearch nvarchar(50) = NULL                        
  , @RecentOnly bit = 1                        
  , @ExportedOnly bit = NULL                
  , @ApproveAndUnExported bit = NULL                
  , @CannotBeExported bit = NULL 
  , @SelectedClientNo int = NULL                        
    WITH RECOMPILE                        
AS                         
    DECLARE @RecentDate datetime                        
      , @StartPage int                        
      , @EndPage int                        
    SET @RecentDate = dbo.ufn_get_date_from_datetime(dateadd(mm, -3, getdate()))                        
    SET @StartPage = (@PageIndex * @PageSize + 1)                        
    SET @EndPage = ((@PageIndex + 1) * @PageSize)                        
-- semi-colon needed for WITH                        
 ;  WITH    cteSearch                        
              AS (SELECT    ci.ClientInvoiceId                      
                          , ci.ClientInvoiceNumber                      
                          --, ci.SupplierName 
                          ,cl.Clientname                     
                          , gi.GoodsInNumber                      
                          , po.PurchaseOrderNumber                      
                          , isnull(gi.PurchaseOrderNo, po.PurchaseOrderId) AS PurchaseOrderNo                      
                          , ci.ClientInvoiceDate                      
                          , cil.Part                      
                          , ci.InvoiceAmount                     
                          , ci.CompanyNo                     
                          , isnull(cil.GoodsInNo,gi.GoodsInId) as GoodsInId                  
                          , ci.CurrencyNo                  
                          , gcu.GlobalCurrencyName as CurrencyCode     
                          , ci.URNNumber 
						  ,ci.InvoiceClientNo 
						  , ipo.InternalPurchaseOrderId AS InternalPurchaseOrderNo    
						  , ipo.InternalPurchaseOrderNumber 
                          , ROW_NUMBER() OVER (ORDER BY --                        
                case WHEN @OrderBy = 1                        
                                            AND @SortDir = 2 THEN ci.ClientInvoiceNumber                        
                                       END DESC                         
                                , case WHEN @OrderBy = 1 THEN ci.ClientInvoiceNumber                        
                                       END                        
                                     , case WHEN @OrderBy = 2                        
											AND @SortDir = 2 THEN cl.Clientname                        
                                       END DESC                        
                                     , case WHEN @OrderBy = 2 THEN cl.Clientname                      
                                      END                        
                                     , case WHEN @OrderBy = 3                        
                                            AND @SortDir = 2 THEN gi.GoodsInNumber                       
                                        END DESC                        
                                     , case WHEN @OrderBy = 3 THEN gi.GoodsInNumber                      
                                       END                        
                                     , case WHEN @OrderBy = 4            
                                            AND @SortDir = 2 THEN ci.URNNumber                     
                                       END DESC                        
                                   , case WHEN @OrderBy = 4 THEN ci.URNNumber                     
                                       END                        
                                     , case WHEN @OrderBy = 5                        
                                            AND @SortDir = 2 THEN ci.ClientInvoiceDate                       
                                       END DESC                        
                                     , case WHEN @OrderBy = 5 THEN ci.ClientInvoiceDate                      
                                       END                       
                                     , case WHEN @OrderBy = 6                        
                                            AND @SortDir = 2 THEN cil.Part                    
                                       END DESC                        
                                     , case WHEN @OrderBy = 6 THEN cil.Part                    
                                       END                         
                                     , case WHEN @OrderBy = 7                        
                                                 AND @SortDir = 2 THEN ci.InvoiceAmount                      
                                       END DESC                        
                                     , case WHEN @OrderBy = 7 THEN ci.InvoiceAmount                      
                                       END) AS RowNum                        
                  FROM      tbClientInvoice ci                      
                  LEFT JOIN tbClientInvoiceLine cil on  ci.ClientInvoiceId=cil.ClientInvoiceNo                      
                  LEFT JOIN tbGoodsIn gi ON cil.GoodsInNo = gi.GoodsInId                        
                  LEFT JOIN tbPurchaseOrder po ON gi.PurchaseOrderNo = po.PurchaseOrderId 
				  LEFT JOIN tbInternalPurchaseOrder ipo ON ipo.PurchaseOrderNo = po.PurchaseOrderId                    
                  LEFT JOIN tbGlobalCurrencyList gcu on ci.CurrencyNo=gcu.GlobalCurrencyId 
                  LEFT JOIN tbClient cl on ci.InvoiceClientNo=cl.ClientId
                  WHERE     ci.ClientNo = @ClientId                              
                            AND ((@RecentOnly = 0)                        
                                 OR (@RecentOnly = 1                        
                                     AND ci.ClientInvoiceDate >= @RecentDate))                        
                                                           
                           --AND ((@ClientInvoiceNumber IS NULL)                        
                           --      OR ( ci.ClientInvoiceNumber LIKE @ClientInvoiceNumber))          
                            
                            AND ((@CINoLo IS NULL)                        
                                 OR (NOT @CINoLo IS NULL                        
								AND ci.ClientInvoiceNumber >= @CINoLo))
								
                            AND ((@CINoHi IS NULL)  
                    OR (NOT @CINoHi IS NULL                        
                                     AND ci.ClientInvoiceNumber <= @CINoHi))                          
                            AND ((@URNNoLo IS NULL)                        
                                 OR (NOT @URNNoLo IS NULL                        
                                     AND ci.URNNumber >= @URNNoLo))                        
                            AND ((@URNNoHi IS NULL)                        
                                 OR (NOT @URNNoHi IS NULL                        
                                     AND ci.URNNumber <= @URNNoHi))                        
                            AND ((@PurchaseOrderNoLo IS NULL)                        
                                 OR (NOT @PurchaseOrderNoLo IS NULL                        
                                     AND po.PurchaseOrderNumber >= @PurchaseOrderNoLo))                        
                            AND ((@PurchaseOrderNoHi IS NULL)                 
                                 OR (NOT @PurchaseOrderNoHi IS NULL                        
                                     AND po.PurchaseOrderNumber <= @PurchaseOrderNoHi))                 
                            AND ((@GoodsInNoLo IS NULL)                        
                                 OR (NOT @GoodsInNoLo IS NULL                        
                                     AND gi.GoodsInNumber >= @GoodsInNoLo))                        
                          AND ((@GoodsInNoHi IS NULL)                        
                                 OR (NOT @GoodsInNoHi IS NULL                        
                                     AND gi.GoodsInNumber <= @GoodsInNoHi))                         
                            AND ((@ClientInvoiceDateFrom IS NULL)                        
                           OR (NOT @ClientInvoiceDateFrom IS NULL                        
                                     AND ci.ClientInvoiceDate >= @ClientInvoiceDateFrom))                        
                            AND ((@ClientInvoiceDateTo IS NULL)                        
                                 OR (NOT @ClientInvoiceDateTo IS NULL                        
                                     AND ci.ClientInvoiceDate <= @ClientInvoiceDateTo))                        
                            AND ((@CMSearch IS NULL)                        
                                 OR (NOT @CMSearch IS NULL                        
                                     AND cl.Clientname LIKE @CMSearch))                  
                             AND ((@ExportedOnly IS NULL)                        
                                 OR (ISNULL(ci.Exported,0)=@ExportedOnly))                        
                            AND ((@CannotBeExported IS NULL )                        
                                 OR (ISNULL(ci.CanbeExported,0)=@CannotBeExported))         
                                         
                            AND ((@ApproveAndUnExported IS NULL) OR (ISNULL(ci.CanbeExported,0)=1 AND ISNULL(ci.Exported,0)=0))                      
                            AND ((@SelectedClientNo IS NULL)                        
                                 OR (NOT @SelectedClientNo IS NULL                        
                                     AND ci.InvoiceClientNo = @SelectedClientNo)) 
                            --And gi.ClientNo= @SelectedClientNo
                 )                        
        SELECT  *                        
              , (SELECT count(*)                        
                 FROM   cteSearch                        
                ) AS RowCnt                        
        FROM    cteSearch                        
        WHERE   RowNum BETWEEN @StartPage AND @EndPage                        
  ORDER BY RowNum



GO


