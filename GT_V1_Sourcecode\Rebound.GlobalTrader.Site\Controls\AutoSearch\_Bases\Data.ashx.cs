using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.AutoSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Base : Rebound.GlobalTrader.Site.Data.Base, IHttpHandler {

		protected Rebound.GlobalTrader.Site.AutoSearch _objAutoSearch;

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {

                int _intID = GetFormValue_Int("cLogIn");
                //Log out, if user login from diferent login in other tab
                if (_intID != SessionManager.LoginID)
                {
                    WriteErrorNoSession();
                    //return false;
                }

				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		protected virtual void GetData() { }

		/// <summary>
		/// Output company list data
		/// </summary>
		protected void OutputCompanyList(List<Company> lst) {
			JsonObject jsn = new JsonObject();
			jsn.AddVariable("TotalRecords", lst.Count);
			JsonObject jsnRows = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				if (i < lst.Count) {
					JsonObject jsnRow = new JsonObject();
					jsnRow.AddVariable("ID", lst[i].CompanyId);
					jsnRow.AddVariable("Name", lst[i].CompanyName);
                    //jsnRow.AddVariable("SupNo", lst[i].CompanyId);
                    //jsnRow.AddVariable("Email", lst[i].EMail);
					jsnRows.AddVariable(jsnRow);
					jsnRow.Dispose();
					jsnRow = null;
				}
			}
			jsn.AddVariable("Results", jsnRows);
			OutputResult(jsn);
			jsnRows.Dispose(); jsnRows = null;
			jsn.Dispose(); jsn = null;
		}

        /// <summary>
        /// Output company list data
        /// </summary>
        protected void OutputSalesPersionList(List<Login> lst)
        {
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("TotalRecords", lst.Count);
            JsonObject jsnRows = new JsonObject(true);
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lst[i].LoginId);
                    jsnRow.AddVariable("Name", lst[i].EmployeeName);
                    jsnRows.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRows);
            OutputResult(jsn);
            jsnRows.Dispose(); jsnRows = null;
            jsn.Dispose(); jsn = null;
        }
        protected void OutputCountryList(List<GlobalCountryList> lst)
        {
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("TotalRecords", lst.Count);
            JsonObject jsnRows = new JsonObject(true);
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lst[i].GlobalCountryId);
                    jsnRow.AddVariable("Name", lst[i].GlobalCountryName);
                    jsnRows.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRows);
            OutputResult(jsn);
            jsnRows.Dispose(); jsnRows = null;
            jsn.Dispose(); jsn = null;
        }
        protected void OutputQuoteList(List<Quote> lst)
        {
            JsonObject jsn = new JsonObject();
            jsn.AddVariable("TotalRecords", lst.Count);
            JsonObject jsnRows = new JsonObject(true);
            for (int i = 0; i < lst.Count; i++)
            {
                if (i < lst.Count)
                {
                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lst[i].QuoteId);
                    jsnRow.AddVariable("QuoteNumber", lst[i].QuoteNumber);
                    jsnRows.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
            }
            jsn.AddVariable("Results", jsnRows);
            OutputResult(jsn);
            jsnRows.Dispose(); jsnRows = null;
            jsn.Dispose(); jsn = null;
        }
    }
}