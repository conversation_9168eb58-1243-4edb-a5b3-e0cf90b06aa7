///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - complete dispose event
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.Confirmation = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Confirmation.initializeBase(this, [element]);
	this._dummy = null;
};

Rebound.GlobalTrader.Site.Controls.Confirmation.prototype = {

	get_ibtnYes: function() { return this._ibtnYes; }, 	set_ibtnYes: function(value) { if (this._ibtnYes !== value)  this._ibtnYes = value; }, 
	get_ibtnNo: function() { return this._ibtnNo; }, 	set_ibtnNo: function(value) { if (this._ibtnNo !== value)  this._ibtnNo = value; }, 
	get_dummy: function() { return this._dummy; }, 	set_dummy: function(value) { if (this._dummy !== value)  this._dummy = value; }, 

	addClickYesEvent: function(handler) { this.get_events().addHandler("ClickYes", handler); },
	removeClickYesEvent: function(handler) { this.get_events().removeHandler("ClickYes", handler); },
	onClickYes: function() { 
		var handler = this.get_events().getHandler("ClickYes");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	addClickNoEvent: function(handler) { this.get_events().addHandler("ClickNo", handler); },
	removeClickNoEvent: function(handler) { this.get_events().removeHandler("ClickNo", handler); },
	onClickNo: function() { 
		var handler = this.get_events().getHandler("ClickNo");
		if (handler) handler(this, Sys.EventArgs.Empty); 
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Confirmation.callBaseMethod(this, "initialize");
		$R_IBTN.addClick(this._ibtnYes, Function.createDelegate(this, this.onClickYes));
		$R_IBTN.addClick(this._ibtnNo, Function.createDelegate(this, this.onClickNo));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._ibtnYes) $R_IBTN.clearHandlers(this._ibtnYes);
		if (this._ibtnNo) $R_IBTN.clearHandlers(this._ibtnNo);
		this._ibtnYes = null;
		this._ibtnNo = null;
		Rebound.GlobalTrader.Site.Controls.Confirmation.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	}
	
};

Rebound.GlobalTrader.Site.Controls.Confirmation.registerClass("Rebound.GlobalTrader.Site.Controls.Confirmation", Sys.UI.Control, Sys.IDisposable);