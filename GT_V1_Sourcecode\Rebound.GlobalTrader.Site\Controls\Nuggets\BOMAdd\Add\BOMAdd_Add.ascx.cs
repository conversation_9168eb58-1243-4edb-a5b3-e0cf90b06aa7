using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class BOMAdd_Add : Base
    {

        #region Locals
        protected FlexiDataTable _tblPVVBOM;
        protected IconButton _ibtnEditPPV;
        protected IconButton _ibtnDeletePPV;
        protected IconButton _ibtnViewPPV;

        #endregion

        #region Overrides
        private void SetupTable()
        {
            _tblPVVBOM.AllowSelection = false;
            _tblPVVBOM.Columns.Add(new FlexiDataColumn("Question", WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsNotes)));
            _tblPVVBOM.Columns.Add(new FlexiDataColumn("Answer", WidthManager.GetWidth(WidthManager.ColumnWidth.HUBRFQBOMItemsNotes)));

        }
        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "BOMAdd_Add");
            WireUpControls();

            AddScriptReference("Controls.Nuggets.BOMAdd.Add.BOMAdd_Add.js");
            SetupTable();

        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e) {
            WireUpButtons();
            SetupScriptDescriptors();
            _ibtnEditPPV.Visible = true;
            _ibtnDeletePPV.Visible = true;
            _ibtnViewPPV.Visible = true;
            base.OnPreRender(e);
		}
        #endregion

        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCompanyID", _objQSManager.CompanyID);
            _scScriptControlDescriptor.AddProperty("strCompanyName", _objQSManager.CompanyName);
            _scScriptControlDescriptor.AddComponentProperty("tblPVVBOM", _tblPVVBOM.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnEditPPV", _ibtnEditPPV.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnDeletePPV", _ibtnDeletePPV.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnViewPPV", _ibtnViewPPV.ClientID);

        }
        private void WireUpButtons()
        {
            _ibtnEditPPV = (IconButton)ctlDesignBase.FindContentControl("ibtnEditPPV");
            _ibtnDeletePPV = (IconButton)ctlDesignBase.FindContentControl("ibtnDeletePPV");
            _ibtnViewPPV = (IconButton)ctlDesignBase.FindContentControl("ibtnViewPPV");
        }
        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
           
            _tblPVVBOM = (FlexiDataTable)ctlDesignBase.FindContentControl("tblPVVBOM");
        }
    }
}