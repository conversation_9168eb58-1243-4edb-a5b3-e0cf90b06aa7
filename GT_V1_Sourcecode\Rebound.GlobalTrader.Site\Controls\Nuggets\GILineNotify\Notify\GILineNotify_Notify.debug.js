///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.initializeBase(this, [element]);
    this._intGIID = 0;
    this._intLineID = -1;
    this._intCurrencyID = -1;
    this._strCurrencyCode = "";
    this._dblPOLineShipInCost = 0;
    this._intPOQuantityOrdered = 0;
    this._blnCanEditShipInCost = false;
    this._blnCanEditPurchasePrice = false;
    this._blnRelatedToIPO = false;
    this._intIPOClientNo = -1;
    this._poBankFee = 0;
    this._intGlobalClientNo = -1;
    this._intSerialNoCount = 0;
    this._blnSerNoRecorded = false;
    this._blnProductHaza = false;
    this._blnGISplited = false;
    this._intManufacturerId = -1;
    this._intPackagingType = -1;
    this._intROHSValue = -1;
    this._ctlMSL = "";
    this._intGILineId = -1;
    this._intApprovalType = -1;
    this._intLoginType = 0;
    this._intGINumber = -1;
    this._intUpdatedBy = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.prototype = {

    get_ibtnSend: function () { return this._ibtnSend; }, set_ibtnSend: function (value) { if (this._ibtnSend !== value) this._ibtnSend = value; },
    get_ibtnSend_Footer: function () { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function (v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    get_tblPackagingBreakdown: function () { return this._tblPackagingBreakdown; }, set_tblPackagingBreakdown: function (v) { if (this._tblPackagingBreakdown !== v) this._tblPackagingBreakdown = v; },
    get_tblDateCode: function () { return this._tblDateCode; }, set_tblDateCode: function (v) { if (this._tblDateCode !== v) this._tblDateCode = v; },
    get_intGILineId: function () { return this._intGILineId; }, set_intGILineId: function (v) { if (this._intGILineId !== v) this._intGILineId = v; },
    get_IsPOHub: function () { return this._IsPOHub; }, set_IsPOHub: function (v) { if (this._IsPOHub !== v) this._IsPOHub = v; },
    //get_ibtnGeneratePDF: function () { return this._ibtnGeneratePDF; }, set_ibtnGeneratePDF: function (value) { if (this._ibtnGeneratePDF !== value) this._ibtnGeneratePDF = value; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addCancel(Function.createDelegate(this, this.cancelClicked));


    },

    formShown: function () {
        this.getGIQuery();
        if (this._blnFirstTimeShown) {
            $R_IBTN.addClick(this._ibtnSend, Function.createDelegate(this, this.sendMail));
            $R_IBTN.addClick(this._ibtnSend_Footer, Function.createDelegate(this, this.sendMail));
        }
        $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedSales')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedPurchase')).getData();
        $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedQuality')).getData();
        $addHandler($get("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibtnGeneratePDF_hyp"), "click", this.createPDF);
        $addHandler($get("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibnQuarantineProduct_hyp"), "click", this.ReportNPR);
        document.getElementById("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibtnGeneratePDF_hyp").style.color = "white";
        document.getElementById("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibnQuarantineProduct_hyp").style.color = "white";

        $addHandler($get("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibnAddImage_hyp"), "click", this.uploadImage);
        document.getElementById("ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ibnAddImage_hyp").style.color = "white";
        $find(this.getFormControlID(this._element.id, 'ddlUpdateType')).getData();
        $("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlUpdateType_ddl").prop("disabled", true);
        $("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlUpdateType_ctl02").css("display", "none");

        $find(this.getFormControlID(this._element.id, 'ddlROHSStatus')).getData();
        $("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlROHSStatus_ddl").prop("disabled", true);
        $("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlROHSStatus_ctl02").css("display", "none");
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        if (this._tblPackagingBreakdown) this._tblPackagingBreakdown.dispose();
        this._tblPackagingBreakdown = null;
        if (this._tblDateCode) this._tblDateCode.dispose();
        this._tblDateCode = null;
        Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.callBaseMethod(this, "dispose");
    },
    //[001] code start
    sendMail: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILineNotify");
        obj.set_DataObject("GILineNotify");
        obj.set_DataAction("NotifyQuery");
        obj.addParameter("id", this._intGILineId);
        //obj.addParameter("IsQueryApproved", this.getControlValue(this.getFormControlID(this._element.id, 'chkQueryApproved'), 'CheckBox'));
        //obj.addParameter("IsBySales", this.getControlValue(this.getFormControlID(this._element.id, 'chkBySales'), 'CheckBox'));
        //obj.addParameter("IsByPurchasing", this.getControlValue(this.getFormControlID(this._element.id, 'chkByPurchasing'), 'CheckBox'));
        //obj.addParameter("IsByQuality", this.getControlValue(this.getFormControlID(this._element.id, 'chkByQuality'), 'CheckBox'));
        obj.addParameter("QueryApprovedStatusSales", $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedSales')).getValue());
        obj.addParameter("QueryApprovedStatusPurchase", $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedPurchase')).getValue());
        obj.addParameter("QueryApprovedStatusQuality", $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedQuality')).getValue());
        obj.addParameter("IsPDFReportRequired", this.getControlValue(this.getFormControlID(this._element.id, 'chkPDFReportRequired'), 'CheckBox'));
        obj.addParameter("IsQuarantineProduct", this.getControlValue(this.getFormControlID(this._element.id, 'chkQuarantineProduct'), 'CheckBox'));
        obj.addParameter("QueryReply", $get(this.getFormControlID(this._element.id, 'GIQueryReply')).value);
        obj.addParameter("LoginType", this._intLoginType);
        obj.addParameter("GINumber", this._intGINumber);
        obj.addParameter("UpdatedBy", this._intUpdatedBy);

        obj.addDataOK(Function.createDelegate(this, this.sendMailComplete));
        obj.addError(Function.createDelegate(this, this.sendMailError));
        obj.addTimeout(Function.createDelegate(this, this.sendMailError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        //this.showLoading(true);
        //this.enableButton(false);
    },
    //[001] code end
    validateForm: function () {
        var blnOK = true;
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMailComplete: function () {
        this.showLoading(false);
        this.showSavedOK(true);
        location.href = $RGT_gotoURL_GoodsIn(this._intGIID);
        this.enableButton(true);
    },
    sendMailError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },
    cancelClicked: function () {
        if (this._intGIID <= 0)
            $R_FN.navigateBack();
        else
            location.href = $RGT_gotoURL_GoodsIn(this._intGIID);
    },
    enableButton: function (bln) {
        $R_IBTN.enableButton(this._ibtnSend, bln);
        $R_IBTN.enableButton(this._ibtnSend_Footer, bln);
    },
    setCurrency: function (intID, strCode) {
        this._intCurrencyID = intID;
        this._strCurrencyCode = strCode;
        $R_FN.setInnerHTML(this._lblCurrency_Price, strCode);
        $R_FN.setInnerHTML(this._lblCurrency_PriceLabel, strCode);
        $R_FN.setInnerHTML(this._lblCurrency_Price_IPO, strCode);
        $R_FN.setInnerHTML(this._lblCurrency_PriceLabel_IPO, strCode);
    },

    setCurrencyIPO: function (strCode) {
        this._strCurrencyCode = strCode;
        $R_FN.setInnerHTML(this._lblCurrency_Price_IPO, strCode);
        $R_FN.setInnerHTML(this._lblCurrency_PriceLabel_IPO, strCode);
    },
    showHideQuantity: function (bln) {
        if (bln == true) {
            $('#' + (this.getFormControlID(this._element.id, 'txtQuantity'))).prop('disabled', true);
            $find((this.getFormControlID(this._element.id, 'chkFullQuantityReceived'))).enableButton(false);
        }
        else {
            if (this.getControlValue(this.getFormControlID(this._element.id, 'chkFullQuantityReceived'), 'CheckBox') != true) {
                $('#' + (this.getFormControlID(this._element.id, 'txtQuantity'))).prop('disabled', false);
                $find((this.getFormControlID(this._element.id, 'chkFullQuantityReceived'))).enableButton(true);
            }

        }
    },
    getFormControlID: function (ParentId, controlID) {

        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    populatePackagingBreakdownList: function (result) {
        this._tblPackagingBreakdown.clearTable();
        if (result != undefined) {
            if (result.length > 0) {
                for (var i = 0; i < result.length; i++) {
                    var row = result[i];
                    var aryData = [
                        $R_FN.setCleanTextValue(row.PackBreakdownType)
                        , '<label id="NumberOfPacks' + (i + 1) + '" style="width: 85px;">' + row.NumberOfPacks + '</label>'
                        , '<label id="PackSize' + (i + 1) + '" style="width: 85px;">' + row.PackSize + '</label>'
                        , '<label id="TotalPackSize' + (i + 1) + '" style="width: 85px;">' + row.TotalPackSize + '</label>'

                    ];
                    this._tblPackagingBreakdown.addRowRowColor(aryData, row.ID);
                    row = null; aryData = null;
                }
            }
        }
    },
    populateDateCodeList: function (result) {
        this._tblDateCode.clearTable();
        if (result != undefined) {
            if (result.length > 0) {
                for (var i = 0; i < result.length; i++) {
                    var row = result[i];
                    var aryData = [
                        $R_FN.setCleanTextValue(row.DateCodes)
                        , $R_FN.setCleanTextValue(row.Quantity)
                    ];
                    this._tblDateCode.addRowRowColor(aryData, row.ID);
                    row = null; aryData = null;
                }
            }
        }
    },
    getGIQuery: function () {
        $R_FN.showElement(this._pnlLines, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("GetGILineData");
        obj.addParameter("ID", this._intGILineId);
        obj.addDataOK(Function.createDelegate(this, this.getGIQueryOK));
        obj.addError(Function.createDelegate(this, this.getGIQueryError));
        obj.addTimeout(Function.createDelegate(this, this.getGIQueryError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    processFormLoadData: function () {
        if (!this._IsPOHub) {
            $('#' + (this.getFormControlID(this._element.id, 'lblShipInCost'))).css("display", (this._blnCanEditShipInCost && !this._blnGISplited) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblShipInCost'))).css("display", !(this._blnCanEditShipInCost && !this._blnGISplited) == true ? "block" : "none");
        }
        else {
            $('#' + (this.getFormControlID(this._element.id, 'lblShipInCost'))).css("display", "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblShipInCost'))).css("display", "none");

        }
        if (this._blnGISplited == false) {
            if (this._blnRelatedToIPO) {
                $('#' + (this.getFormControlID(this._element.id, 'txtPrice'))).css("display", (this._blnCanEditPurchasePrice && this._IsPOHub) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price'))).css("display", (this._blnCanEditPurchasePrice && this._IsPOHub) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblPrice'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
            }
            else {
                $('#' + (this.getFormControlID(this._element.id, 'txtPrice'))).css("display", (this._blnCanEditPurchasePrice) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price'))).css("display", (this._blnCanEditPurchasePrice) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblPrice'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
            }
        }
        else {
            if (this._blnRelatedToIPO) {
                $('#' + (this.getFormControlID(this._element.id, 'txtPrice'))).css("display", (this._blnCanEditPurchasePrice && this._IsPOHub && !this._blnGISplited) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price'))).css("display", (this._blnCanEditPurchasePrice && this._IsPOHub && !this._blnGISplited) == true ? "block" : "none");

                $('#' + (this.getFormControlID(this._element.id, 'lblPrice'))).css("display", (!(this._blnCanEditPurchasePrice && this._IsPOHub && !this._blnGISplited)) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel'))).css("display", (!(this._blnCanEditPurchasePrice && this._IsPOHub && !this._blnGISplited)) == true ? "block" : "none");
            }
            else {
                $('#' + (this.getFormControlID(this._element.id, 'txtPrice'))).css("display", (this._blnCanEditPurchasePrice && !this._blnGISplited) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price'))).css("display", (this._blnCanEditPurchasePrice && !this._blnGISplited) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblPrice'))).css("display", (!(this._blnCanEditPurchasePrice && !this._blnGISplited)) == true ? "block" : "none");
                $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel'))).css("display", (!(this._blnCanEditPurchasePrice && !this._blnGISplited)) == true ? "block" : "none");

            }
        }
        if (this._blnGISplited == false) {
            $('#' + (this.getFormControlID(this._element.id, 'txtPrice_IPO'))).css("display", (this._blnCanEditPurchasePrice && this._blnRelatedToIPO && !this._IsPOHub) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price_IPO'))).css("display", (this._blnCanEditPurchasePrice && this._blnRelatedToIPO && !this._IsPOHub) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblPrice_IPO'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel_IPO'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
        }
        else {
            $('#' + (this.getFormControlID(this._element.id, 'txtPrice_IPO'))).css("display", (this._blnCanEditPurchasePrice && this._blnRelatedToIPO && !this._IsPOHub && !this._blnGISplited) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_Price_IPO'))).css("display", (this._blnCanEditPurchasePrice && this._blnRelatedToIPO && !this._IsPOHub && !this._blnGISplited) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblPrice_IPO'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
            $('#' + (this.getFormControlID(this._element.id, 'lblCurrency_PriceLabel_IPO'))).css("display", (!this._blnCanEditPurchasePrice) == true ? "block" : "none");
        }
        $find((this.getFormControlID(this._element.id, 'chkReqSerailNo'))).enableButton(!(this._intSerialNoCount > 0));
        $find((this.getFormControlID(this._element.id, 'chkSerialNosRecorded'))).enableButton(!(this._intSerialNoCount > 0));
        if (this._blnGISplited == false)
            this.showHideQuantity(this._blnSerNoRecorded);
        else
            this.showHideQuantity(this._blnGISplited);
        $find((this.getFormControlID(this._element.id, 'chkFullQuantityReceived'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkPartNumberCorrect'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkManufacturerCorrect'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkDateCodeCorrect'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkDateCodeRequired'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkPackageCorrect'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkMSLCorrect'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkHICCorrect'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkRohsStatusCorrect'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkReqSerailNo'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkLotCodeReq'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkEnhancedInpectionRequired'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkbakingYes'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkbakingNo'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkbakingNA'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkInspectionConducted'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkUnavailable'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkSerialNosRecorded'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkPrintHazWar'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkSales'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkPurchasing'))).enableButton(false);
        $find((this.getFormControlID(this._element.id, 'chkQualityApproval'))).enableButton(false);
    },
    setFieldsFromGIQuery: function (res) {
        if (!res) return;
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblGoodsIn'), res.GoodsInNumber, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblSupplier'), res.CompanyName, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblSupplierType'), res.CompanyType, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblAirWayBill'), res.AirWayBill, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblReference'), res.Reference, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblLocation'), res.Location, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblLot'), $R_FN.setCleanTextValue(res.LotName), "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtQualityControlNotes'), res.QCNotes, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblQuantityOrdered'), res.QuantityOrdered, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtQuantity'), res.Quantity, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblPartNo'), res.Part, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblManufacturer'), res.Manufacturer, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblDateCode'), res.DateCode, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblPackage'), res.Package, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblCorrectPackage'), res.CorrectPackageName, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblMSL'), res.MSLLevel, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblCountryOfManufacture'), res.CountryOfManufactureName);
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblCountingMethod'), res.CountingMethod);
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkReqSerailNo'), res.ReqSeriaNo, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkLotCodeReq'), res.IsLotCodesReq, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkEnhancedInpectionRequired'), res.EnhancedInspectionReq, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtGeneralInspectionNotes'), res.GeneralInspectionNotes, "");
        if (res.BakingLevelAdded == true) {
            this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkbakingYes'), res.BakingLevelAdded, "");
        }
        else if (res.BakingLevelAdded == "false") {
            this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkbakingNo'), true, "");
        }
        else {
            this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkbakingNA'), true, "");
        }
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkInspectionConducted'), res.IsInspectionConducted, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblReceivingNotes'), res.ReceivingNotes, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtSupplierPart'), res.SupplierPart, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblProducts'), res.Product);
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtPrice'), res.Price, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblPrice'), res.Price, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtPrice_IPO'), res.ClientPriceVal, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblPrice_IPO'), res.ClientPriceVal, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtShipInCost'), res.ShipInCostActVal, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblShipInCost'), res.ShipInCostActVal, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkUnavailable'), res.Unavailable, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkSerialNosRecorded'), res.SerialNosRecorded, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtPartMarkings'), res.PartMarkings, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkPrintHazWar'), res.IsPrintHaz, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtLineNotes'), res.LineNotes, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblCorrectRohsStatus'), res.ROHSValue, "");

        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtCorrectPartNo'), res.CorrectPartNo, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtCorrectDateCode'), res.CorrectDateCode, "");
        //this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblManufacturer'), res.ProductNo, res.Product"));
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtCorrectDateCode'), res.CorrectDateCode, "");
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblPackage'), res.CorrectPackageName);
        this.setControlValue("Literal", this.getFormControlID(this._element.id, 'lblCorrectMSL'), res.CorrectMSLLevel, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtCorrectHIC'), res.CorrectHICStatus, "");
        this.setControlValue("DropDown", this.getFormControlID(this._element.id, 'ddlROHSStatus'), "", res.CorrectROHSStatus);

        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkFullQuantityReceived'), res.IsFullQtyRecieved == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkPartNumberCorrect'), res.IsPartNoCorrect == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkManufacturerCorrect'), res.IsManufacturerCorrect == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkDateCodeCorrect'), res.IsDateCodeCorrect == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkPackageCorrect'), res.IsPackageTypeCorrect == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkMSLCorrect'), res.IsMSLLevelCorrect == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkHICCorrect'), res.IsHICStatusCorrect == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkRohsStatusCorrect'), res.IsROHSStatusCorrect == true ? true : false, "");
        //if (res.HICStatus <= 0)
        //    $find((this.getFormControlID(this._element.id, 'chkHICCorrect'))).enableButton(false);
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkDateCodeRequired'), res.IsDateCodeRequired == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkSales'), res.IsSalesNotify, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkPurchasing'), res.IsPurchaseNotify, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkQualityApproval'), res.IsQualityNotify, "");
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtGIAllQueries'), res.GIQuery.replaceAll(/((<br \/>)|(<br>)|(<br\/>))/gi, "\r\n"), "");

        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkPDFReportRequired'), res.IsPDFReportRequired == true ? true : false, "");
        this.setControlValue("CheckBox", this.getFormControlID(this._element.id, 'chkQuarantineProduct'), res.IsQuarantineProduct == true ? true : false, "");
        $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedSales')).setValue(res.SalesApprovalStatus);
        $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedPurchase')).setValue(res.PurchaseApprovalStatus);
        $find(this.getFormControlID(this._element.id, 'ddlQueryApprovedQuality')).setValue(res.QualityApprovalStatus);

        if (res.IsDateCodeRequired == true) {
            $('#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_tblDateCode').css("display", "block");
            $('#lblDateCodeReceived').css("display", "block");
        }
        else {
            $('#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_tblDateCode').css("display", "none");
            $('#lblDateCodeReceived').css("display", "none");
        }
        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'txtPackageBreakdownInfo'), res.PKGBreakdownMismatch, "");
        this.enableApprovalDropDownByGroupName(res.LoginGroupMember, res);

        this.setCurrency(res.CurrencyID, res.CurrencyCode);
        this.setCurrencyIPO(res.ClientCurCode);
        this._blnCanEditShipInCost = this._blnCanEditShipInCost;
        this._blnCanEditPurchasePrice = this._blnCanEditPurchasePrice;
        this._blnRelatedToIPO = res.RelatedIPO;
        this._intIPOClientNo = res.IPOClientNo;
        this._poBankFee = res.POBankFee;
        this._intGlobalClientNo = -1;//this._intGlobalClientNo;
        this._intSerialNoCount = res.SerialNoCount;
        this._blnSerNoRecorded = (res.SerialNoCount === res.Quantity);
        this._blnProductHaza = res.IsProdHaz;
        //[005] start
        this._StringDLUP = res.StringDLUP;
        this._blnGISplited = res.GISplitted;
        this._intManufacturerId = res.ManufacturerNo;
        this._intPackagingType = res.PackageNo;
        this._ctlMSL = res.MSL;
        this._intROHSValue = res.ROHS;
        this._intGIID = res.GoodsInId;
        this._intGINumber = res.GoodsInNumber;
        this._intUpdatedBy = res.UpdatedBy;
        this.populatePackagingBreakdownList(res.PackagingBreakdown);
        this.populateDateCodeList(res.DateCodeRequired);
        this.processFormLoadData();
    },
    getGIQueryOK: function (args) {
        var res = args._result;
        this.setFieldsFromGIQuery(res);
    },
    getGIQueryError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.showError(true, this._strErrorMessage);
    },
    enableApprovalDropDownByGroupName: function (result, res) {
        
        if (result != undefined) {
            if (result.length > 0) {
                var IsSales = false;
                var IsPurchase = false;
                var IsQuality = false;
                for (var i = 0; i < result.length; i++) {
                    var row = result[i];
                    if (row.Name == 'Sales') {
                        $('#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_trQueryApprovedSales').css("display", "");
                        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'GIQueryReply'), res.SalesQueryReply, "");
                        IsSales = true;
                    }
                    if (row.Name == 'Purchase') {
                        $('#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_trQueryApprovedPurchase').css("display", "");
                        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'GIQueryReply'), res.PurchaseQueryReply, "");
                        IsPurchase = true;
                    }
                    if (row.Name == 'Quality') {
                        $('#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_trQueryApprovedQuality').css("display", "");
                        this.setControlValue("TextBox", this.getFormControlID(this._element.id, 'GIQueryReply'), res.QualityQueryReply, "");
                        IsQuality = true;
                    }
                    row = null;
                }
                if (IsSales == true) {
                    this._intLoginType = 1;
                }
                if (IsPurchase == true) {
                    this._intLoginType = 2;
                }
                if (IsQuality == true) {
                    this._intLoginType = 3;
                }
                if (IsSales == true && IsPurchase == true) {
                    this._intLoginType = 4;
                }
                if (IsSales == true && IsPurchase == true && IsQuality == true) {
                    this._intLoginType = 5;
                }
                if (IsPurchase == true && IsQuality == true) {
                    this._intLoginType = 6;
                }
                if (IsQuality == true && IsSales == true) {
                    this._intLoginType = 7;
                }

            }
        }
    },
    createPDF: function () {
        var baseUrl = (window.location).href;
        var url = new URL(baseUrl);
        var gilId = url.searchParams.get("gilId");
        $R_FN.openPrintWindow($R_ENUM$PrintObject.PrintGIScreenPDF, gilId);
        $("#ctl00_cphMain_ctlGILineNotify_ctlDB_ctl14_ctlNotify_ctlDB_chkPDFReportRequired").attr("class", "imageCheckBoxDisabled");
    },
    ReportNPR: function () {
        var baseUrl = (window.location).href;
        var url = new URL(baseUrl);
        var gilId = url.searchParams.get("gilId");
        $R_FN.openNPRPrintWindow(gilId);
    },
    uploadImage: function () {
        var baseUrl = (window.location).href;
        var url = new URL(baseUrl);
        var gilId = url.searchParams.get("gilId");
        $R_FN.openGIImageWindow(gilId);
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
