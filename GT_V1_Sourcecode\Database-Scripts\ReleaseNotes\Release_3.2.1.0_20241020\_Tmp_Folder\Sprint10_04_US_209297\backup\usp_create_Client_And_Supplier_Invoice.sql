Alter PROCEDURE [dbo].[usp_create_Client_And_Supplier_Invoice]
--********************************************************************************************                                  
--* Marker     Changed by      Date         Remarks                                        
--* [001]      Vinay           08/04/2016   Create supplier and client invoice for Client group and purchase hub              
-- proc will be run through scheduler             

--**********************Need to convert @InvoiceAmount in to usd***********************--       
--********************************************************************************************                 
AS
BEGIN

    SET NOCOUNT ON;
    DECLARE @ClientNo INT
    DECLARE @ClientInvoiceNo INT
    DECLARE @PurchaseHubClientNo INT
    DECLARE @SupplierInvoiceNo INT
    DECLARE @StartDate DATETIME
    --DECLARE @SchedularStartDate DATETIME            
    DECLARE @EndDate DATETIME
    DECLARE @GroupNo BIT
    DECLARE @GoodsInId INT
    DECLARE @POHubCompanyNo INT
    DECLARE @SupplierCode NVARCHAR(20)
    DECLARE @SupplierName NVARCHAR(128)
    DECLARE @CurrencyId INT
    DECLARE @InvoiceAmount FLOAT
    DECLARE @CurrencyCode NVARCHAR(20)
    DECLARE @SchDuration INT
    DECLARE @SchDiffDuration INT

    SET @SupplierInvoiceNo = 0
    SET @ClientInvoiceNo = 0
    SET @CurrencyId = 0
    SET @InvoiceAmount = 0

	DECLARE @GoodsInLineId INT  
	Declare @STOId bigint  
	Declare @ClientInvoiceLineId INT

    --SET @GroupNo = 0              
    --select @StartDate =  dbo.ufn_get_date_from_datetime(dateadd(dd, -5, getdate()))                
    --select @EndDate =  dbo.ufn_get_date_from_datetime(dateadd(dd, -0, getdate()))                


    --GET PURCHASE HUB CLIENT NO    
    SELECT @PurchaseHubClientNo = c.ClientId,
           @CurrencyId = c.CurrencyNo,
           @CurrencyCode = cr.CurrencyCode
    FROM tbClient c
        JOIN tbCurrency cr
            on c.CurrencyNo = cr.CurrencyId
    WHERE ISNULL(IsPOHub, 0) = 1

    DECLARE cur_Client CURSOR STATIC FOR
    SELECT ClientId
    FROM tbClient
    WHERE ISNULL(Inactive, 0) = 0
    OPEN cur_Client
    IF @@CURSOR_ROWS > 0
    BEGIN
        FETCH NEXT FROM cur_Client
        INTO @ClientNo
        WHILE @@Fetch_status = 0
        BEGIN
            SELECT @POHubCompanyNo = CompanyId,
                   @SupplierCode = SupplierCode,
                   @SupplierName = CompanyName
            FROM tbCompany
            where ClientNo = @ClientNo
                  and isnull(IsPOHub, 0) = 1
            -- Get client invoice setting               
            SELECT TOP 1
                @GroupNo = ISNULL(IsGrouped, 0),
                @StartDate = dbo.ufn_get_date_from_datetime(dateadd(dd, - (Duration), isnull(LastRun, getdate()))),
                @EndDate = dbo.ufn_get_date_from_datetime(dateadd(dd, -0, getdate())),
                @SchDuration = ISNULL(Duration, 0),
                @SchDiffDuration = DATEDIFF(dd, isnull(LastRun, getdate()), getdate())
            FROM tbScheduledInvoice
            WHERE ClientNo = @ClientNo
                  AND ISNULL(ISACTIVE, 0) = 1 /*AND ISNULL(IsLocked,0) = 0*/
            -- Return, if client               
            -- 2 1      
            --IF (@StartDate IS NULL OR @EndDate IS NULL OR @GroupNo IS NULL OR DATEADD(dd, 0, DATEDIFF(dd, 0, @SchedularStartDate)) > DATEADD(dd, 0, DATEDIFF(dd, 0, GETDATE())))              
            --IF (@StartDate IS NULL OR @EndDate IS NULL OR @GroupNo IS NULL OR @SchDuration <> @SchDiffDuration)      
            --IF (@StartDate IS NULL OR @EndDate IS NULL OR @GroupNo IS NULL )      

            declare @IsRun bit
            set @IsRun = 0
            IF @SchDuration <= @SchDiffDuration
            BEGIN
                set @IsRun = 1
            END
            IF (
                   @StartDate IS NULL
                   OR @EndDate IS NULL
                   OR @GroupNo IS NULL
                   OR @IsRun = 0
               )
            BEGIN
                CLOSE cur_Client
                DEALLOCATE cur_Client
                RETURN
            END
            DECLARE @HubSellCurrencyNo INT
            DECLARE @LinkMultiCurrencyNo INT
            declare @SplitPONumber INT
            IF (@GroupNo = 1)
            BEGIN
                -- Need to furthure group into warehouse basis.   
                DECLARE @WarehouseNo INT
                DECLARE @TaxMatrixId INT
                DECLARE @HubTaxNo INT
                DECLARE @ClientTaxNo INT
                DECLARE @TaxMatrixIdForCI INT
                DECLARE @HubTaxNoForCI INT
                DECLARE @ClientTaxNoForSI INT

                DECLARE cur_WareHouse_VAT CURSOR FOR
                --SELECT WarehouseNo FROM tbGoodsIn WHERE ClientNo = @ClientNo group by WarehouseNo      
                SELECT gi.WarehouseNo,
                       dbo.ufn_get_TaxMatrix_Id(gi.GoodsInId) as TaxMatrixId,
                       ip.HubCurrencyNo,
                       gil.LinkMultiCurrencyNo,
                       dbo.ufn_splitP_Number(po.PurchaseOrderNumber) as SplitedPONumber,
                       dbo.ufn_get_TaxMatrix_Id_For_ClientInvoice(gi.GoodsInId) AS TaxMatrixIdForCI
                FROM tbGoodsInLine gil
                    JOIN tbGoodsIn gi
                        ON gi.GoodsInId = gil.GoodsInNo
                    JOIN tbInternalPurchaseOrderLine ipl
                        ON gil.PurchaseOrderLineNo = ipl.PurchaseOrderLineNo
                    LEFT JOIN tbInternalPurchaseOrder IP
                        ON ipl.InternalPurchaseOrderNo = IP.InternalPurchaseOrderId
                    --Espire: 11 Jan 21: Split the PO Number     
                    LEFT JOIN tbPurchaseOrder po
                        on IP.PurchaseOrderNo = po.PurchaseOrderId
                WHERE IP.ClientNo = @ClientNo
                      AND ISNULL(gil.IsInvoiceCreated, 0) = 0
                      AND gil.DateInspected IS NOT NULL
                      and gil.CustomerRMALineNo is null
                      AND gi.DateReceived <= @EndDate
                GROUP BY gi.WarehouseNo,
                         dbo.ufn_get_TaxMatrix_Id(gi.GoodsInId),
                         ip.HubCurrencyNo,
                         gil.LinkMultiCurrencyNo,
                         dbo.ufn_splitP_Number(po.PurchaseOrderNumber),
                         dbo.ufn_get_TaxMatrix_Id_For_ClientInvoice(gi.GoodsInId)

                OPEN cur_WareHouse_VAT
                FETCH NEXT FROM cur_WareHouse_VAT
                INTO @WarehouseNo,
                     @TaxMatrixId,
                     @HubSellCurrencyNo,
                     @LinkMultiCurrencyNo,
                     @SplitPONumber,
                     @TaxMatrixIdForCI
                WHILE (@@FETCH_STATUS = 0) -- Inner cursor loop (nested cursor while)        
                BEGIN
                    IF EXISTS
                    (
                        SELECT gil.GoodsInLineId
                        FROM tbGoodsInLine gil
                            JOIN tbGoodsIn gi
                                ON gi.GoodsInId = gil.GoodsInNo
                            JOIN tbInternalPurchaseOrderLine ipl
                                ON gil.PurchaseOrderLineNo = ipl.PurchaseOrderLineNo
                            LEFT JOIN tbInternalPurchaseOrder IP
                                ON ipl.InternalPurchaseOrderNo = IP.InternalPurchaseOrderId
                        WHERE IP.ClientNo = @ClientNo
                              AND gi.WarehouseNo = @WarehouseNo
                              AND dbo.ufn_get_TaxMatrix_Id(gi.GoodsInId) = @TaxMatrixId
                              and IP.HubCurrencyNo = @HubSellCurrencyNo
                              AND gil.LinkMultiCurrencyNo = @LinkMultiCurrencyNo
                              AND ISNULL(gil.IsInvoiceCreated, 0) = 0
                              AND gil.DateInspected IS NOT NULL
                              and gil.CustomerRMALineNo is null
                              AND gi.DateReceived <= @EndDate
                    )
                    -- AND ((@StartDate IS NULL) OR (NOT @StartDate IS NULL  AND gi.DateReceived >= @StartDate))                            
                    -- AND ((@EndDate IS NULL)   OR (NOT @EndDate IS NULL  AND gi.DateReceived <= @EndDate))  )       
                    BEGIN
						-- select goodsinlineid to maintain STO
						SELECT 
							@GoodsInLineId = gil.GoodsInLineId
							FROM tbGoodsInLine gil
								JOIN tbGoodsIn gi
									ON gi.GoodsInId = gil.GoodsInNo
								JOIN tbInternalPurchaseOrderLine ipl
									ON gil.PurchaseOrderLineNo = ipl.PurchaseOrderLineNo
								LEFT JOIN tbInternalPurchaseOrder IP
									ON ipl.InternalPurchaseOrderNo = IP.InternalPurchaseOrderId
							WHERE IP.ClientNo = @ClientNo
								  AND gi.WarehouseNo = @WarehouseNo
								  AND dbo.ufn_get_TaxMatrix_Id(gi.GoodsInId) = @TaxMatrixId
								  and IP.HubCurrencyNo = @HubSellCurrencyNo
								  AND gil.LinkMultiCurrencyNo = @LinkMultiCurrencyNo
								  AND ISNULL(gil.IsInvoiceCreated, 0) = 0
								  AND gil.DateInspected IS NOT NULL
								  and gil.CustomerRMALineNo is null
								  AND gi.DateReceived <= @EndDate

                        SET @HubTaxNo = 0
                        SET @ClientTaxNo = 0
                        SELECT @HubTaxNo = HubTaxNo,
                               @ClientTaxNo = ClientTaxNo
                        from tbTaxMatrix
                        WHERE TaxMatrixId = @TaxMatrixId


                        --INSERT DATA INTO CLIENT INVOICE HEADER  
                        --SET @TaxMatrixIdForCI= dbo.ufn_get_TaxMatrix_Id_For_ClientInvoice(@GoodsInId)   
                        SET @HubTaxNoForCI = 0
                        SET @ClientTaxNoForSI = 0
                        SELECT @HubTaxNoForCI = HubTaxNo,
                               @ClientTaxNoForSI = ClientTaxNo
                        FROM tbTaxMatrix
                        WHERE TaxMatrixId = @TaxMatrixIdForCI
                        EXEC usp_insert_ClientInvoice_Group @ClientNo,
                                                            @PurchaseHubClientNo,
                                                            @StartDate,
                                                            @EndDate,
                                                            @POHubCompanyNo,
                                                            @SupplierCode,
                                                            @SupplierName,
                                                            @CurrencyId,
                                                            @InvoiceAmount,
                                                            @CurrencyCode,
                                                            @WarehouseNo,
                                                            @TaxMatrixIdForCI,
                                                            @HubTaxNoForCI,
                                                            @HubSellCurrencyNo,
                                                            @LinkMultiCurrencyNo,
                                                            @ClientInvoiceNo OUTPUT

                        --INSERT DATA INTO SUPPLIER INVOICE HEADER              
                        EXEC usp_insert_SupplierInvoice_Group @ClientNo,
                                                              @PurchaseHubClientNo,
                                                              @StartDate,
                                                              @EndDate,
                                                              @POHubCompanyNo,
                                                              @SupplierCode,
                                                              @SupplierName,
                                                              @CurrencyId,
                                                              @InvoiceAmount,
                                                              @CurrencyCode,
                                                              @ClientInvoiceNo,
                                                              @TaxMatrixIdForCI,
                                                              @ClientTaxNoForSI,
                                                              @HubSellCurrencyNo,
                                                              @LinkMultiCurrencyNo,
                                                              @SupplierInvoiceNo OUTPUT

                        -- Insert data into line table              
                        IF (@ClientInvoiceNo > 0 AND @SupplierInvoiceNo > 0)
                        BEGIN

                            EXEC usp_insert_Client_And_Supplier_InvoiceLine @ClientNo,
                                                                            @PurchaseHubClientNo,
                                                                            @ClientInvoiceNo,
                                                                            @SupplierInvoiceNo,
                                                                            @StartDate,
                                                                            @EndDate,
                                                                            @WarehouseNo,
                                                                            @TaxMatrixId,
                                                                            @HubTaxNo,
                                                                            @HubSellCurrencyNo,
                                                                            @LinkMultiCurrencyNo,
                                                                            @SplitPONumber,
                                                                            @HubTaxNoForCI
							--added by cuongdx to maintain STO 

							SELECT @STOId = STOId 
							FROM tbStockTransferOrder sto
							join tbStock st on st.StockId = sto.ClientStockNo
							join tbGoodsInLine gil on st.GoodsInLineNo = gil.GoodsInLineId
							where gil.GoodsInLineId = @GoodsInLineId
						
							SELECT @ClientInvoiceLineId = ClientInvoiceLineId FROM tbClientInvoiceLine WHERE ClientInvoiceNo = @ClientInvoiceNo
							IF(@STOId IS NOT NULL)
							BEGIN
								UPDATE tbStockTransferOrder 
								SET ClientInvoiceLineNo = @ClientInvoiceLineId 
								WHERE STOId = @STOId
							END
						--added by cuongdo to maintain STO 
					   END
                    END
                    FETCH NEXT FROM cur_WareHouse_VAT
                    INTO @WarehouseNo,
                         @TaxMatrixId,
                         @HubSellCurrencyNo,
                         @LinkMultiCurrencyNo,
                         @SplitPONumber,
                         @TaxMatrixIdForCI
                END -- Inner cursor loop        
                CLOSE cur_WareHouse_VAT
                DEALLOCATE cur_WareHouse_VAT

            END
            ELSE
            BEGIN
                DECLARE @TaxMatrixNo int
                ---------------------------        
                DECLARE cur_SupplierInvoice CURSOR FOR -- Nested cursor        

                SELECT gi.GoodsInId,
                       IP.HubCurrencyNo,
                       gil.LinkMultiCurrencyNo,
                       dbo.ufn_splitP_Number(po.PurchaseOrderNumber) as SplitedPONumber,
					   gil.GoodsInLineId
                FROM tbGoodsInLine gil
                    JOIN tbGoodsIn gi
                        ON gi.GoodsInId = gil.GoodsInNo
                    JOIN tbInternalPurchaseOrderLine ipl
                        ON gil.PurchaseOrderLineNo = ipl.PurchaseOrderLineNo
                    LEFT JOIN tbInternalPurchaseOrder IP
                        ON ipl.InternalPurchaseOrderNo = IP.InternalPurchaseOrderId
                    --Espire: 11 Jan 21: Split the PO Number     
                    LEFT JOIN tbPurchaseOrder po
                        on IP.PurchaseOrderNo = po.PurchaseOrderId
                WHERE gi.ClientNo = @ClientNo
                      AND IP.ClientNo = @ClientNo
                      AND ISNULL(gil.IsInvoiceCreated, 0) = 0
                      AND gil.DateInspected IS NOT NULL
                      and gil.CustomerRMALineNo is null
                      AND gi.DateReceived <= @EndDate
                group by gi.GoodsInId,
                         IP.HubCurrencyNo,
                         gil.LinkMultiCurrencyNo,
                         dbo.ufn_splitP_Number(po.PurchaseOrderNumber),
						 gil.GoodsInLineId
                -- AND ((@StartDate IS NULL) OR (NOT @StartDate IS NULL  AND gi.DateReceived >= @StartDate))                        
                --AND ((@EndDate IS NULL)   OR (NOT @EndDate IS NULL  AND gi.DateReceived <= @EndDate))        

                OPEN cur_SupplierInvoice
                FETCH NEXT FROM cur_SupplierInvoice
                INTO @GoodsInId,
                     @HubSellCurrencyNo,
                     @LinkMultiCurrencyNo,
                     @SplitPONumber,
					 @GoodsInLineId
                WHILE (@@FETCH_STATUS = 0) -- Inner cursor loop (nested cursor while)        
                BEGIN
                    SET @HubTaxNo = 0
                    SET @ClientTaxNo = 0
                    select @TaxMatrixNo = dbo.ufn_get_TaxMatrix_Id(@GoodsInId)
                    SELECT @HubTaxNo = HubTaxNo,
                           @ClientTaxNo = ClientTaxNo
                    from tbTaxMatrix
                    WHERE TaxMatrixId = @TaxMatrixNo

                    SET @TaxMatrixIdForCI = dbo.ufn_get_TaxMatrix_Id_For_ClientInvoice(@GoodsInId)
                    SET @HubTaxNoForCI = 0
                    SELECT @HubTaxNoForCI = HubTaxNo
                    FROM tbTaxMatrix
                    WHERE TaxMatrixId = @TaxMatrixIdForCI
                    --INSERT DATA INTO CLIENT INVOICE HEADER              
                    EXEC usp_insert_ClientInvoice_For_GoodsIn @ClientNo,
                                                              @PurchaseHubClientNo,
                                                              @StartDate,
                                                              @EndDate,
                                                              @POHubCompanyNo,
                                                              @SupplierCode,
                                                              @SupplierName,
                                                              @GoodsInId,
                                                              @CurrencyCode,
                                                              @TaxMatrixIdForCI,
                                                              @HubTaxNoForCI,
                                                              @HubSellCurrencyNo,
                                                              @LinkMultiCurrencyNo,
                                                              @ClientInvoiceNo OUTPUT   

                    --INSERT DATA INTO SUPPLIER INVOICE HEADER              
                    EXEC usp_insert_SupplierInvoice_For_GoodsIn @ClientNo,
                                                                @PurchaseHubClientNo,
                                                                @StartDate,
                                                                @EndDate,
                                                                @POHubCompanyNo,
                                                                @SupplierCode,
                                                                @SupplierName,
                                                                @GoodsInId,
                                                                @CurrencyCode,
                                                                @ClientInvoiceNo,
                                                                @TaxMatrixNo,
                                                                @ClientTaxNo,
                                                                @HubSellCurrencyNo,
                                                                @LinkMultiCurrencyNo,
                                                                @SupplierInvoiceNo OUTPUT

                    -- Insert data into line table              
                    IF @SupplierInvoiceNo > 0
                       AND @ClientInvoiceNo > 0
                    BEGIN
                        EXEC usp_insert_Client_And_Supplier_InvoiceLine_For_GoodsIn @ClientNo,
                                                                                    @PurchaseHubClientNo,
                                                                                    @ClientInvoiceNo,
                                                                                    @SupplierInvoiceNo,
                                                                                    @GoodsInId,
                                                                                    @TaxMatrixNo,
                                                                                    @HubTaxNo,
                                                                                    @HubSellCurrencyNo,
                                                                                    @LinkMultiCurrencyNo,
                                                                                    @SplitPONumber
						--added by cuongdx to maintain STO 
						SELECT @STOId = STOId 
						FROM tbStockTransferOrder sto
						join tbStock st on st.StockId = sto.ClientStockNo
						join tbGoodsInLine gil on st.GoodsInLineNo = gil.GoodsInLineId
						where gil.GoodsInLineId = @GoodsInLineId
						 
						SELECT @ClientInvoiceLineId = ClientInvoiceLineId FROM tbClientInvoiceLine WHERE ClientInvoiceNo = @ClientInvoiceNo
						IF(@STOId IS NOT NULL)
						BEGIN
							UPDATE tbStockTransferOrder 
							SET ClientInvoiceLineNo = @ClientInvoiceLineId 
							WHERE STOId = @STOId
						END
						--added by cuongdo to maintain STO 
                    END
                    FETCH NEXT FROM cur_SupplierInvoice
                    INTO @GoodsInId,
                         @HubSellCurrencyNo,
                         @LinkMultiCurrencyNo,
                         @SplitPONumber,
						 @GoodsInLineId
                END -- Inner cursor loop        
                CLOSE cur_SupplierInvoice
                DEALLOCATE cur_SupplierInvoice

            ----------------------------------        
            END

            -- PRINT 'ID : '+ convert(varchar(20),@ClientNo)              
            FETCH NEXT FROM cur_Client
            INTO @ClientNo
        END
    END
    CLOSE cur_Client
    DEALLOCATE cur_Client
    SET NOCOUNT OFF

END