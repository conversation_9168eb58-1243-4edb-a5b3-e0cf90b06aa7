//-----------------------------------------------------------------------------------------
//Marker  Date          Changed By     Remarks
//[001]   03/07/2018    A<PERSON><PERSON>    Add customer order value nugget on broker and sales tab
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class PODeliveryStatus : Rebound.GlobalTrader.Site.Data.Base
    {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Gets the main data
		/// </summary>
		private void GetData() {

            try
            {
                int intClientId = SessionManager.ClientID ?? 0;
                List<PurchaseOrder> lstPuchaseOrder = PurchaseOrder.GetListPODeliveryDetail(intClientId);
                if (lstPuchaseOrder == null)
                {
                    WriteErrorDataNotFound();
                }
                else
                {
                    JsonObject jsn = new JsonObject();
                    JsonObject jsnItems = new JsonObject(true);
                    for (int i = 0; i < lstPuchaseOrder.Count; i++)
                    {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("CompanyName", lstPuchaseOrder[i].CompanyName);
                        jsnItem.AddVariable("CompanyId", lstPuchaseOrder[i].CompanyNo);
                        jsnItem.AddVariable("PurchaseOrderId", lstPuchaseOrder[i].PurchaseOrderId);
                        jsnItem.AddVariable("PurchaseOrderNumber", lstPuchaseOrder[i].PurchaseOrderNumber);
                        jsnItem.AddVariable("InternalPurchaseOrderId", lstPuchaseOrder[i].InternalPurchaseOrderId);
                        jsnItem.AddVariable("InternalPurchaseOrderNumber", lstPuchaseOrder[i].InternalPurchaseOrderNumber);
                        jsnItem.AddVariable("Part", lstPuchaseOrder[i].Part);
                        jsnItem.AddVariable("PromiseDate", Functions.FormatDate(lstPuchaseOrder[i].PromiseDate));
                        jsnItem.AddVariable("RowCSS", lstPuchaseOrder[i].RowCSS);
                        jsnItems.AddVariable(jsnItem);
                        jsnItem.Dispose();
                        jsnItem = null;
                    }
                    jsn.AddVariable("PODeliveryDetail", jsnItems);
                    jsn.AddVariable("IsPOHub", SessionManager.IsPOHub);
                    jsn.AddVariable("Count", lstPuchaseOrder.Count);
                    jsnItems.Dispose();
                    jsnItems = null;

                    OutputResult(jsn);
                    jsn.Dispose();
                    jsn = null;
                }
                lstPuchaseOrder = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }
        }


	}
}
