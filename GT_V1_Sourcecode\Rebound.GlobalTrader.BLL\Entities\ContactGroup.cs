﻿using System;
using System.Collections.Generic;
using System.Data;
using Rebound.GlobalTrader.DAL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class ContactGroup : BizObject
    {
        protected static ContactGroupElement Settings => Globals.Settings.ContactGroup;

        public int Id { get; set; }
        public string ContactName { get; set; }
        public string Code { get; set; }
        public string Type { get; set; }
        public string Inactive { get; set; }

        // Properties from table
        public int StockId { get; set; }
        public int CustomerRequirementId { get; set; }
        public int CustomerRequirementNumber { get; set; }
        public string FullPart { get; set; }
        public string Part { get; set; }
        public int? ManufacturerNo { get; set; }
        public string DateCode { get; set; }
        public int? PackageNo { get; set; }
        public int? WarehouseNo { get; set; }
        public int ClientNo { get; set; }
        public string QualityControlNotes { get; set; }
        public int? PurchaseOrderNo { get; set; }
        public int? PurchaseOrderLineNo { get; set; }
        public int QuantityInStock { get; set; }
        public int QuantityOnOrder { get; set; }
        public string Location { get; set; }
        public int? ProductNo { get; set; }
        public double? ResalePrice { get; set; }
        public bool Unavailable { get; set; }
        public int? LotNo { get; set; }
        public double? LandedCost { get; set; }
        public string SupplierPart { get; set; }
        public byte? ROHS { get; set; }
        public int? PackageUnit { get; set; }
        public int? StockKeepingUnit { get; set; }
        public int? CustomerRMANo { get; set; }
        public int? CustomerRMALineNo { get; set; }
        public int? GoodsInLineNo { get; set; }
        public int? UpdatedBy { get; set; }
        public DateTime DLUP { get; set; }
        public string FullSupplierPart { get; set; }
        public int? CountryOfManufacture { get; set; }
        public string PartMarkings { get; set; }
        public int? CountingMethodNo { get; set; }
        public string ManufacturerCode { get; set; }
        public int QuantityAllocated { get; set; }
        public string WarehouseName { get; set; }
        public string LotName { get; set; }
        public int? SupplierNo { get; set; }
        public string SupplierName { get; set; }
        public long? RowNum { get; set; }
        public int? QuantityAvailable { get; set; }
        public int? StatusNo { get; set; }
        public int? RowCnt { get; set; }
        public DateTime? PODeliveryDate { get; set; }
        public int? PurchaseOrderNumber { get; set; }
        public int? CustomerRMANumber { get; set; }
        public DateTime? CustomerRMADate { get; set; }
        public string PackageName { get; set; }
        public string PackageDescription { get; set; }
        public string ProductName { get; set; }
        public string ProductDescription { get; set; }
        public string ManufacturerName { get; set; }
        public string CurrencyCode { get; set; }
        public string ClientBaseCurrencyCode { get; set; }
        public string LotCode { get; set; }
        public int? Buyer { get; set; }
        public string BuyerName { get; set; }
        public int? GoodsInNo { get; set; }
        public double? GoodsInPrice { get; set; }
        public double? GoodsInShipInCost { get; set; }
        public int? GoodsInNumber { get; set; }
        public int? GoodsInCurrencyNo { get; set; }
        public DateTime StockDate { get; set; }
        public string ROHSStatus { get; set; }
        public string CountryOfManufactureName { get; set; }
        public double? PurchasePrice { get; set; }
        public string CountingMethodDescription { get; set; }
        public string StockLogDetail { get; set; }
        public string StockLogChangeNotes { get; set; }
        public int? StockLogReasonNo { get; set; }
        public bool? UpdateShipments { get; set; }
        public string RelationType { get; set; }
        public string ClientName { get; set; }
        public bool? ClientDataVisibleToOthers { get; set; }
        public bool? IsImageAvailable { get; set; }
        public bool? IsPDFAvailable { get; set; }
        public int StockUnallocatedCount { get; set; }
        public string SupplierType { get; set; }
        public string StockStartDate { get; set; }
        public double? OriginalLandedCost { get; set; }
        public DateTime? ManualStockSplitDate { get; set; }
        public DateTime? FirstStockProvisionDate { get; set; }
        public DateTime? LastStockProvisionDate { get; set; }
        public bool? IsManual { get; set; }
        public int LotStockProvisionID { get; set; }
        public string LotStockProvisionName { get; set; }
        public double? CurrentLandedCost { get; set; }
        public double? StockProvisionValue { get; set; }
        public int? StockProvisonPercentage { get; set; }
        public int LotStockProvisionLineID { get; set; }
        public string LotStockProvisionNo { get; set; }
        public int CurrencyId { get; set; }
        public short? POSerialNo { get; set; }
        public int? DivisionNo { get; set; }
        public string DivisionName { get; set; }
        public int? InternalPurchaseOrderNumber { get; set; }
        public int? InternalPurchaseOrderId { get; set; }
        public int? IPOSupplier { get; set; }
        public string IPOSupplierName { get; set; }
        public int? POClientNo { get; set; }
        public int? IsPoHub { get; set; }
        public double? ClientLandedCost { get; set; }
        public double? ClientPurchasePrice { get; set; }
        public int? IPONo { get; set; }
        public bool? IsClientUpdate { get; set; }
        public string ClientCode { get; set; }
        public int? SalesOrderNumber { get; set; }
        public string NPRNo { get; set; }
        public string CustomerPO { get; set; }
        public int? CustomerNo { get; set; }
        public string CustomerName { get; set; }
        public int? ClientBaseCurrencyID { get; set; }
        public string MSLLevel { get; set; }
        public int? CurrencyNo { get; set; }
        public bool? IsProdHazardous { get; set; }
        public bool? IsOrderViaIPOonly { get; set; }
        public string SupplierMessage { get; set; }
        public string GeneralInspectionNotes { get; set; }
        public string ActeoneTestStatus { get; set; }
        public string IsopropryleStatus { get; set; }
        public string ActeoneTest { get; set; }
        public string Isopropryle { get; set; }
        public string HICStatusName { get; set; }
        public string BakingLevelAdded { get; set; }
        public bool? ReqSerialNo { get; set; }
        public bool? IsLotCodesReq { get; set; }
        public int? BookedLotQuoteNo { get; set; }
        public int? QuoteNumber { get; set; }
        public bool IsBookedLotQuote { get; set; }
        public int? BookedLotSONo { get; set; }
        public int? SONumber { get; set; }
        public bool IsBookedLotSO { get; set; }

        public static List<ContactGroup> GetDataByNameOrCode(string nameSearch, string codeSearch, string type)
        {
            var dalList = SiteProvider.ContactGroup.GetDataByNameOrCode(nameSearch, codeSearch, type);
            if (dalList == null)
            {
                return new List<ContactGroup>();
            }

            var resultList = new List<ContactGroup>();
            foreach (var dalGroup in dalList)
            {
                var bllGroup = new BLL.ContactGroup
                {
                    Id = dalGroup.Id,
                    Code = dalGroup.Code,
                    ContactName = dalGroup.ContactName,
                    Inactive = dalGroup.Inactive
                };
                resultList.Add(bllGroup);
            }

            return resultList;
        }

        public static int SaveGroupData(string groupName, string groupCode, string groupType, int updatedBy)
        {
            try
            {
                return SiteProvider.ContactGroup.SaveGroupData(groupName, groupCode, groupType, updatedBy);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("An error occurred while saving group data.", ex);
            }
        }

        public static int EditGroupData(int groupId, string groupName, string groupCode, string groupType, int updatedBy)
        {
            try
            {
                return SiteProvider.ContactGroup.EditGroupData(groupId, groupName, groupCode, groupType, updatedBy);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("An error occurred while saving group data.", ex);
            }
        }

        public static List<ContactGroup> GetAllGroupCodeForCompany(string groupType, bool isForDropDown)
        {
            var dalList = SiteProvider.ContactGroup.GetAllGroupCodeForCompany(groupType, isForDropDown);
            if (dalList == null)
            {
                return new List<ContactGroup>();
            }

            var resultList = new List<ContactGroup>();
            foreach (var dalGroup in dalList)
            {
                var bllGroup = new BLL.ContactGroup
                {
                    Id = dalGroup.Id,
                    Code = dalGroup.Code,
                    ContactName = dalGroup.ContactName,
                    Inactive = dalGroup.Inactive
                };
                resultList.Add(bllGroup);
            }

            return resultList;
        }

        public static List<ContactGroup> AutoSearchGroupCode(string strSearch, string groupType, bool isForDropDown)
        {
            var dalList = SiteProvider.ContactGroup.AutoSearchGroupCode(strSearch, groupType, isForDropDown);
            if (dalList == null)
            {
                return new List<ContactGroup>();
            }

            var resultList = new List<ContactGroup>();
            foreach (var dalGroup in dalList)
            {
                var bllGroup = new BLL.ContactGroup
                {
                    Id = dalGroup.Id,
                    Code = dalGroup.Code,
                    ContactName = dalGroup.ContactName,
                    Inactive = dalGroup.Inactive
                };
                resultList.Add(bllGroup);
            }

            return resultList;
        }

        public static int DeleteCustomerGroupCodeByID(int groupId)
        {
            try
            {
                return SiteProvider.ContactGroup.DeleteCustomerGroupCodeByID(groupId);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("An error occurred while delete customer group code.", ex);
            }
        }
        public static int InactiveCustomerGroupCodeByID(int groupId, int inactive, int updatedBy)
        {
            try
            {
                return SiteProvider.ContactGroup.InactiveCustomerGroupCodeByID(groupId, inactive, updatedBy);
            }
            catch (Exception ex)
            {
                throw new ApplicationException("An error occurred while inactive/active customer group code.", ex);
            }
        }

        public static DataTable GetManufacturerGroupByMfr(System.Int32? MfrId)
        {
            return DAL.SiteProvider.ContactGroup.GetManufacturerGroupByMfr(MfrId);
        }

        public static DataTable GetSupplierTypeByMfrGroup(System.Int32? ContactGroupId, System.Int32? ClientNo)
        {
            return DAL.SiteProvider.ContactGroup.GetSupplierTypeByMfrGroup(ContactGroupId, ClientNo);
        }

        public static List<ContactGroup> GetManufacturerGroupBySupplier(int? supplierId, int? clientNo, System.Boolean? inactive)
        {
            var dalList = DAL.SiteProvider.ContactGroup.GetManufacturerGroupBySupplier(supplierId, clientNo, inactive);
            if (dalList == null)
            {
                return new List<ContactGroup>();
            }

            var resultList = new List<ContactGroup>();
            foreach (var dalGroup in dalList)
            {
                var bllGroup = new BLL.ContactGroup
                {
                    Id = dalGroup.Id,
                    Code = dalGroup.Code,
                    ContactName = dalGroup.ContactName
                };
                resultList.Add(bllGroup);
            }

            return resultList;
        }
    }
}