Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogReason=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogReason.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogReason.prototype={get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogReason.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogReason.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/StockLogReason");this._objData.set_DataObject("StockLogReason");this._objData.set_DataAction("GetData");this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogReason.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.StockLogReason",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);