Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.initializeBase(this,[n]);this._intCRMAID=-1;this._intLineID=-1;this._intLineCount=0;this._intLineDataCalls=0;this._blnLineIsFullyReceived=!1;this._blnLineIsPartReceived=!1;this._blnLineIsAllocated=!1;this._blnCRMAClosed=!1;this._intLineQuantityReceived=0;this._intLineQuantityExists=0;this._intLineQuantityCRMA=0;this._intLineQuantityAvailable=0;this._intInvoiceLineNo=0;this._blnLineLoaded=!1;this._intLineQuantityReceived=0;this._Reason1="";this._Reason2="";this._Reason1Val="";this._Reason2Val="";this._isClosed=!1;this._isEditEnable=!1;this._isAvoidable=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_intLineID:function(){return this._intLineID},set_intLineID:function(n){this._intLineID!==n&&(this._intLineID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnClose:function(){return this._ibtnClose},set_ibtnClose:function(n){this._ibtnClose!==n&&(this._ibtnClose=n)},get_ctlTabStrip:function(){return this._ctlTabStrip},set_ctlTabStrip:function(n){this._ctlTabStrip!==n&&(this._ctlTabStrip=n)},get_tblOpen:function(){return this._tblOpen},set_tblOpen:function(n){this._tblOpen!==n&&(this._tblOpen=n)},get_tblClosed:function(){return this._tblClosed},set_tblClosed:function(n){this._tblClosed!==n&&(this._tblClosed=n)},get_tblAll:function(){return this._tblAll},set_tblAll:function(n){this._tblAll!==n&&(this._tblAll=n)},get_hypPrev:function(){return this._hypPrev},set_hypPrev:function(n){this._hypPrev!==n&&(this._hypPrev=n)},get_hypNext:function(){return this._hypNext},set_hypNext:function(n){this._hypNext!==n&&(this._hypNext=n)},get_lblLineNumber:function(){return this._lblLineNumber},set_lblLineNumber:function(n){this._lblLineNumber!==n&&(this._lblLineNumber=n)},get_pnlLineDetail:function(){return this._pnlLineDetail},set_pnlLineDetail:function(n){this._pnlLineDetail!==n&&(this._pnlLineDetail=n)},get_pnlLoadingLineDetail:function(){return this._pnlLoadingLineDetail},set_pnlLoadingLineDetail:function(n){this._pnlLoadingLineDetail!==n&&(this._pnlLoadingLineDetail=n)},get_pnlLineDetailError:function(){return this._pnlLineDetailError},set_pnlLineDetailError:function(n){this._pnlLineDetailError!==n&&(this._pnlLineDetailError=n)},get_fldAllocations:function(){return this._fldAllocations},set_fldAllocations:function(n){this._fldAllocations!==n&&(this._fldAllocations=n)},get_tblAllocations:function(){return this._tblAllocations},set_tblAllocations:function(n){this._tblAllocations!==n&&(this._tblAllocations=n)},get_fldReceived:function(){return this._fldReceived},set_fldReceived:function(n){this._fldReceived!==n&&(this._fldReceived=n)},get_tblReceived:function(){return this._tblReceived},set_tblReceived:function(n){this._tblReceived!==n&&(this._tblReceived=n)},get_blnCRMAClosed:function(){return this._blnCRMAClosed},set_blnCRMAClosed:function(n){this._blnCRMAClosed!==n&&(this._blnCRMAClosed=n)},get_ibtnDeallocate:function(){return this._ibtnDeallocate},set_ibtnDeallocate:function(n){this._ibtnDeallocate!==n&&(this._ibtnDeallocate=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.callBaseMethod(this,"initialize");this._strDataPath="controls/Nuggets/CRMALines";this._strDataObject="CRMALines";this._ctlTabStrip.addTabIndexChanged(Function.createDelegate(this,this.tabChanged));this.addRefreshEvent(Function.createDelegate(this,this.tabChanged));this._tblOpen.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._tblClosed.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this._tblAll.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this.addRefreshEvent(Function.createDelegate(this,this.getData));this._fldAllocations.addShown(Function.createDelegate(this,this.onShownAllocations));this._fldAllocations.addRefresh(Function.createDelegate(this,this.onRefreshAllocations));this._tblAllocations.addMultipleSelectionChanged(Function.createDelegate(this,this.tblAllocationsSelectionChanged));this._fldReceived.addShown(Function.createDelegate(this,this.onShownReceived));this._fldReceived.addRefresh(Function.createDelegate(this,this.onRefreshReceived));this._tblAll.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));$addHandler(this._hypPrev,"click",Function.createDelegate(this,this.prevLine));$addHandler(this._hypNext,"click",Function.createDelegate(this,this.nextLine));this._ibtnAdd&&($R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAdd=$find(this._aryFormIDs[0]),this._frmAdd.addCancel(Function.createDelegate(this,this.hideAddForm)),this._frmAdd.addSaveComplete(Function.createDelegate(this,this.saveAddComplete)));this._ibtnEdit&&($R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmEdit=$find(this._aryFormIDs[1]),this._frmEdit.addCancel(Function.createDelegate(this,this.hideEditForm)),this._frmEdit.addSaveComplete(Function.createDelegate(this,this.saveEditComplete)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[2]),this._frmDelete.addCancel(Function.createDelegate(this,this.hideDeleteForm)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.deleteComplete)),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.hideDeleteForm)));this._ibtnDeallocate&&($R_IBTN.addClick(this._ibtnDeallocate,Function.createDelegate(this,this.showDeallocateForm)),this._frmDeallocate=$find(this._aryFormIDs[3]),this._frmDeallocate.addCancel(Function.createDelegate(this,this.hideDeallocateForm)),this._frmDeallocate.addSaveComplete(Function.createDelegate(this,this.deallocateComplete)),this._frmDeallocate.addNotConfirmed(Function.createDelegate(this,this.hideDeallocateForm)));this._ibtnClose&&($R_IBTN.addClick(this._ibtnClose,Function.createDelegate(this,this.showCloseForm)),this._frmClose=$find(this._aryFormIDs[4]),this._frmClose.addCancel(Function.createDelegate(this,this.hideCloseForm)),this._frmClose.addSaveComplete(Function.createDelegate(this,this.closeComplete)),this._frmClose.addNotConfirmed(Function.createDelegate(this,this.hideCloseForm)));this.tabChanged();this._frmEdit&&(this._ctlItemsReason1=$find(this._frmEdit.getField("ctlItemsReason1").ID),this._ctlItemsReason1.addItem(),this._ctlItemsReason2=$find(this._frmEdit.getField("ctlItemsReason2").ID),this._ctlItemsReason2.addItem())},tabChanged:function(){this.clearMessages();this.getTabData()},getTabData:function(){this.enableEditButtons(!1);$R_FN.showElement(this._pnlLineDetail,!1);switch(this._ctlTabStrip._selectedTabIndex){case 0:this.getTabData_All();break;case 1:this.getTabData_Open();break;case 2:this.getTabData_Closed()}},getTabData_Open:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines_Open");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getTabDataOK_Open));n.addError(Function.createDelegate(this,this.getTabDataError));n.addTimeout(Function.createDelegate(this,this.getTabDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getTabDataOK_Open:function(n){var t=n._result;this.processLines(n._result);this.getDataOK_End()},getTabData_Closed:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines_Closed");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getTabDataOK_Closed));n.addError(Function.createDelegate(this,this.getTabDataError));n.addTimeout(Function.createDelegate(this,this.getTabDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getTabDataOK_Closed:function(n){var t=n._result;this.processLines(n._result);this.getDataOK_End()},getTabData_All:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getTabDataOK_All));n.addError(Function.createDelegate(this,this.getTabDataError));n.addTimeout(Function.createDelegate(this,this.getTabDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getTabDataOK_All:function(n){var t=n._result;this.processLines(n._result);this.getDataOK_End()},getTabDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getCurrentTable:function(){var n;switch(this._ctlTabStrip._selectedTabIndex){case 0:n=this._tblAll;break;case 1:n=this._tblOpen;break;case 2:n=this._tblClosed}return n},dispose:function(){this.isDisposed||(this._hypPrev&&$clearHandlers(this._hypPrev),this._hypNext&&$clearHandlers(this._hypNext),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._ibtnDeallocate&&$R_IBTN.clearHandlers(this._ibtnDeallocate),this._tblAll&&this._tblAll.dispose(),this._tblAllocations&&this._tblAllocations.dispose(),this._tblReceived&&this._tblReceived.dispose(),this._fldReceived&&this._fldReceived.dispose(),this._frmAdd&&this._frmAdd.dispose(),this._frmEdit&&this._frmEdit.dispose(),this._frmDelete&&this._frmDelete.dispose(),this._frmDeallocate&&this._frmDeallocate.dispose(),this._ctlTabStrip&&this._ctlTabStrip.dispose(),this._ibtnClose&&$R_IBTN.clearHandlers(this._ibtnClose),this._tblOpen&&this._tblOpen.dispose(),this._tblClosed&&this._tblClosed.dispose(),this._tblAll&&this._tblAll.dispose(),this._frmAdd=null,this._frmEdit=null,this._frmDelete=null,this._frmDeallocate=null,this.intCRMAID=null,this._intLineID=null,this._intContactID=null,this._ibtnAdd=null,this._ibtnEdit=null,this._ibtnDelete=null,this._tblAll=null,this._hypPrev=null,this._hypNext=null,this._lblLineNumber=null,this._pnlLineDetail=null,this._pnlLoadingLineDetail=null,this._pnlLineDetailError=null,this._fldAllocations=null,this._tblAllocations=null,this._fldReceived=null,this._tblReceived=null,this._ibtnDeallocate=null,this._isAvoidable=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.callBaseMethod(this,"dispose"))},getData:function(){this.enableEditButtons(!1);$R_FN.showElement(this._pnlLineDetail,!1);this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},enableEditButtons:function(n){var i,t,r;if(n){if(i=this.getCurrentTable(),!i)return;if(t=i.getSelectedExtraData(),!t)return;r=!1;r=!t.IsReceived;this._isClosed=t.IsClosed;this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,this._blnLineLoaded&&!this._isEditEnable);this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!this._blnLineIsFullyReceived&&!this._blnLineIsPartReceived&&!this._blnLineIsAllocated&&this._blnLineLoaded&&!t.IsClosed&&!this._isEditEnable);this._ibtnClose&&$R_IBTN.enableButton(this._ibtnClose,!t.IsClosed&&this._blnLineLoaded)}else this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!1),this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!1),this._ibtnClose&&$R_IBTN.enableButton(this._ibtnClose,!1)},tbl_SelectedIndexChanged:function(){var n=this.getCurrentTable();this._blnLineIsFullyReceived=n.getSelectedExtraData().IsFullyReceived;this._blnLineIsPartReceived=n.getSelectedExtraData().IsPartReceived;this._blnLineIsAllocated=n.getSelectedExtraData().IsAllocated;this._intLineQuantityReceived=n.getSelectedExtraData().QuantityReceived;this.enableEditButtons(!0);this._intLineID=n._varSelectedValue;this.getLineData();this._intLineQuantityExists=n.getSelectedExtraData().QuantityExists;this._intLineQuantityAvailable=n.getSelectedExtraData().QuantityShipped;this._intInvoiceLineNo=n.getSelectedExtraData().InvoiceLineNo;this._isEditEnable=n.getSelectedExtraData().IsParentCustomerRMALineNo},processLines:function(n){var u,f,t,r;if(this.showLoading(!1),u=this.getCurrentTable(),this._currentAS6081,u.clearTable(),n.Lines)for(f=0;f<n.Lines.length;f++){t=n.Lines[f];this._currentAS6081=t.AS6081==!0?"Yes":"No";var h=[$R_FN.writeDoubleCellValue($R_FN.writePartNo(t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue(t.Quantity,t.Received),$R_FN.writeDoubleCellValue($RGT_nubButton_Invoice(t.InvoiceNo,t.Invoice),t.InvoiceDate),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.ReturnDate),$R_FN.setCleanTextValue(t.Reason1),t.RootCause),$R_FN.writeDoubleCellValue(this._currentAS6081)],c=Number.parseLocale(t.Quantity),l=Number.parseLocale(t.QuantityShipped),a=t.InvoiceLineNo,i="unposted",v=Number.parseLocale(t.Allocated),e=Number.parseLocale(t.Received),o=!1,s=!1,y=v>0;e>0&&(e>=parseInt(t.Quantity,0)?o=!0:s=!0);r={IsAllocated:y,IsFullyReceived:o,IsPartReceived:s,QuantityReceived:e,QuantityExists:c,QuantityShipped:l,IsClosed:t.Closed,InvoiceLineNo:a,IsParentCustomerRMALineNo:t.IsParentCustomerRMALineNo};r.IsClosed&&(i="shipped");t.IsAllocated&&(i="allocated");r.IsPartReceived&&(i="partReceived");r.IsFullyReceived&&(i="received");u.addRow(h,t.ID,t.ID==this._intLineID,r,i);t=null}this._intLineCount=this._tblAll.countRows();this.showContent(!0);this.showContentLoading(!1);u.resizeColumns()},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},prevLine:function(){var n=this._tblAll._intSelectedIndex-1;n<0||this._tblAll.selectRow(n,!0)},nextLine:function(){var n=this._tblAll._intSelectedIndex+1;n>=this._intLineCount||this._tblAll.selectRow(n,!0)},getLineData:function(){this._intLineDataCalls+=1;this._blnLineLoaded=!1;this.enableEditButtons(!1);this.showLoading(!0);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);$R_FN.showElement(this._pnlLineDetail,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetData");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getLineDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();this.getLineAllocations();this.getLineReceived();n=null},getLineDataError:function(n){this.lineDataCallComplete();$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlLineDetail,!1);$R_FN.showElement(this._pnlLineDetailError,!0);$R_FN.setInnerHTML(this._pnlLineDetailError,n.get_ErrorMessage())},getLineDataOK:function(n){var t,r,i,u,f;if(this.lineDataCallComplete(),$R_FN.showElement(this._pnlLineDetailError,!1),t=n._result,r="",t.CreditIds&&t.CreditNumbers)for(i=0;i<t.CreditNumbers.length;i++)u=t.CreditIds[i],f=t.CreditNumbers[i],r+=$RGT_nubButton_CreditNote(u.CreditId,f.CreditNumber);this.setFieldValue("ctlQuantity",t.Quantity);this.setFieldValue("ctlQuantityReceived",t.Received);this.setFieldValue("ctlPartNo",$RGT_nubButton_Stock(t.StockNo,t.Part,t.ROHS));this.setFieldValue("hidPartNo",t.Part);this.setFieldValue("ctlReason",$R_FN.setCleanTextValue(t.Reason1));this.setFieldValue("ctlReason2",$R_FN.setCleanTextValue(t.Reason2));this._ctlItemsReason1&&this._ctlItemsReason1.setSubCategory($R_FN.setCleanTextValue(t.Reason1),$R_FN.setCleanTextValue(t.Reason1Val));this._ctlItemsReason2&&this._ctlItemsReason2.setSubCategory($R_FN.setCleanTextValue(t.Reason2),$R_FN.setCleanTextValue(t.Reason2Val));this._Reason1=$R_FN.setCleanTextValue(t.Reason1);this._Reason2=$R_FN.setCleanTextValue(t.Reason2);this._Reason1Val=$R_FN.setCleanTextValue(t.Reason1Val);this._Reason2Val=$R_FN.setCleanTextValue(t.Reason2Val);this.setFieldValue("ctlManufacturer",$RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer,t.MfrAdvisoryNotes));this.setFieldValue("hidManufacturer",t.Manufacturer);this.setFieldValue("hidManufacturerNo",t.ManufacturerNo);this.setFieldValue("ctlCustomerPart",$R_FN.setCleanTextValue(t.CustomerPart));this.setFieldValue("ctlPackage",$R_FN.setCleanTextValue(t.Package));this.setFieldValue("ctlDateCode",$R_FN.setCleanTextValue(t.DC));this.setFieldValue("ctlReturnDate",t.ReturnDate);this.setFieldValue("hidProductNo",t.ProductNo);this.setFieldValue("hidPackageNo",t.PackageNo);this.setFieldValue("ctlROHS",$R_FN.writeROHS(t.ROHS));this.setFieldValue("hidROHS",t.ROHS);this.setFieldValue("ctlLineNotes",$R_FN.setCleanTextValue(t.LineNotes));this.setFieldValue("ctlCreditNoteNos",r);this.setFieldValue("ctlRootCause",t.RootCause);this.setFieldValue("ctlIsAvoidable",t.Avoidable);this._isAvoidable=t.Avoidable;this.showField("ctlCreditNoteNos",r.length>0);this.setFieldValue("ctlProduct",$R_FN.setCleanTextValue(t.Product));this.setFieldValue("ctlProductDis",$R_FN.showHazardousNew(t.Product,t.IsProdHaz,$R_FN.setCleanTextValue(t.ProductMessage)));this.setFieldValue("hidProductHazar",t.IsProdHaz);this.setFieldValue("hidPrintHaza",t.IsPrintHaz);this.setFieldValue("ctlAS6081",t.AS6081==!0?"Yes":"No");$R_FN.highlightBackgroundColorOfText("ctl00_cphMain_ctlLines_ctlDB_ctl13_ctlAS6081_lbl",t.AS6081);t.AS6081==!0?this.getAS6081BannerMessage():this.clearMessages();$R_FN.showElement(this._pnlLineDetail,!0);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.setInnerHTML(this._lblLineNumber,String.format($R_RES.LineXOfY,this._tblAll._intSelectedIndex+1,this._intLineCount));this._blnLineLoaded=!0;this.enableEditButtons(!0)},getLineAllocations:function(){this._intLineDataCalls+=1;this.showLoading(!0);this._fldAllocations.showLoading(!0);this.enableAllocationsButtons(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLineAllocations");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getLineAllocationsOK));n.addError(Function.createDelegate(this,this.getLineAllocationsError));n.addTimeout(Function.createDelegate(this,this.getLineAllocationsError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineAllocationsError:function(n){this.lineDataCallComplete();this._fldAllocations.showError(!0,n.get_ErrorMessage())},getLineAllocationsOK:function(n){var i,r,t,u;if(this.lineDataCallComplete(),i=n._result,this._fldAllocations.showContent(!0),this._fldAllocations.resetCount(),this._tblAllocations.clearTable(),this._fldAllocations.updateCount(i.Count),i.Lines)for(r=0;r<i.Lines.length;r++)t=i.Lines[r],u=[$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(t.StockNo,t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),$R_FN.writeDoubleCellValue($RGT_nubButton_Company(t.CustomerNo,t.Customer,null,null,null,t.CustomerAdvisoryNotes),$R_FN.setCleanTextValue(t.CustomerPO)),$R_FN.writeDoubleCellValue($RGT_nubButton_POSO(t.SalesOrderNo,t.SalesOrderNumber,t.SalesOrderLineNo),t.DatePromised),$R_FN.writeDoubleCellValue($RGT_nubButton_SRMA(t.SRMANo,t.SRMANumber),t.ReturnDate),$R_FN.writeDoubleCellValue(t.QuantityAllocated,t.Price)],this._tblAllocations.addRow(u,t.ID,!1),t=null,u=null;this._tblAllocations.resizeColumns()},getLineReceived:function(){this._intLineDataCalls+=1;this.showLoading(!0);this._fldReceived.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLineReceived");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getLineReceivedOK));n.addError(Function.createDelegate(this,this.getLineReceivedError));n.addTimeout(Function.createDelegate(this,this.getLineReceivedError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineReceivedError:function(n){this.lineDataCallComplete();this._fldReceived.showError(!0,n.get_ErrorMessage())},onPotentialStatusChange:function(){var n=this.get_events().getHandler("PotentialStatusChange");n&&n(this,Sys.EventArgs.Empty)},getLineReceivedOK:function(n){var i,r,t,u;if(this.lineDataCallComplete(),i=n._result,this._fldReceived.showContent(!0),this._fldReceived.resetCount(),this._tblReceived.clearTable(),this._fldReceived.updateCount(i.Count),i.Items)for(r=0;r<i.Items.length;r++)t=i.Items[r],u=[$RGT_nubButton_GoodsIn(t.GoodsInNo,t.GoodsInNumber),$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(t.StockNo,t.Part,t.ROHS),$R_FN.setCleanTextValue(t.SupplierPart)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.MfrNo,t.Mfr,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue(t.Qty,t.LandedCost),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Location),$R_FN.setCleanTextValue(t.DateReceived))],this._tblReceived.addRow(u,t.ID,!1),t=null,u=null;this._tblReceived.resizeColumns()},lineDataCallComplete:function(){this._intLineDataCalls-=1;this._intLineDataCalls<1&&this.showLoading(!1)},onShownAllocations:function(){this._tblAllocations.resizeColumns()},onRefreshAllocations:function(){this.getLineAllocations()},tblAllocationsSelectionChanged:function(){this.enableAllocationsButtons(!0)},enableAllocationsButtons:function(n){n?this._ibtnDeallocate&&$R_IBTN.enableButton(this._ibtnDeallocate,this._tblAllocations._aryCurrentValues.length>0):this._ibtnDeallocate&&$R_IBTN.enableButton(this._ibtnDeallocate,!1)},onShownReceived:function(){this._tblReceived.resizeColumns()},onRefreshReceived:function(){this.getLineReceived()},showAddForm:function(){this._frmAdd._intCRMAID=this._intCRMAID;this.showForm(this._frmAdd,!0)},hideAddForm:function(){this.showForm(this._frmAdd,!1);this._tblAll.resizeColumns()},saveAddComplete:function(){this.hideAddForm();this._intLineID=this._frmAdd._intNewID;this.getTabData();this.onPotentialStatusChange();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully)},showEditForm:function(){this._frmEdit._intCRMAID=this._intCRMAID;this._frmEdit._intLineID=this._intLineID;this._frmEdit._isClosed=this._isClosed;this._frmEdit._intInvoiceLineNo=this._intInvoiceLineNo;this._frmEdit._intLineQuantityExists=this._intLineQuantityExists;this._frmEdit.setFieldValue("ctlPartNo",this.getFieldValue("hidPartNo"));this._frmEdit.setFieldValue("ctlQuantity",this.getFieldValue("ctlQuantity"));this._frmEdit.setFieldValue("ctlQuantity_Lable",this.getFieldValue("ctlQuantity"));this._frmEdit.setFieldValue("ctlReason",this.getFieldValue("ctlReason"));this._ctlItemsReason1&&this._ctlItemsReason1.setSubCategory(this._Reason1,this._Reason1Val);this._ctlItemsReason2&&this._ctlItemsReason2.setSubCategory(this._Reason2,this._Reason2Val);this._frmEdit.setFieldValue("ctlLineNotes",this.getFieldValue("ctlLineNotes"));this._frmEdit.setFieldValue("ctlRootCause",this.getFieldValue("ctlRootCause"));this._frmEdit.setFieldValue("ctlIsAvoidable",this._isAvoidable);this._frmEdit._blnProductHaza=Boolean.parse(this.getFieldValue("hidProductHazar"));this.showForm(this._frmEdit,!0)},hideEditForm:function(){this.showForm(this._frmEdit,!1);this._tblAll.resizeColumns()},saveEditComplete:function(){this.hideEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getTabData();this.onPotentialStatusChange()},showDeleteForm:function(){this._frmDelete._intLineID=this._intLineID;this._frmDelete.setFieldValue("ctlPartNo",this.getFieldValue("hidPartNo"));this._frmDelete.setFieldValue("ctlQuantity",this.getFieldValue("ctlQuantity"));this._frmDelete.setFieldValue("ctlReason",this.getFieldValue("ctlReason"));this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1);this._tblAll.resizeColumns()},deleteComplete:function(){this.hideDeleteForm();this.getTabData();this.onPotentialStatusChange()},showCloseForm:function(){this._frmClose._intCRMAID=this._intCRMAID;this._frmClose._intLineID=this._intLineID;this._frmClose.setFieldValue("ctlPartNo",this.getFieldValue("hidPartNo"));this._frmClose.setFieldValue("ctlQuantity",this.getFieldValue("ctlQuantity"));this._frmClose.setFieldValue("ctlReason",this.getFieldValue("ctlReason"));this.showForm(this._frmClose,!0)},hideCloseForm:function(){this.showForm(this._frmClose,!1);this.getCurrentTable().resizeColumns()},closeComplete:function(){this.hideCloseForm();this.getTabData();this.onPotentialStatusChange()},showDeallocateForm:function(){this._frmDeallocate._intCRMAID=this._intCRMAID;this._frmDeallocate._aryLineIDs=this._tblAllocations._aryCurrentValues;this.showForm(this._frmDeallocate,!0)},hideDeallocateForm:function(){this.showForm(this._frmDeallocate,!1);this._tblAll.resizeColumns()},deallocateComplete:function(){this.hideDeallocateForm();this.getTabData();this.onPotentialStatusChange()},getAS6081BannerMessage:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/SetupNuggets/AS6081");n.set_DataObject("AS6081");n.set_DataAction("GetAlertMessageByOperationType");n.addParameter("operationType","ReceiveCRMABanner");n.addDataOK(Function.createDelegate(this,this.getAS6081BannerMessageOK));n.addError(Function.createDelegate(this,this.getAS6081BannerMessageError));n.addTimeout(Function.createDelegate(this,this.getAS6081BannerMessageError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getAS6081BannerMessageOK:function(n){var t=n._result;this.clearMessages();this.addMessage(t.Message,$R_ENUM$MessageTypeList.Warning)},getAS6081BannerMessageError:function(){console.error(`error occured while trying to fetch 'banner for AS6081'`)}};Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);