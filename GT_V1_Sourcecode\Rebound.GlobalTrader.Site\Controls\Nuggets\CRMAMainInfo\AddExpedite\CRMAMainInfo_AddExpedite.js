Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.initializeBase(this,[n]);this._intCustomerRMAID=-1};Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.prototype={get_intCustomerRMAID:function(){return this._intCustomerRMAID},set_intCustomerRMAID:function(n){this._intCustomerRMAID!==n&&(this._intCustomerRMAID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked))},dispose:function(){this.isDisposed||(this._intCustomerRMAID=null,Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CRMAMainInfo");n.set_DataObject("CRMAMainInfo");n.set_DataAction("SaveCRMAInternalLog");n.addParameter("intCustomerRMAId",this._intCustomerRMAID);n.addParameter("AddNotes",this.getFieldValue("ctlExpediteNotes"));n.addDataOK(Function.createDelegate(this,this.saveAddComplete));n.addError(Function.createDelegate(this,this.saveAddError));n.addTimeout(Function.createDelegate(this,this.saveAddError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveAddError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveAddComplete:function(n){n._result.Result>0?this.onSaveComplete():(n._result.Message&&(this._strErrorMessage=n._result.Message),this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()}};Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_AddExpedite",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);