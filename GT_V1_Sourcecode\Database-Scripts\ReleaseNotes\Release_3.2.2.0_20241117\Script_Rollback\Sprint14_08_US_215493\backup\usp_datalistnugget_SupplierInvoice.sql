﻿CREATE OR ALTER PROCEDURE  [dbo].[usp_datalistnugget_SupplierInvoice]            
--********************************************************************************************                                    
   --* Created By :- <PERSON><PERSON>                                  
   --* Created Date: - 11/06/2013                                  
   --* Description :- Search supplier invoice                                  
--********************************************************************************************                                    
    @ClientId int                                    
  , @OrderBy int = 1                                    
  , @SortDir int = 1                                    
  , @PageIndex int = 0                                    
  , @PageSize int = 50                              
  , @SupplierInvoiceNumber nvarchar(100) = NULL                                          
  , @URNNoLo int = NULL                                    
  , @URNNoHi int = NULL                                    
  , @PurchaseOrderNoLo int = NULL                                    
  , @PurchaseOrderNoHi int = NULL                                    
  , @GoodsInNoLo int = NULL                                    
  , @GoodsInNoHi int = NULL                                   
  , @SupplierInvoiceDateFrom datetime = NULL                                    
  , @SupplierInvoiceDateTo datetime = NULL                                     
  , @CMSearch nvarchar(50) = NULL                                    
  , @RecentOnly bit = 0                                    
  , @ExportedOnly bit = NULL                            
  , @ApproveAndUnExported bit = NULL                            
  , @CannotBeExported bit = NULL           
  , @InternalPurchaseOrderNoLo int = NULL              
  , @InternalPurchaseOrderNoHi int = NULL             
  , @StatusReason int = NULL                 
  ,@OCR bit = 0     
    WITH RECOMPILE                                    
AS                                     
    DECLARE @RecentDate datetime                                    
      , @StartPage int                                    
      , @EndPage int      
      
    SET @RecentDate = dbo.ufn_get_date_from_datetime(dateadd(mm, -3, getdate()))                                    
    SET @StartPage = (@PageIndex * @PageSize + 1)                                    
    SET @EndPage = ((@PageIndex + 1) * @PageSize)                                    
-- semi-colon needed for WITH                
    IF (NOT @SupplierInvoiceDateFrom IS NULL)                                 
        SET @SupplierInvoiceDateFrom = dbo.ufn_get_start_of_day_for_date(@SupplierInvoiceDateFrom)       
      
       
    
    IF (NOT @SupplierInvoiceDateTo IS NULL)                                 
        SET @SupplierInvoiceDateTo = dbo.ufn_get_end_of_day_for_date(@SupplierInvoiceDateTo)                                      
;                                    
  
    WITH    cteSearch                                    
              AS (SELECT    si.SupplierInvoiceId                                  
                          , si.SupplierInvoiceNumber                                  
                          , si.SupplierName                                  
                          , gi.GoodsInNumber                                  
                          , isnull(ipo.InternalPurchaseOrderNumber, po.PurchaseOrderNumber) as  PurchaseOrderNumber                                 
                          , isnull(ipo.InternalPurchaseOrderId ,isnull(gi.PurchaseOrderNo, po.PurchaseOrderId)) AS PurchaseOrderNo                                  
                          , si.SupplierInvoiceDate                                  
                          , sil.Part                                  
                          , si.InvoiceAmount                                 
                          , si.CompanyNo                                 
                          , isnull(sil.GoodsInNo,gi.GoodsInId) as GoodsInId                              
                          , si.CurrencyNo                              
       , gcu.GlobalCurrencyName as CurrencyCode                 
                          , si.URNNumber             
        ,si.UpdatedBy    
        , ipo.InternalPurchaseOrderId        
        ,si.CanbeExported      
        ,(select SUM(osi.Quantity) from tbOcr_SupplierInvoice osi where osi.SupplierInvoiceNumber = si.SupplierInvoiceNumber) as ItemCount      
        ,(select top 1 ocst.MatchPercentage from tbOcrStatus ocst where ocst.SupplierInvoiceId = si.SupplierInvoiceId) as MatchPercentage       
                          , ROW_NUMBER() OVER (ORDER BY --             
          case WHEN @OrderBy = 2                                    
           AND @SortDir = 2 THEN si.SupplierInvoiceNumber                                    
                                         END DESC                                     
                                     , case WHEN @OrderBy = 2 THEN si.SupplierInvoiceNumber                                    
                                       END                                    
                                     , case WHEN @OrderBy = 3                                    
                                                 AND @SortDir = 2 THEN si.SupplierName                                    
                                       END DESC                          
                                     , case WHEN @OrderBy = 3 THEN si.SupplierName                                  
                                      END                                    
                                     , case WHEN @OrderBy = 4                                    
                                            AND @SortDir = 2 THEN gi.GoodsInNumber                                   
                                        END DESC                                    
                                     , case WHEN @OrderBy = 4 THEN gi.GoodsInNumber                                  
                                       END                                    
                                     , case WHEN @OrderBy = 5                        
                                        AND @SortDir = 2 THEN si.URNNumber                                 
                                       END DESC                                    
                                   , case WHEN @OrderBy = 5 THEN si.URNNumber                                 
                                       END                                    
                                     , case WHEN @OrderBy = 6                                    
                                                AND @SortDir = 2 THEN si.SupplierInvoiceDate                                   
                                       END DESC                                    
                                     , case WHEN @OrderBy = 6 THEN si.SupplierInvoiceDate                                  
                                       END                                   
                                     , case WHEN @OrderBy = 7                                    
                                               AND @SortDir = 2 THEN sil.Part                                
                                       END DESC                                    
                                     , case WHEN @OrderBy = 7 THEN sil.Part                                
                                       END                                     
                                     , case WHEN @OrderBy = 8                                    
                                                 AND @SortDir = 2 THEN si.InvoiceAmount                                  
                                       END DESC                                    
                                     , case WHEN @OrderBy = 8 THEN si.InvoiceAmount                                  
                            END      
          , case WHEN @OrderBy = 9                                    
             AND @SortDir = 2 THEN (select SUM(osi.Quantity) from tbOcr_SupplierInvoice osi       
                  where osi.SupplierInvoiceNumber = si.SupplierInvoiceNumber)                                  
                                END DESC                                    
                                     , case WHEN @OrderBy = 9 THEN (select top 1 ocst.MatchPercentage from tbOcrStatus ocst       
                 where ocst.SupplierInvoiceId = si.SupplierInvoiceId)      
            END      
          , case WHEN @OrderBy = 10                                    
             AND @SortDir = 2 THEN (select top 1 ocst.MatchPercentage from tbOcrStatus ocst       
                  where ocst.SupplierInvoiceId = si.SupplierInvoiceId)                                  
                                       END DESC                                    
                                     , case WHEN @OrderBy = 10 THEN (select top 1 ocst.MatchPercentage from tbOcrStatus ocst       
                  where ocst.SupplierInvoiceId = si.SupplierInvoiceId)        
                                       END      
            ) AS RowNum      
                  FROM      tbSupplierInvoice si                                  
                  LEFT JOIN tbSupplierInvoiceLine sil on  si.SupplierInvoiceId=sil.SupplierInvoiceNo                                  
                  LEFT JOIN  tbGoodsIn gi ON sil.GoodsInNo = gi.GoodsInId                                    
                  LEFT JOIN tbPurchaseOrder po ON gi.PurchaseOrderNo = po.PurchaseOrderId             
      LEFT JOIN tbInternalPurchaseOrder ipo on ipo.PurchaseOrderNo = po.PurchaseOrderId              
                  LEFT JOIN tbGlobalCurrencyList gcu on si.CurrencyNo=gcu.GlobalCurrencyId              
                  WHERE     si.ClientNo = @ClientId    
        
                              
       AND ((@OCR = 0)                                    
                                 OR (@OCR = 1              
      AND si.ISOCRGEN = @OCR))   
                            AND ((@RecentOnly = 0)                                    
                                 OR (@RecentOnly = 1              
      AND si.SupplierInvoiceDate >= @RecentDate))                                    
                                                                       
                           AND ((@SupplierInvoiceNumber IS NULL)             
                                 OR ( si.SupplierInvoiceNumber LIKE @SupplierInvoiceNumber))                               
                                                                  
                            AND ((@URNNoLo IS NULL)                                    
                                 OR (NOT @URNNoLo IS NULL                                    
                                     AND si.URNNumber >= @URNNoLo))                                    
                            AND ((@URNNoHi IS NULL)                                    
                                 OR (NOT @URNNoHi IS NULL                                    
                        AND si.URNNumber <= @URNNoHi))                                    
                            AND ((@PurchaseOrderNoLo IS NULL)                                    
                                 OR (NOT @PurchaseOrderNoLo IS NULL                                    
                                     AND po.PurchaseOrderNumber >= @PurchaseOrderNoLo))                                    
                            AND ((@PurchaseOrderNoHi IS NULL)                             
                                 OR (NOT @PurchaseOrderNoHi IS NULL                                    
                                     AND po.PurchaseOrderNumber <= @PurchaseOrderNoHi))                             
                            AND ((@GoodsInNoLo IS NULL)                                    
                  OR (NOT @GoodsInNoLo IS NULL                                    
                                     AND gi.GoodsInNumber >= @GoodsInNoLo))                                    
                          AND ((@GoodsInNoHi IS NULL)                                    
                                 OR (NOT @GoodsInNoHi IS NULL     
                                     AND gi.GoodsInNumber <= @GoodsInNoHi))                                     
                            AND ((@SupplierInvoiceDateFrom IS NULL)                                    
       OR (NOT @SupplierInvoiceDateFrom IS NULL                                    
                                     AND si.SupplierInvoiceDate >= @SupplierInvoiceDateFrom))                                    
                            AND ((@SupplierInvoiceDateTo IS NULL)                                    
                                 OR (NOT @SupplierInvoiceDateTo IS NULL                                    
                                     AND si.SupplierInvoiceDate <= @SupplierInvoiceDateTo))                                    
                            AND ((@CMSearch IS NULL)                                    
                                 OR (NOT @CMSearch IS NULL                                    
                                     AND si.SupplierName LIKE @CMSearch))                              
                             AND ((@ExportedOnly IS NULL)                                    
                                 OR (ISNULL(si.Exported,0)=@ExportedOnly))                                    
                            AND ((@CannotBeExported IS NULL )                                    
                                 OR (ISNULL(si.CanbeExported,0)=@CannotBeExported))                     
                                                     
                            AND ((@ApproveAndUnExported IS NULL) OR (ISNULL(si.CanbeExported,0)=1 AND ISNULL(si.Exported,0)=0))           
                                      
                             AND ((@InternalPurchaseOrderNoLo IS NULL)                                    
                OR (NOT @InternalPurchaseOrderNoLo IS NULL                                    
                                     AND ipo.InternalPurchaseOrderNumber >= @InternalPurchaseOrderNoLo))                                    
                            AND ((@InternalPurchaseOrderNoHi IS NULL)                             
                                 OR (NOT @InternalPurchaseOrderNoHi IS NULL                                  
                        AND ipo.InternalPurchaseOrderNumber <= @InternalPurchaseOrderNoHi))             
              AND ((@StatusReason IS NULL)                             
                                 OR (NOT @StatusReason IS NULL                                    
                                     AND isnull(si.StatusReasonNo,0) = @StatusReason))     
              
                 )                                    
               
                          
        SELECT  *                                    
              , (SELECT count(*)                                    
                 FROM   cteSearch                                    
                ) AS RowCnt                                    
        FROM    cteSearch                                    
        WHERE   RowNum BETWEEN @StartPage AND @EndPage                                    
  ORDER BY RowNum           
          
   
   
   
   