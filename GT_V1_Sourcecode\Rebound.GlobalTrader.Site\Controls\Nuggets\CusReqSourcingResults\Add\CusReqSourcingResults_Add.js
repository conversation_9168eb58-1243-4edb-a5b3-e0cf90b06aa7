Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Add.initializeBase(this,[n]);this._intCustomerRequirementID=-1;this._intSupplierId=-1;this._intGlobalCurrencyNo=-1;this._intPOCurrencyNo=-1;this._PocurrencyCode="";this._blnSupHasCurrency=!0;this._intFinalCurrencyNo=0;this._mfrAdvisoryNotes=""};Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Add.prototype={get_intCustomerRequirementID:function(){return this._intCustomerRequirementID},set_intCustomerRequirementID:function(n){this._intCustomerRequirementID!==n&&(this._intCustomerRequirementID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Add.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&this.addSave(Function.createDelegate(this,this.saveClicked));this._intGlobalCurrencyNo=-1;this._intSupplierId=-1;this.setFieldValue("ctlPartNo",this._strPartNo);$find(this.getField("ctlSupplier").ControlID)&&$find(this.getField("ctlSupplier").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.supplierChangeEvent));$find(this.getField("ctlManufacturer").ControlID)&&$find(this.getField("ctlManufacturer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.getMfrNotes));this.showField("ctlBuyCurrency",!1);this.showField("ctlCurrency",!0);this.getDropDownsData();$find(this.getField("ctlManufacturer").ControlID)&&$find(this.getField("ctlManufacturer").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.getMfrNotes));$find(this.getField("ctlSupplier").ControlID)&&$find(this.getField("ctlSupplier").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this,this.getSupplierNotes))},dispose:function(){this.isDisposed||(this._intCustomerRequirementID=null,this._blnSupHasCurrency=null,this._mfrAdvisoryNotes=null,Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Add.callBaseMethod(this,"dispose"))},getDropDownsData:function(){this.getFieldDropDownData("ctlROHS");this.getFieldDropDownData("ctlOfferStatus");this.getFieldDropDownData("ctlMSL")},saveClicked:function(){if(this.getFinalCurrency(),this.validateForm()){this._blnSupHasCurrency||alert("Selected currency will be set as default purchase currency of supplier");var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CusReqSourcingResults");n.set_DataObject("CusReqSourcingResults");n.set_DataAction("AddNew");n.addParameter("CustomerRequirementID",this._intCustomerRequirementID);n.addParameter("Part",this.getFieldValue("ctlPartNo"));n.addParameter("Manufacturer",this.getFieldValue("ctlManufacturer"));n.addParameter("DateCode",this.getFieldValue("ctlDateCode"));n.addParameter("Package",this.getFieldValue("ctlPackage"));n.addParameter("Product",this.getFieldValue("ctlProduct"));n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("Price",this.getFieldValue("ctlPrice"));n.addParameter("Currency",this._intFinalCurrencyNo);n.addParameter("OfferStatus",this.getFieldValue("ctlOfferStatus"));n.addParameter("ROHS",this.getFieldValue("ctlROHS"));n.addParameter("SupplierNo",this.getFieldValue("ctlSupplier"));n.addParameter("SupplierName",this.getFieldComboText("ctlSupplier"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("MSL",this.getFieldValue("ctlMSL"));n.addParameter("blnSupHasCurrency",this._blnSupHasCurrency);n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return(this._intFinalCurrencyNo==null||this._intFinalCurrencyNo<=0)&&(n=!1,this.showError(!0,"Please select the currency")),n},supplierChangeEvent:function(){this._intSupplierId!=this.getFieldValue("ctlSupplier")&&(this._intSupplierId=this.getFieldValue("ctlSupplier"),this.getPurchaseData())},getPurchaseData:function(){var n=new Rebound.GlobalTrader.Site.Data;this._strPath="controls/Nuggets/PurchaseRequestLineDetail";this._strData="PurchaseRequestLineDetail";n.set_PathToData(this._strPath);n.set_DataObject(this._strData);n.set_DataAction("GetDefaultPurchasingInfo");n.addParameter("id",this.getFieldValue("ctlSupplier"));n.addDataOK(Function.createDelegate(this,this.getPurchaseDataOK));n.addError(Function.createDelegate(this,this.getPurchaseDataError));n.addTimeout(Function.createDelegate(this,this.getPurchaseDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getPurchaseDataOK:function(n){var t=n._result;this._intGlobalCurrencyNo=t.GlobalCurrencyNo;this._intPOCurrencyNo=t.CurrencyNo;this._intGlobalCurrencyNo==0?(this.showField("ctlBuyCurrency",!0),this.showField("ctlCurrency",!1),this._blnSupHasCurrency=!1,this.getFieldDropDownData("ctlBuyCurrency")):(this.showField("ctlBuyCurrency",!1),this.showField("ctlCurrency",!0),this._blnSupHasCurrency=!0,this.bindCurrency());$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSupplier_ctl04_aut_ctl03").parent().find(".advisory-notes").remove();$("#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlSupplier_ctl04_aut_ctl03").append($R_FN.createAdvisoryNotesIcon(t.CompanyAdvisoryNotes,"margin-left-10"))},getPurchaseDataError:function(n){this.showError(!0,n.get_ErrorMessage())},bindCurrency:function(){this.getFieldControl("ctlCurrency")._intGlobalCurrencyNo=this._intGlobalCurrencyNo;this.getFieldDropDownData("ctlCurrency");this.setFieldValue("ctlCurrency",this._intPOCurrencyNo)},getFinalCurrency:function(){this._intFinalCurrencyNo=this._blnSupHasCurrency==!1?this.getFieldValue("ctlBuyCurrency"):this.getFieldValue("ctlCurrency")},getMfrNotes:function(){var n=new Rebound.GlobalTrader.Site.Data;this._strPath="controls/Nuggets/ManufacturerMainInfo";this._strData="ManufacturerMainInfo";n.set_PathToData(this._strPath);n.set_DataObject(this._strData);n.set_DataAction("GetAdvisoryNotes");n.addParameter("ID",this.getFieldValue("ctlManufacturer"));n.addDataOK(Function.createDelegate(this,this.getMfrNotesOK));n.addError(Function.createDelegate(this,this.getMfrNotesError));n.addTimeout(Function.createDelegate(this,this.getMfrNotesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getMfrNotesOK:function(n){var i=n._result,t=$find(this.getField("ctlManufacturer").ControlID)._aut._lblSelectedValue;$(t).parent().find(".advisory-notes").remove();$(t).append($R_FN.createAdvisoryNotesIcon(i.MfrAdvisoryNotes,"margin-left-10"))},getMfrNotesError:function(n){this.showError(!0,n.get_ErrorMessage())},getSupplierNotes:function(){var n=new Rebound.GlobalTrader.Site.Data;this._strPath="controls/Nuggets/CompanyMainInfo";this._strData="CompanyMainInfo";n.set_PathToData(this._strPath);n.set_DataObject(this._strData);n.set_DataAction("GetAdvisoryNotes");n.addParameter("ID",this.getFieldValue("ctlSupplier"));n.addDataOK(Function.createDelegate(this,this.getSupplierNotesOK));n.addError(Function.createDelegate(this,this.getSupplierNotesError));n.addTimeout(Function.createDelegate(this,this.getSupplierNotesError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getSupplierNotesOK:function(n){var i=n._result,t=$find(this.getField("ctlSupplier").ControlID)._aut._lblSelectedValue;$(t).parent().find(".advisory-notes").remove();$(t).append($R_FN.createAdvisoryNotesIcon(i.AdvisoryNotes,"margin-left-10"))},getSupplierNotesError:function(n){this.showError(!0,n.get_ErrorMessage())}};Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CusReqSourcingResults_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);