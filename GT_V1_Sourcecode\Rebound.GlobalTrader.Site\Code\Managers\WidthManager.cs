//Marker     changed by         Date          Remarks
//[001]      <PERSON><PERSON>      14/12/2018    Adding Columns for Import in Orders Requirement
//[002]      <PERSON><PERSON>      08/02/2019    Adjusting Width for Columns for BOM Items.
//[003]      <PERSON><PERSON>      08/02/2019    Adjusting Width for Columns for HUBRFQ BOM Items.

using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.Site {
	public class WidthManager {

		public static Unit GetWidth(TextBoxWidth enmTextBoxWidth) {
			Unit unt = Unit.Empty;
			switch (enmTextBoxWidth) {
				case TextBoxWidth.Date: unt = Unit.Pixel(120); break;
				case TextBoxWidth.PartNo: unt = Unit.Pixel(200); break;
				case TextBoxWidth.Quantity: unt = Unit.Pixel(100); break;
				case TextBoxWidth.ReportParameter: unt = Unit.Pixel(200); break;
			}
			return unt;
		}

		public enum TextBoxWidth {
			Date,
			PartNo,
			Quantity,
			ReportParameter
		}

		public static Unit GetWidth(ColumnWidth enmColumnWidth) {
			Unit unt = Unit.Empty;
			switch (enmColumnWidth) {
				case ColumnWidth.Quantity: unt = Unit.Pixel(85); break;
				case ColumnWidth.CurrencyValue: unt = Unit.Pixel(90); break;
				case ColumnWidth.CurrencyCode: unt = Unit.Pixel(80); break;
				case ColumnWidth.Date: unt = Unit.Pixel(115); break;
				case ColumnWidth.DateAndTime: unt = Unit.Pixel(130); break;
				case ColumnWidth.IsDefault: unt = Unit.Pixel(110); break;
				case ColumnWidth.BooleanValue: unt = Unit.Pixel(100); break;
				case ColumnWidth.ListName: unt = Unit.Pixel(180); break;
				case ColumnWidth.LoginName: unt = Unit.Pixel(150); break;
				case ColumnWidth.ContactName: unt = Unit.Pixel(160); break;
				case ColumnWidth.CompanyName: unt = Unit.Pixel(160); break;
				case ColumnWidth.CompanyType: unt = Unit.Pixel(140); break;
				case ColumnWidth.TelNo: unt = Unit.Pixel(150); break;
				case ColumnWidth.PartNo: unt = Unit.Pixel(180); break;
				case ColumnWidth.EmailAddress: unt = Unit.Pixel(250); break;
				case ColumnWidth.ManufacturerCode: unt = Unit.Pixel(70); break;
				case ColumnWidth.DateCode: unt = Unit.Pixel(70); break;
				case ColumnWidth.Product: unt = Unit.Pixel(270); break;
				case ColumnWidth.Package: unt = Unit.Pixel(70); break;
				case ColumnWidth.SystemDocumentNumber: unt = Unit.Pixel(120); break;
				case ColumnWidth.SystemDocumentName: unt = Unit.Pixel(150); break;
				case ColumnWidth.StarRating: unt = Unit.Pixel(90); break;
				case ColumnWidth.Warehouse: unt = Unit.Pixel(150); break;
				case ColumnWidth.ExternalCompanyDocument: unt = Unit.Pixel(150); break;
				case ColumnWidth.City: unt = Unit.Pixel(150); break;
				case ColumnWidth.Country: unt = Unit.Pixel(150); break;
				case ColumnWidth.Lot: unt = Unit.Pixel(170); break;
				case ColumnWidth.LineNumber: unt = Unit.Pixel(40); break;
				case ColumnWidth.StockLogType: unt = Unit.Pixel(220); break;
                case ColumnWidth.DefaultSOPO: unt = Unit.Pixel(53); break;
                case ColumnWidth.FinanceContact: unt = Unit.Pixel(63); break;
                case ColumnWidth.QuantityStock: unt = Unit.Pixel(120); break;
                case ColumnWidth.OriginalLandedCost: unt = Unit.Pixel(134); break;
                case ColumnWidth.CurrentLandedCost: unt = Unit.Pixel(134); break;
                case ColumnWidth.Location: unt = Unit.Pixel(35); break;
                case ColumnWidth.LineNo: unt = Unit.Pixel(25); break;
                case ColumnWidth.ClientName: unt = Unit.Pixel(80); break;
                case ColumnWidth.IPO: unt = Unit.Pixel(68); break;
                case ColumnWidth.SerialNo: unt = Unit.Pixel(280); break;
                case ColumnWidth.BOMNo: unt = Unit.Pixel(140); break;//[001] 
                case ColumnWidth.BOMName: unt = Unit.Pixel(80); break;//[001] 
                case ColumnWidth.ImportDate: unt = Unit.Pixel(80); break;//[001] 
                case ColumnWidth.NoOfRequirement: unt = Unit.Pixel(41); break;//[001] 
                case ColumnWidth.BOMItemsSystemDocumentNumber: unt = Unit.Pixel(120); break;//[002] 
                case ColumnWidth.BOMItemsQuantity: unt = Unit.Pixel(129); break;//[002] 
                case ColumnWidth.BOMItemsPartNumber: unt = Unit.Pixel(111); break;//[002] 
                case ColumnWidth.BOMItemsManufacturerCode: unt = Unit.Pixel(70); break;//[002] 
                case ColumnWidth.BOMItemsPackage: unt = Unit.Pixel(89); break;//[002] 
                case ColumnWidth.BOMItemsCompanyName: unt = Unit.Pixel(139); break;//[002] 
                case ColumnWidth.BOMItemsSalesman: unt = Unit.Pixel(138); break;//[002] 
                case ColumnWidth.BOMItemsFactorySealed: unt = Unit.Pixel(87); break;//[002] 
                case ColumnWidth.BOMItemsNotes: unt = Unit.Pixel(32); break;//[002] 
                case ColumnWidth.HUBRFQBOMItemsSystemDocumentNumber: unt = Unit.Pixel(120); break;//[003] 
                case ColumnWidth.HUBRFQBOMItemsQuantity: unt = Unit.Pixel(129); break;//[003] 
                case ColumnWidth.HUBRFQBOMItemsPartNumber: unt = Unit.Pixel(111); break;//[003] 
                case ColumnWidth.HUBRFQBOMItemsManufacturerCode: unt = Unit.Pixel(70); break;//[003] 
                case ColumnWidth.HUBRFQBOMItemsPackage: unt = Unit.Pixel(89); break;//[003] 
                case ColumnWidth.HUBRFQBOMItemsCompanyName: unt = Unit.Pixel(139); break;//[003] 
                case ColumnWidth.HUBRFQBOMItemsSalesman: unt = Unit.Pixel(138); break;//[003] 
                case ColumnWidth.HUBRFQBOMItemsFactorySealed: unt = Unit.Pixel(108); break;//[003] 
                case ColumnWidth.HUBRFQBOMItemsNotes: unt = Unit.Pixel(32); break;//[003] 
                case ColumnWidth.BOMRecordsRemProcess: unt = Unit.Pixel(140); break;
                case ColumnWidth.PurchaseOrderCompanyName: unt = Unit.Pixel(133); break;
				case ColumnWidth.POLineCount: unt = Unit.Pixel(100); break;
            }
			return unt;
		}

		public enum ColumnWidth {
			Quantity,
			CurrencyValue,
			CurrencyCode,
			Date,
			DateAndTime,
			IsDefault,
			BooleanValue,
			ListName,
			LoginName,
			ContactName,
			TelNo,
			PartNo,
			EmailAddress,
			CompanyName,
			ManufacturerCode,
			DateCode,
			Product,
			Package,
			SystemDocumentNumber,
			SystemDocumentName,
			StarRating,
			Warehouse,
			ExternalCompanyDocument,
			City,
			Country,
			Lot,
			CompanyType,
			LineNumber,
			StockLogType,
            DefaultSOPO,
            FinanceContact,
            QuantityStock,
            OriginalLandedCost,
            CurrentLandedCost,
            Location,
            LineNo,
            ClientName,
            IPO,
            SerialNo,
            BOMNo,//[001] 
            BOMName,//[001] 
            ImportDate,//[001] 
            NoOfRequirement,//[001]
            BOMItemsSystemDocumentNumber,//[002]
            BOMItemsQuantity,//[002]
            BOMItemsPartNumber,//[002]
            BOMItemsManufacturerCode,//[002]
            BOMItemsPackage,//[002]
            BOMItemsCompanyName,//[002]
            BOMItemsSalesman,//[002]
            BOMItemsFactorySealed,//[002]
            BOMItemsNotes,//[002]
            HUBRFQBOMItemsSystemDocumentNumber,//[003]
            HUBRFQBOMItemsQuantity,//[003]
            HUBRFQBOMItemsPartNumber,//[003]
            HUBRFQBOMItemsManufacturerCode,//[003]
            HUBRFQBOMItemsPackage,//[003]
            HUBRFQBOMItemsCompanyName,//[003]
            HUBRFQBOMItemsSalesman,//[003]
            HUBRFQBOMItemsFactorySealed,//[003]
            HUBRFQBOMItemsNotes,//[003]
            BOMRecordsRemProcess,
            PurchaseOrderCompanyName,
			MOQ,
			UnitCostPrice,
			Supplier,
			GTDateCreated,
			Reference,
			PackagingType,
			ECCN,
			PublishDate,
			SupplierType,
			ApiResponse,
            CCUserID,
			SendToGroup,
            POLineCount
        }
	}
}
