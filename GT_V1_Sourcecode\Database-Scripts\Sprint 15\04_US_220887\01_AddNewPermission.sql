﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-220887]     An.TranTan		 18-Nov-2024		CREATE		Add new permissions allow bulk edit Strategic stock in Order > Sourcing
===========================================================================================  
*/
DECLARE @CurrentSortOrder INT;
SELECT @CurrentSortOrder = ISNULL(MAX(DisplaySortOrder),0) FROM tbSecurityFunction WHERE SiteSectionNo = 2; --Order

IF NOT EXISTS (SELECT 1 FROM tbSecurityFunction 
	WHERE FunctionName = 'Orders_Sourcing_BulkEdit_StrategicStock'
		OR SecurityFunctionId = 20010030
)
BEGIN
INSERT INTO tbSecurityFunction (
	SecurityFunctionId,
    FunctionName,
    Description,
    SitePageNo,
    SiteSectionNo,
    ReportNo,
    UpdatedBy,
    DLUP,
    InitiallyProhibitedForNewLogins,
    DisplaySortOrder
)
VALUES (
     20010030,                -- SecurityFunctionId
    'Orders_Sourcing_BulkEdit_StrategicStock',            -- FunctionName
    'Allow Permission to View Bulk Edit Strategic Logistic', -- Description
    2000101,                -- Sourcing
    2,                      -- SiteSectionNo
    NULL,                   -- ReportNo (NULL because it's not related to a report)
    1,						-- UpdatedBy (ID of the user who updated this function)
    GETDATE(),              -- DLUP (current date and time)
    1,                      -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
    @CurrentSortOrder + 1   -- DisplaySortOrder
);
END

