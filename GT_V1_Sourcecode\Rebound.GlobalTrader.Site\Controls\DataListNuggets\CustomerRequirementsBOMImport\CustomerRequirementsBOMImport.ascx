<%@ Control Language="C#" CodeBehind="CustomerRequirementsBOMImport.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.CustomerRequirementsBOMImport" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server"  BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server" >
			<FieldsLeft>
				
		       <ReboundUI_FilterDataItemRow:TextBox id="ctlBOMName" runat="server" ResourceTitle="BOMName" FilterField="ClientBOMName"  />
		        <ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName"  />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Salesman" />
                <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
               
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlStatus" runat="server" ResourceTitle="Status" DropDownType="ClientBomStatus"  DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Status" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateImportFrom" runat="server" ResourceTitle="DateImportFrom" FilterField="ImportDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateImportTo" runat="server" ResourceTitle="DateImportTo" FilterField="ImportDateTo" />
            </FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
 