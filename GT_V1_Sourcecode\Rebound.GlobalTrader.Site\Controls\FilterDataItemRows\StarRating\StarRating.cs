using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Nuggets;
using Rebound.GlobalTrader.Site.Controls.DropDowns;

namespace Rebound.GlobalTrader.Site.Controls.FilterDataItemRows {
    [DefaultProperty("")]
    [ToolboxData("<{0}:StarRating runat=server></{0}:StarRating>")]
    public class StarRating : Base {

        #region Locals

        protected Controls.StarRating _ctlRating;
        protected DropDowns.NumericalComparison _ddl;

        #endregion

        #region Properties

        private int _intMaxRating = 5;
        public int MaxRating {
            get { return _intMaxRating; }
            set { _intMaxRating = value; }
        }

        private int _intCurrentRating = 0;
        public int CurrentRating {
            get { return _intCurrentRating; }
            set { _intCurrentRating = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            FieldType = Type.StarRating;
            base.OnInit(e);
            AddScriptReference(Functions.GetScriptReference(_blnConfigurationIsDebug, "Rebound.GlobalTrader.Site", "Controls.FilterDataItemRows.StarRating.StarRating.js", true));
        }

        /// <summary>
        /// OnLoad
        /// </summary>
        /// <param name="e"></param>
        protected override void OnLoad(EventArgs e) {
            EnsureChildControls();
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.StarRating", ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlRating", _ctlRating.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ddl", _ddl.ddl.ClientID);
            base.OnLoad(e);
        }

        /// <summary>
        /// create controls
        /// </summary>
        protected override void CreateChildControls() {
            base.CreateChildControls();

            //dropdown
            _ddl = (DropDowns.NumericalComparison)DropDownManager.GetDropDown("Rebound.GlobalTrader.Site", "NumericalComparison");
            _ddl.IncludeNoValue = false;
            _ddl.CanRefresh = false;
            _ddl.ID = "ddlComparison";
            _lblField.Controls.Add(_ddl);

            //stars control
            _ctlRating = new Controls.StarRating();
            _ctlRating.MaxRating = _intMaxRating;
            _ctlRating.CurrentRating = _intCurrentRating;
            _lblField.Controls.Add(_ctlRating);
        }

        public override void SetDefaultValue() {
            if (DefaultValue == null) DefaultValue = "";
            int intDefault = 0;
            if (int.TryParse(DefaultValue, out intDefault)) SetInitialValue(intDefault);
            base.SetDefaultValue();
        }

        public override void Reset() {
			EnsureChildControls();
			//_ddl.InitialValue = Convert.ToInt32(NumericalComparison.NumericalComparisonType.EqualTo).ToString();
            _ctlRating.CurrentRating = 0;
            Enable(false);
            base.Reset();
        }

        #endregion

        public void SetInitialValue(int intValue, int intNumericalComparison) {
            EnsureChildControls();
            _ddl.InitialValue = intNumericalComparison.ToString();
            _ctlRating.CurrentRating = intValue;
			if (intValue > 0) Enable(true);
        }
        public void SetInitialValue(int intValue) {
            SetInitialValue(intValue, Convert.ToInt32(NumericalComparison.NumericalComparisonType.EqualTo));
        }

    }

}