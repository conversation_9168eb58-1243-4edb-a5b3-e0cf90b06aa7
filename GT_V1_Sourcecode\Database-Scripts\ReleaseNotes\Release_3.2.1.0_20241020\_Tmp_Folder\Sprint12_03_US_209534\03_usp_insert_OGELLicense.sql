﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209534]     NgaiTo		 	17-Sep-2024			UPDATE		209534: OGEL approval dropdown to be moved out of code to the setup screen
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_OGELLicense] (
	@OgelNumber NVARCHAR(250),
	@Description NVARCHAR(500) = NULL,
	@InActive BIT = NULL,
	@UpdatedBy INT = NULL,
	@OgelId INT OUTPUT
	)
AS
BEGIN
	-- Check for existing tbOGELLicense with the same OgelNumber
	IF EXISTS (SELECT 1 FROM tbOGELLicense WHERE OgelNumber = LTRIM(RTRIM(@OgelNumber)))
	BEGIN
		SET @OgelId = - 1;
	END
	ELSE
	BEGIN
		BEGIN    
            INSERT INTO dbo.tbOGELLicense (
			OgelNumber,
			[Description],
			Inactive,
			UpdatedBy,
			DLUP
			)
		VALUES (
			@OgelNumber,
			@Description,
			@InActive,
			@UpdatedBy,
			CURRENT_TIMESTAMP
			)
		END
		SET @OgelId = SCOPE_IDENTITY();
	END

	SELECT @OgelId;
END
GO


