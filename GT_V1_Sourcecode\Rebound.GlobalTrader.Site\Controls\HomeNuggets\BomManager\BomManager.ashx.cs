using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BomManager : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }

        /// <summary>
        /// Gets the main data
        /// </summary>
        private void GetData()
        {
            try
            {
                //List<BLL.BOM> allBom = BLL.BOM.GetBomList(SessionManager.ClientID, SessionManager.IsPOHub, null,);
                List<BLL.BOMManagerContract> lstReady = new List<BOMManagerContract>();
                List<BLL.BOMManagerContract> lstPartial = new List<BOMManagerContract>();
                List<BLL.BOMManagerContract> lstNew = new List<BOMManagerContract>();
                List<BLL.BOMManagerContract> lstRFQ = new List<BOMManagerContract>();
                List<BLL.BOMManagerContract> lstClosed = new List<BOMManagerContract>();
                List<BLL.BOMManagerContract> lstNoBid = new List<BOMManagerContract>();

                //Closed
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                //lstClosed = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.Closed);
                //lstClosed = lstClosed.FindAll(x => SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstClosed = BLL.BOMManagerContract.GetBomManagerList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BomManagerStatus.List.Closed, SessionManager.LoginID);
                //for (int i = 0; i < (lstClosed.Count > 5 ? 5 : lstClosed.Count); i++)
                for (int i = 0; i < lstClosed.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMManagerId", lstClosed[i].BOMManagerId);
                    jsnItem.AddVariable("BOMManagerCode", lstClosed[i].BOMManagerName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstClosed[i].RequestToPOHubBy > 0 ? lstClosed[i].ClientName : lstClosed[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstClosed[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstClosed[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Closed", jsnItems);
                jsn.AddVariable("ClosedCount", lstClosed.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstClosed = null;
                //Ready                
                jsnItems = new JsonObject(true);
                //lstReady = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.Released );
                //lstReady = lstReady.FindAll(x=> SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstReady = BLL.BOMManagerContract.GetBomManagerList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BomManagerStatus.List.Released, SessionManager.LoginID);
                for (int i = 0; i < lstReady.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMManagerId", lstReady[i].BOMManagerId);
                    jsnItem.AddVariable("BOMManagerCode", lstReady[i].BOMManagerName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstReady[i].RequestToPOHubBy > 0 ? lstReady[i].ClientName : lstReady[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstReady[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstReady[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Ready", jsnItems);
                jsn.AddVariable("ReadyCount", lstReady.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstReady = null;
                //Partial
                jsnItems = new JsonObject(true);
                //lstPartial = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.PartialReleased );
                //lstPartial = lstPartial.FindAll(x=> SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstPartial = BLL.BOMManagerContract.GetBomManagerList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BomManagerStatus.List.PartialReleased, SessionManager.LoginID);
                for (int i = 0; i < lstPartial.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMManagerId", lstPartial[i].BOMManagerId);
                    jsnItem.AddVariable("BOMManagerCode", lstPartial[i].BOMManagerName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstPartial[i].RequestToPOHubBy > 0 ? lstPartial[i].ClientName : lstPartial[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstPartial[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstPartial[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Partial", jsnItems);
                jsn.AddVariable("PartialCount", lstPartial.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstPartial = null;

                //New               
                jsnItems = new JsonObject(true);
                //lstNew = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.New);
                //lstNew = lstNew.FindAll(x=> SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                //lstNew = BLL.BOMManagerContract.GetBomManagerList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BomManagerStatus.List.New, SessionManager.LoginID);
                lstNew = BLL.BOMManagerContract.GetBomManagerList(SessionManager.ClientID, SessionManager.IsPOHub,
                    null,
                   SessionManager.IsPOHub == true ?(int)BomManagerStatus.List.OffersAdded: (int)BomManagerStatus.List.New, 
                    SessionManager.LoginID);
                for (int i = 0; i < lstNew.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMManagerId", lstNew[i].BOMManagerId);
                    jsnItem.AddVariable("BOMManagerCode", lstNew[i].BOMManagerName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstNew[i].RequestToPOHubBy > 0 ? lstNew[i].ClientName : lstNew[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstNew[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstNew[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("New", jsnItems);
                jsn.AddVariable("NewCount", lstNew.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstNew = null;

                //RFQ
                jsnItems = new JsonObject(true);
                //lstRFQ = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.RPQ);
                //lstRFQ = lstRFQ.FindAll(x => SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                lstRFQ = BLL.BOMManagerContract.GetBomManagerList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BomManagerStatus.List.SendToHub, SessionManager.LoginID);
                for (int i = 0; i < lstRFQ.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("BOMManagerId", lstRFQ[i].BOMManagerId);
                    jsnItem.AddVariable("BOMManagerCode", lstRFQ[i].BOMManagerName);
                    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstRFQ[i].RequestToPOHubBy > 0 ? lstRFQ[i].ClientName : lstRFQ[i].CompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lstRFQ[i].DLUP));
                    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstRFQ[i].QuoteRequired));
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("IsHub", SessionManager.IsPOHub == true);
                jsn.AddVariable("RFQ", jsnItems);
                jsn.AddVariable("RFQCount", lstRFQ.Count);
                jsnItems.Dispose();
                jsnItems = null;
                lstRFQ = null;
                //NoBid
                ////jsnItems = new JsonObject(true);
                //lstNoBid = allBom.FindAll(x => x.StatusValue == (int)BOMStatus.List.NoBid);
                //lstNoBid = lstNoBid.FindAll(x => SessionManager.IsPOHub == true ? true : x.UpdatedBy == SessionManager.LoginID);
                ////lstNoBid = BLL.BOMManagerContract.GetBomManagerList(SessionManager.ClientID, SessionManager.IsPOHub, null, (int)BOMManagerStatus.List.NoBid, SessionManager.LoginID);
                ////for (int i = 0; i < lstNoBid.Count; i++)
                ////{
                ////    JsonObject jsnItem = new JsonObject();
                ////    jsnItem.AddVariable("BOMManagerId", lstNoBid[i].BOMManagerId);
                ////    jsnItem.AddVariable("BOMManagerCode", lstNoBid[i].BOMManagerName);
                ////    jsnItem.AddVariable("Name", SessionManager.IsPOHub == true && lstNoBid[i].RequestToPOHubBy > 0 ? lstNoBid[i].ClientName : lstNoBid[i].CompanyName);
                ////    jsnItem.AddVariable("Date", Functions.FormatDate(lstNoBid[i].DLUP));
                ////    jsnItem.AddVariable("QuoteRequired", Functions.FormatDate(lstNoBid[i].QuoteRequired));
                ////    jsnItems.AddVariable(jsnItem);
                ////    jsnItem.Dispose();
                ////    jsnItem = null;
                ////}
                ////jsn.AddVariable("NoBid", jsnItems);
                ////jsn.AddVariable("NoBidCount", (lstNoBid.Count > 5 ? 5 : lstNoBid.Count));
                ////jsnItems.Dispose();
                ////jsnItems = null;
                ////lstNoBid = null;

                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                Errorlog obj = new Errorlog();
                obj.LogMessage(Convert.ToString("Exception- " + ex.Message + " " + ex.StackTrace));
            }

        }
    }
}

