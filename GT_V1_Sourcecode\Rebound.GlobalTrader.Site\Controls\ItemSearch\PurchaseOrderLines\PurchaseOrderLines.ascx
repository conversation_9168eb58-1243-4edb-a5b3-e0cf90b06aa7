<%@ Control Language="C#" CodeBehind="PurchaseOrderLines.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlPurchaseOrderNo" runat="server" ResourceTitle="PurchaseOrderNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlPartNo" runat="server" ResourceTitle="PartNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateDeliveredFrom" runat="server" ResourceTitle="DateDeliveredFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateDeliveredTo" runat="server" ResourceTitle="DateDeliveredTo" />
	</FieldsRight>	
</ReboundUI_ItemSearch:DesignBase>
