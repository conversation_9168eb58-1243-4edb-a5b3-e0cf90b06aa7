using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class StockItemsBelowTheMinimumQuantity : Base {

		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "StockItemsBelowTheMinimumQuantity";
			base.OnInit(e);
			AddScriptReference("Controls.HomeNuggets.StockItemsBelowTheMinimumQuantity.StockItemsBelowTheMinimumQuantity.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base", this.ctlDesignBase.ClientID);
			base.OnLoad(e);
		}
	}
}