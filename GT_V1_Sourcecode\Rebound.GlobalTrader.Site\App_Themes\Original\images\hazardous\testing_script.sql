select * from tbTeam tm join tblogin lg on tm.Manager = lg.<PERSON>ginId  WHERE lg.LoginName = 'simong'

select count(*) from tbTeam tm join tblogin lg on tm.Manager = lg.LoginId  WHERE lg.<PERSON>gin<PERSON><PERSON> = 'simong' and tm.Manager is not null 
select * from tbDivision dv join tblogin lg on dv.Manager = lg.LoginId  WHERE lg.LoginName = 'simong'  and not dv.Inactive = 1

select * from tbLogin where Login<PERSON>ame like '%Simon%'
select * from tbLogin where LoginName='simong'
select * from tbLogin where LoginName='espiregt'
select * from tbTeam  where Manager=2858 --TeamId=100270-- Manager=2858
select * from tbdivision where manager=2858
update tbTeam  set Manager=null where TeamId=100250
select * from tbTeam where Manager=2858
select * from tbdivision where manager=2858

select * from tbTeam tm where tm.Manager =1973
select * from tbdivision where divisionid=101





--delete from tbDivisionTargetDraft
--delete from tbDivisionTargetFinal



--delete from tbTeamTargetDraft
--delete from tbTeamTargetFinal



--delete from tbCustomerTargetDraft
--delete from tbCustomerTargetFinal



--delete from tbSalesTargetFinal
--delete from tbSalesTargetDraft







select * from tbDivisionTargetDraft
select *  from tbDivisionTargetFinal

select * from tbCustomerTargetDraft
select *  from tbCustomerTargetFinal

select * from tbSalesTargetFinal
select *  from tbSalesTargetDraft

select * from tbTeamTargetDraft
select *  from tbTeamTargetFinal


--crm prospect
--update tbCRMProspectsDLL set name ='Daily' where CRMProspectsDLLTypeId=4  and id=9
--update tbCRMProspectsDLL set name ='Weekly' where CRMProspectsDLLTypeId=4  and id=10
--update tbCRMProspectsDLL set name ='Monthly' where CRMProspectsDLLTypeId=4  and id=11
--insert into tbCRMProspectsDLL values ('Quarterly',getdate(),0,4)

  
 
--alter table tbProspectsQualification   add [CommoditiesName] [varchar](500) NULL
--usp_update_CRMProspects_for_Company
--usp_get_CRMProspects_for_Company

--tbPartEccnMapped
--sp_helptext [usp_autosearch_PartEccnMapped]
--sp_helptext [usp_insert_CustomerRequirement]
--sp_helptext [usp_update_CustomerRequirement]
--sp_helptext [usp_Search_for_MappedPartWithECCNCode]
--sp_helptext [usp_Search_for_IHSECCNCodeDetails]
--setup section sp
--sp_helptext [ufn_GetECCNMessage]
--sp_helptext [usp_insert_ECCN]
--sp_helptext [usp_selectAll_ECCN_for_Client]

COMPANY SETTINGS
ECCN
ECCN
Save Cancel
RES(FORMTITLES:ECCN_ADD)

STM32G473QBT6TR
MF10CCWMX/NOPB
BAV99
BAV99W-AQ
AD8016ARBZ-REEL
XAZU4EV-1SFVC784Q
BAV99L6327
BAV99RW
select *from tbPurchaseOrderLine where PurchaseOrderNo=573575
--update tbPurchaseOrderLine set ECCNCode='EAR99' where PurchaseOrderNo=573575


select * from tbECCN   --where ECCNCode='3A001.A.1.C'
select * from tbPartEccnMapped where part='BAV99'
select count(*) from tbTempEccnMapped  -- where ImportedMfrPN='5STP21F1400'




select * from tbECCN -- ECCN Master Table
select * from tbPartEccnMapped -- ECCN sub Master Table
select * from tbTempEccnMapped --temp table Total Record Imported from Excel file  263554



USE [BorisGlobalTrader]
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[tbTempEccnMapped](
	[TempEccnMapped] [int] IDENTITY(1,1) NOT NULL,
	[ECCN] [nvarchar](50)  NULL,
	[ECCNGovernance] [nvarchar](50)  NULL,
	[ImportedMfr] [nvarchar](50)  NULL,
	[ImportedMfrPN] [nvarchar](50)  NULL,
	[MatchedPN] [nvarchar](50)  NULL,
	[MatchedMfr] [nvarchar](50) NULL,
)


select * from tbProduct where ProductDescription='Thyristors'
update tbPartEccnMapped set eccncode='3A001.A.3' where =
--truncate table tbeccn
insert into tbPartEccnMapped values('AD8016ARBZ-REEL',44,'3A001.A.1.C',101,1973,getdate())
select top 1 ECCNCode  from dbo.tbECCN m   where m.ECCNCode= '3A001.A.1.C' order by m.dlup  desc

select st.PartEccnMappedId,st.ECCNNo ,st.ECCNCode  FROM   dbo.tbPartEccnMapped st                               
WHERE  Part='AD8016ARBZ-REEL' 
sp_help tbPartEccnMapped
select * from tbIHSparts where part='BAV99L6327'-- ECCNCode is null
--STM32G473QBT6TR

select top 1 ECCNCode,ECCNId  from dbo.tbECCN m   where m.ECCNCode= 'EAR99' order by m.dlup  desc
select * from tbCustomerRequirement where CustomerRequirementId=4123936

select 
(case when (PartColumn is not null) then  'Part' 
when (CustomerPartColumn is not null) then  'CustomerPart' 
when (PackageColumn is not null) then  'Package' 
when (FixedCurrencyColumn is not null) then  'FixedCurrency' 
when (DateCodeColumn is not null) then  'DateCode' 
else ''    end) as testquary
 from  BorisGlobalTraderImports.dbo.tbBomImportMapping where CompanyNo=83150

declare @strQuary nvarchar(max)='a,b,c'
select * FROM dbo.[SplitString](@strQuary,',')
print @strQuary
--CustomerPartColumn
--PackageColumn
--FixedCurrencyColumn
--DateCodeColumn
--InstructionsColumn
--ManufacturerColumn
--ROHSColumn
--NotesColumn
--ProductColumn
--PriceColumn
--QuantityColumn
--from  BorisGlobalTraderImports.dbo.tbBomImportMapping where CompanyNo=83150

















select * from tbStock

--posted=1
--[usp_print_SalesOrderLine_for_SalesOrder]
--ECCN Printed on Printed document sp
--sp_helptext [usp_selectAll_ConsolidateSalesOrderLine_for_SalesOrder]
--sp_helptext [usp_print_SalesOrderLine_for_SalesOrder]
--sp_helptext [usp_selectAll_SalesOrderLine_for_SalesOrder]
--[usp_selectAll_SalesOrderLine_ReportPOStock]
--sp_helptext [usp_selectAll_SalesOrderLine_ReportPO]
--[usp_selectAll_SalesOrderLine_ReportManualStock]
--[usp_selectAll_SalesOrderLine_ReportShipped]
select * from BorisGlobalTraderImports.dbo.tbExcelStockImportColumnHeading
select * from BorisGlobalTraderImports.dbo.tbTempStockImportData
select * from BorisGlobalTraderImports.dbo.tbStockDataToBeImported
select * from tbmsllevel
select * from tblocation
select * from BorisGlobalTraderimports.dbo.tbCusReqToBeImported
select top 50 * from tbCustomerRequirement order by CustomerRequirementId desc --IHS  11 column
select * from tbIHSparts where Part='Bav99'
--truncate table  BorisGlobalTraderimports.dbo.tbExcelStockImportColumnHeading
--usp_Stock_Import_By_User trigger sp for lot toll
select * from tbstock order by stockid desc --3152349
usp_Stock_Import_By_User

select * from tbstock where stockid=908159

select * from tbLogin where LoginId=2266
select * from tbROHSStatus
1227399
select count(*) from tbstock where isimport=1
delete from tbstock where isimport=1

sp_help tbStockDataToBeImported
--alter table tbstock add [IsImport] [bit] NULL
--alter table tbstock add	[ImportDate] [datetime] NULL

select * from tbmanufacturer  where manufacturername like  '%Amphenol Sine Systems%' --11110
select * from tbproduct where productid=13428 -- productname like '%Adhesive Tape%' --13428

tbStockDataToBeImported
usp_GetDynamicStockHeaderColumn
usp_Select_ImportStockUtilityData
sp_helptext [usp_datalistnugget_Company_as_Customers]
sp_helptext usp_datalistnugget_Company_as_Suppliers
sp_helptext [usp_datalistnugget_Company_as_Prospects

select * from tbcompany where salesman=4702
select * from tblogin order by loginid desc  where loginname='TeamUser1'

--sp_for_bindgrider change order by 
[KPI_GetTeamSalesTarget]
[KPI_GetDivisionTarget]
sp_helptext [KPI_GetSalesCustomerTarget]


sp_helptext usp_SaveStockExcelColumn

declare @FROMDATE Datetime='2021-07-21'
declare @ENDDATE Datetime='2022-01-31'
select  * from tbSourcingResult where SourcingTable in ('PQ','EXPH','OFPH')  and FullPart like 'MF10CCWMXNOPB%' and ISNULL(IsReleased,0) = 1 --and OriginalEntryDate  between @FROMDATE   AND  @ENDDATE
ORDER BY ISNULL(OfferStatusChangeDate, OriginalEntryDate) DESC
--sp_helptext [usp_Select_SourcingResult_Epo]
update tbSourcingResult set FullPart='BAV99' , Part='BAV99' where SourcingResultId=1599359
select  top 10* from tbSourcingResult
IPO Buy Price
IPO Unit Sell Price
select * from tbManufacturer where ManufacturerName='Amphenol Sine Systems'

select * from tbPurchaseOrderLine where ECCNCode is not null order by PurchaseOrderLineId desc
select * from tbPurchaseOrderLine where PurchaseOrderNo=573536
update tbPurchaseOrderLine set ECCNCode='EAR99' where PurchaseOrderNo=573536

select top 20* from tbSalesOrderLine where Posted=1 order by SalesOrderNo desc
select * from tbSalesOrder where SalesOrderId=457081
select * from tbGoodsInLine where ECCNCode='EAR99'




select * from tbGoodsInLine order by GoodsInLineId  desc --761315
select * from tbInvoiceLine order by invoicelineid desc
select * from tbinvoice where invoiceid=844076
--[usp_select_Login_by_Name]
select * from tbAudit where TableName='tbSupplierInvoice' and headerno=301061
select top 31 * from tbAudit where headerno=301061 and TableName='tbSupplierInvoice' order by 
SELECT top 1* 
FROM tbAudit where headerno=301061 and TableName='tbSupplierInvoice'
and Auditid IN (SELECT Auditid FROM tbAudit WHERE dateauthorised = (SELECT MAX(dateauthorised) FROM tbAudit))
ORDER BY Auditid DESC
LIMIT 1
2022-01-31 15:51:51.540
SELECT TOP 1 ad.headerno as  SupplierInvoiceId,ad.updatedby as AuthorisedBy,(select lg.Employeename from tbLogin lg where lg.loginid=ad.updatedby)as AuthorisedByName,ad.DateAuthorised,ad.note as AuthorisedNote  FROM tbAudit ad
where ad.headerno=301061 and ad.TableName='tbSupplierInvoice'  ORDER BY ad.dateauthorised desc
select * from tbLogin

2022-02-01 11:05:34.360
SELECT
  top 1*
FROM
  tbAudit AS [data]
WHERE [data].headerno=301061 and [data].TableName='tbSupplierInvoice' and
  dateauthorised = (SELECT MAX(dateauthorised) FROM tbAudit )

--alter table tbAudit alter column note nvarchar(500)
--[usp_insert_SupplierInvoice]
--usp_update_SupplierInvoice
--[usp_insert_SupplierInvoiceLine]
--usp_insert_SupplierAuthorized
--usp_select_SupplierInvoice_Audit_Log
select * from tbSupplierInvoice where SupplierInvoiceId=301061 --order by SupplierInvoiceId desc
select * from tbSupplierInvoiceLine where SupplierInvoiceNo in (301061)
select * from tbPurchaseOrderLine where PurchaseOrderLineId=755142
select LoginId,LoginName,EMail,ADEmail from tblogin where loginid=1973
select top 20 * from tbPurchaseOrder order by PurchaseOrderId desc
select * from tbGoodsIn where GoodsInId=607190
select * from tbGoodsInLine where GoodsInNo=607190
insert into tbTempEccnMapped(ECCn,ECCNGovernance,ImportedMfr,ImportedMfrPn,MatchedPN,MatchedMfr) values('','','','8520502001','','')
select * from tbMailGroup where LOWER(Name)='quality approval' and ClientNo=101
select * from tbLogin where LoginName like '%Team%'
select * from tbCurrency
--3564

--<EMAIL>
--wFrVc}hjdW93ElR?jj
--<EMAIL>
--f:-Sz*)$k:D4P5!b
--------------------------------------add start date and end date into daily report log------------------------
--sp_helptext usp_datalistnugget_IHSCatalogue_Export
--sp_helptext usp_Import_BOM_By_User
select top 10 * from tbdebit order by DebitId desc
select * from tbdebit where DebitId=13052
select top 10 * from tbQuote
select top 10 * from tbQuoteLine
select * from tbSystemDocumentFooterHistory where SystemDocumentFooterHistoryId=90


select * from tbProduct
select * from tbIHSparts
select * from tbMSLLevel
sp_help tbSalesOrderline
select * from tbIHSparts where IHSPartsId=23951
select top(10) *, PartWatch from tbCustomerRequirement order by CustomerRequirementid desc --4123651
select *,REQStatus from tbCustomerRequirement where CustomerRequirementId=4122588
select *,REQStatus from tbCustomerRequirement where CustomerRequirementId=4123910
select Notes from tbSalesOrder where SalesOrderId=457076
select * from tbSourcingResult where bo
select SourcingResultId,IsReleased from tbSourcingResult where   SourcingResultId=1598494 and SourcingTable in ('PQ','OFPH','EXPH')
select * from BorisGlobalTraderimports.dbo.tbCusReqToBeImported
--sp_helptext [usp_update_BOM_Release]
select  top 10 ActualPrice as BuyPrice,Price as SellPrice,IsReleased,POHubCompanyNo,
SourcingTable,SourcingResultId from tbSourcingResult where  CustomerRequirementNo=4122588 
and POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH')

--
select  top 10 ActualPrice as BuyPrice,Price as SellPrice,IsReleased,POHubCompanyNo,
SourcingTable,SourcingResultId from tbSourcingResult where  SourcingResultId=1598494 
and POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH')
select * from tbCustomerRequirement where BOMNo = 229484 AND   POHubReleaseBy IS NULL
select * from tbCustomerRequirement where CustomerRequirementId=4122588
select POHubReleaseBy,REQStatus from tbCustomerRequirement  WHERE     CustomerRequirementId= 4122588
select count(*) from tbSourcingResult where    CustomerRequirementNo=4123910   and ActualPrice>=Price
-----------------------

Select  ROW_NUMBER() OVER(ORDER BY SourcingResultId DESC) AS Row, SourcingResultId,CustomerRequirementNo,  
  ActualPrice,Price,IsReleased,POHubCompanyNo,SourcingTable into #Reqtemp From tbSourcingResult   
  where CustomerRequirementNo=4122588  and SourcingTable in ('PQ','OFPH','EXPH')
  select * from #Reqtemp






select SourcingResultId from tbSourcingResult where  CustomerRequirementNo=4123911 and POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH')
select * from tbSourcingResult sr where   sr.ActualPrice>=sr.Price and CustomerRequirementNo in (select bm.CustomerRequirementId from tbCustomerRequirement bm where bm.BOMNo=229513)    
update tbSourcingResult set IsReleased=0 where SourcingResultId in (1598492
,1598493
,1599227)
update tbSourcingResult set Price=550 where SourcingResultId=785859
--785859--785855
update tbCustomerRequirement set DatePOHubRelease=null, POHubReleaseBy= null where CustomerRequirementId=4123911
Select  ROW_NUMBER() OVER(ORDER BY BOMNo DESC) AS Row, BOMNo,CustomerRequirementId into #temp From tbCustomerRequirement  where BOMNo =229513 AND   POHubReleaseBy IS NULL            

Select  ROW_NUMBER() OVER(ORDER BY SourcingResultId DESC) AS Row, SourcingResultId,CustomerRequirementNo,ActualPrice,Price,IsReleased,POHubCompanyNo,SourcingTable into #istemp From tbSourcingResult  where CustomerRequirementNo=4122588  and SourcingTable in ('PQ','OFPH','EXPH')
select * from #temp 
drop table #temp
4123911
4123912
sele4123913
4123914
4123915
4123923
select *  from tbCustomerRequirement where CustomerRequirementNumber in (2569610)
select * from tbSourcingResult where CustomerRequirementNo=2569610

select count(*) from tbLogin


4123923)
--delete from tbSourcingResult where SourcingResultId in (1598494,1598496,1598558)
1598494
1598496
1598558
  Select  ROW_NUMBER() OVER(ORDER BY SourcingResultId DESC) AS Row, SourcingResultId,CustomerRequirementNo,ActualPrice,
  Price,IsReleased,POHubCompanyNo,SourcingTable into #istemp From tbSourcingResult  where CustomerRequirementNo=4123914 and
  POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH') 
select * from #istemp 
drop table #temp

select ApprovedBy,DateApproved from tbPurchaseOrder where PurchaseOrderId= 573487
select * from tbPurchaseOrderPORPDF
select * from tbPurchaseOrderPDF
select * from tbSalesOrderPDF where FileType='SORPDFNEW'
--D:\Rebound-Project\GTMainUAT_1\GTMainB_UAT_2\Support\GTMainB_UAT2B_SPRT_22_Dec_21\Rebound.GlobalTrader.Site\User\UploadTemp\573487_20220104182119.pdf
select * from tbManufacturer
select count(*) from tbSourcingResult where    CustomerRequirementNo=4123910   and ActualPrice>=Price
select * from tbSourcingResult where  CustomerRequirementNo=4122588  and SourcingTable in ('PQ','OFPH','EXPH')
select   * from tbRestrictedManufacturer
--delete from tbRestrictedManufacturer where RestrictedManufacturerId=57
select * from tbproduct
--new work for team kpi
--sp_helptext  [usp_dropdown_TeamKpi_for_Client]

select * from tbDivision where ClientNo=101 and Manager=1984--1486
update tbDivision set Manager=1984,Inactive=0 where DivisionId=3

select * from tbTeam where ClientNo=101 and Manager=1984
update tbTeam set Manager=1984 where TeamId=176

update tbTeamTargetDraft set JanTarget=2000 where TeamTargetDraftId=7
select * from tbLogin where LoginName like '%vinay%'

select * from tbDivisionTargetDraft
select * from tbTeamTargetDraft





select count(*) from tbRestrictedManufacturer r join   
tbManufacturer m  on r.ManufacturerNo=m.ManufacturerId where m.Inactive=0 and m.ManufacturerName='Aavid Thermalloy' and ClientNo=114
sp_helptext usp_insert_RestrictedManufacture
sp_helptext usp_update_RestrictedManufacturer


 select * FROM dbo.tbRestrictedManufacturer AS rm INNER JOIN dbo.tbManufacturer AS m     
       ON rm.ManufacturerNo=m.ManufacturerId     
where m.ManufacturerName='Aavid Thermalloy' and m.Inactive=0 and  ClientNo=114   
select * from tbShipVia where ShipViaId=891
select * from tbWarningMessage
select * from tbRestrictedManufacturer
--truncate table tbRestrictedManufacturer
 --update tbWarningMessage set InActive=1 where WarningId=1
 select * from tbSystemWarningMessage
 --truncate table tbSystemWarningMessage
-- delete from tbSystemWarningMessage where SystemWarningMessageId in (43)
 --product -2
 --MFR -1
 --ERI -3

 select Exported from tbInvoice where Exported=null
 sp_help tbInvoice
 select * from tbSalesTargetFinal where SalesManNo=1973
   --Sp_helptext [usp_update_BOM_Release]
 --Sp_helptext [usp_update_CustomerRequirement_Release]
 --Sp_helptext [usp_update_Sourcing_Release]

 --insert into tbSystemWarningMessage values(101,6,'This product is known to have potential purchasing risks associated to it and as such you are requested to purchase directly from the IPO team only. ',2,0,0,null,getdate())
 --insert into tbWarningMessage (WarningName,InActive,ApplyCatagory)values('EPO',0,0)
 --insert into tbWarningMessage (WarningName,InActive,ApplyCatagory)values('Hazardous',0,0)
 --insert into tbWarningMessage (WarningName,InActive,ApplyCatagory)values('ERAI reported',0,0)
 --insert into tbWarningMessage (WarningName,InActive,ApplyCatagory)values('SupplierProdReqTesting',0,0)
 --insert into tbWarningMessage (WarningName,InActive,ApplyCatagory)values('Restricted Manufacturer',0,0)
 --insert into tbWarningMessage (WarningName,InActive,ApplyCatagory)values('Order Via IPO only',0,0)
 sp_helptext usp_datalistnugget_IHSCatalogue_Export

Select  ROW_NUMBER() OVER(ORDER BY SourcingResultId DESC) AS Row, SourcingResultId,CustomerRequirementNo,ActualPrice,Price into #i1stemp From tbSourcingResult  where CustomerRequirementNo=4122588 and POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH')       
select price from #i1stemp where SourcingResultId=1591508
 select COUNT(1) from #istemp
select SourcingResultId,CustomerRequirementNo,IsReleased from tbSourcingResult where    CustomerRequirementNo=4122588  and POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH')  --and ActualPrice>=Price 


select SourcingResultId,CustomerRequirementNo,IsReleased from tbSourcingResult where    CustomerRequirementNo in (2560186,2560188)  and POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH')  --and ActualPrice>=Price 


select count(*) from tbSourcingResult where    CustomerRequirementNo=4122588 and POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH')  and ActualPrice>=Price 
select * From tbCustomerRequirement  where BOMNo =229484 AND   POHubReleaseBy IS NULL  
Select  ROW_NUMBER() OVER(ORDER BY BOMNo DESC) AS Row, BOMNo,CustomerRequirementId into #temp From tbCustomerRequirement  where BOMNo =229484 AND   POHubReleaseBy IS NULL      
select * from #temp
declare @Max int=0
select @Max=COUNT(1) from #temp 
print @Max

select  count(*) from tbSourcingResult where  CustomerRequirementNo=4122588 and ActualPrice>=Price 
and POHubCompanyNo is not null and SourcingTable in ('PQ','OFPH','EXPH')

select mf.ManufacturerCode tbManufacturer mf ON cr.ManufacturerNo = mf.ManufacturerId
--alter table tbAddress add DivisionHeaderNo int 

select * from tbAddress
select * from tbPurchaseOrder where PurchaseOrdernumber=1141113123 --604313
select top 10 * from tbGoodsInLine order by GoodsInLineId desc
select * from tbWarningMessage
select * from tbSystemWarningMessage
trauncate
update tbSystemWarningMessage set InActive=0 where SystemWarningMessageId=10
delete from tbRestrictedManufacturer  where RestrictedManufacturerId=55
(select count(CustomerRequirementId) from tbCustomerRequirement where CustomerRequirementId=@CustomerRequirementId and REQStatus=1 and BOMNo is null and REQStatus!=5)  as PartEditStatus
ERAI reported
This supplier product may need enhanced testing.
select  top  10* from tbCustomerRequirement order by CustomerRequirementId desc --4123615
--delete from tbSystemWarningMessage where SystemWarningMessageId in (17)
select * from tbCustomerRequirement where CustomerRequirementId=4123609 and REQStatus=1 and BOMNo is null and REQStatus!=5
select *from tbCurrency
select * from tbPurchaseOrderPDF
select * from tbSalesOrderExcel
alter table tbsalesorder add [IsExcelDocAvailable] [bit] NULL
select *from tbManufacturerExcel
select * from tbIHSparts
SELECT Quantity,Price,CustomerRequirementNumber,CurrencyNo,ClientNo FROM dbo.tbCustomerRequirement 
WHERE  customerrequirementid=2660008 AND PartWatch=1   AND ISNULL(Closed,0)= 0 
--[usp_insert_CloneRequirementDataHUB]
--[usp_insert_CloneRequirementDataHUB]

select * from tbmasterlogin where adloginname like '%anand%'
select * from tblogin where masterloginno=537
select * from tbSecurityGroup where ClientNo=114 --186
select * from tbSecurityGroupLogin where SecurityGroupNo=186 --5102
--insert into tbSecurityGroupLogin (SecurityGroupNo,loginno)values(186,5102)
----------------------------------------------------------------------------
select * from tbreportparameter
select top 20 * from tbPurchaseOrder where PurchaseOrderId=573532
update tbPurchaseOrder set headerimagename='' where PurchaseOrderId=573532
--client_101_20210705173820.jpg
update tbPurchaseOrder set DateApproved=getdate(),DateOrdered=GETDATE()where PurchaseOrderId=589519
select top 40 * from tbPrintDocumentLog order by printdocumentlogid desc
--alter table  tbPrintDocumentLog drop column  DocCounterNumber
select isnull(DocCounterNumber+1,0) from  tbPrintDocumentLog 
where SectionName='Invoice' and SubSectionName='Invoice' and DocumentNo=843069
select top 8* from tbCustomerRequirement order by CustomerRequirementid desc --4223793
15051	181794
select top 10* from tbbom order by bomid desc --229461
select top 10* from tbbom where bomid=229463
select top 50 * from tbcompany where typeno is null and clientno=101 order by companyid desc

select * from  BorisGlobalTraderimports.dbo.tbTempBOMData
truncate table BorisGlobalTraderimports.dbo.tbTempBOMData
select top 10 SalesOrderNo,CustomerPO,SalesOrderNumber from tbInvoice where invoiceid=844078 --457040
select  top 50 *,SalesOrderNo from tbInvoiceLine where invoiceno=844078 
--select top 10 * from tbInvoice where invoicenumber=905400
-- sonumber=454712
--customerPo=144
--SalesorderNumber=1001641
select top 10 SalesOrderNo,CustomerPO,SalesOrderNumber from tbInvoice where invoiceid=514091 --450035
select  top 50 *,SalesOrderNo from tbInvoiceLine where invoiceno=514091 

select top 10 SalesOrderNo,CustomerPO,SalesOrderNumber from tbInvoice where invoiceid=843014 --463727
select  top 50 *,SalesOrderNo from tbInvoiceLine where invoiceno=843014 

select top  10 * from tbSourcingResult 
select * from tbCustomerRequirement where BOMNo=167891
select top  10 * from tbSourcingResult where CustomerRequirementNo in (select CustomerRequirementId from tbCustomerRequirement where BOMNo=167891)
sp_helptext usp_GetSupplierMessage
go
sp_helptext [ufn_GetSupplierMessage]
go
sp_helptext [usp_Select_Search_for_RestrictedManufacture]               
go
sp_helptext [usp_autosearch_Product]
go
sp_helptext [ufn_GetProductMessage]
go
sp_helptext [usp_GetProductMessage]
go
sp_helptext [usp_GetHazardousProductMessage]
-- first need to check invoice line same so number or not
-- if not tak customerpo number from salesorder
select top 10 * from tbInvoice where invoiceid=843052
select  top 50 *,SalesOrderNo from tbInvoiceLine where invoiceno=456657  
select  top 50 *,SalesOrderNo from tbInvoiceLine order by InvoiceLineid desc --where invoiceno=843060  
select * from tbShipVia
--all function relase at hub side grdie sp  need to send again to vinay sir
--sp_helptext usp_selectAll_SourcingResult_for_AllRelease
select * from tbSecurityFunction where SecurityFunctionId in (6010909,6010910,6010911)
select * from tbRestrictedManufacturer 
update tbRestrictedManufacturer set Inactive=0 where RestrictedManufacturerId=70
delete from tbRestrictedManufacturer where Manufacturerno=2018
select * from tbManufacturer where Inactive=0 and ManufacturerId=2018
insert into tbRestrictedManufacturer values(2018,'testdfd',0,101,null,getdate())
select ManufacturerName, count(ManufacturerName) as ManufacturerNameCount  from tbManufacturer where Inactive=0 and ManufacturerName='Aavid Thermalloy'  group by ManufacturerName having count(ManufacturerName)>1 order by  count(ManufacturerName) desc
select ManufacturerName, count(ManufacturerName) from tbManufacturer where Inactive=0   group by ManufacturerName having count(ManufacturerName)>1 order by  count(ManufacturerName) desc
--delete from tbRestrictedManufacturer  where RestrictedManufacturerId=5
select * from  tbManufacturer where Inactive=0 and ManufacturerId=1930 --and ManufacturerName='MICRON'
select * from  tbManufacturer where Inactive=0 and ManufacturerName='NEXPERIA'
--first check start from tbManufacturer active --where Inactive=0
--if record already exist then show meesage
select distinct  * from tbsalesorder  where SalesOrderId in (468396)  
      
      
CREATE PROCEDURE [dbo].[usp_update_RestrictedManufacturer]         
(       
@RestrictedManufacturerId    int ,        
@ClientNo    int ,        
@ManufacturerNo    int ,        
@Notes  nvarchar(500) = Null ,        
@InActive bit = Null ,        
@UpdatedBy    int    = Null ,        
@RowsAffected   int = NULL Output        
)      
AS        
BEGIN      
      
 UPDATE dbo.tbRestrictedManufacturer        
 SET  ManufacturerNo    = @ManufacturerNo           
  , Notes  = @Notes         
  , ClientNo    = @ClientNo        
  , Inactive    = @Inactive         
  , UpdatedBy    = @UpdatedBy        
  , DLUP     = current_timestamp        
 WHERE RestrictedManufacturerId    = @RestrictedManufacturerId        
--        
SELECT  @RowsAffected = @@ROWCOUNT        
      
END      
        
select  top 10 * from tbsalesorderline where salesorderno=456990  

select * from BorisGlobalTraderImports.dbo.tbCusReqToBeImported
--po
select * from tbterms where buy=1 and sell=0 or (buy=1 and sell=1)
--so
select * from tbterms where buy=0 and sell=1 or (buy=1 and sell=1)

select * from tbSupplierRMA where CompanyNo=216461

--229444

sp_help tbinvoice
--DocCounterNumber

select * from tbGoodsInLine where GoodsInLineId = 453179  
select * from tbGoodsInLine where GoodsInno=610670
select * from tbSalesOrder order by SalesOrderId desc
select * from tbSalesOrder where SalesOrderId=457042
select top 10 * from tbSalesOrderline where SalesOrderNo=457042
select top 10 * from tbSalesOrderline where 
update tbSalesOrder set as9120=1 where SalesOrderId=457042
457042
select * from tbSystemDocumentFooterHistory
select * from tbSalesOrderline where SalesOrderNo=455300
sp_help tbSalesOrder
select * from tbCredit
--update tbSalesOrderLine set Posted=1,Inactive=1 where SalesOrderLineId=869352
--check thos below table only [ SysDocHazardousHistoryNo ]  Section
select * from tbDebit order by Debitid desc --26
select * from tbInvoice  where invoiceid=843069 --26
select * from tbCustomerRMA order by CustomerRMAid desc
select * from tbSupplierRMA order by SupplierRMAid desc
select * from tbPurchaseOrder order by PurchaseOrderid desc --568752
--update tbPurchaseOrder set  SysDocHazardousHistoryNo=26 where PurchaseOrderid=573494
--update tbCustomerRMA set  SysDocHazardousHistoryNo=26 where CustomerRMAid=12949
--update tbSupplierRMA set  SysDocHazardousHistoryNo=26 where SupplierRMAid=14060
--update tbDebit set  SysDocHazardousHistoryNo=26 where Debitid=12903
----UAT
--update tbQuote set  SysDocHazardousHistoryNo=26,SysDocAS9120HistoryNo=21,SysDocCOOHistoryNo=27 where QuoteId=685674
select * from tbSystemDocumentFooterHistory where SystemDocumentFooterHistoryId=564
Version: 101 - 2021-07-20  This product may have a Dangerous Goods classification when transported. There may be additional transit time on this item
--ProFormaInvoice = 13, 
select * from tbSalesOrder where SalesOrderId=453215--453214
select * from tbSystemDocumentFooterHistory where SystemDocumentFooterHistoryId=5--577
sp_help tbSalesOrder
select authorisedby from tbSalesOrder where SalesOrderId=457035
update tbSalesOrder set authorisedby=0 where SalesOrderId=457035
select top 10 loginid,loginname,employeename,firstname,lastname,telephone from tblogin 
select * from tbInvoice  where InvoiceId =844077
select * from tbcustomerrma where customerrmaid=12879
select * from tbsupplierrma where supplierrmaid=13957
select * from tbdebit where debitid=12904
select top 10 * from tbCustomerRequirement order by CustomerRequirementid desc
sp_help [tbPrintSalesOrder]
insert into tbInvoiceDocumentHeader (InvoiceNo,DocType,HeaderImageName,SystemDocumentFooterHistoryNo,SystemDocumentHazardousHistoryNo,UpdatedBy,DLUP)
 values(843069,'IN','division_150117_20210709055332.jpg',5,26,1984,getdate())
delete from [tbPrintInvoice]
delete from [tbPrintSalesOrder]
delete from [tbPrintDocuments]
select top 40* from tbInvoice order by InvoiceId desc
select * from [tbPrintInvoice]
select * from [tbPrintSalesOrder]
select * from [tbPrintDocuments]
select * from tbGlobalProduct where IsHazardous is not null
select * from tbProduct

--------------------------------------
--need make script
--alter table tbGlobalProduct add [IsOrderViaIPOonly] [bit] NULL
--alter table tbProduct add [IsOrderViaIPOonly] [bit] NULL
-- sp_helptext [usp_insert_GlobalProduct]
-- sp_helptext [usp_update_GlobalProduct]
-- sp_helptext [usp_update_AllClient_Product]
--sp_helptext  [usp_selectAll_GlobalProduct]
-- sp_helptext [usp_autosearch_Product]
--sp_helptext [vwCustomerRequirement]
--sp_helptext [vwQuoteLine]
--sp_helptext [vwSalesOrderLine]
-- sp_helptext [vwStock]
--remove
--sp_helptext [vwInvoiceLine]
--sp_helptext [usp_select_InvoiceLine]
--sp_helptext [vwPurchaseOrderLine]
--sp_helptext [vwCustomerRMALine]
--sp_helptext [vwSupplierRMALine]
--sp_helptext [vwDebitLine]
--sp_helptext [usp_select_GoodsInLine]
--sp_helptext [vwGoodsInLine]
--[usp_selectAll_SalesOrderLine_for_SalesOrder]
--[usp_selectAll_SalesOrderLine_ReportPOStock]
--[usp_selectAll_SalesOrderLine_ReportPO]
--[usp_selectAll_SalesOrderLine_ReportManualStock]
--[usp_selectAll_SalesOrderLine_ReportShipped]
-- [vwPurchaseOrderLineReceiveList]
--------------------------------------

--1)--CusReqMainInfo\CusReqMainInfo.ashx.cs   |  Done
--2)--QuoteLines\QuoteLines.ashx.cs   |  Done	
--3)--SOLines\SOLines.ashx.cs   |  Done	
--4)--InvoiceLines\InvoiceLines.ashx.cs   |  Done	
--5)--POLines\POLines.ashx.cs   |  Inprogress	
--6)--CRMALines\CRMALines.ashx.cs	
--7)--SRMALines\SRMALines.ashx.cs	
--8)--DebitLines\DebitLines.ashx.cs	
--9)--GILines\GILines.ashx.cs
--------------------------------------


sp_help tbPrintDocuments
select top 1 DivisionHeaderNo,ClientNo,HeaderImageName from tbInvoice  order by InvoiceId desc --838354
declare @checkRecordExitsdiv int=0 
set @checkRecordExitsdiv=(select count(*) from tbPrintInvoice where InvoiceNo=844076 and DocType='PSP' and HeaderImageName='division_150102_20210803103909.jpg')      
if(@checkRecordExitsdiv=0)    
begin 
print @checkRecordExitsdiv
end   
--client_101_20210705173820.jpg
update tbPrintInvoice set HeaderImageName='client_101_20210705173820.jpg' where PrintInvoiceId=7
select * from printdocumentlog
SELECT DISTINCT
o.name AS Object_Name,
o.type_desc
FROM sys.sql_modules m
INNER JOIN
sys.objects o
ON m.object_id = o.object_id
WHERE m.definition Like '%NonPreferredCompany%'

--add  DocCount
--tbPrintInvoice
--PrintDocumentInvoiceId
--INV
--COC
--PSP
--CIN


Tested --------------------- Licence Number: GBOGE2020/00615 All sales are made in accordance with our Terms and Conditions of Sale.  Returns will only be accepted if accompanied by a returns authorisation number issued by Rebound Electronics (UK) Ltd.   RoHS Rebound Electronics does not undertake any design or manufacturing activities and cannot declare nor certify the chemical properties of resale components with regard to RoHS compliance. The declaration of chemical properties and RoHS compliance remains the responsibility of the component manufacturer.  For details of our Privacy Policy please refer to our website: https://www.reboundeu.com/privacy-policy/

--
--tbPrintSalesOrder

select *,  SystemDocumentFooterHistoryId from tbSystemDocumentFooterHistory   
            where SystemDocumentNo=5 and ClientNo=101 order by DLUP desc	

tbQuote-- done
select * from tbQuote order by QuoteId desc

--alter table tbSalesOrder add SysDocProFormaHistoryNo  int


tbCustomerRMA


select * from tbGlobalCountryList where GlobalCountryid=53
truncate table tbinvoicePODpdf
select count(*) from tbinvoicePODpdf 
select * from tbinvoicePODpdf 
SELECT  COUNT(*),InvoiceNo FROM tbInvoicePODPDF GROUP BY InvoiceNo HAVING COUNT(*) > 1

SELECT InvoiceId,AirWayBill from tbInvoice(nolock) where AirWayBill is not null and AirWayBill in ('1Z8W63820473001400') 
select count(*),InvoiceNo from  tbInvoicePODPDF  where InvoiceNo=834096 group by InvoiceNo

select  top 10 * from tbStock 
Pending
select * from tbCurrency
update tbInvoice set SystemDocumentFooterHistoryNo=null where InvoiceId in (843014)  --division_150117_20210705181048.jpg
select * from tbinvoice where InvoiceId in (843014)

select * from tbPrintDocumentLog
Reportlogid  identity(1,1)
ReportNo -- reportid
userid
parameter
dlup
cllientid
--Espirebch
--Espire123
select * from tb45
select * from tbReportLog order by ReportLogId desc --306
sp_help tbReportLog
sp_help tbReportLog
truncate table  tbReportLog


-- sp_helptext [usp_selectAll_ConsolidateSalesOrderLine_for_SalesOrder]

select authorisedby from tbsalesorder where salesorderid=457044
update tbsalesorder set authorisedby=1, DateAuthorised=getdate() where salesorderid=457035
select * from tbinvoice where invoiceid=844098
select top 50 * from tbinvoice where  headerimagename is not null--order by InvoiceId desc
select top 50 * from tbInvoiceEmail order by InvoiceEmailId desc
select top 10 * from tbInvoiceEmail where Invoiceno=844111 --order by InvoiceEmailId desc
select * from tbInvoiceEmail where EmailStatus='Pending' -- InvoiceEmailId in (290776)
select * from tbInvoiceEmail where  InvoiceEmailId in (290776)
update tbInvoiceEmail set  SentStatus=0,EmailStatus='Pending',AttachCOC=0,AttachPackagingSlip=1 where InvoiceEmailId=290776
select * from tbInvoiceLine where InvoiceNo=844098
select * from tbInvoice where  invoiceid=844098
update tbInvoiceLine set ECCNCode='EAR99' where InvoiceLineId=858238
select * from tbInvoiceEmail order by InvoiceEmailId desc

select top 1 *  from tbInvoiceLineAllocation where invoicelineno in (select invoicelineid from tbInvoiceLine where invoiceNo=844111)                  
select @IsDivisionheader=IsDivisionheader , @AppliedDivisionNoForWHS = ApplyDivisionHeaderNo from tbwarehouse where warehouseid=@WHSNo 

select * from tbWarehouse where WarehouseId=150063
--alter table  tbWarehouse  add [IsDivisionheader] [bit]  NULL
--ALTER TABLE [dbo].[tbWarehouse] ADD  CONSTRAINT [DF_tbWarehouse_IsDivisionheader]  DEFAULT ((0)) FOR [IsDivisionheader]
--sp_helptext vwWarehouse
--sp_helptext usp_update_Warehouse
--sp_helptext usp_select_Invoice_for_Print
--usp_select_all_Invoice_for_email
select * from tbDivision where DivisionId=3

select * from tbProduct where ProductDescription='Inductors'
--sp_helptext usp_selectAll_InvoiceLine_for_SalesOrder


select * from tbProduct where ProductDescription='PCI/PCIe Devices' like 
select * from sys.tables where name like '%tbinvoice%'
--<EMAIL>
--Sent


----------------------------------------------
select * from BorisGlobalTraderimports.dbo.tbTempStockData

select * from BorisGlobalTraderimports.dbo.tbofferstobeimported
select * from BorisGlobalTraderimports.dbo.tbexcesstobeimported
select * from BorisGlobalTraderimports.dbo.tbStockInfoDataToBeImported
select * from  BorisGlobalTraderImports.dbo.tbOffer  order by offerid desc
select top 25 * from  BorisGlobalTraderImports.dbo.tbExcess   order by Excessid desc
select top 25 * from tbStockInfoData order by stockinfodataid desc


--truncate table BorisGlobalTraderimports.dbo.tbofferstobeimported
--truncate table BorisGlobalTraderimports.dbo.tbexcesstobeimported
--truncate table BorisGlobalTraderimports.dbo.tbStockInfoDataToBeImported
--truncate table BorisGlobalTraderimports.dbo.tbTempStockData
select top 10* from tbpurchaseorder order by purchaseorderid desc
select top 10* from tbpurchaseorderline where purchaseorderno=573481
select top 10 * from tbPurchaseOrderLine





--offer import tool issue fixed sp
Use [BorisGlobalTrader]
go
sp_helptext usp_GetDynamicHeaderColumn
,cast(dbo.ufn_GetProductMessage(p.ProductId,p.IsHazardous,p.IsOrderViaIPOonly,@ClientId)as nvarchar(900))as ProductMessage 
Use [BorisGlobalTrader]
go
sp_help tbeccn
--Sp_helptext [usp_insert_ECCN]
--Sp_helptext [usp_update_ECCN]
--Sp_helptext [usp_selectAll_ECCN_for_Client]
--Sp_helptext [ufn_GetECCNMessage]
--Sp_helptext [usp_select_CustomerRequirement]
--Sp_helptext [usp_select_QuoteLine]  
--Sp_helptext [usp_select_SalesOrderLine]
--Sp_helptext [usp_select_PurchaseOrderLine]
--utrg_Import_From_tbIHSparts_To_tbECCN


update tbPurchaseOrderLine set ECCNCode='EAR99' where PurchaseOrderLineId=776536
select * from tbPurchaseOrderLine where PurchaseOrderNo=573536
select * from tbCustomerRequirement where ClientNo=114
update tbCustomerRequirement set ECCNCode='3A001.A.2.A' where CustomerRequirementId=4123936
update tbeccn set clientno=101
select * from tbECCN where ECCNCode='3A991' and ClientNo=101
select ECCNCode,Notes,ECCNStatus,Inactive,ClientNo,UpdatedBy,DLUP from tbeccn
select  distinct ECCNCode from tbIHSparts
select  *from tbeccn
--truncate table tbECCN
select * from tbECCN
select * from tbIHSparts
select * from tbProduct where ProductName like '%Adhesive Tape%'
--
select * from tbSupplierApprovalTermCondetionLog


select * from tbAddress where DivisionHeaderNo is not null
select * from tbAddress where AddressId=321308
select * from tbCustomerRequirement where customerrequirementnumber=2660099

select * from tbWarehouse
update tbAddress set DivisionHeaderNo=null

INSERT INTO tbeccn(ECCNCode)   
SELECT DISTINCT ECCNCode   
FROM tbIHSparts  
WHERE eccncode is not null and eccncode!='' and  ECCNCode NOT IN(SELECT ECCNCode FROM tbeccn); 


SELECT DISTINCT ECCNCode   
FROM tbIHSparts  where eccncode is not null

SELECT DISTINCT ECCNCode
FROM tbIHSparts where eccncode is not null and eccncode!=''

select * from tbCustomerRequirement where ECCNCode='3A001.A.5.A.4'


select * from tbLogin where LoginName like '%vinay%'
select * from tbLogin where DivisionNo=3 and clientno=101 and LoginId=4409


