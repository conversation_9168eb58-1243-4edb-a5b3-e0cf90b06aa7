  
/*    
--============================================================================================================================    
TASK   UPDATED BY       DATE    ACTION  DESCRIPTION      
[US-229094]     An.TranTan   21-Jan-2025  UPDATE  Trigger alter vwToDo for new columns added    
[US-225842]     An.TranTan   03-Apr-2025  UPDATE  Trigger alter vwToDo for column SalesOrderNo added    
[US-239019]     CuongDoX     21-Apr-2025  UPDATE  To Do List - Task Reminder Enhancement   
==============================================================================================================================      
*/  
CREATE  OR ALTER VIEW [dbo].[vwToDo]  
AS  
SELECT dbo.tbLogin.EmployeeName AS LoginName 
	,td.ToDoId
	,td.LoginNo
	,td.Subject
	,td.DateAdded
	,td.DueDate
	,td.ToDoText
	,td.Priority
	,td.IsComplete
	,td.ReminderDate
	,td.ReminderText
	,CASE   
	   WHEN td.CompanyNo IS NOT NULL THEN td.CompanyNo  
	   WHEN comq.CompanyId IS NOT NULL THEN comq.CompanyId  
	   ELSE comso.CompanyId  
	  END AS CompanyNo 
	,td.ReminderHasBeenNotified
	,td.RelatedMailMessageNo
	,td.UpdatedBy
	,td.DLUP
	,td.REFIDHK
	,td.NewRecord
	,td.HasReview
	,td.Name
	,td.ContactNo
	,td.TypeNo
	,td.Task
	,td.TaskDate
	,td.ToDoCategoryNo
	,td.QuoteNo
	,td.DailyReminder
	--,td.CustomerRequirementNo
	,td.SalesOrderNo 
    ,   CASE   
   WHEN co.CompanyId IS NOT NULL THEN co.CompanyId  
   WHEN comq.CompanyId IS NOT NULL THEN comq.CompanyId  
   ELSE comso.CompanyId  
  END AS CompanyId  
       ,case  
   when co.CompanyId is not null then  
    case  
       when ISNULL(co.SOApproved, 0) = 1  
      and ISNULL(co.POApproved, 0) = 1 then  
        co.CompanyName + ' (SO : ' + c2.CurrencyCode + ' , PO : ' + c.CurrencyCode + ')'  
       when ISNULL(co.SOApproved, 0) = 1 THEN  
        co.CompanyName + ' (SO : ' + c2.CurrencyCode + ')'  
       WHEN ISNULL(co.POApproved, 0) = 1 THEN  
        co.CompanyName + ' (PO : ' + c.CurrencyCode + ')'  
       ELSE  
        co.CompanyName + ' (No CUR)'  
    end  
   when comq.CompanyId is not null then  
    comq.CompanyName  
   else  
    comso.CompanyName  
       END as CompanyName  
FROM dbo.tbToDo  td
    LEFT OUTER JOIN dbo.tbLogin  
        ON td.LoginNo = dbo.tbLogin.LoginId  
    LEFT OUTER JOIN dbo.tbCompany co  
        on co.CompanyId = td.CompanyNo  
    left join tbCurrency c  
        on co.POCurrencyNo = c.CurrencyId  
    left join tbCurrency c2  
        on c2.CurrencyId = co.SOCurrencyNo  
 left join tbQuote q  
  on q.QuoteId = td.QuoteNo  
 left join tbCompany comq  
  on q.CompanyNo = comq.CompanyId  
 left join tbSalesOrder so  
  on so.SalesOrderId = td.SalesOrderNo  
 left join tbCompany comso  
  on comso.CompanyId= so.CompanyNo  