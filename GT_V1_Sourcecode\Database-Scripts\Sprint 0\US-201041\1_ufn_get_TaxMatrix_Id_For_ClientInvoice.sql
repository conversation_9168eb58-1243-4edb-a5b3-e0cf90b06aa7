/****** Object:  UserDefinedFunction [dbo].[ufn_get_TaxMatrix_Id_For_ClientInvoice]    Script Date: 4/24/2024 3:28:51 PM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF object_id('ufn_get_TaxMatrix_Id_For_ClientInvoice', 'FN') IS NOT NULL
BEGIN
    DROP FUNCTION [dbo].[ufn_get_TaxMatrix_Id_For_ClientInvoice]
END
GO
/*
-- ==========================================================================================
-- TASK      	UPDATED BY     			DATE         ACTION 			DESCRIPTION                                    
-- RP-2765 		Abhinav <PERSON>xena     		19-12-2023   Create			For RP-2765
-- US-201041 	Hau Nguyen Hoang Trung  26-04-2024   Update			For RP-3002 in Jira and US 201041 in AzureDevops
-- ==========================================================================================
*/

CREATE FUNCTION [dbo].[ufn_get_TaxMatrix_Id_For_ClientInvoice] ( @GoodsInNo int )
RETURNS int
AS
BEGIN 
 
    DECLARE @TaxMatrixId INT, @ClientNo INT, @GlobalCountryNo INT
    
    SELECT TOP 1 @GlobalCountryNo = co.GlobalCountryNo, @ClientNo = gi.ClientNo
    FROM tbGoodsIn gi
    JOIN tbWarehouse w ON gi.WarehouseNo = w.WarehouseId
    JOIN tbAddress a ON w.AddressNo = a.AddressId
    JOIN tbCountry co ON a.CountryNo = co.CountryId
    WHERE gi.GoodsInId = @GoodsInNo
    
    IF (@ClientNo IN (101, 117, 108, 107))
    BEGIN
        SELECT @TaxMatrixId = ISNULL(TaxMatrixId, 0)
        FROM tbTaxMatrix
        WHERE ClientNo = @ClientNo AND GlobalCountryNo = @GlobalCountryNo
    END
    ELSE
    BEGIN
        SELECT @TaxMatrixId = ISNULL(TaxMatrixId, 0)
        FROM tbTaxMatrix
        WHERE ClientNo = @ClientNo
    END
    
    RETURN ISNULL(@TaxMatrixId, 0)
END
GO


