//--------------------------------------------------------------------------------------------------------
// RP 21.12.2009:
// - cache data
// - allow System Documents to be added to dropdown (e.g. 'New Quote');
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CommunicationLogType : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("CommunicationLogType");
            base.ProcessRequest(context);
        }

        protected override void GetData() {
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            bool blnIncludeNewSystemDocuments = GetFormValue_Boolean("IncludeNewSystemDocuments");
            string ddlLogTypeId = Convert.ToString(Rebound.GlobalTrader.Site.Site.GetInstance().GetDropDown("CommunicationLogType").ID);
            Functions.ClearAllCacheByDropDown(ddlLogTypeId);
            string strCacheOptions = CacheManager.SerializeOptions(new object[] { blnIncludeNewSystemDocuments });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            if (string.IsNullOrEmpty(strCachedData)) {
                if (blnIncludeNewSystemDocuments) {
                    List<BLL.CommunicationLogType> lst = BLL.CommunicationLogType.DropDown();
                    for (int i = 0; i < lst.Count; i++) {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lst[i].CommunicationLogTypeId);
                        jsnItem.AddVariable("Name", lst[i].Name);
                        jsnItem.AddVariable("Type", "LOGTYPE");
                        jsnList.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    lst.Clear(); lst = null;
                    IEnumerator en = Enum.GetValues(typeof(BLL.SystemDocument.ListForSequencer)).GetEnumerator();
                    while (en.MoveNext()) {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", Convert.ToInt32(en.Current));
                        jsnItem.AddVariable("Name", Functions.GetGlobalResource("Misc", string.Format("New{0}", en.Current.ToString())));
                        jsnItem.AddVariable("Type", "SYSDOC");
                        jsnList.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                } else {
                    List<BLL.CommunicationLogType> lst = BLL.CommunicationLogType.DropDown();
                    for (int i = 0; i < lst.Count; i++) {
                        JsonObject jsnItem = new JsonObject();
                        jsnItem.AddVariable("ID", lst[i].CommunicationLogTypeId);
                        jsnItem.AddVariable("Name", lst[i].Name);
                        jsnList.AddVariable(jsnItem);
                        jsnItem.Dispose(); jsnItem = null;
                    }
                    lst.Clear(); lst = null;
                }
                jsn.AddVariable("Types", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            } else {
                _context.Response.Write(strCachedData);
            }
        }
    }
}
