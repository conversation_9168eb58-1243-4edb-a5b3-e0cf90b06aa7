<%@ Control Language="C#" CodeBehind="CompanyManufacturers_View.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CompanyManufacturers_View" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	<%--<Explanation><%=Functions.GetGlobalResource("FormExplanations", "ManufacturerSuppliers_View")%></Explanation>--%>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField ID="ctlManufacturerSelected" FieldID="lblManufacturerSelected" runat="server" ResourceTitle="Manufacturer" >
				<Field>
					<asp:Label ID="lblManufacturerSelected" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlPOLine" runat="server" FieldID="ctlPOLineCount" ResourceTitle="POLineCount">
				<Field>
					<asp:Label ID="ctlPOLineCount" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlRating" runat="server" FieldID="ctlStarRating" ResourceTitle="Rating">
				<Field>
					<ReboundUI:StarRating ID="ctlStarRating" runat="server" />
				</Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlFranchise" runat="server" FieldID="chkFranchise" ResourceTitle="Franchise">
				<Field><ReboundUI:ImageCheckBox ID="chkFranchise" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
