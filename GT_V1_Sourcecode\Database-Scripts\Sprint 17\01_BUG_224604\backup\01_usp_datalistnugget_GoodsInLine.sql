﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_datalistnugget_GoodsInLine]                    
                            
  @ClientId INT                      
, @OrderBy INT = 1                      
, @SortDir INT = 1                      
, @PageIndex INT = 0                      
, @PageSize INT = 10                      
, @PartSearch NVARCHAR(50) = NULL                      
, @CMSearch NVARCHAR(50) = NULL                      
, @ReceivedBy INT = NULL                      
, @AirWayBill NVARCHAR(50) = NULL                      
, @IncludeInvoiced BIT = 0                      
, @PurchaseOrderNoLo INT = NULL                      
, @PurchaseOrderNoHi INT = NULL                      
, @GoodsInNoLo INT = NULL                      
, @GoodsInNoHi INT = NULL                      
, @DateReceivedFrom DATETIME = NULL                      
, @DateReceivedTo DATETIME = NULL                      
, @SupplierInvoice NVARCHAR(50) = NULL                      
, @Reference NVARCHAR(50) = NULL                      
, @RecentOnly BIT = 1                      
, @UninspectedOnly BIT = 0                      
, @ClientSearch INT = NULL                      
, @IsPoHub INT = 0                  
, @IsGlobalLogin BIT = 0                  
, @WarehouseNo INT = NULL    
, @AS6081 BIT = NULL --[003]    
 WITH RECOMPILE                      
AS            
-- [003] Ravi          19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages                
 DECLARE @RecentDate DATETIME                      
   ,@StartPage INT                      
   ,@EndPage INT                      
 SET @RecentDate = dbo.ufn_get_date_from_datetime(DATEADD(mm, -3, GETDATE()))                      
 SET @StartPage = (@PageIndex * @PageSize + 1)                      
 SET @EndPage = ((@PageIndex + 1) * @PageSize)                      
                      
 -- semi-colon needed for WITH                          
  IF @IsPoHub = 0                         
  BEGIN                                           
  SELECT                      
      *                    
      ,ROW_NUMBER() OVER (ORDER BY --                                            
      CASE                      
    WHEN @OrderBy = 1 AND                      
     @SortDir = 2 THEN GoodsInNumber                      
END DESC                      
      , CASE                      
    WHEN @OrderBy = 1 THEN GoodsInNumber                      
      END                      
      , CASE                      
    WHEN @OrderBy = 2 AND                      
     @SortDir = 2 THEN Part                      
      END DESC                      
      , CASE                      
WHEN @OrderBy = 2 THEN Part                      
      END                      
      , CASE                      
    WHEN @OrderBy = 3 AND                      
     @SortDir = 2 THEN Quantity                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 3 THEN Quantity                      
      END                      
 , CASE                      
    WHEN @OrderBy = 4 AND                      
     @SortDir = 2 THEN CompanyName                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 4 THEN CompanyName                      
      END                      
      , CASE                      
    WHEN @OrderBy = 5 AND                      
     @SortDir = 2 THEN DateReceived                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 5 THEN DateReceived                      
      END                      
      , CASE                      
    WHEN @OrderBy = 6 AND                      
     @SortDir = 2 THEN DeliveryDate                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 6 THEN DeliveryDate  
      END) AS RowNum                      
    into #tempStock   FROM (                  
                    
  SELECT DISTINCT                      
    gi.GoodsInId                   
    ,gi.GoodsInNumber                      
    ,gil.Part                      
    ,gil.ManufacturerNo                      
    ,mf.ManufacturerCode        
    ,gil.Quantity                      
    ,gi.DateReceived                      
    ,lg.EmployeeName AS ReceiverName                      
    ,ISNULL(gi.PurchaseOrderNo, po.PurchaseOrderId) AS PurchaseOrderNo                      
    ,pol.DeliveryDate                      
    ,po.PurchaseOrderNumber            
    ,ipo.InternalPurchaseOrderId                      
    ,ipo.InternalPurchaseOrderNumber                      
    ,gil.ROHS                      
    ,gi.CompanyNo                      
    ,co.CompanyName                      
    ,gi.AirWayBill                      
    ,c.ClientId AS ClientNo                      
    ,c.ClientName                      
    ,ipo.CompanyNo AS IPOSupplier                      
    ,ipoco.CompanyName AS IPOSupplierName                 
 --,dbo.ufn_get_goodsIn_statusNo(gi.GoodsInId)      AS StatusNo                            
 ,dbo.ufn_get_stock_statusNo(sk.StockId)      AS StatusNo             
 , dbo.ufn_GetGoodInLineMessage(gil.GoodsInLineId) as      GoodInLineMessage             
 ,(CASE WHEN (SELECT COUNT(1) AS QueryCounter FROM tbGI_QueryMessageApprovals aa Where aa.GILineNo=gil.GoodsInLineId)>0 THEN 1 ELSE 0 END) AS QueryRaised           
  , gil.GoodsInLineId    
  -- , ISNULL(gi.AS6081,0) AS AS6081 -- [003] (Uncomment this line once you alter tbGI and add AS6081 column)    
      FROM tbGoodsIn gi   JOIN tbGoodsInLine gil  ON gil.GoodsInNo = gi.GoodsInId                      
      LEFT JOIN tbManufacturer mf    ON mf.ManufacturerId = gil.ManufacturerNo                      
      LEFT JOIN tbLogin lg   ON lg.LoginId = gi.ReceivedBy                      
      LEFT JOIN tbStock sk  ON gil.GoodsInLineId = sk.GoodsInLineNo                      
      LEFT JOIN tbPurchaseOrderLine pol ON sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId                      
      LEFT JOIN tbPurchaseOrder po  ON pol.PurchaseOrderNo = po.PurchaseOrderId                      
      LEFT JOIN tbInternalPurchaseOrder ipo  ON ipo.PurchaseOrderNo = gi.PurchaseOrderNo                      
      LEFT JOIN tbCompany co  ON co.CompanyId = gi.ClientCompanyNo -- GA                      
      LEFT JOIN tbClient c   ON c.ClientId = gi.ClientNo                      
      LEFT JOIN dbo.tbCompany ipoco ON ipo.CompanyNo = ipoco.CompanyId                                          
    WHERE  (( @IsGlobalLogin=1 ) OR (@IsGlobalLogin=0 AND gi.ClientNo = @ClientId ))                                           
      AND ((@RecentOnly = 0)  OR (@RecentOnly = 1 AND gi.DateReceived >= @RecentDate))                      
      AND ((@GoodsInNoLo IS NULL) OR (NOT @GoodsInNoLo IS NULL  AND GoodsInNumber >= @GoodsInNoLo))                      
      AND ((@GoodsInNoHi IS NULL)  OR (NOT @GoodsInNoHi IS NULL   AND GoodsInNumber <= @GoodsInNoHi))                      
      AND ((@DateReceivedFrom IS NULL)  OR (NOT @DateReceivedFrom IS NULL  AND gi.DateReceived >= @DateReceivedFrom))                      
      AND ((@DateReceivedTo IS NULL)  OR (NOT @DateReceivedTo IS NULL   AND gi.DateReceived <= @DateReceivedTo))                      
      AND ((@ReceivedBy IS NULL)  OR (NOT @ReceivedBy IS NULL   AND ReceivedBy = @ReceivedBy))                  
      AND ((@PurchaseOrderNoLo IS NULL)   OR (NOT @PurchaseOrderNoLo IS NULL  AND po.PurchaseOrderNumber >= @PurchaseOrderNoLo))                      
      AND ((@PurchaseOrderNoHi IS NULL) OR (NOT @PurchaseOrderNoHi IS NULL  AND po.PurchaseOrderNumber <= @PurchaseOrderNoHi))                      
      AND ((@CMSearch IS NULL)  OR (NOT @CMSearch IS NULL  AND co.FullName LIKE @CMSearch))                      
      AND ((@PartSearch IS NULL) OR (NOT @PartSearch IS NULL   AND (gil.FullPart LIKE @PartSearch  OR gil.FullSupplierPart LIKE @PartSearch)))                      
      AND ((@SupplierInvoice IS NULL)  OR (NOT @SupplierInvoice IS NULL AND SupplierInvoice LIKE @SupplierInvoice))                      
      AND ((@Reference IS NULL)  OR (NOT @Reference IS NULL  AND gi.Reference LIKE @Reference))                      
      AND ((@AirWayBill IS NULL)  OR (NOT @AirWayBill IS NULL  AND AirWayBill LIKE @AirWayBill))                      
      AND ((@IncludeInvoiced = 1)  OR (@IncludeInvoiced = 0  AND gi.InvoiceDate IS NULL))                      
      AND ((@UninspectedOnly = 0)  OR (@UninspectedOnly = 1   AND gil.DateInspected IS NULL))                      
    AND ((@ClientSearch IS NULL) OR (NOT @ClientSearch IS NULL AND gi.ClientNo = @ClientSearch))                   
    --warehouse filter                  
    AND ((@WarehouseNo IS NULL) OR (NOT @WarehouseNo IS NULL AND gi.warehouseno = @WarehouseNo))      
 --AND ((@AS6081 IS NULL)  OR (NOT @AS6081 IS NULL AND gi.AS6081 = @AS6081) )  -- [003] (Uncomment this line once you alter tbGI and add AS6081 column)    
 AND (  
   (@AS6081 = 1 and gi.AS6081 = 1)  
   OR  
   (@AS6081 IS NULL and (gi.AS6081 IS NULL OR gi.AS6081 = 1 or gi.AS6081 = 0))  
   OR  
   (@AS6081 = 0 and (gi.AS6081 IS NULL or gi.AS6081 = 0))  
  )  
     
      ) BASE                      
     UNION                   
                         
     SELECT                     
      *                      
      ,ROW_NUMBER() OVER (ORDER BY --                                            
      CASE                      
    WHEN @OrderBy = 1 AND                      
     @SortDir = 2 THEN GoodsInNumber                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 1 THEN GoodsInNumber                      
      END                      
      , CASE                      
    WHEN @OrderBy = 2 AND                      
     @SortDir = 2 THEN Part                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 2 THEN Part                      
      END                      
      , CASE                      
    WHEN @OrderBy = 3 AND                      
     @SortDir = 2 THEN Quantity                     
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 3 THEN Quantity                      
      END                      
      , CASE                      
    WHEN @OrderBy = 4 AND                      
     @SortDir = 2 THEN CompanyName                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 4 THEN CompanyName                      
      END                      
      , CASE                      
    WHEN @OrderBy = 5 AND                      
     @SortDir = 2 THEN DateReceived                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 5 THEN DateReceived                      
      END                      
      , CASE                      
    WHEN @OrderBy = 6 AND                      
     @SortDir = 2 THEN DeliveryDate                      
      END DESC                      
      , CASE                      
    WHEN @OrderBy = 6 THEN DeliveryDate                      
      END) AS RowNum                      
         FROM (SELECT DISTINCT                      
    Gi.GoodsInId                      
  ,Gi.GoodsInNumber                      
    ,gil.Part                      
    ,gil.ManufacturerNo                      
    ,mf.ManufacturerCode                      
    ,gil.Quantity                      
    ,Gi.DateReceived                      
    ,lg.EmployeeName AS ReceiverName                      
    ,ISNULL(Gi.PurchaseOrderNo, po.PurchaseOrderId) AS PurchaseOrderNo                      
    ,pol.DeliveryDate                      
    ,po.PurchaseOrderNumber                      
    ,ipo.InternalPurchaseOrderId               
    ,ipo.InternalPurchaseOrderNumber                      
    ,gil.ROHS                      
    ,Gi.CompanyNo                      
    ,co.CompanyName                      
    ,Gi.AirWayBill                      
,c.ClientId AS ClientNo                      
    ,c.ClientName                      
    ,ipo.CompanyNo AS IPOSupplier                      
    ,ipoco.CompanyName AS IPOSupplierName                   
 --,dbo.ufn_get_goodsIn_statusNo(gi.GoodsInId)      AS StatusNo                          
 ,dbo.ufn_get_stock_statusNo(sk.StockId)      AS StatusNo              
 , dbo.ufn_GetGoodInLineMessage(gil.GoodsInLineId) as      GoodInLineMessage            
 , (CASE WHEN (SELECT COUNT(1) AS QueryCounter FROM tbGI_QueryMessageApprovals aa Where aa.GILineNo=gil.GoodsInLineId)>0 THEN 1 ELSE 0 END) AS QueryRaised            
 , gil.GoodsInLineId      
 -- , ISNULL(gi.AS6081,0) AS AS6081 -- [003] (Uncomment this line once you alter tbGI and add AS6081 column)    
      FROM tbSupplierInvoiceLine SIL                      
      JOIN tbSupplierInvoice si                 
    ON si.SupplierInvoiceId = SIL.SupplierInvoiceNo                      
      LEFT JOIN tbGoodsIn Gi                      
    ON SIL.GoodsInNo = Gi.GoodsInId                      
      JOIN tbGoodsInLine gil                      
    ON gil.GoodsInNo = Gi.GoodsInId                      
      LEFT JOIN tbManufacturer mf                      
    ON mf.ManufacturerId = gil.ManufacturerNo                      
      LEFT JOIN tbLogin lg                      
    ON lg.LoginId = Gi.ReceivedBy                      
      LEFT JOIN tbStock sk                      
    ON gil.GoodsInLineId = sk.GoodsInLineNo                      
      LEFT JOIN tbPurchaseOrderLine pol                      
    ON sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId                      
      LEFT JOIN tbPurchaseOrder po                      
    ON pol.PurchaseOrderNo = po.PurchaseOrderId                      
      LEFT JOIN tbInternalPurchaseOrder ipo                      
    ON ipo.PurchaseOrderNo = gi.PurchaseOrderNo                      
      LEFT JOIN tbCompany co                      
    ON co.CompanyId = Gi.CompanyNo                      
      LEFT JOIN tbClient c                      
    ON c.ClientId = Gi.ClientNo                      
     LEFT JOIN dbo.tbCompany ipoco ON ipo.CompanyNo = ipoco.CompanyId                      
      WHERE (@SupplierInvoice IS NOT NULL)                      
      AND (    (( @IsGlobalLogin=1 ) OR (@IsGlobalLogin=0 AND gi.ClientNo = @ClientId ))                    
      AND ((@ClientSearch IS NULL) OR (NOT @ClientSearch IS NULL AND gi.ClientNo = @ClientSearch))                                      
      AND ((@RecentOnly = 0)                      
      OR (@RecentOnly = 1                      
      AND Gi.DateReceived >= @RecentDate))                      
      AND ((@GoodsInNoLo IS NULL)                      
      OR (NOT @GoodsInNoLo IS NULL                      
      AND GoodsInNumber >= @GoodsInNoLo))                      
      AND ((@GoodsInNoHi IS NULL)                      
      OR (NOT @GoodsInNoHi IS NULL                      
      AND GoodsInNumber <= @GoodsInNoHi))                      
      AND ((@DateReceivedFrom IS NULL)                      
      OR (NOT @DateReceivedFrom IS NULL                      
      AND Gi.DateReceived >= @DateReceivedFrom))                      
      AND ((@DateReceivedTo IS NULL)                      
      OR (NOT @DateReceivedTo IS NULL                      
      AND Gi.DateReceived <= @DateReceivedTo))                      
     AND ((@ReceivedBy IS NULL)                      
      OR (NOT @ReceivedBy IS NULL            
      AND ReceivedBy = @ReceivedBy))                      
      AND ((@PurchaseOrderNoLo IS NULL)                      
      OR (NOT @PurchaseOrderNoLo IS NULL                      
      AND po.PurchaseOrderNumber >= @PurchaseOrderNoLo))                      
      AND ((@PurchaseOrderNoHi IS NULL)                      
   OR (NOT @PurchaseOrderNoHi IS NULL                      
      AND po.PurchaseOrderNumber <= @PurchaseOrderNoHi))                      
      AND ((@CMSearch IS NULL)                      
      OR (NOT @CMSearch IS NULL                      
      AND co.FullName LIKE @CMSearch))                      
      AND ((@PartSearch IS NULL)                      
      OR (NOT @PartSearch IS NULL                      
      AND (gil.FullPart LIKE @PartSearch                      
      OR gil.FullSupplierPart LIKE @PartSearch)))                      
      AND ((@SupplierInvoice IS NULL)                      
      OR (NOT @SupplierInvoice IS NULL                      
      AND SupplierInvoiceNumber LIKE @SupplierInvoice))                      
      AND ((@Reference IS NULL)                      
      OR (NOT @Reference IS NULL                      
      AND Gi.Reference LIKE @Reference))                      
      AND ((@AirWayBill IS NULL)      
      OR (NOT @AirWayBill IS NULL                      
      AND AirWayBill LIKE @AirWayBill))                      
      AND ((@IncludeInvoiced = 1)                      
      OR (@IncludeInvoiced = 0                      
      AND Gi.InvoiceDate IS NULL))                      
      AND ((@UninspectedOnly = 0)                      
      OR (@UninspectedOnly = 1                      
      AND gil.DateInspected IS NULL))                  
   --warehouse filter                  
   AND ((@warehouseno IS NULL) OR (NOT @warehouseno IS NULL AND gi.warehouseno = @warehouseno))       
   --AND ((@AS6081 IS NULL)  OR (NOT @AS6081 IS NULL AND gi.AS6081 = @AS6081))  -- [003] (Uncomment this line once you alter tbGI and add AS6081 column)    
    AND (  
   (@AS6081 = 1 and gi.AS6081 = 1)  
   OR  
   (@AS6081 IS NULL and (gi.AS6081 IS NULL OR gi.AS6081 = 1 or gi.AS6081 = 0))  
   OR  
   (@AS6081 = 0 and (gi.AS6081 IS NULL or gi.AS6081 = 0))  
  )  
    )                      
     ) BASE2                  
                        
                         
                         
    SELECT                      
     *                      
     ,(SELECT                      
    COUNT(*)                      
      FROM #tempStock)                      
     AS RowCnt    ,              
  dbo.ufn_GetSupplierMessage(CompanyNo) as      SupplierMessage                   
    FROM #tempStock                      
    WHERE RowNum BETWEEN @StartPage AND @EndPage                    
    ORDER BY RowNum                      
     drop table #tempStock                     
 END                      
                       
                       
 ELSE                      
 BEGIN                      
 SELECT                      
   *                      
   ,ROW_NUMBER() OVER (ORDER BY --                                            
   CASE                      
    WHEN @OrderBy = 1 AND                      
     @SortDir = 2 THEN GoodsInNumber                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 1 THEN GoodsInNumber                      
   END                      
   , CASE                      
    WHEN @OrderBy = 2 AND                      
     @SortDir = 2 THEN Part                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 2 THEN Part                      
   END                      
   , CASE                      
    WHEN @OrderBy = 3 AND                      
     @SortDir = 2 THEN Quantity               
   END DESC                      
   , CASE  
    WHEN @OrderBy = 3 THEN Quantity                      
   END                      
   , CASE                      
    WHEN @OrderBy = 4 AND                      
     @SortDir = 2 THEN CompanyName                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 4 THEN CompanyName                      
   END                      
   , CASE                      
    WHEN @OrderBy = 5 AND                      
     @SortDir = 2 THEN DateReceived                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 5 THEN DateReceived                      
   END                      
   , CASE   
    WHEN @OrderBy = 6 AND                      
     @SortDir = 2 THEN DeliveryDate                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 6 THEN DeliveryDate                      
   END) AS RowNum                      
 into #tempStock1 FROM (SELECT DISTINCT                      
    gi.GoodsInId                      
    ,gi.GoodsInNumber                      
    ,gil.Part                      
    ,gil.ManufacturerNo                      
    ,mf.ManufacturerCode                      
    ,gil.Quantity                      
    ,gi.DateReceived                      
    ,lg.EmployeeName AS ReceiverName                      
    ,ISNULL(gi.PurchaseOrderNo, po.PurchaseOrderId) AS PurchaseOrderNo                      
    ,pol.DeliveryDate                      
    ,po.PurchaseOrderNumber                      
    ,ipo.InternalPurchaseOrderId                      
    ,ipo.InternalPurchaseOrderNumber                      
    ,gil.ROHS                      
    ,gi.CompanyNo                      
    ,co.CompanyName                      
    ,gi.AirWayBill                      
    ,c.ClientId AS ClientNo        
    ,c.ClientName                      
    ,ipo.CompanyNo AS IPOSupplier                      
    ,ipoco.CompanyName AS IPOSupplierName                   
 --,dbo.ufn_get_goodsIn_statusNo(gi.GoodsInId)      AS StatusNo                          
 ,dbo.ufn_get_stock_statusNo(sk.StockId)      AS StatusNo             
 , dbo.ufn_GetGoodInLineMessage(gil.GoodsInLineId) as      GoodInLineMessage     
 , ISNULL(gi.AS6081,0) AS AS6081 -- [003] (Uncomment this line once you alter tbGI and add AS6081 column)    
   FROM tbGoodsIn gi                      
   JOIN tbGoodsInLine gil              
    ON gil.GoodsInNo = gi.GoodsInId                      
   LEFT JOIN tbManufacturer mf                      
    ON mf.ManufacturerId = gil.ManufacturerNo                      
   LEFT JOIN tbLogin lg                      
    ON lg.LoginId = gi.ReceivedBy                      
   LEFT JOIN tbStock sk                      
    ON gil.GoodsInLineId = sk.GoodsInLineNo                      
   LEFT JOIN tbPurchaseOrderLine pol                      
    ON sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId                      
   LEFT JOIN tbPurchaseOrder po                      
    ON pol.PurchaseOrderNo = po.PurchaseOrderId                      
    JOIN tbInternalPurchaseOrder ipo                      
    ON ipo.PurchaseOrderNo = gi.PurchaseOrderNo                      
LEFT JOIN tbCompany co                      
    ON co.CompanyId = gi.CompanyNo                      
   LEFT JOIN tbClient c                      
    ON c.ClientId = gi.ClientNo                      
    JOIN tbInternalPurchaseOrderLine tpol on pol.PurchaseOrderLineId = tpol.PurchaseOrderLineNo                       
        LEFT JOIN dbo.tbCompany ipoco ON ipo.CompanyNo = ipoco.CompanyId                                    
   WHERE  ((@RecentOnly = 0)                      
   OR (@RecentOnly = 1                      
   AND gi.DateReceived >= @RecentDate))                      
   AND ((@GoodsInNoLo IS NULL)                 
   OR (NOT @GoodsInNoLo IS NULL                 
   AND GoodsInNumber >= @GoodsInNoLo))                      
   AND ((@GoodsInNoHi IS NULL)                      
   OR (NOT @GoodsInNoHi IS NULL                      
   AND GoodsInNumber <= @GoodsInNoHi))                      
   AND ((@DateReceivedFrom IS NULL)                      
   OR (NOT @DateReceivedFrom IS NULL                      
   AND gi.DateReceived >= @DateReceivedFrom))                      
   AND ((@DateReceivedTo IS NULL)                      
   OR (NOT @DateReceivedTo IS NULL                      
   AND gi.DateReceived <= @DateReceivedTo))                      
   AND ((@ReceivedBy IS NULL)                      
   OR (NOT @ReceivedBy IS NULL                      
   AND ReceivedBy = @ReceivedBy))                      
   AND ((@PurchaseOrderNoLo IS NULL)                      
   OR (NOT @PurchaseOrderNoLo IS NULL                      
   AND po.PurchaseOrderNumber >= @PurchaseOrderNoLo))                      
   AND ((@PurchaseOrderNoHi IS NULL)                      
   OR (NOT @PurchaseOrderNoHi IS NULL                      
   AND po.PurchaseOrderNumber <= @PurchaseOrderNoHi))                      
   AND ((@CMSearch IS NULL)                      
   OR (NOT @CMSearch IS NULL                      
   AND co.FullName LIKE @CMSearch))                      
   AND ((@PartSearch IS NULL)                
   OR (NOT @PartSearch IS NULL                      
   AND (gil.FullPart LIKE @PartSearch                      
   OR gil.FullSupplierPart LIKE @PartSearch)))                      
   AND ((@SupplierInvoice IS NULL)                      
   OR (NOT @SupplierInvoice IS NULL                      
   AND SupplierInvoice LIKE @SupplierInvoice))                      
   AND ((@Reference IS NULL)                      
   OR (NOT @Reference IS NULL                      
   AND gi.Reference LIKE @Reference))                      
   AND ((@AirWayBill IS NULL)                      
   OR (NOT @AirWayBill IS NULL                      
   AND AirWayBill LIKE @AirWayBill))                      
   AND ((@IncludeInvoiced = 1)                      
   OR (@IncludeInvoiced = 0           
   AND gi.InvoiceDate IS NULL))                      
   AND ((@UninspectedOnly = 0)                      
   OR (@UninspectedOnly = 1                      
   AND gil.DateInspected IS NULL))                      
   AND ((@ClientSearch IS NULL) OR (NOT @ClientSearch IS NULL AND gi.ClientNo = @ClientSearch))                  
   --warehouse filter                  
   AND ((@warehouseno IS NULL) OR (NOT @warehouseno IS NULL AND gi.warehouseno = @warehouseno))      
   --AND ((@AS6081 IS NULL)  OR (NOT @AS6081 IS NULL AND gi.AS6081 = @AS6081))  -- [003] (Uncomment this line once you alter tbGI and add AS6081 column)    
    AND (  
   (@AS6081 = 1 and gi.AS6081 = 1)  
   OR  
   (@AS6081 IS NULL and (gi.AS6081 IS NULL OR gi.AS6081 = 1 or gi.AS6081 = 0))  
   OR  
   (@AS6081 = 0 and (gi.AS6081 IS NULL or gi.AS6081 = 0))  
  )  
   ) BASE                      
  UNION                      
                      
  SELECT                      
   *                      
   ,ROW_NUMBER() OVER (ORDER BY --                                            
   CASE                      
    WHEN @OrderBy = 1 AND                      
     @SortDir = 2 THEN GoodsInNumber                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 1 THEN GoodsInNumber                      
   END                      
   , CASE                      
    WHEN @OrderBy = 2 AND                      
     @SortDir = 2 THEN Part                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 2 THEN Part                      
   END                      
   , CASE                      
    WHEN @OrderBy = 3 AND                      
     @SortDir = 2 THEN Quantity       
   END DESC               
   , CASE                      
    WHEN @OrderBy = 3 THEN Quantity                      
   END                      
   , CASE                      
    WHEN @OrderBy = 4 AND                      
     @SortDir = 2 THEN CompanyName                      
   END DESC       
   , CASE                      
    WHEN @OrderBy = 4 THEN CompanyName                      
   END                      
   , CASE                      
    WHEN @OrderBy = 5 AND                      
     @SortDir = 2 THEN DateReceived                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 5 THEN DateReceived                      
   END                      
   , CASE                      
    WHEN @OrderBy = 6 AND                      
     @SortDir = 2 THEN DeliveryDate                      
   END DESC                      
   , CASE                      
    WHEN @OrderBy = 6 THEN DeliveryDate                      
   END) AS RowNum                      
  FROM (SELECT DISTINCT                      
    Gi.GoodsInId                      
    ,Gi.GoodsInNumber                      
    ,gil.Part                      
    ,gil.ManufacturerNo                      
    ,mf.ManufacturerCode                      
    ,gil.Quantity                      
    ,Gi.DateReceived                      
    ,lg.EmployeeName AS ReceiverName                      
    ,ISNULL(Gi.PurchaseOrderNo, po.PurchaseOrderId) AS PurchaseOrderNo                      
    ,pol.DeliveryDate                      
    ,po.PurchaseOrderNumber                      
    ,ipo.InternalPurchaseOrderId                      
    ,ipo.InternalPurchaseOrderNumber                      
    ,gil.ROHS                      
    ,Gi.CompanyNo                      
    ,co.CompanyName                      
    ,Gi.AirWayBill                      
    ,c.ClientId AS ClientNo                      
    ,c.ClientName                      
    ,ipo.CompanyNo AS IPOSupplier                      
    ,ipoco.CompanyName AS IPOSupplierName                
 --,dbo.ufn_get_goodsIn_statusNo(gi.GoodsInId)      AS StatusNo                        
 ,dbo.ufn_get_stock_statusNo(sk.StockId)      AS StatusNo             
 , dbo.ufn_GetGoodInLineMessage(gil.GoodsInLineId) as      GoodInLineMessage    
 , ISNULL(gi.AS6081,0) AS AS6081 -- [003] (Uncomment this line once you alter tbGI and add AS6081 column)    
   FROM tbSupplierInvoiceLine SIL                      
   JOIN tbSupplierInvoice si                      
    ON si.SupplierInvoiceId = SIL.SupplierInvoiceNo                      
   LEFT JOIN tbGoodsIn Gi                      
    ON SIL.GoodsInNo = Gi.GoodsInId                      
   JOIN tbGoodsInLine gil                      
    ON gil.GoodsInNo = Gi.GoodsInId                      
   LEFT JOIN tbManufacturer mf                      
    ON mf.ManufacturerId = gil.ManufacturerNo                      
   LEFT JOIN tbLogin lg                      
    ON lg.LoginId = Gi.ReceivedBy                      
   LEFT JOIN tbStock sk                      
    ON gil.GoodsInLineId = sk.GoodsInLineNo                      
   LEFT JOIN tbPurchaseOrderLine pol                      
    ON sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId                      
   LEFT JOIN tbPurchaseOrder po                      
    ON pol.PurchaseOrderNo = po.PurchaseOrderId                      
    JOIN tbInternalPurchaseOrder ipo                      
    ON ipo.PurchaseOrderNo = gi.PurchaseOrderNo                    
   LEFT JOIN tbCompany co                      
    ON co.CompanyId = Gi.CompanyNo                      
   LEFT JOIN tbClient c                      
    ON c.ClientId = Gi.ClientNo                      
     JOIN tbInternalPurchaseOrderLine tpol on pol.PurchaseOrderLineId = tpol.PurchaseOrderLineNo                     
         LEFT JOIN dbo.tbCompany ipoco ON ipo.CompanyNo = ipoco.CompanyId                       
   WHERE                       
   (@SupplierInvoice IS NOT NULL)                      
   AND ((@RecentOnly = 0)                      
   OR (@RecentOnly = 1                      
   AND ((@ClientSearch IS NULL) OR (NOT @ClientSearch IS NULL AND gi.ClientNo = @ClientSearch))                      
   AND Gi.DateReceived >= @RecentDate))                      
   AND ((@GoodsInNoLo IS NULL)                      
   OR (NOT @GoodsInNoLo IS NULL                      
   AND GoodsInNumber >= @GoodsInNoLo))                      
   AND ((@GoodsInNoHi IS NULL)                      
   OR (NOT @GoodsInNoHi IS NULL                      
   AND GoodsInNumber <= @GoodsInNoHi))                      
   AND ((@DateReceivedFrom IS NULL)                      
   OR (NOT @DateReceivedFrom IS NULL                      
   AND Gi.DateReceived >= @DateReceivedFrom))                      
   AND ((@DateReceivedTo IS NULL)                      
   OR (NOT @DateReceivedTo IS NULL                      
   AND Gi.DateReceived <= @DateReceivedTo))                      
   AND ((@ReceivedBy IS NULL)                      
   OR (NOT @ReceivedBy IS NULL                      
   AND ReceivedBy = @ReceivedBy))                      
   AND ((@PurchaseOrderNoLo IS NULL)                      
   OR (NOT @PurchaseOrderNoLo IS NULL                      
   AND po.PurchaseOrderNumber >= @PurchaseOrderNoLo))                      
   AND ((@PurchaseOrderNoHi IS NULL)                      
   OR (NOT @PurchaseOrderNoHi IS NULL                      
   AND po.PurchaseOrderNumber <= @PurchaseOrderNoHi))                      
   AND ((@CMSearch IS NULL)                      
   OR (NOT @CMSearch IS NULL                      
   AND co.FullName LIKE @CMSearch))                      
   AND ((@PartSearch IS NULL)                      
   OR (NOT @PartSearch IS NULL                      
   AND (gil.FullPart LIKE @PartSearch                      
   OR gil.FullSupplierPart LIKE @PartSearch)))                      
   AND ((@SupplierInvoice IS NULL)                      
   OR (NOT @SupplierInvoice IS NULL                      
   AND SupplierInvoiceNumber LIKE @SupplierInvoice))                      
   AND ((@Reference IS NULL)                      
   OR (NOT @Reference IS NULL                      
   AND Gi.Reference LIKE @Reference))                      
   AND ((@AirWayBill IS NULL)                      
   OR (NOT @AirWayBill IS NULL                      
   AND AirWayBill LIKE @AirWayBill))                      
   AND ((@IncludeInvoiced = 1)                      
   OR (@IncludeInvoiced = 0                      
   AND Gi.InvoiceDate IS NULL))                      
   AND ((@UninspectedOnly = 0)                      
   OR (@UninspectedOnly = 1                      
   AND gil.DateInspected IS NULL))                  
   --warehouse filter                  
   AND ((@warehouseno IS NULL) OR (NOT @warehouseno IS NULL AND gi.warehouseno = @warehouseno))     
   --AND ((@AS6081 IS NULL)  OR (NOT @AS6081 IS NULL AND gi.AS6081 = @AS6081))  -- [003] (Uncomment this line once you alter tbGI and add AS6081 column)    
    AND (  
   (@AS6081 = 1 and gi.AS6081 = 1)  
   OR  
   (@AS6081 IS NULL and (gi.AS6081 IS NULL OR gi.AS6081 = 1 or gi.AS6081 = 0))  
   OR  
   (@AS6081 = 0 and (gi.AS6081 IS NULL or gi.AS6081 = 0))  
  )  
   ) BASE2                  
                      
 SELECT                      
  *                      
  ,(SELECT                      
    COUNT(*)                      
   FROM #tempStock1)                      
  AS RowCnt  ,              
  dbo.ufn_GetSupplierMessage(CompanyNo) as      SupplierMessage                       
 FROM #tempStock1                      
 WHERE RowNum BETWEEN @StartPage AND @EndPage                      
 ORDER BY RowNum                      
                  
 drop table #tempStock1                 
END 
GO


