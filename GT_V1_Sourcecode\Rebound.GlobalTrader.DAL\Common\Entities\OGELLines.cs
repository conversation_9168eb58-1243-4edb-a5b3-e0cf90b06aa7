﻿/*
Marker     changed by      date         Remarks
[001]      Vinay           21/08/2012   ESMS Ref:54 - If SO line created from Quote line then create hyperlink from sales order to quote
[002]      Vinay           04/02/2014   CR:- Add AS9120 Requirement in GT application
[003]      Vinay           11/04/2018    [REB-11304]: CHG-570795 Hazarders product type
[004]      <PERSON><PERSON><PERSON>     18/07/2018   REB-12614 :Sales order Confirmation requirements
[005]      <PERSON><PERSON><PERSON>     03-Oct-2018  REB-12615 - More enhancement to the Sales Order
[006]      <PERSON><PERSON><PERSON>     03-Dec-2018  [REB-13584]: Link Requirement to SO Line
[006]      A<PERSON><PERSON>     21-Nov-2018  [REB-13395]: Log reasons for changes to Date Promised 
[007]      A<PERSON><PERSON><PERSON>  30-09-2021   Add new property EnhancedInspectionReq
[008]      <PERSON>     11/01/2022   (GTDP-240)  Add function to add warning message to show on all screens REQ - Quote - SO - PO related to ECCN field
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL {
	
	public class OGELLines {
		
		#region Constructors
		
		public OGELLines() { }

		#endregion

		#region Properties
		public System.Int32 SalesOrderId { get; set; }
		public System.Int32 SalesOrderNumber { get; set; }
	
		public System.Int32 SalesOrderLineId { get; set; }
		public System.String AirWayBill { get; set; }
		
		public System.String CommodityCode { get; set; }
		public System.String ECCNCode { get; set; }
		
		public System.String ECCNDescription { get; set; }
		public System.String PartNumber { get; set; }
		public System.String OGELNumber { get; set; }
		public System.Int32 OGEL_MilitaryUse { get; set; }
		public System.Int32 OGEL_EndDestinationCountry { get; set; }
		public System.String CountryName { get; set; }
		
		public System.Int32? TotalCount { get; set; }
		public System.Int32? RowCnt { get; set; }

		public System.DateTime DatePromised { get; set; }
		public System.Int32 CompanyNo { get; set; }
		public System.String CompanyName { get; set; }
		public System.String CustomerPO { get; set; }
		public System.Int32? Status { get; set; }
		public System.Int32 SalesOrderLineId1 { get; set; }
		public System.Int32? AllocationID { get; set; }
		public System.String CreditStatus { get; set; }
		#endregion

	}
}
