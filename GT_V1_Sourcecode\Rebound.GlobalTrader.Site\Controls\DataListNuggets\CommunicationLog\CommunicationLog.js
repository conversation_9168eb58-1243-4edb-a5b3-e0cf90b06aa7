Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.initializeBase(this,[n]);this._blnNoData=!1;this._inactive=!1};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.prototype={get_blnForContact:function(){return this._blnForContact},set_blnForContact:function(n){this._blnForContact!==n&&(this._blnForContact=n)},get_enmCompanyListType:function(){return this._enmCompanyListType},set_enmCompanyListType:function(n){this._enmCompanyListType!==n&&(this._enmCompanyListType=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.callBaseMethod(this,"initialize");this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this.addBaseControlsInitialized(Function.createDelegate(this,this.baseControlsInitialized));this._strPathToData="controls/DataListNuggets/CommunicationLog";this._strDataObject="CommunicationLog";this._table.addSelectedIndexChanged(Function.createDelegate(this,this.selectedItem));(this._ibtnAdd||this._ibtnEdit)&&(this._ibtnAdd&&$R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._ibtnEdit&&$R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._frmAddEdit=$find(this._aryFormIDs[0]),this._frmAddEdit.addCancel(Function.createDelegate(this,this.hideAddEditForm)),this._frmAddEdit.addSaveComplete(Function.createDelegate(this,this.saveAddEditComplete)),this._frmAddEdit.addSaveError(Function.createDelegate(this,this.saveAddEditError)))},baseControlsInitialized:function(){this.getFilterField("ctlContact")._ddl._intCompanyID=this._intCompanyID;this._blnForContact&&this.getFilterField("ctlContact").setValue(this._intCompanyID);this.showFilterField("ctlContact",!this._blnForContact)},initAfterBaseIsReady:function(){this.getData();this.getCompanyInactive()},dispose:function(){this.isDisposed||(this._frmAddEdit&&this._frmAddEdit.dispose(),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._blnForContact=null,this._enmCompanyListType=null,this._intCompanyID=null,this._intContactID=null,this._ibtnEdit=null,this._ibtnAdd=null,this._blnNoData=null,this._inactive=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.addParameter("ForContact",this._blnForContact);this._objData.addParameter("CompanyNo",this._intCompanyID);this._blnForContact&&this._objData.addParameter("ContactNo",this._intContactID);this._objData.addParameter("CallType",this.getFilterFieldDropDownExtraText("ctlType"));this.updateButtonsEnabledState(!1)},getDataOK:function(){var t,i;for(this._blnNoData=!0,t=0,i=this._objResult.Items.length;t<i;t++){var n=this._objResult.Items[t],r=[$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Date),$R_FN.setCleanTextValue(n.EnteredBy)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Type),n.KeyNo?$RGT_nubButton_SystemDocument(n.SysDocNo,n.KeyNo,n.SysDocNumber):n.SysDocNumber),$RGT_nubButton_Contact(n.ContactID,n.Contact,this._enmCompanyListType),$R_FN.setCleanTextValue(n.Details)],u={ContactID:n.ContactID,TypeID:n.TypeID,Frozen:n.Frozen};this._table.addRow(r,n.ID,!1,u);r=null;n=null;this._blnNoData=!0}this.showNoData(this._blnNoData)},getCompanyInactive:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCompanyDetailInactive");n.addParameter("id",this._intCompanyID);n.addDataOK(Function.createDelegate(this,this.getCompanyInactiveOK));n.addError(Function.createDelegate(this,this.getCompanyInactiveError));n.addTimeout(Function.createDelegate(this,this.getCompanyInactiveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCompanyInactiveOK:function(n){var t=n._result;this._inactive=t.Inactive;this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!t.Inactive);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!t.Inactive)},getCompanyInactiveError:function(n){this.showError(!0,n.get_ErrorMessage())},updateButtonsEnabledState:function(n){this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive&&n&&!this._table.getSelectedExtraData().Frozen)},selectedItem:function(){this._frmAddEdit._intLogItemID=this._table._varSelectedValue;this.updateButtonsEnabledState(!0)},showAddForm:function(){this._frmAddEdit.changeMode("ADD");this._frmAddEdit._intCompanyID=this._intCompanyID;this._blnForContact&&(this._frmAddEdit._intContactID=this._intContactID);this._frmAddEdit._intDataListNuggetID=this._intDataListNuggetID;this.showForm(this._frmAddEdit,!0)},showEditForm:function(){this._frmAddEdit.changeMode("EDIT");this._blnForContact&&(this._frmAddEdit._intContactID=this._intContactID);this._frmAddEdit._intCompanyID=this._intCompanyID;this._frmAddEdit._intDataListNuggetID=this._intDataListNuggetID;this.showForm(this._frmAddEdit,!0);this._frmAddEdit.setFieldValue("ctlContact",this._table.getSelectedExtraData().ContactID);this._frmAddEdit.setFieldValue("ctlLogType",this._table.getSelectedExtraData().TypeID);this._frmAddEdit.setFieldValue("ctlNotes",this._table.getSelectedCellValue(3))},hideAddEditForm:function(){this.showForm(this._frmAddEdit,!1)},saveAddEditComplete:function(){this.hideAddEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveEditComplete();this.getData()},saveAddEditError:function(){this.hideAddEditForm();this.showError(!0,this._frmAddEdit._strErrorMessage)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.CommunicationLog",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);