﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY		DATE         	ACTION 		DESCRIPTION
[US-215481]		An.TranTan		04-Nov-2024		Update		Get specific columns instead of get *
[US-215481]		An.TranTan		11-Nov-2024		Update		Get distinct pending debit note ID
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_Debit_for_Email]
AS
BEGIN
	DECLARE @IsHUBdebitNo BIT = 0
	DECLARE @POHubDebitNo INT
	DECLARE @ClientRefNo NVARCHAR(25)
	DECLARE @POHubCompanyNo INT
	DECLARE @POHubCompanyName NVARCHAR(100)
	DECLARE @ShowLinkSupCode BIT
	DECLARE @Count INT = 1;
	DECLARE @MaxCount INT = 0;

	CREATE TABLE #tempDabitList (
		DebitId INT
		,DebitNumber INT
		,ClientNo INT
		,CompanyNo INT
		,ContactNo INT
		,DebitDate DATETIME
		,CurrencyNo INT
		,RaisedBy INT
		,Buyer INT
		,Notes NVARCHAR(MAX)
		,Instructions NVARCHAR(MAX)
		,Freight FLOAT
		,DivisionNo INT
		,TaxNo INT
		,PurchaseOrderNo INT
		,SupplierRMANo INT
		,ReferenceDate DATETIME
		,SupplierInvoice NVARCHAR(MAX)
		,SupplierRMA NVARCHAR(MAX)
		,SupplierCredit NVARCHAR(MAX)
		,UpdatedBy INT
		,DLUP DATETIME
		,IncotermNo INT
		,ClientDebitNo INT
		,CompanyName NVARCHAR(MAX)
		,ContactName NVARCHAR(MAX)
		,CurrencyCode NVARCHAR(MAX)
		,CurrencyDescription NVARCHAR(MAX)
		,RaiserName NVARCHAR(MAX)
		,BuyerName NVARCHAR(MAX)
		,TeamNo INT
		,DivisionName NVARCHAR(MAX)
		,TaxName NVARCHAR(MAX)
		,PurchaseOrderNumber INT
		,PurchaseOrderDate DATETIME
		,SupplierRMANumber INT
		,DebitValue FLOAT
		,TaxRate FLOAT
		,InternalPurchaseOrderNo INT
		,InternalPurchaseOrderNumber INT
		,IPOCompanyNo INT
		,IPOCompanyName NVARCHAR(MAX)
		,ishublocked BIT
		,LinkMultiCurrencyNo INT
		,HeaderImageName NVARCHAR(350)
		,FooterText NVARCHAR(350)
		,SysDocHazardousHistoryNo INT
		,DateExported DATETIME
		,Exported BIT
		,URNNumber INT
		,CanBeExported BIT
		,CompanyTelephone NVARCHAR(MAX)
		,CompanyFax NVARCHAR(MAX)
		,ContactEmail NVARCHAR(MAX)
		,VATNo NVARCHAR(MAX)
		,SupplierCode NVARCHAR(MAX)
		,POHubCompanyNo INT
		,POHubCompanyName NVARCHAR(MAX)
		,ClientRefNo INT
		,faHeaderImageName NVARCHAR(MAX)
		,faFooterText NVARCHAR(MAX)
		,SysDocHazardousHistoryText NVARCHAR(MAX)
		)

	CREATE TABLE #tbDebitNoteNo (
		ID INT IDENTITY(1, 1)
		,DebitNoteNo INT
		)

	INSERT INTO #tbDebitNoteNo
	SELECT DISTINCT DabitNoteNo
	FROM tbDebitEmail
	WHERE SentStatus = 0
		AND EmailStatus = 'Pending'
		AND ISNULL(ContactEmail, '0') <> '0'

	SET @ShowLinkSupCode = 1
	SET @POHubDebitNo = NULL

	SELECT @MaxCount = COUNT(1)
	FROM #tbDebitNoteNo

	WHILE (@Count <= @MaxCount)
	BEGIN
		DECLARE @DebitNo INT = 0;

		SELECT @DebitNo = DebitNoteNo
		FROM #tbDebitNoteNo
		WHERE ID = @Count

		IF (
				(
					SELECT ISNULL(ClientDebitNo, 0)
					FROM tbDebit
					WHERE DebitId = @DebitNo
					) > 0
				)
		BEGIN
			SET @IsHUBdebitNo = 1;
		END
		ELSE
		BEGIN
			SET @IsHUBdebitNo = 0;
		END

		IF (@IsHUBdebitNo = 1)
		BEGIN
			SET @ShowLinkSupCode = 0

			SELECT TOP 1 @POHubDebitNo = DebitId
			FROM tbDebit
			WHERE ClientDebitNo = @DebitNo

			IF ISNULL(@POHubDebitNo, 0) <> 0
			BEGIN
				SELECT @ClientRefNo = cast(ClientNo AS VARCHAR(5)) + '-' + cast(DebitNumber AS VARCHAR(8))
				FROM tbDebit
				WHERE DebitId = @DebitNo
			END

			SELECT TOP 1 @POHubCompanyNo = CompanyNo
				,@POHubCompanyName = c.CompanyName
			FROM tbDebit d
			JOIN tbCompany c ON d.CompanyNo = c.CompanyId
			WHERE ClientDebitNo = @DebitNo
		END

		DECLARE @ParentDebitNo INT

		SET @ParentDebitNo = NULL

		SELECT TOP 1 @ParentDebitNo = isnull(DebitId, 0)
		FROM tbDebit
		WHERE ClientDebitNo = @DebitNo

		IF (
				ISNULL(@ParentDebitNo, 0) > 0
				AND @IsHUBdebitNo = 0
				)
		BEGIN
			SET @ShowLinkSupCode = 1
		END

		IF (
				ISNULL(@ParentDebitNo, 0) <= 0
				AND @IsHUBdebitNo = 0
				)
		BEGIN
			SET @ShowLinkSupCode = 0
		END

		INSERT INTO #tempDabitList
		(
			DebitId 
			,DebitNumber 
			,ClientNo 
			,CompanyNo 
			,ContactNo 
			,DebitDate 
			,CurrencyNo 
			,RaisedBy 
			,Buyer 
			,Notes 
			,Instructions 
			,Freight 
			,DivisionNo 
			,TaxNo 
			,PurchaseOrderNo 
			,SupplierRMANo 
			,ReferenceDate 
			,SupplierInvoice 
			,SupplierRMA 
			,SupplierCredit 
			,UpdatedBy 
			,DLUP 
			,IncotermNo 
			,ClientDebitNo 
			,CompanyName
			,ContactName 
			,CurrencyCode 
			,CurrencyDescription 
			,RaiserName 
			,BuyerName 
			,TeamNo 
			,DivisionName 
			,TaxName 
			,PurchaseOrderNumber 
			,PurchaseOrderDate 
			,SupplierRMANumber 
			,DebitValue 
			,TaxRate 
			,InternalPurchaseOrderNo 
			,InternalPurchaseOrderNumber 
			,IPOCompanyNo 
			,IPOCompanyName
			,ishublocked 
			,LinkMultiCurrencyNo 
			,HeaderImageName 
			,FooterText 
			,SysDocHazardousHistoryNo 
			,DateExported 
			,Exported 
			,URNNumber 
			,CanBeExported
			,CompanyTelephone 
			,CompanyFax 
			,ContactEmail 
			,VATNo 
			,SupplierCode 
			,POHubCompanyNo 
			,POHubCompanyName 
			,ClientRefNo 
			,faHeaderImageName 
			,faFooterText 
			,SysDocHazardousHistoryText 
		)
		SELECT
			db.[DebitId]
			,db.[DebitNumber]
			,db.[ClientNo]
			,db.[CompanyNo]
			,db.[ContactNo]
			,db.[DebitDate]
			,db.[CurrencyNo]
			,db.[RaisedBy]
			,db.[Buyer]
			,db.[Notes]
			,db.[Instructions]
			,db.[Freight]
			,db.[DivisionNo]
			,db.[TaxNo]
			,db.[PurchaseOrderNo]
			,db.[SupplierRMANo]
			,db.[ReferenceDate]
			,db.[SupplierInvoice]
			,db.[SupplierRMA]
			,db.[SupplierCredit]
			,db.[UpdatedBy]
			,db.[DLUP]
			,db.[IncotermNo]
			,db.[ClientDebitNo]
			,db.[CompanyName]
			,db.[ContactName]
			,db.[CurrencyCode]
			,db.[CurrencyDescription]
			,db.[RaiserName]
			,db.[BuyerName]
			,db.[TeamNo]
			,db.[DivisionName]
			,db.[TaxName]
			,db.[PurchaseOrderNumber]
			,db.[PurchaseOrderDate]
			,db.[SupplierRMANumber]
			,db.[DebitValue]
			,db.[TaxRate]
			,db.[InternalPurchaseOrderNo]
			,db.[InternalPurchaseOrderNumber]
			,db.[IPOCompanyNo]
			,db.[IPOCompanyName]
			,db.[ishublocked]
			,db.[LinkMultiCurrencyNo]
			,db.[HeaderImageName]
			,db.[FooterText]
			,db.[SysDocHazardousHistoryNo]
			,db.[DateExported]
			,db.[Exported]
			,db.[URNNumber]
			,db.[CanBeExported]
			,co.Telephone AS CompanyTelephone
			,co.Fax AS CompanyFax
			,cn.Email AS ContactEmail
			,co.Tax AS VATNo
			,CASE 
				WHEN @ShowLinkSupCode = 1
					THEN lm.SupplierCode
				ELSE co.SupplierCode
				END AS SupplierCode
			--   , db.ClientCompanyNo         
			--, db.ClientContactNo        
			,@POHubCompanyNo AS POHubCompanyNo
			,@POHubCompanyName AS POHubCompanyName
			,@ClientRefNo AS ClientRefNo
			,db.HeaderImageName
			,db.FooterText
			,(
				SELECT FooterText
				FROM tbSystemDocumentFooterHistory FTH
				WHERE FTH.SystemDocumentFooterHistoryId = db.SysDocHazardousHistoryNo
				) AS SysDocHazardousHistoryText
		FROM vwDebit db
		LEFT JOIN tbCompany co ON db.CompanyNo = co.CompanyId
		LEFT JOIN tbContact cn ON db.ContactNo = cn.ContactId
		LEFT JOIN tbLinkMultiCurrency lm ON lm.LinkMultiCurrencyId = db.LinkMultiCurrencyNo
		WHERE DebitId = isnull(@POHubDebitNo, @DebitNo)

		SET @Count = @Count + 1;
	END

	SELECT *
	FROM #tempDabitList

	DROP TABLE #tempDabitList

	DROP TABLE #tbDebitNoteNo
END
GO


