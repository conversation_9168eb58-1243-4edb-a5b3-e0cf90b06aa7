using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Forms;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.FormFieldCollections {
	[DefaultProperty("")]
	[ToolboxData("<{0}:ToDo runat=server></{0}:ToDo>")]
	public class ToDo : Base, INamingContainer {

		#region Locals

		private Table _tbl;
		private FormField _ctlSubject;
		private FormField _ctlText;
		private FormField _ctlDueDate;
		private FormField _ctlDueTime;
		private FormField _ctlReminder;
		private FormField _ctlReminderDate;
		private FormField _ctlReminderTime;
		private FormField _ctlReminderText;
		private FormField _ctlDailyReminder;

		private FormField _ctlToDoListType;
        private FormField _ctlReview;
        private FormField _ctlContact;
        #endregion

        #region Properties
        private string _strTitleResource = "Title";
		public string TitleResource {
			get { return _strTitleResource; }
			set { _strTitleResource = value; }
		}

		#endregion

		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			AddScriptReference("Controls.FormFieldCollections.ToDo.ToDo");
			RemoveCSSClass = true;
		}

		protected override void OnLoad(EventArgs e) {
			_tbl = ControlBuilders.CreateTable();
			_tbl.Width = Unit.Percentage(100);

            //Customer
            //_ctlCustomer = new FormField();
            //_ctlCustomer.ID = "ctlCustomer";
            //_ctlCustomer.ResourceTitle = "Customer";
            //_ctlCustomer.FieldID = "ddlCustomer";
            ////_ctlCustomer.DisplayRequiredFieldMarkerOnly = true;//display required field markers (without including field in auto-validation checks)
            //Controls.DropDowns.Base ddlCustomer = DropDownManager.GetDropDown("Rebound.GlobalTrader.Site", "Company");
            //ddlCustomer.ID = "ddlCustomer";
            //_ctlCustomer.AddFieldControl(ddlCustomer);
            //_tbl.Rows.Add(_ctlCustomer);

            //To
            //_ctlCompany = new FormField();
            //_ctlCompany.ID = "ctlCompany";
            //_ctlCompany.ResourceTitle = "Company";
            //_ctlCompany.FieldID = "lblSelected";
            ////_ctlCompany.IsRequiredField = true;
            //_pnlSelected = new Panel();
            //_pnlSelected.ID = "pnlSelected";
            //_lblSelected = ControlBuilders.CreateLabelInsideParent(_pnlSelected);
            //_lblSelected.ID = "lblSelected";
            //_ctlCompany.AddFieldControl(_pnlSelected);
            //ReboundTextBox txtTo = new ReboundTextBox();
            //txtTo.ID = "txtCompany";
            //txtTo.Width = 450;
            //txtTo.Attributes.Add("placeholder", "Search Company");
            //_ctlCompany.AddFieldControl(txtTo);
            ////[001] Start Here
            //_aut = new AutoSearch.AllCompanies();
            ////[001]  End Here
            //_aut.ID = "autCompany";
            //_aut.RelatedTextBoxID = "txtCompany";
            //_aut.CharactersToEnterBeforeSearch = 1;
            //_aut.ResultsActionType = Rebound.GlobalTrader.Site.Controls.AutoSearch.Base.AutoSearchResultsActionType.RaiseEvent;
            //_aut.Width = 250;
            //_aut.ResultsHeight = 200;
            //_ctlCompany.AddFieldControl(_aut);
            //_tbl.Rows.Add(_ctlCompany);

            //Type
            _ctlToDoListType = new FormField();
            _ctlToDoListType.ID = "ctlToDoListType";
            _ctlToDoListType.ResourceTitle = "Type";
            _ctlToDoListType.FieldID = "ddlToDoListType";
            //_ctlCustomer.DisplayRequiredFieldMarkerOnly = true;//display required field markers (without including field in auto-validation checks)
            Controls.DropDowns.Base ddlCustomer = DropDownManager.GetDropDown("Rebound.GlobalTrader.Site", "ToDoListType");
            ddlCustomer.ID = "ddlToDoListType";
            _ctlToDoListType.AddFieldControl(ddlCustomer);
            _tbl.Rows.Add(_ctlToDoListType);

            //Contact
            //_ctlContact = new FormField();
            //_ctlContact.ID = "ctlContact";
            //_ctlContact.ResourceTitle = "Contact";
            //_ctlContact.FieldID = "ddlContact";
            //_ctlContact.Visible = false;
            ////_ctlCustomer.DisplayRequiredFieldMarkerOnly = true;//display required field markers (without including field in auto-validation checks)
            //Controls.DropDowns.Base ddlContact = DropDownManager.GetDropDown("Rebound.GlobalTrader.Site", "ContactsForCompany");
            //ddlContact.ID = "ddlContact";
            //_ctlContact.AddFieldControl(ddlContact);
            //_tbl.Rows.Add(_ctlContact);

            //Subject
            _ctlSubject = new FormField();
			_ctlSubject.ID = "ctlSubject";
			_ctlSubject.IsRequiredField = true;
			_ctlSubject.ResourceTitle = _strTitleResource;
			_ctlSubject.FieldID = "txtSubject";
			ReboundTextBox txtSubject = new ReboundTextBox();
			txtSubject.ID = "txtSubject";
			txtSubject.Width = 400;
			_ctlSubject.AddFieldControl(txtSubject);
			_tbl.Rows.Add(_ctlSubject);

			//Text
			_ctlText = new FormField();
			_ctlText.IsRequiredField = true;
			_ctlText.ID = "ctlText";
			_ctlText.ResourceTitle = "Text";
			_ctlText.FieldID = "txtText";
			ReboundTextBox txtText = new ReboundTextBox();
			txtText.TextMode = TextBoxMode.MultiLine;
			txtText.Rows = 4;
			txtText.ID = "txtText";
			txtText.Width = 450;
			txtText.Attributes["maxlength"] = "2000";
			_ctlText.AddFieldControl(txtText);
			_tbl.Rows.Add(_ctlText);

			//DueDate
			_ctlDueDate = new FormField();
			_ctlDueDate.ID = "ctlDueDate";
			_ctlDueDate.ResourceTitle = "DueDate";
			_ctlDueDate.FieldID = "txtDueDate";
			ReboundTextBox txtDueDate = new ReboundTextBox();
			txtDueDate.ID = "txtDueDate";
			_ctlDueDate.AddFieldControl(txtDueDate);
			Controls.Calendar calDueDate = new Controls.Calendar();
			calDueDate.ID = "calDueDate";
			calDueDate.RelatedTextBoxID = "txtDueDate";
			_ctlDueDate.AddFieldControl(calDueDate);
			_tbl.Rows.Add(_ctlDueDate);

			//DueTime
			_ctlDueTime = new FormField();
			_ctlDueTime.ID = "ctlDueTime";
			_ctlDueTime.ResourceTitle = "DueTime";
			_ctlDueTime.FieldID = "txtDueTime";
			TimeSelect timDueTime = new TimeSelect();
			timDueTime.ID = "txtDueTime";
			_ctlDueTime.AddFieldControl(timDueTime);
			_ctlDueTime.DefaultValue = "09:00";
			_tbl.Rows.Add(_ctlDueTime);

			//Reminder
			_ctlReminder = new FormField();
			_ctlReminder.ID = "ctlReminder";
			_ctlReminder.ResourceTitle = "Reminder";
			_ctlReminder.FieldID = "chkReminder";
			ImageCheckBox chkReminder = new ImageCheckBox();
			chkReminder.ID = "chkReminder";
			chkReminder.Enabled = true;
			_ctlReminder.AddFieldControl(chkReminder);
			_tbl.Rows.Add(_ctlReminder);

            //Review
            _ctlReview = new FormField();
            _ctlReview.ID = "ctlReview";
            _ctlReview.ResourceTitle = "Review";
            _ctlReview.FieldID = "chkReview";
            ImageCheckBox chkReview = new ImageCheckBox();
            chkReview.ID = "chkReview";
            chkReview.Enabled = true;
            _ctlReview.AddFieldControl(chkReview);
            _tbl.Rows.Add(_ctlReview);

            //ReminderDate
            _ctlReminderDate = new FormField();
			_ctlReminderDate.IsRequiredField = true;
			_ctlReminderDate.ID = "ctlReminderDate";
			_ctlReminderDate.ResourceTitle = "ReminderDate";
			_ctlReminderDate.FieldID = "txtReminderDate";
			Functions.SetCSSVisibility(_ctlReminderDate, false);
			ReboundTextBox txtReminderDate = new ReboundTextBox();
			txtReminderDate.ID = "txtReminderDate";
			_ctlReminderDate.AddFieldControl(txtReminderDate);
			Controls.Calendar calReminderDate = new Controls.Calendar();
			calReminderDate.ID = "calReminderDate";
			calReminderDate.RelatedTextBoxID = "txtReminderDate";
			_ctlReminderDate.AddFieldControl(calReminderDate);
			_tbl.Rows.Add(_ctlReminderDate);

			//ReminderTime
			_ctlReminderTime = new FormField();
			_ctlReminderTime.ID = "ctlReminderTime";
			_ctlReminderTime.IsRequiredField = true;
			_ctlReminderTime.ResourceTitle = "ReminderTime";
			_ctlReminderTime.FieldID = "txtReminderTime";
			Functions.SetCSSVisibility(_ctlReminderTime, false);
			TimeSelect timReminderTime = new TimeSelect();
			timReminderTime.ID = "txtReminderTime";
			_ctlReminderTime.AddFieldControl(timReminderTime);
			_ctlReminderTime.DefaultValue = "09:00";
			_tbl.Rows.Add(_ctlReminderTime);

			//Daily Reminder
			_ctlDailyReminder = new FormField();
			_ctlDailyReminder.ID = "ctlDailyReminder";
			_ctlDailyReminder.ResourceTitle = "DailyReminder";
			_ctlDailyReminder.FieldID = "chkDailyReminder";
			Functions.SetCSSVisibility(_ctlDailyReminder, false);
			ImageCheckBox chkDailyReminder = new ImageCheckBox();
			chkDailyReminder.ID = "chkDailyReminder";
			chkDailyReminder.Enabled = true;
			_ctlDailyReminder.AddFieldControl(chkDailyReminder);
			_tbl.Rows.Add(_ctlDailyReminder);

			//ReminderText
			_ctlReminderText = new FormField();
			_ctlReminderText.IsRequiredField = true;
			_ctlReminderText.ID = "ctlReminderText";
			_ctlReminderText.ResourceTitle = "ReminderText";
			_ctlReminderText.FieldID = "txtReminderText";
			Functions.SetCSSVisibility(_ctlReminderText, false);
			ReboundTextBox txtReminderText = new ReboundTextBox();
			txtReminderText.TextMode = TextBoxMode.MultiLine;
			txtReminderText.Rows = 2;
			txtReminderText.ID = "txtReminderText";
			txtReminderText.Width = 450;
			txtReminderText.Attributes["maxlength"] = "500";

			_ctlReminderText.AddFieldControl(txtReminderText);
			_tbl.Rows.Add(_ctlReminderText);

			AddControl(_tbl);
			SetupScriptDescriptors();
			base.OnLoad(e);
		}

		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.ToDo", this.ClientID);
		}

	}
}