///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.prototype = {

	get_pnlCurrent: function() { return this._pnlCurrent; }, 	set_pnlCurrent: function(value) { if (this._pnlCurrent !== value)  this._pnlCurrent = value; }, 
	get_pnlOverdue: function() { return this._pnlOverdue; }, 	set_pnlOverdue: function(value) { if (this._pnlOverdue !== value)  this._pnlOverdue = value; }, 
	get_tblCurrent: function() { return this._tblCurrent; }, 	set_tblCurrent: function(value) { if (this._tblCurrent !== value)  this._tblCurrent = value; }, 
	get_tblOverdue: function() { return this._tblOverdue; }, 	set_tblOverdue: function(value) { if (this._tblOverdue !== value)  this._tblOverdue = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblCurrent) this._tblCurrent.dispose();
		if (this._tblOverdue) this._tblOverdue.dispose();
		this._pnlCurrent = null;
		this._pnlOverdue = null;
		this._tblCurrent = null;
		this._tblOverdue = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.callBaseMethod(this, "dispose");
	},
	
	getData: function() {
		this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/MyOpenSalesOrders");
		obj.set_DataObject("MyOpenSalesOrders");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		this.showNoneFoundOrContent(args._result.Count);
		var result = args._result;
		var aryData, row;
		//current
		this._tblCurrent.clearTable();
		this._tblCurrent.show(result.Current.length > 0);
		for (var i = 0; i < result.Current.length; i++) {
			row = result.Current[i];
			aryData = [
				$RGT_nubButton_SalesOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Due
				];
			this._tblCurrent.addRow(aryData, null);
		}
		
		//overdue
		this._tblOverdue.clearTable();
		this._tblOverdue.show(result.Overdue.length > 0);
		for (i = 0; i < result.Overdue.length; i++) {
			row = result.Overdue[i];
			aryData = [
				$RGT_nubButton_SalesOrder(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Due
				];
			this._tblOverdue.addRow(aryData, null);
		}
		this.hideLoading();
	}

};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenSalesOrders", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
