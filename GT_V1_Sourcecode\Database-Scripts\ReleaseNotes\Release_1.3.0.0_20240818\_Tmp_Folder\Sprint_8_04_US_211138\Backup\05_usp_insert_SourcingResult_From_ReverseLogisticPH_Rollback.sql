﻿
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_SourcingResult_From_ReverseLogisticPH]                          
--****************************************************************************************                  
--*  14.04.2023:                  
--* //[001]      Soorya Vyas    14-04-2023   [RP-1421] add revers logistic similar to strategic offer on HUBRFQ page            
--****************************************************************************************                                
    @CustomerRequirementNo int                                
  , @ReverseLogisticNo int                                
  , @UpdatedBy int                            
  , @IsClone bit = 0                            
  , @SourcingResultId int OUTPUT                       
  , @LinkCurrencyMsg varchar(150)=null OUTPUT                                 
AS                                 
    BEGIN                       
  IF (@CustomerRequirementNo <= 0)                           
       BEGIN                          
      return                       
    end                            
    SET @LinkCurrencyMsg = ''                              
     --check if we have a row already for this sourcing result                                
        IF (SELECT  count(*)                                
            FROM    dbo.tbSourcingResult                                
            WHERE   CustomerRequirementNo = @CustomerRequirementNo                                
                    AND SourcingTable = 'RLPH'                                
                    AND SourcingTableItemNo = @ReverseLogisticNo                                
           ) = 0                                 
            BEGIN                                
    DECLARE @ClientCompanyNo INT                                    
    DECLARE @CompanyNo INT                                    
    DECLARE @ClientNo INT                                    
    -- DECLARE @UPLiftPrice FLOAT                                  
    -- DECLARE @Price FLOAT                                  
    DECLARE @ClientCurrencyNo INT                           
    -- DECLARE @ClientLinkCurrencyNo INT                           
    DECLARE @HubCurrencyNo INT                            
    declare @LinkMultiCurrencyNo int                                  
                                              
    declare @ManufacturerNo int                               
    declare @ProductNo int                                
    declare @PackageNo int                               
    DECLARE @ReverseLogisticCurrencyNo INT                        
    DECLARE @BuyExchangeRate float                              
    DECLARE @HubExchangeRate float                          
    DECLARE @GlobalProductNo int                        
    DECLARE @ClientProductNo int                        
    DECLARE @HubCurrencyName varchar(20)                      
    DECLARE @ClientCode varchar(20)                        
                      
                                              
    SELECT @ClientNo = ClientNo,@ManufacturerNo=IsNULL(ManufacturerNo,0),@ProductNo=IsNULL(ProductNo,0),@PackageNo=IsNULL(PackageNo,0)                        
    FROM tbCustomerRequirement where CustomerRequirementId = @CustomerRequirementNo                                     
    SELECT TOP 1 @ClientCompanyNo = CompanyId FROM tbCompany WHERE ClientNo = @ClientNo AND IsPOHub =1                                    
                        
     -- SELECT top 1 @HubCurrencyNo = CurrencyNo FROM tbClient where isnull(IsPOHub,0) = 1                        
    -- SELECT HubCurrencyNo = dbo.ufn_get_HUB_DefaultCurrencyNo(244,101,114)                        
                            
                       
                               
    SELECT @ReverseLogisticCurrencyNo = CurrencyNo , @GlobalProductNo = isnull(p.GlobalProductNo,0) FROM [BorisGlobalTraderImports].dbo.tbReverseLogistic o left join tbProduct p on o.ProductNo = p.ProductId                          
    WHERE ReverseLogisticId = @ReverseLogisticNo                        
             
    SELECT @LinkMultiCurrencyNo = l.LinkMultiCurrencyId,@ClientCurrencyNo = l.SupplierCurrencyNo                        
    from tbCurrency c left join tbLinkMultiCurrency l   on l.GlobalCurrencyNo = c.GlobalCurrencyNo                         
        WHERE  l.ClientNo = @ClientNo and c.ClientNo = 114 AND c.CurrencyId = @ReverseLogisticCurrencyNo                        
            
            SELECT  @HubCurrencyNo = dbo.ufn_get_HUB_DefaultCurrencyNo(@ReverseLogisticCurrencyNo,@ClientNo,114)                          --Get the buy exchange rate .66                        
   SELECT @BuyExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@ReverseLogisticCurrencyNo, 0), GETDATE())                          
   --Get Hub Exchange rate                                   
   SELECT @HubExchangeRate = dbo.ufn_get_exchange_rate(ISNULL(@HubCurrencyNo, 0), GETDATE())                         
                                   
           select top 1 @ClientProductNo = ProductId from tbProduct where GlobalProductNo = @GlobalProductNo and ClientNo = @ClientNo and isnull(Inactive,0) = 0                        
                      
     IF @LinkMultiCurrencyNo IS NULL                      
     BEGIN                      
       SELECT @ClientCode = ClientCode  FROM tbclient where clientid=@ClientNo                      
    select @HubCurrencyName = CurrencyCode   from tbCurrency where CurrencyId = @ReverseLogisticCurrencyNo                      
       SET @LinkCurrencyMsg = 'Cannot use '+@HubCurrencyName+' currency for the '+@ClientCode+' client. Kindly contact administrator.';                      
    SET @SourcingResultId = 0                      
    RETURN                      
     END                      
                            
     INSERT  INTO dbo.tbSourcingResult (                                
       CustomerRequirementNo                                
     , SourcingTable                                
     , SourcingTableItemNo                                
     , FullPart                                
     , Part                                
     , ManufacturerNo                                
     , DateCode                                
     , ProductNo                                
     , PackageNo                                
     , Quantity                                
     , Price                                
     , CurrencyNo                       
     , OriginalEntryDate                                
     , Salesman                                
     , OfferStatusNo                                
     , OfferStatusChangeDate                                
     , OfferStatusChangeLoginNo                                
     , SupplierNo                                
     , UpdatedBy                                
     , DLUP                                
     , TypeName                                
     , Notes                                
     , ROHS                                
     , POHubCompanyNo                                      
     , SupplierPrice                                      
     , ClientCompanyNo                                
     , EstimatedShippingCost                                 
     , ClientCurrencyNo                                 
     , SupplierManufacturerName                                    
     , SupplierDateCode                                    
     , SupplierPackageType                                    
     , SupplierProductType                                    
     , SupplierMOQ                                    
     , SupplierTotalQSA                                    
     , SupplierLTB                                    
     , SupplierNotes                                     
     , SPQ                              
     , LeadTime                               
     , ROHSStatus                         
     , FactorySealed                       
     , MSL                         
     , Buyer                          
  , ActualPrice                        
  , ActualCurrencyNo                        
  , ExchangeRate                           
  , LinkMultiCurrencyNo                  
  , MSLLevelNo                       
  , SupplierWarranty                     
                  
    )                                
                              
                                                    
     SELECT @CustomerRequirementNo            
     , 'RLPH'                                
     , @ReverseLogisticNo                                
     , oph.FullPart                                
     , oph.Part                                
     , isnull(@ManufacturerNo, 0)                                
     , oph.DateCode                  
     , isnull(@ClientProductNo ,@ProductNo)                           
     , isnull(@PackageNo, 0)                         
  , case when @IsClone=1 then isnull(oph.Quantity, 0) else   0   end                            
     --, isnull(oph.Quantity, 0)                                
     --, isnull(@Price, 0)                             
--comment by anand  change upliftprice                 
  --, (                        
  --       (isnull(oph.Price,0) / @BuyExchangeRate)                         
  --       +  ((isnull(oph.Price,0) / @BuyExchangeRate) * ISNULL(company.UPLiftPrice,0))/100                         
  --  ) * @HubExchangeRate                      
  ,oph.UPLiftPrice                  
    -- , isnull(oph.CurrencyNo, 0)                                
 -- , @HubCurrencyNo                        
  , dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(oph.CurrencyNo,company.POCurrencyNo),@ClientNo,114)                        
     , oph.OriginalEntryDate                                
     , isnull(oph.Salesman, 0)                               
     , oph.ReverseLogisticStatusNo                                
     , oph.ReverseLogisticStatusChangeDate                                
     , oph.ReverseLogisticStatusChangeLoginNo                                
     , oph.SupplierNo                                
     , @UpdatedBy                                
     , getdate()                                
     , ''                    
     --, oph.Notes                                
  ,oph.Description          
     , oph.ROHS                                
     , oph.SupplierNo                                      
     --, isnull(oph.Price, 0)                        
  --, (isnull(oph.Price,0) / dbo.ufn_get_exchange_rate(ISNULL(oph.CurrencyNo, 0), GETDATE()))                         
    , (                        
               (isnull(oph.Price,0) / @BuyExchangeRate)                        
   )                         
     , @ClientCompanyNo                                  
     , (isnull(country.ShippingCost,0))* @HubExchangeRate                      
     , @ClientCurrencyNo                        
     , mfr.ManufacturerName                                  
     , oph.DateCode                                  
     , pk.PackageName                                  
    -- , pr.ProductName : Commented on 11 Dec 2017                          
  , pr.ProductDescription                              
     , oph.SupplierMOQ                                  
     --, oph.TotalQuantityAvailableInStock                                
     , case when ISNUMERIC(oph.SupplierTotalQSA) = 1 then oph.SupplierTotalQSA else 0 end                                
     , oph.SupplierLTB                                  
     , oph.Notes                                 
     , oph.SPQ                              
     , oph.LeadTime                              
     , oph.ROHSStatus                              
     , oph.FactorySealed                              
     , oph.MSL                         
     , @UpdatedBy                           
  , ISNULL(oph.Price, 0)                         
  , ISNULL(oph.CurrencyNo, isnull(company.POCurrencyNo,0))                      
  , dbo.ufn_get_exchange_rate(ISNULL(oph.CurrencyNo, isnull(company.POCurrencyNo,0)), GETDATE())                          
  , @LinkMultiCurrencyNo                             
  , oph.MSLLevelNo                      
  , company.SupplierWarranty               
                       
                                    
     FROM   [BorisGlobalTraderImports].dbo.tbReverseLogistic  oph                              
     LEFT JOIN dbo.tbCompany company on oph.SupplierNo = company.CompanyId                               
     LEFT JOIN dbo.tbCountry country on country.CountryId = company.DefaultPOShipCountryNo   AND company.ClientNo=country.ClientNo                                                  
     LEFT JOIN dbo.tbManufacturer mfr ON mfr.ManufacturerId=oph.ManufacturerNo                            
     LEFT JOIN dbo.tbProduct pr ON pr.ProductId=oph.ProductNo                            
     LEFT JOIN dbo.tbPackage pk ON pk.PackageId=oph.PackageNo                            
     WHERE  oph.ReverseLogisticId = @ReverseLogisticNo                                 
                                
                SET @SourcingResultId = scope_identity() ;                                
               
   --Update customer requirement                           
     UPDATE tbCustomerRequirement set HasHubSourcingResult = 1 where CustomerRequirementId = @CustomerRequirementNo                          
            END                                
        ELSE                                 
    BEGIN                            
                SET @SourcingResultId = 1 ; --spoof an OK result                                
            END                                
                                
                              
    END 
GO


