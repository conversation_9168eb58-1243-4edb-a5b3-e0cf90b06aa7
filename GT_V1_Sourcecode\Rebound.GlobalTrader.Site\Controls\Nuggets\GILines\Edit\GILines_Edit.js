Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.initializeBase(this,[n]);this._intGIID=0;this._intLineID=-1;this._intCurrencyID=-1;this._strCurrencyCode="";this._dblPOLineShipInCost=0;this._intPOQuantityOrdered=0;this._blnCanEditShipInCost=!1;this._blnCanEditPurchasePrice=!1;this._strPartNoQuery="";this._strManufacturerQuery="";this._strPackagingTypeQuery="";this._strMslQuery="";this._strRohsQuery="";this._blnRelatedToIPO=!1;this._intIPOClientNo=-1;this._poBankFee=0;this._intGlobalClientNo=-1;this._intSerialNoCount=0;this._blnSerNoRecorded=!1;this._blnProductHaza=!1;this._blnGISplited=!1;this._intPOManufacturerId=-1;this._intPOPackagingNo=-1;this._intPOROHSNo=-1;this._POMSL="";this._FieldChanges={};this._intLotNoCount=0;this._blnLotNoRecorded=!1;this._intGoodsInNumber=-1;this._tempBarcodeRemark="";this._blnBarcodeRemarkMandatory=!1;this._blnFirstTimePageLoad=!0;this._ddl_val_zero=!0;this._blnQAIncludeApproverHtml=!1;this._blnSalesDataIncludeApproverHtml=!1;this._blnPurchaseDataIncludeApproverHtml=!1;this.GIQueryId=0;this._blnC1=!1;this._blnC2=!1;this._blnC3=!1;this._blnC4=!1;this._blnC5=!1;this._blnC6=!1;this._blnC7=!1;this._blnC8=!1;this._blnC9=!1;this._blnC10=!1;this._IsSTOGi=!1;this._IsSTOKHub=!1;document.getElementById("btnSendQuery").addEventListener("click",Function.createDelegate(this,this.SendQueryMessage));document.getElementById("btnSendResponce").addEventListener("click",Function.createDelegate(this,this.SendApprovalResponce));document.getElementById("btnSaveNewApprover").addEventListener("click",Function.createDelegate(this,this.ChangeApprover));document.getElementById("btnSaveRename").addEventListener("click",Function.createDelegate(this,this.RenameCaption));document.getElementById("btnDraftQueryMessage").addEventListener("click",Function.createDelegate(this,this.DraftQueryMessage));document.getElementById("btnResponseDraftQueryMessage").addEventListener("click",Function.createDelegate(this,this.DraftQueryMessage));document.getElementById("btnBulkAttacheDelete").addEventListener("click",Function.createDelegate(this,this.BulkAttachmentDelete));$("#CorrectPartNoError1").append(`<span style="float: left; display: inline - block; clear: both; "> Max length: (30 chrs max)</span>`);this._PackBreakDownJSON=[];this._blnCanViewApproval=!1;this._blnCanViewAttachment=!1;this._isSendClicked=!1;this._GI_QueryId=0;this._NavigateFromGIInfo=!1;this._blnCanManageApproverEdit=!1;this._RefreshFieldChanges={};this._PackagingListJSON=[];this._MFRLabelListJSON=[];this._intEnhancedInspectionStatusId=-1;this._intGIlineBarcodesStatusId=-1;this._IsQueryRaised=!1;this._IsViewModeOnly=!1;this._blnCanEdit=!0;this._IsQueryMessage=!1;this._InspectedBy=0;this._intAttachmentId=0;this._strAttachmentType="";this._blnDeletePermission=!1;this.allowSaving=!0};Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.prototype={get_intGIID:function(){return this._intGIID},set_intGIID:function(n){this._intGIID!==n&&(this._intGIID=n)},get_dblPOLineShipInCost:function(){return this._dblPOLineShipInCost},set_dblPOLineShipInCost:function(n){this._dblPOLineShipInCost!==n&&(this._dblPOLineShipInCost=n)},get_intPOQuantityOrdered:function(){return this._intPOQuantityOrdered},set_intPOQuantityOrdered:function(n){this._intPOQuantityOrdered!==n&&(this._intPOQuantityOrdered=n)},get_IsPOHub:function(){return this._IsPOHub},set_IsPOHub:function(n){this._IsPOHub!==n&&(this._IsPOHub=n)},get_ibtnSendQuery:function(){return this._ibtnSendQuery},set_ibtnSendQuery:function(n){this._ibtnSendQuery!==n&&(this._ibtnSendQuery=n)},get_ibtnSaveRelease:function(){return this._ibtnSaveRelease},set_ibtnSaveRelease:function(n){this._ibtnSaveRelease!==n&&(this._ibtnSaveRelease=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this.addSave(Function.createDelegate(this,this.saveClicked));var n=Function.createDelegate(this,this.finishedForm);document.getElementById("aAttachments").addEventListener("click",Function.createDelegate(this,this.getAttachementsData));document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnSendQuery_hyp").addEventListener("click",Function.createDelegate(this,this.SaveAndSendQuery));document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").addEventListener("click",Function.createDelegate(this,this.SaveAndSendQuery));$("#btnBulkAttacheDelete").hide();document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlQualityApproved_ddl").addEventListener("change",Function.createDelegate(this,this.MadeChangeInQAddl));document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlSalesApproved_ddl").addEventListener("change",Function.createDelegate(this,this.MadeChangeInSalesddl));document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlPurchasingApproved_ddl").addEventListener("change",Function.createDelegate(this,this.MadeChangeInPurchaseddl));document.getElementById("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl").addEventListener("change",Function.createDelegate(this,this.BarcodeScannedChanged))},goClick:function(){},formShown:function(){$R_IBTN.enableButton(this._ibtnSave,!1);$R_IBTN.enableButton(this._ibtnSave_Footer,!1);$R_IBTN.enableButton(this._ibtnCancel,!1);$R_IBTN.enableButton(this._ibtnCancel_Footer,!1);SetDefaultUploadValue("1");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryPartNo").val(this._strPartNoQuery.trim().replace(/<br\s*[\/]?>/gi,"\n"));$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryManfacturer").val(this._strManufacturerQuery.trim().replace(/<br\s*[\/]?>/gi,"\n"));$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtPackageTypeQuery").val(this._strPackagingTypeQuery.trim().replace(/<br\s*[\/]?>/gi,"\n"));$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtMslQuery").val(this._strMslQuery.trim().replace(/<br\s*[\/]?>/gi,"\n"));$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtRohsQuery").val(this._strRohsQuery.trim().replace(/<br\s*[\/]?>/gi,"\n"));$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtAccountNotes").attr("maxlength","128");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtGeneralInspectionNotes").attr("maxlength","1500");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlCCUsersRequest_tdLabel").prop("colspan","6");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlCCUsers_tdLabel").prop("colspan","6");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ctl02_ctlMultiStep_ctlItem1_ctl00").click();$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryPartNo").attr("maxlength","150");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryManfacturer").attr("maxlength","150");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtPackageTypeQuery").attr("maxlength","150");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtMslQuery").attr("maxlength","150");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtRohsQuery").attr("maxlength","150");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtActeoneTest").attr("maxlength","150");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtIsopropryle").attr("maxlength","150");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryBakingLevel").attr("maxlength","50");$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").attr("maxlength","1000");$find(this.getFormControlID(this._element.id,"ddlSalesApproved")).getData();$find(this.getFormControlID(this._element.id,"ddlQualityApproved")).getData();$find(this.getFormControlID(this._element.id,"ddlPurchasingApproved")).getData();$find(this.getFormControlID(this._element.id,"ddlNewSalesApprover")).getData();$find(this.getFormControlID(this._element.id,"ddlNewPurchasingApprover")).getData();this._ctlMail=$find(this.getField("ddlCCUsers").ID);this._ctlMail._ctlRelatedForm=this;this._ctlMailRequest=$find(this.getField("ddlCCUsersRequest").ID);this._ctlMailRequest._ctlRelatedForm=this;this.storeOriginalFieldValues();this._intGlobalClientNo>0?$find(this.getFormControlID(this._element.id,"cmbProducts"))._aut._intGlobalLoginClientNo=this._intGlobalClientNo:$find(this.getFormControlID(this._element.id,"cmbProducts"))._aut._intPOHubClientNo=this._intIPOClientNo;this._IsPOHub?($("#"+this.getFormControlID(this._element.id,"lblShipInCost")).css("display","none"),$("#"+this.getFormControlID(this._element.id,"lblShipInCost")).css("display","none")):($("#"+this.getFormControlID(this._element.id,"lblShipInCost")).css("display",this._blnCanEditShipInCost==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblShipInCost")).css("display",!this._blnCanEditShipInCost==!0?"block":"none"));this._blnRelatedToIPO?($("#"+this.getFormControlID(this._element.id,"txtPrice")).css("display",(this._blnCanEditPurchasePrice&&this._IsPOHub)==!0?"":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price")).css("display",(this._blnCanEditPurchasePrice&&this._IsPOHub)==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblPrice")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none")):($("#"+this.getFormControlID(this._element.id,"txtPrice")).css("display",this._blnCanEditPurchasePrice==!0?"":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price")).css("display",this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblPrice")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none"),$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none"));$("#"+this.getFormControlID(this._element.id,"txtPrice_IPO")).css("display",(this._blnCanEditPurchasePrice&&this._blnRelatedToIPO&&!this._IsPOHub)==!0?"":"none");$("#"+this.getFormControlID(this._element.id,"lblCurrency_Price_IPO")).css("display",(this._blnCanEditPurchasePrice&&this._blnRelatedToIPO&&!this._IsPOHub)==!0?"block":"none");$("#"+this.getFormControlID(this._element.id,"lblPrice_IPO")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none");$("#"+this.getFormControlID(this._element.id,"lblCurrency_PriceLabel_IPO")).css("display",!this._blnCanEditPurchasePrice==!0?"block":"none");this._IsSTOGi==!0?($("#"+this.getFormControlID(this._element.id,"txtPrice")).prop("disabled",!0),$("#"+this.getFormControlID(this._element.id,"txtPrice_IPO")).prop("disabled",!0),$("#imgPriceinfo").show()):($("#"+this.getFormControlID(this._element.id,"txtPrice")).prop("disabled",!1),$("#"+this.getFormControlID(this._element.id,"txtPrice_IPO")).prop("disabled",!1),$("#imgPriceinfo").hide());$find(this.getFormControlID(this._element.id,"chkReqSerailNo")).enableButton(!(this._intSerialNoCount>0));$find(this.getFormControlID(this._element.id,"chkLotCodeReq")).enableButton(!(this._intLotNoCount>0));this._blnGISplited==!1?this.showHideQuantity(this._blnSerNoRecorded):this.showHideQuantity(this._blnGISplited);document.getElementById("GoodsInUpdateTypeError").style.backgroundColor="";document.getElementById("GoodsInUpdateTypeError1").style.backgroundColor="";document.getElementById("LocationError").style.backgroundColor="";document.getElementById("LocationError1").style.backgroundColor="";document.getElementById("ProductsError").style.backgroundColor="";document.getElementById("ProductsError1").style.backgroundColor="";document.getElementById("ShipInCostError").style.backgroundColor="";document.getElementById("ShipInCostError1").style.backgroundColor="";document.getElementById("tcQuantity").style.backgroundColor="";document.getElementById("QuantityError").style.backgroundColor="";document.getElementById("QuantityError1").style.backgroundColor="";this._chkPartNumberCorrect=$find(this.getFormControlID(this._element.id,"chkPartNumberCorrect"));this._chkPartNumberCorrect.addClick(Function.createDelegate(this,this.CorrectPartNo));this._chkManufacturerCorrect=$find(this.getFormControlID(this._element.id,"chkManufacturerCorrect"));this._chkManufacturerCorrect.addClick(Function.createDelegate(this,this.ManufacturerCorrect));this._chkPackageCorrect=$find(this.getFormControlID(this._element.id,"chkPackageCorrect"));this._chkPackageCorrect.addClick(Function.createDelegate(this,this.PackageCorrect));this._chkMSLCorrect=$find(this.getFormControlID(this._element.id,"chkMSLCorrect"));this._chkMSLCorrect.addClick(Function.createDelegate(this,this.MSLCorrect));this._chkRohsStatusCorrect=$find(this.getFormControlID(this._element.id,"chkRohsStatusCorrect"));this._chkRohsStatusCorrect.addClick(Function.createDelegate(this,this.RohsStatusCorrect));this._chkbakingYes=$find(this.getFormControlID(this._element.id,"chkbakingYes"));this._chkbakingYes.addClick(Function.createDelegate(this,this.BakingLabelAdded));this._chkbakingNo=$find(this.getFormControlID(this._element.id,"chkbakingNo"));this._chkbakingNo.addClick(Function.createDelegate(this,this.BakingLabelAddedNo));this._chkbakingNA=$find(this.getFormControlID(this._element.id,"chkbakingNA"));this._chkbakingNA.addClick(Function.createDelegate(this,this.BakingLabelAddedNA));this._chkActeonePass=$find(this.getFormControlID(this._element.id,"chkActeoneTestPass"));this._chkActeonePass.addClick(Function.createDelegate(this,this.ActeoneTestPass));this._chkActeoneFail=$find(this.getFormControlID(this._element.id,"chkActeoneTestFail"));this._chkActeoneFail.addClick(Function.createDelegate(this,this.ActeoneTestFail));this._chkActeoneNA=$find(this.getFormControlID(this._element.id,"chkActeoneTestNA"));this._chkActeoneNA.addClick(Function.createDelegate(this,this.ActeoneTestNA));this._chkIsoproprylePass=$find(this.getFormControlID(this._element.id,"chkIsoproprylePass"));this._chkIsoproprylePass.addClick(Function.createDelegate(this,this.IsopropryleActeoneTestPass));this._chkIsopropryleFail=$find(this.getFormControlID(this._element.id,"chkIsopropryleFail"));this._chkIsopropryleFail.addClick(Function.createDelegate(this,this.IsopropryleActeoneTestFail));this._chkIsopropryleNA=$find(this.getFormControlID(this._element.id,"chkIsopropryleNA"));this._chkIsopropryleNA.addClick(Function.createDelegate(this,this.IsopropryleActeoneTestNA));this._blnCanViewApproval==!1&&$("#aApprovals").hide();this._blnCanViewAttachment==!1&&$("#aAttachments").hide();this.bindPackagingBreakdownData();$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtCorrectPartNo").attr("maxlength",30);$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus").addChanged(Function.createDelegate(this,this.BarcodeScannedChanged));$find("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlEnhancedInspection").addChanged(Function.createDelegate(this,this.findEnhancedInspectionStatus));PackagingDropDownJSON=this._PackagingListJSON;MFRLabelListJSON=this._MFRLabelListJSON;$find(this.getFormControlID(this._element.id,"cmbCountryOfManufacture"))._aut._blnIncludeSelected=!0;IsEditClick=!1;this.stepChanged();$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlCCUsers_ctlTo_tdTitle").css("color","#fffff4");IsEditPermission=this._blnCanEdit;IsQueryMessage=this._IsQueryMessage;InspectedBy=this._InspectedBy;setTimeout(function(){$("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnSave_lblDisabled").hide();$("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnSave_hyp").removeClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");$("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnSave_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft");$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSave_lblDisabled").hide();$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSave_hyp").removeClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSave_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft");$("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnCancel_lblDisabled").hide();$("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnCancel_hyp").removeClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");$("#ctl00_cphMain_ctlLines_ctlDB_ctl17_ibtnCancel_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft");$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnCancel_lblDisabled").hide();$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnCancel_hyp").removeClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnCancel_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft")},1e4);this._blnFirstTimePageLoad=!0;this._ddl_val_zero=!0;this.BarcodeScannedChanged()},dispose:function(){this.isDisposed||(this._intGIID=null,this._lblCurrency_Price=null,this._intLineID=null,this._intCurrencyID=null,this._strCurrencyCode=null,this._dblPOLineShipInCost=null,this._intPOQuantityOrdered=null,this._blnCanEditShipInCost=null,this._blnCanEditPurchasePrice=null,this._lblCurrency_PriceLabel=null,this._lblCurrency_Price_IPO=null,this._lblCurrency_PriceLabel_IPO=null,this._IsPOHub=null,this._blnRelatedToIPO=null,this._poBankFee=null,this._intGlobalClientNo=null,this._blnSerNoRecorded=null,this._blnProductHaza=null,this._ibtnSendQuery&&$R_IBTN.clearHandlers(this._ibtnSendQuery),this._ibtnSendQuery=null,this._intEnhancedInspectionStatusId=null,this._intGIlineBarcodesStatusId=null,this._IsQueryRaised=null,this._blnCanEdit=null,this._InspectedBy=null,this._intAttachmentId=null,this._strAttachmentType=null,this._ibtnSave&&$R_IBTN.clearHandlers(this._ibtnSave),this._ibtnSave=null,this._ibtnSave_Footer&&$R_IBTN.clearHandlers(this._ibtnSave_Footer),this._ibtnSave_Footer=null,Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.callBaseMethod(this,"dispose"))},setCurrency:function(n,t){this._intCurrencyID=n;this._strCurrencyCode=t;$R_FN.setInnerHTML(this._lblCurrency_Price,t);$R_FN.setInnerHTML(this._lblCurrency_PriceLabel,t);$R_FN.setInnerHTML(this._lblCurrency_Price_IPO,t);$R_FN.setInnerHTML(this._lblCurrency_PriceLabel_IPO,t)},setCurrencyIPO:function(n){this._strCurrencyCode=n;$R_FN.setInnerHTML(this._lblCurrency_Price_IPO,n);$R_FN.setInnerHTML(this._lblCurrency_PriceLabel_IPO,n)},saveClicked:function(){var f,n,e;this.allowSaving=!0;var r=!1,t="",u=0,i=0;(u=$get(this.getFormControlID(this._element.id,"txtShipInCost")).value,i=$get(this.getFormControlID(this._element.id,"txtPrice")).value,i==0&&(i=$get(this.getFormControlID(this._element.id,"txtPrice_IPO")).value),u==0&&(r=!0,t+="Ship In Cost value is 0. Are you sure want to update?"),i==0&&(r=!0,t+=t.length>1?"\nPurchase Price is 0. Are you sure want to update?":"Purchase Price is 0. Are you sure want to update?"),this.validateForm())&&(r==!0&&(this.allowSaving=confirm(t)?!0:!1),this.allowSaving==!0?(f=this.filedChangesList("Save"),this.refreshFieldChanged(),n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/GILines"),n.set_DataObject("GILines"),n.set_DataAction("SaveEdit"),n.addParameter("id",this._intLineID),n.addParameter("UpdateType",3),n.addParameter("Location",$get(this.getFormControlID(this._element.id,"txtLocation")).value),n.addParameter("LotNo",$find(this.getFormControlID(this._element.id,"ddlLot")).getValue()),n.addParameter("QCNotes",$get(this.getFormControlID(this._element.id,"txtQualityControlNotes")).value),n.addParameter("Quantity",$get(this.getFormControlID(this._element.id,"txtQuantity")).value),n.addParameter("IsPartNumberCorrect",this.getControlValue(this.getFormControlID(this._element.id,"chkPartNumberCorrect"),"CheckBox")),n.addParameter("CorrectPartNo",$get(this.getFormControlID(this._element.id,"txtCorrectPartNo")).value),n.addParameter("IsManufacturerCorrect",this.getControlValue(this.getFormControlID(this._element.id,"chkManufacturerCorrect"),"CheckBox")),n.addParameter("CorrectManufacturerNo",this.getControlValue(this.getFormControlID(this._element.id,"cmbManufacturer"),"Combo")),n.addParameter("IsPackageCorrect",this.getControlValue(this.getFormControlID(this._element.id,"chkPackageCorrect"),"CheckBox")),n.addParameter("CorrectPackageNo",this.getControlValue(this.getFormControlID(this._element.id,"cmbPackage"),"Combo")),n.addParameter("IsMSLCorrect",this.getControlValue(this.getFormControlID(this._element.id,"chkMSLCorrect"),"CheckBox")),n.addParameter("CorrectMslNo",$find(this.getFormControlID(this._element.id,"ddlMsl")).getValue()),n.addParameter("HICStatus",$find(this.getFormControlID(this._element.id,"HICStatus")).getValue()),n.addParameter("QueryHICStatus",$find(this.getFormControlID(this._element.id,"QueryHICStatus")).getValue()),n.addParameter("IsRohsStatusCorrect",this.getControlValue(this.getFormControlID(this._element.id,"chkRohsStatusCorrect"),"CheckBox")),n.addParameter("CorrectStatusNo",$find(this.getFormControlID(this._element.id,"ddlROHSStatus")).getValue()),n.addParameter("CountryOfManufacture",this.getControlValue(this.getFormControlID(this._element.id,"cmbCountryOfManufacture"),"Combo")),n.addParameter("CountingMethodNo",$find(this.getFormControlID(this._element.id,"ddlCountingMethod")).getValue()),n.addParameter("ReqSerailNo",this.getControlValue(this.getFormControlID(this._element.id,"chkReqSerailNo"),"CheckBox")),n.addParameter("IsLotCodeReq",this.getControlValue(this.getFormControlID(this._element.id,"chkLotCodeReq"),"CheckBox")),n.addParameter("IsEnhancedInpection",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblHeaderEnhancedInpection").text().toLowerCase()=="Yes".toLowerCase()?!0:!1),n.addParameter("EnhInpectionReqId",$find(this.getFormControlID(this._element.id,"ddlEnhancedInspection")).getValue()),n.addParameter("GeneralInspectionNotes",$get(this.getFormControlID(this._element.id,"txtGeneralInspectionNotes")).value),n.addParameter("IsBakingYes",this.getControlValue(this.getFormControlID(this._element.id,"chkbakingYes"),"CheckBox")),n.addParameter("IsBakingNo",this.getControlValue(this.getFormControlID(this._element.id,"chkbakingNo"),"CheckBox")),n.addParameter("IsBakingNA",this.getControlValue(this.getFormControlID(this._element.id,"chkbakingNA"),"CheckBox")),n.addParameter("IsInspectionConducted",this.getControlValue(this.getFormControlID(this._element.id,"chkInspectionConducted"),"CheckBox")),n.addParameter("SupplierPart",$get(this.getFormControlID(this._element.id,"txtSupplierPart")).value),n.addParameter("ProductNo",this.getControlValue(this.getFormControlID(this._element.id,"cmbProducts"),"Combo")),n.addParameter("Price",$get(this.getFormControlID(this._element.id,"txtPrice")).value),n.addParameter("ShipInCost",$get(this.getFormControlID(this._element.id,"txtShipInCost")).value),n.addParameter("ClientPrice",$get(this.getFormControlID(this._element.id,"txtPrice_IPO")).value),n.addParameter("ChangedFields",f),n.addParameter("CurrencyNo",this._intCurrencyID),n.addParameter("PartMarkings",$get(this.getFormControlID(this._element.id,"txtPartMarkings")).value),n.addParameter("AccountNotes",$get(this.getFormControlID(this._element.id,"txtAccountNotes")).value),n.addParameter("POBankFee",this._poBankFee),n.addParameter("PreviousDLUP",this._StringDLUP),e=this.getPackagingBreakdownData(),n.addParameter("PackBreakDownJSON",JSON.stringify(e)),n.addParameter("IsBySendQueryBtn",this._isSendClicked),n.addParameter("IsActeoneTestPass",this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestPass"),"CheckBox")),n.addParameter("IsActeoneTestFail",this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestFail"),"CheckBox")),n.addParameter("IsActeoneTestNA",this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestNA"),"CheckBox")),n.addParameter("ActeoneTest",$get(this.getFormControlID(this._element.id,"txtActeoneTest")).value),n.addParameter("IsIsoproprylePass",this.getControlValue(this.getFormControlID(this._element.id,"chkIsoproprylePass"),"CheckBox")),n.addParameter("IsIsopropryleFail",this.getControlValue(this.getFormControlID(this._element.id,"chkIsopropryleFail"),"CheckBox")),n.addParameter("IsIsopropryleNA",this.getControlValue(this.getFormControlID(this._element.id,"chkIsopropryleNA"),"CheckBox")),n.addParameter("Isopropryle",$get(this.getFormControlID(this._element.id,"txtIsopropryle")).value),n.addParameter("QueryBakingLevel",$get(this.getFormControlID(this._element.id,"txtQueryBakingLevel")).value),n.addParameter("PrintDateCode",$get(this.getFormControlID(this._element.id,"txtPrintDateCode")).value),n.addParameter("IsPackageBreakDownChanged",GetPackageBreakDownChnage()),n.addParameter("HasBarcodeStatusId",$find(this.getFormControlID(this._element.id,"ddlGILineBarcodesStatus")).getValue()),n.addParameter("BarcodeRemarks",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val()),n.addParameter("PartNoQuery",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryPartNo").val()),n.addParameter("ManufacturerQuery",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtQueryManfacturer").val()),n.addParameter("PackagingTypeQuery",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtPackageTypeQuery").val()),n.addParameter("MslQuery",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtMslQuery").val()),n.addParameter("RohsQuery",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtRohsQuery").val()),n.addParameter("GeneralInspectionQuery",this.getControlValue(this.getFormControlID(this._element.id,"ChkGeneralInspectionNote"),"CheckBox")),n.addDataOK(Function.createDelegate(this,this.saveEditComplete)),n.addError(Function.createDelegate(this,this.saveEditError)),n.addTimeout(Function.createDelegate(this,this.saveEditError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null):(this.showError(!0),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_pnlValidateError").hide()))},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?(this.showSaving(!1),IsEditClick==!0?(this.showSavedOK(!0),this.onSaveComplete()):this.RefreshAllQueryMessage(),this._FieldChanges=this._RefreshFieldChanges,$R_IBTN.enableButton(this._ibtnSave,!1),$R_IBTN.enableButton(this._ibtnSave_Footer,!1),$R_IBTN.enableButton(this._ibtnSendQuery,!1),$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").removeClass(),$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").removeClass(),$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").addClass("iconButton iconButton_Nugget_Disabled iconButton_Nugget_Save_Disabled iconButton_alignLeft"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible"),SetSaveValue(!0)):(this._strErrorMessage=n._errorMessage,this.onSaveError());SetPackageBreakDownChnage()},validateForm:function(){$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_pnlValidateError").show();this.onValidate();var n=!0;return this.checkControlEditEntered(this.getFormControlID(this._element.id,"txtLocation"),"TextBox")?this.checkControlEditEntered(this.getFormControlID(this._element.id,"cmbProducts"),"Combo")?this.checkControlEditEntered(this.getFormControlID(this._element.id,"txtShipInCost"),"TextBox")?this.checkControlEditEntered(this.getFormControlID(this._element.id,"txtQuantity"),"TextBox")?this.getControlValue(this.getFormControlID(this._element.id,"chkInspectionConducted"),"CheckBox")==!1?(n=!1,$("#EnhancedInspectiontd1").css("background-color","darkred"),$("#EnhancedInspectiontd2").css("background-color","darkred")):(n=!0,$("#EnhancedInspectiontd1").css("background-color","#6aa363"),$("#EnhancedInspectiontd2").css("background-color","#6aa363")):n=!1:n=!1:n=!1:n=!1,this.ValidateBarscan()||(n=!1),this.ValidatePackageBreakDown()||(n=!1),n||this.showError(!0),n||$("#aGIScreenInfo").click(),n},ValidateBarscan:function(){this.BarcodeScannedChanged();var n=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val();return this._blnBarcodeRemarkMandatory?$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val().trim().length<=0?($("#barcodeRemarError").addClass("error-highlight"),!1):($("#barcodeRemarError").removeClass("error-highlight"),!0):($("#barcodeRemarError").removeClass("error-highlight"),!0)},checkControlEditEntered:function(n,t){var i=!0;switch(t){case"TextBox":i=$R_FN.isEntered($get(n).value);break;case"DropDown":i=!$find(n).isSetAsNoValue();break;case"FileUpload":i=$find(n).checkEntered();break;case"Combo":i=$find(n).checkEntered()}return n==this.getFormControlID(this._element.id,"txtShipInCost")&&$get(this.getFormControlID(this._element.id,"txtShipInCost")).value<"0.00000"&&(i=!1),i?(document.getElementById(n).style.border="",n=="ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlUpdateType"&&i==!0&&(document.getElementById("GoodsInUpdateTypeError").style.backgroundColor="",document.getElementById("GoodsInUpdateTypeError1").style.backgroundColor=""),n=="ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtLocation"&&i==!0&&(document.getElementById("LocationError").style.backgroundColor="",document.getElementById("LocationError1").style.backgroundColor=""),n==this.getFormControlID(this._element.id,"cmbProducts")&&i==!0&&(document.getElementById("ProductsError").style.backgroundColor="",document.getElementById("ProductsError1").style.backgroundColor=""),n==this.getFormControlID(this._element.id,"txtShipInCost")&&i==!0&&(document.getElementById("ShipInCostError").style.backgroundColor="",document.getElementById("ShipInCostError1").style.backgroundColor=""),n==this.getFormControlID(this._element.id,"txtQuantity")&&i==!0&&(document.getElementById("QuantityError").style.backgroundColor="",document.getElementById("QuantityError1").style.backgroundColor="")):this.setControleditInError(n,!0,$R_RES.RequiredFieldMissingMessage),i},setControleditInError:function(n,t){t?(document.getElementById(n).focus(),n=="ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlUpdateType"&&t==!0&&(document.getElementById("GoodsInUpdateTypeError").style.backgroundColor="#990000",document.getElementById("GoodsInUpdateTypeError1").style.backgroundColor="#990000"),n=="ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtLocation"&&t==!0&&(document.getElementById("LocationError").style.backgroundColor="#990000",document.getElementById("LocationError1").style.backgroundColor="#990000"),n==this.getFormControlID(this._element.id,"cmbProducts")&&t==!0&&(document.getElementById("ProductsError").style.backgroundColor="#990000",document.getElementById("ProductsError1").style.backgroundColor="#990000"),n==this.getFormControlID(this._element.id,"txtShipInCost")&&t==!0&&(document.getElementById("ShipInCostError").style.backgroundColor="#990000",document.getElementById("ShipInCostError1").style.backgroundColor="#990000"),n==this.getFormControlID(this._element.id,"txtQuantity")&&t==!0&&(document.getElementById("QuantityError").style.backgroundColor="#990000",document.getElementById("QuantityError1").style.backgroundColor="#990000")):document.getElementById(n).style.border=""},showHideQuantity:function(){},productChange:function(){$("#"+this.getFormControlID(this._element.id,"chkPrintHazWar")).prop("checked",!1);$find(this.getFormControlID(this._element.id,"chkPrintHazWar")).enableButton($find(this.getFormControlID(this._element.id,"cmbProducts"))._aut._varSelectedExtraData)},showMessage:function(n,t){$R_FN.showElement(this._pnlValidateError,n);n&&($R_FN.showElement(this._pnlSaving,!1),$R_FN.showElement(this._pnlSavedOK,!1),$R_FN.showElement(this._pnlLoading,!1),$R_FN.showElement(this._pnlContentInner,!0),$R_FN.showElement(this._pnlLinksHolder,!0),$R_FN.showElement(this._pnlFooterLinksHolder,!0),this._ctlRelatedNugget&&(this._ctlRelatedNugget.control.showLoading(!1),this._ctlRelatedNugget.control.showRefresh(!0)),t||(t=""),this._pnlValidateErrorText.innerHTML=t)},stepChanged:function(){},chooseIfSendMail:function(){this.showMailButtons()},showMailButtons:function(){var n=this.getFieldValue("ctlSendMail");n==!1?$(".mailbody").hide():$(".mailbody").show()},getFormControlID:function(n,t){return String.format("{0}_{1}",n,t)},CorrectPartNo:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkPartNumberCorrect"),"CheckBox");n==!0?($get(this.getFormControlID(this._element.id,"txtCorrectPartNo")).value=$("#"+this.getFormControlID(this._element.id,"lblPartNo")).text(),$("#"+this.getFormControlID(this._element.id,"txtCorrectPartNo")).prop("disabled",!0)):($("#"+this.getFormControlID(this._element.id,"txtCorrectPartNo")).prop("disabled",!1),$get(this.getFormControlID(this._element.id,"txtCorrectPartNo")).value="")},ManufacturerCorrect:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkManufacturerCorrect"),"CheckBox");n==!0?($find(this.getFormControlID(this._element.id,"cmbManufacturer")).setValue(this._intPOManufacturerId,$("#"+this.getFormControlID(this._element.id,"lblManufacturer")).text()),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbManufactureraut_ctl04").css("display","none")):($find(this.getFormControlID(this._element.id,"cmbManufacturer")).setValue(0,""),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbManufactureraut_ctl04").css("display",""),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbManufacturertxt").removeAttr("disabled"))},PackageCorrect:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkPackageCorrect"),"CheckBox");n==!0?($find(this.getFormControlID(this._element.id,"cmbPackage")).setValue(this._intPOPackagingNo,$("#"+this.getFormControlID(this._element.id,"lblPackage")).text()),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbPackageaut_ctl04").css("display","none")):($find(this.getFormControlID(this._element.id,"cmbPackage")).setValue(0,""),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbPackageaut_ctl04").css("display","block"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_cmbPackagetxt").removeAttr("disabled"))},MSLCorrect:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkMSLCorrect"),"CheckBox");n==!0?($find(this.getFormControlID(this._element.id,"ddlMsl")).setValue(this._POMSL),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlMsl_ddl").prop("disabled",!0),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlMsl_ctl02").css("display","none")):($find(this.getFormControlID(this._element.id,"ddlMsl")).setValue(-1),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlMsl_ddl").prop("disabled",!1),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlMsl_ctl02").css("display",""))},HICCorrect:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkHICCorrect"),"CheckBox");n==!0?$("#"+this.getFormControlID(this._element.id,"QueryHICStatus")).prop("disabled",!0):$("#"+this.getFormControlID(this._element.id,"QueryHICStatus")).prop("disabled",!1)},RohsStatusCorrect:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkRohsStatusCorrect"),"CheckBox");n==!0?($find(this.getFormControlID(this._element.id,"ddlROHSStatus")).setValue(this._intPOROHSNo),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlROHSStatus_ddl").prop("disabled",!0),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlROHSStatus_ctl02").css("display","none")):($find(this.getFormControlID(this._element.id,"ddlROHSStatus")).setValue(-1),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlROHSStatus_ddl").prop("disabled",!1),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlROHSStatus_ctl02").css("display",""))},BakingLabelAdded:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkbakingYes"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkbakingNo")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkbakingNA")).setChecked(!1))},BakingLabelAddedNo:function(){var t=this.getControlValue(this.getFormControlID(this._element.id,"chkbakingYes"),"CheckBox"),n=this.getControlValue(this.getFormControlID(this._element.id,"chkbakingNo"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkbakingYes")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkbakingNA")).setChecked(!1))},BakingLabelAddedNA:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkbakingNA"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkbakingYes")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkbakingNo")).setChecked(!1))},BarcodeScannedChanged:function(){var n=0,t=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val(),i,r;this._blnFirstTimePageLoad==!0?(this._blnFirstTimePageLoad=!1,n=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val(),i=setInterval(function(){var r=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option").length;n.indexOf("loading")!==-1||r<=1?(n=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val(),this._tempBarcodeRemark=$("#ctl00_cphMain_ctlLines_ctlDB_ctl13_ctlBarcodeScanRemark_lbl").html().replace(/<br\s*[\/]?>/gi,"\n"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").attr("readonly","readonly"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val("Loading...")):(clearInterval(i),n=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val(),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").removeAttr("readonly","readonly"),n==2?(this._blnBarcodeRemarkMandatory=!0,$("#BarcodeMandatory").removeClass("invisible"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val(t),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl").attr("disabled")=="disabled"?$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").addProp("disabled"):$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").removeProp("disabled"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("placeholder","Remarks field is mandatory!")):(this._blnBarcodeRemarkMandatory=!1,$("#BarcodeMandatory").addClass("invisible"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val(""),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("disabled",!0),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("placeholder","")))},4e3)):(n=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").val(),r=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlGILineBarcodesStatus_ddl option:selected").text(),n==2?(this._blnBarcodeRemarkMandatory=!0,this._tempBarcodeRemark=$("#ctl00_cphMain_ctlLines_ctlDB_ctl13_ctlBarcodeScanRemark_lbl").html().replace(/<br\s*[\/]?>/gi,"\n"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val(t),$("#BarcodeMandatory").removeClass("invisible"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").removeProp("disabled"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("placeholder","Remarks field is mandatory!")):(this._blnBarcodeRemarkMandatory=!1,$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").val(""),$("#BarcodeMandatory").addClass("invisible"),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("disabled",!0),$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_txtBarcodeRemarks").prop("placeholder","")))},finishedForm:function(){this._ctlMultiStep.showExplainLabel(!1);this._ctlMultiStep.showSteps(!1);$R_IBTN.showButton(this._ibtnSave,!1);$R_IBTN.showButton(this._ibtnSave_Footer,!1);this.showSavedOK(!0);this.onSaveComplete()},getQueryData:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetGIQueryData");n.addParameter("GoodsInLineId",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getQueryDataOK));n.addError(Function.createDelegate(this,this.getQueryDataError));n.addTimeout(Function.createDelegate(this,this.getQueryDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getQueryDataOK:function(n){var t=n._result;$("#dvGIAllQueries").html($R_FN.setCleanTextValue(t.GIQuery))},getQueryDataError:function(n){this.showError(!0,n.get_ErrorMessage())},sendClicked:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("NotifyGIQuery");n.addParameter("GoodsInLineId",this._intLineID);n.addParameter("IsSales",this.getControlValue(this.getFormControlID(this._element.id,"chkSales"),"CheckBox"));n.addParameter("IsPurchasing",this.getControlValue(this.getFormControlID(this._element.id,"chkPurchasing"),"CheckBox"));n.addParameter("IsQualityApproval",this.getControlValue(this.getFormControlID(this._element.id,"chkQualityApproval"),"CheckBox"));n.addParameter("Query",document.getElementById("dvGIAllQueries").innerHTML);n.addParameter("GoodsInId",this._intGIID);n.addParameter("GoodsInNumber",this._intGoodsInNumber);n.addDataOK(Function.createDelegate(this,this.sendComplete));n.addError(Function.createDelegate(this,this.sendError));n.addTimeout(Function.createDelegate(this,this.sendError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},sendError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},sendComplete:function(n){n._result.Result==!0?(this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},filedChangesList:function(n){var i=!1,t="";if(eval("this._FieldChanges.txtCorrectPartNo")!=$get(this.getFormControlID(this._element.id,"txtCorrectPartNo")).value&&(t!=""&&(t+="||"),t+="CorrectPartNo"),eval("this._FieldChanges.ddlROHSStatus")=="0"&&$find(this.getFormControlID(this._element.id,"ddlROHSStatus")).getValue()!="0"&&(this._FieldChanges.ddlROHSStatus=""),eval("this._FieldChanges.ddlROHSStatus")!=($find(this.getFormControlID(this._element.id,"ddlROHSStatus")).getValue()==null?"":$find(this.getFormControlID(this._element.id,"ddlROHSStatus")).getValue())&&(t!=""&&(t+="||"),t+="CorrectRohsStatus"),eval("this._FieldChanges.txtLocation")!=$get(this.getFormControlID(this._element.id,"txtLocation")).value&&(t!=""&&(t+="||"),t+="Location"),eval("this._FieldChanges.ddlLot")=="0"&&(this._FieldChanges.ddlLot=""),eval("this._FieldChanges.ddlLot")!=($find(this.getFormControlID(this._element.id,"ddlLot")).getValue()==null?"":$find(this.getFormControlID(this._element.id,"ddlLot")).getValue())&&(t!=""&&(t+="||"),t+="Lot"),eval("this._FieldChanges.txtSupplierPart")!=$get(this.getFormControlID(this._element.id,"txtSupplierPart")).value&&(t!=""&&(t+="||"),t+="SupplierPartNo"),eval("this._FieldChanges.cmbProducts")=="0"&&(this._FieldChanges.cmbProducts=""),eval("this._FieldChanges.cmbProducts")!=(this.getControlValue(this.getFormControlID(this._element.id,"cmbProducts"),"Combo")==null?"":this.getControlValue(this.getFormControlID(this._element.id,"cmbProducts"),"Combo"))&&(t!=""&&(t+="||"),t+="Product",i=!0),eval("this._FieldChanges.cmbPackage")=="0"&&(this._FieldChanges.cmbPackage=""),eval("this._FieldChanges.cmbPackage")!=(this.getControlValue(this.getFormControlID(this._element.id,"cmbPackage"),"Combo")==null?"":this.getControlValue(this.getFormControlID(this._element.id,"cmbPackage"),"Combo"))&&(t!=""&&(t+="||"),t+="CorrectPackage"),eval("this._FieldChanges.cmbManufacturer")=="0"&&(this._FieldChanges.cmbManufacturer=""),eval("this._FieldChanges.cmbManufacturer")!=(this.getControlValue(this.getFormControlID(this._element.id,"cmbManufacturer"),"Combo")==null?"":this.getControlValue(this.getFormControlID(this._element.id,"cmbManufacturer"),"Combo"))&&(t!=""&&(t+="||"),t+="CorrectManufacturer"),eval("this._FieldChanges.cmbCountryOfManufacture")=="0"&&(this._FieldChanges.cmbCountryOfManufacture=""),eval("this._FieldChanges.cmbCountryOfManufacture")!=(this.getControlValue(this.getFormControlID(this._element.id,"cmbCountryOfManufacture"),"Combo")==null?"":this.getControlValue(this.getFormControlID(this._element.id,"cmbCountryOfManufacture"),"Combo"))&&(t!=""&&(t+="||"),t+="countryOforigin"),eval("this._FieldChanges.txtShipInCost")!=$get(this.getFormControlID(this._element.id,"txtShipInCost")).value&&(t!=""&&(t+="||"),t+="ShipInCost",i=!0),this._FieldChanges.txtQualityControlNotes=eval("this._FieldChanges.txtQualityControlNotes").replace(/<br\s*[\/]?>/gi,"\n"),eval("this._FieldChanges.txtQualityControlNotes")!=$get(this.getFormControlID(this._element.id,"txtQualityControlNotes")).value&&(t!=""&&(t+="||"),t+="InsToQualityControlNotes"),eval("this._FieldChanges.ddlCountingMethod")=="0"&&(this._FieldChanges.ddlCountingMethod=""),eval("this._FieldChanges.ddlCountingMethod")!=($find(this.getFormControlID(this._element.id,"ddlCountingMethod")).getValue()==null?"":$find(this.getFormControlID(this._element.id,"ddlCountingMethod")).getValue())&&(t!=""&&(t+="||"),t+="CountingMethod"),eval("this._FieldChanges.txtPartMarkings")!=$get(this.getFormControlID(this._element.id,"txtPartMarkings")).value&&(t!=""&&(t+="||"),t+="BatchReference"),eval("this._FieldChanges.txtPrice")!=$get(this.getFormControlID(this._element.id,"txtPrice")).value&&(t!=""&&(t+="||"),t+="PurchasePrice",i=!0),eval("this._FieldChanges.txtPrice_IPO")!=$get(this.getFormControlID(this._element.id,"txtPrice_IPO")).value&&(t!=""&&(t+="||"),t+="PurchasePrice",i=!0),eval("this._FieldChanges.ddlMsl")=="0"&&(this._FieldChanges.ddlMsl=""),eval("this._FieldChanges.ddlMsl")!=($find(this.getFormControlID(this._element.id,"ddlMsl")).getValue()==null?"":$find(this.getFormControlID(this._element.id,"ddlMsl")).getValue())&&(t!=""&&(t+="||"),t+="CorrectMSL"),eval("this._FieldChanges.QueryHICStatus")!=($find(this.getFormControlID(this._element.id,"QueryHICStatus")).getValue()==null?"":$find(this.getFormControlID(this._element.id,"QueryHICStatus")).getValue())&&(t!=""&&(t+="||"),t+="CorrectHIC"),eval("this._FieldChanges.txtQuantity")!=$get(this.getFormControlID(this._element.id,"txtQuantity")).value&&(t!=""&&(t+="||"),t+="Quantity"),i==!0&&(t+="||LandedCost"),n=="SendQuery"){eval("this._FieldChanges.txtActeoneTest")!=$get(this.getFormControlID(this._element.id,"txtActeoneTest")).value&&(t!=""&&(t+="||"),t+="ActeoneTest");eval("this._FieldChanges.txtIsopropryle")!=$get(this.getFormControlID(this._element.id,"txtIsopropryle")).value&&(t!=""&&(t+="||"),t+="Isopropryle");$R_FN.setCleanTextValue(eval("this._FieldChanges.txtGeneralInspectionNotes")).trim().replace(/<br\s*[\/]?>/gi,"\n")!=$R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id,"txtGeneralInspectionNotes")).value).trim().replace(/<br\s*[\/]?>/gi,"\n")&&(t!=""&&(t+="||"),t+="GeneralInspectionNotes");eval("this._FieldChanges.ddlEnhancedInspection")=="0"&&(this._FieldChanges.ddlEnhancedInspection="");eval("this._FieldChanges.ddlEnhancedInspection")!=($find(this.getFormControlID(this._element.id,"ddlEnhancedInspection")).getValue()==null?"":$find(this.getFormControlID(this._element.id,"ddlEnhancedInspection")).getValue())&&(t!=""&&(t+="||"),t+="EnhancedInspection");eval("this._FieldChanges.chkLotCodeReq")!=(this.getControlValue(this.getFormControlID(this._element.id,"chkLotCodeReq"),"CheckBox")==!0?"true":"false")&&(t!=""&&(t+="||"),t+="LotCodeReq");eval("this._FieldChanges.chkReqSerailNo")!=(this.getControlValue(this.getFormControlID(this._element.id,"chkReqSerailNo"),"CheckBox")==!0?!0:!1)&&(t!=""&&(t+="||"),t+="ReqSerailNo");eval("this._FieldChanges.chkInspectionConducted")!=(this.getControlValue(this.getFormControlID(this._element.id,"chkInspectionConducted"),"CheckBox")==!0?"true":"false")&&(t!=""&&(t+="||"),t+="InspectionConducted");eval("this._FieldChanges.HICStatus")!=($find(this.getFormControlID(this._element.id,"HICStatus")).getValue()==null?"":$find(this.getFormControlID(this._element.id,"HICStatus")).getValue())&&(t!=""&&(t+="||"),t+="HICStatus");eval("this._FieldChanges.chkbaking")!=(this.getControlValue(this.getFormControlID(this._element.id,"chkbakingYes"),"CheckBox")==!0?"1":this.getControlValue(this.getFormControlID(this._element.id,"chkbakingNo"),"CheckBox")==!0?"2":this.getControlValue(this.getFormControlID(this._element.id,"chkbakingNA"),"CheckBox")==!0?"3":"-1")&&(t!=""&&(t+="||"),t+="chkbaking");eval("this._FieldChanges.ActeoneTestStatus")!=(this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestPass"),"CheckBox")==!0?"1":this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestFail"),"CheckBox")==!0?"2":this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestNA"),"CheckBox")==!0?"3":"-1")&&(t!=""&&(t+="||"),t+="ActeoneTest");eval("this._FieldChanges.IsopropryleStatus")!=(this.getControlValue(this.getFormControlID(this._element.id,"chkIsoproprylePass"),"CheckBox")==!0?"1":this.getControlValue(this.getFormControlID(this._element.id,"chkIsopropryleFail"),"CheckBox")==!0?"2":this.getControlValue(this.getFormControlID(this._element.id,"chkIsopropryleNA"),"CheckBox")==!0?"3":"-1")&&(t!=""&&(t+="||"),t+="Isopropryle");var u=this.validatePackagingBreakdownData(),f=JSON.stringify(u)=="[]"?undefined:JSON.stringify(u),r=eval("this._FieldChanges.PackBreakDownJSON");r=="[]"&&(r=undefined);r!=f&&(t!=""&&(t+="||"),t+="PackingBreakDown");eval("this._FieldChanges.txtQueryBakingLevel")!=$get(this.getFormControlID(this._element.id,"txtQueryBakingLevel")).value&&(t!=""&&(t+="||"),t+="QueryBakingLevel");eval("this._FieldChanges.txtPrintDateCode")!=$get(this.getFormControlID(this._element.id,"txtPrintDateCode")).value&&(t!=""&&(t+="||"),t+="PrintDateCode");eval("this._FieldChanges.txtQuantity")!=$get(this.getFormControlID(this._element.id,"txtQuantity")).value&&(t!=""&&(t+="||"),t+="Quantity");eval("this._FieldChanges.txtAccountNotes")!=$get(this.getFormControlID(this._element.id,"txtAccountNotes")).value&&(t!=""&&(t+="||"),t+="AccountNotes");eval("this._FieldChanges.ddlGILineBarcodesStatus")!=$get(this.getFormControlID(this._element.id,"ddlGILineBarcodesStatus")).value&&(t!=""&&(t+="||"),t+="GILineBarcodesStatus")}return t},RefreshAllQueryMessage:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetGILineQueryMessage");n.addParameter("GoodsInId",this._intGIID);n.addParameter("GoodsInLineId",this._intLineID);n.addDataOK(Function.createDelegate(this,this.RefreshAllQueryMessageOK));n.addError(Function.createDelegate(this,this.RefreshAllQueryMessageOK));n.addTimeout(Function.createDelegate(this,this.RefreshAllQueryMessageError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},RefreshAllQueryMessageError:function(){},RefreshAllQueryMessageOK:function(n){var s,t,h,a,v;this._invoiceExist=!1;this._countLotNo=0;res=n._result;var r="",f="";$(".discussionbox").html("");var u="GI Line",i="",e="",o="",c="",l="";for(res.QueryMessageDetails.length>0&&(res.QueryMessageDetails[0].SalesGroupId>0&&this._ctlMailRequest.addNewLoginRecipient(res.QueryMessageDetails[0].SalesGroupId,res.QueryMessageDetails[0].SalesGroupName),res.QueryMessageDetails[0].PurchasingGroupId>0&&this._ctlMailRequest.addNewLoginRecipient(res.QueryMessageDetails[0].PurchasingGroupId,res.QueryMessageDetails[0].PurchasingGroupName)),s=0;s<res.QueryMessageDetails.length;s++)t=res.QueryMessageDetails[s],t.ParentSalesApprovalStatus=t.ParentSalesApprovalStatus==3?2:t.ParentSalesApprovalStatus,t.ParentPurchaseApprovalStatus=t.ParentPurchaseApprovalStatus==3?2:t.ParentPurchaseApprovalStatus,t.ParentQualityApprovalStatus=t.ParentQualityApprovalStatus==3?2:t.ParentQualityApprovalStatus,$("#lblSapproverName").text(t.ProcessSalesApproverName),$("#lblPapproverName").text(t.ProcessPurchaseApproverName),$("#lblQapproverName").text(t.ProcessQualityApproverName),t.ProcessSalesApproverName=="No Approver Found"?($("#lblSapproverName").addClass("GIWarninglbl"),$("#tdSApproverName").addClass("GIWarningtd"),IsSApproverNotFound=1):($("#lblSapproverName").removeClass("GIWarninglbl"),$("#tdSApproverName").removeClass("GIWarningtd"),IsSApproverNotFound=0),t.ProcessPurchaseApproverName=="No Approver Found"?($("#lblPapproverName").addClass("GIWarninglbl"),$("#tdPApproverName").addClass("GIWarningtd"),IsPApproverNotFound=1):($("#lblPapproverName").removeClass("GIWarninglbl"),$("#tdPApproverName").removeClass("GIWarningtd"),IsPApproverNotFound=0),t.ProcessQualityApproverName=="No Approver Found"?($("#lblQapproverName").addClass("GIWarninglbl"),$("#tdQApproverName").addClass("GIWarningtd"),IsQApproverNotFound=1):($("#lblQapproverName").removeClass("GIWarninglbl"),$("#tdQApproverName").removeClass("GIWarningtd"),IsQApproverNotFound=0),u="",(t.Quarantined==!0||t.ISReleaseStock==!0)&&t.IsInitialMessage==!0?(h="",t.Quarantined==!0&&(h+=" <b style='color:red;font-size: 12px;background: yellow;font-style: italic;'>(This Stock is Quarantined)<\/b>"),t.ISReleaseStock==!0&&(h+=" <b style='color:red;font-size: 12px;background: yellow;font-style: italic;'>(This Stock is Released)<\/b>"),u+=" Query Number: "+t.GIQueryNumber+h):u+=" Query Number: "+t.GIQueryNumber,i="",i+=t.NotifyToSales==!0?t.CurrentSalesApprover+"":"",i+=t.NotifyToQuality==!0?i.length>0?", QUALITY":"QUALITY":"",i+=t.NotifyToPurchasing==!0?i.length>0?", "+t.CurrentPurchasingApprover+"":t.CurrentPurchasingApprover+"":"",i+=t.CCUsersName.length>0?i.length>0?", "+t.CCUsersName+"":t.CCUsersName+"":"",i+=i.length==0?"Not Send":"",e="",e+=t.SalesApprovalStatus==1?"<b>Sales:<\/b> Approved ":t.SalesApprovalStatus==2?"<b>Sales:<\/b> Declined ":t.SalesApprovalStatus==3?"<b>Sales:<\/b> Partial Approved":"",e+=t.QualityApprovalStatus==1?"<b>Quality:<\/b> Approved ":t.QualityApprovalStatus==2?"<b>Quality:<\/b> Declined ":t.QualityApprovalStatus==3?"<b>Quality:<\/b> Partial Approved":"",e+=t.PurchaseApprovalStatus==1?"<b>Purchasing:<\/b> Approved ":t.PurchaseApprovalStatus==2?"<b>Purchasing:<\/b> Declined":t.PurchaseApprovalStatus==3?"<b>Purchasing:<\/b> Partial Approved":"",a=this._blnCanManageApproverEdit==!0?t.InDraftMode==!1?String.format("<a class='configicon' title='This screen allows you to change the Approver for a GI Query, but the original Approver will still receive all the messages' onClick=\"ConfigureApprover("+t.Gi_QueryId+",'"+t.CurrentPurchasingApprover+"','"+t.CurrentSalesApprover+"',"+t.ParentPurchaseApprovalStatus+","+t.ParentSalesApprovalStatus+") \">Manage Approvers<img src='../../../../images/ConfigureGi.png'><\/a>"):"":"",l=t.InDraftMode==!0?"<a class='iconButton iconButton_Nugget_PostAll' style='color:white;margin-left: 45px' onClick='RequestForApproval("+t.Gi_QueryId+","+IsSApproverNotFound+","+IsPApproverNotFound+","+IsQApproverNotFound+")'><img src='../../../../images/sendbtn.png'>Send Query<\/a>":"<a class='iconButton iconButton_Nugget_PostAll' id='btnRespondQ' style='color:white;margin-left: 20px' onClick='ResponceForApproval("+t.Gi_QueryId+","+t.NotifyToSales+","+t.NotifyToQuality+","+t.NotifyToPurchasing+","+t.ISSalesPermission+","+t.ISQualityPermission+","+t.ISPurchasingPermission+","+t.ParentSalesApprovalStatus+","+t.ParentPurchaseApprovalStatus+","+t.ParentQualityApprovalStatus+","+t.IsNotGBLPermissionForSales+","+t.IsNotGBLPermissionForPurch+","+t.IsNotGBLPermissionForQaulity+","+t.C1+","+t.C2+","+t.C3+","+t.C4+","+t.C5+","+t.C6+","+t.C7+","+t.C8+","+t.C9+","+t.C10+","+t.IsQueryColumn+")'><img src='../../../../images/sendbtn.png'>Respond to query<\/a>",t.IsInitialMessage==!0&&(v=$R_FN.setCleanTextValue(this.ChangeApproverResponse($R_FN.setCleanTextValue(t.QueryMessageApproval),t.C1,t.C2,t.C3,t.C4,t.C5,t.C6,t.C7,t.C8,t.C9,t.C10)),$("#QueryMessageP").html("<div style='background-color:#c1e5bc;border:1px #c1e5bc solid;' class='contentarea'>"+v+"<\/div>")),o="",o+=t.NotifyToSales==!0?"SALES":"",o+=t.NotifyToQuality==!0?"<br/>QUALITY":"",o+=t.NotifyToPurchasing==!0?"<br/>PURCHASING":"",GIClientName=t.ClientName,t.MyMessage==!0?t.IsInitialMessage==!0?(r+="<div class='rightcontentbox'><div class='leftimgblock'><img src='../../../../images/profilepic.png'><br/><span class=''>"+t.QueryRaisedBy+"<\/span><\/div>",r+="<div class='contentarea'><span class='title'>"+u+"<span class='ManageApprovers'>"+a+"<\/span><\/span><span class='datetime'>"+(i=="Not Send"?"":t.DLUP)+(i!="Not Send"?" | <b>Sent To: <\/b>"+i:"")+"<\/span><p>"+$R_FN.setCleanTextValue(t.QueryMessage)+"<p class='remarkcol' > <b>WAREHOUSE REMARKS: <\/b>"+t.WarehouseRemark+"<\/p><\/span><\/div>",r+="<div  class='rlybtn'>"+l+"<\/div><\/div>"):(f+="<div class='rightcontentbox'><div class='leftimgblock'><img src='../../../../images/profilepic.png'><br/><span class=''>"+t.QueryRaisedBy+"<\/span><\/div>",f+="<div class='contentarea'><span class='title'>RE:"+u+"<\/span><span class='datetime'>"+t.DLUP+" | <b>Sent To: <\/b>WAREHOUSE | "+e+"<\/span><p>"+$R_FN.setCleanTextValue(t.QueryMessage)+"<\/span><\/div>"+$R_FN.setCleanTextValue(t.QueryMessageApproval)+"<\/div>"):t.IsInitialMessage==!0?(r+="<div class='leftcontentbox'><div class='leftimgblock'><img src='../../../../images/profilepic2.png'><br/><span class=''>"+t.QueryRaisedBy+"<\/span><\/div>",r+="<div class='contentarea'><span class='title'>"+u+"<span class='ManageApprovers'>"+a+"<\/span><\/span><span class='datetime'>"+(i=="Not Send"?"":t.DLUP)+(i!="Not Send"?" | <b>Sent To: <\/b>"+i:"")+"<\/span><p>"+$R_FN.setCleanTextValue(t.QueryMessage)+"<p class='remarkcol' > <b>WAREHOUSE REMARKS: <\/b>"+t.WarehouseRemark+"<\/p> <\/span><\/div>",r+="<div class='rlybtn'>"+l+"<\/div><\/div>"):(f+="<div class='leftcontentbox'><div class='leftimgblock'><img src='../../../../images/profilepic2.png'><br/><span class=''>"+t.QueryRaisedBy+"<\/span><\/div>",f+="<div class='contentarea'><span class='title'>RE:"+u+"<\/span><span class='datetime'>"+t.DLUP+" | <b>Sent To: <\/b>WAREHOUSE | "+e+"<\/span><p>"+$R_FN.setCleanTextValue(t.QueryMessage)+" <\/span><\/div>"+$R_FN.setCleanTextValue(t.QueryMessageApproval)+"<\/div>"),t.DraftQueryMessage!=""&&(c=t.DraftQueryMessage),t=null;$("#IsInitailMessage").append(r);$("#IsApprovalMessage").append(f);r="";f="";$get(this.getFormControlID(this._element.id,"TxtMessageBox")).value=c;$get(this.getFormControlID(this._element.id,"txtResponceBox")).value=c},SendQueryMessage:function(){var i=getSApproverNotFound(),r=getPApproverNotFound(),u=getQApproverNotFound(),t="",n;i==1&&this.getControlValue(this.getFormControlID(this._element.id,"chkSales"),"CheckBox")==!0&&(t="Sales Approver not found, You still want to raise query kindly confirm?");r==1&&this.getControlValue(this.getFormControlID(this._element.id,"chkPurchasing"),"CheckBox")==!0&&(t="Purchase Approver not found, You still want to raise query kindly confirm?");u==1&&this.getControlValue(this.getFormControlID(this._element.id,"chkQualityApproval"),"CheckBox")==!0&&(t="Quality Approver not found, You still want to raise query kindly confirm?");$get(this.getFormControlID(this._element.id,"TxtMessageBox")).value.length>0?this.getControlValue(this.getFormControlID(this._element.id,"chkSales"),"CheckBox")==!0||this.getControlValue(this.getFormControlID(this._element.id,"chkPurchasing"),"CheckBox")==!0||this.getControlValue(this.getFormControlID(this._element.id,"chkQualityApproval"),"CheckBox")==!0?i==1&&this.getControlValue(this.getFormControlID(this._element.id,"chkSales"),"CheckBox")==!0||r==1&&this.getControlValue(this.getFormControlID(this._element.id,"chkPurchasing"),"CheckBox")==!0||u==1&&this.getControlValue(this.getFormControlID(this._element.id,"chkQualityApproval"),"CheckBox")==!0?confirm(t)==!0&&($("#btnSendQuery").attr("disabled","disabled"),n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/GILines"),n.set_DataObject("GILines"),n.set_DataAction("AddGILineQueryMessage"),n.addParameter("GoodsInId",this._intGIID),n.addParameter("GoodsInLineId",this._intLineID),n.addParameter("QueryMessage",$get(this.getFormControlID(this._element.id,"TxtMessageBox")).value),n.addParameter("IsSalesNotify",this.getControlValue(this.getFormControlID(this._element.id,"chkSales"),"CheckBox")),n.addParameter("IsPurchasingNotify",this.getControlValue(this.getFormControlID(this._element.id,"chkPurchasing"),"CheckBox")),n.addParameter("IsQulityNotify",this.getControlValue(this.getFormControlID(this._element.id,"chkQualityApproval"),"CheckBox")),n.addParameter("GI_QueryId",Get_GIQueryId()),n.addParameter("PONumber",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPONumber").text()),n.addParameter("GoodsInNumber",this._intGoodsInNumber),n.addParameter("EnhInspectionStatusId",this._intEnhancedInspectionStatusId),n.addParameter("CCUserId",$R_FN.arrayToSingleString(this._ctlMailRequest._aryRecipientLoginIDs)),n.addParameter("CCGroupIDs",$R_FN.arrayToSingleString(this._ctlMailRequest._aryRecipientGroupIDs)),n.addDataOK(Function.createDelegate(this,this.SendQueryMessageOK)),n.addError(Function.createDelegate(this,this.SendQueryMessageError)),n.addTimeout(Function.createDelegate(this,this.SendQueryMessageError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null):($("#btnSendQuery").attr("disabled","disabled"),n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/GILines"),n.set_DataObject("GILines"),n.set_DataAction("AddGILineQueryMessage"),n.addParameter("GoodsInId",this._intGIID),n.addParameter("GoodsInLineId",this._intLineID),n.addParameter("QueryMessage",$get(this.getFormControlID(this._element.id,"TxtMessageBox")).value),n.addParameter("IsSalesNotify",this.getControlValue(this.getFormControlID(this._element.id,"chkSales"),"CheckBox")),n.addParameter("IsPurchasingNotify",this.getControlValue(this.getFormControlID(this._element.id,"chkPurchasing"),"CheckBox")),n.addParameter("IsQulityNotify",this.getControlValue(this.getFormControlID(this._element.id,"chkQualityApproval"),"CheckBox")),n.addParameter("GI_QueryId",Get_GIQueryId()),n.addParameter("PONumber",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPONumber").text()),n.addParameter("GoodsInNumber",this._intGoodsInNumber),n.addParameter("EnhInspectionStatusId",this._intEnhancedInspectionStatusId),n.addParameter("CCUserId",$R_FN.arrayToSingleString(this._ctlMailRequest._aryRecipientLoginIDs)),n.addParameter("CCGroupIDs",$R_FN.arrayToSingleString(this._ctlMailRequest._aryRecipientGroupIDs)),n.addDataOK(Function.createDelegate(this,this.SendQueryMessageOK)),n.addError(Function.createDelegate(this,this.SendQueryMessageError)),n.addTimeout(Function.createDelegate(this,this.SendQueryMessageError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null):alert("Please choose at least one option from send to section."):alert("Please write something in the discussion box.")},SendQueryMessageError:function(){$("#btnSendQuery").removeAttr("disabled")},SendQueryMessageOK:function(){var n,t,i,r;this.RefreshAllQueryMessage();$get(this.getFormControlID(this._element.id,"TxtMessageBox")).value="";n=this.getFormControlID(this._element.id,"chkSales");this.setControlValue("CheckBox",n,!1,"");t=this.getFormControlID(this._element.id,"chkPurchasing");this.setControlValue("CheckBox",t,!1,"");i=this.getFormControlID(this._element.id,"chkQualityApproval");this.setControlValue("CheckBox",i,!1,"");r=document.getElementById("RequestApprovalModel");r.style.display="none";$R_IBTN.enableButton(this._ibtnSaveRelease,!1);$R_IBTN.enableButton(this._ibtnSendQuery,!1);$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").removeClass();$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").removeClass();$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").addClass("iconButton iconButton_Nugget_Disabled iconButton_Nugget_Save_Disabled iconButton_alignLeft");$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");this._IsQueryRaised=!0;$R_IBTN.enableButton(this._ibtnSave,!1);$R_IBTN.enableButton(this._ibtnSave_Footer,!1);$("#btnSendQuery").removeAttr("disabled")},getPackagingBreakdownData:function(){var n=[];return $("tr.data-Breakdown-GI").each(function(){var r=$(this).find(".f-FactorySealed01").is(":checked"),t=$(this).find(".n-NumberofPacks01").val(),i=$(this).find(".p-PackSize01").val(),u=$(this).find(".d-DateCode01").val(),f=$(this).find(".d-BatchCode01").val(),e=$(this).find(".d-PackagingType01").val(),o=$(this).find(".d-MFRLabel01").val(),s=$(this).find(".t-Total01").val(),h={FactorySealed:r,NumberofPacks:t,PackSize:i,DateCode:u,BatchCode:f,PackagingTypeId:e,MFRLabelId:o,Total:s};(t>0||i>0)&&n.push(h)}),n},bindPackagingBreakdownData:function(){var r,u,f,t,i,n;if($("#tbBreakdownTable").empty(),this._PackBreakDownJSON!=undefined&&this._PackBreakDownJSON.length>0){for(r=0,u="<option value ='0'>--Select--<\/option>",t=0;t<this._PackagingListJSON.length;t++)u+="<option value = '"+this._PackagingListJSON[t].ID+"'>"+this._PackagingListJSON[t].Name+" <\/option>";for(f="<option value ='0'>--Select--<\/option>",t=0;t<this._MFRLabelListJSON.length;t++)f+="<option value = '"+this._MFRLabelListJSON[t].ID+"'>"+this._MFRLabelListJSON[t].Name+" <\/option>";for(t=0;t<this._PackBreakDownJSON.length;t++)i=this._PackBreakDownJSON[t],n=$(".data-Breakdown-GI").length+1,$('<tr id="tablerow'+n+'" class="data-Breakdown-GI"><td id="tdFactorySealed'+n+'"><input type="checkbox" name="FactorySealed'+n+'"class="form-control f-FactorySealed01" id=chkFactorySealed'+n+(i.FactorySealed==!0?' checked="checked"':"")+'><\/td><td id="tdNumberofPacks'+n+'"><input type="text" name="NumberofPacks'+n+'"class="form-control n-NumberofPacks01" id=txtNumberofPacks'+n+' onkeypress="return (event.charCode !=8 && event.charCode ==0 || (event.charCode >= 48 && event.charCode <= 57))" onKeyUp="return BindPackagingBreakdown(this.id)" value='+i.NumberofPacks+'><\/td><td id="tdPackSize'+n+'"><input type="text" name="PackSize'+n+'"class="form-control p-PackSize01" id=txtPackSize'+n+' onkeypress="return (event.charCode !=8 && event.charCode ==0 || (event.charCode >= 48 && event.charCode <= 57))" onKeyUp="return BindPackagingBreakdown(this.id)" value='+i.PackSize+'><\/td><td id="tdDateCode'+n+'"><input type="text" name="DateCode'+n+'"class="form-control d-DateCode01" id=txtDateCode'+n+"  value="+i.DateCode+'><\/td><td id="tdPackagingType'+n+'"><select name="PackagingType'+n+'"class="form-control d-PackagingType01" id=ddlPackagingType'+n+' /select><\/td><td id="tdBatchCode'+n+'"><input type="text" style="width: 61px !important;" name="BatchCode'+n+'"class="form-control d-BatchCode01" id=txtBatchCode'+n+" value="+i.BatchCode+'><\/td><td id="tdMFRLabel'+n+'"><select name="MFRLabel'+n+'"class="form-control d-MFRLabel01" id=ddlMFRLabel'+n+' /select><\/td><td id="tdTotal'+n+'"><input type="text" name="Total'+n+'"class="form-control t-Total01" id=txtTotal'+n+" value="+i.Total+' readonly="readonly"><\/td><td><button class="btn btn-primary" onclick="removeTr('+n+');"><i class="fa fa-close"><\/i><\/button><\/td><\/tr>').appendTo("#BreakdownTable"),$("#ddlPackagingType"+n).append(u),$("#ddlPackagingType"+n+' option[value="'+i.PackagingTypeId+'"]').attr("selected","selected"),$("#ddlPackagingType"+n).chosen({allow_single_deselect:!0}),$("#ddlMFRLabel"+n).append(f),$("#ddlMFRLabel"+n+' option[value="'+i.MFRLabelId+'"]').attr("selected","selected"),$("#ddlPackagingType"+n+"_chosen").css("width","200px"),n++,r=r+parseFloat(i.Total);$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPackageBreakTotal").text(r)}},GILineUploadPDF:function(n,t){var i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData("controls/Nuggets/GILines");i.set_DataObject("GILines");i.set_DataAction("SaveGILineUploadPDF");i.addParameter("ID",this._intLineID);i.addParameter("Caption",n.split(".").slice(0,-1).join("."));i.addParameter("OriginalFilename",n);i.addParameter("TempFile",t);i.addDataOK(Function.createDelegate(this,this.saveAddComplete));i.addError(Function.createDelegate(this,this.saveAddError));i.addTimeout(Function.createDelegate(this,this.saveAddError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null},saveAddComplete:function(n){n._result.Result==!0?this.getAttachementsData():alert("No Stock Found.")},saveAddError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},getPDFData:function(){this._intCountPDF==0;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetPDFData");n.addParameter("id",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getPDFDataOK));n.addError(Function.createDelegate(this,this.getPDFDataError));n.addTimeout(Function.createDelegate(this,this.getPDFDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getPDFDataOK:function(n){var o=n._result,f=n._result,u,t;$R_FN.setInnerHTML(this._pnlPDFDocuments,"");var e=f.IconPath,r="",i="margin-left: 57%;";if(f.Items){for(u=0;u<f.Items.length;u++)t=f.Items[u],t.Caption.length==1&&(i="margin-left:15%;"),t.Caption.length==2&&(i="margin-left:21%;"),t.Caption.length==3&&(i="margin-left:23%;"),t.Caption.length==4&&(i="margin-left:25%;"),t.Caption.length==5&&(i="margin-left:26%;"),t.Caption.length==6&&(i="margin-left:29%;"),t.Caption.length==7&&(i="margin-left:33%;"),t.Caption.length==8&&(i="margin-left:35%;"),t.Caption.length==9&&(i="margin-left:37%;"),t.Caption.length==10&&(i="margin-left:40%;"),t.Caption.length==11&&(i="margin-left:43%;"),t.Caption.length==12&&(i="margin-left:46%;"),t.Caption.length==13&&(i="margin-left:49%;"),t.Caption.length==14&&(i="margin-left:53%;"),t.Caption.length==15&&(i="margin-left:56%;"),r+=String.format("<span class='thumbimgbox'>"),t.IsDeletePermission&&(r+=String.format("<div class=\"GIDeleteCheckBox\" \"><input type='checkbox' class='chkhideshow' style='margin-top: 3px;margin-left: 25px;' onclick='AddPdfAttachmentIds("+t.ID+")' id='BulkAttachmentDelete"+t.ID+"' /><\/div>")),r+=String.format('<a href="{0}" ><img  id ="{1}_img{2}" src="{3}" border="0" onclick="$find(\'{4}\').OpenPDF(\'{5}\',\'{6}\');" /><\/a>',"javascript:void(0);",this._element.id,u,e,this._element.id,"",t.FileName),r+="<span class='docdetails'>",r+=String.format("<span class='doctitle' style='color:yellow;cursor: pointer;text-decoration: underline;' title='"+t.FullCaption+"' onclick=\"$find('{0}').RenameImage('{1}','{2}','{3}');\">"+t.Caption+"<span style='text-decoration: underline;margin-top: -15px;"+i+"'><img src='../App_Themes/Original/images/IconButton/nuggets/edit.gif'><\/span><\/span>",this._element.id,t.ID,t.FullCaption,"PDF"),r+="<span class='docdate'>"+t.Date+"<\/span>",r+="<span class='docuploadby'>"+t.By+"<\/span>",r+="<\/span>",t.IsDeletePermission,r+="<\/span>",(u+1)%4==0&&(r+=""),t=null;this._intCountPDF=f.Items.length}$("#pdfAttachments").html(r)},getPDFDataError:function(n){this.showError(!0,n.get_ErrorMessage())},OpenPDF:function(n,t){var i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData("controls/Nuggets/GILines");i.set_DataObject("GILines");i.set_DataAction("GetPDFAccessURL");i.addParameter("section",n);i.addParameter("filename",t);i.addDataOK(Function.createDelegate(this,this.getOpenPDFOK));i.addError(Function.createDelegate(this,this.getOpenPDFError));i.addTimeout(Function.createDelegate(this,this.getOpenPDFError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null},getOpenPDFOK:function(n){var i=n._result,t=n._result;window.open(this.setCleanTextBlobURL(t.bothirl),"_blank")},getOpenPDFError:function(n){this.showError(!0,n.get_ErrorMessage())},setCleanTextBlobURL:function(n){return typeof n=="undefined"&&(n=""),n=(n+"").trim(),n=n.replace(/(:PLUS:)/g,"+"),n=n.replace(/(:AND:)/g,"&"),n.replace(/[+]/g,"%2B")},saveGILineImage:function(n,t){var f,u,i;let r=document.getElementById("uploadImages").childNodes[3];for(f=r.childNodes.length,u=0;u<f;u++)r.removeChild(r.firstElementChild);i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData("controls/Nuggets/GILines");i.set_DataObject("GILines");i.set_DataAction("SaveGILineImage");i.addParameter("ID",this._intLineID);i.addParameter("Caption",n.split(".").slice(0,-1).join("."));i.addParameter("TempFile",t);i.addDataOK(Function.createDelegate(this,this.saveAddComplete));i.addError(Function.createDelegate(this,this.saveAddError));i.addTimeout(Function.createDelegate(this,this.saveAddError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null},getImageData:function(){this._intCountImages==0;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetImageData");n.addParameter("ID",this._intLineID);n.addDataOK(Function.createDelegate(this,this.getImageDataOK));n.addError(Function.createDelegate(this,this.getImageDataError));n.addTimeout(Function.createDelegate(this,this.getImageDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getImageDataOK:function(n){var e=n._result,f=n._result,r,i,u,t;if($R_FN.setInnerHTML(this._pnlImagesDragDrop,""),r="",i="margin-left: 57%;",f.Items)for(this.intCountImages=f.Items.length,u=0;u<f.Items.length;u++)t=f.Items[u],t.Caption.length==1&&(i="margin-left:15%;"),t.Caption.length==2&&(i="margin-left:21%;"),t.Caption.length==3&&(i="margin-left:23%;"),t.Caption.length==4&&(i="margin-left:25%;"),t.Caption.length==5&&(i="margin-left:26%;"),t.Caption.length==6&&(i="margin-left:29%;"),t.Caption.length==7&&(i="margin-left:33%;"),t.Caption.length==8&&(i="margin-left:35%;"),t.Caption.length==9&&(i="margin-left:37%;"),t.Caption.length==10&&(i="margin-left:40%;"),t.Caption.length==11&&(i="margin-left:43%;"),t.Caption.length==12&&(i="margin-left:46%;"),t.Caption.length==13&&(i="margin-left:49%;"),t.Caption.length==14&&(i="margin-left:53%;"),t.Caption.length==15&&(i="margin-left:56%;"),r+="<span class='thumbimgbox'>",t.IsDeletePermission&&(r+=String.format("<div class=\"GIDeleteCheckBox\" \"><input type='checkbox' class='chkhideshow' style='margin-top: 3px;margin-left: 25px;' onclick='AddImgAttachmentIds("+t.ID+")' id='BulkImageDelete"+t.ID+"' /><\/div>")),r+=String.format('<img id ="{0}_img{1}" src="{2}" border="0"  />',this._element.id,u,this.getImageSource(t.ID,"t",""+t.ImageName+"","SOURCEIMAGE")),r+="<span class='docdetails'>",r+=String.format("<span class='doctitle' style='color:yellow; cursor: pointer;text-decoration:underline;' title='"+t.FullCaption+"' onclick=\"$find('{0}').RenameImage('{1}','{2}','{3}');\">"+t.Caption+"<span style='margin-top:-16px;"+i+"'><img src='../App_Themes/Original/images/IconButton/nuggets/edit.gif'><\/span><\/span>",this._element.id,t.ID,t.FullCaption,"IMAGE"),r+="<span class='docdate'>"+t.Date+"<\/span>",r+="<span class='docuploadby'>"+t.By,+"</span>",r+=String.format("<br /><a href=\"javascript:void(0);\" onclick=\"$find('{0}').popupImage({1}, 'm','{4}','STOCKIMGFORSAN');\">{2}<\/a> | <a href=\"javascript:void(0);\" onclick=\"$find('{0}').popupImage({1}, 'f','{4}','STOCKIMGFORSAN');\">{3}<\/a>",this._element.id,t.ID,"Medium","Large",t.ImageName),r+="<\/span><\/span>",t.IsDeletePermission,r+="<\/span>",(u+1)%4==0,t=null;$("#imageAttachements").html(r)},getImageDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getImageSource:function(n,t,i,r){return String.format("StockImage.ashx?img={0}&typ={1}&imagename={2}&imagesourcefrom={3}",n,t,i,r)},popupImage:function(n,t,i,r){t=="m"?window.open(this.getImageSource(n,t,i,r),"","left=300,top=250,width=654,height=488,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes"):window.open(this.getImageSource(n,t,i,r),"","left=300,top=170,width=802,height=602,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes")},getAttachementsData:function(){if(SetDefaultUpload(),$(".spanBorder").css("display","initial"),$R_IBTN.enableButton(this._ibtnSave,!1),$R_IBTN.enableButton(this._ibtnSave_Footer,!1),this._NavigateFromGIInfo==!0){var n=this.filedChangesList("SendQuery");n.length>0&&alert("Please make sure all data has been saved by clicking Save and exit or Send query button before leaving this tab.")}$R_IBTN.enableButton(this._ibtnSendQuery,!1);$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").removeClass();$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").removeClass();$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_lblDisabled").addClass("iconButton iconButton_Nugget_Disabled iconButton_Nugget_Save_Disabled iconButton_alignLeft");$("#ctl00_cphMain_ctlLines_ctlDB_ctl18_ibtnSendQuery_hyp").addClass("iconButton iconButton_Nugget iconButton_Nugget_Save iconButton_alignLeft invisible");this.getPDFData();this.getImageData();this._NavigateFromGIInfo=!1;this._IsViewModeOnly==!0&&($("#ddlUpload").attr("disabled","disabled"),$("#uploadPdf").hide())},SaveAndSendQuery:function(){var n,r,t,i;for(this._isSendClicked=!0,n=this.validatePackagingBreakdownData(),r=!1,t=0;t<n.length;t++)for(i=0;i<n.length;i++)if(t!=i&&n[t].BatchCode==n[i].BatchCode&&n[t].DateCode==n[i].DateCode&&n[t].FactorySealed==n[i].FactorySealed&&n[t].MFRLabelId==n[i].MFRLabelId&&n[t].NumberofPacks==n[i].NumberofPacks&&n[t].PackSize==n[i].PackSize&&n[t].PackagingTypeId==n[i].PackagingTypeId){r=!0;break}this.saveClicked();this.allowSaving==!0&&$("#aQueryMessages").click()},ValidatePackageBreakDown:function(){var n=document.getElementById("tbBreakdownTable"),t=n.rows.length;return $("#tbBreakdownTable tr").each(function(){var n=this.id.split(/([0-9]+)/),t=n[1]}),!0},SendApprovalResponce:function(){if($get(this.getFormControlID(this._element.id,"txtResponceBox")).value.length>0)if($find(this.getFormControlID(this._element.id,"ddlQualityApproved")).getValue()!=0||$find(this.getFormControlID(this._element.id,"ddlSalesApproved")).getValue()!=0||$find(this.getFormControlID(this._element.id,"ddlPurchasingApproved")).getValue()!=0){if($find(this.getFormControlID(this._element.id,"ddlQualityApproved")).getValue()>0&&Get_ParentQualityApprovalStatus()==1)return alert("This query is already approved by the quality you can not take another action."),!1;if($find(this.getFormControlID(this._element.id,"ddlPurchasingApproved")).getValue()>0&&Get_ParentPurchasingApprovalStatus()==1)return alert("This query is already approved by the purchasing you can not take another action."),!1;if($find(this.getFormControlID(this._element.id,"ddlSalesApproved")).getValue()>0&&Get_ParentSalesApprovalStatus()==1)return alert("This query is already approved by the sales you can not take another action."),!1;$("#btnSendResponce").attr("disabled","disabled");var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("AddGILineQueryApprovalResponce");n.addParameter("GoodsInId",this._intGIID);n.addParameter("GoodsInLineId",this._intLineID);n.addParameter("QueryMessage",$get(this.getFormControlID(this._element.id,"txtResponceBox")).value);n.addParameter("SalesApprovalStatus",$find(this.getFormControlID(this._element.id,"ddlSalesApproved")).getValue());n.addParameter("PurchasingApprovalStatus",$find(this.getFormControlID(this._element.id,"ddlPurchasingApproved")).getValue());n.addParameter("QualityApprovalStatus",$find(this.getFormControlID(this._element.id,"ddlQualityApproved")).getValue());n.addParameter("GI_QueryId",Get_GIQueryId());n.addParameter("PONumber",$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblPONumber").text());n.addParameter("GoodsInNumber",this._intGoodsInNumber);n.addParameter("CCUserId",$R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs));n.addParameter("CCGroupIDs",$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs));n.addParameter("EnhInspectionStatusId",this._intEnhancedInspectionStatusId);this._blnQAIncludeApproverHtml==!0||this._blnSalesDataIncludeApproverHtml==!0||this._blnPurchaseDataIncludeApproverHtml==!0?n.addParameter("HtmlApproval",replaceExtra($("#QueryMessageP").html())):n.addParameter("HtmlApproval","");n.addParameter("TotalCheckBoxcount",TotalCheckBoxcount());n.addParameter("CheckedTotalCheckBoxcount",CheckedTotalCheckBoxcount());n.addParameter("GetEnableCheckBoxIds",GetEnableCheckBoxIds());n.addDataOK(Function.createDelegate(this,this.SendApprovalResponceOK));n.addError(Function.createDelegate(this,this.SendApprovalResponceError));n.addTimeout(Function.createDelegate(this,this.SendApprovalResponceError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}else alert("Please Select appropriate Approval option in the dropdown.");else alert("Please write something in the discussion box.")},SendApprovalResponceError:function(){$("#btnSendResponce").removeAttr("disabled")},SendApprovalResponceOK:function(){this.RefreshAllQueryMessage();$get(this.getFormControlID(this._element.id,"txtResponceBox")).value="";this.setControlValue("DropDown",this.getFormControlID(this._element.id,"ddlSalesApproved"),"","");this.setControlValue("DropDown",this.getFormControlID(this._element.id,"ddlQualityApproved"),"","");this.setControlValue("DropDown",this.getFormControlID(this._element.id,"ddlPurchasingApproved"),"","");var n=document.getElementById("ResponceApprovalModel");n.style.display="none";$("#btnSendResponce").removeAttr("disabled");this._blnPurchaseDataIncludeApproverHtml=!1;this._blnSalesDataIncludeApproverHtml=!1;this._blnQAIncludeApproverHtml=!1},GetApprovalTabledata:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetApprovalTabledata");n.addParameter("GoodsInId",this._intGIID);n.addParameter("GoodsInLineId",this._intLineID);$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},GetApprovalTabledataError:function(){},GetApprovalTabledataOK:function(){},ActeoneTestPass:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestPass"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkActeoneTestFail")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkActeoneTestNA")).setChecked(!1))},ActeoneTestFail:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestFail"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkActeoneTestPass")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkActeoneTestNA")).setChecked(!1))},ActeoneTestNA:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestNA"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkActeoneTestPass")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkActeoneTestFail")).setChecked(!1))},IsopropryleActeoneTestPass:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkIsoproprylePass"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkIsopropryleFail")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkIsopropryleNA")).setChecked(!1))},IsopropryleActeoneTestFail:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkIsopropryleFail"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkIsoproprylePass")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkIsopropryleNA")).setChecked(!1))},IsopropryleActeoneTestNA:function(){var n=this.getControlValue(this.getFormControlID(this._element.id,"chkIsopropryleNA"),"CheckBox");n==!0&&($find(this.getFormControlID(this._element.id,"chkIsoproprylePass")).setChecked(!1),$find(this.getFormControlID(this._element.id,"chkIsopropryleFail")).setChecked(!1))},validatePackagingBreakdownData:function(){var n=[];return $("tr.data-Breakdown-GI").each(function(){var r=$(this).find(".f-FactorySealed01").is(":checked"),t=$(this).find(".n-NumberofPacks01").val(),i=$(this).find(".p-PackSize01").val(),u=$(this).find(".d-DateCode01").val(),f=$(this).find(".d-BatchCode01").val(),e=$(this).find(".d-PackagingType01").val(),o=$(this).find(".d-MFRLabel01").val(),s=$(this).find(".t-Total01").val(),h={FactorySealed:r,NumberofPacks:parseInt(t),PackSize:parseInt(i),DateCode:u,BatchCode:f,PackagingTypeId:parseInt(e),MFRLabelId:parseInt(o),Total:parseInt(s)};(t>0||i>0)&&n.push(h)}),n},refreshFieldChanged:function(){this._RefreshFieldChanges.txtCorrectPartNo=$R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id,"txtCorrectPartNo")).value);this._RefreshFieldChanges.ddlROHSStatus=$find(this.getFormControlID(this._element.id,"ddlROHSStatus")).getValue();this._RefreshFieldChanges.txtLocation=$get(this.getFormControlID(this._element.id,"txtLocation")).value;this._RefreshFieldChanges.ddlLot=$find(this.getFormControlID(this._element.id,"ddlLot")).getValue();this._RefreshFieldChanges.txtSupplierPart=$get(this.getFormControlID(this._element.id,"txtSupplierPart")).value;this._RefreshFieldChanges.cmbProducts=this.getControlValue(this.getFormControlID(this._element.id,"cmbProducts"),"Combo");this._RefreshFieldChanges.cmbPackage=this.getControlValue(this.getFormControlID(this._element.id,"cmbPackage"),"Combo");this._RefreshFieldChanges.cmbManufacturer=this.getControlValue(this.getFormControlID(this._element.id,"cmbManufacturer"),"Combo");this._RefreshFieldChanges.cmbCountryOfManufacture=this.getControlValue(this.getFormControlID(this._element.id,"cmbCountryOfManufacture"),"Combo");this._RefreshFieldChanges.txtShipInCost=$R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id,"txtShipInCost")).value);this._RefreshFieldChanges.txtQualityControlNotes=$get(this.getFormControlID(this._element.id,"txtQualityControlNotes")).value;this._RefreshFieldChanges.ddlCountingMethod=$find(this.getFormControlID(this._element.id,"ddlCountingMethod")).getValue();this._RefreshFieldChanges.txtPartMarkings=$get(this.getFormControlID(this._element.id,"txtPartMarkings")).value;this._RefreshFieldChanges.txtAccountNotes=$get(this.getFormControlID(this._element.id,"txtAccountNotes")).value;this._RefreshFieldChanges.txtPrice=$get(this.getFormControlID(this._element.id,"txtPrice")).value;this._RefreshFieldChanges.txtPrice_IPO=$get(this.getFormControlID(this._element.id,"txtPrice_IPO")).value;this._RefreshFieldChanges.ddlMsl=$find(this.getFormControlID(this._element.id,"ddlMsl")).getValue();this._RefreshFieldChanges.QueryHICStatus=$find(this.getFormControlID(this._element.id,"QueryHICStatus")).getValue();this._RefreshFieldChanges.ActeoneTestStatus=this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestPass"),"CheckBox")==!0?"1":this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestFail"),"CheckBox")==!0?"2":this.getControlValue(this.getFormControlID(this._element.id,"chkActeoneTestNA"),"CheckBox")==!0?"3":"-1";this._RefreshFieldChanges.txtActeoneTest=$get(this.getFormControlID(this._element.id,"txtActeoneTest")).value;this._RefreshFieldChanges.IsopropryleStatus=this.getControlValue(this.getFormControlID(this._element.id,"chkIsoproprylePass"),"CheckBox")==!0?"1":this.getControlValue(this.getFormControlID(this._element.id,"chkIsopropryleFail"),"CheckBox")==!0?"2":this.getControlValue(this.getFormControlID(this._element.id,"chkIsopropryleNA"),"CheckBox")==!0?"3":"-1";this._RefreshFieldChanges.txtIsopropryle=$get(this.getFormControlID(this._element.id,"txtIsopropryle")).value;this._RefreshFieldChanges.chkReqSerailNo=this.getControlValue(this.getFormControlID(this._element.id,"chkReqSerailNo"),"CheckBox");this._RefreshFieldChanges.chkLotCodeReq=this.getControlValue(this.getFormControlID(this._element.id,"chkLotCodeReq"),"CheckBox").toString();this._RefreshFieldChanges.ddlEnhancedInspection=$find(this.getFormControlID(this._element.id,"ddlEnhancedInspection")).getValue();this._RefreshFieldChanges.txtGeneralInspectionNotes=$R_FN.setCleanTextValue($get(this.getFormControlID(this._element.id,"txtGeneralInspectionNotes")).value);this._RefreshFieldChanges.chkbaking=this.getControlValue(this.getFormControlID(this._element.id,"chkbakingYes"),"CheckBox")==!0?"1":this.getControlValue(this.getFormControlID(this._element.id,"chkbakingNo"),"CheckBox")==!0?"2":this.getControlValue(this.getFormControlID(this._element.id,"chkbakingNA"),"CheckBox")==!0?"3":"-1";this._RefreshFieldChanges.chkInspectionConducted=this.getControlValue(this.getFormControlID(this._element.id,"chkInspectionConducted"),"CheckBox").toString();this._RefreshFieldChanges.HICStatus=$find(this.getFormControlID(this._element.id,"HICStatus")).getValue();var n=this.validatePackagingBreakdownData();this._RefreshFieldChanges.PackBreakDownJSON=JSON.stringify(n);this._RefreshFieldChanges.txtQueryBakingLevel=$get(this.getFormControlID(this._element.id,"txtQueryBakingLevel")).value;this._RefreshFieldChanges.txtPrintDateCode=$get(this.getFormControlID(this._element.id,"txtPrintDateCode")).value;this._RefreshFieldChanges.txtQuantity=$get(this.getFormControlID(this._element.id,"txtQuantity")).value;this._RefreshFieldChanges=JSON.parse(JSON.stringify(this._RefreshFieldChanges).replace(/\:null/gi,':""'));this._intEnhancedInspectionStatusId=$find(this.getFormControlID(this._element.id,"ddlEnhancedInspection")).getValue();this._intGIlineBarcodesStatusId=$find(this.getFormControlID(this._element.id,"ddlGILineBarcodesStatus")).getValue()},ChangeApprover:function(){if($find(this.getFormControlID(this._element.id,"ddlNewSalesApprover")).getValue()==null&&$find(this.getFormControlID(this._element.id,"ddlNewPurchasingApprover")).getValue()==null)return alert("Please select new approver"),!1;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("ChangeApprover");n.addParameter("GI_QueryId",Get_GIQueryId());n.addParameter("NewSalesApproverId",$find(this.getFormControlID(this._element.id,"ddlNewSalesApprover")).getValue());n.addParameter("NewPurchaseApproverId",$find(this.getFormControlID(this._element.id,"ddlNewPurchasingApprover")).getValue());n.addDataOK(Function.createDelegate(this,this.ChangeApproverOK));n.addError(Function.createDelegate(this,this.ChangeApproverError));n.addTimeout(Function.createDelegate(this,this.ChangeApproverError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},ChangeApproverError:function(){},ChangeApproverOK:function(){this.RefreshAllQueryMessage();this.RefreshAllApprovals();this.setControlValue("DropDown",this.getFormControlID(this._element.id,"ddlNewSalesApprover"),"","");this.setControlValue("DropDown",this.getFormControlID(this._element.id,"ddlNewPurchasingApprover"),"","");var n=document.getElementById("ConfigureApproverModel");n.style.display="none"},findEnhancedInspectionStatus:function(){var t=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlEnhancedInspection_ddl option:selected").val(),n=$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_ddlEnhancedInspection_ddl option:selected").text();t>0&&(n.toLowerCase()=="Enhanced Inspection".toLowerCase()||n.toLowerCase()=="Send for Outwork".toLowerCase()||n.toLowerCase()=="EI And Outwork".toLowerCase())?$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblHeaderEnhancedInpection").text("Yes"):$("#ctl00_cphMain_ctlLines_ctlDB_ctl14_frmEdit_ctlDB_lblHeaderEnhancedInpection").text("No")},RefreshAllApprovals:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("GetGIApprovals");n.addParameter("GoodsInId",this._intGIID);n.addParameter("GoodsInLineId",this._intLineID);n.addDataOK(Function.createDelegate(this,this.RefreshAllApprovalsOK));n.addError(Function.createDelegate(this,this.RefreshAllApprovalsError));n.addTimeout(Function.createDelegate(this,this.RefreshAllApprovalsError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},RefreshAllApprovalsError:function(){},RefreshAllApprovalsOK:function(n){var i,u,f,r,t;if(res=n._result,ManageApprover=res,i="",u="",$("#ApprovalBody").html(""),f="",res.GIApprovals.length>0)for($("#btnApprovalsChange").click(function(){ConfigureApprover(ManageApprover.GIApprovals[0].Gi_QueryId,ManageApprover.GIApprovals[0].CurrentPurchasingApprover,ManageApprover.GIApprovals[0].CurrentSalesApprover,ManageApprover.GIApprovals[0].ParentPurchaseApprovalStatus,ManageApprover.GIApprovals[0].ParentSalesApprovalStatus)}),$("#spnApprovalsChange").show(),r=0;r<res.GIApprovals.length;r++)t=res.GIApprovals[r],$("#lblSalesStatus").text(t.SalesApprovalStatus),$("#lblPurchasingStatus").text(t.PurchasingApprovalStatus),$("#lblQualityStatus").text(t.QualityApprovalStatus),t.QualityApprovalStatus.toUpperCase()=="N/A"?$("#lblQualityUser").text("Approval not sent"):$("#lblQualityUser").text("Quality inbox"),t.PurchasingApprovalStatus.toUpperCase()=="N/A"?$("#lblPurchasingUser").text("Approval not sent"):$("#lblPurchasingUser").text(t.CurrentPurchasingApprover),t.SalesApprovalStatus.toUpperCase()=="N/A"?$("#lblSalesUser").text("Approval not sent"):$("#lblSalesUser").text(t.CurrentSalesApprover),i+="<tr>",i+="<td>"+t.RaisedBy+"<\/td>",i+="<td>"+t.ApprovalName+"<\/td>",i+=t.Department=="Final Messase"?"<td><\/td>":"<td>"+t.ApprovedDate+"<\/td>",i+=t.Status=="Request Declined"?"<td><span class='redtext'>"+t.Status+"<\/span>":t.Status=="Request Approved"?"<td><span class='greentext'>"+t.Status+"<\/span>":t.Status=="Partial Approved"?"<td><span class='greentext'>"+t.Status+"<\/span>":"<td><span class=>"+t.Status+"<\/span><\/td>",i+="<\/tr>",t=null;else i+="<p> No Data found.<\/p>",$("#lblSalesUser").text("Not Set"),$("#lblPurchasingUser").text("Not Set"),$("#lblQualityUser").text("Not Set"),$("#spnApprovalsChange").hide(),$("#lblSalesStatus").text("N/A"),$("#lblPurchasingStatus").text("N/A"),$("#lblQualityStatus").text("N/A");$("#ApprovalBody").append(i);QueryMessageDetails=""},DraftQueryMessage:function(){var t="",n;$get(this.getFormControlID(this._element.id,"TxtMessageBox")).value.length>0?t=$get(this.getFormControlID(this._element.id,"TxtMessageBox")).value:$get(this.getFormControlID(this._element.id,"txtResponceBox")).value.length>0&&(t=$get(this.getFormControlID(this._element.id,"txtResponceBox")).value);t.length>0?(n=new Rebound.GlobalTrader.Site.Data,n.set_PathToData("controls/Nuggets/GILines"),n.set_DataObject("GILines"),n.set_DataAction("DraftGILineQueryMessage"),n.addParameter("GoodsInId",this._intGIID),n.addParameter("GoodsInLineId",this._intLineID),n.addParameter("QueryMessage",t),n.addParameter("GI_QueryId",Get_GIQueryId()),n.addDataOK(Function.createDelegate(this,this.DraftQueryMessageOK)),n.addError(Function.createDelegate(this,this.DraftQueryMessageError)),n.addTimeout(Function.createDelegate(this,this.DraftQueryMessageError)),$R_DQ.addToQueue(n),$R_DQ.processQueue(),n=null):alert("Please write something in the discussion box.")},DraftQueryMessageError:function(){},DraftQueryMessageOK:function(){alert("Saved to draft")},deletePDF:function(n,t){if(confirm("Do you want to delete PDF.")==!0){var i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData("Controls/Nuggets/GILines");i.set_DataObject("GILines");i.set_DataAction("DeletePDF");i.addParameter("id",n);i.addParameter("pdffilename",t);i.addDataOK(Function.createDelegate(this,this.deletePDFOK));i.addError(Function.createDelegate(this,this.deletePDFError));i.addTimeout(Function.createDelegate(this,this.deletePDFError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null}},deletePDFError:function(){},deletePDFOK:function(){this.getAttachementsData()},deleteImage:function(n,t){if(confirm("Do you want to delete Image.")==!0){var i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData("Controls/Nuggets/GILines");i.set_DataObject("GILines");i.set_DataAction("DeleteImage");i.addParameter("id",n);i.addParameter("ImageFileName",t);i.addDataOK(Function.createDelegate(this,this.deleteImageOK));i.addError(Function.createDelegate(this,this.deleteImageError));i.addTimeout(Function.createDelegate(this,this.deleteImageError));$R_DQ.addToQueue(i);$R_DQ.processQueue();i=null}},deleteImageError:function(){},deleteImageOK:function(){this.getAttachementsData()},RenameImage:function(n,t,i){$get(this.getFormControlID(this._element.id,"txtRenameCaption")).value=t;ShowRenamePopUp(n,t);this._intAttachmentId=n;this._strAttachmentType=i},RenameCaption:function(){if($get(this.getFormControlID(this._element.id,"txtRenameCaption")).value.length==0)return alert("Please write proper caption."),!1;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("RenameCaption");n.addParameter("AttachmentId",this._intAttachmentId);n.addParameter("Caption",$get(this.getFormControlID(this._element.id,"txtRenameCaption")).value);n.addParameter("AttachmentType",this._strAttachmentType);n.addDataOK(Function.createDelegate(this,this.RenameCaptionOK));n.addError(Function.createDelegate(this,this.RenameCaptionError));n.addTimeout(Function.createDelegate(this,this.RenameCaptionError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},RenameCaptionError:function(){},RenameCaptionOK:function(){this.getAttachementsData();var n=document.getElementById("RenameModel");n.style.display="none"},BulkAttachmentDelete:function(){if(confirm("Are you sure to delete selected attachments.")==!0){$("#divLoader").show();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/GILines");n.set_DataObject("GILines");n.set_DataAction("BulkAttachmentDelete");n.addParameter("PdfAttachments",GetPdfIds());n.addParameter("ImageAttachments",GetImageIds());n.addDataOK(Function.createDelegate(this,this.BulkAttachmentDeleteOK));n.addError(Function.createDelegate(this,this.BulkAttachmentDeleteError));n.addTimeout(Function.createDelegate(this,this.BulkAttachmentDeleteError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},BulkAttachmentDeleteError:function(){},BulkAttachmentDeleteOK:function(){this.getAttachementsData();ResetAttachmentData();$("#btnBulkAttacheDelete").hide();$("#divLoader").hide()},MadeChangeInQAddl:function(){QAData=$find(this.getFormControlID(this._element.id,"ddlQualityApproved")).getValue();this._blnQAIncludeApproverHtml=QAData==null?!1:!0},MadeChangeInSalesddl:function(){SalesData=$find(this.getFormControlID(this._element.id,"ddlSalesApproved")).getValue();this._blnSalesDataIncludeApproverHtml=SalesData==null?!1:!0},MadeChangeInPurchaseddl:function(){PurchaseData=$find(this.getFormControlID(this._element.id,"ddlPurchasingApproved")).getValue();this._blnPurchaseDataIncludeApproverHtml=PurchaseData==null?!1:!0},ChangeApproverResponse:function(n,t,i,r,u,f,e,o,s,h,c){return htmlData=$R_FN.setCleanTextValue(n),t==!0&&(htmlData=htmlData.replace('id="C1"','id="C1" checked="checked"'),SetEnableCheckBoxIds("1")),i==!0&&(htmlData=htmlData.replace('id="C2"','id="C2" checked="checked"'),SetEnableCheckBoxIds("2")),r==!0&&(htmlData=htmlData.replace('id="C3"','id="C3" checked="checked"'),SetEnableCheckBoxIds("3")),u==!0&&(htmlData=htmlData.replace('id="C4"','id="C4" checked="checked"'),SetEnableCheckBoxIds("4")),f==!0&&(htmlData=htmlData.replace('id="C5"','id="C5" checked="checked"'),SetEnableCheckBoxIds("5")),e==!0&&(htmlData=htmlData.replace('id="C6"','id="C6" checked="checked"'),SetEnableCheckBoxIds("6")),o==!0&&(htmlData=htmlData.replace('id="C7"','id="C7" checked="checked"'),SetEnableCheckBoxIds("7")),s==!0&&(htmlData=htmlData.replace('id="C8"','id="C8" checked="checked"'),SetEnableCheckBoxIds("8")),h==!0&&(htmlData=htmlData.replace('id="C9"','id="C9" checked="checked"'),SetEnableCheckBoxIds("9")),c==!0&&(htmlData=htmlData.replace('id="C10"','id="C10" checked="checked"'),SetEnableCheckBoxIds("10")),htmlData}};Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);