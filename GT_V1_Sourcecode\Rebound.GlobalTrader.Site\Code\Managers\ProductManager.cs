using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.Site {
	public class ProductManager {

		public static Double GetCurrentDutyRateAtDate(int intProductID, DateTime dtmDatePoint) {
			Double dbl = 0;
			BLL.DutyRate dr = BLL.DutyRate.GetCurrentForProduct(intProductID, dtmDatePoint);
			if (dr != null) dbl = (double)dr.DutyRateValue;
			return dbl;
		}

	}
}
