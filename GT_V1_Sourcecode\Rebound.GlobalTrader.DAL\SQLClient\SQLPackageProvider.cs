﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlPackageProvider : PackageProvider {
		/// <summary>
		/// Delete Package
		/// Calls [usp_delete_Package]
		/// </summary>
		public override bool Delete(System.Int32? packageId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_delete_Package", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@PackageId", SqlDbType.Int).Value = packageId;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to delete Package", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// DropDown 
		/// Calls [usp_dropdown_Package]
        /// </summary>
		public override List<PackageDetails> DropDown() {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_dropdown_Package", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<PackageDetails> lst = new List<PackageDetails>();
				while (reader.Read()) {
					PackageDetails obj = new PackageDetails();
					obj.PackageId = GetReaderValue_Int32(reader, "PackageId", 0);
					obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
					obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
					lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Packages", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
		/// <summary>
		/// Create a new row
		/// Calls [usp_insert_Package]
		/// </summary>
		public override Int32 Insert(System.String packageName, System.String packageDescription, System.Int32? updatedBy) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_insert_Package", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@PackageName", SqlDbType.NVarChar).Value = packageName;
				cmd.Parameters.Add("@PackageDescription", SqlDbType.NVarChar).Value = packageDescription;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@PackageId", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (Int32)cmd.Parameters["@PackageId"].Value;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to insert Package", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// Get 
		/// Calls [usp_select_Package]
        /// </summary>
		public override PackageDetails Get(System.Int32? packageId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_select_Package", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cmd.Parameters.Add("@PackageId", SqlDbType.Int).Value = packageId;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
				if (reader.Read()) {
					//return GetPackageFromReader(reader);
					PackageDetails obj = new PackageDetails();
					obj.PackageId = GetReaderValue_Int32(reader, "PackageId", 0);
					obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
					obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
					return obj;
				} else {
					return null;
				}
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Package", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// GetList 
		/// Calls [usp_selectAll_Package]
        /// </summary>
		public override List<PackageDetails> GetList() {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_selectAll_Package", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd);
				List<PackageDetails> lst = new List<PackageDetails>();
				while (reader.Read()) {
					PackageDetails obj = new PackageDetails();
					obj.PackageId = GetReaderValue_Int32(reader, "PackageId", 0);
					obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
					obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
					obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
					obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
					lst.Add(obj);
					obj = null;
				}
				return lst;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get Packages", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


        /// <summary>
        /// GetClientList 
        /// Calls [usp_select_ClientSetup_All]
        /// </summary>
        public override List<PackageDetails> GetClientList()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_ClientSetup_All", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PackageDetails> lst = new List<PackageDetails>();
                while (reader.Read())
                {
                    PackageDetails obj = new PackageDetails();
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientBillTo = GetReaderValue_String(reader, "ClientBillTo", "");
                    obj.CustomerCode = GetReaderValue_String(reader, "CustomerCode", "");
                    obj.TermsNo = GetReaderValue_Int32(reader, "TermsNo", 0);
                    obj.ShipViaNo = GetReaderValue_Int32(reader, "ShipViaNo", 0);
                    obj.TaxNo = GetReaderValue_Int32(reader, "TaxNo", 0);
                    obj.ShipViaName = GetReaderValue_String(reader, "ShipViaName", "");
                    obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.TaxName = GetReaderValue_String(reader, "TaxName", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.addressName = GetReaderValue_String(reader, "AddressName", "");
                    obj.city = GetReaderValue_String(reader, "City", "");
                    obj.line1 = GetReaderValue_String(reader, "Line1", "");
                    obj.line2 = GetReaderValue_String(reader, "Line2", "");
                    obj.line3 = GetReaderValue_String(reader, "Line3", "");
                    obj.county = GetReaderValue_String(reader, "County", "");
                    obj.state = GetReaderValue_String(reader, "State", "");
                    obj.zip = GetReaderValue_String(reader, "ZIP", "");
                    obj.countryNo = GetReaderValue_Int32(reader, "CountryNo", 0);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //code add for Master login list
        /// <summary>
        /// GetClientList 
        /// Calls [usp_select_ClientSetup_All]
        /// </summary>
        public override List<PackageDetails> GetMasterLoginList()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_MasterLoginSetup_All", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PackageDetails> lst = new List<PackageDetails>();
                while (reader.Read())
                {
                    PackageDetails obj = new PackageDetails();
                    obj.MasterLoginId = GetReaderValue_Int32(reader, "MasterLoginId", 0);
                    obj.ADLoginName = GetReaderValue_String(reader, "ADLoginName", "");
                    obj.EmployeeName = GetReaderValue_String(reader, "EmployeeName", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get MasterLogin", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //doe end


        /// <summary>
        /// Get 
        /// Calls [usp_select_ClientSetupById]
        /// </summary>
        public override PackageDetails GetClient(System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_ClientSetupById", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    PackageDetails obj = new PackageDetails();
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientId", 0);
                    obj.ClientBillTo = GetReaderValue_String(reader, "ClientBillTo", "");
                    obj.CustomerCode = GetReaderValue_String(reader, "CustomerCode", "");
                    obj.TermsNo = GetReaderValue_Int32(reader, "TermsNo", 0);
                    obj.ShipViaNo = GetReaderValue_Int32(reader, "ShipViaNo", 0);
                    obj.TaxNo = GetReaderValue_Int32(reader, "TaxNo",0);
                    obj.ShipViaName = GetReaderValue_String(reader, "ShipViaName", "");
                    obj.TermsName = GetReaderValue_String(reader, "TermsName", "");
                    obj.TaxName = GetReaderValue_String(reader, "TaxName", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    return obj;


                    //pk.AddressName = GetFormValue_String("AddressName");
                    //pk.AddressLine1 = GetFormValue_String("AddressLine1");
                    //pk.AddressLine2 = GetFormValue_String("AddressLine2");
                    //pk.AddressLine3 = GetFormValue_String("AddressLine3");
                    //pk.AddressLine3 = GetFormValue_String("Town");
                    //pk.County = GetFormValue_String("County");
                    //pk.State = GetFormValue_String("State");
                    //pk.Country = GetFormValue_NullableInt("Country");
                    //pk.Postcode = GetFormValue_String("Postcode");
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [usp_select_ClientSetupById]
        /// </summary>
        public override PackageDetails GetMasterLogin(System.Int32? MasterLoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_MasterLoginSetupById", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@MasterLoginId", SqlDbType.Int).Value = MasterLoginId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    PackageDetails obj = new PackageDetails();
                    obj.MasterLoginId = GetReaderValue_Int32(reader, "MasterLoginId", 0);
                    obj.ADLoginName = GetReaderValue_String(reader, "ADLoginName", "");
                    obj.EmployeeName = GetReaderValue_String(reader, "EmployeeName", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.ClientId = GetReaderValue_Int32(reader, "ClientNo", 0);
                    return obj;

                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Master Login", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Package
        /// Calls [usp_update_Package]
        /// </summary>
        public override bool Update(System.Int32? packageId, System.String packageName, System.String packageDescription, System.Boolean? inactive, System.Int32? updatedBy) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_update_Package", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@PackageId", SqlDbType.Int).Value = packageId;
				cmd.Parameters.Add("@PackageName", SqlDbType.NVarChar).Value = packageName;
				cmd.Parameters.Add("@PackageDescription", SqlDbType.NVarChar).Value = packageDescription;
				cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
				cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to update Package", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}


        /// <summary>
        /// UpdateClient 
        /// Calls [usp_update_ClientDetail]
        /// </summary>
        public override bool UpdateClient(System.Int32? clientId, System.String clientBillTo, System.String customerCode, Int32? termsNo, Int32? shipViaNo, Int32? taxNo, System.Boolean? inactive, System.Int32? updatedBy, System.String addressName, System.String line1, System.String line2, System.String line3, System.String city, System.String county, System.String state, System.Int32? countryNo, System.String zip)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ClientDetail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ClientBillTo", SqlDbType.NVarChar).Value = clientBillTo;
                cmd.Parameters.Add("@CustomerCode", SqlDbType.NVarChar).Value = customerCode;
                cmd.Parameters.Add("@TermsNo", SqlDbType.Int).Value = termsNo;
                cmd.Parameters.Add("@ShipViaNo", SqlDbType.Int).Value = shipViaNo;
                cmd.Parameters.Add("@TaxNo", SqlDbType.Int).Value = taxNo;

                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@AddressName", SqlDbType.NVarChar).Value = addressName;
                cmd.Parameters.Add("@Line1", SqlDbType.NVarChar).Value = line1;
                cmd.Parameters.Add("@Line2", SqlDbType.NVarChar).Value = line2;
                cmd.Parameters.Add("@Line3", SqlDbType.NVarChar).Value = line3;
                cmd.Parameters.Add("@County", SqlDbType.NVarChar).Value = county;
                cmd.Parameters.Add("@City", SqlDbType.NVarChar).Value = city;
                cmd.Parameters.Add("@State", SqlDbType.NVarChar).Value = state;
                cmd.Parameters.Add("@CountryNo", SqlDbType.Int).Value = countryNo;
                cmd.Parameters.Add("@ZIP", SqlDbType.NVarChar).Value = zip;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update client details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// UpdateClient 
        /// Calls [usp_update_MasterLoginDetail]
        /// </summary>
        public override bool UpdateMasterLogin(System.Int32? MasterLoginId,System.Boolean? inactive)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_MasterLoginDetail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@MasterLoginId", SqlDbType.Int).Value = MasterLoginId;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Master Login details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// AutoSearch 
		/// Calls [usp_autosearch_Package]
        /// </summary>
        public override List<PackageDetails> AutoSearch(System.String nameSearch, Boolean? showInactive)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Package", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@ShowInactive", SqlDbType.Bit).Value = showInactive;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PackageDetails> lst = new List<PackageDetails>();
                while (reader.Read())
                {
                    PackageDetails obj = new PackageDetails();
                    obj.PackageId = GetReaderValue_Int32(reader, "PackageId", 0);
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Packages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<PackageDetails> AllSearch()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_allsearch_Package", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PackageDetails> lst = new List<PackageDetails>();
                while (reader.Read())
                {
                    PackageDetails obj = new PackageDetails();
                    obj.PackageId = GetReaderValue_Int32(reader, "PackageId", 0);
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get AllSearch Packages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //Part ECCN Mapped data Search
        /// <summary>
        /// AutoSearch 
        /// Calls [usp_autosearch_Package]
        /// </summary>
        public override List<PackageDetails> AutoSearchPartEccnCode(System.String nameSearch, Boolean? showInactive)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_PartEccnMapped", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PackageDetails> lst = new List<PackageDetails>();
                while (reader.Read())
                {
                    PackageDetails obj = new PackageDetails();
                    obj.ECCNId = GetReaderValue_Int32(reader, "ECCNId", 0);
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.ECCNStatus = GetReaderValue_Boolean(reader, "ECCNStatus", false);
                    obj.ECCNWarning = GetReaderValue_String(reader, "ECCNWarning", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Packages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

    }
}