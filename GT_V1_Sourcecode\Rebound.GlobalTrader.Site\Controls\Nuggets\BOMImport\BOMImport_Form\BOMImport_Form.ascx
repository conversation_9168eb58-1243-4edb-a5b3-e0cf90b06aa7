<%@ Control Language="C#" CodeBehind="BOMImport_Form.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Import Namespace="System.Configuration" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<link href="css/uploadfile.css" rel="stylesheet">
<link href="css/BOMImport.css" rel="stylesheet" />
<script src="js/jquery.uploadfile.js"></script>
<script src="js/jquery.dataTables.min.js"></script>
<script src="js/dataTables.cellEdit.js"></script>
<link href="css/jquery.dataTables.min.css" rel="stylesheet" />
<link href="css/jquery-ui.css.css" rel="stylesheet" />
<script src="js/jquery-ui.js"></script>
<%--<script src="css/jquery-ui.js" integrity="sha256-xI/qyl9vpwWFOXz7+x/9WkG5j/SVnSw21viy8fWwbeE=" crossorigin="anonymous"></script>--%>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
    <Links>
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>
    <Content>

        <ReboundUI_Table:Form ID="frm" runat="server">
            <%--bom info section --%>
            <asp:TableRow>
                <asp:TableCell ColumnSpan="2">
                <div class="input-color">
                    Note : <div class="color-box" style="background-color: red;"></div><label> Red background color indicates error in the line item.</label>
                    <a id="ibtnShowErrorList" title="Show error list" class="error-list"></a>
                    </div>
                    <hr />
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    BOM Name
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblBomName" runat="server">Testing BOM name</asp:Label>
                    <div class="recordDetail">
                        <b><span>Record Processed : </span>
                            <asp:Label ID="lblRecordProcessed" runat="server"></asp:Label></b><br />
                        <b><span>Record Remaining : </span>
                            <asp:Label ID="lblRecordRemaining" runat="server" /></b>
                    </div>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    Company Name
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblCompanyName" runat="server">Testing Company Name</asp:Label>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    Contact 
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblContact" runat="server">Testing contact</asp:Label>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    Default Currency
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblDefaultCurrency" runat="server">Default Currency</asp:Label>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    Sales Person
                </asp:TableCell>
                <asp:TableCell>
                    <asp:Label ID="lblSalesperson" runat="server">Sales Person</asp:Label>
                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell></asp:TableCell>
            </asp:TableRow>
            <%--file upload section --%>
            <asp:TableRow>
                <asp:TableCell></asp:TableCell>
                <asp:TableCell>
                <div id="Imagesingleupload4" >Upload</div>
                <script type="text/javascript">
                    formControlId = "<%=this.ClientID%>";
                    loginId = "<%=SessionManager.LoginID%>";
                    <%--handlerUrl = "<%= ConfigurationManager.AppSettings["BOMImportHandler"] %>";--%>


                    var dragdropObj = $("#Imagesingleupload4").uploadFile({
                        url: "DocImage.ashx?mxs=1&type=EXCELUPLOAD&IsDragDrop=true",
                        allowedTypes: "csv,xlsx,xls",
                        fileName: "myfile",
                        section: "Bom_BomImport_BMI",
                        autoSubmit: true,
                        multiple: false,
                        maxFileSize: 7900000,
                        showStatusAfterSuccess: false,
                        showCancel: true,
                        showDone: true,
                        async: false,
                        uploadDiv: "excelipload",
                        timeout: 60000,
                        dynamicFormData: function () {
                            var data = { DocId: $find("<%=this.ClientID%>")._intSalesOrderID, section: "Bom_BomImport_BMI" }
                            return data;
                        },
                        //maxFileCount: 1,
                        onSuccess: function (files, data, xhr) {
                            var originalFilename = '';
                            var generatedFilename = '';
                            originalFilename = files[0];
                            var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
                            generatedFilename = json.FileName;
                            $find("<%=this.ClientID%>").impotExcelData(originalFilename, generatedFilename, bomId, excelColumns);

                            var StepMessage = "";
                            StepMessage = "(2) User need to map header column and then cick on lock mapping button. Or if the mapping already locked then click on Process button.";
                            $("#lblbomstep").text(StepMessage);

                        },
                        onSelect: function (fup) {
                            var result = true;
                            $find("<%=this.ClientID%>")._dragobj = dragdropObj;

                            return result;
                        }
                    });
                </script>
                <div class="clearing"></div>
                </asp:TableCell>

            </asp:TableRow>
            <%--datatable section --%>
            <asp:TableRow>
                <asp:TableCell ColumnSpan="4">
                <div class="input-color">
                    
                    Working step of Bom Tool : <div class="color-box" style="background-color: #17a2b8;"></div><label> Blue background color indicates Working Prcess of BOM Tool.</label>
                    <hr />
                   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span><label for="" id="lblbomstep" style="background-color: #17a2b8; color:#000";" ></label></span>
                     &nbsp;&nbsp;<a id="ibtnShowErrorList2" title="Show error list" class="error-list" style="display:none;"></a>
                    </div>
                    <br />
                <div  style="overflow: scroll; width: 1100px; height: 442px;display:none" class="display">
                    <table class="data" id="BOMResult">
                        <thead>
                        </thead>
                    </table>
                </div><br />
                <div style="width: 100%;margin-left: 20%;margin-top:30px;" class="display">
                 <input type="button" id="btnRearrange" value="Rearrange" class="button"/> &nbsp&nbsp
                 <input type="button" id="btnLockMapping" value="Lock Mapping" class="button"/> &nbsp&nbsp
                 <input type="button" id="btnReset" value="Reset" class="button"/> &nbsp&nbsp
                 <%--<input type="button" id="btnSaveTempData" value="Save Temp Data" class="SaveTempData" class="LockMapping" class="Process"/> --%>
                 <input type="button" id="btnProcess" value="Process" class="button"/> 
                 
                </div>
                </asp:TableCell>
            </asp:TableRow>
            <%--Add/Edit section --%>
            <asp:TableRow>
                <asp:TableCell ColumnSpan="4">

                    <div class="hover_bkgr_fricc" id="dvAddEdit">
                        <span class="helper"></span>
                        <div>
                            <div class="popupCloseButton">X</div>

                            <table cellspacing="0" cellpadding="0" border="0" id="tbAddEdit" class="tbAddEdit">
                                <tr>
                                    <td colspan="2">
                                        <div id="dvValidationError" class="errorSummary">
                                            There were some problems with your form<br>
                                            Please check below and try again.<div id="ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ctl04"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <div class="formHeader">
                                            <h4>Enter the detail of the new Requirement
                                            </h4>
                                        </div>
                                    </td>
                                </tr>



                                <tr>
                                    <td class="title">Customer
                                    </td>
                                    <td>
                                        <label for="customer" id="lblCustomer"></label>
                                    </td>

                                </tr>
                                <tr>
                                    <td class="title">Quantity *
                                    </td>
                                    <td>
                                        <input type="text" id="txtQty" class="txtNSelect" />
                                    </td>
                                    <td>
                                        <label for="" id="lblPartNo"></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Partial Quantity Acceptable
                                    </td>
                                    <td>
                                        <input type="checkbox" id="chkPartialQuantityAcceptable" />
                                    </td>
                                </tr>
                                <%--<tr>
                                    <td class="title">Part No *
                                    </td>
                                    <td>
                                        <input type="text" id="txtPartNo" class="txtNSelect" />
                                    </td>
                                </tr>--%>
                                <%--<tr>
                                    <td colspan="2">--%>
                                <ReboundUI_Form:FormField ID="ctlPartNo" runat="server" FieldID="txtPartNo" ResourceTitle="PartNo" IsRequiredField="true">
                                    <Field>
                                        <ReboundUI:ReboundTextBox ID="txtPartNo" runat="server" Width="250" UppercaseOnly="true" placeholder="Type 3 chars to search" />
                                        <%--<ReboundAutoSearch:PartSearch ID="autPartNo" runat="server" RelatedTextBoxID="txtPartNo" ResultsHeight="150" Width="250" ResultsActionType="RaiseEvent" TriggerByButton="true" />--%>
                                        <asp:Label ID="btn1" runat="server" Style="cursor: pointer;" CssClass="PartDetailsGridGoBtn" />
                                        <asp:Label ID="btn2" Text="Clear" runat="server" Style="cursor: pointer;" CssClass="PartDetailsGridGoBtn2" />
                                        <asp:Label ID="lblError" Text="Type 3 chars to search" runat="server" CssClass="PartDetailsGridGoError" />
                                        <%--&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp
                                                <label for="" id="lblPartNo" style="width:100px"></label>--%>
                                    </Field>
                                </ReboundUI_Form:FormField>

                                <ReboundUI_Form:FormField ID="ctlPartDetail" runat="server" FieldID="tblPartdetails">
                                    <Field>
                                        <asp:Panel ID="pnlPartdetails" runat="server" CssClass="GridPartdetails">
                                            <ReboundUI:FlexiDataTable ID="tblPartdetails" runat="server" PanelHeight="100" />
                                        </asp:Panel>

                                    </Field>
                                </ReboundUI_Form:FormField>
                                <%-- </td>
                                </tr>--%>
                                <tr>
                                    <td class="title">Obsolete
                                    </td>
                                    <td>
                                        <input type="radio" name="rblObsolete" value="1">
                                        Yes
                         <input type="radio" name="rblObsolete" value="0" checked="checked">
                                        No
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Last time buy
                                    </td>
                                    <td>
                                        <input type="radio" name="rblLastTimeBuy" value="1">
                                        Yes
                         <input type="radio" name="rblLastTimeBuy" value="0" checked="checked">
                                        No
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Refurbs Acceptable
                                    </td>
                                    <td>
                                        <input type="radio" name="rblRefurbsAcceptable" value="1">
                                        Yes
                         <input type="radio" name="rblRefurbsAcceptable" value="0" checked="checked">
                                        No
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Testing Required
                                    </td>
                                    <td>
                                        <input type="radio" name="rblTestingRequired" value="1">
                                        Yes
                         <input type="radio" name="rblTestingRequired" value="0" checked="checked">
                                        No
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Alternatives Accepted
                                    </td>
                                    <td>
                                        <input type="radio" name="rblAltAccepted" value="1">
                                        Yes
                         <input type="radio" name="rblAltAccepted" value="0" checked="checked">
                                        No
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Regular/Repeat business
                                    </td>
                                    <td>
                                        <input type="radio" name="rblRegularRepeatBusiness" value="1">
                                        Yes
                         <input type="radio" name="rblRegularRepeatBusiness" value="0" checked="checked">
                                        No
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">RoHS
                                    </td>
                                    <td>
                                        <select id="ddlRoHS" class="txtNSelect"></select>
                                    </td>
                                    <td style="width: auto;">
                                        <label for="" id="lblRoHS"></label>
                                    </td>

                                </tr>
                                <tr>
                                    <td class="title">Cust Part No
                                    </td>
                                    <td>
                                        <input type="text" id="txtCustPartNo" class="txtNSelect" />
                                    </td>
                                </tr>

                                <ReboundUI_Form:FormField ID="ctlManufacturer" runat="server" FieldID="cmbManufacturer" ResourceTitle="Manufacturer" IsRequiredField="true">
                                    <Field>
                                        <table>
                                            <tr>
                                                <td style="width: 200px;">
                                                    <ReboundUI:Combo ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" />
                                                </td>
                                                <td style="width: auto;">
                                                    <label for="" id="lblManufacturer"></label>
                                                </td>
                                            </tr>
                                        </table>
                                    </Field>

                                </ReboundUI_Form:FormField>

                                <tr>
                                    <td class="title">Date Code
                                    </td>
                                    <td>
                                        <input type="text" id="txtDateCode" class="txtNSelect" />
                                    </td>
                                </tr>
                                <%--<tr>
                                    <td colspan="2">--%>
                                <ReboundUI_Form:FormField ID="ctlProduct" runat="server" FieldID="cmbProducts" ResourceTitle="Product" IsRequiredField="true">
                                    <Field>
                                        <table>
                                            <tr>
                                                <td style="width: 300px;">
                                                    <ReboundUI:Combo ID="cmbProducts" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="450" PanelHeight="250" AutoSearchControlType="Products" />
                                                </td>
                                                <td style="width: 300px;">
                                                    <label for="" id="lblProducts"></label>
                                                </td>
                                            </tr>
                                        </table>
                                    </Field>
                                </ReboundUI_Form:FormField>
                                <%--</td>
                                </tr>--%>
                                <%--<tr>
                                    <td class="title">Product *
                                    </td>
                                    <td>
                                        <input type="text" id="txtProduct" class="txtNSelect" />
                                    </td>
                                </tr>--%>
                                <tr>
                                    <td class="title">Package
                                    </td>
                                    <td>
                                        <select id="ddlPackage" class="txtNSelect"></select>
                                    </td>
                                    <td style="width: auto;">
                                        <label for="" id="lblPackage"></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Customer Target Price *
                                    </td>
                                    <td>
                                        <input type="text" id="txtCustomerTargetPrice" class="txtNSelect" />
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Competitor Best offer
                                    </td>
                                    <td>
                                        <input type="text" id="txtCompetitorBestPrice" class="txtNSelect" />
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <ReboundUI_Form:FormField ID="ctlDateRequired" runat="server" FieldID="txtDateRequired" ResourceTitle="CustDateRequired" IsRequiredField="true">
                                            <Field>
                                                <ReboundUI:ReboundTextBox ID="txtDateRequired" runat="server" Width="120" />
                                                <ReboundUI:Calendar ID="calDateRequired" runat="server" RelatedTextBoxID="txtDateRequired" />
                                            </Field>
                                        </ReboundUI_Form:FormField>
                                    </td>
                                    <%-- <td class="title">Customer Date Required *
                                    </td>
                                    <td>
                                        <input type="text" id="txtCustomerDateRequired" class="txtNSelect" />
                                    </td>--%>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <ReboundUI_Form:FormField ID="ctlCustomerDecisionDate" runat="server" FieldID="txtCustomerDecisionDate" ResourceTitle="CustomerDecisionDate">
                                            <Field>
                                                <ReboundUI:ReboundTextBox ID="txtCustomerDecisionDate" runat="server" Width="120" />
                                                <ReboundUI:Calendar ID="calCustomerDecisionDate" runat="server" RelatedTextBoxID="txtCustomerDecisionDate" />
                                            </Field>
                                        </ReboundUI_Form:FormField>
                                    </td>
                                    <%-- <td class="title">Customer Decision Date
                                    </td>
                                    <td>
                                        <input type="text" id="txtCustomerDecisionDate" class="txtNSelect" />
                                    </td>--%>
                                </tr>
                                <tr>
                                    <td colspan="2">
                                        <ReboundUI_Form:FormField ID="ctlRFQClosingDate" runat="server" FieldID="txtRFQClosingDate" ResourceTitle="RFQClosingDate">
                                            <Field>
                                                <ReboundUI:ReboundTextBox ID="txtRFQClosingDate" runat="server" Width="120" />
                                                <ReboundUI:Calendar ID="calRFQClosingDate" runat="server" RelatedTextBoxID="txtRFQClosingDate" />
                                            </Field>
                                        </ReboundUI_Form:FormField>
                                    </td>
                                    <%-- <td class="title">RFQ Closing Date
                                    </td>
                                    <td>
                                        <input type="text" id="txtRFQClosingDate" class="txtNSelect" />
                                    </td>--%>
                                </tr>
                                <tr>
                                    <td class="title">Quote Validity Required
                                    </td>
                                    <td>
                                        <select id="ddlQuoteValidityRequired" class="txtNSelect"></select>
                                    </td>
                                    <td style="width: auto;">
                                        <label for="" id="lblQuoteValidityRequired"></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Type *
                                    </td>
                                    <td>
                                        <select id="ddlType" class="txtNSelect"></select>
                                    </td>
                                    <td style="width: auto;">
                                        <label for="" id="lblType"></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Order To Place
                                    </td>
                                    <td>
                                        <input type="checkbox" id="chkOrderToPlace" value="0">
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Usage
                                    </td>
                                    <td>
                                        <select id="ddlUsage" class="txtNSelect"></select>
                                    </td>
                                    <td style="width: auto;">
                                        <label for="" id="lblUsage"></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">PartWatch
                                    </td>
                                    <td>
                                        <input type="checkbox" id="chkPartWatch" value="0">
                                    </td>
                                </tr>
                                <%--<tr>
                                    <td>BOM
                                    </td>
                                    <td>
                                        <input type="checkbox" name="chkBOM" value="0">
                                    </td>
                                </tr>
                                <tr>
                                    <td>BoM Name
                                    </td>
                                    <td>
                                        <input type="text" id="txtBOMName" class="txtNSelect" />
                                    </td>
                                </tr>--%>
                                <tr>
                                    <td class="title">Factory Sealed (Y/N) *
                                    </td>
                                    <td>
                                        <input type="radio" name="rblFactorySealed" value="1">
                                        Yes
                         <input type="radio" name="rblFactorySealed" value="0" checked="checked">
                                        No
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">MSL
                                    </td>
                                    <td>
                                        <select id="ddlMSL" class="txtNSelect"></select>
                                    </td>
                                    <td style="width: auto;">
                                        <label for="" id="lblMSL"></label>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Notes to customer
                                    </td>
                                    <td>
                                        <textarea rows="2" id="txtNotesToCustomer" class="txtNSelect"></textarea>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Internal notes
                                    </td>
                                    <td>
                                        <textarea rows="2" id="txtInternalNotes" class="txtNSelect"></textarea>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Estimated Annual Usage
                                    </td>
                                    <td>
                                        <textarea rows="2" id="txtEstimatedAnnualUsage" class="txtNSelect"></textarea>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="title">Requirement for Traceability *
                                    </td>
                                    <<td style="width: auto">
                                        <select id="ddlReqForTrace" class="txtNSelect"></select>
                                    </td>
                                    <td style="width: auto;">
                                        <label for="" id="lblReqForTrace"></label>
                                    </td>
                                </tr>

                                <tr>
                                    <td colspan="2" class="alignCenter">
                                        <input type="button" id="btnSubmit" value="Save" class="Save" />&nbsp
                                        <input type="button" id="btnCancel" value="Cancel" class="Cancel" />
                                    </td>
                                </tr>

                            </table>
                        </div>
                    </div>

                </asp:TableCell>
            </asp:TableRow>
            <%--Error list section --%>
            <asp:TableRow>
                <asp:TableCell ColumnSpan="4">

                    <div class="hover_bkgr_fricc" id="dvErrorList">
                        <span class="helper"></span>
                        <div>
                            <div class="popupCloseButton" id="xErrorDiv">X</div>
                            
                            <table id="tbErrorList">
                                <thead>
                                <tr>
                                    <th>Row Id</th>
                                    <th>Column Name</th>
                                    <th>Error Description</th>
                               </tr>
                            </thead>
                            </table>
                            
                        </div>
                    </div>

                </asp:TableCell>
            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell ColumnSpan="4">
             <div class="LoaderPopup" id="divLoader">
             <div>
             <div class="cssload-loader">Uploading..</div> 
             </div>
             </div>
                </asp:TableCell>
            </asp:TableRow>


        </ReboundUI_Table:Form>


    </Content>
</ReboundUI_Form:DesignBase>
