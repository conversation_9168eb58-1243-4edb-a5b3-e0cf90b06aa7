<%--
Marker     changed by      date         Remarks
[001]      Vinay          09/07/2012   This need for Rebound- Invoice bulk Emailer
--%>
<%@ Control Language="C#" CodeBehind="ContactMainInfo.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Nuggets.ContactMainInfo" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="Standard">
	<Links>
		<ReboundUI:IconButton ID="ibtnEdit" runat="server" IconGroup="Nugget" IconTitleResource="Edit" IconCSSType="Edit" IconButtonMode="Hyperlink" Href="javascript:void(0);" />
	</Links>
	
	<Content>
		<table class="twoCols">
			<tr>
				<td class="col1">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlFirstName" runat="server" ResourceTitle="FirstName" />
						<ReboundUI:DataItemRow id="ctlSurname" runat="server" ResourceTitle="Surname" />
						<ReboundUI:DataItemRow id="ctlJobTitle" runat="server" ResourceTitle="JobTitle" />
						<ReboundUI:DataItemRow id="ctlTel" runat="server" ResourceTitle="Tel" />
						<ReboundUI:DataItemRow id="ctlFax" runat="server" ResourceTitle="Fax" />
						<ReboundUI:DataItemRow id="ctlTelExt" runat="server" ResourceTitle="TelExt" />
                        <ReboundUI:DataItemRow id="ctlIsSendShipmentNotification" runat="server" ResourceTitle="IsSendShipmentNotification" FieldType="CheckBox" />
					</table>
				</td>
				<td class="col2">
					<table cellpadding="0" cellspacing="0" border="0" class="dataItems">
						<ReboundUI:DataItemRow id="ctlHomeTel" runat="server" ResourceTitle="HomeTel" />
						<ReboundUI:DataItemRow id="ctlMobileTel" runat="server" ResourceTitle="MobileTel" />
						<ReboundUI:DataItemRow id="ctlEmail" runat="server" ResourceTitle="Email" FieldType="NubButton" />
						<ReboundUI:DataItemRow id="ctlIsEmailTextOnly" runat="server" ResourceTitle="IsEmailTextOnly" FieldType="CheckBox" />
						<ReboundUI:DataItemRow id="ctlNickname" runat="server" ResourceTitle="Nickname" />
						<ReboundUI:DataItemRow id="ctlPersonalAddress" runat="server" ResourceTitle="CompanyAddress" />
						<ReboundUI:DataItemRow id="hidAddressID" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAddressName" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAddress1" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAddress2" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidAddress3" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidTown" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCounty" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidCountryNo" runat="server" FieldType="Hidden" />
						<ReboundUI:DataItemRow id="hidPostcode" runat="server" FieldType="Hidden" />
						<%--[001] code start--%>
						<ReboundUI:DataItemRow id="hidFinanceContacts" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="hidCompanyAddress" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="hidComapnyNo" runat="server" FieldType="Hidden" />
                        <ReboundUI:DataItemRow id="hidInactive" runat="server" FieldType="Hidden" />
						<%--[001] code end--%>
					</table>
				</td>
			</tr>
		</table>
	</Content>
	
	<Forms>
		<ReboundForm:ContactMainInfo_Edit id="ctlEdit" runat="server"/>
	</Forms>
</ReboundUI_Nugget:DesignBase>
