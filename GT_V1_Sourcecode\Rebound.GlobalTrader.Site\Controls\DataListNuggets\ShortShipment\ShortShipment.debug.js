﻿
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");
Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment = function (element) {
    Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.initializeBase(this, [element]);
    this._LoginClientId = -1;
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.prototype = {
    get_ibtnExportCSV: function () { return this._ibtnExportCSV; }, set_ibtnExportCSV: function (v) { if (this._ibtnExportCSV !== v) this._ibtnExportCSV = v; },
    get_sortIndex: function () { return this._sortIndex; }, set_sortIndex: function (v) { if (this._sortIndex !== v) this._sortIndex = v; },
    get_sortDir: function () { return this._sortDir; }, set_sortDir: function (v) { if (this._sortDir !== v) this._sortDir = v; },
    get_pageIndex: function () { return this._pageIndex; }, set_pageIndex: function (v) { if (this._pageIndex !== v) this._pageIndex = v; },
    get_pageSize: function () { return this._pageSize; }, set_pageSize: function (v) { if (this._pageSize !== v) this._pageSize = v; },
    get_IsCanViewClient: function () { return this._IsCanViewClient; }, set_IsCanViewClient: function (v) { if (this._IsCanViewClient !== v) this._IsCanViewClient = v; },
    get_LoginClientId: function () { return this._LoginClientId; }, set_LoginClientId: function (v) { if (this._LoginClientId !== v) this._LoginClientId = v; },
    initialize: function () {
        this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
        this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
        this._strPathToData = "controls/DataListNuggets/ShortShipment";
        this._strDataObject = "ShortShipment";
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.callBaseMethod(this, "initialize");
        if (this._ibtnExportCSV) $R_IBTN.addClick(this._ibtnExportCSV, Function.createDelegate(this, this.exportCSV));//[001]
    },

    initAfterBaseIsReady: function () {
        this.updateFilterVisibility();
        this.getData();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnExportCSV) $R_IBTN.clearHandlers(this._ibtnExportCSV);//[001]
        this._ibtnExportCSV = null;//[001]
        this._IsCanViewClient = null;
        this._LoginClientId = null;
        Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.callBaseMethod(this, "dispose");
    },

    getDataOK: function () {
        for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
            var aryData = [
                $R_FN.writeDoubleCellValue($RGT_nubButton_ShortShipmentDetails(row.ShortShipmentId, $R_FN.setCleanTextValue(row.ShortShipmentId)), $RGT_nubButton_DebitNote(row.DebitNoteNo, row.DebitNumber)) //row.ShortShipmentId
                , $R_FN.writeDoubleCellValue($RGT_nubButton_GoodsIn(row.GoodsInId, row.GoodsInNo), $R_FN.setCleanTextValue(row.PartNo))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Raisedby), $R_FN.setCleanTextValue(row.PurchaseOrderNo))
                //, $RGT_nubButton_Company(row.CMNo, row.CM)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Supplier), $R_FN.setCleanTextValue(row.ManufacturerName))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Buyer), $R_FN.setCleanTextValue(row.IsShortageRefundIssue))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.QuantityOrdered), $R_FN.setCleanTextValue(row.QuantityReceived))
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ShortageQuantity), $R_FN.setCleanTextValue(row.ShortageValue.toFixed(2)))
                //, $R_FN.writeDoubleCellValue(row.IsClosed, row.IsCancel)
                , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.DateReceived), row.Status)
            ];
            this._table.addRow(aryData, row.ShortShipmentId, false);
            aryData = null; row = null;
        }
    },
    //start [001]
    exportCSV: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/DataListNuggets/ShortShipment");
        obj.set_DataObject("ShortShipment");
        obj.set_DataAction("ExportToCSV");
        obj._intTimeoutMilliseconds = 500 * 1000;
        obj.addParameter("SortIndex", this._sortIndex);
        obj.addParameter("SortDir", this._sortDir);
        obj.addParameter("PageIndex", this._pageIndex);
        obj.addParameter("PageSize", this._pageSize);
        obj.addParameter("Supplier", this.getFilterFieldValue("ctlSupplier"));
        obj.addParameter("PurchaseOrderNoLo", this.getFilterFieldValue_Min("ctlPurchaseOrderNo"));
        obj.addParameter("PurchaseOrderNoHi", this.getFilterFieldValue_Max("ctlPurchaseOrderNo"));
        obj.addParameter("DateReceived", this.getFilterFieldValue("ctlDateReceived"));
        obj.addParameter("QuantityOrderedLo", this.getFilterFieldValue_Min("ctlQuantityOrdered"));
        obj.addParameter("QuantityOrderedHi", this.getFilterFieldValue_Max("ctlQuantityOrdered"));
        obj.addParameter("QuantityReceivedLo", this.getFilterFieldValue_Min("ctlQuantityReceived"));
        obj.addParameter("QuantityReceivedHi", this.getFilterFieldValue_Max("ctlQuantityReceived"));
        obj.addParameter("ShortageQuantityLo", this.getFilterFieldValue_Min("ctlShortageQuantity"));
        obj.addParameter("ShortageQuantityHi", this.getFilterFieldValue_Max("ctlShortageQuantity"));
        obj.addParameter("ShortageValueLo", this.getFilterFieldValue_Min("ctlShortageValue"));
        obj.addParameter("ShortageValueHi", this.getFilterFieldValue_Max("ctlShortageValue"));
        obj.addParameter("IsShortageRefundIssue", this.getFilterFieldValue("ctlIsShortageRefundIssue"));
        obj.addParameter("ShortShipmentStatus", this.getFilterFieldValue("ctlStatus"));
        obj.addParameter("GINumberLo", this.getFilterFieldValue_Min("ctlGINumber"));
        obj.addParameter("GINumberHi", this.getFilterFieldValue_Max("ctlGINumber"));
        obj.addParameter("Buyer", this.getFilterFieldValue("ctlBuyer"));
        obj.addParameter("IsCanViewClient", this._IsCanViewClient);
        obj.addParameter("ClientByMaster", this.getFilterFieldValue("ctlClient"));
        obj.addParameter("IsRecentOnly", this.getFilterFieldValue("ctlRecentOnly"));
        obj.addDataOK(Function.createDelegate(this, this.exportCSV_OK));
        obj.addError(Function.createDelegate(this, this.exportCSV_Error));
        obj.addTimeout(Function.createDelegate(this, this.exportCSV_Error));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

    },

    exportCSV_OK: function (args) {
        var res = args._result;
        if (res.Filename) {
            //add the date to the file to force the latest version to be returned (not from cache)
            var dt = new Date();
            location.href = String.format("{0}?t={1}", res.Filename, dt.getTime());
            dt = null;
        }
        this.getDataOK_End();
    },

    exportCSV_Error: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    updateFilterVisibility: function () {
        this.getFilterField("ctlClient").show(this._IsCanViewClient);
        if (this._IsCanViewClient == false) {
            this.setFilterFieldValue("ctlClient", this._LoginClientId);
        }
    }
    //end [001]
};




Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.ShortShipment", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
