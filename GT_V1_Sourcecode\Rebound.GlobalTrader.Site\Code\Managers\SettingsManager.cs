using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site {
    public class SettingsManager {

        internal static string GetSetting(BLL.SettingItem.List enmSettingItem) {
            string str = null;
            BLL.Setting st = BLL.Setting.GetValue((int)enmSettingItem, SessionManager.ClientID);
            if (st != null) str = st.SettingValue;
            st = null;
            return str;
        }

		internal static int GetSetting_Int(BLL.SettingItem.List enmSettingItem) {
			int intOut = 0;
			Int32.TryParse(GetSetting(enmSettingItem), out intOut);
			return intOut;
		}

		internal static bool GetSetting_Boolean(BLL.SettingItem.List enmSettingItem) {
			bool blnOut = false;
			bool.TryParse(GetSetting(enmSettingItem), out blnOut);
			return blnOut;
		}

    }
}
