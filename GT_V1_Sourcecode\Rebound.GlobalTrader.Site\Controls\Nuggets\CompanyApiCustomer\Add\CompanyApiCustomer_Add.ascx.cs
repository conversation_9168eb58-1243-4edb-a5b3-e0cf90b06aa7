using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
    public partial class CompanyApiCustomer_Add : Base
    {

		#region Locals

		

		#endregion

		#region Properties

		private int _intCompanyID = -1;
		public int CompanyID {
			get { return _intCompanyID; }
			set { _intCompanyID = value; }
		}

		
		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "CompanyApiCustomer_Add");
            AddScriptReference("Controls.Nuggets.CompanyApiCustomer.Add.CompanyApiCustomer_Add.js");
			if (_objQSManager.CompanyID > 0) _intCompanyID = _objQSManager.CompanyID;
			//WireUpControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			
			SetupSelectSourceScreen();
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup controls for Select Source screen
		/// </summary>
		private void SetupSelectSourceScreen() {
		
		}

	

		

		

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_Add", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intCompanyID", _intCompanyID);
		}

	}
}