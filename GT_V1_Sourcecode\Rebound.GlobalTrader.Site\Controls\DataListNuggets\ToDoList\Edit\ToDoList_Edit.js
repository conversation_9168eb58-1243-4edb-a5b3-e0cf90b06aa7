Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.initializeBase(this,[n]);this._intToDoID=null;this._intCompanyID=null;this._intMessageID=null;this._blnInitializeComplete=!1};Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown));this._blnInitializeComplete=!0},dispose:function(){this.isDisposed||(this._ctlToDo&&this._ctlToDo.dispose(),this._ctlToDo=null,this._intToDoID=null,this._intCompanyID=null,this._intMessageID=null,this._blnInitializeComplete=!1,Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),this._ctlToDo=$find(this.getField("ctlToDo").ID),this._ctlToDo._ctlRelatedForm=this,this._ctlToDo.setupReminderClick());this._ctlToDo._intToDoID=this._intToDoID;this._ctlToDo._intCompanyID=this._intCompanyID;this._ctlToDo._intMessageID=this._intMessageID;this.getData()},getData:function(){this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ToDo");n.set_DataObject("ToDo");n.set_DataAction("GetItem");n.addParameter("ID",this._intToDoID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var t=n._result;this.setFormFieldsToDefaults();t&&(this._ctlToDo._intToDoID=this._intToDoID,this.setFieldValue("ctlSubject",t.Subject),this.setFieldValue("ctlText",t.Text),this.setFieldValue("ctlDueDate",t.DueDate),this.setFieldValue("ctlDueTime",t.DueTime),this.setFieldValue("ctlReminder",t.HasReminder),this._ctlToDo.selectReminder(),t.HasReminder&&(this.setFieldValue("ctlReminderDate",t.ReminderDate),this.setFieldValue("ctlReminderTime",t.ReminderTime),this.setFieldValue("ctlReminderText",t.ReminderText)));this.showLoading(!1);this.showInnerContent(!0)},getDataError:function(n){this._ctlRelatedNugget.showNuggetError(!0,n.get_ErrorMessage())},saveClicked:function(){if(!this.validateForm())return!1;var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ToDo");n.set_DataObject("ToDo");n.set_DataAction("SaveEdit");this._ctlToDo.addFieldsToDataObject(n);n.addDataOK(Function.createDelegate(this,this.saveOK));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},validateForm:function(){this.onValidate();var n=this._ctlToDo.validateFields();return n||this.showError(!0),n},saveError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveOK:function(n){n._result.Result?this.onSaveComplete():this.saveError(n)}};Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);