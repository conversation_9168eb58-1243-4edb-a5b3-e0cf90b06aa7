﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--============================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-229094]     An.TranTan		 21-Jan-2025		CREATE		Get quote offered date
[US-229094]     An.TranTan		 24-Jan-2025		UPDATE		Update logic get quote min reminder date
[US-229094]     An.TranTan		 24-Jan-2025		UPDATE		Get company name
[US-229094]     An.TranTan		 24-Jan-2025		UPDATE		Get quote status
==============================================================================================================================  
*/
CREATE OR ALTER   PROCEDURE [dbo].[usp_select_QuoteOfferedDate]
@QuoteId int
AS
BEGIN

	IF ISNULL(@QuoteId,0) = 0
	BEGIN
		SELECT 
			0 AS QuoteId
			,0 AS QuoteNumber
			,NULL AS QuoteOfferedDate
			,'' AS CompanyName
			,'' AS QuoteStatus
	END
	ELSE
	BEGIN
	SELECT
		q.QuoteId
		,q.QuoteNumber
		, CASE WHEN q.QuoteOfferedDate IS NULL THEN NULL 
			ELSE DATEADD(DAY, 3, q.QuoteOfferedDate)
		END AS QuoteOfferedDate
		,c.CompanyName
		,qs.Name AS QuoteStatus
	FROM tbQuote q WITH(NOLOCK)
	JOIN tbCompany c WITH(NOLOCK) on c.CompanyId = q.CompanyNo
	LEFT JOIN tbQuoteStatus qs WITH(NOLOCK) on qs.QuoteStatusId = q.QuoteStatus
	WHERE q.QuoteId = @QuoteId
	END
END
GO

