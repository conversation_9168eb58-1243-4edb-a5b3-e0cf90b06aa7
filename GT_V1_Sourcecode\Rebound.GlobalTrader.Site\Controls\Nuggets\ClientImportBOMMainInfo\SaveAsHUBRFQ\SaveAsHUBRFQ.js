Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.SaveAsHUBRFQ=function(n){Rebound.GlobalTrader.Site.Controls.Forms.SaveAsHUBRFQ.initializeBase(this,[n]);this._intBOMID=-1;this._BomName=""};Rebound.GlobalTrader.Site.Controls.Forms.SaveAsHUBRFQ.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_BomName:function(){return this._BomName},set_BomName:function(n){this._BomName!==n&&(this._BomName=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.SaveAsHUBRFQ.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._intBOMID=null,this._intContact2No=null,this._BomName=null,Rebound.GlobalTrader.Site.Controls.Forms.SaveAsHUBRFQ.callBaseMethod(this,"dispose"))},formShown:function(){this._blnFirstTimeShown&&(this._ctlConfirm=this.getFieldComponent("ctlConfirm"),this._ctlConfirm.addClickYesEvent(Function.createDelegate(this,this.yesClicked)),this._ctlConfirm.addClickNoEvent(Function.createDelegate(this,this.noClicked)))},noClicked1:function(){this.onNotConfirmed()},yesClicked:function(){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/ClientImportBOMMainInfo");n.set_DataObject("ClientImportBOMMainInfo");n.set_DataAction("SaveAsHUBRFQ");n.addParameter("id",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.saveConfirmComplete));n.addError(Function.createDelegate(this,this.saveError));n.addTimeout(Function.createDelegate(this,this.saveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null;this.onSave()},noClicked:function(){this.showSaving(!1);this.onNotConfirmed()},saveError:function(n){this.showSaving(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},saveConfirmComplete:function(n){n._result.Result==!0?(alert("HUBRFQ( "+this._BomName+" ) successfully created."),this.showSavedOK(!0),this.onSaveComplete()):(this._strErrorMessage=n._errorMessage,this.onSaveError())}};Rebound.GlobalTrader.Site.Controls.Forms.SaveAsHUBRFQ.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.SaveAsHUBRFQ",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);