Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.initializeBase(this,[n]);this._arySelected=[];this._aryUnselected=[]};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.prototype={get_ibtnSelect:function(){return this._ibtnSelect},set_ibtnSelect:function(n){this._ibtnSelect!==n&&(this._ibtnSelect=n)},get_ibtnDeselect:function(){return this._ibtnDeselect},set_ibtnDeselect:function(n){this._ibtnDeselect!==n&&(this._ibtnDeselect=n)},get_tblSelected:function(){return this._tblSelected},set_tblSelected:function(n){this._tblSelected!==n&&(this._tblSelected=n)},get_tblUnselected:function(){return this._tblUnselected},set_tblUnselected:function(n){this._tblUnselected!==n&&(this._tblUnselected=n)},addItemSelected:function(n){this.get_events().addHandler("ItemSelected",n)},removeItemSelected:function(n){this.get_events().removeHandler("ItemSelected",n)},onItemSelected:function(){var n=this.get_events().getHandler("ItemSelected");n&&n(this,Sys.EventArgs.Empty)},addItemDeselected:function(n){this.get_events().addHandler("ItemDeselected",n)},removeItemDeselected:function(n){this.get_events().removeHandler("ItemDeselected",n)},onItemDeselected:function(){var n=this.get_events().getHandler("ItemDeselected");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){$R_IBTN.addClick(this._ibtnSelect,Function.createDelegate(this,this.doSelect));$R_IBTN.addClick(this._ibtnDeselect,Function.createDelegate(this,this.doDeselect));this._tblSelected.addMultipleSelectionChanged(Function.createDelegate(this,this.updateButtons));this._tblUnselected.addMultipleSelectionChanged(Function.createDelegate(this,this.updateButtons));Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._tblSelected&&this._tblSelected.dispose(),this._tblUnselected&&this._tblUnselected.dispose(),this._ibtnSelect=null,this._ibtnDeselect=null,this._tblSelected=null,this._tblUnselected=null,this._arySelected=null,this._aryUnselected=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.callBaseMethod(this,"dispose"))},addRow:function(n,t,i){n?Array.add(this._arySelected,{Text:t,Value:i}):Array.add(this._aryUnselected,{Text:t,Value:i})},clearData:function(){Array.clear(this._arySelected);Array.clear(this._aryUnselected)},populateTables:function(){$R_IBTN.enableButton(this._ibtnDeselect,!1);$R_IBTN.enableButton(this._ibtnSelect,!1);this._tblSelected.clearTable();for(var n=0,t=this._arySelected.length;n<t;n++)this._tblSelected.addRow(this._arySelected[n].Text,this._arySelected[n].Value,!1);for(this._tblUnselected.clearTable(),n=0,t=this._aryUnselected.length;n<t;n++)this._tblUnselected.addRow(this._aryUnselected[n].Text,this._aryUnselected[n].Value,!1)},doSelect:function(){for(var t=!1,n=this._aryUnselected.length-1;n>=0;n--)Array.contains(this._tblUnselected._aryCurrentValues,this._aryUnselected[n].Value)&&(t=!0,Array.add(this._arySelected,this._aryUnselected[n]),Array.removeAt(this._aryUnselected,n));t&&this.populateTables();this.onItemSelected()},doDeselect:function(){for(var t=!1,n=this._arySelected.length-1;n>=0;n--)Array.contains(this._tblSelected._aryCurrentValues,this._arySelected[n].Value)&&(t=!0,Array.add(this._aryUnselected,this._arySelected[n]),Array.removeAt(this._arySelected,n));t&&this.populateTables();this.onItemDeselected()},updateButtons:function(){$R_IBTN.enableButton(this._ibtnDeselect,this._tblSelected._aryCurrentValues.length>0);$R_IBTN.enableButton(this._ibtnSelect,this._tblUnselected._aryCurrentValues.length>0)},getValues:function(n){var r=[],t,i;if(n)for(t=0,i=this._arySelected.length;t<i;t++)Array.add(r,this._arySelected[t].Value);else for(t=0,i=this._aryUnselected.length;t<i;t++)Array.add(r,this._aryUnselected[t].Value);return r},getValuesAsString:function(n){return $R_FN.arrayToSingleString(this.getValues(n))}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.MultiSelection",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);