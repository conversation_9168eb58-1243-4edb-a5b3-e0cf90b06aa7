Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.initializeBase(this,[n]);this._intInvoiceID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.prototype={get_intInvoiceID:function(){return this._intInvoiceID},set_intInvoiceID:function(n){this._intInvoiceID!==n&&(this._intInvoiceID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intInvoiceID=null,Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/SalesOrderAuthorised");this._objData.set_DataObject("SalesOrderAuthorised");this._objData.set_DataAction("GetData");var n=window.location.href,t=new URL(n),i=t.searchParams.get("inv");this._objData.addParameter("InvoiceId",i)},dataCallOK:function(){var t=this._objData._result,n;if(t.Items)for(n=0;n<t.Items.length;n++)this.addOption(t.Items[n].Name,t.Items[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.SalesOrderAuthorised",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);