Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_ctlMainInfo:function(){return this._ctlMainInfo},set_ctlMainInfo:function(n){this._ctlMainInfo!==n&&(this._ctlMainInfo=n)},get_ctlBOMPVV:function(){return this._ctlBOMPVV},set_ctlBOMPVV:function(n){this._ctlBOMPVV!==n&&(this._ctlBOMPVV=n)},get_ctlBOMItems:function(){return this._ctlBOMItems},set_ctlBOMItems:function(n){this._ctlBOMItems!==n&&(this._ctlBOMItems=n)},get_ctlSourcingResults:function(){return this._ctlSourcingResults},set_ctlSourcingResults:function(n){this._ctlSourcingResults!==n&&(this._ctlSourcingResults=n)},get_ctlPOHubSourcing:function(){return this._ctlPOHubSourcing},set_ctlPOHubSourcing:function(n){this._ctlPOHubSourcing!==n&&(this._ctlPOHubSourcing=n)},get_ctlPOHubAutoSourcing:function(){return this._ctlPOHubAutoSourcing},set_ctlPOHubAutoSourcing:function(n){this._ctlPOHubAutoSourcing!==n&&(this._ctlPOHubAutoSourcing=n)},get_ctlBomCSV:function(){return this._ctlBomCSV},set_ctlBomCSV:function(n){this._ctlBomCSV!==n&&(this._ctlBomCSV=n)},get_lblStatus:function(){return this._lblStatus},set_lblStatus:function(n){this._lblStatus!==n&&(this._lblStatus=n)},get_ctlCsvExportHistory:function(){return this._ctlCsvExportHistory},set_ctlCsvExportHistory:function(n){this._ctlCsvExportHistory!==n&&(this._ctlCsvExportHistory=n)},get_btnPrint:function(){return this._btnPrint},set_btnPrint:function(n){this._btnPrint!==n&&(this._btnPrint=n)},get_ctlExpediteHistory:function(){return this._ctlExpediteHistory},set_ctlExpediteHistory:function(n){this._ctlExpediteHistory!==n&&(this._ctlExpediteHistory=n)},get_ctlHubImagesDragDrop:function(){return this._ctlHubImagesDragDrop},set_ctlHubImagesDragDrop:function(n){this._ctlHubImagesDragDrop!==n&&(this._ctlHubImagesDragDrop=n)},get_ApprovalMessage:function(){return this._ApprovalMessage},set_ApprovalMessage:function(n){this._ApprovalMessage!==n&&(this._ApprovalMessage=n)},get_IsPOHub:function(){return this._IsPOHub},set_IsPOHub:function(n){this._IsPOHub!==n&&(this._IsPOHub=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.callBaseMethod(this,"initialize")},goInit:function(){this._btnPrint&&(this._btnPrint.addPrint(Function.createDelegate(this,this.printHUBRFQ)),this._btnPrint.addEmail(Function.createDelegate(this,this.emailHUBRFQ)),this._btnPrint.addExtraButtonClick(Function.createDelegate(this,this.printOtherDocs)));this._ctlMainInfo&&this._ctlMainInfo.addGotData(Function.createDelegate(this,this.mainBOMDetailGotDataOK));this._ctlBOMItems&&this._ctlBOMItems.addPartSelected(Function.createDelegate(this,this.selectPart));this._ctlBOMItems&&this._ctlBOMItems.addStartGetData(Function.createDelegate(this,this.mainInfoStartGetData));this._ctlBOMItems&&this._ctlBOMItems.addGotDataOK(Function.createDelegate(this,this.mainInfoGotDataOK));this._ctlBOMItems&&this._ctlBOMItems.addGetDataComplete(Function.createDelegate(this,this.ctlBOMItems_GetDataComplete));this._ctlSourcingResults&&this._ctlSourcingResults.addAddFormShown(Function.createDelegate(this,this.ctlSourcingResults_AddFormShown));this._ctlSourcingResults&&this._ctlSourcingResults.addGotDataOK(Function.createDelegate(this,this.ctlSourcingResults_GetDataComplete));this._ctlBOMItems&&this._ctlBOMItems.addCallBeforeRelease(Function.createDelegate(this,this.ctlBOMItems_addCallBeforeRelease));this._ctlMainInfo&&this._ctlMainInfo.addCallBeforeRelease(Function.createDelegate(this,this.ctlMainInfo_addCallBeforeRelease));this._ctlBOMItems&&this._ctlBOMItems.addRefereshAfterRelease(Function.createDelegate(this,this.ctlBOMItems_RefereshAfterRelease));this._ctlBOMItems&&this._ctlBOMItems.addImportSourcingResultSuccess(Function.createDelegate(this,this.ctlBOMItems_RefreshAfterImportSourcingResult));this._ctlPOHubSourcing&&this._ctlPOHubSourcing.addSourcingResultAdded(Function.createDelegate(this,this.ctlSourcing_SourcingResultAdded));this._ctlSourcingResults&&this._ctlSourcingResults.addSaveEditComplete(Function.createDelegate(this,this.ctlSourcingResults_SaveEditComplete));this._ctlSourcingResults&&this._ctlSourcingResults.addSourcingResultDeleted(Function.createDelegate(this,this.ctlSourcingResults_SourcingResultDeleted));this._ctlSourcingResults&&this._ctlSourcingResults.addCallBeforeRelease(Function.createDelegate(this,this.ctlSourcingResults_addCallBeforeRelease));this._ctlSourcingResults&&this._ctlSourcingResults.addSourcingResultSelect(Function.createDelegate(this,this.ctlSourcingResults_SourcingResultSelect));this._ctlSourcingResults&&this._ctlSourcingResults.addAddCustFormShown(Function.createDelegate(this,this.ctlSourcingResults_AddCustFormShown));this._ctlPOHubAutoSourcing&&this._ctlPOHubAutoSourcing.updateSourcingResultComplete(Function.createDelegate(this,this.ctlAutoSourcing_SourcingResultUpdated));Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlMainInfo&&this._ctlMainInfo.dispose(),this._ctlBOMPVV&&this._ctlBOMPVV.dispose(),this._ctlSourcingResults&&this._ctlSourcingResults.dispose(),this._ctlPOHubSourcing&&this._ctlPOHubSourcing.dispose(),this._ctlPOHubAutoSourcing&&this._ctlPOHubAutoSourcing.dispose(),this._ctlBomCSV=null,this._ctlBOMPVV=null,this._pnlStatus=null,this._lblStatus=null,this._ctlMainInfo=null,this._ctlBOMItems=null,this._ApprovalMessage=null,this._ctlPOHubSourcing=null,this._ctlExpediteHistory=null,this._ctlSourcingResults=null,this._ctlCsvExportHistory=null,this._ctlHubImagesDragDrop=null,Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.callBaseMethod(this,"dispose"))},selectPart:function(){this._intCustomerRequirementID=this._ctlBOMItems._intCustomerRequirementID;this._ctlSourcingResults._blnRequirementClosed=this._ctlBOMItems._blnRequirementClosed;this._ctlSourcingResults._intCustomerRequirementID=this._intCustomerRequirementID;this._ctlSourcingResults._strPartNo=this._ctlBOMItems.getSelectedPartNo();this._ctlSourcingResults._ClientNo=this._ctlBOMItems.getIPOClientNo();this._ctlSourcingResults._intBOMID=this._ctlMainInfo._intBOMID;this._ctlSourcingResults._BomCode=this._ctlMainInfo.getFieldValue("ctlCode");this._ctlSourcingResults._BomName=this._ctlMainInfo.getFieldValue("ctlName");this._ctlSourcingResults._BomCompanyName=this._ctlMainInfo.getFieldValue("ctlCompany");this._ctlSourcingResults._BomCompanyNo=this._ctlMainInfo.getFieldValue("hidCompanyNo");this._ctlSourcingResults._SalesManNo=this._ctlMainInfo._RequestToPOHubBy;this._ctlSourcingResults._ReqSalesman=this._ctlBOMItems._ReqSalesman;this._ctlSourcingResults._SupportTeamMemberNo=this._ctlBOMItems._SupportTeamMemberNo;this._ctlBOMPVV&&(this._ctlSourcingResults._intBOMID=this._ctlBOMPVV._intBOMID);this._ctlSourcingResults.getData();this._ctlBOMItems._isClosed||(this._ctlPOHubSourcing&&(this._ctlPOHubSourcing._intCustomerRequirementID=this._intCustomerRequirementID),this._ctlPOHubSourcing&&(this._ctlPOHubSourcing._blnRequirementClosed=this._ctlBOMItems._blnRequirementClosed),this._ctlPOHubSourcing&&this._ctlPOHubSourcing.selectPart(this._ctlBOMItems.getSelectedPartNo()),this._ctlPOHubSourcing&&this._ctlPOHubSourcing.updateIPOClientId(this._ctlBOMItems.getIPOClientNo()),this._ctlPOHubSourcing&&(this._ctlPOHubSourcing._blnRequirementReleased=this._ctlBOMItems._blnRequirementReleased||this._ctlBOMItems._isNoBid&&this._ctlBOMItems._blnPOHub));this._ctlPOHubAutoSourcing&&(this._ctlMainInfo._inActive||this._ctlBOMItems._isClosedOnly?this._ctlPOHubAutoSourcing.show(!1):(this._ctlPOHubAutoSourcing._partNo=this._ctlBOMItems.getSelectedPartNo(),this._ctlPOHubAutoSourcing._cusReqID=this._intCustomerRequirementID,this._ctlPOHubAutoSourcing._blnRequirementClosed=this._ctlBOMItems._blnRequirementClosed,this._ctlPOHubAutoSourcing.getAutoSourcing(),this._ctlPOHubAutoSourcing.show(!0)))},ctlSourcing_SourcingResultAdded:function(){this._ctlMainInfo.getData();this._ctlSourcingResults.getData();this._ctlPOHubSourcing&&this._ctlPOHubSourcing.hideAddForm()},ctlSourcingResults_SourcingResultDeleted:function(){this._ctlMainInfo.getData();this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult)},ctlAutoSourcing_SourcingResultUpdated:function(){this._ctlSourcingResults.getData();this._ctlPOHubAutoSourcing&&this._ctlPOHubAutoSourcing.getAutoSourcing()},mainInfoStartGetData:function(){this._ctlPOHubSourcing&&this._ctlPOHubSourcing.show(!1);this._ctlSourcingResults.show(!1);this._ctlHubImagesDragDrop&&this._ctlHubImagesDragDrop.show(!1);this._ctlPOHubAutoSourcing&&this._ctlPOHubAutoSourcing.show(!1)},mainInfoGotDataOK:function(){this._ctlSourcingResults&&(this._ctlSourcingResults._intCompanyID=this._ctlBOMItems._intCompanyID);this._ctlPOHubSourcing&&this._ctlPOHubSourcing.show(!this._ctlMainInfo._inActive&&!this._ctlBOMItems._isClosedOnly);$R_IBTN.enableButton(this._ctlPOHubSourcing._ibtnSearch,GetAssignToMe());this._ctlSourcingResults.show(!this._ctlMainInfo._inActive);this._ctlSourcingResults._hasReleasedItem=!this._ctlBOMItems._blnRequirementReleased;this._ctlPOHubSourcing&&(this._ctlSourcingResults._Quantity=this._ctlBOMItems.getFieldValue("ctlQuantity"),this._ctlSourcingResults._PackageID=this._ctlBOMItems.getFieldValue("hidPackageID"),this._ctlSourcingResults._ProductID=this._ctlBOMItems.getFieldValue("hidProductID"),this._ctlSourcingResults._ROHS=this._ctlBOMItems.getFieldValue("hidROHS"),this._ctlSourcingResults._Manufacturer=this._ctlBOMItems.getFieldValue("hidManufacturer"),this._ctlSourcingResults._ManufacturerNo=this._ctlBOMItems.getFieldValue("hidManufacturerNo"),this._ctlSourcingResults._MfrAdvisoryNotes=this._ctlBOMItems.getFieldValue("hidMfrAdvisoryNotes"),this._ctlSourcingResults._DateCode=this._ctlBOMItems.getFieldValue("ctlDateCode"),this._ctlSourcingResults._Product=this._ctlBOMItems.getFieldValue("ctlProduct"))},ctlBOMItems_RefereshAfterRelease:function(){this._ctlMainInfo.getData()},mainBOMDetailGotDataOK:function(){let n=this._ctlMainInfo.getFieldValue("hidDisplayStatus");$R_FN.setInnerHTML(this._lblStatus,n);this._ctlMainInfo.getFieldValue("hidDisplayStatus")&&(this._ctlBOMItems._inActive=this._ctlMainInfo._inActive);this._ctlBOMItems._blnRequestedToPoHub=this._ctlMainInfo._blnRequestedToPoHub;this._ctlBOMItems._isClosed=this._ctlMainInfo._isClosed;this._ctlBOMItems._isClosedOnly=n=="CLOSED"?!0:!1;this._ctlBOMItems._intContact2No=this._ctlMainInfo._intContact2No;this._ctlBOMItems&&this._ctlBOMItems.getData();let t=!this._IsPOHub&&n=="NEW"||this._IsPOHub&&n=="RPQ";this._ctlBomCSV.showUpdateBOMButton(t)},ctlBOMItems_GetDataComplete:function(){this._ctlMainInfo._blnHasRequirement=this._ctlBOMItems._intCountStock>0;this._ctlMainInfo.enableButtons(!0);this._ctlMainInfo._isPurchaseHub&&this._ctlMainInfo.disableNotifyAndExportButton(!this._ctlMainInfo._isPurchaseHub);this._ctlMainInfo.enableDisableReleaseButton(this._ctlBOMItems._allExistInSourcingResult);this._ctlExpediteHistory&&this._ctlExpediteHistory.GetExpediteHistory()},ctlSourcingResults_GetDataComplete:function(){this._ctlBOMItems._blnAllHasDelDate=this._ctlSourcingResults._blnAllHasDelDate;this._ctlMainInfo._blnAllHasDelDate=this._ctlSourcingResults._blnAllHasDelDate;this._ctlBOMItems._blnAllHasProduct=this._ctlSourcingResults._blnAllHasProduct;this._ctlMainInfo._blnAllHasProduct=this._ctlSourcingResults._blnAllHasProduct},ctlSourcingResults_addCallBeforeRelease:function(){this._ctlSourcingResults.clearMessages();var n=this.validateDeliveryDate(this._ctlSourcingResults._blnAllHasDelDate&&this._ctlSourcingResults._blnAllHasProduct);this._ctlSourcingResults._blnCanRelease=n},ctlBOMItems_addCallBeforeRelease:function(){this._ctlSourcingResults.clearMessages();var n=this.validateReqItemDeliveryDate(this._ctlBOMItems._blnAllHasDelDate&&this._ctlBOMItems._blnAllHasProduct);this._ctlBOMItems._blnCanRelease=n},ctlMainInfo_addCallBeforeRelease:function(){this._ctlMainInfo.clearMessages();var n=this.validateHUBRFQMainInfoDeliveryDate(this._ctlBOMItems._blnAllItemHasDelDate&&this._ctlBOMItems._blnAllItemHasProduct);this._ctlMainInfo._blnCanReleaseAll=n},validateDeliveryDate:function(n){return this._ctlSourcingResults.clearMessages(),n?!0:(this._ctlSourcingResults.addMessage("Delivery date and product is required for all attached sourcing results",$R_ENUM$MessageTypeList.Error),!1)},validateReqItemDeliveryDate:function(n){return this._ctlBOMItems.clearMessages(),n?!0:(this._ctlBOMItems.addMessage("Delivery date and product is required for all attached sourcing results",$R_ENUM$MessageTypeList.Error),!1)},validateProduct:function(n){return this._ctlSourcingResults.clearMessages(),n?!0:(this._ctlSourcingResults.addMessage("Delivery date and product is required for all attached sourcing results",$R_ENUM$MessageTypeList.Error),!1)},validateReqItemProduct:function(n){return this._ctlBOMItems.clearMessages(),n?!0:(this._ctlBOMItems.addMessage("Delivery date and product is required for all attached sourcing results",$R_ENUM$MessageTypeList.Error),!1)},ctlSourcingResults_SaveEditComplete:function(){this._ctlMainInfo.getData()},ctlMainInfo_SaveEditComplete:function(){this._ctlLines.getData()},printHUBRFQ:function(){this._BomId=this._ctlMainInfo._intBOMID;$R_FN.openPrintWindow($R_ENUM$PrintObject.PrintHUBRFQ,this._BomId)},emailHUBRFQ:function(){this._BomId=this._ctlMainInfo._intBOMID;$R_FN.openPrintWindow($R_ENUM$PrintObject.EmailHUBRFQ,this._BomId,!0)},printOtherDocs:function(){this._btnPrint._strExtraButtonClickCommand=="PrintLog"&&$R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog,this._BomId,!1,"PrintHUBRFQ")},ctlSourcingResults_AddFormShown:function(){this._ctlSourcingResults._frmAdd.setFieldValue("ctlMSL",this._ctlBOMItems.getFieldValue("hidMSL"))},ctlSourcingResults_AddCustFormShown:function(){this._ctlSourcingResults._frmAdd.setFieldValue("ctlMSL",this._ctlBOMItems.getFieldValue("hidMSL"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlManufacturer",this._ctlBOMItems.getFieldValue("hidManufacturerNo"),null,this._ctlBOMItems.getFieldValue("hidManufacturer"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlDateCode",this._ctlBOMItems.getFieldValue("ctlDateCode"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlProduct",this._ctlBOMItems.getFieldValue("hidProductID"),null,this._ctlBOMItems.getFieldValue("ctlProduct"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlPackage",this._ctlBOMItems.getFieldValue("hidPackageID"),null,this._ctlBOMItems.getFieldValue("ctlPackage"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlQuantity",this._ctlBOMItems.getFieldValue("ctlQuantity"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlCurrency",this._ctlBOMItems.getFieldValue("hidCurrencyID"));this._ctlSourcingResults._frmAdd.setFieldValue("ctlROHS",this._ctlBOMItems.getFieldValue("hidROHS"))},ctlSourcingResults_SourcingResultSelect:function(){this._ctlHubImagesDragDrop&&this._ctlSourcingResults._selectedRow==1?(this._ctlHubImagesDragDrop.show(!0),this._ctlHubImagesDragDrop._intRefDocID=this._ctlSourcingResults._SourcingResultNo,this._ctlHubImagesDragDrop.viewPanel(this._ctlSourcingResults._blnImageAvail)):this._ctlHubImagesDragDrop.show(!1)},validateHUBRFQMainInfoDeliveryDate:function(n){return this._ctlMainInfo.clearMessages(),n?!0:(this._ctlMainInfo.addMessage("Delivery date and product is required for all attached sourcing results",$R_ENUM$MessageTypeList.Error),!1)},ctlBOMItems_RefreshAfterImportSourcingResult:function(){this._ctlBOMItems.getData();this._ctlBomCSV.showPDFPanel();this._ctlCsvExportHistory.getCreditHistory()}};Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.BOMDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);