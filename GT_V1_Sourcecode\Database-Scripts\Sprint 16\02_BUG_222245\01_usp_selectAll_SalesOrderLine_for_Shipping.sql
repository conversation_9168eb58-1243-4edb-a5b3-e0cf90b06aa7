﻿
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-222245]	NgaiTo		 		28-Nov-2024		UPDATE			222245: [PROD Bug] OGEL - Incorrect warning message and add red highlight
===========================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_SalesOrderLine_for_Shipping]
    @SalesOrderNo int                          
AS         
BEGIN
    SET NOCOUNT ON;
    
    SELECT *
    FROM (
        SELECT
            al.AllocationId,
            sol.SalesOrderLineId,
            sol.SalesOrderNo,
            sol.Part,
            stk.ROHS,
            sol.DateCode,
            sol.Quantity,
            sol.ManufacturerNo,
            mf.ManufacturerName,
            al.StockNo,
            stk.GoodsInLineNo,
            mf.ManufacturerCode,
            CASE WHEN sol.ServiceNo IS NULL THEN al.QuantityAllocated ELSE sol.Quantity END AS QuantityAllocated,
            sol.Quantity AS QuantityOrdered,
            co.CompanyName,
            so.CompanyNo,
            so.CustomerPO,
            so.DateOrdered,
            CASE WHEN sol.ServiceNo IS NULL THEN stk.QuantityInStock ELSE sol.Quantity END AS QuantityInStock,
            so.SalesOrderNumber,
            sol.ProductNo,
            pr.ProductName,
            pr.ProductDescription,
            pa.PackageName,
            pa.PackageDescription,
            stk.Location,
            ISNULL((SELECT SUM(ila.Quantity) FROM dbo.tbInvoicelineAllocation ila WHERE sol.SalesOrderLineId = ila.SalesOrderLineNo), 0) AS QuantityShipped,
            sol.CustomerPart,
            sol.RequiredDate,
            sol.DatePromised,
            CASE WHEN EXISTS (
                SELECT 1
                FROM vwSalesOrderLineShipList
                WHERE (sol.ServiceNo IS NULL AND SalesOrderLineId = sol.SalesOrderLineId AND AllocationId = al.AllocationId AND CreditSatusReadytoShip = 1)
                    AND ((ISNULL(so.OGEL_Required, 0) = 1 AND es.ApprovalStatusId IN (1, 2, 6, 7)) OR (ISNULL(so.OGEL_Required, 0) = 0))
                    OR (sol.ServiceNo IS NOT NULL AND sol.ServiceShipped = 0 AND SalesOrderLineId = sol.SalesOrderLineId AND CreditSatusReadytoShip = 1)
                    AND ((ISNULL(so.OGEL_Required, 0) = 1 AND es.ApprovalStatusId IN (1, 2, 6, 7)) OR (ISNULL(so.OGEL_Required, 0) = 0))
            ) THEN CAST(1 AS bit) ELSE CAST(0 AS bit) END AS ReadyToShip,
            stk.WarehouseNo,
            wh.WarehouseName,
            sol.ServiceNo,
            sol.Instructions,
            CASE WHEN co.CreditStatus = 'M' AND (MONTH(sol.DatePromised) != MONTH(GETDATE()) AND YEAR(sol.DatePromised) = YEAR(GETDATE())) THEN '[Stop Status : M]' ELSE '' END AS CreditStatus,
            ISNULL(gil.ReqSerialNo, 0) AS ReqSerialNo,
            sol.MSLLevel,
            sol.SOSerialNo,
            sm.ApprovalName AS OGELApprovalStatus,
            so.OGEL_Required AS OGELREquired,
			(CASE 
				WHEN ISNULL(es.OGELNumber, 0) > 0
					AND ISNULL(so.OGEL_Required, 0) = 1
					THEN (SELECT ISNULL((SELECT OgelNumber FROM tbOGELLicense WHERE OgelId = es.OGELNumber AND Inactive = 0), ''))
				ELSE ''
				END
			) AS OGELNumber,
			(CASE WHEN LEN((CASE 
				WHEN ISNULL(es.OGELNumber, 0) > 0
					AND ISNULL(so.OGEL_Required, 0) = 1
					THEN (SELECT ISNULL((SELECT OgelNumber FROM tbOGELLicense WHERE OgelId = es.OGELNumber AND Inactive = 0), ''))
				ELSE ''
				END
			)) > 0 THEN 1 ELSE 0 END) AS ShowOGELWarning
        FROM dbo.tbSalesOrderLine sol
        INNER JOIN dbo.tbSalesOrder so ON sol.SalesOrderNo = so.SalesOrderId
        LEFT JOIN dbo.tbCompany co ON so.CompanyNo = co.CompanyId
        LEFT JOIN dbo.tbContact cn ON so.ContactNo = cn.ContactId
        LEFT JOIN dbo.tbLogin lg ON lg.LoginId = so.Salesman
        LEFT JOIN dbo.tbProduct pr ON sol.ProductNo = pr.ProductId
        LEFT JOIN dbo.tbPackage pa ON sol.PackageNo = pa.PackageId
        LEFT JOIN dbo.tbManufacturer mf ON sol.ManufacturerNo = mf.ManufacturerId
        LEFT JOIN dbo.tbAllocation al ON sol.SalesOrderLineId = al.SalesOrderLineNo
        LEFT JOIN dbo.tbStock stk ON al.StockNo = stk.StockId
        LEFT JOIN dbo.tbWarehouse wh ON wh.WarehouseId = stk.WarehouseNo
        LEFT JOIN tbGoodsInLine gil ON gil.GoodsInLineId = stk.GoodsInLineNo
        LEFT JOIN tbSO_ExportApprovalStatusOGEL es ON es.SalesOrderLineNo = sol.SalesOrderLineId
        LEFT JOIN tbSOExportApprovalStatusMaster sm ON sm.ApprovalStatusId = es.ApprovalStatusId
        WHERE so.SalesOrderId = @SalesOrderNo
    ) AS v
    WHERE QuantityShipped < Quantity
    ORDER BY DatePromised;
END


GO


