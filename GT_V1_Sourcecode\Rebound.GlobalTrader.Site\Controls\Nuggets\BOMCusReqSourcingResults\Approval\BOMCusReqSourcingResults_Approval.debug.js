///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Approval = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Approval.initializeBase(this, [element]);
    this._intRequirementLineID = -1;
    this._intBOMID = -1;
    this._CustReqNo = -1;
    this._intSourcingResultID = -1;
  
    this._BomCode = -1;
    this._BomName = null;
    this._BomCompanyName = null;
    this._BomCompanyNo = null;
    this._SalesManNo = null;
    this._SalesManName = null;
    this._ReqSalesman = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Approval.prototype = {

    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function() { return this._BomCompanyNo; }, set_BomCompanyNo: function(value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
    get_SalesManNo: function() { return this._SalesManNo; }, set_SalesManNo: function(value) { if (this._SalesManNo !== value) this._SalesManNo = value; },
    get_SalesManName: function() { return this._SalesManName; }, set_SalesManName: function(value) { if (this._SalesManName !== value) this._SalesManName = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Approval.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intRequirementLineID = null;
        this._ReqSalesman = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Approval.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
           // alert(this._BomCompanyNo);
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
    },

    yesClicked: function() {
        this.showSaving(true);
        //alert(this._intSourcingResultID);
        //return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/BOMCusReqSourcingResults");
        obj.set_DataObject("BOMCusReqSourcingResults");
        obj.set_DataAction("Approval");      

        obj.addParameter("id", this._intRequirementLineID);
        obj.addParameter("BomId", this._intBOMID);
        obj.addParameter("BomCode", this._BomCode);
        obj.addParameter("BomName", this._BomName);
        obj.addParameter("BomCompanyName", this._BomCompanyName);
        obj.addParameter("BomCompanyNo", this._BomCompanyNo);
        obj.addParameter("SalesManName", this._SalesManName);
        obj.addParameter("SalesManNo", this._SalesManNo);
        obj.addParameter("CustReqNo", this._CustReqNo);
        obj.addParameter("SID", this._intSourcingResultID);
        obj.addParameter("Reqsalesman", this._ReqSalesman);

        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },


    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    }



};

Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Approval.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Approval", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
