//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
    public partial class UnProcessSalesOrders : Base
    {
		protected Panel _pnlOpen;
		protected Panel _pnlOverdue;
		protected SimpleDataTable _tblOpen;
		protected SimpleDataTable _tblOverdue;
		protected PageHyperLink _lnkMore;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "UnProcessSalesOrders";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.UnProcessSalesOrders.UnProcessSalesOrders.js");
		}
        
		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.UnProcessSalesOrders", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOpen", _pnlOpen.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlOverdue", _pnlOverdue.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblOpen", _tblOpen.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblOverdue", _tblOverdue.ClientID);
            //_scScriptControlDescriptor.AddElementProperty("pnlMore", FindContentControl("pnlMore").ClientID);
            //_lnkMore.AddQueryStringVariable(QueryStringManager.QueryStringVariables.BypassSavedState, true, QueryStringManager.QueryStringVariables.SalesPersonID, Convert.ToString(SessionManager.LoginID));
            //_scScriptControlDescriptor.AddElementProperty("lnkMore", _lnkMore.ClientID);
            _scScriptControlDescriptor.AddProperty("myLoginID", Convert.ToString(SessionManager.LoginID));

			
            
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblOpen.Columns.Add(new SimpleDataColumn("SalesOrder", Unit.Pixel(65)));
			_tblOpen.Columns.Add(new SimpleDataColumn("Customer"));
			_tblOpen.Columns.Add(new SimpleDataColumn("DueDate", Unit.Pixel(75)));
			_tblOverdue.Columns.Add(new SimpleDataColumn("SalesOrder", Unit.Pixel(65)));
			_tblOverdue.Columns.Add(new SimpleDataColumn("Customer"));
			_tblOverdue.Columns.Add(new SimpleDataColumn("DueDate", Unit.Pixel(75)));
		}

		private void WireUpControls() {
			_pnlOpen = (Panel)FindContentControl("pnlOpen");
			_pnlOverdue = (Panel)FindContentControl("pnlOverdue");
			_tblOpen = (SimpleDataTable)FindContentControl("tblOpen");
			_tblOverdue = (SimpleDataTable)FindContentControl("tblOverdue");
			//_lnkMore = (PageHyperLink)FindContentControl("lnkMore");
		}
	}
}