﻿/*
Marker     changed by      date          Remarks
[001]      Vinay           08/07/2013    Ref:## -32 Nice Label Printing
[002]      Vinay           28/08/2013    NPR Print
[003]      <PERSON><PERSON><PERSON>         27/02/2014    GoodsIn - Po serial No.
[004]     A<PERSON><PERSON>av goyal    09/11/2017    Ref:##- Global Security
[005]     <PERSON><PERSON><PERSON>      26-Oct-2018   Issue - with GI line edit at a time in two tabs.
[006]      <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
[007]     bhooma&Sunil      29-Jul-2021   To Get All PO Variance Information
[008]     A<PERSON><PERSON>av <PERSON>   10/11/2021    Add new functions for the GI lines query messages.
[009]     Ab<PERSON>av <PERSON>   28/12/2021    Add new columns for query message.
[010]     A<PERSON><PERSON>av <PERSON>   19/01/2022    Add ability to CC any GT Users.
[011]     Ab<PERSON>av <PERSON>   09/02/2022    Add new function GetQueryHICStatus.
[012]     Ravi             19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages 

[006]     bho<PERSON>&<PERSON>il      29-Jul-2021   To Get All PO Variance Information
[007]     bhooma           03-Feb-2022    Change Short Shipment button as look like NPR.
[012]     <PERSON>     21-03-2023     RP-968  Barcode dropdown box
[013]     Ravi             08-06-2023     RP-1758
[014]     Ravi             22/09/2023   RP-2339 AS6081 GT Documents - Show AS6081 on detail screens
Code merge for GI Line
*/
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlGoodsInLineProvider : GoodsInLineProvider
    {
        /// <summary>
        /// Count GoodsInLine
        /// Calls [usp_count_GoodsInLine_for_PurchaseOrderLine]
        /// </summary>
        public override Int32 CountForPurchaseOrderLine(System.Int32? purchaseOrderLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_count_GoodsInLine_for_PurchaseOrderLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@PurchaseOrderLineId", SqlDbType.Int).Value = purchaseOrderLineId;
                cn.Open();
                return (Int32)ExecuteScalar(cmd);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to count GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_GoodsInLine]
        /// </summary>
        public override List<GoodsInLineDetails> DataListNugget(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String cmSearch, System.Int32? receivedBy, System.String airWayBill, System.Boolean? includeInvoiced, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.Int32? goodsInNoLo, System.Int32? goodsInNoHi, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.String supplierInvoice, System.String reference, System.Boolean? recentOnly, System.Boolean? uninspectedOnly, System.Int32? clientSearch, int? IsPoHub, Boolean IsGlobalLogin, int? warehouse, System.Boolean? IsQueriedTab, System.Int32? QueryProgressStatus, System.Boolean? AS6081)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (IsQueriedTab == true)
                {
                    cmd = new SqlCommand("usp_datalistnugget_QueriedGoodsInLine", cn);
                }
                else
                {
                    cmd = new SqlCommand("usp_datalistnugget_GoodsInLine", cn);
                }

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@ReceivedBy", SqlDbType.Int).Value = receivedBy;
                cmd.Parameters.Add("@AirWayBill", SqlDbType.NVarChar).Value = airWayBill;
                cmd.Parameters.Add("@IncludeInvoiced", SqlDbType.Bit).Value = includeInvoiced;
                cmd.Parameters.Add("@PurchaseOrderNoLo", SqlDbType.Int).Value = purchaseOrderNoLo;
                cmd.Parameters.Add("@PurchaseOrderNoHi", SqlDbType.Int).Value = purchaseOrderNoHi;
                cmd.Parameters.Add("@GoodsInNoLo", SqlDbType.Int).Value = goodsInNoLo;
                cmd.Parameters.Add("@GoodsInNoHi", SqlDbType.Int).Value = goodsInNoHi;
                cmd.Parameters.Add("@DateReceivedFrom", SqlDbType.DateTime).Value = dateReceivedFrom;
                cmd.Parameters.Add("@DateReceivedTo", SqlDbType.DateTime).Value = dateReceivedTo;
                cmd.Parameters.Add("@SupplierInvoice", SqlDbType.NVarChar).Value = supplierInvoice;
                cmd.Parameters.Add("@Reference", SqlDbType.NVarChar).Value = reference;
                cmd.Parameters.Add("@RecentOnly", SqlDbType.Bit).Value = recentOnly;
                cmd.Parameters.Add("@UninspectedOnly", SqlDbType.Bit).Value = uninspectedOnly;
                cmd.Parameters.Add("@ClientSearch", SqlDbType.Int).Value = clientSearch;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Int).Value = IsPoHub ?? 0;
                cmd.Parameters.Add("@IsGlobalLogin", SqlDbType.Bit).Value = IsGlobalLogin;
                cmd.Parameters.Add("@WarehouseNo", SqlDbType.Int).Value = warehouse;
                cmd.Parameters.Add("@AS6081", SqlDbType.Bit).Value = AS6081; //[013]
                if (IsQueriedTab == true)
                {
                    cmd.Parameters.Add("@QueryProgressStatus", SqlDbType.Int).Value = QueryProgressStatus;
                }
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.GoodsInId = GetReaderValue_Int32(reader, "GoodsInId", 0);
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "DeliveryDate", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.ClientNo = GetReaderValue_NullableInt32(reader, "ClientNo", null) ?? 0;
                    obj.InternalPurchaseOrderId = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderId", null);
                    obj.InternalPurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderNumber", null);
                    obj.IPOSupplier = GetReaderValue_NullableInt32(reader, "IPOSupplier", null);
                    obj.IPOSupplierName = GetReaderValue_String(reader, "IPOSupplierName", "");
                    obj.GIStatus = GetReaderValue_NullableInt32(reader, "StatusNo", null);
                    obj.SupplierMessage = GetReaderValue_String(reader, "SupplierMessage", "");
                    obj.GoodInLineMessage = GetReaderValue_String(reader, "GoodInLineMessage", "");
                    obj.QueryRaised = GetReaderValue_Boolean(reader, "QueryRaised", false);
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.AS6081 = GetReaderValue_Boolean(reader, "AS6081", false); //[013]
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //Code Start IHS
        /// <summary>
        /// DataListNugget  IHS
        /// Calls [usp_datalistnugget_IHSCatalogue]
        /// </summary>
        public override List<GoodsInLineDetails> DataListNuggetIHS(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String partSearch, System.String MfrSearch, System.Int32? countryOforigin, System.String MSL, System.String HtcCode, System.String Description, System.Boolean? recentOnly, int? IsPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_IHSCatalogue", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@Manufacturer", SqlDbType.NVarChar).Value = MfrSearch;
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = countryOforigin;
                cmd.Parameters.Add("@MSL", SqlDbType.NVarChar).Value = MSL;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HtcCode;
                cmd.Parameters.Add("@Description", SqlDbType.NVarChar).Value = Description;
                cmd.Parameters.Add("@RecentOnly", SqlDbType.Bit).Value = recentOnly;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Int).Value = IsPoHub ?? 0;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.IHSPartsId = GetReaderValue_Int32(reader, "IHSPartsId", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "CountryOfOrigin", "");
                    obj.CountryOfOriginNo = GetReaderValue_Int32(reader, "CountryOfOriginNo", 0);
                    obj.LifeCycleStage = GetReaderValue_String(reader, "LifeCycleStage", "");
                    obj.MSL = GetReaderValue_String(reader, "MSL", "");
                    obj.MSLNo = GetReaderValue_Int32(reader, "MSLNo", 0);
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packaging", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackageCode", "");
                    obj.MSLName = GetReaderValue_String(reader, "MSLName", "");
                    obj.OriginalEntryDate = GetReaderValue_NullableDateTime(reader, "OriginalEntryDate", null);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.IHSCurrencyCode = GetReaderValue_String(reader, "ColPriceCurrency", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.IsPDFAvailable = GetReaderValue_Boolean(reader, "IsPDFAvailable", false);
                    obj.APIImportedData = GetReaderValue_Boolean(reader, "APIImportedData", true);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get HTC CataLogue", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //Code End IHS

        /// <summary>
        /// DataListNuggetAsReceivedPO 
        /// Calls [usp_datalistnugget_GoodsInLine_AsReceivedPO]
        /// </summary>
        public override List<GoodsInLineDetails> DataListNuggetAsReceivedPO(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? orderBy, System.Int32? sortDir, System.Int32? purchaseOrderNoLo, System.Int32? purchaseOrderNoHi, System.String partSearch, System.Boolean? recentOnly, System.String cmSearch, System.String contactSearch, System.Int32? buyerSearch, System.DateTime? receivedDateFrom, System.DateTime? receivedDateTo, System.String airWayBill, System.String supplierPartSearch, System.String reference, bool? isPoHub)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_GoodsInLine_AsReceivedPO", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PurchaseOrderNoLo", SqlDbType.Int).Value = purchaseOrderNoLo;
                cmd.Parameters.Add("@PurchaseOrderNoHi", SqlDbType.Int).Value = purchaseOrderNoHi;
                cmd.Parameters.Add("@PartSearch", SqlDbType.NVarChar).Value = partSearch;
                cmd.Parameters.Add("@RecentOnly", SqlDbType.Bit).Value = recentOnly;
                cmd.Parameters.Add("@CMSearch", SqlDbType.NVarChar).Value = cmSearch;
                cmd.Parameters.Add("@ContactSearch", SqlDbType.NVarChar).Value = contactSearch;
                cmd.Parameters.Add("@BuyerSearch", SqlDbType.Int).Value = buyerSearch;
                cmd.Parameters.Add("@ReceivedDateFrom", SqlDbType.DateTime).Value = receivedDateFrom;
                cmd.Parameters.Add("@ReceivedDateTo", SqlDbType.DateTime).Value = receivedDateTo;
                cmd.Parameters.Add("@AirWayBill", SqlDbType.NVarChar).Value = airWayBill;
                cmd.Parameters.Add("@SupplierPartSearch", SqlDbType.NVarChar).Value = supplierPartSearch;
                cmd.Parameters.Add("@Reference", SqlDbType.NVarChar).Value = reference;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPoHub;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    //obj.IpoCount = GetReaderValue_Int32(reader, "IpoCount", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", "");
                    obj.InvoiceAmount = GetReaderValue_NullableDouble(reader, "InvoiceAmount", null);
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    lst.Add(obj);
                    obj = null;

                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Delete GoodsInLine
        /// Calls [usp_delete_GoodsInLine]
        /// </summary>
        public override bool Delete(System.Int32? goodsInLineId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_GoodsInLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_GoodsInLine]
        /// </summary>
        public override Int32 Insert(System.Int32? goodsInNo, System.Int32? purchaseOrderLineNo, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.Int32? currencyNo, System.Int32? customerRmaLineNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Boolean? unavailable, System.String notes, System.String changedFields, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, out System.String strMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            strMessage = "";
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_GoodsInLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInNo;
                cmd.Parameters.Add("@PurchaseOrderLineNo", SqlDbType.Int).Value = purchaseOrderLineNo;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@ShipInCost", SqlDbType.Float).Value = shipInCost;
                cmd.Parameters.Add("@QualityControlNotes", SqlDbType.NVarChar).Value = qualityControlNotes;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = lotNo;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@CustomerRMALineNo", SqlDbType.Int).Value = customerRmaLineNo;
                cmd.Parameters.Add("@SupplierPart", SqlDbType.NVarChar).Value = supplierPart;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@CountryOfManufacture", SqlDbType.Int).Value = countryOfManufacture;
                cmd.Parameters.Add("@Unavailable", SqlDbType.Bit).Value = unavailable;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@ChangedFields", SqlDbType.NVarChar).Value = changedFields;
                cmd.Parameters.Add("@CountingMethodNo", SqlDbType.Int).Value = countingMethodNo;
                cmd.Parameters.Add("@SerialNosRecorded", SqlDbType.Bit).Value = serialNosRecorded;
                cmd.Parameters.Add("@PartMarkings", SqlDbType.NVarChar).Value = partMarkings;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientPrice", SqlDbType.Float).Value = clientPrice;
                cmd.Parameters.Add("@ReqSerialNo", SqlDbType.Bit).Value = reqSerialNo;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@strMessage", SqlDbType.NVarChar, 400).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                strMessage = (string)cmd.Parameters["@strMessage"].Value;
                return (Int32)cmd.Parameters["@GoodsInLineId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to insert GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override Int32 InsertIHS(System.String part, System.String partStatus, int? manufacturerNo, DateTime? OriginalEntryDate, 
            int? PackageNo, string Descriptions, string HTSCode, int? CountryOfManufacture, string PackageName, int? UpdatedBy, 
            string MSLLevel, string ECCNCode)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("ups_Insert_Manual_IHS", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@PartStatus", SqlDbType.NVarChar).Value = partStatus;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@OriginalEntryDate", SqlDbType.DateTime).Value = OriginalEntryDate;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = PackageNo;
                cmd.Parameters.Add("@Descriptions", SqlDbType.NVarChar).Value = Descriptions;
                cmd.Parameters.Add("@HTSCode", SqlDbType.NVarChar).Value = HTSCode;
                cmd.Parameters.Add("@CountryOfOriginNo", SqlDbType.Int).Value = CountryOfManufacture;
                cmd.Parameters.Add("@PackageCode", SqlDbType.NVarChar).Value = PackageName;
                cmd.Parameters.Add("@MSLLevel", SqlDbType.NVarChar).Value = MSLLevel;
                cmd.Parameters.Add("@ECCNCode", SqlDbType.NVarChar).Value = ECCNCode;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = UpdatedBy;
                cmd.Parameters.Add("@IHSId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@IHSId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to insert InsertIHS", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Get 
        /// Calls [usp_select_GoodsInLine]
        /// </summary>
        public override GoodsInLineDetails Get(System.Int32? goodsInLineId, System.Int32? LoginId, System.Int32? ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GoodsInLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetGoodsInLineFromReader(reader);
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //[005] start
                    obj.StringDLUP = reader.GetDateTime(reader.GetOrdinal("DLUP")).ToString("yyyy-MM-dd HH:mm:ss.fff");
                    //[005] end
                    obj.InspectedBy = GetReaderValue_NullableInt32(reader, "InspectedBy", null);
                    obj.DateInspected = GetReaderValue_NullableDateTime(reader, "DateInspected", null);
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.SerialNosRecorded = GetReaderValue_NullableBoolean(reader, "SerialNosRecorded", null);
                    obj.Unavailable = GetReaderValue_NullableBoolean(reader, "Unavailable", null);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0);
                    obj.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    obj.LineNotes = GetReaderValue_String(reader, "LineNotes", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductDutyCode = GetReaderValue_String(reader, "ProductDutyCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.InspectorName = GetReaderValue_String(reader, "InspectorName", "");
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ReceivedBy = GetReaderValue_Int32(reader, "ReceivedBy", 0);
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null);
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.PurchaseOrderLineShipInCost = GetReaderValue_Double(reader, "PurchaseOrderLineShipInCost", 0);
                    obj.ChangedFields = GetReaderValue_String(reader, "ChangedFields", "");
                    obj.UpdateStock = GetReaderValue_NullableBoolean(reader, "UpdateStock", null);
                    obj.UpdateShipments = GetReaderValue_NullableBoolean(reader, "UpdateShipments", null);
                    obj.HasAllocationOutward = GetReaderValue_NullableBoolean(reader, "HasAllocationOutward", false);
                    obj.SalesOrderNumber = GetReaderValue_NullableInt32(reader, "SalesOrderNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPartNo", string.Empty);
                    obj.NPRPrinted = GetReaderValue_NullableBoolean(reader, "NPRPrinted", false);
                    //[001] code start
                    obj.InspectorNameLabel = GetReaderValue_String(reader, "InspectorNameLabel", string.Empty);
                    //[001] code end
                    //[002] code start
                    obj.NPRIds = GetReaderValue_String(reader, "NPRIds", string.Empty);
                    obj.NPRNos = GetReaderValue_String(reader, "NPRNos", string.Empty);
                    //[002] code end
                    obj.HasStocksplit = GetReaderValue_Boolean(reader, "Stocksplit", false);
                    obj.HasSupplierInvoiceExists = GetReaderValue_Boolean(reader, "SupplierInvoiceExists", false);
                    obj.blnStockProvision = GetReaderValue_Boolean(reader, "blnStockProvision", false);
                    obj.LotCode = GetReaderValue_String(reader, "LotCode", "");

                    obj.InternalPurchaseOrderId = GetReaderValue_Int32(reader, "InternalPurchaseOrderNo", 0);
                    obj.ClientLandedCost = GetReaderValue_Double(reader, "ClientLandedCost", 0);
                    obj.ClientPrice = GetReaderValue_Double(reader, "ClientPrice", 0);

                    obj.ClientCurrencyNo = GetReaderValue_NullableInt32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.IPOClientNo = GetReaderValue_NullableInt32(reader, "IPOClientNo", null);
                    obj.POBankFee = GetReaderValue_NullableDouble(reader, "POBankFee", null);
                    obj.CustomerPO = GetReaderValue_String(reader, "CustomerPO", "");
                    obj.StockDate = GetReaderValue_DateTime(reader, "StockDate", DateTime.MinValue);
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.DutyRate = GetReaderValue_NullableDouble(reader, "ProductDutyRate", null);
                    obj.ReqSerialNo = GetReaderValue_NullableBoolean(reader, "ReqSerialNo", false);
                    obj.SerialNoCount = GetReaderValue_NullableInt32(reader, "SerialNoCount", 0);
                    obj.MSLLevel = GetReaderValue_String(reader, "MSLLevel", "");
                    obj.IsProdHazardous = GetReaderValue_NullableBoolean(reader, "IsProdHazardous", false);
                    obj.PrintHazardous = GetReaderValue_NullableBoolean(reader, "PrintHazardous", false);
                    obj.ParentGoodsInLineId = GetReaderValue_Int32(reader, "ParentGILineNo", 0);
                    obj.TotalShipCost = GetReaderValue_NullableDouble(reader, "TotalShipInCost", 0);
                    //code start for ihs
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "IHSCountryOfOrigin", "");
                    obj.LifeCycleStage = GetReaderValue_String(reader, "LifeCycleStage", "");
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packing", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.SalesPersonId = GetReaderValue_NullableInt32(reader, "SalesPersonId", null);
                    obj.SalesPersonName = GetReaderValue_String(reader, "SalesPersonName", "");
                    //[006] code start
                    obj.IsOrderViaIPOonly = GetReaderValue_NullableBoolean(reader, "IsOrderViaIPOonly", false);
                    //[006] code end

                    obj.IsFullQtyRecieved = GetReaderValue_NullableBoolean(reader, "IsFullQtyRecieved", null);
                    obj.IsPartNoCorrect = GetReaderValue_NullableBoolean(reader, "IsPartNoCorrect", null);
                    obj.CorrectPartNo = GetReaderValue_String(reader, "CorrectPartNo", "");
                    obj.IsManufacturerCorrect = GetReaderValue_NullableBoolean(reader, "IsManufacturerCorrect", null);
                    obj.CorrectManufacturer = GetReaderValue_NullableInt32(reader, "CorrectManufacturer", null);
                    obj.CorrectManufacturerName = GetReaderValue_String(reader, "CorrectManufacturerName", "");
                    obj.IsDateCodeCorrect = GetReaderValue_NullableBoolean(reader, "IsDateCodeCorrect", null);
                    obj.CorrectDateCode = GetReaderValue_String(reader, "CorrectDateCode", "");
                    obj.IsDateCodeRequired = GetReaderValue_NullableBoolean(reader, "IsDateCodeRequired", null);
                    obj.IsPackageTypeCorrect = GetReaderValue_NullableBoolean(reader, "IsPackageTypeCorrect", null);
                    obj.CorrectPackageType = GetReaderValue_NullableInt32(reader, "CorrectPackageType", null);
                    obj.CorrectPackageName = GetReaderValue_String(reader, "CorrectPackageName", "");
                    obj.IsMSLLevelCorrect = GetReaderValue_NullableBoolean(reader, "IsMSLLevelCorrect", null);
                    obj.CorrectMSLLevel = GetReaderValue_String(reader, "CorrectMSLLevel", "");
                    obj.HIC_Status = GetReaderValue_NullableInt32(reader, "HICStatus", null);
                    obj.IsHICStatusCorrect = GetReaderValue_NullableBoolean(reader, "IsHICStatusCorrect", null);
                    obj.CorrectHICStatus = GetReaderValue_String(reader, "CorrectHICStatus", "");
                    obj.PKGBreakdownMismatch = GetReaderValue_String(reader, "PKGBreakdownMismatch", "");
                    obj.IsROHSStatusCorrect = GetReaderValue_NullableBoolean(reader, "IsROHSStatusCorrect", null);
                    obj.CorrectROHSStatus = GetReaderValue_NullableInt32(reader, "CorrectROHSStatus", null);
                    obj.IsLotCodesReq = GetReaderValue_NullableBoolean(reader, "IsLotCodesReq", null);
                    obj.BakingLevelAdded = GetReaderValue_NullableInt32(reader, "BakingLevelAdded", null);
                    obj.EnhancedInspectionReq = GetReaderValue_NullableBoolean(reader, "EnhancedInspectionReq", null);
                    obj.GeneralInspectionNotes = GetReaderValue_String(reader, "GeneralInspectionNotes", "");
                    obj.IsInspectionConducted = GetReaderValue_NullableBoolean(reader, "IsInspectionConducted", null);
                    obj.CompanyType = GetReaderValue_String(reader, "ClientCompanyType", "");
                    obj.ReqLotNo = GetReaderValue_NullableBoolean(reader, "ReqLotNo", false);
                    obj.LotNoCount = GetReaderValue_NullableInt32(reader, "LotNoCount", 0);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", 0);

                    obj.POPart = GetReaderValue_String(reader, "POPart", "");
                    obj.POManufacturerNo = GetReaderValue_NullableInt32(reader, "POManufacturerNo", null);
                    obj.POManufacturerName = GetReaderValue_String(reader, "POManufacturerName", "");
                    obj.PODateCode = GetReaderValue_String(reader, "PODateCode", "");
                    obj.POPackageNo = GetReaderValue_NullableInt32(reader, "POPackageNo", null);
                    obj.POPackageType = GetReaderValue_String(reader, "POPackageType", "");
                    obj.POMSLLevel = GetReaderValue_String(reader, "POMSLLevel", "");
                    obj.POROHS = GetReaderValue_NullableInt32(reader, "POROHS", null);
                    obj.POROHSStatus = GetReaderValue_String(reader, "POROHSStatus", "");
                    obj.POQuantity = GetReaderValue_NullableInt32(reader, "POQuantity", null);
                    obj.ReleseStockDisbaleReason = GetReaderValue_String(reader, "ReleseStockDisbaleReason", "");

                    obj.ActeoneTestStatus = GetReaderValue_NullableInt32(reader, "ActeoneTestStatus", null);
                    obj.IsopropryleStatus = GetReaderValue_NullableInt32(reader, "IsopropryleStatus", null);
                    obj.ActeoneTest = GetReaderValue_String(reader, "ActeoneTest", "");
                    obj.Isopropryle = GetReaderValue_String(reader, "Isopropryle", "");
                    obj.HICStatusName = GetReaderValue_String(reader, "HICStatusName", "");
                    obj.ReleseStockDisbaleStatus = GetReaderValue_NullableBoolean(reader, "ReleseStockDisbaleStatus", false);
                    obj.UpdateType = GetReaderValue_String(reader, "UpdateType", "");
                    obj.QueryBakingLevel = GetReaderValue_String(reader, "QueryBakingLevel", "");
                    obj.EnhancedInspectionStatusId = GetReaderValue_NullableInt32(reader, "EnhInpectionReqId", null);
                    obj.IsSendQuery = GetReaderValue_NullableBoolean(reader, "IsSendQuery", null);
                    obj.IsShortShipmentEnable = GetReaderValue_NullableBoolean(reader, "IsShortShipmentEnable", null);
                    obj.ShortShipmentId = GetReaderValue_NullableInt32(reader, "ShortShipmentId", null);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", null);
                    //[007] code start
                    obj.ShortShipmentIds = GetReaderValue_String(reader, "ShortShipmentIds", string.Empty);
                    obj.ShortShipmentNos = GetReaderValue_String(reader, "ShortShipmentNos", string.Empty);
                    //[007] code end
                    //code end for ihs
                    obj.PrintDateCode = GetReaderValue_String(reader, "PrintableDC", string.Empty);
                    obj.SalesApproverIds = GetReaderValue_String(reader, "SalesApproverIds", string.Empty);
                    obj.AllocatedSalesOrderNumber = GetReaderValue_NullableInt32(reader, "AllocatedSalesOrderNumber", null);
                    //[012] start
                    obj.HasBarcodeScan = GetReaderValue_NullableInt32(reader, "HasBarCodeScan", null);
                    obj.BarcodeScanRemarks = GetReaderValue_String(reader, "BarcodeScanRemarks", string.Empty);
                    obj.IsStartInspection = GetReaderValue_NullableBoolean(reader, "IsStartInspection", null);
                    obj.ISCloseInspection = GetReaderValue_NullableBoolean(reader, "ISCloseInspection", null);

                    obj.PartNoQuery = GetReaderValue_String(reader, "PartNoQuery", "");
                    obj.ManufacturerQuery = GetReaderValue_String(reader, "ManufacturerQuery", "");
                    obj.PackagingTypeQuery = GetReaderValue_String(reader, "PackagingTypeQuery", "");
                    obj.MslQuery = GetReaderValue_String(reader, "MslQuery", "");
                    obj.RohsQuery = GetReaderValue_String(reader, "RohsQuery", "");
                    obj.IsEditInspection = GetReaderValue_NullableBoolean(reader, "IsEditInspection", false);
                    obj.IsAlreadyClosed = GetReaderValue_NullableBoolean(reader, "IsAlreadyClosed", false);
                    obj.GiInvoiceExported = GetReaderValue_NullableBoolean(reader, "GiInvoiceExported", false);
                    obj.GeneralInspectionQuery = GetReaderValue_NullableBoolean(reader, "ISReaiseGeneralQuery", false);
                    obj.IsEditAfterStartInspection = GetReaderValue_NullableBoolean(reader, "IsEditAfterStartInspection", false);
                    //[012] end
                    obj.AS6081 = GetReaderValue_Boolean(reader, "AS6081", false); //[014]
                    obj.IsSanctioned = GetReaderValue_Boolean(reader, "IsSanctioned", false); //[RP-881]
                    obj.IsSTOGi = GetReaderValue_Boolean(reader, "IsSTOGi", false);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [usp_select_GoodsInLine]
        /// </summary>
        public override GoodsInLineDetails GetTemp(System.Int32? goodsInLineId, System.Int32? LoginId, System.Int32? ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GoodsInLineTemp", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetGoodsInLineFromReader(reader);
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //[005] start
                    obj.StringDLUP = reader.GetDateTime(reader.GetOrdinal("DLUP")).ToString("yyyy-MM-dd HH:mm:ss.fff");
                    //[005] end
                    obj.InspectedBy = GetReaderValue_NullableInt32(reader, "InspectedBy", null);
                    obj.DateInspected = GetReaderValue_NullableDateTime(reader, "DateInspected", null);
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.SerialNosRecorded = GetReaderValue_NullableBoolean(reader, "SerialNosRecorded", null);
                    obj.Unavailable = GetReaderValue_NullableBoolean(reader, "Unavailable", null);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0);
                    obj.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    obj.LineNotes = GetReaderValue_String(reader, "LineNotes", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductDutyCode = GetReaderValue_String(reader, "ProductDutyCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.InspectorName = GetReaderValue_String(reader, "InspectorName", "");
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ReceivedBy = GetReaderValue_Int32(reader, "ReceivedBy", 0);
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null);
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.PurchaseOrderLineShipInCost = GetReaderValue_Double(reader, "PurchaseOrderLineShipInCost", 0);
                    obj.ChangedFields = GetReaderValue_String(reader, "ChangedFields", "");
                    obj.UpdateStock = GetReaderValue_NullableBoolean(reader, "UpdateStock", null);
                    obj.UpdateShipments = GetReaderValue_NullableBoolean(reader, "UpdateShipments", null);
                    obj.HasAllocationOutward = GetReaderValue_NullableBoolean(reader, "HasAllocationOutward", false);
                    obj.SalesOrderNumber = GetReaderValue_NullableInt32(reader, "SalesOrderNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPartNo", string.Empty);
                    obj.NPRPrinted = GetReaderValue_NullableBoolean(reader, "NPRPrinted", false);
                    //[001] code start
                    obj.InspectorNameLabel = GetReaderValue_String(reader, "InspectorNameLabel", string.Empty);
                    //[001] code end
                    //[002] code start
                    obj.NPRIds = GetReaderValue_String(reader, "NPRIds", string.Empty);
                    obj.NPRNos = GetReaderValue_String(reader, "NPRNos", string.Empty);
                    //[002] code end
                    obj.HasStocksplit = GetReaderValue_Boolean(reader, "Stocksplit", false);
                    obj.HasSupplierInvoiceExists = GetReaderValue_Boolean(reader, "SupplierInvoiceExists", false);
                    obj.blnStockProvision = GetReaderValue_Boolean(reader, "blnStockProvision", false);
                    obj.LotCode = GetReaderValue_String(reader, "LotCode", "");

                    obj.InternalPurchaseOrderId = GetReaderValue_Int32(reader, "InternalPurchaseOrderNo", 0);
                    obj.ClientLandedCost = GetReaderValue_Double(reader, "ClientLandedCost", 0);
                    obj.ClientPrice = GetReaderValue_Double(reader, "ClientPrice", 0);

                    obj.ClientCurrencyNo = GetReaderValue_NullableInt32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.IPOClientNo = GetReaderValue_NullableInt32(reader, "IPOClientNo", null);
                    obj.POBankFee = GetReaderValue_NullableDouble(reader, "POBankFee", null);
                    obj.CustomerPO = GetReaderValue_String(reader, "CustomerPO", "");
                    obj.StockDate = GetReaderValue_DateTime(reader, "StockDate", DateTime.MinValue);
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.DutyRate = GetReaderValue_NullableDouble(reader, "ProductDutyRate", null);
                    obj.ReqSerialNo = GetReaderValue_NullableBoolean(reader, "ReqSerialNo", false);
                    obj.SerialNoCount = GetReaderValue_NullableInt32(reader, "SerialNoCount", 0);
                    obj.MSLLevel = GetReaderValue_String(reader, "MSLLevel", "");
                    obj.IsProdHazardous = GetReaderValue_NullableBoolean(reader, "IsProdHazardous", false);
                    obj.PrintHazardous = GetReaderValue_NullableBoolean(reader, "PrintHazardous", false);
                    obj.ParentGoodsInLineId = GetReaderValue_Int32(reader, "ParentGILineNo", 0);
                    obj.TotalShipCost = GetReaderValue_NullableDouble(reader, "TotalShipInCost", 0);
                    //code start for ihs
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "IHSCountryOfOrigin", "");
                    obj.LifeCycleStage = GetReaderValue_String(reader, "LifeCycleStage", "");
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packing", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");
                    obj.SalesPersonId = GetReaderValue_NullableInt32(reader, "SalesPersonId", null);
                    obj.SalesPersonName = GetReaderValue_String(reader, "SalesPersonName", "");
                    //[006] code start
                    obj.IsOrderViaIPOonly = GetReaderValue_NullableBoolean(reader, "IsOrderViaIPOonly", false);
                    //[006] code end

                    obj.IsFullQtyRecieved = GetReaderValue_NullableBoolean(reader, "IsFullQtyRecieved", null);
                    obj.IsPartNoCorrect = GetReaderValue_NullableBoolean(reader, "IsPartNoCorrect", null);
                    obj.CorrectPartNo = GetReaderValue_String(reader, "CorrectPartNo", "");
                    obj.IsManufacturerCorrect = GetReaderValue_NullableBoolean(reader, "IsManufacturerCorrect", null);
                    obj.CorrectManufacturer = GetReaderValue_NullableInt32(reader, "CorrectManufacturer", null);
                    obj.CorrectManufacturerName = GetReaderValue_String(reader, "CorrectManufacturerName", "");
                    obj.IsDateCodeCorrect = GetReaderValue_NullableBoolean(reader, "IsDateCodeCorrect", null);
                    obj.CorrectDateCode = GetReaderValue_String(reader, "CorrectDateCode", "");
                    obj.IsDateCodeRequired = GetReaderValue_NullableBoolean(reader, "IsDateCodeRequired", null);
                    obj.IsPackageTypeCorrect = GetReaderValue_NullableBoolean(reader, "IsPackageTypeCorrect", null);
                    obj.CorrectPackageType = GetReaderValue_NullableInt32(reader, "CorrectPackageType", null);
                    obj.CorrectPackageName = GetReaderValue_String(reader, "CorrectPackageName", "");
                    obj.IsMSLLevelCorrect = GetReaderValue_NullableBoolean(reader, "IsMSLLevelCorrect", null);
                    obj.CorrectMSLLevel = GetReaderValue_String(reader, "CorrectMSLLevel", "");
                    obj.HIC_Status = GetReaderValue_NullableInt32(reader, "HICStatus", null);
                    obj.IsHICStatusCorrect = GetReaderValue_NullableBoolean(reader, "IsHICStatusCorrect", null);
                    obj.CorrectHICStatus = GetReaderValue_String(reader, "CorrectHICStatus", "");
                    obj.PKGBreakdownMismatch = GetReaderValue_String(reader, "PKGBreakdownMismatch", "");
                    obj.IsROHSStatusCorrect = GetReaderValue_NullableBoolean(reader, "IsROHSStatusCorrect", null);
                    obj.CorrectROHSStatus = GetReaderValue_NullableInt32(reader, "CorrectROHSStatus", null);
                    obj.IsLotCodesReq = GetReaderValue_NullableBoolean(reader, "IsLotCodesReq", null);
                    obj.BakingLevelAdded = GetReaderValue_NullableInt32(reader, "BakingLevelAdded", null);
                    obj.EnhancedInspectionReq = GetReaderValue_NullableBoolean(reader, "EnhancedInspectionReq", null);
                    obj.GeneralInspectionNotes = GetReaderValue_String(reader, "GeneralInspectionNotes", "");
                    obj.IsInspectionConducted = GetReaderValue_NullableBoolean(reader, "IsInspectionConducted", null);
                    obj.CompanyType = GetReaderValue_String(reader, "ClientCompanyType", "");
                    obj.ReqLotNo = GetReaderValue_NullableBoolean(reader, "ReqLotNo", false);
                    obj.LotNoCount = GetReaderValue_NullableInt32(reader, "LotNoCount", 0);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", 0);

                    obj.POPart = GetReaderValue_String(reader, "POPart", "");
                    obj.POManufacturerNo = GetReaderValue_NullableInt32(reader, "POManufacturerNo", null);
                    obj.POManufacturerName = GetReaderValue_String(reader, "POManufacturerName", "");
                    obj.PODateCode = GetReaderValue_String(reader, "PODateCode", "");
                    obj.POPackageNo = GetReaderValue_NullableInt32(reader, "POPackageNo", null);
                    obj.POPackageType = GetReaderValue_String(reader, "POPackageType", "");
                    obj.POMSLLevel = GetReaderValue_String(reader, "POMSLLevel", "");
                    obj.POROHS = GetReaderValue_NullableInt32(reader, "POROHS", null);
                    obj.POROHSStatus = GetReaderValue_String(reader, "POROHSStatus", "");
                    obj.POQuantity = GetReaderValue_NullableInt32(reader, "POQuantity", null);
                    obj.ReleseStockDisbaleReason = GetReaderValue_String(reader, "ReleseStockDisbaleReason", "");

                    obj.ActeoneTestStatus = GetReaderValue_NullableInt32(reader, "ActeoneTestStatus", null);
                    obj.IsopropryleStatus = GetReaderValue_NullableInt32(reader, "IsopropryleStatus", null);
                    obj.ActeoneTest = GetReaderValue_String(reader, "ActeoneTest", "");
                    obj.Isopropryle = GetReaderValue_String(reader, "Isopropryle", "");
                    obj.HICStatusName = GetReaderValue_String(reader, "HICStatusName", "");
                    obj.ReleseStockDisbaleStatus = GetReaderValue_NullableBoolean(reader, "ReleseStockDisbaleStatus", false);
                    obj.UpdateType = GetReaderValue_String(reader, "UpdateType", "");
                    obj.QueryBakingLevel = GetReaderValue_String(reader, "QueryBakingLevel", "");
                    obj.EnhancedInspectionStatusId = GetReaderValue_NullableInt32(reader, "EnhInpectionReqId", null);
                    obj.IsSendQuery = GetReaderValue_NullableBoolean(reader, "IsSendQuery", null);
                    obj.IsShortShipmentEnable = GetReaderValue_NullableBoolean(reader, "IsShortShipmentEnable", null);
                    obj.ShortShipmentId = GetReaderValue_NullableInt32(reader, "ShortShipmentId", null);
                    obj.SupportTeamMemberNo = GetReaderValue_NullableInt32(reader, "SupportTeamMemberNo", null);
                    //[007] code start
                    obj.ShortShipmentIds = GetReaderValue_String(reader, "ShortShipmentIds", string.Empty);
                    obj.ShortShipmentNos = GetReaderValue_String(reader, "ShortShipmentNos", string.Empty);
                    //[007] code end
                    //code end for ihs
                    obj.PrintDateCode = GetReaderValue_String(reader, "PrintableDC", string.Empty);
                    obj.SalesApproverIds = GetReaderValue_String(reader, "SalesApproverIds", string.Empty);
                    obj.AllocatedSalesOrderNumber = GetReaderValue_NullableInt32(reader, "AllocatedSalesOrderNumber", null);
                    //[012] start
                    obj.HasBarcodeScan = GetReaderValue_NullableInt32(reader, "HasBarCodeScan", null);
                    obj.BarcodeScanRemarks = GetReaderValue_String(reader, "BarcodeScanRemarks", string.Empty);
                    obj.IsStartInspection = GetReaderValue_NullableBoolean(reader, "IsStartInspection", null);
                    obj.ISCloseInspection = GetReaderValue_NullableBoolean(reader, "ISCloseInspection", null);

                    obj.PartNoQuery = GetReaderValue_String(reader, "PartNoQuery", "");
                    obj.ManufacturerQuery = GetReaderValue_String(reader, "ManufacturerQuery", "");
                    obj.PackagingTypeQuery = GetReaderValue_String(reader, "PackagingTypeQuery", "");
                    obj.MslQuery = GetReaderValue_String(reader, "MslQuery", "");
                    obj.RohsQuery = GetReaderValue_String(reader, "RohsQuery", "");
                    obj.IsEditInspection = GetReaderValue_NullableBoolean(reader, "IsEditInspection", false);
                    obj.IsAlreadyClosed = GetReaderValue_NullableBoolean(reader, "IsAlreadyClosed", false);
                    obj.GiInvoiceExported = GetReaderValue_NullableBoolean(reader, "GiInvoiceExported", false);
                    obj.GeneralInspectionQuery = GetReaderValue_NullableBoolean(reader, "ISReaiseGeneralQuery", false);
                    //[012] end
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForCustomerRMA 
        /// Calls [usp_selectAll_GoodsInLine_for_CustomerRMA]
        /// </summary>
        public override List<GoodsInLineDetails> GetListForCustomerRMA(System.Int32? customerRmaId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GoodsInLine_for_CustomerRMA", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRMAId", SqlDbType.Int).Value = customerRmaId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.InspectedBy = GetReaderValue_NullableInt32(reader, "InspectedBy", null);
                    obj.DateInspected = GetReaderValue_NullableDateTime(reader, "DateInspected", null);
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.SerialNosRecorded = GetReaderValue_NullableBoolean(reader, "SerialNosRecorded", null);
                    obj.Unavailable = GetReaderValue_NullableBoolean(reader, "Unavailable", null);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0);
                    obj.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    obj.LineNotes = GetReaderValue_String(reader, "LineNotes", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductDutyCode = GetReaderValue_String(reader, "ProductDutyCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.InspectorName = GetReaderValue_String(reader, "InspectorName", "");
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ReceivedBy = GetReaderValue_Int32(reader, "ReceivedBy", 0);
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null);
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.PurchaseOrderLineShipInCost = GetReaderValue_Double(reader, "PurchaseOrderLineShipInCost", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForCustomerRMALine 
        /// Calls [usp_selectAll_GoodsInLine_for_CustomerRMALine]
        /// </summary>
        public override List<GoodsInLineDetails> GetListForCustomerRMALine(System.Int32? customerRmaLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GoodsInLine_for_CustomerRMALine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@CustomerRMALineId", SqlDbType.Int).Value = customerRmaLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.InspectedBy = GetReaderValue_NullableInt32(reader, "InspectedBy", null);
                    obj.DateInspected = GetReaderValue_NullableDateTime(reader, "DateInspected", null);
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.SerialNosRecorded = GetReaderValue_NullableBoolean(reader, "SerialNosRecorded", null);
                    obj.Unavailable = GetReaderValue_NullableBoolean(reader, "Unavailable", null);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0);
                    obj.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    obj.LineNotes = GetReaderValue_String(reader, "LineNotes", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductDutyCode = GetReaderValue_String(reader, "ProductDutyCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.InspectorName = GetReaderValue_String(reader, "InspectorName", "");
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ReceivedBy = GetReaderValue_Int32(reader, "ReceivedBy", 0);
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null);
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.PurchaseOrderLineShipInCost = GetReaderValue_Double(reader, "PurchaseOrderLineShipInCost", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForGoodsIn 
        /// Calls [usp_selectAll_GoodsInLine_for_GoodsIn]
        /// </summary>
        public override List<GoodsInLineDetails> GetListForGoodsIn(System.Int32? goodsInId, System.Int32? pageIndex, System.Int32? pageSize)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GoodsInLine_for_GoodsIn", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInId", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.InspectedBy = GetReaderValue_NullableInt32(reader, "InspectedBy", null);
                    obj.DateInspected = GetReaderValue_NullableDateTime(reader, "DateInspected", null);
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.SerialNosRecorded = GetReaderValue_NullableBoolean(reader, "SerialNosRecorded", null);
                    obj.Unavailable = GetReaderValue_NullableBoolean(reader, "Unavailable", null);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0);
                    obj.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    obj.LineNotes = GetReaderValue_String(reader, "LineNotes", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductDutyCode = GetReaderValue_String(reader, "ProductDutyCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.InspectorName = GetReaderValue_String(reader, "InspectorName", "");
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ReceivedBy = GetReaderValue_Int32(reader, "ReceivedBy", 0);
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null);
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.PurchaseOrderLineShipInCost = GetReaderValue_Double(reader, "PurchaseOrderLineShipInCost", 0);
                    obj.PhysicalInspectedBy = GetReaderValue_NullableInt32(reader, "PhysicalInspectedBy", 0);
                    obj.DatePhysicalInspected = GetReaderValue_NullableDateTime(reader, "DatePhysicalInspected", null);
                    //[003] Code Start
                    obj.POSerialNo = GetReaderValue_Int16(reader, "POSerialNo", 0);
                    //[003] Code End
                    obj.InternalPurchaseOrderId = GetReaderValue_NullableInt32(reader, "InternalPurchaseOrderNo", 0);
                    obj.ClientLandedCost = GetReaderValue_Double(reader, "ClientLandedCost", 0);
                    obj.ClientPrice = GetReaderValue_Double(reader, "ClientPrice", 0);
                    obj.ParentGoodsInLineId = GetReaderValue_NullableInt32(reader, "ParentGILineNo", 0);
                    obj.QueryRaised = GetReaderValue_NullableBoolean(reader, "QueryRaised", false);
                    obj.PrintDateCode = GetReaderValue_String(reader, "PrintableDC", "");
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.AS6081 = GetReaderValue_Boolean(reader, "AS6081", false); //[014]
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForPurchaseOrder 
        /// Calls [usp_selectAll_GoodsInLine_for_PurchaseOrder]
        /// </summary>
        public override List<GoodsInLineDetails> GetListForPurchaseOrder(System.Int32? purchaseOrderId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GoodsInLine_for_PurchaseOrder", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@PurchaseOrderId", SqlDbType.Int).Value = purchaseOrderId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.InspectedBy = GetReaderValue_NullableInt32(reader, "InspectedBy", null);
                    obj.DateInspected = GetReaderValue_NullableDateTime(reader, "DateInspected", null);
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.SerialNosRecorded = GetReaderValue_NullableBoolean(reader, "SerialNosRecorded", null);
                    obj.Unavailable = GetReaderValue_NullableBoolean(reader, "Unavailable", null);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0);
                    obj.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    obj.LineNotes = GetReaderValue_String(reader, "LineNotes", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductDutyCode = GetReaderValue_String(reader, "ProductDutyCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.InspectorName = GetReaderValue_String(reader, "InspectorName", "");
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ReceivedBy = GetReaderValue_Int32(reader, "ReceivedBy", 0);
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null);
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.PurchaseOrderLineShipInCost = GetReaderValue_Double(reader, "PurchaseOrderLineShipInCost", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForPurchaseOrderLine 
        /// Calls [usp_selectAll_GoodsInLine_for_PurchaseOrderLine]
        /// </summary>
        public override List<GoodsInLineDetails> GetListForPurchaseOrderLine(System.Int32? purchaseOrderLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_GoodsInLine_for_PurchaseOrderLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@PurchaseOrderLineId", SqlDbType.Int).Value = purchaseOrderLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0);
                    obj.ClientLandedCost = GetReaderValue_Double(reader, "ClientLandedCost", 0);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLines", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update GoodsInLine
        /// Calls [usp_update_GoodsInLine]
        /// </summary>
        public override bool Update(System.Int32? goodsInLineId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Int32? currencyNo, System.Boolean? unavailable, System.String changedFields, System.String notes, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Boolean? updateStock, System.Boolean? updateShipments, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, System.String mslLevel, System.Boolean? printHazardous, System.String previousDLUP, ref string message)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GoodsInLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@ShipInCost", SqlDbType.Float).Value = shipInCost;
                cmd.Parameters.Add("@QualityControlNotes", SqlDbType.NVarChar).Value = qualityControlNotes;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = lotNo;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@SupplierPart", SqlDbType.NVarChar).Value = supplierPart;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@CountryOfManufacture", SqlDbType.Int).Value = countryOfManufacture;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Unavailable", SqlDbType.Bit).Value = unavailable;
                cmd.Parameters.Add("@ChangedFields", SqlDbType.NVarChar).Value = changedFields;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@CountingMethodNo", SqlDbType.Int).Value = countingMethodNo;
                cmd.Parameters.Add("@SerialNosRecorded", SqlDbType.Bit).Value = serialNosRecorded;
                cmd.Parameters.Add("@PartMarkings", SqlDbType.NVarChar).Value = partMarkings;
                cmd.Parameters.Add("@UpdateStock", SqlDbType.Bit).Value = updateStock;
                cmd.Parameters.Add("@UpdateShipments", SqlDbType.Bit).Value = updateShipments;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientPrice", SqlDbType.Float).Value = clientPrice;
                cmd.Parameters.Add("@ReqSerialNo", SqlDbType.Bit).Value = reqSerialNo;
                cmd.Parameters.Add("@PrintHazardous", SqlDbType.Bit).Value = printHazardous;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@MSLLevel", SqlDbType.NVarChar).Value = mslLevel;
                //[005] start
                cmd.Parameters.Add("@PreviousDLUP", SqlDbType.VarChar, 23).Value = previousDLUP;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 200).Direction = ParameterDirection.Output;

                //[005] end
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                message = cmd.Parameters["@ErrorMessage"].Value.ToString();
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to update GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update GoodsInLine
        /// Calls [usp_update_GoodsInLine_Inspect]
        /// </summary>
        public override bool UpdateInspect(System.Int32? goodsInLineId, System.Int32? inspectedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GoodsInLine_Inspect", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@InspectedBy", SqlDbType.Int).Value = inspectedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                int ret = Convert.ToInt32(cmd.Parameters["@RowsAffected"].Value);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[001] code start
        /// <summary>
        /// GetDetailsPrintNiceLabelGoodsInLine 
        /// Calls [usp_select_GoodsInLine]
        /// </summary>
        public override GoodsInLineDetails GetDetailsPrintNiceLabelGoodsInLine(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetDetails_to_PrintNiceLabel_for_GoodsInLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.InspectedByUser = GetReaderValue_String(reader, "InspectedByUser", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CompanyName = GetReaderValue_String(reader, "Customer", "");
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPart", "");
                    obj.SalesOrderNumber = GetReaderValue_NullableInt32(reader, "SalesOrderNumber", null);
                    obj.DatePicked = GetReaderValue_DateTime(reader, "DatePicked", DateTime.MinValue);
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[001] code end
        /// <summary>
        /// usp_update_GoodsInLine_NPRStatus
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <param name="nprPrintStatus"></param>
        /// <returns></returns>
        public override bool UpdateNPRStatus(System.Int32? goodsInLineId, System.Boolean? nprPrintStatus)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GoodsInLine_NPRStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@NPRPrintStatus", SqlDbType.Int).Value = nprPrintStatus;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update NPR Print Status", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update GoodsInLine
        /// Calls [usp_update_GoodsInLine_PhysicalInspect]
        /// </summary>
        public override bool UpdatePhysicalInspect(System.Int32? goodsInLineId, System.Int32? inspectedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GoodsInLine_PhysicalInspect", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@InspectedBy", SqlDbType.Int).Value = inspectedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                int ret = Convert.ToInt32(cmd.Parameters["@RowsAffected"].Value);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override Int32 InsertSerialNo(System.String subGroup, System.String serialNo, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage, out System.Int32 totalSerial)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_SerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@serialNo", SqlDbType.VarChar).Value = serialNo;
                cmd.Parameters.Add("@subGroup", SqlDbType.VarChar).Value = subGroup;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SerialNoId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@CountStatus", SqlDbType.VarChar, 250).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@TotalSerialCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                // int ret = ExecuteNonQuery(cmd);

                validateMessage = null;
                ExecuteScalar(cmd);
                int ret = (int)(cmd.Parameters["@SerialNoId"].Value == null ? 0 : cmd.Parameters["@SerialNoId"].Value);
                validateMessage = (cmd.Parameters["@CountStatus"].Value.Equals(DBNull.Value) == true ? null : (String)cmd.Parameters["@CountStatus"].Value);
                totalSerial = (int)(cmd.Parameters["@TotalSerialCount"].Value == null ? 0 : cmd.Parameters["@TotalSerialCount"].Value);
                if (ret == -1 && validateMessage == null)
                {
                    validateMessage = "Duplicate Serial Numbers not allowed";
                    return ret;
                }

                return (Int32)cmd.Parameters["@SerialNoId"].Value;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override Int32 UpdateSerialNo(System.Int32? serialId, System.String subGroup, System.String serialNo, System.Int32? goodsInId, System.String status, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_SerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@serialId", SqlDbType.Int).Value = serialId;
                cmd.Parameters.Add("@serialNo", SqlDbType.VarChar).Value = serialNo;
                cmd.Parameters.Add("@subGroup", SqlDbType.VarChar).Value = subGroup;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@Status", SqlDbType.VarChar).Value = status;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@SerialNoId", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();
                //int ret = ExecuteNonQuery(cmd);
                //return (Int32)cmd.Parameters["@SerialNoId"].Value;
                int ret = ExecuteNonQuery(cmd);
                validateMessage = null;
                if (ret == -1)
                {
                    validateMessage = "Duplicate Serial Numbers not allowed";
                }
                return ret;// (Int32)cmd.Parameters["@SerialNoId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update SerialNo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<GoodsInLineDetails> GetDataGrid(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_SerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@GoodsInId", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNoId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.SerialNo = GetReaderValue_String(reader, "SerialNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "SubGroup", "");
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.InvoiceLineNo = GetReaderValue_Int32(reader, "InvoiceLineNo", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override Int32 InsertAllSerialNo(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_AllSerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@SerialNoId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@CountStatus", SqlDbType.VarChar, 250).Direction = ParameterDirection.Output;
                cn.Open();
                //int ret = ExecuteNonQuery(cmd);               
                //return (Int32)cmd.Parameters["@SerialNoId"].Value;
                // return ret;
                validateMessage = null;
                ExecuteScalar(cmd);
                //int ret = (int)(cmd.Parameters["@SerialNoId"].Value == null ? 0 : cmd.Parameters["@SerialNoId"].Value);
                validateMessage = (cmd.Parameters["@CountStatus"].Value.Equals(DBNull.Value) == true ? null : (String)cmd.Parameters["@CountStatus"].Value);

                return (Int32)cmd.Parameters["@SerialNoId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<GoodsInLineDetails> GetSerial(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_AllSerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInId", SqlDbType.Int).Value = goodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNumberId", 0);
                    obj.SerialNo = GetReaderValue_String(reader, "SerialNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "Group", null);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Serial Details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<GoodsInLineDetails> AutoSearch(System.String nameSearch, System.String groupName)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_SerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cmd.Parameters.Add("@Group", SqlDbType.VarChar).Value = groupName;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNumberId", 0);
                    obj.SerialNo = GetReaderValue_String(reader, "SerialNo", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<GoodsInLineDetails> DropDown(System.Int32? goodsInLineNo, System.Int32? invoiceLineNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_Group", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = goodsInLineNo;
                cmd.Parameters.Add("@InvoiceLineNo", SqlDbType.Int).Value = invoiceLineNo;
                cmd.Parameters.Add("@IsGoodsInBased", SqlDbType.Bit).Value = (invoiceLineNo ?? 0) > 0 ? false : true;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SubGroup = GetReaderValue_String(reader, "Group", "");
                    obj.SerialNo = GetReaderValue_String(reader, "RemainSerialNo", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get RohsStatuss", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<GoodsInLineDetails> AutoSearchGroup(System.String nameSearch)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_Group", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNumberId", 0);
                    obj.SubGroup = GetReaderValue_String(reader, "Group", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override Int32 DeleteSerialNo(System.Int32? serialNoId, System.String status, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_SerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SerialNoId", SqlDbType.Int).Value = serialNoId;
                cmd.Parameters.Add("@Status", SqlDbType.VarChar).Value = status;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.VarChar).Value = goodsInId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.VarChar).Value = goodsInLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                //return (Int32)cmd.Parameters["@SerialNoId"].Value;
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_GoodsInSerialNo]
        /// </summary>
        public override List<GoodsInLineDetails> GISerialSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String giSerialGroup, System.String serialNoLo, System.Int32? serialNoHi, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.Int32? goodsInLineNo, System.Int32? invoiceLineNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_GoodsInSerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@GroupNo", SqlDbType.NVarChar).Value = giSerialGroup;
                cmd.Parameters.Add("@SerialNoLo", SqlDbType.VarChar).Value = serialNoLo;
                //cmd.Parameters.Add("@SerialNoHi", SqlDbType.Int).Value = serialNoHi;
                cmd.Parameters.Add("@DateReceivedFrom", SqlDbType.DateTime).Value = dateReceivedFrom;
                cmd.Parameters.Add("@DateReceivedTo", SqlDbType.DateTime).Value = dateReceivedTo;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = goodsInLineNo;
                cmd.Parameters.Add("@InvoiceLineNo", SqlDbType.Int).Value = invoiceLineNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNumberId", 0);
                    obj.SerialNo = GetReaderValue_String(reader, "SerialNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "Group", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsIn serial number", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// usp_get_AttachedSerialNo
        /// </summary>
        public override List<GoodsInLineDetails> GetAttachedSerial(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInLineNo, System.Int32? salesOrderLineNo, System.DateTime? dateReceivedFrom, System.DateTime? dateReceivedTo, System.Int32? allocationNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_AttachedSerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = goodsInLineNo;
                cmd.Parameters.Add("@SalesOrderLineNo", SqlDbType.Int).Value = salesOrderLineNo;
                cmd.Parameters.Add("@DateReceivedFrom", SqlDbType.DateTime).Value = dateReceivedFrom;
                cmd.Parameters.Add("@DateReceivedTo", SqlDbType.DateTime).Value = dateReceivedTo;
                cmd.Parameters.Add("@AllocationNo", SqlDbType.Int).Value = allocationNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNumberId", 0);
                    obj.SerialNo = GetReaderValue_String(reader, "SerialNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "Group", "");
                    obj.InvoiceLineNo = GetReaderValue_Int32(reader, "InvoiceLineNo", 0);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsIn serial number", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// usp_delete_AttachedSerial
        /// </summary>
        public override Int32 DeleteAttachedSerial(System.Int32? serialId, System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? updatedBy, System.Int32? allocationNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_AttachedSerial", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SerialNoId", SqlDbType.Int).Value = serialId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.VarChar).Value = goodsInLineId;
                cmd.Parameters.Add("@SOLineNo", SqlDbType.VarChar).Value = soLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@AllocationNo", SqlDbType.Int).Value = allocationNo;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                //return (Int32)cmd.Parameters["@SerialNoId"].Value;
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// usp_update_SerialBySO
        /// </summary>
        public override Int32 UpdateSerialBySO(System.String subGroup, System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? qtyToShpped, System.Int32? allocatedId, out System.String validateMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_SerialBySO", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SubGroup", SqlDbType.VarChar).Value = subGroup;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@SOLineNo", SqlDbType.Int).Value = soLineId;
                cmd.Parameters.Add("@QtyToShpped", SqlDbType.Int).Value = qtyToShpped;
                cmd.Parameters.Add("@AllocatedId", SqlDbType.Int).Value = allocatedId;
                cn.Open();
                // int ret = ExecuteNonQuery(cmd);
                //return (Int32)cmd.Parameters["@SerialNoId"].Value;
                // return ret;

                int ret = ExecuteNonQuery(cmd);
                validateMessage = null;
                if (ret == -1)
                {
                    validateMessage = "Cannot insert Serial Numbers beyond Quantity limit";
                }
                if (ret == 0)
                {
                    validateMessage = "All the serial number are recorded for this group";
                }
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// usp_delete_SerialBySO
        /// </summary>
        public override Int32 DeleteSerialBySO(System.Int32? goodsInLineId, System.Int32? soLineId, System.Int32? AllocatedId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_SerialBySO", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@SOLineNo", SqlDbType.Int).Value = soLineId;
                cmd.Parameters.Add("@AllocationNo", SqlDbType.Int).Value = AllocatedId;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to delete Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }



        public override List<GoodsInLineDetails> GetReasonDetailByPart(System.String part)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_ReasonDetailByPart", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.ReasonCode = GetReaderValue_String(reader, "ReasonCode", "");
                    obj.ReasonDate = Convert.ToString(GetReaderValue_DateTime(reader, "ReasonDate", DateTime.MinValue));
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Reason Code Detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// USP_SELECT_ShipInCostHistory
        /// </summary>
        /// <param name="goodsInLineNo"></param>
        /// <returns></returns>
        public override List<GoodsInLineDetails> GetShipCostHistory(System.Int32? goodsInLineNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("USP_SELECT_ShipInCostHistory", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = goodsInLineNo;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GIShipCostHistoryId = GetReaderValue_Int32(reader, "GIShipCostHistoryId", 0);
                    obj.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "EmployeeName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ship cost history", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }

        }

        /// <summary>
        /// ItemSearch 
        /// Calls [usp_itemsearch_GoodsInTempSerialNo]
        /// </summary>
        public override List<GoodsInLineDetails> GITempSerialSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInNo, System.Int32? goodsInLineNo, System.Int32? loginNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_GoodsInTempSerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInNo;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = goodsInLineNo;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNoId", 0);
                    obj.SerialNo = GetReaderValue_String(reader, "SerialNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "SubGroup", "");
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.InvoiceLineNo = GetReaderValue_Int32(reader, "InvoiceLineNo", 0);
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsIn serial number", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<GoodsInLineDetails> GetAttachedSerial(System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? customerRMANo, System.Int32? customerRMALineNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_AttachedSerialNoBasedOnCRMA", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@CustomerRMANo", SqlDbType.Int).Value = customerRMANo;
                cmd.Parameters.Add("@CustomerRMALineNo", SqlDbType.Int).Value = customerRMALineNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialNoId = GetReaderValue_Int32(reader, "SerialNumberId", 0);
                    obj.SerialNo = GetReaderValue_String(reader, "SerialNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "Group", "");
                    obj.InvoiceLineNo = GetReaderValue_Int32(reader, "InvoiceLineNo", 0);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsIn serial number", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 DeleteAttachedSerial(System.Int32? serialId, System.Int32? customerRMANo, System.Int32? customerRMALineNo, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_AttachedSerialCRMA", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SerialNoId", SqlDbType.Int).Value = serialId;
                cmd.Parameters.Add("@CustomerRMANo", SqlDbType.VarChar).Value = customerRMANo;
                cmd.Parameters.Add("@CustomerRMALineNo", SqlDbType.VarChar).Value = customerRMALineNo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;


                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 DeattachCRMASerial(System.Int32? invoiceLineNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_deattachCRMASerial", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@InvoiceLineNo", SqlDbType.Int).Value = invoiceLineNo;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 AttachSerialByCRMA(System.String subGroup, System.Int32? invoiceLineNo, System.Int32? customerRMANo, System.Int32? customerRMALineNo, out System.String validateMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_attachSerialByCRMA", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SubGroup", SqlDbType.VarChar).Value = subGroup;
                cmd.Parameters.Add("@InvoiceLineNo", SqlDbType.Int).Value = invoiceLineNo;
                cmd.Parameters.Add("@CustomerRMANo", SqlDbType.Int).Value = customerRMANo;
                cmd.Parameters.Add("@CustomerRMALineNo", SqlDbType.Int).Value = customerRMALineNo;
                cn.Open();

                int ret = ExecuteNonQuery(cmd);
                validateMessage = null;
                if (ret == -1)
                {
                    validateMessage = "Cannot insert Serial Numbers beyond Quantity limit";
                }
                if (ret == 0)
                {
                    validateMessage = "All the serial number are recorded for this group";
                }
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Serial No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Split GoodsInLine
        /// Calls [usp_split_GoodsInLine]
        /// </summary>
        public override bool SplitGI(System.Int32? goodsInLineId, System.String part, System.Int32? manufacturerNo, System.String dateCode, System.Int32? packageNo, System.Int32? quantity, System.Double? price, System.Double? shipInCost, System.String qualityControlNotes, System.String location, System.Int32? lotNo, System.Int32? productNo, System.String supplierPart, System.Byte? rohs, System.Int32? countryOfManufacture, System.Int32? currencyNo, System.Boolean? unavailable, System.String changedFields, System.String notes, System.Int32? countingMethodNo, System.Boolean? serialNosRecorded, System.String partMarkings, System.Boolean? updateStock, System.Boolean? updateShipments, System.Int32? updatedBy, System.Double? clientPrice, System.Boolean? reqSerialNo, System.String mslLevel, System.Boolean? printHazardous, System.String previousDLUP, System.String SerialNo, ref string message)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_split_GoodsInLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@Part", SqlDbType.NVarChar).Value = part;
                cmd.Parameters.Add("@ManufacturerNo", SqlDbType.Int).Value = manufacturerNo;
                cmd.Parameters.Add("@DateCode", SqlDbType.NVarChar).Value = dateCode;
                cmd.Parameters.Add("@PackageNo", SqlDbType.Int).Value = packageNo;
                cmd.Parameters.Add("@Quantity", SqlDbType.Int).Value = quantity;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = price;
                cmd.Parameters.Add("@ShipInCost", SqlDbType.Float).Value = shipInCost;
                cmd.Parameters.Add("@QualityControlNotes", SqlDbType.NVarChar).Value = qualityControlNotes;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = location;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = lotNo;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = productNo;
                cmd.Parameters.Add("@SupplierPart", SqlDbType.NVarChar).Value = supplierPart;
                cmd.Parameters.Add("@ROHS", SqlDbType.TinyInt).Value = rohs;
                cmd.Parameters.Add("@CountryOfManufacture", SqlDbType.Int).Value = countryOfManufacture;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Unavailable", SqlDbType.Bit).Value = unavailable;
                cmd.Parameters.Add("@ChangedFields", SqlDbType.NVarChar).Value = changedFields;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@CountingMethodNo", SqlDbType.Int).Value = countingMethodNo;
                cmd.Parameters.Add("@SerialNosRecorded", SqlDbType.Bit).Value = serialNosRecorded;
                cmd.Parameters.Add("@PartMarkings", SqlDbType.NVarChar).Value = partMarkings;
                cmd.Parameters.Add("@UpdateStock", SqlDbType.Bit).Value = updateStock;
                cmd.Parameters.Add("@UpdateShipments", SqlDbType.Bit).Value = updateShipments;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ClientPrice", SqlDbType.Float).Value = clientPrice;
                cmd.Parameters.Add("@ReqSerialNo", SqlDbType.Bit).Value = reqSerialNo;
                cmd.Parameters.Add("@PrintHazardous", SqlDbType.Bit).Value = printHazardous;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@MSLLevel", SqlDbType.NVarChar).Value = mslLevel;
                cmd.Parameters.Add("@SerialNo", SqlDbType.NVarChar).Value = SerialNo;
                //[005] start
                cmd.Parameters.Add("@PreviousDLUP", SqlDbType.VarChar, 23).Value = previousDLUP;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 200).Direction = ParameterDirection.Output;

                //[005] end
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                message = cmd.Parameters["@ErrorMessage"].Value.ToString();
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// HICStatus GoodsInLine
        /// Calls [usp_dropdown_HICStatus]
        /// </summary>
        public override List<GoodsInLineDetails> GetHICStatus()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_HICStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@IsQueryHICStatus", SqlDbType.Bit).Value = 0;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.HICId = GetReaderValue_Int32(reader, "HICId", 0);
                    obj.HICStatus = GetReaderValue_String(reader, "HICStatus", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get HICStatus", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// HICStatus GoodsInLine
        /// Calls [usp_dropdown_HICStatus]
        /// </summary>
        public override List<GoodsInLineDetails> GetQueryHICStatus()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_HICStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@IsQueryHICStatus", SqlDbType.Bit).Value = 1;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.QueryHICId = GetReaderValue_Int32(reader, "HICId", 0);
                    obj.QueryHICStatus = GetReaderValue_String(reader, "HICStatus", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get QueryHICStatus", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// calls[usp_update_GoodsInLineEdit]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="actShipInCost"></param>
        /// <param name="qCNotes"></param>
        /// <param name="location"></param>
        /// <param name="lotNo"></param>
        /// <param name="updateStock"></param>
        /// <param name="updateShipments"></param>
        /// <param name="loginID"></param>
        /// <param name="isFullQuantityReceived"></param>
        /// <param name="quantityReceived"></param>
        /// <param name="isPartNumberCorrect"></param>
        /// <param name="correctPartNo"></param>
        /// <param name="isManufacturerCorrect"></param>
        /// <param name="correctManufacturerNo"></param>
        /// <param name="isDateCodeCorrect"></param>
        /// <param name="correctDateCode"></param>
        /// <param name="isPackageCorrect"></param>
        /// <param name="correctPackageNo"></param>
        /// <param name="isMSLCorrect"></param>
        /// <param name="correctMslNo"></param>
        /// <param name="isHICCorrect"></param>
        /// <param name="correctHIC"></param>
        /// <param name="isRohsStatusCorrect"></param>
        /// <param name="correctStatusNo"></param>
        /// <param name="countryOfManufacture"></param>
        /// <param name="countingMethodNo"></param>
        /// <param name="isSerialNosRecorded"></param>
        /// <param name="isLotCodeReq"></param>
        /// <param name="isEnhancedInpection"></param>
        /// <param name="generalInspectionNotes"></param>
        /// <param name="isBakingYes"></param>
        /// <param name="isBakingNo"></param>
        /// <param name="isBakingNA"></param>
        /// <param name="isInspectionConducted"></param>
        /// <param name="supplierPart"></param>
        /// <param name="productNo"></param>
        /// <param name="price"></param>
        /// <param name="clientPrice"></param>
        /// <param name="unavailable"></param>
        /// <param name="changedFields"></param>
        /// <param name="currencyNo"></param>
        /// <param name="reqSerailNo"></param>
        /// <param name="partMarkings"></param>
        /// <param name="lineNotes"></param>
        /// <param name="printHazWar"></param>
        /// <param name="previousDLUP"></param>
        /// <param name="message"></param>
        /// <returns></returns>
        //[012] added two parameters [System.Int16? HasBarCode, System.String Barcode] in UpdateGILineEdit
        public override bool UpdateGILineEdit(System.Int32? ID, System.Double? actShipInCost, System.String QCNotes, System.String Location, System.Int32? LotNo, System.Boolean? blnUpdateStock, System.Boolean? blnUpdateShipments, System.Int32? loginID, System.Boolean? IsFullQuantityReceived, System.Int32? QuantityReceived, System.Boolean? IsPartNumberCorrect, System.String CorrectPartNo, System.Boolean? IsManufacturerCorrect, System.Int32? CorrectManufacturerNo, System.Boolean? IsDateCodeCorrect, System.String CorrectDateCode, System.Boolean? IsPackageCorrect, System.Int32? CorrectPackageNo, System.Boolean? IsMSLCorrect, System.String CorrectMslNo, System.Boolean? IsHICCorrect, System.String CorrectHIC, System.Boolean? IsRohsStatusCorrect, System.Int32? CorrectStatusNo, System.Int32? CountryOfManufacture, System.Int32? CountingMethodNo, System.Boolean? IsSerialNosRecorded, System.Boolean? IsLotCodeReq, System.Boolean? IsEnhancedInpection, System.String GeneralInspectionNotes, System.Boolean? IsBakingYes, System.Boolean? IsBakingNo, System.Boolean? IsBakingNA, System.Boolean? IsInspectionConducted, System.String SupplierPart, System.Int32? ProductNo, System.Double Price, System.Double? ClientPrice, System.Boolean? Unavailable, System.String ChangedFields, System.Int32? CurrencyNo, System.Boolean? ReqSerailNo, System.String PartMarkings, System.String LineNotes, System.Boolean? PrintHazWar, System.String PreviousDLUP, System.Double shipInCost, System.Int32? quantity, ref System.String message, System.Boolean? IsDateCodeRequired, System.String PackageBreakdownInfo, System.Int32? HICStatus, System.String PackBreakDownJSON, System.Boolean? IsBySendQueryBtn, System.Int32? ActeoneTestStatus, System.Int32? IsopropryleStatus, System.String ActeoneTest, System.String Isopropryle, System.String QueryBakingLevel, System.Int32? EnhInpectionReqId, System.String PrintDateCode, System.Boolean? IsPackageBreakdownChnaged,
            System.Int32? HasBarCode, System.String BarcodeRemarks, System.String PartNoQuery, System.String ManufacturerQuery, System.String PackagingTypeQuery, System.String MslQuery, System.String RohsQuery, System.Boolean? ReaiseGeneralQuery)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GoodsInLineEdit", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 180;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = ID;
                cmd.Parameters.Add("@ActShipInCost", SqlDbType.Decimal).Value = actShipInCost;
                cmd.Parameters.Add("@QualityControlNotes", SqlDbType.NVarChar).Value = QCNotes;
                cmd.Parameters.Add("@Location", SqlDbType.NVarChar).Value = Location;
                cmd.Parameters.Add("@LotNo", SqlDbType.Int).Value = LotNo;
                cmd.Parameters.Add("@UpdateStock", SqlDbType.Bit).Value = blnUpdateStock;
                cmd.Parameters.Add("@UpdateShipments", SqlDbType.Bit).Value = blnUpdateShipments;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = loginID;
                cmd.Parameters.Add("@IsFullQuantityReceived", SqlDbType.Bit).Value = IsFullQuantityReceived;
                cmd.Parameters.Add("@quantityReceived", SqlDbType.Int).Value = QuantityReceived;
                cmd.Parameters.Add("@IsPartNumberCorrect", SqlDbType.Bit).Value = IsPartNumberCorrect;
                cmd.Parameters.Add("@CorrectPartNo", SqlDbType.NVarChar).Value = CorrectPartNo;
                cmd.Parameters.Add("@IsManufacturerCorrect", SqlDbType.Bit).Value = IsManufacturerCorrect;
                cmd.Parameters.Add("@CorrectManufacturerNo", SqlDbType.Int).Value = CorrectManufacturerNo;
                cmd.Parameters.Add("@IsDateCodeCorrect", SqlDbType.Bit).Value = IsDateCodeCorrect;
                cmd.Parameters.Add("@CorrectDateCode", SqlDbType.NVarChar).Value = CorrectDateCode;
                cmd.Parameters.Add("@IsPackageCorrect", SqlDbType.Bit).Value = IsPackageCorrect;
                cmd.Parameters.Add("@CorrectPackageNo", SqlDbType.Int).Value = CorrectPackageNo;
                cmd.Parameters.Add("@IsMSLCorrect", SqlDbType.Bit).Value = IsMSLCorrect;
                cmd.Parameters.Add("@CorrectMslNo", SqlDbType.NVarChar).Value = CorrectMslNo;
                cmd.Parameters.Add("@IsHICCorrect", SqlDbType.Bit).Value = IsHICCorrect;
                cmd.Parameters.Add("@CorrectHIC", SqlDbType.NVarChar).Value = CorrectHIC;
                cmd.Parameters.Add("@IsRohsStatusCorrect", SqlDbType.Bit).Value = IsRohsStatusCorrect;
                cmd.Parameters.Add("@CorrectStatusNo", SqlDbType.Int).Value = CorrectStatusNo;
                cmd.Parameters.Add("@CountryOfManufacture", SqlDbType.Int).Value = CountryOfManufacture;
                cmd.Parameters.Add("@CountingMethodNo", SqlDbType.Int).Value = CountingMethodNo;
                cmd.Parameters.Add("@IsSerialNosRecorded", SqlDbType.Bit).Value = IsSerialNosRecorded;
                cmd.Parameters.Add("@IsLotCodeReq", SqlDbType.Bit).Value = IsLotCodeReq;
                cmd.Parameters.Add("@IsEnhancedInpection", SqlDbType.Bit).Value = IsEnhancedInpection;
                cmd.Parameters.Add("@GeneralInspectionNotes", SqlDbType.NVarChar).Value = GeneralInspectionNotes;
                cmd.Parameters.Add("@IsBakingYes", SqlDbType.Bit).Value = IsBakingYes;
                cmd.Parameters.Add("@IsBakingNo", SqlDbType.Bit).Value = IsBakingNo;
                cmd.Parameters.Add("@IsBakingNA", SqlDbType.Bit).Value = IsBakingNA;
                cmd.Parameters.Add("@IsInspectionConducted", SqlDbType.Bit).Value = IsInspectionConducted;
                cmd.Parameters.Add("@SupplierPart", SqlDbType.NVarChar).Value = SupplierPart;
                cmd.Parameters.Add("@ProductNo", SqlDbType.Int).Value = ProductNo;
                cmd.Parameters.Add("@Price", SqlDbType.Float).Value = Price;
                cmd.Parameters.Add("@ClientPrice", SqlDbType.Float).Value = ClientPrice;
                cmd.Parameters.Add("@Unavailable", SqlDbType.Bit).Value = Unavailable;
                cmd.Parameters.Add("@ChangedFields", SqlDbType.NVarChar).Value = ChangedFields;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = CurrencyNo;
                cmd.Parameters.Add("@ReqSerailNo", SqlDbType.Bit).Value = ReqSerailNo;
                cmd.Parameters.Add("@PartMarkings", SqlDbType.NVarChar).Value = PartMarkings;
                cmd.Parameters.Add("@LineNotes", SqlDbType.NVarChar).Value = LineNotes;
                cmd.Parameters.Add("@PrintHazWar", SqlDbType.Bit).Value = PrintHazWar;
                cmd.Parameters.Add("@PreviousDLUP", SqlDbType.NVarChar, 23).Value = PreviousDLUP;
                cmd.Parameters.Add("@ShipInCost", SqlDbType.Float).Value = shipInCost;
                cmd.Parameters.Add("@Quantity", SqlDbType.Float).Value = quantity;

                cmd.Parameters.Add("@IsDateCodeRequired", SqlDbType.Bit).Value = IsDateCodeRequired;
                cmd.Parameters.Add("@PackageBreakdownInfo", SqlDbType.NVarChar).Value = PackageBreakdownInfo;
                cmd.Parameters.Add("@HICStatus", SqlDbType.Int).Value = HICStatus;
                cmd.Parameters.Add("@PackBreakDownJSON", SqlDbType.NVarChar).Value = PackBreakDownJSON;
                cmd.Parameters.Add("@IsBySendQueryBtn", SqlDbType.Bit).Value = IsBySendQueryBtn;
                cmd.Parameters.Add("@ActeoneTestStatus", SqlDbType.Int).Value = ActeoneTestStatus;
                cmd.Parameters.Add("@IsopropryleStatus", SqlDbType.Int).Value = IsopropryleStatus;
                cmd.Parameters.Add("@ActeoneTest", SqlDbType.VarChar, 500).Value = ActeoneTest;
                cmd.Parameters.Add("@Isopropryle", SqlDbType.VarChar, 500).Value = Isopropryle;
                cmd.Parameters.Add("@QueryBakingLevel", SqlDbType.VarChar, 500).Value = QueryBakingLevel;
                cmd.Parameters.Add("@EnhInpectionReqId", SqlDbType.Int).Value = EnhInpectionReqId;
                cmd.Parameters.Add("@PrintDateCode", SqlDbType.VarChar).Value = PrintDateCode;
                cmd.Parameters.Add("@IsPackageBreakdownChnaged", SqlDbType.Bit).Value = IsPackageBreakdownChnaged;
                //[012] start
                cmd.Parameters.Add("@HasBarcodeScan", SqlDbType.Int).Value = HasBarCode;
                cmd.Parameters.Add("@BarcodeScanRemarks", SqlDbType.NVarChar, 1000).Value = BarcodeRemarks;
                //[012] end

                cmd.Parameters.Add("@PartNoQuery", SqlDbType.NVarChar, 300).Value = PartNoQuery;
                cmd.Parameters.Add("@ManufacturerQuery", SqlDbType.NVarChar, 300).Value = ManufacturerQuery;
                cmd.Parameters.Add("@PackagingTypeQuery", SqlDbType.NVarChar, 300).Value = PackagingTypeQuery;
                cmd.Parameters.Add("@MslQuery", SqlDbType.NVarChar, 300).Value = MslQuery;
                cmd.Parameters.Add("@RohsQuery", SqlDbType.NVarChar, 300).Value = RohsQuery;
                cmd.Parameters.Add("@ReaiseGeneralQuery", SqlDbType.Bit).Value = ReaiseGeneralQuery;

                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                //[005] start
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 200).Direction = ParameterDirection.Output;

                //[005] end

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                message = cmd.Parameters["@ErrorMessage"].Value.ToString();
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to Update GILine Edit (UpdateGILineEdit)", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// calls [usp_select_PackagingBreakdown]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public override List<PackagingBreakdown> GetPackagingBreakdownList(System.Int32 goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_PackagingBreakdown", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PackagingBreakdown> lst = new List<PackagingBreakdown>();
                while (reader.Read())
                {
                    PackagingBreakdown obj = new PackagingBreakdown();
                    obj.FactorySealed = GetReaderValue_NullableBoolean(reader, "FactorySealed", false);
                    obj.NumberOfPacks = GetReaderValue_NullableInt32(reader, "NumberofPacks", null);
                    obj.PackSize = GetReaderValue_NullableDouble(reader, "PackSize", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.BatchCode = GetReaderValue_String(reader, "BatchCode", "");
                    obj.TotalPackSize = GetReaderValue_NullableDouble(reader, "Total", null);
                    obj.PackagingTypeId = GetReaderValue_NullableInt32(reader, "PackagingTypeId", null);
                    obj.MFRLabelId = GetReaderValue_NullableInt32(reader, "MFRLabelId", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get Packaging Break Down List ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }

        }
        public override List<DateCode> GetDateCodeList(System.Int32 goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_DateCodeRequired", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<DateCode> lst = new List<DateCode>();
                while (reader.Read())
                {
                    DateCode obj = new DateCode();
                    obj.ID = GetReaderValue_Int32(reader, "Id", 0);
                    obj.DateCodes = GetReaderValue_String(reader, "DateCodes", "");
                    obj.Quantity = GetReaderValue_NullableInt32(reader, "Quantity", null);
                    obj.QuantityReceived = GetReaderValue_NullableInt32(reader, "QuantityReceived", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get Date Code List ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }

        }
        /// <summary>
        /// Calls[usp_select_GoodsInVariance_Query]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public override GoodsInLineDetails GetGIQueryData(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GoodsInVariance_Query", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@GIQueryData", SqlDbType.NVarChar, -1).Direction = ParameterDirection.Output;
                cn.Open();
                ExecuteNonQuery(cmd);
                //DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                //if (reader.Read())
                //{
                //    GoodsInLineDetails obj = new GoodsInLineDetails();
                //    obj.GIQuery = GetReaderValue_String(reader, "GIQuery", "");
                //    return obj;
                //}
                //else
                //{
                //    return null;
                //}
                GoodsInLineDetails obj = new GoodsInLineDetails();
                obj.GIQuery = cmd.Parameters["@GIQueryData"].Value.ToString();
                return obj;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetGIQueryData", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_select_GIUpdatedLine]
        /// </summary>
        /// <param name="goodsInLineId"></param>
        /// <returns></returns>
        public override GoodsInLineDetails GetGILineData(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GIUpdatedLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetGoodsInLineFromReader(reader);
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.PurchaseOrderLineNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderLineNo", null);
                    obj.FullPart = GetReaderValue_String(reader, "FullPart", "");
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ManufacturerNo = GetReaderValue_NullableInt32(reader, "ManufacturerNo", null);
                    obj.DateCode = GetReaderValue_String(reader, "DateCode", "");
                    obj.PackageNo = GetReaderValue_NullableInt32(reader, "PackageNo", null);
                    obj.Quantity = GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.Price = GetReaderValue_Double(reader, "Price", 0);
                    obj.ShipInCost = GetReaderValue_NullableDouble(reader, "ShipInCost", null);
                    obj.QualityControlNotes = GetReaderValue_String(reader, "QualityControlNotes", "");
                    obj.Location = GetReaderValue_String(reader, "Location", "");
                    obj.ProductNo = GetReaderValue_NullableInt32(reader, "ProductNo", null);
                    obj.LandedCost = GetReaderValue_Double(reader, "LandedCost", 0);
                    obj.CustomerRMALineNo = GetReaderValue_NullableInt32(reader, "CustomerRMALineNo", null);
                    obj.SupplierPart = GetReaderValue_String(reader, "SupplierPart", "");
                    obj.ROHS = GetReaderValue_NullableByte(reader, "ROHS", null);
                    obj.CountryOfManufacture = GetReaderValue_NullableInt32(reader, "CountryOfManufacture", null);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    //[005] start
                    obj.StringDLUP = reader.GetDateTime(reader.GetOrdinal("DLUP")).ToString("yyyy-MM-dd HH:mm:ss.fff");
                    //[005] end
                    obj.InspectedBy = GetReaderValue_NullableInt32(reader, "InspectedBy", null);
                    obj.DateInspected = GetReaderValue_NullableDateTime(reader, "DateInspected", null);
                    obj.CountingMethodNo = GetReaderValue_NullableInt32(reader, "CountingMethodNo", null);
                    obj.SerialNosRecorded = GetReaderValue_NullableBoolean(reader, "SerialNosRecorded", null);
                    obj.Unavailable = GetReaderValue_NullableBoolean(reader, "Unavailable", null);
                    obj.LotNo = GetReaderValue_NullableInt32(reader, "LotNo", null);
                    obj.PartMarkings = GetReaderValue_String(reader, "PartMarkings", "");
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.ManufacturerCode = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.ReceiverName = GetReaderValue_String(reader, "ReceiverName", "");
                    obj.PurchaseOrderNo = GetReaderValue_NullableInt32(reader, "PurchaseOrderNo", null);
                    obj.PurchaseOrderNumber = GetReaderValue_NullableInt32(reader, "PurchaseOrderNumber", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.AirWayBill = GetReaderValue_String(reader, "AirWayBill", "");
                    obj.QuantityOrdered = GetReaderValue_Int32(reader, "QuantityOrdered", 0);
                    obj.SupplierInvoice = GetReaderValue_String(reader, "SupplierInvoice", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyNo", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CountingMethodDescription = GetReaderValue_String(reader, "CountingMethodDescription", "");
                    obj.LineNotes = GetReaderValue_String(reader, "LineNotes", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.PackageName = GetReaderValue_String(reader, "PackageName", "");
                    obj.PackageDescription = GetReaderValue_String(reader, "PackageDescription", "");
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.ProductDutyCode = GetReaderValue_String(reader, "ProductDutyCode", "");
                    obj.ManufacturerName = GetReaderValue_String(reader, "ManufacturerName", "");
                    obj.CountryOfManufactureName = GetReaderValue_String(reader, "CountryOfManufactureName", "");
                    obj.InspectorName = GetReaderValue_String(reader, "InspectorName", "");
                    obj.CustomerRMANo = GetReaderValue_NullableInt32(reader, "CustomerRMANo", null);
                    obj.CustomerRMANumber = GetReaderValue_NullableInt32(reader, "CustomerRMANumber", null);
                    obj.CurrencyDescription = GetReaderValue_String(reader, "CurrencyDescription", "");
                    obj.ReceivedBy = GetReaderValue_Int32(reader, "ReceivedBy", 0);
                    obj.DivisionNo = GetReaderValue_NullableInt32(reader, "DivisionNo", null);
                    obj.TeamNo = GetReaderValue_NullableInt32(reader, "TeamNo", null);
                    obj.StockNo = GetReaderValue_NullableInt32(reader, "StockNo", null);
                    obj.Reference = GetReaderValue_String(reader, "Reference", "");
                    obj.LotName = GetReaderValue_String(reader, "LotName", "");
                    obj.PurchaseOrderLineShipInCost = GetReaderValue_Double(reader, "PurchaseOrderLineShipInCost", 0);
                    obj.ChangedFields = GetReaderValue_String(reader, "ChangedFields", "");
                    obj.UpdateStock = GetReaderValue_NullableBoolean(reader, "UpdateStock", null);
                    obj.UpdateShipments = GetReaderValue_NullableBoolean(reader, "UpdateShipments", null);
                    obj.HasAllocationOutward = GetReaderValue_NullableBoolean(reader, "HasAllocationOutward", false);
                    obj.SalesOrderNumber = GetReaderValue_NullableInt32(reader, "SalesOrderNo", null);
                    obj.CustomerPart = GetReaderValue_String(reader, "CustomerPartNo", string.Empty);
                    obj.NPRPrinted = GetReaderValue_NullableBoolean(reader, "NPRPrinted", false);
                    //[001] code start
                    obj.InspectorNameLabel = GetReaderValue_String(reader, "InspectorNameLabel", string.Empty);
                    //[001] code end
                    //[002] code start
                    obj.NPRIds = GetReaderValue_String(reader, "NPRIds", string.Empty);
                    obj.NPRNos = GetReaderValue_String(reader, "NPRNos", string.Empty);
                    //[002] code end
                    obj.HasStocksplit = GetReaderValue_Boolean(reader, "Stocksplit", false);
                    obj.HasSupplierInvoiceExists = GetReaderValue_Boolean(reader, "SupplierInvoiceExists", false);
                    obj.blnStockProvision = GetReaderValue_Boolean(reader, "blnStockProvision", false);
                    obj.LotCode = GetReaderValue_String(reader, "LotCode", "");

                    obj.InternalPurchaseOrderId = GetReaderValue_Int32(reader, "InternalPurchaseOrderNo", 0);
                    obj.ClientLandedCost = GetReaderValue_Double(reader, "ClientLandedCost", 0);
                    obj.ClientPrice = GetReaderValue_Double(reader, "ClientPrice", 0);

                    obj.ClientCurrencyNo = GetReaderValue_NullableInt32(reader, "ClientCurrencyNo", 0);
                    obj.ClientCurrencyCode = GetReaderValue_String(reader, "ClientCurrencyCode", "");
                    obj.IPOClientNo = GetReaderValue_NullableInt32(reader, "IPOClientNo", null);
                    obj.POBankFee = GetReaderValue_NullableDouble(reader, "POBankFee", null);
                    obj.CustomerPO = GetReaderValue_String(reader, "CustomerPO", "");
                    obj.StockDate = GetReaderValue_DateTime(reader, "StockDate", DateTime.MinValue);
                    obj.ProductInactive = GetReaderValue_NullableBoolean(reader, "ProductInactive", false);
                    obj.DutyRate = GetReaderValue_NullableDouble(reader, "ProductDutyRate", null);
                    obj.ReqSerialNo = GetReaderValue_NullableBoolean(reader, "ReqSerialNo", false);
                    obj.SerialNoCount = GetReaderValue_NullableInt32(reader, "SerialNoCount", 0);
                    obj.MSLLevel = GetReaderValue_String(reader, "MSLLevel", "");
                    obj.IsProdHazardous = GetReaderValue_NullableBoolean(reader, "IsProdHazardous", false);
                    obj.PrintHazardous = GetReaderValue_NullableBoolean(reader, "PrintHazardous", false);
                    obj.ParentGoodsInLineId = GetReaderValue_Int32(reader, "ParentGILineNo", 0);
                    obj.TotalShipCost = GetReaderValue_NullableDouble(reader, "TotalShipInCost", 0);
                    //code start for ihs
                    obj.CountryOfOrigin = GetReaderValue_String(reader, "IHSCountryOfOrigin", "");
                    obj.LifeCycleStage = GetReaderValue_String(reader, "LifeCycleStage", "");
                    obj.HTSCode = GetReaderValue_String(reader, "HTSCode", "");
                    obj.AveragePrice = GetReaderValue_Double(reader, "AveragePrice", 0);
                    obj.Packaging = GetReaderValue_String(reader, "Packing", "");
                    obj.PackagingSize = GetReaderValue_String(reader, "PackagingSize", "");
                    obj.Descriptions = GetReaderValue_String(reader, "Descriptions", "");
                    obj.IHSProduct = GetReaderValue_String(reader, "IHSProduct", "");
                    obj.ECCNCode = GetReaderValue_String(reader, "ECCNCode", "");

                    obj.IsFullQtyRecieved = GetReaderValue_NullableBoolean(reader, "IsFullQtyRecieved", null);
                    obj.IsPartNoCorrect = GetReaderValue_NullableBoolean(reader, "IsPartNoCorrect", null);
                    obj.CorrectPartNo = GetReaderValue_String(reader, "CorrectPartNo", "");
                    obj.IsManufacturerCorrect = GetReaderValue_NullableBoolean(reader, "IsManufacturerCorrect", null);
                    obj.CorrectManufacturer = GetReaderValue_NullableInt32(reader, "CorrectManufacturer", null);
                    obj.CorrectManufacturerName = GetReaderValue_String(reader, "CorrectManufacturerName", "");
                    obj.IsDateCodeCorrect = GetReaderValue_NullableBoolean(reader, "IsDateCodeCorrect", null);
                    obj.CorrectDateCode = GetReaderValue_String(reader, "CorrectDateCode", "");
                    obj.IsDateCodeRequired = GetReaderValue_NullableBoolean(reader, "IsDateCodeRequired", null);
                    obj.IsPackageTypeCorrect = GetReaderValue_NullableBoolean(reader, "IsPackageTypeCorrect", null);
                    obj.CorrectPackageType = GetReaderValue_NullableInt32(reader, "CorrectPackageType", null);
                    obj.CorrectPackageName = GetReaderValue_String(reader, "CorrectPackageName", "");
                    obj.IsMSLLevelCorrect = GetReaderValue_NullableBoolean(reader, "IsMSLLevelCorrect", null);
                    obj.CorrectMSLLevel = GetReaderValue_String(reader, "CorrectMSLLevel", "");
                    obj.HIC_Status = GetReaderValue_NullableInt32(reader, "HICStatus", null);
                    obj.IsHICStatusCorrect = GetReaderValue_NullableBoolean(reader, "IsHICStatusCorrect", null);
                    obj.CorrectHICStatus = GetReaderValue_String(reader, "CorrectHICStatus", "");
                    obj.PKGBreakdownMismatch = GetReaderValue_String(reader, "PKGBreakdownMismatch", "");
                    obj.IsROHSStatusCorrect = GetReaderValue_NullableBoolean(reader, "IsROHSStatusCorrect", null);
                    obj.CorrectROHSStatus = GetReaderValue_NullableInt32(reader, "CorrectROHSStatus", null);
                    obj.IsLotCodesReq = GetReaderValue_NullableBoolean(reader, "IsLotCodesReq", null);
                    obj.BakingLevelAdded = GetReaderValue_NullableInt32(reader, "BakingLevelAdded", null);
                    obj.EnhancedInspectionReq = GetReaderValue_NullableBoolean(reader, "EnhancedInspectionReq", null);
                    obj.GeneralInspectionNotes = GetReaderValue_String(reader, "GeneralInspectionNotes", "");
                    obj.IsInspectionConducted = GetReaderValue_NullableBoolean(reader, "IsInspectionConducted", null);
                    obj.CompanyType = GetReaderValue_String(reader, "CompanyType", "");
                    obj.IsSalesNotify = GetReaderValue_Boolean(reader, "IsSalesNotify", false);
                    obj.IsQualityNotify = GetReaderValue_Boolean(reader, "IsQualityNotify", false);
                    obj.IsPurchaseNotify = GetReaderValue_Boolean(reader, "IsPurchaseNotify", false);


                    obj.SalesQueryReply = GetReaderValue_String(reader, "SalesQueryReply", "");
                    obj.PurchaseQueryReply = GetReaderValue_String(reader, "PurchaseQueryReply", "");
                    obj.QualityQueryReply = GetReaderValue_String(reader, "QualityQueryReply", "");
                    obj.SalesApprovalStatus = GetReaderValue_NullableInt32(reader, "SalesApprovalStatus", null);
                    obj.PurchaseApprovalStatus = GetReaderValue_NullableInt32(reader, "PurchaseApprovalStatus", null);
                    obj.QualityApprovalStatus = GetReaderValue_NullableInt32(reader, "QualityApprovalStatus", null);
                    obj.IsPDFReportRequired = GetReaderValue_Boolean(reader, "IsPDFReportRequired", false);
                    obj.IsQuarantineProduct = GetReaderValue_Boolean(reader, "IsQuarantineProduct", false);
                    obj.GoodsInId = GetReaderValue_Int32(reader, "GoodsInId", 0);
                    obj.GIQuery = GetReaderValue_String(reader, "GIQueryData", "");
                    obj.ReqLotNo = GetReaderValue_NullableBoolean(reader, "ReqLotNo", false);
                    obj.LotNoCount = GetReaderValue_NullableInt32(reader, "LotNoCount", 0);
                    obj.ReleseStockDisbaleStatus = GetReaderValue_NullableBoolean(reader, "ReleseStockDisbaleStatus", false);

                    //[007] code start
                    obj.ShortShipmentIds = GetReaderValue_String(reader, "ShortShipmentIds", string.Empty);
                    obj.ShortShipmentNos = GetReaderValue_String(reader, "ShortShipmentNos", string.Empty);
                    //[007] code end
                    //code end for ihs
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// SaveShortShipment
        /// Calls [usp_insert_ShortShipment]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="Supplier"></param>
        /// <param name="SalesContact"></param>
        /// <param name="Reference"></param>
        /// <param name="GoodsIn"></param>
        /// <param name="ReceivedDate"></param>
        /// <param name="RaisedBy"></param>
        /// <param name="Buyer"></param>
        /// <param name="PartNo"></param>
        /// <param name="Manufacturer"></param>
        /// <param name="QuantityOrder"></param>
        /// <param name="QuantityAdvised"></param>
        /// <param name="QuantityReceived"></param>
        /// <param name="ShortageQuantity"></param>
        /// <param name="ShortageValue"></param>
        /// <param name="loginID"></param>
        /// <param name="clientID"></param>
        /// <param name="message"></param>
        /// <param name="ShortShipmentId"></param>
        /// <returns></returns>
        public override bool SaveShortShipment(System.Int32 ID, System.Int32? Supplier, System.Int32? PurchaseOrderNo, System.Int32? SalesContact, System.String Reference, System.Int32 GoodsIn, System.DateTime? ReceivedDate, System.Int32? RaisedBy, System.Int32? Buyer, System.String PartNo, System.Int32? Manufacturer, System.Int32? QuantityOrder, System.String QuantityAdvised, System.Int32 QuantityReceived, System.Int32 ShortageQuantity, System.Double? ShortageValue, System.Int32 loginID, System.Int32? clientID, ref System.String message, ref System.Int32 ShortShipmentId, System.Boolean? IsPOHub, System.Int32? SupportTeamMemberNo, ref System.String NoReplyEmail, ref System.Int32 NoReplyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ShortShipment", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = ID;
                cmd.Parameters.Add("@Supplier", SqlDbType.Int).Value = Supplier;
                cmd.Parameters.Add("@PurchaseOrderNo", SqlDbType.Int).Value = PurchaseOrderNo;
                cmd.Parameters.Add("@SalesContact", SqlDbType.Int).Value = SalesContact;
                cmd.Parameters.Add("@Reference", SqlDbType.NVarChar, 200).Value = Reference;
                cmd.Parameters.Add("@GoodsIn", SqlDbType.Int).Value = GoodsIn;
                cmd.Parameters.Add("@ReceivedDate", SqlDbType.DateTime).Value = ReceivedDate;
                cmd.Parameters.Add("@RaisedBy", SqlDbType.Int).Value = RaisedBy;
                cmd.Parameters.Add("@Buyer", SqlDbType.Int).Value = Buyer;
                cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar, 200).Value = PartNo;
                cmd.Parameters.Add("@Manufacturer", SqlDbType.Int).Value = Manufacturer;
                cmd.Parameters.Add("@QuantityOrder", SqlDbType.Int).Value = QuantityOrder;
                cmd.Parameters.Add("@QuantityAdvised", SqlDbType.Int).Value = QuantityAdvised;
                cmd.Parameters.Add("@QuantityReceived", SqlDbType.Int).Value = QuantityReceived;
                cmd.Parameters.Add("@ShortageQuantity", SqlDbType.Int).Value = ShortageQuantity;
                cmd.Parameters.Add("@ShortageValue", SqlDbType.Float).Value = ShortageValue;
                cmd.Parameters.Add("@loginID", SqlDbType.Int).Value = loginID;
                cmd.Parameters.Add("@clientID", SqlDbType.Int).Value = clientID;
                cmd.Parameters.Add("@ErrorMessage", SqlDbType.VarChar, 200).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ShortShipmentId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@IsPOHub", SqlDbType.Bit).Value = IsPOHub;
                cmd.Parameters.Add("@SupportTeamMemberNo", SqlDbType.Int).Value = SupportTeamMemberNo;
                cmd.Parameters.Add("@NoReplyId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@NoReplyEmail", SqlDbType.VarChar, 100).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                message = cmd.Parameters["@ErrorMessage"].Value.ToString();
                ShortShipmentId = (int)(cmd.Parameters["@ShortShipmentId"].Value == null ? 0 : cmd.Parameters["@ShortShipmentId"].Value);
                NoReplyId = (int)(cmd.Parameters["@NoReplyId"].Value);
                NoReplyEmail = cmd.Parameters["@NoReplyEmail"].Value.ToString();
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to update GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_update_GIQuery]
        /// </summary>
        /// <param name="IsSales"></param>
        /// <param name="IsPurchasing"></param>
        /// <param name="IsQualityApproval"></param>
        /// <param name="LoginID"></param>
        /// <param name="GoodsInLineId"></param>
        /// <returns></returns>
        public override bool UpdateGiQuery(System.Boolean IsSalesNotify, System.Boolean IsPurchaseNotify, System.Boolean IsQualityNotify, System.Int32? LoginID, System.Int32 GoodsInLineId, System.Int32 GoodsInId, System.String Query)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GIQuery", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@IsSalesNotify", SqlDbType.Bit).Value = IsSalesNotify;
                cmd.Parameters.Add("@IsPurchaseNotify", SqlDbType.Bit).Value = IsPurchaseNotify;
                cmd.Parameters.Add("@IsQualityNotify", SqlDbType.Bit).Value = IsQualityNotify;
                cmd.Parameters.Add("@LoginID", SqlDbType.Int).Value = LoginID;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = GoodsInLineId;
                cmd.Parameters.Add("@GoodsInId", SqlDbType.Int).Value = GoodsInId;
                cmd.Parameters.Add("@Query", SqlDbType.NVarChar, -1).Value = Query;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to Update GILine (UpdateGiQuery)", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Calls [usp_get_GILine_Quarantine_Status]
        /// </summary>
        /// <param name="GILineId"></param>
        /// <returns></returns>
        public override bool GetQuarantineStatus(System.Int32 GILineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            bool quarantine = false;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_GILine_Quarantine_Status", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = GILineId;
                cmd.Parameters.Add("@Unavailable", SqlDbType.Bit).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                quarantine = cmd.Parameters["@Unavailable"].Value == DBNull.Value ? false : (bool)cmd.Parameters["@Unavailable"].Value;

                return (quarantine);
            }
            catch (SqlException sqlex)
            {
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to get quarantine status", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// NotifyQuery
        /// Calls [usp_update_NotifyQuery]
        /// </summary>
        /// <param name="ID"></param>
        /// <param name="GILineQueryStatus"></param>
        /// <param name="IsPDFReportRequired"></param>
        /// <param name="IsQuarantineProduct"></param>
        /// <param name="QueryReply"></param>
        /// <param name="LoginId"></param>
        /// <returns></returns>
        public override bool NotifyQuery(System.Int32 ID, System.Int32? QueryApprovedStatusSales, System.Int32? QueryApprovedStatusPurchase, System.Int32? QueryApprovedStatusQuality, System.Boolean IsPDFReportRequired, System.Boolean IsQuarantineProduct, System.String QueryReply, System.Int32? LoginId, System.Int32 LoginType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_NotifyQuery", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = ID;
                cmd.Parameters.Add("@QueryApprovedStatusSales", SqlDbType.Int).Value = QueryApprovedStatusSales;
                cmd.Parameters.Add("@QueryApprovedStatusPurchase", SqlDbType.Int).Value = QueryApprovedStatusPurchase;
                cmd.Parameters.Add("@QueryApprovedStatusQuality", SqlDbType.Int).Value = QueryApprovedStatusQuality;
                cmd.Parameters.Add("@IsPDFReportRequired", SqlDbType.Bit).Value = IsPDFReportRequired;
                cmd.Parameters.Add("@IsQuarantineProduct", SqlDbType.Bit).Value = IsQuarantineProduct;
                cmd.Parameters.Add("@QueryReply", SqlDbType.NVarChar).Value = QueryReply;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@LoginType", SqlDbType.Int).Value = LoginType;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to Notify Query (NotifyQuery)", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<GoodsInLineDetails> GIQueryStatusDropDown(System.Int32? IsPartialGIQueryStatus)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GIQueryStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@IsPartialGIQueryStatus", SqlDbType.Int).Value = IsPartialGIQueryStatus;
                cmd.CommandTimeout = 60;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.ID = GetReaderValue_NullableInt32(reader, "ID", null);
                    obj.Name = GetReaderValue_String(reader, "Name", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GIQueryStatusDropDown", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update GI Lines Quarantine
        /// Calls [usp_update_GILine_Quarantined]
        /// </summary>
        /// <param name="iD"></param>
        /// <param name="LoginId"></param>
        /// <param name="Quarantine"></param>
        /// <returns></returns>
        public override bool UpdateGILineQuarantine(int iD, int loginID, bool Quarantine, int? clientId, ref string message)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_GILine_Quarantined", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = iD;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = loginID;
                cmd.Parameters.Add("@Quarantine", SqlDbType.Bit).Value = Quarantine;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to Update GI Lines Quarantine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Get email of the sale of the SO of this GI line
        /// </summary>
        /// <param name="GILineID">ID of the GIline</param>
        /// <returns></returns>
        public override int GetSaleEmail(int GILineID, out int StockId, out string partNo, out string strSOLineNumber)
        {
            StockId = 0;
            strSOLineNumber = "";
            partNo = "";
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_GILine_SaleEmail", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = GILineID;
                cmd.Parameters.Add("@SaleLoginId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@StockId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@SOLineNumbers", SqlDbType.NVarChar, 1500).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@PartNo", SqlDbType.NVarChar, 100).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                StockId = cmd.Parameters["@StockId"].Value == DBNull.Value ? 0 : (int)cmd.Parameters["@StockId"].Value;
                strSOLineNumber = cmd.Parameters["@SOLineNumbers"].Value == DBNull.Value ? "" : (string)cmd.Parameters["@SOLineNumbers"].Value;
                partNo = cmd.Parameters["@PartNo"].Value == DBNull.Value ? "" : (string)cmd.Parameters["@PartNo"].Value;

                return cmd.Parameters["@SaleLoginId"].Value == DBNull.Value ? 0 : (int)cmd.Parameters["@SaleLoginId"].Value; ;
            }
            catch (SqlException sqlex)
            {
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to Update GI Lines Quarantine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_StockImageByGILineForSAN]
        /// </summary>
        public override Int32 InsertImage(System.Int32? GILineID, System.String caption, System.String GILineImageName, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(Globals.Settings.StockImages.ConnectionString);
                cmd = new SqlCommand("usp_insert_StockImageByGILineForSAN", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = GILineID;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar).Value = caption;
                cmd.Parameters.Add("@ImageName", SqlDbType.NVarChar).Value = GILineImageName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Gi Line Image", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_GoodsInLinePDF]
        /// </summary>
        public override Int32 InsertPDF(System.Int32? GILineID, System.String caption, System.String GILinePdfName, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(Globals.Settings.StockImages.ConnectionString);
                cmd = new SqlCommand("usp_insert_GoodsInLinePDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = GILineID;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar).Value = caption;
                cmd.Parameters.Add("@FileName", SqlDbType.NVarChar).Value = GILinePdfName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Gi Line PDF", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<StockImageDetails> GetGILineImageList(System.Int32? GILineId, System.String fileType, System.Int32? GIId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Image_for_GILine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ResultNo", SqlDbType.Int).Value = GILineId;
                cmd.Parameters.Add("@GIId", SqlDbType.Int).Value = GIId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockImageDetails> lstPDF = new List<StockImageDetails>();
                while (reader.Read())
                {
                    StockImageDetails obj = new StockImageDetails();
                    obj.ImageId = GetReaderValue_Int32(reader, "ImageId", 0);
                    obj.ImageDocumentRefNo = GetReaderValue_Int32(reader, "ResultNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.ImageName = GetReaderValue_String(reader, "ImageName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.UpdatedByName = GetReaderValue_String(reader, "ImageName", "");
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GI Line Image List", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 InsertGILineImage(System.Int32? resultNo, System.String caption, System.String ImageName, System.Int32? updatedBy, System.Int32? GIId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(Globals.Settings.StockImages.ConnectionString);
                cmd = new SqlCommand("usp_insert_GILineImage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ResultNo", SqlDbType.Int).Value = resultNo;
                cmd.Parameters.Add("@GIId", SqlDbType.Int).Value = GIId;
                cmd.Parameters.Add("@Caption", SqlDbType.NVarChar).Value = caption;
                cmd.Parameters.Add("@ImageName", SqlDbType.NVarChar).Value = ImageName;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert GI Line Image", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool DeleteGILineImage(System.Int32? ImageNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(Globals.Settings.StockImages.ConnectionString);
                cmd = new SqlCommand("usp_delete_GILineImage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ImageNo", SqlDbType.Int).Value = ImageNo;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete GI Line Image", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        #region Add/Edit/Get Lot Code for GI Edit 
        public override List<GoodsInLineDetails> GITempLotSearch(System.Int32? clientId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.Int32? goodsInNo, System.Int32? goodsInLineNo, System.Int32? loginNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_itemsearch_GoodsInTempLotNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInNo;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = goodsInLineNo;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = loginNo;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.LotNoId = GetReaderValue_Int32(reader, "LotNoId", 0);
                    obj.LotNumber = GetReaderValue_String(reader, "LotNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "SubGroup", "");
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.InvoiceLineNo = GetReaderValue_Int32(reader, "InvoiceLineNo", 0);
                    obj.GoodsInNumber = GetReaderValue_Int32(reader, "GoodsInNumber", 0);
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsIn lot number", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 InsertLotNo(System.String subGroup, System.String lotNo, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage, out System.Int32 totalLot)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_LotNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@lotNo", SqlDbType.VarChar).Value = lotNo;
                cmd.Parameters.Add("@subGroup", SqlDbType.VarChar).Value = subGroup;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@LotNoId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@CountStatus", SqlDbType.VarChar, 250).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@TotalLotCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                // int ret = ExecuteNonQuery(cmd);

                validateMessage = null;
                ExecuteScalar(cmd);
                int ret = (int)(cmd.Parameters["@LotNoId"].Value == null ? 0 : cmd.Parameters["@LotNoId"].Value);
                validateMessage = (cmd.Parameters["@CountStatus"].Value.Equals(DBNull.Value) == true ? null : (String)cmd.Parameters["@CountStatus"].Value);
                totalLot = (int)(cmd.Parameters["@TotalLotCount"].Value == null ? 0 : cmd.Parameters["@TotalLotCount"].Value);
                if (ret == -1 && validateMessage == null)
                {
                    validateMessage = "Duplicate Lot Numbers not allowed";
                    return ret;
                }

                return (Int32)cmd.Parameters["@LotNoId"].Value;

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert Lot No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 UpdateLotNo(System.Int32? lotId, System.String subGroup, System.String lotNo, System.Int32? goodsInId, System.String status, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_LotNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@lotId", SqlDbType.Int).Value = lotId;
                cmd.Parameters.Add("@lotNo", SqlDbType.VarChar).Value = lotNo;
                cmd.Parameters.Add("@subGroup", SqlDbType.VarChar).Value = subGroup;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@Status", SqlDbType.VarChar).Value = status;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@LotNoId", SqlDbType.Int).Direction = ParameterDirection.Output;

                cn.Open();
                //int ret = ExecuteNonQuery(cmd);
                //return (Int32)cmd.Parameters["@SerialNoId"].Value;
                int ret = ExecuteNonQuery(cmd);
                validateMessage = null;
                if (ret == -1)
                {
                    validateMessage = "Duplicate Lot Numbers not allowed";
                }
                return ret;// (Int32)cmd.Parameters["@SerialNoId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update LotNo", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 InsertAllLotNo(System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy, out System.String validateMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_AllLotNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@LotNoId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@CountStatus", SqlDbType.VarChar, 250).Direction = ParameterDirection.Output;
                cn.Open();
                //int ret = ExecuteNonQuery(cmd);               
                //return (Int32)cmd.Parameters["@SerialNoId"].Value;
                // return ret;
                validateMessage = null;
                ExecuteScalar(cmd);
                //int ret = (int)(cmd.Parameters["@SerialNoId"].Value == null ? 0 : cmd.Parameters["@SerialNoId"].Value);
                validateMessage = (cmd.Parameters["@CountStatus"].Value.Equals(DBNull.Value) == true ? null : (String)cmd.Parameters["@CountStatus"].Value);

                return (Int32)cmd.Parameters["@LotNoId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert all Lot No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<GoodsInLineDetails> GetLot(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_AllLotNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInId", SqlDbType.Int).Value = goodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.LotNoId = GetReaderValue_Int32(reader, "LotNumberId", 0);
                    obj.LotNumber = GetReaderValue_String(reader, "LotNo", "");
                    obj.SubGroup = GetReaderValue_String(reader, "Group", null);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Lot Details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 DeleteLotNo(System.Int32? lotNoId, System.String status, System.Int32? goodsInId, System.Int32? goodsInLineId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_LotNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@LotNoId", SqlDbType.Int).Value = lotNoId;
                cmd.Parameters.Add("@Status", SqlDbType.VarChar).Value = status;
                cmd.Parameters.Add("@GoodsInNo", SqlDbType.VarChar).Value = goodsInId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.VarChar).Value = goodsInLineId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete Lot No", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        #endregion

        #region

        /// <summary>
        /// Get 
        /// Calls [usp_SelectAll_GILines_QueryMessages]
        /// </summary>
        public override List<GoodsInLineDetails> GetGILineQueryMessage(System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.Int32? ClientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SelectAll_GILines_QueryMessages", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GoodInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GILineNo", SqlDbType.Int).Value = GiLineNo;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = ClientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.QueryMessage = GetReaderValue_String(reader, "QueryMessage", "");
                    obj.QueryMessageApproval = GetReaderValue_String(reader, "QueryMessageApproval", "");
                    obj.NotifyToSales = GetReaderValue_Boolean(reader, "NotifyToSales", false);
                    obj.NotifyToQuality = GetReaderValue_Boolean(reader, "NotifyToQuality", false);
                    obj.NotifyToPurchasing = GetReaderValue_Boolean(reader, "NotifyToPurchasing", false);
                    obj.IsSendMail = GetReaderValue_Boolean(reader, "IsSendMail", false);
                    obj.MyMessage = GetReaderValue_Boolean(reader, "MyMessage", false);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.ISInitialNotifyToSales = GetReaderValue_Boolean(reader, "ISInitialNotifyToSales", false);
                    obj.ISInitialNotifyToQuality = GetReaderValue_Boolean(reader, "ISInitialNotifyToQuality", false);
                    obj.ISInitialNotifyToPurchase = GetReaderValue_Boolean(reader, "ISInitialNotifyToPurchase", false);
                    obj.IsInitialMessage = GetReaderValue_Boolean(reader, "IsInitialMessage", false);
                    obj.SalesApprovalStatus = GetReaderValue_Int32(reader, "IsSalesApproved", 0);
                    obj.QualityApprovalStatus = GetReaderValue_Int32(reader, "IsQualityApproved", 0);
                    obj.PurchaseApprovalStatus = GetReaderValue_Int32(reader, "IsPurchasingApproved", 0);
                    obj.GIQueryNumber = GetReaderValue_String(reader, "GI_QueryNumber", "");
                    obj.InDraftMode = GetReaderValue_Boolean(reader, "InDraftMode", false);
                    obj.Gi_QueryId = GetReaderValue_Int32(reader, "GI_QueryId", 0);
                    obj.WarehouseRemark = GetReaderValue_String(reader, "WarehouseRemark", "");
                    obj.ISSalesPermission = GetReaderValue_Boolean(reader, "ISSalesPermission", false);
                    obj.ISPurchasingPermission = GetReaderValue_Boolean(reader, "ISPurchasingPermission", false);
                    obj.ISQualityPermission = GetReaderValue_Boolean(reader, "ISQualityPermission", false);
                    //[009] code start
                    obj.ParentSalesApprovalStatus = GetReaderValue_Int32(reader, "SalesApprovalStatus", 0);
                    obj.ParentPurchaseApprovalStatus = GetReaderValue_Int32(reader, "PurchasingApprovalStatus", 0);
                    obj.ParentQualityApprovalStatus = GetReaderValue_Int32(reader, "QualityApprovalStatus", 0);
                    obj.CurrentPurchasingApprover = GetReaderValue_String(reader, "CurrentPurchasingApprover", "");
                    obj.CurrentSalesApprover = GetReaderValue_String(reader, "CurrentSalesApprover", "");
                    obj.CCUsersName = GetReaderValue_String(reader, "CCUsersName", "");
                    obj.DraftQueryMessage = GetReaderValue_String(reader, "DraftQueryMessage", "");
                    obj.QueryRaisedBy = GetReaderValue_String(reader, "QueryRaisedBy", "");

                    obj.IsNotGBLPermissionForSales = GetReaderValue_NullableBoolean(reader, "IsNotGBLPermissionForSales", null);
                    obj.IsNotGBLPermissionForPurch = GetReaderValue_NullableBoolean(reader, "IsNotGBLPermissionForPurch", null);
                    obj.IsNotGBLPermissionForQaulity = GetReaderValue_NullableBoolean(reader, "IsNotGBLPermissionForQaulity", null);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.Quarantined = GetReaderValue_NullableBoolean(reader, "Quarantined", false);

                    obj.SalesGroupId = GetReaderValue_Int32(reader, "SalesGroupId", 0);
                    obj.SalesGroupName = GetReaderValue_String(reader, "SalesGroupName", "");
                    obj.PurchasingGroupId = GetReaderValue_Int32(reader, "PurchasingGroupId", 0);
                    obj.PurchasingGroupName = GetReaderValue_String(reader, "PurchasingGroupName", "");

                    obj.ProcessPurchaseApproverName = GetReaderValue_String(reader, "ProcessPurchaseApproverName", "");
                    obj.ProcessSalesApproverName = GetReaderValue_String(reader, "ProcessSalesApproverName", "");
                    obj.ProcessQualityApproverName = GetReaderValue_String(reader, "ProcessQualityApproverName", "");
                    obj.C1 = GetReaderValue_NullableBoolean(reader, "C1", false);
                    obj.C2 = GetReaderValue_NullableBoolean(reader, "C2", false);
                    obj.C3 = GetReaderValue_NullableBoolean(reader, "C3", false);
                    obj.C4 = GetReaderValue_NullableBoolean(reader, "C4", false);
                    obj.C5 = GetReaderValue_NullableBoolean(reader, "C5", false);
                    obj.C6 = GetReaderValue_NullableBoolean(reader, "C6", false);
                    obj.C7 = GetReaderValue_NullableBoolean(reader, "C7", false);
                    obj.C8 = GetReaderValue_NullableBoolean(reader, "C8", false);
                    obj.C9 = GetReaderValue_NullableBoolean(reader, "C9", false);
                    obj.C10 = GetReaderValue_NullableBoolean(reader, "C10", false);
                    obj.ISReleaseStock = GetReaderValue_NullableBoolean(reader, "ISReleaseStock", false);
                    obj.IsQueryColumn = GetReaderValue_NullableBoolean(reader, "IsQueryColumn", false);
                    //[009] code end
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine query messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [usp_Insert_GILineQueryMessage]
        /// </summary>
        public override List<GoodsInLineDetails> AddGILineQueryMessage(System.Int32? GI_QueryId, System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.String QueryMessage, System.Boolean? IsSalesNotify, System.Boolean? IsQualityNotify, System.Boolean? IsPurchasingNotify, System.Int32? ClientNo, System.String CCUserId, System.String CCGroupIDs)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Insert_GILineQueryMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GI_QueryId", SqlDbType.Int).Value = GI_QueryId;
                cmd.Parameters.Add("@GoodInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GILineNo", SqlDbType.Int).Value = GiLineNo;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@QueryMessage", SqlDbType.NVarChar, 3000).Value = QueryMessage;
                cmd.Parameters.Add("@IsSalesNotify", SqlDbType.Bit).Value = IsSalesNotify;
                cmd.Parameters.Add("@IsQualityNotify", SqlDbType.Bit).Value = IsQualityNotify;
                cmd.Parameters.Add("@IsPurchasingNotify", SqlDbType.Bit).Value = IsPurchasingNotify;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cmd.Parameters.Add("@CCUserId", SqlDbType.NVarChar, 1500).Value = CCUserId;
                cmd.Parameters.Add("@CCGroupIDs", SqlDbType.NVarChar, 1500).Value = CCGroupIDs;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.Result = GetReaderValue_Int32(reader, "Result", 0);
                    obj.ISInitialNotifyToSales = GetReaderValue_Boolean(reader, "ISInitialNotifyToSales", false);
                    obj.ISInitialNotifyToQuality = GetReaderValue_Boolean(reader, "ISInitialNotifyToQuality", false);
                    obj.ISInitialNotifyToPurchase = GetReaderValue_Boolean(reader, "ISInitialNotifyToPurchase", false);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.NoReplyId = GetReaderValue_Int32(reader, "NoReplyId", 0);
                    obj.NoReplyEmail = GetReaderValue_String(reader, "NoReplyEmail", "");
                    obj.QueryMessage = GetReaderValue_String(reader, "QueryMessageOut", "");
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine query messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Get 
        /// Calls [usp_Select_GILines_Approvals]
        /// </summary>
        public override List<GoodsInLineDetails> GetGILineApprovals(System.Int32? goodsInId, System.Int32? GiLineNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Select_GILines_Approvals", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GoodInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GILineNo", SqlDbType.Int).Value = GiLineNo;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.RaisedBy = GetReaderValue_String(reader, "RaisedBy", "");
                    obj.Department = GetReaderValue_String(reader, "Department", "");
                    obj.ApprovalName = GetReaderValue_String(reader, "ApprovalName", "");
                    obj.ApprovedDate = GetReaderValue_DateTime(reader, "ApprovedDate", DateTime.MinValue);
                    obj.ApprovalStatus = GetReaderValue_String(reader, "Status", "");
                    obj.CurrentPurchasingApprover = GetReaderValue_String(reader, "CurrentPurchasingApprover", "");
                    obj.CurrentSalesApprover = GetReaderValue_String(reader, "CurrentSalesApprover", "");
                    obj.CurrentSalesApprovalStatus = GetReaderValue_String(reader, "SalesApprovalStatus", "");
                    obj.CurrentPurchasingApprovalStatus = GetReaderValue_String(reader, "PurchasingApprovalStatus", "");
                    obj.CurrentQualityApprovalStatus = GetReaderValue_String(reader, "QualityApprovalStatus", "");
                    obj.ParentSalesApprovalStatus = GetReaderValue_Int32(reader, "ParentSalesApprovalStatus", 0);
                    obj.ParentPurchaseApprovalStatus = GetReaderValue_Int32(reader, "ParentPurchaseApprovalStatus", 0);
                    obj.ParentQualityApprovalStatus = GetReaderValue_Int32(reader, "ParentQualityApprovalStatus", 0);
                    obj.Gi_QueryId = GetReaderValue_Int32(reader, "GI_QueryNo", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine query messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        // [002] code start
        /// <summary>
        /// GetPDFListForGoodsIn 
        /// Calls [usp_selectAll_PDF_for_GoodsIn]
        /// </summary>
        public override List<PDFDocumentDetails> GetPDFListForGoodsInLine(System.Int32? GoodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_PDF_for_GoodsInLine", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = GoodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PDFDocumentDetails> lstPDF = new List<PDFDocumentDetails>();
                while (reader.Read())
                {
                    PDFDocumentDetails obj = new PDFDocumentDetails();
                    obj.PDFDocumentId = GetReaderValue_Int32(reader, "StockPDFId", 0);
                    obj.PDFDocumentRefNo = GetReaderValue_Int32(reader, "StockNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.FullCaption = GetReaderValue_String(reader, "FullCaption", "");
                    obj.FileName = GetReaderValue_String(reader, "FileName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PDF list for GoodsIn", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<SupplierPoApprovalDetails> GetImageData(System.Int32? GoodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Image_for_GILineAttachments", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = GoodsInLineId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SupplierPoApprovalDetails> lstPDF = new List<SupplierPoApprovalDetails>();
                while (reader.Read())
                {
                    SupplierPoApprovalDetails obj = new SupplierPoApprovalDetails();
                    obj.ImageId = GetReaderValue_Int32(reader, "StockImageId", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.FullCaption = GetReaderValue_String(reader, "FullCaption", "");
                    obj.ImageName = GetReaderValue_String(reader, "ImageName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PDF list for supplier approval", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Get 
        /// Calls [usp_Insert_GILineQueryApprovalResponce]
        /// </summary>
        public override List<GoodsInLineDetails> AddGILineQueryApprovalResponce(System.Int32? GI_QueryId, System.Int32? goodsInId, System.Int32? GiLineNo, System.Int32? LoginId, System.String QueryMessage, System.Int32? SalesApprovalStatus, System.Int32? QualityApprovalStatus, System.Int32? PurchasingApprovalStatus, System.String CCUserId, System.Int32? ClientNo, System.String CCGroupIDs, System.String ApproverHtml, System.Int32? TotalCheckBoxcount, System.Int32? CheckedTotalCheckBoxcount, System.String GetEnableCheckBoxIds)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Insert_GILineQueryApprovalResponce", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GI_QueryId", SqlDbType.Int).Value = GI_QueryId;
                cmd.Parameters.Add("@GoodInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GILineNo", SqlDbType.Int).Value = GiLineNo;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@QueryMessage", SqlDbType.NVarChar, 3000).Value = QueryMessage;
                cmd.Parameters.Add("@SalesApprovalStatus", SqlDbType.Int).Value = SalesApprovalStatus;
                cmd.Parameters.Add("@QualityApprovalStatus", SqlDbType.Int).Value = QualityApprovalStatus;
                cmd.Parameters.Add("@PurchasingApprovalStatus", SqlDbType.Int).Value = PurchasingApprovalStatus;
                cmd.Parameters.Add("@CCUserId", SqlDbType.NVarChar, 1500).Value = CCUserId;
                cmd.Parameters.Add("@CCGroupIDs", SqlDbType.NVarChar, 1500).Value = CCGroupIDs;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cmd.Parameters.Add("@ApproverHtml", SqlDbType.NVarChar, 7000).Value = ApproverHtml;
                cmd.Parameters.Add("@TotalCheckBoxcount", SqlDbType.Int).Value = TotalCheckBoxcount;
                cmd.Parameters.Add("@CheckedTotalCheckBoxcount", SqlDbType.Int).Value = CheckedTotalCheckBoxcount;
                cmd.Parameters.Add("@GetEnableCheckBoxIds", SqlDbType.NVarChar, 7000).Value = GetEnableCheckBoxIds;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.Result = GetReaderValue_Int32(reader, "Result", 0);
                    obj.ISInitialNotifyToSales = GetReaderValue_Boolean(reader, "ISInitialNotifyToSales", false);
                    obj.ISInitialNotifyToQuality = GetReaderValue_Boolean(reader, "ISInitialNotifyToQuality", false);
                    obj.ISInitialNotifyToPurchase = GetReaderValue_Boolean(reader, "ISInitialNotifyToPurchase", false);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.NoReplyId = GetReaderValue_Int32(reader, "NoReplyId", 0);
                    obj.NoReplyEmail = GetReaderValue_String(reader, "NoReplyEmail", "");
                    obj.QueryMessage = GetReaderValue_String(reader, "QueryMessageOut", "");
                    obj.SalesApproverId = GetReaderValue_Int32(reader, "SalesApproverId", 0);
                    obj.PurchasingApproverId = GetReaderValue_Int32(reader, "PurchasingApproverId", 0);
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine query messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Calls [usp_Insert_ChangeApprover]
        /// </summary>
        public override List<GoodsInLineDetails> ChangeApprover(System.Int32? GI_QueryId, System.Int32? NewSalesApprover, System.Int32? NewPurchasingApprover, System.Int32? LoginId, System.Int32? ClientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Insert_ChangeApprover", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GI_QueryId", SqlDbType.Int).Value = GI_QueryId;
                cmd.Parameters.Add("@NewSalesApprover", SqlDbType.Int).Value = NewSalesApprover;
                cmd.Parameters.Add("@NewPurchasingApprover", SqlDbType.Int).Value = NewPurchasingApprover;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.PreviousSalesApproverId = GetReaderValue_Int32(reader, "PreviousSalesApproverId", 0);
                    obj.PreviousPurchasingApproverId = GetReaderValue_Int32(reader, "PreviousPurchasingApproverId", 0);
                    obj.PreviousSalesApproverName = GetReaderValue_String(reader, "PreviousSalesApproverName", "");
                    obj.PreviousPurchasingApproverName = GetReaderValue_String(reader, "PreviousPurchasingApproverName", "");
                    obj.CurrentSalesApprover = GetReaderValue_String(reader, "CurrentSalesApproverName", "");
                    obj.CurrentPurchasingApprover = GetReaderValue_String(reader, "CurrentPurchasingApproverName", "");
                    obj.NoReplyId = GetReaderValue_Int32(reader, "NoReplyId", 0);
                    obj.NoReplyEmail = GetReaderValue_String(reader, "NoReplyEmail", "");
                    obj.PurchaseOrderNo = GetReaderValue_Int32(reader, "PurchaseOrderNo", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.GoodsInId = GetReaderValue_Int32(reader, "GoodsInId", 0);
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.GIQueryNumber = GetReaderValue_String(reader, "GIQueryNumber", "");
                    obj.QueryMessage = GetReaderValue_String(reader, "QueryMessage", "");
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine query messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        #endregion
        /// <summary>
        /// calls [usp_select_MyGIQueries]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<GoodsInLineDetails> GetMyGIQueries(System.Int32? loginId, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_MyGIQueries", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInId = GetReaderValue_Int32(reader, "GoodsInId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.SalesOrderNo = GetReaderValue_NullableInt32(reader, "SalesOrderNo", null);
                    obj.SalesOrderNumber = GetReaderValue_NullableInt32(reader, "SalesOrderNumber", null);
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "SentDate", null);
                    obj.Approvers = GetReaderValue_String(reader, "Approvers", "");
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetMyGIQueries ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// calls [GetEnhancedInspection]
        /// </summary>
        /// <returns></returns>
        public override List<GoodsInLineDetails> GetEnhancedInspection()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_EnhancedInspection", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.ID = GetReaderValue_Int32(reader, "Id", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetEnhancedInspection ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Get 
        /// Calls [usp_GetQueryMessagesCCUsers]
        /// </summary>
        public override List<GoodsInLineDetails> GetCCUserFoEmail(System.Int32? GI_QueryId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetQueryMessagesCCUsers", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GI_QueryId", SqlDbType.Int).Value = GI_QueryId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.CCUSerId = GetReaderValue_Int32(reader, "UserId", 0);
                    obj.IsCCMailGroupId = GetReaderValue_NullableBoolean(reader, "IsCCMailGroupId", false);
                    obj.MailType = GetReaderValue_String(reader, "MailType", "");
                    obj.SalesGroupName = GetReaderValue_String(reader, "SalesGroupName", "");
                    obj.PurchasingGroupName = GetReaderValue_String(reader, "PurchasingGroupName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Query Message CC Users.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override List<GoodsInLineDetails> GetMFRLabelListJSON()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_allsearch_MFRLabel", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.ID = GetReaderValue_Int32(reader, "ID", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetMFRLabelListJSON", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override Int32 DraftGILineQueryMessage(System.Int32? GI_QueryId, System.Int32 GoodsInId, System.Int32? GoodsInLineId, System.Int32? LoginID, System.String QueryMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_DraftGILineQuery", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GI_QueryId", SqlDbType.Int).Value = GI_QueryId;
                cmd.Parameters.Add("@GoodsInId", SqlDbType.Int).Value = GoodsInId;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = GoodsInLineId;
                cmd.Parameters.Add("@LoginID", SqlDbType.Int).Value = LoginID;
                cmd.Parameters.Add("@QueryMessage", SqlDbType.VarChar).Value = QueryMessage;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Draft GILine Query Message", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool DeleteGILine_PDF(int PDFDocId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Delete_GILinesPDF", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GI_PDF_ID", SqlDbType.Int).Value = PDFDocId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return ret > 0;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Delete GI-Lines PDF", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool DeleteGILine_Image(int ImageId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Delete_GILinesImage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GI_IMG_ID", SqlDbType.Int).Value = ImageId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return ret > 0;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Delete GI-Lines PDF", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override Int32 RenameCaption(System.Int32? AttachmentId, System.String Caption, System.String Type, System.Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_AttachmentCaption", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@AttachmentId", SqlDbType.Int).Value = AttachmentId;
                cmd.Parameters.Add("@Caption", SqlDbType.VarChar).Value = Caption;
                cmd.Parameters.Add("@Type", SqlDbType.VarChar).Value = Type;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return ret;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed To update the caption.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<GoodsInLineDetails> BulkAttachmentDelete(System.String ImageAttachments, System.String PdfAttachments)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_BulkDelete_GIAttachments", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ImageAttachments", SqlDbType.VarChar).Value = ImageAttachments;
                cmd.Parameters.Add("@PdfAttachments", SqlDbType.VarChar).Value = PdfAttachments;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.AttachmentFileName = GetReaderValue_String(reader, "AttachmentFileName", "");
                    obj.AttachmentType = GetReaderValue_String(reader, "AttachmentType", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed To Bulk delete Attachments", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[013] start
        /// <summary>
        /// calls [usp_stock_attachment_file_count]
        /// </summary>
        /// <param name="FileName"></param>
        /// <param name="FileType"></param>
        /// <returns></returns>
        public override int StockAttachmentFileCountByFileName(string FileName, string FileType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_stock_attachment_file_count", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@FileName", SqlDbType.VarChar).Value = FileName;
                cmd.Parameters.Add("@FileType", SqlDbType.VarChar).Value = FileType;
                cmd.Parameters.Add("@FileCount", SqlDbType.Int).Direction = ParameterDirection.Output;
                //SqlParameter fileCount = new SqlParameter("@FileCount", SqlDbType.Int);
                //fileCount.Direction = ParameterDirection.Output;

                cn.Open();
                int rowCount = cmd.ExecuteNonQuery();
                int blobCount = Convert.ToInt32(cmd.Parameters["@FileCount"].Value);
                return blobCount;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed To Bulk delete Attachments", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[003] end

        /// <summary>
        /// Get 
        /// Calls [usp_Previous_GILines_QueryMessages]
        /// </summary>
        public override List<GoodsInLineDetails> GetPreviousChatOnGILine(System.Int32? GI_QueryId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Previous_GILines_QueryMessages", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GI_QueryId", SqlDbType.Int).Value = GI_QueryId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.MessageAuther = GetReaderValue_String(reader, "MessageAuther", "");
                    obj.QueryMessage = GetReaderValue_String(reader, "QueryMessage", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.Status = GetReaderValue_String(reader, "Status", "");

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine query messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool CheckDeleteAttcmntPermission(int? ClientNo, int? LoginUser)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            bool blnAccessPermission = false;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetGILinesAttachmentsPermissions", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = ClientNo;
                cmd.Parameters.Add("@LoginNo", SqlDbType.Int).Value = LoginUser;
                cmd.Parameters.Add("@IsAccessPermission", SqlDbType.Bit).Direction = ParameterDirection.Output;

                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                blnAccessPermission = (bool)cmd.Parameters["@IsAccessPermission"].Value;
                return blnAccessPermission;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed To update the caption.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// calls [usp_select_MyQualityGIQueries]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<GoodsInLineDetails> MyQualityGIQueries(System.Int32? loginId, System.Int32? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_MyQualityGIQueries", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInId = GetReaderValue_Int32(reader, "GoodsInId", 0);
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.SalesOrderNo = GetReaderValue_NullableInt32(reader, "SalesOrderNo", null);
                    obj.SalesOrderNumber = GetReaderValue_NullableInt32(reader, "SalesOrderNumber", null);
                    obj.DeliveryDate = GetReaderValue_NullableDateTime(reader, "SentDate", null);
                    obj.Status = GetReaderValue_String(reader, "Status", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GetMyGIQueries ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Start/close inspection.
        /// Calls [usp_GILine_StartOrCloseInspection]
        /// </summary>
        /// <param name="iD"></param>
        /// <param name="Comment"></param>
        /// <param name="LoginId"></param>
        /// <param name="InspectionOption"></param>
        /// <returns>bool</returns>
        public override bool StartOrCloseInspection(int iD, string Comment, System.Int32? LoginId, System.Int32? InspectionOption)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GILine_StartOrCloseInspection", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = iD;
                cmd.Parameters.Add("@Comment", SqlDbType.NVarChar, 2000).Value = Comment;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@InspectionOption", SqlDbType.Int).Value = InspectionOption;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to Update start inspection.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// usp_GI_InspectionsLog
        /// </summary>
        public override List<GoodsInLineDetails> GetInspectionHistory(System.Int32? goodsInLineNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GI_InspectionsLog", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@GoodsInLineNo", SqlDbType.Int).Value = goodsInLineNo;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.InspectionLogId = GetReaderValue_Int32(reader, "InspectionLogId", 0);
                    obj.ActionTaken = GetReaderValue_String(reader, "InspectionAction", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "InspectionDate", DateTime.MinValue);
                    obj.InspectedByUser = GetReaderValue_String(reader, "InspectionByName", "");
                    obj.CommentText = GetReaderValue_String(reader, "InspectionComment", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get ship cost history", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }

        }
        /// <summary>
        /// GetCloseInspectionData 
        /// Calls [usp_select_CloseInspectionData]
        /// </summary>
        public override GoodsInLineDetails GetCloseInspectionData(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_CloseInspectionData", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;

                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.GoodsInLineId = GetReaderValue_Int32(reader, "GoodsInLineId", 0);
                    obj.PartNumberFilled = GetReaderValue_String(reader, "PartNumberFilled", "");
                    obj.PartNumberFilledChkEnable = GetReaderValue_NullableBoolean(reader, "PartNumberFilledChkEnable", false);
                    obj.PackageBreakdownComplete = GetReaderValue_String(reader, "PackageBreakdownComplete", "");
                    obj.PackageBreakdownCompleteChkEnable = GetReaderValue_NullableBoolean(reader, "PackageBreakdownCompleteChkEnable", false);
                    obj.PhotosAttached = GetReaderValue_String(reader, "PhotosAttached", "");
                    obj.PhotosAttachedChkEnable = GetReaderValue_NullableBoolean(reader, "PhotosAttachedChkEnable", false);
                    obj.BarcodeScannedTicked = GetReaderValue_String(reader, "BarcodeScannedTicked", "");
                    obj.BarcodeScannedTickedChkEnable = GetReaderValue_NullableBoolean(reader, "BarcodeScannedTickedChkEnable", false);
                    obj.QueryFormatUse = GetReaderValue_String(reader, "QueryFormatUse", "");
                    obj.QueryFormatUseChkEnable = GetReaderValue_NullableBoolean(reader, "QueryFormatUseChkEnable", false);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get close inspection data.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        #region Related Receipt
        /// <summary>
        /// Get 
        /// Calls [usp_SelectAll_GILines_QueryMessages]
        /// </summary>
        public override List<GoodsInLineDetails> GetRelatedReceiptLines(System.Int32? GiLineNo, System.Int32? goodsInId, System.Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_SelectAll_RelatedReceiptLines", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@GoodInNo", SqlDbType.Int).Value = goodsInId;
                cmd.Parameters.Add("@GILineNo", SqlDbType.Int).Value = GiLineNo;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.Part = GetReaderValue_String(reader, "Part", "");
                    obj.ROHS = GetReaderValue_Byte(reader, "ROHS", (byte)0);
                    obj.supplierType = GetReaderValue_String(reader, "supplierType", "");
                    obj.GoodsInNo = GetReaderValue_Int32(reader, "GoodsInNo", 0);
                    obj.DateReceived = GetReaderValue_DateTime(reader, "DateReceived", DateTime.MinValue);
                    obj.PIBy = GetReaderValue_String(reader, "PIBy", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine query messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override string GetPackagingBreakdownHtml(System.Int32 goodsInLineId, System.Boolean? IsPDF)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_PackagingBreakdownHtml", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cmd.Parameters.Add("@IsPDF", SqlDbType.Bit).Value = IsPDF;
                cn.Open();
                string ret = "";
                DbDataReader reader = cmd.ExecuteReader(CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    ret = GetReaderValue_String(reader, "PackBreakDownHtml", "");
                }
                return ret;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to Get Packaging Breakdown Html", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        #endregion

        #region [012]
        /// <summary>
        /// Get
        /// Calls [usp_Get_GI_Barcode_Scan_Status]
        /// </summary>
        /// <param name="loginId"></param>
        /// <param name="clientId"></param>
        /// <returns></returns>
        public override List<GoodsInLineDetails> GetBarcodeStatusList(int? loginId, int? clientId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Get_GI_Barcode_Scan_Status", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 90;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails()
                    {
                        GIBarcodeScanStatusId = GetReaderValue_String(reader, "ID", 0),
                        GIBarcodesScanStatusName = GetReaderValue_String(reader, "Name", "")
                    };
                    lst.Add(obj);
                    obj = null;
                }
                return lst;

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get GoodsInLine query messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        #endregion

        #region RP-2546
        public override GoodsInLineDetails GetGiLineReleaseStatus(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GoodsInLineReleaseStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetGoodsInLineFromReader(reader);
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.InspectedBy = GetReaderValue_Int32(reader, "InspectedBy", 0);
                    obj.DateInspected = GetReaderValue_DateTime(reader, "DateInspected", DateTime.MinValue);
                    obj.AlertMessage = GetReaderValue_String(reader, "AlertMessage", "");
                    obj.ISCloseInspection = GetReaderValue_Boolean(reader, "ISCloseInspection", false);
                    //[007] code end
                    //code end for ihs
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        public override GoodsInLineDetails GetGiLineInspectionStatus(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GoodsInInspectionReleaseStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetGoodsInLineFromReader(reader);
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.InspectionStatus = GetReaderValue_Int32(reader, "InspectionStatus", 0);
                    obj.InspectedBy = GetReaderValue_Int32(reader, "InspectedBy", 0);
                    obj.DateInspected = GetReaderValue_DateTime(reader, "DateInspected", DateTime.MinValue);
                    obj.AlertMessage = GetReaderValue_String(reader, "AlertMessage", "");
                    obj.ISCloseInspection = GetReaderValue_Boolean(reader, "ISCloseInspection", false);
                    //[007] code end
                    //code end for ihs
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get GoodsInLine", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        #endregion
        /// <summary>
        /// Get 
        /// Calls [usp_select_GoodsInLineSerialNo]
        /// </summary>
        public override List<GoodsInLineDetails> GetSerialNoForSplit(System.Int32? goodsInLineId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_GoodsInLineSerialNo", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@GoodsInLineId", SqlDbType.Int).Value = goodsInLineId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<GoodsInLineDetails> lst = new List<GoodsInLineDetails>();
                while (reader.Read())
                {
                    GoodsInLineDetails obj = new GoodsInLineDetails();
                    obj.SerialId = GetReaderValue_Int32(reader, "SerialId", 0);
                    obj.Box = GetReaderValue_String(reader, "Box", "");
                    obj.SerialNumber = GetReaderValue_String(reader, "SerialNumber", null);


                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Serial Details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


    }
}
