/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-209544]     CuongDox		 17-Sep-2024		CREATE		Add permission
===========================================================================================  
*/
IF NOT EXISTS (
    SELECT 1 
    FROM tbSecurityFunction 
    WHERE SecurityFunctionId = 7000010
)
BEGIN
	INSERT INTO tbSecurityFunction (
		SecurityFunctionId,
		FunctionName,
		Description,
		SitePageNo,
		SiteSectionNo,
		ReportNo,
		UpdatedBy,
		DLUP,
		InitiallyProhibitedForNewLogins,
		DisplaySortOrder
	)
	VALUES (
		 7000010,                -- SecurityFunctionId
		'Utility_HUBOfferImportLarge',            -- FunctionName
		'Allow viewing Bulk Offer Scheduled Import Tool', -- Description
		NULL,                      -- SitePageNo
		8,                        -- SiteSectionNo
		NULL,                     -- ReportNo (NULL because it's not related to a report)
		NULL,                     -- UpdatedBy (ID of the user who updated this function)
		GETDATE(),                -- DLUP (current date and time)
		0,                        -- InitiallyProhibitedForNewLogins (0 = false, 1 = true)
		6                        -- DisplaySortOrder
	);
END