///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//Marker     Changed by      Date               Remarks
//[001]      Vinay           12/06/2013         CR:- Client Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo = function(element) {
Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.initializeBase(this, [element]);
    this._intClientInvoiceID = -1;
    this._strClientInvoiceNumber = -1;
    this._intInvoiceClientNo = -1;
    this._isPOHUb = false;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.prototype = {

    get_intClientInvoiceID: function() { return this._intClientInvoiceID; }, set_intClientInvoiceID: function(value) { if (this._intClientInvoiceID !== value) this._intClientInvoiceID = value; },
    get_ibtnEdit: function() { return this._ibtnEdit; }, set_ibtnEdit: function(value) { if (this._ibtnEdit !== value) this._ibtnEdit = value; },
    get_ibtnNotify: function() { return this._ibtnNotify; }, set_ibtnNotify: function(value) { if (this._ibtnNotify !== value) this._ibtnNotify = value; },
    get_blnCanEditMainInfo: function() { return this._blnCanEditMainInfo; }, set_blnCanEditMainInfo: function(value) { if (this._blnCanEditMainInfo !== value) this._blnCanEditMainInfo = value; },
    get_blnCanEditURNNumber: function() { return this._blnCanEditURNNumber; }, set_blnCanEditURNNumber: function(value) { if (this._blnCanEditURNNumber !== value) this._blnCanEditURNNumber = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.callBaseMethod(this, "initialize");

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.getData));


        //setup forms and their events
        if (this._ibtnEdit) {
            $R_IBTN.addClick(this._ibtnEdit, Function.createDelegate(this, this.showEditForm));
            this._frmEdit = $find(this._aryFormIDs[0]);
            this._frmEdit._intClientInvoiceID = this._intClientInvoiceID;
            this._frmEdit.addCancel(Function.createDelegate(this, this.cancelEdit));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
        }

        //notify form
        if (this._ibtnNotify) {
            $R_IBTN.addClick(this._ibtnNotify, Function.createDelegate(this, this.showNotifyForm));
            this._frmNotify = $find(this._aryFormIDs[1]);
            this._frmNotify.addCancel(Function.createDelegate(this, this.cancelNotifyForm));
            this._frmNotify.addSaveComplete(Function.createDelegate(this, this.saveNotifyComplete));
            this._frmNotify.addNotConfirmed(Function.createDelegate(this, this.hideNotifyForm));
        }
        this.getData();

    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ibtnEdit) $R_IBTN.clearHandlers(this._ibtnEdit);
        if (this._ibtnNotify) $R_IBTN.clearHandlers(this._ibtnNotify);
        if (this._frmEdit) this._frmEdit.dispose();
        if (this._frmNotify) this._frmNotify.dispose();
        this._ibtnEdit = null;
        this._ibtnNotify = null;
        this._frmEdit = null;
        this._frmNotify = null;
        this._intClientInvoiceID = null;
        this._strClientInvoiceNumber = null;
        this._blnCanEditMainInfo = null;
        this._blnCanEditURNNumber = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.callBaseMethod(this, "dispose");
    },

    getData: function() {
        this.getData_Start();
        this.enableButtons(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");
        obj.set_DataObject("ClientInvoiceMainInfo");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intClientInvoiceID);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataOK: function(args) {
        var res = args._result;
        this.setFieldValue("ctlClientInvoice", $R_FN.setCleanTextValue(res.ClientInvoiceNumber));
        //this.setFieldValue("ctlSupplierCode", $R_FN.setCleanTextValue(res.SupplierCode));
        this.setFieldValue("ctlInvoiceDate", res.ClientInvoiceDate);
        this.setFieldValue("ctlCurrency", res.CurrencyCode);
        this.setFieldValue("ctlInvoiceAmount", res.InvoiceAmount);
        this.setFieldValue("ctlGoodsValue", res.GoodsValue);
        this.setFieldValue("ctlTax", res.Tax);
        this.setFieldValue("ctlTaxName", res.TaxName);
        this.setFieldValue("ctlPOOrder", res.Receiver);
        this.setFieldValue("ctlDeliveryCharge", res.DeliveryCharge);
        this.setFieldValue("ctlBankFee", res.BankFee);
        this.setFieldValue("ctlCreditCardFee", res.CreditCardFee);
        this.setFieldValue("ctlNotes", res.Notes);
//        this.setFieldValue("ctlExported", res.Exported);
//        this.setFieldValue("ctlCanBeExported", res.CanbeExported);
//        this.setFieldValue("ctlURNNumber", res.URNNumber);
        this.setFieldValue("ctlPOOrder", $RGT_nubButton_PurchaseOrder(res.PONo, res.PO));
        
        if (res.InternalPurchaseOrderNo > 0) {
            this.setFieldValue("ctlInternalPurchaseOrder", $RGT_nubButton_InternalPurchaseOrder(res.InternalPurchaseOrderNo, res.InternalPurchaseOrderNumber));
        }
        else {
            this.showField("ctlInternalPurchaseOrder", false);
        }
        this.setFieldValue("hidSupplier", res.SupplierName);
        this.setFieldValue("hidCompnayNo", res.CompanyNo);
        this.setFieldValue("hidCurrency", res.CurrencyNo);
        this.setFieldValue("hidInvoiceAmount", res.InvoiceAmountValue);
        this.setFieldValue("hidGoodsInValue", res.GoodsValueValue);
        this.setFieldValue("hidTax", res.TaxValue);
        this.setFieldValue("hidDeliveryCharges", res.DeliveryChargeValue);
        this.setFieldValue("hidBankFee", res.BankFeeValue);
        this.setFieldValue("hidCreditCardFee", res.CreditCardFeeValue);
        this.setFieldValue("hidTaxId", res.TaxNo);
        this.setFieldValue("ctlSecondRef", $R_FN.setCleanTextValue(res.SecondRef));
        this.setFieldValue("ctlNarrative", $R_FN.setCleanTextValue(res.Narrative));
        this._strClientInvoiceNumber = res.ClientInvoiceNumber;
        this._intInvoiceClientNo = res.InvoiceClientNo
        this._isPOHUb = res.IsPOHub;
        this.setDLUP(res.DLUP);
        this.enableButtons(true);
        this.getDataOK_End();
    },

    getDataError: function(args) {
        this.showError(true, args.get_ErrorMessage());
    },

    enableButtons: function(bln) {
        if (bln) {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, bln && this._blnCanEditMainInfo && this._isPOHUb);
            if (this._ibtnNotify) $R_IBTN.enableButton(this._ibtnNotify, true);
        } else {
            if (this._ibtnEdit) $R_IBTN.enableButton(this._ibtnEdit, false);
            if (this._ibtnNotify) $R_IBTN.enableButton(this._ibtnNotify, false);
        }
    },

    showEditForm: function () {
       // alert("hi");
        //this._frmEdit.getFieldDropDownData("ctlCurrency");
        //this._frmEdit.getFieldDropDownData("ctlddlTax");
        this._frmEdit.setFieldValue("ctlSupplier", this.getFieldValue("hidSupplier"));
        //this._frmEdit.setFieldValue("ctlSupplierCode", this.getFieldValue("ctlSupplierCode"));
        this._frmEdit.setFieldValue("ctlClientInvoice", this.getFieldValue("ctlClientInvoice"));
        this._frmEdit.setFieldValue("ctlInvoiceDate", this.getFieldValue("ctlInvoiceDate"));
        this._frmEdit.setFieldValue("ctlCurrency", this.getFieldValue("hidCurrency"));
        this._frmEdit.setFieldValue("ctlInvoiceAmount", this.getFieldValue("hidInvoiceAmount"));
        this._frmEdit.setFieldValue("ctlGoodsValue", this.getFieldValue("hidGoodsInValue"));
        this._frmEdit.setFieldValue("ctlTax", this.getFieldValue("hidTax"));
        this._frmEdit.setFieldValue("ctlDeliveryCharge", this.getFieldValue("hidDeliveryCharges"));
        this._frmEdit.setFieldValue("ctlBankFee", this.getFieldValue("hidBankFee"));
        this._frmEdit.setFieldValue("ctlCreditCardFee", this.getFieldValue("hidCreditCardFee"));
        //this._frmEdit.setFieldValue("ctlCanExported", this.getFieldValue("ctlCanBeExported"));
        this._frmEdit.setFieldValue("ctlNotes",$R_FN.setCleanTextValue( this.getFieldValue("ctlNotes")));
        this._frmEdit.setFieldValue("ctlddlTax", this.getFieldValue("hidTaxId"));
        this._frmEdit.setFieldValue("ctlSecondRef", $R_FN.setCleanTextValue(this.getFieldValue("ctlSecondRef")));
        this._frmEdit.setFieldValue("ctlNarrative", $R_FN.setCleanTextValue( this.getFieldValue("ctlNarrative")));
       // this._frmEdit.setFieldValue("ctlURNNumber", this.getFieldValue("ctlURNNumber"));
        var code = this.getFieldValue("ctlCurrency");
        //this._frmEdit.showField("ctlCanExported", (!this.getFieldValue("ctlExported")));
      //  this._frmEdit.showField("ctlURNNumber", this._blnCanEditURNNumber && this.getFieldValue("ctlExported") && this.getFieldValue("ctlCanBeExported"));
        this._frmEdit._TaxNo = this.getFieldValue("hidTaxId");
        this._frmEdit._CurrencyCode = code;
        $R_FN.setInnerHTML(this._frmEdit._lblCurrency_InvoiceAmount, code);
        $R_FN.setInnerHTML(this._frmEdit._lblCurrency_GoodsInValue, code);
        $R_FN.setInnerHTML(this._frmEdit._lblCurrency_Tax, code);
        $R_FN.setInnerHTML(this._frmEdit._lblCurrency_DeliveryCharge, code);
        $R_FN.setInnerHTML(this._frmEdit._lblCurrency_BankFee, code);
        $R_FN.setInnerHTML(this._frmEdit._lblCurrency_CreditCardFee, code);
        this.showForm(this._frmEdit, true);
        code = null;
    },

    hideEditForm: function() {
        this.showForm(this._frmEdit, false);
    },

    cancelEdit: function() {
        this.hideEditForm();
    },

    saveEditComplete: function() {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getData();
    },

    showNotifyForm: function() {
        this._frmNotify._intClientInvoiceID = this._intClientInvoiceID;
        this._frmNotify._intCompanyID = this.getFieldValue("hidCompnayNo");
        this._frmNotify._strClientInvoiceNumber = this._strClientInvoiceNumber;
        this.showForm(this._frmNotify, true);
    },

    hideNotifyForm: function() {
        this.showForm(this._frmNotify, false);
    },

    cancelNotifyForm: function() {
        this.showForm(this._frmNotify, false);
        this.showContent(true);
    },

    saveNotifyComplete: function() {
        this.showForm(this._frmNotify, false);
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    }

};

Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ClientInvoiceMainInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
