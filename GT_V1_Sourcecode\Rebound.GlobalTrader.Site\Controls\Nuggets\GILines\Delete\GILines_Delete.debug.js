///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Delete = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.GILines_Delete.initializeBase(this, [element]);
	this._intLineID = -1;
	//locals
	this._intGIID = 0;
	this._ctlConfirm = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Delete.prototype = {

	get_intGIID: function() { return this._intGIID; }, 	set_intGIID: function(v) { if (this._intGIID !== v)  this._intGIID = v; }, 
	get_intLineID: function() { return this._intLineID; }, 	set_intLineID: function(value) { if (this._intLineID !== value)  this._intLineID = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.GILines_Delete.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	    this.getGoodsIn();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._intLineID = null;
		this._intGIID = null;
		this._ctlConfirm = null;
		Rebound.GlobalTrader.Site.Controls.Forms.GILines_Delete.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
	},

	getGoodsIn: function() {
		$R_FN.showElement(this._pnlLines, false);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/GIMainInfo");
		obj.set_DataObject("GIMainInfo");
		obj.set_DataAction("GetData");
		obj.addParameter("ID", this._intGIID);
		obj.addDataOK(Function.createDelegate(this, this.getGoodsInOK));
		obj.addError(Function.createDelegate(this, this.getGoodsInError));
		obj.addTimeout(Function.createDelegate(this, this.getGoodsInError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getGoodsInOK: function(args) {
		var res = args._result;
		this.setFieldsFromGoodsIn(res);
	},
	
	setFieldsFromGoodsIn: function(res) {
		if (!res) return;
		this.setFieldValue("ctlGoodsIn", res.GoodsInNumber);
		this.setFieldValue("ctlSupplier", res.SupplierName);
	},

	getGoodsInError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	yesClicked: function() {
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/GILines");
		obj.set_DataObject("GILines");
		obj.set_DataAction("Delete");
		obj.addParameter("id", this._intLineID);
		obj.addDataOK(Function.createDelegate(this, this.saveComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function() {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function(args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function(args) {
		this.showSaving(false);
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Delete", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
