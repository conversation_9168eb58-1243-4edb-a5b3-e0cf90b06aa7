///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

/*Marker     Changed by      Date         Remarks
//[001]      Aashu Singh     27-Nov-2018  Show customer requirement all info in tree. 
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.initializeBase(this, [element]);
    this._DocNo = "";
    this._actionType = "";
    this._DocId = "";
    this.ReqDetailURL = 'Ord_CusReqDetail.aspx?req=';
    this.BOMDetailURL = 'Ord_BOMDetail.aspx?BOM=';
    this.QuoteDetailURL = 'Ord_QuoteDetail.aspx?qt=';
    this.SODetailURL = 'Ord_SODetail.aspx?so=';
    this.InvDetailURL = 'Ord_InvoiceDetail.aspx?inv=';
    this.PODetailURL = 'Ord_PODetail.aspx?po=';
    this.GIDetailURL = 'Whs_GIDetail.aspx?gi=';
    this.STKDetailURL = 'Whs_StockDetail.aspx?stk=';
    this.CRMADetailURL = 'Ord_CRMADetail.aspx?crma=';
    this.SRMADetailURL = 'Ord_SRMADetail.aspx?srma=';
    this.CreditDetailURL = 'Ord_CreditNoteDetail.aspx?crd=';
    this.DebitDetailURL = 'Ord_DebitNoteDetail.aspx?deb=';

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.prototype = {

    get_intDocNo: function () { return this._DocNo; }, set_intDocNo: function (value) { if (this._DocNo !== value) this._DocNo = value; },
    get_actionType: function () { return this._actionType; }, set_actionType: function (value) { if (this._actionType !== value) this._actionType = value; },
    get_intDocId: function () { return this._DocId; }, set_intDocId: function (value) { if (this._DocId !== value) this._DocId = value; },
    get_pnlCustReqAllDoc: function () { return this._pnlCustReqAllDoc; }, set_pnlCustReqAllDoc: function (value) { if (this._pnlCustReqAllDoc !== value) this._pnlCustReqAllDoc = value; },
    get_pnlLoadingLineDetail: function () { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function (v) { if (this._pnlLoadingLineDetail !== v) this._pnlLoadingLineDetail = v; },

    addStartGetData: function (handler) { this.get_events().addHandler("StartGetData", handler); },
    removeStartGetData: function (handler) { this.get_events().removeHandler("StartGetData", handler); },
    onStartGetData: function () {
        var handler = this.get_events().getHandler("StartGetData");
        if (handler) handler(this, Sys.EventArgs.Empty);
        alert(1);
    },

    addGotDataOK: function (handler) { this.get_events().addHandler("GotDataOK", handler); },
    removeGotDataOK: function (handler) { this.get_events().removeHandler("GotDataOK", handler); },
    onGotDataOK: function () {
        var handler = this.get_events().getHandler("GotDataOK");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.callBaseMethod(this, "initialize");
        this.getData();
    },
    dispose: function () {
        if (this.isDisposed) return;
        this._DocNo = null;
        this._pnlCustReqAllDoc = null;
        this._pnlLoadingLineDetail = null;
        this._DocId = null;
        Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.callBaseMethod(this, "dispose");
    },

    getData: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        this.showLoading(true);
        $R_FN.showElement(this._pnlLineDetail, false);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        obj.set_PathToData("controls/Nuggets/CusReqMainInfo");
        obj.set_DataObject("CusReqMainInfo");
        obj.set_DataAction("CustomerAllInfo");
        obj.addParameter("DocNo", this._DocNo);
        obj.addParameter("ActionType", this._actionType);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getDataOK: function (args) {
        var allResult = args._result;
        $R_FN.setInnerHTML(this._pnlCustReqAllDoc, this.generateLink(allResult.Items));
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.showElement(this._pnlContentLoading, false);
        $R_FN.showElement(this._pnlContent, true);
        this.showLoading(false);
        this.onGotDataOK();

    },
    generateLink: function (collection) {
        var docDesc = '';
        switch (this._actionType) {
            case 'REQ': docDesc = 'Requirement (' + this._DocId + ')'; break;
            case 'BOM': docDesc = 'HUBRFQ (' + this._DocId + ')'; break;
            case 'Q': docDesc = 'Quotes (' + this._DocId + ')'; break;
            case 'SO': docDesc = 'Sales Order (' + this._DocId + ')'; break;
            case 'INV': docDesc = 'Invoice (' + this._DocId + ')'; break;
            case 'PO': docDesc = 'Purchase Order (' + this._DocId + ')'; break;
            case 'GI': docDesc = 'Goods In (' + this._DocId + ')'; break;
            case 'STK': docDesc = 'Stock (' + this._DocId + ')'; break;
            case 'CRMA': docDesc = 'CRMA (' + this._DocId + ')'; break;
            case 'SRMA': docDesc = 'SRMA (' + this._DocId + ')'; break;
            case 'CRD': docDesc = 'Credit Note (' + this._DocId + ')'; break;
            case 'DBT': docDesc = 'Debit Note (' + this._DocId + ')'; break;
        }
        var divContent = '<ul><input class="toggle-box" id="identifier-1" type="checkbox" checked><label for="identifier-1">All Document Detail For ' + docDesc + '</label><div>';
        //Create Requirement Link
        if (this._actionType != 'REQ') {
            var reqCollection = this.filter(collection, "REQ");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-2" type="checkbox" checked><label for="identifier-2">Requirement</label> <div>';
            for (var j = 0; j < reqCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.ReqDetailURL + reqCollection[j].ID + '" target="_blank">' + reqCollection[j].Number + '</a></li>';
            }
            divContent = divContent + " </div></ul>";
        }
        //Create HUBRFQ Link
        if (this._actionType != 'BOM') {
            var bomCollection = this.filter(collection, "BOM");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-3" type="checkbox" checked><label for="identifier-3">HUBRFQ</label> <div>';
            for (var j = 0; j < bomCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.BOMDetailURL + bomCollection[j].ID + '" target="_blank">' + bomCollection[j].Number + '</a></li>';
            }
            divContent = divContent + " </div></ul>";
        }
        //Create Quote Link
        if (this._actionType != 'Q') {
            var quoteCollection = this.filter(collection, "Q");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-4" type="checkbox" checked><label for="identifier-4">Quote</label> <div>';
            for (var j = 0; j < quoteCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.QuoteDetailURL + quoteCollection[j].ID + '" target="_blank">' + quoteCollection[j].Number + '</a></li>';
            }
            divContent = divContent + " </div></ul>";
        }
        //Create SO Link
        if (this._actionType != 'SO') {
            var soCollection = this.filter(collection, "SO");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-5" type="checkbox" checked><label for="identifier-5">Sales Order</label><div> ';
            for (var j = 0; j < soCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.SODetailURL + soCollection[j].ID + '" target="_blank">' + soCollection[j].Number + '</a></li> ';
            }
            divContent = divContent + "</div> </ul>";
        }
        //Create Invoice Link
        if (this._actionType != 'INV') {
            var invCollection = this.filter(collection, "INV");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-6" type="checkbox" checked><label for="identifier-6">Invoice</label><div> ';
            for (var j = 0; j < invCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.InvDetailURL + invCollection[j].ID + '" target="_blank">' + invCollection[j].Number + '</a></li>';
            }
            divContent = divContent + "</div></ul>";
        }
        //Create Purchase Order Link
        if (this._actionType != 'PO') {
            var poCollection = this.filter(collection, "PO");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-7" type="checkbox" checked><label for="identifier-7">Purchase Order</label><div> ';
            for (var j = 0; j < poCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.PODetailURL + poCollection[j].ID + '" target="_blank">' + poCollection[j].Number + '</a></li>';
            }
            divContent = divContent + "</div></ul>";
        }
        //Create GoodsIn Link
        if (this._actionType != 'GI') {
            var giCollection = this.filter(collection, "GI");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-8" type="checkbox" checked><label for="identifier-8">Goods In</label><div> ';
            for (var j = 0; j < giCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.GIDetailURL + giCollection[j].ID + '" target="_blank">' + giCollection[j].Number + '</a></li>';
            }
            divContent = divContent + "</div></ul>";
        }
        //Create Stock Link
        if (this._actionType != 'STK') {
            var stkCollection = this.filter(collection, "STK");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-9" type="checkbox" checked><label for="identifier-9">Stock</label><div> ';
            for (var j = 0; j < stkCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.STKDetailURL + stkCollection[j].ID + '" target="_blank">' + stkCollection[j].Number + '</a></li>';
            }
            divContent = divContent + "</div></ul>";
        }
        //Create CRMA Link
        if (this._actionType != 'CRMA') {
            var crmaCollection = this.filter(collection, "CRMA");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-10" type="checkbox" checked><label for="identifier-10">CRMA</label><div> ';
            for (var j = 0; j < crmaCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.CRMADetailURL + crmaCollection[j].ID + '" target="_blank">' + crmaCollection[j].Number + '</a></li>';
            }
            divContent = divContent + "</div></ul>";
        }
        //Create SRMA Link
        if (this._actionType != 'SRMA') {
            var srmaCollection = this.filter(collection, "SRMA");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-11" type="checkbox" checked><label for="identifier-11">SRMA</label><div> ';
            for (var j = 0; j < srmaCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.SRMADetailURL + srmaCollection[j].ID + '" target="_blank">' + srmaCollection[j].Number + '</a></li>';
            }
            divContent = divContent + "</div></ul>";
        }
        //Create Credit Note Link
        if (this._actionType != 'CRD') {
            var crdCollection = this.filter(collection, "CRDT");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-12" type="checkbox" checked><label for="identifier-12">Credit Note</label><div> ';
            for (var j = 0; j < crdCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.CreditDetailURL + crdCollection[j].ID + '" target="_blank">' + crdCollection[j].Number + '</a></li>';
            }
            divContent = divContent + "</div></ul>";
        }
        //Create Debit Note Link
        if (this._actionType != 'DBT') {
            var dbtCollection = this.filter(collection, "DBT");
            divContent = divContent + '<ul><input class="toggle-box" id="identifier-13" type="checkbox" checked><label for="identifier-13">Debit Note</label><div> ';
            for (var j = 0; j < dbtCollection.length; j++) {
                divContent = divContent + "<li>" + '<a href="' + this.DebitDetailURL + dbtCollection[j].ID + '" target="_blank">' + dbtCollection[j].Number + '</a></li>';
            }
            divContent = divContent + "</div></ul>";
        }

        divContent = divContent + '</div> </ul>'

        return divContent;
    },
    filter: function (collection, predicate) {
        var result = new Array();
        var length = collection.length;

        for (var j = 0; j < length; j++) {
            if (collection[j].ResultType == predicate) {
                result.push(collection[j]);
            }
        }

        return result;
    },
    getDataError: function (args) {
        this.showLoading(false);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.showElement(this._pnlLineDetailError, true);
        $R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
    }

};

Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CustReqAllInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base, Sys.IDisposable);
