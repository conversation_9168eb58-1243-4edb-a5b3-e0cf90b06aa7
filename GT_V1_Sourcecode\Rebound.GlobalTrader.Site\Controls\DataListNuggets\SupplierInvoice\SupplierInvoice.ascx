<%--
Marker     Changed by      Date               Remarks
[001]      Vinay           11/06/2013         CR:- Supplier Invoice
--%>
<%@ Control Language="C#" CodeBehind="SupplierInvoice.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.SupplierInvoice" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Links>
        <ReboundUI:IconButton ID="btnExportToCSV" runat="server" Style="margin-left:8px;" IconButtonMode="hyperlink" IconGroup="Nugget" IconTitleResource="SaveForExport" IconCSSType="Add"/>
    </Links>

	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
			    <ReboundUI_FilterDataItemRow:TextBox id="ctlInvoiceNo" runat="server" ResourceTitle="SupplierInvoice" FilterField="SupplierInvoiceNo" />
			    <ReboundUI_FilterDataItemRow:Numerical id="ctlURNNo" runat="server" ResourceTitle="URNNo" FilterField="URNNo" />
			    <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
			    <ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
			    <ReboundUI_FilterDataItemRow:DropDown id="ctlStatus" runat="server" IncludeNoValue="false" ResourceTitle="ImportStatus" DropDownType="SupplierInvoiceStatus" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Status" />
                <ReboundUI_FilterDataItemRow:DropDown id="ctlStatusReason" runat="server" IncludeNoValue="true" ResourceTitle="StatusReason" DropDownType="StatusReason" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Reason" />
                 <ReboundUI_FilterDataItemRow:CheckBox id="ctlOCR" runat="server" ResourceTitle="OCRGEN" FilterField="OCR" DefaultValue="False" />
                 <ReboundUI_FilterDataItemRow:CheckBox id="ctlDescrepancy" runat="server" ResourceTitle="SIDescrepancy" FilterField="Descrepancy" DefaultValue="False" />

                	<%--<ReboundUI_FilterDataItemRow:DropDown id="ctllstCountry" runat="server"  ResourceTitle="OCR" DropDownType="OCR" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="OCR" />--%>
			</FieldsLeft>
			<FieldsRight>
			    <ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo"  TextBoxMaxLength="10"  />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlGINo" runat="server" ResourceTitle="GoodsIn" FilterField="GINo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlSupplierInvoiceDateFrom" runat="server" ResourceTitle="SupplierInvoiceDateFrom" FilterField="SupplierInvoiceDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlSupplierInvoiceDateTo" runat="server" ResourceTitle="SupplierInvoiceDateTo" FilterField="SupplierInvoiceDateTo" />
		    	<ReboundUI_FilterDataItemRow:Numerical id="ctlIPO" runat="server" ResourceTitle="InternalPurchaseOrderNo" FilterField="IPONo" />
            </FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>

    <script type="text/javascript">
        $(document).ready(function () {
            //$('#SelectALLSupplierInvoice').val(this.checked);

            $('.SupplierInvoiceCheckbox').change(function () {
                debugger;
                if ($(this).prop("checked")) {
                    $(".SupplierInvoiceCheckbox [value='" + $(this).prop("value") + "']").prop("checked", true);
                }
                else {
                    $("[.SupplierInvoiceCheckbox value='" + $(this).prop("value") + "']").prop("checked", false);
                }
            });

            $('#SelectALLSupplierInvoice').change(function () {
                if (this.checked) {
                    $(".SupplierInvoiceCheckbox").not(":disabled").each(function () {
                        $(this).prop("checked", true);
                    });
                }
                else {
                    $(".SupplierInvoiceCheckbox").not(":disabled").each(function () {
                        $(this).prop("checked", false);
                    });
                }
            });


            $('#ctl00_cphMain_ctlSupplierInvoice_ctlDB_ctl12_btnExportToCSV_hyp').click(function () {
                var SupplierInvoiceIdList = "";

                $(".SupplierInvoiceCheckbox:checkbox:checked").not(":disabled").each(function () {
                    SupplierInvoiceIdList += $(this).val() + ",|,";
                });

                if (SupplierInvoiceIdList == ",|," || SupplierInvoiceIdList == "") {
                    alert("Please Select at least one of the Rows to Proceed with Save For Export");
                    return;
                }
                else {
                    if (confirm("This will update Ready To Export Status of the selected Supplier Invoices\nDo You Want To Continue") == true) {
                        var formData = new FormData();
                        formData.append('action', 'SaveForExport');
                        formData.append('SupplierInvoiceIdList', SupplierInvoiceIdList);

                        $.ajax({
                            processData: true,
                            type: 'POST',
                            url: 'controls/Nuggets/SupplierInvoiceMainInfo/SupplierInvoiceMainInfo.ashx',
                            data: formData,
                            processData: false,
                            contentType: false,
                            success: function (data) {
                                location.reload(true);
                            },
                            error: function (err) {
                                alert('Error in Updating Export Status for Selected Supplier Invoices');

                            }
                        });
                    }
                    else {
                        return;
                    }
                }
            });

        });
    </script>