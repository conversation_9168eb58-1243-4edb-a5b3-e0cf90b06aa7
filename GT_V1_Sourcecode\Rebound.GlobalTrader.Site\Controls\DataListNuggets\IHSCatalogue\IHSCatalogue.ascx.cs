//----------------------------------------------------------------------------------------------------
// RP 23.12.2009:
// - render state on server
//
// RP 15.12.2009:
// - allow ordering by PO Delivery Date (task 171)
//----------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
    public partial class IHSCatalogue : Base
    {
        #region Locals
        protected IconButton _ibtnExportCSV;//[001]   
        #endregion

        #region Properties

        private bool _blnShowUninspectedOnly = false;
        public bool ShowUninspectedOnly {
            get { return _blnShowUninspectedOnly; }
            set { _blnShowUninspectedOnly = value; }
        }

        private bool _IsGlobalLogin = false;
        public bool IsGlobalLogin
        {
            get { return _IsGlobalLogin; }
            set { _IsGlobalLogin = value; }
        }

        private bool _blnAllowExportCSV = true;
        public bool CanExportCSV
        {
            get { return _blnAllowExportCSV; }
            set { _blnAllowExportCSV = value; }
        }

        private bool _blnAllowToShowAvgPrice = true;
        public bool AllowToShowAvgPrice
        {
            get { return _blnAllowToShowAvgPrice; }
            set { _blnAllowToShowAvgPrice = value; }
        }

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
            //IsGlobalLogin = true;//Call here Global Security.
            SetDataListNuggetType("IHSCatalogue");
            base.OnInit(e);
            SecurityManager obj = new SecurityManager(BLL.SiteSection.List.Warehouse, (int)SessionManager.LoginID);
            AllowToShowAvgPrice = obj.CheckSectionLevelPermission(BLL.SecurityFunction.List.Warehouse_IHS_Allow_View_AvgPrice);
            CanExportCSV = obj.CheckSectionLevelPermission(BLL.SecurityFunction.List.Warehouse_IHS_Allow_Export_CSV);
            obj = null;
            WireUpControls();//[001]
            TitleText = Functions.GetGlobalResource("Nuggets", "IHSCatalogue");
            AddScriptReference("Controls.DataListNuggets.IHSCatalogue.IHSCatalogue");
            IsGlobalLogin = SessionManager.IsGlobalUser.Value;
            _ibtnExportCSV.Visible = _blnAllowExportCSV;

            SetupTable();
        }

		protected override void OnLoad(EventArgs e) {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.IHSCatalogue", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            _scScriptControlDescriptor.AddProperty("AllowAvgPrice", _blnAllowToShowAvgPrice);

            if (!IsPostBack)
            {
                var session = Session["mfr"];
                if (session != null && !string.IsNullOrEmpty(session.ToString()))
                {
                    _scScriptControlDescriptor.AddProperty("redirectFromMFR", session);
                    Session.Remove("mfr");
                }
            }
            if (_blnAllowExportCSV) _scScriptControlDescriptor.AddElementProperty("ibtnExportCSV", _ibtnExportCSV.ClientID);
			base.OnLoad(e);
		}
		protected override void RenderAdditionalState() {
			int intTab = 0;
			if (string.IsNullOrEmpty(_objQSManager.SearchPartNo)) {
				var strCallType = this.GetSavedStateValue("CallType").ToUpper();
				if (string.IsNullOrEmpty(strCallType)) strCallType = "ALL";
				if (strCallType == "ALL") intTab = 0;
				if (strCallType == "UNINSPECTED") intTab = 1;
			}
			this._blnShowUninspectedOnly = (intTab == 1);
			((Pages.Content)Page).CurrentTab = intTab;
			this.OnAskPageToChangeTab();
			base.RenderAdditionalState();
		}

		protected override void OnPreRender(EventArgs e) {
			base.OnPreRender(e);
			//_scScriptControlDescriptor.AddProperty("blnShowUninspectedOnly", _blnShowUninspectedOnly);
		}

        #endregion
        private void WireUpControls()
        {
            _ibtnExportCSV = (IconButton)FindIconButton("ibtnExportCSV");

        }
        private void SetupTable() {
            _tbl.AllowSelection = false;
            _tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
            _tbl.Columns.Add(new FlexiDataColumn("PartNo", "PartStatus"));
            _tbl.Columns.Add(new FlexiDataColumn("Manufacturer", "MSL", Unit.Pixel(70),true));
            _tbl.Columns.Add(new FlexiDataColumn("CountryOfOrigin", "HTSCode", Unit.Pixel(90)));
            _tbl.Columns.Add(new FlexiDataColumn("Packaging", "PackagingCode", Unit.Pixel(90), true));
            if (SessionManager.IsPOHub == true && _blnAllowToShowAvgPrice == true)
            {
                _tbl.Columns.Add(new FlexiDataColumn("GlobalAvgPrice", "Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            }
            else
            {
                _tbl.Columns.Add(new FlexiDataColumn("Date", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
            }
            //_tbl.Columns.Add(new FlexiDataColumn("Description", Unit.Pixel(350),true));
            _tbl.Columns.Add(new FlexiDataColumn("Description","ECCNCode", Unit.Pixel(400),true));
            _tbl.Columns.Add(new FlexiDataColumn("PDFDocument", "PDFIHSDOCtitle", Unit.Pixel(90), true));
            _tbl.Columns.Add(new FlexiDataColumn("APIImportedData", Unit.Pixel(90)));
        }
    }
}