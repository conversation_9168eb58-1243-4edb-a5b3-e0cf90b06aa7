<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Azure.Storage.File</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Azure.Storage.File.CloudFile">
            <summary>
            Represents a Microsoft Azure File.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.OpenReadAsync">
            <summary>
            Opens a stream for reading from the file.
            </summary>
            <returns>A stream to be used for reading from the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.OpenReadAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Opens a stream for reading from the file.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A stream to be used for reading from the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.OpenReadAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Opens a stream for reading from the file.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A stream to be used for reading from the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.OpenWriteAsync(System.Nullable{System.Int64})">
            <summary>
            Opens a stream for writing to the file. If the file already exists, then existing data in the file may be overwritten.
            </summary>
            <param name="size">The size of the write operation, in bytes.</param>
            <returns>A stream to be used for writing to the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.OpenWriteAsync(System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Opens a stream for writing to the file. If the file already exists, then existing data in the file may be overwritten.
            </summary>
            <param name="size">The size of the write operation, in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A stream to be used for writing to the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.OpenWriteAsync(System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Opens a stream for writing to the file.
            </summary>
            <param name="size">The size of the write operation, in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A stream to be used for writing to the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsync(System.IO.Stream)">
            <summary>
            Uploads a stream to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="source">The stream providing the file content.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsync(System.IO.Stream,System.Int64)">
            <summary>
            Uploads a stream to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="length">The number of bytes to write from the source stream at its current position.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsync(System.IO.Stream,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Uploads a stream to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsync(System.IO.Stream,System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Uploads a stream to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="length">The number of bytes to write from the source stream at its current position.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsync(System.IO.Stream,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Uploads a stream to a file. 
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsync(System.IO.Stream,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Uploads a stream to a file. 
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsync(System.IO.Stream,System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Uploads a stream to a file. 
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="length">The number of bytes to write from the source stream at its current position.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsync(System.IO.Stream,System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Uploads a stream to a file. 
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="length">The number of bytes to write from the source stream at its current position.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsyncHelper(System.IO.Stream,System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Uploads a stream to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="length">The number of bytes to write from the source stream at its current position.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsyncHelper(System.IO.Stream,System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Uploads a stream to a file. 
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="length">The number of bytes to write from the source stream at its current position.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromStreamAsyncHelper(System.IO.Stream,System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Uploads a stream to a file. 
            </summary>
            <param name="source">The stream providing the file content.</param>
            <param name="length">The number of bytes to write from the source stream at its current position.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromFileAsync(System.String)">
            <param name="path">A string containing the path to the target file.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromFileAsync(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <param name="path">A string containing the path to the target file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromFileAsync(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Uploads a file to the Azure File Service. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="path">A string containing the path to the target file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromFileAsync(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Uploads a file to the Azure File Service. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="path">A string containing the path to the target file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromByteArrayAsync(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Uploads the contents of a byte array to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="buffer">An array of bytes.</param>
            <param name="index">The zero-based byte offset in buffer at which to begin uploading bytes to the file.</param>
            <param name="count">The number of bytes to be written to the file.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromByteArrayAsync(System.Byte[],System.Int32,System.Int32,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Uploads the contents of a byte array to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="buffer">An array of bytes.</param>
            <param name="index">The zero-based byte offset in buffer at which to begin uploading bytes to the file.</param>
            <param name="count">The number of bytes to be written to the file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromByteArrayAsync(System.Byte[],System.Int32,System.Int32,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Uploads the contents of a byte array to a file.
            </summary>
            <param name="buffer">An array of bytes.</param>
            <param name="index">The zero-based byte offset in buffer at which to begin uploading bytes to the file.</param>
            <param name="count">The number of bytes to be written to the file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadFromByteArrayAsync(System.Byte[],System.Int32,System.Int32,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Uploads the contents of a byte array to a file.
            </summary>
            <param name="buffer">An array of bytes.</param>
            <param name="index">The zero-based byte offset in buffer at which to begin uploading bytes to the file.</param>
            <param name="count">The number of bytes to be written to the file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadTextAsync(System.String)">
            <summary>
            Uploads a string of text to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="content">The text to upload, encoded as a UTF-8 string.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadTextAsync(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Uploads a string of text to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="content">The text to upload, encoded as a UTF-8 string.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadTextAsync(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Uploads a string of text to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="content">The text to upload, encoded as a UTF-8 string.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UploadTextAsync(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Uploads a string of text to a file. If the file already exists on the service, it will be overwritten.
            </summary>
            <param name="content">The text to upload, encoded as a UTF-8 string.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToStreamAsync(System.IO.Stream)">
            <summary>
            Downloads the contents of a file to a stream.
            </summary>
            <param name="target">The target stream.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToStreamAsync(System.IO.Stream,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Downloads the contents of a file to a stream.
            </summary>
            <param name="target">The target stream.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToStreamAsync(System.IO.Stream,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a stream.
            </summary>
            <param name="target">The target stream.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToStreamAsync(System.IO.Stream,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a stream.
            </summary>
            <param name="target">The target stream.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToFileAsync(System.String,System.IO.FileMode)">
            <param name="path">A string containing the file path providing the blob content.</param>
            <param name="mode">A <see cref="T:System.IO.FileMode"/> enumeration value that specifies how to open the file.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToFileAsync(System.String,System.IO.FileMode,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <param name="path">A string containing the file path providing the blob content.</param>
            <param name="mode">A <see cref="T:System.IO.FileMode"/> enumeration value that specifies how to open the file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToFileAsync(System.String,System.IO.FileMode,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a file.
            </summary>
            <param name="path">A string containing the file path providing the blob content.</param>
            <param name="mode">A <see cref="T:System.IO.FileMode"/> enumeration value that specifies how to open the file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToFileAsync(System.String,System.IO.FileMode,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a file.
            </summary>
            <param name="path">A string containing the file path providing the blob content.</param>
            <param name="mode">A <see cref="T:System.IO.FileMode"/> enumeration value that specifies how to open the file.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToByteArrayAsync(System.Byte[],System.Int32)">
            <summary>
            Downloads the contents of a file to a byte array.
            </summary>
            <param name="target">The target byte array.</param>
            <param name="index">The starting offset in the byte array.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToByteArrayAsync(System.Byte[],System.Int32,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Downloads the contents of a file to a byte array.
            </summary>
            <param name="target">The target byte array.</param>
            <param name="index">The starting offset in the byte array.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToByteArrayAsync(System.Byte[],System.Int32,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a byte array.
            </summary>
            <param name="target">The target byte array.</param>
            <param name="index">The starting offset in the byte array.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadToByteArrayAsync(System.Byte[],System.Int32,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a byte array.
            </summary>
            <param name="target">The target byte array.</param>
            <param name="index">The starting offset in the byte array.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadRangeToStreamAsync(System.IO.Stream,System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            Downloads the contents of a file to a stream.
            </summary>
            <param name="target">The target stream.</param>
            <param name="offset">The offset at which to begin downloading the file, in bytes.</param>
            <param name="length">The length of the data to download from the file, in bytes.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadTextAsync">
            <summary>
            Downloads the file's contents as a string.
            </summary>
            <returns>The contents of the file, as a string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadTextAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Downloads the file's contents as a string.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The contents of the file, as a string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadTextAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Downloads the file's contents as a string.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The contents of the file, as a string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadTextAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Downloads the file's contents as a string.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The contents of the file, as a string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadRangeToStreamAsync(System.IO.Stream,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Downloads the contents of a file to a stream.
            </summary>
            <param name="target">The target stream.</param>
            <param name="offset">The offset at which to begin downloading the file, in bytes.</param>
            <param name="length">The length of the data to download from the file, in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadRangeToStreamAsync(System.IO.Stream,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a stream.
            </summary>
            <param name="target">The target stream.</param>
            <param name="offset">The offset at which to begin downloading the file, in bytes.</param>
            <param name="length">The length of the data to download from the file, in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadRangeToStreamAsync(System.IO.Stream,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a stream.
            </summary>
            <param name="target">The target stream.</param>
            <param name="offset">The offset at which to begin downloading the file, in bytes.</param>
            <param name="length">The length of the data to download from the file, in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadRangeToByteArrayAsync(System.Byte[],System.Int32,System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            Downloads the contents of a file to a byte array.
            </summary>
            <param name="target">The target byte array.</param>
            <param name="index">The starting offset in the byte array.</param>
            <param name="fileOffset">The starting offset of the data range, in bytes.</param>
            <param name="length">The length of the data range, in bytes.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadRangeToByteArrayAsync(System.Byte[],System.Int32,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Downloads the contents of a file to a byte array.
            </summary>
            <param name="target">The target byte array.</param>
            <param name="index">The starting offset in the byte array.</param>
            <param name="fileOffset">The starting offset of the data range, in bytes.</param>
            <param name="length">The length of the data range, in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadRangeToByteArrayAsync(System.Byte[],System.Int32,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a byte array.
            </summary>
            <param name="target">The target byte array.</param>
            <param name="index">The starting offset in the byte array.</param>
            <param name="fileOffset">The starting offset of the data range, in bytes.</param>
            <param name="length">The length of the data range, in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DownloadRangeToByteArrayAsync(System.Byte[],System.Int32,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Downloads the contents of a file to a byte array.
            </summary>
            <param name="target">The target byte array.</param>
            <param name="index">The starting offset in the byte array.</param>
            <param name="fileOffset">The starting offset of the data range, in bytes.</param>
            <param name="length">The length of the data range, in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The total number of bytes read into the buffer.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.CreateAsync(System.Int64)">
            <summary>
            Creates a file. If the file already exists, it will be overwritten.
            </summary>
            <param name="size">The maximum size of the file, in bytes.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.CreateAsync(System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Creates a file. If the file already exists, it will be overwritten.
            </summary>
            <param name="size">The maximum size of the file, in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.CreateAsync(System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Creates a file. If the file already exists, it will be overwritten.
            </summary>
            <param name="size">The maximum size of the file, in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ExistsAsync">
            <summary>
            Checks existence of the file.
            </summary>
            <returns><c>true</c> if the file exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Checks existence of the file.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the file exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Checks existence of the file.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the file exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.FetchAttributesAsync">
            <summary>
            Populates a file's properties and metadata.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.FetchAttributesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Populates a file's properties and metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.FetchAttributesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Populates a file's properties and metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DeleteAsync">
            <summary>
            Deletes the file.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DeleteAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Deletes the file.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DeleteAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Deletes the file.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DeleteIfExistsAsync">
            <summary>
            Deletes the file if it already exists.
            </summary>
            <returns><c>true</c> if the file already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DeleteIfExistsAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Deletes the file if it already exists.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the file already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DeleteIfExistsAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Deletes the file if it already exists.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the file already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <!-- Badly formed XML comment ignored for member "M:Microsoft.Azure.Storage.File.CloudFile.ListRangesAsync" -->
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ListRangesAsync(System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Gets a collection of valid ranges and their starting and ending bytes.
            </summary>
            <param name="offset">The starting offset of the data range over which to list file ranges, in bytes.</param>
            <param name="length">The length of the data range over which to list file ranges, in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>An enumerable collection of ranges.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ListRangesAsync(System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Gets a collection of valid ranges and their starting and ending bytes.
            </summary>
            <param name="offset">The starting offset of the data range over which to list file ranges, in bytes.</param>
            <param name="length">The length of the data range over which to list file ranges, in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>An enumerable collection of ranges.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ListHandlesSegmentedAsync(Microsoft.Azure.Storage.File.FileContinuationToken,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary> 
            Returns a task that performs an asynchronous operation to get the SMB handles open on this file. 
            </summary> 
            <param name="token">Continuation token for paginated results.</param> 
            <param name="maxResults">The maximum number of results to be returned by the server.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param> 
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param> 
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> object that represents the current operation.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.CloseAllHandlesSegmentedAsync(Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary> 
            Returns a task that performs an asynchronous operation to close all SMB handles on this file. 
            </summary> 
            <param name="token">Continuation token for closing many handles.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param> 
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param> 
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> object that represents the current operation.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.CloseHandleSegmentedAsync(System.String,Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary> 
            Returns a task that performs an asynchronous operation to close the specified SMB handle on this file. 
            </summary> 
            <param name="handleId">Id of the handle, "*" if all handles on the file.</param> 
            <param name="token">Continuation token for when the handle takes exceedingly long to find.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param> 
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param> 
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> object that represents the current operation.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SetPropertiesAsync">
            <summary>
            Updates the file's properties.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SetPropertiesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Updates the file's properties.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SetPropertiesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Updates the file's properties.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ResizeAsync(System.Int64)">
            <summary>
            Resizes a file.
            </summary>
            <param name="size">The maximum size of the file, in bytes.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ResizeAsync(System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Resizes a file.
            </summary>
            <param name="size">The maximum size of the file, in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ResizeAsync(System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Resizes a file.
            </summary>
            <param name="size">The maximum size of the file, in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SetMetadataAsync">
            <summary>
            Updates the file's metadata.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SetMetadataAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Updates the file's metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SetMetadataAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Updates the file's metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.WriteRangeAsync(System.Uri,System.Int64,System.Int64,System.Int64,Microsoft.Azure.Storage.Shared.Protocol.Checksum,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary>
            Writes range from a source file to this file.
            </summary>
            <param name="sourceUri">A <see cref="T:System.Uri"/> specifying the absolute URI to the source file.</param>
            <param name="sourceOffset">The offset at which to begin reading the source, in bytes.</param>
            <param name="count">The number of bytes to write</param>
            <param name="destOffset">The offset at which to begin writing, in bytes.</param>
            <param name="sourceContentChecksum">A hash value used to ensure transactional integrity. May be <c>null</c> or Checksum.None</param>
            <param name="sourceAccessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the source file. If <c>null</c>, no condition is used.</param>
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.WriteRangeAsync(System.IO.Stream,System.Int64,System.String)">
            <summary>
            Writes range to a file.
            </summary>
            <param name="rangeData">A stream providing the range data.</param>
            <param name="startOffset">The offset at which to begin writing, in bytes.</param>
            <param name="contentMD5">An optional hash value that will be used to set the <see cref="P:Microsoft.Azure.Storage.File.FileProperties.ContentMD5"/> property
            on the file. May be <c>null</c> or an empty string.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.WriteRangeAsync(System.IO.Stream,System.Int64,System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Writes range to a file.
            </summary>
            <param name="rangeData">A stream providing the range data.</param>
            <param name="startOffset">The offset at which to begin writing, in bytes.</param>
            <param name="contentMD5">An optional hash value that will be used to set the <see cref="P:Microsoft.Azure.Storage.File.FileProperties.ContentMD5"/> property
            on the file. May be <c>null</c> or an empty string.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.WriteRangeAsync(System.IO.Stream,System.Int64,System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Writes range to a file.
            </summary>
            <param name="rangeData">A stream providing the range data.</param>
            <param name="startOffset">The offset at which to begin writing, in bytes.</param>
            <param name="contentMD5">An optional hash value that will be used to set the <see cref="P:Microsoft.Azure.Storage.File.FileProperties.ContentMD5"/> property
            on the file. May be <code>null</code> or an empty string.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.WriteRangeAsync(System.IO.Stream,System.Int64,System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Writes range to a file.
            </summary>
            <param name="rangeData">A stream providing the range data.</param>
            <param name="startOffset">The offset at which to begin writing, in bytes.</param>
            <param name="contentMD5">An optional hash value that will be used to set the <see cref="P:Microsoft.Azure.Storage.File.FileProperties.ContentMD5"/> property
            on the file. May be <c>null</c> or an empty string.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.WriteRangeAsync(System.IO.Stream,System.Int64,Microsoft.Azure.Storage.Shared.Protocol.Checksum,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.IProgress{Microsoft.Azure.Storage.Core.Util.StorageProgress},System.Threading.CancellationToken)">
            <summary>
            Writes range to a file.
            </summary>
            <param name="rangeData">A stream providing the range data.</param>
            <param name="startOffset">The offset at which to begin writing, in bytes.</param>
            <param name="contentChecksum">A hash value that will be used to set the checksum property on the file. May be <c>null</c> or Checksum.None.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="progressHandler"> A <see cref="T:System.IProgress`1"/> object to handle <see cref="T:Microsoft.Azure.Storage.Core.Util.StorageProgress"/> messages.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ClearRangeAsync(System.Int64,System.Int64)">
            <summary>
            Clears ranges from a file.
            </summary>
            <param name="startOffset">The offset at which to begin clearing file ranges, in bytes.</param>
            <param name="length">The length of the data range to be cleared, in bytes.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ClearRangeAsync(System.Int64,System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Clears ranges from a file.
            </summary>
            <param name="startOffset">The offset at which to begin clearing file ranges, in bytes.</param>
            <param name="length">The length of the data range to be cleared, in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ClearRangeAsync(System.Int64,System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Clears ranges from a file.
            </summary>
            <param name="startOffset">The offset at which to begin clearing file ranges, in bytes.</param>
            <param name="length">The length of the data range to be cleared, in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.StartCopyAsync(System.Uri)">
            <summary>
            Begins an operation to start copying an existing blob or Azure file's contents, properties, and metadata to a new Azure file.
            </summary>
            <param name="source">The URI of a source object.</param>
            <returns>The copy ID associated with the copy operation.</returns>
            <remarks>
            This method fetches the file's ETag, last modified time, and part of the copy state.
            The copy ID and copy status fields are fetched, and the rest of the copy state is cleared.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.StartCopyAsync(Microsoft.Azure.Storage.File.CloudFile)">
            <summary>
            Begins an operation to start copying an existing Azure file's contents, properties, and metadata to a new Azure file.
            </summary>
            <param name="source">The source file.</param>
            <returns>The copy ID associated with the copy operation.</returns>
            <remarks>
            This method fetches the file's ETag, last modified time, and part of the copy state.
            The copy ID and copy status fields are fetched, and the rest of the copy state is cleared.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.StartCopyAsync(System.Uri,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Begins an operation to start copying a blob or file's contents, properties, and metadata to a new Azure file.
            </summary>
            <param name="source">The URI of a source object.</param>
            <param name="sourceAccessCondition">An object that represents the access conditions for the source object. If <c>null</c>, no condition is used.</param>
            <param name="destAccessCondition">An object that represents the access conditions for the destination file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The copy ID associated with the copy operation.</returns>
            <remarks>
            This method fetches the file's ETag, last modified time, and part of the copy state.
            The copy ID and copy status fields are fetched, and the rest of the copy state is cleared.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.StartCopyAsync(System.Uri,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Begins an operation to start copying a blob or file's contents, properties, and metadata to a new Azure file.
            </summary>
            <param name="source">The URI of a source object.</param>
            <param name="sourceAccessCondition">An object that represents the access conditions for the source object. If <c>null</c>, no condition is used.</param>
            <param name="destAccessCondition">An object that represents the access conditions for the destination file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The copy ID associated with the copy operation.</returns>
            <remarks>
            This method fetches the file's ETag, last modified time, and part of the copy state.
            The copy ID and copy status fields are fetched, and the rest of the copy state is cleared.
            </remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.AbortCopyAsync(System.String)">
            <summary>
            Aborts an ongoing copy operation.
            </summary>
            <param name="copyId">A string identifying the copy operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.AbortCopyAsync(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Aborts an ongoing copy operation.
            </summary>
            <param name="copyId">A string identifying the copy operation.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.AbortCopyAsync(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Aborts an ongoing copy operation.
            </summary>
            <param name="copyId">A string identifying the copy operation.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.GetFileImpl(System.IO.Stream,System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implements getting the file.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:SynchronousTask"/> that gets the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.CreateImpl(System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implements the Create method.
            </summary>
            <param name="sizeInBytes">The size in bytes.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> that creates the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.FetchAttributesImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implements the FetchAttributes method. The attributes are updated immediately.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that fetches the attributes.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ExistsImpl(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implements the Exists method. The attributes are updated immediately.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that checks existence.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.DeleteFileImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implements the DeleteFile method.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that deletes the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ListRangesImpl(System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implements the ListRanges method.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> for getting the ranges.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ListHandlesImpl(Microsoft.Azure.Storage.File.FileContinuationToken,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary> 
            Gets the list handles implementation. 
            </summary> 
            <param name="token">Continuation token for paged responses.</param> 
            <param name="maxResults">The maximum number of results to be returned by the server.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> for getting the handles.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.CloseHandleImpl(Microsoft.Azure.Storage.File.FileContinuationToken,System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary> 
            Gets the close handles implementation. 
            </summary> 
            <param name="token">Continuation token for closing many handles.</param> 
            <param name="handleId">Id of the handle, "*" if all handles on the file.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> for closing the handles.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SetPropertiesImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the SetProperties method.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> that sets the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ResizeImpl(System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the Resize method.
            </summary>
            <param name="sizeInBytes">The size in bytes.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that sets the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SetMetadataImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the SetMetadata method.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that sets the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.PutRangeImpl(System.IO.Stream,System.Int64,Microsoft.Azure.Storage.Shared.Protocol.Checksum,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation method for the WriteRange methods.
            </summary>
            <param name="rangeData">The range data.</param>
            <param name="startOffset">The start offset.</param> 
            <param name="contentChecksum">A hash value that will be used to set the checksum property on the file. May be <c>null</c> or Checksum.None.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that writes the range.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.PutRangeFromUriImpl(System.Uri,System.Int64,System.Int64,System.Int64,Microsoft.Azure.Storage.Shared.Protocol.Checksum,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation method for the WriteRange from source file method.
            </summary>
            <param name="sourceUri">A <see cref="T:System.Uri"/> specifying the absolute URI to the source file.</param>
            <param name="sourceOffset">The beginning source offset</param>
            <param name="count">The number of bytes to write</param>
            <param name="destOffset">The beginning destination offset</param>
            <param name="sourceContentChecksum">The checksum calculated for the range of bytes of the source.</param>
            <param name="sourceAccessCondition">The source access condition to apply to the request</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that writes the range from the source file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ClearRangeImpl(System.Int64,System.Int64,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation method for the ClearRange methods.
            </summary>
            <param name="startOffset">The start offset.</param>
            <param name="length">Length of the data range to be cleared.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that writes the ranges.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.StartCopyImpl(System.Uri,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation of the StartCopy method. Result is a CloudFileAttributes object derived from the response headers.
            </summary>
            <param name="source">The URI of the source object.</param>
            <param name="sourceAccessCondition">An object that represents the access conditions for the source object. If null, no condition is used.</param>
            <param name="destAccessCondition">An object that represents the access conditions for the destination file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="setResult">A delegate for setting the CloudFileAttributes result.</param>
            <returns>A <see cref="!:RESTCommand"/> that starts to copy the object.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.AbortCopyImpl(System.String,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation of the AbortCopy method. No result is produced.
            </summary>
            <param name="copyId">The copy ID of the copy operation to abort.</param>
            <param name="accessCondition">An object that represents the access conditions for the operation. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:TaskSequence"/> that copies the object.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.SourceFileToUri(Microsoft.Azure.Storage.File.CloudFile)">
            <summary>
            Converts the source file of a copy operation to an appropriate access URI, taking Shared Access Signature credentials into account.
            </summary>
            <param name="source">The source file.</param>
            <returns>A URI addressing the source file, using SAS if appropriate.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UpdateAfterFetchAttributes(System.Net.Http.HttpResponseMessage)">
            <summary>
            Updates this file with the given attributes a the end of a fetch attributes operation.
            </summary>
            <param name="response">The response.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.UpdateETagLMTAndLength(System.Net.Http.HttpResponseMessage,System.Boolean)">
            <summary>
            Retrieve ETag, LMT and Length from response.
            </summary>
            <param name="response">The response to parse.</param>
            <param name="updateLength">If set to <c>true</c>, update the file length.</param>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFile.streamWriteSizeInBytes">
            <summary>
            Default is 4 MB.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFile.streamMinimumReadSizeInBytes">
            <summary>
            Default is 4 MB.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFile.filePermission">
            <summary>
            Stores the file permission to set on the next File Create or Set Properties call.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.#ctor(System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> class using an absolute URI to the file.
            </summary>
            <param name="fileAbsoluteUri">The absolute URI to the file.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.#ctor(System.Uri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> class using an absolute URI to the file.
            </summary>
            <param name="fileAbsoluteUri">The absolute URI to the file.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.#ctor(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> class using an absolute URI to the file.
            </summary>
            <param name="fileAbsoluteUri">The absolute URI to the file.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.#ctor(Microsoft.Azure.Storage.StorageUri,System.String,Microsoft.Azure.Storage.File.CloudFileShare)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> class using the specified file name and
            the parent share reference.
            </summary>
            <param name="uri">The file's Uri.</param>
            <param name="fileName">Name of the file.</param>
            <param name="share">The reference to the parent share.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.#ctor(Microsoft.Azure.Storage.File.CloudFileAttributes,Microsoft.Azure.Storage.File.CloudFileClient)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> class.
            </summary>
            <param name="attributes">The attributes.</param>
            <param name="serviceClient">The service client.</param>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFile.share">
            <summary>
            Stores the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> that contains this file.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFile.parent">
            <summary>
            Stores the file's parent <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/>.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFile.attributes">
            <summary>
            Stores the file's attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.FilePermission">
            <summary>
            The file permission to set on the next File Create or Set Properties call.
            This field will be null after File.Create, File.SetProperties, File.GetProperties, and File.Get calls.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.ServiceClient">
            <summary>
            Gets the <see cref="T:Microsoft.Azure.Storage.File.CloudFileClient"/> object that represents the File service.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.CloudFileClient"/> object that specifies the File service endpoint.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.StreamWriteSizeInBytes">
            <summary>
            Gets or sets the number of bytes to buffer when writing to a file stream.
            </summary>
            <value>The number of bytes to buffer, ranging from between 512 bytes and 4 MB inclusive.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.StreamMinimumReadSizeInBytes">
            <summary>
            Gets or sets the minimum number of bytes to buffer when reading from a file stream.
            </summary>
            <value>The minimum number of bytes to buffer, being at least 16KB.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.Properties">
            <summary>
            Gets the file's system properties.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.FileProperties"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.Metadata">
            <summary>
            Gets the user-defined metadata for the file.
            </summary>
            <value>The file's metadata, as a collection of name-value pairs.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.Uri">
            <summary>
            Gets the file's URI.
            </summary>
            <value>The absolute URI to the file.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.StorageUri">
            <summary>
            Gets the absolute URI to the file.
            </summary>
            <value>A <see cref="P:Microsoft.Azure.Storage.File.CloudFile.StorageUri"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.SnapshotQualifiedUri">
            <summary>
            Gets the absolute URI to the file, including query string information if the file's share is a snapshot.
            </summary>
            <value>A <see cref="T:System.Uri"/> specifying the absolute URI to the file, including snapshot query information if the file's share is a snapshot.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.SnapshotQualifiedStorageUri">
            <summary>
            Gets the file's URI for both the primary and secondary locations, including query string information if the file's share is a snapshot.
            </summary>
            <value>An object of type <see cref="P:Microsoft.Azure.Storage.File.CloudFile.StorageUri"/> containing the file's URIs for both the primary and secondary locations, 
            including snapshot query information if the file's share is a snapshot.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.AssertNoSnapshot">
            <summary>
            Verifies that the file's share is not a snapshot.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.AssertValidFilePermissionOrKey">
            <summary>
            Verifies that the files's filePermission and properties.FilePermissionKey are both not set.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.CopyState">
            <summary>
            Gets the state of the most recent or pending copy operation.
            </summary>
            <value>A <see cref="P:Microsoft.Azure.Storage.File.CloudFile.CopyState"/> object containing the copy state, or <c>null</c> if there is no copy state for the file.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.Name">
            <summary>
            Gets the file's name.
            </summary>
            <value>The file's name.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.Share">
            <summary>
            Gets a <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> object representing the file's share.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFile.Parent">
            <summary>
            Gets the <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> object representing the
            parent directory for the file.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> object.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.GetSharedAccessSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy)">
            <summary>
            Returns a shared access signature for the file.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> object specifying the access policy for the shared access signature.</param>
            <returns>A shared access signature, as a URI query string.</returns>
            <remarks>The query string returned includes the leading question mark.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.GetSharedAccessSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy,System.String)">
            <summary>
            Returns a shared access signature for the file.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> object specifying the access policy for the shared access signature.</param>
            <param name="groupPolicyIdentifier">A string identifying a stored access policy.</param>
            <returns>A shared access signature, as a URI query string.</returns>
            <remarks>The query string returned includes the leading question mark.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.GetSharedAccessSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy,Microsoft.Azure.Storage.File.SharedAccessFileHeaders)">
            <summary>
            Returns a shared access signature for the file.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> object specifying the access policy for the shared access signature.</param>
            <param name="headers">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFileHeaders"/> object specifying optional header values to set for a file accessed with this SAS.</param>
            <returns>A shared access signature, as a URI query string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.GetSharedAccessSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy,Microsoft.Azure.Storage.File.SharedAccessFileHeaders,System.String)">
            <summary>
            Returns a shared access signature for the file.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> object specifying the access policy for the shared access signature.</param>
            <param name="headers">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFileHeaders"/> object specifying optional header values to set for a file accessed with this SAS.</param>
            <param name="groupPolicyIdentifier">A string identifying a stored access policy.</param>
            <returns>A shared access signature, as a URI query string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.GetSharedAccessSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy,Microsoft.Azure.Storage.File.SharedAccessFileHeaders,System.String,System.Nullable{Microsoft.Azure.Storage.SharedAccessProtocol},Microsoft.Azure.Storage.IPAddressOrRange)">
            <summary>
            Returns a shared access signature for the file.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> object specifying the access policy for the shared access signature.</param>
            <param name="headers">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFileHeaders"/> object specifying optional header values to set for a file accessed with this SAS.</param>
            <param name="groupPolicyIdentifier">A string identifying a stored access policy.</param>
            <param name="protocols">The allowed protocols (https only, or http and https). Null if you don't want to restrict protocol.</param>
            <param name="ipAddressOrRange">The allowed IP address or IP address range. Null if you don't want to restrict based on IP address.</param>
            <returns>A shared access signature, as a URI query string.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.GetCanonicalName">
            <summary>
            Gets the canonical name of the file, formatted as file/&lt;account-name&gt;/&lt;share-name&gt;/&lt;directory-name&gt;/&lt;file-name&gt;.
            <para>This is used by both Shared Access and Copy operations.</para>
            </summary>
            <returns>The canonical name of the file.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFile.ParseQueryAndVerify(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Parse URI.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="credentials">The credentials to use.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.CloudFileClient">
            <summary>
            Provides a client-side logical representation of the Microsoft Azure File service. This client is used to configure and execute requests against the File service.
            </summary>
            <remarks>The service client encapsulates the base URI for the File service. If the service client will be used for authenticated access, it also encapsulates 
            the credentials for accessing the storage account.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileClient.AuthenticationScheme">
            <summary>
            Gets or sets the authentication scheme to use to sign HTTP requests.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.ListSharesSegmentedAsync(Microsoft.Azure.Storage.File.FileContinuationToken)">
            <summary>
            Returns a result segment containing a collection of shares.
            </summary>
            <param name="currentToken">A continuation token returned by a previous listing operation.</param> 
            <returns>A result segment of shares.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.ListSharesSegmentedAsync(System.String,Microsoft.Azure.Storage.File.FileContinuationToken)">
            <summary>
            Returns a result segment containing a collection of shares.
            </summary>
            <param name="prefix">The share name prefix.</param>
            <param name="currentToken">A continuation token returned by a previous listing operation.</param> 
            <returns>A result segment of shares.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.ListSharesSegmentedAsync(System.String,Microsoft.Azure.Storage.File.ShareListingDetails,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Returns a result segment containing a collection of shares
            whose names begin with the specified prefix.
            </summary>
            <param name="prefix">The share name prefix.</param>
            <param name="detailsIncluded">A value that indicates whether to return share metadata with the listing.</param>
            <param name="maxResults">A non-negative integer value that indicates the maximum number of results to be returned 
            in the result segment, up to the per-operation limit of 5000. If this value is null, the maximum possible number of results will be returned, up to 5000.</param>         
            <param name="currentToken">A continuation token returned by a previous listing operation.</param> 
            <returns>A result segment of shares.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.ListSharesSegmentedAsync(System.String,Microsoft.Azure.Storage.File.ShareListingDetails,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Returns a result segment containing a collection of shares
            whose names begin with the specified prefix.
            </summary>
            <param name="prefix">The share name prefix.</param>
            <param name="detailsIncluded">A value that indicates whether to return share metadata with the listing.</param>
            <param name="maxResults">A non-negative integer value that indicates the maximum number of results to be returned 
            in the result segment, up to the per-operation limit of 5000. If this value is null, the maximum possible number of results will be returned, up to 5000.</param>         
            <param name="currentToken">A continuation token returned by a previous listing operation.</param> 
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A result segment of shares.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.GetServicePropertiesAsync">
            <summary>
            Gets the properties of the File service.
            </summary>
            <returns>The File service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.GetServicePropertiesAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Gets the properties of the File service.
            </summary>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies execution options, such as retry policy and timeout settings, for the operation.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The File service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.GetServicePropertiesAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Gets the properties of the File service.
            </summary>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies execution options, such as retry policy and timeout settings, for the operation.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The File service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.SetServicePropertiesAsync(Microsoft.Azure.Storage.File.Protocol.FileServiceProperties)">
            <summary>
            Sets the properties of the File service.
            </summary>
            <param name="properties">The File service properties.</param>
            <returns>The properties of the File service.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.SetServicePropertiesAsync(Microsoft.Azure.Storage.File.Protocol.FileServiceProperties,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Gets the properties of the File service.
            </summary>
            <param name="properties">The File service properties.</param>
            <param name="requestOptions">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies execution options, such as retry policy and timeout settings, for the operation.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The properties of the File service.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.SetServicePropertiesAsync(Microsoft.Azure.Storage.File.Protocol.FileServiceProperties,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Gets the properties of the File service.
            </summary>
            <param name="properties">The File service properties.</param>
            <param name="requestOptions">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies execution options, such as retry policy and timeout settings, for the operation.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The properties of the File service.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.ListSharesImpl(System.String,Microsoft.Azure.Storage.File.ShareListingDetails,Microsoft.Azure.Storage.File.FileContinuationToken,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Core implementation for the ListShares method.
            </summary>
            <param name="prefix">The share prefix.</param>
            <param name="detailsIncluded">The details included.</param>
            <param name="currentToken">The continuation token.</param>
            <param name="pagination">The pagination.</param>
            <param name="setResult">The result report delegate.</param>
            <returns>A <see cref="!:TaskSequence"/> that lists the shares.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.#ctor(System.Uri,Microsoft.Azure.Storage.Auth.StorageCredentials,System.Net.Http.DelegatingHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileClient"/> class using the specified File service endpoint
            and account credentials.
            </summary>
            <param name="baseUri">The File service endpoint to use to create the client.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
            <param name="delegatingHandler">A chain of 1 or more DelegatingHandler instances, the innermost of which must have a null InnerHandler.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.#ctor(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials,System.Net.Http.DelegatingHandler)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileClient"/> class using the specified File service endpoint
            and account credentials.
            </summary>
            <param name="storageUri">The File service endpoint to use to create the client.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
            <param name="delegatingHandler">A chain of 1 or more DelegatingHandler instances, the innermost of which must have a null InnerHandler.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileClient.BufferManager">
            <summary>
            Gets or sets a buffer manager that implements the <see cref="T:Microsoft.Azure.Storage.IBufferManager"/> interface, 
            specifying a buffer pool for use with operations against the File service client.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileClient.Credentials">
            <summary>
            Gets the account credentials used to create the File service client.
            </summary>
            <value>The account credentials.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileClient.BaseUri">
            <summary>
            Gets the base URI for the File service client.
            </summary>
            <value>The base URI used to construct the File service client.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileClient.StorageUri">
            <summary>
            Gets the list of URIs for all locations.
            </summary>
            <value>The list of URIs for all locations.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileClient.DefaultRequestOptions">
            <summary>
            Gets or sets the default request options for requests made via the File service client.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileClient.UsePathStyleUris">
            <summary>
            Gets a value indicating whether the service client is used with Path style or Host style.
            </summary>
            <value>Is <c>true</c> if use path style URIs; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.GetShareReference(System.String)">
            <summary>
            Returns a reference to a <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> object with the specified name.
            </summary>
            <param name="shareName">A string containing the name of the share.</param>
            <returns>A reference to a share.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileClient.GetShareReference(System.String,System.Nullable{System.DateTimeOffset})">
            <summary>
            Returns a reference to a <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> object with the specified name and snapshot time.
            </summary>
            <param name="shareName">A string containing the name of the share.</param>
            <param name="snapshotTime">A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</param>
            <returns>A reference to a share.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.CloudFileDirectory">
            <summary>
            Represents a directory of files, designated by a delimiter character.
            </summary>
            <remarks>Shares, which are encapsulated as <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> objects, hold directories, and directories hold files. Directories can also contain sub-directories.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CreateAsync">
            <summary>
            Creates the directory.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CreateAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Creates the directory.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CreateAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Creates the directory.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CreateIfNotExistsAsync">
            <summary>
            Creates the directory if it does not already exist.
            </summary>
            <returns><c>true</c> if the directory did not already exist and was created; otherwise, <c>false</c>.</returns>
            <remarks>This API requires Create or Write permissions.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CreateIfNotExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Creates the directory if it does not already exist.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the directory did not already exist and was created; otherwise <c>false</c>.</returns>
            <remarks>This API requires Create or Write permissions.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CreateIfNotExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Creates the directory if it does not already exist.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the directory did not already exist and was created; otherwise <c>false</c>.</returns>
            <remarks>This API requires Create or Write permissions.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.DeleteAsync">
            <summary>
            Deletes the directory.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.DeleteAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Deletes the directory.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the directory. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.DeleteAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Deletes the directory.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the directory. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.DeleteIfExistsAsync">
            <summary>
            Deletes the directory if it already exists.
            </summary>
            <returns><c>true</c> if the directory already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.DeleteIfExistsAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Deletes the directory if it already exists.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the condition that must be met in order for the request to proceed.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the directory already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.DeleteIfExistsAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Deletes the directory if it already exists.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the condition that must be met in order for the request to proceed.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the directory already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ExistsAsync">
            <summary>
            Checks existence of the directory.
            </summary>
            <returns><c>true</c> if the directory exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Checks existence of the directory.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the directory exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Checks existence of the directory.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the directory exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.FetchAttributesAsync">
            <summary>
            Populates a directory's properties and metadata.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.FetchAttributesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Populates a directory's properties and metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.FetchAttributesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Populates a directory's properties and metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ListFilesAndDirectoriesSegmentedAsync(Microsoft.Azure.Storage.File.FileContinuationToken)">
            <summary>
            Returns a result segment containing a collection of file items 
            in the share.
            </summary>
            <param name="currentToken">A continuation token returned by a previous listing operation.</param> 
            <returns>A result segment containing objects that implement <see cref="T:Microsoft.Azure.Storage.File.IListFileItem"/>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ListFilesAndDirectoriesSegmentedAsync(System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Returns a result segment containing a collection of file items 
            in the share.
            </summary>
            <param name="maxResults">A non-negative integer value that indicates the maximum number of results to be returned at a time, up to the 
            per-operation limit of 5000. If this value is zero, the maximum possible number of results will be returned, up to 5000.</param>
            <param name="currentToken">A continuation token returned by a previous listing operation.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <returns>A file result segment.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ListFilesAndDirectoriesSegmentedAsync(System.String,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Returns a result segment containing a collection of file items 
            in the share.
            </summary>
            <param name="prefix">A string containing the file or directory name prefix.</param>
            <param name="maxResults">A non-negative integer value that indicates the maximum number of results to be returned at a time, up to the 
            per-operation limit of 5000. If this value is zero, the maximum possible number of results will be returned, up to 5000.</param>         
            <param name="currentToken">A continuation token returned by a previous listing operation.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <returns>A file result segment.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ListFilesAndDirectoriesSegmentedAsync(System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Returns a result segment containing a collection of file items 
            in the share.
            </summary>
            <param name="maxResults">A non-negative integer value that indicates the maximum number of results to be returned at a time, up to the 
            per-operation limit of 5000. If this value is zero, the maximum possible number of results will be returned, up to 5000.</param>         
            <param name="currentToken">A continuation token returned by a previous listing operation.</param> 
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A file result segment.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ListFilesAndDirectoriesSegmentedAsync(System.String,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Returns a result segment containing a collection of file items 
            in the share.
            </summary>
            <param name="prefix">A string containing the file or directory name prefix.</param>
            <param name="maxResults">A non-negative integer value that indicates the maximum number of results to be returned at a time, up to the 
            per-operation limit of 5000. If this value is zero, the maximum possible number of results will be returned, up to 5000.</param>         
            <param name="currentToken">A continuation token returned by a previous listing operation.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A file result segment.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ListHandlesSegmentedAsync(Microsoft.Azure.Storage.File.FileContinuationToken,System.Nullable{System.Int32},System.Nullable{System.Boolean},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary> 
            Returns a task that performs an asynchronous operation to get the SMB handles open on this directory. 
            </summary> 
            <param name="token">Continuation token for paginated results.</param> 
            <param name="maxResults">The maximum number of results to be returned by the server.</param> 
            <param name="recursive">Whether to recurse through this directory's files and subfolders. A lack of value is interpreted as false.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param> 
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param> 
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> object that represents the current operation.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CloseAllHandlesSegmentedAsync(Microsoft.Azure.Storage.File.FileContinuationToken,System.Nullable{System.Boolean},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary> 
            Returns a task that performs an asynchronous operation to close all SMB handles on this directory. 
            </summary> 
            <param name="token">Continuation token for closing the handles.</param> 
            <param name="recursive">Whether to recurse through this directory's sub files and folders. A lack of value is interpreted as false.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param> 
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param> 
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> object that represents the current operation.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CloseHandleSegmentedAsync(System.String,Microsoft.Azure.Storage.File.FileContinuationToken,System.Nullable{System.Boolean},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary> 
            Returns a task that performs an asynchronous operation to close the specified SMB handle on this directory. 
            </summary> 
            <param name="handleId">Id of the handle, "*" if all handles on the file.</param> 
            <param name="token">Continuation token for when closing the handle takes exceedingly long.</param> 
            <param name="recursive">Whether to recurse through this directory's sub files and folders. A lack of value is interpreted as false.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param> 
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param> 
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> object that represents the current operation.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CloseHandleSegmentedAsync(System.UInt64,Microsoft.Azure.Storage.File.FileContinuationToken,System.Nullable{System.Boolean},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary> 
            Returns a task that performs an asynchronous operation to close the specified SMB handle on this directory. 
            </summary> 
            <param name="handleId">Id of the handle</param> 
            <param name="token">Continuation token for when closing the handle takes exceedingly long.</param> 
            <param name="recursive">Whether to recurse through this directory's sub files and folders. A lack of value is interpreted as false.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param> 
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param> 
            <returns>A <see cref="T:System.Threading.Tasks.Task`1"/> object that represents the current operation.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.SetMetdataAsync">
            <summary>
            Updates the directory's metadata.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.SetMetadataAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Updates the directory's metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the directory. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.SetMetadataAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Updates the directory's metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the directory. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="cancellationToken">Cancellation Token</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.SetPropertiesAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary>
            Updates the directory's properties.
            </summary>
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CreateDirectoryImpl(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the Create method.
            </summary>
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> that creates the directory.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.DeleteDirectoryImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the Delete method.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the directory. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that deletes the directory.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ExistsImpl(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the Exists method.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that checks existence.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ListHandlesImpl(Microsoft.Azure.Storage.File.FileContinuationToken,System.Nullable{System.Int32},System.Nullable{System.Boolean},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary> 
            Gets the list handles implementation. 
            </summary> 
            <param name="token">Continuation token for paged responses.</param> 
            <param name="maxResults">The maximum number of results to be returned by the server.</param> 
            <param name="recursive">Whether to recurse through this directory's files and subfolders.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> for getting the handles.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.CloseHandleImpl(Microsoft.Azure.Storage.File.FileContinuationToken,System.String,System.Nullable{System.Boolean},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary> 
            Gets the close handles implementation. 
            </summary> 
            <param name="token">Continuation token for closing many files.</param> 
            <param name="handleId">Id of the handle, "*" if all handles on the file.</param> 
            <param name="recursive">Whether to recurse through this directory's files and subfolders.</param> 
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param> 
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param> 
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> for closing the handles.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.FetchAttributesImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implements the FetchAttributes method. The attributes are updated immediately.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> that fetches the attributes.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ListFilesAndDirectoriesImpl(System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.File.FileContinuationToken,System.String)">
            <summary>
            Core implementation of the ListFilesAndDirectories method.
            </summary>
            <param name="maxResults">A non-negative integer value that indicates the maximum number of results to be returned at a time, up to the 
            per-operation limit of 5000. If this value is zero, the maximum possible number of results will be returned, up to 5000.</param>         
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="currentToken">The continuation token.</param>
            <param name="prefix">A string containing the file or directory name prefix.</param>
            <returns>A <see cref="!:RESTCommand"/> that lists the files.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.SetMetadataImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the SetMetadata method.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the directory. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that sets the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.SetPropertiesImpl(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the SetProperties method.
            </summary>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.Executor.RESTCommand`1"/> that sets the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.UpdateETagAndLastModified(System.Net.Http.HttpResponseMessage)">
            <summary>
            Retrieve ETag and LastModified date time from response.
            </summary>
            <param name="response">The response to parse.</param>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileDirectory.share">
            <summary>
            Stores the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> that contains this directory.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileDirectory.parent">
            <summary>
            Stores the parent directory.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileDirectory.filePermission">
            <summary>
            Stores the file permission to set on the next Directory Create or Set Properties call.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.#ctor(System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> class using an absolute URI to the directory.
            </summary>
            <param name="directoryAbsoluteUri">A <see cref="T:System.Uri"/> object containing the absolute URI to the directory.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.#ctor(System.Uri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> class using an absolute URI to the directory.
            </summary>
            <param name="directoryAbsoluteUri">A <see cref="T:System.Uri"/> object containing the absolute URI to the directory.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.#ctor(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> class using an absolute URI to the directory.
            </summary>
            <param name="directoryAbsoluteUri">A <see cref="T:System.Uri"/> object containing the absolute URI to the directory.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.#ctor(Microsoft.Azure.Storage.StorageUri,System.String,Microsoft.Azure.Storage.File.CloudFileShare)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> class given an address and a client.
            </summary>
            <param name="uri">The file directory's Uri.</param>
            <param name="directoryName">Name of the directory.</param>
            <param name="share">The share for the directory.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.ServiceClient">
            <summary>
            Gets a <see cref="T:Microsoft.Azure.Storage.File.CloudFileClient"/> object that specifies the endpoint for the File service.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.CloudFileClient"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.Uri">
            <summary>
            Gets the directory's URI for the primary location.
            </summary>
            <value>A <see cref="T:System.Uri"/> specifying the absolute URI to the directory at the primary location.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.StorageUri">
            <summary>
            Gets the file directory's URIs for all locations.
            </summary>
            <value>A <see cref="P:Microsoft.Azure.Storage.File.CloudFileDirectory.StorageUri"/> object containing the file directory's URIs for all locations.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.SnapshotQualifiedUri">
            <summary>
            Gets the absolute URI to the directory, including query string information if the directory's share is a snapshot.
            </summary>
            <value>A <see cref="T:System.Uri"/> specifying the absolute URI to the directory, including snapshot query information if the directory's share is a snapshot.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.SnapshotQualifiedStorageUri">
            <summary>
            Gets the directory's URI for both the primary and secondary locations, including query string information if the directory's share is a snapshot.
            </summary>
            <value>An object of type <see cref="P:Microsoft.Azure.Storage.File.CloudFileDirectory.StorageUri"/> containing the directory's URIs for both the primary and secondary locations, 
            including snapshot query information if the directory's share is a snapshot.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.AssertNoSnapshot">
            <summary>
            Verifies that the directory's share is not a snapshot.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.AssertValidFilePermissionOrKey">
            <summary>
            Verifies that the directory's filePermission and properties.FilePermissionKey are both not set.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.Properties">
            <summary>
            Gets a <see cref="T:Microsoft.Azure.Storage.File.FileDirectoryProperties"/> object that represents the directory's system properties.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.FileDirectoryProperties"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.Metadata">
            <summary>
            Gets the user-defined metadata for the directory.
            </summary>
            <value>The directory's metadata, as a collection of name-value pairs.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.FilePermission">
            <summary>
            Gets or sets the file permission for this directory.
            This field will be null after Directory.Create, Directory.SetProperties, Directory.GetProperties, and Directory.Get calls.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.Share">
            <summary>
            Gets a <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> object that represents the share for the directory.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.Parent">
            <summary>
            Gets a <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> object that represents the parent directory for the directory.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> object.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileDirectory.Name">
            <summary>
            Gets the name of the directory.
            </summary>
            <value>A <see cref="T:System.String"/> containing the name of the directory.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.SelectListFileItem(Microsoft.Azure.Storage.File.Protocol.IListFileEntry)">
            <summary>
            Selects the protocol response.
            </summary>
            <param name="protocolItem">The protocol item.</param>
            <returns>The parsed <see cref="T:Microsoft.Azure.Storage.File.IListFileItem"/>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.GetFileReference(System.String)">
            <summary>
            Returns a <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> object that represents a file in this directory.
            </summary>
            <param name="fileName">A <see cref="T:System.String"/> containing the name of the file.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> object.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.GetDirectoryReference(System.String)">
            <summary>
            Returns a <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> object that represents a subdirectory within this directory.
            </summary>
            <param name="itemName">A <see cref="T:System.String"/> containing the name of the subdirectory.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.File.CloudFileDirectory"/> object.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileDirectory.ParseQueryAndVerify(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Parse URI.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="credentials">The credentials to use.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.CloudFileShare">
            <summary>
            Represents a share in the Microsoft Azure File service.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateAsync">
            <summary>
            Creates the share.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Creates the share.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Creates the share.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateIfNotExistsAsync">
            <summary>
            Creates the share if it does not already exist.
            </summary>
            <returns><c>true</c> if the share did not already exist and was created; otherwise, <c>false</c>.</returns>
            <remarks>This API requires Create or Write permissions.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateIfNotExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Creates the share if it does not already exist.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the share did not already exist and was created; otherwise <c>false</c>.</returns>
            <remarks>This API requires Create or Write permissions.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateIfNotExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Creates the share if it does not already exist.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the share did not already exist and was created; otherwise <c>false</c>.</returns>
            <remarks>This API requires Create or Write permissions.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SnapshotAsync">
            <summary>
            Creates a snapshot of the share.
            </summary>
            <returns>A share snapshot.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SnapshotAsync(System.Threading.CancellationToken)">
            <summary>
            Creates a snapshot of the share.
            </summary>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A share snapshot.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SnapshotAsync(System.Collections.Generic.IDictionary{System.String,System.String},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Creates a snapshot of the share.
            </summary>
            <param name="metadata">A collection of name-value pairs defining the metadata of the snapshot.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request, or <c>null</c>.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A share snapshot.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SnapshotAsync(System.Collections.Generic.IDictionary{System.String,System.String},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Creates a snapshot of the share.
            </summary>
            <param name="metadata">A collection of name-value pairs defining the metadata of the snapshot.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request, or <c>null</c>.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A share snapshot.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteAsync">
            <summary>
            Deletes the share.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Deletes the share.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Deletes the share.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteAsync(Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Deletes the share.
            </summary>
            <param name="deleteSnapshotsOption">A <see cref="T:Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption"/> object indicating whether to only delete the share or delete the share and all snapshots.</param>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteIfExistsAsync">
            <summary>
            Deletes the share if it already exists.
            </summary>
            <returns><c>true</c> if the share already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteIfExistsAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Deletes the share if it already exists.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the share already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteIfExistsAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Deletes the share if it already exists.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the share already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteIfExistsAsync(Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Deletes the share if it already exists.
            </summary>
            <param name="deleteSnapshotsOption">A <see cref="T:Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption"/> object indicating whether to only delete the share or delete the share and all snapshots.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the share already existed and was deleted; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.ExistsAsync">
            <summary>
            Checks existence of the share.
            </summary>
            <returns><c>true</c> if the share exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.ExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Checks existence of the share.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns><c>true</c> if the share exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.ExistsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Checks existence of the share.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns><c>true</c> if the share exists.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.FetchAttributesAsync">
            <summary>
            Retrieves the share's attributes.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.FetchAttributesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Retrieves the share's attributes.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.FetchAttributesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Retrieves the share's attributes.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetPermissionsAsync(Microsoft.Azure.Storage.File.FileSharePermissions)">
            <summary>
            Sets permissions for the share.
            </summary>
            <param name="permissions">The permissions to apply to the share.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetPermissionsAsync(Microsoft.Azure.Storage.File.FileSharePermissions,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Sets permissions for the share.
            </summary>
            <param name="permissions">The permissions to apply to the share.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetPermissionsAsync(Microsoft.Azure.Storage.File.FileSharePermissions,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Sets permissions for the share.
            </summary>
            <param name="permissions">The permissions to apply to the share.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetPropertiesAsync">
            <summary>
            Updates the share's properties.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetPropertiesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Updates the share's properties.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetPropertiesAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Updates the share's properties.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetPermissionsAsync">
            <summary>
            Gets the permissions settings for the share.
            </summary>
            <returns>The share's permissions.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetPermissionsAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Gets the permissions settings for the share.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The share's permissions.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetPermissionsAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Gets the permissions settings for the share.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The share's permissions.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetStatsAsync">
            <summary>
            Gets the stats of share.
            </summary>
            <returns>The share stats.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetStatsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Gets the stats of the share.
            </summary>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies execution options, such as retry policy and timeout settings, for the operation.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The share stats.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetStatsAsync(Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Gets the stats of the share.
            </summary>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies execution options, such as retry policy and timeout settings, for the operation.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <returns>The share stats.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetMetadataAsync">
            <summary>
            Sets the share's user-defined metadata.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetMetadataAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Sets the share's user-defined metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetMetadataAsync(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Threading.CancellationToken)">
            <summary>
            Sets the share's user-defined metadata.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateFilePermissionAsync(System.String,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary>
            Creates a File Permission.
            </summary>
            <param name="permission">The file permission to create</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request. If <c>null</c>, default options are applied to the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The file permission key</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetFilePermissionAsync(System.String,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext,System.Nullable{System.Threading.CancellationToken})">
            <summary>
            Gets a File Permission.
            </summary>
            <param name="filePermissionKey">Key of the file permission to retrieve.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request. If <c>null</c>, default options are applied to the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="cancellationToken">A <see cref="T:System.Threading.CancellationToken"/> to observe while waiting for a task to complete.</param>
            <returns>The file permission key</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateShareImpl(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the Create method.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that creates the share.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SnapshotImpl(System.Collections.Generic.IDictionary{System.String,System.String},Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the Snapshot method.
            </summary>
            <param name="metadata">A collection of name-value pairs defining the metadata of the snapshot, or null.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that creates the snapshot.</returns>
            <remarks>If the <c>metadata</c> parameter is <c>null</c> then no metadata is associated with the request.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.DeleteShareImpl(Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the Delete method.
            </summary>
            <param name="deleteSnapshotsOption">A <see cref="T:Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption"/> object indicating whether to only delete the share or delete the share and all snapshots.</param>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that deletes the share.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.FetchAttributesImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the FetchAttributes method.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that fetches the attributes.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.ExistsImpl(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the Exists method.
            </summary>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that checks existence.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetPermissionsImpl(Microsoft.Azure.Storage.File.FileSharePermissions,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the SetPermissions method.
            </summary>
            <param name="acl">The permissions to set.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that sets the permissions.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetPermissionsImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the GetPermissions method.
            </summary>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the share. If <c>null</c>, no condition is used.</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that gets the permissions.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetStatsImpl(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the GetStats method.
            </summary>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that gets the share stats.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetMetadataImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the SetMetadata method.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that sets the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.SetPropertiesImpl(Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the SetProperties method.
            </summary>
            <param name="accessCondition">An object that represents the access conditions for the share. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <returns>A <see cref="!:RESTCommand"/> that sets the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.CreateFilePermissionImp(System.String,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the CreatePermissions method.
            </summary>
            <param name="permission">Permission to create</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>File permission key of newly created file permission</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetFilePermissionImp(System.String,Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Implementation for the GetFilePermission method.
            </summary>
            <param name="filePermissionKey">Key of file permission to retrieve</param>
            <param name="options">A <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <returns>File permission key of newly created file permission</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.UpdateETagAndLastModified(System.Net.Http.HttpResponseMessage)">
            <summary>
            Retrieve ETag and LastModified date time from response.
            </summary>
            <param name="response">The response to parse.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.#ctor(System.Uri)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> class.
            </summary>
            <param name="shareAddress">The absolute URI to the share.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.#ctor(System.Uri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> class.
            </summary>
            <param name="shareAddress">The absolute URI to the share.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.#ctor(System.Uri,System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> class.
            </summary>
            <param name="shareAddress">The absolute URI to the share.</param>
            <param name="snapshotTime">A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.#ctor(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> class.
            </summary>
            <param name="shareAddress">The absolute URI to the share.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.#ctor(Microsoft.Azure.Storage.StorageUri,System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> class.
            </summary>
            <param name="shareAddress">The absolute URI to the share.</param>
            <param name="snapshotTime">A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.#ctor(System.String,System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.File.CloudFileClient)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare" /> class.
            </summary>
            <param name="shareName">The share name.</param>
            <param name="snapshotTime">A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</param>
            <param name="serviceClient">A client object that specifies the endpoint for the File service.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.#ctor(Microsoft.Azure.Storage.File.FileShareProperties,System.Collections.Generic.IDictionary{System.String,System.String},System.String,System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.File.CloudFileClient)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare" /> class.
            </summary>
            <param name="properties">The properties.</param>
            <param name="metadata">The metadata.</param>
            <param name="shareName">The share name.</param>
            <param name="snapshotTime">A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</param>
            <param name="serviceClient">The client to be used.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.ServiceClient">
            <summary>
            Gets the service client for the share.
            </summary>
            <value>A client object that specifies the endpoint for the File service.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.Uri">
            <summary>
            Gets the share's URI.
            </summary>
            <value>The absolute URI to the share.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.StorageUri">
            <summary>
            Gets the list of URIs for all locations.
            </summary>
            <value>The list of URIs for all locations.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.SnapshotTime">
            <summary>
            Gets the date and time that the share snapshot was taken, if this share is a snapshot.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> containing the share's snapshot time if the share is a snapshot; otherwise, <c>null</c>.</value>
            <remarks>
            If the share is not a snapshot, the value of this property is <c>null</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.IsSnapshot">
            <summary>
            Gets a value indicating whether this share is a snapshot.
            </summary>
            <value><c>true</c> if this share is a snapshot; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.SnapshotQualifiedUri">
            <summary>
            Gets the absolute URI to the share, including query string information if the share is a snapshot.
            </summary>
            <value>A <see cref="T:System.Uri"/> specifying the absolute URI to the share, including snapshot query information if the share is a snapshot.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.SnapshotQualifiedStorageUri">
            <summary>
            Gets the share's URI for both the primary and secondary locations, including query string information if the share is a snapshot.
            </summary>
            <value>An object of type <see cref="P:Microsoft.Azure.Storage.File.CloudFileShare.StorageUri"/> containing the share's URIs for both the primary and secondary locations, 
            including snapshot query information if the share is a snapshot.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.AssertNoSnapshot">
            <summary>
            Verifies that the share is not a snapshot.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.Name">
            <summary>
            Gets the name of the share.
            </summary>
            <value>The share's name.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.Metadata">
            <summary>
            Gets the share's metadata.
            </summary>
            <value>The share's metadata.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileShare.Properties">
            <summary>
            Gets the share's system properties.
            </summary>
            <value>The share's properties.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetSharedAccessCanonicalName">
            <summary>
            Returns the canonical name for shared access.
            </summary>
            <returns>The canonical name.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetSharedAccessSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy)">
            <summary>
            Returns a shared access signature for the share.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> object specifying the access policy for the shared access signature.</param>
            <returns>A shared access signature, as a URI query string.</returns>
            <remarks>The query string returned includes the leading question mark.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetSharedAccessSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy,System.String)">
            <summary>
            Returns a shared access signature for the share.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> object specifying the access policy for the shared access signature.</param>
            <param name="groupPolicyIdentifier">A share-level access policy.</param>
            <returns>A shared access signature, as a URI query string.</returns>
            <remarks>The query string returned includes the leading question mark.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetSharedAccessSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy,System.String,System.Nullable{Microsoft.Azure.Storage.SharedAccessProtocol},Microsoft.Azure.Storage.IPAddressOrRange)">
            <summary>
            Returns a shared access signature for the share.
            </summary>
            <param name="policy">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> object specifying the access policy for the shared access signature.</param>
            <param name="groupPolicyIdentifier">A share-level access policy.</param>
            <param name="protocols">The allowed protocols (https only, or http and https). Null if you don't want to restrict protocol.</param>
            <param name="ipAddressOrRange">The allowed IP address or IP address range. Null if you don't want to restrict based on IP address.</param>
            <returns>A shared access signature, as a URI query string.</returns>
            <remarks>The query string returned includes the leading question mark.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.ParseQueryAndVerify(Microsoft.Azure.Storage.StorageUri,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Parse URI for SAS (Shared Access Signature) information.
            </summary>
            <param name="address">The complete Uri.</param>
            <param name="credentials">The credentials to use.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileShare.GetRootDirectoryReference">
            <summary>
            Returns a reference to the root directory for this share.
            </summary>
            <returns>A reference to the root directory.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileReadStream">
            <summary>
            Provides an input stream to read a given file resource.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStream.#ctor(Microsoft.Azure.Storage.File.CloudFile,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.FileReadStream"/> class.
            </summary>
            <param name="file">File reference to read from.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStream.#ctor(Microsoft.Azure.Storage.File.FileReadStream)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.FileReadStream"/> class.
            </summary>
            <param name="otherStream">Another FileReadStream instance to clone.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileReadStream.ContentType">
            <summary>
            Gets the format of the data.
            </summary>
            <value>The format of the data.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads a sequence of bytes from the current stream and advances the
            position within the stream by the number of bytes read.
            </summary>
            <param name="buffer">The buffer to read the data into.</param>
            <param name="offset">The byte offset in buffer at which to begin writing
            data read from the stream.</param>
            <param name="count">The maximum number of bytes to read.</param>
            <returns>The total number of bytes read into the buffer. This can be
            less than the number of bytes requested if that many bytes are not
            currently available, or zero (0) if the end of the stream has been reached.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads a sequence of bytes from the current stream, advances the
            position within the stream by the number of bytes read, and monitors cancellation requests.
            </summary>
            <remarks>In the returned <see cref="T:System.Threading.Tasks.Task`1"/> object, the value of the integer
            parameter contains the total number of bytes read into the buffer. The result value can be
            less than the number of bytes requested if the number of bytes currently available is less
            than the requested number, or it can be 0 (zero) if the end of the stream has been reached.</remarks>
            <param name="buffer">The buffer to read the data into.</param>
            <param name="offset">The byte offset in buffer at which to begin writing
            data read from the stream.</param>
            <param name="count">The maximum number of bytes to read.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task that represents the asynchronous read operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStream.DispatchReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Dispatches a sync read operation that either reads from the cache or makes a call to
            the server.
            </summary>
            <param name="buffer">The buffer to read the data into.</param>
            <param name="offset">The byte offset in buffer at which to begin writing
            data read from the stream.</param>
            <param name="count">The maximum number of bytes to read.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>Number of bytes read from the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.#ctor(Microsoft.Azure.Storage.File.CloudFile,System.Int64,System.Boolean,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Initializes a new instance of the FileWriteStream class for a file.
            </summary>
            <param name="file">File reference to write to.</param>
            <param name="fileSize">Size of the file.</param>
            <param name="createNew">Use <c>true</c> if the file is newly created, <c>false</c> otherwise.</param>
            <param name="accessCondition">An object that represents the access conditions for the file. If null, no condition is used.</param>
            <param name="options">An object that specifies additional options for the request.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">A value of type <c>SeekOrigin</c> indicating the reference
            point used to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Writes a sequence of bytes to the current stream and advances the current
            position within this stream by the number of bytes written.
            </summary>
            <param name="buffer">An array of bytes. This method copies count bytes from
            buffer to the current stream.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin
            copying bytes to the current stream.</param>
            <param name="count">The number of bytes to be written to the current stream.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Asynchronously writes a sequence of bytes to the current stream, advances the current
            position within this stream by the number of bytes written, and monitors cancellation requests.
            </summary>
            <param name="buffer">An array of bytes. This method copies count bytes from
            buffer to the current stream.</param>
            <param name="offset">The zero-based byte offset in buffer at which to begin
            copying bytes to the current stream.</param>
            <param name="count">The number of bytes to be written to the current stream.</param>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.Flush">
            <summary>
            Clears all buffers for this stream and causes any buffered data to be written to the underlying file.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously clears all buffers for this stream, causes any buffered data to be written to the underlying device, and monitors cancellation requests.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests.</param>
            <returns>A task that represents the asynchronous flush operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.Dispose(System.Boolean)">
            <summary>
            Releases the file resources used by the Stream.
            </summary>
            <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.CommitAsync">
            <summary>
            Asynchronously clears all buffers for this stream, causes any buffered data to be written to the underlying file, and commits the file.
            </summary>
            <returns>A task that represents the asynchronous commit operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.DispatchWriteAsync">
            <summary>
            Asynchronously dispatches a write operation.
            </summary>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStream.WriteRangeAsync(System.IO.Stream,System.Int64,Microsoft.Azure.Storage.Shared.Protocol.Checksum)">
            <summary>
            Starts an asynchronous WriteRange operation as soon as the parallel
            operation semaphore becomes available.
            </summary>
            <param name="rangeData">Data to be uploaded</param>
            <param name="offset">Offset within the file</param>
            <returns>A task that represents the asynchronous write operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.Create(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileDirectoryProperties,System.String,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to create a new directory.
            </summary>
            <param name="uri">The absolute URI to the directory.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="properties">The properties to set for the directory.</param>
            <param name="filePermissionToSet">The file permissions to set for the directory.</param>
            <param name="content">HttpContent for the request</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="canonicalizer">A canonicalizer that converts HTTP request data into a standard form appropriate for signing.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object providing credentials for the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.Delete(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to delete the directory and all of the files within it.
            </summary>
            <param name="uri">The absolute URI to the directory.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.GetProperties(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to return the properties and user-defined metadata for this directory.
            </summary>
            <param name="uri">The absolute URI to the directory.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.GetMetadata(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to return the user-defined metadata for this directory.
            </summary>
            <param name="uri">The absolute URI to the directory.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.List(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.File.Protocol.FileListingContext,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to return a listing of all files and subdirectories in the directory.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="listingContext">A set of parameters for the listing operation.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.SetMetadata(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to set user-defined metadata for the directory.
            </summary>
            <param name="uri">The absolute URI to the directory.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.SetProperties(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileDirectoryProperties,System.String,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to set system properties for a directory.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="properties">The directory's properties.</param>
            <param name="filePermissionToSet">The file's file permission</param>
            <param name="content">HttpContent for the request</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext" /> object for tracking the current operation.</param>
            <param name="canonicalizer">A canonicalizer that converts HTTP request data into a standard form appropriate for signing.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object providing credentials for the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.AddMetadata(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Adds user-defined metadata to the request as one or more name-value pairs.
            </summary>
            <param name="request">The web request.</param>
            <param name="metadata">The user-defined metadata.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.AddShareSnapshot(Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Nullable{System.DateTimeOffset})">
            <summary>
            Adds the share snapshot.
            </summary>
            <param name="builder">An object of type <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> that contains additional parameters to add to the URI query string.</param>
            <param name="snapshot">The snapshot version, if the share is a snapshot.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.GetDirectoryUriQueryBuilder">
            <summary>
            Gets the directory Uri query builder.
            </summary>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> for the directory.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.AddFilePermissionOrFilePermissionKey(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String,Microsoft.Azure.Storage.File.FileDirectoryProperties,System.String)">
            <summary>
            Adds the File Permission or File Permission Key to a StorageRequest.
            </summary>
            <param name="request">The <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/></param>
            <param name="filePermissionToSet">The File Permission</param>
            <param name="properties">The <see cref="T:Microsoft.Azure.Storage.File.FileDirectoryProperties"/></param>
            <param name="defaultValue">The default value to set if fileermissionToSet and properties.filePermissionKeyToSet are null</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.AddNtfsFileAttributes(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.File.FileDirectoryProperties,System.String)">
            <summary>
            Adds the <see cref="T:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes"/> to the <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/>
            </summary>
            <param name="request">The <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/></param>
            <param name="properties">The <see cref="T:Microsoft.Azure.Storage.File.FileDirectoryProperties"/></param>
            <param name="defaultValue">The default value to set if properties.ntfsAttributesToSet is null</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.AddCreationTime(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.File.FileDirectoryProperties,System.String)">
            <summary>
            Adds the File Creation Time to the <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/>
            </summary>
            <param name="request">The <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/></param>
            <param name="properties">The <see cref="T:Microsoft.Azure.Storage.File.FileDirectoryProperties"/></param>
            <param name="defaultValue">The value to set if properties.creationTimeToSet is null</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpRequestMessageFactory.AddLastWriteTime(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.File.FileDirectoryProperties,System.String)">
            <summary>
            Adds the File Last Write Time to the <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/>
            </summary>
            <param name="request">The <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/></param>
            <param name="properties">The <see cref="T:Microsoft.Azure.Storage.File.FileDirectoryProperties"/></param>
            <param name="defaultValue">The default value to set if properties.lastWriteTimeToSet is null</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpResponseParsers.GetProperties(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets the directory's properties from the response.
            </summary>
            <param name="response">The web response.</param>
            <returns>The directory's attributes.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpResponseParsers.UpdateSmbProperties(System.Net.Http.HttpResponseMessage,Microsoft.Azure.Storage.File.FileDirectoryProperties)">
            <summary>
            Sets the SMB related file properties.
            </summary>
            <param name="response">The web response.</param>
            <param name="properties">The properties to modify.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.DirectoryHttpResponseParsers.GetMetadata(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets the user-defined metadata.
            </summary>
            <param name="response">The response from server.</param>
            <returns>A <see cref="!:IDictionary"/> of the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.Create(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileProperties,System.String,System.Int64,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to create a new file.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="properties">The properties to set for the file.</param>
            <param name="filePermissionToSet">The file permission to set for the file.</param>
            <param name="fileSize">For a file, the size of the file. This parameter is ignored
            for block files.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <param name="content">The HttpContent to set on the request</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object that represents the context for the current operation.</param>
            <param name="canonicalizer">A canonicalizer that converts HTTP request data into a standard form appropriate for signing.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object providing credentials for the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.GetProperties(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to return the file's system properties.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.GetMetadata(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to return the user-defined metadata for the file.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.SetMetadata(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to set user-defined metadata for the file.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AddMetadata(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Adds user-defined metadata to the request as one or more name-value pairs.
            </summary>
            <param name="request">The web request.</param>
            <param name="metadata">The user-defined metadata.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AddMetadata(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String,System.String)">
            <summary>
            Adds user-defined metadata to the request as a single name-value pair.
            </summary>
            <param name="request">The web request.</param>
            <param name="name">The metadata name.</param>
            <param name="value">The metadata value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.Delete(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to delete a file.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AddRange(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.Nullable{System.Int64},System.Nullable{System.Int64})">
            <summary>
            Adds the Range Header for File Service Operations.
            </summary>
            <param name="request">Request</param>
            <param name="offset">Starting byte of the range</param>
            <param name="count">Number of bytes in the range</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.Get(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to get the file's content, properties, and metadata.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.ListRanges(System.Uri,System.Nullable{System.Int32},System.Nullable{System.Int64},System.Nullable{System.Int64},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to return the list of valid ranges for a file.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="offset">The starting offset of the data range over which to list file ranges, in bytes.</param>
            <param name="count">The length of the data range over which to list file ranges, in bytes.</param>
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.ListHandles(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},System.Nullable{System.Int32},System.Nullable{System.Boolean},Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary> 
            Constructs a web request to return the list of open handles for a file or directory. 
            </summary> 
            <param name="uri">The absolute URI to the file.</param> 
            <param name="timeout">The server timeout interval.</param> 
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="maxResults">The maximum number of results to be returned by the server.</param> 
            <param name="recursive">Whether to recurse through a directory's files and subfolders.</param>
            <param name="nextMarker">Marker returned by a previous call to continue fetching results.</param> 
            <param name="accessCondition">The access condition to apply to the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext" /> object for tracking the current operation.</param> 
            <returns>A <see cref="!:System.Net.HttpWebRequest"/> object.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.CloseHandle(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},System.String,System.Nullable{System.Boolean},Microsoft.Azure.Storage.File.FileContinuationToken,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary> 
            Constructs a web request to close one or more open handles for a file or directory. 
            </summary> 
            <param name="uri">The absolute URI to the file.</param> 
            <param name="timeout">The server timeout interval.</param> 
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="handleId">ID of the handle to be closed, "*" if all should be closed.</param> 
            <param name="recursive">Whether to recurse through this directory's subfiles and folders.</param> 
            <param name="token">Continuation token for closing many handles.</param> 
            <param name="accessCondition">The access condition to apply to the request.</param> 
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext" /> object for tracking the current operation.</param> 
            <returns>A <see cref="!:System.Net.HttpWebRequest"/> object.</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.SetProperties(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileProperties,System.String,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to set system properties for a file.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="properties">The file's properties.</param>
            <param name="filePermissionToSet">The file's file permission</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <param name="content">HttpContent for the request</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext" /> object for tracking the current operation.</param>
            <param name="canonicalizer">A canonicalizer that converts HTTP request data into a standard form appropriate for signing.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object providing credentials for the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.Get(System.Uri,System.Nullable{System.Int32},System.Nullable{System.Int64},System.Nullable{System.Int64},Microsoft.Azure.Storage.Shared.Protocol.ChecksumRequested,System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to return a specified range of the file's content, together with its properties and metadata.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval, in seconds.</param>
            <param name="offset">The byte offset at which to begin returning content.</param>
            <param name="count">The number of bytes to return, or null to return all bytes through the end of the file.</param>
            <param name="shareSnapshot">A <see cref="T:System.DateTimeOffset"/> specifying the share snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.Resize(System.Uri,System.Nullable{System.Int32},System.Int64,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="newFileSize">The new file size. Set this parameter to <c>null</c> to keep the existing file size.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <param name="content">HttpContent for the request</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext" /> object for tracking the current operation.</param>
            <param name="canonicalizer">A canonicalizer that converts HTTP request data into a standard form appropriate for signing.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object providing credentials for the request.</param>
            <returns>A web request to use to perform the operation.</returns>
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.PutRange(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileRange,Microsoft.Azure.Storage.File.Protocol.FileRangeWrite,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to write or clear a range of pages in a file.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="fileRange">The beginning and ending offsets.</param>
            <param name="fileRangeWrite">Action describing whether we are writing to a file or clearing a set of ranges.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <param name="content">The corresponding Http content.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.PutRangeFromUrl(System.Uri,System.Uri,Microsoft.Azure.Storage.File.FileRange,Microsoft.Azure.Storage.File.FileRange,System.Nullable{System.Int32},Microsoft.Azure.Storage.Shared.Protocol.Checksum,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to write a range of pages in a file from the source file.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="sourceUri">A <see cref="T:System.Uri"/> specifying the absolute URI to the source file.</param>
            <param name="sourceFileRange">The beginning and ending source offsets.</param>
            <param name="destFileRange">The beginning and ending destination offsets.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="sourceContentChecksum">The checksum calculated for the range of bytes of the source.</param>
            <param name="sourceAccessCondition">The source access condition to apply to the request</param>
            <param name="content">The HTTP entity body and content headers.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="canonicalizer">A canonicalizer that converts HTTP request data into a standard form appropriate for signing.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object providing credentials for the request.</param>
            <returns></returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.CopyFrom(System.Uri,System.Nullable{System.Int32},System.Uri,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to copy.
            </summary>
            <param name="uri">The absolute URI to the destination file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="source">The absolute URI to the source object, including any necessary authentication parameters.</param>
            <param name="sourceAccessCondition">The access condition to apply to the source object.</param>
            <param name="destAccessCondition">The access condition to apply to the destination file.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.GetServiceProperties(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to get the properties of the service.
            </summary>
            <param name="uri">The absolute URI to the service.</param>
            <param name="timeout">The server timeout interval.</param>
            <returns>A StorageRequestMessage to get the service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.SetServiceProperties(System.Uri,System.Nullable{System.Int32},System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Creates a web request to set the properties of the service.
            </summary>
            <param name="uri">The absolute URI to the service.</param>
            <param name="timeout">The server timeout interval.</param>
            <returns>A web request to set the service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AbortCopy(System.Uri,System.Nullable{System.Int32},System.String,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to abort a copy operation.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="copyId">The ID string of the copy operation to be aborted.</param>
            <param name="accessCondition">The access condition to apply to the request.
                Only lease conditions are supported for this operation.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AddShareSnapshot(Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Nullable{System.DateTimeOffset})">
            <summary>
            Adds the share snapshot.
            </summary>
            <param name="builder">An object of type <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> that contains additional parameters to add to the URI query string.</param>
            <param name="snapshot">The snapshot version, if the share is a snapshot.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AddFilePermissionOrFilePermissionKey(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String,Microsoft.Azure.Storage.File.FileProperties,System.String)">
            <summary>
            Adds the File Permission or File Permission Key to a StorageRequest.
            </summary>
            <param name="request">The <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/></param>
            <param name="filePermissionToSet">The File Permission</param>
            <param name="properties">The <see cref="T:Microsoft.Azure.Storage.File.FileProperties"/></param>
            <param name="defaultValue">The default value to set if fileermissionToSet and properties.filePermissionKeyToSet are null</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AddNtfsFileAttributes(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.File.FileProperties,System.String)">
            <summary>
            Adds the <see cref="T:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes"/> to the <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/>
            </summary>
            <param name="request">The <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/></param>
            <param name="properties">The <see cref="T:Microsoft.Azure.Storage.File.FileProperties"/></param>
            <param name="defaultValue">The default value to set if properties.ntfsAttributesToSet is null</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AddCreationTime(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.File.FileProperties,System.String)">
            <summary>
            Adds the File Creation Time to the <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/>
            </summary>
            <param name="request">The <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/></param>
            <param name="properties">The <see cref="T:Microsoft.Azure.Storage.File.FileProperties"/></param>
            <param name="defaultValue">The value to set if properties.creationTimeToSet is null</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpRequestMessageFactory.AddLastWriteTime(Microsoft.Azure.Storage.Core.StorageRequestMessage,Microsoft.Azure.Storage.File.FileProperties,System.String)">
            <summary>
            Adds the File Last Write Time to the <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/>
            </summary>
            <param name="request">The <see cref="T:Microsoft.Azure.Storage.Core.StorageRequestMessage"/></param>
            <param name="properties">The <see cref="T:Microsoft.Azure.Storage.File.FileProperties"/></param>
            <param name="defaultValue">The default value to set if properties.lastWriteTimeToSet is null</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpResponseParsers.GetProperties(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets the file's properties from the response.
            </summary>
            <param name="response">The web response.</param>
            <returns>The file's properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpResponseParsers.UpdateSmbProperties(System.Net.Http.HttpResponseMessage,Microsoft.Azure.Storage.File.FileProperties)">
            <summary>
            Sets the SMB related file properties.
            </summary>
            <param name="response">The web response.</param>
            <param name="properties">The properties to modify.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpResponseParsers.GetMetadata(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets the user-defined metadata.
            </summary>
            <param name="response">The response from server.</param>
            <returns>A <see cref="!:IDictionary"/> of the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpResponseParsers.GetCopyAttributes(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Builds a <see cref="T:Microsoft.Azure.Storage.File.CopyState"/> object from the given strings containing formatted copy information.
            </summary>
            <param name="copyStatusString">The copy status, as a string.</param>
            <param name="copyId">The copy ID.</param>
            <param name="copySourceString">The source URI of the copy, as a string.</param>
            <param name="copyProgressString">A string formatted as progressBytes/TotalBytes.</param>
            <param name="copyCompletionTimeString">The copy completion time, as a string, or <c>null</c>.</param>
            <param name="copyStatusDescription">The copy status description, if any.</param>
            <param name="copyDestinationSnapshotTimeString">The incremental destination snapshot time for the latest incremental copy</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.File.CopyState"/> object populated from the given strings.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpResponseParsers.GetCopyAttributes(System.Net.Http.HttpResponseMessage)">
            <summary>
            Extracts a <see cref="T:Microsoft.Azure.Storage.File.CopyState"/> object from the headers of a web response.
            </summary>
            <param name="response">The HTTP web response.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.File.CopyState"/> object, or null if the web response does not contain a copy status.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpResponseParsers.ReadServicePropertiesAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads service properties from a stream.
            </summary>
            <param name="inputStream">The stream from which to read the service properties.</param>
            <returns>The service properties stored in the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileHttpResponseParsers.ReadServiceStatsAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads service stats from a stream.
            </summary>
            <param name="inputStream">The stream from which to read the service stats.</param>
            <returns>The service stats stored in the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.Create(System.Uri,Microsoft.Azure.Storage.File.FileShareProperties,System.Nullable{System.Int32},System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to create a new share.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="properties">Properties to set on the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.Delete(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to delete the share and all of the files within it.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="snapshot">A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</param>
            <param name="deleteSnapshotsOption">A <see cref="T:Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption"/> object indicating whether to only delete the share or delete the share and all snapshots.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.GetMetadata(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to return the user-defined metadata for this share.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="snapshot">A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.GetProperties(System.Uri,System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to return the properties and user-defined metadata for this share.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="snapshot">A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.SetMetadata(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to set user-defined metadata for the share.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.SetProperties(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileShareProperties,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to set system properties for a share.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="properties">The share's properties.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.AddMetadata(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.Collections.Generic.IDictionary{System.String,System.String})">
            <summary>
            Adds user-defined metadata to the request as one or more name-value pairs.
            </summary>
            <param name="request">The web request.</param>
            <param name="metadata">The user-defined metadata.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.AddMetadata(Microsoft.Azure.Storage.Core.StorageRequestMessage,System.String,System.String)">
            <summary>
            Adds user-defined metadata to the request as a single name-value pair.
            </summary>
            <param name="request">The web request.</param>
            <param name="name">The metadata name.</param>
            <param name="value">The metadata value.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.List(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.Shared.Protocol.ListingContext,Microsoft.Azure.Storage.File.ShareListingDetails,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to return a listing of all shares in this storage account.
            </summary>
            <param name="uri">The absolute URI for the account.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="listingContext">A set of parameters for the listing operation.</param>
            <param name="detailsIncluded">Additional details to return with the listing.</param>
            <returns>A web request for the specified operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.GetAcl(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to return the ACL for a share.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns><returns>A web request to use to perform the operation.</returns></returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.SetAcl(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.File.FileSharePublicAccessType,Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to set the ACL for a share.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="publicAccess">The type of public access to allow for the share.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns><returns>A web request to use to perform the operation.</returns></returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.GetStats(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to get the stats of the service.
            </summary>
            <param name="uri">The absolute URI to the service.</param>
            <param name="timeout">The server timeout interval.</param>
            <returns>A StorageRequestMessage to get the service stats.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.Snapshot(System.Uri,System.Nullable{System.Int32},Microsoft.Azure.Storage.AccessCondition,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Constructs a web request to create a snapshot of a share.
            </summary>
            <param name="uri">The absolute URI to the share.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="accessCondition">The access condition to apply to the request.</param>
            <returns>A web request to use to perform the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.CreateFilePermission(System.Uri,System.Nullable{System.Int32},System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to create a file permission.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="content">The corresponding Http content.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="canonicalizer">A canonicalizer that converts HTTP request data into a standard form appropriate for signing.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object providing credentials for the request.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.GetFilePermission(System.Uri,System.Nullable{System.Int32},System.String,System.Net.Http.HttpContent,Microsoft.Azure.Storage.OperationContext,Microsoft.Azure.Storage.Core.Auth.ICanonicalizer,Microsoft.Azure.Storage.Auth.StorageCredentials)">
            <summary>
            Generates a web request to get a file permission.
            </summary>
            <param name="uri">The absolute URI to the file.</param>
            <param name="timeout">The server timeout interval.</param>
            <param name="filePermissionKey">The file permission key of the permission to retrieve.</param>
            <param name="content">The corresponding Http content.</param>
            <param name="operationContext">An object that represents the context for the current operation.</param>
            <param name="canonicalizer">A canonicalizer that converts HTTP request data into a standard form appropriate for signing.</param>
            <param name="credentials">A <see cref="T:Microsoft.Azure.Storage.Auth.StorageCredentials"/> object providing credentials for the request.</param>
            <returns>A web request for performing the operation.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.AddShareSnapshot(Microsoft.Azure.Storage.Core.UriQueryBuilder,System.Nullable{System.DateTimeOffset})">
            <summary>
            Adds the share snapshot.
            </summary>
            <param name="builder">An object of type <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> that contains additional parameters to add to the URI query string.</param>
            <param name="snapshot">The snapshot version, if the share is a snapshot.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpRequestMessageFactory.GetShareUriQueryBuilder">
            <summary>
            Gets the share Uri query builder.
            </summary>
            <returns>A <see cref="T:Microsoft.Azure.Storage.Core.UriQueryBuilder"/> for the share.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.ShareHttpResponseParsers">
            <summary>
            Provides a set of methods for parsing share responses from the File service.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpResponseParsers.GetProperties(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets the share's properties from the response.
            </summary>
            <param name="response">The web response.</param>
            <returns>The share's attributes.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpResponseParsers.GetMetadata(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets the user-defined metadata.
            </summary>
            <param name="response">The response from server.</param>
            <returns>A <see cref="!:IDictionary"/> of the metadata.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpResponseParsers.ReadSharedAccessIdentifiersAsync(System.IO.Stream,Microsoft.Azure.Storage.File.FileSharePermissions,System.Threading.CancellationToken)">
            <summary>
            Reads the share access policies from a stream in XML.
            </summary>
            <param name="inputStream">The stream of XML policies.</param>
            <param name="permissions">The permissions object to which the policies are to be written.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpResponseParsers.GetSnapshotTime(System.Net.Http.HttpResponseMessage)">
            <summary>
            Gets the snapshot timestamp from the response.
            </summary>
            <param name="response">The web response.</param>
            <returns>The snapshot timestamp.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpResponseParsers.ReadShareStats(System.IO.Stream)">
            <summary>
            Reads share stats from a stream.
            </summary>
            <param name="inputStream">The stream from which to read the share stats.</param>
            <returns>The share stats stored in the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareHttpResponseParsers.ReadShareStatsAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Reads share stats from a stream.
            </summary>
            <param name="inputStream">The stream from which to read the share stats.</param>
            <returns>The share stats stored in the stream.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.FileAccessPolicyResponse">
            <summary>
            Parses the response XML from an operation to set the access policy for a share.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileAccessPolicyResponse.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the FileAccessPolicyResponse class.
            </summary>
            <param name="stream">The stream to be parsed.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileAccessPolicyResponse.ParseElement(System.Xml.Linq.XElement)">
            <summary>
            Parses the current element.
            </summary>
            <param name="accessPolicyElement">The shared access policy element to parse.</param>
            <returns>The shared access policy.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings">
            <summary>
            Provides error code strings that are specific to the File service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ShareNotFound">
            <summary>
            The specified share was not found.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ShareAlreadyExists">
            <summary>
            The specified share already exists.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ShareDisabled">
            <summary>
            The specified share is disabled.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ShareBeingDeleted">
            <summary>
            The specified share is being deleted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.DeletePending">
            <summary>
            The specified resource is marked for deletion by an SMB client.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ParentNotFound">
            <summary>
            The specified parent was not found.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.InvalidResourceName">
            <summary>
            The specified resource name contains invalid characters.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ResourceAlreadyExists">
            <summary>
            The specified resource already exists.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ResourceTypeMismatch">
            <summary>
            The specified resource type does not match the type of the existing resource.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.SharingViolation">
            <summary>
            The specified resource may be in use by an SMB client.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.CannotDeleteFileOrDirectory">
            <summary>
            The file or directory could not be deleted because it is in use by an SMB client.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.FileLockConflict">
            <summary>
            A portion of the specified file is locked by an SMB client.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ReadOnlyAttribute">
            <summary>
            The specified resource is read-only and cannot be modified at this time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ClientCacheFlushDelay">
            <summary>
            The specified resource state could not be flushed from an SMB client in the specified time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.InvalidFileOrDirectoryPathName">
            <summary>
            File or directory path is too long.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileErrorCodeStrings.ConditionHeadersNotSupported">
            <summary>
            Condition headers are not supported.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.FileListingContext">
            <summary>
            Provides a set of parameters for a file listing operation.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileListingContext.#ctor(System.Nullable{System.Int32})">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.Protocol.FileListingContext"/> class.
            </summary>
            <param name="maxResults">The maximum number of results to return.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.FileRangeWrite">
            <summary>
            Describes actions that may be used for writing to a file or clearing a set of ranges.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileRangeWrite.Update">
            <summary>
            Update the file range with new data.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.FileRangeWrite.Clear">
            <summary>
            Clear the file range.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.FileRequest">
            <summary>
            Provides a set of helper methods for constructing a request against the File service.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileRequest.WriteSharedAccessIdentifiers(Microsoft.Azure.Storage.File.SharedAccessFilePolicies,System.IO.Stream)">
            <summary>
            Writes a collection of shared access policies to the specified stream in XML format.
            </summary>
            <param name="sharedAccessPolicies">A collection of shared access policies.</param>
            <param name="outputStream">An output stream.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties">
            <summary>
            Class representing a set of properties pertaining to the Azure File service.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties.#ctor(Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties,Microsoft.Azure.Storage.Shared.Protocol.MetricsProperties,Microsoft.Azure.Storage.Shared.Protocol.CorsProperties)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties.Cors">
            <summary>
            Gets or sets the Cross Origin Resource Sharing (CORS) properties for the File service.
            </summary>
            <value>The CORS properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties.HourMetrics">
            <summary>
            Gets or sets the hour metrics properties.
            </summary>
            <value>The metrics properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties.MinuteMetrics">
            <summary>
            Gets or sets the minutes metrics properties.
            </summary>
            <value>The metrics properties.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties.FromServiceXml(System.Xml.Linq.XDocument)">
            <summary>
            Constructs a <c>ServiceProperties</c> object from an XML document received from the service.
            </summary>
            <param name="servicePropertiesDocument">The XML document.</param>
            <returns>A <c>ServiceProperties</c> object containing the properties in the XML document.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties.ToServiceXml">
            <summary>
            Converts these properties into XML for communicating with the service.
            </summary>
            <returns>An XML document containing the service properties.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileServiceProperties.WriteServiceProperties(System.IO.Stream)">
            <summary>
            Writes service properties to a stream, formatted in XML.
            </summary>
            <param name="outputStream">The stream to which the formatted properties are to be written.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.FileShareEntry">
            <summary>
            Represents a share item returned in the XML response for a share listing operation.
            </summary>
            
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.FileShareEntry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.Protocol.FileShareEntry"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.FileShareEntry.Metadata">
            <summary>
            Gets the user-defined metadata for the share.
            </summary>
            <value>The share's metadata, as a collection of name-value pairs.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.FileShareEntry.Properties">
            <summary>
            Gets the share's system properties.
            </summary>
            <value>The share's properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.FileShareEntry.Name">
            <summary>
            Gets the name of the share.
            </summary>
            <value>The share's name.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.FileShareEntry.Uri">
            <summary>
            Gets the share's URI.
            </summary>
            <value>The absolute URI to the share.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.FileShareEntry.SnapshotTime">
            <summary>
            Gets the share's snapshot time, if any.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> specifying the snapshot timestamp, if the share is a snapshot.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.IListFileEntry">
            <summary>
            Represents an item that may be returned by a file listing operation.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.IListFileEntry.Name">
            <summary>
            Gets the name of the file/directory item.
            </summary>
            <value>The name of the file/directory item.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.ListFileDirectoryEntry">
            <summary>
            Represents a directory item that is returned in the XML response for a file listing operation.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListFileDirectoryEntry.#ctor(System.String,System.Uri,Microsoft.Azure.Storage.File.FileDirectoryProperties)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.Protocol.ListFileDirectoryEntry"/> class.
            </summary>
            <param name="name">The name of the directory.</param>
            <param name="uri">The Uri of the directory.</param>
            <param name="properties">The directory's properties.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFileDirectoryEntry.Name">
            <summary>
            Gets the name of the directory item.
            </summary>
            <value>The name of the directory item.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFileDirectoryEntry.Uri">
            <summary>
            Gets the directory address.
            </summary>
            <value>The directory URL.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFileDirectoryEntry.Properties">
            <summary>
            Gets the directory item's properties.
            </summary>
            <value>The directory item's properties.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.ListFileEntry">
            <summary>
            Represents a file item returned in the XML response for a file listing operation.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListFileEntry.#ctor(System.String,Microsoft.Azure.Storage.File.CloudFileAttributes)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.Protocol.ListFileEntry"/> class.
            </summary>
            <param name="name">The name of the file.</param>
            <param name="attributes">The file's attributes.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFileEntry.Attributes">
            <summary>
            Stores the file item's attributes.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFileEntry.Name">
            <summary>
            Gets the name of the file item.
            </summary>
            <value>The name of the file item.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFileEntry.Properties">
            <summary>
            Gets the file item's system properties.
            </summary>
            <value>The file item's properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFileEntry.Metadata">
            <summary>
            Gets the user-defined metadata for the file item.
            </summary>
            <value>The file item's metadata, as a collection of name-value pairs.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFileEntry.Uri">
            <summary>
            Gets the file item's URI.
            </summary>
            <value>The absolute URI to the file item.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.ListFilesAndDirectoriesResponse">
            <summary>
            Provides methods for parsing the response from a file listing operation.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFilesAndDirectoriesResponse.Files">
            <summary>
            Gets an enumerable collection of objects that implement <see cref="T:Microsoft.Azure.Storage.File.Protocol.IListFileEntry"/> from the response.
            </summary>
            <value>An enumerable collection of objects that implement <see cref="T:Microsoft.Azure.Storage.File.Protocol.IListFileEntry"/>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListFilesAndDirectoriesResponse.NextMarker">
            <summary>
            Gets the NextMarker value from the XML response, if the listing was not complete.
            </summary>
            <value>The NextMarker value.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListFilesAndDirectoriesResponse.ParseFileEntryAsync(System.Xml.XmlReader,System.Uri,System.Threading.CancellationToken)">
            <summary>
            Parses a file entry in a file listing response.
            </summary>
            <returns>File listing entry</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListFilesAndDirectoriesResponse.ParseFileDirectoryEntryAsync(System.Xml.XmlReader,System.Uri,System.Threading.CancellationToken)">
            <summary>
            Parses a file directory entry in a file listing response.
            </summary>
            <returns>File listing entry</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListFilesAndDirectoriesResponse.ParseAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Parses the response XML for a file listing operation.
            </summary>
            <returns>An enumerable collection of objects that implement <see cref="T:Microsoft.Azure.Storage.File.Protocol.IListFileEntry"/>.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.ListHandlesResponse">
            <summary> 
            Provides methods for parsing the response from an operation to get a range for a file. 
            </summary> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListHandlesResponse.#ctor(System.IO.Stream)">
            <summary> 
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.Protocol.ListHandlesResponse"/> class. 
            </summary> 
            <param name="stream">The stream of handles to be parsed.</param> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListHandlesResponse.Handles">
            <summary> 
            Gets an enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.FileHandle"/> objects from the response. 
            </summary> 
            <value>An enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.FileHandle"/> objects.</value> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListHandlesResponse.NextMarker">
            <summary> 
            Gets the NextMarker value from the XML response, if the listing was not complete. 
            </summary> 
            <value>A string containing the NextMarker value, should one exist.</value> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListHandlesResponse.MaxResults">
            <summary> 
            Gets the MaxResults value from the XML response, if one was specified. 
            </summary> 
            <value>A nullable int containing the MaxResults value, should one exist.</value> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListHandlesResponse.ParseHandle">
            <summary> 
            Reads a handle. 
            </summary> 
            <returns>Range entry</returns> 
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListHandlesResponse.ParseXml">
            <summary> 
            Parses the XML response for an operation to get a handle for a file. 
            </summary> 
            <returns>An enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.FileHandle"/> objects.</returns> 
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.ListRangesResponse">
            <summary>
            Provides methods for parsing the response from an operation to get a range for a file.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListRangesResponse.ParseRangeAsync(System.Xml.XmlReader,System.Threading.CancellationToken)">
            <summary>
            Reads a range.
            </summary>
            <returns>Range entry</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListRangesResponse.ParseAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Parses the XML response for an operation to get a range for a file.
            </summary>
            <returns>An enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.FileRange"/> objects.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.ListSharesResponse">
            <summary>
            Provides methods for parsing the response from a share listing operation.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListSharesResponse.Shares">
            <summary>
            Gets an enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.Protocol.FileShareEntry"/> objects from the response.
            </summary>
            <value>An enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.Protocol.FileShareEntry"/> objects.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ListSharesResponse.NextMarker">
            <summary>
            Gets the NextMarker value from the XML response, if the listing was not complete.
            </summary>
            <value>The NextMarker value.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListSharesResponse.ParseShareEntryAsync(System.Xml.XmlReader,System.Uri,System.Threading.CancellationToken)">
            <summary>
            Reads a share entry completely including its properties and metadata.
            </summary>
            <returns>Share listing entry</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ListSharesResponse.ParseAsync(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Parses the response XML for a share listing operation.
            </summary>
            <returns>An enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.Protocol.FileShareEntry"/> objects.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.Protocol.ShareStats">
            <summary>
            Class representing a set of stats pertaining to a File Share.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.ShareStats.ShareStatsName">
            <summary>
            The name of the root XML element.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.Protocol.ShareStats.ShareUsageBytes">
            <summary>
            The name of the share usage XML element.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareStats.#ctor">
            <summary>
            Initializes a new instance of the ServiceStats class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ShareStats.Usage">
            <summary>
            Gets the share usage in GB, rounded up.
            </summary>
            <value>The share usage, in GB.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.Protocol.ShareStats.UsageInBytes">
            <summary>
            Gets the share usage in bytes.
            </summary>
            <value>The share usage, in bytes.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.Protocol.ShareStats.FromServiceXml(System.Xml.Linq.XDocument)">
            <summary>
            Constructs a <c>ShareStats</c> object from an XML document received from the service.
            </summary>
            <param name="shareStatsDocument">The XML document.</param>
            <returns>A <c>ShareStats</c> object containing the properties in the XML document.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileAccountExtensions.CreateCloudFileClient(Microsoft.Azure.Storage.CloudStorageAccount)">
            <summary>
            Creates the File service client.
            </summary>
            <returns>A <see cref="T:Microsoft.Azure.Storage.File.CloudFileClient"/> object.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.CloseFileHandleResultSegment">
            <summary> 
            Represents a close file handles result, with continuation information for pagination scenarios. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloseFileHandleResultSegment.NumHandlesClosed">
            <summary> 
            The number of handles closed on this request. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloseFileHandleResultSegment.ContinuationToken">
            <summary> 
            Gets the continuation token used to continue the close operation, should it not have completed within one request. 
            </summary> 
            <value>A <see cref="T:Microsoft.Azure.Storage.File.FileContinuationToken"/> object.</value> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileAttributes.Properties">
            <summary>
            Gets the file's system properties.
            </summary>
            <value>The file's properties.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileAttributes.Metadata">
            <summary>
            Gets the user-defined metadata for the file.
            </summary>
            <value>The file's metadata, as a collection of name-value pairs.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileAttributes.Uri">
            <summary>
            Gets the file's URI.
            </summary>
            <value>The absolute URI to the file.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileAttributes.StorageUri">
            <summary>
            Gets the list of URIs for all locations.
            </summary>
            <value>The list of URIs for all locations.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CloudFileAttributes.CopyState">
            <summary>
            Gets the state of the most recent or pending copy operation.
            </summary>
            <value>A <see cref="P:Microsoft.Azure.Storage.File.CloudFileAttributes.CopyState"/> object containing the copy state, or <c>null</c> if no copy file state exists for this file.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes">
            <summary> 
            Represents a range in a file. 
            </summary> 
            <summary>
            Provides attributes for files and directories.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.None">
            <summary>
            Clear all flags.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.ReadOnly">
            <summary>
            The file is read-only.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.Hidden">
            <summary>
            The file is hidden, and thus is not included in an ordinary directory listing.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.System">
            <summary>
            The file is a system file. That is, the file is part of the operating system
            or is used exclusively by the operating system.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.Normal">
            <summary>
            The file is a standard file that has no special attributes. This attribute is
            valid only if it is used alone.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.Directory">
            <summary>
            The file is a directory.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.Archive">
            <summary>
            The file is a candidate for backup or removal.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.Temporary">
            <summary>
            The file is temporary. A temporary file contains data that is needed while an
            application is executing but is not needed after the application is finished.
            File systems try to keep all the data in memory for quicker access rather than
            flushing the data back to mass storage. A temporary file should be deleted by
            the application as soon as it is no longer needed.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.Offline">
            <summary>
            The file is offline. The data of the file is not immediately available.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.NotContentIndexed">
            <summary>
            The file will not be indexed by the operating system's content indexing service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes.NoScrubData">
            <summary>
            The file or directory is excluded from the data integrity scan. When this value
            is applied to a directory, by default, all new files and subdirectories within
            that directory are excluded from data integrity.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.CloudFileNtfsAttributesHelper">
            <summary>
            CloudFileNtfsAttributesHelper helper.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileNtfsAttributesHelper.ToString(Microsoft.Azure.Storage.File.CloudFileNtfsAttributes)">
            <summary>
            Converts a CloudFileNtfsAttributes to a string
            </summary>
            <param name="attributes"><see cref="T:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes"/></param>
            <returns>string</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileNtfsAttributesHelper.ToAttributes(System.String)">
            <summary>
            Parses an attributes string to a <see cref="T:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes"/>
            </summary>
            <param name="attributesString">string</param>
            <returns><see cref="T:Microsoft.Azure.Storage.File.CloudFileNtfsAttributes"/></returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption">
            <summary>
            The set of options describing delete operation.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption.None">
            <summary>
            Delete the share only. If the share has snapshots, this option will result in an error from the service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.DeleteShareSnapshotsOption.IncludeSnapshots">
            <summary>
            Delete the share and its snapshots.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileContinuationToken">
            <summary>
            Represents a continuation token for listing operations. 
            </summary>
            <remarks><see cref="T:Microsoft.Azure.Storage.File.FileContinuationToken"/> continuation tokens are used in methods that return a <see cref="T:Microsoft.Azure.Storage.File.FileResultSegment"/> object, such as <see cref="!:CloudFileDirectory.ListFilesAndDirectoriesSegmented(FileContinuationToken)"/>.</remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileContinuationToken.Version">
            <summary>
            Gets or sets the version for continuing results for <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> enumeration operations.
            </summary>
            <value>The version.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileContinuationToken.Type">
            <summary>
            Gets or sets the type element (blob, queue, table, file) for continuing results for <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> enumeration operations.
            </summary>
            <value>The type element.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileContinuationToken.NextMarker">
            <summary>
            Gets or sets the next marker for continuing results for <see cref="T:Microsoft.Azure.Storage.File.CloudFile"/> enumeration operations.
            </summary>
            <value>The next marker.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileContinuationToken.TargetLocation">
            <summary>
            Gets or sets the storage location that the continuation token applies to.
            </summary>
            <value>The storage location that the continuation token applies to.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileDirectoryProperties">
            <summary>
            Represents the system properties for a directory.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.ETag">
            <summary>
            Gets the ETag value for the directory.
            </summary>
            <value>The directory's quoted ETag value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.LastModified">
            <summary>
            Gets the directory's last-modified time.
            </summary>
            <value>The directory's last-modified time.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.IsServerEncrypted">
            <summary>
            Gets the directory's server-side encryption state.
            </summary>
            <value>A bool representing the directory's server-side encryption state.</value>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileDirectoryProperties.filePermissionKey">
            <summary>
            The directory's File Permission Key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileDirectoryProperties.filePermissionKeyToSet">
            <summary>
            The file permission key to set on the next Directory Create or Set Properties call.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.FilePermissionKey">
            <summary>
            Gets or sets the directory's File Permission Key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileDirectoryProperties.ntfsAttributes">
            <summary>
            The file system attributes for this directory.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileDirectoryProperties.ntfsAttributesToSet">
            <summary>
            The file system attributes to set on the next Directory Create or Set Properties call.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.NtfsAttributes">
            <summary>
            Gets or sets the file system attributes for this directory
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileDirectoryProperties.creationTime">
            <summary>
            The <see cref="T:System.DateTimeOffset"/> when the File or Directory was created.  Read only.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileDirectoryProperties.creationTimeToSet">
            <summary>
            The directory creation time to set on the next Directory Create or Set Properties call.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.CreationTime">
            <summary>
            Gets or sets the <see cref="T:System.DateTimeOffset"/> when the File or Directory was created.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileDirectoryProperties.lastWriteTime">
            <summary>
            The <see cref="T:System.DateTime"/> when the Directory was last modified.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileDirectoryProperties.lastWriteTimeToSet">
            <summary>
            The directory last write time to set on the next Directory Create or Set Properties call.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.LastWriteTime">
            <summary>
            Gets or sets the <see cref="T:System.DateTimeOffset"/> when the Directory was last modified.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.ChangeTime">
            <summary>
            The <see cref="T:System.DateTimeOffset"/> when the File was last changed.  Ready only.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.DirectoryId">
            <summary>
            The Id of this directory.  Ready only.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileDirectoryProperties.ParentId">
            <summary>
            The Id of this directory's parent.  Read only.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileHandle">
            <summary> 
            Represents a range in a file. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.HandleId">
            <summary> 
            SMB service handle ID. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.Path">
            <summary> 
            File or directory name including full path starting from share root. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.ClientIp">
            <summary> 
            Client IP that opened the handle. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.ClientPort">
            <summary> 
            Client port that opened the handle. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.OpenTime">
            <summary> 
            Time the handle was opened. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.LastReconnectTime">
            <summary> 
            Time the handle was last connected.
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.FileId">
            <summary> 
            Unique file ID. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.ParentId">
            <summary> 
            Parent's unique file ID. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandle.SessionId">
            <summary> 
            SMB session ID in context of which the file handle was opened. 
            </summary> 
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileHandleResultSegment">
            <summary> 
            Represents a segment of <see cref="T:Microsoft.Azure.Storage.File.FileHandle"/> results, with continuation information for pagination scenarios. 
            </summary> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandleResultSegment.Results">
            <summary> 
            Gets an enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.FileHandle"/> results. 
            </summary> 
            <value>An enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.FileHandle"/> objects.</value> 
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileHandleResultSegment.ContinuationToken">
            <summary> 
            Gets the continuation token used to retrieve the next segment of <see cref="T:Microsoft.Azure.Storage.File.FileHandle"/> results. Returns <c>null</c> if there are no more results. 
            </summary> 
            <value>A <see cref="T:Microsoft.Azure.Storage.File.FileContinuationToken"/> object.</value> 
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileProperties">
            <summary>
            Represents the system properties for a file.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileProperties.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.FileProperties"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileProperties.#ctor(Microsoft.Azure.Storage.File.FileProperties)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.FileProperties"/> class based on an existing instance.
            </summary>
            <param name="other">The set of file properties to clone.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.CacheControl">
            <summary>
            Gets or sets the cache-control value stored for the file.
            </summary>
            <value>The file's cache-control value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ContentDisposition">
            <summary>
            Gets or sets the content-disposition value stored for the file.
            </summary>
            <value>The file's content-disposition value.</value>
            <remarks>
            If this property has not been set for the file, it returns null.
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ContentEncoding">
            <summary>
            Gets or sets the content-encoding value stored for the file.
            </summary>
            <value>The file's content-encoding value.</value>
            <remarks>
            If this property has not been set for the file, it returns <c>null</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ContentLanguage">
            <summary>
            Gets or sets the content-language value stored for the file.
            </summary>
            <value>The file's content-language value.</value>
            <remarks>
            If this property has not been set for the file, it returns <c>null</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.Length">
            <summary>
            Gets the size of the file, in bytes.
            </summary>
            <value>The file's size in bytes.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ContentMD5">
            <summary>
            Gets or sets the content-MD5 value stored for the file.
            </summary>
            <value>The file's content-MD5 hash.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ContentChecksum">
            <summary>
            Gets or sets the content checksum value stored for the file.
            </summary>
            <value>A Checksum instance containing the blob's content checksum values.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ContentType">
            <summary>
            Gets or sets the content-type value stored for the file.
            </summary>
            <value>The file's content-type value.</value>
            <remarks>
            If this property has not been set for the file, it returns <c>null</c>.
            </remarks>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ETag">
            <summary>
            Gets the file's ETag value.
            </summary>
            <value>The file's ETag value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.LastModified">
            <summary>
            Gets the the last-modified time for the file, expressed as a UTC value.
            </summary>
            <value>The file's last-modified time, in UTC format.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.IsServerEncrypted">
            <summary>
            Gets the file's server-side encryption state.
            </summary>
            <value>A bool representing the file's server-side encryption state.</value>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileProperties.filePermissionKey">
            <summary>
            The file's File Permission Key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileProperties.filePermissionKeyToSet">
            <summary>
            The file permission key to set on the next File Create or Set Properties call.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.FilePermissionKey">
            <summary>
            Gets or sets the file's File Permission Key.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileProperties.ntfsAttributes">
            <summary>
            Get or sets the file system attributes for files and directories.
            If not set, indicates preservation of existing values.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileProperties.ntfsAttributesToSet">
            <summary>
            The NTFS file attributes to set on the new File Create or Set Properties call.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.NtfsAttributes">
            <summary>
            Get or sets the file system attributes for files and directories.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileProperties.creationTime">
            <summary>
            The <see cref="T:System.DateTimeOffset"/> when the File was created.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileProperties.creationTimeToSet">
            <summary>
            The file creation time to set on the next Create File or Set Properties call.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.CreationTime">
            <summary>
            Gets or sets the <see cref="T:System.DateTimeOffset"/> when the File was created.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileProperties.lastWriteTime">
            <summary>
            The <see cref="T:System.DateTimeOffset"/> when the File was last written to.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileProperties.lastWriteTimeToSet">
            <summary>
            The last write time to set on the next Create or Update Properties call.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.LastWriteTime">
            <summary>
            Gets or sets the <see cref="T:System.DateTimeOffset"/> when the File was last written to.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ChangeTime">
            <summary>
            The <see cref="T:System.DateTimeOffset"/> when the File was last changed.  Read only.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.FileId">
            <summary>
            The File Id.  Read only.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileProperties.ParentId">
            <summary>
            The Id of this file's parent.  Ready only.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileRange">
            <summary>
            Represents a range in a file.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileRange.#ctor(System.Int64,System.Int64)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.FileRange"/> class.
            </summary>
            <param name="start">The starting offset.</param>
            <param name="end">The ending offset.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRange.StartOffset">
            <summary>
            Gets the starting offset of the page range.
            </summary>
            <value>The starting offset.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRange.EndOffset">
            <summary>
            Gets the ending offset of the page range.
            </summary>
            <value>The ending offset.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileRange.ToString">
            <summary>
            Returns the content of the range as a string.
            </summary>
            <returns>The content of the range.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.#ctor(Microsoft.Azure.Storage.File.CloudFile,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.FileReadStreamBase"/> class.
            </summary>
            <param name="file">File reference to read from</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object for tracking the current operation.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileReadStreamBase.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileReadStreamBase.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileReadStreamBase.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileReadStreamBase.Position">
            <summary>
            Gets or sets the position within the current stream.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileReadStreamBase.Length">
            <summary>
            Gets the length in bytes of the stream.
            </summary>
            <value>The length in bytes of the stream.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position within the current stream.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">A value of type <c>SeekOrigin</c> indicating the reference
            point used to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
            <remarks>Seeking in a FileReadStream disables checksum validation.</remarks>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.SetLength(System.Int64)">
            <summary>
            This operation is not supported in FileReadStreamBase.
            </summary>
            <param name="value">Not used.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            This operation is not supported in FileReadStreamBase.
            </summary>
            <param name="buffer">Not used.</param>
            <param name="offset">Not used.</param>
            <param name="count">Not used.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.Flush">
            <summary>
            This operation is a no-op in FileReadStreamBase.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.ConsumeBuffer(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read as much as we can from the internal buffer
            </summary>
            <param name="buffer">The buffer to read the data into.</param>
            <param name="offset">The byte offset in buffer at which to begin writing
            data read from the stream.</param>
            <param name="count">The maximum number of bytes to read.</param>
            <returns>Number of bytes read from the stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.GetReadSize">
            <summary>
            Calculates the number of bytes to read from the file.
            </summary>
            <returns>Number of bytes to read.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.VerifyFileChecksum(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Updates the file checksum with newly downloaded content.
            </summary>
            <param name="buffer">The buffer to read the data from.</param>
            <param name="offset">The byte offset in buffer at which to begin reading data.</param>
            <param name="count">The maximum number of bytes to read.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileReadStreamBase.Dispose(System.Boolean)">
            <summary>
            Releases the file resources used by the Stream.
            </summary>
            <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileRequestOptions">
            <summary>
            Represents a set of timeout and retry policy options that may be specified for a request against the File service.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileRequestOptions.parallelOperationThreadCount">
            <summary>
            Stores the parallelism factor.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileRequestOptions.maximumExecutionTime">
            <summary>
            Stores the maximum execution time.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileRequestOptions.BaseDefaultRequestOptions">
            <summary>
            Defines the absolute default option values, should neither the user nor client specify anything.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileRequestOptions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileRequestOptions.#ctor(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Clones an instance of FileRequestOptions so that we can apply defaults.
            </summary>
            <param name="other">FileRequestOptions instance to be cloned.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.OperationExpiryTime">
            <summary>
            Gets or sets the absolute expiry time across all potential retries for the request. 
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.RetryPolicy">
            <summary>
            Gets or sets the retry policy.
            </summary>
            <value>The retry policy.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.LocationMode">
            <summary>
            Gets or sets the location mode of the request.
            </summary>
            <value>The location mode of the request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.ServerTimeout">
            <summary>
            Gets or sets the server timeout interval for the request.
            </summary>
            <value>The server timeout interval for the request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.MaximumExecutionTime">
            <summary>
            Gets or sets the maximum execution time across all potential retries for the request. 
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> representing the maximum execution time for retries for the request.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.NetworkTimeout">
            <summary>
            Gets or sets the timeout applied to an individual network operations.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.ParallelOperationThreadCount">
            <summary>
            Gets or sets the number of ranges that may be simultaneously uploaded when uploading a file.
            </summary>
            <value>The number of parallel operations that may proceed.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.UseTransactionalMD5">
            <summary>
            Gets or sets a value to calculate and send/validate content MD5 for transactions.
            </summary>
            <value>Use <c>true</c> to calculate and send/validate content MD5 for transactions; otherwise, <c>false</c>.</value>       
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.StoreFileContentMD5">
            <summary>
            Gets or sets a value to indicate that an MD5 hash will be calculated and stored when uploading a file.
            </summary>
            <value>Use true to calculate and store an MD5 hash when uploading a file; otherwise, false.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileRequestOptions.DisableContentMD5Validation">
            <summary>
            Gets or sets a value to indicate that MD5 validation will be disabled when downloading files.
            </summary>
            <value>Use true to disable MD5 validation; false to enable MD5 validation.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileResultSegment">
            <summary>
            Represents a segment of <see cref="T:Microsoft.Azure.Storage.File.IListFileItem"/> results, with continuation information for pagination scenarios.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileResultSegment.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Azure.Storage.File.IListFileItem},Microsoft.Azure.Storage.File.FileContinuationToken)">
            <summary>
            Creates in instance of FileResultSegment.
            </summary>
            <param name="files">An enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.IListFileItem"/> objects.</param>
            <param name="continuationToken">The <see cref="T:Microsoft.Azure.Storage.File.FileContinuationToken"/> used to retrieve the next segment of <see cref="T:Microsoft.Azure.Storage.File.IListFileItem"/> results.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileResultSegment.Results">
            <summary>
            Gets an enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.IListFileItem"/> results.
            </summary>
            <value>An enumerable collection of results.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileResultSegment.ContinuationToken">
            <summary>
            Gets the continuation token used to retrieve the next segment of <see cref="T:Microsoft.Azure.Storage.File.IListFileItem"/> results. Returns <c>null</c> if there are no more results.
            </summary>
            <value>The continuation token.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileSharePermissions">
            <summary>
            Represents the permissions for a share.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileSharePermissions.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.FileSharePermissions"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileSharePermissions.SharedAccessPolicies">
            <summary>
            Gets the set of shared access policies for the share.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicies"/> object.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileShareProperties">
            <summary>
            Represents the system properties for a share.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileShareProperties.ETag">
            <summary>
            Gets the ETag value for the share.
            </summary>
            <value>The share's quoted ETag value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileShareProperties.LastModified">
            <summary>
            Gets the share's last-modified time.
            </summary>
            <value>The share's last-modified time.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileShareProperties.Quota">
            <summary>
            Gets or sets the maximum size for the share, in gigabytes.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileSharePublicAccessType">
            <summary>
            Specifies the level of public access that is allowed on the share.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.FileSharePublicAccessType.Off">
            <summary>
            No public access. Only the account owner can read resources in this share.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStreamBase.#ctor(Microsoft.Azure.Storage.File.CloudFile,System.Int64,System.Boolean,Microsoft.Azure.Storage.AccessCondition,Microsoft.Azure.Storage.File.FileRequestOptions,Microsoft.Azure.Storage.OperationContext)">
            <summary>
            Initializes a new instance of the FileWriteStreamBase class for a file.
            </summary>
            <param name="file">File reference to write to.</param>
            <param name="fileSize">Size of the file.</param>
            <param name="createNew">Use <c>true</c> if the file is newly created, <c>false</c> otherwise.</param>
            <param name="accessCondition">An <see cref="T:Microsoft.Azure.Storage.AccessCondition"/> object that represents the access conditions for the file. If <c>null</c>, no condition is used.</param>
            <param name="options">An <see cref="T:Microsoft.Azure.Storage.File.FileRequestOptions"/> object that specifies additional options for the request.</param>
            <param name="operationContext">An <see cref="T:Microsoft.Azure.Storage.OperationContext"/> object for tracking the current operation.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileWriteStreamBase.CanRead">
            <summary>
            Gets a value indicating whether the current stream supports reading.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileWriteStreamBase.CanSeek">
            <summary>
            Gets a value indicating whether the current stream supports seeking.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileWriteStreamBase.CanWrite">
            <summary>
            Gets a value indicating whether the current stream supports writing.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileWriteStreamBase.Length">
            <summary>
            Gets the length in bytes of the stream.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileWriteStreamBase.Position">
            <summary>
            Gets or sets the position within the current stream.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStreamBase.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            This operation is not supported in FileWriteStreamBase.
            </summary>
            <param name="buffer">Not used.</param>
            <param name="offset">Not used.</param>
            <param name="count">Not used.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStreamBase.GetNewOffset(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Calculates the new position within the current stream for a Seek operation.
            </summary>
            <param name="offset">A byte offset relative to the origin parameter.</param>
            <param name="origin">A value of type <c>SeekOrigin</c> indicating the reference
            point used to obtain the new position.</param>
            <returns>The new position within the current stream.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStreamBase.SetLength(System.Int64)">
            <summary>
            This operation is not supported in FileWriteStreamBase.
            </summary>
            <param name="value">Not used.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.FileWriteStreamBase.Dispose(System.Boolean)">
            <summary>
            Releases the file resources used by the Stream.
            </summary>
            <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.CloudFileStream.CommitAsync">
            <summary>
            Asynchronously clears all buffers for this stream, causes any buffered data to be written to the underlying file, and commits the file.
            </summary>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents an asynchronous action.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.IListFileItem">
            <summary>
            Represents an item that may be returned by a file listing operation.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.IListFileItem.Uri">
            <summary>
            Gets the URI to the file item.
            </summary>
            <value>The file item's URI.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.IListFileItem.StorageUri">
            <summary>
            Gets the URI to the file item.
            </summary>
            <value>The file item's URI.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.IListFileItem.Parent">
            <summary>
            Gets the file item's parent directory.
            </summary>
            <value>The file item's parent directory.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.IListFileItem.Share">
            <summary>
            Gets the file item's share.
            </summary>
            <value>The file item's share.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.SharedAccessFileHeaders">
            <summary>
            Represents the optional headers that can be returned with files accessed using SAS.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFileHeaders.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFileHeaders"/> class.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFileHeaders.#ctor(Microsoft.Azure.Storage.File.SharedAccessFileHeaders)">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFileHeaders"/> class based on an existing instance.
            </summary>
            <param name="sharedAccessFileHeaders">The set of <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFileHeaders"/> to clone.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFileHeaders.CacheControl">
            <summary>
            Gets or sets the cache-control header returned with the file.
            </summary>
            <value>A string containing the cache-control value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFileHeaders.ContentDisposition">
            <summary>
            Gets or sets the content-disposition header returned with the file.
            </summary>
            <value>A string containing the content-disposition value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFileHeaders.ContentEncoding">
            <summary>
            Gets or sets the content-encoding header returned with the file.
            </summary>
            <value>A string containing the content-encoding value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFileHeaders.ContentLanguage">
            <summary>
            Gets or sets the content-language header returned with the file.
            </summary>
            <value>A string containing the content-language value.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFileHeaders.ContentType">
            <summary>
            Gets or sets the content-type header returned with the file.
            </summary>
            <value>A string containing the content-type value.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.SharedAccessFilePermissions">
            <summary>
            Specifies the set of possible permissions for a shared access policy.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.SharedAccessFilePermissions.None">
            <summary>
            No shared access granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.SharedAccessFilePermissions.Read">
            <summary>
            Read access granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.SharedAccessFilePermissions.Write">
            <summary>
            Write access granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.SharedAccessFilePermissions.Delete">
            <summary>
            Delete access granted for files.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.SharedAccessFilePermissions.List">
            <summary>
            List access granted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.SharedAccessFilePermissions.Create">
            <summary>
            Create access granted.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicies">
            <summary>
            Represents the collection of shared access policies defined for a share.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Add(System.String,Microsoft.Azure.Storage.File.SharedAccessFilePolicy)">
            <summary>
            Adds the specified key and <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value to the collection of shared access policies.
            </summary>
            <param name="key">The key of the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value to add.</param>
            <param name="value">The <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value to add the collection of shared access policies.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.ContainsKey(System.String)">
            <summary>
            Determines whether the collection of shared access policies contains the specified key.
            </summary>
            <param name="key">The key to locate in the collection of shared access policies.</param>
            <returns><c>true</c> if the collection of shared access policies contains an element with the specified key; otherwise, <c>false</c>.</returns>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Keys">
            <summary>
            Gets a collection containing the keys in the shared access policies collection.
            </summary>
            <value>A collection of strings containing the keys of the shared access policies collection.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Remove(System.String)">
            <summary>
            Removes the value with the specified key from the shared access policies collection.
            </summary>
            <param name="key">A string containing the key of the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> item to remove.</param>
            <returns><c>true</c> if the element is successfully found and removed; otherwise, <c>false</c>. This method returns <c>false</c> if the key is not found.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.TryGetValue(System.String,Microsoft.Azure.Storage.File.SharedAccessFilePolicy@)">
            <summary>
            Gets the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> item associated with the specified key. 
            </summary>
            <param name="key">A string containing the key of the value to get.</param>
            <param name="value">The <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> item to get.</param>
            <returns>The <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> item associated with the specified key, if the key is found; otherwise, the default value for the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> type.</returns>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Values">
            <summary>
            Gets a collection containing the values in the shared access policies collection.
            </summary>
            <value>A collection of <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> items in the shared access policies collection.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Item(System.String)">
            <summary>
            Gets or sets the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> item associated with the specified key.
            </summary>
            <param name="key">A string containing the key of the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value to get or set.</param>
            <returns>The <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> item associated with the specified key, or <c>null</c> if key is not in the shared access policies collection.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Add(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Azure.Storage.File.SharedAccessFilePolicy})">
            <summary>
            Adds the specified key/<see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value, stored in a <see cref="T:System.Collections.Generic.KeyValuePair`2"/>, to the collection of shared access policies.
            </summary>
            <param name="item">The <see cref="T:System.Collections.Generic.KeyValuePair`2"/> object, containing a key/<see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value pair, to add to the shared access policies collection.</param>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Clear">
            <summary>
            Removes all keys and <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> values from the shared access collection.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Contains(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Azure.Storage.File.SharedAccessFilePolicy})">
            <summary>
            Determines whether the collection of shared access policies contains the key and <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value in the specified <see cref="T:System.Collections.Generic.KeyValuePair`2"/> object.
            </summary>
            <param name="item">A <see cref="T:System.Collections.Generic.KeyValuePair`2"/> object containing the key and <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value to search for.</param>
            <returns><c>true</c> if the shared access policies collection contains the specified key/value; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.CopyTo(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Azure.Storage.File.SharedAccessFilePolicy}[],System.Int32)">
            <summary>
            Copies each key in the key/<see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value pair to a compatible one-dimensional array, starting at the specified index
            of the target array.
            </summary>
            <param name="array">A one-dimensional array of <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> objects that serves as the destination for the elements copied from the shared access policies collection.</param>
            <param name="arrayIndex">The zero-based index in <paramref name="array"/> at which copying begins.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Count">
            <summary>
            Gets the number of key/<see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value pairs contained in the shared access policies collection.
            </summary>
            <value>The number of key/<see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value pairs contained in the shared access policies collection.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.IsReadOnly">
            <summary>
            Gets a value indicating whether the collection of shared access policies is read-only. 
            </summary>
            <value><c>true</c> if the collection of shared access policies is read-only; otherwise, <c>false</c>.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.Remove(System.Collections.Generic.KeyValuePair{System.String,Microsoft.Azure.Storage.File.SharedAccessFilePolicy})">
            <summary>
            Removes the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value, specified in the <see cref="T:System.Collections.Generic.KeyValuePair`2"/> object, from the shared access policies collection.
            </summary>
            <param name="item">The <see cref="T:System.Collections.Generic.KeyValuePair`2"/> object, containing a key and <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> value, to remove from the shared access policies collection.</param>
            <returns><c>true</c> if the item was successfully removed; otherwise, <c>false</c>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection of shared access policies.
            </summary>
            <returns>An <see cref="T:System.Collections.Generic.IEnumerator`1"/> of type <see cref="T:System.Collections.Generic.KeyValuePair`2"/>.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicies.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection of shared access policies.
            </summary>
            <returns>An <see cref="T:System.Collections.IEnumerator"/> object that can be used to iterate through the collection of shared access policies.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy">
            <summary>
            Represents a shared access policy, which specifies the start time, expiry time, 
            and permissions for a shared access signature.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicy.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePolicy"/> class.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFilePolicy.SharedAccessStartTime">
            <summary>
            Gets or sets the start time for a shared access signature associated with this shared access policy.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> specifying the shared access start time.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFilePolicy.SharedAccessExpiryTime">
            <summary>
            Gets or sets the expiry time for a shared access signature associated with this shared access policy.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> specifying the shared access expiry time.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.SharedAccessFilePolicy.Permissions">
            <summary>
            Gets or sets the permissions for a shared access signature associated with this shared access policy.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePermissions"/> object.</value>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicy.PermissionsToString(Microsoft.Azure.Storage.File.SharedAccessFilePermissions)">
            <summary>
            Converts the permissions specified for the shared access policy to a string.
            </summary>
            <param name="permissions">A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePermissions"/> object.</param>
            <returns>The shared access permissions, in string format.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.SharedAccessFilePolicy.PermissionsFromString(System.String)">
            <summary>
            Constructs a <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePermissions"/> object from a permissions string.
            </summary>
            <param name="input">The shared access permissions, in string format.</param>
            <returns>A <see cref="T:Microsoft.Azure.Storage.File.SharedAccessFilePermissions"/> object.</returns>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.CopyState">
            <summary>
            Represents the attributes of a copy operation.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CopyState.CopyId">
            <summary>
            Gets the ID of the copy operation.
            </summary>
            <value>A copy ID string.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CopyState.CompletionTime">
            <summary>
            Gets the time the copy operation completed, and indicates whether completion was due to a successful copy, the cancelling of the operation, or a failure.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> containing the completion time, or <c>null</c> if the operation has not completed.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CopyState.Status">
            <summary>
            Gets the status of the copy operation.
            </summary>
            <value>A <see cref="T:Microsoft.Azure.Storage.File.CopyStatus"/> enumeration indicating the status of the operation.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CopyState.Source">
            <summary>
            Gets the source URI of a copy operation.
            </summary>
            <value>A <see cref="T:System.Uri"/> indicating the source of a copy operation, or <c>null</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CopyState.BytesCopied">
            <summary>
            Gets the number of bytes copied in the operation so far.
            </summary>
            <value>The number of bytes copied in the operation so far, or <c>null</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CopyState.TotalBytes">
            <summary>
            Gets the total number of bytes in the source of the copy.
            </summary>
            <value>The number of bytes in the source, or <c>null</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CopyState.StatusDescription">
            <summary>
            Gets the description of the current status, if any.
            </summary>
            <value>A status description string, or <c>null</c>.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.CopyState.DestinationSnapshotTime">
            <summary>
            Gets the incremental destination snapshot time for the latest incremental copy, if present.
            </summary>
            <value>A <see cref="T:System.DateTimeOffset"/> containing the destination snapshot time for the latest
            incremental copy, or <c>null</c>.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.CopyStatus">
            <summary>
            Represents the status of a copy File operation.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CopyStatus.Invalid">
            <summary>
            The copy status is invalid.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CopyStatus.Pending">
            <summary>
            The copy operation is pending.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CopyStatus.Success">
            <summary>
            The copy operation succeeded.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CopyStatus.Aborted">
            <summary>
            The copy operation has been aborted.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.CopyStatus.Failed">
            <summary>
            The copy operation encountered an error.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.ShareListingDetails">
            <summary>
            Specifies which details to include when listing the shares in this storage account.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.ShareListingDetails.None">
            <summary>
            No additional details.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.ShareListingDetails.Metadata">
            <summary>
            Retrieve share metadata.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.ShareListingDetails.Snapshots">
            <summary>
            Retrieve share snapshots.
            </summary>
        </member>
        <member name="F:Microsoft.Azure.Storage.File.ShareListingDetails.All">
            <summary>
            Retrieve all available details.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.ShareResultSegment">
            <summary>
            Represents a segment of <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> results and contains continuation and pagination information.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.File.ShareResultSegment.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Azure.Storage.File.CloudFileShare},Microsoft.Azure.Storage.File.FileContinuationToken)">
            <summary>
            Creates an instance of ShareResultSegment.
            </summary>
            <param name="shares">An enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> objects.</param>
            <param name="continuationToken">The <see cref="T:Microsoft.Azure.Storage.File.FileContinuationToken"/> used to retrieve the next segment of <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> results.</param>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.ShareResultSegment.Results">
            <summary>
            Gets an enumerable collection of <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> results.
            </summary>
            <value>An enumerable collection of results.</value>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.ShareResultSegment.ContinuationToken">
            <summary>
            Gets the <see cref="T:Microsoft.Azure.Storage.File.FileContinuationToken"/> object used to retrieve the next segment of <see cref="T:Microsoft.Azure.Storage.File.CloudFileShare"/> results.
            </summary>
            <value>The continuation token.</value>
        </member>
        <member name="T:Microsoft.Azure.Storage.File.FileCopyOptions">
            <summary>
            Represent options in file copying.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileCopyOptions.PreservePermissions">
            <summary>
            Get or set a boolen value that indicates whether to copy the File Permission from the source file to the destination file.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileCopyOptions.PreserveNtfsAttributes">
            <summary>
            Get or set a boolen value that indicates whether to copy the file system attributes from the source file to the destination file.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileCopyOptions.PreserveCreationTime">
            <summary>
            Get or set a boolen value that indicates whether to copy the creation time from the source file to the destination file.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileCopyOptions.PreserveLastWriteTime">
            <summary>
            Get or set a boolen value that indicates whether to copy the last write time from the source file to the destination file.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileCopyOptions.SetArchive">
            <summary>
            Get or set a boolean value that indicates whether the Archive attribute should be set.
            </summary>
        </member>
        <member name="P:Microsoft.Azure.Storage.File.FileCopyOptions.IgnoreReadOnly">
            <summary>
            Get or set a boolean value that indicates whether the ReadOnly attribute on a preexisting destination file should be respected.
            If true, the copy will succeed, otherwise, a previous file at the destination with the ReadOnly attribute set will cause the copy to fail.
            </summary>
        </member>
        <member name="T:Microsoft.Azure.Storage.Core.Auth.FileSharedAccessSignatureHelper">
            <summary>
            Contains helper methods for implementing shared access signatures.
            </summary>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.FileSharedAccessSignatureHelper.GetSignature(Microsoft.Azure.Storage.File.SharedAccessFilePolicy,Microsoft.Azure.Storage.File.SharedAccessFileHeaders,System.String,System.String,System.String,System.String,System.String,System.Nullable{Microsoft.Azure.Storage.SharedAccessProtocol},Microsoft.Azure.Storage.IPAddressOrRange)">
            <summary>
            Get the complete query builder for creating the Shared Access Signature query.
            </summary>
            <param name="policy">The shared access policy to hash.</param>
            <param name="headers">The optional header values to set for a file returned with this SAS.</param>
            <param name="accessPolicyIdentifier">An optional identifier for the policy.</param>
            <param name="resourceType">Either "f" for files or "s" for shares.</param>
            <param name="signature">The signature to use.</param>
            <param name="accountKeyName">The name of the key used to create the signature, or <c>null</c> if the key is implicit.</param>
            <param name="sasVersion">A string indicating the desired SAS version to use, in storage service version format.</param>
            <param name="protocols">The HTTP/HTTPS protocols for Account SAS.</param>
            <param name="ipAddressOrRange">The IP range for IPSAS.</param>
            <returns>The finished query builder.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Auth.FileSharedAccessSignatureHelper.GetHash(Microsoft.Azure.Storage.File.SharedAccessFilePolicy,Microsoft.Azure.Storage.File.SharedAccessFileHeaders,System.String,System.String,System.String,System.Nullable{Microsoft.Azure.Storage.SharedAccessProtocol},Microsoft.Azure.Storage.IPAddressOrRange,System.Byte[])">
            <summary>
            Get the signature hash embedded inside the Shared Access Signature.
            </summary>
            <param name="policy">The shared access policy to hash.</param>
            <param name="headers">The optional header values to set for a file returned with this SAS.</param>
            <param name="accessPolicyIdentifier">An optional identifier for the policy.</param>
            <param name="resourceName">The canonical resource string, unescaped.</param>
            <param name="sasVersion">A string indicating the desired SAS version to use, in storage service version format.</param>
            <param name="protocols">The HTTP/HTTPS protocols for Account SAS.</param>
            <param name="ipAddressOrRange">The IP range for IPSAS.</param>
            <param name="keyValue">The key value retrieved as an atomic operation used for signing.</param>
            <returns>The signed hash.</returns>
        </member>
        <member name="M:Microsoft.Azure.Storage.Core.Util.FileCommonUtility.CreateTemporaryExecutionState(Microsoft.Azure.Storage.File.FileRequestOptions)">
            <summary>
            Create an ExecutionState object that can be used for pre-request operations
            such as buffering user's data.
            </summary>
            <param name="options">Request options</param>
            <returns>Temporary ExecutionState object</returns>
        </member>
    </members>
</doc>
