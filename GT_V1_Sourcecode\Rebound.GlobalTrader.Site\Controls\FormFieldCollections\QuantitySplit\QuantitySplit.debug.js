///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.initializeBase(this, [element]);
	this._intTotalWidth = 300;
	this._intSplits = 1;
	this._intTotalQuantity = 400;
	this._aryQuantities = [];
	this._intTotalWidth = 300;
	this._aryWidths = [];
	this._intMaxSplits = 5;
	this._intSepWidth = 5;
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.prototype = {

	get_hypItemSplit: function() { return this._hypItemSplit; }, 	set_hypItemSplit: function(v) { if (this._hypItemSplit !== v)  this._hypItemSplit = v; }, 
	get_hypItemUnsplit: function() { return this._hypItemUnsplit; }, 	set_hypItemUnsplit: function(v) { if (this._hypItemUnsplit !== v)  this._hypItemUnsplit = v; }, 
	get_pnlItems: function() { return this._pnlItems; }, 	set_pnlItems: function(v) { if (this._pnlItems !== v)  this._pnlItems = v; }, 
	get_intTotalQuantity: function() { return this._intTotalQuantity; }, 	set_intTotalQuantity: function(v) { if (this._intTotalQuantity !== v)  this._intTotalQuantity = v; }, 
	get_intTotalWidth: function() { return this._intTotalWidth; }, 	set_intTotalWidth: function(v) { if (this._intTotalWidth !== v)  this._intTotalWidth = v; }, 
	get_intMaxSplits: function() { return this._intMaxSplits; }, 	set_intMaxSplits: function(v) { if (this._intMaxSplits !== v)  this._intMaxSplits = v; }, 
	
	initialize: function() {
		$addHandler(this._hypItemSplit, "click", Function.createDelegate(this, this.addSplit));
		$addHandler(this._hypItemUnsplit, "click", Function.createDelegate(this, this.removeSplit));
		this._aryQuantities = [this._intTotalQuantity];
		this._aryWidths = [this._intTotalWidth];
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._hypItemSplit) $clearHandlers(this._hypItemSplit);
		if (this._hypItemUnsplit) $clearHandlers(this._hypItemUnsplit);
		this._hypItemSplit = null;
		this._hypItemUnsplit = null;
		this._pnlItems = null;
		this._aryQuantities = null;
		this._aryWidths = null;
		this._intTotalQuantity = null;
		this._intMaxSplits = null;
		this._intTotalWidth = null;
		Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.callBaseMethod(this, "dispose");
	},
	
	addSplit: function() {
		if (this._intSplits == this._intMaxSplits) return;
		this._aryQuantities[this._intSplits - 1] = Math.max(1, Math.floor(this._aryQuantities[this._intSplits - 1] / 2));
		this._aryQuantities[this._intSplits] = this._aryQuantities[this._intSplits - 1];
		this._intSplits += 1;
		this.adjustFirstQuantity();
		this.calculateWidths();
		this.displaySplits();
	},
	
	removeSplit: function() {
		if (this._intSplits == 1) return;
		this._intSplits -= 1;
		this._aryQuantities[0] += this._aryQuantities[this._intSplits];
		this._aryQuantities.splice(this._intSplits, this._intMaxSplits);
		this.adjustFirstQuantity();
		this.calculateWidths();
		this.displaySplits();
	},
	
	ensureCollectiveExhaustion: function(ary, intTotal) {
		var j = 0;
		while (ary.sum() < intTotal) {
			ary[j] += 1;
			j += 1; if (j >= this._intSplits) j = 0;
		}
	},
	
	calculateWidths: function() {
		var intW = this._intTotalWidth - (this._intSepWidth * (this._intSplits - 1));
		for (var i = 0; i < this._intSplits; i++) {
			this._aryWidths[i] = Math.floor(intW / this._intSplits);
		}
	},
	
	reset: function() {
		this._intSplits = 1;
		this._aryQuantities = [this._intTotalQuantity];
		this._aryWidths = [this._intTotalWidth];
		this.displaySplits();
	},
	
	displaySplits: function() {
		this.ensureCollectiveExhaustion(this._aryQuantities, this._intTotalQuantity);
		var str = "";
		var strID = this._element.id;
		var intLeft = 0;
		for (var i = 0; i < this._intSplits; i++) {
			str += String.format("<div class=\"{2}\" style=\"left:{0}px; width:{1}px;\"", intLeft, this._aryWidths[i], (i == 0) ? "splitStockItemSelected" : "splitStockItem");
			if (i > 0) str += String.format(" onclick=\"$find('{0}').editItem({1});\"", strID, i);
			str += String.format("><span id=\"{0}\">{1}</span>", this.getControlID("item", i), this._aryQuantities[i]);
			if (i > 0) str += String.format("<input class=\"invisible\" type=\"text\" id=\"{0}\" value=\"{1}\" onkeypress=\"if (event.keyCode == Sys.UI.Key.enter) {{ $find('{2}').finishEditItem({3}); return false; }} else if (event.keyCode == 27) {{ $find('{2}').showTextBox(false, {3}); }};\" />", this.getControlID("txt", i), this._aryQuantities[i], strID, i);
			str += "</div>";
			intLeft += this._aryWidths[i] + this._intSepWidth;
		}
		$R_FN.setInnerHTML(this._pnlItems, str);
	},
	
	getControlID: function(str, i) {
		return String.format("{0}_{1}{2}", this._element.id, str, i);
	},
	
	showButtons: function() {
		$R_FN.showElement($get("hypItemSplit"), this._intSplits < this._intMaxSplits);
		$R_FN.showElement($get("hypItemUnsplit"), this._intSplits > 1);
	},
	
	updateQuantities: function() {
		for (var i = 0; i < this._intSplits; i++) {
			$R_FN.setInnerHTML($get(this.getControlID("item", i)), this._aryQuantities[i]);
			$get(this.getControlID("txt", i)).value = this._aryQuantities[i];
		}
	},
	
	editItem: function(i) {
		this.showTextBox(true, i);
	},
	
	finishEditItem: function(i) {
		this.showTextBox(false, i);
		var txt = $get(this.getControlID("txt", i));
		if (!Number.parseLocale(eval(txt.value).toString())) return;
		this._aryQuantities[i] = Math.max(Math.min(Number.parseLocale(eval(txt.value).toString()), this._intTotalQuantity - (this._aryQuantities.sum() - this._aryQuantities[0] - this._aryQuantities[i]) - 1), 1);
		this.adjustFirstQuantity();
		this.displaySplits();
	},
	
	showTextBox: function(bln, i) {
		$R_FN.showElement($get(this.getControlID("item", i)), !bln);
		var txt = $get(this.getControlID("txt", i));
		$R_FN.showElement(txt, bln);
		if (bln) {
			txt.value = this._aryQuantities[i];
			txt.select();
			txt.focus();
		}
	},
	
	adjustFirstQuantity: function() {
		this._aryQuantities[0] = Math.max(this._intTotalQuantity - (this._aryQuantities.sum() - this._aryQuantities[0]), 1);
	},
	
	getQuantitiesString: function() {
		return $R_FN.arrayToSingleString(this._aryQuantities);
	}
		
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.QuantitySplit", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);
