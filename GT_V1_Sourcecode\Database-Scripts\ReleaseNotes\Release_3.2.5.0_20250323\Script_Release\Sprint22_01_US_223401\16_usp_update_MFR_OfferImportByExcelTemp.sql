﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*
=========================================================================================================================================================
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-223401]     Phuc Hoang		 20-Feb-2025		CREATE		IPO - Bulk Offer Import tool - Apply fuzzy matching and rationalize data before importing
=========================================================================================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_update_MFR_OfferImportByExcelTemp] (
	@ImportFileId INT = 0,
    @IncorrectMFR NVARCHAR(MAX),
	@NewMFR NVARCHAR(MAX),
	@ClientNo INT = 0,
	@UpdatedBy INT,
	@RecordCount INT OUTPUT
)
AS
BEGIN
    IF (@IncorrectMFR = '_Blank_')
	BEGIN
		SET @IncorrectMFR = ''
	END
    UPDATE BorisGlobalTraderimports.dbo.tbOfferImportByExcelTemp
    SET
		UpdatedBy = @UpdatedBy,
		MFR = @NewMFR
    WHERE HUBOfferImportLargeFileID = @ImportFileId AND ISNULL(MFR, '') = @IncorrectMFR AND @ClientNo = ClientNo;

	SELECT @RecordCount = @@ROWCOUNT 
END
GO