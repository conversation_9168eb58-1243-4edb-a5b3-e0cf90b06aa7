﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-214760]		An.TranTan		14-Jan-2025		CREATE		Import BOM Sourcing Results
===========================================================================================
*/
CREATE OR ALTER   PROCEDURE [dbo].[usp_Import_BOMSourcingResults]
	@UserID INT
	,@ImportCount INT OUTPUT                                                
	,@ImportMessage NVARCHAR(2000) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;
	
	DECLARE @CustomerRefNo NVARCHAR(200)
			,@CustomerRequirementNo INT
			,@ReqClientNo INT
			,@ReqProductNo INT
			,@ReqClientProductNo INT
			,@ReqGlobalProductNo INT
			,@ReqManufacturerNo INT
			,@ClientCurrencyNo INT
			,@ClientCompanyNo INT
			,@OriginalFilename NVARCHAR(200)
			,@GeneratedFilename NVARCHAR(200)
			,@BOMNo INT
			,@CustomerRequirementNumber INT
	DECLARE @InsertedOffers TABLE (OfferId INT);
			
	BEGIN TRY
	BEGIN TRANSACTION
    IF EXISTS
    (
        SELECT TOP 1 1
        FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported
        WHERE CreatedBy = @UserID
    )
    BEGIN
		SELECT TOP 1 
			@CustomerRefNo = CustomerRefNo 
			,@CustomerRequirementNo = CustomerRequirementNo
			,@OriginalFilename = OriginalFilename
			,@GeneratedFilename = GeneratedFilename
		FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported WHERE CreatedBy = @UserID;

		SELECT TOP 1
			@ReqClientNo = cr.ClientNo
			, @ReqProductNo = cr.ProductNo
			, @ReqGlobalProductNo = p.GlobalProductNo
			, @ReqClientProductNo = p1.ProductId
			, @ReqManufacturerNo = cr.ManufacturerNo
			, @BOMNo = ISNULL(cr.BOMNo,0)
			, @CustomerRequirementNumber = cr.CustomerRequirementNumber
		FROM tbCustomerRequirement cr WITH(NOLOCK)
		LEFT JOIN tbProduct p WITH(NOLOCK) ON p.ProductId = cr.ProductNo
		LEFT JOIN tbProduct p1 WITH(NOLOCK) ON p1.GlobalProductNo = p.GlobalProductNo
			AND p1.ClientNo = cr.ClientNo
			AND p1.Inactive = 0
		WHERE cr.CustomerRequirementId = @CustomerRequirementNo;

		SELECT TOP 1 @ClientCompanyNo = CompanyId
        FROM tbCompany WITH(NOLOCK)
        WHERE ClientNo = @ReqClientNo AND IsPOHub = 1;

		--insert into offer
		INSERT INTO [BorisGlobalTraderImports].dbo.tboffer
		(
		    [FullPart]
			,[Part]
			,[SupplierNo]
			,[SupplierName]
			,[CurrencyNo]
			,[ManufacturerNo]
			,[ManufacturerName]
			,[ProductNo]
			,[PackageNo]
			,[DateCode]
			,[Quantity]
			,[Price]
			,[OriginalEntryDate]
			,[UpdatedBy]
			,[DLUP]
			,[OfferStatusNo]
			,[OfferStatusChangeDate]
			,[OfferStatusChangeLoginNo]
			,[Notes]
			,[SPQ]
			,[LeadTime]
			,[ROHSStatus]
			,[FactorySealed]
			,[MSL]
			,[SupplierManufacturerName]
			,[SupplierDateCode]
			,[SupplierPackageType]
			,[SupplierMOQ]
			,[SupplierTotalQSA]
			,[SupplierLTB]
			,[SupplierNotes]
			,[IsPoHub]
			,[MSLLevelNo]
			,[SellPrice]
			,[ShippingCost]
			,[RegionNo]
			,[DeliveryDate]
		)
		OUTPUT Inserted.OfferId INTO @InsertedOffers(OfferId)
		SELECT 
			dbo.Ufn_get_fullpart(s.SupplierPart)
			,s.SupplierPart
			,s.SupplierNo
			,s.SupplierName
			,s.CurrencyNo
			,s.ManufacturerNo
			,s.ManufacturerName
			,@ReqProductNo
			,s.PackageNo
			,s.DateCode
			,s.Quantity
			,s.BuyPrice
			,GETDATE()
			,@UserId
			,GETDATE()
			,s.OfferStatusNo
			,GETDATE()
			,@UserId
			,s.Notes
			,s.SPQ
			,s.LeadTime
			,s.ROHSStatus
			,s.FactorySealed
			,msl.MSLLevel
			,s.ManufacturerName
			,s.DateCode
			,p.PackageName
			,s.MOQ
			,s.QtyInStock
			,s.LastTimeBuy
			,s.Notes
			,1 --IsPOHub
			,s.MSLLevelNo
			,s.SellPrice
			,s.ShippingCost
			,s.RegionNo
			,s.DeliveryDate
		FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported s
		LEFT JOIN tbPackage p WITH(NOLOCK) ON p.PackageId = s.PackageNo
		LEFT JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevelId = s.MSLLevelNo
		WHERE s.CreatedBy = @UserID;

		--INSERT to tbSourcingResult, re-use logic: usp_insert_SourcingResult_From_QuotesToClient
		--get offer currencies-------------
		SELECT 
			o.OfferId
			,ISNULL(o.CurrencyNo,0) AS OfferCurrencyNo
			,dbo.ufn_get_exchange_rate(ISNULL(o.CurrencyNo, 0), GETDATE()) AS BuyExchangeRate
			,l.LinkMultiCurrencyId AS LinkMultiCurrencyNo
			,l.SupplierCurrencyNo AS ClientCurrencyNo
		INTO #tempOfferCurrency
		FROM [BorisGlobalTraderImports].dbo.tboffer o WITH(NOLOCK)
		JOIN @InsertedOffers i on i.OfferId = o.OfferId
		LEFT JOIN tbCurrency c WITH(NOLOCK) ON c.CurrencyId = o.CurrencyNo
		LEFT JOIN tbLinkMultiCurrency l WITH(NOLOCK) on l.GlobalCurrencyNo = c.GlobalCurrencyNo
		WHERE l.ClientNo = @ReqClientNo and c.ClientNo = 114;

		-----------------------------------------
		INSERT INTO dbo.tbSourcingResult
        (
            CustomerRequirementNo,
            SourcingTable,
            SourcingTableItemNo,
            FullPart,
            Part,
            ManufacturerNo,
            DateCode,
            ProductNo,
            PackageNo,
            Quantity,
            Price,
            CurrencyNo,
            OriginalEntryDate,
            Salesman,
            OfferStatusNo,
            OfferStatusChangeDate,
            OfferStatusChangeLoginNo,
            SupplierNo,
            UpdatedBy,
            DLUP,
            TypeName,
            Notes,
            ROHS,
            POHubCompanyNo,
            SupplierPrice,
            ClientCompanyNo,
            EstimatedShippingCost,
            ClientCurrencyNo,
            SupplierManufacturerName,
            SupplierDateCode,
            SupplierPackageType,
            SupplierProductType,
            SupplierMOQ,
            SupplierTotalQSA,
            SupplierLTB,
            SupplierNotes,
            SPQ,
            LeadTime,
            ROHSStatus,
            FactorySealed,
            MSLLevelNo,
            MSL,
            Buyer,
            ActualPrice,
            ActualCurrencyNo,
            ExchangeRate,
            LinkMultiCurrencyNo,
            DeliveryDate,
            RegionNo
        )
		SELECT 
			@CustomerRequirementNo
			,'OFPH'
			,o.OfferId
			,o.FullPart
			,o.Part
			,ISNULL(o.ManufacturerNo,0)
			,o.DateCode
			,ISNULL(@ReqClientProductNo, @ReqProductNo)
			,ISNULL(o.PackageNo,0)
			,o.Quantity
			,o.Price
			,dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(o.CurrencyNo, company.POCurrencyNo), @ReqClientNo, 114)
			,o.OriginalEntryDate
			,isnull(o.Salesman, 0)
			,o.OfferStatusNo
            ,o.OfferStatusChangeDate
            ,o.OfferStatusChangeLoginNo
			,o.SupplierNo
			,@UserID
			,GETDATE()
			,''	--type name
			,o.Notes
			,o.ROHS
			,o.SupplierNo
			,((isnull(o.Price, 0) / oc.BuyExchangeRate))	--SupplierPrice
			,@ClientCompanyNo
			,ISNULL(o.ShippingCost,0)
			,oc.ClientCurrencyNo
			,o.ManufacturerName
			,o.DateCode
			,o.SupplierPackageType
			,pd.ProductDescription
			,o.SupplierMOQ
			,ISNULL(TRY_CAST(o.SupplierTotalQSA AS INT),0)
			,o.SupplierLTB
			,o.Notes
			,o.SPQ
			,o.LeadTime
			,o.ROHSStatus
			,o.FactorySealed
			,o.MSLLevelNo
			,o.MSL
			,@UserId
			,o.Price
			,ISNULL(o.CurrencyNo, isnull(company.POCurrencyNo, 0))
			,dbo.ufn_get_exchange_rate(ISNULL(o.CurrencyNo, isnull(company.POCurrencyNo, 0)), GETDATE())
			,oc.LinkMultiCurrencyNo
			,o.DeliveryDate
			,o.RegionNo
		FROM [BorisGlobalTraderImports].dbo.tbOffer o
		JOIN @InsertedOffers i on i.OfferId = o.OfferId
		LEFT JOIN #tempOfferCurrency oc ON oc.OfferId = o.OfferId
		LEFT JOIN dbo.tbCompany company on o.SupplierNo = company.CompanyId
		LEFT JOIN dbo.tbProduct pd on pd.ProductId = @ReqProductNo
		
		--update flags for requirement
		UPDATE tbCustomerRequirement
        SET REQStatus = 3
			,CustomerRefNo = @CustomerRefNo
			,HasHubSourcingResult = 1
        WHERE CustomerRequirementId = @CustomerRequirementNo;
		--------------------------------------------------------------------
		SELECT @ImportCount = COUNT(1) FROM @InsertedOffers;
		DECLARE @CsvLogMessage NVARCHAR(1000);
		/********* Save imported file to HUBRFQ Uploaded Documents *********/
		INSERT INTO [tbBOMCSV] 
		(
			[BOMNo]
			,[Caption]
			,[FileName]
			,[UpdatedBy]
			,[DLUP]
			,[ImportType]
		)VALUES(@BOMNo, @OriginalFilename, @GeneratedFilename, @UserId, GETDATE(), 'SRCIMPORT')
		
		SET @CsvLogMessage = CONCAT('Import sourcing result for Requirement: '
									, CAST(@CustomerRequirementNumber AS NVARCHAR(20)) 
									, ' | '
									, 'Import row(s) count: '
									, CAST(@ImportCount AS NVARCHAR(10))
							)
		INSERT INTO dbo.[tbBomCsvLog]
		(
			[BOMNo]
			,[FileName]
			,[Status]
			,[Message]
			,[DLUP]
		)VALUES(@BOMNo, @OriginalFilename, 1, @CsvLogMessage, GETDATE())
		--------------------------------------------------------------------
		/*============== Clear all temp data ================*/
		DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData WHERE CreatedBy = @UserID;
		DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported WHERE CreatedBy = @UserID;

		DROP TABLE #tempOfferCurrency;
		SET @ImportMessage = 'Import success';
	END
	COMMIT TRANSACTION;
	END TRY
	BEGIN CATCH
		ROLLBACK TRANSACTION
		SET @ImportCount = -1;
		SET @ImportMessage = Error_message();
	END CATCH
END
GO


