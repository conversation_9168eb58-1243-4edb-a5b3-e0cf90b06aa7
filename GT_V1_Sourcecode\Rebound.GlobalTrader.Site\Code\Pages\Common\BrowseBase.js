Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");Rebound.GlobalTrader.Site.Pages.BrowseBase=function(n){Rebound.GlobalTrader.Site.Pages.BrowseBase.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.BrowseBase.prototype={get_ctlDataListNugget:function(){return this._ctlDataListNugget},set_ctlDataListNugget:function(n){this._ctlDataListNugget!==n&&(this._ctlDataListNugget=n)},get_ctlPageTitle:function(){return this._ctlPageTitle},set_ctlPageTitle:function(n){this._ctlPageTitle!==n&&(this._ctlPageTitle=n)},get_intCurrentTab:function(){return this._intCurrentTab},set_intCurrentTab:function(n){this._intCurrentTab!==n&&(this._intCurrentTab=n)},get_aryTabs:function(){return this._aryTabs},set_aryTabs:function(n){this._aryTabs!==n&&(this._aryTabs=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.BrowseBase.callBaseMethod(this,"initialize")},goInit:function(){this._ctlDataListNugget&&this._ctlDataListNugget.addAskPageToChangeTab(Function.createDelegate(this,this.ctlDataListNugget_AskPageToChangeTab));this.invisibleTabs();Rebound.GlobalTrader.Site.Pages.BrowseBase.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPageTitle&&this._ctlPageTitle.dispose(),this._ctlDataListNugget&&this._ctlDataListNugget.dispose(),this._ctlPageTitle=null,this._ctlDataListNugget=null,this._intCurrentTab=null,Rebound.GlobalTrader.Site.Pages.BrowseBase.callBaseMethod(this,"dispose"))},changeTab:function(n){this._ctlDataListNugget._blnGettingData||(this._ctlPageTitle.selectTab(n),this.invisibleTabs(),this._ctlDataListNugget._intCurrentTab=this._ctlPageTitle._intCurrentTab,this._ctlDataListNugget.onPageTabChanged())},ctlDataListNugget_AskPageToChangeTab:function(){this._ctlPageTitle.selectTab(this._ctlDataListNugget._intCurrentTab)},invisibleTabs:function(){if(this._aryTabs!="undefined"&&this._aryTabs!=null)for(var n=0,t=this._aryTabs.length;n<t;n++)this._ctlPageTitle.showTab(this._aryTabs[n],!1)}};Rebound.GlobalTrader.Site.Pages.BrowseBase.registerClass("Rebound.GlobalTrader.Site.Pages.BrowseBase",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);