/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
using System.IO;
//using System.Linq;
using System.Net;
using System.Text;
using HttpUtils;
using IHSPart;
using System.Text.RegularExpressions;
using System.Data.Common;
using System.Reflection;
using Rebound.GlobalTrader.Site.Code.Common;


using System.Configuration;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class IhsSearch : Rebound.GlobalTrader.Site.Data.ItemSearch.Base
    {

        protected override void GetData()
        {

            List<BLL.Part> lst = null;
            try
            {
                lst = BLL.Part.CustReqPartsGRIDIHSAPI(
                      SessionManager.ClientID, GetFormValue_StringForPartSearch("partsearch")
                     , GetFormValue_StringForPartSearch("searchType")
                     , GetFormValue_NullableInt("SortIndex")
                     , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                     , GetFormValue_NullableInt("PageIndex", 0)
                     , GetFormValue_NullableInt("PageSize", 10)

                   );
               // bool active = false;
                JsonObject jsn = new JsonObject();
                JsonObject jsnRows = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {


                    JsonObject jsnRow = new JsonObject();
                    jsnRow.AddVariable("ID", lst[i].PartName);
                    jsnRow.AddVariable("Name", Functions.ReplaceLineBreaks(lst[i].PartNameWithManufacture));
                    jsnRow.AddVariable("Ids", lst[i].ManufacturerNo + "|" + lst[i].ProductNo + "|" + lst[i].PackageNo);
                    jsnRow.AddVariable("ManufacturerNo", lst[i].ManufacturerNo);
                    jsnRow.AddVariable("Manufacturer", lst[i].ManufacturerName);

                    jsnRow.AddVariable("Descriptions", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                    if (string.IsNullOrEmpty(lst[i].Descriptions))
                        jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                    else if (lst[i].Descriptions.Length <= 10)
                        jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions));
                    else
                        jsnRow.AddVariable("DescShort", Functions.ReplaceLineBreaks(lst[i].Descriptions.Substring(0, 10)));

                    jsnRow.AddVariable("ROHSNo", lst[i].ROHSNo);
                    jsnRow.AddVariable("ROHSName", lst[i].ROHSName);
                    jsnRow.AddVariable("CountryOfOrigin", lst[i].CountryOfOrigin);
                    jsnRow.AddVariable("CountryOfOriginNo", lst[i].CountryOfOriginNo);
                    jsnRow.AddVariable("PartStatus", Functions.ReplaceLineBreaks(lst[i].PartStatus));
                    jsnRow.AddVariable("HTSCode", Functions.ReplaceLineBreaks(lst[i].HTSCode));
                    jsnRow.AddVariable("AveragePrice", lst[i].AveragePrice);
                    jsnRow.AddVariable("Packaging", lst[i].Packaging);
                    jsnRow.AddVariable("PackagingSize", lst[i].PackagingSize);
                    jsnRow.AddVariable("IHSPartsId", lst[i].IHSPartsId);
                    jsnRow.AddVariable("ResultType", lst[i].ResultType);
                    jsnRow.AddVariable("ihsCurrencyCode", lst[i].IHSCurrencyCode);
                    jsnRow.AddVariable("ProdDesc", Functions.ReplaceLineBreaks(lst[i].ProductDescription));
                    jsnRow.AddVariable("ProdNo", lst[i].ProductNo);
                    jsnRow.AddVariable("IHSProdDesc", Functions.ReplaceLineBreaks(lst[i].IHSProductDescription));
                    jsnRow.AddVariable("IHSDutyCode", lst[i].IHSDutyCode);
                    jsnRow.AddVariable("RowCnt", lst[i].RowCnt);
                    jsnRow.AddVariable("ManufacturerFullName", lst[i].ManufacturerFullName);
                    jsnRow.AddVariable("IHSProduct", lst[i].IHSProduct);
                    jsnRow.AddVariable("ECCNCode", Functions.ReplaceLineBreaks(lst[i].ECCNCode));
                    jsnRows.AddVariable(jsnRow);
                    jsnRow.Dispose();
                    jsnRow = null;
                }
                jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
                jsn.AddVariable("Results", jsnRows);
                OutputResult(jsn);
                jsnRows.Dispose();
                jsnRows = null;
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            finally
            {
                lst = null;
            }
            base.GetData();
        }


















	}
}
