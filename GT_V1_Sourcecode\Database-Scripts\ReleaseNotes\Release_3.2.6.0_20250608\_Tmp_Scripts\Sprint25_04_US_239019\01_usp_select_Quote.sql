﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/* 
====================================================================================================================================
TASK      			UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[BUG-211583]		Trung Pham			05-Sep-2024		UPDATE			Add inactive in filter condition for Restricted Manufacturer
[US-239019]         Phuc Hoang		    05-Apr-2025		CREATE		    Quote Task Reminder Enhancement on Quote/ HUBRFQ
====================================================================================================================================
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_select_Quote]  
@QuoteId	int 
AS
BEGIN

DECLARE @IsLineHasRestMFR BIT
SET @IsLineHasRestMFR=0
DECLARE @ClientNo int

SELECT @ClientNo=ClientNo FROM tbQuote WHERE QuoteId=@QuoteId

 IF EXISTS(SELECT 'a' FROM tbQuoteLine a JOIN tbRestrictedManufacturer b on a.ManufacturerNo=b.ManufacturerNo
 WHERE QuoteNo=@QuoteId AND b.ClientNo=@ClientNo AND b.Inactive = 0)
 BEGIN
   SET @IsLineHasRestMFR=1
 END

SELECT	q.*,cu.GlobalCurrencyNo ,
 dbo.ufn_check_CurrencyInSameFaimly(q.ClientNo,q.CurrencyNo) as IsCurrencyInSameFaimly
 , qs.[Name] as QuoteStatusName, @IsLineHasRestMFR AS IsLineHasRestMFR
 , CASE WHEN EXISTS(SELECT TOP 1 1 FROM tbToDo td WITH(NOLOCK) WHERE td.QuoteNo = q.QuoteId AND td.IsComplete = 0) 
		THEN 1 ELSE 0 END AS HasUnFinishedTask

FROM 	dbo.vwQuote q
LEFT JOIN tbCurrency cu on cu.CurrencyId = q.SOCurrencyNo
LEFT JOIN tbQuoteStatus qs on qs.QuoteStatusId = q.QuoteStatus
WHERE	QuoteId		= @QuoteId

END



GO


