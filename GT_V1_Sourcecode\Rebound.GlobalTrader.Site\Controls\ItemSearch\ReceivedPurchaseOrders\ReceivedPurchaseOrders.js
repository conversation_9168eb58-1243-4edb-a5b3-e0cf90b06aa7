Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.ReceivedPurchaseOrders=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.ReceivedPurchaseOrders.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.ItemSearch.ReceivedPurchaseOrders.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.ReceivedPurchaseOrders.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.ItemSearch.ReceivedPurchaseOrders.callBaseMethod(this,"dispose")},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/ReceivedPurchaseOrders");this._objData.set_DataObject("ReceivedPurchaseOrders");this._objData.set_DataAction("GetData");this._objData.addParameter("Contact",this.getFieldValue("ctlContact"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("IncludeClosed",this.getFieldValue("ctlIncludeClosed"));this._objData.addParameter("Buyer",this.getFieldValue("ctlBuyer"));this._objData.addParameter("PurchaseOrderNoLo",this.getFieldValue_Min("ctlPurchaseOrderNo"));this._objData.addParameter("PurchaseOrderNoHi",this.getFieldValue_Max("ctlPurchaseOrderNo"));this._objData.addParameter("DateOrderedFrom",this.getFieldValue("ctlDateOrderedFrom"));this._objData.addParameter("DateOrderedTo",this.getFieldValue("ctlDateOrderedTo"));this._objData.addParameter("InternalPurchaseOrderNoLo",this.getFieldValue_Min("ctlInternalPurchaseOrderNo"));this._objData.addParameter("InternalPurchaseOrderNoHi",this.getFieldValue_Max("ctlInternalPurchaseOrderNo"))},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.setCleanTextValue(n.Contact),$R_FN.setCleanTextValue(n.Date),$R_FN.setCleanTextValue(n.Buyer),n.LastReceived],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.ReceivedPurchaseOrders.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.ReceivedPurchaseOrders",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);