using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

    public class PurchaseQuotes : Base
    {

		/// <summary>
		/// Gets the main data
		/// </summary>
		protected override void GetData() {
			JsonObject jsn = new JsonObject();

			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

			//get data	
            //List<PurchaseQuoteLine> lst = null;//Taken by later
            List<PurchaseQuoteLine> lst = PurchaseQuoteLine.DataListNugget(
                SessionManager.ClientID
                , (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
                , (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
                , (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
                , GetFormValue_NullableInt("SortIndex")
                , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                , GetFormValue_NullableInt("PageIndex", 0)
                , GetFormValue_NullableInt("PageSize", 10)
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
                //, GetFormValue_StringForNameSearch("Contact")
              //  , GetFormValue_StringForNameSearchDecode("Contact")
              , null
                //, GetFormValue_StringForNameSearch("CMName")
                , GetFormValue_StringForNameSearchDecode("CMName")
                , GetFormValue_NullableInt("Salesman")
                , GetFormValue_Boolean("IncludeClosed")
                , GetFormValue_NullableInt("POQuoteNoLo")
                , GetFormValue_NullableInt("POQuoteNoHi")
                , GetFormValue_NullableDateTime("DatePOQuotedFrom")
                , GetFormValue_NullableDateTime("DatePOQuotedTo")
                , GetFormValue_Boolean("RecentOnly")
            );

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format data
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].PurchaseQuoteLineId);
                jsnRow.AddVariable("POQNo", lst[i].PurchaseQuoteNo);
				jsnRow.AddVariable("POQNu", lst[i].PurchaseQuoteNumber);
				jsnRow.AddVariable("Part", lst[i].Part);
				//jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].UnitPrice, lst[i].CurrencyCode));
				jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].Quantity));
				jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].DatePOQuoted));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				//jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("BOM", lst[i].BOMName);
				jsnRow.AddVariable("BOMNo", lst[i].BOMNo);
                jsnRow.AddVariable("Emp", lst[i].SalesmanName);
				//jsnRow.AddVariable("ROHS", lst[i].ROHS);
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Salesman");
			AddFilterState("IncludeClosed");
            AddFilterState("POQuoteNo");
            AddFilterState("DatePOQuotedFrom");
            AddFilterState("DatePOQuotedTo");
			AddFilterState("RecentOnly");
			base.AddFilterStates();
		}

	}
}
