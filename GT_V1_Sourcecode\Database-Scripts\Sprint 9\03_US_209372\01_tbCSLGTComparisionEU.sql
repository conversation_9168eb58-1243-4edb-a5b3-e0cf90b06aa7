﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[BUG-209372]    cuongdx			21-AUG-2024			Create		compare company from EU Sanctioned list
===========================================================================================  
*/ 
DROP TABLE IF EXISTS [tbCSLGTComparisionEU];

CREATE TABLE [dbo].[tbCSLGTComparisionEU](
	[RecordID] [int] IDENTITY(1,1) NOT NULL,
	[companyid] [int] NULL,
	[CompanyNum] [bigint] NULL,
	[CustomerCode] [nvarchar](max) NULL,
	[ClientNo] [int] NULL,
	[ClientName] [varchar](max) NULL,
	[CompanyName] [varchar](max) NULL,
	[GT_Company_Address] [varchar](max) NULL,
	[CSL_Name] [varchar](max) NULL,
	[CSL_Address] [varchar](max) NULL,
	[CSL_ALT_Name] [varchar](max) NULL,
	[Insertedon] [datetime] NULL,
	[Notes] [nvarchar](max) NULL,
	[ImportantNotes] [nvarchar](max) NULL,
	[ERAIReported] [nvarchar](10) NULL
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [companyid]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [CompanyNum]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [CustomerCode]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [ClientNo]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [ClientName]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [CompanyName]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [GT_Company_Address]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [CSL_Name]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [CSL_Address]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [CSL_ALT_Name]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [Insertedon]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [Notes]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [ImportantNotes]
GO

ALTER TABLE [dbo].[tbCSLGTComparisionEU] ADD  DEFAULT (NULL) FOR [ERAIReported]
GO


