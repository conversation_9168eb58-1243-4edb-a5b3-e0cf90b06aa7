///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.prototype = {

	get_blnForPOs: function() { return this._blnForPOs; }, 	set_blnForPOs: function(v) { if (this._blnForPOs !== v)  this._blnForPOs = v; }, 
	get_blnForSOs: function () { return this._blnForSOs; }, set_blnForSOs: function (v) { if (this._blnForSOs !== v) this._blnForSOs = v; },
	get_blnShowPOFilter: function () { return this._blnShowPOFilter; }, set_blnShowPOFilter: function (v) { if (this._blnShowPOFilter !== v) this._blnShowPOFilter = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
		$R_FN.showElement(this.getField("ctlPurchaseOrderNo")._element, this._blnShowPOFilter);
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._blnForPOs = null;
		this._blnForSOs = null;
		this._blnShowPOFilter = null;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.callBaseMethod(this, "dispose");
	},

	doSetupData: function () {
	   
		this._objData.set_PathToData("controls/ItemSearch/Supplier");
		this._objData.set_DataObject("Supplier");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Name", this.getFieldValue("ctlCompanyName"));
		if (this._blnForPOs) this._objData.addParameter("POApproved", true);
		if (this._blnForSOs) this._objData.addParameter("SOApproved", true);
		this._objData.addParameter("PONoLo", this.getFieldValue_Min("ctlPurchaseOrderNo"));
		this._objData.addParameter("PONoHi", this.getFieldValue_Max("ctlPurchaseOrderNo"));
		//this._objData.addParameter("isClientInvoice", this._isClientInvoice);
		
	},
	
	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				$R_FN.setCleanTextValue(row.Name),
				$R_FN.setCleanTextValue(row.SupplierCode),
				$R_FN.setCleanTextValue(row.Type),
				$R_FN.setCleanTextValue(row.City),
				$R_FN.setCleanTextValue(row.Country),
				$R_FN.setCleanTextValue(row.Tel),
				$R_FN.setCleanTextValue(row.Salesperson),
				$R_FN.setCleanTextValue(row.LastContact)
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
		
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Supplier", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
