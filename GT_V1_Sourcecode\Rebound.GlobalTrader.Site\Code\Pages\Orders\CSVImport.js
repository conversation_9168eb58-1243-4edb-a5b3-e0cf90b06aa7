Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.CSVImport=function(n){Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.prototype={get_ctlUploadExcelDragDrop:function(){return this._ctlUploadExcelDragDrop},set_ctlUploadExcelDragDrop:function(n){this._ctlUploadExcelDragDrop!==n&&(this._ctlUploadExcelDragDrop=n)},get_ctlCsvUploadHistory:function(){return this._ctlCsvUploadHistory},set_ctlCsvUploadHistory:function(n){this._ctlCsvUploadHistory!==n&&(this._ctlCsvUploadHistory=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.callBaseMethod(this,"initialize")},goInit:function(){this._ctlUploadExcelDragDrop&&this._ctlUploadExcelDragDrop.addPotentialStatusChange(Function.createDelegate(this,this.ctlUploadExcelDragDrop_PotentialStatusChange));Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlUploadExcelDragDrop&&this._ctlUploadExcelDragDrop.dispose(),this._ctlCsvUploadHistory&&this._ctlCsvUploadHistory.dispose(),this._ctlUploadExcelDragDrop=null,this._ctlCsvUploadHistory=null,Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.callBaseMethod(this,"dispose"))},ctlUploadExcelDragDrop_PotentialStatusChange:function(){this._ctlCsvUploadHistory.getHistory()}};Rebound.GlobalTrader.Site.Pages.Orders.CSVImport.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.CSVImport",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);