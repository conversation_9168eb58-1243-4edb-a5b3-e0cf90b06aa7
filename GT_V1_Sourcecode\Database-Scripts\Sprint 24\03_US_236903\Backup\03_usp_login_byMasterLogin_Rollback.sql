﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO



--select * from t

CREATE OR ALTER PROCEDURE [dbo].[usp_login_byMasterLogin]
   @AdLoginName VARCHAR(150)
  , @IPAddress nvarchar(15)      
  , @SessionId nvarchar(20)   
  , @Server<PERSON> nvarchar(15) = null    
  , @Result int OUTPUT    
  , @LoginName varchar(100)='' OUTPUT  
AS
BEGIN
DECLARE @LoginId int ;  
		--clear old sessions      
			EXEC usp_update_Session_ClearOldSessions      
            
			set @LoginName ='NotFoundAnyUser'
			 -- check if loginName is valid      
    IF EXISTS ( SELECT MasterLoginId from tbMasterLogin WHERE ADLoginName = @AdLoginName and isnull(inactive,0)=0)       
        BEGIN      
  -- get loginName     
            DECLARE @MasterLoginNo INT
			DECLARE @DefaultClientNo INT
			SELECT top 1  @MasterLoginNo = MasterLoginId,@DefaultClientNo = LastClientNo from tbMasterLogin WHERE ADLoginName = @AdLoginName 
			
			IF @DefaultClientNo IS NULL And @MasterLoginNo is not null
			BEGIN
			   declare @LastClientNo int
			   SELECT top 1 @LastClientNo = ClientNo from tbLogin where MasterLoginNo=@MasterLoginNo and LoginId<>0 order by ClientNo asc
			   UPDATE tbMasterLogin SET LastClientNo=@LastClientNo where MasterLoginId=@MasterLoginNo
			END 
			
			SELECT top 1 @LoginId = LoginId   , @LoginName = LoginName   
                            FROM    dbo.tbLogin     
                            WHERE   MasterLoginNo = @MasterLoginNo and ClientNo= @DefaultClientNo 

							if @LoginId is null
							begin
							  SET @Result = 99      
                               RETURN 99   
							end
     
        END      
     ELSE       
        BEGIN      
            SET @Result = 99      
            RETURN 99      
        END

		 IF (SELECT  count(*)      
        FROM    dbo.tbSession      
        WHERE   LoginNo = @LoginId      
       ) > 0       
        BEGIN      
            UPDATE  dbo.tbSession      
            SET     SessionTimestamp = CURRENT_TIMESTAMP      
                  , SessionName = @SessionId      
                  , IPAddress = @IPAddress     
                  , ServerIP = @ServerIP   
            WHERE   LoginNo = @LoginId       
        END      
    ELSE       
        BEGIN      
            INSERT  INTO dbo.tbSession      
                    (LoginNo      
                   , SessionName      
                   , SessionTimestamp      
                   , StartTime      
                   , IPAddress  
                   , ServerIP)      
            VALUES  (      
                     @LoginId      
                   , @SessionId      
                   , CURRENT_TIMESTAMP      
                   , CURRENT_TIMESTAMP      
                   , @IPAddress  
                   , @ServerIP)        
        END   
		SET @Result = 0      
    RETURN @Result   
END





GO


