using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {

	/// <summary>
	/// Common functionality for left-side nuggets
	/// </summary>
	public class Base : UserControl, IScriptControl, INamingContainer {

		#region Locals

		public DesignBase ctlDesignBase;
		protected ScriptManager _sm;
		protected ScriptReference[] _srScriptReference = new ScriptReference[5];
		private int _intNumberOfScriptReferences = 0;
		protected ScriptControlDescriptor _scScriptControlDescriptor;
		protected Rebound.GlobalTrader.Site.ItemSearch _objItemSearch;

		#endregion

		#region Properties

		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("ItemSearch.css");
			ctlDesignBase = (DesignBase)Functions.FindControlRecursive(this, "ctlDB");
			if (ctlDesignBase == null) throw new Exception("An ItemSearch must have a Design Base control with ID 'ctlDB' - have you checked Web.config?");
			AddScriptReference("Controls.ItemSearch._Bases.Base.js");
			base.OnInit(e);
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			if (!this.DesignMode) _sm.RegisterScriptDescriptors(this);
			base.Render(writer);
		}

		#endregion

		#region Methods

		protected void SetItemSearchType(string strType) {
			_objItemSearch = Rebound.GlobalTrader.Site.Site.GetInstance().GetItemSearch(strType);
		}

		/// <summary>
		/// Add AJAX script reference
		/// </summary>
		/// <param name="sr"></param>
		protected void AddScriptReference(bool blnDebug, string strAssembly, string strRef) {
			ScriptReference sr = Functions.GetScriptReference(blnDebug, strAssembly, strRef, true);
			_srScriptReference[_intNumberOfScriptReferences] = sr;
			_intNumberOfScriptReferences += 1;
		}
		protected void AddScriptReference(string strRef) {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			AddScriptReference(blnDebug, "Rebound.GlobalTrader.Site", strRef);
		}

		#endregion

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			return _srScriptReference;
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			_scScriptControlDescriptor.AddElementProperty("pnlLoading", ctlDesignBase.pnlLoading.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlError", ctlDesignBase.pnlError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblError", ctlDesignBase.lblError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlContent", ctlDesignBase.pnlContent.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlNoneFound", ctlDesignBase.pnlNoneFound.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblResults", ctlDesignBase.tblResults.ClientID);
			if (ctlDesignBase.ShowPaging) _scScriptControlDescriptor.AddComponentProperty("ctlPagingButtons", ctlDesignBase.ctlPagingButtons.ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnSearch", ctlDesignBase.ibtnSearch.ClientID);
			_scScriptControlDescriptor.AddProperty("objFieldIDs", ctlDesignBase.dctFieldIDs);
			_scScriptControlDescriptor.AddProperty("aryFieldIDs", ctlDesignBase.lstFieldIDs);
			_scScriptControlDescriptor.AddProperty("enmInitialSortDirection", ctlDesignBase.InitialSortDirection);
			_scScriptControlDescriptor.AddProperty("intItemSearchID", _objItemSearch.ID);

			return new ScriptDescriptor[] { _scScriptControlDescriptor };
		}

		System.Collections.Generic.IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() { return GetScriptDescriptors(); }
		System.Collections.Generic.IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() { return GetScriptReferences(); }

		#endregion

	}
}