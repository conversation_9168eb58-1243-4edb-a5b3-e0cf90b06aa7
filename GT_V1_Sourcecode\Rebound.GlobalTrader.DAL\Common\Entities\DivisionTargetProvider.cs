﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Data.Common;

using Rebound.GlobalTrader;

namespace Rebound.GlobalTrader.DAL
{
	
	public abstract class DivisionTargetProvider : DataAccess {
		static private DivisionTargetProvider _instance = null;
		/// <summary>
		/// Returns an instance of the provider type specified in the config file
		/// </summary>       
		static public DivisionTargetProvider Instance {
			get {
				if (_instance == null) _instance = (DivisionTargetProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.DivisionTargets.ProviderType));
				return _instance;
			}
		}
		public DivisionTargetProvider() {
			this.ConnectionString = Globals.Settings.DivisionTargets.ConnectionString;
		}

		#region Method Registrations

		/// <summary>
		/// Update
		/// Calls [KPI_GetDetailsEditGrid]
		/// </summary>
		public abstract List<DivisionTargetDetails> GetTargetDataForEdit(System.Int32? clientID, System.Int32? selectedDDLID, System.Int32? Userid, System.Int32 yearNo, System.String reqDataType);

		/// <summary>
		/// soorya
		/// Update
		/// Calls [KPI_UpdateDraftGridDetails]
		/// </summary>
		public abstract bool UpdateDivisionGrid(List<DivisionTargetDetails> updateDivisions);

		public abstract bool SubmitEditGridChanges(System.Int32? clientID, System.Int32? Userid);

		public abstract List<DivisionTargetDetails> GetDivisionAndTeamTarget(System.Int32? divisionNo, System.Int32? managerNo, System.Int32 yearNo, System.String table);
		/// <summary>
		/// Update
		/// Calls [usp_update_DivisionTeamTarget]
		/// </summary>
		public abstract bool Update(System.Int32? rowId, System.String rowType, System.String columnName, System.Double? targetValue, System.Int32? updatedBy, System.Int32? Year, System.Int32? divisionNo);

		/// <summary>
		/// Update
		/// Calls [usp_saveAllDivisionTeamTarget]
		/// </summary>
		public abstract bool SaveAllDivisionTeamTarget(System.Int32? Year, System.Int32? divisionNo, System.Int32? updatedBy);

		#endregion

		/// <summary>
		/// Returns a new DivisionDetails instance filled with the DataReader's current record data
		/// </summary>        
		protected virtual DivisionDetails GetDivisionFromReader(DbDataReader reader) {
			DivisionDetails division = new DivisionDetails();
			if (reader.HasRows) {
				division.DivisionId = GetReaderValue_Int32(reader, "DivisionId", 0); //From: [Table]
				division.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0); //From: [Table]
				division.DivisionName = GetReaderValue_String(reader, "DivisionName", ""); //From: [usp_select_Credit]
				division.AddressNo = GetReaderValue_NullableInt32(reader, "AddressNo", null); //From: [Table]
				division.Manager = GetReaderValue_NullableInt32(reader, "Manager", null); //From: [Table]
				division.Budget = GetReaderValue_NullableDouble(reader, "Budget", null); //From: [Table]
				division.Telephone = GetReaderValue_String(reader, "Telephone", ""); //From: [Table]
				division.Fax = GetReaderValue_String(reader, "Fax", ""); //From: [Table]
				division.EMail = GetReaderValue_String(reader, "EMail", ""); //From: [Table]
				division.Notes = GetReaderValue_String(reader, "Notes", ""); //From: [usp_select_Address_DefaultBilling_for_Company]
				division.URL = GetReaderValue_String(reader, "URL", ""); //From: [Table]
				division.Inactive = GetReaderValue_Boolean(reader, "Inactive", false); //From: [Table]
				division.HasDocumentHeaderImage = GetReaderValue_Boolean(reader, "HasDocumentHeaderImage", false); //From: [Table]
				division.UseCompanyHeaderForInvoice = GetReaderValue_Boolean(reader, "UseCompanyHeaderForInvoice", false); //From: [Table]
				division.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null); //From: [Table]
				division.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue); //From: [Table]
				division.ManagerName = GetReaderValue_String(reader, "ManagerName", ""); //From: [usp_select_Division]
				division.NumberOfMembers = GetReaderValue_NullableInt32(reader, "NumberOfMembers", null); //From: [usp_selectAll_Division_for_Client]
			}
			return division;
		}
	
		/// <summary>
		/// Returns a collection of DivisionDetails objects with the data read from the input DataReader
		/// </summary>                
		protected virtual List<DivisionDetails> GetDivisionCollectionFromReader(DbDataReader reader) {
			List<DivisionDetails> divisions = new List<DivisionDetails>();
			while (reader.Read()) divisions.Add(GetDivisionFromReader(reader));
			return divisions;
		}
		
		
	}
}