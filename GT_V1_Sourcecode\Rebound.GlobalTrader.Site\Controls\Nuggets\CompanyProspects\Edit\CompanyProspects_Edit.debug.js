///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
/*
Marker     Changed by      Date         Remarks
[001]       Bhooma Nand   28/01/2021       Added New Nugget for CRM prospects
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.initializeBase(this, [element]);
    this._intCustomerID = -1
    this._intCRMProspectID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));

    },

    dispose: function () {
        if (this.isDisposed) return;
        this._intCustomerID = null;
        if (this._ctlSelectIndustryType) this._ctlSelectIndustryType.dispose();
        this._ctlSelectIndustryType = null;
        if (this._ctlSelectEntertainmentType) this._ctlSelectEntertainmentType.dispose();
        this._ctlSelectEntertainmentType = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this.addSave(Function.createDelegate(this, this.saveClicked));
            this._ctlSelectIndustryType = $find(this.getField("ctlIndustryType").ControlID);
            //this._ctlSelectEntertainmentType = $find(this.getField("ctlEntertainmentType").ControlID);
            //this._ctlSelectIndustryAreaType = $find(this.getField("ctlIndustryAreaType").ControlID);
        }
        this.getFieldControl("ctlProspectType")._CompanyProspectsDDLType = 'ProspectType';
        this.getFieldDropDownData("ctlProspectType");
        //this.getFieldControl("ctlIndustry")._CompanyProspectsDDLType = 'Industry';
        //this.getFieldDropDownData("ctlIndustry");
        this.getFieldControl("ctlElectronicSpend")._CompanyProspectsDDLType = 'ElectronicSpend';
        this.getFieldDropDownData("ctlElectronicSpend");
        this.getFieldControl("ctlFrequencyOfPurchase")._CompanyProspectsDDLType = 'FrequencyOfPurchase';
        this.getFieldDropDownData("ctlFrequencyOfPurchase");
        //this.getFieldControl("ctlCommodities")._CompanyProspectsDDLType = 'Commodities';
        //this.getFieldDropDownData("ctlCommodities");
        this.getFieldControl("ctlTurnover")._CompanyProspectsDDLType = 'Turnover';
        this.getFieldDropDownData("ctlTurnover");
       
    },
    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyProspects");
        obj.set_DataObject("CompanyProspects");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intCustomerID);
        obj.addParameter("CRMProspectId", this._intCRMProspectID);
        obj.addParameter("ProspectTypeId", this.getFieldValue("ctlProspectType"));
        var IsChekBoxChecked = ($get('ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel_0')).checked == true ? true : ($get('ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlMFRBoardLevel_ctl03_radMFRBoardLevel_1')).checked == true ? false : null;
        obj.addParameter("IscrmProspectBoardLevel", IsChekBoxChecked != null ? IsChekBoxChecked.toString() : null);
        IsChekBoxChecked = null;
        IsChekBoxChecked = ($get('ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly_0')).checked == true ? true : ($get('ctl00_cphMain_ctlCRMProspects_ctlDB_ctl14_ctlCompanyProspects_Edit_ctlDB_ctlFinalAssembly_ctl03_radFinalAssembly_1')).checked == true ? false : null;
        obj.addParameter("IsFinalAssembly", IsChekBoxChecked != null ? IsChekBoxChecked.toString() : null);
        obj.addParameter("EndCustomer", this.getFieldValue("ctlEndCustomer"));
        //obj.addParameter("IndustryId", this.getFieldValue("ctlIndustry"));
        obj.addParameter("IsIndustry", 0);
        obj.addParameter("LimitedEstimate", this.getFieldValue("ctlLimitedEstimate"));
        obj.addParameter("HealthRating", this.getFieldValue("ctlHealthRating"));
        obj.addParameter("ElectronicSpendId", this.getFieldValue("ctlElectronicSpend"));
        obj.addParameter("FrequencyOfPurchaseId", this.getFieldValue("ctlFrequencyOfPurchase"));
        obj.addParameter("Commoditiestext", this.getFieldValue("ctlCommodities"));
        obj.addParameter("TurnoverId", this.getFieldValue("ctlTurnover"));
        obj.addParameter("IndustryTypes", this._ctlSelectIndustryType.getValuesAsString(true));
        //obj.addParameter("EntertainmentTypes", this._ctlSelectEntertainmentType.getValuesAsString(true));
        //obj.addParameter("IndustryAreaTypes", this._ctlSelectIndustryAreaType.getValuesAsString(true));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function (args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
            $("#ctl00_cphMain_ctlMainCompanyInfo_ctlDB_imgRefresh").trigger('click');
        } else {
            if (args._result.Message) this._strErrorMessage = args._result.Message;
            this.onSaveError();
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyProspects_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
