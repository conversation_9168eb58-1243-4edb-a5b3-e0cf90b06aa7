<%@ Control Language="C#" CodeBehind="BOMCusReqSourcingResults_Delete.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Delete" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false"
    AllowQuickHelp="false">
    <Explanation>
        <%=Functions.GetGlobalResource("FormExplanations", "BOMCusReqSourcingResults_Delete")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server">
            <ReboundUI_Form:FormField ID="ctlDelete" runat="server" FieldID="ctlDelete"
                ResourceTitle="SaveConfirm">
                <Field>
                    <ReboundUI:Confirmation ID="ctlDelete" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>
