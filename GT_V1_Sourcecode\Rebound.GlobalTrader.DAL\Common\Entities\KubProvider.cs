﻿using System;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.DAL
{
    //Anuj
    public abstract class KubProvider : DataAccess
    {
        static private KubProvider _instance = null;
        /// <summary>
        /// Returns an instance of the provider type specified in the config file
        /// </summary>       
        static public KubProvider Instance
        {
            get
            {
                if (_instance == null) _instance = (KubProvider)Activator.CreateInstance(Type.GetType(Globals.Settings.Kubs.ProviderType));
                return _instance;
            }
        }
        public KubProvider()
        {
            this.ConnectionString = Globals.Settings.Kubs.ConnectionString;
        }

        #region Method Registrations
        public abstract KubAssistanceDetails FetchKubAssistanceDetails(System.String PartNo, System.Int32 ClientID);
        public abstract KubAssistanceDetails ShowReadMoreData(System.String PartNo, System.Int32 ClientID);
        public abstract KubAssistanceDetails StartKubCache(System.String PartNo, System.Int32 ClientID);
        public abstract List<KubCountryWiseSaleDetails> ListKubCountryWiseSaleDetails(System.String PartNo, System.Int32 ClientID);
        public abstract List<KubTop3BuyPriceDetails> ListKubTop3BuyPriceDetails(System.String PartNo, System.Int32 ClientID);
        public abstract List<KubLast10RecentQuoteDetails> ListKubLast10RecentQuoteDetails(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID);
        public abstract KubAvgPriceDetails FetchKubAvgPriceDetails(System.String PartNo, System.Int32 ClientID);
        public abstract KubTotalLineInvoiceDetails FetchKubTotalLineInvoiceDetails(System.String PartNo, System.Int32 ClientID, System.Int32? CustomerReqId);
        public abstract List<KubMainProductGroupDetails> ListKubMainProductGroupDetails(System.String PartNo, System.Int32 ClientID);
        public abstract List<KubLast10RecentQuoteDetails> StartKubCacheForBrowsePage(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID);

        public abstract List<KubLast10RecentQuoteDetails> IsAllowedEnabled(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID);

        public abstract SQLAllowedEnabledForHUB IsAllowedEnabledForHUB(System.String PartNo, System.Int32 ClientID, int bomId, int manufacturerId, string manufacturerName, bool isHubRFQ);

        public abstract List<KubCountryWiseSaleDetails> GetGPCalculationDetails(System.String PartNo, System.Int32 ClientID);

        public abstract List<KubCountryWiseSaleDetails> GetGPLastSaleCalculationDetails(System.String PartNo, System.Int32 ClientID);

        public abstract KubAssistanceDetails LoadKubAssistanceForBOMManager(System.String PartNo, System.Int32 ClientID, int BOMManagerID, bool isHubRFQ, int manufacturerId, string manufacturerName);

        public abstract List<KubTop3BuyPriceDetails> ListKubTop3BuyPriceDetailsForHUB(System.String PartNo, System.Int32 ClientID);

        public abstract List<KubTop20CustomeRequirementForBOM> ListKubTop20CustomerRequirementForBOM(System.String PartNo, System.Int32 ClientID);
        
        public abstract List<SqlKubTop10QuoteForBOM> ListKubTop10QuoteForBOM(System.String PartNo, System.Int32 ClientID);
        public abstract KubStockDetailsForBOM GetStockDetailsForBOMPart(string partNo, int clientId, int BOMId, bool isHubRFQ);
        #endregion

    }
}
