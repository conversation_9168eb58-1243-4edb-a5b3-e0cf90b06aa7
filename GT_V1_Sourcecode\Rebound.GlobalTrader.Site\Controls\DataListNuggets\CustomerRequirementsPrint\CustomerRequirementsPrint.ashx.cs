using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

    public class CustomerRequirementsPrint : Base
    {

        /// <summary>
        /// Gets the main data
        /// </summary>
        protected override void GetData() {
            JsonObject jsn = new JsonObject();
            int? getPageLimit = GetFormValue_NullableInt("PageLimit") > 0 ? GetFormValue_NullableInt("PageLimit") : 50;
            if (GetFormValue_Boolean("IsGet"))
            {
                List<CustomerRequirement> lst = CustomerRequirement.DataListNuggetPrint(
                    SessionManager.ClientID
                    , (int?)LoginID
                    , GetFormValue_NullableInt("SortIndex", 0)
                    , GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
                    , GetFormValue_NullableInt("PageIndex", 0)
                    //, GetFormValue_NullableInt("PageSize", 50)
                    , GetFormValue_NullableInt("NewPageSize", getPageLimit)
                    , GetFormValue_StringForNameSearch("CMName")
                    , GetFormValue_NullableInt("Salesman")
                    , GetFormValue_NullableInt("CmpID")
                    , GetFormValue_NullableDateTime("ReceivedDateFrom")
                    , GetFormValue_NullableDateTime("ReceivedDateTo")
                    , GetFormValue_NullableDateTime("DatePromisedFrom")
                    , GetFormValue_NullableDateTime("DatePromisedTo")
                    , GetFormValue_NullableInt("ConID")
                );

                //check counts
                jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

                //format data
                JsonObject jsnRowsArray = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    if (i < lst.Count)
                    {
                        JsonObject jsnRow = new JsonObject();
                        jsnRow.AddVariable("ID", lst[i].CustomerRequirementId);
                        jsnRow.AddVariable("No", lst[i].CustomerRequirementNumber);
                        jsnRow.AddVariable("Part", lst[i].Part);
                        jsnRow.AddVariable("ROHS", lst[i].ROHS);
                        jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
                        jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
                        jsnRow.AddVariable("Quantity", lst[i].Quantity);
                        jsnRow.AddVariable("CM", lst[i].CompanyName);
                        jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                        jsnRow.AddVariable("Contact", lst[i].ContactName);
                        jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
                        jsnRow.AddVariable("Salesman", lst[i].SalesmanName);
                        jsnRow.AddVariable("Received", Functions.FormatDate(lst[i].ReceivedDate));
                        jsnRow.AddVariable("Promised", Functions.FormatDate(lst[i].DatePromised));
                        jsnRowsArray.AddVariable(jsnRow);
                        jsnRow.Dispose();
                        jsnRow = null;
                    }
                }
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose();
                jsnRowsArray = null;
                jsn.Dispose();
                jsn = null;
                base.GetData();
            }
            else
            {
                jsn.AddVariable("TotalRecords",  0);
                JsonObject jsnRowsArray = new JsonObject(true);
                jsn.AddVariable("Results", jsnRowsArray);
                OutputResult(jsn);
                jsnRowsArray.Dispose();
                jsnRowsArray = null;
                jsn.Dispose();
                jsn = null;
                base.GetData();
            }
        }

        protected override void AddFilterStates()
        {
           //  AddFilterState("CMName");
            // AddFilterState("Salesman");
            //base.AddFilterStates();
        }
    }
}
