﻿
/*
============================================================================================================================= 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-208547]     Phuc Hoang		 01-Dec-2024		CREATE		Company Settings - Mass Restricted/ Unrestricted Manufacturer
=============================================================================================================================  
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_update_MultipleRestrictedManufacturer] (
    @ClientNo INT,
    @ManufacturerNos NVARCHAR(2000),
	@MFRNameSuffix NVARCHAR(255) = Null,
    @Notes NVARCHAR(500) = Null,
    @InActive BIT = Null,
    @UpdatedBy INT = Null,
    @RowsAffected INT = NULL OUTPUT 
)               
AS 
BEGIN 

	SELECT String as ManufacturerNo
	INTO #tempManufacturersNo
	FROM dbo.[ufn_splitString](@ManufacturerNos, '||');

	SELECT temp.ManufacturerNo, m.ManufacturerName
	INTO #tempRestrictedManufacturers
	FROM #tempManufacturersNo temp
	INNER JOIN tbManufacturer m ON temp.ManufacturerNo = m.ManufacturerId;

	UPDATE rm 
	SET      
		rm.Notes  = @Notes
		, rm.ManufacturerNameSuffix = @MFRNameSuffix
		, rm.ClientNo    = @ClientNo 
		, rm.Inactive    = @Inactive 
		, rm.UpdatedBy    = @UpdatedBy 
		, rm.DLUP     = current_timestamp 
	FROM dbo.tbRestrictedManufacturer AS rm 
		INNER JOIN dbo.tbManufacturer AS m ON rm.ManufacturerNo=m.ManufacturerId 
		INNER JOIN #tempRestrictedManufacturers temp ON temp.ManufacturerName = m.ManufacturerName 
	WHERE m.Inactive=0 AND rm.ClientNo=@ClientNo;

	DROP TABLE #tempManufacturersNo, #tempRestrictedManufacturers;

	SELECT @RowsAffected = @@ROWCOUNT;
END 
