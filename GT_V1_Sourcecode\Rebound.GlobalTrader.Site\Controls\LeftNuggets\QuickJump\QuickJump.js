Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets");Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.initializeBase(this,[n]);this._intID=-1;this._intMaxRadioButtons=15};Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.prototype={get_ibtnGo:function(){return this._ibtnGo},set_ibtnGo:function(n){this._ibtnGo!==n&&(this._ibtnGo=n)},get_txt:function(){return this._txt},set_txt:function(n){this._txt!==n&&(this._txt=n)},get_rad:function(){return this._rad},set_rad:function(n){this._rad!==n&&(this._rad=n)},get_pnlNotFound:function(){return this._pnlNotFound},set_pnlNotFound:function(n){this._pnlNotFound!==n&&(this._pnlNotFound=n)},get_enmSection:function(){return this._enmSection},set_enmSection:function(n){this._enmSection!==n&&(this._enmSection=n)},get_enmSelectedType:function(){return this._enmSelectedType},set_enmSelectedType:function(n){this._enmSelectedType!==n&&(this._enmSelectedType=n)},get_pnlQJSearchResultTooltip:function(){return this._pnlQJSearchResultTooltip},set_pnlQJSearchResultTooltip:function(n){this._pnlQJSearchResultTooltip!==n&&(this._pnlQJSearchResultTooltip=n)},get_pnlQJSearchResult:function(){return this._pnlQJSearchResult},set_pnlQJSearchResult:function(n){this._pnlQJSearchResult!==n&&(this._pnlQJSearchResult=n)},get_ibtnShowRelDoc:function(){return this._ibtnShowRelDoc},set_ibtnShowRelDoc:function(n){this._ibtnShowRelDoc!==n&&(this._ibtnShowRelDoc=n)},initialize:function(){var n,t;for(Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.callBaseMethod(this,"initialize"),$R_IBTN.addClick(this._ibtnGo,Function.createDelegate(this,this.goClick)),$R_TXTBOX.addEnterPressedEvent(this._txt,Function.createDelegate(this,this.goClick)),$R_TXTBOX.removeInitialSettings(this._txt),n=0;n<this._intMaxRadioButtons;n++)t=$get(String.format("{0}_{1}",this._rad.id,n)),t&&$addHandler(t,"click",Function.createDelegate(this,this.changedSelection)),t=null;this._ibtnShowRelDoc&&$R_IBTN.addClick(this._ibtnShowRelDoc,Function.createDelegate(this,this.showRelatedDocument));this.changedSelection()},dispose:function(){var n,t;if(!this.isDisposed){for(this._ibtnGo&&$R_IBTN.clearHandlers(this._ibtnGo),n=0;n<this._intMaxRadioButtons;n++)t=$get(String.format("{0}_{1}",this._rad.id,n)),t&&$clearHandlers(t),t=null;this._txt&&$R_TXTBOX.clearEvents(this._txt);this._txt=null;this._rad=null;this._pnlNotFound=null;this._ibtnGo=null;this._pnlQJSearchResultTooltip=null;this._pnlQJSearchResult=null;Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.callBaseMethod(this,"dispose")}},changedSelection:function(){this.getSelectedItem();this._enmSection==$R_ENUM$QuickJumpSection.Warehouse&&this._enmSelectedType==$R_ENUM$QuickJumpType.Stock||this._enmSection==$R_ENUM$QuickJumpSection.Warehouse&&this._enmSelectedType==$R_ENUM$QuickJumpType.StockNumber||this._enmSection==$R_ENUM$QuickJumpSection.Orders&&this._enmSelectedType==$R_ENUM$QuickJumpType.Sourcing||this._enmSection==$R_ENUM$QuickJumpSection.Orders&&this._enmSelectedType==$R_ENUM$QuickJumpType.HUBRFQ?($R_TXTBOX.setMode(this._txt,$R_ENUM$TextBoxMode.Normal),$R_TXTBOX.setUpperCaseOnly(this._txt,!0),this._txt.value="",this._txt.setAttribute("Maxlength",40)):this._enmSection==$R_ENUM$QuickJumpSection.Contact?($R_TXTBOX.setMode(this._txt,$R_ENUM$TextBoxMode.Normal),$R_TXTBOX.setUpperCaseOnly(this._txt,!1),this._txt.setAttribute("Maxlength",40)):this._enmSection==$R_ENUM$QuickJumpSection.Warehouse&&this._enmSelectedType==$R_ENUM$QuickJumpType.NPR?($R_TXTBOX.setMode(this._txt,$R_ENUM$TextBoxMode.NumericDash),this._txt.setAttribute("Maxlength",30)):(isNaN(parseInt(this._txt.value,10))&&(this._txt.value=""),$R_TXTBOX.setMode(this._txt,$R_ENUM$TextBoxMode.Numeric),$R_TXTBOX.setUpperCaseOnly(this._txt,!1),this._txt.setAttribute("Maxlength",10))},goClick:function(){if(this.showNotFound(!1),!(this._txt.value.trim().length<1)){this._txt.value=this._txt.value.trim();switch(this._enmSection){case $R_ENUM$QuickJumpSection.Warehouse:switch(this._enmSelectedType){case $R_ENUM$QuickJumpType.ReceivePurchaseOrder:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetPurchaseOrderID",Function.createDelegate(this,this.goPOComplete));break;case $R_ENUM$QuickJumpType.ShipSalesOrder:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetSalesOrderID",Function.createDelegate(this,this.goSOComplete));break;case $R_ENUM$QuickJumpType.ReceiveCustomerRMA:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetCRMAID",Function.createDelegate(this,this.goCRMAComplete));break;case $R_ENUM$QuickJumpType.ShipSupplierRMA:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetSRMAID",Function.createDelegate(this,this.goSRMAComplete));break;case $R_ENUM$QuickJumpType.GoodsIn:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetGoodsInID",Function.createDelegate(this,this.goGoodsInComplete));break;case $R_ENUM$QuickJumpType.Stock:location.href=$RGT_gotoURL_StockBrowse(this._txt.value.toUpperCase());break;case $R_ENUM$QuickJumpType.NPR:this.getNPRIDLine(this._txt.value);break;case $R_ENUM$QuickJumpType.StockNumber:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetStockID",Function.createDelegate(this,this.goStockComplete))}break;case $R_ENUM$QuickJumpSection.Orders:switch(this._enmSelectedType){case $R_ENUM$QuickJumpType.CustomerRequirement:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetRequirementID",Function.createDelegate(this,this.goRequirementComplete));break;case $R_ENUM$QuickJumpType.Quote:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetQuoteID",Function.createDelegate(this,this.goQuoteComplete));break;case $R_ENUM$QuickJumpType.PurchaseOrder:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetPurchaseOrderID",Function.createDelegate(this,this.goPOComplete));break;case $R_ENUM$QuickJumpType.SalesOrder:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetSalesOrderID",Function.createDelegate(this,this.goSOComplete));break;case $R_ENUM$QuickJumpType.Invoice:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetInvoiceID",Function.createDelegate(this,this.goInvoiceComplete));break;case $R_ENUM$QuickJumpType.CustomerRMA:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetCRMAID",Function.createDelegate(this,this.goCRMAComplete));break;case $R_ENUM$QuickJumpType.SupplierRMA:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetSRMAID",Function.createDelegate(this,this.goSRMAComplete));break;case $R_ENUM$QuickJumpType.CreditNote:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetCreditID",Function.createDelegate(this,this.goCreditComplete));break;case $R_ENUM$QuickJumpType.DebitNote:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetDebitID",Function.createDelegate(this,this.goDebitComplete));break;case $R_ENUM$QuickJumpType.InternalPurchaseOrder:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetInternalPurchaseOrderID",Function.createDelegate(this,this.goIPOComplete));break;case $R_ENUM$QuickJumpType.HUBRFQ:this.doDataCall("GetHUBRFQID",Function.createDelegate(this,this.goHUBRFQComplete));break;case $R_ENUM$QuickJumpType.Sourcing:location.href=$RGT_gotoURL_Sourcing(this._txt.value.toUpperCase())}break;case $R_ENUM$QuickJumpSection.Contact:switch(this._enmSelectedType){case $R_ENUM$QuickJumpType.Company:location.href=$RGT_gotoURL_CompanyBrowse($R_ENUM$CompanyListType.AllCompanies,this._txt.value);break;case $R_ENUM$QuickJumpType.Customer:location.href=$RGT_gotoURL_CompanyBrowse($R_ENUM$CompanyListType.Customers,this._txt.value);break;case $R_ENUM$QuickJumpType.Manufacturer:location.href=$RGT_gotoURL_ManufacturerBrowse(this._txt.value);break;case $R_ENUM$QuickJumpType.Supplier:location.href=$RGT_gotoURL_CompanyBrowse($R_ENUM$CompanyListType.Suppliers,this._txt.value);break;case $R_ENUM$QuickJumpType.Prospect:location.href=$RGT_gotoURL_CompanyBrowse($R_ENUM$CompanyListType.Prospects,this._txt.value);break;case $R_ENUM$QuickJumpType.Contact:location.href=$RGT_gotoURL_ContactBrowse(this._txt.value)}break;case $R_ENUM$QuickJumpSection.Accounts:switch(this._enmSelectedType){case $R_ENUM$QuickJumpType.ReceivedPurchaseOrder:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetPurchaseOrderID",Function.createDelegate(this,this.goPOComplete));break;case $R_ENUM$QuickJumpType.ReceivedCustomerRMA:if(!$R_FN.checkNumeric(this._txt)){this.showMessage(!0);return}this.doDataCall("GetCRMAID",Function.createDelegate(this,this.goCRMAComplete))}}}},showRelatedDocument:function(){var n="";switch(this._enmSelectedType){case $R_ENUM$QuickJumpType.CustomerRequirement:n="REQ";break;case $R_ENUM$QuickJumpType.HUBRFQ:n="BOM";break;case $R_ENUM$QuickJumpType.Quote:n="Q";break;case $R_ENUM$QuickJumpType.SalesOrder:n="SO";break;case $R_ENUM$QuickJumpType.Invoice:n="INV";break;case $R_ENUM$QuickJumpType.PurchaseOrder:n="PO";break;case $R_ENUM$QuickJumpType.GoodsIn:n="GI";break;case $R_ENUM$QuickJumpType.Stock:n="STK";break;case $R_ENUM$QuickJumpType.CustomerRMA:n="CRMA";break;case $R_ENUM$QuickJumpType.SupplierRMA:n="SRMA";break;case $R_ENUM$QuickJumpType.CreditNote:n="CRD";break;case $R_ENUM$QuickJumpType.DebitNote:n="DBT";break;case $R_ENUM$QuickJumpType.StockNumber:n="STK"}n!=""&&(this._txt.value.trim().length<1||(location.href="AllDocumentInformation.aspx?DocNo="+this._txt.value.trim()+"&ActionType="+n))},doDataCall:function(n,t){var i=new Rebound.GlobalTrader.Site.Data;i.set_PathToData("controls/LeftNuggets/QuickJump");i.set_DataObject("QuickJump");i.set_DataAction(n);i.addParameter("No",this._txt.value);i.addDataOK(t);i.addError(Function.createDelegate(this,this.getDataError));i.addTimeout(Function.createDelegate(this,this.getDataError));i.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(i);$R_DQ.processQueue()},getSelectedItem:function(){for(var t,n=0;n<this._intMaxRadioButtons;n++)if(t=$get(String.format("{0}_{1}",this._rad.id,n)),t&&t.checked){this._enmSelectedType=Number.parseInvariant(t.value.toString());return}},getDataError:function(){this.showNotFound(!0)},goRequirementComplete:function(n){this.checkResult(n._result)&&(location.href=$RGT_gotoURL_CustomerRequirement(this._intID))},goQuoteComplete:function(n){this.checkResult(n._result)&&(location.href=$RGT_gotoURL_Quote(this._intID))},goIPOComplete:function(n){this.checkResult(n._result)&&(location.href=$RGT_gotoURL_InternalPurchaseOrder(this._intID))},goHUBRFQComplete:function(n){this.checkResult(n._result)&&(location.href=$RGT_gotoURL_BOM(this._intID))},goPOComplete:function(n){if(this.checkResult(n._result)){var t="";this._enmSection==$R_ENUM$QuickJumpSection.Warehouse&&(n._result.QJSearchResult.length==1?t=$RGT_gotoURL_ReceivePurchaseOrder(this._intID):this.showTopIcons(n._result));this._enmSection==$R_ENUM$QuickJumpSection.Orders&&(n._result.QJSearchResult.length==1?t=$RGT_gotoURL_PurchaseOrder(this._intID):this.showTopIcons(n._result));this._enmSection==$R_ENUM$QuickJumpSection.Accounts&&(t=$RGT_gotoURL_ReceivedPurchaseOrder(this._intID));t&&(location.href=t);t=null}},goStockComplete:function(n){this.checkResult(n._result)&&(location.href=$RGT_gotoURL_Stock(this._intID))},goSOComplete:function(n){this.checkResult(n._result)&&(n._result.QJSearchResult.length==1?location.href=this._enmSection==$R_ENUM$QuickJumpSection.Warehouse?$RGT_gotoURL_ShipSalesOrder(this._intID):$RGT_gotoURL_SalesOrder(this._intID):this.showTopIcons(n._result))},goInvoiceComplete:function(n){this.checkResult(n._result)&&(n._result.QJSearchResult.length==1?location.href=$RGT_gotoURL_Invoice(this._intID):this.showTopIcons(n._result))},goCRMAComplete:function(n){if(this.checkResult(n._result)){var t="";this._enmSection==$R_ENUM$QuickJumpSection.Warehouse&&(n._result.QJSearchResult.length==1?t=$RGT_gotoURL_ReceiveCRMA(this._intID):this.showTopIcons(n._result));this._enmSection==$R_ENUM$QuickJumpSection.Orders&&(t=$RGT_gotoURL_CRMA(this._intID));this._enmSection==$R_ENUM$QuickJumpSection.Accounts&&(t=$RGT_gotoURL_ReceivedCRMA(this._intID));t&&(location.href=t);t=null}},goSRMAComplete:function(n){this.checkResult(n._result)&&(n._result.QJSearchResult.length==1?location.href=this._enmSection==$R_ENUM$QuickJumpSection.Warehouse?$RGT_gotoURL_ShipSRMA(this._intID):$RGT_gotoURL_SRMA(this._intID):this.showTopIcons(n._result))},goCreditComplete:function(n){this.checkResult(n._result)&&(location.href=$RGT_gotoURL_CreditNote(this._intID))},goDebitComplete:function(n){this.checkResult(n._result)&&(n._result.QJSearchResult.length==1?location.href=$RGT_gotoURL_DebitNote(this._intID):this.showTopIcons(n._result))},goGoodsInComplete:function(n){this.checkResult(n._result)&&(n._result.QJSearchResult.length==1?location.href=$RGT_gotoURL_GoodsIn(this._intID):this.showTopIcons(n._result))},goNPRGILineComplete:function(n){this.checkResult(n._result)&&$RGT_openNPRWindow(n._result.ID,n._result.NPRID)},showNotFound:function(n){$R_FN.showElement(this._pnlNotFound,n);$R_FN.setInnerHTML(this._pnlNotFound,$R_RES.Document)},showMessage:function(n){$R_FN.showElement(this._pnlNotFound,n);$R_FN.setInnerHTML(this._pnlNotFound,$R_RES.NumericFieldError)},getNPRIDLine:function(n){var t=n.split("-");if(t==null||t.length<=0||t.length>3)return this.showNotFound(!0),!1;this.doDataCall("GetNPRGILineID",Function.createDelegate(this,this.goNPRGILineComplete))},checkResult:function(n){var t=!1;return n&&(n.Error||(n.QJSearchResult!==undefined?n.QJSearchResult.length>0&&(t=!0,this._intID=n.QJSearchResult[0].ID):n.ID>0&&(t=!0,this._intID=n.ID))),this.showNotFound(!t),t},setPOResult:function(n){for(var u,t="<div class='topMenuRolloverLink'>",i="",f="",r=0;r<n.QJSearchResult.length;r++)u=n.QJSearchResult[r],f=$RGT_gotoURL_PurchaseOrder(u.ID),i=i+"<a href='"+f+"' >"+u.ItemDescription+"<\/a><br/>";t=t+i+"<\/div>";$R_FN.setInnerHTML(this._pnlQJSearchResult,"");$R_FN.setInnerHTML(this._pnlQJSearchResult,t)},setInvoiceResult:function(n){for(var u,t="<div class='topMenuRolloverLink'>",i="",f="",r=0;r<n.QJSearchResult.length;r++)u=n.QJSearchResult[r],f=$RGT_gotoURL_Invoice(u.ID),i=i+"<a href='"+f+"' >"+u.ItemDescription+"<\/a><br/>";t=t+i+"<\/div>";$R_FN.setInnerHTML(this._pnlQJSearchResult,"");$R_FN.setInnerHTML(this._pnlQJSearchResult,t)},setDebitNotesResult:function(n){for(var u,t="<div class='topMenuRolloverLink'>",i="",f="",r=0;r<n.QJSearchResult.length;r++)u=n.QJSearchResult[r],f=$RGT_gotoURL_DebitNote(u.ID),i=i+"<a href='"+f+"' >"+u.ItemDescription+"<\/a><br/>";t=t+i+"<\/div>";$R_FN.setInnerHTML(this._pnlQJSearchResult,"");$R_FN.setInnerHTML(this._pnlQJSearchResult,t)},setSalesOrderResult:function(n){for(var t,i="<div class='topMenuRolloverLink'>",r="",f="",u=0;u<n.QJSearchResult.length;u++)t=n.QJSearchResult[u],f=this._enmSection==$R_ENUM$QuickJumpSection.Warehouse?$RGT_gotoURL_ShipSalesOrder(t.ID):$RGT_gotoURL_SalesOrder(t.ID),r=r+"<a href='"+f+"' >"+t.ItemDescription+"<\/a><br/>";i=i+r+"<\/div>";$R_FN.setInnerHTML(this._pnlQJSearchResult,"");$R_FN.setInnerHTML(this._pnlQJSearchResult,i)},setRecieveCRMAResult:function(n){for(var u,t="<div class='topMenuRolloverLink'>",i="",f="",r=0;r<n.QJSearchResult.length;r++)u=n.QJSearchResult[r],f=$RGT_gotoURL_ReceiveCRMA(u.ID),i=i+"<a href='"+f+"' >"+u.ItemDescription+"<\/a><br/>";t=t+i+"<\/div>";$R_FN.setInnerHTML(this._pnlQJSearchResult,"");$R_FN.setInnerHTML(this._pnlQJSearchResult,t)},setShipSRMAResult:function(n){for(var t,i="<div class='topMenuRolloverLink'>",r="",f="",u=0;u<n.QJSearchResult.length;u++)t=n.QJSearchResult[u],f=this._enmSection==$R_ENUM$QuickJumpSection.Warehouse?$RGT_gotoURL_ShipSRMA(t.ID):$RGT_gotoURL_SRMA(t.ID),r=r+"<a href='"+f+"' >"+t.ItemDescription+"<\/a><br/>";i=i+r+"<\/div>";$R_FN.setInnerHTML(this._pnlQJSearchResult,"");$R_FN.setInnerHTML(this._pnlQJSearchResult,i)},setGoodsInResult:function(n){for(var u,t="<div class='topMenuRolloverLink'>",i="",f="",r=0;r<n.QJSearchResult.length;r++)u=n.QJSearchResult[r],f=$RGT_gotoURL_GoodsIn(u.ID),i=i+"<a href='"+f+"' >"+u.ItemDescription+"<\/a><br/>";t=t+i+"<\/div>";$R_FN.setInnerHTML(this._pnlQJSearchResult,"");$R_FN.setInnerHTML(this._pnlQJSearchResult,t)},setRecievePOResult:function(n){for(var u,t="<div class='topMenuRolloverLink'>",i="",f="",r=0;r<n.QJSearchResult.length;r++)u=n.QJSearchResult[r],f=$RGT_gotoURL_ReceivePurchaseOrder(u.ID),i=i+"<a href='"+f+"' >"+u.ItemDescription+"<\/a><br/>";t=t+i+"<\/div>";$R_FN.setInnerHTML(this._pnlQJSearchResult,"");$R_FN.setInnerHTML(this._pnlQJSearchResult,t)},showTopIcons:function(n){this._enmSection==$R_ENUM$QuickJumpSection.Orders?this._enmSelectedType==$R_ENUM$QuickJumpType.PurchaseOrder?this.setPOResult(n):this._enmSelectedType==$R_ENUM$QuickJumpType.Invoice?this.setInvoiceResult(n):this._enmSelectedType==$R_ENUM$QuickJumpType.DebitNote?this.setDebitNotesResult(n):this._enmSelectedType==$R_ENUM$QuickJumpType.SalesOrder&&this.setSalesOrderResult(n):this._enmSection==$R_ENUM$QuickJumpSection.Warehouse&&(this._enmSelectedType==$R_ENUM$QuickJumpType.ReceivePurchaseOrder?this.setRecievePOResult(n):this._enmSelectedType==$R_ENUM$QuickJumpType.ShipSalesOrder?this.setSalesOrderResult(n):this._enmSelectedType==$R_ENUM$QuickJumpType.ReceiveCustomerRMA?this.setRecieveCRMAResult(n):this._enmSelectedType==$R_ENUM$QuickJumpType.ShipSupplierRMA?this.setShipSRMAResult(n):this._enmSelectedType==$R_ENUM$QuickJumpType.GoodsIn&&this.setGoodsInResult(n));clearTimeout(this._intTimeout);$R_FN.showElement(this._pnlQJSearchResultTooltip,!0);this.setToolTipLocation()},setToolTipLocation:function(){this._pnlNPRTootTip&&(this._ibtnNPRLabel&&(this._pnlNPRTootTip.style.top=String.format("{0}px",Sys.UI.DomElement.getBounds(this._ibtnNPRLabel).y-Sys.UI.DomElement.getBounds(this._pnlNPRTootTip).y+15)),this._ibtnNPRLabel&&(this._pnlNPRTootTip.style.left=String.format("{0}px",Sys.UI.DomElement.getBounds(this._ibtnNPRLabel).x-Sys.UI.DomElement.getBounds(this._pnlNPRTootTip).x)))}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.QuickJump",Rebound.GlobalTrader.Site.Controls.LeftNuggets.Base);