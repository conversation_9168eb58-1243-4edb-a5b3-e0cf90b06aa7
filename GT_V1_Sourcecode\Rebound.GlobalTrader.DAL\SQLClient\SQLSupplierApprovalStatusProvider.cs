﻿/*
Marker     Changed by               Date         Remarks
[001]      <PERSON><PERSON><PERSON><PERSON>           29/07/2021   Implement a new dropdown for supplier Approval Status
*/
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlSupplierApprovalStatusProvider : SupplierApprovalStatusProvider
    {
        public override List<SupplierApprovalStatusDetails> DropDown()
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_SupplierApprovalStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<SupplierApprovalStatusDetails> lst = new List<SupplierApprovalStatusDetails>();
                while (reader.Read())
                {
                    SupplierApprovalStatusDetails obj = new SupplierApprovalStatusDetails();
                    obj.SupplierApprovalStatusId = GetReaderValue_Int32(reader, "SupplierApprovalStatusId", 0);
                    obj.Name = GetReaderValue_String(reader, "Name", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get PurchaseOrderStatuss", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
    }
}
