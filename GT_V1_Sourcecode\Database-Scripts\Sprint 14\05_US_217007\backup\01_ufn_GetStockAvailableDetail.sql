﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
--===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-213481]     Phuc Hoang		 06-Oct-2024		UPDATE		Show Stock Alert on HUBRFQ page as Requirement page
=============================================================================================  
*/
CREATE OR ALTER FUNCTION [dbo].[ufn_GetStockAvailableDetail] (
	@PartNo NVARCHAR(50) = NULL
	,@ClientNo INT = 0
	,@stockid INT = 0
	)
RETURNS NVARCHAR(400)
AS
BEGIN
	DECLARE @StockAvailableMessage NVARCHAR(400)
	DECLARE @QuantityInStock NVARCHAR(100)
	DECLARE @QuantityOnOrder NVARCHAR(100)
	DECLARE @QuantityAllocated NVARCHAR(100)
	DECLARE @QuantityAvailable NVARCHAR(100)
	DECLARE @stockNo NVARCHAR(100)

	SET @StockAvailableMessage = ''

	IF (@StockAvailableMessage IS NOT NULL)
	BEGIN
		IF (@stockid != 0)
		BEGIN
			IF EXISTS (
					SELECT TOP 1 QuantityInStock
						,QuantityOnOrder
						,QuantityAllocated
						,QuantityAvailable
					FROM vwStock
					WHERE stockid IN (
							SELECT stockid
							FROM tbStock
							WHERE stockid = @stockid
							)
					ORDER BY dlup DESC
					)
			BEGIN
				SELECT TOP 1 @stockNo = stockid
					,@QuantityInStock = QuantityInStock
					,@QuantityOnOrder = QuantityOnOrder
					,@QuantityAllocated = QuantityAllocated
					,@QuantityAvailable = QuantityAvailable
				FROM vwStock
				WHERE stockid IN (
						SELECT stockid
						FROM tbStock
						WHERE stockid = @stockid
						)
				ORDER BY dlup DESC
			END
		END
		ELSE
		BEGIN
			IF EXISTS (
					SELECT TOP 1 QuantityInStock
						,QuantityOnOrder
						,QuantityAllocated
						,QuantityAvailable
					FROM vwStock
					WHERE Part = @PartNo
					ORDER BY dlup DESC
					)
			BEGIN
				SELECT TOP 1 @stockNo = stockid
				FROM vwStock
				WHERE Part = @PartNo
					AND QuantityAvailable > 0
					AND ClientNo = @ClientNo
				ORDER BY dlup DESC

				SELECT @QuantityInStock = SUM(QuantityInStock)
					,@QuantityOnOrder = SUM(QuantityOnOrder)
					,@QuantityAllocated = SUM(QuantityAllocated)
					,@QuantityAvailable = SUM(QuantityAvailable)
				FROM vwStock
				WHERE Part = @PartNo
			END
		END

		--SET @StockAvailableMessage = @StockAvailableMessage +  @QuantityInStock +' In Stock -'+ @QuantityOnOrder +' On Order -'+ @QuantityAllocated +' Allocated -'+ @QuantityAvailable +' Available -' + @stockNo            
		IF (@QuantityAvailable > 0)
		BEGIN
			SET @StockAvailableMessage = @StockAvailableMessage + ' In Stock : ' + @QuantityInStock + ' -' + ' On Order : ' + @QuantityOnOrder + '-' + ' Allocated : ' + @QuantityAllocated + '-' + ' Available : ' + @QuantityAvailable + '-' + ISNULL(@stockNo, 0)
		END
	END

	RETURN @StockAvailableMessage
END
GO


