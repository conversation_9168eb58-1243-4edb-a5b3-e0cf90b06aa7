using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch {
	[DefaultProperty("")]
	[ToolboxData("<{0}:DesignBase runat=server></{0}:DesignBase>")]
	public class DesignBase : WebControl, INamingContainer {

		#region Locals

		public Panel pnlLoading;
		public Panel pnlError;
		public Label lblError;
		public Panel pnlContent;
		public Panel pnlNoneFound;
		public FlexiDataTable tblResults;
		public PagingButtons ctlPagingButtons;
		public IconButton ibtnSearch;
		public Tables.Base tblOuter;
		public List<string> lstFieldIDs = new List<string>();
		public ConcurrentDictionary<string, string> dctFieldIDs = new ConcurrentDictionary<string, string>();

		#endregion

		#region Properties

		/// <summary>
		/// Left Fields
		/// </summary>
		private ITemplate _tmpFieldsLeft = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		[TemplateInstance(TemplateInstance.Single)]
		public ITemplate FieldsLeft {
			get { return _tmpFieldsLeft; }
			set { _tmpFieldsLeft = value; }
		}

		/// <summary>
		/// Right Fields
		/// </summary>
		private ITemplate _tmpFieldsRight = null;
		[PersistenceMode(PersistenceMode.InnerProperty)]
		[TemplateContainer(typeof(Container))]
		[TemplateInstance(TemplateInstance.Single)]
		public ITemplate FieldsRight {
			get { return _tmpFieldsRight; }
			set { _tmpFieldsRight = value; }
		}

		/// <summary>
		/// Should paging controls be shown?
		/// </summary>
		private bool _blnShowPaging = true;
		public bool ShowPaging {
			get { return _blnShowPaging; }
			set { _blnShowPaging = value; }
		}

		private SortColumnDirection _enmInitialSortDirection = SortColumnDirection.ASC;
		public SortColumnDirection InitialSortDirection {
			get { return _enmInitialSortDirection; }
			set { _enmInitialSortDirection = value; }
		}


		#endregion

		#region Overrides

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {

			//outer table
			tblOuter = new Tables.Base();
			tblOuter.ID = "tblOuter";
			tblOuter.CssClass = "itemSearchFilters";
			TableRow trOuter = new TableRow();
			tblOuter.Rows.Add(trOuter);
			TableCell tdLeft = new TableCell();
			tdLeft.CssClass = "col1";
			trOuter.Cells.Add(tdLeft);
			TableCell tdRight = new TableCell();
			tdRight.CssClass = "col2";
			trOuter.Cells.Add(tdRight);
			Controls.Add(tblOuter);

			//Fields
			if (_tmpFieldsLeft != null) AddFilters(_tmpFieldsLeft, tdLeft);
			if (_tmpFieldsRight != null) AddFilters(_tmpFieldsRight, tdRight);

			//search button
			Panel pnlGoButton = ControlBuilders.CreatePanel("itemSearchGo");
			ibtnSearch = new IconButton();
			ibtnSearch.IconGroup = IconButton.IconGroupList.FormBody;
			ibtnSearch.IconTitleResource = "Search";
			ibtnSearch.IconButtonMode = IconButton.IconButtonModeList.Hyperlink;
			ibtnSearch.Href = "javascript:void(0);";
			pnlGoButton.Controls.Add(ibtnSearch);
			Controls.Add(pnlGoButton);

			//loading message
			pnlLoading = ControlBuilders.CreatePanelInsideParent(this, "itemSearchLoading");
			ControlBuilders.CreateLiteralInsideParent(pnlLoading, Functions.GetGlobalResource("Misc", "Loading"));
			Functions.SetCSSVisibility(pnlLoading, false);

			//error message
			pnlError = ControlBuilders.CreatePanelInsideParent(this, "itemSearchError");
			lblError = ControlBuilders.CreateLabelInsideParent(pnlError);
			Functions.SetCSSVisibility(pnlError, false);

			//none found panel
			pnlNoneFound = ControlBuilders.CreatePanelInsideParent(this, "itemSearchNoneFound");
			ControlBuilders.CreateLiteralInsideParent(pnlNoneFound, Functions.GetGlobalResource("NotFound", "Generic"));
			Functions.SetCSSVisibility(pnlNoneFound, false);

			//content panel
			pnlContent = ControlBuilders.CreatePanelInsideParent(this, "itemSearch");
			Functions.SetCSSVisibility(pnlContent, false);
			if (_blnShowPaging) {
				ctlPagingButtons = new PagingButtons();
				ctlPagingButtons.ShowFilterButton = false;
				pnlContent.Controls.Add(ctlPagingButtons);
			}
			tblResults = new FlexiDataTable();
			tblResults.AllowSelection = true;
			pnlContent.Controls.Add(tblResults);

			base.CreateChildControls();
		}

		#endregion

		private void AddFilters(ITemplate tmpTemplate, TableCell td) {
			Tables.ItemSearchForm tbl = new Tables.ItemSearchForm();
			td.Controls.Add(tbl);
			Container cnt = new Container();
			tmpTemplate.InstantiateIn(cnt);
			for (int i = 0; i < cnt.Controls.Count; i++) {
				Control ctl = cnt.Controls[i];
				if (ctl is FilterDataItemRows.Base) {
					tbl.Rows.Add((TableRow)ctl);
					if (!dctFieldIDs.ContainsKey(ctl.ID)) dctFieldIDs.TryAdd(ctl.ID, (ctl.ClientID));
					if (!lstFieldIDs.Contains(ctl.ClientID)) lstFieldIDs.Add(ctl.ClientID);
				}
			}
			cnt.Dispose(); cnt = null;
		}

		internal Control FindFieldControl(string strField) {
			EnsureChildControls();
			return Functions.FindControlRecursive(tblOuter, strField);
		}

		public void MakeChildControls() {
			EnsureChildControls();
		}
	}
}