Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.initializeBase(this,[n]);this._intGoodsInLineNo=-1;this._intInvoiceLineNo=-1};Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.prototype={addPotentialStatusChange:function(n){this.get_events().addHandler("PotentialStatusChange",n)},removePotentialStatusChange:function(n){this.get_events().removeHand<PERSON>("PotentialStatusChange",n)},onPotentialStatusChange:function(){var n=this.get_events().getHandler("PotentialStatusChange");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete));this.addSearched(Function.createDelegate(this,this.doSearched))},dispose:function(){this.isDisposed||(this._intGoodsInLineNo=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.callBaseMethod(this,"dispose"))},refereshGroup:function(){$find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl")._intGoodsInLineNo=this._intGoodsInLineNo;$find("ctl00_cphMain_ctlShippingLines_ctlDB_ctl14_frmShip_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl").getData()},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/GISerialNumber");this._objData.set_DataObject("GISerialNumber");this._objData.set_DataAction("GetData");this._objData.addParameter("Group",this.getFieldValue("ctlGroup"));this._objData.addParameter("Serial",this.getFieldValue("ctlSerialNo"));this._objData.addParameter("GoodsInLineNo",this._intGoodsInLineNo);this._objData.addParameter("InvoiceLineNo",this._intInvoiceLineNo)},getGroupValue:function(){return this.getFieldValue("ctlGroup")},doGetDataComplete:function(){for(var t=0,i=this._objResult.Results.length;t<i;t++){var n=this._objResult.Results[t],r=[$R_FN.setCleanTextValue(n.Group),n.SerialNo],u={SerialNoId:n.ID,SubGroup:n.Group,SerialNo:n.SerialNo};this._tblResults.addRow(r,n.ID,!1,u);r=null;n=null}},doSearched:function(){this.onPotentialStatusChange()},refereshCRMAGroup:function(){$find("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl")._intInvoiceLineNo=this._intInvoiceLineNo;$find("ctl00_cphMain_ctlReceivingLines_ctlDB_ctl14_frmReceive_ctlDB_ctlGiSerialNumber_ctlDB_ctlGroup_ddl").getData()}};Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.GISerialNumber",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);