/*
Marker     Changed by      Date         Remarks
[001]      Vinay           21/01/2014   CR:- Add AS9120 Requirement in GT application
*/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ProductCategory : Rebound.GlobalTrader.Site.Data.DropDowns.Base
    {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("ProductCategory");
            base.ProcessRequest(context);
        }

        protected override void GetData()
        {
            //string strCacheOptions = CacheManager.SerializeOptions(new object[] { SessionManager.Culture });
            //string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strCacheOptions);
            //if (string.IsNullOrEmpty(strCachedData))
            //{
            JsonObject jsn = new JsonObject();
            JsonObject jsnList = new JsonObject(true);
            List<BLL.ProductCategory> lst = BLL.ProductCategory.DropDown();
            for (int i = 0; i < lst.Count; i++)
            {
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].ProductCategoryId);
                //jsnItem.AddVariable("Name",Functions.GetGlobalResource("Misc", lst[i].Name));
                jsnItem.AddVariable("Name", Functions.ReplaceLineBreaks(lst[i].Name));
                jsnList.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst.Clear(); lst = null;
            jsn.AddVariable("Types", jsnList);
            jsnList.Dispose(); jsnList = null;
            //CacheManager.StoreDropDown(_objDropDown.ID, strCacheOptions, jsn.Result, CacheManager.CacheExpiryType.OneHour);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
            //}
            //else
            //{
            //    _context.Response.Write(strCachedData);
            //}
            //strCachedData = null;
        }
    }


}
