<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="Profile_MailMessageGroups" xml:space="preserve">
    <value>Mail Message Groups</value>
  </data>
  <data name="Profile_MailMessages" xml:space="preserve">
    <value>Mail Messages</value>
  </data>
  <data name="Profile_ToDo" xml:space="preserve">
    <value>To Do List</value>
  </data>
  <data name="Contact" xml:space="preserve">
    <value>Contact</value>
  </data>
  <data name="Contact_CompanyAdd" xml:space="preserve">
    <value>Add New Company</value>
  </data>
  <data name="Contact_GroupCodeCompanyAdd" xml:space="preserve">
    <value>Add New Group Code</value>
  </data>
  <data name="Contact_CompanyBrowse" xml:space="preserve">
    <value>Browse Companies</value>
  </data>
  <data name="Contact_CompanyDetail" xml:space="preserve">
    <value>Company Detail</value>
  </data>
  <data name="Contact_ContactDetail" xml:space="preserve">
    <value>Contacts</value>
  </data>
  <data name="Contact_ManufacturerAdd" xml:space="preserve">
    <value>Add New Manufacturer</value>
  </data>
  <data name="Contact_ManufacturerBrowse" xml:space="preserve">
    <value>Browse Manufacturers</value>
  </data>
  <data name="Contact_ManufacturerDetail" xml:space="preserve">
    <value>Manufacturer Detail</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Feedback" xml:space="preserve">
    <value>Feedback</value>
  </data>
  <data name="Help" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>Page Not Found</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Orders</value>
  </data>
  <data name="Orders_CreditNoteAdd" xml:space="preserve">
    <value>Add New Credit Note</value>
  </data>
  <data name="Orders_CreditNoteBrowse" xml:space="preserve">
    <value>Credit Notes</value>
  </data>
  <data name="Orders_CustomerRequirementAdd" xml:space="preserve">
    <value>Add New Requirement </value>
  </data>
  <data name="Orders_CustomerRequirementBrowse" xml:space="preserve">
    <value>Customer Requirements</value>
  </data>
  <data name="Orders_CustomerRMAAdd" xml:space="preserve">
    <value>Add New Customer RMA</value>
  </data>
  <data name="Orders_CustomerRMABrowse" xml:space="preserve">
    <value>Customer RMAs</value>
  </data>
  <data name="Orders_DebitNoteAdd" xml:space="preserve">
    <value>Add New Debit Note</value>
  </data>
  <data name="Orders_DebitNoteBrowse" xml:space="preserve">
    <value>Debit Notes</value>
  </data>
  <data name="Orders_InvoiceAdd" xml:space="preserve">
    <value>Add New Invoice</value>
  </data>
  <data name="Orders_InvoiceBrowse" xml:space="preserve">
    <value>Invoices</value>
  </data>
  <data name="Orders_PurchaseOrderAdd" xml:space="preserve">
    <value>Add New Purchase Order</value>
  </data>
  <data name="Orders_PurchaseOrderBrowse" xml:space="preserve">
    <value>Purchase Orders</value>
  </data>
  <data name="Orders_PurchaseRequisitionBrowse" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="Orders_QuoteAdd" xml:space="preserve">
    <value>Add New Quote</value>
  </data>
  <data name="Orders_QuoteBrowse" xml:space="preserve">
    <value>Quotes</value>
  </data>
  <data name="Orders_SalesOrderAdd" xml:space="preserve">
    <value>Add New Sales Order</value>
  </data>
  <data name="Orders_SalesOrderBrowse" xml:space="preserve">
    <value>Sales Orders</value>
  </data>
  <data name="Orders_Sourcing" xml:space="preserve">
    <value>Sourcing</value>
  </data>
  <data name="Orders_SupplierRMAAdd" xml:space="preserve">
    <value>Add New Supplier RMA</value>
  </data>
  <data name="Orders_SupplierRMABrowse" xml:space="preserve">
    <value>Supplier RMAs</value>
  </data>
  <data name="Reports" xml:space="preserve">
    <value>Reports</value>
  </data>
  <data name="Reports_ReportDetail" xml:space="preserve">
    <value>Report</value>
  </data>
  <data name="Setup" xml:space="preserve">
    <value>Setup</value>
  </data>
  <data name="Setup_CompanyDetails_Country" xml:space="preserve">
    <value>Countries</value>
  </data>
  <data name="Setup_CompanyDetails_Currency" xml:space="preserve">
    <value>Currencies</value>
  </data>
  <data name="Setup_CompanyDetails_Division" xml:space="preserve">
    <value>Divisions</value>
  </data>
  <data name="Setup_CompanyDetails_Product" xml:space="preserve">
    <value>Products</value>
  </data>
  <data name="Setup_CompanyDetails_SequenceNumber" xml:space="preserve">
    <value>Sequence Numbers</value>
  </data>
  <data name="Setup_CompanyDetails_ShippingMethod" xml:space="preserve">
    <value>Shipping Methods</value>
  </data>
  <data name="Setup_CompanyDetails_SourcingLinks" xml:space="preserve">
    <value>Sourcing Links</value>
  </data>
  <data name="Setup_CompanyDetails_StockLogReason" xml:space="preserve">
    <value>Stock Log Reasons</value>
  </data>
  <data name="Setup_CompanyDetails_Tax" xml:space="preserve">
    <value>Taxes</value>
  </data>
  <data name="Setup_CompanyDetails_Team" xml:space="preserve">
    <value>Teams</value>
  </data>
  <data name="Setup_CompanyDetails_Terms" xml:space="preserve">
    <value>Terms</value>
  </data>
  <data name="Setup_CompanyDetails_User" xml:space="preserve">
    <value>Users</value>
  </data>
  <data name="Setup_CompanyDetails_Warehouse" xml:space="preserve">
    <value>Warehouses</value>
  </data>
  <data name="Setup_GlobalSettings_CommunicationLogType" xml:space="preserve">
    <value>Communication Log Types</value>
  </data>
  <data name="Setup_GlobalSettings_CompanyType" xml:space="preserve">
    <value>Company Types</value>
  </data>
  <data name="Setup_GlobalSettings_IndustryType" xml:space="preserve">
    <value>Industry Types</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCountryList" xml:space="preserve">
    <value>Master Country List</value>
  </data>
  <data name="Setup_GlobalSettings_MasterCurrencyList" xml:space="preserve">
    <value>Master Currency List</value>
  </data>
  <data name="Setup_GlobalSettings_Package" xml:space="preserve">
    <value>Packages</value>
  </data>
  <data name="Setup_GlobalSettings_ProductType" xml:space="preserve">
    <value>Product Types</value>
  </data>
  <data name="Setup_GlobalSettings_Reason" xml:space="preserve">
    <value>Close Reasons</value>
  </data>
  <data name="Setup_GlobalSettings_Salutation" xml:space="preserve">
    <value>Salutations</value>
  </data>
  <data name="Setup_Personal_UserProfile" xml:space="preserve">
    <value>My Profile</value>
  </data>
  <data name="Setup_Security_Groups" xml:space="preserve">
    <value>Security Groups</value>
  </data>
  <data name="Setup_Security_Users" xml:space="preserve">
    <value>Security Users</value>
  </data>
  <data name="Warehouse" xml:space="preserve">
    <value>Warehouse</value>
  </data>
  <data name="Warehouse_GoodsInBrowse" xml:space="preserve">
    <value>Goods In</value>
  </data>
  <data name="Warehouse_LotsAdd" xml:space="preserve">
    <value>Add New Lot</value>
  </data>
  <data name="Warehouse_LotsBrowse" xml:space="preserve">
    <value>Lots</value>
  </data>
  <data name="Warehouse_LotsDetail" xml:space="preserve">
    <value>Lot</value>
  </data>
  <data name="Warehouse_ReceiveCustomerRMABrowse" xml:space="preserve">
    <value>Receive Customer RMAs</value>
  </data>
  <data name="Warehouse_ReceiveCustomerRMADetail" xml:space="preserve">
    <value>Receive Customer RMA</value>
  </data>
  <data name="Warehouse_ReceivePurchaseOrderBrowse" xml:space="preserve">
    <value>Receive Purchase Orders</value>
  </data>
  <data name="Warehouse_ReceivePurchaseOrderDetail" xml:space="preserve">
    <value>Receive Purchase Order</value>
  </data>
  <data name="Warehouse_ServicesAdd" xml:space="preserve">
    <value>Add New Service</value>
  </data>
  <data name="Warehouse_ServicesBrowse" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="Warehouse_ServicesDetail" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="Warehouse_ShipSalesOrderBrowse" xml:space="preserve">
    <value>Ship Sales Orders</value>
  </data>
  <data name="Warehouse_ShipSalesOrderDetail" xml:space="preserve">
    <value>Ship Sales Order</value>
  </data>
  <data name="Warehouse_ShipSupplierRMABrowse" xml:space="preserve">
    <value>Ship Supplier RMAs</value>
  </data>
  <data name="Warehouse_ShipSupplierRMADetail" xml:space="preserve">
    <value>Ship Supplier RMA</value>
  </data>
  <data name="Warehouse_StockAdd" xml:space="preserve">
    <value>Add New Stock Item</value>
  </data>
  <data name="Warehouse_StockBrowse" xml:space="preserve">
    <value>Stock</value>
  </data>
  <data name="Warehouse_StockDetail" xml:space="preserve">
    <value>Stock Item</value>
  </data>
  <data name="Warehouse_GoodsInAdd" xml:space="preserve">
    <value>Add New Goods In Note</value>
  </data>
  <data name="Setup_CompanyDetails_MailMessageGroups" xml:space="preserve">
    <value>Mail Groups</value>
  </data>
  <data name="Setup_Personal_MailMessageGroups" xml:space="preserve">
    <value>Mail Groups</value>
  </data>
  <data name="Setup_CompanyDetails_MailGroups" xml:space="preserve">
    <value>Mail Groups</value>
  </data>
  <data name="Setup_CompanyDetails_PrintedDocuments" xml:space="preserve">
    <value>Printed Documents</value>
  </data>
  <data name="Accounts" xml:space="preserve">
    <value>Accounts</value>
  </data>
  <data name="Profile_Edit" xml:space="preserve">
    <value>Edit My Profile</value>
  </data>
  <data name="Setup_GlobalSettings_CountingMethod" xml:space="preserve">
    <value>Counting Methods</value>
  </data>
  <data name="Accounts_ReceivedCustomerRMABrowse" xml:space="preserve">
    <value>Received Customer RMAs</value>
  </data>
  <data name="Accounts_ReceivedCustomerRMADetail" xml:space="preserve">
    <value>Received Customer RMA</value>
  </data>
  <data name="Accounts_ReceivedPurchaseOrderBrowse" xml:space="preserve">
    <value>Received Purchase Orders</value>
  </data>
  <data name="Accounts_ReceivedPurchaseOrderDetail" xml:space="preserve">
    <value>Received Purchase Order</value>
  </data>
  <data name="Orders_InvoiceBulkPrint" xml:space="preserve">
    <value>Bulk Print / Bulk Email</value>
  </data>
  <data name="Orders_CustomerRequirementDetail" xml:space="preserve">
    <value>Customer Requirement</value>
  </data>
  <data name="Setup_GlobalSettings_ApplicationSettings" xml:space="preserve">
    <value>Application Settings</value>
  </data>
  <data name="Setup_CompanyDetails_ApplicationSettings" xml:space="preserve">
    <value>Application Settings</value>
  </data>
  <data name="Setup_GlobalSettings_Incoterm" xml:space="preserve">
    <value>Incoterms</value>
  </data>
  <data name="Setup_GlobalSettings_AS6081" xml:space="preserve">
    <value>AS6081</value>
  </data>
  <data name="OverallSetup_AddCompany" xml:space="preserve">
    <value>Setup | Add Company</value>
  </data>
  <data name="OverallSetup_AppSettings" xml:space="preserve">
    <value>Setup | Application Settings</value>
  </data>
  <data name="OverallSetup_DatabaseSettings" xml:space="preserve">
    <value>Setup | Database Settings</value>
  </data>
  <data name="OverallSetup_DisableCompany" xml:space="preserve">
    <value>Setup | Disable Company</value>
  </data>
  <data name="OverallSetup_EmailSettings" xml:space="preserve">
    <value>Setup | Email Settings</value>
  </data>
  <data name="OverallSetup_Home" xml:space="preserve">
    <value>Setup</value>
  </data>
  <data name="OverallSetup_InitialSetup" xml:space="preserve">
    <value>Initial Setup</value>
  </data>
  <data name="OverallSetup_Sessions" xml:space="preserve">
    <value>Setup | Sessions</value>
  </data>
  <data name="Orders_CustomerRequirement_DetailAdd" xml:space="preserve">
    <value>Add New Requirement To This Company</value>
  </data>
  <data name="Setup_CompanyDetails_EmailComposer" xml:space="preserve">
    <value>Bulk Invoice Email Composer</value>
  </data>
  <data name="EmailStatus" xml:space="preserve">
    <value>Invoice Email Status</value>
  </data>
  <data name="Setup_CompanyDetails_LocalCurrency" xml:space="preserve">
    <value>Local Currency</value>
  </data>
  <data name="Warehouse_SupplierInvoiceBrowse" xml:space="preserve">
    <value>Supplier Invoice</value>
  </data>
  <data name="Warehouse_SupplierInvoiceAdd" xml:space="preserve">
    <value>Add New Supplier Invoice</value>
  </data>
  <data name="SalesCalc" xml:space="preserve">
    <value>Sales Calculator</value>
  </data>
  <data name="ReportNPR" xml:space="preserve">
    <value>Rebound Global: Trader - Report NPR</value>
  </data>
  <data name="Warehouse_NPRNotify" xml:space="preserve">
    <value>NPR Notify</value>
  </data>
  <data name="Setup_CompanyDetails_Printer" xml:space="preserve">
    <value>Printer</value>
  </data>
  <data name="Setup_GlobalSettings_Certificate" xml:space="preserve">
    <value>Certificate</value>
  </data>
  <data name="Setup_CompanyDetails_LabelPath" xml:space="preserve">
    <value>Nice Label Path</value>
  </data>
  <data name="Setup_GlobalSettings_EightDCode" xml:space="preserve">
    <value>Root Cause Code</value>
  </data>
  <data name="EightDCode_Add" xml:space="preserve">
    <value>Root Cause Code Add</value>
  </data>
  <data name="PurchaseOrder_EPRNotify" xml:space="preserve">
    <value>EPR Notify</value>
  </data>
  <data name="Warehouse_Npr" xml:space="preserve">
    <value>NPR</value>
  </data>
  <data name="Orders_CustomerReqPrint" xml:space="preserve">
    <value>Print Customer Requirements Enquiry Form</value>
  </data>
  <data name="Dashboards" xml:space="preserve">
    <value>Dashboards</value>
  </data>
  <data name="Dashboards_SO" xml:space="preserve">
    <value>Sales</value>
  </data>
  <data name="Dashboards_Stock" xml:space="preserve">
    <value>Sales Performance</value>
  </data>
  <data name="Orders_BOMAdd" xml:space="preserve">
    <value>Add New HUBRFQ</value>
  </data>
  <data name="Orders_BOMImport" xml:space="preserve">
    <value>BOM Import</value>
  </data>
  <data name="Orders_BOMBrowse" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="Orders_InternalPurchaseOrderBrowse" xml:space="preserve">
    <value>Internal Purchase Order</value>
  </data>
  <data name="Orders_InternalPurchaseOrderAdd" xml:space="preserve">
    <value>Add New Internal Purchase</value>
  </data>
  <data name="Orders_POQuoteBrowse" xml:space="preserve">
    <value>Price Request</value>
  </data>
  <data name="Orders_POQuoteAdd" xml:space="preserve">
    <value>Add New Price Request</value>
  </data>
  <data name="Orders_BOMDetail" xml:space="preserve">
    <value>HUBRFQ</value>
  </data>
  <data name="CSV_Import" xml:space="preserve">
    <value>Import CSV File</value>
  </data>
  <data name="Orders_ClientInvoiceBrowse" xml:space="preserve">
    <value>Client Invoices</value>
  </data>
  <data name="Setup_GlobalSettings_InvoiceSetting" xml:space="preserve">
    <value>Client Invoice</value>
  </data>
  <data name="Orders_ATMIPOBOM" xml:space="preserve">
    <value>Assign HUBRFQ</value>
  </data>
  <data name="Orders_BOMAddRequirement" xml:space="preserve">
    <value>Add New HUBRFQ To This Company</value>
  </data>
  <data name="Setup_CompanyDetails_GlobalTax" xml:space="preserve">
    <value>Master Taxes</value>
  </data>
  <data name="Orders_ClientInvoiceAdd" xml:space="preserve">
    <value>Add New Client Invoice</value>
  </data>
  <data name="Setup_GlobalSettings_Product" xml:space="preserve">
    <value>Products</value>
  </data>
  <data name="Setup_GlobalSecurity_Groups" xml:space="preserve">
    <value>Global Security Groups</value>
  </data>
  <data name="Setup_GlobalSettings_GTUpdate" xml:space="preserve">
    <value>GT Update Notifications</value>
  </data>
  <data name="Setup_GlobalSettings_SetupList" xml:space="preserve">
    <value>Master Status</value>
  </data>
  <data name="Orders_CustomerReqImport" xml:space="preserve">
    <value>BOM Import</value>
  </data>
  <data name="Orders_ClientBOMAdd" xml:space="preserve">
    <value>Add New BOM</value>
  </data>
  <data name="Orders_ClientBOMDetail" xml:space="preserve">
    <value>BOM</value>
  </data>
  <data name="ExchangeRate" xml:space="preserve">
    <value>Exchange Rate</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Product</value>
  </data>
  <data name="Orders_CrossMatchURL" xml:space="preserve">
    <value>Cross Match</value>
  </data>
  <data name="Setup_GlobalSettings_Client" xml:space="preserve">
    <value>Client</value>
  </data>
  <data name="Warehouse_IHSCatalogueBrowse" xml:space="preserve">
    <value>Product Catalogue</value>
  </data>
  <data name="Setup_GlobalSettings_MasterLogin" xml:space="preserve">
    <value>Master Login</value>
  </data>
  <data name="All_Document" xml:space="preserve">
    <value>All Document</value>
  </data>
  <data name="Utility" xml:space="preserve">
    <value>Utility</value>
  </data>
  <data name="Utility_BOM" xml:space="preserve">
    <value>BOM Import</value>
  </data>
  <data name="Utility_BOMImport" xml:space="preserve">
    <value>BOM Import</value>
  </data>
  <data name="Utility_Offer" xml:space="preserve">
    <value>Offer Import</value>
  </data>
  <data name="Utility_OfferImport" xml:space="preserve">
    <value>Offer Import</value>
  </data>
  <data name="Utility_XMatch" xml:space="preserve">
    <value>XMatch Utility</value>
  </data>
  <data name="Utility_HUBOffer" xml:space="preserve">
    <value>Strategic Offers Import Tool</value>
  </data>
  <data name="Utility_HUBOfferImport" xml:space="preserve">
    <value>Strategic Offers Import Tool</value>
  </data>
  <data name="All_IHSDocument" xml:space="preserve">
    <value>IHS PDF Document Attached</value>
  </data>
  <data name="Warehouse_ShortShipment" xml:space="preserve">
    <value>Short Shipment</value>
  </data>
  <data name="Warehouse_ShortShipmentNotify" xml:space="preserve">
    <value>Short Shipment Notify</value>
  </data>
  <data name="All_SupplierApprovalDoc" xml:space="preserve">
    <value>Supplier Approval PDF Document Attached</value>
  </data>
  <data name="All_SupplierApprovalImage" xml:space="preserve">
    <value>Supplier Approval Image Attached</value>
  </data>
  <data name="Warehouse_GILineNotify" xml:space="preserve">
    <value>Goods In Line Notify</value>
  </data>
  <data name="All_GILineDocument" xml:space="preserve">
    <value>GI Line Image</value>
  </data>
  <data name="Setup_CompanySettings_RestrictedManufacture" xml:space="preserve">
    <value>Restricted Manufacturer</value>
  </data>
  <data name="Setup_CompanyDetails_RestrictedManufacture" xml:space="preserve">
    <value>Restricted Manufacturer</value>
  </data>
  <data name="Setup_CompanyDetails_Warnings" xml:space="preserve">
    <value>Warnings</value>
  </data>
  <data name="ClientExchangeRate" xml:space="preserve">
    <value>Client Exchange Rate</value>
  </data>
  <data name="Warehouse_ShortShipmentDetails" xml:space="preserve">
    <value>Short Shipment Details</value>
  </data>
  <data name="Setup_CompanySettings_ECCN" xml:space="preserve">
    <value>ECCN</value>
  </data>
  <data name="PolishExchangeRate" xml:space="preserve">
    <value>Polish Exchange Rate</value>
  </data>
  <data name="KPI_DivisionDefault" xml:space="preserve">
    <value>Division KPI</value>
  </data>
  <data name="KPI_DivisionDetail" xml:space="preserve">
    <value>Division Detail</value>
  </data>
  <data name="KPI_DivisionEdit" xml:space="preserve">
    <value>Division Edit</value>
  </data>
  <data name="Utility_Stock" xml:space="preserve">
    <value>Stock Import</value>
  </data>
  <data name="Utility_StockImport" xml:space="preserve">
    <value>Stock Import</value>
  </data>
  <data name="Setup_GlobalSettings_ToDoListType" xml:space="preserve">
    <value>To Do List Type</value>
  </data>
  <data name="KPI_SalesDefault" xml:space="preserve">
    <value>Sales KPI</value>
  </data>
  <data name="KPI_SalesDetail" xml:space="preserve">
    <value>Sales Detail</value>
  </data>
  <data name="KPI_SalesEdit" xml:space="preserve">
    <value>Sales Edit</value>
  </data>
  <data name="KPI_TeamDefault" xml:space="preserve">
    <value>Team KPI</value>
  </data>
  <data name="KPI_TeamDetail" xml:space="preserve">
    <value>Team Detail</value>
  </data>
  <data name="KPI_TeamEdit" xml:space="preserve">
    <value>Team Edit</value>
  </data>
  <data name="GT" xml:space="preserve">
    <value>GT</value>
  </data>
  <data name="Power_BI_Sales" xml:space="preserve">
    <value>PowerBI Sales</value>
  </data>
  <data name="CSLsearch" xml:space="preserve">
    <value>CSL Search</value>
  </data>
  <data name="Utility_Log" xml:space="preserve">
    <value>Utility Log</value>
  </data>
  <data name="Utility_LogImport" xml:space="preserve">
    <value>Utility Log</value>
  </data>
  <data name="EUSanctionsMap" xml:space="preserve">
    <value>EU Sanctions Map</value>
  </data>
  <data name="TheUKSanctionsList" xml:space="preserve">
    <value>The UK Sanctions List</value>
  </data>
  <data name="Contact_ManufacturerGroup" xml:space="preserve">
    <value>Add in Manufacturer Group Code</value>
  </data>
  <data name="CSLSanctioned" xml:space="preserve">
    <value>CSL Sanctioned</value>
  </data>
  <data name="Orders_OGELLinesExportBrowse" xml:space="preserve">
    <value>OGEL Lines</value>
  </data>
  <data name="EndUserUndetakingPDF" xml:space="preserve">
    <value>End User Undertaking PDF Document</value>
  </data>
  <data name="Utility_ReverseLogisticsImport" xml:space="preserve">
    <value>Reverse Logistics Import Tool</value>
  </data>
  <data name="Utility_ReverseLogisticsOfferImport" xml:space="preserve">
    <value>Utility Reverse Logistics Offer Import</value>
  </data>
  <data name="Utility_RLImport" xml:space="preserve">
    <value>Reverse Logistics Import Tool</value>
  </data>
  <data name="BOMManager" xml:space="preserve">
    <value>BOM Manager</value>
  </data>
  <data name="BOMSearch" xml:space="preserve">
    <value>BOMSearch</value>
  </data>
  <data name="Utility_BOMManager" xml:space="preserve">
    <value>BOM Manager Import</value>
  </data>
  <data name="Utility_BOMManagerImport" xml:space="preserve">
    <value>BOM Manager Import</value>
  </data>
  <data name="Utility_PriceQuoteImport" xml:space="preserve">
    <value>Price Quote Import</value>
  </data>
  <data name="Setup_GlobalSettings_PDFDocumentFileSize" xml:space="preserve">
    <value>Document File Size</value>
  </data>
  <data name="Setup_GlobalSettings_EntertainmentType" xml:space="preserve">
    <value>EntertaimentType</value>
  </data>
  <data name="Power_BI_Saless" xml:space="preserve">
    <value>Sales Dashboard</value>
  </data>
  <data name="Setup_CompanyDetails_ClientInvoiceHeader" xml:space="preserve">
    <value>Client Invoice Header</value>
  </data>
  <data name="BOMSearchAssign" xml:space="preserve">
    <value>Assign BOM</value>
  </data>
  <data name="CompanyCertificatePDF" xml:space="preserve">
    <value>Certificate PDF Document</value>
  </data>
  <data name="AssignBOM" xml:space="preserve">
    <value>Assign BOM</value>
  </data>
  <data name="Utility_Alternative" xml:space="preserve">
    <value>Alternative Import</value>
  </data>
  <data name="Utility_AlternativeImport" xml:space="preserve">
    <value>Alternative Import</value>
  </data>
  <data name="Setup_GlobalSettings_PPVBOMQualification" xml:space="preserve">
    <value>PPV/ BOM Qualification</value>
  </data>
  <data name="Orders_CreditBulkPrint" xml:space="preserve">
    <value>Bulk Print / Bulk Email</value>
  </data>
  <data name="Ord_CreditBulkPrint" xml:space="preserve">
    <value>Bulk Print / Bulk Email</value>
  </data>
  <data name="Orders_DabitBulkPrint" xml:space="preserve">
    <value>Bulk Print / Bulk Email</value>
  </data>
  <data name="BOMManagerSourcing" xml:space="preserve">
    <value>BOM Manager Sourcing</value>
  </data>
  <data name="GermanyExchangeRate" xml:space="preserve">
    <value>Germany Exchange Rate</value>
  </data>
  <data name="Utility_ProspectiveOffer" xml:space="preserve">
    <value>Prospective Offers</value>
  </data>
  <data name="Utility_ProsOfferImport" xml:space="preserve">
    <value>Prospective Offers Import</value>
  </data>
  <data name="Utility_HUBOfferImportLarge" xml:space="preserve">
    <value>Bulk Offer Scheduled Import</value>
  </data>
  <data name="Orders_ProspectiveCrossSelling" xml:space="preserve">
    <value>Prospective Cross Selling</value>
  </data>
  <data name="Orders_ProsCrossSellingImport" xml:space="preserve">
    <value>Prospective Cross Selling Import</value>
  </data>
  <data name="Setup_CompanyDetails_OGELLicenses" xml:space="preserve">
    <value>OGEL Licenses</value>
  </data>
  <data name="Setup_GlobalSettings_StarRating" xml:space="preserve">
    <value>Star Rating</value>
  </data>>
  <data name="Warehouse_IHSCatalogueAdd" xml:space="preserve">
    <value>Add Part Information</value>
  </data>
</root>