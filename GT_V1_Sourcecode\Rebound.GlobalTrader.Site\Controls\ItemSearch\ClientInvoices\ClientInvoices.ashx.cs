/*
Marker     Changed by      Date         Remarks
[001]      Vinay           23/08/2012   Customize the invoice control for exported record, Set Exported=1
*/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data
{
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ClientInvoices : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetData": GetData(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }
        /// <summary>
        /// get an invoice by key
        /// </summary>
        private void GetData()
        {
            List<ClientInvoice> lst = null;
            try
            {
                int? ClientID = GetFormValue_NullableInt("ClientID");


                lst = ClientInvoice.ItemSearch(
                    ClientID,
                    GetFormValue_NullableInt("Order", 0),
                    GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
                    GetFormValue_NullableInt("PageIndex", 0),
                    GetFormValue_NullableInt("PageSize", 10),
                    //GetFormValue_String("Contact"),
                    //GetFormValue_StringForNameSearch("CMName"),
                    //GetFormValue_StringForNameSearchDecode("CMName"),
                    //GetFormValue_NullableInt("Salesman"),
                    GetFormValue_NullableInt("GoodsInNoLo"),
                    GetFormValue_NullableInt("GoodsInNoHi"),
                    //GetFormValue_Boolean("IncludePaid"),
                    GetFormValue_NullableInt("InvoiceNoLo"),
                    GetFormValue_NullableInt("InvoiceNoHi"),
                    //GetFormValue_NullableInt("SONoLo"),
                    //GetFormValue_NullableInt("SONoHi"),
                    GetFormValue_NullableDateTime("DateInvoicedFrom"),
                    GetFormValue_NullableDateTime("DateInvoicedTo"),
                      GetFormValue_NullableInt("ClientDebitNoLo"),
                    GetFormValue_NullableInt("ClientDebitNoHi")
                    //[001] code start
                    // Exported
                    //[001] code end
                    );
                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                for (int i = 0; i < lst.Count; i++)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].ClientInvoiceID);
                    jsnItem.AddVariable("No", lst[i].ClientInvoiceNumber);
                    jsnItem.AddVariable("CMNo", lst[i].CompanyNo);
                    jsnItem.AddVariable("CMName", lst[i].ClientCompanyName);
                    jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].ClientInvoiceDate));
                    jsnItem.AddVariable("GoodsInNo", lst[i].GoodsInNo);
                    jsnItem.AddVariable("Narrative", lst[i].Narrative);
                    jsnItem.AddVariable("SecondRef", lst[i].SecondRef);
                    jsnItem.AddVariable("SalesmanName", lst[i].SalesmanName);
                    jsnItem.AddVariable("SalesmanNo", lst[i].SalesmanNo);
                    jsnItem.AddVariable("InvLineNo", lst[i].ClientInvoiceLineNo);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose();
                    jsnItem = null;
                }
                jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
                jsn.AddVariable("Results", jsnItems);
                OutputResult(jsn);
                jsnItems.Dispose();
                jsnItems = null;
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception ex)
            {
                WriteError(ex);
            }
            finally
            {
                lst = null;
            }
        }
    }
}
