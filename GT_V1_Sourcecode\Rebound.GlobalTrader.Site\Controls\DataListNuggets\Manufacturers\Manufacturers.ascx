<%@ Control Language="C#" CodeBehind="Manufacturers.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.Manufacturers" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
    <%--<Links>
        <ReboundUI:IconButton ID="ibtnAdd" runat="server" IconGroup="Nugget" IconTitleResource="AddManufacturerInGroup" IconCSSType="Add" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>--%>
    <Filters>
        <ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
            <FieldsLeft>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlName" runat="server" ResourceTitle="Name" commonid="Name" FilterField="Name" />
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlManuName" runat="server" ResourceTitle="GroupName" commonid="GroupName" FilterField="GroupName" />
            </FieldsLeft>
            <FieldsRight>
                <ReboundUI_FilterDataItemRow:TextBox ID="ctlCode" runat="server" ResourceTitle="Code" commonid="Code" FilterField="Code" />
                <ReboundUI_FilterDataItemRow:TextBox id="ctlGroup" runat="server" ResourceTitle="GroupCode" commonid="GroupCode" FilterField="GroupCode" />
            </FieldsRight>
        </ReboundUI_DataListNugget:Filter>

    </Filters>
   <%-- <Forms>
        <ReboundForm:ContactGroup_Add id="ctlContactGroup" runat="server" />
    </Forms>--%>
</ReboundUI_Nugget:DesignBase>
