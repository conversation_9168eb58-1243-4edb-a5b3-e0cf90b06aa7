Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.initializeBase(this,[n]);this._intLineID=-1;this._blnLineIsService=!1;this._dblCurrencyRateToBase=1;this._strCurrencyCode=""};Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.prototype={get_lblCurrency:function(){return this._lblCurrency},set_lblCurrency:function(n){this._lblCurrency!==n&&(this._lblCurrency=n)},get_lblLandedCost:function(){return this._lblLandedCost},set_lblLandedCost:function(n){this._lblLandedCost!==n&&(this._lblLandedCost=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),$addHandler(this.getFieldControl("ctlLandedCost"),"change",Function.createDelegate(this,this.landedCostChanged)));this.showFields(!0);this.landedCostChanged()},dispose:function(){this.isDisposed||(this.getFieldControl("ctlLandedCost")&&$clearHandlers(this.getFieldControl("ctlLandedCost")),this._lblCurrency=null,this._lblLandedCost=null,Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.callBaseMethod(this,"dispose"))},landedCostChanged:function(){this.setLandedCostInCurrency($R_FN.formatCurrency(this.getFieldValue("ctlLandedCost")*this._dblCurrencyRateToBase,this._strCurrencyCode))},setLandedCostInCurrency:function(n){$R_FN.setInnerHTML(this._lblLandedCost,String.format("({0})",n))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/CreditLines");n.set_DataObject("CreditLines");n.set_DataAction("SaveEdit");n.addParameter("id",this._intLineID);n.addParameter("Quantity",this.getFieldValue("ctlQuantity"));n.addParameter("Price",this.getFieldValue("ctlPrice"));n.addParameter("LandedCost",this.getFieldValue("ctlLandedCost"));n.addParameter("LineIsService",this._blnLineIsService);n.addParameter("LineNotes",this.getFieldValue("ctlLineNotes"));this._blnLineIsService&&(n.addParameter("Service",this.getFieldValue("ctlService")),n.addParameter("ServiceDescription",this.getFieldValue("ctlServiceDescription")));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this._blnLineIsService&&(this.checkFieldEntered("ctlService")||(n=!1)),this.checkFieldEntered("ctlQuantity")||(n=!1),this.checkFieldNumeric("ctlQuantity")||(n=!1),this.checkFieldEntered("ctlPrice")||(n=!1),this.checkFieldNumeric("ctlPrice")||(n=!1),n||this.showError(!0),n},setCurrency:function(n){$R_FN.setInnerHTML(this._lblCurrency,n)},showFields:function(n){this.showField("ctlQuantity",n);this.showField("ctlPrice",n);this.showField("ctlLandedCost",n);this.showField("ctlPartNo",n&&!this._blnLineIsService);this.showField("ctlManufacturer",n&&!this._blnLineIsService);this.showField("ctlDateCode",n&&!this._blnLineIsService);this.showField("ctlPackage",n&&!this._blnLineIsService);this.showField("ctlProduct",n&&!this._blnLineIsService);this.showField("ctlService",n&&this._blnLineIsService);this.showField("ctlServiceDescription",n&&this._blnLineIsService)}};Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditLines_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);