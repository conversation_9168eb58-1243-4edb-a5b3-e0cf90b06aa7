//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class FriendlyPageTitles {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FriendlyPageTitles() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.FriendlyPageTitles", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Communications Home.
        /// </summary>
        internal static string Communications {
            get {
                return ResourceManager.GetString("Communications", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Message Groups.
        /// </summary>
        internal static string Communications_MailMessageGroups {
            get {
                return ResourceManager.GetString("Communications_MailMessageGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Home.
        /// </summary>
        internal static string Contact {
            get {
                return ResourceManager.GetString("Contact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Company.
        /// </summary>
        internal static string Contact_CompanyAdd {
            get {
                return ResourceManager.GetString("Contact_CompanyAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Companies.
        /// </summary>
        internal static string Contact_CompanyBrowse {
            get {
                return ResourceManager.GetString("Contact_CompanyBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Company Detail.
        /// </summary>
        internal static string Contact_CompanyDetail {
            get {
                return ResourceManager.GetString("Contact_CompanyDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Contact Detail.
        /// </summary>
        internal static string Contact_ContactDetail {
            get {
                return ResourceManager.GetString("Contact_ContactDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Group Code.
        /// </summary>
        internal static string Contact_GroupCodeCompanyAdd {
            get {
                return ResourceManager.GetString("Contact_GroupCodeCompanyAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Manufacturer.
        /// </summary>
        internal static string Contact_ManufacturerAdd {
            get {
                return ResourceManager.GetString("Contact_ManufacturerAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Manufacturers.
        /// </summary>
        internal static string Contact_ManufacturerBrowse {
            get {
                return ResourceManager.GetString("Contact_ManufacturerBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manufacturer Detail.
        /// </summary>
        internal static string Contact_ManufacturerDetail {
            get {
                return ResourceManager.GetString("Contact_ManufacturerDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home.
        /// </summary>
        internal static string Home {
            get {
                return ResourceManager.GetString("Home", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders Home.
        /// </summary>
        internal static string Orders {
            get {
                return ResourceManager.GetString("Orders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New HUBRFQ.
        /// </summary>
        internal static string Orders_BOMAdd {
            get {
                return ResourceManager.GetString("Orders_BOMAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse HUBRFQ.
        /// </summary>
        internal static string Orders_BOMBrowse {
            get {
                return ResourceManager.GetString("Orders_BOMBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ Detail.
        /// </summary>
        internal static string Orders_BOMDetail {
            get {
                return ResourceManager.GetString("Orders_BOMDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Import.
        /// </summary>
        internal static string Orders_BOMImport {
            get {
                return ResourceManager.GetString("Orders_BOMImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice.
        /// </summary>
        internal static string Orders_ClientInvoiceDetail {
            get {
                return ResourceManager.GetString("Orders_ClientInvoiceDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Credit Note.
        /// </summary>
        internal static string Orders_CreditNoteAdd {
            get {
                return ResourceManager.GetString("Orders_CreditNoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Credit Notes.
        /// </summary>
        internal static string Orders_CreditNoteBrowse {
            get {
                return ResourceManager.GetString("Orders_CreditNoteBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Note Detail.
        /// </summary>
        internal static string Orders_CreditNoteDetail {
            get {
                return ResourceManager.GetString("Orders_CreditNoteDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Requirement.
        /// </summary>
        internal static string Orders_CustomerRequirementAdd {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirementAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Customer Requirements.
        /// </summary>
        internal static string Orders_CustomerRequirementBrowse {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirementBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirement Detail.
        /// </summary>
        internal static string Orders_CustomerRequirementDetail {
            get {
                return ResourceManager.GetString("Orders_CustomerRequirementDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Customer RMA.
        /// </summary>
        internal static string Orders_CustomerRMAAdd {
            get {
                return ResourceManager.GetString("Orders_CustomerRMAAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Customer RMAs.
        /// </summary>
        internal static string Orders_CustomerRMABrowse {
            get {
                return ResourceManager.GetString("Orders_CustomerRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA Detail.
        /// </summary>
        internal static string Orders_CustomerRMADetail {
            get {
                return ResourceManager.GetString("Orders_CustomerRMADetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Debit Note.
        /// </summary>
        internal static string Orders_DebitNoteAdd {
            get {
                return ResourceManager.GetString("Orders_DebitNoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Debit Notes.
        /// </summary>
        internal static string Orders_DebitNoteBrowse {
            get {
                return ResourceManager.GetString("Orders_DebitNoteBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Note Detail.
        /// </summary>
        internal static string Orders_DebitNoteDetail {
            get {
                return ResourceManager.GetString("Orders_DebitNoteDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Internal Purchase.
        /// </summary>
        internal static string Orders_InternalPurchaseOrderAdd {
            get {
                return ResourceManager.GetString("Orders_InternalPurchaseOrderAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Purchase Order.
        /// </summary>
        internal static string Orders_InternalPurchaseOrderBrowse {
            get {
                return ResourceManager.GetString("Orders_InternalPurchaseOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Internal Purchase Order.
        /// </summary>
        internal static string Orders_InternalPurchaseOrderDetail {
            get {
                return ResourceManager.GetString("Orders_InternalPurchaseOrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Invoice.
        /// </summary>
        internal static string Orders_InvoiceAdd {
            get {
                return ResourceManager.GetString("Orders_InvoiceAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoices.
        /// </summary>
        internal static string Orders_InvoiceBrowse {
            get {
                return ResourceManager.GetString("Orders_InvoiceBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Detail.
        /// </summary>
        internal static string Orders_InvoiceDetail {
            get {
                return ResourceManager.GetString("Orders_InvoiceDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Lines.
        /// </summary>
        internal static string Orders_OGELLinesExportBrowse {
            get {
                return ResourceManager.GetString("Orders_OGELLinesExportBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Purchase Order.
        /// </summary>
        internal static string Orders_PurchaseOrderAdd {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrderAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Purchase Orders.
        /// </summary>
        internal static string Orders_PurchaseOrderBrowse {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Detail.
        /// </summary>
        internal static string Orders_PurchaseOrderDetail {
            get {
                return ResourceManager.GetString("Orders_PurchaseOrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Requisitions.
        /// </summary>
        internal static string Orders_PurchaseRequisitionBrowse {
            get {
                return ResourceManager.GetString("Orders_PurchaseRequisitionBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Quote.
        /// </summary>
        internal static string Orders_QuoteAdd {
            get {
                return ResourceManager.GetString("Orders_QuoteAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quotes.
        /// </summary>
        internal static string Orders_QuoteBrowse {
            get {
                return ResourceManager.GetString("Orders_QuoteBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quote Detail.
        /// </summary>
        internal static string Orders_QuoteDetail {
            get {
                return ResourceManager.GetString("Orders_QuoteDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Sales Order.
        /// </summary>
        internal static string Orders_SalesOrderAdd {
            get {
                return ResourceManager.GetString("Orders_SalesOrderAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Sales Orders.
        /// </summary>
        internal static string Orders_SalesOrderBrowse {
            get {
                return ResourceManager.GetString("Orders_SalesOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Detail.
        /// </summary>
        internal static string Orders_SalesOrderDetail {
            get {
                return ResourceManager.GetString("Orders_SalesOrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sourcing.
        /// </summary>
        internal static string Orders_Sourcing {
            get {
                return ResourceManager.GetString("Orders_Sourcing", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Supplier RMA.
        /// </summary>
        internal static string Orders_SupplierRMAAdd {
            get {
                return ResourceManager.GetString("Orders_SupplierRMAAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Supplier RMAs.
        /// </summary>
        internal static string Orders_SupplierRMABrowse {
            get {
                return ResourceManager.GetString("Orders_SupplierRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA Detail.
        /// </summary>
        internal static string Orders_SupplierRMADetail {
            get {
                return ResourceManager.GetString("Orders_SupplierRMADetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mail Messages.
        /// </summary>
        internal static string Profile_MailMessages {
            get {
                return ResourceManager.GetString("Profile_MailMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To Do List.
        /// </summary>
        internal static string Profile_ToDo {
            get {
                return ResourceManager.GetString("Profile_ToDo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reports Home.
        /// </summary>
        internal static string Reports {
            get {
                return ResourceManager.GetString("Reports", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Report Detail.
        /// </summary>
        internal static string Reports_ReportDetail {
            get {
                return ResourceManager.GetString("Reports_ReportDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Home.
        /// </summary>
        internal static string Setup {
            get {
                return ResourceManager.GetString("Setup", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Application Settings.
        /// </summary>
        internal static string Setup_CompanyDetails_ApplicationSettings {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_ApplicationSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Client Invoice Header.
        /// </summary>
        internal static string Setup_CompanyDetails_ClientInvoiceHeader {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_ClientInvoiceHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Countries.
        /// </summary>
        internal static string Setup_CompanyDetails_Country {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Country", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Currencies.
        /// </summary>
        internal static string Setup_CompanyDetails_Currency {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Currency", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Divisions.
        /// </summary>
        internal static string Setup_CompanyDetails_Division {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Division", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Nice Label Path.
        /// </summary>
        internal static string Setup_CompanyDetails_LabelPath {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_LabelPath", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Mail Groups.
        /// </summary>
        internal static string Setup_CompanyDetails_MailGroups {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_MailGroups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Printed Documents.
        /// </summary>
        internal static string Setup_CompanyDetails_PrintedDocuments {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_PrintedDocuments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Printer.
        /// </summary>
        internal static string Setup_CompanyDetails_Printer {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Printer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Products.
        /// </summary>
        internal static string Setup_CompanyDetails_Product {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Product", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Sequence Numbers.
        /// </summary>
        internal static string Setup_CompanyDetails_SequenceNumber {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_SequenceNumber", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Shipping Methods.
        /// </summary>
        internal static string Setup_CompanyDetails_ShippingMethod {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_ShippingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Sourcing Links.
        /// </summary>
        internal static string Setup_CompanyDetails_SourcingLinks {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_SourcingLinks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Stock Log Reasons.
        /// </summary>
        internal static string Setup_CompanyDetails_StockLogReason {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_StockLogReason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Taxes.
        /// </summary>
        internal static string Setup_CompanyDetails_Tax {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Tax", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Teams.
        /// </summary>
        internal static string Setup_CompanyDetails_Team {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Team", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Terms.
        /// </summary>
        internal static string Setup_CompanyDetails_Terms {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Terms", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Users.
        /// </summary>
        internal static string Setup_CompanyDetails_User {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_User", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Warehouses.
        /// </summary>
        internal static string Setup_CompanyDetails_Warehouse {
            get {
                return ResourceManager.GetString("Setup_CompanyDetails_Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Local Currencies.
        /// </summary>
        internal static string Setup_CompanySettings_LocalCurrencies {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_LocalCurrencies", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - OGEL Licenses.
        /// </summary>
        internal static string Setup_CompanySettings_OGELLicenses {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_OGELLicenses", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Printer.
        /// </summary>
        internal static string Setup_CompanySettings_Printer {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_Printer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Company Settings - Restricted Manufacturer.
        /// </summary>
        internal static string Setup_CompanySettings_RestrictedManufacture {
            get {
                return ResourceManager.GetString("Setup_CompanySettings_RestrictedManufacture", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Application Settings.
        /// </summary>
        internal static string Setup_GlobalSettings_ApplicationSettings {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_ApplicationSettings", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - AS6081.
        /// </summary>
        internal static string Setup_GlobalSettings_AS6081 {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_AS6081", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Certificate &amp; Certificate Category.
        /// </summary>
        internal static string Setup_GlobalSettings_Certificate {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Certificate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Communication Log Types.
        /// </summary>
        internal static string Setup_GlobalSettings_CommunicationLogType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CommunicationLogType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Company Types.
        /// </summary>
        internal static string Setup_GlobalSettings_CompanyType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CompanyType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Counting Methods.
        /// </summary>
        internal static string Setup_GlobalSettings_CountingMethod {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_CountingMethod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Entertainment Type.
        /// </summary>
        internal static string Setup_GlobalSettings_EntertainmentType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_EntertainmentType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Incoterms.
        /// </summary>
        internal static string Setup_GlobalSettings_Incoterm {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Incoterm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Industry Types.
        /// </summary>
        internal static string Setup_GlobalSettings_IndustryType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_IndustryType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Master Country List.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCountryList {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCountryList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Master Currency List.
        /// </summary>
        internal static string Setup_GlobalSettings_MasterCurrencyList {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_MasterCurrencyList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Packages.
        /// </summary>
        internal static string Setup_GlobalSettings_Package {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Package", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Document File Size.
        /// </summary>
        internal static string Setup_GlobalSettings_PDFDocumentFileSize {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PDFDocumentFileSize", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - PPV/ BOM Qualification.
        /// </summary>
        internal static string Setup_GlobalSettings_PPVBOMQualification {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_PPVBOMQualification", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Product Types.
        /// </summary>
        internal static string Setup_GlobalSettings_ProductType {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_ProductType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Global Settings - Quote Close Reasons.
        /// </summary>
        internal static string Setup_GlobalSettings_Reason {
            get {
                return ResourceManager.GetString("Setup_GlobalSettings_Reason", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Personal Settings - My Profile.
        /// </summary>
        internal static string Setup_Personal_UserProfile {
            get {
                return ResourceManager.GetString("Setup_Personal_UserProfile", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Security Settings - Security Groups.
        /// </summary>
        internal static string Setup_Security_Groups {
            get {
                return ResourceManager.GetString("Setup_Security_Groups", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup Security Settings - Security Users.
        /// </summary>
        internal static string Setup_Security_Users {
            get {
                return ResourceManager.GetString("Setup_Security_Users", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Internal Purchase Order.
        /// </summary>
        internal static string String1 {
            get {
                return ResourceManager.GetString("String1", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Offers Import.
        /// </summary>
        internal static string Utility_ProsOfferImport {
            get {
                return ResourceManager.GetString("Utility_ProsOfferImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string vxcv {
            get {
                return ResourceManager.GetString("vxcv", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse Home.
        /// </summary>
        internal static string Warehouse {
            get {
                return ResourceManager.GetString("Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Goods In Note.
        /// </summary>
        internal static string Warehouse_GoodsInAdd {
            get {
                return ResourceManager.GetString("Warehouse_GoodsInAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Goods In.
        /// </summary>
        internal static string Warehouse_GoodsInBrowse {
            get {
                return ResourceManager.GetString("Warehouse_GoodsInBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Detail.
        /// </summary>
        internal static string Warehouse_GoodsInDetail {
            get {
                return ResourceManager.GetString("Warehouse_GoodsInDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Lot.
        /// </summary>
        internal static string Warehouse_LotsAdd {
            get {
                return ResourceManager.GetString("Warehouse_LotsAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Lots.
        /// </summary>
        internal static string Warehouse_LotsBrowse {
            get {
                return ResourceManager.GetString("Warehouse_LotsBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Lot.
        /// </summary>
        internal static string Warehouse_LotsDetail {
            get {
                return ResourceManager.GetString("Warehouse_LotsDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Receive Customer RMAs.
        /// </summary>
        internal static string Warehouse_ReceiveCustomerRMABrowse {
            get {
                return ResourceManager.GetString("Warehouse_ReceiveCustomerRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Customer RMA.
        /// </summary>
        internal static string Warehouse_ReceiveCustomerRMADetail {
            get {
                return ResourceManager.GetString("Warehouse_ReceiveCustomerRMADetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Receive Purchase Orders.
        /// </summary>
        internal static string Warehouse_ReceivePurchaseOrderBrowse {
            get {
                return ResourceManager.GetString("Warehouse_ReceivePurchaseOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Purchase Order.
        /// </summary>
        internal static string Warehouse_ReceivePurchaseOrderDetail {
            get {
                return ResourceManager.GetString("Warehouse_ReceivePurchaseOrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Service.
        /// </summary>
        internal static string Warehouse_ServicesAdd {
            get {
                return ResourceManager.GetString("Warehouse_ServicesAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Services.
        /// </summary>
        internal static string Warehouse_ServicesBrowse {
            get {
                return ResourceManager.GetString("Warehouse_ServicesBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Service.
        /// </summary>
        internal static string Warehouse_ServicesDetail {
            get {
                return ResourceManager.GetString("Warehouse_ServicesDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Ship Sales Orders.
        /// </summary>
        internal static string Warehouse_ShipSalesOrderBrowse {
            get {
                return ResourceManager.GetString("Warehouse_ShipSalesOrderBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Sales Order.
        /// </summary>
        internal static string Warehouse_ShipSalesOrderDetail {
            get {
                return ResourceManager.GetString("Warehouse_ShipSalesOrderDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Ship Supplier RMAs.
        /// </summary>
        internal static string Warehouse_ShipSupplierRMABrowse {
            get {
                return ResourceManager.GetString("Warehouse_ShipSupplierRMABrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Supplier RMA.
        /// </summary>
        internal static string Warehouse_ShipSupplierRMADetail {
            get {
                return ResourceManager.GetString("Warehouse_ShipSupplierRMADetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Stock Item.
        /// </summary>
        internal static string Warehouse_StockAdd {
            get {
                return ResourceManager.GetString("Warehouse_StockAdd", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse Stock.
        /// </summary>
        internal static string Warehouse_StockBrowse {
            get {
                return ResourceManager.GetString("Warehouse_StockBrowse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Item.
        /// </summary>
        internal static string Warehouse_StockDetail {
            get {
                return ResourceManager.GetString("Warehouse_StockDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice.
        /// </summary>
        internal static string Warehouse_SupplierInvoiceDetail {
            get {
                return ResourceManager.GetString("Warehouse_SupplierInvoiceDetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Short Shipment.
        /// </summary>
        internal static string WHS_SHORTSHIPMENTDETAILS {
            get {
                return ResourceManager.GetString("WHS_SHORTSHIPMENTDETAILS", resourceCulture);
            }
        }
    }
}
