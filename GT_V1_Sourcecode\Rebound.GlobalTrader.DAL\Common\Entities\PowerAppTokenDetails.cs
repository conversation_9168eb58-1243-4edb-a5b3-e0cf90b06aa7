﻿/*
Marker     Changed by              Date            Remarks
[001]      A<PERSON><PERSON><PERSON>          18/05/2022      Need to generate Power App token.
*/
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{
    public class PowerAppTokenDetails
    {
        #region Constructors

        public PowerAppTokenDetails() { }

        #endregion

        #region Properties
        /// <summary>
        /// RequestId
        /// </summary>
        public System.Int32 RequestId { get; set; }
        /// <summary>
        /// TokenValue
        /// </summary>
        public System.String TokenValue { get; set; }
        #endregion
    }
}
