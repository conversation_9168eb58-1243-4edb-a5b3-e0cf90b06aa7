///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Delete = function(element) {
	Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Delete.initializeBase(this, [element]);
	this._intBOMID = -1;
	this._strGeneratedID = "";
	this._IsFromBOMAdd = false;

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Delete.prototype = {

	get_intBOMID: function() { return this._intBOMID; }, 	set_intBOMID: function(value) { if (this._intBOMID !== value)  this._intBOMID = value; },
	get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
	get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
	get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
	get_BomCompanyNo: function() { return this._BomCompanyNo; }, set_BomCompanyNo: function(value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Delete.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intBOMID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Delete.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
	},

	yesClicked: function() {
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("Controls/Nuggets/BOMPVV");
		obj.set_DataObject("BOMPVV");
		if (this._IsFromBOMAdd) {
			obj.set_DataAction("DeleteTemp");
			obj.addParameter("idGenerated", this._strGeneratedID);
		}
		else {
			obj.set_DataAction("Delete");
			obj.addParameter("id", this._intBOMID);
		}
		
		obj.addDataOK(Function.createDelegate(this, this.saveComplete));
		obj.addError(Function.createDelegate(this, this.saveError));
		obj.addTimeout(Function.createDelegate(this, this.saveError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},

	noClicked: function () {
		this.showSaving(false);
		this.onNotConfirmed();
	},

	saveError: function (args) {
		this.showSaving(false);
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},

	saveComplete: function (args) {
		this.showSaving(false);
		if (args._result.Result) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	}
	

	
	
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Delete.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMPVV_Delete", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
