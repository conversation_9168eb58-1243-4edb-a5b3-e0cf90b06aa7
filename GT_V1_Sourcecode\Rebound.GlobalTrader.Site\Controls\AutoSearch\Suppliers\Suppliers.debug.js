///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 15.02.2010:
// - fix data coming from AllCompanies.ashx rather than Suppliers.ashx
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");

Rebound.GlobalTrader.Site.Controls.AutoSearch.Suppliers = function(element) { 
	Rebound.GlobalTrader.Site.Controls.AutoSearch.Suppliers.initializeBase(this, [element]);
	this._blnUseSupplierNo = false;
};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Suppliers.prototype = {

    get_blnUseSupplierNo: function() { return this._blnUseSupplierNo; }, set_blnUseSupplierNo: function(v) { if (this._blnUseSupplierNo !== v) this._blnUseSupplierNo = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.AutoSearch.Suppliers.callBaseMethod(this, "initialize");
        this.addDataReturnedEvent(Function.createDelegate(this, this.dataReturned));
        this.setupDataObject("Suppliers");
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._blnUseSupplierNo = null;
        Rebound.GlobalTrader.Site.Controls.AutoSearch.Suppliers.callBaseMethod(this, "dispose");
    },

    dataReturned: function() {
        if (!this._result) return;
        if (this._result.TotalRecords > 0) {
            for (var i = 0, l = this._result.Results.length; i < l; i++) {
                var res = this._result.Results[i];
                var strHTML = "";
                if (this._enmResultsActionType == $R_ENUM$AutoSearchResultsActionType.Navigate) {
                    strHTML = $RGT_nubButton_Company(res.ID, res.Name);
                } else {
                    strHTML = res.Name;
                }
                this.addResultItem(strHTML, res.Name, (this._blnUseSupplierNo) ? res.SupNo : res.ID, res.Email);
                strHTML = null; res = null;
            }
        }
    }

};

Rebound.GlobalTrader.Site.Controls.AutoSearch.Suppliers.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.Suppliers", Rebound.GlobalTrader.Site.Controls.AutoSearch.Base, Sys.IDisposable);
