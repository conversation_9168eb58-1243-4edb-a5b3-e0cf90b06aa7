﻿GO
/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-202901]     Phuc Hoang		 11-Sep-2024		CREATE		German Invoice Part 1-Implement monthly Germany exchange rates published by tax authorities
===========================================================================================  
*/

IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[tbGermanyExchangeRate]') AND type in (N'U'))

BEGIN
	CREATE TABLE [dbo].[tbGermanyExchangeRate](
		[ExchangeRateId] [int] IDENTITY(1,1) NOT NULL,
		[CountryName] [nvarchar](128) NULL,
		[FromCurrency] [nvarchar](32) NULL,
		[ToCurrency] [nvarchar](32) NULL,
		[January] [decimal](18, 5) NULL,
		[February] [decimal](18, 5) NULL,
		[March] [decimal](18, 5) NULL,
		[April] [decimal](18, 5) NULL,
		[May] [decimal](18, 5) NULL,
		[June] [decimal](18, 5) NULL,
		[July] [decimal](18, 5) NULL,
		[August] [decimal](18, 5) NULL,
		[September] [decimal](18, 5) NULL,
		[October] [decimal](18, 5) NULL,
		[November] [decimal](18, 5) NULL,
		[December] [decimal](18, 5) NULL,
		[Inactive] [bit] NOT NULL,
		[UpdatedBy] [int] NULL,
		[DLUP] [datetime] NOT NULL,
	 CONSTRAINT [PK_tbGermanyExchangeRate] PRIMARY KEY CLUSTERED 
	(
		[ExchangeRateId] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]

	ALTER TABLE [dbo].[tbGermanyExchangeRate] ADD  CONSTRAINT [DF_tbGermanyExchangeRate_Inactive]  DEFAULT ((0)) FOR [Inactive]

	ALTER TABLE [dbo].[tbGermanyExchangeRate] ADD  CONSTRAINT [DF_tbGermanyExchangeRate_DLUP]  DEFAULT (getdate()) FOR [DLUP]
	
END