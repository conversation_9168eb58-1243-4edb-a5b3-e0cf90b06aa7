///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by      Date         Remarks
//[001]      Vinay           23/11/2015   Debit note should be fill with PO buyer
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.prototype = {

	get_intSRMAID: function() { return this._intSRMAID; }, 	set_intSRMAID: function(v) { if (this._intSRMAID !== v)  this._intSRMAID = v; }, 
	get_ctlMainInfo: function() { return this._ctlMainInfo; }, 	set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v)  this._ctlMainInfo = v; }, 
	get_ctlLines: function() { return this._ctlLines; }, 	set_ctlLines: function(v) { if (this._ctlLines !== v)  this._ctlLines = v; }, 
	get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.callBaseMethod(this, "initialize");
	},
	
	goInit: function() {
		if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
		if (this._ctlLines) this._ctlLines.addSaveShipComplete(Function.createDelegate(this, this.ctlLines_SaveShipComplete));
		Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.callBaseMethod(this, "goInit");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlMainInfo) this._ctlMainInfo.dispose();
		if (this._ctlLines) this._ctlLines.dispose();
		this._intSRMAID = null;
		this._ctlMainInfo = null;
		this._ctlLines = null;
		this._IsGlobalLogin = null;
		Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.callBaseMethod(this, "dispose");
	},
	
	ctlMainInfo_GetDataComplete: function() {
		if (this._ctlLines._frmShip) {
			this._ctlLines._frmShip.setFieldsFromHeader(
				this._ctlMainInfo.getFieldValue("hidSupplierNo")
				, this._ctlMainInfo.getFieldValue("hidSupplierName")
				, this._ctlMainInfo.getFieldValue("hidNo")
				, this._ctlMainInfo.getFieldValue("ctlShipToAddress")
				, this._ctlMainInfo.getFieldValue("hidContactNo")
				, this._ctlMainInfo.getFieldValue("hidContactName")
				, this._ctlMainInfo.getFieldValue("hidAuthorisedBy")
				, this._ctlMainInfo.getFieldValue("ctlAuthoriser")
				, this._ctlMainInfo.getFieldValue("hidDivisionNo")
				, this._ctlMainInfo.getFieldValue("hidCurrencyNo")
				, this._ctlMainInfo.getFieldValue("ctlCurrency")
				, this._ctlMainInfo.getFieldValue("hidCurrencyCode")
				, this._ctlMainInfo.getFieldValue("hidPurchaseOrderNo")
				, this._ctlMainInfo.getFieldValue("hidPurchaseOrderNumber")
				, this._ctlMainInfo.getFieldValue("hidTaxNo")
				, this._ctlMainInfo.getFieldValue("ctlTax")
				, this._ctlMainInfo.getFieldValue("ctlNotes")
				, this._ctlMainInfo.getFieldValue("ctlInstructions")
			     //[001] code start
				, this._ctlMainInfo.getFieldValue("hidPOBuyerNo")
				, this._ctlMainInfo.getFieldValue("hidPOBuyerName")
			    //[001] code end
			);
		}
		this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
		this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;
	},
	
	ctlLines_SaveShipComplete: function() {
		this._ctlMainInfo.getData();
	}

};
Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ShipSRMADetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
