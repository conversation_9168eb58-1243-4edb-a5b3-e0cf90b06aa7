﻿using System;
using System.Configuration;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Xml;
using System.Net;
using System.IO;
using System.Xml.Linq;

namespace Rebound.GlobalTrader.Site
{
    internal class RequestWebAPI
    {        
        //string _apiURL = Common.ConvertTo<string>(ConfigurationSettings.AppSettings["APIURL"], string.Empty);

        public string DataToPost { get; set; }
        public string URL { get; set; }
        public string Result { get; private set; }

        public RequestWebAPI(string _dataTopost, string _apiURL)
        {
            this.DataToPost = _dataTopost;
            this.URL = _apiURL;
            this.Result = this.GetServiceData();
        }

        public DataTable GetDataTable(string tableName, string[] strColumns, string nodeToTraverse)
        {   
            XmlDocument objXMLDoc = new XmlDocument();
            objXMLDoc.LoadXml(this.Result);
            XmlNodeList nodelist = objXMLDoc.SelectNodes(nodeToTraverse);
            return XmlNodeListToDataTable(nodelist, tableName, strColumns);            
        }
        
        private string GetServiceData()
        {
            byte[] buffer = Encoding.ASCII.GetBytes(this.DataToPost);
            HttpWebRequest request = (HttpWebRequest)WebRequest.Create(this.URL);
            request.Method = "POST";
            request.ContentType = "application/x-www-form-urlencoded";
            request.ContentLength = buffer.Length;

            Stream PostData = request.GetRequestStream();
            PostData.Write(buffer, 0, buffer.Length);
            PostData.Close();

            HttpWebResponse response = (HttpWebResponse)request.GetResponse();
            Stream result = response.GetResponseStream();
            StreamReader sr = new StreamReader(result);
            string strResult = sr.ReadToEnd();
            return strResult;
        }
        
        /// <summary>
        /// Converts an XMLNodelist object to data table.
        /// </summary>
        /// <param name="xmlNodeList">The XMLNodeList to convert to DataTable.</param>
        /// <param name="Columns">The columns required for the DataTable, using this parameter the
        /// data will be fetched from xmlnodelist.</param>
        /// <returns>DataTable with columns specified in the Columns parameter</returns>
        private DataTable XmlNodeListToDataTable(XmlNodeList xmlNodeList, string tableName,  string[] Columns)
        {
            //Creating the DataTable.
            using (DataTable dataTable = new DataTable(tableName))
            {
                //Adding data Table columns based on the columns parameter
                foreach (string column in Columns)
                {
                    dataTable.Columns.Add(column);
                }
                //Adding rows with values.
                DataRow dataRow;
                foreach (XmlNode node in xmlNodeList)
                {
                    dataRow = dataTable.NewRow();
                    foreach (string column in Columns)
                    {
                        dataRow[column] = node.SelectSingleNode(column).InnerText;
                    }
                    dataTable.Rows.Add(dataRow);
                }
                return dataTable;
            }
        }
    }
}
