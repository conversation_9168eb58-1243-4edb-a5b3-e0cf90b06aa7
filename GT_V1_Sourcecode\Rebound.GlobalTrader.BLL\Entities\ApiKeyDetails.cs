﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    public partial class ApiKeyDetails : BizObject
    {
        public System.Int32 ApiURLKeyId { get; set; }
        public System.String URL { get; set; }
        public System.String Licensekey { get; set; }
        public System.String HostName { get; set; }
        public System.String ApiShortName { get; set; }
        public System.String ApiName { get; set; }
    }
}
