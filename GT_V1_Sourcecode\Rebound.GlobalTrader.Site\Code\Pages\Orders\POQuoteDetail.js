Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail=function(n){Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.prototype={get_ctlPOQuoteMainInfo:function(){return this._ctlPOQuoteMainInfo},set_ctlPOQuoteMainInfo:function(n){this._ctlPOQuoteMainInfo!==n&&(this._ctlPOQuoteMainInfo=n)},get_ctlPOQuoteLines:function(){return this._ctlPOQuoteLines},set_ctlPOQuoteLines:function(n){this._ctlPOQuoteLines!==n&&(this._ctlPOQuoteLines=n)},get_ctlPurchaseRequestLineDetail:function(){return this._ctlPurchaseRequestLineDetail},set_ctlPurchaseRequestLineDetail:function(n){this._ctlPurchaseRequestLineDetail!==n&&(this._ctlPurchaseRequestLineDetail=n)},get_ctlPurchaseCSV:function(){return this._ctlPurchaseCSV},set_ctlPurchaseCSV:function(n){this._ctlPurchaseCSV!==n&&(this._ctlPurchaseCSV=n)},get_ctlCsvExportHistory:function(){return this._ctlCsvExportHistory},set_ctlCsvExportHistory:function(n){this._ctlCsvExportHistory!==n&&(this._ctlCsvExportHistory=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.callBaseMethod(this,"initialize")},goInit:function(){this._ctlPOQuoteMainInfo&&this._ctlPOQuoteMainInfo.addSaveEditComplete(Function.createDelegate(this,this.ctlPOQuoteMainInfo_SaveEditComplete));this._ctlPOQuoteLines&&this._ctlPOQuoteLines.addPotentialStatusChange(Function.createDelegate(this,this.ctlLines_PotentialStatusChange));this._ctlPOQuoteLines&&this._ctlPOQuoteLines.addPurchaseRequestLineSelect(Function.createDelegate(this,this.ctlLines_PurchaseRequestLineSelect));this._ctlPOQuoteMainInfo&&this._ctlPOQuoteMainInfo.addRefreshLog(Function.createDelegate(this,this.ctlPOQuoteMainInfo_addRefreshLog));Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlPOQuoteMainInfo&&this._ctlPOQuoteMainInfo.dispose(),this._ctlPOQuoteLines&&this._ctlPOQuoteLines.dispose(),this._ctlCsvExportHistory=null,this._ctlPOQuoteMainInfo=null,this._ctlPOQuoteLines=null,this._ctlPurchaseCSV=null,Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.callBaseMethod(this,"dispose"))},ctlPOQuoteMainInfo_GetDataComplete:function(){},ctlPOQuoteMainInfo_addRefreshLog:function(){this._ctlCsvExportHistory.getCreditHistory()},ctlPOQuoteMainInfo_SaveEditComplete:function(){this._ctlPOQuoteLines&&this._ctlPOQuoteLines.getData()},ctlLines_PotentialStatusChange:function(){this._ctlPOQuoteMainInfo.enableExportAndNotifyButton(this._ctlPOQuoteLines._enableExportAndNotifyButton);this._ctlPurchaseRequestLineDetail._intPurchaseRequestLineNo=0;this._ctlPurchaseRequestLineDetail.getData();this._ctlPurchaseRequestLineDetail.enableDisableAddButton(!1);this._ctlPurchaseRequestLineDetail.enableDisableEditButton(!1)},ctlLines_PurchaseRequestLineSelect:function(){this._ctlPOQuoteLines._intPurchaseRequestLineId>0?(this._ctlPurchaseRequestLineDetail._intPurchaseRequestLineNo=this._ctlPOQuoteLines._intPurchaseRequestLineId,this._ctlPurchaseRequestLineDetail.getData(),this._ctlPurchaseRequestLineDetail._ctlPart=this._ctlPOQuoteLines._ctlPart,this._ctlPurchaseRequestLineDetail.enableDisableAddButton(!0),this._ctlPurchaseRequestLineDetail.enableDisableEditButton(!1)):this._ctlPurchaseRequestLineDetail.enableDisableAddButton(!1)},printQuote:function(){}};Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.POQuoteDetail",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);