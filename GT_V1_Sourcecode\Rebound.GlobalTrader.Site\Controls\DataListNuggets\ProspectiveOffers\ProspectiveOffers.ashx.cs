﻿using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data
{

    public class ProspectiveOffers : Base
    {

		protected override void GetData()
		{
			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

			////get data
			List<ProspectiveOffer> lst = ProspectiveOffer.DataListNugget(
				(enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_PartForLikeSearch("SourceFileName")
				, GetFormValue_PartForLikeSearch("Part")
				, GetFormValue_NullableDateTime("DateUploadFrom")
				, GetFormValue_NullableDateTime("DateUploadTo")
				, GetFormValue_NullableInt("ImportedBy")
				, GetFormValue_PartForLikeSearch("SupplierName")
			);

			JsonObject jsn = new JsonObject();
			JsonObject jsnRowsArray = new JsonObject(true);

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format json
			for (int i = 0; i < lst.Count; i++)
			{
				if (i < lst.Count)
				{
					JsonObject jsnRow = new JsonObject();
					jsnRow.AddVariable("ID", lst[i].ProspectiveOfferId);
					jsnRow.AddVariable("No", lst[i].ProspectiveOfferId);
					jsnRow.AddVariable("ImportDate", Functions.FormatDate(lst[i].ImportDate));
					jsnRow.AddVariable("Source", lst[i].SourceFileName);
					jsnRow.AddVariable("Rows", Functions.FormatNumeric(lst[i].ImportRowCount));
					jsnRow.AddVariable("ImportStatus", lst[i].ImportStatus);
					jsnRow.AddVariable("Supplier", lst[i].SupplierName);
					jsnRow.AddVariable("ImportedBy", lst[i].ImportedBy);
					jsnRowsArray.AddVariable(jsnRow);
					jsnRow.Dispose();
				}
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose(); 
			jsn.Dispose(); 
			base.GetData();
		}

		protected override void AddFilterStates()
		{
			base.AddFilterStates();
		}
	}
}