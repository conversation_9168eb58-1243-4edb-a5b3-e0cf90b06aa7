//----------------------------------------------------------------------------------------
// RP 21.12.2009:
//- use SimpleDataTable
//----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.HomeNuggets {
	public partial class MyStatistics : Base {

		protected Panel _pnlStatsTY;
		protected Panel _pnlStatsTM;
		protected Panel _pnlStatsNM;
		protected Panel _pnlStatsLY;
		protected Panel _pnlStatsLM;
		protected SimpleDataTable _tblStatsTY;
		protected SimpleDataTable _tblStatsTM;
		protected SimpleDataTable _tblStatsNM;
		protected SimpleDataTable _tblStatsLY;
		protected SimpleDataTable _tblStatsLM;

		/// <summary>
		/// On init
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			this.HomePageNuggetType = "MyStatistics";
			base.OnInit(e);
			WireUpControls();
			SetupTables();
			AddScriptReference("Controls.HomeNuggets.MyStatistics.MyStatistics.js");
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyStatistics", this.ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlStatsTY", _pnlStatsTY.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlStatsTM", _pnlStatsTM.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlStatsNM", _pnlStatsNM.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlStatsLY", _pnlStatsLY.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlStatsLM", _pnlStatsLM.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblStatsTY", _tblStatsTY.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblStatsTM", _tblStatsTM.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblStatsNM", _tblStatsNM.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblStatsLY", _tblStatsLY.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblStatsLM", _tblStatsLM.ClientID);
			base.OnLoad(e);
		}

		private void SetupTables() {
			_tblStatsTY.Columns.Add(new SimpleDataColumn("GPDetail"));
			_tblStatsTY.Columns.Add(new SimpleDataColumn("SalesValue", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsTY.Columns.Add(new SimpleDataColumn("Cost", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsTY.Columns.Add(new SimpleDataColumn("GP", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsTM.Columns.Add(new SimpleDataColumn("GPDetail"));
			_tblStatsTM.Columns.Add(new SimpleDataColumn("SalesValue", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsTM.Columns.Add(new SimpleDataColumn("Cost", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsTM.Columns.Add(new SimpleDataColumn("GP", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsNM.Columns.Add(new SimpleDataColumn("GPDetail"));
			_tblStatsNM.Columns.Add(new SimpleDataColumn("SalesValue", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsNM.Columns.Add(new SimpleDataColumn("Cost", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsNM.Columns.Add(new SimpleDataColumn("GP", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsLY.Columns.Add(new SimpleDataColumn("GPDetail"));
			_tblStatsLY.Columns.Add(new SimpleDataColumn("SalesValue", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsLY.Columns.Add(new SimpleDataColumn("Cost", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsLY.Columns.Add(new SimpleDataColumn("GP", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsLM.Columns.Add(new SimpleDataColumn("GPDetail"));
			_tblStatsLM.Columns.Add(new SimpleDataColumn("SalesValue", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsLM.Columns.Add(new SimpleDataColumn("Cost", Unit.Pixel(75), HorizontalAlign.Right));
			_tblStatsLM.Columns.Add(new SimpleDataColumn("GP", Unit.Pixel(75), HorizontalAlign.Right));
		}

		private void WireUpControls() {
			_pnlStatsTY = (Panel)FindContentControl("pnlStatsTY");
			_pnlStatsTM = (Panel)FindContentControl("pnlStatsTM");
			_pnlStatsNM = (Panel)FindContentControl("pnlStatsNM");
			_pnlStatsLY = (Panel)FindContentControl("pnlStatsLY");
			_pnlStatsLM = (Panel)FindContentControl("pnlStatsLM");
			_tblStatsTY = (SimpleDataTable)FindContentControl("tblStatsTY");
			_tblStatsTM = (SimpleDataTable)FindContentControl("tblStatsTM");
			_tblStatsNM = (SimpleDataTable)FindContentControl("tblStatsNM");
			_tblStatsLY = (SimpleDataTable)FindContentControl("tblStatsLY");
			_tblStatsLM = (SimpleDataTable)FindContentControl("tblStatsLM");
		}

	}
}