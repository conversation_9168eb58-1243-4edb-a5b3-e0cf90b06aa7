//--------------------------------------------------------------------------------------------------------
// RP 11.12.2009:
// - cache data
//--------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class BuyShipMethod : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

        public override void ProcessRequest(HttpContext context) {
            SetDropDownType("BuyShipMethod");
            base.ProcessRequest(context);
        }
        
        protected override void GetData() {
            int? intPOHubClientNo;
            intPOHubClientNo = GetFormValue_NullableInt("POHubClientNo");
              int? intGlobalLoginClientNo;
            intGlobalLoginClientNo = GetFormValue_NullableInt("GlobalLoginClientNo");
            string strOptions = CacheManager.SerializeOptions(new object[] { (intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value > 0) ? intGlobalLoginClientNo.Value : SessionManager.ClientID, (intPOHubClientNo.HasValue) ? intPOHubClientNo.Value : 0 });
            string strCachedData = CacheManager.GetDropDownData(_objDropDown.ID, strOptions);
            if (string.IsNullOrEmpty(strCachedData)) {
                JsonObject jsn = new JsonObject();
                JsonObject jsnList = new JsonObject(true);
                List<BLL.ShipVia> lst = BLL.ShipVia.DropDownBuyForClient((intGlobalLoginClientNo.HasValue && intGlobalLoginClientNo.Value>0)?intGlobalLoginClientNo.Value:((intPOHubClientNo.HasValue && intPOHubClientNo.Value > 0) ? intPOHubClientNo.Value : SessionManager.ClientID));
                for (int i = 0; i < lst.Count; i++) {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", lst[i].ShipViaId);
                    jsnItem.AddVariable("Name", lst[i].ShipViaName);
                    jsnList.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                lst.Clear(); lst = null;
                jsn.AddVariable("ShipVias", jsnList);
                jsnList.Dispose(); jsnList = null;
                CacheManager.StoreDropDown(_objDropDown.ID, strOptions, jsn.Result, CacheManager.CacheExpiryType.OneWorkingDay);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            } else {
                _context.Response.Write(strCachedData);
            }
            strCachedData = null;
        }
    }
}
