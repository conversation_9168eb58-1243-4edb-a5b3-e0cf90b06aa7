///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");

Rebound.GlobalTrader.Site.Pages.Setup.Sec_Groups = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Setup.Sec_Groups.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Setup.Sec_Groups.prototype = {

    get_ctlSecurityGroups: function() { return this._ctlSecurityGroups; }, set_ctlSecurityGroups: function(v) { if (this._ctlSecurityGroups !== v) this._ctlSecurityGroups = v; },
    get_ctlSecurityGroupMembers: function() { return this._ctlSecurityGroupMembers; }, set_ctlSecurityGroupMembers: function(v) { if (this._ctlSecurityGroupMembers !== v) this._ctlSecurityGroupMembers = v; },
    get_ctlSecurityGroupPermissionsGeneral: function() { return this._ctlSecurityGroupPermissionsGeneral; }, set_ctlSecurityGroupPermissionsGeneral: function(v) { if (this._ctlSecurityGroupPermissionsGeneral !== v) this._ctlSecurityGroupPermissionsGeneral = v; },
    get_ctlSecurityGroupPermissionsContact: function() { return this._ctlSecurityGroupPermissionsContact; }, set_ctlSecurityGroupPermissionsContact: function(v) { if (this._ctlSecurityGroupPermissionsContact !== v) this._ctlSecurityGroupPermissionsContact = v; },
    get_ctlSecurityGroupPermissionsOrders: function() { return this._ctlSecurityGroupPermissionsOrders; }, set_ctlSecurityGroupPermissionsOrders: function(v) { if (this._ctlSecurityGroupPermissionsOrders !== v) this._ctlSecurityGroupPermissionsOrders = v; },
    get_ctlSecurityGroupPermissionsWarehouse: function() { return this._ctlSecurityGroupPermissionsWarehouse; }, set_ctlSecurityGroupPermissionsWarehouse: function(v) { if (this._ctlSecurityGroupPermissionsWarehouse !== v) this._ctlSecurityGroupPermissionsWarehouse = v; },
    get_ctlSecurityGroupPermissionsSetup: function() { return this._ctlSecurityGroupPermissionsSetup; }, set_ctlSecurityGroupPermissionsSetup: function(v) { if (this._ctlSecurityGroupPermissionsSetup !== v) this._ctlSecurityGroupPermissionsSetup = v; },
    get_ctlSecurityGroupPermissionsReports: function() { return this._ctlSecurityGroupPermissionsReports; }, set_ctlSecurityGroupPermissionsReports: function(v) { if (this._ctlSecurityGroupPermissionsReports !== v) this._ctlSecurityGroupPermissionsReports = v; },
    get_ctlTabSecurity: function () { return this._ctlTabSecurity; }, set_ctlTabSecurity: function (v) { if (this._ctlTabSecurity !== v) this._ctlTabSecurity = v; },
    get_ctlSecurityGroupPermissionsUtility: function () { return this._ctlSecurityGroupPermissionsUtility; }, set_ctlSecurityGroupPermissionsUtility: function (v) { if (this._ctlSecurityGroupPermissionsUtility !== v) this._ctlSecurityGroupPermissionsUtility = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.Setup.Sec_Groups.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        this._ctlSecurityGroups.addSelectGroup(Function.createDelegate(this, this.ctlSecurityGroups_SelectGroup));
        this._ctlSecurityGroupMembers.addSaveEditComplete(Function.createDelegate(this, this.ctlSecurityGroupMembers_SaveEditComplete));
        this._ctlSecurityGroups.addDeleteComplete(Function.createDelegate(this, this.ctlSecurityGroupMembers_DeleteComplete));
        Rebound.GlobalTrader.Site.Pages.Setup.Sec_Groups.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlSecurityGroups) this._ctlSecurityGroups.dispose();
        if (this._ctlSecurityGroupMembers) this._ctlSecurityGroupMembers.dispose();
        if (this._ctlSecurityGroupPermissionsGeneral) this._ctlSecurityGroupPermissionsGeneral.dispose();
        if (this._ctlSecurityGroupPermissionsContact) this._ctlSecurityGroupPermissionsContact.dispose();
        if (this._ctlSecurityGroupPermissionsOrders) this._ctlSecurityGroupPermissionsOrders.dispose();
        if (this._ctlSecurityGroupPermissionsWarehouse) this._ctlSecurityGroupPermissionsWarehouse.dispose();
        if (this._ctlSecurityGroupPermissionsSetup) this._ctlSecurityGroupPermissionsSetup.dispose();
        if (this._ctlSecurityGroupPermissionsReports) this._ctlSecurityGroupPermissionsReports.dispose();
        if (this._ctlSecurityGroupPermissionsUtility) this._ctlSecurityGroupPermissionsUtility.dispose();
        if (this._ctlTabSecurity) this._ctlTabSecurity.dispose();
        this._ctlSecurityGroups = null;
        this._ctlSecurityGroupMembers = null;
        this._ctlSecurityGroupPermissionsGeneral = null;
        this._ctlSecurityGroupPermissionsContact = null;
        this._ctlSecurityGroupPermissionsOrders = null;
        this._ctlSecurityGroupPermissionsWarehouse = null;
        this._ctlSecurityGroupPermissionsSetup = null;
        this._ctlSecurityGroupPermissionsReports = null;
        this._ctlTabSecurity = null;
        this._ctlSecurityGroupPermissionsUtility = null;
        Rebound.GlobalTrader.Site.Pages.Setup.Sec_Groups.callBaseMethod(this, "dispose");
    },

    ctlSecurityGroups_SelectGroup: function() {
        this._ctlSecurityGroupMembers._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlSecurityGroupMembers.refresh();
        this._ctlSecurityGroupPermissionsGeneral._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlSecurityGroupPermissionsGeneral.refresh();
        this._ctlSecurityGroupPermissionsContact._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlSecurityGroupPermissionsContact.refresh();
        this._ctlSecurityGroupPermissionsOrders._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlSecurityGroupPermissionsOrders.refresh();
        this._ctlSecurityGroupPermissionsWarehouse._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlSecurityGroupPermissionsWarehouse.refresh();
        this._ctlSecurityGroupPermissionsSetup._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlSecurityGroupPermissionsSetup.refresh();
        this._ctlSecurityGroupPermissionsReports._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlSecurityGroupPermissionsReports.refresh();
        this._ctlTabSecurity._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlTabSecurity.getData();

        this._ctlSecurityGroupPermissionsUtility._intSecurityGroupID = this._ctlSecurityGroups._intSecurityGroupID;
        this._ctlSecurityGroupPermissionsUtility.refresh();
        this._ctlSecurityGroups._tbl.resizeColumns();
        this.showNuggets(true);
    },

    ctlSecurityGroupMembers_SaveEditComplete: function() {
        this._ctlSecurityGroups.refresh();
    },

    ctlSecurityGroupMembers_DeleteComplete: function() {
        this.showNuggets(false);
    },

    showNuggets: function(bln) {
        this._ctlSecurityGroupMembers.show(bln);
        this._ctlSecurityGroupPermissionsGeneral.show(bln);
        this._ctlSecurityGroupPermissionsContact.show(bln);
        this._ctlSecurityGroupPermissionsOrders.show(bln);
        this._ctlSecurityGroupPermissionsWarehouse.show(bln);
        this._ctlSecurityGroupPermissionsReports.show(bln);
        this._ctlSecurityGroupPermissionsSetup.show(bln);
        this._ctlTabSecurity.show(bln);
        this._ctlSecurityGroupPermissionsUtility.show(bln);
    }

};

Rebound.GlobalTrader.Site.Pages.Setup.Sec_Groups.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.Sec_Groups", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
