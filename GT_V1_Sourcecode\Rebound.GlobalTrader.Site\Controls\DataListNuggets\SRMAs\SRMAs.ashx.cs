/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class SRMAs : Base {


		protected override void GetData() {

			//check view level
			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

			List<SupplierRmaLine> lst = SupplierRmaLine.DataListNugget(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				 //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
                // , GetFormValue_StringForNameSearch("Contact")
                , GetFormValue_StringForNameSearchDecode("Contact")
                //, GetFormValue_StringForNameSearch("CMName")
                 , GetFormValue_StringForNameSearchDecode("CMName")
				, GetFormValue_NullableInt("Buyer")
				, GetFormValue_StringForSearch("SRMANotes")
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
				, GetFormValue_NullableInt("SRMANoLo")
				, GetFormValue_NullableInt("SRMANoHi")
				, GetFormValue_NullableDateTime("SRMADateFrom")
				, GetFormValue_NullableDateTime("SRMADateTo")
                , GetFormValue_Boolean("IncludeShipped")
                , GetFormValue_Boolean("RecentOnly")
                 , SessionManager.IsPOHub == true ? GetFormValue_Boolean("PohubOnly") : false
                 ,null
                 ,false
           );

			JsonObject jsn = new JsonObject();
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				if (i < lst.Count) {
					JsonObject jsnRow = new JsonObject();
					jsnRow.AddVariable("ID", lst[i].SupplierRMAId);
					jsnRow.AddVariable("No", lst[i].SupplierRMANumber);
					jsnRow.AddVariable("Part", lst[i].Part);
					//jsnRow.AddVariable("Price", Functions.FormatCurrency(lst[i].p, lst[i].CurrencyCode));
					jsnRow.AddVariable("Quantity", Functions.FormatNumeric(lst[i].Quantity));
					jsnRow.AddVariable("Date", Functions.FormatDate(lst[i].SupplierRMADate));
                    if (lst[i].InternalPurchaseOrderId.HasValue && lst[i].InternalPurchaseOrderId.Value > 0)
                    {
                        //jsnRow.AddVariable("CM", lst[i].IPOCompanyName);
                        //jsnRow.AddVariable("CMNo", lst[i].IPOCompanyNo);
                        jsnRow.AddVariable("CM", SessionManager.IsPOHub.Value ? lst[i].CompanyName : lst[i].IPOCompanyName);
                        jsnRow.AddVariable("CMNo", SessionManager.IsPOHub.Value ? lst[i].CompanyNo : lst[i].IPOCompanyNo);
                    }
                    else
                    {
                        jsnRow.AddVariable("CM", lst[i].CompanyName);
                        jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
                    }

					jsnRow.AddVariable("PO", lst[i].PurchaseOrderNumber);
					jsnRow.AddVariable("PONo", lst[i].PurchaseOrderNo);
					jsnRow.AddVariable("Contact", lst[i].ContactName);
					jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
					jsnRow.AddVariable("ROHS", lst[i].ROHS);
					jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
					jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
					jsnRowsArray.AddVariable(jsnRow);
					jsnRow.Dispose();
					jsnRow = null;
				}
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			lst = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
            //Prevent filter state for Tab
			//AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("Part");
			AddFilterState("Contact");
			AddFilterState("CMName");
			AddFilterState("Buyer");
			AddFilterState("SRMANotes");
			AddFilterState("PONo");
			AddFilterState("SRMANo");
			AddFilterState("SRMADateFrom");
			AddFilterState("SRMADateTo");
            AddFilterState("IncludeShipped");
            AddFilterState("RecentOnly");
            AddFilterState("PohubOnly");
            base.AddFilterStates();
		}
	}
}
