using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets
{
    public partial class GILineNotify : Base
    {

        #region Locals
        protected IconButton ibtnAdd;

        #endregion

        #region Properties
        private bool _blnCanEditShipInCost = true;
        public bool CanEditShipInCost
        {
            get { return _blnCanEditShipInCost; }
            set { _blnCanEditShipInCost = value; }
        }
        private bool _blnCanEditPurchasePrice = true;
        public bool CanEditPurchasePrice
        {
            get { return _blnCanEditPurchasePrice; }
            set { _blnCanEditPurchasePrice = value; }
        }
        #endregion

        #region Overrides

        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            AddScriptReference("Controls.Nuggets.GILineNotify.GILineNotify.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "GILineNotify");
            ibtnAdd = FindIconButton("ibtnAdd");
        }

        protected override void OnLoad(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnLoad(e);
        }

        protected override void OnPreRender(EventArgs e)
        {
            base.OnPreRender(e);
        }

        #endregion

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.GILineNotify", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnAdd", ibtnAdd.ClientID);
            _scScriptControlDescriptor.AddProperty("blnCanEditShipInCost", _blnCanEditShipInCost);
            _scScriptControlDescriptor.AddProperty("blnCanEditPurchasePrice", _blnCanEditPurchasePrice);
        }
    }
}
