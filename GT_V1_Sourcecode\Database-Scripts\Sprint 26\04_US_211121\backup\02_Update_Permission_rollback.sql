﻿/*===========================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-211121]     NgaiTo		 18-Apr-2025			CREATE		211121: PPV/BOM qualification needs moving on the setup screen: rollback permission
===========================================================================================  
*/

IF EXISTS (
		SELECT TOP 1 1
		FROM tbSecurityFunction
		WHERE SecurityFunctionId = 6011604
			AND FunctionName = 'Setup_GlobalSettings_PPVBOMQualification'
		)
BEGIN
	--update security function
	UPDATE tbSecurityFunction
	SET [FunctionName] = 'Setup_CompanyDetails_PPVBOMQualification',
		[Description] = 'Setup CompanyDetails PPVBOMQualification'
	WHERE SecurityFunctionId = 6011604
		AND FunctionName = 'Setup_GlobalSettings_PPVBOMQualification'
END

IF EXISTS (
		SELECT TOP 1 1
		FROM tbSecurityFunction
		WHERE SecurityFunctionId = 6011605
			AND FunctionName = 'Setup_GlobalSettings_PPVBOMQualification_Add'
		)
BEGIN
	--update security function
	UPDATE tbSecurityFunction
	SET [FunctionName] = 'Setup_CompanySettings_PPVBOMQualification_Add',
		[Description] = 'Setup_CompanySettings_PPVBOMQualification_Add'
	WHERE SecurityFunctionId = 6011605
		AND FunctionName = 'Setup_GlobalSettings_PPVBOMQualification_Add'
END

IF EXISTS (
		SELECT TOP 1 1
		FROM tbSecurityFunction
		WHERE SecurityFunctionId = 6011606
			AND FunctionName = 'Setup_GlobalSettings_PPVBOMQualification_Edit'
		)
BEGIN
	--update security function
	UPDATE tbSecurityFunction
	SET [FunctionName] = 'Setup_CompanySettings_PPVBOMQualification_Edit',
		[Description] = 'Setup_CompanySettings_PPVBOMQualification_Edit'
	WHERE SecurityFunctionId = 6011606
		AND FunctionName = 'Setup_GlobalSettings_PPVBOMQualification_Edit'
END
