Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.TermsWarning=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.TermsWarning.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.TermsWarning.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.TermsWarning.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.TermsWarning.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/TermsWarning");this._objData.set_DataObject("TermsWarning");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Types)for(n=0;n<t.Types.length;n++)this.addOption(t.Types[n].Name,t.Types[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.TermsWarning.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.TermsWarning",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);