<%--
Marker     changed by      date         Remarks
--%>
<%@ Control Language="C#" CodeBehind="BOMAdd_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMAdd_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<link href="css/uploadfile.css" rel="stylesheet">
<link href="css/BOMImport.css" rel="stylesheet" />
<script src="js/jquery.uploadfile.js" ></script>
<script src="js/jquery.dataTables.min.js" ></script>
<link href="css/jquery-ui.css.css" rel="stylesheet" />
<script src="js/jquery-ui.js"></script>

<style>
	#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_pnlContentInner{
		display: flex;
	}

	#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_frm1{
		margin-top: -25px;
		height: fit-content;
	}

	#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_frm1 td{
		height: 22px;
	}
	.spanBorder{
		color: #c1cbce;
	}
	#removeUploadedFile{
		color: red; 
		font-weight:bold;
		text-decoration:none;
	}

	#uploadedFile .title{
		font-weight: bold;
		color: #d3ffcc;
		margin-right: 74px;
	}

	#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_frm{
		margin-right: 10px;
	}

	#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ctlBuyer_tdTitle,
	#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_ctlSendMailMessage_ctlTo_tdTitle{
		width: 130px;
	}

	#divLoader{
		text-align: center;
		width: 100%;
		height: 100%;
		position: absolute;
		z-index: 999;
		background:rgba(0,0,0,.4);
		border-radius: 5px;
	}
	#divLoader div {
	    width: 244px;
	    height: 49px;
	    line-height: 49px;
	    text-align: center;
	    position: absolute;
	    left: 40%;
	    top: 50%;
	    font-family: helvetica, arial, sans-serif;
	    text-transform: uppercase;
	    font-weight: 900;
	    font-size: 14px;
	    color: rgb(206,66,51);
	    letter-spacing: 0.2em;
	    background-color: aliceblue;
	}
</style>
	<div id="divLoader" style="display:none;">
        <div>Loading..</div>
    </div>

<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
    <Links>
        <ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
        <ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>

    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMAdd_Add")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">			
			<ReboundUI_Form:FormField id="ctlName" runat="server" FieldID="txtName" ResourceTitle="Name" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtName" runat="server" Width="250"  MaxLength="50"/></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlCompany" runat="server" FieldID="cmbCustomer" ResourceTitle="Company" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbCustomer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="AllSaleCompanies" /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlContact" runat="server" FieldID="ddlContact" ResourceTitle="Contact" IsRequiredField="true">
				<Field><ReboundDropDown:ContactsForCompany ID="ddlContact" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField ID="ctlCurrency" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency"  IsRequiredField="true">                           
            <Field> <ReboundDropDown:SellCurrency ID="ddlCurrency" runat="server" Visible="true" /> </Field>
             </ReboundUI_Form:FormField>
             <ReboundUI_Form:FormField id="ctlCurrentSupplier" runat="server" FieldID="txtCurrentSupplier" ResourceTitle="CurrentSupplier">
				<Field><ReboundUI:ReboundTextBox ID="txtCurrentSupplier" runat="server" Width="250"  MaxLength="50"/></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlQuoteRequired" runat="server" FieldID="txtQuoteRequired" ResourceTitle="QuoteRequired" IsRequiredField="true">
	            <Field>
		            <ReboundUI:ReboundTextBox ID="txtQuoteRequired" runat="server" Width="150" />
		            <ReboundUI:Calendar ID="calQuoteRequired" runat="server" RelatedTextBoxID="txtQuoteRequired" />
	            </Field>	            
            </ReboundUI_Form:FormField>
                        
            <ReboundUI_Form:FormField id="ctlAS9120" runat="server" FieldID="chkAS9120" ResourceTitle="AS9120">
				<Field><ReboundUI:ImageCheckBox ID="chkAS9120" runat="server" Enabled="True"  /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlInActive" runat="server" FieldID="chkInActive" ResourceTitle="IsInactive">
				<Field><ReboundUI:ImageCheckBox ID="chkInActive" runat="server" Enabled="true" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="450" TextMode="MultiLine" Rows="2" /></Field>
			</ReboundUI_Form:FormField>	
             <asp:TableRow >               
				<asp:TableCell id="TableCell2" ColumnSpan="2" style="border-bottom:1px dotted #CCCCCC;margin-top:10px;" ></asp:TableCell>
			</asp:TableRow> 
            
			<asp:TableRow >               
				<asp:TableCell id="TableCell1" ColumnSpan="2" style="border-bottom:1px dotted #CCCCCC;margin-top:10px;" >
					<asp:Label  ID="lblMessage" runat="server" Text="Optional: Select an additional salesperson to receive HUBRFQ communication notes from Purchase Hub" Font-Bold="true"  color="White"/>
				</asp:TableCell>
			</asp:TableRow>

            <ReboundUI_Form:FormField id="ctlSalesman" runat="server" FieldID="ddlSalesman" ResourceTitle="Contact2">
				<Field><ReboundDropDown:Employee ID="ddlSalesman" runat="server" /></Field>
			</ReboundUI_Form:FormField>		
		</ReboundUI_Table:Form>
		<ReboundUI_Table:Form ID="frm1" runat="server">
			<asp:TableRow>
				<asp:TableCell ColumnSpan="2">
					<asp:Label runat="server" ID="lblAttachBOM" Text="ATTACH BOM FILE" Font-Bold="true"></asp:Label>
				</asp:TableCell>
			</asp:TableRow>
			<asp:TableRow>
				<asp:TableCell ColumnSpan="2">
					<asp:Label runat="server" ID="lblAttachNotes" Text="Please note the attached BOM file will be validated and saved as the new associated Requirements of HUBRFQ" ></asp:Label>
				</asp:TableCell>
			</asp:TableRow>
			<asp:TableRow>
				<asp:TableCell ColumnSpan="2">
					<div class="lableopt" style="width: 250px;">
						<div id="Imagesingleupload4" >Upload</div>
					</div>
					<div style="text-align: right; margin-top:-45px;">
						<p title="Download BOM Template">
							<a href="User/EpoTemplate/BOMImportTemplate.xlsx" style="font-size: 13px; color: white; font-weight:bold">Download BOM Template</a>
						</p>
					</div>
					<script>
                        $(function () {
                            formControlId = "<%=this.ClientID%>";
                            loginId = "<%=SessionManager.LoginID%>";
                            var dragdropObj = $("#Imagesingleupload4").uploadFile({
                                url: "DocImage.ashx?mxs=1&type=EXCELUPLOAD&IsDragDrop=true",
                                allowedTypes: "csv,xlsx,xls",
                                fileName: "myfile",
                                section: "UtilityBOM_BOMImport_STKI",
                                autoSubmit: true,
                                multiple: false,
                                maxFileSize: 7900000,
                                showStatusAfterSuccess: false,
                                showCancel: true,
                                showDone: true,
                                async: false,
                                uploadDiv: "excelipload",
                                timeout: 60000,
                                dynamicFormData: function () {
                                    var data = { section: "UtilityBOM_BOMImport_STKI" }
								    return data;
								},
								onSuccess: function (files, data, xhr) {
								    var originalFilename = '';
								    var generatedFilename = '';
								    originalFilename = files[0];
								    var json = Sys.Serialization.JavaScriptSerializer.deserialize(data);
									generatedFilename = json.FileName;
									$("#excelipload").prop('disabled', true).css('opacity', 0.5);
                                    $('input:file').filter(function () {
                                        return this.files.length == 0
                                    }).prop('disabled', true);
                                    $find("<%=this.ClientID%>").importExcelData(originalFilename, generatedFilename);
								},
								onSelect: function (fup) {
								    var result = true;
								    $find("<%=this.ClientID%>")._dragobj = dragdropObj;
								    return result;
								}
							});
						});
                    </script>
                </asp:TableCell>
			</asp:TableRow>
			<asp:TableRow>
				<asp:TableCell ColumnSpan="2">
					<div id="uploadedFile" style="display:none">
						<label class="title" style="font-weight: bold">Upload File</label>
						<label id="uploadedFileName"></label>
						<a id="removeUploadedFile" href="javascript:void(0)">X</a>
					</div>
				</asp:TableCell>
			</asp:TableRow>
			<%--BEGIN new field for send to purchase hub purpose, show when BOM file attached--%>
			<ReboundUI_Form:FormField id="ctlBuyer" runat="server" FieldID="ddlBuyer" ResourceTitle="Buyer">
				<Field><ReboundDropDown:PoHubBuyer ID="ddlBuyer" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundFormFieldCollection:NotifyMailMessageReqToHUB id="ctlSendMailMessage" runat="server"/>
			<%--END new field for send to purchase hub purpose, show when BOM file attached--%>
			<asp:TableRow>
                <asp:TableCell ColumnSpan="2">
                    <div class="box boxStandard">
                        <div class="boxInner">
                            <div class="boxTL"></div>
                            <div class="boxTR"></div>
                            <div class="boxHeader">
                                <div class="boxHeaderInner">
                                    <h4>
                                        <div class="loadingOuter">
                                            <%--<div class="loading"></div>--%>
                                        </div>
                                        <span>PPV/ BOM Qualification</span></h4>
                                    <div class="boxLinks">
										<reboundui:iconbutton id="ibtnEditPPV" runat="server" iconbuttonmode="hyperlink" icongroup="Nugget"
											icontitleresource="AddEdit" iconcsstype="Edit" />
										<reboundui:iconbutton id="ibtnDeletePPV" runat="server" iconbuttonmode="hyperlink" icongroup="Nugget"
										    icontitleresource="Delete" iconcsstype="Delete" />
										
										<reboundui:iconbutton id="ibtnViewPPV" runat="server" iconbuttonmode="hyperlink" icongroup="Nugget"
										    icontitleresource="View" iconcsstype="Add" />
                                    </div>
                                </div>
                            </div>
                            <div class ="boxContent">
								<reboundui:flexidatatable id="tblPVVBOM" runat="server" panelheight="100" />
                            </div>
                            <div class="boxBL"></div>
                            <div class="boxBR"></div>
                        </div>
                    </div>
                </asp:TableCell>
            </asp:TableRow>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>

