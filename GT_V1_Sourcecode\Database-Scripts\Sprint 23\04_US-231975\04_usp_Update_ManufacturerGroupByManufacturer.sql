﻿
GO


SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
============================================================================================================================ 
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-231975]     Phuc Hoang		 05-Mar-2025		CREATE		IPO - MFR Group Code Franchised Tick Box Further Enhancement
============================================================================================================================  
*/

CREATE OR ALTER PROC [dbo].[usp_Update_ManufacturerGroupByManufacturer] (
	@ManufacturerNo INT = NULL  
	,@ClientNo INT = 0 
	,@UpdatedBy INT = NULL
	,@RowsAffected INT = 0 OUTPUT
)
AS

BEGIN
	UPDATE ml
	SET ml.IsFranchised = 1, 
		ml.UpdatedBy = @UpdatedBy,
		ml.DLUP = GETDATE()
	FROM  dbo.tbManufacturerLink ml    
	JOIN dbo.tbManufacturer mfr ON ml.ManufacturerNo = mfr.ManufacturerId    
	JOIN dbo.tbCompany com ON ml.SupplierCompanyNo = com.CompanyId  
	WHERE ml.ManufacturerNo = @ManufacturerNo 
		AND com.ClientNo = @ClientNo
		AND ISNULL(com.Inactive, 0) = 0;

	SELECT @RowsAffected = @@ROWCOUNT;  
END
