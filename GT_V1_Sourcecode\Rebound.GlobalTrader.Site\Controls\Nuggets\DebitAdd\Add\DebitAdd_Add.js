Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add=function(n){Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.initializeBase(this,[n]);this._intNewID=0;this._intCompanyID=0;this._intLoginID=0;this._intContactID=0;this._intDivisionID=0;this._intPOID=0;this._intSRMAID=0;this._intBuyerID=0;this._intCurrencyID=0;this._intTaxID=0;this._strCompanyName="";this._strContactName="";this._strSearchCompanyName=""};Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.prototype={get_ctlSelectPO:function(){return this._ctlSelectPO},set_ctlSelectPO:function(n){this._ctlSelectPO!==n&&(this._ctlSelectPO=n)},get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},get_strCompanyName:function(){return this._strCompanyName},set_strCompanyName:function(n){this._strCompanyName!==n&&(this._strCompanyName=n)},get_intLoginID:function(){return this._intLoginID},set_intLoginID:function(n){this._intLoginID!==n&&(this._intLoginID=n)},get_intContactID:function(){return this._intContactID},set_intContactID:function(n){this._intContactID!==n&&(this._intContactID=n)},get_strContactName:function(){return this._strContactName},set_strContactName:function(n){this._strContactName!==n&&(this._strContactName=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},get_lblCurrency_Freight:function(){return this._lblCurrency_Freight},set_lblCurrency_Freight:function(n){this._lblCurrency_Freight!==n&&(this._lblCurrency_Freight=n)},get_strSearchCompanyName:function(){return this._strSearchCompanyName},set_strSearchCompanyName:function(n){this._strSearchCompanyName!==n&&(this._strSearchCompanyName=n)},get_intQSPurchaseOrderID:function(){return this._intQSPurchaseOrderID},set_intQSPurchaseOrderID:function(n){this._intQSPurchaseOrderID!==n&&(this._intQSPurchaseOrderID=n)},initialize:function(){var n,t;Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.callBaseMethod(this,"initialize");this._ctlMail=$find(this.getField("ctlSendMailMessage").ID);this._ctlMail._ctlRelatedForm=this;this.addCancel(Function.createDelegate(this,this.cancelClicked));this.addSave(Function.createDelegate(this,this.saveClicked));this.addShown(Function.createDelegate(this,this.formShown));this._ctlMultiStep.addStepChanged(Function.createDelegate(this,this.stepChanged));this._ctlSelectPO.addItemSelected(Function.createDelegate(this,this.selectPO));this.addFieldCheckBoxClickEvent("ctlSendMail",Function.createDelegate(this,this.chooseIfSendMail));n=Function.createDelegate(this,this.finishedForm);$R_IBTN.addClick(this._ibtnContinue,n);$R_IBTN.addClick(this._ibtnContinue_Footer,n);t=Function.createDelegate(this,this.sendMail);$R_IBTN.addClick(this._ibtnSend,t);$R_IBTN.addClick(this._ibtnSend_Footer,t);this.getFieldDropDownData("ctlIncoterm");this._intQSPurchaseOrderID!=null&&this._intQSPurchaseOrderID>0&&this.getPOFromPODetail()},dispose:function(){this.isDisposed||(this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ctlSelectPO&&this._ctlSelectPO.dispose(),this._ctlSelectPO=null,this._intCompanyID=null,this._strCompanyName=null,this._intLoginID=null,this._intContactID=null,this._strContactName=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._lblCurrency_Freight=null,this._strSearchCompanyName=null,this._intLoginID=null,this._intContactID=null,this._intDivisionID=null,this._intPOID=null,this._intSRMAID=null,this._intBuyerID=null,this._intCurrencyID=null,this._intTaxID=null,this._intQSPurchaseOrderID=null,Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.callBaseMethod(this,"dispose"))},formShown:function(){this.resetSteps();this._intCompanyID>0?this.doInitialCompanySearch():this._strSearchCompanyName&&(this._ctlSelectPO.setFieldValue("ctlCompany",this._strSearchCompanyName),this._ctlSelectPO.searchClicked());this._intQSPurchaseOrderID!=null&&this._intQSPurchaseOrderID>0?this.gotoStep(2):this.gotoStep(1)},doInitialCompanySearch:function(){this._ctlSelectPO._initialized||setTimeout(Function.createDelegate(this,this.doInitialCompanySearch),100);this._ctlSelectPO.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(this._strCompanyName));this._ctlSelectPO.setFieldValue("ctlContact",$R_FN.setCleanTextValue(this._strContactName));this._ctlSelectPO.getData()},getPO:function(){this.showPOFieldsLoading(!0);$R_FN.showElement(this._pnlLines,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/POMainInfo");n.set_DataObject("POMainInfo");n.set_DataAction("GetData");n.addParameter("ID",this._intPOID);n.addDataOK(Function.createDelegate(this,this.getPOOK));n.addError(Function.createDelegate(this,this.getPOError));n.addTimeout(Function.createDelegate(this,this.getPOError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getPOOK:function(n){var t=n._result;this.setFieldValue("ctlCompany",$R_FN.setCleanTextValue(t.SupplierName));this.setFieldValue("ctlPONumber",t.PONumber);this.setFieldValue("ctlContact",$R_FN.setCleanTextValue(t.Contact));this.setFieldValue("ctlBuyer",$R_FN.setCleanTextValue(t.Buyer));this.setFieldValue("ctlCurrency",$R_FN.setCleanTextValue(t.Currency));this.setFieldValue("ctlTax",$R_FN.setCleanTextValue(t.Tax));this.setFieldValue("ctlDivision",$R_FN.setCleanTextValue(t.Division));this.setFieldValue("ctlReferenceDate",t.DateOrdered);this.setFieldValue("ctlRaisedBy",this._intLoginID);this.setFieldValue("ctlFreight","0.00");this.setFieldValue("ctlIncoterm",t.IncotermNo);this.getFieldDropDownData("ctlRaisedBy");$R_FN.setInnerHTML(this._lblCurrency_Freight,t.CurrencyCode);this._intCompanyID=t.SupplierNo;this._intContactID=t.ContactNo;this._intDivisionID=t.DivisionNo;this._intTaxID=t.TaxNo;this._intBuyerID=t.BuyerNo;this._intCurrencyID=t.CurrencyNo;this.showPOFieldsLoading(!1)},getPOError:function(n){this.showPOFieldsLoading(!1);this._strErrorMessage=n._errorMessage;this.onSaveError()},showPOFieldsLoading:function(n){this.showFieldLoading("ctlCompany",n);this.showFieldLoading("ctlPONumber",n);this.showFieldLoading("ctlContact",n);this.showFieldLoading("ctlBuyer",n);this.showFieldLoading("ctlCurrency",n);this.showFieldLoading("ctlTax",n);this.showFieldLoading("ctlDivision",n);this.showFieldLoading("ctlReferenceDate",n);this.showFieldLoading("ctlRaisedBy",n)},cancelClicked:function(){$R_FN.navigateBack()},stepChanged:function(){var n=this._ctlMultiStep._intCurrentStep;$R_IBTN.showButton(this._ibtnSend,n==3);$R_IBTN.showButton(this._ibtnSend_Footer,n==3);$R_IBTN.enableButton(this._ibtnSave,n==2);$R_IBTN.enableButton(this._ibtnSave_Footer,n==2);$R_IBTN.showButton(this._ibtnSave,n!=3);$R_IBTN.showButton(this._ibtnSave_Footer,n!=3);$R_IBTN.showButton(this._ibtnCancel,n!=3);$R_IBTN.showButton(this._ibtnCancel_Footer,n!=3);$R_IBTN.showButton(this._ibtnContinue,n==3);$R_IBTN.showButton(this._ibtnContinue_Footer,n==3);n==1&&this._ctlSelectPO.resizeColumns();this._ctlMultiStep.showSteps(n!=3);n==3&&(this.getMessageText(),this.setFieldValue("ctlSendMail",!1),this.showMailButtons())},selectPO:function(){this._intPOID=this._ctlSelectPO.getSelectedID();this.getPO();this.nextStep()},saveClicked:function(){if(this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/DebitAdd");n.set_DataObject("DebitAdd");n.set_DataAction("AddNew");n.addParameter("RaisedBy",this.getFieldValue("ctlRaisedBy"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("Instructions",this.getFieldValue("ctlInstructions"));n.addParameter("DebitDate",$R_FN.shortDate());n.addParameter("ReferenceDate",this.getFieldValue("ctlReferenceDate"));n.addParameter("Freight",this.getFieldValue("ctlFreight"));n.addParameter("TaxNo",this._intTaxID);n.addParameter("PurchaseOrderNo",this._intPOID);n.addParameter("SupplierRMANo",this._intSRMAID);n.addParameter("Buyer",this._intBuyerID);n.addParameter("CurrencyNo",this._intCurrencyID);n.addParameter("CMNo",this._intCompanyID);n.addParameter("ContactNo",this._intContactID);n.addParameter("DivisionNo",this._intDivisionID);n.addParameter("SupplierInvoice",this.getFieldValue("ctlSupplierInvoice"));n.addParameter("SupplierReturn",this.getFieldValue("ctlSupplierReturn"));n.addParameter("SupplierCredit",this.getFieldValue("ctlSupplierCredit"));n.addParameter("IncotermNo",this.getFieldValue("ctlIncoterm"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.NewID>0?(this._intNewID=n._result.NewID,this.showSaving(!1),this.showInnerContent(!0),this.nextStep()):(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();var n=!0;return this._ctlMultiStep._intCurrentStep==2&&(this.checkFieldEntered("ctlRaisedBy")||(n=!1),this.checkFieldEntered("ctlIncoterm")||(n=!1)),this._ctlMultiStep._intCurrentStep==3&&(this._ctlMail.validateFields()||(n=!1)),n||this.showError(!0),n},showMailButtons:function(){var n=this.getFieldValue("ctlSendMail");this.showField("ctlSendMailMessage",n);$R_IBTN.showButton(this._ibtnSend,n);$R_IBTN.showButton(this._ibtnSend_Footer,n);$R_IBTN.showButton(this._ibtnContinue,!n);$R_IBTN.showButton(this._ibtnContinue_Footer,!n)},chooseIfSendMail:function(){this.showMailButtons()},getMessageText:function(){Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NewDebitNote(this._intNewID,Function.createDelegate(this,this.getMessageTextComplete))},getMessageTextComplete:function(n){this._ctlMail.setValue_Body(n);this._ctlMail.setValue_Subject($R_RES.NewDebitNoteAdded)},validateMailForm:function(){var n=this._ctlMail.validateFields();return n||this.showError(!0),n},sendMail:function(){this.validateMailForm()&&(Rebound.GlobalTrader.Site.WebServices.SendMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs),$R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs),this._ctlMail.getValue_Subject(),this._ctlMail.getValue_Body(),this._intNewID,Function.createDelegate(this,this.sendMailComplete)),$R_IBTN.showButton(this._ibtnSave,!1),$R_IBTN.showButton(this._ibtnSave_Footer,!1),$R_IBTN.showButton(this._ibtnSend,!1),$R_IBTN.showButton(this._ibtnSend_Footer,!1))},sendMailComplete:function(){this.finishedForm()},finishedForm:function(){this._ctlMultiStep.showExplainLabel(!1);this._ctlMultiStep.showSteps(!1);$R_IBTN.showButton(this._ibtnSave,!1);$R_IBTN.showButton(this._ibtnSave_Footer,!1);$R_IBTN.showButton(this._ibtnSend,!1);$R_IBTN.showButton(this._ibtnSend_Footer,!1);this.showSavedOK(!0);this.onSaveComplete()},getPOFromPODetail:function(){this._intPOID=this._intQSPurchaseOrderID;this.getPO();this.nextStep()}};Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.DebitAdd_Add",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);