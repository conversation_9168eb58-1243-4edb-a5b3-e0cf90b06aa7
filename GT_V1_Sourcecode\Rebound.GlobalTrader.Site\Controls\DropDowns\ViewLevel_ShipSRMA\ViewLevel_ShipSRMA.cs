﻿//-----------------------------------------------------------------------------------------
// RP 28.10.2009:
// - New dropdown
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
	public partial class ViewLevel_ShipSRMA : Base {

		protected override void OnInit(EventArgs e) {
			CanAddTo = false;
			IncludeNoValue = false;
			NoValue_Value = "0";
			InitialValue = "0";
			base.OnInit(e);
		}

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("ViewLevel_ShipSRMA");
            AddScriptReference("Controls.DropDowns.ViewLevel_ShipSRMA.ViewLevel_ShipSRMA");
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.ViewLevel_ShipSRMA", ClientID);
			base.OnLoad(e);
		}

	}
}