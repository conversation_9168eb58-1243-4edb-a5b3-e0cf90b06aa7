﻿/*
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-211441]		Trung Pham			29-SEP-2024		CREATE			UPDATE SCHEMA
===========================================================================================

*/ 
 IF (NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbBOM'
	AND COLUMN_NAME IN ('IsFromProspectiveOffer')))
	BEGIN
		ALTER TABLE tbBOM
		ADD IsFromProspectiveOffer BIT
	END
	
 IF (NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.COLUMNS
	WHERE TABLE_NAME = 'tbBOM'
	AND COLUMN_NAME IN ('ProspectiveOfferNo')))
	BEGIN
		ALTER TABLE tbBOM
		ADD ProspectiveOfferNo INT
	END