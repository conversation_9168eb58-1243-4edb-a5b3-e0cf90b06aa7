﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*       
===========================================================================================      
TASK			UPDATED BY		DATE			ACTION      DESCRIPTION     
[US-232285]		CuongDox		18-Apr-2025		Update		PO - Add ability to modify ECCN  
[BUG-243646]	AnTran			08-May-2025		Update		Convert Shipping cost to IPO base client currency  
[BUG-243646]	AnTran			22-May-2025		Update		Change logic convert ship in cost: use DMCC exchange rate 
===========================================================================================      
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_PurchaseOrderLine]
	--[RP-881]      Ravi            09/05/2023   RP-881(show notification if company purchaisng is 'on stop', stock will be quarantined and GI will not be released.)             
	@PurchaseOrderLineId INT
AS
DECLARE @SOLineECCNCode NVARCHAR(250)
	,@SOLineECCNId INT

SELECT @SOLineECCNCode = sol.ECCNCode
FROM tbStock sk
JOIN tbAllocation al ON sk.StockId = al.StockNo
JOIN tbSalesOrderLine sol ON sol.SalesOrderLineId = al.SalesOrderLineNo
LEFT JOIN tbpurchaseOrderLine pol ON sk.PurchaseOrderLineNo = pol.PurchaseOrderLineId
WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineId

SELECT @SOLineECCNId = ECCNId
FROM tbECCN
WHERE ECCNCode = @SOLineECCNCode

-- For EPR Start                                                      
DECLARE @vEPRIds VARCHAR(1000)
	,@IsAuthorised BIT

SELECT TOP 1 @IsAuthorised = CASE 
		WHEN isnull(E.Authorized, '') = ''
			THEN cast(0 AS BIT)
		ELSE cast(1 AS BIT)
		END
FROM tbEPR E
INNER JOIN tbPOLineEPR PE ON E.eprID = PE.eprNo
WHERE PE.POLineNo = @PurchaseOrderLineId
	AND isnull(E.Inactive, 0) = 0
	AND isnull(PE.Inactive, 0) = 0
	AND isnull(e.Authorized, '') != ''
ORDER BY DLUP DESC

IF OBJECT_ID('tempdb..#Results') IS NOT NULL
	DROP TABLE #Results

SELECT DISTINCT eprno
	,CreatedOn
INTO #Results
FROM tbPOLineEPR
WHERE POLineNo = @PurchaseOrderLineId
	AND isnull(Inactive, 0) = 0
ORDER BY CreatedOn DESC

SELECT @vEPRIds = COALESCE(@vEPRIds + ',', '') + Cast(eprno AS VARCHAR) FROM #Results

-- For EPR end                                            
DECLARE @ClientNo INT
	,@DefaultClientLotNo INT
	,@ClientCurrencyInDmcc INT
SELECT TOP 1 @ClientNo = CASE 
		WHEN ipo.InternalPurchaseOrderId IS NULL
			THEN po.ClientNo
		ELSE ipo.ClientNo
		END
FROM tbPurchaseOrder po
JOIN tbPurchaseOrderLine pol ON po.PurchaseOrderId = pol.PurchaseOrderNo
LEFT JOIN tbInternalPurchaseOrder ipo ON ipo.PurchaseOrderNo = po.PurchaseOrderId
JOIN tbClient c ON c.ClientId = po.ClientNo
WHERE pol.PurchaseOrderLineId = @PurchaseOrderLineId;

--get client base currency in DMCC for get exchange rate for ShipInCost
SELECT TOP 1 @ClientCurrencyInDmcc = c.CurrencyId
FROM tbCurrency c with(nolock)
JOIN tbCurrency cc WITH(NOLOCK) on cc.GlobalCurrencyNo = c.GlobalCurrencyNo
JOIN tbClient cl WITH(NOLOCK) on cl.CurrencyNo = cc.CurrencyId
WHEre c.ClientNo = 114 --DMCC ID
	AND c.Inactive = 0
	AND cl.ClientId = @ClientNo;

SELECT TOP 1 @DefaultClientLotNo = LotId
FROM tbLot
WHERE ClientNo = @ClientNo
	AND LotName = CONVERT(CHAR(2), getdate(), 101) + RIGHT(YEAR(GETDATE()), 2) + ' Rebound Electronics Stock ' + DateName(month, DateAdd(month, month(getdate()), 0) - 1) + ' ' + cast(year(getdate()) AS VARCHAR(5))

DECLARE @SOPrice FLOAT
DECLARE @SOCurrecny INT

---            
SELECT @SOPrice = isnull(isnull(sol.Price, 0) / dbo.ufn_get_exchange_rate(so.CurrencyNo, so.DateOrdered), 0)
FROM tbInternalPurchaseOrderLine a
JOIN tbSalesOrderLine sol ON a.SalesOrderLineNo = sol.SalesOrderLineId
JOIN tbsalesorder SO ON sol.SalesOrderNo = so.SalesOrderID
WHERE a.PurchaseOrderLineNo = @PurchaseOrderLineId

SELECT *
	,cu.CurrencyCode AS ClientCurrencyCode
	,(SELECT TOP 1 BOMName FROM tbbom WHERE BomId = BomNo) AS BOMName
	,@DefaultClientLotNo AS DefaultClientLotNo
	,dbo.ufn_get_productdutyrate(ProductNo, getdate()) AS ProductDutyRate --   (Rate %) on form   Purchase Order                                       
	,@vEPRIds AS 'EPRIds'
	,@IsAuthorised AS IsAuthorised
	,CASE 
		WHEN ISNULL(a.ReleaseBy, '') = ''
			THEN CAST(0 AS BIT)
		ELSE CAST(1 AS BIT)
		END AS IsReleased
	,a.OriginalDeliveryDate
	,isnull(@SOPrice, 0) AS SOPrice
	,isnull((a.ClientPrice * a.Quantity), 0) AS IpoLineTotal
	--,isnull(ROUND(CAST( ((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))-(isnull(a.ClientPrice,0) * a.Quantity) as float),2),0)as LineProfit                  
	,isnull(ROUND(CAST(((isnull(@SOPrice, 0) * a.Quantity) * dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate)) - (isnull(a.ClientPrice, 0) * a.Quantity) * dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate) AS FLOAT), 2), 0) AS LineProfit
	,CASE 
		WHEN isnull(@SOPrice * a.Quantity, 0) = 0
			THEN 0
		ELSE
			--isnull(ROUND(CAST(( ((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))-          
			--(isnull(a.ClientPrice,0) * a.Quantity)) * 100 / (((isnull(@SOPrice,0) * a.Quantity)* dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))) AS FLOAT), 2),0) end AS LineProfitPercentage           
			isnull(ROUND(CAST((((isnull(@SOPrice, 0) * a.Quantity) * dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate)) - (isnull(a.ClientPrice, 0) * a.Quantity) * dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate)) * 100 / (((isnull(@SOPrice, 0) * a.Quantity) * dbo.ufn_get_exchange_rate(a.CurrencyNo, a.PurchaseRequestDate))) AS FLOAT), 2), 0)
		END AS LineProfitPercentage
	,cast(dbo.ufn_GetECCNMessage(a.ECCNCode, a.ClientNo) AS NVARCHAR(900)) AS IHSECCNCodeDefination
	-- [RP-881] start            
	,(
		SELECT CASE 
				WHEN c.SupplierOnStop = 1
					THEN 'Purchasing is ''On Stop''.  '
				ELSE ''
				END
		FROM tbCompany c
		WHERE c.CompanyId = a.CompanyNo
		) AS OnSupplierStopMessage
	,ISNULL(c.IsSanctioned, 0) AS 'IsSanctioned' --[RP-881]          
	--[RP-881] end            
	,@SOLineECCNCode AS ECCNCodeSOLine
	,@SOLineECCNId AS ECCNIdSOLine
	--,dbo.ufn_convert_between_client_currencies(a.ShipInCost, a.ClientNo, a.IPOClientNo, a.DateOrdered) AS ShipInCostInBaseClient
	,a.ShipInCost * dbo.ufn_get_exchange_rate(@ClientCurrencyInDmcc, a.DateOrdered) AS ShipInCostInBaseClient
FROM vwPurchaseOrderLine a
LEFT JOIN tbCurrency cu ON a.ClientCurrencyNo = cu.CurrencyId
LEFT JOIN tbCompany c ON c.CompanyId = a.CompanyNo --[RP-881]          
WHERE PurchaseOrderLineId = @PurchaseOrderLineId
GO


