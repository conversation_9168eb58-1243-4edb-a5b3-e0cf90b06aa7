﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER VIEW  [dbo].[vwAllocation_Outward]   

AS  --SOs                    
      SELECT  al.AllocationId                    
      , al.QuantityAllocated                    
      , sk.CustomerRMALineNo                    
      , sk.<PERSON>s<PERSON>n<PERSON>ine<PERSON>o                    
      , sk.<PERSON>ur<PERSON>O<PERSON>r<PERSON>                    
      , al.<PERSON>                    
      , NULL AS SupplierRMANo                    
      , NULL AS ReturnDate                    
      , sol.SalesOrderNo                    
      , so.SalesOrderNumber                    
      , so.CustomerPO                    
      , sol.DatePromised                    
      , NULL AS SupplierRMANumber                    
      , NULL AS SupplierRMADate                    
      , so.Salesman                    
      , lg.LoginName AS SalesmanName                    
      , so.CompanyNo                    
      , co.CompanyName                    
      , sol.Part                    
      , sol.ManufacturerNo                    
      , mf.ManufacturerCode                    
      , mf.ManufacturerName                    
      , sol.ProductNo                    
      , pr.ProductName                    
      , sol.PackageNo                    
      , pk.PackageName                    
      , so.<PERSON>ur<PERSON>cyNo                    
      , cu<PERSON>                    
      , sol.<PERSON>                    
      , sol.<PERSON>er<PERSON><PERSON>                    
      , sk.<PERSON>                   
      , al.<PERSON><PERSON>o                   
      , sol.SOSerialNo              
   , sol.ShipASAP  --- Uesd to bind with Promised in Allocation          
   ,lg.EmployeeName as EmployeeName          
   , pol.deliverydate AS [DeliveryDate]         
   , (ISNULL(sol.Price,0)*ISNULL(sol.Quantity,0)) AS [CustomerTargetPrice]        
   , so.CurrencyNo AS [CutomerTargetCurrency]        
   , (ISNULL(pol.Price,0)*ISNULL(pol.Quantity,0)) AS [SourcingPrice]        
   , po.CurrencyNo AS [SourcingCurrency]        
   , pol.DateCode    
   , so.SupportTeamMemberNo        
FROM    tbStock sk                    
JOIN    tbAllocation al ON sk.StockId = al.StockNo                    
JOIN    tbSalesOrderLine sol ON sol.SalesOrderLineId = al.SalesOrderLineNo                    
JOIN    tbSalesOrder so ON so.SalesOrderId = sol.SalesOrderNo                    
LEFT JOIN tbLogin lg ON lg.LoginId = so.Salesman                    
LEFT JOIN tbCompany co ON co.CompanyId = so.CompanyNo                    
LEFT JOIN tbCurrency cu ON cu.CurrencyId = so.CurrencyNo                    
LEFT JOIN tbManufacturer mf ON mf.ManufacturerId = sol.ManufacturerNo                    
LEFT JOIN tbProduct pr ON pr.ProductId = sol.ProductNo                    
LEFT JOIN tbPackage pk ON pk.PackageId = sol.PackageNo           
LEFT JOIN tbpurchaseOrderLine pol on sk.PurchaseOrderLineNo=pol.PurchaseOrderLineId    
LEFT JOIN tbPurchaseOrder po ON sk.PurchaseOrderno=po.PurchaseOrderId  
WHERE   al.SupplierRMALineNo IS NULL                   
UNION                    
--SRMAs                    
SELECT  al.AllocationId                    
      , al.QuantityAllocated                    
      , sk.CustomerRMALineNo                    
      , sk.GoodsInLineNo                    
      , sk.PurchaseOrderLineNo                    
      , al.StockNo                    
      , sln.SupplierRMANo                    
      , sln.ReturnDate AS ReturnDate                    
      , NULL                    
      , NULL                    
      , NULL                    
      , NULL                    
      , srma.SupplierRMANumber                   
      , srma.SupplierRMADate                    
      , srma.AuthorisedBy                    
      , lg.LoginName                    
      , srma.CompanyNo                    
, co.CompanyName                    
      , sln.Part                    
      , sln.ManufacturerNo                    
      , mf.ManufacturerCode                    
      , mf.ManufacturerName                    
     , sln.ProductNo                    
      , pr.ProductName                    
      , sln.PackageNo                    
      , pk.PackageName                    
      , NULL                    
   , NULL                    
      , NULL                    
      , NULL                    
      , sk.ROHS                   
      , NULL                  
      , 0              
   , null   --- Uesd to bind with Promised in Allocation            
   , null          
   , null        
   , null        
   , null        
   , null        
   , null        
   , null     
   , null       
FROM    tbStock sk                    
JOIN    tbAllocation al ON sk.StockId = al.StockNo                    
JOIN    tbSupplierRMALine sln ON sln.SupplierRMALineId = al.SupplierRMALineNo                    
JOIN    tbSupplierRMA srma ON srma.SupplierRMAId = sln.SupplierRMANo                    
LEFT JOIN tbLogin lg ON lg.LoginId = srma.AuthorisedBy                    
LEFT JOIN tbCompany co ON co.CompanyId = srma.CompanyNo                    
LEFT JOIN tbManufacturer mf ON mf.ManufacturerId = sln.ManufacturerNo                    
LEFT JOIN tbProduct pr ON pr.ProductId = sln.ProductNo                    
LEFT JOIN tbPackage pk ON pk.PackageId = sln.PackageNo                    
WHERE   al.SalesOrderLineNo IS NULL   
GO