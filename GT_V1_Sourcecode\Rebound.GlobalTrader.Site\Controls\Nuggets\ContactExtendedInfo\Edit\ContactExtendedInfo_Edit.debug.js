///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.initializeBase(this, [element]);
	this._intContactID = -1;
};

Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.prototype = {

	get_intContactID: function() { return this._intContactID; }, 	set_intContactID: function(value) { if (this._intContactID !== value)  this._intContactID = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
		this.addSave(Function.createDelegate(this, this.saveClicked));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intContactID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.callBaseMethod(this, "dispose");
	},

	formShown: function() {
		this.getFieldDropDownData("ctlMaritalStatus");
		this.getFieldDropDownData("ctlGender");
		this.getFieldDropDownData("ctlChild1Sex");
		this.getFieldDropDownData("ctlChild2Sex");
		this.getFieldDropDownData("ctlChild3Sex");
	},

	saveClicked: function() {
		if (!this.validateForm()) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/ContactExtendedInfo");
		obj.set_DataObject("ContactExtendedInfo");
		obj.set_DataAction("SaveEdit");
		obj.addParameter("id", this._intContactID);
		obj.addParameter("Gender", this.getFieldValue("ctlGender"));
		obj.addParameter("Birthday", this.getFieldValue("ctlBirthday"));
		obj.addParameter("MaritalStatus", this.getFieldValue("ctlMaritalStatus"));
		obj.addParameter("Partner", this.getFieldValue("ctlPartner"));
		obj.addParameter("PartnerBirthday", this.getFieldValue("ctlPartnerBirthday"));
		obj.addParameter("Anniversary", this.getFieldValue("ctlAnniversary"));
		obj.addParameter("NumberOfChildren", this.getFieldValue("ctlNumberChildren"));
		obj.addParameter("ChildName1", this.getFieldValue("ctlChild1Name"));
		obj.addParameter("ChildGender1", this.getFieldValue("ctlChild1Sex"));
		obj.addParameter("ChildBirthday1", this.getFieldValue("ctlChild1Birthday"));
		obj.addParameter("ChildName2", this.getFieldValue("ctlChild2Name"));
		obj.addParameter("ChildGender2", this.getFieldValue("ctlChild2Sex"));
		obj.addParameter("ChildBirthday2", this.getFieldValue("ctlChild2Birthday"));
		obj.addParameter("ChildName3", this.getFieldValue("ctlChild3Name"));
		obj.addParameter("ChildGender3", this.getFieldValue("ctlChild3Sex"));
		obj.addParameter("ChildBirthday3", this.getFieldValue("ctlChild3Birthday"));
		obj.addParameter("PersonalCellphone", this.getFieldValue("ctlMobileTel"));
		obj.addParameter("FavouriteSport", this.getFieldValue("ctlFavouriteSport"));
		obj.addParameter("FavouriteTeam", this.getFieldValue("ctlFavouriteTeam"));
		obj.addParameter("Hobbies", this.getFieldValue("ctlHobbies"));
		obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = this.autoValidateFields();
		return blnOK;
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ContactExtendedInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
