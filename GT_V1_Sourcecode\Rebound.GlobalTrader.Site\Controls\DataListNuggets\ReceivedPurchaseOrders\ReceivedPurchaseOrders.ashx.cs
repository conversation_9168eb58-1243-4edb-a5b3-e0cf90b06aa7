/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets.Data {

	public class ReceivedPurchaseOrders : Base {

		protected override void GetData() {

			JsonObject jsn = new JsonObject();

			ViewLevelList enmViewLevel = (ViewLevelList)GetFormValue_Int("ViewLevel");

			//get data	
			List<GoodsInLine> lst = GoodsInLine.DataListNuggetAsReceivedPO(
				SessionManager.ClientID
				, (enmViewLevel == ViewLevelList.Team) ? (int?)SessionManager.LoginTeamID : null
				, (enmViewLevel == ViewLevelList.Division) ? (int?)SessionManager.LoginDivisionID : null
				, (enmViewLevel == ViewLevelList.My) ? (int?)LoginID : null
				, GetFormValue_NullableInt("PageIndex", 0)
				, GetFormValue_NullableInt("PageSize", 10)
				, GetFormValue_NullableInt("SortIndex")
				, GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC)
				, GetFormValue_NullableInt("PONoLo")
				, GetFormValue_NullableInt("PONoHi")
                //[0001] start code
                //, GetFormValue_StringForPartSearch("Part")
                  , GetFormValue_PartForLikeSearch("Part")
                //[0001] end code
				, GetFormValue_Boolean("RecentOnly")
                , GetFormValue_StringForNameSearch("Supplier")
                , GetFormValue_StringForNameSearch("Contact")
				, GetFormValue_NullableInt("Buyer")
				, GetFormValue_NullableDateTime("DateReceivedFrom")
				, GetFormValue_NullableDateTime("DateReceivedTo")
				, GetFormValue_StringForSearch("AirWayBill")
                , GetFormValue_StringForPartSearch("SupplierPart")
				, GetFormValue_StringForSearch("Reference")
                , SessionManager.IsPOHub
			);

			//check counts
			jsn.AddVariable("TotalRecords", (lst.Count > 0) ? lst[0].RowCnt : 0);

			//format data
			JsonObject jsnRowsArray = new JsonObject(true);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnRow = new JsonObject();
				jsnRow.AddVariable("ID", lst[i].PurchaseOrderNo);
				jsnRow.AddVariable("No", lst[i].PurchaseOrderNumber);
				jsnRow.AddVariable("DateR", Functions.FormatDate(lst[i].DateReceived));
				jsnRow.AddVariable("Part", lst[i].Part);
				jsnRow.AddVariable("ROHS", lst[i].ROHS);
				jsnRow.AddVariable("Qty", Functions.FormatNumeric(lst[i].QuantityOrdered));
				jsnRow.AddVariable("QtyR", Functions.FormatNumeric(lst[i].Quantity));
				jsnRow.AddVariable("CM", lst[i].CompanyName);
				jsnRow.AddVariable("CMNo", lst[i].CompanyNo);
				jsnRow.AddVariable("Contact", lst[i].ContactName);
				jsnRow.AddVariable("ContactNo", lst[i].ContactNo);
				jsnRow.AddVariable("Mfr", lst[i].ManufacturerCode);
				jsnRow.AddVariable("MfrNo", lst[i].ManufacturerNo);
				jsnRow.AddVariable("GI", lst[i].GoodsInNumber);
				jsnRow.AddVariable("GINo", lst[i].GoodsInNo);
				jsnRow.AddVariable("SuppPart", lst[i].SupplierPart);
				jsnRow.AddVariable("SuppInv", lst[i].SupplierInvoice);
				jsnRow.AddVariable("AWB", lst[i].AirWayBill);
				jsnRow.AddVariable("InvTotal", Functions.FormatCurrency(lst[i].InvoiceAmount, lst[i].CurrencyCode));
				jsnRowsArray.AddVariable(jsnRow);
				jsnRow.Dispose();
				jsnRow = null;
			}
			jsn.AddVariable("Results", jsnRowsArray);
			OutputResult(jsn);
			jsnRowsArray.Dispose();
			jsnRowsArray = null;
			jsn.Dispose();
			jsn = null;
			base.GetData();
		}

		protected override void AddFilterStates() {
			AddExplicitFilterState("ViewLevel", GetFormValue_Int("ViewLevel"));
			AddFilterState("PONo");
			AddFilterState("Part");
			AddFilterState("RecentOnly");
			AddFilterState("Supplier");
			AddFilterState("Contact");
			AddFilterState("Buyer");
			AddFilterState("DateReceivedFrom");
			AddFilterState("DateReceivedTo");
			AddFilterState("AirWayBill");
			AddFilterState("SupplierPart");
			AddFilterState("Reference");
			base.AddFilterStates();
		}
	}
}
