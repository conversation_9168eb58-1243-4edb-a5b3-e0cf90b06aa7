<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Add" xml:space="preserve">
    <value>Fügen Sie hinzu</value>
  </data>
  <data name="AddedFromCancelledReceipt" xml:space="preserve">
    <value>{0} Teil fügte vom annullierten Empfang hinzu</value>
  </data>
  <data name="AddedFromCRMA" xml:space="preserve">
    <value>Zusätzliches {0} Teil auf Kunden RMA {1}</value>
  </data>
  <data name="AddedFromPartialReceipt" xml:space="preserve">
    <value>Verursacht {0} bessert vom teilweisen Empfang auf Waren in aus {1}</value>
  </data>
  <data name="AddFromPOLine" xml:space="preserve">
    <value>Zusätzliches {0} Teil auf Kaufauftrag {1}</value>
  </data>
  <data name="AllocatedToSalesOrder" xml:space="preserve">
    <value>{0} Teil teilte Verkaufs-Auftrag zu {1}</value>
  </data>
  <data name="AllocatedToSRMA" xml:space="preserve">
    <value>{0} Teil teilte Lieferanten RMA zu {1}</value>
  </data>
  <data name="AllocationRemovedFromSalesOrder" xml:space="preserve">
    <value>{0} Teil entfernt von Verteilung zu Verkaufs-Auftrag {1}</value>
  </data>
  <data name="AllocationRemovedFromSRMA" xml:space="preserve">
    <value>{0} Teil entfernt von Verteilung zu Lieferanten RMA {1}</value>
  </data>
  <data name="AutoCreatedFromCancelledReceipt" xml:space="preserve">
    <value>Automatisch verursacht vom annullierten Empfang</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Löschen</value>
  </data>
  <data name="CancelledReceipt" xml:space="preserve">
    <value>Annullierter Empfang</value>
  </data>
  <data name="CancelledShipmentForInvoice" xml:space="preserve">
    <value>Annullierter Versand von {0} zerteilt für Rechnung {1}</value>
  </data>
  <data name="ChangesFromReceivePO" xml:space="preserve">
    <value>Änderungen vorgenommen von empfangenem Kaufauftrag {0}</value>
  </data>
  <data name="ChangesOnGoodsIn" xml:space="preserve">
    <value>Änderungen vorgenommen auf Waren in {0}</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Löschung</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Redigieren Sie</value>
  </data>
  <data name="EditPOLine" xml:space="preserve">
    <value>Redigieren Sie vom Kaufauftrag {0} {1} Teil addierend</value>
  </data>
  <data name="EditPOLine_NegativeQuantity" xml:space="preserve">
    <value>Redigieren Sie vom Kaufauftrag {0} {1} Teil entfernend</value>
  </data>
  <data name="EditPOLine_NoQuantityChange" xml:space="preserve">
    <value>Redigieren Sie vom Kaufauftrag {0}</value>
  </data>
  <data name="Inspected" xml:space="preserve">
    <value>Teile kontrolliert</value>
  </data>
  <data name="LinkPO" xml:space="preserve">
    <value>Linked to Purchase Order {0}</value>
  </data>
  <data name="LinkPO_UnknownPO" xml:space="preserve">
    <value>Verbunden mit Kaufauftrag</value>
  </data>
  <data name="ManuallyAdded" xml:space="preserve">
    <value>Manually added stock</value>
  </data>
  <data name="POLineDeleted" xml:space="preserve">
    <value>Kaufauftraglinie Einzelteil gelöscht</value>
  </data>
  <data name="POLineDeleted_FromPO" xml:space="preserve">
    <value>(von Kaufauftrag {0})</value>
  </data>
  <data name="Quarantine" xml:space="preserve">
    <value>Unter Quarantäne gestellt</value>
  </data>
  <data name="QuarantineRemoved" xml:space="preserve">
    <value>Entfernt von der Quarantäne</value>
  </data>
  <data name="Received" xml:space="preserve">
    <value>Empfangenes {0} Teil</value>
  </data>
  <data name="ReceivedOnGoodsIn" xml:space="preserve">
    <value>Empfangenes {0} Teil auf Waren in {1}</value>
  </data>
  <data name="Ship" xml:space="preserve">
    <value>Versendet worden</value>
  </data>
  <data name="ShippedInvoice" xml:space="preserve">
    <value>Versendetes {0} Teil auf Rechnung {1}</value>
  </data>
  <data name="ShippedSRMA" xml:space="preserve">
    <value>Versendetes {0} Teil auf Lieferanten RMA {1}</value>
  </data>
  <data name="Split" xml:space="preserve">
    <value>Spalte</value>
  </data>
  <data name="SplitNew" xml:space="preserve">
    <value>Verursacht indem die Spaltung {0} zerteilt von {1}</value>
  </data>
  <data name="SplitNew_UnknownSourceStock" xml:space="preserve">
    <value>eine vorhandene auf lagereintragung</value>
  </data>
  <data name="SplitOriginal" xml:space="preserve">
    <value>Spalten Sie {0} Stücke von dieser Eintragung auf {1}</value>
  </data>
  <data name="SplitOriginal_UnknownSourceStock" xml:space="preserve">
    <value>Aufspalten Sie {0} Stücke von dieser Eintragung er</value>
  </data>
  <data name="UnlinkPO" xml:space="preserve">
    <value>Gelöst vom Kaufauftrag {0}</value>
  </data>
</root>