﻿
GO

-- =============================================
-- Author:		Cuongdx
-- Create date: 12 Jul 2024
-- Description:	Update quarantine for the GI from Stock
--TASK      			UPDATED BY     		DATE         		ACTION 			DESCRIPTION
--[Task-209817]         Cuong.DoX:	       12/08/24         	Update          uncheck SO, complete inspection, add inspection history
--[211822]              Phuc Hoang	       12/08/24         	Update          Quarantine split GI Line(s) wrongly
-- =============================================


CREATE OR ALTER PROCEDURE [dbo].[usp_update_GILine_Quarantined_From_Stock]
	-- Add the parameters for the stored procedure here
	@GoodsInLineId int,
    @UpdatedBy int,
	@ClientNo INT = 101
AS
BEGIN
	BEGIN TRANSACTION
	/*---- BUG-204859 start ---------*/
	/*-----------------Uncheck all SO associated---------------------------*/
	DECLARE @SaleOrderNo INT, @AuthorisedBy INT
	DECLARE db_cursorSOs CURSOR FOR
	SELECT sol.SalesOrderNo,so.AuthorisedBy
		from tbStock sk
		JOIN tbAllocation al
			ON sk.StockId = al.StockNo
		JOIN tbSalesOrderLine sol
			ON sol.SalesOrderLineId = al.SalesOrderLineNo
		JOIN tbSalesOrder so
			ON so.SalesOrderId = sol.SalesOrderNo
	WHERE sk.GoodsInLineNo = @GoodsInLineId
	OPEN db_cursorSOs
	FETCH next FROM db_cursorSOs
	INTO @SaleOrderNo,@AuthorisedBy
	WHILE @@FETCH_STATUS = 0
	BEGIN
		IF @AuthorisedBy IS NOT NULL
		BEGIN
			EXEC usp_update_SalesOrder_Authorise @SalesOrderId = @SaleOrderNo,@AuthorisedBy=@UpdatedBy,@Authorise=0,@Comment = 'SO unapproved due to containing a line with Quarantined Stock'
		END
		FETCH NEXT FROM db_cursorSOs INTO @SaleOrderNo,@AuthorisedBy;
	END
	CLOSE db_cursorSOs
	DEALLOCATE db_cursorSOs

	/* unallocate all stock relate to that goods in line */
	declare @allocationId int

	DECLARE db_cursorGIAllocation CURSOR FOR
	select AllocationId
	from tbAllocation a
		inner join tbStock sk
			ON sk.StockId = a.StockNo
		inner join tbGoodsInLine gil
            ON gil.PurchaseOrderLineNo = sk.PurchaseOrderLineNo AND gil.GoodsInLineId = sk.GoodsInLineNo
	where gil.GoodsInLineId = @GoodsInLineId

	OPEN db_cursorGIAllocation

	FETCH next FROM db_cursorGIAllocation
	INTO @allocationId
	WHILE @@FETCH_STATUS = 0
	BEGIN
		EXEC usp_delete_Allocation @allocationId,@UpdatedBy

		FETCH NEXT FROM db_cursorGIAllocation INTO @allocationId;
	END
	CLOSE db_cursorGIAllocation
	DEALLOCATE db_cursorGIAllocation

	/*-- send message response to decline all query --*/
	--1. get all query from a goods in line
	declare 
			@GI_QueryId INT,
			@GoodInNo INT,
			@GILineNo INT,
			@LoginId INT,
			@QueryMessage NVARCHAR(3000),
			@SalesApprovalStatus INT = 0,
			@QualityApprovalStatus INT = 0,
			@PurchasingApprovalStatus INT = 0,
			@ApproverHtml NVARCHAR(MAX) = NULL,
			@TotalCheckBoxcount INT = 0,
			@GetEnableCheckBoxIds NVARCHAR(1000) = ''

	DECLARE db_cursorGIQueryMessage CURSOR FOR
	SELECT  GI_QueryId,GoodInNo,GILineNo,SalesApprovalStatus,PurchasingApprovalStatus,QualityApprovalStatus
	FROM tbGI_QueryMessageApprovals WHERE GILineNo = @GoodsInLineId
	
	--get checkbox ids
	SELECT @GetEnableCheckBoxIds = STUFF((
    SELECT ',' + col
    FROM (
        SELECT CASE WHEN C1 = 1 THEN '1' ELSE NULL END AS col  from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C2 = 1 THEN '2' ELSE NULL END AS col  from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C3 = 1 THEN '3' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C4 = 1 THEN '4' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C5 = 1 THEN '5' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C6 = 1 THEN '6' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C7 = 1 THEN '7' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C8 = 1 THEN '8' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C9 = 1 THEN '9' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
        UNION ALL
        SELECT CASE WHEN C10 = 1 THEN '10' ELSE NULL END AS col from tbGI_QueryApprovalResponse where GoodsInLineNo = @GoodsInLineId
    ) AS sub
    WHERE col IS NOT NULL
    FOR XML PATH('')), 1, 1, '');

	--count total checkbox count
	SELECT @TotalCheckBoxcount = 
		(CASE WHEN C1 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C2 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C3 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C4 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C5 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C6 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C7 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C8 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C9 = 1 THEN 1 ELSE 0 END +
		 CASE WHEN C10 = 1 THEN 1 ELSE 0 END)
	FROM tbGI_QueryApprovalResponse 
	WHERE GoodsInLineNo = @GoodsInLineId

	OPEN db_cursorGIQueryMessage

	FETCH next FROM db_cursorGIQueryMessage
	INTO @GI_QueryId,@GoodInNo,@GILineNo,@SalesApprovalStatus,@PurchasingApprovalStatus,@QualityApprovalStatus
	WHILE @@FETCH_STATUS = 0
	BEGIN
		--2. send all query set as status = 2 means declined
		SET @SalesApprovalStatus =
		(
			SELECT CASE WHEN @SalesApprovalStatus is not null THEN 2 ELSE NULL END
		)
		SET @QualityApprovalStatus =
		(
			SELECT CASE WHEN @QualityApprovalStatus is not null THEN 2 ELSE NULL END
		)
		SET @PurchasingApprovalStatus =
		(
			SELECT CASE WHEN @PurchasingApprovalStatus is not null THEN 2 ELSE NULL END
		)
		EXEC usp_Insert_GILineQueryApprovalResponce
										   @GI_QueryId = @GI_QueryId
										  ,@GoodInNo = @GoodInNo
										  ,@GILineNo = @GILineNo
										  ,@LoginId = @UpdatedBy
										  ,@QueryMessage = N'Query is closed (declined) since this GI is quarantined'
										  ,@SalesApprovalStatus = @SalesApprovalStatus
										  ,@QualityApprovalStatus = @QualityApprovalStatus
										  ,@PurchasingApprovalStatus = @PurchasingApprovalStatus
										  ,@ClientNo = @ClientNo
										  ,@TotalCheckBoxcount = @TotalCheckBoxcount
										  ,@CheckedTotalCheckBoxcount =  @TotalCheckBoxcount
										  ,@GetEnableCheckBoxIds = @GetEnableCheckBoxIds
		FETCH NEXT FROM db_cursorGIQueryMessage
		INTO @GI_QueryId,@GoodInNo,@GILineNo,@SalesApprovalStatus,@PurchasingApprovalStatus,@QualityApprovalStatus
	END
	CLOSE db_cursorGIQueryMessage
	DEALLOCATE db_cursorGIQueryMessage

	/*--Complete inspection if this GI Line is started inspection--*/
	DECLARE @IsStartInspection INT, @RowsAffected INT = NULL 
	SELECT @IsStartInspection = IsStartInspection
	FROM tbGoodsInLine
	WHERE GoodsInLineId = @GoodsInLineId
	IF (@IsStartInspection = 1) --Start Inspection                  
        BEGIN
			EXEC usp_GILine_StartOrCloseInspection @GoodsInLineId,@Comment='It has been quarantined',@UpdatedBy=@UpdatedBy,@InspectionOption=2,@RowsAffected = @RowsAffected OUTPUT
		END

	/*--release the GI line--*/
	EXEC usp_update_GoodsInLine_Inspect @GoodsInLineId,@UpdatedBy,@Quarantined = 1

	/*update gi line inspection log*/
	                
	  INSERT INTO tbGoodsInLineInspectionLog                
	  (                
	  GoodsInLineNo,                
	  InspectionAction,                 
	  InspectionDate,                 
	  InspectionBy,                  
	  InspectionComment,                 
	  ISActive                   
	  )                 
	  VALUES(                
	  @GoodsInLineId,                
	  'Quarantined',
	  GETDATE(),                
	  @UpdatedBy,                
	  'Quarantined',                
	  1                
	  )  

    COMMIT TRANSACTION
END	

GO