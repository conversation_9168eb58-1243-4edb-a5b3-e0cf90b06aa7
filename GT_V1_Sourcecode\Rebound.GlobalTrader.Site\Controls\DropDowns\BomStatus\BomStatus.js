Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.initializeBase(this,[n]);this._intCompanyID=null};Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.prototype={get_intCompanyID:function(){return this._intCompanyID},set_intCompanyID:function(n){this._intCompanyID!==n&&(this._intCompanyID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.callBaseMethod(this,"dispose")},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/BomStatus");this._objData.set_DataObject("BomStatus");this._objData.set_DataAction("GetData");this._objData.addParameter("id",this._intCompanyID)},dataCallOK:function(){var t=this._objData._result,n;if(t.BomStatus)for(n=0;n<t.BomStatus.length;n++)this.addOption(t.BomStatus[n].Name,t.BomStatus[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.BomStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);