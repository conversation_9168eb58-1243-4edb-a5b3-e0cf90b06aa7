﻿//Marker     Changed by      Date         Remarks
//[001]      Vinay           04/09/2012   Print Label
//[002]      Vinay           08/07/2013    Ref:## -32 Nice Label Printing
//[003]      Vinay           28/08/2013    NPR Print
//[004]      <PERSON><PERSON><PERSON>         27/02/2014    GoodsIn - Po serial No.
//[005]     <PERSON><PERSON><PERSON>      26-Oct-2018    Issue - with GI line edit at a time in two tabs.
//[006]     <PERSON>     17/08/2021   (GTDP-198) Controlling specific product groups for purchasing
//[007]     Ab<PERSON>av <PERSON>  10/11/2021   Work on new query message of GI Lines.
//[008]     Abhinav <PERSON>  23/11/201    Add new property for Gi Query No.
//[009]     Abhinav <PERSON>a  22/12/2021   Add new property for no-reply.
//[010]     Abhinav <PERSON>  29/12/2021  Add new property for approval logic
//[011]     Ab<PERSON>av <PERSON>  13/01/2022  Add new property for approver add/remove logic.
//[012]     Ab<PERSON>av <PERSON>  19/01/2022  Add ability to CC any GT Users.
//[013]     Ab<PERSON><PERSON>  09/02/2022  Add new properties for query HIC status.
//[014]      <PERSON>    21-03-2023  [RP-968] Added two new properties for HasBarcodeScan and BarcodeScanRemarks 
//[015]      Ravi Bhushan    15-05-2023  [RP-616] Adding Property for 'InvoiceExported'
//[016]      Ravi Bhushan    19-09-2023  RP-2338  AS6081 Search/Filter functionality on different pages 
//[RP-2546]  Ravi bushan     GI Phase 2 Re-Open/ Released Issue
//[RP-881]   Ravi Bhushan    22-11-2023   New CR by client to show banner on GI Screen
//Code merge for GI Line
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;

namespace Rebound.GlobalTrader.DAL
{

    public class GoodsInLineDetails
    {

        #region Constructors

        public GoodsInLineDetails() { }

        #endregion

        #region Properties
        public System.Boolean? IsSTOGi { get; set; }
        /// <summary>
        /// GoodsInLineId (from Table)
        /// </summary>
        public System.Int32 GoodsInLineId { get; set; }
        /// <summary>
        /// GoodsInNo (from Table)
        /// </summary>
        public System.Int32 GoodsInNo { get; set; }
        /// <summary>
        /// PurchaseOrderLineNo (from Table)
        /// </summary>
        public System.Int32? PurchaseOrderLineNo { get; set; }
        /// <summary>
        /// FullPart (from Table)
        /// </summary>
        public System.String FullPart { get; set; }
        public System.String IHSProduct { get; set; }
        /// <summary>
        /// Part (from Table)
        /// </summary>
        public System.String Part { get; set; }
        /// <summary>
        /// ManufacturerNo (from Table)
        /// </summary>
        public System.Int32? ManufacturerNo { get; set; }
        /// <summary>
        /// DateCode (from Table)
        /// </summary>
        public System.String DateCode { get; set; }
        /// <summary>
        /// PackageNo (from Table)
        /// </summary>
        public System.Int32? PackageNo { get; set; }
        /// <summary>
        /// Quantity (from Table)
        /// </summary>
        public System.Int32 Quantity { get; set; }
        /// <summary>
        /// Price (from Table)
        /// </summary>
        public System.Double Price { get; set; }
        /// <summary>
        /// ShipInCost (from Table)
        /// </summary>
        public System.Double? ShipInCost { get; set; }
        /// <summary>
        /// QualityControlNotes (from Table)
        /// </summary>
        public System.String QualityControlNotes { get; set; }
        /// <summary>
        /// Location (from Table)
        /// </summary>
        public System.String Location { get; set; }
        /// <summary>
        /// ProductNo (from Table)
        /// </summary>
        public System.Int32? ProductNo { get; set; }
        /// <summary>
        /// LandedCost (from Table)
        /// </summary>
        public System.Double LandedCost { get; set; }

        /// <summary>
        /// CustomerRMALineNo (from Table)
        /// </summary>
        public System.Int32? CustomerRMALineNo { get; set; }
        /// <summary>
        /// SupplierPart (from Table)
        /// </summary>
        public System.String SupplierPart { get; set; }
        /// <summary>
        /// ROHS (from Table)
        /// </summary>
        public System.Byte? ROHS { get; set; }
        /// <summary>
        /// CountryOfManufacture (from Table)
        /// </summary>
        public System.Int32? CountryOfManufacture { get; set; }
        /// <summary>
        /// UpdatedBy (from Table)
        /// </summary>
        public System.Int32? UpdatedBy { get; set; }

        ////[012] code start
        /// <summary>
        /// CCUSerId (from Table)
        /// </summary>
        public System.Int32? CCUSerId { get; set; }
        ////[012] code end
        /// <summary>
        /// NoReplyId (from Table)
        /// </summary>
        public System.Int32? NoReplyId { get; set; }
        /// <summary>
        /// NoReplyEmail (from Table)
        /// </summary>
        public System.String NoReplyEmail { get; set; }
        /// <summary>
        /// DLUP (from Table)
        /// </summary>
        public System.DateTime DLUP { get; set; }
        /// <summary>
        /// InspectedBy (from Table)
        /// </summary>
        public System.Int32? InspectedBy { get; set; }
        /// <summary>
        /// DateInspected (from Table)
        /// </summary>
        public System.DateTime? DateInspected { get; set; }
        /// <summary>
        /// CountingMethodNo (from Table)
        /// </summary>
        public System.Int32? CountingMethodNo { get; set; }
        /// <summary>
        /// SerialNosRecorded (from Table)
        /// </summary>
        public System.Boolean? SerialNosRecorded { get; set; }
        /// <summary>
        /// Unavailable (from Table)
        /// </summary>
        public System.Boolean? Unavailable { get; set; }
        /// <summary>
        /// LotNo (from Table)
        /// </summary>
        public System.Int32? LotNo { get; set; }
        /// <summary>
        /// Notes (from Table)
        /// </summary>
        public System.String Notes { get; set; }
        /// <summary>
        /// PartMarkings (from Table)
        /// </summary>
        public System.String PartMarkings { get; set; }
        /// <summary>
        /// FullSupplierPart (from Table)
        /// </summary>
        public System.String FullSupplierPart { get; set; }
        /// <summary>
        /// GoodsInId (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.Int32 GoodsInId { get; set; }
        /// <summary>
        /// GoodsInNumber (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.Int32 GoodsInNumber { get; set; }
        /// <summary>
        /// ManufacturerCode (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.String ManufacturerCode { get; set; }
        /// <summary>
        /// DateReceived (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.DateTime DateReceived { get; set; }
        public System.String PIBy { get; set; }
        /// <summary>
        /// ReceiverName (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.String ReceiverName { get; set; }
        /// <summary>
        /// PurchaseOrderNo (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.Int32? PurchaseOrderNo { get; set; }
        /// <summary>
        /// DeliveryDate (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.DateTime? DeliveryDate { get; set; }
        /// <summary>
        /// PurchaseOrderNumber (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.Int32? PurchaseOrderNumber { get; set; }
        /// <summary>
        /// CompanyNo (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.Int32 CompanyNo { get; set; }
        /// <summary>
        /// CompanyName (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.String CompanyName { get; set; }
        /// <summary>
        /// AirWayBill (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.String AirWayBill { get; set; }
        /// <summary>
        /// RowNum (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.Int64? RowNum { get; set; }
        /// <summary>
        /// RowCnt (from usp_datalistnugget_GoodsInLine)
        /// </summary>
        public System.Int32? RowCnt { get; set; }
        /// <summary>
        /// QuantityOrdered (from usp_datalistnugget_GoodsInLine_AsReceivedPO)
        /// </summary>
        public System.Int32 QuantityOrdered { get; set; }
        /// <summary>
        /// ContactNo (from usp_datalistnugget_GoodsInLine_AsReceivedPO)
        /// </summary>
        public System.Int32 ContactNo { get; set; }
        /// <summary>
        /// ContactName (from usp_datalistnugget_GoodsInLine_AsReceivedPO)
        /// </summary>
        public System.String ContactName { get; set; }
        /// <summary>
        /// SupplierInvoice (from usp_datalistnugget_GoodsInLine_AsReceivedPO)
        /// </summary>
        public System.String SupplierInvoice { get; set; }
        /// <summary>
        /// InvoiceAmount (from usp_datalistnugget_GoodsInLine_AsReceivedPO)
        /// </summary>
        public System.Double? InvoiceAmount { get; set; }
        /// <summary>
        /// CurrencyNo (from usp_datalistnugget_GoodsInLine_AsReceivedPO)
        /// </summary>
        public System.Int32? CurrencyNo { get; set; }
        /// <summary>
        /// CurrencyCode (from usp_datalistnugget_GoodsInLine_AsReceivedPO)
        /// </summary>
        public System.String CurrencyCode { get; set; }
        /// <summary>
        /// CountingMethodDescription (from usp_select_GoodsInLine)
        /// </summary>
        public System.String CountingMethodDescription { get; set; }
        /// <summary>
        /// LineNotes (from usp_select_GoodsInLine)
        /// </summary>
        public System.String LineNotes { get; set; }
        /// <summary>
        /// ClientNo (from usp_select_GoodsInLine)
        /// </summary>
        public System.Int32 ClientNo { get; set; }
        /// <summary>
        /// PackageName (from usp_select_GoodsInLine)
        /// </summary>
        public System.String PackageName { get; set; }
        /// <summary>
        /// PackageDescription (from usp_select_GoodsInLine)
        /// </summary>
        public System.String PackageDescription { get; set; }
        /// <summary>
        /// ProductName (from usp_select_GoodsInLine)
        /// </summary>
        public System.String ProductName { get; set; }
        /// <summary>
        /// ProductDescription (from usp_select_GoodsInLine)
        /// </summary>
        public System.String ProductDescription { get; set; }
        /// <summary>
        /// ProductDutyCode (from usp_select_GoodsInLine)
        /// </summary>
        public System.String ProductDutyCode { get; set; }
        /// <summary>
        /// ManufacturerName (from usp_select_GoodsInLine)
        /// </summary>
        public System.String ManufacturerName { get; set; }
        /// <summary>
        /// CountryOfManufactureName (from usp_select_GoodsInLine)
        /// </summary>
        public System.String CountryOfManufactureName { get; set; }
        /// <summary>
        /// InspectorName (from usp_select_GoodsInLine)
        /// </summary>
        public System.String InspectorName { get; set; }
        /// <summary>
        /// CustomerRMANo (from usp_select_GoodsInLine)
        /// </summary>
        public System.Int32? CustomerRMANo { get; set; }
        /// <summary>
        /// CustomerRMANumber (from usp_select_GoodsInLine)
        /// </summary>
        public System.Int32? CustomerRMANumber { get; set; }
        /// <summary>
        /// CurrencyDescription (from usp_select_GoodsInLine)
        /// </summary>
        public System.String CurrencyDescription { get; set; }
        /// <summary>
        /// ReceivedBy (from usp_select_GoodsInLine)
        /// </summary>
        public System.Int32 ReceivedBy { get; set; }
        /// <summary>
        /// DivisionNo (from usp_select_GoodsInLine)
        /// </summary>
        public System.Int32? DivisionNo { get; set; }
        /// <summary>
        /// TeamNo (from usp_select_GoodsInLine)
        /// </summary>
        public System.Int32? TeamNo { get; set; }
        /// <summary>
        /// StockNo (from usp_select_GoodsInLine)
        /// </summary>
        public System.Int32? StockNo { get; set; }
        /// <summary>
        /// Reference (from usp_select_GoodsInLine)
        /// </summary>
        public System.String Reference { get; set; }
        /// <summary>
        /// LotName (from usp_select_GoodsInLine)
        /// </summary>
        public System.String LotName { get; set; }
        /// <summary>
        /// PurchaseOrderLineShipInCost (from usp_select_GoodsInLine)
        /// </summary>
        public System.Double PurchaseOrderLineShipInCost { get; set; }
        /// <summary>
        /// ChangedFields (from usp_select_GoodsInLine)
        /// </summary>
        public System.String ChangedFields { get; set; }
        /// <summary>
        /// UpdateStock (from usp_select_GoodsInLine)
        /// </summary>
        public System.Boolean? UpdateStock { get; set; }
        /// <summary>
        /// UpdateShipments (from usp_select_GoodsInLine)
        /// </summary>
        public System.Boolean? UpdateShipments { get; set; }
        //[001] code start
        /// <summary>
        /// CustomerPart
        /// </summary>
        public System.String CustomerPart { get; set; }
        /// <summary>
        /// SalesOrderNumber
        /// </summary>
        public System.Int32? SalesOrderNumber { get; set; }
        /// <summary>
        /// DatePicked
        /// </summary>
        public System.DateTime DatePicked { get; set; }
        /// <summary>
        /// InspectedByUser
        /// </summary>
        public System.String InspectedByUser { get; set; }
        //[001] code end
        /// <summary>
        /// HasAllocationOutward
        /// </summary>
        public System.Boolean? HasAllocationOutward { get; set; }
        /// <summary>
        /// NPRPrinted
        /// </summary>
        public System.Boolean? NPRPrinted { get; set; }
        //IHS code start
        //IHS End
        /// <summary>
        /// IHSPartsId
        /// </summary>
        public System.Int32? IHSPartsId { get; set; }
        /// <summary>
        /// OriginalEntryDate
        /// </summary>
        public System.DateTime? OriginalEntryDate { get; set; }
        /// <summary>
        /// Descriptions
        /// </summary>
        public System.String Descriptions { get; set; }
        /// <summary>
        /// CountryOfOrigin
        /// </summary>
        public System.String CountryOfOrigin { get; set; }
        /// <summary>
        /// CountryOfOriginNo
        /// </summary>
        public System.Int32? CountryOfOriginNo { get; set; }
        /// <summary>
        /// LifeCycleStage
        /// </summary>
        public System.String LifeCycleStage { get; set; }

        /// <summary>
        /// MSL
        /// </summary>
        public System.String MSL { get; set; }
        /// <summary>
        /// MSLNo
        /// </summary>
        public System.Int32? MSLNo { get; set; }
        /// <summary>
        /// AveragePrice
        /// </summary>
        public System.Double AveragePrice { get; set; }
        /// <summary>
        /// HTSCode
        /// </summary>
        public System.String HTSCode { get; set; }

        /// <summary>
        /// Packaging
        /// </summary>
        public System.String Packaging { get; set; }
        /// <summary>
        /// PackagingSize
        /// </summary>
        public System.String PackagingSize { get; set; }
        /// <summary>
        /// MSLName
        /// </summary>
        public System.String MSLName { get; set; }
        //[004] code End
        public System.String ECCNCode { get; set; }

        //[002] code start
        /// <summary>
        /// InspectorNameLabel
        /// </summary>
        public System.String InspectorNameLabel { get; set; }
        public System.String IHSCurrencyCode { get; set; }
        //[002] code end

        //[003] code start
        /// <summary>
        /// NPRIds
        /// </summary>
        public System.String NPRIds { get; set; }
        /// <summary>
        /// NPRNos
        /// </summary>
        public System.String NPRNos { get; set; }
        //[003] code end

        /// SupplierInvoiceExists
        /// </summary>
        public System.Boolean HasSupplierInvoiceExists { get; set; }

        /// HasStocksplit
        /// </summary>
        public System.Boolean HasStocksplit { get; set; }
        /// <summary>
        /// blnStockProvision
        /// </summary>
        public System.Boolean blnStockProvision { get; set; }

        /// <summary>
        /// PhysicalInspectedBy
        /// </summary>
        public System.Int32? PhysicalInspectedBy { get; set; }
        /// <summary>
        /// DatePhysicalInspected
        /// </summary>
        public System.DateTime? DatePhysicalInspected { get; set; }
        /// <summary>
        /// LotCode
        /// </summary>
        public System.String LotCode { get; set; }

        //[004] Code Start
        /// POSerialNo
        /// </summary>
        public System.Int16? POSerialNo { get; set; }
        //[004] Code End
        public System.Int32? SerialId { get; set; }
        public System.String Box { get; set; }
        public System.String SerialNumber { get; set; }
        public int? InternalPurchaseOrderNumber { get; set; }
        public int? InternalPurchaseOrderId { get; set; }
        public int? IPOSupplier { get; set; }
        public string IPOSupplierName { get; set; }
        /// <summary>
        /// ClientPrice (from Table)
        /// </summary>
        public System.Double ClientPrice { get; set; }

        public string ClientName { get; set; }
        /// <summary>
        /// ClientLandedCost (from Table)
        /// </summary>
        public System.Double ClientLandedCost { get; set; }
        public System.Int32? ClientCurrencyNo { get; set; }
        public System.String ClientCurrencyCode { get; set; }

        public int IpoCount { get; set; }
        public System.Int32? IPOClientNo { get; set; }
        public System.Double? POBankFee { get; set; }
        /// <summary>
        /// CustomerPO
        /// </summary>
        public System.String CustomerPO { get; set; }
        /// </summary>
        public System.String ClientBaseCurrencyCode { get; set; }
        public System.Int32? ClientBaseCurrencyID { get; set; }
        public System.DateTime? StockDate { get; set; }
        public System.Boolean? ProductInactive { get; set; }
        public System.Double? DutyRate { get; set; }

        public System.String SerialNo { get; set; }
        public int? SerialNoId { get; set; }
        public int? CustomerRequirementId { get; set; }
        public int? CustomerRequirementNumber { get; set; }

        public System.String SubGroup { get; set; }
        public System.Boolean? ReqSerialNo { get; set; }
        public System.Int32? SerialNoCount { get; set; }
        public System.String Status { get; set; }
        public System.Int32? InvoiceLineNo { get; set; }
        public System.String ReasonDate { get; set; }
        public System.String ReasonCode { get; set; }
        public System.String MSLLevel { get; set; }
        public System.Boolean? IsProdHazardous { get; set; }
        public System.Boolean? PrintHazardous { get; set; }
        public System.Int32 GIShipCostHistoryId { get; set; }
        //[005] start
        public string StringDLUP { get; set; }
        //[005] end
        public System.Int32? ParentGoodsInLineId { get; set; }
        public System.Double? TotalShipCost { get; set; }
        public System.Int32? GIStatus { get; set; }
        /// <summary>
        /// IsPDFAvailable
        /// </summary>
        public System.Boolean? IsPDFAvailable { get; set; }
        public System.Int32? SalesPersonId { get; set; }
        public System.String SalesPersonName { get; set; }
        //[006] code start
        public System.Boolean? IsOrderViaIPOonly { get; set; }
        //[006] code end
        public System.String SupplierMessage { get; set; }
        public System.String GoodInLineMessage { get; set; }
        public System.Int32? HICId { get; set; }
        public System.String HICStatus { get; set; }
        //[013] code start
        public System.Int32? QueryHICId { get; set; }
        public System.String QueryHICStatus { get; set; }
        //[013] code end
        public System.Boolean? IsFullQtyRecieved { get; set; }
        public System.Boolean? IsPartNoCorrect { get; set; }
        public System.String CorrectPartNo { get; set; }
        public System.Boolean? IsManufacturerCorrect { get; set; }
        public System.Int32? CorrectManufacturer { get; set; }
        public System.Boolean? IsDateCodeCorrect { get; set; }
        public System.String CorrectDateCode { get; set; }
        public System.Boolean? IsDateCodeRequired { get; set; }
        public System.Boolean? IsPackageTypeCorrect { get; set; }
        public System.Int32? CorrectPackageType { get; set; }
        public System.Boolean? IsMSLLevelCorrect { get; set; }
        public System.String CorrectMSLLevel { get; set; }
        public System.Int32? HIC_Status { get; set; }
        public System.Boolean? IsHICStatusCorrect { get; set; }
        public System.String CorrectHICStatus { get; set; }
        public System.String PKGBreakdownMismatch { get; set; }
        public System.Boolean? IsROHSStatusCorrect { get; set; }
        public System.Int32? CorrectROHSStatus { get; set; }
        public System.Boolean? IsLotCodesReq { get; set; }
        public System.Int32? BakingLevelAdded { get; set; }
        public System.Boolean? EnhancedInspectionReq { get; set; }
        public System.String GeneralInspectionNotes { get; set; }
        public System.Boolean? IsInspectionConducted { get; set; }
        public System.String CompanyType { get; set; }
        public System.String CorrectManufacturerName { get; set; }
        public System.String CorrectPackageName { get; set; }
        public System.String GIQuery { get; set; }
        public System.Boolean IsSalesNotify { get; set; }
        public System.Boolean IsQualityNotify { get; set; }
        public System.Boolean IsPurchaseNotify { get; set; }
        public System.Int32? ID { get; set; }
        public System.String Name { get; set; }
        public System.String SalesQueryReply { get; set; }
        public System.String PurchaseQueryReply { get; set; }
        public System.String QualityQueryReply { get; set; }
        public System.Int32? SalesApprovalStatus { get; set; }
        public System.Int32? PurchaseApprovalStatus { get; set; }
        public System.Int32? QualityApprovalStatus { get; set; }
        //[010] code start
        public System.Int32? ParentSalesApprovalStatus { get; set; }
        public System.Int32? ParentPurchaseApprovalStatus { get; set; }
        public System.Int32? ParentQualityApprovalStatus { get; set; }

        public System.String CurrentPurchasingApprover { get; set; }
        public System.String CurrentSalesApprover { get; set; }
        public System.String CCUsersName { get; set; }
        public System.String PreviousPurchasingApproverName { get; set; }
        public System.String PreviousSalesApproverName { get; set; }
        public System.Int32? PreviousPurchasingApproverId { get; set; }
        public System.Int32? PreviousSalesApproverId { get; set; }
        //[010] code end
        public System.Boolean IsPDFReportRequired { get; set; }
        public System.Boolean IsQuarantineProduct { get; set; }
        public System.Int32? LotNoId { get; set; }
        public System.String LotNumber { get; set; }
        public System.Boolean? ReqLotNo { get; set; }
        public System.Int32? LotNoCount { get; set; }
        public System.Int32? SupportTeamMemberNo { get; set; }
        //[011] code start
        public System.Int32? PurchasingApproverId { get; set; }
        public System.Int32? SalesApproverId { get; set; }
        //[011] code end
        public System.String QueryMessage { get; set; }
        public System.String QueryMessageApproval { get; set; }
        public System.Boolean? NotifyToSales { get; set; }
        public System.Boolean? NotifyToQuality { get; set; }

        public System.Boolean? NotifyToPurchasing { get; set; }
        public System.Boolean? IsSendMail { get; set; }
        public System.Boolean? MyMessage { get; set; }
        public System.Boolean? ISInitialNotifyToSales { get; set; }
        public System.Boolean? ISInitialNotifyToQuality { get; set; }
        public System.Boolean? ISInitialNotifyToPurchase { get; set; }
        public System.Boolean? IsInitialMessage { get; set; }
        /// <summary>
        /// PackBreakDownJSON
        /// </summary>
        public System.String PackBreakDownJSON { get; set; }
        public System.Int32? Result { get; set; }
        public System.String RaisedBy { get; set; }
        public System.String Department { get; set; }
        public System.String ApprovalName { get; set; }
        public System.DateTime? ApprovedDate { get; set; }
        public System.String ApprovalStatus { get; set; }
        public System.String GIQueryNumber { get; set; }
        public System.Boolean? InDraftMode { get; set; }
        public System.Int32? Gi_QueryId { get; set; }
        public System.String WarehouseRemark { get; set; }
        public System.Boolean? ISSalesPermission { get; set; }
        public System.Boolean? ISPurchasingPermission { get; set; }
        public System.Boolean? ISQualityPermission { get; set; }
        public System.String CurrentSalesApprovalStatus { get; set; }
        public System.String CurrentPurchasingApprovalStatus { get; set; }
        public System.String CurrentQualityApprovalStatus { get; set; }
        public System.Boolean? Quarantined { get; set; }
        /// <summary>
        ///  POPart
        /// </summary>
        public System.String POPart { get; set; }
        /// <summary>
        /// POManufacturerName
        /// </summary>
        public System.String POManufacturerName { get; set; }
        /// <summary>
        /// PODateCode
        /// </summary>
        public System.String PODateCode { get; set; }
        /// <summary>
        /// POPackageType
        /// </summary>
        public System.String POPackageType { get; set; }
        /// <summary>
        /// POMSLLevel
        /// </summary>
        public System.String POMSLLevel { get; set; }
        /// <summary>
        /// POROHSStatus
        /// </summary>
        public System.String POROHSStatus { get; set; }
        public System.Int32? POManufacturerNo { get; set; }
        public System.Int32? POPackageNo { get; set; }
        public System.Int32? POROHS { get; set; }
        public System.Int32? POQuantity { get; set; }
        public System.String ReleseStockDisbaleReason { get; set; }
        public System.Boolean? QueryRaised { get; set; }
        public System.Int32? ActeoneTestStatus { get; set; }
        public System.Int32? IsopropryleStatus { get; set; }
        public System.String ActeoneTest { get; set; }
        public System.String Isopropryle { get; set; }
        public System.String HICStatusName { get; set; }
        public System.String UpdateType { get; set; }
        public System.Boolean? ReleseStockDisbaleStatus { get; set; }
        public System.String QueryBakingLevel { get; set; }
        public System.Int32? SalesOrderNo { get; set; }
        public System.Int32? EnhancedInspectionStatusId { get; set; }
        public System.Boolean? IsSendQuery { get; set; }
        public System.String DraftQueryMessage { get; set; }
        public System.Boolean? IsShortShipmentEnable { get; set; }
        public System.Int32? ShortShipmentId { get; set; }
        public System.String Approvers { get; set; }
        /// <summary>
        /// ShortShipmentIds
        /// </summary>
        public System.String ShortShipmentIds { get; set; }
        /// <summary>
        /// ShortShipmentNos
        /// </summary>
        public System.String ShortShipmentNos { get; set; }
        public System.String PrintDateCode { get; set; }
        public System.String SalesApproverIds { get; set; }
        public System.Boolean? IsCCMailGroupId { get; set; }
        public System.String MailType { get; set; }
        public System.String supplierType { get; set; }
        public System.String QueryRaisedBy { get; set; }
        public System.Int32? AllocatedSalesOrderNumber { get; set; }

        public System.Boolean? IsNotGBLPermissionForSales { get; set; }
        public System.Boolean? IsNotGBLPermissionForPurch { get; set; }
        public System.Boolean? IsNotGBLPermissionForQaulity { get; set; }
        public System.String AttachmentFileName { get; set; }
        public System.String AttachmentType { get; set; }
        public System.String MessageAuther { get; set; }

        //[012] start
        public System.Int32? HasBarcodeScan { get; set; }
        public System.String BarcodeScanRemarks { get; set; }

        public System.Int32 GIBarcodeScanStatusId { get; set; } //  property from tbGI_BarcodeScanStatus
        public System.String GIBarcodesScanStatusName { get; set; } //  property from tbGI_BarcodeScanStatus
        public System.Boolean? IsStartInspection { get; set; }
        public System.Boolean? ISCloseInspection { get; set; }
        public System.Int32? InspectionLogId { get; set; }
        public System.String ActionTaken { get; set; }
        public System.String CommentText { get; set; }
        public string PartNoQuery { get; set; }
        public string ManufacturerQuery { get; set; }
        public string PackagingTypeQuery { get; set; }
        public string MslQuery { get; set; }
        public string RohsQuery { get; set; }
        public System.Boolean? IsEditInspection { get; set; }
        public System.Boolean? IsAlreadyClosed { get; set; }
        //[012] end

        public System.Boolean? GiInvoiceExported { get; set; } //[015]

        public System.Int32? SalesGroupId { get; set; }
        public System.String SalesGroupName { get; set; }

        public System.Int32? PurchasingGroupId { get; set; }
        public System.String PurchasingGroupName { get; set; }
        public System.String PartNumberFilled { get; set; }
        public System.Boolean? PartNumberFilledChkEnable { get; set; }
        public System.String PackageBreakdownComplete { get; set; }
        public System.Boolean? PackageBreakdownCompleteChkEnable { get; set; }
        public System.String PhotosAttached { get; set; }
        public System.Boolean? PhotosAttachedChkEnable { get; set; }
        public System.String BarcodeScannedTicked { get; set; }
        public System.Boolean? BarcodeScannedTickedChkEnable { get; set; }
        public System.String QueryFormatUse { get; set; }
        public System.Boolean? QueryFormatUseChkEnable { get; set; }

        public System.String ProcessPurchaseApproverName { get; set; }
        public System.String ProcessSalesApproverName { get; set; }
        public System.String ProcessQualityApproverName { get; set; }
        public System.Boolean? C1 { get; set; }
        public System.Boolean? C2 { get; set; }
        public System.Boolean? C3 { get; set; }
        public System.Boolean? C4 { get; set; }
        public System.Boolean? C5 { get; set; }
        public System.Boolean? C6 { get; set; }
        public System.Boolean? C7 { get; set; }
        public System.Boolean? C8 { get; set; }
        public System.Boolean? C9 { get; set; }
        public System.Boolean? C10 { get; set; }
        public System.Boolean? IsQueryColumn { get; set; }
        public System.Boolean? ISReleaseStock { get; set; }
        public System.Boolean? GeneralInspectionQuery { get; set; }
        public System.Boolean? IsEditAfterStartInspection { get; set; }

        public System.Boolean AS6081 { get; set; } //[016]

        public System.String AlertMessage { get; set; } //[RP-2546]
        public System.Int32 InspectionStatus { get; set; }//[RP-2546]
        public System.Boolean IsSanctioned { get; set; } //[RP-881]
        public System.Boolean? APIImportedData { get; set; }
        #endregion

    }
    public class PackagingBreakdown
    {
        public System.Boolean? FactorySealed { get; set; }
        public System.Int32? NumberOfPacks { get; set; }
        public System.Double? PackSize { get; set; }
        public System.String DateCode { get; set; }
        public System.String BatchCode { get; set; }
        public System.Double? TotalPackSize { get; set; }
        public System.Int32? PackagingTypeId { get; set; }
        public System.Int32? MFRLabelId { get; set; }

    }
    public class DateCode
    {
        public System.Int32 ID { get; set; }
        public System.String DateCodes { get; set; }
        public System.Int32? Quantity { get; set; }
        public System.Int32? QuantityReceived { get; set; }
    }
}
