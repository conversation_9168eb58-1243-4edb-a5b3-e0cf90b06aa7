///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// SK 13.04.2010:
// - display Print/Email buttons based on PO aproval/disapproval 
//
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
[001]      Vinay           07/05/2012   This need to upload pdf document for Purchage Order section
[002]       <PERSON><PERSON>an         25/06/2015  Pdf Drag and Drop
[003]      Shashi Keshar   12/07/2017   Supplier edit disable if Partial Quantity received
[004]      Aashu Singh     22-Aug-2018  REB-12084:Lock PO lines when EPR is authorised
[005]      Abhinav Saxena  30-Nov-2021  Add export to excel.
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Orders");

Rebound.GlobalTrader.Site.Pages.Orders.PODetail = function(el) { 
	Rebound.GlobalTrader.Site.Pages.Orders.PODetail.initializeBase(this, [el]);
	this._blnIsApproved = false;
};

Rebound.GlobalTrader.Site.Pages.Orders.PODetail.prototype = {

    get_intPOID: function() { return this._intPOID; }, set_intPOID: function(v) { if (this._intPOID !== v) this._intPOID = v; },
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
    get_ctlLines: function() { return this._ctlLines; }, set_ctlLines: function(v) { if (this._ctlLines !== v) this._ctlLines = v; },
    get_btnPrint: function() { return this._btnPrint; }, set_btnPrint: function(v) { if (this._btnPrint !== v) this._btnPrint = v; },
    get_pnlStatus: function() { return this._pnlStatus; }, set_pnlStatus: function(v) { if (this._pnlStatus !== v) this._pnlStatus = v; },
    get_lblStatus: function() { return this._lblStatus; }, set_lblStatus: function(v) { if (this._lblStatus !== v) this._lblStatus = v; },
    // [001] code start
    get_ctlPODocuments: function() { return this._ctlPODocuments; }, set_ctlPODocuments: function(v) { if (this._ctlPODocuments !== v) this._ctlPODocuments = v; },
    // [001] code end
    // [002] code start
    get_ctlPOPDFDragDrop: function() { return this._ctlPOPDFDragDrop }, set_ctlPOPDFDragDrop: function(a) { if (this._ctlPOPDFDragDrop !== a) { this._ctlPOPDFDragDrop = a } },
    // [002] code end
      get_lblCompanyStatus: function () { return this._lblCompanyStatus; }, set_lblCompanyStatus: function (v) { if (this._lblCompanyStatus !== v) this._lblCompanyStatus = v; },
      get_hypCM: function () { return this._hypCM; }, set_hypCM: function (v) { if (this._hypCM !== v) this._hypCM = v; },
      get_lblSupplierUpdated: function () { return this._lblSupplierUpdated; }, set_lblSupplierUpdated: function (v) { if (this._lblSupplierUpdated !== v) this._lblSupplierUpdated = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },
    // [006] code start
    get_ctlDragDropForPOR: function () { return this._ctlDragDropForPOR }, set_ctlDragDropForPOR: function (a) { if (this._ctlDragDropForPOR !== a) { this._ctlDragDropForPOR = a } },
    // [006] code end
    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Orders.PODetail.callBaseMethod(this, "initialize");
    },

    goInit: function() {
        if (this._btnPrint) this._btnPrint.addPrint(Function.createDelegate(this, this.printPO));
        if (this._btnPrint) this._btnPrint.addEmail(Function.createDelegate(this, this.emailPO));
        if (this._btnPrint) this._btnPrint.addExtraButtonClick(Function.createDelegate(this, this.printOtherDocs));
        if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        if (this._ctlMainInfo) this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_SaveEditComplete));
        if (this._ctlMainInfo) this._ctlMainInfo.addPotentialStatusChange(Function.createDelegate(this, this.ctlMainInfo_PotentialStatusChange));
        if (this._ctlLines) this._ctlLines.addPotentialStatusChange(Function.createDelegate(this, this.ctlLines_PotentialStatusChange));
        if (this._ctlMainInfo) this.setLineFieldsFromHeader();
        //[003] Code Start Here
        if (this._ctlLines) this._ctlLines.addGetDataComplete(Function.createDelegate(this, this.ctlLines_GetDataComplete));
        //[003] Code End Here
        // [001] code start
        if (this._ctlPODocuments) this._ctlPODocuments.getData();
        // [001] code end
        // [002] code start
        if (this._ctlPOPDFDragDrop) { this._ctlPOPDFDragDrop.getData() }
        // [002] code end
        // [002] code start
        if (this._ctlDragDropForPOR) { this._ctlDragDropForPOR.getData() }
        // [002] code end
        Rebound.GlobalTrader.Site.Pages.Orders.PODetail.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._btnPrint) this._btnPrint.dispose();
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        if (this._ctlLines) this._ctlLines.dispose();
        this._btnPrint = null;
        this._ctlMainInfo = null;
        this._ctlLines = null;
        this._pnlStatus = null;
        this._lblStatus = null;
        this._intPOID = null;
        // [001] code start
        this._ctlPODocuments = null;
        // [001] code end
        this._lblCompanyStatus = null;
        this._hypCM = null;
        this._lblSupplierUpdated = null;
        this._IsGlobalLogin = null;
        // [001] code start
        this._ctlDragDropForPOR = null;
        // [001] code end
        Rebound.GlobalTrader.Site.Pages.Orders.PODetail.callBaseMethod(this, "dispose");
    },

    printPO: function() {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrder, this._intPOID);
    },

    emailPO: function() {
        $R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrder, this._intPOID, true);
    },
    printOtherDocs: function() {
        if (this._btnPrint._strExtraButtonClickCommand == "EmailPOHTML") $R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrderEmail, this._intPOID, true);
        if (this._btnPrint._strExtraButtonClickCommand == "POR") $R_FN.openPrintWindow($R_ENUM$PrintObject.PurchaseOrderReport, this._intPOID);
        if (this._btnPrint._strExtraButtonClickCommand == "PrintLog") $R_FN.openPrintLogWindow($R_ENUM$PrintObject.PrintLog, this._intPOID, false, "PurchaseOrder");
        if (this._btnPrint._strExtraButtonClickCommand == "ExportToExcel") this.exportClicked();                                                         
    },

    ctlMainInfo_SaveEditComplete: function() {
        this._ctlLines.getTabData();
        this._ctlLines.enableDisableAddButton(this._ctlMainInfo._isIPO);
        this._ctlLines._ipoClientNo = this._ctlMainInfo._ipoClientNo;
        
    },
    //[003] Code Start Here
    ctlLines_GetDataComplete: function () {
       // alert(this._ctlLines._IsPOLineReceived);
        this._ctlMainInfo._IsPOLineReceived = this._ctlLines._IsPOLineReceived;
    },
    //[003] Code End Here
    ctlMainInfo_GetDataComplete: function () {
        
        this.setLineFieldsFromHeader();
        this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
        this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;

        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
        this._ctlLines.updateStatus(this._ctlMainInfo.getFieldValue("hidStatusNo"));
        this._ctlLines._ipoClientNo = this._ctlMainInfo._ipoClientNo;
        if (this._btnPrint) $R_FN.showElement(this._btnPrint._element, this._ctlMainInfo._blnIsApproved);
        this._ctlLines.enableDisableAddButton(this._ctlMainInfo._isIPO);
        //[004] start
        this._ctlLines._PONumber = this._ctlMainInfo._PONumber;
        var eprHtml = "";
        for (var i = 0; i < this._ctlMainInfo._POLineEPRIds.length; i++) {
            var row = this._ctlMainInfo._POLineEPRIds[i];
            eprHtml += $RGT_nubButton_EPR(this._ctlMainInfo._intPurchaseOrderID, row.POLineEPRIds, this._ctlMainInfo._PONumber); //$RGT_nubButton_DebitNote(row.DebitId, row1.DebitNumber);
            }
        $R_FN.setInnerHTML(this._ctlLines._pnlEPR, "");
        $R_FN.setInnerHTML(this._ctlLines._pnlEPR, eprHtml);
        $R_FN.showElement(this._ctlLines._pnlEPR, (eprHtml.length > 0));
        //[004] end
        this.CompanyStatus();
        
    },

    CompanyStatus: function(){
     
        $R_FN.showElement(this._lblCompanyStatus, false);
        $R_FN.showElement(this._lblSupplierUpdated, this._ctlMainInfo._lblCompanyStatus);
        $R_FN.setInnerHTML(this._lblSupplierUpdated, "Supplier Changed");
        
      //  $R_FN.setInnerHTML(this._hypCM,  this._ctlMainInfo.getFieldValue("hidCompanyNameType"));
        //if (this._ctlMainInfo._lblCompanyStatus) {
        //    $R_FN.setInnerHTML(this._hypCM, $RGT_nubButton_Company(this._ctlMainInfo.getFieldValue("hidSupplierNo"), this._ctlMainInfo.getFieldValue("hidCompanyNameType")));
        //} 
    },

    setLineFieldsFromHeader: function() {
        var strSupplierName = this._ctlMainInfo.getFieldValue("hidSupplierName");
        var strSupplierNotes = this._ctlMainInfo.getFieldValue("hidSupplierNotes");
        var strPONumber = this._ctlMainInfo.getFieldValue("hidPONumber");
        var strTotalShipInCostText = this._ctlMainInfo.getFieldValue("hidTotalShipInCostText");
        var strCurrencyCode = this._ctlMainInfo.getFieldValue("hidCurrencyCode");
        var intCurrencyNo = this._ctlMainInfo.getFieldValue("hidCurrencyNo");
        var strPODate = this._ctlMainInfo.getFieldValue("ctlDateOrdered");
        var MailGroupId = this._ctlMainInfo._MailGroupId;
        if (this._ctlLines._frmAdd) this._ctlLines._frmAdd.setFieldsFromHeader(strPONumber, strSupplierName, intCurrencyNo, strCurrencyCode, strTotalShipInCostText, strPODate, strSupplierNotes);
        if (this._ctlLines._frmEdit) this._ctlLines._frmEdit.setFieldsFromHeader(strPONumber, strSupplierName, strCurrencyCode, strTotalShipInCostText);
        if (this._ctlLines._frmPost) this._ctlLines._frmPost.setFieldsFromHeader(strPONumber, strSupplierName);
        if (this._ctlLines._frmDelete) this._ctlLines._frmDelete.setFieldsFromHeader(strPONumber, strSupplierName);
        if (this._ctlLines._frmDeallocate) this._ctlLines._frmDeallocate.setFieldsFromHeader(strPONumber, strSupplierName, MailGroupId);
        if (this._ctlLines._frmClose) this._ctlLines._frmClose.setFieldsFromHeader(strPONumber, strSupplierName);
      
    },

    ctlMainInfo_PotentialStatusChange: function() {
        this._ctlMainInfo.getData();
        this._ctlLines.getTabData();
        this._ctlLines.onRefreshAllocations();
       
    },

    ctlLines_PotentialStatusChange: function() {
        this._ctlMainInfo.getData();
        this._ctlLines.getTabData();
      
        
    },

    getStatus: function() {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/POMainInfo");
        obj.set_DataObject("POMainInfo");
        obj.set_DataAction("GetStatus");
        obj.addParameter("id", this._intPOID);
        obj.addDataOK(Function.createDelegate(this, this.getStatusOK));
        obj.addError(Function.createDelegate(this, this.getStatusError));
        obj.addTimeout(Function.createDelegate(this, this.getStatusError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getStatusOK: function(args) {
        var strStatus = $R_FN.setCleanTextValue(args._result.Status);
        this._ctlLines.updateStatus(args._result.StatusNo);
        this._ctlMainInfo.setFieldValue("hidStatus", strStatus);
        $R_FN.setInnerHTML(this._lblStatus, strStatus);
        $R_FN.showElement(this._pnlStatus, true);
        strStatus = null;
       
    },

    getStatusError: function(args) {
        $R_FN.showElement(this._pnlStatus, false);
    },
    //[005] start
    exportClicked: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/POMainInfo");
        obj.set_DataObject("POMainInfo");
        obj.set_DataAction("ExportPurchaseOrderReport");
        obj.addParameter("id", this._intPOID);
        obj._intTimeoutMilliseconds = 90 * 1000;
        obj.addDataOK(Function.createDelegate(this, this.exportComplete));
        obj.addError(Function.createDelegate(this, this.exportError));
        obj.addTimeout(Function.createDelegate(this, this.exportError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    exportError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    exportComplete: function (args) {
        if (args._result.Result == 1) {
            var dt = new Date();
            //location.href = String.format("{0}?t={1}", args._result.FileURL, dt.getTime());;
            var IsFileExists = false;
            IsFileExists = this.UrlExists((window.location.origin + '/' + args._result.FileURL));
            if (IsFileExists == true) {
                location.href = String.format("{0}?t={1}", args._result.FileURL, dt.getTime());
            }
            dt = null;
        } else {
            //alert("Error");
        }
        //this.getDataOK_End();
    },
    UrlExists: function (url) {
        var http = new XMLHttpRequest();
        http.open('HEAD', url, false);
        try {
            http.send();
        }
        catch (err) {
        }
        return http.status != 404;
    }
    //[005] end

};
Rebound.GlobalTrader.Site.Pages.Orders.PODetail.registerClass("Rebound.GlobalTrader.Site.Pages.Orders.PODetail", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
