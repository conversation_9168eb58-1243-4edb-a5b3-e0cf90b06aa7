﻿//Marker     Changed by      Date               Remarks
//[001]      Vinay           25/06/2013         CR:- Supplier Invoice
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns {
    public partial class SupplierInvoiceStatus : Base
    {

		protected override void OnLoad(EventArgs e) {
            SetDropDownType("SupplierInvoiceStatus");
            AddScriptReference("Controls.DropDowns.SupplierInvoiceStatus.SupplierInvoiceStatus");
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DropDowns.SupplierInvoiceStatus", ClientID);
			base.OnLoad(e);
		}

	}
}