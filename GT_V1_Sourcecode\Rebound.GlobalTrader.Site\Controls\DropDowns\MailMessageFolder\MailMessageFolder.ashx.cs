using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.Site.Controls.DropDowns.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class MailMessageFolder : Rebound.GlobalTrader.Site.Data.DropDowns.Base {

		public override void ProcessRequest(HttpContext context) {
			SetDropDownType("MailMessageFolder");
			base.ProcessRequest(context);
		}

		protected override void GetData() {
			JsonObject jsn = new JsonObject();
			JsonObject jsnList = new JsonObject(true);
			List<BLL.MailMessageFolder> lst = BLL.MailMessageFolder.DropDown(LoginID);
			for (int i = 0; i < lst.Count; i++) {
				JsonObject jsnItem = new JsonObject();
				jsnItem.AddVariable("ID", lst[i].MailMessageFolderId);
				jsnItem.AddVariable("Name", lst[i].Name);
				jsnList.AddVariable(jsnItem);
				jsnItem.Dispose(); jsnItem = null;
			}
			lst.Clear(); lst = null;
			jsn.AddVariable("Types", jsnList);
			jsnList.Dispose(); jsnList = null;
			OutputResult(jsn);
			jsn.Dispose(); jsn = null;
		}
	}
}
