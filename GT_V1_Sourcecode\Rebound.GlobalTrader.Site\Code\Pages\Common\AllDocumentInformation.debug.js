///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 09.09.2010:
// - pass Closed flag from MainInfo to the other nuggets
//
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages");

Rebound.GlobalTrader.Site.Pages.AllDocumentInformation = function (el) {
    Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.prototype = {

   
    get_ctlMainInfo: function() { return this._ctlMainInfo; }, set_ctlMainInfo: function(v) { if (this._ctlMainInfo !== v) this._ctlMainInfo = v; },
   
    initialize: function() {
        Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.callBaseMethod(this, "initialize");
    },

    goInit: function () {
        //if (this._ctlMainInfo) this._ctlMainInfo.addPartSelected(Function.createDelegate(this, this.selectPart));
        Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.callBaseMethod(this, "goInit");
    },
    selectPart: function () {
        
    },
    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.callBaseMethod(this, "dispose");
    }

};
Rebound.GlobalTrader.Site.Pages.AllDocumentInformation.registerClass("Rebound.GlobalTrader.Site.Pages.AllDocumentInformation", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
