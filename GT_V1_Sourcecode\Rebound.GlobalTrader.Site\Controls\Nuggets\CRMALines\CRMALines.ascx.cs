
/*

Marker     changed by      date         Remarks

[001]      Abhinav       17/11/20011   ESMS Ref:25 & 34  - Virtual Stock Update & Closeing of line CRMA
[RP-2339]	Ravi		11-10-2023		AS6081 GT Documents - Show AS6081 on detail screens

*/
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CRMALines : Base {

		#region Locals

		protected IconButton _ibtnEdit;
		protected IconButton _ibtnAdd;
		protected IconButton _ibtnDelete;
        //[001] code start
        private TabStrip _ctlTabs;
        protected Tab _ctlTabOpen;
        protected FlexiDataTable _tblOpen;
        protected Tab _ctlTabAll;
        protected Tab _ctlTabClosed;
        protected FlexiDataTable _tblClosed;
        protected IconButton _ibtnClose;
        //[001] code end 
		protected FlexiDataTable _tblAll;
		protected HyperLink _hypPrev;
		protected HyperLink _hypNext;
		protected Label _lblLineNumber;
		protected Panel _pnlLineDetail;
		protected Panel _pnlLoadingLineDetail;
		protected Panel _pnlLineDetailError;
		protected FieldSet _fldAllocations;
		protected FlexiDataTable _tblAllocations;
		protected FieldSet _fldReceived;
		protected FlexiDataTable _tblReceived;
		protected IconButton _ibtnDeallocate;

		#endregion

		#region Properties

		private int _intCRMAID = -1;
		public int CRMAID {
			get { return _intCRMAID; }
			set { _intCRMAID = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}

		private bool _blnCanDeallocate = true;
		public bool CanDeallocate {
			get { return _blnCanDeallocate; }
			set { _blnCanDeallocate = value; }
		}

        //[001] code start
        private bool _blnCanClose = true;
        public bool CanClose
        {
            get { return _blnCanClose; }
            set { _blnCanClose = value; }
        }

        private bool _blnCRMAClosed;
        public bool CRMAClosed
        {
            get { return _blnCRMAClosed; }
            set { _blnCRMAClosed = value; }
        }
        //[001] code end
		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CRMALines.CRMALines.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CRMALines");
			if (_objQSManager.CRMAID > 0) _intCRMAID = _objQSManager.CRMAID;
			SetupTables();
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnEdit.Visible = _blnCanEdit;
			_ibtnDelete.Visible = _blnCanDelete;
            //[001] code start
            _ibtnClose.Visible = _blnCanClose;
            //[001] code end
			_ibtnDeallocate.Visible = _blnCanDeallocate;
			SetupScriptDescriptors();
			((Rebound.GlobalTrader.Site.Pages.Base)Page).AddCSSFile("ItemSearch.css");
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMALines", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);
			if (_blnCanDeallocate) _scScriptControlDescriptor.AddElementProperty("ibtnDeallocate", _ibtnDeallocate.ClientID);
            //[001] code start
            if (_blnCanClose) _scScriptControlDescriptor.AddElementProperty("ibtnClose", _ibtnClose.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("ctlTabStrip", _ctlTabs.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblOpen", _tblOpen.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblClosed", _tblClosed.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblAll", _tblAll.ClientID);
            _scScriptControlDescriptor.AddProperty("blnCRMAClosed", _blnCRMAClosed);
            _scScriptControlDescriptor.AddProperty("intCRMAID", _intCRMAID);
            //[001] code end
			_scScriptControlDescriptor.AddComponentProperty("tblAll", _tblAll.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypPrev", _hypPrev.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypNext", _hypNext.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblLineNumber", _lblLineNumber.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLineDetail", _pnlLineDetail.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", _pnlLoadingLineDetail.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLineDetailError", _pnlLineDetailError.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("fldAllocations", _fldAllocations.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblAllocations", _tblAllocations.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("fldReceived", _fldReceived.ClientID);
			_scScriptControlDescriptor.AddComponentProperty("tblReceived", _tblReceived.ClientID);
		}

		private void SetupTables() {
			_tblAll.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
			_tblAll.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
			_tblAll.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
			_tblAll.Columns.Add(new FlexiDataColumn("Quantity", "QuantityReceived", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tblAll.Columns.Add(new FlexiDataColumn("Invoice", "InvoiceDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tblAll.Columns.Add(new FlexiDataColumn("ReturnDate", "Reason"));
			_tblAll.Columns.Add(new FlexiDataColumn("AS6081Required")); //[RP-2339]

			_tblOpen.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tblOpen.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblOpen.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
            _tblOpen.Columns.Add(new FlexiDataColumn("Quantity", "QuantityReceived", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
            _tblOpen.Columns.Add(new FlexiDataColumn("Invoice", "InvoiceDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tblOpen.Columns.Add(new FlexiDataColumn("ReturnDate", "Reason"));
			_tblOpen.Columns.Add(new FlexiDataColumn("AS6081Required")); //[RP-2339]

			_tblClosed.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
            _tblClosed.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
            _tblClosed.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
            _tblClosed.Columns.Add(new FlexiDataColumn("Quantity", "QuantityReceived", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
            _tblClosed.Columns.Add(new FlexiDataColumn("Invoice", "InvoiceDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
            _tblClosed.Columns.Add(new FlexiDataColumn("ReturnDate", "Reason"));
			_tblClosed.Columns.Add(new FlexiDataColumn("AS6081Required")); //[RP-2339]

			_tblReceived.Columns.Add(new FlexiDataColumn("GoodsInNo", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber)));
			_tblReceived.Columns.Add(new FlexiDataColumn("PartNo", "SupplierPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
			_tblReceived.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
			_tblReceived.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
			_tblReceived.Columns.Add(new FlexiDataColumn("QuantityReceived", "LandedCost", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tblReceived.Columns.Add(new FlexiDataColumn("Location", "ReceivedDate"));

			_tblAllocations.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
			_tblAllocations.Columns.Add(new FlexiDataColumn("Customer", "CustomerPO"));
			_tblAllocations.Columns.Add(new FlexiDataColumn("SalesOrder", "DatePromised", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tblAllocations.Columns.Add(new FlexiDataColumn("SRMA", "ReturnDate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date)));
			_tblAllocations.Columns.Add(new FlexiDataColumn("Quantity", "Price", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
			
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnAdd = FindIconButton("ibtnAdd");
			_ibtnEdit = FindIconButton("ibtnEdit");
			_ibtnDelete = FindIconButton("ibtnDelete");
            //[001] code start
            _ibtnClose = FindIconButton("ibtnClose");
            _ctlTabs = (TabStrip)ctlDesignBase.FindContentControl("ctlTabs");
            _ctlTabs.CreateControls();
            _ctlTabOpen = (Tab)ctlDesignBase.FindContentControl("ctlTabOpen");
            _tblOpen = (FlexiDataTable)_ctlTabOpen.FindContentControl("tblOpen");
            _ctlTabClosed = (Tab)ctlDesignBase.FindContentControl("ctlTabClosed");
            _tblClosed = (FlexiDataTable)_ctlTabClosed.FindContentControl("tblClosed");
            _ctlTabAll = (Tab)ctlDesignBase.FindContentControl("ctlTabAll");
            _tblAll = (FlexiDataTable)_ctlTabAll.FindContentControl("tblAll");
            //[001] code end 
			_tblAll = (FlexiDataTable)Functions.FindControlRecursive(this, "tblAll");
			_hypPrev = (HyperLink)Functions.FindControlRecursive(this, "hypPrev");
			_hypNext = (HyperLink)Functions.FindControlRecursive(this, "hypNext");
			_lblLineNumber = (Label)Functions.FindControlRecursive(this, "lblLineNumber");
			_pnlLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLineDetail");
			_pnlLoadingLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLoadingLineDetail");
			_pnlLineDetailError = (Panel)Functions.FindControlRecursive(this, "pnlLineDetailError");
			_fldAllocations = (FieldSet)Functions.FindControlRecursive(this, "fldAllocations");
			_ibtnDeallocate = (IconButton)_fldAllocations.FindButtonControl("ibtnDeallocate");
			_tblAllocations = (FlexiDataTable)_fldAllocations.FindContentControl("tblAllocations");
			_fldReceived = (FieldSet)Functions.FindControlRecursive(this, "fldReceived");
			_tblReceived = (FlexiDataTable)_fldReceived.FindContentControl("tblReceived");
		}
	}
}
