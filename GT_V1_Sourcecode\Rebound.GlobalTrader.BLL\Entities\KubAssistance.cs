﻿using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.DAL.SQLClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    //Anuj
    public partial class KubAssistance : BizObject
    {
        #region Properties
        public System.String PartNo { get; set; }
        public System.String TotalPartsTreaded { get; set; }
        public System.String TotalGP { get; set; }
        public System.String AverageBestPrice { get; set; }
        public System.String LastUpdatedDate { get; set; }
        public System.Boolean? IsClientPrice { get; set; }
        public System.String TotalGPbasedLastActualBuyPrice { get; set; }
        public System.String LowestSalesPriceLast12Months { get; set; }
        public System.String NumberOfPartsSoldLast12months { get; set; }
        public System.String LastEnquiredDateOfPart { get; set; }
        public System.String LastQuotedPrice { get; set; }
        public System.String LastSoldPrice { get; set; }
        public System.String LastHubprice { get; set; }
        public System.String NumberOfRequirement { get; set; }
        public System.String LastHighestSoldPrice { get; set; }
        public System.String LastLowestSoldPrice { get; set; }
        public System.String NumberOfInvoice { get; set; }
        public System.String LastestHubRFQName { get; set; }
        public System.DateTime LastestHubNumberDate { get; set; }
        public System.String LastestHubRFQId { get; set; }
        public System.String NumberOfQuote { get; set; }
        public System.String NumberQuoteToSalesOrder { get; set; }
        public System.String LastDatePartSoldToBomCustomer { get; set; }
        public System.String IHSResultForPartNo { get; set; }
        public System.String LyticaResultForPartNo { get; set; }

        #endregion

        #region Methods

        /// <summary>
        /// getKub Details
        /// Calls [sp_Kub]
        /// </summary>
        public static KubAssistance GetKubAssistanceDetails(System.String PartNo, System.Int32 ClientID)
        {
            KubAssistance obj = new KubAssistance();
            KubAssistanceDetails objDetails = new KubAssistanceDetails();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                objDetails = objSQLKubProvider.FetchKubAssistanceDetails(PartNo, ClientID);
                if (objDetails == null)
                {
                    return null;
                }
                else
                {
                    obj.PartNo = objDetails.PartNo;
                    obj.TotalPartsTreaded = objDetails.TotalPartsTreaded;
                    obj.TotalGP = objDetails.TotalGP;
                    obj.AverageBestPrice = objDetails.AverageBestPrice;
                    obj.LastUpdatedDate = objDetails.LastUpdatedDate;
                    obj.IsClientPrice = objDetails.IsClientPrice;
                }
                return obj;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                obj = null;
            }
        }

        /// <summary>
        /// getKub Details
        /// Calls [sp_Kub]
        /// </summary>
        public static KubAssistance ShowReadMoreData(System.String PartNo, System.Int32 ClientID)
        {
            KubAssistance obj = new KubAssistance();
            KubAssistanceDetails objDetails = new KubAssistanceDetails();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                objDetails = objSQLKubProvider.ShowReadMoreData(PartNo, ClientID);
                if (objDetails == null)
                {
                    return null;
                }
                else
                {
                    obj.TotalGPbasedLastActualBuyPrice = objDetails.TotalGPbasedLastActualBuyPrice;
                    obj.LowestSalesPriceLast12Months = objDetails.LowestSalesPriceLast12Months;
                    obj.NumberOfPartsSoldLast12months = objDetails.NumberOfPartsSoldLast12months;
                    obj.LastEnquiredDateOfPart = objDetails.LastEnquiredDateOfPart;
                    obj.LastQuotedPrice = objDetails.LastQuotedPrice;
                    obj.LastSoldPrice = objDetails.LastSoldPrice;
                    obj.LastHubprice = objDetails.LastHubprice;
                }
                return obj;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                obj = null;
            }
        }

        /// <summary>
        /// getKub Details
        /// Calls [sp_Kub]
        /// </summary>
        public static KubAssistance StartKubCache(System.String PartNo, System.Int32 ClientID)
        {
            KubAssistance obj = new KubAssistance();
            KubAssistanceDetails objDetails = new KubAssistanceDetails();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                objDetails = objSQLKubProvider.StartKubCache(PartNo, ClientID);
                if (objDetails == null)
                {
                    return null;
                }
                else
                {
                    //obj.PartNo = objDetails.PartNo;
                    //obj.TotalPartsTreaded = objDetails.TotalPartsTreaded;
                    //obj.TotalGP = objDetails.TotalGP;
                    //obj.AverageBestPrice = objDetails.AverageBestPrice;
                    //obj.TotalGPbasedLastActualBuyPrice = objDetails.TotalGPbasedLastActualBuyPrice;
                    //obj.LowestSalesPriceLast12Months = objDetails.LowestSalesPriceLast12Months;
                    //obj.NumberOfPartsSoldLast12months = objDetails.NumberOfPartsSoldLast12months;
                    //obj.LastEnquiredDateOfPart = objDetails.LastEnquiredDateOfPart;
                    //obj.LastQuotedPrice = objDetails.LastQuotedPrice;
                    //obj.LastSoldPrice = objDetails.LastSoldPrice;
                    //obj.LastHubprice = objDetails.LastHubprice;
                }
                return obj;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                obj = null;
            }
        }

        /// <summary>
        /// getKub Details for BOM Manager
        /// Calls [usp_select_KubAssistanceForBOMManager]
        /// </summary>
        public static KubAssistance LoadKubAssistanceForBOMManager(System.String PartNo, System.Int32 ClientID, int BOMManagerID, int manufactuerId, string manufacturerName, bool isHubRFQ = false)
        {
            KubAssistance obj = new KubAssistance();
            KubAssistanceDetails objDetails = new KubAssistanceDetails();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                objDetails = objSQLKubProvider.LoadKubAssistanceForBOMManager(PartNo, ClientID, BOMManagerID, isHubRFQ, manufactuerId, manufacturerName);
                if (objDetails == null)
                {
                    return obj;
                }
                else
                {
                    obj.NumberOfRequirement = objDetails.NumberOfRequirement;
                    obj.LastQuotedPrice = objDetails.LastQuotedPrice;
                    obj.LastHubprice = objDetails.LastHubprice;
                    obj.NumberOfInvoice = objDetails.NumberOfInvoice;
                    obj.LastestHubRFQName = objDetails.LastestHubRFQName;
                    obj.LastestHubNumberDate = objDetails.LastestHubNumberDate;
                    obj.LastestHubRFQId = objDetails.LastestHubRFQId;
                    obj.LastSoldPrice = objDetails.LastSoldPrice;
                    obj.LastHighestSoldPrice = objDetails.LastHighestSoldPrice;
                    obj.LastLowestSoldPrice = objDetails.LastLowestSoldPrice;
                    obj.NumberOfQuote = objDetails.NumberOfQuote;
                    obj.NumberQuoteToSalesOrder = objDetails.NumberQuoteToSalesOrder;
                    obj.LastUpdatedDate = objDetails.LastUpdatedDate;
                    obj.LastDatePartSoldToBomCustomer = objDetails.LastDatePartSoldToBomCustomer;
                    obj.IHSResultForPartNo = objDetails.IHSResultForPartNo;
                    obj.LyticaResultForPartNo = objDetails.LyticaResultForPartNo;
                }
                return obj;
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }

        #endregion
    }
}
