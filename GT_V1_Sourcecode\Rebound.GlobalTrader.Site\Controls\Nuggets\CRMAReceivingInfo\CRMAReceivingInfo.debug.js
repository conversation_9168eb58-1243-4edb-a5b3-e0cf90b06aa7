///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.initializeBase(this, [element]);
	this._intCRMAID = -1;
	this._intDataCalls = 0;
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.prototype = {

	get_intCRMAID: function() { return this._intCRMAID; }, set_intCRMAID: function(value) { if (this._intCRMAID !== value) this._intCRMAID = value; },
	get_fldReceived: function() { return this._fldReceived; }, 	set_fldReceived: function(v) { if (this._fldReceived !== v)  this._fldReceived = v; }, 
	get_tblReceived: function() { return this._tblReceived; }, 	set_tblReceived: function(v) { if (this._tblReceived !== v)  this._tblReceived = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.callBaseMethod(this, "initialize");	
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
		this._fldReceived.addShown(Function.createDelegate(this, this.onShownReceived));
		this._fldReceived.addRefresh(Function.createDelegate(this, this.onRefreshReceived));
		this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._fldReceived) this._fldReceived.dispose();
		if (this._tblReceived) this._tblReceived.dispose();
		this._intDataCalls = null;
		this._intCRMAID = null;
		this._fldReceived = null;
		this._tblReceived = null;
		Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.callBaseMethod(this, "dispose");
	},

	getData: function() { 
		this.getData_Start();
		this._intDataCalls += 1;
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CRMAMainInfo");
		obj.set_DataObject("CRMAMainInfo");
		obj.set_DataAction("GetData");
		obj.addParameter("id", this._intCRMAID);
		obj.addDataOK(Function.createDelegate(this, this.getDataOK));
		obj.addError(Function.createDelegate(this, this.getDataError));
		obj.addTimeout(Function.createDelegate(this, this.getDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		this.getReceived();
		obj = null;
	},

	getDataOK: function(args) { 
		var res = args._result;
		this.setFieldValue("hidNo", res.CRMANumber);
		this.setFieldValue("ctlCustomerName", $RGT_nubButton_Company(res.CustomerNo, res.Customer, null, null, null, res.CustomerAdvisoryNotes));
		this.setFieldValue("hidCustomer", $R_FN.setCleanTextValue(res.Customer));
		this.setFieldValue("hidCustomerNo", res.CustomerNo);
		this.setFieldValue("ctlWarehouse", $R_FN.setCleanTextValue(res.Warehouse));
		this.setFieldValue("hidWarehouseNo", res.WarehouseNo);
		this.setFieldValue("ctlContact", $RGT_nubButton_Contact(res.ContactNo, res.Contact));
		this.setFieldValue("hidContactNo", res.ContactNo);
		this.setFieldValue("ctlAuthoriser", $R_FN.setCleanTextValue(res.Authoriser));
		this.setFieldValue("hidAuthorisedBy", res.AuthorisedBy);
		this.setFieldValue("ctlDivision", $R_FN.setCleanTextValue(res.Division));
		this.setFieldValue("hidDivisionNo", res.DivisionNo);
		this.setFieldValue("ctlRMADate", res.RMADate);
		this.setFieldValue("ctlInvoice", res.Invoice, $RGT_nubButton_Invoice(res.InvoiceNo, res.Invoice));
		this.setFieldValue("hidInvoiceNo", res.InvoiceNo);
		this.setFieldValue("ctlSalesOrder", $RGT_nubButton_SalesOrder(res.SalesOrderNo, res.SalesOrder));
		this.setFieldValue("hidSalesOrderNo", res.SalesOrderNo);
		this.setFieldValue("ctlShipVia", $R_FN.setCleanTextValue(res.ShipVia));
		this.setFieldValue("hidShipViaNo", res.ShipViaNo);
		this.setFieldValue("ctlShippingAccountNo", $R_FN.setCleanTextValue(res.ShippingAccountNo));
		this.setFieldValue("ctlNotes", $R_FN.setCleanTextValue(res.Notes));
		this.setFieldValue("ctlInstructions", $R_FN.setCleanTextValue(res.Instructions));
		this.setFieldValue("ctlCurrency", $R_FN.setCleanTextValue(res.Currency));
		this.setFieldValue("hidCurrencyNo", res.CurrencyNo);
		this.setFieldValue("hidGlobalClientNo", res.ClientNo);
		//alert(res.ClientNo);
		this.setDLUP(res.DLUP);
		this.getDataOK_End();
		this.finishDataCall();
	},

	getDataError: function(args) {
		this.showError(true, args.get_ErrorMessage());
		this.finishDataCall();
	},
	
	getReceived: function() {
		this._intDataCalls += 1;
		this.showLoading(true);
		this._fldReceived.showLoading(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CRMAMainInfo");
		obj.set_DataObject("CRMAMainInfo");
		obj.set_DataAction("GetReceived");
		obj.addParameter("id", this._intCRMAID);
		obj.addDataOK(Function.createDelegate(this, this.getReceivedOK));
		obj.addError(Function.createDelegate(this, this.getReceivedError));
		obj.addTimeout(Function.createDelegate(this, this.getReceivedError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getReceivedError: function(args) {
		this._fldReceived.showError(true, args.get_ErrorMessage());
		this.finishDataCall();
	},
	
	getReceivedOK: function(args) {
		this._intDataCalls -= 1;
		if (this._intDataCalls < 1) this.showLoading(false);
		var res = args._result;
		this._fldReceived.showContent(true);
		this._fldReceived.resetCount();
		this._tblReceived.clearTable();
		this._fldReceived.updateCount(res.Count);
		if (res.Received) {
			for (var i = 0; i < res.Received.length; i++) {
				var row = res.Received[i];
				var aryData = [
					$RGT_nubButton_GoodsIn(row.GoodsInNo, row.GoodsInNumber)
					,	$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(row.StockNo, row.PartNo, row.ROHS), $R_FN.setCleanTextValue(row.SupplierPart))
					, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes)), $R_FN.setCleanTextValue(row.DC))
					,   $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue($R_FN.setCleanTextValue(row.Product)), $R_FN.setCleanTextValue(row.Package))
					,   $R_FN.writeDoubleCellValue(row.Quantity, $R_FN.setCleanTextValue(row.Location))
					,   $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Receiver), $R_FN.setCleanTextValue(row.ReceivedDate))
			    ];
				this._tblReceived.addRow(aryData, row.GoodsInNo, false);
				row = null; 
			}
		}
		this._tblReceived.resizeColumns();
		this.finishDataCall();
	},

	onShownReceived: function() {
		this._tblReceived.resizeColumns();
	},
	
	onRefreshReceived: function() {
		this.getReceived();
	},
	
	finishDataCall: function() {
		this._intDataCalls -= 1;
		if (this._intDataCalls < 1) this.showLoading(false);
	}
};

Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingInfo", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
