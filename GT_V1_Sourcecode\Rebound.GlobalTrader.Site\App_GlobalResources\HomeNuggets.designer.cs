//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class HomeNuggets {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal HomeNuggets() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.HomeNuggets", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Purchase Orders Due In.
        /// </summary>
        internal static string AllPurchaseOrdersDueIn {
            get {
                return ResourceManager.GetString("AllPurchaseOrdersDueIn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Purchase Orders Due In (Within 7 Days).
        /// </summary>
        internal static string AllPurchaseOrdersDueInWithin7Days {
            get {
                return ResourceManager.GetString("AllPurchaseOrdersDueInWithin7Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All Sales Orders Ready To Ship (Within 7 Days).
        /// </summary>
        internal static string AllSalesOrdersReadyToShipWithin7Days {
            get {
                return ResourceManager.GetString("AllSalesOrdersReadyToShipWithin7Days", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Statistics (as of last day&apos;s trading).
        /// </summary>
        internal static string AllStatistics {
            get {
                return ResourceManager.GetString("AllStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approved Customers On Stop.
        /// </summary>
        internal static string ApprovedCustomersOnStop {
            get {
                return ResourceManager.GetString("ApprovedCustomersOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HUBRFQ.
        /// </summary>
        internal static string Bom {
            get {
                return ResourceManager.GetString("Bom", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager.
        /// </summary>
        internal static string BOMManager {
            get {
                return ResourceManager.GetString("BOMManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Order Value.
        /// </summary>
        internal static string CustomerOrderValue_ForUser {
            get {
                return ResourceManager.GetString("CustomerOrderValue_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Approved Customers On Stop.
        /// </summary>
        internal static string MyApprovedCustomersOnStop {
            get {
                return ResourceManager.GetString("MyApprovedCustomersOnStop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Approved Customers On Stop.
        /// </summary>
        internal static string MyApprovedCustomersOnStop_ForUser {
            get {
                return ResourceManager.GetString("MyApprovedCustomersOnStop_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My GI Queries.
        /// </summary>
        internal static string MyGIQueries {
            get {
                return ResourceManager.GetString("MyGIQueries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Messages.
        /// </summary>
        internal static string MyMessages {
            get {
                return ResourceManager.GetString("MyMessages", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Open Purchase Orders.
        /// </summary>
        internal static string MyOpenPurchaseOrders {
            get {
                return ResourceManager.GetString("MyOpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Open Purchase Orders.
        /// </summary>
        internal static string MyOpenPurchaseOrders_ForUser {
            get {
                return ResourceManager.GetString("MyOpenPurchaseOrders_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Open Quotes.
        /// </summary>
        internal static string MyOpenQuotes {
            get {
                return ResourceManager.GetString("MyOpenQuotes", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Open Quotes.
        /// </summary>
        internal static string MyOpenQuotes_ForUser {
            get {
                return ResourceManager.GetString("MyOpenQuotes_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Open Requirements.
        /// </summary>
        internal static string MyOpenRequirements {
            get {
                return ResourceManager.GetString("MyOpenRequirements", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Open Requirements.
        /// </summary>
        internal static string MyOpenRequirements_ForUser {
            get {
                return ResourceManager.GetString("MyOpenRequirements_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Open Sales Orders.
        /// </summary>
        internal static string MyOpenSalesOrders {
            get {
                return ResourceManager.GetString("MyOpenSalesOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Open Sales Orders.
        /// </summary>
        internal static string MyOpenSalesOrders_ForUser {
            get {
                return ResourceManager.GetString("MyOpenSalesOrders_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Team GI Queries.
        /// </summary>
        internal static string MyQualityGIQueries {
            get {
                return ResourceManager.GetString("MyQualityGIQueries", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Recent Activity.
        /// </summary>
        internal static string MyRecentActivity {
            get {
                return ResourceManager.GetString("MyRecentActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Recent Activity.
        /// </summary>
        internal static string MyRecentActivity_ForUser {
            get {
                return ResourceManager.GetString("MyRecentActivity_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Recently Received Orders.
        /// </summary>
        internal static string MyRecentlyReceivedOrders {
            get {
                return ResourceManager.GetString("MyRecentlyReceivedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Recently Received Orders.
        /// </summary>
        internal static string MyRecentlyReceivedOrders_ForUser {
            get {
                return ResourceManager.GetString("MyRecentlyReceivedOrders_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Recently Shipped Orders.
        /// </summary>
        internal static string MyRecentlyShippedOrders {
            get {
                return ResourceManager.GetString("MyRecentlyShippedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Recently Shipped Orders.
        /// </summary>
        internal static string MyRecentlyShippedOrders_ForUser {
            get {
                return ResourceManager.GetString("MyRecentlyShippedOrders_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Scheduled Tasks.
        /// </summary>
        internal static string MyScheduledTasks {
            get {
                return ResourceManager.GetString("MyScheduledTasks", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Statistics (as of last day&apos;s trading).
        /// </summary>
        internal static string MyStatistics {
            get {
                return ResourceManager.GetString("MyStatistics", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Statistics (as of last day&apos;s trading).
        /// </summary>
        internal static string MyStatistics_ForUser {
            get {
                return ResourceManager.GetString("MyStatistics_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My To Do List.
        /// </summary>
        internal static string MyToDoList {
            get {
                return ResourceManager.GetString("MyToDoList", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Supplier Approvals.
        /// </summary>
        internal static string OpenSupplierPOApproval {
            get {
                return ResourceManager.GetString("OpenSupplierPOApproval", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Orders Due Out.
        /// </summary>
        internal static string OrdersDueOut {
            get {
                return ResourceManager.GetString("OrdersDueOut", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Order Due Out.
        /// </summary>
        internal static string OrdersDueOut_ForUser {
            get {
                return ResourceManager.GetString("OrdersDueOut_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Parts Being Ordered Today.
        /// </summary>
        internal static string PartsBeingOrderedToday {
            get {
                return ResourceManager.GetString("PartsBeingOrderedToday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Parts Being Ordered Today.
        /// </summary>
        internal static string PartsBeingOrderedToday_ForUser {
            get {
                return ResourceManager.GetString("PartsBeingOrderedToday_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Delivery Status.
        /// </summary>
        internal static string PODeliveryStatus {
            get {
                return ResourceManager.GetString("PODeliveryStatus", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to My Power BI Report Activity.
        /// </summary>
        internal static string PowerBIActivity {
            get {
                return ResourceManager.GetString("PowerBIActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0}&apos;s Power BI Report Activity.
        /// </summary>
        internal static string PowerBIActivity_ForUser {
            get {
                return ResourceManager.GetString("PowerBIActivity_ForUser", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PowerBI Sales Dashboard.
        /// </summary>
        internal static string PowerBISalesDashboard {
            get {
                return ResourceManager.GetString("PowerBISalesDashboard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Received Orders.
        /// </summary>
        internal static string ReceivedOrders {
            get {
                return ResourceManager.GetString("ReceivedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Orders Ready To Ship.
        /// </summary>
        internal static string SalesOrdersReadyToShip {
            get {
                return ResourceManager.GetString("SalesOrdersReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shipped Orders.
        /// </summary>
        internal static string ShippedOrders {
            get {
                return ResourceManager.GetString("ShippedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Items Below The Minimum Quantity.
        /// </summary>
        internal static string StockItemsBelowTheMinimumQuantity {
            get {
                return ResourceManager.GetString("StockItemsBelowTheMinimumQuantity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recent Activity.
        /// </summary>
        internal static string TableActivity {
            get {
                return ResourceManager.GetString("TableActivity", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un-Approved Purchase Orders.
        /// </summary>
        internal static string TodayOpenPurchaseOrders {
            get {
                return ResourceManager.GetString("TodayOpenPurchaseOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today&apos;s Received Orders.
        /// </summary>
        internal static string TodaysReceivedOrders {
            get {
                return ResourceManager.GetString("TodaysReceivedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Today&apos;s Shipped Orders.
        /// </summary>
        internal static string TodaysShippedOrders {
            get {
                return ResourceManager.GetString("TodaysShippedOrders", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Top Salespeople This Month.
        /// </summary>
        internal static string TopSalespersons {
            get {
                return ResourceManager.GetString("TopSalespersons", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to IPOs waiting SO approval.
        /// </summary>
        internal static string UncheckedIPO {
            get {
                return ResourceManager.GetString("UncheckedIPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un-Process Sales Orders.
        /// </summary>
        internal static string UnProcessSalesOrders {
            get {
                return ResourceManager.GetString("UnProcessSalesOrders", resourceCulture);
            }
        }
    }
}
