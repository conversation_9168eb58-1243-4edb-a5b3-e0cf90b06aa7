///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 11.01.2010:
// - change fields returned
//
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.initializeBase(this, [element]);
	this._intQuoteID = 0;
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.prototype = {
  
	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	
	},

	dispose: function() { 
		if (this.isDisposed) return;
		
		Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.callBaseMethod(this, "dispose");
	},

	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/CusReqSourcingResults");
		this._objData.set_DataObject("CusReqSourcingResults");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Part", this.getFieldValue("ctlPartNo"));
		this._objData.addParameter("CM", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("ReqNoLo", this.getFieldValue_Min("ctlReqNo"));
		this._objData.addParameter("ReqNoHi", this.getFieldValue_Max("ctlReqNo"));
		this._objData.addParameter("Supplier", this.getFieldValue("ctlSupplier"));
		this._objData.addParameter("IsPoHub", this.getFieldValue("ctlIsPoHub"));
		this._objData.addParameter("intQuoteID", this._intQuoteID);
		this._objData.addParameter("BOM", this.getFieldValue("ctlBOM"));
	},
	
	doGetDataComplete: function() {		
		
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
            var row = this._objResult.Results[i];
           // alert(row.isRestrictedManufacturer);
			var aryData = [
				$R_FN.writeDoubleCellValue(String.format("{0} - {1}", row.No, $R_FN.setCleanTextValue(row.CMName)), $R_FN.setCleanTextValue(row.Supplier))
				, $R_FN.writePartNo(row.Part, row.ROHS)
                //, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Mfr), $R_FN.setCleanTextValue(row.DC))
                , $R_FN.writeDoubleCellValue((row.isRestrictedManufacturer == 1) ? "<span   style=\"background-color: #ff0000; color:#282424f2; background-position: right -1px; !important\" title=\"this manufacturer is restricted use\"> Restricted Use </span>" : $R_FN.setCleanTextValue(row.Mfr), $R_FN.setCleanTextValue(row.DC))
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
				, $R_FN.setCleanTextValue(row.OfferDate)
				, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Quantity), $R_FN.setCleanTextValue(row.Price))
				, $R_FN.setCleanTextValue(row.IsPoHub)
            ];
            var xtraData = {
                isRestrictedManufacturer: row.isRestrictedManufacturer
               
            };
            
            //this._tblResults.addRow(aryData, row.ID, (row.ID == this._intInitialID));
            var strCSS = (row.isRestrictedManufacturer) ? "ceased2" : "";
            this._tblResults.addRow(aryData, row.ID, (row.ID == this._intInitialID), xtraData, strCSS);
            aryData = null;
            row = null;
		}
	}
		
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.CustomerRequirementSourcingResults", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
