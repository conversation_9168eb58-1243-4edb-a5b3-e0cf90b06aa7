using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class EPRNotify : Rebound.GlobalTrader.Site.Data.Base
    {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					//case "AddNew": AddNew(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Add new purchaseOrder
		/// </summary>
        //public void AddNew() {
        //    try {
        //        int intNewPurchaseOrderID = PurchaseOrder.Insert(
        //            SessionManager.ClientID,
        //            GetFormValue_NullableInt("CMNo"),
        //            GetFormValue_NullableInt("ContactNo"),
        //            GetFormValue_NullableDateTime("DateOrdered", DateTime.Now),
        //            GetFormValue_NullableInt("WarehouseNo"),
        //            GetFormValue_NullableInt("CurrencyNo"),
        //            GetFormValue_NullableInt("BuyerNo"),
        //            GetFormValue_NullableInt("ShipViaNo"),
        //            GetFormValue_String("Account"),
        //            GetFormValue_NullableInt("TermsNo"),
        //            null,
        //            null,
        //            GetFormValue_NullableDouble("TotalShipInCost"),
        //            GetFormValue_NullableInt("DivisionNo"),
        //            GetFormValue_NullableInt("TaxNo"),
        //            GetFormValue_String("Notes"),
        //            GetFormValue_String("Instructions"),
        //            false,
        //            GetFormValue_Boolean("Confirmed"),
        //            GetFormValue_NullableInt("PurchaseOrderImportCountryNo"),
        //            GetFormValue_String("FreeOnBoard"),
        //            0,
        //            false,
        //            GetFormValue_NullableInt("IncotermNo"),
        //            LoginID
        //        );

        //        if (intNewPurchaseOrderID > 0) {
        //            int intSalesOrderLineID = Convert.ToInt32(GetFormValue_NullableInt("SOLineID"));
        //            //copy SO line to PO if we have come from a Pur Req
        //            if (intSalesOrderLineID > 0) {
        //                int intNewPurchaseOrderLineID = InsertPurchaseOrderLineFromSalesOrderLine(intNewPurchaseOrderID, intSalesOrderLineID);
        //                //now allocate
        //                Stock stk = Stock.GetForPurchaseOrderLine(intNewPurchaseOrderLineID);
        //                int intNewAllocationID = Allocation.Insert(
        //                      stk.StockId
        //                    , intSalesOrderLineID
        //                    , stk.QuantityOnOrder
        //                    , null
        //                    , LoginID
        //                );
        //                stk = null;
        //            }
        //            JsonObject jsn = new JsonObject();
        //            jsn.AddVariable("NewID", intNewPurchaseOrderID);
        //            OutputResult(jsn);
        //            jsn.Dispose();
        //            jsn = null;
        //        } else {
        //            WriteErrorSQLActionFailed("Insert");
        //        }
        //    } catch (Exception e) {
        //        WriteError(e);
        //    }
        //}

        //private int InsertPurchaseOrderLineFromSalesOrderLine(int intNewPurchaseOrderID, int intSalesOrderLineID) {
        //    return PurchaseOrderLine.InsertFromSalesOrderLine(intSalesOrderLineID, intNewPurchaseOrderID, LoginID);
        //}
	}
}
