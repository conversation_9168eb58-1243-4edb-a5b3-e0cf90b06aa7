///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals.initializeBase(this, [element]);
    this._intLineID = -1;
    this._strQualityApprove = null;
    this._strTitle_ExportApprove = null;
    this._lblExplainQualityApprove = null;
    this._lblExplainQualityDecline = null;
    this._lblExplainExportApprove = null;
    this._lblExplainLineManagerDecline = null;
    this._lblExplainLineManagerIndpdt = null;
    this._lblExplainQualityEscalate = null;
    this._aryUnpostedLineIDs = [];
    this._aryPostedLineIDs = [];
    this._intExportApprovalID = 0;
    this._ctlConfirm = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals.prototype = {

    get_intExportApprovalID: function () { return this._intExportApprovalID; }, set_intExportApprovalID: function (v) { if (this._intExportApprovalID !== v) this._intExportApprovalID = v; },
    get_intLineID: function () { return this._intLineID; }, set_intLineID: function (value) { if (this._intLineID !== value) this._intLineID = value; },
    
    get_strTitle_ExportApprove: function () { return this._strTitle_ExportApprove; }, set_strTitle_ExportApprove: function (value) { if (this._strTitle_ExportApprove !== value) this._strTitle_ExportApprove = value; },
    get_lblExplainExportApprove: function () { return this._lblExplainExportApprove; }, set_lblExplainExportApprove: function (value) { if (this._lblExplainExportApprove !== value) this._lblExplainExportApprove = value; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
       
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            
            this.getFieldDropDownData("ctlOGELNumber");
            //this.getFieldDropDownData("ctlMilitaryuse");
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.approveClicked));
            this._ctlConfirm.addClickRejectEvent(Function.createDelegate(this, this.rejectClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
            document.getElementById("ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmApprovals_ctlDB_ctlEUUForm_ctl03_ibtnGeneratePDF_hyp").style.color = "white";
            $addHandler($get("ctl00_cphMain_ctlExportApprovalStatus_ctlDB_ctl14_frmApprovals_ctlDB_ctlEUUForm_ctl03_ibtnGeneratePDF_hyp"), "click", this.createPDF);
            
        }
        SetExportApprovalId(this._intLineID);
        this.getExportApprovalData();

        if (this._mode == "QUALITY_APPROVE" || this._mode == "QUALITY_DECLINE" || this._mode == "QUALITY_ESCALATE" || this._mode == "LINEMANAGER_INDEPDNTTEST") {
            //this.showField("ctlSAPartERAI", true);
            if (!this._IsEscalate) {
                this.showField("ctlLineManagerNote", true);
                this.showField("ctlQualityNote", false);
            }
            else {
                this.showField("ctlLineManagerNote", false);
                this.showField("ctlRequesterNote", false);
                this.showField("ctlQualityNote", true);
            }
            
        }
        else {

        }

        this.checkMode();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intLineID = null;
        this._strTitle_ExportApprove = null;
        this._lblExplainExportApprove = null;
        this._aryUnpostedLineIDs = null;
        this._aryPostedLineIDs = null;
        this._intExportApprovalID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.POLines_Post.callBaseMethod(this, "dispose");
    },

    approveClicked: function () {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
        obj.set_DataObject("ExportApprovalStatus");
        obj.set_DataAction("ApproveExportRequest");
        obj.addParameter("id", this._intLineID);
        obj.addParameter("OGELNumber", this.getFieldValue("ctlOGELNumber"));
        obj.addParameter("MilitaryUse", 0);
        obj.addParameter("EndUser", this.getFieldValue("ctlEndUser"));
        obj.addParameter("Comment", this.getFieldValue("ctlComment"));
        obj.addParameter("SendEmail", this.getFieldValue("ctlSendMail"));
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },
    rejectClicked: function () {
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
        obj.set_DataObject("ExportApprovalStatus");
        obj.set_DataAction("RejectExportRequest");
        obj.addParameter("id", this._intLineID);
        obj.addParameter("OGELNumber", this.getFieldValue("ctlOGELNumber"));
        obj.addParameter("MilitaryUse", 0);
        obj.addParameter("EndUser", this.getFieldValue("ctlEndUser"));
        obj.addParameter("Comment", this.getFieldValue("ctlComment"));
        obj.addParameter("SendEmail", this.getFieldValue("ctlSendMail"));
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
        this.onSave();
    },
    noClicked: function () {
        this.showSaving(false);
        this.onNotConfirmed();
        this.setFieldValue("ctlOGELNumber", '');
        //this.setFieldValue("ctlMilitaryuse", '');
        this.setFieldValue("ctlEndUser", '');
        this.setFieldValue("ctlComment", '');
    },

    saveError: function (args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function (args) {
        this.showSaving(false);
        if (args._result.Result == true) {
            this.onSaveComplete();
            this.setFieldValue("ctlOGELNumber", '');
            //this.setFieldValue("ctlMilitaryuse",'');
            this.setFieldValue("ctlEndUser", '');
            this.setFieldValue("ctlComment", '');
            setTimeout(function () {
                $("#ctl00_cphMain_ctlAuthorisation_ctlDB_imgRefresh").trigger('click');
            }, 1500);
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    checkMode: function () {
        switch (this._mode) {
            case "LINEMANAGER_APPROVE": this.changeTitle(this._strTitle_ExportApprove); break;  
        }
        //show correct explanation
        $R_FN.showElement(this._lblExplainExportApprove, this._mode == "LINEMANAGER_APPROVE");
    },

    setFieldsFromHeader: function (strPONumber, strSupplierName) {
        this.setFieldValue("ctlPurchaseOrder", strPONumber);
        this.setFieldValue("ctlSalesPerson", strSupplierName);
    },
    getExportApprovalData: function () {

        this.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ExportApprovalStatus");
        obj.set_DataObject("ExportApprovalStatus");
        obj.set_DataAction("GetExportApprovalData");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getExportApprovalDataOK));
        obj.addError(Function.createDelegate(this, this.getExportApprovalDataError));
        obj.addTimeout(Function.createDelegate(this, this.getExportApprovalDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getExportApprovalDataError: function (args) {
        this.showInnerContent(true);
        this.showLoading(false);
    },

    getExportApprovalDataOK: function (args) {
        var result = args._result;
        this.setFieldValue("ctlSalesPerson", result.SalesmanName);
        this.setFieldValue("ctlSalesOrder", result.SalesOrderNo);
        this.setFieldValue("ctlSOLineNo", result.SOSerialNo);
        this.setFieldValue("ctlCustomer", result.CustomerName);
        this.setFieldValue("ctlPartNumber", result.Part);
        this.setFieldValue("ctlDestinationCountry", result.DestinationCountry);
        this.setFieldValue("ctlMilitaryuseName", result.MilitaryUseName);
        this.setFieldValue("ctlEndUser", result.EndUser);
        if (result.ISPdfAttached == true) {
            this.showField("ctlEUUForm", true);
            this.showField("ctlEUUFormLbl", false);
        } else {
            this.showField("ctlEUUForm", false);
            this.showField("ctlEUUFormLbl", true);
        }
        
        this.showInnerContent(true);
        this.showLoading(false);
    },

    createPDF: function () {
        var ipdf = GetExportApprovalId();
        var baseUrl = (window.location).href;
        var url = new URL(baseUrl);
        $R_FN.openPrintWindow($R_ENUM$PrintObject.EUUForm, ipdf);
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ExportApprovalStatus_Approvals", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
