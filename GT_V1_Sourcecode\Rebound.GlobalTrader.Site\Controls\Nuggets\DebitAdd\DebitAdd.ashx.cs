//Marker     Changed by      Date         Remarks
//[001]      Vinay           30/07/2012   Add compulsory incoterms field when create Credit and debit note. :ESMS No:- 105
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class DebitAdd : Rebound.GlobalTrader.Site.Data.Base {
		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "AddNew": AddNew(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

		/// <summary>
		/// Add new debit
		/// </summary>
		public void AddNew() {
			try {
                int? intGlobalClientNo;
                intGlobalClientNo = GetFormValue_NullableInt("GlobalClientNo");

				int intResult = Debit.Insert(
                    (intGlobalClientNo.HasValue && intGlobalClientNo.Value > 0) ? intGlobalClientNo.Value : SessionManager.ClientID,
					GetFormValue_Int("CMNo"),
					GetFormValue_Int("ContactNo"),
					GetFormValue_DateTime("DebitDate"),
					GetFormValue_Int("CurrencyNo"),
					GetFormValue_NullableInt("RaisedBy"),
					GetFormValue_NullableInt("Buyer"),
					GetFormValue_String("Notes"),
					GetFormValue_String("Instructions"),
					GetFormValue_NullableDouble("Freight"),
					GetFormValue_NullableInt("DivisionNo"),
					GetFormValue_NullableInt("TaxNo"),
					GetFormValue_NullableInt("PurchaseOrderNo"),
					GetFormValue_NullableInt("SupplierRMANo"),
					GetFormValue_DateTime("ReferenceDate"),
					GetFormValue_String("SupplierInvoice"),
					GetFormValue_String("SupplierReturn"),
					GetFormValue_String("SupplierCredit"),
					LoginID,
                    //[001] code start
                    GetFormValue_NullableInt("IncotermNo"),
					FileUploadManager.GetDocumentHeaderImageNameForAdd(GetFormValue_Int("DivisionNo"), true, (int)SessionManager.ClientID)
					//[001] code end
					);
				if (intResult > 0) {
					//return result
					JsonObject jsn = new JsonObject();
					jsn.AddVariable("NewID", intResult);
					OutputResult(jsn);
					jsn.Dispose();
					jsn = null;
				} else {
					WriteErrorSQLActionFailed("Insert");
				}
			} catch (Exception e) {
				WriteError(e);
			}
		}
	}
}