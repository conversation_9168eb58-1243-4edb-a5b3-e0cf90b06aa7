using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;


namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class BOMImport_Form : Base
    {

        #region Locals
        protected FlexiDataTable _tblPartdetails;
        #endregion

        #region Properties

        private int _intBOMID = -1;
        public int BOMID
        {
            get { return _intBOMID; }
            set { _intBOMID = value; }
        }
       
        #endregion

        #region Overrides
        /// <summary>
        /// Oninit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            WireUpControls();
            SetupTables();
            AddScriptReference("Controls.Nuggets.BOMImport.BOMImport_Form.BOMImport_Form.js");
            TitleText = Functions.GetGlobalResource("Nuggets", "BOMImport_Form");
            if (_objQSManager.BOMID > 0) _intBOMID = _objQSManager.BOMID;
        }

        protected override void OnPreRender(EventArgs e)
        {
           
            base.OnPreRender(e);
        }

        protected override void OnLoad(EventArgs e)
        {
            SetupScriptDescriptors();
            base.OnLoad(e);
        }


        #endregion

        private void SetupTables()
        {

            _tblPartdetails.AllowSelection = true;
            _tblPartdetails.Columns.Add(new FlexiDataColumn("PartNo", WidthManager.GetWidth(WidthManager.ColumnWidth.Warehouse)));
            _tblPartdetails.Columns.Add(new FlexiDataColumn("Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.Warehouse)));
            _tblPartdetails.Columns.Add(new FlexiDataColumn("Product", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));
            _tblPartdetails.Columns.Add(new FlexiDataColumn("Package", WidthManager.GetWidth(WidthManager.ColumnWidth.DateAndTime)));
            _tblPartdetails.Columns.Add(new FlexiDataColumn("Datecode", Unit.Empty, true));
           
        }

        /// <summary>
        /// Sets up AJAX script descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.BOMImport_Form", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("intBOMID", _intBOMID);
            _scScriptControlDescriptor.AddComponentProperty("tblPartdetails", FindFieldControl("ctlPartDetail", "tblPartdetails").ClientID);
            _scScriptControlDescriptor.AddElementProperty("btn1", FindFieldControl("ctlPartNo", "btn1").ClientID);
            _scScriptControlDescriptor.AddElementProperty("btn2", FindFieldControl("ctlPartNo", "btn2").ClientID);
            _scScriptControlDescriptor.AddElementProperty("lblError", FindFieldControl("ctlPartNo", "lblError").ClientID);
            _scScriptControlDescriptor.AddProperty("intLoginID", (Int32)SessionManager.LoginID);
        }

        /// <summary>
        /// Wire up controls from the ascx
        /// </summary>
        private void WireUpControls()
        {
            _tblPartdetails = (FlexiDataTable)ctlDesignBase.FindContentControl("tblPartdetails");
        }

    }
}
