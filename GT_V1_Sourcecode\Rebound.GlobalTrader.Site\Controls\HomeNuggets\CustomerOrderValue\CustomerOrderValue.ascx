<%@ Control Language="C#" CodeBehind="CustomerOrderValue.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.HomeNuggets.CustomerOrderValue" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase id="ctlDB" runat="server">
	<Content>
		<div class="homepageNugget">
			<ReboundUI:SimpleDataTable ID="tblCustomerOrder" runat="server" AllowSelection="false" />
		</div>
	</Content>
</ReboundUI_Nugget:DesignBase>
