///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.initializeBase(this, [element]);
	this._aryToDoIDs = [];
};

Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.prototype = {

	get_aryToDoIDs: function() { return this._aryToDoIDs; }, 	set_aryToDoIDs: function(v) { if (this._aryToDoIDs !== v)  this._aryToDoIDs = v; }, 
	get_strTitle_Delete: function() { return this._strTitle_Delete; }, 	set_strTitle_Delete: function(v) { if (this._strTitle_Delete !== v)  this._strTitle_Delete = v; }, 
	get_strTitle_MarkComplete: function() { return this._strTitle_MarkComplete; }, 	set_strTitle_MarkComplete: function(v) { if (this._strTitle_MarkComplete !== v)  this._strTitle_MarkComplete = v; }, 
	get_strTitle_MarkIncomplete: function() { return this._strTitle_MarkIncomplete; }, 	set_strTitle_MarkIncomplete: function(v) { if (this._strTitle_MarkIncomplete !== v)  this._strTitle_MarkIncomplete = v; }, 
	get_strExplanation_Delete: function() { return this._strExplanation_Delete; }, 	set_strExplanation_Delete: function(v) { if (this._strExplanation_Delete !== v)  this._strExplanation_Delete = v; }, 
	get_strExplanation_MarkComplete: function() { return this._strExplanation_MarkComplete; }, 	set_strExplanation_MarkComplete: function(v) { if (this._strExplanation_MarkComplete !== v)  this._strExplanation_MarkComplete = v; }, 
	get_strExplanation_MarkIncomplete: function() { return this._strExplanation_MarkIncomplete; }, 	set_strExplanation_MarkIncomplete: function(v) { if (this._strExplanation_MarkIncomplete !== v)  this._strExplanation_MarkIncomplete = v; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
		this.addModeChanged(Function.createDelegate(this, this.modeChanged));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlConfirm) this._ctlConfirm.dispose();
		this._aryToDoIDs = null;
		this._ctlConfirm = null;
		this._strTitle_Delete = null;
		this._strTitle_MarkComplete = null;
		this._strTitle_MarkIncomplete = null;
		this._strExplanation_Delete = null;
		this._strExplanation_MarkComplete = null;
		this._strExplanation_MarkIncomplete = null;
		Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.callBaseMethod(this, "dispose");
	},
	
	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlConfirm = this.getFieldComponent("ctlConfirm");
			this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
			this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
		}
	},
	
	modeChanged: function(){
		switch(this._mode) {
			case "DELETE": 
				this.changeTitle(this._strTitle_Delete); 
				this.changeExplanation(this._strExplanation_Delete); 
				break;
			case "MARK_COMPLETE": 
				this.changeTitle(this._strTitle_MarkComplete); 
				this.changeExplanation(this._strExplanation_MarkComplete); 
				break;
			case "MARK_INCOMPLETE": 
				this.changeTitle(this._strTitle_MarkIncomplete); 
				this.changeExplanation(this._strExplanation_MarkIncomplete); 
				break;
		}
	},

	yesClicked: function() {
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/ToDo");
		obj.set_DataObject("ToDo");
		switch (this._mode) {
			case "DELETE": 
				obj.set_DataAction("Delete");
				break;
			case "MARK_COMPLETE": 
				obj.set_DataAction("Mark");
				obj.addParameter("Complete", true);
				break;
			case "MARK_INCOMPLETE": 
				obj.set_DataAction("Mark");
				obj.addParameter("Complete", false);
				break;
		}
		obj.addParameter("IDs", $R_FN.arrayToSingleString(this._aryToDoIDs));
		obj.addDataOK(Function.createDelegate(this, this.saveConfirmComplete));
		obj.addError(Function.createDelegate(this, this.saveConfirmError));
		obj.addTimeout(Function.createDelegate(this, this.saveConfirmError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
		this.onSave();
	},
	
	noClicked: function() {
		this.onNotConfirmed();
	},

	saveConfirmError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveConfirmComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this.saveConfirmError(args);
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ToDoList_Confirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
