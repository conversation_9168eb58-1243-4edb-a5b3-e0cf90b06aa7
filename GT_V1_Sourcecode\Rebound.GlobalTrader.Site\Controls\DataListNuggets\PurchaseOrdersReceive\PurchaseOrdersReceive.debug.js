///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 23.12.2009:
// - render state on server
//
// RP 16.10.2009:
// - Changes due to changes in base class
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");

Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive = function(element) { 
	Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.prototype = {

    get_blnShowAllOrders: function () { return this._blnShowAllOrders; }, set_blnShowAllOrders: function (v) { if (this._blnShowAllOrders !== v) this._blnShowAllOrders = v; },
    get_IsGlobalLogin: function () { return this._IsGlobalLogin; }, set_IsGlobalLogin: function (v) { if (this._IsGlobalLogin !== v) this._IsGlobalLogin = v; },

	initialize: function() {
		this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseIsReady));
		this.addSetupDataCallEvent(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		this._strPathToData = "controls/DataListNuggets/PurchaseOrdersReceive";
		this._strDataObject = "PurchaseOrdersReceive";
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.callBaseMethod(this, "initialize");
	},
	
	initAfterBaseIsReady: function() {
		this.addPageTabChangedEvent(Function.createDelegate(this, this.pageTabChanged));
		this.updateFilterVisibility();
		this.getData();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._blnShowAllOrders = null;
        this._IsGlobalLogin=null;
		Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.callBaseMethod(this, "dispose");
	},
			
	pageTabChanged: function() {
		this._table._intCurrentPage = 1;
		this._blnShowAllOrders = (this._intCurrentTab == 1);
		this.updateFilterVisibility();
		this.getData();
	},

	setupDataCall: function () {
	    this._objData.addParameter("IsGlobalLogin", this._IsGlobalLogin);
		var strAction = "GetData";
	    if (this._blnShowAllOrders) strAction += "_All";
		this._objData.set_DataAction(strAction);
	},

	getDataOK: function(args) {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
		    var row = this._objResult.Results[i];
		    var aryData = null;
		    if (this._IsGlobalLogin==true) {
		         aryData = [
                    //$R_FN.writeDoubleCellValue($RGT_nubButton_PurchaseOrder(row.ID, row.No),$RGT_nubButton_InternalPurchaseOrder(row.IPOID,row.IPONo))
                      $R_FN.writeDoubleCellValue($RGT_nubButton_ReceivePurchaseOrder(row.ID, row.No), $RGT_nubButton_InternalPurchaseOrder(row.IPOID, row.IPONo))
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                    , $R_FN.writeDoubleCellValue(row.Quantity, row.Oustanding)
                    , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                    , $R_FN.setCleanTextValue(row.Delivery)
                    , $R_FN.setCleanTextValue(row.Ship)
                   // , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.PoClientName), $R_FN.setCleanTextValue(row.ClientName))
                     , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ClientName))
                     , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ReceivePOStatus))
		        ];
		    }
		    else {
		        aryData = [
                  //$R_FN.writeDoubleCellValue($RGT_nubButton_PurchaseOrder(row.ID, row.No),$RGT_nubButton_InternalPurchaseOrder(row.IPOID,row.IPONo))
                    $R_FN.writeDoubleCellValue($RGT_nubButton_ReceivePurchaseOrder(row.ID, row.No), $RGT_nubButton_InternalPurchaseOrder(row.IPOID, row.IPONo))
                  , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr))
                  , $R_FN.writeDoubleCellValue(row.Quantity, row.Oustanding)
                  , $R_FN.writeDoubleCellValue($RGT_nubButton_Company(row.CMNo, row.CM), $RGT_nubButton_Contact(row.ContactNo, row.Contact))
                  , $R_FN.setCleanTextValue(row.Delivery)
                  , $R_FN.setCleanTextValue(row.Ship)
                 // , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.PoClientName), $R_FN.setCleanTextValue(row.ClientName))
                 //, $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ClientName))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.ReceivePOStatus))
		        ];
		    }
			this._table.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	},
	
	updateFilterVisibility: function() {
		this.getFilterField("ctlRecentOnly").show(this._blnShowAllOrders);
		this.getFilterField("ctlIncludeClosed").show(this._blnShowAllOrders);
		this.getFilterField("ctlClientName").show(this._IsGlobalLogin);
	}
	
};
Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrdersReceive", Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base, Sys.IDisposable);
