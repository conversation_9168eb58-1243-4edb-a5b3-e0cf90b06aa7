﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

IF OBJECT_ID('dbo.usp_GetExcelStockHeaderColumnFrom_PriceQuote', 'P') IS NOT NULL
BEGIN
    DROP PROCEDURE dbo.usp_GetExcelStockHeaderColumnFrom_PriceQuote
END
GO
/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-205174]		An.TranTan			28-Jun-2024		Update			Get 1 more column and remove incorrect order by
===========================================================================================
*/
CREATE PROCEDURE [dbo].[usp_GetExcelStockHeaderColumnFrom_PriceQuote]
    @ClientId INT,
    @UserId INT,
    @SelectedClientID INT
AS
BEGIN
    SELECT ColumnName AS ExcelHeaderid,
           ColumnValue AS ColumnHeading
    from BorisGlobalTraderimports.dbo.[tbPriceQuoteImportColumnHeading]
        cross apply
    (
        values
            ('Column1', Column1),
            ('Column2', Column2),
            ('Column3', Column3),
            ('Column4', Column4),
            ('Column5', Column5),
            ('Column6', Column6),
            ('Column7', Column7),
            ('Column8', Column8),
            ('Column9', Column9),
            ('Column10', Column10),
            ('Column11', Column11),
            ('Column12', Column12),
            ('Column13', Column13),
            ('Column14', Column14),
            ('Column15', Column15),
            ('Column16', Column16),
            ('Column17', Column17),
            ('Column18', Column18),
            ('Column19', Column19),
            ('Column20', Column20),
            ('Column21', Column21),
            ('Column22', Column22),
            ('Column23', Column23),
            ('Column24', Column24),
            ('Column25', Column25)
    ) c (ColumnName, ColumnValue)
    WHERE ClientId = @ClientId
          and SelectedClientID = @SelectedClientID
          AND CreatedBy = @UserId
          and ColumnValue is not null
END
GO
/*
	exec usp_GetExcelStockHeaderColumnFrom_PriceQuote
		@ClientId = 114,
		@UserId = 6670,
		@SelectedClientID = 114
*/


