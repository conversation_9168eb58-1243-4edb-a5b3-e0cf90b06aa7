﻿//Marker     Changed by         Date         Remarks
//[001]      A<PERSON><PERSON><PERSON>     10/09/2021    Added class for precogs Supplier.
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class PrecogsSupplier : BizObject
    {
        #region Properties

        protected static DAL.RohsStatusElement Settings
        {
            get { return Globals.Settings.RohsStatuss; }
        }

        /// <summary>
        /// ROHSStatusId
        /// </summary>
        public System.Int32 PrecogSupplierId { get; set; }
        /// <summary>
        /// Name
        /// </summary>
        public System.String Name { get; set; }

        #endregion

        #region Methods

        /// <summary>
        /// Delete
        /// Calls [usp_delete_ROHSStatus]
        /// </summary>
        public static bool Delete(System.Int32? rohsStatusId)
        {
            return Rebound.GlobalTrader.DAL.SiteProvider.RohsStatus.Delete(rohsStatusId);
        }
        /// <summary>
        /// DropDown
        /// Calls [usp_dropdown_ROHSStatus]
        /// </summary>
        public static List<PrecogsSupplier> DropDown()
        {
            List<PurchaseMethodDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.PrecogsSupplier.DropDown();
            if (lstDetails == null)
            {
                return new List<PrecogsSupplier>();
            }
            else
            {
                List<PrecogsSupplier> lst = new List<PrecogsSupplier>();
                foreach (PurchaseMethodDetails objDetails in lstDetails)
                {
                    Rebound.GlobalTrader.BLL.PrecogsSupplier obj = new Rebound.GlobalTrader.BLL.PrecogsSupplier();
                    obj.PrecogSupplierId = objDetails.PurchaseMethodId;
                    obj.Name = objDetails.Name;
                    lst.Add(obj);
                    obj = null;
                }
                lstDetails = null;
                return lst;
            }
        }





        #endregion
    }
}
