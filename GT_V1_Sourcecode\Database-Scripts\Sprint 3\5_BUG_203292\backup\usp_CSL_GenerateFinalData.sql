
/****** Object:  StoredProcedure [dbo].[usp_CSL_GenerateFinalData]    Script Date: 5/28/2024 1:12:46 AM ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
---RP-3009---
ALTER procedure [dbo].[usp_CSL_GenerateFinalData]
AS
BEGIN

    --[001] SOORYA VYAS 16-JAN-2024  RP-2845 --  Changes in CSL_Sanctioned sheet (Added new column IsSanctioned)    

    --- delete tbCSL_Address_Comparision table as this table will be recreated everytime when this procedure runs                      
    --- prepare the company complete address                      
    IF OBJECT_ID(N'tbCSL_Address_Comparision', N'U') IS NOT NULL
        drop table tbCSL_Address_Comparision

    ------ delete the data from final table for running process day                    
    delete from tbCSLGTComparision
    where CAST(Insertedon as date) = cast(CURRENT_TIMESTAMP as date)

    ---Use below step to recreate the tbCSL_Address_Comparision                       
    select ca.CompanyNo,
           src.CompanyName,
           src.CustomerCode,
           replace(
                      Replace(
                                 Stuff(
                                          Coalesce(', ' + a.AddressName, '') + Coalesce(', ' + a.Line1, '')
                                          + Coalesce(', ' + a.Line2, '') + Coalesce(', ' + a.line3, '')
                                          + Coalesce(', ' + a.county, '') + Coalesce(', ' + a.city, '')
                                          + Coalesce(', ' + a.State, '') + Coalesce(', ' + a.city, '')
                                          + Coalesce(', ' + cntry.CountryName, '') + Coalesce(',' + [Zip], ''),
                                          1,
                                          1,
                                          ''
                                      ),
                                 ',',
                                 ''
                             ),
                      '-',
                      ''
                  ) AS [Address],
           a.AddressName,
           a.Line1,
           a.Line2,
           a.Line3,
           a.county,
           a.City,
           a.State,
           a.CountryNo,
           cntry.CountryName,
           a.ZIP,
           src.ClientNo
    into tbCSL_Address_Comparision
    from tbCompany src with (nolock)
        join tbCompanyAddress ca
            on src.CompanyId = ca.CompanyNo
        join tbAddress a
            on ca.AddressNo = a.AddressId
        left join tbCountry cntry
            on a.CountryNo = cntry.CountryId ---- 163503,159264                      
    order by ca.CompanyNo

    ---This steps compare and fetch the companies from GT as per CSV data                      
    insert into tbCSLGTComparision
    (
        companyid,
        CustomerCode,
        ClientNo,
        ClientName,
        CompanyName,
        GT_Company_Address,
        CSL_Name,
        CSL_Address,
        CSL_ALT_Name,
        Insertedon,
        Notes,
        ImportantNotes,
        ERAIReported
    )
    select distinct
        src.CompanyId,
        IsNUll(src.CustomerCode, '') CustomerCode,
        src.ClientNo,
        (
            select tbClient.ClientName
            from tbclient with (nolock)
            where tbclient.ClientId = src.ClientNo
        ) ClientName,
        src.CompanyName,
        gtcmpAdd.Address [GT Company Address],
        dest.name [CSL Name],
        IsNUll(dest.addresses, '') [CSL Address],
        IsNull(dest.altnames, '') [CSL ALT_Name],
        CURRENT_TIMESTAMP,
        src.notes,
        src.ImportantNotes,
        case src.ERAIReported
            when 1 then
                'true'
            else
                'false'
        end
    from tbCompany src with (nolock)
        left join tbCSVImport dest with (nolock)
            on src.CompanyName LIKE dest.name + '%'
               or src.CompanyName = altnames
        left join tbCSL_Address_Comparision gtcmpAdd with (nolock)
            on gtcmpAdd.CompanyNo = src.CompanyId
    where cast(dest.CreatedOn as date) = cast(CURRENT_TIMESTAMP as date)
          and (
                  dest.name like '%' + src.CompanyName + '%'
                  or dest.altnames like '%' + src.CompanyName + '%'
              )
          and (
                  len(src.CompanyName) not in ( 0, 1, 2, 3 )
                  and src.CompanyName != ''
              )
    order by src.CompanyId


    select CSLGT.companyid,
           CSLGT.CustomerCode,
           CSLGT.ClientNo,
           CSLGT.ClientName,
           CSLGT.CompanyName,
           CSLGT.Notes as [General_customer_info],
           CSLGT.ImportantNotes as Accounts_notes,
           CSLGT.GT_Company_Address,
           CSL_Name,
           CSL_Address,
           CSL_ALT_Name,
           CSLGT.ERAIReported,
           CASE
               WHEN IsSanctioned = 1 THEN
                   'True'
               ELSE
                   'False'
           END as IsSanctioned --[001]                  
    from tbCSLGTComparision CSLGT
        Left join tbCompany comp
            on CSLGT.companyid = comp.companyid
        LEFT Join tbClient cl
            on cl.ClientId = CSLGT.ClientNo
    where cast(Insertedon as date) = cast(CURRENT_TIMESTAMP as date)
          AND comp.Inactive != 1
          AND cl.Inactive != 1
END
