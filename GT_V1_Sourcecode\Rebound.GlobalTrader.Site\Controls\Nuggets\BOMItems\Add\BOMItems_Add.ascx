<%@ Control Language="C#" CodeBehind="BOMItems_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false" NumberOfSteps="3" MultiStepControlID="ctlMultiStep">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>
	
	<Content>
	
		
		<ReboundUI_Table:Form id="frmStep2" runat="server">
			<asp:TableRow id="trSourceFromRequirement" runat="server">
				<asp:TableCell id="tdSourceFromRequirement" runat="server"><ReboundItemSearch:RequiredBomItem id="ctlReqsWithBOM" runat="server" /></asp:TableCell>
			</asp:TableRow>
		</ReboundUI_Table:Form>
		
		
	</Content>
</ReboundUI_Form:DesignBase>
