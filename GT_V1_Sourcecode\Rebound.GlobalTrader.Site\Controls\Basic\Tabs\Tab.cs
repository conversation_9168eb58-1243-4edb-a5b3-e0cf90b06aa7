using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:Tab runat=server></{0}:Tab>")]
	public class Tab : WebControlWithScript, IScriptControl, INamingContainer {

		#region Locals

		private Panel _pnlTab;
		private Literal _litTitle;

		#endregion

		#region Properties

		/// <summary>
		/// TitleText
		/// </summary>
		private string _strTitleText;
		public string TitleText {
			get { return _strTitleText; }
			set { _strTitleText = Functions.GetGlobalResource("Tabs", value); }
		}

		/// <summary>
		/// Is it selected?
		/// </summary>
		private bool _blnIsSelected;
		public bool IsSelected {
			get { return _blnIsSelected; }
			set { _blnIsSelected = value; }
		}

		/// <summary>
		/// The control containing the content for the tab
		/// </summary>
		private string _strRelatedContentPanelID;
		public string RelatedContentPanelID {
			get { return _strRelatedContentPanelID; }
			set { _strRelatedContentPanelID = value; }
		}
		private Panel _pnlRelatedContentPanel;
		public Panel RelatedContentPanel {
			get { return _pnlRelatedContentPanel; }
			set { _pnlRelatedContentPanel = value; }
		}

		/// <summary>
		/// Index of this tab within its parent tab strip control
		/// </summary>
		private int _intTabIndex;
		internal new int TabIndex {
			get { return _intTabIndex; }
			set { _intTabIndex = value; }
		}

		/// <summary>
		/// The parent tab strip control
		/// </summary>
		private TabStrip _ctlParentTabStrip;
		internal TabStrip ParentTabStrip {
			get { return _ctlParentTabStrip; }
			set { _ctlParentTabStrip = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			((Pages.Base)Page).AddCSSFile("Tabs.css");
			EnsureChildControls();
			base.OnLoad(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			_pnlTab = ControlBuilders.CreatePanel();
			_pnlTab.ID = "tabItem";
			_pnlTab.CssClass = (IsSelected) ? "tabItemSelected" : "tabItem";
			_litTitle = ControlBuilders.CreateLiteralInsideParent(_pnlTab);
			Controls.Add(_pnlTab);
			base.CreateChildControls();
		}

		protected override void Render(HtmlTextWriter writer) {
			_litTitle.Text = _strTitleText;
			base.Render(writer);
		}

		#endregion

		/// <summary>
		/// Finds a control within the tab content
		/// </summary>
		/// <param name="strName"></param>
		/// <returns></returns>
		public Control FindContentControl(string strName) {
			return Functions.FindControlRecursive(_pnlRelatedContentPanel, strName);
		}

		#region IScriptControl Members

		protected new virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { 
				Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.Tabs.TabStrip", true), 
				Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.Tabs.Tab", true) 
			};
		}

		protected new virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Tab", this.ClientID);
			descriptor.AddProperty("TabIndex", TabIndex);
			descriptor.AddProperty("TitleText", TitleText);
			descriptor.AddElementProperty("pnlTab", _pnlTab.ClientID);
			descriptor.AddProperty("IsSelected", IsSelected);
			descriptor.AddElementProperty("pnlRelatedContent", RelatedContentPanel.ClientID);
			descriptor.AddComponentProperty("ParentTabStrip", ParentTabStrip.ClientID);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion
	}
}