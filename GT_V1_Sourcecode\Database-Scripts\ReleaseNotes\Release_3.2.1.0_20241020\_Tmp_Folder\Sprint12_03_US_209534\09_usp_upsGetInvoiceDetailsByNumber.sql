﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-210540]		Phuc Hoang			07-Aug-2024		UPDATE			[PROD Bug] DHL Interface - Label Print Issue
[US-209534]		NgaiTo		 		17-Sep-2024		UPDATE			209534: OGEL approval dropdown to be moved out of code to the setup screen
===========================================================================================
*/

      
/*                                    
Created By: <PERSON><PERSON><PERSON>                                    
Created On: 24 Jan 2012                                    
Purpose: To Get Invoice details by invoice number                                    
EXECUTE usp_upsGetInvoiceDetailsByNumber 1075046,101                                    
*/ 

CREATE OR ALTER PROC [dbo].[usp_upsGetInvoiceDetailsByNumber]                         
(                    
--exec [dbo].[usp_upsGetInvoiceDetailsByNumber] 1074976, 101                                   
   @vInvoiceNumber Int,                                    
   @vClientNo Int                                    
)                                 
As                                    
Begin                          
    Select InvoiceNumber, InvoiceDate, IsNull(Inv.CompanyNo,0) as 'CompanyNo', CompanyShipTo,                                     
        IsNull(Inv.ShipToAddressNo,0) As 'AddressId',                                     
        IsNull(Inv.ContactNo,0) As 'ContactId',                                                
        Con.EMail,                                     
        --Con.Telephone,  
  tbCompany.Telephone,  
        dbo.ufn_convert_unicode_to_western_character(IsNull(Con.ContactName,'')) As 'ContactName',                                                
        dbo.ufn_convert_unicode_to_western_character(IsNull(Adrs.AddressName,''))  As 'AddressName',                                     
        dbo.ufn_convert_unicode_to_western_character(IsNull(Adrs.Line1,'')) As 'AddressLine1',                                     
        dbo.ufn_convert_unicode_to_western_character(IsNull(Adrs.Line2,''))  As 'AddressLine2',                                    
        dbo.ufn_convert_unicode_to_western_character(IsNull(Adrs.Line3,''))  As 'AddressLine3',                                    
        dbo.ufn_convert_unicode_to_western_character(IsNull(Adrs.City,''))  As 'City',                                     
        dbo.ufn_convert_unicode_to_western_character(IsNull(Adrs.[State],'')) As 'State',                                     
  --IsNull(Adrs.CountryNo,0) as 'CountryNo',                                     
  IsNull(tbCountry.GlobalCountryNo,0) as 'CountryNo',                                     
  IsNull(Adrs.County,'') As 'County',                                    
  IsNull(tbCountry.CountryName,'') As 'Country' ,                                     
  dbo.ufn_convert_unicode_to_western_character(IsNull(Adrs.ZIP,'')) As 'ZIP',                                    
  ISNULL(ups.CountryCode,'') As CountryCode,                                    
  IsNull(Inv.SalesOrderNumber,0) As 'SalesOrderNumber',                                    
  IsNull(Con.ContactName,'') As 'Buyer',                                     
  (SELECT Cast(sum(IsNull(z.Price,0.00) * isnull(x.Quantity, 0)) As Decimal(10,2))                                      
            FROM  dbo.tbInvoiceLine z                                      
            INNER JOIN dbo.tbInvoiceLineAllocation x ON x.InvoiceLineNo = z.InvoiceLineId                                      
            WHERE  z.InvoiceNo = Inv.InvoiceId                                      
  ) AS InvoiceValue,                                    
  ISNULL(tbCompany.CompanyName,'') As 'CompanyName',                              
  IsNull(Inv.IsUpsInvoiceExported,0) As 'IsUpsInvoiceExported',                          
  ISNULL(Inv.CustomerPO,'') as   CustomerPO ,                        
  ISNULL(Inv.Account,'') AS ShipViaAccount ,            
  ISNULL(cu.CurrencyCode,'') AS  CurrencyCode ,            
     tbCompanyAddress.ShipViaAccount as CustomerShipViaAccount ,            
     sv.ShipViaName,          
  tit.Code IncotermCode,          
  tpck.PackageName UnitsOfMeasure      
  ,tbCompany.EORINumber ---Added By Manoj Kumar 28-03-2023--------      
  --,iif(isnull(ts.OGEL_Required,0)=1,tcl.OGELNumber,null) OGELNumber ---Added By Manoj Kumar 29-03-2023-------- 
  ,(
		CASE 
			WHEN es.OGELNumber > 0
				AND ts.OGEL_Required = 1
				THEN (
						--SELECT ct.OGELNumber
						--FROM tbClient ct
						--WHERE ct.ClientId = so.ClientNO
						SELECT ISNULL((SELECT OgelNumber FROM tbOGELLicense WHERE OgelId = es.OGELNumber and Inactive = 0),'GBOGE2020/00615')
						--CASE 
						--	WHEN es.OGELNumber = 1
						--		THEN 'GBOGE2020/00615'
						--	WHEN es.OGELNumber = 2
						--		THEN 'GBOGE2024/00532 - OGEL MIL GMST'
						--	WHEN es.OGELNumber = 3
						--		THEN 'GBOGE2024/00756 - OGEL PCB Comp MIL'
						--	ELSE 'GBOGE2020/00615'
						--	END
						)
			ELSE ''
			END
	) AS OGELNumber
  ,tbCompany.Tax as CompanyVATNo    
    From tbInvoice Inv             
 Inner join dbo.tbInvoiceLine InvLine on InvLine.InvoiceNo=inv.InvoiceId            
    Inner Join tbContact Con On Con.ContactId = Inv.ContactNo                                    
    Inner Join tbAddress Adrs On Adrs.AddressId = Inv.ShipToAddressNo             
    Left Outer Join tbCountry On tbCountry.CountryId = Adrs.CountryNo                                    
    Left Outer Join tbGlobalCountryList gCountry On gCountry.GlobalCountryName = tbCountry.CountryName            
    --Left Outer Join tbGlobalCountryList gCountry On gCountry.GlobalCountryId = tbCountry.GlobalCountryNo -- this what it should be GA 06/03/2015                                     
 Left Outer Join UPSCountryList ups On ups.GTCountryID = gCountry.GlobalCountryId                            
    Left Outer Join tbCompany On tbCompany.CompanyId = Inv.CompanyNo                             
    Left Outer Join tbCompanyAddress on tbCompany.CompanyId=tbCompanyAddress.CompanyNo AND tbCompanyAddress.AddressNo= inv.ShipToAddressNo              
    left outer join tbCurrency cu on cu.CurrencyId=Inv.CurrencyNo                 
 left join tbShipVia sv on sv.ShipViaId= tbCompanyAddress.ShipViaNo           
 left join tbIncoterm tit on tit.IncotermId=inv.IncotermNo                                      
    left join dbo.tbInvoiceLineAllocation InvLAllo ON InvLAllo.InvoiceLineNo = InvLine.InvoiceLineId       
 left join tbPackage tpck on tpck.PackageId=InvLine.PackageNo      
 --Left Join tbSalesOrderLine tsl on InvLAllo.SalesOrderLineNo=tsl.SalesOrderNo       
 --Left Join tbSalesOrder ts on tsl.SalesOrderLineId=ts.SalesOrderId      
 Left Join tbSalesOrder ts on ts.SalesOrderId=Inv.SalesOrderNo
 LEFT JOIN tbSalesOrderLine sol ON isnull(InvLine.SalesOrderLineNo, 0) = isnull(sol.SalesOrderLineId, 0)     
 LEFT JOIN tbSO_ExportApprovalStatusOGEL es ON isnull(InvLine.SalesOrderLineNo, 0) = isnull(es.SalesOrderLineNo, 0)
 --Left Outer join tbSO_ExportApprovalStatusOGEL tse on tse.SalesOrderLineNo=InvLine.SalesOrderLineNo --Inv.salesorderNo=tse.SalesOrderNo and       
 Left join tbclient tcl on tbCompany.ClientNo=tcl.ClientId      
    Where Inv.InvoiceNumber = @vInvoiceNumber And Inv.ClientNo = @vClientNo                 
    AND ISNULL(inv.Exported,0) = 0      
End       
GO


