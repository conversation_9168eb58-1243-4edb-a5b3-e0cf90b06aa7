﻿/* Marker    changed by      date           Remarks
  [001]      Vinay           11/08/2014     ESMS  Ticket Number: 	200
 */
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL {

		public partial class GlobalSalesPerson : BizObject {

		#region Properties
		protected static DAL.GlobalSalesPersonElement Settings
		{
			get { return Globals.Settings.GlobalSalesPersons; }
		}

		public System.Int32? GlobalSalesPersonId { get; set; }
		public System.Int32? CompanyNo { get; set; }
		public System.String SalesPersonName { get; set; }
		public System.String ClientName { get; set; }
		public System.Int32? LoginNo { get; set; }

		#endregion

		#region Methods

		/// <summary>
		/// GetListForSupplier
		/// Calls [usp_selectAll_ManufacturerLink_for_Supplier]
		/// </summary>
		public static List<GlobalSalesPerson> GetListForGlobalSalesPerson(System.Int32? supplierCompanyNo, System.Int32? ClientNo=0) {
			List<GlobalSalesPersonDetails> lstDetails = Rebound.GlobalTrader.DAL.SiteProvider.GlobalSalesPerson.GetListForGlobalSalesPerson(supplierCompanyNo, ClientNo);
			if (lstDetails == null) {
				return new List<GlobalSalesPerson>();
			} else {
				List<GlobalSalesPerson> lst = new List<GlobalSalesPerson>();
				foreach (GlobalSalesPersonDetails objDetails in lstDetails) {
					Rebound.GlobalTrader.BLL.GlobalSalesPerson obj = new Rebound.GlobalTrader.BLL.GlobalSalesPerson();
					obj.GlobalSalesPersonId = objDetails.GlobalSalesPersonId;
					obj.SalesPersonName = objDetails.SalesPersonName;
					obj.CompanyNo = objDetails.CompanyNo;
					obj.ClientName = objDetails.ClientName;
					obj.LoginNo = objDetails.LoginNo;
					lst.Add(obj);
					obj = null;
				}
				lstDetails = null;
				return lst;
			}
		}

		/// <summary>
		/// Insert
		/// Calls [usp_insert_SecurityGroupLogin]
		/// </summary>
		public static Int32 Insert(System.Int32? CompanyNo, System.Int32? loginNo, System.Int32? updatedBy, System.Int32? ClientNo)
		{
			Int32 objReturn = Rebound.GlobalTrader.DAL.SiteProvider.GlobalSalesPerson.Insert(CompanyNo, loginNo, updatedBy, ClientNo);
			return objReturn;
		}

		/// <summary>
		/// Delete
		/// Calls [usp_delete_SecurityGroupLogin]
		/// </summary>
		public static bool Delete(System.Int32? loginNo, System.Int32? CompannyNo, System.Int32? ClientNo)
		{
			return Rebound.GlobalTrader.DAL.SiteProvider.GlobalSalesPerson.Delete(loginNo, CompannyNo, ClientNo);
		}

		#endregion

	}
}