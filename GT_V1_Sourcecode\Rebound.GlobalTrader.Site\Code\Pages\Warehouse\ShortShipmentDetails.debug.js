///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference name="~/Controls/IconButton/IconButton.js" />
///<reference name="~/Controls/ReboundTextBox/ReboundTextBox.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// SK 13.04.2010:
// - display Print/Email buttons based on PO aproval/disapproval 
//
// RP 06.01.2010:
// - fully dispose everything
/*
Marker     Changed by      Date         Remarks
*/
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Warehouse");

Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails = function (el) {
    Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.initializeBase(this, [el]);
    this._blnIsApproved = false;
};

Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.prototype = {

    get_intShortShipmentID: function () { return this._intShortShipmentID; }, set_intShortShipmentID: function (v) { if (this._intShortShipmentID !== v) this._intShortShipmentID = v; },
    initialize: function () {
        Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.callBaseMethod(this, "initialize");
    },

    goInit: function () {

        //if (this._ctlMainInfo) this._ctlMainInfo.addGetDataComplete(Function.createDelegate(this, this.ctlMainInfo_GetDataComplete));
        //if (this._ctlMainInfo) this._ctlMainInfo.addSaveEditComplete(Function.createDelegate(this, this.ctlMainInfo_SaveEditComplete));
        //if (this._ctlMainInfo) this._ctlMainInfo.addPotentialStatusChange(Function.createDelegate(this, this.ctlMainInfo_PotentialStatusChange));
        //if (this._ctlMainInfo) this.setLineFieldsFromHeader();

        Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.callBaseMethod(this, "goInit");
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ctlMainInfo) this._ctlMainInfo.dispose();
        this._intShortShipmentID = null;
        Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.callBaseMethod(this, "dispose");
    },
    ctlMainInfo_SaveEditComplete: function () {
    },
    ctlMainInfo_GetDataComplete: function () {

        this.setLineFieldsFromHeader();
        this._ctlLines._intGlobalClientNo = this._IsGlobalLogin == true ? this._ctlMainInfo.getFieldValue("hidGlobalClientNo") : null;
        this._ctlMainInfo._IsGlobalLogin = this._IsGlobalLogin;

        $R_FN.setInnerHTML(this._lblStatus, this._ctlMainInfo.getFieldValue("hidStatus"));
        this._ctlLines.updateStatus(this._ctlMainInfo.getFieldValue("hidStatusNo"));
        this._ctlLines._ipoClientNo = this._ctlMainInfo._ipoClientNo;
        if (this._btnPrint) $R_FN.showElement(this._btnPrint._element, this._ctlMainInfo._blnIsApproved);
        this._ctlLines.enableDisableAddButton(this._ctlMainInfo._isIPO);
        //[004] start
        this._ctlLines._PONumber = this._ctlMainInfo._PONumber;
        var eprHtml = "";
        for (var i = 0; i < this._ctlMainInfo._POLineEPRIds.length; i++) {
            var row = this._ctlMainInfo._POLineEPRIds[i];
            eprHtml += $RGT_nubButton_EPR(this._ctlMainInfo._intPurchaseOrderID, row.POLineEPRIds, this._ctlMainInfo._PONumber); //$RGT_nubButton_DebitNote(row.DebitId, row1.DebitNumber);
        }
        $R_FN.setInnerHTML(this._ctlLines._pnlEPR, "");
        $R_FN.setInnerHTML(this._ctlLines._pnlEPR, eprHtml);
        $R_FN.showElement(this._ctlLines._pnlEPR, (eprHtml.length > 0));
        //[004] end
        this.CompanyStatus();

    }

};
Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails.registerClass("Rebound.GlobalTrader.Site.Pages.Warehouse.ShortShipmentDetails", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
