CREATE or alter PROCEDURE usp_select_importUtilityLog                                          
 @DisplayLength int=0                                        
,@DisplayStart int=0                                        
,@SortCol int=0                                      
,@SortDir nvarchar(10)                                          
,@Search nvarchar(255) = NULL                     
,@UtilityType int=0                       
,@selectedclientid int=0                     
,@CreateBy int=0                       
                                        
as                                          
begin                           
                                                       
    Declare @FirstRec int, @LastRec int                                          
    Set @FirstRec = @DisplayStart;                                          
    Set @LastRec = @DisplayStart + @DisplayLength;                                          
                                             
    With CTE_Stock as                                          
    (                                          
         Select ROW_NUMBER() over (order by                                          
                                                  
         ul.DLUP  desc                                     
   )                                          
         as RowNum,                                          
         COUNT(*) over() as TotalCount,                                          
                
  ul.DLUP       
  ,ul.FileName       
  ,ISNULL(ut.UtilityTypeName, '') as UtilityType             
  ,(case when ul.Clientid=114 then 'HUB' when ul.Clientid=101 then 'UK' when ul.Clientid=108 then 'GMBH'          
   when ul.Clientid=107 then 'Rebound Singapore PTE Ltd' when ul.Clientid=107 then 'Rebound Electronics Schweiz'           
   when ul.Clientid=107 then 'Hong Kong'   else  '' end) ClientType             
   ,ul.iRowCount               
   ,lg.employeeName as ImportedBy            
                                        
        from BorisGlobalTraderimports.dbo.tbUtilityLog ul    
  inner join  tbLogin lg on    ul.LoginNo=lg.LoginId  
  left join tbUtilityType ut on ut.UtilityTypeId = ul.UtilityType  
      where  ClientId=@selectedclientid and  UtilityType=@UtilityType              
                           
    )                                          
    Select RowNum,TotalCount,                                      
                               
 CONVERT(varchar,DLUP,9) as Date,                        
 FileName ,UtilityType as Source ,ClientType,iRowCount as Rows ,ImportedBy                                
                                       
    from CTE_Stock                                          
    where  RowNum > @FirstRec and RowNum <= @LastRec                                         
    ORDER BY                         
                         
DLUP desc   ,                        
FileName asc                                 
end                           