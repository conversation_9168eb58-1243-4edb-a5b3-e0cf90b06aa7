///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits = function(element) { 
    Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.initializeBase(this, [element]);
    this._intGlobalClientNo = -1;
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
	    if (this.isDisposed) return;
	    this._intGlobalClientNo =null;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.callBaseMethod(this, "dispose");
	},

	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/Debits");
		this._objData.set_DataObject("Debits");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Contact", this.getFieldValue("ctlContact"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("Salesman", this.getFieldValue("ctlSalesman"));
		this._objData.addParameter("DebitNoLo", this.getFieldValue_Min("ctlDebitNo"));
		this._objData.addParameter("DebitNoHi", this.getFieldValue_Max("ctlDebitNo"));
		this._objData.addParameter("SRMANoLo", this.getFieldValue_Min("ctlSupplierRMANo"));
		this._objData.addParameter("SRMANoHi", this.getFieldValue_Max("ctlSupplierRMANo"));
		this._objData.addParameter("PONoLo", this.getFieldValue_Min("ctlPurchaseOrderNo"));
		this._objData.addParameter("PONoHi", this.getFieldValue_Max("ctlPurchaseOrderNo"));
		this._objData.addParameter("DebitDateFrom", this.getFieldValue("ctlDebitDateFrom"));
		this._objData.addParameter("DebitDateTo", this.getFieldValue("ctlDebitDateTo"));
		this._objData.addParameter("GlobalClientNo", this._intGlobalClientNo);
	},

	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
				$R_FN.setCleanTextValue(row.Contact),
				$R_FN.setCleanTextValue(row.Date),
				$R_FN.setCleanTextValue(row.Salesman),
				row.PO,
				row.SRMA
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Debits", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
