/*
Marker     Changed by      Date         Remarks
[001]      Vinay           23/08/2012   Customize the invoice control for exported record, Set Exported=1
*/
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;
using System.Linq;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class Invoices : Rebound.GlobalTrader.Site.Data.Base {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
					case "GetData": GetData(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}
		/// <summary>
		/// get an invoice by key
		/// </summary>
		private void GetData() {
			List<Invoice> lst = null;
			try {

                int? intGlobalClientNo;
                intGlobalClientNo = GetFormValue_NullableInt("GlobalClientNo");

                int? Exported = GetFormValue_NullableInt("InvoiceExported");
                if (Exported != null && Convert.ToInt32(Exported) == 2)   // 1 = Exported, 2 = NotExported                
                    Exported = 0;
                if (Exported != null && Convert.ToInt32(Exported) == 1)   // 1 = Exported, 2 = NotExported                
                    Exported = 1;

				lst = Invoice.ItemSearch(
                    (intGlobalClientNo.HasValue && intGlobalClientNo.Value > 0) ? intGlobalClientNo.Value : SessionManager.ClientID,
					GetFormValue_NullableInt("Order", 0),
					GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
					GetFormValue_NullableInt("PageIndex", 0),
					GetFormValue_NullableInt("PageSize", 10),
					GetFormValue_String("Contact"),
					//GetFormValue_StringForNameSearch("CMName"),
                    GetFormValue_StringForNameSearchDecode("CMName"),
					GetFormValue_NullableInt("Salesman"),
					GetFormValue_String("CustomerPO"),
					GetFormValue_Boolean("IncludePaid"),
					GetFormValue_NullableInt("InvoiceNoLo"),
					GetFormValue_NullableInt("InvoiceNoHi"),
					GetFormValue_NullableInt("SONoLo"),
					GetFormValue_NullableInt("SONoHi"),
					GetFormValue_NullableDateTime("DateInvoicedFrom"),
					GetFormValue_NullableDateTime("DateInvoicedTo"),
                    //[001] code start
                    Exported
                    //[001] code end
					);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].InvoiceId);
					jsnItem.AddVariable("No", lst[i].InvoiceNumber);
					jsnItem.AddVariable("CMNo", lst[i].CompanyNo);
					jsnItem.AddVariable("CMName", lst[i].CompanyName);
					jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].InvoiceDate));
					jsnItem.AddVariable("ContactNo", lst[i].ContactNo);
					jsnItem.AddVariable("Contact", lst[i].ContactName);
					jsnItem.AddVariable("Salesman", lst[i].SalesmanName);
					jsnItem.AddVariable("CustomerPO", lst[i].CustomerPO);
					jsnItem.AddVariable("SalesOrderNo", lst[i].SalesOrderNumber);
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose();
					jsnItem = null;
				}
				jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
				jsn.AddVariable("Results", jsnItems);
				OutputResult(jsn);
				jsnItems.Dispose();
				jsnItems = null;
				jsn.Dispose();
				jsn = null;
			} catch (Exception ex) {
				WriteError(ex);
			} finally {
				lst = null;
			}
		}
	}
}
