Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.prototype={get_intSalesPersonID:function(){return this._intSalesPersonID},set_intSalesPersonID:function(n){this._intSalesPersonID!==n&&(this._intSalesPersonID=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},get_ibtnExportCSV:function(){return this._ibtnExportCSV},set_ibtnExportCSV:function(n){this._ibtnExportCSV!==n&&(this._ibtnExportCSV=n)},get_sortIndex:function(){return this._sortIndex},set_sortIndex:function(n){this._sortIndex!==n&&(this._sortIndex=n)},get_sortDir:function(){return this._sortDir},set_sortDir:function(n){this._sortDir!==n&&(this._sortDir=n)},get_pageIndex:function(){return this._pageIndex},set_pageIndex:function(n){this._pageIndex!==n&&(this._pageIndex=n)},get_pageSize:function(){return this._pageSize},set_pageSize:function(n){this._pageSize!==n&&(this._pageSize=n)},get_code:function(){return this._code},set_code:function(n){this._code!==n&&(this._code=n)},get_name:function(){return this._name},set_name:function(n){this._name!==n&&(this._name=n)},initialize:function(){this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this._strPathToData="controls/DataListNuggets/OGELLinesExport";this._strDataObject="OGELLinesExport";Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.callBaseMethod(this,"initialize");this._ibtnExportCSV&&$R_IBTN.addClick(this._ibtnExportCSV,Function.createDelegate(this,this.exportCSV))},initAfterBaseIsReady:function(){this.getData()},dispose:function(){this.isDisposed||(this._intSalesPersonID=null,this._IsGlobalLogin=null,this._ibtnExportCSV&&$R_IBTN.clearHandlers(this._ibtnExportCSV),this._ibtnExportCSV=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var n,i,r="",t=0,u=this._objResult.Results.length;t<u;t++)r="",n=this._objResult.Results[t],r=n.DatePromisedStatus=="Green"?"green":n.DatePromisedStatus=="Amber"?"#FFBF00":n.DatePromisedStatus=="Red"?"Red":n.DatePromisedStatus=="White"?"White":"White",i=[$RGT_nubButton_SalesOrder(n.SoID,n.ID),$R_FN.writeDoubleCellValue($RGT_nubButton_Company(n.CMNo,n.CM),$R_FN.setCleanTextValue(n.CustPONO)),$R_FN.writeDoubleCellValue(n.Status,$R_FN.setCleanTextValue(n.DatePromised)),$R_FN.setCleanTextValue(n.No),$R_FN.setCleanTextValue(n.AirWayBill),$R_FN.setCleanTextValue(n.CommodityCode),$R_FN.setCleanTextValue(n.PartNumber),$R_FN.setCleanTextValue(n.OGELNumber),$R_FN.setCleanTextValue(n.OGEL_MilitaryUse),$R_FN.setCleanTextValue(n.CountryName)],this._table.addRow(i,n.ID,!1),i=null,n=null},exportCSV:function(){this.getData_Start();var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/DataListNuggets/OGELLinesExport");n.set_DataObject("OGELLinesExport");n.addParameter("ViewLevel",this._enmViewLevel);n.set_DataAction("ExportToCSV");n._intTimeoutMilliseconds=5e5;n.addParameter("SortIndex",this._sortIndex);n.addParameter("SortDir",this._sortDir);n.addParameter("PageIndex",this._pageIndex);n.addParameter("PageSize",this._pageSize);n.addParameter("Code",this._code);n.addParameter("Name",this._name);n.addParameter("SalesPerson",this._intSalesPersonId);n.addParameter("SONoLo",this.getFilterFieldValue_Min("ctlSoNo"));n.addParameter("SONoHi",this.getFilterFieldValue_Max("ctlSoNo"));n.addParameter("RecentOnly",this.getFilterFieldValue("ctlRecentOnly"));n.addParameter("InvoiceNoLo",this.getFilterFieldValue_Min("ctlInvoiceNo"));n.addParameter("InvoiceNoHi",this.getFilterFieldValue_Max("ctlInvoiceNo"));n.addParameter("Country",this.getFilterFieldValue("ctlCountry"));n.addParameter("dateOrderedFrom",this.getFilterFieldValue("ctlDateOrderedFrom"));n.addParameter("dateOrderedTo",this.getFilterFieldValue("ctlDateOrderedTo"));n.addDataOK(Function.createDelegate(this,this.exportCSV_OK));n.addError(Function.createDelegate(this,this.exportCSV_Error));n.addTimeout(Function.createDelegate(this,this.exportCSV_Error));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},exportCSV_OK:function(n){var i=n._result,t;i.Filename&&(t=new Date,location.href=String.format("{0}?t={1}",i.Filename,t.getTime()),t=null);this.getDataOK_End()},exportCSV_Error:function(n){this.showError(!0,n.get_ErrorMessage())}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.OGELLinesExport",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);