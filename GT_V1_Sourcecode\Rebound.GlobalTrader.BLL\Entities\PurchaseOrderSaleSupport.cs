﻿using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.BLL
{
    public partial class PurchaseOrderSaleSupport : BizObject
    {

        #region Properties

        protected static DAL.PurchaseOrderElement Settings
        {
            get { return Globals.Settings.PurchaseOrders; }
        }

        /// <summary>
        /// PurchaseOrderId
        /// </summary>
        public System.Int32 PurchaseOrderId { get; set; }
        public System.Int32 InternalPurchaseOrderId { get; set; }

        /// <summary>
        /// PurchaseOrderNumber
        /// </summary>
        public System.Int32 PurchaseOrderNumber { get; set; }
        /// <summary>
        /// SupportTeamMemberNo
        /// </summary>
        public System.Int32? SupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String SupportTeamMemberName { get; set; }
        /// <summary>
        /// Buyer
        /// </summary>
        public System.Int32 Buyer { get; set; }
        /// <summary>
        /// BuyerName
        /// </summary>
        public System.String BuyerName { get; set; }
        public System.Int32? IPOSupportTeamMemberNo { get; set; }
        /// <summary>
        /// SupportTeamMemberName
        /// </summary>
        public System.String IPOSupportTeamMemberName { get; set; }
        /// <summary>
        /// Buyer
        /// </summary>
        public System.Int32 IPOBuyer { get; set; }
        /// <summary>
        /// BuyerName
        /// </summary>
        public System.String IPOBuyerName { get; set; }
        #endregion

        #region Methods


        /// <summary>
        /// Get
        /// Calls [usp_select_PurchaseOrder]
        /// </summary>
        public static PurchaseOrderSaleSupport Get(System.Int32? purchaseOrderId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderSaleSupportDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrder.GetSaleSupport(purchaseOrderId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderSaleSupport obj = new PurchaseOrderSaleSupport();
                obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.Buyer = objDetails.Buyer;
                obj.BuyerName = objDetails.BuyerName;
                obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                obj.IPOBuyer = objDetails.IPOBuyer;
                obj.IPOBuyerName = objDetails.IPOBuyerName;
                obj.IPOSupportTeamMemberName = objDetails.IPOSupportTeamMemberName;
                obj.IPOSupportTeamMemberNo = objDetails.IPOSupportTeamMemberNo;
                
                objDetails = null;
                return obj;
            }
        }
        /// <summary>
        /// Get
        /// Calls [usp_select_PurchaseOrder]
        /// </summary>
        public static PurchaseOrderSaleSupport GetFromIPO(System.Int32? internalPurchaseOrderId)
        {
            Rebound.GlobalTrader.DAL.PurchaseOrderSaleSupportDetails objDetails = Rebound.GlobalTrader.DAL.SiteProvider.PurchaseOrder.GetSaleSupportIPO(internalPurchaseOrderId);
            if (objDetails == null)
            {
                return null;
            }
            else
            {
                PurchaseOrderSaleSupport obj = new PurchaseOrderSaleSupport();
                obj.PurchaseOrderId = objDetails.PurchaseOrderId;
                obj.PurchaseOrderNumber = objDetails.PurchaseOrderNumber;
                obj.Buyer = objDetails.Buyer;
                obj.BuyerName = objDetails.BuyerName;
                obj.InternalPurchaseOrderId = objDetails.InternalPurchaseOrderId;
                obj.SupportTeamMemberName = objDetails.SupportTeamMemberName;
                obj.SupportTeamMemberNo = objDetails.SupportTeamMemberNo;
                obj.IPOBuyer = objDetails.IPOBuyer;
                obj.IPOBuyerName = objDetails.IPOBuyerName;
                obj.IPOSupportTeamMemberName = objDetails.IPOSupportTeamMemberName;
                obj.IPOSupportTeamMemberNo = objDetails.IPOSupportTeamMemberNo;

                objDetails = null;
                return obj;
            }
        }
        #endregion

    }
}
