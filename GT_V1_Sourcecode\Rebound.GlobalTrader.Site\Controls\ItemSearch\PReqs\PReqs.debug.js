///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.callBaseMethod(this, "dispose");
	},

	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/PReqs");
		this._objData.set_DataObject("PReqs");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("SalesOrderNoLo", this.getFieldValue_Min("ctlSalesOrderNo"));
		this._objData.addParameter("SalesOrderNoHi", this.getFieldValue_Max("ctlSalesOrderNo"));
		this._objData.addParameter("Part", this.getFieldValue("ctlPartNo"));
		this._objData.addParameter("CM", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("DateOrderedFrom", this.getFieldValue("ctlDateOrderedFrom"));
		this._objData.addParameter("DateOrderedTo", this.getFieldValue("ctlDateOrderedTo"));
	},

	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No + String.format(" ({0})", row.LineNo)
				, $R_FN.setCleanTextValue(row.CMName)
				, $R_FN.writePartNo(row.Part, row.ROHS)
				, row.Price
				, row.Quantity
				, $R_FN.setCleanTextValue(row.Date)
				, $R_FN.setCleanTextValue(row.DatePromised)
			];
			this._tblResults.addRow(aryData, row.LineID, false);
			aryData = null; row = null;
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.PReqs", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
