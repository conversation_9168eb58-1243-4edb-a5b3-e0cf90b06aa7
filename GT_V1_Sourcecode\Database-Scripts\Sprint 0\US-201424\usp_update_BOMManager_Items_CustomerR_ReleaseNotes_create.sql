IF OBJECT_ID('usp_update_BOMManager_Items_CustomerR_ReleaseNotes', 'P') IS NOT NULL
DROP PROC usp_update_BOMManager_Items_CustomerR_ReleaseNotes

GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
-- =============================================
-- Author:		<Cuong DX>
-- Create date: <04/23/24>
-- Description:	<Update release note for customer requirement>
-- =============================================
CREATE PROCEDURE usp_update_BOMManager_Items_CustomerR_ReleaseNotes
	-- Add the parameters for the stored procedure here
	@CustomerRequirementId int
	, @ReleaseNote varchar(max)
	
AS
BEGIN
	-- SET NOCOUNT ON added to prevent extra result sets from
	-- interfering with SELECT statements.
	SET NOCOUNT ON;
	BEGIN TRAN                
	BEGIN TRY   
	-- Insert statements for procedure here
		UPDATE tbCustomerRequirement
		SET ReleaseNote = @ReleaseNote
		WHERE CustomerRequirementId = @CustomerRequirementId
		COMMIT TRAN                
		select 'Success' as 'Status','Items Updated' as 'Message'                     
	END TRY                
	BEGIN CATCH                
		ROLLBACK TRAN                          
		select 'Fail' as 'Status',ERROR_MESSAGE() as 'Message'                
	END CATCH      
END
GO
