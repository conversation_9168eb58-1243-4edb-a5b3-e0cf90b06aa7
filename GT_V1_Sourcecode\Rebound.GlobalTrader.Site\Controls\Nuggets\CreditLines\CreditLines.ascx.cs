//---------------------------------------------------------------------------------------------------------
// RP 04.12.2009:
// - allow sources for new lines to be set on permissions
//---------------------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets {
	public partial class CreditLines : Base {

		#region Locals

		protected IconButton _ibtnEdit;
		protected IconButton _ibtnAdd;
		protected IconButton _ibtnDelete;
		protected FlexiDataTable _tbl;
		protected HyperLink _hypPrev;
		protected HyperLink _hypNext;
		protected Label _lblLineNumber;
		protected Panel _pnlLineDetail;
		protected Panel _pnlLoadingLineDetail;
		protected Panel _pnlLineDetailError;
		protected Label _lblSubTotal;
		protected Label _lblFreight;
		protected Label _lblTax;
		protected Label _lblTotal;
		protected Forms.CreditLines_Add _frmAdd;
        protected IconButton _ibtnConfirm;

		#endregion

		#region Properties

		private int _intCreditID = -1;
		public int CreditID {
			get { return _intCreditID; }
			set { _intCreditID = value; }
		}

		private bool _blnCanAdd = true;
		public bool CanAdd {
			get { return _blnCanAdd; }
			set { _blnCanAdd = value; }
		}

		private bool _blnCanEdit = true;
		public bool CanEdit {
			get { return _blnCanEdit; }
			set { _blnCanEdit = value; }
		}

		private bool _blnCanDelete = true;
		public bool CanDelete {
			get { return _blnCanDelete; }
			set { _blnCanDelete = value; }
		}

		private bool _blnCanAddFromCRMALine = true;
		public bool CanAddFromCRMALine {
			get { return _blnCanAddFromCRMALine; }
			set { _blnCanAddFromCRMALine = value; }
		}

		private bool _blnCanAddFromInvoiceLine = true;
		public bool CanAddFromInvoiceLine {
			get { return _blnCanAddFromInvoiceLine; }
			set { _blnCanAddFromInvoiceLine = value; }
		}

		private bool _blnCanAddFromService = true;
		public bool CanAddFromService {
			get { return _blnCanAddFromService; }
			set { _blnCanAddFromService = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// Oninit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			base.OnInit(e);
			WireUpControls();
			AddScriptReference("Controls.Nuggets.CreditLines.CreditLines.js");
			TitleText = Functions.GetGlobalResource("Nuggets", "CreditLines");
			if (_objQSManager.CreditID > 0) _intCreditID = _objQSManager.CreditID;
			SetupTables();
		}

		protected override void OnLoad(EventArgs e) {
			base.OnLoad(e);
			_frmAdd.CanAddFromCRMALine = _blnCanAddFromCRMALine;
			_frmAdd.CanAddFromInvoiceLine = _blnCanAddFromInvoiceLine;
			_frmAdd.CanAddFromService = _blnCanAddFromService;
		}

		protected override void OnPreRender(EventArgs e) {
			_ibtnAdd.Visible = _blnCanAdd;
			_ibtnEdit.Visible = _blnCanEdit;
			_ibtnDelete.Visible = _blnCanDelete;
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Sets up AJAX script descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Nuggets.CreditLines", ctlDesignBase.ClientID);
			if (_blnCanEdit) _scScriptControlDescriptor.AddElementProperty("ibtnEdit", _ibtnEdit.ClientID);
			if (_blnCanAdd) _scScriptControlDescriptor.AddElementProperty("ibtnAdd", _ibtnAdd.ClientID);
			if (_blnCanDelete) _scScriptControlDescriptor.AddElementProperty("ibtnDelete", _ibtnDelete.ClientID);            
			_scScriptControlDescriptor.AddProperty("intCreditID", _intCreditID);
			_scScriptControlDescriptor.AddComponentProperty("tbl", _tbl.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypPrev", _hypPrev.ClientID);
			_scScriptControlDescriptor.AddElementProperty("hypNext", _hypNext.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblLineNumber", _lblLineNumber.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLineDetail", _pnlLineDetail.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLoadingLineDetail", _pnlLoadingLineDetail.ClientID);
			_scScriptControlDescriptor.AddElementProperty("pnlLineDetailError", _pnlLineDetailError.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblSubTotal", _lblSubTotal.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblTax", _lblTax.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblFreight", _lblFreight.ClientID);
			_scScriptControlDescriptor.AddElementProperty("lblTotal", _lblTotal.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnConfirm", _ibtnConfirm.ClientID);
		}

		private void SetupTables() {
            _tbl.Columns.Add(new FlexiDataColumn("Select", Unit.Pixel(40), false, HorizontalAlign.Center));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "CustomerPart", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo)));
			_tbl.Columns.Add(new FlexiDataColumn("ManufacturerAbbreviation", "DateCode", WidthManager.GetWidth(WidthManager.ColumnWidth.ManufacturerCode)));
			_tbl.Columns.Add(new FlexiDataColumn("Product", "Package", WidthManager.GetWidth(WidthManager.ColumnWidth.Product)));
			_tbl.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity)));
			_tbl.Columns.Add(new FlexiDataColumn("Price", "LandedCost", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
			_tbl.Columns.Add(new FlexiDataColumn("Total", "Tax", Unit.Empty));
		}

		/// <summary>
		/// Wire up controls from the ascx
		/// </summary>
		private void WireUpControls() {
			_ibtnAdd = FindIconButton("ibtnAdd");
			_ibtnEdit = FindIconButton("ibtnEdit");
			_ibtnDelete = FindIconButton("ibtnDelete");
			_tbl = (FlexiDataTable)Functions.FindControlRecursive(this, "tbl");
			_hypPrev = (HyperLink)Functions.FindControlRecursive(this, "hypPrev");
			_hypNext = (HyperLink)Functions.FindControlRecursive(this, "hypNext");
			_lblLineNumber = (Label)Functions.FindControlRecursive(this, "lblLineNumber");
			_pnlLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLineDetail");
			_pnlLoadingLineDetail = (Panel)Functions.FindControlRecursive(this, "pnlLoadingLineDetail");
			_pnlLineDetailError = (Panel)Functions.FindControlRecursive(this, "pnlLineDetailError");
			_lblSubTotal = (Label)ctlDesignBase.FindContentControl("lblSubTotal");
			_lblTax = (Label)ctlDesignBase.FindContentControl("lblTax");
			_lblFreight = (Label)ctlDesignBase.FindContentControl("lblFreight");
			_lblTotal = (Label)ctlDesignBase.FindContentControl("lblTotal");
			_frmAdd = (Forms.CreditLines_Add)FindFormControl("frmAdd");
            _ibtnConfirm = FindIconButton("ibtnConfirm");
		}
	}
}
