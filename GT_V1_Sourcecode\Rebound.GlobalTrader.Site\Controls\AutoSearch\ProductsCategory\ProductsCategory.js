Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.AutoSearch");Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsCategory=function(n){Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsCategory.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsCategory.prototype={get_ShowInactive:function(){return this._ShowInactive},set_ShowInactive:function(n){this._ShowInactive!==n&&(this._ShowInactive=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsCategory.callBaseMethod(this,"initialize");this.addDataReturnedEvent(Function.createDelegate(this,this.dataReturned));this.setupDataObject("ProductsCategory")},dispose:function(){this.isDisposed||(this._ShowInactive=null,Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsCategory.callBaseMethod(this,"dispose"))},selectionMade:function(){var t=this._varSelectedExtraData,i="";if(typeof t!="undefined"){var n=t.split(":"),r=n[0],u=n[1],f=n[2];i=f.replace("&#013;","\n\n")}setTimeout(function(){(r=="true"||u=="true")&&alert(i)},300)},dataReturned:function(){var t,r,n,i;if(this._result&&this._result.TotalRecords>0)for(t=0,r=this._result.Results.length;t<r;t++)n=this._result.Results[t],i="",i=this._enmResultsActionType==$R_ENUM$AutoSearchResultsActionType.Navigate?$RGT_nubButton_Manufacturer(n.ID,n.Name):$R_FN.setCleanTextValue(n.Name),this.addResultItem(i,$R_FN.setCleanTextValue(n.Name),n.ID),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsCategory.registerClass("Rebound.GlobalTrader.Site.Controls.AutoSearch.ProductsCategory",Rebound.GlobalTrader.Site.Controls.AutoSearch.Base,Sys.IDisposable);