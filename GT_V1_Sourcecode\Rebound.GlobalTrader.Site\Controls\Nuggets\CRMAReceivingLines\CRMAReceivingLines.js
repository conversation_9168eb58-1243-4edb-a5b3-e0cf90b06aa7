Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.initializeBase(this,[n]);this._intCRMAID=-1;this._intInvoiceLineAllocationID=-1;this._intCRMALineID=-1;this._intLineCount=0;this._blnLineLoaded=!1;this._intGlobalClientNo=-1;this._serialExist=!1;this._reqSerialNo=!1};Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.prototype={get_intCRMAID:function(){return this._intCRMAID},set_intCRMAID:function(n){this._intCRMAID!==n&&(this._intCRMAID=n)},get_intLineID:function(){return this._intInvoiceLineAllocationID},set_intLineID:function(n){this._intInvoiceLineAllocationID!==n&&(this._intInvoiceLineAllocationID=n)},get_ibtnReceive:function(){return this._ibtnReceive},set_ibtnReceive:function(n){this._ibtnReceive!==n&&(this._ibtnReceive=n)},get_tblAll:function(){return this._tblAll},set_tblAll:function(n){this._tblAll!==n&&(this._tblAll=n)},get_hypPrev:function(){return this._hypPrev},set_hypPrev:function(n){this._hypPrev!==n&&(this._hypPrev=n)},get_hypNext:function(){return this._hypNext},set_hypNext:function(n){this._hypNext!==n&&(this._hypNext=n)},get_lblLineNumber:function(){return this._lblLineNumber},set_lblLineNumber:function(n){this._lblLineNumber!==n&&(this._lblLineNumber=n)},get_pnlLineDetail:function(){return this._pnlLineDetail},set_pnlLineDetail:function(n){this._pnlLineDetail!==n&&(this._pnlLineDetail=n)},get_pnlLoadingLineDetail:function(){return this._pnlLoadingLineDetail},set_pnlLoadingLineDetail:function(n){this._pnlLoadingLineDetail!==n&&(this._pnlLoadingLineDetail=n)},get_pnlLineDetailError:function(){return this._pnlLineDetailError},set_pnlLineDetailError:function(n){this._pnlLineDetailError!==n&&(this._pnlLineDetailError=n)},get_serialExist:function(){return this._serialExist},set_serialExist:function(n){this._serialExist!==n&&(this._serialExist=n)},get_reqSerialNo:function(){return this._reqSerialNo},set_reqSerialNo:function(n){this._reqSerialNo!==n&&(this._reqSerialNo=n)},addSaveReceiveComplete:function(n){this.get_events().addHandler("SaveReceiveComplete",n)},removeSaveReceiveComplete:function(n){this.get_events().removeHandler("SaveReceiveComplete",n)},onSaveReceiveComplete:function(){var n=this.get_events().getHandler("SaveReceiveComplete");n&&n(this,Sys.EventArgs.Empty)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.callBaseMethod(this,"initialize");this._strDataPath="controls/Nuggets/CRMAReceivingLines";this._strDataObject="CRMAReceivingLines";this.addRefreshEvent(Function.createDelegate(this,this.getData));this._tblAll.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));$addHandler(this._hypPrev,"click",Function.createDelegate(this,this.prevLine));$addHandler(this._hypNext,"click",Function.createDelegate(this,this.nextLine));this._ibtnReceive&&($R_IBTN.addClick(this._ibtnReceive,Function.createDelegate(this,this.showReceiveForm)),this._frmReceive=$find(this._aryFormIDs[0]),this._frmReceive.addCancel(Function.createDelegate(this,this.hideReceiveForm)),this._frmReceive.addSaveComplete(Function.createDelegate(this,this.saveReceiveComplete)),this._frmConfirm=$find(this._aryFormIDs[1]),this._frmConfirm.addNotConfirmed(Function.createDelegate(this,this.cancelConfirm)),this._frmConfirm.addSaveComplete(Function.createDelegate(this,this.saveConfirmComplete)));this.getData();$R_FN.showElement(this._pnlLineDetail,!1)},dispose:function(){this.isDisposed||(this._hypPrev&&$clearHandlers(this._hypPrev),this._hypNext&&$clearHandlers(this._hypNext),this._ibtnReceive&&$R_IBTN.clearHandlers(this._ibtnReceive),this._frmReceive&&this._frmReceive.dispose(),this._tblAll&&this._tblAll.dispose(),this._hypPrev=null,this._hypNext=null,this._ibtnReceive=null,this._frmReceive=null,this._intCRMAID=null,this._intLineID=null,this._tblAll=null,this._lblLineNumber=null,this._pnlLineDetail=null,this._pnlLoadingLineDetail=null,this._pnlLineDetailError=null,this._intInvoiceLineAllocationID=null,this._intCRMALineID=null,this._intLineCount=null,this._intGlobalClientNo=null,this._serialExist=null,this._reqSerialNo=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.callBaseMethod(this,"dispose"))},getData:function(){this.enableEditButtons(!1);$R_FN.showElement(this._pnlLineDetail,!1);this.showLoading(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strDataPath);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLines");n.addParameter("id",this._intCRMAID);n.addDataOK(Function.createDelegate(this,this.getDataOK));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataOK:function(n){var i,u,r;if(this.showLoading(!1),i=n._result,u=!1,this._tblAll.clearTable(),i.Lines)for(r=0;r<i.Lines.length;r++){var t=i.Lines[r],f=[$R_FN.writeDoubleCellValue($RGT_nubButton_Stock(t.StockNo,t.Part,t.ROHS),$R_FN.setCleanTextValue(t.CustomerPart)),$R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer,t.MfrAdvisoryNotes),$R_FN.setCleanTextValue(t.DC)),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.Product),$R_FN.setCleanTextValue(t.Package)),$R_FN.writeDoubleCellValue(t.Quantity,t.Received),t.Outstanding,$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(t.ReturnDate),$R_FN.setCleanTextValue(t.Reason))],e={Outstanding:Number.parseLocale(t.Outstanding.toString()),ILAID:t.ILAID};this._tblAll.addRow(f,t.ID,t.ID==this._intCRMALineID,e);u=!0;t=null}this._tblAll.resizeColumns();this.showContent(!0);this.showContentLoading(!1);this.showNoData(!u)},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},enableEditButtons:function(n){n?this._ibtnReceive&&$R_IBTN.enableButton(this._ibtnReceive,this._tblAll.getSelectedExtraData().Outstanding>0&&this._blnLineLoaded):this._ibtnReceive&&$R_IBTN.enableButton(this._ibtnReceive,!1)},tbl_SelectedIndexChanged:function(){this.enableEditButtons(!0);this._intInvoiceLineAllocationID=this._tblAll.getSelectedExtraData().ILAID;this._intCRMALineID=this._tblAll._varSelectedValue;this._intLineCount=this._tblAll.countRows();this.getLineData()},prevLine:function(){var n=this._tblAll._intSelectedIndex-1;n<0||this._tblAll.selectRow(n,!0)},nextLine:function(){var n=this._tblAll._intSelectedIndex+1;n>=this._intLineCount||this._tblAll.selectRow(n,!0)},getLineData:function(){this.showLoading(!0);this._blnLineLoaded=!1;this.enableEditButtons(!1);$R_FN.showElement(this._pnlLoadingLineDetail,!0);$R_FN.showElement(this._pnlLineDetailError,!1);$R_FN.showElement(this._pnlLineDetail,!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("Controls/Nuggets/CRMALines");n.set_DataObject("CRMALines");n.set_DataAction("GetData");n.addParameter("id",this._intCRMALineID);n.addDataOK(Function.createDelegate(this,this.getLineDataOK));n.addError(Function.createDelegate(this,this.getLineDataError));n.addTimeout(Function.createDelegate(this,this.getLineDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getLineDataError:function(n){this.showLoading(!1);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.showElement(this._pnlLineDetailError,!0);$R_FN.setInnerHTML(this._pnlLineDetailError,n.get_ErrorMessage())},getLineDataOK:function(n){this.showLoading(!1);$R_FN.showElement(this._pnlLineDetailError,!1);var t=n._result;this.setFieldValue("ctlQuantityOrdered",t.Quantity);this.setFieldValue("ctlQuantityReceived",t.Received);this.setFieldValue("ctlQuantityOutstanding",Number.parseLocale(t.Quantity.toString())-Number.parseLocale(t.Received.toString()));this.setFieldValue("ctlPartNo",$R_FN.writePartNo(t.Part,t.ROHS));this.setFieldValue("ctlReason",$R_FN.setCleanTextValue(t.Reason));this.setFieldValue("ctlManufacturer",$RGT_nubButton_Manufacturer(t.ManufacturerNo,t.Manufacturer,t.MfrAdvisoryNotes));this.setFieldValue("hidManufacturer",$R_FN.setCleanTextValue(t.Manufacturer));this.setFieldValue("hidManufacturerNo",t.ManufacturerNo);this.setFieldValue("ctlDateCode",$R_FN.setCleanTextValue(t.DC));this.setFieldValue("ctlCustomerPart",$R_FN.setCleanTextValue(t.CustomerPart));this.setFieldValue("ctlProduct",$R_FN.setCleanTextValue(t.Product));this.setFieldValue("hidProductNo",t.ProductNo);this.setFieldValue("ctlPackage",$R_FN.setCleanTextValue(t.Package));this.setFieldValue("hidPackageNo",t.PackageNo);this.setFieldValue("ctlReturnDate",t.ReturnDate);this.setFieldValue("ctlROHS",$R_FN.writeROHS(t.ROHS));this.setFieldValue("hidROHS",t.ROHS);this.setFieldValue("ctlLineNotes",$R_FN.setCleanTextValue(t.LineNotes));this._reqSerialNo=t.ReqSerialNo;$R_FN.showElement(this._pnlLineDetail,!0);$R_FN.showElement(this._pnlLoadingLineDetail,!1);$R_FN.setInnerHTML(this._lblLineNumber,String.format($R_RES.LineXOfY,this._tblAll._intSelectedIndex+1,this._intLineCount));this._blnLineLoaded=!0;this.enableEditButtons(!0)},showReceiveForm:function(){this._frmReceive._intCRMAID=this._intCRMAID;this._frmReceive._intCRMALineID=this._intCRMALineID;this._frmReceive._intInvoiceLineAllocationID=this._intInvoiceLineAllocationID;this._frmReceive._intQuantityOutstanding=Number.parseLocale(this.getFieldValue("ctlQuantityOutstanding").toString());this._frmReceive._intGlobalClientNo=this._intGlobalClientNo;this._frmReceive._reqSerialNo=this._reqSerialNo;this.showForm(this._frmReceive,!0)},hideReceiveForm:function(){this._frmReceive._countSerialRecords>0?(this.showForm(this._frmConfirm,!0),this._frmConfirm._intInvoiceLineID=this._frmReceive._intInvoiceLineID):(this.showForm(this._frmReceive,!1),this._tblAll.resizeColumns())},saveReceiveComplete:function(){this.hideReceiveForm();this.getData();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.onSaveReceiveComplete()},hideConfirmForm:function(){this.showForm(this._frmConfirm,!1)},cancelConfirm:function(){this.hideConfirmForm()},saveConfirmComplete:function(){this.hideConfirmForm()}};Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CRMAReceivingLines",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);