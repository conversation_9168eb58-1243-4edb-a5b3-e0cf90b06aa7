Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Priority=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intPOHubClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Priority");this._objData.set_DataObject("Priority");this._objData.set_DataAction("GetData")},dataCallOK:function(){var t=this._objData._result,n;if(t.Priority)for(n=0;n<t.Priority.length;n++)this.addOption(t.Priority[n].PriorityNo,t.Priority[n].PriorityId)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Priority.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Priority",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);