Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts=function(n){Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.prototype={get_enmCompanyListType:function(){return this._enmCompanyListType},set_enmCompanyListType:function(n){this._enmCompanyListType!==n&&(this._enmCompanyListType=n)},initialize:function(){this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/Contacts";this._strDataObject="Contacts";Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._enmCompanyListType=null,Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.callBaseMethod(this,"dispose"))},getDataOK:function(){for(var n,t=0,i=this._objResult.Results.length;t<i;t++)n=this._objResult.Results[t],this._tbl.addRow([String.format('<a href="{0}">{1}<\/a>',$RGT_gotoURL_Contact(n.ID,this._enmCompanyListType),$R_FN.setCleanTextValue(n.Name?n.Name:"???"))]),strData=null,n=null}};Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Contacts",Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base,Sys.IDisposable);