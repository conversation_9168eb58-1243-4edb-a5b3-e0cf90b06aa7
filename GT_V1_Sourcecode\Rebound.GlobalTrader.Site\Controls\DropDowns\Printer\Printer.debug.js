///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");

Rebound.GlobalTrader.Site.Controls.DropDowns.Printer = function(element) {
    Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.prototype = {
	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.callBaseMethod(this, "initialize");
		this.addSetupDataCall(Function.createDelegate(this, this.setupDataCall));
		this.addGetDataOK(Function.createDelegate(this, this.dataCallOK));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		   Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.callBaseMethod(this, "dispose");
	},
	
	setupDataCall: function() {
	    this._objData.set_PathToData("controls/DropDowns/Printer");
	    this._objData.set_DataObject("Printer");
		this._objData.set_DataAction("GetData");
	},

	dataCallOK: function() { 
		var result = this._objData._result;
		if (result.Printers) {
		    for (var i = 0; i < result.Printers.length; i++) {
		        this.addOption(result.Printers[i].Name, result.Printers[i].ID);
			}
		}
	}
	
};

Rebound.GlobalTrader.Site.Controls.DropDowns.Printer.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Printer", Rebound.GlobalTrader.Site.Controls.DropDowns.Base);
