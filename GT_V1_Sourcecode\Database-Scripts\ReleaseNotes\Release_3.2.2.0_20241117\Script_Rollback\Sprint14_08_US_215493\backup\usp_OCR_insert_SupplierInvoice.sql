﻿/*   
===========================================================================================  
TASK        UPDATED BY       DATE          ACTION    DESCRIPTION  
[US-201932]  Phuc Hoang   08-May-2024  Update   [Production Bug] Delay OCR Import from Nanonets API  
===========================================================================================  
*/  
  
CREATE OR ALTER PROCEDURE [dbo].[usp_OCR_insert_SupplierInvoice]                      
    (                      
    @SupplierInvoiceNumber nvarchar(50) = NULL                              
 ,  @InvoiceDate date = NULL                             
 ,  @BankFee numeric(12,5) = NULL           
 ,  @InvoiceAmount numeric(12,5) = NULL                 
 ,  @TotalTax numeric(12,5) = NULL                 
 ,  @PurchaseOrderNo int = NULL                             
 ,  @GoodsValue numeric(12,5) =NULL                             
 --, @ocr_PurchaseOrderNos as type_ocr_PurchaseOrderId READONLY                          
 ,  @rows_affected int OUT       
 ,  @DeliveryCharge numeric(12,5) = 0       
 ,  @error_message NVARCHAR(MAX) OUT                      
)                      
AS                      
BEGIN TRY                          
                              
--DECLARE @SupplierInvoiceId INT;                              
 BEGIN TRANSACTION                      
 Declare @processValue int;                    
 select  @processValue=count(1) from tbSupplierInvoice where SupplierInvoiceNumber =@SupplierInvoiceNumber;                    
 if @processValue=0                    
 INSERT INTO tbSupplierInvoice                      
    (CompanyNo, ClientNo, SupplierInvoiceDate, SupplierCode, SupplierName, CurrencyNo, InvoiceAmount, GoodsValue, DLUP, UpdatedBy, SupplierInvoiceNumber, CurrencyCode,Tax,isocrgen,BankFee,DeliveryCharge)                      
SELECT top 1                      
    po.CompanyNo                              
  , po.ClientNo                              
  , @InvoiceDate                              
  , c.SupplierCode                              
  , c.CompanyName                              
  , po.CurrencyNo as 'CurrencyNo' --0 'CurrencyNo'                              
  , @InvoiceAmount                              
  , @GoodsValue                              
  , GETDATE() 'DLUP'                              
  , '' 'UpdatedBy'                              
  , @SupplierInvoiceNumber 'SupplierInvoiceNo'                              
  , (select c.CurrencyCode                      
    from tbCurrency c                      
    where c.CurrencyId = po.CurrencyNo)--PO.CurrencyNo                   
 ,@TotalTax,              
 1,            
 @BankFee,      
 @DeliveryCharge      
             
FROM tbPurchaseOrder po                      
    LEFT JOIN tbGoodsIn gi on po.PurchaseOrderId = gi.PurchaseOrderNo                      
    LEFT JOIN tbCompany c ON po.CompanyNo = c.CompanyId                      
WHERE PO.PurchaseOrderNumber = @PurchaseOrderNo                              
                          
  SET @rows_affected = @@ROWCOUNT;                          
  COMMIT TRAN                     
                     
END TRY                          
BEGIN CATCH                          
 SET @error_message = ERROR_MESSAGE();                          
 SET @rows_affected = 0                          
 ROLLBACK TRAN                          
                           
END CATCH         