Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray=function(n){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.initializeBase(this,[n]);this._aryComponents=[];this._intCheckBoxNo=0;this._intHeadingNo=-1;this._intMyTabHeading=-1};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.prototype={get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this.get_element()&&$clearHandlers(this.get_element()),this._tbl=null,this._aryComponents=null,this._intCheckBoxNo=null,this._intHeadingNo=null,this._intMyTabHeading=null,Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.callBaseMethod(this,"dispose"))},addItem:function(n,t,i,r,u,f){var e=this._tbl.insertRow(-1),s,o;if(this.addItemCells(e,n,t,i,!1),r)this.addItemCells(e,r,u,f,!0);else for(s=0;s<3;s++)o=document.createElement("td"),o.innerHTML="&nbsp;",e.appendChild(o),o=null;e=null},addItemCells:function(n,t,i,r,u){var f=[];u&&(f[0]=document.createElement("td"),f[0].className="sep",f[0].innerHTML="&nbsp;",n.appendChild(f[0]));f[1]=document.createElement("td");f[1].className="label";f[1].innerHTML=$R_FN.setCleanTextValue(i);n.appendChild(f[1]);f[2]=document.createElement("td");f[2].className="item";f[2].innerHTML=this.writeCheckbox(t,this._intCheckBoxNo,r);n.appendChild(f[2]);this.registerCheckBox(t,this._intCheckBoxNo,r,!0);this._intCheckBoxNo+=1;f=null},addItemForTabSecurity:function(n,t,i,r,u,f){var e=this._tbl.insertRow(-1);this.addItemCellsForTabSecurity(e,n,t,i,r,u,f);e=null},addItemCellsForTabSecurity:function(n,t,i,r,u,f,e){var o=[],h,s;this._intMyTabHeading+=1;o[1]=document.createElement("td");o[1].className="itemTab";o[1].innerHTML=$R_FN.setCleanTextValue(i);n.appendChild(o[1]);o[2]=document.createElement("td");o[2].className="itemTab";h=this._intCheckBoxNo;o[2].innerHTML=this.writeSecurityCheckbox(t,this._intCheckBoxNo,r);n.appendChild(o[2]);this.registerCheckBox(t,this._intCheckBoxNo,r,!0);s=this.getCheckBox(this._intCheckBoxNo);s._element.setAttribute("onclick",String.format('$find("{0}").clickMyTabCheckBox({1}, {2});',this._element.id,this._intCheckBoxNo,this._intMyTabHeading));s=null;this._intCheckBoxNo+=1;o[3]=document.createElement("td");o[3].className="itemTab";o[3].innerHTML=this.writeSecurityCheckbox(t,this._intCheckBoxNo,u);n.appendChild(o[3]);this.registerCheckBox(t,this._intCheckBoxNo,u,!0);s=this.getCheckBox(this._intCheckBoxNo);s._element.setAttribute("onclick",String.format('$find("{0}").clickExceptMyTabCheckBox({1}, {2});',this._element.id,h,this._intCheckBoxNo));s=null;this._intCheckBoxNo+=1;o[4]=document.createElement("td");o[4].className="itemTab";o[4].innerHTML=this.writeSecurityCheckbox(t,this._intCheckBoxNo,f);n.appendChild(o[4]);this.registerCheckBox(t,this._intCheckBoxNo,f,!0);s=this.getCheckBox(this._intCheckBoxNo);s._element.setAttribute("onclick",String.format('$find("{0}").clickExceptMyTabCheckBox({1}, {2});',this._element.id,h,this._intCheckBoxNo));s=null;this._intCheckBoxNo+=1;o[5]=document.createElement("td");o[5].className="itemTab";o[5].innerHTML=this.writeSecurityCheckbox(t,this._intCheckBoxNo,e);n.appendChild(o[5]);this.registerCheckBox(t,this._intCheckBoxNo,e,!0);s=this.getCheckBox(this._intCheckBoxNo);s._element.setAttribute("onclick",String.format('$find("{0}").clickExceptMyTabCheckBox({1}, {2});',this._element.id,h,this._intCheckBoxNo));s=null;this._intCheckBoxNo+=1;o=null;h=null},addItemCellsForTabSecurityHeader:function(n,t,i,r,u){var e=this._tbl.insertRow(-1),f=[];f[1]=document.createElement("td");f[1].className="itemTabHeader";f[1].innerHTML=$R_FN.setCleanTextValue(n);e.appendChild(f[1]);f[2]=document.createElement("td");f[2].className="itemTabHeader";f[2].innerHTML=$R_FN.setCleanTextValue(t);e.appendChild(f[2]);f[3]=document.createElement("td");f[3].className="itemTabHeader";f[3].innerHTML=$R_FN.setCleanTextValue(i);e.appendChild(f[3]);f[4]=document.createElement("td");f[4].className="itemTabHeader";f[4].innerHTML=$R_FN.setCleanTextValue(r);e.appendChild(f[4]);f[5]=document.createElement("td");f[5].className="itemTabHeader";f[5].innerHTML=$R_FN.setCleanTextValue(u);e.appendChild(f[5]);f=null;e=null},addHeading:function(n,t,i){var u,o;this._intHeadingNo+=1;var f=this._tbl.insertRow(-1),r=document.createElement("td"),e=String.format("heading{0}",this._intHeadingNo);return r.colSpan=5,u=String.format("<h{0}{1}>",n,this._tbl.rows.length==1?' class="first"':""),i&&(u+=String.format("{0}&nbsp;",this.writeCheckbox(e,this._intCheckBoxNo,!1))),u+=String.format("{0}<\/h{1}>",t,n),r.innerHTML=u,f.appendChild(r),f=null,r=null,i&&(this.registerCheckBox(e,this._intCheckBoxNo,!1,!1),o=this.getCheckBox(this._intCheckBoxNo),o._element.setAttribute("onclick",String.format('$find("{0}").clickHeadingCheckBox({1}, {2});',this._element.id,this._intCheckBoxNo,this._intHeadingNo)),this._intCheckBoxNo+=1),this._intCheckBoxNo-1},clickHeadingCheckBox:function(n,t){for(var i,u=this.getCheckBox(n),r=0,f=this._aryComponents.length;r<f;r++)i=$find(this._aryComponents[r].ControlID),i._element.getAttribute("bgt_GroupingNo")==t&&i.setChecked(u._blnChecked),i=null;u=null},clickMyTabCheckBox:function(n,t){var u=this.getCheckBox(n),i,f,r;if(!u._blnChecked)for(i=0,f=this._aryComponents.length;i<f;i++)r=$find(this._aryComponents[i].ControlID),r._element.getAttribute("bgt_SecurityGroupNo")==t&&r.setChecked(u._blnChecked),r=null;u=null},clickExceptMyTabCheckBox:function(n,t){var i=this.getCheckBox(n),r=this.getCheckBox(t);i._blnChecked||r.setChecked(!1);i=null;r=null},getControlID:function(n,t){return String.format("{0}_{1}{2}",this._element.id,n,t)},clearItems:function(){for(var t,n=this._tbl.rows.length-1;n>=0;n--)this._tbl.deleteRow(n);for(n=0,l=this._aryComponents.length;n<l;n++)t=$find(this._aryComponents[n].ControlID),t&&t.dispose(),t=null;Array.clear(this._aryComponents)},getIDs:function(){for(var t=[],n=0,i=this._aryComponents.length;n<i;n++)Array.add(t,this._aryComponents[n].ID);return t},getIDsAsString:function(){return $R_FN.arrayToSingleString(this.getIDs())},getValues:function(){for(var t,i=[],n=0,r=this._aryComponents.length;n<r;n++)t=$find(this._aryComponents[n].ControlID),Array.add(i,t._blnChecked),t=null;return i},getValuesAsString:function(){return $R_FN.arrayToSingleString(this.getValues())},writeCheckbox:function(n,t,i){var r=this.getControlID("chk",t),u=this.getControlID("chkImg",t);return String.format('<div class="imageCheckBoxDisabled" id="{0}" bgt_GroupingNo="{3}"><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /><\/div>',r,u,i?"on":"off",this._intHeadingNo)},writeSecurityCheckbox:function(n,t,i){var r=this.getControlID("chk",t),u=this.getControlID("chkImg",t);return String.format('<div class="imageCheckBoxDisabled" id="{0}" bgt_GroupingNo="{3}" bgt_SecurityGroupNo="{4}" ><img id="{1}" class="{2}" src="images/x.gif" style="border-width: 0px;" /><\/div>',r,u,i?"on":"off",this._intHeadingNo,this._intMyTabHeading)},registerCheckBox:function(n,t,i,r){var u=this.getControlID("chk",t),f=this.getControlID("chkImg",t);eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox",[["blnChecked",i],["blnEnabled","true"],["img",String.format('$get("{0}")',f)]],u));r&&Array.add(this._aryComponents,{ID:n,ControlID:u})},getCheckBox:function(n){return $find(this.getControlID("chk",n))},setCheckBoxValue:function(n,t){var i=$find(this.getControlID("chk",n));i&&i.setChecked(t);i=null}};Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.CheckBoxArray",Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField,Sys.IDisposable);