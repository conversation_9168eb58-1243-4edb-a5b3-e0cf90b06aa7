﻿GO
/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         ACTION		DESCRIPTION
[US-208975]		<PERSON>uc Hoang		23-Aug-2024  UPDATE		Large HUB Offers Import File be imported by BACKEND/ SCRIPT supporting
===========================================================================================
*/


CREATE FUNCTION [dbo].[ufn_Trim_MultipleSpace] (@input VARCHAR(500))
RETURNS VARCHAR(500)
AS BEGIN
    DECLARE @Work VARCHAR(500)

    SET @Work = @input

    SET @Work = REPLACE(REPLACE(REPLACE(@Work,' ','<>'),'><',''),'<>',' ')
    SET @Work = RTRIM(LTRIM(@Work))

    RETURN @work
END
GO
