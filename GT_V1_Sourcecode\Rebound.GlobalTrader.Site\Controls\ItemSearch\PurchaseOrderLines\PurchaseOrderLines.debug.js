///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//----------------------------------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//----------------------------------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");

Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines = function(element) { 
	Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.callBaseMethod(this, "initialize");
		this.addSetupData(Function.createDelegate(this, this.doSetupData));
		this.addGetDataComplete(Function.createDelegate(this, this.doGetDataComplete));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.callBaseMethod(this, "dispose");
	},
	
	doSetupData: function() {
		this._objData.set_PathToData("controls/ItemSearch/PurchaseOrderLines");
		this._objData.set_DataObject("PurchaseOrderLines");
		this._objData.set_DataAction("GetData");
		this._objData.addParameter("Part", this.getFieldValue("ctlPartNo"));
		this._objData.addParameter("CMName", this.getFieldValue("ctlCompany"));
		this._objData.addParameter("IncludeClosed", this.getFieldValue("ctlIncludeClosed"));
		this._objData.addParameter("PurchaseOrderNoLo", this.getFieldValue_Min("ctlPurchaseOrderNo"));
		this._objData.addParameter("PurchaseOrderNoHi", this.getFieldValue_Max("ctlPurchaseOrderNo"));
		this._objData.addParameter("DateOrderedFrom", this.getFieldValue("ctlDateOrderedFrom"));
		this._objData.addParameter("DateOrderedTo", this.getFieldValue("ctlDateOrderedTo"));
		this._objData.addParameter("DateDeliveredFrom", this.getFieldValue("ctlDateDeliveredFrom"));
		this._objData.addParameter("DateDeliveredTo", this.getFieldValue("ctlDateDeliveredTo"));
	},

	doGetDataComplete: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var aryData = [
				row.No,
				$R_FN.setCleanTextValue(row.CMName),
				$R_FN.writePartNo(row.Part, row.ROHS),
				$R_FN.setCleanTextValue(row.DateOrdered),
				row.Price,
				$R_FN.setCleanTextValue(row.DeliveryDate)
			];
			this._tblResults.addRow(aryData, row.ID, false);
			aryData = null; row = null;
		}
	}
};

Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.PurchaseOrderLines", Rebound.GlobalTrader.Site.Controls.ItemSearch.Base, Sys.IDisposable);
