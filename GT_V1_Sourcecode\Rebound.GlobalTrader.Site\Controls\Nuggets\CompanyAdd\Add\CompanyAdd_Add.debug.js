///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
/**********************************************************************************************
Marker     changed by      date         Remarks

[001]      Abhinav       02/09/20011   ESMS Ref:12 - Add new field "Company Registration No" 

* **********************************************************************************************/

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.initializeBase(this, [element]);
	this._intNewID = 0;
	this._txtCompanyName = null;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.prototype = {

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.callBaseMethod(this, "initialize");
		this.addCancel(Function.createDelegate(this, this.cancelClicked));
		this.addSave(Function.createDelegate(this, this.saveClicked));
		this.addShown(Function.createDelegate(this, this.formShown));
	},
	
	dispose: function() { 
		if (this.isDisposed) return;
		if (this._ctlAddress) this._ctlAddress.dispose();
		if (this._txtCompanyName) $clearHandlers(this._txtCompanyName);
		this._ctlAddress = null;
		this._intNewID = null;
		this._txtCompanyName = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.callBaseMethod(this, "dispose");
	},
	
	formShown: function() {
		if (this._blnFirstTimeShown) {
			this._ctlAddress = $find(this.getField("ctlAddress").ID);
			this._ctlAddress._ctlRelatedForm = this;
			this._txtCompanyName = $get(this.getField("ctlCompanyName").ControlID);
			$addHandler(this._txtCompanyName, "change", Function.createDelegate(this, this.updateAddressName));
		}
		this.getFieldDropDownData("ctlSalesman");
		this.getFieldDropDownData("ctlCountry");
	},
	
	cancelClicked: function() {
		$R_FN.navigateBack();
	},

	saveClicked: function() {
		if (!this.validateForm()) return;
		this.showSaving(true);
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/Nuggets/CompanyAdd");
		obj.set_DataObject("CompanyAdd");
        obj.set_DataAction("AddNew");
		obj.addParameter("CompanyName", this.getFieldValue("ctlCompanyName"));
		obj.addParameter("Salesman", this.getFieldValue("ctlSalesman"));
		obj.addParameter("Telephone", this.getFieldValue("ctlTelephone"));
		obj.addParameter("Telephone800", this.getFieldValue("ctlTelephone800"));
		obj.addParameter("Fax", this.getFieldValue("ctlFax"));
		obj.addParameter("EMail", this.getFieldValue("ctlEMail"));
        obj.addParameter("URL", this.getFieldValue("ctlURL"));
        obj.addParameter("VatNo", this.getFieldValue("ctlVATNumber"));
		obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
		obj.addParameter("AddressEntered", this._ctlAddress.addressHasBeenEntered());
		obj.addParameter("AddressName", this.getFieldValue("ctlAddressName"));
		obj.addParameter("AddressLine1", this.getFieldValue("ctlLine1"));
		obj.addParameter("AddressLine2", this.getFieldValue("ctlLine2"));
		obj.addParameter("AddressLine3", this.getFieldValue("ctlLine3"));
		obj.addParameter("Town", this.getFieldValue("ctlTown"));
		obj.addParameter("County", this.getFieldValue("ctlCounty"));
		obj.addParameter("State", this.getFieldValue("ctlState"));
		obj.addParameter("Country", this.getFieldValue("ctlCountry"));
		obj.addParameter("Postcode", this.getFieldValue("ctlPostcode"));
		obj.addParameter("FirstName", this.getFieldValue("ctlFirstName"));
		obj.addParameter("LastName", this.getFieldValue("ctlLastName"));
		//[001] code start
		obj.addParameter("CompRegNo", this.getFieldValue("ctlCmpRegNO"));
		//[001] code end
		//[002] start
		obj.addParameter("CertificateNotes", this.getFieldValue("ctlCertificateNotes"));
		obj.addParameter("QualityNotes", this.getFieldValue("ctlqualityNotes"));
		//[002] end
        obj.addParameter("EORINumber", this.getFieldValue("ctlEORINumber"));
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.NewID > 0) {
			this._intNewID = args._result.NewID;
			this.showSavedOK(true);
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
		this.onValidate();
		var blnOK = this.autoValidateFields();
		if (!this._ctlAddress.validateFields()) blnOK = false;
		if (!blnOK) this.showError(true);
		return blnOK;
	},
	
	updateAddressName: function() {
		this.setFieldValue("ctlAddressName", this.getFieldValue("ctlCompanyName"));
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyAdd_Add", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
