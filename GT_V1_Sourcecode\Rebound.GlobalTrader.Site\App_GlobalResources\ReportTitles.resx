﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="ActiveVendors" xml:space="preserve">
    <value>Active Vendors from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="AllBuyers" xml:space="preserve">
    <value>All Buyers</value>
  </data>
  <data name="AllSalespeople" xml:space="preserve">
    <value>All Salespeople</value>
  </data>
  <data name="AllWarehouses" xml:space="preserve">
    <value>All Warehouses</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Approved Customers On Stop</value>
  </data>
  <data name="AutoEnteredSuppliers_Unedited" xml:space="preserve">
    <value>Auto Entered Suppliers ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="ClosedRequirementsReasons" xml:space="preserve">
    <value>Closed Requirements Reasons</value>
  </data>
  <data name="CommunicationLogActivityforaUser" xml:space="preserve">
    <value>Communication Log Activity for [#EMPLOYEE#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CompaniesApprovedToPurchaseFrom" xml:space="preserve">
    <value>Companies Approved To Purchase From</value>
  </data>
  <data name="CompaniesNotContacted" xml:space="preserve">
    <value>Not Contacted Since [#CUTOFFDATE#]</value>
  </data>
  <data name="ContactEmailList" xml:space="preserve">
    <value>Contact Email List in [#COUNTRY#]</value>
  </data>
  <data name="ContactsNotContacted" xml:space="preserve">
    <value>Contacts Not Contacted Since [#CUTOFFDATE#]</value>
  </data>
  <data name="CreditNotes" xml:space="preserve">
    <value>Credit Notes from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CreditNotesforaCustomer" xml:space="preserve">
    <value>Credit Notes for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CreditNotesforaSalesperson" xml:space="preserve">
    <value>Credit Notes for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CustomerListforSalesperson" xml:space="preserve">
    <value>Customer List for [#SALESPERSON#]</value>
  </data>
  <data name="CustomerOnTimeDeliveryReport" xml:space="preserve">
    <value>Customer On Time Delivery Report from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="CustomerStatement" xml:space="preserve">
    <value>Customer Statement for [#COMPANY_NAME#]  [#LATE_ONLY#]</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Detailed" xml:space="preserve">
    <value>Daily Customer Requirements Details for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Summary" xml:space="preserve">
    <value>Daily Customer Requirements Summary for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="DailyCustomerRequirementsbySalesperson_Totals" xml:space="preserve">
    <value>Daily Customer Requirements Totals for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="DailyImports" xml:space="preserve">
    <value>Daily Imports ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="DailyImportsBySource" xml:space="preserve">
    <value>Daily Imports By Source ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="DaysSinceLastInvoicebyContact" xml:space="preserve">
    <value>Days Since Last Invoice by Contact</value>
  </data>
  <data name="DaysSinceLastInvoicebyCustomer" xml:space="preserve">
    <value>Days Since Last Invoice by Customer</value>
  </data>
  <data name="GoodsReceived" xml:space="preserve">
    <value>Goods received from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="GoodsReceivedNotInvoiced" xml:space="preserve">
    <value>Goods Received Not Invoiced</value>
  </data>
  <data name="GoodsReceivedShipmentDetails" xml:space="preserve">
    <value>Goods Received Shipment Details from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="GrossProfitBreakdown" xml:space="preserve">
    <value>Gross Profit Breakdown from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="IncludesCredits" xml:space="preserve">
    <value>Includes Credits</value>
  </data>
  <data name="IncludesShipping" xml:space="preserve">
    <value>Includes Shipping</value>
  </data>
  <data name="IntrastatReportforEECArrivals_CustomerRMAs" xml:space="preserve">
    <value>Intrastat Report for EEC Arrivals (Customer RMAs) for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="IntrastatReportforEECArrivals_Purchases" xml:space="preserve">
    <value>Intrastat Report for EEC Arrivals (Purchases) for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="IntrastatReportforEECDispatches_Sales" xml:space="preserve">
    <value>Intrastat Report for EEC Dispatches (Sales) for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="IntrastatReportforEECDispatches_SupplierRMAs" xml:space="preserve">
    <value>Intrastat Report for EEC Dispatches (Supplier RMAs) for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="InventoryLocationReport" xml:space="preserve">
    <value>Inventory Location Report for [#WAREHOUSE#]</value>
  </data>
  <data name="InventoryLocationReportforLot" xml:space="preserve">
    <value>Inventory Location Report for Lot [#LOT#] at [#WAREHOUSE#] </value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumber" xml:space="preserve">
    <value>Invoices Sorted by Invoice Number from [#STARTDATE#] to [#ENDDATE#] [#INCLUDING_SHIPPING#]</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Invoices Sorted by Invoice Number for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="InvoicesSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Invoices Sorted by Invoice Number for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="LoginStatistics" xml:space="preserve">
    <value>Login Statistics  ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="LoginStatisticsbyName" xml:space="preserve">
    <value>Login Statistics for [#USERNAME#] ([#STARTDATE#] - [#ENDDATE#])</value>
  </data>
  <data name="NumberofAccountsbySalesperson" xml:space="preserve">
    <value>Number of Accounts by Salesperson</value>
  </data>
  <data name="NumberofOffersbyVendor" xml:space="preserve">
    <value>Offers by Supplier </value>
    <comment>was "Supplier Offers"</comment>
  </data>
  <data name="NumberofOffersHistorybyVendor" xml:space="preserve">
    <value>Offers by Supplier - History</value>
    <comment>was "Supplier Offers in History"</comment>
  </data>
  <data name="NumberofRequirementsbyVendor" xml:space="preserve">
    <value>Requirements by Customer</value>
    <comment>was "Supplier Offers in History"</comment>
  </data>
  <data name="OpenCustomerRMAs" xml:space="preserve">
    <value>Open Customer RMAs from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Open Customer RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Open Customer RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Open Customer RMAs for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Open Customer RMAs for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenCustomerRMAswithReasons" xml:space="preserve">
    <value>Open Customer RMAs from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenRequirementsbyCustomer" xml:space="preserve">
    <value>Open Requirements by Customer</value>
  </data>
  <data name="OpenRequirementsReportBySalesperson" xml:space="preserve">
    <value>Open Requirements for [#SALESPERSON#]</value>
  </data>
  <data name="OpenSalesOrders" xml:space="preserve">
    <value>Open Sales Orders [#POSTED_STATUS#]</value>
  </data>
  <data name="OpenSalesOrdersforSalesperson" xml:space="preserve">
    <value>Open Sales Orders for [#SALESPERSON#][#POSTED_STATUS#]</value>
  </data>
  <data name="OpenSupplierRMAs" xml:space="preserve">
    <value>Open Supplier RMAs from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Open Supplier RMAs for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Open Supplier RMAs for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Open Supplier RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Open Supplier RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenSupplierRMAswithReasons" xml:space="preserve">
    <value>Open Supplier RMAs from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OrdersToBeShipped" xml:space="preserve">
    <value>Outstanding Orders to be Shipped [#DUEDATE#]</value>
  </data>
  <data name="OrdersToBeShippedBySalesperson" xml:space="preserve">
    <value>Outstanding Orders to be Shipped [#DUEDATE#] for [#SALESPERSON#] </value>
  </data>
  <data name="OustandingCustomerInvoices" xml:space="preserve">
    <value>Outstanding Customer Invoices [#LATE_ONLY#]</value>
  </data>
  <data name="PickSheetSalesOrdersBasic" xml:space="preserve">
    <value>Pick Sheet for [#DUEDATE#]</value>
  </data>
  <data name="PickSheetSalesOrdersDetailed" xml:space="preserve">
    <value>Pick Sheet for [#DUEDATE#]</value>
  </data>
  <data name="PickSheetSupplierRMAs" xml:space="preserve">
    <value>Supplier RMA Pick Sheet for [#DUEDATE#]</value>
  </data>
  <data name="PostedOnly" xml:space="preserve">
    <value>Posted Lines Only</value>
  </data>
  <data name="PurchaseOrdersDueIn" xml:space="preserve">
    <value>Purchase Orders Due In from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="PurchaseOrdersDueInforBuyer" xml:space="preserve">
    <value>Purchase Orders Due In for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="PurchaseOrdersDueInforSalesperson" xml:space="preserve">
    <value>Purchase Orders Due In for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="PurchaseRequisitions" xml:space="preserve">
    <value>Purchase Requisitions</value>
  </data>
  <data name="PurchaseRequisitionsforaCustomer" xml:space="preserve">
    <value>Purchase Requisitions for [#COMPANY_NAME#]</value>
  </data>
  <data name="PurchaseRequisitionsforaSalesPerson" xml:space="preserve">
    <value>Purchase Requisitions for [#SALESPERSON#]</value>
  </data>
  <data name="ReceivedCustomerRMAs" xml:space="preserve">
    <value>Received Customer RMAs from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomer" xml:space="preserve">
    <value>Received Customer RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAsforaCustomerwithReasons" xml:space="preserve">
    <value>Received Customer RMAs with Reasons for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSaleperson" xml:space="preserve">
    <value>Received Customer RMAs for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAsforaSalepersonwithReasons" xml:space="preserve">
    <value>Received Customer RMAs with Reasons for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ReceivedCustomerRMAswithReasons" xml:space="preserve">
    <value>Received Customer RMAs with Reasons from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ReceivedGoodsValuationbyCountry" xml:space="preserve">
    <value>Received Goods Valuation by Country for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ShippedGoodsValuationbyCountry" xml:space="preserve">
    <value>Shipped Goods Valuation by Country for [#WAREHOUSE#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumber" xml:space="preserve">
    <value>Shipped Orders from [#STARTDATE#] to [#ENDDATE#] Ordered by Invoice Number</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaCustomer" xml:space="preserve">
    <value>Shipped Orders for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#] Ordered by Invoice Number</value>
  </data>
  <data name="ShippedOrdersSortedbyInvoiceNumberforaSalesperson" xml:space="preserve">
    <value>Shipped Orders for [#SALESPERSON#] from [#STARTDATE#] to [#ENDDATE#] Ordered by Invoice Number</value>
  </data>
  <data name="ShippedSalesforLot" xml:space="preserve">
    <value>Shipped Sales for Lot [#LOT#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAs" xml:space="preserve">
    <value>Shipped Supplier RMAs from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyer" xml:space="preserve">
    <value>Shipped Supplier RMAs for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAsforaBuyerwithReasons" xml:space="preserve">
    <value>Shipped Supplier RMAs for [#BUYER#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplier" xml:space="preserve">
    <value>Shipped Supplier RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAsforaSupplierwithReasons" xml:space="preserve">
    <value>Shipped Supplier RMAs for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="ShippedSupplierRMAswithReasons" xml:space="preserve">
    <value>Shipped Supplier RMAs from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="StockCount" xml:space="preserve">
    <value>Stock Count for [#WAREHOUSE#]</value>
  </data>
  <data name="StockList" xml:space="preserve">
    <value>Stock List</value>
  </data>
  <data name="StockValuation" xml:space="preserve">
    <value>Stock Valuation for [#WAREHOUSE#]</value>
  </data>
  <data name="SummaryBookedOrdersbyCustomer" xml:space="preserve">
    <value>Summary Booked Orders by Customer from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping</value>
  </data>
  <data name="SummaryBookedOrdersbyDivision" xml:space="preserve">
    <value>Summary Booked Orders by Division from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping</value>
  </data>
  <data name="SummaryBookedOrdersbySalesperson" xml:space="preserve">
    <value>Summary Booked Orders by Salesperson from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping</value>
  </data>
  <data name="SummaryOpenOrdersbyCustomer" xml:space="preserve">
    <value>Summary Open Orders by Customer from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping</value>
  </data>
  <data name="SummaryOpenOrdersbyDivision" xml:space="preserve">
    <value>Summary Open Orders by Division from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping</value>
  </data>
  <data name="SummaryOpenOrdersbySalesperson" xml:space="preserve">
    <value>Summary Open Orders by Salesperson from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping</value>
  </data>
  <data name="SummaryShippedSalesbyCustomer" xml:space="preserve">
    <value>Summary Shipped Sales by Customer from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping[#INCLUDING_CREDITS#]</value>
  </data>
  <data name="SummaryShippedSalesbyDivision" xml:space="preserve">
    <value>Summary Shipped Sales by Division from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping[#INCLUDING_CREDITS#]</value>
  </data>
  <data name="SummaryShippedSalesbySalesperson" xml:space="preserve">
    <value>Summary Shipped Sales by Salesperson from [#STARTDATE#] to [#ENDDATE#] - Includes Shipping[#INCLUDING_CREDITS#]</value>
  </data>
  <data name="SupplierOnTimeDeliveryReport" xml:space="preserve">
    <value>Supplier On Time Delivery Report from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="UserList" xml:space="preserve">
    <value>User List</value>
  </data>
  <data name="OpenQuotes" xml:space="preserve">
    <value>Open Quotes</value>
  </data>
  <data name="IncludeConfirmed" xml:space="preserve">
    <value>Include Confirmed</value>
  </data>
  <data name="OpenPurchaseOrders" xml:space="preserve">
    <value>Open Purchase Orders from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenPurchaseOrdersbyCompanyType" xml:space="preserve">
    <value>Open Purchase Orders for [#COMPANY_TYPE#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="OpenPurchaseOrdersbySupplier" xml:space="preserve">
    <value>Open Purchase Orders for [#COMPANY_NAME#] from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="InvalidCompanyPurchasingInfo" xml:space="preserve">
    <value>Companies with Invalid / Missing Purchasing Information [#INVALID_ONLY#]</value>
  </data>
  <data name="InvalidCompanySalesInfo" xml:space="preserve">
    <value>Companies with Invalid / Missing Sales Information [#INVALID_ONLY#]</value>
  </data>
  <data name="InvalidOnly" xml:space="preserve">
    <value>Invalid Only</value>
  </data>
  <data name="LateOnly" xml:space="preserve">
    <value>Late Only</value>
  </data>
  <data name="RandomStockCheck" xml:space="preserve">
    <value>Random Stock Check for [#WAREHOUSE#]</value>
  </data>
  <data name="BulkEmailInvoiceStatus" xml:space="preserve">
    <value>Bulk Email Invoice Status Report from [#STARTDATE#] to [#ENDDATE#]</value>
  </data>
  <data name="EmailStatus" xml:space="preserve">
    <value>Invoice Email Status</value>
  </data>
  <data name="LotStockProvision" xml:space="preserve">
    <value>[#LOT#]</value>
  </data>
  <data name="PurchaseQuote" xml:space="preserve">
    <value>Price Request summary</value>
  </data>
  <data name="RequirementWithBOM" xml:space="preserve">
    <value>Requirement summary With HUBRFQ</value>
  </data>
  <data name="DailyReportLog" xml:space="preserve">
    <value>Daily Report Log</value>
  </data>
  <data name="IncludeUnconfirmed" xml:space="preserve">
    <value>Include Unconfirmed</value>
  </data>
</root>