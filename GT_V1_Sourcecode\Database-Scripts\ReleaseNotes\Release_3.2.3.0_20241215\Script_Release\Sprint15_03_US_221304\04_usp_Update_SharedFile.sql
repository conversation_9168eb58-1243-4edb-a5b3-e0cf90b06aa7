﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
/*   
===========================================================================================  
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-211441]		Trung Pham Van		24-Sep-2024		CREATE		Update date when offer is sent
[US-215028]		Trung Pham Van		06-Nov-2024		UPDATE		Update SentProspectiveOfferAt for customer requirement(s) 
[US-215028]		Trung Pham Van		12-Nov-2024		UPDATE		Remove unused column (SentDate)
[US-221304]		Trung Pham Van		14-Nov-2024		CREATE		Add NewOfferPriceFromProspective column
===========================================================================================  
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Update_SharedFile]
	@ProspectiveOfferId INT,
	@ProspectiveOfferLineIds VARCHAR(MAX),
	@CustomerRequirementIds VARCHAR(MAX),
	@JsonData NVARCHAR(MAX)
AS
BEGIN
	IF @JsonData <> ''
	BEGIN
		DECLARE @NewPriceData TABLE (
			Id INT,
			NewPrice FLOAT
		);

		INSERT INTO @NewPriceData (Id, NewPrice)
		SELECT Id, NewPrice
		FROM OPENJSON(@JsonData) 
		WITH (
			Id INT '$.Id',
			NewPrice FLOAT '$.Price'
			);

		UPDATE tbCustomerRequirement
		SET SentProspectiveOfferAt = GETDATE()
		WHERE CustomerRequirementId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@CustomerRequirementIds, ','))

		UPDATE cr
		SET cr.NewOfferPriceFromProspective = d.NewPrice
		FROM tbCustomerRequirement AS cr
		JOIN @NewPriceData AS d ON d.Id = cr.CustomerRequirementId
		WHERE cr.CustomerRequirementId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@CustomerRequirementIds, ','))
	END
	ELSE
	BEGIN
		UPDATE tbCustomerRequirement
		SET SentProspectiveOfferAt = GETDATE()
		WHERE CustomerRequirementId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@CustomerRequirementIds, ','))
	END
END
