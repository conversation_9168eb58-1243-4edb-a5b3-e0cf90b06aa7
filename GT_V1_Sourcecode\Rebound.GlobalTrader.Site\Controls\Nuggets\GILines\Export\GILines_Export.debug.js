///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.initializeBase(this, [element]);
    this._intGIID = 0;
    this._intLineID = -1;
    this._selectedImageList = [];
    this._Lot = "";
    this._SupplierType = "";
    this._Quantity = "";
    this._PartNo = "";
    this._Manufacturer = "";
    this._DateCode = "";
    this._Package = "";
    this._MSL = "";
    this._HICStatus = "";
    this._RohsStatus = "";
    this._CountryOfManufacture = "";
    this._ReqSerailNo = "";
    this._LotCodeReq = "";
    this._GeneralInspectionNotes = "";
    this._BakingAdded = "";
    this._CountryOfOrigin = "";
    this._StockNo = -1;
    this._Mode = "";

    this._ActeoneTestStatus = 0;
    this._IsopropryleStatus = 0;
    this._ActeoneTest = "";
    this._Isopropryle = "";
    this._CountingMethodName = "";
    this._IsInspectionConducted = false;
};


Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.prototype = {
    get_strMedium: function () { return this._strMedium; }, set_strMedium: function (value) { if (this._strMedium !== value) this._strMedium = value; },
    get_strLarge: function () { return this._strLarge; }, set_strLarge: function (value) { if (this._strLarge !== value) this._strLarge = value; },
    get_pnlImages: function () { return this._pnlImages; }, set_pnlImages: function (value) { if (this._pnlImages !== value) this._pnlImages = value; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addSave(Function.createDelegate(this, this.saveClicked));

    },

    formShown: function () {
        this.storeOriginalFieldValues();
        this.getData();
        this._chkSelectAllImage = $find("ctl00_cphMain_ctlLines_ctlDB_ctl14_frmExport_ctlDB_ctlSelectAllImage_ctl03_chkSelectAllImage");
        this._chkSelectAllImage.addClick(Function.createDelegate(this, this.SelectAllImage));
    },
    SelectAllImage: function () {
        var CheckedStatus = this.getFieldValue("ctlSelectAllImage");
        this._selectedImageList = [];
        for (var i = 0; i < this._intCountImages; i++) {
            if (CheckedStatus == true) {
                var id = 'ctl00_cphMain_ctlLines_ctlDB_ctl14_frmExport_ctlDB_chk' + i;
                $('#' + id).prop('checked', true).trigger('change');
            }
            else {
                var id = 'ctl00_cphMain_ctlLines_ctlDB_ctl14_frmExport_ctlDB_chk' + i;
                $('#' + id).prop('checked', false).trigger('change');
            }
        }
    },
    getFormControlID: function (ParentId, controlID) {

        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    dispose: function () {
        if (this.isDisposed) return;
        this._intGIID = null;
        this._intLineID = null;
        this._selectedImageList = null;
        this._Lot = null;
        this._SupplierType = null;
        this._Quantity = null;
        this._PartNo = null;
        this._Manufacturer = null;
        this._DateCode = null;
        this._Package = null;
        this._MSL = null;
        this._HICStatus = null;
        this._RohsStatus = null;
        this._CountryOfManufacture = null;
        this._ReqSerailNo = null;
        this._LotCodeReq = null;
        this._GeneralInspectionNotes = null;
        this._BakingAdded = null;
        this._CountryOfOrigin = null;
        this._Mode = null;
        Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.callBaseMethod(this, "dispose");
    },
    saveClicked: function () {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/GILines");
        obj.set_DataObject("GILines");
        obj.set_DataAction("ExportPDFWord");
        obj.addParameter("id", this._intLineID);
        obj.addParameter("GoodsIn", this.getFieldValue("ctlGoodsIn"));
        obj.addParameter("Lot", this._Lot);
        obj.addParameter("SupplierType", this._SupplierType);
        obj.addParameter("Quantity", this._Quantity);
        obj.addParameter("PartNo", this._PartNo);
        obj.addParameter("Manufacturer", this._Manufacturer);
        obj.addParameter("DateCode", this._DateCode);
        obj.addParameter("Package", this._Package);
        obj.addParameter("MSL", this._MSL);
        obj.addParameter("HICStatus", this._HICStatus);
        obj.addParameter("RohsStatus", this._RohsStatus);
        obj.addParameter("CountryOfManufacture", this._CountryOfManufacture);
        obj.addParameter("ReqSerailNo", this._ReqSerailNo);
        obj.addParameter("LotCodeReq", this._LotCodeReq);
        obj.addParameter("GeneralInspectionNotes", this._GeneralInspectionNotes);
        obj.addParameter("BakingAdded", this._BakingAdded);
        obj.addParameter("SelectedImageList", this._selectedImageList);
        obj.addParameter("CountryOfOrigin", this._CountryOfOrigin);

        obj.addParameter("ActeoneTestStatus", this._ActeoneTestStatus);
        obj.addParameter("IsopropryleStatus", this._IsopropryleStatus);
        obj.addParameter("ActeoneTest", this._ActeoneTest);
        obj.addParameter("Isopropryle", this._Isopropryle);
        obj.addParameter("CountingMethodName", this._CountingMethodName);
        obj.addParameter("IsInspectionConducted", this._IsInspectionConducted);

        obj.addParameter("IsPDFExport", this._Mode == "PDF" ? true : false);
        obj.addParameter("IsWordExport", this._Mode == "WORD" ? true : false);
        obj.addParameter("IsGeneralNotesAdd", this.getFieldValue("ctlIsGeneralNotesAdd"));
        obj.addParameter("ExternalNotes", this.getFieldValue("ctlExternalNotes"));

        obj.addDataOK(Function.createDelegate(this, this.saveExportComplete));
        obj.addError(Function.createDelegate(this, this.saveExportError));
        obj.addTimeout(Function.createDelegate(this, this.saveExportError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveExportError: function (args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveExportComplete: function (args) {
        
        if (args._result.Result == true) {
            var url = window.location.origin;
            if (args._result.PDFFilePath != "") {
                window.open(url + args._result.PDFFilePath, "winPrint", "left=20,top=20,width=770,height=574,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=yes,scrollbars=yes");
            }
            if (args._result.WordFilePath != "") {
                window.open(url + args._result.WordFilePath, '_blank');
            }
            this.onSaveComplete();
        } else {
            var message = args._result.Message == "" ? args._errorMessage : args._result.Message;
            this.showMessage(true, message);
            return false;
        }

    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        return blnOK;
    },
    showMessage: function (bln, strText) {
        $R_FN.showElement(this._pnlValidateError, bln);
        if (bln) {
            $R_FN.showElement(this._pnlSaving, false);
            $R_FN.showElement(this._pnlSavedOK, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._pnlContentInner, true);
            $R_FN.showElement(this._pnlLinksHolder, true);
            $R_FN.showElement(this._pnlFooterLinksHolder, true);
            if (this._ctlRelatedNugget) {
                this._ctlRelatedNugget.control.showLoading(false);
                this._ctlRelatedNugget.control.showRefresh(true);
            }
            if (!strText) strText = "";
            this._pnlValidateErrorText.innerHTML = strText;
        }
    },
    getData: function () {
        this._intCountImages == 0;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/StockMultipleImageDragDrop");
        obj.set_DataObject("StockMultipleImageDragDrop");
        obj.set_DataAction("GetData");
        //obj.addParameter("id", 907096);
        obj.addParameter("id", this._StockNo);
        obj.addDataOK(Function.createDelegate(this, this.getDataOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    getDataOK: function (args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlImages, "");
        var strImages = "";
        if (result.Items) {
            this.intCountImages = result.Items.length;
            for (var i = 0; i < result.Items.length; i++) {
                var row = result.Items[i];
                strImages += "<div class=\"stockImage\">";
                strImages += String.format("<img id=\"{0}_img{1}\" src=\"{2}\" border=\"0\" />", this._element.id, i, this.getImageSource(row.ID, "t", '' + row.ImageName + '', "STOCKIMGFORSAN"));
                strImages += "<div class=\"stockImageCaption\">";
                strImages += String.format("<input type=\"checkbox\" id=\"{0}_chk{1}\" value=\"{2}\" onchange=\"$find('{3}').getImageValue('{4}','{5}_chk{6}','{7}');\" />", this._element.id, i, this.getImageSource(row.ID, "f", '' + row.ImageName + '', "STOCKIMGFORSAN"), this._element.id, this.getImageSource(row.ID, "f", '' + row.ImageName + '', "STOCKIMGFORSAN"), this._element.id, i, row.Caption);
                if (row.Caption) strImages += row.Caption + "<br />";
                strImages += row.Date;
                if (row.By) strImages += "<br />" + row.By;
                strImages += String.format("<br /><a href=\"javascript:void(0);\" onclick=\"$find('{0}').popupImage({1}, 'm','{4}','STOCKIMGFORSAN');\">{2}</a> | <a href=\"javascript:void(0);\" onclick=\"$find('{0}').popupImage({1}, 'f','{4}','STOCKIMGFORSAN');\">{3}</a>", this._element.id, row.ID, this._strMedium, this._strLarge, row.ImageName);
                strImages += "</div>";
                strImages += "</div>";
                row = null;
            }
            this._intCountImages = result.Items.length;
        }
        $R_FN.setInnerHTML(this._pnlImages, strImages);
    },

    getDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },
    getDataCount: function () {
        this.getData_Start();
        this._intCountImages == 0;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/StockMultipleImageDragDrop");
        obj.set_DataObject("StockMultipleImageDragDrop");
        obj.set_DataAction("GetData");
        obj.addParameter("id", this._intStockID);
        obj.addDataOK(Function.createDelegate(this, this.getDataCountOK));
        obj.addError(Function.createDelegate(this, this.getDataError));
        obj.addTimeout(Function.createDelegate(this, this.getDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getDataCountOK: function (args) {
        var res = args._result;
        var result = args._result;
        $R_FN.setInnerHTML(this._pnlImages, "");
        var strImages = "";
        if (result.Items) {
            this._intCountImages = result.Items.length;
            strImages += "<div style=\"height:50px;\"></div>";
        }
        $R_FN.setInnerHTML(this._pnlImages, strImages);
        this.getDataOK_End();
        this.enableViewButton();
        this.viewButtonAfterDelete(this._intCountImages > 0);
    },
    getImageSource: function (intID, strType, strImageName, strstockimage) {
        return String.format("StockImage.ashx?img={0}&typ={1}&imagename={2}&imagesourcefrom={3}", intID, strType, strImageName, strstockimage);
    },

    // [001] code start
    popupImage: function (intID, strType, strFileName, strstockimage) {
        if (strType == 'm')
            window.open(this.getImageSource(intID, strType, strFileName, strstockimage), "", "left=300,top=250,width=654,height=488,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes");
        else
            window.open(this.getImageSource(intID, strType, strFileName, strstockimage), "", "left=300,top=170,width=802,height=602,toolbar=no,resizable=yes,status=no,directories=no,location=no,menubar=no,scrollbars=yes");
    },
    getImageValue: function (ImageName, chkBoxId, Caption) {
        
        var status = document.getElementById(chkBoxId).checked;
        if (status == true) {
            //if (this._selectedImageList.includes(ImageName) == false) {
            //    this._selectedImageList.push(ImageName);
            //}
            //if (Caption != '')
            //    this._selectedImageList.push(ImageName + "##" + Caption);
            //else
            //    this._selectedImageList.push(ImageName);
            this._selectedImageList.push(ImageName + "#" + Caption);
        }
        else {
            //if (Caption != '')
            //    this._selectedImageList.pop(ImageName + "##" + Caption);
            //else
            //    this._selectedImageList.pop(ImageName);
            this._selectedImageList.pop(ImageName + "#" + Caption);
        }

    },

    readTextFile: function (file) {
        var rawFile = new XMLHttpRequest();
        rawFile.open("GET", file, false);
        rawFile.onreadystatechange = function () {
            if (rawFile.readyState === 4) {
                if (rawFile.status === 200 || rawFile.status == 0) {
                    var allText = rawFile.responseText;
                    alert(allText);
                }
            }
        }
        rawFile.send(null);

    }
};

Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.GILines_Export", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
