﻿//using System;
//using System.Collections.Generic;
//using System.Data;
//using System.Data.OleDb;
//using System.IO;
//using System.Linq;
//using System.Web;
//using System.Web.Mvc;
//using ExcelDataReader;
//using Microsoft.IdentityModel.Tokens;
//using Rebound.GlobalTrader.Site.Areas.BOM.Data;
//using Rebound.GlobalTrader.Site.Areas.BOM.Models;
//using System.Threading.Tasks;
//using Newtonsoft.Json;
//using System.Text;
//using System.Web.Script.Serialization;
//using Microsoft.Azure.Storage.Blob;
//using Microsoft.Azure.Storage.Auth;
//using Microsoft.Azure.Storage;
//using System.Configuration;
//using System.Net.Http;
//using System.Reflection;
//using Rebound.GlobalTrader.BLL;
//using Microsoft.ApplicationInsights;

//namespace Rebound.GlobalTrader.Site.Areas.BOM.Controllers
//{
//    public class SalesBomSheetController : Controller
//    {
//        // GET: BOM/SalesBomSheet
//        SalesBomSheetModel model = new SalesBomSheetModel();
//        SalesBomServiceRequest salesBomserviceRequest = new SalesBomServiceRequest();
//        string accountname = ConfigurationManager.AppSettings.Get("StorageName");
//        string accesskey = ConfigurationManager.AppSettings.Get("StorageKey1");
//        List<ExcelFileColumnsModel> excelFileColumn=new List<ExcelFileColumnsModel>();

//        List<ExcelFileColumnsModel> excelFileColumnHeader = new List<ExcelFileColumnsModel>();

//        StorageCredentials creden;
//        CloudStorageAccount acc;
//        CloudBlobClient cbclient;

//        public SalesBomSheetController()
//        {
//            creden = new StorageCredentials(accountname, accesskey);
//            acc = new CloudStorageAccount(creden, useHttps: true);
//            cbclient = acc.CreateCloudBlobClient();
//        }

//        public ActionResult DownLoadUploadBomSheet()
//        {
//            //SalesBomSheetModel model = new SalesBomSheetModel();
//            //SalesBomServiceRequest salesBomserviceRequest = new SalesBomServiceRequest();
//            HttpRequestBase requestBase = HttpContext.Request;
//            System.Web.HttpContext curr = System.Web.HttpContext.Current;
//            ExcelFileColumnsModel excelFileColumnsModel = new ExcelFileColumnsModel();

//            try
//            {
//                model = salesBomserviceRequest.FillDefault(model);
//                ViewData["clients"] = model.clients;
//                ViewData["currency"] = model.currency;
//                // excelFileColumnsModel = new ExcelFileColumnsModel();
//                excelFileColumnHeader.Add(new ExcelFileColumnsModel
//                {
//                    Id = 0,
//                    ColumnsName = "select"
//                });
//                ViewData["excelColumnName"] = excelFileColumnHeader;

//                List<RequiremenTraceability> requiremenTraceability = new List<RequiremenTraceability>
//                    {
//                        new RequiremenTraceability { Id = 8, RequiremenTraceabilityName = "Manufacturer C of C" },
//                        new RequiremenTraceability { Id = 9, RequiremenTraceabilityName = "Distributor C of C"},
//                        new RequiremenTraceability { Id = 10, RequiremenTraceabilityName = "Authorised channel only" },
//                        new RequiremenTraceability { Id = 11, RequiremenTraceabilityName = "No traceability required" }
//                    };

//                ViewData["RequiremenTraceability"] = requiremenTraceability;

//                List<UploadType> uploadTypes = new List<UploadType>
//                    {
//                        new UploadType { Id = 5, DataType = "Shortage" },
//                        new UploadType { Id = 6, DataType = "PPV"},
//                        new UploadType { Id = 7, DataType = "Noise" },
//                        new UploadType { Id = 14, DataType = "Spot buy" },
//                        new UploadType { Id = 15, DataType = "BOM Upload" },
//                        new UploadType { Id = 16, DataType = "Repeat Business" },
//                        new UploadType { Id = 17, DataType = "BOM Manager Upload" }
//                    };

//                ViewData["UploadType"] = uploadTypes;

//                //DataTable dt= salesBomserviceRequest.FillDefaultDDL(curr);
//            }
//            catch(Exception ex)
//            {
//                model.ErrorCode = ex.StackTrace;
//                model.ErrorMessages = ex.Message;
//                model.ErrorId = ex.Source;
//            }
           
//            return View(model);
//        }

//        [HttpPost]
//        public ActionResult DownLoadUploadBomSheet(string submitButton,SalesBomSheetModel bomSheetModel)
//        {
//            try
//            {
//                model = salesBomserviceRequest.FillDefault(model);

//                //if (submitButton == Resources.Messages.RetrieveSupplierMappings)
//                //{
//                //    // delegate sending to another controller action
//                //    return (RetrieveSupplierMappings());

//                //}
//                //else if (submitButton == Resources.Messages.Cancel)
//                //{
//                //    // call another action to perform the cancellation
//                //    return (Cancel());
//                //}

//                switch (submitButton)
//                {
//                    case "RetrieveSupplierMappings":
//                        // delegate sending to another controller action
//                       // var str=ImportBOMangerdata(bomSheetModel);
//                        return (RetrieveSupplierMappings());
//                    case "Cancel":
//                        // call another action to perform the cancellation
//                        return (SaveSupplierMappings());
//                    default:
//                        // If they've submitted the form without a submitButton, 
//                        // just return the view again.
//                        return (View());
//                }
//            }
//            catch (Exception ex)
//            {
//                model.ErrorCode = ex.StackTrace;
//                model.ErrorMessages = ex.Message;
//                model.ErrorId = ex.Source;
//            }
//            return View(model);
//        }

//        private JsonResult RetrieveSupplierMappings()
//        {
//            return null;
//        }

//        private JsonResult SaveSupplierMappings()
//        {
//            return null;
//        }

//        [HttpPost]
//        public ActionResult SalesBomExcelImport()
//        {
//            //string filePath = null;
//            //model = salesBomserviceRequest.FillDefault(model);
//            try
//            {
//                var filePath1 = ExcelFile();
//                // GetExcel();
//                //ViewBag.ExcelFileData = GetExcelFile();
//                model.excelFileModel = GetExcelFile(model.ExcelBOMFileName);
//            }
//            catch (Exception ex)
//            {
//                model.ErrorCode = ex.StackTrace;
//                model.ErrorMessages = ex.Message;
//                model.ErrorId = ex.Source;
//            }
//            return View(model);
//        }

//        [HttpPost]
//        public JsonResult DisplayRawBOMData()
//        {
//            try
//            {
//                ////string filePath = null;
//                model = salesBomserviceRequest.FillDefault(model);
//                var filePath1 = ExcelFile();

//                // GetExcel();
//                //ViewBag.ExcelFileData = GetExcelFile().ToList();            

//                //JsonResult result = new JsonResult();
//                if (filePath1.Data.ToString() != "No files selected.")
//                {
//                    var result = Json(GetExcelFile(model.ExcelBOMFileName).ToList(), JsonRequestBehavior.AllowGet);
//                    //if (result!=null)
//                    //{
//                    //    ExcelFileColumn= GetExcelFileColumn(model.ExcelBOMFileName);
//                    //}

//                    return result;
//                }
//            }
//            catch (Exception ex)
//            {
//                model.ErrorCode = ex.StackTrace;
//                model.ErrorMessages = ex.Message;
//                model.ErrorId = ex.Source;
//            }
//            return null;
//        }

//        //[HttpPost]
//        //public string DisplayRawBOMDataString()
//        //{
//        //    ////string filePath = null;
//        //    //model = salesBomserviceRequest.FillDefault(model);
//        //    //var filePath1 = ExcelFile();

//        //    // GetExcel();
//        //    //ViewBag.ExcelFileData = GetExcelFile().ToList();
//        //    //
//        //    var list = GetExcelFile().ToList();

//        //    JsonResult result = new JsonResult();
//        //    //model.userModel= ViewBag.ExcelFileData;
//        //    result = this.Json(JsonConvert.SerializeObject(list), JsonRequestBehavior.AllowGet);

//        //    var arr = list.ToArray();

//        //    StringBuilder sb = new StringBuilder(@"{""data"":");

//        //    JavaScriptSerializer js = new JavaScriptSerializer();
//        //    String json = js.Serialize(list);
//        //    sb.Append(json);

//        //    sb.Append("}");
//        //    return sb.ToString();

//        //    //return arr;
//        //}

//        [HttpPost]
//        public ActionResult UploadFiles()
//        {
//            // Checking no of files injected in Request object  
//            if (Request.Files.Count > 0)
//            {
//                try
//                {
//                    //  Get all files from Request object  
//                    HttpFileCollectionBase files = Request.Files;
//                    for (int i = 0; i < files.Count; i++)
//                    {
//                        //string path = AppDomain.CurrentDomain.BaseDirectory + "Uploads/";  
//                        //string filename = Path.GetFileName(Request.Files[i].FileName);  

//                        HttpPostedFileBase file = files[i];
//                        string fname;

//                        // Checking for Internet Explorer  
//                        if (Request.Browser.Browser.ToUpper() == "IE" || Request.Browser.Browser.ToUpper() == "INTERNETEXPLORER")
//                        {
//                            string[] testfiles = file.FileName.Split(new char[] { '\\' });
//                            fname = testfiles[testfiles.Length - 1];
//                        }
//                        else
//                        {
//                            fname = file.FileName;
//                        }

//                        // Get the complete folder path and store the file inside it.  
//                        fname = Path.Combine(Server.MapPath("~/Uploads/"), fname);
//                        file.SaveAs(fname);
//                    }
//                    // Returns message that successfully uploaded  
//                    return Json("File Uploaded Successfully!");
//                }
//                catch (Exception ex)
//                {
//                    return Json("Error occurred. Error details: " + ex.Message);
//                }
//            }
//            else
//            {
//                return Json("No files selected.");
//            }
//        }

//        //[HttpPost]
//        //[ValidateAntiForgeryToken]
//        //public ActionResult Upload(HttpPostedFileBase upload)
//        //{
//        //    if (ModelState.IsValid)
//        //    {

//        //        if (upload != null && upload.ContentLength > 0)
//        //        {
//        //            // ExcelDataReader works with the binary Excel file, so it needs a FileStream
//        //            // to get started. This is how we avoid dependencies on ACE or Interop:
//        //            Stream stream = upload.InputStream;

//        //            // We return the interface, so that
//        //            IExcelDataReader reader = null;


//        //            if (upload.FileName.EndsWith(".xls"))
//        //            {
//        //                reader = ExcelReaderFactory.CreateBinaryReader(stream);
//        //            }
//        //            else if (upload.FileName.EndsWith(".xlsx"))
//        //            {
//        //                reader = ExcelReaderFactory.CreateOpenXmlReader(stream);
//        //            }
//        //            else
//        //            {
//        //                ModelState.AddModelError("File", "This file format is not supported");
//        //                return View();
//        //            }

//        //            reader.IsFirstRowAsColumnNames = true;

//        //            DataSet result = reader.AsDataSet();
//        //            reader.Close();

//        //            return View(result.Tables[0]);
//        //        }
//        //        else
//        //        {
//        //            ModelState.AddModelError("File", "Please Upload Your file");
//        //        }
//        //    }
//        //    return View();
//        //}


//        private JsonResult ExcelFile()
//        {
//            if (Request.Files.Count > 0)
//            {
//                try
//                {
//                    //  Get all files from Request object  
//                    HttpFileCollectionBase files = Request.Files;
//                    for (int i = 0; i < files.Count; i++)
//                    {
//                        HttpPostedFileBase file = files[i];
//                        string fname;

//                        // Checking for Internet Explorer  
//                        if (Request.Browser.Browser.ToUpper() == "IE" || Request.Browser.Browser.ToUpper() == "INTERNETEXPLORER")
//                        {
//                            string[] testfiles = file.FileName.Split(new char[] { '\\' });
//                            fname = testfiles[testfiles.Length - 1];
//                        }
//                        else
//                            fname = file.FileName;

//                        //var currentdatetime = (SessionManager.ClientID+System.DateTime.Now.ToString("yyyyMMddHHmmss"));
//                        //model.ExcelBOMFileName = fname.Split('.')[0]+ currentdatetime+'.'+fname.Split('.')[1];

//                        model.ExcelBOMFileName = fname;

//                        // Get the complete folder path and store the file inside it.
//                        var fileNamePath = Path.Combine(Server.MapPath("/Areas/BOM/Uploads/"));

//                        if (!Directory.CreateDirectory(fileNamePath).Exists)
//                        {
//                            var bareFilename = Path.GetFileNameWithoutExtension(file.FileName);
//                            var fileExt = Path.GetExtension(file.FileName);
                            
//                            //save to dir first
//                            var dir = Directory.CreateDirectory(fileNamePath);
//                            file.SaveAs(fileNamePath);
//                        }

//                        using (var exportData = new MemoryStream())
//                        {
//                            fileNamePath = fileNamePath + fname;
//                            FileStream filess = new FileStream(fileNamePath, FileMode.Create, FileAccess.Write);
//                            exportData.WriteTo(filess);                            
//                            filess.Close();
//                        }

//                        file.SaveAs(fileNamePath);
//                    }
//                    // Returns message that successfully uploaded  
//                    return Json("File Uploaded Successfully!");
//                }
//                catch (Exception ex)
//                {
//                    return Json("Error occurred. Error details: " + ex.Message);
//                }
//            }
//            else
//            {
//                return Json("No files selected.");
//            }
//        }

//        private List<ExcelFileModel> GetExcelFile(string ExcelFileName)
//        {
//            try
//            {
//                //var fileName = "./Users.xlsx";
//                //var fileName = Path.Combine(Server.MapPath("/Areas/BOM/Uploads/" + ExcelFileName));
//                var fileName = FileUploadManager.GetTemporaryUploadFilePath() + ExcelFileName;
//                List<ExcelFileModel> _excelFileModel = new List<ExcelFileModel>();
//                List<ExcelFileModel> _excelFileHeaderModel = new List<ExcelFileModel>();
//                //List<ExcelFileModel> _excelFileColumnModel = new List<ExcelFileModel>();
//                // For .net core, the next line requires NuGet package, 

//                DataTable customTable = new DataTable();

//                int firstrw = 0;
//                System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);

//                using (var stream = System.IO.File.Open(fileName, FileMode.Open, FileAccess.Read))
//                {
//                    using (var reader = ExcelReaderFactory.CreateReader(stream))
//                    {
//                        while (reader.Read()) //Each ROW
//                        {
//                            if(firstrw==0)
//                            {
//                                _excelFileHeaderModel.Add(new ExcelFileModel
//                                {
//                                    StockCode = reader.GetValue(0).ToString(),
//                                    Description = reader.GetValue(1).ToString(),
//                                    Part = reader.GetValue(2).ToString(),
//                                    RFQ = reader.GetValue(3).ToString(),
//                                    UnitPrice = reader.GetValue(4).ToString(),
//                                    LineTotal = reader.GetValue(5).ToString()
//                                });

//                                DataTable dt = new DataTable();
//                                dt = salesBomserviceRequest.ToDataTable(_excelFileHeaderModel);
//                                //ViewData["excelColumnName"] = salesBomserviceRequest.ToDataTable(_excelFileModel);

//                                excelFileColumnHeader.Add(new ExcelFileColumnsModel
//                                {
//                                    Id = 0,
//                                    ColumnsName = "select"
//                                });

//                                for (int i=0;i<= dt.Columns.Count-1;i++)
//                                {
//                                        excelFileColumnHeader.Add(new ExcelFileColumnsModel
//                                        {
//                                            Id = i+1,                                           
//                                            ColumnsName = dt.Rows[0].ItemArray[i].ToString()
//                                        });                                   
//                                }

//                                ViewData["excelColumnName"] = excelFileColumnHeader.ToList();
//                            }
//                            if (firstrw >= 0)
//                            {
//                                _excelFileModel.Add(new ExcelFileModel
//                                {
//                                    StockCode = reader.GetValue(0).ToString(),
//                                    Description = reader.GetValue(1).ToString(),
//                                    Part = reader.GetValue(2).ToString(),
//                                    RFQ = reader.GetValue(3).ToString(),
//                                    UnitPrice = reader.GetValue(4).ToString(),
//                                    LineTotal = reader.GetValue(5).ToString()
//                                });
//                            }

//                            firstrw++;
//                        }

//                        ViewBag.excelFileDataName = _excelFileModel;
//                        customTable = salesBomserviceRequest.ToDataTable(_excelFileModel);
//                        ViewData["ExcelFileData"] = customTable;
//                    }
//                }

//                return _excelFileModel;
//            }
//            catch (Exception ex)
//            {
//                //return Json("Error occurred. Error details: " + ex.Message);
//                return null;
//            }

//            return null;
//        }        

//        [HttpPost]
//        public JsonResult SearchCompanyName(string companyName)
//        {
//            //model = salesBomserviceRequest.CompanyNameList(model);
//            if (!string.IsNullOrEmpty(companyName))
//            {
//                //var result = Json(salesBomserviceRequest.CompanyNameList(companyName), JsonRequestBehavior.AllowGet);
//                //JavaScriptSerializer js = new JavaScriptSerializer();
//                var result= Json(salesBomserviceRequest.CompanyNameList(companyName).ToList(), JsonRequestBehavior.AllowGet);
//                return result;
//            }
            
//            return null;
//        }

//        //private List<string> GetExcelFileColumn(string ExcelFileName)
//        //{
//        //    //var fileName = "./Users.xlsx";
//        //    var fileName = Path.Combine(Server.MapPath("/Areas/BOM/Uploads/" + ExcelFileName));
//        //    List<ExcelFileModel> _excelFileModel = new List<ExcelFileModel>();
//        //    // For .net core, the next line requires NuGet package, 
//        //    // System.Text.Encoding.CodePages
//        //    int firstrw = 0;
//        //    System.Text.Encoding.RegisterProvider(System.Text.CodePagesEncodingProvider.Instance);
//        //    using (var stream = System.IO.File.Open(fileName, FileMode.Open, FileAccess.Read))
//        //    {
//        //        using (var reader = ExcelReaderFactory.CreateReader(stream))
//        //        {
//        //            while (reader.Read()) //Each ROW
//        //            {
//        //                if (firstrw == 0)
//        //                {
//        //                    _excelFileModel.Add(new ExcelFileModel
//        //                    {
//        //                        StockCode = reader.GetValue(0).ToString(),
//        //                        Description = reader.GetValue(1).ToString(),
//        //                        Part = reader.GetValue(2).ToString(),
//        //                        RFQ = reader.GetValue(3).ToString(),
//        //                        UnitPrice = reader.GetValue(4).ToString(),
//        //                        LineTotal = reader.GetValue(5).ToString()
//        //                    });
//        //                }

//        //                firstrw++;
//        //            }
//        //        }
//        //    }
//        //    return _excelFileModel;
//        //}

//        [HttpPost]
//        public ActionResult UploadExcelFileOnCloud(FormCollection collection)
//        {
//            HttpFileCollectionBase files = Request.Files;
//            //var formData = Request.Form;

//            int selectedclientId = int.Parse(collection["clientId"]);
//            string digitalCurrency = collection["digitalCurrency"];
//            string defaultcurrency = collection["Defaultcurrency"];
//            string selectedClientType = collection["SelectedClientType"];
//            string chkcolumnheader = collection["chkcolumnheader"];

//            //HttpFileCollection httpFileCollection = Request.;

//            string strExcelSection = "UtilitySales_BOMImport_STKI";

//            if (HttpContext.Request.Files.Count > 0)
//            {
//                HttpPostedFileBase file = files[0];

//                string strFile = string.Empty;
//                string[] strArray = strExcelSection.Split('_');

//                if (strArray.Length == 3 && strArray[0].Length > 1 && strArray[1].Length > 1)
//                {
//                    //testing code start
//                    string strDir = FileUploadManager.GetTemporaryUploadFilePath();
//                    //if (!Directory.Exists(strDir)) Directory.CreateDirectory(strDir);
//                    //testing code end

//                    string filEextn = Path.GetExtension(file.FileName);
//                    if (filEextn == ".xlsx")
//                        strFile = string.Format(Functions.GetGlobalResource("Misc", "ExcelFileName"), selectedclientId, strArray[0].ToUpper(), strArray[1], DateTime.Now.ToString("yyyyMMddHHmmssfff"));
//                    else if (filEextn == ".xls")
//                        strFile = string.Format(Functions.GetGlobalResource("Misc", "ExcelXlsFileName"), selectedclientId, strArray[0].ToUpper(), strArray[1], DateTime.Now.ToString("yyyyMMddHHmmssfff"));
//                    else
//                        strFile = string.Format(Functions.GetGlobalResource("Misc", "ExcelCsvFileName"), selectedclientId, strArray[0].ToUpper(), strArray[1], DateTime.Now.ToString("yyyyMMddHHmmssfff"));

//                    if (strExcelSection == "Bom_BomImport_BMI" || strExcelSection == "EPO_EpoImport_Stock" || strExcelSection == "StockImport_StockImport_STKI" || 
//                        strExcelSection == "UtilityBOM_BOMImport_STKI" || strExcelSection == "Utility_StockImport_STKI" || strExcelSection == "UtilitySales_BOMImport_STKI")
//                    {
//                        System.IO.File.WriteAllBytes(strDir + strFile, ReadData(file.InputStream));
//                        file.InputStream.Position = 0;
//                    }

//                    CloudBlobContainer cont = cbclient.GetContainerReference("gtdocmgmt");
//                    if (cont.Exists())
//                    {
//                        CloudBlobDirectory directory = cont.GetDirectoryReference(strArray[0].ToUpper());
//                        //cont.SetPermissions(new BlobContainerPermissions { PublicAccess = BlobContainerPublicAccessType.Blob });
//                        CloudBlockBlob cblob = directory.GetBlockBlobReference(strFile);
//                        if (filEextn == ".xlsx")
//                        {
//                            cblob.Properties.ContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
//                        }
//                        else if (filEextn == ".xls")
//                        {
//                            cblob.Properties.ContentType = "application/vnd.ms-excel";
//                        }
//                        using (Stream fileStream = file.InputStream)
//                        {
//                            cblob.UploadFromStream(fileStream);

//                            model.ExcelBOMFileName = strFile;

//                            // Get the complete folder path and store the file inside it.
//                            //var fileNamePath = Path.Combine(Server.MapPath("/Areas/BOM/Uploads/"));
//                            //string fileNamePath = FileUploadManager.GetTemporaryUploadFilePath();

//                            if (!Directory.CreateDirectory(strDir).Exists)
//                            {
//                                var bareFilename = Path.GetFileNameWithoutExtension(file.FileName);
//                                var fileExt = Path.GetExtension(file.FileName);

//                                //save to dir first
//                                var dir = Directory.CreateDirectory(strDir);
//                                file.SaveAs(strDir);
//                            }

//                            using (var exportData = new MemoryStream())
//                            {
//                                strDir = strDir + strFile;
//                                FileStream filess = new FileStream(strDir, FileMode.Create, FileAccess.Write);
//                                exportData.WriteTo(filess);
//                                filess.Close();
//                            }

//                            file.SaveAs(strDir);

//                            //var result =Json(GetExcelFile(model.ExcelBOMFileName).ToList(), JsonRequestBehavior.AllowGet);
//                            //return result;

//                            var excelFileData = GetExcelFile(model.ExcelBOMFileName).ToList();
//                            var excelFileColumn = GetExcelFileColumn().ToList();

//                            //var exceldata = ConvertDataTableToJSON((DataTable)ViewData["ExcelFileData"]);
//                            var excel =(from o in ((DataTable)ViewData["ExcelFileData"]).AsEnumerable()
//                                        select o).ToList();

//                            //excelFileData.RemoveAt(0);
//                            //var finalData = from d in excelFileData
//                            //                from c in excelFileColumn where c.Id=d.
//                            var variable_name = Newtonsoft.Json.JsonConvert.SerializeObject(excel);

//                            var result = new { Result = excelFileData, ExcelFileColumn = excelFileColumn };

//                            //Int32? clientId = clientIdformData;
//                            salesBomserviceRequest.ImportCSVData(strFile, file.FileName,0, selectedclientId.ToString(), chkcolumnheader, digitalCurrency);

//                            JsonObject jsnItem = new JsonObject();

                            
//                            return Json(result, JsonRequestBehavior.AllowGet);
//                        }
//                    }
//                    else
//                    {
//                        //container not exit
//                    }
//                    if (cont.Exists())    //if (System.IO.File.Exists(strDir + strFile))
//                    {
//                        //JsonObject jsn = new JsonObject();
//                        JsonObject jsnItem = new JsonObject();
//                        jsnItem.AddVariable("Result", true);
//                        jsnItem.AddVariable("FileName", strFile);
//                        //jsn.AddVariable(jsnItem);
//                        //context.Response.Write(jsnItem.Result);
//                    }
//                }
//                else
//                {
//                    JsonObject jsnItem = new JsonObject();
//                    jsnItem.AddVariable("Result", false);
//                    jsnItem.AddVariable("FileName", "");
//                    //context.Response.Write(jsnItem.Result);
//                    jsnItem = null;
//                }
//            }

//            return Json("File not found", JsonRequestBehavior.AllowGet); ;
//        }


//        [HttpPost]
//        public JsonResult MyController(HttpPostedFileBase excelFile)
//        {
//            if (Request.Files.Count > 0)
//            {
//                foreach (string file in Request.Files)
//                {
//                    excelFile = Request.Files[file];
//                }
//                var result = //Call Model here for validation checks and return error msges
//                TempData["ExcelFile"] = excelFile; //Store in TempData for further processing
//                return Json(result);
//            }
//            return null;
//        }

//        [HttpPost]
//        public List<ExcelFileColumnsModel> GetExcelFileColumn()
//        {
//            //var result = Json(ViewData["excelColumnName"], JsonRequestBehavior.AllowGet);
//            //return result;
//            List<ExcelFileColumnsModel> excelFileColumns = new List<ExcelFileColumnsModel>();
//            excelFileColumns = (List<ExcelFileColumnsModel>)(System.Collections.IEnumerable)ViewData["excelColumnName"];

//            return excelFileColumns;
//        }

//        [HttpPost]
//        public JsonResult SearchCurreny(int clientId)
//        {
//            //model = salesBomserviceRequest.CompanyNameList(model);
//            if (clientId!=0)
//            {
//                var result = Json(salesBomserviceRequest.GetCurrencyForClient(clientId).ToList(), JsonRequestBehavior.AllowGet);
//                return result;
//            }

//            return null;
//        }

//        [HttpPost]
//        public string GetCompanyAndOtherMasterData(string ClientId)
//        {
//            HttpFileCollectionBase files = Request.Files;
//            if (!string.IsNullOrEmpty(ClientId))
//            {
//                var dtResult = salesBomserviceRequest.GetCompanyAndOtherMasterData(ClientId);
//                var result = ConvertDataTableToJSON(dtResult);
//                //var result = Json(dtResult.Rows, JsonRequestBehavior.AllowGet);
//                return result;
//            }
//            return null;
//        }


//        [HttpPost]
//        public ActionResult AutoSearchSales(string ClientId, string Searchvalue)
//        {
//            //HttpFileCollectionBase files = Request.Files;
//            if (!string.IsNullOrEmpty(ClientId))
//            {
//                var dtResult = salesBomserviceRequest.AutoSearchSale(ClientId, Searchvalue+"%").ToList();
//                //var result = ConvertDataTableToJSON(dtResult);
//                //var result = Json(dtResult.Rows, JsonRequestBehavior.AllowGet);
//                return Json(dtResult, JsonRequestBehavior.AllowGet);

//                //StringBuilder sb = new StringBuilder();
//                //JavaScriptSerializer js = new JavaScriptSerializer();
//                //String json = js.Serialize(dtResult);
//                //sb.Append(json);
//                //sb.Append("}");
//                //return this.Content(sb.ToString(), "text/text");
//            }            
//            return null;
//        }

//        [HttpPost]
//        public ActionResult GenrateBOMManagerData(string company_Id, string companyName_Text, string selectedclient_Id, string ddlCurrency_short, string insertDataList_Colmn,string columnLable_text, string column_Name, string contactName, string contact_Id, string chkOverRide, string digitalCurrency_Name, string defaultCurrency_Id, string currencyColumn_Name, string requiremenTraceability, string type, string currentDateTime)
//        {
//            //int selectedclientId = int.Parse(collection["clientId"]);
//            //int CompanyId = string.IsNullOrEmpty(collection["company_Id"]) ? 0 : int.Parse(collection["company_Id"]);
//            //, string clientId, string company,string salesperson,string ddlClient,string bOMName,Company_Name = company;
//            //string RequirementforTraceability = requiremenTraceability, Type = type;
//            //Selectedclient_Id="", ddl_Currency= digitalCurrency, insertData_List="",  ColumnLable="", ColumnName="", ContactName="", Contact_Id="",
//            //chkOver_Ride="", DefaultCurrency_Name="", DefaultCurrency_Id= digitalCurrency, CurrencyColumn_Name="";
//            //DateTime DateTimeRequired = DateTime.Today;

//            string Column_Lable = "";
//            string insertDataList = string.Empty;
//            string ColumnName = "";
//            DataTable dtGenrateImport = new DataTable();
//            dtGenrateImport.Clear();

//            #region Stock checkbox paramete                
//            int RequirementforTraceabilityId = string.IsNullOrEmpty(requiremenTraceability) ? 0 : int.Parse(requiremenTraceability);
//            int TypeId = string.IsNullOrEmpty(type) ? 0 : int.Parse(type);
//            DateTime DateRequired = string.IsNullOrEmpty(currentDateTime) ? DateTime.Now : Convert.ToDateTime(currentDateTime);
//            //int clientType_con = string.IsNullOrEmpty(context.Request.QueryString["clientType"]) ? 0 : int.Parse(context.Request.QueryString["clientType"]);
//            int CompanyId = string.IsNullOrEmpty(company_Id) ? 0 : int.Parse(company_Id);
//            string CompanyNameText = string.IsNullOrEmpty(companyName_Text) ? "" : Convert.ToString(companyName_Text);
//            //int recordType = string.IsNullOrEmpty(context.Request.QueryString["recordType"]) ? 0 : int.Parse(context.Request.QueryString["recordType"]);
//            int SelectedclientId = string.IsNullOrEmpty(selectedclient_Id) ? 0 : int.Parse(selectedclient_Id);
//            //checkbox parameter
//            #endregion

//            #region Stock  Dropdwon parameter
//            string ddlCurrency = string.IsNullOrEmpty(ddlCurrency_short) ? "" : Convert.ToString(ddlCurrency_short);
//            //string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
//            //mapped selected column list
//            insertDataList = string.IsNullOrEmpty(insertDataList_Colmn) ? "" : Convert.ToString(insertDataList_Colmn);
//            Column_Lable = string.IsNullOrEmpty(columnLable_text) ? "" : Convert.ToString(columnLable_text);
//            ColumnName = string.IsNullOrEmpty(column_Name) ? "" : Convert.ToString(column_Name);

//            string ContactText = string.IsNullOrEmpty(contactName) ? "" : Convert.ToString(contactName);
//            int ContactId = string.IsNullOrEmpty(contact_Id) ? 0 : int.Parse(contact_Id);

//            string FixedCurrency = string.Empty;

//            bool OverRideCurrency = string.IsNullOrEmpty(chkOverRide) ? false : bool.Parse(chkOverRide);
//            string DefaultCurrencyName = string.IsNullOrEmpty(digitalCurrency_Name) ? "" : Convert.ToString(digitalCurrency_Name);
//            int DefaultCurrencyId = string.IsNullOrEmpty(defaultCurrency_Id) ? 0 : int.Parse(defaultCurrency_Id);
//            string CurrencyColumnName = string.IsNullOrEmpty(currencyColumn_Name) ? "" : Convert.ToString(currencyColumn_Name);
//            if (OverRideCurrency)
//                ddlCurrency = DefaultCurrencyName;
//            #endregion

//            if (CompanyId != null)
//            {
//                DataTable Result = salesBomserviceRequest.GenrateBOMManagerData(RequirementforTraceabilityId.ToString(), TypeId.ToString(), DateRequired, CompanyId.ToString(),
//                    CompanyNameText, SelectedclientId.ToString(), ddlCurrency, insertDataList, Column_Lable, ColumnName, ContactText, ContactId.ToString(),
//                    OverRideCurrency.ToString(), DefaultCurrencyName, DefaultCurrencyId.ToString(), CurrencyColumnName);

//                ////DataTable Result = salesBomserviceRequest.GenrateBOMManagerData(collection);
//                //var tt = Result.DataSet;
//                //var tts =(from o in Result.DataSet.Tables[5].AsEnumerable()
//                //          select o).ToList();

//                ////List<T> lstCallAssignement = tt.Rows;
//                //var rss = ConvertDataTableToJSON(Result).ToArray();
//                ////var rsss = ConvertDataTableToJSON(tt).ToArray();
//                //var resultcom = (from o in Result.AsEnumerable()
//                //                 select o
//                //               ).ToList();

//                //var filterdata = (from o in tts.ToList()
//                //                  select o).ToList();
//                //var ff =(from o in filterdata.ToList()
//                //    select o).ToList();

//                //var ntable = (from a in filterdata
//                //              select a).ToList();

//                //var excelFileData = GetExcelFile(model.ExcelBOMFileName).ToList();
//                //var ss = SalesBomSheetController.ConvertDataTable<ImportDataModel>(Result).ToList();

//                List<ImportDataModel> list = new List<ImportDataModel>();
//                for (int i = 0; i < Result.Rows.Count; i++)
//                {
//                    ImportDataModel userinfo = new ImportDataModel();
//                    userinfo.Company = Convert.ToString(Result.Rows[i]["Company"]);
//                    userinfo.Contact = Convert.ToString(Result.Rows[i]["Contact"]);
//                    userinfo.Currency = Convert.ToString(Result.Rows[i]["Currency"]);
//                    userinfo.Manufacturer = Convert.ToString(Result.Rows[i]["Manufacturer"]);
//                    userinfo.Part = Convert.ToString(Result.Rows[i]["Part"]);
//                    userinfo.Quantity = Convert.ToString(Result.Rows[i]["Quantity"]);
//                    userinfo.Price = Convert.ToString(Result.Rows[i]["Price"]);
//                    list.Add(userinfo);
//                }

//                return Json(list, JsonRequestBehavior.AllowGet);
//            }
//            return null;
//        }

//        [HttpPost]
//        public ActionResult ImportBOMManagerdata()
//        {
//            //string ClientId,string company_Id,string companyName_Text,string selectedclient_Id,string ddlCurrency_short,string insertDataList_Colmn,string columnLable_text,string column_Name,string contactName, string chkOverRide,string digitalCurrency_Name,string currencyColumn_Name,string requiremenTraceability,string type,string currentDateTime,string BOMName
//            ////HttpFileCollectionBase files = Request.Files;
//            ////HttpRequestBase formdatd = Request.RequestContext;
//            ////int selectedclientId = int.Parse(context["clientId"]);
//            //int selectedclientId = int.Parse(context["clientId"]);
//            //string digitalCurrency = context["digitalCurrency"];
//            //string defaultcurrency = context["Defaultcurrency"];
//            //string selectedClientType = context["SelectedClientType"];
//            string Column_Lable = "";
//            string insertDataList = "";
//            string Column_Name = "";

//            string clientId = Request["clientId"];
//            string clientIdName = Request["clientIdName"];
//            string digitalcurrencyId = Request["digitalcurrencyId"];
//            string digitalcurrencyName = Request["digitalcurrencyName"];
//            string chkDefaultCurrent = Request["chkDefaultCurrent"];

//            string companyId = Request["companyId"];
//            string companyName = Request["companyName"];
//            string salespersonId = Request["salespersonId"];
//            string salesperson = Request["salespersonName"];
//            string applyPartwatch = Request["applyPartwatch"];

//            string contactId = Request["contactId"];
//            string contactName = Request["contactName"];
//            string bOMName = Request["bOMName"];

//            string insertDataListData = Request["insertDataList"];
//            string column_LableData = Request["column_Lable"];            
//            string column_NameData = Request["column_Name"];

//            string requiremenTraceabilityId = Request["requiremenTraceabilityId"];
//            string requiremenTraceabilityName = Request["requiremenTraceabilityName"];
//            string type = Request["type"];
//            string currentDateTime = Request["currentDateTime"];
            
//            string ActionPerformName = (Request["ActionPerform"]);
//            string SaveImportOrHubRFQData = Request["SaveImportOrHubRFQ"];
//            if (SaveImportOrHubRFQData == "SaveImportOrHubRFQ")
//                ActionPerformName = "ImportData";
//            string btnImportName = Request["btnImport"];
//            string searchvalue = Request["search"];
//            //string ApplyPartWatchId = Request["ApplyPartWatch"];
//            //string ddlCurrencyname = Request["applyPartwatch"];

//            //string Column_LableName = Request["Column_Lable"];
//            //string insertDataListName = Request["insertDataList"];
//            //string Column_Namedata = Request["Column_Name"];


//            //string BomUserName = Request["BOMName"];
//            //string selectedCompanyName = Request["Company"];
//            //string ContactPersonName = Request["ContactName"];
//            //string SalesmanPersonId = Request["SalesmanId"];
//            //string SelectedCompanyId = Request["CompanyId"];
//            //string SelectedContactId = Request["ContactId"];

//            //string SelectedchkOverRide = Request["chkOverRide"];
//            //string SelectedDefaultCurrencyId = Request["DefaultCurrencyId"];
//            //string selectedclientId = Request["selectedclientId"];


//            ////salesBomserviceRequest.ImportBOMManagerdata(strFile, file.FileName, 0, selectedclientId.ToString(), chkcolumnheader, digitalCurrency);
//            //salesBomserviceRequest.ImportBOMManagerdata(ClientId, company_Id, companyName_Text, selectedclient_Id, ddlCurrency_short, insertDataList_Colmn, columnLable_text, column_Name, contactName, chkOverRide, digitalCurrency_Name, currencyColumn_Name, requiremenTraceability, type, currentDateTime, BOMName);
//            //salesBomserviceRequest.ImportBOMManagerdata(formdata);

//            try
//            {
//                #region Bom tool paramete
//                new Errorlog().LogMessage("BOM Import method started " + DateTime.UtcNow.ToString());
//                //int ReqforTraceabilityId = string.IsNullOrEmpty(Request.Request.QueryString["requirementforTraceability"]) ? 0 : int.Parse(context.Request.QueryString["RequirementforTraceability"]);
//                //int TypeId = string.IsNullOrEmpty(context.Request.QueryString["Type"]) ? 0 : int.Parse(context.Request.QueryString["Type"]);
//                //DateTime DateRequired = string.IsNullOrEmpty(context.Request.QueryString["DateRequired"]) ? DateTime.Now : Convert.ToDateTime((context.Request.QueryString["DateRequired"]));
//                //int SelectedclientId = string.IsNullOrEmpty(context.Request.QueryString["SelectedclientId"]) ? 0 : int.Parse(context.Request.QueryString["SelectedclientId"]);
//                //string ActionPerform = string.IsNullOrEmpty(context.Request.QueryString["ActionPerform"]) ? "" : Convert.ToString((context.Request.QueryString["ActionPerform"]));
//                //string SaveImportOrHubRFQ = string.IsNullOrEmpty(context.Request.QueryString["SaveImportOrHubRFQ"]) ? "" : Convert.ToString((context.Request.QueryString["SaveImportOrHubRFQ"]));

//                int ReqforTraceabilityId = string.IsNullOrEmpty(requiremenTraceabilityId) ? 0 : int.Parse(requiremenTraceabilityId);
//                int TypeId = string.IsNullOrEmpty(type) ? 0 : int.Parse(type);
//                DateTime DateRequired = string.IsNullOrEmpty(currentDateTime) ? DateTime.Now : Convert.ToDateTime(currentDateTime);
//                int SelectedclientId = string.IsNullOrEmpty(clientId) ? 0 : int.Parse(clientId);
//                string ActionPerform = string.IsNullOrEmpty(ActionPerformName) ? "" : Convert.ToString(ActionPerformName);
//                string SaveImportOrHubRFQ = string.IsNullOrEmpty(SaveImportOrHubRFQData) ? "" : Convert.ToString(SaveImportOrHubRFQData);
//                //checkbox parameter
//                #endregion

//                #region Bom  Dropdwon parameter
//                //string ddlCurrency = string.IsNullOrEmpty(context.Request.QueryString["ddlCurrency"]) ? "" : Convert.ToString((context.Request.QueryString["ddlCurrency"]));
//                //string btnImport = string.IsNullOrEmpty(context.Request.QueryString["btnImport"]) ? "" : Convert.ToString((context.Request.QueryString["btnImport"]));
//                ////mapped selected column list
//                //Column_Lable = string.IsNullOrEmpty(context.Request.QueryString["Column_Lable"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Lable"]));
//                //insertDataList = string.IsNullOrEmpty(context.Request.QueryString["insertDataList"]) ? "" : Convert.ToString((context.Request.QueryString["insertDataList"]));
//                //Column_Name = string.IsNullOrEmpty(context.Request.QueryString["Column_Name"]) ? "" : Convert.ToString((context.Request.QueryString["Column_Name"]));

//                string ddlCurrency = string.IsNullOrEmpty(digitalcurrencyName) ? "" : Convert.ToString(digitalcurrencyName);
//                string btnImport = string.IsNullOrEmpty(btnImportName) ? "" : Convert.ToString(btnImportName);
//                //mapped selected column list
//                Column_Lable = string.IsNullOrEmpty(column_LableData) ? "" : Convert.ToString(column_LableData);
//                insertDataList = string.IsNullOrEmpty(insertDataListData) ? "" : Convert.ToString(insertDataListData);
//                Column_Name = string.IsNullOrEmpty(column_NameData) ? "" : Convert.ToString(column_NameData);
//                #endregion

//                DataTable dtcount = new DataTable();
//                dtcount.Clear();
//                int displayLength = 11;//int.Parse(context.Request.Params["Length"]);
//                int displayStart = 0;//int.Parse(context.Request.Params["Start"]);
//                int sortCol = 0;// int.Parse(context.Request.Params["order[0][column]"]);
//                string sortDir = "asc";// context.Request.Params["order[0][dir]"];
//                //string search = context.Request.Params["search[value]"];
//                string search = searchvalue;
//                dtcount = BOMManagerContract.GetBOMManagerDetailFromTemp(displayLength, displayStart, sortCol, sortDir, search, SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, 0);
//                string TotalCount = dtcount.Rows[0]["TotalCount"].ToString();
//                string OriginalFilename = dtcount.Rows[0]["OriginalFilename"].ToString();

//                //string BomName = string.IsNullOrEmpty(context.Request.QueryString["BomName"]) ? "" : Convert.ToString((context.Request.QueryString["BomName"]));
//                //string CompanyName = string.IsNullOrEmpty(context.Request.QueryString["CompanyName"]) ? "" : Convert.ToString((context.Request.QueryString["CompanyName"]));
//                //string ContactName = string.IsNullOrEmpty(context.Request.QueryString["ContactName"]) ? "" : Convert.ToString((context.Request.QueryString["ContactName"]));
//                //int SalesmanId = string.IsNullOrEmpty(context.Request.QueryString["SalesmanId"]) ? 0 : int.Parse(context.Request.QueryString["SalesmanId"]);
//                //int CompanyId = string.IsNullOrEmpty(context.Request.QueryString["CompanyId"]) ? 0 : int.Parse(context.Request.QueryString["CompanyId"]);
//                //int ContactId = string.IsNullOrEmpty(context.Request.QueryString["ContactId"]) ? 0 : int.Parse(context.Request.QueryString["ContactId"]);
//                ////bool PartWatch = string.IsNullOrEmpty(context.Request.QueryString["PartWatch"]) ? false : bool.Parse(context.Request.QueryString["PartWatch"]);
//                //bool PartWatch = string.IsNullOrEmpty(context.Request.QueryString["ApplyPartWatch"]) ? false : bool.Parse(context.Request.QueryString["ApplyPartWatch"]);

//                string BomName = string.IsNullOrEmpty(bOMName) ? "" : Convert.ToString(bOMName);
//                string CompanyName = string.IsNullOrEmpty(companyName) ? "" : Convert.ToString(companyName);
//                string ContactName = string.IsNullOrEmpty(contactName) ? "" : Convert.ToString(contactName);
//                int SalesmanId = string.IsNullOrEmpty(salespersonId) ? 0 : int.Parse(salespersonId);
//                int CompanyId = string.IsNullOrEmpty(companyId) ? 0 : int.Parse(companyId);
//                int ContactId = string.IsNullOrEmpty(contactId) ? 0 : int.Parse(contactId);
//                //bool PartWatch = string.IsNullOrEmpty(context.Request.QueryString["PartWatch"]) ? false : bool.Parse(context.Request.QueryString["PartWatch"]);
//                bool PartWatch = string.IsNullOrEmpty(applyPartwatch) ? false : bool.Parse(applyPartwatch);

//                #region Stock Import Save data in SQL Table
//                if (ActionPerform == "ImportData")
//                {
//                    string fileColName = "BOMInfoName";
//                    string FixedCurrency = string.Empty;
//                    //bool OverRideCurrency = string.IsNullOrEmpty(context.Request.QueryString["chkOverRide"]) ? false : bool.Parse(context.Request.QueryString["chkOverRide"]);
//                    //string DefaultCurrencyName = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyName"]) ? "" : Convert.ToString((context.Request.QueryString["DefaultCurrencyName"]));
//                    //int DefaultCurrencyId = string.IsNullOrEmpty(context.Request.QueryString["DefaultCurrencyId"]) ? 0 : int.Parse(context.Request.QueryString["DefaultCurrencyId"]);

//                    bool OverRideCurrency = string.IsNullOrEmpty(chkDefaultCurrent) ? false : bool.Parse(chkDefaultCurrent);
//                    string DefaultCurrencyName = string.IsNullOrEmpty(digitalcurrencyName) ? "" : Convert.ToString(digitalcurrencyName);
//                    int DefaultCurrencyId = string.IsNullOrEmpty(digitalcurrencyId) ? 0 : int.Parse(digitalcurrencyId);

//                    if (OverRideCurrency)
//                        ddlCurrency = DefaultCurrencyName;
//                    else
//                        ddlCurrency = "";

//                    string insertcolumndata = insertDataList.TrimEnd(',');
//                    string errorMessage = "";
//                    string NewBomCode = "";
//                    int NewBomid = 0;
//                    string recordCount = BOMManagerContract.SaveBOMManagerImportData(SessionManager.LoginID ?? 0, SessionManager.ClientID ?? 0, SelectedclientId, Column_Lable.TrimEnd(','), Column_Name.TrimEnd(','), insertcolumndata, fileColName, ddlCurrency, out errorMessage, BomName, CompanyName, ContactName, SalesmanId, CompanyId, ContactId, PartWatch, DefaultCurrencyId, OverRideCurrency, SaveImportOrHubRFQ, out NewBomCode, out NewBomid, ReqforTraceabilityId, TypeId, DateRequired);
//                    new Errorlog().LogMessage("records imported " + TotalCount.ToString());
//                    new Errorlog().LogMessage("BOM Import method finish " + DateTime.UtcNow.ToString());
//                    //context.Response.Write(TotalCount+","+OriginalFilename);
//                    ////context.Response.Write(recordCount + "," + OriginalFilename + "," + errorMessage + "," + NewBomid + "," + NewBomCode);
//                    if(TotalCount.ToString() == "0")
//                        return Json("BOM Import method finish " + DateTime.UtcNow.ToString(), JsonRequestBehavior.AllowGet);
//                    return Json("records imported " + TotalCount.ToString(), JsonRequestBehavior.AllowGet);
//                }
//                #endregion
//            }
//            catch (Exception ex)
//            {
//                var ai = new TelemetryClient();
//                ai.TrackTrace("Exception Header: ImportBOMdata");
//                ai.TrackException(ex);
//                new Errorlog().LogMessage("ImportBOMdata method : " + ex.InnerException.Message);
//                TestData1 err = new TestData1();
//                err.IsError = true;
//                err.ErrorMessage = ex.InnerException.Message;
//                //err.ErrorMessage += "<br />" + ex.StackTrace.Replace(System.Environment.NewLine, "<br />");
//                //context.Response.Write(new JavaScriptSerializer().Serialize(err));
//                //WriteError(ex);
//                return Json(err, JsonRequestBehavior.AllowGet);
//            }
//            return null;
//        }

//        //[HttpPost]
//        //public ActionResult ImportBOMangerdata(FormCollection collection)
//        //{
//        //    //int selectedclientId = int.Parse(collection["clientId"]);
//        //    //string digitalCurrency = collection["digitalCurrency"];
//        //    //string defaultcurrency = collection["Defaultcurrency"];
//        //    //string selectedClientType = collection["SelectedClientType"];
//        //    //string chkcolumnheader = collection["chkcolumnheader"];

//        //    //salesBomserviceRequest.ImportBOMManagerdata(strFile, file.FileName, 0, selectedclientId.ToString(), chkcolumnheader, digitalCurrency);
//        //    salesBomserviceRequest.ImportBOMManagerdata(collection);

//        //    return null;
//        //}

//        private string ConvertDataTableToJSON(DataTable dt)
//        {
//            System.Web.Script.Serialization.JavaScriptSerializer serializer = new System.Web.Script.Serialization.JavaScriptSerializer();
//            serializer.MaxJsonLength = Int32.MaxValue;
//            List<Dictionary<string, object>> rows = new List<Dictionary<string, object>>();
//            Dictionary<string, object> row;
//            foreach (DataRow dr in dt.Rows)
//            {
//                row = new Dictionary<string, object>();
//                foreach (DataColumn col in dt.Columns)
//                {
//                    row.Add(col.ColumnName, dr[col]);
//                }
//                rows.Add(row);
//            }
//            return serializer.Serialize(rows);
//        }

//        private static List<T> ConvertDataTable<T>(DataTable dt)
//        {
//            List<T> data = new List<T>();
//            foreach (DataRow row in dt.Rows)
//            {
//                T item = GetItem<T>(row);
//                data.Add(item);
//            }
//            return data;
//        }
//        private static T GetItem<T>(DataRow dr)
//        {
//            Type temp = typeof(T);
//            T obj = Activator.CreateInstance<T>();

//            foreach (DataColumn column in dr.Table.Columns)
//            {
//                foreach (PropertyInfo pro in temp.GetProperties())
//                {
//                    if (pro.Name == column.ColumnName)
//                        pro.SetValue(obj, dr[column.ColumnName], null);
//                    else
//                        continue;
//                }
//            }
//            return obj;
//        }

//        private byte[] ReadData(Stream stream)
//        {
//            byte[] buffer = new byte[16 * 1024];
//            using (MemoryStream ms = new MemoryStream())
//            {
//                int read;
//                while ((read = stream.Read(buffer, 0, buffer.Length)) > 0)
//                {
//                    ms.Write(buffer, 0, read);
//                }
//                return ms.ToArray();
//            }
//        }
//    }
//}