﻿
/****** Object:  StoredProcedure [dbo].[usp_GetOGELNumberByClientId]    Script Date: 7/16/2024 11:29:39 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

ALTER PROCEDURE [dbo].[usp_GetOGELNumberByClientId]
@ClientId		INT=0
AS
/*
 *[001]		Created		<PERSON><PERSON><PERSON><PERSON>		31-03-2023		Add new dropdown for OGEL number.		
 */
BEGIN
CREATE TABLE #tempOGEL
(
ID		INT,
OGELNumber		NVARCHAR(MAX)
)
INSERT INTO #tempOGEL VALUES(0,'Not Applicable')
INSERT INTO #tempOGEL
Select
1,
ISNULL(OGELNumber,'') AS OGELNumber 
 FROM tbClient WHERE ClientId=@ClientId

SELECT
 * FROM #tempOGEL ORDER BY ID ASC

DROP TABLE #tempOGEL
END
GO


