using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Forms
{
    public partial class GILineNotify_Notify : Base
    {

        #region Locals
        protected IconButton _ibtnSend;
        protected IconButton _ibtnSend_Footer;
        protected FlexiDataTable _tblPackagingBreakdown;
        protected FlexiDataTable _tblDateCode;
        //protected IconButton _ibtnGeneratePDF;
        #endregion

        #region Properties

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e)
        {
            base.OnInit(e);
            TitleText = Functions.GetGlobalResource("FormTitles", "GILine_Notify");
            AddScriptReference("Controls.Nuggets.GILineNotify.Notify.GILineNotify_Notify.js");
        }

        /// <summary>
        /// OnPreRender
        /// </summary>
        /// <param name="e"></param>
        protected override void OnPreRender(EventArgs e)
        {
            WireUpButtons();
            SetupScriptDescriptors();
            PackagingBreakdown();
            DateCode();
            base.OnPreRender(e);
        }

        #endregion

        private void WireUpButtons()
        {
            _ibtnSend = FindIconButton("ibtnSend");
            _ibtnSend_Footer = (IconButton)FindFooterIconButton("ibtnSend");
            _tblPackagingBreakdown = (FlexiDataTable)ctlDesignBase.FindContentControl("tblPackagingBreakdown");
            _tblDateCode = (FlexiDataTable)ctlDesignBase.FindContentControl("tblDateCode");
            //_ibtnGeneratePDF = FindIconButton("ibtnGeneratePDF");
        }
        private void PackagingBreakdown()
        {
            _tblPackagingBreakdown.Columns.Add(new FlexiDataColumn("PackagingBreakdownType", WidthManager.GetWidth(WidthManager.ColumnWidth.BOMItemsQuantity)));
            _tblPackagingBreakdown.Columns.Add(new FlexiDataColumn("NumberOfPacks", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tblPackagingBreakdown.Columns.Add(new FlexiDataColumn("BreakdownPackSize", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tblPackagingBreakdown.Columns.Add(new FlexiDataColumn("TotalNoPackSize"));
        }
        private void DateCode()
        {
            _tblDateCode.Columns.Add(new FlexiDataColumn("DateCodes", WidthManager.GetWidth(WidthManager.ColumnWidth.CurrencyValue)));
            _tblDateCode.Columns.Add(new FlexiDataColumn("Quantity"));
        }
        /// <summary>
        /// Setup Script Descriptors
        /// </summary>
        private void SetupScriptDescriptors()
        {
            _scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.GILineNotify_Notify", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddElementProperty("ibtnSend", _ibtnSend.ClientID);
            if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", _ibtnSend_Footer.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblPackagingBreakdown", _tblPackagingBreakdown.ClientID);
            _scScriptControlDescriptor.AddComponentProperty("tblDateCode", _tblDateCode.ClientID);
            _scScriptControlDescriptor.AddProperty("intGILineId", _objQSManager.GILineId);
            _scScriptControlDescriptor.AddProperty("IsPOHub", SessionManager.IsPOHub.Value);
            //Pages.Content.CheckPagePermission(SecurityFunction.List.Warehouse_GoodsIn_Lines_Edit_PurchasePrice);
            //_scScriptControlDescriptor.AddElementProperty("ibtnGeneratePDF", _ibtnGeneratePDF.ClientID);
        }

    }
}
