///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
///<reference path="~/Controls/FlexiDataTable/FlexiDataTableSorting.js" />
//-----------------------------------------------------------------------------------------
// RP 16.02.2010:
// - slight tweak to ensure left position of resizing handle is correct
// - resize columns after first Async row is added
// 
// RP 25.01.2010:
// - updates for correct rendering on different browsers
// - fix recursive code in table and column resizing
// 
// RP 19.01.2010:
// - remove MoreInfo stuff
// 
// RP 15.01.2010:
// - move client-side sorting script to another file
// 
// RP 14.01.2010:
// - fix table resizing
// 
// RP 06.01.2010:
// - dispose everything fully
// 
// RP 06.11.2009:
// - begin adding ability to sort data in table without returning to the database
//
// RP 30.10.2009:
// - add ability to asyncronously load data into table
//-----------------------------------------------------------------------------------------
/*
Marker     changed by      date         Remarks
[001]      Shashi Keshar  03/01/2017   Added new Function to make row color
*/
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.FlexiDataTable = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FlexiDataTable.initializeBase(this, [element]);
	this.ControlType_FlexiDataTable = true;
	this._intCurrentPageSize = 10;
	this._intCurrentPage = 1;
	this._intSortColumnIndex = 0;
	this._intOriginalSortColumnIndex = 0;
	this._strFilterExpression = "";
	this._strSortExpression = "";
	this._intStartRow = 0;
	this._intEndRow = 0;
	this._intTotalRecords = 0;
	this._intSelectedIndex = -1;
	this._varSelectedValue = "";
	this._blnShowHeader = true;
	this._intDoubleClickTimeout = 0;
	this._aryHeaderCellIDs = [];
	this._aryColumnAlignment = [];
	this._aryColumnWidth = [];
	this._aryColumnClientSortFormat = [];
	this._aryCurrentValues = [];
	this._arySelectedIndexes = [];
	this._aryValues = [];
	this._aryExtraData = [];
	this._blnEnabled = true;
	this._intLastRowClicked = null;
	this._intCountSelected = 0;
	this._aryAsyncDataToAdd = [];
	this._intAsyncRowsToAdd = 0;
	this._intAsyncRowsAdded = 0;
	this._blnColumnsResizing = false;
	this._enmOriginalSortDirection = null;
	this._intHandleLeft = -1;
};

Rebound.GlobalTrader.Site.Controls.FlexiDataTable.prototype = {

	get_blnSelected: function() { return this._blnSelected; }, 	set_blnSelected: function(value) { if (this._blnSelected !== value)  this._blnSelected = value; }, 
	get_blnIsScrollable: function() { return this._blnIsScrollable; }, 	set_blnIsScrollable: function(value) { if (this._blnIsScrollable !== value)  this._blnIsScrollable = value; }, 
	get_intCurrentPageSize: function() { return this._intCurrentPageSize; }, 	set_intCurrentPageSize: function(value) { if (this._intCurrentPageSize !== value)  this._intCurrentPageSize = value; }, 
	get_intCurrentPage: function() { return this._intCurrentPage; }, 	set_intCurrentPage: function(value) { if (this._intCurrentPage !== value)  this._intCurrentPage = value; }, 
	get_blnFiltersOn: function() { return this._blnFiltersOn; }, 	set_blnFiltersOn: function(value) { if (this._blnFiltersOn !== value)  this._blnFiltersOn = value; }, 
	get_intSortColumnIndex: function() { return this._intSortColumnIndex; }, 	set_intSortColumnIndex: function(value) { if (this._intSortColumnIndex !== value)  this._intSortColumnIndex = value; }, 
	get_enmSortDirection: function() { return this._enmSortDirection; }, 	set_enmSortDirection: function(value) { if (this._enmSortDirection !== value)  this._enmSortDirection = value; }, 
	get_strFilterExpression: function() { return this._strFilterExpression; }, 	set_strFilterExpression: function(value) { if (this._strFilterExpression !== value)  this._strFilterExpression = value; }, 
	get_strSortExpression: function() { return this._strSortExpression; }, 	set_strSortExpression: function(value) { if (this._strSortExpression !== value)  this._strSortExpression = value; }, 
	get_intStartRow: function() { return this._intStartRow; }, 	set_intStartRow: function(value) { if (this._intStartRow !== value)  this._intStartRow = value; }, 
	get_intEndRow: function() { return this._intEndRow; }, 	set_intEndRow: function(value) { if (this._intEndRow !== value)  this._intEndRow = value; }, 
	get_intTotalRecords: function() { return this._intTotalRecords; }, 	set_intTotalRecords: function(value) { if (this._intTotalRecords !== value)  this._intTotalRecords = value; }, 
	get_aryHeaderCellIDs: function() { return this._aryHeaderCellIDs; }, 	set_aryHeaderCellIDs: function(value) { if (this._aryHeaderCellIDs !== value)  this._aryHeaderCellIDs = value; }, 
	get_aryColumnIsSortable: function() { return this._aryColumnIsSortable; }, 	set_aryColumnIsSortable: function(value) { if (this._aryColumnIsSortable !== value)  this._aryColumnIsSortable = value; }, 
	get_tbl: function() { return this._tbl; }, 	set_tbl: function(value) { if (this._tbl !== value)  this._tbl = value; }, 
	get_tblHeader: function() { return this._tblHeader; }, 	set_tblHeader: function(value) { if (this._tblHeader !== value)  this._tblHeader = value; }, 
	get_blnAllowSelection: function() { return this._blnAllowSelection; }, 	set_blnAllowSelection: function(value) { if (this._blnAllowSelection !== value)  this._blnAllowSelection = value; }, 
	get_blnAllowMultipleSelection: function() { return this._blnAllowMultipleSelection; }, 	set_blnAllowMultipleSelection: function(value) { if (this._blnAllowMultipleSelection !== value)  this._blnAllowMultipleSelection = value; }, 
	get_aryCurrentValues: function() { return this._aryCurrentValues; }, 	set_aryCurrentValues: function(value) { if (this._aryCurrentValues !== value)  this._aryCurrentValues = value; }, 
	get_aryValues: function() { return this._aryValues; }, 	set_aryValues: function(value) { if (this._aryValues !== value)  this._aryValues = value; }, 
	get_intSelectedIndex: function() { return this._intSelectedIndex; }, 	set_intSelectedIndex: function(value) { if (this._intSelectedIndex !== value)  this._intSelectedIndex = value; }, 
	get_varSelectedValue: function() { return this._varSelectedValue; }, 	set_varSelectedValue: function(value) { if (this._varSelectedValue !== value)  this._varSelectedValue = value; }, 
	get_blnInsideDataListNugget: function() { return this._blnInsideDataListNugget; }, 	set_blnInsideDataListNugget: function(value) { if (this._blnInsideDataListNugget !== value)  this._blnInsideDataListNugget = value; }, 
	get_blnShowHeader: function() { return this._blnShowHeader; }, 	set_blnShowHeader: function(value) { if (this._blnShowHeader !== value)  this._blnShowHeader = value; }, 
	get_aryExtraData: function() { return this._aryExtraData; }, 	set_aryExtraData: function(value) { if (this._aryExtraData !== value)  this._aryExtraData = value; }, 
	get_aryColumnAlignment: function() { return this._aryColumnAlignment; }, 	set_aryColumnAlignment: function(value) { if (this._aryColumnAlignment !== value)  this._aryColumnAlignment = value; }, 
	get_aryColumnWidth: function() { return this._aryColumnWidth; }, 	set_aryColumnWidth: function(value) { if (this._aryColumnWidth !== value)  this._aryColumnWidth = value; }, 
	get_blnAllowDoubleClick: function() { return this._blnAllowDoubleClick; }, 	set_blnAllowDoubleClick: function(value) { if (this._blnAllowDoubleClick !== value)  this._blnAllowDoubleClick = value; }, 
	get_pnlScroll: function() { return this._pnlScroll; }, 	set_pnlScroll: function(value) { if (this._pnlScroll !== value)  this._pnlScroll = value; }, 
	get_pnlHandleFooter: function() { return this._pnlHandleFooter; }, 	set_pnlHandleFooter: function(value) { if (this._pnlHandleFooter !== value)  this._pnlHandleFooter = value; }, 
	get_blnAllowResize: function() { return this._blnAllowResize; }, 	set_blnAllowResize: function(value) { if (this._blnAllowResize !== value)  this._blnAllowResize = value; }, 
	get_rceResizing: function() { return this._rceResizing; }, 	set_rceResizing: function(value) { if (this._rceResizing !== value)  this._rceResizing = value; }, 
	get_blnAllowClientSideSorting: function() { return this._blnAllowClientSideSorting; }, 	set_blnAllowClientSideSorting: function(value) { if (this._blnAllowClientSideSorting !== value)  this._blnAllowClientSideSorting = value; }, 
	get_aryColumnClientSortFormat: function() { return this._aryColumnClientSortFormat; }, 	set_aryColumnClientSortFormat: function(value) { if (this._aryColumnClientSortFormat !== value)  this._aryColumnClientSortFormat = value; }, 

	addSortDataEvent: function(handler) { this.get_events().addHandler("SortData", handler); },
	removeSortDataEvent: function(handler) { this.get_events().removeHandler("SortData", handler); },
	onSortData: function() {
		var handler = this.get_events().getHandler("SortData");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	addFilterDataEvent: function(handler) { this.get_events().addHandler("FilterData", handler); },
	removeFilterDataEvent: function(handler) { this.get_events().removeHandler("FilterData", handler); },
	onFilterData: function() {
		var handler = this.get_events().getHandler("FilterData");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},
	
	addPageChangedEvent: function(handler) { this.get_events().addHandler("PageChange", handler); },
	removePageChangedEvent: function(handler) { this.get_events().removeHandler("PageChange", handler); },
	onPageChanged: function() {
		var handler = this.get_events().getHandler("PageChange");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addSelectedIndexChanged: function(handler) { this.get_events().addHandler("SelectedIndexChanged", handler); },
	removeSelectedIndexChanged: function(handler) { this.get_events().removeHandler("SelectedIndexChanged", handler); },
	onSelectedIndexChanged: function() {
		var handler = this.get_events().getHandler("SelectedIndexChanged");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addMultipleSelectionChanged: function(handler) { this.get_events().addHandler("MultipleSelectionChanged", handler); },
	removeMultipleSelectionChanged: function(handler) { this.get_events().removeHandler("MultipleSelectionChanged", handler); },
	onMultipleSelectionChanged: function() {
		var handler = this.get_events().getHandler("MultipleSelectionChanged");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addDoubleClick: function(handler) { this.get_events().addHandler("DoubleClick", handler); },
	removeDoubleClick: function(handler) { this.get_events().removeHandler("DoubleClick", handler); },
	onDoubleClick: function() {
		var handler = this.get_events().getHandler("DoubleClick");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addResize: function(handler) { this.get_events().addHandler("Resize", handler); },
	removeResize: function(handler) { this.get_events().removeHandler("Resize", handler); },
	onResize: function() {
		var handler = this.get_events().getHandler("Resize");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addAddAsyncRow: function(handler) { this.get_events().addHandler("AddAsyncRow", handler); },
	removeAddAsyncRow: function(handler) { this.get_events().removeHandler("AddAsyncRow", handler); },
	onAddAsyncRow: function() {
		var handler = this.get_events().getHandler("AddAsyncRow");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addAsyncDataAdditionComplete: function(handler) { this.get_events().addHandler("AsyncDataAdditionComplete", handler); },
	removeAsyncDataAdditionComplete: function(handler) { this.get_events().removeHandler("AsyncDataAdditionComplete", handler); },
	onAsyncDataAdditionComplete: function() {
		var handler = this.get_events().getHandler("AsyncDataAdditionComplete");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	addClientSortDataEvent: function(handler) { this.get_events().addHandler("ClientSortData", handler); },
	removeClientSortDataEvent: function(handler) { this.get_events().removeHandler("ClientSortData", handler); },
	onClientSortData: function() {
		var handler = this.get_events().getHandler("ClientSortData");
		if (handler) handler(this, Sys.EventArgs.Empty);
	},

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.FlexiDataTable.callBaseMethod(this, "initialize");
		this.setupColumns();
		if (this._blnShowHeader) $addHandler(window, "resize", Function.createDelegate(this, this.resizeColumns));
		if (this._blnAllowResize) {
			this._pnlScroll.style.width = "100%";
			this.setupResizing();
		}
		this._enmOriginalSortDirection = this._enmSortDirection;		
		this._intOriginalSortColumnIndex = this._intSortColumnIndex;
		this.calculateStartAndEndRow();
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this.get_element()) $clearHandlers(this.get_element());
		if (this._tbl) {
			this.unhookTableRowEvents();
			$clearHandlers(this._tbl);
		}
		if (this._blnShowHeader) $clearHandlers(window);
		this._enmSortDirection = null;
		this._enmOriginalSortDirection = null;
		this._tbl = null;
		this._tblHeader = null;
		this._pnlScroll = null;
		this._pnlHandleFooter = null;
		this._rceResizing = null;
		this._blnSelected = null;
		this._blnIsScrollable = null;
		this._intCurrentPageSize = null;
		this._intCurrentPage = null;
		this._blnFiltersOn = null;
		this._intSortColumnIndex = null;
		this._intOriginalSortColumnIndex = null;
		this._strFilterExpression = null;
		this._strSortExpression = null;
		this._intStartRow = null;
		this._intEndRow = null;
		this._intTotalRecords = null;
		this._blnAllowSelection = null;
		this._blnAllowMultipleSelection = null;
		this._intSelectedIndex = null;
		this._varSelectedValue = null;
		this._blnInsideDataListNugget = null;
		this._blnShowHeader = null;
		this._blnAllowDoubleClick = null;
		this._blnAllowResize = null;
		this._blnAllowClientSideSorting = null;
		this._aryHeaderCellIDs = null;
		this._aryColumnIsSortable = null;
		this._aryAsyncDataToAdd = null;
		this._aryColumnClientSortFormat = null;
		this._aryCurrentValues = null;
		this._aryValues = null;
		this._aryExtraData = null;
		this._aryColumnAlignment = null;
		this._aryColumnWidth = null;
		this._blnColumnsResizing = null;
		Rebound.GlobalTrader.Site.Controls.FlexiDataTable.callBaseMethod(this, "dispose");
		this.isDisposed = true;
	},
	
	setupColumns: function(){
		for (var i = 0, l = this._aryHeaderCellIDs.length; i < l; i++) {
			if (this._aryColumnIsSortable[i]) {
				var th = $get(this._aryHeaderCellIDs[i]);
				Sys.UI.DomElement.addCssClass(th, "sortable");
				th.setAttribute("onmouseover", String.format("$find('{0}').showColumnMouseOver({1});", this._element.id, i));
				th.setAttribute("onmouseout", String.format("$find('{0}').showColumnMouseOut({1});", this._element.id, i));
				th.setAttribute("onclick", String.format("$find('{0}').doSort({1});", this._element.id, i));
				this.formatColumn(i);
				th = null;
			}
		}
	},
	
	formatColumns: function(){
		for (var i = 0, l = this._aryHeaderCellIDs.length; i < l; i++) {
			if (this._aryColumnIsSortable[i]) this.formatColumn(i);
		}
	},
	
	formatColumn: function(i) {
		this.clearColumnSortDirection(i);
		if (i == this._intSortColumnIndex) this.setColumnSortDirection(i);
	},
	
	setInitialSortDirection: function(enmSortDirection) {
		this._enmSortDirection = enmSortDirection;
		this._enmOriginalSortDirection = enmSortDirection;
		this._intOriginalSortColumnIndex = this._intSortColumnIndex;
		this.setColumnSortDirection(this._intSortColumnIndex);
	},
	
	resetInitialSortDirection: function() {
		this._enmSortDirection = this._enmOriginalSortDirection;
		this._intSortColumnIndex = this._intOriginalSortColumnIndex;
		for (var i = 0, l = this._aryHeaderCellIDs.length; i < l; i++) {
			this.clearColumnSortDirection(i);
		}
		this.setColumnSortDirection(this._intSortColumnIndex);
	},
	
	prevPage: function() {
		this.changePage(this.limitPage(this._intCurrentPage - 1));
	},
	
	nextPage: function() {
		this.changePage(this.limitPage(this._intCurrentPage + 1));
	},
	
	changePage: function(intNewPage) {
		this._intCurrentPage = intNewPage;
		this.calculateStartAndEndRow();
		this.calculatePages();
		this.onPageChanged();
	},
	
	calculateStartAndEndRow: function() {
		this.calculateStartRow();
		this.calculateEndRow();
	},
	
	calculateStartRow: function() {
		this._intStartRow = (this._intCurrentPageSize == 0) ? 0 : Math.max((this._intCurrentPageSize * (this._intCurrentPage - 1)), 0) + 1;
	},
	
	calculateEndRow: function() {
		this._intEndRow = (this._intCurrentPageSize == 0) ? this._intTotalRecords : this._intStartRow + this._intCurrentPageSize - 1;
	},
	
	calculatePages: function() {
		if (this._intCurrentPageSize == 0) {
			this._intTotalPages = 1;
		} else {
			this._intTotalPages = Math.ceil(this._intTotalRecords / this._intCurrentPageSize);
		}
		this._intCurrentPage = this.limitPage(this._intCurrentPage);
	},
	
	limitPage: function(intPage) {
		return Math.max(Math.min(intPage, this._intTotalPages), 1);
	},

	clearTable: function() {
		this.unhookTableRowEvents();
		for (var i = this._tbl.rows.length - 1; i >= 0; i--) { this._tbl.deleteRow(i); }
		Array.clear(this._aryCurrentValues);
		Array.clear(this._aryValues);
		Array.clear(this._aryExtraData);
		Array.clear(this._arySelectedIndexes);
		this._intSelectedIndex = -1;
		this._varSelectedValue = "";
		this.resetColumnWidths();
	},
	
	resetColumnWidths: function() {
		for (var i = 0, l = this._aryHeaderCellIDs.length; i < l; i++) {
			var strStyle = "";
			if (this._aryColumnWidth[i] != "") strStyle = "width:" + this._aryColumnWidth[i];
			$get(this._aryHeaderCellIDs[i]).setAttribute("style", strStyle);
			strStyle = null;
		}
	},
	
	doFilter: function() {
		$R_FN.showElement(this._pnlLoading, true);
		this._intCurrentPage = 1;
		this._intStartRow = 1;
		this.calculateEndRow();
		this.onFilterData();
		this.calculatePages();
	},
	
	showColumnMouseOver: function(i) {
		var th = $get(this._aryHeaderCellIDs[i]);
		Sys.UI.DomElement.addCssClass(th, "highlight");
		th = null;
	},
	
	showColumnMouseOut: function(i) {
		var th = $get(this._aryHeaderCellIDs[i]);
		Sys.UI.DomElement.removeCssClass(th, "highlight");
		th = null;
	},

	doSort: function(i) {
		if (i == this._intSortColumnIndex) {
			this.swapSortDirection();
		} else {
			this._enmSortDirection = $R_ENUM$SortColumnDirection.ASC;
		}
		this._intSortColumnIndex = i;
		this.formatColumns();
		if (this._blnAllowClientSideSorting) {
			$R_TABLESORT.doSort(this._element.id, this._intSortColumnIndex);
			this.onClientSortData();
		} else {
			this._intCurrentPage = 1;
			this.calculatePages();
			$R_FN.showElement(this._pnlLoading, true);
			this.onSortData();
		}
	},
	
	getID: function(strControl, intRowIndex) {
		return String.format("{0}_{1}{2}", this._tbl.id, strControl, intRowIndex);
	},
	
	finishResizeColumns: function() {
		if (this._blnColumnsResizing) return;
		var el_bnds = Sys.UI.DomElement.getBounds(this._element);
		if (el_bnds.width == 0) return;
		if (!this._blnShowHeader) return;
		if (this._blnAllowResize) {
			this.calculateResizeHandleLeft();
			this.repositionResizingHandle();
		}
		if (this._tbl.rows.length == 0) { this._tblHeader.style.width = "100%"; return; }
		this._blnColumnsResizing = true;
		$R_FN.setWidthFromOneToAnother(this._tbl, this._tblHeader);
		for (var i = 0, l = this._aryHeaderCellIDs.length; i < l; i++) {
			var td = (this._tbl.rows[0]) ? this._tbl.rows[0].cells[i] : null;
			var th = (this._tblHeader.rows[0]) ? this._tblHeader.rows[0].cells[i] : null;
			var intOffset = (this._blnInsideDataListNugget) ? -7 : -8;
			if (i == 0 && (Sys.Browser.agent == Sys.Browser.Safari || Sys.Browser.agent == Sys.Browser.Opera)) intOffset += 1;
			if (td && th && (i < (l - 1))) $R_FN.setWidthFromOneToAnother(td, th, intOffset);
			td = null; th = null;
		}
		el_bnds = null;
		this._blnColumnsResizing = false;
	},
	
	resizeColumns: function() {
		setTimeout(Function.createDelegate(this, this.finishResizeColumns), 5);
	},
	
	clearSetColumnWidths: function() {
		for (var i = 0, l = this._aryHeaderCellIDs.length; i < l; i++) {
			var th = (this._tblHeader.rows[0]) ? this._tblHeader.rows[0].cells[i] : null;
			if (th) th.style.width = "";
			th = null;
		}
	},
	
	addRow: function(aryData, varValue, blnSelected, objExtraData, strSpecialClass, strElementID) {
		if (!aryData) return;
		if (!strSpecialClass) strSpecialClass = "";
		var tr = this._tbl.insertRow(-1);
		var intRowIndex = tr.rowIndex;
		tr.setAttribute("bui_tableRowIndex", intRowIndex);
		this._aryValues[intRowIndex] = varValue;
		for (var i = 0, l = aryData.length; i < l; i++) {
			if (i >= this._aryHeaderCellIDs.length) break;
			var td = document.createElement("td");
			if (i == 0) td.className = "first";
			if (i == (l - 1)) td.className = "last";
			if (strSpecialClass != "") {
				td.className += " " + strSpecialClass;
				if (i == 0) td.className += String.format(" {0}_First", strSpecialClass);
				if (i == (l - 1)) td.className += String.format(" {0}_Last", strSpecialClass);
			}
			switch (this._aryColumnAlignment[i]) {
				case $R_ENUM$HorizontalAlign.Center: td.className += " alignCenter"; break;
				case $R_ENUM$HorizontalAlign.Right: td.className += " alignRight"; break;
				case $R_ENUM$HorizontalAlign.Justify: td.className += " alignJustify"; break;
			}
			aryData[i] += ""; //convert to string
			var strData = aryData[i].toString().trim();
			if (strData.length == 0 || strData == "undefined") strData = "&nbsp;";
			td.innerHTML = strData;
			td.setAttribute("bui_tableRowIndex", intRowIndex);
			tr.appendChild(td);
			if (intRowIndex == 0) td.style.width = this._tblHeader.rows[0].cells[i].style.width;
			td = null; strData = null;
		}
		var strID = this.get_element().id;
		if (this._blnAllowSelection) {
			$addHandler(tr, "mouseup", Function.createDelegate(this, this.rowMouseUp));
			$addHandler(tr, "mousedown", Function.createDelegate(this, this.rowMouseDown));
		}
		Array.add(this._aryExtraData, objExtraData);
		tr = null;
		if (blnSelected) this.selectRow(intRowIndex, true);
		if (this._blnAllowResize) this.repositionResizingHandle();
		
	},
    // [001]  Start Here
	addRowRowColor: function (aryData, varValue, blnSelected, objExtraData, strSpecialClass, strElementID, blnRowColor, RowColor) {
	    if (!aryData) return;
	    if (!strSpecialClass) strSpecialClass = "";
	    var tr = this._tbl.insertRow(-1);
	    var intRowIndex = tr.rowIndex;
	    tr.setAttribute("bui_tableRowIndex", intRowIndex);
	    this._aryValues[intRowIndex] = varValue;
	    for (var i = 0, l = aryData.length; i < l; i++) {
	        if (i >= this._aryHeaderCellIDs.length) break;
	        var td = document.createElement("td");
	        if (i == 0) td.className = "first";
	        if (i == (l - 1)) td.className = "last";
	        if (strSpecialClass != "") {
	            td.className += " " + strSpecialClass;
	            if (i == 0) td.className += String.format(" {0}_First", strSpecialClass);
	            if (i == (l - 1)) td.className += String.format(" {0}_Last", strSpecialClass);
	        }
	        switch (this._aryColumnAlignment[i]) {
	            case $R_ENUM$HorizontalAlign.Center: td.className += " alignCenter"; break;
	            case $R_ENUM$HorizontalAlign.Right: td.className += " alignRight"; break;
	            case $R_ENUM$HorizontalAlign.Justify: td.className += " alignJustify"; break;
	        }
	        aryData[i] += ""; //convert to string
	        var strData = aryData[i].toString().trim();
	        if (strData.length == 0 || strData == "undefined") strData = "&nbsp;";
	        td.innerHTML = strData;
	        td.setAttribute("bui_tableRowIndex", intRowIndex);
	        tr.appendChild(td);
	        if (intRowIndex == 0) td.style.width = this._tblHeader.rows[0].cells[i].style.width;
	        td = null; strData = null;
	    }
	    var strID = this.get_element().id;
	    if (this._blnAllowSelection) {
	        $addHandler(tr, "mouseup", Function.createDelegate(this, this.rowMouseUp));
	        $addHandler(tr, "mousedown", Function.createDelegate(this, this.rowMouseDown));
	    }
	    Array.add(this._aryExtraData, objExtraData);
	    tr = null;
	    if (blnSelected) this.selectRow(intRowIndex, true);
	    if (this._blnAllowResize) this.repositionResizingHandle();
	    if (blnRowColor) this.RowColor(intRowIndex, RowColor);
	},
	RowColor: function (intRowIndex, RowColor) {
	    Sys.UI.DomElement.addCssClass(this._tbl.rows[intRowIndex], RowColor);
	},
    // [001]  End Here
	rowMouseDown: function(sender, eventargs) {
		$R_FN.clearAllSelections();
	},
	
	rowMouseUp: function(sender, eventargs) {
		var el = $R_FN.findParentElementOfType(sender.target, "tr");
		var intRowIndex = Number.parseInvariant(el.getAttribute("bui_tableRowIndex").toString());
		//clear table selections that some browsers do
		$R_FN.clearAllSelections();
		this.selectRow(intRowIndex, false, false, sender.ctrlKey, sender.shiftKey);
		el = null;
	},

	selectRow: function(intRowIndex, blnScrollToSelected, blnIgnoreRaiseEvent, blnCtrlClick, blnShiftClick) {
		if (!this._blnEnabled) return;
		
		//check for double-click
		if (this._blnAllowDoubleClick) {
			if (this._blnInDoubleClick && (intRowIndex == this._intLastRowIndexClicked)) {
				this._intLastRowIndexClicked = intRowIndex;
				this.endDoubleClickTest();
				this._varSelectedDoubleClickValue = this._aryValues[intRowIndex];
				this.onDoubleClick(); //raise event
				return;
			}
			this._intLastRowIndexClicked = intRowIndex;
			this.endDoubleClickTest();
			this._intDoubleClickTimeout = setTimeout(String.format("$find('{0}').endDoubleClickTest();", this._element.id), $R_DOUBLE_CLICK_TIME);
			this._blnInDoubleClick = true;
		} 
		if (this._blnAllowMultipleSelection) {
			if (blnCtrlClick && !blnShiftClick) {
				//toggle multiple selection
				if (Array.contains(this._arySelectedIndexes, intRowIndex)) {
					this.doUnselectRow(intRowIndex);
				} else {
					this.doSelectRow(intRowIndex, true);
				}			
			} else if (blnShiftClick && !blnCtrlClick) {
				//select range
				this.clearSelection();
				var intStart = (this._intLastRowClicked > intRowIndex) ? intRowIndex : this._intLastRowClicked;
				var intEnd = (this._intLastRowClicked > intRowIndex) ? this._intLastRowClicked : intRowIndex;
				for (var i = intStart; i <= intEnd; i++) {
					if (!Array.contains(this._arySelectedIndexes, i)) this.doSelectRow(i, true);
				}
			} else {
				//single select (on multiple)
				this.clearSelection();
				this.doSelectRow(intRowIndex, true);
			}
			if (!blnIgnoreRaiseEvent) this.onMultipleSelectionChanged();
		} else {
			//update single selection
			if (intRowIndex == this._intSelectedIndex) return;
			this.clearSelection();
			this.doSelectRow(intRowIndex);
			if (!blnIgnoreRaiseEvent) setTimeout(Function.createDelegate(this, this.onSelectedIndexChanged), 0);
			if (blnScrollToSelected) this.scrollToRowAfterPause(intRowIndex);
		}
		this._intLastRowClicked = intRowIndex;
	},
	
	endDoubleClickTest: function() {
		clearTimeout(this._intDoubleClickTimeout);
		this._blnInDoubleClick = false;
	},
	
	getSelectedExtraData: function(intIndex) {
		if (typeof(intIndex) == "undefined") intIndex = this._intSelectedIndex;
		return (this._aryExtraData[intIndex]);
	},
	
	clearSelection: function(blnRaiseEvent) {
		Array.clear(this._aryCurrentValues);
		Array.clear(this._arySelectedIndexes);
		this._intSelectedIndex = null;
		this._varSelectedValue = null;
		for (var i = 0 , l = this._tbl.rows.length; i < l; i++) {
			Sys.UI.DomElement.removeCssClass(this._tbl.rows[i], 'selected');
		}
		this._intCountSelected = 0;
		if (blnRaiseEvent) {
			if (this._blnAllowMultipleSelection) {
				this.onMultipleSelectionChanged();
			} else {
				this.onSelectedIndexChanged();
			}
		}
	},
	
	selectAllRows: function(blnRaiseEvent) {
		if (!this._blnAllowMultipleSelection) return;
		this.clearSelection();
		for (var i = 0 , l = this._tbl.rows.length; i < l; i++) {
			this.doSelectRow(i, true);
		}
		if (blnRaiseEvent) this.onMultipleSelectionChanged();
	},
	
	doSelectRow: function(intRowIndex, blnMultiple) {
		if (blnMultiple) {
			Array.add(this._aryCurrentValues, this._aryValues[intRowIndex]);
			Array.add(this._arySelectedIndexes, intRowIndex);
		} else {
			this._intSelectedIndex = intRowIndex;
			this._varSelectedValue = this._aryValues[intRowIndex];
		}
		Sys.UI.DomElement.addCssClass(this._tbl.rows[intRowIndex], "selected");
		Sys.UI.DomElement.removeCssClass(this._tbl.rows[intRowIndex], "hover");
		this._intCountSelected += 1;
	},
	
	doUnselectRow: function(intRowIndex) {
		Array.remove(this._aryCurrentValues, this._aryValues[intRowIndex]);
		Array.remove(this._arySelectedIndexes, intRowIndex);
		Sys.UI.DomElement.removeCssClass(this._tbl.rows[intRowIndex], "selected");
		this._intCountSelected -= 1;
	},
	
	show: function(blnShow) {
		$R_FN.showElement(this.get_element(), blnShow);
	},
	
	enable: function(bln) {
		this._blnEnabled = bln;
	},
	
	getSelectedCellValue: function(intCellIndex, intRowIndex) {
		if (typeof(intRowIndex) == "undefined") intRowIndex = this._intSelectedIndex;
		var tr = this._tbl.rows[intRowIndex];
		if (!tr) return;
		if (intCellIndex > (tr.cells.length - 1)) return;
		var strReturn = tr.cells[intCellIndex].innerHTML;
		tr = null;
		return strReturn;
	},
	
	countRows: function() {
		if (!this._tbl) return 0;
		if (!this._tbl.rows) return 0;
		return (this._tbl.rows.length);
	},

	swapSortDirection: function() {
		this._enmSortDirection = (this._enmSortDirection == $R_ENUM$SortColumnDirection.ASC) ? $R_ENUM$SortColumnDirection.DESC : $R_ENUM$SortColumnDirection.ASC;
	},
	
	clearColumnSortDirection: function(i) {
		Sys.UI.DomElement.removeCssClass($get(this._aryHeaderCellIDs[i]), "sortASC");
		Sys.UI.DomElement.removeCssClass($get(this._aryHeaderCellIDs[i]), "sortDESC");
	},
	
	setColumnSortDirection: function(i) {
		this._intSortColumnIndex = Number.parseInvariant(i.toString());
		Sys.UI.DomElement.addCssClass($get(this._aryHeaderCellIDs[i]), String.format("sort{0}", (this._enmSortDirection == $R_ENUM$SortColumnDirection.ASC) ? "ASC" : "DESC"));
	},
	
	removeRow: function(intIndex) {
		if (this._tbl.rows[intIndex]) $clearHandlers(this._tbl.rows[intIndex]);
		this._tbl.deleteRow(intIndex);
		Array.removeAt(this._aryCurrentValues, intIndex);
		Array.removeAt(this._aryValues, intIndex);
		Array.removeAt(this._aryExtraData, intIndex);
		Array.removeAt(this._arySelectedIndexes, intIndex);
	},
	
	scrollToRow: function(intIndex) {
		if (intIndex < 0) intIndex = 0;
		var tr = null;
		if (intIndex < this._tbl.rows.length) tr = this._tbl.rows[intIndex];
		if (!tr) return;
		this._pnlScroll.scrollTop += Sys.UI.DomElement.getBounds(tr).y - Sys.UI.DomElement.getBounds(this._pnlScroll).y;
		tr = null;
		if (this._blnAllowResize) this.repositionResizingHandle();
	},
	
	scrollToRowAfterPause: function(intRowIndex) {
		setTimeout(String.format("$find('{0}').scrollToRow({1});", this._element.id, intRowIndex), 10);
	},
	
	changeSelectability: function(bln) {
		this._blnAllowSelection = bln;
		if (bln) {
			Sys.UI.DomElement.addCssClass(this._tbl, "dataTableSelectable");
		} else {
			Sys.UI.DomElement.removeCssClass(this._tbl, "dataTableSelectable");
		}
	},
	
	changeMultipleSelectability: function(bln) {
		this._blnAllowMultipleSelection = bln;
		if (bln) {
			Sys.UI.DomElement.addCssClass(this._tbl, "dataTableSelectable");
		} else {
			Sys.UI.DomElement.removeCssClass(this._tbl, "dataTableSelectable");
		}
	},
	
	setupResizing: function() {
		if (!this._blnAllowResize) return;
		if (!this._rceResizing._initialized) {
			setTimeout(Function.createDelegate(this, this.setupResizing), 10);
			return;
		}
		$R_FN.showElement(this._rceResizing._handle, true);
		setTimeout(Function.createDelegate(this, this.repositionResizingHandle), 500);
		$addHandler(this._tbl, "click", Function.createDelegate(this, this.repositionResizingHandle));
		this._rceResizing.add_resizing(Function.createDelegate(this, this.tableResizing));
		this._rceResizing.add_resizebegin(Function.createDelegate(this, this.tableResizing));
		this._rceResizing.add_resize(Function.createDelegate(this, this.tableResized));
	},
	
	tableResized: function() {
		this.onResize();
	},
	
	tableResizing: function(sender, eventArgs) {
		if (!this._blnAllowResize || !this._rceResizing) return;
		sender.get_element().style.width = "";
		this.repositionResizingHandle();
		$R_FN.clearAllSelections();
	}, 
	
	repositionResizingHandle: function() {
		if (!this._blnAllowResize || !this._rceResizing || !this._intHandleLeft) return;
		if (this._intHandleLeft <= 0) this.calculateResizeHandleLeft();
		var intY = Number.parseInvariant(this._rceResizing.get_Size().height.toString());
		if (Sys.Browser.agent == Sys.Browser.Firefox) intY += Math.round(this._pnlScroll.scrollTop);
		Sys.UI.DomElement.setLocation(this._rceResizing._handle, this._intHandleLeft, intY);
		intY = null;
	},
	
	calculateResizeHandleLeft: function() {
		if (!this._blnAllowResize || !this._rceResizing) return;
		this._intHandleLeft = (Math.round((Sys.UI.DomElement.getBounds(this._pnlHandleFooter).width - Sys.UI.DomElement.getBounds(this._rceResizing._handle).width) / 2) - 15);
	},
	
	unhookTableRowEvents: function() {
		if (!this._blnAllowSelection) return;
		for (var i = 0, l = this._tbl.rows; i < l; i++) {
			if (this._tbl.rows[i]) $clearHandlers(this._tbl.rows[i]);
		}
	},
	
	resetAsyncData: function() {
		this._aryAsyncDataToAdd = [];
		this._intAsyncRowsToAdd = 0;
		this._intAsyncRowsAdded = 0;
	},
	
	addDataForAsyncAddition: function(aryDataToAdd) {
		if (aryDataToAdd) Array.addRange(this._aryAsyncDataToAdd, aryDataToAdd);
		this._intAsyncRowsToAdd = this._aryAsyncDataToAdd.length;
	},
	
	startAddingRowsAsync: function() {
		this._intAsyncRowsAdded = 0;
		if (!this._aryAsyncDataToAdd) this._aryAsyncDataToAdd = [];
		this._intAsyncRowsToAdd = this._aryAsyncDataToAdd.length;
		if (this._aryAsyncDataToAdd.length > 0) {
			this.addRowAsync();
		} else {
			this.finishedAddingRowsAsync();
		}
	},
	
	addRowAsync: function() {
		if (this._intAsyncRowsAdded < this._intAsyncRowsToAdd) {
			if (this._intAsyncRowsAdded == 0) this.resizeColumns();
			this._objAsyncDataRow = this._aryAsyncDataToAdd[this._intAsyncRowsAdded];
			this.onAddAsyncRow();
			this._intAsyncRowsAdded += 1;
			var strID = this._element.id;
			var fn = function() { $find(strID).addRowAsync(); };
			setTimeout(fn, 0);
		} else {
			this.finishedAddingRowsAsync();
		}
	},
	
	finishedAddingRowsAsync: function() {
		this.resetAsyncData();
		this.resizeColumns();	
		this.onAsyncDataAdditionComplete();
	}

};

Rebound.GlobalTrader.Site.Controls.FlexiDataTable.registerClass("Rebound.GlobalTrader.Site.Controls.FlexiDataTable", Sys.UI.Control, Sys.IDisposable);