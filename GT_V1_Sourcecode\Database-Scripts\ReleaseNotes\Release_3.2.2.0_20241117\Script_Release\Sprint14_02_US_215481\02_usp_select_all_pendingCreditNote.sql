﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY		DATE         	ACTION 		DESCRIPTION
[US-215481]		An.TranTan		01-Nov-2024		Update		Get IsXML column in CreditDebitEmailer
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_select_all_pendingCreditNote]      
-- =============================================      
-- Author:  <Abhinav <PERSON>a>      
-- Create date: <12-03-2022>      
-- Description: <select all pending credit note email>      
-- =============================================      
AS      
BEGIN      
SET NOCOUNT ON;      
SELECT CreditEmailId,CreditNoteNo,ContactEmail, CAST(ISNULL(IsXML,0) AS BIT) AS IsXML FROM tbCreditEmail 
WHERE SentStatus=0 and EmailStatus='Pending' and ISNULL(ContactEmail,'0')<>'0' 
SET NOCOUNT OFF;     
END   
GO


