/*
Marker     changed by      date         Remarks
[001]      Vinay           21/08/2014    Generate COC as PDF
*/
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Controls.Forms;

namespace Rebound.GlobalTrader.Site.Controls.Forms {
	public partial class EmailDocument : Base {

		#region Properties

		public string DocumentText { get; set; }
		public string HeaderImage { get; set; }
		public string HeaderImageID { get; set; }
		public string Subject { get; set; }

        //[001] code start
        private bool _blnEmailPDF=false;
        private bool _IsNotesEnable = false;
        public bool EmailPDF
        {
            get { return _blnEmailPDF; }
            set { _blnEmailPDF = value; }
        }
        public int InvoiceNo { get; set; }
        //[001] code end

        private string _pdfDocumentType = "COCF";
        public string PDFDocumentType
        {
            get { return _pdfDocumentType; }
            set { _pdfDocumentType = value; }
        }

        public string SignatureImage { get; set; }
        public string SignatureImageID { get; set; }

        public string TermConditionImage { get; set; }
        public string TermConditionImageID { get; set; }

        private string _sectionName = " ";
        public string SectionName
        {
            get { return _sectionName; }
            set { _sectionName = value; }
        }
        public string SubSectionName { get; set; }
        public string TermsPathForClient { get; set; }
        public bool IsNotesEnable {
            get { return _IsNotesEnable; }
            set { _IsNotesEnable = value; }
        }
        public bool IsFormatEnable { get; set; }
        public string DocumentFormat { get; set; } = "PDF";
        public bool AllowGenerateXml { get; set; } = false;

        #endregion

        #region Overrides

        /// <summary>
        /// OnInit
        /// </summary>
        /// <param name="e"></param>
        protected override void OnInit(EventArgs e) {
			SavedOKMessageResource = "DocumentSentOK";
			SavingMessageResource = "Sending";
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("FormTitles", "EmailDocument");
			AddScriptReference("Controls.Forms.EmailDocument.EmailDocument.js");
			LabelFormField ctlSendError = (LabelFormField)FindContentControl("ctlSendError");
			ctlSendError.AddControl(ControlBuilders.CreateLiteral(Functions.GetGlobalResource("Messages", "DocumentSendError")));
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			SetupScriptDescriptors();
			base.OnPreRender(e);
		}

		#endregion

		/// <summary>
		/// Setup Script Descriptors
		/// </summary>
		private void SetupScriptDescriptors() {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument", ctlDesignBase.ClientID);
			_scScriptControlDescriptor.AddProperty("strLoginEmail", SessionManager.LoginEmail);
			_scScriptControlDescriptor.AddElementProperty("ibtnSend", FindIconButton("ibtnSend").ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnSend_Footer", FindFooterIconButton("ibtnSend").ClientID);
			_scScriptControlDescriptor.AddElementProperty("ibtnContinue", FindIconButton("ibtnContinue").ClientID);
			if (FooterLinksHolder != null) _scScriptControlDescriptor.AddElementProperty("ibtnContinue_Footer", FindFooterIconButton("ibtnContinue").ClientID);
			_scScriptControlDescriptor.AddProperty("strDocumentText", DocumentText);
			_scScriptControlDescriptor.AddProperty("strHeaderImage", HeaderImage);
			_scScriptControlDescriptor.AddProperty("strHeaderImageID", HeaderImageID);
			_scScriptControlDescriptor.AddProperty("strSubject", Subject);
            //[001] code start
            _scScriptControlDescriptor.AddProperty("blnEmailPDF", _blnEmailPDF);
            _scScriptControlDescriptor.AddProperty("intInvoiceNo", InvoiceNo);
            _scScriptControlDescriptor.AddProperty("PDFDocumentType", _pdfDocumentType);
            _scScriptControlDescriptor.AddProperty("strSignatureImage", SignatureImage);
            _scScriptControlDescriptor.AddProperty("strSignatureImageID", SignatureImageID);
            //[001] code end
            _scScriptControlDescriptor.AddProperty("TermConditionImage", TermConditionImage);
            _scScriptControlDescriptor.AddProperty("TermConditionImageID", TermConditionImageID);
            _scScriptControlDescriptor.AddProperty("SectionName", _sectionName);
            _scScriptControlDescriptor.AddProperty("SubSectionName", SubSectionName);
            _scScriptControlDescriptor.AddProperty("termsPathForClient", TermsPathForClient);
            _scScriptControlDescriptor.AddProperty("IsNotesEnable", _IsNotesEnable);
            _scScriptControlDescriptor.AddProperty("IsFormatEnable", IsFormatEnable);
            _scScriptControlDescriptor.AddProperty("ClientNo", SessionManager.ClientID);
            _scScriptControlDescriptor.AddProperty("DocumentFormat", DocumentFormat);
            _scScriptControlDescriptor.AddProperty("AllowGenerateXml", AllowGenerateXml);
        }

	}
}