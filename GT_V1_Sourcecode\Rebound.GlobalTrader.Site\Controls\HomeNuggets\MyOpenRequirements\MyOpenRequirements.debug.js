///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements = function(element) { 
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.prototype = {

	get_pnlOpen: function() { return this._pnlOpen; }, 	set_pnlOpen: function(v) { if (this._pnlOpen !== v)  this._pnlOpen = v; }, 
	get_pnlOverdue: function() { return this._pnlOverdue; }, 	set_pnlOverdue: function(v) { if (this._pnlOverdue !== v)  this._pnlOverdue = v; }, 
	get_tblOpen: function() { return this._tblOpen; }, 	set_tblOpen: function(v) { if (this._tblOpen !== v)  this._tblOpen = v; }, 
	get_tblOverdue: function() { return this._tblOverdue; }, 	set_tblOverdue: function(v) { if (this._tblOverdue !== v)  this._tblOverdue = v; }, 
	get_pnlMore: function() { return this._pnlMore; }, 	set_pnlMore: function(value) { if (this._pnlMore !== value)  this._pnlMore = value; },
	get_lnkMore: function() { return this._lnkMore; }, set_lnkMore: function(value) { if (this._lnkMore !== value) this._lnkMore = value; },

	get_myLoginID: function() { return this.myLoginID; }, set_myLoginID: function(value) { if (this.myLoginID !== value) this.myLoginID = value; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblOpen) this._tblOpen.dispose();
		if (this._tblOverdue) this._tblOverdue.dispose();
		this._pnlOpen = null;
		this._pnlOverdue = null;
		this._tblOpen = null;
		this._tblOverdue = null;
		this._pnlMore = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlMore, false);
		$R_FN.showElement(this._pnlOpen, false);
		$R_FN.showElement(this._pnlOverdue, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.callBaseMethod(this, "setupLoadingState");
	},
	
	showNoData: function(bln) {
		this.showContent(true);
		$R_FN.showElement(this._pnlNoData, bln);
	},
	
	getData: function() {
	this.setupLoadingState();
	if (this._intLoginID_Other > 0) {
	    this._lnkMore.href = $RGT_gotoURL_CusReqBrowse(this._intLoginID_Other);
	   
	}
	else {
	    this._lnkMore.href = $RGT_gotoURL_CusReqBrowse(this.myLoginID);
	}
		
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/MyOpenRequirements");
		obj.set_DataObject("MyOpenRequirements");
		obj.set_DataAction("GetData");
		obj.addParameter("rowcount", this._intRowCount);
		obj.addParameter("OtherLoginID", this._intLoginID_Other);
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		this.showNoData(args._result.Count == 0);
		$R_FN.showElement(this._pnlMore, true);
		var result = args._result;
		var aryData, row;
		//open
		this._tblOpen.clearTable();
		for (var i = 0; i < result.OpenRQ.length; i++) {
			row = result.OpenRQ[i];
			aryData = [
				$RGT_nubButton_CustomerRequirement(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Due
				];
			this._tblOpen.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlOpen, result.OpenRQ.length > 0);
		
		//overdue
		this._tblOverdue.clearTable();
		for (i = 0; i < result.OverdueRQ.length; i++) {
			row = result.OverdueRQ[i];
			aryData = [
				$RGT_nubButton_CustomerRequirement(row.ID, row.No),
				$RGT_nubButton_Company(row.CMNo, row.CM),
				row.Due
				];
			this._tblOverdue.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlOverdue, result.OverdueRQ.length > 0);
		this.hideLoading();
	}
	
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.MyOpenRequirements", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
