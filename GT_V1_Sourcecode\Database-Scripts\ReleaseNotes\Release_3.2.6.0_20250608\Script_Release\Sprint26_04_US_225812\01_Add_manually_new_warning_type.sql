﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
===========================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-225812]     Trung Pham		 16-Apr-2025		CREATE		Insert new warning message type
===========================================================================================================================================================  
*/

IF NOT EXISTS (SELECT 1 FROM tbWarningMessage WHERE WarningName = 'Country Warning' AND WarningId = 9)
BEGIN
	SET IDENTITY_INSERT tbWarningMessage ON
	
	INSERT INTO tbWarningMessage (
		WarningId,
		WarningName,
		ApplyCatagory,
		InActive)
	VALUES (
		9,
		'Country Warning',
		1,
		0)

	SET IDENTITY_INSERT tbWarningMessage OFF
END