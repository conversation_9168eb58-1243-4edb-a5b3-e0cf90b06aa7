<%@ Control Language="C#" CodeBehind="ClientImportBOMMainInfo_Release.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.ClientImportBOMMainInfo_Release" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">
<Explanation><%=Functions.GetGlobalResource("FormExplanations", "BOMItem_Release")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlRelease" runat="server" FieldID="ctlConfirmRelease" ResourceTitle="SaveConfirm">
				<Field><ReboundUI:Confirmation ID="ctlConfirmRelease" runat="server" /></Field>
			</ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
