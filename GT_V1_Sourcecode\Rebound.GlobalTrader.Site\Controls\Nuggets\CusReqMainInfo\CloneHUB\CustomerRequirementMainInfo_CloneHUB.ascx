<%@ Control Language="C#" CodeBehind="CustomerRequirementMainInfo_CloneHUB.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.CustomerRequirementMainInfo_CloneHUB" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="false" AllowQuickHelp="false">

    <Links>
        <ReboundUI:IconButton ID="ibtnBack" runat="server" IconGroup="Nugget" IconTitleResource="Back" IconCSSType="Back" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
    </Links>

    <Explanation><%=Functions.GetGlobalResource("FormExplanations", "CloneHUB")%></Explanation>
    <Content>
        <ReboundUI_Table:Form ID="frm" runat="server">

         <%--   <ReboundUI_Form:FormField ID="ctlSalesperson" runat="server" FieldID="ddlSalesperson" ResourceTitle="Buyer" IsRequiredField="true">
                <Field>
                    <ReboundDropDown:PoHubBuyer ID="ddlSalesperson" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>--%>



            <%--<ReboundFormFieldCollection:NotifyMailMessageReqToHUB id="ctlSendMailMessage" runat="server" />--%>


            <%--<ReboundUI_Form:FormField id="ctlQuoteRequired" runat="server" FieldID="txtQuoteRequired" ResourceTitle="QuoteRequired" IsRequiredField="true">
	            <Field>
		            <ReboundUI:ReboundTextBox ID="txtQuoteRequired" runat="server" Width="150" />
		            <ReboundUI:Calendar ID="calQuoteRequired" runat="server" RelatedTextBoxID="txtQuoteRequired" />
	            </Field>	            
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlSalesman" runat="server" FieldID="ddlSalesman" ResourceTitle="Contact2">
				<Field><ReboundDropDown:Employee ID="ddlSalesman" runat="server" /></Field>
			</ReboundUI_Form:FormField>	--%>
            <asp:TableRow class="">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "RequirementNo")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblCustReqNo" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
             <asp:TableRow class="">
                <asp:TableCell class="lableTD">
                    <label><%=Functions.GetGlobalResource("FormFields", "Customer")%></label>
                </asp:TableCell>
                <asp:TableCell colspan="5">
                    <asp:Label ID="lblCustomer" runat="server" />
                </asp:TableCell>
            </asp:TableRow>
            
            <ReboundUI_Form:FormField ID="ctlConfirm" runat="server" FieldID="ctlConfirmation" ResourceTitle="Confirm">

                <Field>
                    <ReboundUI:Confirmation ID="ctlConfirmation" runat="server" />
                </Field>
            </ReboundUI_Form:FormField>
        </ReboundUI_Table:Form>
    </Content>
</ReboundUI_Form:DesignBase>
