﻿/* 
===========================================================================================
TASK      		UPDATED BY     		DATE         	ACTION 			DESCRIPTION
[US-223239]		An.TranTan			12-Dev-2024		CREATE			Create new 2 tables for bulk edit log
===========================================================================================
*/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
IF NOT EXISTS(SELECT 1 FROM BorisGlobalTraderImports.sys.tables WHERE name = 'tbBulkRLAuditLog')
BEGIN
    CREATE TABLE BorisGlobalTraderImports.[dbo].[tbBulkRLAuditLog](
	[BulkRLAuditLogId] [int] IDENTITY(1,1) NOT NULL,
	[BatchNo] [int] NOT NULL,
	[ReverseLogisticNo] [int] NOT NULL,
	[Part] [nvarchar](30) NOT NULL,
	[FullPart] [nvarchar](30) NOT NULL,
	[Action] [nvarchar](50) NOT NULL,
	[OldValue] [nvarchar](100) NULL,
	[CreatedBy] [int] NOT NULL,
	[CreatedByName] [nvarchar](256) NULL,
	[DLUP] [datetime] NOT NULL,
	PRIMARY KEY CLUSTERED 
	(
		[BulkRLAuditLogId] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
	
	ALTER TABLE BorisGlobalTraderImports.[dbo].[tbBulkRLAuditLog] ADD  DEFAULT (getdate()) FOR [DLUP]
END
GO

IF NOT EXISTS(SELECT 1 FROM BorisGlobalTraderImports.sys.tables WHERE name = 'tbBulkEpoAuditLog')
BEGIN
	CREATE TABLE BorisGlobalTraderImports.[dbo].[tbBulkEpoAuditLog](
		[BulkEpoAuditLogId] [int] IDENTITY(1,1) NOT NULL,
		[BatchNo] [int] NOT NULL,
		[EpoNo] [int] NOT NULL,
		[Part] [nvarchar](30) NOT NULL,
		[FullPart] [nvarchar](30) NOT NULL,
		[Action] [nvarchar](50) NOT NULL,
		[OldValue] [nvarchar](100) NULL,
		[CreatedBy] [int] NOT NULL,
		[CreatedByName] [nvarchar](256) NULL,
		[DLUP] [datetime] NOT NULL,
	PRIMARY KEY CLUSTERED 
	(
		[BulkEpoAuditLogId] ASC
	)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
	) ON [PRIMARY]
	
	ALTER TABLE BorisGlobalTraderImports.[dbo].[tbBulkEpoAuditLog] ADD  DEFAULT (getdate()) FOR [DLUP]
END
GO
