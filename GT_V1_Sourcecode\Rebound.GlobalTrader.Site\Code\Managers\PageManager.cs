//-----------------------------------------------------------------------------------------
// RP 28.10.2009:
// - add Company List Type to Company and Contact Detail links
//Marker     Changed by      Date               Remarks
//[001]      Vinay           14/06/2013         CR:- Supplier Invoice
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;

namespace Rebound.GlobalTrader.Site {
	public class PageManager {

		#region URL Jump functions

		public static string GotoURL_Home() {
			return Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Home").Url;
		}

		public static string GotoURL_Home(Pages.Default.TabList enmTab) {
			return string.Format("{0}?{1}={2}",
				GotoURL_Home(),
				Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName,
				(int)enmTab);
		}

		public static string GotoURL_CompanyDetail(int intCompanyID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Contact_CompanyDetail").Url,
				Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName,
				intCompanyID);
		}

		public static string GotoURL_CompanyDetail(int intCompanyID, int intTab) {
			return string.Format("{0}&{1}={2}",
				GotoURL_CompanyDetail(intCompanyID),
				Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName,
				intTab
			);
		}

		public static string GotoURL_CompanyDetail(int intCompanyID, int intTab, Controls.DataListNuggets.CompanyListType enmCompanyListType) {
			return string.Format("{0}&{1}={2}",
				GotoURL_CompanyDetail(intCompanyID, intTab),
				QueryStringManager.QueryStringVariables.CompanyListType.VariableQSName,
				Convert.ToInt32(enmCompanyListType)
			);
		}

		public static string GotoURL_ManufacturerDetail(int? intManufacturerID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Contact_ManufacturerDetail").Url,
				QueryStringManager.QueryStringVariables.ManufacturerID.VariableQSName,
				intManufacturerID
				);
		}

		public static string GotoURL_ContactDetail(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Contact_ContactDetail").Url,
				Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName,
				intID
			);
		}

		public static string GotoURL_ContactDetail(int intID, Controls.DataListNuggets.CompanyListType enmCompanyListType) {
			return string.Format("{0}&{1}={2}",
				GotoURL_ContactDetail(intID),
				QueryStringManager.QueryStringVariables.CompanyListType.VariableQSName,
				Convert.ToInt32(enmCompanyListType)
			);
		}

		public static string GotoURL_CompanyList() {
			return Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Contact_CompanyBrowse").Url;
		}

		public static string GotoURL_CompanyList(int intTab) {
			return string.Format("{0}?{1}={2}"
				, GotoURL_CompanyList()
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
				, intTab
			);
		}

		public static string GotoURL_CompanyList(Controls.DataListNuggets.CompanyListType enmCompanyListType) {
			return string.Format("{0}?{1}={2}"
				, GotoURL_CompanyList()
				, QueryStringManager.QueryStringVariables.CompanyListType.VariableQSName
				, (int)enmCompanyListType
			);
		}

		public static string GotoURL_CompanyList(int intTab, Controls.DataListNuggets.CompanyListType enmCompanyListType) {
			return string.Format("{0}?{1}={2}"
				, GotoURL_CompanyList(intTab)
				, QueryStringManager.QueryStringVariables.CompanyListType.VariableQSName
				, (int)enmCompanyListType
			);
		}

		public static string GotoURL_SalesOrderList(string strPage, int intTab) {
			return string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage(strPage).Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
				, intTab
				);
		}

		public static string GotoURL_SalesOrder(int intSalesOrderID) {
			return string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_SalesOrderDetail").Url
				, QueryStringManager.QueryStringVariables.SalesOrderID.VariableQSName
				, intSalesOrderID
				);
		}

		public static string GotoURL_SalesOrderAdd(int? intCompanyID, string strCompanyName, int? intContactID) {
			string str = string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_SalesOrderAdd").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
				, intCompanyID
				);
			if (intContactID != null) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
				, intContactID
				);
			if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strCompanyName)
				);
			return str;
		}
		public static string GotoURL_SalesOrderAdd(int? intCompanyID, string strCompanyName) { return GotoURL_SalesOrderAdd(intCompanyID, strCompanyName, null); }
		public static string GotoURL_SalesOrderAdd(int? intCompanyID) { return GotoURL_SalesOrderAdd(intCompanyID, null, null); }

		public static string GotoURL_PurchaseOrderList(int intTab) {
			return string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_PurchaseOrderBrowse").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
				, intTab
				);
		}

		public static string GotoURL_PurchaseOrder(int intID) {
			return string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_PurchaseOrderDetail").Url
				, QueryStringManager.QueryStringVariables.PurchaseOrderID.VariableQSName
				, intID
				);
		}

		public static string GotoURL_PurchaseOrderAdd(int? intCompanyID, string strCompanyName, int? intContactID) {
			string str = string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_PurchaseOrderAdd").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
				, intCompanyID
				);
			if (intContactID != null) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
				, intContactID
				);
			if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strCompanyName)
				);
			return str;
		}
		public static string GotoURL_PurchaseOrderAdd(int? intCompanyID, string strCompanyName) { return GotoURL_PurchaseOrderAdd(intCompanyID, strCompanyName, null); }
		public static string GotoURL_PurchaseOrderAdd(int? intCompanyID) { return GotoURL_PurchaseOrderAdd(intCompanyID, null, null); }

		public static string GotoURL_DebitNote(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_DebitNoteDetail").Url
				, QueryStringManager.QueryStringVariables.DebitID.VariableQSName
				, intID
				);
		}

		public static string GotoURL_DebitNoteAdd(int? intCompanyID, string strCompanyName, int? intContactID, string strContactName) {
			bool blnFirstQueryStringItem = true;
			string str = Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_DebitNoteAdd").Url;
			if (intCompanyID != null) {
				str += string.Format("{0}{1}={2}"
					, (blnFirstQueryStringItem) ? "?" : "&"
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
					, intCompanyID
				);
				blnFirstQueryStringItem = false;
			}
			if (!String.IsNullOrEmpty(strCompanyName)) {
				str += string.Format("{0}{1}={2}"
					, (blnFirstQueryStringItem) ? "?" : "&"
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
					, HttpContext.Current.Server.UrlEncode(strCompanyName)
				);
				blnFirstQueryStringItem = false;
			}
			if (intContactID != null) {
				str += string.Format("{0}{1}={2}"
					, (blnFirstQueryStringItem) ? "?" : "&"
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
					, intContactID
				);
				blnFirstQueryStringItem = false;
			}
			if (!String.IsNullOrEmpty(strContactName)) {
				str += string.Format("{0}{1}={2}"
					, (blnFirstQueryStringItem) ? "?" : "&"
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactName.VariableQSName
					, HttpContext.Current.Server.UrlEncode(strContactName)
				);
				blnFirstQueryStringItem = false;
			}
			return str;
		}
		public static string GotoURL_DebitNoteAdd(int? intCompanyID, string strCompanyName) { return GotoURL_DebitNoteAdd(intCompanyID, strCompanyName, null, null); }

		public static string GotoURL_SRMAList(int intTab) {
			return string.Format("{0}?{1}={2}",
					Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_SupplierRMABrowse").Url
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
					, intTab
					);
		}

		public static string GotoURL_SupplierRMA(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_SupplierRMADetail").Url
				, QueryStringManager.QueryStringVariables.SRMAID.VariableQSName
				, intID
			);
		}

		public static string GotoURL_SupplierRMAAdd(int? intCompanyID, string strCompanyName, int? intContactID, string strContactName) {
			string str = string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_SupplierRMAAdd").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
				, intCompanyID
				);
			if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strCompanyName)
				);
			if (intContactID != null) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
				, intContactID
				);
			if (!String.IsNullOrEmpty(strContactName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strContactName)
				);
			return str;
		}
		public static string GotoURL_SupplierRMAAdd(int? intCompanyID, string strCompanyName, int? intContactID) { return GotoURL_SupplierRMAAdd(intCompanyID, strCompanyName, intContactID, null); }
		public static string GotoURL_SupplierRMAAdd(int? intCompanyID, string strCompanyName) { return GotoURL_SupplierRMAAdd(intCompanyID, strCompanyName, null, null); }
		public static string GotoURL_SupplierRMAAdd(int? intCompanyID) { return GotoURL_SupplierRMAAdd(intCompanyID, null, null, null); }

		public static string GotoURL_CreditNote(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_CreditNoteDetail").Url
				, QueryStringManager.QueryStringVariables.CreditID.VariableQSName
				, intID
			);
		}

		public static string GotoURL_CreditNoteAdd(int? intCompanyID, string strCompanyName, int? intContactID, string strContactName) {
			bool blnFirstQueryStringItem = true;
			string str = Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_CreditNoteAdd").Url;
			if (intCompanyID != null) {
				str += string.Format("{0}{1}={2}"
					, (blnFirstQueryStringItem) ? "?" : "&"
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
					, intCompanyID
				);
				blnFirstQueryStringItem = false;
			}
			if (!String.IsNullOrEmpty(strCompanyName)) {
				str += string.Format("{0}{1}={2}"
					, (blnFirstQueryStringItem) ? "?" : "&"
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
					, HttpContext.Current.Server.UrlEncode(strCompanyName)
				);
				blnFirstQueryStringItem = false;
			}
			if (intContactID != null) {
				str += string.Format("{0}{1}={2}"
					, (blnFirstQueryStringItem) ? "?" : "&"
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
					, intContactID
				);
				blnFirstQueryStringItem = false;
			}
			if (!String.IsNullOrEmpty(strContactName)) {
				str += string.Format("{0}{1}={2}"
					, (blnFirstQueryStringItem) ? "?" : "&"
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactName.VariableQSName
					, HttpContext.Current.Server.UrlEncode(strContactName)
				);
				blnFirstQueryStringItem = false;
			}
			return str;
		}
		public static string GotoURL_CreditNoteAdd(int? intCompanyID, string strCompanyName) { return GotoURL_CreditNoteAdd(intCompanyID, strCompanyName, null, null); }

		public static string GotoURL_CRMAList(int intTab) {
			return string.Format("{0}?{1}={2}",
					Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_CustomerRMABrowse").Url
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
					, intTab
					);
		}

		public static string GotoURL_CustomerRMA(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_CustomerRMADetail").Url
				, QueryStringManager.QueryStringVariables.CRMAID.VariableQSName
				, intID
				);
		}

		public static string GotoURL_CustomerRMAAdd(int? intCompanyID, string strCompanyName, int? intContactID, string strContactName) {
			string str = string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_CustomerRMAAdd").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
				, intCompanyID
				);
			if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strCompanyName)
			);
			if (intContactID != null) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
				, intContactID
				);
			if (!String.IsNullOrEmpty(strContactName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strContactName)
			);
			return str;
		}
		public static string GotoURL_CustomerRMAAdd(int? intCompanyID, string strCompanyName, int? intContactID) { return GotoURL_CustomerRMAAdd(intCompanyID, strCompanyName, intContactID, null); }
		public static string GotoURL_CustomerRMAAdd(int? intCompanyID, string strCompanyName) { return GotoURL_CustomerRMAAdd(intCompanyID, strCompanyName, null, null); }
		public static string GotoURL_CustomerRMAAdd(int? intCompanyID) { return GotoURL_CustomerRMAAdd(intCompanyID, null, null, null); }

		public static string GotoURL_InvoiceList(int intTab) {
			return string.Format("{0}?{1}={2}",
					Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_InvoiceDetail").Url
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
					, intTab
					);
		}

		public static string GotoURL_Invoice(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_InvoiceDetail").Url
				, QueryStringManager.QueryStringVariables.InvoiceID.VariableQSName
				, intID
				);
		}

		public static string GotoURL_InvoiceAdd(int? intCompanyID, string strCompanyName, int? intContactID, string strContactName) {
			string str = string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_InvoiceAdd").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
				, intCompanyID
				);
			if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strCompanyName)
			);
			if (intContactID != null) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
				, intContactID
				);
			if (!String.IsNullOrEmpty(strContactName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strContactName)
			);
			return str;
		}
		public static string GotoURL_InvoiceAdd(int? intCompanyID, string strCompanyName, int? intContactID) { return GotoURL_InvoiceAdd(intCompanyID, strCompanyName, intContactID, null); }
		public static string GotoURL_InvoiceAdd(int? intCompanyID, string strCompanyName) { return GotoURL_InvoiceAdd(intCompanyID, strCompanyName, null, null); }
		public static string GotoURL_InvoiceAdd(int? intCompanyID) { return GotoURL_InvoiceAdd(intCompanyID, null, null, null); }

		public static string GotoURL_QuoteList(int intTab) {
			return string.Format("{0}?{1}={2}",
					Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_QuoteBrowse").Url
					, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
					, intTab
					);
		}

		public static string GotoURL_Quote(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_QuoteDetail").Url
				, QueryStringManager.QueryStringVariables.QuoteID.VariableQSName
				, intID
				);
		}

		public static string GotoURL_QuoteAdd(int? intCompanyID, string strCompanyName, int? intContactID) {
			string str = string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_QuoteAdd").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
				, intCompanyID
				);
			if (intContactID != null) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
				, intContactID
				);
			if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strCompanyName)
				);
			return str;
		}
		public static string GotoURL_QuoteAdd(int? intCompanyID, string strCompanyName) { return GotoURL_QuoteAdd(intCompanyID, strCompanyName, null); }
		public static string GotoURL_QuoteAdd(int? intCompanyID) { return GotoURL_QuoteAdd(intCompanyID, null, null); }

		public static string GotoURL_CustomerRequirementList(int intTab) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_CustomerRequirementBrowse").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
				, intTab
				);
		}

		public static string GotoURL_CustomerRequirement(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_CustomerRequirementDetail").Url
				, QueryStringManager.QueryStringVariables.CustomerRequirementID.VariableQSName
				, intID
				);
		}

		public static string GotoURL_CustomerRequirementAdd(int? intCompanyID, string strCompanyName, int? intContactID) {
			string str = string.Format("{0}?{1}={2}"
				, Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_CustomerRequirementAdd").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
				, intCompanyID
				);
			if (intContactID != null) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.ContactID.VariableQSName
				, intContactID
				);
			if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strCompanyName)
			);
			return str;
		}
		public static string GotoURL_CustomerRequirementAdd(int? intCompanyID, string strCompanyName) { return GotoURL_CustomerRequirementAdd(intCompanyID, strCompanyName, null); }
		public static string GotoURL_CustomerRequirementAdd(int? intCompanyID) { return GotoURL_CustomerRequirementAdd(intCompanyID, null, null); }

		public static string GotoURL_PurchaseRequisitionList(int intTab) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_PurchaseRequisitionBrowse").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.Tab.VariableQSName
				, intTab
				);
		}

		public static string GotoURL_Report(int intReportID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Reports_ReportDetail").RelativeUrl
				, QueryStringManager.QueryStringVariables.ReportID.VariableQSName
				, intReportID
				);
		}

		public static string GotoURL_SecurityUsers(int intLoginID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Setup_Security_Users").Url
				, Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.LoginID.VariableQSName
				, intLoginID
				);
		}

		public static string GotoURL_Sourcing() {
			return Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_Sourcing").Url;
		}
		public static string GotoURL_Sourcing(string strPartNo) {
			return string.Format("{0}?{1}={2}",
				GotoURL_Sourcing()
				, QueryStringManager.QueryStringVariables.SearchPartNo.VariableQSName
				, HttpContext.Current.Server.UrlEncode(strPartNo)
				);
		}

		public static string GotoURL_GoodsIn(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Warehouse_GoodsInDetail").Url
				, QueryStringManager.QueryStringVariables.GoodsInID.VariableQSName
				, intID
				);
		}

		internal static string GotoURL_Lot(int? intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Warehouse_LotsDetail").Url
				, QueryStringManager.QueryStringVariables.LotID.VariableQSName
				, intID
				);
		}

		internal static string GotoURL_Stock(int intID) {
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Warehouse_StockDetail").Url
				, QueryStringManager.QueryStringVariables.StockID.VariableQSName
				, intID
				);
		}
        //[001] code start
        /// <summary>
        /// Get Supplier Invoice Detail Page
        /// </summary>
        /// <param name="intID"></param>
        /// <returns></returns>
        public static string GotoURL_SupplierInvoice(int intID)
        {
            return string.Format("{0}?{1}={2}",
                Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Warehouse_SupplierInvoiceDetail").Url
                , QueryStringManager.QueryStringVariables.SupplierInvoiceID.VariableQSName
                , intID
                );
        }
        public static string GotoURL_SupplierInvoiceAdd(int? intCompanyID,int? goodsInId, string strCompanyName)
        {
            string str = string.Format("{0}?{1}={2}"
                , Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Warehouse_SupplierInvoiceAdd").Url
                , Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
                , intCompanyID
                );
            if (goodsInId.HasValue)
            {
                str += string.Format("&{0}={1}"
                , Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.GoodsInID.VariableQSName
                , goodsInId
            );
            }
            if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
                , Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
                , HttpContext.Current.Server.UrlEncode(strCompanyName)
            );
            return str;
        }
        //[001] code end
        public static string GotoURL_NPR(int? intLineID, int intID)
        {
            string str = string.Format("{0}?{1}={2}",
                Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("NPRPrint").Url
                , QueryStringManager.QueryStringVariables.GenericID.VariableQSName
                , intLineID
                );
            if (intID > 0) str += string.Format("&{0}={1}"
              , QueryStringManager.QueryStringVariables.NPRID.VariableQSName
              , intID
              );
            return MailTemplateManager.GetMessage_NotifyNPROutLook(string.Format(Convert.ToString(System.Configuration.ConfigurationManager.AppSettings["GTVirtualURL"]) + "{0}", str.Replace("~/", "")));
        }

        internal static string GotoURL_BOM(int? intID)
        {
            return string.Format("{0}?{1}={2}",
                Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_BOMDetail").Url
                , QueryStringManager.QueryStringVariables.BOMID.VariableQSName
                , intID
                );
        }
		//code start for [002] 
		internal static string GotoURL_BMM(int? intID)
		{
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_BOMManagerDetail").Url
				, QueryStringManager.QueryStringVariables.BMMID.VariableQSName
				, intID
				);
		}
		internal static string GotoURL_BOMManager(int? intID)
		{
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Ord_BOMManagerDetail").Url
				, QueryStringManager.QueryStringVariables.BMMID.VariableQSName
				, intID
				);
		}
		internal static string GotoURL_BOMManagerSourcing(int? intID)
		{
			return string.Format("{0}?{1}={2}",
				Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Ord_BOMManagerSourcing").Url
				, QueryStringManager.QueryStringVariables.BMMID.VariableQSName
				, intID
				);
		}
		//code start for [002] 
		//public static string GotoURL_ClientInvoice(int intID)
		//{
		//    return string.Format("{0}?{1}={2}",
		//        Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Ord_ClientInvoiceDetail").Url
		//        , QueryStringManager.QueryStringVariables.SupplierInvoiceID.VariableQSName
		//        , intID
		//        );
		//}
		public static string GotoURL_ClientInvoiceAdd(int? intCompanyID, int? goodsInId, string strCompanyName)
        {
            string str = string.Format("{0}?{1}={2}"
                , Rebound.GlobalTrader.Site.Site.GetInstance().GetPage("Orders_ClientInvoiceAdd").Url
                , Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyID.VariableQSName
                , intCompanyID
                );
            if (goodsInId.HasValue)
            {
                str += string.Format("&{0}={1}"
                , Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.GoodsInID.VariableQSName
                , goodsInId
            );
            }
            if (!String.IsNullOrEmpty(strCompanyName)) str += string.Format("&{0}={1}"
                , Rebound.GlobalTrader.Site.QueryStringManager.QueryStringVariables.CompanyName.VariableQSName
                , HttpContext.Current.Server.UrlEncode(strCompanyName)
            );
            return str;
        }
		#endregion
	}
}
