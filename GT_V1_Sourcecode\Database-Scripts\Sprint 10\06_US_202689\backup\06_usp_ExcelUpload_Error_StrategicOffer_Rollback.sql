﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE OR ALTER PROCEDURE [dbo].[usp_ExcelUpload_Error_StrategicOffer]                                                                                                         
 @UserId INT=0 ,                                            
  @ClientId INT =0,                                            
  @SelectedClientId int=0      
      
   /*  Created By  :<PERSON><PERSON>            
      Created On  :28/07/2023            
      Created For :Requirement RP-2055           
          
      Updated By:  <PERSON><PERSON>              
      Updated On: 15/09/2023              
      Updated For   :*[001] RP-1950 (To handle decimal values in Quantity field)       
       
    Updated By:  <PERSON><PERSON>              
      Updated On: 15/12/2023              
      Updated For   :*[002] RP-2733 (To handle DATE FORMAT ISSUE COMMENTED THE DATE CHECK)      
      
   [usp_ExcelUpload_Error_StrategicOffer]   4763,114,114         
 */         
                                            
WITH RECOMPILE AS                                                 
    BEGIN     
 /*[002] start code*/  
 SET DATEFORMAT DMY;  
 /*[002] end code*/  
 /*                 
 ----drop table  #tbEpoToBeImported_Temp               
  select * from BorisGlobalTraderImports.dbo.tbEpo             
 -- exec [dbo].[usp_ExcelUpload_Error_StrategicOffer] 4670,114,114                                             
select ValidationMessage,iif(Price=0,'YES','NO'), * from    #tbEpoToBeImported_Temp --where TempId in (519,520,521,522)                                            
--select * from  BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp  -- where TempId in  (519,520,521,522)                                            
 --update BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp  set OriginalEntryDate='2020-03-03' where TempId=519                                            
 ---Declare @TotalCount varchar(100)='';                     
 */                    
select                                             
TempId,                                            
(case   when len(Part)>30 then dbo.stripAlphahnumeric(SUBSTRING(Part,0, 30)) else dbo.stripAlphahnumeric(Part) end )as Part                                                
,(case   when len( ManufacturerName)>50 then dbo.stripAlphahtestingMfr(SUBSTRING(ManufacturerName,0, 50))else dbo.stripAlphahtestingMfr(ManufacturerName) end )as ManufacturerName                                                                            
,cast(dbo.stripNumeric(Price) as FLOAT ) as Price                                              
  , (case   when len(LeadTime)>30 then dbo.stripAlphahnumeric2(SUBSTRING(LeadTime,0, 30))else dbo.stripAlphahnumeric2(LeadTime) end )as LeadTime                                                                                           
 , (case   when len(SPQ)>30 then dbo.stripAlphahnumeric2(SUBSTRING(SPQ,0, 30))else dbo.stripAlphahnumeric2(SPQ) end )as SPQ                                                                                     
 , (case   when len(SupplierMOQ)>30 then dbo.stripAlphahnumeric2(SUBSTRING(SupplierMOQ,0, 30))else dbo.stripAlphahnumeric2(SupplierMOQ) end )as SupplierMOQ                                             
, [dbo].[stripAlphahnumeric2](SupplierName) as SupplierName                                            
 ,dbo.stripNumeric(Quantity) as Quantity                                                 
 , (case   when len(DateCode)>30 then dbo.stripAlphahnumeric2(SUBSTRING(DateCode,0, 30))else dbo.stripAlphahnumeric2(DateCode) end )as DateCode                                                   
,dbo.stripAlphahnumeric([Description]) as Description                                               
 ,(case   when len(SupplierType)>50 then dbo.stripAlphahnumeric(SUBSTRING(SupplierType,0, 50))else dbo.stripAlphahnumeric(SupplierType) end )as SupplierType                                                    
 ,(case   when len(FactorySealed)>50 then dbo.stripAlphahnumeric(SUBSTRING(FactorySealed,0, 50))else dbo.stripAlphahnumeric(FactorySealed) end )as FactorySealed                                                    
 ,(case   when len(SupplierLTB)>50 then dbo.stripAlphahnumeric(SUBSTRING(SupplierLTB,0, 50))else dbo.stripAlphahnumeric(SupplierLTB) end )as SupplierLTB                                              
,CAST(dbo.stripNumeric(VirtualCostPrice) as float) as VirtualCostPrice    
/*[002] CODE START*/  
  /*--, ( case when ISDATE(cast(Rtrim(LTRIM(OriginalEntryDate))as varchar(10)))=0 then NULL */  
 , ( CASE WHEN ISDATE(CONVERT(VARCHAR(10),RTRIM(LTRIM(OriginalEntryDate)),103) )=0  THEN NULL   
/*[002] CODE END*/  
   WHEN LEN(CONVERT(DATETIME, CAST(RTRIM(LTRIM(OriginalEntryDate))AS VARCHAR(10)),104))>50 THEN CONVERT(DATETIME,                                                             
 CAST(RTRIM(LTRIM(OriginalEntryDate))AS VARCHAR(10)),104) ELSE CONVERT(DATETIME, CAST(RTRIM(LTRIM(OriginalEntryDate))as varchar(10)),104) end )as OriginalEntryDate ,                                            
1 as isValid,                                        
cast('' as varchar(max))  as ValidationMessage                                            
into #tbEpoToBeImported_Temp                              
from  BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp where ClientId=@ClientId and SelectedClientId= @SelectedClientId and CreatedBy=@UserId                                            
                                       
 /*********** Updation Validation Query **************/                                         
--Part length 30  & mandotary--                                            
update  TmpR set                                             
TmpR.ValidationMessage=  case                                  
when   isnull(TRl.Part,'')=''                                           
then  ISNULL(ValidationMessage, '') +'MPN Field is mandotary.' +'<br/>'                                                                  
when  len(TRl.Part)>30                                             
then  ISNULL(ValidationMessage, '') + 'MPN Field only accepts 30 characters.'+'<br/>'                                             
else TmpR.ValidationMessage end                                            
from                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
 --- DateCode length 5--                                            
update  TmpR set                                             
TmpR.ValidationMessage= case                                                                              
when    len(TRl.DateCode)>5                                             
then  ISNULL(ValidationMessage, '') +'DateCode Field only accepts 5 characters.'+'<br/>'                                              
else TmpR.ValidationMessage end                                            
from                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                            
 --- ManufacturerName length 128--                                            
update  TmpR set                                            
TmpR.ValidationMessage= case                                                                                
when len(TRl.ManufacturerName) >128                                            
then  ISNULL(ValidationMessage, '') +'ManufacturerName Field only accepts 128 characters.'+'<br/>'                                             
else TmpR.ValidationMessage end                                            
from                                            
#tbEpoToBeImported_Temp TmpR inner Join                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                     
--- Quantity   & mandotary--     
update  TmpR set                                            
TmpR.ValidationMessage=           
/* [001] CODE START */            
CASE WHEN ISNUMERIC(TmpR.Quantity)=0            
THEN  ISNULL(ValidationMessage, '')+'Quantity Field only accepts Integer values.' +'<br/>'                                     
WHEN  TmpR.Quantity='0'   AND TRl.Quantity <>'0'       
THEN  ISNULL(ValidationMessage, '') +'Quantity Field only accepts Integer values.' +'<br/>'        
/* [001] CODE END */          
ELSE TmpR.ValidationMessage END                                            
FROM                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
ON Trl.TempId=TmpR.TempId                                            
                               
--- Price length 4--                                            
update  TmpR set                                            
TmpR.ValidationMessage= case                                            
WHEN  ISNUMERIC(TmpR.Price)=0                                              
THEN  ISNULL(ValidationMessage, '') +'Cost Field only accepts Integer values.' +'<br/>'                                                                                        
WHEN  TmpR.Price='0'   AND TRl.Price <>'0'       
THEN  ISNULL(ValidationMessage, '') +'Cost Field only accepts Integer values.' +'<br/>'                                                                                        
ELSE TmpR.ValidationMessage end                                            
FROM                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                            
--- LeadTime length 50--                                            
update  TmpR set                                            
TmpR.ValidationMessage= case                                                                               
when len(TRl.LeadTime) >50                                            
then  ISNULL(ValidationMessage, '') +'LeadTime Field only accepts 50 characters.'+'<br/>'                                             
else TmpR.ValidationMessage end                                            
from                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                            
--- SPQ length 10--                                            
update  TmpR set                                            
TmpR.ValidationMessage= case                                            
when   len(TRl.SPQ)>10                               
then  ISNULL(ValidationMessage, '') +'SPQ Field only accepts 10 characters' +'<br/>'                                                            
else TmpR.ValidationMessage end                                            
from                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                  
on Trl.TempId=TmpR.TempId                                            
                           
--- SupplierMOQ length 50--                                            
update  TmpR set                                           
TmpR.ValidationMessage= case                                            
when  len(TRl.SupplierMOQ)>50                                             
then  ISNULL(ValidationMessage, '') +'MOQ Field only accepts 50 characters.'+'<br/>'                                            
else TmpR.ValidationMessage end      
from                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                            
--- SupplierName length 128--                                            
update  TmpR set         
TmpR.ValidationMessage= case                                                                              
when  len(TRl.SupplierName) >128                                            
then  ISNULL(ValidationMessage, '') +'Vendor Field only accepts 128 characters.'+'<br/>'                                            
else TmpR.ValidationMessage end                                            
from                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                            
                                            
--- SupplierType length 50 in tbReverseLogisticToBeImported--                                            
update  TmpR set                                            
TmpR.ValidationMessage= case                                            
when  len(TRl.SupplierType) >50                                           
then  ISNULL(ValidationMessage, '')  +'SupplierType Field only accepts 50 characters' +'<br/>'                                              
                                          
--when  len(TRl.SupplierType) >2                                             
--then  ISNULL(ValidationMessage, '')  +'SupplierType Field only accepts 2 characters.'+'<br/>'                                             
else TmpR.ValidationMessage end                                            
from                                     
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                            
--- FactorySealed length 50--                                            
update  TmpR SET                                            
TmpR.ValidationMessage= CASE                                                                               
WHEN   len(TRl.FactorySealed) >50                                            
THEN  ISNULL(ValidationMessage, '')  +'FactorySealed Field only accepts 50 characters.'+'<br/>'                                            
ELSE TmpR.ValidationMessage END                                            
FROM                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                            
--- SupplierLTB length 50--                                          
update  TmpR set                                            
TmpR.ValidationMessage= case                                                                               
WHEN   Len(TRl.SupplierLTB) >50                                             
THEN  ISNULL(ValidationMessage, '')  +'LTB Field only accepts 50 characters.'+'<br/>'                   
ELSE TmpR.ValidationMessage end                                            
FROM                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                            
--- VirtualCostPrice   & mandotary--                                            
update  TmpR set                                            
TmpR.ValidationMessage= case                                                   
WHEN   ISNUMERIC(TmpR.VirtualCostPrice)=0                         
THEN  ISNULL(ValidationMessage, '')  +'VirtualCostPrice Field only accepts Integer values.' +'<br/>'        
WHEN  TmpR.VirtualCostPrice='0'   AND TRl.VirtualCostPrice <>'0'       
THEN  ISNULL(ValidationMessage, '') +'VirtualCostPrice Field only accepts Integer values.' +'<br/>'        
ELSE TmpR.ValidationMessage end                                            
FROM                                            
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId                                            
                                   
 --- OriginalEntryDate length 10--      
     
    
   
update  TmpR set                                                
TmpR.ValidationMessage= case                                               
WHEN TmpR.OriginalEntryDate IS NULL                                                 
THEN  ISNULL(ValidationMessage, '')  +'Date format is not correct.'                                                 
WHEN TmpR.OriginalEntryDate<> Convert(Datetime, cast(TRl.OriginalEntryDate  as varchar(10)),104)                                               
   --and len(cast(TRl.OriginalEntryDate as varchar(100))) >10                                                
THEN ISNULL(ValidationMessage, '')  +'Date Field only accepts Date values in dd-mm-yyyy format.'                                  
ELSE TmpR.ValidationMessage END                                                
FROM                                                
#tbEpoToBeImported_Temp TmpR inner Join                                              
BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp TRl                                             
on Trl.TempId=TmpR.TempId               
     
     
      
    
/*********** Updation Validation Query  ENDs **************/                                            
update #tbEpoToBeImported_Temp set                                             
--ValidationMessage = ValidationMessage +'. Please verify source data and column mappings and try again'                                 
isvalid=0                                    
where ISNULL(ValidationMessage,'')<>''                                            
                                            
                                        
                                            
 /*********** Select Final Query  **************/                                            
Select ROW_NUMBER()over( order by  RL.TempId asc) as SNo,                                            
RL.Part as MPN ,RL.ManufacturerName as MFR,                  
RL.Quantity,RL.VirtualCostPrice as 'Virtual Cost Price',                                            
RL.Price as COST,RL.LeadTime as 'Lead Time',RL.DateCode as 'Date Code',                                            
RL.SPQ,RL.SupplierMOQ as MOQ,                                            
RL.SupplierName as Vendor, RL.SupplierType as 'Vendor Type',                                            
RL.FactorySealed as 'Factory Sealed',RL.SupplierLTB as 'LTB',                                       
RL.OriginalEntryDate as Date,                                            
RL.Description,                          
 iif(TL.ValidationMessage='','','<b>'+TL.ValidationMessage+'<b/>') as 'Reason for Excel Upload failed',                                            
--TL.ValidationMessage,                                            
/* ,@TotalCount as Tcount */                                             
RL.OriginalFilename                                             
from #tbEpoToBeImported_Temp TL                                            
inner join BorisGlobalTraderImports.dbo.tbEpoToBeImported_Temp RL                                           
on RL.TempId=TL.TempId where isValid<>1                         
END 
