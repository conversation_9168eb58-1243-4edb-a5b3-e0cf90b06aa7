﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;

namespace Rebound.GlobalTrader.Site.Code.Common
{
    public class CSLManager
    {
        public class Address
        {
            public string address { get; set; }
            public string city { get; set; }
            public string state { get; set; }
            public object postal_code { get; set; }
            public string country { get; set; }
        }

        public class Id
        {
            public string type { get; set; }
            public string country { get; set; }
            public string expiration_date { get; set; }
            public string issue_date { get; set; }
            public string number { get; set; }
        }

        public class Result
        {
            public string id { get; set; }
            public string name { get; set; }
            public List<string> alt_names { get; set; }
            public object call_sign { get; set; }
            public List<string> citizenships { get; set; }
            public object country { get; set; }
            public List<string> dates_of_birth { get; set; }
            public object end_date { get; set; }
            public string entity_number { get; set; }
            public object federal_register_notice { get; set; }
            public object gross_registered_tonnage { get; set; }
            public object gross_tonnage { get; set; }
            public object license_policy { get; set; }
            public object license_requirement { get; set; }
            public List<string> nationalities { get; set; }
            public List<string> places_of_birth { get; set; }
            public List<string> programs { get; set; }
            public string remarks { get; set; }
            public string source { get; set; }
            public string source_information_url { get; set; }
            public string source_list_url { get; set; }
            public object standard_order { get; set; }
            public object start_date { get; set; }
            public object title { get; set; }
            public string type { get; set; }
            public object vessel_flag { get; set; }
            public object vessel_owner { get; set; }
            public object vessel_type { get; set; }
            public List<Address> addresses { get; set; }
            public List<Id> ids { get; set; }
        }

        public class Root
        {
            public int total { get; set; }
            public List<Source> sources { get; set; }
            public List<Result> results { get; set; }
        }

        public class Source
        {
            public int count { get; set; }
            public string value { get; set; }
        }
    }
}