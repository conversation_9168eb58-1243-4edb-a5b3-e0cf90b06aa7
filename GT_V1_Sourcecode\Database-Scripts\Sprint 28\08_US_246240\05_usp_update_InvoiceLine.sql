﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

/*
==============================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-246240]     Phuc Hoang		 15-May-2025		UPDATE		Invoice - Line from 'Authorised SO Service Line' source function on Client/ DMCC side (Part 2)
==============================================================================================================================================================  
*/

CREATE OR ALTER PROCEDURE [dbo].[usp_update_InvoiceLine] (
--********************************************************************************
--* SK 29.10.2009:
--* - allow for new column - FullCustomerPart - used for searching
--*  
--* SK 29/07/2009:
--* - allow for Notes
--*
--* SK 09.07.2009:
--* - update CustomerPart
--********************************************************************************
    @InvoiceLineId int
  , @SalesOrderLineNo int = NULL
  , @ShippedBy int = NULL
  , @ShippedDate datetime = NULL
  , @CustomerPart nvarchar(30) = Null
  , @Notes nvarchar(128) = Null 
  , @UpdatedBy int = NULL
  , @PrintHazardous bit = NULL
  , @Cost float = NULL
  , @Price float = NULL
  , @Taxable NVARCHAR(10) = NULL
  , @RequiredDate DateTime = NULL
  , @DatePromised DateTime = NULL	
  , @RowsAffected int = NULL OUTPUT
)
AS 

BEGIN

    UPDATE  dbo.tbInvoiceLine
    SET     SalesOrderLineNo = @SalesOrderLineNo
          , ShippedBy = @ShippedBy
          , ShippedDate = @ShippedDate
		  ,	CustomerPart = @CustomerPart
		  , Notes = @Notes
          , UpdatedBy = @UpdatedBy
          , DLUP = CURRENT_TIMESTAMP
		  , FullCustomerPart = dbo.ufn_get_fullpart(@CustomerPart)
		  , PrintHazardous = @PrintHazardous
		  , RequiredDate = @RequiredDate
		  , DatePromised = @DatePromised
		  , Price = @Price
		  , Taxable = @Taxable
    WHERE   InvoiceLineId = @InvoiceLineId;

	IF (ISNULL(@Cost, 0) > 0)
	BEGIN
		UPDATE dbo.tbInvoiceLineAllocation 
		SET LandedCost = @Cost
		WHERE InvoiceLineNo = @InvoiceLineId;
	END

    SELECT  @RowsAffected = @@ROWCOUNT

END

GO


