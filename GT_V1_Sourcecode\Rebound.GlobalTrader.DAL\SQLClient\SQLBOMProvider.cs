﻿//Marker    changed by      date           Remarks
//[001]      Vinay          12/08/2014     ESMS  Ticket Number: 	201
//[002]    <PERSON><PERSON>    22/02/2016     BOM for Dash Board
//[003]      <PERSON><PERSON>  30/08/2018     Add ClientCode,ReceivedBy and <PERSON><PERSON><PERSON> field
//[004]      <PERSON><PERSON><PERSON>    15-Oct-2018    Export all result instead of single page on HUBRFQ.
//[005]      <PERSON><PERSON>  20-Dec-2018    Add Status Dropdown in Customer Requirement.
//[006]      <PERSON><PERSON>  27-Dec-2018    Showing, Add And update Client BOM Details.
//[006]      <PERSON><PERSON>  27-Dec-2018    Showing, Add And update Client BOM Details.
//[007]      <PERSON><PERSON>  18-Mar-2019    Showing Records Processed and Records Remaining.
//[008]  <PERSON><PERSON>  19-Dec-2023    RP-2629.
using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient {
	public class SqlBOMProvider : BOMProvider {
		/// <summary>
		/// Count BOM
        /// Calls [usp_count_BOM]
		/// </summary>
        public override Int32 CountForBOM(System.Int32? clientId)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_count_BOM", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
				cn.Open();
				return (Int32)ExecuteScalar(cmd);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to count BOM", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
        /// <summary>
        /// DataListNugget 
        /// Calls [usp_datalistnugget_BOM]
        /// </summary>
        public override List<BOMDetails> DataListNugget(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.Int32? pageIndex, System.Int32? pageSize, System.String bomCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo,
                    int? bomStatus, int? IsAssignToMe, int? assignedUser, System.Int32? intDivision, System.Int32? salesPerson, System.Int32? CompanyTypeid, System.DateTime? startdate, System.DateTime? enddate, System.DateTime? RequiredStartdate, System.DateTime? RequiredEndDate, System.Int32? AS6081Required, System.Boolean? IsAS6081Tab, System.Int32? MyPageSize, System.Boolean? isSearchFromRequirements, System.Int32? SelectedLoginId = null)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (isPOHub == true)
                {
                    if (IsAssignToMe == 0)
                    {
                        cmd = new SqlCommand("usp_datalistnugget_PHBOM", cn);
                    }
                    if (IsAssignToMe == 1)
                    {
                        pageSize = MyPageSize;
                        if (IsAS6081Tab == true)
                        {
                            if (isSearchFromRequirements == true)
                            {
                                cmd = new SqlCommand("usp_AS6081_CustomerRequirementAssignforAS6081Tab", cn);
                            }
                            else
                            {
                                cmd = new SqlCommand("usp_AS6081_datalistnugget_PHBOMAssign", cn);
                            }
                        }
                        else
                        {
                            if (isSearchFromRequirements == true)
                            {
                                cmd = new SqlCommand("usp_AS6081_datalistnugget_CustomerRequirementAssign", cn);
                            }
                            else
                            {
                                cmd = new SqlCommand("usp_datalistnugget_PHBOMAssign", cn);
                            }
                        }
                    }
                }

                else
                {
                    cmd = new SqlCommand("usp_datalistnugget_BOM", cn);
                }
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 120;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@PageIndex", SqlDbType.Int).Value = pageIndex;
                cmd.Parameters.Add("@PageSize", SqlDbType.Int).Value = pageSize;
                cmd.Parameters.Add("@BOMCode", SqlDbType.NVarChar).Value = bomCode;
                //cmd.Parameters.Add("@CodeHi", SqlDbType.NVarChar).Value = CodeHi;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BomStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]
                cmd.Parameters.Add("@CompanyTypeid", SqlDbType.NVarChar).Value = CompanyTypeid;//[004] beer singh
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[003]
                cmd.Parameters.Add("@RequiredStartDate", SqlDbType.DateTime).Value = RequiredStartdate;
                cmd.Parameters.Add("@RequiredEndDate", SqlDbType.DateTime).Value = RequiredEndDate;//[003]
                if (cmd.CommandText == "usp_datalistnugget_BOM" 
                    || cmd.CommandText == "usp_datalistnugget_CustomerRequirementForHUBRFQ" 
                    || cmd.CommandText == "usp_datalistnugget_PHBOM")
                cmd.Parameters.Add("@SelectedLoginId", SqlDbType.Int).Value = SelectedLoginId;


                if (assignedUser > 0)
                {
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }
                cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                cmd.Parameters.Add("@AS6081Required", SqlDbType.Int).Value = AS6081Required;
                
                cn.Open();

                DbDataReader reader = ExecuteReader(cmd);
                List<BOMDetails> lst = new List<BOMDetails>();
                while (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.RowCnt = GetReaderValue_NullableInt32(reader, "RowCnt", null);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CompanyNo = GetReaderValue_NullableInt32(reader, "CompanyNo", null);
                    obj.CompanyType = GetReaderValue_String(reader, "CompanyType", "");
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.BOMStatus = GetReaderValue_String(reader, "Status", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyId", 0);
                    obj.TotalBomLinePrice = GetReaderValue_Double(reader, "TotalBomLinePrice", 0.00);
                    obj.POCurrencyNo = GetReaderValue_Int32(reader, "POCurrencyNo", 0);
                    obj.DateRequestToPOHub = GetReaderValue_NullableDateTime(reader, "DateRequestToPOHub", null);
                    obj.AssignedUser = GetReaderValue_String(reader, "AssignedUser", "");
                    obj.DivisionName = GetReaderValue_String(reader, "DivisionName", "");
                    obj.ClientCode = GetReaderValue_String(reader, "ClientCode", "");
                    obj.Requestedby = GetReaderValue_String(reader, "Requestedby", "");
                    obj.SalesmanName = GetReaderValue_String(reader, "SalesmanName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.ExpediteNotes = GetReaderValue_String(reader, "ExpediteNotes", "");
                    obj.ContactNo = GetReaderValue_NullableInt32(reader, "ContactNo", null);
                    obj.RequiredDate = GetReaderValue_NullableDateTime(reader, "RequiredDate", null);
                    obj.RequiredDateStatus = GetReaderValue_String(reader, "RequiredDateStatus", "");
                    if (isSearchFromRequirements == true)
                    {
                        obj.Part = GetReaderValue_String(reader, "Part", "");
                        obj.Quantity = GetReaderValue_NullableInt32(reader, "Quantity", null);
                        obj.CustomerRequirementId = GetReaderValue_NullableInt32(reader, "CustomerRequirementId", null);
                    }

                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Delete BOM
        /// Calls [usp_delete_BOM]
        /// </summary>
        public override bool Delete(System.Int32? bomId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_delete_BOM", cn);
				cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
				cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
				return (ret > 0);
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to delete BOM", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
				
        /// <summary>
        /// DropDownForClient 
		/// Calls [usp_dropdown_BOM_for_Client]
        /// </summary>
        public override List<BOMDetails> DropDownForClient(System.Int32? clientId, System.Int32? companyId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_BOM_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<BOMDetails> lst = new List<BOMDetails>();
                while (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMs", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
		
		/// <summary>
		/// Create a new row
        /// Calls [usp_insert_BOM]
		/// </summary>
        //public override Int32 Insert(System.Int32? clientId, System.String bomName, System.String notes, System.String bomCode, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Boolean? inactive, System.Int32? UpdateRequirement, System.Int32? Status, System.Int32? CurrencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32 AssignUserNo,out System.String ValidationMessage)
        public override Int32 Insert(System.Int32? clientId,
                                     System.String bomName,
                                     System.String notes,
                                     System.String bomCode,
                                     System.Int32? updatedBy,
                                     System.Int32? companyId,
                                     System.Int32? contactId,
                                     System.Boolean? inactive,
                                     System.Int32? UpdateRequirement,
                                     System.Int32? Status,
                                     System.Int32? CurrencyNo,
                                     System.String currentSupplier,
                                     System.DateTime? quoteRequired,
                                     System.Boolean? AS9120,
                                     System.Int32? Contact2,
                                     System.Int32 AssignUserNo,
                                     out System.String ValidationMessage)
        {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_BOM", cn);
				cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;				
				cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@BOMCode", SqlDbType.NVarChar).Value = bomCode;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactId;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdateRequirementId", SqlDbType.Int).Value = UpdateRequirement;
                cmd.Parameters.Add("@Status", SqlDbType.Int).Value = Status;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = CurrencyNo;
                cmd.Parameters.Add("@CurrentSupplier", SqlDbType.VarChar).Value = currentSupplier;
                cmd.Parameters.Add("@QuoteRequired", SqlDbType.DateTime).Value = quoteRequired;
                cmd.Parameters.Add("@AS9120", SqlDbType.Bit).Value = AS9120;
                cmd.Parameters.Add("@Contact2", SqlDbType.Int).Value = Contact2;
                cmd.Parameters.Add("@AssignUserNo", SqlDbType.Int).Value = AssignUserNo;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ValidationMessage", SqlDbType.VarChar,200).Direction = ParameterDirection.Output;
				cn.Open();
				int ret = ExecuteNonQuery(cmd);
                ValidationMessage = null;
                if (ret == -1)
                {
                    ValidationMessage = (String)(cmd.Parameters["@ValidationMessage"].Value == null ? string.Empty : cmd.Parameters["@ValidationMessage"].Value); ;
                    return ret;
                }             
				return (Int32)cmd.Parameters["@BOMId"].Value;
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to insert BOM", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}
		
		
        /// <summary>
        /// Get 
		/// Calls [usp_select_BOM]
        /// </summary>
		public override BOMDetails Get(System.Int32? BOMId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_select_BOM", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BOMId;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
				if (reader.Read()) {
					//return GetBOMFromReader(reader);
                    Rebound.GlobalTrader.DAL.BOMDetails obj = new Rebound.GlobalTrader.DAL.BOMDetails();
                    obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);
					obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");					
					obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "companyId", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CompanyType = GetReaderValue_String(reader, "CompanyType", "");
                    obj.ContactNo = GetReaderValue_Int32(reader, "contactId", 0);
                    obj.ContactName = GetReaderValue_String(reader, "contactName", "");
                    obj.RequestToPOHubBy = GetReaderValue_NullableInt32(reader, "RequestToPOHubBy", null);
                    obj.DateRequestToPOHub = GetReaderValue_NullableDateTime(reader, "DateRequestToPOHub", null);
                    obj.ReleaseBy = GetReaderValue_NullableInt32(reader, "ReleaseBy", null);
                    obj.DateRelease = GetReaderValue_NullableDateTime(reader, "DateRelease", null);
                    obj.BomCount = GetReaderValue_Int32(reader, "BomCount", 0);
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);                 
                    obj.BOMStatus = GetReaderValue_String(reader, "Status", "");
                    obj.StatusValue = GetReaderValue_NullableInt32(reader, "StatusValue", null);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CurrencyNo = GetReaderValue_NullableInt32(reader, "CurrencyId", null);
                    obj.Currency_Code = GetReaderValue_String(reader, "Currency_Code", "");
                    obj.CurrentSupplier = GetReaderValue_String(reader, "CurrentSupplier", "");
                    obj.QuoteRequired = GetReaderValue_DateTime(reader, "QuoteRequired", DateTime.MinValue);
                    obj.AllItemHasSourcing = GetReaderValue_NullableInt32(reader, "AllHasSourcing", 0);
                    obj.AS9120 = GetReaderValue_Boolean(reader, "AS9120", false);
                    obj.Requestedby = GetReaderValue_String(reader, "Requestedby", "");
                    obj.Releasedby = GetReaderValue_String(reader, "Releasedby", "");
                    obj.NoBidCount = GetReaderValue_Int32(reader, "NoBidCount", 0);
                    obj.UpdateByPH = GetReaderValue_Int32(reader, "UpdateByPH", 0);
                    obj.AssignedUser = GetReaderValue_String(reader, "AssignTo", "");

                    obj.Contact2Id = GetReaderValue_Int32(reader, "Contact2Id", 0);
                    obj.Contact2Name = GetReaderValue_String(reader, "Contact2Name", "");
                    obj.ValidationMessage = GetReaderValue_String(reader, "ValidateMessage", "");
                    obj.IsReqInValid = GetReaderValue_Int32(reader, "IsReqInValid", 0) > 0 ? true : false;
                    obj.ReqSalesPerson = GetReaderValue_String(reader, "ReqSalesPerson", "");
                    obj.SupportTeamMemberNoAsString = GetReaderValue_String(reader, "SupportTeamMemberNo", "");
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.AssignedUserIds= GetReaderValue_String(reader, "AssignedUserIds", "");
                    obj.AS6081= GetReaderValue_String(reader, "AS6081", "");
                    obj.PurchasingNotes = GetReaderValue_String(reader, "PurchasingNotes", "");
                    obj.PVVBOMValidateMessage = GetReaderValue_String(reader, "PVVBOMValidateMessage", "");
                    obj.PVVBOMCountValid = GetReaderValue_Int32(reader, "PVVBOMCountValid", 0) > 0 ? true : false;
                    obj.IsFromProspectiveOffer = GetReaderValue_Int32(reader, "IsFromProspectiveOffer", 0) > 0 ? true : false;
                    obj.UploadedBy = GetReaderValue_String(reader, "UploadedBy", "");

                    return obj;
				} else {
					return null;
				}
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get BOM", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}		
		
        /// <summary>
        /// GetForPage 
		/// Calls [usp_select_BOM_for_Page]
        /// </summary>
		public override BOMDetails GetForPage(System.Int32? bomId) {
			SqlConnection cn = null;
			SqlCommand cmd = null;
			try {
				cn = new SqlConnection(this.ConnectionString);
				cmd = new SqlCommand("usp_select_BOM_for_Page", cn);
				cmd.CommandType = CommandType.StoredProcedure;
				cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
				cn.Open();
				DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
				if (reader.Read()) {
					//return GetBOMFromReader(reader);
                    BOMDetails obj = new BOMDetails();
					obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);
					obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
					obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.BOMStatus = GetReaderValue_String(reader, "Status", "");
                    obj.RequestToPOHubBy = GetReaderValue_Int32(reader, "RequestToPOHubBy", 0);
					obj.ClientName= GetReaderValue_String(reader, "ClientName", "");
                    return obj;
				} else {
					return null;
				}
			} catch (SqlException sqlex) {
				//LogException(sqlex);
				throw new Exception("Failed to get BOM", sqlex);
			} finally {
				cmd.Dispose();
				cn.Close();
				cn.Dispose();
			}
		}		
		
        /// <summary>
        /// Update BOM
		/// Calls [usp_update_BOM]
        /// </summary>
        public override bool Update(System.Int32? bomId, System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? currencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32? contact2Id)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;                
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@BOMCode", SqlDbType.NVarChar).Value = bomCode;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactId;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@CurrentSupplier", SqlDbType.VarChar).Value = currentSupplier;
                cmd.Parameters.Add("@QuoteRequired", SqlDbType.DateTime).Value = quoteRequired;
                cmd.Parameters.Add("@AS9120", SqlDbType.Bit).Value = AS9120;
                cmd.Parameters.Add("@Contact2Id", SqlDbType.Int).Value = contact2Id;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }		
		
        /// <summary>
        /// Update BOM
		/// Calls [usp_update_BOM_Delete]
        /// </summary>
        public override bool UpdateDelete(System.Int32? bomId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOM_Delete", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[001] code start
        /// <summary>
        /// AutoSearch 
        /// Calls [usp_autosearch_BOM]
        /// </summary>
        public override List<Rebound.GlobalTrader.DAL.BOMDetails> AutoSearch(System.Int32? clientId, System.String nameSearch)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_autosearch_BOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@NameSearch", SqlDbType.NVarChar).Value = nameSearch;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<Rebound.GlobalTrader.DAL.BOMDetails> lst = new List<Rebound.GlobalTrader.DAL.BOMDetails>();
                while (reader.Read())
                {
                    Rebound.GlobalTrader.DAL.BOMDetails obj = new Rebound.GlobalTrader.DAL.BOMDetails();
                    obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[001] code end		

        /// <summary>
        /// Update BOM
        /// Calls [usp_update_BOM_POHubQuote]
        /// </summary>
        public override bool UpdatePurchaseQuote(System.Int32? bomId, System.Int32? updatedBy, System.Int32? bomStatus, System.Int32 AssignUserNo,out System.String ValidateMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOM_POHubQuote", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@BOMStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@AssignUserNo", SqlDbType.Int).Value = AssignUserNo;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ValidateMessage", SqlDbType.VarChar,250).Direction = ParameterDirection.Output;
                cn.Open();
                //int ret = ExecuteNonQuery(cmd);
               // ExecuteNonQuery(cmd);
                ExecuteScalar(cmd);
                int ret = (int)(cmd.Parameters["@RowsAffected"].Value == null ? 0 : cmd.Parameters["@RowsAffected"].Value);
                ValidateMessage = (String)(cmd.Parameters["@ValidateMessage"].Value == null ? null : cmd.Parameters["@ValidateMessage"].Value);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[002] code start here
        /// <summary>
        /// GetListReadyForClient 
        /// Calls [usp_selectAll_BOM]
        /// </summary>
        public override List<BOMDetails> GetBomList(System.Int32? clientId, System.Boolean? isPoHUB, System.Int32? topToSelect, System.Int32? bomStatus, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_BOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 50;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TopToSelect", SqlDbType.Int).Value = topToSelect;
                cmd.Parameters.Add("@IsPoHUB", SqlDbType.Bit).Value = isPoHUB;
                cmd.Parameters.Add("@BomStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<BOMDetails> lst = new List<BOMDetails>();
                while (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.ClientName = GetReaderValue_String(reader, "ClientName", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.StatusValue = GetReaderValue_Int32(reader, "StatusValue", 0);
                    obj.RequestToPOHubBy = GetReaderValue_Int32(reader, "RequestToPOHubBy", 0);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.QuoteRequired = GetReaderValue_NullableDateTime(reader, "QuoteRequired", null);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        //[002] code end

        /// <summary>
        /// GetCSVListForBOM
        /// Calls [usp_selectAll_CSV_for_BOM]
        /// </summary>
        public override List<PDFDocumentDetails> GetCSVListForBOM(System.Int32? bomNo, System.String strType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_CSV_for_BOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMNo", SqlDbType.Int).Value = bomNo;
                cmd.Parameters.Add("@Type", SqlDbType.VarChar).Value = strType;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<PDFDocumentDetails> lstPDF = new List<PDFDocumentDetails>();
                while (reader.Read())
                {
                    PDFDocumentDetails obj = new PDFDocumentDetails();
                    obj.PDFDocumentId = GetReaderValue_Int32(reader, "BOMCSVId", 0);
                    obj.PDFDocumentRefNo = GetReaderValue_Int32(reader, "BOMNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.FileName = GetReaderValue_String(reader, "FileName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.Section = GetReaderValue_String(reader, "Section", "");

                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get CSV list for BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update BOM By PH
        /// Calls [usp_update_BOMByPH]
        /// </summary>
        public override bool UpdateBOMByPH(System.String bOMIdList, System.Int32? updatedBy,System.Int32? LoginId,System.Boolean? IsGroupAssignment, System.String Type)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOMByPH", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMIdList", SqlDbType.VarChar).Value = bOMIdList;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@IsGroupAssignment", SqlDbType.Bit).Value = IsGroupAssignment;
                cmd.Parameters.Add("@Type", SqlDbType.VarChar,200).Value = Type;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = LoginId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Delete
        /// Calls [usp_IpoBomCsvDelete]
        /// </summary>
        public override bool DeleteBomCsv(System.Int32? BomCSVId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_IpoBomCsvDelete", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomCSVId", SqlDbType.Int).Value = BomCSVId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete HUBRFQ csv", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// DropDownForClient 
        /// Calls [usp_GetUpdatedByListFromBOMIdList]
        /// </summary>
        public override BOMDetails GetUpdatedByListFromBOMIdList(System.String BOMIdList)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_GetUpdatedByListFromBOMIdList", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMIdList", SqlDbType.VarChar).Value = BOMIdList;
                cn.Open();
				DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
				if (reader.Read()) {
                    BOMDetails obj = new BOMDetails();
                    obj.UpdatedByList = GetReaderValue_String(reader, "UserIds", "");
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMs emails", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// DropDownForClient 
        /// Calls [usp_GetUpdatedByListFromBOMIdList]
        /// </summary>
        public override BOMDetails GetBomDetailsForAS6081AssignedToMe(System.String BOMIdList)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetBomDetailsForAssignedToMe", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@BOMIdList", SqlDbType.VarChar).Value = BOMIdList;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                if (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.BOMIds = GetReaderValue_String(reader, "BomId", "");
                    obj.BOMName = GetReaderValue_String(reader, "BomName", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMs emails", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override List<BOMDetails> GetRequirementDetailsForAS6081AssignedToMe(System.String ReqIdList)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetRequirementDetailsForRequester", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@CustReqList", SqlDbType.VarChar).Value = ReqIdList;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<BOMDetails> lstRequester = new List<BOMDetails>();
                while (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.BOMIds = GetReaderValue_String(reader, "BOMId", "");
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.CustomerRequirementId = GetReaderValue_Int32(reader, "CustomerRequirementId", 0);
                    obj.CustomerRequirementNo = GetReaderValue_Int32(reader, "CustomerRequirementNumber", 0);
                    obj.Part= GetReaderValue_String(reader, "FullPart", "");
                    obj.Manufacturer = GetReaderValue_String(reader, "ManufacturerCode", "");
                    obj.Quantity= GetReaderValue_Int32(reader, "Quantity", 0);
                    obj.TargetSellPrice = GetReaderValue_Double(reader, "TargetSellPrice", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.QuoteRequired = GetReaderValue_DateTime(reader, "QuoteRequired", DateTime.MinValue);
                    obj.CurrencyCode = GetReaderValue_String(reader, "CurrencyCode", "");
                    lstRequester.Add(obj);
                    obj = null;
                }
                return lstRequester;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMs emails", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// DropDownForClient 
        /// Calls [usp_GetUpdatedByListFromBOMIdList]
        /// </summary>
        public override List<BOMDetails> GetBomDetailsForRequester(System.String BOMIdList)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetBomDetailsForRequester", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@BOMIdList", SqlDbType.VarChar).Value = BOMIdList;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<BOMDetails> lstRequester = new List<BOMDetails>();
                while (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.BOMIds = GetReaderValue_String(reader, "BomIds", "");
                    obj.BOMName = GetReaderValue_String(reader, "BomNames", "");
                    obj.RequesterId = GetReaderValue_Int32(reader, "RequestToPOHubBy", 0);
                    obj.Requestedby = GetReaderValue_String(reader, "EmployeeName", "");
                    lstRequester.Add(obj);
                }
                return lstRequester;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get requester data emails", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// DropDownForClient 
        /// Calls [usp_GetUpdatedByListFromBOMIdList]
        /// </summary>
        public override List<BOMDetails> GetRequirementDetailsForRequestedUser(System.String ReqIdList)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetRequirementDetailsForRequestedUser", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ReqIdList", SqlDbType.VarChar).Value = ReqIdList;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<BOMDetails> lstRequester = new List<BOMDetails>();
                while (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.RequesterId = GetReaderValue_Int32(reader, "RequestToPOHubBy", 0);
                    obj.Requestedby = GetReaderValue_String(reader, "EmployeeName", "");
                    lstRequester.Add(obj);
                }
                return lstRequester;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get requester data emails", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// DropDownForClient 
        /// Calls [usp_GetUpdatedByListFromBOMIdList]
        /// </summary>
        public override BOMDetails GetSecurityUserDetails(System.Int32 GroupId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_AS6081_GetSecurityUserDetails", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@SecurityGroupId", SqlDbType.Int).Value = GroupId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                if (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.UpdatedByList = GetReaderValue_String(reader, "UserIds", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMs emails", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update BOM Status to Close
        /// Calls [usp_update_BOMStatusToClosed]
        /// </summary>
        public override bool UpdateBOMStatusToClosed(System.Int32? bomId, System.Int32? updatedBy, System.Int32? bomStatus)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_BOMStatusToClosed", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@BOMStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM Status", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetIDByNumber 
        /// Calls [usp_select_BOM_ID_by_Name]
        /// </summary>GetIDByNumber
        public override BOMDetails GetIDByNumber(System.String bomName, System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_BOM_ID_by_Name", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BomName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                   
                    BOMDetails obj = new BOMDetails();
                    obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                LogExceptionAzure(sqlex);
                throw new Exception("Failed to get HUBRFQ", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        // [001] code start
        /// <summary>
        /// Get Stock Image
        /// Calls [usp_selectAll_Image_for_QuoteToClient]
        /// </summary>
        public override List<StockImageDetails> GetImageListForReq(System.Int32? sourcingResultNo, System.String fileType)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_Image_for_QuoteToClient", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SourcingResultNo", SqlDbType.Int).Value = sourcingResultNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<StockImageDetails> lstPDF = new List<StockImageDetails>();
                while (reader.Read())
                {
                    StockImageDetails obj = new StockImageDetails();
                    obj.StockImageId = GetReaderValue_Int32(reader, "SourcingImageId", 0);
                    obj.ImageDocumentRefNo = GetReaderValue_Int32(reader, "SourcingResultNo", 0);
                    obj.Caption = GetReaderValue_String(reader, "Caption", "");
                    obj.ImageName = GetReaderValue_String(reader, "ImageName", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.UpdatedByName = GetReaderValue_String(reader, "ImageName", "");
                    lstPDF.Add(obj);
                    obj = null;
                }
                return lstPDF;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get Image list", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[004] start
        public override DataTable DataListNugget_Export(System.Int32? clientId, System.Int32? teamId, System.Int32? divisionId, System.Int32? loginId, System.Int32? orderBy, System.Int32? sortDir, System.String bomCode, System.String bomName, System.Boolean? isPOHub, System.Int32? selectedClientNo, int? bomStatus, int? IsAssignToMe, int? assignedUser, System.Int32? intDivision, System.Int32? salesPerson,System.DateTime? startdate, System.DateTime? enddate, System.Int32? CompanyTypeid, System.Int32? AS6081Required = 0, System.Int32? SelectedLoginId = null
)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                if (isPOHub == true)
                {
                    if (IsAssignToMe == 0)
                    {
                        cmd = new SqlCommand("usp_datalistnugget_PHBOM_Export", cn);
                    }
                    if (IsAssignToMe == 1)
                    {
                        cmd = new SqlCommand("usp_datalistnugget_PHBOMAssign_Export", cn);
                    }
                }

                else
                {
                    cmd = new SqlCommand("usp_datalistnugget_BOM_Export", cn);
                }
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@TeamId", SqlDbType.Int).Value = teamId;
                cmd.Parameters.Add("@DivisionId", SqlDbType.Int).Value = divisionId;
                cmd.Parameters.Add("@LoginId", SqlDbType.Int).Value = loginId;
                cmd.Parameters.Add("@OrderBy", SqlDbType.Int).Value = orderBy;
                cmd.Parameters.Add("@SortDir", SqlDbType.Int).Value = sortDir;
                cmd.Parameters.Add("@BOMCode", SqlDbType.NVarChar).Value = bomCode;
                //cmd.Parameters.Add("@CodeHi", SqlDbType.NVarChar).Value = CodeHi;
                cmd.Parameters.Add("@BOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@IsPoHub", SqlDbType.Bit).Value = isPOHub;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = selectedClientNo;
                cmd.Parameters.Add("@BomStatus", SqlDbType.Int).Value = bomStatus;
                cmd.Parameters.Add("@SalesPerson", SqlDbType.NVarChar).Value = salesPerson;//[003]
                cmd.Parameters.Add("@StartDate", SqlDbType.DateTime).Value = startdate;
                cmd.Parameters.Add("@EndDate", SqlDbType.DateTime).Value = enddate;//[003]
               
                if (assignedUser > 0)
                {
                    cmd.Parameters.Add("@AssignedUser", SqlDbType.Int).Value = assignedUser;
                }
               cmd.Parameters.Add("@ClientDivisionNo", SqlDbType.Int).Value = intDivision;
                // [008] CODE START -- DEVENDRA SINGH 
               cmd.Parameters.Add("@CompanyTypeid", SqlDbType.NVarChar).Value = CompanyTypeid;
               cmd.Parameters.Add("@AS6081Required", SqlDbType.Int).Value = AS6081Required;
               cmd.Parameters.Add("@SelectedLoginId", SqlDbType.Int).Value = SelectedLoginId;
                //  [008] CODE END -- DEVENDRA SINGH

                cn.Open();

                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);

                return ds.Tables[0];
               
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[004] end
        public override DataTable DataListNugget_ExportToExcel(System.Int32? clientId, System.Int32? BOMId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_datalistnugget_BOM_ExportToExcel", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@ClientId", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BOMId;

                cn.Open();

                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);

                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[005]start
        /// <summary>
        /// DropDown 
        /// Calls [usp_dropdown_ClientBOMStatus]
        /// </summary>
        public override List<BOMDetails> GetDropDownBOMStatus(System.String strSection)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_dropdown_ClientBOMStatus", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<BOMDetails> lst = new List<BOMDetails>();
                while (reader.Read())
                {
                    BOMDetails obj = new BOMDetails();
                    obj.BOMStatusId = GetReaderValue_Int32(reader, "BOMStatusId", 0);
                    obj.BOMStatusName = GetReaderValue_String(reader, "BOMStatusName", "");
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOMStatus", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        //[005]end

        /// [006] start
        /// <summary>
        /// GetForPage 
        /// Calls [usp_select_ClientBOMImport_for_Page]
        /// </summary>
        public override BOMDetails GetForClientBOMPage(System.Int32? bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_ClientBOMImport_for_Page", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetBOMFromReader(reader);

                    BOMDetails obj = new BOMDetails();
                    obj.ClientBOMId = GetReaderValue_Int32(reader, "ClientBOMId", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.ClientBOMCode = GetReaderValue_String(reader, "ClientBOMCode", "");
                    obj.ClientBOMName = GetReaderValue_String(reader, "ClientBOMName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.Salesman = GetReaderValue_String(reader, "Salesman", "");
                    obj.BOMStatusId = GetReaderValue_Int32(reader, "Status", 0);
                    obj.ClosedDate = GetReaderValue_DateTime(reader, "ClosedDate", DateTime.MinValue);
                    obj.ImportDate = GetReaderValue_DateTime(reader, "ImportDate", DateTime.MinValue);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM Details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// BOM Details 
        /// Calls [usp_select_ClientBOMImport_Details]
        /// </summary>
        public override BOMDetails GetClientBOMDetails(System.Int32? bomId,Int32? LoginId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_ClientBOMImport_Details", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UserId", SqlDbType.Int).Value = LoginId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetBOMFromReader(reader);

                    BOMDetails obj = new BOMDetails();
                    obj.ClientBOMId = GetReaderValue_Int32(reader, "ClientBOMId", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.ClientBOMCode = GetReaderValue_String(reader, "ClientBOMCode", "");
                    obj.ClientBOMName = GetReaderValue_String(reader, "ClientBOMName", "");
                    obj.Notes = GetReaderValue_String(reader, "Notes", "");
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.ContactName = GetReaderValue_String(reader, "ContactName", "");
                    obj.CurrencyName = GetReaderValue_String(reader, "CurrencyCode", "");
                    obj.CompanyNo = GetReaderValue_Int32(reader, "CompanyNo", 0);
                    obj.ContactNo = GetReaderValue_Int32(reader, "ContactNo", 0);
                    obj.CurrencyNo = GetReaderValue_Int32(reader, "CurrencyNo", 0);
                    obj.Salesman = GetReaderValue_String(reader, "Salesman", "");
                    obj.SalesmanId = GetReaderValue_Int32(reader, "SalesmanId", 0);
                    obj.BOMStatus = GetReaderValue_String(reader, "BOMStatusName", "");
                    obj.ClosedDate = GetReaderValue_DateTime(reader, "ClosedDate", DateTime.MinValue);
                    obj.ImportDate = GetReaderValue_DateTime(reader, "ImportDate", DateTime.MinValue);
                    obj.Inactive = GetReaderValue_Boolean(reader, "Inactive", false);
                    obj.UpdatedBy = GetReaderValue_Int32(reader, "UpdatedBy", 0);
                    obj.DLUP = GetReaderValue_DateTime(reader, "DLUP", DateTime.MinValue);
                    obj.RecordsProcessed = GetReaderValue_Int32(reader, "RecordsProcessed", 0);//[007]
                    obj.RecordsRemaining = GetReaderValue_Int32(reader, "RecordsRemaining", 0);//[007]
                    obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);//[007]
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");//[007]
                    obj.Status = GetReaderValue_Int32(reader, "Status",0);//[007]

                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM Details", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
       
        /// 
        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_ClientBOM]
        /// </summary>
        //public override Int32 Insert(System.Int32? clientId, System.String bomName, System.String notes, System.String bomCode, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Boolean? inactive, System.Int32? UpdateRequirement, System.Int32? Status, System.Int32? CurrencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32 AssignUserNo,out System.String ValidationMessage)
        public override Int32 InsertClientBOM(System.Int32? clientId, System.String bomName, System.String notes, System.String bomCode, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Boolean? inactive, System.Int32? Status, System.Int32? CurrencyNo, System.String salesman, out System.String ValidationMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_ClientBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientId;
                cmd.Parameters.Add("@ClientBOMName", SqlDbType.NVarChar).Value = bomName;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@ClientBOMCode", SqlDbType.NVarChar).Value = bomCode;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactId;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@Status", SqlDbType.Int).Value = Status;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = CurrencyNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.VarChar).Value = salesman;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ValidationMessage", SqlDbType.VarChar, 200).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                ValidationMessage = null;
                if (ret == -1)
                {
                    ValidationMessage = (String)(cmd.Parameters["@ValidationMessage"].Value == null ? string.Empty : cmd.Parameters["@ValidationMessage"].Value); ;
                    return ret;
                }
                return (Int32)cmd.Parameters["@ClientBOMId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update BOM
        /// Calls [usp_update_ClientBOM]
        /// </summary>
        public override bool UpdateClientBOM(System.Int32? clientbomId, System.Int32? clientNo, System.String clientbomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? currencyNo, System.String salesman)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_ClientBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = clientbomId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@ClientBOMName", SqlDbType.NVarChar).Value = clientbomName;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@ClientBOMCode", SqlDbType.NVarChar).Value = bomCode;
                cmd.Parameters.Add("@Inactive", SqlDbType.Bit).Value = inactive;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@CompanyNo", SqlDbType.Int).Value = companyId;
                cmd.Parameters.Add("@ContactNo", SqlDbType.Int).Value = contactId;
                cmd.Parameters.Add("@CurrencyNo", SqlDbType.Int).Value = currencyNo;
                cmd.Parameters.Add("@Salesman", SqlDbType.VarChar).Value = salesman;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// [006] end

        /// <summary>
        /// Update Client BOM Status to Complete
        /// Calls [usp_complete_ClientBOM]
        /// </summary>
        public override bool ClientBOMMarkComplete(System.Int32? ClientBOMId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_complete_ClientBOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = ClientBOMId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update Client BOM Status", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Update Client BOM Status to Complete
        /// Calls [usp_complete_ClientBOM]
        /// </summary>
        public override bool SaveAsHUBRFQ(System.Int32? ClientBOMId, System.Int32? updatedBy,out string errorMessage)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                errorMessage = string.Empty;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_ClientBom_SaveAsHUBRFQ", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientBOMId", SqlDbType.Int).Value = ClientBOMId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@ValidationMessage", SqlDbType.VarChar,200).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                
                errorMessage = (String)(cmd.Parameters["@ValidationMessage"].Value == null ? string.Empty : cmd.Parameters["@ValidationMessage"].Value); 
                
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to save the hubrfq detail", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Update Client BOM Status to Complete
        /// Calls [usp_update_SourcingApproval]
        /// </summary>
        public override bool UpdateApprovalStatus(System.Int32? bomId, System.Int32? sourcingNo, System.String strStatus, System.Int32? updatedBy, out string message, out string approvedStatus)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                message = string.Empty;
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_SourcingApproval", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BomId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@SourcingResultNo", SqlDbType.Int).Value = sourcingNo;
                cmd.Parameters.Add("@ApprovalStatus", SqlDbType.NVarChar).Value = strStatus;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@Message", SqlDbType.VarChar, 200).Direction = ParameterDirection.Output;
                cmd.Parameters.Add("@ApprovedStatus", SqlDbType.VarChar, 200).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);

                message = (String)(cmd.Parameters["@Message"].Value == null ? string.Empty : cmd.Parameters["@Message"].Value);
                approvedStatus = (String)(cmd.Parameters["@ApprovedStatus"].Value == null ? string.Empty : cmd.Parameters["@ApprovedStatus"].Value);

                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update the quote to client", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        ////////////////////////////PVV Answers
        /// <summary>
        /// Update BOM
		/// Calls [usp_update_BOM]
        /// </summary>
        public override bool UpdatePVV(System.Int32? bomId, System.Int32? clientNo, System.String bomName, System.String notes, System.String bomCode, System.Boolean? inactive, System.Int32? updatedBy, System.Int32? companyId, System.Int32? contactId, System.Int32? currencyNo, System.String currentSupplier, System.DateTime? quoteRequired, System.Boolean? AS9120, System.Int32? contact2Id)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_InsertUpdate_PVVAnswer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert or update PVV Answer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool UpdatePVV(string generatedBOMId, System.Int32? bomId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_InsertUpdate_PVVAnswer_From_TempPVV", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@BomIdGenerated", SqlDbType.NVarChar).Value = generatedBOMId;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert or update PVV Answer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        public override bool UpdatePVVTemp(System.Int32? clientNo, System.String notes, System.Int32? updatedBy, System.String generatedId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_InsertUpdate_Temp_PVVAnswer", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMGeneratedId", SqlDbType.NVarChar).Value = generatedId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@Notes", SqlDbType.NVarChar).Value = notes;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert or update PVV Answer", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Get 
        /// Calls [usp_select_BOM]
        /// </summary>
        public override BOMDetails GetPVV(System.Int32? BOMId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_BOM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BOMId;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetBOMFromReader(reader);
                    Rebound.GlobalTrader.DAL.BOMDetails obj = new Rebound.GlobalTrader.DAL.BOMDetails();
                    obj.BOMId = GetReaderValue_Int32(reader, "BOMId", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.BOMName = GetReaderValue_String(reader, "BOMName", "");
                    obj.BOMCode = GetReaderValue_String(reader, "BOMCode", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.CompanyNo = GetReaderValue_Int32(reader, "companyId", 0);
                    obj.CompanyName = GetReaderValue_String(reader, "CompanyName", "");
                    obj.CompanyType = GetReaderValue_String(reader, "CompanyType", "");
                    obj.ContactNo = GetReaderValue_Int32(reader, "contactId", 0);
                    obj.ContactName = GetReaderValue_String(reader, "contactName", "");
                    
                    //////
                    obj.PVVQuestionId = GetReaderValue_Int32(reader, "PVVQuestionId", 0);
                    obj.PVVQuestionName = GetReaderValue_String(reader, "PVVQuestionName", "");
                    obj.PVVAnswerId = GetReaderValue_Int32(reader, "PVVAnswerId", 0);
                    obj.PVVAnswerName = GetReaderValue_String(reader, "PVVAnswerName", "");
                    obj.HUBRFQNo = GetReaderValue_String(reader, "HUBRFQNo", "");

                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update BOM
        /// Calls [usp_update_BOM_Delete]
        /// </summary>
        public override bool PVVDelete(System.Int32? bomId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_PVVBPM_Delete", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = bomId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update BOM", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool HasImportFile(int BOMId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_check_BOMHasImportFile", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BOMId;
                cmd.Parameters.Add("@HasImportFile", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return  (int)cmd.Parameters["@HasImportFile"].Value > 0;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to check BOM has import file", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override DataTable GetUpdateBOMData(int BOMId)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_get_UpdateBomData", cn);

                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 60;
                cmd.Parameters.Add("@BOMId", SqlDbType.Int).Value = BOMId;

                cn.Open();

                DataSet ds = new DataSet();
                SqlDataAdapter da = new SqlDataAdapter(cmd);
                da.Fill(ds);

                return ds.Tables[0];

            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to get Update BOM import data", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        public override bool PVVDeleteTemp(string generatedId, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_Delete_Temp_PVVBPM", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@GeneratedId", SqlDbType.NVarChar).Value = generatedId;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update PPV", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        ///////////////////////////////
    }
}
