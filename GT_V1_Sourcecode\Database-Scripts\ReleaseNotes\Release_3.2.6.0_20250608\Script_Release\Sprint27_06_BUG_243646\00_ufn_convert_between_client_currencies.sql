﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/*       
===========================================================================================      
TASK			UPDATED BY		DATE			ACTION      DESCRIPTION     
[BUG-243646]	AnTran			08-May-2025		Create		Convert value between client base currencies
===========================================================================================      
*/
CREATE OR ALTER FUNCTION [dbo].[ufn_convert_between_client_currencies] (
      @ValueToBeConverted float
    , @FromClientId int = null
    , @ToClientId int = null
    , @ExchangeRateDate datetime = NULL
    )
--
RETURNS float
--
AS BEGIN
	IF(@FromClientId IS NULL OR @ToClientId IS NULL) 
		RETURN @ValueToBeConverted;

	DECLARE @ClientCurrencyNo INT = NULL;

	SELECT @ClientCurrencyNo = l.SupplierCurrencyNo
	FROM tbClient fc WITH(NOLOCK)
	JOIN tbCurrency fcu WITH(NOLOCK) ON fcu.CurrencyId = fc.CurrencyNo
	LEFT JOIN tbLinkMultiCurrency l WITH(NOLOCK) 
		ON l.GlobalCurrencyNo = fcu.GlobalCurrencyNo
			AND l.ClientNo = @ToClientId
	WHERE fc.ClientId = @FromClientId;

	RETURN @ValueToBeConverted / dbo.ufn_get_exchange_rate(@ClientCurrencyNo, @ExchangeRateDate);
END

GO
