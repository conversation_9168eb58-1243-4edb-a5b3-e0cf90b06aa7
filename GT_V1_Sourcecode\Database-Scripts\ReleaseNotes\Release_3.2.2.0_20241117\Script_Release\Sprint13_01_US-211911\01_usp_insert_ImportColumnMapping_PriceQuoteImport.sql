﻿SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO
/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-211911]		An.TranTan		16-Oct-2024		CREATE		Delete user's old records and insert mapping setting specific for user
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_insert_ImportColumnMapping_PriceQuoteImport]                    
	@strInsertMappings nvarchar(500),
	@UserId INT = NULL
AS

BEGIN
SET NOCOUNT ON;
--delete old records
DELETE BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote
WHERE CreatedBy = @UserId;

DECLARE @sql NVARCHAR(max);                      
SET @sql = 'insert into BorisGlobalTraderimports.dbo.tbImportMapping_PriceQuote (      
    [SupplierNo], [RequirementColumn],[AlternatePartColumn], [ManufacturerColumn], [PartColumn], [QuantityColumn], [PriceColumn], [DateCodeColumn],      
    [ProductColumn], [PackageColumn], [CurrencyColumn], [FixedCurrencyColumn],      
    [FileType], [DescriptionColumn], [SupplierNameColumn], [SupplierPartColumn], [ROHSColumn],      
    [SPQColumn], [MOQColumn], [QtyInStockColumn], [LeadTimeColumn], [OfferStatusColumn], [FactorySealedColumn], [Region]  
 ,[Last_Time_Buy],[Buy_Price],[Sell_PRICE],[Shipping_Cost],[DeliveryDateColumn],[MSLColumn],[ClientNoColumn], [ClientID], [CreatedBy]      
      
) values (' + @strInsertMappings+')'                      
EXEC sp_executesql  @sql, N'@strInsertMappings nvarchar(500)',                      
	@strInsertMappings = @strInsertMappings

SET NOCOUNT OFF;
END
GO


