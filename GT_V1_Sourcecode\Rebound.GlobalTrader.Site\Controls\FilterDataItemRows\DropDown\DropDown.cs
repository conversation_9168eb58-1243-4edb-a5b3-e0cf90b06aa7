//Marker     Changed by      Date               Remarks
//[001]      Vinay           03/07/2013         CR:- Supplier Invoice
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;
using Rebound.GlobalTrader.Site.Controls.Nuggets;
using System.Reflection;

namespace Rebound.GlobalTrader.Site.Controls.FilterDataItemRows {
	[DefaultProperty("")]
	[ToolboxData("<{0}:DropDown runat=server></{0}:DropDown>")]
	public class DropDown : Base {


		#region Properties

		private Unit _untDropDownWidth;
		public Unit DropDownWidth {
			get { return _untDropDownWidth; }
			set { _untDropDownWidth = value; }
		}

		private string _strDropDownAssembly;
		public string DropDownAssembly {
			get { return _strDropDownAssembly; }
			set { _strDropDownAssembly = value; }
		}

		private string _strDropDownType;
		public string DropDownType {
			get { return _strDropDownType; }
			set { _strDropDownType = value; }
		}

		protected DropDowns.Base _ddl;
		public DropDowns.Base DropDownControl {
			get { return _ddl; }
		}
        //[001] code start
        private bool _blnIncludeNoValue = true;
        public bool IncludeNoValue
        {
            get { return _blnIncludeNoValue; }
            set { _blnIncludeNoValue = value; }
        }
		//[001] code end

		//[GTDP-303]
		private bool _limitToCurrentUsersTeam = false;
		public bool LimitToCurrentUsersTeam
		{
			get { return _limitToCurrentUsersTeam; }
			set { _limitToCurrentUsersTeam = value; }
		}
		private bool _limitToCurrentUsersDivision = false;
		public bool LimitToCurrentUsersDivision {
			get { return _limitToCurrentUsersDivision; }
			set { _limitToCurrentUsersDivision = value; }
		}
		private bool _excludeCurrentUser = false;
		public bool ExcludeCurrentUser {
			get { return _excludeCurrentUser; }
			set { _excludeCurrentUser = value; }
		}


		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			FieldType = Type.DropDown;
			base.OnInit(e);
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			AddScriptReference(Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.FilterDataItemRows.DropDown.DropDown.js", true));
		}

		/// <summary>
		/// OnLoad
		/// </summary>
		/// <param name="e"></param>
		protected override void OnLoad(EventArgs e) {
			EnsureChildControls();
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.FilterDataItemRows.DropDown", ClientID);
			_scScriptControlDescriptor.AddComponentProperty("ddl", _ddl.ClientID);
			base.OnLoad(e);
		}

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			base.CreateChildControls();

			//dropdown
			_ddl = DropDownManager.GetDropDown(_strDropDownAssembly, _strDropDownType);
            //[001] code start
            _ddl.IncludeNoValue = _blnIncludeNoValue;
            //[001] code end
			_ddl.CanAddTo = false;
			_ddl.CanRefresh = true;
			/// code to set the property for the salesman loading
			/// [GTDP-303]
			if(_ddl.GetType() == typeof(Rebound.GlobalTrader.Site.Controls.DropDowns.Employee))
            {
				((Rebound.GlobalTrader.Site.Controls.DropDowns.Employee)_ddl).LimitToCurrentUsersDivision = this.LimitToCurrentUsersDivision;
				((Rebound.GlobalTrader.Site.Controls.DropDowns.Employee)_ddl).LimitToCurrentUsersTeam = this.LimitToCurrentUsersTeam;
				((Rebound.GlobalTrader.Site.Controls.DropDowns.Employee)_ddl).ExcludeCurrentUser = this.ExcludeCurrentUser;
			}
			_ddl.ID = "ddl";
			_lblField.Controls.Add(_ddl);
		}

		public override void SetDefaultValue() {
			if (DefaultValue == null) DefaultValue = "";
			SetInitialValue(DefaultValue);
			base.SetDefaultValue();
		}

		public override void Reset() {
			EnsureChildControls();
			_ddl.InitialValue = _ddl.NoValue_Value;
			Enable(false);
			base.Reset();
		}

		#endregion

		#region Methods

		public void SetInitialValue(object objValue) {
			EnsureChildControls();
			_ddl.InitialValue = objValue.ToString();
			Enable(_ddl.InitialValue != _ddl.NoValue_Value && objValue.ToString() != "");
		}

		#endregion

	}

}