﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_selectAll_SourcingResult_for_BOMCustomerRequirement]                                                                             
@CustomerRequirementId int ,                                                                                          
@IsPoHub bit  = 0                                                                                                           
AS          
--********************************************************************************************                
--* Action: Altered  By: Abhinav Saxena  Date: 06-09-2023 Comment: For RP-2228 (AS6081).       
--* Action: Altered  By: Abhinav <PERSON>xena  Date: 20-09-2023 Comment: For RP-2228 (AS6081).     
-- [001] Soorya Vyas RP-2440  10/OCT/2023  Remove date filter for sourcing result from  HUBRFQ only   
--********************************************************************************************             
BEGIN                                                                                                    
DECLARE @BOMCurrencyNo INT                                                                                                  
DECLARE @BOMCurrencyCode NVARCHAR(5)                                                                                                  
                                                
DECLARE @ClientNo int                                                
SET @ClientNo=(SELECT Clientno from tbCustomerRequirement where CustomerRequirementId=@CustomerRequirementId)                   
                
Declare @SourcingTable varchar(100)                  
SET @SourcingTable=(select top 1 SourcingTable from tbSourcingResult                 
where CustomerRequirementNo = @CustomerRequirementId and clientNo=@ClientNo)                
                                                
--SELECT top 1 @BOMCurrencyNo = b.CurrencyNo,@BOMCurrencyCode = cu.CurrencyCode FROM   tbCustomerRequirement c                                                                                                   
--JOIN tbBOM b  on c.BOMNo = b.BOMId                                                                                                   
--LEFT JOIN    dbo.tbCurrency cu ON b.CurrencyNo = cu.CurrencyId                                                                                                         
--where c.CustomerRequirementId = @CustomerRequirementId                     
                
if(@ClientNo=114 And @SourcingTable='HUBSTK')                
Begin                
 Set @IsPoHub=0;                
End                
                
DECLARE @FROMDATE DATETIME                                                
DECLARE @ENDDATE DATETIME                        
SET @FROMDATE=dbo.ufn_get_date_from_datetime(DATEADD(month,-6,getdate()))                                                
SET @ENDDATE =dbo.ufn_get_date_from_datetime(getdate())                                                
                                        
SELECT top 100 sr.SourcingResultId                                                                                                              
 , sr.CustomerRequirementNo                                                                                                              
 , sr.SourcingTable                                                                                                              
 , sr.SourcingTableItemNo                                                                                                              
 , sr.TypeName                                                                                                              
 , sr.FullPart                                                                                                              
 , sr.Part                                                                                                              
 , sr.ManufacturerNo      
 , sr.DateCode                                   
 , sr.ProductNo                        
 , sr.PackageNo                                                                                                             
 , sr.Quantity                                         
 , CASE WHEN sr.PartWatchMatch=1 AND sr.POHubCompanyNo IS NOT NULL AND @ClientNo!=114 AND @IsPoHub =0 THEN 0 ELSE  sr.Price  END Price                                                                                                            
 , sr.CurrencyNo                                                                                                              
 , sr.OriginalEntryDate                         
 , sr.Salesman                       
 , sr.SupplierNo                                          
 , sr.UpdatedBy                                               
 , sr.DLUP                                                                                                              
 , sr.ROHS                                            
 , sr.OfferStatusNo                                                       
 , sr.OfferStatusChangeDate                                                                                                  
 , sr.OfferStatusChangeLoginNo                                          
 , sr.Notes                                             
 , mf.ManufacturerName                                                           
 , mf.ManufacturerCode                                                                 
 , cu.CurrencyCode                                                               
 , CASE WHEN coc.CompanyName IS NOT NULL and @ClientNo!=114 AND @IsPoHub =0 THEN coc.CompanyName ELSE  co.CompanyName end AS SupplierName   -- soorya                                                       
 , ct.Name AS SupplierType                                                                                                            
 , pr.ProductName                                                                                                              
 , pr.ProductDescription                                                                                                   
 , pk.PackageName                                                                                                              
 , pk.PackageDescription                                                                   
 , lg.EmployeeName AS SalesmanName                                                                                                              
 , l2.EmployeeName AS OfferStatusChangeEmployeeName                                                      
 , sr.SupplierPrice                                                                   
 , sr.POHubCompanyNo                                                                                                          
 , cop.CompanyName AS POHubSupplierName                                                                                                         
 , cr.POHubReleaseBy                                                                                        
 , sr.ClientCompanyNo                                                              
 , coc.CompanyName AS ClientSupplierName                                                                               
 , cop.UPLiftPrice                                                                                               
 , sr.ClientCurrencyNo                                                                                                     
 , cur.CurrencyCode as ClientCurrencyCode                                                                                                     
 --, dbo.ufn_convert_currency_value(sr.Price, sr.CurrencyNo, @BOMCurrencyNo, sr.DLUP) AS ConvertedSourcingPrice                                                                                                
 , dbo.ufn_get_spq_sourcingResult(sr.SourcingTableItemNo,sr.SourcingTable) as MslSpqFactorySealed                                                                                                
 , sr.EstimatedShippingCost                       
 , sr.SupplierManufacturerName                                                                     
 , sr.SupplierDateCode                                   
 , sr.SupplierPackageType                                                                               
 , sr.SupplierProductType                                             
 , sr.SupplierMOQ                                                                                      
 , sr.SupplierTotalQSA                                                                          
 , sr.SupplierLTB                                                                                    
 , sr.SupplierNotes                                                                                     
 , sr.DeliveryDate                                                                                    
 , sr.IsReleased as SourcingReleased                                  
 , sr.SPQ                                                                                  
 , sr.LeadTime                                    
 ,sr.ROHSStatus                                                                                  
 ,sr.FactorySealed                                                                                  
 ,sr.MSL                                                                                  
,sr.SupplierMOQ                                                                               
  ,cr.ClientNo                                    
  ,Region.RegionName                                                                           
  ,sr.Closed                                                                          
  ,sr.IsSoCreated                                                                         
  ,tm.TermsName                                                       
  ,tm.IsApplyPOBankFee                                                                        
  ,sr.SourceRef                                                     
  , sr.ActualPrice as BuyPrice                                                                        
  , cub.CurrencyCode as BuyCurrencyCode                                           
  , sr.ActualCurrencyNo                                                                       
  ,(SELECT COUNT(1) FROM tbSourcingResult where ISNULL(IsReleased,0)=1 and CustomerRequirementNo = @CustomerRequirementId ) as SourcingReleasedCount                                                                       
  , sr.MSLLevelNo                                                   
  , ml.MSLLevel as MSLLevelText                                                                      
  , isnull(sr.supplierWarranty,0) AS SupplierWarranty                                                               
  , cast(isnull(sr.TestRecommended,0) as bit) AS IsTestingRecommended                                         
  , sr.IsImageAvailable                                                               
   , sr.PriorityNo                                                                
   --, sr.ApprovedBy                                                            
  -- , lgapp.EmployeeName as ApprovedByName                                                            
  -- , sr.ApprovalSent                                                          
    ,cr.CountryOfOriginNo as IHSCountryOfOriginNo                                            
 ,cro.GlobalCountryName  as IHSCountryOfOriginName                                                           
 ,sr.IHSCountryOfOriginNo  as CountryOfOriginNo                                                         
 ,coo.GlobalCountryName  as CountryOfOriginName                                                     
 --,sr.ReReleased                                                      
 , 1 as  ReReleased                                        
 ,(select rh.Description from tbROHSStatus rh where rh.ROHSStatusId=sr.ROHS) as ROHSDescription           
 ,sr.PartWatchMatchHUBIPO     ,                        
 --case when sr.PartWatchMatchHUBIPO =1 then cl.ClientCode else '' end as 'SourceClient',                        
 --case when sr.PartWatchMatchHUBIPO =1 then sr.ClientNo else 0 end as 'SourceClientNo',                        
 cl.ClientCode   as 'SourceClient',                        
 sr.ClientNo  as 'SourceClientNo',                        
 case when sr.OfferStatusChangeDate is null then OriginalEntryDate else sr.OfferStatusChangeDate end as OfferDate ,                      
 sr.PartWatchMatch AS  IsPartWatchMatchClient           
 , ISNULL(cr.AS6081,0) AS ISAS6081Required         
 , ISNULL(tos.[Name],'') AS TypeOfSupplierName        
 , ISNULL(rfc.[Name],'') AS ReasonForSupplierName         
 , ISNULL(ros.[Name],'') AS RiskOfSupplierName       
 , dbo.ufn_AS6081_GetAssigneeDetails(cr.CustomerRequirementId) AS AssigneeId                   
FROM dbo.tbSourcingResult sr                                                                                                              
LEFT JOIN dbo.tbManufacturer mf ON sr.ManufacturerNo   = mf.ManufacturerID                                                                                             
LEFT JOIN dbo.tbCurrency cu ON sr.CurrencyNo    = cu.CurrencyID                                                                                                    
LEFT JOIN dbo.tbCompany co ON sr.SupplierNo    = co.CompanyId                                                                                                              
LEFT JOIN dbo.tbProduct pr ON sr.ProductNo    = pr.ProductId                                                        
LEFT JOIN dbo.tbPackage pk ON sr.PackageNo    = pk.PackageId                          
LEFT JOIN dbo.tbLogin lg ON sr.Salesman     = lg.LoginId                                                                                     
LEFT JOIN dbo.tbLogin l2 ON sr.OfferStatusChangeLoginNo = l2.LoginId                               
LEFT JOIN dbo.tbCompany cop ON sr.POHubCompanyNo    = cop.CompanyId                                                                                                 
LEFT JOIN dbo.tbCompany coc ON sr.ClientCompanyNo    = coc.CompanyId                   
LEFT JOIN  dbo.tbCustomerRequirement cr on cr.CustomerRequirementId = sr.CustomerRequirementNo                                                                                                         
LEFT JOIN tbCurrency cur on isnull(sr.ClientCurrencyNo,0) = cur.CurrencyId                                                                                      
LEFT JOIN tbCompanyType ct on ct.CompanyTypeId = cop.TypeNo                                                                                                  
Left Join tbregion Region on Region.regionId=sr.regionNo                                                                            
left Join tbCompany c on sr.POHubCompanyNo=  c.CompanyId                                                                          
left join tbTerms tm on c.POTermsNo=tm.TermsId                                                                        
LEFT JOIN tbCurrency cub on isnull(sr.ActualCurrencyNo,0) = cub.CurrencyId                                                                          
LEFT JOIN tbMSLLevel ml on ml.MSLLevelId = sr.MSLLevelNo                                                                
--LEFT JOIN tbLogin lgapp on sr.ApprovedBy = lgapp.LoginId                                                 
left join  tbGlobalCountryList coo on sr.IHSCountryOfOriginNo=coo.GlobalCountryId                                                      
left join  tbGlobalCountryList cro on cr.CountryOfOriginNo=cro.GlobalCountryId                         
left join tbClient cl on cl.ClientId = sr.clientno        
left join tbAS6081_TypeOfSupplier tos ON sr.TypeOfSupplierNo=tos.TypeOfSupplierId        
left join tbAS6081_ReasonForChosenSupplier rfc ON sr.ReasonForSupplierNo=rfc.ReasonForChosenSupplierId        
left join tbAS6081_RiskOfSupplier ros ON sr.RiskOfSupplierNo=ros.RiskOfSupplierId                     
WHERE sr.CustomerRequirementNo = @CustomerRequirementId                                                   
AND(sr.PartWatchMatch=1  OR sr.PartWatchMatchHUBIPO=1 Or ((@IsPoHub = 0 AND (sr.IsReleased=1 OR sr.POHubCompanyNo IS NULL))  OR (@IsPoHub =1 AND NOT sr.POHubCompanyNo IS NULL )))                       
AND (@IsPoHub = 0 OR (@IsPoHub = 1 AND ISNULL(sr.IsRemoveFromHub,0)=0))                                            
--AND (dbo.ufn_get_date_from_datetime(ISNULL(sr.OfferStatusChangeDate, sr.OriginalEntryDate)) between @FROMDATE   AND  @ENDDATE)  -- [001]             
Order by OfferDate  desc              
END   
GO


