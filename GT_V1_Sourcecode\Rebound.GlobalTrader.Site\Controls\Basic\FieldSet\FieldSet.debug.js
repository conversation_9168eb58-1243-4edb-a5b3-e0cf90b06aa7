///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Data/Data.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - add full dispose
//
// RP 30.10.2009:
// - add functionity to show small loading symbol without the full "Loading" display
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.FieldSet = function(element) { 
	Rebound.GlobalTrader.Site.Controls.FieldSet.initializeBase(this, [element]);
	this._intCount = 0;
};

Rebound.GlobalTrader.Site.Controls.FieldSet.prototype = {

    get_pnlOuter: function() { return this._pnlOuter; }, set_pnlOuter: function(v) { if (this._pnlOuter !== v) this._pnlOuter = v; },
    get_lblCount: function() { return this._lblCount; }, set_lblCount: function(v) { if (this._lblCount !== v) this._lblCount = v; },
    get_hypShowHide: function() { return this._hypShowHide; }, set_hypShowHide: function(v) { if (this._hypShowHide !== v) this._hypShowHide = v; },
    get_blnIsRolledUp: function() { return this._blnIsRolledUp; }, set_blnIsRolledUp: function(v) { if (this._blnIsRolledUp !== v) this._blnIsRolledUp = v; },
    get_pnlContent: function() { return this._pnlContent; }, set_pnlContent: function(v) { if (this._pnlContent !== v) this._pnlContent = v; },
    get_pnlLoading: function() { return this._pnlLoading; }, set_pnlLoading: function(v) { if (this._pnlLoading !== v) this._pnlLoading = v; },
    get_pnlError: function() { return this._pnlError; }, set_pnlError: function(v) { if (this._pnlError !== v) this._pnlError = v; },
    get_lblError: function() { return this._lblError; }, set_lblError: function(v) { if (this._lblError !== v) this._lblError = v; },
    get_pnlNoData: function() { return this._pnlNoData; }, set_pnlNoData: function(v) { if (this._pnlNoData !== v) this._pnlNoData = v; },
    get_lblLoadingTop: function() { return this._lblLoadingTop; }, set_lblLoadingTop: function(v) { if (this._lblLoadingTop !== v) this._lblLoadingTop = v; },
    get_hypRefresh: function() { return this._hypRefresh; }, set_hypRefresh: function(v) { if (this._hypRefresh !== v) this._hypRefresh = v; },

    addShown: function(handler) { this.get_events().addHandler("Shown", handler); },
    removeShown: function(handler) { this.get_events().removeHandler("Shown", handler); },
    onShown: function() {
        var handler = this.get_events().getHandler("Shown");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    addRefresh: function(handler) { this.get_events().addHandler("Refresh", handler); },
    removeRefresh: function(handler) { this.get_events().removeHandler("Refresh", handler); },
    onRefresh: function() {
        var handler = this.get_events().getHandler("Refresh");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.FieldSet.callBaseMethod(this, "initialize");
        if (this._hypShowHide) $addHandler(this._hypShowHide, "click", Function.createDelegate(this, this.toggleRollUp));
        if (this._hypRefresh) $addHandler(this._hypRefresh, "click", Function.createDelegate(this, this.onRefresh));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this.get_element()) $clearHandlers(this.get_element());
        if (this._hypShowHide) $clearHandlers(this._hypShowHide);
        if (this._hypRefresh) $clearHandlers(this._hypRefresh);
        this._pnlOuter = null;
        this._lblCount = null;
        this._hypShowHide = null;
        this._pnlContent = null;
        this._pnlLoading = null;
        this._pnlError = null;
        this._lblError = null;
        this._pnlNoData = null;
        this._lblLoadingTop = null;
        this._hypRefresh = null;
        this._intCount = null;
        Rebound.GlobalTrader.Site.Controls.FieldSet.callBaseMethod(this, "dispose");
        this.isDisposed = true;
    },

    toggleRollUp: function() {
        this.rollUp(!this._blnIsRolledUp);
    },

    rollUp: function(bln) {
        this._blnIsRolledUp = bln;
        if (bln) {
            Sys.UI.DomElement.addCssClass(this._pnlOuter, "fieldSetCollapsed");
        } else {
            Sys.UI.DomElement.removeCssClass(this._pnlOuter, "fieldSetCollapsed");
            this.onShown();
        }
    },

    showContent: function(blnShow) {
        if (blnShow) {
            $R_FN.showElement(this._pnlContent, true);
            $R_FN.showElement(this._pnlError, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._lblLoadingTop, false);
            $R_FN.showElement(this._pnlNoData, false);
        } else {
            $R_FN.showElement(this._pnlContent, false);
        }
    },

    showLoading: function(blnShow) {
        if (blnShow) {
            $R_FN.showElement(this._lblLoadingTop, true);
            $R_FN.showElement(this._pnlLoading, true);
            $R_FN.showElement(this._pnlContent, false);
            $R_FN.showElement(this._pnlError, false);
            $R_FN.showElement(this._pnlNoData, false);
            this.resetCount();
        } else {
            $R_FN.showElement(this._lblLoadingTop, false);
            $R_FN.showElement(this._pnlLoading, false);
        }
    },

    showLoadingTop: function(blnShow) {
        $R_FN.showElement(this._lblLoadingTop, blnShow);
    },

    showError: function(blnShow, strMsg) {
        if (blnShow) {
            $R_FN.showElement(this._pnlError, true);
            $R_FN.showElement(this._pnlNoData, false);
            $R_FN.showElement(this._pnlContent, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._lblLoadingTop, false);
            $R_FN.setInnerHTML(this._lblError, strMsg);
        } else {
            $R_FN.showElement(this._pnlError, false);
            $R_FN.setInnerHTML(this._lblError, "");
        }
    },

    showNoData: function(blnShow, strMsg) {
        if (blnShow) {
            $R_FN.showElement(this._pnlNoData, true);
            $R_FN.showElement(this._pnlError, false);
            $R_FN.showElement(this._pnlContent, false);
            $R_FN.showElement(this._pnlLoading, false);
            $R_FN.showElement(this._lblLoadingTop, false);
            $R_FN.setInnerHTML(this._lblError, strMsg);
        } else {
            $R_FN.showElement(this._pnlError, false);
            $R_FN.setInnerHTML(this._lblError, "");
        }
    },
    showMessage: function(blnShow, msg) {
        $R_FN.showElement(this._pnlNoData, blnShow);
        $R_FN.setInnerHTML(this._pnlNoData, msg);
    },

    resetCount: function() {
        $R_FN.setInnerHTML(this._lblCount.innerHTML, "");
        $R_FN.showElement(this._lblCount, false);
    },

    updateCount: function(intCount, blnNoParentheses) {
        this._intCount = intCount;
        $R_FN.showElement(this._lblCount, true);
        var str = "&nbsp;";
        if (!blnNoParentheses) str += "(";
        str += intCount;
        if (!blnNoParentheses) str += ")";
        $R_FN.setInnerHTML(this._lblCount, str);
        if (intCount == 0) this.showNoData(true);
    },

    show: function(bln) {
        $R_FN.showElement(this._element, bln);
    }

};

Rebound.GlobalTrader.Site.Controls.FieldSet.registerClass("Rebound.GlobalTrader.Site.Controls.FieldSet", Sys.UI.Control, Sys.IDisposable);