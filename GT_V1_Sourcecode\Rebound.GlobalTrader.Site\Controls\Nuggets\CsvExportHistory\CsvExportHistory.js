Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory.initializeBase(this,[n]);this._intPOQuoteID=-1;this._intBOMID=-1};Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory.prototype={get_intBOMID:function(){return this._intBOMID},set_intBOMID:function(n){this._intBOMID!==n&&(this._intBOMID=n)},get_tblCreditHistory:function(){return this._tblCreditHistory},set_tblCreditHistory:function(n){this._tblCreditHistory!==n&&(this._tblCreditHistory=n)},get_intPOQuoteID:function(){return this._intPOQuoteID},set_intPOQuoteID:function(n){this._intPOQuoteID!==n&&(this._intPOQuoteID=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/CsvExportHistory";this._strDataObject="CsvExportHistory";this.addRefreshEvent(Function.createDelegate(this,this.getCreditHistory));this.showLoading(!1);this.showContent(!0);this.showContentLoading(!1);this.getCreditHistory()},dispose:function(){this.isDisposed||(this._tblCreditHistory&&this._tblCreditHistory.dispose(),this._tblCreditHistory=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory.callBaseMethod(this,"dispose"))},getCreditHistory:function(){this.showContent(!0);this.showCreditHistoryError(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetLog");n.addParameter("POQuoteID",this._intPOQuoteID);n.addParameter("BomID",this._intBOMID);n.addDataOK(Function.createDelegate(this,this.getCreditHistoryOK));n.addError(Function.createDelegate(this,this.getCreditHistoryError));n.addTimeout(Function.createDelegate(this,this.getCreditHistoryError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCreditHistoryOK:function(n){res=n._result;this.showLoadingCreditHistory(!1);this.showCreditHistoryError(!1);this._tblCreditHistory.clearTable();this.processCreditHistory(this._tblCreditHistory);this._tblCreditHistory.resizeColumns()},getCreditHistoryError:function(n){this.showLoadingCreditHistory(!1);this.showCreditHistoryError(!0,n.get_ErrorMessage())},showLoadingCreditHistory:function(n){$R_FN.showElement(this._pnlLoadingCreditHistory,n);$R_FN.showElement(this._pnlCreditHistory,!n);this.showLoading(n);n&&$R_FN.showElement(this._pnlGetCreditHistory,!1)},showCreditHistoryError:function(n,t){$R_FN.showElement(this._pnlCreditHistoryError,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlCreditHistory,!1),$R_FN.showElement(this._pnlGetCreditHistory,!1),$R_FN.setInnerHTML(this._pnlCreditHistoryError,t))},showCreditHistoryGetData:function(n){$R_FN.showElement(this._pnlGetCreditHistory,n);n&&(this.showLoading(!1),$R_FN.showElement(this._pnlLoadingCreditHistory,!1),$R_FN.showElement(this._pnlCreditHistory,!1),$R_FN.setInnerHTML(this._pnlCreditHistoryError,!1))},processCreditHistory:function(n){var i,t,r;if(res.CreditHist)for(i=0;i<res.CreditHist.length;i++)t=res.CreditHist[i],r=[t.Message,t.Date],n.addRow(r,t.ID,!1),t=null,r=null}};Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CsvExportHistory",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);