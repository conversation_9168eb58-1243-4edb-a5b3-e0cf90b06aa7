﻿CREATE OR ALTER PROCEDURE [dbo].[usp_update_POHubSourcingResult]                                    
--***************************************************************************************************                                    
--* RP 16.02.2010:                                    
--* - remove @SupplierName       
--* Action: Altered  By: Abhinav Saxena  Date: 08-09-2023 Comment: For RP-2228  (AS6081).                                 
--***************************************************************************************************                                    
   @SourcingResultId int                                    
 , @Part nvarchar(30)                                    
 , @ManufacturerNo int = NULL                                    
 , @DateCode nvarchar(5) = NULL                                    
 , @ProductNo int = NULL                                    
 , @PackageNo int = NULL                                    
 , @Quantity int                                    
 , @Price float                                    
 --, @CurrencyNo int = NULL                                    
 , @OfferStatusNo int = NULL                                    
 , @SupplierNo int                                    
 , @ROHS tinyint = NULL                                   
 , @Notes nvarchar(500) = NULL                                    
 , @UpdatedBy int = NULL                                  
 , @SuplierPrice float = NULL                           
 , @EstimatedShippingCost FLOAT=0                         
 , @DeliveryDate DATETIME=NULL                         
 , @PUHUB BIT=0                        
 , @SPQ nvarchar(50)=NULL                      
 , @LeadTime nvarchar(50)=NULL                      
 , @ROHSStatus nvarchar(50)=NULL                      
 , @FactorySealed nvarchar(50)=NULL                      
 , @MSL nvarchar(50)=NULL                     
 , @SupplierTotalQSA nvarchar(30) = null                    
 , @SupplierLTB nvarchar(30) = null                    
 , @SupplierMOQ nvarchar(30) = null                    
 , @RegionNo int = NULL                      
 , @CurrencyNo int = NULL                    
    , @LinkMultiCurrencyNo int = NULL                    
 , @MSLLevelNo int = NULL                    
 , @SupplierWarranty     int = NULL                 
 , @isTestingRecommended bit = 0                
 , @PriorityNo int = NULL              
 , @IHSCountryOfOriginNo int = NULL                  
 , @ChangedFields nvarchar(max) = NULL       
 , @PartWatchMatchHUBIPO BIT=0     
 , @TypeOfSupplier   INT=NULL    
 , @ReasonForSupplier  INT=NULL    
 , @RiskOfSupplier   INT=NULL     
 , @CountryNo INT=NULL   
 , @RowsAffected int = NULL OUTPUT                                    
AS                                     
    BEGIN                       
                        
     --Get the default currency of client                    
     DECLARE @ClientCurrencyNo INT                    
  SELECT @ClientCurrencyNo = SupplierCurrencyNo                     
  FROM tbLinkMultiCurrency where LinkMultiCurrencyId = @LinkMultiCurrencyNo           
            
  declare @MfrName nvarchar(256)          
            
  select @MfrName= ManufacturerName from tbManufacturer where ManufacturerId = @ManufacturerNo                   
                                            
        UPDATE  dbo.tbSourcingResult                                    
        SET     FullPart = dbo.ufn_get_fullpart(@Part)                                    
              , Part = @Part                                    
              , ManufacturerNo = @ManufacturerNo                                    
              , DateCode = @DateCode                                    
              --, ProductNo= CASE WHEN @PUHUB=0 THEN @ProductNo ELSE ProductNo  END                        
     , ProductNo= @ProductNo                               
              , PackageNo = @PackageNo                                    
              , Quantity = @Quantity                                    
              , Price = @Price                                    
             -- , CurrencyNo = @CurrencyNo                                          
              , ROHS = @ROHS                                    
              , OfferStatusNo = @OfferStatusNo                                    
              , OfferStatusChangeDate = CURRENT_TIMESTAMP                                    
              , OfferStatusChangeLoginNo = @UpdatedBy                                    
              , UpdatedBy = @UpdatedBy                       
             -- , SupplierNotes  = @Notes                               
              , Notes = @Notes                                    
, DLUP = CURRENT_TIMESTAMP                                    
   , SupplierPrice = isnull(@SuplierPrice,0) / isnull(ExchangeRate,1)                         
, EstimatedShippingCost = @EstimatedShippingCost                                
      , DeliveryDate=@DeliveryDate                      
      , SPQ=@SPQ                     
      , LeadTime=@LeadTime                      
      --, ROHSStatus=@ROHSStatus                      
      , FactorySealed=@FactorySealed                      
     -- , MSL= @MSL                    
      , SupplierTotalQSA = @SupplierTotalQSA                    
      , SupplierLTB = @SupplierLTB                    
      , SupplierMOQ = @SupplierMOQ                    
      , RegionNo=@RegionNo                      
   , ActualPrice = @SuplierPrice                     
   , CurrencyNo = @CurrencyNo                      
   , LinkMultiCurrencyNo = @LinkMultiCurrencyNo                    
   , ClientCurrencyNo = @ClientCurrencyNo                    
   , MSLLevelNo = @MSLLevelNo                    
  ,SupplierWarranty=@SupplierWarranty                  
    ,TestRecommended =@isTestingRecommended                 
 , PriorityNo=@PriorityNo                  
      ,IHSCountryOfOriginNo=@IHSCountryOfOriginNo           
   , SupplierManufacturerName= @MfrName          
   ,PartWatchMatchHUBIPO=@PartWatchMatchHUBIPO      
   , TypeOfSupplierNo=@TypeOfSupplier    
   , ReasonForSupplierNo=@ReasonForSupplier    
   , RiskOfSupplierNo = @RiskOfSupplier   
   , CountryNo=@CountryNo                   
        WHERE   SourcingResultId = @SourcingResultId                                    
                                    
        SELECT  @RowsAffected = @@rowcount               
            
            
  --- log code start hear          
  IF len(@ChangedFields) > 0                                 
                BEGIN                   
    declare @SourcingResultLogId int                          
              EXEC usp_insert_SourcingResultLog                                  
                          @SourcingResultId = @SourcingResultId                                 
                        , @Detail = @ChangedFields                               
                        , @UpdatedBy = @UpdatedBy                                 
                        , @SourcingResultLogId = @SourcingResultLogId OUTPUT                                
                END            
  --- log code ends hear                                  
                                        
    END           