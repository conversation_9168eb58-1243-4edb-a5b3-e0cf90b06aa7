///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.initializeBase(this, [element]);
	this._intLineID = -1;
	this._intCompanyID = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.prototype = {

    get_intCompanyID: function() { return this._intCompanyID; }, set_intCompanyID: function(v) { if (this._intCompanyID !== v) this._intCompanyID = v; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intLineID = null;
        this._intCompanyID = null;
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
           // this.getFieldDropDownData("ctlCategory");
           // alert("test");
            $find(this.getField("ctlCategory").ControlID).addChanged(Function.createDelegate(this, this.getCertificateByCategory));
            this.addSave(Function.createDelegate(this, this.saveClicked));
        }

    },
    getCertificateByCategory: function() {
        this.showCertificateFieldsLoading(true);
        this.getFieldComponent("ctlCertificate")._intCategoryID = this.getFieldValue("ctlCategory");
        this.getFieldDropDownData("ctlCertificate");
        this.showCertificateFieldsLoading(false);
    },
    showCertificateFieldsLoading: function(bln) {
        this.showFieldLoading("ctlCertificate", bln);
    },

    saveClicked: function() {
        if (!this.validateForm()) return;
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/CompanyCertificate");
        obj.set_DataObject("CompanyCertificate");
        obj.set_DataAction("SaveEdit");
        obj.addParameter("id", this._intLineID);
        obj.addParameter("Category", this.getFieldValue("ctlCategory"));
        obj.addParameter("Certificate", this.getFieldValue("ctlCertificate"));
        obj.addParameter("Number", this.getFieldValue("ctlCertificateNumbre"));
        obj.addParameter("StartDate", this.getFieldValue("ctlStartDate"));
        obj.addParameter("ExpiryDate", this.getFieldValue("ctlExpiryDate"));
        obj.addParameter("InActive", this.getFieldValue("ctlInActive"));
        obj.addParameter("Desc", this.getFieldValue("ctlDescription"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyCertificate_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
