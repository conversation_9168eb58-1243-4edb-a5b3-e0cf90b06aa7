///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
///<reference name="Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataList" assembly="Rebound.GlobalTrader.Site" />
//-----------------------------------------------------------------------------------------
// SK 20.01.2010:
// - only return a hyperlink for system document if the ID is greater than zero
// RP 28.10.2009:
// - add Company List Type to Company and Contact Detail links
//Marker     changed by      date         Remarks
//[001]      Vinay           21/08/2012   ESMS Ref:54 - If SO line created from Quote line then create hyperlink from sales order to quote
//[002]      Vinay           28/08/2012   Add purchase order link in srma lines
//[003]      Vinay           31/08/2012   Add sales order link in purchase order allocation lines
//[004]      Vinay           11/06/2013   CR:- Supplier Invoice
//[005]      Raushan          18/03/2015  GT Home Page- View for my self
//[006]      Prakash           11/04/2016   CR:- Client Invoice
//[011]      Suhail           03/04/2018  Implement IsImportant on My Open Quotes of Sales Home Page
//[013]      Umendra Gupta    26/12/2018  Creating link button for BOM Import
//-----------------------------------------------------------------------------------------
////////////////////////////////////////////////////////////////////////
//functions for page jumping
// Code Merge for GI Screen
$RGT_gotoURL_Company = function (intID, intTab, enmCompanyListType) {
    var sb = new Sys.StringBuilder($R_URL_Contact_CompanyDetail);
    sb.append(String.format("?{0}={1}", $R_QS_CompanyID, intID));
    if (intTab != null) sb.append(String.format("&{0}={1}", $R_QS_Tab, intTab));
    if (enmCompanyListType != null) sb.append(String.format("&{0}={1}", $R_QS_CompanyListType, enmCompanyListType));
    return sb.toString();
};
$RGT_gotoURL_CompanyBrowse = function (enmCompanyListType, strCompanyName) {
    var sb = new Sys.StringBuilder($R_URL_Contact_CompanyBrowse);
    if (enmCompanyListType == null) enmCompanyListType = $R_ENUM$CompanyListType.AllCompanies;
    sb.append(String.format("?{0}={1}", $R_QS_CompanyListType, enmCompanyListType));
    if (strCompanyName) sb.append(String.format("&{0}={1}", $R_QS_CompanyName, escape(strCompanyName)));
    return sb.toString();
};
$RGT_gotoURL_Contact = function (intID, enmCompanyListType) {
    var sb = new Sys.StringBuilder($R_URL_Contact_ContactDetail);
    sb.append(String.format("?{0}={1}", $R_QS_ContactID, intID));
    if (enmCompanyListType != null) sb.append(String.format("&{0}={1}", $R_QS_CompanyListType, enmCompanyListType));
    return sb.toString();
};
//[001] code start
$RGT_gotoURL_SoQuote = function (intID, QuoteLineID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_QuoteDetail);
    sb.append(String.format("?{0}={1}", $R_QS_QuoteID, intID));
    if (QuoteLineID != null) sb.append(String.format("&{0}={1}", $R_QS_QuoteLineID, QuoteLineID));
    return sb.toString();
};
//[001] code end

$RGT_gotoURL_ContactBrowse = function (strContactName) {
    var sb = new Sys.StringBuilder($R_URL_Contact_ContactBrowse);
    if (strContactName) sb.append(String.format("?{0}={1}", $R_QS_ContactName, escape(strContactName)));
    return sb.toString();
};
$RGT_gotoURL_Manufacturer = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Contact_ManufacturerDetail);
    sb.append(String.format("?{0}={1}", $R_QS_ManufacturerID, intID));
    return sb.toString();
};
$RGT_gotoURL_ManufacturerBrowse = function (strManufacturerName) {
    var sb = new Sys.StringBuilder($R_URL_Contact_ManufacturerBrowse);
    if (strManufacturerName) sb.append(String.format("?{0}={1}", $R_QS_ManufacturerName, escape(strManufacturerName)));
    return sb.toString();
};
$RGT_gotoURL_Invoice = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_InvoiceDetail);
    sb.append(String.format("?{0}={1}", $R_QS_InvoiceID, intID));
    return sb.toString();
};
$RGT_gotoURL_InvoiceAdd = function (intCompanyID, strCompanyName, intContactID, strContactName) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    if (strContactName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactName, escape(strContactName)));
    return $R_URL_Orders_InvoiceAdd + sb.toString();
};
$RGT_gotoURL_CustomerRequirement = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_CustomerRequirementDetail);
    sb.append(String.format("?{0}={1}", $R_QS_CustomerRequirementID, intID));
    return sb.toString();
};
$RGT_gotoURL_CustomerRequirementAdd = function (intCompanyID, strCompanyName, intContactID) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    return $R_URL_Orders_CustomerRequirementAdd + sb.toString();
};
$RGT_gotoURL_Quote = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_QuoteDetail);
    sb.append(String.format("?{0}={1}", $R_QS_QuoteID, intID));
    return sb.toString();
};
$RGT_gotoURL_QuoteAdd = function (intCompanyID, strCompanyName, intReqID, strLineIDs, intContactID) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intReqID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CustomerRequirementID, intReqID));
    if (strLineIDs) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_LineIDs, strLineIDs));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    return $R_URL_Orders_QuoteAdd + sb.toString();
};
$RGT_gotoURL_SalesOrder = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_SalesOrderDetail);
    sb.append(String.format("?{0}={1}", $R_QS_SalesOrderID, intID));
    return sb.toString();
};


$RGT_gotoURL_SalesOrderAdd = function (intCompanyID, strCompanyName, intContactID, intQuoteID, strLineIDs) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    if (intQuoteID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_QuoteID, intQuoteID));
    if (strLineIDs) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_LineIDs, strLineIDs));
    return $R_URL_Orders_SalesOrderAdd + sb.toString();
};
$RGT_gotoURL_GoodsIn = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_GoodsInDetail);
    sb.append(String.format("?{0}={1}", $R_QS_GoodsInID, intID));
    return sb.toString();
};
$RGT_gotoURL_PurchaseOrder = function (intID) {
    var sb = new Sys.StringBuilder("");
    sb.append(String.format("?{0}={1}", $R_QS_PurchaseOrderID, intID));
    return $R_URL_Orders_PurchaseOrderDetail + sb.toString();
};
$RGT_gotoURL_PurchaseOrderAdd = function (intCompanyID, strCompanyName, intContactID, intSalesOrderID, intSOLineID) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    if (intSalesOrderID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_SalesOrderID, intSalesOrderID));
    if (intSOLineID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_LineIDs, intSOLineID));
    return $R_URL_Orders_PurchaseOrderAdd + sb.toString();
};
$RGT_gotoURL_ReceivePurchaseOrder = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_ReceivePurchaseOrderDetail);
    sb.append(String.format("?{0}={1}", $R_QS_PurchaseOrderID, intID));
    return sb.toString();
};
$RGT_gotoURL_ShipSalesOrder = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_ShipSalesOrderDetail);
    sb.append(String.format("?{0}={1}", $R_QS_SalesOrderID, intID));
    return sb.toString();
};
$RGT_gotoURL_PurchaseRequisition = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_PurchaseRequisitionDetail);
    sb.append(String.format("?{0}={1}", $R_QS_PurchaseRequisitionID, intID));
    return sb.toString();
};
$RGT_gotoURL_CRMA = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_CustomerRMADetail);
    sb.append(String.format("?{0}={1}", $R_QS_CRMAID, intID));
    return sb.toString();
};
$RGT_gotoURL_CRMAAdd = function (intCompanyID, strCompanyName, intContactID, strContactName) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    if (strContactName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactName, escape(strContactName)));
    return $R_URL_Orders_CustomerRMAAdd + sb.toString();
};
$RGT_gotoURL_SRMA = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_SupplierRMADetail);
    sb.append(String.format("?{0}={1}", $R_QS_SRMAID, intID));
    return sb.toString();
};
$RGT_gotoURL_SRMAAdd = function (intCompanyID, strCompanyName, intContactID, strContactName) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    if (strContactName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactName, escape(strContactName)));
    return $R_URL_Orders_SupplierRMAAdd + sb.toString();
};
$RGT_gotoURL_CreditNote = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_CreditNoteDetail);
    sb.append(String.format("?{0}={1}", $R_QS_CreditID, intID));
    return sb.toString();
};
$RGT_gotoURL_CreditNoteAdd = function (intCompanyID, strCompanyName, intContactID, strContactName) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    if (strContactName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactName, escape(strContactName)));
    return $R_URL_Orders_CreditNoteAdd + sb.toString();
};
$RGT_gotoURL_DebitNote = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_DebitNoteDetail);
    sb.append(String.format("?{0}={1}", $R_QS_DebitID, intID));
    return sb.toString();
};
$RGT_gotoURL_DebitNoteAdd = function (intCompanyID, strCompanyName, intContactID, strContactName) {
    var sb = new Sys.StringBuilder("");
    if (intCompanyID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyID, intCompanyID));
    if (strCompanyName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_CompanyName, escape(strCompanyName)));
    if (intContactID) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactID, intContactID));
    if (strContactName) sb.append(String.format("{0}{1}={2}", (sb.toString().length > 0) ? "&" : "?", $R_QS_ContactName, escape(strContactName)));
    return $R_URL_Orders_DebitNoteAdd + sb.toString();
};
$RGT_gotoURL_Stock = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_StockDetail);
    sb.append(String.format("?{0}={1}", $R_QS_StockID, intID));
    return sb.toString();
};
$RGT_gotoURL_StockBrowse = function (strSearchPart) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_StockBrowse);
    sb.append(String.format("?{0}={1}", $R_QS_SearchPartNo, strSearchPart));
    return sb.toString();
};

$RGT_gotoURL_IHSBrowse = function () {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_IHSCatalogueBrowse);
    return sb.toString();
};

$RGT_gotoURL_Sourcing = function (strPartNo, blnSearch) {
    var sb = new Sys.StringBuilder($R_URL_Orders_Sourcing);
    if (!strPartNo) strPartNo = "";
    if (strPartNo.length > 1) sb.append(String.format("?{0}={1}", (blnSearch) ? $R_QS_SearchPartNo : $R_QS_PartNo, escape(strPartNo)));
    return sb.toString();
};
$RGT_gotoURL_MailMessages = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Profile_MailMessages);
    if (intID) sb.append(String.format("?{0}={1}", $R_QS_MailMessageID, intID));
    return sb.toString();
};
$RGT_gotoURL_ToDo = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Profile_ToDo);
    sb.append(String.format("?{0}={1}", $R_QS_ToDoID, intID));
    return sb.toString();
};
$RGT_gotoURL_SecurityUser = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Setup_Security_Users);
    sb.append(String.format("?{0}={1}", $R_QS_LoginID, intID));
    return sb.toString();
};
$RGT_gotoURL_SecurityGroup = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Setup_Security_Groups);
    sb.append(String.format("?{0}={1}", $R_QS_SecurityGroupID, intID));
    return sb.toString();
};
$RGT_gotoURL_ReceiveCRMA = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_ReceiveCustomerRMADetail);
    sb.append(String.format("?{0}={1}", $R_QS_CRMAID, intID));
    return sb.toString();
};
$RGT_gotoURL_ShipSRMA = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_ShipSupplierRMADetail);
    sb.append(String.format("?{0}={1}", $R_QS_SRMAID, intID));
    return sb.toString();
};
$RGT_gotoURL_Service = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_ServicesDetail);
    sb.append(String.format("?{0}={1}", $R_QS_ServiceID, intID));
    return sb.toString();
};
$RGT_gotoURL_Lot = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_LotsDetail);
    sb.append(String.format("?{0}={1}", $R_QS_LotID, intID));
    return sb.toString();
};
$RGT_gotoURL_BOM = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_BOMDetail);
    sb.append(String.format("?{0}={1}", $R_QS_BOMID, intID));
    return sb.toString();
};
$RGT_gotoURL_ReceivedPurchaseOrder = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Accounts_ReceivedPurchaseOrderDetail);
    sb.append(String.format("?{0}={1}", $R_QS_PurchaseOrderID, intID));
    return sb.toString();
};
$RGT_gotoURL_ReceivedCRMA = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Accounts_ReceivedCustomerRMADetail);
    sb.append(String.format("?{0}={1}", $R_QS_CRMAID, intID));
    return sb.toString();
};
//[013] start
$RGT_gotoURL_ClientBOMImport = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_ClientBOMDetail);
    sb.append(String.format("?{0}={1}", $R_QS_ClientBOMID, intID));
    return sb.toString();
};
//[013] end
///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
//Nub-button creation functions
$RGT_nubButton_Company = function (intID, strName, intTab, enmCompanyListType, blnOnStop, strNotes) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Company(intID, intTab, enmCompanyListType), str);
    if (blnOnStop) str += String.format("&nbsp;<span class=\"companyOnStop\">{0}</span>", $R_RES.OnStop);
    str += $R_FN.createAdvisoryNotesIcon(strNotes, (blnOnStop ? 'margin-left-10' : ''));

    return str;
};

$RGT_nubButton_Contact = function (intID, strName, enmCompanyListType) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Contact(intID, enmCompanyListType), $R_FN.setCleanTextValue(strName));
    return str;
};

//[001] code start

$RGT_nubButton_SoQuote = function (intID, strName, QuoteLineID) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_SoQuote(intID, QuoteLineID), $R_FN.setCleanTextValue(strName));
    return str;
};
//[001] code end

$RGT_nubButton_Stock = function (intID, strPart, intROHS) {
    var str = $R_FN.writePartNo(strPart, intROHS);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Stock(intID), str);
    return str;
};

$RGT_nubButton_Manufacturer = function (intID, strName, strNotes) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Manufacturer(intID), str);
    str += $R_FN.createAdvisoryNotesIcon(strNotes);
    return str;
};

//
$RGT_nubButton_ShipInstructions = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Manufacturer(intID), str);
    return str;
};
//
$RGT_nubButton_Service = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Service(intID), str);
    return str;
};

$RGT_nubButton_Lot = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Lot(intID), str);
    return str;
};
$RGT_nubButton_BOM = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_BOM(intID), str);
    return str;
};

//code start for [012]
$RGT_nubButton_BMM = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    //debugger;
    if (intID > 0) str = $R_FN.createNubButton('ord_BOMManagerDetail.aspx?BOM=' + intID, str);
    return str;
};
$RGT_nubButton_BMMPO = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    //debugger;
    if (intID > 0) str = $R_FN.createNubButton('ord_BOMManagerSourcing.aspx?BOM=' + intID, str);
    return str;
};
//code end for [012]
$RGT_nubButton_ShipSRMA = function (intID, strName) {
    var str = "";
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ShipSRMA(intID), strName);
    return str;
};

$RGT_nubButton_ReceiveCRMA = function (intID, strName) {
    var str = "";
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ReceiveCRMA(intID), strName);
    return str;
};

$RGT_nubButton_ShipSalesOrder = function (intID, strName) {
    var str = "";
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ShipSalesOrder(intID), strName);
    return str;
};

$RGT_nubButton_ReceivePurchaseOrder = function (intID, strName) {
    var str = "";
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ReceivePurchaseOrder(intID), strName);
    return str;
};

$RGT_nubButton_ToDo_CusClass = function (intID, strName, strCusClass) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButtonCustomClass($RGT_gotoURL_ToDo(intID), str, strCusClass);
    return str;
};

$RGT_nubButton_ToDo = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ToDo(intID), str);
    return str;
};

$RGT_nubButton_MailMessage = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_MailMessages(intID), str);
    return str;
};

$RGT_nubButton_ReceivedCRMA = function (intID, strName) {
    var str = "";
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ReceivedCRMA(intID), strName);
    return str;
};

$RGT_nubButton_ReceivedPurchaseOrder = function (intID, strName) {
    var str = "";
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ReceivedPurchaseOrder(intID), strName);
    return str;
};

$RGT_nubButton_SystemDocument = function (intSysDocTypeNo, intKeyNo, intSysDocNumber, intKeyNo2) {
    var strOut = "";

    intSysDocTypeNo = Number.parseInvariant(intSysDocTypeNo.toString());

    if (intSysDocTypeNo <= 0) return "";
    switch (Number.parseInvariant(intSysDocTypeNo.toString())) {
        case $R_ENUM$SystemDocument.CreditNote: strOut = $R_FN.createNubButton($RGT_gotoURL_CreditNote(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.CustomerRequirement: strOut = $R_FN.createNubButton($RGT_gotoURL_CustomerRequirement(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.CustomerRMA: strOut = $R_FN.createNubButton($RGT_gotoURL_CRMA(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.DebitNote: strOut = $R_FN.createNubButton($RGT_gotoURL_DebitNote(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.GoodsIn: strOut = $R_FN.createNubButton($RGT_gotoURL_GoodsIn(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.Invoice: strOut = $R_FN.createNubButton($RGT_gotoURL_Invoice(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.PurchaseOrder: strOut = $R_FN.createNubButton($RGT_gotoURL_PurchaseOrder(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.Quote: strOut = $R_FN.createNubButton($RGT_gotoURL_Quote(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.SalesOrder: strOut = $R_FN.createNubButton($RGT_gotoURL_SalesOrder(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.SupplierRMA: strOut = $R_FN.createNubButton($RGT_gotoURL_SRMA(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.PurchaseRequisition: strOut = $R_FN.createNubButton($RGT_gotoURL_PurchaseRequisition(intKeyNo, intKeyNo2), intSysDocNumber); break;
        //[004] code start   
        case $R_ENUM$SystemDocument.SupplierInvoice: strOut = $R_FN.createNubButton($RGT_gotoURL_SupplierInvoice(intKeyNo, intKeyNo2), intSysDocNumber); break;
        //[004] code end
        case $R_ENUM$SystemDocument.InternalPurchaseOrder: strOut = $R_FN.createNubButton($RGT_gotoURL_InternalPurchaseOrder(intKeyNo, intKeyNo2), intSysDocNumber); break;
        case $R_ENUM$SystemDocument.PurchaseQuote: strOut = $R_FN.createNubButton($RGT_gotoURL_POQuote(intKeyNo, intKeyNo2), intSysDocNumber); break;
        //[005] code start    
        case $R_ENUM$SystemDocument.ClientInvoice: strOut = $R_FN.createNubButton($RGT_gotoURL_ClientInvoice(intKeyNo, intKeyNo2), intSysDocNumber); break;
        //[005] code end 
    }
    return strOut;
};
$RGT_nubButton_SalesOrder = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.SalesOrder, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.SalesOrder, intID, strNumber);
    return str;
};
$RGT_nubButton_PurchaseOrder = function (intID, strNumber) {

    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseOrder, intID, strNumber);
    var str = strNumber;

    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseOrder, intID, strNumber);
    return str;
};
//[002] code start
$RGT_nubButton_PurchaseOrderSRMA = function (intID, strName, PurchageLineID) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_POSRMA(intID, PurchageLineID), $R_FN.setCleanTextValue(strName));
    return str;
};
$RGT_gotoURL_POSRMA = function (intID, PurchageLineID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_PurchaseOrderDetail);
    sb.append(String.format("?{0}={1}", $R_QS_PurchaseOrderID, intID));
    if (PurchageLineID != null) sb.append(String.format("&{0}={1}", $R_QS_PurchaseOrderLineID, PurchageLineID));
    return sb.toString();
};
//[002] code end
//[003] code start
$RGT_nubButton_POSO = function (intID, strName, SoLineID) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_POSO(intID, SoLineID), $R_FN.setCleanTextValue(strName));
    return str;
};
$RGT_gotoURL_POSO = function (intID, SoLineID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_SalesOrderDetail);
    sb.append(String.format("?{0}={1}", $R_QS_SalesOrderID, intID));
    if (SoLineID != null) sb.append(String.format("&{0}={1}", $R_QS_SalesOrderLineID, SoLineID));
    return sb.toString();
};
//[003] code end
$RGT_nubButton_Invoice = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.Invoice, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.Invoice, intID, strNumber);
    return str;
};
$RGT_nubButton_CreditNote = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CreditNote, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CreditNote, intID, strNumber);
    return str;
};
$RGT_nubButton_DebitNote = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.DebitNote, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.DebitNote, intID, strNumber);
    return str;
};
$RGT_nubButton_GoodsIn = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.GoodsIn, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.GoodsIn, intID, strNumber);
    return str;
};
$RGT_nubButton_Quote = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.Quote, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.Quote, intID, strNumber);
    return str;
};
$RGT_nubButton_CustomerRequirement = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CustomerRequirement, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CustomerRequirement, intID, strNumber);
    return str;
};
$RGT_nubButton_SRMA = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.SupplierRMA, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.SupplierRMA, intID, strNumber);
    return str;
};
$RGT_nubButton_CRMA = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CustomerRMA, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.CustomerRMA, intID, strNumber);
    return str;
};
$RGT_nubButton_PurchaseRequisition = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseRequisition, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseRequisition, intID, strNumber);
    return str;
};
//[004] code start
$RGT_nubButton_SupplierInvoice = function (intID, strNumber) {
    //	return $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseOrder, intID, strNumber);
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.SupplierInvoice, intID, strNumber);
    return str;
};
$RGT_gotoURL_SupplierInvoice = function (intID) {
    var sb = new Sys.StringBuilder("");
    sb.append(String.format("?{0}={1}", $R_QS_SupplierInvoiceID, intID));
    return $R_URL_Warehouse_SupplierInvoiceDetail + sb.toString();
};
//[004] code end


//[005] code start
$RGT_nubButton_NPR = function (intID, strNumber, intLineId) {
    var str = strNumber;
    if (intID > 0) str = $R_FN.createNubButtonNPR(intID, strNumber, intLineId)
    return str;
};
$RGT_nubButton_NPRNugget = function (intID, strNumber, intLineId) {
    var str = strNumber;
    if (intID > 0) str = $R_FN.createNubButtonNPRNugget(intID, strNumber, intLineId)
    return str;
};

$RGT_openNPRWindow = function (intLineId, intID) {
    $R_FN.openNPRPrintWindow(intLineId, intID);
};

$RGT_gotoURL_NotifyNPR = function (intID) {
    var sb = new Sys.StringBuilder("");
    sb.append(String.format("?{0}={1}", $R_QS_NPRID, intID));
    return $R_URL_Warehouse_NPRNotify + sb.toString();
};
//[005] code end

$RGT_nubButton_STO = function (intID, strNumber, intLineId) {
    var str = strNumber;
    if (intID > 0) str = $R_FN.createNubButtonSTO(intID, strNumber, intLineId)
    return str;
};

$RGT_openNPRLog = function (enmPrintObject, intID) {
    $R_FN.openPrintNPRWindow(enmPrintObject, intID);
};

$RGT_openEPRWindow = function (intID, eprId, intPOLineID) {
    $R_FN.openEPRWindow(intID, eprId, intPOLineID);
};

$RGT_openCreditLimitWindow = function (intCompanyID, CreditLimitID) {
    $R_FN.openCreditLimitWindow(intCompanyID, CreditLimitID);
};

$RGT_openCreditLimitLogWindow = function (intCompanyID, CreditLimitID) {
    $R_FN.openCreditLimitLogWindow(intCompanyID, CreditLimitID);
};

$RGT_nubButton_EPR = function (intID, strNumber, strText) {
    var str = strNumber;
    if (intID > 0) str = $R_FN.createNubButtonEPR(intID, strNumber, strText);
    return str;
};
$RGT_nubButton_CreditLimit = function (intID, strNumber, strText) {
    var str = strNumber;
    if (intID > 0) str = $R_FN.createNubButtonCreditLimit(intID, strNumber, strText);
    return str;
};
$RGT_openEPRLog = function (enmPrintObject, intID) {
    $R_FN.openPrintEPRWindow(enmPrintObject, intID);
};

//[005] Code Start
$RGT_gotoURL_SalesOrderBrowse = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_SalesOrderBrowse);
    sb.append(String.format("?{0}={1}", "bss", true));
    sb.append(String.format("&{0}={1}", "sp", intID));
    return sb.toString();
};

$RGT_gotoURL_QuoteBrowse = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_QuoteBrowse);
    sb.append(String.format("?{0}={1}", "bss", true));
    sb.append(String.format("&{0}={1}", "sp", intID));
    return sb.toString();
};


$RGT_gotoURL_CusReqBrowse = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_CustomerRequirementBrowse);
    sb.append(String.format("?{0}={1}", "bss", true));
    sb.append(String.format("&{0}={1}", "sp", intID));
    return sb.toString();
};

$RGT_gotoURL_POBrowse = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_PurchaseOrderBrowse);
    sb.append(String.format("?{0}={1}", "bss", true));
    sb.append(String.format("&{0}={1}", "buy", intID));
    return sb.toString();
};

$RGT_openEPRWindowEmail = function (intID, eprId) {
    $R_FN.openEPRWindowEmail(intID, eprId);
};

//[005] Code End

//Generate BOM

$RGT_gotoURL_POQuote = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Orders_POQuoteDetail);
    sb.append(String.format("?{0}={1}", $R_QS_POQuoteID, intID));
    return sb.toString();
};
$RGT_nubButton_InternalPurchaseOrder = function (intID, strNumber) {
    var str = strNumber;
    //if (intID > 0) str =  $R_FN.createNubButton($RGT_gotoURL_InternalPurchaseOrder(intID), str);
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.InternalPurchaseOrder, intID, strNumber);
    return str;
};

$RGT_gotoURL_InternalPurchaseOrder = function (intID) {
    var sb = new Sys.StringBuilder("");
    sb.append(String.format("?{0}={1}", $R_QS_InternalPurchaseOrderID, intID));
    return $R_URL_Orders_InternalPurchaseOrderDetail + sb.toString();
};

$RGT_nubButton_POQuote = function (intID, strNumber) {
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.PurchaseQuote, intID, strNumber);
    return str;
};

$RGT_nubButton_CompanySourcing = function (intID, strName, intTab, enmCompanyListType, blnOnStop, blnToolTip, strToolTipText, strNotes) {

    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Company(intID, intTab, null), str, "", blnToolTip, strToolTipText);
    if (blnOnStop) str += String.format("&nbsp;<span class=\"companyOnStop\">{0}</span>", $R_RES.OnStop);
    if (strNotes) str += $R_FN.createAdvisoryNotesIcon(strNotes);
    return str;
};


//[010] code start
$RGT_nubButton_ClientInvoice = function (intID, strNumber) {
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.ClientInvoice, intID, strNumber);
    return str;
};
$RGT_gotoURL_ClientInvoice = function (intID) {
    var sb = new Sys.StringBuilder("");
    sb.append(String.format("?{0}={1}", $R_QS_ClientInvoiceID, intID));
    return $R_URL_Orders_ClientInvoiceDetail + sb.toString();
};
//[010] code End

//[011] code start
$RGT_nubButton_QuoteImp = function (intID, strNumber, isImp) {
    var str = strNumber;
    if (intID > 0) str = $RGT_nubButton_SystemDocument($R_ENUM$SystemDocument.Quote, intID, strNumber);
    if (isImp) str += String.format("<span title=\"Important\"  class=\"quoteOnStop\">!</span>", $R_RES.Important);

    return str;
};

//[011] code End
//[012] code start
$RGT_nubButton_Caution = function (strName) {
    var str = '';// $R_FN.setCleanTextValue(strName);
    //if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_Company(intID, intTab, enmCompanyListType), str);
    //if (blnOnStop) str += String.format("&nbsp;<span  class=\"quoteOnStop\">Imp !</span>", $R_RES.Important);
    // if (blnOnStop) str += String.format("<span title=\"Important\"  class=\"quoteOnStop\">!</span>", $R_RES.Important);
    //if (blnOnStop)  
    str += String.format("&nbsp;<span class=\"companyOnStop11\">{0}</span>", strName);
    return str;
};
//[012] code end
//[013] start
$RGT_nubButton_ClientBOMImport = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ClientBOMImport(intID), str);
    return str;
};
//[013] end
$RGT_openIHSDoc = function (intID, intF) {
    $R_FN.openIHSPDFWindow(intID, intF);
};

$RGT_openBomItemIHS = function (intID, intF) {
    $R_FN.openBomItemIHSWindow(intID, intF);
};
$RGT_openEndUserUndertakingDoc = function (intID) {
    $R_FN.openEndUserUndertakingPDFWindow(intID);
};
//CIP
$RGT_openCIPPDFDoc = function (intCertificateID, intCompanyID) {
    $R_FN.openCIPPDFWindow(intCertificateID, intCompanyID);
};
$RGT_openShortShipmentWindow = function (intID) {
    var str = $R_FN.setCleanTextValue(intID);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ShortShipment(intID), str);
    return str;
};
$RGT_gotoURL_ShortShipment = function (intID) {
    var sb = new Sys.StringBuilder($R_URL_Warehouse_NotifyShortShipment);
    sb.append(String.format("?{0}={1}", $R_QS_ShortShipmentID, intID));
    return sb.toString();
};
$RGT_gotoURL_NotifyShortShipment = function (intID, Stage) {
    var sb = new Sys.StringBuilder("");
    sb.append(String.format("?{0}={1}&{2}={3}", $R_QS_ShortShipmentID, intID, $R_QS_ShortShipmentStage, Stage));
    return $R_URL_Warehouse_ShortShipmentNotify + sb.toString();
};
$RGT_openSupplierApprovalDoc = function (intID, strUpldTyp, IsEditScreen) {
    $R_FN.openSupplierApprovalDocWindow(intID, strUpldTyp, IsEditScreen);
};
$RGT_openSupplierApprovalImages = function (intID, strUpldTyp, IsEditScreen) {
    $R_FN.openSupplierApprovalImageWindow(intID, strUpldTyp, IsEditScreen);
};
$RGT_nubButton_ShortShipment = function (intID, strName, Stage) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_NotifyShortShipment(intID, Stage), str);
    return str;
};
$RGT_nubButton_ShortShipmentDetails = function (intID, strName) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_ShortShipmentDetails(intID), str);
    return str;
};
$RGT_gotoURL_ShortShipmentDetails = function (intID) {
    var sb = new Sys.StringBuilder("");
    sb.append(String.format("?{0}={1}", $R_QS_ShortShipmentID, intID));
    return $R_URL_Warehouse_ShortShipmentDetails + sb.toString();
};
$RGT_nubButton_QueryGILine = function (intID, intLineId, strName, boolLandedquery) {
    var str = $R_FN.setCleanTextValue(strName);
    if (intID > 0) str = $R_FN.createNubButton($RGT_gotoURL_QueryGILine(intID, intLineId, boolLandedquery), str);
    return str;
};
$RGT_gotoURL_QueryGILine = function (intID, intLineId, boolLandedquery) {
    var sb = new Sys.StringBuilder("");
    sb.append(String.format("?{0}={1}&{2}={3}&{4}={5}", $R_QS_GoodsInID, intID, $R_QS_LandedGiLineId, intLineId, $R_QS_IsLandedQuery, boolLandedquery));
    return $R_URL_Warehouse_GoodsInDetail + sb.toString();
};
$RGT_nubButton_GILineShortShipment = function (intID, strNumber, intLineId) {
    var str = strNumber;
    if (intID > 0) str = $R_FN.createNubButtonGILineShortShipment(intID, strNumber, intLineId)
    return str;
};
