Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.Tax=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.Tax.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.Tax.prototype={get_intGlobalLoginClientNo:function(){return this._intGlobalLoginClientNo},set_intGlobalLoginClientNo:function(n){this._intGlobalLoginClientNo!==n&&(this._intGlobalLoginClientNo=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.Tax.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._intGlobalLoginClientNo=null,Rebound.GlobalTrader.Site.Controls.DropDowns.Tax.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/Tax");this._objData.set_DataObject("Tax");this._objData.set_DataAction("GetData");this._objData.addParameter("GlobalLoginClientNo",this._intGlobalLoginClientNo)},dataCallOK:function(){var t=this._objData._result,n;if(t.Taxes)for(n=0;n<t.Taxes.length;n++)this.addOption(t.Taxes[n].Name,t.Taxes[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.Tax.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.Tax",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);