using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
    [WebService(Namespace = "http://tempuri.org/")]
    [WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class CompanyCertificate : Rebound.GlobalTrader.Site.Data.Base
    {

        public override void ProcessRequest(HttpContext context)
        {
            if (base.init(context))
            {
                switch (Action)
                {
                    case "GetCertificates": GetCertificates(); break;
                    case "AddNew": AddNew(); break;
                    case "SaveEdit": SaveEdit(); break;
                    case "GetCompanyDetailInactive": GetCompanyDetailInactive(); break;
                    default: WriteErrorActionNotFound(); break;
                }
            }
        }



        /// <summary>
        /// get all certificate by company 
        /// </summary>
        private void GetCertificates()
        {
            try
            {

                JsonObject jsn = new JsonObject();
                JsonObject jsnItems = new JsonObject(true);
                List<Certificate> lst = Certificate.GetCertificateByCompany(GetFormValue_Int("CompanyID"));
                foreach (Certificate ln in lst)
                {
                    JsonObject jsnItem = new JsonObject();
                    jsnItem.AddVariable("ID", ln.CompanyCertificateId);
                    jsnItem.AddVariable("CeriticateName", ln.CertificateName);
                    jsnItem.AddVariable("StartDate", Functions.FormatDate(ln.StartDate));
                    jsnItem.AddVariable("ExpiryDate", Functions.FormatDate(ln.ExpiryDate));
                    jsnItem.AddVariable("Inactive", ln.Inactive);
                    jsnItem.AddVariable("CategoryNo", ln.CertificateCategoryNo);
                    jsnItem.AddVariable("Desc", Functions.ReplaceLineBreaks(ln.Description));
                    jsnItem.AddVariable("CertificateID", ln.CertificateId);
                    jsnItem.AddVariable("CategoryName", ln.CertificateCategoryName);
                    jsnItem.AddVariable("CertificateNum", ln.CertificateNumber);
                    jsnItem.AddVariable("isCIPPDFAvailable", ln.isCIPPDFAvailable);
                    jsnItems.AddVariable(jsnItem);
                    jsnItem.Dispose(); jsnItem = null;
                }
                jsn.AddVariable("Lines", jsnItems);
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

        /// <summary>
        /// Add new Certificate category
        /// </summary>
        public void AddNew()
        {
            try
            {
                int intNewCertificateID = BLL.Certificate.InsertCompanyCertificate(
                    SessionManager.ClientID
                   , ID
                   , GetFormValue_Int("Category")
                   , GetFormValue_Int("Certificate")
                   , GetFormValue_String("Desc")
                   , GetFormValue_NullableDateTime("StartDate", null)
                   , GetFormValue_NullableDateTime("ExpiryDate", null)
                   , GetFormValue_NullableBoolean("InActive", false)
                   ,  LoginID
                   , GetFormValue_String("Number")
                    );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", intNewCertificateID > 0);
                jsn.AddVariable("NewID", intNewCertificateID);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;

            }
            catch (Exception e)
            {
                WriteError(e);
            }

        }

        private void GetCompanyDetailInactive()
        {
            Company cm = Company.GetCompanyDetailInactive(ID);
            if (cm == null)
            {
                WriteErrorDataNotFound();
            }
            else
            {
                JsonObject jsn = null;
                if (cm != null)
                {
                    jsn = new JsonObject();
                    jsn.AddVariable("CompanyId", cm.CompanyId);
                    jsn.AddVariable("Inactive", cm.Inactive);
                }
                cm = null;
                OutputResult(jsn);
                jsn.Dispose(); jsn = null;
            }
            cm = null;
        }

        /// <summary>
        /// Update an existing Company Certificate
        /// </summary>
        public void SaveEdit()
        {
            try
            {
                bool Result = BLL.Certificate.UpdateCompanyCertificate(
                            ID
                          , GetFormValue_Int("Category")
                          , GetFormValue_Int("Certificate")
                          , GetFormValue_String("Desc")
                          , GetFormValue_NullableDateTime("StartDate", null)
                          , GetFormValue_NullableDateTime("ExpiryDate", null)
                          , GetFormValue_NullableBoolean("InActive", false)
                          , LoginID
                          , GetFormValue_String("Number")
                           );
                JsonObject jsn = new JsonObject();
                jsn.AddVariable("Result", Result);
                OutputResult(jsn);
                jsn.Dispose();
                jsn = null;
            }
            catch (Exception e)
            {
                WriteError(e);
            }
        }

    }
}