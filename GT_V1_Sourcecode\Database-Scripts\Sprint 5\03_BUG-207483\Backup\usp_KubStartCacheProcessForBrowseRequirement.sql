
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [dbo].[usp_KubStartCacheProcessForBrowseRequirement]    
@PartNo  NVARCHAR(100)='',    
@ClientID INT=NULL,    
@CustomerReqId INT=NULL    
AS    
/*    
 * ACtion: Created  By: <PERSON><PERSON><PERSON><PERSON>  Date: 03/08/2023 Comment: Add proc to cacge requirement Browse page cache.    
 */    
BEGIN    
SET NOCOUNT ON;    
IF((@PartNo !='') AND (@ClientID IS NOT NULL) AND (@CustomerReqId IS NOT NULL))    
BEGIN    
EXEC usp_KubInsertAveragePriceDetailsCache @PartNo,@ClientID  
EXEC usp_KubGetTotalLineInvoicedDetails_Cache @PartNo,@ClientID,@CustomerReqId     
EXEC usp_KubGetMainProductGroupsDetails_Cache @PartNo,@ClientID     
EXEC usp_KubGetLast10QuoteDetails_Cache @PartNo,@ClientID,@CustomerReqId    
END    
SET NOCOUNT OFF;    
END 
GO


