﻿<%@ Control Language="C#" AutoEventWireup="true" CodeBehind="NPR.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.NPR.NPR" %>

<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlNpr" runat="server" ResourceTitle="NprNo" FilterField="Npr" />
		        <ReboundUI_FilterDataItemRow:DropDown id="ctlIsCompleted" runat="server" ResourceTitle="IsCompleted" FilterField="IsCompleted" DropDownType="Completed" DropDownAssembly="Rebound.GlobalTrader.Site" />
		        <ReboundUI_FilterDataItemRow:DropDown id="ctlIsAuthorised" runat="server" ResourceTitle="IsAuthorised" FilterField="IsAuthorised" DropDownType="Authorize" DropDownAssembly="Rebound.GlobalTrader.Site"/>
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlAction" runat="server" ResourceTitle="Action" DropDownType="Action" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="ActionBy" />
				
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlGINo" runat="server" ResourceTitle="GINo" FilterField="GINoNpr" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONoNPR" />	
				
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlNprRaisedDateFrom" runat="server" ResourceTitle="NprRaisedDateFrom" FilterField="NprRaisedDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlNprRaisedDateTo" runat="server" ResourceTitle="NprRaisedDateTo" FilterField="NprRaisedDateTo" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>