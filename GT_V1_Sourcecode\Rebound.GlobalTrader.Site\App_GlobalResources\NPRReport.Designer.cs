//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "16.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class NPRReport {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal NPRReport() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.NPRReport", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4. Action Required (Sales to complete). Please complete either 4a,4b,4c or 4d.
        /// </summary>
        internal static string ActionRequired {
            get {
                return ResourceManager.GetString("ActionRequired", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Advice Note.
        /// </summary>
        internal static string AdviceNote {
            get {
                return ResourceManager.GetString("AdviceNote", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All customers unauthorized return should be recorded in Section 3: Non All customers unauthorized return should be recorded in Section 3: Non Conformance details..
        /// </summary>
        internal static string Allcustomers {
            get {
                return ResourceManager.GetString("Allcustomers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise by/signature.
        /// </summary>
        internal static string Authoriseby {
            get {
                return ResourceManager.GetString("Authoriseby", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise Name.
        /// </summary>
        internal static string AuthoriseName {
            get {
                return ResourceManager.GetString("AuthoriseName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 6. Closure (Logistics).
        /// </summary>
        internal static string Closure {
            get {
                return ResourceManager.GetString("Closure", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Comments.
        /// </summary>
        internal static string Comments {
            get {
                return ResourceManager.GetString("Comments", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Completed By.
        /// </summary>
        internal static string CompletedBy {
            get {
                return ResourceManager.GetString("CompletedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer.
        /// </summary>
        internal static string Customer {
            get {
                return ResourceManager.GetString("Customer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer RMA.
        /// </summary>
        internal static string CustomerRMA {
            get {
                return ResourceManager.GetString("CustomerRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date.
        /// </summary>
        internal static string Date {
            get {
                return ResourceManager.GetString("Date", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Date raised:.
        /// </summary>
        internal static string Dateraised {
            get {
                return ResourceManager.GetString("Dateraised", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit note no:.
        /// </summary>
        internal static string Debitnoteno {
            get {
                return ResourceManager.GetString("Debitnoteno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Incurred cost to sales. Yes/No.
        /// </summary>
        internal static string Incurredcosttosales {
            get {
                return ResourceManager.GetString("Incurredcosttosales", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4c: Move into Stock.
        /// </summary>
        internal static string MoveintoStock {
            get {
                return ResourceManager.GetString("MoveintoStock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 3. Non Conformance Details. Reason for rejection (Warehouse to complete).
        /// </summary>
        internal static string NonConformance {
            get {
                return ResourceManager.GetString("NonConformance", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nonconforming Product Report.
        /// </summary>
        internal static string NPR {
            get {
                return ResourceManager.GetString("NPR", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR No..
        /// </summary>
        internal static string NPRNo {
            get {
                return ResourceManager.GetString("NPRNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 2. Order Details (Warehouse to complete).
        /// </summary>
        internal static string OrderDetails {
            get {
                return ResourceManager.GetString("OrderDetails", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 1. Originator (Warehouse to complete).
        /// </summary>
        internal static string Originator {
            get {
                return ResourceManager.GetString("Originator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outworker Name.
        /// </summary>
        internal static string OutworkerName {
            get {
                return ResourceManager.GetString("OutworkerName", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Outworker P/o No.
        /// </summary>
        internal static string OutworkerPO {
            get {
                return ResourceManager.GetString("OutworkerPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Part No.
        /// </summary>
        internal static string PartNo {
            get {
                return ResourceManager.GetString("PartNo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PO NO.
        /// </summary>
        internal static string PONO {
            get {
                return ResourceManager.GetString("PONO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Q Location.
        /// </summary>
        internal static string QLocation {
            get {
                return ResourceManager.GetString("QLocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Raised By.
        /// </summary>
        internal static string RaisedBy {
            get {
                return ResourceManager.GetString("RaisedBy", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rejected Qty.
        /// </summary>
        internal static string RejectedQty {
            get {
                return ResourceManager.GetString("RejectedQty", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4d: Required outwork.
        /// </summary>
        internal static string Requiredoutwork {
            get {
                return ResourceManager.GetString("Requiredoutwork", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4a : Return to supplier.
        /// </summary>
        internal static string Returntosupplier {
            get {
                return ResourceManager.GetString("Returntosupplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 5. Sales detail &amp; Authorisation.
        /// </summary>
        internal static string Salesdetail {
            get {
                return ResourceManager.GetString("Salesdetail", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Person.
        /// </summary>
        internal static string SalesPerson {
            get {
                return ResourceManager.GetString("SalesPerson", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 4b : Scrap.
        /// </summary>
        internal static string Scrap {
            get {
                return ResourceManager.GetString("Scrap", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SO No.
        /// </summary>
        internal static string SONo {
            get {
                return ResourceManager.GetString("SONo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SRMA No.
        /// </summary>
        internal static string SRMANo {
            get {
                return ResourceManager.GetString("SRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock location. 
        ///(EPO/Consignment department only).
        /// </summary>
        internal static string Stocklocation {
            get {
                return ResourceManager.GetString("Stocklocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier.
        /// </summary>
        internal static string Supplier {
            get {
                return ResourceManager.GetString("Supplier", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Reference.
        /// </summary>
        internal static string SupplierReference {
            get {
                return ResourceManager.GetString("SupplierReference", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA.
        /// </summary>
        internal static string SupplierRMA {
            get {
                return ResourceManager.GetString("SupplierRMA", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier RMA No.
        /// </summary>
        internal static string SupplierRMANo {
            get {
                return ResourceManager.GetString("SupplierRMANo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Ship Via.
        /// </summary>
        internal static string SupplierShipVia {
            get {
                return ResourceManager.GetString("SupplierShipVia", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Ship via a/c no:.
        /// </summary>
        internal static string SupplierShipviaacno {
            get {
                return ResourceManager.GetString("SupplierShipviaacno", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier to credit.
        /// </summary>
        internal static string Suppliertocredit {
            get {
                return ResourceManager.GetString("Suppliertocredit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Rejected value.
        /// </summary>
        internal static string TotalRejectedvalue {
            get {
                return ResourceManager.GetString("TotalRejectedvalue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unit Cost.
        /// </summary>
        internal static string UnitCost {
            get {
                return ResourceManager.GetString("UnitCost", resourceCulture);
            }
        }
    }
}
