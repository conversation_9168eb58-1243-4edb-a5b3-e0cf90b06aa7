
--********************************************************************************************
      --* SK 23.05.2024:
      --* - Add 2 fields into tbGPDetail
      --*   Calculate shipped cost including credit
      --* 
--********************************************************************************************
IF COL_LENGTH('tbGPDetail','ShippedSalesValueCredit') IS NULL
  BEGIN
  ALTER TABLE tbGPDetail
    ADD ShippedSalesValueCredit  float;
  END

IF COL_LENGTH('tbGPDetail','ShippedCostCredit') IS NULL
  BEGIN
  ALTER TABLE tbGPDetail
    ADD ShippedCostCredit  float;
  END

IF COL_LENGTH('tbGPLoginDetail','ShippedSalesValueCredit') IS NULL
  BEGIN
  ALTER TABLE tbGPLoginDetail
    ADD ShippedSalesValueCredit  float;
  END

IF COL_LENGTH('tbGPLoginDetail','ShippedCostCredit') IS NULL
  BEGIN
  ALTER TABLE tbGPLoginDetail
    ADD ShippedCostCredit  float;
  END
