///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.prototype = {

    // get_intNPRID: function() { return this._intNPRID; }, set_intNPRID: function(v) { if (this._intNPRID !== v) this._intNPRID = v; },
    get_ibtnSend: function () { return this._ibtnSend; }, set_ibtnSend: function (value) { if (this._ibtnSend !== value) this._ibtnSend = value; },
    get_ibtnSend_Footer: function () { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function (v) { if (this._ibtnSend_Footer !== v) this._ibtnSend_Footer = v; },
    // get_strMessageText: function() { return this._strMessageText; }, set_strMessageText: function(v) { if (this._strMessageText !== v) this._strMessageText = v; },
    // get_strNPRNo: function() { return this._strNPRNo; }, set_strNPRNo: function(v) { if (this._strNPRNo !== v) this._strNPRNo = v; },
    //  get_intGoodsIn: function() { return this._intGoodsIn; }, set_intGoodsIn: function(v) { if (this._intGoodsIn !== v) this._intGoodsIn = v; },
    //  get_strBuyerName: function() { return this._strBuyerName; }, set_strBuyerName: function(v) { if (this._strBuyerName !== v) this._strBuyerName = v; },
    // get_intBuyerId: function() { return this._intBuyerId; }, set_intBuyerId: function(v) { if (this._intBuyerId !== v) this._intBuyerId = v; },
    // get_intGoodsInLineId: function () { return this._intGoodsInLineId; }, set_intGoodsInLineId: function (v) { if (this._intGoodsInLineId !== v) this._intGoodsInLineId = v; },
    // get_intClientNo: function () { return this._intClientNo; }, set_intClientNo: function (v) { if (this._intClientNo !== v) this._intClientNo = v; },
    get_intMasterLoginNo: function () { return this._intMasterLoginNo; }, set_intMasterLoginNo: function (v) { if (this._intMasterLoginNo !== v) this._intMasterLoginNo = v; },


    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
      //  this.addCancel(Function.createDelegate(this, this.cancelClicked));
    },
    getFormControlID: function (ParentId, controlID) {
        var str = "";
        str = String.format("{0}_{1}", ParentId, controlID);
        return str;
    },
    formShown: function () {
        if (this._blnFirstTimeShown) {
            $("#ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlRequirementforTraceability_ctl02").hide();
            $("#ctl00_cphMain_ctlNotify_ctlDB_ctl14_ctlNotify_ctlDB_ddlType_ctl02").hide();
            $find(this.getFormControlID(this._element.id, 'ddlRequirementforTraceability')).getData();
            $find(this.getFormControlID(this._element.id, 'ddlType')).getData();
            $get(this.getFormControlID(this._element.id, 'txtDateRequired')).value = $R_FN.shortDate();
            // $find(this.getFormControlID(this._element.id, 'ddlType')).setValue(14);
            this.getContact();
            debugger;
            //if ($find(this.getField("ctlCompany").ControlID)) $find(this.getField("ctlCompany").ControlID)._aut.addSelectionMadeEvent(Function.createDelegate(this, this.getContact));

        }
        //this._ctlMail._autLoginOrGroup._intGlobalLoginClientNo = this._intClientNo;
    },
    getContact: function () {
        getCompanyAndOtherMasterData.call(this);

    },
    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ctlMail) this._ctlMail.dispose();
        this._ctlMail = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._intMasterLoginNo = null;

        Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.callBaseMethod(this, "dispose");
    },

    getMessageText: function () {
        Rebound.GlobalTrader.Site.WebServices.GetMailMessage_NotifyNPR(this._intNPRID, Function.createDelegate(this, this.getMessageTextComplete));
    },

    getMessageTextComplete: function (strMsg) {
        this._ctlMail.setValue_Body(strMsg);
        this._ctlMail.setValue_Subject(String.format($R_RES.NotifyNPR, this._strNPRNo));
        this._ctlMail.addNewLoginRecipient(this._intBuyerId, this._strBuyerName);
    },
    //[001] code start
    sendMail: function () {
        if (!this.validateForm()) return;
        this.showLoading(true);
        this.enableButton(false);
        Rebound.GlobalTrader.Site.WebServices.NotifyNPRMessage($R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginIDs), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupIDs), this._ctlMail.getValue_Subject(), this._ctlMail.getValue_Body(), "", this._intNPRID, this._intGoodsInLineId, $R_FN.arrayToSingleString(this._ctlMail._aryRecipientLoginNames, "/"), $R_FN.arrayToSingleString(this._ctlMail._aryRecipientGroupNames, "/"), Function.createDelegate(this, this.sendMailComplete));
    },
    //[001] code end
    validateForm: function () {
        var blnOK = this._ctlMail.validateFields();
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    sendMailComplete: function () {
        this.showLoading(false);
        this.showSavedOK(true);
        location.href = $RGT_gotoURL_GoodsIn(this._intGoodsIn);
        this.enableButton(true);
    },
    cancelClicked: function () {
        /* $R_FN.navigateBack();*/
        window.location.href = window.location.href;
        //this.showLeftMenu();
    },
    enableButton: function (bln) {
        $R_IBTN.enableButton(this._ibtnSend, bln);
        $R_IBTN.enableButton(this._ibtnSend_Footer, bln);
    },
    impotExcelData: function (originalFilename, generatedFilename, clientId, iscolumnheaderchk, FormatId, Delimiter, strfiletype) {
        $('#divLoader').show();
        //clientId = $("#ddlClient option:selected").val();
        clientId = $('#hdnClient').val();
        var ctrl = originalFilename;
        var check = '.json';
        var test = ctrl.includes(check);
        var test2 = false;
        if (strfiletype == 'json') {
            test2 = true;
        };
        debugger;
        if (clientId > 0 && test == test2) {
            DefaultCurrency = $("#ddlMainCurrency option:selected").text();
            if ($("#txtBomName").val() == '') {
                $("#txtBomName").val(originalFilename);
            }
            var obj = new Rebound.GlobalTrader.Site.Data();
            obj._intTimeoutMilliseconds = 200000;
            obj.set_PathToData("controls/Nuggets/CompanyApiCustomer/");
            obj.set_DataObject("CompanyApiCustomer");
            obj.set_DataAction("ImportData");
            obj.addParameter("originalFilename", originalFilename);
            obj.addParameter("generatedFilename", generatedFilename);
            obj.addParameter("ClientId", clientId);
            obj.addParameter("ColumnHeader", iscolumnheaderchk);
            obj.addParameter("FormatId", FormatId);
            obj.addParameter("Delimiter", Delimiter);
            obj.addParameter("DefaultCurrency", DefaultCurrency);
            obj.addParameter("strfiletype", strfiletype);
            //obj.addParameter("SelectedClientType", Client_type);
            //obj.addParameter("percentage", this._decPercentage);
            obj.addDataOK(Function.createDelegate(this, this.importExcelDataOK));
            obj.addError(Function.createDelegate(this, this.importExcelDataError));
            obj.addTimeout(Function.createDelegate(this, this.importExcelDataError));
            $R_DQ.addToQueue(obj);
            $R_DQ.processQueue();
        }
        else {
            alert('Please Check the file type.');
            $('#divLoader').hide();
        }

        obj = null;
    },
    importExcelDataOK: function (args) {
        // alert("test1");
        flogId = args._result.FileLogId;
        //bindBomGridData.call(this);
        $('#divLoader').hide();
        $("#btnDisplayCsvData").prop('disabled', false).css('opacity', 5.5);
        $("#excelipload").prop('disabled', true).css('opacity', 0.5);
        $('input:checkbox[id="chkFileCCH"]').prop('disabled', true);
        $('input:file').filter(function () {
            return this.files.length == 0
        }).prop('disabled', true);

        var IsLimitExceeded = args._result.IsLimitExceeded;
        if (IsLimitExceeded == true) {
            alert(args._result.LimitErrorMessage);
            $("#btnDisplayCsvData").prop('disabled', true).css('opacity', 0.5);
            //RP-25
            this.clearPreviousUploadedData();
            // $("#btnGenerateData").prop('disabled', true).css('opacity', 0.5);

        }
        else {
            $("#btnDisplayCsvData").prop('disabled', false).css('opacity', 5.5);
            // $("#btnGenerateData").prop('disabled', true).css('opacity', 0.5);
        }
    },
    importExcelDataError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        $('#divLoader').hide();
    },

    /*  flush the data having max rows rows count which user upload so that no junk data stays in db.*/
    clearPreviousUploadedData: function () {
        var selectedClientId = $("#ddlClient option:selected").val();
        //var projname = window.location.pathname.replace("Utility_BOMImport.aspx", "");
        //handlerUrl = window.location.origin + (projname.length == 1 ? "" : projname) + "/controls/Nuggets/UtilityBOMImport/UtilityBOMImport.ashx";
        var projname = window.location.pathname.replace("Utility_BOMManagerImport.aspx", "");
        handlerUrl = window.location.origin + (projname.length == 1 ? "" : projname) + "/controls/Nuggets/CompanyApiCustomer/ApiMapping//UtilityBOMManagerImport2.ashx";

        $.ajax({
            processData: false,
            contentType: 'application/json',
            type: 'POST',
            url: handlerUrl + '?action=DeleteRecord&SelectedclientId=' + selectedClientId,
            async: false,
            success: function (data) {
            },
            error: function () {
                //alert("Error occured while deleting record.");
            },
        });
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CompanyApiCustomer_MappingAdd", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
