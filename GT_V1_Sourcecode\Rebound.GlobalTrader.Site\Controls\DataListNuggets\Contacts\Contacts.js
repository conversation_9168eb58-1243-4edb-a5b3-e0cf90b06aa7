Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DataListNuggets");Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts=function(n){Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.prototype={get_enmContactListType:function(){return this._enmContactListType},set_enmContactListType:function(n){this._enmContactListType!==n&&(this._enmContactListType=n)},get_IsGlobalLogin:function(){return this._IsGlobalLogin},set_IsGlobalLogin:function(n){this._IsGlobalLogin!==n&&(this._IsGlobalLogin=n)},initialize:function(){this.addInitCompleteEvent(Function.createDelegate(this,this.initAfterBaseIsReady));this.addSetupDataCallEvent(Function.createDelegate(this,this.setupDataCall));this.addGetDataOKEvent(Function.createDelegate(this,this.getDataOK));this._strPathToData="controls/DataListNuggets/Contacts";this._strDataObject="Contacts";Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.callBaseMethod(this,"initialize")},initAfterBaseIsReady:function(){this.updateFilterVisibility();this.addPageTabChangedEvent(Function.createDelegate(this,this.pageTabChanged));this.getData()},dispose:function(){this.isDisposed||(this._enmContactListType=null,this._IsGlobalLogin=null,Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.callBaseMethod(this,"dispose"))},pageTabChanged:function(){this._table._intCurrentPage=1;this._enmViewLevel=this._intCurrentTab;this.updateFilterVisibility();this.getData()},setupDataCall:function(){this._objData.addParameter("ViewLevel",this._enmViewLevel);this._objData.addParameter("IsGlobalLogin",this._IsGlobalLogin)},getDataOK:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[$RGT_nubButton_Contact(n.ID,n.Name?n.Name:"???"),$R_FN.setCleanTextValue(n.Title),$RGT_nubButton_Company(n.CoID,n.Co),$R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(n.Tel),$R_FN.setCleanTextValue(n.Email)),$R_FN.setCleanTextValue(n.SMan),$R_FN.setCleanTextValue(n.ClientName)],this._table.addRow(i,n.ID),i=null,n=null},updateFilterVisibility:function(){this.getFilterField("ctlClientName").show(this._IsGlobalLogin)}};Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts.registerClass("Rebound.GlobalTrader.Site.Controls.DataListNuggets.Contacts",Rebound.GlobalTrader.Site.Controls.DataListNuggets.Base,Sys.IDisposable);