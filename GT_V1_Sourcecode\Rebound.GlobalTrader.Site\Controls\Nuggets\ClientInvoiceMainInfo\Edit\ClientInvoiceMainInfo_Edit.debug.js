///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//Marker     Changed by      Date               Remarks
//[001]      Vinay           27/05/2013         CR:- Client Invoice
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit = function(element) {
    Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.initializeBase(this, [element]);
    this._intClientInvoiceID = -1;
    this._CurrencyCode = "";
    this._TaxRate = 0;
    this._TaxNo = 0;
};

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.prototype = {

    get_intClientInvoiceID: function() { return this._intClientInvoiceID; }, set_intClientInvoiceID: function(value) { if (this._intClientInvoiceID !== value) this._intClientInvoiceID = value; },
    get_lblCurrency_InvoiceAmount: function() { return this._lblCurrency_InvoiceAmount; }, set_lblCurrency_InvoiceAmount: function(value) { if (this._lblCurrency_InvoiceAmount !== value) this._lblCurrency_InvoiceAmount = value; },
    get_lblCurrency_GoodsInValue: function() { return this._lblCurrency_GoodsInValue; }, set_lblCurrency_GoodsInValue: function(value) { if (this._lblCurrency_GoodsInValue !== value) this._lblCurrency_GoodsInValue = value; },
    get_lblCurrency_Tax: function() { return this._lblCurrency_Tax; }, set_lblCurrency_Tax: function(value) { if (this._lblCurrency_Tax !== value) this._lblCurrency_Tax = value; },
    get_lblCurrency_DeliveryCharge: function() { return this._lblCurrency_DeliveryCharge; }, set_lblCurrency_DeliveryCharge: function(value) { if (this._lblCurrency_DeliveryCharge !== value) this._lblCurrency_DeliveryCharge = value; },
    get_lblCurrency_BankFee: function() { return this._lblCurrency_BankFee; }, set_lblCurrency_BankFee: function(value) { if (this._lblCurrency_BankFee !== value) this._lblCurrency_BankFee = value; },
    get_lblCurrency_CreditCardFee: function() { return this._lblCurrency_CreditCardFee; }, set_lblCurrency_CreditCardFee: function(value) { if (this._lblCurrency_CreditCardFee !== value) this._lblCurrency_CreditCardFee = value; },

    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addSave(Function.createDelegate(this, this.saveClicked));
    },

    formShown: function() {
       // this.updateTaxRate();
        if (this._blnFirstTimeShown) {
            $find(this.getField("ctlCurrency").ControlID).addChanged(Function.createDelegate(this, this.updateCurrency));
           // $find(this.getField("ctlddlTax").ControlID).addChanged(Function.createDelegate(this, this.updateTaxRate));
        }
       // alert("ki");
        this.getFieldDropDownData("ctlCurrency");
        this.getFieldDropDownData("ctlddlTax");
        
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._intClientInvoiceID = null;
        this._CurrencyCode = null;
        this._TaxRate = null;
        this._TaxNo = null;
        Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.callBaseMethod(this, "dispose");
    },

    updateCurrency: function() {
        this._CurrencyCode =  this.getFieldDropDownExtraText("ctlCurrency");
        $R_FN.setInnerHTML(this._lblCurrency_InvoiceAmount, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_GoodsInValue, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_Tax, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_DeliveryCharge, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_BankFee, this._CurrencyCode);
        $R_FN.setInnerHTML(this._lblCurrency_CreditCardFee, this._CurrencyCode);
    },

    saveClicked: function() {
        if (!this.validateForm()) return;
       // if (!this.validateTaxRate()) return;
       // alert(this._CurrencyCode);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");
        obj.set_DataObject("ClientInvoiceMainInfo");
        obj.addParameter("id", this._intClientInvoiceID);
        obj.set_DataAction("SaveEdit");
        //obj.addParameter("ClientInvoiceNumber", this.getFieldValue("ctlClientInvoice"));
        obj.addParameter("ClientInvoiceDate", this.getFieldValue("ctlInvoiceDate"));
        obj.addParameter("CurrencyNo", this.getFieldValue("ctlCurrency"));
        obj.addParameter("Amount", this.getFieldValue("ctlInvoiceAmount"));
        obj.addParameter("GoodsValue", this.getFieldValue("ctlGoodsValue"));
        obj.addParameter("Tax", this.getFieldValue("ctlTax"));
        obj.addParameter("DeliveryCharge", this.getFieldValue("ctlDeliveryCharge"));
        obj.addParameter("BankFee", this.getFieldValue("ctlBankFee"));
        obj.addParameter("CreditCardFee", this.getFieldValue("ctlCreditCardFee"));
       // obj.addParameter("CanBeExported", this.getFieldValue("ctlCanExported"));
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addParameter("TaxNo", this.getFieldValue("ctlddlTax"));
        obj.addParameter("TaxCode", this.getFieldDropDownExtraText("ctlddlTax"));
        obj.addParameter("CurrencyCode", this._CurrencyCode);
        obj.addParameter("SecondRef", $R_FN.setCleanTextValue(this.getFieldValue("ctlSecondRef")));
        obj.addParameter("Narrative", $R_FN.setCleanTextValue(this.getFieldValue("ctlNarrative")));
        //obj.addParameter("URNNumber", this.getFieldValue("ctlURNNumber"));
        obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
        obj.addError(Function.createDelegate(this, this.saveEditError));
        obj.addTimeout(Function.createDelegate(this, this.saveEditError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    saveEditError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveEditComplete: function(args) {
        if (args._result.Result == true) {
            this.showSavedOK(true);
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    validateForm: function() {
        this.onValidate();
        var blnOK = true;
       // if (!this.checkFieldEntered("ctlClientInvoice")) blnOK = false;
        if (!this.checkFieldEntered("ctlInvoiceDate")) blnOK = false;
        if (!this.checkFieldEntered("ctlCurrency")) blnOK = false;
       // if (!this.checkFieldEntered("ctlTax")) blnOK = false;
       // if (!this.checkFieldEntered("ctlddlTax")) blnOK = false;
        if ((this.getFieldValue("ctlSecondRef").length > 16)) {
            this.showError(true, $R_RES.SecondRefMessage);
            blnOK = false;
            return blnOK;
        }
        if ((this.getFieldValue("ctlNarrative").length > 41)) {
            this.showError(true, $R_RES.NarrativeMessage);
            blnOK = false;
            return blnOK;
        }
        //var TotalValue = parseFloat(this.getFieldValue("ctlGoodsValue")) + parseFloat((this.getFieldValue("ctlTax")) ? this.getFieldValue("ctlTax") : "0") + parseFloat((this.getFieldValue("ctlDeliveryCharge")) ? this.getFieldValue("ctlDeliveryCharge") : "0") + parseFloat((this.getFieldValue("ctlBankFee")) ? this.getFieldValue("ctlBankFee") : "0") + parseFloat((this.getFieldValue("ctlCreditCardFee")) ? this.getFieldValue("ctlCreditCardFee") : "0");
        //if (parseFloat(this.getFieldValue("ctlInvoiceAmount")).toFixed(5) != TotalValue.toFixed(5)) {
        //    this.showError(true, $R_RES.InvoiceAmountMessage);
        //    blnOK = false;
        //    return blnOK;
        //}
        if (!blnOK) this.showError(true);
        return blnOK;
    },

    updateTaxRate: function() {
        this.getTaxRate();
    },

    getTaxRate: function() {
        if (this.getFieldValue("ctlddlTax"))
            this._TaxNo = this.getFieldValue("ctlddlTax");
        
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/ClientInvoiceMainInfo");
        obj.set_DataObject("ClientInvoiceMainInfo");
        obj.set_DataAction("GetTaxRate");
        obj.addParameter("TaxNo", this._TaxNo);
        obj.addDataOK(Function.createDelegate(this, this.getTaxRateComplete));
        obj.addError(Function.createDelegate(this, this.getTaxRateError));
        obj.addTimeout(Function.createDelegate(this, this.getTaxRateError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTaxRateError: function(args) {
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    getTaxRateComplete: function(args) {
        if (args._result) {
            this._TaxRate = args._result.Rate;
        }
    },

    validateTaxRate: function() {
        var blnOK = true;
        if (parseFloat(this._TaxRate) <= 0) {
            blnOK = (parseFloat(this.getFieldValue("ctlTax")) == 0);
            if (!blnOK) {
                blnOK = confirm($R_RES.TaxValueMessage);

            }
        }
        else {
            blnOK = (parseFloat(this.getFieldValue("ctlTax")) > 0);
            if (!blnOK) {
                blnOK = confirm($R_RES.TaxValueMessage);
            }
        }
        if (!blnOK) this.showError(true);
        return blnOK;
    }

};

Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.ClientInvoiceMainInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
