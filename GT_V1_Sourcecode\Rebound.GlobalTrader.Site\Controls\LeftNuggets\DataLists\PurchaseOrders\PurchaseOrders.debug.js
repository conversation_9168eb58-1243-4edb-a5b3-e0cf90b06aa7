///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//
// RP 23.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists");

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrders = function(element) { 
	Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrders.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrders.prototype = {

	get_enmViewLevel: function() { return this._enmViewLevel; }, 	set_enmViewLevel: function(value) { if (this._enmViewLevel !== value)  this._enmViewLevel = value; }, 

	initialize: function() {
		this.addGetDataOKEvent(Function.createDelegate(this, this.getDataOK));
		//this.addInitCompleteEvent(Function.createDelegate(this, this.initAfterBaseComplete));
		this._strPathToData = "controls/DataListNuggets/PurchaseOrders";
		this._strDataObject = "PurchaseOrders";
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrders.callBaseMethod(this, "initialize");
	},

	dispose: function() { 
		if (this.isDisposed) return;
		this._enmViewLevel = null;
		Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrders.callBaseMethod(this, "dispose");
	},

	getDataOK: function() {
		for (var i = 0, l = this._objResult.Results.length; i < l; i++) {
			var row = this._objResult.Results[i];
			var strData = String.format("<a href=\"{0}\"><b>{1}</b> - {2}", $RGT_gotoURL_PurchaseOrder(row.ID), row.No, $R_FN.setCleanTextValue(row.CM));
			if (row.Part.length > 0) strData += String.format("<br />{0} x {1}</a>", row.Quantity, $R_FN.writePartNo(row.Part, row.ROHS));
			strData += "</a>";
			this._tbl.addRow([ strData ], row.ID, false);
			strData = null; row = null;
		}
	}

};
Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrders.registerClass("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.PurchaseOrders", Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Base, Sys.IDisposable);
