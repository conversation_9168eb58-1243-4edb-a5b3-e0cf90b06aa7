<%@ Control Language="C#" CodeBehind="SalesOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.ItemSearch.SalesOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_ItemSearch:DesignBase id="ctlDB" runat="server" InitialSortDirection="DESC">
	<FieldsLeft>
		<ReboundUI_FilterDataItemRow:Numerical id="ctlSalesOrderNo" runat="server" ResourceTitle="SalesOrderNo" />
		<ReboundUI_FilterDataItemRow:TextBox id="crlPartno" runat="server" ResourceTitle="Partno" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCompany" runat="server" ResourceTitle="Company" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlContact" runat="server" ResourceTitle="Contact" />
		<ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" />
	</FieldsLeft>
	<FieldsRight>
		<ReboundUI_FilterDataItemRow:DropDown id="ctlSalesman" runat="server" ResourceTitle="Salesman" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" />
		<ReboundUI_FilterDataItemRow:TextBox id="ctlCustomerPO" runat="server" ResourceTitle="CustomerPO" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" />
		<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" />
	</FieldsRight>	
</ReboundUI_ItemSearch:DesignBase>
