///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO = function (element) {
    Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.prototype = {

	get_pnlPartsOrdered: function() { return this._pnlPartsOrdered; }, set_pnlPartsOrdered: function(value) { if (this._pnlPartsOrdered !== value)  this._pnlPartsOrdered = value; }, 
	get_tblPartsOrdered: function() { return this._tblPartsOrdered; }, set_tblPartsOrdered: function(value) { if (this._tblPartsOrdered !== value)  this._tblPartsOrdered = value; }, 
	get_pnlMore: function() { return this._pnlMore; }, set_pnlMore: function(value) { if (this._pnlMore !== value)  this._pnlMore = value; }, 

	initialize: function() {
	    Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.callBaseMethod(this, "initialize");
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		if (this._tblPartsOrdered) this._tblPartsOrdered.dispose();
		this._pnlPartsOrdered = null;
		this._tblPartsOrdered = null;
		this._pnlMore = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.callBaseMethod(this, "dispose");
	},
	
	setupLoadingState: function() {
		$R_FN.showElement(this._pnlMore, false);
		$R_FN.showElement(this._pnlPartsOrdered, false);
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.callBaseMethod(this, "setupLoadingState");
	},
	
	showNoData: function(bln) {
		this.showContent(true);
		$R_FN.showElement(this._pnlNoData, bln);
	},
	
	getData: function() {
		this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/UncheckedIPO");
		obj.set_DataObject("UncheckedIPO");
		obj.set_DataAction("GetData");
		
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function(args) {
		var result = args._result;
		$R_FN.showElement(this._pnlMore, true);
		var aryData, row;
		this._tblPartsOrdered.clearTable();
		for (var i = 0; i < result.UnapprovedIPOToday.length; i++) {
		    row = result.UnapprovedIPOToday[i];
			aryData = [
				$RGT_nubButton_InternalPurchaseOrder(row.ID, row.No),
				row.IsPOHub == true ?row.CompanyName:$RGT_nubButton_Company(row.CompanyId, row.CompanyName),
			    row.EmployeeName,
				row.Date
				];
			this._tblPartsOrdered.addRow(aryData, null);
		}
		$R_FN.showElement(this._pnlPartsOrdered, result.UnapprovedIPOToday.length > 0);
		this.hideLoading();
		this.showNoneFoundOrContent(result.UnapprovedIPOToday.length);
	}
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.UncheckedIPO", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
