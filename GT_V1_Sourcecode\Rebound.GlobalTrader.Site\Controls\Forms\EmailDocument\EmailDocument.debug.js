///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//Marker     changed by      date          Remarks
//[001]      Vinay           21/08/2014    Generate COC as PDF
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.initializeBase(this, [element]);
    this._strMessage = "";
    this._strDocumentText = "";
    this._strHeaderImage = "";
    this._strHeaderImageID = "";
    this._strSubject = "";
    this._IsNotesEnable = false;
    this._IsFormatEnable = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.prototype = {

    get_strLoginEmail: function () { return this._strLoginEmail; }, set_strLoginEmail: function (value) { if (this._strLoginEmail !== value) this._strLoginEmail = value; },
    get_ibtnSend: function () { return this._ibtnSend; }, set_ibtnSend: function (value) { if (this._ibtnSend !== value) this._ibtnSend = value; },
    get_ibtnSend_Footer: function () { return this._ibtnSend_Footer; }, set_ibtnSend_Footer: function (value) { if (this._ibtnSend_Footer !== value) this._ibtnSend_Footer = value; },
    get_ibtnContinue: function () { return this._ibtnContinue; }, set_ibtnContinue: function (value) { if (this._ibtnContinue !== value) this._ibtnContinue = value; },
    get_ibtnContinue_Footer: function () { return this._ibtnContinue_Footer; }, set_ibtnContinue_Footer: function (value) { if (this._ibtnContinue_Footer !== value) this._ibtnContinue_Footer = value; },
    get_strDocumentText: function () { return this._strDocumentText; }, set_strDocumentText: function (value) { if (this._strDocumentText !== value) this._strDocumentText = value; },
    get_strHeaderImage: function () { return this._strHeaderImage; }, set_strHeaderImage: function (value) { if (this._strHeaderImage !== value) this._strHeaderImage = value; },
    get_strHeaderImageID: function () { return this._strHeaderImageID; }, set_strHeaderImageID: function (value) { if (this._strHeaderImageID !== value) this._strHeaderImageID = value; },
    get_strSubject: function () { return this._strSubject; }, set_strSubject: function (value) { if (this._strSubject !== value) this._strSubject = value; },
    //[001] code start
    get_blnEmailPDF: function () { return this._blnEmailPDF; }, set_blnEmailPDF: function (value) { if (this._blnEmailPDF !== value) this._blnEmailPDF = value; },
    get_intInvoiceNo: function () { return this._intInvoiceNo; }, set_intInvoiceNo: function (value) { if (this._intInvoiceNo !== value) this._intInvoiceNo = value; },
    get_PDFDocumentType: function () { return this._PDFDocumentType; }, set_PDFDocumentType: function (value) { if (this._PDFDocumentType !== value) this._PDFDocumentType = value; },
    get_strSignatureImage: function () { return this._strSignatureImage; }, set_strSignatureImage: function (value) { if (this._strSignatureImage !== value) this._strSignatureImage = value; },
    get_strSignatureImageID: function () { return this._strSignatureImageID; }, set_strSignatureImageID: function (value) { if (this._strSignatureImageID !== value) this._strSignatureImageID = value; },
    //[001] code end
    get_TermConditionImage: function () { return this._TermConditionImage; }, set_TermConditionImage: function (value) { if (this._TermConditionImage !== value) this._TermConditionImage = value; },
    get_TermConditionImageID: function () { return this._TermConditionImageID; }, set_TermConditionImageID: function (value) { if (this._TermConditionImageID !== value) this._TermConditionImageID = value; },
    get_SectionName: function () { return this._SectionName; }, set_SectionName: function (value) { if (this._SectionName !== value) this._SectionName = value; },
    get_SubSectionName: function () { return this._SubSectionName; }, set_SubSectionName: function (value) { if (this._SubSectionName !== value) this._SubSectionName = value; },
    get_termsPathForClient: function () { return this._termsPathForClient; }, set_termsPathForClient: function (value) { if (this._termsPathForClient !== value) this._termsPathForClient = value; },
    get_IsNotesEnable: function () { return this._IsNotesEnable; }, set_IsNotesEnable: function (value) { if (this._IsNotesEnable !== value) this._IsNotesEnable = value; },
    get_IsFormatEnable: function () { return this._IsFormatEnable; }, set_IsFormatEnable: function (value) { if (this._IsFormatEnable !== value) this._IsFormatEnable = value; },
    get_ClientNo: function () { return this._ClientNo; }, set_ClientNo: function (value) { if (this._ClientNo !== value) this._ClientNo = value; },
    get_DocumentFormat: function () { return this._DocumentFormat; }, set_DocumentFormat: function (value) { if (this._DocumentFormat !== value) this._DocumentFormat = value; },
    get_AllowGenerateXml: function () { return this._AllowGenerateXml; }, set_AllowGenerateXml: function (value) { if (this._AllowGenerateXml !== value) this._AllowGenerateXml = value; },

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnSend) $R_IBTN.clearHandlers(this._ibtnSend);
        if (this._ibtnSend_Footer) $R_IBTN.clearHandlers(this._ibtnSend_Footer);
        if (this._ibtnContinue) $R_IBTN.clearHandlers(this._ibtnContinue);
        if (this._ibtnContinue_Footer) $R_IBTN.clearHandlers(this._ibtnContinue_Footer);
        if (this._ctlTo) this._ctlTo.dispose();
        this._strMessage = null;
        this._strDocumentText = null;
        this._strHeaderImage = null;
        this._strHeaderImageID = null;
        this._strSubject = null;
        this._ibtnSend = null;
        this._ibtnSend_Footer = null;
        this._ibtnContinue = null;
        this._ibtnContinue_Footer = null;
        this._ctlTo = null;
        this._strLoginEmail = null;
        //[001] code start
        this._blnEmailPDF = null;
        this._intInvoiceNo = null;
        this._PDFDocumentType = null;
        this._strSignatureImage = null;
        this._strSignatureImageID = null;
        //[001] code start
        this._TermConditionImage = null;
        this._TermConditionImageID = null;
        this._SectionName = null;
        this._SubSectionName = null;
        this._termsPathForClient = null;
        this._IsFormatEnable = null;
        Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.callBaseMethod(this, "dispose");
    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            var fnSend = Function.createDelegate(this, this.sendClicked);
            $R_IBTN.addClick(this._ibtnSend, fnSend);
            if (this._ibtnSend_Footer) $R_IBTN.addClick(this._ibtnSend_Footer, fnSend);
            var fnContinue = Function.createDelegate(this, this.continueClicked);
            $R_IBTN.addClick(this._ibtnContinue, fnContinue);
            if (this._ibtnContinue_Footer) $R_IBTN.addClick(this._ibtnContinue_Footer, fnContinue);
            this._ctlTo = $find(this.getField("ctlTo").ID);
        }
        this.setFormFieldsToDefaults();
        this.setFieldValue("ctlReplyTo", this._strLoginEmail);
        $R_IBTN.showButton(this._ibtnContinue, false);
        if (this._ibtnContinue_Footer) $R_IBTN.showButton(this._ibtnContinue_Footer, false);
        $R_IBTN.showButton(this._ibtnSend, true);
        if (this._ibtnSend_Footer) $R_IBTN.showButton(this._ibtnSend_Footer, true);
        $R_IBTN.showButton(this._ibtnCancel, true);
        if (this._ibtnCancel_Footer) $R_IBTN.showButton(this._ibtnCancel_Footer, true);
        this.showFormField("ctlNotes", this._IsNotesEnable);
        this.showInvoiceFormat();
    },

    sendClicked: function () {
        this.showSendForm();
        if (!this.validateForm()) return;
        this.showSaving(true);
        // alert(this._strDocumentText);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Forms/EmailDocument");
        obj.set_DataObject("EmailDocument");
        obj.set_DataAction("SendEmail");
        obj.addParameter("Addresses", this._ctlTo.getValuesAsString());
        obj.addParameter("ReplyTo", this.getFieldValue("ctlReplyTo"));
        obj.addParameter("Subject", this.getFieldValue("ctlSubject"));
        //obj.addParameter("DocumentText", this._IsNotesEnable == true ? (this._strDocumentText + "<br />" + this.getFieldValue("ctlNotes").replace(/\n/g, '<br/>') + "<br /> ") : this._strDocumentText);
        obj.addParameter("DocumentText", this._IsNotesEnable == true ? (this._strDocumentText.replace("EmailNotest", this.getFieldValue("ctlNotes").replace(/\n/g, '<br/>'))) : this._strDocumentText.replace("EmailNotest", ""));
        //obj.addParameter("DocumentText", this._strDocumentText.replace("EmailNotest", this.getFieldValue("ctlNotes").replace(/\n/g, '<br/>')));
        obj.addParameter("Notes", this._IsNotesEnable == true ? ("<br />" + this.getFieldValue("ctlNotes").replace(/\n/g, '<br/>') + "<br /> ") : "");
        obj.addParameter("NotesLog", this._IsNotesEnable == true ? (this.getFieldValue("ctlNotes").replace(/\n/g, '<br/>') + "<br /> ") : "");
        obj.addParameter("HeaderImage", this._strHeaderImage);
        obj.addParameter("HeaderImageID", this._strHeaderImageID);
        //[001] code start
        obj.addParameter("blnEmailPDF", this._blnEmailPDF);
        obj.addParameter("InvoiceNo", this._intInvoiceNo);
        obj.addParameter("DocType", this._PDFDocumentType);
        obj.addParameter("SignatureImage", this._strSignatureImage);
        obj.addParameter("SignatureImageID", this._strSignatureImageID);
        //[001] code end
        obj.addParameter("TermConditionImage", this._TermConditionImage);
        obj.addParameter("TermConditionImageID", this._TermConditionImageID);
        obj.addParameter("SectionName", this._SectionName);
        obj.addParameter("SubSection", this._SubSectionName);
        obj.addParameter("TermsPathForClient", this._termsPathForClient);
        obj.addParameter("IncludeCustomTemplate", this.getFieldValue("ctlIncludeCustomTemplate"));
        obj.addParameter("InvoiceFormat", this.getInvoiceFormat());
        obj.addParameter("DocumentFormat", this._DocumentFormat);
        obj._intTimeoutMilliseconds = 1000 * 20; //20 seconds
        obj.addDataOK(Function.createDelegate(this, this.sendComplete));
        obj.addError(Function.createDelegate(this, this.sendError));
        obj.addTimeout(Function.createDelegate(this, this.sendError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    sendError: function (args) {
        this.showSendError(args._errorMessage);
        this.showContent(true);
        this.showInnerContent(true);
    },

    sendComplete: function (args) {       
        if (args._result.Result == true) {
            this.showSendOK();
            $R_IBTN.showButton(this._ibtnContinue, true);
            if (this._ibtnContinue_Footer) $R_IBTN.showButton(this._ibtnContinue_Footer, true);
            $R_IBTN.showButton(this._ibtnSend, false);
            if (this._ibtnSend_Footer) $R_IBTN.showButton(this._ibtnSend_Footer, false);
            $R_IBTN.showButton(this._ibtnCancel, false);
            if (this._ibtnCancel_Footer) $R_IBTN.showButton(this._ibtnCancel_Footer, false);
            if (this._SectionName = "Quote" && (this._PDFDocumentType == "QO" || this._PDFDocumentType == "COCF")) {
                this.updateQuoteStatus();
            }
        } else {
            this.showSendError(args._errorMessage);
        }
    },
    updateQuoteStatus: function () {
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("controls/Nuggets/QuoteMainInfo");
        obj.set_DataObject("QuoteMainInfo");
        obj.set_DataAction("UpdatePrintQuoteStatus");
        obj.addParameter("id", this._intInvoiceNo);
        obj._intTimeoutMilliseconds = 90 * 1000;
        obj.addDataOK(Function.createDelegate(this, this.updateQuoteStatusComplete));
        obj.addError(Function.createDelegate(this, this.updateQuoteStatusError));
        obj.addTimeout(Function.createDelegate(this, this.updateQuoteStatusError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },
    updateQuoteStatusError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    updateQuoteStatusComplete: function (args) {
        if (args._result.Result) {
            window.opener.postMessage(args._result.Result, location.origin); //"*"
        } else {
            //alert("Error");
        }
    },

    validateForm: function () {
        this.onValidate();
        var blnOK = this.autoValidateFields();
        if (!this._ctlTo.validateAllFields()) blnOK = false;
        return blnOK;
    },

    showSendForm: function () {
        this.showContent(true);
        this.showInnerContent(true);
        this.showFormField("ctlSendError", false);
        this._ctlTo.show(true);
        this.showFormField("ctlReplyTo", true);
        this.showFormField("ctlSubject", true);
    },

    showSendError: function (strMsg) {
        this.showContent(true);
        this.showInnerContent(true);
        this.showFormField("ctlSendError", true);
        this._ctlTo.show(true);
        this.showFormField("ctlReplyTo", true);
        this.showFormField("ctlSubject", true);
    },

    showSendOK: function () {
        this.showContent(true);
        this.showInnerContent(true);
        this.showFormField("ctlSendError", false);
        this._ctlTo.show(false);
        this.showFormField("ctlReplyTo", false);
        this.showFormField("ctlSubject", false);
        this.showSavedOK(true);
    },

    continueClicked: function () {
        this.onSaveComplete();
    },

    showInvoiceFormat: function () {
        if (!this._IsFormatEnable) {
            this.showFormField("ctlFormat", false);
            return;
        }
        this.showFormField("ctlFormat", true);

        if (!this._AllowGenerateXml) {
            document.querySelector('.optionXML').classList.add('invisible');
            document.querySelector('.optionPDF input[type="radio"]').checked = true;
            return;
        }
        document.querySelector('.optionXML').classList.remove('invisible');
        if (this._ClientNo == '108') {
            document.querySelector('.optionXML input[type="radio"]').checked = true;
        } else {
            document.querySelector('.optionPDF input[type="radio"]').checked = true;
        }
    },
    getInvoiceFormat: function () {
        if (!this._IsFormatEnable || !this._AllowGenerateXml) return 'PDF';
        return document.querySelector('.optionXML input[type="radio"]').checked ? 'XML' : 'PDF';
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
