Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.prototype={get_blnLimitToCurrentUsersDivision:function(){return this._blnLimitToCurrentUsersDivision},set_blnLimitToCurrentUsersDivision:function(n){this._blnLimitToCurrentUsersDivision!==n&&(this._blnLimitToCurrentUsersDivision=n)},get_blnLimitToCurrentUsersTeam:function(){return this._blnLimitToCurrentUsersTeam},set_blnLimitToCurrentUsersTeam:function(n){this._blnLimitToCurrentUsersTeam!==n&&(this._blnLimitToCurrentUsersTeam=n)},get_blnExcludeCurrentUser:function(){return this._blnExcludeCurrentUser},set_blnExcludeCurrentUser:function(n){this._blnExcludeCurrentUser!==n&&(this._blnExcludeCurrentUser=n)},initialize:function(){this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK));Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.callBaseMethod(this,"initialize")},dispose:function(){this.isDisposed||(this._blnLimitToCurrentUsersDivision=null,this._blnLimitToCurrentUsersTeam=null,this._blnExcludeCurrentUser=null,Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/EmployeeByLevel");this._objData.set_DataObject("EmployeeByLevel");this._objData.set_DataAction("GetData");this._objData.addParameter("LimitToCurrentUsersTeam",this._blnLimitToCurrentUsersTeam);this._objData.addParameter("LimitToCurrentUsersDivision",this._blnLimitToCurrentUsersDivision);this._objData.addParameter("ExcludeCurrentUser",this._blnExcludeCurrentUser)},dataCallOK:function(){var t=this._objData._result,n;if(t.Employees)for(n=0;n<t.Employees.length;n++)this.addOption(t.Employees[n].Name,t.Employees[n].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.EmployeeByLevel",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);