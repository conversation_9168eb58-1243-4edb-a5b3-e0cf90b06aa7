/* Marker     changed by      date         Remarks
/* [0001]      A<PERSON><PERSON><PERSON>   14/07/2014  ESMS Ref:172 - Issue in Part Searching in whole GT */

using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.ItemSearch.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
	public class InvoiceLinesSoc : Rebound.GlobalTrader.Site.Data.ItemSearch.Base {

		protected override void GetData() {
			List<InvoiceLine> lst = null;
			try {
				lst = InvoiceLine.ItemSearchSoc(
					SessionManager.ClientID,
					GetFormValue_NullableInt("Order", 0),
					GetFormValue_NullableInt("SortDir", SortColumnDirection.ASC),
					GetFormValue_NullableInt("PageIndex", 0),
					GetFormValue_NullableInt("PageSize", 10),
				    //[0001] start code
                    //, GetFormValue_StringForPartSearch("Part")
                     GetFormValue_PartForLikeSearch("Part"),
                    //[0001] end code
					GetFormValue_String("Contact"),
					//GetFormValue_StringForNameSearch("CMName"),
                    GetFormValue_StringForNameSearchDecode("CMName"),
					GetFormValue_NullableInt("Salesman"),
					GetFormValue_String("CustomerPO"),
					GetFormValue_Boolean("IncludePaid"),
					GetFormValue_NullableInt("InvoiceNoLo"),
					GetFormValue_NullableInt("InvoiceNoHi"),
					GetFormValue_NullableInt("SONoLo"),
					GetFormValue_NullableInt("SONoHi"),
					GetFormValue_NullableDateTime("DateInvoicedFrom"),
					GetFormValue_NullableDateTime("DateInvoicedTo")
					);
				JsonObject jsn = new JsonObject();
				JsonObject jsnItems = new JsonObject(true);
				for (int i = 0; i < lst.Count; i++) {
					JsonObject jsnItem = new JsonObject();
					jsnItem.AddVariable("ID", lst[i].InvoiceLineId);
					jsnItem.AddVariable("No", lst[i].InvoiceNumber);
					jsnItem.AddVariable("CMNo", lst[i].CompanyNo);
					jsnItem.AddVariable("CMName", lst[i].CompanyName);
					jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].InvoiceDate));
					jsnItem.AddVariable("Price", Functions.FormatCurrency(lst[i].Price, lst[i].CurrencyCode));
					jsnItem.AddVariable("Quantity", lst[i].Quantity);
					jsnItem.AddVariable("Part", lst[i].Part);
					jsnItem.AddVariable("CustomerPO", lst[i].CustomerPO);
					jsnItem.AddVariable("SalesOrderNo", lst[i].SalesOrderNumber);
					jsnItem.AddVariable("ROHS", lst[i].ROHS);
					jsnItem.AddVariable("Cost", Functions.FormatCurrency(lst[i].Cost, lst[i].CurrencyCode));
					jsnItems.AddVariable(jsnItem);
					jsnItem.Dispose();
					jsnItem = null;
				}
				jsn.AddVariable("Count", (lst.Count > 0) ? lst[0].RowCnt : 0);
				jsn.AddVariable("Results", jsnItems);
				OutputResult(jsn);
				jsnItems.Dispose();
				jsnItems = null;
				jsn.Dispose();
				jsn = null;
			} catch (Exception ex) {
				WriteError(ex);
			} finally {
				lst = null;
			}
			base.GetData();
		}
	}
}
