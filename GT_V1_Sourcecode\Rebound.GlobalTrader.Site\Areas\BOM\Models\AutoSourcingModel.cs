﻿using System;

namespace Rebound.GlobalTrader.Site.Areas.BOM.Models
{
    public class AutoSourcingModel
    {
        public int SourceId { get; set; }
        public int BOMManagerId { get; set; }
        public int AlterCRNumber { get; set; }
        public int Supplier { get; set; }
        public string SupplierName { get; set; }
        public string PartNo { get; set; }
        public int ROHS { get; set; }
        public int ManufacturerNo { get; set; }
        public string ManufacturerName { get; set; }
        public string DateCode { get; set; }
        public int ProductNo { get; set; }
        public string ProductName { get; set; }
        public int? PackageNo { get; set; }
        public string PackageName { get; set; }
        public int Quantity { get; set; }
        public double Price { get; set; }
        public int Currency { get; set; }
        public int OfferStatus { get; set; }
        public string SupplierTotalQSA { get; set; }
        public string SupplierMOQ { get; set; }
        public string SupplierLTB { get; set; }
        public int MSLNo { get; set; }
        public int? SPQ { get; set; }
        public string LeadTime { get; set; }
        public string FactorySealed { get; set; }
        public string ROHSStatus { get; set; }
        public string Notes { get; set; }
        public int? SupplierWarranty { get; set; }
        public int? CountryOfOrigin { get; set; }
        public double SellPrice { get; set; }
        public double ShippingCost { get; set; }
        public int Region { get; set; }
        public DateTime? DeliveryDate { get; set; }
        public bool? IsTestingRecommended { get; set; }
        public string Reason { get; set; }
    }
}