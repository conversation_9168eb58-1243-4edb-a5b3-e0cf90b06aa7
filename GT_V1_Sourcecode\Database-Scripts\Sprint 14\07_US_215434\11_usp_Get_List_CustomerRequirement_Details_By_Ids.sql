﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_Get_List_CustomerRequirement_Details_By_Ids]    Script Date: 11/8/2024 10:42:35 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


/*
==================================================================================================================================================   
TASK			UPDATED BY			DATE			ACTION		DESCRIPTION  
[US-213152]		Trung Pham Van		03-Oct-2024		CREATE		Get CustomerRequirements Details By Ids
[US-213152]		Trung Pham Van		10-Oct-2024		UPDATE		Refactor scripts just return  some fields and convert price per USD
[US-213152]		Trung Pham Van		14-Oct-2024		UPDATE		Remove round. update currency
[US-215434]		Phuc Hoang			06-Nov-2024		Update			Lytica Price should apply fuzzy logic for inserting & displaying
================================================================================================================================================== 
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Get_List_CustomerRequirement_Details_By_Ids]
	@CustomerRequirementIds VARCHAR(MAX)
AS
BEGIN
	-- Get the client currency number
    DECLARE @ClientCurrencyNo INT, @CurrencyCode VARCHAR(MAX), @GlobalClientCurrencyNo INT
    SELECT @ClientCurrencyNo = CurrencyNo FROM tbClient WHERE ClientId = 114
	SELECT @CurrencyCode = CurrencyCode,  @GlobalClientCurrencyNo = GlobalCurrencyNo FROM tbCurrency WHERE CurrencyId = @ClientCurrencyNo

	;WITH PartPrice AS (
		SELECT cr.CustomerRequirementId, cr.Price, c.GlobalCurrencyNo
		FROM tbCustomerRequirement cr
		JOIN tbCurrency c ON c.CurrencyId = cr.CurrencyNo
		WHERE cr.CustomerRequirementId IN (SELECT CAST(value AS INT) FROM STRING_SPLIT(@CustomerRequirementIds, ','))
	)

	SELECT cr.BOMNo,
		cr.CustomerRequirementId,
		cr.ReceivedDate,
		co.CompanyName,
		cr.Part,
		cr.CustomerPart,
		mf.ManufacturerName,
		cr.DateCode,
		pr.ProductName,
		pk.PackageName,
		pp.Price AS Price,
		pp.GlobalCurrencyNo AS ReqGlobalCurrencyNo,
		@GlobalClientCurrencyNo AS CurrencyNo,
		cr.Quantity,
		ISNULL(
			(ihs.AveragePrice / dbo.ufn_get_exchange_rate(cur.CurrencyId, ihs.DLUP)) * dbo.ufn_get_exchange_rate(@ClientCurrencyNo, ihs.DLUP),
			ISNULL(dbo.ufn_extract_IHS_AvgPrice(ihs.Descriptions), 0)
		) AS IHSAvgPrice,
        ROUND(ISNULL(cr.LyticaAveragePrice, ISNULL(lytica.AveragePrice, 0)),2) AS LyticaAvgPrice,
		co.Salesman,
		l.EmployeeName,
		@CurrencyCode AS Currency,
	    @ClientCurrencyNo AS CurrencyNo

	FROM tbCustomerRequirement cr
	JOIN PartPrice pp ON pp.CustomerRequirementId = cr.CustomerRequirementId
	JOIN tbBOM bom ON bom.BOMId = cr.BOMNo
	JOIN tbCompany co ON bom.CompanyNo = co.CompanyId
	JOIN tbLogin l ON l.LoginId = co.Salesman
	LEFT JOIN dbo.tbManufacturer mf ON cr.ManufacturerNo = mf.ManufacturerId
	LEFT JOIN dbo.tbProduct pr ON cr.ProductNo = pr.ProductId
	LEFT JOIN dbo.tbPackage pk ON cr.PackageNo = pk.PackageId
	LEFT JOIN tbIHSparts ihs ON ihs.FullPart = cr.Part AND ihs.ManufacturerFullName = mf.ManufacturerName
	LEFT JOIN tbCurrency cur ON cur.CurrencyCode = ihs.ColPriceCurrency
    --LEFT JOIN tbLyticaAPI lytica ON dbo.ufn_get_fullpart(OriginalPartSearched) = cr.Part AND lytica.Manufacturer = mf.ManufacturerName
	OUTER APPLY (
		SELECT  TOP 1 *
			FROM tbLyticaAPI
			WHERE OriginalPartSearched = cr.Part 
			AND ISNULL(Inactive, 0) = 0
			AND (ISNULL(AveragePrice, 0) + ISNULL(TargetPrice, 0) + ISNULL(MarketLeading, 0)) > 0
			AND (
				Manufacturer = ISNULL(mf.ManufacturerName, '') 
				OR Manufacturer LIKE ISNULL(mf.ManufacturerName, '')  + '%'
				OR mf.ManufacturerName LIKE ISNULL(Manufacturer, '') + '%' 
				OR Manufacturer LIKE ISNULL([dbo].[ufn_GetFirstWord](mf.ManufacturerName), '') + '%'
			)
	) lytica
	WHERE ISNULL(ihs.Inactive, 0) = 0 

	GROUP BY cr.BOMNo,
		cr.CustomerRequirementId,
		cr.ReceivedDate,
		co.CompanyName,
		cr.Part,
		cr.CustomerPart,
		mf.ManufacturerName,
		cr.DateCode,
		pr.ProductName,
		pk.PackageName,
		pp.Price,
		pp.GlobalCurrencyNo,
		cr.Quantity,
		ihs.AveragePrice,
		cur.CurrencyId,
		ihs.DLUP,
		ihs.Descriptions,
		cr.LyticaAveragePrice,
		lytica.AveragePrice,
		cur.CurrencyCode,
		co.Salesman,
		l.EmployeeName
END
GO


