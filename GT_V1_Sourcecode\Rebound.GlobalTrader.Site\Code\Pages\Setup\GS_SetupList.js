Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Setup");Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList=function(n){Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.prototype={get_ctlLabelSetupItem:function(){return this._ctlLabelSetupItem},set_ctlLabelSetupItem:function(n){this._ctlLabelSetupItem!==n&&(this._ctlLabelSetupItem=n)},get_ctlLabelSetup:function(){return this._ctlLabelSetup},set_ctlLabelSetup:function(n){this._ctlLabelSetup!==n&&(this._ctlLabelSetup=n)},initialize:function(){Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.callBaseMethod(this,"initialize")},goInit:function(){this._ctlLabelSetup.addSelectLabelSetup(Function.createDelegate(this,this.ctlLabelSetupItem_addLabelSetup));Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.callBaseMethod(this,"goInit")},dispose:function(){this.isDisposed||(this._ctlLabelSetupItem&&this._ctlLabelSetupItem.dispose(),this._ctlLabelSetup&&this._ctlLabelSetup.dispose(),this._ctlLabelSetupItem=null,this._ctlLabelSetup=null,Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.callBaseMethod(this,"dispose"))},ctlLabelSetupItem_SelectProduct:function(){this._ctlLabelSetupItem._tbl.resizeColumns()},ctlLabelSetupItem_addLabelSetup:function(){this._ctlLabelSetupItem._intLabelSetupID=this._ctlLabelSetup._intLabelSetupID;this._ctlLabelSetupItem.show(!0);this._ctlLabelSetup._tbl.resizeColumns();this._ctlLabelSetupItem.refresh()}};Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList.registerClass("Rebound.GlobalTrader.Site.Pages.Setup.GS_SetupList",Rebound.GlobalTrader.Site.Pages.Content,Sys.IDisposable);