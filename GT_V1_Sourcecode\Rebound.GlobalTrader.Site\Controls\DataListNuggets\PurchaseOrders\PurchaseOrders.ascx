  <%-- Marker     changed by      date         Remarks  
       [001]      V<PERSON><PERSON> kumar     22/11/2011  ESMS Ref:21 - Add Country search option in PO 
       [002]      A<PERSON><PERSON><PERSON>  29/07/2021   Add new dropdown for supplier approval.
       [003]      Ab<PERSON>av <PERSON>a  07/12/2021   Remove Supplier Approval Filter.
	   [004]      Ravi            19-09-2023      RP-2338  AS6081 Search/Filter functionality on different pages
      --%>
<%@ Control Language="C#" CodeBehind="PurchaseOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.PurchaseOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo" TextBoxMaxLength="10" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
		         <ReboundUI_FilterDataItemRow:CheckBox id="ctlPohubOnly" runat="server" ResourceTitle="PohubOnly" FilterField="PohubOnly" DefaultValue="true"  />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlIncludeClosed" runat="server" ResourceTitle="IncludeClosed" FilterField="IncludeClosed" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlCompanyName" runat="server" ResourceTitle="CompanyName" FilterField="CMName" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyerName" runat="server" ResourceTitle="BuyerName" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="BuyerName" />
				<%--[001]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlCountry" runat="server" ResourceTitle="Country" DropDownType="Country" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Country" />				
                <ReboundUI_FilterDataItemRow:DropDown id="ctlCheckedStatus" runat="server" ResourceTitle="CheckedStatus" DropDownType="SOStatus" DropDownAssembly="Rebound.GlobalTrader.Site"  FilterField="SOStatus"/>
				<%--[001]Code End--%>
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedFrom" runat="server" ResourceTitle="DateOrderedFrom" FilterField="DateOrderedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateOrderedTo" runat="server" ResourceTitle="DateOrderedTo" FilterField="DateOrderedTo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDeliveryDateFrom" runat="server" ResourceTitle="DeliveryDateFrom" FilterField="DeliveryDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDeliveryDateTo" runat="server" ResourceTitle="DeliveryDateTo" FilterField="DeliveryDateTo" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlExpediteDateFrom" runat="server" ResourceTitle="ExpediteDateFrom" FilterField="ExpediteDateFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlExpediteDateTo" runat="server" ResourceTitle="ExpediteDateTo" FilterField="ExpediteDateTo" />
				<ReboundUI_FilterDataItemRow:Numerical id="ctlIPO" runat="server" ResourceTitle="IPONo" FilterField="IPONo" />
				<%--[002]Code Start--%>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlClientName" runat="server" ResourceTitle="ClientName" DropDownType="Client" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Client"  />				
				<%--[002]Code End--%>
                <ReboundUI_FilterDataItemRow:DropDown id="ctlStatus" runat="server" ResourceTitle="status" DropDownType="PurchaseOrderStatus" DropDownAssembly="Rebound.GlobalTrader.Site"  FilterField="PurchaseOrderStatus"/>
                <ReboundUI_FilterDataItemRow:DropDown id="ctlSupplierApproval" runat="server" ResourceTitle="SupplierApproval" DropDownType="SupplierApprovalStatus" DropDownAssembly="Rebound.GlobalTrader.Site"  FilterField="SupplierApprovalStatus"/>
				<ReboundUI_FilterDataItemRow:DropDown id="ctlAS6081" runat="server" ResourceTitle="AS6081Filternew" DropDownType="CounterfeitElectronicParts" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="AS6081" />  <%--[004]--%>
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
