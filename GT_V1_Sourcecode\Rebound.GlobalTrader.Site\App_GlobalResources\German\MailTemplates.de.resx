﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="EmailCertificateOfConformance" xml:space="preserve">
    <value>Finden Sie bitte beigefügte Bescheinigung der Übereinstimmung {0} 

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailCreditNote" xml:space="preserve">
    <value>Finden Sie bitte anbrachte Sonderkommandos der Kreditnote {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailCustomerRMA" xml:space="preserve">
    <value>Finden Sie bitte anbrachte Sonderkommandos des Kunden RMA {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailDebitNote" xml:space="preserve">
    <value>Finden Sie bitte anbrachte Sonderkommandos der Belastungsanzeige {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailInvoice" xml:space="preserve">
    <value>Finden Sie bitte angebrachte Rechnung {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailPackingSlip" xml:space="preserve">
    <value>Finden Sie bitte angebrachten Verpackungs-Beleg {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailProFormaInvoice" xml:space="preserve">
    <value>Finden Sie bitte angebrachte Proformarechnung {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailPurchaseOrder" xml:space="preserve">
    <value>Finden Sie bitte anbrachte Sonderkommandos des Kaufauftrags {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailQuote" xml:space="preserve">
    <value>Finden Sie bitte angebrachte Sonderkommandos des Preisangabe{0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailSalesOrder" xml:space="preserve">
    <value>Finden Sie bitte anbrachte Sonderkommandos des Verkaufs-Auftrages {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="EmailSupplierRMA" xml:space="preserve">
    <value>Finden Sie bitte anbrachte Sonderkommandos des Lieferanten RMA {0}

Mit freundlichen Grüßen 
{1}</value>
  </data>
  <data name="NewCredit" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neue Kreditnote&lt;/a&gt; addiert:

Nummer: [#CREDIT_NUMBER#]
Kunde: [#CUSTOMER#]
Contact: [#CONTACT#]
Rechnungs-Zahl: [#INV_NUMBER#]
Kunde PO: [#CUSTOMER_PO#]
Verkäufer: [#SALESMAN#]</value>
  </data>
  <data name="NewCustomerRequirement" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neue Kunden-Anforderung&lt;/a&gt; addiert:

Nummer: [#CUSREQ_NUMBER#]
Kunde: [#CUSTOMER#]
Quantität: [#QUANTITY#]
Teilenummer: [#PART#]
Kunde Teilenummer Part No: [#CUSTOMERPART#]
Hersteller: [#MANUFACTURER#]
DateCode: [#DATECODE#]
Produkt: [#PRODUCT#]
Paket: [#PACKAGE#]
Richtpreis: [#PRICE#]
Datum erforderte: [#DATEREQUIRED#]</value>
  </data>
  <data name="NewCustomerRMA" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neuer Kunde RMA&lt;/a&gt; addiert:

Nummer: [#CRMA_NUMBER#]
Kunde: [#CUSTOMER#]
Kontakt: [#CONTACT#]
Rechnungs-Zahl: [#INVOICE_NUMBER#]
Vorbei autorisiert: [#AUTHORISED_BY#]</value>
  </data>
  <data name="NewDebit" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neue Belastungsanzeige&lt;/a&gt; addiert:

Nummer: [#DEBIT_NUMBER#]
Lieferant: [#SUPPLIER#]
Kontakt: [#CONTACT#]
Kaufauftrag: [#PO_NUMBER#]
Lieferant RMA: [#SRMA_NUMBER#]
Lieferant Invoice: [#SUPPLIER_INV#]
Käufer: [#BUYER#]</value>
  </data>
  <data name="NewGoodsIn" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neue Waren in der Anmerkung&lt;/a&gt; addiert:


Nummer: [#GI_NUMBER#]
Lieferant: [#SUPPLIER#]
PO Nummer: [#PO_NUMBER#]</value>
  </data>
  <data name="NewInvoice" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neue Rechnung&lt;/a&gt;:

Nummer: [#INVOICE_NUMBER#]
Kunder: [#CUSTOMER#]
Kontakt: [#CONTACT#]
Verkaufs-Auftrag: [#SO_NUMBER#]
Kunde PO: [#CUSTOMER_PO#]
Verkäufer: [#SALESMAN#]</value>
  </data>
  <data name="NewPurchaseOrder" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;new Purchase Order&lt;/a&gt; addiert:

Nummer: [#PO_NUMBER#]
Lieferant: [#SUPPLIER#]
Kontakt: [#CONTACT#]
Käufer: [#BUYER#]</value>
  </data>
  <data name="NewQuote" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neuerw Preisangabe&lt;/a&gt; addiert:

Nummer: [#QUOTE_NUMBER#]
Kunde: [#CUSTOMER#]
Kontakt: [#CONTACT#]
Währung: [#CURRENCY#]
Datum: [#DATE#]</value>
  </data>
  <data name="NewSalesOrder" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neuer Verkaufs-Auftrag&lt;/a&gt;:

Nummer: [#SO_NUMBER#]
Kunde: [#CUSTOMER#]
Kontakt: [#CONTACT#]
Verkäufer: [#SALESMAN#]</value>
  </data>
  <data name="NewSupplierRMA" xml:space="preserve">
    <value>Ich habe eine &lt;a href="[#HYPERLINK#]"&gt;neuer Lieferant RMA&lt;/a&gt; addiert:


Nummer: [#SRMA_NUMBER#]
Lieferant: [#SUPPLIER#]
Kontakt: [#CONTACT#]
PO Nummer: [#PO_NUMBER#]
Vorbei autorisiert: [#AUTHORISED_BY#]</value>
  </data>
  <data name="NotifyGoodsIn" xml:space="preserve">
    <value>Wiederholen Sie bitte dieses &lt;a href="[#HYPERLINK#]"&gt;Waren in der Anmerkung&lt;/a&gt;:

Nummer: [#GI_NUMBER#]
Lieferant: [#SUPPLIER#]
Vorbei empfangen: [#RECEIVED_BY#]
PO Nummer: [#PO_NUMBER#]</value>
  </data>
  <data name="NotifySalesOrder" xml:space="preserve">
    <value>Wiederholen Sie bitte dieses &lt;a href="[#HYPERLINK#]"&gt;Verkaufs-Auftrag&lt;/a&gt;:

Nummer: [#SO_NUMBER#]
Kunde: [#CUSTOMER#]
Kontakt: [#CONTACT#]
Verkäufer: [#SALESMAN#]</value>
  </data>
  <data name="ReceivedPurchaseOrder" xml:space="preserve">
    <value>Ich habe Kaufauftrag empfangen [#PO_NUMBER#] aus &lt;a href="[#HYPERLINK#]"&gt;Waren in der Anmerkung [#GI_NUMBER#]&lt;/a&gt;:

Nummer: [#GI_NUMBER#]
Lieferant: [#SUPPLIER#]
PO Nummer: [#PO_NUMBER#]</value>
  </data>
  <data name="RFQ" xml:space="preserve">
    <value>Bestätigen Sie bitte Preis und Verwendbarkeit für das folgende, danken Ihnen</value>
  </data>
</root>