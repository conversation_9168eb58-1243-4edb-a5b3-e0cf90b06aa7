﻿using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using System.Collections.Generic;
using System.Web.Caching;
using System.Data.Common;

namespace Rebound.GlobalTrader.DAL.SqlClient
{
    public class SqlWarningMessageProvider : WarningMessageProvider
    {
        public override bool Delete(System.Int32? systemDocumentFooterNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_delete_SystemDocumentFooter", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SystemDocumentFooterNo", SqlDbType.Int).Value = systemDocumentFooterNo;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to delete SystemDocumentFooter", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Create a new row
        /// Calls [usp_insert_SystemWarningMessage]
        /// </summary>
        public override Int32 Insert(System.Int32? clientNo, System.Int32? WarningNo, System.String WarningText, System.Int32? ApplyCatagoryNo, System.Int32? ApplyTo, System.Int32? updatedBy)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_insert_SystemWarningMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@WarningNo", SqlDbType.Int).Value = WarningNo;
                cmd.Parameters.Add("@WarningText", SqlDbType.NVarChar).Value = WarningText;
                cmd.Parameters.Add("@ApplyCatagoryNo", SqlDbType.Int).Value = ApplyCatagoryNo;
                cmd.Parameters.Add("@ApplyTo", SqlDbType.Int).Value = ApplyTo;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to insert System Warning Message.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

        /// <summary>
        /// Create a new row
        /// Calls [usp_CheckDuplicate_SystemWarningMessage]
        /// </summary>
        public override Int32 CheckDuplicate(System.Int32? clientNo, System.Int32? WarningNo, System.Int32? ApplyCatagoryNo, System.Int32? ApplyTo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_CheckDuplicate_SystemWarningMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@WarningNo", SqlDbType.Int).Value = WarningNo;
                cmd.Parameters.Add("@ApplyCatagoryNo", SqlDbType.Int).Value = ApplyCatagoryNo;
                cmd.Parameters.Add("@ApplyTo", SqlDbType.Int).Value = ApplyTo;
                cmd.Parameters.Add("@NewId", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (Int32)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to check duplicate warning message.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Create a new row
        /// Calls [usp_CheckDuplicate_SystemWarningMessage]
        /// </summary>
        public override bool CheckApplyCatagory(System.Int32? WarningNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_CheckApplyCatagory_SystemWarningMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@WarningNo", SqlDbType.Int).Value = WarningNo;
                cmd.Parameters.Add("@NewId", SqlDbType.Bit).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (bool)cmd.Parameters["@NewId"].Value;
            }
            catch (SqlException sqlex)
            {
                throw new Exception("Failed to check duplicate warning message.", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }
        /// <summary>
        /// Get 
        /// Calls [usp_select_SystemDocumentFooter]
        /// </summary>
        public override WarningMessageDetails Get(System.Int32? SystemWarningMessageNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_SystemWarningMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@SystemWarningMessageNo", SqlDbType.Int).Value = SystemWarningMessageNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    WarningMessageDetails obj = new WarningMessageDetails();
                    obj.SystemWarningMessageId = GetReaderValue_Int32(reader, "SystemWarningMessageId", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.WarningKey = GetReaderValue_String(reader, "WarningKey", "");
                    obj.WarningText = GetReaderValue_String(reader, "WarningText", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.ApplyToCatagory = GetReaderValue_String(reader, "ApplyToCatagory", "");
                    obj.ApplyTo = GetReaderValue_String(reader, "ApplyTo", "");
                    obj.InActive= GetReaderValue_Boolean(reader, "InActive", false);
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SystemDocumentFooter", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetForClientAndDocument 
        /// Calls [usp_select_SystemDocumentFooter_for_Client_and_Document]
        /// </summary>
        public override SystemDocumentFooterDetails GetForClientAndDocument(System.Int32? clientNo, System.Int32? systemDocumentNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_select_SystemDocumentFooter_for_Client_and_Document", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@SystemDocumentNo", SqlDbType.Int).Value = systemDocumentNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd, CommandBehavior.SingleRow);
                if (reader.Read())
                {
                    //return GetSystemDocumentFooterFromReader(reader);
                    SystemDocumentFooterDetails obj = new SystemDocumentFooterDetails();
                    obj.FooterText = GetReaderValue_String(reader, "FooterText", "");
                    return obj;
                }
                else
                {
                    return null;
                }
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get SystemDocumentFooter", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// GetListForClient 
        /// Calls [usp_selectAll_SystemDocumentFooter_for_Client]
        /// </summary>
        public override List<WarningMessageDetails> GetListForClient(System.Int32? clientNo)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_selectAll_SystemWarningMessage_for_Client", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.CommandTimeout = 30;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cn.Open();
                DbDataReader reader = ExecuteReader(cmd);
                List<WarningMessageDetails> lst = new List<WarningMessageDetails>();
                while (reader.Read())
                {
                    WarningMessageDetails obj = new WarningMessageDetails();
                    obj.SystemWarningMessageId = GetReaderValue_Int32(reader, "SystemWarningMessageId", 0);
                    obj.ClientNo = GetReaderValue_Int32(reader, "ClientNo", 0);
                    obj.WarningText = GetReaderValue_String(reader, "WarningText", "");
                    obj.UpdatedBy = GetReaderValue_NullableInt32(reader, "UpdatedBy", null);
                    obj.DLUP = GetReaderValue_NullableDateTime(reader, "DLUP", null);
                    obj.WarningKey = GetReaderValue_String(reader, "WarningKey", "");
                    obj.ApplyToCatagory = GetReaderValue_String(reader, "ApplyToCatagory", "");
                    obj.ApplyTo = GetReaderValue_String(reader, "ApplyTo", "");
                    obj.InActive = GetReaderValue_Boolean(reader, "InActive", false);
                    obj.ProductName = GetReaderValue_String(reader, "ProductName", "");
                    obj.ProductDescription = GetReaderValue_String(reader, "ProductDescription", "");
                    obj.GlobalProductNameId = GetReaderValue_Int32(reader, "GlobalProductNameId", 0);
                    obj.ProductId = GetReaderValue_Int32(reader, "ProductId", 0);
                    obj.GlobalProductCategoryId = GetReaderValue_Int32(reader, "GlobalProductcategoryId", 0);
                    lst.Add(obj);
                    obj = null;
                }
                return lst;
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to get warning messages", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }


        /// <summary>
        /// Update SystemDocumentFooter
        /// Calls [usp_update_SystemDocumentFooter]
        /// </summary>
        public override bool Update(System.Int32? SystemWarningMessageId, System.Int32? clientNo, System.String WarningText, System.Int32? updatedBy, System.Boolean? InActive)
        {
            SqlConnection cn = null;
            SqlCommand cmd = null;
            try
            {
                cn = new SqlConnection(this.ConnectionString);
                cmd = new SqlCommand("usp_update_SystemWarningMessage", cn);
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.Add("@SystemWarningMessageId", SqlDbType.Int).Value = SystemWarningMessageId;
                cmd.Parameters.Add("@ClientNo", SqlDbType.Int).Value = clientNo;
                cmd.Parameters.Add("@WarningText", SqlDbType.NVarChar).Value = WarningText;
                cmd.Parameters.Add("@UpdatedBy", SqlDbType.Int).Value = updatedBy;
                cmd.Parameters.Add("@InActive", SqlDbType.Bit).Value = InActive;
                cmd.Parameters.Add("@RowsAffected", SqlDbType.Int).Direction = ParameterDirection.Output;
                cn.Open();
                int ret = ExecuteNonQuery(cmd);
                return (ret > 0);
            }
            catch (SqlException sqlex)
            {
                //LogException(sqlex);
                throw new Exception("Failed to update SystemDocumentFooter", sqlex);
            }
            finally
            {
                cmd.Dispose();
                cn.Close();
                cn.Dispose();
            }
        }

    }
}
