//-----------------------------------------------------------------------------------------
// RP 26.10.2009:
// - new control
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site;
using Rebound.GlobalTrader.Site.Controls;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists {

    public partial class Debits : Base {

		protected override void OnInit(EventArgs e) {
			SetLeftNuggetType("DataList_Debits");
			SetDataListNuggetType("Debits");
			LoadDataListNugget(_objDataListNugget.Name);
			TitleResource = "QuickBrowse";
			base.OnInit(e);
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.LeftNuggets.DataLists.Debits", ctlDesignBase.ClientID);
			AddScriptReference("Controls.LeftNuggets.DataLists.Debits.Debits");
			SetupTable();
		}

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("DebitNote", Unit.Empty, true));
		}

		protected override void AddNewFilterItemsToStartOfList() {
			AddNewDropDownFilter("ViewLevel", "Rebound.GlobalTrader.Site", "ViewLevel", "ViewLevel", "ViewLevel", Convert.ToInt32(SessionManager.DefaultListPageView));
			ShowFilter("ViewLevel", true);
			SetFilterValue("ViewLevel", Convert.ToInt32(SessionManager.DefaultListPageView));
			base.AddNewFilterItemsToStartOfList();
		}

        protected override void RenderAdditionalState() {
            SetFilterValue("ViewLevel", this.GetSavedStateValue("ViewLevel"));
            base.RenderAdditionalState();
        }

    }

}