///<reference name="MicrosoftAjax.js" />
///<reference name="~/Common/Functions/Functions.js" />
///<reference name="~/Common/Data/Data.js" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Pages.Dashboard");

Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards = function(el) {
Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.initializeBase(this, [el]);
};

Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.prototype = {

    get_strStockurl: function() { return this._strStockurl; }, set_strStockurl: function(v) { if (this._strStockurl !== v) this._strStockurl = v; },


    initialize: function() {
    Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.callBaseMethod(this, "initialize");
    },

    goInit: function() {

        //alert(this._strStockurl);
        document.getElementById("ifrmQlikStock").src = this._strStockurl;

        Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.callBaseMethod(this, "goInit");
    },

    dispose: function() {
        if (this.isDisposed) return;
        this._strStockurl = null;

        Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.callBaseMethod(this, "dispose");
    }



};
Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards.registerClass("Rebound.GlobalTrader.Site.Pages.Dashboard.StockDashboards", Rebound.GlobalTrader.Site.Pages.Content, Sys.IDisposable);
