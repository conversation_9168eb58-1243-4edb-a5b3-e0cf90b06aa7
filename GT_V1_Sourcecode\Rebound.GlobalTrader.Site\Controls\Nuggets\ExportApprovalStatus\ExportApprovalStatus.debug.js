﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 30.03.2010:
// - ensure line is fully loaded before we allow edits (to stop the wrong data being
//   edited)
//
// RP 06.01.2010:
// - fully dispose everything
//Marker     Changed by                 Date         Remarks
//[001]      Abhinav Saxena           28/03/2023   Add for purchase order supplier approval



//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");

Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus = function (element) {
    Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.initializeBase(this, [element]);
    this._intSalesOrderID = -1;
    //[001] code start
    this._intPurchaseOrderLineID = -1;
    //[001] code end
    this._intAllocationID = -1;
    this._blnPOClosed = false;
    this._intLineDataCalls = 0;
    this._blnDisableAllButtons = false;
    this._blnRestrictedEdit = false;
    this._enmPOStatus = 0;
    this._aryUnpostedLineIDs = [];
    this._aryPostedLineIDs = [];
    this._blnLineLoaded = false;
    this._ipoClientNo = 0;
    this._blnClientPO = false;
    this._InternalPurchaseOrderNumber = -1;
    this._PoLine = -1;
    this._Part = "";
    this._Quantity = -1;
    this._arrPOLineIds = [];
    this._IsPOLineReceived = false;
    this._intGlobalClientNo = -1;
    this._blnProdInactive = false;
    this._reqSerialNo = false;
    //[005] start
    this._IsReleased = false;
    this._IsAuthorised = false;
    this._POLineSerialNo = [];
    this._PONumber = -1;
    this._IsPOHUB = false;
    this.blnIsService = false;
    //[005] end
    // this._strProcess="start"
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.prototype = {

    get_intSalesOrderID: function () { return this._intSalesOrderID; }, set_intSalesOrderID: function (v) { if (this._intSalesOrderID !== v) this._intSalesOrderID = v; },
    get_intContactID: function () { return this._intContactID; }, set_intContactID: function (v) { if (this._intContactID !== v) this._intContactID = v; },
    get_ibtnApproval: function () { return this._ibtnApproval; }, set_ibtnApproval: function (v) { if (this._ibtnApproval !== v) this._ibtnApproval = v; },
    get_ibtnEditApproval: function () { return this._ibtnEditApproval; }, set_ibtnEditApproval: function (v) { if (this._ibtnEditApproval !== v) this._ibtnEditApproval = v; },
    get_ibtnEditAllApproval: function () { return this._ibtnEditAllApproval; }, set_ibtnEditAllApproval: function (v) { if (this._ibtnEditAllApproval !== v) this._ibtnEditAllApproval = v; },
    get_ibtnRequestApproval: function () { return this._ibtnRequestApproval; }, set_ibtnRequestApproval: function (v) { if (this._ibtnRequestApproval !== v) this._ibtnRequestApproval = v; },
    get_ctlMultiSelectionCount: function () { return this._ctlMultiSelectionCount; }, set_ctlMultiSelectionCount: function (value) { if (this._ctlMultiSelectionCount !== value) this._ctlMultiSelectionCount = value; },
    get_ibtnUnpostAll: function () { return this._ibtnUnpostAll; }, set_ibtnUnpostAll: function (v) { if (this._ibtnUnpostAll !== v) this._ibtnUnpostAll = v; },
    get_ibtnDeallocate: function () { return this._ibtnDeallocate; }, set_ibtnDeallocate: function (v) { if (this._ibtnDeallocate !== v) this._ibtnDeallocate = v; },
    get_ibtnAdd: function () { return this._ibtnAdd; }, set_ibtnAdd: function (v) { if (this._ibtnAdd !== v) this._ibtnAdd = v; },
    get_ctlTabStrip: function () { return this._ctlTabStrip; }, set_ctlTabStrip: function (v) { if (this._ctlTabStrip !== v) this._ctlTabStrip = v; },
    get_tblApproved: function () { return this._tblApproved; }, set_tblApproved: function (v) { if (this._tblApproved !== v) this._tblApproved = v; },
    get_tblAwaiting: function () { return this._tblAwaiting; }, set_tblAwaiting: function (v) { if (this._tblAwaiting !== v) this._tblAwaiting = v; },
    get_tblAll: function () { return this._tblAll; }, set_tblAll: function (v) { if (this._tblAll !== v) this._tblAll = v; },
    get_hypPrev: function () { return this._hypPrev; }, set_hypPrev: function (v) { if (this._hypPrev !== v) this._hypPrev = v; },
    get_hypNext: function () { return this._hypNext; }, set_hypNext: function (v) { if (this._hypNext !== v) this._hypNext = v; },
    get_lblLineNumber: function () { return this._lblLineNumber; }, set_lblLineNumber: function (v) { if (this._lblLineNumber !== v) this._lblLineNumber = v; },
    get_lblLineInactive: function () { return this._lblLineInactive; }, set_lblLineInactive: function (v) { if (this._lblLineInactive !== v) this._lblLineInactive = v; },
    get_pnlLineDetail: function () { return this._pnlLineDetail; }, set_pnlLineDetail: function (v) { if (this._pnlLineDetail !== v) this._pnlLineDetail = v; },
    get_pnlLoadingLineDetail: function () { return this._pnlLoadingLineDetail; }, set_pnlLoadingLineDetail: function (v) { if (this._pnlLoadingLineDetail !== v) this._pnlLoadingLineDetail = v; },
    get_pnlLineDetailError: function () { return this._pnlLineDetailError; }, set_pnlLineDetailError: function (v) { if (this._pnlLineDetailError !== v) this._pnlLineDetailError = v; },
    get_fldAllocations: function () { return this._fldAllocations; }, set_fldAllocations: function (v) { if (this._fldAllocations !== v) this._fldAllocations = v; },
    get_tblAllocations: function () { return this._tblAllocations; }, set_tblAllocations: function (v) { if (this._tblAllocations !== v) this._tblAllocations = v; },
    get_fldReceived: function () { return this._fldReceived; }, set_fldReceived: function (v) { if (this._fldReceived !== v) this._fldReceived = v; },
    get_tblReceived: function () { return this._tblReceived; }, set_tblReceived: function (v) { if (this._tblReceived !== v) this._tblReceived = v; },
    get_blnPOClosed: function () { return this._blnPOClosed; }, set_blnPOClosed: function (v) { if (this._blnPOClosed !== v) this._blnPOClosed = v; },
    get_enmPOStatus: function () { return this._enmPOStatus; }, set_enmPOStatus: function (v) { if (this._enmPOStatus !== v) this._enmPOStatus = v; },
    get_blnDisableAllButtons: function () { return this._blnDisableAllButtons; }, set_blnDisableAllButtons: function (v) { if (this._blnDisableAllButtons !== v) this._blnDisableAllButtons = v; },
    //[001] code start
    
    get_intPurchaseOrderLineID: function () { return this._intPurchaseOrderLineID; }, set_intPurchaseOrderLineID: function (v) { if (this._intPurchaseOrderLineID !== v) this._intPurchaseOrderLineID = v; },
    get_blnCanEditPriceWithoutUnpost: function () { return this._blnCanEditPriceWithoutUnpost; }, set_blnCanEditPriceWithoutUnpost: function (v) { if (this._blnCanEditPriceWithoutUnpost !== v) this._blnCanEditPriceWithoutUnpost = v; },
    get_ibtnAddExpditeNote: function () { return this._ibtnAddExpditeNote; }, set_ibtnAddExpditeNote: function (v) { if (this._ibtnAddExpditeNote !== v) this._ibtnAddExpditeNote = v; },

    //[005] start
    get_ibtnEPR: function () { return this._ibtnEPR; }, set_ibtnEPR: function (v) { if (this._ibtnEPR !== v) this._ibtnEPR = v; },
    get_pnlEPRTootTip: function () { return this._pnlEPRTootTip; }, set_pnlEPRTootTip: function (v) { if (this._pnlEPRTootTip !== v) this._pnlEPRTootTip = v; },
    get_hypNewEPR: function () { return this._hypNewEPR; }, set_hypNewEPR: function (v) { if (this._hypNewEPR !== v) this._hypNewEPR = v; },
    get_pnlEPR: function () { return this._pnlEPR; }, set_pnlEPR: function (v) { if (this._pnlEPR !== v) this._pnlEPR = v; },
    get_ibtnRelease: function () { return this._ibtnRelease; }, set_ibtnRelease: function (v) { if (this._ibtnRelease !== v) this._ibtnRelease = v; },
    

    addPotentialStatusChange: function (handler) { this.get_events().addHandler("PotentialStatusChange", handler); },
    removePotentialStatusChange: function (handler) { this.get_events().removeHandler("PotentialStatusChange", handler); },
    onPotentialStatusChange: function () {
        var handler = this.get_events().getHandler("PotentialStatusChange");
        if (handler) handler(this, Sys.EventArgs.Empty);
    },

    initialize: function () {
        $("#AllocationErrorMsg").hide();
        $("#dvtxt").html("");
        Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.callBaseMethod(this, "initialize");
        //data
        this._strDataPath = "controls/Nuggets/ExportApprovalStatus";
        this._strDataObject = "ExportApprovalStatus";
        $("#dvERAIMessage").hide();
        //tab strip
        this._ctlTabStrip.addTabIndexChanged(Function.createDelegate(this, this.tabChanged));

        //nugget events
        this.addRefreshEvent(Function.createDelegate(this, this.tabChanged));

        //control events
        this._tblApproved.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChangedForTradeRef));
        //this._tblAll.addSelectedIndexChanged(Function.createDelegate(this, this.tbl_SelectedIndexChangedForSupplierApproval));
        this._tblAll.addMultipleSelectionChanged(Function.createDelegate(this, this.tbl_SelectedIndexChangedForSupplierApproval));
        this._ctlMultiSelectionCount.registerTable(this._tblAll);
        $addHandler(this._hypPrev, "click", Function.createDelegate(this, this.prevLine));
        $addHandler(this._hypNext, "click", Function.createDelegate(this, this.nextLine));
        this._fldAllocations.addRefresh(Function.createDelegate(this, this.onRefreshAllocations));
        this._fldAllocations.addShown(Function.createDelegate(this, this.onShownAllocations));
        this._fldReceived.addRefresh(Function.createDelegate(this, this.onRefreshReceived));
        this._fldReceived.addShown(Function.createDelegate(this, this.onShownReceived));

        //edit form
        if (this._ibtnEditApproval) {
            $R_IBTN.addClick(this._ibtnEditApproval, Function.createDelegate(this, this.showEditForm));

            this._frmEdit = $find(this._aryFormIDs[2]);
            this._frmEdit.addCancel(Function.createDelegate(this, this.hideEditForm));
            this._frmEdit.addSaveComplete(Function.createDelegate(this, this.saveEditComplete));
            this._frmEdit.addNotConfirmed(Function.createDelegate(this, this.hideEditForm));
        }
        //edit all form
        if (this._ibtnEditAllApproval) {
            $R_IBTN.addClick(this._ibtnEditAllApproval, Function.createDelegate(this, this.showEditAllForm));

            this._frmEditAll = $find(this._aryFormIDs[3]);
            this._frmEditAll.addCancel(Function.createDelegate(this, this.hideEditAllForm));
            this._frmEditAll.addSaveComplete(Function.createDelegate(this, this.saveEditAllComplete));
            this._frmEditAll.addNotConfirmed(Function.createDelegate(this, this.hideEditAllForm));
        }
        if (this._ibtnApproval) {
            $R_IBTN.addClick(this._ibtnApproval, Function.createDelegate(this, this.showApprovalForm));

            this._frmApproval = $find(this._aryFormIDs[1]);
            this._frmApproval.addCancel(Function.createDelegate(this, this.hideApprovalForm));
            this._frmApproval.addSaveComplete(Function.createDelegate(this, this.saveApprovalComplete));
            this._frmApproval.addNotConfirmed(Function.createDelegate(this, this.hideApprovalForm));
        }
        if (this._ibtnAdd) {
            $R_IBTN.addClick(this._ibtnAdd, Function.createDelegate(this, this.showAddForm));
            this._frmAdd = $find(this._aryFormIDs[2]);
            this._frmAdd.addCancel(Function.createDelegate(this, this.hideAddForm));
            this._frmAdd.addSaveComplete(Function.createDelegate(this, this.saveAddComplete));
        }
        if (this._ibtnRequestApproval) {
            if (this._ibtnRequestApproval) $R_IBTN.addClick(this._ibtnRequestApproval, Function.createDelegate(this, this.showRequestApprovalForm));
            this._frmRequestApproval = $find(this._aryFormIDs[0]);
            this._frmRequestApproval.addCancel(Function.createDelegate(this, this.hideRequestApprovalForm));
            this._frmRequestApproval.addSaveComplete(Function.createDelegate(this, this.saveRequestApprovalComplete));
            this._frmRequestApproval.addNotConfirmed(Function.createDelegate(this, this.hideRequestApprovalForm));
        }
        //$R_IBTN.showButton(this._ibtnRequestApproval, false);
        this.tabChanged();
    },

    dispose: function () {
        if (this.isDisposed) return;
        if (this._ibtnEditApproval) $R_IBTN.clearHandlers(this._ibtnEditApproval);
        if (this._ibtnEditAllApproval) $R_IBTN.clearHandlers(this._ibtnEditAllApproval);
        if (this._ibtnApproval) $R_IBTN.clearHandlers(this._ibtnApproval);
        if (this._ctlMultiSelectionCount) this._ctlMultiSelectionCount.dispose();
        if (this._ibtnAdd) $R_IBTN.clearHandlers(this._ibtnAdd);
        if (this._ibtnDeallocate) $R_IBTN.clearHandlers(this._ibtnDeallocate);
        if (this._ibtnRequestApproval) $R_IBTN.clearHandlers(this._ibtnRequestApproval);
        if (this._ibtnUnpostAll) $R_IBTN.clearHandlers(this._ibtnUnpostAll);
        if (this._tblApproved) this._tblApproved.dispose();
        if (this._tblAwaiting) this._tblAwaiting.dispose();
        if (this._tblAll) this._tblAll.dispose();
        if (this._fldAllocations) this._fldAllocations.dispose();
        if (this._tblAllocations) this._tblAllocations.dispose();
        if (this._fldReceived) this._fldReceived.dispose();
        if (this._tblReceived) this._tblReceived.dispose();
        if (this._ctlTabStrip) this._ctlTabStrip.dispose();
        if (this._frmDeallocate) this._frmDeallocate.dispose();
        if (this._frmClose) this._frmClose.dispose();
        if (this._frmDelete) this._frmDelete.dispose();
        if (this._frmAdd) this._frmAdd.dispose();
        if (this._frmApproval) this._frmApproval.dispose();
        if (this._frmEdit) this._frmEdit.dispose();
        this._frmDeallocate = null;
        this._frmClose = null;
        this._frmDelete = null;
        this._frmAdd = null;
        this._frmApproval = null;
        this._frmEdit = null;
        this._intSalesOrderID = null;
        this._intContactID = null;
        this._ibtnEditApproval = null;
        this._ibtnEditAllApproval = null;
        this._ibtnApproval = null;
        this._ibtnRequestApproval = null;
        this._ctlMultiSelectionCount = null;
        this._ibtnUnpostAll = null;
        this._ibtnDeallocate = null;
        this._ibtnAdd = null;
        this._ctlTabStrip = null;
        this._tblApproved = null;
        this._tblAwaiting = null;
        this._tblAll = null;
        this._hypPrev = null;
        this._hypNext = null;
        this._lblLineNumber = null;
        this._lblLineInactive = null;
        this._pnlLineDetail = null;
        this._pnlLoadingLineDetail = null;
        this._pnlLineDetailError = null;
        this._fldAllocations = null;
        this._tblAllocations = null;
        this._fldReceived = null;
        this._tblReceived = null;
        this._blnPOClosed = null;
        this._enmPOStatus = null;
        this._blnDisableAllButtons = null;
        //[001] code start
        this._intPurchaseOrderLineID = null;
        //[001] code end
        this._blnClientPO = null;
        this._ibtnAddExpditeNote = null;
        this._arrPOLineIds = null;
        this._intGlobalClientNo = null;
        this._blnProdInactive = null;
        this._reqSerialNo = false;
        //[005] start
        this._ibtnEPR = null;
        this._pnlEPRTootTip = null;
        this._hypNewEPR = null;
        this._pnlEPR = null;
        this.ibtnRelease = null;
        this._PONumber = null;
        this._IsPOHUB = null;
        //[005] end

        Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.callBaseMethod(this, "dispose");
    },

    tabChanged: function () {
        Array.clear(this._arrPOLineIds);
        this.clearPolineFormOnEdit();
        this.getTabData();
    },

    getTabData: function () {
        $("#AllocationErrorMsg").hide();
        $("#dvtxt").html("");
        this.enableEditButtons(false);
        $R_FN.showElement(this._pnlLineDetail, false);
        switch (this._ctlTabStrip._selectedTabIndex) {
            case 0: this.getTabData_All(); break;
            case 1: this.getTabData_Approved(); break;
            case 2: this.getTabData_Awaiting(); break;
        }
    },

    getTabData_Approved: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData_Approved");
        obj.addParameter("id", this._intSalesOrderID);
        obj.addDataOK(Function.createDelegate(this, this.getTabDataOK_Approved));
        obj.addError(Function.createDelegate(this, this.getTabDataError));
        obj.addTimeout(Function.createDelegate(this, this.getTabDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTabDataOK_Approved: function (args) {
        Array.clear(this._arrPOLineIds);
        var res = args._result;
        var result = args._result;
        var tbl = this.getCurrentTable();
        tbl.enable(true);
        tbl.clearTable();
        var imagepath = "app_themes/original/images/IconButton/pdficon.jpg";
        if (this._intPurchaseOrderLineID > 0) {
            this._intLineID = this._intPurchaseOrderLineID;
        }

        if (this._ibtnAddExpditeNote) $R_IBTN.enableButton(this._ibtnAddExpditeNote, false);
        Array.clear(this._aryUnpostedLineIDs);
        Array.clear(this._aryPostedLineIDs);
        Array.clear(this._arrPOLineIds);
        if (result.Lines) {


            for (var i = 0; i < result.Lines.length; i++) {
                var row = result.Lines[i];
                
                var aryData = [
                      row.LineNo
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , (this.blnIsService) ? $R_FN.writeDoubleCellValue("", "") : $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
                    , $R_FN.setCleanTextValue(row.ExportApprovalStatus)
                    , $R_FN.setCleanTextValue(row.OGELLicenseRequired)
                    , $R_FN.setCleanTextValue(row.EUUFormRequired)
                    , $R_FN.setCleanTextValue(row.Dated)
                    , $R_FN.setCleanTextValue(row.By)
                    , $R_FN.setCleanTextValue(row.Comment)
                ];
                var objExtraData = {
                    
                };
                if (row.IsReceived) this._IsPOLineReceived = row.IsReceived;
                var strCSS = "unposted";
                if (row.IsPosted) strCSS = "posted";
                if (objExtraData.IsPartReceived) strCSS = "partReceived";
                if (objExtraData.IsReceived) strCSS = "received";
                if (row.Inactive) strCSS = "inactive";
                tbl.addRow(aryData, row.ExportApprovalId, row.ExportApprovalId == this._intLineID, objExtraData, strCSS);
                if (!row.Inactive && !row.IsAllocated && !row.IsPosted && row.QuantityReceived < 1) Array.add(this._aryUnpostedLineIDs, row.LineID);
                if (!row.Inactive && !row.IsAllocated && row.IsPosted && row.QuantityReceived < 1) Array.add(this._aryPostedLineIDs, row.LineID);
                var chk = this.getCheckBox(i, tbl);
                chk = null;

                row = null;
            }
        }
        //if (this._ibtnRequestApproval) $R_IBTN.showButton(this._ibtnRequestApproval,true);
        if (this._ibtnUnpostAll) $R_IBTN.showButton(this._ibtnUnpostAll, this._aryPostedLineIDs.length > 0);
        tbl.resizeColumns();
        if (this._intPurchaseOrderLineID > 0)
            this._intPurchaseOrderLineID = -1;
        this.getDataOK_End();
    },

    getTabData_Awaiting: function () {
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData_Awaiting");
        obj.addParameter("id", this._intSalesOrderID);
        obj.addDataOK(Function.createDelegate(this, this.getTabDataOK_Awaiting));
        obj.addError(Function.createDelegate(this, this.getTabDataError));
        obj.addTimeout(Function.createDelegate(this, this.getTabDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTabDataOK_Awaiting: function (args) {
        //this._arrPOLineIds = [];
        Array.clear(this._arrPOLineIds);
        var res = args._result;
        var result = args._result;
        var tbl = this.getCurrentTable();
        tbl.enable(true);
        tbl.clearTable();

        if (this._intPurchaseOrderLineID > 0) {
            this._intLineID = this._intPurchaseOrderLineID;
        }

        if (this._ibtnAddExpditeNote) $R_IBTN.enableButton(this._ibtnAddExpditeNote, false);
        Array.clear(this._aryUnpostedLineIDs);
        Array.clear(this._aryPostedLineIDs);
        Array.clear(this._arrPOLineIds);
        if (result.Lines) {
            for (var i = 0; i < result.Lines.length; i++) {
                var row = result.Lines[i];
                var aryData = [
                      row.LineNo
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , (this.blnIsService) ? $R_FN.writeDoubleCellValue("", "") : $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
                    , $R_FN.setCleanTextValue(row.ExportApprovalStatus)
                    , $R_FN.setCleanTextValue(row.OGELLicenseRequired)
                    , $R_FN.setCleanTextValue(row.EUUFormRequired)
                    , $R_FN.setCleanTextValue(row.Dated)
                    , $R_FN.setCleanTextValue(row.By)
                    , $R_FN.setCleanTextValue(row.Comment)
                ];
                var objExtraData = {
                   
                };
                if (row.IsReceived) this._IsPOLineReceived = row.IsReceived;
                var strCSS = "unposted";
                if (row.IsPosted) strCSS = "posted";
                if (objExtraData.IsPartReceived) strCSS = "partReceived";
                if (objExtraData.IsReceived) strCSS = "received";
                if (row.Inactive) strCSS = "inactive";
                tbl.addRow(aryData, row.ExportApprovalId, row.ExportApprovalId == this._intLineID, objExtraData, strCSS);
                if (!row.Inactive && !row.IsAllocated && !row.IsPosted && row.QuantityReceived < 1) Array.add(this._aryUnpostedLineIDs, row.LineID);
                if (!row.Inactive && !row.IsAllocated && row.IsPosted && row.QuantityReceived < 1) Array.add(this._aryPostedLineIDs, row.LineID);
                var chk = this.getCheckBox(i, tbl);
                chk = null;

                row = null;
            }
        }
        //if (this._ibtnRequestApproval) $R_IBTN.showButton(this._ibtnRequestApproval, true);
        if (this._ibtnUnpostAll) $R_IBTN.showButton(this._ibtnUnpostAll, this._aryPostedLineIDs.length > 0);
        tbl.resizeColumns();
        if (this._intPurchaseOrderLineID > 0)
            this._intPurchaseOrderLineID = -1;
        this.getDataOK_End();
    },

    getTabData_All: function () {
        this._ctlMultiSelectionCount.clearAll();
        this.getData_Start();
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData_All");
        obj.addParameter("id", this._intSalesOrderID);
        obj.addDataOK(Function.createDelegate(this, this.getTabDataOK_All));
        obj.addError(Function.createDelegate(this, this.getTabDataError));
        obj.addTimeout(Function.createDelegate(this, this.getTabDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getTabDataOK_All: function (args) {
        Array.clear(this._arrPOLineIds);
        var result =args._result;
        var tbl = this.getCurrentTable();
        tbl.enable(true);
        tbl.clearTable();
      
        if (this._intPurchaseOrderLineID > 0) {
            this._intLineID = this._intPurchaseOrderLineID;
        }
     
        if (this._ibtnAddExpditeNote) $R_IBTN.enableButton(this._ibtnAddExpditeNote, false);
        Array.clear(this._aryUnpostedLineIDs);
        Array.clear(this._aryPostedLineIDs);
        Array.clear(this._arrPOLineIds);
        if (result.Lines) {
            for (var i = 0; i < result.Lines.length; i++) {
                var row = result.Lines[i];
                
                var aryData = [  
                    $R_FN.writeDoubleCellValue(row.LineNo, row.IsExportDetailsFilled == true ? "<img src='../App_Themes/Original/images/StarRating/filled.png' title='Export Approval Data filled' id='imgQuery" + i + "'/>" : "")
                    , $R_FN.writeDoubleCellValue($R_FN.writePartNo(row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , (this.blnIsService) ? $R_FN.writeDoubleCellValue("", "") : $R_FN.writeDoubleCellValue($RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
                    , $R_FN.setCleanTextValue(row.ExportApprovalStatus)
                    , $R_FN.setCleanTextValue(row.OGELLicenseRequired)
                    , $R_FN.setCleanTextValue(row.EUUFormRequired)
                    , $R_FN.setCleanTextValue(row.Dated)
                    , $R_FN.setCleanTextValue(row.By)
                    , $R_FN.setCleanTextValue(row.Comment)
                ];
                var objExtraData = {
                    ExportApprovalStatusId: row.ExportApprovalStatusId,
                    IsAllocationDone: row.IsAllocationDone,
                    IsExportDetailsFilled: row.IsExportDetailsFilled,
                    OgelRequiredOnSO: row.OgelRequiredOnSO,
                    IsExportControlHasPDF: row.IsExportControlHasPDF
                };
                if (row.IsReceived) this._IsPOLineReceived = row.IsReceived;
                var strCSS = "unposted";
                if (row.IsPosted) strCSS = "posted";
                if (objExtraData.IsPartReceived) strCSS = "partReceived";
                if (objExtraData.IsReceived) strCSS = "received";
                if (row.Inactive) strCSS = "inactive";
                tbl.addRow(aryData, row.ExportApprovalId, row.ExportApprovalId == this._intLineID, objExtraData, strCSS);
                if (!row.Inactive && !row.IsAllocated && !row.IsPosted && row.QuantityReceived < 1) Array.add(this._aryUnpostedLineIDs, row.LineID);
                if (!row.Inactive && !row.IsAllocated && row.IsPosted && row.QuantityReceived < 1) Array.add(this._aryPostedLineIDs, row.LineID);
                var chk = this.getCheckBox(i, tbl);
                chk = null;

                row = null;
            }
        }
        //if (this._ibtnRequestApproval) $R_IBTN.showButton(this._ibtnRequestApproval, true);
        if (this._ibtnUnpostAll) $R_IBTN.showButton(this._ibtnUnpostAll, this._aryPostedLineIDs.length > 0);
        tbl.resizeColumns();
        if (this._intPurchaseOrderLineID > 0)
            this._intPurchaseOrderLineID = -1;
        //Code END
        this.getDataOK_End();
    },

    getTabDataError: function (args) {
        this.showError(true, args.get_ErrorMessage());
    },

    getCurrentTable: function () {
        var tbl;
        switch (this._ctlTabStrip._selectedTabIndex) {
            case 0: tbl = this._tblAll; break;
            case 1: tbl = this._tblApproved; break;
            case 2: tbl = this._tblAwaiting; break;
        }
        return tbl;
    },

    tbl_SelectedIndexChangedForTradeRef: function () {
        var tbl = this.getCurrentTable();
        this._intLineID = tbl._varSelectedValue;
    },
    tbl_SelectedIndexChangedForSupplierApproval: function () {
        var tbl = this.getCurrentTable();

        if (this._tblAll._aryCurrentValues.length>0) {
            if (this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).OgelRequiredOnSO == true) {
                this._intLineID = this._tblAll._aryCurrentValues[0];
                if (this._tblAll._aryCurrentValues.length == 1) {
                    if (this._ibtnEditApproval) $R_IBTN.enableButton(this._ibtnEditApproval, true);
                    debugger
                    var intExportApprovalStatusId = this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).ExportApprovalStatusId;
                    var blnAllocationdone = this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).IsAllocationDone;
                    var blnIsExportDetailsFilled = this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).IsExportDetailsFilled;
                    var blnIsExportControlHasPDF = this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[0]).IsExportControlHasPDF;
                    
                    if ((intExportApprovalStatusId == 3 || intExportApprovalStatusId == 4 || intExportApprovalStatusId == 5) && (blnAllocationdone == true)) {
                        if (blnIsExportDetailsFilled == true) {
                            if (this._ibtnApproval) $R_IBTN.enableButton(this._ibtnApproval, true);
                            if (this._ibtnRequestApproval) $R_IBTN.enableButton(this._ibtnRequestApproval, true);
                        }
                        else {
                            if (this._ibtnApproval) $R_IBTN.enableButton(this._ibtnApproval, false);
                            if (this._ibtnRequestApproval) $R_IBTN.enableButton(this._ibtnRequestApproval, false);
                        }
                        if (blnIsExportControlHasPDF) {
                            if (this._ibtnRequestApproval) $R_IBTN.enableButton(this._ibtnRequestApproval, true);
                            $("#dvRequestApprovalDisabledReason").hide()
                            $("#dvRequestApprovalDisabledReason").html("");
                        } else {
                            if (this._ibtnRequestApproval) $R_IBTN.enableButton(this._ibtnRequestApproval, false);
                            $("#dvRequestApprovalDisabledReason").show()
                            $("#dvRequestApprovalDisabledReason").html("<img height='15px' width='15px' src='../../../App_Themes/Original/images/hazardous/ihspartstatuspng.png'>");
                            $("#dvRequestApprovalDisabledReason").append("<span class='tooltiptext'>" + 'Upload Export Control Document under PDF document' + "</span>");
                        }
                        if (this._ibtnEditApproval) $R_IBTN.updateText(this._ibtnEditApproval, 'Edit Export Approval');

                    }
                    else {
                        if (this._ibtnApproval) $R_IBTN.enableButton(this._ibtnApproval, false);
                        if (this._ibtnRequestApproval) $R_IBTN.enableButton(this._ibtnRequestApproval, false);
                        if (intExportApprovalStatusId == 7 || intExportApprovalStatusId == 3 || intExportApprovalStatusId == 4 || intExportApprovalStatusId == 5) {
                            if (this._ibtnEditApproval) $R_IBTN.enableButton(this._ibtnEditApproval, true);
                            if (this._ibtnEditApproval) $R_IBTN.updateText(this._ibtnEditApproval, 'Edit Export Approval');
                        }
                        else {
                            if (this._ibtnEditApproval) $R_IBTN.updateText(this._ibtnEditApproval, 'View Export Approval');
                            if (this._ibtnEditApproval) $R_IBTN.enableButton(this._ibtnEditApproval, true);
                        }

                    }
                    if ((blnAllocationdone == false) && (intExportApprovalStatusId != 7)) {
                        $("#AllocationErrorMsg").show();
                        $("#dvtxt").html("Please do allocation for selected SO line before OGEL approval.");
                    }
                    else {
                        if ((blnIsExportDetailsFilled == false)) {
                            $("#AllocationErrorMsg").show();
                            $("#dvtxt").html('Please fill in required fields using "Edit Export Approval" screen before requesting approval.');
                        }
                        else {
                            $("#AllocationErrorMsg").hide();
                            $("#dvtxt").html("");
                        }

                    }
                }
                else {
                    if (this._ibtnEditApproval) $R_IBTN.enableButton(this._ibtnEditApproval, false);
                    if (this._ibtnApproval) $R_IBTN.enableButton(this._ibtnApproval, false);
                    if (this._ibtnRequestApproval) $R_IBTN.enableButton(this._ibtnRequestApproval, false);
                }
                if (this._tblAll._aryCurrentValues.length > 1) {
                    var ApprovedSelected = false;
                    for (var i = 0; i < this._tblAll._aryCurrentValues.length; i++) {
                        var myApprovaStatus = this._tblAll.getSelectedExtraData(this._tblAll._arySelectedIndexes[i]).ExportApprovalStatusId
                        if ((myApprovaStatus == 1) || (myApprovaStatus == 2) || (myApprovaStatus == 6) || (myApprovaStatus == 8)) {
                            ApprovedSelected = true;
                            break;
                        }
                        else {
                            ApprovedSelected = false;

                        }
                    }
                    if (ApprovedSelected == true) {
                        if (this._ibtnEditAllApproval) $R_IBTN.enableButton(this._ibtnEditAllApproval, false);
                    }
                    else {
                        if (this._ibtnEditAllApproval) $R_IBTN.enableButton(this._ibtnEditAllApproval, true);
                    }
                }
                else {
                    if (this._ibtnEditAllApproval) $R_IBTN.enableButton(this._ibtnEditAllApproval, false);
                }

            }
            else {
                if (this._ibtnEditAllApproval) $R_IBTN.enableButton(this._ibtnEditAllApproval, false);
                if (this._ibtnEditApproval) $R_IBTN.enableButton(this._ibtnEditApproval, false);
                if (this._ibtnApproval) $R_IBTN.enableButton(this._ibtnApproval, false);
                if (this._ibtnRequestApproval) $R_IBTN.enableButton(this._ibtnRequestApproval, false);
            }

        }
    },

    enableEditButtons: function (bln) {
        if (this._blnDisableAllButtons) bln = false;
        if (bln) {

            var blnEnableDelete = true;
            if (this._IsAuthorised)
                blnEnableDelete = false;
            if (this._IsReleased)
                blnEnableDelete = true;

            var tbl = this.getCurrentTable(); if (!tbl) return;
            var obj = tbl.getSelectedExtraData(); if (!obj) return;
            var blnAllowEdit = false;
            blnAllowEdit = !obj.IsReceived;
            
            obj = null;
        } 
    },





    getCurrentTable: function () {
        var tbl;
        switch (this._ctlTabStrip._selectedTabIndex) {
            case 0: tbl = this._tblAll; break;
            case 1: tbl = this._tblApproved; break;
            case 2: tbl = this._tblAwaiting; break;
        }
        return tbl;
    },

    prevLine: function () {
        var tbl = this.getCurrentTable();
        var intNewIndex = tbl._intSelectedIndex - 1;
        if (intNewIndex < 0) return;
        tbl.selectRow(intNewIndex, true);
    },

    nextLine: function () {
        var tbl = this.getCurrentTable();
        var intNewIndex = tbl._intSelectedIndex + 1;
        if (intNewIndex >= this._intLineCount) return;
        tbl.selectRow(intNewIndex, true);
    },

    getLineData: function () {
        var tbl = this.getCurrentTable();
        tbl.enable(false);
        this._intLineDataCalls += 1;
        this._blnLineLoaded = false;
        this.enableEditButtons(false);
        this.showLoading(true);
        $R_FN.showElement(this._pnlLoadingLineDetail, true);
        $R_FN.showElement(this._pnlLineDetailError, false);
        $R_FN.showElement(this._pnlLineDetail, false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetData");
        obj.addParameter("id", tbl._varSelectedValue);
        obj.addDataOK(Function.createDelegate(this, this.getLineDataOK));
        obj.addError(Function.createDelegate(this, this.getLineDataError));
        obj.addTimeout(Function.createDelegate(this, this.getLineDataError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineDataError: function (args) {
        this.finishLineDataCall();
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        $R_FN.showElement(this._pnlLineDetailError, true);
        $R_FN.setInnerHTML(this._pnlLineDetailError, args.get_ErrorMessage());
    },

    getLineDataOK: function (args) {
        this.finishLineDataCall();
        $R_FN.showElement(this._pnlLineDetailError, false);
        var result = args._result;
        this.setFieldValue("ctlIPOBom", $RGT_nubButton_BOM(result.BomNo, result.BomName));

        this.setFieldValue("ctlRepeatOrdr", result.RepeatOrder);
        this.setFieldValue("ctlWeblinkEvidence", result.QuantityOrdered);
        this.setFieldValue("ctlQuantityAllocated", result.QuantityAllocated);
        
        this.setFieldValue("hidPartNo", result.Part);
        this.setFieldValue("ctlROHS", $R_FN.writeROHS(result.ROHS));
        this.setFieldValue("hidROHS", result.ROHS);
        this.setFieldValue("ctlTradeRefOne", result.Price);
        this.setFieldValue("hidPrice", result.PriceVal);
        this.setFieldValue("hidCurrencyCode", result.CurrencyCode);
        this.setFieldValue("hidShipInCost", result.ShipInCostVal);
        this.setFieldValue("ctlSupplierPart", $R_FN.setCleanTextValue(result.SupplierPart));
        this.setFieldValue("hidManufacturer", result.Mfr);
        this.setFieldValue("hidManufacturerNo", result.MfrNo);
        this.setFieldValue("ctlTradeRefTwo", $R_FN.setCleanTextValue(result.DateCode));
        this.setFieldValue("ctlProductDis", $R_FN.showHazardous(result.Product, result.IsProdHaz));
        this.setFieldValue("hidProductNo", result.ProductNo);
        this.setFieldValue("hidPackageNo", result.PackageNo);
        this.setFieldValue("ctlPrdDutyCodeRate", result.DutyCodeAndRate);
        //

        //
        this.setFieldValue("hidIPOClientNo", result.IPOClientNo);
        this.setFieldValue("ctlReqSeriaNo", result.ReqSerialNo);

        //[004] start
        this.setFieldValue("ctlSupplierWarranty", (result.SupplierWarranty != "0" ? result.SupplierWarranty + " days" : ""));
        //[004] end
        this._blnClientPO = result.IsClientPO;
        this._blnProdInactive = result.ProdInactive;
        this._reqSerialNo = result.ReqSerialNo;
        $R_FN.showElement(this._pnlLineDetail, true);
        $R_FN.showElement(this._pnlLoadingLineDetail, false);
        var tbl = this.getCurrentTable();
        $R_FN.setInnerHTML(this._lblLineNumber, String.format($R_RES.LineXOfY, tbl._intSelectedIndex + 1, this._intLineCount));
        $R_FN.setInnerHTML(this._lblLineInactive, String.format((result.Inactive) ? "***Inactive***" : ""));
        this.setFieldValue("hidProductHazar", result.IsProdHaz);
        this.setFieldValue("hidPrintHaza", result.IsPrintHaz);
        //IHS start
        this.setFieldValue("ctlCountryOfOrigin", result.CountryOfOrigin);
        this.setFieldValue("ctlLifeCycleStage", result.LifeCycleStage);
        this.setFieldValue("ctlHTSCode", result.HTSCode);
        this.setFieldValue("ctlECCNCode", result.ECCNCode);
        this.setFieldValue("ctlPackagingSize", result.PackagingSize);
        this.setFieldValue("ctlDescriptions", result.Descriptions);
        this.setFieldValue("ctlIHSProduct", result.IHSProduct);
        this._IsPOHub = result.IsPOHub;
        if (this._IsPOHub == true) {
            this.showField("ctlAveragePrice", false);
            this.setFieldValue("ctlAveragePrice", result.AveragePrice);
        }
        else {
            this.showField("ctlAveragePrice", false);
        }
        //IHS end
        this._blnLineLoaded = true;

        //Enable table selection if process complete
        //[005] start
        this._IsReleased = result.IsReleased;
        this._IsAuthorised = result.IsAuthorised;
        var eprids = "";
        if (result.EPRIds) {
            for (var i = 0; i < result.EPRIds.length; i++) {
                var row = result.EPRIds[i];
                eprids += "<a href='javascript:void(0)'; onclick='$RGT_openEPRWindow(" + this._intPurchaseOrderID + "," + row.EPRId + ");'>" + this._PONumber + "-" + row.EPRId + "</a>&nbsp;&nbsp;";//$RGT_nubButton_EPR(this._intPurchaseOrderID, row.EPRId, res.PONumber); 

            }
        }
        this.setFieldValue("ctlEPR", eprids);
        //[005] end
        /***********enable/disable button end***********/

        var tbl = this.getCurrentTable();
        tbl.enable(true);
        this.enableEditButtons(true);

        //End


    },

    getLineAllocations: function () {
        this._intLineDataCalls += 1;
        this.showLoading(true);
        this._fldAllocations.showLoading(true);
        this._tblAllocations.clearSelection();
        this.enableDeallocateButton(false);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines_Allocated");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getLineAllocationsOK));
        obj.addError(Function.createDelegate(this, this.getLineAllocationsError));
        obj.addTimeout(Function.createDelegate(this, this.getLineAllocationsError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineAllocationsError: function (args) {
        this.finishLineDataCall();
        this._fldAllocations.showError(true, args.get_ErrorMessage());
    },

    getLineAllocationsOK: function (args) {
        this.finishLineDataCall();
        var res = args._result;
        var IsASAP = '';
        this._fldAllocations.showContent(true);
        this._fldAllocations.resetCount();
        this._tblAllocations.clearTable();
        this._fldAllocations.updateCount(res.Count);
        if (res.Lines) {
            for (var i = 0; i < res.Lines.length; i++) {
                IsASAP = '';
                var row = res.Lines[i];
                var strPart = $R_FN.writePartNo(row.Part, row.ROHS);
                IsASAP = row.ShipASAP == true ? row.DatePromised + ' (or ASAP)' : row.DatePromised;
                var aryData = [
                    $R_FN.writeDoubleCellValue(row.IsPoHub == true ? $R_FN.setCleanTextValue(row.Part) : $RGT_nubButton_Stock(row.StockNo, row.Part, row.ROHS), $R_FN.setCleanTextValue(row.CustomerPart))
                    , $R_FN.writeDoubleCellValue(row.IsPoHub == true ? $R_FN.setCleanTextValue(row.Customer) : $RGT_nubButton_Company(row.CustomerNo, row.Customer), $R_FN.setCleanTextValue(row.CustomerPO))
                    
                    , $R_FN.writeDoubleCellValue($R_FN.showSerialNumber(row.IsPoHub == true ? $R_FN.setCleanTextValue(row.SalesOrderNumber) : $RGT_nubButton_POSO(row.SalesOrderNo, row.SalesOrderNumber, row.SalesOrderLineNo), row.LineNo), IsASAP)  // Added as per client requirement 13-3-2018

                    , $R_FN.writeDoubleCellValue($RGT_nubButton_SRMA(row.SRMANo, row.SRMANumber), row.ReturnDate)
                    , $R_FN.writeDoubleCellValue(row.QuantityAllocated, row.Price)
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.DateCode), row.DeliveryDate)
                    , $R_FN.writeDoubleCellValue(row.Margin, row.MarginValue)
                ];

                var objExtraData = { shipped: (row.IsShipped) ? true : false };
                this._tblAllocations.addRow(aryData, row.ID, false, objExtraData);
                row = null; aryData = null;
            }
        }
        this._tblAllocations.resizeColumns();
    },

    onShownAllocations: function () {
        this._tblAllocations.resizeColumns();
    },

    onRefreshAllocations: function () {
        this.getLineAllocations();
    },

    getLineReceived: function () {
        this._intLineDataCalls += 1;
        this.showLoading(true);
        this._fldReceived.showLoading(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData(this._strDataPath);
        obj.set_DataObject(this._strDataObject);
        obj.set_DataAction("GetLines_Received");
        obj.addParameter("id", this._intLineID);
        obj.addDataOK(Function.createDelegate(this, this.getLineReceivedOK));
        obj.addError(Function.createDelegate(this, this.getLineReceivedError));
        obj.addTimeout(Function.createDelegate(this, this.getLineReceivedError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },

    getLineReceivedError: function (args) {
        this.finishLineDataCall();
        this._fldReceived.showError(true, args.get_ErrorMessage());
    },

    getLineReceivedOK: function (args) {
        this.finishLineDataCall();
        var res = args._result;
        this._fldReceived.showContent(true);
        this._fldReceived.resetCount();
        this._tblReceived.clearTable();
        this._fldReceived.updateCount(res.Count);
        if (res.LinesReceived) {
            for (var i = 0; i < res.LinesReceived.length; i++) {
                var row = res.LinesReceived[i];
                var aryData = [
                    
                    $RGT_nubButton_GoodsIn(row.GoodsInNo, row.GoodsInNumber)
                    , $R_FN.writeDoubleCellValue(row.IsPoHub == true ? $R_FN.setCleanTextValue(row.PartNo) : $RGT_nubButton_Stock(row.StockNo, row.PartNo, row.ROHS), $R_FN.setCleanTextValue(row.SupplierPart))
                    , $R_FN.writeDoubleCellValue(row.IsPoHub == true ? $R_FN.setCleanTextValue(row.Mfr) : $RGT_nubButton_Manufacturer(row.MfrNo, row.Mfr, row.MfrAdvisoryNotes), $R_FN.setCleanTextValue(row.DC))
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Product), $R_FN.setCleanTextValue(row.Package))
                    , $R_FN.writeDoubleCellValue(row.Quantity, row.LandedCost)
                    , $R_FN.writeDoubleCellValue($R_FN.setCleanTextValue(row.Location), $R_FN.setCleanTextValue(row.ReceivedDate))
                ];
                this._tblReceived.addRow(aryData, row.ID, false);
                row = null; aryData = null;
            }
        }
        this._tblReceived.resizeColumns();
    },

    onShownReceived: function () {
        this._tblReceived.resizeColumns();
    },

    onRefreshReceived: function () {
        this.getLineReceived();
    },

    showEditForm: function () {
        if (this._blnLineLoaded = false)
            return;
        var tbl = this.getCurrentTable();
        
        this._frmEdit._intLineID = this._tblAll._aryCurrentValues[0]; //this._intLineID; //tbl._varSelectedValue;
        
        this._frmEdit._blnCanEditQty = !(this._IsAuthorised && !this._IsReleased);
        this._frmEdit._blnCanEditPrice = !(this._IsAuthorised && !this._IsReleased);
        //[005] end
        this.showForm(this._frmEdit, true);
    },

    hideEditForm: function () {
        this.showForm(this._frmEdit, false);
        this.getCurrentTable().resizeColumns();
    },

    saveEditComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getTabData();
        this.onPotentialStatusChange();
    },

    showEditAllForm: function () {
        if (this._blnLineLoaded = false)
            return;

        
        this._frmEditAll._aryCurrentValues = $R_FN.arrayToSingleString(this._tblAll._aryCurrentValues)
        this._frmEditAll._intLineID = this._tblAll._aryCurrentValues[0];

        this._frmEditAll._blnCanEditQty = !(this._IsAuthorised && !this._IsReleased);
        this._frmEditAll._blnCanEditPrice = !(this._IsAuthorised && !this._IsReleased);
        //[005] end
        this.showForm(this._frmEditAll, true);
    },

    hideEditAllForm: function () {
        this.showForm(this._frmEditAll, false);
        this.getCurrentTable().resizeColumns();
    },

    saveEditAllComplete: function () {
        this.hideEditForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getTabData();
        this.onPotentialStatusChange();
    },

    showApprovalForm: function () {
        this._frmApproval._aryUnpostedLineIDs = this._aryUnpostedLineIDs;
        this.doShowApprovalForm("LINEMANAGER_APPROVE");
    },
    
    doShowApprovalForm: function (strMode) {
        if (this._blnLineLoaded = false)
            return;
        this._frmApproval.changeMode(strMode);
        this._frmApproval._intExportApprovalID = this._intPurchaseOrderID;
        this._frmApproval._intLineID = this._tblAll._aryCurrentValues[0]; //this._intLineID;
        
        this.showForm(this._frmApproval, true);
    },

    hideApprovalForm: function () {
        this.showForm(this._frmApproval, false);
        this.getCurrentTable().resizeColumns();
    },

    saveApprovalComplete: function () {
        this.hideApprovalForm();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.getTabData();
        this.onPotentialStatusChange();
    },

    showAddForm: function () {
        this._frmAdd._intPurchaseOrderID = this._intPurchaseOrderID;
        var tbl = this.getCurrentTable();
        this._frmAdd._intIPOClientNo = this._ipoClientNo;
        this.showForm(this._frmAdd, true);
    },

    hideAddForm: function () {
        this.showForm(this._frmAdd, false);
        this.getCurrentTable().resizeColumns();
    },

    saveAddComplete: function () {
        this.hideAddForm();
        this._intLineID = this._frmAdd._intNewPurchaseOrderLineID;
        this.getTabData();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onPotentialStatusChange();
    },

    showDeleteForm: function () {
        this._frmDelete._intPurchaseOrderID = this._intPurchaseOrderID;
        this._frmDelete._intLineID = this._intLineID;
        this._frmDelete.setFieldValue("ctlQuantity", this.getFieldValue("ctlWeblinkEvidence"));

        this._frmDelete.setFieldValue("ctlTradeRefOne", this.getFieldValue("ctlTradeRefOne"));
        this.showForm(this._frmDelete, true);
    },

    hideDeleteForm: function () {
        this.showForm(this._frmDelete, false);
        this.getCurrentTable().resizeColumns();
    },

    deleteComplete: function () {
        this.hideDeleteForm();
        this.getTabData();
        this.onPotentialStatusChange();
    },

    showCloseForm: function () {
        this._frmClose._intPurchaseOrderID = this._intPurchaseOrderID;
        this._frmClose._intLineID = this._intLineID;
        this.showForm(this._frmClose, true);
    },

    hideCloseForm: function () {
        this.showForm(this._frmClose, false);
        this.getCurrentTable().resizeColumns();
    },

    closeComplete: function () {
        this.hideCloseForm();
        this.getTabData();
        this.onPotentialStatusChange();
    },

    showDeallocateForm: function () {


        this._frmDeallocate._intPurchaseOrderID = this._intPurchaseOrderID;
        this._frmDeallocate._aryLineIDs = this._tblAllocations._aryCurrentValues;
        this._frmDeallocate._intLineID = this._intLineID;
        this._frmDeallocate._InternalPurchaseOrderNumber = this._InternalPurchaseOrderNumber;
        this._frmDeallocate._PoLine = this._PoLine;
        this._frmDeallocate._Part = this._Part;
        this._frmDeallocate._Quantity = this._Quantity;


        this.showForm(this._frmDeallocate, true);
    },

    hideDeallocateForm: function () {
        this.showForm(this._frmDeallocate, false);
        this.getCurrentTable().resizeColumns();
    },

    deallocateComplete: function () {
        this.hideDeallocateForm();
        this.getTabData();
        this.onPotentialStatusChange();
    },

    finishLineDataCall: function () {
        this._intLineDataCalls -= 1;
        if (this._intLineDataCalls < 1) this.showLoading(false);
    },

    updateStatus: function (strStatusNo) {
        this._enmPOStatus = Number.parseInvariant(strStatusNo.toString());
        this._blnDisableAllButtons = (this._enmPOStatus == $R_ENUM$PurchaseOrderStatus.Received || this._enmPOStatus == $R_ENUM$PurchaseOrderStatus.Complete);
        this.enableEditButtons(true);
    },

    enableDisableAddButton: function (isDisable) {

    },
    clearPolineFormOnEdit: function () {
        if (this._frmEdit) {
            this._frmEdit._intLineID = -1;
            //this._frmEdit.setFieldValue("ctlWeblinkEvidence", "");

            //this._frmEdit.setFieldValue("ctlPurchaseMethod", "");
            //this._frmEdit.setFieldValue("ctlTradeRefOne", "");
            //this._frmEdit.setFieldValue("ctlTradeRefThree", "");
            //this._frmEdit.setFieldValue("ctlTradeRefTwo", "");
        }
    },
    writeCheckbox: function (varID, intIndex, tbl) {
        var strChkID = this.getControlID("chk", intIndex, tbl);
        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);
        var str = String.format("<div class=\"imageCheckBoxDisabled\" id=\"{0}\" ><img id=\"{1}\" class=\"{2}\" src=\"images/x.gif\" style=\"border-width: 0px;\" /> </div>", strChkID, strChkImageID, "off");
        return str;
    },
    getControlID: function (str, i, tbl) {
        return String.format("{0}_{1}{2}", tbl._element.id, str, i);
    },
    getCheckBox: function (intCheckBox, tbl) {
        return $find(this.getControlID("chk", intCheckBox, tbl));
    },
    registerCheckBox: function (varID, intIndex, blnChecked, blnEnabled, tbl) {

        var strChkID = this.getControlID("chk", intIndex, tbl);

        var strChkImageID = this.getControlID("chkImg", intIndex, tbl);

        var chk = this.getCheckBox(intIndex, tbl);

        if (chk) {
            chk.dispose();
            chk = null;
        }

        eval($R_FN.getCreateStatement("Rebound.GlobalTrader.Site.Controls.ImageCheckBox", [["blnChecked", blnChecked], ["blnEnabled", blnEnabled], ["img", String.format("$get(\"{0}\")", strChkImageID)]], strChkID));

    },
    getCheckedCellValue: function (intIndex, poLinID, poSerialNo) {

        var tbl = this.getCurrentTable();
        var chk = this.getCheckBox(intIndex, tbl);
        var IsChecked = chk._blnChecked;
        var tr = tbl._tbl.rows[intIndex];
        // alert(tr);
        if (!tr) return;

        if (IsChecked == true) {
            this.clearMessages();
            Array.add(this._arrPOLineIds, poLinID);
            //[005] start
            Array.add(this._POLineSerialNo, poSerialNo);
            //[005] end
            $R_IBTN.enableButton(this._ibtnAddExpditeNote, true);
            // }
        }
        else {
            $R_IBTN.enableButton(this._ibtnAddExpditeNote, false);
            Array.remove(this._arrPOLineIds, poLinID)
            //[005] start
            Array.remove(this._POLineSerialNo, poSerialNo);
            //[005] end
        }
        if (this._arrPOLineIds.length == 0)
            $R_IBTN.enableButton(this._ibtnAddExpditeNote, false);
        else
            $R_IBTN.enableButton(this._ibtnAddExpditeNote, true);
        //[005] start
        this.enableReleaseButton();
        //[005] end

    },
    //[005] start
    enableReleaseButton: function () {
        var isValid = false;
        var tbl = this.getCurrentTable();
        var checkCount = 0;
        var objExtraData = this.getCurrentTable()._aryExtraData;
        for (i = 0; i < tbl._tbl.rows.length; i++) {
            var chk = this.getCheckBox(i, tbl);
            var rowData = tbl._tbl.rows[i];
            var IsChecked = chk._blnChecked;
            if (IsChecked && !isValid) {
                isValid = objExtraData[i].IsAuthorised && !objExtraData[i].IsReleased;
                ++checkCount;
            }
        }
        //disable release button if no row checked
        if (checkCount == 0)
            isValid = false;
        $R_IBTN.enableButton(this._ibtnRelease, isValid);
    },
    //[005] end
    clearCheckBoxes: function () {
        var tbl = this.getCurrentTable();
        for (var i = 0; i < this._arrPOLineIds.length; i++) {
            var chk = this.getCheckBox(i, tbl);
            chk.setChecked(false);
        }
        $R_IBTN.enableButton(this._ibtnAddExpditeNote, false);
        Array.clear(this._arrPOLineIds);
    },

    showExpediteForm: function () {

        this._frmExpediteAdd._intPurchaseOrderID = this._intPurchaseOrderID;
        this._frmExpediteAdd._IsFromLine = true;
        this._frmExpediteAdd._IsFromIPO = false;
        this._frmExpediteAdd._arrPOLineIds = this._arrPOLineIds;

        this._frmExpediteAdd.setFieldValue("ctlExpediteNotes", "");
        this.showForm(this._frmExpediteAdd, true);
    },

    hideExpediteForm: function () {
        this.showForm(this._frmExpediteAdd, false);
    },

    cancelExpediteForm: function () {
        this.showForm(this._frmExpediteAdd, false);
        this.showContent(true);
    },

    saveExpediteComplete: function () {
        this.showForm(this._frmExpediteAdd, false);
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onPotentialStatusChange();

    },
    resetPOLine: function () {
        this._intLineID = -1;
        this.setFieldValue("ctlIPOBom", "");
        this.setFieldValue("ctlWeblinkEvidence", "");
        this.setFieldValue("ctlQuantityAllocated", "");
        this.setFieldValue("ctlApprovedOrderTwl", "");
        this.setFieldValue("hidPartNo", "");
        this.setFieldValue("ctlROHS", "");
        this.setFieldValue("hidROHS", "");
        this.setFieldValue("ctlTradeRefOne", "");
        this.setFieldValue("hidPrice", "");
        this.setFieldValue("hidCurrencyCode", "");
        this.setFieldValue("hidShipInCost", "");
        this.setFieldValue("ctlSupplierPart", "");
        this.setFieldValue("hidManufacturer", "");
        this.setFieldValue("hidManufacturerNo", "");
        this.setFieldValue("ctlTradeRefTwo", "");
        this.setFieldValue("hidProductNo", "");
        this.setFieldValue("hidPackageNo", "");
        this.setFieldValue("ctlPrdDutyCodeRate", "");
        this.setFieldValue("hidIPOClientNo", "");
    },
    //[005] start
    showTopIcons: function () {
        clearTimeout(this._intTimeout);
        $R_FN.showElement(this._pnlEPRTootTip, true);
        this._pnlEPRTootTip.style.top = "0px";
        this._pnlEPRTootTip.style.left = "0px";
        this.setToolTipLocation();
    },
    hideTopIcons: function () {
        clearTimeout(this._intTimeout);
        this._intTimeout = setTimeout(Function.createDelegate(this, this.finishHideTopIcons), 100);
    },
    setToolTipLocation: function () {
        if (this._pnlEPRTootTip) {
            if (this._ibtnEPR) this._pnlEPRTootTip.style.top = String.format("{0}px", (Sys.UI.DomElement.getBounds(this._ibtnEPR).y - Sys.UI.DomElement.getBounds(this._pnlEPRTootTip).y) + 15);
            if (this._ibtnEPR) this._pnlEPRTootTip.style.left = String.format("{0}px", (Sys.UI.DomElement.getBounds(this._ibtnEPR).x - Sys.UI.DomElement.getBounds(this._pnlEPRTootTip).x));
        }
    },
    finishHideTopIcons: function () {
        $R_FN.showElement(this._pnlEPRTootTip, false);
    },
    OpenEPR: function () {
        if (this._arrPOLineIds.length > 0)
            $R_FN.openEPRWindow(this._intPurchaseOrderID, 0, this._arrPOLineIds.toString(), this._POLineSerialNo.toString());
        else {
            alert("Please select line item to create EPR"); return false;
        }


    },
    ShowGridPopupTROne: function () {
        $RGT_openSupplierApprovalDoc(75);
    },
    ShowGridPopupTRTwo: function () {
        $RGT_openSupplierApprovalDoc(75);
    },
    ShowGridPopupTRThree: function () {
        $RGT_openSupplierApprovalDoc(75);
    },
    showReleaseForm: function () {
        this.showReleaseUnReleaseForm("Release");
    },
    showUnReleaseForm: function () {
        this.showReleaseUnReleaseForm("UnRelease");
    },

    showReleaseUnReleaseForm: function (strMode) {
        if (strMode == "Release") {
            this._frmRelease._arrPOLineIds = this._arrPOLineIds;
            this.showForm(this._frmRelease, true);
        }
        else {
            this._frmUnRelease._intPurchaseOrderLineID = this._intLineID;
            this.showForm(this._frmUnRelease, true);
        }
    },
    hideReleaseForm: function () {
        this.hideReleaseUnReleaseForm("Release");
    },
    hideUnReleaseForm: function () {
        this.hideReleaseUnReleaseForm("UnRelease");
    },
    hideReleaseUnReleaseForm: function (strMode) {
        if (strMode == "Release") {
            this.showForm(this._frmRelease, false);
        }
        else {
            this.showForm(this._frmUnRelease, false);
        }
    },
    saveReleaseComplete: function () {
        $R_IBTN.enableButton(this._ibtnRelease, false);
        this._arrPOLineIds = [];
        this.tabChanged();
        this.showForm(this._frmRelease, false);
        this.showContentLoading(false);
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
        this.onPotentialStatusChange();
    },
    saveUnReleaseComplete: function () {
        this.showForm(this._frmUnRelease, false);
        this.tabChanged();
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);

    },
    clearArray: function (array) {
        while (array.length) {
            array.remove();
        }

    },
    showRequestApprovalForm: function () {
        this._frmRequestApproval._intSalesOrderLineID = this._tblAll._aryCurrentValues[0];
        this.showForm(this._frmRequestApproval, true);
    },
    hideRequestApprovalForm: function () {
        this.showForm(this._frmRequestApproval, false);
    },
    cancelRequestApprovalForm: function () {
        this.showForm(this._frmRequestApproval, false);
        this.showContent(true);
    },
    saveRequestApprovalComplete: function () {
        this.showForm(this._frmRequestApproval, false);
        this.showSavedOK(true, $R_RES.ChangesSavedSuccessfully);
    },
    printEmailLog: function () {
        var baseUrl = (window.location).href;
        var tbl = this.getCurrentTable();
        var SupplierApprovalId = tbl._varSelectedValue;
        var url = new URL(baseUrl);
        $R_FN.openPrintLogWindow($R_ENUM$PrintObject.printTermEmailLog, SupplierApprovalId, false, 'SupplierApprovalEmailLog');
    }
    
    
     
};

Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.ExportApprovalStatus", Rebound.GlobalTrader.Site.Controls.Nuggets.Base);
