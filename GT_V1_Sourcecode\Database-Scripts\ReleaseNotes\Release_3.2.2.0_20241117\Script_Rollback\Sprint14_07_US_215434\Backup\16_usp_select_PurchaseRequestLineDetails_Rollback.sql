﻿
GO

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO


CREATE OR ALTER PROCEDURE [dbo].[usp_select_PurchaseRequestLineDetails] --2,114          
 @PurchaseRequestId int,     
 @ClientId int
 ,@CompanyNo int=0            
AS    
BEGIN     

Declare @CurrencyCode varchar(10)
Declare @CurrencyNo INT

set @CurrencyNo = 0
set @CurrencyCode = ' '
IF (@CompanyNo > 0)
BEGIN
	SELECT top 1 @CurrencyNo = c.POCurrencyNo, @CurrencyCode = gcu.GlobalCurrencyName FROM tbCompany c join tbCurrency cu on c.POCurrencyNo = cu.CurrencyId 
	                               join tbGlobalCurrencyList gcu on gcu.GlobalCurrencyId = cu.GlobalCurrencyNo
	where CompanyId = @CompanyNo
END

--IF (@CompanyNo <= 0 OR ISNULL(@CurrencyNo,0) = 0)
--BEGIN
--SELECT @CurrencyNo = c.CurrencyNo, @CurrencyCode = CurrencyCode FROM tbClient c JOIN tbCurrency cu on c.CurrencyNo = cu.CurrencyId
--where c.ClientId = 114
--END


 
SELECT  PRLine.PurchaseRequestLineId AS PQId,
@CompanyNo AS Company,     
@CurrencyNo AS CurrencyNo ,
cr.Quantity AS QuantityQuoted,  
PRLine.part AS MPNQuoted,     
mf.ManufacturerName,    
DateCode,  
pk.Packagename AS PackageType,  
p.ProductName AS ProductType,
--New columns Added-Start
'' AS SPQ,
'' AS MOQ,
'' As LeadTimeWks,
'' As RohsYN,
'' AS TQSA,
'' AS LTB,
'' AS FactorySealed,
'' AS MSL,
--New columns Added-End  
UnitPrice = 0.00,  
@CurrencyCode AS CurrencyCode ,
'' AS SupplierNotes
,PR.Notes 
         
FROM dbo.tbPurchaseRequest PR        
Inner JOIN dbo.tbPurchaseRequestLine PRLine ON PRLine.PurchaseRequestNo=PR.PurchaseRequestId 
--Left Join dbo.tbPurchaseRequestLineDetail PRLD ON  PRLD.PurchaseRequestLineNo=PRLine.PurchaseRequestLineID   
LEFT JOIN dbo.tbcustomerRequirement cr ON PRLine.customerRequirementNo = cr.customerRequirementid    
LEFT JOIN dbo.tbProduct p ON cr.ProductNo = p.ProductId              
LEFT JOIN dbo.tbPackage pk ON cr.PackageNo = pk.PackageId              
LEFT JOIN dbo.tbManufacturer mf ON cr.ManufacturerNo = mf.ManufacturerId     
--LEFT JOIN dbo.tbBom bom on cr.BomNo=bom.BomId    
--LEFT JOIN dbo.tbCurrency Cur ON  bom.CurrencyNo=Cur.CurrencyId      
WHERE PR.PurchaseRequestId = @PurchaseRequestId    
AND PR.ClientNo = @ClientId    
    
IF EXISTS (select PurchaseRequestNo from  dbo.tbPurchaseRequestLine  WHERE PurchaseRequestNo = @PurchaseRequestId )    
  BEGIN       
  UPDATE  dbo.tbPurchaseRequest            
  SET     [Status] = 2    
  WHERE PurchaseRequestId  = @PurchaseRequestId    
  END    
END

GO


