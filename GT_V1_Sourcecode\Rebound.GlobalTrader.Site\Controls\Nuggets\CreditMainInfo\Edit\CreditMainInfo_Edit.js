Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit=function(n){Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.initializeBase(this,[n]);this._intCreditID=-1;this._hidRaisedByNo=0;this._IsPOHub=!1;this._hidShipViaNo=0;this._ShipVia=-1;this._RaisedBy=-1};Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.prototype={get_intCreditID:function(){return this._intCreditID},set_intCreditID:function(n){this._intCreditID!==n&&(this._intCreditID=n)},get_lblFreight_Currency:function(){return this._lblFreight_Currency},set_lblFreight_Currency:function(n){this._lblFreight_Currency!==n&&(this._lblFreight_Currency=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},formShown:function(){this._blnFirstTimeShown&&(this.addSave(Function.createDelegate(this,this.saveClicked)),$find(this.getField("ctlSalesman2").ControlID).addChanged(Function.createDelegate(this,this.changedSalesman2)),$find(this.getField("ctlCurrency").ControlID).addChanged(Function.createDelegate(this,this.currencyChanged)));this.getFieldDropDownData("ctlDivision");this.getFieldDropDownData("ctlSalesman");this.getFieldDropDownData("ctlSalesman2");this.getFieldDropDownData("ctlRaisedBy");this.getFieldDropDownData("ctlTax");this.getFieldDropDownData("ctlCurrency");this.getFieldDropDownData("ctlShipVia");this.getFieldDropDownData("ctlIncoterm");this.getFieldDropDownData("ctlDivisionHeader")},dispose:function(){this.isDisposed||(this._intCreditID=null,this._lblFreight_Currency=null,Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.callBaseMethod(this,"dispose"))},saveClicked:function(){if(this.validateForm()){var n=new Rebound.GlobalTrader.Site.Data;this._ShipVia=this.getFieldValue("ctlShipVia");this._ShipVia<=0&&(this._ShipVia=this._hidShipViaNo);this._RaisedBy=this.getFieldValue("ctlRaisedBy");this._RaisedBy<=0&&(this._RaisedBy=this._hidRaisedByNo);n.set_PathToData("controls/Nuggets/CreditMainInfo");n.set_DataObject("CreditMainInfo");n.set_DataAction("SaveEdit");n.addParameter("id",this._intCreditID);n.addParameter("CustomerPO",this.getFieldValue("ctlCustomerPO"));n.addParameter("CustomerReturn",this.getFieldValue("ctlCustomerReturn"));n.addParameter("CustomerDebit",this.getFieldValue("ctlCustomerDebit"));n.addParameter("Notes",this.getFieldValue("ctlNotes"));n.addParameter("Instructions",this.getFieldValue("ctlInstructions"));n.addParameter("DivisionNo",this.getFieldValue("ctlDivision"));n.addParameter("SalesmanNo",this.getFieldValue("ctlSalesman"));n.addParameter("RaisedBy",this._RaisedBy);n.addParameter("CreditDate",this.getFieldValue("ctlCreditDate"));n.addParameter("ReferenceDate",this.getFieldValue("ctlReferenceDate"));n.addParameter("TaxNo",this.getFieldValue("ctlTax"));n.addParameter("CurrencyNo",this.getFieldValue("ctlCurrency"));n.addParameter("ShipViaNo",this._ShipVia);n.addParameter("ShippingAccount",this.getFieldValue("ctlShippingAccount"));n.addParameter("ShippingCost",this.getFieldValue("ctlShippingCost"));n.addParameter("Freight",this.getFieldValue("ctlFreight"));n.addParameter("Salesman2No",this.getFieldValue("ctlSalesman2"));n.addParameter("Salesman2Percent",this.getFieldValue("ctlSalesman2Percent"));n.addParameter("Incoterm",this.getFieldValue("ctlIncoterm"));n.addParameter("CreditNoteBankFee",this.getFieldValue("ctlCreditNoteBankFee"));n.addParameter("ExchangeRate",this.getFieldValue("ctlExchangeRate"));n.addParameter("DivisionHeader",this.getFieldValue("ctlDivisionHeader"));n.addDataOK(Function.createDelegate(this,this.saveEditComplete));n.addError(Function.createDelegate(this,this.saveEditError));n.addTimeout(Function.createDelegate(this,this.saveEditError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},saveEditError:function(n){this._strErrorMessage=n._errorMessage;this.onSaveError()},saveEditComplete:function(n){n._result.Result==!0?this.onSaveComplete():(this._strErrorMessage=n._errorMessage,this.onSaveError())},validateForm:function(){this.onValidate();return this.autoValidateFields()},changedSalesman2:function(){$find(this.getField("ctlSalesman2").ControlID).isSetAsNoValue()?this.setFieldValue("ctlSalesman2Percent",""):this.getFieldValue("ctlSalesman2Percent")==0&&this.setFieldValue("ctlSalesman2Percent",50)},currencyChanged:function(){$R_FN.setInnerHTML(this._lblFreight_Currency,this.getFieldDropDownExtraText("ctlCurrency"))}};Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CreditMainInfo_Edit",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);