﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
ALTER  PROCEDURE [dbo].[usp_selectAll_MailGroupMember_by_Group]
	@MailGroupNo INT
AS
    SELECT  mm.*
          , lg.EmployeeName,ISNULL(lg.Inactive,1) as Inactive, lg.EMail
    FROM    tbMailGroupMember mm
    JOIN    tbLogin lg ON mm.LoginNo = lg.LoginId
    WHERE   MailGroupNo = @MailGroupNo  --and lg.Inactive=0
    ORDER BY lg.LastName
          , lg.FirstName