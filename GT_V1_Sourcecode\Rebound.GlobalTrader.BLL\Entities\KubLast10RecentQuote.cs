﻿using Rebound.GlobalTrader.DAL;
using Rebound.GlobalTrader.DAL.SQLClient;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.BLL
{
    //Anuj
    public partial class KubLast10RecentQuote : BizObject
    {

        #region Properties
        public System.String QuoteID { get; set; }
        public System.String QuoteNumber { get; set; }
        public System.String QuoteDate { get; set; }
        public System.String Quantity { get; set; }
        public System.String UnitPrice { get; set; }
        public System.String BuyPrice { get; set; }
        public System.String Profit { get; set; }
        public System.Boolean? IsAllowedEnable { get; set; }
        #endregion

        #region Methods

        /// <summary>
        /// getKub Details
        /// Calls [sp_Kub]
        /// </summary>
        public static List<KubLast10RecentQuote> GetKubLast10RecentQuoteDetails(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID)
        {
            List<KubLast10RecentQuote> lts = new List<KubLast10RecentQuote>();
            List<KubLast10RecentQuoteDetails> lstkubSearchDetails = new List<KubLast10RecentQuoteDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubSearchDetails = objSQLKubProvider.ListKubLast10RecentQuoteDetails(PartNo, CustomerReqId, ClientID);
                if (lstkubSearchDetails == null)
                {
                    return new List<KubLast10RecentQuote>();
                }
                else
                {
                    foreach (KubLast10RecentQuoteDetails objDetails in lstkubSearchDetails)
                    {
                        KubLast10RecentQuote obj = new KubLast10RecentQuote();
                        obj.QuoteID = objDetails.QuoteID;
                        obj.QuoteNumber = objDetails.QuoteNumber;
                        obj.QuoteDate = objDetails.QuoteDate;
                        obj.Quantity = objDetails.Quantity;
                        obj.UnitPrice = objDetails.UnitPrice;
                        obj.BuyPrice = objDetails.BuyPrice;
                        obj.Profit = objDetails.Profit;
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lts = null;
                lstkubSearchDetails = null;
            }
        }

        public static List<KubLast10RecentQuote> StartKubCacheForBrowsePage(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID)
        {
            List<KubLast10RecentQuote> lts = new List<KubLast10RecentQuote>();
            List<KubLast10RecentQuoteDetails> lstkubSearchDetails = new List<KubLast10RecentQuoteDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubSearchDetails = objSQLKubProvider.StartKubCacheForBrowsePage(PartNo, CustomerReqId, ClientID);
                if (lstkubSearchDetails == null)
                {
                    return new List<KubLast10RecentQuote>();
                }
                else
                {
                    foreach (KubLast10RecentQuoteDetails objDetails in lstkubSearchDetails)
                    {
                        KubLast10RecentQuote obj = new KubLast10RecentQuote();
                        obj.QuoteNumber = objDetails.QuoteNumber;
                        obj.QuoteDate = objDetails.QuoteDate;
                        obj.Quantity = objDetails.Quantity;
                        obj.UnitPrice = objDetails.UnitPrice;
                        obj.BuyPrice = objDetails.BuyPrice;
                        obj.Profit = objDetails.Profit;
                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lts = null;
                lstkubSearchDetails = null;
            }
        }

        public static List<KubLast10RecentQuote> IsAllowedEnabled(System.String PartNo, System.String CustomerReqId, System.Int32 ClientID)
        {
            List<KubLast10RecentQuote> lts = new List<KubLast10RecentQuote>();
            List<KubLast10RecentQuoteDetails> lstkubSearchDetails = new List<KubLast10RecentQuoteDetails>();
            SQLKubProvider objSQLKubProvider = new SQLKubProvider();
            try
            {
                lstkubSearchDetails = objSQLKubProvider.IsAllowedEnabled(PartNo, CustomerReqId, ClientID);
                if (lstkubSearchDetails == null)
                {
                    return new List<KubLast10RecentQuote>();
                }
                else
                {
                    foreach (KubLast10RecentQuoteDetails objDetails in lstkubSearchDetails)
                    {
                        KubLast10RecentQuote obj = new KubLast10RecentQuote();
                        obj.IsAllowedEnable = objDetails.IsAllowedEnable;

                        lts.Add(obj);
                    }
                }
                return lts;
            }
            catch (Exception ex)
            {
                throw ex;
            }
            finally
            {
                lts = null;
                lstkubSearchDetails = null;
            }
        }

        #endregion
    }
}
