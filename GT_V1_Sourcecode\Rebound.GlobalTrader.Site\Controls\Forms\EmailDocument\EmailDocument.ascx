<%@ Control Language="C#" CodeBehind="EmailDocument.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSend" runat="server" IconGroup="Nugget" IconTitleResource="Send" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnContinue" runat="server" IconGroup="Nugget" IconTitleResource="Continue" IconCSSType="Continue" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" CssClass="invisible" />
	</Links>
	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "EmailDocument")%></Explanation>
	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			
			<ReboundUI_Form:LabelFormField id="ctlSendError" runat="server" RemoveCssClass="true" CssClass="invisible formMessage error" />

			<ReboundUI_FormFieldCollection:TextBoxArray id="ctlTo" runat="server" TextBoxWidth="300" AllowAddAndDelete="true" RequiredField="true" />
		
			<ReboundUI_Form:FormField id="ctlReplyTo" runat="server" FieldID="txtReplyTo" ResourceTitle="ReplyTo" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtReplyTo" runat="server" Width="300" /></Field>
			</ReboundUI_Form:FormField>
		
			<ReboundUI_Form:FormField id="ctlSubject" runat="server" FieldID="txtSubject" ResourceTitle="Subject" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtSubject" runat="server" Width="250" /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes" CssClass="invisible" >
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" TextMode="MultiLine" runat="server"  Width="300" Height="100" /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField runat="server" id="ctlFormat" FieldID="radFormat" ResourceTitle="InvoiceFormat" CssClass="invisible vertical-align-middle">
                    <Field>
						<style type="text/css">
							.invoice-radio-format{
								margin-left: -7px;
							}
						</style>
                        <asp:RadioButtonList ID="radFormat" CssClass="invoice-radio-format" name="radioList" runat="server" RepeatDirection="Horizontal">
                            <asp:ListItem Text="PDF Format" Value="PDF" Class="optionPDF" Selected="True"/>
                            <asp:ListItem Text="XML Format" Value="XML"  Class="optionXML invisible"/>
                        </asp:RadioButtonList>
                    </Field>
            </ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField ID="ctlIncludeCustomTemplate" runat="server" FieldID="chkIncludeCustomTemplate" ResourceTitle="IncludeCustomTemplate">
                <Field>
                    <ReboundUI:ImageCheckBox ID="chkIncludeCustomTemplate" runat="server" Enabled="true" />
                </Field>
            </ReboundUI_Form:FormField>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
