///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />

///<reference path="~/js/PowerBIToken.js" />

//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.HomeNuggets");

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard = function(element) {
	Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.initializeBase(this, [element]);
	this._tblSalesDashboard = null;
	this._powerBiUrl = null;
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.prototype = {

	//get_tblSalesDashboard: function () { return this._tblSalesDashboard; }, set_tblSalesDashboard: function (v) { if (this._tblSalesDashboard !== v) this._tblSalesDashboard = v; },
	get_powerBiUrl: function () { return this._powerBiUrl; }, set_powerBiUrl: function (v) { if (this._powerBiUrl !== v) this._powerBiUrl = v; },

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.callBaseMethod(this, "initialize");
		this.hideLoading();
		
		$('#ctl00_cphMain_ctlToday_PowerBiSalesDashboard_ctlDB_pnlContent').removeClass('invisible');
		var ISPowerBishowPermission = "";
		
		$("#showSalesDashboard").click(function () {
			var permission = "";
			var projname = window.location.pathname.replace("Default.aspx", "");
			AbsoluteURL = window.location.origin;
			handlerUrl = window.location.origin + "/controls/HomeNuggets/PowerBiSalesDashboard/PowerBiSalesDashboard.ashx";
			$.ajax({
				processData: true,
				contentType: 'application/json',
				type: 'POST',
				url: handlerUrl + '?Action=ReturnURL',
				dataType: 'text',
				async: false,
				success: function (data) {
					
					var nn = data.replaceAll("'", "\"");
					var kn = JSON.stringify(nn);
					var pp = JSON.parse(kn)
					var ln = JSON.parse(pp);
					powerBiURL = ln.url;
					
					AddToken(powerBiURL, 3);
				},
				error: function (xhr, ajaxOptions, thrownError) {
					alert(xhr.status);
					alert(thrownError);
				},
			});
			
			
		});
		this.getData();
		//this.setDefaultTextinTable();
		this.addRefreshEvent(Function.createDelegate(this, this.getData));
	},

	dispose: function() { 
		if (this.isDisposed) return;
		//if (this._tblSalesDashboard) this._tblSalesDashboard.dispose();
		//this._tblSalesDashboard = null;
		Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.callBaseMethod(this, "dispose");
	},
	
	setDefaultTextinTable: function() {
		//var aryData = [
		//	$R_FN.setCleanTextValue("Click here for Sales Dasboard"),
		//];
		//this._tblSalesDashboard.addRow(aryData, null);
	},
	
	showNoData: function(bln) {
		//this.showContent(true);
		//$R_FN.showElement(this._pnlNoData, bln);
	},
	
	getData: function() {
		//this.setupLoadingState();
		var obj = new Rebound.GlobalTrader.Site.Data();
		obj.set_PathToData("controls/HomeNuggets/PowerBiSalesDashboard");
		obj.set_DataObject("PowerBiSalesDashboard");
		obj.set_DataAction("GetData");
		obj.addDataOK(Function.createDelegate(this, this.getDataComplete));
		obj.addError(Function.createDelegate(this, this.homeNuggetDataError));
		obj.addTimeout(Function.createDelegate(this, this.homeNuggetDataError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},
	
	getDataComplete: function (args) {
		//debugger;
		var result = args._result;
		if (result.PowerBiPermission ==true) {
			$('#ctl00_cphMain_ctlToday_PowerBiSalesDashboard_ctlDB_pnlContent').removeClass('invisible');
		}
		else {
			$('#ctl00_cphMain_ctlToday_PowerBiSalesDashboard_ctlDB_pnlContent').addClass('invisible');
		}
	},
	homeNuggetDataError: function () {

    }
};

Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard.registerClass("Rebound.GlobalTrader.Site.Controls.HomeNuggets.PowerBiSalesDashboard", Rebound.GlobalTrader.Site.Controls.HomeNuggets.Base, Sys.IDisposable);
