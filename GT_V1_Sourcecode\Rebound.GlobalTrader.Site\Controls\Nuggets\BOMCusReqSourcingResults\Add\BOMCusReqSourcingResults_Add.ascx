<%--
********************************************************************************************
Marker     changed by      date         Remarks
[001]      Aashu          06/06/2018     Added supplier warranty field
**********************************************************************************************
--%>
<%@ Control Language="C#" CodeBehind="BOMCusReqSourcingResults_Add.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.Forms.BOMCusReqSourcingResults_Add" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<%@ Register TagPrefix="cc1" Namespace="Rebound.GlobalTrader.Site" Assembly="Rebound.GlobalTrader.Site" %>
<ReboundUI_Form:DesignBase ID="ctlDB" runat="server" ShowRequiredFieldsExplanation="true" ShowQuickHelp="false">
	<Links>
		<ReboundUI:IconButton ID="ibtnSave" runat="server" IconGroup="Nugget" IconTitleResource="Save" IconCSSType="Save" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
		<ReboundUI:IconButton ID="ibtnCancel" runat="server" IconGroup="Nugget" IconTitleResource="Cancel" IconCSSType="Cancel" IconButtonMode="HyperLink" Href="javascript:void(0);" Alignment="Left" />
	</Links>

	<Explanation><%=Functions.GetGlobalResource("FormExplanations", "CusReqSourcingResults_Add")%></Explanation>

	<Content>
		<ReboundUI_Table:Form id="frm" runat="server">
			<ReboundUI_Form:FormField id="ctlSupplier" runat="server" FieldID="cmbSupplier" ResourceTitle="Supplier" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbSupplier" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="AllCompanies" PanelWidth="250" /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlCompanyNamelbl" runat="server" FieldID="lblCompanyName" ResourceTitle="Location" >
	            <Field><asp:Label ID="lblCompanyName" runat="server" /></Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlCompanyNameddl" runat="server" FieldID="ddlCompanyName" ResourceTitle="Location" IsRequiredField="false" >
				<Field><ReboundDropDown:Country ID="ddlCompanyName" runat="server" /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlTypeOfSupplier" runat="server" FieldID="ddlTypeOfSupplier" ResourceTitle="AS6081TOS"  IsRequiredField="false">
				<Field><ReboundDropDown:TypeOfSupplier ID="ddlTypeOfSupplier" runat="server"  NoValue_Value="" InitialValue=""  /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlReasonForSupplier" runat="server" FieldID="ddlReasonForSupplier" ResourceTitle="AS6081RCS"  IsRequiredField="false">
				<Field><ReboundDropDown:ReasonForSupplier ID="ddlReasonForSupplier" runat="server"  NoValue_Value="" InitialValue=""  /></Field>
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlRiskOfSupplier" runat="server" FieldID="ddlRiskOfSupplier" ResourceTitle="AS6081ROS"  IsRequiredField="false">
				<Field><ReboundDropDown:RiskOfSupplier ID="ddlRiskOfSupplier" runat="server"  NoValue_Value="" InitialValue=""  /></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlPartNo" runat="server" FieldID="txtPartNo" ResourceTitle="PartNo" IsRequiredField="true">
				<Field>
					<%--<ReboundUI:ReboundTextBox ID="txtPartNo" runat="server" Width="250" UppercaseOnly="true" />
					<ReboundAutoSearch:PartSearch ID="autPartNo" runat="server" RelatedTextBoxID="txtPartNo" ResultsHeight="150" Width="250" ResultsActionType="RaiseEvent" TriggerByButton="true" />--%>
                    <ReboundUI:ReboundTextBox ID="txtPartNo" runat="server" Width="250" UppercaseOnly="true" placeholder="Type 3 chars to search" />
					            <asp:Label ID="btn1"  runat="server"  Style=" cursor: pointer;" ToolTip="Click to Search IHS Part"  CssClass="PartDetailsGridGoBtnSearchIcon"/>
                                <asp:Label ID="btn2" Text="Clear"  runat="server"  Style="cursor: pointer;" CssClass="PartDetailsGridGoBtn2"/>
                                <asp:Label ID="lblError" Text="Type 3 chars to search"  runat="server" CssClass="PartDetailsGridGoError" />
                   </Field>
			</ReboundUI_Form:FormField>
           <%--[001] code start--%>
			<ReboundUI_Form:FormField id="ctlROHS" runat="server" FieldID="ddlROHS" ResourceTitle="ROHS"  IsRequiredField="false">
				<Field><ReboundDropDown:ROHSStatus ID="ddlROHS" runat="server"  NoValue_Value="" InitialValue=""  /></Field>
			</ReboundUI_Form:FormField>
           <%--[001] code end--%>
			<ReboundUI_Form:FormField id="ctlManufacturer" runat="server" FieldID="cmbManufacturer" ResourceTitle="Manufacturer" IsRequiredField="true">
				<Field><ReboundUI:Combo ID="cmbManufacturer" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" AutoSearchControlType="Manufacturers" />
                    &nbsp;&nbsp;&nbsp;&nbsp;
                    <span  id="spnManufacturer"></span>
				</Field>
                
                
			</ReboundUI_Form:FormField>

            
             <ReboundUI_Form:FormField ID="ctlCountryOfManufacture" runat="server" FieldID="ddlCountryOfManufacture"
                ResourceTitle="CountryOfOrigin">
                <Field>
                    <ReboundDropDown:GlobalCountryList ID="ddlCountryOfManufacture" runat="server" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span  id="spnIHSCountryOfOrigin"></span>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<asp:Label ID="lblCountryOfOrigin"     runat="server" />
                   &nbsp;&nbsp; <asp:Label ID="lblCountryRiskStatus" runat="server" class="ihspartstatusdoc" />
                    </Field>
            </ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlDateCode" runat="server" FieldID="txtDateCode" ResourceTitle="DateCode" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtDateCode" runat="server" Width="60" MaxLength="5" UppercaseOnly="true" /></Field>			
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="ddlProduct" ResourceTitle="Product">
				<Field><ReboundDropDown:Product ID="ddlProduct" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
            <ReboundUI_Form:FormField id="ctlProduct" runat="server" FieldID="cmbProducts" ResourceTitle="Product" IsRequiredField="true">
				<Field>
                    <ReboundUI:Combo ID="cmbProducts" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site" PanelWidth="450" PanelHeight="250"  AutoSearchControlType="Products" />
				</Field>
			</ReboundUI_Form:FormField>
            <%--<asp:TableRow>
                <asp:TableCell>
                    <label id="lblihsproduct"  style="width: 180px; color:#d3fFcC; padding-right: 10px;padding-bottom: 7px;text-align: left;font-weight: bold;font-size: 11px; " ><%=Functions.GetGlobalResource("FormFields", "IHSProduct")%></label>
                     
                </asp:TableCell>
                <asp:TableCell>
                    <span id="spnIHSProduct" ></span>
                </asp:TableCell>
            </asp:TableRow>

              <asp:TableRow>
                <asp:TableCell>
                    <label id="lblihsHTSCode"  style="width: 180px; color:#d3fFcC; padding-right: 10px;padding-bottom: 7px;text-align: left;font-weight: bold;font-size: 11px; " ><%=Functions.GetGlobalResource("FormFields", "HTSCode")%></label>
                     
                </asp:TableCell>
                <asp:TableCell>
                   <span id="spnHTSCode" ></span>
                </asp:TableCell>
            </asp:TableRow>

               <asp:TableRow>
                <asp:TableCell>
                    <label id="lblduty"  style="width: 180px; color:#d3fFcC; padding-right: 10px;padding-bottom: 7px;text-align: left;font-weight: bold;font-size: 11px; " ><%=Functions.GetGlobalResource("FormFields", "Duty")%></label>
                     
                </asp:TableCell>
                <asp:TableCell>
                   <span id="spnIHSDutyCode" ></span>
                </asp:TableCell>

            </asp:TableRow>
            <asp:TableRow>
                <asp:TableCell>
                    <label id="lblECCNCode"  style="width: 180px; color:#d3fFcC; padding-right: 10px;padding-bottom: 7px;text-align: left;font-weight: bold;font-size: 11px; " ><%=Functions.GetGlobalResource("FormFields", "ECCNCode")%></label>
                </asp:TableCell>
                <asp:TableCell>
                    <span id="spnIHSECCNCode" ></span>
                </asp:TableCell>
            </asp:TableRow>--%>
            
			<ReboundUI_Form:FormField id="ctlPackage" runat="server" FieldID="cmbPackage" ResourceTitle="Package" IsRequiredField="false">
				<Field>
                    <%--<ReboundDropDown:Package ID="ddlPackage" runat="server" />--%>
                     <ReboundUI:Combo ID="cmbPackage" runat="server" AutoSearchControlAssembly="Rebound.GlobalTrader.Site"  AutoSearchControlType="Packages" />
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span  id="spnPackaging"></span>
                    
				</Field>
			</ReboundUI_Form:FormField>

			<ReboundUI_Form:FormField id="ctlQuantity" runat="server" FieldID="txtQuantity" ResourceTitle="Quantity" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtQuantity" runat="server" TextBoxMode="Numeric"/>&nbsp;&nbsp;<asp:Label ID="lblQty" runat="server" /></Field>
			</ReboundUI_Form:FormField>

            <ReboundUI_Form:FormField id="ctlOfferStatus" runat="server" FieldID="ddlOfferStatus" ResourceTitle="OfferStatus" IsRequiredField="false" >
				<Field><ReboundDropDown:OfferStatus ID="ddlOfferStatus" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			            
            <ReboundUI_Form:FormField id="ctlSPQ" runat="server" FieldID="txtSPQ" ResourceTitle="SPQ" IsRequiredField="false" >
				<Field><ReboundUI:ReboundTextBox ID="txtSPQ" runat="server" /></Field>
			</ReboundUI_Form:FormField>		
			
			<ReboundUI_Form:FormField id="ctlFactorySealed" runat="server" FieldID="txtFactorySealed" ResourceTitle="FactorySealed" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtFactorySealed" runat="server" /></Field>
			</ReboundUI_Form:FormField>

			<%--<ReboundUI_Form:FormField id="ctlROHSStatus" runat="server" FieldID="txtROHSStatus" ResourceTitle="ROHSStatus" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtROHSStatus" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>

			<%--<ReboundUI_Form:FormField id="ctlMSL" runat="server" FieldID="txtMSL" ResourceTitle="MSL"   >
				<Field><ReboundUI:ReboundTextBox ID="txtMSL" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>

              <ReboundUI_Form:FormField id="ctlMSL" runat="server" FieldID="ddlMsl" ResourceTitle="MSL" IsRequiredField="false">
				<Field><ReboundDropDown:MSLLevelNo ID="ddlMsl" runat="server" />
                    
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span  id="spnROHSName"></span>
				</Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlTQSA" runat="server" FieldID="txtTQSA" ResourceTitle="TQSA"  IsRequiredField="false" >
				<Field><ReboundUI:ReboundTextBox ID="txtTQSA" runat="server"  TextBoxMode="Numeric"/></Field>
			</ReboundUI_Form:FormField>
			<ReboundUI_Form:FormField id="ctlMOQ" runat="server" FieldID="txtMOQ" ResourceTitle="MOQ" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtMOQ" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlLTB" runat="server" FieldID="txtLTB" ResourceTitle="LTB" >
				<Field><ReboundUI:ReboundTextBox ID="txtLTB" runat="server" /></Field>
			</ReboundUI_Form:FormField>

           <%-- <ReboundUI_Form:FormField id="ctlCurrencyNew" runat="server" FieldID="ddlCurrency" ResourceTitle="Currency" IsRequiredField="true">
				<Field><ReboundDropDown:BuyCurrency ID="ddlCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>--%>
         
             <ReboundUI_Form:FormField id="ctlGlobalCurrency" runat="server" FieldID="ddlGlobalCurrency" ResourceTitle="SellCurrency" IsRequiredField="true">
				<Field><ReboundDropDown:BuyCurrencyByGlobalNo ID="ddlGlobalCurrency" runat="server" /></Field>
			</ReboundUI_Form:FormField>

                 <ReboundUI_Form:FormField id="ctlSupplierPrice" runat="server" FieldID="lblSupplierPrice" ResourceTitle="BuyPrice1" IsRequiredField="true" >
				<Field><ReboundUI:ReboundTextBox ID="lblSupplierPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> &nbsp;<asp:Label ID="lblSupplierPrice_Currency" runat="server" />  </Field>
			</ReboundUI_Form:FormField>
            	
			<ReboundUI_Form:FormField id="ctlPrice" runat="server" FieldID="txtPrice" ResourceTitle="UpliftPrice" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />&nbsp;&nbsp;<asp:Label ID="lblUpliftPrice" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<%--<ReboundUI_Form:FormField id="ctlPriceClient" runat="server" FieldID="txtcPrice" ResourceTitle="Price" IsRequiredField="true">
				<Field><ReboundUI:ReboundTextBox ID="txtcPrice" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" /> &nbsp;<asp:Label ID="txtcPrice_Currency" runat="server" />  </Field>
			</ReboundUI_Form:FormField>--%>
            <asp:TableRow ID="ctlSellPriceLessLabel">
                <asp:TableCell ColumnSpan="2" Style="padding-top:5px">
                    <label id="lblSellPriceLess"  style="color:#FFFF00; padding-bottom: 5px;font-weight: bold;font-size: 11px;">Uplift Sell Price is Less than Buy Price</label>
                </asp:TableCell>
            </asp:TableRow>
			<ReboundUI_Form:FormField id="ctlSellPriceLessReason" runat="server" FieldID="txtSellPriceLessReason" ResourceTitle="txtReason" IsRequiredField="false">
                <Field><ReboundUI:ReboundTextBox ID="txtSellPriceLessReason" runat="server" Width="400" TextMode="multiLine" MaxLength="128" /></Field>			
			</ReboundUI_Form:FormField>
		

             <ReboundUI_Form:FormField id="ctlEstimatedShippingCost" runat="server" FieldID="lblEstimatedShippingCost" ResourceTitle="EstimatedShippingCost" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="lblEstimatedShippingCost" runat="server" TextBoxMode="currency" FormatDecimalPlaces="true" Width="100" />  </Field>
			 </ReboundUI_Form:FormField>
			 
			 <ReboundUI_Form:FormField id="ctlLeadTime" runat="server" FieldID="txtLeadTime" ResourceTitle="LeadTime" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtLeadTime" runat="server" /></Field>
			</ReboundUI_Form:FormField>
			
			<ReboundUI_Form:FormField id="ctlRegion" runat="server" FieldID="ddlRegion" ResourceTitle="Region" IsRequiredField="false">
				<Field><ReboundDropDown:Region ID="ddlRegion" runat="server" NoValue_Value="" InitialValue=""/></Field>
			</ReboundUI_Form:FormField>

			 <ReboundUI_Form:FormField ID="ctlDeliveryDate" runat="server" FieldID="txtDeliveryDate"
                ResourceTitle="DeliveryDate" IsRequiredField="false">
                <Field>
                    <ReboundUI:ReboundTextBox ID="txtDeliveryDate" runat="server" Width="150" />
                    <ReboundUI:Calendar ID="calDeliveryDate" runat="server" RelatedTextBoxID="txtDeliveryDate" />
                </Field>
            </ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlROHSStatus" runat="server" FieldID="txtROHSStatus" ResourceTitle="ROHSStatus" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtROHSStatus" Width="50" runat="server" /></Field>
			</ReboundUI_Form:FormField>
            <%-- [001] code start--%>
            <ReboundUI_Form:FormField id="ctlSupplierWarranty" runat="server" FieldID="txtSupplierWarranty" ResourceTitle="SupplierWarranty">
				<Field><ReboundUI:ReboundTextBox ID="txtSupplierWarranty" runat="server" Width="50" MaxLength="10" TextBoxMode="Numeric"/> days</Field>
			</ReboundUI_Form:FormField>
            <%-- [001] code end--%>
			<ReboundUI_Form:FormField id="ctlNotes" runat="server" FieldID="txtNotes" ResourceTitle="Notes" IsRequiredField="false">
				<Field><ReboundUI:ReboundTextBox ID="txtNotes" runat="server" Width="400" TextMode="multiLine" MaxLength="128" /></Field>			
			</ReboundUI_Form:FormField>
            <ReboundUI_Form:FormField id="ctlTestingRecommended" runat="server" FieldID="chkTestingRecommended" ResourceTitle="TestingRecommended" IsRequiredField="false">
				<Field><ReboundUI:ImageCheckBox ID="chkTestingRecommended" runat="server"  Enabled="true"/></Field>			
			</ReboundUI_Form:FormField>
            
                <asp:TableRow>
                <asp:TableCell ColumnSpan="2">
                    <%--<script src="js/jquery.min.js"></script>--%>
                    <style type="text/css">
                        /* Button used to open the chat form - fixed at the bottom of the page */
                        .open-button {
                            /*top: 144px; 
                        left: 1076px;*/
                            position: absolute;
                            bottom: -3px;
                            right: 15px;
                            border: 3px solid #f1f1f1;
                            z-index: 9;
                            /* bottom: 28px; */
                            right: 1px;
                            width: 25px;
                            font-size: xx-large;
                            text-align: center;
                            height: 20px;
                            /* color: black; */
                            background-color: black;
                            /*padding-top: 5px;*/
                        }

                        .open-buttons {
                            /*top: 144px; 
                        left: 1076px;*/
                            /*position: absolute;*/
                            bottom: -3px;
                            /*right: 15px;*/
                            border: 3px solid #f1f1f1;
                            z-index: 9;
                            /* bottom: 28px; */
                            /*right: 1px;*/
                            width: 25px;
                            font-size: xx-large;
                            text-align: center;
                            height: 20px;
                            /* color: black; */
                            background-color: black;
                           /* padding-top: 5px;*/
                        }

                        /* The popup chat - hidden by default */
                        .chat-popup {
                            /*display: none;
                        position: absolute;*/
                            bottom: 29px;
                            border: 3px solid #f1f1f1;
                            z-index: 9;
                            bottom: 28px;
                            right: 1px;
                            width: 300px;
                            font-size: xx-large;
                            background-color: darkslategray;
                            text-align: right;
                            min-height: 100px;
                        }

                        .popupCloseButton {
                            border: 3px solid #999;
                            border-radius: 50px;
                            cursor: pointer;
                            display: inline-block;
                            font-family: arial;
                            font-weight: bold;
                            position: absolute;
                            top: -17px;
                            right: -5px;
                            font-size: 25px;
                            line-height: 10px;
                            width: 30px;
                            height: 20px;
                            text-align: center;
                            background-color: black;
                        }

                        #mydiv {
                            top: 110px;
                            right: 100px;
                            z-index: 9;
                            font-size: xx-large;
                            background-color: white;
                            text-align: right;
                            position: absolute;
                            height: 0px;
                            width: 450px;
                        }

                        #mydivheader {
                            top: 191px;
                            left: 800px;
                            cursor: move;
                            z-index: 10;
                        }

                        .tbForm {
                            color: black;
                            text-align: left;
                            width: 100%;
                            margin: 6px 0px;
                        }
                        /* IHS Part Search Popup css start */


                       /* The Modal (background) */
                        .modal {
                            display: none; /* Hidden by default */
                            position: fixed; /* Stay in place */
                            z-index: 1; /* Sit on top */
                            padding-top: 100px; /* Location of the box */
                            left: 0;
                            top: 0;
                            width: 100%; /* Full width */
                            height: 100%; /* Full height */
                            overflow: auto; /* Enable scroll if needed */
                            background-color: rgb(0,0,0); /* Fallback color */
                            background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
                        }

                        /* Modal Content */
                        .modal-content {
                            background-color: #66a75e;
                            margin: auto;
                            padding: 20px;
                            border: 1px solid #66a75e;
                            width: 50%;
                            position: relative;
                            overflow: hidden;
                        }

                            .modal-content table tr td, .modal-content table tr th {
                                text-align: left;
                            }
                        /* The Close Button */
                        .close {
                            color: #aaaaaa;
                            float: right;
                            font-size: 28px;
                            font-weight: bold;
                        }

                            .close:hover,
                            .close:focus {
                                color: #000;
                                text-decoration: none;
                                cursor: pointer;
                            }

                        

                           

                        .GridPartdetails table.dataTable td {
                            border-color: #5d8857;
                            border-top-color: rgb(93, 136, 87);
                            background-color: #c8fac2;
                            color: #000;
                            font-size: 10px;
                            font-family: Verdana !important;
                            position: relative;
                            border-top: none !important;
                        }

                        .LoaderPopup {
                            background: rgba(0,0,0,.4);
                            cursor: pointer;
                            display: none;
                            /* height: auto; */
                            height: 93%;
                            position: absolute;
                            text-align: center;
                            top: -49px;
                            width: 92%;
                            z-index: 10000;
                        }

                        .cssload-loader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206,66,51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }


                        .loader {
                            border: 16px solid #f3f3f3;
                            border-radius: 50%;
                            border-top: 16px solid black;
                            border-right: 16px solid black;
                            border-bottom: 16px solid black;
                            border-left: 16px solid black;
                            width: 50px;
                            height: 50px;
                            -webkit-animation: spin 2s linear infinite;
                            animation: spin 2s linear infinite;
                        }

                        @-webkit-keyframes spin {
                            0% {
                                -webkit-transform: rotate(0deg);
                            }

                            100% {
                                -webkit-transform: rotate(360deg);
                            }
                        }

                        @keyframes spin {
                            0% {
                                transform: rotate(0deg);
                            }

                            100% {
                                transform: rotate(360deg);
                            }
                        }

                        
                          table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB__fr {
                        }


                        table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr {
                            width: 86%;
                            /*border-radius: 4px;
                            padding: 15px;
                            margin: 1% 7% !important;*/
                        }

                        table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr {
                            background-color: #66a75e;
                        }

                            table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr tr.lastrow {
                                border-bottom: 10px #56954e solid;
                            }

                            table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr td {
                                /*width: 30%;*/
                                padding-top: 5px!important;
                                padding-left: 15px!important;
                                padding-right: 15px!important;
                            }

                                table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr td.lasttd {
                                    padding-bottom: 15px;
                                }

                                table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr td label {
                                    width: 100%;
                                    display: block;
                                    margin-bottom: 3px;
                                    text-align: left;
                                    font-weight: bold;
                                    font-size: 11px;
                                    color: #d3fFcC;
                                }

                                table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr td span {
                                    /*width: 75%;
                                    display: block;*/
                                }

                            table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_frm textarea,
                            table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_frm input,
                            table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_frm select {
                                font-size: 11px;
                                border-style: none;
                                padding: 2px;
                                width: 250px;
                                float: left;
                            }
							table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_frm span input[type="radio"],
							table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_frm span label
							{width:11% !important;
display: block !important;
float: left !important}

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn1 {
                            width: 20px !important;
                            height: 20px;
                            float: left;
                            margin-left: 5px;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_lblError {
                            width: 100%;
                            float: left;
                            margin-left: 0px;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_lblError,
                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn2,
                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn1 {
                            background-color: transparent;
                            width: auto;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn2 {
                            border: 1px #5e845a solid;
                            border-radius: 2px;
                            float: left;
                            width: auto !important;
                            background: #5e845a;
                        }

                        .ReqSection3 span input[type="radio"] {
                            width: auto!important;
                        }

                        .ReqSection3 span label {
                            width: 24% !important;
                            vertical-align: middle;
                            display: inline-block;
                        }

                        #ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_calRFQClosingDate_ctl06 tr td {
                            background-color: #fff;
                            width: auto!important;
                            padding-top: 0;
                            padding-left: 0;
                            padding-right: 0;
                        }

                        .LoaderIHSPopup {
                            background: rgba(0,0,0,.4);
                            cursor: pointer;
                            display: none;
                            /* height: auto; */
                            height: 100%;
                            position: absolute;
                            text-align: center;
                            top: -34px;
                            width: 1300px;
                            right: 100px;
                            z-index: 10000;
                        }
                         .cssloadIHSloader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206,66,51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPartDetail_pnlFieldControls { /*position: absolute;top: 0;top: 28%;left: 33%*/
                            width: 100%;
                            border: 1px #7eb757 solid;
                        }

                        #ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_radObsolete br {
                            display: none;
                        }

                        .tbForm tr {
                            background-color: transparent!important;
                        }

                        #mydivheader .chat-popup {
                            width: auto!important;
                        }

                        #mydivheader .popupCloseButton {
                            border: 2px solid #fff;
                            top: -11px;
                            right: -8px;
                            width: 20px;
                            height: 20px;
                        }
						
#tbpartdet{box-shadow: 2px 1px 22px 3px #000;width:450px!important;}
.firstth {
    background-color: #5490ce;
    color: #fff;
    font-size: 12px !important;
    text-align: center !important;
    padding: 10px;
}
.firttd{background-color:#1d66b0;padding: 10px 15px !important;color: #fff !important;width:40%;vertical-align: top;font-size: 12px !important;}
.secondtd{background-color:#1c334a;padding:10px 15px !important;color: #fff !important;vertical-align: top;font-size: 12px !important;}
.last-td{background-color:#5490ce;padding:10px 15px !important;color: #fff !important;vertical-align: top;font-size: 12px !important;}
.itemSearch {
    padding: 0px;
}
.itemSearch .pagingControls {
    border: 1px solid #333;
    padding: 10px 5px;
    background-color: #333;
}
#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl12_pnlScroll{height: 193px;
overflow: auto;}
#myModal{height:100% !important;}
.modal{top:25px;}
.modal-content {
    box-shadow: 2px 2px 20px 1px #000;
    border: 3px #385e26 solid;
}
.itemSearch table.dataTable td {
    background-color: #fff !important;color: #333;
}
.dataTableHeader, .itemSearch table.dataTable tr th{border-color: #bdbdbd;
background-color: #bdbdbd !important;color: #333!important;}
.itemSearch table.dataTable tr td, .itemSearch table.dataTable tr th{color: #333!important;}
                        /****add screen ------***/

                        .floatna input {
                            vertical-align: middle!important;
                        }

                        .floatna input, .floatna label {
                            float: none!important;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_pnlContentInner {
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr tr td {
                            padding: 4px 0px;
                        }

                            #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr tr td label {
                                width: 100%;
                               /* display: inline-block;*/
                                margin-bottom: 4px;
                                font-weight: 600;
                            }

                            #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr tr td span {
                                width: 100%;
                            }


                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn1 {
                            margin-left: 4px;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_lblError {
                            display: block;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr .ReqSection3 span label {
                            width: 11% !important;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn6 {
                             cursor: pointer;
                            font-size: 22px;
                            color: #fff;
                            width: auto;
                            margin-top: -16px;
                            margin-right: -16px;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_SearchtxtPartNo {
                            width: 45%;
                            margin-right: 10px;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn3 {
                           cursor: pointer;
width: auto;
padding: 11px 15px !important;
background: #5e845a;
display: block;
float: left;
border-radius: 2px;
border: 1px #5e845a solid;
margin: 0 !important;
                        }

                        #myModal .modal-content span, #myModal .modal-content label, #myModal input {
                            width: auto!important;
                        }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctlPartDetail_tdTitle {
                            display: none;
                        }

                        .GridPartdetails .dataTableHeader table.dataTable th {
                            background-color: #5e845a!important;
                            color: #ffffff;
                            border-color: #85a681!important;
                            font-size: 9px!important;
                            font-family: Verdana !important;
                        }

                        .GridPartdetails table.dataTable td {
                            border-color: #5d8857;
                            background-color: #5d8857;
                            color: #ffffff;
                            font-size: 10px;
                            font-family: Verdana !important;
                            position: relative;
                            border-top: none!important;
                        }

                        table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl12_tblHeader tr td, 
                        table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl12_tblHeader tr th{width:20%! important;}
                        #ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_calRFQClosingDate_ctl06 tr td,
                        #ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_calDateRequired_ctl06 tr td,
                        #ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_calCustomerDecisionDate_ctl06 tr td {
                            background-color: #fff;
                            width: auto !important;
                            padding-top: 0 !important;
                            padding-left: 0 !important;
                            padding-right: 0 !important;
                        }

                        #myModal .ReqSection2 {
                            float: left;
                            margin-right: 10px;
                            margin-left: 10px;
                            margin-top: -14px;
                        }

                            #myModal .ReqSection2 br {
                                display: none;
                            }

                            #myModal .ReqSection2 .headfield {
                            }

                            #myModal .ReqSection2 label {
                                float: left;
                                margin-right: 5px;
margin-top: 10px;
                            }

                        #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn5 {
                            display: none!important;
                        }

                        #myModal table#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_fr td label {
                            display: block;
                            margin-bottom: 3px;
                            text-align: left;
                            font-size: 12px;
                            color: #fff;
                        }

                        span.PartDetailsGridGoBtn2 {
    padding: 2px 0px!important;
    margin-left: 1px!important;
}
          #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn7 {
    cursor: pointer;
    width: auto;
    padding: 11px 15px !important;
    background: #5e845a;
    display: block;
    float: left;
    border-radius: 2px;
    border: 1px #5e845a solid;
    margin-left: 6px !important;
}              
#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn3 {
     cursor: pointer; 
     width: auto; 
     padding: 11px 15px !important; 
     background: #5e845a; 
     display: block; 
     float: left; 
     border-radius: 2px; 
     border: 1px #5e845a solid; 
     margin: 0 !important; 
}
                        .LoaderPopup {
                            background: rgba(0,0,0,.4);
                            cursor: pointer;
                            display: none;
                            /* height: auto; */
                            height: 100%;
                            position: absolute;
                            text-align: center;
                            top: -32px;
                            width: 95%;
                            z-index: 10000;
                        }

                        .cssload-loader {
                            width: 244px;
                            height: 49px;
                            line-height: 49px;
                            text-align: center;
                            position: absolute;
                            left: 50%;
                            top: 50%;
                            transform: translate(-50%, -50%);
                            -o-transform: translate(-50%, -50%);
                            -ms-transform: translate(-50%, -50%);
                            -webkit-transform: translate(-50%, -50%);
                            -moz-transform: translate(-50%, -50%);
                            font-family: helvetica, arial, sans-serif;
                            text-transform: uppercase;
                            font-weight: 900;
                            font-size: 18px;
                            color: rgb(206,66,51);
                            letter-spacing: 0.2em;
                            background-color: aliceblue;
                        }

                       
                        .modal {
                            height: auto!important;
                        }

                        .modal-content {
                            0px 0px 15px 7px #000;
                        }

                            .modal-content input {
                                padding: 12px !important;
                            }

                        .headertitlet {
                            font-size: 15px;
                        }

                        .headertitle {
                            background: #5f9553;
                            padding: 12px;
                        }

                        .allradio {
                            margin-top: 10px;
                            margin-bottom: 10px;
                            background: #80bd73;
                            padding: 10px;
                        }

                        .twobtn {
                           border-bottom: 1px #558838 solid;
                            float: left;
                            width: 100%;
                            padding-bottom: 15px;
                        }

                        .headertitle {
                            display: block;
                            float: left;
                        }

                        .model-content #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_SearchtxtPartNo {
                        }

                        .searcttype {
                           background: transparent !important;
                           
                            margin: 0px 0 0px 0 !important;
                        }


                       
                        /*#ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_btn3, #ctl00_cphMain_ctlAdd_ctlDB_ctl14_ctlAdd_ctlDB_btn7 {
    cursor: pointer;
    float: left;
    display: block;
    margin-top: 10px;
}*/

                        #myModal .modal-content span, #myModal .modal-content label, #myModal input {
    width: auto!important;
}

  .okbtn {
                                cursor: pointer;
    position: absolute;
    bottom: 38px;
    right: 35px;
    padding: 10px 15px;
    background: #5e845a;
    float: left;
    border-radius: 2px;
    border: 1px #5e845a solid;
    font-weight: bold;display:block!important
}

                            .okbtn:hover {
                                background: #56a34e;
                            }
							
	.okbtn a{
	color: #fff!important;
margin: 0!important;
text-align: center!important;
padding: 0 5px!important;
	}						
							
							
table.formRows select{padding:1px!important;}

.bgbase{float:left;background-color:#5f9553;}
.bgbase input[type="radio"]{width:auto;}
table.itemSearchFilters {
    margin-top: 25px;
}
#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn6{display: block;
background: #333;
padding: 11px;}
#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_SearchtxtPartNo{width:150px;}
.formRows label {
    padding-right: 7px;
    vertical-align: inherit;
    font-weight: bold;
    font-size: 11px;
    color: #d3fFcC;
}
.bgbase input[type="radio"] {
    width: auto;
}
#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn3, #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_btn7{padding: 12px 30px;
display: block;
float: left;
background: #44732f}
                        /* IHS Part Search Popup css start */
						
						
						
						#ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl12_tblHeader tr th, #ctl00_cphMain_ctlSourcingResults_ctlDB_ctl14_ctlAdd_ctlDB_ctltblPartdetails_ctlDB_ctl12_tbl tr td{width:20%!important;}
                    </style>



                    <!-- IHS Part Search Popup modal popup start -->
                    <div id="myModal" class="modal" style="height: 700px;">
                        <!-- Modal content -->
                        <div class="modal-content" style="height: 488px; 0px 0px 15px 0px #000; width: 800px;">
                            <%-- <span class="close">&times;</span>--%>
                            <div class="LoaderPopup" id="divLoader">
                                <div>
                                    <div class="cssload-loader">Loading..</div>
                                </div>
                            </div>
                            <asp:Label ID="btn6" Text="Cancel" runat="server" Style="cursor: pointer;" CssClass="PartDetailsGridGoBtn2 close">&times;</asp:Label>
                            <div class="tophead">
                           
                                    <div  class="bgbase">
                                    <span class="headertitle">Part No</span>
                                    <ReboundUI:ReboundTextBox ID="SearchtxtPartNo" runat="server" UppercaseOnly="true" placeholder="Type 3 chars to search" />
</div>
                                     <div  class="bgbase">
                                    <asp:TableCell class="ReqSection2 searcttype">
										<div Class="headertitle"><%=Functions.GetGlobalResource("FormFields", "SearchType")%></div>                   
									   <label>
									   <input type="radio" name="searchType"    value="startswith" >&nbsp;Starts with</label>
									   <label> 
									   <input type="radio" name="searchType"   value="contains" checked="checked">&nbsp;Contains</label>
									   <label> 
									   <input type="radio" name="searchType"    value="exact" >&nbsp;Exact</label>
                                    </asp:TableCell>
                                         </div>
                                    <div  class="bgbase">
                                    <asp:Label ID="btn3" Text="Go" runat="server" Style="cursor: pointer;" CssClass="PartDetailsGridGoBtn2"></asp:Label>&nbsp&nbsp
                                    <asp:Label ID="btn7" Text="Clear" runat="server" Style="cursor: pointer;" CssClass="PartDetailsGridGoBtn2"></asp:Label>
                                    </div>
                                
                                        <div>
                                            <label id="lblihserror" style="color: #FFFFFF; font-style: normal; font-family: Tahoma !important; font-size: 12px;"></label>
                                              <label id="lblIhsServiceMessage" style="color: #FFFFFF; font-style: normal; font-family: Tahoma !important; font-size: 12px;"></label>
                                        </div>
                                   

                                        <ReboundItemSearch:IhsSearch ID="ctltblPartdetails" runat="server" UppercaseOnly="true" />

                                        <asp:Panel ID="pnlPartDetail" runat="server">
                                        </asp:Panel>
                                   
                                    <div class="okbtn">
                                        <ReboundUI:IconButton ID="btnOK" runat="server"  IconButtonMode="HyperLink" IconCSSType="Add" IconTitleResource="OK" />
                                        <asp:Label ID="btn5" Text="Cancel" runat="server" Style="cursor: pointer;" CssClass="PartDetailsGridGoBtn2"></asp:Label>
                                    </div>
                                
                                </div>
                        </div>

                    </div>

                    <!-- IHS Part Search Popup modal popup end -->
                </asp:TableCell>


            </asp:TableRow>
		</ReboundUI_Table:Form>
	</Content>
</ReboundUI_Form:DesignBase>
