///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.FormFieldCollections");
Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplierApproval = function(element) {
    Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplierApproval.initializeBase(this, [element]);
    this._aryRecipientLoginIDs = [];
    this._aryRecipientLoginNames = [];
    this._aryRecipientGroupIDs = [];
    this._aryRecipientGroupNames = [];
    this._intNumberRecipients = 0;
    this._aryRecipientEmail = [];
    this._aryCompanyIDs = [];
    this._suppliersIdEmail = [];
};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplierApproval.prototype = {
    get_pnlSelected: function() { return this._pnlSelected; }, set_pnlSelected: function(value) { if (this._pnlSelected !== value) this._pnlSelected = value; },
    get_lblSelected: function() { return this._lblSelected; }, set_lblSelected: function(value) { if (this._lblSelected !== value) this._lblSelected = value; },
    get_autLoginOrGroup: function() { return this._autLoginOrGroup; }, set_autLoginOrGroup: function(value) { if (this._autLoginOrGroup !== value) this._autLoginOrGroup = value; },

    initialize: function() {
        //this._autLoginOrGroup.addSelectionMadeEvent(Function.createDelegate(this, this.newRecipientSelected));
        
//        Array.clear(this._aryRecipientLoginIDs);
//        Array.clear(this._aryRecipientLoginNames);
//        Array.clear(this._aryRecipientGroupIDs);
//        Array.clear(this._aryRecipientGroupNames);
//        Array.clear(this._aryCompanyIDs);
//        Array.clear(this._aryRecipientEmail);
            
        this.showSelected();
       this.newRecipient();
      // this.setKeyValueArray()
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplierApproval.callBaseMethod(this, "initialize");
    },
    dispose: function() {
        if (this.isDisposed) return;
        //if (this._autLoginOrGroup) this._autLoginOrGroup.dispose();
        this._pnlSelected = null;
        this._lblSelected = null;
        //this._autLoginOrGroup = null;
        this._aryRecipientLoginIDs = null;
        this._aryRecipientLoginNames = null;
        this._intNumberRecipients = null;
        this._aryRecipientEmail = null;
        this._aryCompanyIDs = null;
        Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplierApproval.callBaseMethod(this, "dispose");
    },

    newRecipientSelected: function(blnShow) {
        //this.addNewLoginRecipient(this._autLoginOrGroup._varSelectedID, this._autLoginOrGroup._varSelectedValue, this._autLoginOrGroup._varSelectedExtraData);
    },

    newRecipient: function() {
        //    
       // this.addNewLoginSupplier(this._autLoginOrGroup._varSelectedID, this._autLoginOrGroup._varSelectedValue, this._autLoginOrGroup._varSelectedExtraData);
    },
    addNewLoginSupplier: function(strEmail) {
//        //alert(strEmail);
//        if (strEmail != null) {
//            //Array.add(this._aryRecipientEmail, strEmail);
//        }
//        this.showSelected();
//        this._autLoginOrGroup.reselect();
    },

    addNewLoginRecipient: function(intID, strName, strEmail) {    
        //this._suppliersIdEmail.push({cmpId:intID,email:strEmail,supname:strName}); 
        this.addKeyValueArray(intID,strEmail,strName);
        this._intNumberRecipients += 1;
                
        this.showSelected();
        //this._autLoginOrGroup.reselect();
    },

//    showSelected: function() {        
//        $R_FN.showElement(this._pnlSelected, (this._intNumberRecipients > 0));
//        var strFN = "";
//        var strHTML = "";
//        for (i = 0, l = this._aryRecipientLoginIDs.length; i < l; i++) {
//            strHTML += '<div class="mailRecipient">';
//            strHTML += this._aryRecipientLoginNames[i] + '&nbsp;&nbsp;&nbsp&nbsp;&nbsp;&nbsp';
//            strHTML += String.format(" <input id=" + "Email" + i + " type=\"text\"  style=\"width: 250px;\" onblur=\"$find('{0}').addLoginRecipient({1});\"  value=" + this._aryRecipientEmail[i] + " >", this._element.id, i);
//            strFN = String.format("$find('{0}').removeLoginRecipient({1});", this._element.id, i);
//            strHTML += String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]</a>', strFN);
//            strHTML += "</div>";

//        }

//        $R_FN.setInnerHTML(this._lblSelected, strHTML);

//    },

showSelected: function() {        
        $R_FN.showElement(this._pnlSelected, (this._intNumberRecipients > 0));
        var strFN = "";
        var strHTML = "";
        for (i = 0, l = this._suppliersIdEmail.length; i < l; i++) {
            strHTML += '<div class="mailRecipient">';
            strHTML += this._suppliersIdEmail[i].supname + '&nbsp;&nbsp;&nbsp&nbsp;&nbsp;&nbsp';
            strHTML += String.format(" <input id=" + "Email" + i + " type=\"text\"  style=\"width: 250px;\" onchange=\"$find('{0}').addLoginRecipient({1},{2},'{3}','{4}');\"  value=" + this._suppliersIdEmail[i].email + " >", this._element.id, i,this._suppliersIdEmail[i].cmpId,this._suppliersIdEmail[i].email,this._suppliersIdEmail[i].supname);
            strFN = String.format("$find('{0}').removeLoginRecipient({1});", this._element.id, this._suppliersIdEmail[i].cmpId);
            strHTML += String.format('&nbsp;&nbsp;&nbsp;<a href="javascript:void(0);" class="quickSearchReselect" onclick="{0}">[x]</a>', strFN);
            strHTML += "</div>";
        }
        $R_FN.setInnerHTML(this._lblSelected, strHTML);
    },

    addLoginRecipient: function(index,cmpId,email,supname) {
    //Array.removeAt(this._aryRecipientEmail, i);
   // Array.add(this._aryRecipientEmail, document.getElementById('Email' + i).value);    
     this.removeKeyValueArray(cmpId);  
    // this.showSelected(); 
    //this._suppliersIdEmail.push({cmpId:cmpId,email:document.getElementById('Email' + index).value,supname:supname}); 
    this.addKeyValueArray(cmpId,document.getElementById('Email' + index).value,supname);
    //alert(this._suppliersIdEmail[this._suppliersIdEmail.length-1].email);
    this._intNumberRecipients += 1;
    },
//    removeLoginRecipient: function(i) {
//        Array.removeAt(this._aryRecipientLoginIDs, i);
//        Array.removeAt(this._aryRecipientLoginNames, i);
//        Array.removeAt(this._aryRecipientEmail, i);
//        Array.removeAt(this._aryCompanyIDs, i);
//        this._intNumberRecipients -= 1;
//        alert(this._aryCompanyIDs);
//        this.showSelected();
//    },

 removeLoginRecipient: function(cmpid) {
//        Array.removeAt(this._aryRecipientLoginIDs, i);
//        Array.removeAt(this._aryRecipientLoginNames, i);
//        Array.removeAt(this._aryRecipientEmail, i);
//        Array.removeAt(this._aryCompanyIDs, i);
        this._intNumberRecipients -= 1;
       
        this.removeKeyValueArray(cmpid);
        this.showSelected();
    },
    
    removeKeyValueArray:function(key)
    {
       for(var i = 0; i < this._suppliersIdEmail.length; ++i)
        {
            if(this._suppliersIdEmail[i].cmpId == key)
            {
                delete this._suppliersIdEmail.splice(i,1);
                return;
            }
        }
    },
    
       addKeyValueArray:function(cmpId,stremail,strSupname)
      {
      var exists=true;
       for(var i = 0; i < this._suppliersIdEmail.length; ++i)
        {
            if(this._suppliersIdEmail[i].cmpId == cmpId)
            {
                var exists=false;
               break;
            }
        }
        
      if(exists)  this._suppliersIdEmail.push({cmpId:cmpId,email:stremail,supname:strSupname});
       //Array.add(this._aryRecipientEmail, stremail);      
       //Array.add(this._aryRecipientEmail, stremail); 
    },
    setKeyValueArray:function(cmpId,stremail,strSupname)
      {
      Array.clear(this._aryRecipientEmail);
      Array.clear(this._aryCompanyIDs); 
      Array.clear(this._aryRecipientLoginNames);     
      for(var i = 0; i < this._suppliersIdEmail.length; ++i)
        {
          Array.add(this._aryRecipientEmail, this._suppliersIdEmail[i].email);
          Array.add(this._aryCompanyIDs, this._suppliersIdEmail[i].cmpId); 
          Array.add(this._aryRecipientLoginNames, this._suppliersIdEmail[i].supname); 
        }
      },

    setValue_Subject: function(strValue) {
        this._ctlRelatedForm.setFieldValue("ctlSubject", strValue);
    },
    

    getValue_Subject: function() {
        return this._ctlRelatedForm.getFieldValue("ctlSubject");
    },

    setValue_Body: function(strValue) {
        this._ctlRelatedForm.setFieldValue("ctlBody", strValue);
    },

    getValue_Body: function() {
        return this._ctlRelatedForm.getFieldValue("ctlBody");
    },

    validateFields: function() {
        var blnOK = true;
        if (!this._ctlRelatedForm.checkFieldEntered("ctlBody")) blnOK = false;
        if (!this._ctlRelatedForm.checkFieldEntered("ctlSubject")) blnOK = false;
        return blnOK;
    },

    resetFields: function() {
        this.setValue_Subject("");
        this.setValue_Body("");
        Array.clear(this._aryRecipientLoginIDs);
        Array.clear(this._aryRecipientLoginNames);
        Array.clear(this._aryRecipientEmail);
        //		Array.clear(this._aryRecipientGroupNames);
        this._intNumberRecipients = 0;
        this.showSelected();
    }

};

Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplierApproval.registerClass("Rebound.GlobalTrader.Site.Controls.FormFieldCollections.SendMailMessageSupplierApproval", Rebound.GlobalTrader.Site.Controls.Forms.LabelFormField, Sys.IDisposable);
