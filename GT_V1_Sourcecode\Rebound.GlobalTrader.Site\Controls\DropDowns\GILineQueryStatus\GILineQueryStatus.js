Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.DropDowns");Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus=function(n){Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.initializeBase(this,[n])};Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.prototype={get_IsPartialGIQueryStatus:function(){return this._IsPartialGIQueryStatus},set_IsPartialGIQueryStatus:function(n){this._IsPartialGIQueryStatus!==n&&(this._IsPartialGIQueryStatus=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.callBaseMethod(this,"initialize");this.addSetupDataCall(Function.createDelegate(this,this.setupDataCall));this.addGetDataOK(Function.createDelegate(this,this.dataCallOK))},dispose:function(){this.isDisposed||(this._IsPartialGIQueryStatus=null,Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.callBaseMethod(this,"dispose"))},setupDataCall:function(){this._objData.set_PathToData("controls/DropDowns/GILineQueryStatus");this._objData.set_DataObject("GILineQueryStatus");this._objData.set_DataAction("GetData");this._objData.addParameter("IsPartialGIQueryStatus",this._IsPartialGIQueryStatus)},dataCallOK:function(){var n=this._objData._result,t;if(n!=null&&n.Types)for(t=0;t<n.Types.length;t++)this.addOption(n.Types[t].Name,n.Types[t].ID)}};Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus.registerClass("Rebound.GlobalTrader.Site.Controls.DropDowns.GILineQueryStatus",Rebound.GlobalTrader.Site.Controls.DropDowns.Base);