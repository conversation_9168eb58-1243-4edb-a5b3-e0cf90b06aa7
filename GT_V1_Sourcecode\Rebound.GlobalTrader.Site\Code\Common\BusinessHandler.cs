﻿//Marker     Changed by      Date         Remarks
//[0001]     <PERSON>     21/06/2022   /// code for converting dictionary to concurrent dictionary
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Xml.Serialization;
using System.Collections.Concurrent;

namespace Rebound.GlobalTrader.Site
{
    internal class BusinessHandler: IAPIHandler
    {
        string msg = string.Empty;

        public string DataToPost { get; set; }

        public string APIURL { get; set; }

        public BusinessHandler(string strData, string apiUrl)
        {
            this.DataToPost = strData;
            this.APIURL = apiUrl;
        }
        //[0001] code start
        //public QlikTicket GetTicket(Dictionary<string, string> req)
        public QlikTicket GetTicket(ConcurrentDictionary<string, string> req)
        {
            QlikTicket ticket = new QlikTicket();            
            try
            {
                RequestWebAPI api = new RequestWebAPI(this.DataToPost, this.APIURL);
                ticket = GTXMLSerializer<QlikTicket>.DeSerialize(api.Result);
            }
            catch (Exception ex)
            {
                ticket.Message = ex.Message;
                ticket.IsValid = false;
            }
            return ticket;
        }

        //public Dictionary<string, string> GetRequestCollection()
        public ConcurrentDictionary<string, string> GetRequestCollection()
        {
            var dataDict = this.DataToPost.Split('&').Select(o => o.Split('=')).Where(o => o.Length == 2).ToDictionary(o => o[0].ToUpper(), o => o[1].ToUpper());
            var newDict = new ConcurrentDictionary<string, string>();
            newDict.TryAdd(dataDict.Keys.ToString(), dataDict.Values.ToString());
            return newDict;
        }
        //[0001] code start
    }

    public interface ICustomMessage
    {
        string Message { get; set; }
        bool IsValid { get; set; }
    }

    public class QlikTicket : ICustomMessage
    {
        public QlikTicket()
        {
            this.QlikTicketID = "";
        }
        public string QlikTicketID { get; set; }
        public string Message { get; set; }
        public bool IsValid { get; set; }

    }

    internal class GTXMLSerializer<T>
    {
        // Method does not read the unicode character
        //internal static T DeSerialize(string xmlString)
        //{
        //    XmlSerializer xmlSerializer;
        //    MemoryStream memStream = null;

        //    //Remove root element
        //    int start = xmlString.IndexOf("?>") + 2;
        //    int end = xmlString.Length - start;
        //    xmlString = xmlString.Substring(start, end);

        //    try
        //    {
        //        xmlSerializer = new XmlSerializer(typeof(T));
        //        byte[] bytes = new byte[xmlString.Length];
        //        Encoding.ASCII.GetBytes(xmlString, 0, xmlString.Length, bytes, 0);
        //        memStream = new MemoryStream(bytes);
        //        object objectFromXml = xmlSerializer.Deserialize(memStream);
        //        return (T)objectFromXml;
        //    }
        //    catch (Exception Ex)
        //    {
        //        throw Ex;
        //    }
        //    finally
        //    {
        //        if (memStream != null) memStream.Close();
        //    }
        //}



        /// <summary>
        /// Read the unicode character
        /// </summary>
        /// <param name="xmlString"></param>
        /// <returns></returns>
        internal static T DeSerialize(string xmlString)
        {
            try
            {
                using (TextReader reader = new StringReader(xmlString))
                {
                    XmlSerializer xs = new XmlSerializer(typeof(T));
                    return (T)xs.Deserialize(reader);
                }
            }
            catch (Exception Ex)
            {
                throw Ex;
            }
        }
    }
    public class QlikCommon
    {
        public const string _TOKEN = "0x002476EA4B1E4E4D8E277FA5C2FE8DB101000000867DFEEDCC917864CA3B3A44B3A81FDCF5BFB4416682C28AEACDDFD6727517A2";

        public enum QlikAction
        {
            QlikSense
        }
    }
   
}
