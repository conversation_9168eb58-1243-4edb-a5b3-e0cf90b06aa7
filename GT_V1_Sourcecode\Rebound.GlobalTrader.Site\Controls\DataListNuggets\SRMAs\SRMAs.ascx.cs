//-----------------------------------------------------------------------------------------
// RP 23.12.2009:
// - render state on server
//-----------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Configuration;
using System.Collections;
using System.Web;
using System.Web.Security;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.WebControls.WebParts;
using System.Web.UI.HtmlControls;
using Rebound.GlobalTrader.Site.Controls;

namespace Rebound.GlobalTrader.Site.Controls.DataListNuggets {
	public partial class SRMAs : Base {

		#region Properties


		#endregion

		#region Overrides

		/// <summary>
		/// OnInit
		/// </summary>
		/// <param name="e"></param>
		protected override void OnInit(EventArgs e) {
			SetDataListNuggetType("SRMAs");
			base.OnInit(e);
			TitleText = Functions.GetGlobalResource("Nuggets", "SRMAs");
			AddScriptReference("Controls.DataListNuggets.SRMAs.SRMAs");
			SetupTable();
		}

		protected override void OnLoad(EventArgs e) {
			_scScriptControlDescriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataListNuggets.SRMAs", ctlDesignBase.ClientID);
            _scScriptControlDescriptor.AddProperty("blnPOHub", Convert.ToBoolean(SessionManager.IsPOHub));
            base.OnLoad(e);
		}

		protected override void RenderAdditionalState() {
			string strViewLevel = this.GetSavedStateValue("ViewLevel");
			if (!string.IsNullOrEmpty(strViewLevel)) {
				((Pages.Content)Page).CurrentTab = Convert.ToInt32(strViewLevel);
				_enmViewLevel = (ViewLevelList)Convert.ToInt32(strViewLevel);
				this.OnAskPageToChangeTab();
			}
			base.RenderAdditionalState();
		}

		#endregion

		private void SetupTable() {
			_tbl.AllowSelection = false;
			_tbl.SortColumnDirection = Rebound.GlobalTrader.Site.Enumerations.SortColumnDirection.DESC;
			_tbl.Columns.Add(new FlexiDataColumn("Number", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
			_tbl.Columns.Add(new FlexiDataColumn("PartNo", "Manufacturer", WidthManager.GetWidth(WidthManager.ColumnWidth.PartNo), true));
			_tbl.Columns.Add(new FlexiDataColumn("Quantity", WidthManager.GetWidth(WidthManager.ColumnWidth.Quantity), true));
			_tbl.Columns.Add(new FlexiDataColumn("Company", "Contact", Unit.Empty, true));
			_tbl.Columns.Add(new FlexiDataColumn("SupplierRMADate", WidthManager.GetWidth(WidthManager.ColumnWidth.Date), true));
			_tbl.Columns.Add(new FlexiDataColumn("PurchaseOrder", WidthManager.GetWidth(WidthManager.ColumnWidth.SystemDocumentNumber), true));
		}

	}
}