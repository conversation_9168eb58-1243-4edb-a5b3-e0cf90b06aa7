﻿
GO

/****** Object:  StoredProcedure [dbo].[usp_insert_QuoteLine]    Script Date: 12/11/2024 10:00:43 AM ******/
SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE OR ALTER PROCEDURE [dbo].[usp_insert_QuoteLine]                                          
--******************************************************************************************                                         
--* RP 14.12.2009:                                         
--* - carry over details from SourcingResult (we can now add new lines from a sourcing                                         
--*   result in the add new line form)                                         
--*                                         
--* SK 29.10.2009:                                         
--* - allow for new column - FullCustomerPart - used for searching                                         
--*                                         
--* SK 29/07/2009:                                         
--* - allow for Notes                                       
--*Marker     changed by      date          Remarks                                     
--*[001]      Vinay           21/01/2014   CR:- Add AS9120 Requirement in GT application                                  
--*[002]      Suhail           12/04/2018  Added New column MSL Level                        
--*[003]      anand           25/08/2020  IHS data Passing            
--*[004]   Ravi     01/09/2023   RP-2230 & RP-2340 (AS6081)         
--******************************************************************************************                                         
    @QuoteNo int                                         
  , @Part nvarchar(30)                                         
  , @ManufacturerNo int = NULL                                         
  , @DateCode nvarchar(5) = NULL                                         
  , @PackageNo int = NULL                                         
  , @Quantity int                                         
  , @Price float                                         
  , @ETA nvarchar(20) = NULL                                         
  , @Instructions nvarchar(max) = NULL                                         
  , @ProductNo int = NULL                                         
  , @ReasonNo int = 0                                         
  , @CustomerPart nvarchar(30) = NULL                                         
  , @ServiceNo int = NULL                                         
  , @StockNo int = NULL                                         
  , @ROHS tinyint = NULL                                        
  , @Notes nvarchar(2000) = NULL                                          
  , @OriginalOfferCurrencyNo  INT = NULL                                         
  , @OriginalOfferDate DATETIME = NULL                                         
  , @OriginalOfferPrice FLOAT = NULL                                         
  , @OriginalOfferSupplierNo INT = NULL                                         
  , @SourcingResultNo INT = NULL                                         
  , @UpdatedBy int = NULL                                     
  --[001] code start                                     
  , @ProductSource tinyint = NULL                                         
  --[001] code end                               
  , @PrintHazardous bit = 0                               
  , @MSLLevel   nvarchar(100) = NULL                                     
  , @RequirementNo int = null       
  , @AS6081 bit = 0 --[004]       
  , @QuoteLineId int OUTPUT                                         
                                         
AS                                          
                                         
    BEGIN                                         
                                   
   DECLARE @PurchaseRequestLineNo INT                                   
   SET @PurchaseRequestLineNo = NULL                                   
   SELECT @PurchaseRequestLineNo = SourcingTableItemNo FROM tbSourcingResult WHERE SourcingResultId = isnull(@SourcingResultNo,0) AND SourcingTable = 'PQ'                                   
          
                                 
                      
  DECLARE @ClientCompanyNo INT                                 
  DECLARE @SourceTable nvarchar(20)                            
  DECLARE @ClientCurrencyNo INT                                   
               
    SELECT @SourceTable=SourcingTable,@OriginalOfferCurrencyNo = CurrencyNo,@ClientCompanyNo = ClientCompanyNo  ,@ClientCurrencyNo = ClientCurrencyNo                                
  FROM tbSourcingResult WHERE SourcingResultId = @SourcingResultNo                                 
                                 
  IF @SourceTable = 'PQ' OR @SourceTable = 'OFPH' OR @SourceTable = 'EXPH'                                 
  BEGIN                                 
     set @OriginalOfferCurrencyNo = @ClientCurrencyNo                                 
  END                                 
                                   
  IF NOT EXISTS( SELECT 'X' FROM tbQuoteLine WHERE QuoteNo = @QuoteNo AND PurchaseQuoteLineNo = ISNULL(@PurchaseRequestLineNo,0))                                   
    BEGIN                                 
--[003] code start                            
 declare                              
--@CountryOfOrigin nvarchar(50)=null,                             
@CountryOfOriginNo int=null,                             
@LifeCycleStage nvarchar(50)=null,                             
@HTSCode varchar(20)=null,                             
@AveragePrice  float=null,                             
@Packing varchar(60)=null,                             
@PackagingSize varchar(100)=null,                          
@Descriptions nvarchar(max)=null,                   
@IHSProduct varchar(100)=null,                 
@ECCNCode varchar(100)=null                 
                              
 if(@RequirementNo is not null)                             
 begin                             
                             
    select                                
   -- @CountryOfOrigin =  CountryOfOrigin,                             
   @CountryOfOriginNo = CountryOfOriginNo,                             
   @LifeCycleStage = LifeCycleStage,                             
   @HTSCode = HTSCode,                             
   @AveragePrice  = AveragePrice,                             
   @Packing = Packing,                             
   @PackagingSize = PackagingSize ,                         
   @Descriptions=Descriptions,                    
   @IHSProduct=IHSProduct ,                      
   @ECCNCode  = ECCNCode          
   , @AS6081 = ISNULL(AS6081,0) --[004]         
   from tbCustomerRequirement where CustomerRequirementId = @RequirementNo                             
                                
 end                             
 --[003] code end                             
                                
                                   
   INSERT  INTO dbo.tbQuoteLine (                                         
       QuoteNo                                         
     , FullPart                                     
     , Part                                         
     , ManufacturerNo                                         
     , DateCode                                         
     , PackageNo                                         
     , Quantity                                         
     , Price                                         
     , ETA                                       
     , Instructions                                         
     , ProductNo                                         
     , ReasonNo                                         
     , CustomerPart                                         
     , ServiceNo                                         
     , StockNo                                         
     , ROHS            
     , Notes                   
     , UpdatedBy                                          
     , FullCustomerPart                                         
     , OriginalOfferCurrencyNo                                  
     , OriginalOfferDate                                         
     , OriginalOfferPrice                                         
     , OriginalOfferSupplierNo                                         
     , SourcingResultNo                                       
      --[001] code start                                     
     , ProductSource                                   
      --[001] code end                                     
     , PurchaseQuoteLineNo                             
  , PrintHazardous                              
  ,MSLLevel                            
  --[003] code start                           
 --, CountryOfOrigin                             
,CountryOfOriginNo                             
,LifeCycleStage                             
,HTSCode                             
,AveragePrice                             
,Packing                             
,PackagingSize                          
,Descriptions                    
,IHSProduct               
,ECCNCode                            
--[003] code end          
, AS6081 --[004]         
      )                                         
   VALUES  (                                         
      @QuoteNo                                         
, dbo.ufn_get_fullpart(@Part)                                         
     , @Part                                  
   , @ManufacturerNo                                         
       , @DateCode                                 
       , @PackageNo                                         
       , @Quantity                                         
       , @Price                                         
       , @ETA                                         
       , @Instructions                                         
       , @ProductNo                                         
      , @ReasonNo                                         
       , @CustomerPart                                         
       , @ServiceNo                                         
       , @StockNo                                         
       , @ROHS                                         
       , @Notes                                         
      , @UpdatedBy                                         
  , dbo.ufn_get_fullpart(@CustomerPart)                                         
       , @OriginalOfferCurrencyNo                                          
       , @OriginalOfferDate                                         
       , @OriginalOfferPrice                                         
       , @OriginalOfferSupplierNo                                         
       , @SourcingResultNo                  
     --[001] code start                                     
       , @ProductSource                                        
     --[001] code start                                     
       , @PurchaseRequestLineNo                              
 , @PrintHazardous                             
    , @MSLLevel                         
 --[003] code start                            
   -- , @CountryOfOrigin                             
 , @CountryOfOriginNo                             
 , @LifeCycleStage                             
 , @HTSCode                             
, @AveragePrice                             
 , @Packing                             
 , @PackagingSize                          
 ,@Descriptions                    
 ,@IHSProduct                    
 ,@ECCNCode                          
 --[003] code end          
 , ISNULL(@AS6081,0) --[004]         
      )                                         
                                        
--[004]  start                             
  SET @QuoteLineId = scope_identity()                                   
  IF (SELECT COUNT(1) from tbQuoteLine where QuoteNo = @QuoteNo and ISNULL(AS6081,0) = 1) > 0     
  BEGIN     
 UPDATE tbQuote set AS6081 = 1 where QuoteId = @QuoteNo     
  END     
--[004]  end     
     
     
  IF(@SourcingResultNo>0)                                 
BEGIN                                 
    IF @SourceTable = 'PQ' OR @SourceTable = 'OFPH' OR @SourceTable = 'EXPH'                                   
    BEGIN                                
  UPDATE tbSourcingResult SET Closed=1,SourceRef='Q' WHERE SourcingResultId=@SourcingResultNo                               
 END                               
  END                                 
                            
 END                                   
 ELSE                                   
 BEGIN                                   
                                        
   SET @QuoteLineId = 1         
 END                                   
                                   
END 
GO


