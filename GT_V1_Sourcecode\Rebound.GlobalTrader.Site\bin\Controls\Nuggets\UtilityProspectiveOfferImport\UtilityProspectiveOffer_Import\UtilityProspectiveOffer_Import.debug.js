﻿///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------

Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.UtilityProspectiveOffer_Import = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.UtilityProspectiveOffer_Import.initializeBase(this, [element]);
};

Rebound.GlobalTrader.Site.Controls.Forms.UtilityProspectiveOffer_Import.prototype = {

    initialize: function () {
        Rebound.GlobalTrader.Site.Controls.Forms.UtilityProspectiveOffer_Import.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
        this.addCancel(Function.createDelegate(this, this.cancelClicked));

    },

    formShown: function () {
        if (this._blnFirstTimeShown) {
            this.addCancel(Function.createDelegate(this, this.cancelClicked));
        }
        this.getFieldControl("ctlSupplier")._intGlobalLoginClientNo = 114;

    },

    dispose: function () {
        if (this.isDisposed) return;
        Rebound.GlobalTrader.Site.Controls.Forms.UtilityProspectiveOffer_Import.callBaseMethod(this, "dispose");
    },

    cancelClicked: function () {
        window.location.href = 'Ord_ProspectiveCSBrowse.aspx';
    },

    impotExcelData: function (originalFilename, generatedFilename, iscolumnheaderchk) {
        $('#divLoader').show();

        var obj = new Rebound.GlobalTrader.Site.Data();
        obj._intTimeoutMilliseconds = 600000;
        obj.set_PathToData("controls/Nuggets/UtilityProspectiveOfferImport");
        obj.set_DataObject("UtilityProspectiveOfferImport");
        obj.set_DataAction("ImportData");

        obj.addParameter("originalFilename", originalFilename);
        obj.addParameter("generatedFilename", generatedFilename);
        obj.addParameter("ColumnHeader", iscolumnheaderchk);

        obj.addDataOK(Function.createDelegate(this, this.importExcelDataOK));
        obj.addError(Function.createDelegate(this, this.importExcelDataError));
        obj.addTimeout(Function.createDelegate(this, this.importExcelDataError));

        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;

        return true;
    },

    importExcelDataOK: function (args) {
        flogId = args._result.FileLogId;
        $('#divLoader').hide();
        $("#btnDisplayCsvData").prop('disabled', false).css('opacity', 5.5);
        $("#excelipload").prop('disabled', true).css('opacity', 0.5);
        $('input:checkbox[id="chkFileCCH"]').prop('disabled', true);
        $('input:file').filter(function () {
            return this.files.length == 0
        }).prop('disabled', true);
        var IsLimitExceeded = args._result.IsLimitExceeded;
        if (IsLimitExceeded) {
            alert(args._result.LimitErrorMessage);
        }
    },

    importExcelDataError: function (args) {
        alert(args._errorMessage.split('<br/>')[0]);
        $('#divLoader').hide();
    }
};

Rebound.GlobalTrader.Site.Controls.Forms.UtilityProspectiveOffer_Import.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.UtilityProspectiveOffer_Import", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
