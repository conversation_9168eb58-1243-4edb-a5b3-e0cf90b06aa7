<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="AllPurchaseOrdersDueIn" xml:space="preserve">
    <value>Alle Kaufaufträge Passend Innen</value>
  </data>
  <data name="AllPurchaseOrdersDueInWithin7Days" xml:space="preserve">
    <value>Alle Kaufaufträge Passend Innen (innerhalb 7 Tage)</value>
  </data>
  <data name="AllSalesOrdersReadyToShipWithin7Days" xml:space="preserve">
    <value>Alle Verkaufsaufträge Bereit Zu Versenden (innerhalb 7 Tage)</value>
  </data>
  <data name="AllStatistics" xml:space="preserve">
    <value>Statistiken (ab letztem day' s-Handel)</value>
  </data>
  <data name="ApprovedCustomersOnStop" xml:space="preserve">
    <value>Anerkannte Kunden auf Anschlag</value>
  </data>
  <data name="MyApprovedCustomersOnStop" xml:space="preserve">
    <value>Meine Anerkannten Kunden auf Anschlag</value>
  </data>
  <data name="MyApprovedCustomersOnStop_ForUser" xml:space="preserve">
    <value>{0}'s Anerkannten Kunden auf Anschlag</value>
  </data>
  <data name="MyMessages" xml:space="preserve">
    <value>Meine Mitteilungen</value>
  </data>
  <data name="MyOpenPurchaseOrders" xml:space="preserve">
    <value>Meine geöffneten Kaufaufträge</value>
  </data>
  <data name="MyOpenPurchaseOrders_ForUser" xml:space="preserve">
    <value>{0}'s geöffneten Kaufaufträge</value>
  </data>
  <data name="MyOpenQuotes" xml:space="preserve">
    <value>Meine offenen Notierungen</value>
  </data>
  <data name="MyOpenQuotes_ForUser" xml:space="preserve">
    <value>{0}'s offenen Notierungen</value>
  </data>
  <data name="MyOpenRequirements" xml:space="preserve">
    <value>Meine offenen Voraussetzungen</value>
  </data>
  <data name="MyOpenRequirements_ForUser" xml:space="preserve">
    <value>{0}'s offenen Voraussetzungen</value>
  </data>
  <data name="MyOpenSalesOrders" xml:space="preserve">
    <value>Meine geöffneten Verkaufs-Aufträge</value>
  </data>
  <data name="MyOpenSalesOrders_ForUser" xml:space="preserve">
    <value>{0}'s geöffneten Verkaufs-Aufträge</value>
  </data>
  <data name="MyRecentActivity" xml:space="preserve">
    <value>Meine neue Tätigkeit</value>
  </data>
  <data name="MyRecentActivity_ForUser" xml:space="preserve">
    <value>{0}'s neue Tätigkeit</value>
  </data>
  <data name="MyRecentlyReceivedOrders" xml:space="preserve">
    <value>Meine vor kurzem empfangenen Aufträge</value>
  </data>
  <data name="MyRecentlyReceivedOrders_ForUser" xml:space="preserve">
    <value>{0}'s vorkurzem empfangenen Aufträge</value>
  </data>
  <data name="MyRecentlyShippedOrders" xml:space="preserve">
    <value>Meine vor kurzem versendeten Aufträge</value>
  </data>
  <data name="MyRecentlyShippedOrders_ForUser" xml:space="preserve">
    <value>{0}'s vor kurzem versendeten Aufträge</value>
  </data>
  <data name="MyScheduledTasks" xml:space="preserve">
    <value>Meine zeitlich geplanten Aufgaben</value>
  </data>
  <data name="MyStatistics" xml:space="preserve">
    <value>Meine Statistiken (ab letztem day' s-Handel)</value>
  </data>
  <data name="MyStatistics_ForUser" xml:space="preserve">
    <value>{0}'s Statistiken (ab letztem day' s-Handel)</value>
  </data>
  <data name="MyToDoList" xml:space="preserve">
    <value>Mein Liste tun</value>
  </data>
  <data name="PartsBeingOrderedToday" xml:space="preserve">
    <value>Teile, die heute bestellt werden</value>
  </data>
  <data name="ReceivedOrders" xml:space="preserve">
    <value>Empfangene Aufträge</value>
  </data>
  <data name="SalesOrdersReadyToShip" xml:space="preserve">
    <value>Verkaufs-Aufträge bereit zu versenden</value>
  </data>
  <data name="ShippedOrders" xml:space="preserve">
    <value>Versendete Aufträge</value>
  </data>
  <data name="StockItemsBelowTheMinimumQuantity" xml:space="preserve">
    <value>Auf lagereinzelteile unterhalb der minimalen Quantität</value>
  </data>
  <data name="TableActivity" xml:space="preserve">
    <value>Neue Tätigkeit</value>
  </data>
  <data name="TodaysReceivedOrders" xml:space="preserve">
    <value>Heutige Erhaltene Ordnungen</value>
  </data>
  <data name="TodaysShippedOrders" xml:space="preserve">
    <value>Heutige Verladene Ordnungen</value>
  </data>
  <data name="TopSalespersons" xml:space="preserve">
    <value>Spitzenverkäufer dieser Monat</value>
  </data>
</root>