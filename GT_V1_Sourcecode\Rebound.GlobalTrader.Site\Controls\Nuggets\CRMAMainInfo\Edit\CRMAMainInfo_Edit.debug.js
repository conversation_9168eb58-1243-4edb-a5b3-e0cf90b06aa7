///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit = function(element) { 
	Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.initializeBase(this, [element]);
	this._intCRMAID = -1;
	this._hidRaisedByNo=0;
	this._IsHubAutoCRMA=false;
	this._hidShipViaNo=0;
	this._ShipVia = -1;
	this._RaisedBy = -1;
	this._clientWarehouseNo=-1;
};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.prototype = {

	get_intCRMAID: function() { return this._intCRMAID; }, 	set_intCRMAID: function(value) { if (this._intCRMAID !== value)  this._intCRMAID = value; }, 

	initialize: function() {
		Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.callBaseMethod(this, "initialize");
		this.addShown(Function.createDelegate(this, this.formShown));
	},

	formShown: function() {
		if (this._blnFirstTimeShown) {
			this.addSave(Function.createDelegate(this, this.saveClicked));
		}
		this.getFieldDropDownData("ctlDivision");
		this.getFieldDropDownData("ctlWarehouse");
		this.getFieldDropDownData("ctlShipVia");
		this.getFieldDropDownData("ctlAuthorisedBy");
		this.getFieldDropDownData("ctlIncoterm");
},

	dispose: function() { 
		if (this.isDisposed) return;
		this._intCRMAID = null;
		Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.callBaseMethod(this, "dispose");
	},

	saveClicked: function() {
		if (!this.validateForm()) return;
		var obj = new Rebound.GlobalTrader.Site.Data();
		
//		this._ShipVia=this.getFieldValue("ctlShipVia");
//		if(this._ShipVia<=0){
//		this._ShipVia=this._hidShipViaNo;
//		}
//		this._RaisedBy=this.getFieldValue("ctlAuthorisedBy");
//		if(this._RaisedBy<=0){
//		this._RaisedBy=this._hidRaisedByNo;
//		}
		
		obj.set_PathToData("controls/Nuggets/CRMAMainInfo");
		obj.set_DataObject("CRMAMainInfo");
		obj.set_DataAction("SaveEdit");
		obj.addParameter("id", this._intCRMAID);		
		obj.addParameter("DivisionNo", this.getFieldValue("ctlDivision"));
		
		if(this._clientWarehouseNo<=0)
		obj.addParameter("WarehouseNo", this.getFieldValue("ctlWarehouse"));
		else
		obj.addParameter("WarehouseNo", this._clientWarehouseNo);
		
		if(this._hidRaisedByNo<=0)
		obj.addParameter("AuthorisedBy", this.getFieldValue("ctlAuthorisedBy"));
		else
		obj.addParameter("AuthorisedBy", this._hidRaisedByNo);
		
		obj.addParameter("RMADate", this.getFieldValue("ctlRMADate"));
		
		if (this._hidShipViaNo <= 0)
		obj.addParameter("ShipVia", this.getFieldValue("ctlShipVia"));
		else
		obj.addParameter("ShipVia", this._hidShipViaNo);
		
		obj.addParameter("ShippingAccount", this.getFieldValue("ctlShippingAccount"));
		obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
		obj.addParameter("Instructions", this.getFieldValue("ctlInstructions"));
		obj.addParameter("Incoterm", this.getFieldValue("ctlIncoterm"));
		obj.addParameter("CustomerRejectionNo", this.getFieldValue("ctlCustomerRejectionNo"));
		obj.addDataOK(Function.createDelegate(this, this.saveEditComplete));
		obj.addError(Function.createDelegate(this, this.saveEditError));
		obj.addTimeout(Function.createDelegate(this, this.saveEditError));
		$R_DQ.addToQueue(obj);
		$R_DQ.processQueue();
		obj = null;
	},

	saveEditError: function(args) {
		this._strErrorMessage = args._errorMessage;
		this.onSaveError();
	},
	
	saveEditComplete: function(args) {
		if (args._result.Result == true) {
			this.onSaveComplete();
		} else {
			this._strErrorMessage = args._errorMessage;
			this.onSaveError();
		}
	},	

	validateForm: function() {
//		this.onValidate();
//		var blnOK = this.autoValidateFields();
//		
//		return blnOK;

         this.onValidate();
         var blnOK =true;// this.autoValidateFields();
        //alert("blnOK"+blnOK);
        if (!this.checkFieldEntered("ctlDivision")) blnOK = false;
        if (!this.checkFieldEntered("ctlRMADate")) blnOK = false;
        if(this._clientWarehouseNo<=0)
        {
        if (!this.checkFieldEntered("ctlWarehouse")) blnOK = false;
        }
        if (this._hidShipViaNo <= 0) {
            if (!this.checkFieldEntered("ctlShipVia")) blnOK = false;
        }
        if (this._hidRaisedByNo <= 0) {
            if (!this.checkFieldEntered("ctlAuthorisedBy")) blnOK = false;
        }
        if (!this.checkFieldEntered("ctlIncoterm")) blnOK = false;
        if (!blnOK) this.showError(true);
        return blnOK;
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.CRMAMainInfo_Edit", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
