﻿/*   
===========================================================================================  
TASK         UPDATED BY   DATE          ACTION    DESCRIPTION  
[US-222480]  Cuong Do   23-Dec-2024		Update    Add more COLUMNS
===========================================================================================  
*/ 
CREATE OR ALTER Procedure usp_AS6081_GetRequirementDetailsForRequester  
@CustReqList varchar(MAX)=''     
AS  
--********************************************************************************************  
--* Action: Created  By: Abhinav Saxena  Date: 19-09-2023  Comment:For RP-2260 (AS6081).             
--********************************************************************************************  
BEGIN  
	SELECT 
		cr.CustomerRequirementId,
		cr.CustomerRequirementNumber,
		bm.BOMId,bm.BOMName,  
		cr.FullPart,
		mfr.ManufacturerCode,
		cr.Quantity,
		cr.TargetSellPrice,
		bm.QuoteRequired
		,com.CompanyName
		,cur.CurrencyCode
	FROM tbCustomerRequirement cr   
	LEFT JOIN tbBOM bm ON cr.BOMNo=bm.BOMId   
	LEFT JOIN tbManufacturer mfr ON cr.ManufacturerNo=mfr.ManufacturerId  
	LEFT JOIN tbCompany com ON com.CompanyId = bm.CompanyNo
	LEFT JOIN tbCurrency cur ON cur.CurrencyId = cr.CurrencyNo
	WHERE cr.CustomerRequirementId  IN(SELECT * from [dbo].[fn_CsvToInt](@CustReqList))  
END  