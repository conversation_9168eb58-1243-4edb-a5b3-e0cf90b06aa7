//-------------------------------------------------------------------------------------------
// RP 12.01.2010:
// - convert values for YTD / Last year as they are returned in base currency
//
// RP 17.11.2009:
// - use specific query for sales info to cut down data going through pipes
// - get the YTD / last year values in one hit
//Marker     Changed by      Date         Remarks
//[001]      Vinay           11/06/2012   This need to Add Incoterms field in company section
//-------------------------------------------------------------------------------------------
using System;
using System.Data;
using System.Web;
using System.Collections;
using System.Web.Services;
using System.Web.Services.Protocols;
using Rebound.GlobalTrader.Site;
using System.Collections.Generic;
using Rebound.GlobalTrader.BLL;

namespace Rebound.GlobalTrader.Site.Controls.Nuggets.Data {
	[WebService(Namespace = "http://tempuri.org/")]
	[WebServiceBinding(ConformsTo = WsiProfiles.BasicProfile1_1)]
    public class ExpediteHistory : Rebound.GlobalTrader.Site.Data.Base
    {

		public override void ProcessRequest(HttpContext context) {
			if (base.init(context)) {
				switch (Action) {
                    case "GetExpediteHistory": GetExpediteHistory(); break;
					default: WriteErrorActionNotFound(); break;
				}
			}
		}

        private void GetExpediteHistory()
        {
            JsonObject jsn = new JsonObject();
            JsonObject jsnItems = new JsonObject(true);
            List<Audit> lst = Audit.GetListExpediteForHUBRFQ(ID, SessionManager.ClientID ?? 0);
            for (int i = 0; i < lst.Count; i++)
            {
                
                JsonObject jsnItem = new JsonObject();
                jsnItem.AddVariable("ID", lst[i].AuditId);
                jsnItem.AddVariable("Note", Functions.ReplaceLineBreaks(lst[i].Note));
                jsnItem.AddVariable("Date", Functions.FormatDate(lst[i].DLUP,false,true));
                jsnItem.AddVariable("Time", Functions.FormatTime(lst[i].DLUP));
                jsnItem.AddVariable("EmployeeName", lst[i].EmployeeName);
                jsnItem.AddVariable("ReqNos", lst[i].ReqNos);
                jsnItem.AddVariable("To", lst[i].To);
                jsnItem.AddVariable("CCUserID", lst[i].CCUserID);
                jsnItem.AddVariable("SendToGroup", lst[i].SendToGroup);
                jsnItems.AddVariable(jsnItem);
                jsnItem.Dispose(); jsnItem = null;
            }
            lst = null;
            jsn.AddVariable("ExpHist", jsnItems);
            OutputResult(jsn);
            jsn.Dispose(); jsn = null;
        }
	
	}
}
