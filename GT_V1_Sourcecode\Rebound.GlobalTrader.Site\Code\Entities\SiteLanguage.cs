using System;
using System.Collections.Generic;
using System.Text;
using Rebound.GlobalTrader.Site.Enumerations;

namespace Rebound.GlobalTrader.Site {
	public struct SiteLanguage {

		public int ID;
		public string Name;
		public string Code;

		public SiteLanguage(int intID, string strName, string strCode) {
			ID = intID;
			Name = strName;
			Code = strCode;
		}

		public SiteLanguage(GlobalLanguage.List enmLanguage) {
			ID = Convert.ToInt32(enmLanguage);
			Name = enmLanguage.ToString();
			Code = GlobalLanguage.GetLanguageCode(enmLanguage);
		}

		public SiteLanguage(Rebound.GlobalTrader.BuildSettingsCode.Enumerations.SiteLanguage enmLanguage) {
			ID = Convert.ToInt32(enmLanguage);
			Name = enmLanguage.ToString();
			Code = GlobalLanguage.GetLanguageCode(ID);
		}
	}

}
