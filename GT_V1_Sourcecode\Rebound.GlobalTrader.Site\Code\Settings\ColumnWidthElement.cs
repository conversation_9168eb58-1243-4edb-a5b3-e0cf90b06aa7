using System;
using System.Data;
using System.Configuration;
using System.Web.Configuration;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;

namespace Rebound.GlobalTrader.Site.Settings {
	public class ColumnWidthElement : ConfigurationElement {

		[ConfigurationProperty("width", IsRequired = true)]
		public string Width {
			get { return this["width"].ToString(); }
		}

		[ConfigurationProperty("name", IsRequired = true)]
		public string Name {
			get { return this["name"] as string; }
		}

	}
}
