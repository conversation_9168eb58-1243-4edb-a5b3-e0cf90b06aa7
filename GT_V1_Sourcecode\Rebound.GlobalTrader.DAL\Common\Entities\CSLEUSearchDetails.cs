﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Rebound.GlobalTrader.DAL
{
    public class CSLEUSearchDetails
    {

        public CSLEUSearchDetails() { }

        #region Properties

        public int CompanyId { get; set; }
        public string Type { get; set; }

        public string CompanyName { get; set; }
        public string Remark { get; set; }
        public string Nationalities { get; set; }
        public string BirthDate { get; set; }
        public string BirthDatePlace { get; set; }
        public string Address { get; set; }
        public string Program { get; set; }
        public string IDs { get; set; }

        #endregion

    }
}
