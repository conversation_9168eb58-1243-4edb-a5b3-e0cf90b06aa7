Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Nuggets");Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers=function(n){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.initializeBase(this,[n]);this._inactive=!1;this._blnNoData=!1;this._intSupplierID=-1;this._intContactGroupID=0};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.prototype={get_intSupplierID:function(){return this._intSupplierID},set_intSupplierID:function(n){this._intSupplierID!==n&&(this._intSupplierID=n)},get_tbl:function(){return this._tbl},set_tbl:function(n){this._tbl!==n&&(this._tbl=n)},get_ibtnEdit:function(){return this._ibtnEdit},set_ibtnEdit:function(n){this._ibtnEdit!==n&&(this._ibtnEdit=n)},get_ibtnAdd:function(){return this._ibtnAdd},set_ibtnAdd:function(n){this._ibtnAdd!==n&&(this._ibtnAdd=n)},get_ibtnDelete:function(){return this._ibtnDelete},set_ibtnDelete:function(n){this._ibtnDelete!==n&&(this._ibtnDelete=n)},get_ibtnView:function(){return this._ibtnView},set_ibtnView:function(n){this._ibtnView!==n&&(this._ibtnView=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.callBaseMethod(this,"initialize");this._strPathToData="controls/Nuggets/CompanyManufacturers";this._strDataObject="CompanyManufacturers";this.latestStarRatingConfig=null;this.getLatestStarRatingConfig();this.addRefreshEvent(Function.createDelegate(this,this.refreshData));this._tbl.addSortDataEvent(Function.createDelegate(this,this.getData));(this._ibtnEdit||this._ibtnAdd)&&(this._ibtnEdit&&$R_IBTN.addClick(this._ibtnEdit,Function.createDelegate(this,this.showEditForm)),this._ibtnAdd&&$R_IBTN.addClick(this._ibtnAdd,Function.createDelegate(this,this.showAddForm)),this._frmAddEdit=$find(this._aryFormIDs[0]),this._frmAddEdit._intSupplierID=this._intSupplierID,this._frmAddEdit.addShown(Function.createDelegate(this,this.addEditFormShown)),this._frmAddEdit.addCancel(Function.createDelegate(this,this.hideAddEditForm)),this._frmAddEdit.addSaveComplete(Function.createDelegate(this,this.saveAddEditOK)),this._frmAddEdit.addSaveError(Function.createDelegate(this,this.saveAddEditError)));this._ibtnView&&(this._frmView=$find(this._aryFormIDs[2]),$R_IBTN.addClick(this._ibtnView,Function.createDelegate(this,this.showViewForm)),this._frmView.addCancel(Function.createDelegate(this,this.hideViewForm)),this._frmView.addSaveComplete(Function.createDelegate(this,this.saveFranchiseOK)),this._frmView.addSaveError(Function.createDelegate(this,this.saveFranchiseError)));this._ibtnDelete&&($R_IBTN.addClick(this._ibtnDelete,Function.createDelegate(this,this.showDeleteForm)),this._frmDelete=$find(this._aryFormIDs[1]),this._frmDelete.addShown(Function.createDelegate(this,this.deleteFormShown)),this._frmDelete.addNotConfirmed(Function.createDelegate(this,this.hideDeleteForm)),this._frmDelete.addSaveComplete(Function.createDelegate(this,this.saveDeleteOK)),this._frmDelete.addSaveError(Function.createDelegate(this,this.saveDeleteError)));var n=$find("ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ddlMfrGroupBySupplier");n&&(n._intCompanyID=this._intSupplierID,n.addChanged(Function.createDelegate(this,this.groupCodeChanged)));this._tbl.addSelectedIndexChanged(Function.createDelegate(this,this.tbl_SelectedIndexChanged));this.getCompanyInactive();this.getFieldDropDownGroupCodeData()},dispose:function(){this.isDisposed||(this._ibtnAdd&&$R_IBTN.clearHandlers(this._ibtnAdd),this._ibtnEdit&&$R_IBTN.clearHandlers(this._ibtnEdit),this._ibtnView&&$R_IBTN.clearHandlers(this._ibtnView),this._ibtnDelete&&$R_IBTN.clearHandlers(this._ibtnDelete),this._frmDelete&&this._frmDelete.dispose(),this._frmAddEdit&&this._frmAddEdit.dispose(),this._frmView&&this._frmView.dispose(),this._tbl&&this._tbl.dispose(),this._frmDelete=null,this._frmAddEdit=null,this._frmView=null,this._tbl=null,this._ibtnAdd=null,this._ibtnEdit=null,this._ibtnDelete=null,this._ibtnView=null,this._intSupplierID=null,this._intContactGroupID=null,this._blnNoData=null,this._inactive=null,Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.callBaseMethod(this,"dispose"))},getData:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.callBaseMethod(this,"getData_Start");this.updateButtonsEnabledState(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetManufacturers");n.addParameter("id",this._intSupplierID);n.addParameter("ContactGroupID",this._intContactGroupID);n.addParameter("SortDir",this._tbl._enmSortDirection);n.addDataOK(Function.createDelegate(this,this.getDataComplete));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getDataError:function(n){this.showError(!0,n.get_ErrorMessage())},getDataComplete:function(n){var i=n._result,r,t,f,e,o,u,s;if(this._tbl.clearTable(),this._blnNoData=!0,i.Items){for(r=0;r<i.Items.length;r++){t=i.Items[r];f=t.IsOldData?"N/A":t.POLineCount;let n=t.IsFranchised==!0?"Yes":"No";e=[$RGT_nubButton_Manufacturer(t.MfrID,t.MfrName,t.MfrAdvisoryNotes),n,$R_FN.setCleanTextValue(f),$R_FN.createStarRating(t.Rating)];o={MfrName:t.MfrName,POLineCount:f,Rating:t.Rating,IsFranchised:t.IsFranchised};this._tbl.addRow(e,t.ID,!1,o);t=null;e=null}this._blnNoData=i.Items.length==0;let n=i.Items.some(n=>n.IsFranchised==!1);n&&this._intContactGroupID>0?$("#ibtnMarkFranchised").prop("disabled",!1):$("#ibtnMarkFranchised").prop("disabled",!0)}u=this._tbl._tblHeader.getElementsByClassName("last")[0];u.innerText=="Rating"&&(s=this.latestStarRatingConfig!=null?this.latestStarRatingConfig:"N/A",u.innerHTML='Rating<br><span class="faded-text">(1 Star per '+s+" PO Lines)<\/span>",u.getElementsByClassName("faded-text")[0].style.opacity=.5);this._tbl.resizeColumns();this.getDataOK_End();this.showNoData(this._blnNoData)},getCompanyInactive:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("GetCompanyDetailInactive");n.addParameter("id",this._intSupplierID);n.addDataOK(Function.createDelegate(this,this.getCompanyInactiveOK));n.addError(Function.createDelegate(this,this.getCompanyInactiveError));n.addTimeout(Function.createDelegate(this,this.getCompanyInactiveError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getCompanyInactiveOK:function(n){var t=n._result;this._inactive=t.Inactive;this._ibtnAdd&&$R_IBTN.enableButton(this._ibtnAdd,!this._inactive);this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,!this._inactive);this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,!this._inactive);this._ibtnView&&$R_IBTN.enableButton(this._ibtnView,!this._inactive)},getCompanyInactiveError:function(n){this.showError(!0,n.get_ErrorMessage())},getFieldDropDownGroupCodeData:function(){var n=$find("ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ddlMfrGroupBySupplier");n&&n.getData()},groupCodeChanged:function(){this._intContactGroupID=$find("ctl00_cphMain_ctlManufacturersSupplied_ctlDB_ctl13_ddlMfrGroupBySupplier").getValue();this.getData();this.getLatestStarRatingConfig()},markFranchised:function(){console.log("markFranchised");var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData(this._strPathToData);n.set_DataObject(this._strDataObject);n.set_DataAction("MarkFranchised");n.addParameter("SupplierId",this._intSupplierID);n.addParameter("GroupCodeId",this._intContactGroupID);n.addDataOK(Function.createDelegate(this,this.markFranchisedOK));n.addError(Function.createDelegate(this,this.markFranchisedError));n.addTimeout(Function.createDelegate(this,this.markFranchisedError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},markFranchisedOK:function(n){var t=n._result;t&&(this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully),this.getData(),this.getLatestStarRatingConfig())},markFranchisedError:function(n){this.showError(!0,n.get_ErrorMessage())},getLatestStarRatingConfig:function(){Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.callBaseMethod(this,"getData_Start");this.updateButtonsEnabledState(!1);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/ManufacturerSuppliers");n.set_DataObject("ManufacturerSuppliers");n.set_DataAction("GetStarRatingConfig");n.addDataOK(Function.createDelegate(this,this.getConfigComplete));n.addError(Function.createDelegate(this,this.getDataError));n.addTimeout(Function.createDelegate(this,this.getDataError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},getConfigComplete:function(n){var t=n._result;t.Items.length>0&&(this.latestStarRatingConfig=t.Items[0].NumOfPO);this.getDataOK_End()},refreshData:function(){this.getData();this.getLatestStarRatingConfig()},updateButtonsEnabledState:function(n){this._ibtnEdit&&$R_IBTN.enableButton(this._ibtnEdit,n);this._ibtnView&&$R_IBTN.enableButton(this._ibtnView,n);this._ibtnDelete&&$R_IBTN.enableButton(this._ibtnDelete,n)},tbl_SelectedIndexChanged:function(){this.updateButtonsEnabledState(!this._inactive);this._frmAddEdit._intManufacturerLinkID=this._tbl._varSelectedValue;this._frmView._intManufacturerLinkID=this._tbl._varSelectedValue;this._frmDelete._intManufacturerLinkID=this._tbl._varSelectedValue},showAddForm:function(){this._frmAddEdit.changeMode("ADD");this.showForm(this._frmAddEdit,!0)},showEditForm:function(){this._frmAddEdit.changeMode("EDIT");this.showForm(this._frmAddEdit,!0)},addEditFormShown:function(){this._frmAddEdit._mode=="ADD"?this._frmAddEdit._autManufacturers.reselect():this._frmAddEdit.setFieldValue("ctlManufacturerSelected",this._tbl.getSelectedExtraData().MfrName)},hideAddEditForm:function(){this.showForm(this._frmAddEdit,!1);this.showNoData(this._blnNoData)},saveAddEditOK:function(){this.hideAddEditForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData();this.getFieldDropDownGroupCodeData()},saveAddEditError:function(){this.showError(!0,this._frmAddEdit._strErrorMessage)},showDeleteForm:function(){this.showForm(this._frmDelete,!0)},hideDeleteForm:function(){this.showForm(this._frmDelete,!1);this.showNoData(this._blnNoData)},saveDeleteOK:function(){this.hideDeleteForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},saveDeleteError:function(){this.showError(!0,this._frmDelete._strErrorMessage)},deleteFormShown:function(){this._frmDelete.setFieldValue("ctlManufacturerSelected",this._tbl.getSelectedExtraData().MfrName)},showViewForm:function(){let n=this._tbl.getSelectedExtraData();var t=this.latestStarRatingConfig!=null?"(1 Star per "+this.latestStarRatingConfig.toString()+" PO Lines)":"(1 Star per N/A PO Lines)",i=n.POLineCount.toString().concat(" ",t);this._frmView.setFieldValue("ctlManufacturerSelected",$R_FN.setCleanTextValue(n.MfrName));this._frmView.setFieldValue("ctlPOLine",i);this._frmView.setFieldValue("ctlRating",n.Rating);this._frmView.setFieldValue("ctlFranchise",n.IsFranchised);this.showForm(this._frmView,!0)},hideViewForm:function(){this.showForm(this._frmView,!1);this.showNoData(this._blnNoData)},saveFranchiseOK:function(){this.hideViewForm();this.showSavedOK(!0,$R_RES.ChangesSavedSuccessfully);this.getData()},saveFranchiseError:function(){this.showError(!0,this._frmView._strErrorMessage)}};Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers.registerClass("Rebound.GlobalTrader.Site.Controls.Nuggets.CompanyManufacturers",Rebound.GlobalTrader.Site.Controls.Nuggets.Base);