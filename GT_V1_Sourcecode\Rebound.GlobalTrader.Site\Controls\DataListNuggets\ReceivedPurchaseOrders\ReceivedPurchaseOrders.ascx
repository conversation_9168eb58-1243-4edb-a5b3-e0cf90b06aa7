<%@ Control Language="C#" CodeBehind="ReceivedPurchaseOrders.ascx.cs" Inherits="Rebound.GlobalTrader.Site.Controls.DataListNuggets.ReceivedPurchaseOrders" %>
<%@ Import Namespace="Rebound.GlobalTrader.Site" %>
<ReboundUI_Nugget:DesignBase ID="ctlDB" runat="server" BoxType="List">
	<Filters>
		<ReboundUI_DataListNugget:Filter ID="ctlFilter" runat="server">
			<FieldsLeft>
				<ReboundUI_FilterDataItemRow:Numerical id="ctlPONo" runat="server" ResourceTitle="PurchaseOrderNo" FilterField="PONo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlPart" runat="server" ResourceTitle="PartNo" FilterField="Part" />
		        <ReboundUI_FilterDataItemRow:CheckBox id="ctlRecentOnly" runat="server" ResourceTitle="RecentOnly" FilterField="RecentOnly" DefaultValue="true" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlSupplierName" runat="server" ResourceTitle="SupplierName" FilterField="Supplier" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlContactName" runat="server" ResourceTitle="ContactName" FilterField="Contact" />
				<ReboundUI_FilterDataItemRow:DropDown id="ctlBuyer" runat="server" ResourceTitle="Buyer" DropDownType="Employee" DropDownAssembly="Rebound.GlobalTrader.Site" FilterField="Buyer" />
			</FieldsLeft>
			<FieldsRight>
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedFrom" runat="server" ResourceTitle="DeliveryDateFrom" FilterField="DateReceivedFrom" />
				<ReboundUI_FilterDataItemRow:DateSelect id="ctlDateReceivedTo" runat="server" ResourceTitle="DeliveryDateTo" FilterField="DateReceivedTo" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlAirWayBill" runat="server" ResourceTitle="AirWayBill" FilterField="AirWayBill" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlSupplierPart" runat="server" ResourceTitle="SupplierPartNo" FilterField="SupplierPart" />
				<ReboundUI_FilterDataItemRow:TextBox id="ctlReference" runat="server" ResourceTitle="Reference" FilterField="Reference" />
			</FieldsRight>
		</ReboundUI_DataListNugget:Filter>
	</Filters>
</ReboundUI_Nugget:DesignBase>
