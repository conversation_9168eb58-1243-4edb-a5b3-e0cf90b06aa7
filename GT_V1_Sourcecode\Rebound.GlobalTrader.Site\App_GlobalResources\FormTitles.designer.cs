//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option or rebuild the Visual Studio project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Web.Application.StronglyTypedResourceProxyBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class FormTitles {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal FormTitles() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Resources.FormTitles", global::System.Reflection.Assembly.Load("App_GlobalResources"));
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Application Settings.
        /// </summary>
        internal static string AppSettings_Edit {
            get {
                return ResourceManager.GetString("AppSettings_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Type Of Suppliers.
        /// </summary>
        internal static string AS6081_ADD {
            get {
                return ResourceManager.GetString("AS6081_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Type Of Suppliers.
        /// </summary>
        internal static string AS6081_Delete {
            get {
                return ResourceManager.GetString("AS6081_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Type Of Suppliers Details.
        /// </summary>
        internal static string AS6081_EDIT {
            get {
                return ResourceManager.GetString("AS6081_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Reason For Chosen Supplier.
        /// </summary>
        internal static string AS6081_RCS_ADD {
            get {
                return ResourceManager.GetString("AS6081_RCS_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Reason For Chosen Supplier.
        /// </summary>
        internal static string AS6081_RCS_Delete {
            get {
                return ResourceManager.GetString("AS6081_RCS_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Reason For Chosen Supplier Details.
        /// </summary>
        internal static string AS6081_RCS_EDIT {
            get {
                return ResourceManager.GetString("AS6081_RCS_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Risk Of Supplier.
        /// </summary>
        internal static string AS6081_ROS_ADD {
            get {
                return ResourceManager.GetString("AS6081_ROS_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Risk Of Supplier.
        /// </summary>
        internal static string AS6081_ROS_Delete {
            get {
                return ResourceManager.GetString("AS6081_ROS_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Risk Of Supplier Details.
        /// </summary>
        internal static string AS6081_ROS_EDIT {
            get {
                return ResourceManager.GetString("AS6081_ROS_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New HUBRFQ.
        /// </summary>
        internal static string BOMAdd_Add {
            get {
                return ResourceManager.GetString("BOMAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Sourcing Result.
        /// </summary>
        internal static string BOMCusReqSourcingResults_Delete {
            get {
                return ResourceManager.GetString("BOMCusReqSourcingResults_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Partwatch Result.
        /// </summary>
        internal static string BOMCusReqSourcingResults_DeletePartWatch {
            get {
                return ResourceManager.GetString("BOMCusReqSourcingResults_DeletePartWatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New HUBRFQ Item.
        /// </summary>
        internal static string BOMITEMS_ADD {
            get {
                return ResourceManager.GetString("BOMITEMS_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DELETE CUSTOMER REQUIREMENT.
        /// </summary>
        internal static string BOMItems_Delete {
            get {
                return ResourceManager.GetString("BOMItems_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release Customer Requirement.
        /// </summary>
        internal static string BOMItems_Save {
            get {
                return ResourceManager.GetString("BOMItems_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply Part Watch for HUBIPO.
        /// </summary>
        internal static string BOMItems_SaveApplyPartwatch {
            get {
                return ResourceManager.GetString("BOMItems_SaveApplyPartwatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Applyed Part Watch for HUBIPO.
        /// </summary>
        internal static string BOMItems_SaveRemoveApplyPartwatch {
            get {
                return ResourceManager.GetString("BOMItems_SaveRemoveApplyPartwatch", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Revoke HUBRFQ Item.
        /// </summary>
        internal static string BOMItems_UnRelease {
            get {
                return ResourceManager.GetString("BOMItems_UnRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete HUBRFQ.
        /// </summary>
        internal static string BOMMainInfo_Delete {
            get {
                return ResourceManager.GetString("BOMMainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Information.
        /// </summary>
        internal static string BOMMainInfo_Edit {
            get {
                return ResourceManager.GetString("BOMMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notification.
        /// </summary>
        internal static string BOMMainInfo_Notify {
            get {
                return ResourceManager.GetString("BOMMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager.
        /// </summary>
        internal static string BOMManager {
            get {
                return ResourceManager.GetString("BOMManager", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PPV/ BOM Qualification Delete.
        /// </summary>
        internal static string BOMPVV_DELETE {
            get {
                return ResourceManager.GetString("BOMPVV_DELETE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PPV/ BOM Qualification ADD/EDIT.
        /// </summary>
        internal static string BOMPVV_EDIT {
            get {
                return ResourceManager.GetString("BOMPVV_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Assign to me HUBRFQ Item.
        /// </summary>
        internal static string BOM_AssignToMe {
            get {
                return ResourceManager.GetString("BOM_AssignToMe", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Certificate Category.
        /// </summary>
        internal static string CertificateCategory_Add {
            get {
                return ResourceManager.GetString("CertificateCategory_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Certificate Category.
        /// </summary>
        internal static string CertificateCategory_Edit {
            get {
                return ResourceManager.GetString("CertificateCategory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Certificate.
        /// </summary>
        internal static string Certificate_Add {
            get {
                return ResourceManager.GetString("Certificate_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Certificate.
        /// </summary>
        internal static string Certificate_Edit {
            get {
                return ResourceManager.GetString("Certificate_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New BOM.
        /// </summary>
        internal static string ClientBOMAdd_Add {
            get {
                return ResourceManager.GetString("ClientBOMAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New BOM Item.
        /// </summary>
        internal static string ClientBOMITEMS_ADD {
            get {
                return ResourceManager.GetString("ClientBOMITEMS_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Client Invoice.
        /// </summary>
        internal static string ClientInvoiceAdd_Add {
            get {
                return ResourceManager.GetString("ClientInvoiceAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Client Invoice Header Image.
        /// </summary>
        internal static string ClientInvoiceHeaderImage_Add {
            get {
                return ResourceManager.GetString("ClientInvoiceHeaderImage_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Client Invoice Header Image.
        /// </summary>
        internal static string ClientInvoiceHeaderImage_Delete {
            get {
                return ResourceManager.GetString("ClientInvoiceHeaderImage_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Client Invoice Header Image.
        /// </summary>
        internal static string ClientInvoiceHeaderImage_Edit {
            get {
                return ResourceManager.GetString("ClientInvoiceHeaderImage_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Client Invoice Header.
        /// </summary>
        internal static string ClientInvoiceHeader_Add {
            get {
                return ResourceManager.GetString("ClientInvoiceHeader_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Client Invoice Header Details.
        /// </summary>
        internal static string ClientInvoiceHeader_Edit {
            get {
                return ResourceManager.GetString("ClientInvoiceHeader_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Client Invoice Line.
        /// </summary>
        internal static string ClientInvoiceLines_Add {
            get {
                return ResourceManager.GetString("ClientInvoiceLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Client Invoice Line.
        /// </summary>
        internal static string ClientInvoiceLines_Delete {
            get {
                return ResourceManager.GetString("ClientInvoiceLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Client Invoice Information.
        /// </summary>
        internal static string ClientInvoiceMainInfo_Edit {
            get {
                return ResourceManager.GetString("ClientInvoiceMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT CLIENT DETAILS.
        /// </summary>
        internal static string CLIENT_EDIT {
            get {
                return ResourceManager.GetString("CLIENT_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone requirement and send to HUB.
        /// </summary>
        internal static string CloneHUB {
            get {
                return ResourceManager.GetString("CloneHUB", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone requirement and add HUBRFQ.
        /// </summary>
        internal static string CloneHUBRFQ {
            get {
                return ResourceManager.GetString("CloneHUBRFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New CommunicationLog Type.
        /// </summary>
        internal static string CommunicationLogType_Add {
            get {
                return ResourceManager.GetString("CommunicationLogType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit CommunicationLog Type details.
        /// </summary>
        internal static string CommunicationLogType_Edit {
            get {
                return ResourceManager.GetString("CommunicationLogType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Contact Log Item.
        /// </summary>
        internal static string CommunicationLog_Add {
            get {
                return ResourceManager.GetString("CommunicationLog_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Contact Log Item.
        /// </summary>
        internal static string CommunicationLog_Edit {
            get {
                return ResourceManager.GetString("CommunicationLog_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Address.
        /// </summary>
        internal static string CompanyAddresses_Add {
            get {
                return ResourceManager.GetString("CompanyAddresses_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cease Address.
        /// </summary>
        internal static string CompanyAddresses_Cease {
            get {
                return ResourceManager.GetString("CompanyAddresses_Cease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default Billing Address.
        /// </summary>
        internal static string CompanyAddresses_DefaultBill {
            get {
                return ResourceManager.GetString("CompanyAddresses_DefaultBill", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default Shipping Address.
        /// </summary>
        internal static string CompanyAddresses_DefaultShip {
            get {
                return ResourceManager.GetString("CompanyAddresses_DefaultShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Address.
        /// </summary>
        internal static string CompanyAddresses_Edit {
            get {
                return ResourceManager.GetString("CompanyAddresses_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Company.
        /// </summary>
        internal static string CompanyAdd_Add {
            get {
                return ResourceManager.GetString("CompanyAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer API Add.
        /// </summary>
        internal static string CompanyApiCustomer_Add {
            get {
                return ResourceManager.GetString("CompanyApiCustomer_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer API Edit.
        /// </summary>
        internal static string CompanyApiCustomer_Edit {
            get {
                return ResourceManager.GetString("CompanyApiCustomer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Company Certificate.
        /// </summary>
        internal static string CompanyCertificate_Add {
            get {
                return ResourceManager.GetString("CompanyCertificate_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Company Certificate.
        /// </summary>
        internal static string CompanyCertificate_Edit {
            get {
                return ResourceManager.GetString("CompanyCertificate_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Contact Log Item.
        /// </summary>
        internal static string CompanyContactLog_Delete {
            get {
                return ResourceManager.GetString("CompanyContactLog_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link Accounts.
        /// </summary>
        internal static string CompanyFinanceInfo_Link {
            get {
                return ResourceManager.GetString("CompanyFinanceInfo_Link", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Global Sales Person Details.
        /// </summary>
        internal static string CompanyGlobalSalesPDetails_Add {
            get {
                return ResourceManager.GetString("CompanyGlobalSalesPDetails_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Global Sales Person.
        /// </summary>
        internal static string CompanyGlobalSalesPDetails_Delete {
            get {
                return ResourceManager.GetString("CompanyGlobalSalesPDetails_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Global Sales Person Details.
        /// </summary>
        internal static string CompanyGlobalSalesPDetails_Edit {
            get {
                return ResourceManager.GetString("CompanyGlobalSalesPDetails_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Company Insurance Certificate.
        /// </summary>
        internal static string COMPANYINSURANCECERTIFICATE_ADD {
            get {
                return ResourceManager.GetString("COMPANYINSURANCECERTIFICATE_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Company Insurance Certificate.
        /// </summary>
        internal static string COMPANYINSURANCECERTIFICATE_EDIT {
            get {
                return ResourceManager.GetString("COMPANYINSURANCECERTIFICATE_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Company Information.
        /// </summary>
        internal static string CompanyMainInfo_Edit {
            get {
                return ResourceManager.GetString("CompanyMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Manufacturer Supplied.
        /// </summary>
        internal static string CompanyManufacturers_Add {
            get {
                return ResourceManager.GetString("CompanyManufacturers_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Manufacturer Supplied.
        /// </summary>
        internal static string CompanyManufacturers_Delete {
            get {
                return ResourceManager.GetString("CompanyManufacturers_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Manufacturer Supplied.
        /// </summary>
        internal static string CompanyManufacturers_Edit {
            get {
                return ResourceManager.GetString("CompanyManufacturers_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Manufacturer Supplied.
        /// </summary>
        internal static string CompanyManufacturers_View {
            get {
                return ResourceManager.GetString("CompanyManufacturers_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .
        /// </summary>
        internal static string COMPANYPROSPECTS_EDIT {
            get {
                return ResourceManager.GetString("COMPANYPROSPECTS_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Purchasing Information.
        /// </summary>
        internal static string CompanyPurchasingInfo_Edit {
            get {
                return ResourceManager.GetString("CompanyPurchasingInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Sales Information.
        /// </summary>
        internal static string CompanySalesInfo_Edit {
            get {
                return ResourceManager.GetString("CompanySalesInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Company Type.
        /// </summary>
        internal static string CompanyType_Add {
            get {
                return ResourceManager.GetString("CompanyType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Company Type details.
        /// </summary>
        internal static string CompanyType_Edit {
            get {
                return ResourceManager.GetString("CompanyType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Extended Contact Information.
        /// </summary>
        internal static string ContactExtendedInfo_Edit {
            get {
                return ResourceManager.GetString("ContactExtendedInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Contact Information.
        /// </summary>
        internal static string ContactMainInfo_Edit {
            get {
                return ResourceManager.GetString("ContactMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Contact.
        /// </summary>
        internal static string ContactsForCompany_Add {
            get {
                return ResourceManager.GetString("ContactsForCompany_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Contact.
        /// </summary>
        internal static string ContactsForCompany_Delete {
            get {
                return ResourceManager.GetString("ContactsForCompany_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default for POs.
        /// </summary>
        internal static string ContactsForCompany_MakeDefaultPO {
            get {
                return ResourceManager.GetString("ContactsForCompany_MakeDefaultPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default Purchase Orders Ledger.
        /// </summary>
        internal static string ContactsForCompany_MakeDefaultPOLedger {
            get {
                return ResourceManager.GetString("ContactsForCompany_MakeDefaultPOLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default for SOs.
        /// </summary>
        internal static string ContactsForCompany_MakeDefaultSO {
            get {
                return ResourceManager.GetString("ContactsForCompany_MakeDefaultSO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Default Sales Orders Ledger.
        /// </summary>
        internal static string ContactsForCompany_MakeDefaultSOLedger {
            get {
                return ResourceManager.GetString("ContactsForCompany_MakeDefaultSOLedger", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Counting Method.
        /// </summary>
        internal static string CountingMethod_Add {
            get {
                return ResourceManager.GetString("CountingMethod_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Counting Method details.
        /// </summary>
        internal static string CountingMethod_Edit {
            get {
                return ResourceManager.GetString("CountingMethod_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Country.
        /// </summary>
        internal static string Country_Add {
            get {
                return ResourceManager.GetString("Country_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Header.
        /// </summary>
        internal static string COUNTRY_DELETEHEADER {
            get {
                return ResourceManager.GetString("COUNTRY_DELETEHEADER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Country details.
        /// </summary>
        internal static string Country_Edit {
            get {
                return ResourceManager.GetString("Country_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Header.
        /// </summary>
        internal static string Country_ManageHeader {
            get {
                return ResourceManager.GetString("Country_ManageHeader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Credit Note.
        /// </summary>
        internal static string CreditAdd_Add {
            get {
                return ResourceManager.GetString("CreditAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Credit Line.
        /// </summary>
        internal static string CreditLines_Add {
            get {
                return ResourceManager.GetString("CreditLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Credit Note For POHUB.
        /// </summary>
        internal static string CreditLines_Confirm {
            get {
                return ResourceManager.GetString("CreditLines_Confirm", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Credit Line.
        /// </summary>
        internal static string CreditLines_Delete {
            get {
                return ResourceManager.GetString("CreditLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Credit Line.
        /// </summary>
        internal static string CreditLines_Edit {
            get {
                return ResourceManager.GetString("CreditLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Information.
        /// </summary>
        internal static string CreditMainInfo_Edit {
            get {
                return ResourceManager.GetString("CreditMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export Credit Note Details.
        /// </summary>
        internal static string CreditMainInfo_Export {
            get {
                return ResourceManager.GetString("CreditMainInfo_Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release Exported Credit Note Details.
        /// </summary>
        internal static string CreditMainInfo_Release {
            get {
                return ResourceManager.GetString("CreditMainInfo_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Credit Bulk Email.
        /// </summary>
        internal static string Credit_Email {
            get {
                return ResourceManager.GetString("Credit_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Customer RMA.
        /// </summary>
        internal static string CRMAAdd_Add {
            get {
                return ResourceManager.GetString("CRMAAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Internal Log.
        /// </summary>
        internal static string CRMAInternalLog {
            get {
                return ResourceManager.GetString("CRMAInternalLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Customer RMA Line.
        /// </summary>
        internal static string CRMALines_Add {
            get {
                return ResourceManager.GetString("CRMALines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Customer RMA Line.
        /// </summary>
        internal static string CRMALines_Close {
            get {
                return ResourceManager.GetString("CRMALines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deallocate CRMA Line.
        /// </summary>
        internal static string CRMALines_Deallocate {
            get {
                return ResourceManager.GetString("CRMALines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Customer RMA Line.
        /// </summary>
        internal static string CRMALines_Delete {
            get {
                return ResourceManager.GetString("CRMALines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Customer RMA Line.
        /// </summary>
        internal static string CRMALines_Edit {
            get {
                return ResourceManager.GetString("CRMALines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to CLOSE CUSTOMER RMA LINE.
        /// </summary>
        internal static string CRMALine_Close {
            get {
                return ResourceManager.GetString("CRMALine_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Information.
        /// </summary>
        internal static string CRMAMainInfo_Edit {
            get {
                return ResourceManager.GetString("CRMAMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Customer RMA Line.
        /// </summary>
        internal static string CRMAReceivingLines_Receive {
            get {
                return ResourceManager.GetString("CRMAReceivingLines_Receive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import CSV File.
        /// </summary>
        internal static string CSV_Add {
            get {
                return ResourceManager.GetString("CSV_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Currency Rate.
        /// </summary>
        internal static string CurrencyRateHistory_Delete {
            get {
                return ResourceManager.GetString("CurrencyRateHistory_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Currency Rate.
        /// </summary>
        internal static string CurrencyRateHistory_Edit {
            get {
                return ResourceManager.GetString("CurrencyRateHistory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Currency.
        /// </summary>
        internal static string Currency_Add {
            get {
                return ResourceManager.GetString("Currency_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Currency Details.
        /// </summary>
        internal static string Currency_Edit {
            get {
                return ResourceManager.GetString("Currency_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Current Currency Rates.
        /// </summary>
        internal static string Currency_EditRates {
            get {
                return ResourceManager.GetString("Currency_EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Customer Requirement.
        /// </summary>
        internal static string CusReqMainInfo_Close {
            get {
                return ResourceManager.GetString("CusReqMainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Alternate Part.
        /// </summary>
        internal static string CUSREQMAININFO_DELETE {
            get {
                return ResourceManager.GetString("CUSREQMAININFO_DELETE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Sourcing Result.
        /// </summary>
        internal static string CusReqSourcingResults_Add {
            get {
                return ResourceManager.GetString("CusReqSourcingResults_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to DELETE PARTWATCH MATCH.
        /// </summary>
        internal static string CUSREQSOURCINGRESULTS_DELETE {
            get {
                return ResourceManager.GetString("CUSREQSOURCINGRESULTS_DELETE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Sourcing Result.
        /// </summary>
        internal static string CusReqSourcingResults_Edit {
            get {
                return ResourceManager.GetString("CusReqSourcingResults_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Quote to Client.
        /// </summary>
        internal static string CusReqSourcingResults_EditHub {
            get {
                return ResourceManager.GetString("CusReqSourcingResults_EditHub", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Requirement.
        /// </summary>
        internal static string CustomerRequirementAdd_Add {
            get {
                return ResourceManager.GetString("CustomerRequirementAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Alternate Part.
        /// </summary>
        internal static string CustomerRequirementMainInfo_AddAlternate {
            get {
                return ResourceManager.GetString("CustomerRequirementMainInfo_AddAlternate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Customer Requirement Information.
        /// </summary>
        internal static string CustomerRequirementMainInfo_Edit {
            get {
                return ResourceManager.GetString("CustomerRequirementMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Requirement All Information.
        /// </summary>
        internal static string CustReqAllInfo {
            get {
                return ResourceManager.GetString("CustReqAllInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Debit Note.
        /// </summary>
        internal static string DebitAdd_Add {
            get {
                return ResourceManager.GetString("DebitAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Debit Line.
        /// </summary>
        internal static string DebitLines_Add {
            get {
                return ResourceManager.GetString("DebitLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Debit Line.
        /// </summary>
        internal static string DebitLines_Delete {
            get {
                return ResourceManager.GetString("DebitLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Debit Line.
        /// </summary>
        internal static string DebitLines_Edit {
            get {
                return ResourceManager.GetString("DebitLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Information.
        /// </summary>
        internal static string DebitMainInfo_Edit {
            get {
                return ResourceManager.GetString("DebitMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export Debit Note Details.
        /// </summary>
        internal static string DebitMainInfo_Export {
            get {
                return ResourceManager.GetString("DebitMainInfo_Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release Exported Debit Note Details.
        /// </summary>
        internal static string DebitMainInfo_Release {
            get {
                return ResourceManager.GetString("DebitMainInfo_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debit Bulk Email.
        /// </summary>
        internal static string Debit_Email {
            get {
                return ResourceManager.GetString("Debit_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Division.
        /// </summary>
        internal static string Division_Add {
            get {
                return ResourceManager.GetString("Division_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Division Details.
        /// </summary>
        internal static string Division_Edit {
            get {
                return ResourceManager.GetString("Division_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Document Footer.
        /// </summary>
        internal static string DocFooters_Edit {
            get {
                return ResourceManager.GetString("DocFooters_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Document Header Image.
        /// </summary>
        internal static string DocHeaderImage_Add {
            get {
                return ResourceManager.GetString("DocHeaderImage_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Document Header Image.
        /// </summary>
        internal static string DocHeaderImage_Delete {
            get {
                return ResourceManager.GetString("DocHeaderImage_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Document Header Image.
        /// </summary>
        internal static string DocHeaderImage_Edit {
            get {
                return ResourceManager.GetString("DocHeaderImage_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD ECCN Code.
        /// </summary>
        internal static string ECCN_ADD {
            get {
                return ResourceManager.GetString("ECCN_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT ECCN Code.
        /// </summary>
        internal static string ECCN_EDIT {
            get {
                return ResourceManager.GetString("ECCN_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone ECCN CODE.
        /// </summary>
        internal static string ECCN_EDITCLONE {
            get {
                return ResourceManager.GetString("ECCN_EDITCLONE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT ECCN Warning.
        /// </summary>
        internal static string ECCN_EDITMAP {
            get {
                return ResourceManager.GetString("ECCN_EDITMAP", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT INVOICE INFORMATION AFTER RELEASE.
        /// </summary>
        internal static string editinvoiceinfoafterrelease {
            get {
                return ResourceManager.GetString("editinvoiceinfoafterrelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Cause Category Add.
        /// </summary>
        internal static string EightDCode_Add {
            get {
                return ResourceManager.GetString("EightDCode_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Root Cause Category Edit.
        /// </summary>
        internal static string EightDCode_Edit {
            get {
                return ResourceManager.GetString("EightDCode_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Root Cause sub category.
        /// </summary>
        internal static string EightDSubCategory_Add {
            get {
                return ResourceManager.GetString("EightDSubCategory_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Root Cause sub category.
        /// </summary>
        internal static string EigthDSubCategory_Edit {
            get {
                return ResourceManager.GetString("EigthDSubCategory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Bulk Email Composer.
        /// </summary>
        internal static string EmailComposer_Edit {
            get {
                return ResourceManager.GetString("EmailComposer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Email.
        /// </summary>
        internal static string EmailDocument {
            get {
                return ResourceManager.GetString("EmailDocument", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add new Test.
        /// </summary>
        internal static string ENHANCEDINSPECTIONTEST_ADD {
            get {
                return ResourceManager.GetString("ENHANCEDINSPECTIONTEST_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you would like to delete this enhance inspection test?.
        /// </summary>
        internal static string EnhancedInspectionTest_Delete {
            get {
                return ResourceManager.GetString("EnhancedInspectionTest_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Enhanced Inspection Test.
        /// </summary>
        internal static string ENHANCEDINSPECTIONTEST_EDIT {
            get {
                return ResourceManager.GetString("ENHANCEDINSPECTIONTEST_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ENTERTAINMENT TYPE ADD.
        /// </summary>
        internal static string ENTERTAINMENTTYPE_ADD {
            get {
                return ResourceManager.GetString("ENTERTAINMENTTYPE_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ENTERTAINMENT TYPE EDIT.
        /// </summary>
        internal static string ENTERTAINMENTTYPE_EDIT {
            get {
                return ResourceManager.GetString("ENTERTAINMENTTYPE_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EPR Notify.
        /// </summary>
        internal static string EPRNotify_Notify {
            get {
                return ResourceManager.GetString("EPRNotify_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Excel.
        /// </summary>
        internal static string EXCEL_Add {
            get {
                return ResourceManager.GetString("EXCEL_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Excel.
        /// </summary>
        internal static string EXCEL_Delete {
            get {
                return ResourceManager.GetString("EXCEL_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Export Approval.
        /// </summary>
        internal static string ExportApprovalRequestSend {
            get {
                return ResourceManager.GetString("ExportApprovalRequestSend", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Export Approval Details.
        /// </summary>
        internal static string ExportApproval_Edit {
            get {
                return ResourceManager.GetString("ExportApproval_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit All Export Approval Details.
        /// </summary>
        internal static string ExportApproval_EditAll {
            get {
                return ResourceManager.GetString("ExportApproval_EditAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Send Feedback.
        /// </summary>
        internal static string Feedback_Add {
            get {
                return ResourceManager.GetString("Feedback_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add new Goods In Note.
        /// </summary>
        internal static string GIAdd_Add {
            get {
                return ResourceManager.GetString("GIAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SHORT SHIPMENT REQUEST - WAREHOUSE.
        /// </summary>
        internal static string GILINES_ADDSHORTSHIPMENT {
            get {
                return ResourceManager.GetString("GILINES_ADDSHORTSHIPMENT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Complete Inspection.
        /// </summary>
        internal static string GILines_CloseInspection {
            get {
                return ResourceManager.GetString("GILines_CloseInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Goods In Line.
        /// </summary>
        internal static string GILines_Delete {
            get {
                return ResourceManager.GetString("GILines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Goods In Line.
        /// </summary>
        internal static string GILines_Edit {
            get {
                return ResourceManager.GetString("GILines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export to PDF/Word.
        /// </summary>
        internal static string GILINES_EXPORT {
            get {
                return ResourceManager.GetString("GILINES_EXPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GI Line Images Upload.
        /// </summary>
        internal static string GILINES_IMAGEUPLOAD {
            get {
                return ResourceManager.GetString("GILINES_IMAGEUPLOAD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspect Goods In Line.
        /// </summary>
        internal static string GILines_Inspect {
            get {
                return ResourceManager.GetString("GILines_Inspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit NPR Status.
        /// </summary>
        internal static string GILines_NPRPrinted {
            get {
                return ResourceManager.GetString("GILines_NPRPrinted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Inspect Goods In Line.
        /// </summary>
        internal static string GILines_PhysicalInspect {
            get {
                return ResourceManager.GetString("GILines_PhysicalInspect", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Goods In Label.
        /// </summary>
        internal static string GILines_PrintLabel {
            get {
                return ResourceManager.GetString("GILines_PrintLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Label.
        /// </summary>
        internal static string GILines_PrintNiceLabel {
            get {
                return ResourceManager.GetString("GILines_PrintNiceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Label CRX B2B.
        /// </summary>
        internal static string GILines_PrintNiceLabelCRX {
            get {
                return ResourceManager.GetString("GILines_PrintNiceLabelCRX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Label Rejected.
        /// </summary>
        internal static string GILines_PrintRejectedLabel {
            get {
                return ResourceManager.GetString("GILines_PrintRejectedLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Label Stock.
        /// </summary>
        internal static string GILines_PrintStockLabel {
            get {
                return ResourceManager.GetString("GILines_PrintStockLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantine Product.
        /// </summary>
        internal static string GILINES_QUARANTINE {
            get {
                return ResourceManager.GetString("GILINES_QUARANTINE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split Goods In Line.
        /// </summary>
        internal static string GILINES_SPLITGI {
            get {
                return ResourceManager.GetString("GILINES_SPLITGI", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start Inspection.
        /// </summary>
        internal static string GILines_StartInspection {
            get {
                return ResourceManager.GetString("GILines_StartInspection", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Upload PDF.
        /// </summary>
        internal static string GILINES_UPLOADPDF {
            get {
                return ResourceManager.GetString("GILINES_UPLOADPDF", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Line Notify.
        /// </summary>
        internal static string GILINE_NOTIFY {
            get {
                return ResourceManager.GetString("GILINE_NOTIFY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Goods In Information.
        /// </summary>
        internal static string GIMainInfo_Edit {
            get {
                return ResourceManager.GetString("GIMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Goods In Notification .
        /// </summary>
        internal static string GIMainInfo_Notify {
            get {
                return ResourceManager.GetString("GIMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Master Country.
        /// </summary>
        internal static string GlobalCountryList_Add {
            get {
                return ResourceManager.GetString("GlobalCountryList_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Master Country.
        /// </summary>
        internal static string GlobalCountryList_Edit {
            get {
                return ResourceManager.GetString("GlobalCountryList_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Master Currency.
        /// </summary>
        internal static string GlobalCurrencyList_Add {
            get {
                return ResourceManager.GetString("GlobalCurrencyList_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Master Currency.
        /// </summary>
        internal static string GlobalCurrencyList_Edit {
            get {
                return ResourceManager.GetString("GlobalCurrencyList_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Client Product details.
        /// </summary>
        internal static string GlobalProductDutyRateHistory_Edit {
            get {
                return ResourceManager.GetString("GlobalProductDutyRateHistory_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD NEW GLOBAL PRODUCT CATEGORY NAME.
        /// </summary>
        internal static string GLOBALPRODUCTMAINCATEGORY_ADD {
            get {
                return ResourceManager.GetString("GLOBALPRODUCTMAINCATEGORY_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Category Name.
        /// </summary>
        internal static string GLOBALPRODUCTMAINCATEGORY_EDIT {
            get {
                return ResourceManager.GetString("GLOBALPRODUCTMAINCATEGORY_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MAP GLOBAL PRODUCT TO CATEGORY.
        /// </summary>
        internal static string GlobalProductMainCategory_Map {
            get {
                return ResourceManager.GetString("GlobalProductMainCategory_Map", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD NEW GLOBAL PRODUCT NAME.
        /// </summary>
        internal static string GlobalProductName_Add {
            get {
                return ResourceManager.GetString("GlobalProductName_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT GLOBAL PRODUCT NAME DETAILS .
        /// </summary>
        internal static string GlobalProductName_Edit {
            get {
                return ResourceManager.GetString("GlobalProductName_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD NEW GLOBAL PRODUCT.
        /// </summary>
        internal static string GlobalProduct_Add {
            get {
                return ResourceManager.GetString("GlobalProduct_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT GLOBAL PRODUCT DETAILS.
        /// </summary>
        internal static string GlobalProduct_Edit {
            get {
                return ResourceManager.GetString("GlobalProduct_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Permissions.
        /// </summary>
        internal static string GlobalSecGroupPermissionsGeneral_Edit {
            get {
                return ResourceManager.GetString("GlobalSecGroupPermissionsGeneral_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Security Group Members.
        /// </summary>
        internal static string GlobalSecurityGroupMembers_EditMembers {
            get {
                return ResourceManager.GetString("GlobalSecurityGroupMembers_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Master Tax Details.
        /// </summary>
        internal static string GlobalTax_Edit {
            get {
                return ResourceManager.GetString("GlobalTax_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Master Tax Rates.
        /// </summary>
        internal static string GlobalTax_EditRates {
            get {
                return ResourceManager.GetString("GlobalTax_EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Customer Group Code.
        /// </summary>
        internal static string GroupCodeCompanyAdd_Add {
            get {
                return ResourceManager.GetString("GroupCodeCompanyAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MANAGE GLOBAL SALES ACCESS MEMBERS.
        /// </summary>
        internal static string GSA_EDITMEMBERS {
            get {
                return ResourceManager.GetString("GSA_EDITMEMBERS", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to GT.
        /// </summary>
        internal static string GT {
            get {
                return ResourceManager.GetString("GT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit GT Application Update Information.
        /// </summary>
        internal static string GTUPDATE_EDIT {
            get {
                return ResourceManager.GetString("GTUPDATE_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add HUBRFQ Communication Note.
        /// </summary>
        internal static string HUBRFQExpNotes_Add {
            get {
                return ResourceManager.GetString("HUBRFQExpNotes_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No-Bid HUBRFQ.
        /// </summary>
        internal static string HUBRFQNoBid {
            get {
                return ResourceManager.GetString("HUBRFQNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Recall No-Bid HUBRFQ.
        /// </summary>
        internal static string HUBRFQRecallNoBid {
            get {
                return ResourceManager.GetString("HUBRFQRecallNoBid", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Part Information.
        /// </summary>
        internal static string IHSAdd_Add {
            get {
                return ResourceManager.GetString("IHSAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Incoterm.
        /// </summary>
        internal static string Incoterm_Add {
            get {
                return ResourceManager.GetString("Incoterm_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable Incoterm.
        /// </summary>
        internal static string Incoterm_Disable {
            get {
                return ResourceManager.GetString("Incoterm_Disable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Incoterm Details.
        /// </summary>
        internal static string Incoterm_Edit {
            get {
                return ResourceManager.GetString("Incoterm_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable Incoterm.
        /// </summary>
        internal static string Incoterm_Enable {
            get {
                return ResourceManager.GetString("Incoterm_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Industry Type.
        /// </summary>
        internal static string IndustryType_Add {
            get {
                return ResourceManager.GetString("IndustryType_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Industry Type Details.
        /// </summary>
        internal static string IndustryType_Edit {
            get {
                return ResourceManager.GetString("IndustryType_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Internal Purchase Order.
        /// </summary>
        internal static string InternalPOAdd_Add {
            get {
                return ResourceManager.GetString("InternalPOAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Expedite Note.
        /// </summary>
        internal static string INTERNALPOEXPNOTES_ADD {
            get {
                return ResourceManager.GetString("INTERNALPOEXPNOTES_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Internal Purchase Order Line.
        /// </summary>
        internal static string INTERNALPOLINES_EDIT {
            get {
                return ResourceManager.GetString("INTERNALPOLINES_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Internal Purchase Order.
        /// </summary>
        internal static string InternalPOMainInfo_Edit {
            get {
                return ResourceManager.GetString("InternalPOMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Invoice.
        /// </summary>
        internal static string InvoiceAdd_Add {
            get {
                return ResourceManager.GetString("InvoiceAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Line.
        /// </summary>
        internal static string InvoiceLines_Add {
            get {
                return ResourceManager.GetString("InvoiceLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Invoice Line.
        /// </summary>
        internal static string InvoiceLines_Delete {
            get {
                return ResourceManager.GetString("InvoiceLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Invoice Line.
        /// </summary>
        internal static string InvoiceLines_Edit {
            get {
                return ResourceManager.GetString("InvoiceLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Invoice Line Allocation.
        /// </summary>
        internal static string InvoiceLines_EditAllocation {
            get {
                return ResourceManager.GetString("InvoiceLines_EditAllocation", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Bank Charge Fee.
        /// </summary>
        internal static string InvoiceLines_EditBankFee {
            get {
                return ResourceManager.GetString("InvoiceLines_EditBankFee", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Invoice Information.
        /// </summary>
        internal static string InvoiceMainInfo_Edit {
            get {
                return ResourceManager.GetString("InvoiceMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Invoice Shipping Information.
        /// </summary>
        internal static string InvoiceMainInfo_EditShippingInfo {
            get {
                return ResourceManager.GetString("InvoiceMainInfo_EditShippingInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export Invoice Details.
        /// </summary>
        internal static string InvoiceMainInfo_Export {
            get {
                return ResourceManager.GetString("InvoiceMainInfo_Export", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hold Exported Invoice Details.
        /// </summary>
        internal static string INVOICEMAININFO_HOLD {
            get {
                return ResourceManager.GetString("INVOICEMAININFO_HOLD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release Exported Invoice Details.
        /// </summary>
        internal static string InvoiceMainInfo_Release {
            get {
                return ResourceManager.GetString("InvoiceMainInfo_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un Hold Exported Invoice Details.
        /// </summary>
        internal static string INVOICEMAININFO_UNHOLD {
            get {
                return ResourceManager.GetString("INVOICEMAININFO_UNHOLD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule invoice.
        /// </summary>
        internal static string InvoiceSetting_Add {
            get {
                return ResourceManager.GetString("InvoiceSetting_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Schedule invoice.
        /// </summary>
        internal static string InvoiceSetting_Edit {
            get {
                return ResourceManager.GetString("InvoiceSetting_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Invoice Bulk Email.
        /// </summary>
        internal static string Invoice_Email {
            get {
                return ResourceManager.GetString("Invoice_Email", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Nice Label Path.
        /// </summary>
        internal static string LabelFullPath_Add {
            get {
                return ResourceManager.GetString("LabelFullPath_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Nice Label Path.
        /// </summary>
        internal static string LabelFullPath_Edit {
            get {
                return ResourceManager.GetString("LabelFullPath_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Master Status List.
        /// </summary>
        internal static string LabelSetupItem_Add {
            get {
                return ResourceManager.GetString("LabelSetupItem_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Master Status List.
        /// </summary>
        internal static string LabelSetupItem_Edit {
            get {
                return ResourceManager.GetString("LabelSetupItem_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Local Currency.
        /// </summary>
        internal static string LocalCurrency_Add {
            get {
                return ResourceManager.GetString("LocalCurrency_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Local Currency.
        /// </summary>
        internal static string LocalCurrency_Edit {
            get {
                return ResourceManager.GetString("LocalCurrency_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Lot.
        /// </summary>
        internal static string LotAdd_Add {
            get {
                return ResourceManager.GetString("LotAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Unallocated Services.
        /// </summary>
        internal static string LotItems_Delete_Service {
            get {
                return ResourceManager.GetString("LotItems_Delete_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Unallocated Stock.
        /// </summary>
        internal static string LotItems_Delete_Stock {
            get {
                return ResourceManager.GetString("LotItems_Delete_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to STOCK LOT PROVISION.
        /// </summary>
        internal static string LotItems_LotStockProvision {
            get {
                return ResourceManager.GetString("LotItems_LotStockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Stock Provision.
        /// </summary>
        internal static string LotItems_Save {
            get {
                return ResourceManager.GetString("LotItems_Save", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Provision.
        /// </summary>
        internal static string LotItems_StockProvision {
            get {
                return ResourceManager.GetString("LotItems_StockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Services.
        /// </summary>
        internal static string LotItems_Transfer_Service {
            get {
                return ResourceManager.GetString("LotItems_Transfer_Service", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer Stock.
        /// </summary>
        internal static string LotItems_Transfer_Stock {
            get {
                return ResourceManager.GetString("LotItems_Transfer_Stock", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Lot.
        /// </summary>
        internal static string LotMainInfo_Delete {
            get {
                return ResourceManager.GetString("LotMainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Information.
        /// </summary>
        internal static string LotMainInfo_Edit {
            get {
                return ResourceManager.GetString("LotMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Mail Group Members.
        /// </summary>
        internal static string MailMessageGroupMembers_EditMembers {
            get {
                return ResourceManager.GetString("MailMessageGroupMembers_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Mail Group.
        /// </summary>
        internal static string MailMessageGroups_Add {
            get {
                return ResourceManager.GetString("MailMessageGroups_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Mail Group.
        /// </summary>
        internal static string MailMessageGroups_Delete {
            get {
                return ResourceManager.GetString("MailMessageGroups_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Mail Group.
        /// </summary>
        internal static string MailMessageGroups_Edit {
            get {
                return ResourceManager.GetString("MailMessageGroups_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Folder.
        /// </summary>
        internal static string MailMessages_DeleteFolder {
            get {
                return ResourceManager.GetString("MailMessages_DeleteFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Message.
        /// </summary>
        internal static string MailMessages_DeleteMessage {
            get {
                return ResourceManager.GetString("MailMessages_DeleteMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Folder.
        /// </summary>
        internal static string MailMessages_EditFolder {
            get {
                return ResourceManager.GetString("MailMessages_EditFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create To Do Item.
        /// </summary>
        internal static string MailMessages_MarkAsToDo {
            get {
                return ResourceManager.GetString("MailMessages_MarkAsToDo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Move Message.
        /// </summary>
        internal static string MailMessages_MoveMessage {
            get {
                return ResourceManager.GetString("MailMessages_MoveMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Folder.
        /// </summary>
        internal static string MailMessages_NewFolder {
            get {
                return ResourceManager.GetString("MailMessages_NewFolder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to New Message.
        /// </summary>
        internal static string MailMessages_NewMessage {
            get {
                return ResourceManager.GetString("MailMessages_NewMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Manufacturer.
        /// </summary>
        internal static string ManufacturerAdd_Add {
            get {
                return ResourceManager.GetString("ManufacturerAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Related Company.
        /// </summary>
        internal static string ManufacturerCompanies_Add {
            get {
                return ResourceManager.GetString("ManufacturerCompanies_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove Related Company.
        /// </summary>
        internal static string ManufacturerCompanies_Delete {
            get {
                return ResourceManager.GetString("ManufacturerCompanies_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Manufacturer Information.
        /// </summary>
        internal static string ManufacturerMainInfo_Edit {
            get {
                return ResourceManager.GetString("ManufacturerMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Supplier who Distributes.
        /// </summary>
        internal static string ManufacturerSuppliers_Add {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Supplier who Distributes.
        /// </summary>
        internal static string ManufacturerSuppliers_Delete {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Supplier who Distributes.
        /// </summary>
        internal static string ManufacturerSuppliers_Edit {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to VIEW SUPPLIER WHO DISTRIBUTES.
        /// </summary>
        internal static string ManufacturerSuppliers_View {
            get {
                return ResourceManager.GetString("ManufacturerSuppliers_View", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Master Login.
        /// </summary>
        internal static string MASTERLOGIN_CONFIRM {
            get {
                return ResourceManager.GetString("MASTERLOGIN_CONFIRM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to NPR Notify.
        /// </summary>
        internal static string NPRNotify_Notify {
            get {
                return ResourceManager.GetString("NPRNotify_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise Export.
        /// </summary>
        internal static string OGELApprovetitle {
            get {
                return ResourceManager.GetString("OGELApprovetitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New OGEL License.
        /// </summary>
        internal static string OGELLicenses_Add {
            get {
                return ResourceManager.GetString("OGELLicenses_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit OGEL License details.
        /// </summary>
        internal static string OGELLicenses_Edit {
            get {
                return ResourceManager.GetString("OGELLicenses_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to OGEL Lines.
        /// </summary>
        internal static string OGELLines {
            get {
                return ResourceManager.GetString("OGELLines", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Package.
        /// </summary>
        internal static string Package_Add {
            get {
                return ResourceManager.GetString("Package_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Package details.
        /// </summary>
        internal static string Package_Edit {
            get {
                return ResourceManager.GetString("Package_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Document File Size.
        /// </summary>
        internal static string PDFDocumentFileSize_Add {
            get {
                return ResourceManager.GetString("PDFDocumentFileSize_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Document File Size Details.
        /// </summary>
        internal static string PDFDocumentFileSize_Edit {
            get {
                return ResourceManager.GetString("PDFDocumentFileSize_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PDF.
        /// </summary>
        internal static string PDF_Add {
            get {
                return ResourceManager.GetString("PDF_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete PDF.
        /// </summary>
        internal static string PDF_Delete {
            get {
                return ResourceManager.GetString("PDF_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Purchase Order.
        /// </summary>
        internal static string POAdd_Add {
            get {
                return ResourceManager.GetString("POAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Expedite Note.
        /// </summary>
        internal static string POExpNotes_Add {
            get {
                return ResourceManager.GetString("POExpNotes_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ARE YOU SURE YOU WOULD LIKE TO UPDATE IN BULK?.
        /// </summary>
        internal static string PoHubSourcing_EditReverseLogisticsBulk {
            get {
                return ResourceManager.GetString("PoHubSourcing_EditReverseLogisticsBulk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Purchase Order Line.
        /// </summary>
        internal static string POLines_Add {
            get {
                return ResourceManager.GetString("POLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocate Purchase Order Line.
        /// </summary>
        internal static string POLines_Allocate {
            get {
                return ResourceManager.GetString("POLines_Allocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Purchase Order Line.
        /// </summary>
        internal static string POLines_Close {
            get {
                return ResourceManager.GetString("POLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deallocate Purchase Order Line.
        /// </summary>
        internal static string POLines_Deallocate {
            get {
                return ResourceManager.GetString("POLines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Purchase Order Line.
        /// </summary>
        internal static string POLines_Delete {
            get {
                return ResourceManager.GetString("POLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Purchase Order Line.
        /// </summary>
        internal static string POLines_Edit {
            get {
                return ResourceManager.GetString("POLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Post Line.
        /// </summary>
        internal static string POLines_Post {
            get {
                return ResourceManager.GetString("POLines_Post", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Post All Lines.
        /// </summary>
        internal static string POLines_PostAll {
            get {
                return ResourceManager.GetString("POLines_PostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpost Line.
        /// </summary>
        internal static string POLines_Unpost {
            get {
                return ResourceManager.GetString("POLines_Unpost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpost All Lines.
        /// </summary>
        internal static string POLines_UnpostAll {
            get {
                return ResourceManager.GetString("POLines_UnpostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release Purchase Order Line.
        /// </summary>
        internal static string POLine_Release {
            get {
                return ResourceManager.GetString("POLine_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UnRelease Purchase Order Line.
        /// </summary>
        internal static string POLine_UnRelease {
            get {
                return ResourceManager.GetString("POLine_UnRelease", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approve Purchase Order.
        /// </summary>
        internal static string POMainInfo_Approve {
            get {
                return ResourceManager.GetString("POMainInfo_Approve", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Purchase Order.
        /// </summary>
        internal static string POMainInfo_Close {
            get {
                return ResourceManager.GetString("POMainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Un-approve Purchase Order.
        /// </summary>
        internal static string POMainInfo_Disapprove {
            get {
                return ResourceManager.GetString("POMainInfo_Disapprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Purchase Order Information.
        /// </summary>
        internal static string POMainInfo_Edit {
            get {
                return ResourceManager.GetString("POMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Purchase Order Notification.
        /// </summary>
        internal static string POMainInfo_Notify {
            get {
                return ResourceManager.GetString("POMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Price Request.
        /// </summary>
        internal static string POQuoteAdd_Add {
            get {
                return ResourceManager.GetString("POQuoteAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Price Request Line.
        /// </summary>
        internal static string POQuoteLines_Add {
            get {
                return ResourceManager.GetString("POQuoteLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Price Request Line.
        /// </summary>
        internal static string POQuoteLines_Close {
            get {
                return ResourceManager.GetString("POQuoteLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Price Request Information.
        /// </summary>
        internal static string POQuoteMainInfo_Edit {
            get {
                return ResourceManager.GetString("POQuoteMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notification.
        /// </summary>
        internal static string POQuoteMainInfo_Notify {
            get {
                return ResourceManager.GetString("POQuoteMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Purchase Order Line.
        /// </summary>
        internal static string POReceivingLines_Receive {
            get {
                return ResourceManager.GetString("POReceivingLines_Receive", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD NEW PPV/BOM.
        /// </summary>
        internal static string PPVBOM_ADD {
            get {
                return ResourceManager.GetString("PPVBOM_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT PPV/BOM DETAILS.
        /// </summary>
        internal static string PPVBOM_EDIT {
            get {
                return ResourceManager.GetString("PPVBOM_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Printer.
        /// </summary>
        internal static string Printer_Add {
            get {
                return ResourceManager.GetString("Printer_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Printer Details.
        /// </summary>
        internal static string Printer_Edit {
            get {
                return ResourceManager.GetString("Printer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Product.
        /// </summary>
        internal static string Product_Add {
            get {
                return ResourceManager.GetString("Product_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Product details.
        /// </summary>
        internal static string Product_Edit {
            get {
                return ResourceManager.GetString("Product_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Price Request line detail.
        /// </summary>
        internal static string PurchaseRequestLineDetail_Add {
            get {
                return ResourceManager.GetString("PurchaseRequestLineDetail_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Price Request Line Detail.
        /// </summary>
        internal static string PurchaseRequestLineDetail_Edit {
            get {
                return ResourceManager.GetString("PurchaseRequestLineDetail_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Quote.
        /// </summary>
        internal static string QuoteAdd_Add {
            get {
                return ResourceManager.GetString("QuoteAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Quote Line.
        /// </summary>
        internal static string QuoteLines_Add {
            get {
                return ResourceManager.GetString("QuoteLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Quote Line.
        /// </summary>
        internal static string QuoteLines_Close {
            get {
                return ResourceManager.GetString("QuoteLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Quote Line.
        /// </summary>
        internal static string QUOTELINES_DELETE {
            get {
                return ResourceManager.GetString("QUOTELINES_DELETE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Line.
        /// </summary>
        internal static string QuoteLines_Edit {
            get {
                return ResourceManager.GetString("QuoteLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Original Offer.
        /// </summary>
        internal static string QuoteLines_EditOffer {
            get {
                return ResourceManager.GetString("QuoteLines_EditOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Quote Information.
        /// </summary>
        internal static string QuoteMainInfo_Edit {
            get {
                return ResourceManager.GetString("QuoteMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Reason.
        /// </summary>
        internal static string Reason_Add {
            get {
                return ResourceManager.GetString("Reason_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Reason details.
        /// </summary>
        internal static string Reason_Edit {
            get {
                return ResourceManager.GetString("Reason_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SOR Approval.
        /// </summary>
        internal static string REQUESTAPPROVAL {
            get {
                return ResourceManager.GetString("REQUESTAPPROVAL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restricted Manufacturer.
        /// </summary>
        internal static string RestrictedManufacture_Add {
            get {
                return ResourceManager.GetString("RestrictedManufacture_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restricted Manufacturer.
        /// </summary>
        internal static string RestrictedManufacture_Edit {
            get {
                return ResourceManager.GetString("RestrictedManufacture_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Images.
        /// </summary>
        internal static string SAIMAGEDRAGDROP_ADD {
            get {
                return ResourceManager.GetString("SAIMAGEDRAGDROP_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales BOM Import.
        /// </summary>
        internal static string SalesBOM_Import {
            get {
                return ResourceManager.GetString("SalesBOM_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Manager Approve.
        /// </summary>
        internal static string SA_LineManagerApprove {
            get {
                return ResourceManager.GetString("SA_LineManagerApprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Line Manager Decline.
        /// </summary>
        internal static string SA_LineManagerDecline {
            get {
                return ResourceManager.GetString("SA_LineManagerDecline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Approve Independent Testing Recommended.
        /// </summary>
        internal static string SA_LineManagerIndpndt {
            get {
                return ResourceManager.GetString("SA_LineManagerIndpndt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Approve.
        /// </summary>
        internal static string SA_QualityApprove {
            get {
                return ResourceManager.GetString("SA_QualityApprove", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Decline.
        /// </summary>
        internal static string SA_QualityDecline {
            get {
                return ResourceManager.GetString("SA_QualityDecline", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quality Escalate.
        /// </summary>
        internal static string SA_QualityEscalate {
            get {
                return ResourceManager.GetString("SA_QualityEscalate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Security Group Members.
        /// </summary>
        internal static string SecurityGroupMembers_EditMembers {
            get {
                return ResourceManager.GetString("SecurityGroupMembers_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Permissions.
        /// </summary>
        internal static string SecurityGroupPermissionsGeneral_Edit {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsGeneral_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Report Permissions.
        /// </summary>
        internal static string SecurityGroupPermissionsReports_Edit {
            get {
                return ResourceManager.GetString("SecurityGroupPermissionsReports_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Security Group.
        /// </summary>
        internal static string SecurityGroups_Add {
            get {
                return ResourceManager.GetString("SecurityGroups_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clone Security Group.
        /// </summary>
        internal static string SecurityGroups_Clone {
            get {
                return ResourceManager.GetString("SecurityGroups_Clone", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Security Group.
        /// </summary>
        internal static string SecurityGroups_Delete {
            get {
                return ResourceManager.GetString("SecurityGroups_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Security Group.
        /// </summary>
        internal static string SecurityGroups_Edit {
            get {
                return ResourceManager.GetString("SecurityGroups_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Group Members.
        /// </summary>
        internal static string SecurityUserGroups_EditMembers {
            get {
                return ResourceManager.GetString("SecurityUserGroups_EditMembers", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New User.
        /// </summary>
        internal static string SecurityUsers_Add {
            get {
                return ResourceManager.GetString("SecurityUsers_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disable User.
        /// </summary>
        internal static string SecurityUsers_Disable {
            get {
                return ResourceManager.GetString("SecurityUsers_Disable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit User.
        /// </summary>
        internal static string SecurityUsers_Edit {
            get {
                return ResourceManager.GetString("SecurityUsers_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable User.
        /// </summary>
        internal static string SecurityUsers_Enable {
            get {
                return ResourceManager.GetString("SecurityUsers_Enable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Transfer User Sales Accounts.
        /// </summary>
        internal static string SecurityUsers_Transfer {
            get {
                return ResourceManager.GetString("SecurityUsers_Transfer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Sequence Numbers.
        /// </summary>
        internal static string Sequencer_Edit {
            get {
                return ResourceManager.GetString("Sequencer_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Service.
        /// </summary>
        internal static string ServiceAdd_Add {
            get {
                return ResourceManager.GetString("ServiceAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deallocate Service.
        /// </summary>
        internal static string ServiceAllocations_Deallocate {
            get {
                return ResourceManager.GetString("ServiceAllocations_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Service.
        /// </summary>
        internal static string ServiceMainInfo_Delete {
            get {
                return ResourceManager.GetString("ServiceMainInfo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Information.
        /// </summary>
        internal static string ServiceMainInfo_Edit {
            get {
                return ResourceManager.GetString("ServiceMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Shipping Method.
        /// </summary>
        internal static string ShipVia_Add {
            get {
                return ResourceManager.GetString("ShipVia_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Shipping Method details.
        /// </summary>
        internal static string ShipVia_Edit {
            get {
                return ResourceManager.GetString("ShipVia_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to SHORT SHIPMENT REQUEST - WAREHOUSE.
        /// </summary>
        internal static string SHORTSHIPMENTNOTIFY_NOTIFY {
            get {
                return ResourceManager.GetString("SHORTSHIPMENTNOTIFY_NOTIFY", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Sales Order.
        /// </summary>
        internal static string SOAdd_Add {
            get {
                return ResourceManager.GetString("SOAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise Sales Order.
        /// </summary>
        internal static string SOAuthorisation_Authorise {
            get {
                return ResourceManager.GetString("SOAuthorisation_Authorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to De-authorise Sales Order.
        /// </summary>
        internal static string SOAuthorisation_Deauthorise {
            get {
                return ResourceManager.GetString("SOAuthorisation_Deauthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ready To Ship.
        /// </summary>
        internal static string SOAuthorisation_ReadyToShip {
            get {
                return ResourceManager.GetString("SOAuthorisation_ReadyToShip", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Sales Order Line.
        /// </summary>
        internal static string SOLines_Add {
            get {
                return ResourceManager.GetString("SOLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocate Sales Order Line.
        /// </summary>
        internal static string SOLines_Allocate {
            get {
                return ResourceManager.GetString("SOLines_Allocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Sales Order Line.
        /// </summary>
        internal static string SOLines_Close {
            get {
                return ResourceManager.GetString("SOLines_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create Clone.
        /// </summary>
        internal static string SOLINES_CREATECLONE {
            get {
                return ResourceManager.GetString("SOLINES_CREATECLONE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deallocate Sales Order Line.
        /// </summary>
        internal static string SOLines_Deallocate {
            get {
                return ResourceManager.GetString("SOLines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Sales Order Line.
        /// </summary>
        internal static string SOLines_Delete {
            get {
                return ResourceManager.GetString("SOLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Sales Order Line.
        /// </summary>
        internal static string SOLines_Edit {
            get {
                return ResourceManager.GetString("SOLines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Post Line.
        /// </summary>
        internal static string SOLines_Post {
            get {
                return ResourceManager.GetString("SOLines_Post", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Post All Lines.
        /// </summary>
        internal static string SOLines_PostAll {
            get {
                return ResourceManager.GetString("SOLines_PostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpost Line.
        /// </summary>
        internal static string SOLines_Unpost {
            get {
                return ResourceManager.GetString("SOLines_Unpost", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unpost All Lines.
        /// </summary>
        internal static string SOLines_UnpostAll {
            get {
                return ResourceManager.GetString("SOLines_UnpostAll", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string SOLines_Warehouse {
            get {
                return ResourceManager.GetString("SOLines_Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Authorise Sales Order.
        /// </summary>
        internal static string SOMainInfo_Authorise {
            get {
                return ResourceManager.GetString("SOMainInfo_Authorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Sales Order.
        /// </summary>
        internal static string SOMainInfo_Close {
            get {
                return ResourceManager.GetString("SOMainInfo_Close", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to De-authorise Sales Order.
        /// </summary>
        internal static string SOMainInfo_Deauthorise {
            get {
                return ResourceManager.GetString("SOMainInfo_Deauthorise", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Sales Order Information.
        /// </summary>
        internal static string SOMainInfo_Edit {
            get {
                return ResourceManager.GetString("SOMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sales Order Notification .
        /// </summary>
        internal static string SOMainInfo_Notify {
            get {
                return ResourceManager.GetString("SOMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pay By Credit Card.
        /// </summary>
        internal static string SOMainInfo_PayByCreditCard {
            get {
                return ResourceManager.GetString("SOMainInfo_PayByCreditCard", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Warehouse.
        /// </summary>
        internal static string SOMainInfo_Warehouse {
            get {
                return ResourceManager.GetString("SOMainInfo_Warehouse", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Sales Order Line.
        /// </summary>
        internal static string SOShippingLines_Ship {
            get {
                return ResourceManager.GetString("SOShippingLines_Ship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Sourcing Link.
        /// </summary>
        internal static string SourcingLinks_Add {
            get {
                return ResourceManager.GetString("SourcingLinks_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Sourcing Link.
        /// </summary>
        internal static string SourcingLinks_Delete {
            get {
                return ResourceManager.GetString("SourcingLinks_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Sourcing Link.
        /// </summary>
        internal static string SourcingLinks_Edit {
            get {
                return ResourceManager.GetString("SourcingLinks_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Offer.
        /// </summary>
        internal static string Sourcing_AddOffer {
            get {
                return ResourceManager.GetString("Sourcing_AddOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Sourcing Info.
        /// </summary>
        internal static string Sourcing_AddStockInfo {
            get {
                return ResourceManager.GetString("Sourcing_AddStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add to Requirement.
        /// </summary>
        internal static string Sourcing_AddToReq {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add to Customer Requirement.
        /// </summary>
        internal static string Sourcing_AddToReq_SelectCusReq {
            get {
                return ResourceManager.GetString("Sourcing_AddToReq_SelectCusReq", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Trusted.
        /// </summary>
        internal static string Sourcing_AddTrusted {
            get {
                return ResourceManager.GetString("Sourcing_AddTrusted", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bom Import Sourcing Result.
        /// </summary>
        internal static string SOURCING_BOMIMPORTSOURCINGRESULT {
            get {
                return ResourceManager.GetString("SOURCING_BOMIMPORTSOURCINGRESULT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show Bulk Edited Reverse Logistic History.
        /// </summary>
        internal static string Sourcing_BulkEditReverseLogisticHistory {
            get {
                return ResourceManager.GetString("Sourcing_BulkEditReverseLogisticHistory", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Alternative Part.
        /// </summary>
        internal static string Sourcing_EditAltPart {
            get {
                return ResourceManager.GetString("Sourcing_EditAltPart", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT Strategic Offers.
        /// </summary>
        internal static string SOURCING_EDITEPO {
            get {
                return ResourceManager.GetString("SOURCING_EDITEPO", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Offer.
        /// </summary>
        internal static string Sourcing_EditOffer {
            get {
                return ResourceManager.GetString("Sourcing_EditOffer", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT Reverse Logistic.
        /// </summary>
        internal static string Sourcing_EditReverseLogistic {
            get {
                return ResourceManager.GetString("Sourcing_EditReverseLogistic", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ARE YOU SURE YOU WOULD LIKE TO UPDATE IN BULK?.
        /// </summary>
        internal static string Sourcing_EditReverseLogisticsBulk {
            get {
                return ResourceManager.GetString("Sourcing_EditReverseLogisticsBulk", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Stock.
        /// </summary>
        internal static string SOURCING_EDITSTOCK {
            get {
                return ResourceManager.GetString("SOURCING_EDITSTOCK", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Sourcing Info.
        /// </summary>
        internal static string Sourcing_EditStockInfo {
            get {
                return ResourceManager.GetString("Sourcing_EditStockInfo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategic Offers Import Tool.
        /// </summary>
        internal static string SOURCING_EPOTOOL {
            get {
                return ResourceManager.GetString("SOURCING_EPOTOOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Request for Quote.
        /// </summary>
        internal static string Sourcing_RFQ {
            get {
                return ResourceManager.GetString("Sourcing_RFQ", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check Supplier / Manufacturer Data.
        /// </summary>
        internal static string SOURCING_SEARCHOFFER {
            get {
                return ResourceManager.GetString("SOURCING_SEARCHOFFER", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Strategic Offers Import Tool.
        /// </summary>
        internal static string SOURCING_STOCKIMPORTEPOTOOL {
            get {
                return ResourceManager.GetString("SOURCING_STOCKIMPORTEPOTOOL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers Import Tool.
        /// </summary>
        internal static string Sourcing_StockImportTool {
            get {
                return ResourceManager.GetString("Sourcing_StockImportTool", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Supplier RMA.
        /// </summary>
        internal static string SRMAAdd_Add {
            get {
                return ResourceManager.GetString("SRMAAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Internal Log.
        /// </summary>
        internal static string SRMAInternalLog {
            get {
                return ResourceManager.GetString("SRMAInternalLog", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Supplier RMA Line.
        /// </summary>
        internal static string SRMALines_Add {
            get {
                return ResourceManager.GetString("SRMALines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Allocate SRMA Line.
        /// </summary>
        internal static string SRMALines_Allocate {
            get {
                return ResourceManager.GetString("SRMALines_Allocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deallocate SRMA Line.
        /// </summary>
        internal static string SRMALines_Deallocate {
            get {
                return ResourceManager.GetString("SRMALines_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Supplier RMA Line.
        /// </summary>
        internal static string SRMALines_Delete {
            get {
                return ResourceManager.GetString("SRMALines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Supplier RMA Line.
        /// </summary>
        internal static string SRMALines_Edit {
            get {
                return ResourceManager.GetString("SRMALines_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Supplier RMA Information.
        /// </summary>
        internal static string SRMAMainInfo_Edit {
            get {
                return ResourceManager.GetString("SRMAMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ship Supplier RMA Line.
        /// </summary>
        internal static string SRMAShippingLines_Ship {
            get {
                return ResourceManager.GetString("SRMAShippingLines_Ship", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel Short Shipment.
        /// </summary>
        internal static string SSMAININFO_CANCEL {
            get {
                return ResourceManager.GetString("SSMAININFO_CANCEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Close Short Shipment.
        /// </summary>
        internal static string SSMAININFO_CLOSE {
            get {
                return ResourceManager.GetString("SSMAININFO_CLOSE", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UPDATE SHORT SHIPMENT.
        /// </summary>
        internal static string SSMAININFO_CONFIRM {
            get {
                return ResourceManager.GetString("SSMAININFO_CONFIRM", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD NEW STAR RATING.
        /// </summary>
        internal static string StarRating_Add {
            get {
                return ResourceManager.GetString("StarRating_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Stock Item.
        /// </summary>
        internal static string StockAdd_Add {
            get {
                return ResourceManager.GetString("StockAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deallocate Stock.
        /// </summary>
        internal static string StockAllocations_Deallocate {
            get {
                return ResourceManager.GetString("StockAllocations_Deallocate", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add a new Image.
        /// </summary>
        internal static string StockImages_Add {
            get {
                return ResourceManager.GetString("StockImages_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Image.
        /// </summary>
        internal static string StockImages_Delete {
            get {
                return ResourceManager.GetString("StockImages_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Stock Log Reason.
        /// </summary>
        internal static string StockLogReason_Add {
            get {
                return ResourceManager.GetString("StockLogReason_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Stock Log Reason Details.
        /// </summary>
        internal static string StockLogReason_Edit {
            get {
                return ResourceManager.GetString("StockLogReason_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Information.
        /// </summary>
        internal static string StockMainInfo_Edit {
            get {
                return ResourceManager.GetString("StockMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Make Stock Available.
        /// </summary>
        internal static string StockMainInfo_MakeAvailable {
            get {
                return ResourceManager.GetString("StockMainInfo_MakeAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Excess Label.
        /// </summary>
        internal static string STOCKMAININFO_PRINTEXCESSSTOCKLABEL {
            get {
                return ResourceManager.GetString("STOCKMAININFO_PRINTEXCESSSTOCKLABEL", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Label.
        /// </summary>
        internal static string StockMainInfo_PrintNiceLabel {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintNiceLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Label CRX B2B.
        /// </summary>
        internal static string StockMainInfo_PrintNiceLabelCRX {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintNiceLabelCRX", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Label Rejected.
        /// </summary>
        internal static string StockMainInfo_PrintRejectedLabel {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintRejectedLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Label.
        /// </summary>
        internal static string StockMainInfo_PrintStockLabel {
            get {
                return ResourceManager.GetString("StockMainInfo_PrintStockLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Quarantine Stock.
        /// </summary>
        internal static string StockMainInfo_Quarantine {
            get {
                return ResourceManager.GetString("StockMainInfo_Quarantine", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Split Stock.
        /// </summary>
        internal static string StockMainInfo_Split {
            get {
                return ResourceManager.GetString("StockMainInfo_Split", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stock Provision.
        /// </summary>
        internal static string StockMainInfo_StockProvision {
            get {
                return ResourceManager.GetString("StockMainInfo_StockProvision", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Supplier Invoice.
        /// </summary>
        internal static string SupplierInvoiceAdd_Add {
            get {
                return ResourceManager.GetString("SupplierInvoiceAdd_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice Line.
        /// </summary>
        internal static string SupplierInvoiceLines_Add {
            get {
                return ResourceManager.GetString("SupplierInvoiceLines_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Supplier Invoice Line.
        /// </summary>
        internal static string SupplierInvoiceLines_Delete {
            get {
                return ResourceManager.GetString("SupplierInvoiceLines_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Main Supplier Invoice Information.
        /// </summary>
        internal static string SupplierInvoiceMainInfo_Edit {
            get {
                return ResourceManager.GetString("SupplierInvoiceMainInfo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Supplier Invoice Notification.
        /// </summary>
        internal static string SupplierInvoiceMainInfo_Notify {
            get {
                return ResourceManager.GetString("SupplierInvoiceMainInfo_Notify", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Supplier Approval Details.
        /// </summary>
        internal static string Supplier_Approval_Edit {
            get {
                return ResourceManager.GetString("Supplier_Approval_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add to Requirement.
        /// </summary>
        internal static string Surcing_AddToReqo {
            get {
                return ResourceManager.GetString("Surcing_AddToReqo", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Permissions for Tab Security.
        /// </summary>
        internal static string TabSecurity_Edit {
            get {
                return ResourceManager.GetString("TabSecurity_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete Future Tax Rate.
        /// </summary>
        internal static string TaxRateHistory_Delete {
            get {
                return ResourceManager.GetString("TaxRateHistory_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Tax.
        /// </summary>
        internal static string Tax_Add {
            get {
                return ResourceManager.GetString("Tax_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Tax Details.
        /// </summary>
        internal static string Tax_Edit {
            get {
                return ResourceManager.GetString("Tax_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Tax Rates.
        /// </summary>
        internal static string Tax_EditRates {
            get {
                return ResourceManager.GetString("Tax_EditRates", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Team.
        /// </summary>
        internal static string Team_Add {
            get {
                return ResourceManager.GetString("Team_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Team Details.
        /// </summary>
        internal static string Team_Edit {
            get {
                return ResourceManager.GetString("Team_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Terms.
        /// </summary>
        internal static string Terms_Add {
            get {
                return ResourceManager.GetString("Terms_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Terms Details.
        /// </summary>
        internal static string Terms_Edit {
            get {
                return ResourceManager.GetString("Terms_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD To Do List Type.
        /// </summary>
        internal static string TODOLISTTYPE_ADD {
            get {
                return ResourceManager.GetString("TODOLISTTYPE_ADD", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EDIT To Do List Type.
        /// </summary>
        internal static string TODOLISTTYPE_EDIT {
            get {
                return ResourceManager.GetString("TODOLISTTYPE_EDIT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete To Do Item(s).
        /// </summary>
        internal static string ToDo_Delete {
            get {
                return ResourceManager.GetString("ToDo_Delete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit To Do Item.
        /// </summary>
        internal static string ToDo_Edit {
            get {
                return ResourceManager.GetString("ToDo_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark Item(s) Complete.
        /// </summary>
        internal static string ToDo_MarkComplete {
            get {
                return ResourceManager.GetString("ToDo_MarkComplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark Item(s) Incomplete.
        /// </summary>
        internal static string ToDo_MarkIncomplete {
            get {
                return ResourceManager.GetString("ToDo_MarkIncomplete", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Preferences.
        /// </summary>
        internal static string UserPreferences_Edit {
            get {
                return ResourceManager.GetString("UserPreferences_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Change Password.
        /// </summary>
        internal static string UserProfile_ChangePassword {
            get {
                return ResourceManager.GetString("UserProfile_ChangePassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Profile.
        /// </summary>
        internal static string UserProfile_Edit {
            get {
                return ResourceManager.GetString("UserProfile_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset Password.
        /// </summary>
        internal static string UserProfile_ResetPassword {
            get {
                return ResourceManager.GetString("UserProfile_ResetPassword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New User.
        /// </summary>
        internal static string User_Add {
            get {
                return ResourceManager.GetString("User_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit User details.
        /// </summary>
        internal static string User_Edit {
            get {
                return ResourceManager.GetString("User_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to BOM Manager Import.
        /// </summary>
        internal static string UTILITYBOMMANAGER_IMPORT {
            get {
                return ResourceManager.GetString("UTILITYBOMMANAGER_IMPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility BOM Import.
        /// </summary>
        internal static string UtilityBOM_Import {
            get {
                return ResourceManager.GetString("UtilityBOM_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bulk Offer Scheduled Import.
        /// </summary>
        internal static string UtilityHUBOffer_ImportLarge {
            get {
                return ResourceManager.GetString("UtilityHUBOffer_ImportLarge", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UTILITY LOG.
        /// </summary>
        internal static string UTILITYLOG_IMPORT {
            get {
                return ResourceManager.GetString("UTILITYLOG_IMPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offers Import Tool.
        /// </summary>
        internal static string UTILITYOFFER_IMPORT {
            get {
                return ResourceManager.GetString("UTILITYOFFER_IMPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Prospective Offer Import Tool.
        /// </summary>
        internal static string UtilityProspectiveOffer_Import {
            get {
                return ResourceManager.GetString("UtilityProspectiveOffer_Import", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reverse Logistics Import Tool.
        /// </summary>
        internal static string UTILITYREVERSELOGISTICS_IMPORT {
            get {
                return ResourceManager.GetString("UTILITYREVERSELOGISTICS_IMPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Utility Stock Import.
        /// </summary>
        internal static string UTILITYSTOCK_IMPORT {
            get {
                return ResourceManager.GetString("UTILITYSTOCK_IMPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to X-Match Import.
        /// </summary>
        internal static string UTILITYXMATCH_IMPORT {
            get {
                return ResourceManager.GetString("UTILITYXMATCH_IMPORT", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reverse Logistics Import Tool.
        /// </summary>
        internal static string Utility_ReverseLogisticsImport {
            get {
                return ResourceManager.GetString("Utility_ReverseLogisticsImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reverse Logistics Import Tool.
        /// </summary>
        internal static string Utility_RLImport {
            get {
                return ResourceManager.GetString("Utility_RLImport", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add New Warehouse.
        /// </summary>
        internal static string Warehouse_Add {
            get {
                return ResourceManager.GetString("Warehouse_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear Default Warehouse.
        /// </summary>
        internal static string Warehouse_ClearDefault {
            get {
                return ResourceManager.GetString("Warehouse_ClearDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Warehouse Details.
        /// </summary>
        internal static string Warehouse_Edit {
            get {
                return ResourceManager.GetString("Warehouse_Edit", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set Default Warehouse.
        /// </summary>
        internal static string Warehouse_MakeDefault {
            get {
                return ResourceManager.GetString("Warehouse_MakeDefault", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ADD WARNING MESSAGE.
        /// </summary>
        internal static string WarningMessage_Add {
            get {
                return ResourceManager.GetString("WarningMessage_Add", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Edit Warning Message.
        /// </summary>
        internal static string WarningMessage_Edit {
            get {
                return ResourceManager.GetString("WarningMessage_Edit", resourceCulture);
            }
        }
    }
}
