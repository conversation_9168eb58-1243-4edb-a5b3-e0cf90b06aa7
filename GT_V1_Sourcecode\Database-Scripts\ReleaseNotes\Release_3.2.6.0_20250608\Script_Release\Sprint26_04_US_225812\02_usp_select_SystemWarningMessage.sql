﻿/*
===========================================================================================================================================================  
TASK			UPDATED BY       DATE				ACTION		DESCRIPTION  
[US-225812]     Trung Pham		 20-Apr-2025		UPDATE		Return Country catagory
===========================================================================================================================================================  
*/  
  
  
CREATE OR ALTER PROCEDURE usp_select_SystemWarningMessage    
    @SystemWarningMessageNo int    
AS     
    SELECT    
  swm.SystemWarningMessageId,  
  swm.WarningText,  
  wm.WarningName AS WarningKey,  
  CASE WHEN swm.ApplyToCatagoryNo=1 THEN 'Manufacturer'  
       WHEN swm.ApplyToCatagoryNo=2 THEN 'Product'  
    WHEN swm.ApplyToCatagoryNo=3 THEN 'Vendor'  
	WHEN swm.ApplyToCatagoryNo=5 THEN 'Country'  
    ELSE '' END AS ApplyToCatagory,  
    CASE WHEN swm.ApplyTo=0 THEN 'ALL' ELSE CASE WHEN swm.ApplyToCatagoryNo=1 THEN mn.ManufacturerName  
           WHEN swm.ApplyToCatagoryNo=2 THEN pd.ProductName  
     WHEN swm.ApplyToCatagoryNo=3 THEN cm.CompanyName
	 WHEN swm.ApplyToCatagoryNo=5 THEN c.CountryName ELSE '' END END  
     AS [Applyto],  
    swm.UpdatedBy,  
    swm.DLUP,  
    swm.InActive,
	CASE WHEN swm.ApplyToCatagoryNo = 5 THEN swm.ApplyTo ELSE NULL END AS ApplyToCountryId
    FROM    dbo.tbSystemWarningMessage swm    
 INNER JOIN dbo.tbWarningMessage wm ON swm.WarningNo=wm.WarningId  
  LEFT JOIN dbo.tbManufacturer mn ON swm.ApplyTo=mn.ManufacturerId  
  LEFT JOIN dbo.tbProduct pd ON swm.ApplyTo=pd.ProductId  
  LEFT JOIN dbo.tbCompany cm ON swm.ApplyTo=cm.CompanyId  
  LEFT JOIN dbo.tbCountry c ON swm.ApplyTo = c.CountryId
    WHERE    SystemWarningMessageId= @SystemWarningMessageNo   
  
  
  