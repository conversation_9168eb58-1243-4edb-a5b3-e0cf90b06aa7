Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.ItemSearch");Rebound.GlobalTrader.Site.Controls.ItemSearch.Invoices=function(n){Rebound.GlobalTrader.Site.Controls.ItemSearch.Invoices.initializeBase(this,[n]);this._ShipExported=null;this._intGlobalClientNo=-1};Rebound.GlobalTrader.Site.Controls.ItemSearch.Invoices.prototype={initialize:function(){Rebound.GlobalTrader.Site.Controls.ItemSearch.Invoices.callBaseMethod(this,"initialize");this.addSetupData(Function.createDelegate(this,this.doSetupData));this.addGetDataComplete(Function.createDelegate(this,this.doGetDataComplete))},dispose:function(){this.isDisposed||(this._ShipExported=null,this._intGlobalClientNo=null,Rebound.GlobalTrader.Site.Controls.ItemSearch.Invoices.callBaseMethod(this,"dispose"))},doSetupData:function(){this._objData.set_PathToData("controls/ItemSearch/Invoices");this._objData.set_DataObject("Invoices");this._objData.set_DataAction("GetData");this._objData.addParameter("Contact",this.getFieldValue("ctlContact"));this._objData.addParameter("CMName",this.getFieldValue("ctlCompany"));this._objData.addParameter("IncludePaid",this.getFieldValue("ctlIncludePaid"));this._objData.addParameter("Salesman",this.getFieldValue("ctlSalesman"));this._objData.addParameter("CustomerPO",this.getFieldValue("ctlCustomerPO"));this._objData.addParameter("InvoiceNoLo",this.getFieldValue_Min("ctlInvoiceNo"));this._objData.addParameter("InvoiceNoHi",this.getFieldValue_Max("ctlInvoiceNo"));this._objData.addParameter("SONoLo",this.getFieldValue_Min("ctlSalesOrderNo"));this._objData.addParameter("SONoHi",this.getFieldValue_Max("ctlSalesOrderNo"));this._objData.addParameter("DateInvoicedFrom",this.getFieldValue("ctlDateInvoicedFrom"));this._objData.addParameter("DateInvoicedTo",this.getFieldValue("ctlDateInvoicedTo"));this._objData.addParameter("InvoiceExported",this._ShipExported);this._objData.addParameter("GlobalClientNo",this._intGlobalClientNo)},doGetDataComplete:function(){for(var n,i,t=0,r=this._objResult.Results.length;t<r;t++)n=this._objResult.Results[t],i=[n.No,$R_FN.setCleanTextValue(n.CMName),$R_FN.setCleanTextValue(n.Contact),$R_FN.setCleanTextValue(n.Date),$R_FN.setCleanTextValue(n.Salesman),$R_FN.setCleanTextValue(n.CustomerPO),n.SalesOrderNo],this._tblResults.addRow(i,n.ID,!1),i=null,n=null}};Rebound.GlobalTrader.Site.Controls.ItemSearch.Invoices.registerClass("Rebound.GlobalTrader.Site.Controls.ItemSearch.Invoices",Rebound.GlobalTrader.Site.Controls.ItemSearch.Base,Sys.IDisposable);