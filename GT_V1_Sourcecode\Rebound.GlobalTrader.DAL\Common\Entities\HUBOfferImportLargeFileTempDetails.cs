﻿using System;
using System.Collections.Generic;

namespace Rebound.GlobalTrader.DAL
{
    public class HUBOfferImportLargeFileTempListDetails
    {
        public List<HUBOfferImportLargeFileTempDetails> lstHUBOfferImportLargeFileTemp { get; set; }
        public int TotalRecords { get; set; }
        public int TotalRecordsError { get; set; }
        public int CurrentPage { get; set; }
    }

    public class HUBOfferImportLargeFileTempDetails
    {
        public int OfferTempId { get; set; }

        public string MPN { get; set; }
        public string MFR { get; set; }
        public string COST { get; set; }
        public string LeadTime { get; set; }
        public string SPQ { get; set; }
        public string MOQ { get; set; }
        public string Remarks { get; set; }
        public string OfferedDate { get; set; }
        public string Vendor { get; set; }
        public int? ClientNo { get; set; }
        public DateTime DLUP { get; set; }
        public string GeneratedFileName { get; set; }
        public int HUBOfferImportLargeFileID { get; set; }

        public string MPNMessage { get; set; }
        public string MFRMessage { get; set; }
        public string COSTMessage { get; set; }
        public string LeadTimeMessage { get; set; }
        public string SPQMessage { get; set; }
        public string MOQMessage { get; set; }
        public string RemarksMessage { get; set; }
        public string OfferedDateMessage { get; set; }
        public string VendorMessage { get; set; }
        public string GTMFR { get; set; }
        public string GTVendor { get; set; }
        public int VendorCount { get; set; }
        public int MFRCount { get; set; }
    }

    public class HUBOfferImportLargeFile
    {
        public int ID { get; set; }
        public string OriginalFileName { get; set; }
        public string GeneratedFileName { get; set; }
        public string GeneratedErrorFile { get; set; }
        public int UtilityLogId { get; set; }
        public string Status { get; set; }
        public DateTime DLUP { get; set; }
        public int ClientId { get; set; }
        public int UpdatedBy { get; set; }
        public int UploadedRowCount { get; set; }
    }
}