///<reference name="MicrosoftAjax.js" />
///<reference path="~/Common/Functions/Functions.js" />
///<reference path="~/Controls/IconButton/IconButton.js" />
//Marker     Changed by      Date         Remarks  
//[001]      Vinay           23/01/2013   Update iconbutton text on runtime
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls");

Rebound.GlobalTrader.Site.Controls.IconButton = function() { 
};

var $R_IBTN = Rebound.GlobalTrader.Site.Controls.IconButton;

Rebound.GlobalTrader.Site.Controls.IconButton.addClick = function(el, fn) {
	$addHandler($get(el.id + "_hyp"), "click", fn);
	el = null;
};

Rebound.GlobalTrader.Site.Controls.IconButton.clearHandlers = function(el) {
	var hyp = $get(el.id + "_hyp");
	if (hyp) $clearHandlers(hyp);
	hyp = null;
	el = null;
};

Rebound.GlobalTrader.Site.Controls.IconButton.enableButton = function(el, bln) {
	$R_FN.showElement($get(el.id + "_hyp"), bln);
	$R_FN.showElement($get(el.id + "_lblDisabled"), !bln);
	el = null;
};

Rebound.GlobalTrader.Site.Controls.IconButton.showButton = function(el, bln) {
	$R_FN.showElement(el, bln);
	el = null;
};

Rebound.GlobalTrader.Site.Controls.IconButton.isButtonEnabled = function(el) {
	var bln = $R_FN.isElementVisible($get(el.id + "_hyp"));
	el = null;
	return bln;
};

Rebound.GlobalTrader.Site.Controls.IconButton.setHref = function(el, strHref) {
	var hyp = $get(el.id + "_hyp");
	if (!hyp) return;
	hyp.href = strHref;
	el = null;
};
//[001] code start
Rebound.GlobalTrader.Site.Controls.IconButton.updateText = function(el, text) {
    $R_FN.setInnerHTML($get(el.id + "_hyp"), text);
    $R_FN.setInnerHTML($get(el.id + "_lblDisabled"), text);
    el = null;
};
//[001] code end

Rebound.GlobalTrader.Site.Controls.IconButton.registerClass("Rebound.GlobalTrader.Site.Controls.IconButton");
