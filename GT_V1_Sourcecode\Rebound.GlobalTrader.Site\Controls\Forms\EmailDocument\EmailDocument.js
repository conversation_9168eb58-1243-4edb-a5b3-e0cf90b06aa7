Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument=function(n){Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.initializeBase(this,[n]);this._strMessage="";this._strDocumentText="";this._strHeaderImage="";this._strHeaderImageID="";this._strSubject="";this._IsNotesEnable=!1;this._IsFormatEnable=!1};Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.prototype={get_strLoginEmail:function(){return this._strLoginEmail},set_strLoginEmail:function(n){this._strLoginEmail!==n&&(this._strLoginEmail=n)},get_ibtnSend:function(){return this._ibtnSend},set_ibtnSend:function(n){this._ibtnSend!==n&&(this._ibtnSend=n)},get_ibtnSend_Footer:function(){return this._ibtnSend_Footer},set_ibtnSend_Footer:function(n){this._ibtnSend_Footer!==n&&(this._ibtnSend_Footer=n)},get_ibtnContinue:function(){return this._ibtnContinue},set_ibtnContinue:function(n){this._ibtnContinue!==n&&(this._ibtnContinue=n)},get_ibtnContinue_Footer:function(){return this._ibtnContinue_Footer},set_ibtnContinue_Footer:function(n){this._ibtnContinue_Footer!==n&&(this._ibtnContinue_Footer=n)},get_strDocumentText:function(){return this._strDocumentText},set_strDocumentText:function(n){this._strDocumentText!==n&&(this._strDocumentText=n)},get_strHeaderImage:function(){return this._strHeaderImage},set_strHeaderImage:function(n){this._strHeaderImage!==n&&(this._strHeaderImage=n)},get_strHeaderImageID:function(){return this._strHeaderImageID},set_strHeaderImageID:function(n){this._strHeaderImageID!==n&&(this._strHeaderImageID=n)},get_strSubject:function(){return this._strSubject},set_strSubject:function(n){this._strSubject!==n&&(this._strSubject=n)},get_blnEmailPDF:function(){return this._blnEmailPDF},set_blnEmailPDF:function(n){this._blnEmailPDF!==n&&(this._blnEmailPDF=n)},get_intInvoiceNo:function(){return this._intInvoiceNo},set_intInvoiceNo:function(n){this._intInvoiceNo!==n&&(this._intInvoiceNo=n)},get_PDFDocumentType:function(){return this._PDFDocumentType},set_PDFDocumentType:function(n){this._PDFDocumentType!==n&&(this._PDFDocumentType=n)},get_strSignatureImage:function(){return this._strSignatureImage},set_strSignatureImage:function(n){this._strSignatureImage!==n&&(this._strSignatureImage=n)},get_strSignatureImageID:function(){return this._strSignatureImageID},set_strSignatureImageID:function(n){this._strSignatureImageID!==n&&(this._strSignatureImageID=n)},get_TermConditionImage:function(){return this._TermConditionImage},set_TermConditionImage:function(n){this._TermConditionImage!==n&&(this._TermConditionImage=n)},get_TermConditionImageID:function(){return this._TermConditionImageID},set_TermConditionImageID:function(n){this._TermConditionImageID!==n&&(this._TermConditionImageID=n)},get_SectionName:function(){return this._SectionName},set_SectionName:function(n){this._SectionName!==n&&(this._SectionName=n)},get_SubSectionName:function(){return this._SubSectionName},set_SubSectionName:function(n){this._SubSectionName!==n&&(this._SubSectionName=n)},get_termsPathForClient:function(){return this._termsPathForClient},set_termsPathForClient:function(n){this._termsPathForClient!==n&&(this._termsPathForClient=n)},get_IsNotesEnable:function(){return this._IsNotesEnable},set_IsNotesEnable:function(n){this._IsNotesEnable!==n&&(this._IsNotesEnable=n)},get_IsFormatEnable:function(){return this._IsFormatEnable},set_IsFormatEnable:function(n){this._IsFormatEnable!==n&&(this._IsFormatEnable=n)},get_ClientNo:function(){return this._ClientNo},set_ClientNo:function(n){this._ClientNo!==n&&(this._ClientNo=n)},get_DocumentFormat:function(){return this._DocumentFormat},set_DocumentFormat:function(n){this._DocumentFormat!==n&&(this._DocumentFormat=n)},get_AllowGenerateXml:function(){return this._AllowGenerateXml},set_AllowGenerateXml:function(n){this._AllowGenerateXml!==n&&(this._AllowGenerateXml=n)},initialize:function(){Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.callBaseMethod(this,"initialize");this.addShown(Function.createDelegate(this,this.formShown))},dispose:function(){this.isDisposed||(this._ibtnSend&&$R_IBTN.clearHandlers(this._ibtnSend),this._ibtnSend_Footer&&$R_IBTN.clearHandlers(this._ibtnSend_Footer),this._ibtnContinue&&$R_IBTN.clearHandlers(this._ibtnContinue),this._ibtnContinue_Footer&&$R_IBTN.clearHandlers(this._ibtnContinue_Footer),this._ctlTo&&this._ctlTo.dispose(),this._strMessage=null,this._strDocumentText=null,this._strHeaderImage=null,this._strHeaderImageID=null,this._strSubject=null,this._ibtnSend=null,this._ibtnSend_Footer=null,this._ibtnContinue=null,this._ibtnContinue_Footer=null,this._ctlTo=null,this._strLoginEmail=null,this._blnEmailPDF=null,this._intInvoiceNo=null,this._PDFDocumentType=null,this._strSignatureImage=null,this._strSignatureImageID=null,this._TermConditionImage=null,this._TermConditionImageID=null,this._SectionName=null,this._SubSectionName=null,this._termsPathForClient=null,this._IsFormatEnable=null,Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.callBaseMethod(this,"dispose"))},formShown:function(){var n,t;this._blnFirstTimeShown&&(n=Function.createDelegate(this,this.sendClicked),$R_IBTN.addClick(this._ibtnSend,n),this._ibtnSend_Footer&&$R_IBTN.addClick(this._ibtnSend_Footer,n),t=Function.createDelegate(this,this.continueClicked),$R_IBTN.addClick(this._ibtnContinue,t),this._ibtnContinue_Footer&&$R_IBTN.addClick(this._ibtnContinue_Footer,t),this._ctlTo=$find(this.getField("ctlTo").ID));this.setFormFieldsToDefaults();this.setFieldValue("ctlReplyTo",this._strLoginEmail);$R_IBTN.showButton(this._ibtnContinue,!1);this._ibtnContinue_Footer&&$R_IBTN.showButton(this._ibtnContinue_Footer,!1);$R_IBTN.showButton(this._ibtnSend,!0);this._ibtnSend_Footer&&$R_IBTN.showButton(this._ibtnSend_Footer,!0);$R_IBTN.showButton(this._ibtnCancel,!0);this._ibtnCancel_Footer&&$R_IBTN.showButton(this._ibtnCancel_Footer,!0);this.showFormField("ctlNotes",this._IsNotesEnable);this.showInvoiceFormat()},sendClicked:function(){if(this.showSendForm(),this.validateForm()){this.showSaving(!0);var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Forms/EmailDocument");n.set_DataObject("EmailDocument");n.set_DataAction("SendEmail");n.addParameter("Addresses",this._ctlTo.getValuesAsString());n.addParameter("ReplyTo",this.getFieldValue("ctlReplyTo"));n.addParameter("Subject",this.getFieldValue("ctlSubject"));n.addParameter("DocumentText",this._IsNotesEnable==!0?this._strDocumentText.replace("EmailNotest",this.getFieldValue("ctlNotes").replace(/\n/g,"<br/>")):this._strDocumentText.replace("EmailNotest",""));n.addParameter("Notes",this._IsNotesEnable==!0?"<br />"+this.getFieldValue("ctlNotes").replace(/\n/g,"<br/>")+"<br /> ":"");n.addParameter("NotesLog",this._IsNotesEnable==!0?this.getFieldValue("ctlNotes").replace(/\n/g,"<br/>")+"<br /> ":"");n.addParameter("HeaderImage",this._strHeaderImage);n.addParameter("HeaderImageID",this._strHeaderImageID);n.addParameter("blnEmailPDF",this._blnEmailPDF);n.addParameter("InvoiceNo",this._intInvoiceNo);n.addParameter("DocType",this._PDFDocumentType);n.addParameter("SignatureImage",this._strSignatureImage);n.addParameter("SignatureImageID",this._strSignatureImageID);n.addParameter("TermConditionImage",this._TermConditionImage);n.addParameter("TermConditionImageID",this._TermConditionImageID);n.addParameter("SectionName",this._SectionName);n.addParameter("SubSection",this._SubSectionName);n.addParameter("TermsPathForClient",this._termsPathForClient);n.addParameter("IncludeCustomTemplate",this.getFieldValue("ctlIncludeCustomTemplate"));n.addParameter("InvoiceFormat",this.getInvoiceFormat());n.addParameter("DocumentFormat",this._DocumentFormat);n._intTimeoutMilliseconds=2e4;n.addDataOK(Function.createDelegate(this,this.sendComplete));n.addError(Function.createDelegate(this,this.sendError));n.addTimeout(Function.createDelegate(this,this.sendError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null}},sendError:function(n){this.showSendError(n._errorMessage);this.showContent(!0);this.showInnerContent(!0)},sendComplete:function(n){n._result.Result==!0?(this.showSendOK(),$R_IBTN.showButton(this._ibtnContinue,!0),this._ibtnContinue_Footer&&$R_IBTN.showButton(this._ibtnContinue_Footer,!0),$R_IBTN.showButton(this._ibtnSend,!1),this._ibtnSend_Footer&&$R_IBTN.showButton(this._ibtnSend_Footer,!1),$R_IBTN.showButton(this._ibtnCancel,!1),this._ibtnCancel_Footer&&$R_IBTN.showButton(this._ibtnCancel_Footer,!1),(this._SectionName="Quote"&&(this._PDFDocumentType=="QO"||this._PDFDocumentType=="COCF"))&&this.updateQuoteStatus()):this.showSendError(n._errorMessage)},updateQuoteStatus:function(){var n=new Rebound.GlobalTrader.Site.Data;n.set_PathToData("controls/Nuggets/QuoteMainInfo");n.set_DataObject("QuoteMainInfo");n.set_DataAction("UpdatePrintQuoteStatus");n.addParameter("id",this._intInvoiceNo);n._intTimeoutMilliseconds=9e4;n.addDataOK(Function.createDelegate(this,this.updateQuoteStatusComplete));n.addError(Function.createDelegate(this,this.updateQuoteStatusError));n.addTimeout(Function.createDelegate(this,this.updateQuoteStatusError));$R_DQ.addToQueue(n);$R_DQ.processQueue();n=null},updateQuoteStatusError:function(n){this.showError(!0,n.get_ErrorMessage())},updateQuoteStatusComplete:function(n){n._result.Result&&window.opener.postMessage(n._result.Result,location.origin)},validateForm:function(){this.onValidate();var n=this.autoValidateFields();return this._ctlTo.validateAllFields()||(n=!1),n},showSendForm:function(){this.showContent(!0);this.showInnerContent(!0);this.showFormField("ctlSendError",!1);this._ctlTo.show(!0);this.showFormField("ctlReplyTo",!0);this.showFormField("ctlSubject",!0)},showSendError:function(){this.showContent(!0);this.showInnerContent(!0);this.showFormField("ctlSendError",!0);this._ctlTo.show(!0);this.showFormField("ctlReplyTo",!0);this.showFormField("ctlSubject",!0)},showSendOK:function(){this.showContent(!0);this.showInnerContent(!0);this.showFormField("ctlSendError",!1);this._ctlTo.show(!1);this.showFormField("ctlReplyTo",!1);this.showFormField("ctlSubject",!1);this.showSavedOK(!0)},continueClicked:function(){this.onSaveComplete()},showInvoiceFormat:function(){if(!this._IsFormatEnable){this.showFormField("ctlFormat",!1);return}if(this.showFormField("ctlFormat",!0),!this._AllowGenerateXml){document.querySelector(".optionXML").classList.add("invisible");document.querySelector('.optionPDF input[type="radio"]').checked=!0;return}document.querySelector(".optionXML").classList.remove("invisible");this._ClientNo=="108"?document.querySelector('.optionXML input[type="radio"]').checked=!0:document.querySelector('.optionPDF input[type="radio"]').checked=!0},getInvoiceFormat:function(){return!this._IsFormatEnable||!this._AllowGenerateXml?"PDF":document.querySelector('.optionXML input[type="radio"]').checked?"XML":"PDF"}};Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.EmailDocument",Rebound.GlobalTrader.Site.Controls.Forms.Base,Sys.IDisposable);