//--------------------------------------------------------------------------------------------------------
// RP 11.10.2010:
// - add scrollable label field (SetHeight property)
//
// RP 04.12.2009:
// - add TooltipResource Property
//--------------------------------------------------------------------------------------------------------
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;
using System.Web;
using System.Web.UI;
using System.Web.UI.WebControls;
using System.Web.UI.HtmlControls;
using System.Collections;

namespace Rebound.GlobalTrader.Site.Controls {
	[DefaultProperty("")]
	[ToolboxData("<{0}:DataItemRow runat=server></{0}:DataItemRow>")]
	public class DataItemRow : TableRow, IScriptControl, INamingContainer {

		#region Locals

		protected ScriptManager _sm;
		protected Label _lbl;
		protected ImageCheckBox _chk;
		protected HiddenField _hid;
		protected HyperLink _hyp;
		protected Controls.StarRating _stars;
		protected TableCell _tdTitle;
		protected Ellipses _ctlEllipses;

		#endregion

		#region Properties

		/// <summary>
		/// Title of resource object in FormFields global resource
		/// </summary>
		private string _strResourceTitle = "";
		public string ResourceTitle {
			get { return _strResourceTitle; }
			set { _strResourceTitle = value; }
		}

		/// <summary>
		/// Common ID used for contained controls
		/// </summary>
		private string _strCommonID;
		public string CommonID {
			get { return _strCommonID; }
			set { _strCommonID = value; }
		}

		/// <summary>
		/// Field type - e.g. checkbox, label etc
		/// </summary>
		private DataItemRowFieldType _enmFieldType = DataItemRowFieldType.Label;
		public DataItemRowFieldType FieldType {
			get { return _enmFieldType; }
			set { _enmFieldType = value; }
		}

		/// <summary>
		/// Show ellipses for getting data asynchronously?
		/// </summary>
		private bool _blnShowEllipses = false;
		public bool ShowEllipses {
			get { return _blnShowEllipses; }
			set { _blnShowEllipses = value; }
		}

		/// <summary>
		/// Title of resource for FullWidthLabel in Messages global resource
		/// </summary>
		private string _strFullWidthLabelTextResource = "";
		public string FullWidthLabelTextResource {
			get { return _strFullWidthLabelTextResource; }
			set { _strFullWidthLabelTextResource = value; }
		}

		/// <summary>
		/// No set width for title column
		/// </summary>
		private bool _blnNoWidthSet = false;
		public bool NoWidthSet {
			get { return _blnNoWidthSet; }
			set { _blnNoWidthSet = value; }
		}

		/// <summary>
		/// Tooltip resource from the FormFieldsExplain.resx file
		/// </summary>
		private string _strTooltipResource = "";
		public string TooltipResource {
			get { return _strTooltipResource; }
			set { _strTooltipResource = value; }
		}

		private int _intSetHeight = 0;
		/// <summary>
		/// Set height for field with scrollbars
		/// </summary>
		public int SetHeight {
			get { return _intSetHeight; }
			set { _intSetHeight = value; }
		}

		#endregion

		#region Overrides

		/// <summary>
		/// create controls
		/// </summary>
		protected override void CreateChildControls() {
			if (_strTooltipResource != "") ToolTip = Functions.GetGlobalResource("FormFieldsExplain", _strTooltipResource);

			if (_enmFieldType == DataItemRowFieldType.Separator
			|| _enmFieldType == DataItemRowFieldType.FullWidthLabel
			|| _enmFieldType == DataItemRowFieldType.SeparatorWithLine) {
				TableCell td = new TableCell();
				td.ColumnSpan = 2;
				switch (_enmFieldType) {
					case DataItemRowFieldType.SeparatorWithLine:
						td.CssClass = "sepLine";
						HtmlGenericControl divLine = ControlBuilders.CreateHtmlGenericControlInsideParent(td, "div", "line");
						ControlBuilders.CreateImageInsideParent(divLine, "", "~/images/x.gif", 1, 1);
						break;
					case DataItemRowFieldType.Separator:
						td.CssClass = "sep";
						ControlBuilders.CreateImageInsideParent(td, "", "~/images/x.gif", 1, 1);
						break;
					case DataItemRowFieldType.FullWidthLabel:
						_lbl = ControlBuilders.CreateLabelInsideParent(td);
						_lbl.ID = "lbl";
						_lbl.Text = Functions.GetGlobalResource("Messages", _strFullWidthLabelTextResource);
						break;
				}
				Cells.Add(td);
			} else {
				_tdTitle = new TableCell();
				_tdTitle.CssClass = "desc";
				if (_blnNoWidthSet) _tdTitle.CssClass += "NoWidth";
				_tdTitle.ID = "tdTitle";
				if (ResourceTitle == "") {
					ControlBuilders.CreateLiteralInsideParent(_tdTitle, "&nbsp;");
				} else {
					ControlBuilders.CreateLiteralInsideParent(_tdTitle, Functions.GetGlobalResource("FormFields", ResourceTitle));
				}
				Cells.Add(_tdTitle);

				TableCell td2 = new TableCell();
				td2.CssClass = "item";
				td2.ID = "tdItem";

				switch (_enmFieldType) {
					case DataItemRowFieldType.Label:
						if (_intSetHeight > 0) {
							Panel pnlScroll = ControlBuilders.CreatePanelInsideParent(td2, "dataItemScroll");
							pnlScroll.Height = Unit.Pixel(_intSetHeight);
							_lbl = ControlBuilders.CreateLabelInsideParent(pnlScroll);
						} else {
							_lbl = ControlBuilders.CreateLabelInsideParent(td2);
						}
						_lbl.ID = "lbl";
						if (_blnShowEllipses) {
							_ctlEllipses = new Ellipses();
							_ctlEllipses.ID = "ellipses";
							td2.Controls.Add(_ctlEllipses);
							Functions.SetCSSVisibility(_lbl, false);
						}
						break;
					case DataItemRowFieldType.CheckBox:
						_chk = new ImageCheckBox();
						_chk.ID = "chk";
						_chk.Enabled = false;
						td2.Controls.Add(_chk);
						break;
					case DataItemRowFieldType.StarRating:
						_stars = new Controls.StarRating();
						_stars.ID = "stars";
						_stars.ReadOnly = true;
						td2.Controls.Add(_stars);
						break;
					case DataItemRowFieldType.Hidden:
						CssClass = "invisible";
						_hid = new HiddenField();
						_hid.ID = "hid";
						td2.Controls.Add(_hid);
						break;
					case DataItemRowFieldType.NubButton:
						_hyp = ControlBuilders.CreateHyperLink("nubButton nubButtonAlignLeft");
						_hyp.ID = "hyp";
						td2.Controls.Add(_hyp);
						break;
				}

				Cells.Add(td2);
			}
			base.CreateChildControls();
		}

		/// <summary>
		/// OnPreRender
		/// </summary>
		/// <param name="e"></param>
		protected override void OnPreRender(EventArgs e) {
			if (!this.DesignMode) {
				_sm = ScriptManager.GetCurrent(Page);
				if (_sm == null) throw new HttpException("A ScriptManager control must exist on the current page.");
				//_sm.RegisterScriptControl(this);
			}
			base.OnPreRender(e);
		}

		/// <summary>
		/// Render
		/// </summary>
		/// <param name="writer"></param>
		protected override void Render(HtmlTextWriter writer) {
			base.Render(writer);
		}

		#endregion

		/// <summary>
		/// Sets the initial value of the field
		/// </summary>
		public void SetInitialValue(object objValue, object objValue2) {
			EnsureChildControls();
			switch (_enmFieldType) {
				case DataItemRowFieldType.Label:
					if (objValue == null) objValue = "";
					_lbl.Text = objValue.ToString().Trim();
					break;
				case DataItemRowFieldType.CheckBox:
					if (objValue == null) objValue = false;
					_chk.Checked = Convert.ToBoolean(objValue);
					break;
				case DataItemRowFieldType.StarRating:
					if (objValue == null) objValue = 0;
					string strValue = objValue.ToString();
					if (strValue == "") strValue = "0";
					_stars.CurrentRating = int.Parse(strValue);
					break;
				case DataItemRowFieldType.Hidden:
					if (objValue == null) objValue = "";
					_hid.Value = objValue.ToString().Trim();
					break;
				case DataItemRowFieldType.NubButton:
					_hyp.Text = objValue.ToString().Trim();
					if (objValue2 == null) objValue2 = "";
					_hyp.NavigateUrl = objValue2.ToString();
					Functions.SetCSSVisibility(_hyp, (_hyp.Text.Length > 0));
					break;
			}
		}

		public void SetInitialValue(object objValue) {
			SetInitialValue(objValue, null);
		}

		public void SetInitialValue_Email(string strEmail) {
			SetInitialValue(strEmail, string.Format("mailto:{0}", strEmail));
		}

		public void SetInitialValue_URL(string strURL, string strText, bool blnExternal) {
			if (blnExternal) {
				strURL = Functions.FormatWebAddress(strURL);
				if (_enmFieldType == DataItemRowFieldType.NubButton) {
					EnsureChildControls();
					_hyp.Target = "_blank";
				}
			}
			if (strText == "") strText = strURL;
			SetInitialValue(strText, strURL);
		}

		/// <summary>
		/// Sets initial title
		/// </summary>
		/// <param name="strTitle"></param>
		public void SetInitialTitle(string strTitle) {
			EnsureChildControls();
			_tdTitle.Text = strTitle;
		}

		#region IScriptControl Members

		protected virtual IEnumerable<ScriptReference> GetScriptReferences() {
			bool blnDebug = false;
#if (DEBUG)
			blnDebug = true;
#endif
			return new ScriptReference[] { Functions.GetScriptReference(blnDebug, "Rebound.GlobalTrader.Site", "Controls.Basic.DataItemRow.DataItemRow", true) };
		}

		protected virtual IEnumerable<ScriptDescriptor> GetScriptDescriptors() {
			ScriptControlDescriptor descriptor = new ScriptControlDescriptor("Rebound.GlobalTrader.Site.Controls.DataItemRow", this.ClientID);
			descriptor.AddElementProperty("tdTitle", _tdTitle.ClientID);
			if (_enmFieldType == DataItemRowFieldType.Label) descriptor.AddElementProperty("lbl", _lbl.ClientID);
			if (_enmFieldType == DataItemRowFieldType.CheckBox) descriptor.AddComponentProperty("chk", _chk.ClientID);
			if (_enmFieldType == DataItemRowFieldType.StarRating) descriptor.AddComponentProperty("stars", _stars.ClientID);
			if (_enmFieldType == DataItemRowFieldType.Hidden) descriptor.AddElementProperty("hid", _hid.ClientID);
			if (_enmFieldType == DataItemRowFieldType.NubButton) descriptor.AddElementProperty("hyp", _hyp.ClientID);
			if (_blnShowEllipses) descriptor.AddComponentProperty("ctlEllipses", _ctlEllipses.ClientID);
			return new ScriptDescriptor[] { descriptor };
		}

		IEnumerable<ScriptReference> IScriptControl.GetScriptReferences() {
			return GetScriptReferences();
		}

		IEnumerable<ScriptDescriptor> IScriptControl.GetScriptDescriptors() {
			return GetScriptDescriptors();
		}

		#endregion

		#region Enumerations

		/// <summary>
		/// DataItemRowFieldType
		/// </summary>
		public enum DataItemRowFieldType {
			Label,
			CheckBox,
			StarRating,
			Hidden,
			NubButton,
			Separator,
			SeparatorWithLine,
			FullWidthLabel
		}

		#endregion
	}

}