///<reference name="MicrosoftAjax.js" />
///<reference name="Rebound.GlobalTrader.Site.Common.Functions.Functions.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Common.Data.Data.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.IconButton.IconButton.js" assembly="Rebound.GlobalTrader.Site" />
///<reference name="Rebound.GlobalTrader.Site.Controls.Basic.ReboundTextBox.ReboundTextBox.js" assembly="Rebound.GlobalTrader.Site" />
///<reference path="~/Code/Common/Functions.js" />
///<reference path="~/Code/Common/PageURLFunctions.js" />
//-----------------------------------------------------------------------------------------
// RP 06.01.2010:
// - fully dispose everything
//-----------------------------------------------------------------------------------------
Type.registerNamespace("Rebound.GlobalTrader.Site.Controls.Forms");

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm = function (element) {
    Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.initializeBase(this, [element]);
    this._intRequirementLineID = -1;
    this._intBOMID = -1;
    this._CustReqNo = -1;
    this._RecallNoBid = false;
};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.prototype = {

    get_BomCode: function() { return this._BomCode; }, set_BomCode: function(value) { if (this._BomCode !== value) this._BomCode = value; },
    get_BomName: function() { return this._BomName; }, set_BomName: function(value) { if (this._BomName !== value) this._BomName = value; },
    get_BomCompanyName: function() { return this._BomCompanyName; }, set_BomCompanyName: function(value) { if (this._BomCompanyName !== value) this._BomCompanyName = value; },
    get_BomCompanyNo: function() { return this._BomCompanyNo; }, set_BomCompanyNo: function(value) { if (this._BomCompanyNo !== value) this._BomCompanyNo = value; },
    get_SalesManNo: function() { return this._SalesManNo; }, set_SalesManNo: function(value) { if (this._SalesManNo !== value) this._SalesManNo = value; },
    get_SalesManName: function() { return this._SalesManName; }, set_SalesManName: function(value) { if (this._SalesManName !== value) this._SalesManName = value; },

    get_strTitle_NoBid: function () { return this._strTitle_NoBid; }, set_strTitle_NoBid: function (value) { if (this._strTitle_NoBid !== value) this._strTitle_NoBid = value; },
    get_strTitle_RecallNoBid: function () { return this._strTitle_RecallNoBid; }, set_strTitle_RecallNoBid: function (value) { if (this._strTitle_RecallNoBid !== value) this._strTitle_RecallNoBid = value; },
    get_lblExplainNoBid: function () { return this._lblExplainNoBid; }, set_lblExplainNoBid: function (value) { if (this._lblExplainNoBid !== value) this._lblExplainNoBid = value; },
    get_lblExplainRecallNoBid: function () { return this._lblExplainRecallNoBid; }, set_lblExplainRecallNoBid: function (value) { if (this._lblExplainRecallNoBid !== value) this._lblExplainRecallNoBid = value; },


    initialize: function() {
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.callBaseMethod(this, "initialize");
        this.addShown(Function.createDelegate(this, this.formShown));
    },

    dispose: function() {
        if (this.isDisposed) return;
        if (this._ctlConfirm) this._ctlConfirm.dispose();
        this._ctlConfirm = null;
        this._intRequirementLineID = null;
        this._strTitle_NoBid = null;
        this._strTitle_RecallNoBid = null;
        this._lblExplainNoBid = null;
        this._lblExplainRecallNoBid = null;
        Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.callBaseMethod(this, "dispose");
    },

    formShown: function() {
        if (this._blnFirstTimeShown) {
           // alert(this._CustReqNo);
            this._ctlConfirm = this.getFieldComponent("ctlConfirm");
            this._ctlConfirm.addClickYesEvent(Function.createDelegate(this, this.yesClicked));
            this._ctlConfirm.addClickNoEvent(Function.createDelegate(this, this.noClicked));
        }
        this.checkMode();
    },

    yesClicked: function () {
        //alert(this._RecallNoBid);
       // return;
        this.showSaving(true);
        var obj = new Rebound.GlobalTrader.Site.Data();
        obj.set_PathToData("Controls/Nuggets/BOMItems");
        obj.set_DataObject("BOMItems");
        if (this._RecallNoBid == true) {
            obj.set_DataAction("RecallNoBidRequirement");
        }
        else {
            obj.set_DataAction("NoBidRequirement");
        }
        obj.addParameter("id", this._intRequirementLineID);
        obj.addParameter("BomId", this._intBOMID);
        obj.addParameter("BomCode", this._BomCode);
        obj.addParameter("BomName", this._BomName);
        obj.addParameter("BomCompanyName", this._BomCompanyName);
        obj.addParameter("BomCompanyNo", this._BomCompanyNo);
        obj.addParameter("SalesManName", this._SalesManName);
        obj.addParameter("SalesManNo", this._SalesManNo);
        obj.addParameter("CustReqNo", this._CustReqNo);
        obj.addParameter("Notes", this.getFieldValue("ctlNotes"));
        obj.addDataOK(Function.createDelegate(this, this.saveComplete));
        obj.addError(Function.createDelegate(this, this.saveError));
        obj.addTimeout(Function.createDelegate(this, this.saveError));
        $R_DQ.addToQueue(obj);
        $R_DQ.processQueue();
        obj = null;
    },


    noClicked: function() {
        this.showSaving(false);
        this.onNotConfirmed();
    },

    saveError: function(args) {
        this.showSaving(false);
        this._strErrorMessage = args._errorMessage;
        this.onSaveError();
    },

    saveComplete: function(args) {
        this.showSaving(false);
        if (args._result.Result) {
            this.onSaveComplete();
        } else {
            this._strErrorMessage = args._errorMessage;
            this.onSaveError();
        }
    },

    checkMode: function() {
		switch (this._mode){
		    case "NoBid": this.changeTitle(this._strTitle_NoBid); break;
		    case "RecallNoBid": this.changeTitle(this._strTitle_RecallNoBid); break;
		}
		$R_FN.showElement(this._lblExplainNoBid, this._mode == "NoBid");
		$R_FN.showElement(this._lblExplainRecallNoBid, this._mode == "RecallNoBid");
		this.showField("ctlNotes", this._mode != "RecallNoBid");
	}

};

Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm.registerClass("Rebound.GlobalTrader.Site.Controls.Forms.BOMItems_NoBidConfirm", Rebound.GlobalTrader.Site.Controls.Forms.Base, Sys.IDisposable);
